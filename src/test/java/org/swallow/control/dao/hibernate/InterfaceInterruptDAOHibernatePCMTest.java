package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.model.Notifications;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class InterfaceInterruptDAOHibernatePCMTest extends BaseDAOIntegrationTest {

    @Autowired
    private InterfaceInterruptDAO interfaceInterruptDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String INTERFACE_ID = "CALYPSO";
    private static final String MESSAGE_TYPE = "MT1";
    private static final String PROGRAM_NAME = "PROG1";

    @Test
    void testGetEndAlertTime() {
        Date endAlertTime = interfaceInterruptDAO.getEndAlertTime(INTERFACE_ID, false);
        assertNotNull(endAlertTime);
    }

    @Test
    void testGetStartAlertTime() {
        Date startAlertTime = interfaceInterruptDAO.getStartAlertTime(INTERFACE_ID, false);
        assertNotNull(startAlertTime);
    }

    @Test
    void testInsertNotifications() {
        Notifications notification = new Notifications();
        notification.setHostId(HOST_ID);
        notification.setEntityId(ENTITY_ID);
        notification.setRelationId("REL1");
        notification.setNotificationType("TYPE1");
        notification.setPriority(1L);
        notification.setNotificationMessage("Test message");
        
        boolean result = interfaceInterruptDAO.insertNotifications(notification, true);
        assertTrue(result);
    }

    @Test
    void testUpdateNotifications() {
        Notifications notification = new Notifications();
        notification.setNotificationId(1);
        notification.setNotificationMessage("Updated message");
        
        boolean result = interfaceInterruptDAO.updateNotifications(notification, true);
        assertTrue(result);
    }

    @Test
    void testGetThresholdTime() {
        Integer threshold = interfaceInterruptDAO.getThresholdTime(INTERFACE_ID, false);
        assertNotNull(threshold);
    }

    @Test
    void testDeleteNotification() {
        Notifications notification = new Notifications();
        notification.setRelationId("REL1");
        
        boolean result = interfaceInterruptDAO.deleteNotification(notification, true);
        assertTrue(result);
    }

    @Test
    void testNotificationJobStatus() {
        boolean result = interfaceInterruptDAO.notificationJobStatus(PROGRAM_NAME, true);
        assertNotNull(result);
    }
} 