package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.MaintenanceLogDAO;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class MaintenanceLogDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MaintenanceLogDAO maintenanceLogDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String USER_ID = "User15";
    private static final String IP_ADDRESS = "127.0.0.1";
    private static final String TABLE_NAME = "TEST_TABLE";
    private static final String REFERENCE = "REF1";
    private static final String ACTION = "a";

    @Test
    void testLogMaintenanceAudit() throws SwtException {
        MaintenanceLog maintenanceLog = new MaintenanceLog();
        maintenanceLog.setHostId(HOST_ID);
        maintenanceLog.setuserId(USER_ID);
        maintenanceLog.setIpAddress(IP_ADDRESS);
        maintenanceLog.setAction(ACTION);
        maintenanceLog.setTableName(TABLE_NAME);
        maintenanceLog.setReference(REFERENCE);
        maintenanceLog.setLogDate(new Date());
        
        maintenanceLogDAO.logMaintenanceAudit(maintenanceLog);
    }

    @Test
    void testGetSystemLogDetails() throws SwtException {
        String logDate = "2024-03-20 10:00:00";
        Collection result = maintenanceLogDAO.getSystemLogDetails(
            HOST_ID, logDate, USER_ID, IP_ADDRESS, TABLE_NAME, REFERENCE, ACTION);
        assertNotNull(result);
    }

    @Test
    void testGetMaintenanceLogList() throws SwtException {
        Date fromDate = new Date();
        Date toDate = new Date();
        int currentPage = 1;
        int maxPage = 10;
        List<MaintenanceLog> maintenanceList = new ArrayList<>();
        String filterSortStatus = "filter<split>sort";
        SystemFormats formats = new SystemFormats();
        
        int result = maintenanceLogDAO.getMaintenanceLogList(
            HOST_ID, fromDate, toDate, currentPage, maxPage, 
            maintenanceList, filterSortStatus, formats);
        assertNotNull(result);
    }
} 