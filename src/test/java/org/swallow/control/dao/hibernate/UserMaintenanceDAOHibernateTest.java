package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.UserMaintenanceDAO;
import org.swallow.control.model.UserMaintenance;
import org.swallow.control.model.Role;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class UserMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private UserMaintenanceDAO userMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ROLE_ID = "Role1";
    private static final String USER_ID = "User15";

    @Test
    void testGetUserList() throws SwtException {
        Collection result = userMaintenanceDAO.getUserList(HOST_ID, ROLE_ID);
        assertNotNull(result);
    }


    @Test
    void testFetchUserDetail() throws SwtException {
        UserMaintenance result = userMaintenanceDAO.fetchUserDetail(HOST_ID, USER_ID);
        assertNull(result); // or assertNotNull depending on test data
    }


    @Test
    void testGetRoleList() throws SwtException {
        Collection<Role> result = userMaintenanceDAO.getRoleList(HOST_ID);
        assertNotNull(result);
    }
} 