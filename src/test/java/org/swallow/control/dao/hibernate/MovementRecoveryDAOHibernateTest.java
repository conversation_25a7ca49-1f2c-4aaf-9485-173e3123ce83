package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.MovementRecoveryDAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MovementLock;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class MovementRecoveryDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MovementRecoveryDAO movementRecoveryDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final Long MOVEMENT_ID = 1167679L;

    @Test
    void testGetMovementLockDetails() throws SwtException {
        Collection result = movementRecoveryDAO.getMovementLockDetails(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testUnlockMovement() throws SwtException {
        // Create the Id object using no-arg constructor
        MovementLock.Id id = new MovementLock.Id();

        // Set fields explicitly
        id.setHostId(HOST_ID);
        id.setMovementId(MOVEMENT_ID);
        id.setEntityId(ENTITY_ID);

        // Create MovementLock object (assuming it has a no-arg constructor)
        MovementLock movLock = new MovementLock();

        // Set the Id object
        movLock.setId(id);

        // Set other fields as needed, for example:
        movLock.setCurrCode(null);
        movLock.setUpdateDate(null);
        movLock.setUpdateUser(null);
        movLock.setUpdateUserNeedUpdated(true);
        movLock.setUpdateDateNeedUpdated(true);
        movLock.setMainEventId(null);
        movLock.setForceNoLogs(false);

        // Now call the DAO method
        movementRecoveryDAO.unlockMovement(movLock);
    }

}