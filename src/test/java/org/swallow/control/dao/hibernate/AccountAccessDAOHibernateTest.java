package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.AccountAccessDAO;
import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.AccountAccessDetails;
import org.swallow.control.model.Role;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuAccess;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class AccountAccessDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AccountAccessDAO accountAccessDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetAccountAccessDetails() throws SwtException {
        AccountAccess accountAccess = new AccountAccess();
        accountAccess.getId().setHostId(HOST_ID);
        accountAccess.getId().setEntityId(ENTITY_ID);
        accountAccess.getId().setRoleId(ROLE_ID);
        accountAccess.getId().setAccountId(ACCOUNT_ID);
        
        List result = accountAccessDAO.getAccountAccessDetails(accountAccess);
        assertNotNull(result);
    }

    @Test
    void testSaveAccountAccessDetails() throws SwtException {
        Collection<AccountAccessDetails> accountAccess = new ArrayList<>();
        accountAccessDAO.saveAccountAccessDetails(accountAccess, USER_ID);
    }

    @Test
    void testAccountAccessDetails() throws SwtException {
        Collection<AccountAccessDetails> result = accountAccessDAO.accountAccessDetails(HOST_ID, ROLE_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(result);
    }

    @Test
    void testDeleteAcctAccessDetails() throws SwtException {
        accountAccessDAO.deleteAcctAccessDetails(ROLE_ID);
    }

    @Test
    void testDeleteAcctDetails() throws SwtException {
        accountAccessDAO.deleteAcctDetails(ACCOUNT_ID);
    }

    @Test
    void testGetRoleDetails() throws SwtException {
        List<Role> result = accountAccessDAO.getRoleDetails(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testCheckAcctAccess() throws SwtException {
        AccountAccess accountAccess = new AccountAccess();
        accountAccess.getId().setHostId(HOST_ID);
        accountAccess.getId().setEntityId(ENTITY_ID);
        accountAccess.getId().setRoleId(ROLE_ID);
        accountAccess.getId().setAccountId(ACCOUNT_ID);
        
        List<Object[]> result = accountAccessDAO.checkAcctAccess(accountAccess);
        assertNotNull(result);
    }

    @Test
    void testCheckMenuAccess() throws SwtException {
        List<MenuAccess> result = accountAccessDAO.checkMenuAccess(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGeAcctDetails() throws SwtException {
        Collection<AccountAccess> result = accountAccessDAO.geAcctDetails(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testCopyAccessDetails() throws SwtException {
        accountAccessDAO.copyAccessDetails(ROLE_ID, "NEW_ROLE_ID");
    }

    @Test
    void testGetRoleAccessDetails() throws SwtException {
        AccountAccess accountAccess = new AccountAccess();
        accountAccess.getId().setHostId(HOST_ID);
        accountAccess.getId().setEntityId(ENTITY_ID);
        accountAccess.getId().setRoleId(ROLE_ID);
        accountAccess.getId().setAccountId(ACCOUNT_ID);
        
        boolean result = accountAccessDAO.getRoleAccessDetails(accountAccess);
        assertNotNull(result);
    }
} 