package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.SchedulerDAO;
import org.swallow.control.model.Job;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.ScheduledReportParams;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class SchedulerDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SchedulerDAO schedulerDAO;

    private static final String HOST_ID = "RABO";
    private static final String JOB_ID = "JOB1";
    private static final Integer SCHEDULE_ID = 1;
    private static final String MENU_ITEM_ID = "MENU1";
    private static final String SCHEDULED_JOB_TYPE = "P";

    @Test
    void testGetJobStatusDetails() throws SwtException {
        Collection<JobStatus> result = schedulerDAO.getJobStatusDetails(HOST_ID, SCHEDULED_JOB_TYPE);
        assertNotNull(result);
    }

    @Test
    void testGetJobNameList() throws SwtException {
        Collection<Job> result = schedulerDAO.getJobNameList(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetJobNames() throws SwtException {
        Collection<Scheduler> result = schedulerDAO.getJobNames(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetSchedulerDetails() throws SwtException {
        Collection<Scheduler> result = schedulerDAO.getSchedulerDetails(SCHEDULE_ID);
        assertNotNull(result);
    }




    @Test
    void testGetJobDetail() throws SwtException {
        Job result = schedulerDAO.getJobDetail(HOST_ID, JOB_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testGetReportsJobList() throws SwtException {
        ArrayList<Job> result = schedulerDAO.getReportsJobList(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetReportTypes() throws SwtException {
        ArrayList<ScheduledReportType> result = schedulerDAO.getReportTypes(HOST_ID, JOB_ID);
        assertNotNull(result);
    }

    @Test
    void testGetJobStatus() throws SwtException {
        JobStatus result = schedulerDAO.getJobStatus(SCHEDULE_ID);
        assertNull(result); // or assertNotNull depending on test data
    }


    @Test
    void testGetJobType() throws SwtException {
        Scheduler result = schedulerDAO.getJobType(HOST_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testGetScheduledReportParams() throws SwtException {
        ScheduledReportParams result = schedulerDAO.getScheduledReportParams(SCHEDULE_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testGetScreenDetails() throws SwtException {
        ArrayList result = schedulerDAO.getScreenDetails(MENU_ITEM_ID);
        assertNotNull(result);
    }

    @Test
    void testGetScheduledReportTypeConfig() throws SwtException {
        ArrayList result = schedulerDAO.getScheduledReportTypeConfig(JOB_ID);
        assertNotNull(result);
    }
} 