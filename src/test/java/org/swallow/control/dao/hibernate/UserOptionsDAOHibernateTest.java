package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.UserOptionsDAO;
import org.swallow.control.model.UserOptions;

import static org.junit.jupiter.api.Assertions.*;

public class UserOptionsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private UserOptionsDAO userOptionsDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User1";

    @Test
    void testFetchUserDetails() {
        UserOptions result = userOptionsDAO.fetchUserDetails(HOST_ID, USER_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testUpdateUserDetails() {
        UserOptions userOptions = new UserOptions();
        userOptionsDAO.updateUserDetails(userOptions);
    }
} 