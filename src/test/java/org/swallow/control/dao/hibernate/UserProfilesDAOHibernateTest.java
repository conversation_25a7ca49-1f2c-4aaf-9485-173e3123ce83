package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.UserProfilesDAO;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;

import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class UserProfilesDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private UserProfilesDAO userProfilesDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";
    private static final String PROFILE_ID = "Profile1";

    @Test
    void testFetchDetails() {
        List result = userProfilesDAO.fetchDetails(HOST_ID, USER_ID, PROFILE_ID);
        assertNotNull(result);
    }

    @Test
    void testSaveuserProfileDetails() {
        UserProfile userProfile = new UserProfile();
        userProfilesDAO.saveuserProfileDetails(userProfile);
    }

    @Test
    void testUpdateuserProfileDetails() {
        UserProfile userProfile = new UserProfile();
        userProfilesDAO.updateuserProfileDetails(userProfile);
    }

    @Test
    void testDeleteUserProfileDetails() {
        UserProfile userProfile = new UserProfile();
        userProfilesDAO.deleteUserProfileDetails(userProfile);
    }

    @Test
    void testUpdateuserProfileDetailsWithCollection() {
        UserProfile userProfile = new UserProfile();
        Collection<UserProfileDetail> details = List.of();
        userProfilesDAO.updateuserProfileDetails(userProfile, details);
    }
} 