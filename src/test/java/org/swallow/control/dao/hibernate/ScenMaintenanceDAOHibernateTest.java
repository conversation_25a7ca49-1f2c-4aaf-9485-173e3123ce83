package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ScenMaintenanceDAO;
import org.swallow.control.model.Scenario;
import org.swallow.control.model.ScenarioCategory;
import org.swallow.control.model.Facility;
import org.swallow.control.model.ScenarioEventFacility;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class ScenMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ScenMaintenanceDAO scenMaintenanceDAO;

    private static final String CATEGORY_ID = "CAT1";
    private static final String SCENARIO_ID = "SCEN1";
    private static final String FACILITY_ID = "FAC1";
    private static final String EVENT_FACILITY_ID = "INS_UPD_BALANCE";

    @Test
    void testGetScenariosDetailList() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenariosDetailList(CATEGORY_ID);
        assertNotNull(result);
    }

    @Test
    void testGetScenariosNotificationDetailList() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenariosNotificationDetailList();
        assertNotNull(result);
    }

    @Test
    void testGetRoleList() throws SwtException {
        Collection result = scenMaintenanceDAO.getRoleList("RABO");
        assertNotNull(result);
    }

    @Test
    void testGetFacilityListForEdit() throws SwtException {
        Collection result = scenMaintenanceDAO.getFacilityListForEdit(true);
        assertNotNull(result);
    }

    @Test
    void testGetEditableData() throws SwtException {
        Scenario result = scenMaintenanceDAO.getEditableData(SCENARIO_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testGetFacilityDetails() throws SwtException {
        Facility result = scenMaintenanceDAO.getFacilityDetails(FACILITY_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testGetScenarioList() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenarioList();
        assertNotNull(result);
    }

    @Test
    void testGetScenariosCategoryDetailList() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenariosCategoryDetailList();
        assertNotNull(result);
    }

    @Test
    void testGetCategoryEditableData() throws SwtException {
        ScenarioCategory result = scenMaintenanceDAO.getCategoryEditableData(CATEGORY_ID);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testCategoryHasChildren() throws SwtException {
        boolean result = scenMaintenanceDAO.categoryHasChildren(CATEGORY_ID);
        assertFalse(result); // or assertTrue depending on test data
    }

    @Test
    void testGetGuiFacilityList() throws SwtException {
        Collection result = scenMaintenanceDAO.getGuiFacilityList();
        assertNotNull(result);
    }

    @Test
    void testGetEventFacilityList() throws SwtException {
        Collection result = scenMaintenanceDAO.getEventFacilityList();
        assertNotNull(result);
    }

    @Test
    void testGetGuiHiglightMapping() throws SwtException {
        Collection result = scenMaintenanceDAO.getGuiHiglightMapping(SCENARIO_ID);
        assertNotNull(result);
    }

    @Test
    void testGetEventMapping() throws SwtException {
        Collection result = scenMaintenanceDAO.getEventMapping(SCENARIO_ID);
        assertNotNull(result);
    }

    @Test
    void testGetScenarioSchedule() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenarioSchedule(SCENARIO_ID);
        assertNotNull(result);
    }

    @Test
    void testGetScenGuiFacilityMappingList() throws SwtException {
        Collection result = scenMaintenanceDAO.getScenGuiFacilityMappingList(SCENARIO_ID);
        assertNotNull(result);
    }

    @Test
    void testGetScenRcdInstance() throws SwtException {
        String result = scenMaintenanceDAO.getScenRcdInstance(SCENARIO_ID);
        assertNotNull(result);
    }
} 