package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.EntityProcessDAO;
import org.swallow.control.model.EntityProcess;
import org.swallow.control.model.EntityProcessStatus;
import org.swallow.control.model.Process;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class EntityProcessDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private EntityProcessDAO entityProcessDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String PROCESS_NAME = "PROC1";

    @Test
    void testGetProcessNameDetails() throws SwtException {
        Collection<Process> result = entityProcessDAO.getProcessNameDetails();
        assertNotNull(result);
    }

    @Test
    void testGetEntityProcess() throws SwtException {
        Collection<EntityProcess> result = entityProcessDAO.getEntityProcess(PROCESS_NAME);
        assertNotNull(result);
    }

    @Test
    void testGetEntityProcessStatus() throws SwtException {
        Collection<EntityProcessStatus> result = entityProcessDAO.getEntityProcessStatus(PROCESS_NAME);
        assertNotNull(result);
    }

    @Test
    void testSave() throws SwtException {
        Collection<EntityProcess> entProColl = new ArrayList<>();
        Process process = new Process();
        entityProcessDAO.save(entProColl, process);
    }

    @Test
    void testSavestatus() throws SwtException {
        EntityProcessStatus entProStatus = new EntityProcessStatus();
        entityProcessDAO.savestatus(entProStatus);
    }

    @Test
    void testGetMonitorJobFlag() throws SwtException {
        boolean result = entityProcessDAO.getMonitorJobFlag(ENTITY_ID, PROCESS_NAME);
        assertNotNull(result);
    }
} 