package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.SystemLogDAO;
import org.swallow.control.model.SystemLog;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class SystemLogDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SystemLogDAO systemLogDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";
    private static final String IP_ADDRESS = "127.0.0.1";
    private static final String PROCESS = "PROC1";
    private static final String ACTION = "a";

    @Test
    void testGetSystemLogList() throws SwtException {
        Date fromDate = new Date();
        Date toDate = new Date();
        int currentPage = 1;
        int maxPage = 10;
        List<SystemLog> sysLogList = new ArrayList<>();
        String filterSortStatus = "filter<split>sort";
        SystemFormats formats = new SystemFormats();
        int result = systemLogDAO.getSystemLogList(HOST_ID, fromDate, toDate, currentPage, maxPage, sysLogList, filterSortStatus, formats);
        assertNotNull(result);
    }

    @Test
    void testSaveSystemLog() throws SwtException {
        SystemLog systemLog = new SystemLog();
        systemLog.setHostId(HOST_ID);
        systemLog.setUserId(USER_ID);
        systemLog.setIpAddress(IP_ADDRESS);
        systemLog.setProcess(PROCESS);
        systemLog.setAction(ACTION);
        systemLog.setLogDate(new Date());
        systemLogDAO.saveSystemLog(systemLog);
    }
} 