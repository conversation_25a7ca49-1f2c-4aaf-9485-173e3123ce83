package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.UserProfilesDtlDAO;
import org.swallow.exception.SwtException;
import org.swallow.model.UserProfileDetail;

import java.util.List;
import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class UserProfilesDtlDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private UserProfilesDtlDAO userProfilesDtlDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";
    private static final String PROFILE_ID = "Profile1";
    private static final String ROLE_ID = "Role1";

    @Test
    void testSavewindowdetails() {
        UserProfileDetail detail = new UserProfileDetail();
        userProfilesDtlDAO.savewindowdetails(detail);
    }

    @Test
    void testUpdatewindowdetails() {
        UserProfileDetail detail = new UserProfileDetail();
        userProfilesDtlDAO.updatewindowdetails(detail);
    }

    @Test
    void testDeletewindowdetails() {
        UserProfileDetail detail = new UserProfileDetail();
        userProfilesDtlDAO.deletewindowdetails(detail);
    }

    @Test
    void testFetchMenuDetails() {
        List result = userProfilesDtlDAO.fetchMenuDetails(HOST_ID, USER_ID, PROFILE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetProfileDetails() throws SwtException {
        List result = userProfilesDtlDAO.getProfileDetails(HOST_ID, USER_ID, PROFILE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMenuList() throws SwtException {
        Collection result = userProfilesDtlDAO.getMenuList(ROLE_ID);
        assertNotNull(result);
    }
} 