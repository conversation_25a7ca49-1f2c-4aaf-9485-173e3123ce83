package org.swallow.control.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.SectionDAO;
import org.swallow.control.model.Section;
import org.swallow.exception.SwtException;

import java.util.Collection;

public class SectionDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    SectionDAO sectionDAO;

    private static final String HOST_ID = "RABO";
    private static final String SECTION_ID = "SEC01";
    private static final String SECTION_NAME = "SECTION01NAME";
    private static final String UPDATE_USER = "User15";

    @Test
    void testGetSectionList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Section> actual = sectionDAO.getSectionList(HOST_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<Section> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<Section>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetSectionList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected section list to not be empty");
        }
    }

    @Test
    void testSaveUpdateDeleteSection() throws SwtException {
        Section section = new Section();
        Section.Id id = new Section.Id();
        id.setHostId(HOST_ID);
        id.setSectionId(SECTION_ID);
        section.setId(id);
        section.setSectionName(SECTION_NAME);
        section.setUpdateUser(UPDATE_USER);

        // Save
        sectionDAO.saveSectionDetail(section);

        // Update
        section.setSectionName("UPDATED_SECTION_NAME");
        sectionDAO.updateSectionDetail(section);

        // Delete
        sectionDAO.deleteSectionDetail(section);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }
} 