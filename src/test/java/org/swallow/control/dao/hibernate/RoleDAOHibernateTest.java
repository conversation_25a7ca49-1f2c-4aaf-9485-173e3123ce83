package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.RoleDAO;
import org.swallow.control.model.FacilityAccess;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class RoleDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private RoleDAO roleDAO;

    private static final String HOST_ID = "RABO";
    private static final String ROLE_ID = "Role1";

    @Test
    void testGetRoleDetails() throws SwtException {
        Collection result = roleDAO.getRoleDetails();
        assertNotNull(result);
    }


    @Test
    void testGetEntityDetails() throws SwtException {
        Collection result = roleDAO.getEntityDetails(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMenuAccessDetails() throws SwtException {
        Collection result = roleDAO.getMenuAccessDetails(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetEntityAccessDetails() throws SwtException {
        Collection result = roleDAO.getEntityAccessDetails(HOST_ID, ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetFacilityAccessList() throws SwtException {
        Collection<FacilityAccess> result = roleDAO.getFacilityAccessList(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMaintainAnyILMScenario() throws SwtException {
        String result = roleDAO.getMaintainAnyILMScenario(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMaintainAnyReportHist() throws SwtException {
        String result = roleDAO.getMaintainAnyReportHist(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMaintainAnyILMGroup() throws SwtException {
        String result = roleDAO.getMaintainAnyILMGroup(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetMaintainAnyPCFeature() throws SwtException {
        String result = roleDAO.getMaintainAnyPCFeature(ROLE_ID);
        assertNotNull(result);
    }
} 