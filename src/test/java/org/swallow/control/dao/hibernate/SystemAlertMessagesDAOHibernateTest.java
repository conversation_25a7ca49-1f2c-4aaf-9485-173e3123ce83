package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.SystemAlertMessagesDAO;
import org.swallow.control.model.SystemAlertMesages;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class SystemAlertMessagesDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SystemAlertMessagesDAO systemAlertMessagesDAO;

    private static final String HOST_ID = "RABO";
    private static final String ALERT_STAGE = "STAGE1";

    @Test
    void testGetAlertMsgDetailList() throws SwtException {
        Collection result = systemAlertMessagesDAO.getAlertMsgDetailList(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetRollList() throws SwtException {
        Collection result = systemAlertMessagesDAO.getRollList(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetEditableData() throws SwtException {
        SystemAlertMesages result = systemAlertMessagesDAO.getEditableData(HOST_ID, ALERT_STAGE);
        assertNull(result); // or assertNotNull depending on test data
    }

    @Test
    void testAlertAllow() throws SwtException {
        boolean result = systemAlertMessagesDAO.alertAllow(HOST_ID, ALERT_STAGE);
        assertFalse(result); // or assertTrue depending on test data
    }
} 