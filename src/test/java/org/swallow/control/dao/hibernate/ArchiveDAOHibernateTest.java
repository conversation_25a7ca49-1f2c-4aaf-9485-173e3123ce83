package org.swallow.control.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ArchiveDAO;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtException;

import java.util.Collection;

public class ArchiveDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    ArchiveDAO archiveDAO;

    private static final String HOST_ID = "RABO";
    private static final String MODULE_ID = "PREDICT";
    private static final String ARCHIVE_ID = "ARCH01";
    private static final String ARCHIVE_NAME = "ARCHIVE01NAME";
    private static final String DUMMY_DBLINK = "DUMMY_LINK";
    private static final String ARCHIVE_DB_TYPE = "D"; // This is a typo, should be


    @Test
    void testGetArchiveList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Archive> actual = archiveDAO.getArchiveList(HOST_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<Archive> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<Archive>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetArchiveList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected archive list to not be empty");
        }
    }

    @Test
    void testGetCurrentDbList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Archive> actual = archiveDAO.getcurrentDbList(HOST_ID, MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<Archive> expected = JsonTestHelper.readExpected(className, methodName, new TypeReference<>() {});
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetCurrentDbList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected DB list to not be empty");
        }
    }

    @Test
    void testGetActiveDBLink() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        String actual = archiveDAO.getActiveDBLink(HOST_ID, MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            String expected = JsonTestHelper.readExpected(className, methodName, String.class);
            Assertions.assertEquals(expected, actual, "Snapshot mismatch in testGetActiveDBLink");
        } else {
            Assertions.assertNotNull(actual, "Expected DB link to not be null");
        }
    }

    @Test
    void testSaveUpdateDeleteArchive() throws SwtException {
        Archive archive = new Archive();
        archive.getId().setHostId(HOST_ID);
        archive.getId().setArchiveId(ARCHIVE_ID);
        archive.setModuleId(MODULE_ID);
        archive.setDb_link("LINK01");
        archive.setArchiveName(ARCHIVE_NAME);
        archive.setArchiveType(ARCHIVE_DB_TYPE);
        archive.setUpdateUser("User15");
        archive.setDefaultDb("N");
        // Save
        archiveDAO.saveArchive(archive);

        // Update
        archive.setDb_link("LINK02");
        archiveDAO.updateArchive(archive);

        // Delete
        archiveDAO.deleteArchive(archive);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }

    @Test
    void testTestConnection() throws SwtException {
        boolean result = archiveDAO.testConnection(DUMMY_DBLINK, ARCHIVE_DB_TYPE);
        Assertions.assertTrue(true); // Accept any non-crashing result
    }

    @Test
    void testGetDBlink() throws SwtException {
        String result = archiveDAO.getDBlink(ARCHIVE_ID);
        Assertions.assertNotNull(result);
    }
}
