package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ErrorLogDAO;
import org.swallow.control.model.ErrorLog;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class ErrorLogDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ErrorLogDAO errorLogDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";


    @Test
    void testGetErrorLogListByHostAndUser() throws SwtException {
        Date fromDate = new Date();
        Date toDate = new Date();
        Collection result = errorLogDAO.getErrorLogList(HOST_ID, USER_ID, fromDate, toDate);
        assertNotNull(result);
    }

    @Test
    void testLogError() {
        ErrorLog errorLog = new ErrorLog();
        errorLogDAO.logError(errorLog);
    }

    @Test
    void testGetErrorLogListUsingStoredProc() throws SwtException {
        Date fromDate = new Date();
        Date toDate = new Date();
        int currentPage = 1;
        int maxPage = 10;
        List<ErrorLog> errorLogList = new ArrayList<>();
        String filterSortStatus = "filter<split>sort";
        SystemFormats formats = new SystemFormats();
        int result = errorLogDAO.getErrorLogListUsingStoredProc(fromDate, toDate, currentPage, maxPage, errorLogList, filterSortStatus, formats);
        assertNotNull(result);
    }
} 