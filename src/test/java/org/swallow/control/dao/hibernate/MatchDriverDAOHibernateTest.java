package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.MatchDriverDAO;
import org.swallow.control.model.SystemLog;
import org.swallow.exception.SwtException;
import org.swallow.work.model.MatchDriver;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class MatchDriverDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MatchDriverDAO matchDriverDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String USER_ID = "User15";

    @Test
    void testGetMatchDriverList() throws SwtException {
        Collection result = matchDriverDAO.getMatchDriverList(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testUpdateMatchDriverDetail() throws SwtException {
        MatchDriver matchDriver = new MatchDriver();
        matchDriver.setId(new MatchDriver.Id(HOST_ID, ENTITY_ID, CURRENCY_CODE));
        matchDriver.setNewMoveFlag("Y");
        matchDriver.setProcessingFlag("N");
        matchDriver.setUpdateDate(new Date());
        matchDriver.setUpdateUser(USER_ID);
        
        matchDriverDAO.updateMatchDriverDetail(matchDriver);
    }

    @Test
    void testGetMatchDriverListWithCurrency() throws SwtException {
        Collection result = matchDriverDAO.getMatchDriverList(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(result);
    }

    @Test
    void testSaveSystemLog() throws SwtException {
        SystemLog systemLog = new SystemLog();
        systemLog.setHostId(HOST_ID);
        systemLog.setLogDate(new Date());
        systemLog.setUserId(USER_ID);

        matchDriverDAO.saveSystemLog(systemLog);
    }
} 