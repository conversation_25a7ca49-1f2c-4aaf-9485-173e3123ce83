package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.AuditLogDAO;
import org.swallow.control.model.AuditLog;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class AuditLogDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AuditLogDAO auditLogDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABOBRSP";
    private static final String USER_ID = "User15";
    private static final String REFERENCE = "Movement";
    private static final String REFERENCE_ID = "REF123";
    private static final String ACTION = "U";
    private static final String SCREEN_FLAG = "N";
    private static final String SOURCE = "A";
    private static final String DB_LINK = "Null";

    @Test
    void testGetRefIdDetail() throws SwtException {
        Date date = new Date();
        Collection result = auditLogDAO.getRefIdDetail(HOST_ID, USER_ID, REFERENCE, REFERENCE_ID, date, ACTION);
        assertNotNull(result);
    }

    @Test
    void testGetAuditLogListUsingStoredProc() throws SwtException {
        Date fromDate = new Date();
        Date toDate = new Date();
        int currentPage = 1;
        int maxPage = 10;
        List<AuditLog> auditLogList = new ArrayList<>();
        String filterSortStatus = "all,0|true";
        SystemFormats formats = new SystemFormats();
        
        int result = auditLogDAO.getAuditLogListUsingStoredProc(
            HOST_ID, USER_ID, fromDate, toDate, currentPage, maxPage,
            auditLogList, filterSortStatus, SCREEN_FLAG, SOURCE, formats,
            DB_LINK, ENTITY_ID
        );
        assertNotNull(result);
    }
} 