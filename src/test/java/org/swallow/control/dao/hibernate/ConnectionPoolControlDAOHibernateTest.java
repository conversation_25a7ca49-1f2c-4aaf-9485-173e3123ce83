package org.swallow.control.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ConnectionPoolControlDAO;
import org.swallow.control.model.ConnectionPool;

import java.util.ArrayList;
import java.util.Collection;

public class ConnectionPoolControlDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    ConnectionPoolControlDAO connectionPoolControlDAO;

    private static final String HOST_ID = "RABO";
    private static final String MODULE_ID = "PREDICT";
    private static final String CONNECTION_ID = "CONN01";
    private static final String CONNECTION_STATUS = "ACTIVE";

    @Test
    void testGetConnectionPool() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        ConnectionPool conn = new ConnectionPool();
        ConnectionPool.Id id = new ConnectionPool.Id();
        id.setConnectionId(CONNECTION_ID);
        conn.setId(id);
        conn.setStatus(CONNECTION_STATUS);

        ConnectionPool actual = connectionPoolControlDAO.getConnectionPool(conn, MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            ConnectionPool expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    ConnectionPool.class
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetConnectionPool");
        } else {
            Assertions.assertNotNull(actual, "Expected connection pool to not be null");
        }
    }

    @Test
    void testGetConnectionPoolList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        ArrayList<ConnectionPool> openConnections = new ArrayList<>();
        ConnectionPool conn = new ConnectionPool();
        ConnectionPool.Id id = new ConnectionPool.Id();
        id.setConnectionId(CONNECTION_ID);
        conn.setId(id);
        conn.setStatus(CONNECTION_STATUS);
        openConnections.add(conn);

        Collection<ConnectionPool> actual = connectionPoolControlDAO.getConnectionPoolList(openConnections, MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<ConnectionPool> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<ConnectionPool>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetConnectionPoolList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected connection pool list to not be empty");
        }
    }

    @Test
    void testGetConnectionPoolListByModule() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<ConnectionPool> actual = connectionPoolControlDAO.getConnectionPoolListByModule(MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<ConnectionPool> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<ConnectionPool>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetConnectionPoolListByModule");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected connection pool list to not be empty");
        }
    }

    @Test
    void testKillDBSessionConnections() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        String connectionsIds = CONNECTION_ID;
        connectionPoolControlDAO.killDBSessionConnections(MODULE_ID, connectionsIds);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }

    @Test
    void testCheckDBViewsGrant() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        boolean actual = connectionPoolControlDAO.checkDBViewsGrant(MODULE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            boolean expected = JsonTestHelper.readExpected(className, methodName, Boolean.class);
            Assertions.assertEquals(expected, actual, "Snapshot mismatch in testCheckDBViewsGrant");
        } else {
            Assertions.assertTrue(true); // Accept any non-crashing result
        }
    }

    @Test
    void testUpdatePoolStatsTable() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        connectionPoolControlDAO.updatePoolStatsTable(MODULE_ID, 10, 5, 20, 10);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }
} 