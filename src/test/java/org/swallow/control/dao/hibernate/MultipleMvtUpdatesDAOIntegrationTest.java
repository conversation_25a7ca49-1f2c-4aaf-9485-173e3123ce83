package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import data.TestDataFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.MultipleMvtUpdatesDAO;
import org.swallow.control.model.ProcessStatus;
import org.swallow.exception.SwtException;
import org.swallow.work.model.Movement;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

public class MultipleMvtUpdatesDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    MultipleMvtUpdatesDAO multipleMvtUpdatesDAO;

    // 🔒 Static test data — specific to this DAO
    private static final String HOST_ID = TestDataFactory.validHostId();
    private static final String ENTITY_ID = TestDataFactory.validEntityId();
    private static final String MOVEMENT_ID = TestDataFactory.validMovementId();
    private static final String ROLE_ID = TestDataFactory.validRoleId();
    private static final String SEQUENCE_ID = TestDataFactory.validSequenceId();
    private static final String ACTION = "ADD_NOTE";
    private static final String JSON_VALUES = "{}";
    private static final String NOTE_TEXT = "Integration test note";
    private static final String USER_ID = "User15";
    private static final List<Long> MOVEMENT_IDS = List.of(1031315L, 2L, 3L);

    @Test
    void testCheckIfMvtExists_shouldReturnTrueIfExists() throws SwtException {
        logStart("testCheckIfMvtExists_shouldReturnTrueIfExists");

        boolean exists = multipleMvtUpdatesDAO.checkIfMvtExists(HOST_ID, ENTITY_ID, MOVEMENT_ID);
        Assertions.assertTrue(exists, "Expected movement to exist");

        logEnd("testCheckIfMvtExists_shouldReturnTrueIfExists");
    }

    @Test
    void testGetMovementListByIds() throws SwtException, IOException {
        logStart("testGetMovementListByIds");
        String className = this.getClass().getSimpleName();
        String methodName = new Object(){}.getClass().getEnclosingMethod().getName();

        Collection<Movement> result = multipleMvtUpdatesDAO.getMovementList(MOVEMENT_IDS);
        Assertions.assertFalse(result.isEmpty(), "Expected non-empty movement list");

        JsonTestHelper.writeResult(className, methodName, result);
        logEnd("testGetMovementListByIds");
    }

    @Test
    void testGetMovementListByIdsAndEntity() throws SwtException {
        logStart("testGetMovementListByIdsAndEntity");

        Collection<Movement> result = multipleMvtUpdatesDAO.getMovementList(MOVEMENT_IDS, HOST_ID, ENTITY_ID);
        Assertions.assertFalse(result.isEmpty(), "Expected non-empty movement list");

        logEnd("testGetMovementListByIdsAndEntity");
    }

    @Test
    void testCheckIfEntityExists_shouldReturnTrue() throws SwtException {
        logStart("testCheckIfEntityExists_shouldReturnTrue");

        boolean result = multipleMvtUpdatesDAO.checkIfEntityExists(HOST_ID, ENTITY_ID);
        Assertions.assertTrue(result, "Expected entity to exist");

        logEnd("testCheckIfEntityExists_shouldReturnTrue");
    }

    @Test
    void testGetRoleAccessDetails_shouldReturnTrueIfAllowed() throws SwtException {
        logStart("testGetRoleAccessDetails_shouldReturnTrueIfAllowed");

        boolean access = multipleMvtUpdatesDAO.getRoleAccessDetails(HOST_ID, ROLE_ID);
        Assertions.assertTrue(access, "Expected role to have access");

        logEnd("testGetRoleAccessDetails_shouldReturnTrueIfAllowed");
    }

    @Test
    void testGetProcessStatus_shouldReturnValidStatus() throws SwtException {
        logStart("testGetProcessStatus_shouldReturnValidStatus");

        int status = multipleMvtUpdatesDAO.getProcessStatus(SEQUENCE_ID);
        Assertions.assertTrue(status >= 0, "Expected non-negative process status");

        logEnd("testGetProcessStatus_shouldReturnValidStatus");
    }

    @Test
    void testGetProcessDetails_shouldReturnCountsAndMovements() throws SwtException {
        logStart("testGetProcessDetails_shouldReturnCountsAndMovements");

        ProcessStatus ps = multipleMvtUpdatesDAO.getProcessDetails(SEQUENCE_ID, "Y");
        Assertions.assertNotNull(ps, "ProcessStatus should not be null");
        Assertions.assertTrue(ps.getTotalCount() >= 0, "Total count should be non-negative");

        logEnd("testGetProcessDetails_shouldReturnCountsAndMovements");
    }

    @Test
    void testExecuteMainProcedure_shouldRunWithoutError() throws SwtException {
        logStart("testExecuteMainProcedure_shouldRunWithoutError");

        multipleMvtUpdatesDAO.executeMainProcedure(
                MOVEMENT_ID, ACTION, JSON_VALUES, NOTE_TEXT, USER_ID, SEQUENCE_ID
        );

        logEnd("testExecuteMainProcedure_shouldRunWithoutError");
    }

    @Test
    void testCleanTempProcess_shouldRunWithoutError() throws SwtException {
        logStart("testCleanTempProcess_shouldRunWithoutError");

        multipleMvtUpdatesDAO.cleanTempProcess(SEQUENCE_ID);

        logEnd("testCleanTempProcess_shouldRunWithoutError");
    }
}
