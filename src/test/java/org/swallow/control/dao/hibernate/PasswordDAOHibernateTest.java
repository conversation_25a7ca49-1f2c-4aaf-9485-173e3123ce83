package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.PasswordDAO;
import org.swallow.control.model.Password;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class PasswordDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private PasswordDAO passwordDAO;

    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";

    @Test
    void testGetPasswordRules() throws SwtException {
        Collection result = passwordDAO.getPasswordRules(HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testUpdatePasswordRules() throws SwtException {
        Password pwdRules = new Password();
        Password.Id id = new Password.Id();
        id.setHostId(HOST_ID);
        pwdRules.setId(id);
        pwdRules.setAlphaChar(2);
        pwdRules.setNumericChar(2);
        pwdRules.setSpecialChar(1);
        pwdRules.setMinLength(8);
        pwdRules.setMaxLength(16);
        pwdRules.setExpireDays(90);
        pwdRules.setRecentUserpwd(5);
        pwdRules.setUnsuccLoginAttempt(3);
        pwdRules.setUpdateDate(new Date());
        pwdRules.setUpdateUser(USER_ID);
        pwdRules.setExpiryDaysNotice(7);
        pwdRules.setMixedCase("Y");
        passwordDAO.updatePasswordRules(pwdRules);
    }
} 