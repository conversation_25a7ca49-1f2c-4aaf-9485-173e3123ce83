package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.SweepLimitsDAO;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class SweepLimitsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepLimitsDAO sweepLimitsDAO;

    private static final String ROLE_ID = "Role1";
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CCY_GRP_ID = "EUR";

    @Test
    void testGetSweepLimitsDetails() throws SwtException {
        Collection result = sweepLimitsDAO.getSweepLimitsDetails(ROLE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetCurrencyDetails() throws SwtException {
        Collection result = sweepLimitsDAO.getCurrencyDetails(HOST_ID, ENTITY_ID, CCY_GRP_ID);
        assertNotNull(result);
    }
} 