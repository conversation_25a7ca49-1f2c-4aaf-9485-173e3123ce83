package org.swallow.control.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ShortcutDAO;
import org.swallow.control.model.Shortcut;
import org.swallow.exception.SwtException;

import java.util.Collection;

public class ShortcutDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ShortcutDAO shortcutDAO;

    // Use valid test data from your test DB or TestDataFactory if available
    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User15";
    private static final String SHORTCUT_ID = "Sc01";
    private static final String NEW_SHORTCUT_ID = "Sc08";
    private static final String SHORTCUT_NAME = "TestShortcut";
    private static final String MENU_ITEM_ID = "159";

    @Test
    void testGetShortcutDetailList_shouldReturnNonEmptyList() throws SwtException {
        Collection<Shortcut> shortcuts = shortcutDAO.getShortcutDetailList(HOST_ID, USER_ID);
        Assertions.assertNotNull(shortcuts);
        Assertions.assertFalse(shortcuts.isEmpty(), "Expected shortcut list to not be empty");
    }

    @Test
    void testGetMenuList_shouldReturnNonEmptyList() throws SwtException {
        Collection<?> menuList = shortcutDAO.getMenuList();
        Assertions.assertNotNull(menuList);
        Assertions.assertFalse(menuList.isEmpty(), "Expected menu list to not be empty");
    }

    @Test
    void testSaveUpdateDeleteShortcutDetails_shouldCompleteWithoutException() throws SwtException {
        Shortcut shortcut = new Shortcut();
        Shortcut.Id id = new Shortcut.Id();
        id.setHostId(HOST_ID);
        id.setUserId(USER_ID);
        id.setShortcutId(NEW_SHORTCUT_ID);
        shortcut.setId(id);
        shortcut.setShortcutName(SHORTCUT_NAME);
        shortcut.setMenuItemId(MENU_ITEM_ID);

        // Delete
//        shortcutDAO.deleteShortcutDetail(shortcut);

        // Save
        shortcutDAO.saveShortcutDetails(shortcut);

        // Update
        shortcutDAO.updateShortcutDetails(shortcut);

        // Delete
        shortcutDAO.deleteShortcutDetail(shortcut);

        Assertions.assertTrue(true, "Save, update, and delete operations completed without exceptions");
    }

    @Test
    void testEditShortcutDetails_shouldReturnShortcutOrNewInstance() throws SwtException {
        Shortcut shortcut = shortcutDAO.editShortcutDetails(USER_ID, SHORTCUT_ID, HOST_ID);
        Assertions.assertNotNull(shortcut);
        // If shortcut does not exist, a new instance is returned, so no further assertion here
    }
}
