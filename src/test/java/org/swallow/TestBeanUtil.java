package org.swallow;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Test utility for setting up BeanUtil in tests
 */
@Component
public class TestBeanUtil {

    /**
     * Initialize the BeanUtil with the given ApplicationContext
     * This method should be called in test setup
     * 
     * @param applicationContext The Spring ApplicationContext to use
     */
    public static void initializeBeanUtil(ApplicationContext applicationContext) {
        // Use reflection to set the static context field in BeanUtil
        try {
            java.lang.reflect.Field contextField = BeanUtil.class.getDeclaredField("context");
            contextField.setAccessible(true);
            contextField.set(null, applicationContext);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize BeanUtil for testing", e);
        }
    }
}
