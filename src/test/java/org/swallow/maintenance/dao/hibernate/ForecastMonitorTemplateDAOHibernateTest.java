package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.ForecastMonitorTemplateDAO;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.ForecastMonitorTemplateCol;
import org.swallow.maintenance.model.ForecastMonitorTemplateColSrc;
import org.swallow.exception.SwtException;

import java.util.List;

public class ForecastMonitorTemplateDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ForecastMonitorTemplateDAO forecastMonitorTemplateDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";
    private static final String TEMPLATE_ID = "TEMPLATE1";

    private ForecastMonitorTemplate testTemplate;
    private ForecastMonitorTemplateCol testTemplateCol;
    private ForecastMonitorTemplateColSrc testTemplateColSrc;

    @BeforeEach
    void setUp() {
        // Setup test template
        testTemplate = new ForecastMonitorTemplate();
        ForecastMonitorTemplate.Id id = new ForecastMonitorTemplate.Id();
        id.setHostId(HOST_ID);
        id.setTemplateId(TEMPLATE_ID);
        testTemplate.setId(id);
        testTemplate.setUserId(USER_ID);
        testTemplate.setTemplateName("Test Template");
        testTemplate.setPublicTemplate("Y");

        // Setup test template column
        testTemplateCol = new ForecastMonitorTemplateCol();
        ForecastMonitorTemplateCol.Id colId = new ForecastMonitorTemplateCol.Id();
        colId.setHostId(HOST_ID);
        colId.setTemplateId(TEMPLATE_ID);
        testTemplateCol.setId(colId);
        testTemplateCol.setUserId(USER_ID);
        testTemplateCol.setColumnDisplayName("Test Column");

        // Setup test template column source
        testTemplateColSrc = new ForecastMonitorTemplateColSrc();
        ForecastMonitorTemplateColSrc.Id srcId = new ForecastMonitorTemplateColSrc.Id();
        srcId.setHostId(HOST_ID);
        srcId.setTemplateId(TEMPLATE_ID);
        testTemplateColSrc.setId(srcId);
        testTemplateColSrc.setUserId(USER_ID);
    }


    @Test
    void testGetForecastMonitorTemplate() throws SwtException {
        List<ForecastMonitorTemplate> templates = forecastMonitorTemplateDAO.getForecastMonitorTemplate(HOST_ID, USER_ID);
        Assertions.assertNotNull(templates);
    }

    @Test
    void testGetForecastMonitorTemplateCol() throws SwtException {
        List<ForecastMonitorTemplateCol> columns = forecastMonitorTemplateDAO.getForecastMonitorTemplateCol(testTemplateCol);
        Assertions.assertNotNull(columns);
    }

    @Test
    void testGetForecastMonitorTemplateColSrc() throws SwtException {
        List<ForecastMonitorTemplateColSrc> sources = forecastMonitorTemplateDAO.getForecastMonitorTemplateColSrc(testTemplateColSrc);
        Assertions.assertNotNull(sources);
    }

    @Test
    void testGetForecastMonitorTemplateColDefault() throws SwtException {
        List<ForecastMonitorTemplateCol> columns = forecastMonitorTemplateDAO.getForecastMonitorTemplateCol();
        Assertions.assertNotNull(columns);
    }



} 