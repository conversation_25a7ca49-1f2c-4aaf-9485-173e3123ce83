package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.NonWorkdayDAO;
import org.swallow.maintenance.model.NonWorkday;
import org.swallow.util.LabelValueBean;

import java.util.Collection;
import java.util.Date;

public class NonWorkdayDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private NonWorkdayDAO nonWorkdayDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_FACILITY = "TEST_FACILITY";
    private static final String TEST_UPDATE_USER = "TEST_USER";
    private static final String TEST_APPLY_ENTITY_COUNTRY = "1";
    private static final String TEST_APPLY_ACCOUNT_COUNTRY = "2";
    private static final String TEST_APPLY_CURRENCY_COUNTRY = "2";

    private NonWorkday testNonWorkday;

    @BeforeEach
    void setUp() {
        testNonWorkday = new NonWorkday();
        NonWorkday.Id id = new NonWorkday.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setFacility(TEST_FACILITY);
        testNonWorkday.setId(id);
        testNonWorkday.setApplyEntityCountry(TEST_APPLY_ENTITY_COUNTRY);
        testNonWorkday.setApplyAccountCountry(TEST_APPLY_ACCOUNT_COUNTRY);
        testNonWorkday.setApplyCurrencyCountry(TEST_APPLY_CURRENCY_COUNTRY);
        testNonWorkday.setUpdateUser(TEST_UPDATE_USER);
        testNonWorkday.setUpdateDate(new Date());
    }


    /**
     * Tests retrieval of facility list for a given entity and host ID.
     */
    @Test
    void testGetFacilityList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<NonWorkday> actual = nonWorkdayDAO.getFacilityList(TEST_ENTITY_ID, TEST_HOST_ID);
        Assertions.assertNotNull(actual, "Collection should not be null");

    }

    /**
     * Tests retrieval of day list.
     */
    @Test
    void testGetDayList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<LabelValueBean> actual = nonWorkdayDAO.getDayList();
        Assertions.assertNotNull(actual, "Collection should not be null");


    }

    /**
     * Tests saving a non-workday record.
     */
    @Transactional
    @Test
    void testSave() throws Exception {
        // Test save operation
        nonWorkdayDAO.save(testNonWorkday, "save");

        // Verify the record was saved
        Collection<NonWorkday> savedRecords = nonWorkdayDAO.getFacilityList(TEST_ENTITY_ID, TEST_HOST_ID);
        Assertions.assertTrue(savedRecords.stream()
                .anyMatch(r -> r.getId().getFacility().equals(TEST_FACILITY)),
                "Saved record should be retrievable");

        // Test update operation
        testNonWorkday.setApplyEntityCountry("2");
        nonWorkdayDAO.save(testNonWorkday, "update");

        // Verify the update
        savedRecords = nonWorkdayDAO.getFacilityList(TEST_ENTITY_ID, TEST_HOST_ID);
        Assertions.assertTrue(savedRecords.stream()
                .anyMatch(r -> r.getId().getFacility().equals(TEST_FACILITY) && 
                             r.getApplyEntityCountry().equals("2")),
                "Updated record should reflect changes");

        // Test delete operation
        nonWorkdayDAO.save(testNonWorkday, "delete");

        // Verify the deletion
        savedRecords = nonWorkdayDAO.getFacilityList(TEST_ENTITY_ID, TEST_HOST_ID);
        Assertions.assertFalse(savedRecords.stream()
                .anyMatch(r -> r.getId().getFacility().equals(TEST_FACILITY)),
                "Deleted record should not be retrievable");
    }
} 