package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;
import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyGroupDAO;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.Currency;
import org.swallow.exception.SwtException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class CurrencyGroupDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyGroupDAO currencyGroupDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_GROUP_ID = "TEST_GRP";
    private static final String TEST_CURRENCY_GROUP_NAME = "Test Currency Group";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final String TEST_UPDATE_USER = "TEST_USER";

    private CurrencyGroup testCurrencyGroup;

    @BeforeEach
    public void setUp() throws SwtException {
        testCurrencyGroup = new CurrencyGroup();
        testCurrencyGroup.setId(new CurrencyGroup.Id());
        testCurrencyGroup.getId().setHostId(TEST_HOST_ID);
        testCurrencyGroup.getId().setEntityId(TEST_ENTITY_ID);
        testCurrencyGroup.getId().setCurrencyGroupId(TEST_CURRENCY_GROUP_ID);
        testCurrencyGroup.setCurrencyGroupName(TEST_CURRENCY_GROUP_NAME);
        testCurrencyGroup.setNoOfCurrencies("0");
        testCurrencyGroup.setUpdateUser(TEST_UPDATE_USER);
        testCurrencyGroup.setUpdateDate(new Date());

    }






    @Test
    public void testGetCurrencyGroupCurrenciesList() throws Exception {
        Collection<Currency> currencies = currencyGroupDAO.getCurrencyGroupCurrenciesList(
            TEST_HOST_ID, TEST_ENTITY_ID, TEST_CURRENCY_GROUP_ID);
        
        Assertions.assertNotNull(currencies, "Currency list should not be null");
        // Note: The list may be empty if no currencies are assigned to the test group
    }


    @Test
    void testGetNoOfCurrencies() throws SwtException {
        Integer actual = currencyGroupDAO.getNoOfCurrencies(
            TEST_HOST_ID, TEST_ENTITY_ID, TEST_CURRENCY_GROUP_ID);

        Assertions.assertNotNull(actual, "Returned count should not be null");
        Assertions.assertTrue(actual >= 0, "Count should be non-negative");
    }
} 