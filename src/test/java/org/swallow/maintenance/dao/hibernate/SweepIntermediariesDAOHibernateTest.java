package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.SweepIntermediariesDAO;
import org.swallow.maintenance.model.SweepIntermediaries;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class SweepIntermediariesDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepIntermediariesDAO sweepIntermediariesDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String TARGET_BIC = "TESTBIC";
    private static final String INTERMEDIARY = "INTERMEDIARY";
    private static final String ACCOUNT_ID = "TEST_ACCOUNT";

    @Test
    void testGetSweepIntermediariesDetails() throws SwtException {
        Collection result = sweepIntermediariesDAO.getSweepIntermediariesDetails(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testGetCurrencyNameFromMaster() throws SwtException {
        String result = sweepIntermediariesDAO.getCurrencyNameFromMaster(CURRENCY_CODE);
        assertNotNull(result);
    }

    @Test
    void testSaveSweepIntermediariesDetail() throws SwtException {
        SweepIntermediaries sweepIntermediaries = new SweepIntermediaries();
        sweepIntermediaries.getId().setHostId(HOST_ID);
        sweepIntermediaries.getId().setEntityId(ENTITY_ID);
        sweepIntermediaries.getId().setCurrencyCode(CURRENCY_CODE);
        sweepIntermediaries.getId().setTargetBic(TARGET_BIC);
        sweepIntermediaries.setIntermediary(INTERMEDIARY);
        sweepIntermediaries.setAccountId(ACCOUNT_ID);

        sweepIntermediariesDAO.saveSweepIntermediariesDetail(sweepIntermediaries);
        assertNotNull(sweepIntermediaries);
    }

    @Test
    void testUpdateSweepIntermediariesDetail() throws SwtException {
        SweepIntermediaries sweepIntermediaries = new SweepIntermediaries();
        sweepIntermediaries.getId().setHostId(HOST_ID);
        sweepIntermediaries.getId().setEntityId(ENTITY_ID);
        sweepIntermediaries.getId().setCurrencyCode(CURRENCY_CODE);
        sweepIntermediaries.getId().setTargetBic(TARGET_BIC);
        sweepIntermediaries.setIntermediary(INTERMEDIARY);
        sweepIntermediaries.setAccountId(ACCOUNT_ID);

        sweepIntermediariesDAO.updateSweepIntermediariesDetail(sweepIntermediaries);
        assertNotNull(sweepIntermediaries);
    }

    @Test
    void testDeleteSweepIntermediariesDetail() throws SwtException {
        SweepIntermediaries sweepIntermediaries = new SweepIntermediaries();
        sweepIntermediaries.getId().setHostId(HOST_ID);
        sweepIntermediaries.getId().setEntityId(ENTITY_ID);
        sweepIntermediaries.getId().setCurrencyCode(CURRENCY_CODE);
        sweepIntermediaries.getId().setTargetBic(TARGET_BIC);

        sweepIntermediariesDAO.deleteSweepIntermediariesDetail(sweepIntermediaries);
    }
} 