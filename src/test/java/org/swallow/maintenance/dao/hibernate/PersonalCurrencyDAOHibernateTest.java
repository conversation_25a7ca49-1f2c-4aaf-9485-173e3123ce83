package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;
import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.PersonalCurrencyDAO;
import org.swallow.maintenance.model.PersonalCurrency;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;

import java.util.Collection;
import java.util.Date;

public class PersonalCurrencyDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private PersonalCurrencyDAO personalCurrencyDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_USER_ID = "User15";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final String TEST_CURRENCY_NAME = "US Dollar";
    private static final Integer TEST_PRIORITY_ORDER = 1;

    private PersonalCurrency testPersonalCurrency;

    @BeforeEach
    void setUp() {
        testPersonalCurrency = new PersonalCurrency();
        PersonalCurrency.Id id = new PersonalCurrency.Id();
        id.setHostId(TEST_HOST_ID);
        id.setUserId(TEST_USER_ID);
        id.setCurrencyCode(TEST_CURRENCY_CODE);
        id.setScreenId(SwtConstants.CURRENCY_MONITOR_ID);
        testPersonalCurrency.setId(id);
        testPersonalCurrency.setCurrencyName(TEST_CURRENCY_NAME);
        testPersonalCurrency.setPriorityOrder(TEST_PRIORITY_ORDER);
        testPersonalCurrency.setUpdateDate(new Date());
    }

    @AfterEach
    void tearDown() {
        try {
            personalCurrencyDAO.removePersonalCurrencyDetails(testPersonalCurrency);
        } catch (SwtException e) {
            // Ignore cleanup errors
        }
    }

    /**
     * Tests retrieval of personal currency detail list.
     */
    @Test
    void testGetPersonalCurrencyDetailList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection actual = personalCurrencyDAO.getPersonalCurrencyDetailList(TEST_HOST_ID, TEST_USER_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetPersonalCurrencyDetailList");
        } else {
            Assertions.assertNotNull(actual, "Collection should not be null");
        }
    }

    /**
     * Tests saving a personal currency record.
     */
    @Transactional
    @Test
    void testSaveOrUpdatePersonalCurrencyDetails() throws Exception {
        // Test save operation
        personalCurrencyDAO.saveOrUpdatePersonalCurrencyDetails(testPersonalCurrency, "S");

        // Verify the record was saved
        Collection savedRecords = personalCurrencyDAO.getPersonalCurrencyDetailList(TEST_HOST_ID, TEST_USER_ID);
        Assertions.assertTrue(savedRecords.stream()
                .anyMatch(r -> {
                    Object[] row = (Object[]) r;
                    return row[0].equals(TEST_CURRENCY_CODE);
                }),
                "Saved record should be retrievable");

        // Test update operation
        testPersonalCurrency.setPriorityOrder(2);
        personalCurrencyDAO.saveOrUpdatePersonalCurrencyDetails(testPersonalCurrency, "U");

        // Verify the update
        savedRecords = personalCurrencyDAO.getPersonalCurrencyDetailList(TEST_HOST_ID, TEST_USER_ID);
        Assertions.assertTrue(savedRecords.stream()
                .anyMatch(r -> {
                    Object[] row = (Object[]) r;
                    return row[0].equals(TEST_CURRENCY_CODE) && row[1].equals(2);
                }),
                "Updated record should reflect changes");

        // Test delete operation
        personalCurrencyDAO.removePersonalCurrencyDetails(testPersonalCurrency);

        // Verify the deletion
        savedRecords = personalCurrencyDAO.getPersonalCurrencyDetailList(TEST_HOST_ID, TEST_USER_ID);
        Assertions.assertFalse(savedRecords.stream()
                .anyMatch(r -> {
                    Object[] row = (Object[]) r;
                    return row[0].equals(TEST_CURRENCY_CODE);
                }),
                "Deleted record should not be retrievable");
    }
} 