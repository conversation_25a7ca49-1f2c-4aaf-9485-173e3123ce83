package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CashRsrvBalManagmntDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.exception.SwtException;
import org.swallow.work.model.CashManagementVO;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class CashRsrvBalManagmntDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CashRsrvBalManagmntDAO cashRsrvBalManagmntDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetCashRsvBalanceGridData() throws SwtException {
        CashManagementVO cashManagementVO = new CashManagementVO();
        cashManagementVO.setHost_id(HOST_ID);
        cashManagementVO.setEntityId(ENTITY_ID);
        cashManagementVO.setAccountId(ACCOUNT_ID);
        cashManagementVO.setPeriod(30);
        CashManagementVO result = cashRsrvBalManagmntDAO.getCashRsvBalanceGridData(cashManagementVO);
        assertNotNull(result);
    }

    @Test
    void testGetCashRsvBalancePeriod() throws SwtException {
        CashManagementVO cashManagementVO = new CashManagementVO();
        cashManagementVO.setHost_id(HOST_ID);
        cashManagementVO.setEntityId(ENTITY_ID);
        cashManagementVO.setAccountId(ACCOUNT_ID);
        cashManagementVO.setPeriod(30);
        CashManagementVO result = cashRsrvBalManagmntDAO.getCashRsvBalancePeriod(cashManagementVO);
        assertNotNull(result);
    }

    @Test
    void testGetAccountIDDropDown() throws SwtException {
        Collection<AcctMaintenance> accounts = cashRsrvBalManagmntDAO.getAccountIDDropDown(HOST_ID, ENTITY_ID, CURRENCY_CODE, "O");
        assertNotNull(accounts);
    }
} 