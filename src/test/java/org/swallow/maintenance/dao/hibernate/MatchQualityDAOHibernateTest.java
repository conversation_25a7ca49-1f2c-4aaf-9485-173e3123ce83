package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MatchQualityDAO;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class MatchQualityDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MatchQualityDAO matchQualityDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetMatchList() throws SwtException {
        Collection matches = matchQualityDAO.getMatchList(ENTITY_ID, HOST_ID);
        assertNotNull(matches);
    }

    @Test
    void testGetMatchQualityList() throws SwtException {
        MatchQuality matchQuality = matchQualityDAO.getMatchQualityList(HOST_ID, ENTITY_ID, CURRENCY_CODE, 1);
        assertNotNull(matchQuality);
    }

    @Test
    void testGetParamDescAll() throws SwtException {
        Collection params = matchQualityDAO.getParamDescAll();
        assertNotNull(params);
    }

    @Test
    void testGetfilterPositionlevel() throws SwtException {
        Collection levels = matchQualityDAO.getfilterPositionlevel(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(levels);
    }
} 