package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CriticalPaymentTypeDAO;
import org.swallow.maintenance.model.CriticalPaymentExp;
import org.swallow.maintenance.model.CriticalPaymentType;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class CriticalPaymentTypeDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CriticalPaymentTypeDAO criticalPaymentTypeDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";
    private static final String CP_TYPE_ID = "TEST_CP_TYPE";

    @Test
    void testGetCriticalPayTypeList() throws SwtException {
        Collection<CriticalPaymentType> result = criticalPaymentTypeDAO.getCriticalPayTypeList(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testGetCriticalPayTypeDetails() throws SwtException {
        CriticalPaymentType result = criticalPaymentTypeDAO.getCriticalPayTypeDetails(ENTITY_ID, HOST_ID, CP_TYPE_ID);
        assertNotNull(result);
    }

    @Test
    void testGetExpectedTimeList() throws SwtException {
        List<CriticalPaymentExp> result = criticalPaymentTypeDAO.getExpectedTimeList(HOST_ID, ENTITY_ID, CP_TYPE_ID);
        assertNotNull(result);
    }

    @Test
    void testCheckEntityAccess() throws SwtException {
        String result = criticalPaymentTypeDAO.checkEntityAccess(HOST_ID, ROLE_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testSaveCriticalPaymentType() throws SwtException {
        CriticalPaymentType criticalPaymentType = new CriticalPaymentType();
        criticalPaymentType.getId().setHostId(HOST_ID);
        criticalPaymentType.getId().setEntityId(ENTITY_ID);
        criticalPaymentType.getId().setCpTypeId(CP_TYPE_ID);
        criticalPaymentType.setCpTypeDesc("Test Description");
        criticalPaymentType.setCriticalPayCateg("Test Category");
        criticalPaymentType.setOrderInCateg("1");
        criticalPaymentType.setSumToCateg("Y");
        criticalPaymentType.setSumToTotal("N");
        criticalPaymentType.setReportable("Y");
        criticalPaymentType.setReportIndivPay("N");
        criticalPaymentType.setEnableProcess("Y");
        criticalPaymentType.setExecTimeFrame("24");
        criticalPaymentType.setExecWorkDaysOnly("Y");
        criticalPaymentType.setExecFrequencyMins("60");
        criticalPaymentType.setExecStartTime("00:00");
        criticalPaymentType.setExecEndTime("23:59");

        criticalPaymentTypeDAO.saveCriticalPaymentType(criticalPaymentType, "save");
        assertNotNull(criticalPaymentType);
    }

    @Test
    void testDeleteCriticalPayType() throws SwtException {
        CriticalPaymentType criticalPaymentType = new CriticalPaymentType();
        criticalPaymentType.getId().setHostId(HOST_ID);
        criticalPaymentType.getId().setEntityId(ENTITY_ID);
        criticalPaymentType.getId().setCpTypeId(CP_TYPE_ID);
        criticalPaymentTypeDAO.deleteCriticalPayType(criticalPaymentType);
    }

    @Test
    void testSaveCriticalPaymentExp() throws SwtException {
        List<CriticalPaymentExp> list = new ArrayList<>();
        CriticalPaymentExp exp = new CriticalPaymentExp();
        exp.getId().setHostId(HOST_ID);
        exp.getId().setEntityId(ENTITY_ID);
        exp.getId().setCpTypeId(CP_TYPE_ID);
        exp.getId().setCurrencyCode(CURRENCY_CODE);
        exp.setDefaultExpectedTime("12:00");
        list.add(exp);
        criticalPaymentTypeDAO.saveCriticalPaymentExp(list);
    }

    @Test
    void testCrudCriticalPayExp() throws SwtException {
        List<CriticalPaymentExp> addList = new ArrayList<>();
        List<CriticalPaymentExp> updateList = new ArrayList<>();
        List<CriticalPaymentExp> deleteList = new ArrayList<>();
        Long maintEventId = null;

        CriticalPaymentExp exp = new CriticalPaymentExp();
        exp.getId().setHostId(HOST_ID);
        exp.getId().setEntityId(ENTITY_ID);
        exp.getId().setCpTypeId(CP_TYPE_ID);
        exp.getId().setCurrencyCode(CURRENCY_CODE);
        exp.setDefaultExpectedTime("12:00");
        addList.add(exp);

        criticalPaymentTypeDAO.crudCriticalPayExp(addList, updateList, deleteList, maintEventId);
    }

    @Test
    void testValidateAndExecuteUpdate() throws SwtException {
        String tableName = "P_ILM_CRITICAL_PAYMENT_TYPE";
        String setClause = "CP_TYPE_DESC = 'Updated Description'";
        String whereClause = "HOST_ID = '" + HOST_ID + "' AND ENTITY_ID = '" + ENTITY_ID + "' AND CP_TYPE_ID = '" + CP_TYPE_ID + "'";
        int timeoutSeconds = 30;

        CriticalPaymentTypeDAOHibernate.ValidationResult result = criticalPaymentTypeDAO.validateAndExecuteUpdate(tableName, setClause, whereClause, timeoutSeconds);
        assertNotNull(result);
    }
} 