package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.LocationMaintenanceDAO;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class LocationMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private LocationMaintenanceDAO locationMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetLocationIdDetails() throws SwtException {
        Collection locations = locationMaintenanceDAO.getLocationIdDetails("TEST_LOCATION");
        assertNotNull(locations);
    }

    @Test
    void testGetLocationDetails() throws SwtException {
        Collection locations = locationMaintenanceDAO.getLocationDetails(HOST_ID, ENTITY_ID);
        assertNotNull(locations);
    }

    @Test
    void testGetLocationIdFromDB() throws SwtException {
        Collection locations = locationMaintenanceDAO.getLocationIdFromDB(HOST_ID, ROLE_ID);
        assertNotNull(locations);
    }

    @Test
    void testGetAllLocationDetails() throws SwtException {
        Collection locations = locationMaintenanceDAO.getAllLocationDetails(HOST_ID);
        assertNotNull(locations);
    }
} 