package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyInterestDAO;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.exception.SwtException;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;

public class CurrencyInterestDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    CurrencyInterestDAO currencyInterestDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String DATE_FORMAT = "dd/MM/yyyy";
    private static final String FROM_DATE = "01/10/2008";
    private static final String TO_DATE = "01/10/2008";
    private static final String UPDATE_USER = "User15";

    @Test
    void testGetCurrencyInterestList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<CurrencyInterest> actual = currencyInterestDAO.getCurrencyInterestList(
                ENTITY_ID, HOST_ID, CURRENCY_CODE, FROM_DATE, TO_DATE, DATE_FORMAT);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<CurrencyInterest> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<CurrencyInterest>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetCurrencyInterestList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected currency interest list to not be empty");
        }
    }

    @Test
    void testSaveUpdateDeleteCurrencyInterest() throws SwtException {
        CurrencyInterest currencyInterest = new CurrencyInterest();
        CurrencyInterest.Id id = new CurrencyInterest.Id();
        id.setHostId(HOST_ID);
        id.setEntityId(ENTITY_ID);
        id.setCurrencyCode(CURRENCY_CODE);
        id.setInterestRateDate(new Date());
        currencyInterest.setId(id);
        currencyInterest.setInterestRate(new BigDecimal("5.25"));
        currencyInterest.setUpdateUser(UPDATE_USER);

        // Save
        currencyInterestDAO.saveCurrencyInterest(currencyInterest);

        // Update
        currencyInterest.setInterestRate(new BigDecimal("5.50"));
        currencyInterestDAO.updateCurrencyInterestDetail(currencyInterest);

        // Delete
        currencyInterestDAO.deleteCurrencyInterest(currencyInterest);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }

    @Test
    void testGetCurrencyName() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        String actual = currencyInterestDAO.getCurrencyName(CURRENCY_CODE);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            String expected = JsonTestHelper.readExpected(className, methodName, String.class);
            Assertions.assertEquals(expected, actual, "Snapshot mismatch in testGetCurrencyName");
        } else {
            Assertions.assertNotNull(actual, "Expected currency name to not be null");
        }
    }
} 