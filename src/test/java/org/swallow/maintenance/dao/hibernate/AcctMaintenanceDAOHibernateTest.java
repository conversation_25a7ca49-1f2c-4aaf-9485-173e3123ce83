package org.swallow.maintenance.dao.hibernate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Collection;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AcctMaintenanceDAO;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;

public class AcctMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AcctMaintenanceDAO acctMaintenanceDAO;

    private String hostId;
    private String entityId;
    private String accountId;
    private String sweepAccountId;
    private String currencyCode;

    @BeforeEach
    public void setUp() {
        hostId = "RABO";
        entityId = "RABONL2U";
        accountId = "300004737UEF";
        sweepAccountId = "TEST_SWEEP_ACCOUNT";
        currencyCode = "USD";
    }

    @AfterEach
    public void tearDown() throws SwtException {
        // Clean up test data
        Collection<AccSweepSchedule> schedules = acctMaintenanceDAO.getAcctSweepScheduleList(hostId, entityId, accountId, false);
        for (AccSweepSchedule schedule : schedules) {
            acctMaintenanceDAO.deleteAcctScheduleSweep(schedule);
        }

        Collection<AccountSweepBalanceGroup> groups = acctMaintenanceDAO.getAcctSweepBalGrpcoll(hostId, entityId, accountId);
        for (AccountSweepBalanceGroup group : groups) {
            acctMaintenanceDAO.deleteAcctSweepBalGrp(group);
        }
    }

    @Test
    public void testSaveAndGetAccSweepSchedule() throws SwtException {
        // Create test schedule
        AccSweepSchedule schedule = new AccSweepSchedule();
        schedule.setHostId(hostId);
        schedule.setEntityId(entityId);
        schedule.setAccountId(accountId);
        schedule.setCurrencyCode(currencyCode);
        schedule.setSweepAccountHostId(hostId);
        schedule.setSweepAccountEntity(entityId);
        schedule.setSweepAccountId(sweepAccountId);
        schedule.setScheduleFrom("09:00");
        schedule.setScheduleTo("17:00");
        schedule.setTargetBalance(1000.0);
        schedule.setTargetBalanceType("C");
        schedule.setMinAmount(100.0);
        schedule.setSweepDirection("B");
        schedule.setAllowMultiple("N");
        schedule.setSweepFromBalanceType("P");
        schedule.setOtherSweepFromBalType("P");
        schedule.setThisAccSweepBookcodeCr("CR");
        schedule.setThisAccSweepBookcodeDr("DR");
        schedule.setOtherAccSweepBookcodeCr("CR");
        schedule.setOtherAccSweepBookcodeDr("DR");
        schedule.setThisAccSettleMethodCr("SETTLE_CR");
        schedule.setThisAccSettleMethodDr("SETTLE_DR");
        schedule.setOtherAccSettleMethodCr("SETTLE_CR");
        schedule.setOtherAccSettleMethodDr("SETTLE_DR");
        schedule.setSweepOnGroupBalance("N");

        // Save schedule
        acctMaintenanceDAO.saveOrUpdateAcctScheduleSweep(schedule, "save");

        // Retrieve and verify
        Collection<AccSweepSchedule> schedules = acctMaintenanceDAO.getAcctSweepScheduleList(hostId, entityId, accountId, false);
        assertNotNull(schedules);
        assertEquals(1, schedules.size());
        
        AccSweepSchedule retrieved = schedules.iterator().next();
        assertEquals(hostId, retrieved.getHostId());
        assertEquals(entityId, retrieved.getEntityId());
        assertEquals(accountId, retrieved.getAccountId());
        assertEquals(currencyCode, retrieved.getCurrencyCode());
        assertEquals(sweepAccountId, retrieved.getSweepAccountId());
        assertEquals("09:00", retrieved.getScheduleFrom());
        assertEquals("17:00", retrieved.getScheduleTo());
        assertEquals(1000.0, retrieved.getTargetBalance());
        assertEquals("C", retrieved.getTargetBalanceType());
        assertEquals(100.0, retrieved.getMinAmount());
        assertEquals("B", retrieved.getSweepDirection());
        assertEquals("N", retrieved.getAllowMultiple());
        assertEquals("P", retrieved.getSweepFromBalanceType());
        assertEquals("P", retrieved.getOtherSweepFromBalType());
        assertEquals("CR", retrieved.getThisAccSweepBookcodeCr());
        assertEquals("DR", retrieved.getThisAccSweepBookcodeDr());
        assertEquals("CR", retrieved.getOtherAccSweepBookcodeCr());
        assertEquals("DR", retrieved.getOtherAccSweepBookcodeDr());
        assertEquals("SETTLE_CR", retrieved.getThisAccSettleMethodCr());
        assertEquals("SETTLE_DR", retrieved.getThisAccSettleMethodDr());
        assertEquals("SETTLE_CR", retrieved.getOtherAccSettleMethodCr());
        assertEquals("SETTLE_DR", retrieved.getOtherAccSettleMethodDr());
        assertEquals("N", retrieved.getSweepOnGroupBalance());
    }

    @Test
    public void testSaveAndGetAccountSweepBalanceGroup() throws SwtException {
        // Create test group
        AccountSweepBalanceGroup group = new AccountSweepBalanceGroup();
        AccountSweepBalanceGroup.Id id = new AccountSweepBalanceGroup.Id();
        id.setHostId(hostId);
        id.setEntityId(entityId);
        id.setAccountId(accountId);
        id.setSweepAccountId(sweepAccountId);
        group.setId(id);

        // Save group
        acctMaintenanceDAO.saveAcctSweepBalGrp(group);

        // Retrieve and verify
        Collection<AccountSweepBalanceGroup> groups = acctMaintenanceDAO.getAcctSweepBalGrpcoll(hostId, entityId, accountId);
        assertNotNull(groups);
        assertEquals(1, groups.size());
        
        AccountSweepBalanceGroup retrieved = groups.iterator().next();
        assertEquals(hostId, retrieved.getId().getHostId());
        assertEquals(entityId, retrieved.getId().getEntityId());
        assertEquals(accountId, retrieved.getId().getAccountId());
        assertEquals(sweepAccountId, retrieved.getId().getSweepAccountId());
    }

    @Test
    public void testDeleteAccSweepSchedule() throws SwtException {
        // Create and save test schedule
        AccSweepSchedule schedule = new AccSweepSchedule();
        schedule.setHostId(hostId);
        schedule.setEntityId(entityId);
        schedule.setAccountId(accountId);
        schedule.setCurrencyCode(currencyCode);
        schedule.setSweepAccountHostId(hostId);
        schedule.setSweepAccountEntity(entityId);
        schedule.setSweepAccountId(sweepAccountId);
        schedule.setScheduleFrom("09:00");
        schedule.setScheduleTo("17:00");
        schedule.setTargetBalance(1000.0);
        schedule.setTargetBalanceType("C");

        acctMaintenanceDAO.saveOrUpdateAcctScheduleSweep(schedule, "save");

        // Delete schedule
        acctMaintenanceDAO.deleteAcctScheduleSweep(schedule);

        // Verify deletion
        Collection<AccSweepSchedule> schedules = acctMaintenanceDAO.getAcctSweepScheduleList(hostId, entityId, accountId, false);
        assertTrue(schedules.isEmpty());
    }

    @Test
    public void testDeleteAccountSweepBalanceGroup() throws SwtException {
        // Create and save test group
        AccountSweepBalanceGroup group = new AccountSweepBalanceGroup();
        AccountSweepBalanceGroup.Id id = new AccountSweepBalanceGroup.Id();
        id.setHostId(hostId);
        id.setEntityId(entityId);
        id.setAccountId(accountId);
        id.setSweepAccountId(sweepAccountId);
        group.setId(id);

        acctMaintenanceDAO.saveAcctSweepBalGrp(group);

        // Delete group
        acctMaintenanceDAO.deleteAcctSweepBalGrp(group);

        // Verify deletion
        Collection<AccountSweepBalanceGroup> groups = acctMaintenanceDAO.getAcctSweepBalGrpcoll(hostId, entityId, accountId);
        assertTrue(groups.isEmpty());
    }

    @Test
    public void testGetAcctSweepScheduleUsedinCount() throws SwtException {
        // Create and save test schedule
        AccSweepSchedule schedule = new AccSweepSchedule();
        schedule.setHostId(hostId);
        schedule.setEntityId(entityId);
        schedule.setAccountId(accountId);
        schedule.setCurrencyCode(currencyCode);
        schedule.setSweepAccountHostId(hostId);
        schedule.setSweepAccountEntity(entityId);
        schedule.setSweepAccountId(sweepAccountId);
        schedule.setScheduleFrom("09:00");
        schedule.setScheduleTo("17:00");
        schedule.setTargetBalance(1000.0);
        schedule.setTargetBalanceType("C");

        acctMaintenanceDAO.saveOrUpdateAcctScheduleSweep(schedule, "save");

        // Verify count
        long count = acctMaintenanceDAO.getAcctSweepScheduleUsedinCount(hostId, entityId, accountId);
        assertEquals(1, count);
    }
}