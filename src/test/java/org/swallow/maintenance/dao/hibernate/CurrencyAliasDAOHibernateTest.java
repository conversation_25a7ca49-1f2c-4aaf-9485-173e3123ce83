package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyAliasDAO;
import org.swallow.maintenance.model.CurrencyAlias;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class CurrencyAliasDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyAliasDAO currencyAliasDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";
    private static final String ALIAS = "TEST_ALIAS";

    @Test
    void testGetCurrencyAliasList() throws SwtException {
        Collection<Object[]> result = currencyAliasDAO.getCurrencyAliasList(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testSaveCurrencyAliasDetails() throws SwtException {
        CurrencyAlias currencyAlias = new CurrencyAlias();
        currencyAlias.getId().setHostId(HOST_ID);
        currencyAlias.getId().setEntityId(ENTITY_ID);
        currencyAlias.getId().setAlias(ALIAS);
        currencyAlias.setCurrencyCode(CURRENCY_CODE);
        currencyAlias.setUpdateDate(new Date());
        currencyAlias.setUpdateUser(USER_ID);

        currencyAliasDAO.saveCurrencyAliasDetails(currencyAlias);
        assertNotNull(currencyAlias);
    }

    @Test
    void testDeleteCurrencyAliasRecord() throws SwtException {
        CurrencyAlias currencyAlias = new CurrencyAlias();
        currencyAlias.getId().setHostId(HOST_ID);
        currencyAlias.getId().setEntityId(ENTITY_ID);
        currencyAlias.getId().setAlias(ALIAS);
        currencyAlias.setCurrencyCode(CURRENCY_CODE);

        currencyAliasDAO.deleteCurrencyAliasRecord(currencyAlias);
    }
} 