package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.GroupDAO;
import org.swallow.maintenance.model.Group;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class GroupDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private GroupDAO groupDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String GROUP_ID = "TEST_GROUP";
    private static final Integer GROUP_LEVEL = 1;

    private Group testGroup;

    @BeforeEach
    void setUp() throws SwtException {
        // Create a test group
        testGroup = new Group();
        testGroup.setId(new Group.Id(HOST_ID, ENTITY_ID, GROUP_ID));
        testGroup.setGroupName("Test Group");
        testGroup.setGroupLvlCode(GROUP_LEVEL);
        testGroup.setMgroupId("TEST_META");
        testGroup.setCutoffOffset("00:00");
        testGroup.setUpdateDate(new java.util.Date());
        testGroup.setUpdateUser("TEST_USER");

        // Save the test group
    }


    @Test
    void testGetGroupList() throws SwtException {
        Collection groupList = groupDAO.getGroupList(ENTITY_ID, HOST_ID, GROUP_LEVEL);
        assertNotNull(groupList);
        assertFalse(groupList.isEmpty());
    }

    @Test
    void testGetMetaGroupList() throws SwtException {
        Collection metaGroupList = groupDAO.getMetaGroupList(ENTITY_ID, HOST_ID);
        assertNotNull(metaGroupList);
    }

    @Test
    void testSaveGroupDetail() throws SwtException {
        // Create a new group
        Group newGroup = new Group();
        newGroup.setId(new Group.Id(HOST_ID, ENTITY_ID, "NEW_GROUP"));
        newGroup.setGroupName("New Test Group");
        newGroup.setGroupLvlCode(GROUP_LEVEL);
        newGroup.setMgroupId("TEST_META");
        newGroup.setCutoffOffset("00:00");
        newGroup.setUpdateDate(new java.util.Date());
        newGroup.setUpdateUser("TEST_USER");

        // Save the new group
        groupDAO.saveGroupDetail(newGroup);

        // Verify the group was saved
        Group savedGroup = groupDAO.getEditableData(HOST_ID, ENTITY_ID, "NEW_GROUP");
        assertNotNull(savedGroup);
        assertEquals("New Test Group", savedGroup.getGroupName());

        // Clean up
        groupDAO.deleteGroupDetail(newGroup);
    }

    @Test
    void testUpdateGroupDetail() throws SwtException {
        // Update the test group
        testGroup.setGroupName("Updated Group Name");
        groupDAO.updateGroupDetail(testGroup);

        // Verify the update
        Group updatedGroup = groupDAO.getEditableData(HOST_ID, ENTITY_ID, GROUP_ID);
        assertNotNull(updatedGroup);
        assertEquals("Updated Group Name", updatedGroup.getGroupName());
    }

    @Test
    void testDeleteGroupDetail() throws SwtException {
        // Delete the test group
        groupDAO.deleteGroupDetail(testGroup);

        // Verify the group was deleted
        Group deletedGroup = groupDAO.getEditableData(HOST_ID, ENTITY_ID, GROUP_ID);
        assertNull(deletedGroup);
    }

    @Test
    void testGetEditableData() throws SwtException {
        Group editableGroup = groupDAO.getEditableData(HOST_ID, ENTITY_ID, GROUP_ID);
        assertNotNull(editableGroup);
        assertEquals(GROUP_ID, editableGroup.getId().getGroupId());
        assertEquals("Test Group", editableGroup.getGroupName());
    }
} 