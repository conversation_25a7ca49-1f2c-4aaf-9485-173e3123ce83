package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.InterfaceRulesDAO;
import org.swallow.maintenance.model.InterfaceRule;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class InterfaceRulesDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private InterfaceRulesDAO interfaceRulesDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetList() throws SwtException {
        Collection<InterfaceRule> rules = interfaceRulesDAO.getList();
        assertNotNull(rules);
    }

    @Test
    void testGetInterfaceRulesList() throws SwtException {
        Collection<InterfaceRule> rules = interfaceRulesDAO.getInterfaceRulesList("TEST_MSG_TYPE", "TEST_RULE_ID", "N");
        assertNotNull(rules);
    }

    @Test
    void testGetMessageTypesList() throws SwtException {
        Collection<String> messageTypes = interfaceRulesDAO.getMessageTypesList();
        assertNotNull(messageTypes);
    }

} 