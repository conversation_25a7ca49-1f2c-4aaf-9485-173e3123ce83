package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.PartyDAO;
import org.swallow.maintenance.model.Party;

import java.util.Collection;
import java.util.Date;

public class PartyDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private PartyDAO partyDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_PARTY_ID = "TEST_PARTY";
    private static final String TEST_PARTY_NAME = "Test Party";
    private static final String TEST_UPDATE_USER = "TEST_USER";
    private static final String TEST_PARTY_TYPE = "O";
    private static final String TEST_PARENT_PARTY = "PARENT_PARTY";

    private Party testParty;

    @BeforeEach
    void setUp() {
        testParty = new Party();
        Party.Id id = new Party.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setPartyId(TEST_PARTY_ID);
        testParty.setId(id);
        testParty.setPartyName(TEST_PARTY_NAME);
        testParty.setPartyType(TEST_PARTY_TYPE);
        testParty.setParentParty(TEST_PARENT_PARTY);
        testParty.setUpdateUser(TEST_UPDATE_USER);
        testParty.setUpdateDate(new Date());
    }


    /**
     * Tests retrieval of party list.
     */
    @Test
    void testGetPartyList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Party> actual = partyDAO.getPartyList(testParty);
        Assertions.assertNotNull(actual, "Collection should not be null");


    }





    /**
     * Tests getting total count of parties.
     */
    @Test
    void testGetTotalCount() throws Exception {
        int count = partyDAO.getTotalCount(testParty);
        Assertions.assertTrue(count >= 0, "Total count should be non-negative");
    }


} 