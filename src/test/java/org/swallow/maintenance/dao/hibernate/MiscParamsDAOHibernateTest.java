package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MiscParamsDAO;
import org.swallow.maintenance.model.MiscParams;

import java.io.IOException;
import java.util.Collection;
import java.util.Date;

public class MiscParamsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MiscParamsDAO miscParamsDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_KEY1 = "TEST_KEY1";
    private static final String TEST_KEY2 = "TEST_KEY2";
    private static final String TEST_PAR_VALUE = "TEST_VALUE";
    private static final String TEST_UPDATE_USER = "TEST_USER";

    private MiscParams testMiscParams;

    @BeforeEach
    void setUp() {
        // Create test data
        testMiscParams = new MiscParams();
        MiscParams.Id id = new MiscParams.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setKey1(TEST_KEY1);
        id.setKey2(TEST_KEY2);
        testMiscParams.setId(id);
        testMiscParams.setParValue(TEST_PAR_VALUE);
        testMiscParams.setUpdateUser(TEST_UPDATE_USER);
        testMiscParams.setUpdateDate(new Date());
    }

    @AfterEach
    void tearDown() {
        // Clean up test data if needed
    }

    /**
     * Tests retrieval of all misc parameters for a given host ID.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The returned collection contains MiscParams objects
     * 3. The values match expected snapshot if comparison is enabled
     */
    @Test
    void testGetAllValues() throws IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<MiscParams> actual = miscParamsDAO.getAllValues(TEST_HOST_ID);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }

    /**
     * Tests retrieval of all misc parameters when host ID is empty.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection is empty or contains valid MiscParams objects
     */
    @Test
    void testGetAllValuesWithEmptyHostId() throws IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<MiscParams> actual = miscParamsDAO.getAllValues("");

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }

    /**
     * Tests retrieval of all misc parameters when host ID is null.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection is empty or contains valid MiscParams objects
     */
    @Test
    void testGetAllValuesWithNullHostId() throws IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<MiscParams> actual = miscParamsDAO.getAllValues(null);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }

    /**
     * Tests that the getAllValues method returns results in the correct order.
     * This test verifies that:
     * 1. The returned collection is ordered by key1 and parValue
     */
    @Test
    void testGetAllValuesOrdering() throws IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<MiscParams> actual = miscParamsDAO.getAllValues(TEST_HOST_ID);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }
} 