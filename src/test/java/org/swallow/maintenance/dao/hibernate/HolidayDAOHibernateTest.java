package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.HolidayDAO;
import org.swallow.maintenance.model.Holiday;
import org.swallow.exception.SwtException;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class HolidayDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private HolidayDAO holidayDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String FACILITY = "MAIN";

    @Test
    void testGetHolidayList() throws SwtException {
        Collection<Holiday> holidays = holidayDAO.getHolidayList(ENTITY_ID, HOST_ID);
        assertNotNull(holidays);
    }

    @Test
    void testGetBusinessDate() throws SwtException {
        Calendar cal = Calendar.getInstance();
        Calendar businessDate = holidayDAO.getBusinessDate(FACILITY, cal, ENTITY_ID, HOST_ID, CURRENCY_CODE, 0, ACCOUNT_ID);
        assertNotNull(businessDate);
    }

    @Test
    void testGetBusinessDateMinusOne() throws SwtException {
        Calendar cal = Calendar.getInstance();
        Calendar businessDate = holidayDAO.getBusinessDateMinusOne(cal, ENTITY_ID, HOST_ID, CURRENCY_CODE, FACILITY);
        assertNotNull(businessDate);
    }

    @Test
    void testGetNextBusinessDate() throws SwtException {
        Date inputDate = new Date();
        Date nextBusinessDate = holidayDAO.getNextBusinessDate(HOST_ID, ENTITY_ID, inputDate, CURRENCY_CODE, "0", FACILITY);
        assertNotNull(nextBusinessDate);
    }

    @Test
    void testIsWeekend() throws SwtException {
        Calendar cal = Calendar.getInstance();
        boolean isWeekend = holidayDAO.isWeekend(FACILITY, cal, ENTITY_ID, HOST_ID, CURRENCY_CODE);
        assertNotNull(isWeekend);
    }
} 