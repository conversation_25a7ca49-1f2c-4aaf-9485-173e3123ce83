package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyMasterDAO;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.exception.SwtException;

import java.io.IOException;
import java.util.Collection;

public class CurrencyMasterDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyMasterDAO currencyMasterDAO;

    @Test
    void testGetCurrenciesFromMst() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<CurrencyMaster> actual = currencyMasterDAO.getCurrenciesFromMst();

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");
        Assertions.assertFalse(actual.isEmpty(), "Collection should not be empty");

    }
} 