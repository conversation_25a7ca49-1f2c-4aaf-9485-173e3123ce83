package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CountryDAO;
import org.swallow.maintenance.model.Country;
import org.swallow.exception.SwtException;

import java.io.IOException;
import java.util.Collection;

public class CountryDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CountryDAO countryDAO;

    private static final String TEST_COUNTRY_CODE = "US";
    private static final String TEST_COUNTRY_NAME = "United States";
    private static final String TEST_WEEKEND1 = "SAT";
    private static final String TEST_WEEKEND2 = "SUN";

    @BeforeEach
    void setUp() throws SwtException {
        // Create a test country
        Country country = new Country();
        country.setCountryCode(TEST_COUNTRY_CODE);
        country.setCountryName(TEST_COUNTRY_NAME);
        country.setWeekend1(TEST_WEEKEND1);
        country.setWeekend2(TEST_WEEKEND2);

        // Save the test country
        // Note: Since Country is read-only (mutable="false" in hbm.xml), we can't save it directly
        // The test will rely on existing data in the database
    }

    @AfterEach
    void tearDown() throws SwtException {
        // No cleanup needed since Country is read-only
    }

    /**
     * Tests retrieval of the country list.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection contains Country objects
     * 3. The countries are ordered by country code
     */
    @Test
    void testGetCountries() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Country> actual = countryDAO.getCountries();

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");
        Assertions.assertFalse(actual.isEmpty(), "Collection should not be empty");


    }
} 