package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.QualityActionDAO;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;

import java.util.Collection;
import java.util.Date;

public class QualityActionDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private QualityActionDAO qualityActionDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final Integer TEST_POSITION_LEVEL = 1;
    private static final Integer TEST_PARAMETER_ID = 1;
    private static final String TEST_UPDATE_USER = "TEST_USER";

    private QualityAction testQualityAction;
    private MatchQuality testMatchQuality;

    @BeforeEach
    void setUp() {
        // Create test QualityAction
        testQualityAction = new QualityAction();
        QualityAction.Id id = new QualityAction.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setCurrencyCode(TEST_CURRENCY_CODE);
        id.setPositionLevel(TEST_POSITION_LEVEL);
        id.setParameterId(TEST_PARAMETER_ID);
        testQualityAction.setId(id);
        testQualityAction.setMatchQualityA("A");
        testQualityAction.setMatchQualityB("B");
        testQualityAction.setMatchQualityC("C");
        testQualityAction.setMatchQualityD("D");
        testQualityAction.setMatchQualityE("E");
        testQualityAction.setUpdateUser(TEST_UPDATE_USER);
        testQualityAction.setUpdateDate(new Date());

        // Create test MatchQuality
        testMatchQuality = new MatchQuality();
        MatchQuality.Id matchId = new MatchQuality.Id();
        matchId.setHostId(TEST_HOST_ID);
        matchId.setEntityId(TEST_ENTITY_ID);
        matchId.setCurrencyCode(TEST_CURRENCY_CODE);
        matchId.setPosLevel(TEST_POSITION_LEVEL);
        testMatchQuality.setId(matchId);
        testMatchQuality.setMatchQuaA("A");
        testMatchQuality.setMatchQuaB("B");
        testMatchQuality.setMatchQuaC("C");
        testMatchQuality.setMatchQuaD("D");
        testMatchQuality.setMatchQuaE("E");
        testMatchQuality.setUpdateUser(TEST_UPDATE_USER);
        testMatchQuality.setUpdateDate(new Date());
    }


    /**
     * Tests retrieval of quality action list.
     */
    @Test
    void testGetQualityAction() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection actual = qualityActionDAO.getQualityAction(TEST_HOST_ID, TEST_ENTITY_ID, TEST_CURRENCY_CODE, TEST_POSITION_LEVEL);
        Assertions.assertNotNull(actual, "Collection should not be null");

    }






} 