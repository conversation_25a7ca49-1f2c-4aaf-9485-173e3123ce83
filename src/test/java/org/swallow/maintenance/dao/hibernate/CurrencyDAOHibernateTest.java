package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyDAO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.exception.SwtException;

import java.io.IOException;
import java.util.Collection;

public class CurrencyDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyDAO currencyDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final String TEST_CURRENCY_GROUP_ID = "TEST_GROUP";
    private static final int TEST_PRIORITY_ORDER = 1;

    @BeforeEach
    void setUp() throws SwtException {
        // Create a test currency
        Currency currency = new Currency();
        Currency.Id id = new Currency.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setCurrencyCode(TEST_CURRENCY_CODE);
        currency.setId(id);
        
        // Set up currency master
        CurrencyMaster currencyMaster = new CurrencyMaster();
        currencyMaster.setCurrencyCode(TEST_CURRENCY_CODE);
        currency.setCurrencyMaster(currencyMaster);
        
        currency.setCurrencyGroupId(TEST_CURRENCY_GROUP_ID);
        currency.setPriorityOrder(TEST_PRIORITY_ORDER);

    }

    @Test
    void testGetCurrencyDetailList() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Currency> actual = currencyDAO.getCurrencyDetailList(TEST_ENTITY_ID, TEST_HOST_ID);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }



    @Test
    void testUpdateCurrencyDetail() throws SwtException {
        // Get the existing currency
        Collection<Currency> currencies = currencyDAO.getCurrencyDetailList(TEST_ENTITY_ID, TEST_HOST_ID);
        Currency currency = currencies.stream()
            .filter(curr -> curr.getId().getCurrencyCode().equals(TEST_CURRENCY_CODE))
            .findFirst()
            .orElse(null);
        Assertions.assertNotNull(currency, "Test currency should exist");

        // Update the currency
        currency.setPriorityOrder(3);
        currencyDAO.updateCurrencyDetail(currency);

        // Verify the update
        Collection<Currency> afterUpdate = currencyDAO.getCurrencyDetailList(TEST_ENTITY_ID, TEST_HOST_ID);
        Currency updatedCurrency = afterUpdate.stream()
            .filter(curr -> curr.getId().getCurrencyCode().equals(TEST_CURRENCY_CODE))
            .findFirst()
            .orElse(null);
        Assertions.assertNotNull(updatedCurrency, "Updated currency should exist");
        Assertions.assertEquals(3, updatedCurrency.getPriorityOrder(), 
            "Priority order should be updated");
    }

    @Test
    void testGetCurrencies() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<Currency> actual = currencyDAO.getCurrencies(TEST_ENTITY_ID, TEST_CURRENCY_GROUP_ID);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");

    }
} 