package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MaintenanceEventMaintenanceDAO;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.model.MaintenanceEventForm;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class MaintenanceEventMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MaintenanceEventMaintenanceDAO maintenanceEventMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetMaintenanceEvent() throws SwtException {
        MaintenanceEvent event = maintenanceEventMaintenanceDAO.getMaintenanceEvent("1");
        assertNotNull(event);
    }

    @Test
    void testGetMaintenanceEventList() throws SwtException {
        MaintenanceEventForm form = new MaintenanceEventForm();
        form.setPendingChecked(true);
        form.setRejectedChecked(true);
        form.setAcceptedChecked(true);
        form.setAllDates(true);
        form.setSelectedUser("all");
        form.setMaintFacilityId("all");
        
        Collection<MaintenanceEvent> events = maintenanceEventMaintenanceDAO.getMaintenanceEventList(form);
        assertNotNull(events);
    }

    @Test
    void testGetMaintenanceEventDetails() throws SwtException {
        MaintenanceEventDetails details = maintenanceEventMaintenanceDAO.getMaintenanceEventDetails("1", "TEST_TABLE");
        assertNotNull(details);
    }

    @Test
    void testGetMaintenanceEventDetailsList() throws SwtException {
        Collection<MaintenanceEventDetails> details = maintenanceEventMaintenanceDAO.getMaintenanceEventDetailsList();
        assertNotNull(details);
    }

    @Test
    void testGetMaintenanceLogList() throws SwtException {
        Collection logs = maintenanceEventMaintenanceDAO.getMaintenanceLogList("1");
        assertNotNull(logs);
    }

    @Test
    void testGetViewLogDetails() throws SwtException {
        Collection logs = maintenanceEventMaintenanceDAO.getViewLogDetails(
            HOST_ID, USER_ID, new Date(), "127.0.0.1", "TEST_TABLE", "TEST_REF", "TEST_ACTION", 1L);
        assertNotNull(logs);
    }

    @Test
    void testIsRecordRelatedToMaintenanceEvent() throws SwtException {
        boolean isRelated = maintenanceEventMaintenanceDAO.isRecordRelatedToMaintenanceEvent("TEST_RECORD", "TEST_FACILITY");
        assertNotNull(isRelated);
    }
} 