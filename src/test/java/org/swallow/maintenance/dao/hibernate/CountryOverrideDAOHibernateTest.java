package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CountryOverrideDAO;
import org.swallow.maintenance.model.CountryOverride;
import org.swallow.exception.SwtException;
import org.swallow.util.LabelValueBean;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class CountryOverrideDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CountryOverrideDAO countryOverrideDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String COUNTRY_CODE = "NL";
    private static final String WEEKEND1 = "6";
    private static final String WEEKEND2 = "7";
    private static final String OVERRIDE_WEEKEND1 = "5";
    private static final String OVERRIDE_WEEKEND2 = "6";
    private static final String USER_ID = "User15";

    @Test
    void testGetCountryList() throws SwtException {
        Collection<CountryOverride> result = countryOverrideDAO.getCountryList(ENTITY_ID, HOST_ID);
        assertNotNull(result);
    }

    @Test
    void testGetDayList() throws SwtException {
        Collection<LabelValueBean> result = countryOverrideDAO.getDayList();
        assertNotNull(result);
    }

    @Test
    void testSave() throws SwtException {
        CountryOverride countryOverride = new CountryOverride();
        countryOverride.getId().setHostId(HOST_ID);
        countryOverride.getId().setEntityId(ENTITY_ID);
        countryOverride.getId().setCountryCode(COUNTRY_CODE);
        countryOverride.setWeekend1(WEEKEND1);
        countryOverride.setWeekend2(WEEKEND2);
        countryOverride.setOverrideWeekend1(OVERRIDE_WEEKEND1);
        countryOverride.setOverrideWeekend2(OVERRIDE_WEEKEND2);
        countryOverride.setUpdateDate(new Date());
        countryOverride.setUpdateUser(USER_ID);

        countryOverrideDAO.save(countryOverride, "save");
        assertNotNull(countryOverride);
    }

    @Test
    void testUpdate() throws SwtException {
        CountryOverride countryOverride = new CountryOverride();
        countryOverride.getId().setHostId(HOST_ID);
        countryOverride.getId().setEntityId(ENTITY_ID);
        countryOverride.getId().setCountryCode(COUNTRY_CODE);
        countryOverride.setWeekend1(WEEKEND1);
        countryOverride.setWeekend2(WEEKEND2);
        countryOverride.setOverrideWeekend1(OVERRIDE_WEEKEND1);
        countryOverride.setOverrideWeekend2(OVERRIDE_WEEKEND2);
        countryOverride.setUpdateDate(new Date());
        countryOverride.setUpdateUser(USER_ID);

        countryOverrideDAO.save(countryOverride, "update");
        assertNotNull(countryOverride);
    }

    @Test
    void testDelete() throws SwtException {
        CountryOverride countryOverride = new CountryOverride();
        countryOverride.getId().setHostId(HOST_ID);
        countryOverride.getId().setEntityId(ENTITY_ID);
        countryOverride.getId().setCountryCode(COUNTRY_CODE);

        countryOverrideDAO.save(countryOverride, "delete");
    }
} 