package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.ILMGeneralMaintenanceDAO;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMParams;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class ILMGeneralMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ILMGeneralMaintenanceDAO ilmGeneralMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetAccountGroupsList() throws SwtException {
        Collection groups = ilmGeneralMaintenanceDAO.getAccountGroupsList(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(groups);
    }

    @Test
    void testGetILMCcyParametersDetailList() throws SwtException {
        Collection<ILMCcyParameters> parameters = ilmGeneralMaintenanceDAO.getILMCcyParametersDetailList(HOST_ID, ROLE_ID, ENTITY_ID);
        assertNotNull(parameters);
    }

    @Test
    void testGetIlmParamsDetails() {
        ILMParams params = ilmGeneralMaintenanceDAO.getIlmParamsDetails(HOST_ID);
        assertNotNull(params);
    }

    @Test
    void testGetAccountListDetails() throws SwtException {
        Collection accounts = ilmGeneralMaintenanceDAO.getAccountListDetails(ENTITY_ID, CURRENCY_CODE);
        assertNotNull(accounts);
    }

    @Test
    void testGetAllowedEntityList() throws SwtException {
        Collection entities = ilmGeneralMaintenanceDAO.getAllowedEntityList(HOST_ID, ROLE_ID);
        assertNotNull(entities);
    }

    @Test
    void testGetAllowedCurrencyList() throws SwtException {
        Collection currencies = ilmGeneralMaintenanceDAO.getAllowedCurrencyList(HOST_ID, ROLE_ID, ENTITY_ID);
        assertNotNull(currencies);
    }

    @Test
    void testGetCcyProcessStatusDetails() throws SwtException {
        Date startDate = new Date();
        Date endDate = new Date();
        Object[] status = ilmGeneralMaintenanceDAO.getCcyProcessStatusDetails(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, USER_ID, startDate, endDate, "N", "ALL");
        assertNotNull(status);
    }

    @Test
    void testGetAccountName() throws SwtException {
        String accountName = ilmGeneralMaintenanceDAO.getAccountName(HOST_ID, ENTITY_ID, ACCOUNT_ID);
        assertNotNull(accountName);
    }
} 