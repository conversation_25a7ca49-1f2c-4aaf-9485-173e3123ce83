package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MetaGroupDAO;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class MetaGroupDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MetaGroupDAO metaGroupDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String MGROUP_ID = "TEST_MGROUP";
    private static final Integer MGROUP_LEVEL = 1;
    private static final String MGROUP_NAME = "Test Meta Group";

    @Test
    void testGetMetaGroupDetails() {
        Collection result = metaGroupDAO.getMetaGroupDetails(HOST_ID, ENTITY_ID, MGROUP_LEVEL);
        assertNotNull(result);
    }

    @Test
    void testGetMgrpLvlCode() {
        Collection result = metaGroupDAO.getMgrpLvlCode(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }


    @Test
    void testSaveMetaGroup() throws SwtException {
        MetaGroup metaGroup = new MetaGroup();
        metaGroup.getId().setHostId(HOST_ID);
        metaGroup.getId().setEntityId(ENTITY_ID);
        metaGroup.getId().setMgroupId(MGROUP_ID);
        metaGroup.setMgroupName(MGROUP_NAME);
        metaGroup.setMgrpLvlCode(MGROUP_LEVEL);

        metaGroupDAO.saveMetaGroup(metaGroup);
        assertNotNull(metaGroup);
    }

    @Test
    void testDeleteMetaGroup() throws SwtException {
        MetaGroup metaGroup = new MetaGroup();
        metaGroup.getId().setHostId(HOST_ID);
        metaGroup.getId().setEntityId(ENTITY_ID);
        metaGroup.getId().setMgroupId(MGROUP_ID);

        metaGroupDAO.deleteMetaGroup(metaGroup);
    }

    @Test
    void testGroupDetails() {
        Collection result = metaGroupDAO.groupDetails(HOST_ID, ENTITY_ID, MGROUP_ID);
        assertNotNull(result);
    }
} 