package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.GroupLevelDAO;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class GroupLevelDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private GroupLevelDAO groupLevelDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";

    @Test
    void testGetGroupLevelDetail() throws SwtException {
        Collection groupLevelList = groupLevelDAO.getGroupLevelDetail(HOST_ID, ENTITY_ID);
        assertNotNull(groupLevelList);
    }
} 