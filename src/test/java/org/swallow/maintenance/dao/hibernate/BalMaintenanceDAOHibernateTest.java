package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.BalMaintenanceDAO;
import org.swallow.maintenance.model.BalMaintenance;
import org.swallow.maintenance.model.BalType;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

public class BalMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    BalMaintenanceDAO balMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String BALANCE_TYPE = "BAL01";
    private static final String CURRENCY = "USD";
    private static final String ROLE_ID = "ROLE01";
    private static final String START_BALANCE = "1000.00";
    private static final String BAL_TYPE = "TYPE01";

    @Test
    void testGetBalanceList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection actual = balMaintenanceDAO.getBalanceList(HOST_ID, ENTITY_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection expected = JsonTestHelper.readExpected(className, methodName, new TypeReference<>() {});
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetBalanceList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected balance list to not be empty");
        }
    }

    @Test
    void testGetEditableData() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        BalMaintenance actual = balMaintenanceDAO.getEditableData(HOST_ID, ENTITY_ID, BALANCE_TYPE, BAL_TYPE, new Date());

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            BalMaintenance expected = JsonTestHelper.readExpected(className, methodName, BalMaintenance.class);
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetEditableData");
        } else {
            Assertions.assertNotNull(actual, "Expected editable data to not be null");
        }
    }

    @Test
    void testAccountDetailUsingStoredProc() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<BalType> actual = balMaintenanceDAO.accountDetailUsingStoredProc(
                HOST_ID, ENTITY_ID, BALANCE_TYPE, CURRENCY, new Date(), 1, 10, "", ROLE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<BalType> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<BalType>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testAccountDetailUsingStoredProc");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected account details to not be empty");
        }
    }

    @Test
    void testUpdateBalanceDetail() throws SwtException {
        // Create a new balance maintenance
        BalMaintenance balMaintenance = new BalMaintenance();
        BalMaintenance.Id id = new BalMaintenance.Id();
        id.setHostId(HOST_ID);
        id.setEntityId(ENTITY_ID);
        id.setBalanceDate(new Date());
        id.setBalanceTypeId(BAL_TYPE);
        balMaintenance.setId(id);
        balMaintenance.setBalanceType(BALANCE_TYPE);
        balMaintenance.setUpdateDate(new Date());
        balMaintenance.setUpdateUser("User15");

        // Update balance
        balMaintenanceDAO.updateBalanceDetail(balMaintenance, START_BALANCE, HOST_ID, ENTITY_ID, BAL_TYPE, new Date());

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }
} 