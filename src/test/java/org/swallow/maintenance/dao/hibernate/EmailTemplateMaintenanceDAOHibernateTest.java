package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.EmailTemplateMaintenanceDAO;
import org.swallow.maintenance.model.EmailTemplate;
import org.swallow.exception.SwtException;

import java.util.Collection;

public class EmailTemplateMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    EmailTemplateMaintenanceDAO emailTemplateMaintenanceDAO;

    private static final String TEMPLATE_ID = "TEMPLATE01";
    private static final String DESCRIPTION = "Test Template";
    private static final String SUBJECT_CONTENT = "Test Subject";
    private static final String BODY_CONTENT = "Test Body";
    private static final String UPDATE_USER = "User15";

    @Test
    void testGetEmailTemplate() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        EmailTemplate actual = emailTemplateMaintenanceDAO.getEmailTemplate(TEMPLATE_ID);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            EmailTemplate expected = JsonTestHelper.readExpected(className, methodName, EmailTemplate.class);
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetEmailTemplate");
        } else {
            Assertions.assertNotNull(actual, "Expected email template to not be null");
        }
    }

    @Test
    void testGetEmailTemplateList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<EmailTemplate> actual = emailTemplateMaintenanceDAO.getEmailTemplateList();

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<EmailTemplate> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<EmailTemplate>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetEmailTemplateList");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected email template list to not be empty");
        }
    }

    @Test
    void testSaveUpdateDeleteEmailTemplate() throws SwtException {
        // Create a new email template
        EmailTemplate emailTemplate = new EmailTemplate();
        emailTemplate.setTemplateId(TEMPLATE_ID);
        emailTemplate.setDescription(DESCRIPTION);
        emailTemplate.setSubjectContent(SUBJECT_CONTENT);
        emailTemplate.setBodyContent(BODY_CONTENT);
      //  emailTemplate.setUpdateUser(UPDATE_USER);

        // Save
        emailTemplateMaintenanceDAO.saveEmailTemplate(emailTemplate);

        // Update
        emailTemplate.setDescription("Updated Template");
        emailTemplateMaintenanceDAO.updateEmailTemplate(emailTemplate);

        // Delete
        emailTemplateMaintenanceDAO.deleteEmailTemplate(emailTemplate);

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }
} 