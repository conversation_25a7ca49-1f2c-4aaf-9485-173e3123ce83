package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.maintenance.model.Entity;
import org.swallow.exception.SwtException;

import java.util.Collection;

public class EntityDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private EntityDAO entityDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    private Entity testEntity;

    @BeforeEach
    void setUp() {
        // Setup test entity
        testEntity = new Entity();
        Entity.Id id = new Entity.Id();
        id.setHostId(HOST_ID);
        id.setEntityId(ENTITY_ID);
        testEntity.setId(id);
    }

    @AfterEach
    void tearDown() {
        // Cleanup test data if needed
    }

    @Test
    void testGetEntityDetail() throws SwtException {
        Entity entity = entityDAO.getEntityDetail(testEntity);
        Assertions.assertNotNull(entity);
        Assertions.assertEquals(HOST_ID, entity.getId().getHostId());
        Assertions.assertEquals(ENTITY_ID, entity.getId().getEntityId());
    }

    @Test
    void testGetCurrencyDetail() throws SwtException {
        Collection currencyDetails = entityDAO.getCurrencyDetail(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(currencyDetails);
    }

    @Test
    void testGetGroupLevel() throws SwtException {
        Collection groupLevels = entityDAO.getGroupLevel(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(groupLevels);
    }

    @Test
    void testGetMetaGroupLevel() throws SwtException {
        Collection metaGroupLevels = entityDAO.getMetaGroupLevel(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(metaGroupLevels);
    }

    @Test
    void testGetEntityList() throws SwtException {
        Collection entityList = entityDAO.getEntityList(HOST_ID);
        Assertions.assertNotNull(entityList);
    }

    @Test
    void testGetEntityPositionLevels() throws SwtException {
        Collection positionLevels = entityDAO.getEntityPositionLevels(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(positionLevels);
    }

    @Test
    void testGetEditableDataDetails() throws SwtException {
        Collection editableData = entityDAO.getEditableDataDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(editableData);
    }
} 