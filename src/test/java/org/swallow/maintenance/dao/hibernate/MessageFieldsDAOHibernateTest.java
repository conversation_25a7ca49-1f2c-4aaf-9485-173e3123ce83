package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MessageFieldsDAO;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class MessageFieldsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MessageFieldsDAO messageFieldsDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String FORMAT_ID = "TEST_FORMAT";

    @Test
    void testGetMsgFieldDetailList() throws SwtException {
        Collection fields = messageFieldsDAO.getMsgFieldDetailList(HOST_ID, FORMAT_ID, ENTITY_ID);
        assertNotNull(fields);
    }

    @Test
    void testGetMaxSerialNo() throws SwtException {
        Collection maxSerialNo = messageFieldsDAO.getMaxSerialNo(HOST_ID, FORMAT_ID, ENTITY_ID);
        assertNotNull(maxSerialNo);
    }

    @Test
    void testGetKeyWordsList() throws SwtException {
        Collection keywords = messageFieldsDAO.getKeyWordsList();
        assertNotNull(keywords);
    }

    @Test
    void testGetScenarioMsgFieldDetailList() throws SwtException {
        Collection fields = messageFieldsDAO.getScenarioMsgFieldDetailList(FORMAT_ID);
        assertNotNull(fields);
    }
} 