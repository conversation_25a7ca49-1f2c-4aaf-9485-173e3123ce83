package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CorrespondentAcctMaintenanceDAO;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.exception.SwtException;

import java.io.IOException;
import java.util.Collection;

public class CorrespondentAcctMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CorrespondentAcctMaintenanceDAO correspondentAcctMaintenanceDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_MESSAGE_TYPE = null;
    private static final String TEST_CURRENCY_CODE = "All";
    private static final String TEST_CORRES_ACC_ID = "UnitTest";
    private static final String TEST_ACCOUNT_ID = "390823686AED";
    private static final String TEST_USER_ID = "User15";
    // start row number 0
    // end row number 100
    // currency code "All"


    private CorrespondentAcct testCorrespondentAcct;

    @BeforeEach
    void setUp() {
        // Create test data
        testCorrespondentAcct = new CorrespondentAcct();
        testCorrespondentAcct.getId().setHostId("RABO");
        testCorrespondentAcct.getId().setEntityId("RABONL2U");
        testCorrespondentAcct.getId().setMessageType("ACBS");
        testCorrespondentAcct.getId().setCurrencyCode("AED");
        testCorrespondentAcct.getId().setCorresAccId("testMed99");
        testCorrespondentAcct.setAccountId("390843067AED");
    }

    @AfterEach
    void tearDown() {
        // Clean up test data if needed
    }

    /**
     * Tests retrieval of correspondent account list with pagination and sorting.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection contains CorrespondentAcct objects
     */
    @Test
    void testGetList() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<CorrespondentAcct> actual = correspondentAcctMaintenanceDAO.getList(
            TEST_ENTITY_ID, TEST_HOST_ID, 1, 100, TEST_MESSAGE_TYPE, TEST_CURRENCY_CODE,
            TEST_USER_ID, null);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");


    }

    /**
     * Tests retrieval of a specific correspondent account.
     * This test verifies that:
     * 1. The returned CorrespondentAcct is not null
     * 2. The account details match the expected values
     */
    @Test
    void testGetCorrespondentAcct() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        CorrespondentAcct actual = correspondentAcctMaintenanceDAO.getCorrespondentAcct(testCorrespondentAcct);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned CorrespondentAcct should not be null");


    }

    /**
     * Tests retrieval of correspondent account list by criteria.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection contains CorrespondentAcct objects matching the criteria
     */
    @Test
    void testGetCorrespondentAcctList() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<CorrespondentAcct> actual = correspondentAcctMaintenanceDAO.getCorrespondentAcctList(
            TEST_HOST_ID, TEST_ENTITY_ID, TEST_MESSAGE_TYPE, TEST_CURRENCY_CODE);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");


    }

    /**
     * Tests saving a new correspondent account.
     * This test verifies that:
     * 1. The account is saved successfully
     * 2. The saved account can be retrieved
     */
    @Transactional
    @Test
    void testSaveCorrespondentAcct() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        // Save the account
        correspondentAcctMaintenanceDAO.saveCorrespondentAcct(testCorrespondentAcct);

        // Retrieve and verify
        CorrespondentAcct actual = correspondentAcctMaintenanceDAO.getCorrespondentAcct(testCorrespondentAcct);

        // Basic validation
        Assertions.assertNotNull(actual, "Saved CorrespondentAcct should be retrievable");


    }

    /**
     * Tests deletion of a correspondent account.
     * This test verifies that:
     * 1. The account is deleted successfully
     * 2. The deleted account cannot be retrieved
     */
    @Transactional
    @Test
    void testDeleteCorrespondentAcct() throws SwtException {
        // First save the account
//        correspondentAcctMaintenanceDAO.saveCorrespondentAcct(testCorrespondentAcct);

        // Delete the account
        correspondentAcctMaintenanceDAO.deleteCorrespondentAcct(testCorrespondentAcct);

        // Try to retrieve the deleted account
        CorrespondentAcct actual = correspondentAcctMaintenanceDAO.getCorrespondentAcct(testCorrespondentAcct);

        // Verify deletion
        Assertions.assertNull(actual, "Deleted CorrespondentAcct should not be retrievable");
    }

    /**
     * Tests retrieval of account ID details.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection contains AcctMaintenance objects
     */
    @Test
    void testGetAccountIdDetails() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<AcctMaintenance> actual = correspondentAcctMaintenanceDAO.getAccountIdDetails(
            TEST_HOST_ID, TEST_ENTITY_ID, TEST_CURRENCY_CODE);

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");


    }

    /**
     * Tests retrieval of message types list.
     * This test verifies that:
     * 1. The returned collection is not null
     * 2. The collection contains String values
     */
    @Test
    void testGetMessageTypesList() throws SwtException, IOException {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<String> actual = correspondentAcctMaintenanceDAO.getMessageTypesList();

        // Basic validation
        Assertions.assertNotNull(actual, "Returned collection should not be null");


    }
} 