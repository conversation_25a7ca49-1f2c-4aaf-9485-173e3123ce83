package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.ILMTransScenarioMaintenanceDAO;
import org.swallow.maintenance.model.ILMTransactionSetDTL;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class ILMTransScenarioMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ILMTransScenarioMaintenanceDAO ilmTransScenarioMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetILMScenarioList() throws SwtException {
        Collection scenarios = ilmTransScenarioMaintenanceDAO.getILMScenarioList(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(scenarios);
    }

    @Test
    void testGetTransactionList() throws SwtException {
        Collection transactions = ilmTransScenarioMaintenanceDAO.getTransactionList(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(transactions);
    }

    @Test
    void testGetTransactionDetailsList() throws SwtException {
        Collection<ILMTransactionSetDTL> details = ilmTransScenarioMaintenanceDAO.getTransactionDetailsList(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, "TEST_TXN_SET");
        assertNotNull(details);
    }


    @Test
    void testGetFilterConditionResult() throws SwtException {
        String result = ilmTransScenarioMaintenanceDAO.getFilterConditionResult("TEST_CONDITION");
        assertNotNull(result);
    }

    @Test
    void testIsTransactionSetAlreadyUsed() throws SwtException {
        boolean result = ilmTransScenarioMaintenanceDAO.isTransactionSetAlreadyUsed("TEST_TXN_SET");
        assertNotNull(result);
    }

    @Test
    void testGetILMScenarioForReporting() throws SwtException {
        ArrayList<String> entities = new ArrayList<>();
        entities.add(ENTITY_ID);
        ArrayList<String> currencies = new ArrayList<>();
        currencies.add(CURRENCY_CODE);
        Collection scenarios = ilmTransScenarioMaintenanceDAO.getILMScenarioForReporting(HOST_ID, entities, currencies);
        assertNotNull(scenarios);
    }
} 