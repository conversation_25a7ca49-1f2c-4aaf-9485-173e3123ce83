package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.DefaultAcctMaintenanceDAO;
import org.swallow.maintenance.model.DefaultAcct;

import java.util.Collection;
import java.util.Date;

public class DefaultAcctMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private DefaultAcctMaintenanceDAO defaultAcctMaintenanceDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final String TEST_XREF_CODE = "TEST_XREF";
    private static final String TEST_ACCOUNT_ID = "TEST_ACCT";
    private static final boolean TEST_UPDATE_USER = true;

    private DefaultAcct testDefaultAcct;

    @BeforeEach
    public void setUp() throws SwtException {
        testDefaultAcct = new DefaultAcct();
        testDefaultAcct.setId(new DefaultAcct.Id());
        testDefaultAcct.getId().setHostId(TEST_HOST_ID);
        testDefaultAcct.getId().setEntityId(TEST_ENTITY_ID);
        testDefaultAcct.getId().setCurrencyCode(TEST_CURRENCY_CODE);
        testDefaultAcct.getId().setXrefCode(TEST_XREF_CODE);
        testDefaultAcct.setAcctId(TEST_ACCOUNT_ID);
        testDefaultAcct.setUpdateUserNeedUpdated(TEST_UPDATE_USER);
        testDefaultAcct.setUpdateDateNeedUpdated(true);

    }




    @Test
    public void testGetAccountList() throws SwtException {
        Collection accounts = defaultAcctMaintenanceDAO.getAccountList(TEST_HOST_ID, TEST_ENTITY_ID, TEST_CURRENCY_CODE);
        
        Assertions.assertNotNull(accounts, "Account list should not be null");
        // Note: The list may be empty if no accounts are assigned to the test entity/currency
    }



}