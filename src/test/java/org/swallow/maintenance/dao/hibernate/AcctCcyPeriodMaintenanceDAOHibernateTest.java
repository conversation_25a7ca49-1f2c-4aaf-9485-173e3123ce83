package org.swallow.maintenance.dao.hibernate;

import com.fasterxml.jackson.core.type.TypeReference;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.AcctCcyPeriodMaintenanceDAO;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;
import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;

public class AcctCcyPeriodMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    AcctCcyPeriodMaintenanceDAO acctCcyPeriodMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String ACCOUNT_ID = "ACC01";
    private static final String CURRENCY_CODE = "USD";
    private static final String SCREEN_NAME = "TEST_SCREEN";

    @Test
    void testGetAcctCcyPeriodMaintRecords() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<AccountCurrencyPeriodMaintenance> actual = acctCcyPeriodMaintenanceDAO.getAcctCcyPeriodMaintRecords(
                HOST_ID, ENTITY_ID, CURRENCY_CODE, new Date(), "ALL");

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<AccountCurrencyPeriodMaintenance> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<AccountCurrencyPeriodMaintenance>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetAcctCcyPeriodMaintRecords");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected account currency period records to not be empty");
        }
    }

    @Test
    void testGetAcctCcyPeriodMaintRecordsPerAccount() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection<AccountCurrencyPeriodMaintenance> actual = acctCcyPeriodMaintenanceDAO.getAcctCcyPeriodMaintRecordsPerAccount(
                HOST_ID, ENTITY_ID, ACCOUNT_ID, new Date(), "ALL");

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection<AccountCurrencyPeriodMaintenance> expected = JsonTestHelper.readExpected(
                    className,
                    methodName,
                    new TypeReference<Collection<AccountCurrencyPeriodMaintenance>>() {}
            );
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetAcctCcyPeriodMaintRecordsPerAccount");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected account currency period records to not be empty");
        }
    }

    @Test
    void testGetAccountIdDropDown() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Collection actual = acctCcyPeriodMaintenanceDAO.getAccountIdDropDown(HOST_ID, ENTITY_ID, CURRENCY_CODE, SCREEN_NAME);

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            Collection expected = JsonTestHelper.readExpected(className, methodName, new TypeReference<>() {});
            Assertions.assertTrue(JsonTestHelper.isEqual(actual, expected), "Snapshot mismatch in testGetAccountIdDropDown");
        } else {
            Assertions.assertFalse(actual.isEmpty(), "Expected account ID list to not be empty");
        }
    }

    @Test
    void testCheckIfOverlaps() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        boolean actual = acctCcyPeriodMaintenanceDAO.checkIfOverlaps(HOST_ID, ENTITY_ID, ACCOUNT_ID, new Date(), new Date());

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            boolean expected = JsonTestHelper.readExpected(className, methodName, Boolean.class);
            Assertions.assertEquals(expected, actual, "Snapshot mismatch in testCheckIfOverlaps");
        } else {
            Assertions.assertFalse(actual, "Expected no overlap");
        }
    }

    @Test
    void testCheckIfAcctCcyPeriodExists() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        boolean actual = acctCcyPeriodMaintenanceDAO.checkIfAcctCcyPeriodExists(HOST_ID, ENTITY_ID, ACCOUNT_ID, new Date(), new Date());

        if (System.getProperty("writeSnapshots") != null) {
            JsonTestHelper.writeResult(className, methodName, actual);
        } else if (System.getProperty("compareSnapshots") != null) {
            boolean expected = JsonTestHelper.readExpected(className, methodName, Boolean.class);
            Assertions.assertEquals(expected, actual, "Snapshot mismatch in testCheckIfAcctCcyPeriodExists");
        } else {
            Assertions.assertFalse(actual, "Expected no existing period");
        }
    }

    @Test
    void testSaveAndDeleteAcctCcyPeriod() throws SwtException {
        // Create a new account currency period
        AccountCurrencyPeriodMaintenance acctCcyPeriod = new AccountCurrencyPeriodMaintenance();
        AccountCurrencyPeriodMaintenance.Id id = new AccountCurrencyPeriodMaintenance.Id();
        id.setHostId(HOST_ID);
        id.setEntityId(ENTITY_ID);
        id.setAccountId(ACCOUNT_ID);
        acctCcyPeriod.setId(id);
        acctCcyPeriod.setEndDate(new Date());
        acctCcyPeriod.setMinimumReserve(1000.0);
        acctCcyPeriod.setTier(1.0);
        acctCcyPeriod.setTargetAvgBalance(5000.0);
        acctCcyPeriod.setFillDays(5.0);
        acctCcyPeriod.setFillBalance(2000.0);
        acctCcyPeriod.setMinTargetBalance(1000.0);
        acctCcyPeriod.setExcludeFillPeriod("N");
        acctCcyPeriod.setEofBalanceSource("INTERNAL");

        // Save
        acctCcyPeriodMaintenanceDAO.saveAcctCcyPeriodRecord(acctCcyPeriod, "INSERT");

        // Delete
        acctCcyPeriodMaintenanceDAO.deleteAcctCcyPeriodMaintRecord(HOST_ID, ENTITY_ID, ACCOUNT_ID, new Date(), new Date());

        Assertions.assertTrue(true); // Just ensure no exception thrown
    }
} 