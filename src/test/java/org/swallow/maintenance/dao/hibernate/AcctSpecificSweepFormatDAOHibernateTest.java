package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.AcctSpecificSweepFormatDAO;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class AcctSpecificSweepFormatDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AcctSpecificSweepFormatDAO acctSpecificSweepFormatDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetAccountSpecificSweepFormatList() throws SwtException {
        Collection formats = acctSpecificSweepFormatDAO.getAccountSpecificSweepFormatList(HOST_ID, ENTITY_ID, ACCOUNT_ID);
        assertNotNull(formats);
    }

    @Test
    void testGetAccountSpecificSweepFormat() throws SwtException {
        AccountSpecificSweepFormat format = acctSpecificSweepFormatDAO.getAccountSpecificSweepFormat(
            HOST_ID, ENTITY_ID, ACCOUNT_ID, ENTITY_ID, "TEST_ACCOUNT");
        assertNotNull(format);
    }

    @Test
    void testSaveAccountSpecificSweepFormat() throws SwtException {
        AccountSpecificSweepFormat format = new AccountSpecificSweepFormat();
        format.getId().setHostId(HOST_ID);
        format.getId().setEntityId(ENTITY_ID);
        format.getId().setAccountId(ACCOUNT_ID);
        format.getId().setSpecifiedEntityId(ENTITY_ID);
        format.getId().setSpecifiedAccountId("TEST_ACCOUNT");
        acctSpecificSweepFormatDAO.saveAccountSpecificSweepFormat(format);
    }

    @Test
    void testUpdateAccountSpecificSweepFormat() throws SwtException {
        AccountSpecificSweepFormat format = new AccountSpecificSweepFormat();
        format.getId().setHostId(HOST_ID);
        format.getId().setEntityId(ENTITY_ID);
        format.getId().setAccountId(ACCOUNT_ID);
        format.getId().setSpecifiedEntityId(ENTITY_ID);
        format.getId().setSpecifiedAccountId("TEST_ACCOUNT");
        acctSpecificSweepFormatDAO.updateAccountSpecificSweepFormat(format);
    }

    @Test
    void testDeleteAccountSpecificSweepFormat() throws SwtException {
        AccountSpecificSweepFormat format = new AccountSpecificSweepFormat();
        format.getId().setHostId(HOST_ID);
        format.getId().setEntityId(ENTITY_ID);
        format.getId().setAccountId(ACCOUNT_ID);
        format.getId().setSpecifiedEntityId(ENTITY_ID);
        format.getId().setSpecifiedAccountId("TEST_ACCOUNT");
        acctSpecificSweepFormatDAO.deleteAccountSpecificSweepFormat(format);
    }

    @Test
    void testGetSpecificAccountName() throws SwtException {
        String accountName = acctSpecificSweepFormatDAO.getSpecificAccountName(ACCOUNT_ID);
        assertNotNull(accountName);
    }
} 