package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.AccountAttributeMaintenanceDAO;
import org.swallow.maintenance.model.AccountAttribute;
import org.swallow.maintenance.model.AccountAttributeFuncGroup;
import org.swallow.maintenance.model.AccountAttributeHDR;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

public class AccountAttributeMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AccountAttributeMaintenanceDAO accountAttributeMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";



    @Test
    void testGetAcctAttributHDRDetailList() throws SwtException {
        Collection<AccountAttributeHDR> list = accountAttributeMaintenanceDAO.getAcctAttributHDRDetailList("TEXT");
        assertNotNull(list);
    }

    @Test
    void testGetAccountAttributeFuncGroupList() throws SwtException {
        ArrayList<AccountAttributeFuncGroup> list = accountAttributeMaintenanceDAO.getAccountAttributeFuncGroupList("TEST_GROUP", "TEXT");
        assertNotNull(list);
    }

    @Test
    void testGetAccountAttributeList() throws SwtException {
        Collection list = accountAttributeMaintenanceDAO.getAccountAttributeList(HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID, "TEST_ATTR", new Date(), new Date());
        assertNotNull(list);
    }

    @Test
    void testGetAccountList() throws SwtException {
        Collection list = accountAttributeMaintenanceDAO.getAccountList(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        assertNotNull(list);
    }

    @Test
    void testGetAcctAttribute() throws SwtException {
        AccountAttribute acctAttr = accountAttributeMaintenanceDAO.getAcctAttribute("1");
        assertNotNull(acctAttr);
    }

    @Test
    void testGetAcctAttributeList() throws SwtException {
        ArrayList<AccountAttribute> list = accountAttributeMaintenanceDAO.getAcctAttributeList("TEST_ATTR");
        assertNotNull(list);
    }

    @Test
    void testGetLastValuesAccountAttrList() throws SwtException {
        ArrayList<AccountAttribute> list = accountAttributeMaintenanceDAO.getLastValuesAccountAttrList(ENTITY_ID, ACCOUNT_ID, HOST_ID);
        assertNotNull(list);
    }

    @Test
    void testSaveAccountAttributeValue() throws SwtException {
        AccountAttribute acctAttr = new AccountAttribute();
        acctAttr.setHostId(HOST_ID);
        acctAttr.setEntityId(ENTITY_ID);
        acctAttr.setAccountId(ACCOUNT_ID);
        acctAttr.setAttributeId("TEST_ATTR");
        acctAttr.setEffectiveDate(new Date());
        accountAttributeMaintenanceDAO.saveAccountAttributeValue(acctAttr);
    }

} 