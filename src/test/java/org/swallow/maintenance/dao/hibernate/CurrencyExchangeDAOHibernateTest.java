package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.CurrencyExchangeDAO;
import org.swallow.maintenance.model.CurrencyExchange;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.Collection;
import java.util.Date;

public class CurrencyExchangeDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyExchangeDAO currencyExchangeDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_CODE = "USD";
    private static final Date TEST_EXCHANGE_RATE_DATE = new Date();
    private static final String TEST_EXCHANGE_RATE = "1.5";
    private static final String TEST_UPDATE_USER = "TEST_USER";

    private CurrencyExchange testExchange;

    @BeforeEach
    public void setUp() throws SwtException {
        testExchange = new CurrencyExchange();
        CurrencyExchange.Id id = new CurrencyExchange.Id();
        id.setHostId(TEST_HOST_ID);
        id.setEntityId(TEST_ENTITY_ID);
        id.setCurrencyCode(TEST_CURRENCY_CODE);
        id.setExchangeRateDate(TEST_EXCHANGE_RATE_DATE);
        testExchange.setId(id);
        testExchange.setExchangeRate(TEST_EXCHANGE_RATE);
        testExchange.setUpdateUser(TEST_UPDATE_USER);
        testExchange.setUpdateDate(new Date());

    }


    @Test
    public void testGetCurrencyList() throws SwtException {
        Collection<CurrencyMaster> currencyList = currencyExchangeDAO.getCurrencyList(TEST_HOST_ID, TEST_ENTITY_ID);
        
        Assertions.assertNotNull(currencyList, "Currency list should not be null");
        Assertions.assertFalse(currencyList.isEmpty(), "Currency list should not be empty");
    }

    @Test
    public void testGetEntityCurrencyFlag() throws SwtException {
        boolean flag = currencyExchangeDAO.getEntityCurrencyFlag(testExchange);
        Assertions.assertNotNull(flag, "Entity currency flag should not be null");
    }

    @Test
    public void testGetTotalCount() throws SwtException {
        int totalCount = currencyExchangeDAO.getTotalCount(testExchange);
        Assertions.assertTrue(totalCount >= 0, "Total count should be non-negative");
    }

    @Test
    public void testGetFilterCount() throws SwtException {
        int filterCount = currencyExchangeDAO.getFilterCount(testExchange);
        Assertions.assertTrue(filterCount >= 0, "Filter count should be non-negative");
    }


    @Test
    public void testGetCurrencyCodeDetailList() throws SwtException {
        Collection<CurrencyMaster> currencyDetails = currencyExchangeDAO.getCurrencyCodeDetailList(TEST_CURRENCY_CODE);
        
        Assertions.assertNotNull(currencyDetails, "Currency details should not be null");
    }

}