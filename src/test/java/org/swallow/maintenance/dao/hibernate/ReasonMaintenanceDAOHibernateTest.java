package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.ReasonMaintenanceDAO;
import org.swallow.maintenance.model.ReasonMaintenance;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class ReasonMaintenanceDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ReasonMaintenanceDAO reasonMaintenanceDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String REASON_CODE = "TEST_REASON";
    private static final String DESCRIPTION = "Test Reason Description";

    @Test
    void testGetReasonMaintenanceDetails() throws SwtException {
        Collection<ReasonMaintenance> result = reasonMaintenanceDAO.getReasonMaintenanceDetails(HOST_ID, ENTITY_ID);
        assertNotNull(result);
    }

    @Test
    void testSaveReasonMaintenanceDetails() throws SwtException {
        ReasonMaintenance reasonMaintenance = new ReasonMaintenance();
        reasonMaintenance.getId().setHostId(HOST_ID);
        reasonMaintenance.getId().setEntityId(ENTITY_ID);
        reasonMaintenance.getId().setReasonCode(REASON_CODE);
        reasonMaintenance.setDescription(DESCRIPTION);

        reasonMaintenanceDAO.saveReasonMaintenanceDetails(reasonMaintenance);
        assertNotNull(reasonMaintenance);
    }

    @Test
    void testUpdateReasonMaintenanceDetails() throws SwtException {
        ReasonMaintenance reasonMaintenance = new ReasonMaintenance();
        reasonMaintenance.getId().setHostId(HOST_ID);
        reasonMaintenance.getId().setEntityId(ENTITY_ID);
        reasonMaintenance.getId().setReasonCode(REASON_CODE);
        reasonMaintenance.setDescription(DESCRIPTION);

        reasonMaintenanceDAO.updateReasonMaintenanceDetails(reasonMaintenance);
        assertNotNull(reasonMaintenance);
    }

    @Test
    void testDeleteReasonMaintenanceRecord() throws SwtException {
        ReasonMaintenance reasonMaintenance = new ReasonMaintenance();
        reasonMaintenance.getId().setHostId(HOST_ID);
        reasonMaintenance.getId().setEntityId(ENTITY_ID);
        reasonMaintenance.getId().setReasonCode(REASON_CODE);

        reasonMaintenanceDAO.deleteReasonMaintenanceRecord(reasonMaintenance);
    }
} 