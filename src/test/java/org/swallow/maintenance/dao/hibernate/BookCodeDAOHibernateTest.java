package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.BookCodeDAO;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class BookCodeDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private BookCodeDAO bookCodeDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";

    @Test
    void testGetBookCodeDetail() throws SwtException {
        Collection bookCodes = bookCodeDAO.getBookCodeDetail(HOST_ID, ENTITY_ID, "TEST_GROUP");
        assertNotNull(bookCodes);
    }

    @Test
    void testGetAllBookCodeDetail() throws SwtException {
        Collection bookCodes = bookCodeDAO.getAllBookCodeDetail(HOST_ID, ENTITY_ID);
        assertNotNull(bookCodes);
    }

    @Test
    void testGetGroupLevelDetails() throws SwtException {
        Collection groups = bookCodeDAO.getGroupLevelDetails(HOST_ID, ENTITY_ID, 1);
        assertNotNull(groups);
    }

    @Test
    void testGetBookLocations() throws SwtException {
        Collection locations = bookCodeDAO.getBookLocations(HOST_ID, ENTITY_ID);
        assertNotNull(locations);
    }

    @Test
    void testGetBooksLocationId() throws SwtException {
        Collection books = bookCodeDAO.getBooksLocationId("TEST_LOCATION");
        assertNotNull(books);
    }

    @Test
    void testCheckBookCodeAndEntity() throws SwtException {
        boolean exists = bookCodeDAO.checkBookCodeAndEntity(HOST_ID, ENTITY_ID, "TEST_BOOK_CODE");
        assertNotNull(exists);
    }

    @Test
    void testCheckIfBookCodeExists() throws SwtException {
        boolean exists = bookCodeDAO.checkIfBookCodeExists(HOST_ID, "TEST_BOOK_CODE");
        assertNotNull(exists);
    }
} 