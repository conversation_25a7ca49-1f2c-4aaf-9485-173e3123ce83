package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.SysParamsDAO;
import org.swallow.maintenance.model.SysParams;
import org.swallow.exception.SwtException;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class SysParamsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private SysParamsDAO sysParamsDAO;

    private static final String TEST_HOST_ID = "RABO";
    private static final String TEST_ENTITY_ID = "RABONL2U";
    private static final String TEST_CURRENCY_ID = "USD";

    @BeforeEach
    void setUp() {
        // Setup test data if needed
    }

    @AfterEach
    void tearDown() {
        // Cleanup test data if needed
    }

    /**
     * Tests retrieval of system parameters for a given host ID.
     */
    @Test
    void testGetSysParamsDetail() {
        SysParams sysParams = sysParamsDAO.getSysParamsDetail(TEST_HOST_ID);
        assertNotNull(sysParams);
        assertEquals(TEST_HOST_ID, sysParams.getHostId());
    }


    /**
     * Tests retrieval of the database system date.
     */
    @Test
    void testGetDBSytemDate() throws SwtException {
        Date systemDate = sysParamsDAO.getDBSytemDate();
        assertNotNull(systemDate);
    }

    /**
     * Tests retrieval of the system date format string.
     */
    @Test
    void testGetDateFormat() throws SwtException {
        String dateFormat = sysParamsDAO.getDateFormat();
        Assertions.assertNotNull(dateFormat, "Date format string should not be null");
        Assertions.assertFalse(dateFormat.isEmpty(), "Date format string should not be empty");
        // Could add assertion to check if it's a known valid format like 'dd/MM/yyyy' etc.
        System.out.println("System Date Format: " + dateFormat);
    }

    /**
     * Tests retrieval of the entity-specific date information.
     */
    @Test
    void testGetEntityDate() throws SwtException {
        Date systemDate = new Date();
        HashMap<String, Object> entityDateMap = sysParamsDAO.getEntityDate(systemDate, TEST_ENTITY_ID);
        assertNotNull(entityDateMap);
        assertTrue(entityDateMap.containsKey("entityDate"));
        assertTrue(entityDateMap.containsKey("offSet"));
        assertNotNull(entityDateMap.get("entityDate"));
        assertNotNull(entityDateMap.get("offSet"));
    }

    /**
     * Tests retrieval of the database system date adjusted by entity offset.
     */
    @Test
    void testGetDBSytemDateWithEntityOffset() throws SwtException {
        Date systemDate = sysParamsDAO.getDBSytemDateWithEntityOffset(TEST_ENTITY_ID);
        assertNotNull(systemDate);
    }

    /**
     * Tests retrieval of N_DAYS_PRIOR_TO_TODAY and N_DAYS_AHEAD_TO_TODAY parameters.
     */
    @Test
    void testGetPriorAndAheadDaysToToday() throws SwtException {
        List<Integer> daysList = sysParamsDAO.getPriorAndAheadDaysToToday();
        assertNotNull(daysList);
        assertEquals(2, daysList.size()); // Should contain both prior and ahead days
    }

    /**
     * Tests retrieval of the currency group for a specific entity and currency.
     */
    @Test
    void testGetCurrencyGroup() throws SwtException {
        String currencyGroup = sysParamsDAO.getCurrencyGroup(TEST_ENTITY_ID, TEST_CURRENCY_ID);
        assertNotNull(currencyGroup);
    }


    /**
     * Tests retrieval of time zone regions list.
     */
    @Test
    void testGetTimeZoneRegionsList() throws Exception {
        String className = this.getClass().getSimpleName();
        String methodName = new Object() {}.getClass().getEnclosingMethod().getName();

        Date systemDate = sysParamsDAO.getDBSytemDate(); // Use current DB date

        HashMap<String, String> actual = sysParamsDAO.getTimeZoneRegionsList(systemDate);
        Assertions.assertNotNull(actual, "Expected time zone regions map not to be null");

    }

    // Add helper methods if needed, similar to isSameDay in CurrencyExchangeDAOIntegrationTest
} 