package org.swallow.maintenance.dao.hibernate;

import config.BaseDAOIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.maintenance.dao.MessageFormatsDAO;
import org.swallow.util.LabelValueBean;
import org.swallow.exception.SwtException;

import java.util.Collection;

import static org.junit.jupiter.api.Assertions.*;

public class MessageFormatsDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private MessageFormatsDAO messageFormatsDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String FORMAT_ID = "TEST_FORMAT";

    @Test
    void testGetMsgFormatDetailList() throws SwtException {
        Collection formats = messageFormatsDAO.getMsgFormatDetailList(ENTITY_ID, HOST_ID);
        assertNotNull(formats);
    }



    @Test
    void testGetInterfaceId() throws SwtException {
        Collection interfaceIds = messageFormatsDAO.getInterfaceId();
        assertNotNull(interfaceIds);
    }

    @Test
    void testGetMessageFormatPath() throws SwtException {
        String path = messageFormatsDAO.getMessageFormatPath();
        assertNotNull(path);
    }

    @Test
    void testGetScenarioMsgFormatDetailList() throws SwtException {
        Collection formats = messageFormatsDAO.getScenarioMsgFormatDetailList();
        assertNotNull(formats);
    }


    @Test
    void testGetScenMsgFormats() throws SwtException {
        Collection<LabelValueBean> formats = messageFormatsDAO.getScenMsgFormats();
        assertNotNull(formats);
    }
} 