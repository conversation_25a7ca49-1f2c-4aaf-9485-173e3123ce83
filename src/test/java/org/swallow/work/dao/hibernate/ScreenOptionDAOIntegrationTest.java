package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.ScreenOptionDAO;
import org.swallow.work.model.ScreenOption;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;

import java.util.Collection;

public class ScreenOptionDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private ScreenOptionDAO screenOptionDAO;

    // Constants for test data based on actual record
    private static final String HOST_ID = "RABO";
    private static final String USER_ID = "User8";
    private static final String SCREEN_ID = "SWEEP_QUEUE";
    private static final String PROPERTY_NAME = "REFRESH_RATE";
    private static final String PROPERTY_VALUE = "30";

    private ScreenOption testScreenOption;

    @BeforeEach
    void setUp() {
        // Initialize test screen option
        testScreenOption = new ScreenOption();
        testScreenOption.getId().setHostId(HOST_ID);
        testScreenOption.getId().setUserId(USER_ID);
        testScreenOption.getId().setScreenId(SCREEN_ID);
        testScreenOption.getId().setPropertyName(PROPERTY_NAME);
        testScreenOption.setPropertyValue(PROPERTY_VALUE);
    }

    @Test
    void testGetScreenOption() throws SwtException {
        Collection<ScreenOption> result = screenOptionDAO.getScreenOption(testScreenOption);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        ScreenOption screenOption = result.iterator().next();
        Assertions.assertEquals(HOST_ID, screenOption.getId().getHostId());
        Assertions.assertEquals(USER_ID, screenOption.getId().getUserId());
        Assertions.assertEquals(SCREEN_ID, screenOption.getId().getScreenId());
        Assertions.assertEquals(PROPERTY_NAME, screenOption.getId().getPropertyName());
        Assertions.assertEquals(PROPERTY_VALUE, screenOption.getPropertyValue());
    }

    @Test
    void testSaveScreenOption() throws SwtException {
        // First save the screen option
        screenOptionDAO.saveScreenOption(testScreenOption, false);

        // Then verify it was saved by retrieving it
        Collection<ScreenOption> result = screenOptionDAO.getScreenOption(testScreenOption);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        ScreenOption savedScreenOption = result.iterator().next();
        Assertions.assertEquals(HOST_ID, savedScreenOption.getId().getHostId());
        Assertions.assertEquals(USER_ID, savedScreenOption.getId().getUserId());
        Assertions.assertEquals(SCREEN_ID, savedScreenOption.getId().getScreenId());
        Assertions.assertEquals(PROPERTY_NAME, savedScreenOption.getId().getPropertyName());
        Assertions.assertEquals(PROPERTY_VALUE, savedScreenOption.getPropertyValue());

        // Update the screen option
        String newValue = "60";
        savedScreenOption.setPropertyValue(newValue);
        screenOptionDAO.saveScreenOption(savedScreenOption, true);

        // Verify the update
        result = screenOptionDAO.getScreenOption(testScreenOption);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(newValue, result.iterator().next().getPropertyValue());
    }

    @Test
    void testDeleteScreenOption() throws SwtException {
        // First save the screen option
        screenOptionDAO.saveScreenOption(testScreenOption, false);

        // Verify it exists
        Assertions.assertTrue(screenOptionDAO.checkIfScreenOptionsExists(
            HOST_ID, USER_ID, SCREEN_ID, PROPERTY_NAME));

        // Delete the screen option
        screenOptionDAO.deleteScreenOption(testScreenOption);

        // Verify it was deleted
        Assertions.assertFalse(screenOptionDAO.checkIfScreenOptionsExists(
            HOST_ID, USER_ID, SCREEN_ID, PROPERTY_NAME));
    }

    @Test
    void testCheckIfScreenOptionsExists() throws SwtException {
        // First save the screen option
        screenOptionDAO.saveScreenOption(testScreenOption, false);

        // Verify it exists
        Assertions.assertTrue(screenOptionDAO.checkIfScreenOptionsExists(
            HOST_ID, USER_ID, SCREEN_ID, PROPERTY_NAME));

        // Verify it doesn't exist with invalid data
        Assertions.assertFalse(screenOptionDAO.checkIfScreenOptionsExists(
            "INVALID_HOST", "INVALID_USER", "INVALID_SCREEN", "INVALID_PROPERTY"));
    }

    @Test
    void testGetPropertyValue() throws SwtException {
        // First save the screen option
        screenOptionDAO.saveScreenOption(testScreenOption, false);

        // Get the property value
        String result = screenOptionDAO.getPropertyValue(
            HOST_ID, USER_ID, SCREEN_ID, PROPERTY_NAME);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(PROPERTY_VALUE, result);

        // Test with invalid data
        result = screenOptionDAO.getPropertyValue(
            "INVALID_HOST", "INVALID_USER", "INVALID_SCREEN", "INVALID_PROPERTY");
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetScreenOptionWithInvalidData() throws SwtException {
        ScreenOption invalidScreenOption = new ScreenOption();
        invalidScreenOption.getId().setHostId("INVALID_HOST");
        invalidScreenOption.getId().setUserId("INVALID_USER");
        invalidScreenOption.getId().setScreenId("INVALID_SCREEN");
        invalidScreenOption.getId().setPropertyName("INVALID_PROPERTY");

        Collection<ScreenOption> result = screenOptionDAO.getScreenOption(invalidScreenOption);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }
} 