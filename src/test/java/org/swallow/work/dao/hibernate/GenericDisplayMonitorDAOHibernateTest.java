package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.dao.ScenMaintenanceDAO;
import org.swallow.control.model.Scenario;
import org.swallow.control.service.ScenMaintenanceManager;
import org.swallow.exception.SwtException;
import org.swallow.util.CommonDataManager;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.work.dao.GenericDisplayMonitorDAO;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;

import java.util.*;

public class GenericDisplayMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private GenericDisplayMonitorDAO genericDisplayMonitorDAO;
    @Autowired
    private ScenMaintenanceDAO scenMaintenanceDAO;


    // Test constants
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String USER_ID = "User15";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String FACILITY_ID = "MATCH_DISPLAY_SINGLE";
    private static final String SCENARIO_ID = "SYS_MOV_BACKVAL";
    private static final String CURRENCY_GROUP = "EUR";
    private static final String APPLY_CURRENCY_THRESHOLD = "N";
    private static final String DB_LINK = "PREDICT_ARCH";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURR";
    private static final String INVALID_FACILITY_ID = "INVALID_FACILITY";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";

    private GenericDisplayPageDTO testPage;
    private OpTimer testOpTimer;
    private Date testDate;
    private String testBaseQuery;

    @BeforeEach
    void setUp() {
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();


        // Set up test page DTO
        testPage = new GenericDisplayPageDTO();

// Using setters to set the values
        testPage.setTotalSize(0);
        testPage.setPageNumber(2);
        testPage.setRecordsPerPage(100);
        testPage.setFromDate(null);
        testPage.setToDate(null);
        testPage.setSelectedFilter("All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|");
        testPage.setSelectedSort("1");
        testPage.setFacilityRefColumns("None");
        testPage.setScenarioRefColumns("[]");
        testPage.setScenarioRefParams("");
        testPage.setFacilityRefParams("Tm9uZQ((");
        testPage.setMessageStatus(0);
        testPage.setOrderByTag(null);
        testPage.setAllPages(false);


        // Set up OpTimer
        testOpTimer = new OpTimer();

        // Set up base query
        testBaseQuery = "SELECT * FROM DUAL";
    }

    @Test
    void testGetRecords() throws SwtException {
        String result = genericDisplayMonitorDAO.getRecords(testBaseQuery);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.contains(SwtConstants.SEPARATOR_RECORD), 
            "Result should contain separator record");
    }

    @Test
    void testGetRecordsWithInvalidQuery() throws SwtException {
        String result = genericDisplayMonitorDAO.getRecords("INVALID SQL");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.startsWith("-1"), 
            "Result should indicate error with -1");
    }

    @Test
    void testGetGenericDisplayData() throws SwtException {
        QueryResult result = genericDisplayMonitorDAO.getGenericDisplayData(testBaseQuery);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getPage(), "Page should not be null");
        Assertions.assertNotNull(result.getMetadataDetails(), "Metadata details should not be null");
    }


    @Test
    void testGetQueryResultPage() throws SwtException {

      //  page = {GenericDisplayPageDTO@21868}
        //String baseQuery = "WITH TST        AS (SELECT E.HOST_ID,                   E.ENTITY_ID,                   GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT ( ( PKG_NON_WORKDAY.GETPREVBUSINESSDATE (                                                             'SOD_BALANCE',                                                             TRUNC (                                                                GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT (GLOBAL_VAR.SYS_DATE,                                                                                                   ENTITY_ID)),                                                             E.ENTITY_ID,                                                             NULL,                                                             NULL,                                                             1)                                                        + 1),                                                      E.ENTITY_ID,                                                      'REV')";
        String baseQuery = null;
        Scenario scenario = null;
        Collection scenarios = scenMaintenanceDAO.getScenarioList(); // returns raw Collection

        for (Object obj : scenarios) {
            if (obj instanceof Scenario) { // Safe check
                Scenario s = (Scenario) obj;
                if (s.getQueryText() != null && !s.getQueryText().trim().isEmpty()) {
                    scenario = s;
                    break;
                }
            }
        }

        if (scenario != null) {
            baseQuery = scenario.getQueryText();
            // Use baseQuery
        } else {
            // Handle case where no Scenario with non-empty queryText was found
        }
        String scenarioId = null;
        String roleId = null;
        String currencyGroup = null;
        String applyCurrencyThreshold = null;


        QueryResult result = genericDisplayMonitorDAO.getQueryResultPage(
            testPage, testOpTimer, baseQuery, scenarioId, roleId,
                currencyGroup, applyCurrencyThreshold);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getPage(), "Page should not be null");
        Assertions.assertNotNull(result.getQueryResult(), "Query result should not be null");
    }


    @Test
    void testGetQueryResultPageWithAllPages() throws SwtException {
        testPage.setAllPages(true);
        QueryResult result = genericDisplayMonitorDAO.getQueryResultPage(
            testPage, testOpTimer, testBaseQuery, SCENARIO_ID, ROLE_ID, 
            CURRENCY_GROUP, APPLY_CURRENCY_THRESHOLD);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getExecutedQuery(), "Executed query should not be null");
    }

    @Test
    void testGetScreenDetails() throws SwtException {
        ArrayList<String> result = genericDisplayMonitorDAO.getScreenDetails(FACILITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(3, result.size(), "Should return program name, height, and width");
    }


    @Test
    void testGetFacilityAccess() throws SwtException {
        String result = genericDisplayMonitorDAO.getFacilityAccess(
            HOST_ID, ROLE_ID, ENTITY_ID, CURRENCY_CODE, FACILITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.matches("[012]"), "Result should be 0, 1, or 2");
    }



    @Test
    void testGetQueryResultPageWithEmptyFilter() throws SwtException {
        testPage.setSelectedFilter("");
        QueryResult result = genericDisplayMonitorDAO.getQueryResultPage(
            testPage, testOpTimer, testBaseQuery, SCENARIO_ID, ROLE_ID, 
            CURRENCY_GROUP, APPLY_CURRENCY_THRESHOLD);
        
        Assertions.assertNotNull(result, "Result should not be null");
    }


    @Test
    void testGetRecordsWithNullQuery() throws SwtException {
        String result = genericDisplayMonitorDAO.getRecords(null);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.startsWith("-1"), 
            "Result should indicate error with -1");
    }

}