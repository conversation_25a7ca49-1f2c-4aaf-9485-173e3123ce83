package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.util.CommonDataManager;
import org.swallow.util.OpTimer;
import org.swallow.work.dao.ILMAnalysisMonitorDAO;
import org.swallow.work.model.ILMSummaryRecord;

import java.util.*;

public class ILMAnalysisMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ILMAnalysisMonitorDAO ilmAnalysisMonitorDAO;

    private String hostId;
    private String entityId;
    private String currencyId;
    private String userId;
    private String roleId;
    private String dbLink;
    private Date selectedDate;
    private CommonDataManager cdm;

    @BeforeEach
    void setUp() {
        hostId = "RABO";
        entityId = "RABONL2U";
        currencyId = "EUR";
        userId = "User15";
        roleId = "VendorSuppor";
        dbLink = "PREDICT_ARCH";
        
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        selectedDate = cal.getTime();

        // Set up CommonDataManager
        cdm = new CommonDataManager();
        cdm.setUser(new org.swallow.model.User());
        cdm.getUser().getId().setUserId(userId);
    }

    @Test
    void testGetChartsData() throws SwtException {
        String result = ilmAnalysisMonitorDAO.getChartsData(
            hostId, entityId, currencyId, selectedDate, userId, dbLink,
            "ALL", "true", "true");
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetChartsDataWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getChartsData(
                "INVALID", "INVALID", "INVALID", selectedDate, userId, dbLink,
                "INVALID", "INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetScenariosDetail() throws SwtException {
        List<ILMScenario> result = ilmAnalysisMonitorDAO.getScenariosDetail(
            hostId, entityId, currencyId, userId, "Y");
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetScenariosDetailWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getScenariosDetail(
                "INVALID", "INVALID", "INVALID", userId, "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetGroupDetails() throws SwtException {
        ILMAccountGroup result = ilmAnalysisMonitorDAO.getGroupDetails(
            hostId, entityId, currencyId, "MAINEURRABONL2U");
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetGroupDetailsWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getGroupDetails(
                "INVALID", "INVALID", "INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetProcessState() throws SwtException {
        HashMap<String, String> result = ilmAnalysisMonitorDAO.getProcessState(
            hostId, entityId, currencyId, selectedDate, roleId, userId, dbLink);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetProcessStateWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getProcessState(
                "INVALID", "INVALID", "INVALID", selectedDate, "INVALID", "INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetCurrentDbLink() throws SwtException {
        String result = ilmAnalysisMonitorDAO.getCurrentDbLink(hostId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetCurrentDbLinkWithInvalidHostId() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getCurrentDbLink("INVALID");
        }, "Should throw exception for invalid host ID");
    }


    @Test
    void testRecalculateDataWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.recalculateData(
                "INVALID", "INVALID", "INVALID", "INVALID", "INVALID", "INVALID", cdm);
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetNowDates() throws SwtException {
        String[] result = ilmAnalysisMonitorDAO.getNowDates(entityId, currencyId);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(2, result.length, "Should return two dates");
    }

    @Test
    void testGetNowDatesWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getNowDates("INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetEntityCurrency() throws SwtException {
        Collection<CurrencyAccessTO> result = ilmAnalysisMonitorDAO.getEntityCurrency(
            entityId, roleId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetEntityCurrencyWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getEntityCurrency("INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testIsAllEntityAvailable() throws SwtException {
        String result = ilmAnalysisMonitorDAO.isAllEntityAvailable(
            hostId, currencyId, roleId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testIsAllEntityAvailableWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.isAllEntityAvailable("INVALID", "INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetEntitiesHasCurrencies() throws SwtException {
        Collection<EntityUserAccess> result = ilmAnalysisMonitorDAO.getEntitiesHasCurrencies(
            hostId, roleId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetEntitiesHasCurrenciesWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getEntitiesHasCurrencies("INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetGroupsAndScenarioNames() throws SwtException {
        HashMap<String, String> result = ilmAnalysisMonitorDAO.getGroupsAndScenarioNames(
            entityId, currencyId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetGroupsAndScenarioNamesWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getGroupsAndScenarioNames("INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    @Transactional
    void testCleanUpProcessDriver() throws SwtException {
        String sequenceNumber = "TEST_SEQ_" + System.currentTimeMillis();
        ilmAnalysisMonitorDAO.cleanUpProcessDriver(sequenceNumber, userId);
        // No assertion needed as this is a cleanup operation
    }

    @Test
    void testCleanUpProcessDriverWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.cleanUpProcessDriver("INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetILMScenarioDetails() throws SwtException {
        ILMScenario result = ilmAnalysisMonitorDAO.getILMScenarioDetails("SYS01_RABONL2UEUR");
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetILMScenarioDetailsWithInvalidId() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getILMScenarioDetails("INVALID");
        }, "Should throw exception for invalid scenario ID");
    }

    @Test
    void testGetILMGroupDetails() throws SwtException {
        ILMAccountGroup result = ilmAnalysisMonitorDAO.getILMGroupDetails("MAINEURRABONL2U");
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetILMGroupDetailsWithInvalidId() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getILMGroupDetails("INVALID");
        }, "Should throw exception for invalid group ID");
    }

    @Test
    void testGetILMSummaryGridData() throws SwtException {
        HashMap<String, Integer> orderMap = new HashMap<>();
        orderMap.put("column1", 1);
        orderMap.put("column2", 2);

        HashMap<String, ILMSummaryRecord> result = ilmAnalysisMonitorDAO.getILMSummaryGridData(
            hostId, "ALL", selectedDate, dbLink, "Y", "Y", "Y", "Y", "N", "N",
            orderMap, "0.00", userId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetILMSummaryGridDataWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getILMSummaryGridData(
                "INVALID", "INVALID", selectedDate, "INVALID", "INVALID", "INVALID",
                "INVALID", "INVALID", "INVALID", "INVALID", new HashMap<>(), "INVALID", "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetILMOptionGridData() throws SwtException {
        HashMap<String, Integer> orderMap = new HashMap<>();
        orderMap.put("column1", 1);
        orderMap.put("column2", 2);

        HashMap<String, ILMSummaryRecord> result = ilmAnalysisMonitorDAO.getILMOptionGridData(
            orderMap, roleId);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetILMOptionGridDataWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getILMOptionGridData(new HashMap<>(), "INVALID");
        }, "Should throw exception for invalid parameters");
    }

    @Test
    void testGetDataState() throws SwtException {
        String result = ilmAnalysisMonitorDAO.getDataState(
            hostId, entityId, currencyId, selectedDate, dbLink);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetDataStateWithInvalidParameters() {
        Assertions.assertThrows(SwtException.class, () -> {
            ilmAnalysisMonitorDAO.getDataState(
                "INVALID", "INVALID", "INVALID", selectedDate, "INVALID");
        }, "Should throw exception for invalid parameters");
    }
} 