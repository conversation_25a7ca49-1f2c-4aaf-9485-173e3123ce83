package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.CentralBankMonitorDAO;
import org.swallow.work.model.CentralBankMonitor;
import org.swallow.work.web.form.CentralBankRecords;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CentralBankMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CentralBankMonitorDAO centralBankMonitorDAO;

    // Test constants
    private static final String ENTITY_ID = "RABONL2U";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String USER_ID = "User15";
    private static final String SESSION_ID = "TEST_SESSION";
    private static final BigDecimal CURRENCY_LIMIT = new BigDecimal("1000000.00");
    private static final String HIDE_WEEKENDS_FLAG = "Y";

    // Invalid test data
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_ACCOUNT_ID = "INVALID_ACCOUNT";
    private static final String INVALID_USER_ID = "INVALID_USER";
    private static final BigDecimal INVALID_CURRENCY_LIMIT = new BigDecimal("-1000.00");

    private CentralBankMonitor testCentralBankMonitor;
    private Date testStartDate;
    private Date testEndDate;
    private SystemFormats testFormat;
    private OpTimer testOpTimer;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testStartDate = cal.getTime();

        cal.add(Calendar.DAY_OF_MONTH, 7);
        testEndDate = cal.getTime();

        // Set up SystemFormats
        testFormat = new SystemFormats();
        testFormat.setDateFormatValue("dd/MM/yyyy");
        testFormat.setCurrencyFormat("currencyPat2");

        // Set up OpTimer
        testOpTimer = new OpTimer();

        // Initialize test central bank monitor
        testCentralBankMonitor = new CentralBankMonitor();
        CentralBankMonitor.Id id = new CentralBankMonitor.Id();
        id.setEntityId(ENTITY_ID);
        id.setUserId(USER_ID);
        id.setValueDate(testStartDate);
        testCentralBankMonitor.setId(id);
        testCentralBankMonitor.setAmount(new BigDecimal("1000.00"));
        testCentralBankMonitor.setUpdateDate(new Date());
        testCentralBankMonitor.setUpdateUser(USER_ID);
    }

    @Test
    void testGetCentralBankMonitorDetails() throws SwtException {
        List<CentralBankRecords> result = centralBankMonitorDAO.getCentralBankMonitorDetails(
            ENTITY_ID, ACCOUNT_ID, CURRENCY_LIMIT, SESSION_ID, USER_ID,
            testStartDate, testEndDate, testFormat, testOpTimer, HIDE_WEEKENDS_FLAG);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");

        // Verify weekends are hidden when flag is set
        if (HIDE_WEEKENDS_FLAG.equals("Y")) {
            for (CentralBankRecords record : result) {
                // Verify no weekend dates in the results
                // Implementation depends on how dates are stored in CentralBankRecords
            }
        }
    }

    @Test
    void testGetCentralBankMonitorDetailsWithInvalidData() throws SwtException {
        List<CentralBankRecords> result = centralBankMonitorDAO.getCentralBankMonitorDetails(
            INVALID_ENTITY_ID, INVALID_ACCOUNT_ID, INVALID_CURRENCY_LIMIT,
            SESSION_ID, INVALID_USER_ID, testStartDate, testEndDate,
            testFormat, testOpTimer, HIDE_WEEKENDS_FLAG);

        Assertions.assertNotNull(result, "Result should not be null even with invalid data");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetCentralMonitorTempDetails() throws SwtException {
        List<CentralBankMonitor> result = centralBankMonitorDAO.getCentralMonitorTempDetails(
            ENTITY_ID, USER_ID, testStartDate);

        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetCentralMonitorTempDetailsWithInvalidData() throws SwtException {
        List<CentralBankMonitor> result = centralBankMonitorDAO.getCentralMonitorTempDetails(
            INVALID_ENTITY_ID, INVALID_USER_ID, testStartDate);

        Assertions.assertNotNull(result, "Result should not be null even with invalid data");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    @Transactional
    void testSaveCentralBankMonitorDetails() throws SwtException {
        // Test saving new record
        String result = centralBankMonitorDAO.saveCentralBankMonitorDetails(testCentralBankMonitor, true);
        Assertions.assertEquals("true", result, "Save operation should return 'true'");

        // Verify save
        List<CentralBankMonitor> savedRecords = centralBankMonitorDAO.getCentralMonitorTempDetails(
            ENTITY_ID, USER_ID, testStartDate);
        Assertions.assertFalse(savedRecords.isEmpty(), "Saved record should be retrievable");

        CentralBankMonitor saved = savedRecords.get(0);
        Assertions.assertEquals(ENTITY_ID, saved.getId().getEntityId(), "Entity ID should match");
        Assertions.assertEquals(USER_ID, saved.getId().getUserId(), "User ID should match");
        Assertions.assertEquals(0, testCentralBankMonitor.getAmount().compareTo(saved.getAmount()),
            "Amount should match");
    }

    @Test
    @Transactional
    void testUpdateCentralBankMonitorDetails() throws SwtException {
        // First save
        centralBankMonitorDAO.saveCentralBankMonitorDetails(testCentralBankMonitor, false);

        // Update
        BigDecimal newAmount = new BigDecimal("2000.00");
        testCentralBankMonitor.setAmount(newAmount);
        String result = centralBankMonitorDAO.saveCentralBankMonitorDetails(testCentralBankMonitor, false);
        Assertions.assertEquals("true", result, "Update operation should return 'true'");

        // Verify update
        List<CentralBankMonitor> updatedRecords = centralBankMonitorDAO.getCentralMonitorTempDetails(
            ENTITY_ID, USER_ID, testStartDate);
        Assertions.assertFalse(updatedRecords.isEmpty(), "Updated record should be retrievable");

        CentralBankMonitor updated = updatedRecords.get(0);
        Assertions.assertEquals(0, newAmount.compareTo(updated.getAmount()),
            "Amount should be updated");
    }

    @Test
    @Transactional
    void testDeleteCentralBankMonitorDetails() throws SwtException {
        // First save

        // Delete
        centralBankMonitorDAO.deleteCentralBankMonitorDetails(testCentralBankMonitor);

        // Verify deletion
        List<CentralBankMonitor> result = centralBankMonitorDAO.getCentralMonitorTempDetails(
            ENTITY_ID, USER_ID, testStartDate);
        Assertions.assertTrue(result.isEmpty(), "Record should be deleted");
    }

    @Test
    void testGetStartDay() throws SwtException {
        Date result = centralBankMonitorDAO.getStartDay(ENTITY_ID);
        Assertions.assertNotNull(result, "Start day should not be null");
    }

    @Test
    void testGetStartDayWithInvalidData() throws SwtException {
        Assertions.assertThrows(SwtException.class, () -> {
            centralBankMonitorDAO.getStartDay(INVALID_ENTITY_ID);
        }, "Should throw exception for invalid entity ID");
    }
} 