package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.model.*;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import java.util.*;

public class MatchDisplayDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private MatchDisplayDAO matchDisplayDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String MATCH_ID = "1000149";
    private static final String MOVEMENT_ID = "1032973";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String LOCATION_ID = "LOC1";
    private static final Integer POSITION_LEVEL = 1;

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_MATCH_ID = "INVALID_MATCH";
    private static final String INVALID_MOVEMENT_ID = "INVALID_MOVEMENT";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURRENCY";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";
    private static final String INVALID_LOCATION_ID = "INVALID_LOCATION";
    private static final Integer INVALID_POSITION_LEVEL = -1;

    private Date testDate;
    private Match testMatch;
    private Movement testMovement;
    private MatchNote testMatchNote;
    private PrevMatch testPrevMatch;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test objects
        testMatch = new Match();
        testMatch.setId(new Match.Id(HOST_ID, ENTITY_ID, Long.valueOf(MATCH_ID)));
        testMatch.setConfirmedDate(SwtUtil.getSystemDatewithTime());
        testMatch.setStatus(SwtConstants.OFFERD_STATUS);
        testMatch.setHighestPosLev(9);
        testMatch.setStatusUser("User15");
        testMatch.setStatusDate(SwtUtil.getSystemDatewithTime());
        testMatch.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);

        testMovement = new Movement();
        testMovement.setId(new Movement.Id(HOST_ID, ENTITY_ID, Long.valueOf(MOVEMENT_ID), MOVEMENT_ID));
        testMovement.setMatchId(Long.valueOf(MATCH_ID));
        testMovement.setValueDate(testDate);
        testMovement.setAmount(1000.0);
        testMovement.setSign("C");
        testMovement.setPositionLevel(POSITION_LEVEL);
        testMovement.setCurrencyCode(CURRENCY_CODE);
        testMovement.setBookCode("TEST_BOOK");
        testMovement.setBookCodeAvail("Y");
        testMovement.setInputRole(ROLE_ID);
        testMovement.setInputUser("User15");
        testMovement.setUpdateUser("User15");
        testMovement.setInputDate(testDate);
        testMovement.setInputSource(SwtConstants.MOVEMENT_SOURCE_DEFAULT);

        testMatchNote = new MatchNote();
        testMatchNote.setId(new MatchNote.Id(HOST_ID, ENTITY_ID, Long.valueOf(MATCH_ID), testDate, null, null));
        testMatchNote.setNoteText("Test note");
        testMatchNote.setUpdateUser("User15");

        testPrevMatch = new PrevMatch();
        testPrevMatch.setId(new PrevMatch.Id(ENTITY_ID, Long.valueOf(MOVEMENT_ID), Long.valueOf(MATCH_ID)));
        testPrevMatch.setUpdateUser("User15");
        testPrevMatch.setUpdateDate(testDate);
    }

    @Test
    void testGetMovementDetailList() throws SwtException {
        Collection<Movement> result = matchDisplayDAO.getMovementDetailList(ENTITY_ID, HOST_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Verify movement details
        Movement movement = result.iterator().next();
        Assertions.assertEquals(HOST_ID, movement.getId().getHostId(), "Host ID should match");
        Assertions.assertEquals(ENTITY_ID, movement.getId().getEntityId(), "Entity ID should match");
        Assertions.assertEquals(Long.valueOf(MATCH_ID), movement.getMatchId(), "Match ID should match");
    }

    @Test
    void testGetMovementDetailListWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getMovementDetailList(INVALID_ENTITY_ID, INVALID_HOST_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetMatchDetails() throws SwtException {
        Collection<Match> result = matchDisplayDAO.getMatchDetails(ENTITY_ID, HOST_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Verify match details
        Match match = result.iterator().next();
        Assertions.assertEquals(HOST_ID, match.getId().getHostId(), "Host ID should match");
        Assertions.assertEquals(ENTITY_ID, match.getId().getEntityId(), "Entity ID should match");
        Assertions.assertEquals(Long.valueOf(MATCH_ID), match.getId().getMatchId(), "Match ID should match");
    }

    @Test
    void testGetMatchDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getMatchDetails(INVALID_ENTITY_ID, INVALID_HOST_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testInsertPrevMatch() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            matchDisplayDAO.insertPrevMatch(testPrevMatch);
        }, "Should not throw exception when inserting valid prev match");
    }

    @Test
    void testInsertPrevMatchWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            PrevMatch invalidPrevMatch = new PrevMatch();
            invalidPrevMatch.setId(new PrevMatch.Id(null, null, null));
            matchDisplayDAO.insertPrevMatch(invalidPrevMatch);
        }, "Should throw SwtException for invalid prev match data");
    }

    @Test
    void testGetPrevMatch() throws SwtException {
        Collection<String> result = matchDisplayDAO.getPrevMatch(HOST_ID, ENTITY_ID, MOVEMENT_ID, false);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPrevMatchWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getPrevMatch(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MOVEMENT_ID, false);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testUpdateMovements() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
          Collection<Movement> mov=   matchDisplayDAO.getMovementDetails("RABONL2U","RABO",MOVEMENT_ID);
          Movement mov1 = mov.iterator().next();
          mov1.setReference1("atef");
            matchDisplayDAO.updateMovements(mov1);
        }, "Should not throw exception when updating valid movement");
    }

    @Test
    void testUpdateMovementsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            Movement invalidMovement = new Movement();
            invalidMovement.setId(new Movement.Id(null, null, null, null));
            matchDisplayDAO.updateMovements(invalidMovement);
        }, "Should throw SwtException for invalid movement data");
    }

    @Test
    void testUpdateMatch() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            matchDisplayDAO.updateMatch(testMatch);
        }, "Should not throw exception when updating valid match");
    }

    @Test
    void testUpdateMatchWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            Match invalidMatch = new Match();
            invalidMatch.setId(new Match.Id(null, null, null));
            matchDisplayDAO.updateMatch(invalidMatch);
        }, "Should throw SwtException for invalid match data");
    }

    @Test
    void testCheckLocked() throws SwtException {
        int result = matchDisplayDAO.checkLocked(HOST_ID, Long.valueOf(MOVEMENT_ID));
        Assertions.assertTrue(result >= 0, "Lock status should be 0 or 1");
    }

    @Test
    void testCheckLockedWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.checkLocked(INVALID_HOST_ID, null);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetMatchNotes() throws SwtException {
        Collection<MatchNote> result = matchDisplayDAO.getMatchNotes(ENTITY_ID, HOST_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMatchNotesWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getMatchNotes(INVALID_ENTITY_ID, INVALID_HOST_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testCheckNotes() throws SwtException {
        boolean result = matchDisplayDAO.checkNotes(ENTITY_ID, HOST_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testCheckNotesWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.checkNotes(INVALID_ENTITY_ID, INVALID_HOST_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetPositionLevelRecord() throws SwtException {
        Collection<EntityPositionLevel> result = matchDisplayDAO.getPositionLevelRecord(HOST_ID, ENTITY_ID, POSITION_LEVEL);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPositionLevelRecordWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getPositionLevelRecord(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_POSITION_LEVEL);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetNoOfNotes() throws SwtException {
        Integer result = matchDisplayDAO.getNoOfNotes(ENTITY_ID, HOST_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result >= 0, "Number of notes should be non-negative");
    }

    @Test
    void testGetNoOfNotesWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getNoOfNotes(INVALID_ENTITY_ID, INVALID_HOST_ID, INVALID_MOVEMENT_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetAllPositionLevels() throws SwtException {
        List<EntityPositionLevel> result = matchDisplayDAO.getAllPositionLevels(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetAllPositionLevelsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getAllPositionLevels(INVALID_HOST_ID, INVALID_ENTITY_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetMatchCount() throws SwtException {
        Integer result = matchDisplayDAO.getMatchCount(HOST_ID, ENTITY_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result >= 0, "Match count should be non-negative");
    }

    @Test
    void testGetMatchCountWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getMatchCount(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetEntityName() throws SwtException {
        String result = matchDisplayDAO.getEntityName(ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result, "Entity name should not be null");
    }

    @Test
    void testGetEntityNameWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getEntityName(INVALID_ENTITY_ID, INVALID_HOST_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetMovementDetailsByMatch() throws SwtException {
        Collection<Movement> result = matchDisplayDAO.getMovementDetailsByMatch(ENTITY_ID, HOST_ID, Long.valueOf(MATCH_ID));
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMovementDetailsByMatchWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            matchDisplayDAO.getMovementDetailsByMatch(INVALID_ENTITY_ID, INVALID_HOST_ID, null);
        }, "Should throw SwtException for invalid data");
    }
} 