package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.work.dao.InputExceptionsMessagesDAO;
import org.swallow.work.model.InputExceptionsDataModel;
import org.swallow.work.model.InputExceptionsDataPageDTO;
import org.swallow.work.model.InputMessageType;

import java.util.Collection;
import java.util.Date;

public class InputExceptionsMessagesDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private InputExceptionsMessagesDAO inputExceptionsMessagesDAO;

    @Autowired
    private InputExceptionsMessagesDAOHibernatePCM InputExceptionsMessagesDAOHibernatePCM;

    private OpTimer opTimer;
    private InputExceptionsDataPageDTO pageDTO;
    private String dateFormat;
    private String currencyCode;
    private String messageStatus;
    private String date;
    private String seqId;

    @BeforeEach
    void setUp() {
        opTimer = new OpTimer();
        dateFormat = "dd/MM/yyyy";
        currencyCode = "EUR";
        messageStatus = "FAILED";
        seqId = "12345";

        // Set up test date
        Date testDate = new Date();
        date = new java.text.SimpleDateFormat(dateFormat).format(testDate);

        // Set up page DTO
        pageDTO = new InputExceptionsDataPageDTO();
        pageDTO.setPageNumber(1);
        pageDTO.setRecordsPerPage(10);
        pageDTO.setFromDate(date);
        pageDTO.setToDate(date);
        pageDTO.setMessageStatus(1); // Assuming 1 represents FAILED status
        pageDTO.setMessageType("MT103");
        pageDTO.setOrderBy(InputExceptionsDataPageDTO.ORDER_SEQ_NO);
        pageDTO.setOrderDesc(false);
    }

    @Test
    void testGetMessagePage() throws SwtException {
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePage(pageDTO, opTimer, dateFormat);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getCurrentPageData(), "Current page data should not be null");
    }

    @Test
    void testGetMessagePageWithNullPageDTO() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePage(null, opTimer, dateFormat);
        }, "Should throw exception for null page DTO");
    }

    @Test
    void testGetMessagePageWithNullOpTimer() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePage(pageDTO, null, dateFormat);
        }, "Should throw exception for null OpTimer");
    }

    @Test
    void testGetMessagePageWithNullDateFormat() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePage(pageDTO, opTimer, null);
        }, "Should throw exception for null date format");
    }

    @Test
    void testGetMessageTypeList() throws SwtException {
        Collection<String> result = InputExceptionsMessagesDAOHibernatePCM.getMessageTypeList(
            currencyCode, messageStatus, date, dateFormat);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMessageTypeListWithInvalidCurrency() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                "INVALID", messageStatus, date, dateFormat);
        }, "Should throw exception for invalid currency");
    }

    @Test
    void testGetMessageTypeListWithInvalidStatus() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                currencyCode, "INVALID", date, dateFormat);
        }, "Should throw exception for invalid status");
    }

    @Test
    void testGetMessageTypeListWithInvalidDate() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                currencyCode, messageStatus, "invalid-date", dateFormat);
        }, "Should throw exception for invalid date");
    }

    @Test
    void testGetArchiveMessage() {
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetArchiveMessageWithInvalidSeqId() {
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage("999999", opTimer);
        Assertions.assertNull(result, "Result should be null for invalid sequence ID");
    }

    @Test
    void testGetArchiveMessageWithNullSeqId() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getArchiveMessage(null, opTimer);
        }, "Should throw exception for null sequence ID");
    }

    @Test
    @Transactional
    void testReprocess() throws SwtException {
        String[] ids = new String[] { seqId };
        inputExceptionsMessagesDAO.reprocess(ids, opTimer, false);
        // Verify the reprocess by checking the status
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null after reprocess");
    }

    @Test
    void testReprocessWithInvalidIds() {
        String[] invalidIds = new String[] { "999999" };
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.reprocess(invalidIds, opTimer, false);
        }, "Should throw exception for invalid IDs");
    }

    @Test
    void testReprocessWithNullIds() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.reprocess(null, opTimer, false);
        }, "Should throw exception for null IDs");
    }

    @Test
    @Transactional
    void testSuppressOrReject() throws SwtException {
        String[] ids = new String[] { seqId };
        String processOption = "SUPPRESS";
        inputExceptionsMessagesDAO.suppressOrReject(ids, processOption, opTimer, false);
        // Verify the suppress/reject by checking the status
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null after suppress/reject");
    }

    @Test
    void testSuppressOrRejectWithInvalidProcessOption() {
        String[] ids = new String[] { seqId };
        String invalidOption = "INVALID";
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.suppressOrReject(ids, invalidOption, opTimer, false);
        }, "Should throw exception for invalid process option");
    }

    @Test
    void testSuppressOrRejectWithNullProcessOption() {
        String[] ids = new String[] { seqId };
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.suppressOrReject(ids, null, opTimer, false);
        }, "Should throw exception for null process option");
    }

    @Test
    void testGetMessageType() {
        InputMessageType result = inputExceptionsMessagesDAO.getMessageType("MT103", opTimer);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMessageTypeWithInvalidType() {
        InputMessageType result = inputExceptionsMessagesDAO.getMessageType("INVALID", opTimer);
        Assertions.assertNull(result, "Result should be null for invalid message type");
    }

    @Test
    void testGetMessageTypeWithNullType() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageType(null, opTimer);
        }, "Should throw exception for null message type");
    }
} 