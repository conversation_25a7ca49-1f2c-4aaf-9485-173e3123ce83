package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.CurrencyMonitorNewDAO;
import org.swallow.work.model.CurrencyRecordVO;
import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.CurrencyRecord;
import org.swallow.util.SwtUtil;

import java.math.BigDecimal;
import java.util.*;

public class CurrencyMonitorNewDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CurrencyMonitorNewDAO currencyMonitorNewDAO;

    // Test constants
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String USER_ID = "User15";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String CURRENCY_GROUP = "All";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURR";
    private static final String INVALID_USER_ID = "INVALID_USER";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";

    private OpTimer testOpTimer;
    private Date testStartDate;
    private Date testEndDate;
    private SystemFormats testFormat;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testStartDate = cal.getTime();

        cal.add(Calendar.DAY_OF_MONTH, 7); // Add 14 days for end date
        testEndDate = cal.getTime();

        // Set up OpTimer
        testOpTimer = new OpTimer();

        // Set up SystemFormats
        testFormat = new SystemFormats();
        testFormat.setDateFormatValue("dd/MM/yyyy");
        testFormat.setCurrencyFormat("currencyPat2");
    }

    @Test
    void testGetAllBalancesUsingStoredProc() throws SwtException {
        CurrencyRecordVO result = currencyMonitorNewDAO.getAllBalancesUsingStoredProc(
            ENTITY_ID, CURRENCY_GROUP, USER_ID, ROLE_ID, testStartDate, testEndDate,
            false, testFormat, testOpTimer);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Collection<CurrencyRecord> records = result.getBalanceDetails();
        Assertions.assertNotNull(records, "Currency monitor records should not be null");
    }


    @Test
    void testGetAllBalancesUsingStoredProcWithHideWeekends() throws SwtException {
        CurrencyRecordVO result = currencyMonitorNewDAO.getAllBalancesUsingStoredProc(
            ENTITY_ID, CURRENCY_GROUP, USER_ID, ROLE_ID, testStartDate, testEndDate,
            true, testFormat, testOpTimer);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Collection<CurrencyRecord> records = result.getBalanceDetails();
        
        // Verify weekends are hidden
        for (CurrencyRecord record : records) {
            Collection<BalanceDateTO> balanceDates = record.getBalanceDateRecords();
            for (BalanceDateTO balance : balanceDates) {
                Calendar cal = Calendar.getInstance();
            }
        }
    }

    @Test
    void testGetAllBalancesUsingStoredProcWithSpecificCurrencyGroup() throws SwtException {
        String specificCurrencyGroup = "EUR_GROUP";
        CurrencyRecordVO result = currencyMonitorNewDAO.getAllBalancesUsingStoredProc(
            ENTITY_ID, specificCurrencyGroup, USER_ID, ROLE_ID, testStartDate, testEndDate,
            false, testFormat, testOpTimer);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Collection<CurrencyRecord> records = result.getBalanceDetails();
        
        // Verify only currencies from the specified group are included
        for (CurrencyRecord record : records) {
            Assertions.assertTrue(record.getCurrCode().startsWith(specificCurrencyGroup),
                "All records should belong to the specified currency group");
        }
    }



    @Test
    void testGetAllBalancesUsingStoredProcResultStructure() throws SwtException {
        CurrencyRecordVO result = currencyMonitorNewDAO.getAllBalancesUsingStoredProc(
            ENTITY_ID, CURRENCY_GROUP, USER_ID, ROLE_ID, testStartDate, testEndDate,
            false, testFormat, testOpTimer);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Collection<CurrencyRecord> records = result.getBalanceDetails();
        Assertions.assertNotNull(records, "Currency monitor records should not be null");
        
        if (!records.isEmpty()) {
            CurrencyRecord firstRecord = records.iterator().next();
            Collection<BalanceDateTO> balanceDates = firstRecord.getBalanceDateRecords();
            Assertions.assertNotNull(balanceDates, "Balance dates should not be null");
            Assertions.assertNotNull(firstRecord.getCurrCode(), "Currency code should not be null");
            Assertions.assertNotNull(firstRecord.getLoroBalanceAsString(), "Loro balance should not be null");
        }
    }

    @Test
    void testGetAllBalancesUsingStoredProcPerformance() throws SwtException {
        long startTime = System.currentTimeMillis();
        
        currencyMonitorNewDAO.getAllBalancesUsingStoredProc(
            ENTITY_ID, CURRENCY_GROUP, USER_ID, ROLE_ID, testStartDate, testEndDate,
            false, testFormat, testOpTimer);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Assuming reasonable performance should be under 5 seconds
        Assertions.assertTrue(duration < 5000,
            "Currency balances retrieval should complete within 5 seconds");
    }
} 