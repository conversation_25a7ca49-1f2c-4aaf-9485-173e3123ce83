package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.PreAdviceInputDAO;
import org.swallow.work.model.InputAuthoriseSelection;
import org.swallow.work.model.Movement;
import org.swallow.maintenance.model.Party;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class PreAdviceInputDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private PreAdviceInputDAO preAdviceInputDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";
    private static final String CURRENCY_GROUP = "ALL";
    private static final String SEARCH_DATE = "01/10/2008";
    private static final String DATE_FORMAT = "dd/MM/yyyy";
    private static final String CURRENCY_CODE = "EUR";
    private static final String INPUT_SOURCE = "PRE-ADVICE";
    private static final String MATCH_STATUS = "A";
    private static final String DATE_FLAG = "01/10/2008";
    private static final String ACCOUNT_ID = "390880744EUR";

    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();
    }

    @Test
    void testGetInputAuthoriseList() throws SwtException {
        Collection<InputAuthoriseSelection> result = preAdviceInputDAO.getInputAuthoriseList(
            HOST_ID, ENTITY_ID, "ALL", "User15", ROLE_ID, SEARCH_DATE, DATE_FORMAT);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMovementWithMatchStatus() throws SwtException {
        Collection<Movement> result = preAdviceInputDAO.getMovementWithMatchStatus(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, INPUT_SOURCE, MATCH_STATUS, DATE_FLAG, USER_ID, ROLE_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMovement() throws SwtException {
        Long movementId = 2080564L; // Example movement ID
        Movement result = preAdviceInputDAO.getMovement(HOST_ID, movementId);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(HOST_ID, result.getId().getHostId(), "Host ID should match");
        Assertions.assertEquals(movementId, result.getId().getMovementId(), "Movement ID should match");
    }

    @Transactional
    @Test
    void testDeleteMovement() throws SwtException {
        Movement movement = new Movement();
        movement.getId().setHostId(HOST_ID);
        movement.getId().setEntityId(ENTITY_ID);
        movement.getId().setMovementId(98777788445L);
        movement.setCurrencyCode(CURRENCY_CODE);
        movement.setAccountId(ACCOUNT_ID);
        movement.setAmount(5005555.00);
        movement.setSign("D");

        String listMovementId = "98777788445L"; // Example movement
        List<Movement> movements = new ArrayList<>();
        movements.add(movement);
        preAdviceInputDAO.saveAll(movements);


        String result = preAdviceInputDAO.deleteMovement(HOST_ID, ENTITY_ID, listMovementId);

        Assertions.assertNotNull(result, "Result should not be null");
    }
    @Test
    void testGetLast24PreAdviceInput() throws SwtException {
        Collection<Movement> result = preAdviceInputDAO.getLast24PreAdviceInput(
            HOST_ID, USER_ID, ENTITY_ID, "A");

        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testSaveAll() throws SwtException {
        List<Movement> movements = new ArrayList<>();
        Movement movement = new Movement();
        movement.getId().setHostId(HOST_ID);
        movement.getId().setEntityId(ENTITY_ID);
        movement.getId().setMovementId(2080564L);
        movement.setCurrencyCode(CURRENCY_CODE);
        movement.setAccountId(ACCOUNT_ID);
        movement.setAmount(5005555.00);
        movement.setSign("D");
        movements.add(movement);

        preAdviceInputDAO.saveAll(movements);
        // No assertion needed as this is a void method
    }

    @Test
    void testGetMvtFromAccountTab() throws SwtException {
        String result = preAdviceInputDAO.getMvtFromAccountTab(ACCOUNT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testCheckIfEntityExists() throws SwtException {
        boolean result = preAdviceInputDAO.checkIfEntityExists(HOST_ID, ENTITY_ID);
        Assertions.assertTrue(result, "Entity should exist");
    }

    @Test
    void testGetAccountStatus() throws SwtException {
        String result = preAdviceInputDAO.getAccountStatus(ACCOUNT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testCheckAccountPreAdviceAccess() throws SwtException {
        String result = preAdviceInputDAO.checkAccountPreAdviceAccess(ACCOUNT_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPartySearchResult() throws SwtException {
        ArrayList<Party> result = preAdviceInputDAO.getPartySearchResult(
            "PARTY1", "Test Party", ENTITY_ID, 10, 1, "partyId|asc");

        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetTotalCount() throws SwtException {
        int result = preAdviceInputDAO.getTotalCount("Test Party", "PARTY1", ENTITY_ID);
        Assertions.assertTrue(result >= 0, "Total count should be non-negative");
    }

    // Tests with invalid data
    @Test
    void testGetInputAuthoriseListWithInvalidData() throws SwtException {
        Collection<InputAuthoriseSelection> result = preAdviceInputDAO.getInputAuthoriseList(
            "INVALID_HOST", "INVALID_ENTITY", CURRENCY_GROUP, USER_ID, ROLE_ID, SEARCH_DATE, DATE_FORMAT);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovementWithMatchStatusWithInvalidData() throws SwtException {
        Collection<Movement> result = preAdviceInputDAO.getMovementWithMatchStatus(
            "INVALID_HOST", "INVALID_ENTITY", "INVALID_CURRENCY", INPUT_SOURCE, MATCH_STATUS, DATE_FLAG, USER_ID, ROLE_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovementWithInvalidData() throws SwtException {
        Movement result = preAdviceInputDAO.getMovement("INVALID_HOST", 999999L);
        Assertions.assertNull(result, "Result should be null for invalid data");
    }


    @Test
    void testGetLast24PreAdviceInputWithInvalidData() throws SwtException {
        Collection<Movement> result = preAdviceInputDAO.getLast24PreAdviceInput(
            "INVALID_HOST", "INVALID_USER", "INVALID_ENTITY", "A");

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMvtFromAccountTabWithInvalidData() throws SwtException {
        String result = preAdviceInputDAO.getMvtFromAccountTab("INVALID_ACCOUNT");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid account");
    }

    @Test
    void testCheckIfEntityExistsWithInvalidData() throws SwtException {
        boolean result = preAdviceInputDAO.checkIfEntityExists("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertFalse(result, "Entity should not exist");
    }

    @Test
    void testGetAccountStatusWithInvalidData() throws SwtException {
        String result = preAdviceInputDAO.getAccountStatus("INVALID_ACCOUNT");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid account");
    }

    @Test
    void testCheckAccountPreAdviceAccessWithInvalidData() throws SwtException {
        String result = preAdviceInputDAO.checkAccountPreAdviceAccess("INVALID_ACCOUNT", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetPartySearchResultWithInvalidData() throws SwtException {
        ArrayList<Party> result = preAdviceInputDAO.getPartySearchResult(
            "INVALID_PARTY", "INVALID_NAME", "INVALID_ENTITY", 10, 1, "partyId|asc");

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetTotalCountWithInvalidData() throws SwtException {
        int result = preAdviceInputDAO.getTotalCount("INVALID_NAME", "INVALID_PARTY", "INVALID_ENTITY");
        Assertions.assertEquals(0, result, "Total count should be 0 for invalid data");
    }
} 