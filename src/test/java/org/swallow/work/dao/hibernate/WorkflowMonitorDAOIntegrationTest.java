package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.WorkflowMonitorDAO;
import org.swallow.work.model.OracleTimeDTO;
import org.swallow.exception.SwtException;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

public class WorkflowMonitorDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private WorkflowMonitorDAO workflowMonitor;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_GRP_ID = "ALL";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String USER_ID = "User15";
    private static final String APPLY_CURRENCY_THRESHOLD = "Y";

    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();
    }

    @Test
    void testGetWorkflowMonitorDetailsFromProc() throws Exception {
        HashMap<String, Object> result = workflowMonitor.getWorkflowMonitorDetailsFromProc(
                HOST_ID, ENTITY_ID, CURRENCY_GRP_ID, ROLE_ID, testDate,
                APPLY_CURRENCY_THRESHOLD, USER_ID);

        // Verify the result contains expected keys
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.containsKey("EXCLUDEDMOVEMENTS"), "Should contain EXCLUDEDMOVEMENTS");
        Assertions.assertTrue(result.containsKey("POSITIONLEVELDEAIL"), "Should contain POSITIONLEVELDEAIL");
        Assertions.assertTrue(result.containsKey("INCLUDEDMOVEMENTSTODAY"), "Should contain INCLUDEDMOVEMENTSTODAY");
        Assertions.assertTrue(result.containsKey("TABFLAG"), "Should contain TABFLAG");

        // Verify the data types of the values
        Assertions.assertTrue(result.get("EXCLUDEDMOVEMENTS") instanceof int[], "EXCLUDEDMOVEMENTS should be int[]");
        Assertions.assertTrue(result.get("POSITIONLEVELDEAIL") instanceof String[], "POSITIONLEVELDEAIL should be String[]");
        Assertions.assertTrue(result.get("INCLUDEDMOVEMENTSTODAY") instanceof int[], "INCLUDEDMOVEMENTSTODAY should be int[]");
        Assertions.assertTrue(result.get("TABFLAG") instanceof String, "TABFLAG should be String");
    }

    @Test
    void testGetAllEntityOption() throws Exception {
        boolean result = workflowMonitor.getAllEntityOption(ROLE_ID);
        // Since we can't predict the exact value, we just verify it's a boolean
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetOracleSystemTime() throws Exception {
        OracleTimeDTO result = workflowMonitor.getOracleSystemTime();

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getTimeISO8601(), "TimeISO8601 should not be null");
        Assertions.assertNotNull(result.getTimeZoneOffset(), "TimeZoneOffset should not be null");

        // Verify the time format
        Assertions.assertTrue(result.getTimeISO8601().matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}"),
                "Time should be in ISO8601 format");
    }

    @Test
    void testGetEntityOffset() throws Exception {
        String result = workflowMonitor.getEntityOffset(ENTITY_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        // The offset should be in the format "+HH:MM" or "-HH:MM"
        Assertions.assertTrue(result.matches("[+-]\\d{2}:\\d{2}"),
                "Offset should be in the format +/-HH:MM");
    }

    @Test
    void testGetEntityOffsetWithInvalidEntity() throws Exception {
        String result = workflowMonitor.getEntityOffset("INVALID_ENTITY");
        
        // Verify that we get a valid response even with invalid entity
        Assertions.assertNotNull(result, "Result should not be null even with invalid entity");
        // The offset should still be in the correct format
        Assertions.assertTrue(result.isEmpty());
    }
} 