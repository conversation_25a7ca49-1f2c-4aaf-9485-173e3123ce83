package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.SweepPriorCutOffDAO;
import org.swallow.work.model.SweepPriorCutOff;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.maintenance.model.AcctMaintenance;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class SweepPriorCutOffDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepPriorCutOffDAO sweepPriorCutOffDAO;

    // Constants for test data based on actual record
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String MAIN_ACCOUNT_ID = "390880531EUR";
    private static final String ACCOUNT_NAME = "Test Account";
    private static final String CUT_OFF = "15:00";
    private static final Double TARGET_BALANCE = 1000.0;
    private static final String MANUAL_SWEEP_FLAG = "N";
    private static final int LEAD_TIME = 0;
    private static final int EXTEND_DISPLAY_TIME_BY = 999;
    private static final String DATE_FORMAT =  "dd/MM/yyyy";
    private static final String CURRENCY_FORMAT = "currencyPat2";
    private Date testDate;
    private SystemFormats systemFormats;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();




        // Initialize system formats
        systemFormats = new SystemFormats();
        systemFormats.setDateFormatValue(DATE_FORMAT);
        systemFormats.setCurrencyFormat(CURRENCY_FORMAT);
    }

    @Test
    void testGetSweepPriorToCutOffListUsingStoredProc() throws SwtException {
        Collection<SweepPriorCutOff> result = sweepPriorCutOffDAO.getSweepPriorToCutOffListUsingStoredProc(
            HOST_ID, 
            ENTITY_ID, 
            "N",
            LEAD_TIME, 
            EXTEND_DISPLAY_TIME_BY, 
            systemFormats
        );

        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

    }

    @Test
    void testGetMainAccountDetails() throws SwtException {
        Collection<AcctMaintenance> result = sweepPriorCutOffDAO.getMainAccountDetails(
            HOST_ID, 
            ENTITY_ID, 
            MAIN_ACCOUNT_ID
        );

        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        AcctMaintenance account = (AcctMaintenance) result.iterator().next();
        Assertions.assertEquals(HOST_ID, account.getId().getHostId());
        Assertions.assertEquals(ENTITY_ID, account.getId().getEntityId());
        Assertions.assertEquals(MAIN_ACCOUNT_ID, account.getId().getAccountId());
    }

    @Test
    void testGetSweepPriorToCutOffListWithInvalidData() throws SwtException {
        Collection<SweepPriorCutOff> result = sweepPriorCutOffDAO.getSweepPriorToCutOffListUsingStoredProc(
            "INVALID_HOST", 
            "INVALID_ENTITY", 
            "INVALID_ACCOUNT", 
            LEAD_TIME, 
            EXTEND_DISPLAY_TIME_BY, 
            systemFormats
        );

        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetMainAccountDetailsWithInvalidData() throws SwtException {
        Collection<AcctMaintenance> result = sweepPriorCutOffDAO.getMainAccountDetails(
            "INVALID_HOST", 
            "INVALID_ENTITY", 
            "INVALID_ACCOUNT"
        );

        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }
} 