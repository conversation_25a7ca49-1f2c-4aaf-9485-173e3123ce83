package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.MovementSearchDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.model.Party;
import org.swallow.util.LabelValueBean;
import org.swallow.exception.SwtException;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class MovementSearchDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private MovementSearchDAO movementSearchDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ACCOUNT_CLASS = "A";
    private static final String BENEFICIARY_ID = "ABCEGB2L";
    private static final String CUSTODIAN_ID = "";
    private static final String BOOK_CODE = "";
    private static final String GROUP_ID = "";
    private static final String META_GROUP_ID = "";
    private static final String COUNTERPARTY_ID = "CP1";

    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();
    }

    @Test
    void testGetCurrencyDetails() throws SwtException {
        Collection<Currency> result = movementSearchDAO.getCurrencyDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetBeneficiaryDetails() throws SwtException {
        Collection<Party> result = movementSearchDAO.getBeneficiaryDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetCustodianDetails() throws SwtException {
        Collection<Party> result = movementSearchDAO.getCustodianDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetBookCodeDetails() throws SwtException {
        Collection<BookCode> result = movementSearchDAO.getBookCodeDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetGroupDetails() throws SwtException {
        Collection<Group> result = movementSearchDAO.getGroupDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMetaGroupDetails() throws SwtException {
        Collection<MetaGroup> result = movementSearchDAO.getMetaGroupDetails(HOST_ID, "RABOJPJT");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetAccountDetails() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetAccountDetailsWithCurrency() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountDetails(HOST_ID, ENTITY_ID, CURRENCY_CODE);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetAccountClassDetails() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountClassDetails(
            HOST_ID, ENTITY_ID, ACCOUNT_CLASS, CURRENCY_CODE);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetCounterPartyDetails() throws SwtException {
        Collection<Party> result = movementSearchDAO.getCounterPartyDetails(HOST_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testFetchDetails() throws SwtException {
        List<Object> listToPopulate = new ArrayList<>();
        int result = movementSearchDAO.fetchDetails(
            listToPopulate, HOST_ID, ENTITY_ID, "D", "C", "C", "E",
            Double.valueOf(0.0), Double.valueOf(************.0), CURRENCY_CODE, BENEFICIARY_ID, CUSTODIAN_ID, Integer.valueOf(1),
            ACCOUNT_ID, GROUP_ID, META_GROUP_ID, BOOK_CODE, "movementId|asc",
            "01/10/2008", "31/10/2008", "", "", "", "", "", "",
            "00:10", "", 1, 10, "all,1|asc", 0);

        Assertions.assertTrue(result >= 0, "Result should be non-negative");
        Assertions.assertFalse(listToPopulate.isEmpty(), "List should not be empty");
    }

    @Test
    void testGetArchiveList() throws SwtException {
        List result = movementSearchDAO.getArchiveList();
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPositionLevelList() throws SwtException {
        ArrayList<LabelValueBean> result = movementSearchDAO.getPositionLevelList(HOST_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    // Tests with invalid data
    @Test
    void testGetCurrencyDetailsWithInvalidData() throws SwtException {
        Collection<Currency> result = movementSearchDAO.getCurrencyDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetBeneficiaryDetailsWithInvalidData() throws SwtException {
        Collection<Party> result = movementSearchDAO.getBeneficiaryDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetCustodianDetailsWithInvalidData() throws SwtException {
        Collection<Party> result = movementSearchDAO.getCustodianDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetBookCodeDetailsWithInvalidData() throws SwtException {
        Collection<BookCode> result = movementSearchDAO.getBookCodeDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetGroupDetailsWithInvalidData() throws SwtException {
        Collection<Group> result = movementSearchDAO.getGroupDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMetaGroupDetailsWithInvalidData() throws SwtException {
        Collection<MetaGroup> result = movementSearchDAO.getMetaGroupDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetAccountDetailsWithInvalidData() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetAccountDetailsWithCurrencyWithInvalidData() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountDetails(
            "INVALID_HOST", "INVALID_ENTITY", "INVALID_CURRENCY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetAccountClassDetailsWithInvalidData() throws SwtException {
        Collection<AcctMaintenance> result = movementSearchDAO.getAccountClassDetails(
            "INVALID_HOST", "INVALID_ENTITY", "INVALID_CLASS", "INVALID_CURRENCY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetCounterPartyDetailsWithInvalidData() throws SwtException {
        Collection<Party> result = movementSearchDAO.getCounterPartyDetails("INVALID_HOST", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testFetchDetailsWithInvalidData() throws SwtException {
        List<Object> listToPopulate = new ArrayList<>();
        int result = movementSearchDAO.fetchDetails(
            listToPopulate, "INVALID_HOST", "INVALID_ENTITY", "N", "B", "B", "A",
            Double.valueOf(0.0), Double.valueOf(0.0), "INVALID_CURRENCY", "INVALID_BENEFICIARY", "INVALID_CUSTODIAN", Integer.valueOf(1),
            "INVALID_ACCOUNT", "INVALID_GROUP", "INVALID_META", "INVALID_BOOK", "movementId|asc",
            "01/10/2008", "31/10/2008", "", "", "", "", "", "",
            "01:00", "", 1, 10, "all,1|asc", 0);

        Assertions.assertTrue(result >= 0, "Result should be non-negative");
        Assertions.assertTrue(listToPopulate.isEmpty(), "List should be empty for invalid data");
    }

    @Test
    void testGetPositionLevelListWithInvalidData() throws SwtException {
        ArrayList<LabelValueBean> result = movementSearchDAO.getPositionLevelList("INVALID_HOST");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }
} 