package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.work.dao.CorporateAccountDAO;
import org.swallow.work.model.CorporateAccount;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class CorporateAccountDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private CorporateAccountDAO corporateAccountDAO;

    // Test constants
    private static final String ENTITY_ID = "RABONL2U";
    private static final String UPDATE_USER = "User15";
    private static final String CORPORATE_NAME = "Test Corporate";
    private static final BigDecimal AMOUNT = new BigDecimal("1000.00");

    // Invalid test data
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CORPORATE_NAME = "INVALID_CORPORATE";
    private static final BigDecimal INVALID_AMOUNT = new BigDecimal("-1000.00");

    private CorporateAccount testCorporateAccount;
    private Date testDate;

    @BeforeEach
    void setUp() {
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test corporate account
        testCorporateAccount = new CorporateAccount();
        testCorporateAccount.setEntityId(ENTITY_ID);
        testCorporateAccount.setValueDate(testDate);
        testCorporateAccount.setCorporateName(CORPORATE_NAME);
        testCorporateAccount.setAmount(AMOUNT);
        testCorporateAccount.setUpdateDate(new Date());
        testCorporateAccount.setUpdateUser(UPDATE_USER);
    }

    @Test
    void testGetCorporateAccount() throws SwtException {
        List<CorporateAccount> result = corporateAccountDAO.getCorporateAccount(testCorporateAccount);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetCorporateAccountWithInvalidData() throws SwtException {
        CorporateAccount invalidAccount = new CorporateAccount();
        invalidAccount.setEntityId(INVALID_ENTITY_ID);
        invalidAccount.setValueDate(testDate);
        invalidAccount.setCorporateName(INVALID_CORPORATE_NAME);

        List<CorporateAccount> result = corporateAccountDAO.getCorporateAccount(invalidAccount);
        Assertions.assertNotNull(result, "Result should not be null even with invalid data");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Transactional
    @Test
    void testSaveCorporateAccount() throws SwtException {
        // Test saving new record
        corporateAccountDAO.saveCorporateAccount(testCorporateAccount, "true");

        // Verify save
        List<CorporateAccount> result = corporateAccountDAO.getCorporateAccount(testCorporateAccount);
        Assertions.assertFalse(result.isEmpty(), "Saved record should be retrievable");

// Find the one that matches CORPORATE_NAME
        CorporateAccount saved = result.stream()
                .filter(ca -> CORPORATE_NAME.equals(ca.getCorporateName()))
                .findFirst()
                .orElseThrow(() -> new AssertionError("No record found with corporate name: " + CORPORATE_NAME));

// Now verify the fields
        Assertions.assertEquals(ENTITY_ID, saved.getEntityId(), "Entity ID should match");
        Assertions.assertEquals(CORPORATE_NAME, saved.getCorporateName(), "Corporate name should match");
    }

    @Test
    @Transactional
    void testUpdateCorporateAccount() throws SwtException {
        // First save
        corporateAccountDAO.saveCorporateAccount(testCorporateAccount, "true");

        // Update
        BigDecimal newAmount = new BigDecimal("2000.00");
        testCorporateAccount.setAmount(newAmount);
        corporateAccountDAO.saveCorporateAccount(testCorporateAccount, "false");

        // Verify update
        List<CorporateAccount> result = corporateAccountDAO.getCorporateAccount(testCorporateAccount);
        Assertions.assertFalse(result.isEmpty(), "Updated record should be retrievable");
        
        CorporateAccount updated = result.get(0);
        Assertions.assertEquals(0, newAmount.compareTo(updated.getAmount()), "Amount should be updated");
    }

    @Test
    @Transactional
    void testDeleteCorporateAccount() throws SwtException {
        // First save
        corporateAccountDAO.saveCorporateAccount(testCorporateAccount, "true");

        // Delete
        corporateAccountDAO.deleteCorporateAccount(testCorporateAccount);

        // Verify deletion
        List<CorporateAccount> result = corporateAccountDAO.getCorporateAccount(testCorporateAccount);
        Assertions.assertTrue(result.isEmpty(), "Record should be deleted");
    }

    @Test
    void testSaveCorporateAccountWithInvalidData() {
        CorporateAccount invalidAccount = new CorporateAccount();
        invalidAccount.setEntityId(INVALID_ENTITY_ID);
        invalidAccount.setValueDate(testDate);
        invalidAccount.setCorporateName(INVALID_CORPORATE_NAME);
        invalidAccount.setAmount(INVALID_AMOUNT);

        Assertions.assertThrows(SwtException.class, () -> {
            corporateAccountDAO.saveCorporateAccount(invalidAccount, "true");
        }, "Should throw exception for invalid data");
    }

    @Test
    void testDeleteCorporateAccountWithInvalidData() {
        CorporateAccount invalidAccount = new CorporateAccount();
        invalidAccount.setEntityId(INVALID_ENTITY_ID);
        invalidAccount.setValueDate(testDate);
        invalidAccount.setCorporateName(INVALID_CORPORATE_NAME);

        Assertions.assertThrows(SwtException.class, () -> {
            corporateAccountDAO.deleteCorporateAccount(invalidAccount);
        }, "Should throw exception for invalid data");
    }
} 