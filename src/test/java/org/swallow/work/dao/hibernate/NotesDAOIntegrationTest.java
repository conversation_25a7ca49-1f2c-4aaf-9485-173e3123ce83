package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.NotesDAO;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.SweepNote;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.util.SwtUtil;

import java.util.*;

public class NotesDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private NotesDAO notesDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String MATCH_ID = "1000149";
    private static final String MOVEMENT_ID = "1032973";
    private static final String SWEEP_ID = "12345";
    private static final String USER_ID = "User15";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_MATCH_ID = "INVALID_MATCH";
    private static final String INVALID_MOVEMENT_ID = "INVALID_MOVEMENT";
    private static final String INVALID_SWEEP_ID = "INVALID_SWEEP";

    private Date testDate;
    private MovementNote testMovementNote;
    private MatchNote testMatchNote;
    private SweepNote testSweepNote;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test objects
        testMovementNote = new MovementNote();
        testMovementNote.setId(new MovementNote.Id(HOST_ID, ENTITY_ID, Long.valueOf(MOVEMENT_ID), testDate, null, null));
        testMovementNote.setNoteText("Test movement note");
        testMovementNote.setUpdateUser(USER_ID);

        testMatchNote = new MatchNote();
        testMatchNote.setId(new MatchNote.Id(HOST_ID, ENTITY_ID, Long.valueOf(MATCH_ID), testDate, null, null));
        testMatchNote.setNoteText("Test match note");
        testMatchNote.setUpdateUser(USER_ID);

        testSweepNote = new SweepNote();
        testSweepNote.setId(new SweepNote.Id(HOST_ID, ENTITY_ID, Long.valueOf(SWEEP_ID), testDate, null, null));
        testSweepNote.setNoteText("Test sweep note");
        testSweepNote.setUpdateUser(USER_ID);
    }

    @AfterEach
    void tearDown() {
        // Clean up test data
        try {
            notesDAO.deleteNote(testMovementNote);
        } catch (Exception e) {
            System.err.println("Failed to clean up movement note: " + testMovementNote.getId());
        }

        try {
            notesDAO.deleteMatchNote(testMatchNote);
        } catch (Exception e) {
            System.err.println("Failed to clean up match note: " + testMatchNote.getId());
        }

        try {
            notesDAO.deleteSweepNote(testSweepNote);
        } catch (Exception e) {
            System.err.println("Failed to clean up sweep note: " + testSweepNote.getId());
        }
    }

    @Test
    void testGetNoteDetails() throws SwtException {
        Collection<MovementNote> result = notesDAO.getNoteDetails(HOST_ID, Long.valueOf(MOVEMENT_ID));
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetNoteDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            notesDAO.getNoteDetails(INVALID_HOST_ID, null);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testSaveNotesDetails() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.saveNotesDetails(testMovementNote);
        }, "Should not throw exception when saving valid movement note");
    }

    @Test
    void testSaveNotesDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            MovementNote invalidNote = new MovementNote();
            invalidNote.setId(new MovementNote.Id(null, null, null, null, null, null));
            notesDAO.saveNotesDetails(invalidNote);
        }, "Should throw SwtException for invalid movement note data");
    }

    @Test
    void testDeleteNote() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.deleteNote(testMovementNote);
        }, "Should not throw exception when deleting valid movement note");
    }

    @Test
    void testDeleteNoteWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            MovementNote invalidNote = new MovementNote();
            invalidNote.setId(new MovementNote.Id(null, null, null, null, null, null));
            notesDAO.deleteNote(invalidNote);
        }, "Should throw SwtException for invalid movement note data");
    }

    @Test
    void testGetMatchNoteDetails() throws SwtException {
        Collection<MatchNote> result = notesDAO.getMatchNoteDetails(HOST_ID, Long.valueOf(MATCH_ID));
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMatchNoteDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            notesDAO.getMatchNoteDetails(INVALID_HOST_ID, null);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testSaveMatchNotesDetails() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.saveMatchNotesDetails(testMatchNote);
        }, "Should not throw exception when saving valid match note");
    }

    @Test
    void testSaveMatchNotesDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            MatchNote invalidNote = new MatchNote();
            invalidNote.setId(new MatchNote.Id(null, null, null, null, null, null));
            notesDAO.saveMatchNotesDetails(invalidNote);
        }, "Should throw SwtException for invalid match note data");
    }

    @Test
    void testDeleteMatchNote() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.deleteMatchNote(testMatchNote);
        }, "Should not throw exception when deleting valid match note");
    }

    @Test
    void testDeleteMatchNoteWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            MatchNote invalidNote = new MatchNote();
            invalidNote.setId(new MatchNote.Id(null, null, null, null, null, null));
            notesDAO.deleteMatchNote(invalidNote);
        }, "Should throw SwtException for invalid match note data");
    }

    @Test
    void testGetSweepNoteDetails() throws SwtException {
        Collection<SweepNote> result = notesDAO.getSweepNoteDetails(HOST_ID, Long.valueOf(SWEEP_ID), null);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetSweepNoteDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            notesDAO.getSweepNoteDetails(INVALID_HOST_ID, null, null);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testSaveSweepNotesDetails() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.saveSweepNotesDetails(testSweepNote);
        }, "Should not throw exception when saving valid sweep note");
    }

    @Test
    void testSaveSweepNotesDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            SweepNote invalidNote = new SweepNote();
            invalidNote.setId(new SweepNote.Id(null, null, null, null, null, null));
            notesDAO.saveSweepNotesDetails(invalidNote);
        }, "Should throw SwtException for invalid sweep note data");
    }

    @Test
    void testDeleteSweepNote() throws SwtException {
        Assertions.assertDoesNotThrow(() -> {
            notesDAO.deleteSweepNote(testSweepNote);
        }, "Should not throw exception when deleting valid sweep note");
    }

    @Test
    void testDeleteSweepNoteWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            SweepNote invalidNote = new SweepNote();
            invalidNote.setId(new SweepNote.Id(null, null, null, null, null, null));
            notesDAO.deleteSweepNote(invalidNote);
        }, "Should throw SwtException for invalid sweep note data");
    }

    @Test
    void testGetMatchDetails() throws SwtException {
        boolean result = notesDAO.getMatchDetails(HOST_ID, ENTITY_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMatchDetailsWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            notesDAO.getMatchDetails(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MATCH_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetCurrency() throws SwtException {
        String result = notesDAO.getCurrency(ENTITY_ID, Long.valueOf(MOVEMENT_ID), Long.valueOf(MATCH_ID));
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetCurrencyWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            notesDAO.getCurrency(INVALID_ENTITY_ID, null, null);
        }, "Should throw SwtException for invalid data");
    }
} 