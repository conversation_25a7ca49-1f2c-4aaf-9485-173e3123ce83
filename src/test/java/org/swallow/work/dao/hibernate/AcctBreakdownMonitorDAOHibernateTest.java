package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.SwtConstants;
import org.swallow.work.dao.AcctBreakdownMonitorDAO;
import org.swallow.work.model.AcctBreakdownModel;
import org.swallow.work.model.AcctBreakdownMonitor;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

public class AcctBreakdownMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AcctBreakdownMonitorDAO acctBreakdownMonitorDAO;

    // Test constants
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String BALANCE_TYPE = "PREDICTED";
    private static final String ACCT_CLASS = "C";
    private static final String SUM_FLAG = "Y";
    private static final String LORO_TO_PREDICTED = "Y";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURR";
    private static final String INVALID_ACCOUNT_ID = "INVALID_ACCOUNT";

    private AcctBreakdownMonitor testMonitor;
    private Date testDate;

    @BeforeEach
    void setUp() {
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test monitor
        testMonitor = new AcctBreakdownMonitor();
        testMonitor.setHostId(HOST_ID);
        testMonitor.setEntityId(ENTITY_ID);
        testMonitor.setCurrencyCode(CURRENCY_CODE);
        testMonitor.setAcctId(ACCOUNT_ID);
        testMonitor.setBalanceType(BALANCE_TYPE);
        testMonitor.setAcctClass(ACCT_CLASS);
        testMonitor.setValueDate(testDate);
        testMonitor.setRoleId(ROLE_ID);
        testMonitor.setApplyCurrencyThreshold("N");
        testMonitor.setHideZeroBalances("N");
    }

    @Test
    void testGetAcctList() throws SwtException {
        Collection<AcctMaintenance> result = acctBreakdownMonitorDAO.getAcctList(testMonitor);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");

        // Verify account status is either open or blocked
        for (AcctMaintenance acct : result) {
            Assertions.assertTrue(
                acct.getAcctstatusflg().equals(SwtConstants.ACCOUNT_STATUS_FLAG_OPEN) ||
                acct.getAcctstatusflg().equals(SwtConstants.ACCOUNT_STATUS_FLAG_BLOCKED),
                "Account status should be either open or blocked"
            );
        }
    }

    @Test
    void testGetAcctListWithInvalidData() throws SwtException {
        testMonitor.setHostId(INVALID_HOST_ID);
        testMonitor.setEntityId(INVALID_ENTITY_ID);
        testMonitor.setCurrencyCode(INVALID_CURRENCY_CODE);

        Collection<AcctMaintenance> result = acctBreakdownMonitorDAO.getAcctList(testMonitor);
        Assertions.assertNotNull(result, "Result should not be null even with invalid data");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetAcctListWithAllAccountClass() throws SwtException {
        testMonitor.setAcctClass("All");
        Collection<AcctMaintenance> result = acctBreakdownMonitorDAO.getAcctList(testMonitor);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetAllBalancesUsingStoredProc() throws SwtException {
        acctBreakdownMonitorDAO.getAllBalancesUsingStoredProc(testMonitor);

        // Verify the results were set in the monitor object
        Assertions.assertNotNull(testMonitor.getAcctBreakdownList(), "Account breakdown list should not be null");
        Assertions.assertNotNull(testMonitor.getTabFlag(), "Tab flag should not be null");
        Assertions.assertNotNull(testMonitor.getPredBalanceTotal(), "Predicted balance total should not be null");
        Assertions.assertNotNull(testMonitor.getStartingBalanceTotal(), "Starting balance total should not be null");
        Assertions.assertNotNull(testMonitor.getUnexpectedBalanceTotal(), "Unexpected balance total should not be null");

        // Verify balance signs
        Assertions.assertTrue(
            testMonitor.getPredBalanceTotalSign().equals(SwtConstants.STR_TRUE) ||
            testMonitor.getPredBalanceTotalSign().equals(SwtConstants.STR_FALSE),
            "Predicted balance sign should be either true or false"
        );
    }


    @Test
    @Transactional
    void testUpdateAccount() throws SwtException {
        acctBreakdownMonitorDAO.updateAccount(HOST_ID, ENTITY_ID, ACCOUNT_ID, SUM_FLAG, LORO_TO_PREDICTED);

        // Verify the update by retrieving the account
        Collection<AcctMaintenance> accounts = acctBreakdownMonitorDAO.getAcctList(testMonitor);
        boolean found = false;
        for (AcctMaintenance acct : accounts) {
            if (acct.getId().getAccountId().equals(ACCOUNT_ID)) {
                Assertions.assertEquals(SUM_FLAG, acct.getAcctMonitorSum(), "Sum flag should be updated");
                found = true;
                break;
            }
        }
        Assertions.assertTrue(found, "Updated account should be found");
    }

    @Test
    void testUpdateAccountWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            acctBreakdownMonitorDAO.updateAccount(
                INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_ACCOUNT_ID, SUM_FLAG, LORO_TO_PREDICTED);
        }, "Should throw exception for invalid data");
    }
} 