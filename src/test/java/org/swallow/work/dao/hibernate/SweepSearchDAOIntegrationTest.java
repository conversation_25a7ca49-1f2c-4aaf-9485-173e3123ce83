package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.SweepSearchDAO;
import org.swallow.work.model.Movement;
import org.swallow.work.model.Sweep;
import org.swallow.maintenance.model.Currency;
import org.swallow.exception.SwtException;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

public class SweepSearchDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepSearchDAO sweepSearchDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final Long TEST_SWEEP_ID = 10211L;
    private static final Long TEST_MOVEMENT_ID = 2080564L;
    private static final String TEST_ACCOUNT_ID_CR = "390880744EUR";
    private static final String TEST_ACCOUNT_ID_DR = "390880531EUR";
    private static final String TEST_ACCOUNT_SEARCH = "101066619EUR";
    private static final String TEST_ENTITY_ID_CR = "RABONL2U";
    private static final String TEST_ENTITY_ID_DR = "RABONL2U";

    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();
    }

    @Test
    void testGetCurrencyDetails() throws Exception {
        Collection<Currency> result = sweepSearchDAO.getCurrencyDetails(HOST_ID, ENTITY_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Should return currency details");
        
        // Verify each currency has required fields
        for (Currency currency : result) {
            Assertions.assertNotNull(currency.getId(), "Currency ID should not be null");
            Assertions.assertNotNull(currency.getId().getHostId(), "Host ID should not be null");
            Assertions.assertNotNull(currency.getId().getEntityId(), "Entity ID should not be null");
            Assertions.assertNotNull(currency.getId().getCurrencyCode(), "Currency code should not be null");
            Assertions.assertNotEquals("*", currency.getId().getCurrencyCode(), "Currency code should not be *");
        }
    }

    @Test
    void testGetSweepDetails() throws Exception {
        Collection<Sweep> result = sweepSearchDAO.getSweepDetails(HOST_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Should return sweep details");
        
        // Verify each sweep has required fields
        for (Sweep sweep : result) {
            Assertions.assertNotNull(sweep.getId(), "Sweep ID should not be null");
            Assertions.assertNotNull(sweep.getId().getHostId(), "Host ID should not be null");
            Assertions.assertNotNull(sweep.getId().getSweepId(), "Sweep ID should not be null");
        }
    }

    @Test
    void testFetchsweepdetails() throws Exception {
        Collection<Sweep> result = sweepSearchDAO.fetchsweepdetails(TEST_SWEEP_ID, HOST_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Should return sweep details for specific ID");
        
        // Verify the returned sweep matches the requested ID
        for (Sweep sweep : result) {
            Assertions.assertEquals(TEST_SWEEP_ID, sweep.getId().getSweepId(), 
                "Sweep ID should match the requested ID");
            Assertions.assertEquals(HOST_ID, sweep.getId().getHostId(), 
                "Host ID should match the requested ID");
        }
    }

    @Test
    void testFetchsweepdetailsArchive() throws Exception {
        // Test without archive ID
        Collection<Sweep> resultWithoutArchive = sweepSearchDAO.fetchsweepdetailsArchive(TEST_SWEEP_ID, HOST_ID, null);
        Assertions.assertNotNull(resultWithoutArchive, "Result should not be null without archive ID");

        // Test with archive ID (if available in your test environment)
        Collection<Sweep> resultWithArchive = sweepSearchDAO.fetchsweepdetailsArchive(TEST_SWEEP_ID, HOST_ID, "PREDICT_ARCH");
        Assertions.assertNotNull(resultWithArchive, "Result should not be null with archive ID");
    }

    @Test
    void testFetchMovementDetails() throws Exception {
        Collection<Movement> result = sweepSearchDAO.fetchMovementDetails(HOST_ID, ENTITY_ID, TEST_MOVEMENT_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Should return movement details");
        
        // Verify each movement has required fields
        for (Movement movement : result) {
            Assertions.assertNotNull(movement.getId(), "Movement ID should not be null");
            Assertions.assertEquals(HOST_ID, movement.getId().getHostId(), "Host ID should match");
            Assertions.assertEquals(ENTITY_ID, movement.getId().getEntityId(), "Entity ID should match");
            Assertions.assertEquals(TEST_MOVEMENT_ID, movement.getId().getMovementId(), "Movement ID should match");
        }
    }

    @Test
    void testFetchsweepCutOff() throws Exception {
        Sweep sweep = new Sweep();
        sweep.getId().setHostId(HOST_ID);
        sweep.getId().setSweepId(TEST_SWEEP_ID);
        
        Sweep result = sweepSearchDAO.fetchsweepCutOff(
            TEST_ACCOUNT_ID_CR, TEST_ACCOUNT_ID_DR,
            TEST_ENTITY_ID_CR, TEST_ENTITY_ID_DR,
            HOST_ID, TEST_SWEEP_ID, sweep, null);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(HOST_ID, result.getId().getHostId(), "Host ID should match");
        Assertions.assertEquals(TEST_SWEEP_ID, result.getId().getSweepId(), "Sweep ID should match");
    }

    @Test
    void testFetchdetails() throws Exception {
        // Test with basic parameters
        Collection<Sweep> result = sweepSearchDAO.fetchdetails(
            null,                  // sortOrder
            "U",                   // acctType
            "U",                   // status
            "M",                   // type
            "EUR",                 // currency
            "Atlas",               // message
            "101066619EUR",        // accountId
            "",                    // bookCode
            "",                    // inputUser
            "",                    // authUser
            "",                    // subUser
            ENTITY_ID,             // entityId
            HOST_ID,               // hostId
            "N",                   // postCutOff
            10.0,                  // amountOver
            1.0E11,                // amountUnder
            "01/10/2008",          // fromDate
            "08/10/2008",          // toDate
            "D",                   // format
            new String[]{"EUR"},   // currencyCodeArray
            null                   // archiveid
        );

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Should return sweep details");
        
        // Verify each sweep has required fields
        for (Sweep sweep : result) {
            Assertions.assertNotNull(sweep.getId(), "Sweep ID should not be null");
            Assertions.assertEquals(HOST_ID, sweep.getId().getHostId(), "Host ID should match");
            Assertions.assertEquals("EUR", sweep.getCurrencyCode(), "Currency code should match");
            
            // Check if the sweep is related to the specified account
            boolean isRelatedToAccount = 
                ("101066619EUR".equals(sweep.getAccountIdCr()) || 
                 "101066619EUR".equals(sweep.getAccountIdDr()));
            Assertions.assertTrue(isRelatedToAccount, "Sweep should be related to the specified account");
            
            // Check if the amount is within the specified range
            if (sweep.getOriginalSweepAmt() != null) {
                Assertions.assertTrue(sweep.getOriginalSweepAmt() >= 10.0, 
                    "Sweep amount should be over the minimum threshold");
                Assertions.assertTrue(sweep.getOriginalSweepAmt() <= 1.0E11, 
                    "Sweep amount should be under the maximum threshold");
            }
        }
    }

    @Test
    void testFetchdetailsWithFilters() throws Exception {
        // Test with specific filters
    /*    Collection<Sweep> result = sweepSearchDAO.fetchdetails(
            null,                  // sortOrder
            "U",                   // acctType
            "U",                   // status
            "M",                   // type
            "EUR",                 // currency
            "Atlas",               // message
                TEST_ACCOUNT_SEARCH,    // accountId
            "",                    // bookCode
            "User15",              // inputUser
            "",                    // authUser
            "",                    // subUser
            ENTITY_ID,             // entityId
            HOST_ID,               // hostId
            "N",                   // postCutOff
            1000.0,                // amountOver
            1.0E11,                // amountUnder
            "01/10/2008",          // fromDate
            "08/10/2008",          // toDate
            "D",                   // format
            new String[]{"EUR"},   // currencyCodeArray
            null                   // archiveid
        );*/

        Collection<Sweep> result = sweepSearchDAO.fetchdetails(
                null,                  // sortOrder
                "A",                   // acctType
                "U",                   // status
                "A",                   // type
                "EUR",                 // currency
                "",               // message
                "101066619EUR",    // accountId
                "",                    // bookCode
                "",              // inputUser
                "",                    // authUser
                "",                    // subUser
                ENTITY_ID,             // entityId
                HOST_ID,               // hostId
                "N",                   // postCutOff
                1000.0,                // amountOver
                ************.0,                // amountUnder
                "01/10/2008",          // fromDate
                "",          // toDate
                "D",                   // format
                new Object[]{"EUR"},   // currencyCodeArray
                null                   // archiveid
        );

        Assertions.assertNotNull(result, "Result should not be null");
        
        // If results are found, verify they match the filters
        if (!result.isEmpty()) {
            for (Sweep sweep : result) {
                Assertions.assertEquals(HOST_ID, sweep.getId().getHostId(), "Host ID should match");
                Assertions.assertEquals("EUR", sweep.getCurrencyCode(), "Currency code should match");
                Assertions.assertEquals("A", sweep.getSweepStatus(), "Status should be U");
                
                // Check if the sweep is related to the specified account
                boolean isRelatedToAccount = 
                    (TEST_ACCOUNT_SEARCH.equals(sweep.getAccountIdCr()) ||
                     TEST_ACCOUNT_SEARCH.equals(sweep.getAccountIdDr()));
                Assertions.assertTrue(isRelatedToAccount, "Sweep should be related to the specified account");
                
                // Check if the amount is within the specified range
                if (sweep.getOriginalSweepAmt() != null) {
                    Assertions.assertTrue(sweep.getOriginalSweepAmt() >= 1000.0, 
                        "Sweep amount should be over the minimum threshold");
                    Assertions.assertTrue(sweep.getOriginalSweepAmt() <= 1.0E11, 
                        "Sweep amount should be under the maximum threshold");
                }
            }
        }
    }

    @Test
    void testFetchdetailsWithArchive() throws Exception {
        // Test with archive ID
        Collection<Sweep> result = sweepSearchDAO.fetchdetails(
            null,                  // sortOrder
            "A",                   // acctType
            "A",                   // status
            "A",                   // type
            "All",                 // currency
            "",               // message
            "",        // accountId
            "",                    // bookCode
            "",                    // inputUser
            "",                    // authUser
            "",                    // subUser
            ENTITY_ID,             // entityId
            HOST_ID,               // hostId
            "N",                   // postCutOff
            0.0,                  // amountOver
            0.0,                // amountUnder
            "01/01/2008",          // fromDate
            "",          // toDate
            "D",                   // format
            new Object[]{"*","CAD","CHF","EUR","GBP","JPY","NOK","NZD","PLN","QAR", "SAR", "SEK", "TRY", "USD", "ZAR", "AUD", "CZK", "DKK", "HKD", "HUF", "MXN", "MYR", "SGD", "THB", "TWD", "AFN", "ANG", "ARS", "BGN", "BHD", "BND", "BRL", "BWP", "CLP", "CNH", "CNY", "CYP", "EEK", "GHC", "IDR", "ILS", "INR", "ISK", "KES", "KRW", "KWD", "LTL", "LVL", "MAD", "MTL", "OMR", "PHP", "PKR", "RSD", "RUB", "SIT", "SKK", "TND", "AED", "BAM", "ETB", "RON"},   // currencyCodeArray
            "PREDICT_ARCH"         // archiveid
        );

        Assertions.assertNotNull(result, "Result should not be null");
        // Note: We don't assert on emptiness as archive data might not be available in test environment
    }

    @Test
    void testGetCurrencyDetailsWithInvalidData() throws Exception {
        // Test with invalid host ID
        Collection<Currency> resultWithInvalidHost = sweepSearchDAO.getCurrencyDetails("INVALID_HOST", ENTITY_ID);
        Assertions.assertNotNull(resultWithInvalidHost, "Result should not be null with invalid host");
        Assertions.assertTrue(resultWithInvalidHost.isEmpty(), "Should return empty list with invalid host");

        // Test with invalid entity ID
        Collection<Currency> resultWithInvalidEntity = sweepSearchDAO.getCurrencyDetails(HOST_ID, "INVALID_ENTITY");
        Assertions.assertNotNull(resultWithInvalidEntity, "Result should not be null with invalid entity");
        Assertions.assertTrue(resultWithInvalidEntity.isEmpty(), "Should return empty list with invalid entity");
    }

    @Test
    void testFetchsweepdetailsWithInvalidData() throws Exception {
        // Test with invalid sweep ID
        Collection<Sweep> resultWithInvalidSweepId = sweepSearchDAO.fetchsweepdetails(-1L, HOST_ID);
        Assertions.assertNotNull(resultWithInvalidSweepId, "Result should not be null with invalid sweep ID");
        Assertions.assertTrue(resultWithInvalidSweepId.isEmpty(), "Should return empty list with invalid sweep ID");

        // Test with invalid host ID
        Collection<Sweep> resultWithInvalidHost = sweepSearchDAO.fetchsweepdetails(TEST_SWEEP_ID, "INVALID_HOST");
        Assertions.assertNotNull(resultWithInvalidHost, "Result should not be null with invalid host");
        Assertions.assertTrue(resultWithInvalidHost.isEmpty(), "Should return empty list with invalid host");
    }
} 