package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.work.dao.InputExceptionsDataDAO;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class InputExceptionsDataDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private InputExceptionsDataDAO inputExceptionsDataDAO;

    private OpTimer opTimer;
    private String dateFormat;
    private String fromDate;
    private String toDate;

    @BeforeEach
    void setUp() {
        opTimer = new OpTimer();
        dateFormat = "dd/MM/yyyy";
        
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        
        // Set from date to 1 month ago
       // cal.add(Calendar.MONTH, -1);
        fromDate = sdf.format(cal.getTime());
        
        // Set to date to current date
        cal.add(Calendar.DATE, 1);
        toDate = sdf.format(cal.getTime());
    }

    @Test
    void testGetInterfaceMessageList() throws SwtException {
        List result = inputExceptionsDataDAO.getInterfaceMessageList(fromDate, toDate, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetInterfaceMessageListWithInvalidDates() {
        String invalidFromDate = "invalid-date";
        String invalidToDate = "invalid-date";

        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList(invalidFromDate, invalidToDate, opTimer, dateFormat, false);
        }, "Should throw exception for invalid dates");
    }

    @Test
    void testGetInterfaceMessageListWithNullDates() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList(null, null, opTimer, dateFormat, false);
        }, "Should throw exception for null dates");
    }

    @Test
    void testGetInterfaceMessageListWithNullOpTimer() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList(fromDate, toDate, null, dateFormat, false);
        }, "Should throw exception for null OpTimer");
    }

    @Test
    void testGetInterfaceMessageListWithNullDateFormat() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList(fromDate, toDate, opTimer, null, false);
        }, "Should throw exception for null date format");
    }

    @Test
    void testGetInterfaceMessageListWithFromDateAfterToDate() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList(toDate, fromDate, opTimer, dateFormat, false);
        }, "Should throw exception when from date is after to date");
    }

    @Test
    void testGetInterfaceMessageListWithSameFromAndToDate() throws SwtException {
        List result = inputExceptionsDataDAO.getInterfaceMessageList(fromDate, fromDate, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null for same from and to date");
    }

    @Test
    void testGetInterfaceMessageListWithDifferentDateFormat() throws SwtException {
        String differentDateFormat = "dd/MM/yyyy";
        SimpleDateFormat sdf = new SimpleDateFormat(differentDateFormat);
        Date date = new Date();
        String newFromDate = sdf.format(date);
        String newToDate = sdf.format(date);

        List result = inputExceptionsDataDAO.getInterfaceMessageList(newFromDate, newToDate, opTimer, differentDateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null for different date format");
    }

    @Test
    void testGetInterfaceMessageListWithEmptyDates() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsDataDAO.getInterfaceMessageList("", "", opTimer, dateFormat, false);
        }, "Should throw exception for empty dates");
    }

    @Test
    void testGetInterfaceMessageListWithPCMFlag() throws SwtException {
        List result = inputExceptionsDataDAO.getInterfaceMessageList(fromDate, toDate, opTimer, dateFormat, true);
        Assertions.assertNotNull(result, "Result should not be null when PCM flag is true");
    }
} 