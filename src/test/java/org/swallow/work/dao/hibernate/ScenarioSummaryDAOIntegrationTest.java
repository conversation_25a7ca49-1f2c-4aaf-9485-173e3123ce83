package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.ScenarioSummaryDAO;
import org.swallow.work.model.AlertInstance;
import org.swallow.work.model.AlertTreeVO;
import org.swallow.work.model.EntityScenarioCount;
import org.swallow.work.model.ScenarioAlertCount;
import org.swallow.work.model.ScenarioInstanceLog;
import org.swallow.work.model.ScenarioInstanceMessage;
import org.swallow.work.model.ScenarioTreeElement;
import org.swallow.work.model.ScenariosSummary;
import org.swallow.exception.SwtException;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class ScenarioSummaryDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private ScenarioSummaryDAO scenarioSummaryDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String SCENARIO_ID = "SYS_INPUT_EXCEPTIONS";
    private static final String THRESHOLD = "Y";
    private static final String HIDE_ZERO = "N";
    private static final String ALERTABLE = "N";
    private static final String SELECTED_CURRENCY_GROUP = "ALL";
    private static final String CALL_OPTION = "N";
    private static final String SELECTED_TAB = "1";

    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();
    }

    @Test
    void testGetScenariosSummaryInfoDetails() throws SwtException {
        ScenariosSummary result = scenarioSummaryDAO.getScenariosSummaryInfoDetails(
            ROLE_ID, ENTITY_ID, THRESHOLD, HIDE_ZERO, ALERTABLE, 
            SELECTED_CURRENCY_GROUP, CALL_OPTION, SELECTED_TAB);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getCategoryInfo(), "Category info should not be null");
        Assertions.assertNotNull(result.getTree(), "Tree should not be null");
        Assertions.assertTrue(result.getTotalCount() >= 0, "Total count should be non-negative");
    }

    @Test
    void testGetScenarioCountByEntituCurrency() throws SwtException {



        Collection<EntityScenarioCount> result = scenarioSummaryDAO.getScenarioCountByEntituCurrency(
            "SYS_INPUT_EXCEPTIONS", ROLE_ID, THRESHOLD, "N", ENTITY_ID,
            "1|DESC", "All", ALERTABLE, "");

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetSelectedScenarioLastRun() throws SwtException {
        String result = scenarioSummaryDAO.getSelectedScenarioLastRun(SCENARIO_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetScenarioInstAccess() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioInstAccess(
            "rcha", HOST_ID, ROLE_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetScenarioReqAccess() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioReqAccess(
            "rcha", HOST_ID, ROLE_ID, ENTITY_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetScenarioFacility() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioFacility(SCENARIO_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetAlertsScenarioCount() throws SwtException {
        AlertInstance instance = new AlertInstance();
        instance.setFaciltityId("SCENARIO_INSTANCE_MONITOR");
        instance.setHostId(HOST_ID);
        instance.setEntityId(ENTITY_ID);
        instance.setStatus("");

        AlertTreeVO result = scenarioSummaryDAO.getAlertsScenarioCount(instance);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getScenarioAlertList(), "Scenario alert list should not be null");
        Assertions.assertTrue(result.getTotalCount() >= 0, "Total count should be non-negative");
    }

    @Test
    void testGetInstanceLogs() throws SwtException {
        String instanceId = "1";
        List<ScenarioInstanceLog> result = scenarioSummaryDAO.getInstanceLogs(instanceId);

        Assertions.assertNotNull(result, "Result should not be null");
    }

    // Tests with invalid data
    @Test
    void testGetScenariosSummaryInfoDetailsWithInvalidData() throws SwtException {
        ScenariosSummary result = scenarioSummaryDAO.getScenariosSummaryInfoDetails(
            "INVALID_ROLE", "INVALID_ENTITY", THRESHOLD, HIDE_ZERO, ALERTABLE,
            SELECTED_CURRENCY_GROUP, CALL_OPTION, SELECTED_TAB);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getCategoryInfo(), "Category info should not be null");
        Assertions.assertNotNull(result.getTree(), "Tree should not be null");
    }

    @Test
    void testGetScenarioCountByEntituCurrencyWithInvalidData() throws SwtException {
        Collection<EntityScenarioCount> result = scenarioSummaryDAO.getScenarioCountByEntituCurrency(
            "INVALID_SCENARIO", "INVALID_ROLE", THRESHOLD, "Y", "INVALID_ENTITY",
            "c.id.exchangeRateDate|asc", SELECTED_CURRENCY_GROUP, ALERTABLE, "ALL");

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetSelectedScenarioLastRunWithInvalidData() throws SwtException {
        String result = scenarioSummaryDAO.getSelectedScenarioLastRun("INVALID_SCENARIO");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid scenario");
    }

    @Test
    void testGetScenarioInstAccessWithInvalidData() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioInstAccess(
            "INVALID_SCENARIO", "INVALID_HOST", "INVALID_ROLE", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetScenarioReqAccessWithInvalidData() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioReqAccess(
            "INVALID_SCENARIO", "INVALID_HOST", "INVALID_ROLE", "INVALID_ENTITY");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetScenarioFacilityWithInvalidData() throws SwtException {
        String result = scenarioSummaryDAO.getScenarioFacility("INVALID_SCENARIO");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid scenario");
    }

    @Test
    void testGetAlertsScenarioCountWithInvalidData() throws SwtException {
        AlertInstance instance = new AlertInstance();
        instance.setFaciltityId("INVALID_FACILITY");
        instance.setHostId("INVALID_HOST");
        instance.setEntityId("INVALID_ENTITY");
        instance.setStatus("");

        AlertTreeVO result = scenarioSummaryDAO.getAlertsScenarioCount(instance);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getScenarioAlertList(), "Scenario alert list should not be null");
        Assertions.assertTrue(result.getTotalCount() >= 0, "Total count should be non-negative");
    }

    @Test
    void testGetInstanceLogsWithInvalidData() throws SwtException {
        List<ScenarioInstanceLog> result = scenarioSummaryDAO.getInstanceLogs("985510000");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid instance");
    }
} 