package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.MetagroupMonitorDAO;
import org.swallow.work.model.MetagroupCodePredictedBalanceTO;
import org.swallow.work.model.MetagroupMonitorCurrencyBalanceTO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.*;

public class MetagroupMonitorDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private MetagroupMonitorDAO metagroupMonitorDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String LOCATION_ID = "LOC1";
    private static final String METAGROUP_ID = "META1";
    private static final String GROUP_CODE = "GRP1";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURRENCY";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";
    private static final String INVALID_LOCATION_ID = "INVALID_LOCATION";
    private static final String INVALID_METAGROUP_ID = "INVALID_META";
    private static final String INVALID_GROUP_CODE = "INVALID_GROUP";

    private Date testDate;
    private SystemFormats systemFormats;
    private ArrayList<MetagroupMonitorCurrencyBalanceTO> metaMonitorDetailsTotalList;
    private ArrayList<MetagroupMonitorCurrencyBalanceTO> groupMonitorDetailsTotalList;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize system formats
        systemFormats = new SystemFormats();
        systemFormats.setDateFormatValue("yyyy-MM-dd");
        systemFormats.setCurrencyFormat("currencyPat2");

        // Initialize total lists
        metaMonitorDetailsTotalList = new ArrayList<>();
        groupMonitorDetailsTotalList = new ArrayList<>();
    }

    @Test
    void testGetMetagroupMonitorDetailsUsingStoredProc() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getMetagroupMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, metaMonitorDetailsTotalList,
            ROLE_ID, LOCATION_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Verify the structure of the result
        Assertions.assertTrue(result.get(0) instanceof Collection, "First element should be a collection");
        Assertions.assertTrue(result.get(1) instanceof String, "Second element should be a string");
    }

    @Test
    void testGetMetagroupMonitorDetailsUsingStoredProcWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            metagroupMonitorDAO.getMetagroupMonitorDetailsUsingStoredProc(
                INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, testDate, systemFormats, metaMonitorDetailsTotalList,
                INVALID_ROLE_ID, INVALID_LOCATION_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetGroupMonitorDetailsUsingStoredProc() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getGroupMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, groupMonitorDetailsTotalList,
            ROLE_ID, LOCATION_ID, METAGROUP_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Verify the structure of the result
        Assertions.assertTrue(result.get(0) instanceof Collection, "First element should be a collection");
        Assertions.assertTrue(result.get(1) instanceof String, "Second element should be a string");
    }

    @Test
    void testGetGroupMonitorDetailsUsingStoredProcWithInvalidData() {
        Assertions.assertThrows(SwtException.class, () -> {
            metagroupMonitorDAO.getGroupMonitorDetailsUsingStoredProc(
                INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, testDate, systemFormats, groupMonitorDetailsTotalList,
                INVALID_ROLE_ID, INVALID_LOCATION_ID, INVALID_METAGROUP_ID);
        }, "Should throw SwtException for invalid data");
    }

    @Test
    void testGetBookMonitorDetailsUsingStoredProc() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getBookMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, groupMonitorDetailsTotalList,
            ROLE_ID, LOCATION_ID, GROUP_CODE);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
        
        // Verify the structure of the result
        Assertions.assertTrue(result.get(0) instanceof Collection, "First element should be a collection");
        Assertions.assertTrue(result.get(1) instanceof String, "Second element should be a string");
    }

    @Test
    void testGetBookMonitorDetailsUsingStoredProcWithInvalidData() throws SwtException {
        Assertions.assertThrows(Exception.class, () -> {
            metagroupMonitorDAO.getBookMonitorDetailsUsingStoredProc(
                INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, testDate, systemFormats, groupMonitorDetailsTotalList,
                INVALID_ROLE_ID, INVALID_LOCATION_ID, INVALID_GROUP_CODE);
        }, "Should throw SwtException for invalid data");


    }

    @Test
    void testGetMetagroupMonitorDetailsUsingStoredProcWithNullDate() {
        Assertions.assertThrows(Exception.class, () -> {
            metagroupMonitorDAO.getMetagroupMonitorDetailsUsingStoredProc(
                ENTITY_ID, CURRENCY_CODE, null, systemFormats, metaMonitorDetailsTotalList,
                ROLE_ID, LOCATION_ID);
        }, "Should throw IllegalArgumentException for null date");
    }

    @Test
    void testGetGroupMonitorDetailsUsingStoredProcWithNullDate() {
        Assertions.assertThrows(Exception.class, () -> {
            metagroupMonitorDAO.getGroupMonitorDetailsUsingStoredProc(
                ENTITY_ID, CURRENCY_CODE, null, systemFormats, groupMonitorDetailsTotalList,
                ROLE_ID, LOCATION_ID, METAGROUP_ID);
        }, "Should throw IllegalArgumentException for null date");
    }

    @Test
    void testGetBookMonitorDetailsUsingStoredProcWithNullDate() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            metagroupMonitorDAO.getBookMonitorDetailsUsingStoredProc(
                ENTITY_ID, CURRENCY_CODE, null, systemFormats, groupMonitorDetailsTotalList,
                ROLE_ID, LOCATION_ID, GROUP_CODE);
        }, "Should throw IllegalArgumentException for null date");
    }

    @Test
    void testGetMetagroupMonitorDetailsUsingStoredProcWithEmptyLists() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getMetagroupMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, new ArrayList<>(),
            ROLE_ID, LOCATION_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetGroupMonitorDetailsUsingStoredProcWithEmptyLists() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getGroupMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, new ArrayList<>(),
            ROLE_ID, LOCATION_ID, METAGROUP_ID);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetBookMonitorDetailsUsingStoredProcWithEmptyLists() throws SwtException {
        ArrayList<Object> result = metagroupMonitorDAO.getBookMonitorDetailsUsingStoredProc(
            ENTITY_ID, CURRENCY_CODE, testDate, systemFormats, new ArrayList<>(),
            ROLE_ID, LOCATION_ID, GROUP_CODE);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }
} 