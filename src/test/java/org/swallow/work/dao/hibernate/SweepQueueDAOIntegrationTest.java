package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import config.JsonTestHelper;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.SweepQueueDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import org.swallow.work.service.SweepDetailVO; // Ensure this import exists

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.ArrayList;

public class SweepQueueDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepQueueDAO sweepQueueDAO;

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "101066619EUR";
    private static final Long TEST_SWEEP_ID = 10211L;
    private static final int CURRENCY_GRP_ACCESS = 0; // Full access

    private Date testDate;
    private Sweep testSweep;
    private Sweep testCancelSweep;
    private SweepQueueDetailVO testSweepQueueDetailVO;
    private Date testDateFrom;
    private Date testDateTo;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test sweep
        testSweep = new Sweep();
        testSweep.getId().setHostId(HOST_ID);
        testSweep.getId().setSweepId(TEST_SWEEP_ID);
        testSweep.setEntityId(ENTITY_ID);
        testSweep.setCurrencyCode(CURRENCY_CODE);
        testSweep.setQueueName("N"); // New queue
        testSweep.setRequestUser("User15");
        testSweep.setRequestRole(ROLE_ID);
        testSweep.setAccountIdCr(ACCOUNT_ID);
        testSweep.setAccountIdDr("390880531EUR");
        testSweep.setEntityIdCr(ENTITY_ID);
        testSweep.setEntityIdDr(ENTITY_ID);
        testSweep.setSweepType("A"); // Auto
        testSweep.setSweepStatus("N"); // New
        testSweep.setValueDate(testDate);
        testSweep.setOriginalSweepAmt(1000.00);
        testSweep.setAccountType("All");

        // Initialize test sweep queue detail VO
        testSweepQueueDetailVO = new SweepQueueDetailVO();
        testSweepQueueDetailVO.setEntityAccessList(new ArrayList<>());
        testCancelSweep = new Sweep();
    }

    @Test
    void testGetSweepQueueDetail() throws Exception {


        testSweep = new Sweep();
        testSweep.getId().setHostId(HOST_ID);
        testSweep.getId().setSweepId(TEST_SWEEP_ID);
        testSweep.setEntityId(ENTITY_ID);
        testSweep.setCurrencyCode(CURRENCY_CODE);
        testSweep.setQueueName("N"); // New queue
        testSweep.setRequestUser("");
        testSweep.setRequestRole(ROLE_ID);
        testSweep.setAccountIdCr(null);
        testSweep.setAccountIdDr(null);
        testSweep.setEntityIdCr(ENTITY_ID);
        testSweep.setEntityIdDr(ENTITY_ID);
        testSweep.setSweepType("A"); // Auto
        testSweep.setSweepStatus("N"); // New
        testSweep.setValueDate(testDate);
        testSweep.setOriginalSweepAmt(null);
        testSweep.setAccountType("All");
        // Assuming currencyCode is a single string or needs specific handling.
        // If it needs a list, adjust accordingly.
        // Using EUR as an example, replace if needed based on how your VO handles multiple currencies.
        String CURRENCY_CODE = "EUR";

        testCancelSweep.setCurrencyCode(CURRENCY_CODE);
        // Set other relevant fields if necessary, e.g., sort order, pagination

        sweepQueueDAO.getSweepQueueDetail(testSweep, ROLE_ID, CURRENCY_GRP_ACCESS, testSweepQueueDetailVO);

        Assertions.assertNotNull(testSweepQueueDetailVO.getSweepDetailList(), "Sweep detail list should not be null");
        Assertions.assertNotNull(testSweepQueueDetailVO.getOtherDetailList(), "Other detail list should not be null");

        // Verify that the lists are populated
        Assertions.assertFalse(testSweepQueueDetailVO.getSweepDetailList().isEmpty(),
            "Sweep detail list should not be empty");
    }

    @Test
    void testGetCancelQueueDetail() throws Exception {
        SystemFormats systemFormats = new SystemFormats();

        testCancelSweep.getId().setHostId(HOST_ID);
        testCancelSweep.setEntityId(ENTITY_ID); // Assuming ENTITY_ID is defined in your class, e.g., "RABONL2U"
        testCancelSweep.setQueueName("C"); // Set to Cancel Queue
        testCancelSweep.setRequestUser("User15");
        testCancelSweep.setRequestRole("VendorSuppor");
        testCancelSweep.setAccountType("All"); // Or specific type if needed
        testCancelSweep.setValueFromDate(testDateFrom);
        testCancelSweep.setValueToDate(testDateTo);
        // Assuming currencyCode is a single string or needs specific handling.
        // If it needs a list, adjust accordingly.
        // Using EUR as an example, replace if needed based on how your VO handles multiple currencies.
        String CURRENCY_CODE = "'1522','ASIA-PACIFIC','CAD','CHF','EAST EUROPE','EMERGING','EUR','GBP','SCANDIES','USD','USDEUR'";

        testCancelSweep.setCurrencyCode(CURRENCY_CODE);
        // Set other relevant fields if necessary, e.g., sort order, pagination
        testCancelSweep.setSortOrder(null); // Or specific sort order



        int result = sweepQueueDAO.getCancelQueueDetail(
            testCancelSweep,
            testSweepQueueDetailVO,
            1, // currentPage
            10, // maxPage
            "all#none", // filterSortStatus
            systemFormats,
            ROLE_ID
        );

        Assertions.assertTrue(result >= 0, "Result should be a non-negative number");
        Assertions.assertNotNull(testSweepQueueDetailVO.getSweepDetailList(), "Sweep detail list should not be null");
    }

    @Test
    void testSubmit() throws Exception {
        // Create a comma-separated list of sweep IDs
        String selectedList = TEST_SWEEP_ID.toString();

        Collection<Sweep> result = sweepQueueDAO.submit(testSweep, selectedList);

        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");

        // Verify that the returned sweeps match the selected IDs
        for (Sweep sweep : result) {
            Assertions.assertEquals(TEST_SWEEP_ID, sweep.getId().getSweepId(),
                "Sweep ID should match the selected ID");
            Assertions.assertEquals(HOST_ID, sweep.getId().getHostId(),
                "Host ID should match");
        }
    }

    @Test
    void testGetPredictBalance() throws Exception {
        Double result = sweepQueueDAO.getPredictBalance(
            ENTITY_ID,
            HOST_ID,
            CURRENCY_CODE,
            ACCOUNT_ID,
            testDate
        );

        Assertions.assertNotNull(result, "Result should not be null");
        // Note: We don't assert on the exact value as it depends on the database state
    }

    @Test
    void testProcessSweep() throws Exception {
        SystemFormats systemFormats = new SystemFormats();
        int result = sweepQueueDAO.processSweep(testSweep, systemFormats);

        Assertions.assertTrue(result >= 0, "Result should be a non-negative number");
    }

    @Test
    void testSubmitWithInvalidData() throws Exception {
        // Test with invalid sweep ID
        String invalidSelectedList = "-1";

        Collection<Sweep> result = sweepQueueDAO.submit(testSweep, invalidSelectedList);

        // Should return empty list for invalid data
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid sweep ID");
    }

}