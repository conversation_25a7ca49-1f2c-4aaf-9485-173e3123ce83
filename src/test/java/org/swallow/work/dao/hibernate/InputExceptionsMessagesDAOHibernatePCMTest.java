package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.work.dao.InputExceptionsMessagesDAO;
import org.swallow.work.model.InputExceptionsDataModel;
import org.swallow.work.model.InputExceptionsDataPageDTO;
import org.swallow.work.model.InputMessageType;

import java.util.Collection;
import java.util.Date;

public class InputExceptionsMessagesDAOHibernatePCMTest extends BaseDAOIntegrationTest {

    @Autowired
    @Qualifier("inputExceptionsMessagesDAOPCM")
    private InputExceptionsMessagesDAO inputExceptionsMessagesDAO;

    private OpTimer opTimer;
    private InputExceptionsDataPageDTO pageDTO;
    private String dateFormat;
    private String currencyCode;
    private String messageStatus;
    private String date;
    private String seqId;

    @BeforeEach
    void setUp() {
        opTimer = new OpTimer();
        dateFormat = "dd/MM/yyyy";
        currencyCode = "EUR";
        messageStatus = "FAILED";
        seqId = "12345";

        // Set up test date
        Date testDate = new Date();
        date = new java.text.SimpleDateFormat(dateFormat).format(testDate);

        // Set up page DTO
        pageDTO = new InputExceptionsDataPageDTO();
        pageDTO.setPageNumber(1);
        pageDTO.setRecordsPerPage(10);
        pageDTO.setFromDate(date);
        pageDTO.setToDate(date);
        pageDTO.setMessageStatus(1); // Assuming 1 represents FAILED status
        pageDTO.setMessageType("MT103");
        pageDTO.setOrderBy(InputExceptionsDataPageDTO.ORDER_SEQ_NO);
        pageDTO.setOrderDesc(false);
    }

    @Test
    void testGetMessagePagePCM() throws SwtException {
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getCurrentPageData(), "Current page data should not be null");
    }

    @Test
    void testGetMessagePagePCMWithNullPageDTO() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePagePCM(null, opTimer, dateFormat, false);
        }, "Should throw exception for null page DTO");
    }

    @Test
    void testGetMessagePagePCMWithNullOpTimer() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, null, dateFormat, false);
        }, "Should throw exception for null OpTimer");
    }

    @Test
    void testGetMessagePagePCMWithNullDateFormat() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, null, false);
        }, "Should throw exception for null date format");
    }

    @Test
    void testGetMessagePagePCMWithDashboardFlag() throws SwtException {
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, dateFormat, true);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertNotNull(result.getCurrentPageData(), "Current page data should not be null");
    }

    @Test
    void testGetMessageTypeListPCM() throws SwtException {
        Collection<String> result = inputExceptionsMessagesDAO.getMessageTypeList(
            currencyCode, messageStatus, date, dateFormat);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMessageTypeListPCMWithInvalidCurrency() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                "INVALID", messageStatus, date, dateFormat);
        }, "Should throw exception for invalid currency");
    }

    @Test
    void testGetMessageTypeListPCMWithInvalidStatus() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                currencyCode, "INVALID", date, dateFormat);
        }, "Should throw exception for invalid status");
    }

    @Test
    void testGetMessageTypeListPCMWithInvalidDate() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageTypeList(
                currencyCode, messageStatus, "invalid-date", dateFormat);
        }, "Should throw exception for invalid date");
    }

    @Test
    void testGetArchiveMessagePCM() {
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetArchiveMessagePCMWithInvalidSeqId() {
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage("999999", opTimer);
        Assertions.assertNull(result, "Result should be null for invalid sequence ID");
    }

    @Test
    void testGetArchiveMessagePCMWithNullSeqId() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getArchiveMessage(null, opTimer);
        }, "Should throw exception for null sequence ID");
    }

    @Test
    @Transactional
    void testReprocessPCM() throws SwtException {
        String[] ids = new String[] { seqId };
        inputExceptionsMessagesDAO.reprocess(ids, opTimer, true);
        // Verify the reprocess by checking the status
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null after reprocess");
    }

    @Test
    void testReprocessPCMWithInvalidIds() {
        String[] invalidIds = new String[] { "999999" };
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.reprocess(invalidIds, opTimer, true);
        }, "Should throw exception for invalid IDs");
    }

    @Test
    void testReprocessPCMWithNullIds() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.reprocess(null, opTimer, true);
        }, "Should throw exception for null IDs");
    }

    @Test
    @Transactional
    void testSuppressOrRejectPCM() throws SwtException {
        String[] ids = new String[] { seqId };
        String processOption = "SUPPRESS";
        inputExceptionsMessagesDAO.suppressOrReject(ids, processOption, opTimer, true);
        // Verify the suppress/reject by checking the status
        InputExceptionsDataModel result = inputExceptionsMessagesDAO.getArchiveMessage(seqId, opTimer);
        Assertions.assertNotNull(result, "Result should not be null after suppress/reject");
    }

    @Test
    void testSuppressOrRejectPCMWithInvalidProcessOption() {
        String[] ids = new String[] { seqId };
        String invalidOption = "INVALID";
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.suppressOrReject(ids, invalidOption, opTimer, true);
        }, "Should throw exception for invalid process option");
    }

    @Test
    void testSuppressOrRejectPCMWithNullProcessOption() {
        String[] ids = new String[] { seqId };
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.suppressOrReject(ids, null, opTimer, true);
        }, "Should throw exception for null process option");
    }

    @Test
    void testGetMessageTypePCM() {
        InputMessageType result = inputExceptionsMessagesDAO.getMessageType("MT103", opTimer);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMessageTypePCMWithInvalidType() {
        InputMessageType result = inputExceptionsMessagesDAO.getMessageType("INVALID", opTimer);
        Assertions.assertNull(result, "Result should be null for invalid message type");
    }

    @Test
    void testGetMessageTypePCMWithNullType() {
        Assertions.assertThrows(SwtException.class, () -> {
            inputExceptionsMessagesDAO.getMessageType(null, opTimer);
        }, "Should throw exception for null message type");
    }

    @Test
    void testGetMessagePagePCMWithDifferentOrderBy() throws SwtException {
        pageDTO.setOrderBy(InputExceptionsDataPageDTO.ORDER_VALUE_DATE);
        pageDTO.setOrderDesc(true);
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null with different order by");
    }

    @Test
    void testGetMessagePagePCMWithDifferentMessageType() throws SwtException {
        pageDTO.setMessageType("MT202");
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null with different message type");
    }

    @Test
    void testGetMessagePagePCMWithDifferentStatus() throws SwtException {
        pageDTO.setMessageStatus(2); // Assuming 2 represents a different status
        InputExceptionsDataPageDTO result = inputExceptionsMessagesDAO.getMessagePagePCM(pageDTO, opTimer, dateFormat, false);
        Assertions.assertNotNull(result, "Result should not be null with different status");
    }
} 