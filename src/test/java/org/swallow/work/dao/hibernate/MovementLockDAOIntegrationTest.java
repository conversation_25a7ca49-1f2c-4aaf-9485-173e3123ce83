package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.MovementLockDAO;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementLock;
import org.swallow.exception.SwtException;

import java.util.*;

public class MovementLockDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private MovementLockDAO movementLockDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String UPDATE_USER = "TestUser";
    private static final Long MOVEMENT_ID = 1116328L;
    private static final Long MATCH_ID = 1000226L;

    private MovementLock testMovementLock;
    private Movement testMovement;
    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test movement lock
        testMovementLock = new MovementLock();
        testMovementLock.getId().setHostId(HOST_ID);
        testMovementLock.getId().setEntityId(ENTITY_ID);
        testMovementLock.getId().setMovementId(MOVEMENT_ID);
        testMovementLock.setCurrCode(CURRENCY_CODE);
        testMovementLock.setUpdateUser(UPDATE_USER);
        testMovementLock.setUpdateDate(testDate);

        // Initialize test movement
        testMovement = new Movement();
        testMovement.getId().setHostId(HOST_ID);
        testMovement.getId().setEntityId(ENTITY_ID);
        testMovement.getId().setMovementId(MOVEMENT_ID);
        testMovement.setCurrencyCode(CURRENCY_CODE);
        testMovement.setMatchId(MATCH_ID);
    }

    @Test
    void testLockMovement_shouldSucceedForNewLock() throws SwtException {
        boolean result = movementLockDAO.lockMovement(testMovementLock);
        Assertions.assertTrue(result, "Should successfully lock a new movement");
    }

    @Test
    void testLockMovement_shouldFailForExistingLock() throws SwtException {
        // First lock
        movementLockDAO.lockMovement(testMovementLock);
        
        // Try to lock again
        boolean result = movementLockDAO.lockMovement(testMovementLock);
        Assertions.assertFalse(result, "Should fail to lock an already locked movement");
    }

    @Test
    void testLockMultipleMovements_shouldSucceed() throws SwtException {
        Collection<Movement> movements = Arrays.asList(testMovement);
        boolean result = movementLockDAO.lockMultipleMovements(movements, UPDATE_USER);
        Assertions.assertTrue(result, "Should successfully lock multiple movements");
    }

    @Test
    void testUnLockMovement_shouldSucceed() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        // Then unlock it
        boolean result = movementLockDAO.unLockMovement(testMovementLock);
        Assertions.assertTrue(result, "Should successfully unlock a movement");
    }

    @Test
    void testUnLockMovement_shouldSucceedForNonExistentLock() throws SwtException {
        boolean result = movementLockDAO.unLockMovement(testMovementLock);
        Assertions.assertTrue(result, "Should succeed when unlocking a non-existent lock");
    }

    @Test
    void testUnLockMovement_shouldSucceedForCollection() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        // Then unlock it using collection
        Collection<MovementLock> locks = Arrays.asList(testMovementLock);
        boolean result = movementLockDAO.unLockMovement(locks);
        Assertions.assertTrue(result, "Should successfully unlock movements from collection");
    }

    @Test
    void testGetLockUpdateUser_shouldReturnCorrectUser() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        String result = movementLockDAO.getLockUpdateUser(HOST_ID, MOVEMENT_ID);
        Assertions.assertEquals(UPDATE_USER, result, "Should return correct update user");
    }

    @Test
    void testGetMovementsLocksSameMatchId_shouldReturnCorrectUsers() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        String result = movementLockDAO.getMovementsLocksSameMatchId(HOST_ID, MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Should return non-null result");
        Assertions.assertTrue(result.contains(UPDATE_USER), "Should contain the update user");
    }

    @Test
    void testGetMovementsLocks_shouldReturnCorrectUsers() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        String result = movementLockDAO.getMovementsLocks(HOST_ID, "DifferentUser", MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Should return non-null result");
        Assertions.assertTrue(result.contains(UPDATE_USER), "Should contain the update user");
    }

    @Test
    void testGetMovementDetails_shouldReturnCorrectMovement() throws SwtException {
        Movement result = movementLockDAO.getMovementDetails(HOST_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Should return non-null movement");
        Assertions.assertEquals(HOST_ID, result.getId().getHostId(), "Should have correct host ID");
        Assertions.assertEquals(MOVEMENT_ID, result.getId().getMovementId(), "Should have correct movement ID");
    }

    @Test
    void testDeleteMovementLock_shouldSucceed() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        // Then delete the lock
        movementLockDAO.deleteMovementLock(UPDATE_USER);
        
        // Verify the lock is gone
        String result = movementLockDAO.getLockUpdateUser(HOST_ID, MOVEMENT_ID);
        Assertions.assertNull(result, "Lock should be deleted");
    }

    @Test
    void testCheckMvtsLock_shouldReturnCorrectLocks() throws SwtException {
        // First lock the movement
        movementLockDAO.lockMovement(testMovementLock);
        
        HashMap<Long, String> result = movementLockDAO.checkMvtsLock(
            HOST_ID, ENTITY_ID, "DifferentUser", MOVEMENT_ID.toString());
        
        Assertions.assertNotNull(result, "Should return non-null result");
        Assertions.assertTrue(result.containsKey(MOVEMENT_ID), "Should contain the movement ID");
        Assertions.assertEquals(UPDATE_USER, result.get(MOVEMENT_ID), "Should have correct update user");
    }
} 