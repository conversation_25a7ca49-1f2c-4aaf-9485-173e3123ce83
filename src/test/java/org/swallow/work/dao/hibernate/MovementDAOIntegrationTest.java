package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementExt;
import org.swallow.work.model.MovementMessage;
import org.swallow.maintenance.model.Currency;
import org.swallow.exception.SwtException;

import java.util.*;

public class MovementDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private MovementDAO movementDAO;

    // Constants for test data
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String CURRENCY_GROUP_ID = "ALL";
    private static final String ROLE_ID = "VendorSuppor";
    private static final Long MOVEMENT_ID = 1116328L;
    private static final Long MATCH_ID = 1000226L;
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String BOOK_CODE = "BOOK1";
    private static final String MOVEMENT_TYPE = "C";
    private static final Double AMOUNT = 1000.0;
    private static final String SIGN = "+";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURRENCY";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";
    private static final Long INVALID_MOVEMENT_ID = 999999L;
    private static final Long INVALID_MATCH_ID = 999999L;
    private static final String INVALID_ACCOUNT_ID = "INVALID_ACCOUNT";
    private static final String INVALID_BOOK_CODE = "INVALID_BOOK";
    private static final String INVALID_MOVEMENT_TYPE = "INVALID_TYPE";
    private static final Double INVALID_AMOUNT = -1000.0;
    private static final String INVALID_SIGN = "INVALID_SIGN";

    private Movement testMovement;
    private MovementExt testMovementExt;
    private Date testDate;

    @BeforeEach
    void setUp() {
        // Use a fixed date to ensure test determinism
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize test movement
        testMovement = new Movement();
        testMovement.getId().setHostId(HOST_ID);
        testMovement.getId().setEntityId(ENTITY_ID);
        testMovement.getId().setMovementId(MOVEMENT_ID);
        testMovement.setCurrencyCode(CURRENCY_CODE);
        testMovement.setBookCode(BOOK_CODE);
        testMovement.setMovementType(MOVEMENT_TYPE);
        testMovement.setAmount(AMOUNT);
        testMovement.setSign(SIGN);
        testMovement.setAccountId(ACCOUNT_ID);
        testMovement.setValueDate(testDate);
        testMovement.setMatchId(MATCH_ID);

        // Initialize test movement extension
        testMovementExt = new MovementExt();
        testMovementExt.getId().setHostId(HOST_ID);
        testMovementExt.getId().setEntityId(ENTITY_ID);
        testMovementExt.getId().setMovementId(MOVEMENT_ID);
    }

    @Test
    void testGetCurrencyList() throws SwtException {
        List<Currency> result = movementDAO.getCurrencyList(ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetCurrencyListWithInvalidData() throws SwtException {
        List<Currency> result = movementDAO.getCurrencyList(INVALID_ENTITY_ID, INVALID_HOST_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetCurrency() throws SwtException {
        String result = movementDAO.getCurrency(CURRENCY_CODE);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetCurrencyWithInvalidData() throws SwtException {
        String result = movementDAO.getCurrency(INVALID_CURRENCY_CODE);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid currency");
    }

    @Test
    void testGetOutMovbyposlevbydate() throws SwtException {
        int result = movementDAO.getOutMovbyposlevbydate(HOST_ID, ENTITY_ID, CURRENCY_CODE, testDate, 1, true);
        Assertions.assertTrue(result >= 0, "Result should be non-negative");
    }

    @Test
    void testGetOutMovbyposlevbydateWithInvalidData() throws SwtException {
        int result = movementDAO.getOutMovbyposlevbydate(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, testDate, 1, true);
        Assertions.assertTrue(result >= 0, "Result should be non-negative for invalid data");
    }

    @Test
    void testGetOutMovbyposlevinterimbydate() throws SwtException {
        int result = movementDAO.getOutMovbyposlevinterimbydate(HOST_ID, ENTITY_ID, CURRENCY_CODE, testDate, 1, 2, true);
        Assertions.assertTrue(result >= 0, "Result should be non-negative");
    }

    @Test
    void testGetOutMovbyposlevinterimbydateWithInvalidData() throws SwtException {
        int result = movementDAO.getOutMovbyposlevinterimbydate(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, testDate, 1, 2, true);
        Assertions.assertTrue(result >= 0, "Result should be non-negative for invalid data");
    }

    @Test
    void testGetOutStandingStatusbyRole() throws SwtException {
        String result = movementDAO.getOutStandingStatusbyRole(HOST_ID, ENTITY_ID, CURRENCY_CODE, ROLE_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetOutStandingStatusbyRoleWithInvalidData() throws SwtException {
        String result = movementDAO.getOutStandingStatusbyRole(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, INVALID_ROLE_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovement() throws SwtException {
        Movement result = movementDAO.getMovement(HOST_ID, ENTITY_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(HOST_ID, result.getId().getHostId(), "Should have correct host ID");
        Assertions.assertEquals(ENTITY_ID, result.getId().getEntityId(), "Should have correct entity ID");
        Assertions.assertEquals(MOVEMENT_ID, result.getId().getMovementId(), "Should have correct movement ID");
    }

    @Test
    void testGetMovementWithInvalidData() throws SwtException {
        Movement result = movementDAO.getMovement(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MOVEMENT_ID);
        Assertions.assertNull(result, "Should return null for invalid data");
    }

    @Test
    void testGetMovementWithIds() throws SwtException {
        List<Movement> result = movementDAO.getMovement(HOST_ID, ENTITY_ID, MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMovementWithInvalidIds() throws SwtException {
        List<Movement> result = movementDAO.getMovement(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testUpdateMovements() throws SwtException {
        Collection<Movement> movements = Arrays.asList(testMovement);
        movementDAO.updateMovements(movements);

        // Verify the update
        Movement result = movementDAO.getMovement(HOST_ID, ENTITY_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(AMOUNT, result.getAmount(), "Should have correct amount");
    }

    @Test
    void testUpdateMovementsWithInvalidData() throws SwtException {
        Movement invalidMovement = new Movement();
        invalidMovement.getId().setHostId(INVALID_HOST_ID);
        invalidMovement.getId().setEntityId(INVALID_ENTITY_ID);
        invalidMovement.getId().setMovementId(INVALID_MOVEMENT_ID);
        invalidMovement.setAmount(INVALID_AMOUNT);

        Collection<Movement> movements = Arrays.asList(invalidMovement);
        Assertions.assertThrows(SwtException.class, () -> {
            movementDAO.updateMovements(movements);
        }, "Should throw exception for invalid data");
    }

    @Test
    void testGetMovementDetails() throws SwtException {
        Movement result = movementDAO.getMovementDetails(HOST_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(HOST_ID, result.getId().getHostId(), "Should have correct host ID");
        Assertions.assertEquals(MOVEMENT_ID, result.getId().getMovementId(), "Should have correct movement ID");
    }

    @Test
    void testGetMovementDetailsWithInvalidData() throws SwtException {
        Movement result = movementDAO.getMovementDetails(INVALID_HOST_ID, INVALID_MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.getId().getHostId() == null, "Should have null host ID for invalid data");
    }

    @Test
    void testUpdateMovementDetails() throws SwtException {
        testMovement.setAmount(2000.0);
        movementDAO.updateMovementDetails(testMovement);

        // Verify the update
        Movement result = movementDAO.getMovement(HOST_ID, ENTITY_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(2000.0, result.getAmount(), "Should have updated amount");
    }

    @Test
    void testUpdateMovementDetailsWithInvalidData() throws SwtException {
        Movement invalidMovement = new Movement();
        invalidMovement.getId().setHostId(INVALID_HOST_ID);
        invalidMovement.getId().setEntityId(INVALID_ENTITY_ID);
        invalidMovement.getId().setMovementId(INVALID_MOVEMENT_ID);
        invalidMovement.setAmount(INVALID_AMOUNT);

        Assertions.assertThrows(SwtException.class, () -> {
            movementDAO.updateMovementDetails(invalidMovement);
        }, "Should throw exception for invalid data");
    }

    @Test
    void testGetAllMovement() throws SwtException {
        List<Movement> result = movementDAO.getAllMovement(HOST_ID, ENTITY_ID, MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetAllMovementWithInvalidData() throws SwtException {
        List<Movement> result = movementDAO.getAllMovement(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovementMessageId() throws SwtException {
        Collection<MovementMessage> result = movementDAO.getMovementMessageId(HOST_ID, MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMovementMessageIdWithInvalidData() throws SwtException {
        Collection<MovementMessage> result = movementDAO.getMovementMessageId(INVALID_HOST_ID, INVALID_MOVEMENT_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovMessageColl() throws SwtException {
        Collection<MovementMessage> result = movementDAO.getMovMessageColl(MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetMovMessageCollWithInvalidData() throws SwtException {
        Collection<MovementMessage> result = movementDAO.getMovMessageColl(INVALID_MOVEMENT_ID.toString());
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testGetMovementsForMatch() throws SwtException {
        Collection<Movement> result = movementDAO.getMovementsForMatch(HOST_ID, ENTITY_ID, MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMovementsForMatchWithInvalidData() throws SwtException {
        Collection<Movement> result = movementDAO.getMovementsForMatch(INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_MATCH_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testCheckMovementStatus() throws SwtException {
        boolean result = movementDAO.checkMovementStatus(MOVEMENT_ID.toString(), ENTITY_ID, HOST_ID);
        Assertions.assertTrue(result, "Should return true for valid movement status");
    }

    @Test
    void testCheckMovementStatusWithInvalidData() throws SwtException {
        boolean result = movementDAO.checkMovementStatus(INVALID_MOVEMENT_ID.toString(), INVALID_ENTITY_ID, INVALID_HOST_ID);
        Assertions.assertFalse(result, "Should return false for invalid movement status");
    }

    @Test
    void testGetMovementWithEmptyIds() throws SwtException {
        List<Movement> result = movementDAO.getMovement(HOST_ID, ENTITY_ID, "''");
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for empty movement IDs");
    }

    @Test
    void testGetMovementWithMultipleIds() throws SwtException {
        String movementIds = MOVEMENT_ID + ",999999";
        List<Movement> result = movementDAO.getMovement(HOST_ID, ENTITY_ID, movementIds);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetMovementWithNullIds() throws SwtException {
        Assertions.assertThrows(SwtException.class, () -> {
            movementDAO.getMovement(HOST_ID, ENTITY_ID, (String)null);
        }, "Should throw exception for null movement IDs");
    }

    @Test
    void testGetMovementWithMalformedIds() throws SwtException {
        Assertions.assertThrows(SwtException.class, () -> {
            movementDAO.getMovement(HOST_ID, ENTITY_ID, "not-a-number");
        }, "Should throw exception for malformed movement IDs");
    }
} 