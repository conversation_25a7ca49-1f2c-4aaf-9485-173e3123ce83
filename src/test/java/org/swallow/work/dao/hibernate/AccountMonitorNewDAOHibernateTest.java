package org.swallow.work.dao.hibernate;

import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import config.BaseDAOIntegrationTest;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.AcctMaintenanceDAOHibernate;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.service.AccountMonitorNewDetailVO;

@Transactional
public class AccountMonitorNewDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private AccountMonitorNewDAOHibernate accountMonitorNewDAO;

    @Autowired
    private AcctMaintenanceDAOHibernate acctMaintenanceDAOHibernate;

    // Test constants for valid data
    private static final String ACCOUNT_TYPE = "N";

    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID = "390880744EUR";
    private static final String ROLE_ID = "VendorSuppor";

    // Test constants for invalid data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "XXX";
    private static final String INVALID_ACCOUNT_ID = "INVALID_ACCT";

    private Date testDate;
    private OpTimer opTimer;
    private SystemFormats systemFormats;

    @BeforeEach
    public void setUp() throws Exception {
        testDate = new Date();
        opTimer = new OpTimer();
        systemFormats = new SystemFormats();
    }

    @Test
    public void testGetAccountAndLinkedAccountsList() throws SwtException {
        Collection<AcctMaintenance> accounts = accountMonitorNewDAO.getAccountAndLinkedAccountsList(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID);
        
        assertNotNull(accounts, "Account list should not be null");
        assertFalse(accounts.isEmpty(), "Account list should not be empty");
        
        for (AcctMaintenance account : accounts) {
            assertNotNull(account.getId(), "Account ID should not be null");
            assertNotNull(account.getId().getHostId(), "Host ID should not be null");
            assertNotNull(account.getId().getEntityId(), "Entity ID should not be null");
        }
    }

    @Test
    public void testGetAccountAndLinkedAccountsListWithInvalidData() {
        assertThrows(SwtException.class, () -> {
            accountMonitorNewDAO.getAccountAndLinkedAccountsList(
                INVALID_HOST_ID, INVALID_ENTITY_ID, INVALID_CURRENCY_CODE, INVALID_ACCOUNT_ID);
        });
    }

    @Test
    public void testUpdateMonitorSum() throws SwtException {
        Collection<AcctMaintenance> accounts = new ArrayList<>();
        AcctMaintenance acc = acctMaintenanceDAOHibernate.getEditableData(HOST_ID , ENTITY_ID, ACCOUNT_ID);
        accounts.add(acc);
        accountMonitorNewDAO.updateMonitorSum(accounts);

        // Verify the update by retrieving the account again
        Collection<AcctMaintenance> updatedAccounts = accountMonitorNewDAO.getAccountAndLinkedAccountsList(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID);
        assertFalse(updatedAccounts.isEmpty(), "Updated accounts list should not be empty");
    }

    @Test
    public void testUpdateMonitorSumWithInvalidData() {
        Collection<AcctMaintenance> accounts = new ArrayList<>();
        AcctMaintenance account = new AcctMaintenance();
        account.getId().setHostId(INVALID_HOST_ID);
        account.getId().setEntityId(INVALID_ENTITY_ID);
        account.getId().setAccountId(INVALID_ACCOUNT_ID);
        accounts.add(account);

        assertThrows(SwtException.class, () -> {
            accountMonitorNewDAO.updateMonitorSum(accounts);
        });
    }

    @Test
    public void testUpdateLoroToPredictedFlag() throws SwtException {
        AcctMaintenance account = acctMaintenanceDAOHibernate.getEditableData(HOST_ID , ENTITY_ID, ACCOUNT_ID);

        accountMonitorNewDAO.updateLoroToPredictedFlag(account);

        // Verify the update by retrieving the account again
        Collection<AcctMaintenance> updatedAccounts = accountMonitorNewDAO.getAccountAndLinkedAccountsList(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_ID);
        assertFalse(updatedAccounts.isEmpty(), "Updated accounts list should not be empty");
    }

    @Test
    public void testUpdateLoroToPredictedFlagWithInvalidData() {
        AcctMaintenance account = new AcctMaintenance();
        account.getId().setHostId(INVALID_HOST_ID);
        account.getId().setEntityId(INVALID_ENTITY_ID);
        account.getId().setAccountId(INVALID_ACCOUNT_ID);

        assertThrows(SwtException.class, () -> {
            accountMonitorNewDAO.updateLoroToPredictedFlag(account);
        });
    }

    @Test
    public void testGetAllBalancesUsingStoredProc() throws SwtException {
        AccountMonitorNewDetailVO result = accountMonitorNewDAO.getAllBalancesUsingStoredProc(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_TYPE, testDate, true,
            "Y", "ALL", "N", ROLE_ID);

        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getSummaryDetails(), "Summary details should not be null");
        assertNotNull(result.getTotalDetails(), "Total details should not be null");
        assertNotNull(result.getJobFlag(), "Job flag should not be null");
        assertNotNull(result.getWeekends(), "Weekends list should not be null");
    }


    @Test
    public void testGetAllBalancesUsingStoredProcWithCacheSearch() throws SwtException {
        AccountMonitorNewDetailVO result = accountMonitorNewDAO.getAllBalancesUsingStoredProc(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_TYPE, testDate, true,
            "Y", "ALL", "N", ROLE_ID);

        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getSummaryDetails(), "Summary details should not be null");
    }

    @Test
    public void testGetAllBalancesUsingStoredProcWithoutCacheSearch() throws SwtException {
        AccountMonitorNewDetailVO result = accountMonitorNewDAO.getAllBalancesUsingStoredProc(
            HOST_ID, ENTITY_ID, CURRENCY_CODE, ACCOUNT_TYPE, testDate, false,
            "Y", "ALL", "N", ROLE_ID);

        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getSummaryDetails(), "Summary details should not be null");
    }
}