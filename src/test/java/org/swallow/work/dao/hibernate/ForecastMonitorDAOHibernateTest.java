package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.ForecastMonitorDAO;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.web.form.ForecastRecord;

import java.math.BigDecimal;
import java.util.*;

public class ForecastMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private ForecastMonitorDAO forecastMonitorDAO;

    // Test constants
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String USER_ID = "User15";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String TEMPLATE_ID = "*DEFAULT*";
    private static final String TEMPLATE_ID_2 = "3";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURR";
    private static final String INVALID_USER_ID = "INVALID_USER";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";
    private static final String INVALID_TEMPLATE_ID = "INVALID_TEMPLATE";

    private OpTimer testOpTimer;
    private Date testDate;
    private SystemFormats testFormat;
    private ScenarioData testScenarioData;
    private AssumptionData testAssumptionData;
    private AssumptionData testAssumptionData2;
    @BeforeEach
    void setUp() {
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Set up OpTimer
        testOpTimer = new OpTimer();

        // Set up SystemFormats
        testFormat = new SystemFormats();
        testFormat.setDateFormatValue("yyyy-MM-dd");
        testFormat.setCurrencyFormat("currencyPat2");

        // Set up test ScenarioData
        testScenarioData = new ScenarioData();
        ScenarioData.Id scenarioId = new ScenarioData.Id();
        scenarioId.setHostId(HOST_ID);
        scenarioId.setEntityId(ENTITY_ID);
        scenarioId.setCurrencyCode(CURRENCY_CODE);
        scenarioId.setUserId(USER_ID);
        scenarioId.setTemplateId(TEMPLATE_ID);
        scenarioId.setValueDate(testDate);
        testScenarioData.setId(scenarioId);

        // Set up test AssumptionData
        testAssumptionData = new AssumptionData();
        testAssumptionData.setHostId(HOST_ID);
        testAssumptionData.setEntityId(ENTITY_ID);
        testAssumptionData.setCurrencyCode(CURRENCY_CODE);
        testAssumptionData.setTemplateId(TEMPLATE_ID);
        testAssumptionData.setValueDate(testDate);
        testAssumptionData.setAssumption("Test Assumption");
        testAssumptionData.setAssumptionsAmount(new BigDecimal("1000.00"));


        // Set up test AssumptionData
        testAssumptionData2 = new AssumptionData();
        testAssumptionData2.setHostId(HOST_ID);
        testAssumptionData2.setEntityId(ENTITY_ID);
        testAssumptionData2.setCurrencyCode(CURRENCY_CODE);
        testAssumptionData2.setTemplateId(TEMPLATE_ID_2);
        testAssumptionData2.setValueDate(testDate);
        testAssumptionData2.setAssumption("Test Assumption2");
        testAssumptionData2.setAssumptionsAmount(new BigDecimal("1000.00"));
    }

    @Test
    void testGetAllBalancesUsingStoredProc() throws SwtException {
        ForecastRecord result = forecastMonitorDAO.getAllBalancesUsingStoredProc(
            testOpTimer, CURRENCY_CODE, ENTITY_ID, USER_ID, testFormat, true);
        
        Assertions.assertNotNull(result, "Result should not be null");
    }


    @Test
    void testGetTemplateId() throws SwtException {
        String result = forecastMonitorDAO.getTemplateId(testScenarioData);
        Assertions.assertNotNull(result, "Template ID should not be null");
    }


    @Test
    void testGetForecastTemplate() throws SwtException {
        Collection<ForecastMonitorTemplate> result = forecastMonitorDAO.getForecastTemplate(HOST_ID, USER_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetForecastTemplateWithInvalidData() throws SwtException {
        Collection<ForecastMonitorTemplate> result = forecastMonitorDAO.getForecastTemplate(INVALID_HOST_ID, INVALID_USER_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testCheckEntityAccess() throws SwtException {
        boolean result = forecastMonitorDAO.checkEntityAccess(HOST_ID, ROLE_ID, ENTITY_ID);
        Assertions.assertTrue(result, "Should have access with valid data");
    }

    @Test
    void testCheckEntityAccessWithInvalidData() throws SwtException {
        boolean result = forecastMonitorDAO.checkEntityAccess(INVALID_HOST_ID, INVALID_ROLE_ID, INVALID_ENTITY_ID);
        Assertions.assertFalse(result, "Should not have access with invalid data");
    }

    @Test
    void testGetForecastAssumption() throws SwtException {
        Collection<AssumptionData> result = forecastMonitorDAO.getForecastAssumption(testAssumptionData);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetForecastAssumptionWithAllEntity() throws SwtException {
        testAssumptionData.setEntityId("All");
        Collection<AssumptionData> result = forecastMonitorDAO.getForecastAssumption(testAssumptionData);
        Assertions.assertNotNull(result, "Result should not be null for 'All' entity");
    }

    @Test
    void testGetForecastAssumptionWithInvalidData() throws SwtException {
        AssumptionData invalidAssumption = new AssumptionData();
        invalidAssumption.setHostId(INVALID_HOST_ID);
        invalidAssumption.setEntityId(INVALID_ENTITY_ID);
        invalidAssumption.setCurrencyCode(INVALID_CURRENCY_CODE);
        invalidAssumption.setTemplateId(INVALID_TEMPLATE_ID);

        Collection<AssumptionData> result = forecastMonitorDAO.getForecastAssumption(invalidAssumption);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    @Transactional
    void testSaveForecastAssumption() throws SwtException {
        forecastMonitorDAO.saveForecastAssumption(testAssumptionData2);

        // Verify by retrieving
        Collection<AssumptionData> result = forecastMonitorDAO.getForecastAssumption(testAssumptionData2);
        Assertions.assertFalse(result.isEmpty(), "Saved assumption should be retrievable");
        
        AssumptionData retrieved = result.iterator().next();
        Assertions.assertEquals(testAssumptionData2.getAssumption(), retrieved.getAssumption(),
            "Assumption should match");
        Assertions.assertEquals(testAssumptionData2.getAssumptionsAmount().doubleValue(), retrieved.getAssumptionsAmount().doubleValue(),
            "Amount should match");
    }

    @Test
    @Transactional
    void testUpdateForecastAssumption() throws SwtException {
        // First save
        forecastMonitorDAO.saveForecastAssumption(testAssumptionData);
        
        // Update
        testAssumptionData.setAssumption("Updated Assumption");
        testAssumptionData.setAssumptionsAmount(new BigDecimal("2000.00"));
        forecastMonitorDAO.saveForecastAssumption(testAssumptionData);
        
        // Verify update
        Collection<AssumptionData> result = forecastMonitorDAO.getForecastAssumption(testAssumptionData);
        AssumptionData updated = result.iterator().next();
        Assertions.assertEquals("Updated Assumption", updated.getAssumption(), 
            "Assumption should be updated");
        Assertions.assertEquals(new BigDecimal("2000"), updated.getAssumptionsAmount(),
            "Amount should be updated");

        forecastMonitorDAO.deleteForecastAssumption(testAssumptionData);
    }

    @Test
    void testGetForecastMonitorTemplateDetails() throws SwtException {
        ForecastMonitorTemplate template = new ForecastMonitorTemplate();
        ForecastMonitorTemplate.Id id = new ForecastMonitorTemplate.Id();
        id.setHostId(HOST_ID);
        id.setTemplateId(TEMPLATE_ID);
        template.setId(id);

        ForecastMonitorTemplate result = forecastMonitorDAO.getForecastMonitorTemplateDetails(template);
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetForecastMonitorTemplateDetailsWithInvalidData() throws SwtException {
        ForecastMonitorTemplate template = new ForecastMonitorTemplate();
        ForecastMonitorTemplate.Id id = new ForecastMonitorTemplate.Id();
        id.setHostId(INVALID_HOST_ID);
        id.setTemplateId(INVALID_TEMPLATE_ID);
        template.setId(id);

        ForecastMonitorTemplate result = forecastMonitorDAO.getForecastMonitorTemplateDetails(template);
        Assertions.assertNull(result, "Result should be null for invalid data");
    }
} 