package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.NotesDAO;
import org.swallow.work.dao.SweepDetailDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepAmount;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.model.SweepNote;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.maintenance.model.AcctMaintenance;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class SweepDetailDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepDetailDAO sweepDetailDAO;

    @Autowired
    private NotesDAO notesDAO;

    // Constants for test data based on actual record
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String SWEEP_GROUP_ID = " ";
    private static final String MOVEMENT_ID_CR = "2080564";
    private static final String MOVEMENT_ID_DR = "2080565";
    private static final Double ORIGINAL_SWEEP_AMT = 3615811.27;
    private static final Double SUBMIT_SWEEP_AMT = null;
    private static final Double AUTHORIZE_SWEEP_AMT = 3615811.27;
    private static final String SWEEP_TYPE = "M";
    private static final String SWEEP_STATUS = "U";
    private static final String INPUT_USER = "User8";
    private static final String SUBMIT_USER = null;
    private static final String AUTHORIZED_USER = "User7";
    private static final String CANCEL_USER = null;
    private static final String UPDATE_USER = "User7";
    private static final String ACCOUNT_ID_CR = "390880744EUR";
    private static final String ACCOUNT_ID_DR = "390880531EUR";
    private static final String ENTITY_ID_CR = "RABONL2U";
    private static final String ENTITY_ID_DR = "RABONL2U";
    private static final String SWEEP_UETR1 = "2c21915d-b435-4827-8988-9f0845620128";
    private static final String SWEEP_UETR2 = "a0cbe474-0a69-4b69-b71c-7caf457f71bd";
    private static final Long TEST_SWEEP_ID = 10211L;

    private Date testDate;
    private Date inputDateTime;
    private Date authorizedDateTime;
    private Date updateDate;
    private Date valueDate;
    private Sweep testSweep;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 10, 21, 36);
        cal.set(Calendar.MILLISECOND, 0);
        inputDateTime = cal.getTime();

        cal.set(2008, Calendar.OCTOBER, 1, 15, 39, 18);
        authorizedDateTime = cal.getTime();
        updateDate = cal.getTime();

        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        valueDate = cal.getTime();

        // Initialize test sweep
        testSweep = new Sweep();
        testSweep.getId().setHostId(HOST_ID);
        testSweep.getId().setSweepId(TEST_SWEEP_ID);
        testSweep.setSweepGroupId(SWEEP_GROUP_ID);
        testSweep.setCurrencyCode(CURRENCY_CODE);
        testSweep.setMovementIdCr(MOVEMENT_ID_CR);
        testSweep.setMovementIdDr(MOVEMENT_ID_DR);
        testSweep.setOriginalSweepAmt(ORIGINAL_SWEEP_AMT);
        testSweep.setSubmitSweepAmt(SUBMIT_SWEEP_AMT);
        testSweep.setAuthorizeSweepAmt(AUTHORIZE_SWEEP_AMT);
        testSweep.setSweepType(SWEEP_TYPE);
        testSweep.setSweepStatus(SWEEP_STATUS);
        testSweep.setInputDateTime(inputDateTime);
        testSweep.setSubmitDateTime(null);
        testSweep.setAuthDateTime(authorizedDateTime);
        testSweep.setCancelDateTime(null);
        testSweep.setValueDate(valueDate);
        testSweep.setUpdateDate(updateDate);
        testSweep.setInputUser(INPUT_USER);
        testSweep.setSubmitUser(SUBMIT_USER);
        testSweep.setAuthorizedUser(AUTHORIZED_USER);
        testSweep.setCancelUser(CANCEL_USER);
        testSweep.setUpdateUser(UPDATE_USER);
        testSweep.setAccountIdCr(ACCOUNT_ID_CR);
        testSweep.setAccountIdDr(ACCOUNT_ID_DR);
        testSweep.setEntityIdCr(ENTITY_ID_CR);
        testSweep.setEntityIdDr(ENTITY_ID_DR);
        testSweep.setSweepUetr1(SWEEP_UETR1);
        testSweep.setSweepUetr2(SWEEP_UETR2);
    }

    @Test
    void testGetSweepDetails() throws SwtException {
        List<Sweep> result = sweepDetailDAO.getSweepDetails(ENTITY_ID, HOST_ID, TEST_SWEEP_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        Sweep sweep = result.get(0);
        Assertions.assertEquals(HOST_ID, sweep.getId().getHostId());
        Assertions.assertEquals(TEST_SWEEP_ID, sweep.getId().getSweepId());
        Assertions.assertEquals(SWEEP_GROUP_ID, sweep.getSweepGroupId());
        Assertions.assertEquals(CURRENCY_CODE, sweep.getCurrencyCode());
        Assertions.assertEquals(MOVEMENT_ID_CR, sweep.getMovementIdCr());
        Assertions.assertEquals(MOVEMENT_ID_DR, sweep.getMovementIdDr());
        Assertions.assertEquals(ORIGINAL_SWEEP_AMT, sweep.getOriginalSweepAmt());
        Assertions.assertEquals(SUBMIT_SWEEP_AMT, sweep.getSubmitSweepAmt());
        Assertions.assertEquals(AUTHORIZE_SWEEP_AMT, sweep.getAuthorizeSweepAmt());
        Assertions.assertEquals(SWEEP_TYPE, sweep.getSweepType());
        Assertions.assertEquals(SWEEP_STATUS, sweep.getSweepStatus());
        Assertions.assertEquals(INPUT_USER, sweep.getInputUser());
        Assertions.assertEquals(SUBMIT_USER, sweep.getSubmitUser());
        Assertions.assertEquals(AUTHORIZED_USER, sweep.getAuthorizedUser());
        Assertions.assertEquals(CANCEL_USER, sweep.getCancelUser());
        Assertions.assertEquals(UPDATE_USER, sweep.getUpdateUser());
        Assertions.assertEquals(ACCOUNT_ID_CR, sweep.getAccountIdCr());
        Assertions.assertEquals(ACCOUNT_ID_DR, sweep.getAccountIdDr());
        Assertions.assertEquals(ENTITY_ID_CR, sweep.getEntityIdCr());
        Assertions.assertEquals(ENTITY_ID_DR, sweep.getEntityIdDr());
        Assertions.assertEquals(SWEEP_UETR1, sweep.getSweepUetr1());
        Assertions.assertEquals(SWEEP_UETR2, sweep.getSweepUetr2());
    }

    @Test
    void testGetSweepDetailsArchive() throws SwtException {
        List<Sweep> result = sweepDetailDAO.getSweepDetailsArchive(ENTITY_ID, HOST_ID, TEST_SWEEP_ID, "PREDICT_ARCH");
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetAccountDetails() throws SwtException {
        List<AcctMaintenance> result = sweepDetailDAO.getAccountDetails(ENTITY_ID, HOST_ID, ACCOUNT_ID_CR);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        AcctMaintenance account = result.get(0);
        Assertions.assertEquals(HOST_ID, account.getId().getHostId());
        Assertions.assertEquals(ENTITY_ID, account.getId().getEntityId());
    }

    @Test
    void testGetSweepNoteDetails() throws SwtException {
        Collection<SweepNote> result = notesDAO.getSweepNoteDetails(HOST_ID, TEST_SWEEP_ID, null);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetSweepNoteDetailsArchive() throws SwtException {
        Collection<SweepNote> result = notesDAO.getSweepNoteDetails(HOST_ID, TEST_SWEEP_ID, "PREDICT_ARCH");
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetSweepDetailsWithInvalidData() throws SwtException {
        List<Sweep> result = sweepDetailDAO.getSweepDetails(ENTITY_ID, HOST_ID, 999L);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetAccountDetailsWithInvalidData() throws SwtException {
        List<AcctMaintenance> result = sweepDetailDAO.getAccountDetails(ENTITY_ID, HOST_ID, "INVALID_ACC");
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetSweepNoteDetailsWithInvalidData() throws SwtException {
        Collection<SweepNote> result = notesDAO.getSweepNoteDetails(HOST_ID, 999L, null);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }
} 