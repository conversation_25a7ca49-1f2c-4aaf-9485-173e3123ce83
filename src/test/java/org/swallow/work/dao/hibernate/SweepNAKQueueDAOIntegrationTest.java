package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.SweepNAKQueueDAO;
import org.swallow.work.model.SweepNAKQueue;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class SweepNAKQueueDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepNAKQueueDAO sweepNAKQueueDAO;

    // Constants for test data based on actual record
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String CURRENCY_GRP_ID = "EUR";

    private Date testDate;
    private SystemFormats systemFormats;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize system formats
        systemFormats = new SystemFormats();
        systemFormats.setDateFormatValue("yyyy-MM-dd");
    }

    @Test
    void testGetNAKQueueDetails() throws SwtException {
        String result = sweepNAKQueueDAO.getNAKQueueDetails(ENTITY_ID, CURRENCY_CODE, testDate);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetNAKMessages() throws SwtException {
        Collection result = sweepNAKQueueDAO.getNAKMessages(ENTITY_ID, CURRENCY_CODE, testDate);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetOverdueACKMessages() throws SwtException {
        Collection result = sweepNAKQueueDAO.getOverdueACKMessages(ENTITY_ID, CURRENCY_CODE, testDate);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetNAKQueueDetailsFromProc() throws SwtException {
        Collection<SweepNAKQueue> result = sweepNAKQueueDAO.getNAKQueueDetailsFromProc(
            HOST_ID,
            ENTITY_ID,
            CURRENCY_GRP_ID,
            ROLE_ID,
            testDate
        );

        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        SweepNAKQueue sweepNAKQueue = (SweepNAKQueue) result.iterator().next();
        Assertions.assertEquals(CURRENCY_CODE, sweepNAKQueue.getCurrencyCode());
        Assertions.assertNotNull(sweepNAKQueue.getCurrencyName());
        Assertions.assertNotNull(sweepNAKQueue.getNAKs());
        Assertions.assertNotNull(sweepNAKQueue.getOverdueACKs());
    }

    @Test
    void testGetNAKQueueDetailsWithInvalidData() throws SwtException {
        String result = sweepNAKQueueDAO.getNAKQueueDetails("INVALID_ENTITY", "INVALID_CURRENCY", testDate);
        Assertions.assertNotNull(result);
    }

    @Test
    void testGetNAKMessagesWithInvalidData() throws SwtException {
        Collection result = sweepNAKQueueDAO.getNAKMessages("INVALID_ENTITY", "INVALID_CURRENCY", testDate);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetOverdueACKMessagesWithInvalidData() throws SwtException {
        Collection result = sweepNAKQueueDAO.getOverdueACKMessages("INVALID_ENTITY", "INVALID_CURRENCY", testDate);
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetNAKQueueDetailsFromProcWithInvalidData() throws SwtException {
        Collection<SweepNAKQueue> result = sweepNAKQueueDAO.getNAKQueueDetailsFromProc(
            "INVALID_HOST",
            "INVALID_ENTITY",
            "INVALID_CURRENCY_GRP",
            "INVALID_ROLE",
            testDate
        );

        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }
} 