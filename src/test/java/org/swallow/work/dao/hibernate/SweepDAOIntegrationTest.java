package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.work.dao.SweepDAO;
import org.swallow.work.model.Sweep;
import org.swallow.exception.SwtException;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SystemFormats;
import org.swallow.maintenance.model.Currency;

import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class SweepDAOIntegrationTest extends BaseDAOIntegrationTest {

    @Autowired
    private SweepDAO sweepDAO;

    // Constants for test data based on actual record
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String ACCOUNT_ID_1 = "390880744EUR";
    private static final String ACCOUNT_ID_2 = "390880531EUR";
    private static final String ENTITY_ID_1 = "RABONL2U";
    private static final String ENTITY_ID_2 = "RABONL2U";
    private static final String COUNTRY_CODE = "NL"; // Expected country code for RABONL2U
    private static final String ACCOUNT_LEVEL = "S"; // Example expected level
    private static final String CUT_OFF_TIME = "17:00"; // Example expected cutoff time

    private Date testDate;
    private SystemFormats systemFormats;

    @BeforeEach
    void setUp() {
        // Set up test dates
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Initialize system formats
        systemFormats = new SystemFormats();
        systemFormats.setDateFormatValue("yyyy-MM-dd");
    }

    @Test
    void testGetCurrencyList() throws SwtException {
        List<Currency> result = sweepDAO.getCurrencyList(ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        Currency currency = result.get(0);
        Assertions.assertEquals(HOST_ID, currency.getId().getHostId());
        Assertions.assertEquals(ENTITY_ID, currency.getId().getEntityId());
        Assertions.assertNotEquals("*", currency.getId().getCurrencyCode());
    }

    @Test
    void testGetCurrencyListForAllEntities() throws SwtException {
        List<Currency> result = sweepDAO.getCurrencyList("All", HOST_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());

        Currency currency = result.get(0);
        Assertions.assertEquals(HOST_ID, currency.getId().getHostId());
        Assertions.assertNotEquals("*", currency.getId().getCurrencyCode());
    }

    @Test
    void testGetSweepStatus() throws SwtException {
        String result = sweepDAO.getSweepStatus(
            ENTITY_ID,
            ACCOUNT_ID_1,
            ACCOUNT_ID_2,
            ENTITY_ID_1,
            ENTITY_ID_2,
            "01/10/2008",
            systemFormats
        );

        Assertions.assertNotNull(result);
    }

    @Test
    void testGetCountryCode() throws SwtException {
        List result = sweepDAO.getCountryCode(ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        // Assuming the list contains LabelValueBean objects
        String countryBean = (String) result.get(0);
        Assertions.assertEquals(COUNTRY_CODE, countryBean);
    }

    @Test
    void testGetLevel() throws SwtException {
        String result = sweepDAO.getLevel(ACCOUNT_ID_1, ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(ACCOUNT_LEVEL, result);
    }

    @Test
    void testGetCutOff() throws SwtException {
        String result = sweepDAO.getCutOff(ACCOUNT_ID_1, ENTITY_ID, HOST_ID);
        Assertions.assertNotNull(result);
        Assertions.assertEquals(CUT_OFF_TIME, result);
    }

    // --- Tests with invalid data ---

    @Test
    void testGetSweepStatusWithInvalidData() throws SwtException {
        String result = sweepDAO.getSweepStatus(
            "INVALID_ENTITY",
            "INVALID_ACCOUNT_1",
            "INVALID_ACCOUNT_2",
            "INVALID_ENTITY_1",
            "INVALID_ENTITY_2",
            "2008-10-01",
            systemFormats
        );

        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetCurrencyListWithInvalidData() throws SwtException {
        List<Currency> result = sweepDAO.getCurrencyList("INVALID_ENTITY", "INVALID_HOST");
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetCountryCodeWithInvalidData() throws SwtException {
        List result = sweepDAO.getCountryCode("INVALID_ENTITY", "INVALID_HOST");
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    void testGetLevelWithInvalidData() throws SwtException {
        String result = sweepDAO.getLevel("INVALID_ACCOUNT", "INVALID_ENTITY", "INVALID_HOST");
        Assertions.assertNull(result);
    }

    @Test
    void testGetCutOffWithInvalidData() throws SwtException {
        String result = sweepDAO.getCutOff("INVALID_ACCOUNT", "INVALID_ENTITY", "INVALID_HOST");
        Assertions.assertNull(result);
    }
} 