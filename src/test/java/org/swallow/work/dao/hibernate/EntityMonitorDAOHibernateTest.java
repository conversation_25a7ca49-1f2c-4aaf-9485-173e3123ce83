package org.swallow.work.dao.hibernate;

import config.BaseDAOIntegrationTest;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.EntityMonitorDAO;
import org.swallow.work.model.PersonalEntityList;
import org.swallow.work.model.PersonalEntityListExt;
import org.swallow.work.web.form.EntityRecord;

import java.math.BigDecimal;
import java.util.*;

public class EntityMonitorDAOHibernateTest extends BaseDAOIntegrationTest {

    @Autowired
    private EntityMonitorDAO entityMonitorDAO;

    // Test constants
    private static final String HOST_ID = "RABO";
    private static final String ENTITY_ID = "RABONL2U";
    private static final String CURRENCY_CODE = "EUR";
    private static final String USER_ID = "User15";
    private static final String ROLE_ID = "VendorSuppor";
    private static final String SCREEN_ID = SwtConstants.ENTITY_MONITOR_ID;;
    private static final String CURRENCY_GROUP = "All";

    // Invalid test data
    private static final String INVALID_HOST_ID = "INVALID_HOST";
    private static final String INVALID_ENTITY_ID = "INVALID_ENTITY";
    private static final String INVALID_CURRENCY_CODE = "INVALID_CURR";
    private static final String INVALID_USER_ID = "INVALID_USER";
    private static final String INVALID_ROLE_ID = "INVALID_ROLE";
    private static final String INVALID_SCREEN_ID = "INVALID_SCREEN";

    private OpTimer testOpTimer;
    private Date testDate;
    private SystemFormats testFormat;
    private PersonalEntityList testPersonalEntityList;
    private String testEntityOffsetTime;

    @BeforeEach
    void setUp() {
        // Set up test date
        Calendar cal = Calendar.getInstance();
        cal.set(2008, Calendar.OCTOBER, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        testDate = cal.getTime();

        // Set up OpTimer
        testOpTimer = new OpTimer();

        // Set up SystemFormats
        testFormat = new SystemFormats();
        testFormat.setDateFormatValue("yyyy-MM-dd");
        testFormat.setCurrencyFormat("currencyPat2");

        // Set up test PersonalEntityList
        testPersonalEntityList = new PersonalEntityList();
        PersonalEntityList.Id id = new PersonalEntityList.Id();
        id.setHostId(HOST_ID);
        id.setEntityId(ENTITY_ID);
        id.setUserId(USER_ID);
        id.setScreenId(SCREEN_ID);
        id.setRoleId(ROLE_ID);
        testPersonalEntityList.setId(id);
        testPersonalEntityList.setYesDisplay(SwtConstants.YES);
        testPersonalEntityList.setDisplayDays(5);
        testPersonalEntityList.setPriorityOrder(1);
        testPersonalEntityList.setYesAggrgEntity(SwtConstants.NO);
        testPersonalEntityList.setSaveorupdate("save");
        // Set up entity offset time
        testEntityOffsetTime = "Y";
    }

    @Test
    void testGetAllBalancesUsingStoredProc() throws SwtException {
        EntityRecord result = entityMonitorDAO.getAllBalancesUsingStoredProc(
            testOpTimer, CURRENCY_GROUP, USER_ID, testDate, testFormat, null, testEntityOffsetTime);
        
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPersonalEntityList() throws SwtException {
        Collection<PersonalEntityList> result = entityMonitorDAO.getPersonalEntityList(
            USER_ID, SCREEN_ID, HOST_ID);
        
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    void testGetPersonalEntityListWithAggrEntity() throws SwtException {
        Collection<PersonalEntityList> result = entityMonitorDAO.getPersonalEntityList(
            USER_ID, SCREEN_ID, HOST_ID, ENTITY_ID);
        
        Assertions.assertNotNull(result, "Result should not be null");
    }

    @Test
    @Transactional
    void testSaveAndFetchRecord() throws SwtException {
        // First save
        entityMonitorDAO.deleteSumRecord(
            HOST_ID, USER_ID, SCREEN_ID, ENTITY_ID); // Clean

        entityMonitorDAO.saveRecord(testPersonalEntityList);

        // Then fetch and verify
        PersonalEntityList result = entityMonitorDAO.fetchRecord(testPersonalEntityList);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(testPersonalEntityList.getId().getEntityId(), result.getId().getEntityId(),
            "Entity ID should match");
        Assertions.assertEquals(testPersonalEntityList.getYesDisplay(), result.getYesDisplay(),
            "Display flag should match");
        Assertions.assertEquals(testPersonalEntityList.getDisplayDays(), result.getDisplayDays(),
            "Display days should match");
    }

    @Test
    void testGetUserEntityList() throws SwtException {
        Collection<EntityAccess> result = entityMonitorDAO.getUserEntityList(HOST_ID, ROLE_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertFalse(result.isEmpty(), "Result should not be empty");
    }

    @Test
    void testGetUserEntityListWithInvalidData() throws SwtException {
        Collection<EntityAccess> result = entityMonitorDAO.getUserEntityList(INVALID_HOST_ID, INVALID_ROLE_ID);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertTrue(result.isEmpty(), "Result should be empty for invalid data");
    }

    @Test
    void testFetchSumRecord() throws SwtException {
        testPersonalEntityList.setYesAggrgEntity(SwtConstants.YES);
        PersonalEntityList result = entityMonitorDAO.fetchSumRecord(testPersonalEntityList);
        
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(testPersonalEntityList.getId().getEntityId(), result.getId().getEntityId(),
            "Entity ID should match");
    }

    @Test
    @Transactional
    void testSaveSumRecord() throws SwtException {
        testPersonalEntityList.setYesAggrgEntity(SwtConstants.YES);
        String sumEntities = "RABONL2U,RABOGB2L";
        entityMonitorDAO.deleteSumRecord(
            HOST_ID, USER_ID, SCREEN_ID, sumEntities); // Clean up any existing records
        entityMonitorDAO.saveSumRecord(testPersonalEntityList, sumEntities);
        
        // Verify by fetching
        PersonalEntityList result = entityMonitorDAO.fetchSumRecord(testPersonalEntityList);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(SwtConstants.YES, result.getYesAggrgEntity(),
            "Should be marked as aggregate entity");
        entityMonitorDAO.deleteSumRecord(
            HOST_ID, USER_ID, SCREEN_ID, sumEntities); // Clean  ;
    }

    @Test
    @Transactional
    void testDeleteSumRecord() throws SwtException {
        // First save a sum record
        testPersonalEntityList.setYesAggrgEntity(SwtConstants.YES);
        String sumEntities = "RABONL2U,RABOGB2L";
        try {

            entityMonitorDAO.saveSumRecord(testPersonalEntityList, sumEntities);
        }catch ( Exception e) {

        }
        // Handle exception

        // Then delete it
        boolean result = entityMonitorDAO.deleteSumRecord(
            HOST_ID, USER_ID, SCREEN_ID, ENTITY_ID);
        
        Assertions.assertTrue(result, "Delete operation should succeed");
        
        // Verify deletion
        PersonalEntityList fetched = entityMonitorDAO.fetchSumRecord(testPersonalEntityList);
        Assertions.assertEquals("save", fetched.getSaveorupdate(),
            "Record should be marked for save after deletion");
    }

    @Test
    void testFetchRecordWithInvalidData() throws SwtException {
        PersonalEntityList invalidList = new PersonalEntityList();
        PersonalEntityList.Id invalidId = new PersonalEntityList.Id();
        invalidId.setHostId(INVALID_HOST_ID);
        invalidId.setEntityId(INVALID_ENTITY_ID);
        invalidId.setUserId(INVALID_USER_ID);
        invalidId.setScreenId(INVALID_SCREEN_ID);
        invalidId.setRoleId(INVALID_ROLE_ID);
        invalidList.setId(invalidId);

        PersonalEntityList result = entityMonitorDAO.fetchRecord(invalidList);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals("save", result.getSaveorupdate(),
            "Invalid record should be marked for save");
    }

    @Test
    void testFetchSumRecordWithInvalidData() throws SwtException {
        PersonalEntityList invalidList = new PersonalEntityList();
        PersonalEntityList.Id invalidId = new PersonalEntityList.Id();
        invalidId.setHostId(INVALID_HOST_ID);
        invalidId.setEntityId(INVALID_ENTITY_ID);
        invalidId.setUserId(INVALID_USER_ID);
        invalidId.setScreenId(INVALID_SCREEN_ID);
        invalidList.setId(invalidId);

        PersonalEntityList result = entityMonitorDAO.fetchSumRecord(invalidList);
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals("save", result.getSaveorupdate(),
            "Invalid record should be marked for save");
    }

    @Test
    @Transactional
    void testSaveRecordWithUpdate() throws SwtException {
        // First save
        entityMonitorDAO.deleteSumRecord(
            HOST_ID, USER_ID, SCREEN_ID, ENTITY_ID); // Clean;
        testPersonalEntityList.setSaveorupdate("update");
        entityMonitorDAO.saveRecord(testPersonalEntityList);

        // Update
        testPersonalEntityList.setDisplayDays(10);
        testPersonalEntityList.setPriorityOrder(2);
        entityMonitorDAO.saveRecord(testPersonalEntityList);

        // Verify update
        PersonalEntityList result = entityMonitorDAO.fetchRecord(testPersonalEntityList);
        Assertions.assertEquals(10, result.getDisplayDays(),
            "Display days should be updated");
        Assertions.assertEquals(2, result.getPriorityOrder(),
            "Priority order should be updated");
    }
} 