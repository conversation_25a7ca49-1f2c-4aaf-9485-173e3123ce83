package config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.swallow.security.SwtSecureBasicDataSource;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@TestConfiguration

public class HibernateTestConfig {

    @Bean
    @Primary
    public LocalSessionFactoryBean sessionFactory() {
        LocalSessionFactoryBean sessionFactory = new LocalSessionFactoryBean();
        sessionFactory.setDataSource(dataSource());
        sessionFactory.setPackagesToScan("org.swallow.model", "org.swallow.maintenance.model", "org.swallow.control.model", "org.swallow.work.model"); // Package(s) containing your entity classes
        sessionFactory.setHibernateProperties(hibernateProperties());

        // Add ALL hbm.xml files
        //sessionFactory.setMappingResources("org/swallow/maintenance/model/*.hbm.xml");
        try {
            File baseDir = new File(System.getProperty("user.dir") + "/src/main/java/org/swallow");
            List<Resource> hbmResources = new ArrayList<>();

            Files.walk(baseDir.toPath())
                    .filter(path -> path.toString().endsWith(".hbm.xml"))
                    .forEach(path -> {
                        File file = path.toFile();
                        hbmResources.add(new FileSystemResource(file));
                    });

            sessionFactory.setMappingLocations(hbmResources.toArray(new Resource[0]));
        } catch (IOException e) {
            throw new RuntimeException("I/O Error occurred while mapping HBM.XML resources, cause: "+ e.getMessage(), e);
        }


        return sessionFactory;
    }

    @Value("classpath:./connection.properties")
    private Resource resource;
    public DataSource dataSource() {
        Properties props = new Properties();
        try {
            props.load(resource.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException("Error while loading file: connection.properties, cause: "+e.getMessage(), e);
        }

        SwtSecureBasicDataSource dataSource = new SwtSecureBasicDataSource();
        dataSource.setConnectionModule("PCM");
        dataSource.setDriverClassName(props.getProperty("datasource.driver"));
        dataSource.setUrl(props.getProperty("datasource.url"));
        dataSource.setUsername(props.getProperty("datasource.username"));
        dataSource.setPassword(props.getProperty("datasource.password"));

//        dataSource.setDefaultAutoCommit(false);
        return dataSource;
    }

    private  Properties hibernateProperties() {
        Properties properties = new Properties();
        properties.put("hibernate.dialect", "org.hibernate.dialect.OracleDialect");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hbm2ddl.auto", "false");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.connection.autocommit", "false");
        properties.put("hibernate.allow_update_outside_transaction", "true");
        // Add more Hibernate properties as needed
        return properties;
    }
}