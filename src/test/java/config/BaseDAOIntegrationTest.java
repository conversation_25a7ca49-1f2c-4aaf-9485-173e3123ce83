package config;

import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.swallow.PredictApplication;

@SpringBootTest(classes = { PredictApplication.class, HibernateTestConfig.class })
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
// Optional: use a dedicated profile for integration tests (like "test" or "dev")
@ActiveProfiles("test")
public abstract class BaseDAOIntegrationTest {
    
    // You can add shared helpers, logs, or utilities here later
    protected void logStart(String testName) {
        System.out.println("🔎 Starting test: " + testName);
    }

    protected void logEnd(String testName) {
        System.out.println("✅ Finished test: " + testName);
    }
}
