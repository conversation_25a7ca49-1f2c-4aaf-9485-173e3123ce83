package config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

public class JsonTestHelper {
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(SerializationFeature.INDENT_OUTPUT);

    public static <T> void writeResult(String testClass, String method, T result) throws IOException {
        String path = "src/test/resources/test-results/" + testClass + "/" + method + ".json";
        File file = Paths.get(path).toFile();
        file.getParentFile().mkdirs();
        objectMapper.writeValue(file, result);
    }

    // Existing method
    public static <T> T readExpected(String className, String method, Class<T> type) throws IOException {
        String path = "src/test/resources/test-results/" + className + "/" + method + ".json";
        return objectMapper.readValue(Paths.get(path).toFile(), type);
    }

    public static <T> T readExpected(String className, String method, TypeReference<T> typeRef) throws IOException {
        String path = "src/test/resources/test-results/" + className + "/" + method + ".json";
        try (InputStream is = Files.newInputStream(Paths.get(path))) {
            return objectMapper.readValue(is, typeRef);
        }
    }

    public static <T> boolean isEqual(T actual, T expected) throws IOException {
        String a = objectMapper.writeValueAsString(actual);
        String b = objectMapper.writeValueAsString(expected);
        return a.equals(b);
    }
}