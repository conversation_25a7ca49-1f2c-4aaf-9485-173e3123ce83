package web.mfa;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;
import org.junit.runners.Suite.SuiteClasses;

/**
 * Test suite for MFA session mixup issue.
 * This runs all the individual test classes.
 */
@RunWith(Suite.class)
@SuiteClasses({
    MockHttpSessionTest.class,
    CommonDataManagerTest.class,
    SessionMixupSimulationTest.class,
    SessionMixupReproductionTest.class
})
public class MFASessionMixupTestSuite {
    // This class is just a test suite container
}
