package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.swallow.mfa.RegisterMFA;
import org.swallow.mfa.SamlService;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.web.LogonAction;
import org.swallow.web.LogoutAction;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * Test class that simulates session mixup scenarios
 */
public class SessionMixupSimulationTest {

    private static final Log logger = LogFactory.getLog(SessionMixupSimulationTest.class);

    // Mock objects
    @Mock private LogonManager logonManager;
    @Mock private SamlService samlService;
    @Mock private RegisterMFA registerMFA;
    @Mock private TokensProvider tokensProvider;

    // Test objects
    private LogonAction logonAction;
    private LogoutAction logoutAction;
    private Map<String, MockHttpSession> sessions;
    private Map<String, User> users;
    private Map<String, String> mfaTokens;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // Initialize test objects
        logonAction = new LogonAction();
        logoutAction = new LogoutAction();
        sessions = new HashMap<>();
        users = new HashMap<>();
        mfaTokens = new HashMap<>();

        // Set up LogonAction
        logonAction.setLogonManager(logonManager);

        // Set up LogoutAction
        logoutAction.setLogonManager(logonManager);

        // Set up RegisterMFA mock
        when(registerMFA.isUseSmartAuthenticator()).thenReturn(true);
        when(registerMFA.isAllowInternalAuthentication()).thenReturn(true);
        when(registerMFA.isAllowUserCreationWithSmartAuthenticator()).thenReturn(true);

        // Create test users
        String userId1 = "user1";
        String userId2 = "user2";
        users.put(userId1, TestUtils.createTestUser(userId1));
        users.put(userId2, TestUtils.createTestUser(userId2));
        mfaTokens.put(userId1, "mfa-token-" + userId1);
        mfaTokens.put(userId2, "mfa-token-" + userId2);

        // Set up SamlService mock
        when(samlService.verifySamlToken(anyString())).thenAnswer(new Answer<SamlUserDTO>() {
            @Override
            public SamlUserDTO answer(InvocationOnMock invocation) throws Throwable {
                String token = invocation.getArgument(0);
                List<SamlUserDTO.UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
                for (Map.Entry<String, String> entry : mfaTokens.entrySet()) {
                    if (entry.getValue().equals(token)) {
                        SamlUserDTO dto = new SamlUserDTO();
                        dto.setUsername(entry.getKey());
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_USER_ID", "SMART_USER_ID", entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_ROLE_ID", "SMART_ROLE_ID", "ROLE_" + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("FIRST_NAME", "FIRST_NAME", "User " + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("EMAIL", "EMAIL", entry.getKey() + "@example.com"));
                        dto.setAttributes(attributes);
                        return dto;
                    }
                }
                return null;
            }
        });

        // Set up LogonManager mock
        when(logonManager.getUserDetailByExtAuthId(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });

        when(logonManager.getUserDetail(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });

        // Set up TokensProvider mock
        when(tokensProvider.createToken(any(User.class), anyString())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                User user = invocation.getArgument(0);
                return "token-" + user.getId().getUserId();
            }
        });

        when(tokensProvider.validateToken(any(HttpServletRequest.class))).thenReturn(true);
    }

    @After
    public void tearDown() {
        // Clean up sessions
        for (MockHttpSession session : sessions.values()) {
            try {
                if (session.isValid()) {
                    session.invalidate();
                }
            } catch (IllegalStateException e) {
                // Session already invalidated
            }
        }
        sessions.clear();
        users.clear();
        mfaTokens.clear();

    }

    /**
     * Create a mock HttpServletRequest for MFA login
     */
    private HttpServletRequest createMfaLoginRequest(String userId, String sessionId) {
        HttpServletRequest request = mock(HttpServletRequest.class);
        MockHttpSession session = new MockHttpSession(sessionId);
        sessions.put(sessionId, session);

        when(request.getSession()).thenReturn(session);
        when(request.getSession(anyBoolean())).thenReturn(session);
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        when(request.getParameter("logonflag")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        // Set up parameters for SwtUtil.isSucessfulMFARequest
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));

        // Create parameter map for Struts
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("auth", new String[] {mfaTokens.get(userId)});
        when(request.getParameterMap()).thenReturn(parameterMap);

        return request;
    }

    /**
     * Create a mock HttpServletResponse
     */
    private HttpServletResponse createMockResponse() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        return response;
    }

    /**
     * Test scenario: Simulate session mixup by sharing CDM
     * This test deliberately tries to create conditions that might lead to session mixup
     * by sharing the same CommonDataManager instance between two sessions
     */
    @Test
    public void testSessionMixupBySharedCDM() throws Exception {
        logger.info("Starting session mixup by shared CDM test");

        // Choose two test users
        String userId1 = "user1";
        String userId2 = "user2";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);

        // First user login
        String sessionId1 = "session-1";
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();

        // Set up Struts ActionContext
        StrutsTestHelper.setupActionContext(request1, response1);

        logonAction.setUser(user1);
        String result1 = logonAction.login();

        assertEquals("First user login should succeed", "success", result1);

        // Get first user's CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);

        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());

        // Create second session but use the same CDM instance
        // This simulates a potential issue in the application
        String sessionId2 = "session-2";
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();

        // Deliberately set the same CDM in the second session
        sessions.get(sessionId2).setAttribute(SwtConstants.CDM_BEAN, cdm1);

        // Now check which user is in the CDM of session2
        CommonDataManager cdm2 = (CommonDataManager) sessions.get(sessionId2).getAttribute(SwtConstants.CDM_BEAN);

        // This should be user1's data since we're using the same CDM instance
        assertEquals("Session mixup: wrong user in session2's CDM",
                     userId1, cdm2.getUser().getId().getUserId());

        // Now try to login as user2 using the second session
        // Update ActionContext with the second request
        StrutsTestHelper.setupActionContext(request2, response2);

        logonAction.setUser(user2);
        String result2 = logonAction.login();

        // Check the result - this might succeed or fail depending on the implementation
        logger.info("Second login result: " + result2);

        // Get the CDM from both sessions after login
        CommonDataManager cdmSession1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        CommonDataManager cdmSession2 = (CommonDataManager) sessions.get(sessionId2).getAttribute(SwtConstants.CDM_BEAN);

        // Check if they are the same instance
        boolean sameCdmInstance = (cdmSession1 == cdmSession2);
        logger.info("Same CDM instance in both sessions after login: " + sameCdmInstance);

        // Check which user is in each CDM
        String userIdInCdm1 = cdmSession1.getUser().getId().getUserId();
        String userIdInCdm2 = cdmSession2.getUser().getId().getUserId();

        logger.info("User ID in session1's CDM: " + userIdInCdm1);
        logger.info("User ID in session2's CDM: " + userIdInCdm2);

        logger.info("Session mixup by shared CDM test completed");
    }

    /**
     * Test scenario: Simulate session hijacking
     * This test simulates a scenario where a user's session is "hijacked"
     * by another user, which might be similar to the reported issue
     */
    @Test
    public void testSessionHijacking() throws Exception {
        logger.info("Starting session hijacking simulation test");

        // Choose two test users
        String userId1 = "user1";
        String userId2 = "user2";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);

        // First user login
        String sessionId1 = "session-1";
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();

        // Set up Struts ActionContext
        StrutsTestHelper.setupActionContext(request1, response1);

        logonAction.setUser(user1);
        String result1 = logonAction.login();

        assertEquals("First user login should succeed", "success", result1);

        // Get first user's session and CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);

        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());

        // Simulate session hijacking: Create a new request with the same session ID
        // but for a different user
        HttpServletRequest hijackedRequest = createMfaLoginRequest(userId2, sessionId1);
        HttpServletResponse hijackedResponse = createMockResponse();

        // Use the existing session for the hijacked request
        when(hijackedRequest.getSession()).thenReturn(session1);
        when(hijackedRequest.getSession(anyBoolean())).thenReturn(session1);

        // Try to login as the second user using the hijacked session
        // Update ActionContext with the hijacked request
        StrutsTestHelper.setupActionContext(hijackedRequest, hijackedResponse);

        logonAction.setUser(user2);
        String hijackResult = logonAction.login();

        // Check the result - this might succeed or fail depending on the implementation
        logger.info("Hijack login result: " + hijackResult);

        // Get the CDM from the session after the hijack attempt
        CommonDataManager cdmAfterHijack = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);

        // Check which user is in the CDM
        String userIdInCdm = cdmAfterHijack.getUser().getId().getUserId();
        logger.info("User ID in CDM after hijack attempt: " + userIdInCdm);

        // The important thing is that we don't end up with a mixed state
        // where some parts of the session refer to user1 and others to user2

        // Verify the CDM is consistent
        User userInCdm = cdmAfterHijack.getUser();
        assertEquals("User ID and name should be consistent",
                    "User " + userInCdm.getId().getUserId(), userInCdm.getUserName());
        assertEquals("User ID and email should be consistent",
                    userInCdm.getId().getUserId() + "@example.com", userInCdm.getEmailId());

        logger.info("Session hijacking simulation test completed");
    }
}
