package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.Test;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.when;

/**
 * Test class that simulates real-world scenarios to reproduce the reported issue
 */
public class RealWorldSimulationTest extends MFASessionTestBase {
    
    /**
     * Test scenario: Simulate real-world usage pattern
     * This test simulates a realistic usage pattern with multiple users
     * logging in, performing actions, and logging out
     */
    @Test
    public void testRealWorldUsagePattern() throws Exception {
        logger.info("Starting real-world usage pattern test");
        
        final int numUsers = NUM_USERS;
        final int numActionsPerUser = 10;
        final CountDownLatch finishLatch = new CountDownLatch(numUsers);
        final AtomicBoolean errorOccurred = new AtomicBoolean(false);
        final AtomicInteger actionCounter = new AtomicInteger(0);
        
        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(numUsers);
        
        // Submit user simulation tasks
        for (int i = 0; i < numUsers; i++) {
            final String userId = "user" + i;
            final int userIndex = i;
            
            executor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Stagger user logins to simulate real-world timing
                        Thread.sleep(userIndex * 100);
                        
                        logger.info("Starting simulation for user: " + userId);
                        
                        // Login
                        String sessionId = "session-" + sessionCounter.incrementAndGet();
                        HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
                        HttpServletResponse response = createMockResponse();
                        
                        User user = users.get(userId);
                        logonAction.setUser(user);
                        
                        String loginResult = logonAction.login();
                        if (!"success".equals(loginResult)) {
                            logger.error("Login failed for user: " + userId);
                            errorOccurred.set(true);
                            return;
                        }
                        
                        // Get session and CDM
                        MockHttpSession session = sessions.get(sessionId);
                        CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                        
                        // Verify initial state
                        if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                            logger.error("Initial CDM verification failed for user: " + userId);
                            errorOccurred.set(true);
                            return;
                        }
                        
                        // Perform a series of actions
                        for (int j = 0; j < numActionsPerUser; j++) {
                            // Simulate user activity
                            Thread.sleep(50 + (int)(Math.random() * 100));
                            
                            // Access the session and verify user data
                            session.access();
                            
                            // Get the CDM again (it might have changed)
                            cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                            
                            // Verify user data is still correct
                            if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                                logger.error("CDM verification failed for user: " + userId + " after action " + j);
                                errorOccurred.set(true);
                                return;
                            }
                            
                            // Increment action counter
                            actionCounter.incrementAndGet();
                        }
                        
                        // Logout
                        String logoutResult = logoutAction.logout(request, response);
                        
                        logger.info("Completed simulation for user: " + userId);
                    } catch (Exception e) {
                        logger.error("Exception in simulation for user: " + userId, e);
                        errorOccurred.set(true);
                    } finally {
                        finishLatch.countDown();
                    }
                }
            });
        }
        
        // Wait for all simulations to finish
        finishLatch.await(60, TimeUnit.SECONDS);
        
        // Shut down executor
        executor.shutdown();
        executor.awaitTermination(5, TimeUnit.SECONDS);
        
        // Verify no errors occurred
        assertFalse("No errors should occur during real-world simulation", errorOccurred.get());
        
        // Verify expected number of actions were performed
        assertEquals("Expected number of actions should be performed", 
                    numUsers * numActionsPerUser, actionCounter.get());
        
        logger.info("Real-world usage pattern test completed successfully");
    }
    
    /**
     * Test scenario: Simulate session hijacking
     * This test simulates a scenario where a user's session is "hijacked"
     * by another user, which might be similar to the reported issue
     */
    @Test
    public void testSessionHijacking() throws Exception {
        logger.info("Starting session hijacking simulation test");
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's session and CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());
        
        // Simulate session hijacking: Create a new request with the same session ID
        // but for a different user
        HttpServletRequest hijackedRequest = createMfaLoginRequest(userId2, sessionId1);
        HttpServletResponse hijackedResponse = createMockResponse();
        
        // Use the existing session for the hijacked request
        when(hijackedRequest.getSession()).thenReturn(session1);
        when(hijackedRequest.getSession(anyBoolean())).thenReturn(session1);
        
        // Try to login as the second user using the hijacked session
        logonAction.setUser(user2);
        String hijackResult = logonAction.login();
        
        // Check the result - this might succeed or fail depending on the implementation
        logger.info("Hijack login result: " + hijackResult);
        
        // Get the CDM from the session after the hijack attempt
        CommonDataManager cdmAfterHijack = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Check which user is in the CDM
        String userIdInCdm = cdmAfterHijack.getUser().getId().getUserId();
        logger.info("User ID in CDM after hijack attempt: " + userIdInCdm);
        
        // If the hijack was successful, the user ID would be userId2
        // If the hijack was prevented, the user ID would still be userId1
        
        // The important thing is that we don't end up with a mixed state
        // where some parts of the session refer to user1 and others to user2
        
        // Verify the CDM is consistent
        User userInCdm = cdmAfterHijack.getUser();
        assertEquals("User ID and name should be consistent", 
                    "User " + userInCdm.getId().getUserId(), userInCdm.getUserName());
        assertEquals("User ID and email should be consistent", 
                    userInCdm.getId().getUserId() + "@example.com", userInCdm.getEmailId());
        
        logger.info("Session hijacking simulation test completed");
    }
    
    /**
     * Test scenario: Simulate CDM sharing between sessions
     * This test simulates a scenario where the same CDM instance is shared
     * between multiple sessions, which could lead to session mixup
     */
    @Test
    public void testCdmSharingBetweenSessions() throws Exception {
        logger.info("Starting CDM sharing between sessions test");
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's session and CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);

        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());

        // Second user login
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();

        MockHttpSession session2 = sessions.get(sessionId2);
        // Simulate the issue: Use the same CDM instance for both sessions
        // This is what might be happening in the real application
        sessions.get(sessionId2).setAttribute(SwtConstants.CDM_BEAN, cdm1);
        
        logonAction.setUser(user2);
        String result2 = logonAction.login();
        
        assertEquals("Second user login should succeed", "success", result2);
        
        // Get the CDM from both sessions after login
        CommonDataManager cdmSession1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        CommonDataManager cdmSession2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        // Check if they are the same instance
        boolean sameCdmInstance = (cdmSession1 == cdmSession2);
        logger.info("Same CDM instance in both sessions: " + sameCdmInstance);
        
        // Check which user is in each CDM
        String userIdInCdm1 = cdmSession1.getUser().getId().getUserId();
        String userIdInCdm2 = cdmSession2.getUser().getId().getUserId();
        
        logger.info("User ID in session1's CDM: " + userIdInCdm1);
        logger.info("User ID in session2's CDM: " + userIdInCdm2);
        
        // If they are the same instance and the second login succeeded,
        // we would expect both to contain user2's data, which would be a session mixup
        
        // Verify the CDMs are consistent
        User userInCdm1 = cdmSession1.getUser();
        User userInCdm2 = cdmSession2.getUser();
        
        assertEquals("User ID and name should be consistent in session1", 
                    "User " + userInCdm1.getId().getUserId(), userInCdm1.getUserName());
        assertEquals("User ID and name should be consistent in session2", 
                    "User " + userInCdm2.getId().getUserId(), userInCdm2.getUserName());
        
        logger.info("CDM sharing between sessions test completed");
    }
}
