package web.mfa;

import org.swallow.control.model.UserAuthDetails;
import org.swallow.model.User;

import java.util.Date;

/**
 * Utility class for MFA session tests
 */
public class TestUtils {
    
    /**
     * Create a test user with the given ID
     */
    public static User createTestUser(String userId) {
        User user = new User();
        User.Id id = new User.Id();
        id.setHostId("HOST1");
        id.setUserId(userId);
        user.setId(id);
        user.setUserName("User " + userId);
        user.setRoleId("ROLE_" + userId);
        user.setStatus("1");
        user.setExtAuthId(userId);
        user.setEmailId(userId + "@example.com");
        user.setPhoneNumber("************");
        user.setCurrentEntity("ENTITY1");
        user.setCurrentCcyGrpId("CCY1");
        user.setLanguage("EN");
        user.setPassword("password");
        user.setPasswordChangeDate(new Date());
        user.setInvPassAttempt(0);
        
        // Set up UserAuthDetails
        UserAuthDetails authDetails = new UserAuthDetails();
        authDetails.setCsrfTokenArray(new String[] {"csrf-token-" + userId});
        user.setUserAuthDetails(authDetails);
        
        return user;
    }
}
