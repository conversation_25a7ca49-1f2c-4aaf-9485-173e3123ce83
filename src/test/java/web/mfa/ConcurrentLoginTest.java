package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.Test;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

/**
 * Test class for concurrent MFA logins
 */
public class ConcurrentLoginTest extends MFASessionTestBase {
    
    /**
     * Test scenario: Concurrent logins
     * This test simulates multiple users logging in concurrently
     * to check if race conditions cause session mixups
     */
    @Test
    public void testConcurrentLogins() throws Exception {
        logger.info("Starting concurrent logins test");
        
        final int numThreads = NUM_USERS;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(numThreads);
        final Map<String, String> userSessionMap = new HashMap<>();
        final Map<String, CommonDataManager> sessionCdmMap = new HashMap<>();
        final AtomicBoolean errorOccurred = new AtomicBoolean(false);
        
        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(numThreads);
        
        // Submit login tasks
        for (int i = 0; i < numThreads; i++) {
            final String userId = "user" + i;
            final String sessionId = "session-" + sessionCounter.incrementAndGet();
            
            executor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Wait for all threads to be ready
                        startLatch.await();
                        
                        logger.info("Thread for user: " + userId + " starting login");
                        
                        // Create request and response
                        HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
                        HttpServletResponse response = createMockResponse();
                        
                        // Set up the user for LogonAction
                        User user = users.get(userId);
                        logonAction.setUser(user);
                        
                        // Perform login
                        String result = logonAction.login();
                        
                        // Verify login was successful
                        if (!"success".equals(result)) {
                            logger.error("Login failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        // Get the CDM from the session
                        MockHttpSession session = sessions.get(sessionId);
                        CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                        
                        // Store for verification
                        synchronized (userSessionMap) {
                            userSessionMap.put(userId, sessionId);
                            sessionCdmMap.put(sessionId, cdm);
                        }
                        
                        // Verify CDM contains correct user
                        if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                            logger.error("CDM verification failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        logger.info("Thread for user: " + userId + " completed login");
                    } catch (Exception e) {
                        logger.error("Exception in login thread for user: " + userId, e);
                        errorOccurred.set(true);
                    } finally {
                        finishLatch.countDown();
                    }
                }
            });
        }
        
        // Start all threads simultaneously
        startLatch.countDown();
        
        // Wait for all threads to finish
        finishLatch.await(30, TimeUnit.SECONDS);
        
        // Shut down executor
        executor.shutdown();
        executor.awaitTermination(5, TimeUnit.SECONDS);
        
        // Verify no errors occurred
        assertFalse("No errors should occur during concurrent logins", errorOccurred.get());
        
        // Verify no session mixup occurred
        for (Map.Entry<String, String> entry : userSessionMap.entrySet()) {
            String userId = entry.getKey();
            String sessionId = entry.getValue();
            CommonDataManager cdm = sessionCdmMap.get(sessionId);
            
            logger.info("Verifying user: " + userId + " with session: " + sessionId);
            
            // Verify user data in CDM
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());
            assertEquals("User name in CDM should match", "User " + userId, cdm.getUser().getUserName());
            assertEquals("User email in CDM should match", userId + "@example.com", cdm.getUser().getEmailId());
        }
        
        logger.info("Concurrent logins test completed successfully");
    }
    
    /**
     * Test scenario: Concurrent login-logout
     * This test simulates users logging in and out concurrently
     * to check if race conditions cause session mixups
     */
    @Test
    public void testConcurrentLoginLogout() throws Exception {
        logger.info("Starting concurrent login-logout test");
        
        final int numThreads = NUM_USERS;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(numThreads);
        final AtomicBoolean errorOccurred = new AtomicBoolean(false);
        
        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(numThreads);
        
        // Submit login-logout tasks
        for (int i = 0; i < numThreads; i++) {
            final String userId = "user" + i;
            final String sessionId = "session-" + sessionCounter.incrementAndGet();
            
            executor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Wait for all threads to be ready
                        startLatch.await();
                        
                        logger.info("Thread for user: " + userId + " starting login-logout sequence");
                        
                        // Create request and response
                        HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
                        HttpServletResponse response = createMockResponse();
                        
                        // Set up the user for LogonAction
                        User user = users.get(userId);
                        logonAction.setUser(user);
                        
                        // Perform login
                        String loginResult = logonAction.login();
                        
                        // Verify login was successful
                        if (!"success".equals(loginResult)) {
                            logger.error("Login failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        // Get the CDM from the session
                        MockHttpSession session = sessions.get(sessionId);
                        CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                        
                        // Verify CDM contains correct user
                        if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                            logger.error("CDM verification failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        // Short delay to simulate user activity
                        Thread.sleep(50);
                        
                        // Perform logout
                        String logoutResult = logoutAction.logout(request, response);
                        
                        logger.info("Thread for user: " + userId + " completed login-logout sequence");
                    } catch (Exception e) {
                        logger.error("Exception in login-logout thread for user: " + userId, e);
                        errorOccurred.set(true);
                    } finally {
                        finishLatch.countDown();
                    }
                }
            });
        }
        
        // Start all threads simultaneously
        startLatch.countDown();
        
        // Wait for all threads to finish
        finishLatch.await(30, TimeUnit.SECONDS);
        
        // Shut down executor
        executor.shutdown();
        executor.awaitTermination(5, TimeUnit.SECONDS);
        
        // Verify no errors occurred
        assertFalse("No errors should occur during concurrent login-logout", errorOccurred.get());
        
        logger.info("Concurrent login-logout test completed successfully");
    }
}
