package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test class specifically for session mixup scenarios
 */
public class SessionMixupTest extends MFASessionTestBase {
    
    /**
     * Test scenario: Login while previous session exists
     * This test simulates a user logging in when they already have an active session
     * to check if the previous session is properly invalidated
     */
    @Test
    public void testLoginWithExistingSession() throws Exception {
        logger.info("Starting login with existing session test");
        
        // Choose a test user
        String userId = "user0";
        User user = users.get(userId);
        
        // First login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user);
        String result1 = logonAction.login();
        
        assertEquals("First login should succeed", "success", result1);
        
        // Verify first session
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        assertNotNull("CDM should not be null after first login", cdm1);
        assertEquals("User ID in CDM should match after first login", userId, cdm1.getUser().getId().getUserId());
        
        // Second login (same user)
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        logonAction.setUser(user);
        String result2 = logonAction.login();
        
        // The result might be "fail" with killSessionStatus="K" set in the request
        // This is expected behavior when a user already has an active session
        
        // Check if session1 was invalidated or marked for killing
        Object killSessionStatus = request2.getAttribute("killSessionStatus");
        if ("K".equals(killSessionStatus)) {
            logger.info("Session was marked for killing as expected");
        } else {
            // If not marked for killing, the first session should be invalidated
            try {
                session1.getAttribute(SwtConstants.CDM_BEAN);
                // If we get here, the session wasn't invalidated
                fail("First session should be invalidated or marked for killing");
            } catch (IllegalStateException e) {
                // Expected if session was invalidated
                logger.info("First session was invalidated as expected");
            }
        }
        
        logger.info("Login with existing session test completed");
    }
    
    /**
     * Test scenario: Simulate session mixup
     * This test deliberately tries to create conditions that might lead to session mixup
     */
    @Test
    public void testSimulateSessionMixup() throws Exception {
        logger.info("Starting session mixup simulation test");
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Second user login
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        // Simulate potential issue: Set the same CDM in both sessions
        // This is an artificial way to create a session mixup
        session1.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        sessions.get(sessionId2).setAttribute(SwtConstants.CDM_BEAN, cdm1);
        
        // Now perform an action with the second user's session
        // This should detect the session mixup
        
        // Check which user is in the CDM of session2
        CommonDataManager cdm2 = (CommonDataManager) sessions.get(sessionId2).getAttribute(SwtConstants.CDM_BEAN);
        
        // This should fail if we've successfully simulated a session mixup
        assertEquals("Session mixup detected: wrong user in session2's CDM", 
                     userId1, cdm2.getUser().getId().getUserId());
        
        logger.info("Session mixup simulation test completed");
    }
    
    /**
     * Test scenario: Simulate real-world session mixup
     * This test tries to reproduce the reported issue by manipulating the session map
     */
    @Test
    public void testRealWorldSessionMixup() throws Exception {
        logger.info("Starting real-world session mixup test");
        
        // Mock the SessionManager to use our controlled session map
        ConcurrentHashMap<String, Object> sessionMap = new ConcurrentHashMap<>();
        SessionManager originalManager = SessionManager.getInstance();
        SessionManager mockManager = mock(SessionManager.class);
        
        when(mockManager.getSessionMap()).thenReturn(sessionMap);
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        // Add session to our controlled map
        MockHttpSession session1 = sessions.get(sessionId1);
        sessionMap.put(sessionId1, session1);
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's CDM
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());
        
        // Second user login
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        // Add session to our controlled map
        MockHttpSession session2 = sessions.get(sessionId2);
        sessionMap.put(sessionId2, session2);
        
        // Simulate the issue: Don't properly clean up first user's session
        // This is what might be happening in the real application
        
        logonAction.setUser(user2);
        String result2 = logonAction.login();
        
        assertEquals("Second user login should succeed", "success", result2);
        
        // Get second user's CDM
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify second user's session
        assertNotNull("CDM should not be null", cdm2);
        assertEquals("User ID in CDM should match", userId2, cdm2.getUser().getId().getUserId());
        
        // Now simulate a scenario where the first user logs out
        // This should not affect the second user's session
        
        // Perform logout for first user
        String logoutResult = logoutAction.logout(request1, response1);
        
        // Verify second user's session is still valid and contains correct user
        assertTrue("Second session should still be valid", session2.isValid());
        cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        assertNotNull("CDM should not be null after first user logout", cdm2);
        assertEquals("User ID in CDM should still match second user", 
                     userId2, cdm2.getUser().getId().getUserId());
        
        logger.info("Real-world session mixup test completed");
    }
    
    /**
     * Test scenario: Simulate MFA token reuse
     * This test simulates a scenario where the same MFA token is used for different users
     */
    @Test
    public void testMfaTokenReuse() throws Exception {
        logger.info("Starting MFA token reuse test");
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // Use the same MFA token for both users
        String sharedToken = "shared-mfa-token";
        mfaTokens.put(userId1, sharedToken);
        mfaTokens.put(userId2, sharedToken);
        
        // Mock SamlService to return different users for the same token
        final ArrayList<String> loginSequence = new ArrayList<>();
        loginSequence.add(userId1);
        loginSequence.add(userId2);
        
        when(samlService.verifySamlToken(eq(sharedToken))).thenAnswer(new Answer<SamlUserDTO>() {
            private int callCount = 0;
            
            @Override
            public SamlUserDTO answer(InvocationOnMock invocation) throws Throwable {
                String userId = loginSequence.get(callCount % loginSequence.size());
                callCount++;
                List<SamlUserDTO.UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
                SamlUserDTO dto = new SamlUserDTO();
                dto.setUsername(userId);
                attributes.add(new SamlUserDTO.UserAttribute("SMART_USER_ID", "SMART_USER_ID", userId));
                attributes.add(new SamlUserDTO.UserAttribute("SMART_ROLE_ID", "SMART_ROLE_ID", "ROLE_" + userId));
                attributes.add(new SamlUserDTO.UserAttribute("FIRST_NAME", "FIRST_NAME", "User " + userId));
                attributes.add(new SamlUserDTO.UserAttribute("EMAIL", "EMAIL", userId + "@example.com"));
                dto.setAttributes(attributes);
                return dto;
            }
        });
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());
        
        // Second user login with same token
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        logonAction.setUser(user2);
        String result2 = logonAction.login();
        
        assertEquals("Second user login should succeed", "success", result2);
        
        // Get second user's CDM
        MockHttpSession session2 = sessions.get(sessionId2);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify second user's session
        assertNotNull("CDM should not be null", cdm2);
        assertEquals("User ID in CDM should match", userId2, cdm2.getUser().getId().getUserId());
        
        // Verify first and second CDMs are different instances
        assertNotSame("First and second CDMs should be different instances", cdm1, cdm2);
        
        // Verify first and second CDMs contain different users
        assertNotEquals("Users in CDMs should be different", 
                       cdm1.getUser().getId().getUserId(), 
                       cdm2.getUser().getId().getUserId());
        
        logger.info("MFA token reuse test completed");
    }
}
