package web.mfa;

import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionBindingEvent;
import jakarta.servlet.http.HttpSessionBindingListener;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Mock HttpSession implementation for testing
 */
public class MockHttpSession implements HttpSession {
    private String id;
    private Map<String, Object> attributes = new HashMap<>();
    private boolean valid = true;
    private long creationTime;
    private long lastAccessedTime;
    
    public MockHttpSession(String id) {
        this.id = id;
        this.creationTime = System.currentTimeMillis();
        this.lastAccessedTime = this.creationTime;
    }
    
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public void setAttribute(String name, Object value) {
        if (!valid) return;
        
        Object oldValue = attributes.get(name);
        attributes.put(name, value);
        
        // Simulate HttpSessionBindingListener events
        if (oldValue instanceof HttpSessionBindingListener) {
            HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, oldValue);
            ((HttpSessionBindingListener) oldValue).valueUnbound(event);
        }
        
        if (value instanceof HttpSessionBindingListener) {
            HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, value);
            ((HttpSessionBindingListener) value).valueBound(event);
        }
    }
    
    @Override
    public Object getAttribute(String name) {
        if (!valid) return null;
        return attributes.get(name);
    }
    
    @Override
    public Enumeration<String> getAttributeNames() {
        if (!valid) return null;
        return new Enumeration<String>() {
            private String[] names = attributes.keySet().toArray(new String[0]);
            private int index = 0;
            
            @Override
            public boolean hasMoreElements() {
                return index < names.length;
            }
            
            @Override
            public String nextElement() {
                return names[index++];
            }
        };
    }
    
    @Override
    public void removeAttribute(String name) {
        if (!valid) return;
        
        Object value = attributes.remove(name);
        
        // Simulate HttpSessionBindingListener events
        if (value instanceof HttpSessionBindingListener) {
            HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, value);
            ((HttpSessionBindingListener) value).valueUnbound(event);
        }
    }
    
    @Override
    public void invalidate() {
        if (!valid) throw new IllegalStateException("Session already invalidated");
        
        // Notify all binding listeners
        for (Map.Entry<String, Object> entry : attributes.entrySet()) {
            if (entry.getValue() instanceof HttpSessionBindingListener) {
                HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, entry.getKey(), entry.getValue());
                ((HttpSessionBindingListener) entry.getValue()).valueUnbound(event);
            }
        }
        
        attributes.clear();
        valid = false;
    }
    
    @Override
    public boolean isNew() {
        return creationTime == lastAccessedTime;
    }
    
    @Override
    public long getCreationTime() {
        if (!valid) throw new IllegalStateException("Session already invalidated");
        return creationTime;
    }
    
    @Override
    public long getLastAccessedTime() {
        if (!valid) throw new IllegalStateException("Session already invalidated");
        return lastAccessedTime;
    }
    
    // Update last accessed time
    public void access() {
        this.lastAccessedTime = System.currentTimeMillis();
    }
    
    // Check if session is valid
    public boolean isValid() {
        return valid;
    }
    
    // Other required methods with minimal implementations
    @Override
    public void setMaxInactiveInterval(int interval) {}
    
    @Override
    public int getMaxInactiveInterval() {
        return 1800; // 30 minutes
    }
    
    @Override
    public ServletContext getServletContext() {
        return null;
    }
    
}
