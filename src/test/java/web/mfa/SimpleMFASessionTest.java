package web.mfa;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * A simplified test class that focuses on reproducing the session mixup issue
 * without relying on Struts2 ActionContext.
 */
public class SimpleMFASessionTest {
    
    private static final Log logger = LogFactory.getLog(SimpleMFASessionTest.class);
    
    // Test objects
    private Map<String, MockHttpSession> sessions;
    private Map<String, CommonDataManager> cdms;
    private SessionManager sessionManager;
    
    @Before
    public void setUp() {
        sessions = new HashMap<>();
        cdms = new HashMap<>();
        sessionManager = SessionManager.getInstance();
    }
    
    @After
    public void tearDown() {
        // Clean up sessions
        for (MockHttpSession session : sessions.values()) {
            try {
                if (session.isValid()) {
                    session.invalidate();
                }
            } catch (IllegalStateException e) {
                // Session already invalidated
            }
        }
        sessions.clear();
        cdms.clear();
    }
    
    /**
     * Create a session with a CDM containing the specified user
     */
    private MockHttpSession createSessionWithUser(String sessionId, String userId) {
        // Create session
        MockHttpSession session = new MockHttpSession(sessionId);
        sessions.put(sessionId, session);
        
        // Create CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Create user
        User user = TestUtils.createTestUser(userId);
        cdm.setUser(user);
        
        // Bind CDM to session
        session.setAttribute(SwtConstants.CDM_BEAN, cdm);
        
        // Store CDM for verification
        cdms.put(sessionId, cdm);
        
        return session;
    }
    
    /**
     * Test scenario: Basic session operations
     * This test verifies that sessions and CDMs work correctly in isolation
     */
    @Test
    public void testBasicSessionOperations() {
        // Create two sessions with different users
        MockHttpSession session1 = createSessionWithUser("session-1", "user1");
        MockHttpSession session2 = createSessionWithUser("session-2", "user2");
        
        // Verify each session has the correct user
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        assertEquals("Session 1 should have user1", "user1", cdm1.getUser().getId().getUserId());
        assertEquals("Session 2 should have user2", "user2", cdm2.getUser().getId().getUserId());
        
        // Verify CDMs are different instances
        assertNotSame("CDMs should be different instances", cdm1, cdm2);
    }
    
    /**
     * Test scenario: Session invalidation
     * This test verifies that invalidating one session doesn't affect another
     */
    @Test
    public void testSessionInvalidation() {
        // Create two sessions with different users
        MockHttpSession session1 = createSessionWithUser("session-1", "user1");
        MockHttpSession session2 = createSessionWithUser("session-2", "user2");
        
        // Invalidate first session
        session1.invalidate();
        
        // Verify first session is invalid
        assertFalse("Session 1 should be invalid", session1.isValid());
        
        // Verify second session is still valid
        assertTrue("Session 2 should still be valid", session2.isValid());
        
        // Verify second session still has the correct user
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        assertEquals("Session 2 should still have user2", "user2", cdm2.getUser().getId().getUserId());
    }
    
    /**
     * Test scenario: Shared CDM between sessions
     * This test simulates the potential issue where the same CDM is shared between sessions
     */
    @Test
    public void testSharedCdmBetweenSessions() {
        // Create a session with a user
        MockHttpSession session1 = createSessionWithUser("session-1", "user1");
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Create a second session but use the same CDM
        MockHttpSession session2 = new MockHttpSession("session-2");
        sessions.put("session-2", session2);
        
        // Deliberately use the same CDM instance in the second session
        session2.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        
        // Verify both sessions have the same CDM instance
        assertSame("Both sessions should have the same CDM instance", 
                  cdm1, session2.getAttribute(SwtConstants.CDM_BEAN));
        
        // Verify both sessions have the same user
        assertEquals("Both sessions should have user1", "user1", 
                    ((CommonDataManager)session1.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        assertEquals("Both sessions should have user1", "user1", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        
        // Change the user in the CDM
        User user2 = TestUtils.createTestUser("user2");
        cdm1.setUser(user2);
        
        // Verify the change is reflected in both sessions
        assertEquals("Both sessions should now have user2", "user2", 
                    ((CommonDataManager)session1.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        assertEquals("Both sessions should now have user2", "user2", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        
        // Invalidate the first session
        session1.invalidate();
        
        // Verify the second session still has the CDM
        assertSame("Session 2 should still have the CDM", 
                  cdm1, session2.getAttribute(SwtConstants.CDM_BEAN));
        
        // Verify the second session still has user2
        assertEquals("Session 2 should still have user2", "user2", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
    }
    
    /**
     * Test scenario: Session mixup simulation
     * This test simulates a scenario that might lead to session mixup
     */
    @Test
    public void testSessionMixupSimulation() {
        // Create two sessions with different users
        MockHttpSession session1 = createSessionWithUser("session-1", "user1");
        MockHttpSession session2 = createSessionWithUser("session-2", "user2");
        
        // Get the CDMs
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify initial state
        assertEquals("Session 1 should have user1", "user1", cdm1.getUser().getId().getUserId());
        assertEquals("Session 2 should have user2", "user2", cdm2.getUser().getId().getUserId());
        
        // Simulate the issue: Swap CDMs between sessions
        session1.setAttribute(SwtConstants.CDM_BEAN, cdm2);
        session2.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        
        // Verify the swap
        assertEquals("Session 1 should now have user2", "user2", 
                    ((CommonDataManager)session1.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        assertEquals("Session 2 should now have user1", "user1", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        
        // Invalidate one session
        session1.invalidate();
        
        // Verify the other session is still valid and has the correct user
        assertTrue("Session 2 should still be valid", session2.isValid());
        assertEquals("Session 2 should still have user1", "user1", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
    }
    
    /**
     * Test scenario: Multiple CDMs in one session
     * This test simulates a scenario where multiple CDMs are added to the same session
     */
    @Test
    public void testMultipleCdmsInOneSession() {
        // Create a session
        MockHttpSession session = new MockHttpSession("session-1");
        sessions.put("session-1", session);
        
        // Create two CDMs with different users
        CommonDataManager cdm1 = new CommonDataManager();
        User user1 = TestUtils.createTestUser("user1");
        cdm1.setUser(user1);
        
        CommonDataManager cdm2 = new CommonDataManager();
        User user2 = TestUtils.createTestUser("user2");
        cdm2.setUser(user2);
        
        // Add both CDMs to the session with different attribute names
        session.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        session.setAttribute("anotherCdm", cdm2);
        
        // Verify both CDMs are in the session
        assertSame("Session should have cdm1 as CDM_BEAN", cdm1, session.getAttribute(SwtConstants.CDM_BEAN));
        assertSame("Session should have cdm2 as anotherCdm", cdm2, session.getAttribute("anotherCdm"));
        
        // Verify each CDM has the correct user
        assertEquals("cdm1 should have user1", "user1", cdm1.getUser().getId().getUserId());
        assertEquals("cdm2 should have user2", "user2", cdm2.getUser().getId().getUserId());
        
        // Replace the CDM_BEAN with cdm2
        session.setAttribute(SwtConstants.CDM_BEAN, cdm2);
        
        // Verify the change
        assertSame("Session should now have cdm2 as CDM_BEAN", cdm2, session.getAttribute(SwtConstants.CDM_BEAN));
        assertEquals("CDM_BEAN should now have user2", "user2", 
                    ((CommonDataManager)session.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
    }
}
