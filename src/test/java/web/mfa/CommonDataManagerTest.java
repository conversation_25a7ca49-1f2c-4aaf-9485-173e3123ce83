package web.mfa;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionBindingEvent;
import org.junit.Before;
import org.junit.Test;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;


import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test class for the CommonDataManager's session binding behavior
 */
public class CommonDataManagerTest {
    
    private SessionManager sessionManager;
    
    @Before
    public void setUp() {
        sessionManager = SessionManager.getInstance();
    }
    
    /**
     * Test basic CDM operations
     */
    @Test
    public void testBasicCdmOperations() {
        // Create a CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user = TestUtils.createTestUser("testUser");
        cdm.setUser(user);
        
        // Verify user
        assertEquals("User should match", user, cdm.getUser());
        assertEquals("User ID should match", "testUser", cdm.getUser().getId().getUserId());
    }
    
    /**
     * Test CDM session binding
     */
    @Test
    public void testCdmSessionBinding() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create a CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user = TestUtils.createTestUser("testUser");
        cdm.setUser(user);
        
        // Bind CDM to session
        session.setAttribute(SwtConstants.CDM_BEAN, cdm);
        
        // Verify CDM is in session
        assertSame("CDM should be in session", cdm, session.getAttribute(SwtConstants.CDM_BEAN));
        
        // Verify CDM is in SessionManager's session map
        // This is a key part of how the application tracks sessions
        Object sessionObj = sessionManager.getSessionMap().get(session.getId());
        
        // The session object might be the session itself or a wrapper
        // depending on the implementation
        assertNotNull("Session should be in SessionManager's map", sessionObj);
    }
    
    /**
     * Test CDM session unbinding
     */
    @Test
    public void testCdmSessionUnbinding() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create a CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user = TestUtils.createTestUser("testUser");
        cdm.setUser(user);
        
        // Bind CDM to session
        session.setAttribute(SwtConstants.CDM_BEAN, cdm);
        
        // Verify CDM is in session
        assertSame("CDM should be in session", cdm, session.getAttribute(SwtConstants.CDM_BEAN));
        
        // Unbind CDM from session
        session.removeAttribute(SwtConstants.CDM_BEAN);
        
        // Verify CDM is not in session
        assertNull("CDM should not be in session", session.getAttribute(SwtConstants.CDM_BEAN));
    }
    
    /**
     * Test CDM behavior when session is invalidated
     */
    @Test
    public void testCdmSessionInvalidation() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create a CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user = TestUtils.createTestUser("testUser");
        cdm.setUser(user);
        
        // Bind CDM to session
        session.setAttribute(SwtConstants.CDM_BEAN, cdm);
        
        // Verify CDM is in session
        assertSame("CDM should be in session", cdm, session.getAttribute(SwtConstants.CDM_BEAN));
        
        // Invalidate session
        session.invalidate();
        
        // Verify CDM is not in session
        assertNull("CDM should not be in invalidated session", session.getAttribute(SwtConstants.CDM_BEAN));
    }
    
    /**
     * Test multiple CDMs in different sessions
     */
    @Test
    public void testMultipleCdmsInDifferentSessions() {
        // Create two sessions
        MockHttpSession session1 = new MockHttpSession("session-1");
        MockHttpSession session2 = new MockHttpSession("session-2");
        
        // Create two CDMs
        CommonDataManager cdm1 = new CommonDataManager();
        CommonDataManager cdm2 = new CommonDataManager();
        
        // Set users
        User user1 = TestUtils.createTestUser("user1");
        User user2 = TestUtils.createTestUser("user2");
        cdm1.setUser(user1);
        cdm2.setUser(user2);
        
        // Bind CDMs to sessions
        session1.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        session2.setAttribute(SwtConstants.CDM_BEAN, cdm2);
        
        // Verify CDMs are in correct sessions
        assertSame("CDM1 should be in session1", cdm1, session1.getAttribute(SwtConstants.CDM_BEAN));
        assertSame("CDM2 should be in session2", cdm2, session2.getAttribute(SwtConstants.CDM_BEAN));
        
        // Verify users in CDMs
        assertEquals("User1 should be in CDM1", "user1", 
                    ((CommonDataManager)session1.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        assertEquals("User2 should be in CDM2", "user2", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
    }
    
    /**
     * Test sharing the same CDM between sessions
     * This simulates the potential issue where the same CDM is used in multiple sessions
     */
    @Test
    public void testSharedCdmBetweenSessions() {
        // Create two sessions
        MockHttpSession session1 = new MockHttpSession("session-1");
        MockHttpSession session2 = new MockHttpSession("session-2");
        
        // Create one CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user1 = TestUtils.createTestUser("user1");
        cdm.setUser(user1);
        
        // Bind the same CDM to both sessions
        session1.setAttribute(SwtConstants.CDM_BEAN, cdm);
        session2.setAttribute(SwtConstants.CDM_BEAN, cdm);
        
        // Verify the same CDM is in both sessions
        assertSame("CDM should be in session1", cdm, session1.getAttribute(SwtConstants.CDM_BEAN));
        assertSame("CDM should be in session2", cdm, session2.getAttribute(SwtConstants.CDM_BEAN));
        assertSame("CDM in session1 should be same as CDM in session2", 
                  session1.getAttribute(SwtConstants.CDM_BEAN), session2.getAttribute(SwtConstants.CDM_BEAN));
        
        // Change user in the CDM
        User user2 = TestUtils.createTestUser("user2");
        cdm.setUser(user2);
        
        // Verify the user change is reflected in both sessions
        assertEquals("User2 should be in CDM in session1", "user2", 
                    ((CommonDataManager)session1.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        assertEquals("User2 should be in CDM in session2", "user2", 
                    ((CommonDataManager)session2.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId().getUserId());
        
        // Invalidate one session
        session1.invalidate();
        
        // Verify the CDM is still in the other session
        assertNull("CDM should not be in invalidated session", session1.getAttribute(SwtConstants.CDM_BEAN));
        assertSame("CDM should still be in valid session", cdm, session2.getAttribute(SwtConstants.CDM_BEAN));
    }
    
    /**
     * Test CDM's HttpSessionBindingListener implementation
     */
    @Test
    public void testCdmSessionBindingListener() {
        // Create a CDM
        CommonDataManager cdm = new CommonDataManager();
        
        // Set user
        User user = TestUtils.createTestUser("testUser");
        cdm.setUser(user);
        
        // Create mock session and binding event
        HttpSession mockSession = mock(HttpSession.class);
        when(mockSession.getId()).thenReturn("mock-session");
        
        HttpSessionBindingEvent bindEvent = new HttpSessionBindingEvent(mockSession, SwtConstants.CDM_BEAN, cdm);
        
        // Simulate binding
        cdm.valueBound(bindEvent);
        
        // Verify session is in SessionManager's map
        // This depends on the implementation of CommonDataManager.valueBound()
        
        // Simulate unbinding
        cdm.valueUnbound(bindEvent);
        
        // Verify session is removed from SessionManager's map
        // This depends on the implementation of CommonDataManager.valueUnbound()
    }
}
