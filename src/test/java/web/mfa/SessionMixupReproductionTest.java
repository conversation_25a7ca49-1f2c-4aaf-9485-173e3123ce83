package web.mfa;

import config.BaseDAOIntegrationTest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.swallow.mfa.RegisterMFA;
import org.swallow.mfa.SamlService;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.web.LogonAction;
import org.swallow.web.LogoutAction;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test class specifically designed to reproduce the reported session mixup issue
 * during MFA authentication.
 */
public class SessionMixupReproductionTest extends BaseDAOIntegrationTest {
    
    private static final Log logger = LogFactory.getLog(SessionMixupReproductionTest.class);
    
    // Mock objects
    @Mock private LogonManager logonManager;
    @Mock private SamlService samlService;
    @Mock private RegisterMFA registerMFA;
    @Mock private TokensProvider tokensProvider;
    
    // Test objects
    private LogonAction logonAction;
    private LogoutAction logoutAction;
    private Map<String, MockHttpSession> sessions;
    private Map<String, User> users;
    private Map<String, String> mfaTokens;
    private SessionManager sessionManager;
    
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // Initialize test objects
        logonAction = new LogonAction();
        logoutAction = new LogoutAction();
        sessions = new HashMap<>();
        users = new HashMap<>();
        mfaTokens = new HashMap<>();
        sessionManager = SessionManager.getInstance();
        
        // Set up LogonAction
        logonAction.setLogonManager(logonManager);
        
        // Set up LogoutAction
        logoutAction.setLogonManager(logonManager);
        
        // Set up RegisterMFA mock
        when(registerMFA.isUseSmartAuthenticator()).thenReturn(true);
        when(registerMFA.isAllowInternalAuthentication()).thenReturn(true);
        when(registerMFA.isAllowUserCreationWithSmartAuthenticator()).thenReturn(true);
        
        // Create test users
        String userId1 = "user1";
        String userId2 = "user2";
        users.put(userId1, TestUtils.createTestUser(userId1));
        users.put(userId2, TestUtils.createTestUser(userId2));
        mfaTokens.put(userId1, "mfa-token-" + userId1);
        mfaTokens.put(userId2, "mfa-token-" + userId2);
        
        // Set up SamlService mock
        when(samlService.verifySamlToken(anyString())).thenAnswer(new Answer<SamlUserDTO>() {
            @Override
            public SamlUserDTO answer(InvocationOnMock invocation) throws Throwable {
                String token = invocation.getArgument(0);
                List<SamlUserDTO.UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
                for (Map.Entry<String, String> entry : mfaTokens.entrySet()) {
                    if (entry.getValue().equals(token)) {
                        SamlUserDTO dto = new SamlUserDTO();
                        dto.setUsername(entry.getKey());
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_USER_ID", "SMART_USER_ID", entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_ROLE_ID", "SMART_ROLE_ID", "ROLE_" + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("FIRST_NAME", "FIRST_NAME", "User " + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("EMAIL", "EMAIL", entry.getKey() + "@example.com"));
                        dto.setAttributes(attributes);
                        return dto;
                    }
                }
                return null;
            }
        });
        
        // Set up LogonManager mock
        when(logonManager.getUserDetailByExtAuthId(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });
        
        when(logonManager.getUserDetail(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });
        
        // Set up TokensProvider mock
        when(tokensProvider.createToken(any(User.class), anyString())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                User user = invocation.getArgument(0);
                return "token-" + user.getId().getUserId();
            }
        });
        
        when(tokensProvider.validateToken(any(HttpServletRequest.class))).thenReturn(true);
    }
    
    @After
    public void tearDown() {
        // Clean up sessions
        for (MockHttpSession session : sessions.values()) {
            try {
                if (session.isValid()) {
                    session.invalidate();
                }
            } catch (IllegalStateException e) {
                // Session already invalidated
            }
        }
        sessions.clear();
        users.clear();
        mfaTokens.clear();
        
        // Clean up Struts ActionContext
    }
    
    /**
     * Create a mock HttpServletRequest for MFA login
     */
    private HttpServletRequest createMfaLoginRequest(String userId, String sessionId) {
        HttpServletRequest request = mock(HttpServletRequest.class);
        MockHttpSession session = new MockHttpSession(sessionId);
        sessions.put(sessionId, session);
        
        when(request.getSession()).thenReturn(session);
        when(request.getSession(anyBoolean())).thenReturn(session);
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        when(request.getParameter("logonflag")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        // Set up parameters for SwtUtil.isSucessfulMFARequest
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        
        // Create parameter map for Struts
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("auth", new String[] {mfaTokens.get(userId)});
        when(request.getParameterMap()).thenReturn(parameterMap);
        
        return request;
    }
    
    /**
     * Create a mock HttpServletResponse
     */
    private HttpServletResponse createMockResponse() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        return response;
    }
    
    /**
     * Test scenario: Reproduce the reported session mixup issue
     * This test simulates the scenario where two users are logging in with MFA
     * and one user's session gets mixed up with another user's session
     */
    @Test
    public void testReproduceSessionMixupIssue() throws Exception {
        logger.info("Starting session mixup reproduction test");
        
        // Choose two test users
        String userId1 = "user1";
        String userId2 = "user2";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-1";
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        // Set up Struts ActionContext
        StrutsTestHelper.setupActionContext(request1, response1);
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's session and CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify first user's session
        assertNotNull("CDM should not be null", cdm1);
        assertEquals("User ID in CDM should match", userId1, cdm1.getUser().getId().getUserId());
        
        // Second user login without properly cleaning up first user's session
        // This simulates the issue where the first user's session is not properly invalidated
        String sessionId2 = "session-2";
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        // Set up Struts ActionContext for second user
        StrutsTestHelper.setupActionContext(request2, response2);
        
        // Simulate the issue: Don't properly clean up first user's session
        // In a real application, this might happen due to a bug in session management
        
        logonAction.setUser(user2);
        String result2 = logonAction.login();
        
        assertEquals("Second user login should succeed", "success", result2);
        
        // Get second user's session and CDM
        MockHttpSession session2 = sessions.get(sessionId2);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        // Verify second user's session
        assertNotNull("CDM should not be null", cdm2);
        assertEquals("User ID in CDM should match", userId2, cdm2.getUser().getId().getUserId());
        
        // Now simulate the first user logging out
        // This should not affect the second user's session
        
        // Set up Struts ActionContext for first user again
        StrutsTestHelper.setupActionContext(request1, response1);
        
        String logoutResult = logoutAction.logout(request1, response1);
        
        // Verify second user's session is still valid and contains correct user
        assertTrue("Second session should still be valid", session2.isValid());
        cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        assertNotNull("CDM should not be null after first user logout", cdm2);
        assertEquals("User ID in CDM should still match second user", 
                     userId2, cdm2.getUser().getId().getUserId());
        
        logger.info("Session mixup reproduction test completed");
    }
    
    /**
     * Test scenario: Concurrent MFA logins and logouts
     * This test simulates multiple users logging in and out concurrently
     * to try to reproduce the session mixup issue
     */
    @Test
    public void testConcurrentMfaLoginsAndLogouts() throws Exception {
        logger.info("Starting concurrent MFA logins and logouts test");
        
        final int numUsers = 5;
        final int numIterations = 3;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(numUsers);
        final AtomicBoolean sessionMixupDetected = new AtomicBoolean(false);
        
        // Create users
        for (int i = 1; i <= numUsers; i++) {
            String userId = "user" + i;
            users.put(userId, TestUtils.createTestUser(userId));
            mfaTokens.put(userId, "mfa-token-" + userId);
        }
        
        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(numUsers);
        
        // Submit user simulation tasks
        for (int i = 1; i <= numUsers; i++) {
            final String userId = "user" + i;
            final int userIndex = i;
            
            executor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Wait for all threads to be ready
                        startLatch.await();
                        
                        logger.info("Thread for user: " + userId + " starting");
                        
                        User user = users.get(userId);
                        
                        for (int j = 0; j < numIterations; j++) {
                            // Login
                            String sessionId = userId + "-session-" + j;
                            HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
                            HttpServletResponse response = createMockResponse();
                            
                            // Set up Struts ActionContext
                            synchronized (StrutsTestHelper.class) {
                                StrutsTestHelper.setupActionContext(request, response);
                                
                                logonAction.setUser(user);
                                String loginResult = logonAction.login();
                                
                                if (!"success".equals(loginResult)) {
                                    logger.error("Login failed for user: " + userId);
                                    sessionMixupDetected.set(true);
                                    continue;
                                }
                            }
                            
                            // Get session and CDM
                            MockHttpSession session = sessions.get(sessionId);
                            CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                            
                            // Verify user data in CDM
                            if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                                logger.error("Session mixup detected for user: " + userId);
                                sessionMixupDetected.set(true);
                            }
                            
                            // Short delay to simulate user activity
                            Thread.sleep(50);
                            
                            // Logout
                            synchronized (StrutsTestHelper.class) {
                                StrutsTestHelper.setupActionContext(request, response);
                                logoutAction.logout(request, response);
                            }
                            
                            // Short delay between iterations
                            Thread.sleep(50);
                        }
                        
                        logger.info("Thread for user: " + userId + " completed");
                    } catch (Exception e) {
                        logger.error("Exception in thread for user: " + userId, e);
                        sessionMixupDetected.set(true);
                    } finally {
                        finishLatch.countDown();
                    }
                }
            });
        }
        
        // Start all threads
        startLatch.countDown();
        
        // Wait for all threads to finish
        finishLatch.await(60, TimeUnit.SECONDS);
        
        // Shut down executor
        executor.shutdown();
        executor.awaitTermination(5, TimeUnit.SECONDS);
        
        // Check if session mixup was detected
        assertFalse("No session mixup should be detected", sessionMixupDetected.get());
        
        logger.info("Concurrent MFA logins and logouts test completed");
    }
}
