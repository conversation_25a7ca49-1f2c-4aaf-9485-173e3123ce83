package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Helper class for setting up Spring MVC environment in tests
 */
public class StrutsTestHelper {

    /**
     * Set up the Spring RequestContext for testing
     *
     * @param request The mock HttpServletRequest
     * @param response The mock HttpServletResponse
     */
    public static void setupActionContext(HttpServletRequest request, HttpServletResponse response) {
        // Set up Spring's RequestContextHolder
        ServletRequestAttributes attributes = new ServletRequestAttributes(request, response);
        RequestContextHolder.setRequestAttributes(attributes);
    }

    /**
     * Clean up the Spring RequestContext after testing
     */
    public static void cleanupActionContext() {
        // Clear Spring's RequestContextHolder
        RequestContextHolder.resetRequestAttributes();
    }
}
