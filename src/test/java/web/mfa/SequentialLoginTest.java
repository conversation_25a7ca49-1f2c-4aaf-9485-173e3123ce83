package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.Test;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Test class for sequential MFA logins
 */
public class SequentialLoginTest extends MFASessionTestBase {

    /**
     * Test scenario: Rapid sequential logins
     * This test simulates multiple users logging in one after another
     * to check if session data gets mixed up
     */
    @Test
    public void testRapidSequentialLogins() throws Exception {
        logger.info("Starting rapid sequential logins test");

        // Track session data for verification
        Map<String, String> userSessionMap = new HashMap<>();
        Map<String, CommonDataManager> sessionCdmMap = new HashMap<>();

        // Perform sequential logins
        for (int i = 0; i < NUM_USERS; i++) {
            String userId = "user" + i;
            String sessionId = "session-" + sessionCounter.incrementAndGet();

            logger.info("Logging in user: " + userId + " with session: " + sessionId);

            // Create request and response
            HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
            HttpServletResponse response = createMockResponse();

            // Set up Struts ActionContext
            StrutsTestHelper.setupActionContext(request, response);

            // Set up the user for LogonAction
            User user = users.get(userId);
            logonAction.setUser(user);

            // Perform login
            String result = logonAction.login();

            // Verify login was successful
            assertEquals("Login should succeed", "success", result);

            // Get the CDM from the session
            MockHttpSession session = sessions.get(sessionId);
            CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);

            // Store for verification
            userSessionMap.put(userId, sessionId);
            sessionCdmMap.put(sessionId, cdm);

            // Verify CDM contains correct user
            assertNotNull("CDM should not be null", cdm);
            assertNotNull("User in CDM should not be null", cdm.getUser());
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());

            // Short delay to simulate real-world timing
            Thread.sleep(50);
        }

        // Verify no session mixup occurred
        for (Map.Entry<String, String> entry : userSessionMap.entrySet()) {
            String userId = entry.getKey();
            String sessionId = entry.getValue();
            CommonDataManager cdm = sessionCdmMap.get(sessionId);

            logger.info("Verifying user: " + userId + " with session: " + sessionId);

            // Verify user data in CDM
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());
            assertEquals("User name in CDM should match", "User " + userId, cdm.getUser().getUserName());
            assertEquals("User email in CDM should match", userId + "@example.com", cdm.getUser().getEmailId());
        }

        logger.info("Rapid sequential logins test completed successfully");
    }

    /**
     * Test scenario: Login-logout-login sequence
     * This test simulates a user logging in, logging out, and logging in again
     * to check if session data is properly cleaned up
     */
    @Test
    public void testLoginLogoutLoginSequence() throws Exception {
        logger.info("Starting login-logout-login sequence test");

        // Choose a test user
        String userId = "user0";
        User user = users.get(userId);

        // First login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId, sessionId1);
        HttpServletResponse response1 = createMockResponse();

        // Set up Struts ActionContext
        StrutsTestHelper.setupActionContext(request1, response1);

        logonAction.setUser(user);
        String result1 = logonAction.login();

        assertEquals("First login should succeed", "success", result1);

        // Verify first session
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);

        assertNotNull("CDM should not be null after first login", cdm1);
        assertEquals("User ID in CDM should match after first login", userId, cdm1.getUser().getId().getUserId());

        // Logout
        // Set up Struts ActionContext for logout
        StrutsTestHelper.setupActionContext(request1, response1);
        String logoutResult = logoutAction.logout(request1, response1);

        // Second login (same user)
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId, sessionId2);
        HttpServletResponse response2 = createMockResponse();

        // Set up Struts ActionContext for second login
        StrutsTestHelper.setupActionContext(request2, response2);

        logonAction.setUser(user);
        String result2 = logonAction.login();

        assertEquals("Second login should succeed", "success", result2);

        // Verify second session
        MockHttpSession session2 = sessions.get(sessionId2);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);

        assertNotNull("CDM should not be null after second login", cdm2);
        assertEquals("User ID in CDM should match after second login", userId, cdm2.getUser().getId().getUserId());

        // Verify first and second CDMs are different instances
        assertNotSame("First and second CDMs should be different instances", cdm1, cdm2);

        logger.info("Login-logout-login sequence test completed successfully");
    }
}
