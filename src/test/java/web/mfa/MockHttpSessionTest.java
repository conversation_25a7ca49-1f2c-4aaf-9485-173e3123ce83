package web.mfa;

import jakarta.servlet.http.HttpSessionBindingEvent;
import jakarta.servlet.http.HttpSessionBindingListener;
import org.junit.Test;
import org.swallow.util.CommonDataManager;


import static org.junit.Assert.*;

/**
 * Test class for the MockHttpSession implementation
 */
public class MockHttpSessionTest {
    
    /**
     * Test basic session operations
     */
    @Test
    public void testBasicSessionOperations() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Test initial state
        assertEquals("Session ID should match", "test-session", session.getId());
        assertTrue("Session should be valid", session.isValid());
        assertTrue("Session should be new", session.isNew());
        
        // Test attribute operations
        session.setAttribute("testAttr", "testValue");
        assertEquals("Attribute value should match", "testValue", session.getAttribute("testAttr"));
        
        session.removeAttribute("testAttr");
        assertNull("Attribute should be removed", session.getAttribute("testAttr"));
        
        // Test invalidation
        session.invalidate();
        assertFalse("Session should be invalid after invalidation", session.isValid());
        
        // Test operations on invalid session
        try {
            session.getAttribute("testAttr");
            // This should not throw an exception, just return null
        } catch (Exception e) {
            fail("getAttribute should not throw exception on invalid session");
        }
        
        try {
            session.setAttribute("testAttr", "testValue");
            // This should not throw an exception, just do nothing
        } catch (Exception e) {
            fail("setAttribute should not throw exception on invalid session");
        }
        
        try {
            session.getCreationTime();
            fail("getCreationTime should throw exception on invalid session");
        } catch (IllegalStateException e) {
            // Expected
        }
    }
    
    /**
     * Test HttpSessionBindingListener events
     */
    @Test
    public void testSessionBindingListenerEvents() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create a mock binding listener
        TestSessionBindingListener listener = new TestSessionBindingListener();
        
        // Test binding
        session.setAttribute("testAttr", listener);
        assertTrue("Bound event should be fired", listener.isBound());
        assertFalse("Unbound event should not be fired yet", listener.isUnbound());
        
        // Test unbinding via removeAttribute
        session.removeAttribute("testAttr");
        assertTrue("Unbound event should be fired", listener.isUnbound());
        
        // Reset listener
        listener.reset();
        
        // Test binding again
        session.setAttribute("testAttr", listener);
        assertTrue("Bound event should be fired again", listener.isBound());
        assertFalse("Unbound event should not be fired yet", listener.isUnbound());
        
        // Test unbinding via session invalidation
        session.invalidate();
        assertTrue("Unbound event should be fired on invalidation", listener.isUnbound());
    }
    
    /**
     * Test replacing an attribute that is a binding listener
     */
    @Test
    public void testReplaceBindingListener() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create two mock binding listeners
        TestSessionBindingListener listener1 = new TestSessionBindingListener();
        TestSessionBindingListener listener2 = new TestSessionBindingListener();
        
        // Bind the first listener
        session.setAttribute("testAttr", listener1);
        assertTrue("First listener should be bound", listener1.isBound());
        
        // Replace with the second listener
        session.setAttribute("testAttr", listener2);
        assertTrue("First listener should be unbound", listener1.isUnbound());
        assertTrue("Second listener should be bound", listener2.isBound());
    }
    
    /**
     * Test with CommonDataManager which implements HttpSessionBindingListener
     */
    @Test
    public void testWithCommonDataManager() {
        // Create a session
        MockHttpSession session = new MockHttpSession("test-session");
        
        // Create a CommonDataManager
        CommonDataManager cdm = new CommonDataManager();
        
        // Bind the CDM to the session
        session.setAttribute(org.swallow.util.SwtConstants.CDM_BEAN, cdm);
        
        // Get the CDM from the session
        CommonDataManager retrievedCdm = (CommonDataManager) session.getAttribute(org.swallow.util.SwtConstants.CDM_BEAN);
        
        // Verify it's the same instance
        assertSame("Retrieved CDM should be the same instance", cdm, retrievedCdm);
        
        // Invalidate the session
        session.invalidate();
        
        // Verify the CDM is no longer in the session
        assertNull("CDM should be removed from invalidated session", 
                  session.getAttribute(org.swallow.util.SwtConstants.CDM_BEAN));
    }
    
    /**
     * Mock implementation of HttpSessionBindingListener for testing
     */
    private static class TestSessionBindingListener implements HttpSessionBindingListener {
        private boolean bound = false;
        private boolean unbound = false;
        
        @Override
        public void valueBound(HttpSessionBindingEvent event) {
            bound = true;
        }
        
        @Override
        public void valueUnbound(HttpSessionBindingEvent event) {
            unbound = true;
        }
        
        public boolean isBound() {
            return bound;
        }
        
        public boolean isUnbound() {
            return unbound;
        }
        
        public void reset() {
            bound = false;
            unbound = false;
        }
    }
}
