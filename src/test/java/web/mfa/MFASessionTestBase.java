package web.mfa;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.After;
import org.junit.Before;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.swallow.control.model.UserAuthDetails;
import org.swallow.mfa.RegisterMFA;
import org.swallow.mfa.SamlService;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.util.SessionManager;
import org.swallow.web.LogonAction;
import org.swallow.web.LogoutAction;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.Mockito.*;

/**
 * Base class for MFA session tests with common utilities
 */
public abstract class MFASessionTestBase {

    protected static final Log logger = LogFactory.getLog(MFASessionTestBase.class);

    // Number of test users
    protected static final int NUM_USERS = 5;

    // Mock objects
    @Mock protected LogonManager logonManager;
    @Mock protected SamlService samlService;
    @Mock protected RegisterMFA registerMFA;
    @Mock protected TokensProvider tokensProvider;

    // Test objects
    protected LogonAction logonAction;
    protected LogoutAction logoutAction;
    protected SessionManager sessionManager;
    protected Map<String, MockHttpSession> sessions;
    protected Map<String, User> users;
    protected Map<String, String> mfaTokens;
    protected AtomicInteger sessionCounter;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);

        // Initialize test objects
        sessionManager = SessionManager.getInstance();
        logonAction = new LogonAction();
        logoutAction = new LogoutAction();
        sessions = new HashMap<>();
        users = new HashMap<>();
        mfaTokens = new HashMap<>();
        sessionCounter = new AtomicInteger(0);

        // Set up LogonAction
        logonAction.setLogonManager(logonManager);

        // Set up LogoutAction
        logoutAction.setLogonManager(logonManager);

        // Set up RegisterMFA mock
        when(registerMFA.isUseSmartAuthenticator()).thenReturn(true);
        when(registerMFA.isAllowInternalAuthentication()).thenReturn(true);
        when(registerMFA.isAllowUserCreationWithSmartAuthenticator()).thenReturn(true);

        // Create test users
        for (int i = 0; i < NUM_USERS; i++) {
            String userId = "user" + i;
            User user = createTestUser(userId);
            users.put(userId, user);
            mfaTokens.put(userId, "mfa-token-" + userId);
        }

        // Set up SamlService mock
        when(samlService.verifySamlToken(anyString())).thenAnswer(new Answer<SamlUserDTO>() {
            @Override
            public SamlUserDTO answer(InvocationOnMock invocation) throws Throwable {
                String token = invocation.getArgument(0);
                List<SamlUserDTO.UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
                for (Map.Entry<String, String> entry : mfaTokens.entrySet()) {
                    if (entry.getValue().equals(token)) {
                        SamlUserDTO dto = new SamlUserDTO();
                        dto.setUsername(entry.getKey());
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_USER_ID", "SMART_USER_ID", entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_ROLE_ID", "SMART_ROLE_ID", "ROLE_" + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("FIRST_NAME", "FIRST_NAME", "User " + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("EMAIL", "EMAIL", entry.getKey() + "@example.com"));
                        dto.setAttributes(attributes);
                        return dto;
                    }
                }
                return null;
            }
        });

        // Set up LogonManager mock
        when(logonManager.getUserDetailByExtAuthId(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });

        when(logonManager.getUserDetail(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });

        // Set up TokensProvider mock
        when(tokensProvider.createToken(any(User.class), anyString())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                User user = invocation.getArgument(0);
                return "token-" + user.getId().getUserId();
            }
        });

        when(tokensProvider.validateToken(any(HttpServletRequest.class))).thenReturn(true);
    }

    @After
    public void tearDown() {
        // Clean up sessions
        for (MockHttpSession session : sessions.values()) {
            try {
                if (session.isValid()) {
                    session.invalidate();
                }
            } catch (IllegalStateException e) {
                // Session already invalidated
            }
        }
        sessions.clear();
        users.clear();
        mfaTokens.clear();

        // Clean up Struts ActionContext
    }

    /**
     * Create a test user with the given ID
     */
    protected User createTestUser(String userId) {
        User user = new User();
        User.Id id = new User.Id();
        id.setHostId("HOST1");
        id.setUserId(userId);
        user.setId(id);
        user.setUserName("User " + userId);
        user.setRoleId("ROLE_" + userId);
        user.setStatus("1");
        user.setExtAuthId(userId);
        user.setEmailId(userId + "@example.com");
        user.setPhoneNumber("************");
        user.setCurrentEntity("ENTITY1");
        user.setCurrentCcyGrpId("CCY1");
        user.setLanguage("EN");
        user.setPassword("password");
        user.setPasswordChangeDate(new Date());
        user.setInvPassAttempt(0);

        // Set up UserAuthDetails
        UserAuthDetails authDetails = new UserAuthDetails();
        authDetails.setCsrfTokenArray(new String[] {"csrf-token-" + userId});
        user.setUserAuthDetails(authDetails);

        return user;
    }

    /**
     * Create a mock HttpServletRequest for MFA login
     */
    protected HttpServletRequest createMfaLoginRequest(String userId, String sessionId) {
        HttpServletRequest request = mock(HttpServletRequest.class);
        MockHttpSession session = new MockHttpSession(sessionId);
        sessions.put(sessionId, session);

        when(request.getSession()).thenReturn(session);
        when(request.getSession(anyBoolean())).thenReturn(session);
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        when(request.getParameter("logonflag")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        // Set up SwtUtil.isSucessfulMFARequest to return true
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));

        // Create parameter map for Struts
        Map<String, String[]> parameterMap = new HashMap<>();
        parameterMap.put("auth", new String[] {mfaTokens.get(userId)});
        when(request.getParameterMap()).thenReturn(parameterMap);

        return request;
    }

    /**
     * Create a mock HttpServletResponse
     */
    protected HttpServletResponse createMockResponse() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        return response;
    }
}
