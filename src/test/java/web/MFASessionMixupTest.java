package web;

import jakarta.servlet.ServletContext;
import jakarta.servlet.http.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.swallow.control.model.UserAuthDetails;
import org.swallow.mfa.RegisterMFA;
import org.swallow.mfa.SamlService;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.web.LogonAction;
import org.swallow.web.LogoutAction;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Test class to reproduce the MFA session mixup issue.
 * This test simulates multiple users logging in with MFA authentication
 * and checks for potential session mixups.
 */
public class MFASessionMixupTest {

    private static final Log logger = LogFactory.getLog(MFASessionMixupTest.class);
    
    // Number of test users
    private static final int NUM_USERS = 5;
    
    // Mock objects
    @Mock private LogonManager logonManager;
    @Mock private SamlService samlService;
    @Mock private RegisterMFA registerMFA;
    @Mock private TokensProvider tokensProvider;
    
    // Test objects
    private LogonAction logonAction;
    private SessionManager sessionManager;
    private Map<String, MockHttpSession> sessions;
    private Map<String, User> users;
    private Map<String, String> mfaTokens;
    private AtomicInteger sessionCounter;
    
    /**
     * Mock HttpSession implementation for testing
     */
    private class MockHttpSession implements HttpSession {
        private String id;
        private Map<String, Object> attributes = new HashMap<>();
        private boolean valid = true;
        private long creationTime;
        private long lastAccessedTime;
        
        public MockHttpSession(String id) {
            this.id = id;
            this.creationTime = System.currentTimeMillis();
            this.lastAccessedTime = this.creationTime;
        }
        
        @Override
        public String getId() {
            return id;
        }
        
        @Override
        public void setAttribute(String name, Object value) {
            if (!valid) return;
            
            Object oldValue = attributes.get(name);
            attributes.put(name, value);
            
            // Simulate HttpSessionBindingListener events
            if (oldValue instanceof HttpSessionBindingListener) {
                HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, oldValue);
                ((HttpSessionBindingListener) oldValue).valueUnbound(event);
            }
            
            if (value instanceof HttpSessionBindingListener) {
                HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, value);
                ((HttpSessionBindingListener) value).valueBound(event);
            }
        }
        
        @Override
        public Object getAttribute(String name) {
            if (!valid) return null;
            return attributes.get(name);
        }
        
        @Override
        public Enumeration<String> getAttributeNames() {
            if (!valid) return null;
            return new Enumeration<String>() {
                private String[] names = attributes.keySet().toArray(new String[0]);
                private int index = 0;
                
                @Override
                public boolean hasMoreElements() {
                    return index < names.length;
                }
                
                @Override
                public String nextElement() {
                    return names[index++];
                }
            };
        }
        
        @Override
        public void removeAttribute(String name) {
            if (!valid) return;
            
            Object value = attributes.remove(name);
            
            // Simulate HttpSessionBindingListener events
            if (value instanceof HttpSessionBindingListener) {
                HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, name, value);
                ((HttpSessionBindingListener) value).valueUnbound(event);
            }
        }
        
        @Override
        public void invalidate() {
            if (!valid) throw new IllegalStateException("Session already invalidated");
            
            // Notify all binding listeners
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                if (entry.getValue() instanceof HttpSessionBindingListener) {
                    HttpSessionBindingEvent event = new HttpSessionBindingEvent(this, entry.getKey(), entry.getValue());
                    ((HttpSessionBindingListener) entry.getValue()).valueUnbound(event);
                }
            }
            
            attributes.clear();
            valid = false;
        }
        
        @Override
        public boolean isNew() {
            return creationTime == lastAccessedTime;
        }
        
        @Override
        public long getCreationTime() {
            if (!valid) throw new IllegalStateException("Session already invalidated");
            return creationTime;
        }
        
        @Override
        public long getLastAccessedTime() {
            if (!valid) throw new IllegalStateException("Session already invalidated");
            return lastAccessedTime;
        }
        
        // Update last accessed time
        public void access() {
            this.lastAccessedTime = System.currentTimeMillis();
        }
        
        // Other required methods with minimal implementations
        @Override
        public void setMaxInactiveInterval(int interval) {}
        
        @Override
        public int getMaxInactiveInterval() {
            return 1800; // 30 minutes
        }
        
        @Override
        public ServletContext getServletContext() {
            return null;
        }
        


    }
    
    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // Initialize test objects
        sessionManager = SessionManager.getInstance();
        logonAction = new LogonAction();
        sessions = new HashMap<>();
        users = new HashMap<>();
        mfaTokens = new HashMap<>();
        sessionCounter = new AtomicInteger(0);
        
        // Set up LogonAction
        logonAction.setLogonManager(logonManager);
        
        // Set up RegisterMFA mock
        when(registerMFA.isUseSmartAuthenticator()).thenReturn(true);
        when(registerMFA.isAllowInternalAuthentication()).thenReturn(true);
        when(registerMFA.isAllowUserCreationWithSmartAuthenticator()).thenReturn(true);
        
        // Create test users
        for (int i = 0; i < NUM_USERS; i++) {
            String userId = "user" + i;
            User user = createTestUser(userId);
            users.put(userId, user);
            mfaTokens.put(userId, "mfa-token-" + userId);
        }
        
        // Set up SamlService mock
        when(samlService.verifySamlToken(anyString())).thenAnswer(new Answer<SamlUserDTO>() {
            @Override
            public SamlUserDTO answer(InvocationOnMock invocation) throws Throwable {
                String token = invocation.getArgument(0);
                List<SamlUserDTO.UserAttribute> attributes = new ArrayList<SamlUserDTO.UserAttribute>();
                for (Map.Entry<String, String> entry : mfaTokens.entrySet()) {
                    if (entry.getValue().equals(token)) {
                        SamlUserDTO dto = new SamlUserDTO();
                        dto.setUsername(entry.getKey());
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_USER_ID", "SMART_USER_ID", entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("SMART_ROLE_ID", "SMART_ROLE_ID", "ROLE_" + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("FIRST_NAME", "FIRST_NAME", "User " + entry.getKey()));
                        attributes.add(new SamlUserDTO.UserAttribute("EMAIL", "EMAIL", entry.getKey() + "@example.com"));
                        dto.setAttributes(attributes);
                        return dto;
                    }
                }
                return null;
            }
        });
        
        // Set up LogonManager mock
        when(logonManager.getUserDetailByExtAuthId(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });
        
        when(logonManager.getUserDetail(anyString(), anyString())).thenAnswer(new Answer<User>() {
            @Override
            public User answer(InvocationOnMock invocation) throws Throwable {
                String userId = invocation.getArgument(1);
                return users.get(userId);
            }
        });
        
        // Set up TokensProvider mock
        when(tokensProvider.createToken(any(User.class), anyString())).thenAnswer(new Answer<String>() {
            @Override
            public String answer(InvocationOnMock invocation) throws Throwable {
                User user = invocation.getArgument(0);
                return "token-" + user.getId().getUserId();
            }
        });
        
        when(tokensProvider.validateToken(any(HttpServletRequest.class))).thenReturn(true);
    }
    
    @After
    public void tearDown() {
        // Clean up sessions
        for (MockHttpSession session : sessions.values()) {
            try {
                session.invalidate();
            } catch (IllegalStateException e) {
                // Session already invalidated
            }
        }
        sessions.clear();
        users.clear();
        mfaTokens.clear();
    }
    
    /**
     * Create a test user with the given ID
     */
    private User createTestUser(String userId) {
        User user = new User();
        User.Id id = new User.Id();
        id.setHostId("HOST1");
        id.setUserId(userId);
        user.setId(id);
        user.setUserName("User " + userId);
        user.setRoleId("ROLE_" + userId);
        user.setStatus("1");
        user.setExtAuthId(userId);
        user.setEmailId(userId + "@example.com");
        user.setPhoneNumber("************");
        user.setCurrentEntity("ENTITY1");
        user.setCurrentCcyGrpId("CCY1");
        user.setLanguage("EN");
        user.setPassword("password");
        user.setPasswordChangeDate(new Date());
        user.setInvPassAttempt(0);
        
        // Set up UserAuthDetails
        UserAuthDetails authDetails = new UserAuthDetails();
        authDetails.setCsrfTokenArray(new String[] {"csrf-token-" + userId});
        user.setUserAuthDetails(authDetails);
        
        return user;
    }
    
    /**
     * Create a mock HttpServletRequest for MFA login
     */
    private HttpServletRequest createMfaLoginRequest(String userId, String sessionId) {
        HttpServletRequest request = mock(HttpServletRequest.class);
        MockHttpSession session = new MockHttpSession(sessionId);
        sessions.put(sessionId, session);
        
        when(request.getSession()).thenReturn(session);
        when(request.getSession(anyBoolean())).thenReturn(session);
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        when(request.getParameter("logonflag")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn("12*******");
        
        // Set up SwtUtil.isSucessfulMFARequest to return true
        when(request.getParameter("auth")).thenReturn(mfaTokens.get(userId));
        
        return request;
    }
    
    /**
     * Create a mock HttpServletResponse
     */
    private HttpServletResponse createMockResponse() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        return response;
    }
    
    /**
     * Test scenario 1: Rapid sequential logins
     * This test simulates multiple users logging in one after another
     * to check if session data gets mixed up
     */
    @Test
    public void testRapidSequentialLogins() throws Exception {
        logger.info("Starting rapid sequential logins test");
        
        // Track session data for verification
        Map<String, String> userSessionMap = new HashMap<>();
        Map<String, CommonDataManager> sessionCdmMap = new HashMap<>();
        
        // Perform sequential logins
        for (int i = 0; i < NUM_USERS; i++) {
            String userId = "user" + i;
            String sessionId = "session-" + sessionCounter.incrementAndGet();
            
            logger.info("Logging in user: " + userId + " with session: " + sessionId);
            
            // Create request and response
            HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
            HttpServletResponse response = createMockResponse();
            
            // Set up the user for LogonAction
            User user = users.get(userId);
            logonAction.setUser(user);
            
            // Perform login
            String result = logonAction.login();
            
            // Verify login was successful
            assertEquals("Login should succeed", "success", result);
            
            // Get the CDM from the session
            MockHttpSession session = sessions.get(sessionId);
            CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
            
            // Store for verification
            userSessionMap.put(userId, sessionId);
            sessionCdmMap.put(sessionId, cdm);
            
            // Verify CDM contains correct user
            assertNotNull("CDM should not be null", cdm);
            assertNotNull("User in CDM should not be null", cdm.getUser());
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());
            
            // Short delay to simulate real-world timing
            Thread.sleep(50);
        }
        
        // Verify no session mixup occurred
        for (Map.Entry<String, String> entry : userSessionMap.entrySet()) {
            String userId = entry.getKey();
            String sessionId = entry.getValue();
            CommonDataManager cdm = sessionCdmMap.get(sessionId);
            
            logger.info("Verifying user: " + userId + " with session: " + sessionId);
            
            // Verify user data in CDM
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());
            assertEquals("User name in CDM should match", "User " + userId, cdm.getUser().getUserName());
            assertEquals("User email in CDM should match", userId + "@example.com", cdm.getUser().getEmailId());
        }
        
        logger.info("Rapid sequential logins test completed successfully");
    }
    
    /**
     * Test scenario 2: Concurrent logins
     * This test simulates multiple users logging in concurrently
     * to check if race conditions cause session mixups
     */
    @Test
    public void testConcurrentLogins() throws Exception {
        logger.info("Starting concurrent logins test");
        
        final int numThreads = NUM_USERS;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(numThreads);
        final Map<String, String> userSessionMap = new HashMap<>();
        final Map<String, CommonDataManager> sessionCdmMap = new HashMap<>();
        final AtomicBoolean errorOccurred = new AtomicBoolean(false);
        
        // Create thread pool
        ExecutorService executor = Executors.newFixedThreadPool(numThreads);
        
        // Submit login tasks
        for (int i = 0; i < numThreads; i++) {
            final String userId = "user" + i;
            final String sessionId = "session-" + sessionCounter.incrementAndGet();
            
            executor.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Wait for all threads to be ready
                        startLatch.await();
                        
                        logger.info("Thread for user: " + userId + " starting login");
                        
                        // Create request and response
                        HttpServletRequest request = createMfaLoginRequest(userId, sessionId);
                        HttpServletResponse response = createMockResponse();
                        
                        // Set up the user for LogonAction
                        User user = users.get(userId);
                        logonAction.setUser(user);
                        
                        // Perform login
                        String result = logonAction.login();
                        
                        // Verify login was successful
                        if (!"success".equals(result)) {
                            logger.error("Login failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        // Get the CDM from the session
                        MockHttpSession session = sessions.get(sessionId);
                        CommonDataManager cdm = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
                        
                        // Store for verification
                        synchronized (userSessionMap) {
                            userSessionMap.put(userId, sessionId);
                            sessionCdmMap.put(sessionId, cdm);
                        }
                        
                        // Verify CDM contains correct user
                        if (cdm == null || cdm.getUser() == null || !userId.equals(cdm.getUser().getId().getUserId())) {
                            logger.error("CDM verification failed for user: " + userId);
                            errorOccurred.set(true);
                        }
                        
                        logger.info("Thread for user: " + userId + " completed login");
                    } catch (Exception e) {
                        logger.error("Exception in login thread for user: " + userId, e);
                        errorOccurred.set(true);
                    } finally {
                        finishLatch.countDown();
                    }
                }
            });
        }
        
        // Start all threads simultaneously
        startLatch.countDown();
        
        // Wait for all threads to finish
        finishLatch.await(30, TimeUnit.SECONDS);
        
        // Shut down executor
        executor.shutdown();
        executor.awaitTermination(5, TimeUnit.SECONDS);
        
        // Verify no errors occurred
        assertFalse("No errors should occur during concurrent logins", errorOccurred.get());
        
        // Verify no session mixup occurred
        for (Map.Entry<String, String> entry : userSessionMap.entrySet()) {
            String userId = entry.getKey();
            String sessionId = entry.getValue();
            CommonDataManager cdm = sessionCdmMap.get(sessionId);
            
            logger.info("Verifying user: " + userId + " with session: " + sessionId);
            
            // Verify user data in CDM
            assertEquals("User ID in CDM should match", userId, cdm.getUser().getId().getUserId());
            assertEquals("User name in CDM should match", "User " + userId, cdm.getUser().getUserName());
            assertEquals("User email in CDM should match", userId + "@example.com", cdm.getUser().getEmailId());
        }
        
        logger.info("Concurrent logins test completed successfully");
    }
    
    /**
     * Test scenario 3: Login while previous session exists
     * This test simulates a user logging in when they already have an active session
     * to check if the previous session is properly invalidated
     */
    @Test
    public void testLoginWithExistingSession() throws Exception {
        logger.info("Starting login with existing session test");
        
        // Choose a test user
        String userId = "user0";
        User user = users.get(userId);
        
        // First login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user);
        String result1 = logonAction.login();
        
        assertEquals("First login should succeed", "success", result1);
        
        // Verify first session
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        assertNotNull("CDM should not be null after first login", cdm1);
        assertEquals("User ID in CDM should match after first login", userId, cdm1.getUser().getId().getUserId());
        
        // Second login (same user)
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        logonAction.setUser(user);
        String result2 = logonAction.login();
        
        // The result might be "fail" with killSessionStatus="K" set in the request
        // This is expected behavior when a user already has an active session
        
        // Check if session1 was invalidated or marked for killing
        Object killSessionStatus = request2.getAttribute("killSessionStatus");
        if ("K".equals(killSessionStatus)) {
            logger.info("Session was marked for killing as expected");
        } else {
            // If not marked for killing, the first session should be invalidated
            try {
                session1.getAttribute(SwtConstants.CDM_BEAN);
                // If we get here, the session wasn't invalidated
                fail("First session should be invalidated or marked for killing");
            } catch (IllegalStateException e) {
                // Expected if session was invalidated
                logger.info("First session was invalidated as expected");
            }
        }
        
        logger.info("Login with existing session test completed");
    }
    
    /**
     * Test scenario 4: Logout and login sequence
     * This test simulates a user logging out and then logging back in
     * to check if session cleanup is properly handled
     */
    @Test
    public void testLogoutAndLogin() throws Exception {
        logger.info("Starting logout and login test");
        
        // Choose a test user
        String userId = "user0";
        User user = users.get(userId);
        
        // First login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user);
        String result1 = logonAction.login();
        
        assertEquals("First login should succeed", "success", result1);
        
        // Verify first session
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        assertNotNull("CDM should not be null after first login", cdm1);
        assertEquals("User ID in CDM should match after first login", userId, cdm1.getUser().getId().getUserId());
        
        // Logout
        LogoutAction logoutAction = new LogoutAction();
        logoutAction.setLogonManager(logonManager);
        
        String logoutResult = logoutAction.logout(request1, response1);
        
        // Second login (same user)
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        logonAction.setUser(user);
        String result2 = logonAction.login();
        
        assertEquals("Second login should succeed", "success", result2);
        
        // Verify second session
        MockHttpSession session2 = sessions.get(sessionId2);
        CommonDataManager cdm2 = (CommonDataManager) session2.getAttribute(SwtConstants.CDM_BEAN);
        
        assertNotNull("CDM should not be null after second login", cdm2);
        assertEquals("User ID in CDM should match after second login", userId, cdm2.getUser().getId().getUserId());
        
        // Verify first and second CDMs are different instances
        assertNotSame("First and second CDMs should be different instances", cdm1, cdm2);
        
        logger.info("Logout and login test completed");
    }
    
    /**
     * Test scenario 5: Simulate session mixup
     * This test deliberately tries to create conditions that might lead to session mixup
     */
    @Test
    public void testSimulateSessionMixup() throws Exception {
        logger.info("Starting session mixup simulation test");
        
        // Choose two test users
        String userId1 = "user0";
        String userId2 = "user1";
        User user1 = users.get(userId1);
        User user2 = users.get(userId2);
        
        // First user login
        String sessionId1 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request1 = createMfaLoginRequest(userId1, sessionId1);
        HttpServletResponse response1 = createMockResponse();
        
        logonAction.setUser(user1);
        String result1 = logonAction.login();
        
        assertEquals("First user login should succeed", "success", result1);
        
        // Get first user's CDM
        MockHttpSession session1 = sessions.get(sessionId1);
        CommonDataManager cdm1 = (CommonDataManager) session1.getAttribute(SwtConstants.CDM_BEAN);
        
        // Second user login
        String sessionId2 = "session-" + sessionCounter.incrementAndGet();
        HttpServletRequest request2 = createMfaLoginRequest(userId2, sessionId2);
        HttpServletResponse response2 = createMockResponse();
        
        // Simulate potential issue: Set the same CDM in both sessions
        // This is an artificial way to create a session mixup
        session1.setAttribute(SwtConstants.CDM_BEAN, cdm1);
        sessions.get(sessionId2).setAttribute(SwtConstants.CDM_BEAN, cdm1);
        
        // Now perform an action with the second user's session
        // This should detect the session mixup
        
        // Check which user is in the CDM of session2
        CommonDataManager cdm2 = (CommonDataManager) sessions.get(sessionId2).getAttribute(SwtConstants.CDM_BEAN);
        
        // This should fail if we've successfully simulated a session mixup
        assertEquals("Session mixup detected: wrong user in session2's CDM", 
                     userId1, cdm2.getUser().getId().getUserId());
        
        logger.info("Session mixup simulation test completed");
    }
}
