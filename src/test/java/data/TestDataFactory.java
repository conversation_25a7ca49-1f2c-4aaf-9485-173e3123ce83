package data;

import org.swallow.control.model.ProcessStatus;

import java.util.List;

public class TestDataFactory {

    public static List<Long> validMovementIds() {
        return List.of(1031315L, 2L, 3L);
    }

    public static String validHostId() {
        return "RABO";
    }

    public static String validEntityId() {
        return "RABONL2U";
    }

    public static String validRoleId() {
        return "VendorSuppor";
    }

    public static String validSequenceId() {
        return "244";
    }

    public static String validJson() {
        return "{}";
    }

    public static String validMovementId() {
        return "1031315";
    }
}