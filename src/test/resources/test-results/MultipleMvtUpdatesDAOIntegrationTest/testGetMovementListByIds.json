[{"forceNoLogs": false, "movementExt": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "currencyCode": "EUR", "bookCode": "UTMMTRAD", "valueDate": "2008-09-30T23:00:00.000+00:00", "amount": **********.0, "sign": "D", "movementType": "C", "accountId": "PGGMCLSEUR", "reference1": "********/0", "reference2": "SW507309", "reference4": "KOIM3665191", "counterPartyId": "RABOINA1", "beneficiaryId": "RABOINA1", "bookCodeAvail": "Y", "positionLevel": 3, "predictStatus": "I", "extractStatus": "E", "matchStatus": "E", "updateDate": "2012-07-09T23:00:00.000+00:00", "updateUser": "SYSTEM", "inputDate": "2008-10-01T09:37:46.000+00:00", "inputSource": "AUTOGEN", "messageFormat": "CALYPSO", "initialPredStatus": "I", "inputUser": "SYSTEM", "inputRole": "SYSTEM", "valueFromDate": "2008-10-01T11:48:54.000+00:00", "valueToDate": "2008-10-01T11:48:54.000+00:00", "account": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateDate": "2025-03-24T11:49:15.766+00:00", "swpdays": 0, "currAccess": 0, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "notesCount": 0, "openFlag": "N", "extBalStatus": "E", "toMatch": "Y", "ilmFcastStatus": "I", "successful": false, "id": {"forceNoLogs": false, "hostId": "RABO", "entityId": "RABONL2U", "movementId": 2, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, {"forceNoLogs": false, "movementExt": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "currencyCode": "EUR", "bookCode": "UTMMTRAD", "valueDate": "2008-09-30T23:00:00.000+00:00", "amount": **********.0, "sign": "D", "movementType": "C", "accountId": "SGSGCLSEUR", "reference1": "********/0", "reference2": "SW507309", "reference4": "KOIM3665191", "counterPartyId": "RABOINA1", "beneficiaryId": "RABOINA1", "bookCodeAvail": "Y", "positionLevel": 3, "predictStatus": "I", "extractStatus": "E", "matchStatus": "E", "updateDate": "2012-07-09T23:00:00.000+00:00", "updateUser": "SYSTEM", "inputDate": "2008-10-01T09:37:46.000+00:00", "inputSource": "AUTOGEN", "messageFormat": "CALYPSO", "initialPredStatus": "I", "inputUser": "SYSTEM", "inputRole": "SYSTEM", "valueFromDate": "2008-10-01T11:48:54.000+00:00", "valueToDate": "2008-10-01T11:48:54.000+00:00", "account": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateDate": "2025-03-24T11:49:15.795+00:00", "swpdays": 0, "currAccess": 0, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "notesCount": 0, "openFlag": "N", "extBalStatus": "E", "toMatch": "Y", "ilmFcastStatus": "I", "successful": false, "id": {"forceNoLogs": false, "hostId": "RABO", "entityId": "RABONL2U", "movementId": 3, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, {"forceNoLogs": false, "movementExt": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "currencyCode": "CHF", "bookCode": "CTCR", "valueDate": "2008-09-30T23:00:00.000+00:00", "amount": 1096990.0, "sign": "D", "movementType": "C", "accountId": "390802212CHF", "reference1": "****************", "reference2": "***************", "reference3": "CP4501100", "counterPartyId": "yyyyy", "bookCodeAvail": "N", "positionLevel": 8, "predictStatus": "E", "extractStatus": "E", "matchStatus": "E", "updateDate": "2008-10-01T09:30:24.000+00:00", "updateUser": "User15", "inputDate": "2008-10-01T09:22:10.000+00:00", "inputSource": "SWIFT", "messageFormat": "I900", "initialPredStatus": "E", "inputUser": "SYSTEM", "inputRole": "SYSTEM", "valueFromDate": "2008-10-01T11:48:54.000+00:00", "valueToDate": "2008-10-01T11:48:54.000+00:00", "account": {"forceNoLogs": false, "id": {"forceNoLogs": false, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateDate": "2025-03-24T11:49:15.795+00:00", "swpdays": 0, "currAccess": 0, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "notesCount": 0, "openFlag": "N", "extBalStatus": "I", "toMatch": "Y", "expectedSettlementDateTime": "2008-09-30T23:00:00.000+00:00", "ilmFcastStatus": "E", "successful": false, "id": {"forceNoLogs": false, "hostId": "RABO", "entityId": "RABONL2U", "movementId": 1031315, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}, "updateUserNeedUpdated": true, "updateDateNeedUpdated": true}]