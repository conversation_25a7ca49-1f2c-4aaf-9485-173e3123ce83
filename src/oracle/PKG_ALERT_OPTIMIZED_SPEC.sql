CREATE OR REPLACE PACKAGE TEST_1072_4.PKG_ALERT_OPTIMIZED
AS
/*
Modification History
****************************************************************************************************
VERSION  WHO          WHEN      WHAT
-------- ------------ --------- --------------------------------------------------------------------
1072.7   AI_OPTIMIZER 01/01/25  Performance Optimization Package Specification
                                - Added parallel processing capabilities for scenario instances
                                - Optimized XML parsing with caching mechanisms
                                - Added bulk operations for better performance
                                - Cached frequently accessed data to reduce database calls
                                - Optimized event processing with pre-compiled mappings
                                - Added performance monitoring and cache management
****************************************************************************************************
*/

   -- Constants for parallel processing configuration
   CONST_SYS_USER            CONSTANT VARCHAR2(10) := 'SYSTEM';
   CONST_DATE_FORMAT_EVENT   CONSTANT VARCHAR2(20) := 'DD/MM/YYYY';
   CONST_DECIMAL             CONSTANT VARCHAR2(1) := '.';
   CONST_INSTANCE_ID_ATTR    CONSTANT VARCHAR2(20) := 'INSTANCE_ID';
   CONST_LITERAL_USE_TYPE    CONSTANT VARCHAR2(10) := 'LITERAL';
   CONST_IGNORE_USE_TYPE     CONSTANT VARCHAR2(10) := 'IGNORE';

   -- Type definitions for optimized processing
   TYPE T_SCENARIO_INSTANCE_ROW IS RECORD (
      INSTANCE_UNIQUE_EXPRESSION  VARCHAR2(4000),
      UNIQUE_IDENTIFIER          VARCHAR2(4000),
      HOST_ID                    VARCHAR2(50),
      ENTITY_ID                  VARCHAR2(50),
      CURRENCY_CODE              VARCHAR2(3),
      ACCOUNT_ID                 VARCHAR2(50),
      AMOUNT                     NUMBER,
      SIGN                       VARCHAR2(1),
      MOVEMENT_ID                NUMBER,
      MATCH_ID                   NUMBER,
      SWEEP_ID                   NUMBER,
      PAYMENT_ID                 NUMBER,
      OVER_THRESHOLD             VARCHAR2(1),
      VALUE_DATE                 DATE,
      OTHER_ID                   VARCHAR2(50),
      JSON_ATTRIBUTES            CLOB,
      IS_UNIQUE_ROW              VARCHAR2(1)
   );

   -- Main optimized procedures and functions
   
   -- Optimized scenario processing with parallel execution
   PROCEDURE SP_PROCESS_SCENARIO_PARALLEL (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                           P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                           P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                           P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL);

   -- Process a chunk of instances in parallel
   PROCEDURE SP_PROCESS_INSTANCE_CHUNK (P_START_ID         NUMBER,
                                        P_END_ID           NUMBER,
                                        P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                        P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE);

   -- Optimized event launching with caching
   FUNCTION FN_LAUNCH_SCEN_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                            P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER;

   -- Optimized single event processing with pre-compiled mappings
   FUNCTION FN_PROCESS_SINGLE_EVENT_OPTIMIZED (P_SCENARIO_INSTANCE_ID  P_SCENARIO_INSTANCE.ID%TYPE,
                                               P_EVENT_FACILITY_ID     NUMBER,
                                               P_PROGRAM_ID            NUMBER,
                                               P_MAP_KEY               VARCHAR2,
                                               P_PARAMETERS_XML        CLOB,
                                               P_USER_ID               VARCHAR2)
   RETURN NUMBER;

   -- Optimized SQL builders for different event types
   FUNCTION FN_BUILD_INSERT_MOVEMENT_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                         P_EVENT_FACILITY_ID     NUMBER,
                                         P_MAP_KEY               VARCHAR2,
                                         P_PARAMETERS_XML        CLOB,
                                         P_COL_AUTO_COL          VARCHAR2,
                                         P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB;

   FUNCTION FN_BUILD_UPDATE_MOVEMENT_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                         P_EVENT_FACILITY_ID     NUMBER,
                                         P_MAP_KEY               VARCHAR2,
                                         P_PARAMETERS_XML        CLOB,
                                         P_COL_AUTO_COL          VARCHAR2,
                                         P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB;

   FUNCTION FN_BUILD_SWEEP_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                P_EVENT_FACILITY_ID     NUMBER,
                                P_MAP_KEY               VARCHAR2,
                                P_PARAMETERS_XML        CLOB,
                                P_USER_ID               VARCHAR2)
   RETURN CLOB;

   FUNCTION FN_BUILD_BALANCE_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                 P_EVENT_FACILITY_ID     NUMBER,
                                 P_MAP_KEY               VARCHAR2,
                                 P_PARAMETERS_XML        CLOB,
                                 P_COL_AUTO_COL          VARCHAR2,
                                 P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB;

   FUNCTION FN_BUILD_MESSAGE_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                 P_PARAMETERS_XML        CLOB)
   RETURN CLOB;

   FUNCTION FN_BUILD_ACCOUNT_ATTR_SQL (P_SCENARIO_INSTANCE_ID  NUMBER,
                                      P_EVENT_FACILITY_ID     NUMBER,
                                      P_MAP_KEY               VARCHAR2,
                                      P_PARAMETERS_XML        CLOB,
                                      P_COL_AUTO_COL          VARCHAR2,
                                      P_COL_AUTO_VAL          VARCHAR2)
   RETURN CLOB;

   -- Helper procedures
   PROCEDURE SP_PROCESS_OLD_INSTANCES_PARALLEL (P_SCENARIO_ID  P_SCENARIO.SCENARIO_ID%TYPE,
                                                P_USER_ID      VARCHAR2);

   -- Wrapper procedures for backward compatibility
   PROCEDURE SP_PROCESS_SCENARIO (P_SCENARIO_ID      P_SCENARIO.SCENARIO_ID%TYPE,
                                  P_USER_ID          P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER,
                                  P_QUERY_TEXT       P_SCENARIO.QUERY_TEXT%TYPE DEFAULT NULL,
                                  P_SCENARIO_SCHEDULE_ID P_SCENARIO_SCHEDULE.SCENARIO_SCHEDULE_ID%TYPE DEFAULT NULL);

   FUNCTION FN_LAUNCH_SCEN_EVENT (P_SCENARIO_INSTANCE_ID      P_SCENARIO_INSTANCE.ID%TYPE,
                                  P_USER_ID                   P_SCENARIO_INSTANCE_LOG.LOG_USER%TYPE DEFAULT CONST_SYS_USER)
   RETURN NUMBER;

   -- Cache management procedures
   PROCEDURE SP_CLEAR_CACHE;

   PROCEDURE SP_WARM_CACHE (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE DEFAULT NULL);

   -- Performance monitoring
   FUNCTION FN_GET_PERFORMANCE_STATS (P_SCENARIO_ID P_SCENARIO.SCENARIO_ID%TYPE DEFAULT NULL)
   RETURN VARCHAR2;

END PKG_ALERT_OPTIMIZED;
/
