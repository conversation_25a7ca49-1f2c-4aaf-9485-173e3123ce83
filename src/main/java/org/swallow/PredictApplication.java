package org.swallow;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.Locale;

import org.apache.catalina.connector.Connector;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.swallow.config.ApplicationProperties;
import org.swallow.config.TomcatEnabledCondition;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;

@SpringBootApplication
@ComponentScan
@EnableScheduling
@PropertySource("classpath:application.properties")
@EnableConfigurationProperties({ApplicationProperties.class})
public class PredictApplication {
    private static final Logger log = LoggerFactory.getLogger(PredictApplication.class);
    

    public PredictApplication() {

	}

    public static void main(String[] args) {
    	// System.setProperty("spring.devtools.restart.enabled", "false");
    	// System.setProperty(org.apache.tomcat.util.scan.Constants.SKIP_JARS_PROPERTY, "*.jar");
		String pcmEnabled = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.PCM_ENABLED);
		if ("true".equals(pcmEnabled)) {
			System.setProperty("action_package_list", "org.swallow.action, org.swallow.web, org.swallow.control.web,org.swallow.work.web,org.swallow.export.web,org.swallow.maintenance.web,org.swallow.reports.web,"
					+ "org.swallow.pcm.maintenance.web,org.swallow.pcm.report.web,org.swallow.pcm.control.web,org.swallow.pcm.work.web");
		}else {
			System.setProperty("action_package_list", "org.swallow.action, org.swallow.web, org.swallow.control.web, org.swallow.work.web,  org.swallow.export.web, org.swallow.maintenance.web, org.swallow.reports.web");
		}
    	ConfigurableApplicationContext appContext = SpringApplication.run(PredictApplication.class, args);

    	// Log application 
		Environment env = appContext.getEnvironment();
		logApplicationStartup(env);
        log.info("Application '"+env.getProperty("spring.application.name")+"' startup complete for profiles: "+Arrays.asList(env.getActiveProfiles()));
    }


    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList("http://localhost:4300","http://localhost:4400","http://localhost:8080/swallowtech"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("Requestor-Type", "Authorization"));
        configuration.setExposedHeaders(Arrays.asList("X-Get-Header"));
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Set autoGrowNestedPaths to false for all controllers
        binder.setAutoGrowNestedPaths(false);
    }

    @Bean
    @Conditional(TomcatEnabledCondition.class)
    public ConfigurableServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector connector) {
            	connector.setProperty("relaxedPathChars", "[]|{}^&#x5c;&#x60;&quot;&lt;&gt;&period;.&#46;U+002E");
                connector.setProperty("relaxedQueryChars", "[]|{}^&#x5c;&#x60;&quot;&lt;&gt;&period;.&#46;U+002E");
            }
        });
        return factory;
    }
    

    private static void logApplicationStartup(Environment env) {
        String protocol = "http";
        if (env.getProperty("server.ssl.key-store") != null) {
            protocol = "https";
        }
        String serverPort = env.getProperty("server.port");
        String contextPath = env.getProperty("server.servlet.context-path");
        if (StringUtils.isBlank(contextPath)) {
            contextPath = "/";
        }
        String hostAddress = "localhost";
        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("The host name could not be determined, using `localhost` as fallback");
        }
        System.out.println(String.format(
        		"\n----------------------------------------------------------\n\t" +
                "Application '%s' is running! Access URLs:\n\t" +
                "Local: \t\t%s://localhost:%s%s\n\t" +
                "External: \t%s://%s:%s%s\n\t" +
                "Profile(s): \t%s\n----------------------------------------------------------",
            env.getProperty("spring.application.name"),
            protocol,
            serverPort,
            contextPath,
            protocol,
            hostAddress,
            serverPort,
            contextPath,
            Arrays.asList(env.getActiveProfiles())));
    }

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver resolver = new SessionLocaleResolver();
        resolver.setDefaultLocale(Locale.ENGLISH);
        return resolver;
    }

    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("dictionary");
        return messageSource;
    }


}
