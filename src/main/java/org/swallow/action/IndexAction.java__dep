package org.swallow.action;

import com.opensymphony.xwork2.ActionSupport;
import lombok.Getter;
import lombok.Setter;
import net.minidev.json.JSONObject;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;
import org.swallow.service.UserService;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Getter
@Setter
public class IndexAction extends CustomActionSupport {
    private static final long serialVersionUID = 1L;

    @Override
    @Action(value = "/index", results = {
    		@Result(location = "main.jsp", name = "success")
    })
    public String execute() {
        return SUCCESS;
    }
}