/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.Type;
import org.hibernate.usertype.CompositeUserType;
import org.swallow.util.Batcher;
import org.swallow.util.JDBCExceptionReporter;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class UserProfileSequenceGenerator implements IdentifierGenerator, Configurable, Serializable{
	
	private final Log log = LogFactory.getLog(UserProfileSequenceGenerator.class); // Log object 	
	
	public static final String HIBERNATE_SEQUENCETABLE = "P_SEQUENCE";	 // Default value of sequence table
	
    public static final String SCHEMA = "schema"; 						// constant string for schema word
        
    public static  String _sequenceName = "S_USER_PROFILE_DETAIL_SEQUENCE";				// oracle sequence name
        
    private String _query = null;										//  sql select query
    
    
    
    
    /* (non-Javadoc)
     * @see net.sf.hibernate.id.Configurable#configure(net.sf.hibernate.type.Type, java.util.Properties, net.sf.hibernate.dialect.Dialect)
     */
    public void configure(Type type, Properties params, Dialect dialect) 
    throws MappingException 
	{ 
		     
	//  get the schema name   
    String schemaName = params.getProperty(SCHEMA);
    
    // appends the schema name in table name if doesn't exits           
    if(schemaName != null && _sequenceName.indexOf('.') < 0)
    	_sequenceName = schemaName + '.' + _sequenceName;
    
    // preparing the sql select query  
	
    _query  = "select "+_sequenceName + ".nextval from dual";
    

    log.debug("sql select query - " + _query);

    
	} 
    
    public Serializable generate(SessionImplementor session, Object obj) 
    throws SQLException, HibernateException 
	{ 
    		// UserProfileDetail Object 
    		UserProfileDetail  userProfileDetailObj = null;
    		
    		// UserProfileDetail Id Object
    		UserProfileDetail.Id  userProfileDetailIdObj = null;
    		
    		// PreparedStatement for select query
		    PreparedStatement stSelect = Batcher.of(session).prepareStatement(_query);
		    
		    // Resultset for select query result
		    ResultSet rsSelect = null;
    		
	    	try 
			{	    		
				 			   
			    if(obj == null || !obj.getClass().equals(UserProfileDetail.class))
			    {
			    	throw new HibernateException("Model object is null or object type is not org.swallow.work.UserProfileDetail  ");
			    }
			    else
			    {
			    	// typecasting the object into UserProfileDetail class 
			    	userProfileDetailObj = (UserProfileDetail)obj;
			    	
			    	// Getting the Id oject from UserProfileDetail Object
			    	userProfileDetailIdObj = userProfileDetailObj.getId();
			    	 

			        // Executing  the sql select query 
			    	rsSelect = stSelect.executeQuery();
			        
			    	long sequenceValue = -1 ;
			    	
			    	if(rsSelect.next())
			    		sequenceValue = rsSelect.getLong(1);
			    	if(sequenceValue == -1)
			    		throw new HibernateException("Could not fecth the sequence number");
			    	
			    	log.debug("Got the next sequence value - " + sequenceValue ); 
			    	
			    	// Setting the movementid in movementid object
			    	userProfileDetailIdObj.setSequenceNumber(new Long(sequenceValue));			    				    	
			    	
			    }
			} 
			catch (SQLException sqle) 
			{ 
			       JDBCExceptionReporter.logExceptions(sqle); 
			       throw sqle; 
			} 
			finally 
			{ 
				if(rsSelect != null && stSelect != null)
					Batcher.of(session).closeQueryStatement(stSelect,rsSelect);				
		}
		    		    	    	        
			return userProfileDetailIdObj;
	}
    
	@Override
	public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
		try {
			return generate((SessionImplementor)session, object);
		} catch (Exception e) {
			throw new HibernateException(e);
		}
	}
}
		
