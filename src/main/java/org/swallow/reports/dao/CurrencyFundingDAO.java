/*
 * @(#)CurrencyFundingDAO.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.dao;

import java.util.Collection;

import jakarta.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;

/**
 * 
 * <AUTHOR> 
 * CurrencyFundingDAO interface, contains collection of methods
 * to be used in DAO layer.
 * 
 */

public interface CurrencyFundingDAO extends DAO {

	/**
	 * Get the Account details from the database
	 * 
	 * @param String hostId
	 * @param String entityId
	 * @param String currencyCode
	 * @return Collection 
	 */
	public Collection getAccountDetails(String hostId, String entityId,
			String currencyCode) throws SwtException;

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request - HttpServletRequest
	 * @param hostId - String
	 * @param entityId - String
	 * @param entityName - String
	 * @param currencyCode - String
	 * @param currencyName - String
	 * @param valueDate - String
	 * @param selectedAcctId - String
	 * @param accountName - String
	 * @param thresholdValue -String
	 * @param showDR - String
	 * @param showCR - String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getCurrencyFundingReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String currencyCode, String currencyName, String selectedAcctId,
			String accountName, String valueDate, String thresholdValue,
			String showDR, String showCR, String dateFormat) throws SwtException;

}
