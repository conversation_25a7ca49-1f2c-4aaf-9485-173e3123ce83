/*
 * @(#)TurnoverReportDAOHibernate.java 1.0 31/07/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.reports.dao.hibernate;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.TurnoverReportDAO;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;


import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * <AUTHOR>
 *
 * Class that implements the TurnoverReportDAO and acts as DAO
 * layer for all database operations
 *
 */

@Repository ("turnoverReportDAO")
@Transactional
public class TurnoverReportDAOHibernate extends CustomHibernateDaoSupport implements
		TurnoverReportDAO {
	public TurnoverReportDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/* Initializing Log variable for logging comments */
	private static final Log log = LogFactory
			.getLog(TurnoverReportDAOHibernate.class);

	/**
	 * This method is used to compile and return the reports
	 *
	 * @param request - HttpServletRequest
	 * @param hostId - String
	 * @param entityId - String
	 * @param entityName - String
	 * @param reportDate - String
	 * @param showMain - String
	 * @param showSub - String
	 * @return JasperPrint
	 * @throws SwtException
	 *
	 */
	public JasperPrint getTurnoverReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String reportFromDate,String reportToDate, String showMain, String showSub, String dataSource, String dataSourceName, String dateFormatValue)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Entering ");

		/* Method's local variable and class instance declaration */
		Connection connection = null;
		DataSource ds = null;
		JasperReport jasperReport = null;
		JasperPrint jasperPrint = null;
		List pagesize = null;
		Date fromDate;
		Date toDate;
		/*START:Code added by Mahesh on 01-Mar-2010 for Mantis 1112 : All amount values should be displayed based
		 * amount formates defined in General System parameters screen */
		SystemFormats sysformat = null;
		String currencyFormat = null;


		try {
			if(request != null) {
				sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
				currencyFormat = sysformat.getCurrencyFormat();
			}else {
				currencyFormat = SwtUtil.getCurrentCurrencyFormat(null);
			}
			/*END:Code added by Mahesh on 01-Mar-2010 for Mantis 1112 */

			/* Declare Map to send the parameters to the Japser Engine */
			Map parms = new HashMap();

			/* Jasper file will be compiled */
			jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath
					+ SwtConstants.TURNOVER_REPORT_FILE);
			log.debug("report complied");
			/* Get the datasource object from the bean and get the connection */
			ds = (DataSource) SwtUtil.getBean("dataSource");
			connection = ds.getConnection();
			/* parse the report date as current system date format */
			fromDate = dateFormatValue!=null ? SwtUtil.parseDate(reportFromDate, dateFormatValue):SwtUtil.parseDateGeneral(reportFromDate);
			toDate = dateFormatValue!=null ? SwtUtil.parseDate(reportToDate, dateFormatValue):SwtUtil.parseDateGeneral(reportToDate);
			/* Preparing the parameters to be passed to Jasper Engine. */
			
			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
			parms.put("pEntity_Id", entityId);
			parms.put("pEntity_Name", entityName);
			parms.put("pReport_DateFrom", fromDate);
			parms.put("pReport_DateTo", toDate);
			parms.put("pShow_Main", showMain);
			parms.put("pShow_Sub", showSub);
			parms.put("pUseDataSource", dataSource);
			parms.put("pDataSourceName", dataSourceName);
			parms.put("pDateFormat", dateFormatValue);
			/*START:Code added by Mahesh on 01-Mar-2010 for Mantis 1112 : Pass the currency format
			 * pattern to the Jasper engine*/
			parms.put("pCurrencyPattern",currencyFormat);
			/*END:Code added by Mahesh on 01-Mar-2010 for Mantis 1112 */

			/* Compiled Opportunity cost Report will be filled here. */
			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
					connection);
			log.debug("Report Filled");
			/* To get the page size */
			pagesize = jasperPrint.getPages();
			/*
			 * If the page size is Zero Empty datasource will be passed to avoid
			 * the blank report.
			 */
			if (pagesize.size() == 0) {
				jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
						new JREmptyDataSource(1));

			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getTurnoverReport] method - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getTurnoverReport] method - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getTurnoverReport",
							TurnoverReportDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());

		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(connection);
			
		}

		log.debug(this.getClass().getName()
				+ "- [getTurnoverReport] - Exiting ");
		return jasperPrint;
	}

	/**
	 * This method is used to generate data for the turnover excel report
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportFromDate
	 * @param reportToDate
	 * @param showMain
	 * @param showSub
	 * @param dataSource
	 * @param dataSourceName
	 * @param dateFormatValue
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Map<String, Object>> getExcelTurnoverReportData(HttpServletRequest request, String hostId,
			String entityId, String entityName, String reportFromDate, String reportToDate, String showMain,
			String showSub, String dataSource, String dataSourceName, String dateFormatValue) throws SwtException {
		
		ArrayList<Map<String, Object>> beansList = new ArrayList<Map<String, Object>>();
		LinkedList<HashMap<String, Object>> data = null;
		Date fromDate = null;
		Date toDate = null;
		try {
			
				fromDate = SwtUtil.parseDateGeneral(reportFromDate);
				toDate = SwtUtil.parseDateGeneral(reportToDate);
				data = SwtUtil.executeNamedSelectQuery("turnover_report_query", hostId, entityId, showMain, showSub,  fromDate, toDate,  dataSource, dataSource);
				beansList.add(getBeansMap(data, request,
						entityId, entityName, reportFromDate, reportToDate, showMain, showSub, dataSource, dataSourceName, dateFormatValue));
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [getExcelTurnoverReportData] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getExcelTurnoverReportData",
					ReportsDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());
		}
		return beansList;
		
		
		
		
	}
	
	public Map<String, Object> getBeansMap(LinkedList<HashMap<String, Object>> allData, HttpServletRequest request,
			final String entityId, final String entityName, final String reportFromDate, final String reportToDate, final String showMain,
			final String showSub, final String dataSource, final String dataSourceName, final String dateFormatValue) throws Exception{
		
		LinkedList<TurnoverItem> turnoverList = null;
		TurnoverItem turnOverItem = null;
		Map<String, Object> beans = new HashMap<String, Object>();
		HttpSession session = null;
		try {
			if(request != null) {
				session = request.getSession();
			}else {
				if(UserThreadLocalHolder.getUserSession() != null)
					session = UserThreadLocalHolder.getUserSession();
				else
					session = null;
			}
			HashMap<String, String> dictionary = new HashMap<String, String>();
			dictionary.put("report_title", SwtUtil.getMessageFromSession("turnoverReport.reportTitle", session));
			dictionary.put("entity", SwtUtil.getMessageFromSession("turnoverReport.entity", session));
			dictionary.put("datasource", SwtUtil.getMessageFromSession("turnoverReport.dataSource", session));
			dictionary.put("reportingperiod", SwtUtil.getMessageFromSession("turnoverReport.reportingperiod", session));
			dictionary.put("to", SwtUtil.getMessageFromSession("turnoverReport.to", session));
			dictionary.put("currency", SwtUtil.getMessageFromSession("turnoverReport.currency", session));
			dictionary.put("credit", SwtUtil.getMessageFromSession("turnoverReport.credit", session));
			dictionary.put("debit", SwtUtil.getMessageFromSession("turnoverReport.debit", session));
			dictionary.put("turnover", SwtUtil.getMessageFromSession("turnoverReport.turnoverdic", session));
			dictionary.put("main", SwtUtil.getMessageFromSession("turnoverReport.main", session));
			dictionary.put("sub", SwtUtil.getMessageFromSession("turnoverReport.sub", session));
			
			
			beans.put("dic", dictionary);
			
			// Header
			HashMap<String, Object> header = new HashMap<String, Object>(){
				{
					put("entity_id", entityId);
					put("dataSource_name", dataSourceName);
					put("entity_name", entityName);
					put("date_from",reportFromDate);
					put("date_to",reportToDate);
					put("showmain",showMain);
					put("showsub",showSub);
				}
			};
			
			beans.put("header", header);
			HashMap<String, Object> item = null;
			turnoverList = new LinkedList<TurnoverReportDAOHibernate.TurnoverItem>();
			for(int i = 0 ; i  < allData.size() ; i++) {
				item = allData.get(i);
				turnOverItem = new TurnoverItem();
				turnOverItem.setCurrency(""+item.get("currency_code"));
				
				turnOverItem.setMainCredit(getValueOf(item.get("cr_main_value")));
				turnOverItem.setMainDebit(getValueOf(item.get("dr_main_value")));
				turnOverItem.setMainTotal(getValueOf(item.get("sum_main_value")));
				
				turnOverItem.setSubCredit(getValueOf(item.get("cr_sub_value")));
				turnOverItem.setSubDebit(getValueOf(item.get("dr_sub_value")));
				turnOverItem.setSubTotal(getValueOf(item.get("sum_sub_value")));
				
				turnOverItem.setTotalCredit(getValueOf(item.get("credit")));
				turnOverItem.setTotalDebit(getValueOf(item.get("debit")));
				turnOverItem.setTotalTurnover(getValueOf(item.get("sum_turnover")));
				
				turnoverList.add(turnOverItem);
			}
			
			beans.put("reportData", turnoverList);
		
		}catch(Exception exp) {
			log.error(this.getClass().getName() + "- [getExcelTurnoverReportData] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getExcelTurnoverReportData",
					ReportsDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());
			
		}
		return beans;
	}
	
	protected static Double getValueOf(Object tmp) throws Exception{
		if(tmp != null && tmp.toString().length() > 0) {
			return Double.parseDouble(tmp.toString());
		}
		else 
			return null;
	}
	
	
	public static class TurnoverItem {
		private String currency = null;
		private Double mainCredit = null;
		private Double mainDebit = null;
		private Double subCredit = null;
		private Double subDebit = null;
		private Double mainTotal = null;
		private Double subTotal = null;
		private Double totalDebit = null;
		private Double totalCredit = null;
		private Double totalTurnover = null;
		public String getCurrency() {
			return currency;
		}
		public void setCurrency(String currency) {
			this.currency = currency;
		}
		public Double getMainCredit() {
			return mainCredit;
		}
		public void setMainCredit(Double mainCredit) {
			this.mainCredit = mainCredit;
		}
		public Double getMainDebit() {
			return mainDebit;
		}
		public void setMainDebit(Double mainDebit) {
			this.mainDebit = mainDebit;
		}
		public Double getSubCredit() {
			return subCredit;
		}
		public void setSubCredit(Double subCredit) {
			this.subCredit = subCredit;
		}
		public Double getSubDebit() {
			return subDebit;
		}
		public void setSubDebit(Double subDebit) {
			this.subDebit = subDebit;
		}
		public Double getMainTotal() {
			return mainTotal;
		}
		public void setMainTotal(Double mainTotal) {
			this.mainTotal = mainTotal;
		}
		public Double getSubTotal() {
			return subTotal;
		}
		public void setSubTotal(Double subTotal) {
			this.subTotal = subTotal;
		}
		public Double getTotalDebit() {
			return totalDebit;
		}
		public void setTotalDebit(Double totalDebit) {
			this.totalDebit = totalDebit;
		}
		public Double getTotalCredit() {
			return totalCredit;
		}
		public void setTotalCredit(Double totalCredit) {
			this.totalCredit = totalCredit;
		}
		public Double getTotalTurnover() {
			return totalTurnover;
		}
		public void setTotalTurnover(Double totalTurnover) {
			this.totalTurnover = totalTurnover;
		}
		
		
		
	}
	
}