/*
 * @(#)InterestChargesDAOHibernate.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import javax.sql.DataSource;

import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.InterestChargesDAO;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;

/**
 * 
 * <AUTHOR> A Class that implements the InterestChargesDAO and acts as
 *         DAO layer for all database operations
 * 
 */
@Repository ("interestChargesDAO")
@Transactional
public class InterestChargesDAOHibernate extends CustomHibernateDaoSupport implements
		InterestChargesDAO {
	public InterestChargesDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/*
	 * Final instance for Log
	 */
	private static final Log log = LogFactory
			.getLog(InterestChargesDAOHibernate.class);

	/**
	 * Get the distinct AccountId and AccountName from Database table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return Collection object
	 */
	public Collection getMainAccountDetails(String hostId, String entityId,
			String currencyCode) {

		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet res = null;
		Collection currentStatus = null;
		StringBuffer acctDetailQuery = null;
		try {

			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();
			/* Object initialization */
			currentStatus = new ArrayList();
			/* Query for getting accountId from p_intraday_stats */
			acctDetailQuery = new StringBuffer(
					"select account_id, account_name from p_account where host_id=? and entity_id=? and ACCOUNT_CLASS='N'");
			if (!currencyCode.equals("All")) {
				acctDetailQuery.append("and currency_code=? ");
			}
			acctDetailQuery.append(" order by account_id, account_level ");
			stmt = conn.prepareStatement(acctDetailQuery.toString());
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			if (!currencyCode.equals("All")) {
				stmt.setString(3, currencyCode);
			}
			res = stmt.executeQuery();

			/* Getting the accountId ,accountName and put it in List */

			while (res.next()) {
				currentStatus.add(res.getString(1));
				currentStatus.add(res.getString(2));

			}
		} catch (SQLException e) {

			log
					.error(this.getClass().getName()
							+ "- [getMainAccountDetails] - Exception "
							+ e.getMessage());
		} finally {
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(res, stmt, conn, null);

			try {
				if (conn != null)
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ " - [getMainAccountDetails] - Exception - "
						+ e.getMessage());
			}
			// END : code modified by KaisBS for the mantis 1745 to protect the
			// closer instructions
		}
		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Exit ");

		return currentStatus;
	}

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * This method is used to compile and retrieve the interest charges details
	 * from jasper report
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @param entityName -
	 *            String
	 * @param currencyId -
	 *            String
	 * @param currencyName -
	 *            String
	 * @param accountId -
	 *            String
	 * @param accountName -
	 *            String
	 * @param fromDate -
	 *            String
	 * @param toDate -
	 *            String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getInterestChargesReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String currencyId, String currencyName, String accountId,
			String accountName, String fromDate, String toDate, String savedDateFormat)
			throws SwtException {
		// Connection to retrieve the InterestCharges details
		Connection connection = null;
		// DataSource to get the connection
		DataSource dataSource = null;
		// logged-in user's session
		HttpSession session = null;
		// Common Data manager to get date format
		CommonDataManager commonDataManager = null;
		// To hold the start date in system format
		Date startDate = null;
		// To hold the endDate in system format
		Date endDate = null;
		// To hold interest charges in Jasper Report
		JasperReport jasperReport = null;
		// To hold interest charges in Jasperprint
		JasperPrint jasperPrint = null;
		// To hold the pdf size from jasper print
		List pageSize = null;
		// To hold time difference between from date and todate in milliseconds
		long timeDiff;
		// To hold total number of days between from date and todate
		long dayDiff;
		// To hold the InterestCharges parameters to jasper report
		HashMap jasperParams = null;
	    //edited by Med amine in Mantis_2068
        String dateFormat="";
        

		try {
			log.debug(this.getClass().getName()
					+ "- [getInterestChargesReport] - Entering ");

			/* Design file will be compiled */
			jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath
					+ SwtConstants.INTEREST_CHARGES_REPORT_FILE);
			/* Get the datasource and get the connection */
			dataSource = (DataSource) SwtUtil.getBean("dataSource");
			connection = dataSource.getConnection();
			/* Getting the user session */
			session = UserThreadLocalHolder.getUserSession();
			if(savedDateFormat!=null) {
				dateFormat=	savedDateFormat;
			}
			else if(session !=null) {
			/* Initialize the CommonDataManager for getting dateformat */
			commonDataManager = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);

             if (commonDataManager.getDateFormat().equals("datePat1"))
            	 dateFormat="dd/MM/yyyy";
             else
            	 dateFormat="MM/dd/yyyy";            
             
			}else {
				dateFormat= SwtUtil.getCurrentDateFormat(null);
			}
			
			// Convert the fromDate to systemformat date
			startDate = SwtUtil.parseDate(fromDate, dateFormat);
			// Convert the endDate to systemformat date
			endDate = SwtUtil.parseDate(toDate, dateFormat);
			// Get date range between from date and Todate in milliseconds
			timeDiff = endDate.getTime() - startDate.getTime();
			// Get total number of days between from date and ToDate
			dayDiff = (timeDiff / (1000 * 60 * 60 * 24)) + 1;
			// instantiate the hashmap
			jasperParams = new HashMap();
			/* Preparing the parameters to be passed to Jasper Engine. */
			jasperParams.put("pHost_Id", hostId);
			jasperParams.put("pEntity_Id", entityId);
			jasperParams.put("pEntity_Name", entityName);
			jasperParams.put("pCurrency_Code", currencyId);
			jasperParams.put("pCurrency_Name", currencyName);
			jasperParams.put("pStart_Date", SwtUtil.truncateDateTime(startDate));
			jasperParams.put("pEnd_Date", SwtUtil.truncateDateTime(endDate));
			jasperParams.put("pAccount_Id", accountId);
			jasperParams.put("pAccount_Name", accountName);
			jasperParams.put("pDay_Diff", dayDiff);
			jasperParams.put("pdateFormat", dateFormat);
			/* Compiled Opportunity cost Report will be filled here. */
			jasperPrint = JasperFillManager.fillReport(jasperReport,
					jasperParams, connection);
			/* To get the page size */
			pageSize = jasperPrint.getPages();
			/*
			 * If the page size is Zero Empty datasource will be passed to avoid
			 * the blank report.
			 */
			if ((pageSize.size() == 0)) {
				jasperPrint = JasperFillManager.fillReport(jasperReport,
						jasperParams, new JREmptyDataSource(1));

			}
			log.debug(this.getClass().getName()
					+ "- [getInterestChargesReport] - Exit");
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getInterestChargesReport] - SwtException -"
					+ ex.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					ex, "getInterestChargesReport",
					InterestChargesDAOHibernate.class), request, "");
			// Re-throw SwtException
			throw ex;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [getInterestChargesReport] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getInterestChargesReport",
					InterestChargesDAOHibernate.class), request, "");
			throw new SwtException(exp.getMessage());

		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(connection);
			
			// nullify the object
			connection = null;
			dataSource = null;
			session = null;
			commonDataManager = null;
			jasperReport = null;
			pageSize = null;
			jasperParams = null;
		}
		return jasperPrint;
	}
	/*
	 * End:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
}