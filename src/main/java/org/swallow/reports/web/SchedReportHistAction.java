package org.swallow.reports.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.batchScheduler.ILMDataReporting;
import org.swallow.control.model.Job;
import org.swallow.control.model.ScheduledReportType;
import org.swallow.control.model.Scheduler;
import org.swallow.control.model.SystemLog;
import org.swallow.control.service.SchedulerManager;
import org.swallow.control.service.SystemLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.model.SchedReportHist;
import org.swallow.reports.service.SchedReportHistManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;






import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/schedReportHist", "/schedReportHist.do"})
public class SchedReportHistAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("schedreporthistflexdata", "jsp/reports/schedreporthistflexdata");
		viewMap.put("fail", "error");
		viewMap.put("schedreporthistaddflexdata", "jsp/reports/schedreporthistaddflexdata");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("schedreporthistflex", "jsp/reports/schedreporthistflex");
		viewMap.put("schedreporthistaddflex", "jsp/reports/schedreporthistaddflex");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException, IOException {
		method = String.valueOf(method);
		switch (method) {
			case "displaySchedReportHist":
				return displaySchedReportHist();
			case "displaySchedReportHistList":
				return displaySchedReportHistList();
			case "saveSchedReportHist":
				return saveSchedReportHist();
			case "deleteSchedReportHist":
				return deleteSchedReportHist();
			case "sendMailToUsers":
				return sendMailToUsers();
			case "checkExistingDataMethod":
				return checkExistingDataMethod();
			case "unspecified":
				return unspecified();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "saveColumnOrder":
				return saveColumnOrder();
			case "checkFullAccessToTheScreen":
				return checkFullAccessToTheScreen();
			case "checkIfFileExist":
				return checkIfFileExist();
			case "downloadSchedReportHist":
				return downloadSchedReportHist();
		}


		return unspecified();
	}



	public static  String PDF_EXPORT = "pdf";
	public static  String CSV_EXPORT = "csv";
	public static  String XLS_EXPORT = "excel";
	private final Log log = LogFactory.getLog(SchedReportHistAction.class);

	private String menuItemId = "" + SwtConstants.SCREEN_SCHEDULED_REPORT_HISTORY;
	@Autowired
	private SchedReportHistManager schedReportHistManager;

	public SchedReportHistManager getSchedReportHistManager() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		schedReportHistManager = RequestObjectMapper.getObjectFromRequest(SchedReportHistManager.class, request);
		return schedReportHistManager;
	}

	public void setSchedReportHistManager(SchedReportHistManager schedReportHistManager) {
		this.schedReportHistManager = schedReportHistManager;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("schedReportHistManager", schedReportHistManager);
	}

	public String displaySchedReportHist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SchedReportHist schedReportHist = null;
		String screen = null;
		String fileId = null;
		String dateAsString = null;
		SchedulerManager schedulerManager = null;
		ArrayList<Job> jobList = null;
		ArrayList<ScheduledReportType> reportTypes = null;
		String reportHistJobId = null;
		String reportHistSchedId = null;
		String reportName = null;
		Scheduler scheduler = null;
		Collection schedulerDetails = null;
		try {
			log.debug(this.getClass().getName() + " method [displaySchedReportHist] - Enter ");
			request.setAttribute("screenName", request.getParameter("screenName"));
			fileId = request.getParameter("fileId");
			screen = request.getParameter("screenName");
			if (SwtUtil.isEmptyOrNull(request.getParameter("loadFlex"))) {
				log.debug(this.getClass().getName() + "-[displaySchedReportHist] - Exit loading SWF");
				request.setAttribute("fileId", fileId);
				return getView("schedreporthistaddflex");
			}
			if (screen.equals("addScreen")) {
				schedReportHist = new SchedReportHist();
			} else {
				schedReportHist = schedReportHistManager.getSchedReportHist(fileId);
			}
			schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
			jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
			reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), "all");

			schedulerDetails = schedulerManager.getSchedulerDetails(schedReportHist.getScheduleId(), null);

			if (schedulerDetails != null) {
				Iterator itr = schedulerDetails.iterator();

				while (itr.hasNext()) {
					log.debug("Entering into the iterator loop");
					scheduler = (Scheduler) (itr.next());

					break;
				}
			}



			if (schedReportHist.getElapsedTime() != null) {

				if(schedReportHist.getElapsedTime() > 0) {
					schedReportHist.setElapsedTimeAsString(convertSecondsToTime(schedReportHist.getElapsedTime()));
				}
				else if(schedReportHist.getElapsedTime() == 0){
					schedReportHist.setElapsedTimeAsString("00:00:01");
				}

			}

			dateAsString = SwtUtil.formatDate(schedReportHist.getRunDate(), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()+ " hh:mm:ss");
			schedReportHist.setRunDateAsString(dateAsString);


			if(SwtUtil.isEmptyOrNull(schedReportHist.getMailStatus())) {
				schedReportHist.setMailStatusAsString(SwtUtil.getMessage("scheduledReportHist.status.notAvailable", request));
			}else if("F".equals(schedReportHist.getMailStatus())) {
				schedReportHist.setMailStatusAsString(SwtUtil.getMessage("scheduledReportHist.status.failed",request));
			}else {
				schedReportHist.setMailStatusAsString(SwtUtil.getMessage("scheduledReportHist.status.success",request));
			}

			if("F".equals(schedReportHist.getExportStatus())) {
				schedReportHist.setExportStatus(SwtUtil.getMessage("scheduledReportHist.status.failed",request));
			}else {
				schedReportHist.setExportStatus(SwtUtil.getMessage("scheduledReportHist.status.success",request));
			}

			reportHistJobId = schedReportHist.getJobId();
			reportHistSchedId = schedReportHist.getReportTypeId();


			for (int i = 0; i < jobList.size(); i++) {
				if(jobList.get(i).getId().getJobId().equals(reportHistJobId)) {
					reportName = jobList.get(i).getJobDescription();
					schedReportHist.setJobName(jobList.get(i).getJobDescription());
				}
			}

			for (int i = 0; i < reportTypes.size(); i++) {
				if(reportTypes.get(i).getReportTypeId().equals(reportHistSchedId)) {
					schedReportHist.setReportTypeName(reportTypes.get(i).getReportName());
				}
			}



			if("F".equals(schedReportHist.getMailStatus())){
				schedReportHist.setMailError("Error contacting the mail server");
			}

			schedReportHist.setReportName(reportName);
			if (scheduler != null && scheduler.getScheduledReportParams() != null) {
				reportName += ":" + scheduler.getScheduledReportParams().getReportName();
			} else {
				// Log an error message instead of printing to console
				log.warn("Scheduler or Scheduled Report Params are null.");
			}


			request.setAttribute("schedReportHist", schedReportHist);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displaySchedReportHist] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [displaySchedReportHist] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
		return getView("schedreporthistaddflexdata");
	}

	public String convertSecondsToTime(long totalSecs) {
		long hours, minutes, seconds;
		String timeString;
		hours = totalSecs / 3600;
		minutes = (totalSecs % 3600) / 60;
		seconds = totalSecs % 60;

		timeString = String.format("%02d:%02d:%02d", hours, minutes, seconds);

		return timeString;
	}

	public String displaySchedReportHistList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " method [displaySchedReportHistList] - Enter ");
		Collection<SchedReportHist> result = null;
		String hostId = null;
		String reportJobId = null;
		String reportTypeId = null;
		LinkedList<SchedReportHist> schedReportHistList = null;
		SchedReportHist schedReportHist = null;
		String dateAsString = null;

		boolean reportJobChanged = false;
		String fromDateAsString = null;
		String toDateAsString = null;
		boolean isDateRange = false;
		Date fromDate = null;
		Date toDate  = null;
		SchedulerManager schedulerManager = null;
		ArrayList<Job> jobList = null;
		ArrayList<ScheduledReportType> reportTypes = null;
		String reportHistJobId = null;
		String reportHistSchedId = null;
		String reportName = null;
		String userId;
		try {

//			_requestParams["reportJobId"] = reportJobId;
//			_requestParams["reportTypeId"] = reportTypeId;
//			_requestParams["reportJobChanged"] = reportJobChanged;
//			_requestParams["fromDate"] = fromDateAsString;
//			_requestParams["toDate"] = toDateAsString;
//			_requestParams["isDateRange"] = (dateSingleOrRange.selectedValue == "D");
//


			hostId = SwtUtil.getCurrentHostId();
			reportJobId = request.getParameter("reportJobId");
			reportTypeId = request.getParameter("reportTypeId");
			reportJobChanged = "true".equals(request.getParameter("reportJobChanged"));
			isDateRange = "true".equals(request.getParameter("isDateRange"));
			fromDateAsString = request.getParameter("fromDate");
			toDateAsString = request.getParameter("toDate");
			userId = SwtUtil.getCurrentUserId(request.getSession());


			if (SwtUtil.isEmptyOrNull(reportTypeId)) {
				reportTypeId = "All";
			}
			if (SwtUtil.isEmptyOrNull(reportJobId)) {
				reportJobId = "All";
			}

			if (!SwtUtil.isEmptyOrNull(fromDateAsString) && !fromDateAsString.equals("null")) {
				fromDate = SwtUtil.parseDate(fromDateAsString, SwtUtil.getCurrentDateFormat(request.getSession()));
			}else {
				fromDate = SwtUtil.getSystemDatewithoutTime();
			}
			if (!SwtUtil.isEmptyOrNull(toDateAsString) && !toDateAsString.equals("null")) {
				toDate = SwtUtil.parseDate(toDateAsString, SwtUtil.getCurrentDateFormat(request.getSession()));
			}

			schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");

			jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
			if ("All".equalsIgnoreCase(reportJobId)) {
				reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), "all");
			}else {
				reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(),reportJobId );
			}


			schedReportHistList = new LinkedList<SchedReportHist>();
			result = schedReportHistManager.getSchedReportHistList(hostId, reportJobId, reportTypeId,isDateRange , fromDate , toDate, userId);

			for (Iterator<SchedReportHist> iterator = result.iterator(); iterator.hasNext();) {
				schedReportHist = iterator.next();
				reportName = null;
				if (schedReportHist.getElapsedTime()!= null) {
					if(schedReportHist.getElapsedTime() > 0) {
						schedReportHist.setElapsedTimeAsString(convertSecondsToTime(schedReportHist.getElapsedTime()));
					}
					else if(schedReportHist.getElapsedTime() == 0){
						schedReportHist.setElapsedTimeAsString("00:00:01");
					}
				}
				if (schedReportHist.getRunDate() != null) {
					dateAsString = SwtUtil.formatDate(schedReportHist.getRunDate(), SwtUtil
							.getCurrentSystemFormats(request.getSession())
							.getDateFormatValue()+ " HH:mm:ss");
					schedReportHist.setRunDateAsString(dateAsString);
				}

				if(SwtUtil.isEmptyOrNull(schedReportHist.getMailStatus())) {
					schedReportHist.setMailStatus(SwtUtil.getMessage("scheduledReportHist.status.notAvailable", request));
				}else if("F".equals(schedReportHist.getMailStatus())) {
					schedReportHist.setMailStatus(SwtUtil.getMessage("scheduledReportHist.status.failed",request));
				}else {
					schedReportHist.setMailStatus(SwtUtil.getMessage("scheduledReportHist.status.success",request));
				}

				if("F".equals(schedReportHist.getExportStatus())) {
					schedReportHist.setExportStatus(SwtUtil.getMessage("scheduledReportHist.status.failed",request));
				}else {
					schedReportHist.setExportStatus(SwtUtil.getMessage("scheduledReportHist.status.success",request));
				}

				reportHistJobId = schedReportHist.getJobId();
				reportHistSchedId = schedReportHist.getReportTypeId();


				for (int i = 0; i < jobList.size(); i++) {
					if(jobList.get(i).getId().getJobId().equals(reportHistJobId)) {
						reportName = jobList.get(i).getJobDescription();
					}
				}

				schedReportHist.setReportName(reportName+":"+schedReportHist.getScheduleName());

				schedReportHistList.add(schedReportHist);
			}

			request.setAttribute("schedReportHistList", schedReportHistList);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			putReportJobListInRequest(request, true);

			putReportTypesInRequest(request, reportJobId, true);

			bindColumnOrderInRequest(request);
			bindColumnWidthInRequest(request);
			request.setAttribute("recordCount", result.size());
			request.setAttribute("selectedReportJob", reportJobId);
			request.setAttribute("selectedReportType", reportTypeId);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displaySchedReportHistList] - Exit ");
		} catch (SwtException e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - [displaySchedReportHistList] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
		return getView("schedreporthistflexdata");
	}

	public String saveSchedReportHist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SchedReportHist schedReportHist = null;
		String screen = null;
		ActionErrors errors = null;
		String fileId = null;
		String hostId = null;
		String jobId = null;
		String reportTypeId = null;
		String scheduleId = null;
		Date runDate = null;
		Long elapsedTime = null;
		String fileName = null;
		String outputLocation = null;
		String fileSize = null;
		String exportStatus = null;
		String mailStatus = null;
		String exportError = null;
		String mailRsult = null;
		try {
			log.debug(this.getClass().getName() + " method [saveSchedReportHist] - Enter ");
			errors = new ActionErrors();
			fileId = request.getParameter("fileId");
			hostId = request.getParameter("hostId");
			jobId = request.getParameter("jobId");
			reportTypeId = request.getParameter("reportTypeId");
			scheduleId = request.getParameter("scheduleId");
			if (!SwtUtil.isEmptyOrNull(request.getParameter("runDate"))
					&& !request.getParameter("runDate").equals("null")) {
				String runDatetmp = request.getParameter("runDate");
				runDate = SwtUtil.parseDate(runDatetmp, SwtUtil.getCurrentDateFormat(request.getSession()));
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("elapsedTime"))
					&& !request.getParameter("elapsedTime").equals("null")) {
				String elapsedTimetmp = request.getParameter("elapsedTime");
				elapsedTime = Long.parseLong(elapsedTimetmp);
			}
			fileName = request.getParameter("fileName");
			outputLocation = request.getParameter("outputLocation");
			fileSize = request.getParameter("fileSize");
			exportStatus = request.getParameter("exportStatus");
			mailStatus = request.getParameter("mailStatus");
			exportError = request.getParameter("exportError");
			mailRsult = request.getParameter("mailRsult");
			if(SwtUtil.isEmptyOrNull(fileId)) {
				schedReportHist = new SchedReportHist();
				try {
					schedReportHist.getId().setFileId(Long.parseLong(fileId));
				}catch(Exception e) {

				}
				schedReportHist.setHostId(hostId);
				schedReportHist.setJobId(jobId);
				schedReportHist.setReportTypeId(reportTypeId);
				if(scheduleId != null) {
					try {
						schedReportHist.setScheduleId(Integer.parseInt(scheduleId));
					}catch(Exception e) {

					}
				}
				schedReportHist.setRunDate(runDate);
				schedReportHist.setElapsedTime(elapsedTime);
				schedReportHist.setFileName(fileName);
				schedReportHist.setOutputLocation(outputLocation);
				schedReportHist.setFileSize(fileSize);
				schedReportHist.setExportStatus(exportStatus);
				schedReportHist.setMailStatus(mailStatus);
				schedReportHist.setExportError(exportError);
				schedReportHist.setMailRsult(mailRsult);
				screen = request.getParameter("screenName");
				if ("addScreen".equalsIgnoreCase(screen))
					schedReportHistManager.saveSchedReportHist(schedReportHist);
				else
					schedReportHistManager.updateSchedReportHist(schedReportHist);
			}
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [saveSchedReportHist] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [saveSchedReportHist] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			saveErrors(request, SwtUtil.logException(e, request, ""));
			if (e.getErrorCode().equals("errors.DataIntegrityViolationExceptioninAdd")) {
				errors.add("", new ActionMessage(e.getErrorCode()));
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [saveSchedReportHist] method swt : - "
						+ e.getMessage());
				saveErrors(request, SwtUtil.logException(e, request, ""));
			}
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
		return getView("statechange");
	}

	public String deleteSchedReportHist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SchedReportHist schedReportHist = null;
		String fileId = null;
		try {
			log.debug(this.getClass().getName() + " method [deleteSchedReportHist] - Enter ");
			fileId = request.getParameter("fileId");
			schedReportHist = schedReportHistManager.getSchedReportHist(fileId);
			if(SwtUtil.getMaintainAnyReportHistAccess(request)) {
				schedReportHistManager.deleteSchedReportHist(schedReportHist);
				if(schedReportHist.getExportStatus()!="F") {
					try {
						String pathfile=schedReportHist.getOutputLocation()+"/"+schedReportHist.getFileName();
						File file = new File(pathfile);
						if (file.exists()) {
							file.delete();
						}else {
							log.error(this.getClass().getName() + " - [deleteSchedReportHist] - Warning - File not found on server");
						}
					}catch(Exception e) {
						log.error(this.getClass().getName() + " - [deleteSchedReportHist] - Exception -"
								+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
								+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
					}
				}
				request.setAttribute("deleteResult", "SUCCESS");
			}else {
				request.setAttribute("deleteResult", "NO_ACCESS");
			}
			request.setAttribute("schedReportHist", schedReportHist);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [deleteSchedReportHist] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [deleteSchedReportHist] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
		return displaySchedReportHistList();
	}

	/**
	 * This method is used to send Emails via Report Hist Screen
	 * @return
	 * @throws SwtException
	 */
	public String sendMailToUsers() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String result = null;
		String selectedUsers = null;
		String selectedRoles = null;
		String filePath = null;
		boolean resultStatus = false;
		ArrayList<String> mailList = null;
		SystemLogManager systemLogManager = null;
		SchedReportHist hist = null;
		SystemLog systemLog = null;
		String userId = null;
		String fileId = null;
		SchedulerManager schedulerManager = null;
		ArrayList<Job> jobList = null;
		ArrayList<ScheduledReportType> reportTypes = null;
		String jobName  = null;
		String reportTypeName = null;
		String reportTypeId = null;
		Scheduler schedulerRec = null;
		Collection schedulerDetails = null;
		Integer schedulerId = null;
		String pathFile = null;
		String fileName = null;
		String outputLocationProtieValue = null;

		try {
			log.debug(this.getClass().getName() + "- [testBaseQuery] - Enter");
			try {
				selectedRoles = request.getParameter("selectedRoles");
				selectedUsers = request.getParameter("selectedUsers");
				filePath = request.getParameter("fullFilePath");
				fileId = request.getParameter("fileId");
				if (!SwtUtil.isEmptyOrNull(filePath)) {
					filePath = SwtUtil.decode64(filePath);
				}
				outputLocationProtieValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.SCHEDULED_REPORT_LOCATION_PROPERTIE_NAME);

				userId = SwtUtil.getCurrentUserId(request.getSession());
				if (!SwtUtil.isEmptyOrNull(fileId)) {
					hist = schedReportHistManager.getSchedReportHist(fileId);
					if (hist != null && !SwtUtil.isEmptyOrNull(hist.getOutputLocation())
							&& !SwtUtil.isEmptyOrNull(hist.getFileName())) {
						pathFile = outputLocationProtieValue + "/" + hist.getOutputLocation();
						//pathFile = hist.getOutputLocation();
						fileName = hist.getFileName();
						File file = new File(pathFile + "/" + fileName);
						if (file.exists()) {
							reportTypeId = hist.getReportTypeId();
							schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
							jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
							reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), "all");
							schedulerId = hist.getScheduleId();
							schedulerDetails = schedulerManager.getSchedulerDetails(schedulerId, null);

							if (schedulerDetails != null) {
								Iterator itr = schedulerDetails.iterator();

								while (itr.hasNext()) {
									log.debug("Entering into the iterator loop");
									schedulerRec = (Scheduler) (itr.next());

									break;
								}
							}

							for (int i = 0; i < jobList.size(); i++) {
								if (jobList.get(i).getId().getJobId().equals(hist.getJobId())) {
									jobName = jobList.get(i).getJobDescription();
								}
							}

							for (int i = 0; i < reportTypes.size(); i++) {
								if (reportTypes.get(i).getReportTypeId().equals(reportTypeId)) {
									reportTypeName = reportTypes.get(i).getReportName();
								}
							}

							hist.setJobName(jobName);
							hist.setReportTypeName(reportTypeName);
							hist.setReportName(schedulerRec.getScheduledReportParams().getReportName());
							hist.setReportDescription(schedulerRec.getScheduledReportParams().getReportDesc());
							try {
								resultStatus = ILMDataReporting.sendReporAsEmail(selectedUsers, selectedRoles, file,
										mailList, hist, userId);
								if (resultStatus) {
									result = "SUCCESS";
								} else
									result = "MAILNOTSENT";
							} catch (Exception e) {
								result = "EXCEPTION";
							}
						} else {
							result = "NOT_FOUND";
						}
					} else {
						result = "NOT_FOUND";
					}
				} else {
					result = "NOT_FOUND";
				}

			} catch (Exception e) {
				// Empty exception bloc to ensure that everything will work fine even when any
				// exception rised
				// in the security test
				result = "EXCEPTION";
			}

			// set the access to response
			response.getWriter().print(result);
			log.debug(this.getClass().getName() + " - [testBaseQuery] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [testBaseQuery] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "testBaseQuery",
					SchedReportHistAction.class), request, "");
			return getView("fail");
		}
	}

	public String checkExistingDataMethod() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SchedReportHist schedReportHist = null;
		String fileId = null;
		String existingData = "N";
		try {
			log.debug(this.getClass().getName() + "- [checkExistingDataMethod] - Enter");
			schedReportHist = schedReportHistManager.getSchedReportHist(fileId);
			if (schedReportHist != null) {
				existingData = "Y";
			}
			response.getWriter().print(existingData);
			log.debug(this.getClass().getName() + " - [checkExistingDataMethod] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [checkExistingDataMethod] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "checkExistingDataMethod",
					SchedReportHistAction.class), request, "");
			return getView("fail");
		}
	}

	public String unspecified() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
		request.setAttribute("maintainAnyReportHistAccess", SwtUtil.getMaintainAnyReportHistAccess(request));
		return getView("schedreporthistflex");
	}

	private void bindColumnOrderInRequest(HttpServletRequest request) throws SwtException {
		// To get column order from DB (Comma separated value)
		String columnOrder = null;
		// To hold grid column order
		ArrayList<String> alColumnOrder = null;
		// Property value (split by Comma)
		String[] props = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ bindColumnOrderInRequest ] - Entry ");
			// Get column order from DB (User preference)
			columnOrder = SwtUtil.getPropertyValue(request, menuItemId, "display", "column_order");
			// If user preference not found in DB, set default order
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				columnOrder = "fileId,runDate,reportName,elapsedTime,fileName,exportStatus,mailStatus";
			}
			/*
			 * Comma special character is used to split and put in String array variable
			 */
			props = columnOrder.split(",");
			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				/* Adding the Column values to ArrayList */
				alColumnOrder.add(prop);
			}
			/*
			 * Setting the Column orders value in request object to show in screen
			 */
			request.setAttribute("column_order", alColumnOrder);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - [bindColumnOrderInRequest] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex, "bindColumnOrderInRequest",
					SchedReportHistAction.class);
		} finally {
			// nullify objects
			columnOrder = null;
			alColumnOrder = null;
			props = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ bindColumnOrderInRequest ] - Exit ");
		}

	}

	private void bindColumnWidthInRequest(HttpServletRequest request) throws SwtException {

		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;

		try {
			log.debug(this.getClass().getName() + " - [bindColumnWidthInRequest] - Enter");
			// Read the user preferences for column width from property value
			width = SwtUtil.getPropertyValue(request, menuItemId, "display", "column_width");
			// Condition to set default column width
			// Set default width for columns
			if (SwtUtil.isEmptyOrNull(width)) {
				width = "fileId=100,runDate=155,reportName=330,elapsedTime=105,fileName=240,exportStatus=95,mailStatus=85";
			}
			widths = new HashMap<String, String>();
			// Get column width for each column */
			props = width.split(",");
			// Loop to separate column and width value in hash map
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			request.setAttribute("column_width", widths);
			log.debug(this.getClass().getName() + " - [bindColumnWidthInRequest] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [bindColumnWidthInRequest] - " + e.getMessage());
		} finally {
			// nullify the objects
			widths = null;
			props = null;
		}

	}

	public String saveColumnWidth() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String width = null;
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request, SwtUtil.getUserCurrentEntity(request.getSession()), menuItemId,
						"display", "column_width", width);
			} else {
				throw new Exception("You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName() + " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "saveColumnWidth",
					SchedReportHistAction.class), request, "");
		}
		return getView("statechange");
	}

	public String saveColumnOrder() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String columnOrder = null;
		try {
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Entry ");
			columnOrder = request.getParameter("order");
			if (!SwtUtil.isEmptyOrNull(columnOrder)) {
				SwtUtil.setPropertyValue(request, SwtUtil.getUserCurrentEntity(request.getSession()), menuItemId,
						"display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
		} finally {
			columnOrder = null;
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Exit ");
		}
		return getView("statechange");
	}

	/**
	 * Add report job list in request
	 *
	 * @param request
	 * @param putAllLabel
	 * @throws SwtException
	 */
	public void putReportJobListInRequest(HttpServletRequest request, boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName() + " method [putAccountListInRequest] - Enter ");
		SchedulerManager schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
		ArrayList<Job> jobList = schedulerManager.getReportsJobList(SwtUtil.getCurrentHostId());
		Collection<LabelValueBean> reportJobDropDown = new ArrayList<LabelValueBean>();
		if (putAllLabel) {
			reportJobDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
		}
		for (int i = 0; i < jobList.size(); i++) {
			LabelValueBean bean = new LabelValueBean(jobList.get(i).getId().getJobId(),
					jobList.get(i).getJobDescription());
			reportJobDropDown.add(bean);
		}
		request.setAttribute("reportJobs", reportJobDropDown);
		log.debug(this.getClass().getName() + " method [putAccountListInRequest] - Exit ");
	}


	/**
	 * Add report job list in request
	 *
	 * @param request
	 * @param putAllLabel
	 * @throws SwtException
	 */
	public void putReportTypesInRequest(HttpServletRequest request, String jobId, boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName() + " method [putReportTypesInRequest] - Enter ");
		SchedulerManager schedulerManager = (SchedulerManager) SwtUtil.getBean("schedulerManager");
		ArrayList<ScheduledReportType> reportTypes = schedulerManager.getReportTypes(SwtUtil.getCurrentHostId(), jobId);
		Collection<LabelValueBean> reportTypesDropDown = new ArrayList<LabelValueBean>();
		if (putAllLabel) {
			reportTypesDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
		}
		for (int i = 0; i < reportTypes.size(); i++) {
			LabelValueBean bean = new LabelValueBean(reportTypes.get(i).getReportTypeId(),
					reportTypes.get(i).getReportName());
			reportTypesDropDown.add(bean);
		}
		request.setAttribute("reportTypes", reportTypesDropDown);
		log.debug(this.getClass().getName() + " method [putReportTypesInRequest] - Exit ");
	}




	public String downloadSchedReportHist() throws IOException, SwtException {
		SchedReportHist schedReportHist = null;
		String fileId = null;
		String pathFile = null;
		String nombrefichero=null;
		String outputLocationProtieValue = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " method [downloadSchedReportHist] - Enter ");
			fileId = request.getParameter("fileId");
			if(fileId!= null) {
				outputLocationProtieValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.SCHEDULED_REPORT_LOCATION_PROPERTIE_NAME);

				schedReportHist = schedReportHistManager.getSchedReportHist(fileId);
				pathFile = outputLocationProtieValue + "/" + (!SwtUtil.isEmptyOrNull(schedReportHist.getOutputLocation())?schedReportHist.getOutputLocation():"");
				nombrefichero = schedReportHist.getFileName();
			}

		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [downloadSchedReportHist] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
		}

		response.setContentType("application/octet-stream");
		// To set the content as attachment
		response.setHeader("Content-disposition", "attachment; filename="+ nombrefichero);
		response.setHeader("Content-Transfer-Encoding", "Binary");

		try {
			FileInputStream is = new FileInputStream(pathFile+"/"+nombrefichero);
			IOUtils.copy(is, response.getOutputStream());
			response.flushBuffer();
			response.getOutputStream().close();
		}catch(FileNotFoundException e ){
			log.error(e.getMessage(), e.getCause());
		} catch (IOException e) {
			log.error(e.getMessage(), e.getCause());
		}
		log.debug(this.getClass().getName() + " method [downloadSchedReportHist] - Exit ");
		return null;
	}

	/**
	 * Used to check if the user have full access to the screen to delete or resend email
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkFullAccessToTheScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// Variables to holds passed params
		boolean maintainReportHist = false;
		try {
			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Enter ");
			// Get values from request
			maintainReportHist = SwtUtil.getMaintainAnyReportHistAccess(request);

			if(maintainReportHist) {
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Data fetch OK");
			}
			else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", "NO_ACCESS");
			}

			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Exit ");


		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveAccountAttributeValue] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			return getView("fail");
		}

		return getView("statechange");
	}

	/**
	 * Used to check if the file exist on the server or not
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkIfFileExist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// Variables to holds passed params
		String fileId = null;
		SchedReportHist schedReportHist = null;
		String pathFile = null;
		String fileName=null;
		String outputLocationProtieValue = null;
		try {
			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Enter ");
			// Get values from request
			fileId = request.getParameter("fileId");
			if(!SwtUtil.isEmptyOrNull(fileId)) {
				schedReportHist = schedReportHistManager.getSchedReportHist(fileId);
				outputLocationProtieValue = PropertiesFileLoader.getInstance()
						.getPropertiesValue(SwtConstants.SCHEDULED_REPORT_LOCATION_PROPERTIE_NAME);
				pathFile = outputLocationProtieValue + "/" + (!SwtUtil.isEmptyOrNull(schedReportHist.getOutputLocation())?schedReportHist.getOutputLocation():"");

				fileName=schedReportHist.getFileName();

				File file = new File(pathFile+"/"+fileName);
				if (file.exists()) {
					request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "Data fetch OK");
				}
				else {
					request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "FILE_NOT_FOUND");
				}
			}else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", "FILE_NOT_FOUND");
			}

			log.debug(this.getClass().getName() + " method [saveAccountAttributeValue] - Exit ");


		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [saveAccountAttributeValue] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			return getView("fail");
		}

		return getView("statechange");
	}
}