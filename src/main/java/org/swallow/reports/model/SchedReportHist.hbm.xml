<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.reports.model.SchedReportHist" table="S_SCHEDULED_REPORT_HIST">
		<composite-id name="id" class="org.swallow.reports.model.SchedReportHist$Id" unsaved-value="any">
        <key-property name="fileId" access="field" column="FILE_ID" />
        </composite-id>
		<property name="hostId" column="HOST_ID" not-null="false"/>	
		<property name="jobId" column="JOB_ID" not-null="false"/>	
		<property name="reportTypeId" column="REPORT_TYPE_ID" not-null="false"/>	
		<property name="scheduleId" column="SCHEDULE_ID" not-null="false"/>	
		<property name="runDate" column="RUN_DATE" not-null="false"/>	
		<property name="elapsedTime" column="ELAPSED_TIME" not-null="false"/>	
		<property name="fileName" column="FILE_NAME" not-null="false"/>	
		<property name="outputLocation" column="OUTPUT_LOCATION" not-null="false"/>	
		<property name="fileSize" column="FILE_SIZE" not-null="false"/>	
		<property name="exportStatus" column="EXPORT_STATUS" not-null="false"/>	
		<property name="mailStatus" column="MAIL_STATUS" not-null="false"/>	
		<property name="exportError" column="EXPORT_ERROR" not-null="false"/>	
		<property name="mailRsult" column="MAIL_DIST_RESULT" not-null="false"/>	
		
    </class>
</hibernate-mapping>