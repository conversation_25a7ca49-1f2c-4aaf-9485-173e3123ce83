package org.swallow;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.servlet.http.HttpServletRequest;

@Controller
public class IndexController {

    @Value("${server.servlet.context-path}")
    private String contextPath;

    public IndexController() {
    }

    @GetMapping("/")
    public RedirectView root(HttpServletRequest request, RedirectAttributes attributes) {
        // Create an absolute URL to ensure proper redirection
        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort();
        if (contextPath == null || contextPath.equals("/")) {
            return new RedirectView(baseUrl + "/logon.do");
        } else {
            return new RedirectView(baseUrl + contextPath + "/logon.do");
        }
    }

    @RequestMapping(value = {"/index", "/.do"})
    public RedirectView index(RedirectAttributes attributes) {
        // Relative URL for other mappings
        return new RedirectView("logon.do");
    }
}
