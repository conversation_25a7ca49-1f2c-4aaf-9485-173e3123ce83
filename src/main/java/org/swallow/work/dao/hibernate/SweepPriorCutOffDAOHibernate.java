/*
 * Created on Jul 31, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessResourceFailureException;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.SweepPriorCutOffDAO;
import org.swallow.work.model.SweepPriorCutOff;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * This Class is Used to Retrive Details from the Database for Sweepprior Cut off screen
 */
@Repository("sweepPriorCutOffDAO")
@Transactional
public class SweepPriorCutOffDAOHibernate extends CustomHibernateDaoSupport implements SweepPriorCutOffDAO {
    private final Log log = LogFactory.getLog(SweepPriorCutOffDAOHibernate.class);

    public SweepPriorCutOffDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    public Collection getSweepPriorToCutOffListUsingStoredProc(String hostId, String entityId, 
            String subAccount, int leadTime, int extendDisplayTimeBy, SystemFormats format) throws SwtException {
        log.debug("Entering getSweepPriorToCutOffListUsingStoredProc method");
        Collection outputColl = new ArrayList();

        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall("{call PK_MONITORS.SP_SWEEP_CUT_OFF_MONITOR (?,?,?,?,?,?)}")) {

            log.debug(" PK_MONITORS.SP_SWEEP_CUT_OFF_MONITOR Called at " + SwtUtil.getTimeWithMilliseconds());
            
            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, subAccount);
            cstmt.setInt(4, leadTime);
            cstmt.setInt(5, extendDisplayTimeBy);
            cstmt.registerOutParameter(6, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();
            
            log.debug(" PK_MONITORS.SP_SWEEP_CUT_OFF_MONITOR Ended at " + SwtUtil.getTimeWithMilliseconds());
            
            try (ResultSet rsDetails = (ResultSet) cstmt.getObject(6)) {
                while (rsDetails != null && rsDetails.next()) {
                    SweepPriorCutOff sweepPriorCutOff = new SweepPriorCutOff();
                    sweepPriorCutOff.setCurrcode(rsDetails.getString(1));
                    sweepPriorCutOff.getId().setAccountId(rsDetails.getString(2));
                    sweepPriorCutOff.setAcctname(rsDetails.getString(3));
                    sweepPriorCutOff.setCutOff(rsDetails.getString(4));
                    
                    Double targetBalance = rsDetails.getDouble(5);
                    sweepPriorCutOff.setTargetBalance(targetBalance);
                    sweepPriorCutOff.setTargetBalanceNegative(targetBalance < 0.0);
                    sweepPriorCutOff.setTargetBalanceAsString(SwtUtil.formatCurrency(
                        sweepPriorCutOff.getCurrcode(), sweepPriorCutOff.getTargetBalance()));
                    
                    Date valueDate = rsDetails.getDate(6);
                    sweepPriorCutOff.setValuedate(valueDate);
                    sweepPriorCutOff.setValuedateAsString(SwtUtil.formatDate(valueDate, format.getDateFormatValue()));
                    
                    sweepPriorCutOff.setManualSweepFlag(rsDetails.getString(7));
                    
                    Double predictedBal = Double.valueOf(rsDetails.getString(8));
                    sweepPriorCutOff.setPredictBalance(predictedBal);
                    sweepPriorCutOff.setPredictedBalanceNegative(predictedBal < 0.0);
                    sweepPriorCutOff.setPredictBalanceAsString(SwtUtil.formatCurrency(
                        sweepPriorCutOff.getCurrcode(), sweepPriorCutOff.getPredictBalance()));
                    
                    Double externalBalance = Double.valueOf(rsDetails.getString(9));
                    sweepPriorCutOff.setExternalBalance(externalBalance);
                    sweepPriorCutOff.setExternalBalanceNegative(externalBalance < 0.0);
                    sweepPriorCutOff.setExternalBalanceAsString(SwtUtil.formatCurrency(
                        sweepPriorCutOff.getCurrcode(), sweepPriorCutOff.getExternalBalance()));
                    
                    String mainAccountId = rsDetails.getString(10);
                    sweepPriorCutOff.setMainAccountId(mainAccountId);
                    
                    // Get main account name
                    String accountName = session.createQuery(
                        "select acct.acctname from AcctMaintenance acct where acct.id.hostId = :hostId " +
                        "and acct.id.entityId = :entityId and acct.id.accountId = :accountId", String.class)
                        .setParameter("hostId", hostId)
                        .setParameter("entityId", entityId)
                        .setParameter("accountId", mainAccountId)
                        .uniqueResult();
                    sweepPriorCutOff.setMainAcccoutName(accountName);
                    
                    // Get sweep from balance
                    String sweepFrmbal = session.createQuery(
                        "select acct.sweepFrmbal from AcctMaintenance acct where acct.id.hostId = :hostId " +
                        "and acct.id.entityId = :entityId and acct.id.accountId = :accountId", String.class)
                        .setParameter("hostId", hostId)
                        .setParameter("entityId", entityId)
                        .setParameter("accountId", sweepPriorCutOff.getId().getAccountId())
                        .uniqueResult();
                    
                    sweepPriorCutOff.setSweepFrmbal(sweepFrmbal != null && sweepFrmbal.equalsIgnoreCase("P") 
                        ? "Predicted" : "External");
                    
                    outputColl.add(sweepPriorCutOff);
                }
            }
        } catch (Exception e) {
            log.error("Error in getSweepPriorToCutOffListUsingStoredProc", e);
            throw new SwtException(e.getMessage());
        }
        return outputColl;
    }

    public Collection getMainAccountDetails(String hostId, String entityId, String mainAccountId) throws SwtException {
        log.debug("Entering getMainAccountDetails method");
        try (Session session = getSessionFactory().openSession()) {
            String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId " +
                        "and acct.id.entityId = :entityId and acct.id.accountId = :accountId";
            
            List<AcctMaintenance> result = session.createQuery(hql, AcctMaintenance.class)
                .setParameter("hostId", hostId)
                .setParameter("entityId", entityId)
                .setParameter("accountId", mainAccountId)
                .getResultList();
            
            log.debug("Exiting getMainAccountDetails method");
            return result;
        } catch (Exception e) {
            log.error("Error in getMainAccountDetails", e);
            throw new SwtException("Error retrieving main account details: " + e.getMessage());
        }
    }
}
