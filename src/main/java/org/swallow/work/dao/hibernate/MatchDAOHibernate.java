/*
 * @(#)MatchDAOHibernate.java  11/01/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessResourceFailureException;
import org.swallow.control.model.Job;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.MatchDAO;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.MatchQueue;
import org.swallow.work.model.Movement;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

/*
 * <pre> DAO layer for Match display Screen Class used to - Display match
 * details - Update Existing match - Display Offered/ Suspend/confirmed queues
 * screen
 * 
 * </pre>
 */
@Repository ("matchDAO")
@Transactional
public class MatchDAOHibernate extends CustomHibernateDaoSupport implements MatchDAO {
	public MatchDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	/**
	 * Creating logger object
	 */
	private final Log log = LogFactory.getLog(MatchDAOHibernate.class);

	/**
	 * This method is used to fetch screen data for all the Match Screen's
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyGrpId
	 * @param status
	 * @param roleId
	 * @param selectedTabIndex
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * @param date
	 * @param currGrpId
	 * @param dateTabFlag
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Object> getMatchDetailsUsingStoredProc(String hostId,
			String entityId, String currGrpId, String roleId,
			String dateTabFlag,
			String applyCurrencyThreshold, String noIncludedMovementMatches,
			String status, Date date) throws SwtException {

		ArrayList<Object> arr = new ArrayList<Object>();
		Collection<MatchQueue> matchDetails = new ArrayList<MatchQueue>();
		// Variable to hold Tab Flag
		String tabFlag = "";

		log.debug("Entering MatchDAOHibernate.getMatchDetailsUsingStoredProc() ");
		
		try (Session session = sessionFactory.openSession();
			 Connection conn = SwtUtil.connection(session);
			 CallableStatement cstmt = conn.prepareCall("{call PK_APPLICATION.SP_MATCH_COUNTS (?,?,?,?,?,?,?,?,?,?,?)}")) {

			cstmt.setString(1, hostId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, currGrpId);
			cstmt.setString(4, roleId);
			cstmt.setString(5, dateTabFlag);
			cstmt.setString(6, applyCurrencyThreshold);
			cstmt.setString(7, noIncludedMovementMatches);
			cstmt.setString(8, status);
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.setDate(11, SwtUtil.truncateDateTime(date));
			cstmt.execute();

			try (ResultSet rs = (ResultSet) cstmt.getObject(9);
				 ResultSet rsTabFlag = (ResultSet) cstmt.getObject(10)) {
				
				// To read the Tab flag Values and set the Local String value
				if (rsTabFlag != null) {
					while (rsTabFlag.next()) {
						tabFlag = tabFlag + rsTabFlag.getString(1);
					}
				}
				// To add the arraylist for tabflag
				arr.add(tabFlag);

				if (rs != null) {
					while (rs.next()) {
						MatchQueue matchQueue = new MatchQueue();
						long qualA = 0;
						long qualB = 0;
						long qualC = 0;
						long qualD = 0;
						long qualE = 0;
						long qualZ = 0;

						matchQueue.setCurrencyCode(rs.getString(1));
						matchQueue.setCurrencyName(rs.getString(2));
						matchQueue.setStatus(status);

						qualA = rs.getLong(3);
						qualB = rs.getLong(4);
						qualC = rs.getLong(5);
						qualD = rs.getLong(6);
						qualE = rs.getLong(7);
						qualZ = rs.getLong(8);

						matchQueue.setQualityA(String.valueOf(qualA));
						if (matchQueue.getQualityA().equals("0"))
							matchQueue.setQualityA("");
						matchQueue.setQualityB(String.valueOf(qualB));
						if (matchQueue.getQualityB().equals("0"))
							matchQueue.setQualityB("");
						matchQueue.setQualityC(String.valueOf(qualC));
						if (matchQueue.getQualityC().equals("0"))
							matchQueue.setQualityC("");
						matchQueue.setQualityD(String.valueOf(qualD));
						if (matchQueue.getQualityD().equals("0"))
							matchQueue.setQualityD("");
						matchQueue.setQualityE(String.valueOf(qualE));
						if (matchQueue.getQualityE().equals("0"))
							matchQueue.setQualityE("");
						matchQueue.setQualityZ(String.valueOf(qualZ));
						if (matchQueue.getQualityZ().equals("0"))
							matchQueue.setQualityZ("");
						matchQueue.setEntityId(entityId);

						matchQueue.setDay(dateTabFlag.equalsIgnoreCase("4") ? "All"
								: "NotAll");
						matchQueue.setStatus(status);
						matchDetails.add(matchQueue);
					}
				}
				arr.add(matchDetails);
			}
		} catch (DataAccessResourceFailureException dataAccessException) {
			log.error("DataAccessException in MatchDAOHibernate.getMatchDetailsUsingStoredProc");
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "getMatchDetailsUsingStoredProc",
					MatchDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log.error("illegalStateException in MatchDAOHibernate.getMatchDetailsUsingStoredProc");
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "getMatchDetailsUsingStoredProc",
					MatchDAOHibernate.class);
		} catch (HibernateException hibernateException) {
			log.error("hibernateException in MatchDAOHibernate.getMatchDetailsUsingStoredProc");
			throw SwtErrorHandler.getInstance().handleException(
					hibernateException, "getMatchDetailsUsingStoredProc",
					MatchDAOHibernate.class);
		} catch (SQLException sqlException) {
			log.error("sqlException in MatchDAOHibernate.getMatchDetailsUsingStoredProc");
			throw SwtErrorHandler.getInstance().handleException(sqlException,
					"getMatchDetailsUsingStoredProc", MatchDAOHibernate.class);
		}
		
		log.debug("Exiting MatchDAOHibernate.getMatchDetailsUsingStoredProc() ");
		return arr;
	}

	/**
	 * This method repopulates and updates highest_position level, lowest
	 * position level, max value date and max amount of movements associated
	 * with given match id.
	 * 
	 * @param matchIds
	 * @throws SwtException
	 */
	public void updateMatch(Match match) throws SwtException {
		try (Session session = sessionFactory.openSession()) {
			Transaction tx = session.beginTransaction();
			try {
				session.update(match);
				session.flush();
				tx.commit();
			} catch (Exception ex) {
				if (tx != null && tx.isActive()) {
					tx.rollback();
				}
				throw ex;
			}
		} catch (Exception exception) {
			log.error("Exception in MatchDAOHibernate.updateMatch()");
			throw SwtErrorHandler.getInstance().handleException(exception, 
					"updateMatch", MatchDAOHibernate.class);
		}
	}

	/**
	 * This method repopulates and updates highest_position level, lowest
	 * position level, max value date and max amount of movements associated
	 * with given match id.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchIds
	 * @throws SwtException
	 */
	public void updateBrokenMatch(String hostId, String entityId, long matchId)
			throws SwtException {
		log.debug("Entering MatchDAOHibernate.updateBrokenMatch()");
		Match match = null;
		boolean updateMatch = true;
		
		// Get the SwtInterceptor as used in the original code
		SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		
		try (Session session = sessionFactory.withOptions().interceptor(interceptor).openSession()) {
			Transaction tx = session.beginTransaction();
			try {
				MatchDisplayDAO matchDisplayDAO = (MatchDisplayDAO) SwtUtil
						.getBean("matchDisplayDAO");
				
				String movQuery = "select max(m.amount), max(m.valueDate), max(m.positionLevel),"
						+ " min(m.positionLevel) from Movement m where "
						+ "m.id.hostId = :hostId and m.id.entityId = :entityId and m.matchId = :matchId";
				
				try (Session querySession = sessionFactory.openSession()) {
					TypedQuery<Object[]> query = querySession.createQuery(movQuery, Object[].class);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
					query.setParameter("matchId", Long.valueOf(matchId));
					
					List<Object[]> dataList = query.getResultList();
					
					Double hiAmount = new Double(0);
					Integer hiPosLevel = new Integer(0);
					Integer loPosLevel = new Integer(0);
					Date hiValDate = new Date();
					
					if (dataList != null && !dataList.isEmpty()) { // INFO: Always returns more than one row
						Object[] dataRow = dataList.get(0);
						hiAmount = (Double) (dataRow[0]);
						hiValDate = (Date) (dataRow[1]);
						hiPosLevel = (Integer) (dataRow[2]);
						loPosLevel = (Integer) (dataRow[3]);
					} else {
						/*
						 * INFO: It means match exist without any movement so it has to
						 * be deleted
						 */
						// FIRST : Delete all the match notes
						Collection<MatchNote> matchNotes = matchDisplayDAO.getMatchNotes(entityId, hostId, new Long(matchId).toString());
						for (Iterator<MatchNote> iterator = matchNotes.iterator(); iterator.hasNext();) {
							MatchNote matchNote = iterator.next();
							session.delete(matchNote);
						}
						match = new Match();
						match.getId().setHostId(hostId);
						match.getId().setEntityId(entityId);
						match.getId().setMatchId(new Long(matchId));
						session.delete(match);
						tx.commit();
						updateMatch = false;
					}
					
					if (updateMatch) {
						match = getMatchObject(hostId, entityId, new Long(matchId).toString());
						match.setMaxAmount(new Double(9.99));
						session.update(match);
						tx.commit();
					}
				}
			} catch (Exception ex) {
				if (tx != null && tx.isActive()) {
					tx.rollback();
				}
				throw ex;
			}
		} catch (DataAccessResourceFailureException dataAccessException) {
			log.error("DataAccessException in MatchDAOHibernate.updateBrokenMatch()");
			throw SwtErrorHandler.getInstance().handleException(
					dataAccessException, "updateBrokenMatch",
					MatchDAOHibernate.class);
		} catch (IllegalStateException illegalStateException) {
			log.error("illegalStateException in MatchDAOHibernate.updateBrokenMatch()");
			throw SwtErrorHandler.getInstance().handleException(
					illegalStateException, "updateBrokenMatch",
					MatchDAOHibernate.class);
		} catch (Exception exception) {
			log.debug("Problem in executing hibernate query;");
			throw SwtErrorHandler.getInstance().handleException(exception,
					"updateBrokenMatch", MatchDAOHibernate.class);
		}
		log.debug("Exiting MatchDAOHibernate.updateBrokenMatch() ");
	}

	/**
	 * This method returns the Match object from P_MATCH table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public Match getMatchObject(String hostId, String entityId, String matchId)
			throws SwtException {
		Match match = null;
		SwtDataSource dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
		String archDatabaseName = (String) dataSource.useDataSource.get();

		try {
			if (archDatabaseName != null) {
				dataSource.useArchive(archDatabaseName);
			}

			try (Session session = sessionFactory.openSession()) {
				String hql = "FROM Match WHERE id.hostId = :hostId";
				
				if (entityId != null && entityId.trim().length() > 0) {
					hql+=" AND id.entityId = :entityId";
				}
				if (matchId != null && matchId.trim().length() > 0) {
					hql+=" AND id.matchId = :matchId";
				}
				
				TypedQuery<Match> query = session.createQuery(hql, Match.class);
				query.setParameter("hostId", hostId);
				if (entityId != null && entityId.trim().length() > 0) {
					query.setParameter("entityId", entityId);
				}
				if (matchId != null && matchId.trim().length() > 0) {
					query.setParameter("matchId", Long.parseLong(matchId));
				}

				// Get account details based on the given criteria
				try {
					match = query.getSingleResult();
					// process the match
				} catch (NoResultException e) {
					// handle no result found
				}
			}
			
			if (dataSource != null) {
				dataSource.clearArchive();
			}
		} catch (Exception exception) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getMatchObject]. Cause: " + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception,
					"getMatchObject", MatchDAOHibernate.class);
		}
		return match;
	}
	
	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to get the archived match from archive schema
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param archiveId
	 * @return Match
	 * @throws SwtException
	 */
	public Match getArchiveMatchObject(String hostId, String entityId,
			String matchId, String archiveId) throws SwtException {
		// Declared to hold the Match Object
		Match match = null;
		// declared to hold the db_link
		String dbLink = null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getArchiveMatchObject] - Entering ");
			// initialization of Match object
			match = new Match();
			
			try (Session session = sessionFactory.openSession();
				 Connection connection = SwtUtil.connection(session)) {
				
				// Get the db_link information
				try (Statement statement = connection.createStatement();
					 ResultSet resultSet = statement.executeQuery(
						"select DBLINK_SCHEMA_NAME from p_Archive where archive_id='"
						+ archiveId + "'")) {
					
					// getting Db_link
					if (resultSet != null) {
						while (resultSet.next()) {
							dbLink = resultSet.getString(1);
						}
					}
				}
				
				// Call the procedure to get archive match details
				try (CallableStatement callableStatement = connection
						.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMATCH(?,?,?,?,?)}")) {
					
					// set values for procedure
					callableStatement.setString(1, hostId);
					callableStatement.setString(2, entityId);
					callableStatement.setLong(3, Long.parseLong(matchId));
					callableStatement.setString(4, dbLink);
					callableStatement.registerOutParameter(5, oracle.jdbc.OracleTypes.CURSOR);
					
					// execute procedure
					callableStatement.execute();
					
					// get the result set from procedure
					try (ResultSet resultSetMatch = (ResultSet) callableStatement.getObject(5)) {
					
						// get values from result set to set values in Match object
						if (resultSetMatch != null) {
							while (resultSetMatch.next()) {
								match.getId().setHostId(resultSetMatch.getString(1));
								match.getId().setEntityId(resultSetMatch.getString(2));
								match.getId().setMatchId(resultSetMatch.getLong(3));
								match.setCurrencyCode(resultSetMatch.getString(4));
								match.setHighestPosLev(resultSetMatch.getInt(5));
								match.setLowestPosLev(resultSetMatch.getInt(23));
								match.setMaxAmount(resultSetMatch.getDouble(24));
								match.setPredictStatusFlag(resultSetMatch.getString(22));
								match.setMaxValueDate(resultSetMatch.getDate(25));
								match.setMatchQuality(resultSetMatch.getString(6));
								match.setStatus(resultSetMatch.getString(7));
								match.setStatusDate(resultSetMatch.getDate(8));
								match.setConfirmedDate(resultSetMatch.getDate(9));
								match.setIntQuality1(resultSetMatch.getString(26));
								match.setIntQuality2(resultSetMatch.getString(10));
								match.setIntQuality3(resultSetMatch.getString(11));
								match.setIntQuality4(resultSetMatch.getString(12));
								match.setIntQuality5(resultSetMatch.getString(13));
								match.setIntQuality6(resultSetMatch.getString(14));
								match.setIntQuality7(resultSetMatch.getString(15));
								match.setIntQuality8(resultSetMatch.getString(16));
								match.setIntQuality9(resultSetMatch.getString(17));
								match.setOrigConfirmedDate(resultSetMatch.getDate(18));
								match.setStatusUser(resultSetMatch.getString(19));
								match.setUpdateDate(resultSetMatch.getDate(20));
								match.setUpdateUser(resultSetMatch.getString(21));
							}
						}
					}
				}
				// Commit any pending transactions
				if (!connection.getAutoCommit()) {
					connection.commit();
				}
			}
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " - [getArchiveMatchObject]. Cause: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMatchObject", MatchDAOHibernate.class);
		} finally {
			dbLink = null;
			log.debug(this.getClass().getName()
					+ "- [getArchiveMatchObject] - Exit ");
		}
		return match;
	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
}