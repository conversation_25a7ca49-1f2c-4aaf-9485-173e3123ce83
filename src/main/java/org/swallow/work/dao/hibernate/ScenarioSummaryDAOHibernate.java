/*
 * @(#)ScenarioSummaryDAOHibernate.java 1.0 24/12/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;


import java.io.StringReader;
import java.io.StringWriter;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.ScenarioSummaryDAO;
import org.swallow.work.model.AlertInstance;
import org.swallow.work.model.AlertTreeVO;
import org.swallow.work.model.EntityScenarioCount;
import org.swallow.work.model.ScenarioAlertCount;
import org.swallow.work.model.ScenarioInstanceLog;
import org.swallow.work.model.ScenarioInstanceMessage;
import org.swallow.work.model.ScenarioTreeElement;
import org.swallow.work.model.ScenariosSummary;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;




/*
 * This class is used to get counts of input exceptions from data base.
 */
@Repository ("scenarioSummaryDAO")
@Transactional
public class ScenarioSummaryDAOHibernate extends CustomHibernateDaoSupport
		implements ScenarioSummaryDAO{
	public ScenarioSummaryDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory
			.getLog(ScenarioSummaryDAOHibernate.class);
	/**
	 * Return the number of existing record for each Scenario from P_SCENARIO_COUNTS
	 * @param baseQuery
	 * @return integer
	 * @throws SwtException 
	 * 
	 */
	
	public ScenariosSummary getScenariosSummaryInfoDetails(String roleId,String entityId,String threshold,String hideZero,String alertable,String selectedCurrencyGroup,String callOption, String selectedTab) throws SwtException {
		/* Method's local variable declaration */
		// Declare Session
		Session session = null;
		// Declare Connection
		Connection conn = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		// Declare ResultSet
		ResultSet rs = null;
		// Declare categoryInfoHashMap
		HashMap<String, String> categoryInfoHashMap = null ;
		// Declare tree
		ArrayList<ScenarioTreeElement> tree= null;
		// Declare timeToCutOffInHourMinute		
		String timeToCutOffInHourMinute = null;
		// Declare scenarioSummary	
		ScenariosSummary scenarioSummary = null;
		// Declare categoryName	
		String categoryName = null;
		// Declare categoryCount	
		int categoryCount = 0;
		// Declare indexOfDay	
		int indexOfDay = 0;
		// Declare indexOfMinute	
		int indexOfMinute = 0;
		// Declare treeElement	
		ScenarioTreeElement treeElement= null;
		Integer selectedTabAsInt = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getScenariosSummaryInfoDetails] - " + "Entry");
			
			if(!SwtUtil.isEmptyOrNull(selectedTab))
				selectedTabAsInt =Integer.parseInt(selectedTab);
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */	
			cstmt = conn
					.prepareCall("{call PKG_ALERT.PROC_POPULATE_SCENARIO_CATEG(?,?,?,?,?,?,?,?,?) }");
			cstmt.setString(1, roleId);
			cstmt.setString(2, entityId);
			cstmt.setString(3, threshold);
//			cstmt.setString(4, hideZero);
			cstmt.setString(4, alertable);
			cstmt.setString(5, selectedCurrencyGroup);
			cstmt.setString(6, callOption);
			if(selectedTabAsInt == null)
				cstmt.setNull(7, java.sql.Types.NUMERIC);	 
			else {
				cstmt.setInt(7, selectedTabAsInt);
			}
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.NUMBER);					
			
			scenarioSummary = new ScenariosSummary();
			tree = new ArrayList<ScenarioTreeElement>();
			categoryInfoHashMap = new HashMap<String,String>();
			cstmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) cstmt.getObject(8);
			if (rs != null) {
				
				while (rs.next()) {					
					treeElement = new ScenarioTreeElement();
					//Set catogoryName
					treeElement.setCategoryName(rs.getString(1));
					//Set categoryOrder
					treeElement.setCategoryOrder(rs.getInt(2));
					//Set categoryDescription
					treeElement.setCategoryDescription(rs.getString(3));
					//Set scenario Id
					treeElement.setScenarioId(rs.getString(4));
					//Set scenarioOrder
					treeElement.setScenarioOrder(rs.getInt(5));
					//Set scenarioName
					treeElement.setScenarioName(rs.getString(6));
					//Set scenario counts
					treeElement.setCount(rs.getInt(7));
					//Set Alertable flag
					treeElement.setIsAlertable(rs.getString(9));
					//Set scenario Description
					treeElement.setScenarioDescription(rs.getString(10));					
					//Set scenario timeToCutOffInHourMinute
					if(rs.getString(8)!=null)
					{
						/* Extract number of hours and minutes from result */
						timeToCutOffInHourMinute = rs.getString(8);
						//Extract index of "d" 
						indexOfDay = timeToCutOffInHourMinute.indexOf("d");
						//Extract index of "m"
						indexOfMinute = timeToCutOffInHourMinute.indexOf("m");
						//if cut-off time is not equal "00d 00h 00m 00s" and chars exist on the String then try extract number of hours and minutes
						if((indexOfDay==-1 )|| (indexOfMinute==-1)||(timeToCutOffInHourMinute.trim().equals(SwtConstants.ZERO_TIME)))
							timeToCutOffInHourMinute = "";
						else{		
									timeToCutOffInHourMinute=timeToCutOffInHourMinute.substring((indexOfDay+2),(indexOfMinute+1));
									timeToCutOffInHourMinute+=SwtConstants.SCENARIO_CUT_OFF;
									timeToCutOffInHourMinute =" ("+timeToCutOffInHourMinute+")";
							}
					}//Else set timeToCutOffInHourMinute to empty string
					else 
						timeToCutOffInHourMinute = "";
					
					treeElement.setCutOffTime(timeToCutOffInHourMinute);					
					
					//If category name change than put category info in hashmap	
					if(categoryName!=null)
					{	if(!categoryName.equals(rs.getString(1)))
							{	
								categoryInfoHashMap.put(categoryName, " ("+ categoryCount+")");
								categoryCount = 0;
							}
					
					}
					categoryName = rs.getString(1);
					//Add scenario counts to category counts
					categoryCount += rs.getInt(7);
					//add element to tree
					tree.add(treeElement);
				}
			}
			//For the last element add new category to hashmap
			if(categoryName!=null)
				categoryInfoHashMap.put(categoryName, " ("+categoryCount+")");
			
			// Set scenarioSummary category info HashMap
			scenarioSummary.setCategoryInfo(categoryInfoHashMap);
			// Set scenarioSummary total counts
			scenarioSummary.setTotalCount(cstmt.getInt(9));
			// Set scenarioSummary tree
			scenarioSummary.setTree(tree);
			
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception occured in getScenariosSummaryInfoDetails." + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getScenariosSummaryInfoDetails", ScenarioSummaryDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, null, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getScenariosSummaryInfoDetails",
						ScenarioSummaryDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getScenariosSummaryInfoDetails",
						ScenarioSummaryDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		return scenarioSummary;
	}
	
	/**
	 * Return index of the entityName in the arrayList if it exist else return -1
	 * @param list
	 * @param entityName
	 * @return
	 */
	
	private int getEntityIndexFromArrayList (ArrayList<EntityScenarioCount> list,String entityName) {
		//Declare EntityScenarioCount
		EntityScenarioCount entity = null;
		
		
		Iterator i = list.iterator();
		int j = 0;
		while(i.hasNext())
		{	
			entity = (EntityScenarioCount) i.next();
			if(entity.getEntity().equals(entityName) && entity.getCcy().equals("All"))
			{		
				return j;
			}
			
		 j++;	
		}		
		return -1;
	}

	 
/**
 * Return the number of existing counts grouped by Entity and Currency
 * @param scenarioId
 * @param roleId
 * @param thresholdFlag
 * @param groupingSummary
 * @param entityId
 * @param selectedSort
 * @param selectedCurrencyGroup
 * @return ArrayList
 * @throws SwtException
 */
	public ArrayList<EntityScenarioCount> getScenarioCountByEntituCurrency(String scenarioId,String roleId,String thresholdFlag,String groupingSummary, String entityId,String selectedSort,String selectedCurrencyGroup,String isAlertable,  String selectedFilter) throws SwtException {
		// Session object
				Session session = null;
				// Connection object
				Connection conn = null;
				// CallableStatement object
				CallableStatement cstmt = null;
				// ResultSet object
				ResultSet rs = null;
				// EntityScenarioCount object
				EntityScenarioCount queryResult = null;
				//Statement object
				Statement stmt = null;
				// Declare recordsList
				ArrayList<EntityScenarioCount> recordsList = null;
				// Declare entityPosition in arrayList
				int entityPosition =0; 
				// Declare currencyPosition in arrayList
				int currencyPosition = 0;
				// Declare entityName
				String entityName = null;
				// Declare lastEntityName
				String lastEntityName = null;
				// Declare sortedColumn
				String sortedColumn = null;
				// Declare sortDirection
				String sortDirection = null;
				// Declare sortArray
				String [] sortArray = null;
				
				try {
					log.debug(this.getClass().getName()
							+ " - [getScenarioCountByEntituCurrency] - " + "Entry");
				//If selectedSort is different than set selectedSort and sortDirection
					if(selectedSort !=null)
						{	
							sortArray = selectedSort.split("\\|");
							if(sortArray.length==2)
							sortedColumn = sortArray[0];
							sortDirection = sortArray[1];
						}
					/*
					 * Returns the HibernateTemplate and session factory for this DAO,
					 * and opening a Hibernate Session
					 */
					session = getHibernateTemplate().getSessionFactory().openSession();
					conn = SwtUtil.connection(session);
					/* Using Callable statement to execute the Stored Procedure */					
					cstmt = conn
							.prepareCall("{call PKG_ALERT.PROC_SCENARIO_COUNT_BY_ENT_CUR(?,?,?,?,?,?,?,?,?,?,?,?)}");
					
					cstmt.setString(1,scenarioId);
					cstmt.setString(2,roleId);
					cstmt.setString(3,thresholdFlag);
					cstmt.setString(4,groupingSummary);
					cstmt.setString(5,entityId);
					cstmt.setString(6, sortedColumn);
					cstmt.setString(7, sortDirection);
					cstmt.setString(8, selectedCurrencyGroup);
					cstmt.setString(9, isAlertable);
					cstmt.setString(10, selectedFilter);
					cstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);
					cstmt.registerOutParameter(12, oracle.jdbc.OracleTypes.CURSOR);
					cstmt.execute();
					/* Fetching the Result */
					rs = (ResultSet) cstmt.getObject(11);
					
			 recordsList=new ArrayList<EntityScenarioCount>();
			if(rs!=null){
				 while(rs.next()){
					 //Create a new EntityScenarioCount
					queryResult = new EntityScenarioCount();	
					// Set Entity name
					queryResult.setEntity(rs.getObject(1).toString());
					// Set currency
					queryResult.setCcy(rs.getObject(2).toString());
					// Set count
					queryResult.setCount(rs.getInt(3));
					// Set isGroup flag to false if the grouping Summary is currency else set it to true
					queryResult.setGroup(true); 
					//Set openFlag to false
					queryResult.setOpen(groupingSummary.equals("C")?true:false);
					//Set visibleFlag to true
					queryResult.setVisible(true);
					//Add element to the list
					recordsList.add(queryResult);
				}	
					/* Fetching the second Cursor  */
				rs = (ResultSet) cstmt.getObject(12);
				currencyPosition = 1;
				// If grouping summary is entity than add currency subnodes from the second cursor
				if ((groupingSummary.equals("E")||groupingSummary.equals("C")) && rs != null) {
					while (rs.next()) {
						 //Create a new EntityScenarioCount
						queryResult = new EntityScenarioCount();
						// Set Entity name
						entityName =rs.getObject(1).toString();
						if(!entityName.equals(lastEntityName))
						{	
							//if entityName change than reset currencyPosition counter
							currencyPosition=1;
							//Extract entityName position from the list of records
							entityPosition = getEntityIndexFromArrayList(recordsList, entityName);
							//set hasChildrenFlag for the selected entity
							recordsList.get(entityPosition).setHasChildren(true);
							//Save last Entity Name
							lastEntityName = entityName;
						}
						// Set Entity name
						queryResult.setEntity(entityName);
						// Set currency
						queryResult.setCcy(rs.getObject(2).toString());
						// Set count
						queryResult.setCount(rs.getInt(3));
						// Set isGroup flag to false
						queryResult.setGroup(false);
						//Set openFlag to false
						queryResult.setOpen(false);
						//Set visibleFlag to false
						queryResult.setVisible(groupingSummary.equals("C")?true:false);
						//if the entityPosition+currencyPosition exceed the size of the recordsList then add element at the least
						if(entityPosition+currencyPosition>recordsList.size())
							recordsList.add(queryResult);					
						else
							recordsList.add((entityPosition+currencyPosition),queryResult);
						//increment currencyPosition counter
						currencyPosition++;
					}
				}
			}
			
			log.debug(this.getClass().getName() + " - [getScenarioCountByEntituCurrency] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioCountByEntituCurrency] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getScenarioCountByEntituCurrency",ScenarioSummaryDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getScenarioCountByEntituCurrency",ScenarioSummaryDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		return recordsList;
	}
	
	/**
	 * Return last run date and last run duration of the selected scenario 
	 * @param scenarioId 
	 * @return String
	 */
	public String getSelectedScenarioLastRun(String scenarioId)  throws SwtException
	{
	/* Method's local variable declaration */
	// Declare Session
	Session session = null;
	// Declare Connection
	Connection conn = null;
	// Declare Callable Statement
	CallableStatement cstmt = null;
	// Declare ResultSet
	ResultSet rs = null;
	// Declare Result 
	String result= null;
	try {
		log.debug(this.getClass().getName()
				+ " - [getSelectedScenarioLastRun] - " + "Entry");
		// open the session
		session = getHibernateTemplate().getSessionFactory().openSession();
		// get the connection
		conn = SwtUtil.connection(session);
		/* Using Callable statement to execute stored function */
		cstmt = conn
				.prepareCall("select PKG_ALERT.FN_GET_LAST_RUN_DURATION(?) from dual");
		cstmt.setString(1, scenarioId);
		rs= cstmt.executeQuery();
		rs.next();
		// Get last run date and last run duration
		result = (String) rs.getString(1);	
		result = result!=null?result:"";

	} catch (Exception exp) {
		log.error(this.getClass().getName()
				+ " - Exception Catched in [getSelectedScenarioLastRun] method : - "
				+ exp.getMessage());
	} finally {
		SwtException thrownException = null;
		Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
		
		if (exceptions[0] != null)
			thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getSelectedScenarioLastRun",ScenarioSummaryDAOHibernate.class);
		
		if (thrownException == null && exceptions[1] !=null) 
			thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getSelectedScenarioLastRun",ScenarioSummaryDAOHibernate.class);
		
		if (thrownException != null)
			throw thrownException;
		
	}
	return result.toString();
	
}

	/**
	 * return the access to the facility screen 
	 * - 0: full access
	 * - 1: view access
	 * - 2: No access
	 * @param hostId
	 * @param facilityId
	 * @param roleId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String facilityId,
			String roleId) throws SwtException {
		/* Method's local variable declaration */
		// Declare Session
		Session session = null;
		// Declare Connection
		Connection conn = null;
		// Declare Callable Statement
		CallableStatement cstmt = null;
		// Declare ResultSet
		ResultSet rs = null;
		// Declare Result 
		String result= null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getFacilityAccess] - " + "Entry");
			// open the session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get the connection
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute stored function */
			cstmt = conn
					.prepareCall("select PKG_ALERT. FN_GET_FACILITY_MENU_ACCESS(?,?,?) from dual");
			cstmt.setString(1, hostId);
			cstmt.setString(2, facilityId);
			cstmt.setString(3, roleId);
			rs= cstmt.executeQuery();
			rs.next();
			// Get access to the facility screen
			result = (String) rs.getString(1);	
			result = result!=null?result:"";

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getFacilityAccess",ScenarioSummaryDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getFacilityAccess",ScenarioSummaryDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		return result.toString();
	}

	@Override
	public AlertTreeVO getAlertsScenarioCount(AlertInstance instance) throws SwtException {
		// Session object
		Session session = null;
		// Connection object
		Connection conn = null;
		// CallableStatement object
		PreparedStatement stmt = null;
		// ResultSet object
		ResultSet rs = null;
		// EntityScenarioCount object
		EntityScenarioCount queryResult = null;
		// Declare recordsList
		ArrayList<ScenarioAlertCount> recordsList = null;
		AlertTreeVO result = new AlertTreeVO();
		ScenarioAlertCount record = null;
		int totalInstances = 0;
		
		HashMap<String, String> scenarioTreeGroupingLevels = new HashMap<String, String>();
		
		try {
			log.debug(this.getClass().getName() + " - [getAlertsScenarioCount] - " + "Entry");
			/*
			 * Returns the HibernateTemplate and session factory for this DAO, and opening a
			 * Hibernate Session
			 */
			if(SwtUtil.isEmptyOrNull(instance.getCallerMethod())) {
				instance.setCallerMethod("___");
			}
			
			if(SwtUtil.isEmptyOrNull(instance.getStatus())&& ("SCENARIO_INSTANCE_MONITOR".equalsIgnoreCase(instance.getFaciltityId())
					|| "WORKFLOW_MONITOR".equalsIgnoreCase(instance.getFaciltityId()) )) {
				instance.setStatus("");
			}else if(!SwtUtil.isEmptyOrNull(instance.getStatus()) && "allOpen".equalsIgnoreCase(instance.getStatus())) {
				instance.setStatus("");
			}
			
			long syse= System.currentTimeMillis();
			
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
			stmt = conn.prepareStatement("select pkg_alert.FN_GET_HIGHLIGHTING_DETAILS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) from dual");
			
			
			stmt.setString(1, instance.getFaciltityId());
			stmt.setString(2, instance.getHostId());
			stmt.setString(3, instance.getEntityId());
			stmt.setString(4, instance.getCurrencyCode() != null ? instance.getCurrencyCode().replaceAll("\\s*\\([^)]*\\)", "") : null);
			stmt.setDate(5, SwtUtil.truncateDateTime(instance.getValueDate()));
			stmt.setString(6, instance.getSelectedUserId());
			
			stmt.setString(7, instance.getCallerMethod());
			stmt.setString(8, instance.getSelectedTab());
			
			
			stmt.setString(9, instance.getCurrencyThresholdFlag());
			
//			stmt.setString(10, instance.getIsActive());
			stmt.setString(10, instance.getIsAlertable());
			stmt.setString(11, instance.getStatus());
			stmt.setDate(12, SwtUtil.truncateDateTime(instance.getResolvedDatetime()));
			stmt.setString(13, instance.getAccountId());
			if(instance.getMovementId() != null)
				stmt.setString(14, ""+instance.getMovementId());
			else 
				stmt.setNull(14, Types.NUMERIC);
			
			if(instance.getMatchId() != null)
				stmt.setString(15, ""+instance.getMatchId());
			else 
				stmt.setNull(15, Types.NUMERIC);
			
			stmt.setString(16, instance.getIlmAccountGroup());
			
			if(instance.getSweepId() != null)
				stmt.setString(17, ""+instance.getSweepId());
			else 
				stmt.setNull(17, Types.NUMERIC);
			
			if(instance.getPaymentId() != null)
				stmt.setString(18, ""+instance.getPaymentId());
			else 
				stmt.setNull(18, Types.NUMERIC);
			
			
//			System.err.println(instance.getFaciltityId());                       
//			System.err.println(instance.getHostId());                            
//			System.err.println(instance.getEntityId());                          
//			System.err.println(instance.getCurrencyCode());                      
//			System.err.println(SwtUtil.truncateDateTime(instance.getValueDate())); 
//			
//			
//			
//			System.err.println("suerId"+instance.getSelectedUserId());                    
//			System.err.println("ThresholdFlag="+instance.getCurrencyThresholdFlag());             
//			                                                  
//			System.err.println("active"+instance.getIsActive());                          
//			System.err.println("alertable="+("true".equals(instance.getIsAlertable())?"Y":"N"));
//			
//			System.err.println("accountId = "+instance.getAccountId());  
//			System.err.println("movId = "+instance.getMovementId());  
//			System.err.println("Matchid="+instance.getMatchId());  
//			System.err.println("accoutnGroup="+instance.getIlmAccountGroup());  
//			System.err.println("sweepId="+instance.getSweepId());  
//			System.err.println("paymentId="+instance.getPaymentId());  
//			System.err.println("seelcted tab = "+instance.getSelectedTab());
//			System.err.println("instance.getStatus()=="+instance.getStatus());
			
			
//			P_FACILITY_ID     P_SCENARIO_GUI_ALERT_FACILITY.ID%TYPE,
//            P_HOST_ID         P_SCENARIO_INSTANCE.HOST_ID%TYPE,
//            P_ENTITY_ID       P_SCENARIO_INSTANCE.ENTITY_ID%TYPE,
//            P_CURRENCY_CODE   P_SCENARIO_INSTANCE.CURRENCY_CODE%TYPE,
//            P_VALUE_DATE      P_SCENARIO_INSTANCE.VALUE_DATE%TYPE,
//            P_USER_ID         S_USERS.USER_ID%TYPE,
//            P_CCY_THRESHOLD   VARCHAR2 DEFAULT 'N',
//            P_ACTIVE_INSTANCE VARCHAR2 DEFAULT 'N',
//            P_SHOW_ALERT_SCEN VARCHAR2 DEFAULT 'N',
//            PV_INS_STATUS     VARCHAR2 DEFAULT 'All',
//            P_ACCOUNT_ID      P_SCENARIO_INSTANCE.ACCOUNT_ID%TYPE DEFAULT NULL,
//            P_MOVEMENT_ID     P_SCENARIO_INSTANCE.MOVEMENT_ID%TYPE DEFAULT NULL,
//            P_MATCH_ID        P_SCENARIO_INSTANCE.MATCH_ID%TYPE DEFAULT NULL,
//            P_ILM_GROUP_ID    P_ILM_ACC_GROUP.ILM_GROUP_ID%TYPE DEFAULT NULL,
//            P_SWEEP_ID        P_SCENARIO_INSTANCE.SWEEP_ID%TYPE DEFAULT NULL,
//            P_PAYMENT_ID      P_SCENARIO_INSTANCE.PAYMENT_ID%TYPE DEFAULT NULL)


			// Execute the callable statement
			rs = stmt.executeQuery();
			
//			System.err.println("time1="+(System.currentTimeMillis()-syse));
			// Initialize collection to hold entity details
			recordsList = new ArrayList<ScenarioAlertCount>();
			// Iterate through currency result set and entity id and
			// entity name.
			while (rs.next()) {
				if(!SwtUtil.isEmptyOrNull(rs.getString(1))) {
					Document doc = convertStringToDocument(rs.getString(1));
					NodeList nodes = doc.getElementsByTagName("row");
//				    for (int i = 0; i < nodes.getLength(); i++) {
//				      Element element = (Element) nodes.item(i);
//				      
//				      System.err.println(element
//								.getElementsByTagName("CATEGORY_ID")
//								.item(0).getTextContent().trim());
////				      element
////						.getElementsByTagName("CATEGORY_ID")
////						.item(0).getTextContent().trim();
//								
////				      System.err.println(element.getElementsByTagName("CATEGORY_ID").item(0).getFirstChild().getTextContent());
////				      System.err.println(element.getElementsByTagName("CRITICAL_GUI_HIGHLIGHT").item(0).getFirstChild().getTextContent());
////				      System.err.println(element.getElementsByTagName("RECORD_SCENARIO_INSTANCES").item(0).getFirstChild().getTextContent());
////				      System.err.println(element.getElementsByTagName("SC_COUNT").item(0).getFirstChild().getTextContent());
////				      System.err.println(element.getElementsByTagName("scenario_id").item(0).getFirstChild().getTextContent());
//				      
//				    }
				    	
				    	
				    	 for (int temp = 0; temp < nodes.getLength(); temp++) {
				    		 HashMap<String, String> allFields = new HashMap<String, String>();
				    		 record = new ScenarioAlertCount();
				    		 
				             Node nNode = nodes.item(temp);
				             NodeList nodesChild =  nNode.getChildNodes();
//				             for (int i = 0; temp < nodesChild.getLength(); i++) {
//				            	 
//				            	 System.err.println("Node: " + ((Element) nodesChild.item(i)).getNodeName());
//				            	 System.err.println(((Element) nodesChild.item(i)).getFirstChild().getNodeValue());
//				             }
				             String scenarioId = null;
				             for (int i = 0; i < nodesChild.getLength(); i++) {
				                 Node node = nodesChild.item(i);
			                     // do something with the current element
			                     org.w3c.dom.Element eElement = (org.w3c.dom.Element) node;
			                     
		                		 if("group_columns".equalsIgnoreCase(node.getNodeName())) {
		                			 String groups = "";
		                			
		                			 if(eElement.getFirstChild() != null && !SwtUtil.isEmptyOrNull(eElement.getFirstChild().getNodeValue())) {
		                				 groups = eElement.getFirstChild().getNodeValue().replace("\"", "");
		                			 }
		                			 scenarioTreeGroupingLevels.put(scenarioId, groups);
		                		 }
		                		 
			                	 if(eElement.getFirstChild() != null) {
		                			 if("scenario_id".equalsIgnoreCase(node.getNodeName())) {
		                				 scenarioId = eElement.getFirstChild().getNodeValue();
		                			 }
		                			 
		                			 if("value_date".equalsIgnoreCase(node.getNodeName())) {
		                				 allFields.put(eElement.getNodeName(), StringEscapeUtils.escapeXml(eElement.getFirstChild().getNodeValue()));
		                			 }else {
		                				 allFields.put(eElement.getNodeName(), StringEscapeUtils.escapeXml(eElement.getFirstChild().getNodeValue()));
		                			 }
			                	 }else {
			                		 allFields.put(eElement.getNodeName(), "");
			                	 }
				                	 
				                 
				                 
				                 
				                 if("scenario_id".equalsIgnoreCase(node.getNodeName())) {
				                	 if(eElement.getFirstChild() != null) {
				                		 record.setScenarioId(eElement.getFirstChild().getNodeValue());
				                	 }
				                 }
				                 if("category_id".equalsIgnoreCase(node.getNodeName())) {
				                	 if(eElement.getFirstChild() != null) {
				                		 record.setScenarioCategory(eElement.getFirstChild().getNodeValue());
				                	 }
				                 }
				                 if("count".equalsIgnoreCase(node.getNodeName())) {
				                	 if(eElement.getFirstChild() != null) {
				                		 record.setCount(Integer.parseInt(eElement.getFirstChild().getNodeValue()));
				                		 totalInstances+=Integer.parseInt(eElement.getFirstChild().getNodeValue());
				                	 }
				                 }
				                 if("record_scenario_instances".equalsIgnoreCase(node.getNodeName())) {
				                	 if(eElement.getFirstChild() != null) {
				                		 record.setRecordScenarioInstance("Y".equalsIgnoreCase(eElement.getFirstChild().getNodeValue()));
				                	 }
				                 }
				                 if("critical_gui_highlight".equalsIgnoreCase(node.getNodeName())) {
				                	 if(eElement.getFirstChild() != null) {
				                		 record.setCriticalGuiHighlight("Y".equalsIgnoreCase(eElement.getFirstChild().getNodeValue()));
				                	 }
				                 }
				                 
				                 if("instance".equalsIgnoreCase(node.getNodeName())) {
				                	 NodeList nodesChildInstance =  node.getChildNodes();
//						             for (int i = 0; temp < nodesChild.getLength(); i++) {
//						            	 
//						            	 System.err.println("Node: " + ((Element) nodesChild.item(i)).getNodeName());
//						            	 System.err.println(((Element) nodesChild.item(i)).getFirstChild().getNodeValue());
//						             }
						             for (int k = 0; k < nodesChildInstance.getLength(); k++) {
						                 Node nodeInstanceChild = nodesChildInstance.item(k);
						                 org.w3c.dom.Element eElementInstance = (org.w3c.dom.Element) nodeInstanceChild;
						                 if(eElementInstance.getFirstChild() != null) {
//						                	 System.err.println(eElementInstance.getNodeName() +"  "+eElementInstance.getFirstChild().getNodeValue());
						                	 allFields.put(eElementInstance.getNodeName(), StringEscapeUtils.escapeXml(eElementInstance.getFirstChild().getNodeValue()));
						                 }
						                 }
				                 }
				                 
				                 
				             }
				             
//				             if (nNode.getNodeType() == Node.ELEMENT_NODE) {
//				                 org.w3c.dom.Element eElement = (org.w3c.dom.Element) nNode;
//				                 System.err.println(eElement.getFirstChild().getNodeValue());
//				             }
				             record.setAllFields(allFields);
				             recordsList.add(record);
				         }
				    	
				}
//				record = new ScenarioAlertCount();

//				record.setScenarioId(rs.getString(1));
//				record.setScenarioCategory(rs.getString("CATEGORY_ID"));
//				record.setCriticalGuiHighlight("Y".equals(rs.getString("CRITICAL_GUI_HIGHLIGHT")));
//				record.setRecordScenarioInstance("Y".equals(rs.getString("RECORD_SCENARIO_INSTANCES")));
//				record.setCount(rs.getInt("SC_COUNT"));

				

			}
			result.setScenarioAlertList(recordsList);
			result.setTotalCount(totalInstances);
			result.setScenarioTreeGroupingLevels(scenarioTreeGroupingLevels);
//			System.err.println("time2="+(System.currentTimeMillis()-syse));
			log.debug(this.getClass().getName() + " - [getAlertsScenarioCount] - " + "Exit");
			return result;
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getAlertsScenarioCount] method : - "
					+ exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getAlertsScenarioCount", ScenarioSummaryDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getAlertsScenarioCount", ScenarioSummaryDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

		}
		return result;
	}
	
	private static Document convertStringToDocument(String xmlStr) {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();  
        DocumentBuilder builder;  
        try  
        {  
            builder = factory.newDocumentBuilder();  
            Document doc = builder.parse( new InputSource( new StringReader( xmlStr ) ) ); 
            return doc;
        } catch (Exception e) {  
            e.printStackTrace();  
        } 
        return null;
    }

	@Override
	public List<AlertInstance> getInstanceList(AlertInstance instance) throws SwtException {
		// Session object
		Session session = null;
		// Connection object
		Connection conn = null;
		// CallableStatement object
		CallableStatement stmt=null;
		// ResultSet object
		ResultSet rs = null;
		// EntityScenarioCount object
		AlertInstance queryResult = null;
		
		// CallableStatement object
		Statement statement = null;
		
		// Declare recordsList
		ArrayList<AlertInstance> recordsList = null;
		// Declare entityPosition in arrayList
		int entityPosition = 0;
		// Declare currencyPosition in arrayList
		int currencyPosition = 0;
		// Declare entityName
		String entityName = null;
		// Declare lastEntityName
		String lastEntityName = null;
		// Declare sortedColumn
		String sortedColumn = null;
		// Declare sortDirection
		String sortDirection = null;
		// Declare sortArray
		String[] sortArray = null;
		String scenario=null;
		String selectedStatus = null;

		try {
			log.debug(this.getClass().getName() + " - [getInstanceList] - " + "Entry");
			/*
			 * Returns the HibernateTemplate and session factory for this DAO, and opening a
			 * Hibernate Session
			 */
			
			if(SwtUtil.isEmptyOrNull(instance.getStatus())&& ("SCENARIO_INSTANCE_MONITOR".equalsIgnoreCase(instance.getFaciltityId())
					|| "WORKFLOW_MONITOR".equalsIgnoreCase(instance.getFaciltityId()) )) {
				instance.setStatus("");
			}else if(!SwtUtil.isEmptyOrNull(instance.getStatus()) && "allOpen".equalsIgnoreCase(instance.getStatus())) {
				instance.setStatus("");
			}
			
			
			/* Using Callable statement to execute the Stored Procedure */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */
//			stmt = conn.prepareStatement("select pkg_alert.FN_GET_SCENARIO_INSTANCES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) from dual");
			stmt=conn.prepareCall("{ ? = call pkg_alert.FN_GET_SCENARIO_INSTANCES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			stmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			
			stmt.setString(2, instance.getFaciltityId());
			stmt.setString(3, instance.getHostId());
			stmt.setString(4, instance.getEntityId());
			stmt.setString(5, instance.getCurrencyCode());
			stmt.setDate(6, SwtUtil.truncateDateTime(instance.getValueDate()));
			stmt.setString(7, instance.getSelectedUserId());
			
			stmt.setString(8, instance.getSelectedAditionalQueryFilter());
//			stmt.setString(9, instance.getIsActive());
			stmt.setString(9, instance.getIsAlertable());
			
			stmt.setString(10, instance.getStatus());
			stmt.setDate(11, SwtUtil.truncateDateTime(instance.getResolvedDatetime()));
			
			stmt.setString(12, instance.getCurrencyThresholdFlag());
			stmt.setString(13, instance.getAccountId());
			if(instance.getMovementId() != null)
				stmt.setString(14, ""+instance.getMovementId());
			else 
				stmt.setNull(14, Types.NUMERIC);
			if(instance.getMatchId() != null)
				stmt.setString(15, ""+instance.getMatchId());
			else 
				stmt.setNull(15, Types.NUMERIC);
			
			stmt.setString(16, instance.getIlmAccountGroup());
			if(instance.getSweepId() != null)
				stmt.setString(17, ""+instance.getSweepId());
			else 
				stmt.setNull(17, Types.NUMERIC);
			
			
			if(instance.getPaymentId() != null)
				stmt.setString(18, ""+instance.getPaymentId());
			else 
				stmt.setNull(18, Types.NUMERIC);
			
			stmt.setString(19, instance.getSelectedFilter());
			stmt.setString(20, instance.getSortedColumn() +" "+instance.getSortDirection());
			
			
            
//			System.err.println(instance.getFaciltityId());                       
//			System.err.println(instance.getHostId());                            
//			System.err.println(instance.getEntityId());                          
//			System.err.println(instance.getCurrencyCode());                      
//			System.err.println(SwtUtil.truncateDateTime(instance.getValueDate())); 
//			System.err.println("suerId"+instance.getSelectedUserId());                    
//			System.err.println("ThresholdFlag="+instance.getCurrencyThresholdFlag());             
//			System.err.println("addQuery  sss = "+instance.getSelectedAditionalQueryFilter());
//			System.err.println("active"+instance.getIsActive());                          
//			System.err.println("alertable="+("true".equals(instance.getIsAlertable())?"Y":"N"));
//			
//			System.err.println("accountId = "+instance.getAccountId());  
//			System.err.println("movId = "+instance.getMovementId());  
//			System.err.println("Matchid="+instance.getMatchId());  
//			System.err.println("accoutnGroup="+instance.getIlmAccountGroup());  
//			System.err.println("sweepId="+instance.getSweepId());  
//			System.err.println("paymentId="+instance.getPaymentId());  
//			System.err.println("seelcted tab = "+instance.getSelectedTab());
//			
//			System.err.println("instance.getStatus()=="+instance.getStatus());
			
			
			stmt.execute();
			/* Fetching the Result */
			// Account details
			rs = (ResultSet) stmt.getObject(1);
			
			
			
			

			// Execute the callable statement
//			rs = stmt.executeQuery();

			// Initialize collection to hold entity details
			recordsList = new ArrayList<AlertInstance>();
			// Iterate through currency result set and entity id and
			// entity name.
			
			AlertInstance record = new AlertInstance();
			while (rs.next()) {
				record = new AlertInstance();

				record.setId(rs.getDouble("ID"));
				record.setScenarioId(rs.getString("SCENARIO_ID"));
				record.setUniqueIdentifier(rs.getString("UNIQUE_IDENTIFIER"));
				record.setStatus(rs.getString("STATUS"));
				record.setRaisedDatetime(rs.getDate("RAISED_DATETIME"));
				record.setLastRaisedDatetime(rs.getDate("LAST_RAISED_DATETIME"));							
//				record.setRaisedUser(rs.getString("RAISED_USER"));
//				record.setLastRaisedUser(rs.getString("LAST_RAISED_USER"));						
				record.setResolvedDatetime(rs.getDate("RESOLVED_DATETIME"));
				record.setResolvedByUser(rs.getString("RESOLVED_BY_USER"));
				record.setEventsLaunchStatus(rs.getString("EVENTS_LAUNCH_STATUS"));
				record.setHostId(rs.getString("HOST_ID"));
				record.setEntityId(rs.getString("ENTITY_ID"));
				record.setCurrencyCode(rs.getString("CURRENCY_CODE"));
				record.setAccountId(rs.getString("ACCOUNT_ID"));
				record.setAmount(rs.getBigDecimal("AMOUNT"));
				record.setSign(rs.getString("SIGN"));
				record.setOverThreshold(rs.getString("OVER_THRESHOLD"));
				
				record.setMovementId(rs.getDouble("MOVEMENT_ID"));
				record.setMatchId(rs.getDouble("MATCH_ID"));
				record.setSweepId(rs.getDouble("SWEEP_ID"));
				record.setPaymentId(rs.getDouble("PAYMENT_ID"));
				
				record.setAttributesJson(rs.getString("ATTRIBUTES_JSON"));
				record.setValueDate(rs.getDate("VALUE_DATE"));
				record.setOtherId(rs.getString("OTHER_ID"));				
//				record.setOtherIdType(rs.getString("OTHER_ID_TYPE"));

				
				
				
				recordsList.add(record);

			}
			
			log.debug(this.getClass().getName() + " - [getInstanceList] - " + "Exit");
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstanceList] method : - " + exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
						"getInstanceList", ScenarioSummaryDAOHibernate.class);

			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
						"getInstanceList", ScenarioSummaryDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;

		}
		return recordsList;
	}
	
	
	/**
	 * This method is used to update S_POOL_STATS table with the latest connection pool details
	 *
	 * @param connectionsIds
	 * @return
	 * @throws SwtException
	 */
	public void updateScenInstanceStatus(String id, String status) throws SwtException {
		CallableStatement cstmt = null;
		String call = null;
		Connection conn = null;
		Session session = null;
		ResultSet rset = null;
		try {
			log.debug(this.getClass().getName() + " - [updateScenInstanceStatus] - Enter");
//			session = SwtUtil.sessionFactory.openSession();
//			connection = SwtUtil.connection(session);
			
				/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */	
			cstmt = conn
					.prepareCall("{call PKG_ALERT.SP_SET_INSTANCE_STATUS(?,?,?) }");
			cstmt.setString(1, id);
			cstmt.setString(2, status);
			cstmt.setString(3, UserThreadLocalHolder.getUser());
			
			cstmt.execute();
			conn.commit();
			log.debug(this.getClass().getName() + " - [updateScenInstanceStatus] - Exit");
			
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateScenInstanceStatus] method : - "
					+ e.getMessage());
		} finally {
			
			try {
				JDBCCloser.close(null, cstmt, conn, session);
			} catch (Exception e) {
				log.error(this.getClass().getName() + " - Exception Catched in [updateScenInstanceStatus] method : - "
						+ e.getMessage());
			}
		}
	}
	
	public List<ScenarioInstanceLog>  getInstanceLogs(String instanceId) throws SwtException {
	Session session = null;
	// Connection object
	Connection conn = null;
	// CallableStatement object
	PreparedStatement cstmt = null;
	// ResultSet object
	ResultSet rs = null;
	// CallableStatement object
	Statement statement = null;
	// Declare recordsList
	ArrayList<ScenarioInstanceLog> recordsList = null;


	try {
		log.debug(this.getClass().getName() + " - [getInstanceLogs] - " + "Entry");

		session = getHibernateTemplate().getSessionFactory().openSession();
		conn = SwtUtil.connection(session);
		/* Using Callable statement to execute the Stored Procedure */
		cstmt = conn.prepareStatement("select * from  P_SCENARIO_INSTANCE_LOG where instance_id=? order by id desc");
		
		
		cstmt.setString(1, instanceId);
		//recordsList=new ArrayList<>();
		// Execute the callable statement
		rs = cstmt.executeQuery();
		// Initialize collection to hold entity details
		recordsList = new ArrayList<ScenarioInstanceLog>();
		ScenarioInstanceLog record = new ScenarioInstanceLog();
		while (rs.next()) {
			record = new ScenarioInstanceLog();

			
			record.setId(rs.getDouble("ID"));
			record.setInstanceId(rs.getString("INSTANCE_ID"));
			record.setLogDateTime(rs.getDate("LOG_DATETIME"));
			record.setLogUser(rs.getString("LOG_USER"));
			record.setLogText(rs.getString("LOG_TEXT"));
			recordsList.add(record);
		}
		
		log.debug(this.getClass().getName() + " - [getInstanceLogs] - " + "Exit");
	} catch (Exception exp) {
		exp.printStackTrace();
		log.error(this.getClass().getName()
				+ " - Exception Catched in [getInstanceLogs] method : - " + exp.getMessage());
	} finally {
		SwtException thrownException = null;
		Object[] exceptions = JDBCCloser.close(rs, statement, conn, session);

		if (exceptions[0] != null)
			thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
					"getInstanceLogs", ScenarioSummaryDAOHibernate.class);

		if (thrownException == null && exceptions[1] != null)
			thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
					"getInstanceLogs", ScenarioSummaryDAOHibernate.class);

		if (thrownException != null)
			throw thrownException;

	}
	return recordsList;
}
	
	/**
	 * This method is used to get scenario instance access from P_NOTIFY_SCENARIO table
	 */
	public String getScenarioInstAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getScenarioInstAccess] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String access = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getScenAccess = "select full_instance_access from P_NOTIFY_SCENARIO notif where notif.scenario_id=? "
					+ " and (notif.entity_id=? or notif.entity_id='All') and notif.host_id=? and (notif.role_id=? or notif.role_id='All')";

			stmt = conn.prepareStatement(getScenAccess);			
			stmt.setString(1, scenarioId);
			stmt.setString(2, entityId);
			stmt.setString(3, hostId);
			stmt.setString(4, roleId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					access = rs.getString(1);
					if("Y".equalsIgnoreCase(access)) {
						return access;
					}
				}
			}			
			log.debug(this.getClass().getName() + " - [getScenarioInstAccess] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioInstAccess] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return access;
	}
	
	/**
	 * This method is used to get scenario instance access from P_NOTIFY_SCENARIO table
	 */
	public String getScenarioReqAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getScenarioInstAccess] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String access = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getScenAccess = "select access_required from P_NOTIFY_SCENARIO notif where notif.scenario_id=? "
					+ " and (notif.entity_id=? or notif.entity_id='All') and notif.host_id=? and (notif.role_id=? or notif.role_id='All')";

			stmt = conn.prepareStatement(getScenAccess);			
			stmt.setString(1, scenarioId);
			stmt.setString(2, entityId);
			stmt.setString(3, hostId);
			stmt.setString(4, roleId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					access = rs.getString(1);
					if("Y".equalsIgnoreCase(access)) {
						return access;
					}
				}
			}			
			log.debug(this.getClass().getName() + " - [getScenarioInstAccess] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioInstAccess] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return access;
	}
	/*public String getScenarioInstAccess(String scenarioId, String hostId, String roleId, String entityId){
		String retValue = "N";
		Connection conn=null;
		CallableStatement pstmt=null;
		Session session = null;

		try{
			// Creating a session from the session factory instance
			session = getHibernateTemplate().getSessionFactory().openSession();
			// fetching the connection from session
			conn = SwtUtil.connection(session);
			pstmt=conn.prepareCall("{? = call PKG_ALERT.FN_GET_SCENARIO_ACCESS (?, ?, ?, ?)}");
			pstmt.setString(1,"ILM_GRP");
			pstmt.setString(2,"RABO");
			pstmt.setString(3,"All");
			pstmt.setString(4,"All");
			pstmt.registerOutParameter(5, Types.VARCHAR);
			// Executing the stored procedure
			pstmt.execute();
			retValue=pstmt.getString(1);
		/*}catch(Exception  e){
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
													  .handleException(e,"getScenarioInstAccess", ScenarioSummaryDAOHibernate.class));
			log.debug("ScenarioSummary: Error in getScenarioInstAccess - " + e.getMessage());
		}finally{
			JDBCCloser.close(null, pstmt, null, session);
				
		}
		return retValue;
	}*/
	
	
	/**
	 * This method is used to get scenario facility from p_scenario table
	 */
	public String getScenarioFacility(String scenarioId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getScenarioFacility] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String scenarioFacility = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getScenarioFacility = "select facility_id from  p_scenario scen where scen.scenario_id=?";
			stmt = conn.prepareStatement(getScenarioFacility);
			stmt.setString(1, scenarioId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					scenarioFacility = rs.getString(1);
				}
			}			
			log.debug(this.getClass().getName() + " - [getScenarioFacility] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioFacility] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return scenarioFacility;
	}
	
	/**
	 * This method is used to get scenario facility from p_scenario table
	 */
	public String getInstAttXml(String instanceId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getInstAttXml] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String instanceXml = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getInstanceXml= "select attributes_json from  p_scenario_instance inst where inst.id=?";
			stmt = conn.prepareStatement(getInstanceXml);
			stmt.setString(1, instanceId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					instanceXml = rs.getString(1);
				}
			}			
			log.debug(this.getClass().getName() + " - [getInstAttXml] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstAttXml] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return instanceXml;
	}
	

		public AlertInstance getInstanceNewData(String instanceId) throws SwtException{
			log.debug(this.getClass().getName() + " - [getInstanceNewData] - "
					+ "Entry");
			/* Method's local variable declaration */
			Connection conn = null;
			Session session = null;
			PreparedStatement stmt = null;
			ResultSet rs = null;
			AlertInstance instance = new AlertInstance();
			try {
				conn = ConnectionManager.getInstance().databaseCon();
				String getInstanceXml= "select * from  p_scenario_instance inst where inst.id=?";
				stmt = conn.prepareStatement(getInstanceXml);
				stmt.setString(1, instanceId);
				stmt.execute();
				/* Fetching the Result */
				rs = (ResultSet) stmt.getResultSet();
				if (rs != null) {
					/* Loop to iterate result set */
					while (rs.next()) {
						
						instance.setId(rs.getDouble("ID"));
						instance.setScenarioId(rs.getString("SCENARIO_ID"));
						instance.setUniqueIdentifier(rs.getString("UNIQUE_IDENTIFIER"));
						instance.setStatus(rs.getString("STATUS"));
						instance.setRaisedDatetime(rs.getDate("RAISED_DATETIME"));
						instance.setLastRaisedDatetime(rs.getDate("LAST_RAISED_DATETIME"));							
						instance.setResolvedDatetime(rs.getDate("RESOLVED_DATETIME"));
						instance.setResolvedByUser(rs.getString("RESOLVED_BY_USER"));
						instance.setEventsLaunchStatus(rs.getString("EVENTS_LAUNCH_STATUS"));
						instance.setHostId(rs.getString("HOST_ID"));
						instance.setEntityId(rs.getString("ENTITY_ID"));
						instance.setCurrencyCode(rs.getString("CURRENCY_CODE"));
						instance.setAccountId(rs.getString("ACCOUNT_ID"));
						instance.setAmount(rs.getBigDecimal("AMOUNT"));
						instance.setSign(rs.getString("SIGN"));
						instance.setOverThreshold(rs.getString("OVER_THRESHOLD"));
						
						instance.setMovementId(rs.getDouble("MOVEMENT_ID"));
						instance.setMatchId(rs.getDouble("MATCH_ID"));
						instance.setSweepId(rs.getDouble("SWEEP_ID"));
						instance.setPaymentId(rs.getDouble("PAYMENT_ID"));						
						instance.setAttributesJson(rs.getString("ATTRIBUTES_JSON"));
						instance.setValueDate(rs.getDate("VALUE_DATE"));
						instance.setOtherId(rs.getString("OTHER_ID"));

					}
				}			
				log.debug(this.getClass().getName() + " - [getInstanceNewData] - "
						+ "Exit");
			} catch (Exception exp) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getInstanceNewData] method : - "
						+ exp.getMessage());
			} finally {
				JDBCCloser.close(rs, stmt, conn, session);

			}
			return instance;
		}
		
		public ArrayList<ScenarioInstanceMessage> getInstanceMessages(String instanceId) throws SwtException {
			Session session = null;
			// Connection object
			Connection conn = null;
			// CallableStatement object
			PreparedStatement cstmt = null;
			// ResultSet object
			ResultSet rs = null;
			// CallableStatement object
			Statement statement = null;
			// Declare recordsList
			ArrayList<ScenarioInstanceMessage> recordsList = null;


			try {
				log.debug(this.getClass().getName() + " - [getInstanceLogs] - " + "Entry");

				session = getHibernateTemplate().getSessionFactory().openSession();
				conn = SwtUtil.connection(session);
				/* Using Callable statement to execute the Stored Procedure */
				cstmt = conn.prepareStatement("select * from  P_SCENARIO_INSTANCE_MESSAGE where instance_id=? order by update_date desc");
				cstmt.setString(1, instanceId);
				//recordsList=new ArrayList<>();
				// Execute the callable statement
				rs = cstmt.executeQuery();
				// Initialize collection to hold entity details
				recordsList = new ArrayList<ScenarioInstanceMessage>();
				ScenarioInstanceMessage record = new ScenarioInstanceMessage();
				while (rs.next()) {
					record = new ScenarioInstanceMessage();

					
					record.setInstanceId(rs.getString("INSTANCE_ID"));
					record.setFormatId(rs.getString("FORMAT_ID"));
					record.setMessageId(rs.getString("MESSAGE_ID"));
					record.setInputDate(rs.getDate("INPUT_DATE"));
					record.setUpdateDate(rs.getDate("UPDATE_DATE"));
					record.setUpdateUser(rs.getString("UPDATE_USER"));
					recordsList.add(record);
				}
				
				log.debug(this.getClass().getName() + " - [getInstanceLogs] - " + "Exit");
			} catch (Exception exp) {
				exp.printStackTrace();
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getInstanceMessages] method : - " + exp.getMessage());
			} finally {
				SwtException thrownException = null;
				Object[] exceptions = JDBCCloser.close(rs, statement, conn, session);

				if (exceptions[0] != null)
					thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0],
							"getInstanceMessages", ScenarioSummaryDAOHibernate.class);

				if (thrownException == null && exceptions[1] != null)
					thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1],
							"getInstanceMessages", ScenarioSummaryDAOHibernate.class);

				if (thrownException != null)
					throw thrownException;

			}
			return recordsList;
		}
	
		
		/**
		 * This method is used to get host name from s_host table
		 */
		public String getHostName(String hostId) throws SwtException{
			log.debug(this.getClass().getName() + " - [getHostName] - "
					+ "Entry");
			/* Method's local variable declaration */
			Connection conn = null;
			Session session = null;
			PreparedStatement stmt = null;
			ResultSet rs = null;
			String hostName = new String();
			try {
				conn = ConnectionManager.getInstance().databaseCon();
				String query = "select host_name from  s_host s where s.host_id=?";
				stmt = conn.prepareStatement(query);
				stmt.setString(1, hostId);
				stmt.execute();
				/* Fetching the Result */
				rs = (ResultSet) stmt.getResultSet();
				if (rs != null) {
					/* Loop to iterate result set */
					while (rs.next()) {
						hostName = rs.getString(1);
					}
				}			
				log.debug(this.getClass().getName() + " - [getHostName] - "
						+ "Exit");
			} catch (Exception exp) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getHostName] method : - "
						+ exp.getMessage());
			} finally {
				JDBCCloser.close(rs, stmt, conn, session);

			}
			return hostName;
		}
}
