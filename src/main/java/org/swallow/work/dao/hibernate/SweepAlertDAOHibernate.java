/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtMailSender;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.SweepAlertDAO;
import org.swallow.work.model.SweepAlert;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Tripathi has modified this class on 25th Feb 08
 * 
 */
@Repository("sweepAlertDAO")
@Transactional
public class SweepAlertDAOHibernate extends CustomHibernateDaoSupport implements SweepAlertDAO {
    private static final Log log = LogFactory.getLog(SweepAlertDAOHibernate.class);

    public SweepAlertDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    @Override
    public Collection<SweepAlert> checkSweepAlertMessages(String hostId, String roleId, SystemFormats formats) throws SwtException {
        Collection<SweepAlert> collAlerts = checkSweepNonMGAlertMessages(hostId, roleId);
        User usr = SwtUtil.getCurrentUser(UserThreadLocalHolder.getUserSession());
        
        if (usr.getAlertType().equals(SwtConstants.ALERT_BOTH) || 
            usr.getAlertType().equals(SwtConstants.ALERT_EMAIL)) {
            sendMailtoNonMGAlerts(collAlerts);
        }
        return collAlerts;
    }

    private void sendMailtoNonMGAlerts(Collection<SweepAlert> collNonMGAlerts) throws SwtException {
        User usr = SwtUtil.getCurrentUser(UserThreadLocalHolder.getUserSession());
        String mailFrom = "<EMAIL>";
        String mailTo = usr.getEmailId();

        for (SweepAlert alert : collNonMGAlerts) {
            String message = alert.getAlertMaster().getAlertmessage();
            StringBuilder mailBody = new StringBuilder()
                .append("Message - ").append(message).append("\n")
                .append("Sweep Id - ").append(alert.getSweepId()).append("\n")
                .append("Sweep Amount - ").append(alert.getSweepAmount()).append("\n\n");

            SwtMailSender.getInstance().sendMail(mailFrom, mailTo, message, mailBody.toString());
        }
    }

    private void setMGAlertsAsInterSession(Collection<SweepAlert> mgSweepAlerts, SystemFormats formats) throws SwtException {
        SessionManager sessionManager = SessionManager.getInstance();
        String sessionId = UserThreadLocalHolder.getUserSessionId();
        StringBuilder messageBuf = new StringBuilder();

        for (SweepAlert alert : mgSweepAlerts) {
            if (alert != null) {
                messageBuf.append(getMGAlertMessages(alert, formats));
            }
        }
        sessionManager.sendMessage(sessionId, messageBuf.toString());
    }

    private StringBuffer getMGAlertMessages(SweepAlert alert, SystemFormats formats) throws SwtException {
        if (alert == null) {
            return new StringBuffer();
        }

        return new StringBuffer()
            .append("Message - ").append(alert.getAlertMaster().getAlertmessage()).append("\n")
            .append("Sweep Id - ").append(alert.getSweepId()).append("\n")
            .append("Sweep Amount - ")
            .append(SwtUtil.formatCurrency(alert.getSweepAmount(), formats.getCurrencyFormat()))
            .append("\n\n");
    }

    private Collection<SweepAlert> checkSweepNonMGAlertMessages(String hostId, String roleId) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "from SweepAlert s where s.id.hostId = :hostId and " +
                "(s.alertMaster.roleId = :roleId or s.alertMaster.roleId = 'All') " +
                "and s.status = 'P'", SweepAlert.class)
                .setParameter("hostId", hostId)
                .setParameter("roleId", roleId)
                .getResultList();
        } catch (Exception e) {
            log.error("Error in checkSweepNonMGAlertMessages", e);
            throw new SwtException(e.getMessage());
        }
    }

    @Override
    public String getMenuAccessId(SweepAlert sweepAlert) throws SwtException {
        String sql = "Select a.menu_item_id, a.access_id, p.program_name, m.width, m.height " +
                    "from p_menu_access a, s_program p, p_menu_item m " +
                    "WHERE a.role_id = ? and m.menu_item_id = ? " +
                    "and a.menu_item_id = m.menu_item_id and m.program_id = p.program_id";

        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, sweepAlert.getAlertMaster().getRoleId());
            stmt.setString(2, SwtConstants.getAlertStageMenuitems(sweepAlert.getAlertStage()));

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String accessId = rs.getString("access_id");
                    String programName = rs.getString("program_name");
                    String alertUrl = programName + 
                        (programName.contains("?") ? "&" : "?") + 
                        "menuAccessId=" + accessId;

                    sweepAlert.setWidth(rs.getString("width"));
                    sweepAlert.setHeight(rs.getString("height"));
                    sweepAlert.setAccessId(accessId);
                    return alertUrl;
                }
                return "#";
            }
        } catch (Exception e) {
            log.error("Error in getMenuAccessId", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getMenuAccessId", this.getClass());
        }
    }

    @Override
    public Collection<SweepAlert> getMovementAlert(String hostId, String entityId, Long movementId, 
            String alertStage, String status) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "from SweepAlert s where s.id.hostId = :hostId " +
                "and s.id.entityId = :entityId and s.sweepId = :movementId " +
                "and s.alertStage = :alertStage and s.status = :status", SweepAlert.class)
                .setParameter("hostId", hostId)
                .setParameter("entityId", entityId)
                .setParameter("movementId", movementId)
                .setParameter("alertStage", alertStage)
                .setParameter("status", status)
                .getResultList();
        } catch (Exception e) {
            log.error("Error in getMovementAlert", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getMovementAlert", this.getClass());
        }
    }

    @Override
    public void createMovementAlert(SweepAlert sweepAlert) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.save(sweepAlert);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Error in createMovementAlert", e);
            throw SwtErrorHandler.getInstance().handleException(e, "createMovementAlert", this.getClass());
        }
    }

    @Override
    public void updateMovementAlert(SweepAlert sweepAlert) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.update(sweepAlert);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Error in updateMovementAlert", e);
            throw new SwtException(e.getMessage());
        }
    }

    @Override
    public String getSweepCurrency(Long sweepId) throws SwtException {
        String hostId = CacheManager.getInstance().getHostId();
        try (Session session = getSessionFactory().openSession()) {
            String currency = session.createQuery(
                "select s.currencyCode from Sweep s where s.id.hostId = :hostId and s.id.sweepId = :sweepId",
                String.class)
                .setParameter("hostId", hostId)
                .setParameter("sweepId", sweepId)
                .uniqueResult();
            return currency != null ? currency : SwtConstants.EMPTY_STRING;
        } catch (Exception e) {
            log.error("Error in getSweepCurrency", e);
            throw new SwtException(e.getMessage());
        }
    }

    @Override
    public void deleteMovementAlert(SweepAlert movementAlert) throws SwtException {
        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.delete(movementAlert);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Error in deleteMovementAlert", e);
            throw SwtErrorHandler.getInstance().handleException(e, "deleteMovementAlert", this.getClass());
        }
    }
}
