/*
 * @(#)GenericDisplayMonitorDAOHibernate.java 1.0 30/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CommonDataManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.GenericDisplayMonitorDAO;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

@Repository ("genericDisplayMonitorDAO")
@Transactional
public class GenericDisplayMonitorDAOHibernate extends CustomHibernateDaoSupport
		implements GenericDisplayMonitorDAO {
	public GenericDisplayMonitorDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(GenericDisplayMonitorDAOHibernate.class);

	/**
	 * Return the number of existing record result of the Base Query
	 * 
	 * @param baseQuery
	 * @return String
	 * @throws SwtException 
	 * 
	 */
	public String getRecords(String baseQuery) throws SwtException {
		
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		int count=0;
		String result=null;
		StringBuffer buffer = null;
		long startTime = 0;
		long endTime = 0;
		try {
			buffer = new StringBuffer();
			log.debug(this.getClass().getName() + " - [getRecords] - "
					+ "Entry");
			startTime = System.currentTimeMillis();
			baseQuery = "SELECT count(*) vCount FROM (\n"+baseQuery+" \n)";
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute(baseQuery);			
			rs = stmt.getResultSet();
			if(rs!=null){
			//If query is correct extract number of records
			while(rs.next()){
            	count = rs.getInt("vCount");
			}
			endTime = System.currentTimeMillis();
			
			buffer.append(count + SwtConstants.SEPARATOR_RECORD);
			buffer.append((endTime-startTime)+ SwtConstants.SEPARATOR_RECORD);
			result = buffer.toString();
			log.debug(this.getClass().getName() + " - [getRecords] - "
					+ "Exit");
			}else {
				throw new SwtException("Error occurred in the underlying treatment layer, please investigate database logs...");
			}
		} catch (SQLException SqlExp) {
			count=-1;
			buffer.append(count +SwtConstants.SEPARATOR_RECORD+ SwtConstants.SEPARATOR_RECORD + SqlExp.getMessage());
			result = buffer.toString();
		}
		catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRecords] method : - "
					+ exp.getMessage());
			count=-1;
			buffer.append(count + SwtConstants.SEPARATOR_RECORD + "Cannot Execute Query..");
			result = buffer.toString();
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getRecords",GenericDisplayMonitorDAOHibernate.class);

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getRecords",GenericDisplayMonitorDAOHibernate.class);

			if (thrownException != null)
				throw thrownException;
		}		
		return result;
	}
	
	/**
	 * Return the number of existing record result of the Base Query
	 * @param baseQuery
	 * @param filter
	 * @return QueryResult
	 * @throws SwtException 
	 * 
	 */
	public QueryResult getGenericDisplayData(String baseQuery) throws SwtException {
		
		// Session object
		Session session = null;
		// Connection object
		Connection conn = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		// ResultSet object
		ResultSet rs = null;
		QueryResult queryResult = null;
		Statement stmt = null;
		ArrayList<ColumnMetadata> metadataList= null;
		ColumnMetadata columnMetadata;
		GenericDisplayPageDTO page ;
		int numberOfColumns = 0;
		int count = 0;
		String selectedFilter  = null;
		try {
				log.debug(this.getClass().getName()
						+ " - [getGenericDisplayData] - " + "Entry");
				/*
				 * Returns the HibernateTemplate and session factory for this DAO,
				 * and opening a Hibernate Session
				 */
				session = getHibernateTemplate().getSessionFactory().openSession();
				conn = SwtUtil.connection(session);
				/* Using Callable statement to execute the Stored Procedure */					
				cstmt = conn
						.prepareCall("{call PKG_ALERT.PRC_EXEC_QUERY(?,?,?,?,?,?,?,?,?)}");
				
				cstmt.setString(1,baseQuery);
				cstmt.setString(2,"");
				cstmt.setString(3,"");
				cstmt.setInt(4, 1);
				cstmt.setInt(5,SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE));					
				cstmt.setString(6, "");
				cstmt.registerOutParameter(7, oracle.jdbc.OracleTypes.CURSOR);
				cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.NUMBER);
				cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.VARCHAR);

				cstmt.execute();
				/* Fetching the Result */
				count =  cstmt.getInt(8);
				rs = (ResultSet) cstmt.getObject(7);
				if(rs!=null){
				queryResult = new QueryResult();
				ResultSetMetaData rsMetaData = rs.getMetaData();
				metadataList = new ArrayList<ColumnMetadata>();
				numberOfColumns = rsMetaData.getColumnCount();		
				
				for (int i = 2; i < numberOfColumns+1; i++) {
					columnMetadata = new ColumnMetadata();
					//set metadata attributes
					columnMetadata.setColumnDisplaySize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setColumnClassName(rsMetaData.getColumnClassName(i));
					columnMetadata.setColumnLabel(rsMetaData.getColumnLabel(i));
					columnMetadata.setColumnName("column"+(i-1));
					columnMetadata.setColumnType(rsMetaData.getColumnType(i));
					columnMetadata.setColumnTypeName(rsMetaData.getColumnTypeName(i));
					columnMetadata.setSize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setScale(rsMetaData.getScale(i));
					//add metadata object to the list
					metadataList.add(columnMetadata);
				}
				
				// Define a custom comparator to sort based on column label
				
				queryResult.setMetadataDetails(metadataList) ;
				ArrayList<HashMap<String, Object>>  recordsList=new ArrayList<HashMap<String, Object>> ();
				while(rs.next()){
					int cols = rs.getMetaData().getColumnCount();
					LinkedHashMap<String, Object> arr = new LinkedHashMap<String, Object>();
					for (int i = 1; i < cols; i++) {
							arr.put(metadataList.get(i - 1).getColumnLabel(),rs.getObject(i + 1)!=null?rs.getObject(i + 1):"");
						
					}
					recordsList.add(arr);
				}
			
				selectedFilter = new String();
				for (int i = 0;i<numberOfColumns-1;i++)
					selectedFilter+="All|";
				selectedFilter+="All";
				
				page = new GenericDisplayPageDTO();
				page.setPageNumber(1);
				page.setTotalSize(count);
				page.setOrderByTag("");
				page.setRecordsPerPage(SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE));
				page.setSelectedFilter(selectedFilter);
				page.setSelectedSort("");			
				queryResult.setPage(page);
				queryResult.setQueryResult(recordsList);
				queryResult.setQueryException("");						
				log.debug(this.getClass().getName() + " - [getGenericDisplayData] - "
						+ "Exit");
				}else {
					throw new SwtException("Error occurred in the underlying treatment layer, please investigate database logs...");
				}
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGenericDisplayData] method : - "
					+ exp.getMessage());
			if (queryResult != null) {
				queryResult.setQueryException(exp.getMessage());
			}
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getGenericDisplayData",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getGenericDisplayData",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
			
		}
		return queryResult;
	}

	/**
	 * Get the query result for a specific page in the generic display screen
	 * 
	 * @param page
	 * @param opTimer
	 * @param baseQuery
	 * @param scenarioId
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public QueryResult getQueryResultPage(GenericDisplayPageDTO page, OpTimer opTimer,String baseQuery,String scenarioId, String roleId,String currencyGroup,String applyCurrencyThreshold,boolean...fromExport)
			throws SwtException {
		
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		QueryResult queryResult = null;
		// CallableStatement object
		CallableStatement cstmt = null;
		ArrayList<ColumnMetadata> metadataList= null;
		ColumnMetadata columnMetadata;
		int numberOfColumns = 0;
		int fromValue= 0;
		int toValue= 0;
		int count = 0;
		String orderBy  = "";
		String sortDirection = "";
		String[] sort;
		CommonDataManager CDM = null;
		String cancelExport = ""; 
		String queryText = "";

		try {
			log.debug(this.getClass().getName() + " - [getQueryResultPage] - "
					+ "Entry");
			if (!page.getSelectedSort().equals("1")) {
				sort = page.getSelectedSort().split("\\|");
				orderBy = sort[0];
				sortDirection = sort[1];
			}
			if(!page.isAllPages())
			{
				fromValue = (page.getPageNumber() - 1) * page.getRecordsPerPage();
				toValue = page.getPageNumber() * page.getRecordsPerPage();
			}else {
				fromValue=0;
				toValue= SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE);
			}
			
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opening a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
		

			try {
				CDM = (CommonDataManager)	UserThreadLocalHolder.getUserSession().getAttribute("CDM");

			}catch (Exception exp) {
			}
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */	
			if(SwtUtil.isEmptyOrNull(baseQuery)){
			cstmt = conn
						.prepareCall("{call PKG_ALERT.PRC_EXEC_GENERIC_SCENARIO(?,?,?,?,?,?,?,?,?,?,?,?)}");
				cstmt.setString(1,scenarioId);
				cstmt.setString(2,page.getSelectedFilter()!=null?page.getSelectedFilter():"");
				cstmt.setString(3,orderBy);
				cstmt.setInt(4, fromValue);
				cstmt.setInt(5, toValue);			
				cstmt.setString(6, sortDirection);
				cstmt.setString(7, roleId);
				cstmt.setString(8, currencyGroup);
				cstmt.setString(9, applyCurrencyThreshold);
				cstmt.registerOutParameter(10, oracle.jdbc.OracleTypes.CURSOR);
				cstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.NUMBER);
				cstmt.registerOutParameter(12, oracle.jdbc.OracleTypes.VARCHAR);
				cstmt.execute();
				/* Fetching the Result */
				count =  cstmt.getInt(11);
				rs = (ResultSet) cstmt.getObject(10);
				queryText =cstmt.getString(12);
			}else {
				cstmt = conn
						.prepareCall("{call PKG_ALERT.PRC_EXEC_QUERY(?,?,?,?,?,?,?,?,?)}");
				cstmt.setString(1,baseQuery);
				cstmt.setString(2,page.getSelectedFilter()!=null?page.getSelectedFilter():"");
				cstmt.setString(3,orderBy);
				cstmt.setInt(4, fromValue);
				cstmt.setInt(5, toValue);					
				cstmt.setString(6, sortDirection);
				cstmt.registerOutParameter(7, oracle.jdbc.OracleTypes.CURSOR);
				cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.NUMBER);
				cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.VARCHAR);
				cstmt.execute();
				/* Fetching the Result */
				count =  cstmt.getInt(8);
				queryText =cstmt.getString(9);
				rs = (ResultSet) cstmt.getObject(7);
			
			}
			queryResult = new QueryResult();
			metadataList = new ArrayList<ColumnMetadata>();
			if(rs!=null){
				
				ResultSetMetaData rsMetaData = rs.getMetaData();
				
				numberOfColumns = rsMetaData.getColumnCount();		
				
				for (int i = 2; i < numberOfColumns+1; i++) {
					columnMetadata = new ColumnMetadata();
					//set metadata attributes
					columnMetadata.setColumnDisplaySize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setColumnClassName(rsMetaData.getColumnClassName(i));
					columnMetadata.setColumnLabel(rsMetaData.getColumnLabel(i));
					columnMetadata.setColumnName("column"+(i-1));
					columnMetadata.setColumnType(rsMetaData.getColumnType(i));
					columnMetadata.setColumnTypeName(rsMetaData.getColumnTypeName(i));
					columnMetadata.setSize(rsMetaData.getColumnDisplaySize(i));
					columnMetadata.setScale(rsMetaData.getScale(i));
					//add metadata object to the list
					metadataList.add(columnMetadata);
					
				}
			
				queryResult.setMetadataDetails(metadataList) ;
				
				ArrayList<HashMap<String, Object>> recordsList= new ArrayList<HashMap<String,Object>>();
				while (rs.next()) {
					// Cancel Export
					
					if(fromExport!=null && fromExport.length>0 && fromExport[0]==true && CDM != null){
						cancelExport = CDM.getCancelExport() == null ? cancelExport
								: CDM.getCancelExport();
						if (cancelExport.equals("true")) {
							break;
						}
					}
					int cols = rs.getMetaData().getColumnCount();
					LinkedHashMap<String, Object> arr = new LinkedHashMap<String, Object>();
					for (int i = 1; i < cols; i++) {
						arr.put(metadataList.get(i - 1).getColumnLabel(),rs.getObject(i + 1)!=null?rs.getObject(i + 1):"");
					}
					recordsList.add(arr);
				}
				if (queryResult != null) {
					page.setTotalSize(count);
					queryResult.setPage(page);
					if (page.isAllPages()) {
						queryText += ""+ (SwtUtil.getPageSizeFromProperty(SwtConstants.ALERT_DISPLAY_PAGE_SIZE)* 300);
					} else {
						queryText+= + toValue;
					}
					queryResult.setExecutedQuery(queryText);
					queryResult.setQueryResult(recordsList);
					queryResult.setQueryException("");
				}
				
			}else {
				throw new SwtException("Error occurred in the underlying treatment layer, please investigate database logs...");
			}
			log.debug(this.getClass().getName() + " - [getQueryResultPage] - "
					+ "Exit");
		} catch (SQLException exp) {
			log.error(this.getClass().getName()
					+ " - SQLException Catched in [getQueryResultPage] method : - "
					+ exp.getMessage());
			if (queryResult != null) {
			queryResult.setQueryException(exp.getMessage());
			}
		}catch(OutOfMemoryError exp){
			throw new SwtException("errors.OutOfMemoryError");
		}
		catch (Exception swtEx){
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getQueryResultPage] method : - "
					+ swtEx.getMessage());
		}finally {
			SwtException thrownException = null;
			stmt = null;
			rs = null;
			// CallableStatement object
			cstmt = null;
			metadataList= null;
			columnMetadata = null;
			orderBy  = null;
			sortDirection = null;
			sort = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getQueryResultPage",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getQueryResultPage",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
		}		
		return queryResult;
	}

	/**
	 * Get the screen details of a defined facility from data base.
	 * 
	 * @param facilityId
	 * @return ArrayList
	 * @throws SwtException
	 */
	public ArrayList getScreenDetails(String facilityId) throws SwtException {
		String query = "";
		
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		ArrayList<String> screenDetails = null;
		try{
			log.debug(this.getClass().getName() + " - [getScreenDetails] - "
					+ "Entry");
			query = "SELECT P.PROGRAM_NAME, M.WIDTH, M.HEIGHT " +
					"from S_PROGRAM P, P_MENU_ITEM M, P_FACILITY F " +
					"where P.PROGRAM_ID = M.PROGRAM_ID " +
					"and F.FACILITY_ID ='" + facilityId + "'" + 
					" and P.PROGRAM_ID = F.PROGRAM_ID";
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			stmt = conn.createStatement();
			stmt.execute(query);			
			rs = stmt.getResultSet();
			rs.next();
			screenDetails = new ArrayList<String>();
			// the first element in the rs is the program name
			screenDetails.add(rs.getString(1));
			// the second element in the rs is the height
			screenDetails.add(rs.getString(2));
			// the third element in the rs is the width
			screenDetails.add(rs.getString(3));
			log.debug(this.getClass().getName() + " - [getScreenDetails] - "
					+ "Exit");
		}catch (Exception swtEx){			
		log.error(this.getClass().getName()
				+ " - Exception Catched in [getScreenDetails] method : - "
				+ swtEx.getMessage());
		}finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getScreenDetails",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getScreenDetails",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
		}		
		return screenDetails;
	}
	
	/**
	 * Return the facility access of the required facility screen. The user has access to the facility screen if he: 
	 * - Has access to the menu item 
	 * - Has access to the entity id
	 * - Has access to the currency id 
	 * It returns a string which contains:
	 * - 0: full access
	 * - 1: view access
	 * - 2: no access
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @param currencyCode
	 * @param facilityId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String roleId,String entityId, String currencyCode, String facilityId) throws SwtException {
		
		Connection conn = null;
		Session session = null;
		Statement stmt = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		String access = null;
		
		try {
			log.debug(this.getClass().getName() + " - [getFacilityAccess] - "
					+ "Entry");
			/*
			 * Returns the HibernateTemplate and session factory for this DAO,
			 * and opens a Hibernate Session
			 */
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			/* Using Callable statement to execute the Stored Procedure */					
			cstmt = conn
					.prepareCall("select PKG_ALERT.FN_GET_FACILITY_ACCESS(?,?,?,?,?) from dual");
			cstmt.setString(1,hostId);
			cstmt.setString(2,roleId);
			cstmt.setString(3,entityId);
			cstmt.setString(4, currencyCode);
			cstmt.setString(5, facilityId);
			rs= cstmt.executeQuery();
			rs.next();
			// get the templateId
			access = (String) rs.getString(1);	
			log.debug(this.getClass().getName() + " - [getFacilityAccess] - "
					+ "Exit");
		} catch (SQLException exp) {
			log.error(this.getClass().getName()
					+ " - SQLException Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
		}catch (Exception swtEx){			
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ swtEx.getMessage());
		}
		finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, stmt, conn, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getFacilityAccess",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getFacilityAccess",GenericDisplayMonitorDAOHibernate.class);
			
			if (thrownException != null)
				throw thrownException;
		}
		
		return access;
		
	}
	
}