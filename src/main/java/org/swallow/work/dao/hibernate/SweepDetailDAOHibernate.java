/*
 * @(#)SweepDetailDAOHibernate.java 23/01/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.dao.SweepDetailDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepAmount;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.model.SweepNote;

/**
 * <AUTHOR> This class is used to Retrive the Sweep Data's From the
 *         Database
 */
@Component ("sweepDetailDAO")
@Repository
public class SweepDetailDAOHibernate extends CustomHibernateDaoSupport implements
		SweepDetailDAO {
	
	public SweepDetailDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepDetailDAOHibernate.class);

	/**
	 * Method to get account details for selected account ID
	 * 
	 * @param entityId
	 * @param hostId
	 * @param accId
	 * @return Collection<AcctMaintenance>
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	@Override
    public List<AcctMaintenance> getAccountDetails(String entityId, String hostId, String accId) throws SwtException {
        log.debug("Entering getAccountDetails method");
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "from AcctMaintenance acct where acct.id.entityId = :entityId " +
                "and acct.id.hostId = :hostId and acct.id.accountId = :accId", AcctMaintenance.class)
                .setParameter("entityId", entityId)
                .setParameter("hostId", hostId)
                .setParameter("accId", accId)
                .getResultList();
        } catch (Exception e) {
            log.error("Error in getAccountDetails", e);
            throw new SwtException(e.getMessage());
        }
    }

	public static Date string2Date(String dateString, String format)
			throws SwtException {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat();

			if (format.equals("D")) {
				sdf = new SimpleDateFormat("dd/MM/yyyy");
			} else {
				sdf = new SimpleDateFormat("MM/dd/yyyy");
			}

			return new Date(sdf.parse(dateString).getTime());
		} catch (ParseException parseException) {

			throw new SwtException(parseException.getMessage());
		}
	}

	public Double getPredictBalance(String entityId, String hostId, String currencyId, 
            String accountId, String date, String format) throws SwtException {
        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(
                 "{call PK_MONITORS.SP_GET_SWEEP_PREDICT_BALANCE(?,?,?,?,?,?,?)}")) {

            Date valuedate = string2Date(date.trim(), format);
            String systemDate = SwtUtil.getSystemDateString();
            Date todayDate = string2Date(systemDate.trim(), format);

            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setString(4, accountId);
            cstmt.setDate(5, valuedate);
            cstmt.setDate(6, todayDate);
            cstmt.registerOutParameter(7, Types.DOUBLE);

            cstmt.execute();
            return cstmt.getDouble(7);
        } catch (Exception e) {
            log.error("Error in getPredictBalance", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getPredictBalance", this.getClass());
        }
    }

	
	
	/**
	 * Get used sweep formats for selected sweep id
	 */
	public String getSweepFormatFromSweep(String sweepId) throws SwtException {
        log.debug("Entering getSweepFormatFromSweep method");
        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall("{? = call sweeping_process.FN_GET_MESSAGE_FORMAT(?)}")) {
            
            cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.VARCHAR);
            cstmt.setString(2, sweepId);
            cstmt.execute();
            return cstmt.getString(1);
        } catch (Exception e) {
            log.error("Error in getSweepFormatFromSweep", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getSweepFormatFromSweep", this.getClass());
        }
    }

	/**
	 * This Method is used to get external balance details
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyId
	 * @param accountId
	 * @param date
	 * @param format
	 * @return externalBalance
	 * @throws SwtException
	 */

	 public Double getExternalBalance(String entityId, String hostId, String currencyId, 
            String accountId, String date, String format) throws SwtException {
        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(
                 "{call PK_MONITORS.SP_GET_SWEEP_EXTERNAL_BALANCE(?,?,?,?,?,?,?)}")) {

            String systemDate = SwtUtil.getSystemDateString();
            cstmt.setString(1, hostId);
            cstmt.setString(2, entityId);
            cstmt.setString(3, currencyId);
            cstmt.setString(4, accountId);
            cstmt.setDate(5, string2Date(date.trim(), format));
            cstmt.setDate(6, string2Date(systemDate.trim(), format));
            cstmt.registerOutParameter(7, Types.DOUBLE);

            cstmt.execute();
            return cstmt.getDouble(7);
        } catch (Exception e) {
            log.error("Error in getExternalBalance", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getExternalBalance", this.getClass());
        }
    }

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepDetailDAO#getAuthFlag(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public String getAuthFlag(String entityId, String hostId, String formatId) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            List<String> authFlags = session.createQuery(
                "select m.authorizeFlag from MessageFormats m where m.id.entityId = :entityId " +
                "and m.id.hostId = :hostId and m.id.formatId in (:formatId)", String.class)
                .setParameter("entityId", entityId)
                .setParameter("hostId", hostId)
                .setParameter("formatId", formatId)
                .getResultList();

            if (authFlags.isEmpty()) {
                return SwtConstants.NO;
            }

            return authFlags.stream()
                .anyMatch(flag -> SwtConstants.YES.equalsIgnoreCase(flag)) 
                ? SwtConstants.YES : SwtConstants.NO;
        } catch (Exception e) {
            log.error("Error in getAuthFlag", e);
            throw new SwtException(e.getMessage());
        }
    }


	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepDetailDAO#getSweepLimit(java.lang.String,
	 *      java.lang.String)
	 */
	
	 public Double getSweepLimit(String currCode, String roleId) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "select s.sweepLimit from SweepLimit s where s.id.roleId = :roleId and s.id.currencyCode = :currCode", 
                Double.class)
                .setParameter("roleId", roleId)
                .setParameter("currCode", currCode)
                .uniqueResult();
        } catch (Exception e) {
            log.error("Error in getSweepLimit", e);
            throw new SwtException(e.getMessage());
        }
    }

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepDetailDAO#getSweepDetails(java.lang.String,
	 *      java.lang.String, java.lang.Long)
	 */
	public List<Sweep> getSweepDetails(String entityId, String hostId, Long sweepId) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "from Sweep s where s.id.hostId = :hostId and s.id.sweepId = :sweepId", Sweep.class)
                .setParameter("hostId", hostId)
                .setParameter("sweepId", sweepId)
                .getResultList();
        } catch (Exception e) {
            log.error("Error in getSweepDetails", e);
            throw new SwtException(e.getMessage());
        }
    }

	public List<Sweep> getSweepDetailsArchive(String entityId, String hostId, Long sweepId, String archiveId) throws SwtException {
        if (SwtUtil.isEmptyOrNull(archiveId)) {
            return getSweepDetails(entityId, hostId, sweepId);
        }

        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement stmt = conn.prepareStatement(
                 "select * from p_sweep@" + archiveId + " where sweep_id = ? and host_id = ?")) {
            
            stmt.setLong(1, sweepId);
            stmt.setString(2, hostId);
            List<Sweep> sweepList = new ArrayList<>();
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Sweep sweep = mapResultSetToSweep(rs);
                    populateAccountDetails(sweep, session);
                    sweepList.add(sweep);
                }
            }
            return sweepList;
        } catch (Exception e) {
            log.error("Error in getSweepDetailsArchive", e);
            throw new SwtException(e.getMessage());
        }
    }
	private Sweep mapResultSetToSweep(ResultSet rs) throws SQLException {
        Sweep sweep = new Sweep();
        
        // Set primary key fields
        sweep.getId().setHostId(rs.getString("HOST_ID"));
        sweep.getId().setSweepId(rs.getLong("SWEEP_ID"));
        
        // Set basic fields
        sweep.setSweepGroupId(rs.getString("SWEEP_GROUP_ID"));
        sweep.setCurrencyCode(rs.getString("CURRENCY_CODE"));
        sweep.setMovementIdCr(rs.getString("MOVEMENT_ID_CR"));
        sweep.setMovementIdDr(rs.getString("MOVEMENT_ID_DR"));
        
        // Set amount fields with null checks
        sweep.setOriginalSweepAmt(rs.getObject("ORIGINAL_SWEEP_AMT") != null ? 
            rs.getDouble("ORIGINAL_SWEEP_AMT") : null);
        sweep.setSubmitSweepAmt(rs.getObject("SUBMIT_SWEEP_AMT") != null ? 
            rs.getDouble("SUBMIT_SWEEP_AMT") : null);
        sweep.setAuthorizeSweepAmt(rs.getObject("AUTHORIZE_SWEEP_AMOUNT") != null ? 
            rs.getDouble("AUTHORIZE_SWEEP_AMOUNT") : null);
        
        // Set status and type fields
        sweep.setSweepType(rs.getString("SWEEP_TYPE"));
        sweep.setSweepStatus(rs.getString("SWEEP_STATUS"));
        
        // Set date fields
        sweep.setInputDateTime(rs.getTimestamp("INPUT_DATE_TIME"));
        sweep.setAuthDateTime(rs.getTimestamp("AUTHORIZED_DATE_TIME"));
        sweep.setSubmitDateTime(rs.getTimestamp("SUBMIT_DATE_TIME"));
        sweep.setCancelDateTime(rs.getTimestamp("CANCEL_DATE_TIME"));
        sweep.setValueDate(rs.getDate("VALUE_DATE"));
        sweep.setUpdateDate(rs.getTimestamp("UPDATE_DATE"));
        
        // Set user fields
        sweep.setInputUser(rs.getString("INPUT_USER"));
        sweep.setSubmitUser(rs.getString("SUBMIT_USER"));
        sweep.setAuthorizedUser(rs.getString("AUTHORIZED_USER"));
        sweep.setCancelUser(rs.getString("CANCEL_USER"));
        sweep.setUpdateUser(rs.getString("UPDATE_USER"));
        
        // Set account fields with null checks
        sweep.setAccountIdCr(rs.getString("ACCOUNT_ID_CR"));
        sweep.setAccountIdDr(rs.getString("ACCOUNT_ID_DR"));
        sweep.setAlignAccountId(rs.getString("ALIGN_ACCOUNT_ID"));
        
        // Set entity fields with null checks
        sweep.setEntityIdCr(rs.getString("ENTITY_ID_CR"));
        sweep.setEntityIdDr(rs.getString("ENTITY_ID_DR"));
        
        // Set settlement method fields
        sweep.setSettleMethodCR(rs.getString("SETTL_METHOD_CR"));
        sweep.setSettleMethodDR(rs.getString("SETTL_METHOD_DR"));
        
        // Set book code fields
        sweep.setBookCodeCR(rs.getString("BOOKCODE_CR"));
        sweep.setBookCodeDR(rs.getString("BOOKCODE_DR"));
        
        // Set additional fields
        sweep.setAdditionalReference(rs.getString("ADDITIONAL_REFERENCE"));
        sweep.setSweepFromBalanceTypeCr(rs.getString("SWEEP_FROM_BALANCE_TYPE_CR"));
        sweep.setSweepFromBalanceTypeDr(rs.getString("SWEEP_FROM_BALANCE_TYPE_DR"));
        sweep.setTargetBalanceTypId(rs.getString("TARGET_BALANCE_TYP_ID"));
        
        return sweep;
    }

    private void populateAccountDetails(Sweep sweep, Session session) {
        String query = "from AcctMaintenance acc where acc.id.hostId = :hostId " +
                      "and acc.id.entityId = :entityId and acc.id.accountId = :accountId";
        
        AcctMaintenance accountCr = session.createQuery(query, AcctMaintenance.class)
            .setParameter("hostId", sweep.getId().getHostId())
            .setParameter("entityId", sweep.getEntityIdCr())
            .setParameter("accountId", sweep.getAccountIdCr())
            .uniqueResult();
        
        AcctMaintenance accountDr = session.createQuery(query, AcctMaintenance.class)
            .setParameter("hostId", sweep.getId().getHostId())
            .setParameter("entityId", sweep.getEntityIdDr())
            .setParameter("accountId", sweep.getAccountIdDr())
            .uniqueResult();
        
        sweep.setAccountCr(accountCr);
        sweep.setAccountDr(accountDr);
    }

	/**
	 * Generate Sweep for Sweeping through Sweeppriorcut-off/Manual sweeping
	 * 
	 * @param sweepAct1
	 * @param sweepAct2
	 * @param sweepAmt
	 * @param hostId
	 * @param userId
	 * @param format
	 * @return returnParams
	 * @throws SwtException
	 */
	public List generateSweep(SweepDetail sweepAct1, SweepDetail sweepAct2,
			SweepAmount sweepAmt, String hostId, String userId, String format)
			throws SwtException {
		log.debug("entering generateSweep method");
		// Variable Declaration for returnParams
		List returnParams = new ArrayList();
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for acctCr
		String acctCr = "";
		// Variable Declaration for acctDr
		String acctDr = "";
		// Variable Declaration for entityCr
		String entityCr = "";
		// Variable Declaration for entityDr
		String entityDr = "";
		// Variable Declaration for authFlagCr
		String authFlagCr = "";
		// Variable Declaration for authFlagDr
		String authFlagDr = "";
		// Variable Declaration for alignedAcct
		String alignedAcct = "";
		// Variable Declaration for valuedate
		Date valuedate = null;
		// Variable Declaration for sweepAmount
		Double sweepAmount = null;
		// Variable Declaration for acctLevelFlag
		String acctLevelFlag = null;
		
		String settMethodCR = null;
		String settMethodDR = null;
		String bookCodeCr = null;
		String bookCodeDr = null;
		
		// Variable Declaration for session
		Session session = null;
		// Variable Declaration for conn
		Connection conn = null;
		// Variable Declaration for cstmt
		CallableStatement cstmt = null;
		// Variable Declaration for successFlag
		int successFlag = 1;
		// Variable Declaration for sweepId
		int sweepId = 0;

		try {
			// Get the entitiy Id
			entityId = sweepAct1.getEntityId();
			// Get the orginal sweep amount
			sweepAmount = sweepAmt.getOrigSweepAmt();

			if (sweepAct1.getAlignedSign().equals(SwtConstants.ALLIGNED_CREDIT)) {
				acctCr = sweepAct1.getAccountId();
				acctDr = sweepAct2.getAccountId();
				entityCr = sweepAct1.getEntityId();
				entityDr = sweepAct2.getEntityId();
				authFlagCr = sweepAct1.getAuthQueue();
				authFlagDr = sweepAct2.getAuthQueue();
				bookCodeCr = sweepAct1.getBookcode();
				bookCodeDr = sweepAct2.getBookcode();
				settMethodCR = sweepAct1.getDefaultSettleMethod();
				settMethodDR = sweepAct2.getDefaultSettleMethod();
			} else {
				acctCr = sweepAct2.getAccountId();
				acctDr = sweepAct1.getAccountId();
				entityCr = sweepAct2.getEntityId();
				entityDr = sweepAct1.getEntityId();
				authFlagCr = sweepAct2.getAuthQueue();
				authFlagDr = sweepAct1.getAuthQueue();
				bookCodeCr = sweepAct2.getBookcode();
				bookCodeDr = sweepAct1.getBookcode();
				settMethodCR = sweepAct2.getDefaultSettleMethod();
				settMethodDR = sweepAct1.getDefaultSettleMethod();
			}
			// Get the account Id based on the aligned to target
			if ((sweepAct1.getAlignToTarget() != null)
					&& (sweepAct1.getAlignToTarget().equals(SwtConstants.YES))) {
				alignedAcct = sweepAct1.getAccountId();
			} else {
				alignedAcct = sweepAct2.getAccountId();
			}

			acctLevelFlag = "PGT";
			// Create session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Open connection
			conn = SwtUtil.connection(session);
			// Get th date
			valuedate = string2Date(sweepAmt.getValueDateAsString().trim(),
					format);
			/*
			 * Start:Code Modified for Mantis 1488 By Chinniah on
			 * 2-JUL-2011:INTERNAL - SWEEPING PROCESS: remove unnecessary
			 * variables etc
			 */
			// Create statement
			cstmt = conn
					.prepareCall("{call SWEEPING_PROCESS.SWEEP_MOVEMENT_GENERATION(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}");
			// Setting values for cursor
			cstmt.setString(1, hostId);
//			cstmt.setString(2, entityId);
			cstmt.setString(2, acctCr);
			cstmt.setString(3, acctDr);
			cstmt.setDouble(4, sweepAmount.doubleValue());
			cstmt.setString(5, "PGT");
			cstmt.setString(6, null);
			cstmt.setString(7, null);
			cstmt.setString(8, null);
			cstmt.setString(9, null);
			cstmt.setString(10, "S");
			cstmt.setString(11, "S");
			cstmt.setString(12, " ");
			cstmt.setString(13, userId);
			cstmt.setString(14, alignedAcct);
			cstmt.setString(15, entityCr);
			cstmt.setString(16, entityDr);
			cstmt.setString(17, "M");
			cstmt.setDate(18, valuedate);
			cstmt.registerOutParameter(19, Types.INTEGER);
			cstmt.registerOutParameter(20, Types.INTEGER);
			cstmt.setString(21, settMethodCR);
			cstmt.setString(22, settMethodDR);
			cstmt.setString(23, bookCodeCr);
			cstmt.setString(24, bookCodeDr);
			
			cstmt.setNull(25, Types.NUMERIC);
			cstmt.setNull(26, Types.VARCHAR);
			cstmt.setNull(27, Types.NUMERIC);

			
			
			cstmt.setString(28, sweepAmt.getAdditionalReference());
			cstmt.setNull(29, Types.VARCHAR);
			cstmt.setNull(30, Types.VARCHAR);
			// Execute the statement
			cstmt.execute();
			// Get the values from the cursor
			successFlag = cstmt.getInt(19);
			sweepId = cstmt.getInt(20);
			/*
			 * End:Code Modified for Mantis 1488 By Chinniah on
			 * 2-JUL-2011:INTERNAL - SWEEPING PROCESS: remove unnecessary
			 * variables etc
			 */
			// Add the values to List
			returnParams.add(0, Integer.toString(successFlag));
			returnParams.add(1, Integer.toString(sweepId));
		} catch (SQLException sqlException) {
			log.error("Problem in closing resultset -"
					+ sqlException.getMessage());

			throw new SwtException(sqlException.getMessage());
		} catch (HibernateException hibernateException) {
			log.error("Problem in closing session; -"
					+ hibernateException.getMessage());

			throw new SwtException(hibernateException.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException)exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException)exceptions[1]).getMessage());

			if (thrownException!=null)
			    throw thrownException;
			
			
		
				hostId = null;
				entityId = null;
				acctCr = null;
				acctDr = null;
				userId = null;
				alignedAcct = null;
				entityCr = null;
				entityDr = null;
				valuedate = null;

			// END : code modified by KaisBS for the mantis 1745 to protect the
			// closer instructions and throw exception problem
		}

		return returnParams;
	}

    public void saveNotesDetails(SweepNote sweepNote) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.save(sweepNote);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Error in saveNotesDetails", e);
            throw new SwtException(e.getMessage());
        }
    }

	/**
	 * This function returns status for ManualSweeping process while getting the
	 * data from database.
	 * 
	 * @param sweep
	 * @param systemFormats
	 * @param sweepAmount
	 * @param authFlagCr
	 * @Param authFlagDr
	 * @return status
	 */

	public int processSweep(Sweep sweep, SystemFormats systemFormats,
			Double sweepAmount, String authFlagCr, String authFlagDr)
			throws SwtException {

		// varaible declare for session
		Session session = null;
		// varaiable decalare for callableStatment
		CallableStatement cstmtSweep = null;
		try {
			log.debug(this.getClass().getName() + " - [processSweep] - Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();

			/*
			 * Start : Code modified by sunil for Mantis 1438 on 10-01-2012-
			 * removing the Directory path are never used
			 */

			cstmtSweep = SwtUtil.connection(session).prepareCall(
					"{call MANUAL_SWEEPING(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, ?)}");
			// Setting hostid value
			cstmtSweep.setString(1, sweep.getId().getHostId());
//			// Setting Entity Id value
//			cstmtSweep.setString(2, sweep.getId().getEntityId());
			// Setting the AccountId debit
			cstmtSweep.setString(2, sweep.getAccountIdDr());
			// Setting the AccountIdCredit
			cstmtSweep.setString(3, sweep.getAccountIdCr());
			// setting the Amount value
			cstmtSweep.setDouble(4, sweepAmount.doubleValue());
			// setting the request user
			cstmtSweep.setString(5, sweep.getRequestUser());
			// setting the sweepid value
			cstmtSweep.setLong(6, sweep.getId().getSweepId().longValue());
			// setting the QueueName
			cstmtSweep.setString(7, sweep.getQueueName());
			// setting the role
			cstmtSweep.setString(8, sweep.getRequestRole());
			// setting the Accountid value
			cstmtSweep.setString(9, sweep.getAlignAccountId());
			// Setting the Entityid Credit value
			cstmtSweep.setString(10, sweep.getEntityIdCr());
			// setting the Entityid debit value
			cstmtSweep.setString(11, sweep.getEntityIdDr());
			// setting the Settlement method credit value
			cstmtSweep.setString(12,  sweep.getSettleMethodCR());
			// setting the Settlement method debit value
			cstmtSweep.setString(13, sweep.getSettleMethodDR());
			// setting the Book Code credit value
			cstmtSweep.setString(14, sweep.getBookCodeCR());
			// setting the Book Code debit value
			cstmtSweep.setString(15, sweep.getBookCodeDR());
			// setting the additional References value
			cstmtSweep.setString(16, sweep.getAdditionalReference());
			// Setting sweep from balance credit
			cstmtSweep.setString(17, sweep.getSweepFromBalanceTypeCr());
			//Setting sweep from balance debit
			cstmtSweep.setString(18, sweep.getSweepFromBalanceTypeDr());
			//Setting sweep target balance type id
			cstmtSweep.setString(19, sweep.getTargetBalanceTypId());
			// Retriving using above given values database
			// Getting output from the cursor
			cstmtSweep.registerOutParameter(20, Types.INTEGER);
			// Excecute cursor
			cstmtSweep.execute();
			return cstmtSweep.getInt(20);
			/* End : Code modified by sunil for Mantis 1438 on 10-01-2012 -
			  removing the Directory path are never used
			 */

		} catch (SQLException sqlException) {
			log.error(this.getClass().getName()
					+ " - [proceeSweep] -SQLException "
					+ sqlException.getMessage());

			throw new SwtException(sqlException.getMessage());
		} catch (HibernateException hibernateException) {
			log.error(this.getClass().getName()
					+ " - [processSweep] -HibernateException "
					+ hibernateException.getMessage());

			throw new SwtException(hibernateException.getMessage());
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(null, cstmtSweep, null, session);

			if (exceptions[0] != null)
			    thrownException = new SwtException(((SQLException)exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] !=null) 
			    thrownException = new SwtException(((HibernateException)exceptions[1]).getMessage());

			if (thrownException!=null)
			    throw thrownException;

			log.debug(this.getClass().getName() + " - [processSweep] - Exit");

		}

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepDetailDAO#getOffsetTime(java.lang.String,
	 *      java.lang.String)
	 */
	public String getOffsetTime(String entity, String hostId) throws SwtException {
        try (Session session = getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             PreparedStatement stmt = conn.prepareStatement(
                 "select tz_offset(entity_tzname) from s_entity where entity_id = ? and host_id = ?")) {
            
            stmt.setString(1, entity);
            stmt.setString(2, hostId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next() ? rs.getString(1) : "";
            }
        } catch (Exception e) {
            log.error("Error in getOffsetTime", e);
            throw SwtErrorHandler.getInstance().handleException(e, "getOffsetTime", this.getClass());
        }
    }

    public Collection getsweepMessageDaoColl(String sweepId) throws SwtException {
        try (Session session = getSessionFactory().openSession()) {
            return session.createQuery(
                "from SweepMsgFormat s where s.id.hostId = :hostId and s.id.sweepId = :sweepId")
                .setParameter("hostId", CacheManager.getInstance().getHostId())
                .setParameter("sweepId", sweepId)
                .getResultList();
        } catch (Exception e) {
            log.error("Error in getSweepMessageDaoColl", e);
            throw new SwtException(e.getMessage());
        }
    }

	/**
	 * This method is used to update the sweep details.
	 * 
	 * @param sweep
	 * @throws SwtException
	 */
	 public void updateSweepDetails(Sweep sweep) throws SwtException {
        log.debug("Entering updateSweepDetails method");
        try (Session session = getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.update(sweep);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error("Error in updateSweepDetails", e);
            throw SwtErrorHandler.getInstance().handleException(e, "updateSweepDetails", this.getClass());
        }
    }

}
