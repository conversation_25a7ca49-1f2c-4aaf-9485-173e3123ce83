/**
 * @(#)AcctBreakdownMonitorDAOHibernate.java 1.0 / Mar 7, 2012
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.AcctBreakdownMonitorDAO;
import org.swallow.work.model.AcctBreakdownModel;
import org.swallow.work.model.AcctBreakdownMonitor;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessResourceFailureException;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.TypedQuery;

/**
 * AcctBreakdownMonitorDAOHibernate.java
 * 
 * <pre>
 * This DAO class performs Account Breakdown Monitor related database operation.
 * It extends CustomHibernateDaoSupport, so that it will get the reference of
 * HibernateSession to access database. It performs following tasks,
 * 
 * - Get Account list based on selected entity, currency and account class
 * - Get account breakdown details for grid
 * - Update account details
 * </pre>
 * 
 * <AUTHOR> R / Mar 7, 2012
 * @version SmartPredict-1054
 */
@Repository("acctBreakdownMonitorDAO")
@Transactional
public class AcctBreakdownMonitorDAOHibernate extends CustomHibernateDaoSupport
        implements AcctBreakdownMonitorDAO {

    public AcctBreakdownMonitorDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    private static final Log log = LogFactory.getLog(AcctBreakdownMonitorDAOHibernate.class);

    @SuppressWarnings("unchecked")
    public Collection<AcctMaintenance> getAcctList(AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
        try {
            log.debug(this.getClass().getName() + " - [getAcctList] - Enter");

            StringBuilder queryBuilder = new StringBuilder(
                "FROM AcctMaintenance acct WHERE " +
                "acct.id.hostId = :hostId AND acct.id.entityId = :entityId " +
                "AND acct.id.accountId != '*' AND acct.currcode = :currcode " +
                "AND acct.acctstatusflg IN (:statusFlags)");

            if (!acctBreakdownMonitor.getAcctClass().equals("All")) {
                queryBuilder.append(" AND acct.acctClass = :acctClass");
            }
            queryBuilder.append(" ORDER BY acct.id.accountId");

            try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
                TypedQuery<AcctMaintenance> query = session.createQuery(queryBuilder.toString(), AcctMaintenance.class);
                query.setParameter("hostId", acctBreakdownMonitor.getHostId());
                query.setParameter("entityId", acctBreakdownMonitor.getEntityId());
                query.setParameter("currcode", acctBreakdownMonitor.getCurrencyCode());
                query.setParameter("statusFlags", java.util.Arrays.asList(
                    SwtConstants.ACCOUNT_STATUS_FLAG_OPEN,
                    SwtConstants.ACCOUNT_STATUS_FLAG_BLOCKED
                ));

                if (!acctBreakdownMonitor.getAcctClass().equals("All")) {
                    query.setParameter("acctClass", acctBreakdownMonitor.getAcctClass());
                }

                return query.getResultList();
            }
        } catch (Exception ex) {
            log.error(this.getClass().getName() + " - [getAcctList] - Exception: " + ex.getMessage());
            throw SwtErrorHandler.getInstance().handleException(ex, "getAcctList", this.getClass());
        } finally {
            log.debug(this.getClass().getName() + " - [getAcctList] - Exit");
        }
    }

    public void getAllBalancesUsingStoredProc(AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
        Collection<AcctBreakdownModel> colAcctBreakdown = new ArrayList<>();
        StringBuffer sbTabFlag = null;

        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(
                "{call PKG_ACCOUNT_MONITOR.SP_ACCOUNT_BD_MONITOR_JOB(?,?,?,?,?,?,?,?,?,?,?,?,?)}")) {

            log.debug(this.getClass().getName() + " - [getAllBalancesUsingStoredProc] - Enter");

            setStoredProcParameters(cstmt, acctBreakdownMonitor);

            cstmt.execute();

            try (ResultSet rsAcctBreakdown = (ResultSet) cstmt.getObject(11);
                 ResultSet rsTabFlag = (ResultSet) cstmt.getObject(12);
                 ResultSet rsBalanceTotal = (ResultSet) cstmt.getObject(13)) {

                processAccountBreakdownResults(rsAcctBreakdown, colAcctBreakdown, acctBreakdownMonitor);
                processBalanceTotals(rsBalanceTotal, acctBreakdownMonitor);
                sbTabFlag = processTabFlags(rsTabFlag);
            }

            acctBreakdownMonitor.setTabFlag(sbTabFlag != null ? sbTabFlag.toString() : "");
            acctBreakdownMonitor.setAcctBreakdownList(colAcctBreakdown);

        } catch (Exception ex) {
            handleStoredProcException(ex);
        } finally {
            log.debug(this.getClass().getName() + " - [getAllBalancesUsingStoredProc] - Exit");
        }
    }

    private void setStoredProcParameters(CallableStatement cstmt, AcctBreakdownMonitor monitor) throws SQLException {
        int paramIndex = 1;
        cstmt.setString(paramIndex++, monitor.getHostId());
        cstmt.setString(paramIndex++, monitor.getEntityId());
        cstmt.setString(paramIndex++, monitor.getCurrencyCode());
        cstmt.setString(paramIndex++, monitor.getBalanceType());
        cstmt.setString(paramIndex++, monitor.getAcctId());
        cstmt.setDate(paramIndex++, SwtUtil.truncateDateTime(monitor.getValueDate()));
        cstmt.setString(paramIndex++, monitor.getApplyCurrencyThreshold());
        cstmt.setString(paramIndex++, monitor.getHideZeroBalances());
        cstmt.setString(paramIndex++, monitor.getAcctClass());
        cstmt.setString(paramIndex++, monitor.getRoleId());

        cstmt.registerOutParameter(paramIndex++, oracle.jdbc.OracleTypes.CURSOR);
        cstmt.registerOutParameter(paramIndex++, oracle.jdbc.OracleTypes.CURSOR);
        cstmt.registerOutParameter(paramIndex, oracle.jdbc.OracleTypes.CURSOR);
    }

    private void processAccountBreakdownResults(ResultSet rs, Collection<AcctBreakdownModel> collection,
            AcctBreakdownMonitor monitor) throws SQLException,SwtException {
        if (rs != null) {
            while (rs.next()) {
                AcctBreakdownModel model = new AcctBreakdownModel();
                int paramIndex = 1;

                model.setScenarioHighlighted(rs.getString("SCENARIO_HIGHLIGHTING"));
                model.setCurrencyCode(rs.getString(paramIndex++));
                model.setAcctId(rs.getString(paramIndex++));
                model.setAcctName(rs.getString(paramIndex++));

                processBalances(rs, model, paramIndex);
                processSumFlags(rs, model, paramIndex);

                String acctClass = rs.getString(paramIndex++);
                model.setAccountStatus(rs.getString(paramIndex++));
                model.setIsLoroOrCurrAcct((acctClass.equalsIgnoreCase("L") || acctClass.equalsIgnoreCase("C")) ? "Y" : "N");

                setModelProperties(model, monitor, rs);
                collection.add(model);
            }
        }
    }

    private void processBalances(ResultSet rs, AcctBreakdownModel model, int paramIndex) throws SQLException, SwtException {
        double predictedBalance = rs.getDouble(paramIndex++);
        model.setPredBalance(SwtUtil.formatCurrency(model.getCurrencyCode(), predictedBalance));
        model.setPredBalanceSign(predictedBalance >= 0 ? SwtConstants.STR_TRUE : SwtConstants.STR_FALSE);

        double startOfDayBalance = rs.getDouble(paramIndex++);
        model.setStartingBalance(SwtUtil.formatCurrency(model.getCurrencyCode(), startOfDayBalance));
        model.setStartingBalanceSign(startOfDayBalance >= 0 ? "true" : "false");

        double unexpectedBalance = rs.getDouble(paramIndex++);
        model.setUnexpectedBalance(SwtUtil.formatCurrency(model.getCurrencyCode(), unexpectedBalance));
        model.setUnexpectedBalanceSign(unexpectedBalance >= 0 ? "true" : "false");
    }

    private void processSumFlags(ResultSet rs, AcctBreakdownModel model, int paramIndex) throws SQLException {
        String sumFlag = rs.getString(paramIndex);
        model.setSummable(sumFlag.length() == 2 ? sumFlag.substring(1) : SwtConstants.YES);
        model.setSum(sumFlag.substring(0, 1));
        model.setLoroToPredictedFlag(rs.getString(paramIndex));
        model.setIconIndicatorFlag(rs.getString(paramIndex++));
    }

    private void setModelProperties(AcctBreakdownModel model, AcctBreakdownMonitor monitor, ResultSet rs) throws SQLException {
        model.setEntityId(monitor.getEntityId());
        model.setValueDate(monitor.getValueDateAsString());
        model.setBalanceType(monitor.getBalanceType());
        model.setIncludeLoroInPredictedIndicator(
            "N".equalsIgnoreCase(rs.getString("predict_loro_flag")) ? "" : rs.getString("predict_loro_flag"));
        model.setIncludeLoroInPredictedColor(rs.getString("predict_loro_color"));
    }

    private void processBalanceTotals(ResultSet rs, AcctBreakdownMonitor monitor) throws SQLException,  SwtException {
        if (rs != null && rs.next()) {
            double predictedBalanceTotal = rs.getDouble(1);
            double startOfDayBalanceTotal = rs.getDouble(2);
            double unexpectedBalanceTotal = rs.getDouble(3);

            setTotalBalances(monitor, predictedBalanceTotal, startOfDayBalanceTotal, unexpectedBalanceTotal);
        }
    }

    private void setTotalBalances(AcctBreakdownMonitor monitor, double predicted, double startOfDay, double unexpected) throws SwtException {
        monitor.setPredBalanceTotal(SwtUtil.formatCurrency(monitor.getCurrencyCode(), predicted));
        monitor.setPredBalanceTotalSign(predicted >= 0 ? SwtConstants.STR_TRUE : SwtConstants.STR_FALSE);

        monitor.setStartingBalanceTotal(SwtUtil.formatCurrency(monitor.getCurrencyCode(), startOfDay));
        monitor.setStartingBalanceTotalSign(startOfDay >= 0 ? SwtConstants.STR_TRUE : SwtConstants.STR_FALSE);

        monitor.setUnexpectedBalanceTotal(SwtUtil.formatCurrency(monitor.getCurrencyCode(), unexpected));
        monitor.setUnexpectedBalanceTotalSign(unexpected >= 0 ? SwtConstants.STR_TRUE : SwtConstants.STR_FALSE);
    }

    private StringBuffer processTabFlags(ResultSet rs) throws SQLException {
        StringBuffer sbTabFlag = new StringBuffer();
        if (rs != null) {
            while (rs.next()) {
                sbTabFlag.append(rs.getString(1));
            }
        }
        return sbTabFlag;
    }

    private void handleStoredProcException(Exception ex) throws SwtException {
        log.error(this.getClass().getName() + " - [getAllBalancesUsingStoredProc] - Exception: " + ex.getMessage());
        throw SwtErrorHandler.getInstance().handleException(ex, "getAllBalancesUsingStoredProc", this.getClass());
    }

    public void updateAccount(String hostId, String entityId, String acctId,
            String sumFlag, String loroToPredicted) throws SwtException {
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                log.debug(this.getClass().getName() + " - [updateAccount] - Enter");

                String hql = "FROM AcctMaintenance WHERE id.hostId = :hostId AND id.entityId = :entityId AND id.accountId = :acctId";
                TypedQuery<AcctMaintenance> query = session.createQuery(hql, AcctMaintenance.class);
                query.setParameter("hostId", hostId);
                query.setParameter("entityId", entityId);
                query.setParameter("acctId", acctId);

                AcctMaintenance acctMaintenance = query.getSingleResult();

                if (acctMaintenance != null && !SwtUtil.isEmptyOrNull(sumFlag)) {
                    acctMaintenance.setAcctMonitorSum(sumFlag);
                    session.update(acctMaintenance);
                }
                tx.commit();
            } catch (Exception e) {
                if (tx != null && tx.isActive()) {
                    tx.rollback();
                }
                throw e;
            }
        } catch (Exception ex) {
            log.error(this.getClass().getName() + " - [updateAccount] - Exception: " + ex.getMessage());
            throw SwtErrorHandler.getInstance().handleException(ex, "updateAccount", this.getClass());
        } finally {
            log.debug(this.getClass().getName() + " - [updateAccount] - Exit");
        }
    }
}