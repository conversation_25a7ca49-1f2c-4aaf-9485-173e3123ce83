/*
 * @(#)ScreenOptionDAO.java 1.0 06/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.work.model.ScreenOption;

/**
 * ScreenOptionDAO.java
 * 
 * Interface definitions for Screen Option DAO.
 * 
 * <AUTHOR>
 * @date Aug 06, 2010
 */
public interface ScreenOptionDAO {
	/**
	 * Method to retrieve the screen options
	 * @param screenOption 
	 * @throws SwtException
	 * Collection<ScreenOption>
	 */
	Collection<ScreenOption> getScreenOption(ScreenOption screenOption) throws SwtException;

	/**
	 * Method to save the Screen Option
	 * @param screenOption
	 * @param isUpdate
	 * @throws SwtException
	 */
	void saveScreenOption(ScreenOption screenOption,boolean isUpdate)
			throws SwtException;
	/**
	 * Method to delete the screen Option
	 * @param screenOption
	 * @throws SwtException
	 */
	public void deleteScreenOption(ScreenOption screenOption)
			throws SwtException;
	/**
	 * Method to check the screen Option
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param propertyName
	 * @throws SwtException
	 */
	public boolean checkIfScreenOptionsExists(String hostId, String userId,String screenId, String propertyName)
			throws SwtException ;
	
	/**
	 * This method is used to get property value from s_screen_option
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param propertyName
	 * @throws SwtException
	 */
	public String getPropertyValue(String hostId, String userId,String screenId, String propertyName) throws SwtException;
}
