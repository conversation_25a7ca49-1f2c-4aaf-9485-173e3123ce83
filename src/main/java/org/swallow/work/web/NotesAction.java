/*
 * @(#)NotesAction.java 1.0 27/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 *
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.SweepNote;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.NotesManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionErrors;


/**
 * <AUTHOR>
 *
 * <pre>
 * This class is used for notes actions
 * -used to add notes
 * -used to save notes
 * -used to view notes
 * -used to delete notes
 * </pre>
 */





import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/notes", "/notes.do"})
public class NotesAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/work/notesadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/work/notes");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "notes":
				return notes();
			case "add":
				return add();
			case "addMatchNotes":
				return addMatchNotes();
			case "save":
				return save();
			case "saveMatchNotes":
				return saveMatchNotes();
			case "view":
				return view();
			case "delete":
				return delete();
			case "showMatchNotes":
				return showMatchNotes();
			case "viewMatchNotes":
				return viewMatchNotes();
			case "deleteMatchNotes":
				return deleteMatchNotes();
			case "showSweepNotes":
				return showSweepNotes();
			case "addSweepNotes":
				return addSweepNotes();
			case "saveSweepNotes":
				return saveSweepNotes();
			case "viewSweepNotes":
				return viewSweepNotes();
			case "deleteSweepNotes":
				return deleteSweepNotes();
		}


		return null;
	}

	@Autowired
	private NotesManager notesManager;
	private Log log = LogFactory.getLog(NotesAction.class);

	public void setNotesManager(NotesManager notesManager) {
		this.notesManager = notesManager;
	}

	/**
	 * This method is used to get the notes for Movements
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String notes()
			throws SwtException {
		// variable to hold notesDetails
		Collection<MovementNote> notesDetails = null;
		// variable to hold archiveId
		String archiveId = null;
		// variable to hold dataSource
		SwtDataSource dataSource = null;
		// variable to hold hostId
		String hostId = null;
		// variable to hold movementIdAsString
		String movementIdAsString = null;
		// variable to hold recordFound
		String recordFound = null;
		// variable to hold movement notes
		MovementNote movementNote = null;
		// variable to hold entity id
		String entityId = null;
		// this variable declared to get hold currency Id
		String currencyId = null;
		// this variable is to hold the movement id
		Long movementId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " [notes] -Enters");
			// Instance of movementNote
			movementNote = new MovementNote();
			// Added for Archive Movement Search
			archiveId = request.getParameter("archiveId");
			// get the data source from the bean
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			// set the button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// Get the Host id form cacheManager
			hostId = CacheManager.getInstance().getHostId();

			dataSource.useArchive(archiveId);
			// Getting entityId from request
			entityId = request.getParameter("entityCode");
			if (entityId.equalsIgnoreCase("")) {
				entityId = request.getParameter("selectedEntityId");
			}
			// Getting movementId form request
			movementIdAsString = request.getParameter("movId");
			// get the currency id for the selected movement Id and check for
			// currency group access
			if (!SwtUtil.isEmptyOrNull(movementIdAsString)) {
				currencyId = notesManager.getCurrency(entityId, new Long(
						movementIdAsString), null);
				SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
						currencyId);
				request.setAttribute("currencyAccess", request
						.getAttribute("menuEntityCurrGrpAccess"));
			}
			// set the attribute for recordFound
			request.setAttribute("recordFound", request
					.getParameter("recordFound"));
			if (SwtUtil.isEmptyOrNull(movementIdAsString)) {
				movementIdAsString = request.getParameter("selectedMovId");
			}
			// get the notes details
			if (SwtUtil.isEmptyOrNull(movementIdAsString)) {
				notesDetails = (Collection) request.getSession().getAttribute(
						"sessionNotesDetails");
				if (notesDetails == null) {
					notesDetails = new ArrayList<MovementNote>();
				}
			} else {
				if (movementIdAsString != null) {
					movementIdAsString = movementIdAsString.trim();
				}
				movementId = new Long(movementIdAsString);
				// Getting the notes Manager from the bean
				if (SwtUtil.isEmptyOrNull(archiveId)) {
					// getting the notes details from notes Manager
					notesDetails = notesManager.getNoteDetails(hostId,
							movementId, new SystemInfo(), SwtUtil
									.getCurrentSystemFormats(request
											.getSession()));
				} else {
					notesDetails = notesManager.getArchiveMovementNoteDetails(
							entityId, movementId, new SystemInfo(), SwtUtil
									.getCurrentSystemFormats(request
											.getSession()), archiveId);
				}

			}

			// set the attribute for currentUserId
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			// set the attribute for currentUserId
			request.setAttribute("entityCode", entityId);
			// set the attribute for notesDetails
			request.setAttribute("notesDetails", notesDetails);
			// set the attribute for archiveId
			request.setAttribute("archiveId", archiveId);
			if ((archiveId != null) && (archiveId.length() > 0)) {
				request.setAttribute("fromArchive", "yes");
			}
			// set the attribute for movId
			request.setAttribute("movId", movementIdAsString);
			// put the Movement Summary Display Refresh Parameters In Request
			putMovementSummaryDisplayRefreshParametersInRequest(request);

		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.notes "
					  + swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception in NotesAction.notes " + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "notes", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			((SwtDataSource) SwtUtil.getBean("dataSourceDb")).clearArchive();
			// nullify
			notesDetails = null;
			archiveId = null;
			dataSource = null;
			hostId = null;
			movementIdAsString = null;
			recordFound = null;
			notesManager = null;
			movementNote = null;
			entityId = null;
			currencyId = null;
			log.debug(this.getClass().getName() + " [notes] Exit");
		}

		return getView("success");
	}


	private MovementNote notes = null;


	public MovementNote getNotes() {
		if (notes == null) {
			HttpServletRequest request = SwtUtil.getCurrentRequest();
			notes = RequestObjectMapper.getObjectFromRequest(MovementNote.class, request,"notes");
			if (notes == null) {
				notes = new MovementNote();
				// Set any default values or perform necessary initialization
			}
		}
		return notes;
	}

	public void setNotes(MovementNote notes) {
		this.notes = notes;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if (request != null) {
			request.setAttribute("notes", notes);
		}
	}


	/**
	 * This mehod is called while adding a notes
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold entity id
		String entityId = null;
		// variable to hold movement id
		String movId = null;
		// variable to hold user
		String user = null;
		// variable to hold movement notes
		MovementNote movNot = null;
		try {
			log.debug("Entering 'add' method");
			// Get the entityId from request
			entityId = request.getParameter("entityCode");
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = request.getParameter("selectedEntityId");
			}
			// Get the Movement id form request
			movId = request.getParameter("movId");
			// Get the current user id form request
			user = SwtUtil.getCurrentUserId(request.getSession());
			// instance of movement notes
			movNot = new MovementNote();
			// get the updated date as string
			movNot.getId().setUpdateDateAsString(SwtUtil.getSystemDateString());
			// set the attribute for currency access
			request.setAttribute("currencyAccess", request
					.getParameter("currencyAccess"));
			// setting the button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// set the updated user name
			movNot.setUpdateUser(user);
			// set the attribute for entityCode
			request.setAttribute("entityCode", entityId);
			// set the attribute for movement id
			request.setAttribute("movId", movId);
			// setting notes
			// If you need to set it back to the form
			setNotes(movNot);
			request.setAttribute("notes", notes);
			// set the attribute for unlock
			request.setAttribute("unLock", SwtUtil.isEmptyOrNull(request
					.getParameter("unLock")) ? request.getAttribute("unLock")
					: request.getParameter("unLock"));
			// put Movement Summary Display Refresh Parameters In Request
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug("Exiting 'add' mehtod");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.add " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.add " + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "add", NotesAction.class), request, "");
			return getView("fail");
		} finally {
			entityId = null;
			movId = null;
			user = null;
			movNot = null;
		}

	}

	private MatchNote notesMatch = null;

	public MatchNote getNotesMatch() {
		if (notesMatch == null) {
			HttpServletRequest request = SwtUtil.getCurrentRequest();
			notesMatch = RequestObjectMapper.getObjectFromRequest(MatchNote.class, request ,"notesMatch");
			if (notesMatch == null) {
				notesMatch = new MatchNote();
				// Set any default values or perform necessary initialization
			}
		}
		return notesMatch;
	}

	public void setNotesMatch(MatchNote notesMatch) {
		this.notesMatch = notesMatch;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if (request != null) {
			request.setAttribute("notesMatch", notesMatch);
		}
	}


	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String addMatchNotes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug("Entering addMatchNotes method");

		try {

			String entityId = request.getParameter("entityCode");

			if ("".equalsIgnoreCase(entityId)) {
				entityId = request.getParameter("selectedEntityId");
			}

			log.debug("entityId " + entityId);

			String user = SwtUtil.getCurrentUserId(request.getSession());
			log.debug("The user currently logged on is" + user);

			String matchIdAsString = request.getParameter("matchId");
			MatchNote matchNote = new MatchNote();
			String todayDateAsString = SwtUtil.getSystemDateString();

			matchNote.getId().setUpdateDateAsString(todayDateAsString);

			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			matchNote.setUpdateUser(user);

			request.setAttribute("entityCode", entityId);

			request.setAttribute("method", "showMatchNotes");
			request.setAttribute("matchId", matchIdAsString);
//			dyForm.set("notesMatch", matchNote);
			setNotesMatch(matchNote);
			request.setAttribute("notesMatch", matchNote);
			request.setAttribute(matchIdAsString, todayDateAsString);
			log.debug("Exiting addMatchNotes mehtod");
			putMovementSummaryDisplayRefreshParametersInRequest(request);

			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.addMatchNotes "
					  + swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.addMatchNotes "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addMatchNotes", NotesAction.class), request, "");

			return getView("fail");
		}
	} // End of addMatchNotes() method


	private String selectedEntityId = null;

	public String getSelectedEntityId() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		selectedEntityId = request.getParameter("selectedEntityId");
		if (selectedEntityId == null) {
			selectedEntityId = "";
			// Set any default value or perform necessary initialization
		}
		return selectedEntityId;
	}

	public void setSelectedEntityId(String selectedEntityId) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		this.selectedEntityId = selectedEntityId;
		request.setAttribute("selectedEntityId", selectedEntityId);
	}

	/**
	 * This save method is used to save the notes added to the subscreen of
	 * notes
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String save()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold movement notes
		MovementNote mvmtNote = null;
		// variable to hold entity id
		String entityId = null;
		// variable to hold host id
		String hostId = null;
		// variable to hold movementIdAsString
		String movementIdAsString = null;
		// variable to hold notes details
		Collection<MovementNote> sessionNotesDetails = null;
		// variable to append time in string format
		StringBuffer movTime = null;
		// variable to hold hours
		int movHours;
		// variable to hold minutes
		int movMins;
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");
			// get the movement notes
			mvmtNote = (MovementNote) getNotes();
			// get the entity id form the form
			entityId = getSelectedEntityId();
			// get the host id from the cacheManger
			hostId = CacheManager.getInstance().getHostId();
			// get the system date
			// set the host id
			mvmtNote.getId().setHostId(hostId);
			// set the updated user name
			mvmtNote.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));
			// mvmtNote.getId().setUpdateDate(new Date());
			// set the updated date
			mvmtNote.getId().setUpdateDate(SwtUtil.getSystemDatewithTime());
			// get the selected movement id parameter form request
			movementIdAsString = request.getParameter("selectedMovId");
			// Condition for new Movement
			if (SwtUtil.isEmptyOrNull(movementIdAsString)) {
				// get the hours from the movement note
				movHours = mvmtNote.getId().getUpdateDate().getHours();
				// get the minutes from the movement note
				movMins = mvmtNote.getId().getUpdateDate().getMinutes();
				// Start:code modified by Prasenjit Maji for Mantis 1874
				// on 26/09/12: Pre advice Input:Currency drop down should load
				// currencies for default entity if cancel button is pressed
				// if single digit hours & mins,append '0' to front to get 2
				// digit format
				movTime = new StringBuffer();
				// if single digit hours append '0' to front to get 2
				// digit format
				movTime.append((movHours < 10) ? "0" : "");
				movTime.append(movHours);
				movTime.append(":");
				// if single digit minutes append '0' to front to get 2
				// digit format
				movTime.append((movMins < 10) ? "0" : "");
				movTime.append(movMins);
				// End:code modified by Prasenjit Maji for Mantis 1874
				// on 26/09/12: Pre advice Input:Currency drop down should load
				// currencies for default entity if cancel button is pressed
				// set date as string
				mvmtNote.getId().setUpdateDateAsString(
						SwtUtil.formatDate(mvmtNote.getId().getUpdateDate(),
								SwtUtil.getCurrentSystemFormats(
												request.getSession())
										.getDateFormatValue()));
				// set updated time
				mvmtNote.getId().setUpdateTimeAsString(movTime.toString());

				// set the compressed note Text
				mvmtNote.setCompressedNoteText(mvmtNote.getNoteText());

				// get the collection form session notes details
				sessionNotesDetails = (Collection) request.getSession()
						.getAttribute("sessionNotesDetails");
				if (sessionNotesDetails == null) {
					sessionNotesDetails = new ArrayList<MovementNote>();
				}
				// add the movement notes
				sessionNotesDetails.add(mvmtNote);
				// set the attribute for sessionNotesDetails
				request.getSession().setAttribute("sessionNotesDetails",
						sessionNotesDetails);
			}
			// for existing movement
			else {
				// set the Movement id
				mvmtNote.getId().setMovementId(new Long(movementIdAsString));
				if(SwtUtil.isEmptyOrNull(entityId) || "all".equalsIgnoreCase(entityId)){
					MovementManager movementManager = (MovementManager) SwtUtil
							.getBean("movementManager");
					Long movId = new Long(movementIdAsString);
					Movement movement = movementManager.getMovementDetails(hostId,
							movId);
					entityId = movement.getId().getEntityId();
				}
				// Set the entity id
				mvmtNote.getId().setEntityId(entityId);

				notesManager.saveNotesDetails(mvmtNote);
			}
			request.setAttribute("currencyAccess", request
					.getParameter("currencyAccess"));
			// Set attribute for entity code
			request.setAttribute("entityCode", entityId);
			// Set attribute for movId
			request.setAttribute("movId", movementIdAsString);
			// Set attribute for parentFormRefresh
			request.setAttribute("parentFormRefresh", "yes");
			// Set attribute for unLock
			request.setAttribute("unLock", request.getParameter("unLock"));
			// put Movement Summary Display Refresh Parameters In Request
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException in NotesAction.save "
						   + swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception e) {
			log.error("Exception in NotesAction.save " + e.getMessage());

			saveErrors(request, SwtUtil
					.logException(SwtErrorHandler.getInstance()
									.handleException(e, "save", NotesAction.class),
							request, ""));

			return getView("fail");
		} finally {
			mvmtNote = null;
			entityId = null;
			hostId = null;
			movementIdAsString = null;
			sessionNotesDetails = null;
			movTime = null;
		}
	}

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String saveMatchNotes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ActionErrors errors = new ActionErrors();

		try {
			log.debug("Entering saveMatchNotes method");
			MatchNote matchNote = getNotesMatch();
			String entityId = getSelectedEntityId();
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			Date date = SwtUtil.getSystemDatewithTime();
			matchNote.getId().setHostId(hostId);
			matchNote.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));
			matchNote.getId().setUpdateDate(date);
			String matchIdString = request.getParameter("selectedMatchId");
			if (matchIdString != null) {
				log.debug("matchIdString   " + matchIdString + "  entityId"
						  + entityId);
				log.debug("Inside save method the dyForm got is=>" + matchNote);
				matchNote.getId().setMatchId(new Long(matchIdString));
				matchNote.getId().setEntityId(entityId);
				log.debug("The details to be saved in the database are"
						  + matchNote);
				notesManager.saveMatchNotesDetails(matchNote);
			}
			request.setAttribute("entityCode", entityId);
			request.setAttribute("matchId", matchIdString);
			request.setAttribute("method", "showMatchNotes");
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("notesStatus", "matchNotes");
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug("Exiting saveMatchNotes mehtod");

			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.saveMatchNotes "
					  + swtexp.getMessage());
			swtexp.printStackTrace();

			putMovementSummaryDisplayRefreshParametersInRequest(request);

			MatchNote mn = new MatchNote();
			mn.setNoteText("");
			setNotesMatch(mn);
			request.setAttribute("notesMatch", "mn");
			String matchIdString = request.getParameter("selectedMatchId");
			request.setAttribute("matchId", matchIdString);
			request.setAttribute("method", "showMatchNotes");
			request.setAttribute("notesStatus", "matchNotes");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception e) {
			log.error("Exception in NotesAction.saveMatchNotes "
					  + e.getMessage());

			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "saveMatchNotes",
							NotesAction.class), request, ""));

			return getView("fail");
		}
	}

	/**
	 * This method is used to view the selected notes
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable declaration foe DynaValidatorForm to hold the form
		String selectedDate = null;
		// variable to hold the archiveId
		String archiveId = null;
		// variable to hold the SwtDataSource
		SwtDataSource dataSource = null;
		// variable to hold the movement notes
		MovementNote movNot = null;
		// collection to hole the movement details
		Collection notesDetails = null;
		// variable declared to get the cache manager instance
		CacheManager cacheManagerInst = null;
		// variable to hold the host id
		String hostId = null;
		// variable to hold the selected entity id
		String selectedEntityId = null;
		// variable to hold the movement id as type string
		String movmentIdAsString = null;
		// variable to hold the movement id as type Long
		Long movementId = null;
		// variable to hold the SystemInfo
		SystemInfo systemInfo = null;
		// declared to iterate the match notes
		Iterator iterator = null;
		// flag to indicate the record availability
		boolean recordFound = false;
		// object decalared to hold movement notes
		MovementNote movementNote = null;
		// hold the date in match notes
		String data = null;
		try {
			log.debug(this.getClass().getName() + " [view] -Enters");
			// get date from request
			selectedDate = request.getParameter("selectedDate");
			if (selectedDate != null) {
				;
			}
			// Added for Archive Movement Search
			archiveId = request.getParameter("archiveId");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			// trimming the string selectedDate
			selectedDate = selectedDate.trim();
			// intialising MovementNote and notes details collection
			movNot = new MovementNote();
			notesDetails = new ArrayList();
			// get the cache manager instance
			cacheManagerInst = CacheManager.getInstance();
			// get the host id
			hostId = cacheManagerInst.getHostId();
			dataSource.useArchive(archiveId);
			// getting the entity id from request
			selectedEntityId = request.getParameter("entityCode");
			if ((selectedEntityId == null)
				|| selectedEntityId.equalsIgnoreCase("")) {
				selectedEntityId = request.getParameter("selectedEntityId");
			}
			selectedEntityId = selectedEntityId.trim();
			// get movement id from request
			movmentIdAsString = request.getParameter("movId");
			// getting notes details from manager
			if ((movmentIdAsString != null)
				&& !(movmentIdAsString.equalsIgnoreCase(""))) {
				movementId = new Long(movmentIdAsString);
				systemInfo = new SystemInfo();
				movNot.getId().setHostId(hostId);
				if (SwtUtil.isEmptyOrNull(archiveId)) {
					notesDetails = notesManager.getNoteDetails(hostId,
							movementId, systemInfo, SwtUtil
									.getCurrentSystemFormats(request
											.getSession()));
				} else {
					notesDetails = notesManager.getArchiveMovementNoteDetails(
							selectedEntityId, movementId, new SystemInfo(),
							SwtUtil.getCurrentSystemFormats(request
									.getSession()), archiveId);

				}
			} else {
				notesDetails = (Collection) request.getSession().getAttribute(
						"sessionNotesDetails");
			}
			// iterating notes details to get date
			iterator = notesDetails.iterator();
			while (iterator.hasNext()) {
				movementNote = (MovementNote) (iterator.next());
				movNot = movementNote;
//				data = movementNote.getId().getUpdateDate().toString();
				if (SwtUtil.formatDate(movementNote.getId().getUpdateDate(), "yyyy-MM-dd HH:mm:ss").equals(selectedDate)) {
					recordFound = true;
					break;
				}
			}
			// set button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// things to be done if record found
			if (recordFound) {
				setNotes(movNot);
				request.setAttribute("notes", movNot);
				request.setAttribute("viewButtonStatus", "view");
				request.setAttribute("entityCode", selectedEntityId);
				request.setAttribute("movId", movmentIdAsString);
				request.setAttribute("screenFieldsStatus", "true");
			} else {
				request.setAttribute("entityCode", selectedEntityId);
				request.setAttribute("movId", movmentIdAsString);
				request.setAttribute("parentFormRefresh", "yes");
				request.setAttribute("recordFound", "N");
			}
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug("Exiting 'view' method");
		} catch (SwtException swtexp) {
			log
					.error("SwtException in NotesAction.view "
						   + swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.view " + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "view", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			((SwtDataSource) SwtUtil.getBean("dataSourceDb")).clearArchive();
			selectedDate = null;
			archiveId = null;
			dataSource = null;
			cacheManagerInst = null;
			hostId = null;
			selectedEntityId = null;
			movmentIdAsString = null;
			movementId = null;
			systemInfo = null;
			iterator = null;
			recordFound = false;
			data = null;
			log.debug(this.getClass().getName() + " [view] - Exit");
		}

		return getView("add");
	}

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ActionErrors errors = new ActionErrors();
		log.debug("Entering 'delete' method");

		SystemInfo systemInfo = new SystemInfo();
		Collection notesDetails = new ArrayList();
		MovementNote movNot = new MovementNote();
		Collection newSessionNotesDetails = new ArrayList();

		String selectedDate = request.getParameter("selectedDate");
		selectedDate = selectedDate.trim();
		log.debug("The date for the selected record is" + selectedDate);

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();

		String selectedEntityId = request.getParameter("selectedEntityId");

		if (selectedEntityId != null) {
			selectedEntityId = selectedEntityId.trim();
		}

		log.debug("The entityId for the selected record is=="
				  + selectedEntityId + "==");

		String movmentIdAsString = request.getParameter("selectedMovId");

		try {
			log
					.debug("The request.getParameter(selectedMovId) inside delete method is ==>"
						   + movmentIdAsString);

			if ((movmentIdAsString != null) && !(movmentIdAsString.equals(""))) {
				log.debug("The movementId for the selected record is"
						  + movmentIdAsString);

				Long movementId = new Long(movmentIdAsString);
				movNot.getId().setHostId(hostId);
				notesDetails = notesManager.getNoteDetails(hostId, movementId,
						systemInfo, SwtUtil.getCurrentSystemFormats(request
								.getSession()));
			} else {
				notesDetails = (Collection) request.getSession().getAttribute(
						"sessionNotesDetails");
			}

			MovementNote movementNote = new MovementNote();

			if(SwtUtil.isEmptyOrNull(selectedEntityId) || "all".equalsIgnoreCase(selectedEntityId)) {
				MovementManager movementManager = (MovementManager) SwtUtil
						.getBean("movementManager");
				Long movId = new Long(movmentIdAsString);
				Movement movement = movementManager.getMovementDetails(hostId,
						movId);
				selectedEntityId = movement.getId().getEntityId();
			}

			Iterator itr = notesDetails.iterator();

			if ((movmentIdAsString != null) && !movmentIdAsString.equals("")) {
				String strFormat = "yyyy-MM-dd HH:mm:ss";
				DateFormat myDateFormat = new SimpleDateFormat(strFormat);
				Date myDate = null;

				try {
					myDate = myDateFormat.parse(selectedDate);
					log.debug("myDate:::" + myDate);
				} catch (ParseException e) {
					System.out.println("Invalid Date Parser Exception ");

				}

				log.debug("Inside permanent If");
				movementNote.getId().setHostId(hostId);
				movementNote.getId().setEntityId(selectedEntityId);
				movementNote.getId().setMovementId(new Long(movmentIdAsString));
				movementNote.getId().setUpdateDate(myDate);
				notesManager.deleteNote(movementNote);
			} else {
				while (itr.hasNext()) {
					movementNote = (MovementNote) (itr.next());

					String dat = movementNote.getId().getUpdateDate()
							.toString();
					log.debug("The date for this record is " + dat);
					if (SwtUtil.formatDate(dat, "yyyy-MM-dd HH:mm:ss").equals(selectedDate)) {
						//	if (dat.equals(selectedDate)) {
						log.debug("Inside dat.equals(selectedDate)");
						movementNote.getId().setHostId(hostId);

						if ((movmentIdAsString != null)
							&& !movmentIdAsString.equals("")) {
							log.debug("Inside movmentIdAsString != null");
							movementNote.getId().setEntityId(selectedEntityId);
							notesManager.deleteNote(movementNote);
							log
									.debug("The record selected is with date======? "
										   + dat);

							break;
						}
					} else {
						newSessionNotesDetails.add(movementNote);
					}
				}
			}

			if ((movmentIdAsString != null) && !(movmentIdAsString.equals(""))) {
				Long movid = new Long(movmentIdAsString);
				notesDetails = notesManager.getNoteDetails(hostId, movid,
						systemInfo, SwtUtil.getCurrentSystemFormats(request
								.getSession()));
			} else {
				notesDetails = newSessionNotesDetails;
				request.getSession().setAttribute("sessionNotesDetails",
						newSessionNotesDetails);
			}

			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("movId", movmentIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			log
					.debug("Just before putting the notesDetails into the request the notes Detils are==>"
						   + notesDetails);
			request.setAttribute("notesDetails", notesDetails);

			request.setAttribute("unLock", request.getParameter("unLock"));

			putMovementSummaryDisplayRefreshParametersInRequest(request);

			log.debug("Exiting 'delete' method");

			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.delete "
					  + swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			if ((movmentIdAsString != null) && !(movmentIdAsString.equals(""))) {
				Long movid = new Long(movmentIdAsString);
				notesDetails = notesManager.getNoteDetails(hostId, movid,
						systemInfo, SwtUtil.getCurrentSystemFormats(request
								.getSession()));
			} else {
				notesDetails = newSessionNotesDetails;
				request.getSession().setAttribute("sessionNotesDetails",
						newSessionNotesDetails);
			}

			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("movId", movmentIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			request.setAttribute("notesDetails", notesDetails);

			putMovementSummaryDisplayRefreshParametersInRequest(request);

			return getView("success");
		} catch (Exception e) {
			log.error("Exception in NotesAction.delete " + e.getMessage());

			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "delete",
							NotesAction.class), request, ""));

			return getView("fail");
		}
	} // End of delete method

	/**
	 * This method is used to get the notes for the Match
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String showMatchNotes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// collection declared to get the Notes details for Match
		Collection notesDetails = null;
		// to hold the catch manager instance
		CacheManager cacheManagerInst = null;
		// hold the host id
		String hostId = null;
		// variable to hold the currency entity id
		String entityId = null;
		// variable to hold the current match id as string
		String matchIdAsString = null;
		// variable to hold the current match id as Long
		Long matchId = null;
		// variable to hold the currency id for selected match id
		String currencyId = null;
		// variable to hold the system information
		SystemInfo systemInfo = null;
		// variable to hold the ARCHIVE ID
		String archiveId = null;
		// variable to hold the SwtDataSource
		SwtDataSource dataSource = null;
		try {
			log.debug(this.getClass().getName() + " [showMatchNotes] Enters");
			// for collection
			notesDetails = new ArrayList();
			// set the button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// get the cache manger instance
			cacheManagerInst = CacheManager.getInstance();
			// get the current host id
			hostId = cacheManagerInst.getHostId();
			// get the entity id to which the Match belongs
			entityId = request.getParameter("entityCode");
			// get the selected entity id
			if (entityId.equalsIgnoreCase("")) {
				entityId = request.getParameter("selectedEntityId");
			}
			// get the Match id
			matchIdAsString = request.getParameter("matchId");
			if ("".equalsIgnoreCase(matchIdAsString)
				|| (matchIdAsString == null)) {
				matchIdAsString = request.getParameter("selectedMatchId");
			}
			matchIdAsString = matchIdAsString.trim();
			// convert string to long
			matchId = new Long(matchIdAsString);
			// get the currency id for the selected Match Id and check for
			// currency group access
			if (matchId != null) {
				currencyId = notesManager.getCurrency(entityId, null, matchId);
				SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
						currencyId);
				request.setAttribute("currencyAccess", request
						.getAttribute("menuEntityCurrGrpAccess"));
			}
			// initialization
			systemInfo = new SystemInfo();
			// get archive id
			archiveId = request.getParameter("archiveId");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			if (archiveId != null && !archiveId.equals(""))
				dataSource.useArchive(archiveId);
			// Start:code modified by Prasenjit Maji for Mantis 1874
			// on 28/09/12: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// get Movement notes details from manager
			if (SwtUtil.isEmptyOrNull(archiveId)) {
				notesDetails = notesManager.getMatchNoteDetails(hostId,
						matchId, SwtUtil.getCurrentSystemFormats(request
								.getSession()));
			}
			// End:code modified by Prasenjit Maji for Mantis 1874
			// on 28/09/12: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			else {
				notesDetails = notesManager.getArchiveMatchNoteDetails(hostId,
						matchId, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()),
						entityId, archiveId);

			}
			if (archiveId != null && !archiveId.equals(""))
				dataSource.clearArchive();
			// set variables for the request
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			// set the attribute for archiveId
			request.setAttribute("archiveId", archiveId);
			request.setAttribute("entityCode", entityId);
			request.setAttribute("notesDetails", notesDetails);
			request.setAttribute("matchId", matchIdAsString);
			request.setAttribute("method", "showMatchNotes");
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug(this.getClass().getName() + " [showMatchNotes] Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.showMatchNotes "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.showMatchNotes "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "showMatchNotes", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			// nullify
			cacheManagerInst = null;
			hostId = null;
			entityId = null;
			matchIdAsString = null;
			matchId = null;
			currencyId = null;
			systemInfo = null;
			archiveId = null;
		}
	} // End of showMatchNotes() method

	/**
	 * This method is used to view the selected notes for the Match
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String viewMatchNotes()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold the selected date
		String selectedDate = null;
		// variable to hold the Match notes
		MatchNote matchNote = null;
		// collection to hole the match details
		Collection notesDetails = null;
		// variable declared to get the cache manager instance
		CacheManager cacheManagerInst = null;
		// variable to hold the host id
		String hostId = null;
		// variable to hold the selected entity id
		String selectedEntityId = null;
		// variable to hold the match id as type string
		String matchIdAsString = null;
		// variable to hold the match id as type Long
		Long matchId = null;
		// variable to hold the SystemInfo
		SystemInfo systemInfo = null;
		// variable to hold the archiveId
		String archiveId = null;
		// variable to hold the SwtDataSource
		SwtDataSource dataSource = null;
		// declared to iterate the match notes
		Iterator iterator = null;
		// hold the date in match notes
		String date = null;
		try {
			log.debug(this.getClass().getName() + " [viewMatchNotes] Enters");
			// get the date from request
			selectedDate = request.getParameter("selectedDate");
			selectedDate = selectedDate.trim();
			// Initalizing match note and collection notes details
			matchNote = new MatchNote();
			notesDetails = new ArrayList();
			// get cache manger instance
			cacheManagerInst = CacheManager.getInstance();
			// get the host id
			hostId = cacheManagerInst.getHostId();
			// get entity id from request
			selectedEntityId = request.getParameter("entityCode");
			if (SwtUtil.isEmptyOrNull(selectedEntityId)) {
				selectedEntityId = request.getParameter("selectedEntityId");}
			selectedEntityId = selectedEntityId.trim();
			// get match id from request
			matchIdAsString = request.getParameter("matchId");
			matchId = new Long(matchIdAsString);
			systemInfo = new SystemInfo();
			// get host id from Match note
			matchNote.getId().setHostId(hostId);
			// get archive id from request
			archiveId = request.getParameter("archiveId");
			dataSource = (SwtDataSource) SwtUtil.getBean("dataSourceDb");
			if (archiveId != null && !archiveId.equals(""))
				dataSource.useArchive(archiveId);
			// Start:code modified by Prasenjit Maji for Mantis 1874
			// on 26/09/12: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// get Match notes details from archive id
			if (SwtUtil.isEmptyOrNull(archiveId)
				|| archiveId.equalsIgnoreCase("null")) {
				notesDetails = notesManager.getMatchNoteDetails(hostId,
						matchId, SwtUtil.getCurrentSystemFormats(request
								.getSession()));
			}
			// End:code modified by Prasenjit Maji for Mantis 1874
			// on 26/09/12: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			else {
				notesDetails = notesManager.getArchiveMatchNoteDetails(hostId,
						matchId, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()),
						selectedEntityId, archiveId);
			}
			if (archiveId != null && !archiveId.equals(""))
				dataSource.clearArchive();
			// iterating notes details to get date
			iterator = notesDetails.iterator();
			while (iterator.hasNext()) {
				matchNote = (MatchNote) (iterator.next());
//				date = matchNote.getId().getUpdateDate().toString();
				if (SwtUtil.formatDate(matchNote.getId().getUpdateDate(), "yyyy-MM-dd HH:mm:ss").equals(selectedDate)) {
					break;
				}
			}
			// set button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			setNotesMatch(matchNote);
			request.setAttribute("notesMatch", matchNote);
			request.setAttribute("viewButtonStatus", "view");
			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("matchId", matchIdAsString);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("method", "showMatchNotes");
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			log.debug(this.getClass().getName() + " [viewMatchNotes] Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.viewMatchNotes "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.viewMatchNotes "
					  + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "viewMatchNotes", NotesAction.class), request, "");
			return getView("fail");
		} finally {
			selectedDate = null;
			cacheManagerInst = null;
			hostId = null;
			selectedEntityId = null;
			matchIdAsString = null;
			matchId = null;
			systemInfo = null;
			archiveId = null;
			dataSource = null;
			iterator = null;
			date = null;
		}
	}

	// Start:code modified by Prasenjit Maji for Mantis 1874
	// on 28/09/12: Pre advice Input:Currency drop down should load
	// currencies for default entity if cancel button is pressed
	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String deleteMatchNotes() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// collection to hold the match note details
		Collection notesDetails = null;
		// variable to hold the Match notes
		MatchNote matchNote = null;
		// variable to hold the selected date
		String selectedDate = null;
		// variable declared to get the cache manager instance
		CacheManager cacheManagerInst = null;
		// variable to hold the host id
		String hostId = null;
		// variable to hold the selected entity id
		String selectedEntityId = null;
		// variable to hold the match id as type string
		String matchIdAsString = null;
		// variable to hold the match id as type long
		Long matchId = null;
		// variable to hold user defined date format
		DateFormat userDateFormat = null;

		try {
			log.debug(this.getClass().getName() + " [deleteMatchNotes] Enters");
			// Initalizing match note and collection notes details
			notesDetails = new ArrayList();
			matchNote = new MatchNote();
			// get the date from request
			selectedDate = request.getParameter("selectedDate");
			selectedDate = selectedDate.trim();
			// get cache manger instance
			cacheManagerInst = CacheManager.getInstance();
			// get user defined date format
			userDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			// get the host id
			hostId = cacheManagerInst.getHostId();
			// get selected Entity id from request
			selectedEntityId = request.getParameter("selectedEntityId");
			selectedEntityId = selectedEntityId.trim();
			// get selected match id from request
			matchIdAsString = request.getParameter("selectedMatchId");
			// match id as long
			matchId = new Long(matchIdAsString);
			// setting values
			matchNote.getId().setHostId(hostId);
			matchNote.getId().setHostId(hostId);
			matchNote.getId().setEntityId(selectedEntityId);
			matchNote.getId().setMatchId(new Long(matchIdAsString));
			matchNote.getId().setUpdateDate(userDateFormat.parse(selectedDate));
			// deleting match note
			notesManager.deleteMatchNote(matchNote);
			// get match note details
			notesDetails = notesManager.getMatchNoteDetails(hostId, matchId,
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("matchId", matchIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// setting value to request object
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			// setting note details to request
			request.setAttribute("notesDetails", notesDetails);
			request.setAttribute("method", "showMatchNotes");
			putMovementSummaryDisplayRefreshParametersInRequest(request);

			log.debug(this.getClass().getName() + " [deleteMatchNotes] Exit");

			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.deleteMatchNotes "
					  + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			notesDetails = notesManager.getMatchNoteDetails(hostId, matchId,
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("matchId", matchIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));

			request.setAttribute("notesDetails", notesDetails);
			request.setAttribute("method", "showMatchNotes");
			putMovementSummaryDisplayRefreshParametersInRequest(request);
			return getView("success");
		} catch (Exception e) {
			log.error("Exception in NotesAction.deleteMatchNotes "
					  + e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "deleteMatchNotes",
							NotesAction.class), request, ""));

			return getView("fail");
		} finally {
			notesDetails = null;
			matchNote = null;
			selectedDate = null;
			cacheManagerInst = null;
			selectedEntityId = null;
			hostId = null;
			matchIdAsString = null;
			matchId = null;
			userDateFormat = null;
		}

	}

	// End:code modified by Prasenjit Maji for Mantis 1874
	// on 28/09/12: Pre advice Input:Currency drop down should load
	// currencies for default entity if cancel button is pressed

	/**
	 * This method is used while showing the notes details based on the sweep id
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String showSweepNotes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold notes details
		Collection<SweepNote> notesDetails = null;
		String archiveId = null;
		try {
			log.debug(this.getClass().getName() + " [showSweepNotes] Enters");
			// set attribute for currency access
			request.setAttribute("currencyAccess", request
					.getParameter("currencyAccess"));

			archiveId = request.getParameter("archiveId");

			// set the button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);


			if (SwtUtil.isEmptyOrNull(request.getParameter("selectedSweepId"))) {
				notesDetails = (Collection) request.getSession().getAttribute(
						SwtConstants.NOTES_SWEEP_SESSION_OBJECT);

				if (notesDetails == null) {
					notesDetails = new ArrayList<SweepNote>();
				}
			} else {
				// set attribute for sweep in session
				request.getSession().setAttribute("sweepIdInSession",
						request.getParameter("selectedSweepId"));
				// Start:code modified by Prasenjit Maji for Mantis 1874
				// on 28/09/12: Pre advice Input:Currency drop down should load
				// currencies for default entity if cancel button is pressed
				// get the notes details form Notes manager
				notesDetails = notesManager.getSweepNoteDetails(CacheManager
						.getInstance().getHostId(), new Long(request
						.getParameter("selectedSweepId")), SwtUtil
						.getCurrentSystemFormats(request.getSession()), archiveId);
			}
			// End:code modified by Prasenjit Maji for Mantis 1874
			// on 28/09/12: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed

			// set attribute for currentUserId
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));
			// set attribute for notesDetails
			request.setAttribute("notesDetails", notesDetails);
			// set attribute for sweepId
			request.setAttribute("sweepId", request
					.getParameter("selectedSweepId"));
			// set attribute for method
			request.setAttribute("method", "showSweepNotes");
			log.debug(this.getClass().getName() + " [showSweepNotes] Exit");

			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.showSweepNotes "
					  + swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.showSweepNotes "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "showSweepNotes", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			notesDetails = null;

		}
	}

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String addSweepNotes()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold sweep notes
		SweepNote sweepNote = null;

		try {
			log.debug("Entering addSweepNotes method");
			// instance for sweepNote
			sweepNote = new SweepNote();
			// get the updated date as string
			sweepNote.getId().setUpdateDateAsString(
					SwtUtil.getSystemDateString());

			// Set attribute for currency access
			request.setAttribute("currencyAccess", request
					.getParameter("currencyAccess"));

			// set the button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			// set the updated user name
			sweepNote.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));

			// set attribute for show sweep notes
			request.setAttribute("method", "showSweepNotes");
			// set attribute for sweep id
			request.setAttribute("sweepId", request.getParameter("sweepId"));
			// set notes sweep
			setNotesSweep(sweepNote);
			// If you want to set it as a request attribute
			request.setAttribute("notesSweep", sweepNote);

			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.addSweepNotes "
					  + swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception e) {
			log.error("Exception in NotesAction.addSweepNotes "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addSweepNotes", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			sweepNote = null;
		}
	}
	private SweepNote notesSweep = null;

	public SweepNote getNotesSweep() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if (notesSweep == null) {
			notesSweep = RequestObjectMapper.getObjectFromRequest(SweepNote.class, request, "notesSweep");
			if (notesSweep == null) {
				notesSweep = new SweepNote();
				// Set any default values or perform necessary initialization
			}
		}
		return notesSweep;
	}

	public void setNotesSweep(SweepNote notesSweep) {
		this.notesSweep = notesSweep;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		if (request != null) {
			request.setAttribute("notesSweep", notesSweep);
		}
	}
	/**
	 * This saveSweepNotes is used to save the newly added notes of subscreen in
	 * sweep display screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String saveSweepNotes()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold sweepIdAsString
		String sweepIdAsString = null;
		// varible to hold sweep notes
		SweepNote sweepNotes = null;
		// variable to hold to notes text
		String noteText = null;
		// variable to hold session notes details
		Collection<SweepNote> sessionNotesDetails = null;
		// variable to append time in string format
		StringBuffer sweepTime = null;
		// variable to hold hours
		int sweepHours;
		// variable to hold minutes
		int sweepMins;
		try {
			log.debug(this.getClass().getName() + " - [saveSweepNotes] - "
					  + "Entry");
			// get the selected sweep id
			sweepIdAsString = request.getParameter("selectedSweepId").trim();

			// get the form assign into the dyna validator form
			// Get the sweep notes form form
			sweepNotes = getNotesSweep();


			// set the host id
			sweepNotes.getId()
					.setHostId(CacheManager.getInstance().getHostId());
			// set the updated user name
			sweepNotes.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));

			// set updated date
			sweepNotes.getId().setUpdateDate(SwtUtil.getSystemDatewithTime());
			// condition for new sweep id
			if (SwtUtil.isEmptyOrNull(sweepIdAsString)) {
				// Get the date format value
				sweepNotes.getId().setUpdateDateAsString(
						SwtUtil.formatDate(sweepNotes.getId().getUpdateDate(),
								SwtUtil.getCurrentSystemFormats(
												request.getSession())
										.getDateFormatValue()));
				// Start:code modified by Prasenjit Maji for Mantis 1874
				// on 26/09/12: Pre advice Input:Currency drop down should load
				// currencies for default entity if cancel button is pressed
				// get the Hours & Mins from the sweep note
				sweepHours = sweepNotes.getId().getUpdateDate().getHours();
				// get the minutes from the sweep note
				sweepMins = sweepNotes.getId().getUpdateDate().getMinutes();
				sweepTime = new StringBuffer();
				// if single digit hours append '0' to front to get 2
				// digit format
				sweepTime.append((sweepHours < 10) ? "0" : "");
				sweepTime.append(sweepHours);
				sweepTime.append(":");
				// if single digit minutes append '0' to front to get 2
				// digit format
				sweepTime.append((sweepMins < 10) ? "0" : "");
				sweepTime.append(sweepMins);
				// Get the updated time
				sweepNotes.getId().setUpdateTimeAsString(sweepTime.toString());
				// End:code modified by Prasenjit Maji for Mantis 1874
				// on 26/09/12: Pre advice Input:Currency drop down should load
				// currencies for default entity if cancel button is pressed
				// get the note text
				noteText = sweepNotes.getNoteText();
				// set the compressed note text
				sweepNotes.setCompressedNoteText(noteText);

				// get the collection form sweep session object
				sessionNotesDetails = (Collection) request.getSession()
						.getAttribute(SwtConstants.NOTES_SWEEP_SESSION_OBJECT);

				if (sessionNotesDetails == null) {
					sessionNotesDetails = new ArrayList<SweepNote>();
				}
				// add the session notes details
				sessionNotesDetails.add(sweepNotes);
				// set the attribute for sweep session object
				request.getSession().setAttribute(
						SwtConstants.NOTES_SWEEP_SESSION_OBJECT,
						sessionNotesDetails);
			}
			// for existing sweep id
			else {
				// set the sweep id
				sweepNotes.getId().setSweepId(new Long(sweepIdAsString));
				// set the entity id
//				sweepNotes.getId().setEntityId(
//						(String) dyForm.get("selectedEntityId"));

				notesManager.saveSweepNotesDetails(sweepNotes);
			}

			// set the attribute for currency access
			request.setAttribute("currencyAccess", request
					.getParameter("currencyAccess"));

			// set attribute for sweepId
			request.setAttribute("sweepId", sweepIdAsString);
			// set attribute for method
			request.setAttribute("method", "showSweepNotes");
			// set attribute for notesStatus
			request.setAttribute("notesStatus", "sweepNotes");
			// set attribute for parentFormRefresh
			request.setAttribute("parentFormRefresh", "yes");

			log.debug(this.getClass().getName() + " - [saveSweepNotes] - "
					  + "Exit");

			return getView("add");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.saveSweepNotes "
					  + swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception e) {
			log.error("SwtException in NotesAction.saveSweepNotes "
					  + e.getMessage());

			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "saveSweepNotes",
							NotesAction.class), request, ""));

			return getView("fail");
		} finally {
			sweepIdAsString = null;
			sweepNotes = null;
			noteText = null;
			sessionNotesDetails = null;
			sweepTime = null;
		}
	}

	// Start:code modified by Prasenjit Maji for Mantis 1874
	// on 28/09/12: Pre advice Input:Currency drop down should load
	// currencies for default entity if cancel button is pressed

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String viewSweepNotes()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// collection to hold the sweep note details
		Collection notesDetails = null;
		// variable to hold the SweepNote
		SweepNote sweepNote = null;
		// variable to hold the selected date
		String selectedDate = null;
		// variable declared to get the cache manager instance
		CacheManager cacheManagerInst = null;
		// variable to hold the host id
		String hostId = null;
//		// variable to hold the selected entity id
//		String selectedEntityId = null;
		// variable to hold the sweep id as type string
		String sweepIdAsString = null;
		// variable to hold the sweep id as type long
		Long sweepId = null;
		// variable to hold note Details for iteration
		Iterator noteDetailsItr = null;
		// variable to hold update date
		String updateDate = null;
		// variable to hold sweep notes details
		SweepNote sweepNotesDetails = null;
		String archiveId = null;
		try {
			log.debug(this.getClass().getName() + " - [viewSweepNotes] - "
					  + "Entry");
			selectedDate = request.getParameter("selectedDate");
			selectedDate = selectedDate.trim();
			// instance for sweepNote
			sweepNote = new SweepNote();
			notesDetails = new ArrayList();
			// get cache manger instance
			cacheManagerInst = CacheManager.getInstance();
			archiveId =  request.getParameter("archiveId");
			// get host id
			hostId = cacheManagerInst.getHostId();
//			selectedEntityId = request.getParameter("entityCode");
//
//			if ("".equalsIgnoreCase(selectedEntityId)) {
//				selectedEntityId = request.getParameter("selectedEntityId");
//			}
//
//			selectedEntityId = selectedEntityId.trim();
			sweepIdAsString = request.getParameter("sweepId");

			if ((sweepIdAsString == null)
				|| sweepIdAsString.equalsIgnoreCase("")) {
				notesDetails = (Collection) request.getSession().getAttribute(
						SwtConstants.NOTES_SWEEP_SESSION_OBJECT);
			} else {

				//TODO:
				sweepId = new Long(sweepIdAsString);
				sweepNote.getId().setHostId(hostId);
				notesDetails = notesManager.getSweepNoteDetails(hostId,
						sweepId, SwtUtil.getCurrentSystemFormats(request
								.getSession()),archiveId);
			}

			noteDetailsItr = notesDetails.iterator();

			while (noteDetailsItr.hasNext()) {
				sweepNotesDetails = (SweepNote) (noteDetailsItr.next());
				sweepNote = sweepNotesDetails;

				updateDate = SwtUtil.formatDate(sweepNotesDetails.getId().getUpdateDate(), "yyyy-MM-dd HH:mm:ss");

				if (updateDate.equals(selectedDate)) {
					break;
				}
			}

			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			setNotesSweep(sweepNote);
			// If you want to set it as a request attribute
			request.setAttribute("notesSweep", sweepNote);
			request.setAttribute("viewButtonStatus", "view");
//			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("sweepId", sweepIdAsString);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("method", "showSweepNotes");
			log.debug(this.getClass().getName() + " - [viewSweepNotes] - "
					  + "Exit");

			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error("SwtException in NotesAction.viewSweepNotes "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Exception in NotesAction.viewSweepNotes "
					  + e.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "viewSweepNotes", NotesAction.class), request, "");

			return getView("fail");
		} finally {
			notesDetails = null;
			sweepNote = null;
			selectedDate = null;
			cacheManagerInst = null;
//			selectedEntityId = null;
			hostId = null;
//			selectedEntityId = null;
			sweepIdAsString = null;
			sweepId = null;
			noteDetailsItr = null;
			updateDate = null;
			sweepNotesDetails = null;
		}
	}

	/**
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String deleteSweepNotes() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// collection to hold the sweep note details
		Collection notesDetails = null;
		// variable to hold the SweepNote
		SweepNote sweepNote = null;
		// variable to hold new SessionNotesDetails
		Collection newSessionNotesDetails = null;
		// variable to hold selected date
		String selectedDate = null;
		// variable declared to get the cache manager instance
		CacheManager cacheManagerInst = null;
		// variable to hold the host id
		String hostId = null;
//		// variable to hold the selected entity id
//		String selectedEntityId = null;
		// variable to hold the sweep id as string
		String sweepIdAsString = null;
		// variable to hold the sweep id as long
		Long sweepId = null;
		// variable to hold note details iterator object
		Iterator noteDetailsItr = null;
		// variable to hold the sweepNoteDetails
		SweepNote sweepNoteDetails = null;
		// variable to hold the userDateFormat
		DateFormat userDateFormat = null;
		// variable to check new or old sweep id
		boolean isSweepIdPresent = false;
		try {
			log.debug(this.getClass().getName() + " - [deleteSweepNotes] - "
					  + "Entry");

			notesDetails = new ArrayList();
			// Initalizing sweep notes details
			sweepNote = new SweepNote();
			newSessionNotesDetails = new ArrayList();
			// get selected date from request
			selectedDate = request.getParameter("selectedDate");
			selectedDate = selectedDate.trim();
			// get cache manger instance
			cacheManagerInst = CacheManager.getInstance();
			// get host id
			hostId = cacheManagerInst.getHostId();
			// get selected entity from request
//			selectedEntityId = request.getParameter("selectedEntityId");
//			selectedEntityId = selectedEntityId.trim();
			// get selected sweep id from request
			sweepIdAsString = request.getParameter("selectedSweepId");
			isSweepIdPresent = !((sweepIdAsString == null) || sweepIdAsString
					.equalsIgnoreCase(""));
			if (!isSweepIdPresent) {
				notesDetails = (Collection) request.getSession().getAttribute(
						SwtConstants.NOTES_SWEEP_SESSION_OBJECT);
			} else {
				sweepId = new Long(sweepIdAsString);
				sweepNote.getId().setHostId(hostId);
				notesDetails = notesManager.getSweepNoteDetails(hostId,
						sweepId, SwtUtil.getCurrentSystemFormats(request
								.getSession()), null);
			}

			noteDetailsItr = notesDetails.iterator();
			sweepNoteDetails = new SweepNote();

			if (isSweepIdPresent) {
				userDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sweepNoteDetails.getId().setHostId(hostId);
//				sweepNoteDetails.getId().setEntityId(selectedEntityId);
				sweepNoteDetails.getId().setSweepId(new Long(sweepIdAsString));
				sweepNoteDetails.getId().setUpdateDate(
						userDateFormat.parse(selectedDate));
				notesManager.deleteSweepNote(sweepNoteDetails);
				notesDetails = notesManager.getSweepNoteDetails(hostId,
						sweepId, SwtUtil.getCurrentSystemFormats(request
								.getSession()), null);
			} else {
				while (noteDetailsItr.hasNext()) {
					sweepNoteDetails = (SweepNote) (noteDetailsItr.next());

					String dat = sweepNoteDetails.getId().getUpdateDate()
							.toString();
					if (!SwtUtil.formatDate(dat, "yyyy-MM-dd HH:mm:ss").equals(selectedDate)) {
						newSessionNotesDetails.add(sweepNoteDetails);
					}

					notesDetails = newSessionNotesDetails;
					request.getSession().setAttribute(
							SwtConstants.NOTES_SWEEP_SESSION_OBJECT,
							newSessionNotesDetails);
				}
			}

//			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("sweepId", sweepIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));

			request.setAttribute("notesDetails", notesDetails);
			request.setAttribute("method", "showSweepNotes");

			log.debug(this.getClass().getName() + " - [deleteSweepNotes] - "
					  + "Exit");

			return getView("success");
		} catch (SwtException swtexp) {
			log.error("SwtException in NotesAction.deleteSweepNotes "
					  + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			if (isSweepIdPresent) {
				notesDetails = notesManager.getSweepNoteDetails(hostId,
						sweepId, SwtUtil.getCurrentSystemFormats(request
								.getSession()), null);
			} else {
				notesDetails = newSessionNotesDetails;
				request.getSession().setAttribute(
						SwtConstants.NOTES_SWEEP_SESSION_OBJECT,
						newSessionNotesDetails);
			}

//			request.setAttribute("entityCode", selectedEntityId);
			request.setAttribute("sweepId", sweepIdAsString);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("currentUserId", SwtUtil
					.getCurrentUserId(request.getSession()));

			request.setAttribute("notesDetails", notesDetails);
			request.setAttribute("method", "showSweepNotes");

			return getView("success");
		} catch (Exception e) {
			log.error("SwtException in NotesAction.deleteSweepNotes "
					  + e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "deleteSweepNotes",
							NotesAction.class), request, ""));

			return getView("fail");
		} finally {
			notesDetails = null;
			sweepNote = null;
			newSessionNotesDetails = null;
			selectedDate = null;
			cacheManagerInst = null;
			hostId = null;
			sweepIdAsString = null;
			sweepId = null;
			noteDetailsItr = null;
			sweepNoteDetails = null;
			userDateFormat = null;
		}
	}

	// End:code modified by Prasenjit Maji for Mantis 1874
	// on 28/09/12: Pre advice Input:Currency drop down should load
	// currencies for default entity if cancel button is pressed

	/**
	 *
	 * @param req
	 * @param addStatus
	 * @param viewStatus
	 * @param deleteStatus
	 * @param closeStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String viewStatus, String deleteStatus, String closeStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, SwtConstants.STR_TRUE);
		req.setAttribute(SwtConstants.VIEW_BUT_STS, SwtConstants.STR_TRUE);
		req.setAttribute(SwtConstants.DEL_BUT_STS, SwtConstants.STR_TRUE);
		req.setAttribute(SwtConstants.CLOSE_BUT_STS, closeStatus);
	}

	private void putMovementSummaryDisplayRefreshParametersInRequest(
			HttpServletRequest request) {
		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("isNotesPresent", request
				.getParameter("isNotesPresent"));
		request.setAttribute("currencyCode", request
				.getParameter("currencyCode"));
		request.setAttribute("date", request.getParameter("date"));
	}
}
