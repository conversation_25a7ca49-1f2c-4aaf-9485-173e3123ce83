/*
 * @(#)ScreenOptionAction.java 1.0 06/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.util.ArrayList;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.model.Sweep;
import org.swallow.work.service.ScreenOptionManager;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * ScreenOptionAction.java
 * 
 * Handles the loading and saving of screen option(refresh rate).
 * 
 * <AUTHOR> Balaji A
 * @date Aug 06, 2010
 */



import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/screenOption", "/screenOption.do"})
public class ScreenOptionAction extends BaseController {
    private static final Map<String, String> viewMap = new HashMap<>();
    static {
        viewMap.put("fail", "error");
        viewMap.put("success", "jsp/work/autorefreshrate");
    }

    private String getView(String resultName) {
        return viewMap.getOrDefault(resultName, "error");
    }

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
    public String execute(@RequestParam(value = "method", required = false) String method,
                          HttpServletRequest request, HttpServletResponse response) throws SwtException {
        method = String.valueOf(method);
        switch (method) {
            case "getRate":
                return getRate();
            case "save":
                return save();
            case "saveFontSize":
                return saveFontSize();
        }
		if(method == null || method.equals(""))
			return unspecified();


    return null;
}



private ScreenOption screenOption;
public ScreenOption getScreenOption() {
    HttpServletRequest request = SwtUtil.getCurrentRequest();
    screenOption = RequestObjectMapper.getObjectFromRequest(ScreenOption.class, request, "screenOption");
    return screenOption;
}
public void setScreenOption(ScreenOption screenOption) {
    this.screenOption = screenOption;
    HttpServletRequest request = SwtUtil.getCurrentRequest();
    request.setAttribute("screenOption", screenOption);
}


	/** Log Instance */
	private final Log log = LogFactory.getLog(ScreenOptionAction.class);

	/** ScreenOptionManager instance */
	@Autowired
	private ScreenOptionManager screenOptionManager = null;
	

	
	private Sweep sweepQueue;
	public Sweep getSweepQueue() {
    HttpServletRequest request = SwtUtil.getCurrentRequest();
    sweepQueue = RequestObjectMapper.getObjectFromRequest(Sweep.class, request, "sweepQueue");
    return sweepQueue;
}

	public void setSweepQueue(Sweep sweepQueue) {
		this.sweepQueue = sweepQueue;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("sweepQueue", sweepQueue);
	}
	
	
	/**
	 * Setter method to initialise the ScreenOptionManager
	 * 
	 * @param screenOptionManager
	 */
	public void setScreenOptionManager(ScreenOptionManager screenOptionManager) {
		this.screenOptionManager = screenOptionManager;
	}

	/**
	 * Default method invoked when no method name is specified in the request
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug("entering 'unspecified' method");
		return getRate();
	}

	/**
	 * Method to fetch the refresh rate for the given user and screen id
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getRate()
			throws SwtException {
	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	log.debug("entering 'getRate' method");

		try {
			// Getting the form instance
//			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			// Getting the screen id from request
			String screenId = request.getParameter("screenId");
			// Getting the parentRefreshMethod from request
			String parentRefreshMethod = request
					.getParameter("parentRefreshMethod");
			// Getting the user id from request
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			// Initialising the screen option instance
			ScreenOption screenOption = new ScreenOption();
			// setting the host id to screen option
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// setting the user id to screen option
			screenOption.getId().setUserId(userId);
			// setting the screen id to screen option
			screenOption.getId().setScreenId(screenId);
			// getting the rate for the given screen and user id
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// setting the parentRefreshMethod to request
			request.setAttribute("parentRefreshMethod", parentRefreshMethod);
			// Set the screen option instance to form
			setScreenOption(screenOption);
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in ScreenOptionAction.'getRate' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("success");
		} catch (Exception exp) {
			log
					.debug("Exception Catch in ScreenOptionAction.'getRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getRate", ScreenOptionAction.class), request, "");

			return getView("fail");
		}
		log.debug("existing 'getRate' method");
		return getView("success");
	}

	/**
	 * Method to save the reset refresh rate for the given screen and user id
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
	HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();
	log.debug("entering 'save' method");

		try {
			// Getting the screen option from form object
			this.screenOption = getScreenOption();
			// Getting the parentRefreshMethod from request
			String parentRefreshMethod = request
					.getParameter("parentRefreshMethod");
			// Setting the property name refresh rate
			screenOption.getId().setPropertyName(
					SwtConstants.PROPNAME_REFRESH_RATE);
			String propValue = screenOption.getPropertyValue();
			ScreenOption scrOption = screenOptionManager
					.getRefreshRate(screenOption);
			scrOption.setPropertyValue(propValue);
			// Sending the collection to save screen option
			screenOptionManager.saveRefreshRate(scrOption);
			// setting the parentRefreshMethod to request
			request.setAttribute("parentRefreshMethod", parentRefreshMethod);
			request.setAttribute("parentRefresh", "yes");
			// set the parent refresh flag in request
			request.setAttribute("screenOption", screenOption);
			setScreenOption(screenOption);
		} catch (SwtException swtexp) {
			log
					.debug("SwtException Catch in ScreenOptionAction.'save' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("success");
		} catch (Exception exp) {
			log.debug("Exception Catch in ScreenOptionAction.'save' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", ScreenOptionAction.class), request, "");

			return getView("fail");
		}
		log.debug("existing 'save' method");
		return getView("success");
	}

	/*
	 * Start: Code added for Mantis 1386: User defined option to show normal or
	 * small fonts - by Marshal on 18-Apr-2011
	 */
	/**
	 * This method is used to save the font size of a screen (DataGrid) for
	 * particular User irrespective of Entity.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 * <AUTHOR>
	 */
	public String saveFontSize()
			throws SwtException {
	HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();
	// Declares parentRefreshMethod
		String parentRefreshMethod = null;
		// Declares propertyValue
		String propertyValue = null;
		// Declares tempScreenOption
		ScreenOption tempScreenOption = null;
		// Declares screenOption
		ScreenOption screenOption = null;
		try {
			log.debug(this.getClass().getName() + " - [saveFontSize] Entry");
			// Instantiates tempScreenOption
			tempScreenOption = new ScreenOption();
			// Instantiates screenOption
			screenOption = new ScreenOption();
//			// Type casts ActionForm to DynaValidatorForm
//			DynaValidatorForm validatorForm = (DynaValidatorForm) form;
			// Type casts screenOption
			screenOption = getScreenOption();
			// Reads parentRefreshMethod from request
			parentRefreshMethod = request.getParameter("parentRefreshMethod");
			// Sets Property name in screenOption
			screenOption.getId()
					.setPropertyName(SwtConstants.PROPNAME_FONTSIZE);
			// Gets the property value
			propertyValue = screenOption.getPropertyValue();
			// Calls the method to get the current font size
			tempScreenOption = screenOptionManager.getFontSize(screenOption);
			// Sets the property value
			tempScreenOption.setPropertyValue(propertyValue);
			// Checks the screen Ids against BookGroupMonitor
			if (getScreenIdList().contains(
					Integer.valueOf(screenOption.getId().getScreenId()))) {
				for (Integer itemId : getScreenIdList()) {
					// Sets the screenIds of Book, Group and MetaGroup
					tempScreenOption.getId().setScreenId(itemId.toString());
					// Calls the method to save the font size
					screenOptionManager.saveFontSize(tempScreenOption);
				}
			} else {
				// Calls the method to save the font size
				screenOptionManager.saveFontSize(tempScreenOption);
			}
			setScreenOption(screenOption);
			request.setAttribute("screenOption", this.screenOption);
			request.setAttribute("parentRefreshMethod", parentRefreshMethod);
			request.setAttribute("parentRefresh", "yes");
			log.debug(this.getClass().getName() + " - [saveFontSize] Exit");
		} catch (SwtException swtexp) {
			log.error("SwtException caught in " + this.getClass().getName()
					+ " [saveFontSize] method : " + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception exp) {
			log.error("Exception caught in " + this.getClass().getName()
					+ " [saveFontSize] method : " + exp.getMessage());
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(exp, "saveFontSize",
									ScreenOptionAction.class), request, "");
			return getView("fail");
		}
		return getView("success");
	}

	/**
	 * This method is used to get the screen Ids of BookGroup Monitor in an
	 * ArrayList.<br>
	 * BookGroup has got three screen Ids<br> - Book - 3<br> - Group - 6<br> -
	 * MetaGroup - 7 respectively<br>
	 * and it is to maintain the same font style for the three screens.<br>
	 * 
	 * @return ArrayList of screenIds
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private ArrayList<Integer> getScreenIdList() throws SwtException {
	HttpServletRequest request = SwtUtil.getCurrentRequest();
HttpServletResponse response = SwtUtil.getCurrentResponse();
	/* Local variables declaration */
		Integer bookItemId = null;
		Integer groupItemId = null;
		Integer metaGroupId = null;
		ArrayList<Integer> listScreenIds = null;
		try {
			/* Gets the screen Ids for the three BookGroup monitor screens */
			bookItemId = Integer.valueOf(SwtConstants.BOOK_MONITOR_ID);
			groupItemId = Integer.valueOf(SwtConstants.GROUP_MONITOR_ID);
			metaGroupId = Integer.valueOf(SwtConstants.METAGROUP_MONITOR_ID);
			// Gets the three screen ids in an Integer array
			Integer[] arrItemIds = { bookItemId, groupItemId, metaGroupId };
			listScreenIds = new ArrayList<Integer>();
			// Iterates and assigns the screen ids in the ArrayList
			for (Integer itemId : arrItemIds) {
				listScreenIds.add(itemId);
			}
		} catch (Exception e) {
			log.error("Exception caught in" + this.getClass().getName()
					+ "[arrayScreenIds] " + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"arrayScreenIds", ScreenOptionAction.class);
		}
		return listScreenIds;
	}
	/*
	 * End: Code added for Mantis 1386: User defined option to show normal or
	 * small fonts - by Marshal on 18-Apr-2011
	 */
}