/*
 * @(#)ForecastMonitorAction.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyInterestManager;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.model.UserBuckets;
import org.swallow.work.model.UserTemplate;
import org.swallow.work.service.ForecastMonitorManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.EntityMonitorForm;
import org.swallow.work.web.form.ForecastMonitorForm;
import org.swallow.work.web.form.ForecastRecord;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * ForecastMonitorAction.java
 *
 * ForecastMonitorAction class is used for ForecastMonitorAction screen that
 * will display book predicted Balances for 30 days.<br>
 *
 * <AUTHOR>
 * @date Feb 10, 2011
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/forecastMonitor", "/forecastMonitor.do"})
public class ForecastMonitorAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("flexAddAssumption", "jsp/work/forecastassumptionaddflex");
		viewMap.put("flexobjectOption", "jsp/work/forecastmonitoroptionsflex");
		viewMap.put("fail", "error");
		viewMap.put("flexAddOpenAssumption", "jsp/work/forecastassumptionaddflexdata");
		viewMap.put("success", "jsp/work/forecastmonitorflexdata");
		viewMap.put("flexAssumption", "jsp/work/forecastassumptionflex");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("flexTemplateOption", "jsp/work/templateoptionsflex");
		viewMap.put("options", "jsp/work/forecastmonitoroptionsflexdata");
		viewMap.put("userTemplateoptions", "jsp/work/templateoptionsflexdata");
		viewMap.put("flexobject", "jsp/work/forecastmonitorflex");
		viewMap.put("flexOpenAssumption", "jsp/work/forecastassumptionflexdata");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "flex":
				return flex();
			case "unspecified":
				return unspecified();
			case "displayForecastMonitorDetails":
				return displayForecastMonitorDetails();
			case "displayForecastMonitorOptions":
				return displayForecastMonitorOptions();
			case "displayAssumption":
				return displayAssumption();
			case "displayUserTemplateOptions":
				return displayUserTemplateOptions();
			case "displayAddAssumption":
				return displayAddAssumption();
			case "saveForecastMonitorOptions":
				return saveForecastMonitorOptions();
			case "saveUserTemplateOptions":
				return saveUserTemplateOptions();
			case "saveForecastAssumption":
				return saveForecastAssumption();
			case "deleteForecastAssumption":
				return deleteForecastAssumption();
			case "saveForecastScenario":
				return saveForecastScenario();
			case "flexOption":
				return flexOption();
			case "flexAssumption":
				return flexAssumption();
			case "flexTemplateOption":
				return flexTemplateOption();
			case "flexModifyAssumption":
				return flexModifyAssumption();
			case "saveColumnWidth":
				return saveColumnWidth();
		}


		return unspecified();
	}



	/**
	 * Final log instance for logging this class
	 */
	private final Log logger = LogFactory.getLog(ForecastMonitorAction.class);

	/**
	 * An instance of ForecastMonitorManager to be used across the application
	 */
	@Autowired
	private ForecastMonitorManager forecastMonitorManager = null;

	/**
	 * @param forecastMonitorManager
	 *            the forecastMonitorManager to set
	 */
	public void setForecastMonitorManager(
			ForecastMonitorManager forecastMonitorMngr) {
		this.forecastMonitorManager = forecastMonitorMngr;
	}

	/**
	 * Loads the flex object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flex()
			throws SwtException {
		// variable to hold error message
		String errorMessage = null;
		// variable to hold host id
		String hostId = null;
		// variable to hold user id
		String userId = null;
		// variable to hold item id
		String itemId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			logger.debug(this.getClass().getName() + " - [flex] - Entering ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.FORECAST_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			logger.debug(this.getClass().getName() + " - [flex] - Exit ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
						   + exception.getStackTrace()[0].getMethodName() + ":"
						   + exception.getStackTrace()[0].getLineNumber() + " "
						   + exception.getMessage();
			logger.error(this.getClass().getName() + " - flex(). Exception : "
						 + errorMessage);
		}
		return getView("flexobject");
	}

	/**
	 * This method is called when the response is returned to flex screen and
	 * then submitted.
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		logger.debug(this.getClass().getName()
					 + " - [unspecified] - Enter/Exit - Call to "
					 + this.getClass().getName()
					 + " - [displayForecastMonitorDetails]");
		return displayForecastMonitorDetails();
	}

	private ForecastMonitorForm forecastMonitor = null;


	public ForecastMonitorForm getForecastMonitor() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		forecastMonitor = RequestObjectMapper.getObjectFromRequest(ForecastMonitorForm.class, request, "forecastMonitor");
		return forecastMonitor;
	}

	public void setForecastMonitor(ForecastMonitorForm forecastMonitor) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("forecastMonitor", forecastMonitor);

		this.forecastMonitor = forecastMonitor;
	}

	/**
	 * This method calls the Manager class to get the Forecast monitor details
	 * and forwards the request to display it.<br>
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayForecastMonitorDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Declare OpTimer object */
		OpTimer opTimer = null;
		/* Declare SystemFormats object */
		SystemFormats format = null;
		/* Variable to hold hostId */
		String hostId = null;
		/* Variable to hold userId */
		String userId = null;
		/* Variable to hold screenId */
		String screenId = null;
		/* Declare ScreenOption object to hold forecastmonitor option properties */
		ScreenOption forecastMonitorOption = null;
		/* Declare ScreenOption object */
		ScreenOption scrOption = null;
		/* Variable to hold currency */
		String currency = null;
		/* Declare ForecastMonitorForm object */
		ForecastMonitorForm forecastMonitor = null;
		/* Declare ForecastRecord object */
		ForecastRecord collMonitorDetails = null;
		/* Variable Declaration for entity */
		Entity entity = null;
		/* Variable Declaration for autoRefreshRate */
		int autoRefreshRate = 0;
		// Variable to hold entityId
		String entityId = null;
		// HttpSession object
		HttpSession session = null;
		// User object
		User user = null;
		// Variable holds the Role Id
		String roleId = null;
		// EntityManager object
		EntityManager entityManager = null;
		// Variable to hold the templateId
		String templateId = null;
		// ScenarioData object
		ScenarioData scenarioData = null;
		// holds the collection of currency
		Collection<LabelValueBean> collCurrency = null;
		// Declare CurrencyManager object
		CurrencyManager currencyManager = null;
		/* Declare CurrencyDetailVO object */
		CurrencyDetailVO cdv = null;
		// used to iterate the currency collection
		Iterator<Currency> multiplier_Itr = null;
		// multiplier Flag variable
		boolean multiplierFlag = true;
		// variable for error message
		String message = null;
		// boolean variable to check the access flag
		boolean accessFlag = false;
		// entity collection
		Collection<LabelValueBean> collEntity = null;
		// To hold the collection of entity details
		LabelValueBean entityLabel = null;
		// variable to hold template name
		String templateName = null;
		// Declare ForecastMonitorTemplate object to hold the details
		ForecastMonitorTemplate forecastMonitorTemplate = null;
		// variable to hold currency group access
		int access = 0;
		// Declare ScreenOptionManager object
		ScreenOptionManager screenOptionManager = null;
		// Declare Currency object
		Currency ccy = null;
		// To hold the collection of currency details
		LabelValueBean currencyLabel = null;
		// Start: Code added by Bala for Mantis 1413 on 11-Oct-2011
		// - if change the entity which having no currency, the grid data
		// should
		// not be displayed
		// used to iterate the currency collection
		Iterator<LabelValueBean> itrCurrency = null;
		// To hold the currency label & value
		LabelValueBean currencyValue = null;
		// End: Code added by Bala for Mantis 1413 on 11-Oct-2011
		// - if change the entity which having no currency, the grid data
		// should
		// not be displayed
		try {
			logger.debug("Enter " + this.getClass().getName()
						 + " - [displayForecastMonitorDetails]");
			// create instance for OpTimer
			opTimer = new OpTimer();
			// start the timer
			opTimer.start(SwtConstants.OPTIMER_START_ALL);
			// get the forecastMonitorForm properties
			if(this.forecastMonitor == null) {
				this.forecastMonitor = new ForecastMonitorForm();
			}
			forecastMonitor = this.forecastMonitor;
			// get the Current System Format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Get the currency from request
			currency = request.getParameter("selectedCurrency");
			// create instance for ScenarioData
			scenarioData = new ScenarioData();
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Creating an instance of EntityManager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// get the session
			session = request.getSession();
			// Get the current user from SwtUtil
			user = SwtUtil.getCurrentUser(session);
			// Get the role Id associated to the user
			roleId = user.getRoleId();
			// Get the screen id from SwtConstants
			screenId = SwtConstants.FORECAST_MONITOR_ID;
			// Initializing the screen option instance
			scrOption = new ScreenOption();
			// Setting the hostId
			scrOption.getId().setHostId(hostId);
			// Setting the userId
			scrOption.getId().setUserId(userId);
			// Setting the screenId
			scrOption.getId().setScreenId(screenId);
			// Creating a ScreenOptionManager instance
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// set the scrOption to forecastMonitorOption
			forecastMonitorOption = screenOptionManager
					.getForecastMonitorOptions(scrOption);
			// Create a instance of entity
			entity = new Entity();
			// Set the host id in the entity
			entity.getId().setHostId(CacheManager.getInstance().getHostId());
			// Set the entity id in the entity
			entity.getId().setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));
			// Get the entity id from the request
			entityId = request.getParameter("selectedEntityId");
			// Add the entity list in request
			collEntity = putEntityListInReq(request, "true");
			// check the entity id null & set the entity id
			if (SwtUtil.isEmptyOrNull(entityId)) {
				// get the entityId from monitor option
				entityId = forecastMonitorOption.getEntity();
				// if entityId is null & get the user current entity, else check
				// the entity access
				if (SwtUtil.isEmptyOrNull(entityId)
					|| entityId.equals(SwtConstants.NO)) {
					entityId = SwtUtil.getUserCurrentEntity(request
							.getSession());
				} else {
					// get the entity access flag
					accessFlag = forecastMonitorManager.checkEntityAccess(
							hostId, roleId, entityId);
					// check the access flag & set the user currennt entity
					if (!accessFlag) {
						entityId = SwtUtil.getUserCurrentEntity(request
								.getSession());
					}
				}
				// get the entity access flag
				accessFlag = forecastMonitorManager.checkEntityAccess(hostId,
						roleId, entityId);
				// check the access flag & set the entity
				if (!accessFlag) {
					if (collEntity.size() > 1) {
						entityLabel = (LabelValueBean) collEntity.toArray()[0];
						entityId = entityLabel.getValue();
					} else {
						entityId = null;
					}
				}
			}
			// check the entityid and Re-initialize the Collection
			if (entityId == null) {
				collCurrency = new ArrayList<LabelValueBean>();
			} else {
				// check the currency multiplier and get the currency list
				if (forecastMonitorOption.getUseCurrencyMultiplier().equals(
						SwtConstants.YES)) {
					collCurrency = getCurrencyList(request, hostId, entityId,
							false);
				} else {
					collCurrency = getCurrencyList(request, hostId, entityId,
							true);
				}
			}
			// Start: Code modified by Bala for Mantis 1413 on 11-Oct-2011
			// - if change the entity which having no currency, the grid data
			// should
			// not be displayed
			// If selected currency not equal to null and set the currency
			if (!SwtUtil.isEmptyOrNull(request.getParameter("retainCurrency"))
				&& collCurrency.size() > 0) {
				// iterate the currency collection
				itrCurrency = collCurrency.iterator();
				while (itrCurrency.hasNext()) {
					// get the currency bean
					currencyValue = (LabelValueBean) itrCurrency.next();
					// if the currency parameter equals to currency label bean
					// currency
					if (request.getParameter("retainCurrency").equals(
							currencyValue.getValue())) {
						// set the currency
						currency = request.getParameter("retainCurrency");
						break;
					}
				}
			}
			// End: Code modified by Bala for Mantis 1413 on 03-Oct-2011
			// - if change the entity which having no currency, the grid data
			// should
			// not be displayed
			// check the entity & currency and set the same
			if (SwtUtil.isEmptyOrNull(currency) && entityId != null) {
				// get the currency from monitor option
				currency = forecastMonitorOption.getCurrency();
				// check the currency and set the entity
				if (SwtUtil.isEmptyOrNull(currency)
					|| currency.equals(SwtConstants.NO)) {
					if (!entityId.equals(SwtConstants.ALL_LABEL))
						entity.getId().setEntityId(entityId);
					else
						entity.getId().setEntityId(
								SwtUtil.getUserCurrentEntity(session));
					// get the entity details
					entity = entityManager.getEntityDetail(entity);
					// Setting the default reporting currency from entity
					// bean
					// to
					// screenOption bean.
					forecastMonitorOption.setCurrency(entity
							.getReprotingCurrency());
					// get the currency from monitor option
					currency = forecastMonitorOption.getCurrency();
				} else {
					// if All entity option is selected, get the access and set
					// the currency
					if (entityId.equals(SwtConstants.ALL_LABEL)) {
						entity.getId().setEntityId(
								SwtUtil.getUserCurrentEntity(session));
						// get the currency group access
						access = SwtUtil.getCcyGrpAccessType(request, hostId,
								entity.getId().getEntityId(), currency);
					} else {
						// get the currency group access
						access = SwtUtil.getCcyGrpAccessType(request, hostId,
								entityId, currency);
					}
					// If currency has access, set the currency
					if (access != 2) {
						currency = forecastMonitorOption.getCurrency();
					} else {
						// get the entity details
						entity = entityManager.getEntityDetail(entity);
						// Setting the default reporting currency from entity
						// bean
						// to
						// screenOption bean.
						forecastMonitorOption.setCurrency(entity
								.getReprotingCurrency());
						// get the currency from monitor option
						currency = forecastMonitorOption.getCurrency();
					}
				}
				// if All entity option is selected, get the access and set the
				// currency
				if (entityId.equals(SwtConstants.ALL_LABEL)) {
					entity.getId().setEntityId(
							SwtUtil.getUserCurrentEntity(session));
					// get the currency group access
					access = SwtUtil.getCcyGrpAccessType(request, hostId,
							entity.getId().getEntityId(), currency);
				} else {
					// get the currency group access
					access = SwtUtil.getCcyGrpAccessType(request, hostId,
							entityId, currency);
				}
				// If currency has access, set the currency
				if (access != 2) {
					currency = forecastMonitorOption.getCurrency();
				} else {
					// Set the entity id to the entity object
					entity.getId().setEntityId(
							SwtUtil.getUserCurrentEntity(request.getSession()));
					// get the entity details
					entity = entityManager.getEntityDetail(entity);
					// Setting the default reporting currency from entity
					// bean
					// to
					// screenOption bean.
					forecastMonitorOption.setCurrency(entity
							.getReprotingCurrency());
					// get the currency from monitor option
					currency = forecastMonitorOption.getCurrency();
					if (entityId.equals(SwtConstants.ALL_LABEL)) {
						// Set the entity id to the entity object
						entity.getId().setEntityId(
								SwtUtil.getUserCurrentEntity(session));
						// get the currency group access
						access = SwtUtil.getCcyGrpAccessType(request, hostId,
								entity.getId().getEntityId(), currency);
					} else {
						// get the currency group access
						access = SwtUtil.getCcyGrpAccessType(request, hostId,
								entityId, currency);
					}
					// If currency has access, set the currency
					if (access != 2) {
						currency = forecastMonitorOption.getCurrency();
					} else {
						// check the currency collection, and set the currency
						if (collCurrency.size() > 0) {
							currencyLabel = (LabelValueBean) collCurrency
									.toArray()[0];
							currency = currencyLabel.getValue();
						} else {
							currency = null;
						}
					}
				}
			}
			// Add the currency in form
			forecastMonitor.setCurrency(currency);
			// Add the entity id in form
			forecastMonitor.setEntityId(entityId);
			// get the currencyManager object
			currencyManager = (CurrencyManager) SwtUtil
					.getBean("currencyManager");
			if (entityId.equals(SwtConstants.ALL_LABEL))
				// get the currency details
				cdv = currencyManager.getCurrencyDetailList(SwtUtil
						.getUserCurrentEntity(session), hostId, currency);
			else
				// get the currency details
				cdv = currencyManager.getCurrencyDetailList(entityId, hostId,
						currency);
			// iterate the currency list details
			multiplier_Itr = cdv.getCurrencyListDetails().iterator();
			if (multiplier_Itr.hasNext()) {
				// get the currency details
				ccy = (Currency) multiplier_Itr.next();
			}
			// check the currency and set the multiplier flag
			if (ccy != null
				&& !SwtUtil.isEmptyOrNull(ccy.getMultiplier())
				&& !ccy.getMultiplier().equals(SwtConstants.NO)
				&& forecastMonitorOption.getUseCurrencyMultiplier().equals(
					SwtConstants.YES)) {
				multiplierFlag = false;
			}
			// get the forecast monitor details
			collMonitorDetails = forecastMonitorManager
					.getForecastMonitorDetails(currency, entityId, userId,
							format, opTimer, multiplierFlag);
			// Check the job flag status, set to request
			if (!SwtUtil.isEmptyOrNull(collMonitorDetails.getJobFlagStatus())
				&& collMonitorDetails.getJobFlagStatus().equals(
					"jobFlagStatusTrue")) {
				request.setAttribute("jobFlagStatus", SwtConstants.YES);
				collMonitorDetails.setJobFlagStatus("");
			}
			// set the scenarioData values
			scenarioData.getId().setHostId(hostId);
			scenarioData.getId().setUserId(userId);
			scenarioData.getId().setEntityId(forecastMonitor.getEntityId());
			scenarioData.getId().setCurrencyCode(forecastMonitor.getCurrency());
			// get the templateId
			templateId = forecastMonitorManager.getTemplateId(scenarioData);
			// set the template id to request
			request.setAttribute("templateId", templateId);
			// set the forecast monitor records
			forecastMonitor.setForecastMonitorRecords(collMonitorDetails
					.getForecastMonitorRecords());
			request.setAttribute("recordCount", collMonitorDetails
					.getForecastMonitorRecords().size());
			// set the grand total records
			forecastMonitor.setGrandTotalRecords(collMonitorDetails
					.getGrandTotalRecords());
			// set the forecastMonitor to form
			if(this.forecastMonitor == null) {
				this.forecastMonitor = new ForecastMonitorForm();
			}
			forecastMonitor = this.forecastMonitor;
			// Get the auto refresh rate
			autoRefreshRate = Integer.parseInt(forecastMonitorOption
					.getPropertyValue());
			/* Set Last Reference Time in request. */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));

			// set the autoRefreshRate attribute values
			request.setAttribute("autoRefreshRate", autoRefreshRate);
			// set the menuaccess value
			request.setAttribute("menuAccess", request
					.getParameter("menuAccess"));
			// setting reply status as true in request
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			// setting the reply message in request
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			// Code Added by Balaji for mantis 1991 on 13-07-2012
			if (SwtUtil.isEmptyOrNull(entityId)
				|| entityId.equals(SwtConstants.ALL_LABEL))
				/* Set Last Reference Time in request for default entity */
				request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(
						request, SwtUtil.getUserCurrentEntity(request
								.getSession())));
			else
				/* Set Last Reference Time in request for selected entity */
				request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(
						request, entityId));
			// create instance for ForecastMonitorTemplate
			forecastMonitorTemplate = new ForecastMonitorTemplate();
			// set the host id to forecastMonitorTemplate object
			forecastMonitorTemplate.getId().setHostId(hostId);
			// set the template id to forecastMonitorTemplate object
			forecastMonitorTemplate.getId().setTemplateId(templateId);
			// get the forecastMonitorTemplate details
			forecastMonitorTemplate = forecastMonitorManager
					.getForecastMonitorTemplateDetails(forecastMonitorTemplate);
			// get the template name
			templateName = forecastMonitorTemplate.getTemplateName();
			// set the templateName attribute to request
			request.setAttribute("templateName", templateName);
			setForecastMonitor(forecastMonitor);
			request.setAttribute("forecastMonitor", forecastMonitor);
			// set the column width
			this.bindColumnWidthInRequest(request);
			// stop the timer
			opTimer.stop(SwtConstants.OPTIMER_STOP_ALL);
			request.setAttribute("opTimes", opTimer.getOpTimes());
			logger.debug("Exit " + this.getClass().getName()
						 + " - [displayForecastMonitorDetails]");
		} catch (SwtException e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			logger.error(this.getClass().getName()
						 + " - [displayForecastMonitorDetails] - Exception -"
						 + message);
			request.setAttribute("balanceDate", new ArrayList<BalanceDateTO>());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(e, request, "");
			return getView("dataerror");
		} catch (Exception e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			logger.error(this.getClass().getName()
						 + " - [displayForecastMonitorDetails] - Exception -"
						 + message);
			request.setAttribute("balanceDate", new ArrayList<BalanceDateTO>());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayForecastMonitorDetails",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// null the objects created already
			hostId = null;
			userId = null;
			screenId = null;
			forecastMonitorOption = null;
			currency = null;
			entity = null;
			collMonitorDetails = null;
			multiplier_Itr = null;
			currencyManager = null;
			cdv = null;
			collCurrency = null;
			scenarioData = null;
			entityManager = null;
			scrOption = null;
		}
		return getView("success");
	}

	private ScreenOption forecastMonitorOptions = null;

	/**
	 * This method is used to display existing options for the forecast
	 * monitor(if any) Click on the "Options" button from the forecast monitor
	 * screen
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String displayForecastMonitorOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String variable to hold host id
		String hostId = null;
		// String variable to hold user id
		String userId = null;
		// ScreenOption object
		ScreenOption screenOption = null;
		// session object
		HttpSession session = null;
		// setting the currency multiplier

		Collection<EntityUserAccess> coll = null;
		// Entity object
		Entity entity = null;
		// EntityManager object
		EntityManager entityManager = null;
		// ScreenOption object
		ScreenOption scOption = null;
		// Collection to hold entity user access
		Collection<LabelValueBean> entityAccess = null;
		// Collection to hold user template
		Collection<UserTemplate> templateColl = null;
		// Collection to hold user bucket
		Collection<UserBuckets> bucketColl = null;
		// hold the bucket label
		LabelValueBean endLabel = null;
		// collection to hold the bucket object
		Collection<LabelValueBean> endTo = null;
		// int variable
		int k = 3;
		try {
			logger.debug(this.getClass().getName()
						 + " - [displayForecastMonitorOptions] - Entry");
			// Instantiating Entity object
			entity = new Entity();
			// Instantiate the ArrayList
			endTo = new ArrayList<LabelValueBean>();
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Set the values
			if(this.forecastMonitorOptions == null) {
				this.forecastMonitorOptions = new ScreenOption();
			}
			screenOption = this.forecastMonitorOptions;


			// Initializing the screen option instance
			screenOption = new ScreenOption();
			// Setting the hostId
			screenOption.getId().setHostId(hostId);
			// Setting the userId
			screenOption.getId().setUserId(userId);
			// Setting the screenId
			screenOption.getId().setScreenId(SwtConstants.FORECAST_MONITOR_ID);
			// setting the reporting currency from the CacheManager
			CacheManager cacheManagerInst = CacheManager.getInstance();
			// set the report currency list
			screenOption
					.setReportCurrencyList(cacheManagerInst.getCurrencies());
			// Getting the session from the request
			session = request.getSession();
			/* Collects the list of entity for the user */
			coll = SwtUtil.getUserEntityAccessList(session);
			/* Collects the label value bean for entity name and entity id */
			entityAccess = SwtUtil.convertEntityAcessCollectionLVL(coll,
					session);
			// Creating a ScreenOptionManager instance
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Creating an instance of EntityManager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			// Setting entity Id in Entity bean
			entity.getId().setEntityId(SwtUtil.getUserCurrentEntity(session));
			// Setting host Id in Entity bean
			entity.getId().setHostId(hostId);
			// create instance for ScreenOption
			scOption = new ScreenOption();
			// Get the forecast monitor option values
			scOption = screenOptionManager
					.getForecastMonitorOptions(screenOption);
			if (scOption.getEntity().equals(SwtConstants.NO)) {
				scOption.setEntity(SwtUtil.getUserCurrentEntity(request
						.getSession()));
			}
			if (scOption.getCurrency().endsWith(SwtConstants.NO)) {
				entity = entityManager.getEntityDetail(entity);
				// Setting the default reporting currency from entity bean to
				// screenOption bean.
				scOption.setCurrency(entity.getReprotingCurrency());
			}
			while (k < 31) {
				if (k == 3) {
					endLabel = new LabelValueBean("T", "");
					endTo.add(endLabel);
					endLabel = new LabelValueBean("T+1", "");
					endTo.add(endLabel);
				}
				endLabel = new LabelValueBean(String.valueOf(k), "");
				endTo.add(endLabel);
				k++;
			}
			// set the entity
			if (!SwtUtil
					.isEmptyOrNull(request.getParameter("selectedEntityId"))) {
				entity.getId().setEntityId(
						request.getParameter("selectedEntityId"));
				scOption.setEntity(entity.getId().getEntityId());
			}
			// set the user bucket collection to request
			request.setAttribute("endTo", endTo);
			// get the currency list
			/*
			   Modified by KaisBS for mantis 2040: Forecast Monitor Option,
			   Currency drop down mismatches for the selected entity
			*/
			getCurrencyList(request, hostId, scOption.getEntity(), true);
			// set the reporting Currency collection to request
			request.setAttribute("reportingCurr", entityAccess);
			templateColl = new ArrayList<UserTemplate>();
			templateColl = forecastMonitorManager.getUserTemplateList(hostId,
					userId);
			// set the user template collection to request
			request.setAttribute("userTemplate", templateColl);
			bucketColl = new ArrayList<UserBuckets>();
			bucketColl = forecastMonitorManager.getUserBucketList(hostId,
					userId);

			// set the user bucket collection to request
			request.setAttribute("userBucket", bucketColl);
			// Setting the entity monitor options to form
//			dyForm.set("forecastMonitorOptions", scOption);
			setForecastMonitorOptions(scOption);
			request.setAttribute("forecastMonitorOptions", scOption);

			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
						 + " - [displayForecastMonitorOptions] - Exit");
		} catch (SwtException e) {
			logger.error(this.getClass().getName()
						 + " - [displayForecastMonitorOptions] - SwtException -"
						 + e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - [displayForecastMonitorOptions] - GenericException -"
						 + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayForecastMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// Cleaning the Unreferenced objects
			hostId = null;
			userId = null;
			entityManager = null;
			entity = null;
			screenOption = null;
			// closing the session
			if (session != null) {
				session = null;
			}
			// Nullifies the Non-referenced Collection object
			if (coll != null) {
				coll = null;
			}
		}
		return getView("options");
	}

	private AssumptionData forecastAssumption = null;
	public ScreenOption getForecastMonitorOptions() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		forecastMonitorOptions = RequestObjectMapper.getObjectFromRequest(ScreenOption.class, request,"forecastMonitorOptions");
		return forecastMonitorOptions;
	}

	public void setForecastMonitorOptions(ScreenOption forecastMonitorOptions) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("forecastMonitorOptions", forecastMonitorOptions);
		this.forecastMonitorOptions = forecastMonitorOptions;
	}

	public AssumptionData getForecastAssumption() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		forecastAssumption = RequestObjectMapper.getObjectFromRequest(AssumptionData.class, request,"forecastAssumption");
		return forecastAssumption;
	}


	public void setForecastAssumption(AssumptionData forecastAssumption) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("forecastAssumption", forecastAssumption);
		this.forecastAssumption = forecastAssumption;
	}


	/**
	 * This method is used to display assumption for the forecast monitor
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String displayAssumption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String variable to hold host id
		String hostId = null;
		// Assumption list details
		Collection<AssumptionData> assumptionList = null;
		// Form object
		AssumptionData forecastAssumption = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		// Calender object
		Calendar calDate = null;
		/* Variable Declaration for SystemFormats */
		SystemFormats format = null;
		// used to iterate the AssumptionData collection
		Iterator<AssumptionData> itr = null;
		// Declare AssumptionData object
		AssumptionData assumption = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [displayAssumption] - Entry");
			calDate = Calendar.getInstance();
			// get the Current System Format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			assumptionList = new ArrayList<AssumptionData>();
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Set the values
			// get the entityMonitor for object
			forecastAssumption = getForecastAssumption();
			// Set the date as calendar date
			calDate.setTime(SwtUtil.parseDate(forecastAssumption
					.getValueDateAsString(), format.getDateFormatValue()));
			// set the host id
			forecastAssumption.setHostId(hostId);
			// set the value date
			forecastAssumption.setValueDate(calDate.getTime());
			// get the assumption list
			assumptionList = forecastMonitorManager
					.getForecastAssumption(forecastAssumption);
			// iterate the collection
			itr = assumptionList.iterator();
			// get the system format
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			while (itr.hasNext()) {
				// set the assumption data values
				assumption = (AssumptionData) itr.next();
				assumption.setValueDateAsString(SwtUtil.formatDate(assumption
						.getValueDate(), sysformat.getDateFormatValue()));
				assumption.setAssumptionsAmountAsString(SwtUtil.formatCurrency(
						null, assumption.getAssumptionsAmount()));
			}
			// set the assumption List
			forecastAssumption.setAssumptionList(assumptionList);
			setForecastAssumption(forecastAssumption);
			request.setAttribute("forecastAssumption", this.forecastAssumption);

			// set the request attribute
			request.setAttribute("assumptionList", assumptionList);
			request.setAttribute("currencyFormat", sysformat
					.getCurrencyFormat());
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
						 + " - [displayAssumption] - Exit");
		} catch (SwtException e) {
			e.printStackTrace();
			logger.error(this.getClass().getName()
						 + " - [displayAssumption] - SwtException -"
						 + e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - [displayAssumption] - GenericException -"
						 + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "displayAssumption", ForecastMonitorAction.class),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// Cleaning the Unreferenced objects
			hostId = null;
			assumption = null;
			itr = null;
			format = null;
			calDate = null;
		}
		return getView("flexOpenAssumption");
	}

	private UserTemplate forecastTemplateOptions = null;
	public UserTemplate getForecastTemplateOptions() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		forecastTemplateOptions = RequestObjectMapper.getObjectFromRequest(UserTemplate.class, request,"forecastTemplateOptions");
		return forecastTemplateOptions;
	}


	public void setForecastTemplateOptions(UserTemplate forecastTemplateOptions) {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("forecastTemplateOptions", forecastTemplateOptions);
		this.forecastTemplateOptions = forecastTemplateOptions;
	}





	/**
	 * This method is used to display user template options
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */
	public String displayUserTemplateOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String variable to hold host id
		String hostId = null;
		// String variable to hold user id
		String userId = null;
		// UserTemplate object
		UserTemplate userTemplate = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [displayUserTemplateOptions] - Entry");
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Set the values
			userTemplate = getForecastTemplateOptions();

			// Setting the hostId
			userTemplate.getId().setHostId(hostId);
			// Setting the userId
			userTemplate.getId().setUserId(userId);
			if (!SwtUtil.isEmptyOrNull(userTemplate.getId().getEntityId())) {
				// setting currency list
				getCurrencyList(request, hostId, userTemplate.getId()
						.getEntityId(), true);
				userTemplate.setTemplateId(userTemplate.getTemplateId());
			} else {
				// setting currency list
				getCurrencyList(request, hostId, "*Default*", true);
				userTemplate.getId().setEntityId("*Default*");
				userTemplate.setTemplateId("*DEFAULT*");
			}
			// set the userId
			request.setAttribute("userId", userId);
			// get the template list
			getTemplateList(request, hostId, userId);
			// set entity list
			putEntityListInReq(request, "false");
			setForecastTemplateOptions(userTemplate);
			request.setAttribute("forecastTemplateOptions", userTemplate);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
						 + " - [displayUserTemplateOptions] - Exit");
		} catch (SwtException e) {
			logger.error(this.getClass().getName()
						 + " - [displayUserTemplateOptions] - SwtException -"
						 + e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - [displayUserTemplateOptions] - GenericException -"
						 + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayEntityMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// Cleaning the Unreferenced objects
			hostId = null;
		}
		return getView("userTemplateoptions");
	}

	/**
	 * This method is used to display existing options for the entity monitor(if
	 * any) Click on the "Options" button from the entity monitor screen
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 *
	 * @return ActionForward
	 *
	 * @throws SwtException
	 */


	public String displayAddAssumption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String variable to hold host id
		String hostId = null;
		// ScreenOption object
		AssumptionData assumptionData = null;
		// variable to hold currency name
		String currencyName = null;
		// Declare CurrencyInterestManager object
		CurrencyInterestManager currencyInterestManager = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [displayAddAssumption] - Entry");
			// Get host id from the CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Set the values
			assumptionData = getForecastAssumption();
			// create instance for AssumptionData
			assumptionData = new AssumptionData();
			// Setting the hostId
			assumptionData.setHostId(hostId);
			// set the currencyCode in request
			request.setAttribute("currencyCode", request
					.getParameter("selectedCurrencyCode"));
			// get the currencyInterestManager object from the bean
			currencyInterestManager = (CurrencyInterestManager) SwtUtil
					.getBean("currencyInterestManager");
			if (!SwtUtil.isEmptyOrNull(request
					.getParameter("selectedCurrencyCode"))) {
				currencyName = currencyInterestManager.getCurrencyName(request
						.getParameter("selectedCurrencyCode"));
			}
			// set the currencyName in request
			request.setAttribute("currencyName", currencyName);
			if (request.getParameter("selectedEntityId").equals("All")) {
				// assumptionData.setEntityId("*DEFAULT*");
			} else {
				assumptionData.setEntityId(request
						.getParameter("selectedEntityId"));
			}
			// set the entity list
			putEntityListInReq(request, "no");
			// Setting the forecast assumption to form
			setForecastAssumption(assumptionData);
			request.setAttribute("forecastAssumption", assumptionData);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);
			logger.debug(this.getClass().getName()
						 + " - [displayAddAssumption] - Exit");
		} catch (SwtException e) {
			logger.error(this.getClass().getName()
						 + " - [displayAddAssumption] - SwtException -"
						 + e.getMessage());
			SwtUtil.logException(e, request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - [displayAddAssumption] - GenericException -"
						 + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayEntityMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} finally {
			// Cleaning the Unreferenced objects
			hostId = null;
			currencyInterestManager = null;
		}
		return getView("flexAddOpenAssumption");
	}

	/**
	 * This method is used to save the options or preferences in entity monitor
	 * screen. From entity monitor screen, click on "Options" set/reset the
	 * preference and "Save". This method is called on the save action.
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveForecastMonitorOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// ScreenOption object
		ScreenOption screenOption = null;
		// String object to hold hostId
		String hostId = null;
		// String object to hold userId
		String userId = null;
		// DynaValidatorForm object
		StringBuffer sb = null;
		// ScreenOptionManager object
		ScreenOptionManager screenOptionManager = null;
		// editupdate
		String editupdate = null;
		// editupdate
		String bucketupdate = null;
		// Update list
		String[] editpksToUpdate;
		// Update list
		String[] bucketpksToUpdate;
		// saveorupdate values
		String[] editpkValues;
		// saveorupdate values
		String[] bucketpkValues;
		// Declare UserTemplate object
		UserTemplate userTemplate = null;
		// Declare UserBuckets object
		UserBuckets userBucket = null;
		// good keys
		ArrayList<String[]> goodKeys = null;
		// bad keys
		ArrayList<String[]> badKeys = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [saveFOrecastMonitorOptions] - Entry");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// Instantiate the ArrayList
			goodKeys = new ArrayList<String[]>();
			// Instantiate the ArrayList
			badKeys = new ArrayList<String[]>();
			// Object for DynaValidatorForm
			screenOption = getForecastMonitorOptions();
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// create instance for UserTemplate
			userTemplate = new UserTemplate();
			// create instance for UserBuckets
			userBucket = new UserBuckets();
			// checks whether editupdate is null or not
			if (!SwtUtil.isEmptyOrNull(editupdate = request
					.getParameter("updateTemplate"))) {
				editpksToUpdate = editupdate.split(",");
				for (int i = 0; i < editpksToUpdate.length; i++) {
					editpkValues = editpksToUpdate[i].split("_D_");
					try {
						// set the userTemplate values
						userTemplate.getId().setHostId(hostId);
						userTemplate.getId().setEntityId(editpkValues[2]);
						userTemplate.getId().setCurrencyCode(editpkValues[0]);
						userTemplate.setTemplateId(editpkValues[1]);
						userTemplate.getId().setUserId(userId);
						if (request.getParameter(editpksToUpdate[i]).equals(
								"delete")) {
							// calling the method to save personal currency list
							forecastMonitorManager
									.deleteForecastUserTemplate(userTemplate);
						}
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
									   + " - Exception Catched in [saveForecastMonitorOptions] method - "
									   + e.getMessage());
						request.setAttribute("reply_status_ok",
								SwtConstants.STR_FALSE);
						request.setAttribute("reply_message", e.getMessage());
						request.setAttribute("reply_location", e
																	   .getStackTrace()[0].getClassName()
															   + "."
															   + e.getStackTrace()[0].getMethodName()
															   + ":" + e.getStackTrace()[0].getLineNumber());
					}
				}
				for (int i = 0; i < editpksToUpdate.length; i++) {
					editpkValues = editpksToUpdate[i].split("_D_");
					try {
						// set the userTemplate values
						userTemplate.getId().setHostId(hostId);
						userTemplate.getId().setEntityId(editpkValues[2]);
						userTemplate.getId().setCurrencyCode(editpkValues[0]);
						userTemplate.setTemplateId(editpkValues[1]);
						userTemplate.getId().setUserId(userId);
						if (request.getParameter(editpksToUpdate[i]).equals(
								"save")) {
							// calling the method to save personal currency list
							forecastMonitorManager.saveUserTemplate(
									userTemplate, false);
						} else if (!request.getParameter(editpksToUpdate[i])
								.equals("delete")) {
							// calling the method to save personal currency list
							forecastMonitorManager.saveUserTemplate(
									userTemplate, true);
						}
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
									   + " - Exception Catched in [saveForecastMonitorOptions] method - "
									   + e.getMessage());
						request.setAttribute("reply_status_ok",
								SwtConstants.STR_FALSE);
						request.setAttribute("reply_message", e.getMessage());
						request.setAttribute("reply_location", e
																	   .getStackTrace()[0].getClassName()
															   + "."
															   + e.getStackTrace()[0].getMethodName()
															   + ":" + e.getStackTrace()[0].getLineNumber());
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys) + " | FAIL: "
														  + formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}

			// checks whether editupdate is null or not
			if (!SwtUtil.isEmptyOrNull(bucketupdate = request.getParameter(
					"updateBucket").trim())) {

				bucketpksToUpdate = bucketupdate.split(",");
				// calling the method to save user bucket
				for (int i = 0; i < bucketpksToUpdate.length; i++) {
					bucketpkValues = bucketpksToUpdate[i].split("_");
					try {
						// set the userBucket values
						userBucket.getId().setHostId(hostId);
						userBucket.getId().setUserId(userId);
						userBucket.getId().setBucketId(
								Integer.parseInt(bucketpkValues[0]));
						userBucket.setDaysTo(bucketpkValues[1]);
						if (bucketpkValues[2].equals("true")) {
							userBucket.setBucketState("Y");
						} else {
							userBucket.setBucketState("N");
						}
						if (i == 0)
							forecastMonitorManager.deleteForecastBucket(
									userBucket, true);
						// save user bucket
						forecastMonitorManager.saveUserBucket(userBucket, true);
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
									   + " - Exception Catched in [saveForecastMonitorOptions] method - "
									   + e.getMessage());
						request.setAttribute("reply_status_ok",
								SwtConstants.STR_FALSE);
						request.setAttribute("reply_message", e.getMessage());
						request.setAttribute("reply_location", e
																	   .getStackTrace()[0].getClassName()
															   + "."
															   + e.getStackTrace()[0].getMethodName()
															   + ":" + e.getStackTrace()[0].getLineNumber());
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys) + " | FAIL: "
														  + formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}

			// Set the host id in the ScreenOption form
			screenOption.getId().setHostId(hostId);
			// Set the user id in ScreenOption form
			screenOption.getId().setUserId(userId);
			// Set the screen id in ScreenOption form
			screenOption.getId().setScreenId(SwtConstants.FORECAST_MONITOR_ID);
			// Creating a ScreenOptionManager instance
			screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Save the entity monitor option changes, by calling the
			// EntityMonitorNewManagerImpl
			screenOptionManager.saveForecastMonitorOptions(screenOption);
			// Set the request attribute to refresh the entity monitor screen
			// set the refresh flag
			request.setAttribute("parentFormRefresh", SwtConstants.YES);
			setForecastMonitorOptions(screenOption);
			request.setAttribute("forecastMonitorOptions", screenOption);
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<ForecastMonitor>");
			sb.append("<request_reply><status_ok>true"
					  + "</status_ok><message>true"
					  + "</message></request_reply>");
			sb.append("</ForecastMonitor>");
			// Setting the success message in response
			response.setContentType("text/xml");
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
						 + " - [saveForecastMonitorOptions] - Exit");
			return null;
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
						 + " - [saveForecastMonitorOptions] - SwtException -"
						 + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return getView("success");
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - [saveForecastMonitorOptions] - GenericException -"
						 + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveForecastMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			return getView("fail");
		} finally {
			try {
				// Cleaning the Unreferenced objects
				screenOption = null;
				hostId = null;
				userId = null;
				screenOptionManager = null;
				bucketupdate = null;
				editupdate = null;
				userTemplate = null;
				userBucket = null;
				// Nullifying the Non-referenced StringBuffer object
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in cleaning the objects. Cause : "
							 + ignore.getMessage());
			}
		}
	}

	/**
	 * This method is used to save the user template options
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveUserTemplateOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// ScreenOption object
		UserTemplate userTemplate = null;
		// String object to hold hostId
		String hostId = null;
		// String object to hold userId
		String userId = null;
		// StringBuffer object
		StringBuffer sb = null;

		try {
			logger.debug(this.getClass().getName()
						 + " - [saveUserTemplateOptions] - Entry");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// Object for DynaValidatorForm
//			dyForm = (DynaValidatorForm) form;
//			// Getting the forecast TemplateOptions
//			userTemplate = (UserTemplate) dyForm.get("forecastTemplateOptions");

			UserTemplate forecastTemplateOptions = getForecastTemplateOptions();
			userTemplate = forecastTemplateOptions;

			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Set the host id in the userTemplate
			userTemplate.getId().setHostId(hostId);
			// Set the user id in userTemplate
			userTemplate.getId().setUserId(userId);
			// save the user template
			forecastMonitorManager.saveUserTemplate(userTemplate, false);
			// set the parent Form Refresh
			request.setAttribute("parentFormRefresh", SwtConstants.YES);
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<ForecastMonitor>");
			sb.append("<request_reply><status_ok>true"
					  + "</status_ok><message>true"
					  + "</message></request_reply>");
			sb.append("</ForecastMonitor>");
			// Setting the success message in response
			response.setContentType("text/xml");
			setForecastTemplateOptions(userTemplate);
			request.setAttribute("forecastTemplateOptions", userTemplate);

			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
						 + " - [saveUserTemplateOptions] - Exit");
			return null;
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
						 + " - [saveUserTemplateOptions] - SwtException -"
						 + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return getView("success");
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - [saveUserTemplateOptions] - GenericException -"
						 + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveEntityMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			return getView("fail");
		} finally {
			try {
				// Cleaning the Unreferenced objects
				hostId = null;
				userId = null;
				userTemplate = null;
				// Nullifying the Non-referenced StringBuffer object
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in cleaning the objects. Cause : "
							 + ignore.getMessage());
			}
		}
	}

	/**
	 * This method is used to save the forecast assumptions
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveForecastAssumption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// AssumptionData object
		AssumptionData assumptionData = null;
		// String object to hold hostId
		String hostId = null;
		// StringBuffer object
		StringBuffer sb = null;
		// Date object
		Date assumptionDate = null;
		/* Variable Declaration for SystemFormats */
		SystemFormats format = null;
		// variable amount
		String amount = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [saveForecastAssumption] - Entry");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// get the Current System Format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Getting the forecastAssumption bean object from DynaValidatorForm
			AssumptionData forecastAssumption = getForecastAssumption();
			assumptionData = forecastAssumption;
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Set the host id in the assumptionData
			assumptionData.setHostId(hostId);
			// get the amount
			amount = request.getParameter("assumptionsAmount");
			if (!SwtUtil.isEmptyOrNull(assumptionData.getValueDateAsString())) {
				assumptionDate = SwtUtil.parseDate(assumptionData
						.getValueDateAsString(), format.getDateFormatValue());
				assumptionData.setValueDate(assumptionDate);
			}
			// set the amount
			assumptionData
					.setAssumptionsAmount(SwtUtil.isEmptyOrNull(amount) ? new BigDecimal(
							0.0)
							: SwtUtil.parseCurrencyBig(amount, format
							.getCurrencyFormat()));
			forecastMonitorManager.saveForecastAssumption(assumptionData);
			// Set the request attribute to refresh the entity monitor screen
			// set the refresh flag
			request.setAttribute("parentFormRefresh", SwtConstants.YES);
			// If you need to set it back to the form
			setForecastAssumption(assumptionData);
			request.setAttribute("forecastAssumption", assumptionData);
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<ForecastMonitor>");
			sb.append("<request_reply><status_ok>true"
					  + "</status_ok><message>true"
					  + "</message></request_reply>");
			sb.append("</ForecastMonitor>");
			// Setting the success message in response
			response.setContentType("text/xml");
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
						 + " - [saveForecastAssumption] - Exit");
			return null;
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
						 + " - [saveForecastAssumption] - SwtException -"
						 + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return getView("success");
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - [saveForecastAssumption] - GenericException -"
						 + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveEntityMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			return getView("fail");
		} finally {
			try {
				// Cleaning the Unreferenced objects
				hostId = null;
				assumptionData = null;
				assumptionDate = null;
				// Nullifying the Non-referenced StringBuffer object
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in cleaning the objects. Cause : "
							 + ignore.getMessage());
			}
		}
	}

	/**
	 * This method is used to delete the Forecast assumption
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteForecastAssumption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// AssumptionData object
		AssumptionData assumptionData = null;
		// String object to hold hostId
		String hostId = null;
		// StringBuffer object
		StringBuffer sb = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [deleteForecastAssumption] - Entry");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// Equivalent code using the new structure
			AssumptionData forecastAssumption = getForecastAssumption();
			assumptionData = forecastAssumption;
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Set the host id in the ScreenOption form
			assumptionData.setHostId(hostId);
			// delete the Forecast Assumption
			forecastMonitorManager.deleteForecastAssumption(assumptionData);
			// set the parentFormRefresh to request
			request.setAttribute("parentFormRefresh", SwtConstants.YES);
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<ForecastMonitor>");
			sb.append("<request_reply><status_ok>true"
					  + "</status_ok><message>true"
					  + "</message></request_reply>");
			sb.append("</ForecastMonitor>");
			// Setting the success message in response
			// If you need to set it back to the form
			setForecastAssumption(assumptionData);
			request.setAttribute("forecastAssumption", assumptionData);
			response.setContentType("text/xml");
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
						 + " - [deleteForecastAssumption] - Exit");
			return null;
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
						 + " - [deleteForecastAssumption] - SwtException -"
						 + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return getView("success");
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - [deleteForecastAssumption] - GenericException -"
						 + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveEntityMonitorOptions",
					ForecastMonitorAction.class), request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			return getView("fail");
		} finally {
			try {
				// Cleaning the Unreferenced objects
				hostId = null;
				assumptionData = null;
				// Nullifying the Non-referenced StringBuffer object
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger.error("Error in cleaning the objects. Cause : "
							 + ignore.getMessage());
			}
		}
	}

	/**
	 * This method is used to save the forecast scenarios
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("deprecation")
	public String saveForecastScenario() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// editupdate
		String editupdate = null;
		// Update list
		String[] editpksToUpdate;
		// saveorupdate values
		String[] editpkValues;
		// PersonalCcy object
		ScenarioData scenarioData = null;
		// good keys
		ArrayList<String[]> goodKeys = null;
		// bad keys
		ArrayList<String[]> badKeys = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// StringBuffer object
		StringBuffer sb = null;
		/* Variable Declaration for SystemFormats */
		SystemFormats format = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [saveForecastScenario] - Entering");
			// create instance for StringBuffer
			sb = new StringBuffer();
			// Instantiate ScenarioData
			scenarioData = new ScenarioData();
			// Instantiate the ArrayList
			goodKeys = new ArrayList<String[]>();
			// Instantiate the ArrayList
			badKeys = new ArrayList<String[]>();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the Current System Format
			format = SwtUtil.getCurrentSystemFormats(request.getSession());
			// checks whether editupdate is null or not
			if (!SwtUtil.isEmptyOrNull(editupdate = request
					.getParameter("editupdate"))) {
				editpksToUpdate = editupdate.split(",");
				for (int i = 0; i < editpksToUpdate.length; i++) {
					editpkValues = editpksToUpdate[i].split("_");
					try {
						// set the scenario amount
						scenarioData
								.setScenariosAmount(SwtUtil
										.isEmptyOrNull(request
												.getParameter(editpkValues[0])) ? new BigDecimal(
										0.0)
										: SwtUtil.parseCurrencyBig(request
												.getParameter(editpkValues[0]),
										format.getCurrencyFormat()));
						// Start: code modified by Bala for Mantis 1413 on
						// 09-Sep-2011 - While entering the scenario data, it
						// reflects zero after refresh
						scenarioData.getId().setValueDate(
								SwtUtil.parseDate(editpksToUpdate[i], format
										.getDateFormatValue()));
						// End: code modified by Bala for Mantis 1413 on
						// 09-Sep-2011 - While entering the scenario data, it
						// reflects zero after refresh
						scenarioData.getId().setHostId(hostId);
						scenarioData.getId().setUserId(userId);
						scenarioData.getId().setEntityId(
								request.getParameter("entity"));
						scenarioData.getId().setCurrencyCode(
								request.getParameter("currency"));
						scenarioData.setUpdateDate(SwtUtil
								.getSystemDatewithTime());
						// calling the method to save scenario data
						forecastMonitorManager.saveScenarioData(scenarioData);
					} catch (Exception e) {
						logger
								.error(this.getClass().getName()
									   + " - Exception Catched in [saveForecastScenario] method - "
									   + e.getMessage());
						request.setAttribute("reply_status_ok",
								SwtConstants.STR_FALSE);
						request.setAttribute("reply_message", e.getMessage());
						request.setAttribute("reply_location", e
																	   .getStackTrace()[0].getClassName()
															   + "."
															   + e.getStackTrace()[0].getMethodName()
															   + ":" + e.getStackTrace()[0].getLineNumber());
					}
				}
				// Sets the error message to be displayed else request was good
				if (badKeys.size() > 0) {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys) + " | FAIL: "
														  + formatKeyList(badKeys));
				} else {
					request.setAttribute("reply_status_ok",
							SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "SUCCESS: "
														  + formatKeyList(goodKeys));
				}
			} else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message",
						SwtConstants.UPDATE_PARAM_NOT_SENT);
			}
			// Appending the success message in the StringBuffer
			sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?> ");
			sb.append("<ForecastScenario>");
			sb.append("<request_reply><status_ok>true"
					  + "</status_ok><message>true"
					  + "</message></request_reply>");
			sb.append("</ForecastScenario>");
			// Setting the success message in the response
			response.setContentType("text/xml");
			response.getOutputStream().println(sb.toString());
			logger.debug(this.getClass().getName()
						 + " - [saveForecastScenario] - Exiting");
		} catch (SwtException swtexp) {
			logger.error(this.getClass().getName()
						 + " - [saveForecastScenario] - SwtException -"
						 + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(swtexp, request, "");
			return getView("success");
		} catch (Exception e) {
			logger
					.error(this.getClass().getName()
						   + " - Exception Catched in [saveForecastScenario] method - "
						   + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "saveForecastScenario", ForecastMonitorAction.class),
					request, "");
			return getView("dataerror");
		} finally {
			try {
				// Cleaning up unused objects
				editupdate = null;
				hostId = null;
				userId = null;
				goodKeys = null;
				badKeys = null;
				format = null;
				if (sb != null) {
					sb = null;
				}
			} catch (Exception ignore) {
				logger
						.error("Error occured in cleaning up unused objects. Cause : "
							   + ignore.getMessage());
			}
		}
		return null;
	}

	/**
	 * This method loads the flexOption object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexOption()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold hostId
		String hostId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold itemId
		String itemId = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [flexOption] - Begins ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.FORECAST_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);

			logger.debug(this.getClass().getName() + " - [flexOption] - Ends ");
		} catch (Exception exception) {
			String errorMessage = exception.getStackTrace()[0].getClassName()
								  + "." + exception.getStackTrace()[0].getMethodName() + ":"
								  + exception.getStackTrace()[0].getLineNumber() + " "
								  + exception.getMessage();
			logger.error(this.getClass().getName()
						 + " - flexOption(). Exception : " + errorMessage);

		}
		return getView("flexobjectOption");
	}

	/**
	 * This method loads the flexAssumption object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexAssumption()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold hostId
		String hostId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold itemId
		String itemId = null;
		// variable for errorMessage
		String errorMessage = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [flexOption] - Begins ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.FORECAST_MONITOR_ID;
			// set the itemId
			request.setAttribute("entityId", request.getParameter("entity"));
			// set the itemId
			request.setAttribute("currencyCode", request
					.getParameter("currency"));
			// set the itemId
			request.setAttribute("valueDate", request.getParameter("date"));
			// set the currency Name
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));
			// set the template Id
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			logger.debug(this.getClass().getName() + " - [flexOption] - Ends ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
						   + exception.getStackTrace()[0].getMethodName() + ":"
						   + exception.getStackTrace()[0].getLineNumber() + " "
						   + exception.getMessage();
			logger.error(this.getClass().getName()
						 + " - flexOption(). Exception : " + errorMessage);

		}
		return getView("flexAssumption");
	}

	/**
	 * This method loads the flexOption object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexTemplateOption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold hostId
		String hostId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold itemId
		String itemId = null;
		// variable for errorMessage
		String errorMessage = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [flexTemplateOption] - Begins ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.FORECAST_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			if (!SwtUtil.isEmptyOrNull(request.getParameter("entity"))) {
				// set the entityId
				request
						.setAttribute("entityId", request
								.getParameter("entity"));
				// set the currencyCode
				request.setAttribute("currencyCode", request
						.getParameter("currency"));
				// set the templateId
				request.setAttribute("templateId", request
						.getParameter("template"));
			}
			logger.debug(this.getClass().getName()
						 + " - [flexTemplateOption] - Ends ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
						   + exception.getStackTrace()[0].getMethodName() + ":"
						   + exception.getStackTrace()[0].getLineNumber() + " "
						   + exception.getMessage();
			logger.error(this.getClass().getName()
						 + " - flexTemplateOption(). Exception : " + errorMessage);

		}
		return getView("flexTemplateOption");
	}

	/**
	 * This method loads the flexassumption object into the client
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @throws SwtException
	 */
	public String flexModifyAssumption() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold hostId
		String hostId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold itemId
		String itemId = null;
		// variable for errorMessage
		String errorMessage = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [flexModifyAssumption] - Begins ");
			// Get the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Get the user id from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// Get the screen id from SwtConstants
			itemId = SwtConstants.FORECAST_MONITOR_ID;
			// set the hostId
			request.setAttribute("hostId", hostId);
			// set the userId
			request.setAttribute("userId", userId);
			// set the itemId
			request.setAttribute("itemId", itemId);
			// set the entityId
			request.setAttribute("entityId", request.getParameter("entity"));
			// set the currencyCode
			request.setAttribute("currencyCode", request
					.getParameter("currency"));
			// set the assumptionId
			request.setAttribute("assumptionId", request
					.getParameter("assumptionId"));
			// set the currencyName
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));
			// Start: code modified by Bala on 29-Aug-2011 for Issues found on
			// 1053 Beta testing - create Assumption with Spl characters, Amount
			// and description not displayed in the change Assumption screen
			// set the date
			request.setAttribute("date", request.getParameter("date"));
			// set the template id
			request.setAttribute("templateId", request
					.getParameter("templateId"));
			// check the assumptionId and set the screen name
			if (!SwtUtil.isEmptyOrNull(request.getParameter("assumptionId"))
				&& request.getParameter("assumptionId").equals("undefined")) {
				request.setAttribute("screenName", "addScreen");
			} else {
				request.setAttribute("screenName", "changeScreen");
			}
			// End: code modified by Bala on 29-Aug-2011 for Issues found on
			// 1053 Beta testing - create Assumption with Spl characters, Amount
			// and description not displayed in the change Assumption screen

			logger.debug(this.getClass().getName()
						 + " - [flexModifyAssumption] - Ends ");
		} catch (Exception exception) {
			errorMessage = exception.getStackTrace()[0].getClassName() + "."
						   + exception.getStackTrace()[0].getMethodName() + ":"
						   + exception.getStackTrace()[0].getLineNumber() + " "
						   + exception.getMessage();
			logger.error(this.getClass().getName()
						 + " - flexModifyAssumption(). Exception : " + errorMessage);

		}
		return getView("flexAddAssumption");
	}

	/**
	 * This method is used across this action class to add the entity into
	 * request
	 *
	 * @param request
	 *            HttpServletRequest request
	 * @return Collection<LabelValueBean>
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> putEntityListInReq(
			HttpServletRequest request, String flag) throws SwtException {

		// HttpSession variable to hold the session
		HttpSession session = null;
		// Collection to hold the entity access
		Collection<EntityUserAccess> userEntityAccess = null;
		// Collection to hold the entity access
		Collection<LabelValueBean> entityAccess = null;
		// Collection to hold the entity access
		Collection<LabelValueBean> collWithAll = null;
		try {
			logger.debug(this.getClass().getName()
						 + "- [putEntityListInReq] - Entering ");
			// get the session
			session = request.getSession();
			// Get the entity access list, and access list as label value bean
			userEntityAccess = SwtUtil.getUserEntityAccessList(session);
			// get the entity Access
			entityAccess = SwtUtil.convertEntityAcessCollectionLVL(
					userEntityAccess, session);
			// Instantiate the ArrayList
			collWithAll = new ArrayList<LabelValueBean>();
			if (flag.equals("true")) {
				if (entityAccess.size() > 1) {
					collWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
							SwtConstants.ALL_VALUE));
				}
			} else if (flag.equals("false")) {
				collWithAll.add(new LabelValueBean("Default", "*DEFAULT*"));
			}
			// add entityAccess to collection
			collWithAll.addAll(entityAccess);
			request.setAttribute("entities", collWithAll);
			logger.debug(this.getClass().getName()
						 + " - [putEntityListInReq] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - putEntityListInReq(). Exception caught : "
						 + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"bindColumnWidthInRequest", ForecastMonitorAction.class);
		}
		return collWithAll;
	}

	/**
	 *
	 * This function returns collection of currencies
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - collection of currencies
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyList(
			HttpServletRequest request, String hostId, String entityId,
			boolean flag) throws SwtException {

		// variable to hold roleId
		String roleId = null;
		// currency list collection
		ArrayList<LabelValueBean> currencyList = null;
		// currency list collection
		Collection<LabelValueBean> currrencyListWithAll = null;
		// Declare CurrencyManager object
		CurrencyManager currencyManager = null;
		// Declare Iterator object
		Iterator<Currency> multiplier_Itr = null;
		// Declare CurrencyDetailVO object
		CurrencyDetailVO cdv = null;
		// Declare Currency object
		Currency ccy = null;
		// session object
		HttpSession session = null;
		// Hashtable object
		Hashtable<String, String> map = null;
		// Declare LabelValueBean object
		LabelValueBean labelValueBean = null;
		// Declare Iterator object
		Iterator<LabelValueBean> itr = null;
		// Declare Iterator object
		Iterator<CurrencyMaster> itrCcy = null;
		// Declare CurrencyMaster object
		CurrencyMaster currency = null;
		// To hold CurrencyMaster object
		Set<CurrencyMaster> setCcy = null;
		// Declare CurrencyMaster collection
		Collection<CurrencyMaster> currencyDetail = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [ getCurrencyList ] - Entry ");
			map = new Hashtable<String, String>();
			map.put("T", "Thousands");
			map.put("M", "Millions");
			map.put("B", "Billions");
			map.put("N", "None");
			// Getting the session from the request
			session = request.getSession();
			if (!entityId.equals("All")) {
				/* Getting the User's Role Id from the session object */
				roleId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();

				/* Returns the currency Access List based on the Role */
				currencyList = (ArrayList<LabelValueBean>) SwtUtil
						.getSwtMaintenanceCache()
						.getCurrencyViewORFullAcessLVL(roleId, entityId);
				/* Variable Declaration for CurrencyDetailVO */

				/* Check for currency List not NULL */
				if (currencyList != null) {

					/*
					 * Removes the LabelValueBean object for the Key as
					 * 'Default' from the collection
					 */
					currencyList.remove(new LabelValueBean("Default", "*"));
					if (!flag) {
						itr = currencyList.iterator();
						while (itr.hasNext()) {
							labelValueBean = (LabelValueBean) itr.next();
							// get the currencyManager object
							currencyManager = (CurrencyManager) SwtUtil
									.getBean("currencyManager");
							// get the currency details
							cdv = currencyManager
									.getCurrencyDetailList(entityId, hostId,
											labelValueBean.getValue());
							multiplier_Itr = cdv.getCurrencyListDetails()
									.iterator();
							if (multiplier_Itr.hasNext()) {
								// get the currency details
								ccy = (Currency) multiplier_Itr.next();
							}
							if (ccy.getMultiplier().equals("N")
								|| SwtUtil.isEmptyOrNull(ccy
									.getMultiplier())) {
								labelValueBean.setLabel(labelValueBean
										.getLabel());
							} else {
								labelValueBean.setLabel(labelValueBean
																.getLabel()
														+ " ("
														+ map.get(ccy.getMultiplier())
														+ ")");
							}
						}
					}
					/*
					 * Assigning the new ArrayList object to a new Collection
					 * Object
					 */
					currrencyListWithAll = new ArrayList<LabelValueBean>();
					/* Adding the currencyList object to collection object */
					currrencyListWithAll.addAll(currencyList);
				}
				// Setting all currencies in request
				request.setAttribute("currencyList", currrencyListWithAll);
			} else {
				currencyDetail = forecastMonitorManager
						.getCurrencyForAllEntity(SwtUtil
								.getCurrentUserId(request.getSession()));
				// Instantiate LinkedHashSet
				setCcy = new LinkedHashSet<CurrencyMaster>();
				// set the currency detail
				setCcy.addAll(currencyDetail);
				// iterate the collection
				itrCcy = setCcy.iterator();
				// Instantiate the ArrayList
				currrencyListWithAll = new ArrayList<LabelValueBean>();
				while (itrCcy.hasNext()) {
					currency = (CurrencyMaster) itrCcy.next();
					labelValueBean = new LabelValueBean(currency
							.getCurrencyName(), currency.getCurrencyCode());
					// get the currencyManager object
					currencyManager = (CurrencyManager) SwtUtil
							.getBean("currencyManager");
					// get the currency details
					cdv = currencyManager.getCurrencyDetailList(SwtUtil
							.getUserCurrentEntity(session), hostId, currency
							.getCurrencyCode());
					multiplier_Itr = cdv.getCurrencyListDetails().iterator();
					if (multiplier_Itr.hasNext()) {
						// get the currency details
						ccy = (Currency) multiplier_Itr.next();
					}
					// Start: Code modified by Bala on 07-Oct-2011 for 1053 Beta
					// testing issue
					// - while selecting All entity option, screen should be
					// refreshed & populate the data
					if (ccy == null
						|| SwtUtil.isEmptyOrNull(ccy.getMultiplier())
						|| ccy.getMultiplier().equals("N")) {
						labelValueBean.setLabel(labelValueBean.getLabel());
					} else {
						if (!flag)
							labelValueBean
									.setLabel(labelValueBean.getLabel() + " ("
											  + map.get(ccy.getMultiplier())
											  + ")");
						else
							labelValueBean.setLabel(labelValueBean.getLabel());
					}
					// End: Code modified by Bala on 07-Oct-2011 for 1053 Beta
					// testing issue
					// - while selecting All entity option, screen should be
					// refreshed & populate the data
					currrencyListWithAll.add(labelValueBean);
				}
				// Setting all currencies in request
				request.setAttribute("currencyList", currrencyListWithAll);
			}
			logger.debug(this.getClass().getName()
						 + " - [ getCurrencyList ] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - getCurrencyList(). Exception caught : "
						 + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getCurrencyList", ForecastMonitorAction.class);
		} finally {
			// nullify the objects
			roleId = null;
			currencyList = null;
			currencyManager = null;
			multiplier_Itr = null;
			cdv = null;
			ccy = null;
			session = null;
			labelValueBean = null;
			itr = null;
			itrCcy = null;
			currency = null;
			setCcy = null;
			currencyDetail = null;
		}
		return currrencyListWithAll;
	}

	/**
	 *
	 * This function used to get the collection of templates
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            userId
	 * @return
	 * @throws SwtException -
	 *             SwtException object
	 */
	private void getTemplateList(HttpServletRequest request, String hostId,
								 String userId) throws SwtException {

		// Collection to hold ForecastMonitorTemplate
		Collection<ForecastMonitorTemplate> forecastTemplateColl = null;
		// Collection to hold template
		Collection<LabelValueBean> templateColl = null;
		// used to iterate collection
		Iterator<ForecastMonitorTemplate> itrTemplate = null;
		// Declare LabelValueBean object
		LabelValueBean templateValue = null;
		// Declare ForecastMonitorTemplate object
		ForecastMonitorTemplate forecastTemplate = null;

		try {
			logger.debug(this.getClass().getName()
						 + " - [ getTemplateList ] - Entry ");
			// Instantiate the ArrayList
			templateColl = new ArrayList<LabelValueBean>();
			// get the forecast template details
			forecastTemplateColl = forecastMonitorManager.getForecastTemplate(
					hostId, userId);
			if (forecastTemplateColl != null) {
				if (forecastTemplateColl.size() != 0) {
					itrTemplate = forecastTemplateColl.iterator();
					while (itrTemplate.hasNext()) {
						forecastTemplate = (ForecastMonitorTemplate) itrTemplate
								.next();
						templateValue = new LabelValueBean(forecastTemplate
								.getTemplateName(), forecastTemplate.getId()
								.getTemplateId());
						templateColl.add(templateValue);
					}
				}
			}
			// Setting all templateList in request
			request.setAttribute("templateList", templateColl);
			logger.debug(this.getClass().getName()
						 + " - [ getTemplateList ] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - getTemplateList(). Exception caught : "
						 + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getTemplateList", ForecastMonitorAction.class);
		} finally {
			// nullify the objects
			forecastTemplate = null;
			templateValue = null;
			itrTemplate = null;
		}
	}

	/**
	 * This method formats the values and creates a string with all the values.<br>
	 * The single row values are appended with '-' and the row are
	 * differentiated by appending a ','
	 *
	 * @param keyList
	 * @return rtn String
	 */
	private String formatKeyList(ArrayList<String[]> keyList)
			throws SwtException {

		// variable to hold keys
		String rtn = "";
		// used to iterate key values
		Iterator<String[]> itr = null;
		// variable for errorMessage
		String message = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [formatKeyList] - Entering");
			itr = keyList.iterator();
			// Iterates the array list to append the values in a string
			while (itr.hasNext()) {
				String[] keys = itr.next();
				String row = "";
				// For each row values
				for (int i = 0; i < keys.length; i++) {
					row += "-" + keys[i];
				}
				// For differentiating the rows
				if (row.length() > 0)
					rtn += ", " + row.substring(1);
			}
			// setting the substring value for rtn
			if (rtn.length() > 0)
				rtn = rtn.substring(2);
			logger.debug(this.getClass().getName()
						 + " - [formatKeyList] - Exiting");
		} catch (Exception e) {
			message = e.getStackTrace()[0].getClassName() + "."
					  + e.getStackTrace()[0].getMethodName() + ":"
					  + e.getStackTrace()[0].getLineNumber() + " "
					  + e.getMessage();
			logger.error(this.getClass().getName()
						 + " - [formatKeyList] - Exception -" + message);
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"formatKeyList", ForecastMonitorAction.class);
		}
		return rtn;
	}

	// Start: Code Modified by Bala on 06-Oct-2011 for Mantis 1413 - While
	// refreshing, the column width changed
	/**
	 * Method to save the column width as per the user preferences
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return ActionForward
	 */
	public String saveColumnWidth() {
		// String variable to hold the column width
		String width = null;
		// String variable to hold the column width
		String colWidth = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			logger.debug(this.getClass().getName()
						 + " - [saveColumnWidth] - Entering ");
			// get the binding column width
			colWidth = getBindColumnWidth(request);
			// Check the width and save the column width
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				if (!width.contains(",") && width.contains("divider=")) {
					colWidth = colWidth.replace(colWidth.split("=")[3],
							width.split("=")[1]);
					SwtUtil.setPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width", colWidth);
				} else if (width.contains("main=") && !width.contains("sub=")) {
					colWidth = colWidth.replace(colWidth.split("main=")[1].split(",")[0],
							width.split("=")[1]);
					colWidth = width + "," + colWidth.split(",")[1] + ","
							   + colWidth.split(",")[2];
					SwtUtil.setPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width", colWidth);
				} else if (!width.contains("main=") && width.contains("sub=")) {
					colWidth = colWidth.split(",")[0] + "," + width + ","
							   + colWidth.split(",")[2];
					SwtUtil.setPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width", colWidth);
				} else {
					SwtUtil.setPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width", width);
				}

			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			logger.debug(this.getClass().getName()
						 + " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "saveColumnWidth", ForecastMonitorAction.class),
					request, "");
		} finally {
			// nullify the objects
			width = null;
			colWidth = null;
		}
		return getView("statechange");
	}

	/**
	 * Used for binding the column width into request.
	 *
	 * @param HttpServletRequest
	 *            request
	 * @throws SwtException
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request)
			throws SwtException {
		// variable width
		String width = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [bindColumnWidthInRequest] - Entering ");
			width = SwtUtil
					.getPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width");
			if (width.equalsIgnoreCase("")) { // default condition
				width = "main=125,sub=125,divider=750";
			}
			request.setAttribute("main_width", width.split("main=")[1]
					.split(",")[0]);
			request.setAttribute("sub_width",
					width.split("sub=")[1].split(",")[0]);
			request.setAttribute("divider_width", width.split("divider=")[1]);
			logger.debug(this.getClass().getName()
						 + " - [bindColumnWidthInRequest] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - bindColumnWidthInRequest(). Exception caught : "
						 + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"bindColumnWidthInRequest", ForecastMonitorAction.class);
		} finally {
			// nullify the objects
			width = null;
		}
	}

	/**
	 * Used to get binding the column width into request.
	 *
	 * @param HttpServletRequest
	 *            request
	 * @return String
	 * @throws SwtException
	 */
	private String getBindColumnWidth(HttpServletRequest request)
			throws SwtException {
		// variable width
		String width = null;
		try {
			logger.debug(this.getClass().getName()
						 + " - [getBindColumnWidth] - Entering ");
			width = SwtUtil
					.getPropertyValue(request, SwtUtil
									.getUserCurrentEntity(request.getSession()),
							SwtConstants.FORECAST_MONITOR_ID, "display",
							"column_width");
			if (width.equalsIgnoreCase("")) { // default condition
				width = "main=125,sub=125,divider=750";
			}
			logger.debug(this.getClass().getName()
						 + " - [getBindColumnWidth] - Exit ");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
						 + " - getBindColumnWidth(). Exception caught : "
						 + e.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(e,
					"getBindColumnWidth", ForecastMonitorAction.class);
		}
		return width;
	}
	// End: Code Modified by Bala on 06-Oct-2011 for Mantis 1413 - While
	// refreshing, the column width changed
}
