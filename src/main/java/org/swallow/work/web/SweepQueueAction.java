/*
 * @(#)SweepQueueAction.java 1.0 12-12-2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.control.web.AccountAccessAction;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.Movement;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepQueueDetailVO;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.SweepQueueManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionMessages;

import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;

/**
 * <AUTHOR>
 *
 * This class is to run the perform the Authorise,Submit and cancel on sweeps
 * generated
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/sweepqueue", "/sweepqueue.do"})
public class SweepQueueAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("cancel", "jsp/work/sweepqueuecancel");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/work/sweepqueue");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "displayAngular":
				return displayAngular();
			case "displayList":
				return displayList();
			case "submit":
				return submit();
			case "setSweepAccess":
				return setSweepAccess();
		}


		return unspecified();
	}


	private Sweep sweepQueue;
	public Sweep getSweepQueue() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		sweepQueue = RequestObjectMapper.getObjectFromRequest(Sweep.class, request);
		return sweepQueue;
	}

	public void setSweepQueue(Sweep sweepQueue) {
		this.sweepQueue = sweepQueue;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("sweepQueue", sweepQueue);
	}


	@Autowired
	private SweepQueueManager sweepQueueManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SweepQueueAction.class);
	private final String EMPTYSTRING = "";

	/**
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {

		return display();
	} // end of unspecified method

	private String getFirstCurrencyGrp(HttpServletRequest request,
									   String entityId) throws SwtException {
		ArrayList currrencyGroupList = new ArrayList();
		HttpSession session = request.getSession();
		User user = SwtUtil.getCurrentUser(session);
		String roleId = user.getRoleId();
		String firstCurrId = "All";

		currrencyGroupList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);

		if (currrencyGroupList != null) {
			if (currrencyGroupList.size() != 0) {
				LabelValueBean lvl = (LabelValueBean) currrencyGroupList.get(0);
				firstCurrId = lvl.getValue();
			} else {
				firstCurrId = "All";
			}

		}

		return firstCurrId;
	}

	/**
	 * This method is used to display sweep details
	 *
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		String forward = null;
		String queueName = null;
		String currGrpId = null;
		String roleId = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		try {
			log
					.debug(this.getClass().getName() + " - [display()] - "
							+ "Entry");
			forward = "success";
			// ArrayLists added for Currency Group 'All' case
			ArrayList sweepDetailListAll = new ArrayList();
			ArrayList otherDetailListAll = new ArrayList();
			// DynaValidatorForm HttpSession session = request.getSession();
			queueName = (String) request.getParameter("queueName");
			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {
				forward = "cancel";
			}
			SystemFormats systemFormats = new SystemFormats();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			Sweep sweepForm = (Sweep) getSweepQueue();
			String hostId = sweepForm.getId().getHostId();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));

			// Get the user's default currency group

			// Value set to default Currency Grp
			currGrpId = "";

			currGrpId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();

			roleId = getRoleId(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");

				/*
				 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
				 */
				calledFrom = request.getParameter("calledFrom");
				if(calledFrom!=null&&calledFrom.equals("generic")){

					currGrpId =  SwtUtil.getCurrencyGroup(entityId,request
							.getParameter("currencyId" ) );
				}else {
					currGrpId = request.getParameter("currCode");
				}
				sweepForm.setEntityId(entityId);
			}

			int currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}

			// Currency Group set to 0 by default
			int currencyGrpAccess = 0;

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);

			Sweep sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			sweep.setEntityId(entityId);

			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);

			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				Collection groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				Iterator itGroupList = groupList.iterator();
				ArrayList temp = new ArrayList();
				while (itGroupList.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();

					currGrpId = entityCurrencyGroupAccess.getCurrencyGroupId();
					sweep.setCurrencyCode(currGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					Iterator tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				}
			} else {
				sweepQueueManager.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO, 0, 0,
						SwtConstants.EMPTY_STRING, systemFormats);
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();
				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			log.debug(this.getClass().getName() + " - [display()] - " + "Exit");
			return getView(forward);
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepQueueAction.'display' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", SweepQueueAction.class), request, "");

			return getView("fail");
		} finally {
			/* null the objects created already. */
			forward = null;
			queueName = null;
			currGrpId = null;
			roleId = null;
		}
	}
	/**
	 * This method is used to display sweep details
	 *
	 * @return
	 * @throws SwtException
	 */
	public String displayAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		String forward = null;
		String queueName = null;
		String currGrpId = null;
		String roleId = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		String defaultCcyGrp= null;
		try {
			log
					.debug(this.getClass().getName() + " - [displayAngular()] - "
							+ "Entry");
			forward = "success";
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			// ArrayLists added for Currency Group 'All' case
			ArrayList sweepDetailListAll = new ArrayList();
			ArrayList otherDetailListAll = new ArrayList();
			// DynaValidatorForm HttpSession session = request.getSession();
			queueName = (String) request.getParameter("queueName");

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(queueName)) {
				forward = "cancel";
			}
			SystemFormats systemFormats = new SystemFormats();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			Sweep sweepForm = (Sweep) getSweepQueue();
			String hostId = sweepForm.getId().getHostId();
			sweepForm.setEntityId(
					SwtUtil.getUserCurrentEntity(request.getSession()));

			// Get the user's default currency group

			// Value set to default Currency Grp
			currGrpId = "";

			currGrpId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();

			defaultCcyGrp= ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentCcyGrpId();
			roleId = getRoleId(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			if (request.getParameter("entityId") != null) {
				entityId = request.getParameter("entityId");

				/*
				 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
				 */
				calledFrom = request.getParameter("calledFrom");
				if(calledFrom!=null&&calledFrom.equals("generic")){

					currGrpId =  SwtUtil.getCurrencyGroup(entityId,request
							.getParameter("currencyId" ) );
				}else {
					currGrpId = request.getParameter("currCode");
				}
				sweepForm.setEntityId(entityId);
			}

			int currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			if (currGrpAccess == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				currGrpId = "All";
			}
			// Currency Group set to 0 by default
			int currencyGrpAccess = 0;

			// Identifies the screen displayed - Submit or Authorise.
			sweepForm.setEntityId(entityId);
			sweepForm.setQueueName(queueName);
			sweepForm.setCurrencyCode(currGrpId);
			setSweepQueue(sweepForm);
			Sweep sweep = new Sweep();
			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));
			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			sweep.setEntityId(entityId);

			sweep.setCurrencyCode(currGrpId);
			sweep.setAccountType("All");
			sweep.setQueueName(queueName);

			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			// Code added for Currency Group 'All' case
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				Collection groupList = SwtUtil.getSwtMaintenanceCache()

						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				Iterator itGroupList = groupList.iterator();
				ArrayList temp = new ArrayList();

				while (itGroupList.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();

					currGrpId = entityCurrencyGroupAccess.getCurrencyGroupId();

					sweep.setCurrencyCode(currGrpId);
					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);
					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					Iterator tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				}
			} else {
				sweepQueueManager.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO, 0, 0,
						SwtConstants.EMPTY_STRING, systemFormats);
				sweepDetailListAll = (ArrayList) sweepQueueDetailVO
						.getSweepDetailList();
				otherDetailListAll = (ArrayList) sweepQueueDetailVO
						.getOtherDetailList();
			}

			putEntityListInReq(request);
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("AccountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			// Code added for Currency Group 'All' case - attributes set in
			// request
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);

			//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity",
					entityId);
			responseConstructor.createElement("defaultCcyGrp", defaultCcyGrp);
			responseConstructor.createElement("defaultAcctType", "All");

			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);
			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("U".equalsIgnoreCase(queueName)?"auth":"submit", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(sweepDetailListAll.size());
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [displayAngular()] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'displayAngular' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in SweepQueueAction.'displayAngular' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayAngular", SweepQueueAction.class), request, "");

			return getView("fail");
		} finally {
			/* null the objects created already. */
			forward = null;
			queueName = null;
			currGrpId = null;
			roleId = null;
		}
	}

	/**
	 *
	 * @return
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionMessages errors = new ActionMessages();
		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		String defaultCcyGrp = null;
		String defaultAcctType = null;
		String defaultEntity= null;
		String queueName= null;
		try {
			log.debug(" Entering displayList method");
			String forward = "success";
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			ArrayList sweepDetailListAll = new ArrayList();
			ArrayList otherDetailListAll = new ArrayList();
			HttpSession session = null;
			int currencyGrpAccess = 0;
			SystemFormats systemFormats = new SystemFormats();
			String fromEntity = request.getParameter("fromEntity");
			//added by nadia
			String selectedlist = (String) request.getParameter("selectedList");
			// Added to identify if the sweep is to be submitted
			// even if new sweep amount is changed.
			String bypassChangedSweep = (String) request
					.getParameter("bypassChangedSweep");

			String bypassCutOff = (String) request.getParameter("bypassCutOff");

			String bypassAccountBreach = (String) request
					.getParameter("bypassAccountBreach");
			defaultCcyGrp= (String) request.getParameter("currencyCode");
			defaultAcctType= (String) request.getParameter("accountType");
			defaultEntity= (String) request.getParameter("entityId");

			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));
			sweep = (Sweep) getSweepQueue();

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());
			//added by nadia
			sweep.setEntityId(request.getParameter("entityId"));

			sweep.setAccountType(request.getParameter("accountType"));

			sweep.setCurrencyCode(request.getParameter("currencyCode"));

			sweep.setQueueName(request.getParameter("queueName"));


			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				request.setAttribute("AccountDesp",
						SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				request.setAttribute("AccountDesp",
						SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			putEntityListInReq(request);

			// now putCurrencyGroupListInReq function will be used
			// as per the rabo bank implementation
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, sweep.getEntityId());

			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			String currGrpId = "";
			String roleId = getRoleId(request);
			String entityId = sweep.getEntityId();

			session = request.getSession();
			if ((!entityId.equalsIgnoreCase(SwtUtil
					.getUserCurrentEntity(request.getSession())))
					&& sweep.getCurrencyCode().equalsIgnoreCase(
					((CommonDataManager) session
							.getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getCurrentCcyGrpId())) {
				currGrpId = "All";

			} else {
				currGrpId = sweep.getCurrencyCode();
			}

			// code added to handle Entity as All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				RoleTO roleObject = new RoleTO(roleId);
				Collection entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {

					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);

						Iterator itGroupList = groupList.iterator();
						ArrayList temp = new ArrayList();

						while (itGroupList.hasNext()) {
							EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							currGrpId = entityCurrencyGroupAccess
									.getCurrencyGroupId();

							sweep.setCurrencyCode(currGrpId);

							sweepQueueDetailVO.setEntityAccessList(SwtUtil
									.getUserEntityAccessList(request
											.getSession()));
							sweepQueueDetailVO
									.setSweepDetailList(new ArrayList());
							sweepQueueDetailVO
									.setOtherDetailList(new ArrayList());

							currencyGrpAccess = SwtUtil
									.getSwtMaintenanceCache()
									.getCurrencyGroupAccess(roleId, entityId,
											currGrpId);

							sweepQueueManager.getSweepQueueDetail(sweep,
									roleId, currencyGrpAccess,
									sweepQueueDetailVO, 0, 0,
									SwtConstants.EMPTY_STRING, systemFormats);
							temp = (ArrayList) sweepQueueDetailVO
									.getSweepDetailList();

							Iterator tempItr = temp.iterator();

							while (tempItr.hasNext())
								sweepDetailListAll.add(tempItr.next());

							temp = (ArrayList) sweepQueueDetailVO
									.getOtherDetailList();
							tempItr = temp.iterator();

							while (tempItr.hasNext())
								otherDetailListAll.add(tempItr.next());
						}

						sweep.setCurrencyCode("All");
					} else {

						ArrayList temp = new ArrayList();
						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);

						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}

				sweep.setEntityId("All");

			} else {
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					currencyGrpAccess = 0;
				} else {
					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);
				}

				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					Iterator itGroupList = groupList.iterator();
					ArrayList temp = new ArrayList();

					// HashSet temp= new HashSet();
					while (itGroupList.hasNext()) {
						EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						currGrpId = entityCurrencyGroupAccess
								.getCurrencyGroupId();

						sweep.setCurrencyCode(currGrpId);
						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());

						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();

						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());

						sweep.setCurrencyCode("All");
					}
				} else {
					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);

					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}

			setSweepQueue(sweep);

			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}

			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);
			//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement("defaultEntity",
					defaultEntity);
			responseConstructor.createElement("defaultCcyGrp",
					defaultCcyGrp);
			responseConstructor.createElement("defaultAcctType",
					defaultAcctType);
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));

			if (sweep.getAccountType().equalsIgnoreCase("C")) {
				responseConstructor.createElement("accountDesp", SwtConstants.CASH_ACCOUNTS_TYPE);
			}

			if (sweep.getAccountType().equalsIgnoreCase("U")) {
				responseConstructor.createElement("accountDesp", SwtConstants.CUSTODIAN_ACCOUNTS_TYPE);

			}

			if (sweep.getAccountType().equalsIgnoreCase("All")) {
				responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
			}

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("U".equalsIgnoreCase(queueName)?"auth":"submit", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(sweepDetailListAll.size());
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [displayList()] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'displayList' method : "
							+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
//			if (dyForm != null) {
			setSweepQueue(sweep);
//			}

			return getView("success");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueAction.'displayList' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", SweepQueueAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 *
	 * @return
	 * @throws SwtException
	 */
	public String submit()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionMessages errors = new ActionMessages();
		Sweep sweep = null;
		// DynaValidatorForm dyForm = null;
		SystemFormats systemFormats = new SystemFormats();
		ArrayList sweepDetailListAll = new ArrayList();
		ArrayList otherDetailListAll = new ArrayList();
		int currencyGrpAccess = -1;
		String sweeps = "";
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		// To hold date format
		String dateFormat = null;
		// To hold amount format
		String amountFormat = null;
		// To hold system format
		SystemFormats sysFormat = null;
		String  errorSweepAmount = null;
		String errorCutOff = null;
		String errorAccountBreach = null;
		String queueName= null;
		try {
			log.debug(" Entering submit method");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			String forward = "success";
			String selectedlist = (String) request.getParameter("selectedList");
			queueName= request.getParameter("queueName");

			// Added to identify if the sweep is to be submitted
			// even if new sweep amount is changed.
			String bypassChangedSweep = (String) request
					.getParameter("bypassChangedSweep");

			String bypassCutOff = (String) request.getParameter("bypassCutOff");

			String bypassAccountBreach = (String) request
					.getParameter("bypassAccountBreach");
// to check why it's null (sweep)
			sweep = (Sweep) getSweepQueue();
			systemFormats.setCurrencyFormat(SwtUtil
					.getCurrentCurrencyFormat(request.getSession()));
			systemFormats.setDateFormatValue(SwtUtil
					.getCurrentDateFormat(request.getSession()));

			sweep.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			sweep
					.setRequestUser(SwtUtil.getCurrentUserId(request
							.getSession()));

			sweep.setRequestRole(SwtUtil.getCurrentUser(request.getSession())
					.getRoleId());

			sweep.setEntityId(request.getParameter("entityId"));

			sweep.setAccountType(request.getParameter("accountType"));

			sweep.setCurrencyCode(request.getParameter("currencyCode"));

			sweep.setQueueName(request.getParameter("queueName"));

			// submit/authorize/cancel sweep
			ActionMessages list = null;

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				list = sweepQueueManager.cancel(sweep, selectedlist,
						systemFormats);
				forward = "cancel";
			} else {

				list = sweepQueueManager.submit(sweep, selectedlist,
						systemFormats, bypassChangedSweep, bypassCutOff,
						bypassAccountBreach);
			}
			// Checking if error is of type - SwtConstants.SWEEP_AMOUNT_CHANGED
			Iterator errorsType1 = list.get(SwtConstants.SWEEP_CUTOFF_EXCEEDED);

			Iterator errorsType2 = list.get(SwtConstants.SWEEP_AMOUNT_CHANGED);

			Iterator errorsType3 = list
					.get(SwtConstants.SWEEP_ACCOUNT_BREACHED);

			if (errorsType1.hasNext()) {
				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType1.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				sweeps = sweepMsg.substring(start + 1, end);

				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_CUT_OFF", "Y");
				errorCutOff="Y";
				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType2.hasNext()) {

				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType2.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_SWEEP_AMOUNT", "Y");
				errorSweepAmount= "Y";
				request.setAttribute("bypassCutOff", bypassCutOff);

				// sets the sweep ids whose calculated amount <> queue amount

				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			if (errorsType3.hasNext()) {

				// Setting this status will enable to call a pop-up with Yes and
				// No
				// as buttons along with error message.
				String sweepMsg = errorsType3.next().toString();
				int start = sweepMsg.indexOf("[");
				int end = sweepMsg.indexOf("]");
				if (sweeps.equals(""))
					sweeps = sweepMsg.substring(start + 1, end);
				else
					sweeps = sweeps + "," + sweepMsg.substring(start + 1, end);
				// Jsp will open a message box with yes/no options
				// if IS_ERROR_SWEEP_AMOUNT = "Y"
				request.setAttribute("IS_ERROR_ACCOUNT_BREACH", "Y");
				errorAccountBreach= "Y";
				// sets the sweep ids whose calculated amount <> queue amount

				request.setAttribute("bypassChangedSweep", bypassChangedSweep);
				request.setAttribute("bypassCutOff", bypassCutOff);

				// sets queueName in request
				request.setAttribute("queueName", sweep.getQueueName());
			}

			saveErrors(request, list);
			// sets the sweep ids whose calculated amount <> queue amount
			if (!sweeps.equals(""))
				request.setAttribute("ERROR_SWEEPS", sweeps);
			// after processing refresh the screen
			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));
			putEntityListInReq(request);

			String currGrpId = sweep.getCurrencyCode();
			String roleId = getRoleId(request);
			String entityId = sweep.getEntityId();
			if (!entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)
					&& !currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupAccess(roleId, entityId, currGrpId);
			}
			// code handling for Entity:All starts
			if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				RoleTO roleObject = new RoleTO(roleId);
				Collection entityList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(roleObject);
				Iterator itr = entityList.iterator();

				while (itr.hasNext()) {
					currGrpId = sweep.getCurrencyCode();

					EntityUserAccess entityUserAccess = (EntityUserAccess) itr
							.next();
					entityId = entityUserAccess.getEntityId();

					sweep.setEntityId(entityId);

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
						currencyGrpAccess = 0;
					} else {
						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);
					}

					if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

						Collection groupList = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupViewORFullAcess(roleId,
										entityId);

						Iterator itGroupList = groupList.iterator();
						ArrayList temp = new ArrayList();

						while (itGroupList.hasNext()) {
							EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
									.next();
							currGrpId = entityCurrencyGroupAccess
									.getCurrencyGroupId();

							sweep.setCurrencyCode(currGrpId);

							sweepQueueDetailVO.setEntityAccessList(SwtUtil
									.getUserEntityAccessList(request
											.getSession()));
							sweepQueueDetailVO
									.setSweepDetailList(new ArrayList());
							sweepQueueDetailVO
									.setOtherDetailList(new ArrayList());

							currencyGrpAccess = SwtUtil
									.getSwtMaintenanceCache()
									.getCurrencyGroupAccess(roleId, entityId,
											currGrpId);
							sweepQueueManager.getSweepQueueDetail(sweep,
									roleId, currencyGrpAccess,
									sweepQueueDetailVO, 0, 0,
									SwtConstants.EMPTY_STRING, systemFormats);
							temp = (ArrayList) sweepQueueDetailVO
									.getSweepDetailList();

							Iterator tempItr = temp.iterator();

							while (tempItr.hasNext()) {
								sweepDetailListAll.add(tempItr.next());
							}

							temp = (ArrayList) sweepQueueDetailVO
									.getOtherDetailList();
							tempItr = temp.iterator();

							while (tempItr.hasNext()) {
								otherDetailListAll.add(tempItr.next());
							}
						}

						sweep.setCurrencyCode("All");
					} else {
						ArrayList temp = new ArrayList();

						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());
					}
				}

				sweep.setEntityId("All");

			} else {
				// code handling for Entity: All ends
				// code for handling of All currency group added starts
				if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
					Collection groupList = SwtUtil.getSwtMaintenanceCache()
							.getCurrencyGroupViewORFullAcess(roleId, entityId);
					Iterator itGroupList = groupList.iterator();
					ArrayList temp = new ArrayList();

					while (itGroupList.hasNext()) {
						EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
								.next();
						currGrpId = entityCurrencyGroupAccess
								.getCurrencyGroupId();

						sweep.setCurrencyCode(currGrpId);

						sweepQueueDetailVO.setEntityAccessList(SwtUtil
								.getUserEntityAccessList(request.getSession()));
						sweepQueueDetailVO.setSweepDetailList(new ArrayList());
						sweepQueueDetailVO.setOtherDetailList(new ArrayList());

						currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
								.getCurrencyGroupAccess(roleId, entityId,
										currGrpId);

						sweepQueueManager.getSweepQueueDetail(sweep, roleId,
								currencyGrpAccess, sweepQueueDetailVO, 0, 0,
								SwtConstants.EMPTY_STRING, systemFormats);
						temp = (ArrayList) sweepQueueDetailVO
								.getSweepDetailList();

						Iterator tempItr = temp.iterator();

						while (tempItr.hasNext())
							sweepDetailListAll.add(tempItr.next());

						temp = (ArrayList) sweepQueueDetailVO
								.getOtherDetailList();
						tempItr = temp.iterator();

						while (tempItr.hasNext())
							otherDetailListAll.add(tempItr.next());

						sweep.setCurrencyCode("All");
					}
				} else {
					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);
					sweepDetailListAll = (ArrayList) sweepQueueDetailVO
							.getSweepDetailList();
					otherDetailListAll = (ArrayList) sweepQueueDetailVO
							.getOtherDetailList();
				}
			}

			// code for handling of All currency group ends
			setSweepQueue(sweep);
			request.setAttribute("sweepDetailList", sweepDetailListAll);
			request.setAttribute("othersDetailList", otherDetailListAll);

			// now putCurrencyGroupListInReq function
			// will be used as per the rabo bank implementation
			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			putAccountListInReq(request, entityId);

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			/*if (!sweeps.equals("")) {

				responseHandler = new ResponseHandler();
				responseConstructor = new SwtResponseConstructor();
				xmlWriter = responseConstructor.getXMLWriter();

				xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
						SwtConstants.DATA_FETCH_OK);
				// forms singleton node
				xmlWriter.startElement(SwtConstants.SINGLETONS);

				String msg = getMsgDesc(list.toString(), request);
				responseConstructor.createElement("listOfMsgs",
						msg);
				responseConstructor.createElement("defaultEntity",
						entityId);
				responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
						entityId));
				responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
				responseConstructor.createElement("errorSweepAmount", errorSweepAmount);
				responseConstructor.createElement("errorCutOff", errorCutOff);
				responseConstructor.createElement("errorAccountBreach", errorAccountBreach);
				responseConstructor.createElement("errorSweeps", sweeps);
				responseConstructor.createElement("bypassChangedSweep", bypassChangedSweep);
				responseConstructor.createElement("bypassCutOff", bypassCutOff);
				responseConstructor.createElement("bypassAccountBreach", bypassAccountBreach);

				xmlWriter.endElement(SwtConstants.SINGLETONS);

				xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
				request.setAttribute("data", xmlWriter.getData());
				log.debug(this.getClass().getName() + " - [submit()] - " + "Exit");
				return getView("data");
			}else {*/
			//Angular part
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_QUEUE);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			String msg = getMsgDesc(list.toString(), request);
			responseConstructor.createElement("listOfMsgs",
					msg);

			responseConstructor.createElement("defaultEntity",
					entityId);
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			responseConstructor.createElement("accountDesp", SwtConstants.ALL_ACCOUNTS_TYPE);
			responseConstructor.createElement("errorSweepAmount", errorSweepAmount);
			responseConstructor.createElement("errorCutOff", errorCutOff);
			responseConstructor.createElement("errorAccountBreach", errorAccountBreach);
			responseConstructor.createElement("errorSweeps", sweeps);
			responseConstructor.createElement("bypassChangedSweep", bypassChangedSweep);
			responseConstructor.createElement("bypassCutOff", bypassCutOff);
			responseConstructor.createElement("bypassAccountBreach", bypassAccountBreach);

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyGrpList = putCurrencyGroupListInReqNew(request, sweep.getId().getHostId(), sweep
					.getEntityId());

			j = currencyGrpList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGrpList", lstOptions));
			/***** Currency Combo End ***********/


			/***** Account Type list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection acctTypeList = putAccountListInReq(request, sweep.getEntityId());

			j = acctTypeList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getLabel(), row.getValue(), false));
			}
			lstSelect.add(new SelectInfo("acctTypeList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* AuthoriseQueueGrid ******/
			responseConstructor.formGridStart("authoriseQueueGrid");
			responseConstructor.formColumn(getGridColumns("U".equalsIgnoreCase(queueName)?"auth":"submit", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(sweepDetailListAll.size());
			for (Iterator<Sweep> it = sweepDetailListAll.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			};

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* ViewQueueGrid ******/
			responseConstructor.formGridStart("viewQueueGrid");
			responseConstructor.formColumn(getGridColumns("view", width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(otherDetailListAll.size());
			for (Iterator<Sweep> it = otherDetailListAll.iterator(); it.hasNext();) {
				Sweep swp = (Sweep) it.next();
				responseConstructor.formRowStart();
				if("Y".equalsIgnoreCase(swp.getCutOffExceeded())) {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr(), true, false);
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser(), true, false);
				}else {
					responseConstructor.createRowElement(SwtConstants.SWEEP_VALUE_DATE,   SwtUtil.formatDate(swp.getValueDate(), dateFormat ));
					responseConstructor.createRowElement(SwtConstants.SWEEP_CCY_CODE, swp.getCurrencyCode());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CURRENT_AMT, swp.getDisplaySweepAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_NEW_AMOUNT,swp.getNewCalulatedAmount());
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_CR, swp.getEntityIdCr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_CR, swp.getAccountIdCr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_CR_NAME, swp.getAccountCr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_ENTITY_DR, swp.getEntityIdDr());
					responseConstructor.createRowElement(SwtConstants.SWEEP_ACCOUNT_ID_DR, swp.getAccountIdDr());
					responseConstructor.createRowElement(SwtConstants.ACCOUNT_DR_NAME, swp.getAccountDr().getAcctname(), true, false);
					responseConstructor.createRowElement(SwtConstants.SWEEP_SWEEP_ID, swp.getId().getSweepId().toString());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_INT_MSG, swp.getAccountCr().getAcctNewCrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_CRD_EXT_MSG, swp.getAccountCr().getAcctNewCrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_INT_MSG, swp.getAccountDr().getAcctNewDrInternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_DR_EXT_MSG, swp.getAccountDr().getAcctNewDrExternal());
					responseConstructor.createRowElement(SwtConstants.SWEEP_TYPE, "A".equalsIgnoreCase(swp.getSweepType())?SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_AUTO, request):SwtUtil.getMessage(SwtConstants.SUBMIT_SWEEP_COL_VALUE_MAN, request));
					responseConstructor.createRowElement(SwtConstants.SWEEP_USER, swp.getDisplayUser());
				}
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SWEEP_QUEUE);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [submit()] - " + "Exit");
			return getView("data");
			//}
		} catch (SwtException sexp) {
			log
					.error("SwtException Catch in SweepQueueAction.'submit' method : "
							+ sexp.getMessage());

			saveErrors(request, SwtUtil.logException(sexp, request, ""));

			String currGrpId = sweep.getCurrencyCode();
			String roleId = getRoleId(request);
			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			currencyGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);

			// open the screen in same status again
			SweepQueueDetailVO sweepQueueDetailVO = new SweepQueueDetailVO();
			sweepQueueDetailVO.setEntityAccessList(SwtUtil
					.getUserEntityAccessList(request.getSession()));

			// sweepQueueManager.getSweepQueueDetail(sweep, roleId,
			// currencyGrpAccess, sweepQueueDetailVO, systemFormats);
			// code for handling of All currency group added starts
			if (currGrpId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// log.debug("processing for ALL starts");
				Collection groupList = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityId);
				Iterator itGroupList = groupList.iterator();
				ArrayList temp = new ArrayList();

				while (itGroupList.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
							.next();
					currGrpId = entityCurrencyGroupAccess.getCurrencyGroupId();

					sweep.setCurrencyCode(currGrpId);

					sweepQueueDetailVO.setEntityAccessList(SwtUtil
							.getUserEntityAccessList(request.getSession()));
					sweepQueueDetailVO.setSweepDetailList(new ArrayList());
					sweepQueueDetailVO.setOtherDetailList(new ArrayList());

					currencyGrpAccess = SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyGroupAccess(roleId, entityId, currGrpId);

					sweepQueueManager.getSweepQueueDetail(sweep, roleId,
							currencyGrpAccess, sweepQueueDetailVO, 0, 0,
							SwtConstants.EMPTY_STRING, systemFormats);

					temp = (ArrayList) sweepQueueDetailVO.getSweepDetailList();

					Iterator tempItr = temp.iterator();

					while (tempItr.hasNext())
						sweepDetailListAll.add(tempItr.next());

					temp = (ArrayList) sweepQueueDetailVO.getOtherDetailList();
					tempItr = temp.iterator();

					while (tempItr.hasNext())
						otherDetailListAll.add(tempItr.next());

					sweep.setCurrencyCode("All");
				}
				setSweepQueue(sweep);
				request.setAttribute("sweepDetailList", sweepDetailListAll);
				request.setAttribute("othersDetailList", otherDetailListAll);
			} else {
				sweepQueueManager.getSweepQueueDetail(sweep, roleId,
						currencyGrpAccess, sweepQueueDetailVO, 0, 0,
						SwtConstants.EMPTY_STRING, systemFormats);
				setSweepQueue(sweep);
				request.setAttribute("sweepDetailList", sweepQueueDetailVO
						.getSweepDetailList());
				request.setAttribute("othersDetailList", sweepQueueDetailVO
						.getOtherDetailList());
			}

			putEntityListInReq(request);

			putCurrencyGroupListInReq(request, sweep.getId().getHostId(), sweep
					.getEntityId());
			putAccountListInReq(request, entityId);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			String forward = "success";

			if (SwtConstants.SWEEP_STATUS_CANCEL.equalsIgnoreCase(sweep
					.getQueueName())) {
				forward = "cancel";
			}

			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currGrpId);

			return getView(forward);
		} catch (Exception exp) {
			log.error("Exception Catch in SweepQueueAction.'submit' method : "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "submit", SweepQueueAction.class), request, "");

			return getView("fail");
		}
	}

	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
				.getSession());

		ArrayList list = (ArrayList) coll;
		list.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("entityList", coll);
		return list;

	}

	private Collection putCurrencyListInReq(HttpServletRequest request, Sweep sweep)
			throws SwtException {
		CurrencyManager currencyManager = (CurrencyManager) SwtUtil
				.getBean("currencyManager");

		CurrencyDetailVO currencyDetailVO = null;

		if (sweep.getEntityId() != null) {
			if (sweep.getEntityId().equals(SwtConstants.ALL_VALUE)) {
				Collection coll = SwtUtil.getUserEntityAccessList(request
						.getSession());
				coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
						.getSession());
				currencyDetailVO = currencyManager.getCurrencyDetailList(coll,
						sweep.getId().getHostId(), "All", false);

				Set set = new LinkedHashSet();
				set.addAll(currencyDetailVO.getCurrencyList());
				currencyDetailVO.getCurrencyList().clear();
				currencyDetailVO.getCurrencyList().addAll(set);
			} else {
				currencyDetailVO = currencyManager.getCurrencyDetailList(sweep
								.getEntityId(), sweep.getId().getHostId(),
						"All");

				ArrayList coll = (ArrayList) currencyDetailVO.getCurrencyList();

				if (coll.size() > 0) {
					coll.remove(0);
				}
			}

			ArrayList list = (ArrayList) currencyDetailVO.getCurrencyList();
			list.add(0, new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

			request.setAttribute("currencyList", list);
			return list;
		} else {
			request.setAttribute("currencyList", new ArrayList());
			return new ArrayList();
		}
	}

	private Collection putAccountListInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		ArrayList coll = (ArrayList) cacheManagerInst.getMiscParamsLVL(
				"ACCOUNTTYPE", entityId);
		coll.remove(0);
		coll.add(0, new LabelValueBean("All", "All"));
		request.setAttribute("accountList", coll);
		return coll;
	}

	/**
	 * @return Returns the sweepQueueManager.
	 */
	public SweepQueueManager getSweepQueueManager() {
		return sweepQueueManager;
	}

	/**
	 * @param sweepQueueManager
	 *            The sweepQueueManager to set.
	 */
	public void setSweepQueueManager(SweepQueueManager sweepQueueManager) {
		this.sweepQueueManager = sweepQueueManager;
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private String putCurrencyGroupListInReq(HttpServletRequest request,
											 String hostId, String entityId) throws SwtException {
		log.debug("Inside SweepQueueAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);

		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return defaultCurrencyGroup;
	}


	private Collection putCurrencyGroupListInReqNew(HttpServletRequest request,
													String hostId, String entityId) throws SwtException {
		log.debug("Inside SweepQueueAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = new String();
		String roleId = getRoleId(request);

		// code added for handling of the Entity:All STARTS
		ArrayList al = new ArrayList();

		if (entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {

			RoleTO roleObject = new RoleTO(roleId);
			Collection entityList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(roleObject);
			Iterator itr = entityList.iterator();

			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				String entityIdAll = entityUserAccess.getEntityId();

				Collection groupListAll = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyGroupViewORFullAcess(roleId, entityIdAll);
				Iterator groupListAllitr = groupListAll.iterator();

				while (groupListAllitr.hasNext())
					al.add(groupListAllitr.next());
			}
		} else {
			// code added for handling of the Entity:All ENDS
			al = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupViewORFullAcess(roleId, entityId);

		}

		Iterator itGroupList = al.iterator();
		currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
				SwtConstants.ALL_VALUE));

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		if ((currencyGroupList != null) && (currencyGroupList.size() > 1)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(1);

			defaultCurrencyGroup = labelValueBean.getValue();
		}

		return currencyGroupList;
	}

	public String getRoleId(HttpServletRequest request) {
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		User currUser = (User) CDM.getUser();
		String roleId = currUser.getRoleId();

		return roleId;
	}

	/**
	 * Start:Code Modified for Mantis 1483 by Chinniah on
	 * 7-Aug-2011:Authorization button not Enabled. When the last currency set
	 * view access through role for the particular Entity
	 */
	/**
	 * This method is used to enable the Authorise button based on selection of
	 * the Sweeps
	 *
	 * @throws SwtException
	 */
	public void setCurrencyAccess()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Declering string hostId for storing the current hostid
		String hostId = null;
		// Declearing String Entity Id for storing the current entityId
		String entityId = null;
		// Declearing String for storing selected currency in data grid
		String currencyId = null;
		// Declaring Boolean access for determining the access type for the
		// currency
		boolean access = false;
		try {
			log.debug(this.getClass().getName() + " - [setCurrencyAccess()] - "
					+ "Entering");
			// getting current hostId
			hostId = SwtUtil.getCurrentHostId(request.getSession());
			// Getting the current entityId from request
			entityId = request.getParameter("entityId");
			// getting the selected currency in the data grid from request
			currencyId = request.getParameter("selectedCurrency");
			// Detremining the currency access
			access = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId,
					entityId, currencyId);
			// returning response
			response.getWriter().print(access);
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepQueueAction.'setCurrencyAccess' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "setCurrencyAccess", SweepQueueAction.class), request,
					"");
		} finally {
			// nullify the objects
			hostId = null;
			entityId = null;
			currencyId = null;
			log.debug(this.getClass().getName() + " - [setCurrencyAccess()] - "
					+ "Exit");
		}

	}
	/**
	 * End:Code Modified for Mantis 1483 by Chinniah on 7-Aug-2011:Authorization
	 * button not Enabled. When the last currency set view access through role
	 * for the particular Entity
	 */



	private List<ColumnInfo> getGridColumns(String source, String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;

		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getGridColumns ] - Entry");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				width =  SwtConstants.SWEEP_VALUE_DATE + "=80,"
						+ SwtConstants.SWEEP_CCY_CODE + "=67,"
						+ SwtConstants.SWEEP_CURRENT_AMT + "=150,"
						+ SwtConstants.SWEEP_NEW_AMOUNT + "=150,"
						+ SwtConstants.SWEEP_SEARCH_ENTITY_CR+ "=100,"
						+ SwtConstants.SWEEP_ACCOUNT_ID_CR + "=220,"
						+ SwtConstants.ACCOUNT_CR_NAME + "=180,"
						+ SwtConstants.SWEEP_SEARCH_ENTITY_DR + "=100,"
						+ SwtConstants.SWEEP_ACCOUNT_ID_DR + "=220,"
						+ SwtConstants.ACCOUNT_DR_NAME + "=180,"
						+ SwtConstants.SWEEP_SWEEP_ID + "=120,"
						+ SwtConstants.SWEEP_CRD_INT_MSG + "=140,"
						+ SwtConstants.SWEEP_CRD_EXT_MSG + "=140,"
						+ SwtConstants.SWEEP_DR_INT_MSG + "=140,"
						+ SwtConstants.SWEEP_DR_EXT_MSG + "=140,"
						+ SwtConstants.SWEEP_TYPE + "=76,"
						+ SwtConstants.SWEEP_USER + "=144";

			}

			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				/* Default values for column order */
				columnOrder = SwtConstants.SWEEP_VALUE_DATE + ","
						+ SwtConstants.SWEEP_CCY_CODE + ","
						+ SwtConstants.SWEEP_CURRENT_AMT + ","
						+ SwtConstants.SWEEP_NEW_AMOUNT + ","
						+ SwtConstants.SWEEP_SEARCH_ENTITY_CR+ ","
						+ SwtConstants.SWEEP_ACCOUNT_ID_CR + ","
						+ SwtConstants.ACCOUNT_CR_NAME + ","
						+ SwtConstants.SWEEP_SEARCH_ENTITY_DR + ","
						+ SwtConstants.SWEEP_ACCOUNT_ID_DR + ","
						+ SwtConstants.ACCOUNT_DR_NAME + ","
						+ SwtConstants.SWEEP_SWEEP_ID + ","
						+ SwtConstants.SWEEP_CRD_INT_MSG + ","
						+ SwtConstants.SWEEP_CRD_EXT_MSG + ","
						+ SwtConstants.SWEEP_DR_INT_MSG + ","
						+ SwtConstants.SWEEP_DR_EXT_MSG + ","
						+ SwtConstants.SWEEP_TYPE + ","
						+ SwtConstants.SWEEP_USER ;

			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}
			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				if (order.equals(SwtConstants.SWEEP_VALUE_DATE)) {
					// column Alert Date

					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_VALUE_DATE_HEADER, request),
							SwtConstants.SWEEP_VALUE_DATE,
							SwtConstants.COLUMN_TYPE_STRING, true, 0,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_VALUE_DATE)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_VALUE_DATE )));

					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);

				} else if (order.equals(SwtConstants.SWEEP_CCY_CODE)) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CCY_CODE_HEADER, request),
							SwtConstants.SWEEP_CCY_CODE,
							SwtConstants.COLUMN_TYPE_STRING, true, 1,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CCY_CODE)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_CCY_CODE )));

					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_CURRENT_AMT )) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CURRENT_AMT_HEADER, request),
							SwtConstants.SWEEP_CURRENT_AMT ,
							SwtConstants.COLUMN_TYPE_NUMBER, true, 2,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CURRENT_AMT )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_CURRENT_AMT )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_NEW_AMOUNT )) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_NEW_AMOUNT_HEADER, request),
							SwtConstants.SWEEP_NEW_AMOUNT ,
							SwtConstants.COLUMN_TYPE_NUMBER, true, 3,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_NEW_AMOUNT )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_NEW_AMOUNT )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.SWEEP_SEARCH_ENTITY_CR)) {
					// column Alert Date
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_ENTITY_CR_HEADER, request),
							SwtConstants.SWEEP_SEARCH_ENTITY_CR,
							SwtConstants.COLUMN_TYPE_STRING, true, 4,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_CR)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_SEARCH_ENTITY_CR )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.SWEEP_ACCOUNT_ID_CR)) {
					// column Alert Time
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_ACCOUNT_ID_CR_HEADER, request),
							SwtConstants.SWEEP_ACCOUNT_ID_CR,
							SwtConstants.COLUMN_TYPE_STRING, true, 5,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_ACCOUNT_ID_CR)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.SWEEP_ACCOUNT_ID_CR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.ACCOUNT_CR_NAME )) {
					// column Alert Type
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ACCOUNT_CR_NAME_HEADER, request),
							SwtConstants.ACCOUNT_CR_NAME ,
							SwtConstants.COLUMN_TYPE_STRING, false, 7,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_CR_NAME )),
							false, false, hiddenColumnsMap
							.get(SwtConstants.ACCOUNT_CR_NAME )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_SEARCH_ENTITY_DR)) {
					// column Risk Weight
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_ENTITY_DR_HEADER, request),
							SwtConstants.SWEEP_SEARCH_ENTITY_DR,
							SwtConstants.COLUMN_TYPE_STRING, true,
							8,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_DR)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_SEARCH_ENTITY_DR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_ACCOUNT_ID_DR)) {
					// column Account ID
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_ACCOUNT_ID_DR_HEADER, request),
							SwtConstants.SWEEP_ACCOUNT_ID_DR,
							SwtConstants.COLUMN_TYPE_STRING, true,
							9,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_ACCOUNT_ID_DR)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_ACCOUNT_ID_DR)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order.equals(SwtConstants.ACCOUNT_DR_NAME )) {
					// column Alert Type
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ACCOUNT_DR_NAME_HEADER, request),
							SwtConstants.ACCOUNT_DR_NAME ,
							SwtConstants.COLUMN_TYPE_STRING, false, 10,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_DR_NAME )),
							false, false, hiddenColumnsMap
							.get(SwtConstants.ACCOUNT_DR_NAME )));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_SWEEP_ID)) {
					// column Client ID
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_SWEEP_ID_HEADER, request),
							SwtConstants.SWEEP_SWEEP_ID,
							SwtConstants.COLUMN_TYPE_NUMBER, true,
							11,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_SWEEP_ID)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_SWEEP_ID)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_CRD_INT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CRD_INT_MSG_HEADER, request),
							SwtConstants.SWEEP_CRD_INT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, true,
							12,
							Integer.parseInt(widths
									.get(SwtConstants.SWEEP_CRD_INT_MSG)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_CRD_INT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SWEEP_CRD_EXT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_CRD_EXT_MSG_HEADER, request),
							SwtConstants.SWEEP_CRD_EXT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, true,
							13,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_CRD_EXT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_CRD_EXT_MSG))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_CRD_EXT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}else if (order
						.equals(SwtConstants.SWEEP_DR_INT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_DR_INT_MSG_HEADER, request),
							SwtConstants.SWEEP_DR_INT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, true,
							14,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_DR_INT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_DR_INT_MSG))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_DR_INT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}
				else if (order
						.equals(SwtConstants.SWEEP_DR_EXT_MSG)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_DR_EXT_MSG_HEADER, request),
							SwtConstants.SWEEP_DR_EXT_MSG,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							15,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_DR_EXT_MSG))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_DR_EXT_MSG))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_DR_EXT_MSG)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_TYPE)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_TYPE_HEADER, request),
							SwtConstants.SWEEP_TYPE,
							SwtConstants.COLUMN_TYPE_STRING, "auth".equalsIgnoreCase(source)?true:false,
							16,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_TYPE))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_TYPE))
									: 125,
							false,
							"auth".equalsIgnoreCase(source)?true:false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_TYPE)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.SWEEP_USER)) {
					// column Client Name
					tmpColumnInfo = (new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.SWEEP_USER_HEADER, request),
							SwtConstants.SWEEP_USER,
							SwtConstants.COLUMN_TYPE_STRING, false,
							17,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.SWEEP_USER))
									? Integer.parseInt(widths.get(SwtConstants.SWEEP_USER))
									: 125,
							false,
							false,
							hiddenColumnsMap
									.get(SwtConstants.SWEEP_USER)));
					tmpColumnInfo.setHeaderTooltip("auth".equalsIgnoreCase(source)?SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_HEADING_TOOLTIP, request):"");
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setColumnGroup(getColGrpHeader(source, request));
					lstColumns.add(tmpColumnInfo);
				}


			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Caught in [getGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;

			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getGridColumns ] - Exit");
		}
		// return xml column
		return lstColumns;
	}


	private String getMsgDesc(String msg, HttpServletRequest request) {

		String finalMsg = "";
		// Define the pattern to match keys and values
		Pattern pattern = Pattern.compile("(\\w+\\.\\w+)=\\[\\1\\[(.*?)\\]\\]");
		Matcher matcher = pattern.matcher(msg);

		// Map to hold the keys and values
		Map<String, List<String>> resultMap = new HashMap<>();

		while (matcher.find()) {
			String key = matcher.group(1);
			String values = matcher.group(2);

			// Split the values by comma and trim whitespace
			List<String> valueList = new ArrayList<>();
			String[] splitValues = values.split(", ");

			String firstValue = splitValues[0].trim();
			valueList.add(firstValue);
			if(splitValues.length==2) {
				String secondValue = splitValues[1].trim();
				valueList.add(secondValue);
			}

			resultMap.put(key, valueList);
		}

		for (Map.Entry<String, List<String>> entry : resultMap.entrySet()) {
			if(entry.getValue().size()==2) {
				finalMsg = finalMsg+ SwtUtil.getMessage(entry.getKey(), request, entry.getValue().get(0), entry.getValue().get(1));
			}else {
				finalMsg = finalMsg+ SwtUtil.getMessage(entry.getKey(), request, entry.getValue().get(0));

			}

		}

		return finalMsg;
	}


	public String getColGrpHeader(String source, HttpServletRequest request) {
		String header= "";

		switch (source) {

			case "auth":
				header= SwtUtil.getMessage(SwtConstants.SWEEP_AUTHORIZE_PANEL, request);
				break;
			case "submit":
				header= SwtUtil.getMessage(SwtConstants.SWEEP_SUBMIT_PANEL, request);
				break;
			case "view":
				header= SwtUtil.getMessage(SwtConstants.SWEEP_OTHER_PANEL, request);
				break;
			default:
				break;
		}
		return header;
	}



	public String setSweepAccess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Local variable declaration and initialization */
		String roleId=null;
		String hostId=null;
		String entityId=null;
		String accountIdCr=null;
		String accountIdDr=null;
		String entityIdCr=null;
		String entityIdDr=null;
		String currencyCode= null;
		String status =null;
		boolean acctCrFlag = false;
		boolean acctDrFlag = false;
		AccountAccess acctCrAccess = null;
		AccountAccess acctDrAccess = null;

		CacheManager cacheManagerInst=null;
		User userFromCDM =null;
		boolean acctCrAccessFlag = false;
		boolean acctDrAccessFlag = false;
		boolean ccyFlag = false;
		boolean flag = false;
		try {
			log.debug(this.getClass().getName() + " - [setSweepAccess] - " + "Entry");
			acctCrAccess = new AccountAccess();
			acctDrAccess = new AccountAccess();
			cacheManagerInst = CacheManager.getInstance();
			/*Read the current user from SwtUtil file*/
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
			/*Getting host id from cache manager file*/
			hostId = cacheManagerInst.getHostId();
			/*Getting role id using bean class*/
			roleId = userFromCDM.getRoleId();
			/*Getting account id,entity id,status from request*/
			entityId = request.getParameter("entityId");
			accountIdCr = request.getParameter("accountIdCr");
			accountIdDr = request.getParameter("accountIdDr");
			status = request.getParameter("status");
			entityIdCr = request.getParameter("entityIdCr");
			entityIdDr = request.getParameter("entityIdDr");
			currencyCode = request.getParameter("currencyCode");

			//Account CR
			/*Setting role id using bean class*/
			acctCrAccess.getId().setRoleId(roleId);
			/*Setting entity id using bean class*/
			acctCrAccess.getId().setEntityId(entityIdCr);
			/*Setting account id using bean class*/
			acctCrAccess.getId().setAccountId(accountIdCr);
			/*Setting host id using bean class*/
			acctCrAccess.getId().setHostId(hostId);
			AccountAccessManager accountAccessManager = (AccountAccessManager) SwtUtil
					.getBean("accountAccessManager");
			/*Checking role account access flag by calling manager file*/
			acctCrAccessFlag = accountAccessManager.getRoleAccessDetails(acctCrAccess);
			/*Checking account access by calling manager file*/
			if(acctCrAccessFlag){
				acctCrFlag = accountAccessManager.checkAcctAccess(acctCrAccess,status);
			}
			else{
				acctCrFlag = true;
			}


			//Account DR
			acctDrAccess.getId().setRoleId(roleId);
			/*Setting entity id using bean class*/
			acctDrAccess.getId().setEntityId(entityIdDr);
			/*Setting account id using bean class*/
			acctDrAccess.getId().setAccountId(accountIdDr);
			/*Setting host id using bean class*/
			acctDrAccess.getId().setHostId(hostId);
			/*Checking role account access flag by calling manager file*/
			acctDrAccessFlag = accountAccessManager.getRoleAccessDetails(acctDrAccess);
			/*Checking account access by calling manager file*/
			if(acctDrAccessFlag){
				acctDrFlag = accountAccessManager.checkAcctAccess(acctDrAccess,status);
			}
			else{
				acctDrFlag = true;
			}

			// Detremining the currency access
			ccyFlag = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId,
					entityId, currencyCode);
			if(acctCrFlag && acctDrFlag && ccyFlag){
				flag=true;
			}else{
				flag=false;
			}
			response.getWriter().print(flag);
			log.debug(this.getClass().getName() + " - [setSweepAccess] - " + "Exit");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [setSweepAccess] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp,request,"");
			return getView("fail");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "setSweepAccess", SweepQueueAction.class), request, "");
		} finally {
		}

		return null;

	}

}
