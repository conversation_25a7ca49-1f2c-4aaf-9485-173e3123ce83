/*
 * @(#)ILMAnalysisMonitorAction.java 1.0 29/11/2013
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential && proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information && shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.io.ByteArrayInputStream;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.sql.CallableStatement;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.TreeMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.jdbc.support.JdbcUtils;
import org.swallow.config.springMVC.BaseController;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.model.MovementColumnData;
import org.swallow.export.service.impl.ILMReporting;
import org.swallow.export.service.impl.ILMReporting.ColumnInfo;
import org.swallow.export.service.impl.MovementReport;
import org.swallow.export.service.impl.MovementReportCSV;
import org.swallow.export.service.impl.MovementReportExcel;
import org.swallow.export.service.impl.MovementReportPDF;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.model.core.TabInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.reports.model.ThroughputRatioReport;
import org.swallow.reports.service.ReportsManager;
import org.swallow.reports.web.TurnoverReportAction;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.ILMConfig;
import org.swallow.work.model.ILMSummaryRecord;
import org.swallow.work.model.Movement;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.model.ThroughputMonitorRecord;
import org.swallow.work.service.ILMAnalysisMonitorManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.work.service.WorkflowMonitorManager;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;


/**
 * This is the action class which caters to requests made from front end
 *
 * <AUTHOR> Ben Slama
 * @version 1.0 2013-11-29
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/ilmAnalysisMonitor", "/ilmAnalysisMonitor.do"})
public class ILMAnalysisMonitorAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("addProfile", "jsp/work/saveprofilepopup");
		viewMap.put("processStatus", "jsp/work/ilmanalysisprocessStatus");
		viewMap.put("grpcharts", "jsp/work/ilmanalysismonitorgrpchartsdata");
		viewMap.put("grptreeandgrid", "jsp/work/ilmanalysismonitorgrptreeandgrid");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("gbltreeandgrid", "jsp/work/ilmanalysismonitorglobaltreeandgrid");
		viewMap.put("fail", "error");
		viewMap.put("gblcharts", "jsp/work/ilmanalysismonitorglobalchartsdata");
		viewMap.put("dataerror", "jsp/work/ilmanalysismonitorflexdataerror");
		viewMap.put("flex", "jsp/work/ilmanalysismonitor");
		viewMap.put("ilmthroughputratiomonitorbreakdown", "jsp/work/ilmthroughputratiomonitorbreakdown");
		viewMap.put("success", "jsp/work/ilmanalysismonitormaindata");
		viewMap.put("ilmthroughputratiomonitor", "jsp/work/ilmthroughputratiomonitor");
		viewMap.put("options", "jsp/work/ilmoption");
		viewMap.put("seriesStyle", "jsp/work/seriesstylepopup");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "flex":
				return flex();
			case "unspecified":
				return unspecified();
			case "seriesStylePopup":
				return seriesStylePopup();
			case "addProfile":
				return addProfile();
			case "recalculateData":
				return recalculateData();
			case "displayILMScreen":
				return displayILMScreen();
			case "getGlobalTreeAndGridData":
				return getGlobalTreeAndGridData();
			case "getGlobalChartsData":
				return getGlobalChartsData();
			case "getGroupTreeAndGridData":
				return getGroupTreeAndGridData();
			case "getGroupChartsData":
				return getGroupChartsData();
			case "saveLiquidityMonitorConfig":
				return saveLiquidityMonitorConfig();
			case "saveBasicProfile":
				return saveBasicProfile();
			case "cancelILMExport":
				return cancelILMExport();
			case "exportLineChartDetails":
				return exportLineChartDetails();
			case "getCurrentPorcessState":
				return getCurrentPorcessState();
			case "getDataState":
				return getDataState();
			case "isAllEntityAvailable":
				return isAllEntityAvailable();
			case "stopRecalculationProcess":
				return stopRecalculationProcess();
			case "getProfileList":
				return getProfileList();
			case "getThroughputBreakdownDetails":
				return getThroughputBreakdownDetails();
			case "ilmThroughPutMonitor":
				return ilmThroughPutMonitor();
			case "ilmThroughPutRatioMonitorBreakdown":
				return ilmThroughPutRatioMonitorBreakdown();
			case "ilmThroughPutMonitorDisplay":
				return ilmThroughPutMonitorDisplay();
			case "ilmMonitorOptions":
				return ilmMonitorOptions();
			case "ilmGetSummaryTabList":
				return ilmGetSummaryTabList();
			case "ilmSummaryGridDisplay":
				return ilmSummaryGridDisplay();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "saveColumnHidden":
				return saveColumnHidden();
			case "saveColumnOrder":
				return saveColumnOrder();
			case "ilmThrouputBreakDownReport":
				return ilmThrouputBreakDownReport();
			case "ilmThrouputreport":
				return ilmThrouputreport();
			case "deleteProfile":
				return deleteProfile();
			case "getOptionData":
				return getOptionData();
			case "saveILMOption":
				return saveILMOption();
			case "updateILMOptions":
				return updateILMOptions();
			case "optionScreen":
				return optionScreen();
			case "saveThroughputRefreshRate":
				return saveThroughputRefreshRate();
		}


		return displayILMScreen();
	}


	//private String ENTITY_ID;
//public String getENTITY_ID() {
//	return ENTITY_ID;
//}
//public void setENTITY_ID(String ENTITY_ID) {
//	this.ENTITY_ID = ENTITY_ID;
//}
//private String CURRENCY_CODE;
//public String getCURRENCY_CODE() {
//	return CURRENCY_CODE;
//}
//public void setCURRENCY_CODE(String CURRENCY_CODE) {
//	this.CURRENCY_CODE = CURRENCY_CODE;
//}
//private String ACCOUNT_GROUP;
//public String getACCOUNT_GROUP() {
//	return ACCOUNT_GROUP;
//}
//public void setACCOUNT_GROUP(String ACCOUNT_GROUP) {
//	this.ACCOUNT_GROUP = ACCOUNT_GROUP;
//}
//private String SCENARIO_ID;
//public String getSCENARIO_ID() {
//	return SCENARIO_ID;
//}
//public void setSCENARIO_ID(String SCENARIO_ID) {
//	this.SCENARIO_ID = SCENARIO_ID;
//}
//private String CALCULATE_AS;
//public String getCALCULATE_AS() {
//	return CALCULATE_AS;
//}
//public void setCALCULATE_AS(String CALCULATE_AS) {
//	this.CALCULATE_AS = CALCULATE_AS;
//}
	public static String PDF_EXPORT = "pdf";
	public static String CSV_EXPORT = "csv";
	public static String XLS_EXPORT = "excel";
	public static String ACT_T_FOR_T = "1";
	public static String ACT_T_FOR_L = "2";
	public static String ACT_T_ACT_L = "3";
	/**
	 * ILMAnalysisMonitorManager Object
	 */
	@Autowired
	private ILMAnalysisMonitorManager ilmAnalysisMonitorManager = null;


	/**
	 * Setter method for ILM analysis manager
	 *
	 * @param ilmAnalysisMonitorManager
	 */
	public void setIlmAnalysisMonitorManager(ILMAnalysisMonitorManager ilmAnalysisMonitorManager) {
		this.ilmAnalysisMonitorManager = ilmAnalysisMonitorManager;
	}

	/**
	 * Initializing menuItemId.
	 */
	private final String menuItemId = "" + SwtConstants.SCREEN_ILMANALYSISMONITOR;

	/**
	 * Log object
	 */
	private static final Log log = LogFactory
			.getLog(ILMAnalysisMonitorAction.class);


	/**
	 * Called in the first load of screen to load the swf component
	 * <br><i>This method will redirect to ilmanalysismonitor.jsp file</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String flex()
			throws SwtException {

		String hostId = null;
		String roleId = null;
		String userId = null;
		String defaultEntityId = null;
		String defaultCcyId = null;
		Date sysDate = null;
		String sysDateAsString = null;
		String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
		ILMConfig ilmConf = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [flex] - starting ");
			// Get the host id from swtutil
			hostId = SwtUtil.getCurrentHostId();
			// Get the role id from common data manager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the current user id from swtutil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the default entity id from swtutil
			defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
			// get the default currency from SwtUtil
			defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
			// get the system date from swtutil
			sysDate = SwtUtil.getSystemDatewithoutTime();
			// get the formatted system date form swtutil
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			// Used to get the instance from Configuration file
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			// Setting the hostId in request
			request.setAttribute("hostId", hostId);
			// Setting the default entity id in request
			request.setAttribute("defaultEntityId", defaultEntityId);
			// Setting the default currency id in request
			request.setAttribute("defaultCcyId", defaultCcyId);
			// Setting the role id in request
			request.setAttribute("roleId", roleId);
			// Setting the user id in request
			request.setAttribute("userId", userId);
			// Setting the item id in request
			request.setAttribute("itemId", SwtConstants.ILMANALYSIS_MONITOR_ID);
			// Setting the system date as string in request
			request.setAttribute("sysDateAsString", sysDateAsString);
			ilmConf =  getLiquidityMonitorConfig(request, hostId, userId);
			// Setting the currency multiplier value
			request.setAttribute("useCcyMultiplier", ilmConf.getUseCurrencyMultiplier());
			// Set a unique sequence number will be used to stop the recalculation process
			request.setAttribute("uniqueSequenceNumber", SequenceFactory.getSequenceFromDb(SwtConstants.ILM_PROCESS_DRIVER_SEQUENCE_NAME));

			log.debug(this.getClass().getName() + "- [flex] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [flex] method : - "
					  + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"listCurrencyParams",
							ILMAnalysisMonitorAction.class), request, "");
		}finally{
			hostId = null;
			roleId = null;
			userId = null;
			defaultEntityId = null;
			defaultCcyId = null;
			sysDate = null;
			sysDateAsString = null;
			screenId = null;
			ilmConf = null;
		}
		return getView("flex");
	}


	public String unspecified()
			throws SwtException {

		return displayILMScreen();
	}


	/**
	 * This method is called to open the series Style Popup details screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 */
	public String seriesStylePopup() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					  + " - [seriesStylePopup] - " + "Entry");

			request.setAttribute("methodName",
					request.getParameter("methodName"));
			return getView("seriesStyle");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [seriesStylePopup] method : - "
					  + exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"seriesStylePopup",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("detailsdataerror");
		}
	}


	/**
	 * This method is called to open the series Style Popup details screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward
	 */
	public String addProfile() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					  + " - [addProfile] - " + "Entry");

			request.setAttribute("methodName",
					request.getParameter("methodName"));
			return getView("addProfile");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [addProfile] method : - "
					  + exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "."
					+ exp.getStackTrace()[0].getMethodName() + ":"
					+ exp.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"addProfile",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("detailsdataerror");
		}
	}


	/**
	 * Recalculate the ILM data using the default archive database
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String recalculateData()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String entityId = null;
		String currencyId = null;
		String activeArchiveDB = null;
		String recalculationResult = null;
		String selectedDateStr = null;
		String sequenceNumber = null;
		CommonDataManager CDM ;
		try {
			log.debug(this.getClass().getName() + "- [recaluclateData] - starting ");
			hostId = SwtUtil.getCurrentHostId();
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			if(CDM.getIlmScreenConnectionDetails() == null) {
				CDM.setIlmScreenConnectionDetails(new LinkedHashMap<String, Statement>());
			}
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedDateStr = request.getParameter("selectedDate");
			sequenceNumber = request.getParameter("sequenceNumber");
			activeArchiveDB = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			recalculationResult = ilmAnalysisMonitorManager.recalculateData(hostId, entityId, currencyId, selectedDateStr, activeArchiveDB,  sequenceNumber ,CDM);

			if(recalculationResult != null){
				if(recalculationResult.equals("Y")){
					request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
					request.setAttribute("reply_message", "Data fetch OK");
				}else {
					request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
					request.setAttribute("reply_message", SwtUtil.getMessage("ilmanalysismonitor.recalculationError",request));
				}
			}else {
				return null;
			}

			log.debug(this.getClass().getName() + "- [recaluclateData] - exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [recaluclateData] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			if(exp.getMessage() != null && exp.getMessage().equals(SwtConstants.ILM_DB_LINK_FAIL))
				request.setAttribute("reply_message", SwtUtil.getMessage("ilmccyparamsAdd.alert.dbLinkFailed", request));
			else
				request.setAttribute("reply_message", exp);
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"recaluclateData",
							ILMAnalysisMonitorAction.class), request, "");
		}finally{
			hostId = null;
			activeArchiveDB = null;
			recalculationResult = null;
		}
		return getView("dataerror");

	}


	/**
	 * Display the ILM screen with its default selection (entity, currency, date, timeframe, refresh && ccy multiplier)
	 * <br><i>This method will redirect to ilmanalysismonitormaindata.jsp file</i>
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayILMScreen()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String roleId = null;
		String userId = null;
		String defaultEntityId = null;
		String defaultCcyId = null;
		String sysDateAsString = null;
		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<EntityUserAccess> collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		Boolean currentEntityFound = false;
		String lastUsedProfile = null;
		ILMConfig ilmConf =  null;
		ScreenOptionManager screenOptionManager = null;
		Date sysDateInCcyTimeframe = null;
		boolean entityChanged = false;
		Date selectedDate = null;
		String dbLink = null;
		try {
			log.debug(this.getClass().getName() + "- [displayILMScreen] - starting ");
			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Retrieve the user from session
			userId = SwtUtil.getCurrentUserId(request.getSession());

			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			entitiesLblValue.addAll(entityAccessLvlColl);
			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityChanged")) && request.getParameter("entityChanged").equals("true"))
				entityChanged = true;

			// Retrieve the entity id selected by the user, if first load then it will be default entity
			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityId"))){
				defaultEntityId = request.getParameter("entityId");
			}else{
				String currentEntity = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getCurrentEntity();

				// Set currentEntityFound boolean to true if the current entity exists in the collUserEntityAccess list
				for (Iterator iterator = collUserEntityAccess.iterator(); iterator.hasNext();) {
					EntityUserAccess entityUserAccess = (EntityUserAccess) iterator.next();
					if (entityUserAccess.getEntityId().equals(currentEntity)){
						currentEntityFound = true;
					}
				}

				// the default entity will be the current entity if it exists in the list
				if (!currentEntityFound){
					// When we have more than one entity in the list
					if (collUserEntityAccess.size() > 0){
						defaultEntityId = collUserEntityAccess.iterator().next().getEntityId();
						// The case when we don't have access to any entity in the ILM screen, only All value is available
					}else{
						// A test will be in Flex, if the All value is selected then show an error without getting the data
						defaultEntityId = "All";
					}
				}else{
					defaultEntityId = currentEntity;
				}
			}

			// Retrieve the default currency id for the user
			if (SwtConstants.ALL_VALUE.equals(defaultEntityId)){
				defaultCcyId = request.getParameter("currencyId");
			}else{
//				defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
				defaultCcyId = request.getParameter("currencyId");
			}
			// Get the selected date with Date format
			sysDateAsString = request.getParameter("selectedDate");
			if (SwtUtil.isEmptyOrNull(sysDateAsString) || entityChanged){
				ReportsManager reportsManager = (ReportsManager) (SwtUtil
						.getBean("reportsManager"));
				sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(
						defaultEntityId, defaultCcyId, SwtUtil.getSystemDateFromDB());

				sysDateAsString = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}
			selectedDate = SwtUtil.parseDate(sysDateAsString, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			// Instantiate the ccyList
			ccyList = new ArrayList<LabelValueBean>();
			/*// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));*/
			// get the collection of currency access
			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));

			if (collUserEntityAccess != null)
				ccyList.addAll(ccyAccessColl);

			ILMConfig ilmGeneralConfig = getLiquidityMonitorConfig(request, hostId, userId);

			boolean entityExistInList = false;

			for (LabelValueBean o : ccyList){
				if(o.getValue().equals(defaultCcyId))
					entityExistInList = true;
			}
			if(!entityExistInList){
				if(ccyList != null && ccyList.size()>0)
					defaultCcyId = ccyList.iterator().next().getValue();
			}

			putLiquidityMonitorProfileListInRequest(request, hostId, userId, defaultEntityId, defaultCcyId);
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			lastUsedProfile = ilmGeneralConfig.getLastUsedProfiles().get(defaultEntityId + "_" + defaultCcyId);
			ilmConf = getILMProfileConfig(request, hostId, userId, defaultEntityId, defaultCcyId, lastUsedProfile);

			if(!SwtUtil.isEmptyOrNull(lastUsedProfile))
			{
				ScreenOption liquidityScreenOption = new ScreenOption();

				liquidityScreenOption.getId().setHostId(hostId);
				liquidityScreenOption.getId().setUserId(userId);
				liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
				liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(ilmConf));
				screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
				// Save the liquidityMonitorConfig in DB
				screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, defaultEntityId, defaultCcyId, null);

			}else {
				lastUsedProfile = "";
			}

			// set attribute for entity label value
			request.setAttribute("entities", entitiesLblValue);

			request.setAttribute("liquidityMonitorOptions", ilmGeneralConfig);

			request.setAttribute("currencies", ccyList);
			request.setAttribute("hostId", hostId);
			request.setAttribute("defaultEntityId", defaultEntityId);
			request.setAttribute("defaultCcyId", defaultCcyId);
			request.setAttribute("lastUsedProfile", lastUsedProfile);
			request.setAttribute("roleId", roleId);
			request.setAttribute("userId", userId);
			request.setAttribute("sysDateAsString", sysDateAsString);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffsetILM(request, defaultEntityId));
			request.setAttribute("currencyFormat", SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request, defaultEntityId, defaultCcyId);

			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			log.debug(this.getClass().getName() + "- [displayILMScreen] - exiting ");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [displayILMScreen] method : - "
					  + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"displayILMScreen",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [displayILMScreen] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayILMScreen",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		}finally{
			hostId = null;
			roleId = null;
			userId = null;
			defaultEntityId = null;
			defaultCcyId = null;
			sysDateAsString = null;
			entitiesLblValue = null;
			collUserEntityAccess = null;
			entityAccessLvlColl = null;
			ccyList = null;
			ccyAccessColl = null;
		}
		return getView("success");
	}

	/**
	 * Build the tree, the scenario && group grids data for the global analysis view.
	 * <br><i>Redirect to ilmanalysismonitorglobaltreeandgrid.jsp to get data</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getGlobalTreeAndGridData()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String entityId = null;
		String currencyId = null;
		String userId = null;
		String selectedDateTimeFame = null;
		Date selectedDate = null;
		String dbLink = null;
		String isGlobal = null;
		List scenariosDetailList = null;
		List grpsDetaillist = null;
		ILMConfig liquidityMonitorConfig = null;
		ILMConfig liquidityMonitorConfigGlobalView = null;
		String useCcyMultiplier = null;
		String currencyMutiplierValue = null;
		String currencyDecimalPlaces = null;
		String profileTreeData = null;
		String profileGroupGridData = null;
		String roleId = null;
		String[] currencyParams = null;
		String currencyParamsAsString = null;
		String includeOpenMvnts = null;
		String currentProfile = null;
		String noneLabel = null;
		Date sysDateInCcyTimeframe = null;
		Date convertedDateCcyTimeFrame = null;
		Date convertedDate = null;
		String ConvertedDateAsString = null;
		String DateSystemAsString = null;
		String previousEntity = null;
		String tempEntityId = null;
		String previousCurrency = null;
		Date sysDateAsCcyTimeFrame = null;
		String sysDateAsCcyTimeFrameAsString = null;
		try {
			log.debug(this.getClass().getName() + "- [getGlobalTreeAndGridData] - starting ");
			hostId = CacheManager.getInstance().getHostId();
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			useCcyMultiplier = request.getParameter("useCcyMultiplier");
			previousEntity = request.getParameter("previousEntity");
			previousCurrency = request.getParameter("previousCurrency");
			currentProfile = request.getParameter("currentProfile");
			includeOpenMvnts = request.getParameter("includeOpenMvnts");
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get the selected date with Date format
			selectedDateTimeFame = request.getParameter("selectedDate");

			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);
			if(noneLabel.equals(currentProfile)){
				currentProfile = "none";
			}

			ReportsManager reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));

			SimpleDateFormat format = new SimpleDateFormat(SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());
			Date dateSelectedDate = null;
			if(!SwtUtil.isEmptyOrNull(selectedDateTimeFame))
				dateSelectedDate = format.parse(selectedDateTimeFame);
			else
				dateSelectedDate = SwtUtil.getSystemDateFromDB();

			if ( previousEntity != null && previousCurrency != null) {

				if(SwtConstants.ALL_VALUE.equalsIgnoreCase(previousEntity))
					previousEntity = SwtUtil.getUserCurrentEntity(request.getSession());


				convertedDateCcyTimeFrame = reportsManager.getDateInCcyTimeframe(previousEntity, previousCurrency, SwtUtil.getSystemDateFromDB());
				ConvertedDateAsString = SwtUtil.formatDate(convertedDateCcyTimeFrame, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
				convertedDate = format.parse(ConvertedDateAsString);
			}

			if ((!SwtUtil.isEmptyOrNull(selectedDateTimeFame) ) && dateSelectedDate.equals(convertedDate)){


				if(SwtConstants.ALL_VALUE.equalsIgnoreCase(entityId))
					tempEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
				else
					tempEntityId = entityId;

				sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(tempEntityId, currencyId, SwtUtil.getSystemDateFromDB());

				selectedDateTimeFame = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}

			sysDateAsCcyTimeFrame = reportsManager.getDateInCcyTimeframe(entityId, currencyId, SwtUtil.getSystemDateFromDB());
			sysDateAsCcyTimeFrameAsString = SwtUtil.formatDate(sysDateAsCcyTimeFrame, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			// Retrieve the user from session
			userId = SwtUtil.getCurrentUserId(request.getSession());

			liquidityMonitorConfig = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN))
					.getLiquidityMonitorConfig();

			liquidityMonitorConfigGlobalView =  getILMProfileConfig(request, hostId, userId, entityId, currencyId, currentProfile);

			// Set the isGlobal to 'Y' This will be useful in the DB part to get only standard figures
			isGlobal = "Y";
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			if (SwtUtil.isEmptyOrNull(includeOpenMvnts)) {
				//get include open movement from profile if was set otherwise it should be set as default
				includeOpenMvnts = liquidityMonitorConfigGlobalView.getGlobalIncOpenMvnts();
			}

			if(!SwtUtil.isEmptyOrNull(selectedDateTimeFame)) {
				selectedDate  = format.parse(selectedDateTimeFame);
			}else {
				selectedDate  = dateSelectedDate;
			}

			// Get groups' detail to build the group grid
			grpsDetaillist = ilmAnalysisMonitorManager.getGroupsDetailForGrid(hostId, entityId, currencyId, selectedDate, userId, dbLink, isGlobal, includeOpenMvnts);
			// Get the scenarios'detail to build the scenario grid
			scenariosDetailList = ilmAnalysisMonitorManager.getScenariosDetail(hostId, entityId, currencyId, userId, isGlobal);
			String groupName = null;
			String lastUpdate = null;
			for(int i = 0; i < grpsDetaillist.size() ; i++) {
				groupName = ((ILMAccountGroup) grpsDetaillist.get(i)).getId().getIlmGroupId();
				lastUpdate =((ILMAccountGroup) grpsDetaillist.get(i)).getLastUpdated();
				if(lastUpdate!= null){
					((ILMAccountGroup) grpsDetaillist.get(i)).setLastUpdated((SwtUtil.formatDate(lastUpdate, SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue() + " HH:mm:ss")));
				}
				if(SwtConstants.ILM_ALL_GLOBAL_GROUPS_ID.equals(groupName) || SwtConstants.ILM_ALL_ALT_GLOBAL_GROUPS_ID.equals(groupName) || SwtConstants.ILM_ALL_ALT_ONLY_GROUPS_ID.equals(groupName) ){
					((ILMAccountGroup) grpsDetaillist.get(i)).setDefaultLegendText("I");
				}
				if(i != 0) {
					((ILMAccountGroup) grpsDetaillist.get(i)).setGlobal("N");
				}

			}
			request.setAttribute("grpsDetaillist", grpsDetaillist);
			request.setAttribute("scenariosDetailList", scenariosDetailList);
			request.setAttribute("treeGrpsDetail", grpsDetaillist);
			request.setAttribute("treeScenariosDetail", scenariosDetailList);
			request.setAttribute("grpsDetaillistForBalance", grpsDetaillist);


			if (liquidityMonitorConfig != null){
				bindColumnWidthInRequest(request, "grpColWidth", liquidityMonitorConfig.getGblGrpColWidth());
				bindColumnWidthInRequest(request, "scenColWidth", liquidityMonitorConfig.getGblScnColWidth());
				if (!SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getGblBalColWidth()))
					bindColumnWidthInRequest(request, "balColWidth", liquidityMonitorConfig.getGblBalColWidth());
				else
					bindColumnWidthInRequest(request, "balColWidth", "use=40,group=190,extsod=70,exteod=70,fcastsod=80,fcasteod=80,openunexp=100,openunsett=100,lastupdate=130");

				bindColumnOrderInRequest( request, "grpColOrder", liquidityMonitorConfig.getGblGrpColOrder());
				bindColumnOrderInRequest( request, "scenColOrder", liquidityMonitorConfig.getGblScnColOrder());

				if (!SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getGblBalColOrder()))
					bindColumnOrderInRequest( request, "balColOrder", liquidityMonitorConfig.getGblBalColOrder());
				else
					bindColumnOrderInRequest( request, "balColOrder", "use,group,extsod,exteod,fcastsod,fcasteod,openunexp,openunsett,lastupdate");
			}

			ILMConfig ilmProfile = getILMProfileConfig(request, hostId, userId, entityId, currencyId, currentProfile);
			if(ilmProfile != null && ilmProfile.getGlobalTree()!= null){
				profileTreeData = ilmProfile.getGlobalTree();
				request.setAttribute("profileTreeData", profileTreeData);
			}else {
				request.setAttribute("profileTreeData", "");
			}

			if(ilmProfile != null && ilmProfile.getGlobalGroupGrid()!= null){
				profileGroupGridData = ilmProfile.getGlobalGroupGrid();
				request.setAttribute("profileGlobalGroupGrid", profileGroupGridData);
			}else {
				request.setAttribute("profileGlobalGroupGrid", "");
			}
			//Once the selected entity is equal to "All" we use the user's default entity to get the currency multiplier settings
			if(SwtConstants.ALL_VALUE.equalsIgnoreCase(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			//Get currency params from DataBase : Currency multiplier (thousands, millions , billions) , Currency Decimal places (1, 2, 3, ...);
			currencyParamsAsString = getCcyMultiplierAndDecimalPlaces(entityId, currencyId);
			if(currencyParamsAsString != null) {
				currencyParams = currencyParamsAsString.split(",");
				currencyDecimalPlaces = currencyParams[0];
				currencyMutiplierValue = currencyParams[1];
			}

			request.setAttribute("currencyDecimalPlaces", currencyDecimalPlaces);
			request.setAttribute("currencyMutiplierValue", currencyMutiplierValue);

			// Set the row size for the group grid
			request.setAttribute("grpRowSize", grpsDetaillist.size());
			// Set the row size for the scenario grid
			request.setAttribute("scnRowSize", scenariosDetailList.size());
			// Set the currency timeframe
			request.setAttribute("timeframe", getCurrencyTimeframe(entityId, currencyId, selectedDate, roleId));
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffsetILM(request, entityId));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			request.setAttribute("selectedDateTimeFame", selectedDateTimeFame);
			request.setAttribute("sysDateWithCcyTimeFrame", sysDateAsCcyTimeFrameAsString);
			request.setAttribute("entityId", request.getParameter("entityId"));
			request.setAttribute("currencyId", request.getParameter("currencyId"));

			log.debug(this.getClass().getName() + "- [getGlobalTreeAndGridData] - exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getGlobalTreeAndGridData] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			if(exp.getMessage() != null && exp.getMessage().equals(SwtConstants.ILM_DB_LINK_FAIL))
				request.setAttribute("reply_message", SwtUtil.getMessage("ilmccyparamsAdd.alert.dbLinkFailed", request));
			else
			{
				request.setAttribute("reply_message",  SwtUtil.getMessage("label.errorContactSystemAdmin",request)+ "\n"+  exp);
				request.setAttribute("reply_location", exp.getStackTrace()[0]
															   .getClassName()
													   + "."
													   + exp.getStackTrace()[0].getMethodName()
													   + ":"
													   + exp.getStackTrace()[0].getLineNumber());
			}
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getGlobalTreeAndGridData",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} finally {
			hostId = null;
			entityId = null;
			currencyId = null;
			userId = null;
			selectedDateTimeFame = null;
			selectedDate = null;
			dbLink = null;
			isGlobal = null;
			scenariosDetailList = null;
			grpsDetaillist = null;
			liquidityMonitorConfig = null;
			useCcyMultiplier = null;
			currencyMutiplierValue = null;
			currencyDecimalPlaces = null;
		}
		return getView("gbltreeandgrid");
	}

	/**
	 * Build The charts data for global analysis view.
	 * <br><i>Redirect to ilmanalysismonitorglobalchartsdata.jsp to get data</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getGlobalChartsData()
			throws SwtException {
		return getGroupChartsData();
	}

	/**
	 * Build the tree, the scenario && group grids data for the group analysis view.
	 * <br><i>Redirect to ilmanalysismonitorgrptreeandgrid.jsp to get data</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getGroupTreeAndGridData()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String entityId = null;
		String currencyId = null;
		String userId = null;
		String selectedDateStr = null;
		Date selectedDate = null;
		String dbLink = null;
		String isGlobal = null;
		List grpsDetaillist = null;
		List scenariosDetailList = null;
		ILMConfig liquidityMonitorConfig = null;
		ILMConfig liquidityMonitorConfigAnalysis = null;
		String useCcyMultiplier = null;
		String currencyMutiplierValue = null;
		String currencyDecimalPlaces = null;
		String profileTreeData = null;
		String profileGroupGrid = null;
		String profileScenarioGrid = null;
		String roleId = null;
		String[] currencyParams = null;
		String currencyParamsAsString = null;
		String includeOpenMvnts = null;
		String currentProfile = null;
		String noneLabel = null;

		String selectedDateTimeFame = null;

		Date sysDateInCcyTimeframe = null;
		Date convertedDate = null;
		String tempEntityId = null;
		Date sysDateAsCcyTimeFrame = null;
		String sysDateAsCcyTimeFrameAsString = null;

		try {
			log.debug(this.getClass().getName() + "- [getGroupTreeAndGridData] - starting ");

			hostId = CacheManager.getInstance().getHostId();
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedDateStr = request.getParameter("selectedDate");
			useCcyMultiplier = request.getParameter("useCcyMultiplier");
			currentProfile = request.getParameter("currentProfile");
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get the selected date with Date format
			selectedDateStr = request.getParameter("selectedDate");
			selectedDateTimeFame = request.getParameter("selectedDate");
			selectedDateStr= URLDecoder.decode(selectedDateStr,"UTF-8");
			if (!SwtUtil.isEmptyOrNull(selectedDateStr))
				selectedDate = SwtUtil.parseDate(selectedDateStr, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());

			// Retrieve the user from session
			userId = SwtUtil.getCurrentUserId(request.getSession());

			// Set the isGlobal to 'N' This will be useful in the DB part to get only standard && group figures
			isGlobal = "N";

			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);

			if(noneLabel.equals(currentProfile)){
				currentProfile = "none";
			}


			ReportsManager reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));

			SimpleDateFormat format = new SimpleDateFormat(SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());
			Date dateSelectedDate = null;
			if(!SwtUtil.isEmptyOrNull(selectedDateTimeFame))
				dateSelectedDate = format.parse(selectedDateTimeFame);
			else
				dateSelectedDate = SwtUtil.getSystemDateFromDB();

//			if ( previousEntity != null && previousCurrency != null) {
//
//				if(SwtConstants.ALL_VALUE.equalsIgnoreCase(previousEntity))
//					previousEntity = SwtUtil.getUserCurrentEntity(request.getSession());
//
//
//				convertedDateCcyTimeFrame = reportsManager.getDateInCcyTimeframe(previousEntity, previousCurrency, SwtUtil.getSystemDateFromDB());
//				ConvertedDateAsString = SwtUtil.formatDate(convertedDateCcyTimeFrame, SwtUtil
//						.getCurrentSystemFormats(request.getSession())
//						.getDateFormatValue());
//				convertedDate = format.parse(ConvertedDateAsString);
//			}

			if ((!SwtUtil.isEmptyOrNull(selectedDateTimeFame) ) && dateSelectedDate.equals(convertedDate)){


				if(SwtConstants.ALL_VALUE.equalsIgnoreCase(entityId))
					tempEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
				else
					tempEntityId = entityId;

				sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(tempEntityId, currencyId, SwtUtil.getSystemDateFromDB());

				selectedDateTimeFame = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}

			sysDateAsCcyTimeFrame = reportsManager.getDateInCcyTimeframe(entityId, currencyId, SwtUtil.getSystemDateFromDB());
			sysDateAsCcyTimeFrameAsString = SwtUtil.formatDate(sysDateAsCcyTimeFrame, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());


			liquidityMonitorConfig = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN))
					.getLiquidityMonitorConfig();


			liquidityMonitorConfigAnalysis = getILMProfileConfig(request, hostId, userId, entityId, currencyId, currentProfile);


			includeOpenMvnts = request.getParameter("includeOpenMvnts");
			if (SwtUtil.isEmptyOrNull(includeOpenMvnts)) {
				includeOpenMvnts = liquidityMonitorConfigAnalysis.getAnalysisIncOpenMvnts();
			}
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			// Get groups' detail to build the group grid
			grpsDetaillist = ilmAnalysisMonitorManager.getGroupsDetailForGrid(hostId, entityId, currencyId, selectedDate, userId, dbLink, isGlobal, includeOpenMvnts);
			Iterator itr = grpsDetaillist.iterator();
			ILMAccountGroup accountGroup = null;
			while (itr.hasNext()) {
				accountGroup = (ILMAccountGroup ) itr.next();
				if(accountGroup.getLastUpdated()!= null){
					accountGroup.setLastUpdated((SwtUtil.formatDate(accountGroup.getLastUpdated(), SwtUtil.getCurrentSystemFormats(
							request.getSession()).getDateFormatValue() + " HH:mm:ss")));
				}
			}

			// Get the scenarios'detail to build the scenario grid
			scenariosDetailList = ilmAnalysisMonitorManager.getScenariosDetail(hostId, entityId, currencyId, userId, isGlobal);

			request.setAttribute("grpsDetaillist", grpsDetaillist);
			request.setAttribute("scenariosDetailList", scenariosDetailList);
			request.setAttribute("treeGrpsDetail", grpsDetaillist);
			request.setAttribute("grpsDetaillistForBalance", grpsDetaillist);
			request.setAttribute("treeScenariosDetail", scenariosDetailList);


			if (liquidityMonitorConfig != null){
				bindColumnWidthInRequest(request, "grpColWidth", liquidityMonitorConfig.getGrpGrpColWidth());
				bindColumnWidthInRequest(request, "scenColWidth", liquidityMonitorConfig.getGrpScnColWidth());
				if (!SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getGrpBalColWidth()))
					bindColumnWidthInRequest(request, "balColWidth", liquidityMonitorConfig.getGrpBalColWidth());
				else
					bindColumnWidthInRequest(request, "balColWidth", "use=40,group=190,extsod=70,exteod=70,fcastsod=80,fcasteod=80,openunexp=100,openunsett=100,,lastupdate=130");

				bindColumnOrderInRequest( request, "grpColOrder" , liquidityMonitorConfig.getGrpGrpColOrder());
				bindColumnOrderInRequest( request, "scenColOrder" , liquidityMonitorConfig.getGrpScnColOrder());
				if (!SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getGrpBalColOrder()))
					bindColumnOrderInRequest( request, "balColOrder" , liquidityMonitorConfig.getGrpBalColOrder());
				else
					bindColumnOrderInRequest( request, "balColOrder" , "use,group,extsod,exteod,fcastsod,fcasteod,openunexp,openunsett,lastupdate");
			}
			//Once the selected entity is equal to "All" ,we use the user's default entity to get the currency multiplier settings
			if(SwtConstants.ALL_VALUE.equalsIgnoreCase(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			//Get currency params from DataBase : Currency multiplier (thousands, millions , billions) , Currency Decimal places (1, 2, 3, ...);
			currencyParamsAsString = getCcyMultiplierAndDecimalPlaces(entityId, currencyId);
			if(currencyParamsAsString != null) {
				currencyParams = currencyParamsAsString.split(",");
				currencyDecimalPlaces = currencyParams[0];
				currencyMutiplierValue = currencyParams[1];
			}

			ILMConfig ilmProfile = getILMProfileConfig(request, hostId, userId, entityId, currencyId, currentProfile);
			profileTreeData = ilmProfile.getAnalysisTree();
			profileGroupGrid = ilmProfile.getGroupGrid();
			profileScenarioGrid = ilmProfile.getScenarioGrid();

			request.setAttribute("profileTreeData", profileTreeData);
			request.setAttribute("profileGroupGrid", profileGroupGrid);
			request.setAttribute("profileScenarioGrid", profileScenarioGrid);
			request.setAttribute("currencyDecimalPlaces", currencyDecimalPlaces);
			request.setAttribute("currencyMutiplierValue", currencyMutiplierValue);
			// Set the row size for the group grid
			request.setAttribute("grpRowSize", grpsDetaillist.size());
			// Set the row size for the scenario grid
			request.setAttribute("scnRowSize", scenariosDetailList.size());
			// Set the currency timeframe
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffsetILM(request, entityId));

			request.setAttribute("selectedDateTimeFame", selectedDateTimeFame);
			request.setAttribute("sysDateWithCcyTimeFrame", sysDateAsCcyTimeFrameAsString);
			request.setAttribute("timeframe", getCurrencyTimeframe(entityId, currencyId, selectedDate, roleId));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + "- [getGroupTreeAndGridData] - exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getGroupTreeAndGridData] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			if(exp.getMessage() != null && exp.getMessage().equals(SwtConstants.ILM_DB_LINK_FAIL))
				request.setAttribute("reply_message", SwtUtil.getMessage("ilmccyparamsAdd.alert.dbLinkFailed", request));
			else
			{
				request.setAttribute("reply_message",  SwtUtil.getMessage("label.errorContactSystemAdmin",request)+ "\n"+  exp);
				request.setAttribute("reply_location", exp.getStackTrace()[0]
															   .getClassName()
													   + "."
													   + exp.getStackTrace()[0].getMethodName()
													   + ":"
													   + exp.getStackTrace()[0].getLineNumber());
			}
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getGroupTreeAndGridData",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} finally {
			hostId = null;
			entityId = null;
			currencyId = null;
			userId = null;
			selectedDateStr = null;
			selectedDate = null;
			dbLink = null;
			isGlobal = null;
			grpsDetaillist = null;
			scenariosDetailList = null;
			liquidityMonitorConfig = null;
			useCcyMultiplier = null;
			currencyMutiplierValue = null;
			currencyDecimalPlaces = null;
		}
		return getView("grptreeandgrid");
	}

	/**
	 * Build The charts data for group analysis view.
	 * <br><i>Redirect to ilmanalysismonitorgrpchartsdata.jsp to get data</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getGroupChartsData()
			throws SwtException {		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String hostId = null;
		String entityId = null;
		String currencyId = null;
		String userId = null;
		Date selectedDate = null;
		String selectedDateStr = null;
		String useCcyMultiplier = null;
		ILMAccountGroup ilmAccntGroup = null;
		ILMScenario ilmScenario = null;
		HashMap selectedGrpScens = null;
		Set<String> selectedGroups = null;
		String dbLink = null;
		String selectedFigures = null;
		String datasets = null;
		String metadatas = null;
		Iterator itr = null;
		String group = null;
		String groupForLegend = null;
		String scenarioForLegend = null;
		String scenario = null;
		Iterator itrScn = null;
		List<String> scenList = null;
		String nowEntityTime = "";
		String nowCurrencyTime = "";
		boolean globalChart = false;
		HashMap<String, String> chartStyles =null;
		ILMConfig liquidityMonitorConfig = null;
		HashMap<String, String> selectedFiguresTooltips =null;
		String sumByCutOff = null;
		String includeOpenMvnts = null;
		String currentProfile = null;
		String noneLabel = null;
		ILMConfig generalConfig = null;
		String timeRangeAsString = null;
		String dataState = null;
		try {
			long timeStart = System.currentTimeMillis();
			log.debug(this.getClass().getName() + "- [getGroupChartsData] - starting ");
			scenList = new ArrayList<String>();
			hostId  = SwtUtil.getCurrentHostId(request.getSession());
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			currentProfile = request.getParameter("currentProfile");
			globalChart = request.getParameter("globalChart") != null?(request.getParameter("globalChart").equals("true")?true:false):false;
			// Retrieve the user from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);
			generalConfig =  getLiquidityMonitorConfig(request, hostId, userId);

			if(noneLabel.equals(currentProfile)){
				currentProfile = "none";

			}else if ("<last>".equals(currentProfile)){
				currentProfile = generalConfig.getLastUsedProfiles().get(entityId + "_" + currencyId);
			}

			if("none".equals(currentProfile) || SwtUtil.isEmptyOrNull(currentProfile)){
				ILMGeneralMaintenanceManager ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));

				// Get the ILMCcyParameters editable data
				ILMCcyParameters ilmCcyParameters = ilmGenMgr
						.getILMCcyParameterEditableData(hostId, entityId,
								currencyId);
				String startPoint = null;
				String endPoint = null;
				String[] tempTable = null;
				SimpleDateFormat parser = new SimpleDateFormat("HH:mm");
				String startTimeToCompare = "00:10";
				String endTimeToCompare = "23:50";

				if (ilmCcyParameters != null) {
					if(ilmCcyParameters.getClearingStartTime() != null) {
						Date maxStartDateTime = parser.parse(startTimeToCompare);
						Date actualStartDateTime = parser.parse(ilmCcyParameters.getClearingStartTime());

						if(actualStartDateTime.after(maxStartDateTime)) {
							tempTable = ilmCcyParameters.getClearingStartTime().split(":");
							startPoint =""+( Integer.parseInt(tempTable[0]) * 60 + Integer.parseInt(tempTable[1]) - 10 );
						}else {
							startPoint  = "0";
						}
					}else {
						startPoint  = "0";
					}

					if(ilmCcyParameters.getClearingEndTime() != null) {
						Date maxEndDateTime = parser.parse(endTimeToCompare);
						Date actualEndDateTime = parser.parse(ilmCcyParameters.getClearingEndTime());

						if("00:00".equals(ilmCcyParameters.getClearingEndTime()) || actualEndDateTime.after(maxEndDateTime))
							endPoint ="1440";
						else {
							tempTable = ilmCcyParameters.getClearingEndTime().split(":");
							endPoint =""+( Integer.parseInt(tempTable[0]) * 60 + Integer.parseInt(tempTable[1]) + 10);
						}
					}else {
						endPoint  = "1440";
					}
				} else {
					startPoint  = "0";
					endPoint  = "1440";
				}

				timeRangeAsString = startPoint + ","+ endPoint;
			}

			if(SwtUtil.isEmptyOrNull(currentProfile)  || "*".equals(currentProfile))
				currentProfile = "";


			// Get the selected date with Date format
			selectedDateStr = request.getParameter("selectedDate");
			selectedDateStr = URLDecoder.decode(selectedDateStr,"UTF-8");
			sumByCutOff = request.getParameter("sumByCutOff");
			includeOpenMvnts = request.getParameter("includeOpenMvnts");
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			if (!SwtUtil.isEmptyOrNull(selectedDateStr))
				selectedDate = SwtUtil.parseDate(selectedDateStr, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());


			// Get the use currency multiplier value it will be N || Y to be used in DB
			useCcyMultiplier = request.getParameter("useCcyMultiplier");
			// Get the selected figures (the form of this string will be grp1:scn1|grp1:scn2 ...)
			selectedFigures = request.getParameter("selectedFigures");

			selectedFigures = URLDecoder.decode(selectedFigures,"UTF-8");
			selectedFiguresTooltips = ilmAnalysisMonitorManager.getGroupsAndScenarioNames(entityId, currencyId);
			// Get the datasets for the selected figures
			if (SwtUtil.isEmptyOrNull(selectedFigures)) {
				// We don't have any group/scenario, then we have to set the empty values
				metadatas = "";
				datasets = "";
			} else {
				selectedGrpScens = getSelectedGroups(selectedFigures);
				selectedGroups = selectedGrpScens.keySet();
				// Get liquidityMonitorConfig from the session
				liquidityMonitorConfig = /*((CommonDataManager) request.getSession()
									.getAttribute(SwtConstants.CDM_BEAN))
									.getLiquidityMonitorConfig();//*/getILMProfileConfig(request, hostId, userId, entityId, currencyId, currentProfile);

				if (SwtUtil.isEmptyOrNull(includeOpenMvnts)) {
					// Get include open movement from profile if was set otherwise it should be set as default
					if (globalChart) {
						includeOpenMvnts = liquidityMonitorConfig.getGlobalIncOpenMvnts();
					} else {
						includeOpenMvnts = liquidityMonitorConfig.getAnalysisIncOpenMvnts();
					}
				}

				String seriesStyle = null;

				DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
				DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
				Document xmlDoc = documentBuilder.newDocument();


				Element root_Metatdatas = xmlDoc.createElement("metadatas");

				itr	= selectedGroups.iterator();
				if (globalChart) {
					chartStyles =liquidityMonitorConfig.getGlobalChartStyles();
				} else {
					chartStyles =liquidityMonitorConfig.getAnalysisChartStyles();
				}
				List<ILMAccountGroup> grpsDetaillist = null;
				while (itr.hasNext()) {
					group = (String) itr.next();
					Element metadata = null;
					if (selectedGrpScens.get(group) != null) {
						// Get the details for selected groups, it will be used to retrieve the thresholds values
						if(SwtConstants.ILM_ALL_GLOBAL_GROUPS_ID.equals(group) || SwtConstants.ILM_ALL_ALT_GLOBAL_GROUPS_ID.equals(group) || SwtConstants.ILM_ALL_ALT_ONLY_GROUPS_ID.equals(group) ){
							if(grpsDetaillist == null)
								grpsDetaillist = ilmAnalysisMonitorManager.getGroupsDetailForGrid(hostId, entityId, currencyId, selectedDate, userId, dbLink, "Y", includeOpenMvnts);

							Iterator<ILMAccountGroup> itGroups = grpsDetaillist.iterator();
							while(itGroups.hasNext()) {
								ILMAccountGroup element = itGroups.next();
								if(group.equals(element.getId().getIlmGroupId()))
									ilmAccntGroup = element;
							}
						}else{
							ilmAccntGroup = ilmAnalysisMonitorManager.getILMGroupDetails(group);
						}

						// If the default legend text is defined as N (Name), then the value shown in the legend should be the name
						if (ilmAccntGroup!= null && (SwtUtil.isEmptyOrNull(ilmAccntGroup.getDefaultLegendText()) || "I".equals(ilmAccntGroup.getDefaultLegendText()))) {
							groupForLegend = ilmAccntGroup.getId().getIlmGroupId();
						} else if (ilmAccntGroup!= null && "N".equals(ilmAccntGroup.getDefaultLegendText())) {
							groupForLegend = SwtUtil.isEmptyOrNull(ilmAccntGroup.getIlmGroupName())
									? ilmAccntGroup.getId().getIlmGroupId()
									: ilmAccntGroup.getIlmGroupName();
						}
						metadata = xmlDoc.createElement("metadata");
						metadata.setAttribute("groupId", group);

						scenList = (List<String>) selectedGrpScens.get(group);
						itrScn = scenList.iterator();

						while(itrScn.hasNext()) {
							scenario = (String) itrScn.next();
							Element charts = xmlDoc.createElement("charts");
							charts.setAttribute("scenarioId", scenario);
							charts.setAttribute("currencyId", currencyId);
							charts.setAttribute("entityId", entityId);

							ilmScenario = ilmAnalysisMonitorManager.getILMScenarioDetails(scenario);
							if (!"Standard".equals(scenario)) {
								if (SwtUtil.isEmptyOrNull(ilmScenario.getDefaultLegendText()) || "I".equals(ilmScenario.getDefaultLegendText())) {
									scenarioForLegend = ilmScenario.getId().getIlmScenarioId();
								} else if ("N".equals(ilmScenario.getDefaultLegendText())) {
									scenarioForLegend = SwtUtil.isEmptyOrNull(ilmScenario.getIlmScenarioName())
											? ilmScenario.getId().getIlmScenarioId()
											: ilmScenario.getIlmScenarioName();
								}
							}

							// Accumulated actual Credit series
							Element aacChart = xmlDoc.createElement("chart");
							aacChart.setAttribute("type", "area");
							aacChart.setAttribute("dataelement", "aac");
							if (("Standard").equals(scenario)) {
								aacChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accActualC.Title", request) + groupForLegend);
								aacChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							} else {
								aacChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accActualC.Title", request) + scenarioForLegend + "." + groupForLegend);
								aacChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario+".aac") != null)
								seriesStyle = chartStyles.get(group + "." + scenario+".aac");
							else
								seriesStyle = liquidityMonitorConfig.ACC_ACTUAL_CREDITS_DEFAULT;

							aacChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(aacChart);

							// Accumulated actual Credit series
							Element aadChart = xmlDoc.createElement("chart");
							aadChart.setAttribute("type", "area");
							aadChart.setAttribute("dataelement", "aad");
							if (("Standard").equals(scenario)) {
								aadChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accActualD.Title", request) + groupForLegend);
								aadChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							} else {
								aadChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accActualD.Title", request) + scenarioForLegend + "." + groupForLegend);
								aadChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario + ".aad") != null)
								seriesStyle = chartStyles.get(group + "." + scenario + ".aad");
							else
								seriesStyle = liquidityMonitorConfig.ACC_ACTUAL_DEBITS_DEFAULT;

							aadChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(aadChart);

							// Accumulated forecast debit series
							Element afdChart = xmlDoc.createElement("chart");
							afdChart.setAttribute("type", "area");
							afdChart.setAttribute("dataelement", "afd");
							if (("Standard").equals(scenario)) {
								afdChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accForeD.Title", request) + groupForLegend);
								afdChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							} else {
								afdChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accForeD.Title", request) + scenarioForLegend + "." + groupForLegend);
								afdChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario+".afd") != null)
								seriesStyle = chartStyles.get(group + "." + scenario + ".afd");
							else
								seriesStyle = liquidityMonitorConfig.ACC_FORECAST_DEBITS_DEFAULT;

							afdChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(afdChart);

							// Accumulated forecast credit series
							Element afcChart = xmlDoc.createElement("chart");
							afcChart.setAttribute("type", "area");
							afcChart.setAttribute("dataelement", "afc");
							if (("Standard").equals(scenario)) {
								afcChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accForeC.Title", request) + groupForLegend);
								afcChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							} else {
								afcChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.accForeC.Title", request) + scenarioForLegend + "." + groupForLegend);
								afcChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario+".afc")!=null)
								seriesStyle = chartStyles.get(group + "." + scenario+".afc");
							else
								seriesStyle = liquidityMonitorConfig.ACC_FORECAST_CREDITS_DEFAULT;

							afcChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(afcChart);

							// Actual balance series
							Element abChart = xmlDoc.createElement("chart");
							abChart.setAttribute("type", "line");
							abChart.setAttribute("dataelement", "ab");
							if (("Standard").equals(scenario)) {
								abChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.actBalance.Title", request) + groupForLegend);
								abChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							} else {
								abChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.actBalance.Title", request) + scenarioForLegend + "." + groupForLegend);
								abChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario+".ab") != null)
								seriesStyle = chartStyles.get(group + "." + scenario+".ab");
							else
								seriesStyle = liquidityMonitorConfig.ACT_BALANCE_DEFAULT;

							abChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(abChart);

							// Forecast basic balance series
							Element fbbChart = xmlDoc.createElement("chart");
							fbbChart.setAttribute("type", "line");
							fbbChart.setAttribute("dataelement", "fbb");
							if (("Standard").equals(scenario)) {
								fbbChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.forebasic.Title", request) + groupForLegend);
								fbbChart.setAttribute("legendTooltip",  getGroupName(ilmAccntGroup));
							} else {
								fbbChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.forebasic.Title", request) + scenarioForLegend + "." + groupForLegend);
								fbbChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario + ".fbb") != null)
								seriesStyle = chartStyles.get(group + "." + scenario + ".fbb");
							else
								seriesStyle = liquidityMonitorConfig.FC_BAL_BASIC_DEFAULT;

							fbbChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(fbbChart);

							// Forecast include actuals series
							Element fbiaChart = xmlDoc.createElement("chart");
							fbiaChart.setAttribute("type", "line");
							fbiaChart.setAttribute("dataelement", "fbia");
							if (("Standard").equals(scenario)) {
								fbiaChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.forecIncludeAct.Title", request) + groupForLegend);
								fbiaChart.setAttribute("legendTooltip", getGroupName(ilmAccntGroup));
							}
							else {
								fbiaChart.setAttribute("chartId", SwtUtil.getMessage("ilmanalysismonitor.legend.forecIncludeAct.Title", request) + scenarioForLegend + "." + groupForLegend);
								fbiaChart.setAttribute("legendTooltip", getScenarioName(ilmScenario) + "." + getGroupName(ilmAccntGroup));
							}

							if (chartStyles.get(group + "." + scenario+".fbia") != null)
								seriesStyle = chartStyles.get(group + "." + scenario + ".fbia");
							else
								seriesStyle = liquidityMonitorConfig.FC_BA_INCL_ACTUALS_DEFAULT;

							fbiaChart.setAttribute("seriesStyle", seriesStyle);
							charts.appendChild(fbiaChart);

							metadata.appendChild(charts);
						}

						// Add the threshold tag for metadata, only one tag for each group
						Element thresholds = xmlDoc.createElement("thresholds");

						// Get the details for selected groups, it will be used to retrieve the thresholds values
						ilmAccntGroup = ilmAnalysisMonitorManager.getGroupDetails(hostId, entityId, currencyId, group);
						Element min1 = xmlDoc.createElement("min1");
						min1.appendChild(xmlDoc.createTextNode(ilmAccntGroup.getThresholdMin1()));
						thresholds.appendChild(min1);
						Element min2 = xmlDoc.createElement("min2");
						min2.appendChild(xmlDoc.createTextNode(ilmAccntGroup.getThresholdMin2()));
						thresholds.appendChild(min2);
						Element max1 = xmlDoc.createElement("max1");
						max1.appendChild(xmlDoc.createTextNode(ilmAccntGroup.getThresholdMax1()));
						thresholds.appendChild(max1);
						Element max2 = xmlDoc.createElement("max2");
						max2.appendChild(xmlDoc.createTextNode(ilmAccntGroup.getThresholdMax2()));
						thresholds.appendChild(max2);
						// Add the threshold tag to the metadata
						metadata.appendChild(thresholds);

					}
					root_Metatdatas.appendChild(metadata);

				}
				if(SwtUtil.isEmptyOrNull(sumByCutOff)){
					if(globalChart)
						sumByCutOff = liquidityMonitorConfig.getGlobalSumByCutOff();
					else
						sumByCutOff = liquidityMonitorConfig.getAnalysisSumByCutOff();
				}


				// Get the whole dataset
				datasets = ilmAnalysisMonitorManager.getChartsData(hostId, entityId, currencyId, selectedDate, userId, dbLink, selectedFigures, sumByCutOff, includeOpenMvnts);
				// Get the now times
				String[] nowDates = ilmAnalysisMonitorManager.getNowDates(entityId, currencyId);
				if (nowDates != null && nowDates.length == 2) {
					nowCurrencyTime = nowDates[0];
					nowEntityTime =nowDates[1];
				}

				xmlDoc.appendChild(root_Metatdatas);
				metadatas = SwtUtil.convertDocumentToString(xmlDoc);

			}
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			request.setAttribute("metadatas", metadatas);
			request.setAttribute("datasets", datasets);
			request.setAttribute("liquidityMonitorOptions", generalConfig);
			request.setAttribute("nowCurrencyTime", nowCurrencyTime);
			request.setAttribute("nowEntityTime", nowEntityTime);

			if (globalChart) {
				if (liquidityMonitorConfig!=null) {
					if(timeRangeAsString != null && SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getGlobalTimeRange()))
						request.setAttribute("globalTimeRange",SwtUtil.encode64(timeRangeAsString));
					else {

						String globalTimeRange = liquidityMonitorConfig.getGlobalTimeRange();

						// Check if the value is null or empty
						if (SwtUtil.isEmptyOrNull(globalTimeRange)) {
							request.setAttribute("globalTimeRange", SwtUtil.encode64("0,1440"));
						} else {
							// Decode the value to work with it
							String decodedTimeRange = SwtUtil.decode64(globalTimeRange);

							// Validate the format and process the value
							if (decodedTimeRange != null && decodedTimeRange.contains(",")) {
								String[] parts = decodedTimeRange.split(",");

								if (parts.length == 2) {
									int start = Integer.parseInt(parts[0].trim());
									int end = Integer.parseInt(parts[1].trim());

									// Apply correction rules
									if (start == end) {
										if (start == 1440) {
											start = 0; // Move to extreme left
										} else if (start == 0) {
											end = 1440; // Move to extreme right
										} else {
											end = 1440; // Move only end to extreme right
										}
									}

									// Encode the updated value
									decodedTimeRange = start + "," + end;
								}
							}

							request.setAttribute("globalTimeRange", SwtUtil.encode64(decodedTimeRange));
						}

					}
					request.setAttribute("globalSelectedLegend",liquidityMonitorConfig.getGlobalSelectedLegend());
					request.setAttribute("globalEntityScale",liquidityMonitorConfig.getGlobalEntityScale());
					request.setAttribute("globalIncSod",liquidityMonitorConfig.getGlobalIncSod());
					request.setAttribute("globalIsShowActualOnly",liquidityMonitorConfig.getGlobalShowDataSetOnly());
					request.setAttribute("globalIncOpenMvnts",liquidityMonitorConfig.getGlobalIncOpenMvnts());
					request.setAttribute("globalSumByCutOff",liquidityMonitorConfig.getGlobalSumByCutOff());
					request.setAttribute("globalIsShowSourcesLiquidity",liquidityMonitorConfig.getGlobalShowSourceLiquidity());
					request.setAttribute("globalUncheckedThresholds",liquidityMonitorConfig.getGlobalUncheckedThresholds());
					// Get the status of tree divider for global view tab
					request.setAttribute("gblTreeDividerIsClosed", liquidityMonitorConfig.getGblTreeDividerIsClosed());
					// Get the status of grid divider for global view tab
					request.setAttribute("gblGridDividerIsClosed", liquidityMonitorConfig.getGblGridDividerIsClosed());
					// Get the status of legend divider for global view tab
					request.setAttribute("gblLegendDividerIsClosed", liquidityMonitorConfig.getGblLegendDividerIsClosed());
					request.setAttribute("globalLastSelectedGroup",liquidityMonitorConfig.getGlobalLastSelectedGroup());
					request.setAttribute("globalLastSelectedScenario",liquidityMonitorConfig.getGlobalLastSelectedScenario());
				}
			} else {
				if (liquidityMonitorConfig!=null) {
					if(timeRangeAsString != null && SwtUtil.isEmptyOrNull(liquidityMonitorConfig.getAnalysisTimeRange()))
						request.setAttribute("analysisTimeRange",SwtUtil.encode64(timeRangeAsString));
					else
						request.setAttribute("analysisTimeRange",liquidityMonitorConfig.getAnalysisTimeRange());
					request.setAttribute("analysisSelectedLegend",liquidityMonitorConfig.getAnalysisSelectedLegend());
					request.setAttribute("analysisEntityScale",liquidityMonitorConfig.getAnalysisEntityScale());
					request.setAttribute("analysisIncSod",liquidityMonitorConfig.getAnalysisIncSod());
					request.setAttribute("analysisIsShowActualOnly",liquidityMonitorConfig.getAnalysisShowDataSetOnly());
					request.setAttribute("analysisIncOpenMvnts",liquidityMonitorConfig.getAnalysisIncOpenMvnts());
					request.setAttribute("analysisSumByCutOff",liquidityMonitorConfig.getAnalysisSumByCutOff());
					request.setAttribute("analysisIsShowSourcesLiquidity",liquidityMonitorConfig.getAnalysisShowSourceLiquidity());
					request.setAttribute("analysisLastSelectedGroup",liquidityMonitorConfig.getAnalysisLastSelectedGroup());
					request.setAttribute("analysisLastSelectedScenario",liquidityMonitorConfig.getAnalysisLastSelectedScenario());
					request.setAttribute("analysisUncheckedThresholds",liquidityMonitorConfig.getAnalysisUncheckedThresholds());
					// Get the status of tree divider for group analysis tab
					request.setAttribute("grpTreeDividerIsClosed", liquidityMonitorConfig.getGrpTreeDividerIsClosed());
					// Get the status of grid divider for group analysis tab
					request.setAttribute("grpGridDividerIsClosed", liquidityMonitorConfig.getGrpGridDividerIsClosed());
					// Get the status of legend divider for group analysis tab
					request.setAttribute("grpLegendDividerIsClosed", liquidityMonitorConfig.getGrpLegendDividerIsClosed());
				}
			}

			log.debug(this.getClass().getName() + "- [getGroupChartsData] - exiting ");
			if (globalChart) {
				return getView("gblcharts");
			} else {
				return getView("grpcharts");

			}
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [getGroupChartsData] Exception - "
					  + exp.getStackTrace()[0].getClassName() + "."
					  + exp.getStackTrace()[0].getMethodName() + ":"
					  + exp.getStackTrace()[0].getLineNumber() + " "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getGroupChartsData",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} finally {
			hostId = null;
			entityId = null;
			currencyId = null;
			userId = null;
			selectedDate = null;
			selectedDateStr = null;
			useCcyMultiplier = null;
			ilmAccntGroup = null;
			selectedGrpScens = null;
			selectedGroups = null;
			dbLink = null;
			selectedFigures = null;
			datasets = null;
			metadatas = null;
			itr = null;
			group = null;
			scenario = null;
			itrScn = null;
			scenList = null;
		}

	}

	/**
	 * Used to save || Update liquidity monitor screen parameters
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String saveLiquidityMonitorConfig() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String userId = null;
		ILMConfig liquidityMonitorConfig;
		ILMConfig profileConfig = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		String paramName = null;
		String paramValue = null;
		String isGeneral = null;
		String entityId = null;
		String currencyId = null;
		String selectedTab = null;
		String selectedProfile = null;
		String profileReverted = null;
		try {
			log.debug(this.getClass().getName() + "- [saveLiquidityMonitorConfig] - starting ");
			liquidityScreenOption = new ScreenOption();
			// Get liquidityMonitorConfig from the session

			paramName = request.getParameter("paramName");
			paramValue = request.getParameter("paramValue");
			isGeneral = request.getParameter("isGeneral");
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			if (!SwtUtil.isEmptyOrNull(paramName)) {

				if(isGeneral.equals("true")) {
					liquidityMonitorConfig = ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN))
							.getLiquidityMonitorConfig();

					if (liquidityMonitorConfig != null) {
						if ("lastUsedProfile".equalsIgnoreCase(paramName)) {
							entityId = request.getParameter("entityId");
							currencyId = request.getParameter("currencyId");
							liquidityMonitorConfig.mergeLastUsedProfile(entityId, currencyId , paramValue);

							ILMConfig profileILMConfig = null;
							ScreenOption screenOption  = null;
							// Initializing the ScreenOption instance
							screenOption = new ScreenOption();
							// Setting the host id
							screenOption.getId().setHostId(hostId);
							// Setting the user id
							screenOption.getId().setUserId(userId);
							// Setting the screen id for book monitor
							screenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
							screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
							if(paramValue.equals("")){
								paramValue = "";
								screenOption.getId().setPropertyName(SwtConstants.PROPNAME_LIQUIDITY_CONFIG + entityId + "_" + currencyId + "");
								// New instance from ILMConfig
								profileILMConfig = new ILMConfig(false);
								screenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(profileILMConfig));
								// Save the liquidityMonitorConfig in DB
							}else {
								profileILMConfig = getILMProfileConfig(request, hostId, userId, entityId, currencyId, paramValue);
								profileILMConfig.mergeLastUsedProfile(entityId, currencyId , paramValue);
								screenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(profileILMConfig));
								// Save the liquidityMonitorConfig in DB
							}
							screenOptionManager.saveLiquidityMonitorOptions(screenOption, false, entityId, currencyId, null);
						}else
							liquidityMonitorConfig.updateAConfig(paramName,	paramValue);
					} else
						liquidityMonitorConfig = new ILMConfig(true);

					saveGeneralProfileGeneralConfig(hostId, userId, entityId, currencyId, paramValue, liquidityMonitorConfig);
					// Save the liquidityMonitorConfig in session
					((CommonDataManager) request.getSession().getAttribute(
							SwtConstants.CDM_BEAN))
							.setLiquidityMonitorConfig(liquidityMonitorConfig);
				}else {

					entityId = request.getParameter("entityId");
					currencyId = request.getParameter("currencyId");
					selectedTab = request.getParameter("selectedTab");
					selectedProfile = request.getParameter("selectedProfile");
					liquidityMonitorConfig = getILMProfileConfig(request, hostId, userId, entityId, currencyId, null);
					if(paramName.equals("profileName")) {

						hostId = CacheManager.getInstance().getHostId();
						userId = SwtUtil.getCurrentUserId(request.getSession());

						liquidityScreenOption.getId().setHostId(hostId);
						liquidityScreenOption.getId().setUserId(userId);
						liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
						liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));
						screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
						// Save the liquidityMonitorConfig in DB
						screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, entityId, currencyId, paramValue);


						ILMConfig generalLiquidityMonitorConfig = ((CommonDataManager) request.getSession()
								.getAttribute(SwtConstants.CDM_BEAN))
								.getLiquidityMonitorConfig();


						// Save Last Used Profile
						generalLiquidityMonitorConfig.mergeLastUsedProfile(entityId, currencyId , paramValue);
						saveGeneralProfileGeneralConfig(hostId, userId, entityId, currencyId, paramValue, generalLiquidityMonitorConfig);
						// Save the liquidityMonitorConfig in session
						((CommonDataManager) request.getSession().getAttribute(
								SwtConstants.CDM_BEAN))
								.setLiquidityMonitorConfig(generalLiquidityMonitorConfig);

					}else {

						if (paramName.equalsIgnoreCase("chartsStyles")) {//paramName is already tested as not null
							HashMap<String, String> ilmChartsStyles = new HashMap<String, String>();
							StringTokenizer chartsStyleInfos = new StringTokenizer(
									paramValue, "|");
							while (chartsStyleInfos.hasMoreElements()) {
								String value = chartsStyleInfos.nextElement()
										.toString();
								if (value != null) {
									String[] properties = value.split(":");
									ilmChartsStyles.put(properties[0],
											properties[1]);
								}
							}
							// Merge with the actual configs
							liquidityMonitorConfig.mergeChartStyles(ilmChartsStyles,(selectedTab.equals("globalView")?true:false));

						} else
							liquidityMonitorConfig.updateAConfig(paramName,
									paramValue);

						hostId = CacheManager.getInstance().getHostId();
						userId = SwtUtil.getCurrentUserId(request.getSession());

						liquidityScreenOption.getId().setHostId(hostId);
						liquidityScreenOption.getId().setUserId(userId);
						liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);

						liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));

						screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
						// Save the liquidityMonitorConfig in DB
						screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, entityId, currencyId, null);
					}
					profileConfig = getILMProfileConfig(request, hostId, userId, entityId, currencyId, selectedProfile);
					if(profileConfig.compareTo(liquidityMonitorConfig)) {
						profileReverted = "profile_reverted";
					}else {
						profileReverted = "";
					}
				}
			}
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", profileReverted);

			log.debug(this.getClass().getName() + "- [saveLiquidityMonitorConfig] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveLiquidityMonitorConfig] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveLiquidityMonitorConfig", ILMAnalysisMonitorAction.class);

		} finally {
			liquidityMonitorConfig = null;
			liquidityScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
			paramName = null;
			paramValue = null;
		}
		return getView("statechange");
	}
	/**
	 * Used to save || Update liquidity monitor screen parameters
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String saveBasicProfile() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String userId = null;
		ILMConfig liquidityMonitorConfig;
		ILMConfig profileConfig;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		String paramName = null;
		String paramValue = null;
		String entityId = null;
		String selectedTab = null;
		String currencyId = null;
		String selectedProfile = null;
		try {
			log.debug(this.getClass().getName() + "- [saveLiquidityMonitorConfig] - starting ");
			liquidityScreenOption = new ScreenOption();
			// Get liquidityMonitorConfig from the session

			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedTab = request.getParameter("selectedTab");
			selectedProfile = request.getParameter("selectedProfile");
			liquidityMonitorConfig = getILMProfileConfig(request, hostId, userId, entityId, currencyId, selectedProfile);

			Enumeration<String> parameterNames = request.getParameterNames();

			while (parameterNames.hasMoreElements()) {

				paramName = parameterNames.nextElement();

				String[] paramValues = request.getParameterValues(paramName);
				for (int i = 0; i < paramValues.length; i++) {
					paramValue = paramValues[i];
					if(paramName != null && !"fromFlex".equals(paramName) && !"selectedProfile".equals(paramName) &&!"nocache".equals(paramName) &&!"method".equals(paramName)
					   &&!"currencyId".equals(paramName) && !"entityId".equals(paramName) && !"selectedTab".equals(paramName)){
						if (paramName.equalsIgnoreCase("chartsStyles")) {//paramName is already tested as not null
							HashMap<String, String> ilmChartsStyles = new HashMap<String, String>();
							StringTokenizer chartsStyleInfos = new StringTokenizer(
									paramValue, "|");
							while (chartsStyleInfos.hasMoreElements()) {
								String value = chartsStyleInfos.nextElement()
										.toString();
								if (value != null) {
									String[] properties = value.split(":");
									ilmChartsStyles.put(properties[0],
											properties[1]);
								}
							}
							if(selectedTab.equals("globalView"))
								liquidityMonitorConfig.setGlobalChartStyles(new HashMap<String, String>());
							else
								liquidityMonitorConfig.setAnalysisChartStyles(new HashMap<String, String>());
							// Merge with the actual configs
							liquidityMonitorConfig.mergeChartStyles(ilmChartsStyles,(selectedTab.equals("globalView")?true:false));

						}else {
							liquidityMonitorConfig.updateAConfig(paramName,	paramValue);
						}
					}
				}

			}
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));


			profileConfig = getILMProfileConfig(request, hostId, userId, entityId, currencyId, selectedProfile);
			if(!profileConfig.compareTo(liquidityMonitorConfig)) {
				// Save the liquidityMonitorConfig in DB
				screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, entityId, currencyId, selectedProfile);
				if(!SwtUtil.isEmptyOrNull(selectedProfile)) {
					screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, entityId, currencyId, null);
				}
			}

			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "");

			log.debug(this.getClass().getName() + "- [saveLiquidityMonitorConfig] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveLiquidityMonitorConfig] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveLiquidityMonitorConfig", ILMAnalysisMonitorAction.class);

		} finally {
			liquidityMonitorConfig = null;
			liquidityScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
			paramName = null;
			paramValue = null;
		}
		return getView("statechange");
	}



	private void saveGeneralProfileGeneralConfig(String hostId, String userId, String entityId, String currencyCode, String profileName, ILMConfig liquidityMonitorConfig){
		ScreenOption liquidityScreenOption = null;
		ScreenOptionManager screenOptionManager = null;
		try{
			liquidityScreenOption = new ScreenOption();
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));

			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			// Save the liquidityMonitorConfig in DB
			screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, true, null, null, null);

		}catch(Exception e){

		}
	}

	/**
	 * Method to set column width in request attribute
	 * @param request
	 * @param gridName
	 * @param colsWidth
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request,String gridName , String colsWidth) {

		HashMap<String, String> widths = null;
		String[] props = null;

		try {
			log.debug(this.getClass().getName() + "- [bindColumnWidthInRequest] - starting ");

			widths = new HashMap<String, String>();
			// Get column width for each column
			props = colsWidth.split(",");
			// Loop to separate column && width value in hash map
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			request.setAttribute(gridName, widths);
			log.debug(this.getClass().getName() + "- [bindColumnWidthInRequest] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + e.getMessage());
		}finally{
			props = null;
			widths = null;
		}
	}

	/**
	 * This method is used to bind the column order in request object
	 * @param request
	 * @param gridName
	 * @param colsOrder
	 * @throws SwtException
	 */
	private void bindColumnOrderInRequest(HttpServletRequest request,
										  String gridName , String colsOrder) throws SwtException {

		ArrayList<String> alColumnOrder = null;
		String[] props = null;

		try {
			log.debug(this.getClass().getName() + "- [bindColumnOrderInRequest] - starting ");

			// Comma special character is used to split && put in String array variable
			props = colsOrder.split(",");

			// Initialize list to hold grid column order
			alColumnOrder = new ArrayList<String>(props.length);
			for (String prop : props) {
				// Adding the Column values to ArrayList
				alColumnOrder.add(prop);
			}

			// Setting the Column orders value in request object to show in screen
			request.setAttribute(gridName, alColumnOrder);
			log.debug(this.getClass().getName() + "- [bindColumnOrderInRequest] - exiting ");
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - [bindColumnOrderInRequest] - Exception - "
					  + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"bindColumnOrderInRequest", AccountMonitorNewAction.class);
		} finally {
			alColumnOrder = null;
			props = null;
		}
	}

	/**
	 *
	 * @param hostId
	 * @param userId
	 * @throws SwtException
	 */
	public ILMConfig getLiquidityMonitorConfig(HttpServletRequest request,
											   String hostId, String userId) throws SwtException {

		ILMConfig  liquidityMonitorConfig;
		ScreenOptionManager screenOptionManager = null;
		String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;

		try {
			log.debug(this.getClass().getName() + "- [getLiquidityMonitorConfig] - starting ");
			liquidityMonitorConfig = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN))
					.getLiquidityMonitorConfig();
			if(liquidityMonitorConfig == null){
				// Bean manager
				screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

				// Initializing the ScreenOption instance
				ScreenOption screenOption = new ScreenOption();
				// Setting the host id
				screenOption.getId().setHostId(hostId);
				// Setting the user id
				screenOption.getId().setUserId(userId);
				// Setting the screen id for book monitor
				screenOption.getId().setScreenId(screenId);

				// Fetching the refresh rate
				screenOption = screenOptionManager.getLiquidityMonitorOptions(screenOption, true, null, null, null);

				if(screenOption != null && screenOption.getLiquidityMonitorOption() != null){
					// Deserialize LiquidityMonitorOption
					liquidityMonitorConfig = (org.swallow.work.model.ILMConfig) SwtUtil.deserializeFromXML(screenOption.getLiquidityMonitorOption());

					//save the liquidityMonitorConfig in session
					((CommonDataManager) request.getSession().getAttribute(
							SwtConstants.CDM_BEAN)).setLiquidityMonitorConfig(liquidityMonitorConfig);

				}else{
					// Initializing the ScreenOption instance
					screenOption = new ScreenOption();
					// Setting the host id
					screenOption.getId().setHostId(hostId);
					// Setting the user id
					screenOption.getId().setUserId(userId);
					// Setting the screen id for book monitor
					screenOption.getId().setScreenId(screenId);

					// New instance from ILMConfig
					liquidityMonitorConfig = new ILMConfig(true);

					screenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));

					// Save the liquidityMonitorConfig in DB
					screenOptionManager.saveLiquidityMonitorOptions(screenOption, true, null, null, null);

					//save the liquidityMonitorConfig in session
					((CommonDataManager) request.getSession().getAttribute(
							SwtConstants.CDM_BEAN)).setLiquidityMonitorConfig(liquidityMonitorConfig);
				}
			}
			log.debug(this.getClass().getName() + "- [getLiquidityMonitorConfig] - exiting ");
			return liquidityMonitorConfig;
		}catch (Exception e) {
			e.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					  + " - [getLiquidityMonitorConfig] - Exception - "
					  + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e,
					"getLiquidityMonitorConfig", AccountMonitorNewAction.class);
		}finally{
			liquidityMonitorConfig = null;
			screenOptionManager = null;
			screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
		}
	}

	/**
	 *
	 * @param hostId
	 * @param userId
	 * @throws SwtException
	 */
	public void putLiquidityMonitorProfileListInRequest(HttpServletRequest request,
														String hostId, String userId, String entityId, String currencyId) throws SwtException {

		ScreenOptionManager screenOptionManager = null;
		String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;

		try {
			String noneLabel = null;
			ArrayList<String> profileList = null;
			Collection<LabelValueBean> profileLblValue = null;
			log.debug(this.getClass().getName() + "- [getLiquidityMonitorConfig] - starting ");
			// Bean manager
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(hostId);
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);

			// Fetching the refresh rate
			profileList = screenOptionManager.getLiquidityMonitorProfileList(screenOption, entityId, currencyId);
			profileLblValue = new ArrayList<LabelValueBean>();
			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);
			profileLblValue.add(new LabelValueBean(noneLabel, noneLabel));

			for(int i = 0; i<profileList.size(); i++){
				if(!profileList.get(i).equals(""))
					profileLblValue.add(new LabelValueBean(profileList.get(i), profileList.get(i)));
			}

			request.setAttribute("profiles", profileLblValue);
			log.debug(this.getClass().getName() + "- [getLiquidityMonitorConfig] - exiting ");
		}catch (Exception e) {
			e.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					  + " - [getLiquidityMonitorConfig] - Exception - "
					  + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e,
					"getLiquidityMonitorConfig", AccountMonitorNewAction.class);
		}finally{
			screenOptionManager = null;
			screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
		}
	}

	/**
	 *
	 * @param hostId
	 * @param userId
	 * @throws Exception
	 */
	public ILMConfig getILMProfileConfig(HttpServletRequest request,
										 String hostId, String userId, String entityId,  String currencyId, String profileId) throws Exception {

		ILMConfig  liquidityMonitorConfig;
		ScreenOptionManager screenOptionManager = null;
		String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
		ScreenOption screenOption ;

		try {
			log.debug(this.getClass().getName() + "- [getILMProfileConfig] - starting ");
			// Bean manager
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			// Initializing the ScreenOption instance
			screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(hostId);
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getLiquidityMonitorOptions(screenOption, false, entityId, currencyId, profileId);

			if(screenOption!=null&&screenOption.getLiquidityMonitorOption()!=null){
				// Deserialize LiquidityMonitorOption
				liquidityMonitorConfig = (ILMConfig) SwtUtil.deserializeFromXML(screenOption.getLiquidityMonitorOption());
			}else{
				// Initializing the ScreenOption instance
				screenOption = new ScreenOption();
				// Setting the host id
				screenOption.getId().setHostId(hostId);
				// Setting the user id
				screenOption.getId().setUserId(userId);
				// Setting the screen id for book monitor
				screenOption.getId().setScreenId(screenId);

				// New instance from ILMConfig
				liquidityMonitorConfig = new ILMConfig(false);

				screenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));

				// Save the liquidityMonitorConfig in DB
				screenOptionManager.saveLiquidityMonitorOptions(screenOption, false, entityId, currencyId, null);

			}
			log.debug(this.getClass().getName() + "- [getILMProfileConfig] - exiting ");
		}catch (Exception e) {
			// Initializing the ScreenOption instance
			screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(hostId);
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// New instance from ILMConfig
			liquidityMonitorConfig = new ILMConfig(false);
			screenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(liquidityMonitorConfig));
			// Save the liquidityMonitorConfig in DB
			screenOptionManager.saveLiquidityMonitorOptions(screenOption, false, entityId, currencyId, null);
			// log error message
			log.error(this.getClass().getName()
					  + " - [getILMProfileConfig] - Exception - "
					  + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e,
					"getILMProfileConfig", AccountMonitorNewAction.class);
		}
		return liquidityMonitorConfig;

	}

	/**
	 *
	 * This function is called when the user click on the cancel button
	 * when the export is in progress.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String cancelILMExport()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String cancelExport = null;
		/* Set the cancelExport value from Flex part.
		   It will be 'true' only when the user cancels the export before accomplishment*/
		cancelExport = request.getParameter("cancelExport");
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute("CDM");
		CDM.setCancelExport(cancelExport);
		return null;
	}
	/**
	 *
	 * Export the Line chart with the selected filters
	 * using chart && legend snapshots sent in BASE64
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String exportLineChartDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String chartImageData64 = null;
		String legendImageData64 = null;
		String chartDataXml  = null;
		String exportType = null;
		ArrayList<FilterDTO> filterData = null;
		FilterDTO fDTO = null;
		String filePrefix = "ILMExport";
		ILMReporting ilmReporting = new ILMReporting();
		String yField = null;
		ArrayList<ColumnInfo> columnList = null;
		ColumnInfo column = null;
		String currencyId = null;
		String entityId = null;
		String selectedDate = null;
		String timeFrame = null;
		/* Variable Declaration for workFlowMgr */
		WorkflowMonitorManager workFlowMgr = null;
		// Token for download corresponding file
		String tokenForDownload = null;
		ActionMessages errors = null;
		CommonDataManager CDM = null;
		String[] stringList = null;
		try {
			log.debug(this.getClass().getName() + "- [exportLineChartDetails] - starting ");
			// Add a cookie to the response. It is the same as come from jsp part.
			errors= new ActionMessages();

			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			CDM.setCancelExport("false");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			chartImageData64 = request.getParameter("chartSnapshot");
			if(chartImageData64.indexOf(",")>-1) {
				chartImageData64 = chartImageData64.split(",")[1];
			}
			legendImageData64 = request.getParameter("legendSnapshot");
			chartDataXml = request.getParameter("dataXML");
			exportType = request.getParameter("exportType");
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedDate = request.getParameter("selectedDate");
			timeFrame = request.getParameter("timeFrame");
			if (!SwtUtil.isEmptyOrNull(chartDataXml)) {
				Pattern regex = Pattern.compile("(<result>(.*?)</result>)", Pattern.DOTALL);
				Matcher matcher = regex.matcher(chartDataXml);
				if (matcher.find()) {
					columnList = new ArrayList<ColumnInfo>();
					Element node = DocumentBuilderFactory.newInstance().newDocumentBuilder()
							.parse(new ByteArrayInputStream(matcher.group(1).getBytes())).getDocumentElement();
					for (int i = 0; i < node.getChildNodes().getLength(); i++) {
						yField = node.getChildNodes().item(i).getNodeName();

						if (!yField.equals("#text")) {
//							stringList = yField.split("\\.");
							if (yField.equals("timeSlot") || (yField.indexOf("NO_MORE_LIQUIDITY_AVAILABLE") == -1
															  && yField.indexOf("SUM_OTHER_TOTAL") == -1 && yField.indexOf("SUM_COLLATERAL") == -1
															  && yField.indexOf("SUM_CREDIT_LINE_TOTAL") == -1
															  && yField.indexOf("SUM_UN_LIQUID_ASSETS") == -1)) {
								column = new ColumnInfo(yField);
								columnList.add(column);
							}
						}
					}
				}
				// Sort the column list depending of the compareTo method in ColumnInfo class
				Collections.sort(columnList);
			}
			List<String> data = new ArrayList<String>();
			data.add(chartImageData64);
			data.add(legendImageData64);
			data.add(chartDataXml);


			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			// Set scenario ID filter Name && value in Export screen.
			fDTO.setName("Entity");
			fDTO.setValue(entityId);
			filterData.add(fDTO);

			// set scenario title filter Name && value in Export screen.
			fDTO = new FilterDTO();
			fDTO.setName("Currency");
			fDTO.setValue(currencyId);
			filterData.add(fDTO);

			// set query filter Name && value in Export screen.
			fDTO = new FilterDTO();
			fDTO.setName("Value Date");
			fDTO.setValue(selectedDate);
			filterData.add(fDTO);

			// set facility ID filter Name && value in Export screen.
			fDTO = new FilterDTO();
			fDTO.setName("TimeFrame");
			fDTO.setValue(timeFrame);
			filterData.add(fDTO);

			ilmReporting.exportGroupAnalysis(request, response , columnList, filterData, data, exportType, filePrefix);
			log.debug(this.getClass().getName() + "- [exportLineChartDetails] - exiting ");
			return null;
		} catch (SwtException exp) {
			exp.printStackTrace();
			if(exp.getMessage().contains("generatedException")){
				if (CDM != null) {
					CDM.setCancelExport("true");
				}
				// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class.
				tokenForDownload = request.getParameter("tokenForDownload");
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|OK"));
				return null;
			}
			log.error(this.getClass().getName()
					  + " - Exception Catched in [exportLineChartDetails] method : - "
					  + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"exportLineChartDetails",
							ILMAnalysisMonitorAction.class), request, "");
			if (errors != null) {
				errors.add("", new ActionMessage(exp.getMessage()));
			}
			saveErrors(request, errors);
			// Source scan tools may report a "HTTP response splitting" vulnerability, a generic solution works by ignoring text after CRLFs is implemented XSSFilter class.
			tokenForDownload = request.getParameter("tokenForDownload");
			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO"));
			response.setContentType("text/html");
			response.setHeader("Content-disposition","inline");
			return null;
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					  + " - Exception Catched in [exportLineChartDetails] method : - "
					  + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"exportLineChartDetails",
							ILMAnalysisMonitorAction.class), request, "");
			if (errors != null) {
				errors.add("", new ActionMessage(exp.getMessage()));
			}
			saveErrors(request, errors);
			// Source scan tools may report a "HTTP response splitting" vulnerability. A generic solution of
			// ignoring text after CRLFs is implemented in XSSFilter class, so can safely ignore the report.
			tokenForDownload = request.getParameter("tokenForDownload");
			response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|KO"));
			response.setContentType("text/html");
			response.setHeader("Content-disposition","inline");
			return null;
		} finally {
			if (CDM != null) {
				CDM.setCancelExport("false");
			}
			chartImageData64 = null;
			legendImageData64 = null;
			chartDataXml  = null;
			exportType = null;
		}
	}

	/**
	 *
	 * This function return the actual status of the calculation process
	 * containing the number of the total accounts in the groups the number
	 * of accounts incomplete , inconsistency || having new data for the selected currency
	 * && entity
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getCurrentPorcessState()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String currencyId = null;
		String entityId = null;
		String selectedDateAsString = null;
		Date selectedDate = null;
		String hostId = null;
		HashMap<String, String> processStatus = null;
		String roleId = null;
		String userId = null;
		String dbLink;

		try {
			log.debug(this.getClass().getName() + "- [getCurrentPorcessState] - starting ");
			// Add a cookie to the response. It is the same as come from jsp part.
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedDateAsString = request.getParameter("selectedDate");
			if(SwtUtil.isEmptyOrNull(selectedDateAsString)) {
				selectedDate = SwtUtil.getSysParamDate();
			}else {
				selectedDate = SwtUtil.parseDate(selectedDateAsString, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}
			// Get the role ID from session
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get the host id from swtutil
			hostId = SwtUtil.getCurrentHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			//Get current dbLink
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			// get process status from calling a db procedure for the selected host id, entity id currency id && selected date
			processStatus = ilmAnalysisMonitorManager.getProcessState(hostId, entityId, currencyId, selectedDate, roleId, userId, dbLink);
			request.setAttribute("processStatus", processStatus);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			log.debug(this.getClass().getName() + "- [getCurrentPorcessState] - exiting ");
			return getView("processStatus");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getCurrentPorcessState] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getCurrentPorcessState",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} finally {
			currencyId = null;
			entityId = null;
			selectedDateAsString = null;
			selectedDate = null;
			hostId = null;
			processStatus = null;
		}
	}
	/**
	 *
	 * This function return the actual status of the calculation process
	 * containing the number of the total accounts in the groups the number
	 * of accounts incomplete , inconsistency || having new data for the selected currency
	 * && entity
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getDataState()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String currencyId = null;
		String entityId = null;
		String selectedDateAsString = null;
		Date selectedDate = null;
		String hostId = null;
		String resultStatus = null;
		String dbLink;

		try {
			log.debug(this.getClass().getName() + "- [getDataState] - starting ");
			// Add a cookie to the response. It is the same as come from jsp part.
			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("currencyId");
			selectedDateAsString = request.getParameter("selectedDate");
			selectedDate = SwtUtil.parseDate(selectedDateAsString, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			// Get the host id from swtutil
			hostId = SwtUtil.getCurrentHostId();
			//Get current dbLink
			dbLink = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			// get process status from calling a db procedure for the selected host id, entity id currency id && selected date
			resultStatus = ilmAnalysisMonitorManager.getDataState(hostId, entityId, currencyId, selectedDate, dbLink);
			request.setAttribute("dataState", resultStatus);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			log.debug(this.getClass().getName() + "- [getDataState] - exiting ");
			return getView("processStatus");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getDataState] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getDataState",
							ILMAnalysisMonitorAction.class), request, "");
			return getView("dataerror");
		} finally {
			currencyId = null;
			entityId = null;
			selectedDateAsString = null;
			selectedDate = null;
			hostId = null;
			resultStatus = null;
		}
	}

	/**
	 * Returns a hash map that contains as key the list of selected groups && an arrayList its scenarios as value
	 * @param selectedFigures
	 * @return HashMap
	 */
	private HashMap getSelectedGroups(String selectedFigures) {

		LinkedHashMap<String, List<String>> returnHashMap = new LinkedHashMap<String, List<String>>();
		String[] selectedFiguresList = selectedFigures.split("\\|");

		for (int i = 0 ; i < selectedFiguresList.length ; i++){

			String group = selectedFiguresList[i].split(":")[0];
			String scenario = selectedFiguresList[i].split(":")[1];

			if (returnHashMap.get(group) == null){
				List<String> scenList = new ArrayList<String>();
				scenList.add(scenario);
				returnHashMap.put(group, scenList);
			}else{
				List<String> scenList = returnHashMap.get(group);
				scenList.add(scenario);
				returnHashMap.remove(group);
				returnHashMap.put(group, scenList);
			}
		}
		return returnHashMap;
	}

	/**
	 * Test if the "All" in the entity combobox is selectable, this will check the same currency-GMT
	 * offset is  configured under each entity
	 * otherwise we should rise a message
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String isAllEntityAvailable()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String currencyId = null;
		String roleId = null;
		String isAllEntityAvailable = null;

		try {
			log.debug(this.getClass().getName() + "- [isAllEntityAvailable] - starting ");
			hostId = SwtUtil.getCurrentHostId();
			currencyId = request.getParameter("currencyId");
			// Get the role ID from session
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			isAllEntityAvailable = ilmAnalysisMonitorManager.isAllEntityAvailable(hostId, currencyId, roleId);

			if(isAllEntityAvailable.equals("Y")){
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Data fetch OK");
			}else {
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", SwtUtil.getMessage("ilmanalysismonitor.errorAllOptionCcyNotAvailable",request));
			}

			log.debug(this.getClass().getName() + "- [isAllEntityAvailable] - exiting ");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [isAllEntityAvailable] method : - "
					  + swtexp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp);
			request.setAttribute("reply_location", swtexp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtexp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtexp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(swtexp,
							"recaluclateData",
							ILMAnalysisMonitorAction.class), request, "");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [isAllEntityAvailable] method : - "
					  + exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp);
			request.setAttribute("reply_location", exp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + exp.getStackTrace()[0].getMethodName()
												   + ":"
												   + exp.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"isAllEntityAvailable",
							ILMAnalysisMonitorAction.class), request, "");
		}finally{
			hostId = null;
		}
		return getView("dataerror");

	}

	/**
	 * Get the currency timeframe value (GMT +/- 00:00)
	 * @param entityId
	 * @param currencyId
	 * @return String
	 * @throws SwtException
	 */
	public String getCurrencyTimeframe(String entityId, String currencyId,  Date selectedDate, String roleId) throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		return ilmAnalysisMonitorManager.getCurrencyTimeframe(entityId, currencyId, selectedDate, roleId);

	}
	/**
	 *
	 * @param entityId
	 * @param currencyId
	 * @return String
	 * @throws SwtException
	 */
	public String getCcyMultiplierAndDecimalPlaces(String entityId, String currencyId) throws SwtException {

		return ilmAnalysisMonitorManager.getCcyMultiplierAndDecimalPlaces(entityId, currencyId);

	}


	/**
	 * This function stops the recalculation process for the given unique id
	 * each session will have a unique id set on screen opening.
	 * The function can be called if the user close the ILM screen when calculating
	 * || when clicking on the disable button from the scheduler screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String stopRecalculationProcess() throws SwtException {

		String uniqueSequenceId = null;
		CommonDataManager CDM;
		CallableStatement cstmt = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {

			log.debug(this.getClass().getName()
					  + "- [stopRecalculationProcess] - Enter");
			uniqueSequenceId = request.getParameter("uniqueSequenceId");
			// Retrieve User's Menu,Entity && Currency Group
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			if(CDM !=null && CDM.getIlmScreenConnectionDetails() != null && CDM.getIlmScreenConnectionDetails().containsKey(uniqueSequenceId)){
				cstmt =(CallableStatement) CDM.getIlmScreenConnectionDetails().get(uniqueSequenceId);
				CDM.getIlmScreenConnectionDetails().remove(uniqueSequenceId);
				try{
					cstmt.cancel();
				}catch (Exception e){
				}
				JdbcUtils.closeStatement(cstmt);
			}
			log.debug(this.getClass().getName()
					  + " - [stopRecalculationProcess] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [stopRecalculationProcess] method : - "
					  + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"stopRecalculationProcess",
							ILMAnalysisMonitorAction.class), request,
					"");
			return getView("fail");
		} finally {
			// Nullify Objects
			uniqueSequenceId = null;
		}
	}

	/**
	 * Returns the list of existing profiles for the current user depending
	 * on the passed entity && currency id
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getProfileList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable && class instance declaration */
		ScreenOptionManager screenOptionManager = null;
		String noneLabel = null;
		String entityId = null;
		String currencyCode = null;
		ArrayList<String> profileList = null;
		HashMap<String, String> profileComboData = null;
		String profileListAsString = null;
		ILMConfig ilmConf =  null;
		ILMConfig ilmConfProfile =  null;
		String currentProfile = null;
		try{
			String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
			log.debug(this.getClass().getName() + "- [getFacilityIdProperties] - Enter");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			// Bean manager
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(SwtUtil.getCurrentUserId(request.getSession()));
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);

			ilmConf =  getLiquidityMonitorConfig(request, SwtUtil.getCurrentHostId(), SwtUtil.getCurrentUserId(request.getSession()));
			currentProfile = ilmConf.getLastUsedProfiles().get(entityId + "_" + currencyCode);
			if(SwtUtil.isEmptyOrNull(currentProfile))
				currentProfile = "";

			if(!SwtUtil.isEmptyOrNull(currentProfile))
			{
				ilmConfProfile = getILMProfileConfig(request, SwtUtil.getCurrentHostId(), SwtUtil.getCurrentUserId(request.getSession()), entityId, currencyCode, currentProfile);

				ScreenOption liquidityScreenOption = new ScreenOption();
				String hostId = CacheManager.getInstance().getHostId();
				String userId = SwtUtil.getCurrentUserId(request.getSession());

				liquidityScreenOption.getId().setHostId(hostId);
				liquidityScreenOption.getId().setUserId(userId);
				liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
				liquidityScreenOption.setLiquidityMonitorOption(SwtUtil.serializeToXML(ilmConfProfile));
				screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
				// Save the liquidityMonitorConfig in DB
				screenOptionManager.saveLiquidityMonitorOptions(liquidityScreenOption, false, entityId, currencyCode, null);

			}


			// Fetching the refresh rate
			profileList = screenOptionManager.getLiquidityMonitorProfileList(screenOption, entityId, currencyCode);
			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);

			profileComboData = new LinkedHashMap<String, String>();
			profileComboData.put("<none>", noneLabel);
			for(int i = 0; i < profileList.size(); i++){
				profileComboData.put(profileList.get(i), profileList.get(i));
			}
			profileListAsString = createXMLOption(profileComboData, currentProfile);
			// set the profile list in response
			response.getWriter().print(profileListAsString);
			log.debug(this.getClass().getName() + " - [getFacilityIdProperties] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getFacilityIdProperties] method : - "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getFacilityIdProperties", ILMAnalysisMonitorAction.class), request, "");
			return getView("fail");
		}
	}



	/**
	 * This method gets ilmThroughPut Ratio Monitor details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String getThroughputBreakdownDetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String errorMessage = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		String selectedAccountGroup = null;
		ArrayList<LabelValueBean> accountGroupList;
		ArrayList<LabelValueBean> scenarioList;
		HttpSession session = null;
		String hostId = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<EntityUserAccess> collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		String defaultEntityId = null;
		String roleId = null;
		Boolean currentEntityFound = false;
		String defaultCcyId = null;
		String selectedScenario = null;
		boolean entityChanged = false;
		String selectedDate = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		String hiddenColumns = null;
		String foreOutlflowsCheckbox= null;
		String actOutlflowsCheckbox = null;
		String ccyThresholdCheckbox = null;
		String foreIntlflowsCheckbox= null;
		String actInlflowsCheckbox = null;
		String unsettledOutflows = null;
		String currentFilter = null;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null;
		Integer currentPage = 1;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getThroughputBreakdownDetails ] - " + "Entry");
			session = request.getSession();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ILMThroughPutRatioBreakdown";
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.ILM_THOUPUT_MONITOR_ID + "", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, formatDate);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			/**************************** Selects ********************/

			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			// Retrieve the user from session
			userId = SwtUtil.getCurrentUserId(request.getSession());

			selectedAccountGroup = request.getParameter("accountGroup");
			selectedDate = request.getParameter("selectedDate");
			if(SwtUtil.isEmptyOrNull(selectedDate)) {
				selectedDate = SwtUtil.getSystemDateString();
			}

			foreOutlflowsCheckbox = request.getParameter("foreOutlflowsCheckbox");
			actOutlflowsCheckbox = request.getParameter("actOutlflowsCheckbox");
			ccyThresholdCheckbox = request.getParameter("ccyThresholdCheckbox");
			foreIntlflowsCheckbox = request.getParameter("foreIntlflowsCheckbox");
			actInlflowsCheckbox = request.getParameter("actInlflowsCheckbox");
			unsettledOutflows = request.getParameter("unsettledOutflows");
			currentFilter = request.getParameter("currentFilter");

			if(!SwtUtil.isEmptyOrNull(request.getParameter("currentPage"))) {
				currentPage = Integer.parseInt(request.getParameter("currentPage"));
			}
			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			entitiesLblValue.addAll(entityAccessLvlColl);

			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityChanged"))
				&& request.getParameter("entityChanged").equals("true"))
				entityChanged = true;

			// Retrieve the entity id selected by the user, if first load then it will be
			// default entity
			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityId"))) {
				defaultEntityId = request.getParameter("entityId");
			} else {
				String currentEntity = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN))
						.getUser().getCurrentEntity();

				// Set currentEntityFound boolean to true if the current entity exists in the
				// collUserEntityAccess list
				for (Iterator iterator = collUserEntityAccess.iterator(); iterator.hasNext();) {
					EntityUserAccess entityUserAccess = (EntityUserAccess) iterator.next();
					if (entityUserAccess.getEntityId().equals(currentEntity)) {
						currentEntityFound = true;
					}
				}

				// the default entity will be the current entity if it exists in the list
				if (!currentEntityFound) {
					// When we have more than one entity in the list
					if (collUserEntityAccess.size() > 0) {
						defaultEntityId = collUserEntityAccess.iterator().next().getEntityId();
						// The case when we don't have access to any entity in the ILM screen, only All
						// value is available
					} else {
						// A test will be in Flex, if the All value is selected then show an error
						// without getting the data
						defaultEntityId = "All";
					}
				} else {
					defaultEntityId = currentEntity;
				}
			}
			defaultCcyId = request.getParameter("currencyId");
			selectedScenario = request.getParameter("selectedScenario");

			// Retrieve the default currency id for the user
			if (SwtConstants.ALL_VALUE.equals(defaultEntityId)) {
				defaultCcyId = request.getParameter("currencyId");

			} else {
				if (entityChanged)
					defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
				else {
					defaultCcyId = request.getParameter("currencyId");
					if(SwtUtil.isEmptyOrNull(defaultCcyId)) {
						defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
					}

				}
			}
			// Instantiate the ccyList
			ccyList = new ArrayList<LabelValueBean>();
			// get the collection of currency access
			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(
					ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));

			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			if (collUserEntityAccess != null)
				ccyList.addAll(ccyAccessColl);

			boolean entityExistInList = false;

			for (LabelValueBean o : ccyList) {
				if (o.getValue().equals(defaultCcyId))
					entityExistInList = true;
			}
			if (!entityExistInList) {
				if (ccyList != null && ccyList.size() > 0)
					defaultCcyId = ccyList.iterator().next().getValue();
			}

			/**** CurrencyCombo ***********/
			Iterator j = ccyList.iterator();

			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(defaultCcyId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));

			}

			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/**** entity Combo ***********/
			lstOptions = new ArrayList<OptionInfo>();

			j = entitiesLblValue.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(defaultEntityId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));

			/***** Account Group Combo ***********/

			lstOptions = new ArrayList<OptionInfo>();
			boolean accountFound = false;
			accountGroupList = getAccountGroupsList(request, hostId, defaultEntityId, defaultCcyId, true);
			if (SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				selectedAccountGroup = accountGroupList.get(0).getValue();
			}
			j = accountGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedAccountGroup)) {
					accountFound = true;
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				}
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));

			if(accountGroupList.isEmpty()) {
				selectedAccountGroup = null;
			}
			else if(!accountFound && !accountGroupList.isEmpty()) {
				selectedAccountGroup = accountGroupList.get(0).getValue();
			}

			/***** Scenario Group Combo ***********/
			lstOptions = new ArrayList<OptionInfo>();
			boolean scenarioFound = false;
			scenarioList = new ArrayList<LabelValueBean>();
			scenarioList.add(new LabelValueBean(SwtConstants.STANDARD_VALUE, SwtUtil.getMessage("ilmExcelReport.standard", request)));

			scenarioList.addAll(getScenarioList(request, hostId, defaultEntityId, defaultCcyId, true));
			j = scenarioList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (row.getValue() != null && row.getValue().equals(selectedScenario)) {
					scenarioFound = true;
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				}
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_SCENARIOLIST, lstOptions));

			responseConstructor.formSelect(lstSelect);
			if(scenarioList.isEmpty()) {
				selectedScenario = null;
			}else if(!scenarioFound &&  !scenarioList.isEmpty()) {
				selectedScenario = scenarioList.get(0).getValue();
			}


			/**************** dataGrid ******************/
			String applyCcyThresholdAsString = "true".equalsIgnoreCase(ccyThresholdCheckbox)?"Y":"N";
			ArrayList<Movement> movList = new ArrayList<Movement>();

			String selectedDateISO = SwtUtil.formatDate(SwtUtil.parseDate(selectedDate, SwtUtil.getCurrentDateFormat(request.getSession())), "yyyy-MM-dd");
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			totalMap = getMovements(request, movList,  hostId, defaultEntityId, defaultCcyId, selectedDateISO , selectedAccountGroup, selectedScenario, applyCcyThresholdAsString, currentFilter, currentPage, pageSize);
			int totalCount = 1;
			Double totalInPage = null;
			Double totalOverPages = null;


			if(totalMap != null && totalMap.size() > 0) {

				if(totalMap != null && totalMap.get("totalCount") != null )
					totalCount = Integer.parseInt(String.valueOf(totalMap.get("totalCount")));

				if(totalMap != null && totalMap.get("totalInPage") != null )
					totalInPage = Double.parseDouble(String.valueOf(totalMap.get("totalInPage")));

				if(totalMap != null && totalMap.get("totalOverPages") != null )
					totalOverPages = Double.parseDouble(String.valueOf(totalMap.get("totalOverPages")));

			}

			/*************************************
			 * Singletons
			 ******************************/
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.CURRENCYCODE, defaultCcyId);
			responseConstructor.createElement("menuEntityCurrGrpAccess", SwtUtil.getMenuEntityCurrGrpAccess(request, defaultEntityId, defaultCcyId));
			responseConstructor.createElement(PCMConstant.SELECTED_ENTITY, defaultEntityId);
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffsetILM(request, defaultEntityId));
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			// Create the grid data
			responseConstructor.formGridStart();
			// form paging details
			int maxPage = setMaxPageAttribute(totalCount, pageSize);

			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));
			// form column details
			responseConstructor.formColumn(getMovementGridColumns(width, columnOrder, hiddenColumns, request));

			// Get Grid Data

			// form rows (records)
			responseConstructor.formRowsStart(totalCount);
			// Iterating category details
			for (Iterator<Movement> it = movList.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				Movement element = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME                    ,    element.getPositionLevelName() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME                       ,    element.getValueDateAsString() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME                     ,    element.getAmountAsString(), ("D".equals(element.getSign()) ? true : false), false );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME                        ,    element.getSign() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_CCY_TAGNAME                         ,    element.getCurrencyCode() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME                  ,    element.getReference1() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME                     ,    element.getAccountId() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME                       ,    element.getInputDateAsString() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME              ,    element.getCounterPartyId() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_PRED_TAGNAME                        ,    element.getPredictStatus() );
				responseConstructor.createRowElement(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME ,    element.getExtBalStatus() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME                      ,    element.getMatchStatusDesc() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME                     ,    (element.getMatchId() != null?""+element.getMatchId() :""));
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME                      ,    element.getInputSource() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME                   ,    element.getMessageFormat() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME                       ,    element.getHasNotes() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME                 ,    element.getBeneficiaryId() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME                  ,    element.getReference2() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME                  ,    element.getReference3() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME                  ,    ""+element.getId().getMovementId() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME                    ,    element.getBookCode() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME                   ,    element.getCustodianId() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME                    ,    element.getReference4() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME                 ,    element.getUpdateDateAsString() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME               ,    element.getMatchingParty() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME                 ,    element.getProductType() );
				responseConstructor.createRowElement(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME              ,    element.getPostingDateAsString() );
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(componentId);
			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getThroughputBreakdownDetails] method : - "
					  + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [getThroughputBreakdownDetails] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getThroughputBreakdownDetails] - Exit");

		}

		return null;
	}
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		int maxPage = (totalCount) / (pageSize);
		int remainder = totalCount % pageSize;
		if (remainder > 0 || maxPage == 0) {
			maxPage++;
		}
		return maxPage;
	}

	public HashMap<String, Object> getMovements(HttpServletRequest request, ArrayList<Movement> movList,  String hostId, String defaultEntityId, String defaultCcyId, String selectedDate ,
												String selectedAccountGroup, String selectedScenario, String applyCcyThreshold, String currentFilter, int currentPage ,int pageSize) {
		String roleId = null;
		String reference = null;
		String initialInputScreen = null;
		String filterSortStatus = null;
		String archiveId = null;
		String gridFilter = null;
		String gridSort = null;
		String scenarioId = null;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null;
		// total summation of all movements in all pages
		try {
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			gridFilter = request.getParameter("selectedFilter") == null ? "all"
					: request.getParameter("selectedFilter");
			gridSort = request.getParameter("selectedSort") == null ? "7|true|"
					: request.getParameter("selectedSort");

			// Gets the filter sort status
			filterSortStatus = gridFilter + "," + gridSort;


			initialInputScreen = "S";
			reference = "<refparams><include ref1=\"Y\" ref2=\"Y\" ref3=\"Y\" ref4=\"Y\" like=\"N\" ><![CDATA[]]></include><exclude ref1=\"Y\" ref2=\"Y\" ref3=\"Y\" ref4=\"Y\" like=\"N\" ><![CDATA[]]></exclude></refparams>";

			MovementManager movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			/*
			 * Added the External Balance Status, when frame the filter
			 * parameter.
			 */
			/* get framed filter string */
			/*	if ((archiveId != null) && (archiveId.length() > 0)) {
				ArchiveManager archiveManager = null;
				archiveManager = (ArchiveManager) SwtUtil
						.getBean("archiveManager");
				accountId = archiveManager.getDBlink(archiveId);
				initialInputScreen = "R";
			}*/
			currentFilter=defaultEntityId+"|"+defaultCcyId+"|"+selectedAccountGroup+"|"+selectedDate+"|"+currentFilter+applyCcyThreshold+"|"+selectedScenario;
			//int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			totalMap = movementManager.getMonitorMovements(hostId,
					defaultEntityId, defaultCcyId, "", new Date(),
					"All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|"+reference+"|All|All|All|All|All|All|All|All|All|All|#&#"+currentFilter, "", 0, "", roleId, pageSize, currentPage, "N",
					"", movList, filterSortStatus,
					initialInputScreen, "Y", applyCcyThreshold, SwtUtil.getCurrentUserId(request.getSession()),"<None>", scenarioId);

		} catch (Exception e) {
			e.printStackTrace();
		}



		return totalMap;

	}


	/**
	 * Called in the first load of screen to load the swf component
	 * <br><i>This method will redirect to ilmanalysismonitor.jsp file</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String ilmThroughPutMonitor()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String roleId = null;
		String userId = null;
		String defaultEntityId = null;
		String defaultCcyId = null;
		Date sysDate = null;
		String sysDateAsString = null;
		String screenId = SwtConstants.ILM_THOUPUT_MONITOR_ID;

		try {
			log.debug(this.getClass().getName() + "- [ilmThroughPutMonitor] - starting ");
			// Get the host id from swtutil
			hostId = SwtUtil.getCurrentHostId();
			// Get the role id from common data manager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the current user id from swtutil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the default entity id from swtutil
			defaultEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
			// get the default currency from SwtUtil
			defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
			// get the system date from swtutil
			sysDate = SwtUtil.getSystemDatewithoutTime();
			// get the formatted system date form swtutil
			sysDateAsString = SwtUtil.formatDate(sysDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			// Used to get the instance from Configuration file
			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request
			request.setAttribute("autoRefreshRate", new Integer(screenOption
					.getPropertyValue()));
			// Setting the hostId in request
			request.setAttribute("hostId", hostId);
			// Setting the default entity id in request
			request.setAttribute("defaultEntityId", defaultEntityId);
			// Setting the default currency id in request
			request.setAttribute("defaultCcyId", defaultCcyId);
			// Setting the role id in request
			request.setAttribute("roleId", roleId);
			// Setting the user id in request
			request.setAttribute("userId", userId);
			// Setting the item id in request
			request.setAttribute("itemId", SwtConstants.ILM_THOUPUT_MONITOR_ID);
			// Setting the system date as string in request
			request.setAttribute("sysDateAsString", sysDateAsString);
			// Setting the currency multiplier value
			request.setAttribute("useCcyMultiplier", "Y");


			request.setAttribute("entityId", request.getParameter("entityId"));
			request.setAttribute("selectedScenario", request.getParameter("selectedScenario"));
			request.setAttribute("currencyId", request.getParameter("currencyId"));
			request.setAttribute("accountGroup", request.getParameter("accountGroup"));
			request.setAttribute("fromILMMonitor", request.getParameter("fromILMMonitor"));


			// Set a unique sequence number will be used to stop the recalculation process

			log.debug(this.getClass().getName() + "- [flex] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [ilmThroughPutMonitor] method : - "
					  + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"ilmThroughPutMonitor",
							ILMAnalysisMonitorAction.class), request, "");
		}finally{
			hostId = null;
			roleId = null;
			userId = null;
			defaultEntityId = null;
			defaultCcyId = null;
			sysDate = null;
			sysDateAsString = null;
			screenId = null;
		}
		return getView("ilmthroughputratiomonitor");
	}
	/**
	 * Called in the first load of screen to load the swf component
	 * <br><i>This method will redirect to ilmanalysismonitor.jsp file</i>
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String ilmThroughPutRatioMonitorBreakdown()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [ilmThroughPutRatioMonitorBreakdown] - starting ");
			// Initializing the ScreenOption instance

			log.debug(this.getClass().getName() + "- [ilmThroughPutRatioMonitorBreakdown] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [ilmThroughPutRatioMonitorBreakdown] method : - "
					  + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"ilmThroughPutRatioMonitorBreakdown",
							ILMAnalysisMonitorAction.class), request, "");
		}finally{
		}
		return getView("ilmthroughputratiomonitorbreakdown");
	}
	/**
	 * This method gets ilmThroughPut Ratio Monitor  details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String ilmThroughPutMonitorDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String errorMessage = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		ArrayList<LabelValueBean> accountGroupList;
		ArrayList<LabelValueBean> scenarioList = null;
		HttpSession session = null;
		ArrayList<TabInfo> tabs = null;
		String hostId = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;

		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<EntityUserAccess> collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		Date sysDateInCcyTimeframe = null;
		String defaultEntityId = null;
		String roleId = null;
		Boolean currentEntityFound = false;
		String defaultCcyId = null;
		String selectedScenario = null;
		boolean entityChanged = false;
		String sysDateAsString = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		String nowEntityTime = "";
		String nowCurrencyTime = "";
		String activeArchiveDB = null;
		Date selectedDate = null;
		String accountGroupName = null;
		String calculateAs = null;
		boolean firstLoad = false;
		boolean fromILMMonitor = false;
		ILMGeneralMaintenanceManager ilmGenMgr = null;
		HashMap<String, ILMCcyParameters> ccyParams = new HashMap<String, ILMCcyParameters>();

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ ilmThroughPutMonitorDisplay ] - " + "Entry");
			session = request.getSession();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ilmThrouputMonitor";
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.ILM_THOUPUT_MONITOR_ID+"", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);


			String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, formatDate);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();


			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			entitiesLblValue.addAll(entityAccessLvlColl);

			/****************************Selects********************/
			firstLoad = "true".equalsIgnoreCase(request.getParameter("firstLoad"));
			fromILMMonitor = "true".equalsIgnoreCase(""+request.getParameter("fromILMScreen"));
			String userPref = SwtUtil.getPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
					"display", "last_profile");
			if(!fromILMMonitor && firstLoad && !SwtUtil.isEmptyOrNull(userPref)) {
				try {
					JSONObject prefJSON = new JSONObject(userPref);

					defaultEntityId  = (String) prefJSON.get("ENTITY_ID");
					defaultCcyId  = (String) prefJSON.get("CURRENCY_CODE");
					selectedAccountGroup  = (String) prefJSON.get("ACCOUNT_GROUP");
					selectedScenario  = (String) prefJSON.get("SCENARIO_ID");
					calculateAs  = (String) prefJSON.get("CALCULATE_AS");

				}catch (Exception e) {
				}

			}else {
				selectedAccountGroup = request.getParameter("accountGroup");
				selectedScenario = request.getParameter("selectedScenario");
				calculateAs = request.getParameter("calculateAs");



				if (!SwtUtil.isEmptyOrNull(request.getParameter("entityChanged")) && request.getParameter("entityChanged").equals("true"))
					entityChanged = true;

				// Retrieve the entity id selected by the user, if first load then it will be default entity
				if (!SwtUtil.isEmptyOrNull(request.getParameter("entityId"))){
					defaultEntityId = request.getParameter("entityId");
				}else{
					String currentEntity = ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser()
							.getCurrentEntity();

					// Set currentEntityFound boolean to true if the current entity exists in the collUserEntityAccess list
					for (Iterator iterator = collUserEntityAccess.iterator(); iterator.hasNext();) {
						EntityUserAccess entityUserAccess = (EntityUserAccess) iterator.next();
						if (entityUserAccess.getEntityId().equals(currentEntity)){
							currentEntityFound = true;
						}
					}

					// the default entity will be the current entity if it exists in the list
					if (!currentEntityFound){
						// When we have more than one entity in the list
						if (collUserEntityAccess.size() > 0){
							defaultEntityId = collUserEntityAccess.iterator().next().getEntityId();
							// The case when we don't have access to any entity in the ILM screen, only All value is available
						}else{
							// A test will be in Flex, if the All value is selected then show an error without getting the data
							defaultEntityId = "All";
						}
					}else{
						defaultEntityId = currentEntity;
					}
				}


				// Retrieve the default currency id for the user
				if (SwtConstants.ALL_VALUE.equals(defaultEntityId)){
					defaultCcyId = request.getParameter("currencyId");
				}else{
					if(entityChanged)
						defaultCcyId = SwtUtil.getDomesticCurrencyForUser(request, hostId, defaultEntityId);
					else
						defaultCcyId = request.getParameter("currencyId");
				}

			}

			width = SwtUtil.getPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
					"display", "column_width");
			// Get the selected date with Date format
			sysDateAsString = request.getParameter("selectedDate");
			ReportsManager reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));
			sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(
					defaultEntityId, defaultCcyId, SwtUtil.getSystemDateFromDB());

			if (SwtUtil.isEmptyOrNull(sysDateAsString)){

				sysDateAsString = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}
			// Instantiate the ccyList
			ccyList = new ArrayList<LabelValueBean>();
			/*// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));*/
			// get the collection of currency access
			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));

			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			if (collUserEntityAccess != null)
				ccyList.addAll(ccyAccessColl);

			boolean entityExistInList = false;

			for (LabelValueBean o : ccyList){
				if(o.getValue().equals(defaultCcyId))
					entityExistInList = true;
			}
			if(!entityExistInList){
				if(ccyList != null && ccyList.size()>0)
					defaultCcyId = ccyList.iterator().next().getValue();
			}

			/****CurrencyCombo***********/
			Iterator j = ccyList.iterator();

			LabelValueBean row = null;
//			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
//					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(defaultCcyId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));

			}

			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/****entity Combo***********/
			lstOptions = new ArrayList<OptionInfo>();


			j = entitiesLblValue.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(defaultEntityId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));


			/*****Account Group Combo***********/

			lstOptions = new ArrayList<OptionInfo>();
			accountGroupList = getAccountGroupsList(request,hostId,defaultEntityId,defaultCcyId,true);
			j = accountGroupList.iterator();
			row = null;
			boolean accountFound = false;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedAccountGroup)) {
					accountFound = true;
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				}
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_GROUPSLIST, lstOptions));

			if(accountGroupList.isEmpty()) {
				selectedAccountGroup = null;
			}
			else if(!accountFound && !accountGroupList.isEmpty()) {
				selectedAccountGroup = accountGroupList.get(0).getValue();
			}


//			if(defaultCcyId.equals(SwtConstants.ALL_LABEL)) {
//				selectedScenario = SwtConstants.ALL_LABEL;
//			}

			/*****Scenario Group Combo***********/
			lstOptions = new ArrayList<>();
			scenarioList = new ArrayList<>();
			scenarioList.add(new LabelValueBean(SwtConstants.STANDARD_VALUE, SwtUtil.getMessage("ilmExcelReport.standard", request)));
			scenarioList.addAll(getScenarioList(request,hostId,defaultEntityId,defaultCcyId,true));
			j = scenarioList.iterator();
			boolean scenarioFound = false;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(selectedScenario)) {
					scenarioFound = true;
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				}
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ACCOUNT_SCENARIOLIST, lstOptions));

			if(scenarioList.isEmpty()) {
				selectedScenario = null;
			}else if(!scenarioFound &&  !scenarioList.isEmpty()) {
				selectedScenario = scenarioList.get(0).getValue();
			}

			lstOptions = new ArrayList<>();

			if(SwtUtil.isEmptyOrNull(calculateAs)) {
				calculateAs = ACT_T_FOR_T;
			}

			lstOptions.add(new OptionInfo(ACT_T_FOR_T, SwtUtil.getMessage("ilmthroughputActTvsForecT", request), ACT_T_FOR_T.equals(calculateAs)));
			lstOptions.add(new OptionInfo(ACT_T_FOR_L, SwtUtil.getMessage("ilmthroughputActTvsForecL", request), ACT_T_FOR_L.equals(calculateAs)));
			lstOptions.add(new OptionInfo(ACT_T_ACT_L, SwtUtil.getMessage("ilmthroughputActTvsActT", request), ACT_T_ACT_L.equals(calculateAs)));

			lstSelect.add(new SelectInfo(SwtConstants.THROUPUT_CALCULATE_THROUGHPUT_AS , lstOptions));

			responseConstructor.formSelect(lstSelect);


			/****************dataGrid******************/


			/*************************************Singletons******************************/
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(PCMConstant.CURRENCYCODE, defaultCcyId);
			responseConstructor.createElement(PCMConstant.SELECTED_ENTITY, defaultEntityId);
			responseConstructor.createElement(PCMConstant.VALUE_DATE, sysDateAsString);
			responseConstructor.createElement(SwtConstants.SYSTEM_DATE, SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));
			responseConstructor.createElement("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffsetILM(request, defaultEntityId));

			ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
					.getBean("screenOptionManager"));
			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(userId);
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(SwtConstants.ILM_THOUPUT_MONITOR_ID);
			// Setting the property name
			screenOption.getId().setPropertyName(SwtConstants.PROPNAME_REFRESH_RATE);
			// Fetching the refresh rate
			screenOption = screenOptionManager.getRefreshRate(screenOption);
			// Setting the rate in request


			responseConstructor.createElement(PCMConstant.REF_RATE, new Integer(screenOption
					.getPropertyValue()));

			String tabList = "";
			StringBuilder builder = new StringBuilder();
			//Date selectedDate, String hostId, String roleId, String dbLink
			activeArchiveDB = ilmAnalysisMonitorManager.getCurrentDbLink(hostId);
			selectedDate = SwtUtil.parseDate(sysDateAsString, SwtUtil.getCurrentDateFormat(request.getSession()));
			ArrayList<ThroughputMonitorRecord> list = ilmAnalysisMonitorManager.getIlmThroughPutRatioData(defaultEntityId, defaultCcyId,
					selectedAccountGroup, selectedScenario, selectedDate, hostId , roleId, activeArchiveDB, calculateAs);
			ThroughputMonitorRecord record = null;
			builder.append("<tabs>");
			for (Iterator<ThroughputMonitorRecord> it = list.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				record = it.next();
				tabList+=record.getId().getIlmGroup()+",";

				// Get the now times
				String[] nowDates = ilmAnalysisMonitorManager.getNowDates(defaultEntityId, record.getId().getCurrencyCode());
				if (nowDates != null && nowDates.length == 2) {
					nowCurrencyTime = nowDates[0];
					nowEntityTime = nowDates[1];
				}
				Date ccyDate = SwtUtil.parseDate(nowCurrencyTime, "yyyy-MM-dd HH:mm");

				if(!SwtUtil.isEmptyOrNull(record.getThreshold1Time()) && record.getThreshold1Percent() != null) {
					Date threshold1Date = SwtUtil.parseDate(sysDateAsString+ " "+record.getThreshold1Time(), SwtUtil.getCurrentDateFormat(session)+" HH:mm");

					if(ccyDate.before(threshold1Date)) {
						record.setThreshold1State(0);
						record.setThreshold1Color("#DDDDDD !important;");
					}else {
						if(record.getThreshold1ActualPercent()>record.getThreshold1Percent()) {
							record.setThreshold1State(1);
							record.setThreshold1Color("#aadd88 !important;");
						}else {
							record.setThreshold1State(2);
							record.setThreshold1Color("#F79391 !important;");
						}
					}
					record.setThreshold1AsString(record.getThreshold1Time()+" "+String.format("%6.2f%n", record.getThreshold1ActualPercent())+ "% / "+String.format("%6.2f%n", record.getThreshold1Percent())+"%");
				}

				if(!SwtUtil.isEmptyOrNull(record.getThreshold2Time()) && record.getThreshold2Percent() != null) {
					Date threshold2Date = SwtUtil.parseDate(sysDateAsString+ " "+record.getThreshold2Time(), SwtUtil.getCurrentDateFormat(session)+" HH:mm");
					if(ccyDate.before(threshold2Date)) {
						record.setThreshold2State(0);
						record.setThreshold2Color("#DDDDDD !important;");
					}else {

						if(record.getThreshold2ActualPercent()>record.getThreshold2Percent()) {
							record.setThreshold2State(1);
							record.setThreshold2Color("#aadd88 !important;");

						}else {
							record.setThreshold2State(2);
							record.setThreshold2Color("#F79391 !important;");

						}

					}

					record.setThreshold2AsString(record.getThreshold2Time()+" "+String.format("%6.2f%n", record.getThreshold2ActualPercent())+ "% / "+String.format("%6.2f%n", record.getThreshold2Percent())+"%");
				}

				record.setCurrentAsString(record.getCurrentTime()+" / "+String.format("%6.2f%n", record.getCurrent())+" %");

				for (int k = 0; k < accountGroupList.size(); k++) {
					if(accountGroupList.get(k).getValue().equals(record.getId().getIlmGroup())) {
						accountGroupName = accountGroupList.get(k).getLabel();
						break;
					}
				}
				ILMCcyParameters  params = null;
				if(ccyParams.containsKey(hostId+ record.getId().getEntityId()+ record.getId().getCurrencyCode())) {
					params = ccyParams.get(hostId+ record.getId().getEntityId()+ record.getId().getCurrencyCode());
				}else{
					params = ilmGenMgr.getILMCcyParameterEditableData(hostId, record.getId().getEntityId(), record.getId().getCurrencyCode());
					ccyParams.put(hostId+ record.getId().getEntityId()+ record.getId().getCurrencyCode(), params);
				}

				builder.append("<row dataTime='" + record.getChartsTimeData() + "'"
							   + " dataPercentage='"+ record.getChartsPercentData() + "' "
							   + " threshold1Time='"+ record.getThreshold1Time() + "' "
							   + " threshold2Time='"+ record.getThreshold2Time() + "' "
							   + " threshold1Percentage='"+ record.getThreshold1Percent() + "' "
							   + " threshold2Percentage='"+ record.getThreshold2Percent() + "' "
							   + " threshold1State='"+ record.getThreshold1State() + "' "
							   + " threshold2State='"+ record.getThreshold2State() + "' "
							   + " threshold1Color='"+ record.getThreshold1Color() + "' "
							   + " threshold2Color='"+ record.getThreshold2Color() + "' "
							   + " startTime='"+ (params != null? params.getClearingStartTime():"")+ "' "
							   + " endTime='"+  (params != null?params.getClearingEndTime():"") + "' "
							   + ">"
							   + "<id><![CDATA["+record.getId().getIlmGroup()+"]]></id>"
							   + "<name><![CDATA["+accountGroupName+"]]></name>"
							   + "</row>");
			}

			if(tabList.length()>0) {
				tabList = tabList.substring(0,tabList.length()-1);
			}
			responseConstructor.createElement("tabList", tabList);



			builder.append("</tabs>");

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.appendText(builder.toString());


			//Create the grid data
			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getThrouputMonitorGridColumns(width, columnOrder, hiddenColumns, request));


			//Get Grid Data

			// form rows (records)
			responseConstructor.formRowsStart(list.size());
			// Iterating category details
			for (Iterator<ThroughputMonitorRecord> it = list.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				ThroughputMonitorRecord element = it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME, element.getAlerting());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, element.getId().getEntityId());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, element.getId().getCurrencyCode());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME, element.getId().getIlmGroup());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME, element.getForecastedInflow() != null?SwtUtil.formatCurrency(element.getId().getCurrencyCode(), BigDecimal.valueOf(element.getForecastedInflow())) : "", "AMOUNT" , false, true);
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME, element.getForecastedOutflows() !=null ?SwtUtil.formatCurrency(element.getId().getCurrencyCode(), BigDecimal.valueOf(element.getForecastedOutflows())):"", "AMOUNT" , false, true);
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME, element.getActualsInflow() != null?SwtUtil.formatCurrency(element.getId().getCurrencyCode(), BigDecimal.valueOf(element.getActualsInflow())):"", "AMOUNT" , false, true);
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, element.getActualsOutflows() != null?SwtUtil.formatCurrency(element.getId().getCurrencyCode(), BigDecimal.valueOf(element.getActualsOutflows())):"", "AMOUNT" , false, true);
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME, element.getUnsetteledOutflows() != null?SwtUtil.formatCurrency(element.getId().getCurrencyCode(), BigDecimal.valueOf(element.getUnsetteledOutflows())):"", "AMOUNT" , (element.getUnsetteledOutflows() < 0  ? true : false ), true);
//
//				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME, element.getForecastedInflow()+"");
//				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME, element.getForecastedOutflows()+"");
//				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME, element.getActualsInflow()+"");
//				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, element.getActualsOutflows()+"");
//				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, element.getUnsetteledOutflows()+"");
//

				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME, element.getThreshold1AsString());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME, element.getThreshold2AsString());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, element.getCurrentAsString());



				responseConstructor.createRowElement(SwtConstants.THROUPUT_THRESHOLD1_COLOR_TAGNAME, element.getThreshold1Color());
				responseConstructor.createRowElement(SwtConstants.THROUPUT_THRESHOLD2_COLOR_TAGNAME, element.getThreshold2Color());


				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();


			xmlWriter.endElement(componentId);
			//Save user preferences
			if(!fromILMMonitor) {
				JSONObject sampleObject = new JSONObject();
				sampleObject.put("ENTITY_ID", defaultEntityId);
				sampleObject.put("CURRENCY_CODE", defaultCcyId);
				sampleObject.put("ACCOUNT_GROUP", selectedAccountGroup);
				sampleObject.put("SCENARIO_ID", selectedScenario);
				sampleObject.put("CALCULATE_AS", calculateAs);

				SwtUtil.setPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
						"display", "last_profile", sampleObject.toString());
			}

			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [ilmThroughPutMonitorDisplay] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [ilmThroughPutMonitorDisplay] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [ilmThroughPutMonitorDisplay] - Exit");

		}

		return null;
	}

	/**
	 * This method gets ilmThroughPut Ratio Monitor  details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String ilmMonitorOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String errorMessage = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		ArrayList<LabelValueBean> accountGroupList;
		HttpSession session = null;
		String hostId = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;

		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<EntityUserAccess> collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		String roleId = null;
		String width = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ ilmMonitorOptions ] - " + "Entry");
			session = request.getSession();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ilmThrouputMonitor";
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.ILM_THOUPUT_MONITOR_ID+"", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);


			String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, formatDate);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			/*************************************Singletons******************************/
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.addAll(entityAccessLvlColl);

			/****************************Selects********************/


//			String currentEntity = ((CommonDataManager) request.getSession()
//					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
//					.getCurrentEntity();

			// Set currentEntityFound boolean to true if the current entity exists in the collUserEntityAccess list

			HashMap<String, ArrayList<String>> currencyDistinctList = new HashMap<String, ArrayList<String>>();
			for (LabelValueBean e : entitiesLblValue){
				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(e.getValue(), roleId));
				for (LabelValueBean c : ccyAccessColl){
					if(!currencyDistinctList.containsKey(c.getValue())) {
						ArrayList<String> entityList = new ArrayList<String>();
						entityList.add(e.getValue());
						currencyDistinctList.put(c.getValue(), entityList);
					}else {
						ArrayList<String> entityList = currencyDistinctList.get(c.getValue());
						entityList.add(e.getValue());
						currencyDistinctList.put(c.getValue(), entityList);
					}
				}
			}

			width = SwtUtil.getPropertyValue(request, SwtConstants.ILM_OPTIONS_MONITOR_ID,
					"display", "column_width");
			// Get the selected date with Date format
			ccyList = new ArrayList<LabelValueBean>();
			/*// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));*/
			//Create the grid data
			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getILMOptionsGridColumns(width, null, null, request));
			//Get Grid Data

			// form rows (records)
			responseConstructor.formRowsStart(entitiesLblValue.size());
			int parentIdEntity = 0;
			int parentIdCcy = 0;
			int id = 0;
			// get the collection of currency access

			for (Entry<String, ArrayList<String>> entry : currencyDistinctList.entrySet()) {
				String key = entry.getKey();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, key);
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, "A");
				responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, "0000");

				responseConstructor.createRowElement("parentId", null);
				responseConstructor.createRowElement("indent", 0);
				responseConstructor.formRowEnd();
				parentIdEntity = id;
				id++;
				ArrayList<String> entityList = entry.getValue();

//				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(e.getValue(), roleId));
				for (String e : entityList){
					accountGroupList = getAccountGroupsList(request,hostId,e, key,false);

					responseConstructor.formRowStart();
					responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, e);
					responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, "B");
					responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, "2553");

					responseConstructor.createRowElement("parentId", parentIdEntity);
					responseConstructor.createRowElement("indent", 1);
					parentIdCcy = id;
					id++;
					responseConstructor.formRowEnd();

					/****************dataGrid******************/

					for (int i = 0; i < accountGroupList.size(); i++) {
						responseConstructor.formRowStart();
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, accountGroupList.get(i).getValue());
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, "123");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, "C");
						responseConstructor.createRowElement("parentId", parentIdCcy);
						responseConstructor.createRowElement("indent", 2);

						responseConstructor.formRowEnd();
						id++;
					}


				}

			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();





			xmlWriter.endElement(componentId);

			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [ilmMonitorOptions] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [ilmMonitorOptions] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [ilmMonitorOptions] - Exit");

		}

		return null;
	}
	/**
	 * This method gets ilmThroughPut Ratio Monitor  details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String ilmGetSummaryTabList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String errorMessage = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		HttpSession session = null;
		String hostId = null;
		String roleId = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;
		String [] summaryList = null;
		String  summaryAsString = null;
		String ilmTabAsString = null;
		String[] ilmTabEntityList = null;
		String[] ilmTabAcctGrpList = null;
		String ilmTabAcctGrpAsString = null;
		String ilmDateAsString = null;
		String[] ilmDateList = null;
		String globalViewAsString= null;
		String[] globalViewList= null;
		String groupAnalysisAsString= null;
		String[] groupAnalysisList = null;
		String combinedViewAsString= null;
		String[] combinedViewList = null;

		Boolean currentEntityFound = false;
		String defaultCcyId = null;
		boolean entityChanged = false;
		String defaultEntityId = null;

		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		ScreenOption scrOption = null;
		Iterator<ScreenOption> iterator = null;
		String configXml = null;
		Boolean isSaveScreenOption = true;
		String entityPartValue  = null;
		String ccyPartValue = null;
		String accGrpPartValue = null;
		HashMap<String, String> entityCcyListOfAccGrps = new HashMap<String, String>();
		HashMap<String, String> entityCcyListTabs = new HashMap<String, String>();
		String orderCcyString= null;
		String[] orderCcyList = null;
		String orderEntityString= null;
		String[] orderEntityList = null;
		ReportsManager reportsManager = null;
		HashMap<String, Integer> orderMap = new HashMap<String, Integer>();

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ ilmGetSummaryTabList ] - " + "Entry");
			session = request.getSession();
			reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			defaultEntityId = request.getParameter("entityId");
			defaultCcyId = request.getParameter("currencyId");

			componentId = "ilmSummary";
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.ILM_THOUPUT_MONITOR_ID+"", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			/*************************************Singletons******************************/

			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			userId = SwtUtil.getCurrentUserId(request.getSession());
			liquidityScreenOption = new ScreenOption();
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName( SwtConstants.PROPNAME_ILM_OPTIONS );
			Collection<ScreenOption>  options =   screenOptionManager.getScreenOption(liquidityScreenOption);
			if (options != null && options.size() > 0) {
				iterator = options.iterator();
				while (iterator.hasNext()) {
					scrOption = iterator.next();
					configXml= scrOption.getPropertyValue();
					break;

				}
			}
			if(!SwtUtil.isEmptyOrNull(configXml)) {
				if(configXml.indexOf("<Summary>") != -1) {
					summaryAsString = configXml.substring(configXml.indexOf("<Summary>")+9, configXml.indexOf("</Summary>"));
					summaryList=  summaryAsString.split(",");
				}

				if(configXml.indexOf("<ILMTabEntity>") != -1) {
					ilmTabAsString = configXml.substring(configXml.indexOf("<ILMTabEntity>")+14, configXml.indexOf("</ILMTabEntity>"));
					ilmTabEntityList= ilmTabAsString.split(",");


				}
				if(configXml.indexOf("<ILMTabAcctGrp>") != -1) {
					ilmTabAcctGrpAsString = configXml.substring(configXml.indexOf("<ILMTabAcctGrp>")+15, configXml.indexOf("</ILMTabAcctGrp>"));
					ilmTabAcctGrpList=  ilmTabAcctGrpAsString.split(",");

				}
				if(configXml.indexOf("<GroupAnalysis>") != -1) {
					groupAnalysisAsString = configXml.substring(configXml.indexOf("<GroupAnalysis>")+15, configXml.indexOf("</GroupAnalysis>"));
					groupAnalysisList= groupAnalysisAsString.split(",");
				}
				if(configXml.indexOf("<CombinedView>") != -1) {
					combinedViewAsString = configXml.substring(configXml.indexOf("<CombinedView>")+14, configXml.indexOf("</CombinedView>"));
					combinedViewList=  combinedViewAsString.split(",");

				}
				if(configXml.indexOf("<GlobalView>") != -1) {
					globalViewAsString= configXml.substring(configXml.indexOf("<GlobalView>")+12, configXml.indexOf("</GlobalView>"));
					globalViewList=  globalViewAsString.split(",");

				}
				if(configXml.indexOf("<TabDate>") != -1) {
					ilmDateAsString = configXml.substring(configXml.indexOf("<TabDate>")+9, configXml.indexOf("</TabDate>"));
					ilmDateList=  ilmDateAsString.split(",");
				}

				if(configXml.indexOf("<orderCcy>") != -1) {
					orderCcyString = configXml.substring(configXml.indexOf("<orderCcy>")+10, configXml.indexOf("</orderCcy>"));
					orderCcyList = orderCcyString.split(",");
					for (int i = 0; i < orderCcyList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderCcyList[i])) && orderCcyList[i].indexOf('=') > -1){
							orderMap.put(orderCcyList[i].split("=")[0], Integer.parseInt(orderCcyList[i].split("=")[1]));
						}

					}
				}
				if(configXml.indexOf("<orderEntity>") != -1) {
					orderEntityString = configXml.substring(configXml.indexOf("<orderEntity>")+13, configXml.indexOf("</orderEntity>"));
					orderEntityList=  orderEntityString.split(",");
					for (int i = 0; i < orderEntityList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderEntityList[i])) && orderEntityList[i].indexOf('=') > -1){
							orderMap.put(orderEntityList[i].split("=")[0], Integer.parseInt(orderEntityList[i].split("=")[1]));
						}

					}

				}


			}

			String mapKey = null;
			boolean grpAnalysIsfound = false;
			boolean globalIsfound = false;
			boolean combinedIsfound= false;
			if(ilmTabAcctGrpList != null && ilmTabAcctGrpList.length >0) {
				for (int i = 0; i < ilmTabAcctGrpList.length; i++) {
					ccyPartValue = ilmTabAcctGrpList[i].split("&&")[0];
					entityPartValue = ilmTabAcctGrpList[i].split("&&")[1];
					accGrpPartValue = ilmTabAcctGrpList[i].split("&&")[2];
					mapKey = entityPartValue + "&&" + ccyPartValue;
					entityCcyListOfAccGrps.put(mapKey,
							entityCcyListOfAccGrps.containsKey(mapKey)
									? entityCcyListOfAccGrps.get(mapKey) + "," + accGrpPartValue
									: accGrpPartValue);

				}
			}


			StringBuilder builder = new StringBuilder();

			builder.append("<tabs>");
			String tabList = "";
			String tabId = null;
			String tabName = null;
			if(ilmTabEntityList != null && ilmTabEntityList.length> 0) {
				for (int i = 0; i < ilmTabEntityList.length; i++) {
					tabId = ilmTabEntityList[i];
					ccyPartValue = tabId.split("&&")[0];
					entityPartValue = tabId.split("&&")[1];
					if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultEntityId)) {
						if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultCcyId)) {
							tabName = ccyPartValue  + "/" + entityPartValue ;
						} else {
							if(!ccyPartValue.equals(defaultCcyId)) {
								continue;
							}
							tabName = entityPartValue;
						}
					} else {
						if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultCcyId)) {
							if(!defaultEntityId.equals(entityPartValue)) {
								continue;
							}
							tabName = ccyPartValue;
						} else {
							if(!defaultEntityId.equals(entityPartValue) || !ccyPartValue.equals(defaultCcyId) ) {
								continue;
							}

							tabName = ccyPartValue + "/" + entityPartValue;
						}
					}

					tabId = entityPartValue   + "/" + ccyPartValue;


					final String keyTab =  ccyPartValue + "&&" + entityPartValue;
					if(groupAnalysisList != null)
						grpAnalysIsfound = Arrays.stream(groupAnalysisList)
								.anyMatch(x -> x.equals(keyTab));
					if(globalViewList != null)
						globalIsfound = Arrays.stream(globalViewList)
								.anyMatch(x -> x.equals(keyTab));
					if(combinedViewList != null)
						combinedIsfound = Arrays.stream(combinedViewList)
								.anyMatch(x -> x.equals(keyTab));

//				String tabDate = Arrays.stream(ilmDateList)
//                .filter(x -> x.startsWith(keyTab))
//                .findFirst();
					String tabDate = null;
					if(ilmDateList != null && ilmDateList.length > 0) {
						tabDate  = Arrays
								.stream(ilmDateList)
								.filter(x -> x.startsWith(keyTab))
								.findFirst()
								.orElse("T");

						tabDate = tabDate.substring(tabDate.lastIndexOf("=") + 1);
					}


					Date sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(
							defaultEntityId, defaultCcyId, SwtUtil.getSystemDateFromDB());
					if(!SwtUtil.isEmptyOrNull(tabDate) && "T-1".equals(tabDate)) {

						Calendar c = Calendar.getInstance();
						c.setTime(sysDateInCcyTimeframe);
						c.add(Calendar.DATE, -1);
						sysDateInCcyTimeframe = c.getTime();

					}
					tabDate = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
							.getCurrentSystemFormats(request.getSession())
							.getDateFormatValue());

					tabList += tabId + ",";
//				builder.append("<row id='" + tabId + "' name='" + tabName + "' selectedGroups='"
//						+ entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue) + "'" + " grpAnalysIsfound='"
//						+ grpAnalysIsfound + "' " + " globalIsfound='" + globalIsfound + "' " + " combinedIsfound='"
//						+ combinedIsfound + "' " + "></row>");
					String tabSelectedGroups = SwtUtil.isEmptyOrNull(entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue))?"":entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue);

					int orderCcy = (orderMap.containsKey(ccyPartValue)?orderMap.get(ccyPartValue):999) + 1000;
					int  orderEntity= (orderMap.containsKey(ccyPartValue+"&&"+entityPartValue)?orderMap.get(ccyPartValue+"&&"+entityPartValue):999) + 1000;

					String order =  orderCcy +""+orderEntity;
					builder.append("<row> <id><![CDATA[" + tabId + "]]></id> <name><![CDATA[" + tabName
								   + "]]></name> <selectedGroups><![CDATA["
								   + tabSelectedGroups+ "]]></selectedGroups>"
								   + " <grpAnalysIsfound>" + grpAnalysIsfound + "</grpAnalysIsfound> " + " <globalIsfound>"
								   + globalIsfound + "</globalIsfound> " + " <combinedIsfound>" + combinedIsfound+ "</combinedIsfound>  <tabOrder>" + order+ "</tabOrder>  "
								   + "<selectedDate><![CDATA["+tabDate +"]]>  </selectedDate>" + "</row>");
				}
			}

			if (tabList.length() > 0) {
				tabList = tabList.substring(0, tabList.length() - 1);
			}
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement("tabList", tabList);

			builder.append("</tabs>");
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.appendText(builder.toString());
			xmlWriter.endElement(componentId);

			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [ilmGetSummaryTabList] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [ilmGetSummaryTabList] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getDashboardDetails] - Exit");

		}

		return null;
	}
	/**
	 * This method gets ilmThroughPut Ratio Monitor  details.
	 *
	 * @param ActionMapping       mapping
	 * @param ActionForm          form
	 * @param HttpServletRequest  request
	 * @param HttpServletResponse response
	 * @return ActionForward
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public String ilmSummaryGridDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String errorMessage = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		String selectedCurrencyCode = null;
		String selectedAccountGroup = null;
		ArrayList<LabelValueBean> accountGroupList;
		HttpSession session = null;
		String hostId = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;

		Collection<LabelValueBean> entitiesLblValue = null;
		Collection<EntityUserAccess> collUserEntityAccess = null;
		Collection<LabelValueBean> entityAccessLvlColl = null;
		Collection<LabelValueBean> ccyList = null;
		Collection<LabelValueBean> ccyAccessColl = null;
		Boolean currentEntityFound = false;
		String defaultCcyId = null;
		boolean entityChanged = false;
		String roleId = null;
		String width = null;
		String defaultEntityId = null;

		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		ScreenOption scrOption = null;
		Iterator<ScreenOption> iterator = null;
		String configXml = null;
		String [] summaryList = null;
		String  summaryAsString = null;
		String ilmTabAsString = null;
		String[] ilmTabEntityList = null;
		String[] ilmTabAcctGrpList = null;
		String ilmTabAcctGrpAsString = null;
		String ilmDateAsString = null;
		String[] ilmDateList = null;
		String globalViewAsString= null;
		String[] globalViewList= null;
		String groupAnalysisAsString= null;
		String[] groupAnalysisList = null;
		String combinedViewAsString= null;
		String[] combinedViewList = null;
		String orderCcyString= null;
		String[] orderCcyList = null;
		String orderEntityString= null;
		String[] orderEntityList = null;

		HashMap<String, Integer> orderMap = new HashMap<String, Integer>();

		Boolean isSaveScreenOption = true;
		String entityPartValue  = null;
		String ccyPartValue = null;
		String accGrpPartValue = null;
		HashMap<String, String> entityCcyListOfAccGrps = new HashMap<String, String>();
		HashMap<String, String> entityCcyListTabs = new HashMap<String, String>();
		ReportsManager reportsManager = null;
		long time = System.currentTimeMillis();

		Date valueDate = null;
		String includeSOD = "Y";
		String includeCR = "N";
		String includeOpenMovements = "N";
		String includeSumyByCutoff = "N";
		String hideNonSumAcct = "N";
		String applyCcyMultiplier = "N";

		String currencyPattern = null;

		boolean firstLoad = false;
		String profileLastSelectedEntity = null;
		String profileLastSelectedCcy = null;


		Map<String, ILMSummaryRecord> tempMap = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ ilmSummaryGridDisplay ] - " + "Entry");
			session = request.getSession();
			reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));


			firstLoad = "true".equals(request.getParameter("firstLoad"));



			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ilmSummary";
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			cdm = (CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN);

			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.ILMANALYSIS_MONITOR_ID+"", cdm.getUser());
			if (menuItem != null)
				menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);


			currencyPattern = SwtUtil.getCurrentCurrencyFormat(session);

			valueDate = SwtUtil.getSysParamDate();

			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			entitiesLblValue.addAll(entityAccessLvlColl);


			String formatDate = SwtUtil.getCurrentDateFormat(request.getSession());
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, formatDate);
			xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
			ILMConfig ilmGeneralConfig = getLiquidityMonitorConfig(request, hostId, userId);
			if(ilmGeneralConfig != null && firstLoad) {
//				xmlWriter.addAttribute("withrefresh", ilmGeneralConfig.getWithRefresh());
//				xmlWriter.addAttribute("refresh", ilmGeneralConfig.getRefreshRate());

				includeSOD = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getIncludecreditLineSummary())?"Y":ilmGeneralConfig.getIncludeSodSummary();
				includeCR = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getIncludecreditLineSummary())?"N":ilmGeneralConfig.getIncludecreditLineSummary();
				includeSumyByCutoff = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getSumCutOffSummary())?"N":ilmGeneralConfig.getSumCutOffSummary();
				includeOpenMovements = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getIncludeOpenMvmtSummary())?"N":ilmGeneralConfig.getIncludeOpenMvmtSummary();
				hideNonSumAcct = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getHideNonSumAccountSummary())?"N":ilmGeneralConfig.getHideNonSumAccountSummary();
				applyCcyMultiplier = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getUseCurrencyMultiplier())?"Y":ilmGeneralConfig.getUseCurrencyMultiplier();

				profileLastSelectedEntity = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getLastSelectedEntity())?"None":ilmGeneralConfig.getLastSelectedEntity();
				profileLastSelectedCcy = SwtUtil.isEmptyOrNull(ilmGeneralConfig.getLastSelectedCcy())?"None":ilmGeneralConfig.getLastSelectedCcy();


			}else {
				includeSOD = request.getParameter("includeSod");
				includeCR = request.getParameter("includecreditLine");
				includeSumyByCutoff = request.getParameter("sumCutOff");
				includeOpenMovements = request.getParameter("includeOpenMvmt");
				hideNonSumAcct = request.getParameter("hideNonSumAccount");
				applyCcyMultiplier = request.getParameter("applyCcyMultiplier");
			}
			xmlWriter.addAttribute("includeSod", includeSOD);
			xmlWriter.addAttribute("includecreditLine", includeCR);
			xmlWriter.addAttribute("sumCutOff", includeSumyByCutoff);
			xmlWriter.addAttribute("includeOpenMvmt", includeOpenMovements);
			xmlWriter.addAttribute("hideNonSumAccount", hideNonSumAcct);
			xmlWriter.addAttribute("currencymultiplier", applyCcyMultiplier);
			xmlWriter.addAttribute("valueDate",SwtUtil.formatDate(valueDate, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));

			if(ilmGeneralConfig != null && !SwtUtil.isEmptyOrNull(ilmGeneralConfig.getWithRefresh()))
				xmlWriter.addAttribute("withrefresh", ilmGeneralConfig.getWithRefresh());
			if(ilmGeneralConfig != null && !SwtUtil.isEmptyOrNull(ilmGeneralConfig.getRefreshRate()))
				xmlWriter.addAttribute("refresh", ilmGeneralConfig.getRefreshRate());

			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);

			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			/*************************************Singletons******************************/
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			// Get the list of entities that will be figured in the entity combo
			entitiesLblValue = new ArrayList<LabelValueBean>();
			// Get the collection of user entity access
			collUserEntityAccess = ilmAnalysisMonitorManager.getEntitiesHasCurrencies(hostId, roleId);

			// Get the entity access collection
			entityAccessLvlColl = SwtUtil.convertEntityAcessCollectionLVL(
					collUserEntityAccess, null);
			// Add the All option to the user as it could select All value in ILM screen
			entitiesLblValue.add(new LabelValueBean(SwtConstants.FACILITY_NONE, SwtConstants.FACILITY_NONE));
			entitiesLblValue.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			entitiesLblValue.addAll(entityAccessLvlColl);

			/****************************Selects********************/



			LinkedHashMap<String, ArrayList<String>> currencyDistinctList = new LinkedHashMap<String, ArrayList<String>>();
			for (LabelValueBean e : entitiesLblValue){
				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(e.getValue(), roleId));
				for (LabelValueBean c : ccyAccessColl){
					if(!currencyDistinctList.containsKey(c.getValue())) {
						ArrayList<String> entityList = new ArrayList<String>();
						entityList.add(e.getValue());
						currencyDistinctList.put(c.getValue(), entityList);
					}else {
						ArrayList<String> entityList = currencyDistinctList.get(c.getValue());
						entityList.add(e.getValue());
						currencyDistinctList.put(c.getValue(), entityList);
					}
				}
			}



			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityChanged")) && request.getParameter("entityChanged").equals("true"))
				entityChanged = true;

			// Retrieve the entity id selected by the user, if first load then it will be default entity
			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityId"))){
				defaultEntityId = request.getParameter("entityId");
			}else{
//				String currentEntity = ((CommonDataManager) request.getSession()
//						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
//						.getCurrentEntity();
//
//				// Set currentEntityFound boolean to true if the current entity exists in the collUserEntityAccess list
//				for (Iterator iterator = collUserEntityAccess.iterator(); iterator.hasNext();) {
//			        EntityUserAccess entityUserAccess = (EntityUserAccess) iterator.next();
//			        if (entityUserAccess.getEntityId().equals(currentEntity)){
//			        	currentEntityFound = true;
//			        }
//			    }
//
//				// the default entity will be the current entity if it exists in the list
//				if (!currentEntityFound){
//					// When we have more than one entity in the list
//					if (collUserEntityAccess.size() > 0){
//						defaultEntityId = collUserEntityAccess.iterator().next().getEntityId();
//					// The case when we don't have access to any entity in the ILM screen, only All value is available
//					}else{
				// A test will be in Flex, if the All value is selected then show an error without getting the data
//						defaultEntityId = "None";
				defaultEntityId = SwtUtil.isEmptyOrNull(profileLastSelectedEntity)?"None":profileLastSelectedEntity;
//					}
//				}else{
//					defaultEntityId = currentEntity;
//				}
			}


			// Retrieve the default currency id for the user
			if (!SwtUtil.isEmptyOrNull(request.getParameter("currencyId")) || SwtConstants.ALL_VALUE.equals(defaultEntityId)){
				defaultCcyId = request.getParameter("currencyId");
			}else{
				if(entityChanged && !"None".equals(defaultEntityId))
					defaultCcyId = "None";
				else {
					if(!SwtUtil.isEmptyOrNull(request.getParameter("currencyId"))) {
						defaultCcyId = request.getParameter("currencyId");
					}else {
						defaultCcyId = SwtUtil.isEmptyOrNull(profileLastSelectedCcy)?"None":profileLastSelectedCcy;;
					}
				}
			}


			// Instantiate the ccyList
			ccyList = new ArrayList<LabelValueBean>();
			/*// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));*/
			// get the collection of currency access
			ccyAccessColl = new ArrayList<LabelValueBean>();
			if("None".equals(defaultEntityId)) {
				for (Entry<String, ArrayList<String>> entry : currencyDistinctList.entrySet()) {
					String currency = entry.getKey();
					ccyAccessColl.add(new LabelValueBean(currency, currency));
				}
			}else {
				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));

			}

			ccyList.add(new LabelValueBean(SwtConstants.FACILITY_NONE, SwtConstants.FACILITY_NONE));
			if (collUserEntityAccess != null)
				ccyList.addAll(ccyAccessColl);

			boolean entityExistInList = false;

			for (LabelValueBean o : ccyList){
				if(o.getValue().equals(defaultCcyId))
					entityExistInList = true;
			}
			if(!entityExistInList){
				if(ccyList != null && ccyList.size()>0 && entityChanged) {
					Iterator iter = ccyList.iterator();
					LabelValueBean firstElement =(LabelValueBean) iter.next();
					LabelValueBean secondElement =(LabelValueBean) iter.next();
					defaultCcyId = secondElement.getValue();

				} else {
					defaultCcyId = SwtConstants.FACILITY_NONE;
				}

			}

			/****CurrencyCombo***********/
			Iterator j = ccyList.iterator();

			LabelValueBean row = null;
//			lstOptions.add( new OptionInfo(SwtConstants.ALL_LABEL,
//					SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(defaultCcyId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));

			}

			lstSelect.add(new SelectInfo(PCMConstant.CURRENCYLIST, lstOptions));

			/****entity Combo***********/
			lstOptions = new ArrayList<OptionInfo>();


			j = entitiesLblValue.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(row.getValue() != null && row.getValue().equals(defaultEntityId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(),row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo(PCMConstant.ENTITYLIST, lstOptions));



			responseConstructor.formSelect(lstSelect);








//			String currentEntity = ((CommonDataManager) request.getSession()
//					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
//					.getCurrentEntity();

			// Set currentEntityFound boolean to true if the current entity exists in the collUserEntityAccess list




			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			userId = SwtUtil.getCurrentUserId(request.getSession());
			liquidityScreenOption = new ScreenOption();
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName( SwtConstants.PROPNAME_ILM_OPTIONS );
			Collection<ScreenOption>  options =   screenOptionManager.getScreenOption(liquidityScreenOption);
			if (options != null && options.size() > 0) {
				iterator = options.iterator();
				while (iterator.hasNext()) {
					scrOption = iterator.next();
					configXml= scrOption.getPropertyValue();
					break;

				}
			}
			if(!SwtUtil.isEmptyOrNull(configXml)) {
				isSaveScreenOption = false;
				if(configXml.indexOf("<Summary>") != -1) {
					summaryAsString = configXml.substring(configXml.indexOf("<Summary>")+9, configXml.indexOf("</Summary>"));
					summaryList=  summaryAsString.split(",");
				}

				if(configXml.indexOf("<ILMTabEntity>") != -1) {
					ilmTabAsString = configXml.substring(configXml.indexOf("<ILMTabEntity>")+14, configXml.indexOf("</ILMTabEntity>"));
					ilmTabEntityList= ilmTabAsString.split(",");


				}
				if(configXml.indexOf("<ILMTabAcctGrp>") != -1) {
					ilmTabAcctGrpAsString = configXml.substring(configXml.indexOf("<ILMTabAcctGrp>")+15, configXml.indexOf("</ILMTabAcctGrp>"));
					ilmTabAcctGrpList=  ilmTabAcctGrpAsString.split(",");

				}
				if(configXml.indexOf("<GroupAnalysis>") != -1) {
					groupAnalysisAsString = configXml.substring(configXml.indexOf("<GroupAnalysis>")+15, configXml.indexOf("</GroupAnalysis>"));
					groupAnalysisList= groupAnalysisAsString.split(",");
				}
				if(configXml.indexOf("<CombinedView>") != -1) {
					combinedViewAsString = configXml.substring(configXml.indexOf("<CombinedView>")+14, configXml.indexOf("</CombinedView>"));
					combinedViewList=  combinedViewAsString.split(",");

				}
				if(configXml.indexOf("<GlobalView>") != -1) {
					globalViewAsString= configXml.substring(configXml.indexOf("<GlobalView>")+12, configXml.indexOf("</GlobalView>"));
					globalViewList=  globalViewAsString.split(",");

				}
				if(configXml.indexOf("<TabDate>") != -1) {
					ilmDateAsString = configXml.substring(configXml.indexOf("<TabDate>")+9, configXml.indexOf("</TabDate>"));
					ilmDateList=  ilmDateAsString.split(",");
				}


//		    	 	<orderCcy>CHF=1,EUR=2</orderCcy>
//		    	 	<orderEntity>CHF&&RABONL2U=1</orderEntity>

				if(configXml.indexOf("<orderCcy>") != -1) {
					orderCcyString = configXml.substring(configXml.indexOf("<orderCcy>")+10, configXml.indexOf("</orderCcy>"));
					orderCcyList = orderCcyString.split(",");
					for (int i = 0; i < orderCcyList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderCcyList[i])) && orderCcyList[i].indexOf('=') > -1){
							orderMap.put(orderCcyList[i].split("=")[0], Integer.parseInt(orderCcyList[i].split("=")[1]));
						}

					}
				}
				if(configXml.indexOf("<orderEntity>") != -1) {
					orderEntityString = configXml.substring(configXml.indexOf("<orderEntity>")+13, configXml.indexOf("</orderEntity>"));
					orderEntityList=  orderEntityString.split(",");
					for (int i = 0; i < orderEntityList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderEntityList[i])) && orderEntityList[i].indexOf('=') > -1){
							orderMap.put(orderEntityList[i].split("=")[0], Integer.parseInt(orderEntityList[i].split("=")[1]));
						}

					}

				}

			}

			String actualValue = null;
			String mapKey = null;
			boolean grpAnalysIsfound = false;
			boolean globalIsfound = false;
			boolean combinedIsfound= false;
			if(ilmTabAcctGrpList != null && ilmTabAcctGrpList.length >0) {
				for (int i = 0; i < ilmTabAcctGrpList.length; i++) {
					ccyPartValue = ilmTabAcctGrpList[i].split("&&")[0];
					entityPartValue = ilmTabAcctGrpList[i].split("&&")[1];
					accGrpPartValue = ilmTabAcctGrpList[i].split("&&")[2];
					mapKey = entityPartValue + "&&" + ccyPartValue;
					entityCcyListOfAccGrps.put(mapKey,
							entityCcyListOfAccGrps.containsKey(mapKey)
									? entityCcyListOfAccGrps.get(mapKey) + "," + accGrpPartValue
									: accGrpPartValue);

				}
			}


			StringBuilder builder = new StringBuilder();

			builder.append("<tabs>");
			String tabList = "";
			String tabId = null;
			String tabName = null;
			if(ilmTabEntityList != null && ilmTabEntityList.length> 0) {
				for (int i = 0; i < ilmTabEntityList.length; i++) {
					tabId = ilmTabEntityList[i];
					if(!SwtUtil.isEmptyOrNull(tabId)) {
//

						ccyPartValue = tabId.split("&&")[0];
						entityPartValue = tabId.split("&&")[1];
						if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultEntityId)) {
							if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultCcyId)) {
								tabName = ccyPartValue  + "/" + entityPartValue ;
							} else {
								if(!ccyPartValue.equals(defaultCcyId)) {
									continue;
								}
								tabName = entityPartValue;
							}
						} else {
							if (SwtConstants.FACILITY_NONE.equalsIgnoreCase(defaultCcyId)) {
								if(!defaultEntityId.equals(entityPartValue)) {
									continue;
								}
								tabName = ccyPartValue;
							} else {
								if(!defaultEntityId.equals(entityPartValue) || !ccyPartValue.equals(defaultCcyId) ) {
									continue;
								}

								tabName = ccyPartValue + "/" + entityPartValue;
							}
						}

						tabId = entityPartValue   + "/" + ccyPartValue;


						final String keyTab =  ccyPartValue + "&&" + entityPartValue;
						if(groupAnalysisList != null)
							grpAnalysIsfound = Arrays.stream(groupAnalysisList)
									.anyMatch(x -> x.equals(keyTab));
						if(globalViewList != null)
							globalIsfound = Arrays.stream(globalViewList)
									.anyMatch(x -> x.equals(keyTab));
						if(combinedViewList != null)
							combinedIsfound = Arrays.stream(combinedViewList)
									.anyMatch(x -> x.equals(keyTab));

//				String tabDate = Arrays.stream(ilmDateList)
//                .filter(x -> x.startsWith(keyTab))
//                .findFirst();
						String tabDate = null;
						if(ilmDateList != null && ilmDateList.length > 0) {
							tabDate  = Arrays
									.stream(ilmDateList)
									.filter(x -> x.startsWith(keyTab))
									.findFirst()
									.orElse("T");

							tabDate = tabDate.substring(tabDate.lastIndexOf("=") + 1);
						}


						Date sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(
								defaultEntityId, defaultCcyId, SwtUtil.getSystemDateFromDB());
						if(!SwtUtil.isEmptyOrNull(tabDate) && "T-1".equals(tabDate)) {

							Calendar c = Calendar.getInstance();
							c.setTime(sysDateInCcyTimeframe);
							c.add(Calendar.DATE, -1);
							sysDateInCcyTimeframe = c.getTime();

						}
						tabDate = SwtUtil.formatDate(sysDateInCcyTimeframe, SwtUtil
								.getCurrentSystemFormats(request.getSession())
								.getDateFormatValue());

						tabList += tabId + ",";
//				builder.append("<row id='" + tabId + "' name='" + tabName + "' selectedGroups='"
//						+ entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue) + "'" + " grpAnalysIsfound='"
//						+ grpAnalysIsfound + "' " + " globalIsfound='" + globalIsfound + "' " + " combinedIsfound='"
//						+ combinedIsfound + "' " + "></row>");

						int orderCcy = (orderMap.containsKey(ccyPartValue)?orderMap.get(ccyPartValue):999) + 1000;
						int  orderEntity= (orderMap.containsKey(ccyPartValue+"&&"+entityPartValue)?orderMap.get(ccyPartValue+"&&"+entityPartValue):999) + 1000;
						String order =  orderCcy +""+orderEntity;
						String tabSelectedGroups = SwtUtil.isEmptyOrNull(entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue))?"":entityCcyListOfAccGrps.get(entityPartValue + "&&" + ccyPartValue);
						builder.append("<row> <id><![CDATA[" + tabId + "]]></id> <name><![CDATA[" + tabName
									   + "]]></name> <selectedGroups><![CDATA["
									   + tabSelectedGroups+ "]]></selectedGroups>"
									   + " <grpAnalysIsfound>" + grpAnalysIsfound + "</grpAnalysIsfound> " + " <globalIsfound>"
									   + globalIsfound + "</globalIsfound> " + " <combinedIsfound>" + combinedIsfound
									   + "</combinedIsfound>  <selectedDate><![CDATA["+tabDate +"]]>  </selectedDate> <tabOrder>" + order+ "</tabOrder>   </row>");
					}
				}
			}

			if (tabList.length() > 0) {
				tabList = tabList.substring(0, tabList.length() - 1);
			}
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement("tabList", tabList);

			builder.append("</tabs>");

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.appendText(builder.toString());

			HashMap<String, ILMSummaryRecord> result = ilmAnalysisMonitorManager.getILMSummaryGridData(hostId,summaryAsString,valueDate,null,includeSOD,includeCR,includeOpenMovements,includeSumyByCutoff,hideNonSumAcct,applyCcyMultiplier,orderMap, currencyPattern,userId);

			tempMap = result.entrySet()
					.stream()
					.sorted(Comparator.comparingInt(e -> e.getValue().getOrder()))
					.collect(Collectors.toMap(Map.Entry::getKey,
							Map.Entry::getValue,
							(left, right) -> left,
							LinkedHashMap::new));


			width = SwtUtil.getPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
					"display", "column_width");
			String columnOrder = SwtUtil.getPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
					"display", "column_order");
			String columnHidden = SwtUtil.getPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
					"display", "column_hidden");
			// Get the selected date with Date format
			ccyList = new ArrayList<LabelValueBean>();
			/*// add the all label into currency list
			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));*/
			//Create the grid data
			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getILSummarysGridColumns(width, columnOrder, columnHidden, request));
			//Get Grid Data

			// form rows (records)
			responseConstructor.formRowsStart(result.size());
			int parentIdEntity = 0;
			int parentIdCcy = 0;
			int parentIdGroup = 0;
			int id = 0;
			int uniqueSeqNum = 0;
			// get the collection of currency access

			String confDColors = null;

			ILMSummaryRecord iLMSummaryRecord = null;
			for (Entry<String, ILMSummaryRecord> entry : tempMap.entrySet()) {
				iLMSummaryRecord  = entry.getValue();
				String ccyColor = "";
				for (Entry<String, ILMSummaryRecord> entryEntity : iLMSummaryRecord.getSubILMSummaryList().entrySet()) {
					ILMSummaryRecord entityRecord = entryEntity.getValue();
					String entityColor = "";
					for (Entry<String, ILMSummaryRecord> accountGroup : entityRecord.getSubILMSummaryList().entrySet()) {
						String groupColor = "";
						ILMSummaryRecord accountGroupRecord = accountGroup.getValue();

						if( colorServility.get(groupColor) < colorServility.get(accountGroupRecord.getPredictBalCol())) {
							groupColor = accountGroupRecord.getPredictBalCol();
						}
						if( colorServility.get(groupColor) < colorServility.get(accountGroupRecord.getExternalBalCol())) {
							groupColor = accountGroupRecord.getExternalBalCol();
						}
						if( colorServility.get(groupColor) < colorServility.get(accountGroupRecord.getMinBalColor())) {
							groupColor = accountGroupRecord.getMinBalColor();
						}

						confDColors = accountGroupRecord.getdPctThruColor();
						if(!SwtUtil.isEmptyOrNull(confDColors)) {
							if(confDColors.length()==2) {
								String colorItem = ""+confDColors.charAt(0);
								if("A".equals(colorItem) || "R".equals(colorItem)){
									if( colorServility.get(groupColor) < colorServility.get(colorItem)) {
										groupColor = colorItem;
									}
								}
								colorItem = ""+confDColors.charAt(1);
								if("A".equals(colorItem) || "R".equals(colorItem)){
									if( colorServility.get(groupColor) < colorServility.get(colorItem)) {
										groupColor = colorItem;
									}
								}
							}

						}

						accountGroupRecord.setRowColor(groupColor);

						if( colorServility.get(entityColor) < colorServility.get(groupColor)) {
							entityColor = ""+groupColor;
						}
					}
					entityRecord.setRowColor(entityColor);
					if( colorServility.get(ccyColor) < colorServility.get(entityColor)) {
						ccyColor = ""+entityColor;
					}
				}
				iLMSummaryRecord.setRowColor(ccyColor);

			}
			String ccyMultiplier = null;
//			for (Entry<String, ArrayList<String>> entry : currencyDistinctList.entrySet()) {
			for (Entry<String, ILMSummaryRecord> entry : tempMap.entrySet()) {
				String currency = entry.getKey();
				if("all".equalsIgnoreCase(currency))
					continue;
				iLMSummaryRecord  = entry.getValue();
				ccyMultiplier = !SwtUtil.isEmptyOrNull(iLMSummaryRecord.getCurrencyMultiplierLabel())?"("+iLMSummaryRecord.getCurrencyMultiplierLabel()+")":"";
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ALERTING, iLMSummaryRecord.getScenarioHighlighted());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, currency + ccyMultiplier);
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ROW_COLOR_TAGNAME, colorList.get(iLMSummaryRecord.getRowColor()));
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME              , "");
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                    , iLMSummaryRecord.getCutOff());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                 , iLMSummaryRecord.getPredictedBalance());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                  , iLMSummaryRecord.getExternalBal());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                  , iLMSummaryRecord.getTurnover());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                 , iLMSummaryRecord.getAvailable());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                 , iLMSummaryRecord.getUnsettledBalance());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                , iLMSummaryRecord.getUnexpectedBalance());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                 , iLMSummaryRecord.getPreadvices());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                     , !SwtUtil.isEmptyOrNull(iLMSummaryRecord.getDpctThru())?iLMSummaryRecord.getDpctThru()+"%":"");
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                       , "");
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                , iLMSummaryRecord.getActualSod());
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                   , "");
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ORDER_TAGNAME, (iLMSummaryRecord.getOrder() == null?"":""+iLMSummaryRecord.getOrder()));
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                 , "");

				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME+"Color", 				colorList.get(iLMSummaryRecord.getPredictBalCol()));
				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME+"Color"                  , colorList.get(iLMSummaryRecord.getExternalBalCol()));
//				responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME+"Color"                , colorList.get(iLMSummaryRecord.getSodBalCol()));


				responseConstructor.createRowElement("parentId", null);
				responseConstructor.createRowElement("indent", 0);
				responseConstructor.createRowElement("__collapsed", ""+true);
				responseConstructor.createRowElement("seqN", ++uniqueSeqNum);
				responseConstructor.formRowEnd();
				parentIdEntity = id;
				id++;
//				ArrayList<String> entityList = entry.getValue();

				Map<String, ILMSummaryRecord> entityMap = iLMSummaryRecord.getSubILMSummaryList().entrySet()
						.stream()
						.sorted(Comparator.comparingInt(e -> e.getValue().getOrder()))
						.collect(Collectors.toMap(Map.Entry::getKey,
								Map.Entry::getValue,
								(left, right) -> left,
								LinkedHashMap::new));


//				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(e.getValue(), roleId));
				for (Entry<String, ILMSummaryRecord> entryEntity : entityMap.entrySet()) {
//					accountGroupList = getAccountGroupsList(request,hostId,entity, currency,false);
					String entity = entryEntity.getKey();
//					accountGroupList = getAccountGroupsList(request,hostId,entity, currency,false);
					ILMSummaryRecord entityRecord = entryEntity.getValue();
					responseConstructor.formRowStart();
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ALERTING, entityRecord.getScenarioHighlighted());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, entity);
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ROW_COLOR_TAGNAME, colorList.get(entityRecord.getRowColor()));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME              , "");
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                    , entityRecord.getCutOff());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCY_TAGNAME                  , currency);
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ENTITY_TAGNAME                    , entity);
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_GROUP_ID_TAGNAME                  , entityRecord.getIlmGlobalAccountGroupId());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                 , entityRecord.getPredictedBalance());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                  , entityRecord.getExternalBal());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                  , entityRecord.getTurnover());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                 , entityRecord.getAvailable());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                 , entityRecord.getUnsettledBalance());;
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                , entityRecord.getUnexpectedBalance());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                 , entityRecord.getPreadvices());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                     , !SwtUtil.isEmptyOrNull(entityRecord.getDpctThru())?entityRecord.getDpctThru()+"%":"", false, true);
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                       , !SwtUtil.isEmptyOrNull(entityRecord.getPctConfCr())?entityRecord.getPctConfCr()+"%":"", false, false);
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                , entityRecord.getActualSod());
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                   , entityRecord.getMinbal() +"  "+ (SwtUtil.isEmptyOrNull(entityRecord.getMinBalTime())? "" :SwtUtil.formatDate(entityRecord.getMinBalTime(),SwtUtil.getCurrentDateFormat(session).replace("/yyyy" , "")+" HH:mm")));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ORDER_TAGNAME, (entityRecord.getOrder() == null?"":""+entityRecord.getOrder()));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                 , "");

					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME+"Color", 				colorList.get(entityRecord.getPredictBalCol()));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME+"Color"                  , colorList.get(entityRecord.getExternalBalCol()));
//					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME+"Color"                , colorList.get(entityRecord.getSodBalCol()));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME+"Color"                     , getDPctThruColor(entityRecord.getdPctThruColor()));
					responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME+"Color"                     , colorList.get(entityRecord.getMinBalColor()));

					responseConstructor.createRowElement("parentId", parentIdEntity);
					responseConstructor.createRowElement("indent", 1);
					responseConstructor.createRowElement("__collapsed", ""+true);
					responseConstructor.createRowElement("seqN", ++uniqueSeqNum);
					parentIdCcy = id;
					id++;
					responseConstructor.formRowEnd();

					/****************dataGrid******************/

					Map<String, ILMSummaryRecord> accountGroupMap  = new TreeMap<String, ILMSummaryRecord>();
					if(entityRecord.getSubILMSummaryList().size() > 0) {
						accountGroupMap = entityRecord.getSubILMSummaryList().entrySet()
								.stream()
								.sorted(Comparator.comparingInt(e -> e.getValue().getOrder()))
								.collect(Collectors.toMap(Map.Entry::getKey,
										Map.Entry::getValue,
										(left, right) -> left,
										LinkedHashMap::new));
					}


//					for (int i = 0; i < accountGroupList.size(); i++) {
					for (Entry<String, ILMSummaryRecord> groupEntity : accountGroupMap.entrySet()) {
						String groupId = groupEntity.getKey();
//						accountGroupList = getAccountGroupsList(request,hostId,entity, currency,false);
						ILMSummaryRecord groupRecord = groupEntity.getValue();
						responseConstructor.formRowStart();
//						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, accountGroupList.get(i).getValue());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ALERTING, groupRecord.getScenarioHighlighted());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, groupId);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME              , "");
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                    , "");
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCY_TAGNAME                  , currency);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ENTITY_TAGNAME                  , entity);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_GROUP_ID_TAGNAME                  , groupId);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ROW_COLOR_TAGNAME, colorList.get(groupRecord.getRowColor()));
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                 , groupRecord.getPredictedBalance());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                  , groupRecord.getExternalBal());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                  , groupRecord.getTurnover());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                 , groupRecord.getAvailable());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                 , groupRecord.getUnsettledBalance());;
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                , groupRecord.getUnexpectedBalance());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                 , groupRecord.getPreadvices());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                     , !SwtUtil.isEmptyOrNull(groupRecord.getDpctThru())?groupRecord.getDpctThru()+"%":"", false, true);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                       , !SwtUtil.isEmptyOrNull(groupRecord.getPctConfCr())?groupRecord.getPctConfCr()+"%":"", false, false);
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                , groupRecord.getActualSod());
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                   , groupRecord.getMinbal() +"  "+(SwtUtil.isEmptyOrNull(groupRecord.getMinBalTime())? "" :SwtUtil.formatDate(groupRecord.getMinBalTime(),SwtUtil.getCurrentDateFormat(session).replace("/yyyy" , "") +" HH:mm"))) ;
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ORDER_TAGNAME, "");
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                 , "");


						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME+"Color", 				colorList.get(groupRecord.getPredictBalCol()));
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME+"Color"                  , colorList.get(groupRecord.getExternalBalCol()));
//						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME+"Color"                , colorList.get(groupRecord.getSodBalCol()));
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME+"Color"                     , getDPctThruColor(groupRecord.getdPctThruColor()));
						responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME+"Color"                     , colorList.get(groupRecord.getMinBalColor()));


						responseConstructor.createRowElement("parentId", parentIdCcy);
						responseConstructor.createRowElement("indent", 2);
						responseConstructor.createRowElement("__collapsed", ""+true);
						responseConstructor.createRowElement("seqN", ++uniqueSeqNum);

						responseConstructor.formRowEnd();
						parentIdGroup = id;
						id++;
//						ILMGeneralMaintenanceManager ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
//						List<LabelValueBean> accountList = ilmGenMgr
//								.getAccountInGroupListSQL(hostId, entity,currency, accountGroupList.get(i).getValue());


						Map<String, ILMSummaryRecord> accountMap  = new TreeMap<String, ILMSummaryRecord>();
						if(groupRecord.getSubILMSummaryList().size() > 0) {
							accountMap = groupRecord.getSubILMSummaryList().entrySet()
									.stream()
									.sorted(Comparator.comparingInt(e -> e.getValue().getOrder()))
									.collect(Collectors.toMap(Map.Entry::getKey,
											Map.Entry::getValue,
											(left, right) -> left,
											LinkedHashMap::new));
						}


//						for(LabelValueBean accnt : accountList) {
						for (Entry<String, ILMSummaryRecord> accountEntity : accountMap.entrySet()) {
							String accountId = accountEntity.getKey();
//							accountGroupList = getAccountGroupsList(request,hostId,entity, currency,false);
							ILMSummaryRecord accountRecord = accountEntity.getValue();

							responseConstructor.formRowStart();
//							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, accnt.getLabel());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, accountId);
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME              , accountRecord.getAccountName());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                    , "");
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCY_TAGNAME                  , currency);
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ENTITY_TAGNAME                  , entity);
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_GROUP_ID_TAGNAME                  , groupId);
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_EXCEEDED_TAGNAME           ,""+accountRecord.isCutOffExceeded());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                 , accountRecord.getPredictedBalance());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                  , accountRecord.getExternalBal());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                  , accountRecord.getTurnover());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                 , accountRecord.getAvailable());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                 , accountRecord.getUnsettledBalance());;
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                , accountRecord.getUnexpectedBalance());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                 , accountRecord.getPreadvices());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                 , accountRecord.getSum());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                     , "");
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                       , "");
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                , accountRecord.getActualSod());
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                   , "");
							responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ORDER_TAGNAME, "");


							responseConstructor.createRowElement("parentId", parentIdGroup);
							responseConstructor.createRowElement("indent", 3);
							responseConstructor.createRowElement("seqN", ++uniqueSeqNum);
							responseConstructor.createRowElement("__collapsed", ""+true);

							responseConstructor.formRowEnd();
							id++;

						}
					}


				}

			}
			responseConstructor.formRowsEnd();


			String domesticEntity = SwtUtil.getUserCurrentEntity(session);
			String domesticCcy = null;
			if(domesticEntity != null)
				domesticCcy = SwtUtil.getDomesticCurrencyForUser(request, hostId, domesticEntity);

			iLMSummaryRecord  = tempMap.get("All");
			if(iLMSummaryRecord == null)
				iLMSummaryRecord = new ILMSummaryRecord();



			responseConstructor.formTotalsStart(1);
			responseConstructor.formTotalStart();
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME, "Total "+domesticCcy);
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME              , "");
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                    , iLMSummaryRecord.getCutOff());
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                 , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getPredictedBalance()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                  , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getExternalBal()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                  , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getTurnover()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                 , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getAvailable()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                 , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getUnsettledBalance()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getUnexpectedBalance()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                 , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getPreadvices()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                     , "");
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                       , "");
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                , SwtUtil.formatCurrency(domesticCcy, new BigDecimal(iLMSummaryRecord.getActualSod()), currencyPattern));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                   , "");
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_ORDER_TAGNAME, "");

			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME+"Color", 				colorList.get(iLMSummaryRecord.getPredictBalCol()));
			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME+"Color"                  , colorList.get(iLMSummaryRecord.getExternalBalCol()));
//			responseConstructor.createRowElement(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME+"Color"                , colorList.get(iLMSummaryRecord.getSodBalCol()));
			responseConstructor.formTotalEnd();
			responseConstructor.formTotalsEnd();



			responseConstructor.formGridEnd();




			xmlWriter.endElement(componentId);

			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(
					this.getClass().getName() + " - Exception Catched in [ilmSummaryGridDisplay] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception ex) {
			ex.printStackTrace();
			// Build error message
			errorMessage = ex.getStackTrace()[0].getClassName() + "." + ex.getStackTrace()[0].getMethodName() + ":"
						   + ex.getStackTrace()[0].getLineNumber() + " " + ex.getMessage();

			// log error message
			log.error(this.getClass().getName() + " - [ilmSummaryGridDisplay] - SwtException -" + errorMessage);

		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [ilmSummaryGridDisplay] - Exit");

		}

		return null;
	}

	private String getDPctThruColor(String color) {
		if(!SwtUtil.isEmptyOrNull(color)) {
			if(color.length()==2) {
				return (colorList.get(""+color.charAt(0))+"|"+colorList.get(""+color.charAt(1))).replace("!important", "");
			}else {
				return colorList.get(color);
			}
		}else {
			return getView("");
		}
	}


	private static HashMap<String, String> colorList = new HashMap<String, String>() ;
	private static HashMap<String, Integer> colorServility  = new HashMap<String, Integer>() ;
	static {
		colorList.put("A", "#FFFFB4 !important");
		colorList.put("R", "#F79391 !important");
		colorList.put("G", "#aadd88 !important");
		colorList.put("W", "grey !important");
		colorList.put("N", "transparent !important");


		colorServility.put(null, 0);
		colorServility.put("", 0);
		colorServility.put("N", 1);
		colorServility.put("G", 2);
		colorServility.put("A", 3);
		colorServility.put("R", 4);
	}


	private String generateRandom (HttpSession session, String type) throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		boolean isTime = false;
		boolean isAmount = false;
		boolean isColor = false;
		boolean isColorConfD = false;
		String[] colors = new String[3];

		colors[0] = "#ffbf00";
		colors[1] = "red";
		colors[2] = "";


		String[] colorsConfD = new String[3];
		colorsConfD[0] = "green";
		colorsConfD[1] = "red";
		colorsConfD[2] = "grey";

		if(type.equalsIgnoreCase("time")) {
			isTime = true;
		} else if(type.equalsIgnoreCase("amount")) {
			isAmount = true;
		} else if(type.equalsIgnoreCase("color")) {
			isColor = true;
		} else if(type.equalsIgnoreCase("colorConfD")) {
			isColorConfD = true;
		}
		if(isTime) {
			int randomNum = ThreadLocalRandom.current().nextInt(10, 10000 + 1);
			Random generator = new Random(randomNum);
			LocalTime time = LocalTime.MIN.plusSeconds(generator.nextLong());
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

			return time.format(formatter);
		}else if(isAmount) {

			DecimalFormat formatter = new DecimalFormat("#0.00");  // edited here.
			double randomValue = 10 + Math.random( ) * 10000000;
			double tempRes = Math.floor(randomValue * 10);
			BigDecimal finalRes = new BigDecimal( tempRes/10);

			return SwtUtil.formatCurrency("EUR", finalRes);
		}else if(isColor) {
			int randomColor = ThreadLocalRandom.current().nextInt(0, 2 + 1);
			return colors[randomColor];
		}else if(isColorConfD) {
			int randomColor1 = ThreadLocalRandom.current().nextInt(0, 2 + 1);
			int randomColor2 = ThreadLocalRandom.current().nextInt(0, 2 + 1);
			return colorsConfD[randomColor1]+"|"+colorsConfD[randomColor2];
		}else {
			return null;
		}
	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<org.swallow.pcm.maintenance.model.core.ColumnInfo> getThrouputMonitorGridColumns(String width, String columnOrder, String hiddenColumns,
																								  HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<org.swallow.pcm.maintenance.model.core.ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getThrouputMonitorGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME   + "=15,"
						+ SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME + "=250,"
						+ SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME + "=80" + ","
						+ SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME + "=200,"
						+ SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME + "=165,"
						+ SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME + "=165,"
						+ SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME + "=125";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null || empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder =  SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME  + ","
							   + SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME+","
							   + SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME + ","
							   + SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME+ ","
							   + SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME+","
							   + SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME;

			}
			orders = new ArrayList<String>();
			// Split the columns using , && save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null || empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , && save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<org.swallow.pcm.maintenance.model.core.ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				org.swallow.pcm.maintenance.model.core.ColumnInfo tmpColumnInfo = null;

				//ALERTING
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME )) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage("",
									request),
							SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(
									widths.get(SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME )),
							false, false,
							hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ALERTING_TAGNAME ));
					tmpColumnInfo.setHeaderTooltip(SwtUtil
							.getMessage("", request));
					tmpColumnInfo.setColumnGroup("");
					lstColumns.add(tmpColumnInfo);
				}
				// RULE
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ENTITY_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ENTITY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// RULE
				if (order.equals(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CCY_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CCY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Instant Release column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_INF_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 3,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_INF_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Forecasted");
					lstColumns.add(tmpColumnInfo);

				}
				// Include in Target column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 4,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Forecasted");
					lstColumns.add(tmpColumnInfo);
				}


				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME)) {
					tmpColumnInfo =new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_INF_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 5,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_INF_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Actual");
					lstColumns.add(tmpColumnInfo);
				}


				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 6,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Actual");
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME)) {
					tmpColumnInfo =new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 7,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 8,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_THRESHOLD_1_TOOLTIP, request));
					tmpColumnInfo.setSort(false);
					tmpColumnInfo.setColumnGroup("Throughput");
					lstColumns.add(tmpColumnInfo);


				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 9,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_THRESHOLD_2_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Throughput");
					tmpColumnInfo.setSort(false);
					lstColumns.add(tmpColumnInfo);
				}


				// Category ID type column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)) {
					tmpColumnInfo = (new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CURRENT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 10,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CURRENT_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Throughput");
					tmpColumnInfo.setSort(false);
					lstColumns.add(tmpColumnInfo);
				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getThrouputMonitorGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getThrouputMonitorGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getThrouputMonitorGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<org.swallow.pcm.maintenance.model.core.ColumnInfo> getILMOptionsGridColumns(String width, String columnOrder, String hiddenColumns,
																							 HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<org.swallow.pcm.maintenance.model.core.ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getILMOptionsGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME + "=250,"
						+ SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME + "=80" + ","
						+ SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME + "=200,"
						+ SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME + "=150,"
						+ SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME + "=110";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null || empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME+","
							  + SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME + ","
							  + SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME;

			}
			orders = new ArrayList<String>();
			// Split the columns using , && save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null || empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , && save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<org.swallow.pcm.maintenance.model.core.ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				org.swallow.pcm.maintenance.model.core.ColumnInfo tmpColumnInfo = null;

				// RULE
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ENTITY_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME, SwtConstants.COLUMN_TYPE_TREE, 0,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ENTITY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ENTITY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// RULE
				if (order.equals(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CCY_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_CCY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CCY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Instant Release column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ILM_GROUP_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_INF_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 3,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_FORC_INF_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_INF_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Forecasted");
					lstColumns.add(tmpColumnInfo);

				}
				// Include in Target column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 4,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_FORC_OUT_TOOLTIP, request));
					tmpColumnInfo.setColumnGroup("Forecasted");
					lstColumns.add(tmpColumnInfo);
				}


				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME)) {
					tmpColumnInfo =new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_INF_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 5,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ACT_INF_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_INF_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}


				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 6,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_ACT_OUT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME)) {
					tmpColumnInfo =new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 7,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_UNSET_OUT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}


				// Category ID type column
				if (order.equals(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)) {
					tmpColumnInfo = (new org.swallow.pcm.maintenance.model.core.ColumnInfo(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CURRENT_HEADING, request),
							SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
							Integer.parseInt(widths.get(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.THROUPUT_MONITOR_CURRENT_TAGNAME)));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.THROUPUT_MONITOR_CURRENT_TOOLTIP, request));
					tmpColumnInfo.setSort(false);
					lstColumns.add(tmpColumnInfo);
				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getILMOptionsGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getILMOptionsGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getILMOptionsGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	public static String[] differences(String[] first, String[] second) {
		String[] sortedFirst = Arrays.copyOf(first, first.length); // O(n)
		String[] sortedSecond = Arrays.copyOf(second, second.length); // O(m)
		Arrays.sort(sortedFirst); // O(n log n)
		Arrays.sort(sortedSecond); // O(m log m)

		int firstIndex = 0;
		int secondIndex = 0;

		LinkedList<String> diffs = new LinkedList<String>();

		while (firstIndex < sortedFirst.length && secondIndex < sortedSecond.length) { // O(n + m)
			int compare = (int) Math.signum(sortedFirst[firstIndex].compareTo(sortedSecond[secondIndex]));

			switch(compare) {
				case -1:
					diffs.add(sortedFirst[firstIndex]);
					firstIndex++;
					break;
				case 1:
					diffs.add(sortedSecond[secondIndex]);
					secondIndex++;
					break;
				default:
					firstIndex++;
					secondIndex++;
			}
		}

		if(firstIndex < sortedFirst.length) {
			append(diffs, sortedFirst, firstIndex);
		} else if (secondIndex < sortedSecond.length) {
			append(diffs, sortedSecond, secondIndex);
		}

		String[] strDups = new String[diffs.size()];

		return diffs.toArray(strDups);
	}

	private static void append(LinkedList<String> diffs, String[] sortedArray, int index) {
		while(index < sortedArray.length) {
			diffs.add(sortedArray[index]);
			index++;
		}
	}


	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<org.swallow.pcm.maintenance.model.core.ColumnInfo> getILSummarysGridColumns(String width, String columnOrder, String hiddenColumns,
																							 HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<org.swallow.pcm.maintenance.model.core.ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getILSummarysGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.ILM_SUMMARY_GRID_ALERTING   + "=50,"
						+ SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME + "=280,"
						+ SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME                 + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                          + "=50,"
						+ SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                       + "=80,"
						+ SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                    + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                     + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                     + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                    + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                    + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                   + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                    + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                        + "=80,"
						+ SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                          + "=130,"
						+ SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                   + "=150,"
						+ SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                      + "=230";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			String orginalOrder =  SwtConstants.ILM_SUMMARY_GRID_ALERTING   + ","
								   + SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME   + ","
								   + SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME                 + ","
								   + SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                       + ","
								   + SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                       + ","
								   + SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                    + ","
								   + SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                     + ","
								   + SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                     + ","
								   + SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                    + ","
								   + SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                    + ","
								   + SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                   + ","
								   + SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                    + ","
								   + SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                        + ","
								   + SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                          + ","
								   + SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                   + ","
								   + SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                      ;

			// Condition to check column order is null || empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.ILM_SUMMARY_GRID_ALERTING   + ","
							  +  SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME   + ","
							  + SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME                 + ","
							  + SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME                       + ","
							  + SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME                       + ","
							  + SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME                    + ","
							  + SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME                     + ","
							  + SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME                     + ","
							  + SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME                    + ","
							  + SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME                    + ","
							  + SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME                   + ","
							  + SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME                    + ","
							  + SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME                        + ","
							  + SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME                          + ","
							  + SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME                   + ","
							  + SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME                      ;

			}
			if(SwtUtil.isEmptyOrNull(hiddenColumns)) {
				hiddenColumns = "";
			}

			String[] allColumns = null;
			allColumns = columnOrder.split(",");
			String[] orginalOrderArray = orginalOrder.split(",");
			Arrays.sort(allColumns);
			Arrays.sort(orginalOrderArray);
			String[] difference =differences(orginalOrderArray, allColumns) ;
			if(difference.length>0) {
				for (int i = 0; i < difference.length; i++) {
					if(!"dummy".equalsIgnoreCase(difference[i]))
						columnOrder+=","+difference[i];
				}
			}
			orders = new ArrayList<String>();
			// Split the columns using , && save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null || empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , && save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<org.swallow.pcm.maintenance.model.core.ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				org.swallow.pcm.maintenance.model.core.ColumnInfo tmpColumnInfo = null;

				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_ALERTING )) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage("",
									request),
							SwtConstants.ILM_SUMMARY_GRID_ALERTING,
							SwtConstants.COLUMN_TYPE_STRING, 0, widths.get(SwtConstants.ILM_SUMMARY_GRID_ALERTING ) != null?
							Integer.parseInt(
									widths.get(SwtConstants.ILM_SUMMARY_GRID_ALERTING )):50,
							false, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_ALERTING ));
					tmpColumnInfo.setHeaderTooltip(SwtUtil
							.getMessage("", request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_HEADING,
									request),
							SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME,
							SwtConstants.COLUMN_TYPE_TREE, 1,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME) != null?
									Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME)):280,
							false, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil
							.getMessage(SwtConstants.ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_HEADING,
									request),
							SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING, 2,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME) != null ? Integer.parseInt(
									widths.get(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME)): 150,
							true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil
							.getMessage(SwtConstants.ILM_SUMMARY_GRID_ACCOUNT_NAME_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 3,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME) != null ?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME)):80, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_CUTOFF_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_SUM_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 4,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME) != null ?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME)):50, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_SUM_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_SUM_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 5,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME) != null ?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_PREDICTED_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 6,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME) != null ?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_EXTERNAL_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 7,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_TURNOVER_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 8,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_AVAILABLE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 9,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_UNSETTLED_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME) !=null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_UNEXPECTED_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 11,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_PREADVICE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_CONFD_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 12,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME)):80, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_CONFD_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_CONFD_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_INC_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 13,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME) != null ?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME)):130, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_INC_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_INC_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 14,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME)):150, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_STARTOFDAY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_MINBALT_HEADING, request),
							SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 15,
							widths.get(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME) != null?Integer.parseInt(widths.get(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME)):230, true, true,
							hiddenColumnsMap.get(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_SUMMARY_GRID_MINBALT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

			}

		} catch (Exception ex) {
			ex.printStackTrace();
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getILSummarysGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getILSummarysGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getILSummarysGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}
	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<org.swallow.pcm.maintenance.model.core.ColumnInfo> getMovementGridColumns(String width, String columnOrder, String hiddenColumns,
																						   HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<org.swallow.pcm.maintenance.model.core.ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getMovementGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME                         + "=100,"
						 + SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME                            + "=100,"
						 + SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME                          + "=100,"
						 + SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME                             + "=100,"
						 + SwtConstants.MSD_MOVEMENT_CCY_TAGNAME                              + "=100,"
						 + SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME                       + "=100,"
						 + SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME                          + "=100,"
						 + SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME                            + "=100,"
						 + SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME                   + "=100,"
						 + SwtConstants.MSD_MOVEMENT_PRED_TAGNAME                             + "=100,"
						 + SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME      + "=100,"
						 + SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME                           + "=100,"
						 + SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME                          + "=100,"
						 + SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME                           + "=100,"
						 + SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME                        + "=100,"
						 + SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME                            + "=100,"
						 + SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME                      + "=100,"
						 + SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME                       + "=100,"
						 + SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME                       + "=100,"
						 + SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME                       + "=100,"
						 + SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME                         + "=100,"
						 + SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME                        + "=100,"
						 + SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME                         + "=100,"
						 + SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME                      + "=100,"
						 + SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME                    + "=100,"
						 + SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME                      + "=100,"
						 + SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME                   + "=100";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null || empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME                         + ","
							  + SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME                            + ","
							  + SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME                          + ","
							  + SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME                             + ","
							  + SwtConstants.MSD_MOVEMENT_CCY_TAGNAME                              + ","
							  + SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME                       + ","
							  + SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME                          + ","
							  + SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME                            + ","
							  + SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME                   + ","
							  + SwtConstants.MSD_MOVEMENT_PRED_TAGNAME                             + ","
							  + SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME      + ","
							  + SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME                           + ","
							  + SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME                          + ","
							  + SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME                           + ","
							  + SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME                        + ","
							  + SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME                            + ","
							  + SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME                      + ","
							  + SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME                       + ","
							  + SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME                       + ","
							  + SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME                       + ","
							  + SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME                         + ","
							  + SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME                        + ","
							  + SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME                         + ","
							  + SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME                      + ","
							  + SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME                    + ","
							  + SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME                      + ","
							  + SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME                   ;

			}
			orders = new ArrayList<String>();
			// Split the columns using , && save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null || empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , && save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<org.swallow.pcm.maintenance.model.core.ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				org.swallow.pcm.maintenance.model.core.ColumnInfo tmpColumnInfo = null;

				if (order.equals(SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_POSITION_HEADING, request),
							SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_POSITION_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_POSITION_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_VALUE_HEADING, request),
							SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_VALUE_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_VALUE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_AMOUNT1_HEADING, request),
							SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 2,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_AMOUNT1_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_AMOUNT1_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_SIGN_HEADING, request),
							SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_SIGN_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_SIGN_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_CCY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_CCY_HEADING, request),
							SwtConstants.MSD_MOVEMENT_CCY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_CCY_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_CCY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_CCY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE1_HEADING, request),
							SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_REFERENCE1_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE1_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_ACCOUNT_HEADING, request),
							SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_ACCOUNT_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_ACCOUNT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_INPUT_HEADING, request),
							SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 7,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_INPUT_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_INPUT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_HEADING, request),
							SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 8,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_COUNTERPARTYID_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_PRED_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_PRED_HEADING, request),
							SwtConstants.MSD_MOVEMENT_PRED_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 9,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_PRED_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_PRED_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_PRED_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_HEADING, request),
							SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING, 10,
							Integer.parseInt(widths.get(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME)),
							false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_STATUS_HEADING, request),
							SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 11,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_STATUS_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_STATUS_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MATCHID_HEADING, request),
							SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 12,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_MATCHID_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MATCHID_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_SOURCE_HEADING, request),
							SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 13,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_SOURCE_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_SOURCE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MSGFORMAT_HEADING, request),
							SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 14,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_MSGFORMAT_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MSGFORMAT_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_NOTES_HEADING, request),
							SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 15,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_NOTES_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_NOTES_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_BENEFICIARY_HEADING, request),
							SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 16,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_BENEFICIARY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_BENEFICIARY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE2_HEADING, request),
							SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 17,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_REFERENCE2_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE2_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE3_HEADING, request),
							SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 18,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_REFERENCE3_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_REFERENCE3_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MOVEMENTID_HEADING, request),
							SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 19,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_MOVEMENTID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MOVEMENTID_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_BOOKCODE_HEADING, request),
							SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 20,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_BOOKCODE_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_BOOKCODE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_CUSTODIAN_HEADING, request),
							SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 21,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_CUSTODIAN_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_CUSTODIAN_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_EXTRAREF_HEADING, request),
							SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 22,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_EXTRAREF_TAGNAME));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_EXTRAREF_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_HEADING, request),
							SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 23,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME)), false, false,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_UPDATE_DATE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_HEADING, request),
							SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 24,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_MATCHINGPARTY_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_HEADING, request),
							SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 25,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_PRODUCTTYPE_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME)) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_HEADING, request),
							SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 26,
							Integer.parseInt(widths.get(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.MSD_MOVEMENT_POSTINGDATEMSD_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getMovementGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getMovementGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getMovementGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * This method is used to save the dataGrid's column width in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnWidth() {
		// Column width - comma separated value
		String columnWidth = null;
		String screenName = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Entry ");
			// If column width && entity id found in request, save the same in
			// DB (user preference comma separated value)
			screenName = request.getParameter("screenName");
			if ((columnWidth = request.getParameter("width")) != null) {
				if("ilmThroughPutMonitor".equals(screenName))
					SwtUtil.setPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
							"display", "column_width", columnWidth.substring(0, columnWidth.lastIndexOf(",")));
				else if("ilmSummaryGrid".equals(screenName))
					SwtUtil.setPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
							"display", "column_width", columnWidth.substring(0, columnWidth.lastIndexOf(",")));
			}
			/* Setting the reply_status_ok,reply_message value in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					  + "- [ saveColumnWidth() ] - " + e);
			/*
			 * Setting the reply_status_ok,reply_message value,reply_location in
			 * request object
			 */
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify objects
			columnWidth = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Exit");
		}
		/* Return Type of this Struts Action && returns to a JSP page */
		return getView("statechange");
	}
	/**
	 * This method is used to save the dataGrid's column hidden in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnHidden() {
		// Column width - comma separated value
		String screenName = null;
		String columnHidden = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Entry ");
			// If column order && entity id found in request, save the same in
			// DB (user preference comma separated value)
			screenName = request.getParameter("screenName");
			if ((columnHidden = request.getParameter("hidden")) != null) {
				if("ilmSummaryGrid".equals(screenName)) {
					SwtUtil.setPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
							"display", "column_hidden", columnHidden);
				}else {
					SwtUtil.setPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
							"display", "column_hidden", columnHidden);
				}
			}


			/* Setting the reply_status_ok,reply_message value in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
		} catch (Exception e) {
			// log error message
			log.error(this.getClass().getName()
					  + "- [ saveColumnWidth() ] - " + e);
			/*
			 * Setting the reply_status_ok,reply_message value,reply_location in
			 * request object
			 */
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify objects
			columnHidden = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnWidth ] - Exit");
		}
		/* Return Type of this Struts Action && returns to a JSP page */
		return getView("statechange");
	}


	/**
	 * This method is used to save the dataGrid column's order in the database
	 * based on the user
	 *
	 * @param ActionMapping
	 *            mapping
	 * @param ActionForm
	 *            form
	 * @param HttpServletRequest
	 *            request
	 * @param HttpServletResponse
	 *            response
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String saveColumnOrder() {
		// To hold column order (comma separated value)
		String columnOrder = null;
		// Entity id
		String entityId = null;
		String screenName = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Entry ");
			// If column order && entity id found in request, save the same in
			// DB (user preference comma separated value)
			screenName = request.getParameter("screenName");
			if ((columnOrder = request.getParameter("order")) != null) {
				if("ilmSummaryGrid".equals(screenName)) {
					SwtUtil.setPropertyValue(request, SwtConstants.ILMANALYSIS_MONITOR_ID,
							"display", "column_order", columnOrder);
				}else {
					SwtUtil.setPropertyValue(request, SwtConstants.ILM_THOUPUT_MONITOR_ID,
							"display", "column_order", columnOrder);
				}

			}
			/* Setting the reply_status_ok,reply_message in request object */
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			/*
			 * Setting the reply_status_ok,reply_message,reply_location in
			 * request object
			 */
			log.error(this.getClass().getName()
					  + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());
		} finally {
			// nullify object(s)
			columnOrder = null;
			entityId = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ saveColumnOrder ] - Exit ");
		}
		/* Return Type of this Struts Action && returns to flexstatechange.jsp */
		return getView("statechange");
	}

	public String ilmThrouputBreakDownReport() throws SwtException {

		/* Variable Declaration for filterData. */
		ArrayList<FilterDTO> filterData = null;
		/* Variable Declaration for fDTO. */
		FilterDTO fDTO = null;
		/* Variable Declaration for movementReport. */
		MovementReport movementReport = null;
		/* Variable Declaration for fileName. */
		String fileName = null;

		/* Variable Declaration for session. */
		HttpSession session = null;
		/* Variable Declaration for currentFilter. */
		String currentFilter = null;
		/* Variable Declaration for currentSort. */
		String currentSort = null;
		/* Variable Declaration for currPageStr. */
		String currPageStr = null;

		/* Variable Declaration for movementCollection. */
		ArrayList<Movement> movementCollection = null;
		/* Variable Declaration for applyCurrencyThreshold. */
		String applyCurrencyThreshold = null;

		/* Variable Declaration for hostId. */
		String hostId = null;
		String pageCount = null;
		//int maxPageSize = 0;
		// Token for download corresponding file
		String tokenForDownload = null;
		/* Variable Declaration for titleSuffix. */
		String titleSuffix = null;
		/* Variable Declaration for outofMemory. */
		String outofMemory = null;
		/* Variable Declaration for exportType. */
		String exportType = null;
		String screen = null;
		String selectedScenario = null;
		String defaultEntityId = null;
		String defaultCcyId = null;
		String selectedAccountGroup = null;
		String selectedDate = null;
		String ccyThresholdCheckbox = null;
		int currentPage = 1;
		// Totals returned from the SQL call
		HashMap<String, Object> totalMap = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [ilmThrouputBreakDownReport] - " + "Entry");
			// initialize outof memory as false
			outofMemory = "false";
			/* get the current session. */
			session = request.getSession();
			exportType = request.getParameter("exportType");
			screen = request.getParameter("screen");
			hostId = SwtUtil.getCurrentHostId();
			selectedScenario = request.getParameter("selectedScenario");
			defaultEntityId = request.getParameter("entityId");
			defaultCcyId = request.getParameter("currencyId");
			selectedAccountGroup = request.getParameter("accountGroup");
			selectedDate = request.getParameter("selectedDate");
			ccyThresholdCheckbox = request.getParameter("applyThreshold");
			currentFilter = request.getParameter("currentFilter");
			currentPage = Integer.parseInt(request.getParameter("currentPage"));
			pageCount = request.getParameter("pageCount");

			ArrayList<Movement> movList = new ArrayList<Movement>();

			String selectedDateISO = SwtUtil.formatDate(SwtUtil.parseDate(selectedDate, SwtUtil.getCurrentDateFormat(request.getSession())), "yyyy-MM-dd");
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			if (pageCount != null) {
				pageSize = pageSize
						   * Integer.parseInt(pageCount);
			}
			totalMap = getMovements(request, movList,  hostId, defaultEntityId, defaultCcyId, selectedDateISO , selectedAccountGroup, selectedScenario, ccyThresholdCheckbox, currentFilter, currentPage, pageSize);


			//maxPage = setMaxPageAttribute(totalCount, pageSize);

			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();
			/* set Entity filter Name && value in Export screen. */
			fDTO.setName(SwtUtil.getMessage("entity.id", request));
			fDTO.setValue(defaultEntityId);
			filterData.add(fDTO);

			/* set Threshold filter Name && value in Export screen. */
			fDTO = new FilterDTO();
			fDTO.setName(SwtUtil.getMessage("mvmt.applyCurrencyThreshold", request));
			fDTO.setValue(ccyThresholdCheckbox);
			filterData.add(fDTO);

			fileName = request.getParameter("screen").replaceAll(" ", "");
			// set the attribute for filterAcctType ,outofMemory
			session.setAttribute("outofMemory", outofMemory);

			try {
				titleSuffix = PropertiesFileLoader.getInstance().getPropertiesValue("windows.title.suffix");

				/* check whether titleSuffix is null && set empty string */
				if (titleSuffix == null) {
					titleSuffix = "";
				}
				if (exportType.equalsIgnoreCase("excel")) {
					// Initialise Excel report generator
					movementReport = new MovementReportExcel();
					// Generate report in Excel format
					movementReport.generateReport(MovementColumnData.getColumnData(null), filterData, movList,
							fileName);
					response.setContentType("application/vnd.ms-excel");
					response.setHeader("Content-disposition", "attachment; filename=" + fileName + titleSuffix + "_"
															  + SwtUtil.FormatCurrentDate() + ".xls");
					// send response to client
					response.getOutputStream().write(movementReport.getReport().toByteArray());
				} else if (exportType.equalsIgnoreCase("pdf")) {
					// Initialise PDF report generator
					movementReport = new MovementReportPDF();
					// Generate report in PDF format
					movementReport.generateReport(MovementColumnData.getColumnData(null), filterData, movList,
							fileName);
					response.setContentType("application/pdf");
					response.setHeader("Content-disposition", "attachment; filename=" + fileName + titleSuffix + "_"
															  + SwtUtil.FormatCurrentDate() + ".pdf");
					// send response to client
					response.getOutputStream().write(movementReport.getReport().toByteArray());
				} else if (exportType.equalsIgnoreCase("csv")) {
					// Initialise CSV report generator
					movementReport = new MovementReportCSV();
					// Generate report in CSV format
					movementReport.generateReport(MovementColumnData.getColumnData(null), filterData, movList,
							fileName);
					response.setContentType("application/vnd.ms-excel");
					response.setHeader("Content-disposition", "attachment; filename=" + fileName + titleSuffix + "_"
															  + SwtUtil.FormatCurrentDate() + ".csv");
					// send response to client
					response.getOutputStream().write(movementReport.getReport().toByteArray());
				}
			} /*catch (SwtException ex) {
				ex.printStackTrace();
				tokenForDownload = request.getParameter("tokenForDownload");
				/* null the objects created already. */
				/*CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");

				if ("true".equals(CDM.getCancelMSDExport()))
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
				else
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|ERR"));

				if (!ex.getMessage().contains("generatedException")) {
					log.error("Exception Catch in OutStandingMvmtAction.'exportMovementScreen' method : "
							+ ex.getMessage());
					SwtUtil.logException(ex, request, "");
				}
				return getView("fail");
			}*/ catch (OutOfMemoryError ex) {
				ex.printStackTrace();

				// Source scan tools may report a "HTTP response splitting" vulnerability, a
				// generic solution works by ignoring text after CRLFs is implemented XSSFilter
				// class.
				tokenForDownload = request.getParameter("tokenForDownload");
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|MEM"));

				outofMemory = "true";
				log.error(this.getClass().getName() + "- [OutOfMemoryError] - Exiting " + ex.getMessage());
				session.setAttribute("outofMemory", outofMemory);
			} catch (Exception ex) {
				ex.printStackTrace();
				/* null the objects created already. */
				CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");

				if ("true".equals(CDM.getCancelMSDExport()))
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
				else
					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|ERR"));
				if (!ex.getMessage().contains("generatedException")) {

					log.error("Exception Catch in OutStandingMvmtAction.'ilmThrouputBreakDownReport' method : "
							  + ex.getMessage());
					SwtUtil.logException(SwtErrorHandler.getInstance().handleException(ex, "exportErrors",
							ILMAnalysisMonitorAction.class), request, "");
				}
			} finally {
				// nullify stream
				if (movementReport != null)
					movementReport.clearReport();
			}
			if (outofMemory.equals("true")) {
				return flex();
			} else {
				outofMemory = "false";
				session.setAttribute("outofMemory", outofMemory);
				return null;
			}
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			tokenForDownload = request.getParameter("tokenForDownload");
			/* null the objects created already. */
			CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");

			if ("true".equals(CDM.getCancelMSDExport()))
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
			else
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|ERR"));
			if (!swtexp.getMessage().contains("generatedException")) {
				log.error(this.getClass().getName() + "- [ilmThrouputBreakDownReport] - Exception " + swtexp.getMessage());
				SwtUtil.logException(swtexp, request, "");
			}
			return getView("fail");
		} catch (Throwable exp) {
			exp.printStackTrace();
			tokenForDownload = request.getParameter("tokenForDownload");
			/* null the objects created already. */
			CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");

			if ("true".equals(CDM.getCancelMSDExport()))
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|KO"));
			else
				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|ERR"));

			log.error(this.getClass().getName() + "- [ilmThrouputBreakDownReport] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "ilmThrouputBreakDownReport",
					ILMAnalysisMonitorAction.class), request, "");
			return getView("fail");

		} finally {
			log.debug(this.getClass().getName() + " - [ilmThrouputBreakDownReport] - " + "Exit");
			/* null the objects created already. */
			CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			if (CDM != null) {
				CDM.setCancelMSDExport("false");
			}
		}

	}
	public String ilmThrouputreport() throws SwtException {

		String entityId = null;
		String currencyCode = null;
		String currencyName = null;
		String reportFromDate = null;
		String reportToDate = null;
		String entityName = null;
		String exportType = null;
		String useCcyMultiplier = null;
		String reportType = null;
		String accountGroup = null;
		String accountGroupName = null;
		String scenarioId = null;
		String scenarioName = null;
		String chartsData = null;
		String calculateAs = null;
		String singleOrMulti = null;
		JasperPrint jasperPrint = null;
		JRPdfExporter pdfexporter;
		JRXlsxExporter xlsxporter;
		JRCsvExporter csvexporter;
		String sheetName[] = new String[1];
		ServletOutputStream out;
		HashMap<String, String> chartsMap = new HashMap<String, String>();
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {

			log.debug(this.getClass().getName() + "- [ilmThrouputreport] - Entering ");

			exportType = request.getParameter("exportType");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			accountGroup = request.getParameter("accountGroup");
			scenarioId = request.getParameter("scenarioId");
			reportFromDate = request.getParameter("fromDate");
			calculateAs = request.getParameter("calculateAs");
			chartsData = request.getParameter("chartsData");
			scenarioName = request.getParameter("scenarioName");
			accountGroupName = request.getParameter("accountGroupName");
			currencyName = request.getParameter("ccyCodeName");
			entityName = request.getParameter("entityName");

			if(!SwtUtil.isEmptyOrNull(chartsData)) {
				JSONArray  chartsJSONArray = new JSONArray (chartsData);

				for (int i = 0; i < chartsJSONArray.length(); i++) {
					chartsMap.put(chartsJSONArray.getJSONObject(i).getString("id"), chartsJSONArray.getJSONObject(i).getString("chartsData"));
				}


			}
			out = response.getOutputStream();



			ThroughputRatioReport report = new ThroughputRatioReport();
			report.setReportType(reportType);
			report.setCurrencyCode(currencyCode);
			report.setCurrencyName(currencyName);
			report.setCurrencyFormat(SwtUtil.getCurrentCurrencyFormat(request.getSession()));
			report.setAccountGroup(accountGroup);
			report.setAccountGroupName(accountGroupName);
			report.setEntityId(entityId);
			report.setEntityName(entityName);
			report.setScenarioId(scenarioId);
			report.setScenarioName(scenarioName);
			report.setCalculateAs(calculateAs);
			report.setRoleId(((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId());
			report.setDbLink("");
			report.setUseCurrencyMultiplier(useCcyMultiplier);
			report.setSingleGroup("S".equals(singleOrMulti));
			if(SwtUtil.isEmptyOrNull(reportFromDate)) {
				report.setValueDate(SwtUtil.getSystemDateFromDB());
			}else {
				report.setValueDate(SwtUtil.parseDate(reportFromDate, SwtUtil.getCurrentDateFormat(request.getSession())));
			}
			if(!CSV_EXPORT.equals(exportType)) {

				report.setChartsData(chartsMap);
			}

			report.setRequest(request);

			jasperPrint = ilmAnalysisMonitorManager.getILMThroughputRatioReport(report);

			if (PDF_EXPORT.equals(exportType)) {

				/* Initializing the JRDFExporter */
				pdfexporter = new JRPdfExporter();
				/* To set the output type as PDF file */
				response.setContentType("application/pdf");
				/* To set the content as attachment */
				response.setHeader("Content-disposition",
						"attachment; filename=ILMThroughputReport-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
				/* To pass the filled report */
				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				/* Providing the output stream */
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				/* Exporting as UTF-8 */
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				/* Export Report */
				pdfexporter.exportReport();
			} else if (XLS_EXPORT.equals(exportType)) {
				xlsxporter = new JRXlsxExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition",
						"attachment; filename=ILMThroughputReport-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xlsx");

				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
//				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.FALSE);
//				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.FALSE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				/* Set the sheet name as userId */
				sheetName[0] = SwtUtil.getMessage("ilmthroughput.report.sheet1.title", request);
				xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES, sheetName);
				// Export Report to Excel
				xlsxporter.exportReport();
			} else if (CSV_EXPORT.equals(exportType)) {
				csvexporter = new JRCsvExporter();
				response.setContentType("application/csv");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".csv");

				csvexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				csvexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				csvexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);

				csvexporter.exportReport();
			}

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [ilmThrouputreport] method - " + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [ilmThrouputreport] method - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "ilmThrouputreport", TurnoverReportAction.class), request,
					"");
			return getView("fail");
		}

		log.debug(this.getClass().getName() + "- [ilmThrouputreport] - Exiting ");
		return null;
	}

	/**
	 * Method called to populate request with account group list
	 *
	 * @param request
	 * @throws SwtException
	 */
	private ArrayList<LabelValueBean> getAccountGroupsList(HttpServletRequest request,
														   String hostId, String entityId, String currencyCode,
														   boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName()
				  + " - [getAccountGroupsList] - " + "Entry");

		ArrayList accountGroupList = null;
		accountGroupList = new ArrayList<LabelValueBean>();

		/*
		 * Adding a new LabelValueBean object with the Key as 'ALL' && value as
		 * 'ALL'
		 */
		if (putAllLabel)
			accountGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

		ILMGeneralMaintenanceManager ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
		accountGroupList.addAll(ilmGenMgr
				.getAccountGroupsListSQL(hostId, entityId, currencyCode, true));


		log.debug(this.getClass().getName()
				  + " - [getAccountGroupsList] - " + "Exit");

		return accountGroupList;
	}
	/**
	 * Method called to populate request with account group list
	 *
	 * @param request
	 * @throws SwtException
	 */
	private ArrayList<LabelValueBean> getScenarioList(HttpServletRequest request,
													  String hostId, String entityId, String currencyCode,
													  boolean putAllLabel) throws SwtException {
		log.debug(this.getClass().getName()
				  + " - [getScenarioList] - " + "Entry");

		ArrayList scenarioList = null;
		scenarioList = new ArrayList<LabelValueBean>();
		ArrayList scenarios = new ArrayList<ILMScenario>();
		ILMScenario scenario = null;

		ILMTransScenarioMaintenanceManager ilmTransScenarioMaintenanceManager=(ILMTransScenarioMaintenanceManager)(SwtUtil.getBean("ilmTransScenarioMaintenanceManager"));
		/* Collect the Scenario list in scenarioMaintenanceDetailVO */
		scenarios = ilmTransScenarioMaintenanceManager
				.getILMScenarioList(hostId, entityId, currencyCode, request);

		for (int i = 0; i < scenarios.size(); i++) {
			scenario  = (ILMScenario) scenarios.get(i);
			if(SwtConstants.YES.equals(scenario.getThroughputMonitor())){
				if(SwtConstants.ALL_LABEL.equals(entityId)){
					if(!scenario.getEntityId().equals("*")) {
						continue;
					}
				}

				if(SwtConstants.ALL_LABEL.equals(currencyCode)){
					if(!scenario.getCurrencyCode().equals("*")) {
						continue;
					}
				}
				if("Y".equalsIgnoreCase(scenario.getActiveScenario())) {
					scenarioList.add(new LabelValueBean( scenario.getIlmScenarioName(), scenario.getId().getIlmScenarioId()));
				}
			}
		}


		log.debug(this.getClass().getName()
				  + " - [getScenarioList] - " + "Exit");

		return scenarioList;
	}

	/**
	 * Returns the list of existing profiles for the current user depending
	 * on the passed entity && currency id
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String deleteProfile() throws SwtException {
		/* Method's local variable && class instance declaration */
		ScreenOptionManager screenOptionManager = null;
		String noneLabel = null;
		String entityId = null;
		String currencyCode = null;
		String hostId = null;
		String userId = null;
		String selectedProfile = null;
		ArrayList<String> profileList = null;
		HashMap<String, String> profileComboData = null;
		String profileListAsString = null;
		String currentProfile = null;
		ScreenOption screenOptionToDelete = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try{
			String screenId = SwtConstants.ILMANALYSIS_MONITOR_ID;
			log.debug(this.getClass().getName() + "- [deleteProfile] - Enter");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			selectedProfile = request.getParameter("selectedProfile");
			hostId = SwtUtil.getCurrentHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());

			// Bean manager
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			ILMConfig config =  getILMProfileConfig(request, hostId, userId, entityId, currencyCode, selectedProfile);

			screenOptionToDelete = new ScreenOption();
			screenOptionToDelete.getId().setHostId(hostId);
			screenOptionToDelete.getId().setPropertyName(SwtConstants.PROPNAME_LIQUIDITY_CONFIG + entityId + "_" + currencyCode + "_" + selectedProfile);
			screenOptionToDelete.getId().setUserId(userId);
			screenOptionToDelete.getId().setScreenId(screenId);
			screenOptionToDelete.setLiquidityMonitorOption(SwtUtil.serializeToXML(config));
			screenOptionManager.deleteLiquidityMonitorProfile(screenOptionToDelete);

			// Initializing the ScreenOption instance
			ScreenOption screenOption = new ScreenOption();
			// Setting the host id
			screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
			// Setting the user id
			screenOption.getId().setUserId(SwtUtil.getCurrentUserId(request.getSession()));
			// Setting the screen id for book monitor
			screenOption.getId().setScreenId(screenId);

			// Fetching the refresh rate
			profileList = screenOptionManager.getLiquidityMonitorProfileList(screenOption, entityId, currencyCode);
			noneLabel  = SwtUtil.getMessage("ilmanalysismonitor.noneProfile", request);

			profileComboData = new LinkedHashMap<String, String>();
			profileComboData.put("<none>", noneLabel);
			for(int i = 0; i < profileList.size(); i++){
				profileComboData.put(profileList.get(i), profileList.get(i));
			}
			profileListAsString = createXMLOption(profileComboData, currentProfile);
			// set the profile list in response
			response.getWriter().print(profileListAsString);
			log.debug(this.getClass().getName() + " - [deleteProfile] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [deleteProfile] method : - "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteProfile", ILMAnalysisMonitorAction.class), request, "");
			return getView("fail");
		}
	}

	private String createXMLOption(HashMap<String, String> profileListMap, String currentProfile) {

		String result = null;



		DocumentBuilderFactory icFactory = DocumentBuilderFactory.newInstance();
		DocumentBuilder icBuilder;
		try {
			icBuilder = icFactory.newDocumentBuilder();
			Document doc = icBuilder.newDocument();
			Element mainRootElement = doc.createElement("intradayliquidity");
			Element selectsElement = doc.createElement("selects");
			Element selectElement = doc.createElement("select");
			selectElement.setAttribute("id", "profileList");


			for (String key : profileListMap.keySet()) {
				Element option = doc.createElement("option");
				option.setAttribute("value", profileListMap.get(key));
				if(key.equals(currentProfile))
					option.setAttribute("selected", "1");
				else
					option.setAttribute("selected", "0");

				selectElement.appendChild(option);
			}
			selectsElement.appendChild(selectElement);
			mainRootElement.appendChild(selectsElement);
			doc.appendChild(mainRootElement);

			result = getStringFromDocument(doc);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [createXMLOption] method : - "
					  + exp.getMessage());
		}

		return result;
	}


	public static String getStringFromDocument(Document doc) throws TransformerException {
		DOMSource domSource = new DOMSource(doc);
		StringWriter writer = new StringWriter();
		StreamResult result = new StreamResult(writer);
		TransformerFactory tf = TransformerFactory.newInstance();
		Transformer transformer = tf.newTransformer();
		transformer.transform(domSource, result);
		return writer.toString();
	}



	public String getScenarioName(ILMScenario scenario) {
		if (SwtUtil.isEmptyOrNull(scenario.getIlmScenarioName())) {
			return scenario.getId().getIlmScenarioId();
		} else {
			return scenario.getIlmScenarioName();
		}
	}

	public String getGroupName(ILMAccountGroup group) {
		if (SwtUtil.isEmptyOrNull(group.getIlmGroupName())) {
			return group.getId().getIlmGroupId();
		} else {
			return group.getIlmGroupName();
		}
	}

	/**
	 * Returns the grid option
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getOptionData() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String hostId= null;
//		Collection<ILMAccountGroup> acountList = null;
		ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
		ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		ScreenOption scrOption = null;
		Iterator<ScreenOption> iterator = null;
		String configXml = null;
		String [] summaryList = null;
		String  summaryAsString = null;
		String ilmTabAsString = null;
		String[] ilmTabEntityList = null;
		String[] ilmTabAcctGrpList = null;
		String ilmTabAcctGrpAsString = null;
		String ilmDateAsString = null;
		String[] ilmDateList = null;
		String globalViewAsString= null;
		String[] globalViewList= null;
		String groupAnalysisAsString= null;
		String[] groupAnalysisList = null;
		String combinedViewAsString= null;
		String[] combinedViewList = null;
		String orderEntityAsString= null;
		String[] orderEntityList = null;
		String orderCcyAsString= null;
		String[] orderCcyList = null;
		Boolean isSaveScreenOption = true;
		ArrayList<LabelValueBean> accountGroupList = null;
		HashMap<String, Integer> orderMap = new HashMap<String, Integer>();
		String roleId = null;
		try {
			log.debug(this.getClass().getName() + " - [ getOptionData ] - " + "Entry");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "ILMOptions";
			xmlWriter.startElement(componentId);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// Create the grid data
			ILMGeneralMaintenanceManager ilmGenMgr = (ILMGeneralMaintenanceManager) (SwtUtil
					.getBean("ilmGeneralMaintenanceManager"));
			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));

			userId = SwtUtil.getCurrentUserId(request.getSession());
			liquidityScreenOption = new ScreenOption();
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName( SwtConstants.PROPNAME_ILM_OPTIONS );
			Collection<ScreenOption>  options =   screenOptionManager.getScreenOption(liquidityScreenOption);
			if (options != null && options.size() > 0) {
				iterator = options.iterator();
				while (iterator.hasNext()) {
					scrOption = iterator.next();
					configXml= scrOption.getPropertyValue();
					break;

				}
			}
			if(!SwtUtil.isEmptyOrNull(configXml)) {
				isSaveScreenOption = false;
				if(configXml.indexOf("<Summary>") != -1) {
					summaryAsString = configXml.substring(configXml.indexOf("<Summary>")+9, configXml.indexOf("</Summary>"));
					summaryList=  summaryAsString.split(",");
				}

				if(configXml.indexOf("<ILMTabEntity>") != -1) {
					ilmTabAsString = configXml.substring(configXml.indexOf("<ILMTabEntity>")+14, configXml.indexOf("</ILMTabEntity>"));
					ilmTabEntityList= ilmTabAsString.split(",");


				}
				if(configXml.indexOf("<ILMTabAcctGrp>") != -1) {
					ilmTabAcctGrpAsString = configXml.substring(configXml.indexOf("<ILMTabAcctGrp>")+15, configXml.indexOf("</ILMTabAcctGrp>"));
					ilmTabAcctGrpList=  ilmTabAcctGrpAsString.split(",");

				}
				if(configXml.indexOf("<GroupAnalysis>") != -1) {
					groupAnalysisAsString = configXml.substring(configXml.indexOf("<GroupAnalysis>")+15, configXml.indexOf("</GroupAnalysis>"));
					groupAnalysisList= groupAnalysisAsString.split(",");
				}
				if(configXml.indexOf("<CombinedView>") != -1) {
					combinedViewAsString = configXml.substring(configXml.indexOf("<CombinedView>")+14, configXml.indexOf("</CombinedView>"));
					combinedViewList=  combinedViewAsString.split(",");

				}
				if(configXml.indexOf("<GlobalView>") != -1) {
					globalViewAsString= configXml.substring(configXml.indexOf("<GlobalView>")+12, configXml.indexOf("</GlobalView>"));
					globalViewList=  globalViewAsString.split(",");

				}
				if(configXml.indexOf("<TabDate>") != -1) {
					ilmDateAsString = configXml.substring(configXml.indexOf("<TabDate>")+9, configXml.indexOf("</TabDate>"));
					ilmDateList=  ilmDateAsString.split(",");
				}
				if(configXml.indexOf("<orderCcy>") != -1) {
					orderCcyAsString = configXml.substring(configXml.indexOf("<orderCcy>")+10, configXml.indexOf("</orderCcy>"));
					orderCcyList=  orderCcyAsString.split(",");
					for (int i = 0; i < orderCcyList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderCcyList[i])) && orderCcyList[i].indexOf('=') > -1){
							orderMap.put(orderCcyList[i].split("=")[0], Integer.parseInt(orderCcyList[i].split("=")[1]));
						}

					}
				}
				if(configXml.indexOf("<orderEntity>") != -1) {
					orderEntityAsString = configXml.substring(configXml.indexOf("<orderEntity>")+13, configXml.indexOf("</orderEntity>"));
					orderEntityList=  orderEntityAsString.split(",");
					for (int i = 0; i < orderEntityList.length; i++) {
						if(!SwtUtil.isEmptyOrNull((orderEntityList[i])) && orderEntityList[i].indexOf('=') > -1){
							orderMap.put(orderEntityList[i].split("=")[0], Integer.parseInt(orderEntityList[i].split("=")[1]));
						}

					}
				}



			}else {
				if(scrOption != null)
					isSaveScreenOption = false;
			}

			responseConstructor.formGridStart();
			responseConstructor.formPaging(null);
			responseConstructor.formColumn(getOptionGridColumns(width, columnOrder, hiddenColumns, request));

			HashMap<String, ILMSummaryRecord> result = ilmAnalysisMonitorManager.getILMOptionGridData(orderMap, roleId);

			int totalCount = result.size();
			responseConstructor.formRowsStart(totalCount);
			int parentIdEntity = 0;
			int parentIdCcy = 0;
			int id = 0;
			// get the collection of currency access

			for (Entry<String, ILMSummaryRecord> entry : result.entrySet()) {
				String key = entry.getKey();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement("ccyEntityGrp", key);

				if (orderCcyList != null && orderCcyList.length > 0) {
					Boolean exist = false;
					for (int i = 0; i < orderCcyList.length; i++) {

						if (orderCcyList[i].split("=")[0].equals(key)) {
							responseConstructor.createRowElement("order", orderCcyList[i].split("=")[1]);
							exist = true;
							break;
						}
					}
					if(!exist)
						responseConstructor.createRowElement("order", "");
				} else
					responseConstructor.createRowElement("order", "");

				if (summaryList != null && summaryList.length > 0) {
					Boolean exist = false;
					for (int i = 0; i < summaryList.length; i++) {

						if (summaryList[i].split("&&")[0].equals(key)) {
							responseConstructor.createRowElement("summary", "Y");
							exist = true;
							break;
						}
					}
					if(!exist)
						responseConstructor.createRowElement("summary", "N");
				} else
					responseConstructor.createRowElement("summary", "N");

				responseConstructor.createRowElement("global", "");
				responseConstructor.createRowElement("ilmTab", "");
				responseConstructor.createRowElement("tabDate", "");
				responseConstructor.createRowElement("groupAnalysis", "");
				responseConstructor.createRowElement("globalChart", "");
				responseConstructor.createRowElement("combinedChart", "");
				responseConstructor.createRowElement("parentId", null);
				responseConstructor.createRowElement("indent", 0);
				responseConstructor.createRowElement("__collapsed", ""+false);
				responseConstructor.formRowEnd();
				parentIdEntity = id;
				id++;
				ILMSummaryRecord ccyRecord = entry.getValue();
				HashMap<String, ILMSummaryRecord> entityList =  ccyRecord.getSubILMSummaryList();
				entityList.put("All", new ILMSummaryRecord());


				//This code is used to test tree perfermance creating many nodes
				/*Random r = new Random();
				int randomEntityGroupNumber = r.nextInt(52) + 1;
				int randomAccountGroupNumber = r.nextInt(50) + 1;
				for (int i = 0; i < randomEntityGroupNumber; i++) {
					String entityNameee = generatingRandomAlphabeticString();
					ILMSummaryRecord recorddd = new ILMSummaryRecord();
					recorddd.setCurrencyCode(key);
					recorddd.setEntityId(entityNameee);
					HashMap<String, ILMSummaryRecord> subILMSummaryListEntity = new HashMap<String, ILMSummaryRecord>();
					for (int j = 0; j < randomAccountGroupNumber; j++) {

						String groupiddd = generatingRandomAlphabeticString();
						ILMSummaryRecord accountGroupRecodddd = new ILMSummaryRecord();
						recorddd.setCurrencyCode(key);
						recorddd.setEntityId(entityNameee);
						recorddd.setIlmGroupId(groupiddd);
						subILMSummaryListEntity.put(groupiddd, recorddd);
					}
					recorddd.setSubILMSummaryList(subILMSummaryListEntity);
					entityList.put(entityNameee,recorddd );
				}
				*/



//				ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(e.getValue(), roleId));
				for (Entry<String, ILMSummaryRecord> entryEntity : entityList.entrySet()) {
					String entityId = entryEntity.getKey();
					ILMSummaryRecord entityRecord = entryEntity.getValue();
					HashMap<String, String> accountGroupsTypeMap = new HashMap<String, String>();



					if("All".equalsIgnoreCase(entityId)) {
						accountGroupList = new ArrayList<LabelValueBean>();
						accountGroupList.add(new LabelValueBean("Global Groups","Global Groups"));
					}else {
						HashMap<String, ILMSummaryRecord> accountGroupMap =  entityRecord.getSubILMSummaryList();
						accountGroupList = new ArrayList<LabelValueBean>();
						for (Entry<String, ILMSummaryRecord> entryAccountGroup : accountGroupMap.entrySet()) {
							String accountGroup = entryAccountGroup.getKey();
							accountGroupList.add(new LabelValueBean(entryAccountGroup.getValue().getIlmAccountGroupName(), accountGroup ));
							accountGroupsTypeMap.put(accountGroup, entryAccountGroup.getValue().getIlmGlobalAccountGroupId());
						}
//						accountGroupList = getAccountGroupsList(request,hostId,e, key,false);

					}
					String ccyEntityName = null;
					ccyEntityName = key +"&&"+ entityId;
					responseConstructor.formRowStart();
					responseConstructor.createRowElement("ccyEntityGrp", entityId);

					if (summaryList != null && summaryList.length > 0) {
						boolean exist = false;
						for (int i = 0; i < summaryList.length; i++) {
							if (!SwtUtil.isEmptyOrNull(summaryList[i]) && summaryList[i].split("&&")[1].equals(entityId) && summaryList[i].split("&&")[0].equals(key)) {
								responseConstructor.createRowElement("summary", "Y");
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("summary", "N");
					} else
						responseConstructor.createRowElement("summary", "N");
					if (orderEntityList != null && orderEntityList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < orderEntityList.length; i++) {

							if (orderEntityList[i].split("=")[0].equals(ccyEntityName)) {
								responseConstructor.createRowElement("order", orderEntityList[i].split("=")[1]);
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("order", "");
					} else
						responseConstructor.createRowElement("order", "");


					if (ilmTabEntityList != null && ilmTabEntityList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < ilmTabEntityList.length; i++) {

							if (ilmTabEntityList[i].equals(ccyEntityName)) {
								responseConstructor.createRowElement("ilmTab", "Y");
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("ilmTab", "N");
					} else
						responseConstructor.createRowElement("ilmTab", "N");

					if (groupAnalysisList != null && groupAnalysisList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < groupAnalysisList.length; i++) {

							if (groupAnalysisList[i].equals(ccyEntityName)) {
								responseConstructor.createRowElement("groupAnalysis", "Y");
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("groupAnalysis", "N");
					} else
						responseConstructor.createRowElement("groupAnalysis", "N");

					if (globalViewList != null && globalViewList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < globalViewList.length; i++) {

							if (globalViewList[i].equals(ccyEntityName)) {
								responseConstructor.createRowElement("globalChart", "Y");
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("globalChart", "N");
					} else
						responseConstructor.createRowElement("globalChart", "N");

					if (combinedViewList != null && combinedViewList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < combinedViewList.length; i++) {

							if (combinedViewList[i].equals(ccyEntityName)) {
								responseConstructor.createRowElement("combinedChart", "Y");
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("combinedChart", "N");
					} else
						responseConstructor.createRowElement("combinedChart", "N");
					if (ilmDateList != null && ilmDateList.length > 0) {
						Boolean exist = false;
						for (int i = 0; i < ilmDateList.length; i++) {

							if (ilmDateList[i].split("=")[0].equals(ccyEntityName)) {
								responseConstructor.createRowElement("tabDate", ilmDateList[i].split("=")[1]);
								exist = true;
								break;
							}
						}
						if(!exist)
							responseConstructor.createRowElement("tabDate", "T");
					} else
						responseConstructor.createRowElement("tabDate", "T");
					responseConstructor.createRowElement("global", "");
					responseConstructor.createRowElement("currencyParent", key);
					responseConstructor.createRowElement("parentId", parentIdEntity);
					responseConstructor.createRowElement("indent", 1);
					responseConstructor.createRowElement("__collapsed", ""+false);
					parentIdCcy = id;
					id++;
					responseConstructor.formRowEnd();

					/****************dataGrid******************/

					for (int i = 0; i < accountGroupList.size(); i++) {
						responseConstructor.formRowStart();
						responseConstructor.createRowElement("ccyEntityGrp", accountGroupList.get(i).getValue());
						responseConstructor.createRowElement("accountGroupId", accountGroupList.get(i).getValue());
						String globalAcct = null;
						String ccyEntityGrp = null;
						ccyEntityGrp = key +"&&"+ entityId +"&&" + accountGroupList.get(i).getValue();
//						ILMCcyParameters ilmccyParam = ilmGenMgr.getGlobalAlternativeAccount(hostId, entityId, key, accountGroupList.get(i).getValue());
						String accountGroupType = accountGroupsTypeMap.get(accountGroupList.get(i).getValue());
						if(accountGroupType != null ) {
							if("G".equals(accountGroupType))
								globalAcct = "Y";
							else if ("A".equals(accountGroupType))
								globalAcct = "A";
						}else
							globalAcct = "";
						if (ilmTabAcctGrpList != null && ilmTabAcctGrpList.length > 0) {
							Boolean exist = false;
							for (int j = 0; j < ilmTabAcctGrpList.length; j++) {

								if (ilmTabAcctGrpList[j].equals(ccyEntityGrp)) {
									responseConstructor.createRowElement("ilmTab", "Y");
									exist = true;
									break;
								}
							}
							if(!exist)
								responseConstructor.createRowElement("ilmTab", "N");
						} else
							responseConstructor.createRowElement("ilmTab", "N");
						responseConstructor.createRowElement("global", globalAcct);
						if (summaryList != null && summaryList.length > 0) {
							Boolean exist = false;
							for (int k = 0; k < summaryList.length; k++) {
								if (summaryList[k].split("&&").length == 3 && summaryList[k].split("&&")[0].equals(key) && summaryList[k].split("&&")[1].equals(entityId) && summaryList[k].split("&&")[2].equals(accountGroupList.get(i).getValue()) ) {
									responseConstructor.createRowElement("summary", "Y");
									exist = true;
									break;
								}
							}
							if(!exist)
								responseConstructor.createRowElement("summary", "N");
						} else
							responseConstructor.createRowElement("summary", "N");

						responseConstructor.createRowElement("order", "");
						responseConstructor.createRowElement("tabDate", "");
						responseConstructor.createRowElement("groupAnalysis", "");
						responseConstructor.createRowElement("globalChart", "");
						responseConstructor.createRowElement("combinedChart", "");
						responseConstructor.createRowElement("currencyParent", key);
						responseConstructor.createRowElement("entityParent", entityId);
						responseConstructor.createRowElement("parentId", parentIdCcy);
						responseConstructor.createRowElement("indent", 2);
						responseConstructor.createRowElement("__collapsed", ""+false);

						responseConstructor.formRowEnd();
						id++;
					}


				}

			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			/**** ilm date Combo ***********/

			lstOptions.add(new OptionInfo("T", "T", false));
			lstOptions.add(new OptionInfo("T-1", "T-1", false));


			lstSelect.add(new SelectInfo("listDates", lstOptions));
			responseConstructor.formSelect(lstSelect);
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("summaryList", summaryAsString);
			responseConstructor.createElement("globalList", globalViewAsString);
			responseConstructor.createElement("groupAnalysisList", groupAnalysisAsString);
			responseConstructor.createElement("combinedViewList", combinedViewAsString);
			responseConstructor.createElement("tabDateList", ilmDateAsString);
			responseConstructor.createElement("ilmTabEntity", ilmTabAsString);
			responseConstructor.createElement("ilmTabEntityGrp", ilmTabAcctGrpAsString);
			responseConstructor.createElement("orderEntityList", orderEntityAsString);
			responseConstructor.createElement("orderCcyList", orderCcyAsString);
			responseConstructor.createElement("isSaveMethod", isSaveScreenOption.toString());
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(componentId);
			responseHandler.sendResponse(response, xmlWriter.getData());

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getOptionData] method : - "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getOptionData",
					ILMAnalysisMonitorAction.class), request, "");
			return getView("fail");
		}
		return null;
	}


	public String generatingRandomAlphabeticString() {
		long time = System.currentTimeMillis();
		String result =  RandomStringUtils.randomAlphanumeric(17).toUpperCase();

		return result;

	}

	public String saveILMOption() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		String xmlConfig = null;
		String method = null;
		try {
			log.debug(this.getClass().getName() + "- [saveILMOption] - starting ");
			liquidityScreenOption = new ScreenOption();
			// Get liquidityMonitorConfig from the session
			xmlConfig = request.getParameter("xmlConfig");
			method = request.getParameter("methodName");
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName(SwtConstants.PROPNAME_ILM_OPTIONS);
			liquidityScreenOption.setPropertyValue(xmlConfig);
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			if(method.equals("save"))
				screenOptionManager.saveILMOptions(liquidityScreenOption, false);
			else
				screenOptionManager.saveILMOptions(liquidityScreenOption, true);
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);

			log.debug(this.getClass().getName() + "- [saveILMOption] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveILMOption] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
												   + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveILMOption", ILMAnalysisMonitorAction.class);

		} finally {
			liquidityScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return getView("statechange");
	}
	/**
	 * This method is used to update the ilm options for example when the user removes entity/ccy combination || when he removes a group analysis || glob gorup tab
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String updateILMOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption liquidityScreenOption = null;
		String tabId = null;
		String method = null;
		try {
			log.debug(this.getClass().getName() + "- [updateILMOptions] - starting ");
			liquidityScreenOption = new ScreenOption();
			// Get liquidityMonitorConfig from the session
			tabId = request.getParameter("tabId");
			final String tab = tabId.split("/")[1]+"&&"+tabId.split("/")[0];
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			hostId = SwtUtil.getCurrentHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			liquidityScreenOption = new ScreenOption();
			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName( SwtConstants.PROPNAME_ILM_OPTIONS );
			Collection<ScreenOption>  options =   screenOptionManager.getScreenOption(liquidityScreenOption);
			ScreenOption scrOption = null;
			String configXml = null;
			if (options != null && options.size() > 0) {
				Iterator<ScreenOption> iterator = options.iterator();
				while (iterator.hasNext()) {
					scrOption = iterator.next();
					configXml= scrOption.getPropertyValue();
					break;

				}
			}
			if(!SwtUtil.isEmptyOrNull(configXml)) {

				String tabentity = getTagValues(configXml, "ILMTabEntity");
				if(!SwtUtil.isEmptyOrNull(tabentity)) {
					tabentity = String.join(",",Arrays.stream(tabentity.split(","))
							.filter(obj -> !obj.equals(tab))
							.toArray(String[]::new));
					configXml = configXml.replaceAll("<ILMTabEntity>(?:.+?)(.{2})</ILMTabEntity>", "<ILMTabEntity>"+tabentity+"</ILMTabEntity>");
				}
				String tabGroup = getTagValues(configXml, "GroupAnalysis");
				if(!SwtUtil.isEmptyOrNull(tabGroup)) {
					tabGroup = String.join(",",Arrays.stream(tabGroup.split(","))
							.filter(obj -> !obj.equals(tab))
							.toArray(String[]::new));
					configXml = configXml.replaceAll("<GroupAnalysis>(?:.+?)(.{2})</GroupAnalysis>", "<GroupAnalysis>"+tabGroup+"</GroupAnalysis>");
				}
				String tabGlobal = getTagValues(configXml, "GlobalView");
				if(!SwtUtil.isEmptyOrNull(tabGlobal)) {
					tabGlobal = String.join(",",Arrays.stream(tabGlobal.split(","))
							.filter(obj -> !obj.equals(tab))
							.toArray(String[]::new));
					configXml = configXml.replaceAll("<GlobalView>(?:.+?)(.{2})</GlobalView>", "<GlobalView>"+tabGlobal+"</GlobalView>");
				}
				String tabCombined = getTagValues(configXml, "CombinedView");
				if(!SwtUtil.isEmptyOrNull(tabCombined)) {
					tabCombined = String.join(",",Arrays.stream(tabCombined.split(","))
							.filter(obj -> !obj.equals(tab))
							.toArray(String[]::new));
					configXml = configXml.replaceAll("<CombinedView>(?:.+?)(.{2})</CombinedView>", "<CombinedView>"+tabCombined+"</CombinedView>");
				}


			}

			liquidityScreenOption.getId().setHostId(hostId);
			liquidityScreenOption.getId().setUserId(userId);
			liquidityScreenOption.getId().setScreenId(SwtConstants.ILMANALYSIS_MONITOR_ID);
			liquidityScreenOption.getId().setPropertyName(SwtConstants.PROPNAME_ILM_OPTIONS);
			liquidityScreenOption.setPropertyValue(configXml);
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			screenOptionManager.saveILMOptions(liquidityScreenOption, true);
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);

			log.debug(this.getClass().getName() + "- [updateILMOptions] - exiting ");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - [updateILMOptions] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
												   + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "updateILMOptions", ILMAnalysisMonitorAction.class);

		} finally {
			liquidityScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return getView("statechange");
	}

	private static String getTagValues(final String str, String tagName) {
		String tagValue = null;
		Pattern TAG_REGEX = Pattern.compile("<"+tagName+">(.+?)</"+tagName+">", Pattern.DOTALL);
		final Matcher matcher = TAG_REGEX.matcher(str);
		while (matcher.find()) {
			tagValue = matcher.group(1);
		}
		return tagValue;
	}


	public String optionScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		return getView("options");
	}

	/** This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<org.swallow.pcm.maintenance.model.core.ColumnInfo> getOptionGridColumns(String width,
																						 String columnOrder, String hiddenColumns, HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<org.swallow.pcm.maintenance.model.core.ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getOptionGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = "ccyEntityGrp=270," + "global=40," + "summary=90," +"order=20," + "ilmTab=50," + "tabDate=50,"
						+ "globalChart=70," + "groupAnalysis=70," + "combinedChart=80,";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null || empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder =  "ccyEntityGrp," + "global," + "summary,"+"order," + "ilmTab," + "tabDate,"
							   + "globalChart," + "groupAnalysis," + "combinedChart";

			}
			orders = new ArrayList<String>();
			// Split the columns using , && save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null || empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , && save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<org.swallow.pcm.maintenance.model.core.ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				org.swallow.pcm.maintenance.model.core.ColumnInfo tmpColumnInfo = null;

				if (order.equals("ccyEntityGrp")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_CCYENTITY_HEADING, request), "ccyEntityGrp",
							"tree", 0, Integer.parseInt(widths.get("ccyEntityGrp")), false,
							false, hiddenColumnsMap.get("ccyEntityGrp"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_CCYENTITY_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals("global")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GLOBAL_HEADING, request), "global",
							SwtConstants.COLUMN_TYPE_DATE, 1, Integer.parseInt(widths.get("global")), false, false,
							hiddenColumnsMap.get("global"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GLOBAL_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals("summary")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_SUMMARY_HEADING, request), "summary",
							SwtConstants.COLUMN_TYPE_CHECK, 2, Integer.parseInt(widths.get("summary")), true, false,
							hiddenColumnsMap.get("summary"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_SUMMARY_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals("order")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_ORDER_HEADING, request), "order",
							SwtConstants.COLUMN_TYPE_NUMBER, 3, Integer.parseInt(widths.get("order")), true, false,
							hiddenColumnsMap.get("order"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_ORDER_HEADING_TOOLTIP, request));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("3");
					lstColumns.add(tmpColumnInfo);
				}

				if (order.equals("ilmTab")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_ILMTAB_HEADING, request), "ilmTab",
							SwtConstants.COLUMN_TYPE_CHECK, 4, Integer.parseInt(widths.get("ilmTab")), false, false,
							hiddenColumnsMap.get("ilmTab"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_ILMTAB_HEADING_TOOLTIP, request));

					lstColumns.add(tmpColumnInfo);

				}
				if (order.equals("tabDate")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_TABDATE_HEADING, request), "tabDate",
							SwtConstants.COLUMN_TYPE_COMBO, 5, Integer.parseInt(widths.get("tabDate")), true, false,
							hiddenColumnsMap.get("tabDate"));
					tmpColumnInfo
							.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_TABDATE_HEADING_TOOLTIP, request));
					tmpColumnInfo.setDataProvider("listDates");
					lstColumns.add(tmpColumnInfo);
				}

				if (order.equals("globalChart")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GLOBAL_CHART_HEADING, request), "globalChart",
							SwtConstants.COLUMN_TYPE_CHECK, 6, Integer.parseInt(widths.get("globalChart")), false, false,
							hiddenColumnsMap.get("globalChart"));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GLOBAL_CHART_HEADING_TOOLTIP, request));

					lstColumns.add(tmpColumnInfo);
				}

				if (order.equals("groupAnalysis")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GROUP_ANALYSIS_HEADING, request),
							"groupAnalysis", SwtConstants.COLUMN_TYPE_CHECK, 7,
							Integer.parseInt(widths.get("groupAnalysis")), false, false,
							hiddenColumnsMap.get("groupAnalysis"));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_GROUP_ANALYSIS_HEADING_TOOLTIP, request));

					lstColumns.add(tmpColumnInfo);
				}

				if (order.equals("combinedChart")) {
					tmpColumnInfo = new org.swallow.pcm.maintenance.model.core.ColumnInfo(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_COMBINED_CHART_HEADING, request),
							"combinedChart", SwtConstants.COLUMN_TYPE_CHECK, 8,
							Integer.parseInt(widths.get("combinedChart")), false, false,
							hiddenColumnsMap.get("combinedChart"));
					tmpColumnInfo.setHeaderTooltip(
							SwtUtil.getMessage(SwtConstants.ILM_OPTIONS_COMBINED_CHART_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getOptionGridColumns] method : - "
					  + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getOptionGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getOptionGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	/**
	 * This method is used to save refresh rate in s_screen_option table
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String saveThroughputRefreshRate() throws SwtException {
		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption ilmThroughputScreenOption = null;
		String refreshRate = null;
		String oldPropertyOption = null;
		Collection<ScreenOption> option = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [saveThroughputRefreshRate] - starting ");
			ilmThroughputScreenOption = new ScreenOption();
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			refreshRate = request.getParameter("refresh");

			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			ilmThroughputScreenOption.getId().setHostId(hostId);
			ilmThroughputScreenOption.getId().setUserId(userId);
			ilmThroughputScreenOption.getId().setScreenId(SwtConstants.ILM_THOUPUT_MONITOR_ID);
			ilmThroughputScreenOption.getId().setPropertyName(SwtConstants.PROPNAME_REFRESH_RATE);
			ilmThroughputScreenOption.setPropertyValue(refreshRate);

			option = screenOptionManager.getScreenOption(ilmThroughputScreenOption);

			if (option != null && option.size() > 0) {
				oldPropertyOption = (screenOptionManager.getScreenOption(ilmThroughputScreenOption)).iterator().next()
						.getPropertyValue();
				if (!oldPropertyOption.equals(refreshRate)) {
					// update preAdvice input options
					screenOptionManager.savePreAdviceOptions(ilmThroughputScreenOption, true);
				}

			} else {
				// save preAdvice input options
				screenOptionManager.savePreAdviceOptions(ilmThroughputScreenOption, false);

			}
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + "- [saveThroughputRefreshRate] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveThroughputRefreshRate] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
												   + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveThroughputRefreshRate", ILMAnalysisMonitorAction.class);

		} finally {
			ilmThroughputScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return getView("statechange");
	}

}
