/*
 * @(#)InputExceptionsDataAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.TreeMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.InputExceptionsDataManager;
import org.swallow.work.service.ScreenOptionManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * This is the action class which contains all the methods, many of which are
 * invoked form front end.
 *
 */



import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/inputexceptions", "/inputexceptions.do"})
public class InputExceptionsDataAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("data", "jsp/work/inputexceptionsdata");
		viewMap.put("dataerror", "jsp/work/inputexceptionsdataerror");
		viewMap.put("flex", "jsp/work/inputexceptions");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "summaryData":
				return summaryData();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "unspecified":
				return unspecified();
		}


		return unspecified();
	}




	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(InputExceptionsDataAction.class);
	/**
	 * Initializing menuItemId.
	 */
	private final String menuItemId = "" + SwtConstants.SCREEN_INPUT_EXCEPTIONS;
	private final String menuItemIdPCM = "" + SwtConstants.SCREEN_INPUT_EXCEPTIONS_PCM;

	/**
	 * Initializing InputExceptionsDataManager object
	 */
	@Autowired
	private InputExceptionsDataManager inputExceptionsDataManager = null;

	/**
	 * Initializing OpTimer object
	 */
	private OpTimer opTimer = new OpTimer();

	/**
	 * Method to set InputExceptionsDataManager
	 *
	 * @return
	 * @param inputExceptionsDataManager
	 */
	public void setInputExceptionsDataManager(
			InputExceptionsDataManager inputExceptionsDataManager) {
		this.inputExceptionsDataManager = inputExceptionsDataManager;
	}

	/**
	 * Method to load exception message summary to display number of records
	 * from Interfaces in each column
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String summaryData()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for fromDate */
		String fromDate = null;
		/* Variable Declaration for toDate */
		String toDate = null;
		/* Variable Declaration for dbDateTemp */
		Date dbDateTemp = null;
		/* Variable Declaration for sessionToDate */
		String sessionToDate = null;
		// Interface Message tree map
		TreeMap<String, TreeMap<String, String>> interfaceMessageMap = null;
		// result map for interface message
		TreeMap<String, TreeMap<String, String>> interfaceMessageResultMap = null;
		/* Variable Declaration for session */
		HttpSession session = null;
		/* Variable Declaration for dateFormat */
		String dateFormat = null;
		/* Variable Declaration for testDateFrmDB */
		Date testDateFrmDB = null;
		/* Variable Declaration for autoRefresh */
		String autoRefresh = null;
		/* Variable Declaration for systemDate */
		Date systemDate = null;
		/* Variable Declaration for sysDateFrmSession */
		String sysDateFrmSession = null;
		// Iterator for interface message
		Iterator<String> interfaceMsgItr = null;
		// To hold the system date
		Date systemDBDate = null;
		// Is data to be fetched from PCM schema ?
		boolean fromPCM = false;
		try {
			log.debug(this.getClass().getName() + " - [summaryData] - Entry");
			opTimer.start("all");
			/* Read From date from request */
			/*
			 * Start:Modified the mantis1776 by the sutheendran balaji on
			 * 23-03-12.The purpose to avoid runtime Exception to display
			 * default the data (from/To)Fields after click refresh button and
			 * press Enter key
			 */
			if (SwtUtil.isEmptyOrNull(request.getParameter("fromDate")))
				fromDate = SwtUtil.getSystemDateString();
			else
				fromDate = URLDecoder.decode(request.getParameter("fromDate"),
						"UTF-8");
			if (SwtUtil.isEmptyOrNull(request.getParameter("toDate")))
				toDate = fromDate;
			else
				/* Read to date from request */
				toDate = URLDecoder.decode(request.getParameter("toDate"),
						"UTF-8");
			// Instantiate the InterfaceMessageResultMap
			interfaceMessageResultMap = new TreeMap<String, TreeMap<String, String>>();
			// get the session
			session = request.getSession();
			// get the currenct date format
			dateFormat = SwtUtil.getCurrentDateFormat(session);
			// get the system date form request
			sysDateFrmSession = request.getParameter("systemDate");
			sysDateFrmSession = ((sysDateFrmSession != null) && (sysDateFrmSession
																		 .trim().length() > 0)) ? sysDateFrmSession : SwtUtil
					.getSystemDateString();
			// get the sessionToDate
			sessionToDate = request.getParameter("sessionToDate");
			sessionToDate = ((sessionToDate != null) && (sessionToDate.trim()
																 .length() > 0)) ? sessionToDate : SwtUtil
					.getSystemDateString();
			// set the attribute for sessionToDate,sysDateFrmSession in request
			request.setAttribute("sessionToDate", sessionToDate);
			request.setAttribute("sysDateFrmSession", sysDateFrmSession);
			// get the autoRefresh

			autoRefresh = request.getParameter("autoRefresh") == null ? ""
					: request.getParameter("autoRefresh");
			/*
			 * End:modified mantis 1776 by the sutheendran Balaji on
			 * 23-03-12.The purpose to avoid runtime Exeception to display
			 * default the data (from/To)Fields after click refresh button and
			 * press Enter key
			 */
			// get the system date
			systemDBDate = SwtUtil.getSystemDateFromDB();
			// get the parsed date
			testDateFrmDB = SwtUtil.parseDate(SwtUtil
					.getSysDateWithFmt(systemDBDate), dateFormat);
			// get the systemDate
			systemDate = SwtUtil.parseDate(sysDateFrmSession, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());
			fromPCM = request.getParameter("fromPCM") == null ? false
					: request.getParameter("fromPCM").equalsIgnoreCase("yes");
			request.setAttribute("fromPCM", request.getParameter("fromPCM"));
			// check autoRefresh true then set the fromdate and todate
			if (autoRefresh.equals("yes")) {
				dbDateTemp = systemDate;
				if (dbDateTemp.compareTo(testDateFrmDB) != 0) {
					request.setAttribute("dateComparing", SwtConstants.YES);
					String testDateFormatted = SwtUtil.formatDate(
							testDateFrmDB, SwtUtil.getCurrentSystemFormats(
									request.getSession()).getDateFormatValue());
					request.setAttribute("sysDateFrmSession", SwtUtil
							.formatDate(testDateFrmDB, SwtUtil
									.getCurrentSystemFormats(
											request.getSession())
									.getDateFormatValue()));
					request
							.setAttribute("sessionToDate", SwtUtil.formatDate(
									testDateFrmDB, SwtUtil
											.getCurrentSystemFormats(
													request.getSession())
											.getDateFormatValue()));
					fromDate = testDateFormatted;
					toDate = testDateFormatted;
				}
			}
			/* Get the collection of input exception summary from data base */
			interfaceMessageMap = inputExceptionsDataManager
					.getInterfaceMessageList(fromDate, toDate, opTimer,
							dateFormat, fromPCM);
			// Iterate the interface message map
			interfaceMsgItr = interfaceMessageMap.keySet().iterator();
			while (interfaceMsgItr.hasNext()) {
				// get the message
				String msg = interfaceMsgItr.next();
				// put message in the InterfaceMessageResultMap
				interfaceMessageResultMap
						.put(msg, interfaceMessageMap.get(msg));
			}
			// set the attribute for
			// InterfaceMessageResultMap,reply_status_ok,reply_message in
			// request
			request.setAttribute("result", interfaceMessageResultMap);
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Data fetch OK");
			/* Set column width with request attribute */
			bindColumnWidthInRequest(request);
			setScreenRefreshRate(request);
			// set the attribute for fromDate,toDate in request
			request.setAttribute("fromDate", inputExceptionsDataManager
					.getDefaultDate(fromDate, dateFormat));
			request.setAttribute("toDate", inputExceptionsDataManager
					.getDefaultDate(toDate, dateFormat));
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug(this.getClass().getName() + " - [summaryData] - Exit");
			return getView("data");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [summaryData] method : - "
					  + swtExp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location", swtExp.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + swtExp.getStackTrace()[0].getMethodName()
												   + ":"
												   + swtExp.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + " - Exception Catched in [summaryData] method : - "
					  + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "summaryData", InputExceptionsDataAction.class),
					request, "");

			return getView("dataerror");

		} finally {
			/* null the objects created already. */
			fromDate = null;
			toDate = null;
			dbDateTemp = null;
			sessionToDate = null;
			interfaceMessageMap = null;
			interfaceMessageResultMap = null;
			session = null;
			dateFormat = null;
			testDateFrmDB = null;
			autoRefresh = null;
			systemDate = null;
			sysDateFrmSession = null;
			interfaceMsgItr = null;
			systemDBDate = null;
		}
	}

	/**
	 * This method gets the refresh rate for the metagroup monitor screen from
	 * the database
	 *
	 * @param request
	 *            the request parameter
	 * @throws SwtException
	 *             if any\
	 */
	private void setScreenRefreshRate(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				  + "- [setScreenRefreshRate] - Entering ");
		// Fetches the user id from session
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		// Gets the ScreenOptionManager instance
		ScreenOptionManager screenOptionManager = (ScreenOptionManager) (SwtUtil
				.getBean("screenOptionManager"));
		// Initialising the ScreenOption instance
		ScreenOption screenOption = new ScreenOption();
		// Setting the host id
		screenOption.getId().setHostId(SwtUtil.getCurrentHostId());
		// Setting the user id
		screenOption.getId().setUserId(userId);
		// Setting the screen id
		screenOption.getId().setScreenId("" + SwtConstants.INPUTEXCEPTION_ID);
		// Fetching the refresh rate
		screenOption = screenOptionManager.getRefreshRate(screenOption);
		// setting the rate in request
		request.setAttribute("autoRefreshRate", new Integer(screenOption
				.getPropertyValue()));
		log.debug(this.getClass().getName()
				  + "- [setScreenRefreshRate] - Exiting ");
	}

	/**
	 * Method to set column width in request attribute
	 *
	 * @param request
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request) {
		/* Method's local variable declaration */
		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;
		boolean fromPCM = false;

		try {

			log.debug(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + "Entry");

			fromPCM = request.getParameter("fromPCM") == null ? false
					: request.getParameter("fromPCM").equalsIgnoreCase("yes");
			/* Read the user preferences for column width from property value */
			width = SwtUtil.getPropertyValue(request, fromPCM ? menuItemIdPCM:menuItemId, "display",
					"column_width");
			/* Condition to set default column width */
			/* Set default width for columns */
			if(SwtUtil.isEmptyOrNull(width)){
				width = "interface=200,accepted=132,submitted=132,supressed=132,rejected=132,awaiting=132,received=132,repair=132";
			}

			widths = new HashMap<String, String>();
			/* Get column width for each column */
			props = width.split(",");
			/* Loop to separate column and width value in hash map */
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			log.debug(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + "Exit");
			request.setAttribute("column_width", widths);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [bindColumnWidthInRequest] - " + e.getMessage());
		}finally{
			//nullify the objects
			width = null;
			props = null;
		}
	}
	/**
	 * Method to save column width in user preferences list
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @param SwtException
	 */
	public String saveColumnWidth() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
				  + "Entry");
		/* Method's local variable declaration */
		String width = null;
		boolean fromPCM = false;
		try {
			/* Read width values from request */
			width = URLDecoder.decode(request.getParameter("width"), "UTF-8");
			fromPCM = request.getParameter("fromPCM") == null ? false
					: request.getParameter("fromPCM").equalsIgnoreCase("yes");
			/* Condition to check width is not null */
			if (width != null) {
				/* Set width value for the screen in user preference list */
				SwtUtil.setPropertyValue(request, fromPCM?menuItemIdPCM:menuItemId, "display",
						"column_width", width);

				request.setAttribute("reply_status_ok", "true");
				request.setAttribute("reply_message", "Column width saved ok");
			} else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message",
						"Width parameter not sent");
			}
			log.debug(this.getClass().getName() + " - [saveColumnWidth] - "
					  + "Exit");
		} catch (Exception e) {

			log.error(this.getClass().getName()
					  + " - Exception Catched in [saveColumnWidth] method : - "
					  + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
														   .getClassName()
												   + "."
												   + e.getStackTrace()[0].getMethodName()
												   + ":"
												   + e.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "saveColumnWidth", InputExceptionsDataAction.class),
					request, "");
		}

		return getView("statechange");
	}

	/**
	 * Method called when screen is loaded and returns flex page
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [unspecified] - " + " Entry");
		request.setAttribute("userId", SwtUtil.getCurrentUserId(request
				.getSession()));
		request.setAttribute("fromPCM", request.getParameter("fromPCM"));
		return getView("flex");
	}

}
