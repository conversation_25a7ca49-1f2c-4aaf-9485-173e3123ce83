/*
 * Created on Dec 6, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.web.form;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class WorkflowMonitorForm extends BaseObject implements org.swallow.model.AuditComponent {

	private String dummyString = "";
	
	/**
	 * @return Returns the dummyString.
	 */
	public String getDummyString() {
		return dummyString;
	}
	/**
	 * @param dummyString The dummyString to set.
	 */
	public void setDummyString(String dummyString) {
		this.dummyString = dummyString;
	}
}
