/*
 * @(#)CentralBankMonitorBean.java 1.0 19/03/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web.form;

import java.math.BigDecimal;
import java.util.Date;

/**
 * CentralBankMonitorBean.java
 * 
 * This java bean is used to handle the balances for specified period in central
 * bank monitor
 * 
 * <AUTHOR>
 * @date Mar 19,2010
 */
public class CentralBankMonitorBean {
	
	/* Start:Code modified by <PERSON><PERSON><PERSON> to use the Predicted Balance from Double to BigDecimal */
	// Predicted Balance
	private BigDecimal predictedBal;
	/* End:Code modified by <PERSON><PERSON><PERSON> to use the Predicted Balance from Double to BigDecimal */
	// Predicted Balance in string
	private String predictedBalAsString;
	// Balance Date
	private Date balanceDate;
	// Balance Date in string
	private String balanceDateAsString;
	// Start:Code added by Kalidass for Mantis 1181 on 23-July-2010
	// Balance Date in string
	private String balanceOrgDateAsString;
	// End:Code added by Kalidass for Mantis 1181 on 23-July-2010
	// predicted Balance Negative
	private boolean predictedBalNegative;
	// holiday Flag
	private String holiday;
	/* Start:Code commented by Kalidass to use the Predicted Balance from Double to BigDecimal */
//	/**
//	 * @return the predictedBal
//	 */
//	public double getPredictedBal() {
//		return predictedBal;
//	}
//
//	/**
//	 * @param predictedBal
//	 *            the predictedBal to set
//	 */
//	public void setPredictedBal(double predictedBal) {
//		this.predictedBal = predictedBal;
//	}
	/* Start:Code commented by Kalidass to use the Predicted Balance from Double to BigDecimal */
	/**
	 * @return the predictedBalAsString
	 */
	public String getPredictedBalAsString() {
		return predictedBalAsString;
	}

	/**
	 * @param predictedBalAsString
	 *            the predictedBalAsString to set
	 */
	public void setPredictedBalAsString(String predictedBalAsString) {
		this.predictedBalAsString = predictedBalAsString;
	}

	/**
	 * @return the balanceDate
	 */
	public Date getBalanceDate() {
		return balanceDate;
	}

	/**
	 * @param balanceDate
	 *            the balanceDate to set
	 */
	public void setBalanceDate(Date balanceDate) {
		this.balanceDate = balanceDate;
	}

	/**
	 * @return the balanceDateAsString
	 */
	public String getBalanceDateAsString() {
		return balanceDateAsString;
	}

	/**
	 * @param balanceDateAsString
	 *            the balanceDateAsString to set
	 */
	public void setBalanceDateAsString(String balanceDateAsString) {
		this.balanceDateAsString = balanceDateAsString;
	}

	/**
	 * @return the predictedBalNegative
	 */
	public boolean isPredictedBalNegative() {
		return predictedBalNegative;
	}

	/**
	 * @param predictedBalNegative
	 *            the predictedBalNegative to set
	 */
	public void setPredictedBalNegative(boolean predictedBalNegative) {
		this.predictedBalNegative = predictedBalNegative;
	}

	/**
	 * @return the holiday
	 */
	public String getHoliday() {
		return holiday;
	}

	/**
	 * @param holiday
	 *            the holiday to set
	 */
	public void setHoliday(String holiday) {
		this.holiday = holiday;
	}
	/* Start:Code added by Kalidass to use the Predicted Balance from Double to BigDecimal */
	/**
	 * @return the predictedBal
	 */
	public BigDecimal getPredictedBal() {
		return predictedBal;
	}

	/**
	 * @param predictedBal the predictedBal to set
	 */
	public void setPredictedBal(BigDecimal predictedBal) {
		this.predictedBal = predictedBal;
	}
	/* Start:Code added by Kalidass to use the Predicted Balance from Double to BigDecimal */

	// Start:Code added by Kalidass for Mantis 1181 on 23-July-2010
	/**
	 * @return the balanceOrgDateAsString
	 */
	public String getBalanceOrgDateAsString() {
		return balanceOrgDateAsString;
	}

	/**
	 * @param balanceOrgDateAsString the balanceOrgDateAsString to set
	 */
	public void setBalanceOrgDateAsString(String balanceOrgDateAsString) {
		this.balanceOrgDateAsString = balanceOrgDateAsString;
	}
	// End:Code added by Kalidass for Mantis 1181 on 23-July-2010
}
