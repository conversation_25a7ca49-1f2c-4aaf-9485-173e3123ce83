/*
 * @(#)MonitorTabBean.java 1.0 23/08/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.web.form;



/**
 * MonitorTabBean.java
 * 
 * This java bean is used to set the details about seven days in monitor screen tab bar and 
 * also denote the holidays/weekends.
 *  
 * <AUTHOR>
 * @date Aug 23,2010
 */
public class MonitorTabBean {
	
	//Tab Date in String
	private String tabDateAsString;
	//Denote the business day
	private String businessDay;
	// Hold count of alerts
	private int count = 0;
	// Hold Tab name WithoutCount
	private String tabName = null;
	// Hold Tab name WithoutCount
	private int tabId = 0;
	
	//Tab Name in String
	private String tabNameAsString;
	
	/**
	 * @return the tabDateAsString
	 */
	public String getTabDateAsString() {
		return tabDateAsString;
	}
	/**
	 * @param tabDateAsString the tabDateAsString to set
	 */
	public void setTabDateAsString(String tabDateAsString) {
		this.tabDateAsString = tabDateAsString;
	}
	/**
	 * @return the businessDay
	 */
	public String getBusinessDay() {
		return businessDay;
	}
	/**
	 * @param businessDay the businessDay to set
	 */
	public void setBusinessDay(String businessDay) {
		this.businessDay = businessDay;
	}
	public String getTabNameAsString() {
		return tabNameAsString;
	}
	public void setTabNameAsString(String tabNameAsString) {
		this.tabNameAsString = tabNameAsString;
	}
	public int getCount() {
		return count;
	}
	public void setCount(int count) {
		this.count = count;
	}
	public String getTabName() {
		return tabName;
	}
	public void setTabName(String tabName) {
		this.tabName = tabName;
	}
	public int getTabId() {
		return tabId;
	}
	public void setTabId(int tabId) {
		this.tabId = tabId;
	}
	

}
