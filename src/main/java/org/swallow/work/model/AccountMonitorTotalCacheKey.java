/*
 * @(#) AccountMonitorTotalCacheKey. java 1.0 08/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import java.io.Serializable;
import java.util.Date;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
*This is a POJO for storing key objects in the cache for calculating totals displayed on the screen.
*/

public class AccountMonitorTotalCacheKey implements Serializable {
	
	private final Log log = LogFactory.getLog(AccountMonitorTotalCacheKey.class);
	
	private String hostId;
	private String entityId;
	private String currencyCode;
	private String accountId;
	private String accountType;	
	private Date date;
	private Integer dummyFlag = new Integer(99); 
	
	

	
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}
	/**
	 * @param accountType The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the date.
	 */
	public Date getDate() {
		return date;
	}
	/**
	 * @param date The date to set.
	 */
	public void setDate(Date date) {
		this.date = date;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	
	
	/**
	 * Overriding the default equals function
	 */	
	public boolean equals(Object obj) {
		
        log.debug("Entering equals method");
        
        boolean retValue = false;

        if ((obj != null) && obj instanceof AccountMonitorTotalCacheKey) {
            log.debug("obj - " + obj);
            retValue = entityId.equals(((AccountMonitorTotalCacheKey) obj).getEntityId()) 
						&& currencyCode.equals(((AccountMonitorTotalCacheKey) obj).getCurrencyCode())
						&& date.equals(((AccountMonitorTotalCacheKey) obj).getDate())
						&& accountId.equals(((AccountMonitorTotalCacheKey) obj).getAccountId())		
						&& accountType.equals(((AccountMonitorTotalCacheKey) obj).getAccountType())						
						&& hostId.equals(((AccountMonitorTotalCacheKey) obj).getHostId());
        }

        log.debug("retValue - " + retValue);
        log.debug("Exiting equals method");

        return retValue;
    }
	
	/**
	 * Overriding the default hashCode function
	 * @return - hashCode object;
	 */
	  public int hashCode() {
        return entityId.hashCode() + currencyCode.hashCode() + accountId.hashCode() + accountType.hashCode() + date.hashCode()
								   + hostId.hashCode() + dummyFlag.hashCode();
    }
}
