 /*
 * @(#)MetagroupCodePredictedBalanceTO.java 1.0 04/09/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.io.Serializable;

public class MetagroupCodePredictedBalanceTO implements Serializable{
    /** The predicted balance for a metagroup code */
    private String predictedBalance = "";

    /** The flag for determining whether the predictedbalance is negative */
    private boolean predictedBalanceNegative = false;

    /** The Metagroup Code */
    private String metagroupCode = "";

    /** The Metagroup Name */
    private String metagroupName = "";

    private Double predictedBalanceBeforeFormatting = null;

    /** Holds the levelName */
    private String levelName;
    
    /** The Group Code */
    private String groupCode = "";

    /** The Group Name */
    private String groupName = "";
    
    /** The Book Code */
    private String bookCode = "";

    /** The Book Name */
    private String bookName = "";

    /*--Start: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
    /** Holds the locationName */
    private String locationName;
    /*--End: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	

    /** Constructor for the class
     * @param metagroupCode the metagroup code
     * @param metagroupName the metagroup name
     * @param levelName the level name
     * @param predictedBalance the predicted balance
     * @param predictedBalanceNegative the flag to check whether the predicted balance is negative
     */
    public MetagroupCodePredictedBalanceTO(String metagroupCode, String metagroupName, String bookCode, 
    		String bookName, String groupCode, String groupName, String locationName,String levelName,
    		String predictedBalance,boolean predictedBalanceNegative,Double predictedBalanceBeforeFormatting) {
        this.metagroupCode = metagroupCode;
        this.metagroupName = metagroupName;
        this.levelName = levelName;
        this.bookCode = bookCode;
        this.bookName = bookName;
        this.groupCode = groupCode;
        this.groupName = groupName;
        this.locationName = locationName;
        this.predictedBalance = predictedBalance;
        this.predictedBalanceNegative = predictedBalanceNegative;
        this.predictedBalanceBeforeFormatting=predictedBalanceBeforeFormatting;
    }

    /**
     * @return Returns the metagroupCode.
     */
    public String getMetagroupCode() {
        return metagroupCode;
    }

    /**
     * @return Returns the metagroupName.
     */
    public String getMetagroupName() {
        return metagroupName;
    }

    /**
     * @return Returns the predictedBalance.
     */
    public String getPredictedBalance() {
        return predictedBalance;
    }

    /**
     * @return Returns the predictedBalanceNegative.
     */
    public boolean isPredictedBalanceNegative() {
        return predictedBalanceNegative;
    }

    /**
	 * @return Returns the predictedBalanceBeforeFormatting.
	 */
    public Double getPredictedBalanceBeforeFormatting() {
		return predictedBalanceBeforeFormatting;
	}
	/**
	 * @param predictedBalanceBeforeFormatting The predictedBalanceBeforeFormatting to set.
	 */
	public void setPredictedBalanceBeforeFormatting(
			Double predictedBalanceBeforeFormatting) {
		this.predictedBalanceBeforeFormatting = predictedBalanceBeforeFormatting;
	}

    /**
	 * @return Returns the levelName.
	 */
    public String getLevelName() {
		return levelName;
	}
	/**
	 * @param levelName The levelName to set.
	 */
	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}
	
	/* Book Code */
	/**
     * @return Returns the bookCode.
     */
    public String getBookCode() {
        return bookCode;
    }
    /**
     * @return Returns the bookName.
     */
    public String getBookName() {
        return bookName;
    }
    /*--Start: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	
	public String getLocationName() {
		return locationName;
	}
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
    /*--End: Code changed for SRS Book Locations ON 20-JULY-2007 BY Sanjay Sharma--*/	

	/**
     * @return Returns the groupCode.
     */
    public String getGroupCode() {
        return groupCode;
    }

    /**
     * @return Returns the groupName.
     */
    public String getGroupName() {
        return groupName;
    }

	/* Group Code */
}
