/*
 * @(#)GroupCodePredictedBalanceTO .java 05/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;

import java.io.Serializable;

public class GroupCodePredictedBalanceTO implements Serializable{
    /** The predicted balance for a group code */
    private String predictedBalance = "";

    /** The flag for determining whether the predictedbalance is negative */
    private boolean predictedBalanceNegative = false;

    /** The Group Code */
    private String groupCode = "";

    /** The Group Name */
    private String groupName = "";

    private Double predictedBalanceBeforeFormatting = null;

    /** Holds the levelName */
    private String levelName;

    /** Constructor for the class
     * @param groupCode the group code
     * @param groupName the group name
     * @param levelName the level name
     * @param predictedBalance the predicted balance
     * @param predictedBalanceNegative the flag to check whether the predicted balance is negative
     */
    public GroupCodePredictedBalanceTO(String groupCode, String groupName, String levelName,
        String predictedBalance, boolean predictedBalanceNegative,Double predictedBalanceBeforeFormatting) {
        this.groupCode = groupCode;
        this.groupName = groupName;
        this.levelName = levelName;
        this.predictedBalance = predictedBalance;
        this.predictedBalanceNegative = predictedBalanceNegative;
        this.predictedBalanceBeforeFormatting=predictedBalanceBeforeFormatting;
    }

    /**
     * @return Returns the groupCode.
     */
    public String getGroupCode() {
        return groupCode;
    }

    /**
     * @return Returns the groupName.
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * @return Returns the predictedBalance.
     */
    public String getPredictedBalance() {
        return predictedBalance;
    }

    /**
     * @return Returns the predictedBalanceNegative.
     */
    public boolean isPredictedBalanceNegative() {
        return predictedBalanceNegative;
    }

    /**
	 * @return Returns the predictedBalanceBeforeFormatting.
	 */
    public Double getPredictedBalanceBeforeFormatting() {
		return predictedBalanceBeforeFormatting;
	}
	/**
	 * @param predictedBalanceBeforeFormatting The predictedBalanceBeforeFormatting to set.
	 */
	public void setPredictedBalanceBeforeFormatting(
			Double predictedBalanceBeforeFormatting) {
		this.predictedBalanceBeforeFormatting = predictedBalanceBeforeFormatting;
	}

    /**
	 * @return Returns the levelName.
	 */
    public String getLevelName() {
		return levelName;
	}
	/**
	 * @param levelName The levelName to set.
	 */
	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}
}
