<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.CorporateAccount"
		table="P_CB_MONITOR_CORPORATE">	
	<!-- Start:Mapping changes-primary key fields changed  by sami for Mantis 1256 on 28-Sep-2010 -->		
	 <id name="corporateSeqNo" column="SEQUENCE_NUMBER" type="long">
   <generator class="sequence">   
    <param name="sequence_name">SEQ_CB_CORPORATE</param>
   <param name="increment_size">1</param>
   </generator>
  </id>
		<!--   Corporate name/valuedate filed removed from primary key  for Mantis 1256 by sami on 28-Sep-2010 -->
		<property name="entityId" column="ENTITY_ID" not-null="false" />
		<property name="valueDate" column="VALUE_DATE" not-null="false" />
		<property name="corporateName" column="CORPORATE_NAME" not-null="false" />
	<!-- End:Mapping changes-primary key fields changed  by sami for Mantis 1256 on 28-Sep-2010 -->	
		<property name="amount" column="AMOUNT" not-null="false" />
		<property name="updateDate" column="UPDATE_DATE" not-null="false" />
		<property name="updateUser" column="UPDATE_USER" not-null="false" />
		
	</class>
</hibernate-mapping>
