/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;


import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.metamodel.spi.ValueAccess;
import org.hibernate.usertype.CompositeUserType;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */

public class MatchIdType implements CompositeUserType, Serializable{
	
	private final Log log = LogFactory.getLog(MatchIdType.class);	// log object

/*//TODO:SBR	
	 @Override
	    public String[] getPropertyNames() {
	        return new String[]{"hostId", "entityId", "matchId"};
	    }

	    @Override
	    public Type[] getPropertyTypes() {
	        return new Type[]{
	            StringType.INSTANCE,  // for hostId
	            StringType.INSTANCE,  // for entityId
	            LongType.INSTANCE      // for matchId
	        };
	    }
*/
	    @Override
	    public Object getPropertyValue(Object component, int property) throws HibernateException {
	    	Match.Id matchId = (Match.Id) component;
	        switch (property) {
	            case 0:
	                return matchId.getHostId();
	            case 1:
	                return matchId.getEntityId();
	            case 2:
	                return matchId.getMatchId();
	            default:
	                throw new HibernateException("Invalid property index " + property);
	        }
	    }

	    //@Override
	    public void setPropertyValue(Object component, int property, Object value) throws HibernateException {
	    	Match.Id matchId = (Match.Id) component;
	        switch (property) {
	            case 0:
	                matchId.setHostId((String) value);
	                break;
	            case 1:
	                matchId.setEntityId((String) value);
	                break;
	            case 2:
	                matchId.setMatchId((Long) value);
	                break;
	            default:
	                throw new HibernateException("Invalid property index " + property);
	        }
	    }
	    
	   public Class returnedClass() 
	   { 
	      return  Match.Id.class; 
	   } 	   

	   public boolean equals(Object x, Object y) throws HibernateException 
	   { 
	      if (x == y) return true; 
	      if (x == null || y == null) return false; 
	      return x.equals(y); 
	   } 	   
/*//TODO:SBR
	   public Object nullSafeGet(ResultSet rs, String[] names, SessionImplementor session, Object owner) 
			throws HibernateException, SQLException 
       { 
		   	if (rs.getObject(names[0])!=null && rs.getObject(names[1])!=null) 
		   	{ 
		   		String hostId = rs.getString(names[0]); 
		   		String entityId = rs.getString(names[1]);
		   		long matchId = rs.getLong(names[2]);
		   		Match.Id matchIdObj = new Match.Id();
		   		matchIdObj.setHostId(hostId);
		   		matchIdObj.setEntityId(entityId);	
		   		matchIdObj.setMatchId(new Long(matchId)); 		   		
		   		return matchIdObj;
		   	} 
		   	else 
		   	{ 
		   		return null; 
		   	} 
       } 
	   
	   @Override
	    public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner) throws HibernateException, SQLException {
	        String hostId = rs.getString(names[0]);
	        String entityId = rs.getString(names[1]);
	        Long matchId = rs.getLong(names[2]);
	        return new Match.Id(hostId, entityId, matchId);
	    }

	    @Override
	    public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session) throws HibernateException, SQLException {
	        if (value == null) {
	            st.setNull(index, Types.VARCHAR);
	            st.setNull(index + 1, Types.VARCHAR);
	            st.setNull(index + 2, Types.BIGINT);
	        } else {
	        	Match.Id matchId = (Match.Id) value;
	            st.setString(index, matchId.getHostId());
	            st.setString(index + 1, matchId.getEntityId());
	            st.setLong(index + 2, matchId.getMatchId());
	        }
	    }
*/
	   public void nullSafeSet(PreparedStatement st, Object value, int index, SessionImplementor session) throws HibernateException, SQLException { 
	      if (value==null) { 
	         st.setNull(index, Types.VARCHAR); 
	         st.setNull(index+1, Types.VARCHAR);
	         st.setNull(index+1, Types.NUMERIC);	         
	      } 
	      else 
	      { 	      	
	      	 Match.Id matchIdObj = (Match.Id)value;
	      	
	         // Setting the host id
	      	 if (matchIdObj.getHostId() == null) { 
	            st.setNull(index, Types.VARCHAR); 
	         } 
	         else { 
	            st.setString(index, matchIdObj.getHostId()); 
	         } 
	      	 
	         // Setting the entity id
	         if (matchIdObj.getEntityId() == null) { 
	            st.setNull(index+1, Types.VARCHAR); 
	         } 
	         else { 
	         	st.setString(index+1, matchIdObj.getEntityId()); 
	         }
	         
	         // Setting the match id
	         if (matchIdObj.getMatchId() == null) { 
	            st.setNull(index+2, Types.VARCHAR); // TODO 
	         } 
	         else { 
	         	st.setLong(index+2, matchIdObj.getMatchId().longValue()); 
	         }	         	         
	      } 
	   } 

	   public Object deepCopy(Object value) throws HibernateException { 
	      return value; 
	   } 	   

	   public boolean isMutable() { 
	      return false; 
	   } 

	   public Serializable disassemble(Object value, SessionImplementor session) throws HibernateException { 
	      return (Serializable)value; 
	   } 

	   public Object assemble(Serializable cached, SessionImplementor session, Object owner) throws HibernateException { 
	      return cached; 
	   }

	   
	//TODO:SBR   
	@Override
	public int hashCode(Object x) throws HibernateException {
		// TODO Auto-generated method stub
		return 0;
	}

    @Override
    public Object instantiate(ValueAccess values, SessionFactoryImplementor sessionFactory) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Class embeddable() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Serializable disassemble(Object value) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object assemble(Serializable cached, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object replace(Object detached, Object managed, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }

/*//TODO:SBR
	@Override
	public Serializable disassemble(Object value, SharedSessionContractImplementor session) throws HibernateException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Object assemble(Serializable cached, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Object replace(Object original, Object target, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		// TODO Auto-generated method stub
		return null;
	}
*/	
}
