<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.AssumptionData"
		table="P_FCAST_ASSUMPTIONDATA">
		<id name="assumptionId" type="long" column="ASSUMPTION_ID">
			<generator class="sequence">
				<param name="sequence_name">SEQ_P_FCAST_ASSUMPTIONDATA</param>
			 <param name="increment_size">1</param>
			 </generator>
		</id>
		<property name="hostId" column="HOST_ID" not-null="false" />
		<property name="entityId" column="ENTITY_ID" not-null="false" />
		<property name="currencyCode" column="CURRENCY_CODE" not-null="false" />
		<property name="valueDate" column="VALUE_DATE" not-null="false" />
		<property name="assumptionsAmount" column="ASSUMPTIONS_AMT" not-null="false" />
		<property name="assumption" column="ASSUMPTION" not-null="false" />
		<property name="templateId" column="TEMPLATE_ID" not-null="false" />
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
	</class>
</hibernate-mapping>
