<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.PersonalEntityList"
		table="P_PERSONAL_ENTITY_LIST">
		<composite-id class="org.swallow.work.model.PersonalEntityList$Id" name="id" unsaved-value="any">
			<key-property name="hostId" access="field"
				column="HOST_ID" />
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="screenId" access="field"
			column="SCREEN_ID" />
			<key-property name="userId" access="field"
				column="USER_ID" />
		</composite-id>
		
		<property name="updateDate" column="UPDATE_DATE" not-null="false" />
		<property name="yesDisplay" column="Y_DISPLAY" not-null="false" />
		<property name="displayDays" column="DISPLAY_DAYS" not-null="false" />
		<property name="priorityOrder" column="PRIORITY_ORDER" not-null="false" />
		<property name="yesAggrgEntity" column="Y_AGGRG_ENTITY" not-null="false" />
	</class>
</hibernate-mapping>
