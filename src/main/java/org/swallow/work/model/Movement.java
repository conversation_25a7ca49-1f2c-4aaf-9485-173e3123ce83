/*
 * Created on Jan 3, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.util.Date;

import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class Movement extends BaseObject {
	
	private MovementExt movementExt = new MovementExt();
	

 private String alerting;
	/**
	 * Currency Code as String
	 */
	private String currencyCode;

	/**
	 * Currency Name as String
	 */
	private String currName;

	private String archiveId;

	private String access;

	/**
	 * BookCode as String
	 */
	private String bookCode;

	

	/**
	 * Value Date as Date
	 */
	private Date valueDate = null;

	/**
	 * ValueDateAsString as String
	 */
	private String valueDateAsString;

	/**
	 * Amount as Double
	 */
	private Double amount;

	/**
	 * AmountAsString as String
	 */
	private String amountAsString;

	/**
	 * Sign as String
	 */
	private String sign;

	/**
	 * Movement Type as String
	 */
	private String movementType = new String("C");

	/**
	 * Account ID as String
	 */
	private String accountId;

	/**
	 * Reference1 as String
	 */
	private String reference1;

	/**
	 * Reference2 as String
	 */
	private String reference2;

	/**
	 * Reference3 as String
	 */
	private String reference3;

	/**
	 * Reference4 as String
	 */
	private String reference4;

	/**
	 * CounterPartyID as String
	 */
	private String counterPartyId;

	/**
	 * CounterPartyText1 as String
	 */
	private String counterPartyText1;

	/**
	 * CounterPartyText2 as String
	 */
	private String counterPartyText2;

	/**
	 * CounterPartyText3 as String
	 */
	private String counterPartyText3;

	/**
	 * CounterPartyText4 as String
	 */
	private String counterPartyText4;
	/**
	 * CounterPartyText5 as String
	 */
	private String counterPartyText5;

	/**
	 * Beneficiary ID as String
	 */
	private String beneficiaryId;

	/**
	 * BeneficiaryText1 as String
	 */
	private String beneficiaryText1;

	/**
	 * BeneficiaryText2 as String
	 */
	private String beneficiaryText2;

	/**
	 * BeneficiaryText3 as String
	 */
	private String beneficiaryText3;

	/**
	 * BeneficiaryText4 as String
	 */
	private String beneficiaryText4;
	/**
	 * BeneficiaryText5 as String
	 */
	private String beneficiaryText5;

	/**
	 * Custodian ID as String
	 */
	private String custodianId;

	/**
	 * CustodianText1 as String
	 */
	private String custodianText1;

	/**
	 * CustodianText2 as String
	 */
	private String custodianText2;

	/**
	 * CustodianText3 as String
	 */
	private String custodianText3;

	/**
	 * CustodianText4 as String
	 */
	private String custodianText4;
	/**
	 * CustodianText5 as String
	 */
	private String custodianText5;

	/**
	 * BookCodeAvail as String
	 */
	private String bookCodeAvail = SwtConstants.YES;

	/**
	 * PositionLevel as String
	 */
	private Integer positionLevel;

	/**
	 * positionLevelAsString as String
	 */
	private String positionLevelAsString;

	/**
	 * predictStatus as String
	 */
	private String predictStatus = SwtConstants.PREDICT_INC;

	/**
	 * extractStatus as String
	 */
	private String extractStatus = SwtConstants.EXTRACT_EXC;

	/**
	 * matchId as Long
	 */
	private Long matchId;

	/**
	 * DOCUMENT ME!
	 */
	private String timefrom;

	/**
	 * timeto as String
	 */
	private String timeto;

	/**
	 * matchStatus as String
	 */
	private String matchStatus;

	/**
	 * matchStatusDesc as String
	 */
	private String matchStatusDesc;

	/**
	 * updateDate as Date
	 */
	private Date updateDate;

	/**
	 * updateDateAsString as String
	 */
	private String updateDateAsString;

	/**
	 * Value update Date as Date
	 */
	private Date upDate_Date;

	/**
	 * updateTimeAsString as String
	 */
	private String updateTimeAsString;

	/**
	 * updateUser as String
	 */
	private String updateUser;

	/**
	 * inputDate sa Date
	 */
	private Date inputDate;

	/**
	 * inputDateAsString as String
	 */
	private String inputDateAsString;

	/**
	 * inputSource as String
	 */
	private String inputSource;

	/**
	 * messageId as String
	 */
	private String messageId;

	/**
	 * messageFormat as String
	 */
	private String messageFormat;

	/**
	 * initialPredStatus as String
	 */
	private String initialPredStatus;

	/**
	 * source as String
	 */
	private String source;

	/**
	 * amountover as Double
	 */
	private Double amountover;

	/**
	 * amountoverasstring sa String
	 */
	private String amountoverasstring;

	/**
	 * amountunder as Double
	 */
	private Double amountunder;

	/**
	 * amountunderasstring as String
	 */
	private String amountunderasstring;

	/**
	 * group as String
	 */
	private String group;

	/**
	 * metaGroup as String
	 */
	private String metaGroup;

	/**
	 * sortorder as String
	 */
	private String sortorder;

	/**
	 * reference as String
	 */
	private String reference;

	/**
	 * inputUser as String
	 */
	private String inputUser;

	/**
	 * inputRole as String
	 */
	private String inputRole;

	/**
	 * valueFromDate as Date
	 */
	private Date valueFromDate = null;

	/**
	 * valueFromDateAsString as String
	 */
	private String valueFromDateAsString;

	/**
	 * valueToDate as Date
	 */
	private Date valueToDate = null;

	/**
	 * valueToDateAsString as String
	 */
	private String valueToDateAsString;

	// Code Added By Tapan

	/**
	 * accttype as String
	 */
	private String accttype;

	/**
	 * account object of AccountMaintenance Class
	 */
	private AcctMaintenance account = new AcctMaintenance();

	/**
	 * mansweepflg as String
	 */
	private String mansweepflg;

	/**
	 * acctstatusflg as String
	 */
	private String acctstatusflg;

	// Code Added by Girish Bal

	/**
	 * hasNotes as String
	 */
	private String hasNotes;

	// private String hostId;
	// private String entityId;

	/**
	 * entityName as String
	 */
	private String entityName;

	// private String movementId;

	/**
	 * New value Date
	 */
	private String newValueDate;
	/**
	 * Original value date
	 */

	private Integer notesCount;

	private String positionLevelName;

	private Integer noOfNotesAttached;

	/* Start: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc */
	private String openFlag = SwtConstants.NO; // mapped to P_MOVEMENT.OPEN
	/* End: Refer to Smart-Predict_SRS_Open_Movements_0.2.doc */

	/* START: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */
	private String applyCurrencyThreshold;
	/* END: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */

	/*
	 * START: Code changed as par SRS - External Balance Calculation for ING,
	 * 11-JUL-2007
	 */
	private String extBalStatus = SwtConstants.EXT_BAL_INC;
	/*
	 * END: Code changed as par SRS - External Balance Calculation for ING,
	 * 11-JUL-2007
	 */

	// Added for open Movement Queue Currency Group Drop down
	/**
	 * Currency Group ID
	 */
	private String currencyGrpId;

	// This flag carries value 'Y' if no. of Messages are more then one
	private String isManyMessages;

	private String toMatch;

	private String referenceflag;

	/*
	 * Start: modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings,
	 * searching movements restricted to Todays Date"
	 */
	private String openMovementFlag;
	/*
	 * End: modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings,
	 * searching movements restricted to Todays Date"
	 */

	private String matchingParty;
	private String productType;
	private Date postingDate;
	private String postingDateAsString;
	private Date postingFromDate;
	private String postingFromDateAsString;
	private Date postingToDate;
	private String postingToDateAsString;
	private Date expectedSettlementDateTime;
	private Date settlementDateTime;
	private String criticalPaymentType;
	private String expectedSettlementDateTimeAsString;
	private String settlementDateTimeAsString;

	/**
	 * Ordering Customer ID as String
	 */
	private String orderingCustomerId;
	/**
	 * Ordering institution ID  as String
	 */
	private String orderingInstitutionId;
	/**
	 * Sender's Correspondent ID as String
	 */
	private String senderCorrespondentId;
	/**
	 * Receiver's Correspondent ID as String
	 */
	private String receiverCorrespondentId;
	/**
	 * Intermediary Institution ID as String
	 */
	private String intermediaryInstitutionId;
	/**
	 * Account with Institution ID as String
	 */
	private String accountWithInstitutionId;
	/**
	 * Beneficiary Customer as String
	 */
	private String beneficiaryCustomerId;
	
	private String scenarioHighlighted = null;
	private String matchcenarioHighlighted = null;
	
	private String againstAccountEntityId = null;
	
	private String extraText1 = null;
	
	private String ilmFcastStatus = null;
	
	private String attributeXml = null;
	
	private String uetr = null;

	private String failingCause = null;

	private boolean successful;

	public Movement() {
		super();
		Date sysDate = SwtUtil.getSystemDatewithTime();
		this.valueDate = sysDate;
		this.valueFromDate = sysDate;
		this.valueToDate = sysDate;
	}
	
	
	
	public Movement(Date valueDate) {
		super();
		this.valueDate = valueDate;
		this.valueFromDate = valueDate;
		this.valueToDate = valueDate;
	}



	/**
	 * @return Returns the isManyMessages.
	 */
	public String getIsManyMessages() {
		return isManyMessages;
	}

	/**
	 * @param isManyMessages
	 *            The isManyMessages to set.
	 */
	public void setIsManyMessages(String isManyMessages) {
		this.isManyMessages = isManyMessages;
	}

	/**
	 * @return Returns the noOfNotesAttached.
	 */
	public Integer getNoOfNotesAttached() {
		return noOfNotesAttached;
	}

	/**
	 * @param noOfNotesAttached
	 *            The noOfNotesAttached to set.
	 */
	public void setNoOfNotesAttached(Integer noOfNotesAttached) {
		this.noOfNotesAttached = noOfNotesAttached;
	}

	/**
	 * @return Returns the positionLevelName.
	 */
	public String getPositionLevelName() {
		return positionLevelName;
	}

	/**
	 * @param positionLevelName
	 *            The positionLevelName to set.
	 */
	public void setPositionLevelName(String positionLevelName) {
		this.positionLevelName = positionLevelName;
	}

	private String accountClass;

	/**
	 * @return Returns the accountClass.
	 */
	public String getAccountClass() {
		return accountClass;
	}

	/**
	 * @param accountClass
	 *            The accountClass to set.
	 */
	public void setAccountClass(String accountClass) {
		this.accountClass = accountClass;
	}

	/**
	 * private object of inner clasas
	 */
	private Id id = new Id();

	/**
	 * @return Returns the inputRole.
	 */
	public String getInputRole() {
		return inputRole;
	}

	/**
	 * @param inputRole
	 *            The inputRole to set.
	 */
	public void setInputRole(String inputRole) {
		this.inputRole = inputRole;
	}

	/**
	 * @return Returns the inputUser.
	 */
	public String getInputUser() {
		return inputUser;
	}

	/**
	 * @param inputUser
	 *            The inputUser to set.
	 */
	public void setInputUser(String inputUser) {
		this.inputUser = inputUser;
	}

	/**
	 * @return Returns the account.
	 */
	public AcctMaintenance getAccount() {
		return account;
	}

	/**
	 * @param account
	 *            The account to set.
	 */
	public void setAccount(AcctMaintenance account) {
		this.account = account;
	}

	/**
	 * @return Returns the accttype.
	 */
	public String getAccttype() {
		return accttype;
	}

	/**
	 * @param accttype
	 *            The accttype to set.
	 */
	public void setAccttype(String accttype) {
		this.accttype = accttype;
	}

	// Code Ends Added By Tapan

	/**
	 * @return Returns the initialPredStatus.
	 */
	public String getInitialPredStatus() {
		return initialPredStatus;
	}

	/**
	 * @param initialPredStatus
	 *            The initialPredStatus to set.
	 */
	public void setInitialPredStatus(String initialPredStatus) {
		this.initialPredStatus = initialPredStatus;
	}

	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}

	/**
	 * @param accountId
	 *            The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return Returns the amount.
	 */
	public Double getAmount() {
		return amount;
	}

	/**
	 * @param amount
	 *            The amount to set.
	 */
	public void setAmount(Double amount) {
		this.amount = amount;
	}

	/**
	 * @return Returns the beneficiaryId.
	 */
	public String getBeneficiaryId() {
		return beneficiaryId;
	}

	/**
	 * @param beneficiaryId
	 *            The beneficiaryId to set.
	 */
	public void setBeneficiaryId(String beneficiaryId) {
		this.beneficiaryId = beneficiaryId;
	}

	/**
	 * @return Returns the beneficiaryText1.
	 */
	public String getBeneficiaryText1() {
		return beneficiaryText1;
	}

	/**
	 * @return Returns the amountoverasstring.
	 */
	public String getAmountoverasstring() {
		return amountoverasstring;
	}

	/**
	 * @param amountoverasstring
	 *            The amountoverasstring to set.
	 */
	public void setAmountoverasstring(String amountoverasstring) {
		this.amountoverasstring = amountoverasstring;
	}

	/**
	 * @return Returns the amountunderasstring.
	 */
	public String getAmountunderasstring() {
		return amountunderasstring;
	}

	/**
	 * @param amountunderasstring
	 *            The amountunderasstring to set.
	 */
	public void setAmountunderasstring(String amountunderasstring) {
		this.amountunderasstring = amountunderasstring;
	}

	/**
	 * @param beneficiaryText1
	 *            The beneficiaryText1 to set.
	 */
	public void setBeneficiaryText1(String beneficiaryText1) {
		this.beneficiaryText1 = beneficiaryText1;
	}

	/**
	 * @return Returns the beneficiaryText2.
	 */
	public String getBeneficiaryText2() {
		return beneficiaryText2;
	}

	/**
	 * @param beneficiaryText2
	 *            The beneficiaryText2 to set.
	 */
	public void setBeneficiaryText2(String beneficiaryText2) {
		this.beneficiaryText2 = beneficiaryText2;
	}

	/**
	 * @return Returns the beneficiaryText3.
	 */
	public String getBeneficiaryText3() {
		return beneficiaryText3;
	}

	/**
	 * @return Returns the inputDateAsString.
	 */
	public String getInputDateAsString() {
		return inputDateAsString;
	}

	/**
	 * @param inputDateAsString
	 *            The inputDateAsString to set.
	 */
	public void setInputDateAsString(String inputDateAsString) {
		this.inputDateAsString = inputDateAsString;
	}

	/**
	 * @return Returns the valueFromDate.
	 */
	public Date getValueFromDate() {
		return valueFromDate;
	}

	/**
	 * @param valueFromDate
	 *            The valueFromDate to set.
	 */
	public void setValueFromDate(Date valueFromDate) {
		this.valueFromDate = valueFromDate;
	}

	/**
	 * @return Returns the valueFromDateAsString.
	 */
	public String getValueFromDateAsString() {
		return valueFromDateAsString;
	}

	/**
	 * @param valueFromDateAsString
	 *            The valueFromDateAsString to set.
	 */
	public void setValueFromDateAsString(String valueFromDateAsString) {
		this.valueFromDateAsString = valueFromDateAsString;
	}

	/**
	 * @return Returns the valueToDate.
	 */
	public Date getValueToDate() {
		return valueToDate;
	}

	/**
	 * @param valueToDate
	 *            The valueToDate to set.
	 */
	public void setValueToDate(Date valueToDate) {
		this.valueToDate = valueToDate;
	}

	/**
	 * @return Returns the valueToDateAsString.
	 */
	public String getValueToDateAsString() {
		return valueToDateAsString;
	}

	/**
	 * @param valueToDateAsString
	 *            The valueToDateAsString to set.
	 */
	public void setValueToDateAsString(String valueToDateAsString) {
		this.valueToDateAsString = valueToDateAsString;
	}

	/**
	 * @return Returns the amountunder.
	 */
	public Double getAmountunder() {
		return amountunder;
	}

	/**
	 * @param amountunder
	 *            The amountunder to set.
	 */
	public void setAmountunder(Double amountunder) {
		this.amountunder = amountunder;
	}

	/**
	 * @return Returns the group.
	 */
	public String getGroup() {
		return group;
	}

	/**
	 * @param group
	 *            The group to set.
	 */
	public void setGroup(String group) {
		this.group = group;
	}

	/**
	 * @return Returns the metaGroup.
	 */
	public String getMetaGroup() {
		return metaGroup;
	}

	/**
	 * @param metaGroup
	 *            The metaGroup to set.
	 */
	public void setMetaGroup(String metaGroup) {
		this.metaGroup = metaGroup;
	}

	/**
	 * @return Returns the reference.
	 */
	public String getReference() {
		return reference;
	}

	/**
	 * @param reference
	 *            The reference to set.
	 */
	public void setReference(String reference) {
		this.reference = reference;
	}

	/**
	 * @return Returns the sortorder.
	 */
	public String getSortorder() {
		return sortorder;
	}

	/**
	 * @param sortorder
	 *            The sortorder to set.
	 */
	public void setSortorder(String sortorder) {
		this.sortorder = sortorder;
	}

	/**
	 * @return Returns the amountover.
	 */
	public Double getAmountover() {
		return amountover;
	}

	/**
	 * @param amountover
	 *            The amountover to set.
	 */
	public void setAmountover(Double amountover) {
		this.amountover = amountover;
	}

	/**
	 * @param beneficiaryText3
	 *            The beneficiaryText3 to set.
	 */
	public void setBeneficiaryText3(String beneficiaryText3) {
		this.beneficiaryText3 = beneficiaryText3;
	}

	/**
	 * @return Returns the beneficiaryText4.
	 */
	public String getBeneficiaryText4() {
		return beneficiaryText4;
	}

	/**
	 * @param beneficiaryText4
	 *            The beneficiaryText4 to set.
	 */
	public void setBeneficiaryText4(String beneficiaryText4) {
		this.beneficiaryText4 = beneficiaryText4;
	}

	/**
	 * @return Returns the bookCode.
	 */
	public String getBookCode() {
		return bookCode;
	}

	/**
	 * @param bookCode
	 *            The bookCode to set.
	 */
	public void setBookCode(String bookCode) {
		this.bookCode = bookCode;
	}

	/**
	 * @return Returns the bookCodeAvail.
	 */
	public String getBookCodeAvail() {
		return bookCodeAvail;
	}

	/**
	 * @param bookCodeAvail
	 *            The bookCodeAvail to set.
	 */
	public void setBookCodeAvail(String bookCodeAvail) {
		this.bookCodeAvail = bookCodeAvail;
	}

	/**
	 * @return Returns the counterPartyText1.
	 */
	public String getCounterPartyText1() {
		return counterPartyText1;
	}

	/**
	 * @param counterPartyText1
	 *            The counterPartyText1 to set.
	 */
	public void setCounterPartyText1(String counterPartyText1) {
		this.counterPartyText1 = counterPartyText1;
	}

	/**
	 * @return Returns the counterPartyText2.
	 */
	public String getCounterPartyText2() {
		return counterPartyText2;
	}

	/**
	 * @param counterPartyText2
	 *            The counterPartyText2 to set.
	 */
	public void setCounterPartyText2(String counterPartyText2) {
		this.counterPartyText2 = counterPartyText2;
	}

	/**
	 * @return Returns the counterPartyText3.
	 */
	public String getCounterPartyText3() {
		return counterPartyText3;
	}

	/**
	 * @param counterPartyText3
	 *            The counterPartyText3 to set.
	 */
	public void setCounterPartyText3(String counterPartyText3) {
		this.counterPartyText3 = counterPartyText3;
	}

	/**
	 * @return Returns the counterPartyText4.
	 */
	public String getCounterPartyText4() {
		return counterPartyText4;
	}

	/**
	 * @param counterPartyText4
	 *            The counterPartyText4 to set.
	 */
	public void setCounterPartyText4(String counterPartyText4) {
		this.counterPartyText4 = counterPartyText4;
	}

	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}

	/**
	 * @param currencyCode
	 *            The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	/**
	 * @return Returns the custodianId.
	 */
	public String getCustodianId() {
		return custodianId;
	}

	/**
	 * @param custodianId
	 *            The custodianId to set.
	 */
	public void setCustodianId(String custodianId) {
		this.custodianId = custodianId;
	}

	/**
	 * @return Returns the custodianText1.
	 */
	public String getCustodianText1() {
		return custodianText1;
	}

	/**
	 * @param custodianText1
	 *            The custodianText1 to set.
	 */
	public void setCustodianText1(String custodianText1) {
		this.custodianText1 = custodianText1;
	}

	/**
	 * @return Returns the custodianText2.
	 */
	public String getCustodianText2() {
		return custodianText2;
	}

	/**
	 * @param custodianText2
	 *            The custodianText2 to set.
	 */
	public void setCustodianText2(String custodianText2) {
		this.custodianText2 = custodianText2;
	}

	/**
	 * @return Returns the custodianText3.
	 */
	public String getCustodianText3() {
		return custodianText3;
	}

	/**
	 * @param custodianText3
	 *            The custodianText3 to set.
	 */
	public void setCustodianText3(String custodianText3) {
		this.custodianText3 = custodianText3;
	}

	/**
	 * @return Returns the custodianText4.
	 */
	public String getCustodianText4() {
		return custodianText4;
	}

	/**
	 * @param custodianText4
	 *            The custodianText4 to set.
	 */
	public void setCustodianText4(String custodianText4) {
		this.custodianText4 = custodianText4;
	}

	/**
	 * @return Returns the extractStatus.
	 */
	public String getExtractStatus() {
		return extractStatus;
	}

	/**
	 * @param extractStatus
	 *            The extractStatus to set.
	 */
	public void setExtractStatus(String extractStatus) {
		this.extractStatus = extractStatus;
	}

	/**
	 * @return Returns the inputDate.
	 */
	public Date getInputDate() {
		return inputDate;
	}

	/**
	 * @param inputDate
	 *            The inputDate to set.
	 */
	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	/**
	 * @return Returns the inputSource.
	 */
	public String getInputSource() {
		return inputSource;
	}

	/**
	 * @param inputSource
	 *            The inputSource to set.
	 */
	public void setInputSource(String inputSource) {
		this.inputSource = inputSource;
	}

	/**
	 * @return Returns the matchId.
	 */
	public Long getMatchId() {
		return matchId;
	}

	/**
	 * @param matchId
	 *            The matchId to set.
	 */
	public void setMatchId(Long matchId) {
		this.matchId = matchId;
	}

	/**
	 * @return Returns the matchStatus.
	 */
	public String getMatchStatus() {
		return matchStatus;
	}

	/**
	 * @param matchStatus
	 *            The matchStatus to set.
	 */
	public void setMatchStatus(String matchStatus) {
		this.matchStatus = matchStatus;
	}

	/**
	 * @return Returns the messageFormat.
	 */
	public String getMessageFormat() {
		return messageFormat;
	}

	/**
	 * @param messageFormat
	 *            The messageFormat to set.
	 */
	public void setMessageFormat(String messageFormat) {
		this.messageFormat = messageFormat;
	}

	/**
	 * @return Returns the messageId.
	 */
	public String getMessageId() {
		return messageId;
	}

	/**
	 * @param messageId
	 *            The messageId to set.
	 */
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	/**
	 * @return Returns the movementType.
	 */
	public String getMovementType() {
		return movementType;
	}

	/**
	 * @param movementType
	 *            The movementType to set.
	 */
	public void setMovementType(String movementType) {
		this.movementType = movementType;
	}

	/**
	 * @return Returns the counterPartyId.
	 */
	public String getCounterPartyId() {
		return counterPartyId;
	}

	/**
	 * @param counterPartyId
	 *            The counterPartyId to set.
	 */
	public void setCounterPartyId(String counterPartyId) {
		this.counterPartyId = counterPartyId;
	}

	/**
	 * @return Returns the positionLevel.
	 */
	public Integer getPositionLevel() {
		return positionLevel;
	}

	/**
	 * @param positionLevel
	 *            The positionLevel to set.
	 */
	public void setPositionLevel(Integer positionLevel) {
		this.positionLevel = positionLevel;
	}

	/**
	 * @return Returns the predictStatus.
	 */
	public String getPredictStatus() {
		return predictStatus;
	}

	/**
	 * @param predictStatus
	 *            The predictStatus to set.
	 */
	public void setPredictStatus(String predictStatus) {
		this.predictStatus = predictStatus;
	}

	/**
	 * @return Returns the reference1.
	 */
	public String getReference1() {
		return reference1;
	}

	/**
	 * @param reference1
	 *            The reference1 to set.
	 */
	public void setReference1(String reference1) {
		this.reference1 = reference1;
	}

	/**
	 * @return Returns the reference2.
	 */
	public String getReference2() {
		return reference2;
	}

	/**
	 * @param reference2
	 *            The reference2 to set.
	 */
	public void setReference2(String reference2) {
		this.reference2 = reference2;
	}

	/**
	 * @return Returns the reference3.
	 */
	public String getReference3() {
		return reference3;
	}

	/**
	 * @param reference3
	 *            The reference3 to set.
	 */
	public void setReference3(String reference3) {
		this.reference3 = reference3;
	}

	/**
	 * @return Returns the reference4.
	 */
	public String getReference4() {
		return reference4;
	}

	/**
	 * @param reference4
	 *            The reference4 to set.
	 */
	public void setReference4(String reference4) {
		this.reference4 = reference4;
	}

	/**
	 * @return Returns the sign.
	 */
	public String getSign() {
		return sign;
	}

	/**
	 * @param sign
	 *            The sign to set.
	 */
	public void setSign(String sign) {
		this.sign = sign;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the valueDate.
	 */
	public Date getValueDate() {
		return valueDate;
	}

	/**
	 * @param valueDate
	 *            The valueDate to set.
	 */
	public void setValueDate(Date valueDate) {
		this.valueDate = valueDate;
	}

	/**
	 * @return Returns the entityName.
	 */
	public String getEntityName() {
		return entityName;
	}

	/**
	 * @param entityName
	 *            The entityName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}

	/**
	 * @return Returns the valueDateAsString.
	 */
	public String getValueDateAsString() {
		return valueDateAsString;
	}

	/**
	 * @param valueDateAsString
	 *            The valueDateAsString to set.
	 */
	public void setValueDateAsString(String valueDateAsString) {
		this.valueDateAsString = valueDateAsString;
	}

	/**
	 * @return Returns the updateDateAsString.
	 */
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}

	/**
	 * @param updateDateAsString
	 *            The updateDateAsString to set.
	 */
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}

	/**
	 * @return Returns the currName.
	 */
	public String getCurrName() {
		return currName;
	}

	/**
	 * @param currName
	 *            The currName to set.
	 */
	public void setCurrName(String currName) {
		this.currName = currName;
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the amountAsString.
	 */
	public String getAmountAsString() {
		return amountAsString;
	}

	/**
	 * @param amountAsString
	 *            The amountAsString to set.
	 */
	public void setAmountAsString(String amountAsString) {
		this.amountAsString = amountAsString;
	}

	/**
	 * @return Returns the matchStatusDesc.
	 */
	public String getMatchStatusDesc() {
		return matchStatusDesc;
	}

	/**
	 * @param matchStatusDesc
	 *            The matchStatusDesc to set.
	 */
	public void setMatchStatusDesc(String matchStatusDesc) {
		this.matchStatusDesc = matchStatusDesc;
	}

	/**
	 * @return Returns the acctstatusflg.
	 */
	public String getAcctstatusflg() {
		return acctstatusflg;
	}

	/**
	 * @param acctstatusflg
	 *            The acctstatusflg to set.
	 */
	public void setAcctstatusflg(String acctstatusflg) {
		this.acctstatusflg = acctstatusflg;
	}

	/**
	 * @return Returns the mansweepflg.
	 */
	public String getMansweepflg() {
		return mansweepflg;
	}

	/**
	 * @param mansweepflg
	 *            The mansweepflg to set.
	 */
	public void setMansweepflg(String mansweepflg) {
		this.mansweepflg = mansweepflg;
	}

	/**
	 * @return Returns the source.
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source
	 *            The source to set.
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return Returns the access.
	 */
	public String getAccess() {
		return access;
	}

	/**
	 * @param access
	 *            The access to set.
	 */
	public void setAccess(String access) {
		this.access = access;
	}

	/**
	 * @return Returns the positionLevelAsString.
	 */
	public String getPositionLevelAsString() {
		return positionLevelAsString;
	}

	/**
	 * @param positionLevelAsString
	 *            The positionLevelAsString to set.
	 */
	public void setPositionLevelAsString(String positionLevelAsString) {
		this.positionLevelAsString = positionLevelAsString;
	}

	/**
	 * @return Returns the timefrom.
	 */
	public String getTimefrom() {
		return timefrom;
	}

	/**
	 * @param timefrom
	 *            The timefrom to set.
	 */
	public void setTimefrom(String timefrom) {
		this.timefrom = timefrom;
	}

	/**
	 * @return Returns the timeto.
	 */
	public String getTimeto() {
		return timeto;
	}

	/**
	 * @param timeto
	 *            The timeto to set.
	 */
	public void setTimeto(String timeto) {
		this.timeto = timeto;
	}

	/**
	 * @return Returns the hasNotes.
	 */
	public String getHasNotes() {
		return hasNotes;
	}

	/**
	 * @param hasNotes
	 *            The hasNotes to set.
	 */
	public void setHasNotes(String hasNotes) {
		this.hasNotes = hasNotes;
	}

	/**
	 * @return Returns the updateTimeAsString.
	 */
	public String getUpdateTimeAsString() {
		return updateTimeAsString;
	}

	/**
	 * @param updateTimeAsString
	 *            The updateTimeAsString to set.
	 */
	public void setUpdateTimeAsString(String updateTimeAsString) {
		this.updateTimeAsString = updateTimeAsString;
	}

	public String getFailingCause() {
		return failingCause;
	}

	public void setFailingCause(String failingCause) {
		this.failingCause = failingCause;
	}

	public boolean isSuccessful() {
		return successful;
	}

	public void setSuccessful(boolean successful) {
		this.successful = successful;
	}

	public static class Id extends BaseObject {
		private String hostId;
		private String entityId;
		private Long movementId;
		private String movementIdAsString;

		/**
		 * @param hostId
		 * @param entityId
		 * @param movementId
		 * @param movementIdAsString
		 */
		public Id(String hostId, String entityId, Long movementId,
				String movementIdAsString) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.movementId = movementId;
			this.movementIdAsString = movementIdAsString;
		}

		/**
		 * 
		 */
		public Id() {
			super();

			// TODO Auto-generated constructor stub
		}

		/**
		 * @return Returns the movementIdAsString.
		 */
		public String getMovementIdAsString() {
			return movementIdAsString;
		}

		/**
		 * @param movementIdAsString
		 *            The movementIdAsString to set.
		 */
		public void setMovementIdAsString(String movementIdAsString) {
			this.movementIdAsString = movementIdAsString;
		}

		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return Returns the movementId.
		 */
		public Long getMovementId() {
			return movementId;
		}

		/**
		 * @param movementId
		 *            The movementId to set.
		 */
		public void setMovementId(Long movementId) {
			this.movementId = movementId;
		}
	}

	/**
	 * @return Returns the newValueDate.
	 */
	public String getNewValueDate() {
		return newValueDate;
	}

	/**
	 * @param newValueDate
	 *            The newValueDate to set.
	 */
	public void setNewValueDate(String newValueDate) {
		this.newValueDate = newValueDate;
	}

	/**
	 * 
	 * @return currencyGrpId
	 */
	public String getCurrencyGrpId() {
		return currencyGrpId;
	}

	/**
	 * 
	 * @param currencyGrpId -
	 *            currencyGrpId
	 */
	public void setCurrencyGrpId(String currencyGrpId) {
		this.currencyGrpId = currencyGrpId;
	}

	/**
	 * @return Returns the archiveId.
	 */
	public String getArchiveId() {
		return archiveId;
	}

	/**
	 * @param archiveId
	 *            The archiveId to set.
	 */
	public void setArchiveId(String archiveId) {
		this.archiveId = archiveId;
	}

	/**
	 * @return Returns the notesCount.
	 */
	public Integer getNotesCount() {
		return notesCount;
	}

	/**
	 * @param notesCount
	 *            The notesCount to set.
	 */
	public void setNotesCount(Integer notesCount) {
		this.notesCount = notesCount;
	}

	/**
	 * @return Returns the openFlag.
	 */
	public String getOpenFlag() {
		return openFlag;
	}

	/**
	 * @param openFlag
	 *            The openFlag to set.
	 */
	public void setOpenFlag(String openFlag) {
		this.openFlag = openFlag;
	}

	/* START: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */
	/**
	 * @return Returns the applyCurrencyThreshold.
	 */
	public String getApplyCurrencyThreshold() {
		return applyCurrencyThreshold;
	}

	/**
	 * @param applyCurrencyThreshold
	 *            The applyCurrencyThreshold to set.
	 */
	public void setApplyCurrencyThreshold(String applyCurrencyThreshold) {
		this.applyCurrencyThreshold = applyCurrencyThreshold;
	}

	/* END: Code changed as par SRS - Currency Threshold for ING, 09-JUL-2007 */

	/*
	 * START: Code changed as par SRS - External Balance Calculation for ING,
	 * 11-JUL-2007
	 */
	/**
	 * @return Returns the extBalStatus.
	 */
	public String getExtBalStatus() {
		return extBalStatus;
	}

	/**
	 * @param extBalStatus
	 *            The extBalStatus to set.
	 */
	public void setExtBalStatus(String extBalStatus) {
		this.extBalStatus = extBalStatus;
	}

	/*
	 * END: Code changed as par SRS - External Balance Calculation for ING,
	 * 11-JUL-2007
	 */

	/**
	 * @return Returns the toMatch.
	 */
	public String getToMatch() {
		return toMatch;
	}

	/**
	 * @param toMatch
	 *            The toMatch to set.
	 */
	public void setToMatch(String toMatch) {
		this.toMatch = toMatch;
	}

	public String getReferenceflag() {
		return referenceflag;
	}

	public void setReferenceflag(String referenceflag) {
		this.referenceflag = referenceflag;
	}

	/*
	 * Start: modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings,
	 * searching movements restricted to Todays Date"
	 */
	public String getOpenMovementFlag() {
		return openMovementFlag;
	}

	public void setOpenMovementFlag(String openMovementFlag) {
		this.openMovementFlag = openMovementFlag;
	}

	/*
	 * End: modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings,
	 * searching movements restricted to Todays Date"
	 */

	/**
	 * @return Returns the Updated_Date.
	 */
	public Date getUpDate_Date() {
		return upDate_Date;
	}

	/**
	 * @param UpDate_Date
	 *            The UpDate_Date to set.
	 */
	public void setUpDate_Date(Date upDate_Date) {
		this.upDate_Date = upDate_Date;
	}

	/**
	 * @return Returns the MatchingParty.
	 */
	public String getMatchingParty() {
		return matchingParty;
	}

	/**
	 * @param MatchingParty
	 *            The MatchingParty to set.
	 */
	public void setMatchingParty(String matchingParty) {
		this.matchingParty = matchingParty;
	}

	/**
	 * @return Returns the ProductType.
	 */
	public String getProductType() {
		return productType;
	}

	/**
	 * @param ProductType
	 *            The ProductType to set.
	 */
	public void setProductType(String productType) {
		this.productType = productType;
	}

	/**
	 * @return Returns the PostingDate.
	 */
	public Date getPostingDate() {
		return postingDate;
	}

	/**
	 * @param PostingDate
	 *            The PostingDate to set.
	 */
	public void setPostingDate(Date postingDate) {
		this.postingDate = postingDate;
	}

	/**
	 * @return Returns the PostingDateAsString.
	 */
	public String getPostingDateAsString() {
		return postingDateAsString;
	}

	/**
	 * @param PostingDateAsString
	 *            The PostingDateAsString to set.
	 */
	public void setPostingDateAsString(String postingDateAsString) {
		this.postingDateAsString = postingDateAsString;
	}

	/**
	 * @return Returns the PostingFromDate.
	 */
	public Date getPostingFromDate() {
		return postingFromDate;
	}

	/**
	 * @param PostingFromDate
	 *            The PostingFromDate to set.
	 */
	public void setPostingFromDate(Date postingFromDate) {
		this.postingFromDate = postingFromDate;
	}

	/**
	 * @return Returns the PostingFromDateAsString.
	 */
	public String getPostingFromDateAsString() {
		return postingFromDateAsString;
	}

	/**
	 * @param PostingFromDateAsString
	 *            The PostingFromDateAsString to set.
	 */
	public void setPostingFromDateAsString(String postingFromDateAsString) {
		this.postingFromDateAsString = postingFromDateAsString;
	}

	/**
	 * @return Returns the PostingToDate.
	 */
	public Date getPostingToDate() {
		return postingToDate;
	}

	/**
	 * @param PostingToDate
	 *            The PostingToDate to set.
	 */
	public void setPostingToDate(Date postingToDate) {
		this.postingToDate = postingToDate;
	}

	/**
	 * @return Returns the PostingToDateAsString.
	 */
	public String getPostingToDateAsString() {
		return postingToDateAsString;
	}

	/**
	 * @param PostingToDateAsString
	 *            The PostingToDateAsString to set.
	 */
	public void setPostingToDateAsString(String postingToDateAsString) {
		this.postingToDateAsString = postingToDateAsString;
	}
	/**
	 * 
	 * @return expectedSettlementDateTime
	 */
	public Date getExpectedSettlementDateTime() {
		return expectedSettlementDateTime;
	}
	/**
	 * 
	 * @param expectedSettlementDateTime
	 */
	public void setExpectedSettlementDateTime(Date expectedSettlementDateTime) {
		this.expectedSettlementDateTime = expectedSettlementDateTime;
	}
	/**
	 * 
	 * @return SettlementDateTime
	 */
	public Date getSettlementDateTime() {
		return settlementDateTime;
	}
	/**
	 * 
	 * @param settlementDateTime
	 */
	public void setSettlementDateTime(Date settlementDateTime) {
		this.settlementDateTime = settlementDateTime;
	}
	/**
	 * 
	 * @return criticalPaymentType
	 */
	public String getCriticalPaymentType() {
		return criticalPaymentType;
	}
	/**
	 * 
	 * @param criticalPaymentType
	 */
	public void setCriticalPaymentType(String criticalPaymentType) {
		this.criticalPaymentType = criticalPaymentType;
	}
	/**
	 * 
	 * @return expectedSettlementDateTimeAsString
	 */
	public String getExpectedSettlementDateTimeAsString() {
		return expectedSettlementDateTimeAsString;
	}
	/**
	 * 
	 * @param expectedSettlementDateTimeAsString
	 */
	public void setExpectedSettlementDateTimeAsString(
			String expectedSettlementDateTimeAsString) {
		this.expectedSettlementDateTimeAsString = expectedSettlementDateTimeAsString;
	}
	/**
	 * 
	 * @return SettlementDateTimeAsString
	 */
	public String getSettlementDateTimeAsString() {
		return settlementDateTimeAsString;
	}
	/**
	 * 
	 * @param settlementDateTimeAsString
	 */
	public void setSettlementDateTimeAsString(String settlementDateTimeAsString) {
		this.settlementDateTimeAsString = settlementDateTimeAsString;
	}



	public String getCounterPartyText5() {
		return counterPartyText5;
	}



	public void setCounterPartyText5(String counterPartyText5) {
		this.counterPartyText5 = counterPartyText5;
	}



	public String getBeneficiaryText5() {
		return beneficiaryText5;
	}



	public void setBeneficiaryText5(String beneficiaryText5) {
		this.beneficiaryText5 = beneficiaryText5;
	}



	public String getCustodianText5() {
		return custodianText5;
	}



	public void setCustodianText5(String custodianText5) {
		this.custodianText5 = custodianText5;
	}



	public String getOrderingCustomerId() {
		return orderingCustomerId;
	}



	public void setOrderingCustomerId(String orderingCustomerId) {
		this.orderingCustomerId = orderingCustomerId;
	}



	public String getOrderingInstitutionId() {
		return orderingInstitutionId;
	}



	public void setOrderingInstitutionId(String orderingInstitutionId) {
		this.orderingInstitutionId = orderingInstitutionId;
	}



	public String getSenderCorrespondentId() {
		return senderCorrespondentId;
	}



	public void setSenderCorrespondentId(String senderCorrespondentId) {
		this.senderCorrespondentId = senderCorrespondentId;
	}



	public String getReceiverCorrespondentId() {
		return receiverCorrespondentId;
	}



	public void setReceiverCorrespondentId(String receiverCorrespondentId) {
		this.receiverCorrespondentId = receiverCorrespondentId;
	}



	public String getIntermediaryInstitutionId() {
		return intermediaryInstitutionId;
	}



	public void setIntermediaryInstitutionId(String intermediaryInstitutionId) {
		this.intermediaryInstitutionId = intermediaryInstitutionId;
	}



	public String getAccountWithInstitutionId() {
		return accountWithInstitutionId;
	}



	public void setAccountWithInstitutionId(String accountWithInstitutionId) {
		this.accountWithInstitutionId = accountWithInstitutionId;
	}



	public String getBeneficiaryCustomerId() {
		return beneficiaryCustomerId;
	}



	public void setBeneficiaryCustomerId(String beneficiaryCustomerId) {
		this.beneficiaryCustomerId = beneficiaryCustomerId;
	}



	public MovementExt getMovementExt() {
		return movementExt;
	}



	public void setMovementExt(MovementExt movementExt) {
		this.movementExt = movementExt;
	}



	public String getAlerting() {
		return alerting;
	}



	public void setAlerting(String alerting) {
		this.alerting = alerting;
	}
	
	
	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}



	public String getAgainstAccountEntityId() {
		return againstAccountEntityId;
	}



	public void setAgainstAccountEntityId(String againstAccountEntityId) {
		this.againstAccountEntityId = againstAccountEntityId;
	}



	public String getExtraText1() {
		return extraText1;
	}



	public void setExtraText1(String extraText1) {
		this.extraText1 = extraText1;
	}



	public String getMatchcenarioHighlighted() {
		return matchcenarioHighlighted;
	}



	public void setMatchcenarioHighlighted(String matchcenarioHighlighted) {
		this.matchcenarioHighlighted = matchcenarioHighlighted;
	}



	public String getIlmFcastStatus() {
		return ilmFcastStatus;
	}



	public void setIlmFcastStatus(String ilmFcastStatus) {
		this.ilmFcastStatus = ilmFcastStatus;
	}



	public String getAttributeXml() {
		return attributeXml;
	}

	public void setAttributeXml(String attributeXml) {
		this.attributeXml = attributeXml;
	}



	public String getUetr() {
		return uetr;
	}



	public void setUetr(String uetr) {
		this.uetr = uetr;
	}





	
}
