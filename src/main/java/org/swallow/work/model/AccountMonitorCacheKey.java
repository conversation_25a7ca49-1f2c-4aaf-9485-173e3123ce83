/*
 * @(#) AccountMonitorCacheKey.java 1.0 05/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.model;
import java.io.Serializable;
import java.util.Date;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
*This is a POJO for storing key objects in the cache.
*/

public class AccountMonitorCacheKey implements Serializable {
	
	private final Log log = LogFactory.getLog(AccountMonitorCacheKey.class);
	
	private String hostId;
	private String entityId;
	private String currencyCode;
	private String accountType;
	private String accountId;
	private Date date;
		
	
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	/**
	 * @return Returns the accountType.
	 */
	public String getAccountType() {
		return accountType;
	}
	/**
	 * @param accountType The accountType to set.
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	/**
	 * @return Returns the currencyCode.
	 */
	public String getCurrencyCode() {
		return currencyCode;
	}
	/**
	 * @param currencyCode The currencyCode to set.
	 */
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	/**
	 * @return Returns the date.
	 */
	public Date getDate() {
		return date;
	}
	/**
	 * @param date The date to set.
	 */
	public void setDate(Date date) {
		this.date = date;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	
	/**
	 * Overriding the default equals function
	 */	
	public boolean equals(Object obj) {
		
        log.debug("Entering equals method");
        
        boolean retValue = false;
        log.debug("obj - " + obj);
        if (obj != null)
        	log.debug("Object class name - " + obj.getClass().getName());
        if ((obj != null) && obj instanceof AccountMonitorCacheKey) {
            log.debug("obj to be compared - " + obj);
            log.debug("this obj - " + this.toString());
            log.debug("hashcode of obj to be compared " + obj.hashCode()  + "hashcode of this obj  " + this.hashCode());            
            retValue = hostId.equals(((AccountMonitorCacheKey) obj).getHostId()) 
						&& entityId.equals(((AccountMonitorCacheKey) obj).getEntityId())
						&& currencyCode.equals(((AccountMonitorCacheKey) obj).getCurrencyCode())		
						&& accountType.equals(((AccountMonitorCacheKey) obj).getAccountType())
						&& accountId.equals(((AccountMonitorCacheKey) obj).getAccountId())
						&& date.equals(((AccountMonitorCacheKey) obj).getDate());
        }

        log.debug("retValue - " + retValue);
        log.debug("Exiting equals method");

        return retValue;
    }
	
	/**
	 * Overriding the default hashCode function
	 * @return - hashCode object;
	 */
	 public int hashCode() {
	  	/*int hashCode = hostId.hashCode() + entityId.hashCode() + currencyCode.hashCode() + accountType.hashCode() 
		   + accountId.hashCode() +  date.hashCode();
	  	log.debug("hashCode - " + hashCode);
        return  hashCode;*/
	 	return HashCodeBuilder.reflectionHashCode(this);
	  }
	  
	  public String toString() {
	  	
	  	StringBuffer buf = new StringBuffer();
	  	buf.append("HostId - ")
		   .append(hostId)
		   .append(", EntityId - ")
		   .append(entityId)
		   .append(", CurrencyCode - ")
		   .append(currencyCode)
		   .append(", AccountType - ")
		   .append(accountType)
		   .append(", AccountId - ")
		   .append(accountId)
		   .append(", Date - ")
		   .append(date);
	  	return buf.toString();
	  }
}
