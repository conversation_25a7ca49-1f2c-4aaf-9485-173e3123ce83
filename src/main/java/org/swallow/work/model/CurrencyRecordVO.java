/*
 * @(#)CurrencyRecordVO.java 1.0 / Dec 19, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.util.Collection;

import org.swallow.work.web.form.BalanceDateTO;
import org.swallow.work.web.form.CurrencyRecord;

/**
 * CurrencyRecordVO.java
 * 
 * This java bean has getters and setters for currency balance details
 * 
 * <AUTHOR> R / Dec 19, 2011
 * @version SmartPredict-1054
 */
public class CurrencyRecordVO implements Serializable{

	// To hold grid header details.
	private Collection<BalanceDateTO> headerDetails = null;
	// To hold currency based predicted/loro balances.
	private Collection<CurrencyRecord> balanceDetails = null;
	// To hold sum of predicted/loro balances.
	private CurrencyRecord balanceTotal = null;
	// Flag denotes whether the data building job is running or not
	private boolean jobRunning;

	/**
	 * Getter method of headerDetails
	 * 
	 * @return the headerDetails
	 */
	public Collection<BalanceDateTO> getHeaderDetails() {
		return headerDetails;
	}

	/**
	 * Setter method of headerDetails
	 * 
	 * @param headerDetails
	 *            the headerDetails to set
	 */
	public void setHeaderDetails(Collection<BalanceDateTO> headerDetails) {
		this.headerDetails = headerDetails;
	}

	/**
	 * Getter method of balanceDetails
	 * 
	 * @return the balanceDetails
	 */
	public Collection<CurrencyRecord> getBalanceDetails() {
		return balanceDetails;
	}

	/**
	 * Setter method of balanceDetails
	 * 
	 * @param balanceDetails
	 *            the balanceDetails to set
	 */
	public void setBalanceDetails(Collection<CurrencyRecord> balanceDetails) {
		this.balanceDetails = balanceDetails;
	}

	/**
	 * Getter method of balanceTotal
	 * 
	 * @return the balanceTotal
	 */
	public CurrencyRecord getBalanceTotal() {
		return balanceTotal;
	}

	/**
	 * Setter method of balanceTotal
	 * 
	 * @param balanceTotal
	 *            the balanceTotal to set
	 */
	public void setBalanceTotal(CurrencyRecord balanceTotal) {
		this.balanceTotal = balanceTotal;
	}

	/**
	 * Getter method of jobRunning
	 * 
	 * @return the jobRunning
	 */
	public boolean isJobRunning() {
		return jobRunning;
	}

	/**
	 * Setter method of jobRunning
	 * 
	 * @param jobRunning
	 *            the jobRunning to set
	 */
	public void setJobRunning(boolean jobRunning) {
		this.jobRunning = jobRunning;
	}
}