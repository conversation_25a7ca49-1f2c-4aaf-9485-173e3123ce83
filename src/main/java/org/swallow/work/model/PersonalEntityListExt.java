/*
 * @(#)PersonalEntityList.java 1.0 08/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.model;

import java.util.Collection;
import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * PersonalEntityList.java
 * 
 * This java bean has getters and setters for PersonalEntityList details
 * 
 * <AUTHOR>
 * @date Feb 08, 2011
 * 
 */
public class PersonalEntityListExt extends BaseObject implements
		org.swallow.model.AuditComponent {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// Denote id
	private Id id = new Id();
	// Personal Entity Records
	private Collection<PersonalEntityListExt> personalEntityRecords = null;
	// save or update flag
	private String saveorupdate = null;
	

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// host Id
		private String hostId = null;
		// user Id
		private String userId = null;
		// screen Id
		private String screenId = null;
		// entity Id
		private String entityId = null;
		// role Id
		private String roleId = null;
		// Aggregate Entity Id
		private String aggrgEntityId = null;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return the userId
		 */
		public String getUserId() {
			return userId;
		}

		/**
		 * @param userId
		 *            the userId to set
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}

		/**
		 * @return the screenId
		 */
		public String getScreenId() {
			return screenId;
		}

		/**
		 * @param screenId
		 *            the screenId to set
		 */
		public void setScreenId(String screenId) {
			this.screenId = screenId;
		}

		/**
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * @param entityId
		 *            the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * @return the roleId
		 */
		public String getRoleId() {
			return roleId;
		}

		/**
		 * @param roleId
		 *            the roleId to set
		 */
		public void setRoleId(String roleId) {
			this.roleId = roleId;
		}

		/**
		 * @return the aggrgEntityId
		 */
		public String getAggrgEntityId() {
			return aggrgEntityId;
		}

		/**
		 * @param aggrgEntityId the aggrgEntityId to set
		 */
		public void setAggrgEntityId(String aggrgEntityId) {
			this.aggrgEntityId = aggrgEntityId;
		}
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the personalEntityRecords
	 */
	public Collection<PersonalEntityListExt> getPersonalEntityRecords() {
		return personalEntityRecords;
	}

	/**
	 * @param personalEntityRecords
	 *            the personalEntityRecords to set
	 */
	public void setPersonalEntityRecords(
			Collection<PersonalEntityListExt> personalEntityRecords) {
		this.personalEntityRecords = personalEntityRecords;
	}

	/**
	 * @return the saveorupdate
	 */
	public String getSaveorupdate() {
		return saveorupdate;
	}

	/**
	 * @param saveorupdate
	 *            the saveorupdate to set
	 */
	public void setSaveorupdate(String saveorupdate) {
		this.saveorupdate = saveorupdate;
	}

}