/*
 * @(#)ForecastMonitorManager.java 1.0 12/04/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.service;

import java.util.Collection;

import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.model.UserBuckets;
import org.swallow.work.model.UserTemplate;
import org.swallow.work.web.form.ForecastRecord;

/**
 * ForecastMonitorManager.java
 * 
 * ForecastMonitorManager used to get the Forecast Monitor details
 * 
 * <AUTHOR> D
 * @date Apr 12, 2011
 */
public interface ForecastMonitorManager {

	/**
	 * Interface to get forecast monitor details
	 * 
	 * @param String
	 *            currencyCode
	 * @param String
	 *            entityId
	 * @param String
	 *            userId
	 * @param SystemFormats
	 *            format
	 * @param OpTimer
	 *            opTimer
	 * @param boolean
	 *            multiplierFlag
	 * 
	 * @throws SwtException
	 * @returns ForecastRecord object
	 */
	public ForecastRecord getForecastMonitorDetails(String currencyCode,
			String entityId, String userId, SystemFormats format,
			OpTimer opTimer, boolean multiplierFlag) throws SwtException;

	/**
	 * This is used to get the currency group access details
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection<CurrencyGroup>
	 * @throws SwtException
	 */
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException;

	/**
	 * This method is used to persist the user template in the database
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @param isUpdate
	 * @return
	 * @throws SwtException
	 */
	public void saveUserTemplate(UserTemplate userTemplate, boolean isUpdate)
			throws SwtException;

	/**
	 * This method is used to persist the User bucket in the database
	 * 
	 * @param isUpdate
	 * @param UserBuckets
	 *            userBucket
	 * @return
	 * @throws SwtException
	 */
	public void saveUserBucket(UserBuckets userBucket, boolean isUpdate)
			throws SwtException;

	/**
	 * This method is used to persist the scenario data in the database.
	 * 
	 * @param ScenarioData
	 *            scenarioData
	 * @return
	 * @throws SwtException
	 */
	public void saveScenarioData(ScenarioData scenarioData) throws SwtException;

	/**
	 * This method is used to get the user entity list
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection<EntityAccess>
	 * @throws SwtException
	 */
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException;

	/**
	 * This method is used to get the user templates
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserTemplate>
	 * @throws SwtException
	 */
	public Collection<UserTemplate> getUserTemplateList(String hostId,
			String userId) throws SwtException;

	/**
	 * This method is used to get the user buckets
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserBuckets>
	 * @throws SwtException
	 */
	public Collection<UserBuckets> getUserBucketList(String hostId,
			String userId) throws SwtException;

	/**
	 * This method is used to check the entity access
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkEntityAccess(String hostId, String roleId,
			String entityId) throws SwtException;

	/**
	 * This method is used to get the forecast monitor templates
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<ForecastMonitorTemplate>
	 * @throws SwtException
	 */
	public Collection<ForecastMonitorTemplate> getForecastTemplate(
			String hostId, String userId) throws SwtException;

	/**
	 * This method is used to get the forecast assumption
	 * 
	 * @param AssumptionData
	 *            object
	 * @return Collection<AssumptionData>
	 * @throws SwtException
	 */
	public Collection<AssumptionData> getForecastAssumption(
			AssumptionData assumptionData) throws SwtException;

	/**
	 * This method is used to save the forecast assumption details
	 * 
	 * @param AssumptionData
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void saveForecastAssumption(AssumptionData assumptionData)
			throws SwtException;

	/**
	 * This method is used to delete the forecast assumption details
	 * 
	 * @param AssumptionData
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastAssumption(AssumptionData assumptionData)
			throws SwtException;

	/**
	 * This method is used to delete the forecast user template details
	 * 
	 * @param userTemplate
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastUserTemplate(UserTemplate userTemplate)
			throws SwtException;

	/**
	 * This method is used to delete the forecast bucket details
	 * 
	 * @param userBuckets
	 *            object
	 * @param boolean
	 *            isAll
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastBucket(UserBuckets userBuckets, boolean isAll)
			throws SwtException;

	/**
	 * This is used to get the currency codes for all entities based on the
	 * access set by the administrator.<br>
	 * If particular entity has no access, currency code for that entity will
	 * not be shown to the client.<br>
	 * 
	 * @param String
	 *            userId
	 * @return Collection<CurrencyMaster>
	 * @throws SwtException
	 */
	public Collection<CurrencyMaster> getCurrencyForAllEntity(String userId)
			throws SwtException;

	/**
	 * This method is used to get the template id
	 * 
	 * @param ScenarioData
	 *            scenarioData
	 * @return String
	 * @throws SwtException
	 */
	public String getTemplateId(ScenarioData scenarioData) throws SwtException;

	// Start: Code added by Bala for 1053 Beta3 testing issue on 14-Sep-2011 -
	// Main screen should display the Template Id tooltip as Template Name
	/**
	 * This method is used to get the Forecast Monitor template details
	 * 
	 * @param ForecastMonitorTemplate
	 *            forecastMonitorTemplate
	 * @return ForecastMonitorTemplate object
	 * @throws SwtException
	 */
	public ForecastMonitorTemplate getForecastMonitorTemplateDetails(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;
	// End: Code added by Bala for 1053 Beta3 testing issue on 14-Sep-2011 -
	// Main screen should display the Template Id tooltip as Template Name

}
