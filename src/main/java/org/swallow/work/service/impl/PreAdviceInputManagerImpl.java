/*
 * @(#)PreAdviceInputManagerImpl.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.MatchDriverDAO;
import org.swallow.control.dao.SystemAlertMessagesDAO;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.maintenance.model.CurrencyTO;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.Party;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.dao.PreAdviceInputDAO;
import org.swallow.work.dao.SweepAlertDAO;
import org.swallow.work.model.InputAuthoriseSelection;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.Movement;
import org.swallow.work.model.SweepAlert;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.PreAdviceInputManager;

/**
 * This Class is implementation of PreAdviceInputManager that is used in
 * Preadvice Input,Pre-advice Display, Input Authorise and Input Refered
 * 
 */
@Component("preAdviceInputManager")
public class PreAdviceInputManagerImpl extends MovementManagerImpl implements
		PreAdviceInputManager {
	/**
	 * To create a Log factory reference variable.
	 */
	private static final Log log = LogFactory.getLog(MovementManagerImpl.class);

	/**
	 * Default DAO object associated with this managerimpl class
	 */
	@Autowired
	private PreAdviceInputDAO preAdviceInputDAO;

	/**
	 * @param preAdviceInputDAO
	 *            The preAdviceInputDAO to set.
	 */
	public void setPreAdviceInputDAO(PreAdviceInputDAO preAdviceInputDAO) {
		this.preAdviceInputDAO = preAdviceInputDAO;
	}

	/**
	 * This method checks whether the combination of given inputs exist in
	 * MatchDriver table or not.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return MatchDriver
	 * @throws SwtException
	 */
	public MatchDriver existMatchDriver(String hostId, String entityId,
			String currencyCode) throws SwtException {

		log.debug(this.getClass().getName() + " - existMatchDriver () - "
				+ " Entry ");

		Collection col = ((MatchDriverDAO) (SwtUtil.getBean("matchDriverDAO")))
				.getMatchDriverList(hostId, entityId, currencyCode);

		log.debug(this.getClass().getName() + " - existMatchDriver () - "
				+ " Exit ");

		return col.iterator().hasNext() ? (MatchDriver) col.iterator().next()
				: null;
	}

	/**
	 * This method is used to save the Movement object into the database.
	 * 
	 * @param Movement
	 *            movement
	 * @param MatchDriver
	 *            matchDriver
	 * @param String
	 *            matchDriverOperation
	 * @param Collection
	 *            sessionNotesDetails
	 * @param SystemInfo
	 *            systemInfo
	 * @param SystemFormats
	 *            sysFormat
	 * @throws SwtException
	 */
	public String saveMovementDetails(Movement movement,
			MatchDriver matchDriver, String matchDriverOperation,
			Collection sessionNotesDetails, SystemInfo systemInfo,
			SystemFormats sysFormat) throws SwtException {

		log.debug(this.getClass().getName() + " - saveMovementDetails () - "
				+ " Entry ");

		return ((MovementManager) (SwtUtil.getBean("movementManager")))
				.saveMovementDetails(movement, matchDriver,
						matchDriverOperation, sessionNotesDetails, systemInfo,
						sysFormat);
	}

	/**
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            counterPartyId
	 * @return
	 * @throws SwtException
	 */

	public Collection getCounterPartyRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException {

		log.debug(this.getClass().getName() + " - getCounterPartyRecord () - "
				+ " Entry ");

		return ((MovementManager) (SwtUtil.getBean("movementManager")))
				.getCounterPartyRecord(hostId, entityId, counterPartyId);
	}

	/*
	 * Start :code added by Mahesh on 18-Mar-2010 for Mantis 1034: Getting the
	 * Matching party records from the data base
	 */
	/**
	 * This method used to get Matching party records from the data base
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            counterPartyId
	 * @return
	 * @throws SwtException
	 */

	public Collection getMatchingPartyRecord(String hostId, String entityId,
			String matchingPartyId) throws SwtException {

		log.debug(this.getClass().getName() + " - getMatchingPartyRecord() - "
				+ " Entry/Exit ");

		return ((MovementManager) (SwtUtil.getBean("movementManager")))
				.getMatchingPartyRecord(hostId, entityId, matchingPartyId);
	}

	/*
	 * End :code added by Mahesh on 18-Mar-2010 for Mantis 1034: Getting the
	 * Matching party records from the data base
	 */

	/**
	 * This method is used to delete all the movements included into list
	 * MovementId string. It also deletes all the reference data for given
	 * movements.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            movementId is basically list of movement_Ids seperated with
	 *            '|'
	 * @throws SwtException
	 */
	public void deleteMovement(String hostId, String entityId,
			String listMovementId) throws SwtException {

		log.debug(this.getClass().getName() + " - deleteMovement () - "
				+ " Entry ");

		try {
			preAdviceInputDAO.deleteMovement(hostId, entityId, listMovementId);
		} catch (Exception exp) {

			log.debug(this.getClass().getName() + " - deleteMovement () - "
					+ " Exception - " + exp.getMessage());
			log.error(this.getClass().getName() + " - deleteMovement () - "
					+ " Exception - " + exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMovement", PreAdviceInputManagerImpl.class);
		}

		log.debug(this.getClass().getName() + " - deleteMovement () - "
				+ " Exit ");
	}

	/**
	 * This method is used for searching a Movement with given MovementId and
	 * HostId. This method also sets the notes status to the request object.
	 * 
	 * @param String
	 *            hostId
	 * @param Long
	 *            movementId
	 * @param HttpServletRequest
	 *            request
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovement(String hostId, Long movementId,
			HttpServletRequest request) throws SwtException {

		/* Method's local variable declaration */
		SystemInfo systemInfo = new SystemInfo();
		Movement movement;
		MovementManager movementManager;
		Movement tempMovemetn;

		log.debug(this.getClass().getName() + " - getMovement () - "
				+ " Entry with Request parameter");

		/*
		 * Calls preAdviceInputDAO getAccountMonitorDetails to get the Movement
		 * Object
		 */
		movement = preAdviceInputDAO.getMovement(hostId, movementId);

		movementManager = (MovementManager) (SwtUtil.getBean("movementManager"));

		/* Calls movementManager getMovementDetails to get the Movement Object */
		tempMovemetn = movementManager.getMovementDetails(hostId, movementId,
				systemInfo, SwtUtil.getCurrentSystemFormats(request
						.getSession()));

		if (movement != null) {
			Collection notes = ((MovementDAO) (SwtUtil.getBean("movementDAO")))
					.getNoteDetails(hostId, movementId);

			request.getSession().setAttribute("sessionNotesDetails", notes);

			movement
					.setHasNotes(((notes != null) && (notes.size() > 0)) ? SwtConstants.YES
							: SwtConstants.NO);

			if ((tempMovemetn != null)
					&& (tempMovemetn.getMatchStatusDesc() != null)) {
				request.setAttribute("matchDesc", tempMovemetn
						.getMatchStatusDesc());

				movement.setMatchStatusDesc(tempMovemetn.getMatchStatusDesc());
			} else {
				movement.setMatchStatusDesc("");
			}
		}

		log
				.debug(this.getClass().getName() + " - getMovement () - "
						+ " Exit ");

		return movement;
	}

	/**
	 * This method is used for searching a Movement with given MovementId and
	 * HostId.But this method does not set the notes status of the movement.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param Long
	 *            movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovement(String hostId, String entityId, Long movementId)
			throws SwtException {

		log.debug(this.getClass().getName() + " - getMovement () - "
				+ " Entry ");

		return ((MovementDAO) (SwtUtil.getBean("movementDAO"))).getMovement(
				hostId, entityId, movementId);
	}

	/**
	 * This method is basically used to retrieve List of Account Ids for a given
	 * inputs.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            movType
	 * @return List of Account Id's
	 * @throws SwtException
	 */
	public Collection getAccountIdDropDown(String hostId, String entityId,
			String currencyCode, String movType) throws SwtException {

		log.debug(this.getClass().getName() + " - getAccountIdDropDown () - "
				+ " Entry ");

		return ((MovementManager) (SwtUtil.getBean("movementManager")))
				.getAccountIdDropDown(hostId, entityId, currencyCode, movType,
						SwtConstants.MOVEMENT_SOURCE_PREADVICE);
	}

	/**
	 * This method is used to collect list of Movements for given input
	 * parameters
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyGroup
	 * @param String
	 *            dateRange
	 * @param String
	 *            userId
	 * @param String
	 *            searchDate
	 * @return Collection
	 * @throws SwtException
	 */
	/*
	 * code modified for the Mantis:772 For the problem of date format, thrown
	 * exception not a valid month,if date format mm/dd/yyyy
	 */
	/* START Code:Modified by Mahesh on 04-Dec-2008 */
	public Collection<InputAuthoriseSelection> getInputAuthoriseListToday(
			String hostId, String entityId, String currencyGroup,
			String dateRange, String userId, String searchDate,
			String dateFormat) throws SwtException {
		/* END Code:Modified by Mahesh on 04-Dec-2008 */

		/* Method's local variable declaration */
		String roleId;
		Collection<InputAuthoriseSelection> inputAuthList;

		log.debug(this.getClass().getName()
				+ " - getInputAuthoriseListToday () - " + " Entry ");

		/*
		 * Calls MovementManagerImpl getUserDetails to get the role Id for a
		 * given User ID
		 */
		roleId = getUserDetails(hostId, userId);
		/**
		 * Modified to fetch the authorize and reffered count from a single call
		 * instead of having a loop which created more connections
		 */
		inputAuthList = preAdviceInputDAO
				.getInputAuthoriseList(hostId, entityId, currencyGroup, userId,
						roleId, searchDate, dateFormat);

		log.debug(this.getClass().getName()
				+ " - getInputAuthoriseListToday () - " + " Exit ");

		return inputAuthList;
	}

	/**
	 * This method is used to retrieve MATCH_STATUS value for movement with
	 * given inputs.
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            inputSource
	 * @param String
	 *            matchStatus
	 * @param SystemFormats
	 *            sysFormat
	 * @return
	 * @throws SwtException
	 */
	public void getMovementWithMatchStatus(HttpServletRequest request,
			String hostId, String entityId, String currencyCode,
			String inputSource, String matchStatus, SystemFormats sysFormat,
			String dateFlag, String userId) throws SwtException {

		/* Method's local variable declaration */
		String roleId;
		Collection fullAcessList = new ArrayList();
		Collection veiwAccessList = new ArrayList();
		String access = "";
		Date movementValuedate = null;

		log.debug(this.getClass().getName() + "- [getMovementWithMatchStatus] "
				+ "Entry");
		/*
		 * Calls MovementManagerImpl getUserDetails to get the role Id for a
		 * given User ID
		 */
		roleId = getUserDetails(hostId, userId);

		/*
		 * Code modification for MANTIS ISSUE 545 Starts: Description:To avoid
		 * the currency,entity access for each movement Id that is fetched from
		 * the database.To avoid the notes available for each movement id
		 * fetched from the database. Code Modidfied On:17-06-2008 Code
		 * Modidfied by:Selva Kumar
		 * 
		 */

		/* Used to get all the currencies to check for access */
		HashMap currencyAccessMap = new HashMap();
		Collection currencyGroupAcessColl = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupAcess(roleId, entityId);

		if (currencyGroupAcessColl != null) {
			Iterator itr = currencyGroupAcessColl.iterator();

			while (itr.hasNext()) {
				EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itr
						.next();
				int currencyGroupaccess = entityCurrencyGroupAccess.getAccess();
				Collection currenciesColl = SwtUtil.getSwtMaintenanceCache()
						.getCurrencies(entityId,
								entityCurrencyGroupAccess.getCurrencyGroupId());

				if (currenciesColl != null) {
					Iterator itrCurr = currenciesColl.iterator();

					while (itrCurr.hasNext()) {
						CurrencyTO currencyTO = (CurrencyTO) itrCurr.next();

						if ((currencyTO != null)) {
							currencyAccessMap.put(currencyTO.getCurrencyId(),
									"Y");
						}
					}
				}
			}
		}
		/* Code modification for MANTIS ISSUE 545 Ends : */

		/*
		 * Calls preAdviceInputDAO getMovementWithMatchStatus to fetch record
		 * set from the database
		 */
		Collection movementList = preAdviceInputDAO.getMovementWithMatchStatus(
				hostId, entityId, currencyCode, inputSource, matchStatus,
				dateFlag, userId, roleId);
		Iterator itMovementList = movementList.iterator();

		/*
		 * Iterating the movement object to format the amount for the
		 * currency,formatting the value date and setting the full access or
		 * view access
		 */
		while (itMovementList.hasNext()) {
			Movement movement = (Movement) itMovementList.next();

			movement.setAmountAsString((movement.getAmount() != null) ? SwtUtil
					.formatCurrency(movement.getCurrencyCode(), movement
							.getAmount()) : "");

			try {
				// Start: Modified by Bala on 13092010 for Mantis 1258 to Change the date format for Unparseable date exception
				movementValuedate = new SimpleDateFormat(
						"yyyy-MM-dd HH:mm:ss").parse(movement
						.getValueDateAsString());
				// End: Modified by Bala on 13092010 for Mantis 1258 to Change the date format for Unparseable date exception
				movement
						.setValueDateAsString((movement.getValueDateAsString() != null) ? SwtUtil
								.formatDate(movementValuedate, sysFormat
										.getDateFormatValue())
								: "");
			} catch (ParseException pe) {
				log.debug(this.getClass().getName()
						+ " - getMovementWithMatchStatus () - "
						+ " Parse Exception - " + pe.getMessage());
				log.error(this.getClass().getName()
						+ " - getMovementWithMatchStatus () - "
						+ " Parse Exception - " + pe.getMessage());
			} catch (Exception e) {
				log.debug(this.getClass().getName()
						+ " - getMovementWithMatchStatus () - "
						+ " Exception - " + e.getMessage());
				log.error(this.getClass().getName()
						+ " - getMovementWithMatchStatus () - "
						+ " Exception - " + e.getMessage());
			}

			/*
			 * movement.setValueDateAsString((movement.getValueDate()!= null) ?
			 * SwtUtil.formatDate(movement.getValueDate(),sysformat.getDateFormatValue()) :
			 * "");
			 */

			/*
			 * Code modification for MANTIS ISSUE 545 Starts: Description:To
			 * avoid the currency access for each movement Id that is fetched
			 * from the database. and setting the access as TRUE if present and
			 * false if not present Code Modidfied On:17-06-2008 Code Modidfied
			 * by:Selva Kumar
			 * 
			 */
			if (currencyAccessMap.containsKey(movement.getCurrencyCode())) {
				access = "true";
			}

			if (movement.getMatchStatus().equalsIgnoreCase("A")) {

				if (access.equalsIgnoreCase("true")
						&& !movement.getUpdateUser().equalsIgnoreCase(userId)) {
					access = "true";
				} else {
					access = "false";
				}

			} else {

				if (access.equalsIgnoreCase("true")
						&& movement.getInputRole().equalsIgnoreCase(roleId)) {
					access = "true";
				} else {
					access = "false";
				}
			}

			/* Code modification for MANTIS ISSUE 545 Ends : */

			movement.setAccess(access);

			if (access.equalsIgnoreCase("true")) {
				fullAcessList.add(movement);
			} else {
				veiwAccessList.add(movement);
			}

		}

		/*
		 * Setting the fullAcessList collection & veiwAccessList collection in
		 * request object
		 */
		request.setAttribute("movementSummaryDetails", fullAcessList);
		request.setAttribute("movementDetailsVeiw", veiwAccessList);

		log.debug(this.getClass().getName()
				+ " - [getMovementWithMatchStatus] " + "Exit");

	}

	/**
	 * This Method is to set MATCH_STATUS of selected movements and writes log
	 * record in P_MOVEMENT_LOG and updates Matching Driver table P_MATCH_DRIVER
	 * for selected movements' Entity & Currency (as in Movement Manual Input).
	 * 
	 * @param Movement
	 *            movement
	 * @param Collection
	 *            sessionNotesDetails
	 * @throws SwtException
	 */
	public void updateMovementDetails(Movement movement,
			Collection sessionNotesDetails) throws SwtException {

		/* Method's local variable declaration */
		MatchDriver matchDriver = null;
		String matchDriverOperation = null;

		log.debug(this.getClass().getName() + " - updateMovementDetails () - "
				+ " Entry ");

		try {
			MovementDAO movementDAO = (MovementDAO) (SwtUtil
					.getBean("movementDAO"));

			/*
			 * calls movementDAO's updateMovementDetails to update the movement
			 * details
			 */
			movementDAO.updateMovementDetails(movement);

			/*
			 * Updating the movements based on the movement's match status as
			 * 'L'
			 */
			if (movement.getMatchStatus().equalsIgnoreCase("L")) {
				matchDriver = existMatchDriver(movement.getId().getHostId(),
						movement.getId().getEntityId().toString(), movement
								.getCurrencyCode());

				if (matchDriver == null) {
					matchDriver = getMatchDriverInstance(movement.getId()
							.getHostId(), movement.getId().getEntityId()
							.toString(), movement.getCurrencyCode());

					matchDriverOperation = "Save";
				} else {
					matchDriver.setNewMoveFlag("Y");
					matchDriverOperation = "update";
				}

				if (matchDriverOperation.equalsIgnoreCase("save")) {
					movementDAO.saveMatchDriverDetails(matchDriver);
				} else if (matchDriverOperation.equals("update")) {
					movementDAO.updateMatchDriverDetails(matchDriver);
				}
			}

		} catch (Exception exp) {

			log.debug(this.getClass().getName()
					+ " - updateMovementDetails () - " + " Exception - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - updateMovementDetails () - " + " Exception - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMovementDetails", PreAdviceInputManagerImpl.class);
		}
		log.debug(this.getClass().getName() + " - updateMovementDetails () - "
				+ " Exit ");
	}

	/**
	 * This method use to handle the MovementAlerts updation and creation based
	 * on the MatchStatus passed to it.
	 * 
	 * @param Movement
	 *            movement
	 * @param String
	 *            originalStatus
	 * @param String
	 *            newStatus
	 * @throws SwtException
	 */
	public void manageMovementAlert(Movement movement, String originalStatus,
			String newStatus) throws SwtException {

		/* Method's local variable declaration */
		final boolean MT_FLAG;
		final boolean MR_FLAG;

		log.debug(this.getClass().getName() + " - manageMovementAlert () - "
				+ " Entry ");

		SweepAlertDAO sweepAlertDAO = (SweepAlertDAO) (SwtUtil
				.getBean("sweepAlertDAO"));
		SystemAlertMessagesDAO systemAlertMessagesDAO = (SystemAlertMessagesDAO) (SwtUtil
				.getBean("sysAlertMessagesDAO"));

		/*
		 * Calls systemAlertMessagesDAO alertAllow to get the MT_FLAG,MR_FLAG
		 * flag values
		 */
		MT_FLAG = systemAlertMessagesDAO.alertAllow(movement.getId()
				.getHostId(), SwtConstants.MOVEMENT_ALERT_STAGE_MT);

		MR_FLAG = systemAlertMessagesDAO.alertAllow(movement.getId()
				.getHostId(), SwtConstants.MOVEMENT_ALERT_STAGE_MR);

		/* Alerts enabling based on the Match status */
		if (originalStatus.equals(SwtConstants.AUTHORISE_STATUS)
				&& newStatus.equals(SwtConstants.OUTSTANDING_STATUS)) {

			Collection sweepAlertList = sweepAlertDAO.getMovementAlert(movement
					.getId().getHostId(), movement.getId().getEntityId(),
					movement.getId().getMovementId(),
					SwtConstants.MOVEMENT_ALERT_STAGE_MT,
					SwtConstants.ALERT_STATUS_PENDING);
			sweepAlertList = (sweepAlertList != null) ? sweepAlertList
					: new ArrayList();

			Iterator it_sweepAlertList = sweepAlertList.iterator();

			if (MT_FLAG) {
				while (it_sweepAlertList.hasNext()) {
					SweepAlert sweepAlert = (SweepAlert) it_sweepAlertList
							.next();

					sweepAlert.setStatus(SwtConstants.ALERT_STATUS_CANCLE);
					sweepAlert.setCurrencyCode(movement.getCurrencyCode());
					sweepAlertDAO.updateMovementAlert(sweepAlert);
				}
			}
		}

		/* Alerts enabling based on the Match status */
		if (originalStatus.equals(SwtConstants.AUTHORISE_STATUS)
				&& newStatus.equals(SwtConstants.REFERRED_STATUS)) {

			Collection sweepAlertList = sweepAlertDAO.getMovementAlert(movement
					.getId().getHostId(), movement.getId().getEntityId(),
					movement.getId().getMovementId(),
					SwtConstants.MOVEMENT_ALERT_STAGE_MT,
					SwtConstants.ALERT_STATUS_PENDING);
			sweepAlertList = (sweepAlertList != null) ? sweepAlertList
					: new ArrayList();

			Iterator it_sweepAlertList = sweepAlertList.iterator();

			if (MT_FLAG) {
				while (it_sweepAlertList.hasNext()) {
					SweepAlert sweepAlert = (SweepAlert) it_sweepAlertList
							.next();

					sweepAlert.setStatus(SwtConstants.ALERT_STATUS_CANCLE);

					sweepAlert.setCurrencyCode(movement.getCurrencyCode());

					sweepAlertDAO.updateMovementAlert(sweepAlert);
				}
			}

			if (MR_FLAG) {
				SweepAlert sweepAlert = getMovementAlertInstance(movement);
				sweepAlert.setAlertStage(SwtConstants.MOVEMENT_ALERT_STAGE_MR);
				sweepAlertDAO.createMovementAlert(sweepAlert);
			}
		}

		/* Alerts enabling based on the Match status */
		if (originalStatus.equals(SwtConstants.REFERRED_STATUS)
				&& newStatus.equals(SwtConstants.AUTHORISE_STATUS)) {

			Collection sweepAlertList = sweepAlertDAO.getMovementAlert(movement
					.getId().getHostId(), movement.getId().getEntityId(),
					movement.getId().getMovementId(),
					SwtConstants.MOVEMENT_ALERT_STAGE_MR,
					SwtConstants.ALERT_STATUS_PENDING);

			sweepAlertList = (sweepAlertList != null) ? sweepAlertList
					: new ArrayList();

			Iterator it_sweepAlertList = sweepAlertList.iterator();

			if (MR_FLAG) {
				while (it_sweepAlertList.hasNext()) {
					SweepAlert sweepAlert = (SweepAlert) it_sweepAlertList
							.next();

					sweepAlert.setStatus(SwtConstants.ALERT_STATUS_CANCLE);

					sweepAlert.setCurrencyCode(movement.getCurrencyCode());

					sweepAlertDAO.updateMovementAlert(sweepAlert);
				}
			}

			if (MT_FLAG) {
				SweepAlert sweepAlert = getMovementAlertInstance(movement);
				sweepAlert.setAlertStage(SwtConstants.MOVEMENT_ALERT_STAGE_MT);
				sweepAlertDAO.createMovementAlert(sweepAlert);
			}
		}

		/* Alerts enabling based on the Match status */
		if (originalStatus.equals(SwtConstants.REFERRED_STATUS)
				&& newStatus.equals(SwtConstants.OUTSTANDING_STATUS)) {

			Collection sweepAlertList = sweepAlertDAO.getMovementAlert(movement
					.getId().getHostId(), movement.getId().getEntityId(),
					movement.getId().getMovementId(),
					SwtConstants.MOVEMENT_ALERT_STAGE_MR,
					SwtConstants.ALERT_STATUS_PENDING);

			sweepAlertList = (sweepAlertList != null) ? sweepAlertList
					: new ArrayList();

			Iterator it_sweepAlertList = sweepAlertList.iterator();

			if (MR_FLAG) {
				while (it_sweepAlertList.hasNext()) {
					SweepAlert sweepAlert = (SweepAlert) it_sweepAlertList
							.next();

					sweepAlert.setStatus(SwtConstants.ALERT_STATUS_CANCLE);
					sweepAlert.setCurrencyCode(movement.getCurrencyCode());
					sweepAlertDAO.updateMovementAlert(sweepAlert);
				}
			}
		}

		log.debug(this.getClass().getName() + " - manageMovementAlert () - "
				+ " Exit ");
	}

	/**
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return MatchDriver matchDriver
	 * 
	 */
	private MatchDriver getMatchDriverInstance(String hostId, String entityId,
			String currencyCode) {

		log.debug(this.getClass().getName() + " - getMatchDriverInstance () - "
				+ " Entry ");

		/* Setting the hostId, entityId,currencyCode in MatchDriver object */
		MatchDriver matchDriver = new MatchDriver();
		matchDriver.getId().setHostId(hostId);
		matchDriver.getId().setEntityId(entityId);
		matchDriver.getId().setCurrencyCode(currencyCode);
		matchDriver.setNewMoveFlag("Y");
		matchDriver.setProcessingFlag("N");

		log.debug(this.getClass().getName() + " - getMatchDriverInstance () - "
				+ " Exit ");

		return matchDriver;
	}

	/**
	 * This method is used to create a fresh instance of SweepAlert class. Alert
	 * stage will be given by you only
	 * 
	 * @param Movement
	 *            movement
	 * @return SweepAlert sweepAlert
	 */
	private SweepAlert getMovementAlertInstance(Movement movement) {

		SweepAlert sweepAlert = new SweepAlert();

		log.debug(this.getClass().getName()
				+ " - getMovementAlertInstance () - " + " Entry ");

		/*
		 * Setting the host id,entity id,currency code,movement id,amount from
		 * the movement object and system date and alert status as 'P' (Pending)
		 * in SweepAlert object
		 */

		sweepAlert.getId().setHostId(movement.getId().getHostId());
		sweepAlert.getId().setEntityId(movement.getId().getEntityId());
		sweepAlert.setCurrencyCode(movement.getCurrencyCode());
		sweepAlert.setSweepId(movement.getId().getMovementId());
		sweepAlert.setSweepAmount(movement.getAmount());
		sweepAlert.setAlertDate(SwtUtil.getSystemDatewithoutTime());
		sweepAlert.setStatus(SwtConstants.ALERT_STATUS_PENDING);

		log.debug(this.getClass().getName()
				+ " - getMovementAlertInstance () - " + " Exit ");

		return sweepAlert;
	}

	/**
	 * This method returns the Pre-Advice Position Level of given entity.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getPreadvicePositionLevelOfEntity(String hostId,
			String entityId) throws SwtException {

		/* Method's local variable declaration */
		Collection coll = new ArrayList();
		Integer preadvicePositionLevel = null;
		Entity entity = new Entity();

		log.debug(this.getClass().getName()
				+ " - getPreadvicePositionLevelOfEntity () - " + " Entry ");

		EntityDAO entityDAO = (EntityDAO) (SwtUtil.getBean("entityDAO"));

		/* Setting the hostId,entityId in entity object */
		entity.getId().setHostId(hostId);
		entity.getId().setEntityId(entityId);

		/* Calls entityDAO getEntityDetail to get the entity object */
		entity = entityDAO.getEntityDetail(entity);

		if ((entity != null) && (entity.getPreAdvicePosition() != null)) {
			preadvicePositionLevel = entity.getPreAdvicePosition();
		}

		preadvicePositionLevel = (preadvicePositionLevel == null) ? new Integer(
				"0")
				: preadvicePositionLevel;

		Collection positionLevelList = SwtUtil.getSwtMaintenanceCache()
				.getEntityPositionLevelObjectLVL(entityId);

		if ((positionLevelList != null) && (positionLevelList.size() > 0)) {
			Iterator it_positionLevelList = positionLevelList.iterator();

			/* Iterating the position level for EntityId */
			while (it_positionLevelList.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) it_positionLevelList
						.next();
				String str = lvb.getValue().toString();
				String str2 = preadvicePositionLevel.toString();

				if (str.equals(str2)) {
					coll
							.add(new LabelValueBean(lvb.getLabel(), lvb
									.getValue()));

					break;
				}
			}
		}
		log.debug(this.getClass().getName()
				+ " - getPreadvicePositionLevelOfEntity () - " + " Exit ");

		return coll;
	}

	/**
	 * This method is used to update the open flag of P_MOVEMENT table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param Long
	 *            movementId
	 * @param String
	 *            openFlag
	 * @param String
	 *            updateUser
	 * @return
	 * @throws SwtException
	 * 
	 */
	public void updateOpenUnopenFlag(String hostId, String entityId,
			Long movementId, String openFlag, String updateUser)
			throws SwtException {

		log.debug(this.getClass().getName() + " - updateOpenUnopenFlag () - "
				+ " Entry ");

		((MovementManager) (SwtUtil.getBean("movementManager")))
				.updateOpenUnopenFlag(hostId, entityId, movementId, openFlag,
						updateUser);
	}
	
	
	/**
	 * This method is used to get the pre-advice movements that have been input in
	 * the last 24 hours
	 */
	public Collection<Movement> getLast24PreAdviceInput(String hostId, String updateUser, String entityId, String inputBy)
			throws SwtException {

		Collection<Movement> preAdviceList = new ArrayList<Movement>();

		log.debug(this.getClass().getName() + " - getLast24PreAdviceInput () - " + " Entry ");

		preAdviceList = preAdviceInputDAO.getLast24PreAdviceInput(hostId, updateUser, entityId, inputBy);
		return preAdviceList;

	}

	/**
	 * This method is used to save the pre-advice movements that have been pasted in
	 * import screen bottom grid
	 */
	public void saveAll(List<Movement> listMvt) throws SwtException {

		log.debug(this.getClass().getName() + " - saveAll () - " + " Entry ");
		preAdviceInputDAO.saveAll(listMvt);

	}
	
	
	/**
	 * This method is used to get movement type from p_account table
	 */
	public String getMvtFromAccountTab(String accountId) throws SwtException{
		log.debug(this.getClass().getName() + " - getMvtFromAccountTab () - " + " Entry ");
		return preAdviceInputDAO.getMvtFromAccountTab(accountId);
		
	}

	/**
	 * This method is used to check if entity id exists in S_ENTITY
	 */
	public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " - checkIfEntityExists () - " + " Entry ");
		return preAdviceInputDAO.checkIfEntityExists(hostId, entityId);
	}
	
	/**
	 * This method is used to get account status from p_account table
	 */
	public String getAccountStatus(String accountId) throws SwtException {
		log.debug(this.getClass().getName() + " - getAccountStatus () - " + " Entry ");
		return preAdviceInputDAO.getAccountStatus(accountId);
	}
	
	
	/**
	 * This method is used to get  ALLOW_PRE_ADVICES from p_account table
	 */
	public String checkAccountPreAdviceAccess(String accountId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " - checkAccountPreAdviceAccess () - " + " Entry ");
		return preAdviceInputDAO.checkAccountPreAdviceAccess(accountId, entityId);
	}
	
	
	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
			int currentPage, String selectedsort) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return preAdviceInputDAO.getPartySearchResult(partyId, partyName, entityId,
					pageSize, currentPage, selectedsort);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
		// return result as list
	}

	@Override
	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return preAdviceInputDAO.getTotalCount(partyName, partyId, entityId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
	}
}
