/*
 * @(#)SweepDetailManagerManagerImpl.java 1.0 Jan 04, 2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AcctSpecificSweepFormatDAO;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.SweepDetailDAO;
import org.swallow.work.dao.SweepQueueDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.model.SweepAmount;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.model.SweepNote;
import org.swallow.work.service.SweepDetailManager;
import org.swallow.work.service.SweepDetailValObj;

/**
 * This calss is used to Dispaly handle the Sweep Detail screen
 */
@Component ("sweepDetailManager")
public class SweepDetailManagerImpl implements SweepDetailManager {
	private final Log log = LogFactory.getLog(SweepDetailManagerImpl.class);
	@Autowired
	private SweepDetailDAO sweepDetailDAO;

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.SweepDetailManager#setMatchDAO(org.swallow.work.dao.SweepDetailDAO)
	 */
	@Autowired
	public void setSweepDetailDAO(SweepDetailDAO sweepDetailDAO) {
		this.sweepDetailDAO = sweepDetailDAO;
	}

	/**
	 * Method to get Account Details for the selected accounts for Manual Sweep
	 * 
	 * @param entityId
	 * @param accIds
	 * @param valueDate
	 * @param formats
	 * @param entities
	 * @return SweepDetailValObj
	 * @throws SwtException
	 */
	public SweepDetailValObj getAccountDetails(String entityId, String accIds,
			String valueDate, SystemFormats formats, String entities, String selectedSweepScheduleSetting)
			throws SwtException {
		// Variable to hold the sweepDetailVO object value
		SweepDetailValObj sweepDetailVO = null;
		// String variable to hold the entity value
		String[] entity = null;
		// String variable to hold the acctId value
		String[] accountId = null;
		// String variable to hold the currCode value
		String currencyCode = null;
		// String variable to hold the hostId value
		String hostId = null;
		// Variable to hold the accounts object value
		ArrayList<SweepDetail> accounts = null;
		// Variable to hold the sweepAmnts object value
		ArrayList<SweepAmount> sweepAmnts = null;
		// Variable to hold the acctmain object value
		AcctMaintenance acctmain = null;
		// Variable to hold the acctmain1 object value
		AcctMaintenance acctmain1 = null;
		// Variable to hold the acctmain2 object value
		AcctMaintenance acctmain2 = null;
		// Variable to hold the collAccountDetails object value
		List<AcctMaintenance> collAccountDetails = null;
		// Variable to hold the listAccountDetails object value
		List<AcctMaintenance> listAccountDetails = null;
		// Variable to hold the predictedBalance value
		Double predictedBalance = null;
		// Variable to hold the targetBal value
		Double targetBal = null;
		// Variable to hold the alignedAcct value
		int alignedAcct = 1;
		// Variable to hold the orginalSweepamount value
		Double orginalSweepamount = null;
		// String variable to hold the acct2Sign value
		String acct2Sign = null;
		// String variable to hold the formatId value
		String formatId = null;
		// String variable to hold the formatIdCr value
		String formatIdCr = null;
		// String variable to hold the formatIdDr value
		String formatIdDr = null;
		// String variable to hold the authFlag value
		String authFlag = null;
		// String variable to hold the authFlagCr value
		String authFlagCr = null;
		// String variable to hold the authFlagDr value
		String authFlagDr = null;
		/*
		 * Start:Code modified by sudhakar For Mantis 1838:Message format not
		 * displayed accordingly when sweep direction changed
		 */
		// StringArray variable to hold the acct1CreditMessageType
		String acct1CreditMessageType[] = null;
		// StringArray variable to hold the acct1DebitMessageType
		String acct1DebitMessageType[] = null;
		// StringArray variable to hold the acct2CreditMessageType
		String acct2CreditMessageType[] = null;
		// StringArray variable to hold the acct2DebitMessageType
		String acct2DebitMessageType[] = null;
		// Variable to hold the externalBalance value
		Double externalBalance = null;
		// Variable to hold the accitr object value
		Iterator<AcctMaintenance> itrAcctMaintenance = null;
		// String variable to hold the accLevel1 value
		String accLevel1 = null;
		// String variable to hold the accLevel2 value
		String accLevel2 = null;
		// String variable to hold the targetSign value
		String targetSign = null;
		// String variable to hold the isAcct1Sub value
		String isAcct1Sub = null;
		// String variable to hold the sweepAmt value
		SweepAmount sweepAmt = null;
		// String variable to hold the DateFormat value
		String format = null;
		// Variable to hold the sweep object value
		SweepDetail sweep = null;
		// Variable to hold the sweep object value
		SimpleDateFormat sdfCurrentDate = null;
		String applySchedulerSweepsValuesAsString = "";
		
		boolean applySchedulerSweepsValues = false;
		try {
			log.debug(this.getClass().getName()
					+ " -[getAccountDetails]- Entry");
			// Get the current hostID
			hostId = SwtUtil.getCurrentHostId();
			acct2Sign = "";
			// Instantiate the sweepDetailVO,acctmain,
			// acctmain1,acctmain2coll,AccountDetails,listAccountDetails,accounts,sweepAmnts
			sweepDetailVO = new SweepDetailValObj();
			acctmain = new AcctMaintenance();
			acctmain1 = new AcctMaintenance();
			acctmain2 = new AcctMaintenance();
			collAccountDetails = new ArrayList<AcctMaintenance>();
			listAccountDetails = new ArrayList<AcctMaintenance>();
			accounts = new ArrayList<SweepDetail>();
			sweepAmnts = new ArrayList<SweepAmount>();
			entity = new String[2];
			accountId = new String[2];
			entities = entities.replace('\'', ' ');

			entity = entities.split(",");
			entity[0] = entity[0].trim();
			entity[1] = entity[1].trim();

			accIds = accIds.replace('\'', ' ');

			accountId = accIds.split(",");
			// Get the selected account details
			for (int i = 0; i < 2; i++) {

				listAccountDetails = (List<AcctMaintenance>) sweepDetailDAO
						.getAccountDetails(entity[i].trim(), hostId,
								accountId[i].trim());
				acctmain1 = (AcctMaintenance) listAccountDetails.iterator()
						.next();
				collAccountDetails.add(acctmain1);
			}

			/**
			 * Check f
			 */
			AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			
			
			
			
			itrAcctMaintenance = collAccountDetails.iterator();
			// Iterate the acctmain1,acctmain2 for getting accLevel
			acctmain1 = (AcctMaintenance) itrAcctMaintenance.next();
			acctmain2 = (AcctMaintenance) itrAcctMaintenance.next();
			
			
			ArrayList<AccSweepSchedule> collAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
					.getAcctSweepScheduleListBetweenAccounts(hostId, acctmain1.getId().getEntityId(), currencyCode, acctmain1.getId().getAccountId() ,acctmain2.getId().getEntityId(), acctmain2.getId().getAccountId());
			if(collAcctSweepSchedule.size() == 1) {
				applySchedulerSweepsValuesAsString = "["+collAcctSweepSchedule.get(0).getEntityId()+"/"+collAcctSweepSchedule.get(0).getAccountId()+"]" + collAcctSweepSchedule.get(0).getScheduleFrom() +"-"+collAcctSweepSchedule.get(0).getScheduleTo();
				if("Account".equalsIgnoreCase(selectedSweepScheduleSetting)) {
					applySchedulerSweepsValues = true;
				}
			}
			
			
			accLevel1 = acctmain1.getAcctlevel();
			accLevel2 = acctmain2.getAcctlevel();
			targetSign = "";
			isAcct1Sub = SwtConstants.NO;
			// Set the alignedAcct based on accLevel2,main account
			if(applySchedulerSweepsValues) {
				if(collAcctSweepSchedule.get(0).getAccountId().equals(acctmain1.getId().getAccountId())) {
					alignedAcct = 1;
					isAcct1Sub = SwtConstants.YES;
				}else {
					alignedAcct = 2;
				}
				
			}else {
				if (accLevel1.equals(accLevel2)) {
					alignedAcct = 1;
				} else if (accLevel1.equals(SwtConstants.ACCT_MAIN)) {
					alignedAcct = 2;
				} else {
					alignedAcct = 1;
					isAcct1Sub = SwtConstants.YES;
				}
			}
			if (formats.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
				format = "M";
			} else {
				format = "D";
			}
			
			

			// Condition to check align to target is selected on sub account
			if (alignedAcct == 2) {

				for (int subCount = (collAccountDetails.size() - 1); subCount >= 0; subCount--) {
					acctmain = (AcctMaintenance) collAccountDetails
							.get(subCount);

					if(applySchedulerSweepsValues && collAcctSweepSchedule.get(0).getAccountId().equals(acctmain.getId().getAccountId())) {
						acctmain.setTargetbalance(collAcctSweepSchedule.get(0).getTargetBalance());
						acctmain.setTgtbalsign(collAcctSweepSchedule.get(0).getTargetBalanceType());
					}
					sweep = new SweepDetail();
					
					
					// Set the
					// CurrCode,Acctname,AccountId,MinSweepAmt,MaxSweepAmt,MinSweepAmtAsString,MaxSweepAmtAsString,CutOffTime,Bookcode
					sweep.setCurrCode(acctmain.getCurrcode());
					currencyCode = sweep.getCurrCode();
					sweep.setAcctname(acctmain.getAcctname());
					sweep.setAccountId(acctmain.getId().getAccountId());
					sweep.setMinSweepAmt(acctmain.getMinseepamt());
					sweep.setMaxSweepAmt(acctmain.getMaxsweepamte());
					sweep.setMinSweepAmtAsString(SwtUtil.formatCurrency(
							acctmain.getCurrcode(), sweep.getMinSweepAmt()));
					sweep.setMaxSweepAmtAsString(SwtUtil.formatCurrency(
							acctmain.getCurrcode(), sweep.getMaxSweepAmt()));
					sweep.setCutOffTime(acctmain.getCutoff());
					sweep.setBookcode(acctmain.getSweepbookcode());
					sweep.setDefaultSettleMethod(acctmain.getDefaultSettleMethod());
					
					sweep.setDefaultSettleMethodDr(acctmain.getDefaultSettleMethod());
					sweep.setDefaultSettleMethodCr(acctmain.getDefaultSettleMethod());
					sweep.setBookcodeDr(acctmain.getSweepbookcode());
					sweep.setBookcodeCr(acctmain.getSweepbookcode());
					// Setting Sweep From Balance in Bean
					if (acctmain.getSweepFrmbal().equals("P")) {
						sweep.setSweepFrmbal("Predicted");
					} else {
						sweep.setSweepFrmbal("External");
					}
					// Sub settiung sub acct time
					if (!SwtUtil.isEmptyOrNull(acctmain.getSubAcctim())) {
						sweep.setSubAcctim(acctmain.getSubAcctim());
					} else {
						sweep.setSubAcctim("N");
					}
					sweep.setEntityId(entity[subCount]);

					if (acctmain.getAcctlevel().equalsIgnoreCase(
							SwtConstants.ACCT_MAIN)) {
						sweep.setAccountType(SwtConstants.ACCT_MAIN_VALUE);
					} else {
						sweep.setAccountType(SwtConstants.ACCT_SUB_VALUE);
					}
					String currId = acctmain.getCurrcode();
					// Get the predict balances from dao layer
					predictedBalance = sweepDetailDAO.getPredictBalance(
							entity[subCount], hostId, currId, acctmain.getId()
									.getAccountId(), valueDate, format);
					sweep.setPredictBalance(predictedBalance);
					// assinging external; balnce value
					externalBalance = sweepDetailDAO.getExternalBalance(
							entity[subCount], hostId, currId, acctmain.getId()
									.getAccountId(), valueDate, format);
					sweep.setExternalBalance(externalBalance);

					if (predictedBalance.doubleValue() < 0) {
						sweep.setPredictBalanceSign(SwtConstants.DEBIT);
						sweep.setPredictBalanceAsString(SwtUtil.formatCurrency(
								sweep.getCurrCode(), new Double(0 - sweep
										.getPredictBalance().doubleValue())));
					} else {
						sweep.setPredictBalanceSign(SwtConstants.CREDIT);
						sweep
								.setPredictBalanceAsString(SwtUtil
										.formatCurrency(sweep.getCurrCode(),
												sweep.getPredictBalance()));
					}

					// Set External Balnce in the Bean
					if (externalBalance.doubleValue() < 0) {
						sweep.setExternalBalanceSign(SwtConstants.DEBIT);
						sweep.setExternalBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getExternalBalance()
												.doubleValue())));
					} else {
						sweep.setExternalBalanceSign(SwtConstants.CREDIT);
						sweep.setExternalBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getExternalBalance()));
					}

					sweep.setTargetBalanceAsString(SwtUtil.formatCurrency(sweep
							.getCurrCode(), acctmain.getTargetbalance()));

					targetSign = acctmain.getTgtbalsign();

					if (targetSign != null) {
						if (targetSign.equalsIgnoreCase(SwtConstants.DEBIT)) {

							sweep.setTargetBalanceSign(SwtConstants.DEBIT);
							targetBal = (new Double(0 - acctmain
									.getTargetbalance().doubleValue()));
						} else {

							sweep.setTargetBalanceSign(SwtConstants.CREDIT);
							targetBal = (acctmain.getTargetbalance());
						}
					} else {

						sweep.setTargetBalanceSign(SwtConstants.CREDIT);
						targetBal = (acctmain.getTargetbalance());
					}

					sweep.setTargetBalance(targetBal);
					// Instantiate the subCreditMessageType,subDebitMessageType
					// in String array
					acct1CreditMessageType = new String[3];
					acct1DebitMessageType = new String[3];
					// Assign newCrInt,newDrInt,newCrExt,newDrExt values in
					// subCreditMessageType,subDebitMessageType
					
					AcctSpecificSweepFormatDAO specficAccountDAO = (AcctSpecificSweepFormatDAO) SwtUtil
							.getBean("acctSpecificSweepFormatDAO");
					
					
					AcctMaintenance otherAccount  = null;
					if(subCount == 1) {
						otherAccount = collAccountDetails.get(0);
					}else {
						otherAccount = collAccountDetails.get(1);
					}
					
					AccountSpecificSweepFormat specificAccount =  specficAccountDAO.getAccountSpecificSweepFormat(hostId, acctmain.getId().getEntityId(), acctmain.getId().getAccountId(), otherAccount.getId().getEntityId(), otherAccount.getId().getAccountId());
					
					
					
					if(specificAccount == null) {
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewCrInternal())) {
							acct1CreditMessageType[0] = acctmain
									.getAcctNewCrInternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewDrInternal())) {
							acct1DebitMessageType[0] = acctmain
									.getAcctNewDrInternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewCrExternal())) {
							acct1CreditMessageType[1] = acctmain
									.getAcctNewCrExternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewDrExternal())) {
							acct1DebitMessageType[1] = acctmain
									.getAcctNewDrExternal();
						}
						// Assign the
						// creditIntermediaryMsgType,debitIntermediaryMsgType in
						// String Array
						acct1CreditMessageType[2] = acctmain
								.getCreditExternalInter() != null ? acctmain
								.getCreditExternalInter() : "";
						acct1DebitMessageType[2] = acctmain.getDebitExternalInter() != null ? acctmain
								.getDebitExternalInter()
								: "";
					}else {
						
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewInternalCrFormat())) {
							acct1CreditMessageType[0] = specificAccount.getNewInternalCrFormat();
							acctmain.setAcctNewCrInternal(specificAccount.getNewInternalCrFormat());
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewInternalDrFormat())) {
							acct1DebitMessageType[0] = specificAccount.getNewInternalDrFormat();
							acctmain.setAcctNewDrInternal(specificAccount.getNewInternalDrFormat());
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewExternalCrFormat())) {
							acct1CreditMessageType[1] = specificAccount.getNewExternalCrFormat();
							acctmain.setAcctNewCrExternal(specificAccount.getNewExternalCrFormat());
							
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewExternalDrFormat())) {
							acct1DebitMessageType[1] = specificAccount.getNewExternalDrFormat();
							acctmain.setAcctNewDrExternal(specificAccount.getNewExternalDrFormat());
						}
						// Assign the
						// creditIntermediaryMsgType,debitIntermediaryMsgType in
						// String Array
						acct1CreditMessageType[2] = specificAccount.getNewExternalCrFormatInt() != null ? specificAccount.getNewExternalCrFormatInt() : "";
						acct1DebitMessageType[2] = specificAccount.getNewExternalDrFormatINt() != null ? specificAccount.getNewExternalDrFormatINt() : "";
								
					}
					
					
					// Set the the separeted MessageTypeCr in Sweep
					sweep.setMessageTypeCr(getJoinMessageType(" / ",
							acct1CreditMessageType));
					// Set the the separeted MessageTypeDr in Sweep

					sweep.setMessageTypeDr(getJoinMessageType(" / ",
							acct1DebitMessageType));
					
					
					if(applySchedulerSweepsValues) {
						if(collAcctSweepSchedule.get(0).getAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getEntityId().equals(acctmain.getId().getEntityId())) {
							// Set the the separeted MessageTypeCr in Sweep
							sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getThisAccSettleMethodCr());
							// Set the the separeted MessageTypeDr in Sweep
							sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getThisAccSettleMethodDr());
							// Set the the separeted MessageTypeCr in Sweep
							sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeCr());
							// Set the the separeted MessageTypeDr in Sweep
							sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeDr());

						}
						
						if(collAcctSweepSchedule.get(0).getSweepAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getSweepAccountEntity().equals(acctmain.getId().getEntityId())) {
							// Set the the separeted MessageTypeCr in Sweep
							sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodCr());
							// Set the the separeted MessageTypeDr in Sweep
							sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodDr());
							// Set the the separeted MessageTypeCr in Sweep
							sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeCr());
							// Set the the separeted MessageTypeDr in Sweep
							sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeDr());

						}
					
					}else {
						//TODO:
						
					}
					
					// Format the formatIdCr,formatIdDr
					formatIdCr = "'" + acctmain.getAcctNewCrInternal() + "','"
							+ acctmain.getAcctNewCrExternal() + "'";
					formatIdDr = "'" + acctmain.getAcctNewDrInternal() + "','"
							+ acctmain.getAcctNewDrExternal() + "'";
					if (subCount == (collAccountDetails.size() - 1)) {

						sweep.setAlignToTarget("Y");
						if (sweep.getSweepFrmbal().equals("External")) {
							// Set an account details based on
							// targetBal,externalBalance
							if (targetBal.doubleValue() > externalBalance
									.doubleValue()) {

								orginalSweepamount = new Double(
										(externalBalance.doubleValue())
												- (targetBal.doubleValue()));

								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance
										- orginalSweepamount);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweep.setMessage(sweep.getMessageTypeCr());
								
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
									sweep.setBookcode(sweep.getBookcodeCr());
								}
								formatId = formatIdCr;
							} else {
								orginalSweepamount = new Double(
										(externalBalance.doubleValue())
												- (targetBal.doubleValue()));

								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance
										- orginalSweepamount);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;
								sweep.setMessage(sweep.getMessageTypeDr());
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
									sweep.setBookcode(sweep.getBookcodeDr());
								}
								formatId = formatIdDr;
							}
						} else {
							// Set an account details based on
							// targetBal,externalBalance
							if (targetBal.doubleValue() > predictedBalance
									.doubleValue()) {
								orginalSweepamount = new Double(
										(predictedBalance.doubleValue())
												- (targetBal.doubleValue()));

								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweep.setMessage(sweep.getMessageTypeCr());
								formatId = formatIdCr;
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
									sweep.setBookcode(sweep.getBookcodeCr());
								}
							} else {
								orginalSweepamount = new Double(
										(predictedBalance.doubleValue())
												- (targetBal.doubleValue()));
								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								sweep.setMessage(sweep.getMessageTypeDr());
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;
								formatId = formatIdDr;
								
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
									sweep.setBookcode(sweep.getBookcodeDr());
								}
							}
						}

					}
					// Set the account1 object details based on subCount
					if (subCount == 0) {

						if ((acct2Sign
								.compareToIgnoreCase(SwtConstants.ALLIGNED_DEBIT) == 0)) {
							sweep
									.setReAlignedBalance(new Double(
											predictedBalance.doubleValue()
													+ (orginalSweepamount
															.doubleValue())));
							if (sweep.getSweepFrmbal().equals("External")) {
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()
												+ (orginalSweepamount
														.doubleValue())));
							} else {
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()));
							}
							sweep.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
							sweep.setMessage(sweep.getMessageTypeDr());
							formatId = formatIdDr;
							if(applySchedulerSweepsValues) {
								sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
								sweep.setBookcode(sweep.getBookcodeDr());
							}
							
						} else if ((acct2Sign
								.compareToIgnoreCase(SwtConstants.ALLIGNED_CREDIT) == 0)) {
							sweep
									.setReAlignedBalance(new Double(
											predictedBalance.doubleValue()
													+ (orginalSweepamount
															.doubleValue())));
							if (sweep.getSweepFrmbal().equals("External")) {
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()
												+ (orginalSweepamount
														.doubleValue())));
							} else {
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()));
							}

							sweep.setMessage(sweep.getMessageTypeCr());
							sweep.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
							formatId = formatIdCr;
							if(applySchedulerSweepsValues) {
								sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
								sweep.setBookcode(sweep.getBookcodeCr());
							}
						}
					}

					if (sweep.getReAlignedBalance().doubleValue() < 0) {
						sweep.setReAlignedBalanceSign(SwtConstants.DEBIT);
						sweep.setReAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getReAlignedBalance()
												.doubleValue())));
					} else {
						sweep.setReAlignedBalanceSign(SwtConstants.CREDIT);
						sweep.setReAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getReAlignedBalance()));
					}
					// Seting Re-Aligned External Balnce
					if (sweep.getReexAlignedBalance().doubleValue() < 0) {
						sweep.setReexAlignedBalanceSign(SwtConstants.DEBIT);
						sweep.setReexAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getReexAlignedBalance()
												.doubleValue())));
					} else {
						sweep.setReexAlignedBalanceSign(SwtConstants.CREDIT);
						sweep.setReexAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getReexAlignedBalance()));
					}
					// Get the authFlagCr,authFlagDr,authFlag from dao
					authFlagCr = sweepDetailDAO.getAuthFlag(entity[subCount],
							hostId, formatIdCr);
					authFlagDr = sweepDetailDAO.getAuthFlag(entity[subCount],
							hostId, formatIdDr);
					authFlag = sweepDetailDAO.getAuthFlag(entity[subCount],
							hostId, formatId);

					sweep.setAuthQueue(authFlag);
					sweep.setAuthFlagCr(authFlagCr);
					sweep.setAuthFlagDr(authFlagDr);

					accounts.add(sweep);
				}
			} else {

				for (int i = 0; i <= (collAccountDetails.size() - 1); i++) {
					acctmain = (AcctMaintenance) collAccountDetails.get(i);
					
					if(applySchedulerSweepsValues && collAcctSweepSchedule.get(0).getAccountId().equals(acctmain.getId().getAccountId())) {
						acctmain.setTargetbalance(collAcctSweepSchedule.get(0).getTargetBalance());
						acctmain.setTgtbalsign(collAcctSweepSchedule.get(0).getTargetBalanceType());
					}
					
					
					sweep = new SweepDetail();
					// Set the
					// CurrCode,Acctname,AccountId,MinSweepAmt,MaxSweepAmt,MinSweepAmtAsString,MaxSweepAmtAsString,CutOffTime,Bookcode
					sweep.setCurrCode(acctmain.getCurrcode());
					currencyCode = sweep.getCurrCode();
					sweep.setAcctname(acctmain.getAcctname());
					sweep.setAccountId(acctmain.getId().getAccountId());
					sweep.setMinSweepAmt(acctmain.getMinseepamt());
					sweep.setMaxSweepAmt(acctmain.getMaxsweepamte());
					sweep.setMinSweepAmtAsString(SwtUtil.formatCurrency(sweep
							.getCurrCode(), sweep.getMinSweepAmt()));
					sweep.setMaxSweepAmtAsString(SwtUtil.formatCurrency(sweep
							.getCurrCode(), sweep.getMaxSweepAmt()));
					sweep.setCutOffTime(acctmain.getCutoff());
					sweep.setBookcode(acctmain.getSweepbookcode());
					sweep.setDefaultSettleMethod(acctmain.getDefaultSettleMethod());
					
					sweep.setDefaultSettleMethodDr(acctmain.getDefaultSettleMethod());
					sweep.setDefaultSettleMethodCr(acctmain.getDefaultSettleMethod());
					sweep.setBookcodeDr(acctmain.getSweepbookcode());
					sweep.setBookcodeCr(acctmain.getSweepbookcode());
					if (acctmain.getAcctlevel().equalsIgnoreCase(
							SwtConstants.ACCT_MAIN)) {
						sweep.setAccountType(SwtConstants.ACCT_MAIN_VALUE);
					} else {
						sweep.setAccountType(SwtConstants.ACCT_SUB_VALUE);
					}

					sweep.setEntityId(entity[i]);

					String currId = acctmain.getCurrcode();

					if (acctmain.getSweepFrmbal().equals("P")) {
						sweep.setSweepFrmbal("Predicted");
					} else {
						sweep.setSweepFrmbal("External");
					}
					if (!SwtUtil.isEmptyOrNull(acctmain.getSubAcctim())) {
						sweep.setSubAcctim(acctmain.getSubAcctim());
					} else {
						sweep.setSubAcctim("N");
					}
					// Get the predict balances from DAO layer
					predictedBalance = sweepDetailDAO.getPredictBalance(
							entity[i], hostId, currId, acctmain.getId()
									.getAccountId(), valueDate, format);

					sweep.setPredictBalance(predictedBalance);

					if (predictedBalance.doubleValue() < 0) {
						sweep.setPredictBalanceSign(SwtConstants.DEBIT);
						sweep.setPredictBalanceAsString(SwtUtil.formatCurrency(
								sweep.getCurrCode(), new Double(0 - sweep
										.getPredictBalance().doubleValue())));
					} else {
						sweep.setPredictBalanceSign(SwtConstants.CREDIT);
						sweep
								.setPredictBalanceAsString(SwtUtil
										.formatCurrency(sweep.getCurrCode(),
												sweep.getPredictBalance()));
					}
					// Set the Re-Aligned External Balance in bean
					externalBalance = sweepDetailDAO.getExternalBalance(
							entity[i], hostId, currId, acctmain.getId()
									.getAccountId(), valueDate, format);
					sweep.setExternalBalance(externalBalance);
					if (externalBalance.doubleValue() < 0) {
						sweep.setExternalBalanceSign(SwtConstants.DEBIT);
						sweep.setExternalBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getExternalBalance()
												.doubleValue())));
					} else {
						sweep.setExternalBalanceSign(SwtConstants.CREDIT);
						sweep.setExternalBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getExternalBalance()));
					}
					sweep.setTargetBalanceAsString(SwtUtil.formatCurrency(sweep
							.getCurrCode(), acctmain.getTargetbalance()));
					targetSign = acctmain.getTgtbalsign();

					if (targetSign != null) {
						if (targetSign.equalsIgnoreCase(SwtConstants.DEBIT)) {
							sweep.setTargetBalanceSign(SwtConstants.DEBIT);
							targetBal = (new Double(0 - acctmain
									.getTargetbalance().doubleValue()));
						} else {
							sweep.setTargetBalanceSign(SwtConstants.CREDIT);
							targetBal = (acctmain.getTargetbalance());
						}
					} else {
						sweep.setTargetBalanceSign(SwtConstants.CREDIT);
						targetBal = (acctmain.getTargetbalance());
					}

					sweep.setTargetBalance(targetBal);
					// Instantiate the
					// mainCreditMessageType,mainDebitMessageType
					acct2CreditMessageType = new String[3];
					acct2DebitMessageType = new String[3];
					// Assign the newCrInt,newDrInt,newCrExt,newDrExt values in
					// mainCreditMessageType,mainDebitMessageType
					
					
					AcctSpecificSweepFormatDAO specficAccountDAO = (AcctSpecificSweepFormatDAO) SwtUtil
							.getBean("acctSpecificSweepFormatDAO");
				
					
					AcctMaintenance otherAccount  = null;
					if(i == 1) {
						otherAccount = collAccountDetails.get(0);
					}else {
						otherAccount = collAccountDetails.get(1);
					}
					
					AccountSpecificSweepFormat specificAccount =  specficAccountDAO.getAccountSpecificSweepFormat(hostId, acctmain.getId().getEntityId(), acctmain.getId().getAccountId(), otherAccount.getId().getEntityId(), otherAccount.getId().getAccountId());
					
					if(specificAccount == null) {

						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewCrInternal())) {
							acct2CreditMessageType[0] = acctmain
									.getAcctNewCrInternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewDrInternal())) {
							acct2DebitMessageType[0] = acctmain
									.getAcctNewDrInternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewCrExternal())) {
							acct2CreditMessageType[1] = acctmain
									.getAcctNewCrExternal();
						}
	
						if (!SwtUtil.isEmptyOrNull(acctmain.getAcctNewDrExternal())) {
							acct2DebitMessageType[1] = acctmain
									.getAcctNewDrExternal();
						}
						// Assign CreditExternalInter,DebitExternalInter in array
						acct2CreditMessageType[2] = acctmain
								.getCreditExternalInter() != null ? acctmain
								.getCreditExternalInter() : "";
						acct2DebitMessageType[2] = acctmain.getDebitExternalInter() != null ? acctmain
								.getDebitExternalInter()
								: "";
						// Set the separeted MessageTypeCr
						sweep.setMessageTypeCr(getJoinMessageType(" / ",
								acct2CreditMessageType));
						// Set the separeted MessageTypeDr
						sweep.setMessageTypeDr(getJoinMessageType(" / ",
								acct2DebitMessageType));
						formatIdCr = "'" + acctmain.getAcctNewCrInternal() + "','"
								+ acctmain.getAcctNewCrExternal() + "'";
						formatIdDr = "'" + acctmain.getAcctNewDrInternal() + "','"
								+ acctmain.getAcctNewDrExternal() + "'";
						
						
						if(applySchedulerSweepsValues) {
							if(collAcctSweepSchedule.get(0).getAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getEntityId().equals(acctmain.getId().getEntityId())) {
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getThisAccSettleMethodCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getThisAccSettleMethodDr());
								
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeDr());
								
								
							}
							
							if(collAcctSweepSchedule.get(0).getSweepAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getSweepAccountEntity().equals(acctmain.getId().getEntityId())) {
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodDr());
								
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeDr());
								
							}
							
							
							
						
						}
						
					
					}else {
						
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewInternalCrFormat())) {
							acct2CreditMessageType[0] = specificAccount.getNewInternalCrFormat();
							acctmain.setAcctNewCrInternal(specificAccount.getNewInternalCrFormat());
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewInternalDrFormat())) {
							acct2DebitMessageType[0] = specificAccount.getNewInternalDrFormat();
							acctmain.setAcctNewDrInternal(specificAccount.getNewInternalDrFormat());
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewExternalCrFormat())) {
							acct2CreditMessageType[1] = specificAccount.getNewExternalCrFormat();
							acctmain.setAcctNewCrExternal(specificAccount.getNewExternalCrFormat());
						}
	
						if (!SwtUtil.isEmptyOrNull(specificAccount.getNewExternalDrFormat())) {
							acct2DebitMessageType[1] = specificAccount.getNewExternalDrFormat();
							acctmain.setAcctNewDrExternal(specificAccount.getNewExternalDrFormat());
						}
						// Assign the
						// creditIntermediaryMsgType,debitIntermediaryMsgType in
						// String Array
						acct2CreditMessageType[2] = specificAccount.getNewExternalCrFormatInt() != null ? specificAccount.getNewExternalCrFormatInt() : "";
						acct2DebitMessageType[2] = specificAccount.getNewExternalDrFormatINt() != null ? specificAccount.getNewExternalDrFormatINt() : "";
						
						formatIdCr = "'" + specificAccount.getNewInternalCrFormat() + "','"
								+ specificAccount.getNewExternalCrFormat() + "'";
						formatIdDr = "'" + specificAccount.getNewInternalDrFormat() + "','"
								+ specificAccount.getNewExternalDrFormat() + "'";
						
						// Set the separeted MessageTypeCr
						sweep.setMessageTypeCr(getJoinMessageType(" / ",
								acct2CreditMessageType));
						// Set the separeted MessageTypeDr
						sweep.setMessageTypeDr(getJoinMessageType(" / ",
								acct2DebitMessageType));
						if(applySchedulerSweepsValues) {
							if(collAcctSweepSchedule.get(0).getAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getEntityId().equals(acctmain.getId().getEntityId())) {
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getThisAccSettleMethodCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getThisAccSettleMethodDr());
								
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getThisAccSweepBookcodeDr());

								
							}
							
							if(collAcctSweepSchedule.get(0).getSweepAccountId().equals(acctmain.getId().getAccountId()) && collAcctSweepSchedule.get(0).getSweepAccountEntity().equals(acctmain.getId().getEntityId())) {
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setDefaultSettleMethodCr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setDefaultSettleMethodDr(collAcctSweepSchedule.get(0).getOtherAccSettleMethodDr());
								
								// Set the the separeted MessageTypeCr in Sweep
								sweep.setBookcodeCr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeCr());
								// Set the the separeted MessageTypeDr in Sweep
								sweep.setBookcodeDr(collAcctSweepSchedule.get(0).getOtherAccSweepBookcodeDr());

							}
						
						}
								
					}
					
					// Set account1 details based on sub account count
					if (i == 0) {
						if (sweep.getSweepFrmbal().equals("External")) {
							// Set the account details based on
							// targetBal,externalBalance
							if (targetBal.doubleValue() > externalBalance
									.doubleValue()) {
								orginalSweepamount = new Double(externalBalance
										.doubleValue()
										- targetBal.doubleValue());

								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance
										- orginalSweepamount);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweep.setMessage(sweep.getMessageTypeCr());
								formatId = formatIdCr;
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
									sweep.setBookcode(sweep.getBookcodeCr());
								}
							} else {
								orginalSweepamount = new Double(externalBalance
										.doubleValue()
										- targetBal.doubleValue());
								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance
										- orginalSweepamount);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;
								sweep.setMessage(sweep.getMessageTypeDr());
								formatId = formatIdDr;
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
									sweep.setBookcode(sweep.getBookcodeDr());
								}
							}
						} else {
							// Set the account details based on
							// targetBal,externalBalance
							if (targetBal.doubleValue() > predictedBalance
									.doubleValue()) {
								orginalSweepamount = new Double(
										predictedBalance.doubleValue()
												- targetBal.doubleValue());
								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweep.setMessage(sweep.getMessageTypeCr());
								formatId = formatIdCr;
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
									sweep.setBookcode(sweep.getBookcodeCr());
								}
							} else {
								orginalSweepamount = new Double(
										predictedBalance.doubleValue()
												- targetBal.doubleValue());
								sweep.setReAlignedBalance(predictedBalance
										- orginalSweepamount);
								sweep.setReexAlignedBalance(externalBalance);
								sweep
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								sweep.setMessage(sweep.getMessageTypeDr());
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;
								formatId = formatIdDr;
								if(applySchedulerSweepsValues) {
									sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
									sweep.setBookcode(sweep.getBookcodeDr());
								}
							}
						}
						sweep.setAlignToTarget("Y");

					}
					// Set account2 details based on sub account count
					if (i == 1) {

						if ((acct2Sign
								.compareToIgnoreCase(SwtConstants.ALLIGNED_DEBIT) == 0)) {
							sweep
									.setReAlignedBalance(new Double(
											predictedBalance.doubleValue()
													+ (orginalSweepamount
															.doubleValue())));
							// Set the External Balnce
							if (sweep.getSweepFrmbal().equals("External"))
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()
												+ (orginalSweepamount
														.doubleValue())));
							else
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()));

							sweep.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
							sweep.setMessage(sweep.getMessageTypeDr());
							formatId = formatIdDr;
							if(applySchedulerSweepsValues) {
								sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodDr());
								sweep.setBookcode(sweep.getBookcodeDr());
							}
						} else if ((acct2Sign
								.compareToIgnoreCase(SwtConstants.ALLIGNED_CREDIT) == 0)) {
							sweep
									.setReAlignedBalance(new Double(
											predictedBalance.doubleValue()
													+ (orginalSweepamount
															.doubleValue())));

							if (sweep.getSweepFrmbal().equals("External"))
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()
												+ (orginalSweepamount
														.doubleValue())));
							else
								sweep.setReexAlignedBalance(new Double(
										externalBalance.doubleValue()));

							sweep.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
							sweep.setMessage(sweep.getMessageTypeCr());
							formatId = formatIdCr;
							if(applySchedulerSweepsValues) {
								sweep.setDefaultSettleMethod(sweep.getDefaultSettleMethodCr());
								sweep.setBookcode(sweep.getBookcodeCr());
							}
						}
					}

					if (sweep.getReAlignedBalance().doubleValue() < 0) {
						sweep.setReAlignedBalanceSign(SwtConstants.DEBIT);
						sweep.setReAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getReAlignedBalance()
												.doubleValue())));
					} else {
						sweep.setReAlignedBalanceSign(SwtConstants.CREDIT);
						sweep.setReAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getReAlignedBalance()));
					}

					// set the Re-aligned External Balnce
					if (sweep.getReexAlignedBalance().doubleValue() < 0) {
						sweep.setReexAlignedBalanceSign(SwtConstants.DEBIT);
						sweep.setReexAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(),
										new Double(0 - sweep
												.getReexAlignedBalance()
												.doubleValue())));
					} else {
						sweep.setReexAlignedBalanceSign(SwtConstants.CREDIT);
						sweep.setReexAlignedBalanceAsString(SwtUtil
								.formatCurrency(sweep.getCurrCode(), sweep
										.getReexAlignedBalance()));
					}
					// Get the AuthQueue,AuthFlagCr,AuthFlagDr from dao
					authFlagCr = sweepDetailDAO.getAuthFlag(entity[i], hostId,
							formatIdCr);
					authFlagDr = sweepDetailDAO.getAuthFlag(entity[i], hostId,
							formatIdDr);
					authFlag = sweepDetailDAO.getAuthFlag(entity[i], hostId,
							formatId);

					sweep.setAuthQueue(authFlag);
					sweep.setAuthFlagCr(authFlagCr);
					sweep.setAuthFlagDr(authFlagDr);

					accounts.add(sweep);

				}
			}

			sweepAmt = new SweepAmount();
			sweepAmt.setEntityId(entityId);
			if (orginalSweepamount < 0)
				orginalSweepamount = 0 - orginalSweepamount;

			sweepAmt.setOrigSweepAmt(orginalSweepamount);

			sweepAmt.setOrigSweepAmtAsString(SwtUtil.formatCurrency(
					currencyCode, sweepAmt.getOrigSweepAmt()));

			sdfCurrentDate = new SimpleDateFormat(formats.getDateFormatValue());

			sweepAmt.setValueDate(sdfCurrentDate.parse(valueDate));

			sweepAmt.setValueDateAsString(valueDate);
			
			sweepAmt.setScheduleSweepExist(collAcctSweepSchedule.size() == 1);
			sweepAmt.setScheduleSweepApplyed(applySchedulerSweepsValues);
			sweepAmt.setScheduleSweepDetailsAsString(applySchedulerSweepsValuesAsString);
			if(collAcctSweepSchedule.size() == 1)
				sweepAmt.setSelectedScheduleSweepOption("Account");
			
			
			sweepAmnts.add(sweepAmt);

			sweepDetailVO.setAccountDetail(accounts);
			sweepDetailVO.setSweepAmountDetail(sweepAmnts);
			

			if (isAcct1Sub.equalsIgnoreCase(SwtConstants.YES)) {
				sweepDetailVO.setAlignIndex(2);
			} else {
				sweepDetailVO.setAlignIndex(alignedAcct);
			}
			log
					.debug(this.getClass().getName()
							+ " -[getAccountDetails]- Exit");
		} catch (SwtException ex) {
			log
					.error("Exception Catch in SweepDetailManagerImpl.'getAccountDetails' method : "
							+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountDetails", SweepDetailManagerImpl.class);
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in SweepDetailManagerImpl.'getAccountDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountDetails", SweepDetailManagerImpl.class);
		} finally {
			// nullify the objects
			entity = null;
			accountId = null;
			currencyCode = null;
			hostId = null;
			accounts = null;
			sweepAmnts = null;
			acctmain = null;
			acctmain1 = null;
			acctmain2 = null;
			collAccountDetails = null;
			listAccountDetails = null;
			predictedBalance = null;
			targetBal = null;
			orginalSweepamount = null;
			acct2Sign = null;
			acct1CreditMessageType = null;
			acct1DebitMessageType = null;
			acct2CreditMessageType = null;
			acct2DebitMessageType = null;
			formatId = null;
			formatIdCr = null;
			formatIdDr = null;
			authFlag = null;
			authFlagCr = null;
			authFlagDr = null;
			externalBalance = null;
			itrAcctMaintenance = null;
			accLevel1 = null;
			accLevel2 = null;
			targetSign = null;
			isAcct1Sub = null;
			sweepAmt = null;
			format = null;
			sweep = null;
		}

		return sweepDetailVO;
	}

	/*
	 * End:Code modified by sudhakar For Mantis 1838:Message format not
	 * displayed accordingly when sweep direction changed
	 */
	/**
	 * This method is used to return the generate sweep id
	 * 
	 * @param sweepAct1
	 * @param sweepAct2
	 * @param sweepAmt
	 * @param currentSystemFormats
	 * @param roleId
	 * @param userId
	 * @return
	 */
	public int sweep(SweepDetail sweepAct1, SweepDetail sweepAct2,
			SweepAmount sweepAmt, SystemFormats currentSystemFormats,
			String roleId, String userId, Collection notesDetails)
			throws SwtException, Exception {
		// variable to hold generated sweep id.
		int generatedSweepId = 0;
//		// variable to hold action message
//		ActionMessage actionMessage = null;
		// variable to hold limit
		Double limit = null;
		// variable to hold hostid
		String hostId = null;
		// variable to hold entityid
		String entityId = null;
		// variable to hold format
		String format = null;
		// variable to hold return params
		List returnParams = null;
		// variable to hold sweepflag
		int sweepFlag;

		try {
			log.debug(this.getClass().getName() + " - [sweep] - Entering");
			// getting the sweep limit
			limit = sweepDetailDAO.getSweepLimit(sweepAct1.getCurrCode(),
					roleId);
			// get host id
			hostId = CacheManager.getInstance().getHostId();
			// get entity id
			entityId = sweepAct2.getEntityId();
			// checks the current system format
			if (currentSystemFormats.getDateFormatValue().equalsIgnoreCase(
					"MM/dd/yyyy")) {
				format = "M";
			} else {
				format = "D";
			}
			// check the limit not equal to null
			if (limit != null) {
				if (sweepAmt.getOrigSweepAmt().doubleValue() <= limit
						.doubleValue()) {
					// get the generated sweep details
					returnParams = sweepDetailDAO.generateSweep(sweepAct1,
							sweepAct2, sweepAmt, hostId, userId, format);
					// get sweep flag
					sweepFlag = Integer.parseInt((String) returnParams.get(0));
					// get the generated sweep id
					generatedSweepId = Integer.parseInt((String) returnParams
							.get(1));
					// checks sweep flag is equal to null
					if (sweepFlag == 0) {
//						actionMessage = new ActionMessage(
//								SwtConstants.SWEEP_SAVED);

						// save the sweep notes details into the db
						if (notesDetails != null) {
							Iterator itr = notesDetails.iterator();
							while (itr.hasNext()) {
								SweepNote sweepNote = (SweepNote) (itr.next());
								sweepNote.getId().setHostId(hostId);
//								sweepNote.getId().setEntityId(entityId);
								sweepNote.getId().setSweepId(
										new Long(generatedSweepId));

								sweepDetailDAO.saveNotesDetails(sweepNote);
							}
						}
						// checks the sweep flag
					} else if (sweepFlag == 1) {
						throw new SwtException("sweep.notSuccess", "N");
					} else if (sweepFlag == 3) {
						throw new SwtException("sweep.invalidDirectoryPath",
								"N");
					} else if (sweepFlag == 4) {
						throw new SwtException("sweep.accountTypeNotFound", "N");
					} else if (sweepFlag == 5) {
						throw new SwtException("sweep.errorinPredictBal", "N");
					} else if (sweepFlag == 6) {
						throw new SwtException("sweep.acctLevelNotFound", "N");
					} else if (sweepFlag == 7) {
						throw new SwtException("sweep.mainAcctNotFound", "N");
					} else if (sweepFlag == 8) {
						throw new SwtException("sweep.messgeFormatNotDefined",
								"N");
					} else if (sweepFlag == 9) {
						throw new SwtException("sweep.errorInsertingAlert", "N");
					} else if (sweepFlag == 10) {
						throw new SwtException("sweep.messageGenerationError",
								"N");
					} else {
					}
				} else {
					throw new SwtException("sweep.limitExceeded", "N");
				}
			} else {
				throw new SwtException("sweep.limitNotDefined", "N");
			}
			return generatedSweepId;

			/*
			 * Start:Code Modified for Mantis 1877 by sandeep kumar on
			 * 24-APR-12-For Manual sweeping: Unnecessary error written to
			 * server log when a sweep limit is not defined
			 */
		} catch (SwtException swtExp) {
			log
					.info("Exception Catch in SweepDetailManagerImpl.'sweep' method : "
							+ swtExp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(swtExp,
					"sweep", SweepDetailManagerImpl.class);
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepDetailManagerImpl.'sweep' method : "
							+ exp.getMessage());

			throw new Exception(exp.getMessage());

		} finally {
			// nullifying objects
			limit = null;
			hostId = null;
			entityId = null;
			format = null;
		}

		/*
		 * End:Code Modified for Mantis 1877 by sandeep kumar on 24-APR-12-For
		 * Manual sweeping: Unnecessary error written to server log when a sweep
		 * limit is not defined
		 */

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.SweepDetailManager#getSweepDetails(java.lang.String,
	 *      int, org.swallow.util.SystemFormats)
	 */
	public SweepDetailValObj getSweepDetails(String entityId, Long sweepId,
			SystemFormats currentSystemFormats, String sweepStatus)
			throws SwtException {
		SweepDetailValObj sweepDetailVO = new SweepDetailValObj();

		try {

			String hostId = CacheManager.getInstance().getHostId();
			List listSweepDetails = sweepDetailDAO.getSweepDetails(entityId,
					hostId, sweepId);

			Iterator itr = listSweepDetails.iterator();
			AcctMaintenance acctmain1 = new AcctMaintenance();
			AcctMaintenance acctmain2 = new AcctMaintenance();
			String alignedAcct = "";
			Sweep sweep = new Sweep();
			sweep = (Sweep) itr.next();

			int alignIndex = 0;
			String accId1 = "";
			String accId2 = "";
			String acclevel1 = sweep.getAccountCr().getAcctlevel();
			String acclevel2 = sweep.getAccountDr().getAcctlevel();

			if ((acclevel1.compareToIgnoreCase(acclevel2)) == 0) {

				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 1;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 1;
				}
			} else if (acclevel1.equals(SwtConstants.ACCT_MAIN)) {
				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 1;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 2;
				}
			} else {
				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 2;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 1;
				}
			}
			if (acctmain1.getSweepFrmbal().equals("P")) {
				acctmain1.setSweepFrmbal("Predicted");
			} else {
				acctmain1.setSweepFrmbal("External");
			}
			// Sub settiung sub acct time
			if (!SwtUtil.isEmptyOrNull(acctmain1.getSubAcctim())) {
				acctmain1.setSubAcctim(acctmain1.getSubAcctim());
			} else {
				acctmain1.setSubAcctim("N");
			}

			if (acctmain2.getSweepFrmbal().equals("P")) {
				acctmain2.setSweepFrmbal("Predicted");
			} else {
				acctmain2.setSweepFrmbal("External");
			}
			// Sub settiung sub acct time
			if (!SwtUtil.isEmptyOrNull(acctmain2.getSubAcctim())) {
				acctmain2.setSubAcctim(acctmain2.getSubAcctim());
			} else {
				acctmain2.setSubAcctim("N");
			}

			sweepDetailVO = getCalculatedAccounts(acctmain1, acctmain2, sweep,
					accId1, accId2, currentSystemFormats, entityId, hostId,
					sweepStatus);

			sweepDetailVO.setAlignIndex(alignIndex);
		} catch (Exception exp) {
			exp.printStackTrace();
			exp.printStackTrace();
			log
					.debug("Exception Catch in SweepDetailManagerImpl.'getSweepDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSweepDetails", SweepDetailManagerImpl.class);
		}

		return sweepDetailVO;
	}

	public SweepDetailValObj getSweepDetailsArchive(String entityId, Long sweepId,
			SystemFormats currentSystemFormats, String sweepStatus, String archiveId)
			throws SwtException {
		SweepDetailValObj sweepDetailVO = new SweepDetailValObj();

		try {

			String hostId = CacheManager.getInstance().getHostId();
			List listSweepDetails = sweepDetailDAO.getSweepDetailsArchive(entityId,
					hostId, sweepId, archiveId);

			Iterator itr = listSweepDetails.iterator();
			AcctMaintenance acctmain1 = new AcctMaintenance();
			AcctMaintenance acctmain2 = new AcctMaintenance();
			String alignedAcct = "";
			Sweep sweep = new Sweep();
			sweep = (Sweep) itr.next();

			int alignIndex = 0;
			String accId1 = "";
			String accId2 = "";
			String acclevel1 = sweep.getAccountCr().getAcctlevel();
			String acclevel2 = sweep.getAccountDr().getAcctlevel();

			if ((acclevel1.compareToIgnoreCase(acclevel2)) == 0) {

				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 1;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 1;
				}
			} else if (acclevel1.equals(SwtConstants.ACCT_MAIN)) {
				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 1;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 2;
				}
			} else {
				alignedAcct = sweep.getAlignAccountId();

				if (alignedAcct.equals(sweep.getAccountIdCr())) {
					acctmain1 = sweep.getAccountCr();
					acctmain2 = sweep.getAccountDr();
					accId1 = sweep.getAccountIdCr();
					accId2 = sweep.getAccountIdDr();
					alignIndex = 2;
				} else {
					acctmain1 = sweep.getAccountDr();
					acctmain2 = sweep.getAccountCr();
					accId1 = sweep.getAccountIdDr();
					accId2 = sweep.getAccountIdCr();
					alignIndex = 1;
				}
			}
			if (acctmain1.getSweepFrmbal().equals("P")) {
				acctmain1.setSweepFrmbal("Predicted");
			} else {
				acctmain1.setSweepFrmbal("External");
			}
			// Sub settiung sub acct time
			if (!SwtUtil.isEmptyOrNull(acctmain1.getSubAcctim())) {
				acctmain1.setSubAcctim(acctmain1.getSubAcctim());
			} else {
				acctmain1.setSubAcctim("N");
			}

			if (acctmain2.getSweepFrmbal().equals("P")) {
				acctmain2.setSweepFrmbal("Predicted");
			} else {
				acctmain2.setSweepFrmbal("External");
			}
			// Sub settiung sub acct time
			if (!SwtUtil.isEmptyOrNull(acctmain2.getSubAcctim())) {
				acctmain2.setSubAcctim(acctmain2.getSubAcctim());
			} else {
				acctmain2.setSubAcctim("N");
			}

			sweepDetailVO = getCalculatedAccounts(acctmain1, acctmain2, sweep,
					accId1, accId2, currentSystemFormats, entityId, hostId,
					sweepStatus);

			sweepDetailVO.setAlignIndex(alignIndex);
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.debug("Exception Catch in SweepDetailManagerImpl.'getSweepDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSweepDetails", SweepDetailManagerImpl.class);
		}

		return sweepDetailVO;
	}

	/**
	 * Method to calculate Origal Sweep Amount / Sweep Submitted Amount / Sweep
	 * Cancel Amount
	 * 
	 * @param acctmain1
	 * @param acctmain2
	 * @param sweep
	 * @param accId2
	 * @param accId1
	 * @param formats
	 * @param hostId
	 * @param entityId
	 * @param sweepStatus
	 * @return
	 * @throws SwtException
	 */
	private SweepDetailValObj getCalculatedAccounts(AcctMaintenance acctmain1,
			AcctMaintenance acctmain2, Sweep sweep, String accId1,
			String accId2, SystemFormats formats, String entityId,
			String hostId, String sweepStatus) throws SwtException {

		SweepDetailValObj sweepDetailVO = new SweepDetailValObj();
		AcctMaintenance acct = new AcctMaintenance();
		SweepAmount sweepAmt = new SweepAmount();
		ArrayList<SweepDetail> accounts = new ArrayList<SweepDetail>();
		ArrayList<SweepAmount> listSweepAmt = new ArrayList<SweepAmount>();
		Double predictedBal = null;
		Double externalBal = null;
		String acctId = "";
		Double targetBal = null;
		Double submitSweepAmt = null;
		String formatIdCr = "";
		String formatIdDr = "";
		String formatId = "";
		String acct2Sign = "";
		String authFlag = "";
		String sweepLevel = "";
		Double sweepedAmt = null;
		String newCrInt = "";
		String newDrInt = "";
		String newCrExt = "";
		String newDrExt = "";

		Date newDate = sweep.getValueDate();
		SimpleDateFormat sdf = new SimpleDateFormat();
		String todayDate = null;
		String targetSign = "";
		String format = "";

		try {
			log.debug(this.getClass().getName()
					+ "-[getCalculatedAccounts]-Entry");
			if (formats.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
				sdf = new SimpleDateFormat("MM/dd/yyyy");
				format = "M";
			} else {
				sdf = new SimpleDateFormat("dd/MM/yyyy");
				format = "D";
			}

			todayDate = sdf.format(newDate);

			for (int i = 0; i < 2; i++) {
				if (i == 0) {
					acct = acctmain1;
					acctId = accId1;
				} else {
					acct = acctmain2;
					acctId = accId2;
				}

				SweepDetail sweepDetail = new SweepDetail();
				sweepDetail.setAccountId(acctId);
				sweepDetail.setEntityId(acct.getId().getEntityId());

				sweepDetail.setAcctname(acct.getAcctname());

				if (acct.getAcctlevel()
						.equalsIgnoreCase(SwtConstants.ACCT_MAIN)) {
					sweepDetail.setAccountType(SwtConstants.ACCT_MAIN_VALUE);
				} else {
					sweepDetail.setAccountType(SwtConstants.ACCT_SUB_VALUE);
				}

				sweepDetail.setBookcode(acct.getSweepbookcode());
				sweepDetail.setSweepFrmbal(acct.getSweepFrmbal());
				sweepDetail.setCurrCode(acct.getCurrcode());
				sweepDetail.setCutOffTime(acct.getCutoff());
				sweepDetail.setMaxSweepAmt(acct.getMaxsweepamte());
				sweepDetail.setMaxSweepAmtAsString(SwtUtil
						.formatCurrency(sweepDetail.getCurrCode(), sweepDetail
								.getMaxSweepAmt()));
				if(!SwtUtil.isEmptyOrNull(sweep.getAccountIdOrigin()) && sweep.getAccountIdOrigin().equals(acctId)) {
					acct.setMinseepamt(sweep.getMinAmount());
					acct.setTgtbalsign(sweep.getTargetBalanceType());
					acct.setTargetbalance(sweep.getTargetBalance());
					
				}
				
				sweepDetail.setMinSweepAmt(acct.getMinseepamt());
				sweepDetail.setMinSweepAmtAsString(SwtUtil
						.formatCurrency(sweepDetail.getCurrCode(), sweepDetail
								.getMinSweepAmt()));
				targetSign = acct.getTgtbalsign();
				sweepDetail.setTargetBalanceAsString(SwtUtil.formatCurrency(
						sweepDetail.getCurrCode(), acct.getTargetbalance()));
				
				if (targetSign != null) {
					if (targetSign.equalsIgnoreCase(SwtConstants.DEBIT)) {
						sweepDetail.setTargetBalanceSign(SwtConstants.DEBIT);
						sweepDetail.setTargetBalance(new Double(0 - acct
								.getTargetbalance().doubleValue()));
						
						sweepDetail.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("acc.overdraft", UserThreadLocalHolder
								.getUserSession()));
					} else if (targetSign.equalsIgnoreCase(SwtConstants.ACCOUNT_ATTRIBUTE)) {
						sweepDetail.setTargetBalanceSign(sweep.getTargetBalance()>0?SwtConstants.CREDIT:SwtConstants.DEBIT);
						sweepDetail.setTargetBalance(sweep.getTargetBalance());
						sweepDetail.setTargetBalanceAsString(SwtUtil.formatCurrency(
								sweepDetail.getCurrCode(), Math.abs(acct.getTargetbalance())));
						sweepDetail.setTargetBalanceType(SwtConstants.ACCOUNT_ATTRIBUTE);
						sweepDetail.setTargetBalanceTypeId(sweep.getTargetBalanceTypId());
						sweepDetail.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("sweepdetail.accountattribute.label", UserThreadLocalHolder
								.getUserSession()));
					} else if (targetSign.equalsIgnoreCase(SwtConstants.RULE)) {
						sweepDetail.setTargetBalanceSign(sweep.getTargetBalance()>0?SwtConstants.CREDIT:SwtConstants.DEBIT);
						sweepDetail.setTargetBalance(sweep.getTargetBalance());
						sweepDetail.setTargetBalanceAsString(SwtUtil.formatCurrency(
								sweepDetail.getCurrCode(), Math.abs(acct.getTargetbalance())));
						sweepDetail.setTargetBalanceType(SwtConstants.RULE);
						sweepDetail.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("sweepdetail.rule.label", UserThreadLocalHolder
								.getUserSession()));
						sweepDetail.setTargetBalanceTypeId(sweep.getTargetBalanceTypId());
					}else {
						sweepDetail.setTargetBalanceSign(SwtConstants.CREDIT);
						sweepDetail.setTargetBalance(acct.getTargetbalance());
						sweepDetail.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("acc.credit", UserThreadLocalHolder
								.getUserSession()));
					}
				} else {
					sweepDetail.setTargetBalanceSign(SwtConstants.CREDIT);
					sweepDetail.setTargetBalance(acct.getTargetbalance());
				}

				predictedBal = sweepDetailDAO.getPredictBalance(acct.getId()
						.getEntityId(), hostId, sweepDetail.getCurrCode(),
						acctId, todayDate, format);

				sweepDetail.setPredictBalance(predictedBal);

				if (predictedBal.doubleValue() < 0) {
					sweepDetail.setPredictBalanceSign(SwtConstants.DEBIT);
					sweepDetail
							.setPredictBalanceAsString(SwtUtil.formatCurrency(
									sweepDetail.getCurrCode(), new Double(
											0 - sweepDetail.getPredictBalance()
													.doubleValue())));
				} else {
					sweepDetail.setPredictBalanceSign(SwtConstants.CREDIT);
					sweepDetail.setPredictBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									sweepDetail.getPredictBalance()));
				}
				// Assingning External Balance
				externalBal = sweepDetailDAO.getExternalBalance(acct.getId()
						.getEntityId(), hostId, sweepDetail.getCurrCode(),
						acctId, todayDate, format);
				sweepDetail.setExternalBalance(externalBal);

				// Set External Balnce in the Bean
				if (externalBal.doubleValue() < 0) {
					sweepDetail.setExternalBalanceSign(SwtConstants.DEBIT);
					sweepDetail
							.setExternalBalanceAsString(SwtUtil.formatCurrency(
									sweepDetail.getCurrCode(), new Double(
											0 - sweepDetail
													.getExternalBalance()
													.doubleValue())));
				} else {
					sweepDetail.setExternalBalanceSign(SwtConstants.CREDIT);
					sweepDetail.setExternalBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									sweepDetail.getExternalBalance()));
				}

				targetBal = sweepDetail.getTargetBalance();
				
				AcctSpecificSweepFormatDAO specficAccountDAO = (AcctSpecificSweepFormatDAO) SwtUtil
						.getBean("acctSpecificSweepFormatDAO");
				
				
				AcctMaintenance otherAccount  = null;
				if (i == 0) {
					otherAccount = acctmain2;
				} else {
					otherAccount = acctmain1;
				}
				
				AccountSpecificSweepFormat specificAccount =  specficAccountDAO.getAccountSpecificSweepFormat(hostId, acct.getId().getEntityId(), acctId, otherAccount.getId().getEntityId(), otherAccount.getId().getAccountId());
				

				if(sweep.getId().getSweepId() != null && sweep.getMovementIdCr() != null) {
					 
					 String usedSweepFromats = getSweepFormatFromSweep(""+sweep.getId().getSweepId());
					 
					 if(!SwtUtil.isEmptyOrNull(usedSweepFromats)){
						if(usedSweepFromats.split("@#@").length==2) {
							sweepDetail.setMessageTypeCr(usedSweepFromats.split("@#@")[0]);
							sweepDetail.setMessageTypeDr(usedSweepFromats.split("@#@")[1]);
						}
					 }
						
						
				}else {
					if(specificAccount == null) {
						if (acct.getAcctNewCrInternal() == null) {
							newCrInt = "                        ";
						} else {
							newCrInt = acct.getAcctNewCrInternal();
						}
	
						if (acct.getAcctNewDrInternal() == null) {
							newDrInt = "                        ";
						} else {
							newDrInt = acct.getAcctNewDrInternal();
						}
	
						if (acct.getAcctNewCrExternal() == null) {
							newCrExt = "                        ";
						} else {
							newCrExt = acct.getAcctNewCrExternal();
						}
	
						if (acct.getAcctNewDrExternal() == null) {
							newDrExt = "                        ";
						} else {
							newDrExt = acct.getAcctNewDrExternal();
						}
						
					}else {
						
						
						
						if (specificAccount.getNewInternalCrFormat() == null) {
							newCrInt = "                        ";
						} else {
							newCrInt = specificAccount.getNewInternalCrFormat();
						}
						acct.setAcctNewCrInternal(specificAccount.getNewInternalCrFormat());
						
	
						if (specificAccount.getNewInternalDrFormat() == null) {
							newDrInt = "                        ";
						} else {
							newDrInt = specificAccount.getNewInternalDrFormat();
						}
						acct.setAcctNewDrInternal(specificAccount.getNewInternalDrFormat());
						if (specificAccount.getNewExternalCrFormat() == null) {
							newCrExt = "                        ";
						} else {
							newCrExt = specificAccount.getNewExternalCrFormat();
						}
						acct.setAcctNewCrExternal(specificAccount.getNewExternalCrFormat());
						if (specificAccount.getNewExternalDrFormat() == null) {
							newDrExt = "                        ";
						} else {
							newDrExt = specificAccount.getNewExternalDrFormat();
						}
						acct.setAcctNewDrExternal(specificAccount.getNewExternalDrFormat());
					}
					
					sweepDetail.setMessageTypeCr(newCrInt + "   " + newCrExt);
					sweepDetail.setMessageTypeDr(newDrInt + "   " + newDrExt);
					
				}
				

				
				formatIdCr = "'" + acct.getAcctNewCrInternal() + "','"
						+ acct.getAcctNewCrExternal() + "'";

				formatIdDr = "'" + acct.getAcctNewDrInternal() + "','"
						+ acct.getAcctNewDrExternal() + "'";
				
				if ((sweepDetail.getAccountId()).equals(sweep
						.getAccountIdCr())) {
					sweepDetail.setDefaultSettleMethod(sweep.getSettleMethodCR());
					sweepDetail.setBookcode(sweep.getBookCodeCR());
					if("E".equalsIgnoreCase(sweep.getSweepFromBalanceTypeCr())) {
						
						acct.setSweepFrmbal("External");
					}else if("P".equalsIgnoreCase(sweep.getSweepFromBalanceTypeCr())) {
						acct.setSweepFrmbal("Predicted");
					}
					sweepDetail.setSweepFrmbal(acct.getSweepFrmbal());
				}else {
					sweepDetail.setDefaultSettleMethod(sweep.getSettleMethodDR());
					sweepDetail.setBookcode(sweep.getBookCodeDR());
					if("E".equalsIgnoreCase(sweep.getSweepFromBalanceTypeDr())) {
						
						acct.setSweepFrmbal("External");
					}else if("P".equalsIgnoreCase(sweep.getSweepFromBalanceTypeDr())) {
						acct.setSweepFrmbal("Predicted");
					}
					sweepDetail.setSweepFrmbal(acct.getSweepFrmbal());
				}

				
				// Condition to check the first account where aligned to target
				// is set
				if (i == 0) {
					// Condition tocheck the screen is opened from cancel queue
					if (sweepStatus.equals(SwtConstants.SWEEP_STATUS_CANCEL)) {

						sweepLevel = sweep.getSweepStatus();

						sweepDetail.setAlignToTarget("Y");

						if (sweepLevel.equals(SwtConstants.SWEEP_STATUS_NEW)) {

							sweepedAmt = sweep.getOriginalSweepAmt();
						}

						if (sweepLevel.equals(SwtConstants.SWEEP_STATUS_SUBMIT)) {

							sweepedAmt = sweep.getSubmitSweepAmt();

						}

						if (sweepLevel
								.equals(SwtConstants.SWEEP_STATUS_AUTHORIZE)) {

							sweepedAmt = sweep.getAuthorizeSweepAmt();
						}
						if (sweep.getAuthorizeSweepAmt() != null) {
							sweepedAmt = sweep.getAuthorizeSweepAmt();
						}

						if (sweep.getSubmitSweepAmt() != null) {
							sweepedAmt = sweep.getSubmitSweepAmt();
						} else {
							sweepedAmt = sweep.getOriginalSweepAmt();
						}

						if ((sweepDetail.getAccountId()).equals(sweep
								.getAccountIdCr())) {
							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
							acct2Sign = SwtConstants.ALLIGNED_DEBIT;

							sweepDetail.setReAlignedBalance(new Double(
									predictedBal.doubleValue()
											+ sweepedAmt.doubleValue()));

							if (sweepDetail.getSweepFrmbal().equals("External")) {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()
												+ sweepedAmt.doubleValue()));
							} else {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()));
							}
							formatIdCr = "'" + acct.getAcctNewCrInternal()
									+ "','" + acct.getAcctNewCrExternal() + "'";

							formatId = formatIdCr;
						} else {

							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
							acct2Sign = SwtConstants.ALLIGNED_CREDIT;

							sweepDetail.setReAlignedBalance(new Double(
									predictedBal.doubleValue()
											- sweepedAmt.doubleValue()));
							if (sweepDetail.getSweepFrmbal().equals("External")) {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()
												- sweepedAmt.doubleValue()));
							} else {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()));
							}
							formatIdDr = "'" + acct.getAcctNewDrInternal()
									+ "','" + acct.getAcctNewDrExternal() + "'";

							formatId = formatIdDr;
						}
					} else {// Screen is opened from Authorize or submit queue
						sweepDetail.setAlignToTarget("Y");

						// Condition to check account is external to set the
						// submit or authorize sweep amount
						if (sweepDetail.getSweepFrmbal().equals("External")) {
							// Condition to check target is greater than
							// external balance to set credit sign in alignement
							if (targetBal.doubleValue() > externalBal
									.doubleValue()) {
								if(targetSign.equalsIgnoreCase(SwtConstants.RULE)) {
									submitSweepAmt = sweep.getOriginalSweepAmt();
								}else {
									submitSweepAmt = new Double(externalBal
											.doubleValue()
											- targetBal.doubleValue());
								}
								sweepDetail
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweepDetail
										.setReAlignedBalance(new Double(
												predictedBal.doubleValue()
														- (submitSweepAmt
																.doubleValue())));
								if (sweepDetail.getSweepFrmbal().equals(
										"External")) {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()
															- (submitSweepAmt
																	.doubleValue())));
								} else {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()));
								}

								sweepDetail.setMessage(sweepDetail
										.getMessageTypeCr());

								formatId = formatIdCr;
							} else {// Condition to check target is lesser than
								// external balance to set debit sign in
								// alignement
								if(targetSign.equalsIgnoreCase(SwtConstants.RULE)) {
									submitSweepAmt = sweep.getOriginalSweepAmt();
								}else {
									submitSweepAmt = new Double(externalBal
											.doubleValue()
											- targetBal.doubleValue());
								}

								sweepDetail
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;

								sweepDetail
										.setReAlignedBalance(new Double(
												predictedBal.doubleValue()
														- (submitSweepAmt
																.doubleValue())));
								if (sweepDetail.getSweepFrmbal().equals(
										"External")) {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()
															- (submitSweepAmt
																	.doubleValue())));
								} else {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()));
								}

								sweepDetail.setMessage(sweepDetail
										.getMessageTypeDr());
								formatId = formatIdDr;
							}

						} else {
							// Condition to check target is greater than
							// predicted to set credit sign in alignement
							if (targetBal.doubleValue() > predictedBal
									.doubleValue()) {
								if(targetSign.equalsIgnoreCase(SwtConstants.RULE)) {
									submitSweepAmt = sweep.getOriginalSweepAmt();
								}else {
									submitSweepAmt = new Double(predictedBal
											.doubleValue()
											- targetBal.doubleValue());
								}
								sweepDetail
										.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);
								acct2Sign = SwtConstants.ALLIGNED_DEBIT;
								sweepDetail
										.setReAlignedBalance(new Double(
												predictedBal.doubleValue()
														- (submitSweepAmt
																.doubleValue())));
								if (sweepDetail.getSweepFrmbal().equals(
										"External")) {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()
															- (submitSweepAmt
																	.doubleValue())));
								} else {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()));
								}

								sweepDetail.setMessage(sweepDetail
										.getMessageTypeCr());

								formatId = formatIdCr;
							} else {// Condition to check target is greater than
								// predicted to set credit sign in
								// alignement
								if(targetSign.equalsIgnoreCase(SwtConstants.RULE)) {
									submitSweepAmt = sweep.getOriginalSweepAmt();
								}else {
									submitSweepAmt = new Double(predictedBal
											.doubleValue()
											- targetBal.doubleValue());
								}

								sweepDetail
										.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);
								acct2Sign = SwtConstants.ALLIGNED_CREDIT;

								sweepDetail
										.setReAlignedBalance(new Double(
												predictedBal.doubleValue()
														- (submitSweepAmt
																.doubleValue())));
								if (sweepDetail.getSweepFrmbal().equals(
										"External")) {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()
															- (submitSweepAmt
																	.doubleValue())));
								} else {
									sweepDetail
											.setReexAlignedBalance(new Double(
													externalBal.doubleValue()));
								}

								sweepDetail.setMessage(sweepDetail
										.getMessageTypeDr());
								formatId = formatIdDr;
							}
						}

					}
				} else {// balances calculation for main account (probably align
					// to target not checked)
					// condition tocheck screen is called from cancel queue
					// screen
					if (sweepStatus.equals(SwtConstants.SWEEP_STATUS_CANCEL)) {

						if (sweep.getAuthorizeSweepAmt() != null) {
							sweepedAmt = sweep.getAuthorizeSweepAmt();
						}

						if (sweep.getSubmitSweepAmt() != null) {
							sweepedAmt = sweep.getSubmitSweepAmt();
						} else {
							sweepedAmt = sweep.getOriginalSweepAmt();
						}

						if (acct2Sign.equals(SwtConstants.ALLIGNED_DEBIT)) {
							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);

							sweepDetail.setReAlignedBalance(new Double(
									predictedBal.doubleValue()
											- sweepedAmt.doubleValue()));

							if (sweepDetail.getSweepFrmbal().equals("External")) {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()
												- sweepedAmt.doubleValue()));
							} else {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()));
							}

							formatIdDr = "'" + acct.getAcctNewDrInternal()
									+ "','" + acct.getAcctNewDrExternal() + "'";

							formatId = formatIdDr;
						} else {
							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);

							sweepDetail.setReAlignedBalance(new Double(
									predictedBal.doubleValue()
											+ sweepedAmt.doubleValue()));
							if (sweepDetail.getSweepFrmbal().equals("External")) {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()
												+ sweepedAmt.doubleValue()));
							} else {
								sweepDetail.setReexAlignedBalance(new Double(
										externalBal.doubleValue()));
							}
							formatIdCr = "'" + acct.getAcctNewCrInternal()
									+ "','" + acct.getAcctNewCrExternal() + "'";

							formatId = formatIdCr;
						}
					} else {// Screen is opened from authorize or submit queue
						// Condition to check aligned sign is debit to calculate
						// the value

						sweepDetail.setReAlignedBalance(new Double(predictedBal
								.doubleValue()
								+ submitSweepAmt.doubleValue()));
						if (sweepDetail.getSweepFrmbal().equals("External")) {
							sweepDetail.setReexAlignedBalance(new Double(
									externalBal.doubleValue()
											+ submitSweepAmt.doubleValue()));
						} else {
							sweepDetail.setReexAlignedBalance(new Double(
									externalBal.doubleValue()));
						}

						if (acct2Sign.equals(SwtConstants.ALLIGNED_DEBIT)) {
							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_DEBIT);

							sweepDetail.setMessage(sweepDetail
									.getMessageTypeDr());
							formatId = formatIdDr;
						} else {
							sweepDetail
									.setAlignedSign(SwtConstants.ALLIGNED_CREDIT);

							sweepDetail.setMessage(sweepDetail
									.getMessageTypeCr());

							formatId = formatIdCr;
						}
					}

				}

				if (sweepDetail.getReAlignedBalance().doubleValue() < 0) {
					sweepDetail.setReAlignedBalanceSign(SwtConstants.DEBIT);
					sweepDetail.setReAlignedBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									new Double(0 - sweepDetail
											.getReAlignedBalance()
											.doubleValue())));
				} else {
					sweepDetail.setReAlignedBalanceSign(SwtConstants.CREDIT);
					sweepDetail.setReAlignedBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									sweepDetail.getReAlignedBalance()));
				}

				if (sweepDetail.getReexAlignedBalance().doubleValue() < 0) {
					sweepDetail.setReexAlignedBalanceSign(SwtConstants.DEBIT);
					sweepDetail.setReexAlignedBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									new Double(0 - sweepDetail
											.getReexAlignedBalance()
											.doubleValue())));
				} else {
					sweepDetail.setReexAlignedBalanceSign(SwtConstants.CREDIT);
					sweepDetail.setReexAlignedBalanceAsString(SwtUtil
							.formatCurrency(sweepDetail.getCurrCode(),
									sweepDetail.getReexAlignedBalance()));
				}

				authFlag = sweepDetailDAO.getAuthFlag(acct.getId()
						.getEntityId(), hostId, formatId);

				sweepDetail.setAuthQueue(authFlag);

				accounts.add(sweepDetail);
			}

			sweepAmt.setEntityId(entityId);
			sweepAmt.setOrigSweepAmt(sweep.getOriginalSweepAmt());
			sweepAmt.setOrigSweepAmtAsString(SwtUtil.formatCurrency(acctmain1
					.getCurrcode(), sweepAmt.getOrigSweepAmt()));

			if (submitSweepAmt != null && submitSweepAmt.doubleValue() < 0)
				submitSweepAmt = 0 - submitSweepAmt;

			if (sweepStatus.equals(SwtConstants.SWEEP_STATUS_NEW)) {
				sweepAmt.setSubSweepAmt(submitSweepAmt);
				sweepAmt.setSubSweepAmtAsString(SwtUtil.formatCurrency(
						acctmain1.getCurrcode(), sweepAmt.getSubSweepAmt()));
			} else if (sweepStatus.equals(SwtConstants.SWEEP_STATUS_SUBMIT)) {
				sweepAmt.setSubSweepAmt(sweep.getSubmitSweepAmt());

				sweepAmt.setSubSweepAmtAsString(SwtUtil.formatCurrency(
						acctmain1.getCurrcode(), sweepAmt.getSubSweepAmt()));

				if (sweep.getAuthorizeSweepAmt() != null) {
					sweepAmt.setAuthSweepAmt(sweep.getAuthorizeSweepAmt());
				} else {
					sweepAmt.setAuthSweepAmt(submitSweepAmt);
				}

				sweepAmt.setAuthSweepAmtAsString(SwtUtil.formatCurrency(
						acctmain1.getCurrcode(), sweepAmt.getAuthSweepAmt()));
			}

			if (sweepStatus.equals(SwtConstants.SWEEP_STATUS_CANCEL)) {
				sweepAmt.setSubSweepAmt(sweep.getSubmitSweepAmt());
				sweepAmt.setSubSweepAmtAsString(SwtUtil.formatCurrency(
						acctmain1.getCurrcode(), sweepAmt.getSubSweepAmt()));

				sweepAmt.setAuthSweepAmt(sweep.getAuthorizeSweepAmt());
				sweepAmt.setAuthSweepAmtAsString(SwtUtil.formatCurrency(
						acctmain1.getCurrcode(), sweepAmt.getAuthSweepAmt()));

			}
			sweepAmt.setAdditionalReference(sweep.getAdditionalReference());

			sweepAmt.setValueDate(sweep.getValueDate());
			sweepAmt.setValueDateAsString(SwtUtil.formatDate(sweepAmt
					.getValueDate(), formats.getDateFormatValue()));
			listSweepAmt.add(sweepAmt);

			sweepDetailVO.setAccountDetail(accounts);
			sweepDetailVO.setSweepAmountDetail(listSweepAmt);
			log.debug(this.getClass().getName()
					+ "-[getCalculatedAccounts]-Exit");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in SweepDetailManagerImpl.'getCalculatedAccounts' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCalculatedAccounts", SweepDetailManagerImpl.class);
		}

		return sweepDetailVO;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.SweepDetailManager#checkCutoff(org.swallow.work.model.SweepDetail,
	 *      org.swallow.work.model.SweepDetail)
	 */
	public boolean cutOffExeeded(SweepDetail sweepAct1, SweepDetail sweepAct2)
			throws SwtException {
		log.debug("Entering cutOffExeeded method");

		try {
			String entity1 = sweepAct1.getEntityId();
			String entity2 = sweepAct2.getEntityId();
			String hostId = CacheManager.getInstance().getHostId();

//			String offSet1 = sweepDetailDAO.getOffsetTime(entity1, hostId);
//			String offSet2 = sweepDetailDAO.getOffsetTime(entity2, hostId);
			
			String offSet1 = SwtUtil.getEntityOffsetTime( hostId, entity1);
			String offSet2 = SwtUtil.getEntityOffsetTime(hostId , entity2);

			String cutoff1 = sweepAct1.getCutOffTime();
			String cutoff2 = sweepAct2.getCutOffTime();

			cutoff1 = cutoff1.replace('+', '0');
			cutoff2 = cutoff2.replace('+', '0');

			int index = cutoff1.indexOf(":");
			String hour = cutoff1.substring(0, index);
			String minute = cutoff1.substring(index + 1, cutoff1.length());
			Calendar date1 = new GregorianCalendar();
			date1.setTime(SwtUtil.getSystemDatewithoutTime());
			date1.set(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			date1.set(Calendar.MINUTE, Integer.parseInt(minute));

			index = cutoff2.indexOf(":");
			hour = cutoff2.substring(0, index);
			minute = cutoff2.substring(index + 1, cutoff2.length());

			java.util.Calendar date2 = new java.util.GregorianCalendar();
			date2.setTime(SwtUtil.getSystemDatewithoutTime());
			date2.set(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			date2.set(Calendar.MINUTE, Integer.parseInt(minute));

			// sysdate + server time offset
			cutoff1 = offSet1;
			cutoff2 = offSet2;
			cutoff1 = cutoff1.replace('+', '0');
			cutoff2 = cutoff2.replace('+', '0');

			index = cutoff1.indexOf(":");
			hour = cutoff1.substring(0, index);
			minute = cutoff1.substring(index + 1, cutoff1.length());

			Calendar calnow1 = new GregorianCalendar();
			calnow1.setTime(SwtUtil.getSystemDatewithTime());
			calnow1.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calnow1.add(Calendar.MINUTE, Integer.parseInt(minute));

			index = cutoff2.indexOf(":");
			hour = cutoff2.substring(0, index);
			minute = cutoff2.substring(index + 1, cutoff2.length());

			Calendar calnow2 = new java.util.GregorianCalendar();
			calnow2.setTime(SwtUtil.getSystemDatewithTime());
			calnow2.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calnow2.add(Calendar.MINUTE, Integer.parseInt(minute));
			AcctMaintenance acctmain1 = new AcctMaintenance();
			AcctMaintenance acctmain2 = new AcctMaintenance();
			AcctMaintenance acctmain3 = new AcctMaintenance();
			List collAccountDetails1 = new ArrayList();
			List collAccountDetails2 = new ArrayList();
			List collAccountDetails3 = new ArrayList();
			collAccountDetails1 = (List) sweepDetailDAO.getAccountDetails(
					entity1, hostId, sweepAct1.getAccountId());
			acctmain1 = (AcctMaintenance) collAccountDetails1.iterator().next();
			collAccountDetails2 = (List) sweepDetailDAO.getAccountDetails(
					entity1, hostId, sweepAct1.getAccountId());
			acctmain2 = (AcctMaintenance) collAccountDetails1.iterator().next();
			collAccountDetails3 = (List) sweepDetailDAO.getAccountDetails(
					entity2, hostId, sweepAct2.getAccountId());
			acctmain3 = (AcctMaintenance) collAccountDetails3.iterator().next();

			if (!"Y".equals(acctmain1.getSubAcctim())
					&& !"Y".equals(acctmain3.getSubAcctim())) {

				if (calnow1.before(date1) && calnow2.before(date2)) {

					return true;
				} else {

					return false;
				}
			} else if (!"Y".equals(acctmain1.getSubAcctim())) {
				if (calnow2.before(date2)) {
					return true;
				} else {

					return false;
				}
			} else if (!"Y".equals(acctmain3.getSubAcctim())) {
				if (calnow1.before(date1)) {

					return true;
				} else {

					return false;
				}


			} else {
				if (calnow1.before(date1) && calnow2.before(date2)) {
					return true;
				} else {

					return false;
				}
			}

		} catch (Exception exp) {
			log
					.debug("Exception Catch in SweepDetailManagerImpl.'cutOffExeeded' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"cutOffExeeded", SweepDetailManagerImpl.class);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.SweepDetailManager#submit(org.swallow.work.model.SweepDetail,
	 *      org.swallow.work.model.SweepDetail,
	 *      org.swallow.work.model.SweepAmount, org.swallow.util.SystemFormats,
	 *      java.lang.String)
	 */
	public void submit(SweepDetail sweepAct1, SweepDetail sweepAct2,
			SweepAmount sweepAmt, SystemFormats currentSystemFormats,
			String roleId, Long sweepId, String userId, String process)
			throws SwtException {
		// log.debug("entering submit method");
		int out = 0;
		SystemFormats systemFormats = new SystemFormats();

		// Sweep swp=new Sweep();
		AcctMaintenance acctmain1 = new AcctMaintenance();
		AcctMaintenance acctmain2 = new AcctMaintenance();
		String entityId = sweepAmt.getEntityId();
		String hostId = CacheManager.getInstance().getHostId();

		// log.debug("Before Getting Sweep Detail");
		List listSweepDetails = sweepDetailDAO.getSweepDetails(entityId,
				hostId, sweepId);

		// log.debug("Getting Sweep Detail");
		Iterator itr = listSweepDetails.iterator();
		Sweep sweep = (Sweep) itr.next();

		// Sweep sweep = (Sweep) SwtUtil.copy(swp);
		acctmain1 = sweep.getAccountCr();
		acctmain2 = sweep.getAccountDr();

		String accountCr = sweep.getAccountIdCr();
		String accountCrDr = sweep.getAccountIdDr();

		String acctCr = "";
		String acctDr = "";
		String entityCr = "";
		String entityDr = "";
		String alignedAcct = "";
		String authFlagCr = "";
		String authFlagDr = "";
		
		String settlementMethodCr = "";
		String settlementMethodDr = "";
		
		String bookCr = "";
		String bookDr = "";

		// log.debug("Updating sweep");
		// log.debug("act 1 aligned sign "+sweepAct1.getAlignedSign());
		if ((sweepAct1.getAlignedSign() != null)
				&& (sweepAct1.getAlignedSign()
						.equals(SwtConstants.ALLIGNED_CREDIT))) {
			acctCr = sweepAct1.getAccountId();
			entityCr = sweepAct1.getEntityId();
			acctDr = sweepAct2.getAccountId();
			entityDr = sweepAct2.getEntityId();
			authFlagCr = sweepAct1.getAuthQueue();
			authFlagDr = sweepAct2.getAuthQueue();
			
			bookCr = sweepAct1.getBookcode();
			bookDr = sweepAct2.getBookcode();
			
			settlementMethodCr = sweepAct1.getDefaultSettleMethod();
			settlementMethodDr = sweepAct2.getDefaultSettleMethod();
		} else {
			acctCr = sweepAct2.getAccountId();
			entityCr = sweepAct2.getEntityId();
			acctDr = sweepAct1.getAccountId();
			entityDr = sweepAct1.getEntityId();
			authFlagCr = sweepAct2.getAuthQueue();
			authFlagDr = sweepAct1.getAuthQueue();
			
			bookCr = sweepAct2.getBookcode();
			bookDr = sweepAct1.getBookcode();
			
			settlementMethodCr = sweepAct2.getDefaultSettleMethod();
			settlementMethodDr = sweepAct1.getDefaultSettleMethod();
		}

		if (acctCr.equals(accountCr)) {
			// log.debug("account credit is-->"+acctCr);
			sweep.setAccountCr(acctmain1);
			sweep.setAccountDr(acctmain2);
		} else {
			sweep.setAccountCr(acctmain2);
			sweep.setAccountDr(acctmain1);
		}

		// log.debug("acct 1 aligned==>"+sweepAct1.getAlignToTarget());
		if ((sweepAct1.getAlignToTarget() != null)
				&& (sweepAct1.getAlignToTarget().equals(SwtConstants.YES))) {
			alignedAcct = sweepAct1.getAccountId();
		} else {
			alignedAcct = sweepAct2.getAccountId();
		}

		// log.debug("aligned acct==>"+alignedAcct);
		// sweep.setCutOffExceeded();
		Double sweepAmount = null;
		String currentQueue = "";

		if (process.equals("submit")) {
			sweepAmount = sweepAmt.getSubSweepAmt();
			currentQueue = SwtConstants.SWEEP_STATUS_NEW;
		} else {
			sweepAmount = sweepAmt.getAuthSweepAmt();
			currentQueue = SwtConstants.SWEEP_STATUS_SUBMIT;
		}

		// log.debug("Getting Bean sweepQueueDAO");
		SweepQueueDAO sweepQueueDAO = (SweepQueueDAO) SwtUtil
				.getBean("sweepQueueDAO");
//		ActionMessages errorList = new ActionMessages();
//		ActionMessage actionMessage = null;

		// log.debug("sweepAct1==>"+sweepAct1+"sweepAct2===>"+sweepAct1);
		try {
			// log.debug("Calling sweepDetailDAO function getSweepLimit");
			Double limit = sweepDetailDAO.getSweepLimit(
					sweepAct1.getCurrCode(), roleId);
			String msgStr1 = ""; // cutoff exceeded

			// String hostId=CacheManager.getInstance().getHostId();
			// log.debug("currentQ==>"+currentQueue);
			// log.debug("sweep status==>"+sweep.getSweepStatus());
			// log.debug("sweep==>"+sweep);
			if ((sweep.getSweepStatus().equalsIgnoreCase(currentQueue))
					|| ((sweep.getSweepStatus()
							.equals(SwtConstants.SWEEP_STATUS_NEW)) && (currentQueue
							.equals(SwtConstants.SWEEP_STATUS_SUBMIT)))) {
				if (limit != null) {
					if (sweepAmount.doubleValue() <= limit.doubleValue()) {
						// log.debug("sweep within limit");
						// log.debug("sweep to be submitted=="+sweep);
						sweep.setRequestRole(roleId);
						sweep.setRequestUser(userId);
						sweep.setAccountIdCr(acctCr);
						sweep.setAccountIdDr(acctDr);
						sweep.setEntityIdCr(entityCr);
						sweep.setEntityIdDr(entityDr);
						sweep.setAlignAccountId(alignedAcct);
						sweep.setSettleMethodCR(settlementMethodCr);
						sweep.setSettleMethodDR(settlementMethodDr);
						sweep.setBookCodeCR(bookCr);
						sweep.setBookCodeDR(bookDr);
						sweep.setAdditionalReference(sweepAmt.getAdditionalReference());

						if (process.equals("submit")) {
							sweep.setQueueName(SwtConstants.SWEEP_STATUS_NEW);
						} else {
							sweep
									.setQueueName(SwtConstants.SWEEP_STATUS_SUBMIT);
						}

						// log.debug("Before calling the Procedure");
						out = sweepDetailDAO.processSweep(sweep, systemFormats,
								sweepAmount, authFlagCr, authFlagDr);

						if (out == 0) {
							// log.debug("out Flag----->"+out);
//							actionMessage = new ActionMessage(
//									SwtConstants.SWEEP_SAVED);
						} else if (out == 1) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException("sweep.notSuccess", "N");
						} else if (out == 3) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException(
									"sweep.invalidDirectoryPath", "N");
						} else if (out == 4) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException("sweep.accountTypeNotFound",
									"N");
						} else if (out == 5) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException(
									"sweep.messgeFormatNotDefined", "N");
						} else if (out == 9) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException("sweep.errorInsertingAlert",
									"N");
						} else if (out == 10) {
							// log.debug("Throwing SwtException not saved");
							throw new SwtException(
									"sweep.messageGenerationError", "N");
						} else {
							// FTP the generated Message to App Server
							// Code commented to remove FTP handling starts

							/*
							 * try{ //log.debug("out Flag----->"+out);
							 * StartAutoSweeping startAutoSweeping = new
							 * StartAutoSweeping();
							 * startAutoSweeping.exportMessage();
							 * //log.debug("FTP generated Successfully from
							 * submit/authorize"); }catch(Exception ex){
							 * log.error("Error occured in FTP message from
							 * Submit: "+ex); try{ SwtErrorHandler
							 * swtErrorHandler = SwtErrorHandler.getInstance();
							 * SwtException swtexp =
							 * swtErrorHandler.handleException(ex,
							 * "submit",SweepDetailManagerImpl.class);
							 * //log.debug("Got The Exception in FTP the message - " +
							 * swtexp); }catch(Exception ex1){ //log.debug("Got
							 * The Exception in handleException - " + ex1);
							 * ex1.printStackTrace(); } }
							 */

							// Code commented to remove FTP handling ends
						}
					} else {
						// log.debug("Throwing SwtException");
						throw new SwtException("sweep.limitExceeded", "N");

						// return errorList;
					}
				} else {
					// log.debug("Throwing SwtException");
					throw new SwtException("sweep.limitNotDefined", "N");
				}
			} else {
				// actionMessage = new
				// ActionMessage(SwtConstants.SWEEP_AlRDY_SUBMIT);
				// errorList.add(SwtConstants.SWEEP_AlRDY_SUBMIT,actionMessage);
				// log.debug("Throwing SwtException already submitted");
				if (process.equals("submit")) {
					throw new SwtException("sweep.Submitted", "N");
				} else {
					throw new SwtException("sweep.alreadyAuthorised", "N");
				}
			}
		} catch (Exception exp) {
			log
					.debug("Exception Catch in SweepDetailManagerImpl.'sweep' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp, "sweep",
					SweepDetailManagerImpl.class);
		}
	}

	public Collection getsweepMessageMgrColl(String sweepId)
			throws SwtException {
		try {
			Collection sweepMessageColl = sweepDetailDAO
					.getsweepMessageDaoColl(sweepId);

			return sweepMessageColl;
		} catch (Exception exp) {
			log
					.debug("Exception Catch in SweepDetailManagerImpl.'getsweepMessageMgrColl' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getsweepMessageMgrColl", SweepDetailManagerImpl.class);
		}
	}

	/**
	 * This method is used to update the sweep details.
	 * 
	 * @param entityId
	 * @param sweepId
	 * @param authorizeSweepAmt
	 * @param currentUser
	 * @throws SwtException
	 */
	public void updateSweepDetails(String entityId, Long sweepId,
			SweepAmount sweepAmt,  SweepDetail sweepAct1, SweepDetail sweepAct2, String currentUser) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [updateSweepDetails] - Entering ");
		// local variable declaration.
		String hostId = null;
		List listSweepDetails = null;
		Iterator itr = null;
		Sweep sweep = null;
		try {
			// Getting the host id from the Cache manager.
			hostId = CacheManager.getInstance().getHostId();
			// Getting the sweep detail
			listSweepDetails = sweepDetailDAO.getSweepDetails(entityId, hostId,
					sweepId);
			// Iterating the sweep objects from the listSweepDetails
			itr = listSweepDetails.iterator();
			sweep = (Sweep) itr.next();
			// Setting the authorizeSweepAmt,currentUser to the sweep object.
			sweep.setAuthorizeSweepAmt(sweepAmt.getAuthSweepAmt());
			

			if (sweepAct1.getAccountId().equals(sweep.getAccountIdCr())) {
				sweep.setBookCodeCR(sweepAct1.getBookcode());
				sweep.setBookCodeDR(sweepAct2.getBookcode());
				
				
				sweep.setSettleMethodCR(sweepAct1.getDefaultSettleMethod());
				sweep.setSettleMethodDR(sweepAct2.getDefaultSettleMethod());
			} else {
				sweep.setBookCodeCR(sweepAct2.getBookcode());
				sweep.setBookCodeDR(sweepAct1.getBookcode());
				
				sweep.setSettleMethodCR(sweepAct2.getDefaultSettleMethod());
				sweep.setSettleMethodDR(sweepAct1.getDefaultSettleMethod());
				
			}
			sweep.setAdditionalReference(sweepAmt.getAdditionalReference());
			
			sweep.setUpdateUser(currentUser);
			// Invoking updateSweepDetails from sweepDetailDAO to update the
			// sweep details
			sweepDetailDAO.updateSweepDetails(sweep);
		} catch (SwtException exp) {
			// logging the exception
			log.debug(this.getClass().getName()
					+ "- [updateSweepDetails] - Exception " + exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [updateSweepDetails] - Exception " + exp.getMessage());
			// Printing exception trace.
			exp.printStackTrace();
			SwtException swtexp = SwtErrorHandler.getInstance()
					.handleException(exp, "updateSweepDetails",
							SweepDetailManagerImpl.class);
			throw swtexp;
		}
		log.debug(this.getClass().getName()
				+ "- [updateSweepDetails] - Exiting ");
	}

	/**
	 * This method is used to get the sweep object for the given entity id,
	 * sweep id.
	 * 
	 * @param entityId
	 * @param sweepId
	 * @return sweep object
	 * @throws SwtException
	 */
	public Sweep getSweepDetails(String entityId, Long sweepId)
			throws SwtException {
		// local variable declaration.
		String hostId = null;
		List listSweepDetails = null;
		Sweep sweep = null;
		Iterator itr = null;
		try {
			// Getting the host id from the Cache manager.
			hostId = CacheManager.getInstance().getHostId();
			// Getting the sweep detail
			listSweepDetails = sweepDetailDAO.getSweepDetails(entityId, hostId,
					sweepId);
			// Iterating the sweep objects from the listSweepDetails
			itr = listSweepDetails.iterator();
			sweep = (Sweep) itr.next();
			// Returning sweep object.
			return sweep;

		} catch (SwtException exp) {
			// logging the exception
			log.debug(this.getClass().getName()
					+ "- [updateSweepDetails] - Exception " + exp.getMessage());
			log.error(this.getClass().getName()
					+ "- [updateSweepDetails] - Exception " + exp.getMessage());
			// Printing exception trace.
			exp.printStackTrace();
			SwtException swtexp = SwtErrorHandler.getInstance()
					.handleException(exp, "updateSweepDetails",
							SweepDetailManagerImpl.class);
			throw swtexp;
		}
	}

	/*
	 * Start:Code added by sudhakar For Mantis 1838:Message format not displayed
	 * accordingly when sweep direction changed
	 */
	/**
	 * This method is used to separeted the three messagetype with ' / ' for the
	 * Mainaccount and subaccount
	 * 
	 * @param separator
	 * @param arrMessageType
	 * @return sweep object
	 * @throws SwtException
	 */
	private String getJoinMessageType(String separator,
			String... arrMessageType) throws SwtException {
		// String buffer to hold the messageType
		StringBuffer messageType = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[getJoinMessageType]- Entry");
			// Instantiate the messageType
			messageType = new StringBuffer();
			// Separeted the MessageType based on " / "
			for (String strMessageType : arrMessageType) {
				if (strMessageType != null && strMessageType.length() != 0) {
					if (messageType.length() > 0)
						messageType.append(separator);
					// Append the message type
					messageType.append(strMessageType);
				}
			}
			log.debug(this.getClass().getName()
					+ " -[getJoinMessageType]- Exit");
			return messageType.toString();
		} catch (Exception exp) {
			log
					.error("Exception Catch in SweepDetailManagerImpl.'joinMessageType' method : "
							+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		}

	}
	/*
	 * End:Code added by sudhakar For Mantis 1838:Message format not displayed
	 * accordingly when sweep direction changed
	 */
	
	
	/**
	 * get used sweep formats for selected sweep id 
	 * @param sweepId
	 * @return
	 * @throws SwtException
	 */
	public String getSweepFormatFromSweep(String sweepId) throws SwtException{
			try {
				return sweepDetailDAO
						.getSweepFormatFromSweep(sweepId);

			} catch (Exception exp) {
				log
						.debug("Exception Catch in SweepDetailManagerImpl.'getSweepFormatFromSweep' method : "
								+ exp.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exp,
						"getSweepFormatFromSweep", SweepDetailManagerImpl.class);
			}
	}
}