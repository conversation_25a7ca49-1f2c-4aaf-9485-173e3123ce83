/*
 * @(#)ScenarioSummaryManagerImpl.java 1.0 24/12/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.work.dao.ScenarioSummaryDAO;
import org.swallow.work.model.AlertInstance;
import org.swallow.work.model.AlertTreeVO;
import org.swallow.work.model.EntityScenarioCount;
import org.swallow.work.model.ScenarioAlertCount;
import org.swallow.work.model.ScenarioInstanceLog;
import org.swallow.work.model.ScenarioInstanceMessage;
import org.swallow.work.model.ScenariosSummary;
import org.swallow.work.service.ScenarioSummaryManager;
@Component("scenarioSummaryManager")
public class ScenarioSummaryManagerImpl implements
			ScenarioSummaryManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(ScenarioSummaryManagerImpl.class);

	/**
	 * Initializing InputExceptionsDataDAO object
	 */
	@Autowired
	private ScenarioSummaryDAO scenarioSummaryDAO = null;



	/**
	 * @param scenarioSummaryDAO the scenarioSummaryDAO to set
	 */
	public void setScenarioSummaryDAO(ScenarioSummaryDAO scenarioSummaryDAO) {
		this.scenarioSummaryDAO = scenarioSummaryDAO;
	}
	
	/**
	 * Return the number of existing counts grouped by Entity and Currency
	 * @param scenarioId
	 * @param roleId
	 * @param thresholdFlag
	 * @param groupingSummary
	 * @return
	 * @throws SwtException
	 */
		public ArrayList<EntityScenarioCount> getScenarioCountByEntituCurrency(String scenarioId,String roleId,String thresholdFlag,String groupingSummary, String entityId,String selectedSort,String selectedCurrencyGroup,String isAlertable,  String selectedFilter) throws SwtException 		
		{
			ArrayList<EntityScenarioCount> result=null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getScenarioCountByEntituCurrency] - Entry");
			result=scenarioSummaryDAO.getScenarioCountByEntituCurrency(scenarioId, roleId, thresholdFlag, groupingSummary,  entityId,selectedSort,selectedCurrencyGroup,isAlertable, selectedFilter);
			log.debug(this.getClass().getName()
					+ "- [getScenarioCountByEntituCurrency] - Exit");
			return result;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getScenarioCountByEntituCurrency] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenarioCountByEntituCurrency] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScenarioCountByEntituCurrency", ScenarioSummaryManagerImpl.class);
		}
	}
	/**
	 * Return the number of existing record for each Scenario from P_SCENARIO_COUNTS
	 * @param baseQuery
	 * @return integer
	 * @throws SwtException 
	 * 
	 */
		public ScenariosSummary getScenariosSummaryInfoDetails(String roleId,String entityId,String threshold,String hideZero,String alertable,String selectedCurrencyGroup,String callOption, String selectedTab) throws SwtException {
		ScenariosSummary result=null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getScenariosSummaryInfoDetails] - Entry");
			result=scenarioSummaryDAO.getScenariosSummaryInfoDetails( roleId,entityId,threshold,hideZero,alertable,selectedCurrencyGroup,callOption, selectedTab);
			log.debug(this.getClass().getName()
					+ "- [getScenariosSummaryInfoDetails] - Exit");
			return result;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getScenariosSummaryInfoDetails] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenariosSummaryInfoDetails] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScenariosSummaryInfoDetails", ScenarioSummaryManagerImpl.class);
		}
	
	}
	/**
	 * Return the number of existing record for each Scenario from P_SCENARIO_COUNTS
	 * @param baseQuery
	 * @return integer
	 * @throws SwtException 
	 * 
	 */
		public AlertTreeVO getAlertsScenarioCount(AlertInstance instance) throws SwtException {
			AlertTreeVO result=null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getScenariosSummaryInfoDetails] - Entry");
			result=scenarioSummaryDAO.getAlertsScenarioCount(instance);
			log.debug(this.getClass().getName()
					+ "- [getScenariosSummaryInfoDetails] - Exit");
			return result;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getScenariosSummaryInfoDetails] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenariosSummaryInfoDetails] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScenariosSummaryInfoDetails", ScenarioSummaryManagerImpl.class);
		}
	
	}
		
	 /**
	 * Return the lastRun time of the selected scenario
	 * @param scenarioId
	 * @return String
	 * @throws SwtException
	 */
	public String getSelectedScenarioLastRun(String scenarioId)  throws SwtException {
		String result=null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getSelectedScenarioLastRun] - Entry");
			result=scenarioSummaryDAO.getSelectedScenarioLastRun(scenarioId);
			log.debug(this.getClass().getName()
					+ "- [getSelectedScenarioLastRun] - Exit");
			return result;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getSelectedScenarioLastRun] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getSelectedScenarioLastRun] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSelectedScenarioLastRun", ScenarioSummaryManagerImpl.class);
		}
	
	}

	/**
	 * Get the access to the facility screen
	 * 
	 * @param hostId
	 * @param facilityId
	 * @param roleId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String facilityId,
			String roleId) throws SwtException {
		String facilityAccess = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getFacilityAccess] - Entry");
			facilityAccess = scenarioSummaryDAO.getFacilityAccess(hostId, facilityId, roleId);
			log.debug(this.getClass().getName()
					+ "- [getFacilityAccess] - Exit");
			return facilityAccess;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getFacilityAccess", ScenarioSummaryManagerImpl.class);
		}
	}
	
	/**
	 * get instance list based on some criteria
	 * @param instanceCriteria
	 * @return
	 * @throws SwtException
	 */
	public List<AlertInstance> getInstanceList(AlertInstance instanceCriteria) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getInstanceList] - Entry");
			return scenarioSummaryDAO.getInstanceList(instanceCriteria);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getInstanceList] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstanceList] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInstanceList", ScenarioSummaryManagerImpl.class);
		}
	}
	
	
	public void updateScenInstanceStatus(String id, String status) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [updateScenInstanceStatus] - Entry");
			scenarioSummaryDAO.updateScenInstanceStatus(id, status);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [updateScenInstanceStatus] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateScenInstanceStatus] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateScenInstanceStatus", ScenarioSummaryManagerImpl.class);
		}
	}
	
	public List<ScenarioInstanceLog>  getInstanceLogs(String instanceId) throws SwtException{
		try {
			log.debug(this.getClass().getName()
					+ "- [getInstanceLogs] - Entry");
			return scenarioSummaryDAO.getInstanceLogs(instanceId);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getInstanceLogs] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getInstanceLogs] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInstanceLogs", ScenarioSummaryManagerImpl.class);
		}
	}
	
	/**
	 * This method is used to get scenario instance access from P_NOTIFY_SCENARIO
	 * table
	 */
	public String getScenarioInstAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [getScenarioInstAccess] - Entry");
			return scenarioSummaryDAO.getScenarioInstAccess(scenarioId, hostId, roleId, entityId);
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [getScenarioInstAccess] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName() + " - Exception Catched in [getScenarioInstAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp, "getScenarioInstAccess",
					ScenarioSummaryManagerImpl.class);
		}

	}
	
	/**
	 * This method is used to get scenario facility from p_scenario table
	 */
	public String getScenarioFacility(String scenarioId) throws SwtException{
		log.debug(this.getClass().getName() + " - getScenarioFacility () - " + " Entry ");
		return scenarioSummaryDAO.getScenarioFacility(scenarioId);
		
	}

	public String getInstAttXml(String instanceId) throws SwtException{
		log.debug(this.getClass().getName() + " - getInstAttXml () - " + " Entry ");
		return scenarioSummaryDAO.getInstAttXml(instanceId);
		
	}
	
	public AlertInstance getInstanceNewData(String instanceId) throws SwtException{
		log.debug(this.getClass().getName() + " - getInstanceNewData () - " + " Entry ");
		return scenarioSummaryDAO.getInstanceNewData(instanceId);
	}
	
	public ArrayList<ScenarioInstanceMessage> getInstanceMessages(String instanceId) throws SwtException{
		log.debug(this.getClass().getName() + " - getInstanceMessages () - " + " Entry ");
		return scenarioSummaryDAO.getInstanceMessages(instanceId);	
	}
	
	public String getHostName(String hostId) throws SwtException{
		log.debug(this.getClass().getName() + " - getHostName () - " + " Entry ");
		return scenarioSummaryDAO.getHostName(hostId);	
	}	
	/**
	 * This method is used to get scenario required access from P_NOTIFY_SCENARIO
	 * table
	 */
	public String getScenarioReqAccess(String scenarioId, String hostId, String roleId, String entityId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [getScenarioInstAccess] - Entry");
			return scenarioSummaryDAO.getScenarioReqAccess(scenarioId, hostId, roleId, entityId);
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [getScenarioReqAccess] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName() + " - Exception Catched in [getScenarioReqAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp, "getScenarioReqAccess",
					ScenarioSummaryManagerImpl.class);
		}

	}
}