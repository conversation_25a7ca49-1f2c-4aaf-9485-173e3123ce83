/**
 * @(#)AcctBreakdownMonitorManagerImpl.java 1.0 / Mar 7, 2012
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.AcctBreakdownMonitorDAO;
import org.swallow.work.model.AcctBreakdownModel;
import org.swallow.work.model.AcctBreakdownMonitor;
import org.swallow.work.service.AcctBreakdownMonitorManager;

/**
 * AcctBreakdownMonitorManagerImpl.java
 * 
 * <pre>
 * This class performs Account Breakdown Monitor related 
 * business logic, like
 * 
 * - Get balance type
 * - Get account details
 * - Get account breakdown balance details
 * - Update account details
 * </pre>
 * 
 * <AUTHOR> R / Mar 7, 2012
 * @version SmartPredict-1054
 */
@Component("acctBreakdownMonitorManager")
public class AcctBreakdownMonitorManagerImpl implements
		AcctBreakdownMonitorManager {

	/**
	 * logger object for logging
	 */
	private static final Log log = LogFactory
			.getLog(AcctBreakdownMonitorManagerImpl.class);
	/**
	 * Implementation of AcctBreakdownMonitorDAO to perform database operation
	 */
	@Autowired
	private AcctBreakdownMonitorDAO acctBreakdownMonitorDAO = null;

	/**
	 * Setter method of acctBreakdownMonitorDAO
	 * 
	 * @param acctBreakdownMonitorDAO
	 *            the acctBreakdownMonitorDAO to set
	 */
	public void setAcctBreakdownMonitorDAO(
			AcctBreakdownMonitorDAO acctBreakdownMonitorDAO) {
		this.acctBreakdownMonitorDAO = acctBreakdownMonitorDAO;
	}

	/**
	 * This method returns list of Balance Type
	 * 
	 * @return List<LabelValueBean>
	 * @throws SwtException
	 */
	public List<LabelValueBean> getBalanceType() throws SwtException {
		// To hold balance type dropdown value
		List<LabelValueBean> lstBalanceType = null;

		try {
			// log debug message
			log
					.debug(this.getClass().getName()
							+ " - [getBalanceType] - Enter");
			// Initialize list to hold balance type value
			lstBalanceType = new ArrayList<LabelValueBean>();
			// Add balance type values
			lstBalanceType.add(new LabelValueBean(
					SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED,
					SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED));
			lstBalanceType.add(new LabelValueBean(
					SwtConstants.ACCT_MONITOR_BALTYPE_UNSETTLED,
					SwtConstants.ACCT_MONITOR_BALTYPE_UNSETTLED));
			lstBalanceType.add(new LabelValueBean(
					SwtConstants.ACCT_MONITOR_BALTYPE_UNEXPECTED,
					SwtConstants.ACCT_MONITOR_BALTYPE_UNEXPECTED));
			lstBalanceType.add(new LabelValueBean(
					SwtConstants.ACCT_MONITOR_BALTYPE_LORO,
					SwtConstants.ACCT_MONITOR_BALTYPE_LORO));
			lstBalanceType.add(new LabelValueBean(
					SwtConstants.ACCT_MONITOR_BALTYPE_EXTERNAL,
					SwtConstants.ACCT_MONITOR_BALTYPE_EXTERNAL));
			// Return list of balance type
			return lstBalanceType;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getBalanceType] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getBalanceType", AcctBreakdownMonitorManagerImpl.class);
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getBalanceType] - Exit");
		}
	}

	/**
	 * This method gets account details belongs to selected entity, currency and
	 * account class. If "All" currency option is selected by the user, no need
	 * to fetch currency details. Simply show "All" Account option
	 * 
	 * @param acctBreakdownMonitor
	 * @return Collection<LabelValueBean>
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAcctList(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
		// Holds list of account details as LabelValueBean
		Collection<LabelValueBean> colAcct = null;
		// Iterate through account list and convert them as LabelValueBean
		// object list
		Iterator<AcctMaintenance> itrAcct = null;
		// To hold details of an account
		AcctMaintenance acctMaintenance = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAcctList] - Enter");

			// Initialize collection to hold account details as LabelValueBean
			colAcct = new ArrayList<LabelValueBean>();
			// Add "All" option
			colAcct.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
			// For "All" currency is selected, no need to fetch currency
			// details. Simply show "All" option in account dropdown control
			if (!acctBreakdownMonitor.getCurrencyCode().equals(
					SwtConstants.ALL_VALUE)) {
				// Get currency details belongs to selected entity, currency and
				// account class
				itrAcct = acctBreakdownMonitorDAO.getAcctList(
						acctBreakdownMonitor).iterator();
				// Iterate through account list and convert them as
				// LabelValueBean object list
				while (itrAcct.hasNext()) {
					acctMaintenance = itrAcct.next();
					colAcct.add(new LabelValueBean(acctMaintenance
							.getAcctname(), acctMaintenance.getId()
							.getAccountId()));
				}
			}
			return colAcct;
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAcctList] - SwtException: " + ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAcctList] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAcctList", this.getClass());
		} finally {
			// nullify objects
			itrAcct = null;
			acctMaintenance = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getAcctList] - Exit");
		}
	}

	/**
	 * This method is used to get account breakdown monitor details based on the
	 * selected criteria. First this method checks whether the Monitor Job is
	 * running or not. If its running then it will indicate user that job is
	 * running, wait for some time. Otherwise it displays data to the user
	 * 
	 * @param acctBreakdownMonitor
	 * @return boolean - job flag
	 * @throws SwtException
	 */
	public void getAcctBreakdownBalances(
			AcctBreakdownMonitor acctBreakdownMonitor) throws SwtException {
		// Flag denotes whether the job(build data from main table to monitor
		// table) is running or not
		boolean jobFlag;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAcctBreakdownBalances] - Enter");
			// Get flag value, whether the job is running or not
			jobFlag = SwtUtil.getMonitorJobFlag(acctBreakdownMonitor
					.getEntityId(), SwtConstants.JOB_PROCESS_MONITOR);
			// Set job flag
			acctBreakdownMonitor.setDataBuildStatus(String.valueOf(jobFlag));
			// If jobFlag is true, which means job is not running, so get the
			// balances
			if (jobFlag) {
				acctBreakdownMonitorDAO
						.getAllBalancesUsingStoredProc(acctBreakdownMonitor);
			} else {
				// job is running, so set the flag
				acctBreakdownMonitor
						.setAcctBreakdownList(new ArrayList<AcctBreakdownModel>());
				// Set total value as 0
				// Set predicted balance total and sign flag
				acctBreakdownMonitor.setPredBalanceTotal(SwtUtil
						.formatCurrency(acctBreakdownMonitor.getCurrencyCode(),
								0d));
				acctBreakdownMonitor
						.setPredBalanceTotalSign(SwtConstants.STR_TRUE);
				// Set starting balance total and sign flag
				acctBreakdownMonitor.setStartingBalanceTotal(SwtUtil
						.formatCurrency(acctBreakdownMonitor.getCurrencyCode(),
								0d));
				acctBreakdownMonitor
						.setStartingBalanceTotalSign(SwtConstants.STR_TRUE);
				// Set unexpected balance total and sign flag
				acctBreakdownMonitor.setUnexpectedBalanceTotal(SwtUtil
						.formatCurrency(acctBreakdownMonitor.getCurrencyCode(),
								0d));
				acctBreakdownMonitor
						.setUnexpectedBalanceTotalSign(SwtConstants.STR_TRUE);
			}
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAcctBreakdownBalances] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAcctBreakdownBalances] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAcctBreakdownBalances", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [getAcctBreakdownBalances] - Exit");
		}
	}

	/**
	 * This method is used to update account details, like sum flag or loro to
	 * predicted flag
	 * 
	 * @param hostId
	 * @param entityId
	 * @param acctId
	 * @param sumFlag
	 * @param loroToPredicted
	 * @throws SwtException
	 */
	public void updateAccount(String hostId, String entityId, String acctId,
			String sumFlag, String loroToPredicted) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [updateAccount] - Enter");
			// Update account details
			acctBreakdownMonitorDAO.updateAccount(hostId, entityId, acctId,
					sumFlag, loroToPredicted);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [updateAccount] - SwtException: " + ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [updateAccount] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateAccount", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [updateAccount] - Exit");
		}

	}
}