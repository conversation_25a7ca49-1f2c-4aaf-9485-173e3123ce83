/*
 * @(#)GenericDisplayMonitorManagerImpl.java 1.0 30/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.service.impl.ScenMaintenanceManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.work.dao.GenericDisplayMonitorDAO;
import org.swallow.work.model.GenericDisplayPageDTO;
import org.swallow.work.model.QueryResult;
import org.swallow.work.service.GenericDisplayMonitorManager;
@Component("genericDisplayMonitorManager")
public class GenericDisplayMonitorManagerImpl implements
		GenericDisplayMonitorManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(GenericDisplayMonitorManagerImpl.class);

	@Autowired
	private GenericDisplayMonitorDAO genericDisplayMonitorDAO = null;

	/**
	 * @param genericDisplayMonitorDAO the genericDisplayMonitorDAO to set
	 */
	public void setGenericDisplayMonitorDAO( 
			GenericDisplayMonitorDAO genericDisplayMonitorDAO) {
		this.genericDisplayMonitorDAO = genericDisplayMonitorDAO;
	}

	/**
	 * Return the queryResult related on a page and baseQuery
	 * 
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @return QueryResult
	 * @throws SwtException
	 */
	public QueryResult getQueryResultPage(
			GenericDisplayPageDTO page, OpTimer opTimer,String baseQuery,String scenarioId, String roleId,String currencyGroup,String applyCurrencyThreshold,boolean...fromExport)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [getQueryResultPage] -"
				+ "Entry - Return QueryResult");
		return genericDisplayMonitorDAO.getQueryResultPage(page, opTimer,baseQuery,scenarioId,roleId,currencyGroup,applyCurrencyThreshold,fromExport);
	}

	/**
	 * Return the number of existing record result of the Base Query
	 * 
	 * @param baseQuery
	 * @return String
	 * @throws SwtException
	 */
	public String getRecords(String baseQuery) throws SwtException {
		
		String queryResult=null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getRecords] - Entry");
			queryResult=genericDisplayMonitorDAO.getRecords(baseQuery);
			log.debug(this.getClass().getName()
					+ "- [getRecords] - Exit");
			return queryResult;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getRecords] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRecords] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRecords", ScenMaintenanceManagerImpl.class);
		}
	}
	
	/**
	 * Return the number of existing record result of the Base Query
	 * 
	 * @param baseQuery
	 * @param filter
	 * @return QueryResult
	 * @throws SwtException
	 */
	public QueryResult getGenericDisplayData(String baseQuery) throws SwtException {
		
		QueryResult queryResult=null;
		
		try {
			log.debug(this.getClass().getName()
					+ "- [getGenericDisplayData] - Entry");
			queryResult = genericDisplayMonitorDAO.getGenericDisplayData(baseQuery);
			log.debug(this.getClass().getName()
					+ "- [getGenericDisplayData] - Exit");
			return queryResult;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGenericDisplayData] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getGenericDisplayData", ScenMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Returns the screen details for a defined facility and fill them into a string with format: 
	 * 'program name','height','width'
	 * 
	 * @param facilityId
	 * @return String
	 * @throws SwtException
	 */
	public String getScreenDetails(String facilityId) throws SwtException {
		
		String screenDetails = "";
		ArrayList screenDetailsArray = null;
		
		try {
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Entry");
			
			screenDetailsArray = genericDisplayMonitorDAO.getScreenDetails(facilityId);
			if(screenDetailsArray != null && screenDetailsArray.size()==3) {
				screenDetails = screenDetailsArray.get(0)
						+ SwtConstants.SEPARATOR_SCREEN_DETAILS
						+ screenDetailsArray.get(1)
						+ SwtConstants.SEPARATOR_SCREEN_DETAILS
						+ screenDetailsArray.get(2);
			}
			
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Exit");
			return screenDetails;
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScreenDetails] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScreenDetails", ScenMaintenanceManagerImpl.class);
		}
	}

	/**
	 * Return the facility access of the required facility screen.
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @param currencyCode
	 * @param facilityId
	 * @return String
	 * @throws SwtException 
	 */
	public String getFacilityAccess(String hostId, String roleId,
			String entityId, String currencyCode, String facilityId) throws SwtException {
		
		String access = "";
		
		try {
			log.debug(this.getClass().getName() + "- [getFacilityAccess] - Entry");
			
			access = genericDisplayMonitorDAO.getFacilityAccess(hostId, roleId,
					entityId, currencyCode, facilityId);
			
			log.debug(this.getClass().getName() + "- [getFacilityAccess] - Exit");
			return access;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getFacilityAccess", ScenMaintenanceManagerImpl.class);
		}
	}
}
