/*
 * @(#)InputExceptionsMessagesManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.util.OpTimer;
import org.swallow.work.dao.InputExceptionsMessagesDAO;
import org.swallow.work.model.InputExceptionsDataPageDTO;

public interface InputExceptionsMessagesManager {
	
	/**
	 * Method to set InputExceptionsMessagesDAO
	 * @param inputExceptionsMessagesDAO
	 */
	public void setInputExceptionsMessagesDAO(InputExceptionsMessagesDAO inputExceptionsMessagesDAO);
	
	/**
	 * Method to get exception messages for message status and message type
	 * @param page
	 * @param opTimer
	 * @param dateFormat
	 * @param fromPCM
	 * @param fromDashboard 
	 * @return InputExceptionsDataPageDTO
	 * @throws SwtException
	 */
	public InputExceptionsDataPageDTO getMessages (InputExceptionsDataPageDTO page, OpTimer opTimer, String dateFormat, boolean fromPCM, boolean fromDashboard) throws SwtException;
	
	/**
	 * Method to get message of selected sequence id 
	 * @param seqId
	 * @param opTimer
	 * @param fromPCM
	 * @return String
	 * @throws SwtException
	 */
	public String getMessageBody (String seqId, OpTimer opTimer, boolean fromPCM)throws SwtException;
	
	/**
	 * Method to reprocess exception message for selected sequence id
	 * @param seqId
	 * @param opTimer
	 * @param fromPCM
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean reprocessMessage(String[] seqId, OpTimer opTimer, boolean fromPCM) throws SwtException;
	
	/**
	 * Method to suppress or reject exception message for selected sequence id
	 * @param seqId
	 * @param pProcessOption
	 * @param opTimer
	 * @param fromPCM
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean suppressOrRejectMessage(String[] seqId,String pProcessOption, OpTimer opTimer, boolean fromPCM) throws SwtException;

	public Collection<String> getMessageTypeList(String currencyCode, String status, String date, String dateFormat) throws SwtException;
	
	/**
	 * Return total messages when drilling down from PCM Monitor.
	 * When Value is selected we aren't able to provide exact messages to be shown
	 *  
	 * @param messageStatus
	 * @param fromDate
	 * @param dateFormat
	 * @param currencyCode
	 * @return String
	 * @throws SwtException
	 */
	public String getTotalCountFromDashboard(String messageStatus, String fromDate, String dateFormat, String currencyCode) throws SwtException;

}

