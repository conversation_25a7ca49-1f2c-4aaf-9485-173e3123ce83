/*
 * @(#) PreAdviceInputManager.java  17/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Party;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.Movement;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;


public interface PreAdviceInputManager extends MovementManager {
	/**
	 * This method is used to retieve Role ID for a given User ID
	 * 
	 * @param hostId
	 *            String
	 * @param userId
	 *            String
	 * @return String RoleId corresponding to UserID
	 * @throws SwtException
	 *             User Defined
	 */
	public String getUserDetails(String hostId, String userId)
			throws SwtException;

	/**
	 * This method is basically used to retrieve Authorize flag for a given
	 * RoleID from S_ROLE table.
	 * 
	 * @param roleId
	 *            String
	 * @return Authorizr flag for corresponding roleId from S_ROLE table.
	 * @throws SwtException
	 *             User defined.Throws in case of any exception
	 */
	public String getRoleDetails(String roleId) throws SwtException;

	/**
	 * This method is basically used to retrieve List of Account Ids for a given
	 * inputs.
	 * 
	 * @param hostId
	 * @return List of Account Ids
	 * @throws SwtException
	 */
	public Collection getAccountIdDropDown(String hostId, String entityId,
			String currencyCode, String movType) throws SwtException;

	/**
	 * This method checks whether the combination of given inputs exist in
	 * MatchDriver table or not.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return boolean
	 * @throws SwtException
	 */
	public MatchDriver existMatchDriver(String hostId, String entityId,
			String currencyCode) throws SwtException;

	/**
	 * This method is used to save the Movement object into the database.
	 * 
	 * @param movement
	 * @param matchDriver
	 * @param systemInfo
	 * @param sysforma
	 * @throws SwtException
	 */
	public String saveMovementDetails(Movement movement,
			MatchDriver matchDriver, String matchDriverOperation,
			Collection sessionNotesDetails, SystemInfo systemInfo,
			SystemFormats sysforma) throws SwtException;

	/**
	 * This method is used to collect list of Movements for given input
	 * parameters
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyGroup
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	/*
	 * code modified for the Mantis:772 For the problem of date format, thrown
	 * exception not a valid month,if date format mm/dd/yyyy
	 */
	/* START Code:Modified by Mahesh on 04-Dec-2008 */
	public Collection getInputAuthoriseListToday(String hostId,
			String entityId, String currencyGroup, String dateRange,
			String userId, String searchDate, String dateFormat)
			throws SwtException;

	/* END Code:Modified by Mahesh on 04-Dec-2008 */

	/**
	 * This method is used to retrieve MATCH_STATUS value for movement with
	 * given inputs.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param inputSource
	 * @param matchStatus
	 * @param SystemFormats
	 * @return
	 * @throws SwtException
	 */
	public void getMovementWithMatchStatus(HttpServletRequest request,
			String hostId, String entityId, String currencyCode,
			String inputSource, String matchStatus, SystemFormats sysformat,
			String dateFlag, String userId) throws SwtException;

	/**
	 * This method is used for searching a Movement with given MovementId and
	 * HostId. This method also sets the notes status to the request object.
	 * 
	 * @param hostId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovement(String hostId, Long movementId,
			HttpServletRequest request) throws SwtException;

	/*
	 * START : Code Added for providing service used in defect number 223 on
	 * 30/04/2007
	 */

	/**
	 * This method is used to delete all the movements included into
	 * listMovementId string. It also deletes all the referense data for given
	 * movements.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 *            is basically list of movement_Ids seperated with '|'
	 * @throws SwtException
	 */
	public void deleteMovement(String hostId, String entityId,
			String listMovementId) throws SwtException;

	/*
	 * END : Code Added for providing service used in defect number 223 on
	 * 30/04/2007
	 */

	/**
	 * This method is used for searching a Movement with given MovementId and
	 * HostId.But this method does not set the notes status of the movement.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovement(String hostId, String entityId, Long movementId)
			throws SwtException;

	/**
	 * Method set MATCH_STATUS of selected movements and writes log record in
	 * P_MOVEMENT_LOG and updates Matching Driver table P_MATCH_DRIVER for
	 * selected movements Entity & Currency (as in Movement Manual Input).
	 * 
	 * @param movement
	 * @param sessionNotesDetails
	 * @throws SwtException
	 */
	public void updateMovementDetails(Movement movement,
			Collection sessionNotesDetails) throws SwtException;

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param counterPartyId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getCounterPartyRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException;

	/*
	 * Start :code added by Mahesh on 18-Mar-2010 for Mantis 1034: Getting the
	 * Matching party records from the data base
	 */
	/**
	 * This method used to Matching party records from the data base
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return collection
	 * @throws SwtException
	 */
	public Collection getMatchingPartyRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException;

	/*
	 * End :code added by Mahesh on 18-Mar-2010 for Mantis 1034: Getting the
	 * Matching party records from the data base
	 */

	/**
	 * This method use to handle the MovementAlerts updation and creation based
	 * on the MatchStatus passed to it.
	 * 
	 * @param movement
	 * @param originalStatus
	 * @param newStatus
	 * @throws SwtException
	 */
	public void manageMovementAlert(Movement movement, String originalStatus,
			String newStatus) throws SwtException;

	/**
	 * This method returns the Pre-Advice Position Level of given entity.
	 * 
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getPreadvicePositionLevelOfEntity(String hostId,
			String entityId) throws SwtException;

	/**
	 * This method is used to update the open flag of P_MOVEMENT table
	 */
	public void updateOpenUnopenFlag(String hostId, String entityId,
			Long movementId, String openFlag, String updateUser)
			throws SwtException;
	
	/**
	 * This method is used to get the pre-advice movements that have been input in
	 * the last 24 hours
	 */
	public Collection<Movement> getLast24PreAdviceInput(String hostId, String updateUser, String entityId, String inputBy)
			throws SwtException;

	/**
	 * This method is used to save the pre-advice movements that have been pasted in
	 * import screen bottom grid
	 */
	public void saveAll(List<Movement> listMvt) throws SwtException;
	
	/**
	 * This method is used to get movement type from p_account table
	 */
	public String getMvtFromAccountTab(String accountId) throws SwtException;
	
	/**
	 * This method is used to check if entity id exists in S_ENTITY
	 */
	public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException;
	
	/**
	 * This method is used to get account status from p_account table
	 */
	public String getAccountStatus(String accountId) throws SwtException;
	
	/**
	 * This method is used to get  ALLOW_PRE_ADVICES from p_account table
	 */
	public String checkAccountPreAdviceAccess(String accountId, String entityId) throws SwtException;
	
	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId,
			int pageSize, int currentPage, String selectedsort) throws SwtException;

	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException;
}
