/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service;

import org.swallow.exception.SwtException;

import org.swallow.work.dao.MovementLockDAO;

import java.util.HashMap;


public interface MovementLockManager {
    /**
     * @param movementId
     * @return
     * @throws SwtException
     */
	
	/**Subject		:Mantis Issue 739
	 *Description	:Earlier only movement ID was passed to lock the
	 *				 movement.In that case in the UPDATE_USER  column 
	 *				'SYSTEM' will be stored by default instead of locked User ID.
	 *				To avoid that userId is taken from session and passed
	 *				in the movementLockManager's  lockMovement()	 	
	 * *Code Modified on date	07/10/2008 by <PERSON><PERSON><PERSON>.A*/   
	 public String lockMovement(Long movementId,String user) throws SwtException;

    /**
     * @param movementId
     * @return
     * @throws SwtException
     */
    public boolean unLockMovement(Long movementId) throws SwtException;

    /**
    * @param movementLockDAO
    */
    public void setMovementLockDAO(MovementLockDAO movementLockDAO);

    /**
     * @param matchLongId
     * @param entityId
     * @param userId
     * @return
     */
    public boolean lockMatch(Long matchId, String entityId, String userId)
        throws SwtException;
    
    
    /**
	 * Subject		:	Mantis Issue 739
	 *Description	:	To make a movement unlock If being locked by an user
	 *					when the session gets unbind from the application	
	 *Code Modified on date	05/08/2008 by Selva Kumar.A 
	 * */    
    /** This method is used to delete a movement by passing the user 
     * @param String user ID
     * @return
     * @throws SwtException
     * */   
    public void deleteMovementLock(String user)throws SwtException;


/*Mantis 813 Method Written by Saminathan to check lock exists for a movement*/
    /**    
     * @param movementId     
     * @return String
     */
    
    public String checkLock(Long movementId)
    throws SwtException;
    
    /**
     * 
     * @param hostId
     * @param currentUserId
     * @param movementIds
     * @return
     * @throws SwtException
     */
    public String getMovementsLocks(String hostId, String currentUserId, String movementIds)
    		throws SwtException;
    
    /* START:Code added for Mantis 758 by Kalidass on 12-Dec-2008    */
    
    /** Gets the lock status for adding the movement in MMSD 
     * @param movementId
     * @param user
     * @return String
     */
    public String lockMovementForMMSD(Long movementId,String user) throws SwtException;
    
    /* End:Code added for Mantis 758 by Kalidas on 12-Dec-2008    */
    public HashMap<Long, String> checkMvtsLock(String hostId, String entityId, String currentUserId, String movementIds) throws SwtException ;
}
