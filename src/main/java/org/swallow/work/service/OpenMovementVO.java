/*
 * Created on Jan 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 * Bean to hold Open Movement Values
 */
public class OpenMovementVO {
	/**
	 * 
	 */
	private Collection currencyList;
	private Collection openMovListDetailsToday;
	private Collection openMovListDetailsTomorr;
	private Collection openMovListDetailsDayAfterTomorr;
	private Collection openMovListDetailsAll;

	/*
	 * Start : Mantis Issue 1306 : Code added for Enhancement to Non-Working
	 * Days (Holidays and Weekends) by Arumugam on 08-Mar-2011
	 */
	private String tabFlag = null;

	/**
	 * @return Returns the tabFlag.
	 */
	public String getTabFlag() {
		return tabFlag;
	}

	/**
	 * @param tabFlag
	 *            The tabFlag to set.
	 */
	public void setTabFlag(String tabFlag) {
		this.tabFlag = tabFlag;
	}

	/*
	 * End : Mantis Issue 1306 : Code added for Enhancement to Non-Working Days
	 * (Holidays and Weekends) by Arumugam on 08-Mar-2011
	 */

	// private Collection openMovListDetails;
	/**
	 * @return Returns the currencyList.
	 */
	public Collection getCurrencyList() {
		return currencyList;
	}

	/**
	 * @param currencyList
	 *            The currencyList to set.
	 */
	public void setCurrencyList(Collection currencyList) {
		this.currencyList = currencyList;
	}

	/**
	 * @return Collection
	 */
	public Collection getOpenMovListDetailsAll() {
		return openMovListDetailsAll;
	}

	public void setOpenMovListDetailsAll(Collection openMovListDetailsAll) {
		this.openMovListDetailsAll = openMovListDetailsAll;
	}

	public Collection getOpenMovListDetailsDayAfterTomorr() {
		return openMovListDetailsDayAfterTomorr;
	}

	public void setOpenMovListDetailsDayAfterTomorr(
			Collection openMovListDetailsDayAfterTomorr) {
		this.openMovListDetailsDayAfterTomorr = openMovListDetailsDayAfterTomorr;
	}

	public Collection getOpenMovListDetailsToday() {
		return openMovListDetailsToday;
	}

	public void setOpenMovListDetailsToday(Collection openMovListDetailsToday) {
		this.openMovListDetailsToday = openMovListDetailsToday;
	}

	public Collection getOpenMovListDetailsTomorr() {
		return openMovListDetailsTomorr;
	}

	public void setOpenMovListDetailsTomorr(Collection openMovListDetailsTomorr) {
		this.openMovListDetailsTomorr = openMovListDetailsTomorr;
	}

	/**
	 * @return
	 */
	// public Collection getOpenMovListDetails() {
	// return openMovListDetails;
	// }
	/**
	 * @param openMovListDetails
	 */
	// public void setOpenMovListDetails(Collection openMovListDetails) {
	// this.openMovListDetails = openMovListDetails;
	// }
}
