/*
 * @(#)MatchManager.java  11/01/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service;

import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;

import org.swallow.exception.SwtException;
import org.swallow.work.dao.MatchDAO;


public interface MatchManager {

	/**
	 * @param matchDAO
	 */
	public void setMatchDAO(MatchDAO matchDAO);
	// Start: Code modified by Bala on 19102010 for Mantis 1209
    /**
	 * This method is used to fetch screen data for all the Match Screen's 
	 * @param entityId
	 * @param hostId
	 * @param currencyGrpId
	 * @param status
	 * @param roleId
	 * @param selectedTabIndex
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * @param date
	 * @return
	 * @throws SwtException
	 */
    public MatchDetailVO getMatchScreenData(String entityId,String hostId, String currencyGrpId,
    		String status,String roleId, String selectedTabIndex, String applyCurrencyThreshold,
			String noIncludedMovementMatches, Date date) 
    throws SwtException;
	// End: Code modified by Bala on 19102010 for Mantis 1209
    /**
	 * This method repopulates and updates highest_position level, lowest position level, max value date 
	 * and max amount of movements associated with given match id.
	 * @param hostId
	 * @param entityId 
	 * @param matchIds
	 * @throws SwtException
	 */
	public void updateBrokenMatch(String hostId, String entityId, HashSet brokenMatches)
	throws SwtException;
	/**
	 * This method returns the string array containing INPUT_HST, highestPositionLevel and orgPredictStatusList from
	 * the remaining movements of match.
	 * @param lockedMap
	 * @param movementObjctList
	 * @return String[INPUT_HST,highestPositionLevel,orgPredictStatusList]
	 */
	public HashMap getMatchSRCDetsils(Collection movementObjctList);
	
}