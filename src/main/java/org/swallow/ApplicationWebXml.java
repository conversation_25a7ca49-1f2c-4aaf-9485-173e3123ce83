package org.swallow;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.Banner;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;

/**
 * This is a helper Java class that provides an alternative to creating a {@code web.xml}.
 * This will be invoked only when the application is deployed to a Servlet container like Tomcat, JBoss etc.
 */
public class ApplicationWebXml extends SpringBootServletInitializer {
    private static final Logger log = LoggerFactory.getLogger(ApplicationWebXml.class);

    public ApplicationWebXml() {
        // Disable reference to Tomcat
        System.setProperty(SwtConstants.PROPERTY_ENV_TOMCAT_ENABLED, "false");
    }
    
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        String pcmEnabled = PropertiesFileLoader.getInstance()
                .getPropertiesValue(SwtConstants.PCM_ENABLED);
    	if ("true".equals(pcmEnabled)) {
	    	System.setProperty("action_package_list", "org.swallow.action, org.swallow.web, org.swallow.control.web, org.swallow.work.web,  org.swallow.export.web, org.swallow.maintenance.web, org.swallow.reports.web,"
	    			+ "    org.swallow.pcm.maintenance.web, org.swallow.pcm.report.web,org.swallow.pcm.control.web, org.swallow.pcm.work.web");
		}else {
			System.setProperty("action_package_list", "org.swallow.action, org.swallow.web, org.swallow.control.web, org.swallow.work.web,  org.swallow.export.web, org.swallow.maintenance.web, org.swallow.reports.web");
		}
    	
    	SpringApplicationBuilder appBuilder = application.profiles(addDefaultProfile()).bannerMode(Banner.Mode.CONSOLE).sources(PredictApplication.class);    	
        return appBuilder;
    }
    
    /**
     * Set a default profile if it has not been set.
     * <p/>
     * <p>
     * Please use -Dspring.profiles.active=dev
     * </p>
     */
    private String addDefaultProfile() {
        String profile = System.getProperty("spring.profiles.active");
        if (profile != null) {
            log.info("Running with Spring profile(s) : {}", profile);
            return profile;
        }

        log.warn("No Spring profile configured, running with default configuration");
        return SwtConstants.SPRING_PROFILE_PRODUCTION;
    }
}