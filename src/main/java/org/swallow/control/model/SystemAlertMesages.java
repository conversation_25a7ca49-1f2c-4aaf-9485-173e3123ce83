/*
 * @(#)SystemAlertMesages.java  23/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.util.Date;
import java.util.Hashtable;
import org.swallow.maintenance.model.AcctMaintenance.Id;
import org.swallow.model.BaseObject;


public class SystemAlertMesages extends BaseObject implements org.swallow.model.AuditComponent {
	
	private Id id = new Id();
	private String updateUser;
	private Date updateDate = new Date()  ;
	private String alertmessage;
	private String roleId;
	private String alertStageAsString;
	private String enableflg="A";
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("alertmessage","Alert Message");
		logTable.put("roleId","Role Id");
		logTable.put("enableflg","Enabled Flag");
	}
	
	
	/**
	 * @return Returns the enableflg.
	 */
	public String getEnableflg() {
		return enableflg;
	}
	/**
	 * @param enableflg The enableflg to set.
	 */
	public void setEnableflg(String enableflg) {
		this.enableflg = enableflg;
	}
	/**
	 * @return Returns the roleId.
	 */
	public String getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId The roleId to set.
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	public static class Id extends BaseObject{
		
		private String hostId ;
		 
		private String alertstage;
		public Id() {
		}
		
		public Id(String hostId ,String entityId ,String accountId ,String alertstage) {
			this.hostId = hostId;
			 
			this.alertstage = alertstage;
			 
			
		}
		/**
		 * @return Returns the alertstage.
		 */
		public String getAlertstage() {
			return alertstage;
		}
		/**
		 * @param alertstage The alertstage to set.
		 */
		public void setAlertstage(String alertstage) {
			this.alertstage = alertstage;
		}
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}

	/**
	 * @return Returns the alertmessage.
	 */
	public String getAlertmessage() {
		return alertmessage;
	}
	/**
	 * @param alertmessage The alertmessage to set.
	 */
	public void setAlertmessage(String alertmessage) {
		this.alertmessage = alertmessage;
	}
	/**
	 * @return Returns the alertStageAsString.
	 */
	public String getAlertStageAsString() {
		return alertStageAsString;
	}
	/**
	 * @param alertStageAsString The alertStageAsString to set.
	 */
	public void setAlertStageAsString(String alertStageAsString) {
		this.alertStageAsString = alertStageAsString;
	}
}
