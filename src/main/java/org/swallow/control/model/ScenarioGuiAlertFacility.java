package org.swallow.control.model;

import java.util.Hashtable;
import org.swallow.model.BaseObject;

public class ScenarioGuiAlertFacility extends BaseObject implements org.swallow.model.AuditComponent {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Id id = new Id();
	private String description;
	private String requiresScenarioInstance;
	private String programId;
	private String otherIdType;
	
	private String requiredParameters;
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("id","Scenario Gui Alert");
		logTable.put("description","Description");
	}
	
	
	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getRequiresScenarioInstance() {
		return requiresScenarioInstance;
	}

	public void setRequiresScenarioInstance(String requiresScenarioInstance) {
		this.requiresScenarioInstance = requiresScenarioInstance;
	}

	public String getProgramId() {
		return programId;
	}

	public void setProgramId(String programId) {
		this.programId = programId;
	}

	public String getRequiredParameters() {
		return requiredParameters;
	}

	public void setRequiredParameters(String requiredParameters) {
		this.requiredParameters = requiredParameters;
	}
	
	public String getOtherIdType() {
		return otherIdType;
	}

	public void setOtherIdType(String otherIdType) {
		this.otherIdType = otherIdType;
	}
	
	
	public static class Id extends BaseObject{

		 
		private String guiFacilityId;
		public Id() {
		}
		
		public Id(String guiFacilityId) {
			 
			this.setGuiFacilityId(guiFacilityId);
			 
			
		}

		/**
		 * @return the facilityId
		 */
		public String getGuiFacilityId() {
			return guiFacilityId;
		}

		/**
		 * @param scenarioid the facilityId to set
		 */
		public void setGuiFacilityId(String guiFacilityId) {
			this.guiFacilityId = guiFacilityId;
		}

		

	}



}
