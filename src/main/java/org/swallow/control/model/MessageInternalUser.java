package org.swallow.control.model;



import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MessageInternalUser extends BaseObject {

	private int messageId;
	private String[] roleId;
	private String[] userId;
	private String user;
	private String role;
	private Long seqNo;

	
	
	/**
	 * @return Returns the messageId.
	 */
	public int getMessageId() {
		return messageId;
	}
	/**
	 * @param messageId The messageId to set.
	 */
	public void setMessageId(int messageId) {
		this.messageId = messageId;
	}
	
	/**
	 * @return Returns the roleId.
	 */
	public String[] getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId The roleId to set.
	 */
	public void setRoleId(String[] roleId) {
		this.roleId = roleId;
	}
	/**
	 * @return Returns the userId.
	 */
	public String[] getUserId() {
		return userId;
	}
	/**
	 * @param userId The userId to set.
	 */
	public void setUserId(String[] userId) {
		this.userId = userId;
	}
	/**
	 * @return Returns the seqNo.
	 */
	public Long getSeqNo() {
		return seqNo;
	}
	/**
	 * @param seqNo The seqNo to set.
	 */
	public void setSeqNo(Long seqNo) {
		this.seqNo = seqNo;
	}
	/**
	 * @return Returns the role.
	 */
	public String getRole() {
		return role;
	}
	/**
	 * @param role The role to set.
	 */
	public void setRole(String role) {
		this.role = role;
	}
	/**
	 * @return Returns the user.
	 */
	public String getUser() {
		return user;
	}
	/**
	 * @param user The user to set.
	 */
	public void setUser(String user) {
		this.user = user;
	}
}
