<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
 <class name="org.swallow.control.model.ErrorLog" table="S_ERROR_LOG">
 
	<id name="errSeq" type="long" column="ERROR_SEQ">
		<generator class="sequence">
			<param name="sequence_name">S_ERROR_LOG_SEQUENCE</param>
		 <param name="increment_size">1</param>
		 </generator>
	</id>

  <property name="hostId" column="HOST_ID"/>
  <property name="errorDate" column="ERROR_DATE" />
  <property name="userId" column="USER_ID"/>
  <property name="ipAddress" column="IP_ADDRESS"/>
  <property name="source" column="SOURCE"/>
  <property name="errorId" column="ERROR_ID"/>
   <property name="errorDesc" column="ERROR_DESC"/>
 </class>

</hibernate-mapping>


