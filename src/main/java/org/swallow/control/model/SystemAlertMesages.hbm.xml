<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.SystemAlertMesages" table="P_ALERT">
		<composite-id name="id" class="org.swallow.control.model.SystemAlertMesages$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID"/>
			<key-property name="alertstage" access="field" column="ALERT_STAGE"/>
		</composite-id>
	
			
		<property name="alertmessage" column="ALERT_MESSAGE" not-null="false"/>
		<property name="roleId" column="ROLE_ID" not-null="false"/>
		<property name="enableflg" column="ENABLED_FLAG" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		
					
    </class>
</hibernate-mapping>
