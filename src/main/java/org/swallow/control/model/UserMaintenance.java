/*
 * @(#)UserMaintenance.java  15/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.util.Date;
import java.util.Hashtable;
import org.swallow.model.BaseObject;
import java.text.SimpleDateFormat; 
import java.text.ParseException;


public class UserMaintenance extends BaseObject implements org.swallow.model.AuditComponent{
	
	private Date updateDate ;
	private String updateUser;
	private Id id = new Id();
	private String username;
	private String sectionid;
	private String language;
	private String phonenumber;
	private String emailId;
	private String entityId;
	private String roleId;
	private String password;
	private String currententity;
	private String newPassword;
	//Added for Implement amount and date formats specific to user
	private String amountDelimiter;
	private String dateFormat;
	
	private Integer invPassAttempt;
	/*Code modified as per mail from <PERSON> and forwaded by <PERSON> for Amending the User Profile Changes on 17-APR-2008 -->
	  Reference SRS : Smart-Predict_SRS_USER_PROFILE_0.2.doc by <PERSON> and David 
	  Description : It is required to have a user whose sole purpose to be able to reset user passwords. 
	              This requires a change to the "Change Role" window. */
	//Start  : Code added to add confirm password field for User Maintenance on 17-Apr-2008.
	private String confirmPassword;
	//End    : Code added to add confirm password field for User Maintenance on 17-Apr-2008.
	//Added for Currency Group Id
	private String currentCcyGrpId;
	
	public static String  className = "User";
	
	private String extAuthId; 
	
	private Date lastLoginFailed = null;
	private String lastLoginFailedIp = null;
	private String lastLoginIp = null;
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("username","User Name");
		logTable.put("sectionid","Section Id");
		logTable.put("language","Language");
		logTable.put("phonenumber","Phone Number");
		logTable.put("emailId","Email Address");
		logTable.put("roleId","Role Id");
		logTable.put("currententity","Default Entity");
		logTable.put("status","User Status");
		logTable.put("currentCcyGrpId","Currency Group ID");
			
	}
	
	/**
	 * @return Returns the invPassAttempt.
	 */
	public Integer getInvPassAttempt() {
		return invPassAttempt;
	}
	/**
	 * @param invPassAttempt The invPassAttempt to set.
	 */
	public void setInvPassAttempt(Integer invPassAttempt) {
		this.invPassAttempt = invPassAttempt;
	}
	private Date lastlogin;
	private String lastloginasstring;
	private String lastlogin_date;
	
	/**
	 * @param lastlogin The lastlogin to set.
	 */
	public void setLastlogin(Date lastlogin) {
		this.lastlogin = lastlogin;
	}
	/**
	 * @param lastloginasstring The lastloginasstring to set.
	 */
	public void setLastloginasstring(String lastloginasstring) {
		this.lastloginasstring = lastloginasstring;
	}
	/**
	 * @param lastlogout The lastlogout to set.
	 */
	public void setLastlogout(Date lastlogout) {
		this.lastlogout = lastlogout;
	}
	/**
	 * @param lastlogoutasstring The lastlogoutasstring to set.
	 */
	public void setLastlogoutasstring(String lastlogoutasstring) {
		this.lastlogoutasstring = lastlogoutasstring;
	}
	/**
	 * @param passwordchangedate The passwordchangedate to set.
	 */
	public void setPasswordchangedate(Date passwordchangedate) {
		this.passwordchangedate = passwordchangedate;
	}
	/**
	 * @param passwordchangedateasstring The passwordchangedateasstring to set.
	 */
	public void setPasswordchangedateasstring(String passwordchangedateasstring) {
		this.passwordchangedateasstring = passwordchangedateasstring;
	}
	private Date passwordchangedate;
	private String passwordchangedateasstring;
	private String passwordchangedate_date;
	
	private Date lastlogout;
	private String lastlogoutasstring;
	private String lastlogout_date;
	
	private String status="1";
	
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	public static class Id extends BaseObject{
		private String hostId;
		private String userId;
		

		public Id() {}

		public Id(String hostId, String userId) {
			this.hostId = hostId;
			this.userId = userId;
			
		}
		
		
			
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId){
			this.hostId = hostId;
		}
	}

	/**
	 * @return Returns the currententity.
	 */
	public String getCurrententity() {
		return currententity;
	}
	/**
	 * @param currententity The currententity to set.
	 */
	public void setCurrententity(String currententity) {
		this.currententity = currententity;
	}
	/**
	 * @return Returns the emailId.
	 */
	public String getEmailId() {
		return emailId;
	}
	/**
	 * @param emailId The emailId to set.
	 */
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}
	/**
	 * @return Returns the language.
	 */
	public String getLanguage() {
		return language;
	}
	/**
	 * @param language The language to set.
	 */
	public void setLanguage(String language) {
		this.language = language;
	}
	/**
	 * @return Returns the lastlogin.
	 */
	public Date getLastlogin() {
		return lastlogin;
	}
//	/**
//	 * @param lastlogin The lastlogin to set.
//	 */
//	public void setLastlogin(Date lastlogin) {
//		this.lastlogin = lastlogin;
//		if(lastlogin != null)
//			setLastloginasstring(lastlogin.toString());
//	}
	/**
	 * @return Returns the lastlogout.
	 */
	public Date getLastlogout() {
		return lastlogout;
	}
//	/**
//	 * @param lastlogout The lastlogout to set.
//	 */
//	public void setLastlogout(Date lastlogout) {
//		this.lastlogout = lastlogout;
//		if(lastlogout != null)
//			setLastlogoutasstring(lastlogout.toString());
//
//	}
	/**
	 * @return Returns the password.
	 */
	public String getPassword() {
		return password;
	}
	/**
	 * @param password The password to set.
	 */
	public void setPassword(String password) {
		this.password = password;
	}
	/**
	 * @return Returns the passwordchangedate.
	 */
	public Date getPasswordchangedate() {
		return passwordchangedate;
	}
//	/**
//	 * @param passwordchangedate The passwordchangedate to set.
//	 */
//	public void setPasswordchangedate(Date passwordchangedate) {
//		this.passwordchangedate = passwordchangedate;
//		if(passwordchangedate != null)
//			setPasswordchangedateasstring(passwordchangedate.toString());
//
//	}
	/**
	 * @return Returns the phonenumber.
	 */
	public String getPhonenumber() {
		return phonenumber;
	}
	/**
	 * @param phonenumber The phonenumber to set.
	 */
	public void setPhonenumber(String phonenumber) {
		this.phonenumber = phonenumber;
	}
	/**
	 * @return Returns the roleId.
	 */
	public String getRoleId() {
		return roleId;
	}
	/**
	 * @param roleId The roleId to set.
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	/**
	 * @return Returns the sectionid.
	 */
	public String getSectionid() {
		return sectionid;
	}
	/**
	 * @param sectionid The sectionid to set.
	 */
	public void setSectionid(String sectionid) {
		this.sectionid = sectionid;
	}
	/**
	 * @return Returns the status.
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * @param status The status to set.
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * @return Returns the username.
	 */
	public String getUsername() {
		return username;
	}
	/**
	 * @param username The username to set.
	 */
	public void setUsername(String username) {
		this.username = username;
	}
	/**
	 * @return Returns the lastloginasstring.
	 */
	public String getLastloginasstring() {
		return lastloginasstring;
	}
//	/**
//	 * @param lastloginasstring The lastloginasstring to set.
//	 */
//	public void setLastloginasstring(String lastloginasstring) {
//		this.lastloginasstring = lastloginasstring;
//		SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
//		try
//		{
//			Date date=sdf.parse(lastloginasstring);
//			setLastlogin(date);
//		}catch(ParseException parExp){
//			
//		}
//		
//		
//		
//	}
	/**
	 * @return Returns the lastlogoutasstring.
	 */
	public String getLastlogoutasstring() {
		return lastlogoutasstring;
	}
//	/**
//	 * @param lastlogoutasstring The lastlogoutasstring to set.
//	 */
//	public void setLastlogoutasstring(String lastlogoutasstring) {
//		this.lastlogoutasstring = lastlogoutasstring;
//				  
//		 SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
//		try
//		{
//			Date date=sdf.parse(lastlogoutasstring);
//			setLastlogout(date);
//		}catch(ParseException parExp){
//			
//		}
//		
//	}
	/**
	 * @return Returns the passwordchangedateasstring.
	 */
	public String getPasswordchangedateasstring() {
		return passwordchangedateasstring;
	}
//	/**
//	 * @param passwordchangedateasstring The passwordchangedateasstring to set.
//	 */
//	public void setPasswordchangedateasstring(String passwordchangedateasstring) {
//		this.passwordchangedateasstring = passwordchangedateasstring;
//		SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
//		try
//		{
//			Date date=sdf.parse(passwordchangedateasstring);
//			setPasswordchangedate(date);
//		}catch(ParseException parExp){
//			
//		}
//	}
	/**
	 * @return Returns the lastlogin_date.
	 */
	public String getLastlogin_date() {
		SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
		return sdf.format(getLastlogin());
		
	}
	/**
	 * @param lastlogin_date The lastlogin_date to set.
	 */
	public void setLastlogin_date(String lastlogin_date) {
		this.lastlogin_date = lastlogin_date;
	}
	/**
	 * @return Returns the lastlogout_date.
	 */
	public String getLastlogout_date() {
		SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
		return sdf.format(getLastlogout());
		
	}
	/**
	 * @param lastlogout_date The lastlogout_date to set.
	 */
	public void setLastlogout_date(String lastlogout_date) {
		this.lastlogout_date = lastlogout_date;
	}
	/**
	 * @return Returns the passwordchangedate_date.
	 */
	public String getPasswordchangedate_date() {
		SimpleDateFormat sdf=new SimpleDateFormat("dd/MM/yyyy");
		return sdf.format(getPasswordchangedate());
		
	}
	/**
	 * @param passwordchangedate_date The passwordchangedate_date to set.
	 */
	public void setPasswordchangedate_date(String passwordchangedate_date) {
		this.passwordchangedate_date = passwordchangedate_date;
	}

	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the newPassword.
	 */
	public String getNewPassword() {
		return newPassword;
	}
	/**
	 * @param newPassword The newPassword to set.
	 */
	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}
	/**
	 * @return Returns the currentCcyGrpId.
	 */
	public String getCurrentCcyGrpId() {
		return currentCcyGrpId;
	}
	/**
	 * @param currentCcyGrpId The currentCcyGrpId to set.
	 */
	public void setCurrentCcyGrpId(String currentCcyGrpId) {
		this.currentCcyGrpId = currentCcyGrpId;
	}
	/*Code modified as per mail from Steve and forwaded by JP for Amending the User Profile Changes on 17-APR-2008 -->
	  Reference SRS : Smart-Predict_SRS_USER_PROFILE_0.2.doc by James Cook and David 
	  Description : It is required to have a user whose sole purpose to be able to reset user passwords. 
	              This requires a change to the "Change Role" window. */
	//Start  : Code added to set and get the confirm password value for User Maintenance on 17-Apr-2008.
	/**
	 * This method returns the confirm password value entered by the user.
	 * @param 
	 * @return confirmPassword
	 */
	public String getConfirmPassword() {
		return confirmPassword;
	}
	/**
	 * This method puts the confirm password value entered by the user in the form to the variable.
	 * @param String confirmPassword
	 * @return
	 */
	public void setConfirmPassword(String confirmPassword) {
		this.confirmPassword = confirmPassword;
	}
	/*Code modified as per mail from Steve and forwaded by JP for Amending the User Profile Changes on 17-APR-2008 -->
	  Reference SRS : Smart-Predict_SRS_USER_PROFILE_0.2.doc by James Cook and David 
	  Description : It is required to have a user whose sole purpose to be able to reset user passwords. 
	              This requires a change to the "Change Role" window. */
	//End    : Code added to set and get the confirm password value for User Maintenance on 17-Apr-2008.
	public String getAmountDelimiter() {
		return amountDelimiter;
	}
	public void setAmountDelimiter(String amountDelimiter) {
		this.amountDelimiter = amountDelimiter;
	}
	public String getDateFormat() {
		return dateFormat;
	}
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
	
	public String getExtAuthId() {
		return extAuthId;
	}
	public void setExtAuthId(String extAuthId) {
		this.extAuthId = extAuthId;
	}
	public Date getLastLoginFailed() {
		return lastLoginFailed;
	}
	public void setLastLoginFailed(Date lastLoginFailed) {
		this.lastLoginFailed = lastLoginFailed;
	}
	public String getLastLoginFailedIp() {
		return lastLoginFailedIp;
	}
	public void setLastLoginFailedIp(String lastLoginFailedIp) {
		this.lastLoginFailedIp = lastLoginFailedIp;
	}
	public String getLastLoginIp() {
		return lastLoginIp;
	}
	public void setLastLoginIp(String lastLoginIp) {
		this.lastLoginIp = lastLoginIp;
	}

	
	
}
