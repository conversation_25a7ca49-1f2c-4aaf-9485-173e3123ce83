<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.control.model.Shortcut" table="S_SHORTCUT">
  <composite-id class="org.swallow.control.model.Shortcut$Id" name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="userId" access="field" column="USER_ID"/>
   <key-property name="shortcutId" access="field" column="SHORTCUT_ID"/>
  </composite-id>
  <property name="shortcutName" column="SHORTCUT_NAME"/>
  <property name="menuItemId" column="MENU_ITEM_ID"/>
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
  <many-to-one name="menuItem" class="org.swallow.model.MenuItem" lazy="false"
   column="MENU_ITEM_ID" not-null="true" outer-join="true"
   update="false" insert="false" foreign-key="FK_S_SHORTCUT_P_MENU_ITEM"/>
 </class>
</hibernate-mapping>
