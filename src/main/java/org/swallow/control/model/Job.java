/*
 * Created on Jan 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * Job.java
 * 
 * This java bean has getters and setters for Job details
 * 
 * <AUTHOR>
 * 
 * 
 */
public class Job extends BaseObject {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	// JOB_DESCRIPTION
	private String jobDescription;
	// system job
	private String systemJob;
	// Id
	private Id id = new Id();
	// update user
	private String updateUser = null;
	// update date
	private Date updateDate = null;
	// program name
	private String programName = null;
	// job type
	private String jobType = null;

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		// HOST_ID
		private String hostId;
		// JOB_ID
		private String jobId;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * constructor
		 */
		public Id(String hostId, String jobId) {
			this.hostId = hostId;
			this.jobId = jobId;
		}

		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * @param hostId
		 *            The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * @return Returns the jobId.
		 */
		public String getJobId() {
			return jobId;
		}

		/**
		 * @param jobId
		 *            The jobId to set.
		 */
		public void setJobId(String jobId) {
			this.jobId = jobId;
		}
	}

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return Returns the jobDescription.
	 */
	public String getJobDescription() {
		return jobDescription;
	}

	/**
	 * @param jobDescription
	 *            The jobDescription to set.
	 */
	public void setJobDescription(String jobDescription) {
		this.jobDescription = jobDescription;
	}

	/**
	 * @return the systemJob
	 */
	public String getSystemJob() {
		return systemJob;
	}

	/**
	 * @param systemJob
	 *            the systemJob to set
	 */
	public void setSystemJob(String systemJob) {
		this.systemJob = systemJob;
	}

	/**
	 * @return the updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            the updateUser to set
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return the programName
	 */
	public String getProgramName() {
		return programName;
	}

	/**
	 * @param programName
	 *            the programName to set
	 */
	public void setProgramName(String programName) {
		this.programName = programName;
	}

	public String getJobType() {
		return jobType;
	}

	public void setJobType(String jobType) {
		this.jobType = jobType;
	}

}
