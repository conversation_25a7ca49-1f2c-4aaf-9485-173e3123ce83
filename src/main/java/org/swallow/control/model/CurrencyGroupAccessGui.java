/*
 * Created on Aug 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class CurrencyGroupAccessGui extends BaseObject {
	
	/**
	 * @return Returns the ccyGrpAccessHTML1.
	 */
	public String getCcyGrpAccessHTML1() {
		return ccyGrpAccessHTML1;
	}
	/**
	 * @param ccyGrpAccessHTML1 The ccyGrpAccessHTML1 to set.
	 */
	public void setCcyGrpAccessHTML1(String ccyGrpAccessHTML1) {
		this.ccyGrpAccessHTML1 = ccyGrpAccessHTML1;
	}
	String hostId;
	String entityId;
	String currencyGroupId;
	String currencyGroupName;
	String ccyGrpAccessHTML1;
	String ccyGrpAccessHTML2;
	String ccyGrpAccessHTML3;
	
	

	/**
	 * @return Returns the ccyGrpAccessHTML2.
	 */
	public String getCcyGrpAccessHTML2() {
		return ccyGrpAccessHTML2;
	}
	/**
	 * @param ccyGrpAccessHTML2 The ccyGrpAccessHTML2 to set.
	 */
	public void setCcyGrpAccessHTML2(String ccyGrpAccessHTML2) {
		this.ccyGrpAccessHTML2 = ccyGrpAccessHTML2;
	}
	/**
	 * @return Returns the ccyGrpAccessHTML3.
	 */
	public String getCcyGrpAccessHTML3() {
		return ccyGrpAccessHTML3;
	}
	/**
	 * @param ccyGrpAccessHTML3 The ccyGrpAccessHTML3 to set.
	 */
	public void setCcyGrpAccessHTML3(String ccyGrpAccessHTML3) {
		this.ccyGrpAccessHTML3 = ccyGrpAccessHTML3;
	}
	/**
	 * @return Returns the currencyGroupId.
	 */
	public String getCurrencyGroupId() {
		return currencyGroupId;
	}
	/**
	 * @param currencyGroupId The currencyGroupId to set.
	 */
	public void setCurrencyGroupId(String currencyGroupId) {
		this.currencyGroupId = currencyGroupId;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	/**
	 * @return Returns the currencyGroupName.
	 */
	public String getCurrencyGroupName() {
		return currencyGroupName;
	}
	/**
	 * @param currencyGroupName The currencyGroupName to set.
	 */
	public void setCurrencyGroupName(String currencyGroupName) {
		this.currencyGroupName = currencyGroupName;
	}
}
