/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao;
import org.swallow.control.model.*;

import java.util.Collection;
import org.swallow.dao.*;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface PasswordDAO extends DAO{
	
	
	/*
	 * Created on Nov 4, 2005
	 *
	 * TODO To change the template for this generated file go to
	 * Window - Preferences - Java - Code Style - Code Templates
	 */
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getPasswordRules(String hostId)  throws SwtException;	
	/**
	 * @param pwdRules
	 * @throws SwtException
	 */
	public void  updatePasswordRules(Password  pwdRules)throws SwtException;	


}
