/*
 * @(#)ErrorLogDAOHibernate.java 1.0 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.dao.ErrorLogDAO;
import org.swallow.control.model.ErrorLog;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import jakarta.persistence.TypedQuery;
/**
 * <AUTHOR>
 * 
 * This class is dao layer of the Error log screen it use dto get the Error log
 * details of the application
 * 
 */
@Repository ("errorLogDAO")
@Transactional
public class ErrorLogDAOHibernate extends CustomHibernateDaoSupport implements
		ErrorLogDAO {
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(ErrorLogDAOHibernate.class);

	public ErrorLogDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	/**
	 * @param fromDate
	 * @param toDate
	 * @return Collection
	 */
	public Collection getErrorLogList(String hostId, Date fromDate, Date toDate)
			throws SwtException {
		log.debug("Entring getErrorLogList Method");
		List<ErrorLog> records;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<ErrorLog> query = session.createQuery(
					"from ErrorLog e where e.hostId = :hostId and trunc(e.errorDate) between :fromDate and :toDate order by e.errorDate desc",
					ErrorLog.class);
			query.setParameter("hostId", hostId);
			query.setParameter("fromDate", fromDate);
			query.setParameter("toDate", toDate);
			records = query.getResultList();
		}
		log.debug("records - " + records);
		log.debug("Exiting getErrorLogList Method");
		return records;
	}

	/**
	 * This method is used to get the Error log details from the database for
	 * exporting data based on selected userId and dates
	 * 
	 * @param hostId
	 * @param userId
	 * @param fromDate
	 * @param toDate
	 * @throws SwtException
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection<ErrorLog> getErrorLogList(String hostId, String userId,
			Date fromDate, Date toDate) throws SwtException {
		// List declared for assigning error log details

		try {
			log.debug(this.getClass().getName()
					+ " - [ getErrorLogList ] - Entry ");
			// assigning checking wehether the userId is 'ALL' or else
			userId = userId.equals("ALL") ? "%" : userId;

			// Code Modified For Mantis 1566 by Chinniah on 28-Dec-2011:Log
			// Display Screens: Sort by sequence number instead of date time
			// getting error log details from the database

			Collection<ErrorLog> results;
			try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<ErrorLog> query = session.createQuery(
						"from ErrorLog e where e.hostId = :hostId and e.userId like :userId and e.errorDate between :fromDate and :toDate order by e.errSeq desc",
						ErrorLog.class);
				query.setParameter("hostId", hostId);
				query.setParameter("userId", userId);
				query.setParameter("fromDate", fromDate);
				query.setParameter("toDate", toDate);
				results = query.getResultList();
			}
			return results;

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getErrorLogList] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getErrorLogList", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " - [ getErrorLogList ] - Exit ");

		}

	}

	/**
	 * @param errorLog
	 * @return
	 */
	public void logError(ErrorLog errorLog) {
		if(!SwtUtil.isEmptyOrNull(errorLog.getSource()) && errorLog.getSource().length()>1000) {
			errorLog.setSource(errorLog.getSource().substring(0, 999));
		}
		getHibernateTemplate().save(errorLog);
	}

	public int getErrorLogListUsingStoredProc(Date fromDate, Date toDate,
			int currentPage, int maxPage, List errorLogList,
			String filterSortStatus, SystemFormats formats) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [ getErrorLogListUsingStoredProc ] - Entry ");
		Session session = null;
		Connection conn = null;
		CallableStatement cstmt = null;
		int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);		
		String showdata = "Y";
		int recordcount = 0;
		ResultSet rs = null;
		ResultSet rsTotal = null;

		String[] filterSortArr = filterSortStatus.split("<split>");

		String filterCriteria = filterSortArr[0].replaceAll("&gt;", ">")
				.replaceAll("<quot;>", "\"");

		String sortCriteria = filterSortArr[1];
		sortCriteria = sortCriteria + "|";

		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);

			log.debug(" $$$$$$$$$  PK_USER_LOG.SP_ERROR_LOG Called at "
					+ SwtUtil.getTimeWithMilliseconds());
			cstmt = conn
					.prepareCall("{call PK_USER_LOG.SP_ERROR_LOG(?,?,?,?,?,?,?,?,?)}");
			cstmt.setDate(1, SwtUtil.truncateDateTime(fromDate));
			cstmt.setDate(2, SwtUtil.truncateDateTime(toDate));
			cstmt.setInt(3, pageSize);
			cstmt.setInt(4, currentPage);
			cstmt.setString(5, filterCriteria);

			cstmt.setString(6, sortCriteria);
			cstmt.setString(7, showdata);
			cstmt.registerOutParameter(8, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.registerOutParameter(9, oracle.jdbc.OracleTypes.CURSOR);
			cstmt.execute();

			log.debug(" $$$$$$$$$  PK_USER_LOG.SP_ERROR_LOG Ended at "
					+ SwtUtil.getTimeWithMilliseconds());
			rs = (ResultSet) cstmt.getObject(8);
			rsTotal = (ResultSet) cstmt.getObject(9);
			if (rs != null) {
				while (rs.next()) {
					ErrorLog errorLog = new ErrorLog();
					Date date = (Date) rs.getObject(1);
					String user = rs.getString(2);
					String sourceFileName = rs.getString(3);
					String errorDesc = rs.getString(4);
					errorLog.setErrorDate(date);
					errorLog.setErrorDate_Date(SwtUtil.formatDate(errorLog
							.getErrorDate(), formats.getDateFormatValue()));
					errorLog.setUserId(user);

					if (sourceFileName != null && sourceFileName.length() > 0)
						errorLog.setSource(sourceFileName.replaceAll(">",
								"&gt;").replaceAll("\"", "<quot;>"));
					else
						errorLog.setSource("");
					if (errorDesc != null && errorDesc.length() > 0)
						errorLog.setErrorDesc(errorDesc.replaceAll(">", "&gt;")
								.replaceAll("\"", "<quot;>"));
					else
						errorLog.setErrorDesc("");

					errorLogList.add(errorLog);
				}

			}

			if (rsTotal != null) {
				while (rsTotal.next()) {
					recordcount = rsTotal.getInt(1);
				}

			}
			log.debug(this.getClass().getName()
					+ " - [ getErrorLogListUsingStoredProc ] - Exit ");

		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getErrorLogListUsingStoredProc] method : - "
							+ e.getMessage());
		} finally {
				
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			SQLException sqlException = null;
			
			sqlException = JDBCCloser.close(rs, rsTotal);
			if (sqlException!=null)
				thrownException = SwtErrorHandler.getInstance().handleException(sqlException, "getErrorLogListUsingStoredProc",ErrorLogDAOHibernate.class);
			
			Object[] exceptions = JDBCCloser.close(null, cstmt, conn, session);
			
			if (thrownException == null && exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getErrorLogListUsingStoredProc", ErrorLogDAOHibernate.class);
			
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getErrorLogListUsingStoredProc", ErrorLogDAOHibernate.class);
			
			if (thrownException!=null)
				throw thrownException;
			
		}

		return recordcount;
	}

}
