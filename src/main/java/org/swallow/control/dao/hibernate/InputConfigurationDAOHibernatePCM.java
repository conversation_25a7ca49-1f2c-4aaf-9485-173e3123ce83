/*
 * @(#)InputConfigurationDAOHibernate.java 1.0 11/06/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.dao.InputConfigurationDAO;
import org.swallow.control.model.InputInterface;
import org.swallow.control.model.InputInterfaceProperty;
import org.swallow.control.model.InputInterfaceSBeanProperty;
import org.swallow.control.model.InterfaceInterruption;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.control.model.PCInterfaceInterruption;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * 
 * This class is used to get the input configuration details.
 */

@Repository("inputConfigurationDAOPCM")
@Transactional
public class InputConfigurationDAOHibernatePCM extends CustomHibernateDaoSupport implements InputConfigurationDAO {
    private final Log log = LogFactory.getLog(InputConfigurationDAOHibernatePCM.class);

    public InputConfigurationDAOHibernatePCM(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManagerPC") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    public ArrayList<InputInterface> fetchData(OpTimer opTimer, boolean fromPCM, String filter, String sort) throws SwtException {
        log.debug(this.getClass().getName() + " - [fetchData] - Entering");
        ArrayList<InputInterface> rtn = new ArrayList<>();
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(fromPCM ? 
                 "{call pkg_pc_interface_monitor.i_settings_header(?,?,?)}" :
                 "{call pkg_interface_monitor.i_settings_header(?,?,?)}")) {
            
            opTimer.start("fetch-data");
            cstmt.setString(1, filter);
            cstmt.setString(2, sort);
            cstmt.registerOutParameter(3, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();
            
            try (ResultSet rs = (ResultSet) cstmt.getObject(3)) {
                while (rs != null && rs.next()) {
                    InputInterface input = new InputInterface();
                    input.setExpand(rs.getString(1));
                    input.setInterfaceId(rs.getString(2));
                    input.setMessageType(rs.getString(3));
                    input.setEmailLogs(rs.getString(4));
                    input.setEmailLogsTo(rs.getString(5));
                    input.setEngineActive(rs.getString(6));
                    input.setStartAlertTime(rs.getString(7));
                    input.setEndAlertTime(rs.getString(8));
                    input.setThreshold(rs.getString(9));
                    rtn.add(input);
                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [fetchData] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "fetchData", InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("fetch-data");
        }
        return rtn;
    }

    public ArrayList<InputInterface> fetchBottomGridData(OpTimer opTimer, String interfaceId, boolean fromPCM) 
            throws SwtException {
        log.debug(this.getClass().getName() + " - [fetchbottomgridData] - Entering");
        ArrayList<InputInterface> rtn = new ArrayList<>();
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(fromPCM ? 
                 "{call pkg_pc_interface_monitor.i_settings_detail(?,?)}" :
                 "{call pkg_interface_monitor.i_settings_detail(?,?)}")) {
            
            opTimer.start("fetch-data");
            cstmt.setString(1, interfaceId);
            cstmt.registerOutParameter(2, oracle.jdbc.OracleTypes.CURSOR);
            cstmt.execute();
            
            try (ResultSet rs = (ResultSet) cstmt.getObject(2)) {
                while (rs != null && rs.next()) {
                    InputInterface input = new InputInterface();
                    input.setClasss(rs.getString(1));
                    input.setProperty(rs.getString(2));
                    input.setValue(rs.getString(3));
                    input.setBeanId(rs.getString(4));
                    input.setName(rs.getString(5));
                    input.setMandatory(rs.getString(6));
                    input.setPropertyType(rs.getString(7));
                    input.setIsPassword((input.getProperty() != null) && 
                        input.getProperty().toLowerCase().contains("password"));
                    rtn.add(input);
                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [fetchbottomgridData] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "fetchbottomgridData", 
                InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("fetch-data");
        }
        return rtn;
    }

    public InputInterface fetchRecord(OpTimer opTimer, String interfaceId) {
        log.debug(this.getClass().getName() + " - [fetchRecord] - Entering");
        opTimer.start("fetch-record");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<InputInterface> query = session.createQuery(
                "from InputInterface i where i.interfaceId = :interfaceId order by i.label",
                InputInterface.class);
            query.setParameter("interfaceId", interfaceId);
            List<InputInterface> result = query.getResultList();
            return result.isEmpty() ? null : result.get(0);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [fetchRecord] - Exception: " + e.getMessage());
        } finally {
            opTimer.stop("fetch-record");
        }
        return null;
    }

    public void saveRecord(OpTimer opTimer, InputInterface inputInterface) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveRecord] - Entering");
        opTimer.start("save-record");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.update(inputInterface);
                
                TypedQuery<PCInterfaceInterruption> query = session.createQuery(
                    "from PCInterfaceInterruption i where i.id.interfaceId = :interfaceId",
                    PCInterfaceInterruption.class);
                query.setParameter("interfaceId", inputInterface.getInterfaceId());
                List<PCInterfaceInterruption> data = query.getResultList();
                
                PCInterfaceInterruption pcInterfaceInterruption = PCInterfaceInterruption
                    .convertToPCInterfaceInterruption(inputInterface.getInterfaceInterruption(
                        inputInterface.getInterfaceId(), inputInterface.getExtensions()));
                
                if (!data.isEmpty()) {
                    session.update(pcInterfaceInterruption);
                } else {
                    session.save(pcInterfaceInterruption);
                }
                
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [saveRecord] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "saveRecord", InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("save-record");
        }
    }

    public void updateRecord(OpTimer opTimer, InputInterfaceSBeanProperty dto, boolean fromPCM) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateRecord] - Entering");
        opTimer.start("save-record");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession();
             Connection conn = SwtUtil.connection(session);
             CallableStatement cstmt = conn.prepareCall(fromPCM ?
                 "{call pkg_pc_interface_monitor.UpdatePropertyValues(?,?,?,?)}" :
                 "{call pkg_interface_monitor.UpdatePropertyValues(?,?,?,?)}")) {
            
            cstmt.setString(1, dto.getId().getInterfaceId());
            cstmt.setString(2, dto.getId().getName());
            cstmt.setString(3, dto.getId().getBeanId());
            cstmt.setString(4, dto.getValue());
            cstmt.execute();
            
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [updateRecord] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "updateRecord", InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("update-record");
        }
    }

    public void updateImage(OpTimer opTimer, InputInterface dto) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateImage] - Entering");
        opTimer.start("update-image");
        
        try (Session session = getHibernateTemplate().getSessionFactory()
                .withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {
            
            TypedQuery<InputInterface> query = session.createQuery(
                "from InputInterface i where i.interfaceId = :id",
                InputInterface.class);
            query.setParameter("id", dto.getInterfaceId());
            List<InputInterface> data = query.getResultList();
            
            if (!data.isEmpty()) {
                Transaction tx = session.beginTransaction();
                try {
                    InputInterface inputInterface = data.get(0);
                    inputInterface.setEngineActive(dto.getEngineActive());
                    session.update(inputInterface);
                    tx.commit();
                } catch (Exception e) {
                    tx.rollback();
                    throw e;
                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [updateImage] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "updateImage", InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("update-image");
        }
    }

    public InputInterfaceProperty getPropertyForUpdate(OpTimer opTimer, String interfaceId, 
            String logicalClass, String name) throws SwtException {
        log.debug(this.getClass().getName() + " - [getPropertyForUpdate] - Entering");
        opTimer.start("fetch-record");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<InputInterfaceProperty> query = session.createQuery(
                "from InputInterfaceProperty i where i.id.interfaceId = :interfaceId " +
                "and i.id.logicalClass = :logicalClass and i.id.name = :name",
                InputInterfaceProperty.class);
            query.setParameter("interfaceId", interfaceId);
            query.setParameter("logicalClass", logicalClass);
            query.setParameter("name", name);
            
            List<InputInterfaceProperty> result = query.getResultList();
            return result.isEmpty() ? null : result.get(0);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [getPropertyForUpdate] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "InputInterfaceProperty", 
                InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("fetch-record");
        }
    }

    public void saveProperty(OpTimer opTimer, InputInterfaceProperty dto) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveProperty] - Entering");
        opTimer.start("save-property");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.update(dto);
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (HibernateException e) {
            log.error(this.getClass().getName() + " - [saveProperty] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "saveProperty", 
                InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("save-property");
        }
    }

    public InterfaceInterruption getMsgTypeExtension(OpTimer opTimer, String interfaceId, String messageType) {
        log.debug(this.getClass().getName() + " - [getMsgTypeExtension] - Entering");
        opTimer.start("get-Msg-Type-Exceptions");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<InterfaceInterruption> query = session.createQuery(
                "from InterfaceInterruption i where i.id.interfaceId = :interfaceId " +
                "and i.id.messageType = :messageType",
                InterfaceInterruption.class);
            query.setParameter("interfaceId", interfaceId);
            query.setParameter("messageType", messageType);
            
            List<InterfaceInterruption> result = query.getResultList();
            return result.isEmpty() ? null : result.get(0);
        } finally {
            opTimer.stop("fetch-record");
        }
    }

    public void saveMessageTypeParams(OpTimer opTimer,
            InterfaceInterruption interfaceInterrupt) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveMessageTypeParams] - Entering");
        opTimer.start("save-messageType-params");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                TypedQuery<InterfaceInterruption> query = session.createQuery(
                    "from InterfaceInterruption i where i.id.interfaceId = :interfaceId " +
                    "and i.id.messageType = :messageType",
                    InterfaceInterruption.class);
                query.setParameter("interfaceId", interfaceInterrupt.getId().getInterfaceId());
                query.setParameter("messageType", interfaceInterrupt.getId().getMessageType());
                List<InterfaceInterruption> data = query.getResultList();
                
                if (!data.isEmpty()) {
                    session.update(interfaceInterrupt);
                } else {
                    session.save(interfaceInterrupt);
                }
                tx.commit();
            } catch (Exception e) {
                tx.rollback();
                throw e;
            }
        } catch (HibernateException e) {
            log.error(this.getClass().getName() + " - [saveMessageTypeParams] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "saveMessageTypeParams", 
                InputConfigurationDAOHibernatePCM.class);
        } finally {
            opTimer.stop("save-messageType-params");
        }
    }

    public InputInterfaceSBeanProperty fetchInterfaceSBeanProperty(String interfaceId, 
            String BeanId, String propertyName) throws SwtException {
        log.debug(this.getClass().getName() + " - [fetchInterfaceSBeanProperty] - Entering");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<InputInterfaceSBeanProperty> query = session.createQuery(
                "from InputInterfaceSBeanProperty i where i.id.interfaceId = :interfaceId " +
                "and i.id.beanId = :beanId and i.id.name = :name",
                InputInterfaceSBeanProperty.class);
            query.setParameter("interfaceId", interfaceId);
            query.setParameter("beanId", BeanId);
            query.setParameter("name", propertyName);
            
            List<InputInterfaceSBeanProperty> result = query.getResultList();
            return result.isEmpty() ? null : result.get(0);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [fetchInterfaceSBeanProperty] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "fetchInterfaceSBeanProperty", 
                InputConfigurationDAOHibernatePCM.class);
        }
    }
}