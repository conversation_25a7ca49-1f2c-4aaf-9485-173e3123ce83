/*
 * Created on Dec 26, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.List;

import jakarta.transaction.Transactional;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.swallow.control.dao.ChangePasswordDAO;
import org.swallow.control.model.Password;
import org.swallow.control.model.PasswordHistory;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository ("changePasswordDAO")
@Transactional
public class ChangePasswordDAOHibernate extends CustomHibernateDaoSupport implements ChangePasswordDAO {
    private final Log log = LogFactory.getLog(ChangePasswordDAOHibernate.class);
    private final static String STRHQLQuery = "SELECT MAX(p.id.seqNo) FROM PasswordHistory p where p.id.hostId=:hostId and p.id.userId=:userId"; 
    private final static String STRHQLQuery1 = "SELECT p FROM Password p where p.id.hostId=:hostId";
    
    // Constructor remains unchanged
    public ChangePasswordDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
        super(sessionfactory, entityManager);
    }
    
    public Collection getPasswordRules(User user) throws SwtException {
        log.debug("entering 'getPasswordRules' method");
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Password> query = session.createQuery(STRHQLQuery1, Password.class);
            query.setParameter("hostId", user.getId().getHostId());
            return query.getResultList();
        }
    }
    
    public void setNewPassword(User user) throws SwtException {
        log.debug("entering 'setNewPassword' method");
        Session session = null;
        Transaction tx = null;
        SwtInterceptor interceptor = null;
        
        try {
            LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
            logonDAO.saveChangePassword(user);
            
            // Existing user object copying logic remains unchanged
            User userObj = new User();
            userObj.setCurrentCcyGrpId(user.getCurrentCcyGrpId());
            userObj.setCurrentEntity(user.getCurrentEntity());		
            userObj.setEmailId(user.getEmailId());
            userObj.setId(user.getId());
            userObj.setInvPassAttempt(user.getInvPassAttempt());
            userObj.setLanguage(user.getLanguage());
            userObj.setLastLogin(user.getLastLogin());
            userObj.setLastLogout(user.getLastLogout());
            userObj.setPassword(user.getPassword());
            /* Start : Modified by Vivekanandan A on 30-12-2010 for Mantis 1329 */
            userObj.setPasswordChangeDate(SwtUtil.getSysParamDate());
            /* End : Modified by Vivekanandan A on 30-12-2010 for Mantis 1329 */
            userObj.setPhoneNumber(user.getPhoneNumber());		
            userObj.setRoleId(user.getRoleId());
            userObj.setSectionId(user.getSectionId());
            userObj.setStatus(user.getStatus());
            userObj.setUserName(user.getUserName());
            userObj.setAmountDelimiter(user.getAmountDelimiter());
            userObj.setDateFormat(user.getDateFormat());
            userObj.setExtAuthId(user.getExtAuthId());
            
            interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            session.update(userObj);
            tx.commit();
        } catch (Exception exp) {
            if (tx != null) {
                tx.rollback();
            }
            log.error(this.getClass().getName() + "- [setNewPassword] - Exception " + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp, "setNewPassword", ChangePasswordDAOHibernate.class);
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
        log.debug("exiting 'setNewPassword' method");
    }
    
    public void updatePasswordHistory(PasswordHistory pwdhis) throws SwtException {
        log.debug("entering 'updatePasswordHistory' method");
        String hostId = pwdhis.getId().getHostId();
        String userId = pwdhis.getId().getUserId();
        
        List<Integer> seqNo = getLastSeqNo(hostId, userId);
        int No = seqNo.get(0) != null ? Integer.parseInt(seqNo.get(0).toString()) + 1 : 1;
        pwdhis.getId().setSeqNo(Integer.toString(No));
        
        Session session = null;
        Transaction tx = null;
        SwtInterceptor interceptor = null;
        
        try {
            interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            session.save(pwdhis);
            tx.commit();
        } catch (HibernateException e) {
            if (tx != null) {
                tx.rollback();
            }
            log.error(this.getClass().getName() + "- [updatePasswordHistory] - Exception " + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
        log.debug("exiting 'updatePasswordHistory' method");
    }
    
    public List getLastSeqNo(String hostId, String userId) throws SwtException {
        log.debug("entering 'getLastSeqNo' method");
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Integer> query = session.createQuery(STRHQLQuery, Integer.class);
            query.setParameter("hostId", hostId);
            query.setParameter("userId", userId);
            return query.getResultList();
        }
    }
    
    public List getPasswordList(String hostId, String userId, int num) throws SwtException {
        log.debug("entering 'getPasswordList' method");
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<String> query = session.createQuery(
                "SELECT p.password FROM PasswordHistory p where p.id.hostId=:hostId and p.id.userId=:userId and p.id.seqNo>=:num",
                String.class);
            query.setParameter("hostId", hostId);
            query.setParameter("userId", userId);
            query.setParameter("num", num);
            return query.getResultList();
        }
    }
    
    public void savePasswordHistory(PasswordHistory pwdhis) throws DataAccessException {
        log.debug("entering 'savePasswordHistory' method");
        Session session = null;
        Transaction tx = null;
        SwtInterceptor interceptor = null;
        
        try (Session querySession = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<PasswordHistory> query = querySession.createQuery(
                "FROM PasswordHistory p where p.id.hostId=:hostId and p.id.userId=:userId and p.id.seqNo=0",
                PasswordHistory.class);
            query.setParameter("hostId", pwdhis.getId().getHostId());
            query.setParameter("userId", pwdhis.getId().getUserId());
            List<PasswordHistory> pwdList = query.getResultList();
            
            interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            if (pwdList != null && !pwdList.isEmpty()) {
                session.delete(pwdList.get(0));
                tx.commit();
                tx = session.beginTransaction();
            }
            
            session.save(pwdhis);
            tx.commit();
        } catch (HibernateException e) {
            if (tx != null) {
                tx.rollback();
            }
            log.error(this.getClass().getName() + "- [savePasswordHistory] - Exception " + e.getMessage());
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
        log.debug("Exiting 'savePasswordHistory' method");
    }
    
    public void deletePasswordHistryObject(PasswordHistory pwdhis) throws SwtException {
        log.debug("entering 'deletePasswordHistryObject' method");
        Session session = null;
        Transaction tx = null;
        SwtInterceptor interceptor = null;
        
        try {
            interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            session.delete(pwdhis);
            tx.commit();
        } catch (HibernateException e) {
            if (tx != null) {
                tx.rollback();
            }
            log.error(this.getClass().getName() + "- [deletePasswordHistryObject] - Exception " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "deletePasswordHistryObject", ChangePasswordDAOHibernate.class);
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
        log.debug("Exiting deletePasswordHistryObject");
    }
}