/*
 * @(#)ScenMainteanceDAOHibernate.java 1.0 12/11/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.sql.*;
import java.util.*;

import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.ScenMaintenanceDAO;
import org.swallow.control.model.*;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.model.Currency;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.persistence.EntityManager;


@Repository ("scenMaintenanceDAO")
@Transactional
public class ScenMaintenanceDAOHibernate extends CustomHibernateDaoSupport
		implements ScenMaintenanceDAO {
	public ScenMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}


	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory
			.getLog(ScenMaintenanceDAOHibernate.class);

	/**
	 * Collects the Scenario  detail list from Database table P_SENARIO 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getScenariosDetailList(String category) throws SwtException {
		List<Scenario> list;
		Session session = null;
		Interceptor interceptor= null;
		try {
			log.debug(this.getClass().getName() + " - [getScenariosDetailList] - Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from Scenario sce where (:category is null or sce.categoryId = :category)";

			// Create a TypedQuery
			TypedQuery<Scenario> query = session.createQuery(hql, Scenario.class);
			query.setParameter("category", category);

			// Execute the query and return results
			list = query.getResultList();
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getScenariosDetailList] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getScenariosDetailList", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		log.debug(this.getClass().getName() + " - [getScenariosDetailList] - Exit");
		return list;
	}

	/**
	 * Collects the Scenario  detail list from Database table P_NOTIFY_SENARIO 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getScenariosNotificationDetailList() throws SwtException {
		List<ScenarioNotify> list = null;
		Session session = null;
		Interceptor interceptor= null;
		try {
			log.debug(this.getClass().getName() + " - [getScenariosNotificationDetailList] - Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from ScenarioNotify";

			// Create a TypedQuery
			TypedQuery<ScenarioNotify> query = session.createQuery(hql, ScenarioNotify.class);

			// Execute the query and return results
			list = query.getResultList();
			log.debug(this.getClass().getName() + " - [getScenariosNotificationDetailList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getScenariosNotificationDetailList] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getScenariosNotificationDetailList", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		return list;
	}

	/**
	 * Collects the Role list from database table S_Role 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getRoleList(String hostId) throws SwtException {
		List<Role> noofRecords = null;
		Session session = null;
		Interceptor interceptor= null;
		try {
			log.debug(this.getClass().getName() + " - [getRoleList] - Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from Role role where role.hostId = :hostId order by role.roleId";

			// Create a TypedQuery
			TypedQuery<Role> query = session.createQuery(hql, Role.class);
			query.setParameter("hostId", hostId);

			// Execute the query and return results
			noofRecords = query.getResultList();
			log.debug(this.getClass().getName() + " - [getRoleList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getRoleList] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getRoleList", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		return noofRecords;
	}

	/**
	 * Collects the Role list from database table S_Role 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCategoryList() throws SwtException {
		List noofRecords= null;
		Interceptor interceptor= null;
		Session session = null;
		try{
			log.debug(this.getClass().getName() + " - [getCategoryList] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from ScenarioCategory sc order by sc.id.categoryid";

			// Create a TypedQuery
			TypedQuery<ScenarioCategory> query = session.createQuery(hql, ScenarioCategory.class);

			// Execute the query and return results
			noofRecords = query.getResultList();
			log.debug(this.getClass().getName() + " - [getCategoryList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getCategoryList] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getCategoryList", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		return noofRecords;
	}

	/**
	 * Collects the Role list from database table S_Role
	 *
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getFacilityList() throws SwtException {
		List noofRecords = null;
		Interceptor interceptor= null;
		Session session = null;
		try {
			log.debug(this.getClass().getName() + " - [getFacilityList] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from Facility m order by m.facilityid asc";

			// Create a TypedQuery
			TypedQuery<Facility> query = session.createQuery(hql, Facility.class);

			// Execute the query and return results
			noofRecords = query.getResultList();
			log.debug(this.getClass().getName() + " - [getFacilityList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getFacilityList] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getFacilityList", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		return noofRecords;
	}
	/**
	 * Return the facility list to be chosen by user in the advanced details screen.
	 * It depends on genericDisplaySelected value:
	 * - If genericDisplaySelected=true, return the list of facilities with USER_SELECTABLE!='G'
	 * - If genericDisplaySelected=false, return the list of facilities with USER_SELECTABLE='G'
	 *
	 * @param genericDisplaySelected
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getFacilityListForEdit(boolean genericDisplaySelected)
			throws SwtException {
		List noofRecords = null;
		Interceptor interceptor= null;
		Session session = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getFacilityListForEdit] - " + "Entry");

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			// Define the HQL query
			String hql = "from Facility f where (:genericDisplaySelected = true and f.userselectable IN ('Y', 'G')) " +
					"or (:genericDisplaySelected = false and f.userselectable = 'Y') " +
					"order by f.facilityid asc";

			// Create a TypedQuery
			TypedQuery<Facility> query = session.createQuery(hql, Facility.class);
			query.setParameter("genericDisplaySelected", genericDisplaySelected);

			// Execute the query and return results
			noofRecords = query.getResultList();
			log.debug(this.getClass().getName() + " - [getFacilityListForEdit] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getFacilityListForEdit] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getFacilityListForEdit", ScenMaintenanceDAOHibernate.class);
		} finally {
			session.close(); // Ensure the session is closed to prevent resource leaks
		}

		return noofRecords;
	}

	/**
	 * Update the values in ScenarioMaintenance bean in the database table
	 * P_SCENARIO
	 *
	 * @param scenario
	 * @throws SwtException
	 */
	public void updateScenarioDetail(Scenario  scenario)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [updateScenarioDetail] - "
				+ "Entry");
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try{
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(scenario);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName() + " - [updateScenarioDetail] - "
					+ "Exit");
		}catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateScenarioDetail] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateScenarioDetail", ScenMaintenanceDAOHibernate.class);
		}

	}

	/**
	 * Save the scenario details from P_SCENARIO table
	 *
	 * @param scenario
	 * @throws SwtException
	 */
	public void saveScenarioDetails(Scenario scenario) throws SwtException {

		// Holds the hibernate Transaction instance
		ScenarioSystem scenarioSystem = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveScenarioDetails] - Entry");

			// Get the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate().getSessionFactory()
					.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Use a TypedQuery to check if the scenario already exists
			TypedQuery<Scenario> query = session.createQuery(
					"from Scenario s where s.id.scenarioId = :scenarioId", Scenario.class);
			query.setParameter("scenarioId", scenario.getId().getScenarioId());
			List<Scenario> records = query.getResultList();

			// Condition to check list size
			if (records.isEmpty()) {
				// Create a Scenario System from Scenario details
				scenarioSystem = new ScenarioSystem();
				scenarioSystem.setScenarioId(scenario.getId().getScenarioId());

				// Save the scenario
				session.save(scenario);
				session.flush(); // Ensure the scenario is saved before checking

				// Check if the scenario was saved successfully
				records = query.getResultList(); // Re-fetch the records
				if (!records.isEmpty()) {
					session.save(scenarioSystem);
				}
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}
		} catch (Exception e) {
			if (tx != null) {
				tx.rollback(); // Rollback the transaction in case of an error
			}
			log.error(this.getClass().getName() + " - Exception Catched in [saveScenarioDetails] method : - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "saveScenarioDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
			log.debug(this.getClass().getName() + " - [saveScenarioDetails] - Exit");
		}
	}
	/**
	 * Delete the scenario details from P_SCENARIO table
	 *
	 * @param scenario
	 * @return
	 * @exception SwtException
	 */
	public void deleteScenarioDetail(Scenario scenario) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			// HQL queries for deletion
			String deleteScenarioHQL = "DELETE FROM Scenario s WHERE s.id.scenarioId = :scenarioId";
			String deleteScenarioSystemHQL = "DELETE FROM ScenarioSystem sy WHERE sy.scenarioId = :scenarioId";
			String deleteScenarioNotifyHQL = "DELETE FROM ScenarioNotify sn WHERE sn.id.scenarioId = :scenarioId";
			String deleteScenarioGuiAlertMappingHQL = "DELETE FROM ScenarioGuiAlertMapping sg WHERE sg.id.scenarioId = :scenarioId";
			String deleteScenarioEventMappingHQL = "DELETE FROM ScenarioEventMapping se WHERE se.scenarioId = :scenarioId";
			String deleteScenarioScheduleHQL = "DELETE FROM ScenarioSchedule sched WHERE sched.scenarioId = :scenarioId";

			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Execute deletion queries
			int deletedCountGuiAlertMapping = session.createQuery(deleteScenarioGuiAlertMappingHQL)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.executeUpdate();

			int deletedCountEventMapping = session.createQuery(deleteScenarioEventMappingHQL)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.executeUpdate();

			int deletedCountSchedule = session.createQuery(deleteScenarioScheduleHQL)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.executeUpdate();

			int deletedCounterScenario = session.createQuery(deleteScenarioHQL)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.executeUpdate();

			int deletedCounterSystem = session.createQuery(deleteScenarioSystemHQL)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.executeUpdate();

			// Check if any scenario or system was deleted
			if (deletedCounterScenario == 0 && deletedCounterSystem == 0) {
				log.debug(this.getClass().getName() + " - [deleteScenarioDetail] - Throw SwtRecordNotExist");
				throw new SwtRecordNotExist();
			}

			// Check for ScenarioNotify and delete if exists
			List<ScenarioNotify> scenCounterNotify = session.createQuery("FROM ScenarioNotify sn WHERE sn.id.scenarioId = :scenarioId", ScenarioNotify.class)
					.setParameter("scenarioId", scenario.getId().getScenarioId())
					.getResultList();

			if (!scenCounterNotify.isEmpty()) {
				int deletedCounterNotify = session.createQuery(deleteScenarioNotifyHQL)
						.setParameter("scenarioId", scenario.getId().getScenarioId())
						.executeUpdate();
				if (deletedCounterNotify == 0) {
					log.debug(this.getClass().getName() + " - [deleteScenarioDetail] - Throw SwtRecordNotExist");
					throw new SwtRecordNotExist();
				}
			}

			// Commit the transaction
			tx.commit();
			log.debug(this.getClass().getName() + " - [deleteScenarioDetail] - Exit");
		} catch (Exception exp) {
			if (tx != null) {
				try {
					tx.rollback(); // Rollback the transaction in case of an error
				} catch (HibernateException rollbackException) {
					log.error("HibernateException occurred during rollback in [deleteScenarioDetail]: " + rollbackException.getMessage());
				}
			}
			log.debug(this.getClass().getName() + " - Exception caught in [deleteScenarioDetail] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [deleteScenarioDetail] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteScenarioDetail", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * Get the Editable fields of a scenarioID from the Database table P_SCENARIO
	 *
	 * @param scenarioID
	 * @return Scenario
	 * @throws SwtException
	 */
	public Scenario getEditableData(String scenarioID) throws SwtException {
		Scenario scenario = null;

		try (Session session = getHibernateTemplate()
				.getSessionFactory()
				.withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
				.openSession()) {

			log.debug(this.getClass().getName() + " - [getEditableData] - Entry");

			String hql = "FROM Scenario scenario WHERE scenario.id.scenarioId = :scenarioId";
			TypedQuery<Scenario> query = session.createQuery(hql, Scenario.class);
			query.setParameter("scenarioId", scenarioID);

			// Get the single result or return null if no result is found
			scenario = query.getResultList().stream().findFirst().orElse(null);

			log.debug(this.getClass().getName() + " - [getEditableData] - Exit");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in [getEditableData]: " + exp.getMessage(), exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getEditableData", ScenMaintenanceDAOHibernate.class);
		}

		return scenario;
	}


	/**
	 * Get the details of the categoryID from the Database table P_SCENARIO_CATEGORY
	 * @param categoryID
	 * @return ScenarioCategory
	 * @throws SwtException
	 */
	public ScenarioCategory getcategoryDetails(String categoryID)  throws SwtException{

		ScenarioCategory category = null;
		List categoryIdDetails;
		Iterator itr;
		Interceptor interceptor= null;
		Session session = null;
		try{
			log.debug(this.getClass().getName() + " - [getcategoryDetails] - "
					+ "Entry");
			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query
			String hql = "FROM ScenarioCategory cat WHERE cat.id.categoryid = :categoryId";
			TypedQuery<ScenarioCategory> query = session.createQuery(hql, ScenarioCategory.class);
			query.setParameter("categoryId", categoryID);

			// Get the single result or null if not found
			category = query.getResultStream().findFirst().orElse(null); // This will return null if no result is found

			log.debug(this.getClass().getName() + " - [getCategoryDetails] - Exit");
			return category;

		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getCategoryDetails] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getCategoryDetails] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getCategoryDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * Get the details of the facilityID from the Database table P_FACILITY
	 * @param facilityID
	 * @return Facility
	 * @throws SwtException
	 *
	 */
	public Facility getFacilityDetails(String facilityID) throws SwtException {

		Facility facility = null;
		List facilityIdDetails;
		Iterator itr;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getFacilityDetails] - Entry");

			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query
			String hql = "FROM Facility f WHERE f.facilityid = :facilityId";
			TypedQuery<Facility> query = session.createQuery(hql, Facility.class);
			query.setParameter("facilityId", facilityID);

			// Get the single result or null if not found
			facility = query.getResultStream().findFirst().orElse(null); // This will return null if no result is found

			log.debug(this.getClass().getName() + " - [getFacilityDetails] - Exit");
			return facility;

		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getFacilityDetails] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getFacilityDetails] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getFacilityDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * Get the list of the scenarios from the Database table P_SCENARIO
	 * @return Facility
	 * @throws SwtException
	 *
	 */
	public Collection getScenarioList() throws SwtException {

		List scenarioList;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getScenarioList] - Entry");

			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query
			String hql = "FROM Scenario s ORDER BY s.id.scenarioId";
			TypedQuery<Scenario> query = session.createQuery(hql, Scenario.class);

			// Execute the query and get the result list
			scenarioList = query.getResultList();

			log.debug(this.getClass().getName() + " - [getScenarioList] - Exit");
			return scenarioList;

		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getScenarioList] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getScenarioList] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getScenarioList", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * Update the scenarioNot in the database table P_NOTIFY_SCENARIO
	 * @throws SwtException
	 *
	 */
	public void updateScenarioNotificationDetail(ScenarioNotify scenarioNot)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateScenarioNotificationDetail] - " + "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(scenarioNot);
			tx.commit();
			getHibernateTemplate().clear();
			session.close();
			log.debug(this.getClass().getName()
					+ " - [updateScenarioNotificationDetail] - Exit");
		}catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateScenarioNotificationDetail] method : - "
							+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateScenarioNotificationDetail", ScenMaintenanceDAOHibernate.class);
		}
	}

	/**
	 *
	 * Get the role assignment for a scenarioID in the database table P_NOTIFY_SCENARIO
	 * @param scenarioID
	 * @return ScenarioNotify
	 * @throws SwtException
	 *
	 */
	public ScenarioNotify getRoleAssignmentEditableData(String hostId,String scenarioID,String entityId,String roleId)
			throws SwtException {

		ScenarioNotify scenario = null;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getRoleAssignmentEditableData] - Entry");

			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query
			String hql = "FROM ScenarioNotify s WHERE s.id.hostId = :hostId AND s.id.scenarioId = :scenarioId AND s.id.entityId = :entityId AND s.id.roleId = :roleId";
			TypedQuery<ScenarioNotify> query = session.createQuery(hql, ScenarioNotify.class);
			query.setParameter("hostId", hostId);
			query.setParameter("scenarioId", scenarioID);
			query.setParameter("entityId", entityId);
			query.setParameter("roleId", roleId);

			// Get the single result or null if not found
			scenario = query.getResultStream().findFirst().orElse(null); // This will return null if no result is found

			log.debug(this.getClass().getName() + " - [getRoleAssignmentEditableData] - Exit");
			return scenario;

		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getRoleAssignmentEditableData] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getRoleAssignmentEditableData] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getRoleAssignmentEditableData", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * Save the scenarioNot in the database table P_NOTIFY_SCENARIO
	 * @throws SwtException
	 *
	 */
	public void saveScenarioNotificationDetails(ScenarioNotify scenarioNot)
			throws SwtException {
		ScenarioNotify existingScenario = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveScenarioNotificationDetails] - Entry");

			// Open a new session with the interceptor
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();
			tx = session.beginTransaction();

			// Define the HQL query to check for existing records
			String hql = "FROM ScenarioNotify s WHERE s.id.scenarioId = :scenarioId AND s.id.hostId = :hostId AND s.id.roleId = :roleId AND s.id.entityId = :entityId";
			TypedQuery<ScenarioNotify> query = session.createQuery(hql, ScenarioNotify.class);
			query.setParameter("scenarioId", scenarioNot.getId().getScenarioId());
			query.setParameter("hostId", scenarioNot.getId().getHostId());
			query.setParameter("roleId", scenarioNot.getId().getRoleId());
			query.setParameter("entityId", scenarioNot.getId().getEntityId());

			// Get the single result or null if not found
			existingScenario = query.getResultStream().findFirst().orElse(null);

			// Condition to check if the record already exists
			if (existingScenario == null) {
				// Save the new scenario notification
				session.save(scenarioNot);
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			log.debug(this.getClass().getName() + " - [saveScenarioNotificationDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [saveScenarioNotificationDetails] method: ", e);
			if (tx != null) {
				tx.rollback(); // Rollback the transaction in case of an error
			}
			throw SwtErrorHandler.getInstance().handleException(e, "saveScenarioNotificationDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}
	/**
	 * Save the scenarioNot in the database table P_NOTIFY_SCENARIO
	 * @throws SwtException
	 *
	 */
	public void saveCategoryDetails(ScenarioCategory scenarioCat)
			throws SwtException {
		ScenarioCategory existingCategory = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveCategoryDetails] - Entry");

			// Open a new session with the interceptor
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();
			tx = session.beginTransaction();

			// Define the HQL query to check for existing records
			String hql = "FROM ScenarioCategory c WHERE c.id.categoryid = :categoryId";
			TypedQuery<ScenarioCategory> query = session.createQuery(hql, ScenarioCategory.class);
			query.setParameter("categoryId", scenarioCat.getId().getCategoryid());

			// Get the single result or null if not found
			existingCategory = query.getResultStream().findFirst().orElse(null);

			// Condition to check if the record already exists
			if (existingCategory == null) {
				// Save the new scenario category
				session.save(scenarioCat);
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			log.debug(this.getClass().getName() + " - [saveCategoryDetails] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [saveCategoryDetails] method: ", e);
			if (tx != null) {
				tx.rollback(); // Rollback the transaction in case of an error
			}
			throw SwtErrorHandler.getInstance().handleException(e, "saveCategoryDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}


	/**
	 * Delete the scenarioNot from the database table P_NOTIFY_SCENARIO
	 * @throws SwtException
	 *
	 */
	public void deleteScenarioNotificationDetail(ScenarioNotify scenarioNot)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteScenarioNotificationDetail] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(scenarioNot);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName()
					+ " - [deleteScenarioNotificationDetail] - Exit");

		}catch (Exception e){
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteScenarioNotificationDetail] method : - "
							+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteScenarioNotificationDetail", ScenMaintenanceDAOHibernate.class);
		}
	}


	/**
	 * Collects the Category  detail list from Database table P_CATEGORY 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getScenariosCategoryDetailList() throws SwtException {
		List list = null;
		Session session = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [getScenariosCategoryDetailList] - Entry");

			// Open a new session with the interceptor
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query to retrieve all ScenarioCategory records
			String hql = "FROM ScenarioCategory";
			TypedQuery<ScenarioCategory> query = session.createQuery(hql, ScenarioCategory.class);

			// Execute the query and get the result list
			list = query.getResultList();

			log.debug(this.getClass().getName() + " - [getScenariosCategoryDetailList] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getScenariosCategoryDetailList] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getScenariosCategoryDetailList", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
		return list;
	}


	/**
	 *
	 * Get the category for a scenarioCatId in the database table P_CATEGORY
	 * @return ScenarioNotify
	 * @throws SwtException
	 *
	 */
	public ScenarioCategory getCategoryEditableData(String scenarioCatId) throws SwtException {
		ScenarioCategory category = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getCategoryEditableData] - Entry");

			// Open a Hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query
			String hql = "from ScenarioCategory cat where cat.id.categoryid = :categoryid";

			// Create a TypedQuery
			TypedQuery<ScenarioCategory> query = session.createQuery(hql, ScenarioCategory.class);
			query.setParameter("categoryid", scenarioCatId);

			// Get the result
			List<ScenarioCategory> scenarioList = query.getResultList();

			// Extract the single result if available
			if (!scenarioList.isEmpty()) {
				category = scenarioList.get(0);
			}

			log.debug(this.getClass().getName() + " - [getCategoryEditableData] - Exit");

			return category;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception caught in [getCategoryEditableData] method: " + exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception caught in [getCategoryEditableData] method: " + exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp, "getCategoryEditableData", ScenMaintenanceDAOHibernate.class);

		} finally {
			// Ensure the session is closed
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null) {
				throw new SwtException("Error closing session: " + hThrownException.getMessage(), hThrownException);
			}
		}
	}
	/**
	 * Delete the category from the database table P_CATEGORY
	 * @throws SwtException
	 *
	 */
	public void deleteScenarioCategoryDetails(ScenarioCategory scenarioCat)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteScenarioCategoryDetails] - Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(scenarioCat);
			tx.commit();
			session.close();
			log.debug(this.getClass().getName()
					+ " - [deleteScenarioCategoryDetails] - Exit");

		}catch (Exception e){
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteScenarioCategoryDetails] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteScenarioCategoryDetails", ScenMaintenanceDAOHibernate.class);
		}

	}
	/**
	 * Update the Scenario Category in the database table P_CATEGORY
	 */
	public void updateScenarioCategoryDetails(ScenarioCategory scenarioCat)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateScenarioCategoryDetails] - " + "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(scenarioCat);
			tx.commit();
			getHibernateTemplate().clear();
			session.close();
			log.debug(this.getClass().getName()
					+ " - [updateScenarioCategoryDetails] - Exit");
		}catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateScenarioCategoryDetails] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateScenarioCategoryDetails", ScenMaintenanceDAOHibernate.class);
		}

	}
	/**
	 * Get if a category has related scenario associated
	 */
	public boolean categoryHasChildren(String categoryId) throws SwtException {
		Session session = null;
		boolean hasChildren = false;

		try {
			log.debug(this.getClass().getName() + " - [categoryHasChildren] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to check for children
			String hql = "FROM Scenario sce WHERE sce.categoryId = :categoryId";
			TypedQuery<Scenario> query = session.createQuery(hql, Scenario.class);
			query.setParameter("categoryId", categoryId);

			// Execute the query and check if any results exist
			hasChildren = !query.getResultList().isEmpty();

			log.debug(this.getClass().getName() + " - [categoryHasChildren] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [categoryHasChildren] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "categoryHasChildren", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return hasChildren;
	}

	/**
	 * Get list of user by role
	 * @param hostId
	 * @param entityId
	 * @param roleId
	 * @param scenarioID
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getDistListByRole(String hostId, String entityId, String roleId, String scenarioID) throws SwtException {
		List listDistribution = null;

		try{
			log.debug(this.getClass().getName() + " - [getDistListByRole] - " + "Entry");

			String distributionList = SwtUtil.getNamedQuery("distribution_list");
			listDistribution = SwtUtil.executeSelectQuery(distributionList, hostId, entityId, roleId, scenarioID);

			log.debug(this.getClass().getName() + " - [getDistListByRole] - " + "Exit");
		}catch(Exception e){
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getDistListByRole] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getDistListByRole", ScenMaintenanceDAOHibernate.class);
		}
		return listDistribution;
	}

	/**
	 * Collects the Gui Facility list from database table P_SCENARIO_EVENT_FACILITY 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getGuiFacilityList() throws SwtException {
		List noofRecords= null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getGuiFacilityList] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve all ScenarioGuiAlertFacility records
			String hql = "FROM ScenarioGuiAlertFacility scGui";
			TypedQuery<ScenarioGuiAlertFacility> query = session.createQuery(hql, ScenarioGuiAlertFacility.class);

			// Execute the query and get the result list
			noofRecords = query.getResultList();

			log.debug(this.getClass().getName() + " - [getGuiFacilityList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getGuiFacilityList] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getGuiFacilityList", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
		return noofRecords;
	}

	/**
	 * Get the details of the gui facility from the Database table P_SCENARIO_GUI_ALERT_FACILITY
	 * @return ScenarioCategory
	 * @throws SwtException
	 */
	public ScenarioGuiAlertFacility getGuiFacilityDetails(String guiFacilityId)  throws SwtException{

		ScenarioGuiAlertFacility guiAlertFacility = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getGuiFacilityDetails] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the ScenarioGuiAlertFacility by guiFacilityId
			String hql = "FROM ScenarioGuiAlertFacility sc WHERE sc.id.guiFacilityId = :guiFacilityId";
			TypedQuery<ScenarioGuiAlertFacility> query = session.createQuery(hql, ScenarioGuiAlertFacility.class);
			query.setParameter("guiFacilityId", guiFacilityId);

			// Get the single result or null if not found
			guiAlertFacility = query.getResultStream().findFirst().orElse(null);

			log.debug(this.getClass().getName() + " - [getGuiFacilityDetails] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getGuiFacilityDetails] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getGuiFacilityDetails] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getGuiFacilityDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return guiAlertFacility;
	}

	/**
	 * Collects the Event Facility list from database table P_SCENARIO_EVENT_FACILITY 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getEventFacilityList() throws SwtException {
		List noofRecords= null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getEventFacilityList] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve all ScenarioEventFacility records
			String hql = "FROM ScenarioEventFacility scEvent ORDER BY scEvent.id.eventFacilityId ASC";
			TypedQuery<ScenarioEventFacility> query = session.createQuery(hql, ScenarioEventFacility.class);

			// Execute the query and get the result list
			noofRecords = query.getResultList();

			log.debug(this.getClass().getName() + " - [getEventFacilityList] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getEventFacilityList] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getEventFacilityList", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return noofRecords;
	}

	/**
	 * Get the details of the gui facility from the Database table P_SCENARIO_EVENT_FACILITY
	 * @param eventFacilityID
	 * @return ScenarioEventFacility
	 * @throws SwtException
	 */
	public ScenarioEventFacility getEventFacilityDetails(String eventFacilityID)  throws SwtException{

		ScenarioEventFacility eventAlertFacility = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getEventFacilityDetails] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the ScenarioEventFacility by eventFacilityID
			String hql = "FROM ScenarioEventFacility sc WHERE sc.id.eventFacilityId = :eventFacilityId";
			TypedQuery<ScenarioEventFacility> query = session.createQuery(hql, ScenarioEventFacility.class);
			query.setParameter("eventFacilityId", eventFacilityID);

			// Get the single result or null if not found
			eventAlertFacility = query.getResultStream().findFirst().orElse(null);

			log.debug(this.getClass().getName() + " - [getEventFacilityDetails] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getEventFacilityDetails] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getEventFacilityDetails] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getEventFacilityDetails", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return eventAlertFacility;
	}
	/**
	 * crud operations inP_SCENARIO_GUI_ALERT_MAPPING
	 * @param listGuiAdd, listGuiUpdate, listGuiDelete
	 * @throws SwtException
	 */
	public void crudGuiHighlight(List<ScenarioGuiAlertMapping> listGuiAdd,
								 List<ScenarioGuiAlertMapping> listGuiUpdate,
								 List<ScenarioGuiAlertMapping> listGuiDelete) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [crudGuiHighlight] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Handle additions
			if (listGuiAdd != null && !listGuiAdd.isEmpty()) {
				for (ScenarioGuiAlertMapping guiAdd : listGuiAdd) {
					TypedQuery<ScenarioGuiAlertMapping> query = session.createQuery(
							"FROM ScenarioGuiAlertMapping scMap WHERE scMap.id.scenarioId = :scenarioId AND scMap.id.guiFacilityId = :guiFacilityId",
							ScenarioGuiAlertMapping.class);
					query.setParameter("scenarioId", guiAdd.getId().getScenarioId());
					query.setParameter("guiFacilityId", guiAdd.getId().getGuiFacilityId());

					List<ScenarioGuiAlertMapping> records = query.getResultList();

					if (!records.isEmpty()) {
						session.update(guiAdd);
					} else {
						session.save(guiAdd);
					}
				}
			}

			// Handle updates
			if (listGuiUpdate != null && !listGuiUpdate.isEmpty()) {
				for (ScenarioGuiAlertMapping guiUpdate : listGuiUpdate) {
					TypedQuery<ScenarioGuiAlertMapping> query = session.createQuery(
							"FROM ScenarioGuiAlertMapping scMap WHERE scMap.id.scenarioId = :scenarioId AND scMap.id.guiFacilityId = :guiFacilityId",
							ScenarioGuiAlertMapping.class);
					query.setParameter("scenarioId", guiUpdate.getId().getScenarioId());
					query.setParameter("guiFacilityId", guiUpdate.getId().getGuiFacilityId());

					List<ScenarioGuiAlertMapping> records = query.getResultList();

					if (!records.isEmpty()) {
						session.update(guiUpdate);
					}
				}
			}

			// Handle deletions
			if (listGuiDelete != null && !listGuiDelete.isEmpty()) {
				for (ScenarioGuiAlertMapping guiDelete : listGuiDelete) {
					session.delete(guiDelete);
				}
			}

			tx.commit();
			log.debug(this.getClass().getName() + " - [crudGuiHighlight] - Exit");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (HibernateException exception) {
					log.error("HibernateException occurred in [crudGuiHighlight] when rolling back transaction. Cause: " + exception.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [crudGuiHighlight] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "crudGuiHighlight", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}

	/**
	 * crud operations in P_SCENARIO_EVENT_MAPPING
	 * @param listEventAdd, listEventUpdate, listEventDelete
	 * @throws SwtException
	 */
	public void crudEventMapping(List<ScenarioEventMapping> listEventAdd,
								 List<ScenarioEventMapping> listEventUpdate,
								 List<ScenarioEventMapping> listEventDelete) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [crudEventMapping] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Handle additions
			if (listEventAdd != null && !listEventAdd.isEmpty()) {
				for (ScenarioEventMapping eventAdd : listEventAdd) {
					session.save(eventAdd);
				}
			}

			// Handle updates
			if (listEventUpdate != null && !listEventUpdate.isEmpty()) {
				for (ScenarioEventMapping eventUpdate : listEventUpdate) {
					TypedQuery<ScenarioEventMapping> query = session.createQuery(
							"FROM ScenarioEventMapping scMap WHERE scMap.scenarioId = :scenarioId AND scMap.eventFacilityId = :eventFacilityId AND scMap.mapKey = :mapKey",
							ScenarioEventMapping.class);
					query.setParameter("scenarioId", eventUpdate.getScenarioId());
					query.setParameter("eventFacilityId", eventUpdate.getEventFacilityId());
					query.setParameter("mapKey", eventUpdate.getMapKey());

					List<ScenarioEventMapping> records = query.getResultList();

					if (!records.isEmpty()) {
						session.merge(eventUpdate);
					}
				}
			}

			// Handle deletions
			if (listEventDelete != null && !listEventDelete.isEmpty()) {
				for (ScenarioEventMapping eventDelete : listEventDelete) {
					session.delete(eventDelete);
				}
			}

			tx.commit();
			log.debug(this.getClass().getName() + " - [crudEventMapping] - Exit");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (HibernateException exception) {
					log.error("HibernateException occurred in [crudEventMapping] when rolling back transaction. Cause: " + exception.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [crudEventMapping] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "crudEventMapping", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

	}
	/**
	 * get deails of P_SCENARIO_GUI_ALERT_MAPPING
	 * @param scenarioId
	 *  @return Collection
	 * @throws SwtException
	 */
	public Collection getGuiHiglightMapping(String scenarioId)
			throws SwtException {

		List<ScenarioGuiAlertMapping> listCriteria = new ArrayList<ScenarioGuiAlertMapping>();
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getGuiHiglightMapping] - Enter");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the mappings and facilities
			String hql = "SELECT guiFac, scMap FROM ScenarioGuiAlertMapping scMap JOIN ScenarioGuiAlertFacility guiFac " +
					"ON scMap.id.guiFacilityId = guiFac.id.guiFacilityId " +
					"WHERE scMap.id.scenarioId = :scenarioId";
			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("scenarioId", scenarioId);

			// Execute the query and get the result list
			List<Object[]> records = query.getResultList();

			// Process the results
			for (Object[] su : records) {
				ScenarioGuiAlertMapping scGuiMapping = (ScenarioGuiAlertMapping) su[1];
				ScenarioGuiAlertFacility scGuiFacility = (ScenarioGuiAlertFacility) su[0];
				scGuiMapping.setGuiFacilityDescription(scGuiFacility.getDescription());
				listCriteria.add(scGuiMapping);
			}

			log.debug(this.getClass().getName() + " - [getGuiHiglightMapping] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getGuiHiglightMapping] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getGuiHiglightMapping", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return listCriteria;
	}

	/**
	 * get deails of P_SCENARIO_EVENT_MAPPING
	 * @param scenarioId
	 *  @return Collection
	 * @throws SwtException
	 */
	public Collection getEventMapping(String scenarioId)
			throws SwtException {

		List<ScenarioEventMapping> listCriteria = new ArrayList<ScenarioEventMapping>();
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getEventMapping] - Enter");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the event mappings and facilities
			String hql = "SELECT eventFac, scMap FROM ScenarioEventMapping scMap " +
					"JOIN ScenarioEventFacility eventFac ON scMap.eventFacilityId = eventFac.id.eventFacilityId " +
					"WHERE scMap.scenarioId = :scenarioId " +
					"ORDER BY scMap.ordinal ASC";
			TypedQuery<Object[]> query = session.createQuery(hql, Object[].class);
			query.setParameter("scenarioId", scenarioId);

			// Execute the query and get the result list
			List<Object[]> records = query.getResultList();

			// Process the results
			for (Object[] su : records) {
				ScenarioEventMapping scEventMapping = (ScenarioEventMapping) su[1];
				ScenarioEventFacility scEventFacility = (ScenarioEventFacility) su[0];
				scEventMapping.setEventFacilityDescription(scEventFacility.getDescription());
				listCriteria.add(scEventMapping);
			}

			log.debug(this.getClass().getName() + " - [getEventMapping] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getEventMapping] method: ", e);
			throw SwtErrorHandler.getInstance().handleException(e, "getEventMapping", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return listCriteria;
	}


	public void saveScenSchedule(List<ScenarioSchedule> listScenScheduleToAdd, List<ScenarioSchedule> listScenScheduleToUpdate, List<ScenarioSchedule> listScenSchedToDelete) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveScenSchedule] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			if (listScenScheduleToAdd != null && listScenScheduleToAdd.size() > 0) {
				for (Iterator iterator = listScenScheduleToAdd.iterator(); iterator.hasNext();) {
					ScenarioSchedule scheduleAdd = (ScenarioSchedule) iterator.next();
					session.save(scheduleAdd);

				}
			}

			if (listScenScheduleToUpdate != null && listScenScheduleToUpdate.size() > 0) {
				for (Iterator iterator = listScenScheduleToUpdate.iterator(); iterator.hasNext();) {
					ScenarioSchedule scheduleUpdate = (ScenarioSchedule) iterator.next();
					session.update(scheduleUpdate);

				}
			}

			if (listScenSchedToDelete != null && listScenSchedToDelete.size() > 0) {
				for (Iterator iterator = listScenSchedToDelete.iterator(); iterator.hasNext();) {
					ScenarioSchedule scheduleDelete = (ScenarioSchedule) iterator.next();
					session.delete(scheduleDelete);

				}
			}

			tx.commit();
			log.debug(this.getClass().getName() + " - [saveScenSchedule] - Exit");
		} catch (Exception e) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in [saveScenSchedule] when rolling back transaction. Cause : "
						+ exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception Catched in [saveScenSchedule] method : - " + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "saveScenSchedule", this.getClass());
		} finally {
			JDBCCloser.close(session);
		}
	}

	public void deleteScenSchedule(String  scenarioId) throws SwtException {

		log.debug("entering 'deleteScenSchedule' method");

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			// Open a new session with the interceptor
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Define the HQL query to retrieve the ScenarioSchedule records
			String hql = "FROM ScenarioSchedule sched WHERE sched.scenarioId = :scenarioId";
			TypedQuery<ScenarioSchedule> query = session.createQuery(hql, ScenarioSchedule.class);
			query.setParameter("scenarioId", scenarioId);

			// Execute the query and get the result list
			List<ScenarioSchedule> scenSchedule = query.getResultList();

			// Delete each record
			if (scenSchedule != null && !scenSchedule.isEmpty()) {
				for (ScenarioSchedule schedule : scenSchedule) {
					session.delete(schedule);
				}
			}

			tx.commit();
			log.debug("Exiting 'deleteScenSchedule' method");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback(); // Rollback the transaction in case of an error
				} catch (HibernateException rollbackException) {
					log.error("HibernateException occurred during rollback in [deleteScenSchedule]: " + rollbackException.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteScenSchedule] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deleteScenSchedule", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

	}

	public Collection getScenarioSchedule(String scenarioId) throws SwtException {
		List<ScenarioSchedule> scenSchedule = null;

		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getScenarioSchedule] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve ScenarioSchedule records
			String hql = "FROM ScenarioSchedule scen WHERE scen.scenarioId = :scenarioId";
			TypedQuery<ScenarioSchedule> query = session.createQuery(hql, ScenarioSchedule.class);
			query.setParameter("scenarioId", scenarioId);

			// Execute the query and get the result list
			scenSchedule = query.getResultList();

			log.debug(this.getClass().getName() + " - [getScenarioSchedule] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getScenarioSchedule] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getScenarioSchedule] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getScenarioSchedule", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return scenSchedule;
	}

	public Long  getEventMappingKey(String scenarioId, String eventFacilityId, Integer ordinal) throws SwtException {

		log.debug(this.getClass().getName() + " - [getEventMappingKey] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String mapKey=null;
		Long key=null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getAccountType = "select map_key from P_SCENARIO_EVENT_MAPPING event where event.scenario_id=? and event.EVENT_FACILITY_ID= ? and event.ORDINAL= ?";
			stmt = conn.prepareStatement(getAccountType);
			stmt.setString(1, scenarioId);
			stmt.setString(2, eventFacilityId);
			stmt.setInt(3, ordinal);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					mapKey = rs.getString(1);
					key=Long.parseLong(mapKey);
				}
			}
			log.debug(this.getClass().getName() + " - [getEventMappingKey] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEventMappingKey] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return key;
	}


	/**
	 * This method is used to get other Id types  from P_SCENARIO_OTHER_ID_TYPES
	 */
	public Collection<LabelValueBean>  getOtherIdTypes() throws SwtException{
		log.debug(this.getClass().getName() + " - [getOtherIdTypes] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Collection<LabelValueBean> types =null;
		try {
			types = new ArrayList<LabelValueBean>();
			conn = ConnectionManager.getInstance().databaseCon();
			String getTypes = "select OTHER_ID_TYPE, DESCRIPTION from P_SCENARIO_OTHER_ID_TYPES ORDER BY  OTHER_ID_TYPE ASC";
			stmt = conn.prepareStatement(getTypes);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					types.add(new LabelValueBean(rs.getString(1),rs.getString(2)));
				}
			}
			log.debug(this.getClass().getName() + " - [getOtherIdTypes] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getOtherIdTypes] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return types;
	}


	public Collection<LabelValueBean>  getEmailTemplates() throws SwtException{
		log.debug(this.getClass().getName() + " - [getEmailTemplates] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Collection<LabelValueBean> templates =null;
		try {
			templates = new ArrayList<LabelValueBean>();
			conn = ConnectionManager.getInstance().databaseCon();
			String getTypes = "select TEMPLATE_ID, DESCRIPTION from P_EMAIL_TEMPLATE ORDER BY  TEMPLATE_ID ASC";
			stmt = conn.prepareStatement(getTypes);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					templates.add(new LabelValueBean(rs.getString(1),rs.getString(2)));
				}
			}
			log.debug(this.getClass().getName() + " - [getEmailTemplates] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEmailTemplates] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return templates;
	}


	public boolean isTemplateIdUsed(String templateId) {
		String sql = "SELECT COUNT(*) FROM P_SCENARIO_EVENT_MAPPING " +
				"WHERE EVENT_FACILITY_ID = 'SEND_EMAIL' " +
				"AND EXTRACTVALUE(XMLTYPE(PARAMETERS_XML), '/mappedParameters/parameter/templateId') = ?";

		try (Connection conn = ConnectionManager.getInstance().databaseCon();
			 PreparedStatement stmt = conn.prepareStatement(sql)) {

			stmt.setString(1, templateId);

			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next()) {
					return rs.getInt(1) > 0;
				}
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Caught in [isTemplateIdUsed] method : - "
					+ exp.getMessage());
		}

		return false;
	}


	public HashMap<String, String>  getFacilityMappingOtherId(String scenarioId) throws SwtException {

		log.debug(this.getClass().getName() + " - [getFacilityMappingOtherId] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String otherId=null;
		String facilityId=null;
		HashMap<String, String> hashMapResult = null;

		try {
			hashMapResult = new HashMap<String, String>();
			conn = ConnectionManager.getInstance().databaseCon();
			String getOtherId = "select other_id, GUI_FACILITY_ID from   P_SCENARIO_GUI_ALERT_MAPPING scMap where scMap.SCENARIO_ID=?";
			stmt = conn.prepareStatement(getOtherId);
			stmt.setString(1, scenarioId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					otherId = rs.getString(1);
					facilityId=rs.getString(2);
					hashMapResult.put(facilityId, otherId);
				}
			}
			log.debug(this.getClass().getName() + " - [getFacilityMappingOtherId] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFacilityMappingOtherId] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return hashMapResult;
	}


	/**
	 * Collects the Gui Facility mapping list from database table P_SCENARIO_GUI_ALERT_MAPPING 
	 * @return Collection
	 * @throws SwtException
	 */
	public List getScenGuiFacilityMappingList(String scenarioId) throws SwtException {
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String facilityId = new String();
		List facilities = null;
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getScenGuiMapping = "select GUI_FACILITY_ID from P_SCENARIO_GUI_ALERT_MAPPING guiMap where guiMap.scenario_id=?";
			stmt = conn.prepareStatement(getScenGuiMapping);
			stmt.setString(1, scenarioId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			facilities = new ArrayList();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					facilityId = rs.getString(1);
					facilities.add(facilityId);
				}
			}
			log.debug(this.getClass().getName() + " - [getAccountStatus] - " + "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getAccountStatus] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return facilities;
	}

	/**
	 * Method to delete the facility mapping
	 *
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	public void deleteFacilitiesMapping(String scenarioId)
			throws SwtException {
		// Fetch the list of screen options

		//declare a connection object
		Connection conn=null;
		//statement object is initialized
		PreparedStatement pst=null;
		//String variable for holding delete query
		String delete_facilities="";
		try {
			logger.debug(this.getClass().getName()
					+ " - [deleteFacilityMapping] - Entry");
			/* Method's local variable declaration */

			conn=ConnectionManager.getInstance().databaseCon();

			delete_facilities="DELETE FROM P_SCENARIO_GUI_ALERT_MAPPING  where SCENARIO_ID=?";
			//declare statement for delete
			pst=conn.prepareStatement(delete_facilities);
			pst.setString(1, scenarioId);
			//execute delete query
			pst.executeUpdate();
			//committed the transaction
			conn.commit();

			logger.debug(this.getClass().getName()
					+ " - [deleteFacilityMapping] - Exit");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - Exception Catched in [deleteFacilityMapping] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteFacilityMapping",
					ScenMaintenanceDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pst, conn, null);

		}

	}


	/**
	 * This method is used to get RECORD_SCENARIO_INSTANCES from P_SCENARIO table
	 */
	public String getScenRcdInstance(String scenarioId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getScenRcdInstance] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String scenRcdInstance = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getScenRcdInstance = "select record_scenario_instances from p_scenario scen where scen.scenario_id=?";
			stmt = conn.prepareStatement(getScenRcdInstance);
			stmt.setString(1, scenarioId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					scenRcdInstance = rs.getString(1);
				}
			}
			log.debug(this.getClass().getName() + " - [getScenRcdInstance] - "
					+ "Exit");
			//return mvtType;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getScenRcdInstance] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return scenRcdInstance;
	}


	public String checkIfInstExist(String scenarioId) throws SwtException {
		log.debug(this.getClass().getName() + " - [checkIfInstExist] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		String isExistFlag= null;
		// Declare Result 
		String result= null;
		try {
			log.debug(this.getClass().getName() + " - [checkIfInstExist] - " + "Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			cstmt = conn.prepareCall("select pkg_alert.FN_EXIST_INST_FOR_SCENARIO(?) from dual");
			cstmt.setString(1, scenarioId);
			rs= cstmt.executeQuery();
			rs.next();
			// Get result
			result = (String) rs.getString(1);
			isExistFlag = result!=null?result:"";

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [checkIfInstExist] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "checkIfInstExist",
					this.getClass());
		}  finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);
		}

		log.debug(this.getClass().getName() + " - [checkIfInstExist] - " + "Exit");

		return isExistFlag;

	}

	/**
	 * Method to delete the scenario events from  P_SCENARIO_EVENT_MAPPING
	 *
	 * @throws SwtException
	 *
	 */
	@SuppressWarnings("unchecked")
	public void deleteEventMapping(String scenarioId)
			throws SwtException {
		// Fetch the list of screen options

		//declare a connection object
		Connection conn=null;
		//statement object is initialized
		PreparedStatement pst=null;
		//String variable for holding delete query
		String delete_events="";
		try {
			logger.debug(this.getClass().getName()
					+ " - [deleteEventMapping] - Entry");
			/* Method's local variable declaration */

			conn=ConnectionManager.getInstance().databaseCon();

			delete_events="DELETE FROM P_SCENARIO_EVENT_MAPPING  where SCENARIO_ID=?";
			//declare statement for delete
			pst=conn.prepareStatement(delete_events);
			pst.setString(1, scenarioId);
			//execute delete query
			pst.executeUpdate();
			//committed the transaction
			conn.commit();

			logger.debug(this.getClass().getName()
					+ " - [deleteEventMapping] - Exit");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - Exception Catched in [deleteEventMapping] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteEventMapping",
					ScenMaintenanceDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pst, conn, null);

		}

	}

	public ScenarioSchedule getSchedulerById(String scenarioId, String scheduledId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getSchedulerById] - " + "Entry");
		Session session = null;
		ScenarioSchedule scenarioSched = null;

		try {
			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the ScenarioSchedule record
			String hql = "FROM ScenarioSchedule sched WHERE sched.scenarioId = :scenarioId AND sched.scenScheduleId = :scheduledId";
			TypedQuery<ScenarioSchedule> query = session.createQuery(hql, ScenarioSchedule.class);
			query.setParameter("scenarioId", scenarioId);
			query.setParameter("scheduledId", scheduledId);

			// Get the single result or null if not found
			scenarioSched = query.getResultStream().findFirst().orElse(null);

			log.debug(this.getClass().getName() + " - [getSchedulerById] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getSchedulerById] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getSchedulerById] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getSchedulerById", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return scenarioSched;
	}


	public void updateScenarioQueryExecutionListColumns(String scenarioId , String queryExecutionListOfcolumns) throws SwtException{
		// Fetch the list of screen options

		//declare a connection object
		Connection conn=null;
		//statement object is initialized
		PreparedStatement pst=null;
		//String variable for holding delete query
		String updateQuery="";
		try {
			logger.debug(this.getClass().getName()
					+ " - [updateScenarioQueryExecutionListColumns] - Entry");
			/* Method's local variable declaration */

			conn=ConnectionManager.getInstance().databaseCon();
			updateQuery="UPDATE P_SCENARIO SET QUERY_EXECUTION_LIST_COLUMNS=? where SCENARIO_ID=?";
			//declare statement for delete
			pst=conn.prepareStatement(updateQuery);
			pst.setString(1, queryExecutionListOfcolumns);
			pst.setString(2, scenarioId);
			//execute delete query
			pst.executeUpdate();
			//committed the transaction
			conn.commit();

			logger.debug(this.getClass().getName()
					+ " - [updateScenarioQueryExecutionListColumns] - Exit");
		} catch (Exception e) {
			logger.error(this.getClass().getName()
					+ " - Exception Catched in [updateScenarioQueryExecutionListColumns] method : - "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateScenarioQueryExecutionListColumns",
					ScenMaintenanceDAOHibernate.class);
		} finally {
			JDBCCloser.close(null, pst, conn, null);

		}

	}



	public ScenarioEventMapping getScenarioEventMapping(Long mapKey) throws SwtException {
		ScenarioEventMapping mapEvent = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getScenarioEventMapping] - Entry");

			// Open a new session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Define the HQL query to retrieve the ScenarioEventMapping record
			String hql = "FROM ScenarioEventMapping cat WHERE cat.mapKey = :mapKey";
			TypedQuery<ScenarioEventMapping> query = session.createQuery(hql, ScenarioEventMapping.class);
			query.setParameter("mapKey", mapKey);

			// Get the single result or null if not found
			mapEvent = query.getResultStream().findFirst().orElse(null);

			log.debug(this.getClass().getName() + " - [getScenarioEventMapping] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " - Exception caught in [getScenarioEventMapping] method: " + exp.getMessage());
			log.error(this.getClass().getName() + " - Exception caught in [getScenarioEventMapping] method: ", exp);
			throw SwtErrorHandler.getInstance().handleException(exp, "getScenarioEventMapping", ScenMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		return mapEvent;

	}

	public Map<String, String> getInstanceKeywordVals(List<String> keywords, Long instanceID) throws SwtException {

		Connection conn = null;
		Session session = null;
		ResultSet rs = null;
		CallableStatement cstmt = null;
		Map<String, String> resultMap = new HashMap<>();

		try {
			log.debug(ScenMaintenanceDAOHibernate.class.getName() + " - [ getInstanceKeywordVals ] - Entry");

			// Open the Hibernate session and get the connection
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);

			StringBuilder sql = new StringBuilder("SELECT keyword, PKG_ALERT.FN_GET_INSTANCE_KEYWORD_VAL(keyword, ?) AS value FROM (");
			for (int i = 0; i < keywords.size(); i++) {
				if (i > 0) sql.append(" UNION ALL ");
				sql.append("SELECT ? AS keyword FROM dual");
			}
			sql.append(")");

			cstmt = conn.prepareCall(sql.toString());

			// Set the instance ID
			cstmt.setLong(1, instanceID);

			// Set the keywords
			int paramIndex = 2;
			for (String keyword : keywords) {
				cstmt.setString(paramIndex++, keyword);
			}

			rs = cstmt.executeQuery();

			while (rs.next()) {
				String keyword = rs.getString("keyword");
				String value = rs.getString("value");
				if ("ERR#NOT_FOUND".equals(value)) {
					log.warn("Keyword value not found for keyword: " + keyword + " and instance ID: " + instanceID);
				} else {
					resultMap.put(keyword, value);
				}
			}
			log.debug(ScenMaintenanceDAOHibernate.class.getName() + " - [ getInstanceKeywordVals ] - Exit");
		} catch (SQLException sqlException) {
			sqlException.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(sqlException, "getInstanceKeywordVals", ScenMaintenanceDAOHibernate.class);
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(ScenMaintenanceDAOHibernate.class.getName() + " - Exception caught in [ getInstanceKeywordVals ] method: " + exp.getMessage());
			throw new SwtException(exp.getMessage());
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null) {
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getInstanceKeywordVals", ScenMaintenanceDAOHibernate.class);
			}

			if (thrownException == null && exceptions[1] != null) {
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getInstanceKeywordVals", ScenMaintenanceDAOHibernate.class);
			}

			if (thrownException != null) {
				throw thrownException;
			}
		}

		return resultMap;
	}
}
