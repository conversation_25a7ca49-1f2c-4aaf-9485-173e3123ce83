/*
 * @(#)MovementRecoveryDAO.java 03/05/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao;

import org.swallow.control.model.ProcessStatus;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementLock;

import java.util.Collection;
import java.util.List;

public interface MultipleMvtUpdatesDAO extends DAO {

    public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException;

    public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId)throws SwtException;

    public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException;
    public Collection<Movement> getMovementList(List<Long> movementIds) throws SwtException;
    public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException;

    void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException;
    int getProcessStatus(String seq) throws SwtException;
    void cleanTempProcess(String seq) throws SwtException;
    ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException;
}
