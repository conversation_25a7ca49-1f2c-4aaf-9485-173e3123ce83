/*
 * @(#)UserMaintenanceAction.java 1.0 06/12/2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;









import org.swallow.control.model.PasswordHistory;
import org.swallow.control.model.UserMaintenance;
import org.swallow.control.model.UserStatus;
import org.swallow.control.service.SectionManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR> / Mayank Tripathi This class has been re-written by Mayank
 *         Tripathi on 26 Sep 07 * Preferences - Java - Code Style - Code
 *         Templates
 */
/*
 * This is action class for User screen
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/usermaintenance", "/usermaintenance.do"})
public class UserMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("success", "jsp/control/usermaintenance");
		viewMap.put("add", "jsp/control/usermaintenanceadd");
		viewMap.put("save", "jsp/control/usermaintenanceadd");
		viewMap.put("adduser", "jsp/control/usermaintenanceadvanceduser");
//		viewMap.put("saveuser", "jsp/control/usermaintenanceadvanceduser");
		viewMap.put("fail", "error");
//		viewMap.put("view", "jsp/control/usermaintenanceview");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();




	private UserMaintenance usermaintenance;
	public UserMaintenance getUsermaintenance() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		usermaintenance = RequestObjectMapper.getObjectFromRequest(UserMaintenance.class, request);
		return usermaintenance;
	}

	public void setUsermaintenance(UserMaintenance usermaintenance) {
		this.usermaintenance = usermaintenance;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("usermaintenance", usermaintenance);
	}

	private final String dummyPass = "###############";

	private String strAdvancedUserFlag = SwtConstants.YES;

	private final Log log = LogFactory.getLog(UserMaintenanceAction.class);
	@Autowired
	private UserMaintenanceManager usermaintenanceManager;

	private static String updatedValue = "n";

	/**
	 * @param usermaintenanceManager
	 */
	public void setUsermaintenanceManager(
			UserMaintenanceManager usermaintenanceManager) {
		this.usermaintenanceManager = usermaintenanceManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "add":
				return add();
			case "roleEntityAccess":
				return roleEntityAccess();
			case "displayCcyGrpListByEntity":
				return displayCcyGrpListByEntity();
			case "save":
				return save();
			case "report":
				return report();
			case "delete":
				return delete();
			case "change":
				return change();
			case "update":
				return update();
			case "view":
				return view();
			case "getAdvancedUserRights":
				return getAdvancedUserRights();
		}


		return unspecified();
	}
	/**
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
		return display();
	}

	/**
	 * @return
	 * @throws Exception
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SysParams sysParams = null;
		SysParamsManager sysParamsManager = null;
		try {
			log.debug("Entering UserMaintenanceAction.display method");
			putUserInRequest(request);
			UserMaintenance usermaintenance = new UserMaintenance();
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			putUserInRequest(request);
			String hostId= SwtUtil.getCurrentHostId();
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			if(session != null){
				if(SwtUtil.isEmptyOrNull(objUser.getAmountDelimiter())||SwtUtil.isEmptyOrNull(objUser.getDateFormat())) {
					// Getting system parameter details from manager class
					sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
					sysParams = sysParamsManager.getSysParamsDetail(hostId, SwtUtil.getCurrentSystemFormats(request.getSession()));
					String amountDelimiter =sysParams.getAmountDelimiter();
					String dateformat=sysParams.getDateFormat();

					if(SwtUtil.isEmptyOrNull(objUser.getAmountDelimiter())) {
						usermaintenance.setAmountDelimiter(amountDelimiter);
					}

					if(SwtUtil.isEmptyOrNull(objUser.getDateFormat())) {
						usermaintenance.setDateFormat(dateformat);
					}
				}
			}
			setUsermaintenance(usermaintenance);
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in UserMaintenanceAction.'display' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", UserMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putUserInRequest(HttpServletRequest request)
			throws SwtException {

		String hostId = SwtUtil.getCurrentHostId();
		String roleId = SwtUtil.getCurrentUser(request.getSession())
				.getRoleId();
		Collection collEntity = usermaintenanceManager.getUserList(hostId,
				roleId);

		request.setAttribute("entities", collEntity);
		String userIdInSession = SwtUtil.getCurrentUserId(request.getSession());
		request.setAttribute("currentUserId", userIdInSession);
	}

	/**
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	private void putCurrencyGroupDetailsinRequest(HttpServletRequest request,
												  String roleId, String entityId) throws SwtException {
		List currrencyGroupList = new ArrayList();
		HttpSession session = request.getSession();
		currrencyGroupList = (List) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);

		if (currrencyGroupList != null && currrencyGroupList.size() > 0) {
			currrencyGroupList.add(0, new LabelValueBean(
					SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
		}

		request.setAttribute("currencyGroupList", currrencyGroupList);
	}

	/**
	 * @return throws Exception
	 */
	public String add()
			throws SwtException {
		try {
			log.debug("Entering UserMaintenanceAction.add method");
			updatedValue = "n";
			SystemInfo systemInfo = new SystemInfo();
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			UserMaintenance usermaintenance = (UserMaintenance) getUsermaintenance();
			usermaintenance.setStatus("1");
			String hostId = CacheManager.getInstance().getHostId();
			UserMaintenance usrmain = new UserMaintenance();
			SectionManager sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));

			Collection sectionlist = sectionManager
					.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionlist);

			String currentUserRoleId = SwtUtil.getCurrentUser(
					request.getSession()).getRoleId();
			Collection currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			Iterator currentEntityItr = currentRoleEntityAccessList.iterator();
			String currentUserEntityIds = "";
			while (currentEntityItr.hasNext()) {
				LabelValueBean role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			Collection roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);

			String roleId = null;
			Iterator roleIdColl = roleList.iterator();
			if (roleIdColl.hasNext()) {
				LabelValueBean role = (LabelValueBean) roleIdColl.next();
				roleId = role.getValue();
			}
			String entityId = null;
			Collection roleEntity = usermaintenanceManager
					.getRoleEntityAccess(roleId);
			Iterator roleEntityColl = roleEntity.iterator();
			if (roleEntityColl.hasNext()) {
				LabelValueBean entity = (LabelValueBean) roleEntityColl.next();
				entityId = entity.getValue();
			}
			CacheManager cacheManagerInst = CacheManager.getInstance();
			Collection languagelist = (ArrayList) cacheManagerInst
					.getMiscParamsLVL("LANG", entityId);
			request.setAttribute("languagedetails", languagelist);
			request.setAttribute("roleEntityList", roleEntity);
			putCurrencyGroupDetailsinRequest(request, roleId, entityId);
			request.setAttribute("roleIdList", roleList);
			request.setAttribute("screenFieldsStatus", "false");
			request.setAttribute("methodName", "save");
			// Getting system parameter details from manager class
			SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
			SysParams sysParams = sysParamsManager.getSysParamsDetail(hostId, SwtUtil.getCurrentSystemFormats(request.getSession()));
			usermaintenance.setAmountDelimiter(sysParams.getAmountDelimiter());
			usermaintenance.setDateFormat(sysParams.getDateFormat());

			setUsermaintenance(usermaintenance);
			log.debug("Exiting from userMaintenaceAction.'ADD' method");

			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'add' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, SwtConstants.EMPTY_STRING);

			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			return getView("add");
		} catch (Exception e) {
			log
					.error("Exception Catch in UserMaintenanceAction.'add' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "add", UserMaintenanceAction.class), request, "");
			return getView("fail");
		}

	}

	/**
	 * populating the entity drop down When changing user role
	 *
	 * @return
	 * @throws Exception
	 */
	public String roleEntityAccess() throws SwtException {
		// Variable to hold dyForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold usermaintenance object
		UserMaintenance userMaintenance = null;
		// Variable to hold sectionManager object
		SectionManager sectionManager = null;
		// Variable to hold cacheManagerInst object
		CacheManager cacheManagerInst = null;
		// Variable to hold systemFormats object
		SystemFormats systemFormats = null;
		// Variable to hold systemInfo object
		SystemInfo systemInfo = null;
		// Collection variable to sectionlist
		Collection sectionList = null;
		// Collection variable to roleList
		Collection roleList = null;
		// Collection variable to roleEntity
		Collection roleEntity = null;
		// Collection variable to entColl
		Collection entColl = null;
		// Collection variable to languagelist
		Collection languageList = null;
		// List variable to pwdChangedate
		List pwdChangeDate = null;
		// Collection variable to currentRoleEntityAccessList
		Collection currentRoleEntityAccessList = null;
		// List variable to lastLoginInfo
		List lastLoginInfo = null;
		// Variable to hold currentEntityItr object
		Iterator currentEntityItr = null;
		// Variable to hold itrEntity object
		Iterator itrEntity = null;
		// Variable to hold entityCollItr object
		Iterator entityCollItr = null;
		// Variable to hold lastLoginItr object
		Iterator lastLoginItr = null;
		// Object to hold entity object
		Object entity = null;
		// String variable to entityId
		String entityId = null;
		// String variable to hostId
		String hostId = null;
		// String variable to currentUserRoleId
		String currentUserRoleId = null;
		// String variable to userId
		String userId = null;
		// String variable to userName
		String userName = null;
		// String variable to roleId
		String roleId = null;
		// String variable to sectionId
		String sectionId = null;
		// String variable to language
		String language = null;
		// String variable to phNumber
		String phNumber = null;
		// String variable to emailId
		String emailId = null;
		// String variable to pwd
		String userPassword = null;
		// String variable to lastChangePasswordDateStr
		String lastChangePasswordDateStr = null;
		// String variable to loginDate
		String loginDateStr = null;
		// String variable to logoutDate
		String logoutDateStr = null;
		// Variable to hold logindate object
		Date loginDate = null;
		// Variable to hold logoutdate object
		Date logoutDate = null;
		// Variable to hold lastChangePasswordDate object
		Date lastChangePasswordDate = null;
		// Variable to hold userStatus object
		UserStatus userStatus = null;
		// Variable to hold pwdHist object
		PasswordHistory pwdHist = null;
		// String variable to currentUserEntityIds
		String currentUserEntityIds = "";
		// Variable to hold lvbEntity object
		LabelValueBean lvbEntity = null;
		// Variable to hold lvbEntityColl object
		LabelValueBean lvbEntityColl = null;
		// Variable to hold sdfLoginInfo object
		SimpleDateFormat sdfLoginInfo = null;
		// Variable to hold loginInfoListSize
		int loginInfoListSize = 0;
		// Variable to hold pwdChangedateSize
		int pwdChangeDateSize = 0;
		// Variable to hold lvbRole object
		LabelValueBean lvbRole = null;
		// Variable to hold entityFlag
		boolean entityFlag = false;

		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [roleEntityAccess] - Entering ");
			// Setting screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "false");
			// Getting host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Creating systemInfo object
			systemInfo = new SystemInfo();
// To remove: 			dyForm = (DynaValidatorForm) form;
			userMaintenance = (UserMaintenance) getUsermaintenance();
			/* Setting user id and user name using bean class */
			userId = userMaintenance.getId().getUserId();
			userName = userMaintenance.getUsername();
			sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));
			/* Retrieve the section details and store in collection */
			sectionList = sectionManager.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionList);
			/* Read the current user role id */
			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			/* Read the role entity access using bean class */
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			currentEntityItr = currentRoleEntityAccessList.iterator();
			/* Loop to iterate the records */
			while (currentEntityItr.hasNext()) {
				lvbRole = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ lvbRole.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ lvbRole.getValue() + "'";
				}
			}


			/* Fetches the role list by calling manager class */
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			request.setAttribute("roleIdList", roleList);
			/* Getting user roleid using bean class */
			roleId = userMaintenance.getRoleId();
			/* Fetches the role entity access using bean class */
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			request.setAttribute("roleEntityList", roleEntity);
			/* Get the Current User Entity */
			entityId = userMaintenance.getCurrententity();
			/*
			 * Conditions to check the role entity is empty and get the current
			 * role id
			 */
			if (roleEntity != null && roleEntity.size() > 0) {
				itrEntity = roleEntity.iterator();
				/* Loop to iterate the records */
				while (itrEntity.hasNext()) {
					lvbEntity = (LabelValueBean) itrEntity.next();
					if (entityId.equals(lvbEntity.getValue())) {
						entityFlag = true;
						break;
					}
				}
				if (!entityFlag)
					entityId = ((LabelValueBean) roleEntity.toArray()[0])
							.getValue();
				else
					entityId = userMaintenance.getCurrententity();
			} else {
				entityId = userMaintenance.getCurrententity();
			}
			entColl = (Collection) request.getAttribute("currencies");
			if (entColl != null) {
				entityCollItr = entColl.iterator();
				while (entityCollItr.hasNext()) {
					lvbEntityColl = (LabelValueBean) (entityCollItr.next());
					break;
				}
			}
			cacheManagerInst = CacheManager.getInstance();
			userMaintenance.setCurrententity(entityId);
			/* Retrieve the language details and store in collection */
			languageList = (ArrayList) cacheManagerInst.getMiscParamsLVL(
					"LANG", entityId);
			request.setAttribute("languagedetails", languageList);
			putCurrencyGroupDetailsinRequest(request, roleId, entityId);
			/* Used to put the user list in request */
			putUserInRequest(request);
			userMaintenance.setUsername(userName);
			/*
			 * Setting Section id,language,phone number,emailid and password
			 * using bean class
			 */
			sectionId = userMaintenance.getSectionid();
			language = userMaintenance.getLanguage();
			phNumber = userMaintenance.getPhonenumber();
			emailId = userMaintenance.getEmailId();
			userPassword = userMaintenance.getPassword();
			userId = request.getParameter("usermaintenance.id.userId");
			/* Start:code modified by sudhakar on 13-9-2011 for Mantis 1569 */
			// get and set last date of password change
			pwdChangeDate = (List) usermaintenanceManager
					.getLastPasswordChangeDate(hostId, userId);
			// Get the pwdChangedateSize
			pwdChangeDateSize = pwdChangeDate.size() - 1;
			if (pwdChangeDateSize > -1) {
				pwdHist = (PasswordHistory) pwdChangeDate
						.get(pwdChangeDateSize);
				// Getting lastChangePasswordDate from pwdHist
				lastChangePasswordDate = pwdHist.getUpdateDate();
			}
			systemFormats = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			// Check the condition lastChangePasswordDate is not equal to null
			if (lastChangePasswordDate != null) {
				lastChangePasswordDateStr = SwtUtil.formatDate(
						lastChangePasswordDate, systemFormats
								.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				lastChangePasswordDateStr = lastChangePasswordDateStr + " "
						+ sdfLoginInfo.format(lastChangePasswordDate);
				request.setAttribute("uDate", lastChangePasswordDateStr);
			}
			// get and set lastLogin date Info
			lastLoginInfo = (List) usermaintenanceManager.getLoginDateInfo(
					hostId, userId);
			// Getting loginInfoListSize
			loginInfoListSize = lastLoginInfo.size() - 1;
			if (loginInfoListSize > -1) {
				userStatus = (UserStatus) lastLoginInfo.get(loginInfoListSize);
				// Getting loginDate from userStatus
				loginDate = userStatus.getId().getLogOnTime();
				// Getting logoutDate from userStatus
				logoutDate = userStatus.getLogOutTime();
			}
			// Check the condition logindate is not equal to null
			if (loginDate != null) {
				loginDateStr = SwtUtil.formatDate(loginDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				loginDateStr = loginDateStr + " "
						+ sdfLoginInfo.format(loginDate);
				request.setAttribute("loginDate", loginDateStr);
			}else {
				if(!SwtUtil.isEmptyOrNull(request.getParameter("loginDate"))) {
					request.setAttribute("loginDate", request.getParameter("loginDate"));
				}

			}
			// getting logoutdate Info
			logoutDate = usermaintenanceManager.getLastLogoutDate(hostId,
					userId);
			// Check the condition logoutdate is not equal to null
			if (logoutDate != null) {
				logoutDateStr = SwtUtil.formatDate(logoutDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				logoutDateStr = logoutDateStr + " "
						+ sdfLoginInfo.format(logoutDate);
				/* End:code modified by sudhakar on 13-9-2011 for Mantis 1569 */
				request.setAttribute("logoutDate", logoutDateStr);
			}else {
				if(!SwtUtil.isEmptyOrNull(request.getParameter("logoutDate"))) {
					request.setAttribute("logoutDate", request.getParameter("logoutDate"));
				}

			}
			if(!SwtUtil.isEmptyOrNull(request.getParameter("extAuthId"))) {
				request.setAttribute("extAuthId", request.getParameter("extAuthId"));
				request.setAttribute("lockedExternalAuthId", request.getParameter("lockedExternalAuthId"));
			}

			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			User oldUserDetails = logon.getUserDetail(hostId, userId);

			if(oldUserDetails != null) {

				lastFailedIP = oldUserDetails.getLastLoginFailedIp();
				if(lastFailedIP != null)
				{
					request.setAttribute("lastFailedIP" , lastFailedIP);
				}
				lastLoginIP = oldUserDetails.getLastLoginIp();
				if(lastLoginIP != null)
				{
					request.setAttribute("lastLoginIP" , lastLoginIP);
				}

				lastFailedndate = oldUserDetails.getLastLoginFailed();

				if(lastFailedndate != null)
				{
					String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
					lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
					request.setAttribute("lastFaileddate" , lastFailedndateDate);
				}
			}


			// Check the condition updatedValue is equal to y
			if (updatedValue.equals("y")) {
				request.setAttribute("disableRoleId", "disableRoleId");
				request.setAttribute("methodName", "update");
			} else {
				request.setAttribute("methodName", "save");
			}
			setUsermaintenance(userMaintenance);
			// Setting screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "false");
			// Setting advancedUser in request
			request.setAttribute("advancedUser", SwtConstants.YES);
			log.debug(this.getClass().getName()
					+ "- [roleEntityAccess] - Exit ");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'roleEntityAccess' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("advancedUser", SwtConstants.YES);
			return getView("add");
		} catch (Exception e) {
			log
					.error("Exception Catch in UserMaintenanceAction.'roleEntityAccess' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "roleEntityAccess", UserMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
// To remove: 			dyForm = null;
			userMaintenance = null;
			sectionManager = null;
			cacheManagerInst = null;
			systemFormats = null;
			systemInfo = null;
			sectionList = null;
			roleList = null;
			roleEntity = null;
			entColl = null;
			languageList = null;
			pwdChangeDate = null;
			currentRoleEntityAccessList = null;
			lastLoginInfo = null;
			currentEntityItr = null;
			itrEntity = null;
			entityCollItr = null;
			lastLoginItr = null;
			entity = null;
			entityId = null;
			hostId = null;
			currentUserRoleId = null;
			userId = null;
			userName = null;
			roleId = null;
			sectionId = null;
			language = null;
			phNumber = null;
			emailId = null;
			userPassword = null;
			lastChangePasswordDateStr = null;
			loginDateStr = null;
			logoutDateStr = null;
			loginDate = null;
			logoutDate = null;
			lastChangePasswordDate = null;
			userStatus = null;
			pwdHist = null;

		}
	}

	/**
	 * For Populating Currency Grp dropdown When Changing Default Entity
	 *
	 * @return
	 * @throws Exception
	 */
	public String displayCcyGrpListByEntity() throws SwtException {
		// Variable to hold dyForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold usermaintenance object
		UserMaintenance userMaintenance = null;
		// Variable to hold systemInfo object
		SystemInfo systemInfo = null;
		// String variable to hostId
		String hostId = null;
		// String variable to userId
		String userId = null;
		// String variable to userName
		String userName = null;
		// Variable to hold sectionManager object
		SectionManager sectionManager = null;
		// Collection variable to sectionlist
		Collection sectionList = null;
		// Variable to hold cacheManagerInst object
		CacheManager cacheManagerInst = null;
		// Collection variable to languagelist
		Collection languageList = null;
		// String variable to currentUserRoleId
		String currentUserRoleId = null;
		// Collection variable to currentRoleEntityAccessList
		Collection currentRoleEntityAccessList = null;
		// Iterator variable to currentEntityItr
		Iterator currentEntityItr = null;
		// String variable to currentUserEntityIds
		String currentUserEntityIds = "";
		// LabelValueBean variable to role
		LabelValueBean role = null;
		// Collection variable to roleList
		Collection roleList = null;
		// String variable to roleId
		String roleId = null;
		// Collection variable to roleEntity
		Collection roleEntity = null;
		// String variable to entityId
		String entityId = null;
		// String variable to sectionId
		String sectionId = null;
		// String variable to language
		String language = null;
		// String variable to phNumber
		String phNumber = null;
		// String variable to emailId
		String emailId = null;
		// String variable to pwd
		String userPwd = null;
		// List variable to pwdChangedate
		List pwdChangedate = null;
		// Variable to hold udate object
		Date lastChangePasswordDate = null;
		// String variable to uDateStr
		String lastChangePasswordDateStr = null;
		// Variable to hold itr object
		Iterator pwdChangeDateItr = null;
		// Variable to hold pwdHist object
		PasswordHistory pwdHist = null;
		// Variable to hold systemFormats object
		SystemFormats systemFormats = null;
		// Variable to hold simpleDateFormat object
		SimpleDateFormat sdfLoginInfo = null;
		// Variable to hold logindate object
		Date loginDate = null;
		// String variable to loginDateStr
		String loginDateStr = null;
		// Variable to hold logoutdate object
		Date logoutDate = null;
		// String variable to logoutDateStr
		String logoutDateStr = null;
		// Variable to hold usrSts object
		UserStatus userStatus = null;
		// List variable to lastLoginInfo
		List lastLoginInfo = null;
		// variable to hold loginInfoListSize
		int loginInfoListSize = 0;
		// variable to hold pwdChangedateSize
		int pwdChangeDateSize = 0;

		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;


		try {
			log.debug(this.getClass().getName()
					+ "- [displayCcyGrpListByEntity] - Entering ");
// To remove: 			dyForm = (DynaValidatorForm) form;
			// getting usermaintenance from the dyForm
			userMaintenance = (UserMaintenance) getUsermaintenance();
			// creating the systemInfo object
			systemInfo = new SystemInfo();
			// Set screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "false");
			// getting hostid from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// getting userId from usermaintenance form
			userId = userMaintenance.getId().getUserId();
			userName = userMaintenance.getUsername();
			// Getting sectionManager bean
			sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));
			sectionList = sectionManager.getSectionListAsLebelBean(hostId);
			// Set sectionlist in request
			request.setAttribute("sectiondetails", sectionList);
			// Getting cacheManagerInst object from CacheManager
			cacheManagerInst = CacheManager.getInstance();
			languageList = (ArrayList) cacheManagerInst.getMiscParamsLVL(
					"LANG", userMaintenance.getCurrententity());
			// Set languagelist in request
			request.setAttribute("languagedetails", languageList);

			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			// Getting currentRoleEntityAccessList from usermaintenanceManager
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			currentEntityItr = currentRoleEntityAccessList.iterator();
			// Loop tp iterate current entity
			while (currentEntityItr.hasNext()) {
				role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			// Getting roleList from usermaintenanceManager
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			request.setAttribute("roleIdList", roleList);

			roleId = userMaintenance.getRoleId();
			// Getting roleEntity from usermaintenanceManager
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			// Set roleEntity in request
			request.setAttribute("roleEntityList", roleEntity);

			entityId = userMaintenance.getCurrententity();
			putCurrencyGroupDetailsinRequest(request, roleId, entityId);
			putUserInRequest(request);
			// Set userName to usermaintenance form
			userMaintenance.setUsername(userName);
			// Getting sectionId usermaintenance form
			sectionId = userMaintenance.getSectionid();
			// Getting language usermaintenance form
			language = userMaintenance.getLanguage();
			// Getting phNumber usermaintenance form
			phNumber = userMaintenance.getPhonenumber();
			// Getting emailId usermaintenance form
			emailId = userMaintenance.getEmailId();
			// Getting pwd usermaintenance form
			userPwd = userMaintenance.getPassword();
			/* Start:code modified by sudhakar on 13-9-2011 for Mantis 1569 */
			// Getting pwdChangedate from usermaintenanceManager
			pwdChangedate = (List) usermaintenanceManager
					.getLastPasswordChangeDate(hostId, userId);
			// Getting pwdChangedateSize from pwdChangedate
			pwdChangeDateSize = pwdChangedate.size() - 1;
			if (pwdChangeDateSize > -1) {
				pwdHist = (PasswordHistory) pwdChangedate
						.get(pwdChangeDateSize);
				// Getting lastChangePasswordDate from pwdHist
				lastChangePasswordDate = pwdHist.getUpdateDate();
			}
			// Getting current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			// Check the condition Password date is not equal to null
			if (lastChangePasswordDate != null) {
				lastChangePasswordDateStr = SwtUtil.formatDate(
						lastChangePasswordDate, systemFormats
								.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				lastChangePasswordDateStr = lastChangePasswordDateStr + " "
						+ sdfLoginInfo.format(lastChangePasswordDate);
				request.setAttribute("uDate", lastChangePasswordDateStr);
			}
			// Getting lastLoginInfo from usermaintenanceManager
			lastLoginInfo = (List) usermaintenanceManager.getLoginDateInfo(
					hostId, userId);
			// Getting loginInfoListSize from lastLoginInfo
			loginInfoListSize = lastLoginInfo.size() - 1;
			if (loginInfoListSize > -1) {
				userStatus = (UserStatus) lastLoginInfo.get(loginInfoListSize);
				loginDate = userStatus.getId().getLogOnTime();
				logoutDate = userStatus.getLogOutTime();
			}
			// Check the condition logindate date is not equal to null
			if (loginDate != null) {
				loginDateStr = SwtUtil.formatDate(loginDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				loginDateStr = loginDateStr + " "
						+ sdfLoginInfo.format(loginDate);
				request.setAttribute("loginDate", loginDateStr);
			}else {
				if(!SwtUtil.isEmptyOrNull(request.getParameter("loginDate"))) {
					request.setAttribute("loginDate", request.getParameter("loginDate"));
				}

			}
			// getting logoutdate Info
			logoutDate = usermaintenanceManager.getLastLogoutDate(hostId,
					userId);
			// Check the condition logoutdate date is not equal to null
			if (logoutDate != null) {
				logoutDateStr = SwtUtil.formatDate(logoutDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				logoutDateStr = logoutDateStr + " "
						+ sdfLoginInfo.format(logoutDate);
				/* End:code modified by sudhakar on 13-9-2011 for Mantis 1569 */
				request.setAttribute("logoutDate", logoutDateStr);
			}else {
				if(!SwtUtil.isEmptyOrNull(request.getParameter("logoutDate"))) {
					request.setAttribute("logoutDate", request.getParameter("logoutDate"));
				}

			}
			if(!SwtUtil.isEmptyOrNull(request.getParameter("extAuthId"))) {
				request.setAttribute("extAuthId", request.getParameter("extAuthId"));
				request.setAttribute("lockedExternalAuthId", request.getParameter("lockedExternalAuthId"));
			}

			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			User oldUserDetails = logon.getUserDetail(hostId, userId);

			if(oldUserDetails != null) {


				lastFailedIP = oldUserDetails.getLastLoginFailedIp();
				if(lastFailedIP != null)
				{
					request.setAttribute("lastFailedIP" , lastFailedIP);
				}
				lastLoginIP = oldUserDetails.getLastLoginIp();
				if(lastLoginIP != null)
				{
					request.setAttribute("lastLoginIP" , lastLoginIP);
				}

				lastFailedndate = oldUserDetails.getLastLoginFailed();

				if(lastFailedndate != null)
				{
					String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
					lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
					request.setAttribute("lastFaileddate" , lastFailedndateDate);
				}
			}

			// Check the condition updatedValue is equal to y
			if (updatedValue.equals("y")) {
				request.setAttribute("disableRoleId", "disableRoleId");
				request.setAttribute("methodName", "update");
			} else {
				request.setAttribute("methodName", "save");
			}
			// Setting usermaintenance in dyform
			setUsermaintenance(userMaintenance);
			// Setting screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "false");
			// Setting advancedUser in request
			request.setAttribute("advancedUser", SwtConstants.YES);
			log.debug(this.getClass().getName()
					+ "- [displayCcyGrpListByEntity] - Exit ");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'displayCcyGrpListByEntity' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			request.setAttribute("advancedUser", SwtConstants.YES);

			return getView("add");
		} catch (Exception e) {
			log
					.error("Exception Catch in UserMaintenanceAction.'displayCcyGrpListByEntity' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(e, "displayCcyGrpListByEntity",
							UserMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
// To remove: 			dyForm = null;
			userMaintenance = null;
			systemInfo = null;
			hostId = null;
			userId = null;
			userName = null;
			sectionManager = null;
			sectionList = null;
			cacheManagerInst = null;
			languageList = null;
			currentUserRoleId = null;
			currentRoleEntityAccessList = null;
			currentEntityItr = null;
			role = null;
			roleList = null;
			roleId = null;
			roleEntity = null;
			entityId = null;
			sectionId = null;
			language = null;
			phNumber = null;
			emailId = null;
			userPwd = null;
			pwdChangedate = null;
			lastChangePasswordDate = null;
			lastChangePasswordDateStr = null;
			pwdChangeDateItr = null;
			pwdHist = null;
			systemFormats = null;
			sdfLoginInfo = null;
			loginDate = null;
			loginDateStr = null;
			logoutDate = null;
			logoutDateStr = null;
			userStatus = null;
		}
	}
	/*Start:Code modified by Alibasha for Mantis 1608 for serverlog issues */
	/**
	 * This is used to save the records in S_USER table
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Methods local variable declaration */
		ActionMessages errors = null;
		// variable to hold dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold usermaintenance
		UserMaintenance userMaintenance = null;
		// variable to hold systemInfo
		SystemInfo systemInfo = null;
		// variable to hold pwdHist
		PasswordHistory pwdHist = null;
		// variable to hold sectionManager
		SectionManager sectionManager = null;
		// variable to hold cacheManagerInst
		CacheManager cacheManagerInst = null;
		// variable to hold session
		HttpSession session = null;
		// variable to hold objUser
		User objUser = null;
		// variable to hold roleId
		String roleId = null;
		// variable to hold userId
		String userId = null;
		// variable to hold userIdInSession
		String userIdInSession = null;
		// variable to hold hostId
		String hostId = null;
		// variable to hold pwd
		String pwd = null;
		// variable to hold sectionlist
		Collection<LabelValueBean> sectionList = null;
		// variable to hold languagelist
		Collection<MiscParams> languageList = null;
		// variable to hold roleEntity
		Collection<LabelValueBean> roleEntity = null;
		// variable to hold roleList
		Collection<LabelValueBean> roleList = null;
		// variable to hold currentRoleEntityAccessList
		Collection<LabelValueBean> currentRoleEntityAccessList = null;
		// variable to hold currentEntityItr
		Iterator<LabelValueBean> currentEntityItr = null;
		// variable to hold currentUserEntityIds
		String currentUserEntityIds = "";
		// variable to hold currentUserRoleId
		String currentUserRoleId = null;
		// variable to hold extAuthId
		String extAuthId = null;

		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			// create an instance of errors
			errors = new ActionMessages();
			// getting the dynavalidatorform from form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// getting userMaintenance from dyform
			userMaintenance = (UserMaintenance) getUsermaintenance();
			// create SystemInfo instance
			systemInfo = new SystemInfo();
			// set screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "false");
			// get hostid from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// set hostId in userMaintenance
			userMaintenance.getId().setHostId(hostId);

			extAuthId = request.getParameter("extAuthId");


			/* Get the current user id from swtutil file */
			userIdInSession = SwtUtil.getCurrentUserId(request.getSession());
			/* Setting host id */
			userMaintenance.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			systemInfo.setIpAddress(request.getRemoteAddr());
			// BEGIN: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
			String encryptedPwd=request.getParameter("encnewpasswd");

			// Added by Saber Chebka: Keep the key creation out of radar.. (TODO: add obfuscation for this class to prevent reverse engineering)
			String pass = request.getSession().getId().substring(0, request.getSession().getId().length() > 12 ? 12 : request.getSession().getId().length());
			pass+=userMaintenance.getId().getUserId().substring(0,userMaintenance.getId().getUserId().length()>4?4:userMaintenance.getId().getUserId().length());
//			pass = SwtUtil.hash(pass);

			userMaintenance.setPassword(SwtUtil.decryptCBC(pass, encryptedPwd));
			String encryptedConfirmPwd=request.getParameter("encconfirmpasswd");
			userMaintenance.setConfirmPassword(SwtUtil.decryptCBC(pass, encryptedConfirmPwd));
			// END: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...

			/* setting the password change date */
			userMaintenance.setPasswordchangedate(new Date());
			User user = new User();
			/*
			 * Setting the host id,user id,user name,section is and language
			 * using bean class
			 */
			user.getId().setHostId(hostId);
			user.getId().setUserId(userMaintenance.getId().getUserId());
			user.setUserName(userMaintenance.getUsername());
			user.setSectionId(userMaintenance.getSectionid());
			user.setLanguage(userMaintenance.getLanguage());
			/*
			 * Setting the phone number,email id,role id and password using bean
			 * class
			 */
			user.setPhoneNumber(userMaintenance.getPhonenumber());
			user.setEmailId(userMaintenance.getEmailId());
			user.setRoleId(userMaintenance.getRoleId());
			user.setPassword(userMaintenance.getPassword());
			/*
			 * Setting current entity,password change date,invalid password
			 * attempt using bean class
			 */
			user.setCurrentEntity(userMaintenance.getCurrententity());
			user.setStatus(userMaintenance.getStatus());

			user.setPasswordChangeDate(SwtUtil.getSystemDatewithTime());

//			user.setInvPassAttempt(new Integer(0));
			user.setInvPassAttempt(Integer.valueOf(0));

			/* Setting currency group id using bean class */
			user.setCurrentCcyGrpId(userMaintenance.getCurrentCcyGrpId());

			user.setAmountDelimiter(userMaintenance.getAmountDelimiter());
			user.setDateFormat(userMaintenance.getDateFormat());
			/* Getting password using bean class */
			pwd = userMaintenance.getPassword();
			pwdHist = new PasswordHistory();
			/* Setting user id,host id and password using bean class */
			userId = userMaintenance.getId().getUserId();
			pwdHist.getId().setHostId(hostId);
			pwdHist.getId().setUserId(userId);
			pwdHist.setPassword(SwtUtil.encodePassword(pwd, userId));
			if(!SwtUtil.isEmptyOrNull(extAuthId))
				user.setExtAuthId(extAuthId);
			/* Save the records by calling manager class */
			usermaintenanceManager.saveUserDetail(user, systemInfo, SwtUtil
					.getCurrentSystemFormats(request.getSession()), pwdHist);
			setUsermaintenance(userMaintenance);
			request.setAttribute("parentFormRefresh", "yes");

			sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));
			/* Retrieve the section details and store in collection */
			sectionList = sectionManager.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionList);

			cacheManagerInst = CacheManager.getInstance();
			/* Retrieve the language details and store in collection */
			languageList = (ArrayList) cacheManagerInst.getMiscParamsLVL(
					"LANG", userMaintenance.getCurrententity());

			request.setAttribute("languagedetails", languageList);

			/* Used to put the user list in request */
			putUserInRequest(request);

			/* Getting user id using bean class */
			roleId = userMaintenance.getRoleId();
			/* Fetches the role entity access using bean class */
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			request.setAttribute("roleEntityList", roleEntity);

			/* Read the current user role id */
			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			/* Read the role entity access using bean class */
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			currentEntityItr = currentRoleEntityAccessList.iterator();

			/* Loop to iterate the records */
			while (currentEntityItr.hasNext()) {
				LabelValueBean role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			/* Fetches the role list by calling manager class */
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			// set roleIdList in request
			request.setAttribute("roleIdList", roleList);

			request.setAttribute("currencyGroupList", new ArrayList());

			session = request.getSession();
			objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			return getView("success");
		} catch (SwtException swtexp) {

			if(!(swtexp.getMessage().equals("errors.password.alphaChars")
					|| swtexp.getMessage().equals("errors.password.mixedCase")
					|| swtexp.getMessage().equals("errors.password.numbers")
					|| swtexp.getMessage().equals("errors.password.specialChar")
					||	swtexp.getMessage().equals("errors.password.minLength")
					||	swtexp.getMessage().equals("errors.password.maxLength")
					||	swtexp.getMessage().equals("errors.password.inHistory")))

				log.error(this.getClass().getName()
						+ " - Exception Catched in [save] method : - "
						+ swtexp.getMessage());

			// create instance of systemInfo
			systemInfo = new SystemInfo();
			// set methodName in request
			request.setAttribute("methodName", "save");
// To remove: 			// get DynaValidatorForm from form
// To remove: 			dyForm = (DynaValidatorForm) form;
			userMaintenance = (UserMaintenance) getUsermaintenance();
			// getting SectionManager bean
			sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));
			// get the sectionlist from sectionManager
			sectionList = sectionManager.getSectionListAsLebelBean(hostId);
			// set the sectionlist in request
			request.setAttribute("sectiondetails", sectionList);
			// create CacheManager instance
			cacheManagerInst = CacheManager.getInstance();
			languageList = (ArrayList) cacheManagerInst.getMiscParamsLVL(
					"LANG", userMaintenance.getCurrententity());
			// set the languagelist in request
			request.setAttribute("languagedetails", languageList);
			// get roleId from userMaintenance
			roleId = userMaintenance.getRoleId();
			// get roleId from usermaintenanceManager
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			// set the roleEntityList in request
			request.setAttribute("roleEntityList", roleEntity);

			putCurrencyGroupDetailsinRequest(request, roleId, userMaintenance
					.getCurrententity());
			// get the currentUserRoleId from SwtUtil
			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			// get currentRoleEntityAccessList
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			// Loop to iterate the currentEntityItr
			currentEntityItr = currentRoleEntityAccessList.iterator();
			currentUserEntityIds = "";
			while (currentEntityItr.hasNext()) {
				LabelValueBean role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			// get the rolelist usermaintenanceManager
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			// set the roleIdList in request
			request.setAttribute("roleIdList", roleList);
			request.setAttribute("screenFieldsStatus", "");
			// get the current userId
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// set the userId in userMaintenance
			userMaintenance.setUpdateUser(userId);
			// get the hostId from userMaintenance
			userMaintenance.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			// set ipAddress in systemInfo
			systemInfo.setIpAddress(request.getRemoteAddr());

			SwtUtil.logErrorInDatabase(swtexp);
			// check the condition for ErrorLogGile is not equal to null
			if (errors != null) {
				if (swtexp.getErrorLogFlag() != null
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("Y")
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("N"))
					errors.add("entityId", new ActionMessage(swtexp.getErrorCode(),
							swtexp.getErrorLogFlag()));
				else
					errors
							.add("entityId", new ActionMessage(swtexp
									.getErrorCode()));
			}
			saveErrors(request, errors);
			// get the session
			session = request.getSession();
			// create objUser
			objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			// set the advancedUser in request
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			return getView("add");
		} catch (Exception e) {


			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "save",
							UserMaintenanceAction.class), request, ""));
			return getView("fail");
		} finally {
			log.debug(this.getClass().getName() + "- [save] - Exit ");
			errors = null;
// To remove: 			 dyForm = null;
			userMaintenance = null;
			systemInfo = null;
			pwdHist = null;
			sectionManager = null;
			cacheManagerInst = null;
			session = null;
			objUser = null;
			roleId = null;
			userId = null;
			userIdInSession = null;
			hostId = null;
			pwd = null;
			sectionList = null;
			languageList = null;
			roleEntity = null;
			roleList = null;
			currentRoleEntityAccessList = null;
			currentEntityItr = null;
			currentUserEntityIds = "";
			currentUserRoleId = null;
		}

	}
	/*End:Code modified by Alibasha for Mantis 1608 for serverlog issues */
	/**
	 * Method to generate User Reports when user click on Print button
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String report()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		String userId;
		ServletOutputStream out;
		JasperPrint jasperPrint;
		JRPdfExporter pdfExporter;
		String fileName = null;

		String fileType;
		JRXlsExporter xlsxporter;
		String sheetName[] = new String[1];

		try {
			log.debug(this.getClass().getName() + " - [ report ]- " + "Entry");
			userId = request.getParameter("selectedUserCodeId");
			fileType = request.getParameter("fileType");
			out = response.getOutputStream();

			request.setAttribute("fileType", fileType);

			jasperPrint = usermaintenanceManager.getUserReport(request, userId);

			if (!"".equals(userId))
				fileName = "UserReport-SmartPredict_" + userId + "_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss");
			else
				fileName = "UserReport-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss");

			/* Condition to check fileType is pdf */
			if (fileType.equals("pdf")) {
				pdfExporter = new JRPdfExporter();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + ".pdf");

				pdfExporter.setParameter(JRExporterParameter.JASPER_PRINT,
						jasperPrint);

				// Providing the output stream
				pdfExporter
						.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				pdfExporter.setParameter(
						JRExporterParameter.CHARACTER_ENCODING, "UTF-8");

				// Export Report to PDF
				pdfExporter.exportReport();

				/* Condition to check fileType is xls */
			} else if (fileType.equals("excel")) {
				xlsxporter = new JRXlsExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + ".xls");

				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT,
						jasperPrint);

				// Providing the output stream
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
						"UTF-8");
				// xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,Boolean.TRUE);
				xlsxporter
						.setParameter(
								JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS,
								Boolean.TRUE);
				xlsxporter
						.setParameter(
								JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS,
								Boolean.TRUE);
				//modified for jasper jar  upgrade-Mantis 1648
				xlsxporter.setParameter(
						JRXlsExporterParameter.IS_DETECT_CELL_TYPE,
						Boolean.TRUE);
				/* Condition to check userId is not empty */
				if (!"".equals(userId)) {
					/* Set the sheet name as userId */
					sheetName[0] = userId;
					xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES,
							sheetName);
				} else {
					/* Set the sheet name as UserReport */
					sheetName[0] = "UserReport";
					xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES,
							sheetName);
				}
				// Export Report to Excel
				xlsxporter.exportReport();
			}

		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [report] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", UserMaintenanceAction.class), request, "");
		}
		log.debug(this.getClass().getName() + " - [ report ]- " + "Exit");
		return null;
	}

	/**
	 * @return
	 * @throws Exception
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log
				.debug("Entering UserMaintenanceAction.displayCcyGrpListByEntity method");
		ActionErrors errors = new ActionErrors();
		SystemInfo systemInfo = new SystemInfo();
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		UserMaintenance usermaintenance = null;
		String userid = request.getParameter("selectedUserCode");
		String hostId = CacheManager.getInstance().getHostId();
		try {
			usermaintenance = (UserMaintenance) getUsermaintenance();

			String userIdInSession = SwtUtil.getCurrentUserId(request
					.getSession());
			userid = userid != null && userid.trim().length() > 0 ? userid : "";
			if (userid.equals(userIdInSession)) {
				request.setAttribute("killStatus", "true");
			} else {
				usermaintenance.getId().setHostId(hostId);
				usermaintenance.getId().setUserId(userid);
				usermaintenance.setUpdateUser(userIdInSession);
				usermaintenance.getId().setHostId(
						SwtUtil.getCurrentHostId(request.getSession()));
				systemInfo.setIpAddress(request.getRemoteAddr());
				usermaintenance = usermaintenanceManager.fetchUserDetail(
						hostId, userid);
				PasswordHistory pwdhist = new PasswordHistory();
				String userId = usermaintenance.getId().getUserId();
				pwdhist.getId().setHostId(hostId);
				pwdhist.getId().setUserId(userId);
				SessionManager sessionManager = SessionManager.getInstance();
				sessionManager.killSession(hostId, userId, "SYSTEM");
				usermaintenanceManager.deleteUserDetail(usermaintenance,
						systemInfo, SwtUtil.getCurrentSystemFormats(request
								.getSession()), pwdhist);
			}

			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			setUsermaintenance(usermaintenance);
			display();
			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'delete' method : "
							+ swtexp.getMessage());
			usermaintenance = usermaintenanceManager.fetchUserDetail(hostId,
					userid);
			usermaintenance.getId().setHostId(hostId);
			usermaintenance.getId().setUserId(userid);
			request.setAttribute("screenFieldsStatus", "");
			SwtUtil.logErrorInDatabase(swtexp);
			putUserInRequest(request);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			return getView("success");
		} catch (Exception e) {
			log
					.error("Exception Catch in UserMaintenanceAction.'delete' method : "
							+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "delete",
							UserMaintenanceAction.class), request, ""));
			return getView("fail");
		}
	}

	/**
	 * This method is called on the click of 'Change' button of User maintenance
	 * screen.
	 *
	 * @return ActionForward object
	 * @throws Exception
	 * <AUTHOR> and modified by Mayank Tripathi on 10/04/2007
	 */
	public String change()
			throws SwtException {
		Date udate = null;
		Date logindate = null;
		Date logoutdate = null;
		String extAuthId = null;
		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;
		try {
			request.setAttribute("screenFieldsStatus", "false");

			log.debug("Entering the change method");
			SystemInfo systemInfo = new SystemInfo();
			updatedValue = "y";
			String userid = request.getParameter("selectedUserCode");
			request.setAttribute("userid", userid);
			String hostId = CacheManager.getInstance().getHostId();

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			UserMaintenance usermaintenance = (UserMaintenance) getUsermaintenance();
			usermaintenance.getId().setHostId(hostId);
			usermaintenance.getId().setUserId(userid);
			usermaintenance.setStatus("1");
			usermaintenance = usermaintenanceManager.fetchUserDetail(hostId,
					userid);

			SectionManager sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));
			Collection sectionlist = sectionManager
					.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionlist);
			CacheManager cacheManagerInst = CacheManager.getInstance();
			Collection languagelist = (ArrayList) cacheManagerInst
					.getMiscParamsLVL("LANG", usermaintenance
							.getCurrententity());
			request.setAttribute("languagedetails", languagelist);
			String roleId = usermaintenance.getRoleId();
			List roleEntity = (List) usermaintenanceManager
					.getRoleEntityAccess(roleId);
			if (usermaintenance.getCurrententity() == null) {
				roleEntity.add(0, new LabelValueBean("", ""));
			}
			request.setAttribute("roleEntityList", roleEntity);
			List currrencyGroupList = new ArrayList();
			HttpSession session = request.getSession();
			currrencyGroupList = (List) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupFullORViewAcessLVL(roleId,
							usermaintenance.getCurrententity());

			if (usermaintenance.getCurrentCcyGrpId() == null) {
				currrencyGroupList.add(0, new LabelValueBean("", ""));

			} else {
				if (currrencyGroupList != null && currrencyGroupList.size() > 0) {
					currrencyGroupList.add(0, new LabelValueBean(
							SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
				}
			}

			request.setAttribute("currencyGroupList", currrencyGroupList);

			String currentUserRoleId = SwtUtil.getCurrentUser(
					request.getSession()).getRoleId();
			Collection currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			Iterator currentEntityItr = currentRoleEntityAccessList.iterator();
			String currentUserEntityIds = "";
			while (currentEntityItr.hasNext()) {
				LabelValueBean role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			Collection roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);

			udate = usermaintenance.getPasswordchangedate();
			SystemFormats systemFormats = SwtUtil
					.getCurrentSystemFormats(request.getSession());
			if (udate != null) {
				String uDate = SwtUtil.formatDate(udate, systemFormats
						.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				uDate = uDate + " " + sdf.format(udate);
				request.setAttribute("uDate", uDate);
			}

			logindate = usermaintenance.getLastlogin();
			if (logindate != null) {
				String loginDate = SwtUtil.formatDate(logindate, systemFormats
						.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				loginDate = loginDate + " " + sdf.format(logindate);
				request.setAttribute("loginDate", loginDate);
			}
			logoutdate = usermaintenance.getLastlogout();
			if (logoutdate != null) {
				String logoutDate = SwtUtil.formatDate(logoutdate,
						systemFormats.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				logoutDate = logoutDate + " " + sdf.format(logoutdate);
				request.setAttribute("logoutDate", logoutDate);
			}

			//Mantis 5709 Start
			extAuthId = usermaintenance.getExtAuthId();
			if (extAuthId != null) {
				request.setAttribute("extAuthId", extAuthId);
			}


			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			User oldUserDetails = logon.getUserDetail(hostId, userid);

			if(oldUserDetails != null) {

				lastFailedIP = oldUserDetails.getLastLoginFailedIp();
				if(lastFailedIP != null)
				{
					request.setAttribute("lastFailedIP" , lastFailedIP);
				}
				lastLoginIP = oldUserDetails.getLastLoginIp();
				if(lastLoginIP != null)
				{
					request.setAttribute("lastLoginIP" , lastLoginIP);
				}

				lastFailedndate = oldUserDetails.getLastLoginFailed();

				if(lastFailedndate != null)
				{
					String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
					lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
					request.setAttribute("lastFaileddate" , lastFailedndateDate);
				}
			}

			//Mantis 5709 End

			String oldVal = new StringBuffer("USER-NAME=").append(
							usermaintenance.getUsername()).append("^SECTION-ID=")
					.append(usermaintenance.getSectionid()).append("^LANG=")
					.append(usermaintenance.getLanguage()).append(
							"^PHONE-NUMBER=").append(
							usermaintenance.getPhonenumber()).append(
							"^EMAIL-ADDRESS=").append(
							usermaintenance.getEmailId()).append("^ROLE-ID=")
					.append(usermaintenance.getRoleId()).append("^PWD=")
					.append(usermaintenance.getPassword()).append(
							"^CURRENT-ENTITY=").append(
							usermaintenance.getCurrententity()).append(
							"^STATUS=").append(usermaintenance.getStatus())
					.toString();

			request.setAttribute("roleIdList", roleList);
			request.setAttribute("methodName", "update");
			request.setAttribute("oldVal", oldVal);

			usermaintenance.getId().setUserId(userid);
			usermaintenance.setNewPassword(dummyPass);

			usermaintenance.setConfirmPassword(dummyPass);

			setUsermaintenance(usermaintenance);
			request.setAttribute("disableRoleId", "disableRoleId");
			log.debug("exiting 'change' method");

			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			if (strAdvancedUserFlag.equalsIgnoreCase(SwtConstants.NO)) {
				return getView("adduser");
			}

			if(SwtUtil.isEmptyOrNull(usermaintenance.getAmountDelimiter())||SwtUtil.isEmptyOrNull(usermaintenance.getDateFormat())) {
				// Getting system parameter details from manager class
				SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
				SysParams sysParams = sysParamsManager.getSysParamsDetail(hostId, SwtUtil.getCurrentSystemFormats(request.getSession()));
				String amountDelimiter =sysParams.getAmountDelimiter();
				String dateformat=sysParams.getDateFormat();

				if(SwtUtil.isEmptyOrNull(usermaintenance.getAmountDelimiter())) {
					usermaintenance.setAmountDelimiter(amountDelimiter);
				}

				if(SwtUtil.isEmptyOrNull(usermaintenance.getDateFormat())) {
					usermaintenance.setDateFormat(dateformat);
				}
			}

			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'change' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception Catch");
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "change", UserMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}
	/*Start:Code Modified by Alibasha for Mantis 1608 */
	/**
	 * This method is called on when user details is saved in change user Screen
	 *
	 * @return
	 * @throws Exception
	 */
	public String update()
			throws SwtException {
		// variable to hold errors object
		ActionErrors errors = null;
		// Variable to hold dyForm object
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold usermaintenance object
		UserMaintenance userMaintenance = null;
		// String variable to userIdInSession
		String userIdInSession = null;
		// Variable to hold systemInfo object
		SystemInfo systemInfo = null;
		// String variable to hostId
		String hostId = null;
		// String variable to passwordchanged
		String passwordChanged = null;
		// String variable to changePwdFlag
		String changePwdFlag = null;
		// String variable to roleId
		String roleId = null;
		// Collection variable to roleEntity
		Collection roleEntity = null;
		// String variable to currentUserRoleId
		String currentUserRoleId = null;
		// Collection variable to currentRoleEntityAccessList
		Collection currentRoleEntityAccessList = null;
		// Collection variable to languagelist
		Collection languageList = null;
		// Variable to hold currentEntityItr object
		Iterator currentEntityItr = null;
		// Variable to hold role
		LabelValueBean role = null;
		// String variable to currentUserEntityIds
		String currentUserEntityIds = null;
		// Collection variable to roleList
		Collection roleList = null;
		// String variable to pwd
		String userPassword = null;
		// Variable to hold pwdhist object
		PasswordHistory pwdHist = null;
		// Variable to hold user object
		User user = null;
		// String variable to userId
		String userId = null;
		// String variable to oldValue
		String oldValue = null;
		// String variable to newVal
		String newVal = null;
		// Variable to hold httpSession object
		HttpSession httpSession = null;
		// Variable to hold cdm object
		CommonDataManager commonDataManager = null;
		// Variable to hold changeUser object
		User changeUser = null;
		// String variable to cureentRoleId
		String cureentRoleId = null;
		// String variable to newRoleId
		String newRoleId = null;
		// String variable to message
		String message = null;
		// Variable to hold session object
		HttpSession session = null;
		// Variable to hold objUser object
		User objUser = null;
		// Collection variable to sectionlist
		Collection sectionList = null;
		// Variable to hold cacheManagerInst object
		CacheManager cacheManagerInst = null;
		// List variable to lastLoginInfo
		List lastLoginInfo = null;
		// Variable to hold logindate object
		Date loginDate = null;
		// Variable to hold logoutdate object
		Date logoutDate = null;
		// Variable to hold extAuthId object
		String extAuthId = null;
		// Variable to hold usrSts object
		UserStatus userStatus = null;
		// Variable to hold lastLoginInfoItr object
		Iterator lastLoginInfoItr = null;
		// Variable to hold udate object
		Date lastChangePasswordDate = null;
		// Variable to hold systemFormats object
		SystemFormats systemFormats = null;
		// String variable to loginDateStr
		String loginDateStr = null;
		// Variable to hold simpleDateformat object
		SimpleDateFormat sdfLoginInfo = null;
		// String variable to lastChangePasswordDateStr
		String lastChangePasswordDateStr = null;
		// String variable to logoutDateStr
		String logoutDateStr = null;
		// List variable to pwdChangeDate
		List pwdChangeDate = null;
		// Variable to hold pwdChangeDateItr object
		Iterator pwdChangeDateItr = null;
		// Variable to hold loginInfoListSize
		int loginInfoListSize = 0;
		// Variable to hold pwdChangedateSize
		int pwdChangeDateSize = 0;
		// code added by sudhakar on 7-11-2011 for Mantis 1569:issues found on
		// 1053_STL_94 stl testing
		// Variable to hold userMaintenance_tableData object
		UserMaintenance userMaintenanceStatus = null;

		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;

		String decryptednewPassword = null;
		String decryptedConfirmPassword = null;

		try {
			log.debug(this.getClass().getName() + "- [update] - Entering ");
			// creating the errors object
			errors = new ActionErrors();
// To remove: 			dyForm = (DynaValidatorForm) form;
			currentUserEntityIds = "";
			// getting usermaintenance from the dyForm
			userMaintenance = (UserMaintenance) getUsermaintenance();
			// assign value to updatedValue
			updatedValue = "y";
			// getting CurrentUserId from session
			userIdInSession = SwtUtil.getCurrentUserId(request.getSession());
			// creating the systemInfo object
			systemInfo = new SystemInfo();
			systemInfo.setIpAddress(request.getRemoteAddr());
			// gettin hostid from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// setting hostid to usermaintenance
			userMaintenance.getId().setHostId(hostId);
			// setting parentFormRefresh in request
			request.setAttribute("parentFormRefresh", "yes");
			// setting sectiondetails in request
			request.setAttribute("sectiondetails", new ArrayList());
			// BEGIN: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
			// Added by Saber Chebka: Keep the key creation out of radar.. (TODO: add obfuscation for this class to prevent reverse engineering)
			String pass = request.getSession().getId().substring(0, request.getSession().getId().length() > 12 ? 12 : request.getSession().getId().length());
			pass+=userMaintenance.getId().getUserId().substring(0,userMaintenance.getId().getUserId().length()>4?4:userMaintenance.getId().getUserId().length());
//			pass = SwtUtil.hash(pass);

			userMaintenance.setPassword(usermaintenanceManager.fetchUserDetail(hostId, userMaintenance.getId().getUserId()).getPassword());

			String encryptedNewPwd=request.getParameter("encnewpasswd");
			String encryptedConfirmPwd=request.getParameter("encconfirmpasswd");
			try {
				decryptednewPassword = SwtUtil.decryptCBC(pass, encryptedNewPwd);
				decryptedConfirmPassword = SwtUtil.decryptCBC(pass, encryptedConfirmPwd);
			} catch (Exception e) {
				throw new RuntimeException("Error during password decryption", e);
			}
			if (!decryptedConfirmPassword.equals(dummyPass))
				userMaintenance.setConfirmPassword(decryptedConfirmPassword);

			if (!decryptednewPassword.equals(dummyPass))
				userMaintenance.setNewPassword(decryptednewPassword);
			// END: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...

			passwordChanged = userMaintenance.getNewPassword();
			changePwdFlag = SwtConstants.NO;

			if (!passwordChanged.equals(dummyPass)) {
				changePwdFlag = SwtConstants.YES;
				userMaintenance.setPassword(passwordChanged);
			}
			// setting languagedetails in request
			request.setAttribute("languagedetails", new ArrayList());

			roleId = userMaintenance.getRoleId();
			// getting roleEntity from usermaintenanceManager
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			// setting roleEntityList in request
			request.setAttribute("roleEntityList", roleEntity);
			// getting currentUserRoleId from session
			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			// getting currentRoleEntityAccessList from usermaintenanceManager
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			currentEntityItr = currentRoleEntityAccessList.iterator();
			// Loop to iterate currentEntityItr
			while (currentEntityItr.hasNext()) {
				role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			// getting roleList from usermaintenanceManager
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			// setting roleIdList in request
			request.setAttribute("roleIdList", roleList);

			userPassword = userMaintenance.getPassword();
			// creating the pwdhist object
			pwdHist = new PasswordHistory();
			// creating the user object
			user = new User();
			userId = userMaintenance.getId().getUserId();
			// setting hostid to pwdhist
			pwdHist.getId().setHostId(hostId);
			// setting userId to pwdhist
			pwdHist.getId().setUserId(userId);
			// setting pwd to pwdhist
			pwdHist.setPassword(userPassword);

			// setting hostId to user
			user.getId().setHostId(hostId);
			// setting userId to user
			user.getId().setUserId(userId);
			// setting UserName to user
			user.setUserName(userMaintenance.getUsername());
			// setting SectionId to user
			user.setSectionId(userMaintenance.getSectionid());
			// setting Language to user
			user.setLanguage(userMaintenance.getLanguage());
			// setting PhoneNumber to user
			user.setPhoneNumber(userMaintenance.getPhonenumber());
			// setting EmailId to user
			user.setEmailId(userMaintenance.getEmailId());
			// setting RoleId to user
			user.setRoleId(userMaintenance.getRoleId());
			// setting Password to user
			user.setPassword(userMaintenance.getPassword());
			// setting currentEntity to user
			user.setCurrentEntity(userMaintenance.getCurrententity());
			// setting Status to user
			user.setStatus(userMaintenance.getStatus());
			user.setManualChange(true);
			// setting InvPassAttempt to user
			user.setInvPassAttempt(userMaintenance.getInvPassAttempt());
			// setting CurrentCcyGrpId to user
			user.setCurrentCcyGrpId(userMaintenance.getCurrentCcyGrpId());
			// setting UpdateUser to user
			user.setUpdateUser(UserThreadLocalHolder.getUser());
			user.setAmountDelimiter(userMaintenance.getAmountDelimiter());
			user.setDateFormat(userMaintenance.getDateFormat());
			userMaintenanceStatus = usermaintenanceManager.fetchUserDetail(
					hostId, userId);
			// Getting last Logout date
			logoutDate = userMaintenanceStatus.getLastlogout();
			// Getting Last Login date
			loginDate = userMaintenanceStatus.getLastlogin();
			// Getting Last password change date
			lastChangePasswordDate = userMaintenanceStatus.getPasswordchangedate();

			// Mantis 5709
			extAuthId = request.getParameter("extAuthId");

			// Getting current systemFormats
			systemFormats = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			// Check the condition logindate date is not equal to null
			if (loginDate != null) {
				loginDateStr = SwtUtil.formatDate(loginDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				loginDateStr = loginDateStr + " "
						+ sdfLoginInfo.format(loginDate);
				/*
				 * Start:code added by sudhakar on 7-11-2011 for Mantis
				 * 1569:issues found on 1053_STL_94 stl testing
				 */
				// Checking the enableStatusFlag is true and user status
				if (userMaintenance.getStatus().equals(
						SwtConstants.USER_ENABLE_STATUS)
						&& userMaintenanceStatus.getStatus().equals(
						SwtConstants.USER_DISABLE_STATUS)) {
					// Assign systemparameter time in logindate
					loginDate = SwtUtil.getSysParamDate();
					user.setInvPassAttempt(Integer.valueOf(0));
				}else if(userMaintenance.getStatus().equals(
						SwtConstants.USER_DISABLE_STATUS)
						&& userMaintenanceStatus.getStatus().equals(
						SwtConstants.USER_ENABLE_STATUS)) {
					user.setInvPassAttempt(Integer.valueOf(0));
				}
				/*
				 * End:code added by sudhakar on 7-11-2011 for Mantis
				 * 1569:issues found on 1053_STL_94 stl testing
				 */
				// setting LastLogin to user
				user.setLastLogin(loginDate);
				request.setAttribute("loginDate", loginDateStr);

			}
			// Check the condition passwordchange date is not equal to null
			if (lastChangePasswordDate != null) {
				lastChangePasswordDateStr = SwtUtil.formatDate(
						lastChangePasswordDate, systemFormats
								.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				lastChangePasswordDateStr = lastChangePasswordDateStr + " "
						+ sdfLoginInfo.format(lastChangePasswordDate);
				// Setting PasswordChangeDate to user
				user.setPasswordChangeDate(lastChangePasswordDate);
				// Setting uDate in request
				request.setAttribute("uDate", lastChangePasswordDateStr);
			}
			// Check the condition logoutdate date is not equal to null
			if (logoutDate != null) {
				logoutDateStr = SwtUtil.formatDate(logoutDate, systemFormats
						.getDateFormatValue());
				sdfLoginInfo = new SimpleDateFormat("HH:mm:ss");
				logoutDateStr = logoutDateStr + " "
						+ sdfLoginInfo.format(logoutDate);
				// Setting LastLogout to user
				user.setLastLogout(logoutDate);
				// Setting logoutDate in request
				request.setAttribute("logoutDate", logoutDateStr);
			}

			//Mantis 5709
			if (extAuthId != null) {
				user.setExtAuthId(extAuthId);
				// Setting extAuthId in request
				request.setAttribute("extAuthId", extAuthId);
			}

			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			User oldUserDetails = logon.getUserDetail(hostId, userId);

			if(oldUserDetails != null) {

				lastFailedIP = oldUserDetails.getLastLoginFailedIp();
				if(lastFailedIP != null)
				{
					request.setAttribute("lastFailedIP" , lastFailedIP);
				}
				lastLoginIP = oldUserDetails.getLastLoginIp();
				if(lastLoginIP != null)
				{
					request.setAttribute("lastLoginIP" , lastLoginIP);
				}

				lastFailedndate = oldUserDetails.getLastLoginFailed();

				if(lastFailedndate != null)
				{
					String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
					SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
					lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
					request.setAttribute("lastFaileddate" , lastFailedndateDate);
				}
			}

			/* End:code added by sudhakar on 13-9-2011 for Mantis 1569 */
			// setting currencyGroupList in request
			request.setAttribute("currencyGroupList", new ArrayList());
			// setting usermaintenance in dyForm
			setUsermaintenance(userMaintenance);
			// getting currencyGroupList in request
			oldValue = request.getParameter("oldValue");
			// creating the newVal object
			newVal = new StringBuffer("USER-NAME=").append(
							userMaintenance.getUsername()).append("^SECTION-ID=")
					.append(userMaintenance.getSectionid()).append("^LANG=")
					.append(userMaintenance.getLanguage()).append(
							"^PHONE-NUMBER=").append(
							userMaintenance.getPhonenumber()).append(
							"^EMAIL-ADDRESS=").append(
							userMaintenance.getEmailId()).append("^ROLE-ID=")
					.append(userMaintenance.getRoleId()).append("^PWD=")
					.append(userMaintenance.getPassword()).append(
							"^CURRENT-ENTITY=").append(
							userMaintenance.getCurrententity()).append(
							"^STATUS=").append(userMaintenance.getStatus())
					.toString();
			// setting oldValue to systemInfo
			systemInfo.setOldLogString(oldValue);
			// setting newVal to systemInfo
			systemInfo.setNewLogString(newVal);
			if(SwtConstants.YES.equals(changePwdFlag)){
				user.setInvPassAttempt(Integer.valueOf(0));
			}
			usermaintenanceManager.updateUserDetail(userMaintenance, user,
					pwdHist, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()),
					changePwdFlag);

			updateCurrentUser(user, request);
			// setting methodName in request
			request.setAttribute("methodName", "update");
			// getting httpSession from SessionManager
			httpSession = (HttpSession) SessionManager.getInstance()
					.getUserSession(CacheManager.getInstance().getHostId(),
							user.getId().getUserId());

			if (httpSession != null) {
				// getting cdm from CommonDataManager
				commonDataManager = (CommonDataManager) httpSession
						.getAttribute(SwtConstants.CDM_BEAN);
				if (commonDataManager != null) {
					changeUser = commonDataManager.getUser();
					if (changeUser != null) {
						// Setting current entity
						changeUser.setCurrentEntity(user.getCurrentEntity());

						cureentRoleId = changeUser.getRoleId();
						newRoleId = userMaintenance.getRoleId();
						boolean isRoleChanged = false;

						if (cureentRoleId != null
								&& !cureentRoleId.equals(newRoleId))
							isRoleChanged = true;

						if (isRoleChanged) {
							message = "Your role has been changed. Please log off and logon again.";
							SessionManager.getInstance().sendMessage(
									httpSession.getId(), message);
						}

						//Setting currency format and date format
						systemFormats = new SystemFormats();
						systemFormats.setCurrencyFormat(SwtConstants.CURRENCY_PAT + userMaintenance.getAmountDelimiter());

						String dateFormat= null;
						if ((SwtConstants.DATE_PAT + userMaintenance.getDateFormat()).equals("datePat1"))
							dateFormat = "dd/MM/yyyy";
						else
							dateFormat = "MM/dd/yyyy";
						commonDataManager.setDateFormatValue(dateFormat);
						commonDataManager.setCurrencyFormatValue(userMaintenance.getAmountDelimiter());
						systemFormats.setDateFormatValue(dateFormat);
						commonDataManager.setSystemFormats(systemFormats);
						commonDataManager.setDateFormat(SwtConstants.DATE_PAT + userMaintenance.getDateFormat());
						changeUser.setDateFormat(userMaintenance.getDateFormat());
						changeUser.setAmountDelimiter(userMaintenance.getAmountDelimiter());
					}
				}
			}
			session = request.getSession();
			// getting objUser from CommonDataManager
			objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			// getting strAdvancedUserFlag from usermaintenanceManager
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			// setting advancedUser in request
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			/*
			 * If the user doesn't have the advanced user rights then he will be
			 * routed to another jsp where he can view only partial details of
			 * the user selected.
			 */
			if (strAdvancedUserFlag.equalsIgnoreCase(SwtConstants.NO)) {
				return getView("adduser");
			}
			log.debug(this.getClass().getName() + "- [update] - Exit ");
			return getView("add");
		} catch (SwtException swtexp) {
			if(!(swtexp.getMessage().equals("errors.password.alphaChars")|| swtexp.getMessage().equals("errors.password.mixedCase")
					|| swtexp.getMessage().equals("errors.password.numbers")|| swtexp.getMessage().equals("errors.password.specialChar") ||
					swtexp.getMessage().equals("errors.password.minLength") ||
					swtexp.getMessage().equals("errors.password.maxLength") || swtexp.getMessage().equals("errors.password.inHistory")))

				log.error(this.getClass().getName() + "- [Update] - Exception "
						+ swtexp.getMessage());

			request.setAttribute("parentFormRefresh", "");
			request.setAttribute("methodName", "update");
			// getting sectionManager from sectionManager bean
			SectionManager sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));

			sectionList = sectionManager.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionList);

			cacheManagerInst = CacheManager.getInstance();
			languageList = (ArrayList) cacheManagerInst.getMiscParamsLVL(
					"LANG", userMaintenance.getCurrententity());
			request.setAttribute("languagedetails", languageList);

			roleId = userMaintenance.getRoleId();
			roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
			// setting roleEntityList in request
			request.setAttribute("roleEntityList", roleEntity);

			putCurrencyGroupDetailsinRequest(request, roleId, userMaintenance
					.getCurrententity());
			// getting currentUserRoleId from session
			currentUserRoleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			// getting currentRoleEntityAccessList from usermaintenanceManager
			currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			currentEntityItr = currentRoleEntityAccessList.iterator();
			currentUserEntityIds = "";
			// Loop to iterate currentEntityItr
			while (currentEntityItr.hasNext()) {
				role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			// getting roleList from usermaintenanceManager
			roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);
			request.setAttribute("roleIdList", roleList);

			SwtUtil.logErrorInDatabase(swtexp);

			if (errors != null) {
				errors.add("entityId",
						new ActionMessage("NEIL :: Update method, error code: "
								+ swtexp.getMessage()));
				if (swtexp.getErrorLogFlag() != null
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("Y")
						&& !swtexp.getErrorLogFlag().equalsIgnoreCase("N"))
					errors.add("entityId", new ActionMessage(swtexp.getErrorCode(),
							swtexp.getErrorLogFlag()));
				else
					errors
							.add("entityId", new ActionMessage(swtexp
									.getErrorCode()));
			}

			saveErrors(request, errors);

			// Code added to retrieve, whether the user has the Advanced user
			// rights or not from Database.
			session = request.getSession();
			objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			/*
			 * If the user doesn't have the advanced user rights then he will be
			 * routed to another jsp where he can view only partial details of
			 * the user selected.
			 */
			if (strAdvancedUserFlag.equalsIgnoreCase(SwtConstants.NO)) {
				return getView("adduser");
			}

			return getView("add");
		} finally {
			userMaintenance = null;
			userIdInSession = null;
			systemInfo = null;
			hostId = null;
			passwordChanged = null;
			changePwdFlag = null;
			roleId = null;
			roleEntity = null;
			currentUserRoleId = null;
			currentRoleEntityAccessList = null;
			languageList = null;
			currentEntityItr = null;
			role = null;
			roleList = null;
			userPassword = null;
			pwdHist = null;
			user = null;
			userId = null;
			oldValue = null;
			newVal = null;
			commonDataManager = null;
			changeUser = null;
			cureentRoleId = null;
			newRoleId = null;
			message = null;
			objUser = null;
			sectionList = null;
			cacheManagerInst = null;
			lastLoginInfo = null;
			loginDate = null;
			logoutDate = null;
			extAuthId = null;
			userStatus = null;
			lastLoginInfoItr = null;
			lastChangePasswordDate = null;
			systemFormats = null;
			loginDateStr = null;
			sdfLoginInfo = null;
			lastChangePasswordDateStr = null;
			logoutDateStr = null;
			currentUserEntityIds = null;
			userMaintenanceStatus = null;
		}
	}
	/*End:Code Modified by Alibasha for Mantis 1608 */

	/**
	 * @return
	 * @throws Exception
	 */
	public String view()
			throws SwtException {
		Date logindate = null;
		Date logoutdate = null;
		Date udate = null;
		String extAuthId = null;
		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;
		User oldUserDetails = null;

		try {
			SystemInfo systemInfo = new SystemInfo();

			String userid = request.getParameter("selectedUserCode");
			String hostId = CacheManager.getInstance().getHostId();

			request.setAttribute("screenFieldsStatus", "true");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			UserMaintenance usermaintenance1 = (UserMaintenance) getUsermaintenance();
			usermaintenance1.getId().setHostId(hostId);
			usermaintenance1.getId().setUserId(userid);

			UserMaintenance usermaintenance = usermaintenanceManager
					.fetchUserDetail(hostId, userid);
			usermaintenance.setNewPassword(dummyPass);
			usermaintenance.setConfirmPassword(dummyPass);

			LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
			oldUserDetails = logon.getUserDetail(hostId, userid);

			setUsermaintenance(usermaintenance);

			SectionManager sectionManager = (SectionManager) (SwtUtil
					.getBean("sectionManager"));

			Collection sectionlist = sectionManager
					.getSectionListAsLebelBean(hostId);
			request.setAttribute("sectiondetails", sectionlist);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			Collection languagelist = (ArrayList) cacheManagerInst
					.getMiscParamsLVL("LANG", usermaintenance
							.getCurrententity());
			request.setAttribute("languagedetails", languagelist);

			String currentUserRoleId = SwtUtil.getCurrentUser(
					request.getSession()).getRoleId();
			Collection currentRoleEntityAccessList = usermaintenanceManager
					.getRoleEntityAccess(currentUserRoleId);
			Iterator currentEntityItr = currentRoleEntityAccessList.iterator();
			String currentUserEntityIds = "";
			while (currentEntityItr.hasNext()) {
				LabelValueBean role = (LabelValueBean) currentEntityItr.next();
				if (currentUserEntityIds.equals("")) {
					currentUserEntityIds = currentUserEntityIds + "'"
							+ role.getValue() + "'";
				} else {
					currentUserEntityIds = currentUserEntityIds + " , '"
							+ role.getValue() + "'";
				}
			}
			Collection roleList = usermaintenanceManager.getRoleList(hostId,
					currentUserEntityIds);

			request.setAttribute("roleIdList", roleList);

			String roleId = usermaintenance.getRoleId();

			List roleEntity = (List) usermaintenanceManager
					.getRoleEntityAccess(roleId);
			if (usermaintenance.getCurrententity() == null) {
				roleEntity.add(0, new LabelValueBean("", ""));
			}
			request.setAttribute("roleEntityList", roleEntity);

			String entityId = usermaintenance.getCurrententity();
			List currrencyGroupList = new ArrayList();
			HttpSession session = request.getSession();

			currrencyGroupList = (List) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupFullORViewAcessLVL(roleId,
							usermaintenance.getCurrententity());
			if (usermaintenance.getCurrentCcyGrpId() == null) {
				currrencyGroupList.add(0, new LabelValueBean("", ""));
				currrencyGroupList.add(1, new LabelValueBean(
						SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			} else {
				currrencyGroupList.add(0, new LabelValueBean(
						SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			}
			request.setAttribute("currencyGroupList", currrencyGroupList);

			udate = usermaintenance.getPasswordchangedate();
			SystemFormats systemFormats = SwtUtil
					.getCurrentSystemFormats(request.getSession());
			if (udate != null) {
				String uDate = SwtUtil.formatDate(udate, systemFormats
						.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				uDate = uDate + " " + sdf.format(udate);

				request.setAttribute("uDate", uDate);
			}

			logindate = usermaintenance.getLastlogin();
			if (logindate != null) {
				String loginDate = SwtUtil.formatDate(logindate, systemFormats
						.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				loginDate = loginDate + " " + sdf.format(logindate);

				request.setAttribute("loginDate", loginDate);

			}

			logoutdate = usermaintenance.getLastlogout();
			if (logoutdate != null) {
				String logoutDate = SwtUtil.formatDate(logoutdate,
						systemFormats.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				logoutDate = logoutDate + " " + sdf.format(logoutdate);

				request.setAttribute("logoutDate", logoutDate);

			}

			//Mantis 5709 Start
			extAuthId = usermaintenance.getExtAuthId();
			if (extAuthId != null) {
				request.setAttribute("extAuthId", extAuthId);
			}


			lastFailedIP = oldUserDetails.getLastLoginFailedIp();
			if(lastFailedIP != null)
			{
				request.setAttribute("lastFailedIP" , lastFailedIP);
			}
			lastLoginIP = oldUserDetails.getLastLoginIp();
			if(lastLoginIP != null)
			{
				request.setAttribute("lastLoginIP" , lastLoginIP);
			}

			lastFailedndate = oldUserDetails.getLastLoginFailed();

			if(lastFailedndate != null)
			{
				String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
				lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
				request.setAttribute("lastFaileddate" , lastFailedndateDate);
			}



			request.setAttribute("methodName", "view");

			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);

			if (strAdvancedUserFlag.equalsIgnoreCase(SwtConstants.NO)) {
				return getView("adduser");
			}

			return getView("add");
		} catch (SwtException swtexp) {

			log
					.error("SwtException Catch in UserMaintenanceAction.'view' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception Catch");
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "view", UserMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to update the current user in the session with the
	 * values that has been changes vai User Maintenance screeen.
	 *
	 * @param user
	 * @param request
	 * @throws SwtException
	 * <AUTHOR>
	 */
	private void updateCurrentUser(User user, HttpServletRequest request)
			throws SwtException {
		CommonDataManager cdm = ((CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN));
		User currentUser = cdm.getUser();
		if (user.getId().getUserId().equals(currentUser.getId().getUserId())) {
			// Repopulating current user with the one that has been saved into
			// DB
			currentUser.getId().setUserId(user.getId().getUserId());
			currentUser.setCurrentEntity(user.getCurrentEntity());
			currentUser.setCurrentCcyGrpId(user.getCurrentCcyGrpId());
			currentUser.setRoleId(user.getRoleId());
			currentUser.setUserName(user.getUserName());
			currentUser.setSectionId(user.getSectionId());
			currentUser.setLanguage(user.getLanguage());
			currentUser.setEmailId(user.getEmailId());
			currentUser.setPhoneNumber(user.getPhoneNumber());
			currentUser.setPassword(user.getPassword());
			currentUser.setPasswordChangeDate(user.getPasswordChangeDate());
			String bearerToken = TokensProvider.getInstance().createToken(currentUser, (request.getSession() != null?request.getSession().getId():null));
			cdm.setBearerToken(bearerToken);
			// Updating the CDM request object for further reference.
			cdm.setUser(currentUser);

			request.setAttribute(SwtConstants.CDM_BEAN, cdm);
		}else {

			HttpSession httpSession =
					(HttpSession) SessionManager.getInstance().getUserSession(CacheManager.getInstance().getHostId(),user.getId().getUserId());

			if(httpSession != null)
			{
				cdm = (CommonDataManager)httpSession.getAttribute(SwtConstants.CDM_BEAN);
				if(cdm != null)
				{
					String bearerToken = TokensProvider.getInstance().createToken(currentUser, (request.getSession() != null?httpSession.getId():null));
					cdm.setBearerToken(bearerToken);
				}
			}

		}

	}

	/*
	 * Code to fetch the advanced user rights for the user on 22-04-2008
	 */
	public String getAdvancedUserRights() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log
				.debug("UserMaintenanceAction : entering 'getAdvancedUserRights' method");

		try {
			HttpSession session = request.getSession();
			User objUser = (User) ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			strAdvancedUserFlag = usermaintenanceManager.getAdvancedUser(
					objUser.getId().getHostId(), objUser.getId().getUserId());
			request.setAttribute("advancedUser", strAdvancedUserFlag);
			if (strAdvancedUserFlag != null) {
				response.getWriter().print(strAdvancedUserFlag);
			}

			log
					.debug("UserMaintenanceAction : exiting 'getAdvancedUserRights' method");

			return null;
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in UserMaintenanceAction.'getAdvancedUserRights' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return null;
		} catch (Exception exp) {
			log
					.error("Exception Catch in UserMaintenanceAction.'getAdvancedUserRights' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getAdvancedUserRights", UserMaintenanceAction.class),
					request, "");

			return null;
		}
	}

}