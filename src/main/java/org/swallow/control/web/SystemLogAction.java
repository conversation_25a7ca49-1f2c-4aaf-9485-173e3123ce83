/*
 * Copyright (c) 2006-2015 SwallowTech, Inc.
 * 14 Lion Yard, Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 *
 * Developed by Perot Systems.
 */

/*
 * Created on Dec 16, 2005
 */
package org.swallow.control.web;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.context.ApplicationContext;
import org.swallow.control.model.SystemLog;
import org.swallow.control.service.SystemLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.PageDetails;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;

import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;

/**
 * <AUTHOR> This class is used for System Log Screen
 *
 * this class is used to Maintain the System Log details.
 */



import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/systemlog", "/systemlog.do"})
public class SystemLogAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/control/systemlog");
		viewMap.put("refresh", "jsp/control/systemlog");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();

	private SystemLog systemLog;
	public SystemLog getSystemLog() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		systemLog = RequestObjectMapper.getObjectFromRequest(SystemLog.class, request);
		return systemLog;
	}

	public void setSystemLog(SystemLog systemLog) {
		this.systemLog = systemLog;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("systemLog", systemLog);
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "defaultDetails":
				return defaultDetails();
			case "showDetails":
				return showDetails();
			case "next":
				return next();
			case "displayAngular":
				return displayAngular();
		}


		return unspecified();
	}




	/** The log variable is used for store the log object. */
	private final Log log = LogFactory.getLog(SystemLogAction.class);
	@Autowired
	private SystemLogManager systemLogManager = null;
	private ApplicationContext ctx = null;

	/**
	 *
	 * @param systemLogManager
	 */
	public void setSystemLogManager(SystemLogManager systemLogManager) {
		this.systemLogManager = systemLogManager;
	}

	/**
	 * @desc This method is called when the System Log screen is loaded for the
	 *       first time
	 */
	public String unspecified()
			throws SwtException {
		request.setAttribute("systemLogList", new ArrayList());
		log.debug("exiting 'unspecified' method");
		return defaultDetails();
	}

	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		return hostId;

	}

	/**
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private String putSystemDateInRequest(HttpServletRequest request)
			throws Exception {
		String systemDate = SwtUtil.getSystemDateString();
		return systemDate;
	}

	/**
	 * @desc This method fetches the System Log details for the Predict System
	 *       Date
	 * @return
	 * @throws Exception
	 */
	public String defaultDetails()
			throws SwtException {
		/* Variable Declaration for systemDate */
		String systemDate = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for pageSize */
		int pageSize = 0;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		try {
			log.debug("entering defaultDetails  method");

			systemDate = putSystemDateInRequest(request);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			SystemLog systemLog = (SystemLog) (getSystemLog());
			systemLog.setFromDateAsString(systemDate);
			systemLog.setToDateAsString(systemDate);

			currentFilter = request.getParameter("selectedFilter");
			currentSort = request.getParameter("selectedSort");
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			filterSortStatus = currentFilter + "," + currentSort;
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));

			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));
			String hostId = putHostIdListInReq(request);

			currentPage = 1;
			ArrayList sysLogList = new ArrayList();

			int maxPage = 0;
			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
							.getFromDate(), systemLog.getToDate(), currentPage - 1,
					initialPageCount, sysLogList, filterSortStatus, sysformat);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			PageDetails pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", systemDate);
			request.setAttribute("toDate", systemDate);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting 'defaultDetails' method");

			return getView("refresh");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SystemLogAction.'defaultDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SystemLogAction.'defaultDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "defaultDetails", SystemLogAction.class), request, "");
			return getView("fail");
		} finally {
			systemDate = null;
			filterSortStatus = null;
			currentFilter = null;
			currentSort = null;
			sysformat = null;
		}
	}

	/**
	 * @desc This method fetches the System Log details for all the dates
	 *       starting "From Date" till "To Date"
	 * @return
	 * @throws Exception
	 */
	public String showDetails()
			throws SwtException {
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for pageSize */
		int pageSize = 0;
		try {
			log.debug("entering 'showDetails' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			SystemLog systemLog = (SystemLog) (getSystemLog());
			fromDateAsString = systemLog.getFromDateAsString();

			toDateAsString = systemLog.getToDateAsString();

			/* gets all the filter values specified for all columns */
			currentFilter = request.getParameter("selectedFilter");
			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");

			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			filterSortStatus = currentFilter + "," + currentSort;

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));

			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));
			hostId = putHostIdListInReq(request);
			currentPage = 1;
			List sysLogList = new ArrayList();

			// START: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010
			int maxPage = 0;
			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
							.getFromDate(), systemLog.getToDate(), currentPage - 1,
					initialPageCount, sysLogList, filterSortStatus, sysformat);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			// END: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010

			PageDetails pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting 'showDetails' method");
			return getView("refresh");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in SystemLogAction.'showDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log
					.error("Exception Catch in SystemLogAction.'showDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "showDetails", SystemLogAction.class), request, "");
			return getView("fail");
		} finally {
			filterSortStatus = null;
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			hostId = null;
			sysformat = null;
		}
	} // End of showDetails method

	/**
	 * @desc This method is called when PREVIOUS LINK, NEXT LINK or any Page no
	 *       is clicked. It fetches the system Log details for that page.
	 * @return
	 * @throws Exception
	 */
	public String next()
			throws SwtException {
		/* Variable Declaration for systemLog */
		SystemLog systemLog = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		int currentPage = 0;
		int clickedPage = 0;
		int maxPage = 0;

		try {
			log.debug("entering next method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			systemLog = new SystemLog();
			fromDateAsString = request.getParameter("fromDate");

			toDateAsString = request.getParameter("toDate");
			systemLog.setFromDateAsString(fromDateAsString);
			systemLog.setToDateAsString(toDateAsString);
			SystemFormats sysformat = SwtUtil.getCurrentSystemFormats(request
					.getSession());

			systemLog.setFromDate(SwtUtil.parseDate(systemLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));
			systemLog.setToDate(SwtUtil.parseDate(
					systemLog.getToDateAsString(), sysformat
							.getDateFormatValue()));

			/* gets all the filter values specified for all columns */
			currentFilter = request.getParameter("selectedFilter");
			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "1|false";
			}
			filterSortStatus = currentFilter + "," + currentSort;
			currentPage = Integer.parseInt(request.getParameter("currentPage"));
			clickedPage = Integer.parseInt(request.getParameter("goToPageNo"));

			if (clickedPage == -2) { // Previous Link Clicked
				currentPage--;
				clickedPage = currentPage;

			} else {
				if (clickedPage == -1) { // Next Link Clicked
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}

			maxPage = Integer.parseInt(request.getParameter("maxPages"));

			hostId = putHostIdListInReq(request);

			List sysLogList = new ArrayList();

			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
							.getFromDate(), systemLog.getToDate(), currentPage - 1,
					maxPage, sysLogList, filterSortStatus, sysformat);
			// START: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010
			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			// END: Modified code by Aliveni to set totalCount attribute in
			// request to display the
			// total number of records on screen, on 30-SEP-2010

			PageDetails pSummary = new PageDetails();
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			String nextLinkStatus = null;
			String prevLinkStatus = null;

			if (clickedPage > 1) // PREVIOUS LINK is enabled if clickedPage
				// is greater than 1
				prevLinkStatus = "true";
			else
				prevLinkStatus = "false";

			if (clickedPage < maxPage) // NEXT LINK is enabled if clickedPage
				// is less than maximun Page No.
				nextLinkStatus = "true";
			else
				nextLinkStatus = "false";

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}

			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			log.debug("exiting next method");

			return getView("refresh");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in SystemLogAction.'next' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error("Exception Catch in SystemLogAction.'next' method : "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "next", SystemLogAction.class), request, "");
			return getView("fail");
		} finally {
			systemLog = null;
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			hostId = null;
		}
	} // End of next method

	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}
	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
											HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns

				width =   SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME + "=85" + ","
						+ SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME + "=80" + ","
						+ SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME + "=144" + ","
						+ SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME + "=125" + ","
						+ SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME + "=364" + ","
						+ SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME + "=142";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder =
						SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME + ","
								+ SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME + ","
								+ SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME + ","
								+ SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME + ","
								+ SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME + ","
								+ SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME;
			}

			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();



			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;

				// LOG_DATE column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// LOG_TIME column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// USER_ID column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_USER_ID_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_USER_ID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// IP_ADDRESS column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// PROCESS column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_PROCESS_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_PROCESS_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// ACTION column
				if (order.equals(SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_ACTION_HEADING, request),
							SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SYSTEM_LOG_LIST_ACTION_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	/**
	 * @desc This method fetches the System Log details for the Predict System
	 *       Date
	 * @return
	 * @throws Exception
	 */
	public String displayAngular()
			throws SwtException {
		/* Variable Declaration for systemDate */
		String systemDate = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for pageSize */
		int pageSize = 0;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for initialPageCount */
		int initialPageCount = 0;
		/* Variable Declaration for isNext */
		boolean isNext = false;
		/* Variable Declaration for date manupilation */
		String forDate = null;
		String displayedDate = null;
		Date selectedDate = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug("entering defaultDetails  method");

			systemDate = putSystemDateInRequest(request);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			SystemLog systemLog = (SystemLog) (getSystemLog());
			systemLog.setFromDateAsString(systemDate);
			systemLog.setToDateAsString(systemDate);
			fromDateAsString = systemLog.getFromDateAsString();
			toDateAsString = systemLog.getToDateAsString();

			currentFilter = request.getParameter("selectedFilter");
			currentSort = request.getParameter("selectedSort");
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			filterSortStatus = currentFilter + "," + currentSort;

			if (request.getParameter("selectedFromDateChooser") != null) {
				fromDateAsString = request.getParameter("selectedFromDateChooser");
			}
			if (request.getParameter("selectedToDateChooser") != null) {
				toDateAsString = request.getParameter("selectedToDateChooser");
			}

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			systemLog.setFromDate(SwtUtil.parseDate(fromDateAsString, sysformat.getDateFormatValue()));

			systemLog.setToDate(SwtUtil.parseDate(toDateAsString, sysformat.getDateFormatValue()));
			String hostId = putHostIdListInReq(request);

			currentPage = 1;
			if (request.getParameter("currentPage") != null) {
				currentPage =  Integer.parseInt(request.getParameter("currentPage"));
			}
			ArrayList sysLogList = new ArrayList();

			int maxPage = 0;
			int totalCount = 0;
			totalCount = systemLogManager.getSystemLogList(hostId, systemLog
							.getFromDate(), systemLog.getToDate(), currentPage - 1,
					initialPageCount, sysLogList, filterSortStatus, sysformat);

			/*
			 * this Block reads PageSize from the property file and calculates
			 * the maxPages
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			PageDetails pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			ArrayList<PageDetails> pageSummaryList = new ArrayList<PageDetails>();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;

			setSystemLog(systemLog);
			Iterator itr = sysLogList.iterator();
			while (itr.hasNext()) {
				systemLog = (SystemLog) (itr.next());
				systemLog.setLogDate_Date(SwtUtil.formatDate(systemLog
						.getLogDate(), sysformat.getDateFormatValue()));
			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("systemLogList", sysLogList);
			request.setAttribute("fromDate", systemDate);
			request.setAttribute("toDate", systemDate);
			request.setAttribute("totalCount", totalCount);

			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));

			//selected for date value
			if (SwtUtil.isEmptyOrNull(forDate)) {

				selectedDate = SwtUtil.getSystemDatewithoutTime();
				displayedDate= SwtUtil.formatDate(selectedDate, SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());

			}else {
				selectedDate =SwtUtil.parseDate(forDate,SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue());
				displayedDate= forDate;
			}

			//Angular part
			String dateFormat = null;
			String width = null;
			String columnOrder = null;
			String hiddenColumns = null;
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SYSTEM_LOG_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", "entityId");
			responseConstructor.createRowElement(SwtConstants.ERROR_LOG_LIST_ERROR_FROMDATE_TAGNAME, fromDateAsString);
			responseConstructor.createRowElement(SwtConstants.ERROR_LOG_LIST_ERROR_TODATE_TAGNAME, toDateAsString);
			responseConstructor.createRowElement("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request, SwtUtil.getUserCurrentEntity(request.getSession())));
			responseConstructor.createElement("displayedDate",displayedDate );
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement("totalCount",	totalCount);
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			/******* SystemLogList ******/
			responseConstructor.formGridStart("grid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)
			responseConstructor.formRowsStart(sysLogList.size());
			for (Iterator<SystemLog> it = sysLogList.iterator(); it.hasNext();) {
				// Obtain system log record from iterator
				SystemLog systemLogRecord = (SystemLog) it.next();
				responseConstructor.formRowStart();

				String dateFormatted = SwtUtil.formatDate(systemLogRecord.getLogDate().toString(), SwtUtil.getCurrentDateFormat(request.getSession()));

				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_LOG_DATE_TAGNAME,
						(systemLogRecord.getLogDate() == null || systemLogRecord.getLogDate().equals(""))
								? ""
								: SwtUtil.formatDate(systemLogRecord.getLogDate(), dateFormat));

				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_LOG_TIME_TAGNAME, systemLogRecord.getLogDate_Time());
				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_USER_ID_TAGNAME, systemLogRecord.getUserId());
				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME, systemLogRecord.getIpAddress());
				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_PROCESS_TAGNAME, systemLogRecord.getProcess());
				responseConstructor.createRowElement(SwtConstants.SYSTEM_LOG_LIST_ACTION_TAGNAME, systemLogRecord.getAction());

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SYSTEM_LOG_LIST);
			request.setAttribute("data", xmlWriter.getData());

			log.debug("exiting 'defaultDetails' method");

			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in SystemLogAction.'defaultDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception Catch in SystemLogAction.'defaultDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "defaultDetails", SystemLogAction.class), request, "");
			return getView("fail");
		} finally {
			systemDate = null;
			filterSortStatus = null;
			currentFilter = null;
			currentSort = null;
			sysformat = null;
		}
	}
} // End of Class SystemLogAction