package org.swallow.control.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;




import org.swallow.cluster.RegisterZookeeper;
import org.swallow.control.model.ConnectionPool;
import org.swallow.control.service.ConnectionPoolControlManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuItem;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.pcm.SwtPCDataSource;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Autowired;





import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/connectionPool", "/connectionPool.do"})
public class ConnectionPoolControlAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("connectionpoolflex", "jsp/control/connectionpoolmonitor");
		viewMap.put("data", "jsp/data");
		viewMap.put("connectionpoolview", "jsp/control/connectionpoolview");
		viewMap.put("statechange", "jsp/flexstatechange");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "connectionPoolView":
				return connectionPoolView();
			case "displayConnectionPool":
				return displayConnectionPool();
			case "displayConnectionPoolList":
				return displayConnectionPoolList();
			case "killConnectionPool":
				return killConnectionPool();
			case "checkConnectionChanged":
				return checkConnectionChanged();
			case "unspecified":
				return unspecified();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "saveColumnOrder":
				return saveColumnOrder();
		}


		return unspecified();
	}




	private final String screenId = "25";
	private final Log log = LogFactory.getLog(ConnectionPoolControlAction.class);
	@Autowired
	private ConnectionPoolControlManager connectionPoolControlManager;

	public void setConnectionPoolControlManager(ConnectionPoolControlManager connectionPoolControlManager) {
		this.connectionPoolControlManager = connectionPoolControlManager;
	}

	public String connectionPoolView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("method", "connectionpoolview");
		return getView("connectionpoolview");
	}

	/**
	 * This method will be used to obtain datasources information how many
	 * connection is opened how many connection has been leaked What those
	 * connection are doing : the Stack trace.
	 */
	public String connectionMonitor(String userName, String password) throws Exception {
		String pcmConn = "";
		// Get the notification object
		String pcmEnabled = null;
		String statsResult = null;
		StringBuffer buffer = new StringBuffer();
//		buffer.append("<b>Connection Pools stats</b>");
//		buffer.append("<br>");
//		buffer.append("Predict = " + ((SwtDataSource) SwtDataSource.getInstance()).getLastConnectionPoolStats());
//		buffer.append("<br>");
//		pcmEnabled = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PCM_ENABLED);
//		if ("true".equals(pcmEnabled)) {
//			// load the AppTest at runtime
//			Class cls = Class.forName("org.swallow.util.pcm.SwtPCDataSource");
//			Object obj = cls.newInstance();
//			Class noparams[] = {};
//			// call the printIt method
//			Method method = cls.getDeclaredMethod("getLeakedConnection", noparams);
//			pcmConn = (String) method.invoke(obj);
//
//			Method methodStats = cls.getDeclaredMethod("getLastConnectionPoolStats", noparams);
//			statsResult = (String) methodStats.invoke(obj);
//			buffer.append("PCM = " + statsResult);
//			buffer.append("<br>");
//		}
//		buffer.append("<b>Leaked Connections</b>");
//		buffer.append("<br>");
//		buffer.append(
//				"<table><tr><th><br>Module</th><th><br>Hash</th><th><br>isClosed</th><th><br>Duration<br></th><th><br>StackTrace</th></tr>");
//		String predictConn = ((SwtDataSource) SwtDataSource.getInstance()).getLeakedConnection();
//		buffer.append(pcmConn);
//		buffer.append(predictConn);
//		buffer.append("</table>");

		return buffer.toString();
	}

	public String displayConnectionPool() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ConnectionPool connectionPool = null;
		String screen = null;
		String connectionId = null;
		String moduleId = null;
		try {
			log.debug(this.getClass().getName() + " method [displayConnectionPool] - Enter ");
			request.setAttribute("screenName", request.getParameter("screenName"));
			connectionId = request.getParameter("connectionId");
			moduleId = request.getParameter("moduleId");

			if (!SwtUtil.isEmptyOrNull(moduleId)) {
				if (moduleId.equalsIgnoreCase("PREDICT")) {
					if(RegisterZookeeper.getInstance().isClusterEnabled()) {
						connectionPool = ((SwtDataSource) SwtDataSource.getInstance())
								.getConnectionPoolByIdFromCluster(connectionId);
					}else {

						connectionPool = ((SwtDataSource) SwtDataSource.getInstance())
								.getConnectionPoolById(connectionId);
					}
				} else {
					boolean pcmEnabled = SwtUtil.getPcmEnabled();
					if (pcmEnabled) {

						if(RegisterZookeeper.getInstance().isClusterEnabled()) {
							connectionPool = ((SwtPCDataSource) SwtPCDataSource.getInstance())
									.getConnectionPoolByIdFromCluster(connectionId);
						}else {

							connectionPool = ((SwtPCDataSource) SwtPCDataSource.getInstance())
									.getConnectionPoolById(connectionId);

						}

					}

//					connectionPoolItem = ((SwtPCDataSource) SwtPCDataSource.getInstance())
//							.getConnectionPoolById(connectionId);
				}
			}
			connectionPool = connectionPoolControlManager.getConnectionPool(connectionPool, moduleId);
			request.setAttribute("connectionPool", connectionPool);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [displayConnectionPool] - Exit ");
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [displayConnectionPool] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
		return getView("connectionpooladdflexdata");
	}

	public String displayConnectionPoolList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " method [displayConnectionPoolList] - Enter ");

		List<ConnectionPool> openConnections = null;
		String moduleId = null;
		HashMap<String, String> connectionStats = null;
		ArrayList<ConnectionPool> clonedList = null;
		boolean dbViewGranted = false;
		boolean freshDataViews = true;
		try {
			moduleId = request.getParameter("moduleId");
			if (SwtUtil.isEmptyOrNull(moduleId)) {
				moduleId = "PREDICT";
			}
			if (!SwtUtil.isEmptyOrNull(moduleId) && moduleId.equals("PREDICT")) {
				freshDataViews= ((SwtDataSource) SwtDataSource.getInstance()).getFreshViewsData();
				if(RegisterZookeeper.getInstance().isClusterEnabled()) {
					openConnections = ((SwtDataSource) SwtDataSource.getInstance()).getLastActiveConnectionsFromCluster();
					clonedList = new ArrayList<ConnectionPool>(openConnections);
					// clonedList = connectionPoolControlManager.getConnectionPoolList(clonedList,
					// moduleId);
					connectionStats = ((SwtDataSource) SwtDataSource.getInstance()).getLastConnectionPoolStatsFromCluster();
				}else {
					openConnections = ((SwtDataSource) SwtDataSource.getInstance()).getLastActiveConnections();
					clonedList = new ArrayList<ConnectionPool>(openConnections);
					// clonedList = connectionPoolControlManager.getConnectionPoolList(clonedList,
					// moduleId);
					connectionStats = ((SwtDataSource) SwtDataSource.getInstance()).getLastConnectionPoolStats();
				}

			} else if (moduleId.equalsIgnoreCase("PCM")){
				boolean pcmEnabled = SwtUtil.getPcmEnabled();
				if (pcmEnabled) {
					// load the AppTest at runtime
					Class cls;
					Class noparams[] = {};
					try {

						if(RegisterZookeeper.getInstance().isClusterEnabled()) {
							openConnections = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastActiveConnectionsFromCluster();

							clonedList = new ArrayList<ConnectionPool>(openConnections);

//								method = cls.getDeclaredMethod("getLastConnectionPoolStats", noparams);
							connectionStats = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastConnectionPoolStatsFromCluster();
						}else {
							openConnections = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastActiveConnections();

							clonedList = new ArrayList<ConnectionPool>(openConnections);

//								method = cls.getDeclaredMethod("getLastConnectionPoolStats", noparams);
							connectionStats = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastConnectionPoolStats();
						}



//							connectionStats = (HashMap<String, String>) method.invoke(obj);
						//connectionStats = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastConnectionPoolStats();

//							method = cls.getDeclaredMethod("getFreshViewsData", noparams);
						freshDataViews= ((SwtPCDataSource) SwtPCDataSource.getInstance()).getFreshViewsData();

						//freshDataViews= ((SwtPCDataSource) SwtPCDataSource.getInstance()).getFreshViewsData();
					} catch (Exception e) {
						throw SwtErrorHandler.getInstance().handleException(new SwtException("Method invoke fails"+e.getMessage()), "displayConnectionPoolList",
								this.getClass());
					}
				}
			}
			if(freshDataViews)
				dbViewGranted = connectionPoolControlManager.checkDBViewsGrant(moduleId);
			else
				dbViewGranted= true;
			log.debug(this.getClass().getName() + " method [displayConnectionPoolList] - Exit ");
			if (dbViewGranted) {
				return sendDisplayResponse(request, clonedList, connectionStats, moduleId, freshDataViews);
			} else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", "NO_ENOUGH_GRANTS");
				return getView("statechange");

			}
		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - [displayConnectionPoolList] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}
//		return getView("connectionpoolflexdata");
	}

	/**
	 * This method forms the xml for displaying the category list.
	 *
	 * @param connectionStats - passing languageId
	 * @return
	 */
	public String sendDisplayResponse(HttpServletRequest request, ArrayList<ConnectionPool> connectionPools, HashMap<String, String> connectionStats, String selectedModule, boolean freshViewsData)
			throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		int menuAccessId = 2;
		CommonDataManager cdm = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			// Instantiate SwtResponseConstructor
			responseConstructor = new SwtResponseConstructor();
			// Get instance of SwtXMLWriter
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "connectionPoolMonitorStats";

			cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);

			// TODO: FIX MENU ACCESS ID
			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");




			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);

			xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
			if (freshViewsData) {
				MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_CONNECTION_POOL + "",
						cdm.getUser());
				if (menuItem != null)
					menuAccessId = SwtUtil.getHierarchicalAccessId(menuItem, request);

				xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, menuAccessId);
				xmlWriter.addAttribute(PCMConstant.LAST_REF_TIME,
						SwtUtil.getLastRefTime(request, SwtUtil.getUserCurrentEntity(request.getSession())));
			}
			else {
				SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
				Date date = new Date();
				String lastRef = formatter.format(date);
				xmlWriter.addAttribute(PCMConstant.LAST_REF_TIME, lastRef);
				xmlWriter.addAttribute(SwtConstants.MENU_ACCESS, 0);
			}
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(SwtConstants.CONNECTIONPOOLMONITOR);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node

			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement(SwtConstants.MODULE, selectedModule);
			if(connectionStats != null && connectionStats.size() >0) {
				responseConstructor.createElement(SwtConstants.CONNECTION_POOL_ACTIVE_STATS,
						connectionStats.get(SwtConstants.CONNECTION_POOL_ACTIVE_ID));
				responseConstructor.createElement(SwtConstants.CONNECTION_POOL_IDLE_STATS,
						connectionStats.get(SwtConstants.CONNECTION_POOL_IDLE_ID));
			}
			responseConstructor.createElement(SwtConstants.CONNECTION_POOL_IS_FRESH_VIEWS_DATA, ""+freshViewsData);
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			xmlWriter.clearAttribute();

			/***** SpreadProfile Combo ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("Predict", "PREDICT", selectedModule.equalsIgnoreCase("PREDICT")));
			if(SwtUtil.getPcmEnabled()) {
				lstOptions.add(new OptionInfo("PCM", "PCM", selectedModule.equalsIgnoreCase("PCM")));
			}
			lstSelect.add(new SelectInfo(SwtConstants.MODULE_LIST, lstOptions));

			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("", "", false));
			lstSelect.add(new SelectInfo("DUMMY", lstOptions));
			/***** TargetCalculation Combo ***********/

			responseConstructor.formSelect(lstSelect);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getConnectionPoolGridColumns(width, columnOrder, hiddenColumns, request));

			HashMap<String, String> sqlStatusListValues = new HashMap<String, String>();
			sqlStatusListValues.put("ACTIVE", SwtUtil.getMessage("connectionPool.connectionSQLActive", request));
			sqlStatusListValues.put("INACTIVE", SwtUtil.getMessage("connectionPool.connectionSQLInActive", request));
			sqlStatusListValues.put("KILLED", SwtUtil.getMessage("connectionPool.connectionSQLKilled", request));
			sqlStatusListValues.put("SNIPED", SwtUtil.getMessage("connectionPool.connectionSQLSniped", request));

			HashMap<String, String> javaStatusListValues = new HashMap<String, String>();
			javaStatusListValues.put("CLOSED", SwtUtil.getMessage("connectionPool.connectionJDBCClosed", request));
			javaStatusListValues.put("OPEN", SwtUtil.getMessage("connectionPool.connectionJDBCOpen", request));

			// form rows (records)
			responseConstructor.formRowsStart(connectionPools.size());
			// Iterating category details
			for (Iterator<ConnectionPool> it = connectionPools.iterator(); it.hasNext();) {
				// Obtain category tag from iterator
				ConnectionPool connectionPool = (ConnectionPool) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME,
						connectionPool.getId().getConnectionId());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_MODULE_TAGNAME,
						connectionPool.getModule());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_STATUS_TAGNAME,
						javaStatusListValues.get(connectionPool.getStatus()));
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_STATUS_VALUE_TAGNAME,
						connectionPool.getStatus());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME,
						sqlStatusListValues.get(connectionPool.getSqlStatus()));
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SQL_STATUS_VALUE_TAGNAME,
						connectionPool.getSqlStatus());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SQLID_TAGNAME,
						connectionPool.getSqlId());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME,
						connectionPool.getSqlStatement());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME,
						"" + SwtUtil.formatDate(connectionPool.getLastActionTime(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SSID_TAGNAME,
						connectionPool.getSsId());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME,
						connectionPool.getAudSid());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME,
						connectionPool.getStackTrace());
				responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_SQL_EXEC_START,
						"" + SwtUtil.formatDate(connectionPool.getSqlExecStartTime(),
								SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"));

				if(connectionPool.getDuration() == null) {
					connectionPool.setDuration(new Double((new Date().getTime() - connectionPool.getStartTime().getTime()) / 1000));
				}

				if("CLOSED".equals(connectionPool.getStatus())) {
					responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_DURATION_TAGNAME,"");
					responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_BACKGROUND_COLOR,"");
				}else {

					if(SwtUtil.isEmptyOrNull(connectionPool.getSqlStatus())){
						responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_BACKGROUND_COLOR,"GREY");
					}else {
						if("INACTIVE".equals(connectionPool.getSqlStatus())){
							if(connectionPool.getDuration() != null) {
								responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_BACKGROUND_COLOR,connectionPool.getDuration()>5?"RED":"");
							}else {

							}
						}else {
							responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_BACKGROUND_COLOR,"");
						}
					}
					responseConstructor.createRowElement(SwtConstants.CONNECTION_POOL_DURATION_TAGNAME,
							connectionPool.getDuration() != null? ""+connectionPool.getDuration() :"");

				}




				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.clearAttribute();
			// form drop down details
			xmlWriter.endElement(SwtConstants.CONNECTIONPOOLMONITOR);
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			ex.printStackTrace();
			xmlWriter.clearData();
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
		} catch (Exception ex) {
			ex.printStackTrace();
			xmlWriter.clearData();

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					+ ex.getMessage());
			ex.printStackTrace();
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
		return null;

	}

	/**
	 * This method creates sample column details
	 *
	 * @param width       - passing columns widths
	 * @param columnOrder - passing default column order
	 * @return List 0f columns
	 * @throws SwtException
	 */
	private List<ColumnInfo> getConnectionPoolGridColumns(String width, String columnOrder, String hiddenColumns,
														  HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = SwtConstants.CONNECTION_POOL_MODULE_TAGNAME + "=100,"
						+ SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME + "=150" + ","
						+ SwtConstants.CONNECTION_POOL_STATUS_TAGNAME + "=120,"
						+ SwtConstants.CONNECTION_POOL_DURATION_TAGNAME + "=140,"
						+ SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME + "=500,"
						+ SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME + "=110,"
						+ SwtConstants.CONNECTION_POOL_SQLID_TAGNAME + "=130,"
						+ SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME + "=500,"
						+ SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME + "=150,"
						+ SwtConstants.CONNECTION_POOL_SSID_TAGNAME + "=80,"
						+ SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME + "=110";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_STATUS_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_DURATION_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_SSID_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME+","
						+ SwtConstants.CONNECTION_POOL_SQLID_TAGNAME + ","
						+ SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME+ ","
						+ SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME+","
						+ SwtConstants.CONNECTION_POOL_MODULE_TAGNAME;

			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// Category ID type column
				if (order.equals(SwtConstants.CONNECTION_POOL_MODULE_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_HEADING, request),
							SwtConstants.CONNECTION_POOL_MODULE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_MODULE_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_MODULE_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// RULE
				if (order.equals(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_CONNECTIONID_HEADING, request),
							SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_CONNECTIONID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// RULE
				if (order.equals(SwtConstants.CONNECTION_POOL_STATUS_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_STATUS_HEADING, request),
							SwtConstants.CONNECTION_POOL_STATUS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_STATUS_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_STATUS_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_STATUS_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Instant Release column
				if (order.equals(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_HEADING, request),
							SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 3,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_LASTACTIONTIME_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_STACKTRACE_HEADING, request),
							SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME)), false, true,
							hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_STACKTRACE_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_STACKTRACE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Include in Target column
				if (order.equals(SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_SQL_STATUS_HEADING, request),
							SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME)), true,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_SQL_STATUS_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_MODULE_SQL_STATUS_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_SQLID_TAGNAME)) {
					tmpColumnInfo =new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SQLID_HEADING, request),
							SwtConstants.CONNECTION_POOL_SQLID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_SQLID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_SQLID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SQLID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_HEADING, request),
							SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 7,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SQLSTATEMENT_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_SSID_TAGNAME)) {
					tmpColumnInfo =new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SSID_HEADING, request),
							SwtConstants.CONNECTION_POOL_SSID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 8,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_SSID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_SSID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_SSID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_AUDSID_HEADING, request),
							SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 9,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_AUDSID_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_AUDSID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);

				}
				// CATEGORY_NAME
				if (order.equals(SwtConstants.CONNECTION_POOL_DURATION_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_DURATION_HEADING, request),
							SwtConstants.CONNECTION_POOL_DURATION_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_DURATION_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_DURATION_TAGNAME));
					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_DURATION_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);

				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCategoryMaintenanceGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getCategoryMaintenanceGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getCategoryMaintenanceGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	public String killConnectionPool() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ConnectionPool connectionPool = null;
		String connectionIds = null;
		String moduleId = null;
		try {
			log.debug(this.getClass().getName() + " method [deleteConnectionPool] - Enter ");
			connectionIds = request.getParameter("connectionIds");
			moduleId = request.getParameter("moduleId");

			if (!SwtUtil.isEmptyOrNull(moduleId)) {

				try {
					connectionPoolControlManager.killDBSessionConnections(moduleId, connectionIds);
				} catch (Exception e) {
				}


				if (moduleId.equalsIgnoreCase("PREDICT")) {
					if(RegisterZookeeper.getInstance().isClusterEnabled()) {
						((SwtDataSource) SwtDataSource.getInstance()).closeConnectionsFromCluster(connectionIds);
					}
					else {
						((SwtDataSource) SwtDataSource.getInstance()).closeConnections(connectionIds);
					}
				} else {
					boolean pcmEnabled = SwtUtil.getPcmEnabled();
					if (pcmEnabled) {
						Class params[] = {String.class};
						// call the printIt method
						if(RegisterZookeeper.getInstance().isClusterEnabled()) {
							((SwtPCDataSource) SwtPCDataSource.getInstance()).closeConnectionsFromCluster(connectionIds);
						}
						else {
							((SwtPCDataSource) SwtPCDataSource.getInstance()).closeConnections(connectionIds);
						}

//						Method method = cls.getDeclaredMethod("closeConnections", params);
//						method.invoke(obj, connectionIds);
						//					((SwtPCDataSource) SwtPCDataSource.getInstance()).closeConnections(connectionIds);
					}

				}
			}
			request.setAttribute("connectionPool", connectionPool);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [deleteConnectionPool] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [deleteConnectionPool] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}

		return displayConnectionPoolList();
	}

	/**
	 * Check if the connection still not changed from the last refresh before
	 * killing
	 *
	 * @return
	 * @throws SwtException
	 */
	public String checkConnectionChanged() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		ConnectionPool connectionPoolItem = null;
		ConnectionPool selectedConnectionItem = null;
		String connectionId = null;

		String moduleId = null;
		String duration = null;
		String sqlStatement = null;
		String sqlStatus = null;
		String audSid = null;
		String stackTrace = null;
		String ssid = null;
		String lastActionTime = null;
		String sqlId = null;
		String status = null;

		try {
			log.debug(this.getClass().getName() + " method [deleteConnectionPool] - Enter ");
			connectionId = request.getParameter("connectionId");
			moduleId = request.getParameter("moduleId");
			duration = request.getParameter("duration");
			sqlStatement = request.getParameter("sqlStatement");
			sqlStatus = request.getParameter("sqlStatus");
			audSid = request.getParameter("audSid");
			stackTrace = request.getParameter("stackTrace");
			ssid = request.getParameter("ssid");
			lastActionTime = request.getParameter("lastActionTime");
			status = request.getParameter("status");
			sqlId = request.getParameter("sqlId");

			Date lastActionTimeAsDate = SwtUtil.parseDate(lastActionTime,
					SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss");

			selectedConnectionItem = new ConnectionPool();
			selectedConnectionItem.getId().setConnectionId(connectionId);
			selectedConnectionItem.setModule(moduleId);
			selectedConnectionItem.setSqlStatement(sqlStatement);
			selectedConnectionItem.setSqlStatus(sqlStatus);
			selectedConnectionItem.setSqlId(sqlId);
			selectedConnectionItem.setAudSid(audSid);
			selectedConnectionItem.setStackTrace(stackTrace);
			selectedConnectionItem.setSsId(ssid);
			selectedConnectionItem.setLastActionTime(lastActionTimeAsDate);
			selectedConnectionItem.setStatus(status);

			if (!SwtUtil.isEmptyOrNull(moduleId)) {
				if (moduleId.equalsIgnoreCase("PREDICT")) {
					if(RegisterZookeeper.getInstance().isClusterEnabled()) {
						connectionPoolItem = ((SwtDataSource) SwtDataSource.getInstance())
								.getConnectionPoolByIdFromCluster(connectionId);
					}else {

						connectionPoolItem = ((SwtDataSource) SwtDataSource.getInstance())
								.getConnectionPoolById(connectionId);
					}
				} else {
					boolean pcmEnabled = SwtUtil.getPcmEnabled();
					if (pcmEnabled) {

						if(RegisterZookeeper.getInstance().isClusterEnabled()) {
							connectionPoolItem = ((SwtPCDataSource) SwtPCDataSource.getInstance())
									.getConnectionPoolByIdFromCluster(connectionId);
						}else {

							connectionPoolItem = ((SwtPCDataSource) SwtPCDataSource.getInstance())
									.getConnectionPoolById(connectionId);

						}

					}

//					connectionPoolItem = ((SwtPCDataSource) SwtPCDataSource.getInstance())
//							.getConnectionPoolById(connectionId);
				}
			}

			if (connectionPoolItem != null) {

				connectionPoolItem = connectionPoolControlManager.getConnectionPool(connectionPoolItem, moduleId);

				if (compare(selectedConnectionItem.getModule(), connectionPoolItem.getModule())
						&& compare(selectedConnectionItem.getSqlStatement(), connectionPoolItem.getSqlStatement())
						&& compare(selectedConnectionItem.getSqlStatus(), connectionPoolItem.getSqlStatus())
						&& compare(selectedConnectionItem.getAudSid(), connectionPoolItem.getAudSid())
						&& compare(selectedConnectionItem.getStackTrace(), connectionPoolItem.getStackTrace())
						&& compare(selectedConnectionItem.getSsId(), connectionPoolItem.getSsId())
						&& compare(selectedConnectionItem.getStatus(), connectionPoolItem.getStatus())
						&& compare(selectedConnectionItem.getSqlId(), connectionPoolItem.getSqlId())) {
				} else {
					request.setAttribute("reply_status_ok", "false");
					request.setAttribute("reply_message", "CONNECTION_DETAILS_CHANGED");
					return getView("statechange");
				}

			} else {
				request.setAttribute("reply_status_ok", "false");
				request.setAttribute("reply_message", "CONNECTION_NOT_EXIST");
				return getView("statechange");
			}

			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName() + " method [deleteConnectionPool] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [deleteConnectionPool] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "." + e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " " + e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		}

		return displayConnectionPoolList();
	}

	public static boolean compare(String str1, String str2) {
		if (SwtUtil.isEmptyOrNull(str1) && SwtUtil.isEmptyOrNull(str1))
			return true;
		else if (!SwtUtil.isEmptyOrNull(str1) && SwtUtil.isEmptyOrNull(str2)
				|| SwtUtil.isEmptyOrNull(str1) && !SwtUtil.isEmptyOrNull(str2)) {
			return false;
		} else {
			if (str1.trim().equals(str2.trim())) {
				return true;
			} else {
				return false;
			}
		}

	}

	public String unspecified() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
		return getView("connectionpoolflex");
	}

	private void bindColumnOrderInRequest(HttpServletRequest request) throws SwtException {
	}

	private void bindColumnWidthInRequest(HttpServletRequest request) throws SwtException {
	}

	public String saveColumnWidth() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String width = null;
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request, SwtUtil.getUserCurrentEntity(request.getSession()), screenId,
						"display", "column_width", width);
			} else {
				throw new Exception("You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName() + " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "saveColumnWidth", ConnectionPool.class), request,
					"");
		}
		return getView("statechange");
	}

	public String saveColumnOrder() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String columnOrder = null;
		try {
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Entry ");
			columnOrder = request.getParameter("order");
			if (!SwtUtil.isEmptyOrNull(columnOrder)) {
				SwtUtil.setPropertyValue(request, SwtUtil.getUserCurrentEntity(request.getSession()), screenId,
						"display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [ saveColumnOrder() ] - " + e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
		} finally {
			columnOrder = null;
			log.debug(this.getClass().getName() + " - [ saveColumnOrder ] - Exit ");
		}
		return getView("statechange");
	}
}