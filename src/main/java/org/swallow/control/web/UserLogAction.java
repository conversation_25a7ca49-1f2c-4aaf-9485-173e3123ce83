/*
 * @(#) UserLogAction.java  03/01/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.control.dao.hibernate.ArchiveDAOHibernate;
import org.swallow.control.model.AuditLog;
import org.swallow.control.service.AuditLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.*;
import org.springframework.beans.factory.annotation.Autowired;

import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;

/**
 *
 *
 * this class is used to display the User Log details.
 */



import java.util.Collection;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/userlog", "/userlog.do"})
public class UserLogAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("viewDetail", "jsp/control/auditlogview");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/control/userlog");
		viewMap.put("user", "jsp/control/userlog");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
	private AuditLog auditLog;
	public AuditLog getAuditLog() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		auditLog = RequestObjectMapper.getObjectFromRequest(AuditLog.class, request);
		return auditLog;
	}

	public void setAuditLog(AuditLog auditLog) {
		this.auditLog = auditLog;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("auditLog", auditLog);
	}

	@Autowired
	private AuditLogManager auditLogManager = null;
	private final static String dbLink = "Null";
	private final Log log = LogFactory.getLog(UserLogAction.class);

	/**
	 * @param auditLogManager
	 */
	public void setAuditLogManager(AuditLogManager auditLogManager) {
		this.auditLogManager = auditLogManager;
	}

	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();

		return hostId;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "defaultDetails":
				return defaultDetails();
			case "next":
				return next();
			case "showDetails":
				return showDetails();
			case "viewDetail":
				return viewDetail();
			case "displayAngular":
				return displayAngular();
			case "openViewDetails":
				return openViewDetails();
		}


		return unspecified();
	}

	/**
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
		return getView("user");
	}

	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putSystemDateInRequest(HttpServletRequest request)
			throws SwtException {
		String systemDate = SwtUtil.getSystemDateString();

		return systemDate;
	}

	/**
	 * this method is used to display the default details for User Log.
	 *
	 * @return
	 * @throws SwtException
	 */
	public String defaultDetails()
			throws SwtException {
		/* Local variable declaration */
		// Variable to hold fromDateAsString
		String fromDateAsString = null;
		// Variable to hold toDateAsString
		String toDateAsString = null;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold currentSort
		String currentSort = null;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold CDM
		CommonDataManager CDM = null;
		// Variable to hold auditLogList
		ArrayList auditLogList = null;
		// Variable to hold pSummary
		PageDetails pSummary = null;
		// Variable to hold pageSummaryList
		ArrayList pageSummaryList = null;
		// Variable to hold dyForm
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable to hold auditLog
		AuditLog auditLog = null;
		// Variable to hold currUser
		User currUser = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold userName
		String userName = null;
		// Variable to hold systemDate
		String systemDate = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold sysformat
		SystemFormats sysFormat = null;
		// Variable to hold filterSortStatus
		String filterSortStatus = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currentPage
		int currentPage = 0;
		// Variable to hold initialPageCount
		int initialPageCount = 0;
		// Variable to hold isNext
		boolean isNext = false;
		// Variable to hold maxPage
		int maxPage = 0;
		// Variable to hold totalcount
		int totalcount = 0;

		try {
			log.debug(this.getClass().getName() + " - [defaultDetails()] - "
					+ "Entry");
// To remove: 			/* Converts the Action Form to DynaValidatorForm */
// To remove: 			dyForm = (DynaValidatorForm) form;
			// creating the auditLog instance
			auditLog = (AuditLog) (getAuditLog());
			// creating the CommonDataManager instance
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			currentPage = 1;
			// Getting currentuser
			currUser = (User) CDM.getUser();
			// Getting userId
			userId = currUser.getId().getUserId();
			// Getting userName
			userName = currUser.getUserName();
			// Put systemDate in request
			systemDate = putSystemDateInRequest(request);
			// Setting userId
			auditLog.getId().setUserId(userId);
			// Put HostId in request
			hostId = putHostIdListInReq(request);
			// Getting sysformat from the session
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Setting systemDate
			auditLog.setToDateAsString(systemDate);
			auditLog.setFromDateAsString(systemDate);
			// Setting From & To date
			auditLog.setToDate(SwtUtil.parseDateGeneral(auditLog
					.getToDateAsString()));
			auditLog.setFromDate(SwtUtil.parseDateGeneral(auditLog
					.getFromDateAsString()));
			// Getting FromDate
			fromDateAsString = auditLog.getFromDateAsString();
			// Getting ToDate
			toDateAsString = auditLog.getToDateAsString();

			// Creating new Arraylist for auditLogList
			auditLogList = new ArrayList();
			// Getting currentFilter from the request
			currentFilter = request.getParameter("selectedFilter");
			// Getting currentSort from the request
			currentSort = request.getParameter("selectedSort");
			// Checking the current filter
			if (currentFilter == null) {
				currentFilter = "all";
			}

			if (currentSort == null) {
				currentSort = "none";
			}

			if ("none".equals(currentSort)) {
				currentSort = "0|false"; // default sorting column
			}
			// Setting currentFilter & currentSort
			filterSortStatus = currentFilter + "," + currentSort;
			// Getting entity Id from the session
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			/*
			 * Code modified by Chidambaranathan for Mantis 1484 for newly added
			 * fields on 24-June-2011
			 */
			// To pass the parameter flag "YES" to the total count for Movement
			// Audit Log
			totalcount = auditLogManager.getAuditLogList(hostId, userId,
					auditLog.getFromDate(), auditLog.getToDate(), currentPage,
					initialPageCount, auditLogList, filterSortStatus,
					SwtConstants.NO, "M", sysFormat, dbLink, entityId);
			// Getting pagesize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// Value count for max page size
			maxPage = ((totalcount % pageSize) > 0) ? ((totalcount / pageSize) + 1)
					: (totalcount / pageSize);
			// creating Page Details
			pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			// Creating arraylist for pageSummaryList
			pageSummaryList = new ArrayList();
			pageSummaryList = new ArrayList<PageDetails>();
			// setting setCurrentPageNo
			pSummary.setCurrentPageNo(currentPage);
			// setting setMaxPages
			pSummary.setMaxPages(maxPage);
			// setting total count
			pSummary.setTotalCount(totalcount);
			// Adding into pageSummaryList
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1) {
				isNext = true;
			}

			// set auditlog dyform
			setAuditLog(auditLog);
			// Sets the lastRefTime in request object
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			// Sets the userId in request object
			request.setAttribute("userId", userId);
			// Sets the userName in request object
			request.setAttribute("userName", userName);
			// Sets the auditLog in request object
			request.setAttribute("auditLog", "auditLog");
			// Sets the currentPage in request object
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// Sets the prevEnabled in request object
			request.setAttribute("prevEnabled", "false");
			// Sets the nextEnabled in request object
			request.setAttribute("nextEnabled", "" + isNext);
			// Sets the maxPage in request object
			request.setAttribute("maxPage", Integer.toString(maxPage));
			// Sets the selectedFilter in request object
			request.setAttribute("selectedFilter", currentFilter);
			// Sets the selectedSort in request object
			request.setAttribute("selectedSort", currentSort);
			// Sets the pageSummaryList in request object
			request.setAttribute("pageSummaryList", pageSummaryList);
			// Sets the auditLogList in request object
			request.setAttribute("auditLogList", auditLogList);
			// Sets the fromDate in request object
			request.setAttribute("fromDate", systemDate);
			// Sets the toDate in request object
			request.setAttribute("toDate", systemDate);
			// Sets the totalCount in request object
			request.setAttribute("totalCount", "" + totalcount);
			log.debug(this.getClass().getName() + " - [defaultDetails()] - "
					+ "Exit");
			return getView("user");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [defaultDetails()] method : - "
					+ e.getMessage());
			SystemExceptionHandler.logError(e);

			return getView("fail");
		} finally {
			// Nullifyied the objects which already created
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			CDM = null;
			auditLogList = null;
			pSummary = null;
			pageSummaryList = null;
// To remove: 			dyForm = null;
			auditLog = null;
			currUser = null;
			userId = null;
			userName = null;
			systemDate = null;
			hostId = null;
			sysFormat = null;
			filterSortStatus = null;
			entityId = null;
		}
	}

	/**
	 * this method is used to get the next record.
	 *
	 * @return
	 * @throws SwtException
	 */
	public String next()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Local variable declaration */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold fromDateAsString
		String fromDateAsString = null;
		// Variable to hold toDateAsString
		String toDateAsString = null;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold CDM
		CommonDataManager CDM = null;
		// Variable to hold pSummary
		PageDetails pSummary = null;
		// Variable to hold auditLogList
		List auditLogList = null;
		// Variable to hold pageSummaryList
		ArrayList pageSummaryList = null;
		// Variable to hold nextLinkStatus
		String nextLinkStatus = null;
		// Variable to hold prevLinkStatus
		String prevLinkStatus = null;
		// Variable to hold sysformat
		SystemFormats sysFormat = null;
		// Variable to hold collUser
		Collection collUser = null;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold currentSort
		String currentSort = null;
		// Variable to hold filterSortStatus
		String filterSortStatus = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold totalcount
		int totalcount = 0;
		// Variable to hold currUser
		User currUser = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold userName
		String userName = null;
		// Variable to hold maxPage
		int maxPage = 0;
		// Variable to hold currentPage
		int currentPage = 0;
		// Variable to hold clickedPage
		int clickedPage = 0;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String dateFormat= null;
		Date fromDate= null;
		Date toDate= null;
		String selectedFromDate= null;
		String selectedToDate= null;
		String systemDate= null;
		try {
			log.debug(this.getClass().getName() + " - [next] - " + "Entry");
			currentPage = 1;

			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			// Getting CurrentSystemFormats from the request
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Getting systemDate from the request
			systemDate = putSystemDateInRequest(request);
			// Put HostId in request
			hostId = putHostIdListInReq(request);

			// Populating the user dropdown box
			collUser = auditLogManager.getUserList(hostId);
			// Getting currentuser
			request.setAttribute("users", collUser);

			// Populating the date and usedid seach criteria from the screen
			// inputs
			// Getting fromDateAsString from the request
			fromDateAsString = request.getParameter("fromDate");
			// Getting toDateAsString from the request
			toDateAsString = request.getParameter("toDate");
			// creating the CommonDataManager instance
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			// Getting currentuser
			currUser = (User) CDM.getUser();
			// Getting userId
			userId = currUser.getId().getUserId();
			// Getting userName
			userName = currUser.getUserName();
			/* gets all the filter values specified for all columns */
			currentFilter = request.getParameter("selectedFilter");

			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");
			// Checking the current filter
			if (currentFilter == null) {
				currentFilter = "all";
			}

			if (currentSort == null) {
				currentSort = "1|false";
			}
			// Setting currentFilter & currentSort
			filterSortStatus = currentFilter + "," + currentSort;
			// Parsing currentPage & clickedPage
			currentPage = Integer
					.parseInt((request.getParameter("currentPage") != null) ? request
							.getParameter("currentPage").trim().toString()
							: "0");
			clickedPage = Integer
					.parseInt((request.getParameter("goToPageNo") != null) ? request
							.getParameter("goToPageNo").trim().toString()
							: "0");

			if (clickedPage == -2) {
				// Previous Link Clicked
				currentPage--;
				clickedPage = currentPage;
			} else {
				if (clickedPage == -1) {
					// Next Link Clicked
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}
			// Getting maxpages from the request
			maxPage = Integer.parseInt(request.getParameter("maxPages"));
			// Creating arraylist for auditLogList
			auditLogList = new ArrayList();

			// Getting entityId from the session
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			if (!SwtUtil.isEmptyOrNull(request.getParameter("currentPage") )) {
				currentPage =  Integer.parseInt(request.getParameter("currentPage"));
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("fromDate")))  {
				selectedFromDate= request.getParameter("fromDate");
				fromDate =  SwtUtil.parseDate(selectedFromDate, sysFormat.getDateFormatValue());
			}else{
				selectedFromDate= systemDate;
				fromDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("toDate"))) {
				selectedToDate= request.getParameter("toDate");
				toDate =  SwtUtil.parseDate(selectedToDate,  sysFormat.getDateFormatValue());
			}else{
				selectedToDate= systemDate;
				toDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			// To pass the parameter flag "YES" to the total count for Movement
			// Audit Log
			totalcount = auditLogManager.getAuditLogList(hostId, userId,
					fromDate, toDate, currentPage,
					maxPage, auditLogList, filterSortStatus, SwtConstants.NO,
					"M", sysFormat, dbLink, entityId);
			// Getting pagesize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// Value count for max page size
			maxPage = ((totalcount % pageSize) > 0) ? ((totalcount / pageSize) + 1)
					: (totalcount / pageSize);
			// creating Page Details
			pSummary = new PageDetails();
			// Creating arraylist for pageSummaryList
			pageSummaryList = new ArrayList<PageDetails>();
			// setting setCurrentPageNo
			pSummary.setCurrentPageNo(currentPage);
			// setting setMaxPages
			pSummary.setMaxPages(maxPage);
			// setting total count
			pSummary.setTotalCount(totalcount);
			// Adding into pageSummaryList
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (clickedPage > 1) { // PREVIOUS LINK is enabled if clickedPage
				// is greater than 1
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}

			if (clickedPage < maxPage) { // NEXT LINK is enabled if
				// clickedPage is less than maximun
				// Page No.
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}

			// Sets the userId in request object
			request.setAttribute("userId", userId);
			// Sets the userName in request object
			request.setAttribute("userName", userName);
			// Sets the currentPage in request object
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// Sets the prevEnabled in request object
			request.setAttribute("prevEnabled", prevLinkStatus);
			// Sets the nextEnabled in request object
			request.setAttribute("nextEnabled", nextLinkStatus);
			// Sets the maxPage in request object
			request.setAttribute("maxPage", Integer.toString(maxPage));
			// Sets the selectedFilter in request object
			request.setAttribute("selectedFilter", currentFilter);
			// Sets the selectedSort in request object
			request.setAttribute("selectedSort", currentSort);
			// Sets the pageSummaryList in request object
			request.setAttribute("pageSummaryList", pageSummaryList);
			// Sets the totalCount in request object
			request.setAttribute("totalCount", "" + totalcount);
			// Sets the auditLogList in request object
			request.setAttribute("auditLogList", auditLogList);
			// Sets the fromDate in request object
			request.setAttribute("fromDate", fromDateAsString);
			// Sets the toDate in request object
			request.setAttribute("toDate", toDateAsString);
			// Sets the lastRefTime in request object
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));

			//Angular part

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.AUDIT_LOG_DATA);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", "entityId");
			responseConstructor.createRowElement("fromDate", selectedFromDate);
			responseConstructor.createRowElement("toDate", selectedToDate);
			responseConstructor.createRowElement("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement("totalCount",	totalcount);
			responseConstructor.createElement("defaultUserId",	userId);
			responseConstructor.createElement("defaultUserName", userName);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Iterator j = collUser.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("userList", lstOptions));
			responseConstructor.formSelect(lstSelect);

			/***** Entity Combo End ***********/

			/******* UserLogList ******/
			responseConstructor.formGridStart("grid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalcount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)
			responseConstructor.formRowsStart(auditLogList.size());
			for (Iterator<AuditLog> it = auditLogList.iterator(); it.hasNext();) {
				// Obtain error log record from iterator
				AuditLog auditLogRecord = (AuditLog) it.next();
				responseConstructor.formRowStart();
				String dateFormatted = SwtUtil.formatDate(auditLogRecord.getLogDate_Date(), SwtUtil.getCurrentDateFormat(request.getSession()));
				responseConstructor.createRowElement(SwtConstants.USER_LOG_DATE_TAGNAME,auditLogRecord.getLogDate_Date());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_TIME_TAGNAME, auditLogRecord.getLogDate_Time());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_USER_ID_TAGNAME, auditLogRecord.getId().getUserId());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_TAGNAME, auditLogRecord.getId().getReference());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_ID_TAGNAME, auditLogRecord.getId().getReferenceId());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ACTION_TAGNAME, auditLogRecord.getId().getAction());
				//hidden property

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.AUDIT_LOG_DATA);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug("Exiting UserLogAction.'next' method");
			return getView("data");

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			SystemExceptionHandler.logError(e);

			return getView("fail");
		} finally {
			/* nullifyied the objects created already. */
			hostId = null;
			fromDateAsString = null;
			toDateAsString = null;

			CDM = null;
			pSummary = null;
			auditLogList = null;
			pageSummaryList = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			sysFormat = null;
			collUser = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			entityId = null;
			currUser = null;
			userId = null;
			userName = null;

		}
	}

	/**
	 * this method is used to display the log details.
	 *
	 * @return
	 * @throws SwtException
	 */
	public String showDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Local variable declaration */
		// Variable to hold userId
		String userId = null;
		// Variable to hold userName
		String userName = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold sysFormat
		SystemFormats sysFormat = null;
		// Variable to hold
		String fromDateAsString = null;
		// Variable to hold
		String toDateAsString = null;
		// Variable to hold CDM
		CommonDataManager CDM = null;
		// Variable to hold pSummary
		PageDetails pSummary = null;
		// Variable to hold auditLogList
		List auditLogList = null;
		// Variable to hold pageSummaryList
		ArrayList pageSummaryList = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currUser
		User currUser = null;
		// Variable to hold currentPage
		int currentPage = 0;
		// Variable to hold initialPageCount
		int initialPageCount = 0;
		// Variable to hold maxPage
		int maxPage = 0;
		// Variable to hold totalcount
		int totalcount = 0;
		// Variable to hold isNext
		boolean isNext = false;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold currentSort
		String currentSort = null;
		// Variable to hold filterSortStatus
		String filterSortStatus = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String dateFormat= null;
		Date fromDate= null;
		Date toDate= null;
		String systemDate= null;
		String selectedFromDate= null;
		String selectedToDate= null;
		try {
			log.debug(this.getClass().getName() + " - [showDetails()] - "
					+ "Entry");
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			// Getting systemDate from the request
			systemDate = putSystemDateInRequest(request);
			currentPage = 1;
			// creating the CommonDataManager instance
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			// Getting currentuser
			currUser = (User) CDM.getUser();
			// Getting userId
			userId = currUser.getId().getUserId();
			// Getting userName
			userName = currUser.getUserName();
			// Put hostId in request
			hostId = putHostIdListInReq(request);
			// Getting sysformat from the session
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());

			// Getting currentFilter from the request
			currentFilter = request.getParameter("selectedFilter");

			/* gets the sort column specified */
			currentSort = request.getParameter("selectedSort");
			// Checking the current filter
			if (currentFilter == null) {
				currentFilter = "all";
			}

			if (currentSort == null) {
				currentSort = "none";
			}

			if (currentSort.equalsIgnoreCase("none")) {
				currentSort = "0|false"; // default sorting column
			}
			// Setting currentFilter & currentSort
			filterSortStatus = currentFilter + "," + currentSort;

			// creating Page Details
			pSummary = new PageDetails();
			// Creating new Arraylist for auditloglist
			auditLogList = new ArrayList();

			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			// Creating new Arraylist for pageSummaryList
			pageSummaryList = new ArrayList();
			// Getting entityId from the session
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			if (!SwtUtil.isEmptyOrNull(request.getParameter("userId")))  {
				userId =  request.getParameter("userId");
			}else{
				userId="All";
			}

			if (!SwtUtil.isEmptyOrNull(request.getParameter("currentPage") )) {
				currentPage =  Integer.parseInt(request.getParameter("currentPage"));
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("fromDate")))  {
				selectedFromDate= request.getParameter("fromDate");
				fromDate =  SwtUtil.parseDate(selectedFromDate, sysFormat.getDateFormatValue());
			}else{
				fromDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("toDate"))) {
				selectedToDate= request.getParameter("toDate");
				toDate =  SwtUtil.parseDate(selectedToDate,  sysFormat.getDateFormatValue());
			}else{
				toDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			// To pass the parameter flag "YES" to the total count for Movement
			// Audit Log
			totalcount = auditLogManager.getAuditLogList(hostId, userId,
					fromDate, toDate, currentPage,
					initialPageCount, auditLogList, filterSortStatus,
					SwtConstants.NO, "M", sysFormat, dbLink, entityId);

			// Getting pagesize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// Value count for max page size
			maxPage = ((totalcount % pageSize) > 0) ? ((totalcount / pageSize) + 1)
					: (totalcount / pageSize);
			// Set totalcount lesstha or equal to 100
			if (totalcount <= 100) {
				currentPage = 1;
			}
			// Creating new Arraylist for pageSummaryList as PageDetails generic
			pageSummaryList = new ArrayList<PageDetails>();
			// setting setCurrentPageNo
			pSummary.setCurrentPageNo(currentPage);
			// setting setMaxPages
			pSummary.setMaxPages(maxPage);
			// setting total count
			pSummary.setTotalCount(totalcount);
			// Adding into pageSummaryList
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1) {
				isNext = true;
			}
			// Sets the lastRefTime in request object
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			// set auditlog dyform
			setAuditLog(auditLog);
			// Sets the userId in request object
			request.setAttribute("userId", userId);
			// Sets the userName in request object
			request.setAttribute("userName", userName);
			// Sets the auditLog in request object
			request.setAttribute("auditLog", "auditLog");
			// Sets the currentPage in request object
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// Sets the prevEnabled in request object
			request.setAttribute("prevEnabled", "false");
			// Sets the nextEnabled in request object
			request.setAttribute("nextEnabled", "" + isNext);
			// Sets the maxPage in request object
			request.setAttribute("maxPage", Integer.toString(maxPage));
			// Sets the selectedFilter in request object
			request.setAttribute("selectedFilter", currentFilter);
			// Sets the selectedSort in request object
			request.setAttribute("selectedSort", currentSort);
			// Sets the pageSummaryList in request object
			request.setAttribute("pageSummaryList", pageSummaryList);
			// Sets the auditLogList in request object
			request.setAttribute("auditLogList", auditLogList);
			// Sets the fromDate in request object
			request.setAttribute("fromDate", fromDateAsString);
			// Sets the toDate in request object
			request.setAttribute("toDate", toDateAsString);
			// Sets the totalCount in request object
			request.setAttribute("totalCount", "" + totalcount);

			//Angular part

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.AUDIT_LOG_DATA);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", "entityId");
			responseConstructor.createRowElement("fromDate", selectedFromDate);
			responseConstructor.createRowElement("toDate", selectedToDate);
			responseConstructor.createRowElement("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement("totalCount",	totalcount);
			responseConstructor.createElement("defaultUserId",	userId);
			responseConstructor.createElement("defaultUserName", userName);

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			/******* UserLogList ******/
			responseConstructor.formGridStart("grid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalcount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)
			responseConstructor.formRowsStart(auditLogList.size());
			for (Iterator<AuditLog> it = auditLogList.iterator(); it.hasNext();) {
				// Obtain error log record from iterator
				AuditLog auditLogRecord = (AuditLog) it.next();
				responseConstructor.formRowStart();
				String dateFormatted = SwtUtil.formatDate(auditLogRecord.getLogDate_Date(), SwtUtil.getCurrentDateFormat(request.getSession()));
				responseConstructor.createRowElement(SwtConstants.USER_LOG_DATE_TAGNAME,auditLogRecord.getLogDate_Date());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_TIME_TAGNAME, auditLogRecord.getLogDate_Time());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_USER_ID_TAGNAME, auditLogRecord.getId().getUserId());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_TAGNAME, auditLogRecord.getId().getReference());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_ID_TAGNAME, auditLogRecord.getId().getReferenceId());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ACTION_TAGNAME, auditLogRecord.getId().getAction());
				//hidden property

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.AUDIT_LOG_DATA);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [showDetails()] - "
					+ "Exit");
			return getView("data");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ e.getMessage());
			SystemExceptionHandler.logError(e);

			return getView("fail");
		} finally {
			/* null the objects created already. */
			userId = null;
			userName = null;
			hostId = null;
			sysFormat = null;
			fromDateAsString = null;
			toDateAsString = null;
// To remove: 			dyForm = null;
			auditLog = null;
			CDM = null;
			pSummary = null;
			auditLogList = null;
			pageSummaryList = null;
			entityId = null;
			currUser = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String viewDetail()
			throws SwtException {

		try {
			log.debug(this.getClass().getName() + " - [viewDetail()] - "
					+ "Entry");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			AuditLog auditLog = (AuditLog) (getAuditLog());
			String referenceId = request.getParameter("referenceId");
			String userId = request.getParameter("selectedUserId");
			String reference = request.getParameter("selectedReference");
			String selectedDate = request.getParameter("selectedDate");
			String selectedTime = request.getParameter("selectedTime");
			String selectedAction = request.getParameter("selectedAction");
			String concatDateTime = selectedDate + " " + selectedTime;
			SystemFormats sysformat = SwtUtil.getCurrentSystemFormats(request
					.getSession());
			String dateFormat = sysformat.getDateFormatValue();
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat
					+ " HH:mm:ss");
			Date date = new Date();

			try {
				date = sdf.parse(concatDateTime);
			} catch (ParseException parExp) {
			}

			String hostId = putHostIdListInReq(request);

			String entityId = SwtUtil
					.getUserCurrentEntity(request.getSession());
			Collection auditLogColl = auditLogManager.getRefIdDetail(hostId,
					userId, reference, referenceId, date, selectedAction,
					entityId);

			setAuditLog(auditLog);
			request.setAttribute("auditLogList", auditLogColl);
			log.debug(this.getClass().getName() + " - [viewDetail()] - "
					+ "Exit");

			return getView("viewDetail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewDetail] method : - "
					+ e.getMessage());
			SystemExceptionHandler.logError(e);

			return getView("fail");
		}
	}


	public String displayAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String fromDateAsString = null;
		// Variable to hold toDateAsString
		String toDateAsString = null;
		// Variable to hold currentFilter
		String currentFilter = null;
		// Variable to hold currentSort
		String currentSort = null;
		// Variable to hold pageSize
		int pageSize = 0;
		// Variable to hold CDM
		CommonDataManager CDM = null;
		// Variable to hold auditLogList
		ArrayList auditLogList = null;
		// Variable to hold pSummary
		PageDetails pSummary = null;
		// Variable to hold pageSummaryList
		ArrayList pageSummaryList = null;
		// Variable to hold currUser
		User currUser = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold userName
		String userName = null;
		// Variable to hold systemDate
		String systemDate = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold sysformat
		SystemFormats sysFormat = null;
		// Variable to hold filterSortStatus
		String filterSortStatus = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currentPage
		int currentPage = 0;
		// Variable to hold initialPageCount
		int initialPageCount = 0;
		// Variable to hold isNext
		boolean isNext = false;
		// Variable to hold maxPage
		int maxPage = 0;
		// Variable to hold totalcount
		int totalcount = 0;
		// Variable to hold archiveDAO
		ArchiveDAOHibernate archiveDAO = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String dateFormat= null;
		Date fromDate= null;
		Date toDate= null;
		String selectedFromDate= null;
		String selectedToDate= null;
		try {

			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			currentPage = 1;
			// Getting currentuser
			currUser = (User) CDM.getUser();
			// Getting userId
			userId = currUser.getId().getUserId();
			// Getting userName
			userName = currUser.getUserName();
			// Put systemDate in request
			systemDate = putSystemDateInRequest(request);
			// Put HostId in request
			hostId = putHostIdListInReq(request);
			// Getting sysformat from the session
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());

			// Creating new Arraylist for auditLogList
			auditLogList = new ArrayList();
			// Getting currentFilter from the request
			currentFilter = request.getParameter("selectedFilter");
			// Getting currentSort from the request
			currentSort = request.getParameter("selectedSort");
			// Checking the current filter
			if (currentFilter == null) {
				currentFilter = "all";
			}

			if (currentSort == null) {
				currentSort = "none";
			}

			if ("none".equals(currentSort)) {
				currentSort = "0|false"; // default sorting column
			}
			// Setting currentFilter & currentSort
			filterSortStatus = currentFilter + "," + currentSort;
			// Getting entity Id from the session
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			/*
			 * Code modified by Chidambaranathan for Mantis 1484 for newly added
			 * fields on 24-June-2011
			 */
			// To pass the parameter flag "YES" to the total count for Movement
			// Audit Log

			// Getting entity Id from the session
			entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			if (!SwtUtil.isEmptyOrNull(request.getParameter("currentPage") )) {
				currentPage =  Integer.parseInt(request.getParameter("currentPage"));
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("fromDate")))  {
				selectedFromDate= request.getParameter("fromDate");
				fromDate =  SwtUtil.parseDate(selectedFromDate, sysFormat.getDateFormatValue());
			}else{
				selectedFromDate= systemDate;
				fromDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			if (!SwtUtil.isEmptyOrNull(request.getParameter("toDate"))) {
				selectedToDate= request.getParameter("toDate");
				toDate =  SwtUtil.parseDate(selectedToDate,  sysFormat.getDateFormatValue());
			}else{
				selectedToDate= systemDate;
				toDate =  SwtUtil.parseDate(systemDate, sysFormat.getDateFormatValue());
			}
			totalcount = auditLogManager.getAuditLogList(hostId, userId,
					fromDate, toDate, currentPage,
					initialPageCount, auditLogList, filterSortStatus,
					SwtConstants.NO, "M", sysFormat, dbLink, entityId);
			// Getting pagesize
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			// Value count for max page size
			maxPage = ((totalcount % pageSize) > 0) ? ((totalcount / pageSize) + 1)
					: (totalcount / pageSize);
			// creating Page Details
			pSummary = new PageDetails();
			/*
			 * pageSummaryList contains the Page Link (only max 10 Pages are
			 * allowed at one time) and details shown on the screen
			 */
			// Creating arraylist for pageSummaryList
			pageSummaryList = new ArrayList();
			pageSummaryList = new ArrayList<PageDetails>();
			// setting setCurrentPageNo
			pSummary.setCurrentPageNo(currentPage);
			// setting setMaxPages
			pSummary.setMaxPages(maxPage);
			// setting total count
			pSummary.setTotalCount(totalcount);
			// Adding into pageSummaryList
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1) {
				isNext = true;
			}

			// Sets the lastRefTime in request object
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			// Sets the userId in request object
			request.setAttribute("userId", userId);
			// Sets the userName in request object
			request.setAttribute("userName", userName);
			// Sets the auditLog in request object
			request.setAttribute("auditLog", "auditLog");
			// Sets the currentPage in request object
			request.setAttribute("currentPage", Integer.toString(currentPage));
			// Sets the prevEnabled in request object
			request.setAttribute("prevEnabled", "false");
			// Sets the nextEnabled in request object
			request.setAttribute("nextEnabled", "" + isNext);
			// Sets the maxPage in request object
			request.setAttribute("maxPage", Integer.toString(maxPage));
			// Sets the selectedFilter in request object
			request.setAttribute("selectedFilter", currentFilter);
			// Sets the selectedSort in request object
			request.setAttribute("selectedSort", currentSort);
			// Sets the pageSummaryList in request object
			request.setAttribute("pageSummaryList", pageSummaryList);
			// Sets the auditLogList in request object
			request.setAttribute("auditLogList", auditLogList);
			// Sets the fromDate in request object
			request.setAttribute("fromDate", systemDate);
			// Sets the toDate in request object
			request.setAttribute("toDate", systemDate);
			// Sets the totalCount in request object
			request.setAttribute("totalCount", "" + totalcount);

			//Angular part

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.AUDIT_LOG_DATA);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", entityId);
			responseConstructor.createRowElement("fromDate", selectedFromDate);
			responseConstructor.createRowElement("toDate", selectedToDate);
			responseConstructor.createRowElement("lastRefTime", SwtUtil.getLastRefTime(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement("totalCount",	totalcount);
			responseConstructor.createElement("defaultUserId",	userId);
			responseConstructor.createElement("defaultUserName", userName);

			xmlWriter.endElement(SwtConstants.SINGLETONS);


			/******* UserLogList ******/
			responseConstructor.formGridStart("grid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalcount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)
			responseConstructor.formRowsStart(auditLogList.size());
			for (Iterator<AuditLog> it = auditLogList.iterator(); it.hasNext();) {
				// Obtain error log record from iterator
				AuditLog auditLogRecord = (AuditLog) it.next();
				responseConstructor.formRowStart();
				String dateFormatted = SwtUtil.formatDate(auditLogRecord.getLogDate_Date(), SwtUtil.getCurrentDateFormat(request.getSession()));
				responseConstructor.createRowElement(SwtConstants.USER_LOG_DATE_TAGNAME,auditLogRecord.getLogDate_Date());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_TIME_TAGNAME, auditLogRecord.getLogDate_Time());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_TAGNAME, auditLogRecord.getId().getReference());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ITEM_ID_TAGNAME, auditLogRecord.getId().getReferenceId());
				responseConstructor.createRowElement(SwtConstants.USER_LOG_ACTION_TAGNAME, auditLogRecord.getId().getAction());
				//hidden property

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.AUDIT_LOG_DATA);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug("Exiting AuditLogAction.'display' method");
			return getView("data");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAngular()] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayAngular()] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayAngular", AuditLogAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullify the objects for already created
			sysFormat = null;
			systemDate = null;
			hostId = null;
			userId = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			entityId = null;
// To remove: 			dyForm = null;
			auditLog = null;
			pageSummaryList = null;
			auditLogList = null;
			pSummary = null;

		}

	}
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ " Entry");

		/* Method's local variable declaration */
		int maxPage;
		int remainder;
		/*
		 * The max page is calculate by the total number of records divided by
		 * the number of records per page
		 */
		maxPage = (totalCount) / (pageSize);
		/*
		 * Remainder is the modulo of the total number of records and the number
		 * of records per page
		 */
		remainder = totalCount % pageSize;

		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName() + " - [setMaxPageAttribute] - "
				+ "Exit");
		return maxPage;
	}

	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
											HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns

				width =   SwtConstants.USER_LOG_DATE_TAGNAME + "=90" + ","
						+ SwtConstants.USER_LOG_TIME_TAGNAME + "=80" + ","
						+ SwtConstants.USER_LOG_ITEM_TAGNAME + "=90" + ","
						+ SwtConstants.USER_LOG_ITEM_ID_TAGNAME + "=140"+ ","
						+ SwtConstants.USER_LOG_ACTION_TAGNAME + "=100";


			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder =
						SwtConstants.USER_LOG_DATE_TAGNAME + ","
								+ SwtConstants.USER_LOG_TIME_TAGNAME + ","
								+ SwtConstants.USER_LOG_ITEM_TAGNAME + ","
								+ SwtConstants.USER_LOG_ITEM_ID_TAGNAME + ","
								+ SwtConstants.USER_LOG_ACTION_TAGNAME;
			}

			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();



			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;

				// ERROR_DATE column
				if (order.equals(SwtConstants.USER_LOG_DATE_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_DATE_HEADING, request),
							SwtConstants.USER_LOG_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 0,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_DATE_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_DATE_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_DATE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// USER_ID column
				if (order.equals(SwtConstants.USER_LOG_TIME_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_TIME_HEADING, request),
							SwtConstants.USER_LOG_TIME_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 2,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_TIME_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_TIME_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_TIME_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// SOURCE column
				if (order.equals(SwtConstants.USER_LOG_ITEM_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_ITEM_HEADING, request),
							SwtConstants.USER_LOG_ITEM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_ITEM_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_ITEM_TAGNAME
					)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_ITEM_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// ERROR column
				if (order.equals(SwtConstants.USER_LOG_ITEM_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_ITEM_ID_HEADING, request),
							SwtConstants.USER_LOG_ITEM_ID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 4,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_ITEM_ID_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_ITEM_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_ITEM_ID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				if (order.equals(SwtConstants.USER_LOG_ACTION_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_ACTION_HEADING, request),
							SwtConstants.USER_LOG_ACTION_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_ACTION_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_ACTION_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_ACTION_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}



	private List<ColumnInfo> getUserViewLogGridColumns(String width, String columnOrder, String hiddenColumns,
													   HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME + "=200" + ","
						+ SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME + "=290"+ ","
						+ SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME + "=290" ;
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME + ","
						+ SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME + ","
						+ SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// Host Id column
				if (order.equals(SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_FIELD_HEADING, request),
							SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_VIEW_FIELD_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_FIELD_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Entity Id column
				if (order.equals(SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_CHANGED_FROM_HEADING, request),
							SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_VIEW_CHANGED_FROM_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_CHANGED_FROM_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Account Id column
				if (order.equals(SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_CHANGED_TO_HEADING, request),
							SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_LOG_VIEW_CHANGED_TO_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getViewLogGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getViewLogGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	public String openViewDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("selectedReferenceId", request.getParameter("selectedReferenceId"));
		request.setAttribute("selectedUserId", request.getParameter("selectedUserId"));
		request.setAttribute("selectedDate", request.getParameter("selectedDate"));
		request.setAttribute("selectedTime", request.getParameter("selectedTime"));
		request.setAttribute("selectedReference", request.getParameter("selectedReference"));
		request.setAttribute("selectedAction", request.getParameter("selectedAction"));

		return getView("viewDetail");
	}
}