package org.swallow.control.web;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.struts.ActionErrors;

import org.swallow.util.struts.ActionMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.control.model.Section;
import org.swallow.control.service.SectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;




/**
 *
 * This is action class for section screen.
 *
 */

import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/section", "/section.do"})
public class SectionAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/control/sectionmaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/control/sectionmaintenance");
		viewMap.put("change", "jsp/control/sectionmaintenanceadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	@Autowired
	private SectionManager sectionManager = null;
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(SectionAction.class);

	/**
	 * @param sectionManager
	 */
	public void setSectionManager(SectionManager sectionManager) {
		this.sectionManager = sectionManager;
	}

	Section section;
	public Section getSection() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		section = RequestObjectMapper.getObjectFromRequest(Section.class, request);
		return section;
	}

	public void setSection(Section section) {
		this.section = section;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("section", section);
	}


	private Collection<Section> sectionColl;
	public Collection<Section> getSectionColl() {
		return sectionColl;
	}

	public void setSectionColl(Collection<Section> sectionColl) {
		this.sectionColl = sectionColl;
	}


	String method;
	public String getMethod() {
		return method;
	}
	public void setMethod(String method) {
		this.method = method;
	}

	/**
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 */
//	@Override
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "save":
				return save();
			case "add":
				return add();
			case "change":
				return change();
			case "update":
				return update();
			case "delete":
				return delete();
		}


		return displayList();
	}

	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putHostIdListInReq' method");
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		log.debug("exiting 'putHostIdListInReq' method");
		return hostId;

	}

	/**
	 * @return
	 * @throws Exception
	 */
	public String displayList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		Section section = null;
		try {
			log.debug("entering 'displayList' method");
			String hostId = putHostIdListInReq(request);
			section = getSection();
			section.getId().setHostId(hostId);
			log.debug("Debugging Value >> " + hostId);
			Collection collSec = sectionManager.getSectionList(hostId);
			log.debug("entering 'displayList' method");
			Iterator itr = collSec.iterator();

			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE);
			request.setAttribute("sectionColl", collSec);
			sectionColl = collSec;
//			System.err.println(sectionColl);
			log.debug("exiting 'displayList' method");
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			return getView("success");
		} catch (SwtException swtexp) {
			log.debug("SwtException Catch in SectionAction.'displayList' method : "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception Catch in SectionAction.'displayList' method : "
					+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displayList", SectionAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to save the section in database
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public String save()
			throws SwtException {
		/*Methods local variable declaration*/
		ActionErrors errors =null;
		SystemInfo systemInfo=null;

		String userId=null;
		String hostId=null;
		Section section =null;
		String SectionId=null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			errors = new ActionErrors();
			/*Read host id from swtutil file*/
			hostId = SwtUtil.getCurrentHostId();
			section = getSection();
			/*Gertting and setting hostid using bean class*/
			SectionId = section.getId().getSectionId();
			section.getId().setHostId(hostId);
			systemInfo = new SystemInfo();
			/*read the userid from swtutil file*/
			userId = SwtUtil.getCurrentUserId(request.getSession());
			section.setUpdateUser(userId);
			/*Setting ipaddress using bean class*/
			systemInfo.setIpAddress(request.getRemoteAddr());
			/*Save the section details by calling manager class*/
			sectionManager.saveSectionDetail(section, systemInfo);
			section = new Section();
			this.setSection(section);
			request.setAttribute("methodName", "save");
			/*Put the section list in request*/
			putSectionList();
			request.setAttribute("methodName", "displayList");
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [save] - Exiting ");
			return getView("add");

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 *  : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			return getView("fail");
		}finally
		{
			SectionId=null;
		}
	}


	/**
	 * @return
	 * @throws Exception
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("entering 'add' method");
			Section section = new Section();
			this.setSection(section);;
			request.setAttribute("methodName", "add");
			log.debug("exiting 'add' method");
			return getView("add");
		} catch (Exception e) {
			log.debug("Exception Catch in SectionAction.'add' method : "
					+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "add", SectionAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws Exception
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		Section section = null;
		try {

			log.debug("entering 'change' method");
			request.setAttribute("screenFieldsStatus", "true");
			String sectionId = request.getParameter("sectionId");
			String sectionName = request.getParameter("sectionName");
			String hostId = putHostIdListInReq(request);
			section = getSection();
			section.getId().setHostId(hostId);
			section.getId().setSectionId(sectionId);
			section.setSectionName(sectionName);
			SystemInfo systemInfo = new SystemInfo();
			String oldVal = new StringBuffer("Section-Name=").append(
					section.getSectionName()).toString();
			this.setSection(section);
			request.setAttribute("methodName", "change");
			request.setAttribute("oldValue", oldVal);
			log.debug("exiting 'change' method");
			return getView("change");
		} catch (SwtException swtexp) {
			log.debug("SwtException Catch in SectionAction.'change' method : "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug("Exception Catch");
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "change", SectionAction.class), request, "");
			return getView("fail");
		}
	}
	/**
	 * @return
	 * @throws Exception
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ActionErrors errors = new ActionErrors();
		SystemInfo systemInfo = new SystemInfo();
		Section section = null;
		try {
			log.debug("entering 'update' method");

			String oldValue = request.getParameter("oldValue");
			section = getSection();
			String hostId = putHostIdListInReq(request);
			section.getId().setHostId(hostId);
			String newValue = new StringBuffer("Section-Name=").append(
					section.getSectionName()).toString();
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			section.setUpdateUser(userId);
			systemInfo.setIpAddress(request.getRemoteAddr());
			systemInfo.setOldLogString(oldValue);
			systemInfo.setNewLogString(newValue);
			sectionManager.updateSectionDetail(section, systemInfo);
			section = new Section();
			this.setSection(section);;
			putSectionList();
			request.setAttribute("methodName", "displayList");
			request.setAttribute("parentFormRefresh", "yes");
			log.debug("exiting 'update' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log.debug("SwtException Catch in SectionAction.'update' method : "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");

		} catch (Exception e) {
			log.debug("Exception Catch in SectionAction.'update' method : "
					+ e.getMessage());
			e.printStackTrace();
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "update",
							SectionAction.class), request, ""));
			return getView("fail");
		}
	}
	/**
	 * @return
	 * @throws Exception
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ActionErrors errors = new ActionErrors();
		Section section = null;
		try {
			log.debug("entering 'delete' method");
			section = getSection();
			String entityId = request.getParameter("entityCode");

			SystemInfo systemInfo = new SystemInfo();
			systemInfo.setIpAddress(request.getRemoteAddr());
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			section.setUpdateUser(userId);
			String sectionId = request.getParameter("selectedSectionId");
			String sectionName = request.getParameter("selectedSectionName");
			String hostId = putHostIdListInReq(request);
			section.getId().setHostId(hostId);
			section.getId().setSectionId(sectionId);
			section.setSectionName(sectionName);
			sectionManager.deleteSectionDetail(section, systemInfo);
			putSectionList();
			section = new Section();
			this.setSection(section);;

			request.setAttribute("methodName", "delete");
			Collection collSec = sectionManager.getSectionList(hostId);
			request.setAttribute("sectionColl", collSec);
			log.debug("exiting 'delete' method");
			return displayList();
		} catch (SwtException swtexp) {
			log.debug("SwtException Catch in SectionAction.'delete' method : "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayList();
		} catch (Exception e) {
			log.debug("Exception Catch in SectionAction.'delete' method : "
					+ e.getMessage());
			e.printStackTrace();
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "delete",
							SectionAction.class), request, ""));
			return getView("fail");
		}
	}

	/**

	 * @throws Exception
	 */
	public void putSectionList()
			throws Exception {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		Section section = null;
		try {
			log.debug("entering 'putSectionList' method");
			String hostId = putHostIdListInReq(request);
			section = this.getSection();
			section = getSection();
			section.getId().setHostId(hostId);
			log.debug("Debugging Value >> " + hostId);
			Collection collSec = sectionManager.getSectionList(hostId);
			log.debug("entering 'displayList' method");
			Iterator itr = collSec.iterator();

			while (itr.hasNext()) {
				section = (Section) (itr.next());
			}

			request.setAttribute("sectionColl", collSec);
			log.debug("exiting 'putSectionList' method");

		} catch (Exception e) {
			log.debug("Exception Catch");
			e.printStackTrace();
			SystemExceptionHandler.logError(e);

		}
	}

	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String cancelStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
	}

}