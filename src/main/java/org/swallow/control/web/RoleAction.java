/*
 * @(#)RoleAction.java 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * This is action class for Role screen
 *
 */
package org.swallow.control.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.Map;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.json.JSONArray;
import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.AccountAccessDetails;
import org.swallow.control.model.CurrencyGroupAccess;
import org.swallow.control.model.CurrencyGroupAccessGui;
import org.swallow.control.model.EntityAccess;
import org.swallow.control.model.EntityAccessGui;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.model.FacilityAccess;
import org.swallow.control.model.LocationAccessGui;
import org.swallow.control.model.MenuAccessOptionsGui;
import org.swallow.control.model.Role;
import org.swallow.control.model.RoleBasedControl;
import org.swallow.control.model.SweepLimits;
import org.swallow.control.model.WorkQAccess;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.control.service.ErrorLogManager;
import org.swallow.control.service.RoleManager;
import org.swallow.control.service.SweepLimitsManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.control.service.WorkQAccessManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.Location;
import org.swallow.maintenance.model.LocationAccess;
import org.swallow.maintenance.service.LocationMaintenanceManager;
import org.swallow.model.MenuAccess;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;






import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/role", "/role.do"})
public class RoleAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("success", "jsp/control/rolemaintenance");
		viewMap.put("view", "jsp/control/rolemaintenanceadd");
		viewMap.put("add", "jsp/control/rolemaintenanceadd");
		viewMap.put("change", "jsp/control/rolemaintenanceadd");
		viewMap.put("menuAccessOptions", "jsp/control/menuaccessoptions");
		viewMap.put("roleEntityAccessList", "jsp/control/entityaccesslist");
		viewMap.put("ccyGrpAccess", "jsp/control/currencygroupaccess");
		viewMap.put("locationDetails", "jsp/control/locationaccess");
		viewMap.put("accountAccess", "jsp/control/accountAccess");
		viewMap.put("workQAccess", "jsp/control/workqueueaccess");
		viewMap.put("sweepLimit", "jsp/control/sweepinglimits");
		viewMap.put("copyFrom", "jsp/control/copyfromrole");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("roleBasedControl", "jsp/control/rolebasedcontrolflex");
		viewMap.put("fail", "error");
		viewMap.put("rolebasedcontroldata", "jsp/control/rolebasedcontrolflexdata");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	private Role role;
	public Role getRole() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		role = RequestObjectMapper.getObjectFromRequest(Role.class, request);
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("role", role);
	}
	private CurrencyGroup currencyGroup;
	public CurrencyGroup getCurrencyGroup() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		currencyGroup = RequestObjectMapper.getObjectFromRequest(CurrencyGroup.class, request);
		return currencyGroup;
	}

	public void setCurrencyGroup(CurrencyGroup currencyGroup) {
		this.currencyGroup = currencyGroup;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("currencyGroup", currencyGroup);
	}
	private AccountAccess accountAccess;
	public AccountAccess getAccountAccess() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		accountAccess = RequestObjectMapper.getObjectFromRequest(AccountAccess.class, request);
		return accountAccess;
	}

	public void setAccountAccess(AccountAccess accountAccess) {
		this.accountAccess = accountAccess;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("accountAccess", accountAccess);
	}


	private LocationAccessGui locationAccess;
	public LocationAccessGui getLocationAccess() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		locationAccess = RequestObjectMapper.getObjectFromRequest(LocationAccessGui.class, request);
		return locationAccess;
	}


	public void setLocationAccess(LocationAccessGui locationAccess) {
		this.locationAccess = locationAccess;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("locationAccess", locationAccess);
	}


	// Creating instance of Log
	private final Log log = LogFactory.getLog(RoleAction.class);

	// Declare the ccyGrpAccessList1 to get the currency group access list
	private Collection ccyGrpAccessList1 = null;
	/* Creating and Setting instance of Role Manager */
	@Autowired
	private RoleManager roleMgr = null;

	public void setRoleManager(RoleManager roleManager) {
		this.roleMgr = roleManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "update":
				return update();
			case "view":
				return view();
			case "delete":
				return delete();
			case "menuAccessOptions":
				return menuAccessOptions();
			case "saveMenuAccessOptions":
				return saveMenuAccessOptions();
			case "entityAccessList":
				return entityAccessList();
			case "saveEntityAccessList":
				return saveEntityAccessList();
			case "copyFrom":
				return copyFrom();
			case "copy":
				return copy();
			case "cancel":
				return cancel();
			case "cancelFromParent":
				return cancelFromParent();
			case "saveCurrencyGroupAccessDetails":
				return saveCurrencyGroupAccessDetails();
			case "addLocationAccessList":
				return addLocationAccessList();
			case "changeLocationAccessList":
				return changeLocationAccessList();
			case "viewLocationAccessList":
				return viewLocationAccessList();
			case "saveLocationAccessList":
				return saveLocationAccessList();
			case "viewAccountAccess":
				return viewAccountAccess();
			case "saveAccountAccess":
				return saveAccountAccess();
			case "getRoleReport":
				return getRoleReport();
			case "roleBasedControl":
				return roleBasedControl();
			case "displayRoleBasedControl":
				return displayRoleBasedControl();
			case "saveFacilityAccessInSession":
				return saveFacilityAccessInSession();
			case "displayCurrencyGroupAccessDetails":
				return displayCurrencyGroupAccessDetails();
		}


		return unspecified();
	}

	/**
	 * Control first goes to this method of Action Class whenever no particular
	 * value is given to "methodName" variable This calls the dispaly() method
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering 'unspecified' method");

			// Setting different flags to there initial values
			request.getSession().setAttribute("menuAccessOptionsFlag", "0"); // 0-
			// if
			// entering
			// first
			// time
			// otherwise
			// 1
			request.getSession().setAttribute("entityAccessListFlag", "0"); // 0-
			// if
			// entering
			// first
			// time
			// otherwise
			// 1
			request.getSession().setAttribute("workQAccessFlag", "0");

			request.getSession().setAttribute("copyFromFlag", "false");

			request.getSession().setAttribute("listAccessUpdateInSession", null);

			request.getSession().setAttribute("listAccessAddInSession", null);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			log.debug("Exiting 'unspecified' method");

			return display();
		} catch (SwtException e) {
			log.debug("Exception in RoleAction.unspecified" + e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);

			return getView("fail");
		}
	}

	/**
	 * This Function populates the Role Details grid which is present on the
	 * parent Screen
	 *
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering 'display' Method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = new Role();
			setRole(role);

			// Getting Role Details from Role Manager
			Collection collRole = roleMgr.getRoleDetails(role);
			request.setAttribute("RoleDetailsList", collRole);
			// Setting status of the buttons according to the default entity
			putDefaultEntityAccessInReq(request);

			log.debug("Exiting 'display' Method");
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			request.setAttribute("accessAltered", request
					.getParameter("accessAltered"));
			return getView("success");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This Function opens the Add Role pop-Up
	 *
	 * @return
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			// Setting status of various buttons
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = (Role) getRole();

			/*
			 * "roleMaintenanceOperation" ==>This flag stores the name of the
			 * operation taking place which could be "add", "change", "copyFrom"
			 */
			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");


			if ((roleMaintenanceOperation == null)
					|| (roleMaintenanceOperation.equals("add"))) {
				request.getSession().setAttribute("roleMaintenanceOperation",
						"add");
				role.setRestrictLocations("Y");
				role.setAllEntityOption("N");

				/*variable declared for setting & getting the values from the screen & database*/
				role.setAdvancedUser("Y");
				request.setAttribute("methodName", "save");
			} else if (roleMaintenanceOperation.equals("change")) {
				request.setAttribute("methodName", "update");
			}

			String roleIdInSession = (String) request.getSession()
					.getAttribute("roleIdInSession");

			if (roleIdInSession != null) {
				role.setRoleId(roleIdInSession);

				if ((roleMaintenanceOperation != null)
						&& roleMaintenanceOperation.equals("copyFrom")) {
					request.setAttribute("methodName", "update");
				}
			} else if ((roleMaintenanceOperation != null)
					&& roleMaintenanceOperation.equals("copyFrom")) {
				request.setAttribute("methodName", "save");
			}
			/*
			 * "sessionWorkQAccessDetails" ==>This is a collection which holds
			 * the details of the WorkQAccess Corresponding to a particular
			 * role. Initially it is set to null.
			 */
			Collection workQAccessDetails = new ArrayList();
			workQAccessDetails = (Collection) request.getSession()
					.getAttribute("sessionWorkQAccessDetails");

			if (workQAccessDetails != null) {
				request.getSession().setAttribute("sessionWorkQAccessDetails",
						workQAccessDetails);
			}

			/*
			 * "saveEnableMenu"==>This flag controls the status of "save" button
			 * on the Add Role pop up "saveEnableEntity"==>This flag controls
			 * the status of "save" & "workQAccess" button on the Add Role pop
			 * up.
			 */
			String saveEnableMenu = (String) request.getSession().getAttribute(
					"saveEnableMenu");
			String saveEnableEntity = (String) request.getSession()
					.getAttribute("saveEnableEntity");
			if ((saveEnableMenu == null) && (saveEnableEntity == null)) {
				/*
				 * This attribute is set to true only when at least one menu item is
				 * selected in the Menu Access Options screen*/
				request.getSession().setAttribute("saveEnableMenu", "GBP");
				/*
				 * This attribute is set to true only when at least one entity is
				 * selected either for full or read access in the Entity Access List screen*/
			}

			if ((saveEnableMenu != null)
					&& saveEnableMenu.equals(SwtConstants.STR_TRUE)) {
				request.setAttribute("saveEnableMenu", SwtConstants.STR_TRUE);
			}

			if ((saveEnableEntity != null)
					&& saveEnableEntity.equals(SwtConstants.STR_TRUE)) {
				request.setAttribute("saveEnableEntity", SwtConstants.STR_TRUE);
			}

			putDefaultEntityAccessInReq(request);
			putSweepButtonStatusInReq(request);
			request.getSession().setAttribute("Operation","add");
			setRole(role);
			log.debug("Exiting 'add' Method");

			return getView("add");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to save the newly added records in database.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/*Methods local variable declaration*/
		ActionMessages errors=null;
// To remove: 		DynaValidatorForm dyForm = null;
		Role role = null;
		ArrayList colMenuAccess= null;
		ArrayList colEntityAccess= null;
		ArrayList colCcyGrpAccess=null;
		String roleId=null;
		ArrayList colSweepLimits =null;
		CacheManager cacheManagerInst=null;
		SystemInfo systemInfo = null;
		String copiedRoleId=null;
		String accountAccess=null;
		String radio1=null;
		String radio2=null;
		String radio3=null;
		String hostId=null;
		String userId=null;


		Collection coll=null;
		Collection ccyGrpAccessList=null;
		Collection entityAccessList=null;
		Collection locationAccessDetailsList=null;
		Collection sessionSweepLimitDetails=null;
		Collection newLocationAccessList=null;
		Collection accountAccessDetailsList=null;

		ArrayList<FacilityAccess> facilityAccessList=null;
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			errors = new ActionMessages();
// To remove: 			dyForm = (DynaValidatorForm) form;
			role = (Role) getRole();

			colMenuAccess = new ArrayList();
			colEntityAccess = new ArrayList();
			colCcyGrpAccess = new ArrayList();
			colSweepLimits = new ArrayList();
			roleId = role.getRoleId();
			cacheManagerInst = CacheManager.getInstance();
			/*Read the hostid from swtutil file*/
			hostId = SwtUtil.getCurrentHostId();
			/*Setting host id using bean class*/
			role.setHostId(hostId);
			/*Read the copied role id from request parameter*/
			copiedRoleId = request.getParameter("copiedRoleId");
			/*Read the account access control*/
			accountAccess= role.getAccountAccessControl();
			/*Condition to check the value for account access*/
			if (!copiedRoleId.trim().equals("")
					&& (accountAccess != null
					&& accountAccess.equalsIgnoreCase("Y"))){
				copyAccountAccess(roleId, copiedRoleId);
			}

			/*fetches the facility access details*/

			facilityAccessList= new ArrayList<FacilityAccess>();
			if(request.getSession().getAttribute("listAccessAddInSession")!=null) {
				facilityAccessList= (ArrayList<FacilityAccess>) request.getSession().getAttribute("listAccessAddInSession");
				if(facilityAccessList!=null) {
					roleMgr.saveRoleFacilityAccess(facilityAccessList,roleId);

				}
			}


			/*Fetches the menu item details and store in collection*/
			coll= extractMenuItemsForSave(request);
			/*Condition to check collection has value*/
			if (coll != null) {
				Iterator itrMenuItemId = coll.iterator();
				/*Loop to iterate the details*/
				while (itrMenuItemId.hasNext()) {

					MenuAccessOptionsGui menuAccessOptionsGui = (MenuAccessOptionsGui) (itrMenuItemId
							.next());

					MenuAccess menuAccess = new MenuAccess();
					menuAccess.getId().setItemId(
							menuAccessOptionsGui.getItemId());
					menuAccess.getId().setRoleId(roleId);
					menuAccess.getId().setDescription(
							menuAccessOptionsGui.getDescription());
					/*Condition to check the access rights*/
					if (menuAccessOptionsGui.getMenuAccessHTMLFullAccess()
							.equals("checked")) {
						menuAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_FULL_ACCESS));
					} else if (menuAccessOptionsGui
							.getMenuAccessHTMLViewAccess().equals("checked")) {
						menuAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_READ_ACCESS));
					}
					colMenuAccess.add(menuAccess);
				}
			}
			/*fetches the entity access details*/
			entityAccessList = (Collection) (request.getSession()
					.getAttribute("roleEntityAccessList"));
			/*Condition to check the collection has value*/
			if (entityAccessList != null) {
				Iterator itrEntityAccess = entityAccessList.iterator();
				/*Loop to iterate the details*/
				while (itrEntityAccess.hasNext()) {
					EntityAccessGui entityAccessGui = (EntityAccessGui) (itrEntityAccess
							.next());
					radio1 = entityAccessGui.getEntityAccessHTML1();
					radio2 = entityAccessGui.getEntityAccessHTML2();
					radio3 = entityAccessGui.getEntityAccessHTML3();
					if (radio1.equals("checked")) {
						EntityAccess entityAccess = new EntityAccess();
						/*Setting role id,entity id and role id using bean class*/
						entityAccess.getId().setHostId(hostId);
						entityAccess.getId().setRoleId(roleId);
						entityAccess.getId().setEntityId(
								entityAccessGui.getEntityId());
						entityAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_FULL_ACCESS));
						colEntityAccess.add(entityAccess);
					} else if (radio2.equals("checked")) {
						EntityAccess entityAccess = new EntityAccess();
						entityAccess.getId().setHostId(hostId);
						entityAccess.getId().setRoleId(roleId);
						entityAccess.getId().setEntityId(
								entityAccessGui.getEntityId());
						entityAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_READ_ACCESS));
						colEntityAccess.add(entityAccess);
					}
				}
			}
			ccyGrpAccessList = (Collection) (request.getSession()
					.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST));

			/*Iterating through the whole list and saving only those entries
			 for which Access is either 'Full' or 'View Only'*/
			if (ccyGrpAccessList != null) {
				Iterator itrCcyGrpAccess = ccyGrpAccessList.iterator();
				while (itrCcyGrpAccess.hasNext()) {
					CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itrCcyGrpAccess
							.next());
					radio1 = ccyGrpAccGui.getCcyGrpAccessHTML1();
					radio2 = ccyGrpAccGui.getCcyGrpAccessHTML2();
					radio3 = ccyGrpAccGui.getCcyGrpAccessHTML3();
					if (radio1.equals("checked")) {
						CurrencyGroupAccess ccyGrpAccess = new CurrencyGroupAccess();
						ccyGrpAccess.getId().setHostId(hostId);
						ccyGrpAccess.getId().setRoleId(roleId);
						ccyGrpAccess.getId().setEntityId(
								ccyGrpAccGui.getEntityId());
						ccyGrpAccess.getId().setCurrencyGroupId(
								ccyGrpAccGui.getCurrencyGroupId());
						ccyGrpAccess.setAccessId(String
								.valueOf(SwtConstants.CURRENCYGRP_FULL_ACCESS));
						colCcyGrpAccess.add(ccyGrpAccess);
					} else if (radio2.equals("checked")) {
						CurrencyGroupAccess ccyGrpAccess = new CurrencyGroupAccess();
						ccyGrpAccess.getId().setHostId(hostId);
						ccyGrpAccess.getId().setRoleId(roleId);
						ccyGrpAccess.getId().setEntityId(
								ccyGrpAccGui.getEntityId());
						ccyGrpAccess.getId().setCurrencyGroupId(
								ccyGrpAccGui.getCurrencyGroupId());
						ccyGrpAccess.setAccessId(String
								.valueOf(SwtConstants.CURRENCYGRP_READ_ACCESS));
						colCcyGrpAccess.add(ccyGrpAccess);
					}
				}
			}

			/* Saving Sweeping Detail*/
			sessionSweepLimitDetails = (Collection) request
					.getSession().getAttribute("sessionSweepLimitDetails");

			if (sessionSweepLimitDetails != null) {
				Iterator itr = sessionSweepLimitDetails.iterator();

				while (itr.hasNext()) {
					SweepLimits sweepLimits = (SweepLimits) (itr.next());
					/*Setting role id using bean class*/
					sweepLimits.getId().setRoleId(roleId);
					colSweepLimits.add(sweepLimits);
				}
			}
			locationAccessDetailsList = (Collection) (request
					.getSession()
					.getAttribute(SwtConstants.LOCATION_ACCESS_LIST));
			/*Saving account access details*/
			newLocationAccessList = new ArrayList();
			if (locationAccessDetailsList != null) {
				/*Loop to iterate the details*/
				Iterator itr = locationAccessDetailsList.iterator();
				while (itr.hasNext()) {
					LocationAccessGui locAccess = (LocationAccessGui) (itr
							.next());
					LocationAccess loc = new LocationAccess();

					if (locAccess.getLocAccess().equalsIgnoreCase("checked")) {
						loc.getId().setHostId(hostId);
						loc.getId().setRoleId(roleId);
						loc.getId().setEntityId(locAccess.getEntityId());
						loc.getId().setLocationId(locAccess.getLocationId());

						newLocationAccessList.add(loc);
					}

				}
			}
			/*Condition to check restrict location is empty and null*/
			if (role.getRestrictLocations() == null
					|| role.getRestrictLocations().equalsIgnoreCase("")) {
				/*Setting value as "N" using bean class*/
				role.setRestrictLocations("N");
			} else if (role.getRestrictLocations().equalsIgnoreCase("Y")) {
				/*Setting value as "Y" using bean class*/
				role.setRestrictLocations("Y");
			}
			/* Condition to check account access value is null */
			if (role.getAccountAccessControl() == null
					|| role.getAccountAccessControl().equals("")) {
				/* Setting account access value as "N" using bean class */
				role.setAccountAccessControl("N");
			}
			/*Condition to check entity option and set the value using bean class*/
			if (role.getAllEntityOption() == null
					|| role.getAllEntityOption().equalsIgnoreCase("")) {
				role.setAllEntityOption("N");
			} else if (role.getAllEntityOption().equalsIgnoreCase("Y")) {
				role.setAllEntityOption("Y");
			}

			/*
			 * Code  for advanced user configuration retrieving
			 * from database
			 */

			if (role.getAdvancedUser() == null
					|| role.getAdvancedUser().equalsIgnoreCase("")) {
				role.setAdvancedUser("N");
			} else if (role.getAdvancedUser().equalsIgnoreCase("Y")) {
				role.setAdvancedUser("Y");
			}

			if (role.getInputInterruption() == null
					|| role.getInputInterruption().equals("")) {
				role.setInputInterruption("N");
			} else if (role.getInputInterruption().equalsIgnoreCase("Y")) {
				role.setInputInterruption("Y");
			}

			systemInfo = new SystemInfo();
			roleMgr.saveRoleWithOtherdetails(role, colMenuAccess,
					colEntityAccess, colCcyGrpAccess, colSweepLimits,
					newLocationAccessList, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			/* Read the host id from cache manager class */
			hostId = SwtUtil.getCurrentHostId();
			/* Read account Access Manager file */
			AccountAccessManager accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			/* Retrieve account access details */
			accountAccessDetailsList = (Collection) (request
					.getSession().getAttribute("roleAccountAccessList"));
			/*Read the current user id from swtutil file*/
			userId=SwtUtil.getCurrentUserId(request.getSession());
			if (accountAccessDetailsList != null && accountAccessDetailsList.size() > 0)
				accountAccessManager.saveAccountAccessControlDetails(
						accountAccessDetailsList,userId );

			request.getSession().setAttribute("itemDescList", new ArrayList());
			request.getSession().setAttribute("roleEntityAccessList",
					new ArrayList());
			request.getSession().setAttribute("sessionWorkQAccessDetails",
					new ArrayList());

			request.getSession().setAttribute("listAccessUpdateInSession", null);

			request.getSession().setAttribute("listAccessAddInSession", null);
			setRole(role);
			request.setAttribute("methodName", "save");
			request.setAttribute("parentFormRefresh", "yes");
			clearSessionVariablesForRole(request);
			putDefaultEntityAccessInReq(request);
			log.debug(this.getClass().getName() + "- [save] - Exiting ");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				ActionMessages error1 = new ActionMessages();
				error1.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, error1);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			if (!swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")
					&& (swtexp.getErrorLogFlag().equals("Y"))) {
				/*
				 * End : Modified for Mantis 1366-Remove entry to log if
				 * duplicate records added by betcy on 18-02-2011
				 */


				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());

				errorLogManager.logError(errorLog);
			}
			if (errors != null) {
				errors.add("entityId", new ActionMessage(swtexp.getErrorCode()));
			}
			if (role != null) {
				setRole(role);
			}
			request.setAttribute("methodName", "save");
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			request.getSession().setAttribute("saveEnableMenu",
					SwtConstants.STR_TRUE);
			request.getSession().setAttribute("saveEnableEntity",
					SwtConstants.STR_TRUE);
			request.setAttribute("saveEnableMenu", SwtConstants.STR_TRUE);
			request.setAttribute("saveEnableEntity", SwtConstants.STR_TRUE);


			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ e.getMessage());
			clearSessionVariablesForRole(request);
			saveErrors(request, SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "save",
							RoleAction.class), request, ""));
			return getView("fail ");
		}

	}

	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {

			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = (Role) getRole();

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if ((roleMaintenanceOperation == null)
					|| roleMaintenanceOperation.equals("change")) {
				request.getSession().setAttribute("roleMaintenanceOperation",
						"change");
			}

			String roleId = request.getParameter("selectedRoleId");
			role.setRoleId(roleId);
			request.getSession().setAttribute("roleIdInSession", roleId);

			Collection coll = (Collection) roleMgr.getRoleDetails(role);
			role = (Role) (coll.iterator().next());

			String restrictLocations = role.getRestrictLocations();
			if (restrictLocations.equalsIgnoreCase("Y")) {
				role.setRestrictLocations("Y");

			} else {
				role.setRestrictLocations("N");
			}
			String allEntityOption = role.getAllEntityOption();
			log.debug("role.getAllEntityOption()" + allEntityOption);
			if (allEntityOption.equalsIgnoreCase("Y")) {
				role.setAllEntityOption("Y");

			} else {
				role.setAllEntityOption("N");
			}
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyIlmScenario()))
				role.setMaintainAnyIlmScenario("N");
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyIlmGroup()))
				role.setMaintainAnyIlmGroup("N");
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyReportHist()))
				role.setMaintainAnyReportHist("N");

			//Mantis 6891
			if (SwtUtil.isEmptyOrNull(role.getInputInterruptionAlertType()))
				role.setInputInterruptionAlertType("0");


			setRole(role);

			request.getSession().setAttribute("saveEnableMenu",
					SwtConstants.STR_TRUE);
			request.getSession().setAttribute("saveEnableEntity",
					SwtConstants.STR_TRUE);
			/*Putting menu access details in session*/
			putMenuAccessDetailsGuiInSession(request, roleId);
			/*Putting entity access details in session*/
			putEntityAccessDetailsGuiInSession(request, hostId, roleId);
			putCurrencyGroupItemInReq(request);
			putCurrencyAccessDetailsGuiInSession(request, hostId, roleId);
			request.setAttribute("methodName", "update");
			putDefaultEntityAccessInReq(request);

			SystemInfo systemInfo = new SystemInfo();
			SweepLimitsManager sweepLimitsManager = (SweepLimitsManager) (SwtUtil
					.getBean("sweepLimitsManager"));
			Collection sessionSweepLimitDetails = sweepLimitsManager
					.getSweepLimitsDetails(roleId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// Putting Sweep Limit Details in Session
			request.getSession().setAttribute("sessionSweepLimitDetails",
					sessionSweepLimitDetails);

			Collection sessionSweepLimitDetailsInitial = (ArrayList) SwtUtil
					.copy(sessionSweepLimitDetails);
			request.getSession().setAttribute(
					"sessionSweepLimitDetailsInitial",
					sessionSweepLimitDetailsInitial);

			putSweepButtonStatusInReq(request);
			request.getSession().setAttribute("Operation","change");
			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "change", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	public String update()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionMessages errors = new ActionMessages();
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		Role role = (Role) getRole();
		String roleId = role.getRoleId();
		/*
		 * Start Code added by Arumugam for mantis 1029 on 23-Nov-2009:Added a
		 * new field Account Access Control in Role screen
		 */
		String Acctccy = null;
		String Acctentity = null;
		boolean updateflag = false;
		String copiedRoleId = request.getParameter("copiedRoleId");
		String accountAccess= role.getAccountAccessControl();
		if (!copiedRoleId.trim().equals("")
				&& (accountAccess != null
				&& accountAccess.equalsIgnoreCase("Y"))){
			copyAccountAccess(roleId, copiedRoleId);
		}

		ArrayList<FacilityAccess> facilityAccessList= null;
		try {
			/*
			 * Defining variable to pass into Manager Class There are two case
			 * for Menu Access (Save and Delete) and three ( Save, Update and
			 * Delete) for other cases So defining Hash table which will have
			 * collection for different ( Save, Update and Delete) cases
			 */
			Hashtable htMenuAccess = new Hashtable();
			Hashtable htEntityAccess = new Hashtable();
			Hashtable htWorlQAccess = new Hashtable();
			Hashtable htSweepLimits = new Hashtable();

			// Defining the variables for all the possible cases
			ArrayList colMenuAccessSave;


			ArrayList colMenuAccessUpdate;


			// Defining the variables for all the possible cases
			ArrayList colMenuAccessDelete;

			// Defining the variables for all the possible cases
			ArrayList colEntityAccessSave;

			// Defining the variables for all the possible cases
			ArrayList colEntityAccessUpdate;

			// Defining the variables for all the possible cases
			ArrayList colEntityAccessDelete;

			// Defining the variables for all the possible cases
			ArrayList colCcyGrpAccessSave;

			// Defining the variables for all the possible cases
			ArrayList colCcyGrpAccessUpdate;

			// Defining the variables for all the possible cases
			ArrayList colCcyGrpAccessDelete;

			// Defining the variables for all the possible cases
			ArrayList colSweepLimitsSave;

			// Defining the variables for all the possible cases
			ArrayList colSweepLimitsUpdate;

			// Defining the variables for all the possible cases
			ArrayList colSweepLimitsDelete;

			// Initializing all the variables
			colMenuAccessSave = new ArrayList();
			colMenuAccessUpdate = new ArrayList();
			colMenuAccessDelete = new ArrayList();
			colEntityAccessSave = new ArrayList();
			colEntityAccessUpdate = new ArrayList();
			colEntityAccessDelete = new ArrayList();

			colCcyGrpAccessSave = new ArrayList();
			colCcyGrpAccessUpdate = new ArrayList();
			colCcyGrpAccessDelete = new ArrayList();

			colSweepLimitsSave = new ArrayList();
			colSweepLimitsUpdate = new ArrayList();
			colSweepLimitsDelete = new ArrayList();

			// Adding to the Hash tables
			htMenuAccess.put("menuAccessSave", colMenuAccessSave);
			htMenuAccess.put("menuAccessDelete", colMenuAccessDelete);
			htMenuAccess.put("menuAccessUpdate", colMenuAccessUpdate);
			htMenuAccess.put("entityAccessSave", colEntityAccessSave);
			htMenuAccess.put("entityAccessUpdate", colEntityAccessUpdate);
			htMenuAccess.put("entityAccessDelete", colEntityAccessDelete);

			htMenuAccess.put("ccyGrpAccessSave", colCcyGrpAccessSave);
			htMenuAccess.put("ccyGrpAccessUpdate", colCcyGrpAccessUpdate);
			htMenuAccess.put("ccyGrpAccessDelete", colCcyGrpAccessDelete);

			htMenuAccess.put("sweepLimitsSave", colSweepLimitsSave);
			htMenuAccess.put("sweepLimitsUpdate", colSweepLimitsUpdate);
			htMenuAccess.put("sweepLimitsDelete", colSweepLimitsDelete);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			role.setHostId(hostId);

			/* Updating Menu Access Details in Database */
			Collection menuItemId = extractMenuItemsForUpdate(request);

			if (menuItemId != null) {
				getMenuItemsForSaveUpdateDelete(request, roleId, menuItemId,
						colMenuAccessSave, colMenuAccessUpdate,
						colMenuAccessDelete);
			}

			/*fetches the facility access details*/
			facilityAccessList= new ArrayList<FacilityAccess>();
			if(request.getSession().getAttribute("listAccessUpdateInSession")!=null) {
				facilityAccessList= (ArrayList<FacilityAccess>) request.getSession().getAttribute("listAccessUpdateInSession");
				if(facilityAccessList!=null) {
					roleMgr.updateRoleFacilityAccess(facilityAccessList, roleId);

				}
			}else {
				facilityAccessList= (ArrayList<FacilityAccess>) request.getSession().getAttribute("listAccessAddInSession");
				if(facilityAccessList!=null) {
					roleMgr.saveRoleFacilityAccess(facilityAccessList, roleId);

				}
			}





			/* Updating Entity Access Details in Database */
			Collection entityAccessList = (Collection) (request.getSession()
					.getAttribute("roleEntityAccessList"));

			// Iterating through the whole list and saving only those entries
			// for which Access is either
			// 'Full' or 'View Only'
			Iterator itrEntityAccess = entityAccessList.iterator();

			Collection collEntityAccessGuiInitial = (Collection) request
					.getSession().getAttribute("entityAccessListInitial");
			Iterator itrEntityAccessInitial = collEntityAccessGuiInitial
					.iterator();

			Collection wqaDetailsToBeDeleted = new ArrayList();

			while ((itrEntityAccess.hasNext())
					&& (itrEntityAccessInitial.hasNext())) {
				EntityAccessGui entityAccessGui = (EntityAccessGui) (itrEntityAccess
						.next());
				EntityAccessGui entityAccessGuiInitial = (EntityAccessGui) (itrEntityAccessInitial
						.next());

				String radio1 = entityAccessGui.getEntityAccessHTML1();
				String radio1Initial = entityAccessGuiInitial
						.getEntityAccessHTML1();
				String radio2 = entityAccessGui.getEntityAccessHTML2();
				String radio2Initial = entityAccessGuiInitial
						.getEntityAccessHTML2();
				String radio3 = entityAccessGui.getEntityAccessHTML3();
				String radio3Initial = entityAccessGuiInitial
						.getEntityAccessHTML3();
				String entityId = entityAccessGui.getEntityId();

				/*
				 * Making a new object of type EntityAccess that is to be saved,
				 * updated or deleted in the database depending on the
				 * conditions checked below
				 */
				EntityAccess entityAccess = new EntityAccess();
				entityAccess.getId().setHostId(hostId);
				entityAccess.getId().setRoleId(roleId);
				entityAccess.getId().setEntityId(entityId);


				// Code for deletion and to revert back to user for error msg
				if (((radio1Initial.equals("checked")) || (radio2Initial
						.equals("checked")))
						&& (radio3.equals("checked"))) {

					// Now deleting the entry from S_ENTITY_ACCESS table
					colEntityAccessDelete.add(entityAccess);

				}

				if (!((radio3Initial.equals("checked")) && (radio3
						.equals("checked")))) {
					if (radio3Initial.equals("checked")) {

						if (radio1.equals("checked")) {
							entityAccess.setAccessId(String
									.valueOf(SwtConstants.ENTITY_FULL_ACCESS));
						} else {
							entityAccess.setAccessId(String
									.valueOf(SwtConstants.ENTITY_READ_ACCESS));
						}


						colEntityAccessSave.add(entityAccess);
					} else if (radio1Initial.equals("checked")
							&& radio2.equals("checked")) {
						entityAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_READ_ACCESS));

						colEntityAccessUpdate.add(entityAccess);
					} else if (radio2Initial.equals("checked")
							&& radio1.equals("checked")) {
						entityAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_FULL_ACCESS));

						colEntityAccessUpdate.add(entityAccess);
					}
				}
			}

			Collection ccyGrpAccessList = (Collection) (request.getSession()
					.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST));

			Collection collCcyGrpAccessInitial = (Collection) request
					.getSession().getAttribute(
							"currencyGropuAccessDetailsInitial");

			findRecordsForSaveUpdateDelete(ccyGrpAccessList,
					collCcyGrpAccessInitial, colCcyGrpAccessSave,
					colCcyGrpAccessUpdate, colCcyGrpAccessDelete, hostId,
					roleId);

			/* Updating Sweep Limits Details */
			// log.debug("Updating the Sweep Limits Details=>");
			Collection sessionSweepLimitDetails = (Collection) request
					.getSession().getAttribute("sessionSweepLimitDetails");

			Collection sessionSweepLimitDetailsInitial = (Collection) request
					.getSession().getAttribute(
							"sessionSweepLimitDetailsInitial");

			if (sessionSweepLimitDetails != null) {
				Iterator itrCurrent = sessionSweepLimitDetails.iterator();
				ArrayList matchedObj = new ArrayList();

				while (itrCurrent.hasNext()) {
					boolean isPresent = false;
					SweepLimits sweepLimitsCurrent = (SweepLimits) (itrCurrent
							.next());
					sweepLimitsCurrent.getId().setRoleId(roleId);

					String roleIdCurrent = sweepLimitsCurrent.getId()
							.getRoleId();
					String currencyCodeCurrent = sweepLimitsCurrent.getId()
							.getCurrencyCode();

					if (sessionSweepLimitDetailsInitial != null) {
						Iterator itrInitial = sessionSweepLimitDetailsInitial
								.iterator();

						while (itrInitial.hasNext()) {
							SweepLimits sweepLimitsInitial = (SweepLimits) (itrInitial
									.next());
							String roleIdInitial = sweepLimitsInitial.getId()
									.getRoleId();
							String currencyCodeInitial = sweepLimitsInitial
									.getId().getCurrencyCode();

							if ((roleIdInitial.equals(roleIdCurrent))
									&& (currencyCodeInitial
									.equals(currencyCodeCurrent))) {
								// Firstly comparing current and Initial objects
								isPresent = true;

								if (sweepLimitsInitial.equals(
										sweepLimitsCurrent, sweepLimitsInitial)) {
									matchedObj.add(sweepLimitsInitial);
								} else {

									colSweepLimitsUpdate
											.add(sweepLimitsCurrent);
									matchedObj.add(sweepLimitsInitial);
								}

								break;
							}
						}
					}

					if (!isPresent) {

						colSweepLimitsSave.add(sweepLimitsCurrent);
					}
				}

				Iterator itrTemp = matchedObj.iterator();

				while (itrTemp.hasNext()) {
					SweepLimits sweepLimitsInitial = (SweepLimits) (itrTemp
							.next());
					sessionSweepLimitDetailsInitial.remove(sweepLimitsInitial);
				}

				if (sessionSweepLimitDetailsInitial != null) {
					itrTemp = sessionSweepLimitDetailsInitial.iterator();

					while (itrTemp.hasNext()) {
						SweepLimits sweepLimitTemp = (SweepLimits) (itrTemp
								.next());
						colSweepLimitsDelete.add(sweepLimitTemp);
					}
				}
			}


			Collection locationAccessDetailsList = (Collection) (request
					.getSession()
					.getAttribute(SwtConstants.LOCATION_ACCESS_LIST));
			Collection initialLocationAccessDetailsList = (Collection) (request
					.getSession()
					.getAttribute(SwtConstants.INITIAL_LOCATION_ACCESS_LIST));
			Collection newLocationAccessAddList = new ArrayList();
			Collection newLocationAccessDeleteList = new ArrayList();
			htMenuAccess.put("newLocationAccessAddList",
					newLocationAccessAddList);
			htMenuAccess.put("newLocationAccessDeleteList",
					newLocationAccessDeleteList);

			if (locationAccessDetailsList != null
					&& initialLocationAccessDetailsList != null) {
				Iterator itr = locationAccessDetailsList.iterator();
				Iterator itrInitial = initialLocationAccessDetailsList
						.iterator();

				while (itr.hasNext() && itrInitial.hasNext()) { // Comparing
					LocationAccessGui locAccess = (LocationAccessGui) (itr
							.next());
					LocationAccessGui locAccessInitial = (LocationAccessGui) (itrInitial
							.next());
					String checkBoxInitial = locAccessInitial.getLocAccess();
					String checkBox = locAccess.getLocAccess();
					LocationAccess loc = new LocationAccess();
					if ((checkBoxInitial.equalsIgnoreCase("checked"))
							&& !(checkBox.equalsIgnoreCase("checked"))) {

						loc.getId().setHostId(hostId);
						loc.getId().setRoleId(roleId);
						loc.getId().setEntityId(locAccess.getEntityId());
						loc.getId().setLocationId(locAccess.getLocationId());

						newLocationAccessDeleteList.add(loc);
					}

					if ((!checkBoxInitial.equalsIgnoreCase("checked"))
							&& (checkBox.equalsIgnoreCase("checked"))) {
						loc.getId().setHostId(hostId);
						loc.getId().setRoleId(roleId);
						loc.getId().setEntityId(locAccess.getEntityId());
						loc.getId().setLocationId(locAccess.getLocationId());

						newLocationAccessAddList.add(loc);
					}
				}
			}

			if (role.getRestrictLocations() == null
					|| role.getRestrictLocations().equalsIgnoreCase("")) {
				role.setRestrictLocations("N");
			} else if (role.getRestrictLocations().equalsIgnoreCase("Y")) {
				role.setRestrictLocations("Y");
			}

			/* Condition to check account access value is null */
			if (role.getAccountAccessControl() == null
					|| role.getAccountAccessControl().equals("")) {
				/* Setting account access value as "N" using bean class */
				role.setAccountAccessControl("N");
			}

			if (role.getAllEntityOption() == null
					|| role.getAllEntityOption().equalsIgnoreCase("")) {
				role.setAllEntityOption("N");
			} else if (role.getAllEntityOption().equalsIgnoreCase("Y")) {
				role.setAllEntityOption("Y");
			}

			/* Description: It is required to have a user whose sole purpose to
			 * be able to reset user passwords
			 */

			/* Advanced user configuration retrieving from database*/

			if (role.getAdvancedUser() == null
					|| role.getAdvancedUser().equalsIgnoreCase("")) {
				role.setAdvancedUser("N");
			} else if (role.getAdvancedUser().equalsIgnoreCase("Y")) {
				role.setAdvancedUser("Y");
			}

			/* It is required to check whether the user has privilege to view
			 * Input interruption notification--> Start code:code added for
			 * Input interruption notification
			 */
			if (role.getInputInterruption() == null
					|| role.getInputInterruption().equals("")) {
				role.setInputInterruption("N");
			} else if (role.getInputInterruption().equalsIgnoreCase("Y")) {
				role.setInputInterruption("Y");
			}
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyIlmScenario()))
				role.setMaintainAnyIlmScenario("N");
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyIlmGroup()))
				role.setMaintainAnyIlmGroup("N");
			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyReportHist()))
				role.setMaintainAnyReportHist("N");

			if (SwtUtil.isEmptyOrNull(role.getAllowMsdMultiMvtUpdates()))
				role.setAllowMsdMultiMvtUpdates("N");

			SystemInfo systemInfo = new SystemInfo();

			roleMgr.updateRoleWithOtherdetails(role, htMenuAccess,
					htEntityAccess, wqaDetailsToBeDeleted, htWorlQAccess,
					htSweepLimits, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));


			String hostid = cacheManagerInst.getHostId();
			AccountAccessManager accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			Collection accountAccessDetailsList = (Collection) (request
					.getSession().getAttribute("roleAccountAccessList"));
			String userId=SwtUtil.getCurrentUserId(request.getSession());
			if (accountAccessDetailsList != null && accountAccessDetailsList.size() > 0)
				accountAccessManager.saveAccountAccessControlDetails(
						accountAccessDetailsList, userId);

			request.setAttribute("methodName", "update");
			request.setAttribute("parentFormRefresh", "yes");

			// Clearing all the temporary collection from session
			clearSessionVariablesForRole(request);

			putDefaultEntityAccessInReq(request);
			log.debug("Exiting 'update' Method");

			return getView("add");
		} catch (SwtException swtexp) {

			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}

			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			request.setAttribute("methodName", "update");
			role.setRoleId(roleId);
			setRole(role);
			swtexp.printStackTrace();
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception e) {
			e.printStackTrace();
			clearSessionVariablesForRole(request);
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "update",
							RoleAction.class), request, ""));
			return getView("fail ");
		}
	}

	/**
	 * This function unchecks the chechboxes for those menuitems for which
	 * programId is null so that only those menu items are taken into
	 * consideration for which programId is not null
	 *
	 * @param request
	 * @return
	 */
	private Collection extractMenuItemsForUpdate(HttpServletRequest request) {
		// Collection colMenuAccess = new ArrayList();
		Collection menuItemId = (Collection) (request.getSession()
				.getAttribute("itemDescList"));

		if (menuItemId != null) {
			Iterator itrMenuItemId = menuItemId.iterator();

			while (itrMenuItemId.hasNext()) {

				MenuAccessOptionsGui menuAccessOptionsGui = (MenuAccessOptionsGui) (itrMenuItemId
						.next());

				if ((menuAccessOptionsGui.getProgramId() == null)
						|| menuAccessOptionsGui.getProgramId().equals("")) {
				}
			}
		}

		return menuItemId;
	}

	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			// Setting status of all the buttons
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			// "selectedRoleId"==>This hidden parameter holds the roleId of the
			// role whose details are to be seen
			String roleId = request.getParameter("selectedRoleId");
			Role role = (Role) getRole();
			role.setRoleId(roleId);
			// Getting hostId
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			// Getting Details for that roleId
			Collection coll = (Collection) roleMgr.getRoleDetails(role);
			role = (Role) (coll.iterator().next());
			// Putting that roleId in session
			request.getSession().setAttribute("roleIdInSession", roleId);

			//Mantis 6891
			if (SwtUtil.isEmptyOrNull(role.getInputInterruptionAlertType()))
				role.setInputInterruptionAlertType("0");


			// Setting dynaForm with role Details so as to get them on screen
			setRole(role);

			// "isViewRole"==>This flag controls the presence of different
			// buttons on the screens
			request.setAttribute("isViewRole", "yes");

			/*
			 * The View functionality is same as 'change' functionality the only
			 * difference is that the fields on the screens are view-only.Fields
			 * on different screens are made view-only using "isViewRole" flag.
			 * Hence the value of the "roleMaintenanceOperation" flag is put
			 * "change"
			 *
			 */
			request.getSession().setAttribute("roleMaintenanceOperation",
					"change");

			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			// setting the WorkQAccess Details in session so that in the method
			// saveEntityAccessList()
			// the entities for which workQAccess is defined can be obtained
			WorkQAccessManager workQAccessManager = (WorkQAccessManager) (SwtUtil
					.getBean("workQAccessManager"));
			Collection workQAccessDetails = workQAccessManager
					.getDetailsByRoleId(hostId, roleId);
			request.getSession().setAttribute("sessionWorkQAccessDetails",
					workQAccessDetails);

			getEntityComboForWorkQAccess(request);

			// putDefaultEntityAccessInReq(request);
			SystemInfo systemInfo = new SystemInfo();
			SweepLimitsManager sweepLimitsManager = (SweepLimitsManager) (SwtUtil
					.getBean("sweepLimitsManager"));
			Collection sessionSweepLimitDetails = sweepLimitsManager
					.getSweepLimitsDetails(roleId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			// Putting Sweep Limit Details in Session
			request.getSession().setAttribute("sessionSweepLimitDetails",
					sessionSweepLimitDetails);
			/* putting entity Access Details in session*/
			putEntityAccessDetailsGuiInSession(request, hostId, roleId);
			putMenuAccessDetailsGuiInSession(request,roleId);
			putDefaultEntityAccessInReq(request);

			putCurrencyGroupItemInReq(request);
			putCurrencyAccessDetailsGuiInSession(request, hostId, roleId);
			putSweepButtonStatusInReq(request);

			request.setAttribute("methodName", "view");
			request.getSession().setAttribute("Operation","view");
			log.debug("Exiting 'View' method");

			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "view", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This Function deletes the role from database It deletes the role Selected
	 * from following tables S_ROLE -- data corresponding to that role is
	 * deleted S_ENTITY_ACCESS -- All Entity Access rights Corresponding to that
	 * role are deleted P_MENU_ACCESS-- All Menu Access rights corresponding to
	 * that role are deleted P_WORKQ_ACCESS -- All Work Queue Access rights
	 * corresponding to that role are deleted P_SWEEP_LIMIT -- data
	 * corresponding to this roleID is deleted
	 *
	 * @return
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {

		log.debug("Entering 'delete' Method");
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ArrayList colMenuAccess;
		ArrayList colEntityAccess;
		ArrayList colWorlQAccess;
		ArrayList colCcyGrpAccess;
		ArrayList colSweepLimits;
		colMenuAccess = new ArrayList();
		colEntityAccess = new ArrayList();
		colWorlQAccess = new ArrayList();
		colCcyGrpAccess = new ArrayList();
		colSweepLimits = new ArrayList();

		ActionMessages errors = new ActionMessages();

		try {
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			/*Retrieving roleID through hidden parameter*/
			String roleId = request.getParameter("selectedRoleId").trim();
			Role role = (Role) getRole();
			role.setRoleId(roleId);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			/* Deleting MenuAccess Details */
			Collection toBeDeletedDetails = (Collection) roleMgr
					.getMenuAccessDetails(roleId);
			if (toBeDeletedDetails.size() > 0) {
				Iterator<MenuAccess> itrMenuAccess = toBeDeletedDetails.iterator();

				while (itrMenuAccess.hasNext()) {
					MenuAccess menuAccess = itrMenuAccess.next();

					if (menuAccess.getMenuItem() != null) {
						menuAccess.getId().setDescription(menuAccess.getMenuItem().getDescription());
					}

					colMenuAccess.add(menuAccess);
				}
			}


			/* Deleting Work Queue Access Details */
			WorkQAccessManager workQAccessManager = (WorkQAccessManager) (SwtUtil
					.getBean("workQAccessManager"));
			toBeDeletedDetails = workQAccessManager.getDetailsByRoleId(hostId,
					roleId);

			if (toBeDeletedDetails.size() > 0) {

				Iterator itrWorkQAccess = toBeDeletedDetails.iterator();

				while (itrWorkQAccess.hasNext()) {
					WorkQAccess workQAccess = (WorkQAccess) (itrWorkQAccess
							.next());

					colWorlQAccess.add(workQAccess);
				}
			}

			/* Deleting Currency Group Access Details */

			toBeDeletedDetails = roleMgr.getCurrencyGroupAccessDetails(hostId,
					roleId);

			if (toBeDeletedDetails.size() > 0) {

				Iterator itrCcyGrpAccess = toBeDeletedDetails.iterator();

				while (itrCcyGrpAccess.hasNext()) {
					CurrencyGroupAccess ccyGrpAcc = (CurrencyGroupAccess) (itrCcyGrpAccess
							.next());

					colCcyGrpAccess.add(ccyGrpAcc);
				}
			}

			/* Deleting Entity Access Details */
			/*
			 * Entity Access Table has some parent keys for WorkQAccess table so
			 * it should be delete corresponding entries for EntityAccess Table
			 * are deleted
			 */
			toBeDeletedDetails = (Collection) roleMgr.getEntityAccessDetails(
					hostId, roleId);

			if (toBeDeletedDetails.size() > 0) {

				Iterator itrEntityAccess = toBeDeletedDetails.iterator();

				while (itrEntityAccess.hasNext()) {
					EntityAccess entityAccess = (EntityAccess) (itrEntityAccess
							.next());

					colEntityAccess.add(entityAccess);
				}
			}

			/* Deleting the SweepLimit entry */
			SweepLimitsManager sweepLimitsManager = (SweepLimitsManager) (SwtUtil
					.getBean("sweepLimitsManager"));
			SystemInfo systemInfo = new SystemInfo();
			toBeDeletedDetails = (Collection) sweepLimitsManager
					.getSweepLimitsDetails(roleId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			if (toBeDeletedDetails != null) {

				Iterator itrSweepLimit = toBeDeletedDetails.iterator();

				while (itrSweepLimit.hasNext()) {
					SweepLimits sweepLimits = (SweepLimits) (itrSweepLimit
							.next());

					colSweepLimits.add(sweepLimits);
				}
			}

			/*
			 * Deleting details corresponding to selected role from S_USERS
			 * table
			 */
			/* Deleting the role Details from the table S_ROLE */
			Collection coll = (Collection) roleMgr.getRoleDetails(role);

			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				role = (Role) (itr.next());
			}

			LocationMaintenanceManager mgr = (LocationMaintenanceManager) (SwtUtil
					.getBean("locationMaintenanceManager"));
			Collection locationIdColl = mgr.getLocationIdFromDB(hostId, roleId);

			AccountAccessManager accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			accountAccessManager.deleteAcctAccessDetails(roleId);

			roleMgr.deleteRoleWithOtherdetails(role, colMenuAccess,
					colEntityAccess, colCcyGrpAccess, colSweepLimits,
					locationIdColl);
			request.setAttribute("methodName", "save");
			putDefaultEntityAccessInReq(request);
			log.debug("Exiting 'delete' Method");

			return display();
		} catch (SwtException swtexp) {
			log.debug("SwtException in RoleAction.delete  - swtexp.getErrorCode() - "
					+ swtexp.getErrorCode()
					+ "swtexp.getErrorLogFlag() - "
					+ swtexp.getErrorLogFlag());

			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}

			Collection collRole = roleMgr.getRoleDetails(new Role());
			request.setAttribute("RoleDetailsList", collRole);
			putDefaultEntityAccessInReq(request);
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			swtexp.printStackTrace();
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception e) {
			e.printStackTrace();
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "delete",
							RoleAction.class), request, ""));
			return getView("fail");

		}

	}

	/**
	 * This Function Opens the MenuAccessOptions Screen with status of
	 * CheckBoxes Empty -- In case of Add Method Save as that of Corresponding
	 * DataBase field -- In case of Change and Copy From
	 *
	 * @return
	 * @throws SwtException
	 */
	public String menuAccessOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering into menuAccessOptions() method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			// Setting status of different buttons on the screen
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			// Getting the dynaForm
			Role role = (Role) getRole();
			// log.debug("The dynaForm is" + role);

			String roleId = (String) request.getSession().getAttribute(
					"roleIdInSession");

			/*
			 * Getting value of roleMaintenanceOperationFlag to get the name of
			 * operation operation can be "add", "change" ,"copyFrom"
			 */
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			/* If operation is "add" */
			if (roleMaintenanceOperationFlag.equals("add")) {
				/*
				 * menuAccessOptionsFlag checks if user is entering first time
				 * in this method
				 */
				String menuAccessOptionsFlag = (String) request.getSession()
						.getAttribute("menuAccessOptionsFlag");
				// If user enters first time in this method then setAttribute
				// itemDescList in Session
				if (menuAccessOptionsFlag.equals("0")) {
					putMenuItemListGuiInSession(request);

					Collection menuItemGui = (Collection) request.getSession()
							.getAttribute("itemDescList");
					request.setAttribute("itemDescList", menuItemGui);
					request.getSession().setAttribute("menuAccessOptionsFlag",
							"1");
				}
				// If user enters again then only retrieve the collection
				// itemDescList from Session
				else {
					Collection menuItemGui = (Collection) request.getSession()
							.getAttribute("itemDescList");
					request.setAttribute("itemDescList", menuItemGui);
				}
			}

			/* If operation is "change" */
			if (roleMaintenanceOperationFlag.equals("change")) {
				/*
				 * menuAccessOptionsFlauig checks if user is entering first time
				 * in this method
				 */
				String menuAccessOptionsFlag = (String) request.getSession()
						.getAttribute("menuAccessOptionsFlag");
				// log.debug("The value of menuAccessOptionsFlag is " +
				// menuAccessOptionsFlag);

				// If user enters first time in this method then setAttribute
				// itemDescList in Session
				if (menuAccessOptionsFlag.equals("0")) {
					putMenuAccessDetailsGuiInSession(request, roleId);

					Collection menuAccessGui = (Collection) request
							.getSession().getAttribute("itemDescList");
					request.setAttribute("itemDescList", menuAccessGui);
					request.getSession().setAttribute("menuAccessOptionsFlag",
							"1");
				}
				// If user enters again then only retrieve the collection
				// itemDescList from Session
				else {
					Collection menuAccessGui = (Collection) request
							.getSession().getAttribute("itemDescList");
					request.setAttribute("itemDescList", menuAccessGui);
				}
			}

			/* If operation is "copyFrom" */
			if (roleMaintenanceOperationFlag.equals("copyFrom")) {
				Collection menuAccessGui = (Collection) request.getSession()
						.getAttribute("itemDescList");
				request.setAttribute("itemDescList", menuAccessGui);
			}

			/*
			 * Setting "isView attribute to true in the request object so that
			 * only "close" button should be visible in different sub Screens
			 */
			String isViewRole = request.getParameter("isViewRole");

			if ((isViewRole != null) && isViewRole.equals("yes")) {
				request.setAttribute("isViewRole", isViewRole);
			}

			String screenTitle = request.getParameter("screenTitle");

			if ((screenTitle != null) && screenTitle.equals("addRole")) {
				request.setAttribute("screenTitle", "addRole");
			} else {
				request.setAttribute("screenTitle", "changeRole");
			}
			request.setAttribute("enableLocationButton", request
					.getParameter("enableLocationButton"));
			putDefaultEntityAccessInReq(request);
			log.debug("Exiting into menuAccessOptions() method");

			return getView("menuAccessOptions");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "menuAccessOptions", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * out of the menuItemCollecion this function extracts only those menu items
	 * which are checked by the user and whose programId is not null
	 *
	 * @param request
	 * @return
	 */
	private Collection extractMenuItemsForSave(HttpServletRequest request) {
		Collection colMenuAccess = new ArrayList();
		Collection menuItemId = (Collection) (request.getSession()
				.getAttribute("itemDescList"));

		if (menuItemId != null) {
			Iterator itrMenuItemId = menuItemId.iterator();

			while (itrMenuItemId.hasNext()) {

				MenuAccessOptionsGui menuAccessOptionsGui = (MenuAccessOptionsGui) (itrMenuItemId
						.next());
				String fullAccessRadioStatus = menuAccessOptionsGui
						.getMenuAccessHTMLFullAccess();
				String viewAccessRadioStatus = menuAccessOptionsGui
						.getMenuAccessHTMLViewAccess();

				/*
				 * if(checkBoxStatus.equals("checked") &&
				 * menuAccessOptionsGui.getProgramId() != null){
				 * colMenuAccess.add(menuAccessOptionsGui); //log.debug("The
				 * saved entry is"+menuAccessOptionsGui); }
				 */

				if (fullAccessRadioStatus.equals("checked")
						|| viewAccessRadioStatus.equals("checked")) {
					colMenuAccess.add(menuAccessOptionsGui);
				}

			}
		}

		return colMenuAccess;
	}

	/**
	 * This Function temporarily saves details filled by the user into a
	 * collection
	 *
	 * @return
	 * @throws SwtException
	 */
	public String saveMenuAccessOptions() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering 'saveMenuAccessOptions' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			// Setting status of different buttons
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			int itemIdIndex = 1;

			// Getting the Menu Item Gui From Session
			Collection collMenuItemGui = (Collection) request.getSession()
					.getAttribute("itemDescList");

			Iterator itr = collMenuItemGui.iterator();
			int i = 0;
			// This variable is set to true only when at least one of the menu access option is checked
			String saveEnableMenu = SwtConstants.STR_FALSE;
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");
			if (roleMaintenanceOperationFlag.equals("change")) {
				saveEnableMenu = SwtConstants.STR_TRUE;
				request.setAttribute("methodName", "update");
			}
			int k = 0;
			while (itr.hasNext()) {
				MenuAccessOptionsGui menuAccessOptionsGuiObject = (MenuAccessOptionsGui) itr
						.next();
				String radioButtonStatus = request
						.getParameter("menuAccessHTML" + (k + 1));


				if (radioButtonStatus != null) {
					menuAccessOptionsGuiObject.setMenuAccessHTMLFullAccess("");
					menuAccessOptionsGuiObject.setMenuAccessHTMLViewAccess("");
					menuAccessOptionsGuiObject.setMenuAccessHTMLNoAccess("");

					if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_FULL_ACCESS))) {
						menuAccessOptionsGuiObject
								.setMenuAccessHTMLFullAccess("checked");
						if (saveEnableMenu.equals(SwtConstants.STR_FALSE)) {
							saveEnableMenu = SwtConstants.STR_TRUE;
						}
					} else if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_READ_ACCESS))) {
						menuAccessOptionsGuiObject
								.setMenuAccessHTMLViewAccess("checked");
						if (saveEnableMenu.equals(SwtConstants.STR_FALSE)) {
							saveEnableMenu = SwtConstants.STR_TRUE;
						}
					} else {
						menuAccessOptionsGuiObject
								.setMenuAccessHTMLNoAccess("checked");
					}

				}

				k++;
			}

			request.getSession().setAttribute("itemDescList", collMenuItemGui);
			request.setAttribute("parentFormRefresh", "yes");
			request.getSession().setAttribute("saveEnableMenu", saveEnableMenu);
			request.setAttribute("saveEnableMenu", saveEnableMenu);
			putDefaultEntityAccessInReq(request);
			request.setAttribute("enableLocationButton", request
					.getParameter("enableLocationButton"));

			return getView("menuAccessOptions");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveMenuAccessOptions", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This function finds which all checkBoxes are checked in the list of menu
	 * items
	 *
	 * @param menuItemGui
	 * @param menuAccessDetails
	 * @return
	 */
	private Collection findMenuAccessGui(Collection menuItemGui,
										 Collection menuAccessDetails) {
		log.debug("Entering into findMenuAccessGui()");

		Collection output = new ArrayList();
		Iterator itr = menuItemGui.iterator();

		try {
			while (itr.hasNext()) {
				MenuAccessOptionsGui menuAccessOptionsGui = (MenuAccessOptionsGui) (itr
						.next());
				String itemId = menuAccessOptionsGui.getItemId();
				Iterator itrAnother = menuAccessDetails.iterator();

				while (itrAnother.hasNext()) {
					MenuAccess menuAccess = (MenuAccess) (itrAnother.next());
					String itemIdAnother = menuAccess.getId().getItemId();

					if (itemIdAnother.equals(itemId)) {
						menuAccessOptionsGui.setMenuAccessHTMLFullAccess("");
						menuAccessOptionsGui.setMenuAccessHTMLViewAccess("");
						menuAccessOptionsGui.setMenuAccessHTMLNoAccess("");
						if (menuAccess.getAccessId() != null) {
							if (menuAccess
									.getAccessId()
									.equals(
											String
													.valueOf(SwtConstants.ENTITY_FULL_ACCESS))) {
								menuAccessOptionsGui
										.setMenuAccessHTMLFullAccess("checked");
							} else if (menuAccess
									.getAccessId()
									.equals(
											String
													.valueOf(SwtConstants.ENTITY_READ_ACCESS))) {
								menuAccessOptionsGui
										.setMenuAccessHTMLViewAccess("checked");
							} else {
								menuAccessOptionsGui
										.setMenuAccessHTMLNoAccess("checked");
							}
						}

						break;
					}
				}

				output.add(menuAccessOptionsGui);
			}

			log.debug("Exiting from findMenuAccessGui()");

			return output;
		} catch (Exception e) {
			log.debug("Exception in RoleAction.findMenuAccessGui"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);

			return output;
		}
	}

	/**
	 * This function finds the the EntityAccessGui collection EntityAccessGui
	 * collection--Contains the list of all entities and three radio button
	 * corresponding to each these button corresponds to "full", "viewOnly",
	 * "noAccess" for that particular entity The radio button corresponding to
	 * that particular access is checked
	 *
	 * @param collEntityAccessGui
	 * @param collEntityAccessList
	 * @return
	 */
	private Collection findEntityAccessGui(Collection collEntityAccessGui,
										   Collection collEntityAccessList) {
		log.debug("Entering into findEntityAccessGui()");

		Collection output = new ArrayList();
		Iterator itr = collEntityAccessGui.iterator();
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			// This identifies the parameter name of a particular radio button corresponding to a particular entry
			int identifier = 0;

			while (itr.hasNext()) {
				EntityAccessGui entityAccessGui = (EntityAccessGui) (itr.next());
				String entityId = entityAccessGui.getEntityId();

				Iterator itrAnother = collEntityAccessList.iterator();

				while (itrAnother.hasNext()) {
					EntityAccess entityAccess = (EntityAccess) (itrAnother
							.next());
					String entityIdAnother = entityAccess.getId().getEntityId();

					if (entityIdAnother.equals(entityId)) {
						String accessId = entityAccess.getAccessId();
						entityAccessGui.setEntityAccessHTML1("");
						entityAccessGui.setEntityAccessHTML2("");
						entityAccessGui.setEntityAccessHTML3("");

						if (accessId != null) {
							if (accessId.equals("0")) {
								entityAccessGui.setEntityAccessHTML1("checked");
							} else if (accessId.equals("1")) {
								entityAccessGui.setEntityAccessHTML2("checked");
							} else if (accessId.equals("2")) {
								entityAccessGui.setEntityAccessHTML3("checked");
							}
						}
					}
				}

				identifier++;
				output.add(entityAccessGui);
			}

			log.debug("Exiting from findEntityAccessGui()");

			return output;
		} catch (Exception e) {
			log.debug("Exception in RoleAction.findEntityAccessGui"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);

			return output;
		}
	}

	/**
	 * This function creates a Gui Collection from the List of entities
	 *
	 * @param input
	 * @return
	 */
	private Collection convertEntityCollection(Collection input) {
		Collection output = new ArrayList();
		Iterator itr = input.iterator();
		int identifier = 0;

		while (itr.hasNext()) {
			Entity entityObject = (Entity) (itr.next());
			String hostId = entityObject.getId().getHostId();
			String entityId = entityObject.getId().getEntityId();
			String entityName = entityObject.getEntityName();
			EntityAccessGui entityAccessGuiObject = new EntityAccessGui();
			entityAccessGuiObject.setHostId(hostId);
			entityAccessGuiObject.setEntityId(entityId);
			entityAccessGuiObject.setEntityName(entityName);

			entityAccessGuiObject.setEntityAccessHTML1("");
			entityAccessGuiObject.setEntityAccessHTML2("");
			entityAccessGuiObject.setEntityAccessHTML3("checked");
			output.add(entityAccessGuiObject);
			identifier++;
		}

		return output;
	}

	/**
	 * This Function show the entityAccess jsp
	 *
	 * @return
	 * @throws SwtException
	 */
	public String entityAccessList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = (Role) getRole();
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			String roleId = (String) request.getSession().getAttribute(
					"roleIdInSession");

			/*
			 * Getting value of roleMaintenanceOperationFlag to get the name of
			 * operation operation can be "add", "change" ,"copyFrom"
			 */
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			/* If operation is "add" */
			if (roleMaintenanceOperationFlag.equals("add")) {
				/*
				 * entityAccessListFlag checks if user is entering first time in
				 * this method
				 */
				String entityAccessListFlag = (String) request.getSession()
						.getAttribute("entityAccessListFlag");

				// If user enters first time in this method then setAttribute
				// 'entityAccessList' in Session
				if (entityAccessListFlag.equals("0")) {
					putEntityListGuiInSession(request, hostId);

					Collection entityListGui = (Collection) request
							.getSession().getAttribute("roleEntityAccessList");
					// log.debug("The Entityt List Gui is" + entityListGui);
					request.setAttribute("roleEntityAccessList", entityListGui);
					request.getSession().setAttribute("entityAccessListFlag",
							"1");
				}
				// If user enters againg then only retrieve the collection
				// itemDescList from Session
				else {

					Collection EntityAccessGui = (Collection) request
							.getSession().getAttribute("roleEntityAccessList");
					request.setAttribute("roleEntityAccessList",
							EntityAccessGui);
				}
			} else if (roleMaintenanceOperationFlag.equals("change")) {
				/* If operation is "change" */
				/*
				 * entityAccessListFlag checks if user is entering first time in
				 * this method
				 */
				String entityAccessListFlag = (String) request.getSession()
						.getAttribute("entityAccessListFlag");

				// If user enters first time in this method then setAttribute
				// 'entityAccessList' in Session
				if (entityAccessListFlag.equals("0")) {
					putEntityAccessDetailsGuiInSession(request, hostId, roleId);

					Collection entityAccessDetailsGui = (Collection) request
							.getSession().getAttribute("roleEntityAccessList");
					request.setAttribute("roleEntityAccessList",
							entityAccessDetailsGui);
					request.getSession().setAttribute("entityAccessListFlag",
							"1");
				}
				// If user enters again then only retrieve the collection
				// itemDescList from Session
				else {

					Collection EntityAccessDetailsGui = (Collection) request
							.getSession().getAttribute("roleEntityAccessList");
					request.setAttribute("roleEntityAccessList",
							EntityAccessDetailsGui);
				}
			}

			/* If operation is "copyFrom" */
			if (roleMaintenanceOperationFlag.equals("copyFrom")) {

				Collection EntityAccessDetailsGui = (Collection) request
						.getSession().getAttribute("roleEntityAccessList");
				request.setAttribute("roleEntityAccessList",
						EntityAccessDetailsGui);
			}

			String isViewRole = request.getParameter("isViewRole");

			if ((isViewRole != null) && isViewRole.equals("yes")) {
				request.setAttribute("isViewRole", isViewRole);
			}

			String screenTitle = request.getParameter("screenTitle");

			if ((screenTitle != null) && screenTitle.equals("addRole")) {
				request.setAttribute("screenTitle", "addRole");
			} else {
				request.setAttribute("screenTitle", "changeRole");
			}

			return getView("roleEntityAccessList");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "entityAccess", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method saves Entity Access details in a temporary Collection
	 * 'entityAccessList'
	 *
	 * @return
	 * @throws SwtException
	 */
	public String saveEntityAccessList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'saveEntityAccessList' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			String roleId = (String) request.getSession().getAttribute(
					"roleIdInSession");

			/*
			 * Here we creates an ArrayList which contains a list of those
			 * entities which has only those entities which has full or viewOnly
			 * access in entity access List
			 *
			 */
			ArrayList entityComboForCcyGrpAccess = new ArrayList();
			Collection entityAccessList = (Collection) request.getSession()
					.getAttribute("roleEntityAccessList");
			log.debug("EntityAccessList entityAccessList >> "
					+ entityAccessList);

			Iterator itr = entityAccessList.iterator();
			int i = 0; // acts as a counter
			// This variable will only be set to true if atleast one entity is selected for full or read only access
			String saveEnableEntity = SwtConstants.STR_FALSE;
			// This is needed to enable or disable "save" button on add role
			// screen
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if (roleMaintenanceOperationFlag.equals("change")) {
				saveEnableEntity = SwtConstants.STR_TRUE;
				request.setAttribute("methodName", "update");
			} else {
				request.setAttribute("methodName", "save");
			}

			// Getting the Work Queue Access Details from the database or from
			// temporary collection
			Collection workQAccessDetails = new ArrayList();
			workQAccessDetails = (Collection) request.getSession()
					.getAttribute("sessionWorkQAccessDetails");

			putDefaultEntityAccessInReq(request);
			/*
			 * Here we gets entityAccessList Collection from Session and
			 * iterates through each of its row also value of each radio button
			 * is obtained through hidden parameters which are set in the jsp
			 * The accessTypeNew array is populated based on the value of these
			 * hidden parameters
			 */
			boolean isWorkQAccessRecordPresent = false;

			while (itr.hasNext()) {
				EntityAccessGui entityAccessGui = (EntityAccessGui) (itr.next());
				String entityId = entityAccessGui.getEntityId();
				String radioButtonStatus = request
						.getParameter("entityAccessList" + (i + 1));
				if (radioButtonStatus != null) {
					entityAccessGui.setEntityAccessHTML1("");
					entityAccessGui.setEntityAccessHTML2("");
					entityAccessGui.setEntityAccessHTML3("");

					if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_FULL_ACCESS))) {
						entityAccessGui.setEntityAccessHTML1("checked");
						entityComboForCcyGrpAccess.add(entityAccessGui);

						if (saveEnableEntity.equals(SwtConstants.STR_FALSE)) {
							saveEnableEntity = SwtConstants.STR_TRUE;
						}
					} else if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_READ_ACCESS))) {
						entityAccessGui.setEntityAccessHTML2("checked");
						entityComboForCcyGrpAccess.add(entityAccessGui);
						checkCcyGrpAccessForEntityReadAccess(request, entityId);
						if (saveEnableEntity.equals(SwtConstants.STR_FALSE)) {
							saveEnableEntity = SwtConstants.STR_TRUE;
						}
					} else {
						entityAccessGui.setEntityAccessHTML3("checked");
						checkCcyGrpAccessForEntityNoAccess(request, entityId);
					}

					log.debug("EntityAccessHTML1>>"
							+ entityAccessGui.getEntityAccessHTML1()
							+ "<<EntityAccessHTML2>>"
							+ entityAccessGui.getEntityAccessHTML2()
							+ "<<EntityAccessHTML3>>"
							+ entityAccessGui.getEntityAccessHTML3());
				}

				i++;

			}

			request.getSession().setAttribute("entityComboForCcyGrpAccess",
					entityComboForCcyGrpAccess);
			request.getSession().setAttribute("saveEnableEntity",
					saveEnableEntity);// flag to get "Save" button enable
			request.getSession().setAttribute("roleEntityAccessList",
					entityAccessList);
			request.setAttribute("parentFormRefresh", "yes");

			putSweepButtonStatusInReq(request);
			putDefaultEntityAccessInReq(request);

			log.debug("Exiting 'saveEntityAccessList' method");

			return getView("roleEntityAccessList");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "saveEntityAccessList", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method opens the 'copyFrom role' jsp copyFrom method
	 *
	 * @return
	 * @throws SwtException
	 */
	public String copyFrom()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'copyFrom' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			String newRoleId = request.getParameter("newRoleId");
			request.setAttribute("newRoleId", newRoleId);
			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if ((roleMaintenanceOperation != null)
					&& roleMaintenanceOperation.equalsIgnoreCase("add")) {
				request.setAttribute("screenTitle", "addRole");
			} else {
				request.setAttribute("screenTitle", "changeRole");
			}

			Role role = new Role();
			Collection collRole = roleMgr.getRoleDetails(role);

			ArrayList roleList = new ArrayList();
			Iterator itr = (collRole).iterator();

			while (itr.hasNext()) {
				Role rol = (Role) (itr.next());
				roleList.add(new LabelValueBean(rol.getRoleName(), rol
						.getRoleId()));
			}

			request.setAttribute("roleList", roleList);

			String roleIdSelectedParentScreen = request
					.getParameter("roleIdSelected");
			request.setAttribute("roleIdSelected", roleIdSelectedParentScreen);
			putDefaultEntityAccessInReq(request);

			log.debug("Exiting 'copyFrom' method");

			return getView("copyFrom");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "copyFrom", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method fetches the details for Role, MenuAccessOptions,
	 * EntityAccess, WorkQAccess and Sweepinglimit from database and put them in
	 * session
	 *
	 * @return
	 * @throws SwtException
	 */
	public String copy()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'copy' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = (Role) getRole();

			// Setting the "copyFromFlag" to true
			request.getSession().setAttribute("copyFromFlag", "true");

			request.getSession().setAttribute("roleMaintenanceOperation",
					"copyFrom"); // Shows operation is copyFrom

			String roleId = request.getParameter("copiedRoleId");

			request.getSession().setAttribute("copiedRoleId",
					roleId);

			// Getting hostId from the CacheManager
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			// Getting previous role id which user filled before clicking on
			// copyFrom button
			String newRoleId = request.getParameter("newRoleId");
			role.setRoleId(roleId);

			// Getting roleDetails from the Role Manager
			Collection coll = (Collection) roleMgr.getRoleDetails(role);
			role = (Role) (coll.iterator().next());

			String restrictLocations = role.getRestrictLocations();
			if (restrictLocations.equalsIgnoreCase("Y")) {
				role.setRestrictLocations("Y");
			} else {
				role.setRestrictLocations("N");
			}

			String allEntityOption = role.getAllEntityOption();
			if (allEntityOption.equalsIgnoreCase("Y")) {
				role.setAllEntityOption("Y");
			} else {
				role.setAllEntityOption("N");
			}
			setRole(role);

			// Getting the Menu Access Details from the database
			putMenuAccessDetailsGuiInSession(request, roleId);

			// Getting the Entity Access Details from the database
			putEntityAccessDetailsGuiInSession(request, hostId, roleId);

			putCurrencyGroupItemInReq(request);
			putCurrencyAccessDetailsGuiInSession(request, hostId, roleId);
			putSweepButtonStatusInReq(request);
			// Getting SweepLimits Details from the database
			SystemInfo systemInfo = new SystemInfo();
			SweepLimitsManager sweepLimitsMgr = (SweepLimitsManager) (SwtUtil
					.getBean("sweepLimitsManager"));
			Collection sessionSweepLimitDetails = sweepLimitsMgr
					.getSweepLimitsDetails(roleId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			// Putting Sweep Limit Details in Session
			request.getSession().setAttribute("sessionSweepLimitDetails",
					sessionSweepLimitDetails);

			// Setting attributes to enable buttons
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_TRUE);
			request.setAttribute("methodName", "save");

			String roleIdSelectedParentScreen = request
					.getParameter("roleIdSelected");
			role.setRoleId(roleIdSelectedParentScreen);

			if ((roleIdSelectedParentScreen != null)
					&& !roleIdSelectedParentScreen.equals("")) {
				request.setAttribute("methodName", "update");
			}

			request.getSession().setAttribute("saveEnableMenu",
					SwtConstants.STR_TRUE);
			request.getSession().setAttribute("saveEnableEntity",
					SwtConstants.STR_TRUE);
			request.setAttribute("saveEnableMenu", SwtConstants.STR_TRUE);
			request.setAttribute("saveEnableEntity", SwtConstants.STR_TRUE);

			String screenTitle = request.getParameter("screenTitle");

			if ((screenTitle != null) && screenTitle.equals("addRole")) {
				request.setAttribute("screenTitle", "addRole");
			} else {
				request.setAttribute("screenTitle", "changeRole");
			}
			request.setAttribute("copiedRoleId", roleId);
			putDefaultEntityAccessInReq(request);
			role.setRoleId(newRoleId);

			return getView("add");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "copy", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This Function Makes comboBox for Entities so as to make it available in
	 * WorkQAccess The Entities selected are those which have either 'Full' or
	 * 'View Only' Access.
	 *
	 * @param request
	 */
	void putExistingEntityComboForWorkQAccessInSession(
			HttpServletRequest request, String hostId, String roleId) {
		try {
			Collection existingEntityComboForWorkQAccess = new ArrayList();
			// Getting EntityAccessDetails from Role Manager
			Collection collEntityAccessDetails = (Collection) roleMgr
					.getEntityAccessDetails(hostId, roleId);

			/*
			 * Iterating through the whole Collection so as to select only those
			 * entities for which access type is 'Full' or 'View Only'
			 */
			Iterator itr = collEntityAccessDetails.iterator();

			while (itr.hasNext()) {
				EntityAccess entityAccess = (EntityAccess) (itr.next());
				String entityId = entityAccess.getId().getEntityId();
				String accessId = entityAccess.getAccessId();

				if (accessId != null) {
					if (accessId.equals(String
							.valueOf(SwtConstants.ENTITY_FULL_ACCESS))
							|| accessId.equals(String
							.valueOf(SwtConstants.ENTITY_READ_ACCESS))) {
						existingEntityComboForWorkQAccess
								.add(new LabelValueBean(entityId, entityId));
					}
				}
			}

			request.getSession().setAttribute(
					"existingEntityComboForWorkQAccess",
					existingEntityComboForWorkQAccess);
		} catch (Exception e) {
			log.debug("Exception in RoleAction.putExistingEntityComboForWorkQAccessInSession"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
		}
	}

	/**
	 * This method gets the list of menu items and puts the corresponding gui
	 * collection in the session the gui collection contains the a checkbox
	 * corresponding to each menu Item
	 *
	 * @param request
	 */
	void putMenuItemListGuiInSession(HttpServletRequest request) {
		try {
			log.debug("Entering into 'putMenuItemListGuiInSession' Method ");

			// Getting Menu Item List from roleManager
			Collection collMenuItem = roleMgr.getMenuItemDetails();

			ArrayList menuItemTree = new ArrayList();
			ArrayList topMenuItemList = new ArrayList();

			if (collMenuItem != null) {
				Iterator itr = collMenuItem.iterator();

				while (itr.hasNext()) {
					MenuItem menuItemTop = (MenuItem) (itr.next());

					if ((menuItemTop != null)
							&& (menuItemTop.getParentId() != null)
							&& menuItemTop.getParentId().equals("0")) {
						topMenuItemList.add(menuItemTop);
					}
				}
			}

			getMenuItemTree(collMenuItem, topMenuItemList, menuItemTree);

			if (menuItemTree != null) {
				Iterator itrTemp = menuItemTree.iterator();

				while (itrTemp.hasNext()) {
					MenuAccessOptionsGui miGui = (MenuAccessOptionsGui) (itrTemp
							.next());
				}
			}

			request.getSession().setAttribute("itemDescList", menuItemTree);

			log.debug("Exiting from 'putMenuItemListGuiInSession' Method ");
		} catch (Exception e) {
			log.debug("Exception in RoleAction.putMenuItemListGuiInSession"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
		}
	}

	/**
	 * This is a recursive function which creates a tree structure of the menu
	 * items and also converts the collection of menu items into the Gui
	 * collection Menu Item Collection-- Contains only the list of menu items
	 * Menu Item Gui Collection --- Contains the list of menu items and a list
	 * of checkboxes corresponding to each
	 *
	 * @param collMenuItem
	 * @param topMenuItemList
	 * @param menuItemTree
	 */
	private void getMenuItemTree(Collection collMenuItem,
								 Collection topMenuItemList, Collection menuItemTree) {
		if (collMenuItem != null) {
			Iterator itr = collMenuItem.iterator();

			while (itr.hasNext()) {
				MenuItem mi = (MenuItem) (itr.next());
				String parentItemId = mi.getParentId();
				MenuAccessOptionsGui miGui = new MenuAccessOptionsGui();

				miGui.setProgramId(mi.getProgramId());
				miGui.setItemId(mi.getItemId());
				miGui.setParentId(mi.getParentId());
				miGui.setDescription(mi.getDescription());

				miGui.setDescriptionLevel1("");
				miGui.setDescriptionLevel2("");
				miGui.setDescriptionLevel3("");
				miGui.setMenuAccessHTMLFullAccess("");
				miGui.setMenuAccessHTMLViewAccess("");
				miGui.setMenuAccessHTMLNoAccess("checked");

				Collection collSub = mi.getSubMenuList();
				boolean eligibleForLevel2 = false;

				if ((mi.getParentId() != null) && mi.getParentId().equals("0")) {
					miGui.setDescriptionLevel1(mi.getDescription());
				} else {
					if (topMenuItemList != null) {
						Iterator itrTop = topMenuItemList.iterator();

						while (itrTop.hasNext()) {
							MenuItem menuTop = (MenuItem) (itrTop.next());
							String itemIdTop = menuTop.getItemId();

							if ((itemIdTop != null) && (parentItemId != null)
									&& itemIdTop.equals(parentItemId)) {
								eligibleForLevel2 = true;
							}
						}
					}

					if (eligibleForLevel2) {
						miGui.setDescriptionLevel2(mi.getDescription());
					} else {
						miGui.setDescriptionLevel3(mi.getDescription());
					}
				}

				menuItemTree.add(miGui);

				if (collSub != null) {
					getMenuItemTree(collSub, topMenuItemList, menuItemTree);
				}
			}
		}
	}

	/**
	 * This method fetches details of the Menu Access Options from the database
	 * and puts them in the session
	 *
	 * @param request
	 * @param roleId
	 */
	void putMenuAccessDetailsGuiInSession(HttpServletRequest request,
										  String roleId) {
		try {

			// Getting Menu Access Details for a particular roleId from
			// roleManager
			Collection collMenuAccess = roleMgr.getMenuAccessDetails(roleId);

			// Getting Menu Item List from P_MENU_ITEM table in the form of gui object and putting that in session
			putMenuItemListGuiInSession(request);

			// Getting Menu Item Gui from Session
			Collection collMenuItemGui = (Collection) request.getSession()
					.getAttribute("itemDescList");

			// Finding Menu Access Details Gui Collection
			Collection collMenuAccessGui = findMenuAccessGui(collMenuItemGui,
					collMenuAccess);

			request.getSession()
					.setAttribute("itemDescList", collMenuAccessGui);

			Collection collMenuAccessGuiInitial = new ArrayList();
			collMenuAccessGuiInitial = (ArrayList) SwtUtil
					.copy(collMenuAccessGui);

			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if ((roleMaintenanceOperation != null)
					&& roleMaintenanceOperation.equals("copyFrom")) {
			} else {
				request.getSession().setAttribute("initialIitemDescList",
						collMenuAccessGuiInitial);
			}

			log.debug("Exiting from 'putMenuAccessDetailsGuiInSession' Method ");
		} catch (Exception e) {
			log
					.debug("Exception in RoleAction.putMenuAccessDetailsGuiInSession"
							+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
		}
	}

	void putEntityListGuiInSession(HttpServletRequest request, String hostId) {
		try {
			log.debug("Entering into 'putEntityListGuiInSession' Method ");

			// Getting Entity list
			Collection collEntity = roleMgr.getEntityDetails(hostId);

			// Converting Entity List into Gui Object
			Collection collEntityListGui = (Collection) convertEntityCollection(collEntity);

			// Putting the Gui Object in Session
			request.getSession().setAttribute("roleEntityAccessList",
					collEntityListGui);
			log.debug("Exiting from 'putEntityListGuiInSession' Method ");
		} catch (Exception e) {
			log.debug("Exception in RoleAction.putEntityListGuiInSession"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
		}
	}

	void putEntityAccessDetailsGuiInSession(HttpServletRequest request,
											String hostId, String roleId) {
		try {
			log.debug("Entering into 'putEntityAccessDetailsInSession' Method ");

			// Getting EntityAccess Details From roleManager
			Collection collEntityAccessDetails = (Collection) roleMgr
					.getEntityAccessDetails(hostId, roleId);
			// log.debug("The data in EntityAccessTable is" +
			// collEntityAccessDetails);

			// Getting Entity List Gui from Session
			putEntityListGuiInSession(request, hostId);

			Collection collEntityListGui = (Collection) request.getSession()
					.getAttribute("roleEntityAccessList");

			// Finding EntityAccessGui
			Collection collEntityAccessGui = findEntityAccessGui(
					collEntityListGui, collEntityAccessDetails);

			request.getSession().setAttribute("roleEntityAccessList",
					collEntityAccessGui);

			// Getting entityComboForCcyGrpAccess from Session
			Collection entityComboForCcyGrpAccess = new ArrayList();

			Collection collEntityAccessGuiInitial = new ArrayList();

			for (int j = 0; j < collEntityAccessGui.size(); j++) {
				EntityAccessGui ea = (EntityAccessGui) ((ArrayList) collEntityAccessGui)
						.get(j);
				EntityAccessGui e = new EntityAccessGui();
				e.setEntityAccessHTML1(ea.getEntityAccessHTML1());
				e.setEntityAccessHTML2(ea.getEntityAccessHTML2());
				e.setEntityAccessHTML3(ea.getEntityAccessHTML3());
				e.setEntityId(ea.getEntityId());
				e.setEntityName(ea.getEntityName());

				if ((ea.getEntityAccessHTML1().equalsIgnoreCase("checked"))
						|| (ea.getEntityAccessHTML2()
						.equalsIgnoreCase("checked"))) {
					entityComboForCcyGrpAccess.add(ea);
				}

				collEntityAccessGuiInitial.add(e);
			}

			String roleMaintenanceOperation = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if ((roleMaintenanceOperation != null)
					&& roleMaintenanceOperation.equals("copyFrom")) {
			} else {
				request.getSession().setAttribute("entityAccessListInitial",
						collEntityAccessGuiInitial);
			}

			request.getSession().setAttribute("entityComboForCcyGrpAccess",
					entityComboForCcyGrpAccess);

			log.debug("Exiting from 'putEntityAccessDetailsInSession' Method ");
		} catch (Exception e) {
			log.debug("Exception in RoleAction.putEntityAccessDetailsInSession"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
		}
	}

	/**
	 * This method forms the LabelValueBean for the EntityDropDown in
	 * WorkQAccess screen
	 *
	 * @param request
	 */
	private String getEntityComboForWorkQAccess(HttpServletRequest request) {
		Collection entityComboForCcyGrpAccess = (Collection) (request
				.getSession().getAttribute("entityComboForCcyGrpAccess"));
		int i = 0;
		String entityId = null;

		ArrayList entitiesList = new ArrayList();

		if ((entityComboForCcyGrpAccess != null)
				&& (entityComboForCcyGrpAccess.size() > 0)) {
			Iterator itr = (entityComboForCcyGrpAccess).iterator();

			while (itr.hasNext()) {
				// String ent =(String)(itr.next());
				EntityAccessGui entGui = (EntityAccessGui) (itr.next());
				entitiesList.add(new LabelValueBean(entGui.getEntityName(),
						entGui.getEntityId()));

				if (i == 0) {
					entityId = entGui.getEntityId();
				}

				i++;
			}
//			Mantis M7082 :  to fix Unordered in Dropdown Menus : Only need to comment or remove the line below.
//			Collections.sort(entitiesList);

			request.setAttribute("entities", entitiesList);
		}

		return entityId;
	}

	/**
	 * This function sets the status of different screens
	 *
	 * @param req
	 * @param menuAOStatus
	 * @param entityALStatus
	 * @param workQAStatus
	 * @param sweepLCStatus
	 * @param copyFromStatu
	 * @param saveStatus
	 * @param cancelStatus
	 * @param viewStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String menuAOStatus,
								 String entityALStatus, String workQAStatus, String sweepLCStatus,
								 String copyFromStatu, String saveStatus, String cancelStatus,
								 String viewStatus) {
		req.setAttribute(SwtConstants.MENUAO_BUT_STS, menuAOStatus);
		req.setAttribute(SwtConstants.ENTITYAL_BUT_STS, entityALStatus);
		req.setAttribute(SwtConstants.WORKQA_BUT_STS, workQAStatus);
		req.setAttribute(SwtConstants.SWEEPLC_BUT_STS, sweepLCStatus);
		req.setAttribute(SwtConstants.COPYFROM_BUT_STS, copyFromStatu);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.VIEW_BUT_STS, viewStatus);
	}

	public String cancel()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'cancel' Method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = new Role();
			setRole(role);

			// Clearing all the temporary collection from session
			clearSessionVariablesForRole(request);

			request.setAttribute("methodName", "GBP");
			request.setAttribute("cancelFlag", "yes");
			log.debug("Exiting 'cancel' Method");
			putDefaultEntityAccessInReq(request);

			return getView("add");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "cancel", RoleAction.class), request, "");
			return getView("fail");
		}
	}


	public String cancelFromParent() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("Entering 'cancel' Method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			Role role = new Role();
			setRole(role);

			// Clearing all the temporary collection from session
			clearSessionVariablesForRole(request);

			request.setAttribute("methodName", "GBP");
			request.setAttribute("cancelFlag", "yes");
			log.debug("Exiting 'cancel' Method");
			putDefaultEntityAccessInReq(request);
			// Getting Role Details from Role Manager
			Collection collRole = roleMgr.getRoleDetails(role);
			request.setAttribute("RoleDetailsList", collRole);
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			request.setAttribute("accessAltered", request.getParameter("accessAltered"));
			return getView("success");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "cancel", RoleAction.class), request,
					"");
			return getView("fail");
		}
	}

	private void clearSessionVariablesForRole(HttpServletRequest request) {
		log.debug("Entering 'clearSessionVariables' Method");

		request.getSession().setAttribute("menuAccessOptionsFlag", "0");
		request.getSession().setAttribute("itemDescList", null);
		request.getSession().setAttribute("initialIitemDescList", null);

		request.getSession().setAttribute("accountAccesssFlag", "0");
		request.getSession().setAttribute("roleAccountAccessList", null);

		request.getSession().setAttribute("entityAccessListFlag", "0");
		request.getSession().setAttribute("roleEntityAccessList", null);
		request.getSession().setAttribute("entityAccessListInitial", null);

		request.getSession().setAttribute("workQAccessFlag", "0");
		request.getSession().setAttribute("sessionWorkQAccessDetails", null);
		request.getSession().setAttribute("sessionWorkQAccessDetailsInitial",
				null);

		request.getSession().setAttribute("sessionSweepLimitDetails", null);
		request.getSession().setAttribute("sessionSweepLimitDetailsInitial",
				null);

		request.getSession().setAttribute("saveEnableMenu", "GBP");
		request.getSession().setAttribute("saveEnableEntity", "GBP");

		request.getSession().setAttribute("copyFromFlag", "false");
		request.getSession().setAttribute("roleMaintenanceOperation", null);
		request.getSession().setAttribute("roleIdInSession", null);
		request.getSession().setAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST, null);

		request.getSession().setAttribute(SwtConstants.LOCATION_ACCESS_LIST,
				null);
		request.getSession().setAttribute(
				SwtConstants.INITIAL_LOCATION_ACCESS_LIST, null);
		request.getSession().setAttribute(
				SwtConstants.ALL_LOCATION_ACCESS_LIST, null);

		String roleId = (String) request.getSession().getAttribute(
				"roleIdInSession");
		request.getSession().setAttribute("listAccessUpdateInSession", null);

		request.getSession().setAttribute("listAccessAddInSession", null);

		log.debug("Exiting 'clearSessionVariables' Method");
	}

	private Collection getStandardWorkQAccessCollection(
			Collection colWorkQAccess) {
		log.debug("Entering  'getStandardWorkQAccessCollection' Method");

		if (colWorkQAccess != null) {
			Iterator itrWorkQAccess = colWorkQAccess.iterator();

			while (itrWorkQAccess.hasNext()) {
				WorkQAccess wqa = (WorkQAccess) (itrWorkQAccess.next());

				// Setting the confirmed Queue Status
				if ((wqa.getConfirmMatchQualA() != null)
						&& !(wqa.getConfirmMatchQualA()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualA(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualA() == null) {
					wqa.setConfirmMatchQualA(SwtConstants.NO);
				}

				if ((wqa.getConfirmMatchQualB() != null)
						&& !(wqa.getConfirmMatchQualB()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualB(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualB() == null) {
					wqa.setConfirmMatchQualB(SwtConstants.NO);
				}

				if ((wqa.getConfirmMatchQualC() != null)
						&& !(wqa.getConfirmMatchQualC()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualC(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualC() == null) {
					wqa.setConfirmMatchQualC(SwtConstants.NO);
				}

				if ((wqa.getConfirmMatchQualD() != null)
						&& !(wqa.getConfirmMatchQualD()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualD(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualD() == null) {
					wqa.setConfirmMatchQualD(SwtConstants.NO);
				}

				if ((wqa.getConfirmMatchQualE() != null)
						&& !(wqa.getConfirmMatchQualE()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualE(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualE() == null) {
					wqa.setConfirmMatchQualE(SwtConstants.NO);
				}

				if ((wqa.getConfirmMatchQualZ() != null)
						&& !(wqa.getConfirmMatchQualZ()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setConfirmMatchQualZ(SwtConstants.NO);
				} else if (wqa.getConfirmMatchQualZ() == null) {
					wqa.setConfirmMatchQualZ(SwtConstants.NO);
				}

				// Setting the suspended Queue Status
				if ((wqa.getSuspMatchQualA() != null)
						&& !(wqa.getSuspMatchQualA()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualA(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualA() == null) {
					wqa.setSuspMatchQualA(SwtConstants.NO);
				}

				if ((wqa.getSuspMatchQualB() != null)
						&& !(wqa.getSuspMatchQualB()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualB(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualB() == null) {
					wqa.setSuspMatchQualB(SwtConstants.NO);
				}

				if ((wqa.getSuspMatchQualC() != null)
						&& !(wqa.getSuspMatchQualC()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualC(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualC() == null) {
					wqa.setSuspMatchQualC(SwtConstants.NO);
				}

				if ((wqa.getSuspMatchQualD() != null)
						&& !(wqa.getSuspMatchQualD()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualD(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualD() == null) {
					wqa.setSuspMatchQualD(SwtConstants.NO);
				}

				if ((wqa.getSuspMatchQualE() != null)
						&& !(wqa.getSuspMatchQualE()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualE(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualE() == null) {
					wqa.setSuspMatchQualE(SwtConstants.NO);
				}

				if ((wqa.getSuspMatchQualZ() != null)
						&& !(wqa.getSuspMatchQualZ()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setSuspMatchQualZ(SwtConstants.NO);
				} else if (wqa.getSuspMatchQualZ() == null) {
					wqa.setSuspMatchQualZ(SwtConstants.NO);
				}

				// Setting the offered Queue Status
				if ((wqa.getOfferMatchQualA() != null)
						&& !(wqa.getOfferMatchQualA()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOfferMatchQualA(SwtConstants.NO);
				} else if (wqa.getOfferMatchQualA() == null) {
					wqa.setOfferMatchQualA(SwtConstants.NO);
				}

				if ((wqa.getOfferMatchQualB() != null)
						&& !(wqa.getOfferMatchQualB()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOfferMatchQualB(SwtConstants.NO);
				} else if (wqa.getOfferMatchQualB() == null) {
					wqa.setOfferMatchQualB(SwtConstants.NO);
				}

				if ((wqa.getOfferMatchQualC() != null)
						&& !(wqa.getOfferMatchQualC()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOfferMatchQualC(SwtConstants.NO);
				} else if (wqa.getOfferMatchQualC() == null) {
					wqa.setOfferMatchQualC(SwtConstants.NO);
				}

				if ((wqa.getOfferMatchQualD() != null)
						&& !(wqa.getOfferMatchQualD()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOfferMatchQualD(SwtConstants.NO);
				} else if (wqa.getOfferMatchQualD() == null) {
					wqa.setOfferMatchQualD(SwtConstants.NO);
				}

				if ((wqa.getOfferMatchQualE() != null)
						&& !(wqa.getOfferMatchQualE()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOfferMatchQualE(SwtConstants.NO);
				} else if (wqa.getOfferMatchQualE() == null) {
					wqa.setOfferMatchQualE(SwtConstants.NO);
				}

				// Setting outStanding queue
				if ((wqa.getOutstanding() != null)
						&& !(wqa.getOutstanding()
						.equalsIgnoreCase(SwtConstants.YES))) {
					wqa.setOutstanding(SwtConstants.NO);
				} else if (wqa.getOutstanding() == null) {
					wqa.setOutstanding(SwtConstants.NO);
				}
			}
		}

		log.debug("Exiting  getStandardWorkQAccessCollection Method");

		return colWorkQAccess;
	}

	private void putDefaultEntityAccessInReq(HttpServletRequest request) {
		log.debug("Entering into the putDefaultEntityAccessInReq method");

		String entityId = SwtUtil.getUserCurrentEntity(request.getSession());
		Collection collEntity = SwtUtil.getUserEntityAccessList(request
				.getSession());
		int entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);

		if (entityAccess == SwtConstants.ENTITY_FULL_ACCESS) {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}

		log.debug("Exiting from the putDefaultEntityAccessInReq method");
	}

	/**
	 *
	 * @return
	 * @throws SwtException
	 */
	public String displayCurrencyGroupAccessDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'displayCurrencyGroupAccessDetails' Method");

			String entityId = null;
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			CurrencyGroup currencyGroupForm = (CurrencyGroup) getCurrencyGroup();
			entityId = currencyGroupForm.getId().getEntityId();

			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			String entityIdReturned = getEntityComboForWorkQAccess(request);

			if ((entityId == null) || entityId.equals("")) {
				entityId = entityIdReturned;
			}

			CurrencyGroup ccyGrp = new CurrencyGroup();
			ccyGrp.getId().setEntityId(entityId);

			setCurrencyGroup(ccyGrp);

			/*
			 * Getting value of roleMaintenanceOperationFlag to get the name of
			 * operation operation can be "add", "change" ,"copyFrom"
			 */
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			/* If operation is "add" */
			if (roleMaintenanceOperationFlag.equals("add")) {
				Collection coll = putCurrencyGroupItemInReq(request);
				Collection collFiltered = filterCurrencyGroupByEntity(coll,
						entityId);
				putEntityAccessInReq(request, entityId);
				request.setAttribute("currencyGroupList", collFiltered);
			}

			/* If operation is "change" */
			if (roleMaintenanceOperationFlag.equals("change")
					|| roleMaintenanceOperationFlag.equals("copyFrom")) {
				String roleId = (String) request.getSession().getAttribute(
						"roleIdInSession");

				putCurrencyGroupItemInReq(request);
				Collection coll = (Collection) request.getSession()
						.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
				Collection filteredColl = filterCurrencyGroupByEntity(coll,
						entityId);
				request.setAttribute("currencyGroupList", filteredColl);
				putEntityAccessInReq(request, entityId);
			}

			// Checking the value of the "isViewRole" flag
			String isViewRole = request.getParameter("isViewRole");

			if ((isViewRole != null) && isViewRole.equals("yes")) {
				request.setAttribute("isViewRole", "yes");
			}
			putSweepButtonStatusInReq(request);
			return getView("ccyGrpAccess");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "displayCurrencyGripuAccessList", RoleAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @return - Collection of objects of type CurrencyGroupAccessGui.java
	 * @throws SwtException
	 */
	private Collection putCurrencyGroupItemInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("Entering into putCurrencyGroupItemInReq() method");

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();

		Collection allCurrGrpList = new ArrayList();
		Collection collCcyGrpItems = (Collection) request.getSession()
				.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);

		Collection entityComboForCcyGrpAccess = (Collection) (request
				.getSession().getAttribute("entityComboForCcyGrpAccess"));
		boolean recordFound = false;

		/**
		 * Adding any initial currencyGroupAccessDetail present into the
		 * newCollection
		 */
		if (collCcyGrpItems != null) {
			allCurrGrpList.addAll(collCcyGrpItems);
		}

		/**
		 * If a entityId is present in entityComboForCcyGrpAccess then also add
		 * currncyGroup records corresponding to it in the
		 * sessionCurrencyGroupDetails.
		 */
		if (entityComboForCcyGrpAccess != null) {
			Iterator itrEntityList = entityComboForCcyGrpAccess.iterator();

			while (itrEntityList.hasNext()) {
				EntityAccessGui entGui = (EntityAccessGui) (itrEntityList
						.next());
				String entityIdFromEntityList = entGui.getEntityId();

				if (collCcyGrpItems != null) {

					Iterator itrCurrGrpList = collCcyGrpItems.iterator();

					while (itrCurrGrpList.hasNext()) {
						CurrencyGroupAccessGui cgGui = (CurrencyGroupAccessGui) (itrCurrGrpList
								.next());

						if (cgGui.getEntityId().equals(entityIdFromEntityList)) {
							recordFound = true;
							break;
						}
					}

					if (!recordFound) {
						Collection currGrpDetails = roleMgr
								.getCurrencyGroupDetailsByEntityId(hostId,
										entityIdFromEntityList);
						Collection convertedColl = convertIntoCurrencyGrpGuiColl(currGrpDetails);
						allCurrGrpList.addAll(convertedColl);
					}

					recordFound = false;
				} else {
					Collection currGrpDetails = roleMgr
							.getCurrencyGroupDetailsByEntityId(hostId,
									entityIdFromEntityList);
					Collection convertedColl = convertIntoCurrencyGrpGuiColl(currGrpDetails);
					allCurrGrpList.addAll(convertedColl);
				}
			}
		}

		if ((entityComboForCcyGrpAccess == null)
				|| (entityComboForCcyGrpAccess.size() == 0)) {
			allCurrGrpList = new ArrayList();
		} else {
			Collection tempList = new ArrayList();

			if (allCurrGrpList != null) {
				Iterator itrCurrGrpList = allCurrGrpList.iterator();

				while (itrCurrGrpList.hasNext()) {
					CurrencyGroupAccessGui cgGui = (CurrencyGroupAccessGui) itrCurrGrpList
							.next();
					Iterator itrEntityList = entityComboForCcyGrpAccess
							.iterator();

					while (itrEntityList.hasNext()) {
						EntityAccessGui eaGui = (EntityAccessGui) (itrEntityList
								.next());

						if (cgGui.getEntityId().equals(eaGui.getEntityId())) {
							tempList.add(cgGui);

							break;
						}
					}
				}
			}

			allCurrGrpList = tempList;
		}

		request.getSession().setAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST, allCurrGrpList);

		Collection refreshedColl = refreshCcyGrpSessionColl(hostId, request);

		request.getSession().setAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST, refreshedColl);

		request.setAttribute("currencyGroupList", refreshedColl);
		log.debug("Exiting from putCurrencyGroupItemInReq() method");

		return refreshedColl;
	}

	/**
	 * This function take objects of type CurrencyGroup.java as Input and
	 * convert them into objects of type CurrencyGroupAccessGui.java
	 *
	 * @param inputColl -
	 *            Collection of objects of type CurrencyGroup.java
	 * @return - Collection of objects of type CurrencyGroupAccessGui.java
	 */
	private Collection convertIntoCurrencyGrpGuiColl(Collection inputColl) {
		log.debug("Entering into convertIntoCurrencyGrpGuiColl() ");

		Collection outColl = new ArrayList();

		if (inputColl != null) {
			Iterator itr = inputColl.iterator();

			while (itr.hasNext()) {
				CurrencyGroup ccyGrp = (CurrencyGroup) itr.next();
				CurrencyGroupAccessGui ccyGrpAccess = new CurrencyGroupAccessGui();
				ccyGrpAccess.setEntityId(ccyGrp.getId().getEntityId());
				ccyGrpAccess.setCurrencyGroupId(ccyGrp.getId()
						.getCurrencyGroupId());
				ccyGrpAccess
						.setCurrencyGroupName(ccyGrp.getCurrencyGroupName());
				ccyGrpAccess.setCcyGrpAccessHTML1("");
				ccyGrpAccess.setCcyGrpAccessHTML2("");
				ccyGrpAccess.setCcyGrpAccessHTML3("checked");
				outColl.add(ccyGrpAccess);
			}
		}

		log.debug("Exiting from convertIntoCurrencyGrpGuiColl");

		return outColl;
	}

	/**
	 * This function filters the CurrencyGroupAccess details according to
	 * entityId
	 *
	 * @param inputColl -
	 *            The collection of objects of type CurrencyGroupAccessGui.java
	 *            that are present in the sessionCurrencyGroupDetails.
	 * @param entityId -
	 *            Entity Identifier
	 * @return - ArrayList object.
	 */
	private Collection filterCurrencyGroupByEntity(Collection inputColl,
												   String entityId) {
		log.debug("Entering into filterCurrencyGroupByEntity() ");

		Collection outColl = new ArrayList();

		if (inputColl != null) {
			Iterator itr = inputColl.iterator();

			while (itr.hasNext()) {
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) itr
						.next();

				if (ccyGrpGui.getEntityId().equals(entityId)) {
					outColl.add(ccyGrpGui);
				}

			}
		}

		log.debug("Exiting from filterCurrencyGroupByEntity");
		log.debug("Exiting from filterCurrencyGroupByEntity() ");
		return outColl;
	}

	/**
	 * This function puts the AccessType of a particular entity into request
	 * object so that In the currency Group access screen validation can be
	 * provided
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @param entityId -
	 *            Entity identifier
	 */
	private void putEntityAccessInReq(HttpServletRequest request,
									  String entityId) {
		log.debug("Entering into putEntityAccessInReq() ");

		String entityAccess = "";
		Collection roleEntityAccessList = (Collection) (request.getSession()
				.getAttribute("roleEntityAccessList"));
		if ((roleEntityAccessList != null) && (roleEntityAccessList.size() > 0)) {
			Iterator itr = (roleEntityAccessList).iterator();
			while (itr.hasNext()) {
				EntityAccessGui entGui = (EntityAccessGui) (itr.next());
				if (entGui.getEntityId().equals(entityId)) {
					if (entGui.getEntityAccessHTML1().equals("checked")) {
						entityAccess = SwtConstants.ENTITY_FULL_ACCESS + "";
					} else if (entGui.getEntityAccessHTML2().equals("checked")) {
						entityAccess = SwtConstants.ENTITY_READ_ACCESS + "";
					} else if (entGui.getEntityAccessHTML3().equals("checked")) {
						entityAccess = SwtConstants.ENTITY_NO_ACCESS + "";
					}
					break;
				}
			}
		}
		request.setAttribute("entAccess", entityAccess);
		log.debug("Exiting from putEntityAccessInReq() " + entityAccess + "==");

	}

	/**
	 * This function saves the CurrencyGroupAccess details that user provides on
	 * the front end, also updating the sweep Limit details accordingly
	 *
	 * @throws SwtException -
	 *             Exception object
	 */
	public String saveCurrencyGroupAccessDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'saveCurrencyGroupAccessDetails' method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			CurrencyGroup currGrp = (CurrencyGroup) getCurrencyGroup();
			String entityId = currGrp.getId().getEntityId();
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			String roleId = (String) request.getSession().getAttribute(
					"roleIdInSession");

			// This is needed to enable or disable "save" button on add role
			// screen
			String roleMaintenanceOperationFlag = (String) request.getSession()
					.getAttribute("roleMaintenanceOperation");

			if (roleMaintenanceOperationFlag.equals("change")) {

				request.setAttribute("methodName", "update");
			} else {
				request.setAttribute("methodName", "save");
			}

			/*
			 * Here we creates an ArrayList which contains a list of those
			 * entities which has full or viewOnly access in entity access List.
			 * This is necessary to update any CurrencyGroupAccess Record that
			 * is already existing
			 *
			 */
			Collection collCurrencyAccess = (Collection) request.getSession()
					.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
			Collection collFiltered = filterCurrencyGroupByEntity(
					collCurrencyAccess, entityId);
			int i = 0;
			Iterator itr = collFiltered.iterator();
			/**
			 * This function updates any sweep Limit record present with invalid
			 * accessType
			 */
			checkSwpLimitForCcyGrpFullAccess(request);

			/**
			 * Now saving the details provide by user into CurrencyGroupAccess
			 * details present in session
			 */
			itr = collFiltered.iterator();
			while (itr.hasNext()) {
				CurrencyGroupAccessGui currencyAccessGui = (CurrencyGroupAccessGui) (itr
						.next());
				String currencyGrpId = currencyAccessGui.getEntityId();
				String radioButtonStatus = request
						.getParameter("ccyGrpAccessList" + (i + 1));


				if (radioButtonStatus != null) {
					currencyAccessGui.setCcyGrpAccessHTML1("");
					currencyAccessGui.setCcyGrpAccessHTML2("");
					currencyAccessGui.setCcyGrpAccessHTML3("");

					if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_FULL_ACCESS))) {
						currencyAccessGui.setCcyGrpAccessHTML1("checked");
					} else if (radioButtonStatus.equals(String
							.valueOf(SwtConstants.ENTITY_READ_ACCESS))) {
						currencyAccessGui.setCcyGrpAccessHTML2("checked");
					} else {
						currencyAccessGui.setCcyGrpAccessHTML3("checked");
					}
				}
				i++;
			}

			putEntityAccessInReq(request, entityId);
			getEntityComboForWorkQAccess(request);
			request.setAttribute("currencyGroupList", collFiltered);
			request.setAttribute("parentFormRefresh", "yes");
			/* End code added by Selva Kumar on 05 June 2008 */
			putSweepButtonStatusInReq(request);
			return getView("ccyGrpAccess");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "saveCurrencyGropuAccessList", RoleAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * This function updates the session Collection for CurrencyGroupAccess
	 * Details according to details present in database
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @param hostId -
	 *            Host Identifier
	 * @param roleId -
	 *            Role Identifier
	 * @throws SwtException -
	 *             SwtException object
	 */
	private void putCurrencyAccessDetailsGuiInSession(
			HttpServletRequest request, String hostId, String roleId)
			throws SwtException {
		log.debug("Entering 'putCurrencyAccessDetailsGuiInSession' method");

		Collection currGrpAccessDetails = roleMgr
				.getCurrencyGroupAccessDetails(hostId, roleId);
		Collection currGrp = (Collection) request.getSession().getAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
		Collection outColl = new ArrayList(); // Holds output of the function.

		/**
		 * updating the session Collection for CurrencyGroupAccess Details
		 * according to details present in database
		 */
		if (currGrpAccessDetails != null) {
			Iterator itrAcc = currGrpAccessDetails.iterator();

			while (itrAcc.hasNext()) {
				CurrencyGroupAccess ccyGrpAcc = (CurrencyGroupAccess) itrAcc
						.next();
				Iterator itr = currGrp.iterator();
				while (itr.hasNext()) {
					CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itr
							.next());
					boolean recordMatched = ccyGrpAcc.getId().getEntityId()
							.equals(ccyGrpGui.getEntityId())
							&& ccyGrpAcc.getId().getCurrencyGroupId().equals(
							ccyGrpGui.getCurrencyGroupId());
					if (recordMatched) {
						String accessType = ccyGrpAcc.getAccessId();
						if (ccyGrpAcc
								.getAccessId()
								.equals(
										String
												.valueOf(SwtConstants.CURRENCYGRP_FULL_ACCESS))) {
							ccyGrpGui.setCcyGrpAccessHTML1("checked");
							ccyGrpGui.setCcyGrpAccessHTML2("");
							ccyGrpGui.setCcyGrpAccessHTML3("");
						} else if (ccyGrpAcc
								.getAccessId()
								.equals(
										String
												.valueOf(SwtConstants.CURRENCYGRP_READ_ACCESS))) {
							ccyGrpGui.setCcyGrpAccessHTML1("");
							ccyGrpGui.setCcyGrpAccessHTML2("checked");
							ccyGrpGui.setCcyGrpAccessHTML3("");
						}
						outColl.add(ccyGrpGui);
						request.getSession()
								.setAttribute(
										SwtConstants.ROLE_CURRENCY_ACCESS_LIST,
										outColl);
						break;
					}
				}
			}

		}


		/**
		 * Also keeping the initial details of the CurrencyGroupAccess In
		 * session
		 */
		String roleMaintenanceOperation = (String) request.getSession()
				.getAttribute("roleMaintenanceOperation");

		if ((roleMaintenanceOperation != null)
				&& roleMaintenanceOperation.equals("copyFrom")) {
		} else {
			Collection temp = (ArrayList) SwtUtil.copy(currGrpAccessDetails);
			request.getSession().setAttribute(
					"currencyGropuAccessDetailsInitial", temp);
		}

		log.debug("Exiting 'putCurrencyAccessDetailsGuiInSession' method");
	}

	/**
	 * This function compares the 'New CurrencyGroupAccess Records collection'
	 * and 'Exiting CurrencyGroupAccess Records collection Same as that of
	 * database)'. It then finds records which are to be saved, updated and
	 * delted.
	 *
	 * @param ccyGrpAccessList -
	 *            Collection of Objects of CurrencyGroupAccessGui.java - final
	 *            collection
	 * @param collCcyGrpAccessInitial -
	 *            Collection of Objects of CurrencyGroupAccess.java(same as that
	 *            in DB table-- Initial collection
	 * @param colCcyGrpAccessSave -
	 *            Collection which will hold records that are to be saved.
	 * @param colCcyGrpAccessUpdate -
	 *            Collection which will hold records that are to be updated.
	 * @param colCcyGrpAccessDelete -
	 *            Collection which will hold records that are to be deleted.
	 * @param hostId -
	 *            Host Identifier
	 * @param roleId -
	 *            Role Identifier
	 */
	private void findRecordsForSaveUpdateDelete(Collection ccyGrpAccessList,
												Collection collCcyGrpAccessInitial, ArrayList colCcyGrpAccessSave,
												ArrayList colCcyGrpAccessUpdate, ArrayList colCcyGrpAccessDelete,
												String hostId, String roleId) {
		log.debug("Entering into findRecordsForSaveUpdateDelete() method");

		Collection recordsToBeSaved = new ArrayList(); // It holds that
		// CurrencyGroupAccessGui
		// objects that are to
		// be saved.
		Collection recordsToBeUpdated = new ArrayList(); // It holds that
		// CurrencyGroupAccessGui
		// objects that are
		// to be updated.

		/**
		 * If the user provided collection and intial collection of
		 * CurrencyGroupAccess Records are null then return
		 */
		if (ccyGrpAccessList == null && collCcyGrpAccessInitial == null) {
			return;
		}

		/**
		 * If user provided collection is not null and initial collection is
		 * null then save all records present in the user provided collection.
		 * IF user provided collection is not null then remove all the records
		 * from it which has their AccessType as No Access as those are not to
		 * be saved in the database.
		 */
		if (ccyGrpAccessList != null && collCcyGrpAccessInitial == null) {
			Iterator itr = ccyGrpAccessList.iterator();
			CurrencyGroupAccess ccyGrpAccess = new CurrencyGroupAccess();
			while (itr.hasNext()) {

				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itr
						.next());
				if (!ccyGrpGui.getCcyGrpAccessHTML3().equals("checked")) {
					ccyGrpAccess.getId().setEntityId(ccyGrpGui.getEntityId());
					ccyGrpAccess.getId().setCurrencyGroupId(
							ccyGrpGui.getCurrencyGroupId());
					ccyGrpAccess.getId().setHostId(hostId);
					ccyGrpAccess.getId().setRoleId(roleId);
					colCcyGrpAccessSave.add(ccyGrpAccess);
				}
			}

		}

		/**
		 * If user provided collection is null and intial collection is not null
		 * then remove all the records which are present in initial collection.
		 */
		if (ccyGrpAccessList == null && collCcyGrpAccessInitial != null) {
			Iterator itr = collCcyGrpAccessInitial.iterator();
			while (itr.hasNext()) {

				CurrencyGroupAccess ccyGrpAccess = (CurrencyGroupAccess) (itr
						.next());
				colCcyGrpAccessDelete.add(ccyGrpAccess);

			}
		}

		/**
		 * If both user provided collection and intial collection are not null
		 * then first, remove all records from user provided collection which
		 * has AccessType as NoAccess, as these records are not to be saved in
		 * the database.
		 */
		if (ccyGrpAccessList != null && collCcyGrpAccessInitial != null) {
			Collection temp = new ArrayList(); // Stores only those records for
			Iterator itrInitial = null;

			Iterator itrFinal = ccyGrpAccessList.iterator();
			while (itrFinal.hasNext()) {
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itrFinal
						.next());
				if (!ccyGrpGui.getCcyGrpAccessHTML3().equals("checked")) {
					temp.add(ccyGrpGui);
				}
			}

			ccyGrpAccessList = temp;
			itrFinal = ccyGrpAccessList.iterator();

			/**
			 * Comparing both the collections User Provided Collection (UPC) and
			 * Initial Collection (IC) If any record is found in UPC and not in
			 * IC then it is to be saved. If any record is present in both the
			 * collections with diffence in AccessID only then it is to be
			 * updated If any record is prestnt in IC but not in UPC then it is
			 * to be delted.
			 */
			while (itrFinal.hasNext()) {
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itrFinal
						.next());
				itrInitial = collCcyGrpAccessInitial.iterator();
				boolean recordMatched = false;
				CurrencyGroupAccess ccyGrpAccess = null;

				while (itrInitial.hasNext()) {
					ccyGrpAccess = (CurrencyGroupAccess) (itrInitial.next());
					recordMatched = ccyGrpAccess.getId().getEntityId().equals(
							ccyGrpGui.getEntityId())
							&& ccyGrpAccess.getId().getCurrencyGroupId()
							.equals(ccyGrpGui.getCurrencyGroupId());

					if (recordMatched) {
						String accessId = ccyGrpAccess.getAccessId();
						String finalAccessId = "";
						if (ccyGrpGui.getCcyGrpAccessHTML1().equals("checked")) {
							finalAccessId = String
									.valueOf(SwtConstants.CURRENCYGRP_FULL_ACCESS);
						} else if (ccyGrpGui.getCcyGrpAccessHTML2().equals(
								"checked")) {
							finalAccessId = String
									.valueOf(SwtConstants.CURRENCYGRP_READ_ACCESS);
						}
						if (!accessId.equals(finalAccessId)) {
							recordsToBeUpdated.add(ccyGrpGui);
						}
						break;
					}

				}
				if (recordMatched) {
					collCcyGrpAccessInitial.remove(ccyGrpAccess);
				} else {
					recordsToBeSaved.add(ccyGrpGui);
				}
			}
		}

		/**
		 * recordsToBeSaved is a collection which has objects of the type
		 * CurrencyGroupAccessGui.java that are to be saved, convetring them to
		 * the type CurrencyGroupAccess.java so that they can be saved in
		 * database
		 */
		if (recordsToBeSaved != null) {
			Iterator itr = recordsToBeSaved.iterator();
			CurrencyGroupAccess ccyGrpAcc = new CurrencyGroupAccess();
			while (itr.hasNext()) {
				ccyGrpAcc = new CurrencyGroupAccess();
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itr
						.next());
				ccyGrpAcc.getId().setHostId(hostId);
				ccyGrpAcc.getId().setRoleId(roleId);
				ccyGrpAcc.getId().setCurrencyGroupId(
						ccyGrpGui.getCurrencyGroupId());
				ccyGrpAcc.getId().setEntityId(ccyGrpGui.getEntityId());
				if (ccyGrpGui.getCcyGrpAccessHTML1().equals("checked")) {
					ccyGrpAcc.setAccessId(String
							.valueOf(SwtConstants.CURRENCYGRP_FULL_ACCESS));
				} else if (ccyGrpGui.getCcyGrpAccessHTML2().equals("checked")) {
					ccyGrpAcc.setAccessId(String
							.valueOf(SwtConstants.CURRENCYGRP_READ_ACCESS));
				}

				colCcyGrpAccessSave.add(ccyGrpAcc);
			}
		}

		/**
		 * recordsToBeUpdated is a collection which has objects of the type
		 * CurrencyGroupAccessGui.java that are to be updated, convering them to
		 * the type CurrencyGroupAccess.java so that they can be updated in
		 * database
		 */
		if (recordsToBeUpdated != null) {
			Iterator itr = recordsToBeUpdated.iterator();
			CurrencyGroupAccess ccyGrpAcc = new CurrencyGroupAccess();
			while (itr.hasNext()) {
				ccyGrpAcc = new CurrencyGroupAccess();
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itr
						.next());
				ccyGrpAcc.getId().setHostId(hostId);
				ccyGrpAcc.getId().setRoleId(roleId);
				ccyGrpAcc.getId().setCurrencyGroupId(
						ccyGrpGui.getCurrencyGroupId());
				ccyGrpAcc.getId().setEntityId(ccyGrpGui.getEntityId());

				if (ccyGrpGui.getCcyGrpAccessHTML1().equals("checked")) {
					ccyGrpAcc.setAccessId(String
							.valueOf(SwtConstants.CURRENCYGRP_FULL_ACCESS));
				} else if (ccyGrpGui.getCcyGrpAccessHTML2().equals("checked")) {
					ccyGrpAcc.setAccessId(String
							.valueOf(SwtConstants.CURRENCYGRP_READ_ACCESS));
				}

				colCcyGrpAccessUpdate.add(ccyGrpAcc);
			}
		}

		/**
		 * The above code leaves only those records in collCcyGrpAccessInitial
		 * collection which are to be deleted.
		 */
		if (collCcyGrpAccessInitial != null) {
			Iterator itr = collCcyGrpAccessInitial.iterator();
			CurrencyGroupAccess ccyGrpAcc = new CurrencyGroupAccess();
			while (itr.hasNext()) {
				ccyGrpAcc = (CurrencyGroupAccess) (itr.next());
				colCcyGrpAccessDelete.add(ccyGrpAcc);
			}
		}
		ccyGrpAccessList1 = ccyGrpAccessList;
		log.debug("Exiting from findRecordsForSaveUpdateDelete() ");
	}

	/**
	 * The sweepLimit button will be enabled iff there exists a CurrencyGroup
	 * with AccessType as Full Access.
	 *
	 * @param request -
	 *            HttpServletRequest object
	 */
	private void putSweepButtonStatusInReq(HttpServletRequest request) {
		log.debug("Entering 'putSweepButtonStatusInReq' method");
		Collection coll = (Collection) request.getSession().getAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
		String limitButtonStatus = "disable";
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) (itr
						.next());

				if (ccyGrpGui.getCcyGrpAccessHTML1().equals("checked")) {
					limitButtonStatus = "enable";
					break;
				}
			}
		}

		request.setAttribute("limitButtonStatus", limitButtonStatus);
		log.debug("Exiting 'putSweepButtonStatusInReq' method");
	}

	/**
	 * This function checks if for an entity whose accessType is ReadOnly Access
	 * there exists any CurrencyGroupRecord whose AccessType is Full access. If
	 * such a record of currencyGroup is found then its AccessType is also made
	 * 'ReadOnly Access'.
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @param entityId -
	 *            Entity Identifier
	 * @throws SwtException -
	 *             SwtException object
	 */
	private void checkCcyGrpAccessForEntityReadAccess(
			HttpServletRequest request, String entityId) throws SwtException {
		log.debug("Entering 'checkCcyGrpAccessForEntityReadAccess' method");

		Collection temp = null;
		Collection coll = (Collection) request.getSession().getAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST);

		/**
		 * If a currencyGroup Record has AccessType as FullAccess and its
		 * EntityAccess is ReadAccess then changing the AccessType of
		 * currencyGroup Record to Read Access.
		 */
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				temp = new ArrayList();
				CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itr
						.next());
				if (ccyGrpAccGui.getEntityId().equals(entityId)) {
					if (ccyGrpAccGui.getCcyGrpAccessHTML1().equals("checked")) {
						ccyGrpAccGui.setCcyGrpAccessHTML1("");
						ccyGrpAccGui.setCcyGrpAccessHTML2("checked");
						ccyGrpAccGui.setCcyGrpAccessHTML3("");
						temp.add(ccyGrpAccGui);
						checkSwpLimitForCcyGrpFullAccess(request);
					}
				}
			}
		}

		log.debug("Exiting 'checkCcyGrpAccessForEntityReadAccess' method");
	}

	/**
	 * This function checks if for an entity whose accessType is No Access there
	 * exists any CurrencyGroupRecord whose AccessType is Full/Read only access.
	 * If such a record of currencyGroup is found then its AccessType is also
	 * made 'NO Access'.
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @param entityId -
	 *            Entity Identifier
	 * @throws SwtException -
	 *             SwtException object
	 */
	private void checkCcyGrpAccessForEntityNoAccess(HttpServletRequest request,
													String entityId) throws SwtException {
		log.debug("Entering 'checkCcyGrpAccessForEntityNoAccess' method");

		Collection temp = null;
		Collection coll = (Collection) request.getSession().getAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST);

		/**
		 * Setting access Type of all CurrencyGroup records to 'NO Access' whose
		 * entityId matches the entityId provided as input paramter.
		 */
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				temp = new ArrayList();
				CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itr
						.next());
				if (ccyGrpAccGui.getEntityId().equals(entityId)) {
					ccyGrpAccGui.setCcyGrpAccessHTML1("");
					ccyGrpAccGui.setCcyGrpAccessHTML2("");
					ccyGrpAccGui.setCcyGrpAccessHTML3("checked");
					temp.add(ccyGrpAccGui);
					checkSwpLimitForCcyGrpFullAccess(request);
				}
			}
		}

		log.debug("Exiting 'checkCcyGrpAccessForEntityNoAccess' method");

	}

	/**
	 * This function removes any existing sweepLimits record, whose currency
	 * belongs to a currency Group that has Read only or No access.
	 *
	 * @param request -
	 *            HttpServletRequest object
	 * @throws SwtException -
	 *             SwtException object
	 */
	private void checkSwpLimitForCcyGrpFullAccess(HttpServletRequest request)
			throws SwtException {
		log.debug("Entering 'checkSwpLimitForCcyGrpFullAccess' method");

		SweepLimitsManager sweepLimitsMgr = (SweepLimitsManager) (SwtUtil
				.getBean("sweepLimitsManager"));
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		Collection collSweepLimits = (Collection) request.getSession()
				.getAttribute("sessionSweepLimitDetails");
		Collection collCcyGrpAcc = (Collection) request.getSession()
				.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
		/**
		 * newSweepLimitsColl is a collection which holds only those sweep limit
		 * record whose currencyGroup has Full access.
		 */
		Collection newSweepLimitsColl = new ArrayList();
		String entityId = ""; // Holds entityId of the records present in
		// collInput.
		String ccyGrpId = ""; // Holds currencyGroupId of the records present
		// in collInput.
		Collection collCurrency = null; // Holds collection of currencies
		// returned from the database.
		Collection CurrList = new ArrayList();
		Collection collInput = new ArrayList(); // Holds currencyGroupAccessGui
		/**
		 * Return if No SweepLimit record is exiting
		 */
		if (collSweepLimits == null || collSweepLimits.size() == 0) {
			return;
		}

		if (collCcyGrpAcc != null) {
			Iterator itr = collCcyGrpAcc.iterator();
			while (itr.hasNext()) {
				CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itr
						.next());
				if (ccyGrpAccGui.getCcyGrpAccessHTML1().equals("checked")) {
					collInput.add(ccyGrpAccGui);
				}
			}
		}

		/**
		 * Adding all currencies corresponding to a particular currencyGroup
		 * into CurrList collection
		 */
		if (collInput != null) {
			Iterator itr = collInput.iterator();
			while (itr.hasNext()) {
				CurrencyGroupAccessGui ccyGrpAccGui = (CurrencyGroupAccessGui) (itr
						.next());
				entityId = ccyGrpAccGui.getEntityId();
				ccyGrpId = ccyGrpAccGui.getCurrencyGroupId();
				collCurrency = sweepLimitsMgr.getCurrencyDetails(hostId,
						entityId, ccyGrpId);
				CurrList.addAll(collCurrency);
			}
		}

		/**
		 * Checking if the any record of sweepLimits has a CurrencyGroup which
		 * has Read/No access if any such record is found it is not placed in
		 * newSweepLimitsColl collection.
		 */
		boolean recordMatched = false;
		if (collSweepLimits != null) {
			Iterator itrSweepLimitsList = collSweepLimits.iterator();
			while (itrSweepLimitsList.hasNext()) {
				SweepLimits sweepLimit = (SweepLimits) (itrSweepLimitsList
						.next());
				recordMatched = false;
				if (CurrList != null) {
					Iterator itr = CurrList.iterator();
					while (itr.hasNext()) {
						Currency ccy = (Currency) (itr.next());
						if (ccy.getId().getCurrencyCode().equals(
								sweepLimit.getId().getCurrencyCode())) {
							// log.debug("The currency codes matched");
							recordMatched = true;
							break;
						}
					}
				}
				if (recordMatched) {
					newSweepLimitsColl.add(sweepLimit);
				}
			}
		}
		request.getSession().setAttribute("sessionSweepLimitDetails",
				newSweepLimitsColl);
		log.debug("Exiting 'checkSwpLimitForCcyGrpFullAccess' method");
	}

	public Collection refreshCcyGrpSessionColl(String hostId,
											   HttpServletRequest request) throws SwtException {

		log.debug("Entering into the refreshCcyGrpSessionColl() method");

		Collection collCcyGrpItems = (Collection) request.getSession()
				.getAttribute(SwtConstants.ROLE_CURRENCY_ACCESS_LIST);

		Collection refreshedColl = new ArrayList();

		Collection entityComboForCcyGrpAccess = (Collection) (request
				.getSession().getAttribute("entityComboForCcyGrpAccess"));

		Collection refreshedCollFromDB = new ArrayList();

		if (entityComboForCcyGrpAccess != null) {

			Collection tempVar = new ArrayList();

			Iterator itr = entityComboForCcyGrpAccess.iterator();

			while (itr.hasNext()) {

				EntityAccessGui entGui = (EntityAccessGui) (itr.next());

				Collection currGrpDetailsDB = roleMgr
						.getCurrencyGroupDetailsByEntityId(hostId, entGui
								.getEntityId());

				Collection convertedColl = convertIntoCurrencyGrpGuiColl(currGrpDetailsDB);

				refreshedCollFromDB.addAll(convertedColl);
			}
		}

		if (refreshedCollFromDB == null) {

			return new ArrayList();

		} else if (collCcyGrpItems == null) {

			return refreshedCollFromDB;
		} else {

			Iterator itrDB = refreshedCollFromDB.iterator();

			boolean recFound = false;

			CurrencyGroupAccessGui cgGuiDB = new CurrencyGroupAccessGui();

			while (itrDB.hasNext()) {

				recFound = false;

				cgGuiDB = (CurrencyGroupAccessGui) (itrDB.next());

				Iterator itrCurrGrpList = collCcyGrpItems.iterator();

				while (itrCurrGrpList.hasNext()) {

					CurrencyGroupAccessGui cgGui = (CurrencyGroupAccessGui) (itrCurrGrpList
							.next());

					if (cgGuiDB.getCurrencyGroupId().equals(
							cgGui.getCurrencyGroupId())
							&& cgGuiDB.getEntityId()
							.equals(cgGui.getEntityId())) {

						refreshedColl.add(cgGui);
						recFound = true;
						break;
					}
				}

				if (!recFound) {

					refreshedColl.add(cgGuiDB);
				}

			}

		}


		log.debug("Exiting from the refreshCcyGrpSessionColl() method");

		return refreshedColl;

	}

	private void getMenuItemsForSaveUpdateDelete(HttpServletRequest request,
												 String roleId, Collection menuItemColl,
												 Collection colMenuAccessSave, Collection colMenuAccessUpdate,
												 Collection colMenuAccessDelete) {

		log.debug("Entering inside getMenuItemsForSaveUpdateDelete");

		// Iterating through the whole list and saving only those entries for
		// which Access is either
		// 'Full' or 'View Only'
		Iterator itrMenuItemId = menuItemColl.iterator();

		Collection collMenuAccessGui = (Collection) request.getSession()
				.getAttribute("initialIitemDescList");
		Iterator itrInitialMenuItemId = collMenuAccessGui.iterator();

		while ((itrMenuItemId.hasNext()) && (itrInitialMenuItemId.hasNext())) {
			MenuAccessOptionsGui menuAccessOptionsGui = (MenuAccessOptionsGui) (itrMenuItemId
					.next());
			MenuAccessOptionsGui menuAccessOptionsGuiInitial = (MenuAccessOptionsGui) (itrInitialMenuItemId
					.next());

			String radio1 = menuAccessOptionsGui.getMenuAccessHTMLFullAccess();
			String radio1Initial = menuAccessOptionsGuiInitial
					.getMenuAccessHTMLFullAccess();

			String radio2 = menuAccessOptionsGui.getMenuAccessHTMLViewAccess();
			String radio2Initial = menuAccessOptionsGuiInitial
					.getMenuAccessHTMLViewAccess();

			String radio3 = menuAccessOptionsGui.getMenuAccessHTMLNoAccess();
			String radio3Initial = menuAccessOptionsGuiInitial
					.getMenuAccessHTMLNoAccess();

			String menuItemId = menuAccessOptionsGui.getItemId();

			/*
			 * Making a new object of type MEnuAccess that is to be saved,
			 * updated or deleted in the database depending on the conditions
			 * checked below
			 */
			MenuAccess menuAccess = new MenuAccess();
			menuAccess.getId().setItemId(menuAccessOptionsGui.getItemId());
			menuAccess.getId().setRoleId(roleId);
			menuAccess.getId().setDescription(
					menuAccessOptionsGui.getDescription());
			// Code for deletion and to revert back to user for error msg
			if (((radio1Initial.equals("checked")) || (radio2Initial
					.equals("checked")))
					&& (radio3.equals("checked"))) {
				// Now deleting the entry from p_menu_access table
				// roleMgr.deleteEntityAccessDetails(entityAccess);
				colMenuAccessDelete.add(menuAccess);

			}

			if (!((radio3Initial.equals("checked")) && (radio3
					.equals("checked")))) {
				if (radio3Initial.equals("checked")) {

					if (radio1.equals("checked")) {
						menuAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_FULL_ACCESS));
					} else {
						menuAccess.setAccessId(String
								.valueOf(SwtConstants.ENTITY_READ_ACCESS));
					}
					colMenuAccessSave.add(menuAccess);
				} else if (radio1Initial.equals("checked")
						&& radio2.equals("checked")) {

					menuAccess.setAccessId(String
							.valueOf(SwtConstants.ENTITY_READ_ACCESS));

					colMenuAccessUpdate.add(menuAccess);
				} else if (radio2Initial.equals("checked")
						&& radio1.equals("checked")) {
					menuAccess.setAccessId(String
							.valueOf(SwtConstants.ENTITY_FULL_ACCESS));

					colMenuAccessUpdate.add(menuAccess);
				}
			}
		}

		String currentUserRoleId = ((CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
		if (currentUserRoleId.equalsIgnoreCase(roleId)) {
			if (colMenuAccessSave.size() > 0 || colMenuAccessUpdate.size() > 0
					|| colMenuAccessDelete.size() > 0) {
				request.setAttribute("accessAltered", "Y");
			}
		}
		log.debug("Exiting from getMenuItemsForSaveUpdateDelete() ");
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String addLocationAccessList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'addLocationList' Method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			String hostId = CacheManager.getInstance().getHostId();
			String entityId = request.getParameter("selectedEntityId");
			String roleId = request.getParameter("selectedRoleId");

			putEntityListInReq(request);
			// If entityId in req is null, it will set to 1st entity of the entity collection
			if (entityId == null || entityId.trim().length() <= 0)
			{
				Collection col = (ArrayList) request.getAttribute("entities");
				if (col != null) {
					Iterator itr = col.iterator();
					while (itr.hasNext()) {
						LabelValueBean lb = (LabelValueBean) (itr.next());
						entityId = lb.getValue();
						break;
					}
				}

			}
			LocationAccessGui locAccessGui = new LocationAccessGui();
			locAccessGui.setEntityId(entityId);
			Collection locationDetailsList = (Collection) request.getSession()
					.getAttribute(SwtConstants.LOCATION_ACCESS_LIST);

			if (locationDetailsList == null) { // if nothing is set in session
				LocationMaintenanceManager mgr = (LocationMaintenanceManager) (SwtUtil
						.getBean("locationMaintenanceManager"));

				Collection collLocation = mgr.getAllLocationDetails(hostId); // coll  from P_LOCATION
				Collection CollLocationDetailsInSession = new ArrayList();
				Iterator itr = collLocation.iterator();
				while (itr.hasNext()) { // castin the objects into
					// LocationAccessGui types
					Location locAcc = (Location) (itr.next());
					LocationAccessGui locAccess = new LocationAccessGui();
					locAccess.setEntityId(locAcc.getId().getEntityId());
					locAccess.setLocationId(locAcc.getId().getLocationId());
					locAccess.setLocationName(locAcc.getLocationName());
					locAccess.setLocAccess("");
					CollLocationDetailsInSession.add(locAccess);

				}
				Collection fillLocationDetails = new ArrayList();
				Iterator itrr = CollLocationDetailsInSession.iterator();

				while (itrr.hasNext()) { // Filtering records of
					// selectedEntityId
					LocationAccessGui locAcc = (LocationAccessGui) (itrr.next());
					if ((locAcc.getEntityId()).equalsIgnoreCase(entityId)) {
						fillLocationDetails.add(locAcc);
					}
				}

				request
						.setAttribute("locationDetailsList",
								fillLocationDetails);
				request.getSession().setAttribute(
						SwtConstants.LOCATION_ACCESS_LIST,
						CollLocationDetailsInSession);
			} else {
				Collection fillLocationDetails = new ArrayList();
				Iterator itrr = locationDetailsList.iterator();

				while (itrr.hasNext()) { // Filtering records of selectedEntityId
					LocationAccessGui locAcc = (LocationAccessGui) (itrr.next());
					if ((locAcc.getEntityId()).equalsIgnoreCase(entityId)) {
						fillLocationDetails.add(locAcc);
					}
				}

				request
						.setAttribute("locationDetailsList",
								fillLocationDetails);
				request.getSession().setAttribute(
						SwtConstants.LOCATION_ACCESS_LIST, locationDetailsList);

			}

			setLocationAccess(locAccessGui);
			request.setAttribute("selectedRoleId", roleId);
			request.setAttribute("methodName", "add");
			log.debug("Exiting 'addLocationList' Method");
			return getView("locationDetails");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "addLocationAccessList", RoleAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String changeLocationAccessList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'displayLocationList' Method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			String hostId = CacheManager.getInstance().getHostId();
			String entityId = request.getParameter("selectedEntityId");
			String roleId = request.getParameter("selectedRoleId");
			putEntityListInReq(request);
			if (entityId == null || entityId.trim().length() <= 0) {
				Collection col = (ArrayList) request.getAttribute("entities");
				if (col != null) {
					Iterator itr = col.iterator();
					while (itr.hasNext()) {
						LabelValueBean lb = (LabelValueBean) (itr.next());
						entityId = lb.getValue();
						break;
					}
				}

			}

			putLocationAccessListInSession(request, hostId, entityId, roleId);

			LocationAccessGui locAccessGui = new LocationAccessGui();
			locAccessGui.setEntityId(entityId);

			setLocationAccess(locAccessGui);
			request.setAttribute("selectedRoleId", roleId);
			request.setAttribute("methodName", "change");
			log.debug("Exiting 'display' Method");
			return getView("locationDetails");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "changeLocationAccessList", RoleAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String viewLocationAccessList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug("Entering viewLocationList method");
		try {
			String hostId = CacheManager.getInstance().getHostId();
			String entityId = request.getParameter("selectedEntityId");
			String roleId = request.getParameter("selectedRoleId");
			putEntityListInReq(request);
			if (entityId == null || entityId.trim().length() <= 0) {
				Collection col = (ArrayList) request.getAttribute("entities");
				if (col != null) {
					Iterator itr = col.iterator();
					while (itr.hasNext()) {
						LabelValueBean lb = (LabelValueBean) (itr.next());
						entityId = lb.getValue();
						break;
					}
				}

			}

			putLocationAccessListInSession(request, hostId, entityId, roleId);
			request.setAttribute("methodName", "view");
			log.debug("Exiting 'display' Method");
			return getView("locationDetails");

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(e, "viewLocationAccessList",
									RoleAction.class), request, "");
			return getView("fail");
		}

	}

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		HttpSession session = request.getSession();
		Collection entities = new ArrayList();
		entities = SwtUtil.getUserEntityAccessList(session);
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		request.setAttribute("entities", entities);

		log.debug("Exit LocationMaintenanceAction.putEntityListInReq method");
	}

	public String saveLocationAccessList() throws SwtException {
		log.debug("Entering SaveLocations Method");
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			putEntityListInReq(request);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			String calleeMethodName = request.getParameter("calleeMethodName");
			String entityId = request.getParameter("selectedEntityId");
			Collection locationDetailsList = (Collection) request.getSession()
					.getAttribute(SwtConstants.LOCATION_ACCESS_LIST);

			Collection fillColl = new ArrayList();
			Iterator itr = locationDetailsList.iterator();
			while (itr.hasNext()) {
				LocationAccessGui loc = (LocationAccessGui) (itr.next());
				if (loc.getEntityId().equalsIgnoreCase(entityId)) {
					fillColl.add(loc);
				}
			}

			Collection updatedFillColl = new ArrayList();
			Iterator itrFill = fillColl.iterator();
			int itemIdIndex = 1;
			while (itrFill.hasNext()) {
				LocationAccessGui locAccess = (LocationAccessGui) (itrFill
						.next());
				String checkBoxStatus = request.getParameter("locAccessHTML"
						+ itemIdIndex);
				if (checkBoxStatus == null) {
					locAccess.setLocAccess("");

				} else {
					locAccess.setLocAccess("checked");

				}
				itemIdIndex++;
				updatedFillColl.add(locAccess);
			}
			request.setAttribute("locationDetailsList", updatedFillColl);

			Iterator itrr = locationDetailsList.iterator();
			Collection updatedLocationAccessList = new ArrayList();
			while (itrr.hasNext()) {
				LocationAccessGui locAcc = (LocationAccessGui) (itrr.next());
				Iterator itrColl = fillColl.iterator();
				String entityId1 = locAcc.getEntityId();
				if (entityId1.equalsIgnoreCase(entityId)) {
					while (itrColl.hasNext()) {
						LocationAccessGui loca = (LocationAccessGui) (itrColl
								.next());
						if ((locAcc.getLocationId()).equalsIgnoreCase(loca
								.getLocationId())) {
							if ((loca.getLocAccess())
									.equalsIgnoreCase("checked")) {
								locAcc.setLocAccess("checked");


							} else if ((loca.getLocAccess())
									.equalsIgnoreCase("")) {
								locAcc.setLocAccess("");

							}
						}
					}
				}
				updatedLocationAccessList.add(locAcc);
			}

			request.getSession().setAttribute(
					SwtConstants.LOCATION_ACCESS_LIST,
					updatedLocationAccessList);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("methodName", calleeMethodName);
			request.setAttribute("selectedRoleId", request
					.getParameter("selectedRoleId"));
			return getView("locationDetails");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(e, "saveLocationAccessList",
									RoleAction.class), request, "");
			return getView("fail");
		}

	}

	public void putLocationAccessListInSession(HttpServletRequest request,
											   String hostId, String entityId, String roleId) {
		log.debug("Entering putLocationAccessListInSession method");
		try {
			Collection allLocationDetailsList = (Collection) request
					.getSession().getAttribute(
							SwtConstants.ALL_LOCATION_ACCESS_LIST);
			Collection allLocationIdDetailsList = (Collection) request
					.getSession().getAttribute(
							SwtConstants.LOCATION_ACCESS_LIST);

			LocationMaintenanceManager mgr = (LocationMaintenanceManager) (SwtUtil
					.getBean("locationMaintenanceManager"));

			Collection collLocation = new ArrayList(); // for filtered records
			Collection collAllLocation = new ArrayList(); // for all records
			Collection collAllLocationId = new ArrayList(); // for all records
			Collection collLocationId = new ArrayList(); // for filtered

			Iterator itr; // for coll from P_LOCATION
			Iterator itrr; // for coll from P_LOCATION_ACCESS

			if (allLocationDetailsList == null) {
				collAllLocation = mgr.getAllLocationDetails(hostId); // coll from P_LOCATION

				request.getSession().setAttribute(
						SwtConstants.ALL_LOCATION_ACCESS_LIST, collAllLocation);
				Collection coll = mgr.getLocationIdFromDB(hostId, roleId); // coll
				// from
				// P_LOCATION_ACCESS

				if (collAllLocation != null && coll != null) { // Checking those boxes whose LocationId
					itr = collAllLocation.iterator(); // is present in
					// P_LOCATION_ACCESS
					while (itr.hasNext()) {
						int flag = 0;
						Location loc = (Location) (itr.next());
						LocationAccessGui locAccess = new LocationAccessGui();
						locAccess.setEntityId(loc.getId().getEntityId());
						locAccess.setLocationId(loc.getId().getLocationId());
						locAccess.setLocationName(loc.getLocationName());

						LocationAccess access = new LocationAccess();
						itrr = coll.iterator();
						while (itrr.hasNext()) {
							access = (LocationAccess) (itrr.next());
							if (((loc.getId().getEntityId())
									.equalsIgnoreCase(access.getId()
											.getEntityId()))
									&& ((loc.getId().getHostId())
									.equalsIgnoreCase(access.getId()
											.getHostId()))
									&& ((loc.getId().getLocationId())
									.equalsIgnoreCase(access.getId()
											.getLocationId()))) {

								flag = 1;
								break;

							} else {
								flag = 0;

							}
						}
						if (flag == 1) {
							locAccess.setLocAccess("checked"); // set location access to checked
						} else {
							locAccess.setLocAccess(""); // set location access to unchecked
						}

						collAllLocationId.add(locAccess);
					}
				}
				/*collAllLocationId has all the records with their resp checkboxes checked/unchecked*/
				request.getSession().setAttribute(
						SwtConstants.LOCATION_ACCESS_LIST, collAllLocationId); // 

				Collection collAllLocationIdInitial = new ArrayList(); // Making a copy of initial coll to put in session
				collAllLocationIdInitial = (ArrayList) SwtUtil
						.copy(collAllLocationId);

				request.getSession().setAttribute(
						SwtConstants.INITIAL_LOCATION_ACCESS_LIST,
						collAllLocationIdInitial);

				itr = collAllLocation.iterator();
				while (itr.hasNext()) {
					Location loc = (Location) (itr.next());
					if ((loc.getId().getEntityId()).equalsIgnoreCase(entityId)) { // Filtering records of selectedEntityId
						collLocation.add(loc);
					}
				}

				itrr = collAllLocationId.iterator();
				while (itrr.hasNext()) { // Filtering records of selectedEntityId

					LocationAccessGui locAcc = (LocationAccessGui) (itrr.next());
					if ((locAcc.getEntityId()).equalsIgnoreCase(entityId)) {
						collLocationId.add(locAcc);
					}
				}

			} else {
				itr = allLocationDetailsList.iterator();
				while (itr.hasNext()) {
					Location loc = (Location) (itr.next());
					if ((loc.getId().getEntityId()).equalsIgnoreCase(entityId)) { // Filtering records of selectedEntityId

						collLocation.add(loc);
					}
				}

				itrr = allLocationIdDetailsList.iterator();
				while (itrr.hasNext()) { // Filtering records of selectedEntityId,hostId & roleId
					LocationAccessGui locAcc = (LocationAccessGui) (itrr.next());
					if ((locAcc.getEntityId()).equalsIgnoreCase(entityId)) {
						collLocationId.add(locAcc);
					}
				}
			}

			request.setAttribute("locationDetailsList", collLocationId); // coll wrt to selectedEntityId set in request
			log.debug("Exiting putLocationAccessListInSession");
		} catch (Exception e) {
			log.debug("Exception in RoleAction.putLocationAccessListInSession"
					+ e.getMessage());
			e.printStackTrace();
			SystemExceptionHandler.logError(e);

		}

	}


	/**
	 * This is used to view the account access details
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
	public String viewAccountAccess() throws SwtException {
		/* Method's local variable declaration */
// To remove: 		DynaValidatorForm dyForm = null;
		AccountAccess accountAccess = null;
		User userFromCDM = null;
		CacheManager cacheManagerInst = null;
		Collection acctmaintenanceDetails = null;
		String entityId = null;
		String entityIdCombo = null;
		String roleId = null;
		String hostId = null;

		boolean matchingAllFlag=true;
		boolean sweepingAllFlag=true;
		boolean manualInputAllFlag=true;
		Collection checkAllFlag =null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [viewAccountAccess] - "
					+ "Entry");
// To remove: 			dyForm = (DynaValidatorForm) form;
			accountAccess = (AccountAccess) getAccountAccess();
			setAccountAccess(accountAccess);
			/* Used to put the entity list in request */
			entityIdCombo = getEntityComboForWorkQAccess(request);
			/* Used to put the role list in request */
			putRoleListInReq(request);
			/* Getting entity id using bean class */
			entityId = accountAccess.getId().getEntityId();
			/* Condition to check entity id has value */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Read the current user's entity from request */
				entityId = entityIdCombo;
				/* Setting entity id using bean class */
				accountAccess.getId().setEntityId(entityId);
			}

			/* Read the selected entity id from the request */
			if (request.getParameter("selectedEntityId") != null) {
				/* Setting selected entity id using bean class */
				accountAccess.getId().setEntityId(
						request.getParameter("selectedEntityId"));
			}
			/* Read the current user from swtutil fiel */
			userFromCDM = SwtUtil.getCurrentUser(request.getSession());
			/* Read the selected role id from request */
			roleId = request.getParameter("selectedRoleId");
			/* Setting role id using bean class */
			accountAccess.getId().setRoleId(roleId);
			cacheManagerInst = CacheManager.getInstance();
			/* Read host id from swtutil file */
			hostId = cacheManagerInst.getHostId();
			// Getting EntityAccess Details From roleManager
			setAccountAccess(accountAccess);
			/* Read account access manager file */
			AccountAccessManager accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			if (!getCurrencyAccess(request, entityId).equals("")) {
				acctmaintenanceDetails = accountAccessManager
						.accountAccessDetails(hostId, roleId, entityId,
								getCurrencyAccess(request, entityId));
				/* Read account access details from session */
				if (request.getSession().getAttribute("roleAccountAccessList") != null) {
					Collection acctDetails = new ArrayList();
					Collection acctSession = (Collection) (request.getSession()
							.getAttribute("roleAccountAccessList"));
					/* Condition to check collections has value */
					if (acctmaintenanceDetails != null && acctSession != null) {
						/* Iterate account details */
						Iterator itr = acctmaintenanceDetails.iterator();
						Iterator itrInitial = acctSession.iterator();
						/* Loop to iterate the deails */
						while (itr.hasNext()) {
							AccountAccessDetails acctMain = (AccountAccessDetails) (itr
									.next());
							while (itrInitial.hasNext()) {
								AccountAccessDetails acctSes = (AccountAccessDetails) (itrInitial
										.next());
								if (roleId.equals(acctSes.getRoleId())
										&& entityId.equals(acctSes
										.getEntityId())
										&& acctMain.getAccountId().equals(
										acctSes.getAccountId())) {
									/* Setting manual input using bean class */
									acctMain.setAllowManualInput(acctSes
											.getAllowManualInput());
									/* Setting matching using bean class */
									acctMain.setAllowMatching(acctSes
											.getAllowMatching());
									//Start: Added by Bala on ******** for set the sweeping values 
									/* Setting sweeping using bean class */
									acctMain.setAllowSweeping(acctSes
											.getAllowSweeping());
									//End: Added by Bala on ******** for set the sweeping values 
									break;
								}

							}
							/* Add the accounts access details in collection */
							acctDetails.add(acctMain);
						}
					}
					checkAllFlag=acctDetails;
					request.setAttribute("AccountAccessDetails", acctDetails);
				} else {
					request.setAttribute("AccountAccessDetails",
							acctmaintenanceDetails);
					checkAllFlag =acctmaintenanceDetails;
				}
			} else {
				acctmaintenanceDetails = new ArrayList();
				request.setAttribute("AccountAccessDetails",
						acctmaintenanceDetails);
				checkAllFlag =acctmaintenanceDetails;
			}


			Iterator itr = checkAllFlag.iterator();
			if(acctmaintenanceDetails!=null && acctmaintenanceDetails.size()>0)
				while (itr.hasNext()) {
					AccountAccessDetails acctAccess = (AccountAccessDetails) (itr.next());
					if(acctAccess.getAllowManualInput()!=null&&acctAccess.getAllowManualInput().equals("Y")&& manualInputAllFlag==true)
						manualInputAllFlag=true;
					else
						manualInputAllFlag=false;
					if(acctAccess.getAllowMatching()!=null&&acctAccess.getAllowMatching().equals("Y")&& matchingAllFlag==true)
						matchingAllFlag=true;
					else
						matchingAllFlag=false;
					if(acctAccess.getAllowSweeping()!=null&&acctAccess.getAllowSweeping().equals("Y")&& sweepingAllFlag==true)
						sweepingAllFlag=true;
					else
						sweepingAllFlag=false;
				}
			if(acctmaintenanceDetails!=null && acctmaintenanceDetails.size()==0)
			{
				matchingAllFlag=false;
				sweepingAllFlag=false;
				manualInputAllFlag=false;
			}

			request.setAttribute("sweepingAllFlag",
					sweepingAllFlag);
			request.setAttribute("matchingAllFlag",
					matchingAllFlag);
			request.setAttribute("manualInputAllFlag",
					manualInputAllFlag);

			request.setAttribute("entityId", entityId);
			request.setAttribute("methodName", "view");
			log.debug(this.getClass().getName() + " - [viewAccountAccess] - "
					+ "Exit");
			return getView("accountAccess");

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewAccountAccess] method : - "
					+ swtexp.getMessage());
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewAccountAccess] method : - "
					+ e.getMessage());
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "viewAccountAccess", RoleAction.class), request, "");
			return getView("fail");
		}

	}

	/**
	 * Method to put the role details in request
	 *
	 * @param request
	 * @param entityId
	 * @return none
	 * @throws SwtException
	 */
	private String getCurrencyAccess(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [getCurrencyAccess] - "
				+ "Entry");
		/* Method's local variable declaration */
		CacheManager cacheManagerInst = null;
		String hostId = null;
		Collection roleList = null;
		HttpSession session = null;
		Collection collEntity = null;
		Entity entityObj = null;
		LabelValueBean labelValueBeanObj = null;
		Collection entities = new ArrayList();
		session = request.getSession();
		StringBuffer ccy = new StringBuffer();
		Collection collCurrencyAccess = null;
		Collection collFiltered = null;

		/* to collect the all entity */
		collCurrencyAccess = (Collection) request.getSession().getAttribute(
				SwtConstants.ROLE_CURRENCY_ACCESS_LIST);
		collFiltered = filterCurrencyGroupByEntity(collCurrencyAccess, entityId);
		Collection outColl = new ArrayList();
		if (collFiltered != null) {
			Iterator itr = collFiltered.iterator();
			while (itr.hasNext()) {
				CurrencyGroupAccessGui ccyGrpGui = (CurrencyGroupAccessGui) itr
						.next();
				if (ccyGrpGui.getCcyGrpAccessHTML1().equals("checked"))
					ccy.append("'" + ccyGrpGui.getCurrencyGroupId() + "',");
			}

		}
		if (ccy.lastIndexOf(",") > 0) {
			ccy.delete(ccy.length() - 1, ccy.length());
		}
		log.debug(this.getClass().getName() + " - [getCurrencyAccess] - "
				+ "Exit");
		return ccy.toString();

	}

	/**
	 * Method to put the role details in request
	 *
	 * @param request
	 * @return none
	 * @throws SwtException
	 */
	private void putRoleListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - "
				+ "Entry");
		/* Method's local variable declaration */
		CacheManager cacheManagerInst = null;
		String hostId = null;
		Collection roleList = null;
		UserMaintenanceManager usermaintenanceManager = null;
		cacheManagerInst = CacheManager.getInstance();
		hostId = cacheManagerInst.getHostId();
		usermaintenanceManager = (UserMaintenanceManager) (SwtUtil
				.getBean("usermaintenanceManager"));
		roleList = usermaintenanceManager.getRoleList(hostId);
		request.setAttribute("roleIdList", roleList);
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - "
				+ "Exit");
	}

	/**
	 * This is used to save the account access details
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
	public String saveAccountAccess() throws SwtException {
		/* Method's local variable declaration and intialization */
		String roleId = null;
		String entityId = null;
		String accountId = null;
		String recordCount = null;
		Collection acctDetails = null;
		Collection acctSession = null;
		Collection acct = null;
		String hostId = null;
		CacheManager cacheManagerInst = null;
		AccountAccessDetails acctdetails = null;
		Collection acctmaintenanceDetails = null;
		Collection collAcctAcess = new ArrayList();
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [saveAccountAccess] - "
					+ "Entry");

			String getData = request.getParameter("setData");
			String[] rowData = getData.split("##");
			roleId = request.getParameter("roleId");
			entityId = request.getParameter("entityId");
			cacheManagerInst = CacheManager.getInstance();
			// Start: code added by Arumugam on 11-NOV-2010 for Mantis:0001281- to get the screen value and set the bean object
			/* Getting host id using cache manager */
			hostId = cacheManagerInst.getHostId();
			AccountAccessManager accountAccessManager = null;
			accountAccessManager = (AccountAccessManager) (SwtUtil.getBean("accountAccessManager"));
			acctSession = (Collection) (request.getSession().getAttribute("roleAccountAccessList"));
			boolean flag = true;
			/* Condition to check collection has value */
			if (acctSession != null) {
				Iterator itrInitial = acctSession.iterator();
				while (itrInitial.hasNext()) {
					AccountAccessDetails acctSes = (AccountAccessDetails) (itrInitial
							.next());
					/*
					 * Condition to check role id and entity id has
					 * value
					 */

					if (roleId.equals(acctSes.getRoleId())&& entityId.equals(acctSes.getEntityId())&&hostId.equals(acctSes.getHostId())){
						flag=false;
						break;

					}

				}
			}

			/* Condition to check collection has value */
			if(flag){

				if (acctSession == null) {
					acctSession = new ArrayList();
				}
				acctmaintenanceDetails = accountAccessManager.accountAccessDetails(
						hostId, roleId, entityId,getCurrencyAccess(request, entityId));
				Iterator itr = acctmaintenanceDetails.iterator();
				while (itr.hasNext()) {
					AccountAccessDetails acctAccess = (AccountAccessDetails) (itr.next());
					String inputcheck =request.getParameter("inputcheck#"+acctAccess.getAccountId());
					if(inputcheck==null){
						acctAccess.setAllowManualInput("N");
					}else{
						acctAccess.setAllowManualInput("Y");
					}
					String matchingcheck =request.getParameter("matchingcheck#"+acctAccess.getAccountId());
					if(matchingcheck==null){
						acctAccess.setAllowMatching("N");
					}else{
						acctAccess.setAllowMatching("Y");
					}
					String sweepingcheck =request.getParameter("sweepingcheck#"+acctAccess.getAccountId());
					if(sweepingcheck==null){
						acctAccess.setAllowSweeping("N");
					}else{
						acctAccess.setAllowSweeping("Y");
					}
					acctAccess.setRoleId(roleId);
					acctAccess.setHostId(hostId);
					acctAccess.setEntityId(entityId);
					acctSession.add(acctAccess);
				}
				request.getSession().setAttribute("roleAccountAccessList",acctSession);

			}else{
				Iterator itrInitial = acctSession.iterator();
				while (itrInitial.hasNext()) {
					AccountAccessDetails acctSes = (AccountAccessDetails) (itrInitial
							.next());
					/*
					 * Condition to check role id and entity id has
					 * value
					 */
					if (roleId.equals(acctSes.getRoleId())&& entityId.equals(acctSes.getEntityId())&&hostId.equals(acctSes.getHostId()))
					{

						String inputcheck =request.getParameter("inputcheck#"+acctSes.getAccountId());
						if(inputcheck==null){
							acctSes.setAllowManualInput("N");
						}else{
							acctSes.setAllowManualInput("Y");
						}
						String matchingcheck =request.getParameter("matchingcheck#"+acctSes.getAccountId());
						if(matchingcheck==null){
							acctSes.setAllowMatching("N");
						}else{
							acctSes.setAllowMatching("Y");
						}
						String sweepingcheck =request.getParameter("sweepingcheck#"+acctSes.getAccountId());
						if(sweepingcheck==null){
							acctSes.setAllowSweeping("N");
						}else{
							acctSes.setAllowSweeping("Y");
						}

					}
					collAcctAcess.add(acctSes);
				}
				request.getSession().setAttribute("roleAccountAccessList",collAcctAcess);

				// end: code added by Arumugam on 11-NOV-2010 for Mantis:0001281- to get the screen value and set the bean object
			}
			log.debug(this.getClass().getName() + " - [saveAccountAccess] - "
					+ "Exit");

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAccountAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveAccountAccess", RoleAction.class), request, "");
			return getView("fail");
		}

		return null;
	}


	/*
	 * Generate the report for selected role, if the user clicked on the print button
	 * in role maintenance screen.If the user clicked on printAll button in
	 * role maintanace screen,the report will be generated for all roles
	 * present in the smart predict system.
	 */

	/**
	 *
	 * The getRoleReport method is used to get the report of all roles or
	 * selected roles present in a smart predict system as a PDF File.
	 *
	 * @return Report
	 */
	public String getRoleReport() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Declaring role Id
		String roleId = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// Declaring the PDFExporter Variable
		JRPdfExporter pdfExporter = null;
		// Declaring ServletOutputStream
		ServletOutputStream out = null;
		// Getting the selected roleId.
		roleId = request.getParameter("selectedRoleId_Report");
		String fileName = null;
		String fileType = null;
		JRXlsExporter xlsxporter;
		Collection collRole = null;
		Iterator roleIterator;
		Role role = null;
		int counter = 0;
		String[] roleArray = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getRoleReport] - Entering ");
			fileType = request.getParameter("fileType");
			// Calling the getRoleReport from manager to get the compiled report
			// design.
			jasperPrint = roleMgr.getRoleReport(request, roleId);
			// Initialise the outputstream
			out = response.getOutputStream();
			if (!"".equals(roleId))
				fileName = "RoleReport-SmartPredict_" + roleId + "_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss");
			else
				fileName = "RoleReport-SmartPredict_"
						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
						+ SwtUtil.formatDate(new Date(), "HHmmss");

			if (fileType.equals("pdf")) {
				// Intialising the PDFExporter.
				pdfExporter = new JRPdfExporter();
				// To set the output type as PDF file
				response.setContentType("application/pdf");
				// To set the content as attachment
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + ".pdf");

				// Providing the jasperPrint
				pdfExporter.setParameter(JRExporterParameter.JASPER_PRINT,
						jasperPrint);

				// Providing the output stream
				pdfExporter
						.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				pdfExporter.setParameter(
						JRExporterParameter.CHARACTER_ENCODING, "UTF-8");

				// Export Report to PDF
				pdfExporter.exportReport();

				/* Condition to check fileType is xls */
			} else if (fileType.equals("excel")) {
				xlsxporter = new JRXlsExporter();
				// To set the output type as xls file
				response.setContentType("application/xls");
				// To set the content as attachment
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + ".xls");

				// Providing the jasperPrint
				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT,
						jasperPrint);

				// Providing the output stream
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
						"UTF-8");
				// To print records in each sheet
				xlsxporter.setParameter(
						JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,
						Boolean.TRUE);
				// To remove empty space between rows
				xlsxporter
						.setParameter(
								JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS,
								Boolean.TRUE);
				// To remove empty space between columns
				xlsxporter
						.setParameter(
								JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS,
								Boolean.TRUE);
				// TO detect cell Type
				//modified for jasper jar  upgrade-Mantis 1648
				xlsxporter.setParameter(
						JRXlsExporterParameter.IS_DETECT_CELL_TYPE,
						Boolean.TRUE);

				/* Condition to check roleId id null or empty */
				if (roleId.equals("") || roleId == null) {

					role = new Role();
					// Getting Role Details from Role Manager
					collRole = roleMgr.getRoleDetails(role);
					// Initializing array to collection size
					roleArray = new String[collRole.size()];
					roleIterator = collRole.iterator();
					/* Loop to get role Id in array list */
					while (roleIterator.hasNext()) {
						Role roleBean = (Role) roleIterator.next();
						roleArray[counter] = roleBean.getRoleId();
						counter++;
					}
					/* Set array of roleId's as sheet names */
					xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES,
							roleArray);
				} else {
					/*
					 * If role id is not null Initialize array string for single
					 * value
					 */
					roleArray = new String[1];
					/* Assign selected RoleId to array */
					roleArray[0] = roleId;
					/* Set the sheet name for selected RoleId */
					xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES,
							roleArray);
				}
				// Export Report to Excel
				xlsxporter.exportReport();
			}
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ "- [getRoleReport] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
					+ "- [getRoleReport] - Exception " + e.getMessage());
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "save", RoleAction.class), request, "");
			return getView("fail");
		}

		return null;

	}

	//Start: Added by Bala on Mantis:1029 - copy account access details
	/**
	 * Method to copy account access deatils from one role to another
	 *
	 * @param roleId
	 * @param copiedRoleId
	 * @return none
	 * @throws SwtException
	 */
	private void copyAccountAccess(String roleId, String copiedRoleId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [copyAccountAccess] - "
					+ "Entry");
			AccountAccessManager accountAccessManager = null;
			accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			accountAccessManager.deleteAcctAccessDetails(roleId);
			accountAccessManager.copyAccessDetails(copiedRoleId,roleId);
			log.debug(this.getClass().getName() + " - [copyAccountAccess] - "
					+ "Exit");
		}
		catch(Exception e){
			log.error(this.getClass().getName()
					+ "- [copyAccountAccess] - Exception " + e.getMessage());
			e.printStackTrace();
		}
		//End: Added by Bala on Mantis:1029 - copy account access details
	}


	public String roleBasedControl() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {

			String isViewRole = request.getParameter("isViewRole");

			if ((isViewRole != null) && isViewRole.equals("yes")) {
				request.setAttribute("isViewRole", isViewRole);
			}

			String screenTitle = request.getParameter("screenTitle");

			if ((screenTitle != null) && screenTitle.equals("addRole")) {
				request.setAttribute("screenTitle", "addRole");
			} else {
				request.setAttribute("screenTitle", "changeRole");
			}
			return getView("roleBasedControl");
		}  catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "entityAccess", RoleAction.class),
					request, "");
			return getView("fail");
		}
	}



	public String displayRoleBasedControl() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable holds the Role Id
		String roleId = null;
		// Host Id
		String hostId = null;
		// Current user Id
		String userId = null;
		// Screen Id for entity monitor
		String screenId = null;
// To remove: 		// DynaValidatorForm object
// To remove: 		DynaValidatorForm form = null;
		// HttpSession
		HttpSession session = null;
		// User object
		User user = null;
		// Collection of PersonalEntityList
		Collection<FacilityAccess> facilityAccessInfo = null;
		Collection<RoleBasedControl> roleBasedControlInfo = null;
		HashMap<String, String> facilitiesList= null;
		Iterator<FacilityAccess> itrFacilityAccess = null;
		String configExistFlag=null;
		String copiedRoleId = null;
		String operation = null;
		try {
			// getting session from request
			session = request.getSession();
			// Instantiating the ArrayList personalEntityInfo
			facilityAccessInfo = new ArrayList<FacilityAccess>();
			roleBasedControlInfo = new ArrayList<RoleBasedControl>();
// To remove: 			// Type casting ActionForm with DynaValidatorForm
// To remove: 			form = (DynaValidatorForm) actionForm;

			// Getting the current user from the session
			user = SwtUtil.getCurrentUser(session);
			// Gets the role Id from session
			//roleId = user.getRoleId();
			// Gets the host id from CacheManager
			hostId = CacheManager.getInstance().getHostId();
			// Gets the user from SwtUtil
			userId = SwtUtil.getCurrentUserId(request.getSession());
			roleId = (String) request.getSession().getAttribute(
					"roleIdInSession");
			operation= (String) request.getSession().getAttribute("Operation");
			if("copyFrom".equalsIgnoreCase(operation)) {
				copiedRoleId=	 (String)request.getSession().getAttribute("copiedRoleId");
				roleId=copiedRoleId;
			}
			// calling method to get the facility access list
			if(!SwtUtil.isEmptyOrNull(roleId)) {
				facilityAccessInfo = roleMgr.getFacilityAccessList(roleId);
			}
			if(facilityAccessInfo.size()==0 || SwtUtil.isEmptyOrNull(roleId)) {
				configExistFlag= "false";
				RoleBasedControl roleBasedControl= new RoleBasedControl();
				ArrayList<FacilityAccess> facilityAccessList= new ArrayList<FacilityAccess>();
				if(request.getSession().getAttribute("listAccessAddInSession")!=null) {
					facilityAccessList= (ArrayList<FacilityAccess>) request.getSession().getAttribute("listAccessAddInSession");
					Iterator<FacilityAccess> iterator = facilityAccessList.iterator();
					while (iterator.hasNext()) {
						FacilityAccess facility = iterator.next();
						roleBasedControl= new RoleBasedControl();
						roleBasedControl.setFacilityId(facility.getId().getFacilityId());
						String description= !SwtUtil.isEmptyOrNull(facility.getId().getFacilityId())?roleMgr.getFacilityDesc(facility.getId().getFacilityId()):"";
						roleBasedControl.setFacilityDesc(description);
						roleBasedControl.setReqAuth(facility.getReqAuth());
						roleBasedControl.setReqOthers(facility.getAuthOthers());
						roleBasedControlInfo.add(roleBasedControl);
					}


				}else {
					facilitiesList=  roleMgr.getAllFacilitiesList();
					Iterator<Map.Entry<String, String>> iterator = facilitiesList.entrySet().iterator();
					while (iterator.hasNext()) {

						Map.Entry<String, String> entry = iterator.next();
						String key = entry.getKey();
						String value = entry.getValue();
						roleBasedControl= new RoleBasedControl();
						roleBasedControl.setFacilityId(key);
						roleBasedControl.setFacilityDesc(value);
						roleBasedControl.setReqAuth("N");
						roleBasedControl.setReqOthers("N");
						roleBasedControlInfo.add(roleBasedControl);
					}

				}

			}else {
				configExistFlag= "true";
				ArrayList<FacilityAccess> facilityAccessList= new ArrayList<FacilityAccess>();
				if(request.getSession().getAttribute("listAccessUpdateInSession")!=null) {
					facilityAccessList= (ArrayList<FacilityAccess>) request.getSession().getAttribute("listAccessUpdateInSession");
					Iterator<FacilityAccess> iterator = facilityAccessList.iterator();
					while (iterator.hasNext()) {
						FacilityAccess facility = iterator.next();
						RoleBasedControl roleBasedControl= new RoleBasedControl();
						roleBasedControl= new RoleBasedControl();
						roleBasedControl.setFacilityId(facility.getId().getFacilityId());
						String description= !SwtUtil.isEmptyOrNull(facility.getId().getFacilityId())?roleMgr.getFacilityDesc(facility.getId().getFacilityId()):"";
						roleBasedControl.setFacilityDesc(description);
						roleBasedControl.setReqAuth(facility.getReqAuth());
						roleBasedControl.setReqOthers(facility.getAuthOthers());
						roleBasedControlInfo.add(roleBasedControl);
					}
				}else {
					itrFacilityAccess = facilityAccessInfo.iterator();
					while (itrFacilityAccess.hasNext()) {
						FacilityAccess record = itrFacilityAccess.next();
						RoleBasedControl roleBasedControl= new RoleBasedControl();
						roleBasedControl.setFacilityId(record.getId().getFacilityId());
						String description= !SwtUtil.isEmptyOrNull(record.getId().getFacilityId())?roleMgr.getFacilityDesc(record.getId().getFacilityId()):"";
						roleBasedControl.setFacilityDesc(description);
						roleBasedControl.setReqAuth(record.getReqAuth());
						roleBasedControl.setReqOthers(record.getAuthOthers());
						roleBasedControlInfo.add(roleBasedControl);
					}
				}
			}

			// set the facilityAccessList to request
			request.setAttribute("roleBasedControlList", roleBasedControlInfo);
			request.setAttribute("operation", operation);
			request.setAttribute("configExistFlag", configExistFlag);
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", SwtConstants.DATA_FETCH_OK);

		}catch (Exception e) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "displayRoleBasedControl", RoleAction.class),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
			return getView("fail");
		} finally {
			try {
				// Cleaning Unreferenced objects
				hostId = null;
				userId = null;
				roleId = null;
				screenId = null;
				// Closing the session
				if (session != null) {
					session = null;
				}
				facilityAccessInfo = null;
				roleBasedControlInfo = null;
				facilitiesList= null;
				itrFacilityAccess = null;
				configExistFlag=null;
				copiedRoleId = null;
				operation = null;
			} catch (Exception ignore) {
			}
		}
		return getView("rolebasedcontroldata");
	}


	public String saveFacilityAccessInSession() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String gridChangeList = null;
		FacilityAccess facilityAccess = null;
		ArrayList<FacilityAccess> listFacilityAccess = new ArrayList<FacilityAccess>();
		String roleId= null;
		String userId= null;
		String configExistFlag= null;
		try {
			log.debug(this.getClass().getName() + "- [saveFacilityAccessInSession] - starting ");

			roleId = SwtUtil.getCurrentUser(request.getSession())
					.getRoleId();
			// get the current user Id
			userId = SwtUtil.getCurrentUserId(request.getSession());
			configExistFlag=  request.getParameter("configExistFlag");
			gridChangeList = request.getParameter("gridChangesList");
			JSONArray chartsJSONArray = new JSONArray(gridChangeList);
			for (int i = 0; i < chartsJSONArray.length(); i++) {
				facilityAccess = new FacilityAccess();
				//facilityAccess.getId().setRoleId(roleId);
				facilityAccess.getId().setFacilityId(chartsJSONArray.getJSONObject(i).getString("FACILITY_ID"));
				facilityAccess.setReqAuth (chartsJSONArray.getJSONObject(i).getString("REQ_AUTH"));
				facilityAccess.setAuthOthers (chartsJSONArray.getJSONObject(i).getString("REQ_OTHERS"));
				facilityAccess.setUpdateDate(SwtUtil.getSystemDatewithoutTime());
				facilityAccess.setUpdateUser(userId);
				listFacilityAccess.add(facilityAccess);
			}
			if ("true".equalsIgnoreCase(configExistFlag)) {
				request.getSession().setAttribute("listAccessUpdateInSession", listFacilityAccess);
			}else {
				request.getSession().setAttribute("listAccessAddInSession", listFacilityAccess);
			}
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + "- [saveFacilityAccessInSession] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveFacilityAccessInSession] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			return getView("fail");

		} finally {
			log.debug(this.getClass().getName() + "- [saveFacilityAccessInSession] - Exit");
			// nullify objects
			gridChangeList = null;
			facilityAccess = null;
			listFacilityAccess = new ArrayList<FacilityAccess>();
			roleId= null;
			userId= null;
			configExistFlag= null;

		}
		return getView("statechange");
	}



}