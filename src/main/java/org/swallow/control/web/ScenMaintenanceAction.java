/*
 * @(#)ScenMaintenanceAction.java 1.0 15/12/12
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;




import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.*;
import org.swallow.control.service.ScenMaintenanceManager;
import org.swallow.control.service.SchedulerManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.model.ScenarioMessageFormats;
import org.swallow.maintenance.service.MessageFormatsManager;
import org.swallow.maintenance.web.GroupAction;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.*;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.work.model.ColumnMetadata;
import org.swallow.work.model.Movement;
import org.swallow.work.model.QueryResult;
import org.swallow.work.service.GenericDisplayMonitorManager;
import org.swallow.work.service.WorkflowMonitorManager;
import org.swallow.work.web.CurrencyMonitorNewAction;
import org.swallow.work.web.PreAdviceInputAction;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.util.*;
import java.util.Map.Entry;




































import java.util.Collection;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/scenMaintenance", "/scenMaintenance.do"})
public class ScenMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/control/scenariomaintenanceadd");
		viewMap.put("addrole", "jsp/control/scenarioroleassignmentadd");
		viewMap.put("subGuiHighlight", "jsp/control/scenariosubhighlight");
		viewMap.put("role", "jsp/control/scenarioroleassignment");
		viewMap.put("distList", "jsp/control/scenariodistributionlist");
		viewMap.put("data", "jsp/data");
		viewMap.put("openSchedDetails", "jsp/control/scenarioscheduledetails");
		viewMap.put("advanced", "jsp/control/scenarioadvanceddetail");
		viewMap.put("change", "jsp/control/scenariomaintenanceadd");
		viewMap.put("save", "jsp/control/scenariomaintenanceadd");
		viewMap.put("update", "jsp/control/scenariomaintenanceadd");
		viewMap.put("addcategory", "jsp/control/scenariocategoryadd");
		viewMap.put("openDefParam", "jsp/control/scenariodefparameters");
		viewMap.put("delete", "jsp/control/scenariomaintenance");
		viewMap.put("changecategory", "jsp/control/scenariocategoryadd");
		viewMap.put("subEvents", "jsp/control/scenariosubevent");
		viewMap.put("configRecipients", "jsp/control/configrecipients");
		viewMap.put("fail", "error");
		viewMap.put("view", "jsp/control/scenariomaintenanceadd");
		viewMap.put("alertInst", "jsp/work/alertinstancesummary");
		viewMap.put("configParams", "jsp/control/scenarioconfigscreen");
		viewMap.put("success", "jsp/control/scenariomaintenance");
		viewMap.put("category", "jsp/control/scenariocategory");
		viewMap.put("changerole", "jsp/control/scenarioroleassignmentadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");




















































	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "addScreen":
				return addScreen();
			case "subEvents":
				return subEvents();
			case "guiHighlightSub":
				return guiHighlightSub();
			case "unspecified":
				return unspecified();
			case "list":
				return list();
			case "role":
				return role();
			case "category":
				return category();
			case "change":
				return change();
			case "view":
				return view();
			case "changeRole":
				return changeRole();
			case "changeCategory":
				return changeCategory();
			case "add":
				return add();
			case "addRole":
				return addRole();
			case "advanced":
				return advanced();
			case "advancedFromAdd":
				return advancedFromAdd();
			case "advancedFromChangeAndView":
				return advancedFromChangeAndView();
			case "save":
				return save();
			case "saveRole":
				return saveRole();
			case "saveCategory":
				return saveCategory();
			case "getFacilityIdProperties":
				return getFacilityIdProperties();
			case "categoryHasChildren":
				return categoryHasChildren();
			case "getScenarioIdProperties":
				return getScenarioIdProperties();
			case "update":
				return update();
			case "updateRole":
				return updateRole();
			case "updateCategory":
				return updateCategory();
			case "delete":
				return delete();
			case "deleteRole":
				return deleteRole();
			case "deleteCategory":
				return deleteCategory();
			case "updateAdvancedDetails":
				return updateAdvancedDetails();
			case "updateFacilityList":
				return updateFacilityList();
			case "getAdditionalInfo":
				return getAdditionalInfo();
			case "getScenPropertiesForGeneric":
				return getScenPropertiesForGeneric();
			case "addCategory":
				return addCategory();
			case "displayDistList":
				return displayDistList();
			case "getGuiFacilityData":
				return getGuiFacilityData();
			case "getEventFacilityData":
				return getEventFacilityData();
			case "getQueryColumns":
				return getQueryColumns();
			case "defineParameters":
				return defineParameters();
			case "openSchedule":
				return openSchedule();
			case "ConfigureParams":
				return ConfigureParams();
			case "openAlertInstSummary":
				return openAlertInstSummary();
			case "getMsgFormatsList":
				return getMsgFormatsList();
			case "checkIfInstExist":
				return checkIfInstExist();
			case "configureRecipients":
				return configureRecipients();
			case "displayConfigRecipientsData":
				return displayConfigRecipientsData();
		}


		return unspecified();
	}


	private Facility facility;
	public Facility getFacility() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		facility = RequestObjectMapper.getObjectFromRequest(Facility.class, request);

		return facility;
	}

	public void setFacility(Facility facility) {
		this.facility = facility;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("facility", facility);
	}
	private ScenarioNotify scenarioNotification;
	public ScenarioNotify getScenarioNotification() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		scenarioNotification = RequestObjectMapper.getObjectFromRequest(ScenarioNotify.class, request,"scenarioNotification");

		return scenarioNotification;
	}

	public void setScenarioNotification(ScenarioNotify scenarioNotification) {
		this.scenarioNotification = scenarioNotification;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scenarioNotification", scenarioNotification);
	}
	private ScenarioCategory scenarioCategory;
	public ScenarioCategory getScenarioCategory() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		scenarioCategory = RequestObjectMapper.getObjectFromRequest(ScenarioCategory.class, request,"scenarioCategory");

		return scenarioCategory;
	}

	public void setScenarioCategory(ScenarioCategory scenarioCategory) {
		this.scenarioCategory = scenarioCategory;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scenarioCategory", scenarioCategory);
	}
	private Scenario scenarioMaintenance;
	public Scenario getScenarioMaintenance() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		scenarioMaintenance = RequestObjectMapper.getObjectFromRequest(Scenario.class, request,"scenarioMaintenance");

		return scenarioMaintenance;
	}

	public void setScenarioMaintenance(Scenario scenarioMaintenance) {
		this.scenarioMaintenance = scenarioMaintenance;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scenarioMaintenance", scenarioMaintenance);
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(ScenMaintenanceAction.class);

	/**
	 * Used to hold SystemAlertMessagesManager reference object
	 */
	@Autowired
	private ScenMaintenanceManager scenMaintenanceManager = null;

	/**
	 * @param scenMaintenanceManager the scenMaintenanceManager to set
	 */
	public void setScenMaintenanceManager(ScenMaintenanceManager scenMaintenanceManager) {
		this.scenMaintenanceManager = scenMaintenanceManager;
	}

	/**
	 * Set the category list with label value bean into the request attribute which
	 * will be filled in the category list combo box of the maintenance add screen
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putCategoryListInReq(HttpServletRequest request) throws SwtException {
		log.debug(this.getClass().getName() + " - [putCategoryListInReq] - " + "Entry");
		// Method's local variable declaration
		Collection categoryList = null;
		// Fetching category details from manager file
		categoryList = scenMaintenanceManager.getCategoryList();
		// Setting role details in request
		request.setAttribute("categoryTitleList", categoryList);
		log.debug(this.getClass().getName() + " - [putCategoryListInReq] - " + "Exit");
		return categoryList;
	}

	/**
	 * Collects the Role list of the host id from the ScenMaintenanceManager
	 *
	 * @param hostId
	 * @param request
	 * @throws SwtException
	 */
	private void putRoleListInReq(String hostId, HttpServletRequest request) throws SwtException {
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - " + "Entry");
		/* Method's local variable declaration */
		Collection roleList = null;
		roleList = scenMaintenanceManager.getRoleList(hostId);
		request.setAttribute("roleIdlist", roleList);
		log.debug(this.getClass().getName() + " - [putRoleListInReq] - " + "Exit");
	}

	/**
	 * Collects the facility list which will be edited for a scenario and put it in
	 * the request. Note that this list does not contain any facility not selectable
	 * (with P_FAICILITY.USER_SELECTABLE = 'N'). Also, it depends on the boolean
	 * genericDisplaySelected: - If true then the list contains the facilities with
	 * the condition P_FAICILITY.USER_SELECTABLE != 'G' - I false then the list
	 * contains the facilities with the condition P_FAICILITY.USER_SELECTABLE = 'G'
	 *
	 * @param request
	 * @param genericDisplaySelected
	 * @throws SwtException
	 */
	private Collection putFacilityListForEditInReq(HttpServletRequest request, boolean genericDisplaySelected)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putFacilityListForEditInReq] - " + "Entry");
		Collection facilityListForEdit = scenMaintenanceManager.getFacilityListForEdit(genericDisplaySelected);
		request.setAttribute("facilityList", facilityListForEdit);
		log.debug(this.getClass().getName() + " - [putFacilityListForEditInReq] - " + "Exit");
		return facilityListForEdit;
	}

	/**
	 * This method gets entity list from database and put them in request scope
	 *
	 * @param request HttpServletRequest request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request) throws SwtException {
		// Logged in user's session
		HttpSession session = null;
		// To hold entity list allocated for user
		ArrayList<LabelValueBean> alEntity = null;
		// To check whether the user has right for "All" entity option
		WorkflowMonitorManager workFlowMonitorManager = null;
		// Role id associated with logged-in user, to check "All" entity option
		String roleId = null;

		try {
			// log debug message
			log.debug(this.getClass().getName() + "- [putEntityListInReq] - Entry");

			// Logged-in user's session
			session = request.getSession();
			// Get entity list belongs to the user
			alEntity = (ArrayList<LabelValueBean>) SwtUtil
					.convertEntityAcessCollectionLVL(SwtUtil.getUserEntityAccessList(session), session);

			// Get implementation of WorkflowMonitorManager to check "All"
			// entity option
			workFlowMonitorManager = (WorkflowMonitorManager) (SwtUtil.getBean("workflowMonitorManager"));
			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();

			// If the user has right for "All" entity option, add the option
			if (workFlowMonitorManager.getAllEntityOption(roleId)) {
				alEntity.add(0, new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
			}
			// put entity list in request scope
			request.setAttribute("entities", alEntity);
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName() + " - [putEntityListInReq] - SwtException -" + ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - [putEntityListInReq] - Exception -" + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "putEntityListInReq",
					CurrencyMonitorNewAction.class);
		} finally {
			// nullify objects
			session = null;
			alEntity = null;
			workFlowMonitorManager = null;
			roleId = null;
			// log debug message
			log.debug(this.getClass().getName() + "- [putEntityListInReq] - Exit");
		}
	}

	/**
	 * Method called to populate request with scenarioList
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putScenarioListInReq(HttpServletRequest request) throws SwtException {
		log.debug(this.getClass().getName() + " - [putScenarioListInReq] - " + "Entry");
		Collection scenarioList = scenMaintenanceManager.getScenarioList();
		request.setAttribute("scenarioList", scenarioList);
		log.debug(this.getClass().getName() + " - [putScenarioListInReq] - " + "Exit");
	}

	/**
	 * Collects the selectable facility list
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putFacilityListInReq(HttpServletRequest request) throws SwtException {
		log.debug(this.getClass().getName() + " - [putFacilityListInReq] - " + "Entry");
		Collection facilityList = scenMaintenanceManager.getFacilityList();
		request.setAttribute("facilityList", facilityList);
		log.debug(this.getClass().getName() + " - [putFacilityListInReq] - " + "Exit");
		return facilityList;
	}

	/**
	 * Collects the summary grouping list
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putSummaryGroupingListInReq(HttpServletRequest request) throws SwtException {
		log.debug(this.getClass().getName() + " - [putSummaryGroupingListInReq] - " + "Entry");
		Collection sumGroupingList = scenMaintenanceManager.getSummaryGroupingList();
		request.setAttribute("summaryGroupingList", sumGroupingList);
		log.debug(this.getClass().getName() + " - [putSummaryGroupingListInReq] - " + "Exit");
		return sumGroupingList;
	}

	/**
	 * Method to define the Button status in the screen
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param viewStatus
	 * @param deleteStatus
	 * @param roleStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus, String changeStatus, String viewStatus,
								 String deleteStatus, String roleStatus) {

		log.debug(this.getClass().getName() + " - [setButtonStatus] - " + "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.VIEW_BUT_STS, viewStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.ROLL_BUT_STS, roleStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - " + "Exit");
	}

	/**
	 * Default method of the action returns List
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String addScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("methodName", request.getParameter("screenName"));
		request.setAttribute("selectedScenarioID", request.getParameter("selectedScenarioID"));
		request.setAttribute("selectedSystemFlag", request.getParameter("selectedSystemFlag"));
		request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
		request.setAttribute("fromAdvanced", request.getParameter("fromAdvanced"));
		return getView("add");
	}

	public String subEvents() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("subEvents");
	}

	public String guiHighlightSub() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("subGuiHighlight");
	}

	public String unspecified() throws Exception {
		log.debug(this.getClass().getName() + " - [unspecified] - " + "Return list Action");
		return list();
	}

	/**
	 * List all Scenarios from database
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String list() throws Exception {
		int accessInd;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [list] - " + "Entry");

			/* Method's class instance and local variable declaration */
			ArrayList scenarioMaintenanceDetail;

			/*
			 * Set the screenFieldsStatus to true because we have to display the list of
			 * scenarios. if screenFieldsStatus is true then disable the related fields
			 * which contain this attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			/* Collect the Scenario list in scenarioMaintenanceDetailVO */
			scenarioMaintenanceDetail = scenMaintenanceManager.getScenariosDetailList(null);
			StringBuilder textInRunEveryColumn = null;
			boolean textTruncated = false;
			for (Iterator<Scenario> it = scenarioMaintenanceDetail.iterator(); it.hasNext();) {
				Scenario scen = (Scenario) it.next();
				if("S".equals(scen.getGenerationBasis())) {
//					Scheduled: (08:00, 10:00, 12:30...) show first 3 defined and use '...'
					textInRunEveryColumn = new StringBuilder();
					textInRunEveryColumn.append("Scheduled:(");

					Collection scenScheduler = scenMaintenanceManager.getScenarioSchedule(scen.getId().getScenarioId());
					int numberOfSchedule = 0;
					if(scenScheduler != null) {
						for (Iterator<ScenarioSchedule> it2 = scenScheduler.iterator(); it2.hasNext();) {
							ScenarioSchedule scenSchedule = (ScenarioSchedule) it2.next();
							String time=(!SwtUtil.isEmptyOrNull(scenSchedule.getCheckTime()))?scenSchedule.getCheckTime():"";
							textInRunEveryColumn.append(time+",");
							numberOfSchedule++;
							if(numberOfSchedule==3) {
								textTruncated = true;
								break;
							}
						}
					}
					if(textTruncated) {
						textInRunEveryColumn.append("...");
					}
					textInRunEveryColumn.append(" )");
					scen.setRunEvery(textInRunEveryColumn.toString().replace(", )", ")").replace(":( )", ""));


				}else if("A".equals(scen.getGenerationBasis())) {

				}else {
					textInRunEveryColumn = new StringBuilder();
					textInRunEveryColumn.append("Cyclic:(");
//					Cyclic: (Every <run_every> between <start> and <end>
					if(!SwtUtil.isEmptyOrNull(scen.getRunEvery())) {
						textInRunEveryColumn.append(" Every "+scen.getRunEvery());
					}

					if(!SwtUtil.isEmptyOrNull(scen.getStartTime())) {
						textInRunEveryColumn.append(" between "+scen.getStartTime() +" and "+scen.getEndTime());
					}
					textInRunEveryColumn.append(" )");
					scen.setRunEvery(textInRunEveryColumn.toString().replace(":( )", ""));

				}
			}
			request.setAttribute("scenariosMaintenanceDetails", scenarioMaintenanceDetail);
			/*
			 * Pass the Request parameter to set MiscParams of alert to the request
			 * attribute
			 */
			putCategoryListInReq(request);

			/* Get the Access Id of the for the screen */
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			log.debug(this.getClass().getName() + " - [list] - " + "Exit");
			return getView("success");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName() + " - Exception Catched in [list] method : -" + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [list] method : -" + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [list] method : -" + e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [list] method : -" + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "list", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * List the Role Assignment for all Scenarios.
	 * @return ActionForward
	 * @throws Exception
	 */
	public String role() throws Exception {
		/* Method's class instance and local variable declaration */
		ArrayList scenarioNotifDetails;
		int accessInd;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [role] - " + "Entry");

			/*
			 * Set the screenFieldsStatus to true as we are in the role screen. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = Integer.parseInt(request.getParameter("menuAccessId"));

			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			/* Collect the Alert message list in SystemAlertMessagesDetailVO */
			scenarioNotifDetails = scenMaintenanceManager.getScenariosNotificationDetailList();
			request.setAttribute("scenarioNotificationDetails", scenarioNotifDetails);
			request.setAttribute("menuAccessId", accessInd);
			/* Get the Access Id of the for the screen */
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);

			log.debug(this.getClass().getName() + " - [role] - " + "Exit");
			return getView("role");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName() + " - Exception Catched in [role] method : -" + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [role] method : -" + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [role] method : -" + e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [role] method : -" + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(e, "role", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * List the Role Assignment for all Scenarios.
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String category() throws Exception {
		/* Method's class instance and local variable declaration */
		ArrayList scenarioCategoryDetails;
		int accessInd;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [category] - " + "Entry");

			/*
			 * Set the screenFieldsStatus to true as we are in the role screen. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = Integer.parseInt(request.getParameter("menuAccessId"));

			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			/* Collect the Alert message list in SystemAlertMessagesDetailVO */
			scenarioCategoryDetails = (ArrayList) scenMaintenanceManager.getCategoryDetailedList();

			/*
			 * tabNames = PropertiesFileLoader.getInstance()
			 * .getPropertiesValue(SwtConstants.SCENARIO_CATEGORY_TAB_NAMES);
			 * if(!SwtUtil.isEmptyOrNull(tabNames)) { tabNamesArray =
			 * tabNames.split("\\s*,\\s*"); if(tabNamesArray.length>0) { for(int i = 0 ; i<
			 * scenarioCategoryDetails.size(); i++) { category =
			 * (ScenarioCategory)scenarioCategoryDetails.get(i); tabToDisplay =
			 * category.getDisplayTab()-1;
			 * ((ScenarioCategory)scenarioCategoryDetails.get(i)).setDisplayTabName(
			 * tabNamesArray[tabToDisplay]); } } }
			 */

			String tabName1 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME);
			String tabName2 = PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME);

			Integer displayedTab = null;

			for (int i = 0; i < scenarioCategoryDetails.size(); i++) {
				displayedTab = ((ScenarioCategory) scenarioCategoryDetails.get(i)).getDisplayTab();
				if (displayedTab == null || displayedTab == 1)
					((ScenarioCategory) scenarioCategoryDetails.get(i)).setDisplayTabName(tabName1);
				else
					((ScenarioCategory) scenarioCategoryDetails.get(i)).setDisplayTabName(tabName2);

			}
			request.setAttribute("scenarioCategoryDetails", scenarioCategoryDetails);
			request.setAttribute("menuAccessId", accessInd);
			/* Get the Access Id of the for the screen */
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [category] - " + "Exit");
			return getView("category");
		} catch (SwtException swtexp) {

			log.debug(
					this.getClass().getName() + " - Exception Catched in [category] method : -" + swtexp.getMessage());

			log.error(
					this.getClass().getName() + " - Exception Catched in [category] method : -" + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.debug(this.getClass().getName() + " - Exception Catched in [category] method : -" + e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [category] method : -" + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "category", ScenMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * Method called when change Button clicked on scenario maintenance screen.
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String change() throws Exception {

		/* Method's local variable and class instance declaration */
		Scenario scenario;
		Scenario scenarioForm;
		String scenarioID;
		String systemFlag;
		String oldVal;
		String fromAdvanced = null;
		Facility facility;
		ScenarioCategory category;
		String categoryTitle = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Boolean useGenericDisplay = false;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String defaultSelections = null;
		String savedAlertInstCols = null;
		Integer itemId = null;
		String  itemText = null;
		String baseQuery = null;
		QueryResult queryResult=null;
		ArrayList<ColumnDTO> columnData = null;
		ColumnMetadata metadata;
		List<String> columnsName= null;
		String[] refColumns = null;
		Collection scenScheduler=null;
		String hostId=null;
		String time=null;
		String scheduleXml=null;
		HashMap<String, String> scheduleParams = new LinkedHashMap<String, String>();
		HashMap<String, String> otherIdMap = new HashMap<String, String>();
		String savedResolRefCols = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [change] - " + "Entry");
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			scenarioID = request.getParameter("selectedScenarioID").trim();
			scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioID);
			category = scenMaintenanceManager.getCategoryEditableData(scenario.getCategoryId());
			// get all scenario schedule
			scenScheduler = scenMaintenanceManager.getScenarioSchedule(scenarioID);
			if(scenScheduler != null) {
				for (Iterator<ScenarioSchedule> it = scenScheduler.iterator(); it.hasNext();) {
					ScenarioSchedule scen = (ScenarioSchedule) it.next();
					time=(!SwtUtil.isEmptyOrNull(scen.getCheckTime()))?scen.getCheckTime():"";
					if(!SwtUtil.isEmptyOrNull(scen.getParameterXml())) {
						scheduleXml=scen.getParameterXml().replace(">", "#");
					}else {
						scheduleXml = "<PARAMETERS#</PARAMETERS#";
					}
					scheduleParams.put(scen.getScenScheduleId().toString()+"-"+time, scheduleXml);
				}
			}
//			System.err.println(scheduleXml);

			// Added by Fmrad
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("scenarioId", scenario.getId().getScenarioId());
			responseConstructor.createElement("scenarioTitle", scenario.getTitle());
			responseConstructor.createElement("scenarioDesc", scenario.getDescription());
			responseConstructor.createElement("selectedSystemFlag", scenario.getSystemFlag());
			responseConstructor.createElement("selectedActiveFlag", scenario.getActiveFlag());
			// GeneralTab
			responseConstructor.createElement("selectedCategoryId", scenario.getCategoryId());
			responseConstructor.createElement("displayOrder", scenario.getDisplayOrder());
			responseConstructor.createElement("runEvery", scenario.getRunEvery());
			responseConstructor.createElement("startTime", scenario.getStartTime());
			responseConstructor.createElement("endTime", scenario.getEndTime());
			responseConstructor.createElement("emailWhenDiff", scenario.getEmailWhenDiff());
			responseConstructor.createElement("requiredParamsXml", (scenario.getScheduleParametersXml()== null)? "" : scenario.getScheduleParametersXml().replace(">", "#"));
			responseConstructor.createElement("scheduleParamsXml", ((scheduleParams.toString().replace("{", "")).replace("}", "")).replace(" ", ""));
			responseConstructor.createElement("savedApiParams", scenario.getApiRequiredCols());
			responseConstructor.createElement("savedGenBasis", scenario.getGenerationBasis());
			// IdentificationTab
			responseConstructor.createElement("queryText", !SwtUtil.isEmptyOrNull(scenario.getQueryText())?SwtUtil.encode64(scenario.getQueryText()):"");
			responseConstructor.createElement("hostCol", scenario.getSecHostCol());
			responseConstructor.createElement("entityCol", scenario.getSecEntityCol());
			responseConstructor.createElement("currencyCol", scenario.getSecCurrencyCol());
			responseConstructor.createElement("amountCol", scenario.getAmtThresholdCol());
			responseConstructor.createElement("useGenericDisplay", scenario.getUseGenericDisplay());
			//responseConstructor.createElement("facilityRefCols", scenario.getFacilityRefCols());
			responseConstructor.createElement("facilityParamVals", scenario.getFacilityParamVals());
			responseConstructor.createElement("selectedfacilityId",
					scenario.getFacility() != null ? scenario.getFacility().getFacilityid() : "None");
			responseConstructor.createElement("selectedSummaryGrouping", scenario.getSummaryGrouping());

			// InstanceTab
			responseConstructor.createElement("recordScenInstance", scenario.getRecordScenarioInstance());
			responseConstructor.createElement("uniqueExpression", scenario.getInstanceUniqueExpression() != null?SwtUtil.encode64(scenario.getInstanceUniqueExpression()):"");
			responseConstructor.createElement("accountIdColumn", scenario.getAccountColumn());
			responseConstructor.createElement("signColumn", scenario.getSignCol());

			responseConstructor.createElement("mvtColumn", scenario.getMvtColumn());
			responseConstructor.createElement("matchColumn", scenario.getMatchColumn());
			responseConstructor.createElement("sweepColumn", scenario.getSweepColumn());
			responseConstructor.createElement("payColumn", scenario.getPaymentColumn());
			responseConstructor.createElement("valueDateColumn", scenario.getValueDateColumn());
			responseConstructor.createElement("otherIdColumn", scenario.getOtherIdColumn());
			responseConstructor.createElement("otherIdTypeColumn", scenario.getOtherIdTypeColumn());
			responseConstructor.createElement("customTreeLevel1", scenario.getCustomTreeLevel1());
			responseConstructor.createElement("customTreeLevel2", scenario.getCustomTreeLevel2());

			responseConstructor.createElement("instanceExpiryMins",(scenario.getInstanceExpiryMins() != null ? scenario.getInstanceExpiryMins().toString(): ""));
			responseConstructor.createElement("reRaiseAfterExpiry", scenario.getAllowReraiseAfterExpiry());
			responseConstructor.createElement("minsAfterExpiry",(scenario.getReraiseIntervalMins() != null ? scenario.getReraiseIntervalMins().toString(): ""));

			// GuiHighlight Tab
			responseConstructor.createElement("criticalGuiHighlight", scenario.getCriticalGuiHighlight());

			// EventTab
			responseConstructor.createElement("afterTrigEvent", scenario.getAfterTrigEvent());
			responseConstructor.createElement("resolutionQueryText", !SwtUtil.isEmptyOrNull(scenario.getScenarioResolutionQueryText())?SwtUtil.encode64(scenario.getScenarioResolutionQueryText()):"");
			responseConstructor.createElement("resolutionRefCols", scenario.getResolutionRefCols());
			responseConstructor.createElement("pendingResolutionTimeLimit",
					(scenario.getPendingResolutionTimeLimit() != null ? scenario.getPendingResolutionTimeLimit().toString(): ""));

			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** IdentificationTab MutiSelect ref columns Combo default selections ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			lstOptions = new ArrayList<OptionInfo>();
			defaultSelections= scenario.getFacilityRefCols();
			if (!SwtUtil.isEmptyOrNull(defaultSelections)) {
				if(defaultSelections.startsWith("[") ) {
					JSONArray SelectionsJSONArray = new JSONArray(defaultSelections);
					for (int i = 0; i < SelectionsJSONArray.length(); i++) {
						itemId=SelectionsJSONArray.getJSONObject(i).getInt("value");
						itemText=SelectionsJSONArray.getJSONObject(i).getString("content");
						lstOptions.add(new OptionInfo(itemId.toString(), itemText, false));
					}
				}
				//added to fix old sscenarios issue (records without query)
				else {
					refColumns=defaultSelections.split(",");
					for (int i = 0; i < refColumns.length; i++) {
						Integer index=i;
						lstOptions.add(new OptionInfo(index.toString(), refColumns[i], false));
					}
				}
			}
			lstSelect.add(new SelectInfo("listSelections", lstOptions));


			/***** IdentificationTab MutiSelect alert instance Combo default selections ***********/
			lstOptions = new ArrayList<OptionInfo>();
			savedAlertInstCols= scenario.getAlertInstanceColumn();
			if (!SwtUtil.isEmptyOrNull(savedAlertInstCols)) {
				if(savedAlertInstCols.startsWith("[") ) {
					JSONArray AlertInstJSONArray = new JSONArray(savedAlertInstCols);
					for (int i = 0; i < AlertInstJSONArray.length(); i++) {
						itemId=AlertInstJSONArray.getJSONObject(i).getInt("value");
						itemText=AlertInstJSONArray.getJSONObject(i).getString("content");
						lstOptions.add(new OptionInfo(itemId.toString(), itemText, false));
					}
				}
			}
			lstSelect.add(new SelectInfo("savedAlertInstCols", lstOptions));

			/***** IdentificationTab get columns options list (columns) from the user query ***********/
			lstOptions = new ArrayList<OptionInfo>();
			GenericDisplayMonitorManager genericDisplayMonitorManager = (GenericDisplayMonitorManager) SwtUtil
					.getBean("genericDisplayMonitorManager");
			baseQuery = scenario.getQueryText();

			ArrayList<OptionInfo> nbrLstOptions = new ArrayList<OptionInfo>();
			ArrayList<OptionInfo> textLstOptions = new ArrayList<OptionInfo>();
			ArrayList<OptionInfo> dateLstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("","", false));
			nbrLstOptions.add(new OptionInfo("","", false));
			textLstOptions.add(new OptionInfo("","", false));
			dateLstOptions.add(new OptionInfo("","", false));
			if(!SwtUtil.isEmptyOrNull( scenario.getQueryExecutionListColumns())) {
				String[] listOfcolumns = scenario.getQueryExecutionListColumns().split("#\\$#")[0].split(",");
				String[] listOfColsTypes = scenario.getQueryExecutionListColumns().split("#\\$#")[1].split(",");
				for (int i = 0; i < listOfcolumns.length; i++) {
					itemId=i;
					lstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
					if ("NUMBER".equalsIgnoreCase(listOfColsTypes[i])
							|| "INTEGER".equalsIgnoreCase(listOfColsTypes[i])
							|| "LONG".equalsIgnoreCase(listOfColsTypes[i])
							|| "FLOAT".equalsIgnoreCase(listOfColsTypes[i])) {
						nbrLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
					}

					if ("VARCHAR2".equalsIgnoreCase(listOfColsTypes[i])
							|| "CHAR".equalsIgnoreCase(listOfColsTypes[i])
							|| "CLOB".equalsIgnoreCase(listOfColsTypes[i])) {
						textLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
					}

					if ("DATE".equalsIgnoreCase(listOfColsTypes[i])) {

						dateLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
					}

				}

				// Define a custom comparator to compare OptionInfo objects based on their names
				Comparator<OptionInfo> optionComparator = new Comparator<OptionInfo>() {
					public int compare(OptionInfo o1, OptionInfo o2) {
						// Check for null values
						if (SwtUtil.isEmptyOrNull(o1.getText()) && SwtUtil.isEmptyOrNull(o2.getText())) {
							return 0; // Both are null, consider them equal
						} else if (SwtUtil.isEmptyOrNull(o1.getText())) {
							return -1; // optionInfo1 is null, so it comes before optionInfo2
						} else if (SwtUtil.isEmptyOrNull(o2.getText())) {
							return 1; // optionInfo2 is null, so it comes before optionInfo1
						}else {
							// Compare based on column texts
							return o1.getText().compareToIgnoreCase(o2.getText());
						}

					}
				};

				if (lstOptions!= null) {
					// Sort queryColumns
					Collections.sort(lstOptions, optionComparator);
				}


				if (nbrLstOptions!= null) {
					// Sort nbrLstOptions
					Collections.sort(nbrLstOptions, optionComparator);
				}

				if (textLstOptions!= null) {
					// Sort textLstOptions
					Collections.sort(textLstOptions, optionComparator);
				}

				if (dateLstOptions!= null) {
					// Sort dateLstOptions
					Collections.sort(dateLstOptions, optionComparator);
				}

				lstSelect.add(new SelectInfo("queryColumns", lstOptions));
				lstSelect.add(new SelectInfo("numbersColumns", nbrLstOptions));
				lstSelect.add(new SelectInfo("textColumns", textLstOptions));
				lstSelect.add(new SelectInfo("dateColumns", dateLstOptions));


				//query is null we need to send selected values as combo dataprovider
			}else {
				StringBuilder colsBuilder = new StringBuilder();
				StringBuilder typesBuilder = new StringBuilder();
				String listOfcolumnsLastExecution = null;
				String listOftypesLastExecution = null;
				if(!SwtUtil.isEmptyOrNull(baseQuery)) {
					String replalcedBaseQuery = baseQuery.replaceAll("P\\{.*?}", "''");
					if (replalcedBaseQuery != null)
						queryResult = genericDisplayMonitorManager.getGenericDisplayData(replalcedBaseQuery);

				}
				columnData = new ArrayList<ColumnDTO>();
				columnsName = new ArrayList<String>();
				lstOptions.add(new OptionInfo("","", false));
				if (queryResult != null) {
					for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
						metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
						itemId=i;
						lstOptions.add(new OptionInfo(itemId.toString(), '"'+metadata.getColumnLabel()+'"', false));
						colsBuilder.append('"'+metadata.getColumnLabel()+'"'+",");
						typesBuilder.append(metadata.getColumnTypeName()+",");
					}
					listOfcolumnsLastExecution = colsBuilder.toString();
					listOftypesLastExecution = typesBuilder.toString();
					if(listOfcolumnsLastExecution.length()>0 && listOftypesLastExecution.length()>0) {
						listOfcolumnsLastExecution = listOfcolumnsLastExecution.substring(0, listOfcolumnsLastExecution.length()-1)
								+ "#$#" + listOftypesLastExecution.substring(0, listOftypesLastExecution.length()-1);
						scenMaintenanceManager.updateScenarioQueryExecutionListColumns(scenario.getId().getScenarioId(), listOfcolumnsLastExecution);
					}
					lstSelect.add(new SelectInfo("queryColumns", lstOptions));

					String[] listOfcolumns = listOfcolumnsLastExecution.split("#\\$#")[0].split(",");
					String[] listOfColsTypes = listOfcolumnsLastExecution.split("#\\$#")[1].split(",");

					for (int i = 0; i < listOfcolumns.length; i++) {
						itemId=i;
						lstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
						if ("NUMBER".equalsIgnoreCase(listOfColsTypes[i])
								|| "INTEGER".equalsIgnoreCase(listOfColsTypes[i])
								|| "LONG".equalsIgnoreCase(listOfColsTypes[i])
								|| "FLOAT".equalsIgnoreCase(listOfColsTypes[i])) {
							nbrLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
						}

						if ("VARCHAR2".equalsIgnoreCase(listOfColsTypes[i])
								|| "CHAR".equalsIgnoreCase(listOfColsTypes[i])
								|| "CLOB".equalsIgnoreCase(listOfColsTypes[i])) {
							textLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
						}

						if ("DATE".equalsIgnoreCase(listOfColsTypes[i])) {

							dateLstOptions.add(new OptionInfo(itemId.toString(), listOfcolumns[i], false));
						}

					}

					// Define a custom comparator to compare OptionInfo objects based on their names
					Comparator<OptionInfo> optionComparator = new Comparator<OptionInfo>() {
						public int compare(OptionInfo o1, OptionInfo o2) {

							// Check for null values
							if (SwtUtil.isEmptyOrNull(o1.getText()) && SwtUtil.isEmptyOrNull(o2.getText())) {
								return 0; // Both are null, consider them equal
							} else if (SwtUtil.isEmptyOrNull(o1.getText())) {
								return -1; // optionInfo1 is null, so it comes before optionInfo2
							} else if (SwtUtil.isEmptyOrNull(o2.getText())) {
								return 1; // optionInfo2 is null, so it comes before optionInfo1
							}else {
								// Compare based on column texts
								return o1.getText().compareToIgnoreCase(o2.getText());
							}
						}
					};

					if(nbrLstOptions!=null) {
						// Sort nbrLstOptions
						Collections.sort(nbrLstOptions, optionComparator);
					}

					if(textLstOptions!=null) {
						// Sort textLstOptions
						Collections.sort(textLstOptions, optionComparator);
					}

					if(dateLstOptions!=null) {
						// Sort dateLstOptions
						Collections.sort(dateLstOptions, optionComparator);
					}

					lstSelect.add(new SelectInfo("numbersColumns", nbrLstOptions));
					lstSelect.add(new SelectInfo("textColumns", textLstOptions));
					lstSelect.add(new SelectInfo("dateColumns", dateLstOptions));



				}
				//query is null we need to send selected values as combo dataprovider
				else {
					lstOptions = new ArrayList<OptionInfo>();
					lstOptions.add(new OptionInfo("0",scenario.getSecHostCol(), false));
					lstSelect.add(new SelectInfo("selectedHost", lstOptions));

					lstOptions = new ArrayList<OptionInfo>();
					lstOptions.add(new OptionInfo("0",scenario.getSecEntityCol(), false));
					lstSelect.add(new SelectInfo("selectedEntity", lstOptions));

					lstOptions = new ArrayList<OptionInfo>();
					lstOptions.add(new OptionInfo("0",scenario.getSecCurrencyCol(), false));
					lstSelect.add(new SelectInfo("selectedCurrency", lstOptions));

					lstOptions = new ArrayList<OptionInfo>();
					lstOptions.add(new OptionInfo("0", scenario.getAmtThresholdCol(), false));
					lstSelect.add(new SelectInfo("selectedAmount", lstOptions));


				}
			}


			/***** EventTab MutiSelect alert instance Combo saved selections ***********/
			lstOptions = new ArrayList<OptionInfo>();
			savedResolRefCols= scenario.getResolutionRefCols();
			if (!SwtUtil.isEmptyOrNull(savedResolRefCols)) {
				if(savedResolRefCols.startsWith("[") ) {
					JSONArray ResolRefColsJSONArray = new JSONArray(savedResolRefCols);
					for (int i = 0; i < ResolRefColsJSONArray.length(); i++) {
						itemId=ResolRefColsJSONArray.getJSONObject(i).getInt("value");
						itemText=ResolRefColsJSONArray.getJSONObject(i).getString("content");
						lstOptions.add(new OptionInfo(itemId.toString(), itemText, false));
					}
				}
			}
			lstSelect.add(new SelectInfo("savedResolRefCols", lstOptions));



			/***** gui highlight tab grid combo options ***********/
			/*lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("","", false));
			if (queryResult != null) {
				for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
					metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
		            lstOptions.add(new OptionInfo(metadata.getColumnLabel(), '"'+metadata.getColumnLabel()+'"', false));

				}
				lstSelect.add(new SelectInfo("GuiOtherIdOptions", lstOptions));
			}*/
			/***** Category Combo ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection catgoryList = putCategoryListInReq(request);
			Iterator j = catgoryList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("categoryList", lstOptions));
			/***** Summary Grouping Combo ***********/
			lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection summaryGroupingList = putSummaryGroupingListInReq(request);
			j = summaryGroupingList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("summaryGroupingList", lstOptions));
			/***** Facility Combo ***********/

			lstOptions = new ArrayList<OptionInfo>();
			row = null;

			if ("Y".equals(scenario.getUseGenericDisplay()))
				useGenericDisplay = true;
			Collection facilityList = null;
			// It depends on the system flag of the selected scenario.
			if (scenario.getSystemFlag().equals(SwtConstants.YES)) {
				// send all the list of facilities
				facilityList = putFacilityListInReq(request);
			} else {
				facilityList = putFacilityListForEditInReq(request, useGenericDisplay);
			}

			j = facilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("facilityList", lstOptions));

			/** event tab email templates combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection templates = scenMaintenanceManager.getEmailTemplates();
			j = templates.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("emailTemplatesList", lstOptions));


			/** SubGuiHighlight facility combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection guiFacilityList = null;
			guiFacilityList = scenMaintenanceManager.getGuiFacilityList();
			lstOptions.add(new OptionInfo("", "", false));
			j = guiFacilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("guiHighlightFacilityList", lstOptions));

			/** SubEvent facility combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection eventFacilityList = null;
			eventFacilityList = scenMaintenanceManager.getEventFacilityList();
			lstOptions.add(new OptionInfo("", "", false));
			j = eventFacilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				//add below condition to disable future implementation (send email facility)
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("guiEventFacilityList", lstOptions));

			/** Execute when combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("A", "Always", false));
			lstOptions.add(new OptionInfo("E", "Only when one or more prior events fail (result in an error)", false));
			lstOptions.add(new OptionInfo("S", "Only when prior events execute without error", false));

			lstSelect.add(new SelectInfo("executeWhenList", lstOptions));

			/** Api type combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("O", "Oracle", false));
			lstSelect.add(new SelectInfo("apiTypeList", lstOptions));


			/** scenario message formats combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection messageFormats=getScenMsgFormats(request,scenario.getId().getScenarioId());
			j = messageFormats.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("messageFormats", lstOptions));

			/** map from values combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("Instance Attribute", "Instance Attribute", false));
			lstOptions.add(new OptionInfo("Literal", "Literal", false));
			lstOptions.add(new OptionInfo("Ignore", "Ignore", false));

			lstSelect.add(new SelectInfo("mapFromComboVal", lstOptions));


			/** istance tab other id types combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection types = scenMaintenanceManager.getOtherIdTypes();
			lstOptions.add(new OptionInfo("", "", false));
			j = types.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherIdTypeCombo", lstOptions));

			responseConstructor.formSelect(lstSelect);

			/******* GUIHighlight grid ******/

			Collection guiAlertFacilityList = null;
			guiAlertFacilityList = scenMaintenanceManager.getGuiFacilityGrid();

			//get scenario mapping list
			List facilities = scenMaintenanceManager.getScenGuiFacilityMappingList(scenario.getId().getScenarioId());

			//get other id from mapping table
			//otherIdMap = scenMaintenanceManager.getFacilityMappingOtherId(scenario.getId().getScenarioId());
			responseConstructor.formGridStart("guiGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "guiGrid"));
			// form rows (records)
			responseConstructor.formRowsStart(guiAlertFacilityList.size());
			for (Iterator it = guiAlertFacilityList.iterator(); it.hasNext();) {
				ScenarioGuiAlertFacility guiAlert = (ScenarioGuiAlertFacility) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement("guiIdDescripion",
						guiAlert.getId().getGuiFacilityId()+ " - " + guiAlert.getDescription());
				responseConstructor.createRowElement("guiSelect", facilities.contains(guiAlert.getId().getGuiFacilityId())? "Y" : "N");
				responseConstructor.createRowElement("colorflag", "N");
				//responseConstructor.createRowElement("guiOtherId", SwtUtil.isEmptyOrNull(otherIdMap.get(guiAlert.getId().getGuiFacilityId()))? "HOST_ID" :otherIdMap.get(guiAlert.getId().getGuiFacilityId()));
				responseConstructor.createRowElement("guiScenInstance", guiAlert.getRequiresScenarioInstance());
				responseConstructor.createRowElement("guiRequiredParams", SwtUtil.isEmptyOrNull(guiAlert.getRequiredParameters())? "":guiAlert.getRequiredParameters());
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			/******* SubGUIHighlight grid ******/
			responseConstructor.formGridStart("subGuiGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "subGuiGrid"));

			responseConstructor.formRowsStart(30);
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* SubEvent email case grid ******/
			responseConstructor.formGridStart("emailGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "emailGrid"));
			responseConstructor.formGridEnd();

			/******* EVent grid ******/
			Collection eventMappingList = null;
			eventMappingList = scenMaintenanceManager.getEventMapping(scenario.getId().getScenarioId());
			responseConstructor.formGridStart("eventGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "eventGrid"));
			responseConstructor.formRowsStart(eventMappingList.size());
			for (Iterator<ScenarioEventMapping> it = eventMappingList.iterator(); it.hasNext();) {
				ScenarioEventMapping eventMapping = (ScenarioEventMapping) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement("eventSeq", eventMapping.getOrdinal());
				responseConstructor.createRowElement("eventId", eventMapping.getEventFacilityId());
				responseConstructor.createRowElement("eventDescription", eventMapping.getUserDescription());
				if("SEND_MESSAGE".equalsIgnoreCase(eventMapping.getEventFacilityId())) {
					responseConstructor.createRowElement("eventParams", eventMapping.getParametersXml(), false, false);
				}else {
					responseConstructor.createRowElement("eventParams" , "XML", eventMapping.getParametersXml(), false, true);
				}
				//responseConstructor.createRowElement("eventParams", eventMapping.getParametersXml());
				responseConstructor.createRowElement("eventRepeat", eventMapping.getRepeatOnReraise());
				responseConstructor.createRowElement("eventExecuteWhen", eventMapping.getExecuteWhen());
				responseConstructor.createRowElement("mapKey", eventMapping.getMapKey().toString());
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* SubEVent grid ******/
			responseConstructor.formGridStart("subEventGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "subEventGrid"));
			responseConstructor.formRowsStart(30);
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* General grid ******/
			responseConstructor.formGridStart("generalGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "generalGrid"));
			responseConstructor.formGridEnd();

			/******* define Parameters grid ******/
			responseConstructor.formGridStart("defParamsGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "defParamsGrid"));
			responseConstructor.formRowsStart(1);
			responseConstructor.formRowStart();
			responseConstructor.createRowElement("name", "");
			responseConstructor.createRowElement("description", "");
			responseConstructor.formRowEnd();
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* schedule details grid ******/
			responseConstructor.formGridStart("scheduleGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "scheduleGrid"));
			responseConstructor.formGridEnd();


			/******* roles main grid ******/
			responseConstructor.formGridStart("rolesMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "rolesMainGrid", request));
			responseConstructor.formGridEnd();


			/******* users main grid ******/
			responseConstructor.formGridStart("usersMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "usersMainGrid", request));
			responseConstructor.formGridEnd();


			/******* other email main grid ******/
			responseConstructor.formGridStart("otherEmailMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "otherEmailMainGrid",request));
			responseConstructor.formGridEnd();


			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());

//			if (fromAdvanced.equals(SwtConstants.STR_TRUE)){
//				request.setAttribute("fromAdvanced", SwtConstants.STR_TRUE);
//
//				if(systemFlag.equals(SwtConstants.NO)){
//					// If the change scenario maintenance screen comes from the advanced screen, then set the new properties
//					facility = (Facility) getFacility();
//					scenario.setTitle(request.getParameter("scenTitle"));
//					scenario.setCategoryId(scenarioForm.getCategoryId());
//					scenario.setActiveFlag(scenarioForm.getActiveFlag());
//					scenario.setDisplayOrder(scenarioForm.getDisplayOrder());
//					scenario.setStartTime(scenarioForm.getStartTime());
//					scenario.setEndTime(scenarioForm.getEndTime());
//					scenario.setEmailWhenDiff(scenarioForm.getEmailWhenDiff());
//					scenario.setRunEvery(scenarioForm.getRunEvery());
//					scenario.setDescription(request.getParameter("scenDescription"));
//					scenario.setSummaryGrouping(request.getParameter("scenSummaryGrouping"));
//					scenario.setQueryText(request.getParameter("scenBaseQuery"));
//					scenario.setSecHostCol(request.getParameter("scenHostColumn"));
//					scenario.setSecEntityCol(request.getParameter("scenEntityColumn"));
//					scenario.setSecCurrencyCol(request.getParameter("scenCurrencyColumn"));
//					scenario.setAmtThresholdCol(request.getParameter("scenAmountColumn"));
//					scenario.setUseGenericDisplay(request.getParameter("scenGenericDisplay"));
//					facility.setFacilityid(request.getParameter("scenFacilityID"));
//					scenario.setFacilityRefCols(request.getParameter("scenRefcolumns"));
//					scenario.setFacilityParamVals(request.getParameter("scenParamValues"));
//					request.setAttribute("selectedFacilityId",request.getParameter("selectedFacilityId"));
//					scenario.setFacility(facility);
//					setFacility(scenario.getFacility());
//				}else {
//					if(!"".equals(request.getParameter("scenSummaryGrouping")))
//							scenario.setSummaryGrouping(request.getParameter("scenSummaryGrouping"));
//				}
//			}else{
//				/*
//				 * If not we have to build a new form of facility to do not have a problem
//				 * in the advanced screen if the we don't have a facility id of a scenario
//				 */
//				facility = new Facility();
//				setFacility(facility);
//			}
//
//			// set the old values in the oldVal variable that will be used in logging
//			oldVal = new StringBuffer("Scenario-ID=").append(scenario.getId().getScenarioId())
//					.append("^Display-Order=")
//					.append(scenario.getDisplayOrder()).toString();
//			categoryTitle= category.getTitle();
//			categoryTitle=SwtUtil.decreaseStringWidth(SwtConstants.SCENARIO_CATEGORY_MAX_WIDTH,categoryTitle,SwtConstants.VERDANA_STYLE,SwtConstants.VERDANA_12P_SIZE);
//			// Put the required attributes in the request
//			setScenarioMaintenance(scenario);
//			setScenarioCategory(category);
//			request.setAttribute("scenarioMaintenance", scenario);
//			request.setAttribute("selectedSystemFlag",systemFlag);
//			request.setAttribute("methodName", "change");
//			request.setAttribute("scenTitle", request.getParameter("scenTitle"));
//			request.setAttribute("scenDescription", request.getParameter("scenDescription"));
//			request.setAttribute("scenSummaryGrouping", request.getParameter("scenSummaryGrouping"));
//			request.setAttribute("scenBaseQuery", request.getParameter("scenBaseQuery"));
//			request.setAttribute("scenHostColumn", request.getParameter("scenHostColumn"));
//			request.setAttribute("scenEntityColumn", request.getParameter("scenEntityColumn"));
//			request.setAttribute("scenCurrencyColumn", request.getParameter("scenCurrencyColumn"));
//			request.setAttribute("scenAmountColumn",request.getParameter("scenAmountColumn") );
//			request.setAttribute("scenGenericDisplay", request.getParameter("scenGenericDisplay"));
//			request.setAttribute("scenFacilityID", request.getParameter("scenFacilityID"));
//			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
//			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
//			request.setAttribute("scenParamValues", request.getParameter("scenParamValues"));
//			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
//			request.setAttribute("selectedCategoryTitle", categoryTitle);
//			request.setAttribute("oldValue", oldVal);

			log.debug(this.getClass().getName() + " - [change] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.debug(this.getClass().getName() + " - Exception Catched in [change] method : - " + swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [change] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.debug(this.getClass().getName() + " - Exception Catched in [change] method : - " + e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [change] method : - " + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "change", ScenMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
		}

	}

	public String view() throws Exception {
		log.debug(this.getClass().getName() + " - [unspecified] - " + "Return list Action");
		return change();
	}

	/**
	 * Method called when change Button clicked on role screen
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String changeRole() throws Exception {
		/* Method's local variable and class instance declaration */
		ScenarioNotify scenarioNot;
		String hostId;
		String scenarioID;
		String entityId;
		String roleId;
		String oldVal;
		String scenRcdInstance;
		String scenRcdInstFlag;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [changeRole] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to true because we are in the change role part. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			// Retrieve the scenario notification form
			scenarioNot = (ScenarioNotify) getScenarioNotification();

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			scenarioID = request.getParameter("selectedScenarioID");
			entityId = request.getParameter("selectedEntityValue");
			roleId = request.getParameter("selectedRoleValue");
			/*
			 * Pass the Request parameter to set entity, role and scenario List for change
			 * screen to the request attribute
			 */
			putEntityListInReq(request);
			putRoleListInReq(hostId, request);
			putScenarioListInReq(request);


			//get the record_scenario_instance column value from p_scenario table
			scenRcdInstance = scenMaintenanceManager.getScenRcdInstance (scenarioID);
			if("Y".equalsIgnoreCase(scenRcdInstance)) {
				scenRcdInstFlag="";
			}else {
				scenRcdInstFlag="true";
			}
			/* Get the editable data list to the ScenarioNotify bean */
			scenarioNot = scenMaintenanceManager.getRoleAssignmentEditableData(hostId, scenarioID, entityId, roleId);

			oldVal = new StringBuffer("Scenario-ID=").append(scenarioNot.getId().getScenarioId()).append("^Role-ID=")
					.append(scenarioNot.getId().getRoleId()).toString();
			setScenarioNotification(scenarioNot);
			// Put the required attributes in the request
			request.setAttribute("scenRcdInstFlag", scenRcdInstFlag);
			request.setAttribute("scenarioNotification", scenarioNot);
			request.setAttribute("methodName", "changeRole");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			request.setAttribute("oldValue", oldVal);
			request.setAttribute("emailusers", scenarioNot.getEmailusers());

			log.debug(this.getClass().getName() + " - [changeRole] - " + "Exit");
			return getView("changerole");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [changeRole] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [changeRole] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [changeRole] method : - " + e.getMessage());
			log.error(this.getClass().getName() + " - Exception Catched in [changeRole] method : - " + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "changeRole", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}

	}

	/**
	 * Method called when change Button change clicked on category maintenance
	 * screen
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String changeCategory() throws Exception {
		/* Method's local variable and class instance declaration */
		ScenarioCategory scenCategory;
		String categoryId;
		String categorySystemFlag;
		String oldVal;
		String tabNames = null;
		String[] tabNamesArray = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [changeCategory] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to true because we are in the change role part. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			// Retrieve the scenario notification form
			scenCategory = (ScenarioCategory) getScenarioCategory();
			/* Reads the hostId from SwtUtil */
			categoryId = request.getParameter("selectedCategoryId");
			categorySystemFlag = request.getParameter("categorySystemFlag");
			/* Get the editable data list to the ScenarioNotify bean */
			scenCategory = scenMaintenanceManager.getCategoryEditableData(categoryId);
			oldVal = new StringBuffer("Category-ID=").append(scenCategory.getId().getCategoryid())
					.append("^System-Flag=").append(scenCategory.getSystemflag()).toString();
			setScenarioCategory(scenCategory);
			// Put the required attributes in the request
			request.setAttribute("scenarioCategory", scenCategory);
			request.setAttribute("categorySystemFlag", categorySystemFlag);
			request.setAttribute("methodName", "changeCategory");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			request.setAttribute("oldValue", oldVal);

//			tabNames = PropertiesFileLoader.getInstance()
//					.getPropertiesValue(SwtConstants.SCENARIO_CATEGORY_TAB_NAMES);
//			if(!SwtUtil.isEmptyOrNull(tabNames)) {
//				tabNamesArray = tabNames.split("\\s*,\\s*");
//				if(tabNamesArray.length>0) {
//					for(int i = 0 ; i < tabNamesArray.length ; i++) {
//						request.setAttribute("tabName"+(i+1), tabNamesArray[i]);
//					}
//				}
//			}
			request.setAttribute("tabName1",
					PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME));
			request.setAttribute("tabName2",
					PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME));

			log.debug(this.getClass().getName() + " - [changeCategory] - " + "Exit");
			return getView("changecategory");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " - Exception Catched in [changeCategory] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [changeCategory] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [changeCategory] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName() + " - Exception Catched in [changeCategory] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "changeCategory", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}

	}

	/**
	 * Method called when view Button is clicked in alert Messages screen Get the
	 * alert message details to view screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws Exception
	 */
//	public String view()
//			throws Exception {
//		/* Method's Local variable and class instance declaration */
//		// DynaValidatorForm dyForm=null;
//		Scenario scenario=null;
//		ScenarioCategory category = null;
//		String scenarioID=null;
//		String systemFlag=null;
//		String categoryTitle=null;
//		try {
//			log.debug(this.getClass().getName() + " - [view] - " + "Entry");
//			return change()
//
//			/*
//			 * Set the screenFieldsStatus to true because we are in the view part. if screenFieldsStatus
//			 * is true then disable the related fields which contain this attribute (disabled=${screenFieldsStatus})
//			 */
//			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
//
//			//			scenario = (Scenario) getScenarioMaintenance();
//			scenarioID = request.getParameter("selectedScenarioID");
//			/*
//			 * Retrieve the selectedSystemFlag from the request as the facility id can be editable
//			 * only when we have non system scenario
//			 */
//			systemFlag = request.getParameter("selectedSystemFlag");
//
//
//			/* Pass the Request parameter to set CategoryList of Scenario for add
//			 * screen to the request attribute
//			 */
//			putCategoryListInReq(request);
//
//			/* Get the editable data list to the SystemAlertMesages bean */
//			scenario = scenMaintenanceManager
//					.getEditableDataDetailList(scenarioID);
//			if (scenario != null) {
//				category = scenMaintenanceManager
//						.getCategoryEditableData(scenario.getCategoryId());
//				if (category != null && category.getTitle() != null) {
//					categoryTitle = category.getTitle();
//
//					categoryTitle=SwtUtil.decreaseStringWidth(SwtConstants.SCENARIO_CATEGORY_MAX_WIDTH, categoryTitle, SwtConstants.VERDANA_STYLE, SwtConstants.VERDANA_12P_SIZE);
//				}
//				setScenarioMaintenance(scenario);
//				setScenarioCategory(category);
//				setFacility(scenario.getFacility());
//				// Put the required attributes in the request
//				request.setAttribute("scenarioMaintenance", scenario);
//				request.setAttribute("methodName", "view");
//				request.setAttribute(SwtConstants.SAV_BUT_STS,
//						SwtConstants.STR_FALSE);
//				request.setAttribute("selectedSystemFlag", systemFlag);
//				request.setAttribute("selectedCategoryTitle",
//						categoryTitle);
//				log.debug(this.getClass().getName() + " - [view] - " + "Exit");
//				return getView("view");
//			} else {
//				return getView("fail");
//			}
//		} catch (SwtException swtexp) {
//			log.debug(this.getClass().getName()
//					+ " - Exception Catched in [view] method : - "
//					+ swtexp.getMessage());
//
//			log.error(this.getClass().getName()
//					+ " - Exception Catched in [view] method : - "
//					+ swtexp.getMessage());
//			SwtUtil.logException(swtexp, request, "");
//			return getView("fail");
//		} catch (Exception e) {
//			log.debug(this.getClass().getName()
//					+ " - Exception Catched in [view] method : - "
//					+ e.getMessage());
//
//			log.error(this.getClass().getName()
//					+ " - Exception Catched in [view] method : - "
//					+ e.getMessage());
//			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
//					e, "view", ScenMaintenanceAction.class), request, "");
//			return getView("fail");
//		}
//	}

	/**
	 * This method is used to add the new scenario.

	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Method's local variable and class instance declaration */
		Scenario scenario;
		ScenarioCategory category;
		Facility facility;
		String fromAdvanced;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		try {
			log.debug(this.getClass().getName() + " - [add] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to false because we are in the add part. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			/*
			 * Retrieve the fromAdvanced parameter from the request. In fact, if the user
			 * modifies some properties in the advanced screen and then access another time
			 * to the same screen (fromAdvanced=true), then we have to display the last
			 * entered values by the user
			 */
			fromAdvanced = request.getParameter("fromAdvanced");
			if (fromAdvanced.equals(SwtConstants.STR_TRUE)) {
				// If the change scenario maintenance screen comes from the advanced screen,
				// then set the new properties
				scenario = (Scenario) getScenarioMaintenance();
				category = (ScenarioCategory) getScenarioCategory();
				facility = (Facility) getFacility();
				scenario.setFacility(facility);
				scenario.getId().setScenarioId(request.getParameter("selectedScenarioID"));
				scenario.setTitle(request.getParameter("scenTitle"));
				scenario.setDescription(request.getParameter("scenDescription"));
				scenario.setQueryText(request.getParameter("scenBaseQuery"));
				scenario.setQueryText(request.getParameter("scenSummaryGrouping"));
				scenario.setSecHostCol(request.getParameter("scenHostColumn"));
				scenario.setSecEntityCol(request.getParameter("scenEntityColumn"));
				scenario.setSecCurrencyCol(request.getParameter("scenCurrencyColumn"));
				scenario.setAmtThresholdCol(request.getParameter("scenAmountColumn"));
				scenario.setUseGenericDisplay(request.getParameter("scenGenericDisplay"));
				scenario.getFacility().setFacilityid(request.getParameter("scenFacilityID"));
				scenario.setFacilityRefCols(request.getParameter("scenRefcolumns"));
				scenario.setFacilityParamVals(request.getParameter("scenParamValues"));
				request.setAttribute("fromAdvanced", SwtConstants.STR_TRUE);
			} else {
				/*
				 * If not we have to build a new form of scenario, category and facility to do
				 * not have a problem in the advanced screen as we don't have a facility id of a
				 * scenario
				 */
				scenario = new Scenario();
				scenario.setActiveFlag(SwtConstants.YES);
				category = new ScenarioCategory();
				facility = new Facility();
				scenario.setFacility(facility);
			}
			/*
			 * Pass the Request parameter to set categoryList of alert for add screen to the
			 * request attribute
			 */
			// putCategoryListInReq(request);
			// Set the category of the particular scenario to add
			scenario.setCategoryId(category.getId().getCategoryid());
			setScenarioMaintenance(scenario);
			setScenarioCategory(category);
			setFacility(facility);
			// Put the required attributes in the request
			request.setAttribute("scenarioMaintenance", scenario);
			request.setAttribute("methodName", "add");
			request.setAttribute("selectedSystemFlag", SwtConstants.NO);
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			request.setAttribute("scenTitle", request.getParameter("scenTitle"));
			request.setAttribute("scenDescription", request.getParameter("scenDescription"));
			request.setAttribute("scenSummaryGrouping", request.getParameter("scenSummaryGrouping"));
			request.setAttribute("scenBaseQuery", request.getParameter("scenBaseQuery"));
			request.setAttribute("scenHostColumn", request.getParameter("scenHostColumn"));
			request.setAttribute("scenEntityColumn", request.getParameter("scenEntityColumn"));
			request.setAttribute("scenCurrencyColumn", request.getParameter("scenCurrencyColumn"));
			request.setAttribute("scenAmountColumn", request.getParameter("scenAmountColumn"));
			request.setAttribute("scenGenericDisplay", request.getParameter("scenGenericDisplay"));
			request.setAttribute("scenFacilityID", request.getParameter("scenFacilityID"));
			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
			request.setAttribute("scenParamValues", request.getParameter("scenParamValues"));

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("selectedSystemFlag", SwtConstants.NO);
			responseConstructor.createElement(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Category Combo ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection catgoryList = putCategoryListInReq(request);
			Iterator j = catgoryList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("categoryList", lstOptions));
			/***** Summary Grouping Combo ***********/
			lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection summaryGroupingList = putSummaryGroupingListInReq(request);
			j = summaryGroupingList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("summaryGroupingList", lstOptions));
			/***** Facility Combo ***********/

			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection facilityList = putFacilityListForEditInReq(request, false);
			j = facilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("facilityList", lstOptions));

			/** SubGuiHighlight facility combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection guiFacilityList = null;
			guiFacilityList = scenMaintenanceManager.getGuiFacilityList();
			lstOptions.add(new OptionInfo("", "", false));
			j = guiFacilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("guiHighlightFacilityList", lstOptions));

			/** SubEvent facility combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection eventFacilityList = null;
			eventFacilityList = scenMaintenanceManager.getEventFacilityList();
			lstOptions.add(new OptionInfo("", "", false));
			j = eventFacilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				//add below condition to disable future implementation (send email facility)
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("guiEventFacilityList", lstOptions));


			/** event tab email templates combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection templates = scenMaintenanceManager.getEmailTemplates();
			j = templates.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("emailTemplatesList", lstOptions));

			/** Execute when combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("A", "Always", false));
			lstOptions.add(new OptionInfo("E", "Only when one or more prior events fail (result in an error)", false));
			lstOptions.add(new OptionInfo("S", "Only when prior eventsexecute without error", false));

			lstSelect.add(new SelectInfo("executeWhenList", lstOptions));

			/** Api type combo ****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("O", "Oracle", false));
			lstSelect.add(new SelectInfo("apiTypeList", lstOptions));

			/** map from values combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			lstOptions.add(new OptionInfo("Instance Attribute", "Instance Attribute", false));
			lstOptions.add(new OptionInfo("Literal", "Literal", false));
			lstOptions.add(new OptionInfo("Ignore", "Ignore", false));

			lstSelect.add(new SelectInfo("mapFromComboVal", lstOptions));

			/** istance tab other id types combo****/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection types = scenMaintenanceManager.getOtherIdTypes();
			lstOptions.add(new OptionInfo("", "", false));
			j = types.iterator();
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherIdTypeCombo", lstOptions));

			responseConstructor.formSelect(lstSelect);
			/******* GUIHighlight grid ******/
			responseConstructor.formGridStart("guiGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "guiGrid"));

			Collection guiAlertMappingList = null;
			guiAlertMappingList = scenMaintenanceManager.getGuiFacilityGrid();
			// form rows (records)
			responseConstructor.formRowsStart(guiAlertMappingList.size());
			for (Iterator it = guiAlertMappingList.iterator(); it.hasNext();) {
				ScenarioGuiAlertFacility guiAlert = (ScenarioGuiAlertFacility) it.next();

				responseConstructor.formRowStart();
				responseConstructor.createRowElement("guiIdDescripion",
						guiAlert.getId().getGuiFacilityId()+ " - " + guiAlert.getDescription());
				responseConstructor.createRowElement("guiSelect", "N");
				responseConstructor.createRowElement("colorflag", "D");
				//responseConstructor.createRowElement("guiOtherId", "HOST_ID");//WE NEED TO FIX THAT TBX SIDE
				responseConstructor.createRowElement("guiScenInstance", guiAlert.getRequiresScenarioInstance());
				responseConstructor.createRowElement("guiRequiredParams", SwtUtil.isEmptyOrNull(guiAlert.getRequiredParameters())?"":guiAlert.getRequiredParameters());
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* SubEvent email case grid ******/
			responseConstructor.formGridStart("emailGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "emailGrid"));
			responseConstructor.formGridEnd();

			/******* SubGUIHighlight grid ******/
			responseConstructor.formGridStart("subGuiGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "subGuiGrid"));
			responseConstructor.formGridEnd();

			/******* EVent grid ******/
			responseConstructor.formGridStart("eventGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "eventGrid"));
			responseConstructor.formGridEnd();

			/******* SubEVent grid ******/
			responseConstructor.formGridStart("subEventGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "subEventGrid"));
			responseConstructor.formGridEnd();

			/******* General grid ******/
			responseConstructor.formGridStart("generalGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "generalGrid"));
			responseConstructor.formGridEnd();

			/******* define Parameters grid ******/
			responseConstructor.formGridStart("defParamsGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "defParamsGrid"));
			responseConstructor.formRowsStart(11);
			//for (int i = 0; i < 11; i++) {
			responseConstructor.formRowStart();
			responseConstructor.createRowElement("name", "");
			responseConstructor.createRowElement("description", "");
			responseConstructor.formRowEnd();
			//}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* schedule details grid ******/
			responseConstructor.formGridStart("scheduleGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, "scheduleGrid"));
			responseConstructor.formGridEnd();

			/******* roles main grid ******/
			responseConstructor.formGridStart("rolesMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "rolesMainGrid", request));
			responseConstructor.formGridEnd();


			/******* users main grid ******/
			responseConstructor.formGridStart("usersMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "usersMainGrid", request));
			responseConstructor.formGridEnd();


			/******* other email main grid ******/
			responseConstructor.formGridStart("otherEmailMainGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns, "otherEmailMainGrid",request));
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);

			request.setAttribute("data", xmlWriter.getData());
			request.getSession().setAttribute("scenarioMessageFormatsInSession", null);
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [add] method : - " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "add", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * This method is used to add the new Role assignment to existing Scenario.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addRole() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		ScenarioNotify scenarioNot;
		String hostId;

		try {
			log.debug(this.getClass().getName() + " - [addRole] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to false because we are in the add role part. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			scenarioNot = (ScenarioNotify) getScenarioNotification();
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/*
			 * Pass the Request parameter to set entity, role and scenario List for add
			 * screen to the request attribute
			 */
			putEntityListInReq(request);
			putRoleListInReq(hostId, request);
			putScenarioListInReq(request);

			/* Get the editable data list to the ScenarioNotify bean */
			scenarioNot = new ScenarioNotify();

			setScenarioNotification(scenarioNot);
			request.setAttribute("scenarioNotification", scenarioNot);
			request.setAttribute("methodName", "addRole");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [addRole] - " + "Exit");
			return getView("addrole");
		} catch (SwtException swtexp) {
			log.debug(
					this.getClass().getName() + " - Exception Catched in [addRole] method : - " + swtexp.getMessage());

			log.error(
					this.getClass().getName() + " - Exception Catched in [addRole] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [addRole] method : - " + e.getMessage());
			log.error(this.getClass().getName() + " - Exception Catched in [addRole] method : - " + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "addRole", ScenMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
		}

	}

	/**
	 * amend the advanced details of a scenario
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String advanced() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [advanced] - Enter");

			// Pass the request parameter to set the summaryGroupingList and fill it in the
			// related combo box.
			putSummaryGroupingListInReq(request);
			request.setAttribute("openedMethodName", request.getParameter("openedMethodName"));
			log.debug(this.getClass().getName() + "- [advanced] - Exit");
			// The build of the advanced details screen depends on the opened method (add,
			// change or view)
			if (request.getParameter("openedMethodName").equals("add"))
				return advancedFromAdd();
			else
				return advancedFromChangeAndView();
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [advanced] method : - " + exp.getMessage());
			exp.getStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "advanced", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}

	}

	/**
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String advancedFromAdd() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's Local variable and class instance declaration */
		Scenario scenario;
		Facility facility;
		boolean genericDisplaySelected;
		String fromAdvanced;
		try {
			log.debug(this.getClass().getName() + "- [advancedFromAdd] - Enter");

			/*
			 * Set the screenFieldsStatus to false as we are in the add part in advanced
			 * screen. if screenFieldsStatus is true then disable the related fields which
			 * contain this attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			scenario = (Scenario) getScenarioMaintenance();
			facility = new Facility();
			scenario.setFacility(facility);

			// Pass the request parameter to set the facilityList and fill it in the related
			// combo box.
			genericDisplaySelected = request.getParameter("scenGenericDisplay").equals(SwtConstants.YES);
			putFacilityListForEditInReq(request, genericDisplaySelected);

			setScenarioMaintenance(scenario);
			request.setAttribute("scenario", scenario);
			fromAdvanced = request.getParameter("fromAdvanced");
			// Put the required attributes in the request
			request.setAttribute("scenarioID", request.getParameter("selectedScenarioID"));
			request.setAttribute("title", request.getParameter("title"));
			request.setAttribute("description", request.getParameter("description"));
			request.setAttribute("methodName", "add");
			request.setAttribute("scenSummaryGrouping", request.getParameter("scenSummaryGrouping"));
			request.setAttribute("scenBaseQuery", request.getParameter("scenBaseQuery"));
			request.setAttribute("scenHostColumn", request.getParameter("scenHostColumn"));
			request.setAttribute("scenEntityColumn", request.getParameter("scenEntityColumn"));
			request.setAttribute("scenCurrencyColumn", request.getParameter("scenCurrencyColumn"));
			request.setAttribute("scenAmountColumn", request.getParameter("scenAmountColumn"));
			request.setAttribute("scenGenericDisplay", request.getParameter("scenGenericDisplay"));
			request.setAttribute("scenFacilityID", request.getParameter("scenFacilityID"));
			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
			request.setAttribute("scenParamValues", request.getParameter("scenParamValues"));
			request.setAttribute("fromAdvanced", fromAdvanced);

			log.debug(this.getClass().getName() + " - [advancedFromAdd] - " + "Exit");
			return getView("advanced");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [advancedFromAdd] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "advancedFromAdd", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String advancedFromChangeAndView() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String scenarioID;
		String systemFlag = null;
		String baseQuery = null;
		/* Method's Local variable and class instance declaration */
		Scenario scenario;
		Facility facility;
		String openedMethodName;
		boolean genericDisplaySelected;
		String fromAdvanced = null;

		try {
			log.debug(this.getClass().getName() + "- [advancedFromChangeAndView] - Enter");

			/*
			 * Set the screenFieldsStatus to true/false according to view/change methods. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			if (request.getParameter("openedMethodName").equals("view"))
				request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			else
				request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			/*
			 * Retrieve the selectedSystemFlag from the request as the facility id can be
			 * editable only when we have non system scenario
			 */
			systemFlag = request.getParameter("selectedSystemFlag");
			// get fromAdvanced Flag from request
			fromAdvanced = request.getParameter("fromAdvanced");

			// Retrieve the opened method name view or change method because we have the
			// facility list which depends on method
			openedMethodName = request.getParameter("openedMethodName");

			scenarioID = request.getParameter("selectedScenarioID");
			scenario = (Scenario) getScenarioMaintenance();
			facility = (Facility) getFacility();

			if (facility.getFacilityid() == null) {
				// set the scenario details from the data base
				scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioID);
				facility = scenario.getFacility();
				// if the facility is null in the database we have to build a new facility
				if (facility == null) {
					facility = new Facility();
					/*
					 * set the facilityID to the 'scenFacilityID' parameter retrieved from
					 * change/view screen in order to save temporarily the selected scenario
					 */
					if (!request.getParameter("scenFacilityID").equals(""))
						facility.setFacilityid(request.getParameter("scenFacilityID"));
				} else {
					// it occurs when the scenario has a facilityId in the data base but the user to
					// remove it
					if (request.getParameter("selectedFacilityId").equals(SwtConstants.FACILITY_NONE)) {
						facility = new Facility();
						request.setAttribute("selectedFacilityId", "");
					}
				}
			}
			// Set use generic display property. It depends on the check box values
			if (scenario.getUseGenericDisplay() == null || (!scenario.getUseGenericDisplay().equals(SwtConstants.YES)))
				scenario.setUseGenericDisplay(SwtConstants.NO);

			// Pass the request parameter to set the facilityList and fill it in the related
			// combo box.
			// It depends on the system flag of the selected scenario.
			if (systemFlag.equals(SwtConstants.YES) && openedMethodName.equals("change")
					|| openedMethodName.equals("view")) {
				// send all the list of facilities
				putFacilityListInReq(request);
			} else {
				// send only the facilities which have the selectable user <> 'N'

				if (SwtUtil.isEmptyOrNull(request.getParameter("scenGenericDisplay"))) {

					if (!SwtUtil.isEmptyOrNull(scenario.getUseGenericDisplay())) {
						genericDisplaySelected = scenario.getUseGenericDisplay().equals(SwtConstants.YES) ? true
								: false;
					} else
						genericDisplaySelected = false;
				} else {
					genericDisplaySelected = request.getParameter("scenGenericDisplay").equals(SwtConstants.YES) ? true
							: false;
				}
				putFacilityListForEditInReq(request, genericDisplaySelected);
			}

			setScenarioMaintenance(scenario);
			setFacility(facility);
			request.setAttribute("fromAdvanced", fromAdvanced);

			// Put the required attributes in the request
			request.setAttribute("scenario", scenario);
			request.setAttribute("facility", facility);
			if (request.getParameter("openedMethodName").equals("change"))
				request.setAttribute("selectedSystemFlag", systemFlag);
			baseQuery = request.getParameter("scenBaseQuery");
			request.setAttribute("methodName", request.getParameter("openedMethodName"));
			request.setAttribute("scenarioID", request.getParameter("selectedScenarioID"));
			request.setAttribute("title", request.getParameter("title"));
			request.setAttribute("description", request.getParameter("description"));
			request.setAttribute("scenSummaryGrouping", request.getParameter("scenSummaryGrouping"));
			request.setAttribute("scenBaseQuery", baseQuery);
			request.setAttribute("scenHostColumn", request.getParameter("scenHostColumn"));
			request.setAttribute("scenEntityColumn", request.getParameter("scenEntityColumn"));
			request.setAttribute("scenCurrencyColumn", request.getParameter("scenCurrencyColumn"));
			request.setAttribute("scenAmountColumn", request.getParameter("scenAmountColumn"));
			request.setAttribute("scenGenericDisplay", request.getParameter("scenGenericDisplay"));
			request.setAttribute("scenFacilityID", request.getParameter("scenFacilityID"));
			request.setAttribute("scenRefcolumns", request.getParameter("scenRefcolumns"));
			request.setAttribute("scenParamValues", request.getParameter("scenParamValues"));
			log.debug(this.getClass().getName() + " - [advancedFromChangeAndView] - " + "Exit");
			return getView("advanced");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [advancedFromChangeAndView] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "advancedFromChangeAndView",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 *
	 * Method to validate the scenario details that are given in add scenario
	 * maintenance screen and returns an error message if the validation fails or
	 * save the scenario details in data base
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		// DynaValidatorForm dyForm = null;
		Scenario scenario = null;
		ScenarioCategory category = null;
		Facility facility;
		String fromAdvanced;
		ActionMessages errors = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String saveOrUpdate = null;
		responseHandler = new ResponseHandler();
		responseConstructor = new SwtResponseConstructor();
		xmlWriter = responseConstructor.getXMLWriter();
		String operationsList = null;
		String scheduleXml=null;
		ScenarioGuiAlertMapping scenarioGuiAlertMapping = null;
		ArrayList<ScenarioGuiAlertMapping> listGuiAdd = new ArrayList<ScenarioGuiAlertMapping>();
		ArrayList<ScenarioGuiAlertMapping> listGuiUpdate = new ArrayList<ScenarioGuiAlertMapping>();
		ArrayList<ScenarioGuiAlertMapping> listGuiDelete = new ArrayList<ScenarioGuiAlertMapping>();

		ScenarioEventMapping scenarioEventMapping = null;
		ArrayList<ScenarioEventMapping> listEventAdd = new ArrayList<ScenarioEventMapping>();
		ArrayList<ScenarioEventMapping> listEventUpdate = new ArrayList<ScenarioEventMapping>();
		ArrayList<ScenarioEventMapping> listEventDelete = new ArrayList<ScenarioEventMapping>();
		// Variable Declaration for messageFieldDetails
		Collection messageFieldDetails = null;
		ScenarioMessageFormats msgFormat = null;
		String selectedScenarioID = null;
		String hostId;
		String criticalGuiHighlight=null;

		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");
			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			scenario = (Scenario) getScenarioMaintenance();
			category = (ScenarioCategory) getScenarioCategory();
			scenarioGuiAlertMapping = new ScenarioGuiAlertMapping();
			saveOrUpdate = request.getParameter("saveOrUpdate");
			criticalGuiHighlight= request.getParameter("criticalGuiHighlight");
			// Set active flag and system flag properties. They depend on the check box
			// values
			if (scenario.getActiveFlag() == null || (!scenario.getActiveFlag().equals(SwtConstants.YES)))
				scenario.setActiveFlag(SwtConstants.NO);
			if (scenario.getSystemFlag() == null || (!scenario.getSystemFlag().equals(SwtConstants.YES)))
				scenario.setSystemFlag(SwtConstants.NO);

			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");

			// Get the scenario category details from the data base and set it to the
			// scenario which to be saved
			category = scenMaintenanceManager.getcategoryDetails(category.getId().getCategoryid());
			scenario.setCategoryId(category.getId().getCategoryid());
			operationsList = request.getParameter("operationsList");

			//delete facility mapping if exists before inserting new scenario facilities
			selectedScenarioID = request.getParameter("selectedScenarioID");
			scenMaintenanceManager.deleteFacilitiesMapping(selectedScenarioID);

			//delete all scenario events from P_SCENARIO_EVENT_MAPPING
//			scenMaintenanceManager.deleteEventMapping(selectedScenarioID);


			Collection eventMappingList = scenMaintenanceManager.getEventMapping(scenario.getId().getScenarioId());
			ArrayList<String> eventInfo = new ArrayList<String>();
			for (Iterator<ScenarioEventMapping> it = eventMappingList.iterator(); it.hasNext();) {
				ScenarioEventMapping eventMapping = (ScenarioEventMapping) it.next();
				eventInfo.add(eventMapping.getScenarioId()+","+eventMapping.getEventFacilityId()+","+eventMapping.getMapKey());
			}

			JSONArray chartsJSONArray = new JSONArray(operationsList);
			for (int i = 0; i < chartsJSONArray.length(); i++) {
				if ("GuiAlertMapping".equals(chartsJSONArray.getJSONObject(i).getString("TABLE"))) {
					scenarioGuiAlertMapping = new ScenarioGuiAlertMapping();
					scenarioGuiAlertMapping.getId().setScenarioId(chartsJSONArray.getJSONObject(i).getString("SCENARIO_ID"));
					scenarioGuiAlertMapping.getId().setGuiFacilityId(chartsJSONArray.getJSONObject(i).getString("GUI_FACILITY_ID"));
					if ("I".equals(chartsJSONArray.getJSONObject(i).getString("OPERATION"))) {
						listGuiAdd.add(scenarioGuiAlertMapping);
					} else if ("U".equals(chartsJSONArray.getJSONObject(i).getString("OPERATION"))) {
						listGuiUpdate.add(scenarioGuiAlertMapping);
					} else if ("D".equals(chartsJSONArray.getJSONObject(i).getString("OPERATION"))) {
						listGuiDelete.add(scenarioGuiAlertMapping);
					}

				} else if ("EventMapping".equals(chartsJSONArray.getJSONObject(i).getString("TABLE"))) {
					String scenarioId=chartsJSONArray.getJSONObject(i).getString("SCENARIO_ID");
					String eventFacilityId=chartsJSONArray.getJSONObject(i).getString("EVENT_FACILITY_ID");
					scenarioEventMapping = new ScenarioEventMapping();
					scenarioEventMapping.setScenarioId(scenarioId);
					scenarioEventMapping.setEventFacilityId(eventFacilityId);
					scenarioEventMapping.setUserDescription(chartsJSONArray.getJSONObject(i).getString("EVENT_FACILITY_DESC"));
					scenarioEventMapping.setParametersXml(chartsJSONArray.getJSONObject(i).getString("PARAMETERS_XML"));
					scenarioEventMapping.setOrdinal(chartsJSONArray.getJSONObject(i).getInt("ORDINAL"));
					scenarioEventMapping.setRepeatOnReraise(chartsJSONArray.getJSONObject(i).getString("REPEAT_ON_RERAISE"));
					scenarioEventMapping.setExecuteWhen(chartsJSONArray.getJSONObject(i).getString("EXECUTE_WHEN"));

					if ("D".equals(chartsJSONArray.getJSONObject(i).getString("OPERATION"))) {
						Long mapKey= Long.parseLong(chartsJSONArray.getJSONObject(i).getString("MAP_KEY"));
						//to be checked
						scenarioEventMapping.setMapKey(mapKey);
						listEventDelete.add(scenarioEventMapping);
					}
					else if(chartsJSONArray.getJSONObject(i).has("MAP_KEY") && !SwtUtil.isEmptyOrNull(chartsJSONArray.getJSONObject(i).getString("MAP_KEY"))) {
						Long mapKey= Long.parseLong(chartsJSONArray.getJSONObject(i).getString("MAP_KEY"));
						String scenId= chartsJSONArray.getJSONObject(i).getString("SCENARIO_ID");
						String facilityId= chartsJSONArray.getJSONObject(i).getString("EVENT_FACILITY_ID");
						//to be checked
						scenarioEventMapping.setMapKey(mapKey);
						listEventUpdate.add(scenarioEventMapping);
						eventInfo.remove(eventInfo.indexOf(scenId+","+facilityId+","+mapKey));
					}else {
						listEventAdd.add(scenarioEventMapping);
					}

				}

			}

			for (int j = 0; j < eventInfo.size(); j++) {
				ScenarioEventMapping todelete = new ScenarioEventMapping();
				todelete.setScenarioId(eventInfo.get(j).split(",")[0]);
				todelete.setEventFacilityId(eventInfo.get(j).split(",")[1]);
				todelete.setMapKey(Long.parseLong(eventInfo.get(j).split(",")[2]));
				listEventDelete.add(todelete);
			}
			/*
			 * Retrieve the fromAdvanced parameter from the request. In fact, if the user
			 * modifies some properties in the advanced screen and then access another time
			 * to the same screen (fromAdvanced=true), then we have to display the last
			 * entered values by the user
			 */
			fromAdvanced = request.getParameter("fromAdvanced");
			if (fromAdvanced.equals(SwtConstants.STR_TRUE)) {
				// The scenario will be saved with the properties filled the advanced details
				// screen
				facility = (Facility) getFacility();
				scenario.setSummaryGrouping(request.getParameter("scenSummaryGrouping"));
				scenario.setQueryText(SwtUtil.decode64(request.getParameter("scenBaseQuery")));
				scenario.setInstanceUniqueExpression((SwtUtil.decode64(request.getParameter("scenarioMaintenance.instanceUniqueExpression"))));
				scenario.setScenarioResolutionQueryText(SwtUtil.decode64(request.getParameter("scenarioMaintenance.scenarioResolutionQueryText")));
				scenario.setSecHostCol(request.getParameter("scenHostColumn"));
				scenario.setSecEntityCol(request.getParameter("scenEntityColumn"));
				scenario.setSecCurrencyCol(request.getParameter("scenCurrencyColumn"));
				scenario.setAmtThresholdCol(request.getParameter("scenAmountColumn"));
				scenario.setUseGenericDisplay(request.getParameter("scenGenericDisplay"));
				facility.setFacilityid(request.getParameter("scenFacilityID"));
				scenario.setFacilityRefCols(request.getParameter("scenRefcolumns"));
				scenario.setResolutionRefCols(request.getParameter("resolutionRefcolumns"));
				scenario.setAlertInstanceColumn(request.getParameter("scenAlertInstcolumns"));
				scenario.setFacilityParamVals(request.getParameter("scenParamValues"));
				scenario.setFacility(facility);
				setFacility(scenario.getFacility());
			} else {
				// As we don't have entered to the advanced details screen, we have to make a
				// new facility to the scenario
				facility = new Facility();
				setFacility(facility);
			}
			//general table
			scenario.setScheduleParametersXml(request.getParameter("requiredParamsXml"));
			scenario.setApiRequiredCols(request.getParameter("apiRequiredCols"));
			scenario.setGenerationBasis(request.getParameter("genBasis"));
			//GuiHighlight Tab
			scenario.setCriticalGuiHighlight(criticalGuiHighlight);
			/*
			 * Pass the Request parameter to set categoryList of alert for add screen to the
			 * request attribute
			 */
			putCategoryListInReq(request);
			HashMap<ScenarioMessageFormats, Collection> scenarioMessageFormatsInSession = (HashMap<ScenarioMessageFormats, Collection>) request.getSession().getAttribute("scenarioMessageFormatsInSession");
			MessageFormatsManager messageFormatsManager = (MessageFormatsManager) (SwtUtil
					.getBean("messageFormatsManager"));
			// Save the modified values in the data base
			if ("save".equals(saveOrUpdate)) {
				QueryResult queryResult = null;
				ColumnMetadata metadata;
				String listOfcolumnsLastExecution = null;
				String listOftypesLastExecution = null;
				StringBuilder colsBuilder = new StringBuilder();
				StringBuilder typesBuilder = new StringBuilder();
				GenericDisplayMonitorManager genericDisplayMonitorManager = (GenericDisplayMonitorManager) SwtUtil
						.getBean("genericDisplayMonitorManager");
				if(!SwtUtil.isEmptyOrNull(scenario.getQueryText())) {
					String replalcedBaseQuery = scenario.getQueryText().replaceAll("P\\{.*?}", "''");
					if (replalcedBaseQuery != null)
						queryResult = genericDisplayMonitorManager.getGenericDisplayData(replalcedBaseQuery);

				}
				if (queryResult != null) {
					for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
						metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
						colsBuilder.append('"'+metadata.getColumnLabel()+'"'+",");
						typesBuilder.append(metadata.getColumnTypeName() +",");
					}
					listOfcolumnsLastExecution = colsBuilder.toString();
					listOftypesLastExecution = typesBuilder.toString();
					if(listOfcolumnsLastExecution.length()>0 && listOftypesLastExecution.length()>0) {
						listOfcolumnsLastExecution = listOfcolumnsLastExecution.substring(0, listOfcolumnsLastExecution.length()-1)
								+ "#$#" + listOftypesLastExecution.substring(0, listOftypesLastExecution.length()-1);
						scenario.setQueryExecutionListColumns(listOfcolumnsLastExecution);
					}
				}

				scenMaintenanceManager.saveScenarioDetails(scenario);
				messageFieldDetails = null;
				msgFormat = null;
				//if(MapUtils.isNotEmpty(scenarioMessageFormatsInSession)) {
				if (scenarioMessageFormatsInSession != null && !scenarioMessageFormatsInSession.isEmpty()) {

					for (Entry<ScenarioMessageFormats, Collection> entry : scenarioMessageFormatsInSession.entrySet()) {
						msgFormat = entry.getKey();
//						msgFormat.getId().setScenarioId(scenario.getId().getScenarioId());
						messageFieldDetails = entry.getValue();
						Iterator it = messageFieldDetails.iterator();
						while(it.hasNext()) {
							ScenarioMessageFields scenmsgfield = (ScenarioMessageFields) it.next();
//								scenmsgfield.getId().setScenarioId(scenario.getId().getScenarioId());
						}

						messageFormatsManager.saveScenarioMsgFormatDetails(msgFormat, messageFieldDetails);
					}
				}


			}if ("update".equals(saveOrUpdate)) {

				Scenario oldScenario = scenMaintenanceManager.getEditableDataDetailList(scenario.getId().getScenarioId());

				if(!SwtUtil.isEmptyOrNull(oldScenario.getQueryText()) && !oldScenario.getQueryText().equalsIgnoreCase(scenario.getQueryText())){
					QueryResult queryResult = null;
					ColumnMetadata metadata;
					String listOfcolumnsLastExecution = null;
					String listOftypesLastExecution = null;
					StringBuilder colsBuilder = new StringBuilder();
					StringBuilder typesBuilder = new StringBuilder();
					GenericDisplayMonitorManager genericDisplayMonitorManager = (GenericDisplayMonitorManager) SwtUtil
							.getBean("genericDisplayMonitorManager");
					if(!SwtUtil.isEmptyOrNull(scenario.getQueryText())) {
						String replalcedBaseQuery = scenario.getQueryText().replaceAll("P\\{.*?}", "''");
						if (replalcedBaseQuery != null)
							queryResult = genericDisplayMonitorManager.getGenericDisplayData(replalcedBaseQuery);

					}
					if (queryResult != null) {
						for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
							metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
							colsBuilder.append('"'+metadata.getColumnLabel()+'"'+",");
							typesBuilder.append(metadata.getColumnTypeName()+",");
						}
						listOfcolumnsLastExecution = colsBuilder.toString();
						listOftypesLastExecution = typesBuilder.toString();
						if(listOfcolumnsLastExecution.length()>0 && listOftypesLastExecution.length()>0) {
							listOfcolumnsLastExecution = listOfcolumnsLastExecution.substring(0, listOfcolumnsLastExecution.length()-1)
									+ "#$#" + listOftypesLastExecution.substring(0, listOftypesLastExecution.length()-1);
							scenario.setQueryExecutionListColumns(listOfcolumnsLastExecution);
						}
					}
				}else {
					scenario.setQueryExecutionListColumns(oldScenario.getQueryExecutionListColumns());
				}



				scenMaintenanceManager.updateScenarioDetail(scenario);
				//delete old scenario msg format and save the new ones in case we add new formats from msg formats screen
				//if(MapUtils.isNotEmpty(scenarioMessageFormatsInSession)) {
				if (scenarioMessageFormatsInSession != null && !scenarioMessageFormatsInSession.isEmpty()) {
//				messageFormatsManager.deleteScenMsgFormatById(hostId, scenario.getId().getScenarioId());
					messageFieldDetails = null;
					msgFormat = null;

					for (Entry<ScenarioMessageFormats, Collection> entry : scenarioMessageFormatsInSession.entrySet()) {
						msgFormat = entry.getKey();
//						msgFormat.getId().setScenarioId(scenario.getId().getScenarioId());
						messageFieldDetails = entry.getValue();
						Iterator it = messageFieldDetails.iterator();
						while(it.hasNext()) {
							ScenarioMessageFields scenmsgfield = (ScenarioMessageFields) it.next();
//								scenmsgfield.getId().setScenarioId(scenario.getId().getScenarioId());
						}
						messageFormatsManager.saveScenarioMsgFormatDetails(msgFormat, messageFieldDetails);
					}
				}
			}
			scenMaintenanceManager.crudGuiHighlight(listGuiAdd, listGuiUpdate, listGuiDelete);
			scenMaintenanceManager.crudEventMapping(listEventAdd, listEventUpdate, listEventDelete);
			setScenarioMaintenance(scenario);
			setScenarioCategory(category);
			// Put the required attributes in the request
			request.setAttribute("scenarioMaintenance", scenario);
			request.setAttribute("selectedSystemFlag", scenario.getSystemFlag());
			request.setAttribute("methodName", "change");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			//save schedule parameters in P_SCENARIO_SCHEDULE table
			scheduleXml=request.getParameter("scheduleParams");
			saveScheduleData(scenario,request,scheduleXml);
			request.getSession().setAttribute("scenarioMessageFormatsInSession",null);

			log.debug(this.getClass().getName() + " - [save] - " + "Exit");

		} catch (SwtException swtexp) {
			request.setAttribute("methodName", "add");
			/*
			 * Pass the Request parameter to set categoryList of alert for add screen to the
			 * request attribute
			 */
			putCategoryListInReq(request);
			//to be checked
			/*if (dyForm != null) {
				setScenarioMaintenance(scenario);
				setScenarioCategory(category);
			}*/
			// Put the required attributes in the request
			request.setAttribute("scenarioMaintenance", scenario);
			if (scenario != null) {
				request.setAttribute("selectedSystemFlag", scenario.getSystemFlag());
			}
			request.setAttribute("methodName", "add");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
//				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
//				if (swtexp.getErrorCode().equals(
//						"errors.DataIntegrityViolationExceptioninAdd")) {
//					if (errors != null) {
//						errors.add("", new ActionMessage(swtexp.getErrorCode()));
//					}
//
//					saveErrors(request, errors);
//				} else {
//					log.error(this.getClass().getName()
//							+ " - Exception Catched in [save] method swt : - "
//							+ swtexp.getMessage());
//					saveErrors(request, SwtUtil.logException(swtexp, request, ""));
//				}

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), swtexp.getErrorCode());
			// return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [save] method : - " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "save", ScenMaintenanceAction.class), request,
					"");
			// return getView("fail");
		} finally {
		}
		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}

	/**
	 * Method to validate the Notify Scenario details that are given in add Scenario
	 * Role Notifications screen and returns an error message if the validation
	 * fails or save the Role Notifications details in database
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveRole() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		String hostId = null;
		// DynaValidatorForm dyForm = null;
		ScenarioNotify scenarioNotif = null;
		// declared to hold the errors
		ActionMessages errors = null;
		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [saveRole] - " + "Entry");
			scenarioNotif = (ScenarioNotify) getScenarioNotification();
			// Set access required, deliver popups, flash icon and send mail properties.
			// They depend on the check box values
			if (scenarioNotif.getAccessrequired() == null
					|| (!scenarioNotif.getAccessrequired().equals(SwtConstants.YES)))
				scenarioNotif.setAccessrequired(SwtConstants.NO);
			if (scenarioNotif.getDeliverpopups() == null
					|| (!scenarioNotif.getDeliverpopups().equals(SwtConstants.YES)))
				scenarioNotif.setDeliverpopups(SwtConstants.NO);
			if (scenarioNotif.getFlashicon() == null || (!scenarioNotif.getFlashicon().equals(SwtConstants.YES)))
				scenarioNotif.setFlashicon(SwtConstants.NO);
			if (scenarioNotif.getSendemail() == null || (!scenarioNotif.getSendemail().equals(SwtConstants.YES)))
				scenarioNotif.setSendemail(SwtConstants.NO);
			if (scenarioNotif.getFullinstanceaccess() == null
					|| (!scenarioNotif.getFullinstanceaccess().equals(SwtConstants.YES)))
				scenarioNotif.setFullinstanceaccess(SwtConstants.NO);

			String emailUsers = request.getParameter("emailusers");
			scenarioNotif.setEmailusers(emailUsers);
			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			scenarioNotif.getId().setHostId(hostId);
			scenMaintenanceManager.saveScenarioNotificationDetails(scenarioNotif);

			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");
			/*
			 * Pass the Request parameter to set entity, role and scenario List for add
			 * screen to the request attribute
			 */
			putEntityListInReq(request);
			putRoleListInReq(hostId, request);
			putScenarioListInReq(request);

			setScenarioNotification(scenarioNotif);
			// Put the required attributes in the request
			request.setAttribute("scenarioNotification", scenarioNotif);
			request.setAttribute("methodName", "addRole");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveRole] - " + "Exit");
			return getView("addrole");
		} catch (SwtException swtexp) {

			request.setAttribute("methodName", "addRole");
			/*
			 * Pass the Request parameter to set entity, role and scenario List for add
			 * screen to the request attribute
			 */
			putEntityListInReq(request);
			putRoleListInReq(hostId, request);
			putScenarioListInReq(request);
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals("errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [saveRole] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			return getView("addrole");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [saveRole] method : - " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "updsaveRoleate", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * Method to validate the Scenario category details that are given in add
	 * Scenario Category screen and returns an error message if the validation fails
	 * or save the Category details in database
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveCategory() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		// DynaValidatorForm dyForm = null;
		ScenarioCategory scenarioCat = null;
		ActionMessages errors = null;
		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [saveCategory] - " + "Entry");
			scenarioCat = (ScenarioCategory) getScenarioCategory();

			// Set System flag value it depend on the check box value
			if (scenarioCat.getSystemflag() == null || (!scenarioCat.getSystemflag().equals(SwtConstants.YES)))
				scenarioCat.setSystemflag(SwtConstants.NO);

			scenMaintenanceManager.saveScenarioCategoryDetails(scenarioCat);
			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");
			setScenarioCategory(scenarioCat);
			// Put the required attributes in the request
			request.setAttribute("scenarioCategory", scenarioCat);
			request.setAttribute("methodName", "saveCategory");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveCategory] - " + "Exit");
			return getView("changecategory");
		} catch (SwtException swtexp) {

			request.setAttribute("methodName", "addCategory");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals("errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName() + " - Exception Catched in [saveCategory] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			return getView("addcategory");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [saveRole] method : - " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "updsaveRoleate", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * Returns (through HttpServletResponse) a string which contains the required
	 * values of a facility specified by its Id. It is used when the user changes
	 * the facility Id in the scenario advanced details screen. The string format
	 * is: 'Reference table':'Reference columns':'Reference parameters'
	 * @return
	 * @throws SwtException
	 */
	public String getFacilityIdProperties() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		StringBuffer facilityProperties = new StringBuffer();
		Facility facility;
		String facilityId = "";
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug(this.getClass().getName() + "- [getFacilityIdProperties] - Enter");
			facilityId = request.getParameter("facilityId");
			facility = scenMaintenanceManager.getFacilityDetails(facilityId);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			if (facility != null) {
				// Concatenate the table, columns and parameters references values
				if (facility.getReftable() != null)
					facilityProperties.append(facility.getReftable());
				facilityProperties.append(SwtConstants.SEPARATOR_FACILITIES);
				facilityProperties.append(facility.getRefcolumns());
				facilityProperties.append(SwtConstants.SEPARATOR_FACILITIES);
				facilityProperties.append(facility.getRefparam());

				responseConstructor.createElement("facilityProperties", facilityProperties.toString());

			} else {
				responseConstructor.createElement("facilityProperties",
						SwtConstants.SEPARATOR_FACILITIES + SwtConstants.SEPARATOR_FACILITIES);
			}
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
			// return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getFacilityIdProperties] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getFacilityIdProperties",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * Returns (through HttpServletResponse) a boolean set to true when category has
	 * related scenario specified by its Id. It is used when we want to delete a
	 * category from category maintenance screen.
	 *
	 * @return
	 * @throws SwtException
	 */
	public String categoryHasChildren() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		boolean hasChildren;
		String categoryId = "";

		try {
			log.debug(this.getClass().getName() + "- [categoryHasChildren] - Enter");
			categoryId = request.getParameter("categoryId");
			hasChildren = scenMaintenanceManager.categoryHasChildren(categoryId);
			response.getWriter().print(hasChildren);
			log.debug(this.getClass().getName() + " - [categoryHasChildren] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [categoryHasChildren] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "categoryHasChildren",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * Returns (through HttpServletResponse) a string which contains the entity
	 * column property of a senario
	 * @return
	 * @throws SwtException
	 */
	public String getScenarioIdProperties() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		String scenarioProperties = "";
		Scenario scenario;
		String scenarioId = "";

		try {
			log.debug(this.getClass().getName() + "- [getScenarioIdProperties] - Enter");
			scenarioId = request.getParameter("scenarioId");
			scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioId);
			// Concatenate the table, columns and parameters references values
			scenarioProperties = scenario.getSecEntityCol();
			// set the access to response
			response.getWriter().print(scenarioProperties);
			log.debug(this.getClass().getName() + " - [getScenarioIdProperties] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getScenarioIdProperties] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getScenarioIdProperties",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 *
	 * Method to validate the scenario details that are given in add currency group
	 * screen and returns an error message if the validation fails or update the
	 * scenario details in data base
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		// DynaValidatorForm dyForm = null;
		Scenario scenario = null;
		ScenarioCategory category = null;
		Facility facility;
		String fromAdvanced = null;
		String scenarioID;
		ResponseHandler responseHandler = new ResponseHandler();
		SwtResponseConstructor responseConstructor = new SwtResponseConstructor();
		SwtXMLWriter xmlWriter = responseConstructor.getXMLWriter();
		try {
			log.debug(this.getClass().getName() + " - [update] - " + "Entry");
			scenario = (Scenario) getScenarioMaintenance();
			category = (ScenarioCategory) getScenarioCategory();
			facility = (Facility) getFacility();
			scenario.setFacility(facility);

			// Retrieve the fromAdvanced parameter from the request
			fromAdvanced = request.getParameter("fromAdvanced");
			if (fromAdvanced.equals(SwtConstants.STR_TRUE)) {
				// Set the scenario details that will be updated
				scenario.setQueryText(SwtUtil.decode64(request.getParameter("scenBaseQuery")));
				scenario.setSummaryGrouping(request.getParameter("scenSummaryGrouping"));
				scenario.setSecHostCol(request.getParameter("scenHostColumn"));
				scenario.setSecEntityCol(request.getParameter("scenEntityColumn"));
				scenario.setSecCurrencyCol(request.getParameter("scenCurrencyColumn"));
				scenario.setAmtThresholdCol(request.getParameter("scenAmountColumn"));
				scenario.setUseGenericDisplay(request.getParameter("scenGenericDisplay"));
				scenario.getFacility().setFacilityid(request.getParameter("scenFacilityID"));
				scenario.setFacilityRefCols(request.getParameter("scenRefcolumns"));
				scenario.setFacilityParamVals(request.getParameter("scenParamValues"));
			} else {
				scenarioID = request.getParameter("selectedScenarioID");
				scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioID);
				Scenario scenarioFromForm = (Scenario) getScenarioMaintenance();
				scenario.setTitle(scenarioFromForm.getTitle());
				scenario.setDescription(scenarioFromForm.getDescription());
				scenario.setActiveFlag(scenarioFromForm.getActiveFlag());
				scenario.setCategoryId(scenarioFromForm.getCategoryId());
				scenario.setDisplayOrder(scenarioFromForm.getDisplayOrder());
				scenario.setRunEvery(scenarioFromForm.getRunEvery());
				scenario.setStartTime(scenarioFromForm.getStartTime());
				scenario.setEndTime(scenarioFromForm.getEndTime());
				scenario.setEmailWhenDiff(scenarioFromForm.getEmailWhenDiff());
			}
			setScenarioMaintenance(scenario);
			// Set Active and system flags properties. They depend on the check box values
			if (scenario.getActiveFlag() == null || (!scenario.getActiveFlag().equals(SwtConstants.YES)))
				scenario.setActiveFlag(SwtConstants.NO);
			if (scenario.getSystemFlag() == null || (!scenario.getSystemFlag().equals(SwtConstants.YES)))
				scenario.setSystemFlag(SwtConstants.NO);

			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + " - [update] - " + "Exit");

			category = scenMaintenanceManager.getcategoryDetails(category.getId().getCategoryid());
			scenario.setCategoryId(category.getId().getCategoryid());

			// update the scenario details in the data base
			scenMaintenanceManager.updateScenarioDetail(scenario);
			/*
			 * Pass the Request parameter to set categoryList of alert for screen to the
			 * request attribute
			 */
			putCategoryListInReq(request);
			setScenarioMaintenance(scenario);
			setScenarioCategory(category);
			// Put the required attributes in the request
			request.setAttribute("scenarioMaintenance", scenario);
			request.setAttribute("selectedSystemFlag", scenario.getSystemFlag());
			request.setAttribute("methodName", "change");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [update] method swt : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "update");
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), swtexp.getErrorCode());
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [update] method : - " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "update", ScenMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
		}
		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}

	/**
	 *
	 * Method to validate the Notify Scenario details that are given in add Scenario
	 * Role Notifications screen and returns an error message if the validation
	 * fails or update the Role Notifications details in database
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String updateRole() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		String hostId = null;
		// DynaValidatorForm dyForm = null;
		ScenarioNotify scenarioNotif = null;

		try {
			log.debug(this.getClass().getName() + " - [updateRole] - " + "Entry");
			scenarioNotif = (ScenarioNotify) getScenarioNotification();

			// Set access required, deliver popups, flash icon and send mail properties.
			// They depend on the check box values
			if (scenarioNotif.getAccessrequired() == null
					|| (!scenarioNotif.getAccessrequired().equals(SwtConstants.YES)))
				scenarioNotif.setAccessrequired(SwtConstants.NO);
			if (scenarioNotif.getDeliverpopups() == null
					|| (!scenarioNotif.getDeliverpopups().equals(SwtConstants.YES)))
				scenarioNotif.setDeliverpopups(SwtConstants.NO);
			if (scenarioNotif.getFlashicon() == null || (!scenarioNotif.getFlashicon().equals(SwtConstants.YES)))
				scenarioNotif.setFlashicon(SwtConstants.NO);
			if (scenarioNotif.getSendemail() == null || (!scenarioNotif.getSendemail().equals(SwtConstants.YES)))
				scenarioNotif.setSendemail(SwtConstants.NO);
			if (scenarioNotif.getFullinstanceaccess() == null
					|| (!scenarioNotif.getFullinstanceaccess().equals(SwtConstants.YES)))
				scenarioNotif.setFullinstanceaccess(SwtConstants.NO);

			String emailUsers = request.getParameter("emailusers");
			scenarioNotif.setEmailusers(emailUsers);
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			scenarioNotif.getId().setHostId(hostId);
			scenMaintenanceManager.updateScenarioNotificationDetail(scenarioNotif);

			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");

			/*
			 * Pass the Request parameter to set entity, role and scenario List for add
			 * screen to the request attribute
			 */
			putEntityListInReq(request);
			putRoleListInReq(hostId, request);
			putScenarioListInReq(request);

			setScenarioNotification(scenarioNotif);
			// Put the required attributes in the request
			request.setAttribute("scenarioNotification", scenarioNotif);
			request.setAttribute("methodName", "changeRole");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [updateRole] - " + "Exit");
			return getView("changerole");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateRole] method swt : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "updateRole");
			return getView("changerole");
		} catch (Exception exp) {
			log.error(
					this.getClass().getName() + " - Exception Catched in [updateRole] method : - " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "updateRole", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 *
	 * Method to validate the Scenario Category details that are given in add
	 * Scenario Category Maintenance screen and returns an error message if the
	 * validation fails or update the Category details in database
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String updateCategory() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		// DynaValidatorForm dyForm = null;
		ScenarioCategory scenarioCateg = null;

		try {
			log.debug(this.getClass().getName() + " - [updateCategory] - " + "Entry");
			scenarioCateg = (ScenarioCategory) getScenarioCategory();
			// Set access system flag They depend on the check box values
			if (scenarioCateg.getSystemflag() == null || (!scenarioCateg.getSystemflag().equals(SwtConstants.YES)))
				scenarioCateg.setSystemflag(SwtConstants.NO);

			/* Reads the hostId from SwtUtil */
			scenMaintenanceManager.updateScenarioCategoryDetails(scenarioCateg);

			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");

			setScenarioCategory(scenarioCateg);
			// Put the required attributes in the request
			request.setAttribute("scenarioCategory", scenarioCateg);
			request.setAttribute("methodName", "changeCategory");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [updateCategory] - " + "Exit");
			return getView("changecategory");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateCategory] method swt : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "updateCategory");
			return getView("changecategory");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateCategory] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "updateCategory", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * This function is used to delete the scenario details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		String scenarioID = "";
		Scenario scenario;
		int accessInd;
		ArrayList scenariosDetailList;

		try {
			log.debug(this.getClass().getName() + "- [delete] - Entering");
			scenarioID = request.getParameter("selectedScenarioID");
			scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioID);

			// Delete scenario details
			scenMaintenanceManager.deleteScenarioDetail(scenario);
			/*
			 * Set the screenFieldsStatus to true. if screenFieldsStatus is true then
			 * disable the related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			// Collect the scenario list in scenarioMaintenanceDetailVO
			scenariosDetailList = scenMaintenanceManager.getScenariosDetailList(null);


			StringBuilder textInRunEveryColumn = null;
			for (Iterator<Scenario> it = scenariosDetailList.iterator(); it.hasNext();) {
				Scenario scen = (Scenario) it.next();
				if("S".equals(scen.getGenerationBasis())) {
//					Scheduled: (08:00, 10:00, 12:30...) show first 3 defined and use '...'
					textInRunEveryColumn = new StringBuilder();
					textInRunEveryColumn.append("Scheduled:(");

					Collection scenScheduler = scenMaintenanceManager.getScenarioSchedule(scen.getId().getScenarioId());
					int numberOfSchedule = 0;
					if(scenScheduler != null) {
						for (Iterator<ScenarioSchedule> it2 = scenScheduler.iterator(); it2.hasNext();) {
							ScenarioSchedule scenSchedule = (ScenarioSchedule) it2.next();
							String time=(!SwtUtil.isEmptyOrNull(scenSchedule.getCheckTime()))?scenSchedule.getCheckTime():"";
							textInRunEveryColumn.append(time+",");
							numberOfSchedule++;
							if(numberOfSchedule==3)
								break;
						}
					}
					if(numberOfSchedule>3) {
						textInRunEveryColumn.append("...");
					}
					textInRunEveryColumn.append(" )");
					scen.setRunEvery(textInRunEveryColumn.toString().replace(", )", ")").replace(":( )", ""));


				}else if("A".equals(scen.getGenerationBasis())) {

				}else {
					textInRunEveryColumn = new StringBuilder();
					textInRunEveryColumn.append("Cyclic:(");
//					Cyclic: (Every <run_every> between <start> and <end>
					if(!SwtUtil.isEmptyOrNull(scen.getRunEvery())) {
						textInRunEveryColumn.append(" Every "+scen.getRunEvery());
					}

					if(!SwtUtil.isEmptyOrNull(scen.getStartTime())) {
						textInRunEveryColumn.append(" between "+scen.getStartTime() +" and "+scen.getEndTime());
					}
					textInRunEveryColumn.append(" )");
					scen.setRunEvery(textInRunEveryColumn.toString().replace(":( )", ""));

				}
			}


			request.setAttribute("scenariosMaintenanceDetails", scenariosDetailList);

			/*
			 * Pass the Request parameter to set MiscParams of alert to the request
			 * attribute
			 */
			putCategoryListInReq(request);
			// Put the required attribute in the request
			request.setAttribute("methodName", "list");
			log.debug(this.getClass().getName() + "- [delete] - Exiting");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [delete] method : - " + swtexp.getMessage());
			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			/* Collect the Alert message list in SystemAlertMessagesDetailVO */
			scenariosDetailList = scenMaintenanceManager.getScenariosDetailList(null);
			request.setAttribute("scenariosMaintenanceDetails", scenariosDetailList);
			request.setAttribute("methodName", "list");
			log.debug(this.getClass().getName() + "- [delete] - Exiting");
			return getView("success");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [delete] method : - " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "delete", GroupAction.class),
					request, "");

			return getView("fail");
		}
	}

	/**
	 * This function is used to delete the scenario Role assignment details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteRole() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		String scenarioID = "";
		ScenarioNotify scenarioNot;
		int accessInd;
		ArrayList scenarioNotifDetails;
		String hostId;
		String entityId;
		String roleId;

		try {
			log.debug(this.getClass().getName() + "- [deleteRole] - Entering");
			scenarioID = request.getParameter("selectedScenarioID");
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			scenarioID = request.getParameter("selectedScenarioID");
			entityId = request.getParameter("selectedEntityValue");
			roleId = request.getParameter("selectedRoleValue");
			// Get scenario Notification details
			scenarioNot = scenMaintenanceManager.getRoleAssignmentEditableData(hostId, scenarioID, entityId, roleId);
			// delete scenario notification details
			scenMaintenanceManager.deleteScenarioNotificationDetail(scenarioNot);

			/*
			 * Set the screenFieldsStatus to true. if screenFieldsStatus is true then
			 * disable the related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			// Collect the scenario notification detail list
			scenarioNotifDetails = scenMaintenanceManager.getScenariosNotificationDetailList();
			// Put the required attribute in the request
			request.setAttribute("scenarioNotificationDetails", scenarioNotifDetails);
			request.setAttribute("methodName", "role");
			log.debug(this.getClass().getName() + "- [deleteRole] - Exiting");
			return getView("role");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [deleteRole] method : - "
					+ swtexp.getMessage());
			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			/* Collect the scenario notification list in scenarioNotifDetails */
			scenarioNotifDetails = scenMaintenanceManager.getScenariosNotificationDetailList();

			request.setAttribute("scenarioNotificationDetails", scenarioNotifDetails);
			request.setAttribute("methodName", "role");
			log.debug(this.getClass().getName() + "- [deleteRole] - Exiting");
			return getView("role");

		} catch (Exception exp) {
			log.error(
					this.getClass().getName() + " - Exception Catched in [deleteRole] method : - " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "deleteRole", GroupAction.class),
					request, "");

			return getView("fail");
		}
	}

	/**
	 * This function is used to delete the scenario Role assignment details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteCategory() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		String categoryId = "";
		ScenarioCategory scenarioCat;
		int accessInd;
		ArrayList scenarioCategoryDetails;
		String tabName1 = null;
		String tabName2 = null;
		try {
			log.debug(this.getClass().getName() + "- [deleteCategory] - Entering");
			categoryId = request.getParameter("selectedCategoryId");
			// Get scenario Notification details
			scenarioCat = scenMaintenanceManager.getCategoryEditableData(categoryId);
			// delete scenario notification details
			scenMaintenanceManager.deleteScenarioCategoryDetails(scenarioCat);

			/*
			 * Set the screenFieldsStatus to true. if screenFieldsStatus is true then
			 * disable the related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			// Collect the scenario notification detail list
			scenarioCategoryDetails = (ArrayList) scenMaintenanceManager.getCategoryDetailedList();

			tabName1 = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME);
			tabName2 = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME);

			for (int i = 0; i < scenarioCategoryDetails.size(); i++) {
				if (((ScenarioCategory) scenarioCategoryDetails.get(i)).getDisplayTab() == 1)
					((ScenarioCategory) scenarioCategoryDetails.get(i)).setDisplayTabName(tabName1);
				else
					((ScenarioCategory) scenarioCategoryDetails.get(i)).setDisplayTabName(tabName2);

			}

			// Put the required attribute in the request
			request.setAttribute("scenarioCategoryDetails", scenarioCategoryDetails);
			request.setAttribute("methodName", "category");
			log.debug(this.getClass().getName() + "- [deleteCategory] - Exiting");
			return getView("category");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [deleteCategory] method : - "
					+ swtexp.getMessage());

			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			/* Collect the scenario notification list in scenarioNotifDetails */
			scenarioCategoryDetails = (ArrayList) scenMaintenanceManager.getCategoryDetailedList();

			request.setAttribute("scenarioCategoryDetails", scenarioCategoryDetails);
			request.setAttribute("methodName", "category");
			log.debug(this.getClass().getName() + "- [deleteCategory] - Exiting");
			return getView("category");

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [deleteCategory] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "deleteCategory", GroupAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Update the advanced details of a scenario not in the data base but in the
	 * add/change scenario maintenance screen. The update/save of advanced details
	 * will be done only when saving from the add/change scenario maintenance
	 * screen.
	 * @return
	 * @throws SwtException
	 */
	public String updateAdvancedDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Method's local variable and class instance declaration
		// DynaValidatorForm dyForm = null;
		Scenario scenario = null;
		Facility facility = null;

		try {
			log.debug(this.getClass().getName() + " - [updateAdvancedDetails] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to false as we have to update the details. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			scenario = (Scenario) getScenarioMaintenance();
			facility = (Facility) getFacility();
			// Set use generic display property. It depends on the check box values
			if (scenario.getUseGenericDisplay() == null || (!scenario.getUseGenericDisplay().equals(SwtConstants.YES)))
				scenario.setUseGenericDisplay(SwtConstants.NO);

			// Set the facility to the required scenario
			// Get the detail of facility if the user selects a valid facility. If None then
			// create a new facility
			if (facility.getFacilityid().equals(SwtConstants.FACILITY_NONE))
				facility = new Facility();
			else
				facility = scenMaintenanceManager.getFacilityDetails(facility.getFacilityid());
			scenario.setFacility(facility);

			setScenarioMaintenance(scenario);
			setFacility(facility);

			// Pass the request parameter to set summaryGroupingList and facilityList then
			// fill it in the related combo boxes.
			putSummaryGroupingListInReq(request);
			putFacilityListInReq(request);

			// Put the required attribute in the request
			request.setAttribute("scenTitle", scenario.getTitle());
			request.setAttribute("scenDescription", request.getParameter("description"));
			request.setAttribute("scenSummaryGrouping", scenario.getSummaryGrouping());
			request.setAttribute("scenBaseQuery", request.getParameter("baseQuery"));
			request.setAttribute("scenHostColumn", request.getParameter("advSecHostCol"));
			request.setAttribute("scenEntityColumn", request.getParameter("advSecEntityCol"));
			request.setAttribute("scenCurrencyColumn", request.getParameter("advSecCurrencyCol"));
			request.setAttribute("scenAmountColumn", request.getParameter("advAmtThresholdCol"));
			request.setAttribute("scenRefcolumns", request.getParameter("advFacilityRefCols"));
			request.setAttribute("scenParamValues", request.getParameter("advFacilityParamVals"));
			request.setAttribute("scenGenericDisplay", scenario.getUseGenericDisplay());
			request.setAttribute("scenFacilityID", scenario.getFacility().getFacilityid());
			request.setAttribute("selectedSystemFlag", scenario.getSystemFlag());
			request.setAttribute("methodName", request.getParameter("openedMethodName"));
			request.setAttribute("openedMethodName", request.getParameter("openedMethodName"));
			request.setAttribute("selectedSystemFlag", request.getParameter("selectedSystemFlag"));
			request.setAttribute("selectedScenarioID", scenario.getId().getScenarioId());
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			// If parentFromRefresh value is 'yes', then we will close the opened screen and
			// refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("selectedFacilityId", request.getParameter("selectedFacilityId"));
			log.debug(this.getClass().getName() + " - [updateAdvancedDetails] - " + "Exit");
			return getView("advanced");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateAdvancedDetails] method swt : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "update");
			return getView("change");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateAdvancedDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "updateAdvancedDetails",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
		}
	}

	public String updateFacilityList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Method's local variable and class instance declaration
		// DynaValidatorForm dyForm = null;
		Scenario scenario = null;
		Facility facility = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String genericDisplaySelected = null;
		try {
			log.debug(this.getClass().getName() + " - [updateFacilityList] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to false. if screenFieldsStatus is true then
			 * disable the related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			scenario = (Scenario) getScenarioMaintenance();
			facility = (Facility) getFacility();
			scenario.getId().setScenarioId(request.getParameter("selectedScenarioID"));
			scenario.setDescription(request.getParameter("description"));
			scenario.setTitle(request.getParameter("title"));
			scenario.setQueryText(request.getParameter("baseQuery"));
			genericDisplaySelected = request.getParameter("genericDisplaySelected");
			// Set use generic display property. It depends on the check box values
			if (scenario.getUseGenericDisplay() == null || (!scenario.getUseGenericDisplay().equals(SwtConstants.YES)))
				scenario.setUseGenericDisplay(SwtConstants.NO);

			facility = new Facility();
			scenario.setFacility(facility);
			setScenarioMaintenance(scenario);
			setFacility(facility);

			// Pass the request parameter to set the summaryGroupingList and fill it in the
			// related combo box.
			putSummaryGroupingListInReq(request);

			// The selected facilities depends on the use generic display value.
			// If selected then put the list of facilities with the condition
			// P_FAICILITY.USER_SELECTABLE != 'G'
			if (scenario.getUseGenericDisplay().equals(SwtConstants.YES))
				putFacilityListForEditInReq(request, true);
			else
				putFacilityListForEditInReq(request, false);
			// added by FMrad
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			/***** Facility Combo ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection facilityList = putFacilityListForEditInReq(request,
					Boolean.parseBoolean(genericDisplaySelected));
			Iterator j = facilityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("facilityList", lstOptions));
			responseConstructor.formSelect(lstSelect);

			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());

			request.setAttribute("scenarioID", scenario.getId().getScenarioId());
			request.setAttribute("title", scenario.getTitle());
			request.setAttribute("description", scenario.getDescription());
			request.setAttribute("scenSummaryGrouping", scenario.getSummaryGrouping());
			request.setAttribute("scenBaseQuery", scenario.getQueryText());
			request.setAttribute("scenHostColumn", request.getParameter("advSecHostCol"));
			request.setAttribute("scenEntityColumn", request.getParameter("advSecEntityCol"));
			request.setAttribute("scenCurrencyColumn", request.getParameter("advSecCurrencyCol"));
			request.setAttribute("scenAmountColumn", request.getParameter("advAmtThresholdCol"));
			request.setAttribute("scenRefcolumns", request.getParameter("advFacilityRefCols"));
			request.setAttribute("scenParamValues", request.getParameter("advFacilityParamVals"));
			request.setAttribute("scenGenericDisplay", scenario.getUseGenericDisplay());
			request.setAttribute("scenFacilityID", scenario.getFacility().getFacilityid());
			request.setAttribute("selectedSystemFlag", scenario.getSystemFlag());
			request.setAttribute("methodName", request.getParameter("openedMethodName"));
			request.setAttribute("openedMethodName", request.getParameter("openedMethodName"));
			request.setAttribute("fromAdvanced", SwtConstants.STR_TRUE);
			request.setAttribute("selectedScenarioID", scenario.getId().getScenarioId());
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [updateFacilityList] - " + "Exit");
			return getView("data");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [updateFacilityList] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "updateFacilityList", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * return additional params of the facility
	 * @return
	 * @throws SwtException
	 */
	public String getAdditionalInfo() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String scenarioId = "";
		String scenarioRefParams;
		String facilityRefParams;
		Scenario scenario;
		XmlEncoder encoder = null;
		String additionalParams = null;
		try {
			log.debug(this.getClass().getName() + "- [getScreenDetails] - Enter");
			scenarioId = request.getParameter("scenarioId");
			// get scenario data
			scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioId);
			if (scenario != null) {
				if (scenario.getFacilityParamVals() != null) {
					scenarioRefParams = scenario.getFacilityParamVals();
				} else {
					scenarioRefParams = "";
				}
				if (scenario.getFacility() != null)
					facilityRefParams = scenario.getFacility().getRefparam();
				else {
					facilityRefParams = "";
				}

				if (facilityRefParams != null) {
					encoder = new XmlEncoder();
					additionalParams = encoder.builFacilityParamsInUrl(facilityRefParams, scenarioRefParams);
				} else
					additionalParams = "";
			} else
				return getView("fail");
			// Set the screenDetails to response
			response.getWriter().print(additionalParams);
			log.debug(this.getClass().getName() + " - [getScreenDetails] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getScreenDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "getScreenDetails", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}

	}

	/**
	 * Get the base query, entity column and currency column of a scenario id These
	 * properties are used when opening the generic display screen from scenario
	 * summary
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getScenPropertiesForGeneric() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String scenarioId = "";
		String scenProperties = "";
		Scenario scenario;
		String entityColumn = null;
		String currencyColumn = null;
		String facilityRefCols = null;
		String scenariorefcolumns = null;
		String scenarioRefParams = null;
		String facilityRefParams = null;
		try {
			log.debug(this.getClass().getName() + "- [getScenPropertiesForGeneric] - Enter");
			scenarioId = request.getParameter("scenarioId");
			// Get a string like: 'program name','height','width'
			scenario = scenMaintenanceManager.getEditableDataDetailList(scenarioId);
			if (scenario != null) {
				entityColumn = SwtUtil.isEmptyOrNull(scenario.getSecEntityCol()) ? "" : scenario.getSecEntityCol();
				currencyColumn = SwtUtil.isEmptyOrNull(scenario.getSecCurrencyCol()) ? ""
						: scenario.getSecCurrencyCol();
				scenariorefcolumns = SwtUtil.isEmptyOrNull(scenario.getFacilityRefCols()) ? ""
						: scenario.getFacilityRefCols();
				scenarioRefParams = SwtUtil.isEmptyOrNull(scenario.getFacilityParamVals()) ? ""
						: scenario.getFacilityParamVals();
				if (scenario.getFacility() != null) {
					facilityRefCols = SwtUtil.isEmptyOrNull(scenario.getFacility().getRefcolumns()) ? ""
							: scenario.getFacility().getRefcolumns();
					facilityRefParams = SwtUtil.isEmptyOrNull(scenario.getFacility().getRefparam()) ? ""
							: scenario.getFacility().getRefparam();
				} else {
					facilityRefCols = "";
					facilityRefParams = "";
				}
				scenProperties = scenario.getQueryText() + SwtConstants.SEPARATOR_RECORD + entityColumn
						+ SwtConstants.SEPARATOR_RECORD + currencyColumn + SwtConstants.SEPARATOR_RECORD
						+ scenariorefcolumns + SwtConstants.SEPARATOR_RECORD + facilityRefCols
						+ SwtConstants.SEPARATOR_RECORD + SwtUtil.encode64(scenarioRefParams)
						+ SwtConstants.SEPARATOR_RECORD + SwtUtil.encode64(facilityRefParams);
			}
			// Get the screenDetails to response
			response.getWriter().print(scenProperties);
			log.debug(this.getClass().getName() + " - [getScenPropertiesForGeneric] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getScenPropertiesForGeneric] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getScenPropertiesForGeneric",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to add the new category.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addCategory() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String tabNames = null;
		String[] tabNamesArray = null;
		try {
			log.debug(this.getClass().getName() + " - [addCategory] - " + "Entry");
			/*
			 * Set the screenFieldsStatus to false because we are in the add role part. if
			 * screenFieldsStatus is true then disable the related fields which contain this
			 * attribute (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);

			/*
			 * tabNames = PropertiesFileLoader.getInstance()
			 * .getPropertiesValue(SwtConstants.SCENARIO_CATEGORY_TAB_NAMES);
			 * if(!SwtUtil.isEmptyOrNull(tabNames)) { tabNamesArray =
			 * tabNames.split("\\s*,\\s*"); if(tabNamesArray.length>0) { for(int i = 0 ; i <
			 * tabNamesArray.length ; i++) { request.setAttribute("tabName"+(i+1),
			 * tabNamesArray[i]); } } }
			 */

			request.setAttribute("tabName1",
					PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB1_NAME));
			request.setAttribute("tabName2",
					PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ALERT_SUMMARY_TAB2_NAME));

			/* Get the editable data list to the ScenarioNotify bean */
			request.setAttribute("methodName", "addCategory");
			request.setAttribute(SwtConstants.SAV_BUT_STS, SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [addCategory] - " + "Exit");
			return getView("addcategory");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [addCategory] method : - " + e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "addCategory", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to display the distribution list.
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String displayDistList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [displayDistList] - " + "Entry");

		String entityId = null;
		String roleId = null;
		String scenarioID = null;
		String hostId = null;
		try {
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("selectedEntityID");
			roleId = request.getParameter("selectedRoleID");
			scenarioID = request.getParameter("selectedScenarioID");

			// Getting distributionList from scenMaintenance Manager
			Collection collUser = scenMaintenanceManager.getDistListByRole(hostId, entityId, roleId, scenarioID);
			request.setAttribute("usersDistributionList", collUser);

			request.setAttribute("hostId", hostId);
			request.setAttribute("entityId", entityId);
			request.setAttribute("roleId", roleId);
			request.setAttribute("scenarioID", scenarioID);

			log.debug(this.getClass().getName() + " - [displayDistList] - " + "Exit");
			return getView("distList");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [display] method : - " + exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "displayDistList", ScenMaintenanceAction.class),
					request, "");
			return getView("fail");

		}
	}

	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns, String grid)
			throws SwtException {

		ArrayList<String> orders = null;
		String[] columnOrderProp = null;
		Iterator<String> columnOrderItr = null;
		LinkedHashMap<String, String> widths = null;
		String[] columnWidthProperty = null;
		List<ColumnInfo> lstColumns = null;
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		ArrayList<String> lstHiddenColunms = null;
		String[] hiddenColumnsProp = null;
		ColumnInfo tmpColumnInfo = null;

		try {
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
			// Condition to check width is null
			if (grid.equals("guiGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					//width = "guiIdDescripion" + "=610," + "guiSelect" + "=50,"  + "guiOtherId" + "=150," + "guiRequiredParams" + "=5,"  + "guiScenInstance"  + "=5";
					width = "guiIdDescripion" + "=610," + "guiSelect" + "=50," + "guiRequiredParams" + "=5,"  + "guiScenInstance"  + "=5";
				}
			} else if (grid.equals("eventGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "eventSeq" + "=50," + "eventId" + "=300," + "eventDescription" + "=450," + "eventParams" + "=150,"
							+ "eventRepeat" + "=150," + "xmlColumn" + "=5," + "mapKey" + "=5";

				}
			} else if (grid.equals("emailGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "user" + "=200," + "name" + "=200," + "role" + "=200,"
							+ "access" + "=80," + "email" + "=80";

				}
			}else if (grid.equals("subGuiGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "subGuiReq" + "=10," + "subGuiId" + "=100," + "subGuiDescription" + "=300," + "subGuiType"
							+ "=80," + "subGuiMapFrom" + "=200";

				}
			} else if (grid.equals("subEventGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "subEvReq" + "=10," + "subEvId" + "=250," + "subEvDescription" + "=310," + "subEvType"
							+ "=70," + "subEvMapFrom" + "=130," + "subEvMapFromValue" + "=150," + "subEvAddInfo" + "=5,"
							+ "subEvRegExp" + "=5,"+ "subEvRegExpMsg" + "=5";

				}
			}else if (grid.equals("generalGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "schedulerId" + "=5,"+ "time" + "=120,"+ "parameters" + "=750";

				}
			}else if (grid.equals("defParamsGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "name" + "=160,"+ "description" + "=390";

				}
			}else if (grid.equals("scheduleGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "parameter" + "=160,"+ "value" + "=390";

				}
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (grid.equals("guiGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					//columnOrder =  "guiIdDescripion" + "," + "guiSelect" + "," +"guiOtherId" + ","+ "guiRequiredParams"+ ","+ "guiScenInstance";
					columnOrder =  "guiIdDescripion" + "," + "guiSelect" + ","+ "guiRequiredParams"+ ","+ "guiScenInstance";
				}
			} else if (grid.equals("eventGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "eventSeq" + "," + "eventId" + "," + "eventDescription" + "," + "eventParams" + "," + "eventRepeat" + "," + "xmlColumn"+"," + "mapKey";
				}
			} else if (grid.equals("emailGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "user" + "," + "name" + "," + "role" + "," + "access" + "," + "email";
				}
			}else if (grid.equals("subGuiGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "subGuiReq" + "," + "subGuiId" + "," + "subGuiDescription" + "," + "subGuiType" + ","
							+ "subGuiMapFrom";
				}
			} else if (grid.equals("subEventGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "subEvReq" + "," + "subEvId" + "," + "subEvDescription" + "," + "subEvType" + ","
							+ "subEvMapFrom" + "," + "subEvMapFromValue" + "," + "subEvAddInfo" + "," + "subEvRegExp" + "," + "subEvRegExpMsg";
				}
			} else if (grid.equals("generalGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "schedulerId" + "," + "time" + "," + "parameters";
				}
			}else if (grid.equals("defParamsGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "name" + "," + "description";
				}
			}else if (grid.equals("scheduleGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "parameter" + "," + "value";
				}
			}

			orders = new ArrayList<String>();
			columnOrderProp = columnOrder.split(",");

			for (int i = 0; i < columnOrderProp.length; i++) {
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			if (grid.equals("guiGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("guiSeq"))
						lstColumns.add(
								new ColumnInfo(SwtConstants.SCENARIO_GUI_SEQ, "guiSeq", PCMConstant.COLUMN_TYPE_NUMBER,
										0, Integer.parseInt(widths.get("guiSeq")), false, true, true));

					else if (order.equals("guiIdDescripion"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_IDDESCRIPTION, "guiIdDescripion",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("guiIdDescripion")),
								false, true, true));


					else if (order.equals("guiSelect")) {
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_SELECT, "guiSelect",
								PCMConstant.COLUMN_TYPE_CHECK, 2, Integer.parseInt(widths.get("guiSelect")), false,
								true, true));
					} /*else if (order.equals("guiOtherId")) {
						tmpColumnInfo = new ColumnInfo(SwtConstants.SCENARIO_GUI_OTHER_ID, "guiOtherId",
								SwtConstants.COLUMN_TYPE_COMBO,3, Integer.parseInt(widths.get("guiOtherId")), false,
								true, true);
						tmpColumnInfo.setDataProvider("GuiOtherIdOptions");
						lstColumns.add(tmpColumnInfo);

					}*/else if (order.equals("guiRequiredParams")) {
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_PARAMETERS, "guiRequiredParams",
								PCMConstant.COLUMN_TYPE_STRING, 4, Integer.parseInt(widths.get("guiRequiredParams")), false,
								false, false));
					}else if (order.equals("guiScenInstance")) {
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_REQUIRED_SCENARIO_INSTANCE, "guiScenInstance",
								PCMConstant.COLUMN_TYPE_STRING, 5, Integer.parseInt(widths.get("guiScenInstance")), false,
								false, false));
					}




				}
			} else if (grid.equals("emailGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("user"))
						lstColumns.add(
								new ColumnInfo(SwtConstants.SCENARIO_USER, "user", PCMConstant.COLUMN_TYPE_NUMBER,
										0, Integer.parseInt(widths.get("user")), false, true, true));

					else if (order.equals("name"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_NAME, "name",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("name")),
								false, true, true));
					else if (order.equals("role"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_ROLE, "role",
								PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get("role")), false,
								false, false));

					else if (order.equals("access")) {
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_ACCESS, "access",
								PCMConstant.COLUMN_TYPE_CHECK, 3, Integer.parseInt(widths.get("access")), false,
								true, true));
					} else if (order.equals("email")) {
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EMAIL, "email",
								PCMConstant.COLUMN_TYPE_CHECK, 4, Integer.parseInt(widths.get("email")), false,
								true, true));
					}
				}
			}else if (grid.equals("eventGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("eventSeq"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_SEQ, "eventSeq",
								PCMConstant.COLUMN_TYPE_NUMBER, 0, Integer.parseInt(widths.get("eventSeq")), false,
								true, true));

					else if (order.equals("eventId"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_FACILITY_ID, "eventId",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("eventId")),
								false, true, true));
					else if (order.equals("eventDescription"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_FACILITY_DESCRIPTION, "eventDescription",
								PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get("eventDescription")),
								false, true, true));
					else if (order.equals("eventParams"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_PARAMETERS, "eventParams",
								PCMConstant.COLUMN_TYPE_STRING, 3, Integer.parseInt(widths.get("eventParams")), false,
								true, true));
					else if (order.equals("eventRepeat"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_REPEAT, "eventRepeat",
								PCMConstant.COLUMN_TYPE_STRING, 4, Integer.parseInt(widths.get("eventRepeat")), false,
								true, true));
					else if (order.equals("xmlColumn"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_XML, "xmlColumn",
								PCMConstant.COLUMN_TYPE_STRING, 5, Integer.parseInt(widths.get("xmlColumn")), false,
								false, false));

					else if (order.equals("mapKey"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_MAP_KEY_XML, "mapKey",
								PCMConstant.COLUMN_TYPE_STRING, 6, Integer.parseInt(widths.get("mapKey")), false,
								false, false));
				}
			} else if (grid.equals("subGuiGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("subGuiReq"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_REQ, "subGuiReq",
								PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get("subGuiReq")), false,
								true, true));

					else if (order.equals("subGuiId"))
						lstColumns.add(
								new ColumnInfo(SwtConstants.SCENARIO_GUI_ID, "subGuiId", PCMConstant.COLUMN_TYPE_STRING,
										1, Integer.parseInt(widths.get("subGuiId")), false, true, true));
					else if (order.equals("subGuiDescription"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_DESCRIPTION, "subGuiDescription",
								PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get("subGuiDescription")),
								false, true, true));
					else if (order.equals("subGuiType"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_TYPE, "subGuiType",
								PCMConstant.COLUMN_TYPE_STRING, 3, Integer.parseInt(widths.get("subGuiType")), false,
								true, true));
					else if (order.equals("subGuiMapFrom"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GUI_MAP_FROM, "subGuiMapFrom",
								PCMConstant.COLUMN_TYPE_STRING, 4, Integer.parseInt(widths.get("subGuiMapFrom")), false,
								true, true));

				}
			} else if (grid.equals("subEventGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("subEvReq"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_REQ, "subEvReq",
								PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get("subEvReq")), false,
								true, true));

					else if (order.equals("subEvId"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_ID, "subEvId",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("subEvId")), false, true,
								true));
					else if (order.equals("subEvDescription"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_DESCRIPTION, "subEvDescription",
								PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get("subEvDescription")),
								false, true, true));
					else if (order.equals("subEvType"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_TYPE, "subEvType",
								PCMConstant.COLUMN_TYPE_STRING, 3, Integer.parseInt(widths.get("subEvType")), false,
								true, true));
					else if (order.equals("subEvMapFrom"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_MAP_FROM, "subEvMapFrom",
								PCMConstant.COLUMN_TYPE_STRING, 4, Integer.parseInt(widths.get("subEvMapFrom")), false,
								true, true));
					else if (order.equals("subEvMapFromValue"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_MAP_FROM_VAL, "subEvMapFromValue",
								PCMConstant.COLUMN_TYPE_STRING, 5, Integer.parseInt(widths.get("subEvMapFromValue")), false,
								true, true));
					else if (order.equals("subEvAddInfo"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_ADDITIONAL_INFO, "subEvAddInfo",
								PCMConstant.COLUMN_TYPE_STRING, 6, Integer.parseInt(widths.get("subEvAddInfo")), false,
								false, false));
					else if (order.equals("subEvRegExp"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_REGULAR_EXP, "subEvRegExp",
								PCMConstant.COLUMN_TYPE_STRING, 6, Integer.parseInt(widths.get("subEvRegExp")), false,
								false, false));
					else if (order.equals("subEvRegExpMsg"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_EVENT_REGULAR_EXP_MSG, "subEvRegExpMsg",
								PCMConstant.COLUMN_TYPE_STRING, 6, Integer.parseInt(widths.get("subEvRegExpMsg")), false,
								false, false));


				}
			}else if (grid.equals("generalGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("schedulerId"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_SCHED_ID, "schedulerId",
								PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get("schedulerId")), false,
								true, false));

					else if (order.equals("time"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_TIME, "time",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("time")), false,
								true, true));

					else if (order.equals("parameters"))
						lstColumns.add(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_PARAMS, "parameters",
								PCMConstant.COLUMN_TYPE_STRING, 2, Integer.parseInt(widths.get("parameters")), false, true,
								true));
				}
			}else if (grid.equals("defParamsGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();

					if (order.equals("name")) {
						tmpColumnInfo=(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_NAME, "name",
								PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get("name")), false,
								true, true));
						tmpColumnInfo.setEditable(false);
						tmpColumnInfo.setMaxChars("20");
						lstColumns.add(tmpColumnInfo);
					}
					else if (order.equals("description")) {
						tmpColumnInfo=(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_DESC, "description",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("description")), false, true,
								true));
						tmpColumnInfo.setEditable(false);
						tmpColumnInfo.setMaxChars("20");
						lstColumns.add(tmpColumnInfo);

					}
				}
			}else if (grid.equals("scheduleGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("parameter")) {
						tmpColumnInfo=(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_SCHED_PARAM, "parameter",
								PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get("parameter")), false,
								true, true));
						tmpColumnInfo.setEditable(false);
						tmpColumnInfo.setMaxChars("20");
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("value")) {
						tmpColumnInfo=(new ColumnInfo(SwtConstants.SCENARIO_GENERAL_SCHED_VAL, "value",
								PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get("value")), false, true,
								true));
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("20");
						lstColumns.add(tmpColumnInfo);

					}

				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

	public String getGuiFacilityData() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		StringBuffer facilityProperties = new StringBuffer();
		ScenarioGuiAlertFacility guiAlertFacility;
		String selectedGuiFacility = "";
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug(this.getClass().getName() + "- [getGuiFacilityData] - Enter");
			selectedGuiFacility = request.getParameter("selectedGuiFacility");
			guiAlertFacility = scenMaintenanceManager.getGuiFacilityDetails(selectedGuiFacility);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			if (guiAlertFacility != null) {

				responseConstructor.createElement("requireScInstance", guiAlertFacility.getRequiresScenarioInstance());
				responseConstructor.createElement("parameterXML", guiAlertFacility.getRequiredParameters());

			} else {
				responseConstructor.createElement("parameterXML", "");
			}
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
			// return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getGuiFacilityData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getGuiFacilityData",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	public String getEventFacilityData() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable and class instance declaration */
		ScenarioEventFacility eventAlertFacility;
		String selectedEventFacility = "";
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug(this.getClass().getName() + "- [getGuiFacilityData] - Enter");
			selectedEventFacility = request.getParameter("selectedEventFacility");
			eventAlertFacility = scenMaintenanceManager.getEventFacilityDetails(selectedEventFacility);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			if (eventAlertFacility != null) {
				responseConstructor.createElement("parameterXML", eventAlertFacility.getRequiredParameters().replace(">", "#"));

			} else {
				responseConstructor.createElement("parameterXML", "");
			}
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
			// return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception Catched in [getEventFacilityData] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "getEventFacilityData",
					ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}



	/**
	 * Get columns related on the base query done by the user.

	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getQueryColumns() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String baseQuery="";
		QueryResult queryResult=null;
		ArrayList<ColumnDTO> columnData = null;
		ColumnMetadata metadata;
		List<String> columnsNames= null;
		List<String> numberColumnsNames= null;
		List<String> textColumnsNames= null;
		List<String> dateColumnsNames= null;
		List<String> otherIdCols= null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Integer index=null;
		//HashMap<String, String> columnsHashMap = null;
		try{
			log.debug(this.getClass().getName() + "- [getQueryColumns] - Enter");

			GenericDisplayMonitorManager genericDisplayMonitorManager = (GenericDisplayMonitorManager) SwtUtil
					.getBean("genericDisplayMonitorManager");
			baseQuery = request.getParameter("baseQuery");
			baseQuery = SwtUtil.decode64(baseQuery);

			if (baseQuery != null)
				queryResult = genericDisplayMonitorManager.getGenericDisplayData(baseQuery);

			columnData = new ArrayList<ColumnDTO>();
			columnsNames = new ArrayList<String>();
			numberColumnsNames= new ArrayList<String>();
			textColumnsNames= new ArrayList<String>();
			dateColumnsNames= new ArrayList<String>();
			otherIdCols = new ArrayList<String>();
			//columnsHashMap = new HashMap<String, String>();
			if (queryResult != null) {
				for (int i = 0; i < queryResult.getMetadataDetails().size(); i++) {
					metadata = (ColumnMetadata) queryResult.getMetadataDetails().get(i);
					columnsNames.add('"'+metadata.getColumnLabel()+'"');
					otherIdCols.add(metadata.getColumnLabel());
					//columnsHashMap.put( '"'+metadata.getColumnLabel()+'"',metadata.getColumnTypeName());
					if ("NUMBER".equalsIgnoreCase(metadata.getColumnTypeName())
							|| "INTEGER".equalsIgnoreCase(metadata.getColumnTypeName())
							|| "LONG".equalsIgnoreCase(metadata.getColumnTypeName())
							|| "FLOAT".equalsIgnoreCase(metadata.getColumnTypeName())) {
						numberColumnsNames.add('"'+metadata.getColumnLabel()+'"');
					}

					if ("VARCHAR2".equalsIgnoreCase(metadata.getColumnTypeName())
							|| "CHAR".equalsIgnoreCase(metadata.getColumnTypeName())
							|| "CLOB".equalsIgnoreCase(metadata.getColumnTypeName())) {
						textColumnsNames.add('"'+metadata.getColumnLabel()+'"');
					}

					if ("DATE".equalsIgnoreCase(metadata.getColumnTypeName())){
						dateColumnsNames.add('"'+metadata.getColumnLabel()+'"');
					}
				}
			}

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SCENARIO_DETAILS);
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			/*****  Combo End ***********/

			/*****  all columns Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("","", false));
			if (columnsNames != null) {
				for (int i = 0; i < columnsNames.size(); i++) {
					index=i;
					lstOptions.add(new OptionInfo(index.toString(), columnsNames.get(i), false));
				}

			}

		/*if (columnsHashMap != null) {
		    for(Entry<String, String> entry: columnsHashMap.entrySet()) {
				lstOptions.add(new OptionInfo(entry.getValue(), entry.getKey(), false));
		      }
		}*/
			lstSelect.add(new SelectInfo("queryColumns", lstOptions));
			/*****  Combo End ***********/


			/*****  number columns Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("","", false));
			if (numberColumnsNames != null) {
				for (int i = 0; i < numberColumnsNames.size(); i++) {
					index=i;
					lstOptions.add(new OptionInfo(index.toString(), numberColumnsNames.get(i), false));
				}

			}
			lstSelect.add(new SelectInfo("numberColumns", lstOptions));
			/*****  number columns Combo End ***********/

			/*****  text columns Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("","", false));
			if (textColumnsNames != null) {
				for (int i = 0; i < textColumnsNames.size(); i++) {
					index=i;
					lstOptions.add(new OptionInfo(index.toString(), textColumnsNames.get(i), false));
				}

			}
			lstSelect.add(new SelectInfo("textColumns", lstOptions));
			/*****  text columns Combo End ***********/

			/*****  date columns Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("","", false));
			if (dateColumnsNames != null) {
				for (int i = 0; i < dateColumnsNames.size(); i++) {
					index=i;
					lstOptions.add(new OptionInfo(index.toString(), dateColumnsNames.get(i), false));
				}

			}
			lstSelect.add(new SelectInfo("dateColumns", lstOptions));
			/*****  date columns Combo End ***********/

			/*****  gui highlight grid Combo Start ***********/
		/*lstOptions = new ArrayList<OptionInfo>();
		lstOptions.add(new OptionInfo("","", false));
		if (otherIdCols != null) {
			for (int i = 0; i < otherIdCols.size(); i++) {
				lstOptions.add(new OptionInfo(otherIdCols.get(i), columnsName.get(i), false));
			}
		}
		lstSelect.add(new SelectInfo("GuiOtherIdOptions", lstOptions));	*/
			/*****  gui highlight grid Combo End ***********/

			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.SCENARIO_DETAILS);
			request.setAttribute("data", xmlWriter.getData());

			log.debug(this.getClass().getName() + " - [getQueryColumns] - " + "Exit");
			return getView("data");

		} catch (SwtException exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getQueryColumns] method : - "
					+ exp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute("reply_location", exp.getStackTrace()[0]
					.getClassName()
					+ "."
					+ exp.getStackTrace()[0].getMethodName()
					+ ":"
					+ exp.getStackTrace()[0].getLineNumber());
			return getView("dataerror");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getQueryColumns] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getQueryColumns", ScenMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	public String defineParameters() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("openDefParam");
	}


	public String openSchedule() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("openSchedDetails");
	}

	public String ConfigureParams() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("configParams");
	}

	public void saveScheduleData(Scenario scenario,HttpServletRequest request, String scheduleXml) throws SwtException {

		String scenarioId = null;
		String time = null;
		String schedulerId = null;
		ScenarioSchedule  scenSchedule = null;
		String xmlParams=null;
		ScenarioSchedule schenarioSchedule= null;
		try {
			log.debug(this.getClass().getName() + "- [saveScheduleData] - starting ");
			ArrayList<ScenarioSchedule> listScenSchedToAdd = new ArrayList<ScenarioSchedule>();
			ArrayList<ScenarioSchedule> listScenSchedToUpdate = new ArrayList<ScenarioSchedule>();
			ArrayList<ScenarioSchedule> listScenSchedToDelete = new ArrayList<ScenarioSchedule>();

			scenarioId=scenario.getId().getScenarioId();
			//delete all scenario schedule
			//scenMaintenanceManager.deleteScenSchedule(scenarioId);
			Collection scenSchedList = scenMaintenanceManager.getScenarioSchedule(scenario.getId().getScenarioId());
			ArrayList<Long> schedIds = new ArrayList<Long>();
			for (Iterator<ScenarioSchedule> it = scenSchedList.iterator(); it.hasNext();) {
				ScenarioSchedule scenSched = (ScenarioSchedule) it.next();
				schedIds.add(scenSched.getScenScheduleId());
			}

			JSONArray scenJSONArray = new JSONArray(scheduleXml);
			for (int i = 0; i < scenJSONArray.length(); i++) {
				scenSchedule = new ScenarioSchedule();
				time = scenJSONArray.getJSONObject(i).getString("time");
				schedulerId = scenJSONArray.getJSONObject(i).getString("schedulerId");
				xmlParams = scenJSONArray.getJSONObject(i).getString("xml");
				scenSchedule.setCheckTime(time);
				scenSchedule.setParameterXml(xmlParams);
				scenSchedule.setScenarioId(scenarioId);
				if (scenJSONArray.getJSONObject(i).has("schedulerId")
						&& !SwtUtil.isEmptyOrNull(scenJSONArray.getJSONObject(i).getString("schedulerId"))) {

					schenarioSchedule = scenMaintenanceManager.getSchedulerById(scenarioId, schedulerId);
					scenSchedule.setScenScheduleId(Long.parseLong(schedulerId));
					scenSchedule.setLastRunStarted(schenarioSchedule.getLastRunStarted());
					scenSchedule.setLastRunEnded(schenarioSchedule.getLastRunEnded());
					scenSchedule.setLastRunStatus(schenarioSchedule.getLastRunStatus());
					listScenSchedToUpdate.add(scenSchedule);
					schedIds.remove(schedIds.indexOf(scenSchedule.getScenScheduleId()));
				} else {
					listScenSchedToAdd.add(scenSchedule);
				}

			}


			for (int j = 0; j < schedIds.size(); j++) {
				ScenarioSchedule todelete = new ScenarioSchedule();
				todelete.setScenScheduleId(schedIds.get(j));
				listScenSchedToDelete.add(todelete);
			}

			scenMaintenanceManager.saveScenSchedule(listScenSchedToAdd, listScenSchedToUpdate, listScenSchedToDelete);

			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + "- [saveScheduleData] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveScheduleData] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveScheduleData", ScenMaintenanceAction.class);

		} finally {
			scenarioId = null;
			time = null;
			scenSchedule = null;
			xmlParams=null;
		}
		//return getView("statechange");
	}

	// open Alert instance summary screen
	public String openAlertInstSummary() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("selectedNodeId", request.getParameter("selectedNodeId"));
		request.setAttribute("treeLevelValue", request.getParameter("treeLevelValue"));
		request.setAttribute("callerMethod", request.getParameter("callerMethod"));

		return getView("alertInst");
	}


	/**
	 * This method is used to put the collection of message formats list for
	 * given scenario id in request and it will be populated in the select box
	 *
	 * @param request
	 * @param hostId
	 * @param scenarioId
	 * @throws SwtException
	 */
	private Collection getScenMsgFormats(HttpServletRequest request,
										 String scenarioId) throws SwtException {
		Collection ScenMsgFormatsList = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [getScenMsgFormats] - Entering");

			MessageFormatsManager msgFldMgr = (MessageFormatsManager) (SwtUtil
					.getBean("messageFormatsManager"));

			ScenMsgFormatsList= msgFldMgr.getScenMsgFormats();
			request.setAttribute("ScenMsgFormatsList",ScenMsgFormatsList);
			log.debug(this.getClass().getName()
					+ " - [getScenMsgFormats] - Existing");
			return ScenMsgFormatsList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getScenMsgFormats] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}



	public String getMsgFormatsList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Movement movement = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getMsgFormatsList] - Entering");

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** Msg Formats Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			Collection collMsgFmts = ((MessageFormatsManager)SwtUtil.getBean("messageFormatsManager")).getScenarioMsgFormatDetailList( "All");
			if(!collMsgFmts.isEmpty()) {

				for (Iterator<ScenarioMessageFormats> it = collMsgFmts.iterator(); it.hasNext();) {
					ScenarioMessageFormats msgFormat = (ScenarioMessageFormats) it.next();
					lstOptions.add(new OptionInfo(msgFormat.getId().getFormatId(), msgFormat.getFormatName(), false));
				}
			}else {
				lstOptions.add(new OptionInfo("", "", false));
			}
			lstSelect.add(new SelectInfo("msgFormatList", lstOptions));
			/***** Msg Formats Combo End ***********/

			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName()
					+ " - [getMsgFormatsList] - Existing");
			return getView("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in ScenMaintenanceAction.'getMsgFormatsList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in ScenMaintenanceAction.'getMsgFormatsList' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getMsgFormatsList", ScenMaintenanceAction.class),
					request, "");
		} finally {
			// nullify objects
		}
		return null;
	}

	public String checkIfInstExist() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String scenarioId = null;
		String isInstExist = null;
		// debug message
		log.debug(this.getClass().getName() + " - [ checkIfInstExist ] - Entry");
		scenarioId = request.getParameter("scenarioId");

		// check if instances exist for a given scenario
		isInstExist = scenMaintenanceManager.checkIfInstExist(scenarioId);
		responseConstructor = new SwtResponseConstructor();
		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "scenMaintenance";
		//xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.startElement("scenMaintenance");
		// forms singleton node
		xmlWriter.startElement(SwtConstants.SINGLETONS);
		responseConstructor.createElement("isInstExist", isInstExist);
		xmlWriter.endElement(SwtConstants.SINGLETONS);
		// end singleton node
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("scenMaintenance");

		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}

	public String configureRecipients() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		//request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("configRecipients");
	}

	public String displayConfigRecipientsData()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the current entity from screen
		String entityId = null;
		// To hold selected currency in screen
		String currencyCode = null;
		Collection<Movement> listPreAdvice = new ArrayList<Movement>();

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;

		try {
			log.debug(this.getClass().getName() + " - [displayConfigRecipientsData()] - Entry");

			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();


			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.CONFIG_RECIPIENTS);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			/*responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());*/
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			// Instance of SchedulerManager
			SchedulerManager schedulerMgr = null;
			// Initialize the scheduler manager
			schedulerMgr = (SchedulerManager) SwtUtil
					.getBean("schedulerManager");
			Collection collRole = schedulerMgr.getRoleList(hostId, null);

			/******* rolesGrid ******/
			responseConstructor.formGridStart("rolesGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns,"rolesGrid", request));
			responseConstructor.formRowsStart(collRole.size());
			for (Iterator<Role> it = collRole.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Role role = (Role) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement("role", role.getRoleId());
				responseConstructor.createRowElement("name", role.getRoleName());
				responseConstructor.createRowElement(SwtConstants.CONFIG_ACCESS, "N");
				responseConstructor.createRowElement(SwtConstants.CONFIG_EMAIL, "N");

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();



			Collection collUser = schedulerMgr.getUserList(hostId, null);

			/******* usersGrid ******/
			responseConstructor.formGridStart("usersGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns,"usersGrid", request));
			responseConstructor.formRowsStart(collUser.size());
			for (Iterator<User> it = collUser.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				User user = (User) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement("user", user.getId().getUserId());
				responseConstructor.createRowElement("name", user.getUserName());
				responseConstructor.createRowElement("role", user.getRoleId());
				responseConstructor.createRowElement(SwtConstants.CONFIG_ACCESS, "N");
				responseConstructor.createRowElement(SwtConstants.CONFIG_EMAIL, "N");

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* otherEmailGrid ******/
			responseConstructor.formGridStart("otherEmailGrid");
			responseConstructor.formColumn(getConfigGridColumns(width, columnOrder, hiddenColumns,"otherEmailGrid", request));
			responseConstructor.formRowsStart(collUser.size());
			//for (Iterator<User> it = collUser.iterator(); it.hasNext();) {
			// Obtain rules definition tag from iterator
			//User user = (User) it.next();
			responseConstructor.formRowStart();
			responseConstructor.createRowElement("emailAddress", "<EMAIL>");
			responseConstructor.createRowElement("description", "email address for user x");
			responseConstructor.createRowElement("send", "N");

			responseConstructor.formRowEnd();
			//}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.CONFIG_RECIPIENTS);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [display()] - Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'display' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			currencyCode = null;
		}
	}


	private List<ColumnInfo> getConfigGridColumns(String width, String columnOrder, String hiddenColumns, String grid, HttpServletRequest request)
			throws SwtException {

		ArrayList<String> orders = null;
		String[] columnOrderProp = null;
		Iterator<String> columnOrderItr = null;
		LinkedHashMap<String, String> widths = null;
		String[] columnWidthProperty = null;
		List<ColumnInfo> lstColumns = null;
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		ArrayList<String> lstHiddenColunms = null;
		String[] hiddenColumnsProp = null;
		ColumnInfo tmpColumnInfo = null;

		try {
			log.debug(this.getClass().getName() + " - [ getConfigGridColumns ] - Entry");
			// Condition to check width is null
			if(grid.equals("rolesGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "role" + "=150,"+ "name" + "=280," + "send" + "=60";

				}
			}
			if(grid.equals("usersGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "user" + "=140," + "name" + "=150," + "role" + "=150," + "send" + "=60";

				}
			}

			if(grid.equals("otherEmailGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "emailAddress" + "=240," + "description" + "=200," + "send" + "=60";

				}
			}

			if(grid.equals("rolesMainGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "roleId" + "=400,"+ "name" + "=480";

				}
			}
			if(grid.equals("usersMainGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "userId" + "=400,"+ "name" + "=480";

				}
			}

			if(grid.equals("otherEmailMainGrid")) {
				if (SwtUtil.isEmptyOrNull(width)) {
					// default width for columns
					width = "emailAddress" + "=400," + "description" + "=480";

				}
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			if(grid.equals("rolesGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "role" + "," + "name" + "," +  "send";
				}
			}
			if(grid.equals("usersGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "user" + "," + "name" + "," + "role" + "," +  "send";
				}
			}

			if(grid.equals("otherEmailGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// default width for columns
					columnOrder = "emailAddress" + "," +  "description" + "," +  "send";

				}
			}

			if(grid.equals("rolesMainGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "roleId" + "," + "name";
				}
			}
			if(grid.equals("usersMainGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// Default values for column order
					columnOrder = "userId" + "," + "name";
				}
			}

			if(grid.equals("otherEmailMainGrid")) {
				if (SwtUtil.isEmptyOrNull(columnOrder)) {
					// default width for columns
					columnOrder = "emailAddress" + "," +  "description";

				}
			}

			orders = new ArrayList<String>();
			columnOrderProp = columnOrder.split(",");

			for (int i = 0; i < columnOrderProp.length; i++) {
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			if (grid.equals("rolesGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("role")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_ROLE, request),"role",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("role")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ROLE_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("name")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_NAME, request), "name",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("name")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.NAME_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}else if (order.equals("send")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_SEND, request), "send",
								PCMConstant.COLUMN_TYPE_CHECK, false, 2, Integer.parseInt(widths.get("send")), false,
								false, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_SEND_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}

				}
			}
			if (grid.equals("usersGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("user")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_USER_ID, request),"user",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("user")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_ID_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("name")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_USER_NAME, request), "name",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("name")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_NAME_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}else if (order.equals("role")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_ROLE, request), "role",
								PCMConstant.COLUMN_TYPE_STRING, true, 2, Integer.parseInt(widths.get("role")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ROLE_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}else if (order.equals("send")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_SEND, request), "send",
								PCMConstant.COLUMN_TYPE_CHECK, false, 2, Integer.parseInt(widths.get("send")), false,
								false, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_SEND_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}

				}
			}
			if (grid.equals("otherEmailGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("emailAddress")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_EMAIL_ADDRESS, request),"emailAddress",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("emailAddress")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_EMAIL_ADDRESS_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("description")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_DESCRIPTION, request), "description",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("description")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_DESCRIPTION_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}else if (order.equals("send")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_SEND, request), "send",
								PCMConstant.COLUMN_TYPE_CHECK, false, 2, Integer.parseInt(widths.get("send")), false,
								false, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_SEND_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}

				}
			}

			if (grid.equals("rolesMainGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("roleId")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_ID, request),"roleId",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("roleId")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ROLE_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("name")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_NAME, request), "name",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("name")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.NAME_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}

				}
			}
			if (grid.equals("usersMainGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("userId")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_ID, request),"userId",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("userId")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_ID_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("name")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_USER_NAME, request), "name",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("name")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.USER_NAME_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}

				}
			}
			if (grid.equals("otherEmailMainGrid")) {
				while (columnOrderItr.hasNext()) {
					String order = (String) columnOrderItr.next();
					if (order.equals("emailAddress")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_EMAIL_ADDRESS, request),"emailAddress",
								PCMConstant.COLUMN_TYPE_STRING, true, 0, Integer.parseInt(widths.get("emailAddress")), false,
								true, true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_EMAIL_ADDRESS_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);
					}else if (order.equals("description")) {
						tmpColumnInfo=(new ColumnInfo(SwtUtil.getMessage(SwtConstants.CONFIG_DESCRIPTION, request), "description",
								PCMConstant.COLUMN_TYPE_STRING, true, 1, Integer.parseInt(widths.get("description")), false, true,
								true));
						tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONFIG_DESCRIPTION_HEADER_TOOLTIP, request));
						lstColumns.add(tmpColumnInfo);

					}

				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Catched in [getConfigGridColumns] method : - "
					+ ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getConfigGridColumns", this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getConfigGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


}
