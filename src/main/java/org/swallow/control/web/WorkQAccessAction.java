/*
 * @(#)WorkQAccessAction .java 26/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.control.web;

import java.util.Collection;
import java.util.ArrayList;
import java.util.Iterator;

import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
////spring Related classes
import org.swallow.util.CacheManager;
import org.swallow.util.SwtUtil;
import org.swallow.control.service.*;
import org.swallow.util.LabelValueBean;






import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/workQAccess", "/workQAccess.do"})
public class WorkQAccessAction extends BaseController {
    private static final Map<String, String> viewMap = new HashMap<>();
    static {
        viewMap.put("add", "jsp/control/workqueueaccessadd");
        viewMap.put("fail", "error");
        viewMap.put("success", "jsp/control/workqueueaccess");
        viewMap.put("change", "jsp/control/workqueueaccessadd");
    }

    private String getView(String resultName) {
        return viewMap.getOrDefault(resultName, "error");
    }

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
    public String execute(@RequestParam(value = "method", required = false) String method,
                          HttpServletRequest request, HttpServletResponse response) throws SwtException {
        method = String.valueOf(method);
        switch (method) {
            case "displayDetails":
                return displayDetails();
            case "displayDetailsByCurrency":
                return displayDetailsByCurrency();
            case "add":
                return add();
            case "change":
                return change();
            case "save":
                return save();
            case "update":
                return update();
            case "delete":
                return delete();
            case "view":
                return view();
            case "displayCurrencyList":
                return displayCurrencyList();
        }


    return null;
}


private WorkQAccess workQAccess;
public WorkQAccess getWorkQAccess() {
    HttpServletRequest request = SwtUtil.getCurrentRequest();
    workQAccess = RequestObjectMapper.getObjectFromRequest(WorkQAccess.class, request, "workQAccess");
    return workQAccess;
}

public void setWorkQAccess(WorkQAccess workQAccess) {
	this.workQAccess = workQAccess;
	HttpServletRequest request = SwtUtil.getCurrentRequest();
	request.setAttribute("workQAccess", workQAccess);
}

	@Autowired
	private WorkQAccessManager wqaMgr = null;
	private final Log log = LogFactory.getLog(WorkQAccessAction.class);
    private ApplicationContext ctx = null;
    private int workQAccessflag=0;

	public void setWorkQAccessManager(WorkQAccessManager wqaMgr) 
	{
	        this.wqaMgr = wqaMgr;
	}
	
	/**
	 * This Function shows WorkQAccess details for a particular role
	 * It also allows to add or change details temporarily
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayDetails()
	throws SwtException
	{
		try
		{	
			log.debug("Entering 'displayDetails' Method");
			
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm)form;
			WorkQAccess wqa=(WorkQAccess)getWorkQAccess();
			
			if(wqa.getId().getCurrencyCode()==null)
				wqa.getId().setCurrencyCode("All");
			
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			wqa.getId().setHostId(hostId);
			setWorkQAccess(wqa);
			log.debug("Inside displayDetails the dynaform is  "+wqa);
			
//			Getting the Entity Drop Down for the WorkQAccess
			String entityIdReturned =  getEntityComboForWorkQAccess(request);
			
			String entityId = wqa.getId().getEntityId();
			if(entityId == null || entityId.equals(""))
				 entityId = entityIdReturned;
			String currencyCode =wqa.getId().getCurrencyCode();
			
			
			/*Getting value of roleMaintenanceOperationFlag to get the name of operation
			 * operation can be "add", "change" ,"copyFrom"
			 */
			String roleMaintenanceOperationFlag = (String)request.getSession().getAttribute("roleMaintenanceOperation");
			log.debug("The name of Operation is"+roleMaintenanceOperationFlag);		
			
			/*If operation is "add"  */
			if(roleMaintenanceOperationFlag.equals("add"))
			{
			/* 	workQAccessFlag checks if user is entering first time in this method */
			 String workQAccessFlag = (String)request.getSession().getAttribute("workQAccessFlag");	
			 log.debug("The value of workQAccessFlag is "+workQAccessflag);
			 
			//If user enters first time in this method then set Attribute sessionWorkQAccessDetails in Session
			
			 	log.debug("The WorkQAccess Flag is 0");
			 	//Collection workQAccessDetails = new ArrayList();
			 	Collection workQAccessDetails = (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
			 	log.debug("Getting from session sessionWorkQAccessDetails workQAccessDetails >> " + workQAccessDetails);
			 	log.debug("entityId >> " + entityId + " currencyCode>> " + currencyCode);
			 	Collection outputCol = selectCollectionByEntity(workQAccessDetails,entityId,currencyCode);
			 	Collection workQAccessDetailsGui = convertWorkQAccessCollection(outputCol);
			 	log.debug("workQAccessDetailsGui >> " + workQAccessDetailsGui);
			 	request.setAttribute("workQAccessDetails",workQAccessDetailsGui);
		 		request.setAttribute("workQAccessDetails",workQAccessDetailsGui);
			 }
			
			/*If operation is "change"  */
			if(roleMaintenanceOperationFlag.equals("change"))
			{
				String roleId=(String)request.getSession().getAttribute("roleIdInSession");
				
				/* 	menuAccessOptionsFlag checks if user is entering first time in this method */
			 String workQAccessFlag=(String)request.getSession().getAttribute("workQAccessFlag");	
			 log.debug("The value of workQAccessFlag is "+workQAccessflag);
			 
			//If user enters first time in this method then setAttribute sessionWorkQAccessDetails in Session
			 if(workQAccessFlag.equals("0")){
			 	log.debug("The WorkQAccess Flag is 0");
			 	putWorkQAccessDetailsInSession(request,hostId,roleId,entityId,currencyCode);
			 	Collection workQAccessDetails= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
			 	Collection coll = selectCollectionByEntity(workQAccessDetails,entityId,currencyCode);
			 	Collection workQAccessDetailsGui = convertWorkQAccessCollection(coll);			 	
			 	request.setAttribute("workQAccessDetails",workQAccessDetailsGui);
			 	request.getSession().setAttribute("workQAccessFlag","1");
			}
			 //If user enters againg then only retrieve the collection sessionWorkQAccessDetails from Session
			 else{			 	
			 	Collection workQAccessDetails= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
			 	if(workQAccessDetails==null){
			 		putWorkQAccessDetailsInSession(request,hostId,roleId,entityId,currencyCode);
			 	}
			 	Collection coll= selectCollectionByEntity(workQAccessDetails,entityId,currencyCode);
			 	Collection workQAccessDetailsGui =convertWorkQAccessCollection(coll);			 	
			 	request.setAttribute("workQAccessDetails",workQAccessDetailsGui);			 	
			}
			} 
			 /*If operation is "copyFrom"  */
			 if(roleMaintenanceOperationFlag.equals("copyFrom"))
			 {
			 	//putWorkQAccessDetailsInSession(request,hostId,entityId,roleId,currencyCode);
			 	log.debug("The name of the operation is copyFrom");
			 	Collection workQAccessDetails= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
			 	log.debug("The sessionWorkQAccessDetails is"+workQAccessDetails);
			 	
			 	Collection coll= selectCollectionByEntity(workQAccessDetails,entityId,currencyCode);
			 	Collection workQAccessDetailsGui =convertWorkQAccessCollection(coll);
			 	log.debug("The workQAccessDetailsGui is"+workQAccessDetailsGui);
			 	request.setAttribute("workQAccessDetails",workQAccessDetailsGui);
			 }
			
			 
			 findCurrencyListByEntity(request,entityId,hostId);
			 
			 //Checking the value of the "isViewRole" flag
			 String isViewRole = request.getParameter("isViewRole");
			 log.debug("The isViewFlag inside displayDetails is ===>"+isViewRole);
			 if(isViewRole != null && isViewRole.equals("yes")){
			 	request.setAttribute("isViewRole","yes");
			 }
			return getView("success");
			
		}catch(Exception e)
		{
			log.debug("Exception Catch"+e); 
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
			return getView("fail");
		}
	}
	/**
	 * This Method puts the  Work Q Access Details in Session
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 */
	public void putWorkQAccessDetailsInSession(HttpServletRequest request,String hostId,String roleId,String entityId,String currencyCode){
		try{
		log.debug("Entering into putWorkQAccessDetailsInSession() ");
		
		//Getting WorkQAccess Details using workQAccess Manager
		//WorkQAccessDetailVO workQAccessDetailsVO= wqaMgr.getCurrencyDetailList(entityId,hostId,roleId,currencyCode); 
		Collection workQAccessDetails=wqaMgr.getDetailsByRoleId(hostId,roleId);
		request.getSession().setAttribute("sessionWorkQAccessDetails",workQAccessDetails);
		log.debug("Inside putWorkQAccessDetailsInSession the details are==> "+workQAccessDetails);
		Collection temp = (ArrayList) SwtUtil.copy(workQAccessDetails);
		request.getSession().setAttribute("sessionWorkQAccessDetailsInitial",temp);			
		
	}catch(Exception e){
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		
	}
}
	
	/**
	 * This Function displays the details filtered after a particular currency is selected
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String displayDetailsByCurrency()
	throws SwtException
	{
		try
		{			
			log.debug("Entering 'displayDetailsByCurrency' Method");
			
			//Getting the dynaForm so as to retrieve currency selected from it
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm)form;								
			WorkQAccess wqa = (WorkQAccess)getWorkQAccess();
			
			log.debug("The currency selected is===>"+wqa.getId().getCurrencyCode());
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			wqa.getId().setHostId(hostId);
			setWorkQAccess(wqa);
			
			String entityId = wqa.getId().getEntityId();
			hostId = wqa.getId().getHostId();
			String currencyCode= wqa.getId().getCurrencyCode();	
			
			//Getting the complete details from collection set in the session
			Collection sessionWorkQAccessDetails = (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
			log.debug("Getting from session sessionWorkQAccessDetails >> " + sessionWorkQAccessDetails);
			
			Collection coll = new ArrayList();
			log.debug("entityId >> " + entityId + " currencyCode  > " + currencyCode);
			if(sessionWorkQAccessDetails != null){
				coll = selectCollectionByEntity(sessionWorkQAccessDetails,entityId,currencyCode);			
				log.debug("Now finding the workQAccessGui");		
				Collection collWorkQAccess = convertWorkQAccessCollection(coll);
				log.debug("The work Q Access Collection is ====>"+collWorkQAccess);		
				request.setAttribute("workQAccessDetails",collWorkQAccess);
			}
			else{
				request.setAttribute("workQAccessDetails",new ArrayList());
			}
			
			getEntityComboForWorkQAccess(request);
			
			/**This function provides the currency dropdown with only those currency codes present which
			 * are present corresponding to a particular entityId in the S_CURRENCY table
			 */
			
			findCurrencyListByEntity(request,entityId,hostId);
			
			//Checking the status of "isViewRole" flag so as to set visibility of different buttons on the screens
			String isViewRole = request.getParameter("isViewRole");
			log.debug("The isViewFlag inside displayDetails is ===>"+isViewRole);
			if(isViewRole != null && isViewRole.equals("yes")){
				request.setAttribute("isViewRole","yes");
			}
			
			log.debug("Exiting 'displayDetailsByCurrency' Method");
			return getView("success");		
		}catch(Exception e)
		{
			log.debug("Exception Catch"+e); 
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
			return getView("fail");
		}
	}
	
	/**
	 * This function opens the workqueueaccessadd.jsp
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	
	public String add()
	throws SwtException
	{
	try
	{
		log.debug("Entering 'Work Queue Access Add' Method");
		
		//"screenFieldsStatus"==> sets the editablity of different fields on the screen
		request.setAttribute("screenFieldsStatus","false");
		
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		WorkQAccess workQAccess =(WorkQAccess)getWorkQAccess();
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		String entityId =   request.getParameter("entityCode");
		String currencyCode = request.getParameter("currencyCode");
		String entityName = request.getParameter("entityName");
		log.debug("The parameters got inside add method is=="+entityName+"=="+entityId+"=="+currencyCode);
		
		WorkQAccess wqa = new WorkQAccess();
		wqa.getId().setEntityId(entityId);
		wqa.getId().setCurrencyCode(currencyCode);
		setWorkQAccess(wqa);
		request.setAttribute("entityName",entityName);
		request.setAttribute("methodName", "save");	
		getEntityComboForWorkQAccess(request);
		findCurrencyListByEntityWithoutAll(request,entityId,hostId);
		
		request.setAttribute("methodName", "save");
		
		log.debug("Exiting 'wqaadd' Method");			
		return getView("add");		
	}catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		return getView("fail");
	}
	}

	/**
	 * This function opens the changeworkqueueaccess window with details previously populated
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String change()
	throws SwtException 
	{
	try
	{
		log.debug("Entering 'Work Queue Access change' Method");
		
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		request.setAttribute("screenFieldsStatus","false");
		//Getting the entityId and currencyCode for the selected row whose Work Queue Access is to be changed
		String currencyCode=request.getParameter("selectedCurrencyCode");
		if(currencyCode != null)
			currencyCode = currencyCode.trim();
		String entityId= request.getParameter("entityCode");
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		
		log.debug("The row selected is");
		log.debug("The currencycode is"+currencyCode);
		log.debug("The entityId is"+entityId);
		
		//Getting the already existing details 
		Collection sessionWorkQAccessDetails= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
		log.debug("Getting the sessionWorkQAccessDetails "+sessionWorkQAccessDetails);
		
		/**Setting dynaForm with that object in the sessionWorkQAccessDetails whose entityId and currencyCode matches
		 * with the selected currencyCode and entityId
		 */ 
		Iterator itr=sessionWorkQAccessDetails.iterator();
		while(itr.hasNext()){
			WorkQAccess wqa= (WorkQAccess)(itr.next());
			log.debug("Iterating the sessionWorkQAccessDetails in change method"+wqa);
			if(wqa.getId().getCurrencyCode().equals(currencyCode) && wqa.getId().getEntityId().equals(entityId) && wqa.getId().getHostId().equals(hostId))
			{
				log.debug("The entry to be changed is"+wqa);
				setWorkQAccess(wqa);
			}				
		}
		
		String entityName = request.getParameter("entityName");
		
		request.setAttribute("entityName",entityName);
		request.setAttribute("methodName", "update");	
		
		//setting the Dropdown for entity
		getEntityComboForWorkQAccess(request);
		//Setting the text Box for currency
		WorkQAccessDetailVO workQAccessDetailsVO= wqaMgr.getCurrencyDetailList(entityId,hostId,"All","All");
		Collection currencyMaster =workQAccessDetailsVO.getCurrencyList();
		if(currencyMaster != null)
		{
		 	Iterator itrCurrency = currencyMaster.iterator();
			while(itrCurrency.hasNext()){
				LabelValueBean lvb = (LabelValueBean)(itrCurrency.next());
				if(lvb.getValue().equals(currencyCode))
						{
					      request.setAttribute("selectedCurrencyName",lvb.getLabel());
						}
			}
		}
		//request.setAttribute("currencyMaster",currencyMaster);
		
		log.debug("Exiting 'Work Queue Access change' Method");
					
		return getView("add");		
	}catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		return getView("fail");
	}
	}

	
	/**
	 * This function saves an entry into P_WORKQ_ACCESS table in the database.
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String save()
	throws SwtException 
	{
	try
	{
		log.debug("Entering save method  of Work Queue Access Action");
				
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		WorkQAccess workQAccess = (WorkQAccess)getWorkQAccess();
		log.debug("The DynaForm Collection is"+workQAccess.toString());
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		workQAccess.getId().setHostId(hostId);		
		
		String entityId =workQAccess.getId().getEntityId();
		String currencyCode =workQAccess.getId().getCurrencyCode();
		
		boolean isPresent= false;//This flag checks if the entry to be saved already exists in the temporary collection or not
		
		//Making a new collection and adding to it the rows of previous collection and also the new entry added
		Collection sessionWorkQAccessDetailsNew =new ArrayList();
		if(entityId!=null && currencyCode!=null && !(entityId.equals("")) && !(currencyCode.equals("")))
		{
		log.debug("checking if the entry already exists");	
		log.debug("Initially new Collection is"+sessionWorkQAccessDetailsNew);
		Collection sessionWorkQAccessDetailsOld= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
		log.debug("after getting sessionWorkQAccessDetailsOld"+sessionWorkQAccessDetailsOld);
		
		if(sessionWorkQAccessDetailsOld != null){
		log.debug("sessionWorkQAccessDetailsOld is"+sessionWorkQAccessDetailsOld);
		Iterator itr=sessionWorkQAccessDetailsOld.iterator();
			while(itr.hasNext()){
				WorkQAccess wqa=(WorkQAccess)(itr.next());
				log.debug("The  row of the iterator is "+wqa);
	
				if(wqa.getId().getEntityId().equals(entityId)&& wqa.getId().getCurrencyCode().equals(currencyCode))
				{
				    log.debug("The row already exists");
					isPresent=true;
					sessionWorkQAccessDetailsNew.add(wqa);
					log.debug("after adding new row to new collection the sessionWorkQAccessDetailsNew is"+sessionWorkQAccessDetailsNew);
				}
				else{
					log.debug("This row doesnot exists so adding this row to the new collection");
					sessionWorkQAccessDetailsNew.add(wqa);
				}
			}
			
			if(!isPresent)
			{
				log.debug("The value of isPresent flag is"+isPresent);
				log.debug("Adding new row to the session Collection");
				sessionWorkQAccessDetailsNew.add(workQAccess);
				isPresent=false;
				log.debug("The sessionWorkQAccessDetails===========> is"+sessionWorkQAccessDetailsNew);
				request.setAttribute("parentFormRefresh","yes");
			}	
			
			if(isPresent)
			{
				request.setAttribute("queueAccessExiting","yes");
			}
		}
		else{			
			log.debug("Adding first row to the session Collection");
			sessionWorkQAccessDetailsNew.add(workQAccess);
			log.debug("The sessionWorkQAccessDetailsNew===========> is"+sessionWorkQAccessDetailsNew);
			Collection workQAccessDetailsGui =convertWorkQAccessCollection(sessionWorkQAccessDetailsNew);
			//request.getSession().setAttribute("sessionWorkQAccessDetails",sessionWorkQAccessDetailsNew);
			request.setAttribute("workQAccessDetails",workQAccessDetailsGui);
			log.debug("The value of workQAccessDetails is "+workQAccessDetailsGui);	
			request.setAttribute("parentFormRefresh","yes");
			}
		request.getSession().setAttribute("sessionWorkQAccessDetails",sessionWorkQAccessDetailsNew);
		}
		
		request.setAttribute("methodName","save");
		
		log.debug("Exiting 'save' Method");	
		
		//Getting entityDropDown
		getEntityComboForWorkQAccess(request);
		//Getting currencyDropDown
		findCurrencyListByEntity(request,entityId,hostId);
		return getView("add");		
		
	}
	catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);	
		return getView("fail");
	}
}
	
	/**This method temporarily updates the WorkQAccess details
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	
	public String update()
	throws SwtException 
	{
	try
	{
		log.debug("Entering 'Work Queue Access update' Method");
		
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		WorkQAccess workQAccess = (WorkQAccess)getWorkQAccess();
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		Collection sessionWorkQAccessDetailsNew = new ArrayList();
		workQAccess.getId().setHostId(hostId);
		
		log.debug("For updation the entry in the dynaform is "+workQAccess);
		
		String entityId=workQAccess.getId().getEntityId();
		String currencyCode = workQAccess.getId().getCurrencyCode();
				
		Collection sessionWorkQAccessDetailsOld= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
		log.debug("Getting the sessionWorkQAccessDetails "+sessionWorkQAccessDetailsOld);
		
		Iterator itr=sessionWorkQAccessDetailsOld.iterator();
		while(itr.hasNext()){
			WorkQAccess wqa= (WorkQAccess)(itr.next());
			
			if(wqa.getId().getCurrencyCode().equals(currencyCode) && wqa.getId().getEntityId().equals(entityId) && wqa.getId().getHostId().equals(hostId))
			{
			  log.debug("The entry to be updated is"+wqa);
			  sessionWorkQAccessDetailsNew.add(workQAccess);
			  log.debug("The updated entry now is"+workQAccess);
				
			}else{
				sessionWorkQAccessDetailsNew.add(wqa);
			}
				
		}
		request.getSession().setAttribute("sessionWorkQAccessDetails",sessionWorkQAccessDetailsNew);
		
		request.setAttribute("methodName","update");
		request.setAttribute("parentFormRefresh","yes");
		
		
		getEntityComboForWorkQAccess(request);
		findCurrencyListByEntity(request,entityId,hostId);
		
		log.debug("Exiting 'Work Queue Access update' Method");
					
		return getView("add");		
	}catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		return getView("fail");
	}
	}
/**
 * This method temporarily deletes the WorkQAccess entry selected to be deleted
 * @param mapping
 * @param form
 * @param request
 * @param response
 * @return
 * @throws SwtException
 */
	public String delete()
	throws SwtException 
	{
	try
	{
		log.debug("Entering 'Work Queue Access delete' Method");
		
		
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		WorkQAccess workQAccess = (WorkQAccess)getWorkQAccess();
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		
		String currencyCode=request.getParameter("selectedCurrencyCode");
		String entityId= request.getParameter("entityCode");
		Collection sessionWorkQAccessDetailsNew = new ArrayList();
		
		log.debug("inside delete method the entry to be deleted is");
		log.debug("entityId is=====>"+entityId+"The currencyCode is====>"+currencyCode);
		
		workQAccess.getId().setHostId(hostId);
		workQAccess.getId().setCurrencyCode(currencyCode);
		workQAccess.getId().setEntityId(entityId);
		
		Collection sessionWorkQAccessDetailsOld= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
		log.debug("Getting the sessionWorkQAccessDetails "+sessionWorkQAccessDetailsOld);
		
		Iterator itr=sessionWorkQAccessDetailsOld.iterator();
		while(itr.hasNext()){
			WorkQAccess wqa= (WorkQAccess)(itr.next());
			
			if(wqa.getId().getCurrencyCode().equals(currencyCode) && wqa.getId().getEntityId().equals(entityId) && wqa.getId().getHostId().equals(hostId))
			{
			  log.debug("The entry to be deleted is"+wqa);
			  wqa.getId().setCurrencyCode("All");//after deleting all three rows the parent form should show all currencies corresponding to a particular entity;
			  setWorkQAccess(wqa);
				
			}else{
				sessionWorkQAccessDetailsNew.add(wqa);
			}
				
		}
		request.getSession().setAttribute("sessionWorkQAccessDetails",sessionWorkQAccessDetailsNew);
		log.debug("After deletion the WorkQAccess sessionWorkQAccessDetails is"+sessionWorkQAccessDetailsNew);
		getEntityComboForWorkQAccess(request);
		findCurrencyListByEntity(request,entityId,hostId);
		
		log.debug("Exiting 'Work Queue Access delete' Method");
					
		return 	displayDetails();	
	}catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		return getView("fail");
	}
	}

	/**
	 * This function filters the WorkQAccess details collection on the basis of currencyCode and entityId
	 * @param input
	 * @param entityId
	 * @param currencyCode
	 * @return
	 */
	
	public Collection selectCollectionByEntity(Collection input,String entityId,String currencyCode){
		
		log.debug("inside selectCollectionByEntity()");
		log.debug("The input collection to  selectCollectionByEntity() is" + input);
		log.debug("the input entityId is" + entityId);
		log.debug("The input currencyCode is" + currencyCode);
		Collection output = new ArrayList();
	    if(input!=null)
	    	{
	    		Iterator itr =input.iterator();
	
	    		while(itr.hasNext()){
	    			WorkQAccess workQAccess = (WorkQAccess)(itr.next());
				if(currencyCode.equalsIgnoreCase("All"))
				{
					if(workQAccess.getId().getEntityId().equals(entityId))
						output.add(workQAccess);
				}
				else 
					if(workQAccess.getId().getEntityId().equals(entityId) && workQAccess.getId().getCurrencyCode().equals(currencyCode))
						output.add(workQAccess);
			}		
	    }
	   log.debug("the selectCollectionByEntity give output >> "+output);
		return output;
	}		
	
	/**
	 * This function converts the WorkQAccess details collection from the database into the Gui collection
	 * @param input
	 * @return
	 */
	private Collection convertWorkQAccessCollection(Collection input)
	{
		log.debug("inside convertWorkQAccessCollection()  method >>>>>"+input);
		Collection output = new ArrayList();
		
		if(input.size() > 0){
		Iterator itr = input.iterator();
		//int identifier=0;
		
	  
		while(itr.hasNext())
		{
			
			WorkQAccess workQAccess = (WorkQAccess)(itr.next());
			log.debug("the first Collection from iterator is"+workQAccess);
			
			String entityId=workQAccess.getId().getEntityId();	
			String currencyCode = workQAccess.getId().getCurrencyCode();
			String offerMatchStatus[]=new String[5];
			String suspMatchStatus[]=new String[6];
			String confirmMatchStatus[]=new String[6];
			
			log.debug("After first assignment");
			
			offerMatchStatus[0]=workQAccess.getOfferMatchQualA();
			offerMatchStatus[1]=workQAccess.getOfferMatchQualB();
			offerMatchStatus[2]=workQAccess.getOfferMatchQualC();
			offerMatchStatus[3]=workQAccess.getOfferMatchQualD();
			offerMatchStatus[4]=workQAccess.getOfferMatchQualE();
			
			log.debug("After second assignment");
			
			suspMatchStatus[0]=workQAccess.getSuspMatchQualA();
			suspMatchStatus[1]=workQAccess.getSuspMatchQualB();
			suspMatchStatus[2]=workQAccess.getSuspMatchQualC();
			suspMatchStatus[3]=workQAccess.getSuspMatchQualD();
			suspMatchStatus[4]=workQAccess.getSuspMatchQualE();
			suspMatchStatus[5]=workQAccess.getSuspMatchQualZ();
			
			log.debug("After third assignment");
			
			confirmMatchStatus[0]=workQAccess.getConfirmMatchQualA();
			confirmMatchStatus[1]=workQAccess.getConfirmMatchQualB();
			confirmMatchStatus[2]=workQAccess.getConfirmMatchQualC();
			confirmMatchStatus[3]=workQAccess.getConfirmMatchQualD();
			confirmMatchStatus[4]=workQAccess.getConfirmMatchQualE();
			confirmMatchStatus[5]=workQAccess.getConfirmMatchQualZ();
			
			log.debug("After fourth assignment");
			
			String outStanding=workQAccess.getOutstanding();

			log.debug("after first check");
			
//			int j=0;
//			for(int i=0;i<5;i++){
//			if(offerMatchStatus[i]==null){
//			if(offerMatchStatus[i].equalsIgnoreCase("y")||offerMatchStatus[i].equalsIgnoreCase("on")){
//			//workQAccessGuiObject.setMatchStatus("OFF");
//			j=1;
//			break;
//			}
//			}
//			}
//			if(j==1){
			
			for(int i=0;i<5;i++)
			 {
				if(offerMatchStatus[i] == null)
					offerMatchStatus[i] = "";
					
			 }
					
			
			for(int i=0;i<6;i++)
			 {
				if(suspMatchStatus[i] == null)
					suspMatchStatus[i] = "";
				
				if(confirmMatchStatus[i] == null)
					confirmMatchStatus[i] = "";
					
			 }
			
			if( outStanding == null)
				outStanding ="";
				
				WorkQAccessGui  workQAccessGuiObject = new WorkQAccessGui();
				workQAccessGuiObject.setCurrencyCode(currencyCode);
				workQAccessGuiObject.setMatchStatus("Offered");
				
				if(offerMatchStatus[0]!=null){
			    if(offerMatchStatus[0].equalsIgnoreCase("y")||offerMatchStatus[0].equalsIgnoreCase("on"))
			    	workQAccessGuiObject.setQualA("Y");
			    else
			    	workQAccessGuiObject.setQualA("");
			    	
				}
			    
			    if(offerMatchStatus[1]!=null){
				if(offerMatchStatus[1].equalsIgnoreCase("y")||offerMatchStatus[1].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualB("Y");
				 else
			    	workQAccessGuiObject.setQualB("");
			    }
				
				if(offerMatchStatus[2]!=null){
				if(offerMatchStatus[2].equalsIgnoreCase("y")||offerMatchStatus[2].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualC("Y");
				 else
			    	workQAccessGuiObject.setQualC("");
				}
				
				if(offerMatchStatus[3]!=null){
				if(offerMatchStatus[3].equalsIgnoreCase("y")||offerMatchStatus[3].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualD("Y");
				 else
			    	workQAccessGuiObject.setQualD("");
				}
				
				if(offerMatchStatus[4]!=null){
				if(offerMatchStatus[4].equalsIgnoreCase("y")||offerMatchStatus[4].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualE("Y");
				 else
			    	workQAccessGuiObject.setQualE("");
				}
				
				workQAccessGuiObject.setQualZ("");
				workQAccessGuiObject.setOutstanding("");
				output.add(workQAccessGuiObject);
//				j=0;
					
//			 }
			
			
			log.debug("after second check");
			
//			for(int i=0;i<6;i++){
//				if(suspMatchStatus[i]!=null){
//				if(suspMatchStatus[i].equalsIgnoreCase("Y")||suspMatchStatus[i].equalsIgnoreCase("on")){
//				//workQAccessGuiObject.setMatchStatus("SUS");
//				j=1;
//				break;
//				}
//				}
//			}
	//			if(j==1){
					
					//WorkQAccessGui  workQAccessGuiObject = new WorkQAccessGui();
			 		workQAccessGuiObject = new WorkQAccessGui();
					workQAccessGuiObject.setCurrencyCode(currencyCode);
					workQAccessGuiObject.setMatchStatus("Suspended");
					
					
					if(suspMatchStatus[0]!=null){
					if(suspMatchStatus[0].equalsIgnoreCase("Y")||suspMatchStatus[0].equalsIgnoreCase("on"))
						workQAccessGuiObject.setQualA("Y");
					else
						workQAccessGuiObject.setQualA("");
					}
					
					if(suspMatchStatus[1]!=null){
					if(suspMatchStatus[1].equalsIgnoreCase("y")||suspMatchStatus[1].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualB("Y");
					else
						workQAccessGuiObject.setQualB("");
					}
					
					if(suspMatchStatus[2]!=null){
					if(suspMatchStatus[2].equalsIgnoreCase("y")||suspMatchStatus[2].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualC("Y");
					else
						workQAccessGuiObject.setQualC("");
					}
					
					if(suspMatchStatus[3]!=null){
					if(suspMatchStatus[3].equalsIgnoreCase("y")||suspMatchStatus[3].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualD("Y");
					else
						workQAccessGuiObject.setQualD("");
					}
					
					if(suspMatchStatus[4]!=null){
					if(suspMatchStatus[4].equalsIgnoreCase("y")||suspMatchStatus[4].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualE("Y");
					else
						workQAccessGuiObject.setQualE("");
					}
					
					if(suspMatchStatus[5]!=null){
					if(suspMatchStatus[5].equalsIgnoreCase("y")||suspMatchStatus[5].equalsIgnoreCase("on"))
					workQAccessGuiObject.setQualZ("Y");
					else
						workQAccessGuiObject.setQualZ("");
					}
					
					workQAccessGuiObject.setOutstanding("");
				/*	if(outstanding!=null){
					if(outstanding.equalsIgnoreCase("y"))
						workQAccessGuiObject.setOutstanding("y");
					}*/
					
					output.add(workQAccessGuiObject);
				//	j=0;
						
				// }
				
				log.debug("after third check");
		
//				for(int i=0;i<6;i++){
//					if(confirmMatchStatus[i]!=null){
//					if(confirmMatchStatus[i].equalsIgnoreCase("y")||confirmMatchStatus[i].equalsIgnoreCase("on")){
//					//workQAccessGuiObject.setMatchStatus("SUS");
//					j=1;
//					break;
//					}
//					}
//					}
				//	if(j==1){
						
						//WorkQAccessGui  workQAccessGuiObject = new WorkQAccessGui();
						workQAccessGuiObject = new WorkQAccessGui();
						workQAccessGuiObject.setCurrencyCode(currencyCode);
						workQAccessGuiObject.setMatchStatus("Confirmed");
						
						
						if(confirmMatchStatus[0]!=null){
					    if(confirmMatchStatus[0].equalsIgnoreCase("y")||confirmMatchStatus[0].equalsIgnoreCase("on"))
					    	workQAccessGuiObject.setQualA("Y");
					    else
					    	workQAccessGuiObject.setQualA("");
						}
					    
					    if(confirmMatchStatus[1]!=null){
						if(confirmMatchStatus[1].equalsIgnoreCase("y")||confirmMatchStatus[1].equalsIgnoreCase("on"))
						workQAccessGuiObject.setQualB("Y");
						 else
					    	workQAccessGuiObject.setQualB("");
					    }
						
						if(confirmMatchStatus[2]!=null){
						if(confirmMatchStatus[2].equalsIgnoreCase("y")||confirmMatchStatus[2].equalsIgnoreCase("on"))
							workQAccessGuiObject.setQualC("Y");
						 else
					    	workQAccessGuiObject.setQualC("");
						}
						
						if(confirmMatchStatus[3]!=null){
						if(confirmMatchStatus[3].equalsIgnoreCase("y")||confirmMatchStatus[3].equalsIgnoreCase("on"))
							workQAccessGuiObject.setQualD("Y");
						 else
					    	workQAccessGuiObject.setQualD("");
						}
						
						if(confirmMatchStatus[4]!=null){
						if(confirmMatchStatus[4].equalsIgnoreCase("y")||confirmMatchStatus[4].equalsIgnoreCase("on"))
							workQAccessGuiObject.setQualE("Y");
						 else
					    	workQAccessGuiObject.setQualE("");
						}
						
						if(confirmMatchStatus[5]!=null){
						if(confirmMatchStatus[5].equalsIgnoreCase("y")||confirmMatchStatus[5].equalsIgnoreCase("on"))
							workQAccessGuiObject.setQualZ("Y");
						 else
					    	workQAccessGuiObject.setQualZ("");
						}
						
						workQAccessGuiObject.setOutstanding("");
					/*	if(outstanding!=null){
						if(outstanding.equalsIgnoreCase("y"))
							workQAccessGuiObject.setOutstanding("y");
						}*/
						
						output.add(workQAccessGuiObject);
							
					// }
					
					//if((outstanding!=null) && !(outstanding.equalsIgnoreCase("N")) ){
						//WorkQAccessGui  workQAccessGuiObject = new WorkQAccessGui();
						 workQAccessGuiObject = new WorkQAccessGui();
						workQAccessGuiObject.setCurrencyCode(currencyCode);
						workQAccessGuiObject.setMatchStatus("Outstanding");
						if( outStanding != null && outStanding.equalsIgnoreCase("y"))
							workQAccessGuiObject.setOutstanding("Y");
						 else
						 	workQAccessGuiObject.setOutstanding("");
						workQAccessGuiObject.setQualA("");
						workQAccessGuiObject.setQualB("");
						workQAccessGuiObject.setQualC("");
						workQAccessGuiObject.setQualD("");
						workQAccessGuiObject.setQualE("");
						workQAccessGuiObject.setQualZ("");
						output.add(workQAccessGuiObject);
					
					
	
			}
		}
		log.debug("The WorkQAccess Collection converted to GUI is ==>"+output);
		return output;
	}
	
	/**
	 * This function opens the WorkQAccess details for a particular currencyCode and entityId in the viewOnly mode
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String view()
	throws SwtException 
	{
	try
	{		
		log.debug("Entering 'Work Queue Access view' Method");
		
		request.setAttribute("screenFieldsStatus","true");
		String currencyCode=request.getParameter("selectedCurrencyCode");
		String entityId= request.getParameter("entityCode");
		
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		
		log.debug("The row selected is");
		log.debug("The currencycode is"+currencyCode);
		log.debug("The entityId is"+entityId);
		
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm)form;
		Collection sessionWorkQAccessDetails= (Collection)request.getSession().getAttribute("sessionWorkQAccessDetails");
		log.debug("Getting the sessionWorkQAccessDetails "+sessionWorkQAccessDetails);
		
		Iterator itr=sessionWorkQAccessDetails.iterator();
		while(itr.hasNext()){
			WorkQAccess wqa= (WorkQAccess)(itr.next());
			log.debug("Iterating the sessionWorkQAccessDetails in view method"+wqa);
			if(wqa.getId().getCurrencyCode().equals(currencyCode) && wqa.getId().getEntityId().equals(entityId) && wqa.getId().getHostId().equals(hostId))
			{
			  log.debug("The entry to be viewed is"+wqa);
				setWorkQAccess(wqa);
				
			}
				
		}
		
		request.setAttribute("methodName", "view");	
		request.setAttribute("viewButtonStatus","view");
		
		String entityName = request.getParameter("entityName");		
		request.setAttribute("entityName",entityName);
		
		getEntityComboForWorkQAccess(request);
		//findCurrencyListByEntity(request,entityId,hostId);
//		Setting the text Box for currency
		WorkQAccessDetailVO workQAccessDetailsVO= wqaMgr.getCurrencyDetailList(entityId,hostId,"All","All");
		Collection currencyMaster =workQAccessDetailsVO.getCurrencyList();
		if(currencyMaster != null)
		{
		 	Iterator itrCurrency = currencyMaster.iterator();
			while(itrCurrency.hasNext()){
				LabelValueBean lvb = (LabelValueBean)(itrCurrency.next());
				if(lvb.getValue().equals(currencyCode))
						{
					       request.setAttribute("selectedCurrencyName",lvb.getLabel());
						}
			}
		}
		log.debug("Exiting 'Work Queue Access view' Method");
					
		return getView("add");		
	}catch(Exception e)
	{
		log.debug("Exception Catch"+e); 
		e.printStackTrace();
		SystemExceptionHandler.logError(e);
		return getView("fail");
	}
	}

	/**
	 * This function creates LabelValueBeans for the Entity Drop Down in WorkQAccess
	 * @param request
	 */
		private String getEntityComboForWorkQAccess(HttpServletRequest request)
	{
		Collection entityComboForWorkQueueAccess= (Collection)(request.getSession().getAttribute("entityComboForWorkQueueAccess"));
		log.debug("Entering into getEntityComboForWorkQAccess()");		
		log.debug(entityComboForWorkQueueAccess);
		int i=0;
		String entityId = null;
//		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
//		log.debug("The list of the user entities are ===============>"+coll);
		
		
		ArrayList entitiesList = new ArrayList();
		Iterator itr = (entityComboForWorkQueueAccess).iterator(); 		
		while(itr.hasNext())
			{
			EntityAccessGui entList =(EntityAccessGui)(itr.next());
			entitiesList.add(new LabelValueBean(entList.getEntityName(),entList.getEntityId()));
			if(i==0)
				entityId = entList.getEntityId();
			i++;
			}
		log.debug("The entityDropDown is ==>"+entitiesList);
		request.setAttribute("entities",entitiesList);
		log.debug("The entity returned by the getEntityComboForWorkQAccess method is ==>"+entityId);
		return entityId;
	}
		
		/**
		 * This function finds the currency dropdown according to the entityId
		 * @param request
		 * @param entityId
		 * @param hostId
		 * @throws SwtException
		 */
		public  void findCurrencyListByEntity (HttpServletRequest request,String entityId,String hostId) throws SwtException
		{
			log.debug("Entering 'findCurrencyListByEntity' Method");
		
			CurrencyManager currencyManagerObj = (CurrencyManager)(SwtUtil.getBean("currencyManager"));
			CurrencyDetailVO currencyDetailVOObj =  currencyManagerObj. getCurrencyDetailListWithAll (entityId, hostId,"All");
			log.debug("The currency drop down is========>"
					+ currencyDetailVOObj.getCurrencyList());
			request.setAttribute("currencyMaster",currencyDetailVOObj.getCurrencyList());	
			log.debug("Exiting 'findCurrencyListByEntity' Method");
			
		}
		
		/**
		 * This function finds the currency dropdown according to the entityId
		 * @param request
		 * @param entityId
		 * @param hostId
		 * @throws SwtException
		 */
		public  void findCurrencyListByEntityWithoutAll (HttpServletRequest request,String entityId,String hostId) throws SwtException
		{
			log.debug("Entering 'findCurrencyListByEntity' Method");		
			CurrencyManager currencyManagerObj = (CurrencyManager)(SwtUtil.getBean("currencyManager"));
			CurrencyDetailVO currencyDetailVOObj =  currencyManagerObj. getCurrencyDetailListWithAll (entityId, hostId,"All");
			log.debug("The currency drop down is========>"
					+ currencyDetailVOObj.getCurrencyList());
			
			Collection currencyMaster = new ArrayList();
			currencyMaster.add(new LabelValueBean("",""));
			if(currencyDetailVOObj.getCurrencyList() != null)
			{
				Iterator itr = currencyDetailVOObj.getCurrencyList().iterator();
				while(itr.hasNext())
				{
					LabelValueBean lvb = (LabelValueBean)(itr.next());
					if(!(lvb.getLabel().equalsIgnoreCase("All")))
						currencyMaster.add(lvb);
						
					
				}
				request.setAttribute("currencyMaster",currencyMaster);	
				log.debug("Exiting 'findCurrencyListByEntity' Method");
			}
			
		}
		
		
		/**This function finds the currency available to a particular entity in the workqueueaccessadd.jsp
		 * 
		 * @param mapping
		 * @param form
		 * @param request
		 * @param response
		 * @return
		 * @throws SwtException
		 */
		public String displayCurrencyList()
		throws SwtException 
		{
		try
		{
			
			log.debug("Entering 'displayCurrencyList' Method");
			
			
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm)form;
			WorkQAccess workQAccess = (WorkQAccess)getWorkQAccess();
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();
			String entityId=workQAccess.getId().getEntityId();
			
			getEntityComboForWorkQAccess(request);
			WorkQAccessDetailVO workQAccessDetailsVO= wqaMgr.getCurrencyDetailList(entityId,hostId,"All","All");
			Collection currencyMaster =workQAccessDetailsVO.getCurrencyList();
			currencyMaster.remove(new LabelValueBean("All","All"));
			
			Collection currencyMasterNew =new ArrayList();
			currencyMasterNew.add(new LabelValueBean("",""));
			currencyMasterNew.addAll(currencyMaster);
			
			request.setAttribute("currencyMaster",currencyMasterNew);
			
			request.setAttribute("methodName", "save");	
			
			log.debug("Exiting 'displayCurrencyList' Method");
						
			return getView("add");		
		}catch(Exception e)
		{
			log.debug("Exception Catch"+e); 
			e.printStackTrace();
			SystemExceptionHandler.logError(e);
			return getView("fail");
		}
		}

	
}