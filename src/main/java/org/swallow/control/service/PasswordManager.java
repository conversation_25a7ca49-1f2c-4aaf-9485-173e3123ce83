/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service;
import org.swallow.control.dao.*;
import org.swallow.control.model.Password;
import org.swallow.maintenance.model.*;
import org.swallow.util.SystemInfo;
import org.swallow.exception.*;

import java.util.Collection;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface PasswordManager {
	
	/*
	 * Created on Nov 4, 2005
	 *
	 * TODO To change the template for this generated file go to
	 * Window - Preferences - Java - Code Style - Code Templates
	 */

	/**
	 * @param passwordDAO
	 */
	public void setPasswordDAO(PasswordDAO passwordDAO);	
	/**
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection  getPasswordRules(String hostId) throws SwtException;
	/**
	 * @param rwdRules
	 * @throws SwtException
	 */
	public void  updatePasswordRules(Password rwdRules,SystemInfo systemInfo) throws SwtException;
		
	}


