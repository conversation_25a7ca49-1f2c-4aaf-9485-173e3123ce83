package org.swallow.maintenance.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 
 * This util class contains the general page details required for pagination  
 * 
 */
public class InterfaceRulesPageDetails implements Serializable{
	
	private int pageValue;

	private int currentPageNo;

	private int maxPages;

	private Map pageDetails;

	public int getCurrentPageNo() {
		return currentPageNo;
	}

	public void setCurrentPageNo(int currentPageNo) {
		this.currentPageNo = currentPageNo;
	}

	public int getMaxPages() {
		return maxPages;
	}

	public void setMaxPages(int maxPages) {
		this.maxPages = maxPages;
	}

	public int getPageValue() {
		return pageValue;
	}

	public void setPageValue(int pageValue) {
		this.pageValue = pageValue;
	}

	public Map getPageDetails() {
		pageDetails = new HashMap();
		pageDetails.put("pageNoValue", Integer.toString(pageValue));
		pageDetails.put("currentPage", Integer.toString(currentPageNo));
		pageDetails.put("maxPages", Integer.toString(maxPages));
		return pageDetails;
	}

	public void setPageDetails(Map pageDetails) {
		this.pageDetails = pageDetails;
	}
}