<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
   <class name="org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance" table="P_CCY_ACC_MAINT_PERIOD" >
   
	   <composite-id class="org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance$Id" name="id">
	       <key-property name="accountId" column="ACCOUNT_ID" access="field"></key-property>
	       <key-property name="entityId" column="ENTITY_ID" access="field"></key-property>
	       <key-property name="hostId" column="HOST_ID" access="field"></key-property>
	       <key-property name="startDate" column="START_DATE" access="field"></key-property>
       </composite-id>

       <property name="endDate" not-null="false" column="END_DATE"  />
       <property name="minimumReserve" not-null="false" column="MINIMUM_RESERVE"  />
       <property name="tier" not-null="false" column="TIER"  />
       <property name="targetAvgBalance" not-null="false" column="TARGET_AVG_BALANCE"  />
       <property name="fillDays" not-null="false" column="FILL_DAYS"  />
       <property name="fillBalance" not-null="false" column="FILL_BALANCE"  />
       <property name="eofBalanceSource" not-null="false" column="PRIOR_EOD_BAL_SOURCE"  />
       <property name="minTargetBalance" not-null="false" column="MINIMUM_TARGET_BALANCE"  />
       <property name="excludeFillPeriod" not-null="false" column="EXCLUDE_FILL_DAYS"  />
       
       

 </class>

</hibernate-mapping>