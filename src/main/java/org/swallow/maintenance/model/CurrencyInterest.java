/*
 * @(#)CurrencyInterest.java 03/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 *
 * 
 * This class is used as model class for populating data from
 * S_CURRENCY_INTEREST_RATE.
 *  
 */
public class CurrencyInterest  extends BaseObject implements org.swallow.model.AuditComponent {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * To maintain maintenance log
	 */
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	static {
		logTable.put("interestRate", "Interest Rate");
		logTable.put("interestRateDate", "interestRate Date");
		logTable.put("currencyCode", "Currency Code");
	}
	
	/**
	 * Used to hold Interest rate value from DB
	 */
	/* Start Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form */	
	/* BigDecimal Variable declaration for interest Rate */
	BigDecimal  interestRate;
	/* End Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form*/
	/**
	 * Used to hold date of Interest rate value from UI
	 */
	String interestRateDateAsString;
	
	/**
	 * Used to hold date of updation as String object for UI
	 */
	String updateDateAsString;
	
	/**
	 * Used to hold date of updation as Date object from DB
	 */
	Date updateDate;
	
	/**
	 * Used to hold user who is updating this interest rate from DB
	 */
	String updateUser;
	
	/**
	 * Used to hold toDate for as a search criteria for UI
	 */
	String fromDateAsString;
	
	/**
	 * Used to hold toDate for as a search criteria for UI
	 */
	String toDateAsString;
	
	/**
	 * Used to hold currency name for the currency of this interest rate.
	 */
	String currencyName;
	
	/**
	 * Used to hold currency level access true or false for this object.
	 */
	String currencyAccess;
	
/*****************************************************************************************/
	// GETTER/SETTER METHODS FOR ABOVE MEMBER VARIABLES
/*****************************************************************************************/

	/**
	 * @return Returns the fromDateAsString.
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}
	
	/**
	 * @param fromDateAsString The fromDateAsString to set.
	 */
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}
	
	/**
	 * @return Returns the currencyAccess.
	 */
	public String getCurrencyAccess() {
		return currencyAccess;
	}
	/**
	 * @param currencyAccess The currencyAccess to set.
	 */
	public void setCurrencyAccess(String currencyAccess) {
		this.currencyAccess = currencyAccess;
	}
	/**
	 * @return Returns the interestRate.
	 */
	/* Start Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form*/
	/* Getting of interestRate for Bigdecimal*/
	public BigDecimal getInterestRate() {
		return interestRate;
	}
	/* End Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form*/
	
	/**
	 * @param interestRate The interestRate to set.
	 */
	/* Start Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form*/
	/*Setting of interestRate for Bigdecimal*/
	public void setInterestRate(BigDecimal  interestRate) {
		this.interestRate = interestRate;
	}
	/* End Code added by Chidambaranathan for Mantis_348 on 1-10-2010 for exponent form*/
	/**
	 * @return Returns the toDateAsString.
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}
	
	/**
	 * @param toDateAsString The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	
	/**
	 * @return Returns the updateDateAsString.
	 */
	public String getUpdateDateAsString() {
		return updateDateAsString;
	}
	
	/**
	 * @param updateDateAsString The updateDateAsString to set.
	 */
	public void setUpdateDateAsString(String updateDateAsString) {
		this.updateDateAsString = updateDateAsString;
	}
	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the currencyName.
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	
	/**
	 * @param currencyName The currencyName to set.
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	
	/**
	 * @return Returns the interestRateDateAsString.
	 */
	public String getInterestRateDateAsString() {
		return interestRateDateAsString;
	}
	
	/**
	 * @param interestRateDateAsString The interestRateDateAsString to set.
	 */
	public void setInterestRateDateAsString(String interestRateDateAsString) {
		this.interestRateDateAsString = interestRateDateAsString;
	}
	
	/*****************************************************************************************/
			// CODE FOR INNER CLASS ID
	/*****************************************************************************************/
	
	/**
	 *  Used to initializing Id class object.
	 */
	private Id id = new Id();

	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	
	/**
	 * Inner class that is used to generate unique primary key for this interest rate.
	 * <AUTHOR>
	 */
	public static class Id extends BaseObject{
		
		/**
		 * used to hold hostId from property file
		 */		
		private String hostId ;
		
		/**
		 * used to hold entityId
		 */		
		private String entityId;
		
		/**
		 * used to hold associated currencyCode
		 */		
		private String currencyCode;
		
		/**
		 * Used to hold date of Interest rate value from DB
		 */
		Date interestRateDate;
		
		/**
		 *	Default constructor for generating Id
		 */
		public Id() {}

		/**
		 * Overloaded constructor for generating parameterized id
		 * @param hostId
		 * @param entityId
		 * @param currencyCode
		 * @param interestDate
		 */
		public Id(String hostId, String entityId ,String currencyCode) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.currencyCode = currencyCode;
		}
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		
		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		
		/**
		 * @return Returns the interestRateDate.
		 */
		public Date getInterestRateDate() {
			return interestRateDate;
		}
		
		/**
		 * @param interestRateDate The interestRateDate to set.
		 */
		public void setInterestRateDate(Date interestRateDate) {
			this.interestRateDate = interestRateDate;
		}
	}
}