/*
 * @(#)Holiday.java  12/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Hashtable;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * POJO to map S_HOLIDAY table
 */
public class Holiday extends BaseObject implements org.swallow.model.AuditComponent{
	/** Default version id */
	private static final long serialVersionUID = 1L;
	/**
	 * @return Returns the holidayDate_Date.
	 */
	public String getHolidayDate_Date() {
		return holidayDate_Date;
	}
	/**
	 * @param holidayDateAsString The holidayDateAsString to set.
	 */
	public void setHolidayDateAsString(String holidayDateAsString) {
		this.holidayDateAsString = holidayDateAsString;
	}
	private final Log log = LogFactory.getLog(Holiday.class);
	private String holidayDateAsString;
	private String holidayDate_Date;
	private String holidayDay;
	private Date updateDate = new Date();
	private String updateUser;

	private Id id = new Id();

	@SuppressWarnings("unchecked")
	public static Hashtable<String, String>  logTable = new Hashtable<String, String>();
	static {
		logTable.put("id.holidayDate","Holiday");
		logTable.put("id.entityId","Entity Id");
		logTable.put("id.countryCode","Country Code");
	
	}
	
	/**
	 * @return Returns the holidayDate_Date.
	 */
	

	
	/**
	 * @param holidayDate_Date The holidayDate_Date to set.
	 */
	public void setHolidayDate_Date(String holidayDate_Date) {
		this.holidayDate_Date = holidayDate_Date;
	}
	
	/**
	 * @return Returns the holidayDateAsString.
	 */
	public String getHolidayDateAsString() {
		return holidayDateAsString;
	}
	/**
	 * @param holidayDateAsString The holidayDateAsString to set.
	 */

	public static class Id extends BaseObject{
		
		private String hostId ;
		private String entityId;
		private Date holidayDate;
		private String countryCode;
		public Id() {}

		public Id(String hostId, String entityId, Date holidayDate) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.holidayDate=holidayDate;
			
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}		
		public String getHostId() {
	
			return hostId;
		}
		public void setHostId(String hostId){
		
			this.hostId = hostId;
		
		}
		/**
	      * @return Returns the holidayDate.
	      */
	    public Date getHolidayDate() {
	    	
		return holidayDate;
	    }
	    /**
	      * @param holidayDate The holidayDate to set.
	      */
	    public void setHolidayDate(Date holidayDate) {
		this.holidayDate = holidayDate;
		
		
		}
		public String getCountryCode() {
			return countryCode;
		}
		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}

	}
		
	/**
	 * @return Returns the holidayDay.
	 */
	
	public String getHolidayDay() {
		String day = null;
        SimpleDateFormat  sdf = new SimpleDateFormat("EEEE");  
        if(id!=null &&  id.getHolidayDate()!=null)  {
             day = sdf.format(id.getHolidayDate());             
      } 
 return day;

}


/*	public String getHolidayDay() {
		SimpleDateFormat  sdf = new SimpleDateFormat("EEEE");
	
		return sdf.format(id.getHolidayDate());
	}*/
	/**
	 * @param holidayDay The holidayDay to set.
	 */
	public void setHolidayDay(String holidayDay) {
		this.holidayDay = holidayDay;
	}
	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
}
