/**
 * Created on September 7, 2010
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * This class is used to hold the Interface rules screen details.
 * 
 * <AUTHOR>
 */
public class InterfaceRule extends BaseObject implements
		org.swallow.model.AuditComponent {
	public static String className = "InterfaceRule";
	private static final long serialVersionUID = 1L;

	private String ruleValue = null;
	private String otherMessageType = null;
	private String searchMessageType = "P";
	private String partialRuleId = null;

	private Id id = new Id();

	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("messageType", "Message Type");
		logTable.put("ruleId", "Rule Id");
		logTable.put("ruleKey", "Rule Key");
		logTable.put("ruleValue", "Rule Value");
	}

	public static class Id extends BaseObject {
		private String messageType;
		private String ruleId;
		private String ruleKey;

		public Id() {
		}

		public Id(String messageType, String ruleId, String ruleKey) {
			this.messageType = messageType;
			this.ruleId = ruleId;
			this.ruleKey = ruleKey;
		}

		/**
		 * @return the messageType
		 */
		public String getMessageType() {
			return messageType;
		}

		/**
		 * @param messageType
		 *            the messageType to set
		 */
		public void setMessageType(String messageType) {
			this.messageType = messageType;
		}

		/**
		 * @return the ruleId
		 */
		public String getRuleId() {
			return ruleId;
		}

		/**
		 * @param ruleId
		 *            the ruleId to set
		 */
		public void setRuleId(String ruleId) {
			this.ruleId = ruleId;
		}

		/**
		 * @return the ruleKey
		 */
		public String getRuleKey() {
			return ruleKey;
		}

		/**
		 * @param ruleKey
		 *            the ruleKey to set
		 */
		public void setRuleKey(String ruleKey) {
			this.ruleKey = ruleKey;
		}
	}

	/**
	 * @return the ruleValue
	 */
	public String getRuleValue() {
		return ruleValue;
	}

	/**
	 * @param ruleValue
	 *            the ruleValue to set
	 */
	public void setRuleValue(String ruleValue) {
		this.ruleValue = ruleValue;
	}

	/**
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * @return the otherMessageType
	 */
	public String getOtherMessageType() {
		return otherMessageType;
	}

	/**
	 * @param otherMessageType
	 *            the otherMessageType to set
	 */
	public void setOtherMessageType(String otherMessageType) {
		this.otherMessageType = otherMessageType;
	}

	/**
	 * @return the searchMessageType
	 */
	public String getSearchMessageType() {
		return searchMessageType;
	}

	/**
	 * @param searchMessageType
	 *            the searchMessageType to set
	 */
	public void setSearchMessageType(String searchMessageType) {
		this.searchMessageType = searchMessageType;
	}

	/**
	 * @return the partialRuleId
	 */
	public String getPartialRuleId() {
		return partialRuleId;
	}

	/**
	 * @param partialRuleId
	 *            the partialRuleId to set
	 */
	public void setPartialRuleId(String partialRuleId) {
		this.partialRuleId = partialRuleId;
	}
}