/*
 * @(#)BalType.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class BalType extends BaseObject {
	/*
	 * Start:Variable modified by Betcy for Mantis 997:Allow Internal and
	 * External forecasts to have independent SOD's
	 */
	private String accountId; // Set the ID
	/*
	 * End:Variable modified by <PERSON><PERSON> for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	private String name; // Set the Name Eg Acct Name, Currency name
	private Double startBalance;
	private String startBalanceAsString;
	private String user;
	private Date inputDate;
	private String inputDateAsString;
	private String inputTime;
	private String inputTimeAsString;
	private String balCurrencyCode;
	private int rowCount;

	/*
	 * Start:Mantis 656-Opportunity cost Report Code added by Saminathan on
	 * 03-sep-2008
	 */
	/*
	 * Start:Variable commented by Betcy for Mantis 997:Allow Internal and
	 * External forecasts to have independent SOD's
	 */
	/*
	 * private Double MT950Balance; private String MT950BalanceAsString; private
	 * boolean startBalanceNegative;
	 */
	/*
	 * End:Variable added by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	private String reasonDesc;
	private String reasonCode;
	private String userNotes;
	/*
	 * End:Mantis 656-Opportunity cost Report Code added by Saminathan on
	 * 03-sep-2008
	 */

	/*
	 * Start:Variable addede by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/*
	 * Start: Code modified by Karthik Ramasamy S on 28-05-2010 for the issue to
	 * show the exact value in the screen. The return-type for the
	 * workingForecastSOD and workingExternalSOD variables are changed from
	 * java.lang.Double to java.math.BigDecimal to accomodate the 18-digited
	 * huge values of forecaseSOD and externalSOD.
	 */
	private BigDecimal forecastSOD;
	private String forecastSODAsString;
	private String forecastSODType;
	private String forecastSODTypeAsString;
	private BigDecimal externalSOD;
	/*
	 * End: Code modified by Karthik Ramasamy S on 28-05-2010 for the issue to
	 * show the exact value in the screen. The return-type for the
	 * workingForecastSOD and workingExternalSOD variables are changed from
	 * java.lang.Double to java.math.BigDecimal to accomodate the 18-digited
	 * huge values of forecaseSOD and externalSOD.
	 */

	private String externalSODTypeAsString;
	private String externalSODAsString;
	private String externalSODType;
	private boolean forecastSODNegative;
	private boolean externalSODNegative;
	private Double suppliedExternalBalance;
	private String suppliedExternalBalanceAsString;
	/*
	 * End:Variable addede by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */

	/*
	 * Start: Refer to Mail , Subject :- New column for P_BALANCE table , Dated :
	 * Tuesday, January 23
	 */

	/**
	 * Start: Refer to Mail , Subject :- New column for P_BALANCE table , Dated :
	 * Tuesday, January 23*
	 */
	private String balanceSource;

	/**
	 * End: Refer to Mail , Subject :- New column for P_BALANCE table , Dated :
	 * Tuesday, January 23*
	 */
	/**
	 * Matnis 5028 enhacned alerting
	 */
	private String scenarioHighlighted = null;

	/**
	 * @return Returns the inputDate.
	 */
	public Date getInputDate() {
		return inputDate;
	}

	/**
	 * @param inputDate
	 *            The inputDate to set.
	 */
	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	/**
	 * @return Returns the inputDateAsString.
	 */
	public String getInputDateAsString() {
		return inputDateAsString;
	}

	/**
	 * @param inputDateAsString
	 *            The inputDateAsString to set.
	 */
	public void setInputDateAsString(String inputDateAsString) {
		this.inputDateAsString = inputDateAsString;
	}

	/**
	 * @return Returns the inputTime.
	 */
	public String getInputTime() {
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		return sdf.format(getInputDate());

	}

	/**
	 * @param inputTime
	 *            The inputTime to set.
	 */
	public void setInputTime(String inputTime) {
		this.inputTime = inputTime;
	}

	/**
	 * @return Returns the name.
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            The name to set.
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return Returns the startBalance.
	 */
	public Double getStartBalance() {
		return startBalance;
	}

	/**
	 * @param startBalance
	 *            The startBalance to set.
	 */
	public void setStartBalance(Double startBalance) {
		this.startBalance = startBalance;
	}

	/**
	 * @return Returns the startBalanceAsString.
	 */
	public String getStartBalanceAsString() {
		return startBalanceAsString;
	}

	/**
	 * @param startBalanceAsString
	 *            The startBalanceAsString to set.
	 */
	public void setStartBalanceAsString(String startBalanceAsString) {
		this.startBalanceAsString = startBalanceAsString;
	}

	/**
	 * @return Returns the user.
	 */
	public String getUser() {
		return user;
	}

	/**
	 * @param user
	 *            The user to set.
	 */
	public void setUser(String user) {
		this.user = user;
	}

	/**
	 * @return Returns the inputTimeAsString.
	 */
	public String getInputTimeAsString() {
		return inputTimeAsString;
	}

	/**
	 * @param inputTimeAsString
	 *            The inputTimeAsString to set.
	 */
	public void setInputTimeAsString(String inputTimeAsString) {
		this.inputTimeAsString = inputTimeAsString;
	}

	/**
	 * @return Returns the balCurrencyCode.
	 */
	public String getBalCurrencyCode() {
		return balCurrencyCode;
	}

	/**
	 * @param balCurrencyCode
	 *            The balCurrencyCode to set.
	 */
	public void setBalCurrencyCode(String balCurrencyCode) {
		this.balCurrencyCode = balCurrencyCode;
	}

	/**
	 * @return Returns the balanceSource.
	 */
	public String getBalanceSource() {
		return balanceSource;
	}

	/**
	 * @param balanceSource
	 *            The balanceSource to set.
	 */
	public void setBalanceSource(String balanceSource) {
		this.balanceSource = balanceSource;
	}

	/**
	 * @return Returns the startBalanceNegative.
	 */
	/*
	 * Start: commented by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	// public boolean isStartBalanceNegative() { return startBalanceNegative; }
	/**
	 * @param startBalanceNegative
	 *            The startBalanceNegative to set.
	 */

	// public void setStartBalanceNegative(boolean startBalanceNegative) {
	// this.startBalanceNegative = startBalanceNegative; }
	/*
	 * End: commented by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/* Added for setting the number of rows fetched on 18-02-2008 */
	public int getRowCount() {
		return rowCount;
	}

	public void setRowCount(int rowCount) {
		this.rowCount = rowCount;
	}

	/* Added for setting the number of rows fetched on 18-02-2008 */
	/*
	 * Start:Mantis 656-Opportunity cost Report Code added by Saminathan on
	 * 03-sep-2008
	 */
	/*
	 * Start: commented by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/**
	 * @return Returns the MT950Balance.
	 */
	/*
	 * public Double getMT950Balance() { return MT950Balance; }
	 */

	/**
	 * @param MT950Balance
	 *            The MT950Balance to set.
	 */
	/*
	 * public void setMT950Balance(Double balance) { MT950Balance = balance; }
	 */
	/*
	 * Enf=d: added by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/**
	 * @return Returns the reasonCode.
	 */
	public String getReasonCode() {
		return reasonCode;
	}

	/**
	 * @param reasonCode
	 *            The reasonCode to set.
	 */
	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	/**
	 * @return Returns the userNotes.
	 */
	public String getUserNotes() {
		return userNotes;
	}

	/**
	 * @param userNotes
	 *            The userNotes to set.
	 */
	public void setUserNotes(String userNotes) {
		this.userNotes = userNotes;
	}

	/*
	 * Start: commented by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/**
	 * @return Returns the MT950BalanceAsString.
	 */
	/*
	 * public String getMT950BalanceAsString() { return MT950BalanceAsString; }
	 */

	/**
	 * @param MT950BalanceAsString
	 *            The MT950BalanceAsString to set.
	 */
	/*
	 * public void setMT950BalanceAsString(String balanceAsString) {
	 * this.MT950BalanceAsString = balanceAsString; }
	 */
	/*
	 * End: commented by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */

	/**
	 * @return Returns the reasonDesc.
	 */
	public String getReasonDesc() {
		return reasonDesc;
	}

	/**
	 * @param reasonDesc
	 *            The reasonDesc to set.
	 */
	public void setReasonDesc(String reasonDesc) {
		this.reasonDesc = reasonDesc;
	}

	/*
	 * Start:Variable modified by Betcy for Mantis 997:Allow Internal and
	 * External forecasts to have independent SOD's
	 */
	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/*
	 * End:Variable modified by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/*
	 * Start:Code added by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/**
	 * This is used to get ForecastSODType
	 * 
	 * @param none
	 * @return String
	 */
	public String getForecastSODType() {
		return forecastSODType;
	}

	/**
	 * This is used to set ForecastSODType
	 * 
	 * @param ForecastSODType
	 * @return none
	 */
	public void setForecastSODType(String forecastSODType) {
		this.forecastSODType = forecastSODType;
	}

	/*
	 * Start: Code modified by Karthik Ramasamy S on 28-05-2010 for the issue to
	 * show the exact value in the screen. The return-type for the forecastSOD
	 * and externalSOD variables are changed from java.lang.Double to
	 * java.math.BigDecimal to accomodate the 18-digited huge values of
	 * forecaseSOD and externalSOD.
	 */
	/**
	 * This is used to get forecast SOD
	 * 
	 * @param none
	 * @return BigDecimal
	 */

	public BigDecimal getForecastSOD() {
		return forecastSOD;
	}

	/**
	 * This is used to set forecast SOD
	 * 
	 * @param forecastSOD
	 * @return none
	 */
	public void setForecastSOD(BigDecimal forecastSOD) {
		this.forecastSOD = forecastSOD;
	}

	/**
	 * This is used to get external SOD
	 * 
	 * @param none
	 * @return BigDecimal
	 */
	public BigDecimal getExternalSOD() {
		return externalSOD;
	}

	/**
	 * This is used to set external SOD
	 * 
	 * @param forecastSOD
	 * @return none
	 */

	public void setExternalSOD(BigDecimal externalSOD) {
		this.externalSOD = externalSOD;
	}

	/*
	 * End: Code modified by Karthik Ramasamy S on 28-05-2010 for the issue to
	 * show the exact value in the screen. The return-type for the forecastSOD
	 * and externalSOD variables are changed from java.lang.Double to
	 * java.math.BigDecimal to accomodate the 18-digited huge values of
	 * forecaseSOD and externalSOD.
	 */
	/**
	 * This is used to get external SOD type
	 * 
	 * @param none
	 * @return String
	 */
	public String getExternalSODType() {
		return externalSODType;
	}

	/**
	 * This is used to set external SOD type
	 * 
	 * @param externalSODType
	 * @return none
	 */
	public void setExternalSODType(String externalSODType) {
		this.externalSODType = externalSODType;
	}

	/**
	 * This is used to get forecast SOD type as string
	 * 
	 * @param none
	 * @return String
	 */
	public String getForecastSODTypeAsString() {
		return forecastSODTypeAsString;
	}

	/**
	 * This is used to set forecast SOD type as string
	 * 
	 * @param forecastSODTypeAsString
	 * @return none
	 */
	public void setForecastSODTypeAsString(String forecastSODTypeAsString) {
		this.forecastSODTypeAsString = forecastSODTypeAsString;
	}

	/**
	 * This is used to get external SOD type as string
	 * 
	 * @param none
	 * @return String
	 */

	public String getExternalSODTypeAsString() {
		return externalSODTypeAsString;
	}

	/**
	 * This is used to set external SOD type as string
	 * 
	 * @param externalSODTypeAsString
	 * @return none
	 */
	public void setExternalSODTypeAsString(String externalSODTypeAsString) {
		this.externalSODTypeAsString = externalSODTypeAsString;
	}

	/**
	 * This is used to get external SOD string
	 * 
	 * @param none
	 * @return String
	 */
	public String getExternalSODAsString() {
		return externalSODAsString;
	}

	/**
	 * This is used to set external SOD as string
	 * 
	 * @param externalSODAsString
	 * @return none
	 */
	public void setExternalSODAsString(String externalSODAsString) {
		this.externalSODAsString = externalSODAsString;
	}

	/**
	 * This is used to check forecast SOD is negative
	 * 
	 * @param none
	 * @return boolean
	 */
	public boolean isForecastSODNegative() {
		return forecastSODNegative;
	}

	/**
	 * This is used to set forecast SOD as negative
	 * 
	 * @param forecastSODNegative
	 * @return boolean
	 */
	public void setForecastSODNegative(boolean forecastSODNegative) {
		this.forecastSODNegative = forecastSODNegative;
	}

	/**
	 * This is used to check external SOD is negative
	 * 
	 * @param none
	 * @return boolean
	 */
	public boolean isExternalSODNegative() {
		return externalSODNegative;
	}

	/**
	 * This is used to set external SOD as negative
	 * 
	 * @param externalSODNegative
	 * @return boolean
	 */
	public void setExternalSODNegative(boolean externalSODNegative) {
		this.externalSODNegative = externalSODNegative;
	}

	/**
	 * This is used to get forecast SOD as string
	 * 
	 * @param none
	 * @return String
	 */
	public String getForecastSODAsString() {
		return forecastSODAsString;
	}

	/**
	 * This is used to set forecast SOD as string
	 * 
	 * @param forecastSODAsString
	 * @return none
	 */
	public void setForecastSODAsString(String forecastSODAsString) {
		this.forecastSODAsString = forecastSODAsString;
	}

	/**
	 * This is used to get supplied external balance.
	 * 
	 * @param none
	 * @return Double
	 */
	public Double getSuppliedExternalBalance() {
		return suppliedExternalBalance;
	}

	/**
	 * This is used to set supplied external balance.
	 * 
	 * @param suppliedExternalBalance
	 * @return none
	 */
	public void setSuppliedExternalBalance(Double suppliedExternalBalance) {
		this.suppliedExternalBalance = suppliedExternalBalance;
	}

	/**
	 * This is used to get supplied external balance as string
	 * 
	 * @param none
	 * @return String
	 */
	public String getSuppliedExternalBalanceAsString() {
		return suppliedExternalBalanceAsString;
	}

	/**
	 * This is used to set supplied external balance as string.
	 * 
	 * @param suppliedExternalBalanceAsString
	 * @return none
	 */
	public void setSuppliedExternalBalanceAsString(
			String suppliedExternalBalanceAsString) {
		this.suppliedExternalBalanceAsString = suppliedExternalBalanceAsString;
	}
	/*
	 * End:Code added by Betcy for Mantis 997:Allow Internal and External
	 * forecasts to have independent SOD's
	 */
	/*
	 * End:Mantis 656-Opportunity cost Report Code added by Saminathan on
	 * 03-sep-2008
	 */

	public String getScenarioHighlighted() {
		return scenarioHighlighted;
	}

	public void setScenarioHighlighted(String scenarioHighlighted) {
		this.scenarioHighlighted = scenarioHighlighted;
	}
}
