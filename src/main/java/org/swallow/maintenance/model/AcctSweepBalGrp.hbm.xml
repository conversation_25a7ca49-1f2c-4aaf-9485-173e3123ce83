<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
   <class name="org.swallow.maintenance.model.AccountSweepBalanceGroup" table="P_ACC_SWEEP_BAL_GRP" >
   
	   <composite-id class="org.swallow.maintenance.model.AccountSweepBalanceGroup$Id" name="id">
       <key-property name="accountId" column="ACCOUNT_ID" access="field"></key-property>
       <key-property name="entityId" column="ENTITY_ID" access="field"></key-property>
       <key-property name="hostId" column="HOST_ID" access="field"></key-property>
       <key-property name="sweepAccountId" column="SWEEP_ACCOUNT_ID" access="field"></key-property>
       </composite-id>

 </class>

</hibernate-mapping>