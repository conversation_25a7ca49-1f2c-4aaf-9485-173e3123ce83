/*
 * @(#)FILENAME.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.io.Serializable;

/**
 * This class is immutable class to contain the Entity Id,Currency Id,Currency Name.
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class CurrencyTO implements Serializable{
    /** Holds the Entity Id  */
    private String entityId = "";

    /** Holds the Currency Id  */
    private String currencyId = "";

    /** Holds the Currency Name  */
    private String currencyName = "";
    
    private int currencyAccess;

	
	
    /**
     * Constructs the CurrencyTO object.
     *
     * @param entityId Entity Id
     * @param currencyId Currency Id
     * @param currencyName Currency Name
     */
    public CurrencyTO(String entityId, String currencyId, String currencyName) {
        super();
        this.entityId = entityId;
        this.currencyId = currencyId;
        this.currencyName = currencyName;
        
    }
    
    /**
     * Constructs the CurrencyTO object.
     *
     * @param entityId Entity Id
     * @param currencyId Currency Id
     * @param currencyName Currency Name
     */
    public CurrencyTO(String entityId, String currencyId, String currencyName, int currencyAccess) {
        super();
        this.entityId = entityId;
        this.currencyId = currencyId;
        this.currencyName = currencyName;
        this.currencyAccess = currencyAccess;
        
    }

    /**
     * Returns the Currency Id.
     *
     * @return Returns the currencyId.
     */
    public String getCurrencyId() {
        return currencyId;
    }

    /**
     * Returns the Currency Name.
     *
     * @return Returns the currencyName.
     */
    public String getCurrencyName() {
        return currencyName;
    }

    /**
     * Returns the Entity Id.
     *
     * @return Returns the entityId.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared.
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof CurrencyTO) {
            CurrencyTO curr = ( CurrencyTO ) obj;
            retValue = curr.getEntityId().equals(entityId)
                && curr.getCurrencyId().equals(currencyId);
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Returns the Hash Code. 
     */
    public int hashCode() {
        return entityId.hashCode() + currencyId.hashCode();
    }

    /**
     * This function overrides the toString function of Object class.
     *
     * @return String repersentation of object.
     */
    public String toString() {
        StringBuffer buf = new StringBuffer();
        buf.append(entityId).append(",").append(currencyId).append(",").append(currencyName);

        return buf.toString();
    }

	/**
	 * @return the currencyAccess
	 */
	public int getCurrencyAccess() {
		return currencyAccess;
	}

	/**
	 * @param currencyAccess the currencyAccess to set
	 */
	public void setCurrencyAccess(int currencyAccess) {
		this.currencyAccess = currencyAccess;
	}
    
    
}
