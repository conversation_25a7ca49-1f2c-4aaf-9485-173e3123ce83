/**
 * @(#)ForecastMonitorTemplateColSeqGeneretor.java 1.0 Jun 1, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

import org.hibernate.Session;
import org.hibernate.jdbc.Work;
import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.Type;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.Batcher;
import org.swallow.util.JDBCExceptionReporter;
import org.swallow.util.SwtConstants;

/**
 * ForecastMonitorTemplateColSeqGeneretor.java
 * 
 * Class to generate the sequence number for column Id in
 * ForecastMonitorTemplateCol
 * 
 * <AUTHOR>
 * 
 */
public class ForecastMonitorTemplateColSeqGeneretor  implements Serializable,
		IdentifierGenerator, Configurable {

	private final Log log = LogFactory.getLog(this.getClass());

	/**
	 * Configure this instance, given the value of parameters specified by the
	 * user as <param> elements. This method is called just once, following
	 * instantiation.
	 * 
	 * @param type
	 * @param params
	 * @param dialect
	 * @return
	 */
	public void configure(Type type, Properties params, Dialect dialect)
			throws MappingException {
		log
				.debug("No Configuration required for generating ForecastMonitorTemplateCol Sequence number");
	}

	/**
	 * Create a new sequence number for ForecastMonitorTemplateCol object.
	 * 
	 * @param SessionImplementor
	 *            session
	 * @param Object
	 *            obj
	 * @return Serializable
	 * @throws SQLException 
	 * 
	 */
	public Serializable generate(SessionImplementor session, Object obj)
			throws HibernateException, SQLException {
		// Method's local variable declaration
		//ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastMonitorTemplateColObj = null;
		//ForecastMonitorTemplateCol.Id instance
		ForecastMonitorTemplateCol.Id forecastMonitorTemplateColIdObj = null;
		// result set instance
		ResultSet resultSet = null;
		//PreparedStatement instance
		PreparedStatement stSelect = null;
		// to hold sequenceValue
		String sequenceValue = null;
		// to hold sequence query
		String _sequenceQuery = null;
		try {
			log.debug(this.getClass().getName() + "-[generate]-Entry");
			// Condition to chcek object is null
			if (obj == null
					|| !obj.getClass().equals(ForecastMonitorTemplateCol.class)) {
				throw new HibernateException(
						"Model object is null or object type is not org.swallow.maintenance.model.ForecastMonitorTemplateCol  ");
			} else {
				forecastMonitorTemplateColObj = (ForecastMonitorTemplateCol) obj;
				forecastMonitorTemplateColIdObj = forecastMonitorTemplateColObj
						.getId();
				// Generate the sequence id from the package given for column id field
				_sequenceQuery = "SELECT LPAD(pk_utility.fn_get_sequence_number('FCASTTEMPLATE'),12,0) FROM dual";
				stSelect = Batcher.of(session)
						.prepareStatement(_sequenceQuery);
				resultSet = stSelect.executeQuery();
				// Condition to result set has value
				if (resultSet.next())
					// get sequlence value
					sequenceValue = resultSet.getString(1);
				if (sequenceValue == null)
					throw new HibernateException(
							"Could not fecth the sequence number");
				// Condition to check column name is total and is fixed
				if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Total")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("TOTAL");
				// Condition to check column name is Bucket and is fixed
				else if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Bucket")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("BUCKET");
				// Condition to check column name is Date and is fixed
				else if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Date")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("VALDATE");
				// Condition to check column name is Assumption and is fixed
				else if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Assumption")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("ASSUMPTION");
				// Condition to check column name is Scenario and is fixed
				else if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Scenario")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("SCENARIO");
				// Condition to check column name is Grand Total and is fixed
				else if (forecastMonitorTemplateColObj.getColumnDisplayName()
						.equals("Grand Total")
						&& forecastMonitorTemplateColObj.getColumnType()
								.equals(SwtConstants.FIXED))
					forecastMonitorTemplateColIdObj.setColumnId("GTOTAL");
				else
					//Set columnid for other user defined columns
					forecastMonitorTemplateColIdObj.setColumnId(sequenceValue);
			}
		} catch (SQLException|HibernateException sqle) {
			log.error("Exception caught in " + this.getClass().getName()
					+ "-[generate]" + sqle.getMessage());
			JDBCExceptionReporter.logExceptions(sqle);
			throw sqle;
		} finally {
			log.debug(this.getClass().getName() + "-[generate]-Exit");
			if (resultSet != null && stSelect != null)
				Batcher.of(session).closeQueryStatement(stSelect, resultSet);
		}
		
		return forecastMonitorTemplateColIdObj;
	}

	@Override
	public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
		try {
			return generate((SessionImplementor)session, object);
		} catch (Exception e) {
			throw new HibernateException(e);
		}
	}

}
