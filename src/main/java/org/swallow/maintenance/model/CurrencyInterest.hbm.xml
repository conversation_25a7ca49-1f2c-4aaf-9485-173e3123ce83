<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.CurrencyInterest" table="S_CURRENCY_INTEREST_RATE">
  <composite-id class="org.swallow.maintenance.model.CurrencyInterest$Id" name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
   <key-property name="interestRateDate" type ="date" access="field" column="INTEREST_RATE_DATE"/>
  </composite-id>
 	<property name="interestRate" column="INTEREST_RATE"/>
	<property name="updateDate" column="UPDATE_DATE"/>
	<property name="updateUser" column="UPDATE_USER"/>
 </class>
</hibernate-mapping>
