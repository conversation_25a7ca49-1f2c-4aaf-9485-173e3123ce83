<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<class name="org.swallow.maintenance.model.Holiday"
		table="S_HOLIDAY">
		<composite-id class="org.swallow.maintenance.model.Holiday$Id" name="id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="holidayDate" access="field"
				column="HOLIDAY_DATE" />
			<key-property name="countryCode" access="field"
				column="COUNTRY_CODE" />
		</composite-id>
		<!-- Mantis Issue 1306 : Code Removed for column in HoliDay by Arumugam on 08-Mar-2011 -->
		<property name="updateDate" column="UPDATE_DATE" />
		<property name="updateUser" column="UPDATE_USER" />
	</class>
</hibernate-mapping>
