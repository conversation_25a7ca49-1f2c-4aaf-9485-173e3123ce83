<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.CurrencyExchange" table="S_CURRENCY_EXCHANGE_RATE">
  <composite-id class="org.swallow.maintenance.model.CurrencyExchange$Id" name="id" unsaved-value="any">
   <key-property name="hostId" access="field" column="HOST_ID"/>
   <key-property name="entityId" access="field" column="ENTITY_ID"/>
   <key-property name="currencyCode" access="field" column="CURRENCY_CODE"/>
    <key-property name="exchangeRateDate" access="field" column="EXCHANGE_RATE_DATE"/>
  </composite-id>
  <property name="exchangeRate" column="EXCHANGE_RATE"/>  
  <property name="updateDate" column="UPDATE_DATE"/>
  <property name="updateUser" column="UPDATE_USER"/>
  	<!-- Code added by Chinniah for Mantis 1351 on 13-JUL-2011:For enable sorting for coluumns Name, FXRATE, Updatedate/Time, and User -->
  <many-to-one name="currencyMaster" lazy="false"
   class="org.swallow.maintenance.model.CurrencyMaster"
   column="CURRENCY_CODE" not-null="true" outer-join="true"
   update="false" insert="false" />
 </class>
</hibernate-mapping>
