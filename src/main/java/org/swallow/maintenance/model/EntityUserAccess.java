package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;



public class EntityUserAccess extends BaseObject{

	/**
	 * @param entityName
	 * @param entityId
	 * @param access
	 */
	public EntityUserAccess( String entityId, String entityName, String domesticCurrency,int access) {
		super();
		this.entityName = entityName;
		this.domesticCurrency = domesticCurrency;
		this.entityId = entityId;
		this.access = access;
	}
	private String entityName;
	private String domesticCurrency;
	private String entityId;
	private int access; // 0 - read only , 1 -  full access
	/**
	 * @return Returns the grpLevelName1.
	 */
	public String getGrpLevelName1() {
		return grpLevelName1;
	}
	/**
	 * @param grpLevelName1 The grpLevelName1 to set.
	 */
	public void setGrpLevelName1(String grpLevelName1) {
		this.grpLevelName1 = grpLevelName1;
	}
	/**
	 * @return Returns the grpLevelName2.
	 */
	public String getGrpLevelName2() {
		return grpLevelName2;
	}
	/**
	 * @param grpLevelName2 The grpLevelName2 to set.
	 */
	public void setGrpLevelName2(String grpLevelName2) {
		this.grpLevelName2 = grpLevelName2;
	}
	/**
	 * @return Returns the grpLevelName3.
	 */
	public String getGrpLevelName3() {
		return grpLevelName3;
	}
	/**
	 * @param grpLevelName3 The grpLevelName3 to set.
	 */
	public void setGrpLevelName3(String grpLevelName3) {
		this.grpLevelName3 = grpLevelName3;
	}
	private String grpLevelName1;
	private String grpLevelName2;
	private String grpLevelName3;	

	
	/**
	 * @return Returns the access.
	 */
	public int getAccess() {
		return access;
	}
	/**
	 * @param access The access to set.
	 */
	public void setAccess(int access) {
		this.access = access;
	}
	/**
	 * @return Returns the entityId.
	 */
	public String getEntityId() {
		return entityId;
	}
	/**
	 * @param entityId The entityId to set.
	 */
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	/**
	 * @return Returns the entityName.
	 */
	public String getEntityName() {
		return entityName;
	}
	/**
	 * @param entityName The entityName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	public String getDomesticCurrency() {
		return domesticCurrency;
	}
	public void setDomesticCurrency(String domesticCurrency) {
		this.domesticCurrency = domesticCurrency;
	}
	
	}
