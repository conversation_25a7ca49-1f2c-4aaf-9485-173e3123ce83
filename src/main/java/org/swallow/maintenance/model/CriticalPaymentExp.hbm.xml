<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
        <hibernate-mapping>
        <class name="org.swallow.maintenance.model.CriticalPaymentExp" table="P_ILM_CRITICAL_PAYMENT_EXP" >

        <composite-id class="org.swallow.maintenance.model.CriticalPaymentExp$Id" name="id">
        <key-property name="entityId" column="ENTITY_ID" access="field"></key-property>
        <key-property name="hostId" column="HOST_ID" access="field"></key-property>
        <key-property name="cpTypeId" column="CP_TYPE_ID" access="field"></key-property>
        <key-property name="currencyCode" column="CURRENCY_CODE" access="field"></key-property>

        </composite-id>

        <property name="defaultExpectedTime" not-null="true" column="DEFAULT_EXPECTED_TIME"  />


        </class>

        </hibernate-mapping>