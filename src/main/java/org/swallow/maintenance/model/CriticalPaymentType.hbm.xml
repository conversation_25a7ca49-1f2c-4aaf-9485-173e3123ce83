<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
        <hibernate-mapping>
        <class name="org.swallow.maintenance.model.CriticalPaymentType" table="P_ILM_CRITICAL_PAYMENT_TYPE" >

        <composite-id class="org.swallow.maintenance.model.CriticalPaymentType$Id" name="id">
        <key-property name="entityId" column="ENTITY_ID" access="field"></key-property>
        <key-property name="hostId" column="HOST_ID" access="field"></key-property>
        <key-property name="cpTypeId" column="CP_TYPE_ID" access="field"></key-property>
        </composite-id>

        <property name="cpTypeDesc" not-null="false" column="CP_TYPE_DESC"  />
        <property name="criticalPayCateg" not-null="false" column="CRITICAL_PAYMENT_CATG"  />
        <property name="sumToCateg" not-null="false" column="SUM_TO_CATEGORY"  />
        <property name="sumToTotal" not-null="false" column="SUM_TO_TOTAL"  />
        <property name="orderInCateg" not-null="false" column="ORDER_IN_CATG"  />
        <property name="reportIndivPay" not-null="false" column="REPORT_INDIVIDUAL_PAYMENTS"  />
        <property name="reportable" not-null="false" column="REPORTABLE"  />
        <property name="enableProcess" not-null="false" column="ENABLE_PROCESS"  />
        <property name="updateSetClause" not-null="false" column="UPDATE_SET_CLAUSE"  />
        <property name="updateWhereClause" not-null="false" column="UPDATE_WHERE_CLAUSE"  />
        <property name="execTimeFrame" not-null="true" column="EXEC_TIMEFRAME"  />
        <property name="execWorkDaysOnly" not-null="true" column="EXEC_WORKDAYS_ONLY"  />
        <property name="execFrequencyMins" not-null="true" column="EXEC_FREQUENCY_MINS"  />
        <property name="execStartTime" not-null="true" column="EXEC_START_TIME"  />
        <property name="execEndTime" not-null="true" column="EXEC_END_TIME"  />
        <property name="lastExecSysTime" not-null="false" column="LAST_EXEC_SYSTIME"  />
        <set name="criticalPayExp" table="P_ILM_CRITICAL_PAYMENT_EXP" inverse="true" cascade="delete" lazy="false">
                        <key>
                                <column name="ENTITY_ID" not-null="true" />
                                <column name="HOST_ID" not-null="true" />
                                <column name="CP_TYPE_ID" not-null="true" />
                        </key>
                        <one-to-many class="org.swallow.maintenance.model.CriticalPaymentExp" />
        </set>

        </class>

        </hibernate-mapping>