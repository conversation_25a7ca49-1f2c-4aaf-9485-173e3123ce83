/*
 * Created on Dec 21, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;


public class MatchQuality extends BaseObject implements org.swallow.model.AuditComponent{

	/**
	 * @return Returns the matchQuaA.
	 */
	public String getMatchQuaA() {
		return matchQuaA;
	}
	/**
	 * @param matchQuaA The matchQuaA to set.
	 */
	public void setMatchQuaA(String matchQuaA) {
		this.matchQuaA = matchQuaA;
	}
	/**
	 * @return Returns the matchQuaB.
	 */
	public String getMatchQuaB() {
		return matchQuaB;
	}
	/**
	 * @param matchQuaB The matchQuaB to set.
	 */
	public void setMatchQuaB(String matchQuaB) {
		this.matchQuaB = matchQuaB;
	}
	/**
	 * @return Returns the matchQuaC.
	 */
	public String getMatchQuaC() {
		return matchQuaC;
	}
	/**
	 * @param matchQuaC The matchQuaC to set.
	 */
	public void setMatchQuaC(String matchQuaC) {
		this.matchQuaC = matchQuaC;
	}
	/**
	 * @return Returns the matchQuaD.
	 */
	public String getMatchQuaD() {
		return matchQuaD;
	}
	/**
	 * @param matchQuaD The matchQuaD to set.
	 */
	public void setMatchQuaD(String matchQuaD) {
		this.matchQuaD = matchQuaD;
	}
	/**
	 * @return Returns the matchQuaE.
	 */
	public String getMatchQuaE() {
		return matchQuaE;
	}
	/**
	 * @param matchQuaE The matchQuaE to set.
	 */
	public void setMatchQuaE(String matchQuaE) {
		this.matchQuaE = matchQuaE;
	}
	private String matchQuaA;
	private String matchQuaB;
	private String matchQuaC;
	private String matchQuaD;
	private String matchQuaE;
	private Date updateDate = new Date();
	private String updateUser;
	private Id id = new Id();
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("matchQuaA","Match Quality A");
		logTable.put("matchQuaB","Match Quality B");
		logTable.put("matchQuaC","Match Quality C");
		logTable.put("matchQuaD","Match Quality D");
		logTable.put("matchQuaE","Match Quality E");
		
	}
	
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	
	
	public static class Id extends BaseObject{
		private String hostId ;
		private String entityId;
		private String currencyCode;
		private Integer posLevel;
		public Id() {}

		public Id(String hostId, String entityId,String currencyCode,Integer posLevel) {
			this.hostId = hostId;			
			this.entityId=entityId;
			this.currencyCode=currencyCode;
			this.posLevel=posLevel;
		}
		
				
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		
		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		
		
		/**
		 * @return Returns the posLevel.
		 */
		public Integer getPosLevel() {
			return posLevel;
		}
		/**
		 * @param posLevel The posLevel to set.
		 */
		public void setPosLevel(Integer posLevel) {
			this.posLevel = posLevel;
		}
	
	}
	
	public void setId(Id id){
		this.id = id; 
		}	
	public Id getId(){
		return id; 
		}
	

	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	}

