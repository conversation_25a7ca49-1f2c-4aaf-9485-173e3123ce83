<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.MetaGroupLevel" table="P_METAGROUP_LEVEL">

		<composite-id name="id" class="org.swallow.maintenance.model.MetaGroupLevel$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID"/>
			<key-property name="entityId" access="field" column="ENTITY_ID" />
			<key-property name="mgrpLvlCode" access="field" column="METAGROUP_LEVEL"/>
		</composite-id>					
		<property name="mgrpLvlName" column="METAGROUP_LEVEL_NAME" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>		
    </class>
</hibernate-mapping>