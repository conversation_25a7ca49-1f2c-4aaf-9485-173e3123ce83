<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.ILMTransactionSetHDR" table="P_ILM_SCENARIO_XTRA_TXN_HDR"  >
		<composite-id class="org.swallow.maintenance.model.ILMTransactionSetHDR$Id" name="id"  unsaved-value="any">
			<key-property name="hostId" column="HOST_ID" access="field" />
			<key-property name="entityId" column="ENTITY_ID" access="field" />
			<key-property name="currencyCode" column="CURRENCY_CODE" access="field"  />
		    <key-property name="txnSetId" access="field" column="TXN_SET_ID"/>
		   	
			
		</composite-id>
		<property name="txnSetName" column="TXN_SET_NAME" not-null="false"/>
		
<!-- 		<set name="ILMCcyParameters" table="P_ILM_CCY_PARAMETERS" 	inverse="true" lazy="true"> -->
<!--             <key> -->
<!--                 <column name="GLOBAL_GROUP_ID" not-null="true" /> -->
<!--             </key> -->
<!--             <one-to-many class="org.swallow.maintenance.model.ILMCcyParameters" /> -->
<!--         </set> -->
<!-- 		<set name="accountsInGroup" table="P_ILM_ACC_IN_GROUP" 	inverse="true" lazy="true"> -->
<!--             <key> -->
<!--                 <column name="ILM_GROUP_ID" not-null="true" /> -->
<!--             </key> -->
<!--             <one-to-many class="org.swallow.maintenance.model.ILMAccountInGroups" /> -->
<!--         </set> -->

	<set name="ilmTransactionSetDTLs" inverse="true" lazy="false"  cascade="delete" table="P_ILM_SCENARIO_XTRA_TXN_DTL" >
      <key>
            
            <column name="HOST_ID" not-null="true" />
            <column name="ENTITY_ID" not-null="true" />
            <column name="CURRENCY_CODE" not-null="true" />
            <column name="TXN_SET_ID" not-null="true" />
            
      </key>
      <one-to-many class="org.swallow.maintenance.model.ILMTransactionSetDTL" />
   </set>

	</class>
</hibernate-mapping>
