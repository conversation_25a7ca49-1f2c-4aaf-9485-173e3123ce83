/*
 * @(#)EntityCurrencyGroupTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.io.Serializable;

/**
 * This class is immutable class as used as transfer object. It contains the Entity Id and Currency Group Id.  
 *
 * <AUTHOR> Systems
 * @version 1.0
 */
public class EntityCurrencyGroupTO implements Serializable {
    /** Holds the Entity Id  */
    private String entityId = "";

    /** Holds the Currency Group Id  */
    private String currencyGroupId = "";

    /**
     * Create the EntityCurrencyGroupTO object.
     *
     * @param entityId Entity Id
     * @param currencyGroupId Currency Group Id
     */
    public EntityCurrencyGroupTO(String entityId, String currencyGroupId) {
        super();
        this.entityId = entityId;
        this.currencyGroupId = currencyGroupId;
    }

    /**
     * Returns the Currency Group Id.
     *
     * @return Returns the currencyGroupId.
     */
    public String getCurrencyGroupId() {
        return currencyGroupId;
    }

    /**
     * Returns the Entity Id.
     *
     * @return Returns the entityId.
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof EntityCurrencyGroupTO) {
            EntityCurrencyGroupTO curr = ( EntityCurrencyGroupTO ) obj;
            retValue = curr.getEntityId().equals(entityId)
                && curr.getCurrencyGroupId().equals(currencyGroupId);
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Retunrs the Hash Code of object.
     */
    public int hashCode() {
        return entityId.hashCode() + currencyGroupId.hashCode();
    }
}
