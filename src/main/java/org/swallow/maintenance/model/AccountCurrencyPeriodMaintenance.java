
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.maintenance.model.AccountAttributeHDR.Id;
import org.swallow.model.BaseObject;

public class AccountCurrencyPeriodMaintenance extends BaseObject implements
org.swallow.model.AuditComponent , Comparable<AccountCurrencyPeriodMaintenance>{
	private static final long serialVersionUID = 1L;
	// To hold short name (key) and its description (value)
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	/**
	 * Static block puts short name and its description in a map
	 */
	static {
		logTable.put("hostId", "Host Id");
		logTable.put("entityId", "Entity Id");
		logTable.put("accountId", "Account Id");
		logTable.put("startDate", "Start Date");
		logTable.put("endDate", "End Date");
		logTable.put("minimumReserve", "Minimum Reserve");
		logTable.put("tier", "Tier");
		logTable.put("targetAvgBalance", "Target Avg Balance");
		logTable.put("fillDays", "Fill Days");
		logTable.put("fillBalance", "Fill Balance");
		logTable.put("eofBalanceSource", "EOD Balance Source");
		logTable.put("minTargetBalance", "Minimum Target Balance");
		logTable.put("excludeFillPeriod", "Exclude Fill Period");

	}
	 
	private Id id = new Id();
	private Date endDate = null;
	private Double minimumReserve = null;
	private Double tier = null;
	private Double  targetAvgBalance = null;
	private Double  fillDays = null;
	private Double fillBalance = null;
	private Double minTargetBalance = null;
	private String excludeFillPeriod = null;
	private String eofBalanceSource = null;
	private String currnecyCode = null;

	public Id getId() {
		return id;
	}
	public void setId(Id id) {
		this.id = id;
	}
	
	public static class Id extends BaseObject{
		private String hostId = null;
		private String entityId = null;
		private String accountId = null;
		private Date startDate = null;
		public Id() {};
		public Id(String hostId, String entityId, String accountId, Date startDate) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;
			this.startDate = startDate;
		};
		public String getHostId() {
			return hostId;
		}
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		public String getEntityId() {
			return entityId;
		}
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		public String getAccountId() {
			return accountId;
		}
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}
		public Date getStartDate() {
			return startDate;
		}
		public void setStartDate(Date startDate) {
			this.startDate = startDate;
		}
	}
	
	
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Double getMinimumReserve() {
		return minimumReserve;
	}
	public void setMinimumReserve(Double minimumReserve) {
		this.minimumReserve = minimumReserve;
	}
	public Double getTier() {
		return tier;
	}
	public void setTier(Double tier) {
		this.tier = tier;
	}
	public Double getTargetAvgBalance() {
		return targetAvgBalance;
	}
	public void setTargetAvgBalance(Double targetAvgBalance) {
		this.targetAvgBalance = targetAvgBalance;
	}
	public Double getFillDays() {
		return fillDays;
	}
	public void setFillDays(Double fillDays) {
		this.fillDays = fillDays;
	}
	public Double getFillBalance() {
		return fillBalance;
	}
	public void setFillBalance(Double fillBalance) {
		this.fillBalance = fillBalance;
	}
	public String getEofBalanceSource() {
		return eofBalanceSource;
	}
	public void setEofBalanceSource(String eofBalanceSource) {
		this.eofBalanceSource = eofBalanceSource;
	}

	
	@Override
	  public int compareTo(AccountCurrencyPeriodMaintenance u) {
	    if (getId().getStartDate() == null || u.getId().getStartDate() == null) {
	      return 0;
	    }
	    return getId().getStartDate().compareTo(u.getId().getStartDate());
	  }
	public String getExcludeFillPeriod() {
		return excludeFillPeriod;
	}
	public void setExcludeFillPeriod(String excludeFillPeriod) {
		this.excludeFillPeriod = excludeFillPeriod;
	}
	public Double getMinTargetBalance() {
		return minTargetBalance;
	}
	public void setMinTargetBalance(Double minTargetBalance) {
		this.minTargetBalance = minTargetBalance;
	}
	public String getCurrnecyCode() {
		return currnecyCode;
	}
	public void setCurrnecyCode(String currnecyCode) {
		this.currnecyCode = currnecyCode;
	}
	
	
}
