<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
   <class name="org.swallow.maintenance.model.AccSweepSchedule" table="P_ACC_SWEEP_SCHEDULE" >
   
	    <id name="sweepScheduleId" column="SWEEP_SCHEDULE_ID" type="long">
		   <generator class="sequence">
		    <param name="sequence_name">P_ACC_SWEEP_SCHEDULE_SEQUENCE</param>
		    <param name="increment_size">1</param>
		   </generator>
	 	 </id>
		  
 	<property name="hostId" not-null="true" column="HOST_ID"  />
	<property name="entityId" not-null="true" column="ENTITY_ID"  />
	<property name="accountId" not-null="true" column="ACCOUNT_ID"  />
	<property name="sweepAccountHostId" not-null="true" column="SWEEP_ACCOUNT_HOST_ID"  />
	<property name="sweepAccountEntity" not-null="true" column="SWEEP_ACCOUNT_ENTITY"  />
	<property name="sweepAccountId" not-null="true" column="SWEEP_ACCOUNT_ID"  />
	<property name="scheduleFrom" not-null="true" column="SCHEDULE_FROM"  />
	<property name="scheduleTo" not-null="true" column="SCHEDULE_TO"  />
	<property name="targetBalance" not-null="true" column="TARGET_BALANCE"  />
	<property name="targetBalanceType" not-null="true" column="TARGET_BALANCE_TYPE"  />
	<property name="minAmount" not-null="false" column="MIN_AMOUNT"  />
	<property name="sweepDirection" not-null="false" column="SWEEP_DIRECTION"  />
	<property name="allowMultiple" not-null="false" column="ALLOW_MULTIPLE"  />
	<property name="sweepFromBalanceType" not-null="false" column="SWEEP_FROM_BALANCE_TYPE"  />
	<property name="thisAccSweepBookcodeCr" not-null="false" column="THIS_ACC_SWEEP_BOOKCODE_CR"  />
	<property name="thisAccSweepBookcodeDr" not-null="false" column="THIS_ACC_SWEEP_BOOKCODE_DR"  />
	<property name="otherAccSweepBookcodeCr" not-null="false" column="OTHER_ACC_SWEEP_BOOKCODE_CR"  />
	<property name="otherAccSweepBookcodeDr" not-null="false" column="OTHER_ACC_SWEEP_BOOKCODE_DR"  />
	<property name="thisAccSettleMethodCr" not-null="false" column="THIS_ACC_SETTLE_METHOD_CR"  />
	<property name="thisAccSettleMethodDr" not-null="false" column="THIS_ACC_SETTLE_METHOD_DR"  />
	<property name="otherAccSettleMethodCr" not-null="false" column="OTHER_ACC_SETTLE_METHOD_CR"  />
	<property name="otherAccSettleMethodDr" not-null="false" column="OTHER_ACC_SETTLE_METHOD_DR"  />
    <property name="sweepOnGroupBalance" not-null="false" column="SWEEP_ON_GROUP_BALANCE"  />
    <property name="targetBalanceTypeId" not-null="false" column="TARGET_BALANCE_TYPE_ID"  />
 
<property name="otherSweepFromBalType" not-null="false" column="OTHER_SWEEP_FROM_BAL_TYPE"  />
 </class>
</hibernate-mapping>