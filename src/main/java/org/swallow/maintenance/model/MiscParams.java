package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;
import java.util.Date;
import java.math.BigDecimal;

public class MiscParams extends BaseObject {

	private Id id = new Id();

    

    /** nullable persistent field */
    private String key1Desc;

    /** nullable persistent field */
    private String key2Desc;

    /** persistent field */
    private String parValue;

    /** nullable persistent field */
    private String parValDesc;

    /** nullable persistent field */
    private Date updateDate;

    /** nullable persistent field */
    private String updateUser;
	

	public static class Id extends BaseObject{
	
		//unnessary code Removed by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId
		private String hostId;
		/*Start: code Added/Modified by <PERSON>rumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		private String entityId;
		
		/*Start code added by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
		/** persistent field */
	    private String key1;
	    /** nullable persistent field */
		private String key2;
	    /*End code added by <PERSON><PERSON> for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
	    
		public Id() {}
		/*Start code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/ 
		public Id(String hostId, String key1,String key2,String entityId) {
			this.hostId = hostId;
			//this.seqNo = seqNo;
			this.key1= key1;
			this.key2 = key2;
		/*End code modified by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
			this.entityId = entityId;
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */	
		}
		
		
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		

		/*Start code added by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
		/**
		 * @return Returns the key1.
		 */
		public String getKey1() {
			return key1;
		}
		/**
		 * @param key1 The key1 to set.
		 */
		public void setKey1(String key1) {
			this.key1 = key1;
		}
		/**
		 * @return Returns the key2.
		 */
		public String getKey2() {
			return key2;
		}
		/**
		 * @param key2 The key2 to set.
		 */
		public void setKey2(String key2) {
			this.key2 = key2;
		}
		/*End code added by Betcy for mantis 1220 on 22-09-10 Change primary key of P_MISC_PARAMS*/
		/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
	}

	public void setId(Id id){
		this.id = id; 
		}
	

	
	public Id getId(){
		return id; 
		}
	
		
	
	
	/**
	 * @return Returns the key1Desc.
	 */
	public String getKey1Desc() {
		return key1Desc;
	}
	/**
	 * @param key1Desc The key1Desc to set.
	 */
	public void setKey1Desc(String key1Desc) {
		this.key1Desc = key1Desc;
	}
	
	/**
	 * @return Returns the key2Desc.
	 */
	public String getKey2Desc() {
		return key2Desc;
	}
	/**
	 * @param key2Desc The key2Desc to set.
	 */
	public void setKey2Desc(String key2Desc) {
		this.key2Desc = key2Desc;
	}
	/**
	 * @return Returns the parValDesc.
	 */
	public String getParValDesc() {
		return parValDesc;
	}
	/**
	 * @param parValDesc The parValDesc to set.
	 */
	public void setParValDesc(String parValDesc) {
		this.parValDesc = parValDesc;
	}
	/**
	 * @return Returns the parValue.
	 */
	public String getParValue() {
		return parValue;
	}
	/**
	 * @param parValue The parValue to set.
	 */
	public void setParValue(String parValue) {
		this.parValue = parValue;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
}
