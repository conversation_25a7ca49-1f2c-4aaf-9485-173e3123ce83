/**
 * @(#)ForecastMonitorTemplate.java 1.0 May 24, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;

import org.swallow.model.BaseObject;
import org.swallow.model.User;

/**
 * ForecastMonitorTemplate.java
 * 
 * ForecastMonitorTemplate class is used for ForecastMonitorTemplate screen that will
 * display ForecastMonitor Templates<br>
 * 
 * <AUTHOR> A
 * @date May 24, 2011
 */
public class ForecastMonitorTemplate extends BaseObject implements org.swallow.model.AuditComponent{

	/**
	 * 
	 */
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("template ID","id.templateId");
		logTable.put("templateName","Template Name");
		logTable.put("publicTemplate","Public Template");
		logTable.put("lockedBy","Locked By");
		logTable.put("userId","user ID");
	}
	private static final long serialVersionUID = 1L;
	// String Variable to hold templateName
	private String templateName = null;
	// String Variable to hold publicTemplate 
	private String publicTemplate = null;
	// Date instance to hold updatDate
	private Date updateDate = new Date();
	// String variable to hold updateUser
	private String updateUser = null;
	// String variable to hold lockedBy
	String lockedBy = null;
	// ID instance
	private Id id = new Id();
	// String variable to hold userId
	private String userId = null;
	//Holds the list of template columns
	private List<ForecastMonitorTemplateCol> forecastTemplateColList = null;
	//Holds the list of users
	private ArrayList<User> templateUsersList = null;

	// Collection Instance to hold ForecastMonitorTemplateCol for deletion
	private List<ForecastMonitorTemplateCol> deleteForecastMonitorTemplateColList = new ArrayList<ForecastMonitorTemplateCol>();

	// Id Class to hold primary and foriegn key values
	public static class Id extends BaseObject {

		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// String variable to hold hostId
		private String hostId = null;
		
		// String variable to hold templateId
		private String templateId = null;

		// default constructor
		public Id() {
		}

		/**
		 * Constructor to set foriegn key values
		 * 
		 * @param hostId
		 * @param templateId
		 * @return
		 */
		public Id(String hostId, String templateId) {
			this.hostId = hostId;
			this.templateId = templateId;

		}
		
		

		/**
		 * Method to Get templateId
		 * @return templateId as String
		 */
		public String getTemplateId() {
			return templateId;
		}

		/**
		 * Method to set templateId
		 * @param templateId 
		 * @return 
		 */
		public void setTemplateId(String templateId) {
			this.templateId = templateId;
		}

		/**
		 * Method to Get hostId
		 * @return hostId as String
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * Method to set hostId
		 * @param hostId 
		 * @return 
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

	}

	/**
	 * Getter method for id
	 * 
	 * @return Id - id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 * @return
	 */
	public void setId(Id id) {
		this.id = id;
	}

	

	/**
	 * Method to Get updateDate
	 * @return updateDate as Date
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * Method to set updateDate
	 * @param updateDate 
	 * @return 
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * Method to Get updateUser
	 * @return updateUser as String
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * Method to set updateUser
	 * @param updateUser 
	 * @return 
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * Method to Get templateName
	 * @return templateName as String
	 */
	public String getTemplateName() {
		return templateName;
	}

	/**
	 * Method to set templateName
	 * @param templateName 
	 * @return 
	 */
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}

	/**
	 * Method to Get publicTemplate
	 * @return publicTemplate as String
	 */
	public String getPublicTemplate() {
		return publicTemplate;
	}

	/**
	 * Method to set publicTemplate
	 * @param publicTemplate 
	 * @return 
	 */
	public void setPublicTemplate(String publicTemplate) {
		this.publicTemplate = publicTemplate;
	}

	/**
	 * Method to Get lockedBy
	 * @return lockedBy as String
	 */
	public String getLockedBy() {
		return lockedBy;
	}

	/**
	 * Method to set lockedBy
	 * @param lockedBy 
	 * @return 
	 */
	public void setLockedBy(String lockedBy) {
		this.lockedBy = lockedBy;
	}
	
	/**
	 * Method to Get userId
	 * @return userId as String
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * Method to set userId
	 * @param userId 
	 * @return 
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	/**
	 * Getter method for forecastTemplateColList
	 * @return forecastTemplateColList
	 */
	public List<ForecastMonitorTemplateCol> getForecastTemplateColList() {
		return forecastTemplateColList;
	}

	/**
	 * Setter method for forecast Template Column list
	 * @param forecastTemplateColList
	 */
	public void setForecastTemplateColList(List<ForecastMonitorTemplateCol> forecastTemplateColList) {
		this.forecastTemplateColList = forecastTemplateColList;
	}

	/**
	 * Getter method for templateUsersList
	 * @return list oftemplateUsersList
	 */
	public ArrayList<User> getTemplateUsersList() {
		return templateUsersList;
	}

	/**
	 * Setter method for templateUsersList
	 * @param templateUsersList
	 */
	public void setTemplateUsersList(ArrayList<User> templateUsersList) {
		this.templateUsersList = templateUsersList;
	}

	/**
	 * Getter method for deleteForecastMonitorTemplateColList
	 * @return List of deleteForecastMonitorTemplateColList
	 */
	public List<ForecastMonitorTemplateCol> getDeleteForecastMonitorTemplateColList() {
		return deleteForecastMonitorTemplateColList;
	}

	/**
	 * Setter method for deleteForecastMonitorTemplateColList
	 * @param deleteForecastMonitorTemplateColList
	 * @return
	 *  
	 */
	public void setDeleteForecastMonitorTemplateColList(
			List<ForecastMonitorTemplateCol> deleteForecastMonitorTemplateColList) {
		this.deleteForecastMonitorTemplateColList = deleteForecastMonitorTemplateColList;
	}

}
