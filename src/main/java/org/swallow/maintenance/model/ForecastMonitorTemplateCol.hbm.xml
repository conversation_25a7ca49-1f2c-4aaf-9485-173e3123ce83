<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<class
		name="org.swallow.maintenance.model.ForecastMonitorTemplateCol"
		table="P_FCAST_TEMPLATE_COL">

<!-- 		<id name="id" access="field" -->
<!-- 			type="org.swallow.maintenance.model.ForecastTemplateIdType"> -->
<!-- 			<column name="HOST_ID" not-null="true" sql-type="hostId" /> -->
<!-- 			<column name="TEMPLATE_ID" not-null="true" -->
<!-- 				sql-type="templateId" /> -->
<!-- 			<column name="COLUMN_ID" not-null="true" -->
<!-- 				sql-type="columnId" /> -->
<!-- 			<generator -->
<!-- 				class="org.swallow.maintenance.model.ForecastMonitorTemplateColSeqGeneretor"> -->
<!-- 				<param name="sequencetable">P_SEQUENCE</param> -->
<!-- 			</generator> -->

<!-- 		</id> -->
		<composite-id name="id" class="org.swallow.maintenance.model.ForecastMonitorTemplateCol$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID"/>
			<key-property name="templateId" access="field" column="TEMPLATE_ID" />
			<key-property name="columnId" access="field" column="COLUMN_ID"/>
		</composite-id>


		<property name="userId" column="USER_ID" not-null="false" />
		<property name="ordinalPos" column="ORDINAL_POS"
			not-null="false" />

		<property name="columnDisplayName" column="COLUMN_DISPLAY_NAME"
			not-null="false" />

		<property name="columnDescription" column="COLUMN_DESCRIPTION"
			not-null="false" />

		<property name="columnType" column="COLUMN_TYPE"
			not-null="false" />

		<property name="columnFormula" column="COLUMN_FORMULA"
			not-null="false" />

		<property name="userEditable" column="USER_EDITABLE"
			not-null="false" />


	</class>
</hibernate-mapping>
