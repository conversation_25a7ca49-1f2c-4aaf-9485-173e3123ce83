package org.swallow.maintenance.model;

import org.swallow.model.AuditComponent;
import java.lang.*;
import java.util.*;
import org.swallow.model.BaseObject;
import java.lang.String;
import java.util.Hashtable;
import java.util.Date;

public class MaintenanceEventDetails extends BaseObject
		implements
			AuditComponent {

	private static final long serialVersionUID = 1L;
	public static Hashtable logTable = new Hashtable();
	private Id id = new Id();
	private String tableName;
	private String recordId;
	private String action;
	private String oldState;
	private String newState;

	public MaintenanceEventDetails() {
		// TODO Auto-generated constructor stub
	}
	
	



	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getRecordId() {
		return recordId;
	}

	public void setRecordId(String recordId) {
		this.recordId = recordId;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getOldState() {
		return oldState;
	}

	public void setOldState(String oldState) {
		this.oldState = oldState;
	}

	public String getNewState() {
		return newState;
	}

	public void setNewState(String newState) {
		this.newState = newState;
	}

	public static class Id extends BaseObject {
		private static final long serialVersionUID = 1L;
		private Long maintEventId;
		private Long maintSeq;

		public Id() {
		}

		public Long getMaintEventId() {
			return maintEventId;
		}

		public void setMaintEventId(Long maintEventId) {
			this.maintEventId = maintEventId;
		}

		public Long getMaintSeq() {
			return maintSeq;
		}

		public void setMaintSeq(Long maintSeq) {
			this.maintSeq = maintSeq;
		}
	}
}