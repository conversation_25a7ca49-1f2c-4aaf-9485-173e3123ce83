<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.maintenance.model.LocationAccess" table="P_Location_ACCESS">
    <composite-id name="id" class="org.swallow.maintenance.model.LocationAccess$Id" unsaved-value="any">
        <key-property name="hostId" access="field" column="HOST_ID"/>
        <key-property name="entityId" access="field" column="ENTITY_ID" />
        <key-property name="locationId" access="field" column="LOCATION_ID"/>
		<key-property name="roleId" access="field" column="ROLE_ID"/>
    </composite-id>
    </class>
</hibernate-mapping> 