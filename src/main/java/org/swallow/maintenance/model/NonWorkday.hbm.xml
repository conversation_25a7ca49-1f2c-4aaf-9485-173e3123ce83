<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<class name="org.swallow.maintenance.model.NonWorkday"
		table="S_NON_WORKDAY_PARAMS">

		<composite-id name="id" class="org.swallow.maintenance.model.NonWorkday$Id">

			<key-property name="hostId" access="field" column="HOST_ID" />

			<key-property name="entityId" access="field"
				column="ENTITY_ID" />

			<key-property name="facility" access="field"
				column="FACILITY" />

		</composite-id>

		<property name="applyEntityCountry"
			column="APPLY_ENTITY_COUNTRY" not-null="false" />

		<property name="applyAccountCountry"
			column="APPLY_ACCOUNT_COUNTRY" not-null="false" />

		<property name="applyCurrencyCountry"
			column="APPLY_CURRENCY_COUNTRY" not-null="false" />

		<property name="updateDate" column="UPDATE_DATE"
			not-null="false" />

		<property name="updateUser" column="UPDATE_USER"
			not-null="false" />
	</class>
</hibernate-mapping>
