package org.swallow.maintenance.model;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Hashtable;

import org.swallow.model.AuditComponent;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

public class AccountSpecificSweepFormat extends BaseObject implements AuditComponent {

	private static final long serialVersionUID = 1L;
	public static Hashtable logTable = new Hashtable();
	private String newInternalCrFormat;
	private String accountName;
	private String newInternalDrFormat;
	private String newExternalCrFormat;
	private String newExternalDrFormat;
	private String newExternalCrFormatInt;
	private String newExternalDrFormatINt;
	private String changed = null;
	private Id id = new Id();

	public String getNewInternalCrFormat() {
		return newInternalCrFormat;
	}

	public void setNewInternalCrFormat(String newInternalCrFormat) {
		this.newInternalCrFormat = newInternalCrFormat;
	}

	public String getNewInternalDrFormat() {
		return newInternalDrFormat;
	}

	public void setNewInternalDrFormat(String newInternalDrFormat) {
		this.newInternalDrFormat = newInternalDrFormat;
	}

	public String getNewExternalCrFormat() {
		return newExternalCrFormat;
	}

	public void setNewExternalCrFormat(String newExternalCrFormat) {
		this.newExternalCrFormat = newExternalCrFormat;
	}

	public String getNewExternalDrFormat() {
		return newExternalDrFormat;
	}

	public void setNewExternalDrFormat(String newExternalDrFormat) {
		this.newExternalDrFormat = newExternalDrFormat;
	}

	public String getNewExternalCrFormatInt() {
		return newExternalCrFormatInt;
	}

	public void setNewExternalCrFormatInt(String newExternalCrFormatInt) {
		this.newExternalCrFormatInt = newExternalCrFormatInt;
	}

	public String getNewExternalDrFormatINt() {
		return newExternalDrFormatINt;
	}

	public void setNewExternalDrFormatINt(String newExternalDrFormatINt) {
		this.newExternalDrFormatINt = newExternalDrFormatINt;
	}

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public static class Id extends BaseObject {
		private static final long serialVersionUID = 1L;
		private String hostId;
		private String entityId;
		private String accountId;
		private String specifiedAccountId;
		private String specifiedEntityId;
		public Id() {
		}

		public String getHostId() {
			return hostId;
		}

		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		public String getEntityId() {
			return entityId;
		}

		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		public String getAccountId() {
			return accountId;
		}

		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		public String getSpecifiedAccountId() {
			return specifiedAccountId;
		}

		public void setSpecifiedAccountId(String specifiedAccountId) {
			this.specifiedAccountId = specifiedAccountId;
		}

		public String getSpecifiedEntityId() {
			return specifiedEntityId;
		}

		public void setSpecifiedEntityId(String specifiedEntityId) {
			this.specifiedEntityId = specifiedEntityId;
		}
		
		

	}

	public String getChanged() {
		return changed;
	}
	
	public void setChanged(String changed) {
		this.changed = changed;
	}
	
	
	public boolean compareTo(AccountSpecificSweepFormat tmp) {

		if (this != null && tmp == null) {
			return false;
		} else if (tmp.getId() != null && this.getId() != null) {
			if (((SwtUtil.isEmptyOrNull(tmp.getId().getAccountId()) && SwtUtil.isEmptyOrNull(this.getId().getAccountId()))
					|| (tmp.getId().getAccountId() != null && this.getId().getAccountId() != null
							&& tmp.getId().getAccountId().equals(this.getId().getAccountId())))
					&& ((tmp.getId().getEntityId() == null && this.getId().getEntityId() == null)
							|| (tmp.getId().getEntityId() != null && this.getId().getEntityId() != null
									&& tmp.getId().getEntityId().equals(this.getId().getEntityId())))
					&& ((tmp.getId().getHostId() == null && this.getId().getHostId() == null)
							|| (tmp.getId().getHostId() != null && this.getId().getHostId() != null
									&& tmp.getId().getHostId().equals(this.getId().getHostId())))
					&& ((tmp.getId().getSpecifiedAccountId() == null && this.getId().getSpecifiedAccountId() == null)
							|| (tmp.getId().getSpecifiedAccountId() != null && tmp.getId().getSpecifiedAccountId()
									.equals(this.getId().getSpecifiedAccountId())))) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}


}