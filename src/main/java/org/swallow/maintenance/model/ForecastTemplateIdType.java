/**
 * @(#)ForecastTemplateIdType.java 1.0 Jun 9, 2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import org.hibernate.Session;
import org.hibernate.jdbc.Work;
import org.hibernate.metamodel.spi.ValueAccess;
import org.hibernate.HibernateException;
import org.hibernate.JDBCException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.exception.spi.SQLExceptionConverter;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.Type;
import org.hibernate.usertype.CompositeUserType;
import org.swallow.api.socket.SwtException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * ForecastTemplateIdType.java
 * 
 * ForecastTemplateIdType implements CompositeUserType which is may be used in
 * almost every way that a component may be used. It may even contain
 * many-to-one associations. Implementors must be immutable and must declare a
 * public default constructor.
 * 
 * <AUTHOR>
 * 
 */
public class ForecastTemplateIdType implements CompositeUserType, Serializable {
/*//TODO:SBR
	private static final String[] PROPERTY_NAMES = { "hostId", "templateId",
			"columnId" }; // property name array

	private static final Type[] PROPERTY_TYPES = { StandardBasicTypes.STRING,
			StandardBasicTypes.STRING, StandardBasicTypes.STRING }; // property type array
*/	
	// Create a log instance for logging
	private static final Log log = LogFactory
			.getLog(ForecastTemplateIdType.class);

	/**
	 * Reconstruct an object from the cacheable representation. At the very
	 * least this method should perform a deep copy. (optional operation)
	 * 
	 * @param arg0
	 * @param arg1
	 * @param arg2
	 * @return Object
	 */
	public Object assemble(Serializable arg0, SessionImplementor arg1,
			Object arg2) throws HibernateException {
		return null;
	}

	/**
	 * Return a deep copy of the persistent state, stopping at entities and at
	 * collections.
	 * 
	 * @param arg0
	 * @return Object
	 */
	public Object deepCopy(Object arg0) throws HibernateException {
		return null;
	}

	/**
	 * Transform the object into its cacheable representation. At the very least
	 * this method should perform a deep copy. That may not be enough for some
	 * implementations, however; for example, associations must be cached as
	 * identifier values. (optional operation)
	 * 
	 * @param arg0
	 * @param arg1
	 * @return Serializable
	 */
	public Serializable disassemble(Object arg0, SessionImplementor arg1)
			throws HibernateException {
		return null;
	}

	/**
	 * Compare two instances of the class mapped by this type for persistence
	 * "equality". Equality of the persistent state.
	 * 
	 * @param arg0
	 * @param arg1
	 * @return boolean
	 */
	public boolean equals(Object arg0, Object arg1) throws HibernateException {
		return false;
	}

/*//TODO:SBR
//	**
//	 * Get the "property names" that may be used in a query.
//	 * 
//	 * @return String[]
//	 *
	public String[] getPropertyNames() {
		log.debug(this.getClass().getName() + "-[getPropertyNames]-Entry/Exit");
		return PROPERTY_NAMES;
	}

//	**
//	 * Get the corresponding "property types".
//	 * 
//	 * @return Type[]
//	 *
	public Type[] getPropertyTypes() {
		log.debug(this.getClass().getName() + "-[getPropertyTypes]-Entry/Exit");
		return PROPERTY_TYPES;
	}
*/
	/**
	 * Get the value of a property.
	 * 
	 * @param component
	 * @param property
	 * @param Object
	 */
	public Object getPropertyValue(Object component, int property)
			throws HibernateException {
		// local variable declaration
		ForecastMonitorTemplateCol.Id forecastMonitorTemplateColIdObject = null;

		try {
			log.debug(this.getClass().getName() + "-[getPropertyValue]-Entry");
			forecastMonitorTemplateColIdObject = (ForecastMonitorTemplateCol.Id) component;
			switch (property) {
			case 0:
				return forecastMonitorTemplateColIdObject.getHostId();
			case 1:
				return forecastMonitorTemplateColIdObject.getTemplateId();

			default:
				throw new HibernateException(property + " is invalid number");
			}
		} catch (HibernateException exp) {
			log.error("Exception caught in " + this.getClass().getName()
					+ "-[getPropertyValue]-" + exp.getMessage());
			throw exp;
		} finally {
			log.debug(this.getClass().getName() + "-[getPropertyValue]-Exit");
		}
	}

	/**
	 * Check if objects of this type mutable.
	 * 
	 * @return boolean
	 */
	public boolean isMutable() {
		return false;
	}

	/**
	 * 
	 * Retrieve an instance of the mapped class from a JDBC resultset.
	 * Implementors should handle possibility of null values.
	 * 
	 * @param resultSet
	 * @param names
	 * @param session
	 * @param owner
	 * @return Object
	 * 
	 */
	public Object nullSafeGet(ResultSet resultSet, String[] names,
			SessionImplementor session, Object owner)
			throws HibernateException, SQLException {
		// local variable declaration
		String templateId = null;
		String columnId = null;
		ForecastMonitorTemplateCol.Id forecastMonitorTemplateColIdObj = null;
		String hostId = null;
		try {
			log.debug(this.getClass().getName() + "-[nullSafeGet]-Entry");
			if (resultSet.getObject(names[0]) != null
					&& resultSet.getObject(names[1]) != null) {
				hostId = resultSet.getString(names[0]);
				templateId = resultSet.getString(names[1]);
				columnId = resultSet.getString(names[2]);

				forecastMonitorTemplateColIdObj = new ForecastMonitorTemplateCol.Id();
				forecastMonitorTemplateColIdObj.setHostId(hostId);
				forecastMonitorTemplateColIdObj.setTemplateId(templateId);
				forecastMonitorTemplateColIdObj.setColumnId(columnId);
			}
		} catch (Exception exp) {
			log.error("Exception Caught in " + this.getClass().getName()
					+ "-[nullSafeGet]-" + exp.getMessage());
		} finally {
			log.debug(this.getClass().getName() + "-[nullSafeGet]-Exit");
		}
		return forecastMonitorTemplateColIdObj;
	}

	/**
	 * Write an instance of the mapped class to a prepared statement.
	 * Implementors should handle possibility of null values. A multi-column
	 * type should be written to parameters starting from index.
	 * 
	 * @param statement
	 * @param value
	 * @param index
	 * @param session
	 * @return
	 */
	public void nullSafeSet(PreparedStatement statement, Object value,
			int index, SessionImplementor session) throws HibernateException,
			SQLException {
		// local variable declaration
		ForecastMonitorTemplateCol.Id forecastMonitorTemplateColIdObj = null;
		try {
			log.debug(this.getClass().getName() + "-[nullSafeSet]-Entry");
			if (value == null) {
				statement.setNull(index, Types.VARCHAR);
				statement.setNull(index + 1, Types.VARCHAR);
				statement.setNull(index + 2, Types.VARCHAR);
			} else {
				// get id object from value
				forecastMonitorTemplateColIdObj = (ForecastMonitorTemplateCol.Id) value;

				// Setting the host id
				if (forecastMonitorTemplateColIdObj.getHostId() == null) {
					statement.setNull(index, Types.VARCHAR);
				} else {
					statement.setString(index, forecastMonitorTemplateColIdObj
							.getHostId());
				}

				// Setting the template id
				if (forecastMonitorTemplateColIdObj.getTemplateId() == null) {
					statement.setNull(index + 1, Types.VARCHAR);
				} else {
					statement.setString(index + 1,
							forecastMonitorTemplateColIdObj.getTemplateId());
				}

				// Setting the column id
				if (forecastMonitorTemplateColIdObj.getColumnId() == null) {
					statement.setNull(index + 2, Types.VARCHAR);
				} else {
					statement.setString(index + 2,
							forecastMonitorTemplateColIdObj.getColumnId());
				}
			}
		} catch (Exception exp) {
			log.error("Exception Caught in " + this.getClass().getName()
					+ "-[nullSafeSet]-" + exp.getMessage());
		} finally {
			log.debug(this.getClass().getName() + "-[nullSafeSet]-Exit");
		}

	}

	/**
	 * The class returned by nullSafeGet().
	 * 
	 * @return ForecastMonitorTemplateCol.Id.class
	 */
	@SuppressWarnings("unchecked")
	public Class returnedClass() {
		log.debug(this.getClass().getName() + "-[returnedClass]-Entry/Exit");
		// return id class
		return ForecastMonitorTemplateCol.Id.class;
	}

	/**
	 * Set the value of a property.
	 * 
	 * @param component
	 *            -an instance of class mapped by this "type"
	 * @param property -
	 *            property to be set
	 * @param value -
	 *            value to set
	 * @return
	 */
	public void setPropertyValue(Object component, int property, Object value)
			throws HibernateException {
		// Local variable declaration
		ForecastMonitorTemplateCol.Id forecastMonitorTemplateColIdObject = null;
		try {
			log.debug(this.getClass().getName() + "-[setPropertyValue]-Entry");
			forecastMonitorTemplateColIdObject = (ForecastMonitorTemplateCol.Id) component;

			switch (property) {
			case 0:
				forecastMonitorTemplateColIdObject.setHostId((String) value);
				break;
			case 1:
				forecastMonitorTemplateColIdObject
						.setTemplateId((String) value);
				break;
			case 2:
				forecastMonitorTemplateColIdObject.setColumnId((String) value);
				break;
			default:
				throw new HibernateException(property + " is invalid index");
				
			}
		} catch (HibernateException exp) {
			log.error("Exception Caught in " + this.getClass().getName()
					+ "-[setPropertyValue]-" + exp.getMessage());
			throw exp;

		} finally {
			log.debug(this.getClass().getName() + "-[setPropertyValue]-Exit");
		}

	}
//TODO:SBR to implement !
	@Override
	public int hashCode(Object x) throws HibernateException {
		return super.hashCode();
	}

    @Override
    public Object instantiate(ValueAccess values, SessionFactoryImplementor sessionFactory) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Class embeddable() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Serializable disassemble(Object value) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object assemble(Serializable cached, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public Object replace(Object detached, Object managed, Object owner) {
        // TODO Auto-generated method stub
        return null;
    }

/*//TODO:SBR
	@Override
	public Object nullSafeGet(ResultSet rs, String[] names, SharedSessionContractImplementor session, Object owner)
			throws HibernateException, SQLException {
		return nullSafeGet(rs, names, (SessionImplementor) session, owner);
	}

	@Override
	public void nullSafeSet(PreparedStatement st, Object value, int index, SharedSessionContractImplementor session)
			throws HibernateException, SQLException {
		nullSafeSet(st, value, index, (SessionImplementor) session);
	}

	@Override
	public Serializable disassemble(Object value, SharedSessionContractImplementor session) throws HibernateException {
		return disassemble(value, (SessionImplementor) session);
	}

	@Override
	public Object assemble(Serializable cached, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		return assemble(session, (SessionImplementor) session, owner);
	}

	@Override
	public Object replace(Object original, Object target, SharedSessionContractImplementor session, Object owner)
			throws HibernateException {
		throw new HibernateException("Method [replace] not implemented in ForecastTemplateIdType");
	}
*/
}
