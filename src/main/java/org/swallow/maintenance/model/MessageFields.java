/*
 * @(#)MessageFields.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtConstants;
import java.util.Hashtable;


public class MessageFields extends BaseObject implements org.swallow.model.AuditComponent, Comparable{

	private Integer lineNo;
	private String lineNoAsString;
	private Integer seqNo;
	private String seqNoAsString;
	private String fieldType = SwtConstants.FIELD_TYPE_TEXT;
	private String value;
	private String valueKeyWord;
	private String startPos;
	private String endPos;
	private Date updateDate = new Date();
	private String updateUser;
	private Id id = new Id();
	private String fieldTypeDisplay = SwtConstants.FIELD_TYPE_TEXT;	

	/* Start: UAT_Defects_RABO_Perot_Internal1.xls  Defect No: 78 */
	//Sorting on line number and start position when the fomat type is Multi-line
	private Integer startPosInt ; 
	/* End: UAT_Defects_RABO_Perot_Internal1.xls: 78 */
	private final Log log = LogFactory.getLog(MessageFields.class);
	/**
	 * @return Returns the fieldTypeDisplay.
	 */
	public String getFieldTypeDisplay() {
		if(fieldType != null)
		{
			if(fieldType.equals(SwtConstants.FIELD_TYPE_TEXT))
				fieldTypeDisplay = SwtConstants.FIELD_TEXT;
			else if(fieldType.equals(SwtConstants.FIELD_TYPE_KEYWORD))
				fieldTypeDisplay = SwtConstants.FIELD_KEYWORD;
			else
				fieldTypeDisplay = SwtConstants.FIELD_HEXADECIMAL;			
		}
		return fieldTypeDisplay;
	}

	/**
	 * @param fieldTypeDisplay The fieldTypeDisplay to set.
	 */
	public void setFieldTypeDisplay(String fieldTypeDisplay) {
		setFieldType(fieldTypeDisplay);
		this.fieldTypeDisplay = fieldTypeDisplay;
	}

	/*Start: Refer to log file attached with issue 106 Mentis
An exception is thrown as SwtInteceptor tries to find 'logTable' in the updated/saved object, which it does not find */
public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("lineNoAsString","Line No");
		logTable.put("seqNoAsString","Sequence No");
		logTable.put("fieldType","Field Type");
		logTable.put("value","Value");
		logTable.put("startPos","Start Position");
		logTable.put("endPos","End Position");				
	}
/*End: Refer to log file attached with issue 106 Mentis*/
	public static class Id extends BaseObject{
		private String hostId;
		private String entityId;
		private String formatId;
		private Integer serialNo;
		public Id() {}

		public Id(String hostId, String entityId,String formatId,Integer serialNo) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.formatId=formatId;
			this.serialNo=serialNo;
		}
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the formatId.
		 */
		public String getFormatId() {
			return formatId;
		}
		/**
		 * @param formatId The formatId to set.
		 */
		public void setFormatId(String formatId) {
			this.formatId = formatId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the serialNo.
		 */
		public Integer getSerialNo() {
			return serialNo;
		}
		/**
		 * @param serialNo The serialNo to set.
		 */
		public void setSerialNo(Integer serialNo) {
			this.serialNo = serialNo;
		}
	}

	
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the endPos.
	 */
	public String getEndPos() {
		return endPos;
	}
	/**
	 * @param endPos The endPos to set.
	 */
	public void setEndPos(String endPos) {
		this.endPos = endPos;
	}
	/**
	 * @return Returns the fieldType.
	 */
	public String getFieldType() {
		return fieldType;
	}
	/**
	 * @param fieldType The fieldType to set.
	 */
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	/**
	 * @return Returns the seqNo.
	 */
	public Integer getSeqNo() {
		return seqNo;
	}
	/**
	 * @param seqNo The seqNo to set.
	 */
	public void setSeqNo(Integer seqNo) {
		this.seqNo = seqNo;
	}
	/**
	 * @return Returns the startPos.
	 */
	public String getStartPos() {
		return startPos;
	}
	/**
	 * @param startPos The startPos to set.
	 */
	public void setStartPos(String startPos) {
		this.startPos = startPos;
		/* Start: UAT_Defects_RABO_Perot_Internal1.xls  Defect No: 78 */
		//Sorting on line number and start position when the fomat type is Multi-line
		if (startPos != null && !startPos.equals(SwtConstants.EMPTY_STRING)) {
			setStartPosInt(new Integer(startPos));
		}
		/* End: UAT_Defects_RABO_Perot_Internal1.xls: 78 */
	}
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the value.
	 */
	public String getValue() {
		return value;
	}
	/**
	 * @param value The value to set.
	 */
	public void setValue(String value) {
		this.value = value;
	}
	/**
	 * @return Returns the seqNoAsString.
	 */
	public String getSeqNoAsString() {
		return seqNoAsString;
	}
	/**
	 * @param seqNoAsString The seqNoAsString to set.
	 */
	public void setSeqNoAsString(String seqNoAsString) {
		this.seqNoAsString = seqNoAsString;
	}
	/**
	 * @return Returns the valueKeyWord.
	 */
	public String getValueKeyWord() {
		return valueKeyWord;
	}
	/**
	 * @param valueKeyWord The valueKeyWord to set.
	 */
	public void setValueKeyWord(String valueKeyWord) {
		this.valueKeyWord = valueKeyWord;
	}
	/**
	 * @return Returns the lineNo.
	 */
	public Integer getLineNo() {
		return lineNo;
	}
	/**
	 * @param lineNo The lineNo to set.
	 */
	public void setLineNo(Integer lineNo) {
		this.lineNo = lineNo;
	}
	/**
	 * @return Returns the lineNoAsString.
	 */
	public String getLineNoAsString() {
		return lineNoAsString;
	}
	/**
	 * @param lineNoAsString The lineNoAsString to set.
	 */
	public void setLineNoAsString(String lineNoAsString) {
		this.lineNoAsString = lineNoAsString;
	}
	
	/* Start: UAT_Defects_RABO_Perot_Internal1.xls  Defect No: 78 */
	//Sorting on line number and start position when the fomat type is Multi-line
	/**
	 * @return Returns the startPosInt.
	 */
	public Integer getStartPosInt() {
		return startPosInt;
	}
	/**
	 * @param startPosInt The startPosInt to set.
	 */
	public void setStartPosInt(Integer startPosInt) {
		this.startPosInt = startPosInt;
	}
	
	public int compareTo(Object obj) {
		log.debug("Inside compareTo() method");
		MessageFields msgFld = (MessageFields) (obj);
		log.debug("the value of the obj is==>" + obj);
		log.debug("the value of the line no  is==>" + msgFld.getLineNoAsString());
		log.debug("the value of the start pos is==>" + msgFld.getStartPos());
			
	
		if (msgFld.getLineNoAsString() != null && !msgFld.getLineNoAsString().equals("")) {
			int cmp = lineNo.compareTo(msgFld.getLineNo());
			return (cmp != 0 ? cmp : startPosInt.compareTo(msgFld.getStartPosInt()));
		} else {
			return 0;
		}
		
	}
	/* End: UAT_Defects_RABO_Perot_Internal1.xls: 78 */
	
}

