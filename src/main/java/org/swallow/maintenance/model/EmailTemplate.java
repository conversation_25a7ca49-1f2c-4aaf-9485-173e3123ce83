package org.swallow.maintenance.model;

import org.swallow.model.AuditComponent;

import java.lang.*;
import java.util.*;

import org.swallow.model.BaseObject;

import java.lang.String;
import java.util.Hashtable;

public class EmailTemplate extends BaseObject implements AuditComponent {

    private static final long serialVersionUID = 1L;
    public static Hashtable logTable = new Hashtable();
    private String description;
    private String subjectContent;
    private String bodyContent;
    private String templateId;

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubjectContent() {
        return subjectContent;
    }

    public void setSubjectContent(String subjectContent) {
        this.subjectContent = subjectContent;
    }

    public String getBodyContent() {
        return bodyContent;
    }

    public void setBodyContent(String bodyContent) {
        this.bodyContent = bodyContent;
    }

    static {
        logTable.put("templateId", "Template Id");
        logTable.put("description", "Description");
        logTable.put("subjectContent", "Subject Content");
        logTable.put("bodyContent", "Body Content");
    }
}