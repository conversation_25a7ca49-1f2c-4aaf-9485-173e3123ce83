/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.io.Serializable;
import java.util.Date;

//import org.swallow.maintenance.model.Group.Id;
import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class GroupLevel implements Serializable {
	
	private String 	groupLevelName;									//GROUP_LEVEL_NAME VARCHAR2(20)
	private Date updateDate = new Date();							//UPDATE_DATE DATE
	private String updateUser;										//UPDATE_USER VARCHAR2(15)
	private Id id = new Id();
	
		
//	 Inner class started
	/* Primary key
	 * 		HOST_ID
	 * 		ENTITY_ID
	 * 		GROUP_LEVEL
	 *  
	*/
	public static class Id extends BaseObject{
		
		
		private String hostId;					//HOST_ID VARCHAR2(12) NOT NULL
		private String entityId;								//ENTITY_ID VARCHAR2(12) NOT NULL
		private Integer groupLevelId;							//GROUP_LEVEL NUMBER(1) NOT NULL

		public Id() {}

		public Id(String hostId, String entityId,Integer groupLevelId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.groupLevelId = groupLevelId; 
		}
		
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the groupLevelId.
		 */
		public Integer getGroupLevelId() {
			return groupLevelId;
		}
		/**
		 * @param groupLevelId The groupLevelId to set.
		 */
		public void setGroupLevelId(Integer groupLevelId) {
			this.groupLevelId = groupLevelId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		
		
	}
	//	* Inner class Ends here
	
	
	
	/**
	 * @return Returns the grouplevelName.
	 */
	public String getGroupLevelName() {
		return groupLevelName;
	}
	/**
	 * @param grouplevelName The grouplevelName to set.
	 */
	public void setGroupLevelName(String groupLevelName) {
		this.groupLevelName = groupLevelName;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	
	
	
	

}
