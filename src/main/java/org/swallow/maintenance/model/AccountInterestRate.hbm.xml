<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
 <class name="org.swallow.maintenance.model.AccountInterestRate" table="P_ACCOUNT_INTEREST_RATE">
  <composite-id class="org.swallow.maintenance.model.AccountInterestRate$Id" name="id" unsaved-value="any">
	   <key-property name="entityId" access="field" column="ENTITY_ID"/>
	   <key-property name="hostId" access="field" column="HOST_ID"/>
	   <key-property name="accountId" access="field" column="ACCOUNT_ID"/>
	   <key-property name="interestDateRate" access="field" column="INTEREST_RATE_DATE"/>
  </composite-id>
  <property name="creditRate" column="CREDIT_RATE"/>
  <property name="overdraftRate" column="OVERDRAFT_RATE"/>
  <property name="updateDate" column="UPDATE_DATE"/>
    <property name="updateUser" column="UPDATE_USER"/>
 </class>
</hibernate-mapping>