<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.maintenance.model.AcctMaintenance"
		table="P_ACCOUNT">
		<composite-id name="id" class="org.swallow.maintenance.model.AcctMaintenance$Id" unsaved-value="any">
			<key-property name="hostId" access="field" column="HOST_ID" />
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="accountId" access="field"
				column="ACCOUNT_ID" />
		</composite-id>


		<property name="acctname" column="ACCOUNT_NAME" not-null="true" />
		<property name="currcode" column="CURRENCY_CODE"
			not-null="false" />
		<property name="acctbiccode" column="ACCOUNT_BIC_ID"
			not-null="false" />
		<property name="corresacccode" column="CORRES_ACC_ID"
			not-null="false" />
		<property name="glcode" column="GL_ID" not-null="false" />
		<property name="accttype" column="ACCOUNT_TYPE"
			not-null="false" />
		<property name="acctstatusflg" column="ACCOUNT_STATUS_FLAG"
			not-null="false" />
		<property name="holidaycalendar" column="HOLIDAY_COUNTRY"
			not-null="false" />
		<property name="minacctcode" column="MAIN_ACCOUNT_ID"
			not-null="false" />
		<property name="acctlevel" column="ACCOUNT_LEVEL"
			not-null="false" />
		<property name="acctgrpflg" column="ACCOUNT_CATEGORY"
			not-null="false" />
		<property name="mansweepflg" column="MANUAL_SWEEP_FLAG"
			not-null="false" />
		<property name="autoswpswitch" column="AUTO_SWEEP_SWITCH"
			not-null="false" />
		<!--Code modified by Sudhakar for Mantis 1487 on  24-07-2010 -->
		<property name="eodSweeptime" column="SWEEP_TIME" not-null="false" />
		<property name="minseepamt" column="MIN_SWEEP_AMOUNT"
			not-null="false" />
		<property name="maxsweepamte" column="MAX_SWEEP_AMOUNT"
			not-null="false" />
		<property name="swpdays" column="SWEEP_DAYS" not-null="false" />
		<property name="tgtbalsign" column="TARGET_BALANCE_SIGN"
			not-null="false" />		

		<property name="currcreditrate" column="CURRENT_CREDIT_RATE"
			not-null="false" />
		<property name="curroverdraftrate"
			column="CURRENT_OVERDRAFT_RATE" not-null="false" />
		<property name="targetbalance" column="TARGET_BALANCE"
			not-null="false" />
		<property name="cutoff" column="CUT_OFF" not-null="true" />
		<property name="sweepbookcode" column="SWEEP_BOOKCODE"
			not-null="false" />
		<property name="updateDate" column="UPDATE_DATE"
			not-null="false" />
		<property name="updateUser" column="UPDATE_USER"
			not-null="false" />


		<property name="monitor" column="MONITOR" not-null="true" />
		<property name="linkAccID" column="LINK_ACCOUNT_ID"
			not-null="false" />
		<property name="acctClass" column="ACCOUNT_CLASS"
			not-null="true" />
		<property name="acctMonitorSum" column="MONITOR_SUM"
			not-null="true" />
		<property name="acctPriorityOrder" column="PRIORITY_ORDER"
			not-null="true" />	

		<property name="acctContactName" column="CONTACT_NAME"
			not-null="false" />
		<property name="acctPhone" column="PHONE_NUMBER"
			not-null="false" />
		<property name="acctEmailAddr" column="EMAIL_ADDRESS"
			not-null="false" />

		<property name="acctNewCrInternal"
			column="NEW_INTERNAL_CR_FORMAT" not-null="false" />
		<property name="acctNewDrInternal"
			column="NEW_INTERNAL_DR_FORMAT" not-null="false" />
		<property name="acctNewCrExternal"
			column="NEW_EXTERNAL_CR_FORMAT" not-null="false" />
		<property name="acctNewDrExternal"
			column="NEW_EXTERNAL_DR_FORMAT" not-null="false" />		

		<property name="autoOpenUnexpected"
			column="AUTO_OPEN_UNEXPECTED" not-null="false" />
		<property name="autoOpenUnsettled" column="AUTO_OPEN_UNSETTLED"
			not-null="false" />

		
		<property name="allPreAdviceEntity" column="ALLOW_PRE_ADVICES"
			not-null="false" />
		
		<property name="creditExternalInter"
			column="NEW_EXTERNAL_CR_FORMAT_INT" not-null="false" />
		<property name="debitExternalInter"
			column="NEW_EXTERNAL_DR_FORMAT_INT" not-null="false" />
		



		
		<property name="aggAccount" column="AGGREGATE_ACCOUNT"
			not-null="false" />
		
		<property name="sweepCode" column="SWEEP_CODE" not-null="false" />
		

		
		<property name="acctextraid" column="EXTRA_ID" not-null="false" />
		
		<property name="archiveData" column="ARCHIVE_DATA"
			not-null="false" />
		
		<property name="primaryForecast"
			column="FORECAST_SOD_BASIS_PREFERRED" not-null="true" />
		<property name="primaryExternal"
			column="EXT_SOD_BASIS_PREFERRED" not-null="true" />
		<property name="secondaryForecast"
			column="FORECAST_SOD_BASIS_SECONDARY" not-null="false" />
		<property name="secondaryExternal"
			column="EXT_SOD_BASIS_SECONDARY" not-null="false" />
		<property name="forecastSOD" column="BV_ADJUST_BASIS_FORECAST"
			not-null="false" />
		<property name="externalSOD" column="BV_ADJUST_BASIS_EXTERNAL"
			not-null="false" />
		
		<property name="futureBalances" column="FUTURE_BALANCE_METHOD"
			not-null="false" />
		
		<property name="sweepFrmbal" column="SWEEP_FROM_BALANCE_TYPE"
			not-null="false" />
		<!--Code modified by Sudhakar for Mantis 1487 on  24-07-2010 -->	
<!-- 		<property name="intraDaySweeptime" column="INTRADAY_SWEEP_TIME" -->
<!-- 			not-null="false" /> -->
<!-- 		<property name="eodTargetbalance" -->
<!-- 			column="INTRADAY_TARGET_BALANCE" not-null="false" /> -->
<!-- 		<property name="eodTgtBalsign" -->
<!-- 			column="INTRADAY_TARGET_BALANCE_SIGN" not-null="false" /> -->
<!-- 		<property name="eodMinseepamt" -->
<!-- 			column="INTRADAY_MIN_SWEEP_AMOUNT" not-null="false" /> -->
		<property name="subAcctim" column="USE_SUB_ACC_TIMING_ONLY"
			not-null="false" />
		<property name="acctIBAN" column="IBAN" not-null="false" />	
		<property name="isIlmLiqContributor" column="IS_ILM_LIQ_CONTRIBUTOR" not-null="false" />	
		<property name="isIlmCustomerAccount" column="IS_ILM_CUSTOMER_ACCOUNT" not-null="false" />	
		<property name="isIlmCentralBankMember" column="IS_ILM_CENTRAL_BANK_MEMBER" not-null="false" />	
		<property name="accountPartyId" column="ACCOUNT_PARTY_ID" not-null="false" />	
		<property name="defaultSettleMethod" column="DEFAULT_SETTLE_METHOD" not-null="false" />	
		<property name="fmi" column="FMI" not-null="false" />
		<property name="thisEntityInclBalFlag" column="THIS_ENTITY_INCL_BAL_FLAG" not-null="false" />
		<property name="thisEntityInclFrom" column="THIS_ENTITY_INCL_FROM" not-null="false" />
		<property name="thisEntityInclTo" column="THIS_ENTITY_INCL_TO" not-null="false" />
		<property name="servicingEntityId" column="SERVICING_ENTITY_ID" not-null="false" />
		<property name="accNameInSvcEntity" column="ACC_NAME_IN_SVC_ENTITY" not-null="false" />
		<property name="svcEntityInclBalFlag" column="SVC_ENTITY_INCL_BAL_FLAG" not-null="false" />
		<property name="svcEntityInclFrom" column="SVC_ENTITY_INCL_FROM" not-null="false" />
		<property name="svcEntityInclTo" column="SVC_ENTITY_INCL_TO" not-null="false" />	
		<many-to-one name="entity" update="false" insert="false" lazy="false"
			cascade="none" class="org.swallow.maintenance.model.Entity"
			not-null="true" outer-join="true"
			foreign-key="FK_P_ACCOUNT_S_ENTITY">
			<column name="HOST_ID" />
			<column name="ENTITY_ID" />
		</many-to-one>

	
		
		
			<set name="accountSweepFormats" inverse="true" lazy="false"  cascade="delete" table="P_ACCOUNT_SWEEP_FORMAT" >
			      <key>
			            
			            <column name="HOST_ID" not-null="true" />
			            <column name="ENTITY_ID" not-null="true" />
			            <column name="ACCOUNT_ID" not-null="true" />
			            
			      </key>
			      <one-to-many class="org.swallow.maintenance.model.AccountSpecificSweepFormat" />
			   </set>


	</class>
</hibernate-mapping>
