/*
 * @(#)CountryOverride.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR> A
 * 
 * CountryOverride. java is the POJO to map S_COUNTRY_OVERRIDE table
 * implementing AuditComponent to log the records in S_MAINTENANCE_LOG
 */
public class CountryOverride extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// variable to hold weekend1
	private String weekend1 = null;
	// variable to hold weekend1
	private String weekend2 = null;
	// variable to hold override weekend1
	private String overrideWeekend1 = null;
	// variable to hold override weekend1
	private String overrideWeekend2 = null;
	// variable to hold country Name
	private String countryName = null;
	// Date instance to hold update date
	private Date updateDate = new Date();
	// variable to hold update user
	private String updateUser = null;

	private Id id = new Id();

	// Hash table instance to hold maintenance log table values
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("countryOverride", "CountryOverride");

	}

	/**
	 * Id Class to hold primary key values
	 * 
	 * <AUTHOR> A
	 * 
	 */
	public static class Id extends BaseObject {

		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// variable to hold hostId
		private String hostId = null;
		// variable to hold entityId
		private String entityId = null;
		// variable to hold countryCode
		private String countryCode = null;

		// Default constructor
		public Id() {

		}

		/**
		 * Constructor to set primary key values
		 * 
		 * @param hostId
		 * @param entityId
		 * @param countryCode
		 */
		public Id(String hostId, String entityId, String countryCode) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.countryCode = countryCode;

		}

		/**
		 * Getter method for EntityId
		 * 
		 * @return String - EntityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 * @return
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return String - hostId
		 */
		public String getHostId() {

			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 * @return
		 */
		public void setHostId(String hostId) {

			this.hostId = hostId;

		}

		/**
		 * Getter method for countryCode
		 * 
		 * @return String - countryCode
		 */
		public String getCountryCode() {
			return countryCode;
		}

		/**
		 * Setter method for countryCode
		 * 
		 * @param countryCode
		 * @return
		 */
		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}

	}

	/**
	 * Getter method for Id class
	 * 
	 * @return Id - id.
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 * @return
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for updateDate
	 * 
	 * @return Date - updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * Setter method for updateDate
	 * 
	 * @param updateDate
	 * @return
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * Getter method for updateUser
	 * 
	 * @return String - updateUser
	 */

	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * Setter method for updateUser
	 * 
	 * @param updateUser
	 * @return
	 */

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * Getter method for weekend1
	 * 
	 * @return String - weekend1
	 */

	public String getWeekend1() {
		return weekend1;
	}

	/**
	 * Setter method for weekend1
	 * 
	 * @param weekend1
	 * @return
	 */

	public void setWeekend1(String weekend1) {
		this.weekend1 = weekend1;
	}

	/**
	 * Getter method for weekend2
	 * 
	 * @return String - weekend2
	 */
	public String getWeekend2() {
		return weekend2;
	}

	/**
	 * Setter method for weekend2
	 * 
	 * @param weekend2
	 * @return
	 */

	public void setWeekend2(String weekend2) {
		this.weekend2 = weekend2;
	}

	/**
	 * Getter method for overrideWeekend1
	 * 
	 * @return String - overrideWeekend1
	 */
	public String getOverrideWeekend1() {
		return overrideWeekend1;
	}

	/**
	 * Setter method for overrideWeekend1
	 * 
	 * @param overrideWeekend1
	 * @return
	 */

	public void setOverrideWeekend1(String overrideWeekend1) {
		this.overrideWeekend1 = overrideWeekend1;
	}

	/**
	 * Getter method for overrideWeekend2
	 * 
	 * @return String - overrideWeekend2
	 */
	public String getOverrideWeekend2() {
		return overrideWeekend2;
	}

	/**
	 * Setter method for overrideWeekend2
	 * 
	 * @param overrideWeekend2
	 * @return
	 */

	public void setOverrideWeekend2(String overrideWeekend2) {
		this.overrideWeekend2 = overrideWeekend2;
	}

	/**
	 * Getter method for countryName
	 * 
	 * @return String - countryName
	 */
	public String getCountryName() {
		return countryName;
	}

	/**
	 * Setter method for countryName
	 * 
	 * @param countryName
	 * @return
	 */

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

}
