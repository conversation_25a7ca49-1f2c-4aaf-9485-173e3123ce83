<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.maintenance.model.Party" table="P_PARTY">
		<composite-id name="id" class="org.swallow.maintenance.model.Party$Id" unsaved-value="any">
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="hostId" access="field" column="HOST_ID" />
			<key-property name="partyId" access="field"
				column="PARTY_ID" />

		</composite-id>
		<property name="partyName" column="PARTY_NAME" not-null="false" />
		<property name="updateDate" column="UPDATE_DATE"
			not-null="false" />
		<property name="updateUser" column="UPDATE_USER"
			not-null="false" />
		<property name="partyType" column="PARTY_TYPE" not-null="false" />
		<!--Code Modified For Mantis 1680 by chinniah on 10-Jan-2012:DNB Screen/Reporting - Mid Term Solution
		-->
		<property name="parentParty" column="PARENT_PARTY_ID"
			not-null="false" />


	</class>
</hibernate-mapping>
