<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
   <class name="org.swallow.maintenance.model.FunctionalGroup" table="P_FUNCTIONAL_GROUP">
   
	  <composite-id class="org.swallow.maintenance.model.FunctionalGroup$Id" name="id" unsaved-value="any">
		   <key-property name="functionalGroup" access="field" column="FUNCTIONAL_GROUP"/>
	  </composite-id>

	<property name="functionalGroupDesc" column="FUNCTIONAL_GROUP_DESC" not-null="false"/>
    <property name="restrictType" column="RESTRICT_TYPE" not-null="false"/>

 </class>
</hibernate-mapping>