/*
 * Created on Jul 27, 2007
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SweepIntermediaries extends BaseObject implements org.swallow.model.AuditComponent {

	private Id id = new Id();
	private String intermediary;
	private String currencyName;
	/*START:code added by <PERSON><PERSON> on 25-Feb-2010 for Mantis 1121: added Account column in UI screen  */
	// variable to get the account id
	private String accountId;
	/*END:code added by <PERSON><PERSON> on 25-Feb-2010 for Mantis 1121: added Account column in UI screen  */
	
	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("intermediary", "Intermediary BIC");

	}



	public static class Id extends BaseObject{
		private String hostId ;
		private String entityId;
		private String currencyCode;
		private String targetBic;
		


		public Id(String hostId ,String entityId, String currencyCode, String targetBic ) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.currencyCode = currencyCode;
			this.targetBic = targetBic;
			

		}
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		public Id() {
		}

		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the targetBic.
		 */
		public String getTargetBic() {
			return targetBic;
		}
		/**
		 * @param targetBic The targetBic to set.
		 */
		public void setTargetBic(String targetBic) {
			this.targetBic = targetBic;
		}
	}
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}


	/**
	 * @return Returns the intermediary.
	 */
	public String getIntermediary() {
		return intermediary;
	}
	/**
	 * @param intermediary The intermediary to set.
	 */
	public void setIntermediary(String intermediary) {
		this.intermediary = intermediary;
	}

	/**
	 * @return Returns the currencyName.
	 */
	public String getCurrencyName() {
		return currencyName;
	}
	/**
	 * @param currencyName The currencyName to set.
	 */
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
	
	/*START:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */
	/**
	 * @return Returns the accountId.
	 */
	public String getAccountId() {
		return accountId;
	}
	/**
	 * @param accountId The accountId to set.
	 */
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	/*END:code added by Bala on 22-Feb-2010 for Mantis 1121: added Account column in UI screen  */

}

