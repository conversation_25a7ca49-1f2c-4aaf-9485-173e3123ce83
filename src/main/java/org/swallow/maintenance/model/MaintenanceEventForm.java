package org.swallow.maintenance.model;

import java.util.Date;

public class MaintenanceEventForm {
	
	private String maintFacilityId;
	private Date requestDate;
	private String requestUser;
	private String authUser;
	private String selectedUser;
	private Date fromDate;
	private Date toDate;
	private Double prevId;
	private Double nextId;
	private String action;
	private boolean isPendingChecked = true;
	private boolean isAcceptedChecked = false;
	private boolean isRejectedChecked = false;
	private boolean isAllDates = true;
	
	
	
	
	public MaintenanceEventForm() {
	}
	public String getMaintFacilityId() {
		return maintFacilityId;
	}
	public void setMaintFacilityId(String maintFacilityId) {
		this.maintFacilityId = maintFacilityId;
	}
	public Date getRequestDate() {
		return requestDate;
	}
	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}
	public String getRequestUser() {
		return requestUser;
	}
	public void setRequestUser(String requestUser) {
		this.requestUser = requestUser;
	}
	public String getAuthUser() {
		return authUser;
	}
	public void setAuthUser(String authUser) {
		this.authUser = authUser;
	}
	public String getSelectedUser() {
		return selectedUser;
	}
	public void setSelectedUser(String selectedUser) {
		this.selectedUser = selectedUser;
	}
	public Date getFromDate() {
		return fromDate;
	}
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	public Date getToDate() {
		return toDate;
	}
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}
	public Double getPrevId() {
		return prevId;
	}
	public void setPrevId(Double prevId) {
		this.prevId = prevId;
	}
	public Double getNextId() {
		return nextId;
	}
	public void setNextId(Double nextId) {
		this.nextId = nextId;
	}
	public String getAction() {
		return action;
	}
	public void setAction(String action) {
		this.action = action;
	}
	public boolean isPendingChecked() {
		return isPendingChecked;
	}
	public void setPendingChecked(boolean isPendingChecked) {
		this.isPendingChecked = isPendingChecked;
	}
	public boolean isAcceptedChecked() {
		return isAcceptedChecked;
	}
	public void setAcceptedChecked(boolean isAcceptedChecked) {
		this.isAcceptedChecked = isAcceptedChecked;
	}
	public boolean isRejectedChecked() {
		return isRejectedChecked;
	}
	public void setRejectedChecked(boolean isRejectedChecked) {
		this.isRejectedChecked = isRejectedChecked;
	}
	public boolean isAllDates() {
		return isAllDates;
	}
	public void setAllDates(boolean isAllDates) {
		this.isAllDates = isAllDates;
	}
	
	
	
	

}
