package org.swallow.maintenance.authorization;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtUtil;
import org.w3c.dom.Node;

public class MaintenanceAuthUtils {
	private static final Log log = LogFactory.getLog(MaintenanceAuthUtils.class);
	 /**
	  * Create a maintenace event that will go in queue or will be accepted directly
	  * @param maintFacilityId
	  * @param recordId
	  * @param requestUser
	  * @param requestDate
	  * @param authUser
	  * @param authDate
	  * @param prevId
	  * @param nextId
	  * @param action
	  * @param status
	  * @return
	  * @throws SwtException
	  */
	public static Long createMaintenanceEventDetailsRecord(String maintFacilityId, String recordId, String requestUser,
			Date requestDate, String authUser, Date authDate, Long prevId, Long nextId, String action,
			String status) throws SwtException {
		Long resultId = null;
		try {
			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
					.getBean("maintenanceEventMaintenanceManager");
			MaintenanceEvent maintenanceEvent = new MaintenanceEvent();
			maintenanceEvent.setAction(action);
			maintenanceEvent.setAuthDate(authDate);
			maintenanceEvent.setAuthUser(authUser);
			maintenanceEvent.setMaintFacilityId(maintFacilityId);
			maintenanceEvent.setNextId(nextId);
			maintenanceEvent.setPrevId(prevId);
			maintenanceEvent.setRecordId(recordId);
			maintenanceEvent.setRequestDate(requestDate);
			maintenanceEvent.setRequestUser(requestUser);
			maintenanceEvent.setStatus(status);
			

			resultId = maintenanceEventMaintenanceManager.saveMaintenanceEvent(maintenanceEvent);

		} catch (Exception e) {
			log.error(
					SwtUtil.class.getName() + " - Exception Catched in [updatePCMViews] method : - " + e.getMessage());
		}

		return resultId;
	}
	 
	public static void createMaintenanceEventDetailsRecordDetails(Long maintEventId, String tableName, String recordId,
			String action, String oldState, String newState) {
		try {
			Long fileId = SequenceFactory.getSequenceFromDbAsLong("SEQ_S_MAINT_DETAILS_EVENT");
			MaintenanceEventDetails maintenanceEventDetails = new MaintenanceEventDetails();

			maintenanceEventDetails.setAction(action);
			maintenanceEventDetails.getId().setMaintEventId(maintEventId);
			maintenanceEventDetails.getId().setMaintSeq(fileId);
			maintenanceEventDetails.setNewState(newState);
//		maintenanceEventDetails.setOldState("");
			maintenanceEventDetails.setRecordId(recordId);
			maintenanceEventDetails.setTableName(tableName);

			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
					.getBean("maintenanceEventMaintenanceManager");
			maintenanceEventMaintenanceManager.saveMaintenanceEventDetails(maintenanceEventDetails);
		} catch (Exception e) {
			log.error(
					SwtUtil.class.getName() + " - Exception Catched in [updatePCMViews] method : - " + e.getMessage());
		}
	}
	
	public static void acceptMaintenanceEvent(String maintEventId) throws SwtException{
		MaintenanceEvent event = null;
		try {
			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
					.getBean("maintenanceEventMaintenanceManager");
			
			event = maintenanceEventMaintenanceManager.getMaintenanceEvent(""+maintEventId);
			
			if(event != null) {
				
				if(MaintenanceEventConstants.MAINTENENCE_FACILITY_ACCOUNT_GROUP_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doAcceptPcmAccountGroupEvent(event);
					
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_SPREAD_PROFILES_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doAcceptSpreadRulesEvent(event);
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_STOP_RULE_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doAcceptPcmStopRulesEvent(event);
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_CRIT_PAYMENT_TYPE_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doAcceptCriticalPayTypeEvent(event);
				}
			}
			
		} catch (SwtException e) {
			log.error(
					SwtUtil.class.getName() + " - Exception Catched in [updatePCMViews] method : - " + e.getMessage());
		}
		
	}
	
	
	public static void rejectMaintenanceEvent(String maintEventId) throws SwtException{
		MaintenanceEvent event = null;
		try {
			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
					.getBean("maintenanceEventMaintenanceManager");
			
			event = maintenanceEventMaintenanceManager.getMaintenanceEvent(""+maintEventId);
			
			if(event != null) {
				
				if(MaintenanceEventConstants.MAINTENENCE_FACILITY_ACCOUNT_GROUP_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doRejectPcmAccountGroupEvent(event);
					
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_SPREAD_PROFILES_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doRejectPcmSpreadProfileEvent(event);
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_STOP_RULE_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doRejectPcmStopRulesEvent(event);
				}else if(MaintenanceEventConstants.MAINTENENCE_FACILITY_CRIT_PAYMENT_TYPE_SCREEN_ID.equalsIgnoreCase(event.getMaintFacilityId())) {
					MaintenanceAuthFacilities.doRejectCriticalPayTypeEvent(event);
				}
			}
			
		} catch (SwtException e) {
			log.error(
					SwtUtil.class.getName() + " - Exception Catched in [updatePCMViews] method : - " + e.getMessage());
		}
		
	}
	
	public static HashMap addPreviousValueBasedOnField(Object newObject, Object oldObject, String fieldName, HashMap<String, String> map) throws Exception {
		
		//Create a new HashmapFrom the old one
		HashMap<String, String> newMap = new HashMap<String, String>();
		Object newValue = null;
		Object oldValue = null;
		try {
			newMap.putAll(map);
			if(newObject != null && oldObject != null) {
				
				Field[] fields = newObject.getClass().getDeclaredFields();
				
				for (Field field : fields) {
					field.setAccessible(true);
					if(fieldName.equals(field.getName())) {
						newValue = field.get(newObject);
						break;
					}
				}
				
				fields = oldObject.getClass().getDeclaredFields();
				
				for (Field field : fields) {
					field.setAccessible(true);
					if(fieldName.equals(field.getName())) {
						oldValue = field.get(oldObject);
						break;
					}
				}
				
			}
			if(newObject instanceof SpreadProcessPoint && "categories".equals(fieldName)) {
				boolean isEqual = false;
				if (Objects.isNull(oldValue) && Objects.isNull(newValue)) {
				    isEqual = true;
				} else if (Objects.nonNull(oldValue) && Objects.nonNull(newValue)) {
				    isEqual = Arrays.stream(oldValue.toString().split(",")).sorted().collect(Collectors.joining())
				            .equals(Arrays.stream(newValue.toString().split(",")).sorted().collect(Collectors.joining()));
				}
				if(!isEqual) {
					newMap.put("previousValue",oldValue != null?oldValue.toString():"null");
				}
				
			} else if (!((oldValue == newValue) || (oldValue != null && oldValue.equals(newValue)))) {
				newMap.put("previousValue",oldValue != null?oldValue.toString():"null");
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		
		
		
		return newMap;
		
		
	}
	
	public static boolean updateMaintenanceEventStatus(Long maintEventId, String action, String userId,Long prevId, Long nextId) throws SwtException{
		MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
				.getBean("maintenanceEventMaintenanceManager");
		MaintenanceEvent maintenanceEvent = null;
		boolean result = false;
		try {
			maintenanceEvent = new MaintenanceEvent();
			maintenanceEvent.setMaintEventId(maintEventId);
			maintenanceEvent.setAuthUser(userId);
			maintenanceEvent.setAuthDate(SwtUtil.getSystemDatewithTime());
			maintenanceEvent.setStatus(action);
			if(prevId != null) {
				maintenanceEvent.setPrevId(prevId);
			}
			if(nextId != null) {
				maintenanceEvent.setNextId(nextId);
			}
			
			maintenanceEventMaintenanceManager
					.updateMaintenanceEvent(maintenanceEvent);
			if("R".equalsIgnoreCase(action)) {
				rejectMaintenanceEvent(""+maintEventId);
			}else if("A".equalsIgnoreCase(action)) {
				acceptMaintenanceEvent(""+maintEventId);
			}
			result = true;
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
		}
		
		return result;
		
	}
	
	public static boolean isRecordRelatedToMaintenanceEvent (String recordId, String facilityId) throws SwtException {
		boolean result  = false;
		try {
		MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
				.getBean("maintenanceEventMaintenanceManager");
		result = maintenanceEventMaintenanceManager.isRecordRelatedToMaintenanceEvent(recordId, facilityId);
		
	} catch (SwtException ex) {
		// log error message
		log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
		// Re-throw as SwtException
		throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
	}
		return result;
	}
	
	

			
}
