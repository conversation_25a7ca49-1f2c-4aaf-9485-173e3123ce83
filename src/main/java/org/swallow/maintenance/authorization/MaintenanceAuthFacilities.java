package org.swallow.maintenance.authorization;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.type.CollectionType;
import org.codehaus.jackson.map.type.TypeFactory;
import org.codehaus.jackson.type.JavaType;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.*;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.CriticalPaymentTypeManager;
import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import org.swallow.pcm.maintenance.model.AccountGroup;
import org.swallow.pcm.maintenance.model.AccountGroupCutoff;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.pcm.maintenance.model.Reserve;
import org.swallow.pcm.maintenance.model.SpreadProcessPoint;
import org.swallow.pcm.maintenance.model.SpreadProfile;
import org.swallow.pcm.maintenance.model.StopRule;
import org.swallow.pcm.maintenance.service.AccountGroupsMaintenanceManager;
import org.swallow.pcm.maintenance.service.SpreadProfilesMaintenanceManager;
import org.swallow.pcm.maintenance.service.StopRulesMaintenanceManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;

public class MaintenanceAuthFacilities {

	private static final Log log = LogFactory.getLog(MaintenanceAuthFacilities.class);

	public static boolean doAcceptPcmAccountGroupEvent(MaintenanceEvent event) throws SwtException {
		AccountGroup accountGroupsDetails;
		boolean result = false;
		if (event != null && ("U".equalsIgnoreCase(event.getAction()) || "I".equalsIgnoreCase(event.getAction()))) {

			ArrayList<Reserve> listReserveAdd = null;
			ArrayList<Reserve> listReserveUpdate = null;
			ArrayList<Reserve> listReserveDelete = null;

			ArrayList<AccountGroupCutoff> listCutoffAdd = null;
			ArrayList<AccountGroupCutoff> listCutoffUpdate = null;
			ArrayList<AccountGroupCutoff> listCutoffUpdateIterate;

			ArrayList<AccountGroupCutoff> listCutoffDelete = null;
			ArrayList<AcctMaintenance> listAccountAdd = null;
			ArrayList<AcctMaintenance> listAccountDelete;

			TypeFactory typeFactory = null;
			CollectionType collectionType = null;
			JavaType mapType = null;

			ArrayList<AccountInGroup> listInGroupAccountAdd = null;
			ArrayList<AccountInGroup> listInGroupAccountDelete = null;

			try {
				AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
						.getBean("accountGroupsMaintenanceManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP");
				ObjectMapper objectMapper = new ObjectMapper();

				if (details != null) {
					String json = details.getNewState();

					accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);

					accountGroupsDetails.setMainEventId(null);
					accountGroupsDetails.setRequireAuthorisation(null);
					accountGroupsMaintenanceManager.updateAcctGroup(accountGroupsDetails);
					accountGroupsMaintenanceManager.updateAcctGroup(accountGroupsDetails);
				}

				// Get list of liquidity from P_Reserve
//			reserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);

				MaintenanceEventDetails detailsReserve = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP_RESERVE");

				if (details != null) {
					String jsonReserve = detailsReserve.getNewState();
					// HashMap<String, ArrayList<Reserve>> reserveMap =
					// objectMapper.readValue(jsonReserve, HashMap.class);

					// ObjectMapper objectMapper = new ObjectMapper();
					typeFactory = objectMapper.getTypeFactory();
					collectionType = typeFactory.constructCollectionType(ArrayList.class, Reserve.class);
					mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class),
							collectionType);
					HashMap<String, ArrayList<Reserve>> reserveMap = objectMapper.readValue(jsonReserve, mapType);

					listReserveAdd = reserveMap.get("add");
					listReserveUpdate = reserveMap.get("update");
					listReserveDelete = reserveMap.get("delete");

				}
				// Delete existing Reserve records
				MaintenanceEventDetails detailsCutoff = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP_CUT_OFF");
				if (detailsCutoff != null) {
					String jsonCutoff = detailsCutoff.getNewState();

					typeFactory = objectMapper.getTypeFactory();
					collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountGroupCutoff.class);
					mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class),
							collectionType);
					HashMap<String, ArrayList<AccountGroupCutoff>> cutOffMap = objectMapper.readValue(jsonCutoff,
							mapType);

					listCutoffAdd = cutOffMap.get("add");
					listCutoffUpdate = cutOffMap.get("update");
					listCutoffDelete = cutOffMap.get("delete");

				}
				MaintenanceEventDetails detailsAccountInGroup = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_IN_GROUP");

				if (detailsAccountInGroup != null) {
					String jsonAccountInGroup = detailsAccountInGroup.getNewState();

					typeFactory = objectMapper.getTypeFactory();
					collectionType = typeFactory.constructCollectionType(ArrayList.class, AccountInGroup.class);
					mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class),
							collectionType);
					HashMap<String, ArrayList<AccountInGroup>> accountMap = objectMapper.readValue(jsonAccountInGroup,
							mapType);

					listInGroupAccountAdd = accountMap.get("add");
					listInGroupAccountDelete = accountMap.get("delete");
				}

				accountGroupsMaintenanceManager.crudReserve(listReserveAdd, listReserveUpdate, listReserveDelete,
						null);
				accountGroupsMaintenanceManager.crudCutOff(listCutoffAdd, listCutoffUpdate, listCutoffDelete, null);
				accountGroupsMaintenanceManager.crudAccountsInGroup(listInGroupAccountAdd, listInGroupAccountDelete,
						null);

				result = true;

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
			}

		} else {

			try {
				AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
						.getBean("accountGroupsMaintenanceManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP");
				ObjectMapper objectMapper = new ObjectMapper();

				if (details != null) {
					String json = details.getNewState();
					accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);
					accountGroupsDetails.setMainEventId(null);
					accountGroupsMaintenanceManager.deleteAcctGroup(accountGroupsDetails);
				}

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
			}

			result = true;
		}
		return result;

	}
	
	public static boolean doAcceptSpreadRulesEvent(MaintenanceEvent event) throws SwtException {
		SpreadProfile spreadProfile;
		boolean result = false;
		if (event != null && ("U".equalsIgnoreCase(event.getAction()) || "I".equalsIgnoreCase(event.getAction()))) {

			ArrayList<SpreadProcessPoint> listSpreadProcessPointAdd = null;
			ArrayList<SpreadProcessPoint> listSpreadProcessPointUpdate = null;
			ArrayList<SpreadProcessPoint> listSpreadProcessPointDelete = null;


			TypeFactory typeFactory = null;
			CollectionType collectionType = null;
			JavaType mapType = null;

			ArrayList<AccountInGroup> listInGroupAccountAdd = null;
			ArrayList<AccountInGroup> listInGroupAccountDelete = null;

			try {
				SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil
						.getBean("spreadProfilesMaintenanceManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "SPREAD_PROFILE");
				ObjectMapper objectMapper = new ObjectMapper();

				if (details != null) {
					String json = details.getNewState();

					spreadProfile = objectMapper.readValue(json, SpreadProfile.class);

					spreadProfile.setMainEventId(null);
					if ("I".equalsIgnoreCase(event.getAction())) {
						spreadProfile.setRequireAuthorisation(null);
						spreadProfilesMaintenanceManager.updateSpreadProfile(spreadProfile);
						// accountIdAfterSave = acctGroup.getId().getAccGrpId();

					} else {
						spreadProfilesMaintenanceManager.updateSpreadProfile(spreadProfile);
					}
				}

				// Get list of liquidity from P_Reserve
//			reserveList = (ArrayList<Reserve>) accountGroupsMaintenanceManager.getReserveDetails(accountGroupId);

				MaintenanceEventDetails detailsReserve = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "SPREAD_PROCESS_POINT");

				if (details != null) {
					String jsonReserve = detailsReserve.getNewState();
					// HashMap<String, ArrayList<Reserve>> reserveMap =
					// objectMapper.readValue(jsonReserve, HashMap.class);

					// ObjectMapper objectMapper = new ObjectMapper();
					typeFactory = objectMapper.getTypeFactory();
					collectionType = typeFactory.constructCollectionType(ArrayList.class, SpreadProcessPoint.class);
					mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class),
							collectionType);
					HashMap<String, ArrayList<SpreadProcessPoint>> reserveMap = objectMapper.readValue(jsonReserve, mapType);

					listSpreadProcessPointAdd = reserveMap.get("add");
					listSpreadProcessPointUpdate = reserveMap.get("update");
					listSpreadProcessPointDelete = reserveMap.get("delete");
					spreadProfilesMaintenanceManager.crudSpreadProcessPoints(listSpreadProcessPointAdd, listSpreadProcessPointUpdate, listSpreadProcessPointDelete, null);
				}

				result = true;

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
			}

		} else {

			try {
				SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil
						.getBean("spreadProfilesMaintenanceManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "SPREAD_PROFILE");
				ObjectMapper objectMapper = new ObjectMapper();

				if (details != null) {
					String json = details.getNewState();
					spreadProfile = objectMapper.readValue(json, SpreadProfile.class);
					spreadProfilesMaintenanceManager.deleteSpreadProfile(spreadProfile.getId().getSpreadProfileId(), null);
				}

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
			}

			result = true;
		}
		return result;

	}
	
	
	
	public static boolean doAcceptPcmStopRulesEvent(MaintenanceEvent event) throws SwtException {
		boolean result = false;
		
		StopRule stopRule = null;
		try {
			StopRulesMaintenanceManager stopRulesMaintenanceManager = (StopRulesMaintenanceManager) SwtUtil
					.getBean("stopRulesMaintenanceManager");

			MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
					.getBean("maintenanceEventMaintenanceManager");

			MaintenanceEventDetails details = maintenanceEventMaintenanceManager
					.getMaintenanceEventDetails("" + event.getMaintEventId(), "STOP_RULE");
			ObjectMapper objectMapper = new ObjectMapper();

			if (details != null) {
				String json = details.getNewState();

				stopRule = objectMapper.readValue(json, StopRule.class);

				stopRule.setMainEventId(null);
				if ("I".equalsIgnoreCase(event.getAction())) {
					stopRulesMaintenanceManager.saveStopRule(stopRule, true);

				} else if ("U".equalsIgnoreCase(event.getAction())) {
					stopRulesMaintenanceManager.saveStopRule(stopRule, false);
				}else {
					stopRulesMaintenanceManager.deleteStopRule(stopRule.getId().getStopRuleId(), null);
				}
			}
		}catch(Exception e) {
			
		}
		result = true;
		
		
		
		return result;
	}

	public static boolean doRejectPcmAccountGroupEvent(MaintenanceEvent event) throws SwtException {
		AccountGroup accountGroupsDetails;
		boolean result = false;

		try {
			if (event != null)
			/*	if("U".equalsIgnoreCase(event.getAction())) {
					AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
							.getBean("accountGroupsMaintenanceManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP");
					ObjectMapper objectMapper = new ObjectMapper();

					if (details != null) {
						String json = details.getNewState();
						accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);
					}
					
				}else*/
					
				if( "I".equalsIgnoreCase(event.getAction())){
					AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
							.getBean("accountGroupsMaintenanceManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP");
					ObjectMapper objectMapper = new ObjectMapper();

					if (details != null) {
						String json = details.getNewState();
						accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);
						accountGroupsDetails.setMainEventId(null);
						accountGroupsDetails.setForceNoLogs(true);
						accountGroupsMaintenanceManager.deleteAcctGroup(accountGroupsDetails);
					}

				}

		} catch (SwtException | IOException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
		}
		return result;
	}
	
	
	public static boolean doRejectPcmSpreadProfileEvent(MaintenanceEvent event) throws SwtException {
		SpreadProfile spreadProfile;
		boolean result = false;

		try {
			if (event != null)
			/*	if("U".equalsIgnoreCase(event.getAction())) {
					AccountGroupsMaintenanceManager accountGroupsMaintenanceManager = (AccountGroupsMaintenanceManager) SwtUtil
							.getBean("accountGroupsMaintenanceManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "ACCOUNT_GROUP");
					ObjectMapper objectMapper = new ObjectMapper();

					if (details != null) {
						String json = details.getNewState();
						accountGroupsDetails = objectMapper.readValue(json, AccountGroup.class);
					}
					
				}else*/
					
				if( "I".equalsIgnoreCase(event.getAction())){
					SpreadProfilesMaintenanceManager spreadProfilesMaintenanceManager = (SpreadProfilesMaintenanceManager) SwtUtil
							.getBean("spreadProfilesMaintenanceManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "SPREAD_PROFILE");
					ObjectMapper objectMapper = new ObjectMapper();
					if (details != null) {
						String json = details.getNewState();
						spreadProfile = objectMapper.readValue(json, SpreadProfile.class);
						spreadProfile.setMainEventId(null);
						spreadProfile.setForceNoLogs(true);
						spreadProfilesMaintenanceManager.deleteSpreadProfile(spreadProfile.getId().getSpreadProfileId(),null);
					}

				}

		} catch (SwtException | IOException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
		}
		return result;
	}
	
	
	
	public static boolean doRejectPcmStopRulesEvent(MaintenanceEvent event) throws SwtException {
		StopRule stopRule;
		boolean result = true;
		
		//Nothing to be done here as no roll back is delete record is needed

		/*try {
			if (event != null)
				if("U".equalsIgnoreCase(event.getAction())) {
					
				}else
					
				if( "I".equalsIgnoreCase(event.getAction())){
					StopRulesMaintenanceManager stopRulesMaintenanceManager = (StopRulesMaintenanceManager) SwtUtil
							.getBean("stopRulesMaintenanceManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "STOP_RULE");
					ObjectMapper objectMapper = new ObjectMapper();

					if (details != null) {
						String json = details.getNewState();
						stopRule = objectMapper.readValue(json, StopRule.class);
						stopRule.setMainEventId(null);
						stopRule.setForceNoLogs(true);
						stopRulesMaintenanceManager.deleteStopRule(stopRule.getId().getStopRuleId(), false)
					}

				}

		} catch (SwtException | IOException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "getEntityDomesticCurrency", SwtUtil.class);
		}*/
		return result;
	}


	public static boolean doAcceptCriticalPayTypeEvent(MaintenanceEvent event) throws SwtException {
		CriticalPaymentType criticalPaymentType;
		boolean result = false;
		if (event != null && ("U".equalsIgnoreCase(event.getAction()) || "I".equalsIgnoreCase(event.getAction()))) {

			ArrayList<CriticalPaymentExp> listCriticalPaymentExpAdd = null;
			ArrayList<CriticalPaymentExp> listCriticalPaymentExpUpdate = null;
			ArrayList<CriticalPaymentExp> listCriticalPaymentExpDelete = null;


			TypeFactory typeFactory = null;
			CollectionType collectionType = null;
			JavaType mapType = null;

			ArrayList<AccountInGroup> listInGroupAccountAdd = null;
			ArrayList<AccountInGroup> listInGroupAccountDelete = null;

			try {
				CriticalPaymentTypeManager criticalPaymentTypeManager = (CriticalPaymentTypeManager) SwtUtil
						.getBean("criticalPaymentTypeManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "CRIT_PAYMENT_TYPE_DETAILS");
				ObjectMapper objectMapper = new ObjectMapper();
				if (details != null) {
					String json = details.getNewState();
					criticalPaymentType = objectMapper.readValue(json, CriticalPaymentType.class);

					criticalPaymentType.setMainEventId(null);
					if ("I".equalsIgnoreCase(event.getAction())) {
						criticalPaymentType.setRequireAuthorisation(null);
						criticalPaymentTypeManager.saveCriticalPaymentType(criticalPaymentType, "save");

					} else {
						criticalPaymentTypeManager.saveCriticalPaymentType(criticalPaymentType, "update");
					}
				}



				MaintenanceEventDetails detailsReserve = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "CRITICAL_PAYMENT_EXP");

				if (details != null) {
					String jsonReserve = detailsReserve.getNewState();
					typeFactory = objectMapper.getTypeFactory();
					collectionType = typeFactory.constructCollectionType(ArrayList.class, CriticalPaymentExp.class);
					mapType = typeFactory.constructMapType(HashMap.class, typeFactory.constructType(String.class),
							collectionType);
					HashMap<String, ArrayList<CriticalPaymentExp>> reserveMap = objectMapper.readValue(jsonReserve, mapType);

					listCriticalPaymentExpAdd = reserveMap.get("add");
					listCriticalPaymentExpUpdate = reserveMap.get("update");
					listCriticalPaymentExpDelete = reserveMap.get("delete");
					criticalPaymentTypeManager.crudCriticalPayExp(listCriticalPaymentExpAdd, listCriticalPaymentExpUpdate, listCriticalPaymentExpDelete, null);
				}

				result = true;

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [doAcceptCriticalPayTypeEvent] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "doAcceptCriticalPayTypeEvent", SwtUtil.class);
			}

		} else {

			try {
				CriticalPaymentTypeManager criticalPaymentTypeManager = (CriticalPaymentTypeManager) SwtUtil
						.getBean("criticalPaymentTypeManager");

				MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
						.getBean("maintenanceEventMaintenanceManager");

				MaintenanceEventDetails details = maintenanceEventMaintenanceManager
						.getMaintenanceEventDetails("" + event.getMaintEventId(), "CRIT_PAYMENT_TYPE_DETAILS");
				ObjectMapper objectMapper = new ObjectMapper();

				if (details != null) {
					String json = details.getNewState();
					criticalPaymentType = objectMapper.readValue(json, CriticalPaymentType.class);
					criticalPaymentType.setMainEventId(null);
					criticalPaymentTypeManager.deleteCriticalPayType(criticalPaymentType);
				}

			} catch (SwtException | IOException ex) {
				// log error message
				log.error("SwtUtil - [doAcceptCriticalPayTypeEvent] - Exception: " + ex.getMessage());
				// Re-throw as SwtException
				throw SwtErrorHandler.getInstance().handleException(ex, "doAcceptCriticalPayTypeEvent", SwtUtil.class);
			}

			result = true;
		}
		return result;

	}


	public static boolean doRejectCriticalPayTypeEvent(MaintenanceEvent event) throws SwtException {
		CriticalPaymentType criticalPayType;
		boolean result = false;

		try {
			if (event != null)
				if( "I".equalsIgnoreCase(event.getAction())){
					CriticalPaymentTypeManager criticalPaymentTypeManager = (CriticalPaymentTypeManager) SwtUtil
							.getBean("criticalPaymentTypeManager");

					MaintenanceEventMaintenanceManager maintenanceEventMaintenanceManager = (MaintenanceEventMaintenanceManager) SwtUtil
							.getBean("maintenanceEventMaintenanceManager");

					MaintenanceEventDetails details = maintenanceEventMaintenanceManager
							.getMaintenanceEventDetails("" + event.getMaintEventId(), "CRIT_PAYMENT_TYPE");
					ObjectMapper objectMapper = new ObjectMapper();
					if (details != null) {
						String json = details.getNewState();
						criticalPayType = objectMapper.readValue(json, CriticalPaymentType.class);
						criticalPayType.setMainEventId(null);
						criticalPayType.setForceNoLogs(true);
						criticalPaymentTypeManager.deleteCriticalPayType(criticalPayType);
					}

				}

		} catch (SwtException | IOException ex) {
			// log error message
			log.error("SwtUtil - [doRejectCriticalPayTypeEvent] - Exception: " + ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex, "doRejectCriticalPayTypeEvent", SwtUtil.class);
		}
		return result;
	}




}
