/*
 * @(#)CurrencyInterestDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyInterest;

public interface CurrencyInterestDAO extends DAO {
	/**
	 * This method is used to save CurrencyInterest object.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException;

	/**
	 * This method is used to delete currencyInterest object from database.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void deleteCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException;

	/**
	 * This method is used to update currencyInterest object in database.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyInterestDetail(CurrencyInterest currencyInterest)
			throws SwtException;

	/**
	 * This method is used to extract CurrencyInterest object from database for
	 * given date.
	 * 
	 * @param entity
	 * @param hostId
	 * @param currencyCode
	 * @param fromDate
	 * @param toDate
	 * @param dateFormat
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CurrencyInterest> getCurrencyInterestList(String entity,
			String hostId, String currencyCode, String fromDate, String toDate,
			String dateFormat) throws SwtException;

	/**
	 * This method is used to extract currency name for given code.
	 * 
	 * @param currencyCode
	 * @return String
	 * @throws SwtException
	 */
	public String getCurrencyName(String currencyCode) throws SwtException;
}
