/*
 * @(#)DefaultAcctMaintenanceDAOHibernate.java 1.0 06/07/03
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/**
 * This is DAO class for Default Account screen 
 *
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.DefaultAcctMaintenanceDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.DefaultAcct;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Repository ("defaultAcctMaintenanceDAO")
@Transactional
public class DefaultAcctMaintenanceDAOHibernate extends CustomHibernateDaoSupport
		implements DefaultAcctMaintenanceDAO {
	public DefaultAcctMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	private final Log log = LogFactory
			.getLog(DefaultAcctMaintenanceDAOHibernate.class);

	/**
	 * Queries hibernate for a collection of defaultAcct objects matching the
	 * supplied entityId and hostId parameters
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId) throws SwtException {
		log.debug("Entering DefaultAcctMaintenanceDAOHibernate.getList()");
		List<DefaultAcct> list = new ArrayList<>();
		SwtInterceptor interceptor = null;

		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			try (Session session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession()) {

				TypedQuery<DefaultAcct> query = session.createQuery(
						"from DefaultAcct acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId " +
						"order by acct.accountMaster.id.accountId", DefaultAcct.class);
				query.setParameter("entityId", entityId);
				query.setParameter("hostId", hostId);

				list = query.getResultList();
			}

		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e, "getList",
					DefaultAcctMaintenanceDAOHibernate.class);
			throw swtexp;
		}

		return list;
	}

	/**
	 * Queries hibernate for a unique record matching the entityId, hostId,
	 * xrefCode and currencyCode contained in the given DefaultAcct object
	 * 
	 * @param acct
	 * @return DefaultAcct
	 * @throws SwtException
	 */
	public DefaultAcct getRecord(DefaultAcct defaultAcct) throws SwtException {
		DefaultAcct acct = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [getRecord] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			try (Session session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession()) {

				TypedQuery<DefaultAcct> query = session.createQuery(
						"FROM DefaultAcct da WHERE da.id.entityId = :entityId AND da.id.hostId = :hostId " +
						"AND da.id.currencyCode = :currencyCode AND da.id.xrefCode = :xrefCode",
						DefaultAcct.class);

				query.setParameter("entityId", defaultAcct.getId().getEntityId());
				query.setParameter("hostId", defaultAcct.getId().getHostId());
				query.setParameter("currencyCode", defaultAcct.getId().getCurrencyCode());
				query.setParameter("xrefCode", defaultAcct.getId().getXrefCode());

				List<DefaultAcct> results = query.getResultList();
				Iterator<DefaultAcct> itr = results.iterator();
				while (itr.hasNext()) {
					acct = itr.next();
				}
			}

			return acct;

		} catch (Exception exception) {
			log.error(this.getClass().getName() + " - [getRecord] - Exception -" + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception, "getRecord", DefaultAcctMaintenanceDAOHibernate.class);
		} finally {
			log.debug(this.getClass().getName() + " - [getRecord] - Exit");
		}
	}


	/**
	 * Deletes the unique account matching the PK data in the given DefaultAcct
	 * object
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteRecord(DefaultAcct acct) throws SwtException {
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			log.debug(this.getClass().getName() + " - [deleteRecord] - Enter");

			try (Session session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession()) {

				tx = session.beginTransaction();
				session.delete(getRecord(acct));
				tx.commit();
			}

			log.debug(this.getClass().getName() + " - [deleteRecord] - Exit");

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [deleteRecord] method - " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			throw swtErrorHandler.handleException(e, "deleteRecord", DefaultAcctMaintenanceDAOHibernate.class);
		}
	}
	public Collection getAccountList(String hostId, String entityId, String currencyCode) throws SwtException {
		List list = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getAccountList] - Enter");

			String hql;
			TypedQuery<AcctMaintenance> query;

			if (currencyCode.equalsIgnoreCase("all")) {
				hql = "from AcctMaintenance a where a.id.hostId = :hostId and a.id.entityId = :entityId order by a.id.accountId";
				query = session.createQuery(hql, AcctMaintenance.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
			} else {
				hql = "from AcctMaintenance a where a.id.hostId = :hostId and a.id.entityId = :entityId and a.currcode = :currencyCode order by a.id.accountId";
				query = session.createQuery(hql, AcctMaintenance.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
			}

			list = query.getResultList();

			log.debug(this.getClass().getName() + " - [getAccountList] - Exit");
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e, "getAccountList", DefaultAcctMaintenanceDAOHibernate.class);
			throw swtexp;
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	public void saveRecord(DefaultAcct acct) throws SwtException {
		List records = null;
		SwtInterceptor interceptor = null;
		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {

			log.debug(this.getClass().getName() + " - [saveRecord] - Entering");

			String hql = "from DefaultAcct acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId "
						 + "and acct.id.currencyCode = :currencyCode and acct.id.xrefCode = :xrefCode";

			TypedQuery<DefaultAcct> query = session.createQuery(hql, DefaultAcct.class);
			query.setParameter("hostId", acct.getId().getHostId());
			query.setParameter("entityId", acct.getId().getEntityId());
			query.setParameter("currencyCode", acct.getId().getCurrencyCode());
			query.setParameter("xrefCode", acct.getId().getXrefCode());

			records = query.getResultList();

			if (records.size() == 0) {
				Transaction tx = session.beginTransaction();
				session.save(acct); // staying with save as you prefer
				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			log.debug(this.getClass().getName() + " - [saveRecord] - Exiting");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [saveRecord] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveRecord", DefaultAcctMaintenanceDAOHibernate.class);
		}
	}


	/**
	 * Updates the persistent storage with the existing DefaultAcct object
	 * given. The PK data contained therein is used to locate the record to
	 * update.
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void updateRecord(DefaultAcct acct) throws SwtException {
		SwtInterceptor interceptor = null;
		Transaction tx = null;

		try (Session session = getHibernateTemplate().getSessionFactory()
				.withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
				.openSession()) {

			log.debug(this.getClass().getName() + " - [updateRecord] - Enter");

			tx = session.beginTransaction();
			session.update(acct); // Keeping update as per your instruction
			tx.commit();

			log.debug(this.getClass().getName() + " - [updateRecord] - Exit");

		} catch (Exception e) {
			if (tx != null) {
				tx.rollback();
			}
			log.error(this.getClass().getName()
					  + " - Exception Catched in [updateRecord] method : - "
					  + e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"updateRecord", DefaultAcctMaintenanceDAOHibernate.class);
		}
	}

}