/*
 * @(#)GroupLevelDAOHibernate.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.GroupLevelDAO;
import org.swallow.maintenance.model.GroupLevel;
import org.swallow.maintenance.service.impl.MetaGroupManagerImpl;

@Component("groupLevelDAO")
@Repository
public class GroupLevelDAOHibernate extends CustomHibernateDaoSupport implements GroupLevelDAO {
    private final Log log = LogFactory.getLog(GroupLevelDAO.class);
    
    /**
     * Constructor with dependency injection
     * @param sessionfactory Hibernate session factory
     * @param entityManager JPA entity manager
     */
    public GroupLevelDAOHibernate(@Lazy SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    /**
     * Fetches group level details from P_GROUP table
     * @param hostId The host identifier
     * @param entityId The entity identifier
     * @return Collection of GroupLevel objects ordered by groupLevelId
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection<GroupLevel> getGroupLevelDetail(String hostId, String entityId) throws SwtException {
        log.debug(this.getClass().getName() + "- [getGroupLevelDetail] - Entering");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<GroupLevel> query = session.createQuery(
                "from GroupLevel c where c.id.entityId = :entityId and c.id.hostId = :hostId " +
                "order by c.id.groupLevelId",
                GroupLevel.class);
            
            query.setParameter("entityId", entityId);
            query.setParameter("hostId", hostId);
            
            List<GroupLevel> groupLevelCollection = query.getResultList();
            
            log.debug(this.getClass().getName() + "- [getGroupLevelDetail] - Exiting");
            return groupLevelCollection.isEmpty() ? new ArrayList<>() : groupLevelCollection;
            
        } catch (Exception e) {
            log.error(this.getClass().getName() + 
                " - Exception in [getGroupLevelDetail] method: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getGroupLevelDetail", 
                    MetaGroupManagerImpl.class);
        }
    }
}