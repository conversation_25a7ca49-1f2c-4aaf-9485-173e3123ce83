/*
 * @(#)EntityDAOHibernate.java 1.0 on Nov 4, 2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.maintenance.model.*;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.model.EntityProcess;
import org.swallow.control.model.EntityProcessStatus;
import org.swallow.control.model.Process;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
/**
 * EntityDAOHibernate.java
 * 
 * This is DAO class for Entity screen.
 * 
 */
@Component ("entityDAO")
@Repository
public class EntityDAOHibernate extends CustomHibernateDaoSupport implements
		EntityDAO {

	
	public EntityDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	private final Log log = LogFactory.getLog(EntityDAOHibernate.class);

	@SuppressWarnings("unchecked")
	public Entity getEntityDetail(Entity entity) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getEntityDetail] - Entry");

			String hql = "from Entity c where c.id.entityId = :entityId and c.id.hostId = :hostId";
			TypedQuery<Entity> query = session.createQuery(hql, Entity.class);
			query.setParameter("entityId", entity.getId().getEntityId());
			query.setParameter("hostId", entity.getId().getHostId());

			Entity result;
			try {
				result = query.getSingleResult(); // Corrected method
			} catch (NoResultException ex) {
				result = null; // Handle case where no entity is found
			}

			log.debug(this.getClass().getName() + " - [getEntityDetail] - Exit");
			return result;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getEntityDetail]: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}


	@SuppressWarnings("unchecked")
	public Collection<EntityMaster> getEntityList(String hostId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getEntityList] - Entry");

			String hql = "from EntityMaster c where c.id.hostId = :hostId order by c.id.entityId";
			TypedQuery<EntityMaster> query = session.createQuery(hql, EntityMaster.class);
			query.setParameter("hostId", hostId);

			List<EntityMaster> resultList = query.getResultList();
			log.debug(this.getClass().getName() + " - [getEntityList] - Exit");

			return resultList;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getEntityList]: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}


	/**
	 * This is used to update the records in entity table
	 * 
	 * @param entity
	 * @param coll
	 * @param colMeta
	 * @param collPosLvlDeleted
	 * @param collPosLvlAdded
	 * @param collPosLvlChanged
	 * @param collEditableData
	 * @param noofRecords
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void updateEntity(Entity entity, Collection coll,
			Collection collMeta, Collection collPosLvlDeleted,
			Collection collPosLvlAdded, Collection collPosLvlChanged,
			Collection collEditableData, int noofRecords) throws SwtException {

		// variable for iterator
		Iterator itr = null;
		// variable for string
		String deletePosLvlNameHQL = null;
		// variable for EntityPositionLevel
		EntityPositionLevel entityPositionLevel = null;
		// variable for Ediatabledata
		EditableData edit = null;
		// variable to hold the object
		Object obj = null;
		// Decleration for rowsdeleted
		int rowsdeleted = 0;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateEntity] - Entering ");

			// To get the position level list
			deletePosLvlNameHQL = "DELETE FROM EntityPositionLevel m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId " +
			        "AND m.id.positionLevel = :positionLevel";
			// update the entity
			// Code Modified for Mantis 1501 by Chinniah on1-Aug-2011:For
			// Changes to Central Bank Date From, Date To, CRR Limit are not
			// logged to the maintenance log
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			
			session.update(entity);
			// iterate the collection
			itr = coll.iterator();
			while (itr.hasNext()) {
				obj = itr.next();

				if (obj != null) {
					// To update the object
					// Code Modified for Mantis 1501 by Chinniah
					// on1-Aug-2011:For
					// Changes to Central Bank Date From, Date To, CRR Limit are
					// not
					// logged to the maintenance log
					session.update(obj);
				}
			}
			itr = collMeta.iterator();
			// iterate the collection
			while (itr.hasNext()) {
				obj = itr.next();

				if (obj != null) {
					// Code Modified for Mantis 1501 by Chinniah
					// on1-Aug-2011:For
					// Changes to Central Bank Date From, Date To, CRR Limit are
					// not
					// logged to the maintenance log
					session.update(obj);
				}
			}
			// to check the position level collection size
			if (collPosLvlDeleted.size() > 0) {

				itr = collPosLvlDeleted.iterator();
				// instance of EntityPositionLevel
				entityPositionLevel = new EntityPositionLevel();
				// iterate the collection
				while (itr.hasNext()) {
					entityPositionLevel = (EntityPositionLevel) itr.next();
					rowsdeleted = session.createQuery(deletePosLvlNameHQL)
				            .setParameter("hostId", entityPositionLevel.getId().getHostId())
				            .setParameter("entityId", entityPositionLevel.getId().getEntityId())
				            .setParameter("positionLevel", entityPositionLevel.getId().getPositionLevel())
				            .executeUpdate();

					if (rowsdeleted == 0) {
						// throw the exception
						throw new SwtRecordNotExist();
					}
				}
			}
			// To check the positionlevel added size
			if (collPosLvlAdded.size() > 0) {
				itr = collPosLvlAdded.iterator();
				// iterate the collection
				while (itr.hasNext()) {
					obj = itr.next();

					if (obj != null) {
						// save the obj
						// Code Modified for Mantis 1501 by Chinniah
						// on1-Aug-2011:For
						// Changes to Central Bank Date From, Date To, CRR Limit
						// are not
						// logged to the maintenance log
						session.save(obj);
					}
				}
			}
			// To check the positionlevel changed size
			if (collPosLvlChanged.size() > 0) {
				itr = collPosLvlChanged.iterator();
				// iterate the collection
				while (itr.hasNext()) {
					obj = itr.next();

					if (obj != null) {
						// update the obj
						// Code Modified for Mantis 1501 by Chinniah
						// on1-Aug-2011:For
						// Changes to Central Bank Date From, Date To, CRR Limit
						// are not
						// logged to the maintenance log
						session.update(obj);
					}
				}
			}

			if (noofRecords > 0) {
				itr = collEditableData.iterator();
				// iterate the collection
				while (itr.hasNext()) {
					obj = itr.next();
					if (obj != null) {
						// EditableData object
						edit = (EditableData) obj;
						// check the save flag
						if (edit.isSaveFlag())
							// to save the object
							// Code Modified for Mantis 1501 by Chinniah
							// on1-Aug-2011:For
							// Changes to Central Bank Date From, Date To, CRR
							// Limit are not
							// logged to the maintenance log
							session.save(obj);
						else
							// to update the object
							// Code Modified for Mantis 1501 by Chinniah
							// on1-Aug-2011:For
							// Changes to Central Bank Date From, Date To, CRR
							// Limit are not
							// logged to the maintenance log
							session.update(obj);
					}
				}

			} else if (noofRecords == 0) {
				itr = collEditableData.iterator();
				// iterate the collection
				while (itr.hasNext()) {
					obj = itr.next();

					if (obj != null) {
						// to save the object
						// Code Modified for Mantis 1501 by Chinniah
						// on1-Aug-2011:For
						// Changes to Central Bank Date From, Date To, CRR Limit
						// are not
						// logged to the maintenance log
						session.save(obj);
					}
				}
			}
			// Code Modified for Mantis 1501 by Chinniah on1-Aug-2011:For
			// Changes to Central Bank Date From, Date To, CRR Limit are not
			// logged to the maintenance log
			tx.commit();
			log.debug(this.getClass().getName()
					+ " - [updateEntity] - Exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateEntity] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateEntity", EntityDAOHibernate.class);
		} finally {
			JDBCCloser.close(session);
			// To null the local variable and close the session
			try {
				itr = null;
				deletePosLvlNameHQL = null;
				entityPositionLevel = null;
				edit = null;
				obj = null;
			} catch (Exception exception) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [updateEntity] method : - "
						+ exception.getMessage());
				throw SwtErrorHandler.getInstance().handleException(exception,
						"updateEntity", EntityDAOHibernate.class);
			}
		}

	}

	public Long getRecordsFromEditableData(String hostId, String entityId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getRecordsFromEditableData] - Entry");

			String hql = "select count(*) from EditableData c where c.id.hostId = :hostId and c.id.entityId = :entityId";
			TypedQuery<Long> query = session.createQuery(hql, Long.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			Long noOfRecords = query.getSingleResult();
			log.debug(this.getClass().getName() + " - [getRecordsFromEditableData] - Exit");
			return noOfRecords != null ? noOfRecords : 0L;  // Handle null case safely
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getRecordsFromEditableData]: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}


	/**
	 * This is used to save the records in s_ROLE table
	 * 
	 * @param entity
	 * @param coll
	 * @param colMeta
	 * @param posLevelNameList
	 * @param collMeta
	 * @param posLevelNameList
	 * @param editableData
	 * @return none
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveEntity(Entity entity, Collection coll, Collection collMeta,
						   Collection posLevelNameList, Collection editableData) throws SwtException {

		List<Entity> records = null;
		SwtInterceptor interceptor = null;

		try (Session session = getHibernateTemplate().getSessionFactory()
				.withOptions().interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {

			log.debug(this.getClass().getName() + " - [saveEntity] - Entering");
			Transaction tx = session.beginTransaction();

			// Check if entity already exists
			String hql = "from Entity c where c.id.hostId = :hostId and c.id.entityId = :entityId";
			TypedQuery<Entity> query = session.createQuery(hql, Entity.class);
			query.setParameter("hostId", entity.getId().getHostId());
			query.setParameter("entityId", entity.getId().getEntityId());
			records = query.getResultList();

			if (records.isEmpty()) {
				session.save(entity);

				saveCollection(session, coll);
				saveCollection(session, collMeta);
				saveCollection(session, posLevelNameList);
				saveCollection(session, editableData);

				Collection<Process> processColl = getProcess();
				for (Process entPro : processColl) {
					EntityProcess entProcess = new EntityProcess();
					entProcess.getId().setEntityId(entity.getId().getEntityId());
					entProcess.getId().setProcessName(entPro.getId().getProcessName());
					session.save(entProcess);
				}

				tx.commit();
			} else {
				throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}

			log.debug(this.getClass().getName() + " - [saveEntity] - Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [saveEntity] - Exception " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	/**
	 * Helper method to save collections in batches.
	 */
	private void saveCollection(Session session, Collection collection) {
		if (collection != null) {
			int count = 0;
			for (Object obj : collection) {
				if (obj != null) {
					session.save(obj);
					count++;
					if (count % 50 == 0) { // Batch processing to improve performance
						session.flush();
						session.clear();
					}
				}
			}
		}
	}


	/**
	 * @desc This method deletes the metaGroup, Group , PositionLevelName ,
	 *       Entity Objects
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Objects
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param collPosLevelName -
	 *            Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public void deleteEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection collPosLevelName,
			Collection collEditableFields) throws SwtException {

		/* Methods local variable declaration */
		// Variable to hold entity process list
		Collection<EntityProcess> entityProList = null;
		// Variable to hold i
		int i = 1;
		// Variable to hold j
		int j = 1;
		// Variable to hold rows deleted
		int rowsdeleted = 0;
		// Variable to hold iterator
		Iterator itr = null;
		// Variable to hold deleteMetaGrpLvlHQL
		String deleteMetaGrpLvlHQL = null;
		// Variable to hold deleteGrpLvlHQL
		String deleteGrpLvlHQL = null;
		// Variable to hold deletePosLvlNameHQL
		String deletePosLvlNameHQL = null;
		// Variable to hold deleteEntityHQL
		String deleteEntityHQL = null;
		// object to hold entity position level
		EntityPositionLevel entityPositionLevel = null;
		// object to hold Group level
		GroupLevel grpLevel = null;
		// object to hold Counter
		Integer counter = null;
		// object to hold Meta Group Level
		MetaGroupLevel metaGrpLevel = null;
		// object to hold Editable Data
		EditableData edit = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteEntityDetails] - "
					+ "Entry");
			// to fetch the meta group data
			itr = metaGroupLevel.iterator();
			/* To write the Hibernate query */
			deleteMetaGrpLvlHQL = "DELETE FROM MetaGroupLevel m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId " +
			        "AND m.id.mgrpLvlCode = :mgrpLvlCode";
			/* To write the Hibernate query */
			deleteGrpLvlHQL = "DELETE FROM GroupLevel m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId " +
			        "AND m.id.groupLevelId = :groupLevelId";
			/* To write the Hibernate query */
			deletePosLvlNameHQL = "DELETE FROM EntityPositionLevel m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId " +
			        "AND m.id.positionLevel = :positionLevel";
			/* To write the Hibernate query */
			deleteEntityHQL = "DELETE FROM Entity m " +
			        "WHERE m.id.hostId = :hostId " +
			        "AND m.id.entityId = :entityId";
			// To fetch the meta group data
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			while (itr.hasNext()) {
				counter = new Integer(j);
				metaGrpLevel = (MetaGroupLevel) (itr.next());
				metaGrpLevel.getId().setEntityId(entity.getId().getEntityId());
				metaGrpLevel.getId().setHostId(entity.getId().getHostId());
				metaGrpLevel.getId().setMgrpLvlCode(counter);
				 rowsdeleted = session.createQuery(deleteMetaGrpLvlHQL)
				            .setParameter("hostId", metaGrpLevel.getId().getHostId())
				            .setParameter("entityId", metaGrpLevel.getId().getEntityId())
				            .setParameter("mgrpLvlCode", metaGrpLevel.getId().getMgrpLvlCode())
				            .executeUpdate();

				if (rowsdeleted == 0) {
					throw new SwtRecordNotExist();
				}

				j = j + 1;
			}

			itr = groupLevel.iterator();
			// To fetch the meta group level data
			while (itr.hasNext()) {
				counter = new Integer(i);
				grpLevel = (GroupLevel) (itr.next());
				grpLevel.getId().setEntityId(entity.getId().getEntityId());
				grpLevel.getId().setHostId(entity.getId().getHostId());
				grpLevel.getId().setGroupLevelId(counter);
				rowsdeleted = session.createQuery(deleteGrpLvlHQL)
			            .setParameter("hostId", grpLevel.getId().getHostId())
			            .setParameter("entityId", grpLevel.getId().getEntityId())
			            .setParameter("groupLevelId", grpLevel.getId().getGroupLevelId())
			            .executeUpdate();

				if (rowsdeleted == 0) {
					throw new SwtRecordNotExist();
				}

				i = i + 1;
			}

			itr = collPosLevelName.iterator();
			// to get the entity position level object
			entityPositionLevel = new EntityPositionLevel();
			// To read the entity position level data
			while (itr.hasNext()) {
				entityPositionLevel = (EntityPositionLevel) itr.next();
				 rowsdeleted = session.createQuery(deletePosLvlNameHQL)
				            .setParameter("hostId", entityPositionLevel.getId().getHostId())
				            .setParameter("entityId", entityPositionLevel.getId().getEntityId())
				            .setParameter("positionLevel", entityPositionLevel.getId().getPositionLevel())
				            .executeUpdate();

				if (rowsdeleted == 0) {
					throw new SwtRecordNotExist();
				}
			}
			// To iterator the editable field data
			itr = collEditableFields.iterator();
			// To read the editable field data
			while (itr.hasNext()) {
				edit = (EditableData) (itr.next());
				session.delete(edit);
			}

			rowsdeleted = session.createQuery(deleteEntityHQL)
		            .setParameter("hostId", entity.getId().getHostId())
		            .setParameter("entityId", entity.getId().getEntityId())
		            .executeUpdate();

			if (rowsdeleted == 0) {
				throw new SwtRecordNotExist();
			}

			entityProList = getEntityProcess(entity.getId().getEntityId());
			for (EntityProcess entPro : entityProList) {
				session.delete(entPro);
			}
			deleteEntProStatus(entity.getId().getEntityId());

			tx.commit();
			log.debug(this.getClass().getName() + " - [deleteEntityDetails] - "
					+ "Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteEntityDetails] - Exception " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"deleteEntityDetails", EntityDAOHibernate.class);
			throw swtExp;
		} finally {
			JDBCCloser.close(session);
			/* To set null value for local variable */
			itr = null;
			deleteMetaGrpLvlHQL = null;
			deleteGrpLvlHQL = null;
			deletePosLvlNameHQL = null;
			deleteEntityHQL = null;
			entityProList = null;
			entityPositionLevel = null;
			grpLevel = null;
			counter = null;
			metaGrpLevel = null;
			edit = null;
		}
	}

	/**
	 * This method is used to delete given Enity from DB.
	 * 
	 * @param Entity
	 *            Object
	 */
	public void deleteEntity(Entity entity) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteEntity] - "
					+ "Entry");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(getEntityDetail(entity));
			tx.commit();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteEntity] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteEntity", EntityDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}

	}

	/**
	 * This method is used to extract the currecny details for the given hostId
	 * and entityId.
	 * 
	 * @param hostId
	 * @param EntityId
	 */
	@SuppressWarnings("unchecked")
	public Collection getCurrencyDetail(String hostId, String entityId) throws SwtException {
		log.debug("entering getCurrencyDetail " + hostId + " " + entityId);

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Entity c where c.id.hostId = :hostId and c.id.entityId = :entityId";
			TypedQuery<Entity> query = session.createQuery(hql, Entity.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<Entity> list = query.getResultList();
			log.debug("exiting getCurrencyDetail");
			return list;
		} catch (Exception e) {
			log.error("Exception in getCurrencyDetail: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public Collection getGroupLevel(String hostId, String entityId) throws SwtException {
		log.debug("entering getGroupLevel");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from GroupLevel c where c.id.hostId = :hostId and c.id.entityId = :entityId";
			TypedQuery<GroupLevel> query = session.createQuery(hql, GroupLevel.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<GroupLevel> list = query.getResultList();
			log.debug("exiting getGroupLevel");
			return list;
		} catch (Exception e) {
			log.error("Exception in getGroupLevel: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public Collection getMetaGroupLevel(String hostId, String entityId) throws SwtException {
		log.debug("entering getMetaGroupLevel");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from MetaGroupLevel c where c.id.hostId = :hostId and c.id.entityId = :entityId";
			TypedQuery<MetaGroupLevel> query = session.createQuery(hql, MetaGroupLevel.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<MetaGroupLevel> list = query.getResultList();
			log.debug("exiting getMetaGroupLevel");
			return list;
		} catch (Exception e) {
			log.error("Exception in getMetaGroupLevel: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public Collection getEntityPositionLevels(String hostId, String entityId) throws SwtException {
		log.debug("entering getEntityPositionLevels");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from EntityPositionLevel c where c.id.hostId = :hostId and c.id.entityId = :entityId order by c.id.positionLevel asc";
			TypedQuery<EntityPositionLevel> query = session.createQuery(hql, EntityPositionLevel.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<EntityPositionLevel> list = query.getResultList();
			log.debug("exiting getEntityPositionLevels");
			return list;
		} catch (Exception e) {
			log.error("Exception in getEntityPositionLevels: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public Collection getEditableDataDetails(String hostId, String entityId) throws SwtException {
		log.debug("Entering into getEditableDataDetails()");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from EditableData e where e.id.hostId = :hostId and e.id.entityId = :entityId";
			TypedQuery<EditableData> query = session.createQuery(hql, EditableData.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<EditableData> list = query.getResultList();
			log.debug("Exiting getEditableDataDetails()");
			return list;
		} catch (Exception e) {
			log.error("Exception in getEditableDataDetails: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public Collection getAccountList(String hostId, String entityId, String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + "- [getAccountList] - Entering ");

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AccountMaster accMas where accMas.id.hostId = :hostId and accMas.id.entityId = :entityId " +
						 "and accMas.currencyCode = :currencyCode and accMas.accountClass='N' order by accMas.id.accountId";
			TypedQuery<AccountMaster> query = session.createQuery(hql, AccountMaster.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);

			List<AccountMaster> cbmAccList = query.getResultList();
			log.debug(this.getClass().getName() + "- [getAccountList] - Exiting ");
			return cbmAccList;
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- Exception in getAccountList: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}


	/**
	 * This method is used to get the movement fields from Misc params table
	 * 
	 * @param hostId
	 *            String
	 * @param entityId
	 *            String
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public Collection getMiscDataDetails(String hostId, String entityId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getMiscDataDetails] - Entering");

			String hql = "from MiscParams e where e.id.key2 not in " +
						 "(select ed.id.movementField from EditableData ed where ed.id.hostId = :hostId and ed.id.entityId = :entityId) " +
						 "and e.id.key1 = 'MOVEMENTFIELD'";

			TypedQuery<MiscParams> query = session.createQuery(hql, MiscParams.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<MiscParams> resultList = query.getResultList();
			log.debug(this.getClass().getName() + " - [getMiscDataDetails] - Exiting");
			return resultList;
		} catch (Exception e) {
			log.error("Exception in getMiscDataDetails: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	private Collection<Process> getProcess() throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getProcess] - Entry");

			String hql = "from Process p";
			TypedQuery<Process> query = session.createQuery(hql, Process.class);

			List<Process> processList = query.getResultList();
			log.debug(this.getClass().getName() + " - [getProcess] - Exit");
			return processList;
		} catch (Exception e) {
			log.error("Exception in getProcess: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	private Collection<EntityProcess> getEntityProcess(String entityId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [getEntityProcess] - Entry");

			String hql = "from EntityProcess p where p.id.entityId = :entityId";
			TypedQuery<EntityProcess> query = session.createQuery(hql, EntityProcess.class);
			query.setParameter("entityId", entityId);

			List<EntityProcess> entityProcessList = query.getResultList();
			log.debug(this.getClass().getName() + " - [getEntityProcess] - Exit");
			return entityProcessList;
		} catch (Exception e) {
			log.error("Exception in getEntityProcess: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}

	@SuppressWarnings("unchecked")
	public void deleteEntProStatus(String entityId) throws SwtException {
		Transaction tx = null;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [deleteEntProStatus] - Entry");

			String hql = "from EntityProcessStatus p where p.id.entityId = :entityId";
			TypedQuery<EntityProcessStatus> query = session.createQuery(hql, EntityProcessStatus.class);
			query.setParameter("entityId", entityId);

			List<EntityProcessStatus> entityProcessStatusList = query.getResultList();

			tx = session.beginTransaction();
			for (EntityProcessStatus entStatus : entityProcessStatusList) {
				session.delete(entStatus);
			}
			tx.commit();

			log.debug(this.getClass().getName() + " - [deleteEntProStatus] - Exit");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (Exception ex) {
					log.error("Rollback failed in deleteEntProStatus: " + ex.getMessage(), ex);
				}
			}
			log.error("Exception in deleteEntProStatus: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}


	public HashMap<String, String> getTimeZoneRegionsList() throws SwtException{

		log.debug(this.getClass().getName() + "- [getTimeZoneRegionsList] - Entry ");
		/* Local variable declaration */
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet res = null;
		LinkedHashMap<String, String> timeZoneRegionsList = new LinkedHashMap<String, String>();

		try {
		    java.util.Date utilDate = SwtUtil.getSystemDatewithoutTime();
		    java.sql.Date sqlDate = new java.sql.Date(utilDate.getTime());
			/* Establish the connection using connection manager */
			conn = ConnectionManager.getInstance().databaseCon();

			/* pass the query in string buffer */
			String regionsQuery =  "WITH TZ_DATA AS (SELECT DISTINCT TZNAME FROM V$TIMEZONE_NAMES), OFFSETS AS (" + 
					"  SELECT TZNAME," + 
					"         TO_CHAR(FROM_TZ(TO_TIMESTAMP(?), TZNAME), 'TZH:TZM') AS TIMEZONE_OFFSET " + 
					"  FROM TZ_DATA )" + 
					"SELECT TZNAME, TIMEZONE_OFFSET " + 
					"FROM OFFSETS " + 
					"GROUP BY TZNAME, TIMEZONE_OFFSET " + 
					"ORDER BY TZNAME";
			pstmt = conn.prepareStatement(regionsQuery);
			pstmt.setDate(1, sqlDate);
			/* execute the statement */
			res = pstmt.executeQuery();

			while (res.next()) {
				timeZoneRegionsList.put(res.getString(1),res.getString(2));

			}
		} catch (SQLException e) {

			log.debug(this.getClass().getName()
					+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());
			log.error(this.getClass().getName()
					+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());
			e.printStackTrace();
		} finally {
			
			JDBCCloser.close(res, pstmt, conn, null);
			
			try{				
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ "- [getTimeZoneRegionsList] - Exception " + e.getMessage());        
			}
		}
		log.debug(this.getClass().getName() + "- [getTimeZoneRegionsList] - Exit ");
		
		return timeZoneRegionsList;
	}

}