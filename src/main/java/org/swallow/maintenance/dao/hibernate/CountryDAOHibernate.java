/*
 * @(#)CountryDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.swallow.maintenance.dao.CountryDAO;

@Repository ("countryDAO")
public class CountryDAOHibernate extends CustomHibernateDaoSupport implements CountryDAO {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CountryDAOHibernate.class);
	
	public CountryDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager){
	    super(sessionfactory, entityManager);
	}
	
	/**
	 * Method returns the collection of country list
	 * @returnCollection
	 */
	public Collection getCountries() {
		log.debug(this.getClass().getName() + " - [getCountries] - Entry");

		Session session = null;
		List countryList = new ArrayList();

		try {
			// Open a Hibernate session
			session = getHibernateTemplate().getSessionFactory().openSession();

			// Execute the query
			countryList = session.createQuery("from Country c order by c.countryCode").list();

		} catch (Exception exp) {
			// Log the exception
			log.error(this.getClass().getName() + " - Exception in [getCountries]: " + exp.getMessage(), exp);

		} finally {
			// Ensure the session is closed
			if (session != null && session.isOpen()) {
				session.close();
			}
			log.debug(this.getClass().getName() + " - [getCountries] - Exit");
		}

		// Return the country list (empty if an exception occurred)
		return countryList;
	}


	}

