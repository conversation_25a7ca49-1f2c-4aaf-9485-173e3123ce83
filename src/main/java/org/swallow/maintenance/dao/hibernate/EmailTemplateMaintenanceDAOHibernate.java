package org.swallow.maintenance.dao.hibernate;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.maintenance.dao.EmailTemplateMaintenanceDAO;

import java.lang.*;
import java.util.*;

import org.hibernate.Session;
import org.hibernate.Transaction;
import org.swallow.util.SwtInterceptor;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtErrorHandler;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.maintenance.model.EmailTemplate;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.util.JDBCCloser;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.swallow.util.SwtUtil;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Repository("emailTemplateMaintenanceDAO")
@Transactional
public class EmailTemplateMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements EmailTemplateMaintenanceDAO {

    private final Log log = LogFactory.getLog(EmailTemplateMaintenanceDAOHibernate.class);

    public EmailTemplateMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    @Override
    public void saveEmailTemplate(EmailTemplate emailTemplate) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveEmailTemplate] - Enter");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            String hql = "FROM EmailTemplate emailTemplate WHERE emailTemplate.templateId = :templateId";
            List<EmailTemplate> existing = session.createQuery(hql, EmailTemplate.class)
                    .setParameter("templateId", emailTemplate.getTemplateId())
                    .getResultList();

            if (existing.isEmpty()) {
                SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
                try (Session interceptedSession = getHibernateTemplate().getSessionFactory()
                        .withOptions().interceptor(interceptor).openSession()) {
                    Transaction tx = interceptedSession.beginTransaction();
                    try {
                        interceptedSession.save(emailTemplate);
                        tx.commit();
                    } catch (Exception e) {
                        if (tx != null && tx.isActive()) {
                            tx.rollback();
                        }
                        throw e;
                    }
                }
            } else {
                throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in [saveEmailTemplate]: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "saveEmailTemplate", this.getClass());
        }
        log.debug(this.getClass().getName() + " - [saveEmailTemplate] - Exit");
    }

    @Override
    public void updateEmailTemplate(EmailTemplate emailTemplate) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateEmailTemplate] - Enter");
        
        SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
        try (Session session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.update(emailTemplate);
                tx.commit();
            } catch (Exception e) {
                if (tx != null && tx.isActive()) {
                    tx.rollback();
                }
                throw e;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in [updateEmailTemplate]: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "updateEmailTemplate", this.getClass());
        }
        log.debug(this.getClass().getName() + " - [updateEmailTemplate] - Exit");
    }

    @Override
    public void deleteEmailTemplate(EmailTemplate emailTemplate) throws SwtException {
        log.debug(this.getClass().getName() + " - [deleteEmailTemplate] - Enter");
        
        SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
        try (Session session = getHibernateTemplate().getSessionFactory()
                .withOptions().interceptor(interceptor).openSession()) {
            Transaction tx = session.beginTransaction();
            try {
                session.delete(emailTemplate);
                tx.commit();
            } catch (Exception e) {
                if (tx != null && tx.isActive()) {
                    tx.rollback();
                }
                throw e;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in [deleteEmailTemplate]: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "deleteEmailTemplate", this.getClass());
        }
        log.debug(this.getClass().getName() + " - [deleteEmailTemplate] - Exit");
    }

    @Override
    public EmailTemplate getEmailTemplate(String templateId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getEmailTemplate] - Enter");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            String hql = "FROM EmailTemplate emailTemplate WHERE emailTemplate.templateId = :templateId";
            EmailTemplate result = session.createQuery(hql, EmailTemplate.class)
                    .setParameter("templateId", templateId)
                    .uniqueResult();
            
            log.debug(this.getClass().getName() + " - [getEmailTemplate] - Exit");
            return result;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in [getEmailTemplate]: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEmailTemplate", this.getClass());
        }
    }

    @Override
    public Collection<EmailTemplate> getEmailTemplateList() throws SwtException {
        log.debug(this.getClass().getName() + " - [getEmailTemplateList] - Enter");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            String hql = "FROM EmailTemplate emailTemplate";
            List<EmailTemplate> results = session.createQuery(hql, EmailTemplate.class)
                    .getResultList();
            
            log.debug(this.getClass().getName() + " - [getEmailTemplateList] - Exit");
            return results;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in [getEmailTemplateList]: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getEmailTemplateList", this.getClass());
        }
    }
}