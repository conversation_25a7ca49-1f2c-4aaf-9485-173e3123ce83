/*
 * @(#)ReasonMaintenanceDAOHibernate.java 1.0 25/08/2008
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.ReasonMaintenanceDAO;
import org.swallow.maintenance.model.ReasonMaintenance;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;

import org.swallow.util.jpa.CustomHibernateDaoSupport;

import java.util.List;

/**
 * 
 * <AUTHOR> G Class that implements the ReasonMaintenanceDAO and acts as
 *         DAO layer for all database operations
 * 
 */
/*
 * This is DAO class for Reason screen
 * 
 */
@Repository
public class ReasonMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements ReasonMaintenanceDAO {

    public ReasonMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ReasonMaintenance> getReasonMaintenanceDetails(String hostId, String entityId) throws SwtException {
        try {
            try (Session session = getSessionFactory().openSession()) {
                TypedQuery<ReasonMaintenance> query = session.createQuery(
                    "FROM ReasonMaintenance r WHERE r.id.hostId = :hostId AND r.id.entityId = :entityId ORDER BY UPPER(r.id.reasonCode)",
                    ReasonMaintenance.class);
                query.setParameter("hostId", hostId);
                query.setParameter("entityId", entityId);
                return query.getResultList();
            }
        } catch (Exception exp) {
            throw SwtErrorHandler.getInstance().handleException(exp, "getReasonMaintenanceDetails", ReasonMaintenanceDAOHibernate.class);
        }
    }

    @Override
    @Transactional
    public void saveReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance) throws SwtException {
        try {
            try (Session session = getSessionFactory().withOptions()
                    .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                    .openSession()) {
                
                TypedQuery<ReasonMaintenance> query = session.createQuery(
                    "from ReasonMaintenance r where r.id.hostId = :hostId and r.id.entityId = :entityId and r.id.reasonCode = :reasonCode",
                    ReasonMaintenance.class)
                    .setParameter("hostId", reasonMaintenance.getId().getHostId())
                    .setParameter("entityId", reasonMaintenance.getId().getEntityId())
                    .setParameter("reasonCode", reasonMaintenance.getId().getReasonCode());
                
                List<ReasonMaintenance> records = query.getResultList();

                if (records.isEmpty()) {
                    Transaction tx = session.beginTransaction();
                    session.save(reasonMaintenance);
                    tx.commit();
                } else {
                    throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
                }
            }
        } catch (Exception exp) {
            throw SwtErrorHandler.getInstance().handleException(exp, "saveReasonMaintenanceDetails", ReasonMaintenanceDAOHibernate.class);
        }
    }

    @Override
    @Transactional
    public void updateReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance) throws SwtException {
        try {
            try (Session session = getSessionFactory().withOptions()
                    .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                    .openSession()) {
                Transaction tx = session.beginTransaction();
                session.update(reasonMaintenance);
                tx.commit();
            }
        } catch (Exception exp) {
            throw SwtErrorHandler.getInstance().handleException(exp, "updateReasonMaintenanceDetails", ReasonMaintenanceDAOHibernate.class);
        }
    }

    @Override
    @Transactional
    public void deleteReasonMaintenanceRecord(ReasonMaintenance reasonMaintenance) throws SwtException {
        try {
            try (Session session = getSessionFactory().withOptions()
                    .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                    .openSession()) {
                Transaction tx = session.beginTransaction();
                session.delete(reasonMaintenance);
                tx.commit();
            }
        } catch (Exception exp) {
            throw SwtErrorHandler.getInstance().handleException(exp, "deleteReasonMaintenanceRecord", ReasonMaintenanceDAOHibernate.class);
        }
    }
}

