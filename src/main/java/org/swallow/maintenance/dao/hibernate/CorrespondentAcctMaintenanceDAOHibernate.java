package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CorrespondentAcctMaintenanceDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 
 * This is DAO class for Correspondent Account Alias screen.
 * 
 */
@Repository ("correspondentAcctMaintenanceDAO")
@Transactional
public class CorrespondentAcctMaintenanceDAOHibernate extends
		CustomHibernateDaoSupport implements CorrespondentAcctMaintenanceDAO {
	
	private final Log log = LogFactory.getLog(CorrespondentAcctMaintenanceDAO.class);
	
	/**
	 * Constructor with dependency injection
	 * @param sessionfactory The session factory
	 * @param entityManager The entity manager
	 */
	public CorrespondentAcctMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	/**
	 * Queries hibernate for a collection of CorrespondentAcct objects matching
	 * the supplied entityId and hostId parameters
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId, int startRowNumber, int endRowNumber, String messageType, String currencyCode, String userId, String selectedSort)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [ getList ] - Entry ");
	
		ArrayList<CorrespondentAcct> returnList = new ArrayList<CorrespondentAcct>();
		String order = "-1";
		String direction = "desc";
		Integer rowCount = null;
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession();
			 Connection conn = SwtUtil.connection(session)) {
			
			if (!SwtUtil.isEmptyOrNull(selectedSort)) {
				String[] result = selectedSort.split("\\|");
				order = result[0];
				direction = result[1];
				if (result[1].equals("true"))
					direction = "desc";
				else 
					direction = "asc";
			}
			
			/* Using Callable statement to execute the Stored Procedure */
			try (CallableStatement pstmt = conn.prepareCall("{call sp_Get_list_corresp_acc(?,?,?,?,?,?,?,?,?,?,?,?)}")) {
				pstmt.setString(1, hostId);
				pstmt.setString(2, entityId);
				pstmt.setString(3, null);
				pstmt.setString(4, messageType);
				pstmt.setString(5, currencyCode);
				pstmt.setString(6, order);
				pstmt.setInt(7, startRowNumber);
				pstmt.setInt(8, endRowNumber);
				pstmt.setString(9, direction);
				pstmt.setString(10, userId);
				pstmt.registerOutParameter(11, oracle.jdbc.OracleTypes.CURSOR);
				pstmt.registerOutParameter(12, oracle.jdbc.OracleTypes.INTEGER);
			
				pstmt.execute();
				try (ResultSet rs = (ResultSet) pstmt.getObject(11)) {
					rowCount = pstmt.getInt(12);
					if (rs != null) {
						while (rs.next()) {
							CorrespondentAcct corrAcc = new CorrespondentAcct();
							corrAcc.getId().setCorresAccId(rs.getString(4));
							corrAcc.setAccountId(rs.getString(7));
							corrAcc.getId().setMessageType(rs.getString(2));
							corrAcc.getId().setEntityId(rs.getString(6));
							corrAcc.getId().setHostId(rs.getString(5));
							corrAcc.getId().setCurrencyCode(rs.getString(3));
							corrAcc.setAccountName(rs.getString(8));
							corrAcc.setRowCount(rowCount);
							returnList.add(corrAcc);
						}
					}
				}
			}
			
			log.debug(this.getClass().getName() + " - [ getList ] - Exit ");
			return returnList;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in getList: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e, "getList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
	}

	/**
	 * Deletes the unique account matching the PK data in the given
	 * CorrespondentAcct object
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [ deleteCorrespondentAcct ] - Entry ");
		
		try {
			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			
			try (Session session = getHibernateTemplate().getSessionFactory()
					.withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					session.delete(getCorrespondentAcct(acct));
					tx.commit();
				} catch (Exception e) {
					if (tx != null) tx.rollback();
					throw e;
				}
			}
			
			log.debug(this.getClass().getName() + " - [ deleteCorrespondentAcct ] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in deleteCorrespondentAcct: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"deleteCorrespondentAcct",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}
	}

	/**
	 * Queries hibernate for a unique record matching the entityId, hostId,
	 * messageType, currencyCode and corresAccId contained in the given
	 * CorrespondentAcct object
	 * 
	 * @param acct
	 * @return CorrespondentAcct
	 * @throws SwtException
	 */
	public CorrespondentAcct getCorrespondentAcct(CorrespondentAcct acct) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getCorrespondentAcct ] - Entry ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String queryString = "FROM CorrespondentAcct ca WHERE ca.id.entityId = :entityId" +
					" AND ca.id.hostId = :hostId" +
					" AND ca.id.messageType = :messageType" +
					" AND ca.id.currencyCode = :currencyCode" +
					" AND ca.id.corresAccId = :corresAccId";
			
			TypedQuery<CorrespondentAcct> query = session.createQuery(queryString, CorrespondentAcct.class);
			query.setParameter("entityId", acct.getId().getEntityId());
			query.setParameter("hostId", acct.getId().getHostId());
			query.setParameter("messageType", acct.getId().getMessageType().replace("%20", " "));
			query.setParameter("currencyCode", acct.getId().getCurrencyCode());
			query.setParameter("corresAccId", acct.getId().getCorresAccId());
			
			List<CorrespondentAcct> results = query.getResultList();
			
			CorrespondentAcct obj = null;
			if (!results.isEmpty()) {
				obj = results.get(0);
			}
			
			log.debug(this.getClass().getName() + " - [ getCorrespondentAcct ] - Exit ");
			return obj;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in getCorrespondentAcct: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e, "getCorrespondentAcct", 
					CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}
	}

	/**
	 * Queries hibernate for a collection of CorrespondentAcct objects matching
	 * the given entityId, hostId, messageType, currencyCode and corresAccId
	 * parameters
	 * 
	 * @param hostId
	 * @param entityId
	 * @param messageType
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCorrespondentAcctList(String hostId, String entityId,
			String messageType, String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getCorrespondentAcctList ] - Entry ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			StringBuilder queryBuilder = new StringBuilder(
					"FROM CorrespondentAcct a WHERE a.id.hostId = :hostId AND a.id.entityId = :entityId");
			
			TypedQuery<CorrespondentAcct> query;
			
			if (currencyCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				if (messageType != null && messageType.trim().length() > 0) {
					int index = messageType.indexOf("%");
					if (index != -1) {
						queryBuilder.append(" AND UPPER(a.id.messageType) LIKE UPPER(:messageType)");
					} else {
						queryBuilder.append(" AND UPPER(a.id.messageType) = UPPER(:messageType)");
					}
					
					query = session.createQuery(queryBuilder.toString(), CorrespondentAcct.class);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
					query.setParameter("messageType", messageType);
				} else {
					query = session.createQuery(queryBuilder.toString(), CorrespondentAcct.class);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
				}
			} else {
				queryBuilder.append(" AND a.id.currencyCode = :currencyCode");
				
				if (messageType != null && messageType.trim().length() > 0) {
					int index = messageType.indexOf("%");
					if (index != -1) {
						queryBuilder.append(" AND UPPER(a.id.messageType) LIKE UPPER(:messageType)");
					} else {
						queryBuilder.append(" AND UPPER(a.id.messageType) = UPPER(:messageType)");
					}
					
					query = session.createQuery(queryBuilder.toString(), CorrespondentAcct.class);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
					query.setParameter("currencyCode", currencyCode);
					query.setParameter("messageType", messageType);
				} else {
					query = session.createQuery(queryBuilder.toString(), CorrespondentAcct.class);
					query.setParameter("hostId", hostId);
					query.setParameter("entityId", entityId);
					query.setParameter("currencyCode", currencyCode);
				}
			}
			
			List<CorrespondentAcct> list = query.getResultList();
			
			log.debug(this.getClass().getName() + " - [ getCorrespondentAcctList ] - Exit ");
			return list;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in getCorrespondentAcctList: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(e,
					"getCorrespondentAcctList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtExp;
		}
	}

	/**
	 * This is used to save the records in database
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void saveCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [ saveCorrespondentAcct ] - Entering ");
			
			// First check if record already exists
			try (Session checkSession = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<CorrespondentAcct> query = checkSession.createQuery(
						"FROM CorrespondentAcct a WHERE a.id.hostId = :hostId AND a.id.entityId = :entityId " +
						"AND a.id.messageType = :messageType AND a.id.currencyCode = :currencyCode " +
						"AND a.id.corresAccId = :corresAccId", 
						CorrespondentAcct.class);
				query.setParameter("hostId", acct.getId().getHostId());
				query.setParameter("entityId", acct.getId().getEntityId());
				query.setParameter("messageType", acct.getId().getMessageType());
				query.setParameter("currencyCode", acct.getId().getCurrencyCode());
				query.setParameter("corresAccId", acct.getId().getCorresAccId());
				
				List<CorrespondentAcct> records = query.getResultList();
				
				if (records.isEmpty()) {
					// No existing record, proceed with save
					SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					
					try (Session session = getHibernateTemplate().getSessionFactory()
							.withOptions().interceptor(interceptor).openSession()) {
						Transaction tx = session.beginTransaction();
						try {
							session.save(acct);
							tx.commit();
						} catch (Exception e) {
							if (tx != null) tx.rollback();
							throw e;
						}
					}
				} else {
					throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
				}
			}
			
			log.debug(this.getClass().getName() + " - [ saveCorrespondentAcct ] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - Exception in saveCorrespondentAcct: " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCorrespondentAcct",
					CorrespondentAcctMaintenanceDAOHibernate.class);
		}
	}

	/**
	 * Updates the persistent storage with the existing CorrespondentAcct object
	 * given. The PK data contained therein is used to locate the record to
	 * update.
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void updateRecord(CorrespondentAcct newAcct) throws SwtException {
		log.debug(this.getClass().getName() + " - [ updateRecord ] - Entry ");
		
		try {
			SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			
			try (Session session = getHibernateTemplate().getSessionFactory()
					.withOptions().interceptor(interceptor).openSession()) {
				Transaction tx = session.beginTransaction();
				try {
					session.update(newAcct);
					tx.commit();
				} catch (Exception e) {
					if (tx != null) tx.rollback();
					throw e;
				}
			}
			
			log.debug(this.getClass().getName() + " - [ updateRecord ] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in updateRecord: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"updateRecord", CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
	}

	/**
	 * To find list of accounts This function appends various filtering
	 * condition in the select query
	 */
	public Collection getAccountIdDetails(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getAccountIdDetails ] - Entry ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<AcctMaintenance> query;
			
			if (currencyCode != null && currencyCode.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				query = session.createQuery(
						"FROM AcctMaintenance acct WHERE acct.id.hostId = :hostId " +
						"AND acct.id.entityId = :entityId ORDER BY acct.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
			} else {
				query = session.createQuery(
						"FROM AcctMaintenance acct WHERE acct.id.hostId = :hostId " +
						"AND acct.id.entityId = :entityId AND acct.currcode = :currencyCode " +
						"ORDER BY acct.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
			}
			
			List<AcctMaintenance> accountIdList = query.getResultList();
			
			log.debug(this.getClass().getName() + " - [ getAccountIdDetails ] - Exit ");
			return accountIdList;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in getAccountIdDetails: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getAccountIdDetails",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
	}

	/**
	 * Get message types list from database
	 * @return Collection of message types
	 * @throws SwtException
	 */
	public Collection<String> getMessageTypesList() throws SwtException {
		log.debug(this.getClass().getName() + " - [ getMessageTypesList ] - Entry ");
		List<String> messageTypes = new ArrayList<>();
		
		try (Connection conn = ConnectionManager.getInstance().databaseCon();
			 Statement stmt = conn.createStatement()) {
			
			// Query to get message types
			String query = "SELECT UPPER(MESSAGEID) FROM I_INTERFACEMESSAGE UNION SELECT UPPER(INTERFACEID) FROM I_INTERFACE";
			
			try (ResultSet rs = stmt.executeQuery(query)) {
				while (rs.next()) {
					messageTypes.add(rs.getString(1));
				}
			}
			
			log.debug(this.getClass().getName() + " - [ getMessageTypesList ] - Exit ");
			return messageTypes;
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in getMessageTypesList: " + e.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getMessageTypesList",
					CorrespondentAcctMaintenanceDAO.class);
			throw swtexp;
		}
	}
}