/*
 * @(#)MatchQualityDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.MatchQualityDAO;
import org.swallow.maintenance.model.MatchParams;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;

/**
 * MatchQualityDAOHibernate.java<br>
 * 
 * This class implements the MatchQualityDAO and acts as DAO layer for all
 * database operations related to Match Quality operations.<br>
 * 
 * @Modified: Marshal on 31-March-2011
 */
@Repository ("matchQualityDAO")
@Transactional
public class MatchQualityDAOHibernate extends CustomHibernateDaoSupport implements
		MatchQualityDAO {
	public MatchQualityDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	/**
	 * Initializing logger object for this class
	 */

	private final Log log = LogFactory.getLog(MatchQualityDAOHibernate.class);

	/**
	 * Collect the Match list from P_MATCH_ACTION
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getMatchList(String entityId, String hostId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getMatchList] - Entry");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<MatchQuality> query = session.createQuery(
					"from MatchQuality c where c.id.hostId = :hostId and c.id.entityId = :entityId " +
					"order by c.id.hostId, c.id.entityId, c.id.currencyCode, c.id.posLevel",
					MatchQuality.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<MatchQuality> list = query.getResultList();
			log.debug(this.getClass().getName() + " - [getMatchList] - Exit");
			return list;
		} catch (Exception e) {
			throw SwtErrorHandler.getInstance().handleException(e, "getMatchList", MatchQualityDAOHibernate.class);
		}
	}

	public MatchQuality getMatchQualityList(String hostId, String entityId, String currencyCode, Integer posLevel) throws SwtException {
		log.debug(this.getClass().getName() + " - [getMatchQualityList] - Entry");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<MatchQuality> query = session.createQuery(
					"from MatchQuality c where c.id.hostId = :hostId and c.id.entityId = :entityId " +
					"and c.id.currencyCode = :currencyCode and c.id.posLevel = :posLevel",
					MatchQuality.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);
			query.setParameter("posLevel", posLevel);
			List<MatchQuality> results = query.getResultList();
			log.debug(this.getClass().getName() + " - [getMatchQualityList] - Exit");
			return results.isEmpty() ? null : results.get(0);
		} catch (Exception e) {
			throw SwtErrorHandler.getInstance().handleException(e, "getMatchQualityList", MatchQualityDAOHibernate.class);
		}
	}

	public Collection getParamDescAll() throws SwtException {
		log.debug(this.getClass().getName() + " - [getParamDescAll] - Entry");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<MatchParams> query = session.createQuery(
					"from MatchParams c order by c.id.paramCode",
					MatchParams.class);
			List<MatchParams> list = query.getResultList();
			log.debug(this.getClass().getName() + " - [getParamDescAll] - Exit");
			return list;
		} catch (Exception e) {
			throw SwtErrorHandler.getInstance().handleException(e, "getParamDescAll", MatchQualityDAOHibernate.class);
		}
	}

	public void addMatchQuality(QualityAction matchQuality) throws SwtException {
        log.debug(this.getClass().getName() + " - [addMatchQuality] - Entry");

        try (Session session = getSessionFactory().withOptions()
                .interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
                .openSession()) {

            TypedQuery<MatchQuality> query = session.createQuery(
                    "from MatchQuality c where c.id.hostId = :hostId and c.id.entityId = :entityId and " +
                    "c.id.currencyCode = :currencyCode and c.id.posLevel = :posLevel", MatchQuality.class);
            query.setParameter("hostId", matchQuality.getId().getHostId());
            query.setParameter("entityId", matchQuality.getId().getEntityId());
            query.setParameter("currencyCode", matchQuality.getId().getCurrencyCode());
            query.setParameter("posLevel", matchQuality.getId().getPositionLevel());
            List<MatchQuality> records = query.getResultList();

            if (records.isEmpty()) {
                TypedQuery<MatchParams> paramQuery = session.createQuery(
                    "from MatchParams c where c.id.paramCode = :paramCode",
                    MatchParams.class);
                paramQuery.setParameter("paramCode", matchQuality.getId().getParameterId());
                List<MatchParams> paramList = paramQuery.getResultList();

                if (!paramList.isEmpty()) {
                    matchQuality.setParamIdRef(paramList.get(0));
                }

                Transaction tx = session.beginTransaction();
                session.save(matchQuality);
                tx.commit();
            } else {
                throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
            }
            log.debug(this.getClass().getName() + " - [addMatchQuality] - Exit");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [addMatchQuality] - Exception " + exp.getMessage());
            throw SwtErrorHandler.getInstance().handleException(exp, "addMatchQuality", MatchQualityDAOHibernate.class);
        }
	}

	public void updateMatchQuality(QualityAction matchQuality) throws SwtException {
		log.debug(this.getClass().getName() + " - [updateMatchQuality] - Entry");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
				.openSession()) {

			Transaction tx = session.beginTransaction();
			session.update(matchQuality);
			tx.commit();

			log.debug(this.getClass().getName() + " - [updateMatchQuality] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [updateMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "updateMatchQuality", MatchQualityDAOHibernate.class);
		}
	}


	public void deleteMatchQuality(MatchQuality matchQuality) throws SwtException {
		log.debug(this.getClass().getName() + " - [deleteMatchQuality] - Entry");

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions()
				.interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor"))
				.openSession()) {

			Transaction tx = session.beginTransaction();
			int i = session.createQuery(
							"delete from MatchQuality c where c.id.hostId = :hostId and c.id.entityId = :entityId " +
							"and c.id.currencyCode = :ccyCode and c.id.posLevel = :posLevel")
					.setParameter("hostId", matchQuality.getId().getHostId())
					.setParameter("entityId", matchQuality.getId().getEntityId())
					.setParameter("ccyCode", matchQuality.getId().getCurrencyCode())
					.setParameter("posLevel", matchQuality.getId().getPosLevel())
					.executeUpdate();
			tx.commit();

			if (i == 0) {
				throw new SwtRecordNotExist();
			}
			log.debug(this.getClass().getName() + " - [deleteMatchQuality] - Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [deleteMatchQuality] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteMatchQuality", MatchQualityDAOHibernate.class);
		}
	}


	@SuppressWarnings("unchecked")
	public Collection getfilterPositionlevel(String hostId, String entityId, String currencyCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [getfilterPositionlevel] - Entry");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Integer> query = session.createQuery(
					"select distinct c.id.posLevel from MatchQuality c where c.id.hostId = :hostId and " +
					"c.id.entityId = :entityId and c.id.currencyCode = :currencyCode",
					Integer.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);
			List<Integer> collection = query.getResultList();
			log.debug(this.getClass().getName() + " - [getfilterPositionlevel] - Exit");
			return collection;
		} catch (Exception exception) {
			log.error(this.getClass().getName() + " - [getfilterPositionlevel] - Exception -" + exception.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exception, "getfilterPositionlevel", MatchQualityDAOHibernate.class);
		}
	}

	/*
	 * End: Code modified for Mantis 1238: Copy Match Qualities functionality
	 * generates error - by Marshal on 31-Mar-2011
	 */
}
