/*
 * @(#)AcctMaintenanceDAOHibernate.java 1.0 20/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.*;
import jakarta.persistence.EntityManager;
import org.hibernate.exception.GenericJDBCException;
import org.hibernate.type.Type;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.dao.hibernate.ScenMaintenanceDAOHibernate;
import org.swallow.control.model.Scenario;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.maintenance.dao.AcctMaintenanceDAO;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountInterestRate;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Party;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.Query;


/**
 * AcctMaintenanceDAOHibernate.java
 * 
 * Class that implements the AcctMaintenanceDAO and acts as DAO layer for all
 * database operations related to Account Maintenance This interface has methods
 * that are used for accessing the persistent storage such as database which
 * helps client to create, retrieve and persists data to the Persistent Object.
 * 
 */
@Repository ("acctMaintenanceDAO")
@Transactional
public class AcctMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements
		AcctMaintenanceDAO {
	public AcctMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory
			.getLog(AcctMaintenanceDAOHibernate.class);

	/**
	 * 
	 * This method is used to retrieve the account details persistent object
	 * from database through stored procedure
	 * PK_APPLICATION.SPACMAINTENANCEACCESS .
	 * 
	 * @param entityId
	 * @param hostId
	 * @param roleId
	 * @return accountList
	 */

	public Collection<AcctMaintenance> getAcctMaintenanceDetailList(
			String entityId, String hostId, String roleId, String currencyId, String selectedFiltredByStatus, String acctType) throws SwtException {
			Collection<AcctMaintenance> accountList = null;
			AcctMaintenance accountMaintenance = null;
	// Use try-with-resources for session, connection, callable statement, and result set
	try (Session session = getHibernateTemplate().getSessionFactory().openSession();
		 Connection connection = SwtUtil.connection(session);
		 CallableStatement callStatement = connection.prepareCall("{call PK_APPLICATION.SPACMAINTENANCEACCESS(?,?,?,?,?,?)}")) {

		log.debug(this.getClass().getName() + "- [getAcctMaintenanceDetailList] - Entering ");

				callStatement.setString(1, hostId);
				callStatement.setString(2, entityId);
				callStatement.setString(3, roleId);
		callStatement.registerOutParameter(4, oracle.jdbc.OracleTypes.CURSOR);
				callStatement.setString(5, selectedFiltredByStatus);
				callStatement.setString(6, acctType);
				callStatement.execute();

		try (ResultSet resultSetAccountDetails = (ResultSet) callStatement.getObject(4)) {
				if (resultSetAccountDetails != null) {
				accountList = new ArrayList<>();
					while (resultSetAccountDetails.next()) {
						accountMaintenance = new AcctMaintenance();
					accountMaintenance.getId().setHostId(resultSetAccountDetails.getString("HOST_ID"));
					accountMaintenance.getId().setEntityId(resultSetAccountDetails.getString("ENTITY_ID"));
					accountMaintenance.getId().setAccountId(resultSetAccountDetails.getString("ACCOUNT_ID"));
						accountMaintenance.setAcctname(resultSetAccountDetails.getString("ACCOUNT_NAME"));
						accountMaintenance.setCurrcode(resultSetAccountDetails.getString("currency_code"));
						accountMaintenance.setAccttype(resultSetAccountDetails.getString("account_type"));
						accountMaintenance.setAcctlevel(resultSetAccountDetails.getString("account_level"));
						accountMaintenance.setAcctClass(resultSetAccountDetails.getString("account_class"));
						accountMaintenance.setAcctIBAN(resultSetAccountDetails.getString("iban"));
						accountMaintenance.setAcctstatusflg(resultSetAccountDetails.getString("account_status_flag"));
					accountMaintenance.setCurrAccess(resultSetAccountDetails.getInt("access_id"));
						accountMaintenance.setCorresacccode(resultSetAccountDetails.getString("corres_acc_id"));
						accountMaintenance.setMinacctcode(resultSetAccountDetails.getString("main_account_id"));
						accountMaintenance.setCutoff(resultSetAccountDetails.getString("cut_off"));
						accountMaintenance.setLinkAccID(resultSetAccountDetails.getString("link_account_id"));
						accountMaintenance.setAccountPartyId(resultSetAccountDetails.getString("account_party_id"));
						accountMaintenance.setAcctbiccode(resultSetAccountDetails.getString("account_bic_id"));
						accountMaintenance.setIsIlmLiqContributor(resultSetAccountDetails.getString("is_ilm_liq_contributor"));
						accountList.add(accountMaintenance);
					}
				}
		}
		log.debug(this.getClass().getName() + "- [getAcctMaintenanceDetailList] - Exit");
			} catch (Exception exp) {
		log.error("Exception Catch in AcctMaintenanceDAOHibernate.'getAcctMaintenanceDetailList' method : " + exp.getMessage());
		throw SwtErrorHandler.getInstance().handleException(exp, "getAcctMaintenanceDetailList", AcctMaintenanceDAOHibernate.class);
			}
			return accountList;
		}

	@SuppressWarnings("unchecked")
public void saveAcctMaintDetail(AcctMaintenance acct, ArrayList accountInterestRateList) throws SwtException {
		List acctMaintenanceRecords = null;
		Iterator interestRateItr = null;
		Object interestRateObj = null;
		SwtInterceptor interceptor = null;
	Transaction tx = null;
	try {
		log.debug(this.getClass().getName() + "- [saveAcctMaintDetail] - Entering ");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			acctMaintenanceRecords = session.createQuery(
					"from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId")
					.setParameter("hostId", acct.getId().getHostId())
					.setParameter("entityId", acct.getId().getEntityId())
					.setParameter("accountId", acct.getId().getAccountId())
					.getResultList();
		}
			if (acctMaintenanceRecords.size() == 0) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				tx = session.beginTransaction();
				session.save(acct);
				if (accountInterestRateList.size() > 0) {
					interestRateItr = accountInterestRateList.iterator();
					while (interestRateItr.hasNext()) {
						interestRateObj = interestRateItr.next();
						if (interestRateObj != null) {
								session.save(interestRateObj);
						}
					}
				}
				tx.commit();
			}
			} else {
			throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
			}
		log.debug(this.getClass().getName() + "- [saveAcctMaintDetail] - Exiting ");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
			log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
		}
		log.error(this.getClass().getName() + " - Exception caught in [saveAcctMaintDetail] method " + exp.getMessage());
		throw SwtErrorHandler.getInstance().handleException(exp, "saveAcctMaintDetail", AcctMaintenanceDAOHibernate.class);
	}
}

	public AcctMaintenance getEditableData(String hostId, String entityId, String accountId) throws SwtException {
		AcctMaintenance acctMaint = null;
		Interceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
	try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
		String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId";
			TypedQuery<AcctMaintenance> query = session.createQuery(hql, AcctMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			acctMaint = query.getResultStream().findFirst().orElse(null);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception in [getEditableData]: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "getEditableData", ILMGeneralMaintenanceDAOHibernate.class);
		}
		log.debug(this.getClass().getName() + " - [getEditableData] - Exit");
		return acctMaint;
	}


	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param collAcctIntRateAdded
	 * @param collAcctIntRateUpdated
	 * @param sysforma
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void updateAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, Collection collAcctIntRateAdded,
			Collection collAcctIntRateUpdated, SystemFormats sysforma)
			throws SwtException {
		/*
		 * Start:Code Modified for MAntis 1690 by sandeepkumar on 3-oct-2012
		 * :Null pointer exception raised while Saving account after deleting
		 * the lone record in the Account interest rate screen
		 */
		Iterator interestRateItr = null;
		int rowsdeleted = 0;
		AccountInterestRate accIntRate = null;
		java.sql.Date interestRateTime = null;
		String deleteAcctIntRateHQL = null;
		
		SwtInterceptor interceptor = null;
	Transaction tx = null;
			
	try (Session session = getHibernateTemplate()
					.getSessionFactory()
			.withOptions().interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {

		log.debug(this.getClass().getName() + "- [updateAcctDetail] - Entry");
			tx = session.beginTransaction();
			
			session.update(acct);

		// Deleted AccountInterestRate
			if (collAcctIntRateDeleted.size() > 0) {
				interestRateItr = collAcctIntRateDeleted.iterator();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
				interestRateTime = SwtUtil.truncateDateTime(accIntRate.getId().getInterestDateRate());

				deleteAcctIntRateHQL = "delete from AccountInterestRate m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.id.accountId = :accountId and m.id.interestDateRate = :interestDateRate";

				rowsdeleted = session.createQuery(deleteAcctIntRateHQL)
						.setParameter("hostId", accIntRate.getId().getHostId())
						.setParameter("entityId", accIntRate.getId().getEntityId())
						.setParameter("accountId", accIntRate.getId().getAccountId())
						.setParameter("interestDateRate", interestRateTime)
						.executeUpdate();

					if (rowsdeleted == 0) {
						throw new SwtRecordNotExist();
					}
				}
			}

		// Added AccountInterestRate
			if (collAcctIntRateAdded.size() > 0) {
				interestRateItr = collAcctIntRateAdded.iterator();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					if (accIntRate != null) {
						session.save(accIntRate);
					}
				}
			}

		// Updated AccountInterestRate
			if (collAcctIntRateUpdated.size() > 0) {
				interestRateItr = collAcctIntRateUpdated.iterator();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					if (accIntRate != null) {
						session.update(accIntRate);
					}
				}
			}

			tx.commit();
		log.debug(this.getClass().getName() + "- [updateAcctDetail] - Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
			log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
		}
		log.error(this.getClass().getName() + " - Exception caught in [updateAcctDetail] method " + exp.getMessage());
		throw SwtErrorHandler.getInstance().handleException(exp, "updateAcctDetail", AcctMaintenanceDAOHibernate.class);
	}
	}

	/**
	 * @param hostId
	 * @param entityId
	 * @return Collection - Booklist
	 */
	public Collection getBookListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getBookListColl: " + hostId);
		List noofRecords = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from BookCode m where m.id.hostId = :hostId and m.id.entityId = :entityId order by m.id.bookCode";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			noofRecords = query.getResultList();
		}
		log.debug("exiting getBookListColl");
		return noofRecords;
	}

	public Collection getFormatListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getFormatListColl: " + hostId);
		List noofRecords = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from MessageFormats m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.usage = 'S' order by LOWER(m.id.formatId)";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			noofRecords = query.getResultList();
		}
		log.debug("noofRecords.size : " + noofRecords.size());
		return noofRecords;
	}

	public Collection getMainAcctListColl(String hostId, String entityId)
			throws SwtException {
		log.debug("hostID for getFormatListColl: " + hostId);
		List noofRecords = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.acctlevel = 'M' and acct.id.accountId != '*'";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			noofRecords = query.getResultList();
		}
		log.debug("exiting getFormatListColl");
		return noofRecords;
	}

	/**
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param sysforma
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public void deleteAcctDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, SystemFormats sysforma)
			throws SwtException {
		Iterator interestRateItr = null;
		Iterator sweepFormatItr = null;
		Iterator sweepSchedItr = null;
		Iterator sweepBalGrpItr = null;
		AccountInterestRate accIntRate = null;
		String deleteHQL = null;
		int deleteCount = 0;
		int rowsdeleted = 0;
		Collection sweepFormatForAccounts = null;
		Collection acctSweepScheduler = null;
		Collection acctSweepBalGrp = null;
		AccountSpecificSweepFormat specificFormat = null;
		AccSweepSchedule accSweepSchedule = null;
		AccountSweepBalanceGroup accSweepBalGrp = null;
		Transaction tx = null;
		
		try (Session session = getHibernateTemplate().getSessionFactory()
				.withOptions().interceptor((SwtInterceptor) SwtUtil.getBean("SwtInterceptor")).openSession()) {
			
			log.debug(this.getClass().getName() + "- [deleteAcctDetail] - Entry");
			tx = session.beginTransaction();
						
			// Delete AccountInterestRate
			if (collAcctIntRateDeleted.size() > 0) {
				interestRateItr = collAcctIntRateDeleted.iterator();
				while (interestRateItr.hasNext()) {
					accIntRate = (AccountInterestRate) interestRateItr.next();
					String deleteAcctIntRateHQL = "delete from AccountInterestRate m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.id.accountId = :accountId";
					if (sysforma.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
						deleteAcctIntRateHQL += " and to_date(to_char(m.id.interestDateRate,'MM-DD-YYYY HH24:MI:SS'),'MM-DD-YYYY HH24:MI:SS') = to_date(:interestDateRate,'MM-DD-YYYY HH24:MI:SS')";
					} else {
						deleteAcctIntRateHQL += " and to_date(to_char(m.id.interestDateRate,'DD-MM-YYYY HH24:MI:SS'),'DD-MM-YYYY HH24:MI:SS') = to_date(:interestDateRate,'DD-MM-YYYY HH24:MI:SS')";
					}
					rowsdeleted = session.createQuery(deleteAcctIntRateHQL)
							.setParameter("hostId", accIntRate.getId().getHostId())
							.setParameter("entityId", accIntRate.getId().getEntityId())
							.setParameter("accountId", accIntRate.getId().getAccountId())
							.setParameter("interestDateRate", accIntRate.getInterestDateRateAsString())
		            .executeUpdate();
					
					if (rowsdeleted == 0) {
						throw new SwtRecordNotExist();
					}
				}
			}
			
			// Delete AccountSpecificSweepFormat
			String hqlSweepFormat = "from AccountSpecificSweepFormat accountSpecificSweepFormat where accountSpecificSweepFormat.id.hostId = :hostId and accountSpecificSweepFormat.id.entityId = :entityId and accountSpecificSweepFormat.id.accountId = :accountId";
			List sweepFormatList;
			sweepFormatList = session.createQuery(hqlSweepFormat)
					.setParameter("hostId", acct.getId().getHostId())
					.setParameter("entityId", acct.getId().getEntityId())
					.setParameter("accountId", acct.getId().getAccountId())
					.getResultList();
			if (sweepFormatList.size() > 0) {
				sweepFormatItr = sweepFormatList.iterator();
				while (sweepFormatItr.hasNext()) {
					specificFormat = (AccountSpecificSweepFormat) sweepFormatItr.next();
					session.delete(specificFormat);
				}
			}

			// Delete AccSweepSchedule
			String hqlSweepSchedule = "from AccSweepSchedule accSweep where accSweep.hostId = :hostId and accSweep.entityId = :entityId and accSweep.accountId = :accountId";
			List sweepScheduleList = session.createQuery(hqlSweepSchedule)
					.setParameter("hostId", acct.getId().getHostId())
					.setParameter("entityId", acct.getId().getEntityId())
					.setParameter("accountId", acct.getId().getAccountId())
					.getResultList();
			if (sweepScheduleList.size() > 0) {
				sweepSchedItr = sweepScheduleList.iterator();
				while (sweepSchedItr.hasNext()) {
					accSweepSchedule = (AccSweepSchedule) sweepSchedItr.next();
					session.delete(accSweepSchedule);
				}
			}

			// Delete AccountSweepBalanceGroup
			String hqlSweepBalGrp = "from AccountSweepBalanceGroup accSwpBalGrp where accSwpBalGrp.id.hostId = :hostId and accSwpBalGrp.id.entityId = :entityId and accSwpBalGrp.id.accountId = :accountId";
			List sweepBalGrpList = session.createQuery(hqlSweepBalGrp)
					.setParameter("hostId", acct.getId().getHostId())
					.setParameter("entityId", acct.getId().getEntityId())
					.setParameter("accountId", acct.getId().getAccountId())
					.getResultList();
			if (sweepBalGrpList.size() > 0) {
				sweepBalGrpItr = sweepBalGrpList.iterator();
				while (sweepBalGrpItr.hasNext()) {
					accSweepBalGrp = (AccountSweepBalanceGroup) sweepBalGrpItr.next();
					session.delete(accSweepBalGrp);
				}
			}
			
			// Delete AcctMaintenance
			deleteHQL = "delete from AcctMaintenance m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.id.accountId = :accountId";
			deleteCount = session.createQuery(deleteHQL)
					.setParameter("hostId", acct.getId().getHostId())
					.setParameter("entityId", acct.getId().getEntityId())
					.setParameter("accountId", acct.getId().getAccountId())
		            .executeUpdate();
			
			if (deleteCount == 0) {
				throw new SwtRecordNotExist();
			}
			tx.commit();
			log.debug(this.getClass().getName() + "- [deleteAcctDetail] - Exit");
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			  if (exp instanceof GenericJDBCException) {
				  GenericJDBCException gExp = (GenericJDBCException) exp;
				if (gExp.getCause() != null && !SwtUtil.isEmptyOrNull(gExp.getCause().getLocalizedMessage()) &&
						 gExp.getCause().getLocalizedMessage().indexOf(SwtErrorHandler.CHILD_RECORD_FOUND_ERROR) > -1) {
					 throw new SwtException("errors.DataIntegrityViolationExceptioninDelete");
				 }
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteAcctDetail] method " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteAcctDetail", AcctMaintenanceDAOHibernate.class);
		}
	}

	public Collection getSubColumnDataDetailList(String hostId,
			String entityId, String accountId) throws SwtException {
		log.debug("Entering getSubColumnDataDetailList(Currency curr): ");
		List list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId and acct.minacctcode = :accountId and acct.id.accountId != '*'";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			query.setParameter("accountId", accountId);
			list = query.getResultList();
		}
		return list;
	}

	public Collection getAccountTypeDetailList(String entityId, String hostId,
			String currencyId, String accttype) throws SwtException {
		log.debug("Entering getAccountTypeDetailList(Currency curr): ");
		List list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId and acct.currcode = :currencyId and acct.accttype = :accttype";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			query.setParameter("currencyId", currencyId);
			query.setParameter("accttype", accttype);
			list = query.getResultList();
		}
		return list;
	}

	/**
	 * @param entityId
	 * @param hostId
	 * 
	 * @param accttype
	 * @return Collection - getAccountTypeList
	 */
	public Collection getAccountTypeList(String entityId, String hostId,
			String accttype) throws SwtException {
		log.debug("Entering getAccountTypeList(Currency curr): ");
		List list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId and acct.accttype = :accttype and acct.id.accountId != '*'";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			query.setParameter("accttype", accttype);
			list = query.getResultList();
		}
		return list;
	}

	public Collection getAccountIDDropDown(String entityId, String hostId)
			throws SwtException {
		List list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId and acct.id.accountId != '*' order by acct.id.accountId";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			list = query.getResultList();
		}
		return list;
	}

	public AcctMaintenance copyAccountIdDetails(String hostId, String entityId,
			String accountId) throws SwtException {
		AcctMaintenance acctMaint = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			List balMaintList = query.getResultList();
			Iterator balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AcctMaintenance) balMaintListItr.next();
			}
		}
		return acctMaint;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.dao.AcctMaintenanceDAO#getAccountTypeCurrencyList(java.lang.String,
	 *      java.lang.String, java.lang.String)
	 */
	public Collection getAccountTypeCurrencyList(String entityId,
			String hostId, String currencyId) throws SwtException {
		log.debug("Entering getAccountTypeCurrencyList(Currency curr): ");
		List list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId and acct.currcode = :currencyId and acct.id.accountId != '*'";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			query.setParameter("currencyId", currencyId);
			list = query.getResultList();
		}
		return list;
	}

	public ArrayList getCurrencyMasterList(String entityId, String hostId)
			throws SwtException {
		log.debug("Entering getCurrencyMasterList(Currency curr): ");
		ArrayList list = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select acct.id.currencyCode from Currency acct where acct.id.entityId = :entityId and acct.id.hostId = :hostId";
			Query query = session.createQuery(hql);
			query.setParameter("entityId", entityId);
			query.setParameter("hostId", hostId);
			list = (ArrayList) query.getResultList();
		}
		return list;
	}

	public AcctMaintenance getMainAcctDetails(String entityId, String hostId,
			String accountId) throws SwtException {
		AcctMaintenance acctMaint = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			List balMaintList = query.getResultList();
			Iterator balMaintListItr = balMaintList.iterator();
		while (balMaintListItr.hasNext()) {
			acctMaint = (AcctMaintenance) balMaintListItr.next();
			}
		}
		return acctMaint;
	}

	public Collection getCountryList() throws SwtException {
		log.debug("Entering 'getCountryList' method");
		List countryList = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Country c order by c.countryName";
			Query query = session.createQuery(hql);
			countryList = query.getResultList();
		}
		return countryList;
	}


	/**
	 * @desc This method fetches a Collection of Account Objects which
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug("Entering getLinkAccountList method");
		List linkAccountColl = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.currcode = :currencyCode and acct.acctstatusflg = 'O' and acct.id.accountId != '*'"
					+ " and acct.id.accountId Not In (select p.id.accountId from AcctMaintenance p where p.id.entityId = :entityId2 and p.linkAccID is not null) order by acct.id.accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);
			query.setParameter("entityId2", entityId);
			linkAccountColl = query.getResultList();
		}
		log.debug("Exiting getLinkAccountList method");
		return linkAccountColl;
	}

	public Collection getLinkAccountListInChange(String hostId,
			String entityId, String currencyCode, String accountId)
			throws SwtException {
		log.debug("Entering getLinkAccountListInChange method");
		List linkAccountColl = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql;
			Query query;
		if (currencyCode.equals(SwtConstants.ALL_VALUE)) {
				hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.acctstatusflg = 'O'"
						+ " and acct.id.accountId Not in ('*', :accountId) and acct.id.accountId Not In (select p.id.accountId from AcctMaintenance p where p.linkAccID is not null) order by acct.id.accountId";
				query = session.createQuery(hql);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("accountId", accountId);
		} else {
				hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.currcode = :currencyCode and acct.acctstatusflg = 'O'"
						+ " and acct.id.accountId Not in ('*', :accountId) and acct.id.accountId Not In (select p.id.accountId from AcctMaintenance p where p.linkAccID is not null) order by acct.id.accountId";
				query = session.createQuery(hql);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
				query.setParameter("accountId", accountId);
			}
			linkAccountColl = query.getResultList();
		}
		log.debug("Exiting getLinkAccountListInChange method");
		return linkAccountColl;
	}

	public Collection getLinkedAccounts(String hostId, String entityId,
			String accountId) throws SwtException {
		log.debug("Entering getLinkedAccounts method");
		List linkedAccountsDetailsList = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.linkAccID = :accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			linkedAccountsDetailsList = query.getResultList();
		}
		log.debug("Exiting getLinkedAccounts method");
		return linkedAccountsDetailsList;
	}

	public Collection getAccInterestRateList(String hostId, String entityId,
			String accountId) throws SwtException {
		log.debug("Entering 'getAccInterestRateList' method");
		List accInterestRateListcoll = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AccountInterestRate accIntRate where accIntRate.id.hostId = :hostId and accIntRate.id.entityId = :entityId and accIntRate.id.accountId = :accountId order by accIntRate.id.interestDateRate desc";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			accInterestRateListcoll = query.getResultList();
		}
		log.debug("Exiting getAccInterestRateList method");
		return accInterestRateListcoll;
	}


	/**
	 * 
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - returns Collection of AcctMaintenance objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getMainAcctListByCurr(String hostId, String entityId,
			String currencyCode) throws SwtException {
		log.debug("Entering getMainAcctListByCurr method ");
		List acctList = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql;
			Query query;
		if (currencyCode.equals(SwtConstants.ALL_VALUE)) {
				hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.acctlevel = 'M' and acct.id.accountId != '*' order by acct.id.accountId asc";
				query = session.createQuery(hql);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
		} else {
				hql = "from AcctMaintenance acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.currcode = :currencyCode and acct.acctlevel = 'M' and acct.id.accountId != '*' order by acct.id.accountId asc";
				query = session.createQuery(hql);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);
			}
			acctList = query.getResultList();
		}
		log.debug("Exiting getMainAcctListByCurr method");
		return acctList;
	}

	public Collection getIntermediaryRecord(String hostId, String entityId,
			String currencyCode, String acctBicCode) throws SwtException {
		log.debug("Entering getIntermediaryRecord method");
		List records = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from SweepIntermediaries acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.currencyCode = :currencyCode and acct.id.targetBic = :acctBicCode";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);
			query.setParameter("acctBicCode", acctBicCode);
			records = query.getResultList();
		}
		log.debug("Exiting getIntermediaryRecord method");
		return records;
	}

	public Country getCountryDetail(String countryId) throws SwtException {
		Country country = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Country country where country.countryCode = :countryId";
			Query query = session.createQuery(hql);
			query.setParameter("countryId", countryId);
			List countryList = query.getResultList();
			Iterator countryListItr = countryList.iterator();
			while (countryListItr.hasNext()) {
				country = (Country) countryListItr.next();
			}
		} catch (Exception e) {
			log.error("An exception occured in AcctMaintenanceDAOHibernate : getCountryDetail " + e.getMessage());
			throw new SwtException(e.getMessage());
		}
		return country;
	}

	public Currency getCurrencyDetail(String entityId, String hostId,
			String currencyCode) throws SwtException {
		Currency currency = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Currency c where c.id.hostId = :hostId and c.id.entityId = :entityId and c.id.currencyCode = :currencyCode";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currencyCode", currencyCode);
			List currencyList = query.getResultList();
			Iterator currencyListItr = currencyList.iterator();
			while (currencyListItr.hasNext()) {
				currency = (Currency) currencyListItr.next();
			}
		} catch (Exception e) {
			log.error("An exception occured in AcctMaintenanceDAOHibernate : getCurrencyDetail " + e.getMessage());
			throw new SwtException(e.getMessage());
		}
		return currency;
	}
	
	public String checkIfPartyIdExists(String hostId, String entityId,
			String partyId) throws SwtException {
		log.debug("Entering checkIfPartyIdExists method");
		String result = "false";
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from Party p where p.id.hostId = :hostId and p.id.entityId = :entityId and p.id.partyId = :partyId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("partyId", partyId);
			List records = query.getResultList();
			result = (records.size() == 0) ? "false" : "true";
		}
		log.debug("Exiting checkIfPartyIdExists method");
		return result;
	}

	public String checkAccountIlmDataMember(String hostId, String entityId, String currency, String account)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String result = "";
		
		try {
			log.debug("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember] - Enter");
			
			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "SELECT DECODE (COUNT (*), 0, 'N', 'Y') IS_ILM_ACCT"
			  + " FROM TABLE ("
			          +"PKG_ILM.FN_GET_ACCS ( ?, ?, ?)) T"
			 +" WHERE ACCOUNT_Id = ?";
			
			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, currency);
			stmt.setString(4, account);
			stmt.execute();
			
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					result = rs.getString(1);
				}
			}
			
			log.debug("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [checkAccountIlmDataMember]  Exception: "
					+ ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {
				// ignore
			}

		} catch (Exception e) {
			log.error("An exception occured in "
					+ "AcctMaintenanceDAOHibernate : checkAccountIlmDataMember "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
			
		} finally {
			//Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}
		
		return result;
	}
	
	public boolean checkIfAccountExistForEntity(String hostId, String entityId, String currency, String account)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkAccountQuery = "select 1 from p_account acct where  acct.host_id=? and acct.entity_id=? and acct.currency_code=? and acct.account_id=?";

			stmt = conn.prepareStatement(checkAccountQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, currency);
			stmt.setString(4, account);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				result = rs.next();
			}

			log.debug("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctMaintenanceDAOHibernate  - [checkIfAccountExistForEntity]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {
				// ignore
			}

		} catch (Exception e) {
			log.error("An exception occured in " + "AcctMaintenanceDAOHibernate : checkIfAccountExistForEntity "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}

	public boolean checkIfAccountExists(String hostId, String accountId) throws SwtException {
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug("AcctMaintenanceDAOHibernate - [checkIfAccountExists] - Enter");

			String hql = "SELECT COUNT(a) FROM Account a WHERE a.id.hostId = :hostId AND a.id.accountId = :accountId";
			TypedQuery<Long> query = session.createQuery(hql, Long.class);
			query.setParameter("hostId", hostId);
			query.setParameter("accountId", accountId);

			boolean exists = query.getSingleResult() > 0; // getSingleResult ensures we always get a Long

			log.debug("AcctMaintenanceDAOHibernate - [checkIfAccountExists] - Exit");
			return exists;
		} catch (Exception e) {
			log.error("AcctMaintenanceDAOHibernate - [checkIfAccountExists] Exception: " + e.getMessage(), e);
			throw new SwtException(e.getMessage());
		}
	}



	public Collection<AccSweepSchedule> getAcctSweepScheduleList(String hostId, String entityId, String accountId, boolean fromAcctMaintenace) throws SwtException {
		log.debug("Entering 'getAcctSweepScheduleList' method");

		Interceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
		Collection<AccSweepSchedule> result = new ArrayList<>();

		try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
			String hql = fromAcctMaintenace
					? "from AccSweepSchedule accSweep where accSweep.hostId = :hostId and accSweep.entityId = :entityId and accSweep.accountId = :accountId order by accSweep.sweepScheduleId asc"
					: "from AccSweepSchedule accSweep where accSweep.sweepAccountHostId = :hostId and accSweep.sweepAccountEntity = :entityId and accSweep.sweepAccountId = :accountId order by accSweep.sweepScheduleId asc";

			TypedQuery<AccSweepSchedule> query = session.createQuery(hql, AccSweepSchedule.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);

			result = query.getResultList();
		} catch (Exception e) {
			log.error("Exception in getAcctSweepScheduleList: " + e.getMessage(), e);
			throw SwtErrorHandler.getInstance().handleException(e, "getAcctSweepScheduleList", ILMGeneralMaintenanceDAOHibernate.class);
		}

		log.debug("Exiting getAcctSweepScheduleList method");
		return result;
	}

	public Collection<AccSweepSchedule> getAcctSweepScheduleListBetweenAccounts(String hostId, String entityId, String currencyCode, String accountId,
																				String otherEntityId, String otherAccountId) throws SwtException {
		log.debug("Entering 'getAcctSweepScheduleListBetweenAccounts' method");
		Session session = null;
		Connection conn = null;
		CallableStatement pstmt = null;
		ResultSet rsData = null;
		AccSweepSchedule accSweepSchedule = null;
		ArrayList<AccSweepSchedule> result = new ArrayList<>();
		try {
			log.debug(this.getClass().getName() + " - [getAcctSweepScheduleListBetweenAccounts] - Entry");
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			pstmt = conn.prepareCall("{ ? = call sweeping_process.FN_GET_ACC_SWP_SCHEDULE(?,?,?,?,?)}");
			pstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			pstmt.setString(2, hostId);
			pstmt.setString(3, entityId);
			pstmt.setString(4, accountId);
			pstmt.setString(5, otherEntityId);
			pstmt.setString(6, otherAccountId);
		
			pstmt.execute();
			rsData = (ResultSet) pstmt.getObject(1);
			
			if (rsData != null) {
				while (rsData.next()) {
					accSweepSchedule = new AccSweepSchedule();
					accSweepSchedule.setCurrencyCode(rsData.getString(1));
					accSweepSchedule.setAccountId(rsData.getString("ACCOUNT_ID"));
					accSweepSchedule.setAllowMultiple(rsData.getString("ALLOW_MULTIPLE"));
					accSweepSchedule.setEntityId(rsData.getString("ENTITY_ID"));
					accSweepSchedule.setHostId(rsData.getString("HOST_ID"));
					accSweepSchedule.setMinAmount(rsData.getDouble("MIN_AMOUNT"));
					accSweepSchedule.setOtherAccSettleMethodCr(rsData.getString("OTHER_ACC_SETTLE_METHOD_CR"));
					accSweepSchedule.setOtherAccSettleMethodDr(rsData.getString("OTHER_ACC_SETTLE_METHOD_DR"));
					accSweepSchedule.setOtherAccSweepBookcodeCr(rsData.getString("OTHER_ACC_SWEEP_BOOKCODE_CR"));
					accSweepSchedule.setOtherAccSweepBookcodeDr(rsData.getString("OTHER_ACC_SWEEP_BOOKCODE_DR"));
					accSweepSchedule.setScheduleFrom(rsData.getString("SCHEDULE_FROM"));
					accSweepSchedule.setScheduleTo(rsData.getString("SCHEDULE_TO"));
					accSweepSchedule.setSweepAccountEntity(rsData.getString("SWEEP_ACCOUNT_ENTITY"));
					accSweepSchedule.setSweepAccountHostId(rsData.getString("HOST_ID"));
					accSweepSchedule.setSweepAccountId(rsData.getString("SWEEP_ACCOUNT_ID"));
					accSweepSchedule.setSweepDirection(rsData.getString("SWEEP_DIRECTION"));
					accSweepSchedule.setSweepFromBalanceType(rsData.getString("SWEEP_FROM_BALANCE_TYPE"));
					accSweepSchedule.setOtherSweepFromBalType(rsData.getString("OTHER_SWEEP_FROM_BAL_TYPE"));
					accSweepSchedule.setSweepOnGroupBalance(rsData.getString("SWEEP_ON_GROUP_BALANCE"));
					accSweepSchedule.setSweepScheduleId(rsData.getLong("SWEEP_SCHEDULE_ID"));
					accSweepSchedule.setTargetBalance(rsData.getDouble("TARGET_BALANCE"));
					accSweepSchedule.setTargetBalanceType(rsData.getString("TARGET_BALANCE_TYPE"));
					accSweepSchedule.setTargetBalanceTypeId(rsData.getString("TARGET_BALANCE_TYPE_ID"));
					accSweepSchedule.setThisAccSettleMethodCr(rsData.getString("THIS_ACC_SETTLE_METHOD_CR"));
					accSweepSchedule.setThisAccSettleMethodDr(rsData.getString("THIS_ACC_SETTLE_METHOD_DR"));
					accSweepSchedule.setThisAccSweepBookcodeCr(rsData.getString("THIS_ACC_SWEEP_BOOKCODE_CR"));
					accSweepSchedule.setThisAccSweepBookcodeDr(rsData.getString("THIS_ACC_SWEEP_BOOKCODE_DR"));
					result.add(accSweepSchedule);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getAcctSweepScheduleListBetweenAccounts] method : - " + e.getMessage());
			 throw SwtErrorHandler.getInstance().handleException(e, "getAcctSweepScheduleListBetweenAccounts", AcctMaintenanceDAOHibernate.class);
		} finally {
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsData, pstmt, conn, session);
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getAcctSweepScheduleListBetweenAccounts", AcctMaintenanceDAOHibernate.class);
			if (thrownException == null && exceptions[1] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getAcctSweepScheduleListBetweenAccounts", AcctMaintenanceDAOHibernate.class);
			if (thrownException != null)
				throw thrownException;
		}
		log.debug("Exiting getAcctSweepScheduleListBetweenAccounts method");
		return result;
	}

	@Override
	public long getAcctSweepScheduleUsedinCount(String hostId, String entityId, String accountId) throws SwtException {
		long noofRecords = 0;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "select count(*) from AccSweepSchedule c where c.sweepAccountHostId = :hostId and c.sweepAccountEntity = :entityId and c.sweepAccountId = :accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			noofRecords = (Long) query.getSingleResult();
		}
		return noofRecords;
	}

	@Override
	public AccSweepSchedule getAcctSweepScheduleDetails(String hostId, String seqNumber) throws SwtException {
		AccSweepSchedule acctMaint = null;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AccSweepSchedule accSweep where accSweep.sweepAccountHostId = :hostId and accSweep.sweepScheduleId = :seqNumber";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("seqNumber", seqNumber);
			acctMaint = (AccSweepSchedule) query.getResultStream().findFirst().orElse(null);
		}
		return acctMaint;
	}

	public void saveOrUpdateAcctScheduleSweep(AccSweepSchedule accountSchedule, String methodName) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			if ("save".equalsIgnoreCase(methodName)) {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(accountSchedule);
				tx.commit();
			} else {
				Long noofRecords = 0L;
				try (Session checkSession = getHibernateTemplate().getSessionFactory().openSession()) {
					String hql = "select count(*) from AccSweepSchedule accSweep where accSweep.sweepScheduleId = :sweepScheduleId";
					Query query = checkSession.createQuery(hql);
					query.setParameter("sweepScheduleId", accountSchedule.getSweepScheduleId());
					noofRecords = (Long) query.getSingleResult();
				}
				if (noofRecords > 0) {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
					tx = session.beginTransaction();
					session.update(accountSchedule);
					tx.commit();
				} else {
					throw new SwtRecordNotExist();
				}
			}
		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [saveOrUpdateAcctScheduleSweep] method " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveOrUpdateAcctScheduleSweep", AcctMaintenanceDAOHibernate.class);
		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);
			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	/**
	 * Delete account schedule sweep record using passed seq number
	 * @param accountSchedule
	 * @throws SwtException
	 */
	public void deleteAcctScheduleSweep(AccSweepSchedule accountSchedule) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		 int deleteCount = 0;
		try {
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			String deleteHQL = "delete from AccSweepSchedule accSweep where accSweep.sweepScheduleId = :sweepScheduleId";
			deleteCount = session.createQuery(deleteHQL)
					.setParameter("sweepScheduleId", accountSchedule.getSweepScheduleId())
		            .executeUpdate();
			
			if (deleteCount == 0) {
				throw new SwtRecordNotExist();
			}
			tx.commit();
		} catch (Exception exp) {
			exp.printStackTrace();
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteAcctScheduleSweep] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "deleteAcctScheduleSweep",
					AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}
	
	public Collection getAcctSweepBalGrpcoll(String hostId, String entityId, String accountId)
			throws SwtException {
		log.debug("hostID for getAcctSweepBalGrpcoll: " + hostId);
		List noofRecords = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AccountSweepBalanceGroup acct where acct.id.hostId = :hostId and acct.id.entityId = :entityId and acct.id.accountId = :accountId";
			Query query = session.createQuery(hql);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			noofRecords = query.getResultList();
		}
		log.debug("exiting getAcctSweepBalGrpcoll");
		return noofRecords;
	}

	public void saveAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
				session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
				tx = session.beginTransaction();
				session.save(acctSweepBalGrp);
				tx.commit();

		} catch (Exception exp) {
			try {
				if (tx != null) {
					tx.rollback();
				}
			} catch (HibernateException exception) {
				log.error("HibernateException occured in rolling back transaction. Cause : " + exception.getMessage());
			}
			log.error(this.getClass().getName() + " - Exception caught in [saveAcctSweepBalGrp] method "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "saveAcctSweepBalGrp",
					AcctMaintenanceDAOHibernate.class);

		} finally {
			HibernateException hThrownException = JDBCCloser.close(session);

			if (hThrownException != null)
				throw new SwtException(hThrownException.getMessage());
		}
	}

        public void deleteAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + " - [deleteAcctSweepBalGrp] - Enter");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(acctSweepBalGrp);
			tx.commit();
			log.debug(this.getClass().getName() + " - [deleteAcctSweepBalGrp] - Exit");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAcctSweepBalGrp] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAcctSweepBalGrp",
					AcctMaintenanceDAOHibernate.class);
		}  finally {
			JDBCCloser.close(session);
		}
	}
        
        /**
    	 * @desc This method fetches a Collection of Account Objects which
    	 * @param hostId -
    	 *            HostID
    	 * @param entityId -
    	 *            EntityID
    	 * @param currencyCode -
    	 *            CurrencyCode
    	 * @return Collection - Collection of AcctMaintenance Objects
    	 * @throws SwtException -
    	 *             SwtException
    	 */
		public List<AcctMaintenance> getLinkAccountFullList(String hostId, String entityId, String currencyCode) throws SwtException {
			log.debug("Entering getLinkAccountFullList method");

			Interceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			List<AcctMaintenance> linkAccountColl = new ArrayList<>();

			try (Session session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession()) {
				String hql = "FROM AcctMaintenance acct " +
							 "WHERE acct.id.hostId = :hostId " +
							 "AND acct.id.entityId = :entityId " +
							 "AND acct.currcode = :currencyCode " +
							 "AND acct.acctstatusflg = 'O' " +
							 "AND acct.id.accountId != '*' " +
							 "ORDER BY acct.id.accountId";

				TypedQuery<AcctMaintenance> query = session.createQuery(hql, AcctMaintenance.class);
				query.setParameter("hostId", hostId);
				query.setParameter("entityId", entityId);
				query.setParameter("currencyCode", currencyCode);

				linkAccountColl = query.getResultList();
			} catch (Exception exp) {
				log.error(this.getClass().getName() + " - Exception in [getLinkAccountFullList] method: ", exp);
				throw SwtErrorHandler.getInstance().handleException(exp, "getLinkAccountFullList", AcctMaintenanceDAOHibernate.class);
			}

			log.debug("Exiting getLinkAccountFullList method");
			return linkAccountColl;
		}



	public Collection getAcctAttributesList() throws SwtException {
    		log.debug("Entering getAcctAttributesList method");
		List acctAttributesColl = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from AccountAttributeFuncGroup acctAtt where acctAtt.id.functionalGroup = :funcGroup";
			Query query = session.createQuery(hql);
			query.setParameter("funcGroup", "SWEEP_TARGET_BALANCE");
			acctAttributesColl = query.getResultList();
		}
    		log.debug("Exiting getAcctAttributesList method");
    		return acctAttributesColl;
    	}    

    	
    	public Collection getAllowedTrgBalRulesList() throws SwtException {
    		log.debug("Entering getAllowedTrgBalRulesList method");
		List sweepRuleColl = new ArrayList();
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String hql = "from SweepRule swpRule";
			Query query = session.createQuery(hql);
			sweepRuleColl = query.getResultList();
		}
    		log.debug("Exiting getAllowedTrgBalRulesList method");
    		return sweepRuleColl;
    	}  
    	
    	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
    			int currentPage, String selectedsort) throws SwtException {

    		Party party = null;
		ArrayList<Party> partiesCollection = new ArrayList<>();
    		String sort = null;
    		String[] selectedSortArray = null;
    		String query = null;
    		PreparedStatement pst = null;
    		ResultSet rs = null;
    		Connection conn = null;
		Session session = null;
    		try {
    			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Entry");
    			selectedsort = "0|ASC";
    			if (!SwtUtil.isEmptyOrNull(selectedsort)) {
    				selectedSortArray = selectedsort.split("\\|");
    				if ("0".equals(selectedSortArray[0])) {
    					sort = "PARTY_ID ";
    				} else {
    					sort = "PARTY_NAME ";
    				}
    				if ("true".equals(selectedSortArray[1])) {
    					sort += "DESC";
    				} else {
    					sort += "ASC";
    				}
    			}
    			conn = SwtUtil.connection(session);
			if (SwtUtil.isEmptyOrNull(entityId)) {
				query = "SELECT * " +
						"FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort + ") ROW_, query.* " +
						"FROM (SELECT * " +
						"FROM p_party " +
						"WHERE  UPPER (party_id) LIKE UPPER ( ? || '%') " +
						"AND UPPER (party_name) LIKE UPPER ( ? || '%')) " +
						"query) " +
						"WHERE     ROW_ > ( ? - 1) * ? " +
    			            "AND ROW_ < ? * ? + 1";
    				pst = conn.prepareStatement(query);
    				pst.setString(1, partyId);
    				pst.setString(2, partyName);
    				pst.setInt(3, currentPage);
    				pst.setInt(4, pageSize);
    				pst.setInt(5, currentPage);
    				pst.setInt(6, pageSize);
			} else {
				query = "SELECT * " +
						"FROM (SELECT ROW_NUMBER () OVER (ORDER BY " + sort + ") ROW_, query.* " +
						"FROM (SELECT * " +
						"FROM p_party " +
						"WHERE     entity_id = ? " +
						"AND UPPER (party_id) LIKE UPPER ( ? || '%') " +
						"AND UPPER (party_name) LIKE UPPER ( ? || '%')) " +
						"query) " +
						"WHERE     ROW_ > ( ? - 1) * ? " +
    			            "AND ROW_ < ? * ? + 1";
    				pst = conn.prepareStatement(query);
    				pst.setString(1, entityId);
    				pst.setString(2, partyId);
    				pst.setString(3, partyName);
    				pst.setInt(4, currentPage);
    				pst.setInt(5, pageSize);
    				pst.setInt(6, currentPage);
    				pst.setInt(7, pageSize);
    			}
    			
    			rs = pst.executeQuery();
    			
    			while (rs.next()) {
    				party = new Party();
    				party.getId().setPartyId(rs.getString("PARTY_ID"));
    				party.setPartyName(rs.getString("PARTY_NAME"));
    				partiesCollection.add(party);
    			}

    			log.debug(this.getClass().getName() + " - [getPartySearchResult] - " + "Exit");
    			return partiesCollection;

    		} catch (Exception exp) {
    			exp.printStackTrace();
    			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
    					+ exp.getMessage());
    			throw SwtErrorHandler.getInstance().handleException(exp, "getPartySearchResult", this.getClass());
    		} finally {
    			Object[] exceptions = JDBCCloser.close(rs, pst, conn, null);
    			log.debug(this.getClass().getName() + "- [ getPartySearchResult ] - Exit");
    		}
    	}

    	
    	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {
    		String query = null;
		ArrayList<Object> paramArr = new ArrayList<>();
    		int totalCount = 0;
    		Session session = null;
    		boolean whereAdded = false;
    		try {
    			log.debug(this.getClass().getName() + "- [getTotalCount] - Entering");
    			
			if (!SwtUtil.isEmptyOrNull(entityId)) {
				query = "select count(*) from Party c where c.id.entityId = :entityId";
			} else {
				query = "select count(*) from Party c";
    			}

    			if ((partyId != null) && (partyId.trim().length() > 0)) {
				if (!whereAdded && query.indexOf("where") == -1) {
					query += " where ";
    					whereAdded = true;
				} else {
					query += " and ";
    				}
				query += "upper(c.id.partyId) like upper(:partyId || '%')";
    			}
    			if ((partyName != null) && (partyName.trim().length() > 0)) {
				if (!whereAdded && query.indexOf("where") == -1) {
					query += " where ";
    					whereAdded = true;
				} else {
					query += " and ";
				}
				query += "upper(c.partyName) like upper(:partyName || '%')";
			}
			session = getHibernateTemplate().getSessionFactory().openSession();
			Query countQuery = session.createQuery(query);
			if (!SwtUtil.isEmptyOrNull(entityId)) {
				countQuery.setParameter("entityId", entityId);
			}
			if ((partyId != null) && (partyId.trim().length() > 0)) {
				countQuery.setParameter("partyId", partyId);
			}
			if ((partyName != null) && (partyName.trim().length() > 0)) {
				countQuery.setParameter("partyName", partyName);
			}
			List resultList = countQuery.getResultList();
			if (!resultList.isEmpty() && resultList.get(0) != null) {
				Object countObj = resultList.get(0);
				if (countObj instanceof Long) {
					totalCount = ((Long) countObj).intValue();
				} else if (countObj instanceof Integer) {
					totalCount = (Integer) countObj;
				}
			}
    			return totalCount;
    		} catch (Exception exp) {
    			log.error(this.getClass().getName() + " - Exception Catched in [getTotalCount] method : - "
    					+ exp.getMessage());
    			throw SwtErrorHandler.getInstance().handleException(exp, "getTotalCount", this.getClass());
    		} finally {
    			JDBCCloser.close(session);
    			log.debug(this.getClass().getName() + "- [getTotalCount] - Exiting");
    		}
    	}
    	
    	
    	/**
    	 * This is used to check if current account is linked to another account
    	 * 
    	 * @param accountAccess
    	 * @return boolean
    	 * @throws SwtException
    	 */
    	public boolean checkAccountLinkedList(String accountId, String entityId) throws SwtException {
    		List acctlist = null;
    		boolean acctFlag = false;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug(this.getClass().getName() + " - [checkAccountLinkedList] - Entry");
			String hql = "from AcctMaintenance a where a.linkAccID = :accountId and a.id.entityId = :entityId";
			Query query = session.createQuery(hql);
			query.setParameter("accountId", accountId);
			query.setParameter("entityId", entityId);
			acctlist = query.getResultList();
			acctFlag = (acctlist.size() > 0);
			log.debug(this.getClass().getName() + " - [checkAccountLinkedList] - Exit");
    			return acctFlag;
    		} catch (Exception exp) {
    			log.error(this.getClass().getName()
    					+ " - Exception Catched in [checkAccountLinkedList] method : - "
    					+ exp.getMessage());
    			exp.printStackTrace();
    			throw SwtErrorHandler.getInstance().handleException(exp,
    					"checkAccountLinkedList", AcctMaintenanceDAOHibernate.class);
    		}
    	}

	@Override
	public String getAccountStatus(String hostId, String entityId, String accountId) throws SwtException {
		String result = "";
		String checkAccountQuery = "select ACCOUNT_STATUS_FLAG from p_account acct where acct.host_id=? and acct.entity_id=? and acct.account_id=?";
		try (
			Connection conn = ConnectionManager.getInstance().databaseCon();
			PreparedStatement stmt = conn.prepareStatement(checkAccountQuery)
		) {
			log.debug("AcctMaintenanceDAOHibernate  - [getAccountStatus] - Enter");
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, accountId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next()) {
					result = rs.getString(1);
				}
			}
			log.debug("AcctMaintenanceDAOHibernate  - [getAccountStatus] - Exit");
		} catch (SQLException ex) {
				log.error("AcctMaintenanceDAOHibernate  - [getAccountStatus]  Exception: " + ex.getMessage());
			try {
				ConnectionManager.getInstance().databaseCon().rollback();
			} catch (SQLException e) {
				// ignore
			}
		} catch (Exception e) {
			log.error("An exception occured in AcctMaintenanceDAOHibernate : getAccountStatus " + e.getMessage());
			throw new SwtException(e.getMessage());
		}
		return result;
	}


}
