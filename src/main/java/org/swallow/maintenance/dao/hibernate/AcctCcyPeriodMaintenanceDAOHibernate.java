
package org.swallow.maintenance.dao.hibernate;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.control.model.MaintenanceLogView;
import org.swallow.control.model.Role;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AcctCcyPeriodMaintenanceDAO;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.pcm.maintenance.model.AccountInGroup;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.model.CashManagementDateRecord;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.persistence.EntityManager;


@Repository ("acctCcyPeriodMaintenanceDAO")
@Transactional
public class AcctCcyPeriodMaintenanceDAOHibernate extends CustomHibernateDaoSupport implements AcctCcyPeriodMaintenanceDAO {
	public AcctCcyPeriodMaintenanceDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}


	/*
	 * Initialising Log variable for logging comments
	 */
	private static final Log log = LogFactory
			.getLog(AcctCcyPeriodMaintenanceDAOHibernate.class);

	/**
	 * This method is used to get Account Currency Maintenance Period records
	 */
	public Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecords(String hostId, String entityId, String currencyCode, Date forDate, String show)
			throws SwtException{
		ArrayList<AccountCurrencyPeriodMaintenance> acctCcyList = null;
//		List<AccountCurrencyPeriodMaintenance> acctCcyList = null;
		// variable declared for session
		Session session = null;
		// variable declared for Connection
		Connection conn = null;
		// variable declared for CallableStatment
		CallableStatement cstmt = null;
		ResultSet rs = null;
		AccountCurrencyPeriodMaintenance record = null;
		log.debug(this.getClass().getName() + " - [getAcctCcyPeriodMaintRecords] - " + "Entry");
		try {
			acctCcyList = new ArrayList<AccountCurrencyPeriodMaintenance>();


//
//
			session =  getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			// Prepared statement for the cursor
			cstmt = conn.prepareCall("{ ? = call PKG_SWEEP_RULES.FN_GET_LIST_MAINT_PERIOD(?,?,?,?,?)}");

			cstmt.registerOutParameter(1, oracle.jdbc.OracleTypes.CURSOR);
			// Setting hostid value
			cstmt.setString(2, hostId);
			// Setting AccountID debit Value
			cstmt.setString(3, entityId);
			// Setting AccountId Credit Value
			cstmt.setString(4, currencyCode);
			// Setting Amount value
			if (show.equalsIgnoreCase("C")) {
				cstmt.setString(5, "C");
			}else {
				cstmt.setString(5, "All");
			}
			if (show.equalsIgnoreCase("D")) {
				cstmt.setDate(6, SwtUtil.truncateDateTime(forDate));
			}else {
				cstmt.setNull(6, Types.DATE);
			}
			// execute the statement
			cstmt.execute();
			rs = (ResultSet) cstmt.getObject(1);
			if (rs != null) {
				while (rs.next()) {
					record = new AccountCurrencyPeriodMaintenance();
					record.getId().setAccountId(rs.getString("ACCOUNT_ID"));
					record.getId().setEntityId(rs.getString("ENTITY_ID"));
					record.getId().setHostId(rs.getString("HOST_ID"));
					record.getId().setStartDate(rs.getDate("START_DATE"));

					record.setEndDate(rs.getDate("END_DATE"));
					record.setEofBalanceSource(rs.getString("PRIOR_EOD_BAL_SOURCE"));
					record.setExcludeFillPeriod(rs.getString("EXCLUDE_FILL_DAYS"));
					record.setFillBalance(rs.getDouble("FILL_BALANCE"));
					record.setFillDays(rs.getDouble("FILL_DAYS"));
					record.setMinimumReserve(rs.getDouble("MINIMUM_RESERVE"));
					record.setMinTargetBalance(rs.getDouble("MINIMUM_TARGET_BALANCE"));
					record.setTargetAvgBalance(rs.getDouble("TARGET_AVG_BALANCE"));
					record.setTier(rs.getDouble("TIER"));
					record.setCurrnecyCode(rs.getString("CURRENCY_CODE"));




					acctCcyList.add(record);
				}

			}


		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception Catched in [getAcctCcyPeriodMaintRecords] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "getAcctCcyPeriodMaintRecords",
					AcctCcyPeriodMaintenanceDAOHibernate.class);
		}finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rs, cstmt, conn, session);

			if (exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] != null)
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

			log.debug(this.getClass().getName() + " - [getAcctCcyPeriodMaintRecords] - Exit");
		}

		log.debug(this.getClass().getName() + " - [getAcctCcyPeriodMaintRecords] - " + "Exit");
		return acctCcyList;

	}


	/**
	 * This method is used to get Account Currency Maintenance Period records
	 */
	public Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecordsPerAccount(String hostId, String entityId,  String accountId, Date forDate, String show)
			throws SwtException{
		List<AccountCurrencyPeriodMaintenance> acctCcyList = null;
		Interceptor interceptor = null;
		Session session = null;

		log.debug(this.getClass().getName() + " - [getAcctCcyPeriodMaintRecordsPerAccount] - Entry");
		try {
			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query based on the 'show' parameter
			String hql;
			if (show.equalsIgnoreCase("D")) {
				hql = "FROM AccountCurrencyPeriodMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.id.accountId = :accountId "
						+ "AND (acct.id.startDate <= :forDate AND acct.endDate >= :forDate)";
			} else if (show.equalsIgnoreCase("C")) {
				forDate = SwtUtil.getSysParamDate();
				hql = "FROM AccountCurrencyPeriodMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.id.accountId = :accountId "
						+ "AND (acct.id.startDate <= :forDate AND acct.endDate >= :forDate)";
			} else {
				hql = "FROM AccountCurrencyPeriodMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.id.accountId = :accountId";
			}

			// Create the query
			TypedQuery<AccountCurrencyPeriodMaintenance> query = session.createQuery(hql, AccountCurrencyPeriodMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			if (show.equalsIgnoreCase("D") || show.equalsIgnoreCase("C")) {
				query.setParameter("forDate", forDate);
			}

			// Execute the query and get the result list
			acctCcyList = query.getResultList();

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getAcctCcyPeriodMaintRecordsPerAccount] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAcctCcyPeriodMaintRecordsPerAccount", AcctCcyPeriodMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		log.debug(this.getClass().getName() + " - [getAcctCcyPeriodMaintRecordsPerAccount] - Exit");
		return acctCcyList;

	}

	/**
	 * To find list of accounts This function appends various filtering
	 * condition in the select query
	 */
	public Collection getAccountIdDropDown(String hostId, String entityId, String currCode, String screenName)
			throws SwtException {
		List<AcctMaintenance> accountIdList = null;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug("Entering into getAccountIdDropDown() method");
			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate()
					.getSessionFactory()
					.withOptions()
					.interceptor(interceptor)
					.openSession();

			// Define the HQL query based on the screenName parameter
			String hql;
			if ("add".equalsIgnoreCase(screenName)) {
				hql = "FROM AcctMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.currcode = :currCode "
						+ "AND acct.acctstatusflg != 'C' "
						+ "ORDER BY acct.id.accountId ASC";
			} else {
				hql = "FROM AcctMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.currcode = :currCode "
						+ "ORDER BY acct.id.accountId ASC";
			}

			// Create the query
			TypedQuery<AcctMaintenance> query = session.createQuery(hql, AcctMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currCode", currCode);

			// Execute the query and get the result list
			accountIdList = query.getResultList();

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getAccountIdDropDown] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getAccountIdDropDown", AcctCcyPeriodMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		log.debug(this.getClass().getName() + " - [getAccountIdDropDown] - Exit");
		return accountIdList;
	}



	/**
	 * This method is used to check if the Start-End Date period overlaps another existing record having the same host/entity/account
	 */

	public boolean checkIfOverlaps(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException {

		boolean overlaps = false;

		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			log.debug("AcctCcyPeriodMaintenanceDAOHibernate  - [checkIfOverlaps] - Enter");

			String hql = "from AccountCurrencyPeriodMaintenance ccyAcct " +
						 "where ccyAcct.id.hostId = :hostId " +
						 "and ccyAcct.id.entityId = :entityId " +
						 "and ccyAcct.id.accountId = :accountId";

			TypedQuery<AccountCurrencyPeriodMaintenance> query = session.createQuery(hql, AccountCurrencyPeriodMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);

			List<AccountCurrencyPeriodMaintenance> list = query.getResultList();

			for (AccountCurrencyPeriodMaintenance acctCcyPeriod : list) {
				Date existingStart = acctCcyPeriod.getId().getStartDate();
				Date existingEnd = acctCcyPeriod.getEndDate();

				if ((endDate.equals(existingStart) || startDate.equals(existingStart)
					 || startDate.equals(existingEnd) || startDate.before(existingStart)) && endDate.after(existingStart)
					|| startDate.before(existingEnd) && endDate.after(existingEnd)
					|| startDate.before(existingStart) && endDate.after(existingEnd)
					|| startDate.after(existingStart) && (endDate.before(existingEnd) || endDate.equals(existingEnd))) {
					return false;
				}
			}

			log.debug("AcctCcyPeriodMaintenanceDAOHibernate  - [checkIfOverlaps] - Exit");
		} catch (Exception e) {
			log.error("An exception occured in AcctCcyPeriodMaintenanceDAOHibernate : checkIfOverlaps " + e.getMessage());
			throw new SwtException(e.getMessage());
		}

		return true;
	}


	/**
	 * This method is used to check if record already exists for given host, entity and account
	 */

	public boolean checkIfAcctCcyPeriodExists(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException {
		Connection conn = null;
		ResultSet res = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		boolean result = false;

		try {
			log.debug("AcctCcyPeriodMaintenanceDAOHibernate  - [checkIfAcctCcyPeriodExists] - Enter");

			conn = ConnectionManager.getInstance().databaseCon();
			String checkEntityQuery =  "select 1 from P_CCY_ACC_MAINT_PERIOD acct where acct.host_id=? "
					+ "and acct.entity_id=? and acct.account_id=? "
					+ "and acct.start_date =  ? "
					+ "and acct.end_date =  ? ";

			stmt = conn.prepareStatement(checkEntityQuery);
			stmt.setString(1, hostId);
			stmt.setString(2, entityId);
			stmt.setString(3, accountId);
			stmt.setDate(4, SwtUtil.truncateDateTime(startDate));
			stmt.setDate(5, SwtUtil.truncateDateTime(endDate));
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				if(rs.next() == false) {
					result=false;
				}else {
					result=true;
				}

			}

			log.debug("AcctCcyPeriodMaintenanceDAOHibernate  - [checkIfAcctCcyPeriodExists] - Exit");
		} catch (SQLException ex) {
			try {
				log.error("AcctCcyPeriodMaintenanceDAOHibernate  - [checkIfAcctCcyPeriodExists]  Exception: " + ex.getMessage());
				// rollBack
				conn.rollback();
			} catch (SQLException e) {

			}

		} catch (Exception e) {
			log.error("An exception occured in " + "AcctCcyPeriodMaintenanceDAOHibernate : checkIfAcctCcyPeriodExists "
					+ e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// Close conn and nullify objects
			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
		}

		return result;
	}


	/**
	 * This method is used to save new account currency period in P_CCY_ACC_MAINT_PERIOD
	 */

	public void saveAcctCcyPeriodRecord(AccountCurrencyPeriodMaintenance acctCcyPeriod,String action) throws SwtException {

		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			log.debug(this.getClass().getName() + " - [saveAcctCcyPeriodRecord] - Enter");

			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Execute the action based on the provided action string
			if ("save".equalsIgnoreCase(action)) {
				session.save(acctCcyPeriod);
			} else {
				// Use TypedQuery to find existing records
				String hql = "FROM AccountCurrencyPeriodMaintenance acct WHERE acct.id.hostId = :hostId "
						+ "AND acct.id.entityId = :entityId "
						+ "AND acct.id.accountId = :accountId "
						+ "AND acct.id.startDate = :startDate";
				TypedQuery<AccountCurrencyPeriodMaintenance> query = session.createQuery(hql, AccountCurrencyPeriodMaintenance.class);
				query.setParameter("hostId", acctCcyPeriod.getId().getHostId());
				query.setParameter("entityId", acctCcyPeriod.getId().getEntityId());
				query.setParameter("accountId", acctCcyPeriod.getId().getAccountId());
				query.setParameter("startDate", acctCcyPeriod.getId().getStartDate());

				List<AccountCurrencyPeriodMaintenance> records = query.getResultList();

				// Check if exactly one record exists
				if (records.size() == 1) {
					session.merge(acctCcyPeriod);
				}
			}

			// Commit the transaction
			tx.commit();

			// Update the log table
			updateAccountPeriodMaintenanceLogTable(acctCcyPeriod.getId().getHostId(), acctCcyPeriod.getId().getEntityId(),
					acctCcyPeriod.getId().getAccountId(), acctCcyPeriod.getId().getStartDate(), false);

			log.debug(this.getClass().getName() + " - [saveAcctCcyPeriodRecord] - Exit");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (HibernateException rollbackException) {
					log.error("HibernateException occurred in [saveAcctCcyPeriodRecord] when rolling back transaction. Cause: " + rollbackException.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [saveAcctCcyPeriodRecord] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "saveAcctCcyPeriodRecord", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}


	/**
	 * This method is used to delete existing account currency period from  P_CCY_ACC_MAINT_PERIOD
	 */

	@SuppressWarnings("unchecked")
	public void deleteAcctCcyPeriodMaintRecord(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;

		try {
			logger.debug(this.getClass().getName() + " - [deleteAcctCcyPeriodMaintRecord] - Entry");

			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();

			// Use TypedQuery to find the record to delete
			String hql = "FROM AccountCurrencyPeriodMaintenance acct WHERE acct.id.hostId = :hostId "
					+ "AND acct.id.entityId = :entityId "
					+ "AND acct.id.accountId = :accountId "
					+ "AND acct.id.startDate = :startDate";

			TypedQuery<AccountCurrencyPeriodMaintenance> query = session.createQuery(hql, AccountCurrencyPeriodMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("accountId", accountId);
			query.setParameter("startDate", startDate);

			List<AccountCurrencyPeriodMaintenance> records = query.getResultList();

			// Check if exactly one record exists
			if (records.size() == 1) {
				AccountCurrencyPeriodMaintenance recordToDelete = records.get(0);
				session.delete(recordToDelete);
			}

			// Commit the transaction
			tx.commit();

			// Update the log table
			updateAccountPeriodMaintenanceLogTable(hostId, entityId, accountId, startDate, true);

			logger.debug(this.getClass().getName() + " - [deleteAcctCcyPeriodMaintRecord] - Exit");
		} catch (Exception e) {
			if (tx != null) {
				try {
					tx.rollback();
				} catch (HibernateException rollbackException) {
					log.error("HibernateException occurred in [deleteAcctCcyPeriodMaintRecord] when rolling back transaction. Cause: " + rollbackException.getMessage());
				}
			}
			log.error(this.getClass().getName() + " - Exception caught in [deleteAcctCcyPeriodMaintRecord] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "deleteAcctCcyPeriodMaintRecord", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}
	}


	/**
	 * This method does retrive log details which are logged by particular ip from VW_S_MAINTENANCE_LOG table
	 *
	 * @param fromDate
	 * @param toDate
	 * @param reference
	 * @return Collection
	 * @throws SwtException
	 */


	public Collection getMaintenanceLogList( Date fromDate, Date toDate, String reference) throws SwtException {

		List<MaintenanceLogView> records = null;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug("Entering into  getMaintenanceLogList() method");
			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			// Open a new session with the interceptor
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			// Define the Native SQL query
			String sql;
			if (!SwtUtil.isEmptyOrNull(reference)) {
				sql = "SELECT * FROM VW_S_MAINTENANCE_LOG ml "
						+ "WHERE TRUNC(ml.LOG_DATE) BETWEEN :fromDate AND :toDate "
						+ "AND ml.TABLE_NAME = 'AccountCurrencyPeriodMaintenance' "
						+ "AND ml.REFERENCE LIKE :reference "
						+ "ORDER BY ml.MAIN_SEQUENCE DESC";
			} else {
				sql = "SELECT * FROM VW_S_MAINTENANCE_LOG ml "
						+ "WHERE TRUNC(ml.LOG_DATE) BETWEEN :fromDate AND :toDate "
						+ "AND ml.TABLE_NAME = 'AccountCurrencyPeriodMaintenance' "
						+ "ORDER BY ml.MAIN_SEQUENCE DESC";
			}

			// Create the SQL query
			Query query = session.createNativeQuery(sql, MaintenanceLogView.class);

			// Bind parameters
			query.setParameter("fromDate", fromDate);
			query.setParameter("toDate", toDate);
			if (!SwtUtil.isEmptyOrNull(reference)) {
				query.setParameter("reference", "%" + reference);  // Use wildcard for LIKE
			}
			// Execute the query and get the result list
			records = query.getResultList();

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getMaintenanceLogList] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getMaintenanceLogList", AcctCcyPeriodMaintenanceDAOHibernate.class);
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		log.debug(this.getClass().getName() + " - [getMaintenanceLogList] - Exit");
		return records;
	}


	/**
	 * This method does retrive log view details which are logged by particular ip and reference from s_maintenance_log table 
	 *
	 * @param hostId
	 * @param logDate
	 * @param userId
	 * @param ipAddress
	 * @param tableName
	 * @param reference
	 * @param action
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getViewLogDetails(String hostId, String userId, Date logDate, String ipAddress, String tableName, String reference, String action) throws SwtException {

		List<MaintenanceLog> records = null;
		Interceptor interceptor = null;
		Session session = null;

		try {
			log.debug(this.getClass().getName() + " - [getViewLogDetails] - Entry");
			// Obtain the interceptor bean
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");

			// Open a new session with the interceptor
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();

			// Define the HQL query
			String hql = "FROM MaintenanceLog log1 WHERE log1.hostId = :hostId "
					+ "AND log1.userId = :userId "
					+ "AND log1.logDate = :logDate "
					+ "AND log1.ipAddress = :ipAddress "
					+ "AND log1.tableName = :tableName "
					+ "AND log1.reference = :reference "
					+ "AND log1.action = :action";

			// Create the query
			TypedQuery<MaintenanceLog> query = session.createQuery(hql, MaintenanceLog.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			query.setParameter("logDate", logDate);
			query.setParameter("ipAddress", ipAddress);
			query.setParameter("tableName", tableName);
			query.setParameter("reference", reference);
			query.setParameter("action", action);

			// Execute the query and get the result list
			records = query.getResultList();

		} catch (Exception e) {
			log.error(this.getClass().getName() + " - Exception caught in [getViewLogDetails] method: " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getViewLogDetails", this.getClass());
		} finally {
			if (session != null) {
				session.close(); // Ensure the session is closed to prevent resource leaks
			}
		}

		log.debug(this.getClass().getName() + " - [getViewLogDetails] - Exit");
		return records;
	}


	/**
	 * This method is used to get account currency code  from p_account table
	 */
	public String getAccountCcy(String accountId) throws SwtException{
		log.debug(this.getClass().getName() + " - [getAccountCcy] - "
				+ "Entry");
		/* Method's local variable declaration */
		Connection conn = null;
		Session session = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String ccy = new String();
		try {
			conn = ConnectionManager.getInstance().databaseCon();
			String getAccountStatus = "select currency_code from p_account acct where acct.account_id=?";
			stmt = conn.prepareStatement(getAccountStatus);
			stmt.setString(1, accountId);
			stmt.execute();
			/* Fetching the Result */
			rs = (ResultSet) stmt.getResultSet();
			if (rs != null) {
				/* Loop to iterate result set */
				while (rs.next()) {
					ccy = rs.getString(1);
				}
			}
			log.debug(this.getClass().getName() + " - [getAccountCcy] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountCcy] method : - "
					+ exp.getMessage());
		} finally {
			JDBCCloser.close(rs, stmt, conn, session);

		}
		return ccy;
	}

	public void updateAccountPeriodMaintenanceLogTable(String hostId, String entityId, String accountId, Date startDate, boolean isDelete) throws SwtException {
		log.debug("Entering into  updateAccountPeriodMaintenanceLogTable() method");

		List records = null;
		String reference = null;
		Connection conn = null;
		PreparedStatement pst=null;
		PreparedStatement statForMainLog = null;
		Session session = null;
		Interceptor interceptor= null;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
			String date = simpleDateFormat.format(startDate);

			reference = entityId + '/' + accountId +"/"+ date;
			if (!SwtUtil.isEmptyOrNull(reference)) {
				if(isDelete) {
					//statement object is initialized
					// prepared Statement Instance

					conn=ConnectionManager.getInstance().databaseCon();
					Long sequ = SequenceFactory.getSequenceFromDbAsLong("SEQ_P_CCY_ACC_MAINT_PERIOD_LOG");

					statForMainLog = conn
							.prepareStatement("insert into P_CCY_ACC_MAINT_PERIOD_LOG values(?,?,?,?,?,?,?,?,?,?,?)");
					statForMainLog.setLong(1, sequ);
					statForMainLog.setString(2, SwtUtil.getCurrentHostId());
					statForMainLog.setString(3, entityId);
					statForMainLog.setString(4, accountId);
					statForMainLog.setDate(5, SwtUtil.truncateDateTime(startDate));
					statForMainLog.setDate(6, new java.sql.Date(SwtUtil.getSysParamDate().getTime()));
					statForMainLog.setString(7, UserThreadLocalHolder.getUser());
					statForMainLog.setString(8, "D");
					statForMainLog.setString(9, "");
					statForMainLog.setString(10, "");
					statForMainLog.setString(11, "");

					statForMainLog.execute();
					conn.commit();

				}else {
					interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
					session = getHibernateTemplate()
							.getSessionFactory()
							.withOptions().interceptor(interceptor).openSession();
					String hql = "from MaintenanceLog m where m.tableName = :tableName and m.reference like :reference";
					TypedQuery<MaintenanceLog> query = session.createQuery(hql, MaintenanceLog.class);
					query.setParameter("tableName", "AccountCurrencyPeriodMaintenance");
					query.setParameter("reference", reference); // Assuming 'reference' is a variable holding the value
					// Execute the query and return results
					records = query.getResultList();
					if(!records.isEmpty()) {
						//statement object is initialized

						conn=ConnectionManager.getInstance().databaseCon();
						String delete_facilities="DELETE FROM P_CCY_ACC_MAINT_PERIOD_LOG  where HOST_ID=? and ENTITY_ID=? and ACCOUNT_ID=? and START_DATE=? ";
						//declare statement for delete
						pst=conn.prepareStatement(delete_facilities);
						pst.setString(1, hostId);
						pst.setString(2, entityId);
						pst.setString(3, accountId);
						pst.setDate(4, SwtUtil.truncateDateTime(startDate));
						//execute delete query
						pst.executeUpdate();
						//committed the transaction
						conn.commit();


						// prepared Statement Instance

						// insert log values into S_MAINTENACE_LOG table
						statForMainLog = conn
								.prepareStatement("insert into P_CCY_ACC_MAINT_PERIOD_LOG values(?,?,?,?,?,?,?,?,?,?,?)");


						for (Iterator iterator = records.iterator(); iterator.hasNext();) {
							MaintenanceLog logRecord = (MaintenanceLog) iterator.next();
							Long sequ = SequenceFactory.getSequenceFromDbAsLong("SEQ_P_CCY_ACC_MAINT_PERIOD_LOG");

							statForMainLog.setLong(1, sequ);
							statForMainLog.setString(2, SwtUtil.getCurrentHostId());
							statForMainLog.setString(3, entityId);
							statForMainLog.setString(4, accountId);
							statForMainLog.setDate(5, SwtUtil.truncateDateTime(startDate));
							statForMainLog.setDate(6, new java.sql.Date(logRecord.getLogDate().getTime()));
							statForMainLog.setString(7, logRecord.getuserId());
							statForMainLog.setString(8, logRecord.getAction());
							statForMainLog.setString(9, logRecord.getColumnName());
							statForMainLog.setString(10, logRecord.getOldValue());
							statForMainLog.setString(11, logRecord.getNewValue());
							statForMainLog.addBatch();

						}

						statForMainLog.executeBatch();
						conn.commit();


					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - Exception Catched in [getMaintenanceLogList] method : - "
					+ e.getMessage());

			throw SwtErrorHandler.getInstance().handleException(e, "updateAccountPeriodMaintenanceLogTable",
					AcctCcyPeriodMaintenanceDAOHibernate.class);
		}finally {

			// edited by KaisBS for mantis 1745 : introducing the implementation of the
			// JDBCCloser class
			SwtException thrownException = null;
			JDBCCloser.close(statForMainLog);
			Object[] exceptions = JDBCCloser.close(null, pst, conn, null);

			if (exceptions[0] != null)
				thrownException = new SwtException(((SQLException) exceptions[0]).getMessage());

			if (thrownException == null && exceptions[1] != null)
				thrownException = new SwtException(((HibernateException) exceptions[1]).getMessage());

			if (thrownException != null)
				throw thrownException;

		}

		log.debug(this.getClass().getName() + " - [getCashRsvBalanceGridData] - " + "Exit");
	}

}
