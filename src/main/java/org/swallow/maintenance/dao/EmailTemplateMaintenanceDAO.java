package org.swallow.maintenance.dao;

import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EmailTemplate;
import org.swallow.dao.DAO;

public interface EmailTemplateMaintenanceDAO extends DAO {

	public void saveEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public void updateEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public void deleteEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public EmailTemplate getEmailTemplate(String templateId)
			throws SwtException;

	public Collection<EmailTemplate> getEmailTemplateList() throws SwtException;
}