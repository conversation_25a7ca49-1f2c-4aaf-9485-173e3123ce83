/*
 * @(#)QualityActionDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;


import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;


public interface QualityActionDAO extends DAO{
	/**
	 * Get the collection of  QualityAction from the data base table P_MATCH_QUALITY
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param posLevel
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getQualityAction(String hostId,String entityId,String currencyCode, Integer posLevel)
	throws SwtException;
	
	
	/**
	 * Save the QualityAction in the database table P_MATCH_QUALITY
	 * @param qualityActionObj
	 * @return None
	 * @throws SwtException
	 */
	public void save(MatchQuality qualityActionObj) throws SwtException;
	
	
	/**
	 * Update the QualityAction in the database table P_MATCH_QUALITY
	 * @param qualityActionObj
	 * @return None
	 * @throws SwtException
	 */
	public void update(MatchQuality qualityActionObj) throws SwtException;
	
	
	/**
	 * Delete the QualityAction from the database table P_MATCH_QUALITY
	 * @param qualityActionObj
	 * @return None
	 * @throws SwtException
	 */
	public void delete(QualityAction qualityActionObj) throws SwtException;
}
