/*
 * @(#)PartyManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.PartyDAO;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.model.PartyAlias;
import org.swallow.util.LabelValueBean;

/**
 * 
 * This class is Interface for Manager layer to get Party,Add/Change/Delete
 * Party and Get Alias ,Add/Change/Delete alias
 * 
 */
public interface PartyManager {
	/**
	 * Get the Party details from database of P_PARTY
	 * 
	 * @param party
	 * @return
	 * @throws SwtException
	 */

	public Collection getCustodianList(Party party) throws SwtException;

	/**
	 * Get the Total no of records from P_PARTY table
	 * 
	 * @param party
	 * @return int
	 * @throws SwtException
	 */
	public int getTotalCount(Party party) throws SwtException;

	/**
	 * Get the filtered record from P_PARTY table
	 * 
	 * @param party
	 * @return int
	 * @throws SwtException
	 */
	public int getFilterCount(Party party) throws SwtException;

	/**
	 * This is used to save the party details in P_PARTY.
	 * 
	 * @param custodian
	 * @return
	 * @throws SwtException
	 */

	public void saveCusdoianDetail(Party custodian) throws SwtException;

	/**
	 * Modified the updated datas in P_PARTY table
	 * 
	 * @param custodian
	 * @throws SwtException
	 * @return
	 */
	public void updateCustodianDetail(Party custodian) throws SwtException;

	/**
	 * Remove the records from P_PARTY table
	 * 
	 * @param custodian
	 * @return
	 * @throws SwtException
	 */
	public void deleteCustodianDetail(Party custodian) throws SwtException;

	/**
	 * This is used to set the partyDAO
	 * 
	 * @param custodianDAO
	 * @return
	 */

	public void setPartyDAO(PartyDAO custodianDAO);

	/**
	 * This is used to get the custodian flag from P_PARTY table
	 * 
	 * @param entityId
	 * @param hostId
	 * @param partyId
	 * @return
	 * @throws SwtException
	 */
	public Party getCustodianFlag(String entityId, String hostId, String partyId)
			throws SwtException;

	/**
	 * This method is used to fetch the Party details from P_PARTY table.
	 * 
	 * @param party
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getPartyList(Party party) throws SwtException;

	/**
	 * This function fetches party alias details from P_PARTY_ALLIAS table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getPartyAliasList(String hostId, String entityId,
			String partyId) throws SwtException;

	/**
	 * This function saves the party alias details in P_PARTY_ALLIAS table
	 * 
	 * @param partyAlias
	 * @return none
	 * @throws SwtException
	 */
	public void saveAliasDetails(PartyAlias partyAlias) throws SwtException;

	/**
	 * This function remove the party alias details from P_PARTY_ALLIAS table
	 * 
	 * @param partyAlias
	 * @return
	 * @throws SwtException
	 */

	public void deleteAliasDetails(PartyAlias partyAlias) throws SwtException;

	/*
	 * Start:Code Modified For Mantis 1680 by chinniah on 16-Feb-2012:DNB
	 * Screen/Reporting - Mid Term Solution
	 */
	/**
	 * This method is used to get the Party List
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getParties(String hostId, String entityId)
			throws SwtException;

	/**
	 * 
	 * This method is used to Check whether the changed Parent Party is forming
	 * cyclic loop.If it is forming cyclic loop it will return '1' other wise it
	 * will return '0' for valid Parent Id
	 * 
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @param changeParentId
	 * @return int
	 * @throws SwtException
	 */
	public int checkParentId(String hostId, String entityId, String partyId,
			String changeParentId) throws SwtException;

	/**
	 * This method is used to check whether the given party is referred by any
	 * child party in the same entity or not, while deleting the party. If it is
	 * referred system will not allow deleting the party.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param partyId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkPartyUse(String hostId, String entityId, String partyId)
			throws SwtException;
	/*
	 * End:Code Modified For Mantis 1680 by chinniah on 16-Feb-2012:DNB
	 * Screen/Reporting - Mid Term Solution
	 */
	public String getPartiesAsXML(String hostId, String entityId) throws SwtException;
}
