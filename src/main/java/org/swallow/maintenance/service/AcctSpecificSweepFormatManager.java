package org.swallow.maintenance.service;

import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;

public interface AcctSpecificSweepFormatManager {

	public void saveAccountSpecificSweepFormat(
			AccountSpecificSweepFormat accountSpecificSweepFormat)
			throws SwtException;

	public void updateAccountSpecificSweepFormat(
			AccountSpecificSweepFormat accountSpecificSweepFormat)
			throws SwtException;

	public void deleteAccountSpecificSweepFormat(
			AccountSpecificSweepFormat accountSpecificSweepFormat)
			throws SwtException;

	public AccountSpecificSweepFormat getAccountSpecificSweepFormat(
			String hostId, String entityId, String accountId, String specifiedAccountEntityId,
			String specifiedAccountId) throws SwtException;

	public Collection<AccountSpecificSweepFormat> getAccountSpecificSweepFormatList(String hostId, String entityId,  String accountId)
			throws SwtException;
	
	public String getSpecificAccountName(String AccountId) throws SwtException;
}