package org.swallow.maintenance.service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.CriticalPaymentTypeDAOHibernate;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;
import org.swallow.maintenance.model.CriticalPaymentExp;
import org.swallow.maintenance.model.CriticalPaymentType;

public interface CriticalPaymentTypeManager {
	
	public Collection<CriticalPaymentType> getCriticalPayTypeList(String hostId, String entityId) throws SwtException;
	
	public void deleteCriticalPayType(CriticalPaymentType criticalPaymentType) throws SwtException;
	
	public String checkEntityAccess(String hostId, String roleId, String entityId) throws SwtException;

	public void saveCriticalPaymentType(CriticalPaymentType criticalPaymentType, String action) throws SwtException;

	public CriticalPaymentType getCriticalPayTypeDetails(String entityId, String hostId, String cpTypeId) throws SwtException;

	public List<CriticalPaymentExp>  getExpectedTimeList(String hostId, String entityId, String cpTypeId) throws SwtException;
	public void deleteAllEntityCriticalPayExp(String hostId, String entityId, String type, Boolean requireAuthorisation) throws SwtException;

	public void saveCriticalPaymentExp(List<CriticalPaymentExp> list) throws SwtException;

	public void crudCriticalPayExp(List<CriticalPaymentExp> listCcyExpectedTimeAdd, List<CriticalPaymentExp> listCcyExpectedTimeUpdate, List<CriticalPaymentExp> listCcyExpectedTimeDelete, Long maintEventId) throws SwtException;
	public CriticalPaymentTypeDAOHibernate.ValidationResult validateAndExecuteUpdate(String tableName, String setClause,
																					 String whereClause, int timeoutSeconds) throws SwtException;
}
