package org.swallow.maintenance.service;

import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.EmailTemplate;

public interface EmailTemplateMaintenanceManager {

	public void saveEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public void updateEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public void deleteEmailTemplate(EmailTemplate emailTemplate)
			throws SwtException;

	public EmailTemplate getEmailTemplate(String templateId)
			throws SwtException;

	public Collection<EmailTemplate> getEmailTemplateList() throws SwtException;
}