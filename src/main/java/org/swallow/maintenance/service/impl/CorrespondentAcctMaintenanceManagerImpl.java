package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CorrespondentAcctMaintenanceDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.maintenance.service.CorrespondentAcctMaintenanceManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;

/*
 * This is manager class for Correspondent Account maintenance
 */
@Component ("correspondentAcctMaintenanceManager")
public class CorrespondentAcctMaintenanceManagerImpl implements
		CorrespondentAcctMaintenanceManager {
	@Autowired
	private CorrespondentAcctMaintenanceDAO correspondentAcctMaintenanceDAO;

	/**
	 * @param correspondentAcctMaintenanceDAO
	 *            the correspondentAcctMaintenanceDAO to set
	 */
	public void setCorrespondentAcctMaintenanceDAO(
			CorrespondentAcctMaintenanceDAO correspondentAcctMaintenanceDAO) {
		this.correspondentAcctMaintenanceDAO = correspondentAcctMaintenanceDAO;
	}

	private final Log log = LogFactory
			.getLog(CorrespondentAcctMaintenanceManagerImpl.class);

	/**
	 * Asks the DAO for a list of DefaultAcct's matching the given entitiyId and
	 * hostId
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId, int startRowNumber, int endRowNumber, String messageType, String currencyCode, String userId, String selectedSort)
			throws SwtException {
		return correspondentAcctMaintenanceDAO.getList(entityId, hostId, startRowNumber, endRowNumber, messageType, currencyCode, userId, selectedSort);
	}

	/**
	 * Asks the DAO to delete the given CorrespondentAcct
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		correspondentAcctMaintenanceDAO.deleteCorrespondentAcct(acct);
	}

	/**
	 * Asks the DAO for the record matching the PK data stored in
	 * CorrespondentAcct parameter
	 * 
	 * @param acct
	 * @return CorrespondentAcct
	 * @throws SwtException
	 */
	public CorrespondentAcct getCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		return correspondentAcctMaintenanceDAO.getCorrespondentAcct(acct);
	}

	/**
	 * Returns a collection of open account label-value beans for use in select
	 * elements and the like
	 * 
	 * @param hostId
	 * @param entityId
	 * @param messageType
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCorrespondentAcctList(String hostId, String entityId,
			String messageType, String currencyCode) throws SwtException {
		return correspondentAcctMaintenanceDAO.getCorrespondentAcctList(hostId,
				entityId, messageType, currencyCode);
	}

	/**
	 * Asks the DAO to save a new copy of the given CorrespondentAcct object
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	/*
	 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
	 * records added by betcy on 18-02-2011
	 */
	public void saveCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [saveCorrespondentAcct] - Entering ");
			correspondentAcctMaintenanceDAO.saveCorrespondentAcct(acct);
			log.debug(this.getClass().getName()
					+ "- [saveCorrespondentAcct] - Exititng ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCorrespondentAcct] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCorrespondentAcct",
					CorrespondentAcctMaintenanceManagerImpl.class);
		}
		/*
		 * End : Modified for Mantis 1366-Remove entry to log if duplicate
		 * records added by betcy on 18-02-2011
		 */
	}

	/**
	 * Checks that the given form bean contains a valid accountId and if it
	 * does, asks the DAO to update it
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void updateCorrespondentAcct(CorrespondentAcct acct) throws SwtException {
		correspondentAcctMaintenanceDAO.updateRecord(acct);
	}

	/**
	 * This method is basically used to retrieve List of Account Ids for a given
	 * inputs.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            movType
	 * @return List of Account Id's
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAccountIdDropDown(String hostId,
			String entityId, String currencyCode, String roleId) throws SwtException {

		log.debug(this.getClass().getName() + " - getAccountIdDropDown () - "
				+ " Entry ");
		ArrayList<LabelValueBean> accountIdList = new ArrayList<LabelValueBean>();
		try {
			Collection accounts = correspondentAcctMaintenanceDAO
					.getAccountIdDetails(hostId, entityId, currencyCode);
			Iterator itr = accounts.iterator();
			AcctMaintenance acct = null;
			// get the map for currency with full access
			Map<String, String> currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, SwtUtil.getCurrentHostId(),
					entityId);
			
			
			while (itr.hasNext()) {
				acct = (AcctMaintenance) (itr.next());
				if(currencyMap.get(acct.getCurrcode()) != null) {
					String statusFlag = acct.getAcctstatusflg();
					if (statusFlag != null
							&& statusFlag
									.equalsIgnoreCase(SwtConstants.ACCOUNT_STATUS_FLAG_OPEN)) {
						accountIdList.add(new LabelValueBean(acct.getAcctname(),
								acct.getId().getAccountId()));
					}
				}
			}
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getAccountList", DefaultAcctMaintenanceManagerImpl.class);
			throw swtexp;
		}
		log.debug(this.getClass().getName() + " - getAccountIdDropDown () - "
				+ " Exit ");
		return accountIdList;
	}

	/**
	 * 
	 * @return list
	 * @throws SwtException
	 */
	public Collection<String> getMessageTypesDropdownValues()
			throws SwtException {
		log.debug(this.getClass().getName() + " - getMessageTypesList () - "
				+ " Entry ");
		return correspondentAcctMaintenanceDAO.getMessageTypesList();
	}
}
