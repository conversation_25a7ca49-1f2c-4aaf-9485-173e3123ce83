/*
 * @(#)CountryManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.maintenance.dao.*;
import org.swallow.maintenance.model.*;
import org.swallow.maintenance.service.*;
import org.swallow.util.LabelValueBean;
import org.swallow.exception.*;

import java.util.*;


@Component("countryManager")
public class CountryManagerImpl implements CountryManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CountryManagerImpl.class);
	
	/**
	 * Initializing ArrayList object for this class
	 */
	private ArrayList countryList=null;
	
	/**
	 * Initializing CountryDAO object for this class
	 */
	@Autowired
	private CountryDAO dao;

	/**
	 * CountryDAO to Set
	 * @param  CountryDAO
	 */
    public void setCountryDAO(CountryDAO dao) {
        this.dao = dao;
    }
    
	/**
     * Method to get List of Countries 
     * @return ArrayList
     * @throws SwtException
     */
	public ArrayList getCountries() throws SwtException
	{
		try{ 
		log.debug(this.getClass().getName() + " - [getCountries] - "+ "Entry");
		if(countryList==null){
			/* Stores the countries list */
			countryList = new ArrayList();
			/* Iterator for fetching the Country details and added into a Label
			 * Value bean
			 */
			Iterator itr = (dao.getCountries()).iterator(); 
			Country country = null;
			/* Adding a default empty value */
			countryList.add(new LabelValueBean("",""));
			/* Iterated the list of countries */
			while(itr.hasNext())
			{
				country = (Country)(itr.next());
				//Adding the country name and code in the label value bean
				countryList.add(new LabelValueBean(country.getCountryName(),country.getCountryCode()));
			}
			
		}
		log.debug(this.getClass().getName() + " - [getCountries] - "+ "Exit");
		return countryList;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCountries] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCountries] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCountries", CountryManagerImpl.class);
		}
	}
}
