/*
 * @(#)MetaGroupManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MetaGroupDAO;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.model.MetaGroupLevel;
import org.swallow.maintenance.service.MetaGroupManager;
import org.swallow.util.LabelValueBean;
/**
 * 
 * This is DAO class for Metagroup screen.
 * 
 */
@Component ("metaGroupManager")
public class MetaGroupManagerImpl implements MetaGroupManager {
	@Autowired
	private MetaGroupDAO metaGroupDAO;

	/**
	 * Create a log instance for logging
	 */
	private final Log log = LogFactory.getLog(MetaGroupManagerImpl.class);

	/**
	 * To set the MetaGroupDAO instance
	 * 
	 * @param metaGroupDAO
	 */
	public void setMetaGroupDAO(MetaGroupDAO metaGroupDAO) {
		this.metaGroupDAO = metaGroupDAO;
	}

	/**
	 * This is used to fetches meta group details from P_METAGROUP and P_GROUP
	 * table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mgroupId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getMetaGroupDetails(String hostId, String entityId,
			Integer mGroupLevel) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getMetaGroupDetails] - Entering");
			/* Method's local variable declaration */
			Collection metaGroupCol = null;
			/*
			 * Retrieve Meta group details from DB based on the parameters
			 * passed.
			 */
			metaGroupCol = metaGroupDAO.getMetaGroupDetails(hostId, entityId,
					mGroupLevel);
			log.debug(this.getClass().getName()
					+ "- [getMetaGroupDetails] - Exiting");
			return metaGroupCol;
		} catch (Exception exp) {
			log
					.debug(this.getClass().getName()
							+ " - Exception Catched in [getMetaGroupDetails] method : - "
							+ exp.getMessage());
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMetaGroupDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMetaGroupDetails", MetaGroupManagerImpl.class);
		}
	}

	/**
	 * This is used to modify the meta group details in P_METAGROUP table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mgroupId
	 * @return MetaGroup
	 * @throws swtException
	 */
	public MetaGroup change(String hostId, String entityId, String mgroupId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "- [change] - Entering");
			/* Class instance declaration */
			MetaGroup metaGroup = null;
			/* Used to modify the meta group details in P_METAGROUP table */
			metaGroup = metaGroupDAO.change(hostId, entityId, mgroupId);
			log.debug(this.getClass().getName() + "- [change] - Exiting");
			return metaGroup;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp, "change",
					MetaGroupManagerImpl.class);
		}
	}

	/**
	 * This is used fetches meta group level from P_METAGROUP_LEVEL table
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getMgrpLvlCodeList(String hostId, String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [getMgrpLvlCodeList] - Entering");
			/* Method's local variable declaration */
			Collection metaGroupLevelList = null;
			ArrayList metaGroupLevelCodeList;
			Iterator itr;
			/* Class instance declaration */
			MetaGroupLevel metaGroupLevel = null;
			metaGroupLevelCodeList = new ArrayList();
			/*
			 * Retrieve Meta group level code from DB based on the parameters
			 * passed.
			 */
			metaGroupLevelList = metaGroupDAO.getMgrpLvlCode(hostId, entityId);
			itr = (metaGroupDAO.getMgrpLvlCode(hostId, entityId)).iterator();
			/* Fetches meta group level from DB to display in drop down box */
			while (itr.hasNext()) {
				/* Fetches the level code from DB and display in drop down box */
				metaGroupLevel = (MetaGroupLevel) itr.next();
				metaGroupLevelCodeList.add(new LabelValueBean(metaGroupLevel
						.getMgrpLvlName(), metaGroupLevel.getId()
						.getMgrpLvlCode().toString()));
			}
			log.debug(this.getClass().getName()
					+ "- [getMgrpLvlCodeList] - Exiting");
			return metaGroupLevelCodeList;
		} catch (Exception exp) {
			log
					.debug(this.getClass().getName()
							+ " - Exception Catched in [getMgrpLvlCodeList] method : - "
							+ exp.getMessage());
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getMgrpLvlCodeList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMgrpLvlCodeList", MetaGroupManagerImpl.class);
		}
	}

	/**
	 * This method is used to save the modify meta group details in P_METAGROUP
	 * table
	 * 
	 * @param metaGroup
	 * @return
	 * @throws SwtException
	 */
	public void updateMetaGroupDetails(MetaGroup metaGroup) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Entering");
			/* Updated details are stored in P_METAGROUP table */
			metaGroupDAO.updateMetaGroupDetails(metaGroup);
			log.debug(this.getClass().getName()
					+ "- [updateMetaGroupDetails] - Exiting");
		} catch (Exception exp) {
			log
					.debug(this.getClass().getName()
							+ " - Exception Catched in [updateMetaGroupDetails] method : - "
							+ exp.getMessage());
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [updateMetaGroupDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMetaGroupDetails", MetaGroupManagerImpl.class);
		}
	}

	/**
	 * This is used to save the newly added meta group details in P_METAGROUP
	 * table
	 * 
	 * @param metaGroup
	 * @return
	 * @throws SwtException
	 */
	public void saveMetaGroup(MetaGroup metaGroup) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [saveMetaGroup] - Entering");
			/* Used to save the meta group details in P_METAGROUP table */
			metaGroupDAO.saveMetaGroup(metaGroup);
			log.debug(this.getClass().getName()
							+ "- [saveMetaGroup] - Exiting");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveMetaGroup] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMetaGroup", MetaGroupManagerImpl.class);
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		}
	}

	/**
	 * This is used to delete the meta group details from P_METAGROUP table
	 * 
	 * @param metaGroup
	 * @return
	 * @throws SwtException
	 */
	public void deleteMetaGroup(MetaGroup metaGroup) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteMetaGroup] - Entering");
			/* Delete the records from P_METAGROUP table */
			metaGroupDAO.deleteMetaGroup(metaGroup);
			log.debug(this.getClass().getName()
					+ "- [deleteMetaGroup] - Exiting");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [deleteMetaGroup] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteMetaGroup] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMetaGroup", MetaGroupManagerImpl.class);
		}
	}

	/**
	 * This is used to get the group details from P_METAGROUP, P_GROUP tables
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mgroupId
	 * @return Collection
	 * @throws SwtException
	 * 
	 */
	public Collection groupDetails(String hostId, String entityId,
			String mgroupId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
							+ "- [groupDetails] - Entering");
			/* Meta group details are fetches from P_METAGROUP, P_GROUP tables */
			Collection groupColl = metaGroupDAO.groupDetails(hostId, entityId,
					mgroupId);
			log.debug(this.getClass().getName() + "- [groupDetails] - Exiting");
			return groupColl;
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getMgrpLvlCodeList] method : - "
							+ exp.getMessage());
			log.error(this.getClass().getName()
							+ " - Exception Catched in [getMgrpLvlCodeList] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMgrpLvlCodeList", MetaGroupManagerImpl.class);
		}
	}
}
