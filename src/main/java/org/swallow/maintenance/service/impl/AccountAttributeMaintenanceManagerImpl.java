package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AccountAttributeMaintenanceDAO;
import org.swallow.maintenance.model.AccountAttribute;
import org.swallow.maintenance.model.AccountAttributeFuncGroup;
import org.swallow.maintenance.model.AccountAttributeHDR;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.FunctionalGroup;
import org.swallow.maintenance.service.AccountAttributeMaintenanceManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;

@Component ("accountAttributeMaintenanceManager")
public class AccountAttributeMaintenanceManagerImpl implements AccountAttributeMaintenanceManager {

	@Autowired
	private AccountAttributeMaintenanceDAO accountAttributeMaintenanceDAO;
	
	private final Log log = LogFactory.getLog(AccountAttributeMaintenanceManagerImpl.class);
	
	public void setAccountAttributeMaintenanceDAO(
			AccountAttributeMaintenanceDAO accountAttributeMaintenanceDAO) {
		this.accountAttributeMaintenanceDAO = accountAttributeMaintenanceDAO;
	}


	public void saveAcctAttribute(AccountAttribute acctAttr)
			throws SwtException {
		try {
			accountAttributeMaintenanceDAO.saveAccountAttributeValue(acctAttr);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveAcctAttribute] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAcctAttribute",
					AccountAttributeMaintenanceManagerImpl.class);
		}
		
	}

	
	public void saveAccountAttributeHDR(AccountAttributeHDR acctAttrHdr)
			throws SwtException {
		try {
			accountAttributeMaintenanceDAO.saveAcctAttributeHDR(acctAttrHdr);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveAcctAttribute] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveAcctAttribute",
					AccountAttributeMaintenanceManagerImpl.class);
		}

	}


	public AccountAttributeHDR getAccountAttributeHDR(String attributeId) throws SwtException {
		AccountAttributeHDR accAttHDR = null;
		try {
			accAttHDR =	accountAttributeMaintenanceDAO.getAcctAttributeHDR(attributeId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAccountAttributeHDR] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountAttributeHDR",
					AccountAttributeMaintenanceManagerImpl.class);
		}
		return accAttHDR;
	}


	public void updateAccountAttributeHDR(AccountAttributeHDR acctAttrHdr)
			throws SwtException {
		try {
			accountAttributeMaintenanceDAO.updateAcctAttributeHDR(acctAttrHdr);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateAccountAttributeHDR] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateAccountAttributeHDR",
					AccountAttributeMaintenanceManagerImpl.class);
		}
		
	}


	public void deleteAccountAttributeHDR(AccountAttributeHDR acctAttrHdr , boolean isCascade)
			throws SwtException {
		ArrayList<AccountAttributeFuncGroup> acctAttrFunGroupList = null;
		ArrayList<AccountAttribute> acctAttrList = null;
		String attributeId = null;
		try {
			attributeId = acctAttrHdr.getId().getAttributeId();
			acctAttrFunGroupList = accountAttributeMaintenanceDAO.getAcctAttFuncGroupList(attributeId);
			if(acctAttrFunGroupList != null && acctAttrFunGroupList.size()>0){
				
				for (AccountAttributeFuncGroup aafg : acctAttrFunGroupList){
					if(aafg !=null)
						accountAttributeMaintenanceDAO.deleteAccntAttributeFunctionalGrp(aafg);
				}
			}			
			if(isCascade){
				acctAttrList = accountAttributeMaintenanceDAO.getAcctAttributeList(attributeId);
				for (AccountAttribute acctAttr :acctAttrList){
					if(acctAttr != null)
						accountAttributeMaintenanceDAO.deleteAcctAttribute(acctAttr);
				}
			}
				
			accountAttributeMaintenanceDAO.deleteAcctAttribueHDR(acctAttrHdr);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteAccountAttributeHDR] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAccountAttributeHDR",
					AccountAttributeMaintenanceManagerImpl.class);
		}
		
	}

	
	
	public ArrayList<AccountAttributeFuncGroup> getAccountAttributeFuncGroupList(
			String functionalGrp, String restriction) throws SwtException {
		
		return accountAttributeMaintenanceDAO.getAccountAttributeFuncGroupList(functionalGrp, restriction);
	}

	public ArrayList<FunctionalGroup> getFunctionalGrpList()
			throws SwtException {
		
		return accountAttributeMaintenanceDAO.getFunctionalGrpList();
	}

	
	public AccountAttributeFuncGroup getAccntAttributeFunctGroup(
			String attributeId, String functionalGrp) throws SwtException {
		return accountAttributeMaintenanceDAO.getAccntAttributeFunctGroup(attributeId, functionalGrp);
	}

	public void deleteAccntAttributeFunctionalGrp(AccountAttributeFuncGroup accntAttributeFuncGrp)
			throws SwtException {
		accountAttributeMaintenanceDAO.deleteAccntAttributeFunctionalGrp(accntAttributeFuncGrp);
	}

	public void saveAttributeUsageSummary(
			AccountAttributeFuncGroup acctAttrFuncGrp) throws SwtException {
		accountAttributeMaintenanceDAO.saveAttributeUsageSummary(acctAttrFuncGrp);
	}


	public AccountAttribute getAcctAttribute(String sequenceId) throws SwtException {
		AccountAttribute accountAttribute = null;
		try {
			accountAttribute = accountAttributeMaintenanceDAO.getAcctAttribute(sequenceId);
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ "- [getAcctAttribute] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAcctAttribute", AccountAttributeMaintenanceManagerImpl.class);
		}
		return accountAttribute;
		
	}

	public void updateAcctAttribute(AccountAttribute accountAttribute) throws SwtException {
		try {
			accountAttributeMaintenanceDAO.updateAccountAttributeValue(accountAttribute);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateAcctAttribute] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateAcctAttribute", AccountAttributeMaintenanceManagerImpl.class);
		}
	}

	public void deleteAcctAttribute(AccountAttribute accountAttribute) throws SwtException {
		try {
			accountAttributeMaintenanceDAO.deleteAcctAttribute(accountAttribute);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteAcctAttribute] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteAcctAttribute", AccountAttributeMaintenanceManagerImpl.class);
		}
	}


	public Collection<AccountAttributeHDR> getAcctAttributHDRDetailList(String... params)
			throws SwtException {
		
		Collection<AccountAttributeHDR> listAccts = null;
		
		try {
			listAccts = accountAttributeMaintenanceDAO.getAcctAttributHDRDetailList(params);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getAcctAttributHDRDetailList] - Exception "
					+ e.getMessage());
	throw SwtErrorHandler.getInstance().handleException(e,
			"getAcctAttributHDRDetailList", AccountAttributeMaintenanceManagerImpl.class);
		}
		return listAccts;
	}
	
	public Collection getAccountAttributeList(String hostId, String entityId, String currencyCode, String accountId, String attributeId, Date dateFrom, Date dateTo)
			throws SwtException {
		
		Collection<AccountAttributeHDR> listAccts = null;
		
		try {
			listAccts = accountAttributeMaintenanceDAO.getAccountAttributeList(hostId, entityId, currencyCode, accountId, attributeId, dateFrom, dateTo);
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ "- [getAccountAttributeList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountAttributeList", AccountAttributeMaintenanceManagerImpl.class);
		}
		return listAccts;
	}
	
	
	public Collection getAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException {

		ArrayList accountIdList = new ArrayList();

		Collection accounts = accountAttributeMaintenanceDAO.getAccountList(hostId,
				entityId, currencyCode);
		try {
			log.debug(this.getClass().getName() + " - [getAccountList] - Enter");
			Iterator itr = accounts.iterator();
			while (itr.hasNext()) {
				AcctMaintenance acct = (AcctMaintenance) (itr.next());
				String statusFlag = acct.getAcctstatusflg();
				if (statusFlag != null && statusFlag.equalsIgnoreCase(SwtConstants.ACCOUNT_STATUS_FLAG_OPEN)) {
					accountIdList.add(new LabelValueBean(acct.getAcctname(),
															acct.getId().getAccountId()));
				}
			}
			log.debug(this.getClass().getName() + " - [getAccountList] - Enter");
		} catch (Exception e) {
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtexp = swtErrorHandler.handleException(e,
					"getAccountList", AccountAttributeMaintenanceManagerImpl.class);
			throw swtexp;
		}

		return accountIdList;
	}

	
	public ArrayList<AccountAttribute> getLastValuesAccountAttrList(
			String entityId, String accountId, String hostId) throws SwtException {
		ArrayList<AccountAttribute> attributesList = null;
		try {
			attributesList = accountAttributeMaintenanceDAO.getLastValuesAccountAttrList(entityId, accountId, hostId);
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ "- [getLastValuesAccountAttrList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getLastValuesAccountAttrList", AccountAttributeMaintenanceManagerImpl.class);
		}
		return attributesList;
	}

	public void updateAttributeUsageSummary(
			AccountAttributeFuncGroup acctAttrFuncGrp) throws SwtException {
		accountAttributeMaintenanceDAO.updateAttributeUsageSummary(acctAttrFuncGrp);
	}
	
	
	public ArrayList<AccountAttribute> getAccountAttributeList(String attributeId)  throws SwtException{
		try {
			return accountAttributeMaintenanceDAO.getAcctAttributeList(attributeId);
		} catch (Exception e) {
			log
			.error(this.getClass().getName()
					+ "- [getAccountAttributeList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getAccountAttributeList", AccountAttributeMaintenanceManagerImpl.class);
		}
	}

}
