/*
 * @(#)CurrencyInterestManagerImpl.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyInterestDAO;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.maintenance.service.CurrencyInterestManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
/*
 * This is manager class for Currency Interest Rate screen
 * 
 */
@Component ("currencyInterestManager")
public class CurrencyInterestManagerImpl implements CurrencyInterestManager {
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(CurrencyInterestManagerImpl.class);

	/**
	 * Used to hold currencyInterestDAO object
	 */
	@Autowired
	private CurrencyInterestDAO currencyInterestDAO;

	/**
	 * Initializing currencyInterestDAO object
	 * 
	 * @param currencyInterestDAO
	 * @return
	 */
	public void setCurrencyInterestDAO(CurrencyInterestDAO currencyInterestDAO) {
		this.currencyInterestDAO = currencyInterestDAO;
	}

	/**
	 * This method is used to save currencyInterest details through DAO.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [saveCurrencyInterest] - "
				+ "Entry");
		try {
			/*
			 * Pass the currencyInterest bean to the save method of
			 * currencyinterestDAO
			 */
			currencyInterestDAO.saveCurrencyInterest(currencyInterest);
			log.debug(this.getClass().getName()
					+ " - [saveCurrencyInterest] - " + "Exit");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
							+ " - Exception Catched in [saveCurrencyInterest] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyInterest", CurrencyInterestManagerImpl.class);
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
		}

	}

	/**
	 * This method is used to delete currencyInterest object from database
	 * through DAO.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */

	public void deleteCurrencyInterest(CurrencyInterest currencyInterest)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyInterest] - " + "Entry");

			/*
			 * Pass the currencyInterest bean to the delete method of
			 * currencyinterestDAO
			 */
			currencyInterestDAO.deleteCurrencyInterest(currencyInterest);
			log.debug(this.getClass().getName()
					+ " - [deleteCurrencyInterest] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyInterest] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [deleteCurrencyInterest] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "deleteCurrencyInterest",
							CurrencyInterestManagerImpl.class);
		}
	}

	/**
	 * This method is used to update CurrencyInterest object into database
	 * through DAO.
	 * 
	 * @param currencyInterest
	 * @return
	 * @throws SwtException
	 */
	public void updateCurrencyInterestDetail(CurrencyInterest currencyInterest)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyInterestDetail] - " + "Entry");
			/*
			 * Pass the currencyInterest bean to the update method of
			 * currencyinterestDAO
			 */
			currencyInterestDAO.updateCurrencyInterestDetail(currencyInterest);
			log.debug(this.getClass().getName()
					+ " - [updateCurrencyInterestDetail] - " + "Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyInterestDetail] method : - "
							+ exp.getMessage());

			log.error(this.getClass().getName()
							+ " - Exception Catched in [updateCurrencyInterestDetail] method : - "
							+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateCurrencyInterestDetail",
					CurrencyInterestManagerImpl.class);
		}

	}

	/**
	 * This method is used to extract currency name for given code through DAO.
	 * 
	 * @param currencyCode
	 * @return String
	 * @throws SwtException
	 */
	public String getCurrencyName(String currencyCode) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getCurrencyName]- "
					+ "Retuns DAO method getCurrencyName");
			/* Pass the currencyCode to the CurrencyInterestDAO */
			return currencyInterestDAO.getCurrencyName(currencyCode);
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyName] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyName] method : - "
					+ exp.getMessage());

			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyName", CurrencyInterestManagerImpl.class);
		}

	}

	/**
	 * This method is used to extract CurrencyInterest object from database
	 * through DAO.
	 * 
	 * @param request
	 * @param entity
	 * @param hostId
	 * @param countryCode
	 * @param fromDate
	 * @param toDate
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CurrencyInterest> getCurrencyInterestList(
			HttpServletRequest request, String entity, String hostId,
			String currencyCode, String fromDate, String toDate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyInterestList] - " + "Entry");

			/* Method's class Instance and variable Declaration */
			Collection<CurrencyInterest> ciList;
			Collection<CurrencyInterest> finalCIList;
			CurrencyInterest currencyInterest;
			SimpleDateFormat sdf;
			SystemFormats sysformat;
			String updateDateAsString;
			String dateFormat;
			HttpSession session;
			Iterator<CurrencyInterest> it_ciList = null;
			session = request.getSession();

			dateFormat = SwtUtil.getCurrentDateFormat(session);
			/*
			 * Passing the entity id,host id,currency code,fromDate and toDate
			 * to collect the currency Interest collection from
			 * CurrencyInterestDAO
			 */
			ciList = currencyInterestDAO.getCurrencyInterestList(entity,
					hostId, currencyCode, fromDate, toDate, dateFormat);
			/* Initializing the array list */
			finalCIList = new ArrayList<CurrencyInterest>();

			/*
			 * Condition to check the collection list is not null and its size
			 * greater than '0'
			 */
			if ((ciList != null) && (ciList.size() > 0)) {

				/* Iterate and store the collection to the iterator */
				it_ciList = ciList.iterator();

				/* Loop to to collect each value from the iterator */
				while (it_ciList.hasNext()) {
					/* Set the values to the currency interest bean */
					currencyInterest = (CurrencyInterest) it_ciList.next();
					/* Set the currency name to the currencyInterest Bean */
					currencyInterest
							.setCurrencyName(getCurrencyName(currencyInterest
									.getId().getCurrencyCode()));
					/* Set the currency access to the currencyInterest bean */
					currencyInterest.setCurrencyAccess(SwtUtil
							.getFullAccesOnCurrencyAndEntity(request, hostId,
									entity, currencyInterest.getId()
											.getCurrencyCode()) ? "true"
							: "false");
					updateDateAsString = "";
					/*
					 * Retrieves the currenctSystemFormats from SwtUtil and
					 * store in SystemFormats
					 */
					sysformat = SwtUtil.getCurrentSystemFormats(request
							.getSession());
					/*
					 * Start : Vivekanandan:27/05/2008:Added currency interest
					 * rate date is converted as string to display date in date
					 * grid with current date format
					 */
					currencyInterest.setInterestRateDateAsString(SwtUtil
							.formatDate(currencyInterest.getId()
									.getInterestRateDate(), sysformat
									.getDateFormatValue()));
					/*
					 * End: Vivekanandan:27/05/2008:Added currency interest rate
					 * date is converted as string to display date in date grid
					 * with current date format
					 */
					/* Condition to check update date is not null */
					if (currencyInterest.getUpdateDate() != null) {
						/* Set the date format date as hh:mm:ss */
						sdf = new SimpleDateFormat("HH:mm:ss");

						/*
						 * Format the date by SwtUtil formatDate and return as
						 * String value
						 */
						updateDateAsString = SwtUtil.formatDate(
								currencyInterest.getUpdateDate(), sysformat
										.getDateFormatValue());
						/* Collects the date and time from simpleDateFormat */
						updateDateAsString = updateDateAsString + " "
								+ sdf.format(currencyInterest.getUpdateDate());
					}
					/* Set the update date as string in currencyInterest Bean */
					currencyInterest.setUpdateDateAsString(updateDateAsString);
					/* Add the currencyInterest bean to the Array list */
					finalCIList.add(currencyInterest);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getCurrencyInterestList] - " + "Exit");
			return finalCIList;
		} catch (Exception exp) {

			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyInterestList] method : - "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyInterestList",
					CurrencyInterestManagerImpl.class);
		}
	}
}
