/*
 * @(#)SysParamsManagerImpl.java 1.0 12/12/2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.SysParamsDAO;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

/**
 * <AUTHOR>
 * 
 * This class implements methods from SysparamManagerImpl That are used to call
 * methods from SysparamManagerDAO(DAO Layer) for accessing updating general
 * System Parameter related data. This Class that implements the
 * SysParamsManager.
 */
@Component("sysParamsManager")
public class SysParamsManagerImpl implements SysParamsManager {
	
	//@Lazy
	@Autowired
	private SysParamsDAO sysParamsDAO;
	
	private final Log log = LogFactory.getLog(SysParamsManagerImpl.class);

	public void setSysParamsDAO(SysParamsDAO sysParamsDAO) {
		this.sysParamsDAO = sysParamsDAO;
	}

	/**
	 * @param hostId
	 * @return SysParams
	 */
	public SysParams getSysParamsDetail(String hostId) throws SwtException {
		log.debug(this.getClass().getName() + " - [getSysParamsDetail] - "
				+ "Entry");

		SysParams sysParams = null;
		sysParams = sysParamsDAO.getSysParamsDetail(hostId);

		log.debug(this.getClass().getName() + " - [getSysParamsDetail] - "
				+ "Exit");

		return sysParams;
	}


	
	/**
	 * This function is used to call methods from SysparamsDAO(DAO Layer) to get
	 * the General System parameter details
	 * 
	 * @param hostId
	 * @param systemFormat
	 * @throws SwtException
	 * @return SysParams
	 */
	public SysParams getSysParamsDetail(String hostId,
			SystemFormats systemFormat) throws SwtException {
		// Variable to hold sysParams object
		SysParams sysParams = null;
		try {

			log.debug(this.getClass().getName() + " - [getSysParamsDetail] - "
					+ "Entry");
			// Getting SysParamsDetail from dao class
			sysParams = sysParamsDAO.getSysParamsDetail(hostId);
			// Settiting System test date in syssparams
			sysParams.setDateAsString(SwtUtil.formatDate(sysParams
					.getTestDate(), systemFormat.getDateFormatValue()));
			log.debug(this.getClass().getName() + " - [getSysParamsDetail] - "
					+ "Exit");
		} catch (Exception exp) {
			log
					.error("Exception Catch in SysParamsManagerImpl.'getSysParamsDetail' method : "
							+ exp.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSysParamsDetail", SysParamsManagerImpl.class);
		}
		return sysParams;
	}

	/**
	 * This function is used to call methods from SysparamsDAO(DAO Layer) to
	 * update the General System parameter details
	 * 
	 * @param sysParams
	 * @param sysFormat
	 * @return
	 */
	public void updateSysParameter(SysParams sysParams, SystemFormats sysFormat)
			throws SwtException {
		// Variable to hold the testDate
		Date testDate = null;
		// Variable to hold the startDate
		Date startDate = null;
		// Variable to hold the  endDate
		Date endDate = null;

		try {
			log.debug(this.getClass().getName() + " - [updateSysParameter] - "
					+ "Entry");
			if (!SwtUtil.isEmptyOrNull(sysParams.getDateAsString())) {
				testDate = SwtUtil.parseDate(sysParams.getDateAsString(),
						sysFormat.getDateFormatValue());
			}
			// Setting system test date in sysParams
			sysParams.setTestDate(testDate);
			// Calling dao class to update general parameter system details
			sysParamsDAO.updateSysParameter(sysParams);
			log.debug(this.getClass().getName() + " - [updateSysParameter] - "
					+ "Exit");
		} catch (SwtException ex) {
			log
					.error("Exception Catch in SysParamsManagerImpl.'updateSysParameter' method : "
							+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"updateSysParameter", SysParamsManagerImpl.class);
		} catch (Exception exp) {
			log
					.error("Exception Catch in SysParamsManagerImpl.'updateSysParameter' method : "
							+ exp.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateSysParameter", SysParamsManagerImpl.class);
		} finally {
			testDate = null;
		}
	}


	/**
	 * This is used to retrieve system date
	 * 
	 * @param none
	 * @return Date
	 * @throws SwtException
	 */
	public Date getDBSytemDate() throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getDBSytemDate] - "
					+ "Entry");
			return sysParamsDAO.getDBSytemDate();

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDBSytemDate] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDate", SysParamsManagerImpl.class);

		}
	}
	/**
	 * This function is used to get currencyGroup from currencyId and entityId 
	 * 
	 */
	public String getCurrencyGroup(String entityId,String currencyId)  throws SwtException{
		try {
			log.debug(this.getClass().getName() + " - [getCurrencyGroup] - "
					+ "Entry");
			return sysParamsDAO.getCurrencyGroup(entityId,currencyId);

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCurrencyGroup] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyGroup", SysParamsManagerImpl.class);

		}
		
	}

	/**
	 * This is used to retrieve entity date
	 * 
	 * @param systemDate
	 * @param entityId
	 * @return HashMap
	 * @throws SwtException
	 */
	public HashMap<String, Object> getEntityDate(Date systemDate,
			String entityId) throws SwtException {
		// Collection to hold entity date and offset
		HashMap<String, Object> entMap = null;

		try {
			log.debug(this.getClass().getName() + " - [getEntityDate] - "
					+ "Entry");
			// To get entity date and offset value for collection
			entMap = sysParamsDAO.getEntityDate(systemDate, entityId);
			log.debug(this.getClass().getName() + " - [getEntityDate] - "
					+ "Exit");
			return entMap;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEntityDate] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEntityDate", SysParamsManagerImpl.class);
		}
	}

	/* Start: Method added by Balaji for Mantis 1991 */
	/**
	 * This is used to retrieve system date with entity offset
	 * 
	 * @param entityId
	 * @return Date
	 * @throws SwtException
	 */
	public Date getDBSytemDateWithEntityOffset(String entityId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [getDBSytemDateWithEntityOffset] - " + "Entry");
			return sysParamsDAO.getDBSytemDateWithEntityOffset(entityId);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getDBSytemDateWithEntityOffset] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDateWithEntityOffset",
					SysParamsManagerImpl.class);
		}
	}
	/* End: Method added by Balaji for Mantis 1991 */
	
	/**
	 * Added by Mefteh for Mantis 2016
	 * To retrieve N_DAYS_PRIOR_TO_TODAY , N_DAYS_AHEAD_TO_TODAY defined in PKG_PREDICT_JOBS 
	 * @param none
	 * @return List
	 * @throws SwtException
	 */
	public List<Integer> getPriorAndAheadDaysToToday() throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getPriorAndAheadDaysToToday] - "
					+ "Entry");
			return sysParamsDAO.getPriorAndAheadDaysToToday();

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getPriorAndAheadDaysToToday] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPriorAndAheadDaysToToday", SysParamsManagerImpl.class);

		}
	}


	public String getDateFormat() throws SwtException {
		String dateFormat = null;
		try {
	
			
			log.debug(this.getClass().getName() + " - [getDateFormat] - "
					+ "Entry");
			dateFormat = sysParamsDAO.getDateFormat();
			
			log.debug(this.getClass().getName() + " - [getDateFormat] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDateFormat] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDateFormat", SysParamsManagerImpl.class);
		}
		return dateFormat;
	}
	
	
	/**
	 * This is used to applicate a keyword on a date to get for example the first day of this week or the last date of the previous month
	 * @param keyword
	 * @param date
	 * @return
	 * @throws SwtException
	 */
	public Date applicateKeywordOnDate(String keyword, Date date, String runDateWorkdays)throws SwtException{
		try {
			log.debug(this.getClass().getName() + " - [getDBSytemDate] - "
					+ "Entry");
			return sysParamsDAO.applicateKeywordOnDate(keyword, date, runDateWorkdays);

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDBSytemDate] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDBSytemDate", SysParamsManagerImpl.class);

		}
	}
	
	
	public HashMap<String, String> getTimeZoneRegionsList(Date systemDate) throws SwtException {

		try {
			log.debug(this.getClass().getName() + " - [getTimeZoneRegionsList] - " + "Entry");
			return sysParamsDAO.getTimeZoneRegionsList(systemDate);

		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [getTimeZoneRegionsList] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [getTimeZoneRegionsList] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e, "getTimeZoneRegionsList",
					SysParamsManagerImpl.class);
		}

	}
	
}