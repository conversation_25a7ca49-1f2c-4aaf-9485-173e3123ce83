/*
 * @(#)AcctMaintenanceManagerImpl.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AcctMaintenanceDAO;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountAttributeFuncGroup;
import org.swallow.maintenance.model.AccountInterestRate;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.model.SweepRule;
import org.swallow.maintenance.service.AcctMaintenanceDetailVO;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;

/**
 * AcctMaintenanceManagerImpl.java
 * 
 * This class implements methods from AcctMaintenanceManager That are used to
 * call methods from AcctMaintenanceDAO(DAO Layer) for persisting,accessing
 * updating and manipulating Account maintenance related data.
 */
@Component("acctMaintenanceManager")
//@Transactional (propagation = Propagation.REQUIRED)
public class AcctMaintenanceManagerImpl implements AcctMaintenanceManager {
	
	@Autowired
	private AcctMaintenanceDAO acctMaintenanceDAO;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(AcctMaintenanceManagerImpl.class);

	/**
	 * @param acctMaintenanceDAO
	 */
	public void setAcctMaintenanceDAO(AcctMaintenanceDAO acctMaintenanceDAO) {
		this.acctMaintenanceDAO = acctMaintenanceDAO;
	}

	/**
	 * This function is used to call methods from AcctMaintenanceDAO(DAO Layer)
	 * to save AcctmaintenanceDetails
	 * 
	 * @param acct
	 * @param systemInfo
	 * @param sysforma
	 * @param accountInterestRateList
	 * @throws SwtException
	 */
	public void saveAcctMaintDetail(AcctMaintenance acct,
			SystemInfo systemInfo, SystemFormats sysforma,
			ArrayList accountInterestRateList) throws SwtException {
		// Variable Declaration for target
		double target = 0;
		// Variable Declaration for eodtarget
		double eodtarget = 0;
		try {
			log.debug(this.getClass().getName()
					+ " - [saveAcctMaintDetail] - Enter");
			// Set the Eod minimum sweep amount
			if (acct.getMinseepamtasString() != null) {
				if (!(acct.getMinseepamtasString().equals(""))) {
					acct.setMinseepamt(SwtUtil.parseCurrency(acct
							.getMinseepamtasString(), sysforma
							.getCurrencyFormat()));
				}
			}
			// set the Intra day minimum sweep amount
			if (!SwtUtil.isEmptyOrNull(acct.getEodMinseepamtasString())) {

				acct.setEodMinseepamt(SwtUtil.parseCurrency(acct
						.getEodMinseepamtasString(), sysforma
						.getCurrencyFormat()));
			}
			// set the maximum sweep amount
			if (acct.getMaxsweepamteasString() != null) {
				if (!(acct.getMaxsweepamteasString().equals(""))) {
					acct.setMaxsweepamte(SwtUtil.parseCurrency(acct
							.getMaxsweepamteasString(), sysforma
							.getCurrencyFormat()));
				}
			}
			// set the Eod Target balance amount
			if (acct.getTargetbalanceasString() != null) {
				if (!(acct.getTargetbalanceasString().equals(""))) {
					acct.setTargetbalance(SwtUtil.parseCurrency(acct
							.getTargetbalanceasString(), sysforma
							.getCurrencyFormat()));
				} else {
					target = 0;
					acct.setTargetbalance(new Double(target));
				}
			} else {

				target = 0;
				acct.setTargetbalance(new Double(target));
			}
			// Set the Intraday Target balance
			if (!SwtUtil.isEmptyOrNull(acct.getEodTargetbalanceasString())) {
				acct.setEodTargetbalance(SwtUtil.parseCurrency(acct
						.getEodTargetbalanceasString(), sysforma
						.getCurrencyFormat()));
			} else {
				eodtarget = 0;
				acct.setEodTargetbalance(new Double(eodtarget));
			}
			if ((acct.getMonitor() == null) || acct.getMonitor().equals("")) {
				acct.setMonitor(SwtConstants.NO);
			}
			if ((acct.getAcctMonitorSum() == null)
					|| acct.getMonitor().equals("")) {
				acct.setAcctMonitorSum(SwtConstants.NO);
			}
			if ((acct.getAutoOpenUnexpected() == null)
					|| acct.getAutoOpenUnexpected().equals("")) {
				acct.setAutoOpenUnexpected(SwtConstants.NO);
			}
			if ((acct.getAutoOpenUnsettled() == null)
					|| acct.getAutoOpenUnsettled().equals("")) {
				acct.setAutoOpenUnsettled(SwtConstants.NO);
			}
			if ((acct.getAllPreAdviceEntity() == null)
					|| acct.getAllPreAdviceEntity().equals("")) {
				acct.setAllPreAdviceEntity(SwtConstants.NO);
			}
			if ((acct.getAggAccount() == null)
					|| acct.getAggAccount().equals("")) {
				acct.setAggAccount(SwtConstants.NO);
			}
			if ((acct.getArchiveData() == null)
					|| acct.getArchiveData().equals("")) {
				acct.setArchiveData(SwtConstants.NO);
			}
			acctMaintenanceDAO.saveAcctMaintDetail(acct,
					accountInterestRateList);
			log.debug(this.getClass().getName()
					+ " - [saveAcctMaintDetail] - Exit");

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ "- [saveAcctMaintDetail] - Exception "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAcctMaintDetail", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * This function is used to call methods from AcctMaintenanceDAO(DAO Layer)
	 * to get the Editabledatadetailslist
	 * 
	 * @param entityId
	 * @param hostId
	 * @param accountId
	 * @return AcctMaintenance
	 */
	public AcctMaintenance getEditableDataDetailList(String entityId,
			String hostId, String accountId, SystemInfo systemInfo,
			SystemFormats sysforma) throws SwtException {
		// Variable Declaration for acct
		AcctMaintenance acct = null;
		// Variable Declaration for autoOpenUnsettled
		String autoOpenUnsettled = null;
		// Variable Declaration for autoOpenUnexpected
		String autoOpenUnexpected = null;
		// Variable Declaration for allPreAdviceEntity
		String allPreAdviceEntity = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getEditableDataDetailList] - Enter");
			acct = acctMaintenanceDAO.getEditableData(hostId, entityId,
					accountId);
			if (acct.getMinseepamt() != null) {
				acct.setMinseepamtasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getMinseepamt()));

			} else {
				acct.setMinseepamtasString("");
			}
			// Set the Intra day minimum Sweep Amount
			if (acct.getEodMinseepamt() != null) {
				acct.setEodMinseepamtasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getEodMinseepamt()));
			} else {
				acct.setEodMinseepamtasString("");
			}
			// Set the Maximum sweep amount
			if (acct.getMaxsweepamte() != null) {
				acct.setMaxsweepamteasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getMaxsweepamte()));
			} else {
				acct.setMaxsweepamteasString("");
			}
			// Set the Eod Sweeping target Balance
			if ((acct.getTargetbalance() != null)
					|| (acct.getTargetbalance().doubleValue() == 0.00)) {
				acct.setTargetbalanceasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getTargetbalance()));
			} else {
				acct.setTargetbalanceasString("");
			}
			// Set the Intra day target Balance
			if ((acct.getEodTargetbalance() != null)					) {
				acct.setEodTargetbalanceasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getEodTargetbalance()));
			} else {
				acct.setEodTargetbalanceasString("");
			}
			// Set the Currency credit rate
			if (acct.getCurrcreditrate() != null) {
				acct.setCurrcreditrateasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getCurrcreditrate()));
			} else {
				acct.setCurrcreditrateasString("");
			}
			if (acct.getCurroverdraftrate() != null) {
				acct.setCurroverdraftrateasString(SwtUtil.formatCurrency(acct
						.getCurrcode(), acct.getCurroverdraftrate()));
			} else {
				acct.setCurroverdraftrateasString("");
			}
			autoOpenUnsettled = acct.getAutoOpenUnsettled();
			autoOpenUnexpected = acct.getAutoOpenUnexpected();
			allPreAdviceEntity = acct.getAllPreAdviceEntity();
			if (autoOpenUnsettled.equalsIgnoreCase("Y")) {
				acct.setAutoOpenUnsettled("Y");
			} else if (autoOpenUnsettled.equalsIgnoreCase("N")) {
				acct.setAutoOpenUnsettled("N");
			}
			if (autoOpenUnexpected.equalsIgnoreCase("Y")) {
				acct.setAutoOpenUnexpected("Y");
			} else if (autoOpenUnexpected.equalsIgnoreCase("N")) {
				acct.setAutoOpenUnexpected("N");
			}
			if (allPreAdviceEntity.equalsIgnoreCase("Y")) {
				acct.setAllPreAdviceEntity("Y");
			} else if (allPreAdviceEntity.equalsIgnoreCase("N")) {
				acct.setAllPreAdviceEntity("N");
			}
			log.debug(this.getClass().getName()
					+ " - [getEditableDataDetailList] - Exit");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getEditableDataDetailList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEditableDataDetailList",
					AcctMaintenanceManagerImpl.class);
		} finally {
			// null the string created already
			autoOpenUnsettled = null;
			autoOpenUnexpected = null;
			allPreAdviceEntity = null;
		}
		return acct;
	}

	/**
	 * This function is used to call methods from AcctMaintenanceDAO(DAO Layer)
	 * to update the AccountsDetail
	 * 
	 * @param acct
	 * @param collAcctIntRateDeleted
	 * @param collAcctIntRateAdded
	 * @param collAcctIntRateUpdated
	 * @param systemInfo
	 * @param sysforma
	 * @throws SwtException
	 */
	public void updateAccountsDetail(AcctMaintenance acct,
			Collection collAcctIntRateDeleted, Collection collAcctIntRateAdded,
			Collection collAcctIntRateUpdated, SystemInfo systemInfo,
			SystemFormats sysforma) throws SwtException {
		// Variable Declaration for target
		double target = 0;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateAccountsDetail] - Enter");
			if (acct.getMinseepamtasString() != null) {
				if (!(acct.getMinseepamtasString().equals(""))) {
					acct.setMinseepamt(SwtUtil.parseCurrency(acct
							.getMinseepamtasString(), sysforma
							.getCurrencyFormat()));
				}
			}
			// Set the Intraday Minimum Sweep Amount
			if (!SwtUtil.isEmptyOrNull(acct.getEodMinseepamtasString())) {
				acct.setEodMinseepamt(SwtUtil.parseCurrency(acct
						.getEodMinseepamtasString(), sysforma
						.getCurrencyFormat()));
			}
			// Set the Maximum Sweep Amount
			if (acct.getMaxsweepamteasString() != null) {
				if (!(acct.getMaxsweepamteasString().equals(""))) {
					acct.setMaxsweepamte(SwtUtil.parseCurrency(acct
							.getMaxsweepamteasString(), sysforma
							.getCurrencyFormat()));
				}
			}
			// Set the Eod Target Balance
			if (acct.getTargetbalanceasString() != null) {
				if (!(acct.getTargetbalanceasString().equals(""))) {

					acct.setTargetbalance(SwtUtil.parseCurrency(acct
							.getTargetbalanceasString(), sysforma
							.getCurrencyFormat()));
				} else {
					target = 0;
					acct.setTargetbalance(new Double(target));
				}
			} else {
				target = 0;
				acct.setTargetbalance(new Double(target));
			}
			// Set the Intraday Target Balance
			if (!SwtUtil.isEmptyOrNull(acct.getEodTargetbalanceasString())) {
				acct.setEodTargetbalance(SwtUtil.parseCurrency(acct
						.getEodTargetbalanceasString(), sysforma
						.getCurrencyFormat()));
			} else {
				target = 0;
				acct.setEodTargetbalance(new Double(target));
			}
			if ((acct.getMonitor() == null) || acct.getMonitor().equals("")) {
				acct.setMonitor(SwtConstants.NO);
			}
			if ((acct.getAcctMonitorSum() == null)
					|| acct.getMonitor().equals("")) {
				acct.setAcctMonitorSum(SwtConstants.NO);
			}
			if ((acct.getAllPreAdviceEntity() == null)
					|| acct.getAllPreAdviceEntity().equals("")) {
				acct.setAllPreAdviceEntity(SwtConstants.NO);
			}
			if ((acct.getAutoOpenUnexpected() == null)
					|| acct.getAutoOpenUnexpected().equals("")) {
				acct.setAutoOpenUnexpected(SwtConstants.NO);
			}
			if ((acct.getAutoOpenUnsettled() == null)
					|| acct.getAutoOpenUnsettled().equals("")) {
				acct.setAutoOpenUnsettled(SwtConstants.NO);
			}
			if ((acct.getAggAccount() == null)
					|| acct.getAggAccount().equals("")) {
				acct.setAggAccount(SwtConstants.NO);
			}
			if ((acct.getArchiveData() == null)
					|| acct.getArchiveData().equals("")) {
				acct.setArchiveData(SwtConstants.NO);
			}
			acctMaintenanceDAO.updateAcctDetail(acct, collAcctIntRateDeleted,
					collAcctIntRateAdded, collAcctIntRateUpdated, sysforma);
			log.debug(this.getClass().getName()
					+ " - [updateAccountsDetail] - Exit");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'updateAccountsDetail' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateAccountsDetail", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection getBookListColl
	 */
	public Collection getBookListColl(String hostId, String entityId,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		log.debug("Entering 'getAcctTypeColl' method");
		ArrayList accttypeList = new ArrayList();
		try {
			Iterator itr = (acctMaintenanceDAO
					.getBookListColl(hostId, entityId)).iterator();
			BookCode aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (BookCode) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getBookName(),
						aactmaint.getId().getBookCode()));
			}
			log.debug("Exiting 'getAcctTypeColl' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getBookListColl' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBookListColl", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection getFormatListColl
	 */
	public Collection getFormatListColl(String hostId, String entityId,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		log.debug("Entering 'getAcctTypeColl' method");
		ArrayList accttypeList = new ArrayList();
		try {
			Iterator itr = (acctMaintenanceDAO.getFormatListColl(hostId,
					entityId)).iterator();
			MessageFormats aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (MessageFormats) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getId()
						.getFormatId(), aactmaint.getId().getFormatId()));
			}
			log.debug("Exiting 'getAcctTypeColl' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getFormatListColl' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getFormatListColl", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection getMainAcctListColl
	 */
	public Collection getMainAcctListColl(String hostId, String entityId,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		log.debug("Entering 'getAcctTypeColl' method");
		ArrayList accttypeList = new ArrayList();
		try {
			Iterator itr = (acctMaintenanceDAO.getMainAcctListColl(hostId,
					entityId)).iterator();
			AcctMaintenance aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getAcctname(),
						aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting 'getAcctTypeColl' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getMainAcctListColl' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMainAcctListColl", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	/**
	 * 
	 * This method calls the DAO layer deleteAcctDetail method to delete the
	 * account records from database.
	 * 
	 * @param acctMaintenance
	 * @param collAcctIntRateDeleted
	 * @param systemFormat
	 * @throws SwtException
	 */
	/*
	 * Method parameter modified For Mantis 1592 by Sudhakar on
	 * 22-12-2011:Account Maintenance screen allows to create account for entity
	 * that has no currency access
	 */
	public void deleteAcctDetail(AcctMaintenance acctMaintenance,
			Collection collAcctIntRateDeleted, SystemFormats systemFormat)
			throws SwtException {
		try {
			acctMaintenanceDAO.deleteAcctDetail(acctMaintenance,
					collAcctIntRateDeleted, systemFormat);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'deleteAcctDetail' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctDetail", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @return Collection getSubColumnDataDetailList
	 */
	public Collection getSubColumnDataDetailList(String hostId,
			String entityId, String accountId, SystemFormats sysforma)
			throws SwtException {
		Collection collacct = null;
		ArrayList acctmaintlist = new ArrayList();
		Collection<MiscParams> accountClassParams = null;
		// Variable to hold the itrAccountClassParams object
		Iterator<MiscParams> itrAccountClassParams = null;
		// Variable to hold the accountClass object
		MiscParams accountClass = null;
		try {
			collacct = acctMaintenanceDAO.getSubColumnDataDetailList(entityId,
					hostId, accountId);
			Iterator itr = collacct.iterator();
			AcctMaintenance acctmaint = new AcctMaintenance();
			while (itr.hasNext()) {
				acctmaint = (AcctMaintenance) (itr.next());
				String acctLevel = acctmaint.getAcctlevel();
				String accttype = acctmaint.getAccttype();
				// If accttype or acctLevel is null then it causes
				// nullpointerexception -- hence checking it before setting
				if (accttype != null) {
					if (accttype != null && accttype.equalsIgnoreCase("C")) {
						acctmaint.setAccttype("CASH");
					} else {
						acctmaint.setAccttype("CUSTODIAN");
					}
				}
				if (acctLevel != null) {
					if (acctLevel.equalsIgnoreCase("M")) {
						acctmaint.setAcctlevel("MAIN");
					} else {
						acctmaint.setAcctlevel("SUB");
					}
				}
				
				accountClassParams = CacheManager.getInstance().getMiscParams(
						SwtConstants.ACCOUNT_CLASS, entityId);
				itrAccountClassParams = accountClassParams.iterator();
				// Iterate the AccountClassParams
				while (itrAccountClassParams.hasNext()) {
					accountClass = itrAccountClassParams.next();
					// Check account class params and set account class in
					// accountMaintenance
					if ((acctmaint.getAcctClass() != null)
							|| (!"".equals(acctmaint.getAcctClass()))) {
						if (acctmaint.getAcctClass().equals(
								accountClass.getId().getKey2())) {
							acctmaint.setAcctClass(accountClass
									.getParValue());
							break;
						}
					}
				}
				
				
				acctmaintlist.add(acctmaint);
			}

		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getSubColumnDataDetailList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSubColumnDataDetailList",
					AcctMaintenanceManagerImpl.class);
		}
		return acctmaintlist;
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @param accttype
	 * @return AcctMaintenanceDetailVO
	 */
	public AcctMaintenanceDetailVO getAccountTypeDetailList(String entityId,
			String hostId, String currencyId, String accttype,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		AcctMaintenanceDetailVO acctMaintVO = new AcctMaintenanceDetailVO();
		ArrayList acctmaintlist = new ArrayList();
		ArrayList acctmaintlistDetails = new ArrayList();
		try {
			Collection collacct = null;
			if (!currencyId.equals("All") && !accttype.equals("All")) {
				collacct = acctMaintenanceDAO.getAccountTypeDetailList(
						entityId, hostId, currencyId, accttype);
			} else if (!accttype.equals("All") && (currencyId.equals("All"))) {
				collacct = acctMaintenanceDAO.getAccountTypeList(entityId,
						hostId, accttype);
			} else if (!currencyId.equals("All") && accttype.equals("All")) {
				collacct = acctMaintenanceDAO.getAccountTypeCurrencyList(
						entityId, hostId, currencyId);
			} else {
				collacct = acctMaintenanceDAO.getAccountIDDropDown(entityId,
						hostId);
			}
			Iterator itr = collacct.iterator();
			acctmaintlist.add(new LabelValueBean("", ""));
			AcctMaintenance acctmaint = new AcctMaintenance();
			while (itr.hasNext()) {
				acctmaint = (AcctMaintenance) (itr.next());
				acctmaintlist.add(new LabelValueBean(acctmaint.getAccttype(),
						acctmaint.getAccttype()));
				if ((currencyId == null) || currencyId.equals("All")
						|| (acctmaint.getCurrcode()).equals(currencyId)
						|| (acctmaint.getAccttype()).equals(accttype)) {
					acctmaintlistDetails.add(acctmaint);
				}
			}
			acctMaintVO.setAcctmaintenancelist(acctmaintlist);
			acctMaintVO.setAcctmaintenancelistDetails(acctmaintlistDetails);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAccountTypeDetailList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountTypeDetailList",
					AcctMaintenanceManagerImpl.class);
		}
		return acctMaintVO;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.service.AcctMaintenanceManager#getAccountIDDropDown(java.lang.String,
	 *      java.lang.String)
	 */
	public Collection getAccountIDDropDown(String hostId, String entityId,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		ArrayList accttypeList = new ArrayList();
		try {
			log.debug("Entering 'getAccountIDDropDown' method");
			Iterator itr = (acctMaintenanceDAO.getAccountIDDropDown(hostId,
					entityId)).iterator();
			AcctMaintenance aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getId()
						.getAccountId(), aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting 'getAccountIDDropDown' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAccountIDDropDown' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAccountIDDropDown", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.service.AcctMaintenanceManager#copyAccountIdDetails(java.lang.String)
	 */
	public AcctMaintenance copyAccountIdDetails(String hostId, String entityId,
			String accountId, SystemInfo systemInfo, SystemFormats sysforma)
			throws SwtException {
		log.debug("Entering 'getAcctTypeColl' method");
		try {
			return acctMaintenanceDAO.copyAccountIdDetails(hostId, entityId,
					accountId);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'copyAccountIdDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"copyAccountIdDetails", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * This method calls the DAO layer to get the Stored Procedure Details for
	 * Account Maintenance screen and used to set the Account records based on
	 * currency code
	 * 
	 * @param entityId
	 * @param hostId
	 * @param roleId
	 * @param currencyId
	 * @param systemInfo
	 * @param sysforma
	 * @return acctMaintVO
	 * @throws SwtException
	 */
	/*
	 * Method parameter modified For Mantis 1592 by Sudhakar on
	 * 22-12-2011:Account Maintenance screen allows to create account for entity
	 * that has no currency access
	 */
	public AcctMaintenanceDetailVO getCurrencyDetailList(String entityId,
			String hostId, String roleId, String currencyId, String quickFilter, String acctType)
			throws SwtException {
		// Variable to hold the acctmaintenanceDetailsVo object
		AcctMaintenanceDetailVO acctMaintenanceDetailsVo = null;
		// Variable to hold the acctmaintlist object
		ArrayList<LabelValueBean> accountMaintenanceList = null;
		// Variable to hold the acctMaintListDetails object
		ArrayList<AcctMaintenance> accountMaintListDetails = null;
		// Variable to hold the accountList object
		Collection<AcctMaintenance> accountList = null;
		// Variable to hold the itraccountList object
		Iterator<AcctMaintenance> itrAccountList = null;
		// Variable to hold the acctMaintenance object
		AcctMaintenance accountMaintenance = null;
		// Variable to hold the accountClassParams object
		Collection<MiscParams> accountClassParams = null;
		// Variable to hold the itrAccountClassParams object
		Iterator<MiscParams> itrAccountClassParams = null;
		// Variable to hold the accountClass object
		MiscParams accountClass = null;
		// String variable to hold the accountLevel
		String accountLevel = null;
		// String variable to hold the accountType
		String accountType = null;

		try {
			log.debug(this.getClass().getName()
					+ "- [getCurrencyDetailList] - Entering ");
			// Instantiate the acctmaintenanceDetailsVo,acctMaintList,
			// acctMaintListDetails object
			acctMaintenanceDetailsVo = new AcctMaintenanceDetailVO();
			accountMaintenanceList = new ArrayList<LabelValueBean>();
			accountMaintListDetails = new ArrayList<AcctMaintenance>();
			accountMaintenance = new AcctMaintenance();
			// Get the accountList details by acctMaintenanceDAO layer
			accountList = acctMaintenanceDAO.getAcctMaintenanceDetailList(
					entityId, hostId, roleId, currencyId, quickFilter, acctType);
			itrAccountList = accountList.iterator();
			accountMaintenanceList.add(new LabelValueBean("", "All"));
			// Iterate the accountList and set accountType,accountLevel
			while (itrAccountList.hasNext()) {
				accountMaintenance = itrAccountList.next();
				accountClassParams = CacheManager.getInstance().getMiscParams(
						SwtConstants.ACCOUNT_CLASS, entityId);
				itrAccountClassParams = accountClassParams.iterator();
				// Iterate the AccountClassParams
				while (itrAccountClassParams.hasNext()) {
					accountClass = itrAccountClassParams.next();
					// Check account class params and set account class in
					// accountMaintenance
					if ((accountMaintenance.getAcctClass() != null)
							|| (!"".equals(accountMaintenance.getAcctClass()))) {
						if (accountMaintenance.getAcctClass().equals(
								accountClass.getId().getKey2())) {
							accountMaintenance.setAcctClass(accountClass
									.getParValue());
							break;
						}
					}
				}
				// Assign accountLevel,Type
				accountLevel = accountMaintenance.getAcctlevel();
				accountType = accountMaintenance.getAccttype();
				// set accountType
				if (accountType != null) {
					if (accountType.equalsIgnoreCase("C")) {
						accountMaintenance.setAccttype("CASH");
					} else {
						accountMaintenance.setAccttype("CUSTODIAN");
					}
				}
				// set accountLevel
				if (accountLevel != null) {
					if (accountLevel.equalsIgnoreCase("M")) {
						accountMaintenance.setAcctlevel("MAIN");
					} else {
						accountMaintenance.setAcctlevel("SUB");
					}
				}
				// Set currency code in LabelValueBean
				accountMaintenanceList.add(new LabelValueBean(
						accountMaintenance.getCurrcode(), accountMaintenance
								.getCurrcode()));

				// set account records based on currency selection
				if (accountMaintenance.getCurrcode() != null) {
					if ((currencyId == null)
							|| currencyId.equals("All")
							|| (accountMaintenance.getCurrcode())
									.equals(currencyId)
							|| currencyId.equals("")) {
						accountMaintListDetails.add(accountMaintenance);
					}
				} else {
					if (currencyId.equals("All")) {
						accountMaintListDetails.add(accountMaintenance);
					}
				}
			}
			// Set account currency list in acctMaintenanceDetailsVo
			acctMaintenanceDetailsVo
					.setAcctmaintenancelist(accountMaintenanceList);
			// Set account records in acctMaintenanceDetailsVo
			acctMaintenanceDetailsVo
					.setAcctmaintenancelistDetails(accountMaintListDetails);
			log.debug(this.getClass().getName()
					+ "- [getCurrencyDetailList] - Exit");
		} catch (SwtException ex) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getCurrencyDetailList' method : "
							+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getCurrencyDetailList", AcctMaintenanceManagerImpl.class);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getCurrencyDetailList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetailList", AcctMaintenanceManagerImpl.class);
		} finally {
			// nullify the object
			accountMaintenanceList = null;
			accountMaintListDetails = null;
			accountList = null;
			itrAccountList = null;
			accountMaintenance = null;
			accountClassParams = null;
			itrAccountClassParams = null;
			accountClass = null;
			accountLevel = null;
			accountType = null;
		}
		return acctMaintenanceDetailsVo;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.service.AcctMaintenanceManager#getCurrencyMasterList(java.lang.String,
	 *      java.lang.String, org.swallow.util.SystemInfo)
	 */
	public Collection getCurrencyMasterList(String entityId, String hostId,
			SystemInfo systemInfo, SystemFormats sysforma) throws SwtException {
		ArrayList accttypeList = new ArrayList();
		try {
			AcctMaintenance aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			Iterator itr = (acctMaintenanceDAO.getCurrencyMasterList(entityId,
					hostId)).iterator();
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getCurrcode(),
						aactmaint.getCurrcode()));
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getCurrencyMasterList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyMasterList", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.maintenance.service.AcctMaintenanceManager#getMainAcctDetails(java.lang.String,
	 *      java.lang.String, java.lang.String, org.swallow.util.SystemInfo,
	 *      org.swallow.util.SystemFormats)
	 */
	public AcctMaintenance getMainAcctDetails(String entityId, String hostId,
			String mainAcctId, SystemInfo systemInfo, SystemFormats sysforma)
			throws SwtException {
		log.debug("Entering 'getMainAcctDetails' method");
		try {
			return acctMaintenanceDAO.getMainAcctDetails(entityId, hostId,
					mainAcctId);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getMainAcctDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMainAcctDetails", AcctMaintenanceManagerImpl.class);
		}
	}

	public Collection getCountryList() throws SwtException {
		log.debug("Entering 'getCountryList' method");
		Collection coll = acctMaintenanceDAO.getCountryList();
		log.debug("Exiting 'getCountryList' method");
		return coll;
	}

	/*
	 * Start:Code added For Mantis 1592 by Sudhakar on 22-12-2011:Account
	 * Maintenance screen allows to create account for entity that has no
	 * currency access
	 */
	/**
	 * 
	 * This method is used to display the account list based on the Currency id
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return accountTypeList
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAccountIDDropDownForCopy(
			String hostId, String entityId, String currencyCode)
			throws SwtException {
		// Variable to hold the accountTypeList object
		ArrayList<LabelValueBean> accountTypeList = null;
		// Variable to hold the itrAcctMaintenance object
		Iterator<AcctMaintenance> itrAccountMaintenance = null;
		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getAccountIDDropDownForCopy] - Entering ");
			accountTypeList = new ArrayList<LabelValueBean>();
			// Get the collection of account id
			itrAccountMaintenance = (acctMaintenanceDAO.getAccountIDDropDown(
					entityId, hostId)).iterator();

			accountTypeList.add(new LabelValueBean("", ""));
			// iterate the account list
			while (itrAccountMaintenance.hasNext()) {
				acctMaintenance = itrAccountMaintenance.next();
				// Check the currency code and Set the selected currency account
				// list in accountTypeList
				if (acctMaintenance.getCurrcode() != null) {
					if ((currencyCode == null)
							|| currencyCode.equals("All")
							|| (acctMaintenance.getCurrcode())
									.equals(currencyCode)
							|| currencyCode.equals("")) {
						accountTypeList.add(new LabelValueBean(acctMaintenance
								.getAcctname(), acctMaintenance.getId()
								.getAccountId()));
					}
				} else {
					if (currencyCode.equals("All")) {
						accountTypeList.add(new LabelValueBean(acctMaintenance
								.getAcctname(), acctMaintenance.getId()
								.getAccountId()));
					}
				}
			}
			log.debug(this.getClass().getName()
					+ "- [getAccountIDDropDownForCopy] - Exit ");
		} catch (SwtException ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountIDDropDownForCopy] - SwtException - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountIDDropDownForCopy",
					AcctMaintenanceManagerImpl.class);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getAccountIDDropDownForCopy] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getAccountIDDropDownForCopy",
					AcctMaintenanceManagerImpl.class);
		} finally {
			itrAccountMaintenance = null;
			acctMaintenance = null;
		}
		return accountTypeList;
	}

	/*
	 * End:Code added For Mantis 1592 by Sudhakar on 22-12-2011:Account
	 * Maintenance screen allows to create account for entity that has no
	 * currency access
	 */
	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		ArrayList acctlinkList = new ArrayList();
		try {
			log.debug("Exiting getLinkAccountList method");
			ArrayList linkAccountList = (ArrayList) acctMaintenanceDAO
					.getLinkAccountList(hostId, entityId, currencyCode);
			Iterator itr = linkAccountList.iterator();
			AcctMaintenance aactmaint = null;
			acctlinkList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				acctlinkList.add(new LabelValueBean(aactmaint.getAcctname(),
						aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting getLinkAccountList method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getLinkAccountList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLinkAccountList", AcctMaintenanceManagerImpl.class);
		}
		return acctlinkList;
	}

	/**
	 * @desc This method makes a call to DAO method which returns the Collection
	 *       of Accounts eligible for Linking with this account.
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountListInChange(String hostId,
			String entityId, String currencyCode, String accountId)
			throws SwtException {
		ArrayList acctlinkList = new ArrayList();
		try {
			log.debug("Exiting getLinkAccountListInChange method");
			ArrayList linkAccountList = (ArrayList) acctMaintenanceDAO
					.getLinkAccountListInChange(hostId, entityId, currencyCode,
							accountId);
			Iterator itr = linkAccountList.iterator();
			AcctMaintenance aactmaint = null;
			acctlinkList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				acctlinkList.add(new LabelValueBean(aactmaint.getAcctname(),
						aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting getLinkAccountListInChange method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getLinkAccountListInChange' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLinkAccountListInChange",
					AcctMaintenanceManagerImpl.class);
		}
		return acctlinkList;
	}

	/**
	 * @desc - This method makes a call to DAO method which returns all the
	 *       Accounts Linked with this account
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param accountId -
	 *            AccountID
	 * @return Collection - Collection of Accounts Objects
	 * @throws SwtException -
	 *             SwtException
	 * 
	 */
	public Collection getLinkedAccounts(String hostId, String entityId,
			String accountId) throws SwtException {
		
		Collection collacct = null;
		ArrayList acctmaintlist = new ArrayList();
		Collection<MiscParams> accountClassParams = null;
		// Variable to hold the itrAccountClassParams object
		Iterator<MiscParams> itrAccountClassParams = null;
		// Variable to hold the accountClass object
		MiscParams accountClass = null;
		
		try {
		log.debug("Entering getLinkedAccounts method");
		collacct =  acctMaintenanceDAO
				.getLinkedAccounts(hostId, entityId, accountId);
		
		
		Iterator itr = collacct.iterator();
		AcctMaintenance acctmaint = new AcctMaintenance();
		while (itr.hasNext()) {
			acctmaint = (AcctMaintenance) (itr.next());
			String acctLevel = acctmaint.getAcctlevel();
			String accttype = acctmaint.getAccttype();
			// If accttype or acctLevel is null then it causes
			// nullpointerexception -- hence checking it before setting
			if (accttype != null) {
				if (accttype != null && accttype.equalsIgnoreCase("C")) {
					acctmaint.setAccttype("CASH");
				} else {
					acctmaint.setAccttype("CUSTODIAN");
				}
			}
			if (acctLevel != null) {
				if (acctLevel.equalsIgnoreCase("M")) {
					acctmaint.setAcctlevel("MAIN");
				} else {
					acctmaint.setAcctlevel("SUB");
				}
			}
			
			accountClassParams = CacheManager.getInstance().getMiscParams(
					SwtConstants.ACCOUNT_CLASS, entityId);
			itrAccountClassParams = accountClassParams.iterator();
			// Iterate the AccountClassParams
			while (itrAccountClassParams.hasNext()) {
				accountClass = itrAccountClassParams.next();
				// Check account class params and set account class in
				// accountMaintenance
				if ((acctmaint.getAcctClass() != null)
						|| (!"".equals(acctmaint.getAcctClass()))) {
					if (acctmaint.getAcctClass().equals(
							accountClass.getId().getKey2())) {
						acctmaint.setAcctClass(accountClass
								.getParValue());
						break;
					}
				}
			}
			
			
			acctmaintlist.add(acctmaint);
		}
		
	} catch (Exception exp) {
			exp.printStackTrace();
		log
				.error("Exception Catch in AcctMaintenanceManagerImpl.'getSubColumnDataDetailList' method : "
						+ exp.getMessage());
		throw SwtErrorHandler.getInstance().handleException(exp,
				"getSubColumnDataDetailList",
				AcctMaintenanceManagerImpl.class);
	}
	return acctmaintlist;
		
	}

	/**
	 * @desc This function is used to get the account interest rate list which
	 *       comes from P_ACCOUNT_INTEREST_RATE
	 * @param hostId -
	 *            HostID
	 * @param entityId-
	 *            EntityID
	 * @param accountId-AccountID
	 * @param sysformat
	 *            -System format
	 * @return -account Interest rate collection
	 * @throws SwtException
	 */
	public Collection<AccountInterestRate> getAcctInterestRatecoll(
			String hostId, String entityId, String accountId,
			SystemFormats sysFormat) throws SwtException {
		// To get the account interest rate collection
		Collection<AccountInterestRate> acctIntRateColl = null;
		// To iterate the account interest rate collection
		Iterator<AccountInterestRate> acctInterestRateIterator = null;
		// Variable to handle Account interest rate
		AccountInterestRate accountInterestRate = null;
		// To format the date
		SimpleDateFormat simpleDateFormat = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAcctInterestRatecoll] - " + "Entering");
			// get the account interest rate list
			acctIntRateColl = acctMaintenanceDAO.getAccInterestRateList(hostId,
					entityId, accountId);
			// Assign the iterator
			acctInterestRateIterator = acctIntRateColl.iterator();
			simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
			// Iterates the collection
			while (acctInterestRateIterator.hasNext()) {
				accountInterestRate = acctInterestRateIterator.next();
				/*
				 * Start : Nithiyananthan 20/12/2011 Mantis 1559: Account
				 * Interest Rate : Displays "infinite" for infinite numbers
				 */
				// Sets the date as string
				accountInterestRate.setInterestDateRateAsString(SwtUtil
						.formatDate(accountInterestRate.getId()
								.getInterestDateRate(), sysFormat
								.getDateFormatValue()));
				/*
				 * End : Nithiyananthan 20/12/2011 Mantis 1559: Account Interest
				 * Rate : Displays "infinite" for infinite numbers
				 */
				// Checks null for updated date and then sets the updated date
				if (accountInterestRate.getUpdateDate() != null) {
					accountInterestRate.setUpdateDateAsString(SwtUtil
							.formatDate(accountInterestRate.getUpdateDate(),
									sysFormat.getDateFormatValue())
							+ " "
							+ simpleDateFormat.format(accountInterestRate
									.getUpdateDate()));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [getAcctInterestRatecoll] - " + "Exiting");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getAcctInterestRatecoll] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
			acctInterestRateIterator = null;
			accountInterestRate = null;
			simpleDateFormat = null;
		}

		return acctIntRateColl;
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param systemInfo
	 * @param sysforma
	 * @return
	 * @throws SwtException
	 */
	public Collection getMainAcctListByCurr(String hostId, String entityId,
			String currencyCode, SystemInfo systemInfo, SystemFormats sysforma)
			throws SwtException {
		log.debug("Entering getMainAcctListByCurr method");
		ArrayList accttypeList = new ArrayList();
		try {
			Iterator itr = (acctMaintenanceDAO.getMainAcctListByCurr(hostId,
					entityId, currencyCode)).iterator();
			AcctMaintenance aactmaint = null;
			accttypeList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				accttypeList.add(new LabelValueBean(aactmaint.getAcctname(),
						aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting getMainAcctListByCurr method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getMainAcctListByCurr' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMainAcctListByCurr", AcctMaintenanceManagerImpl.class);
		}
		return accttypeList;
	}

	public Collection getIntermediaryRecord(String hostId, String entityId,
			String currencyCode, String acctBicCode) throws SwtException {
		log.debug("Entering getIntermediaryRecord method");
		return acctMaintenanceDAO.getIntermediaryRecord(hostId, entityId,
				currencyCode, acctBicCode);
	}

	/**
	 * This function is used to get the details of country for given countryId
	 * 
	 * @param countryId -
	 *            String
	 * @return -Country
	 * @throws SwtException
	 */
	public Country getCountryDetail(String countryId) throws SwtException {
		log.debug("Entering 'getCountryDetail' method");
		try {
			// getting the country details and return the same.
			return acctMaintenanceDAO.getCountryDetail(countryId);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getCountryDetail' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCountryDetail", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * This function is used to get the details of currency for given currency
	 * code
	 * 
	 * @param entityId -
	 *            String
	 * @param hostId -
	 *            String
	 * @param currencyCode -
	 *            String
	 * @return -Currency
	 * @throws SwtException
	 */
	public Currency getCurrencyDetail(String entityId, String hostId,
			String currencyCode) throws SwtException {
		log.debug("Entering 'getCurrencyDetail' method");
		try {
			// getting the currency details and return the same
			return acctMaintenanceDAO.getCurrencyDetail(entityId, hostId,
					currencyCode);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getCurrencyDetail' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyDetail", AcctMaintenanceManagerImpl.class);
		}
	}

	/**
	 * This function is used to get the details of main or link account
	 * 
	 * @param entityId -
	 *            String
	 * @param hostId -
	 *            String
	 * @param accountId -
	 *            String
	 * @return -AcctMaintenance
	 * @throws SwtException
	 */
	public AcctMaintenance getMainOrLinkAccount(String entityId, String hostId,
			String accountId) throws SwtException {
		log.debug("Entering 'getCurrencyDetail' method");
		try {
			// getting the main or link account details and return the same
			return acctMaintenanceDAO.getEditableData(hostId, entityId,
					accountId);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getMainOrLinkAccount' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMainOrLinkAccount", AcctMaintenanceManagerImpl.class);
		}
	}

	public String checkIfPartyIdExists(String hostId, String entityId,
			String partyId) throws SwtException {
			return acctMaintenanceDAO.checkIfPartyIdExists(hostId, entityId,
					partyId);
	}

	public String checkAccountIlmDataMember(String hostId, String entityId, String currency, String account)
			throws SwtException {
		log.debug("Entering 'checkAccountIlmDataMember' method");
		try {
			// detect if the account is a member of an ILM group
			return acctMaintenanceDAO.checkAccountIlmDataMember(hostId, entityId,
					currency, account);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'checkAccountIlmDataMember' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkAccountIlmDataMember", AcctMaintenanceManagerImpl.class);
		}
	}
	
	/**
	 * @param hostId
	 * @param entityId
	 * @param currency
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfAccountExistForEntity(String hostId, String entityId, String currency, String account) 
			throws SwtException{
		log.debug("Entering 'checkIfAccountExistForEntity' method");
		try {
			
			return acctMaintenanceDAO.checkIfAccountExistForEntity(hostId, entityId,
					currency,account);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'checkIfAccountExistForEntity' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkIfAccountExistForEntity", AcctMaintenanceManagerImpl.class);
		}	
		
	}
	
	
	/**
	 * @param hostId
	 * @param accountId
	 * @return String
	 * @throws SwtException 
	 */
	public boolean checkIfAccountExists(String hostId,String account) 
			throws SwtException{
		log.debug("Entering 'checkIfAccountExists' method");
		try {
			
			return acctMaintenanceDAO.checkIfAccountExists(hostId,account);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'checkIfAccountExists' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkIfAccountExistForEntity", AcctMaintenanceManagerImpl.class);
		}	
		
	}
	
	public ArrayList<AccSweepSchedule> getAcctSweepScheduleListBetweenAccounts(String hostId, String entityId, String currencyCode,  String accountId, String otherEntityId,
			 String otherAccountId) throws SwtException{
		
		// To get the account interest rate collection
		Collection<AccSweepSchedule> accSweepScheduleColl = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAcctSweepScheduleListBetweenAccounts] - " + "Entering");
			// get the account interest rate list
			accSweepScheduleColl = acctMaintenanceDAO.getAcctSweepScheduleListBetweenAccounts(hostId,
					entityId, currencyCode, accountId, otherEntityId, otherAccountId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getAcctSweepScheduleListBetweenAccounts] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}

		return (ArrayList<AccSweepSchedule>) accSweepScheduleColl;
		
	}
	
	public ArrayList<AccSweepSchedule> getAcctSweepScheduleList(String hostId, String entityId, String accountId,
			HttpSession request, String currencyCode,boolean fromAcctMaintenace) throws SwtException{
		// To get the account interest rate collection
				Collection<AccSweepSchedule> accSweepScheduleColl = null;
				// To iterate the account interest rate collection
				Iterator<AccSweepSchedule> accSweepScheduleIterator = null;
				// Variable to handle Account interest rate
				AccSweepSchedule accSweepSchedule = null;
				// To format the date
				SimpleDateFormat simpleDateFormat = null;
				try {
					log.debug(this.getClass().getName()
							+ " - [getAcctSweepScheduleList] - " + "Entering");
					// get the account interest rate list
					accSweepScheduleColl = acctMaintenanceDAO.getAcctSweepScheduleList(hostId,
							entityId, accountId, fromAcctMaintenace);
					// Assign the iterator
					accSweepScheduleIterator = accSweepScheduleColl.iterator();
					simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
					// Iterates the collection
					int i = 0;
					while (accSweepScheduleIterator.hasNext()) {
						accSweepSchedule = accSweepScheduleIterator.next();
						// Sets the date as string
						if(accSweepSchedule.getTargetBalance() != null) {
							accSweepSchedule.setTargetBalanceAsString(SwtUtil.formatCurrency(currencyCode, accSweepSchedule.getTargetBalance()));
						}
						if(accSweepSchedule.getMinAmount() != null) {
							accSweepSchedule.setMinAmountAsString(SwtUtil.formatCurrency(currencyCode, accSweepSchedule.getMinAmount()));
						}
						
						if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getSweepFromBalanceType())) {
							if("P".contentEquals(accSweepSchedule.getSweepFromBalanceType())){
								accSweepSchedule.setSweepFromBalanceTypeAsString(SwtUtil.getMessageFromSession("acc.predicted", request));
							}else {
								accSweepSchedule.setSweepFromBalanceTypeAsString(SwtUtil.getMessageFromSession("label.entity.externalIndicator", request));
							}
						}
						
						//Mantis 6298
						if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getOtherSweepFromBalType())) {
							if("P".contentEquals(accSweepSchedule.getOtherSweepFromBalType())){
								accSweepSchedule.setOtherSweepFromBalTypeAsString(SwtUtil.getMessageFromSession("acc.predicted", request));
							}else {
								accSweepSchedule.setOtherSweepFromBalTypeAsString(SwtUtil.getMessageFromSession("label.entity.externalIndicator", request));
							}
						}
						
						if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getTargetBalanceType())) {
							if("C".contentEquals(accSweepSchedule.getTargetBalanceType())){
								accSweepSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Credit", request));
							}else if("D".contentEquals(accSweepSchedule.getTargetBalanceType())){
								accSweepSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Debit", request));
							}else if("A".contentEquals(accSweepSchedule.getTargetBalanceType())){
								accSweepSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Acc.Attribute", request));
							}else {
								accSweepSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Rule", request));
							}
						}
						
						
						if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getSweepDirection())) {
							if("B".contentEquals(accSweepSchedule.getSweepDirection())){
								accSweepSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.Both", request));
							}else if("F".contentEquals(accSweepSchedule.getSweepDirection())){
								accSweepSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.fund", request));
							}else {
								accSweepSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.defund", request));
							}
						}
						
						
						if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getAllowMultiple())) {
							if("Y".contentEquals(accSweepSchedule.getAllowMultiple())){
								accSweepSchedule.setAllowMultipleAsString(SwtUtil.getMessageFromSession("button.yes", request));
							}else {
								accSweepSchedule.setAllowMultipleAsString(SwtUtil.getMessageFromSession("button.no", request));
							}
						}
						
						accSweepSchedule.setUniqueId(i);	
						i++;
						
						
						
					}
					log.debug(this.getClass().getName()
							+ " - [getAcctSweepScheduleList] - " + "Exiting");
				} catch (Exception e) {
					log.error(this.getClass().getName()
							+ " - [getAcctSweepScheduleList] - Exception -"
							+ e.getMessage());
					throw new SwtException(e.getMessage());
				} finally {
					// Nullify objects
					accSweepScheduleIterator = null;
					accSweepSchedule = null;
					simpleDateFormat = null;
				}

				return (ArrayList<AccSweepSchedule>) accSweepScheduleColl;
		
	}
	
	public long getAcctSweepScheduleUsedinCount(String hostId, String entityId, String accountId)  throws SwtException{
		long count =  0;
		
		log.debug("Entering 'getAcctSweepScheduleUsedinCount' method");
		try {
			
			count = acctMaintenanceDAO.getAcctSweepScheduleUsedinCount(hostId,entityId, accountId);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAcctSweepScheduleUsedinCount' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAcctSweepScheduleUsedinCount", AcctMaintenanceManagerImpl.class);
		}	
		
		return count;
		
	}
	

	public AccSweepSchedule getAcctSweepScheduleDetails(String hostId, String seqNumber) throws SwtException {
		log.debug("Entering 'getAcctSweepScheduleUsedinCount' method");
		AccSweepSchedule accSweepSchedule = null;
		try {
			
			accSweepSchedule = acctMaintenanceDAO.getAcctSweepScheduleDetails(hostId, seqNumber);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAcctSweepScheduleDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAcctSweepScheduleDetails", AcctMaintenanceManagerImpl.class);
		}	
		
		return accSweepSchedule;
	}

	@Override
	public void saveOrUpdateAcctScheduleSweep(AccSweepSchedule accountSchedule, String methodName) throws SwtException {
		// TODO Auto-generated method stub
		try {
			
			acctMaintenanceDAO.saveOrUpdateAcctScheduleSweep(accountSchedule, methodName);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'saveOrUpdateAcctScheduleSweep' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveOrUpdateAcctScheduleSweep", AcctMaintenanceManagerImpl.class);
		}	
		
	}

	@Override
	public void deleteAcctScheduleSweep(AccSweepSchedule accountSchedule) throws SwtException {
		try {
			
			acctMaintenanceDAO.deleteAcctScheduleSweep(accountSchedule);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'deleteAcctScheduleSweep' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctScheduleSweep", AcctMaintenanceManagerImpl.class);
		}	
	}
	
	public Collection<AccountSweepBalanceGroup> getAcctSweepBalGrpcoll(String hostId, String entityId, String accountId) throws SwtException{
		// To get the account interest rate collection
		Collection<AccountSweepBalanceGroup> acctSweepBalGrpColl = null;
		// To iterate the account interest rate collection
		Iterator<AccountSweepBalanceGroup> acctSweepBalGrpIterator = null;
		// Variable to handle Account interest rate
		AccountSweepBalanceGroup accountSweepBalanceGroup = null;
		
		try {
			// get the account sweep  balance group list
			acctSweepBalGrpColl = acctMaintenanceDAO.getAcctSweepBalGrpcoll(hostId, entityId, accountId);
			// Assign the iterator
			acctSweepBalGrpIterator = acctSweepBalGrpColl.iterator();
			// Iterates the collection
			int i = 0;
			while (acctSweepBalGrpIterator.hasNext()) {
				accountSweepBalanceGroup = acctSweepBalGrpIterator.next();				
				accountSweepBalanceGroup.setUniqueId(i);
				accountSweepBalanceGroup.setValid("true");
				i++;
				
			}
			return acctSweepBalGrpColl;
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAcctSweepBalGrpcoll' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAcctSweepBalGrpcoll", AcctMaintenanceManagerImpl.class);
		}
	}
	
	
	@Override
	public void saveAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp)  throws SwtException {
		// TODO Auto-generated method stub
		try {
			
			acctMaintenanceDAO.saveAcctSweepBalGrp(acctSweepBalGrp);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'saveAcctSweepBalGrp' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAcctSweepBalGrp", AcctMaintenanceManagerImpl.class);
		}	
		
	}

	@Override
	public void  deleteAcctSweepBalGrp(AccountSweepBalanceGroup acctSweepBalGrp)throws SwtException {
		try {
			
			acctMaintenanceDAO.deleteAcctSweepBalGrp(acctSweepBalGrp);
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'deleteAcctSweepBalGrp' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctSweepBalGrp", AcctMaintenanceManagerImpl.class);
		}	
	}
		
	
	/**
	 * 
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyCode -
	 *            CurrencyCode
	 * @return Collection - Collection of AcctMaintenance Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	public Collection getLinkAccountFullList(String hostId, String entityId,
			String currencyCode) throws SwtException {
		ArrayList acctlinkList = new ArrayList();
		try {
			log.debug("Exiting getLinkAccountFullList method");
			ArrayList linkAccountList = (ArrayList) acctMaintenanceDAO
					.getLinkAccountFullList(hostId, entityId, currencyCode);
			Iterator itr = linkAccountList.iterator();
			AcctMaintenance aactmaint = null;
			acctlinkList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				aactmaint = (AcctMaintenance) (itr.next());
				acctlinkList.add(new LabelValueBean(aactmaint.getAcctname(),
						aactmaint.getId().getAccountId()));
			}
			log.debug("Exiting getLinkAccountFullList method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getLinkAccountFullList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLinkAccountFullList", AcctMaintenanceManagerImpl.class);
		}
		return acctlinkList;
	}


	
	public Collection getAcctAttributesList() throws SwtException {
		ArrayList acctAttrList = new ArrayList();
		try {
			log.debug("Exiting getAcctAttributesList method");
			ArrayList acctAttributeList = (ArrayList) acctMaintenanceDAO
					.getAcctAttributesList();
			Iterator itr = acctAttributeList.iterator();
			AccountAttributeFuncGroup actAttr = null;
			acctAttrList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				actAttr = (AccountAttributeFuncGroup) (itr.next());
				acctAttrList.add(new LabelValueBean(actAttr.getId().getAttributeId(),
						actAttr.getId().getAttributeId()));
			}
			log.debug("Exiting getAcctAttributesList method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAcctAttributesList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAcctAttributesList", AcctMaintenanceManagerImpl.class);
		}
		return acctAttrList;
	}

	public Collection getAllowedTrgBalRulesList() throws SwtException {
		ArrayList swpRuleList = new ArrayList();
		try {
			log.debug("Exiting getAllowedTrgBalRulesList method");
			ArrayList sweepRuleList = (ArrayList) acctMaintenanceDAO
					.getAllowedTrgBalRulesList();
			Iterator itr = sweepRuleList.iterator();
			SweepRule swpRule = null;
			swpRuleList.add(new LabelValueBean("", ""));
			while (itr.hasNext()) {
				swpRule = (SweepRule) (itr.next());
				swpRuleList.add(new LabelValueBean(swpRule.getRuleName(), 
						swpRule.getRuleId()));
			}
			log.debug("Exiting getAllowedTrgBalRulesList method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getAllowedTrgBalRulesList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getAllowedTrgBalRulesList", AcctMaintenanceManagerImpl.class);
		}
		return swpRuleList;
	}
	
	
	public ArrayList<Party> getPartySearchResult(String partyId, String partyName, String entityId, int pageSize,
			int currentPage, String selectedsort) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return acctMaintenanceDAO.getPartySearchResult(partyId, partyName, entityId,
					pageSize, currentPage, selectedsort);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
		// return result as list
	}

	@Override
	public int getTotalCount(String partyName, String partyId, String entityId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getPartySearchResult ] - " + "Entry");
			return acctMaintenanceDAO.getTotalCount(partyName, partyId, entityId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getPartySearchResult] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchResult", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchResult] - Exit");
		}
	}
	
	
	/**
	 * This is used to check if current account is linked to another account
	 * 
	 * @param accountAccess
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAccountLinkedList(String accountId, String entityId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ checkAccountLinkedList ] - " + "Entry");
			return acctMaintenanceDAO.checkAccountLinkedList(accountId, entityId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [checkAccountLinkedList] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "checkAccountLinkedList", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [checkAccountLinkedList] - Exit");
		}
	}
	
	public Collection getLinkAccountColl(String hostId, String entityId,
			String currencyCode) throws SwtException {
		ArrayList linkAccountList = new ArrayList();
		try {
			log.debug("Exiting getLinkAccountList method");
			 linkAccountList = (ArrayList) acctMaintenanceDAO
					.getLinkAccountList(hostId, entityId, currencyCode);

			log.debug("Exiting getLinkAccountList method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceManagerImpl.'getLinkAccountList' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getLinkAccountList", AcctMaintenanceManagerImpl.class);
		}
		return linkAccountList;
	}

	@Override
	public String getAccountStatus(String hostId, String entityId, String accountId) throws SwtException {
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAccountStatus ] - " + "Entry");
			return acctMaintenanceDAO.getAccountStatus(hostId, entityId, accountId);
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - Exception Caught in [getAccountStatus] method : - "
					+ ex.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ex, "getAccountStatus", this.getClass());
		} finally {
			// log debug message
			log.debug(this.getClass().getName() + " - [getAccountStatus] - Exit");
		}
	}
}
