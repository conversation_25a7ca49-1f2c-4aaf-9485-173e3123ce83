/*
 * (c) MessageFormatsManagerImpl.java 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MessageFormatsDAO;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.model.ScenarioMessageFormats;
import org.swallow.maintenance.service.MessageFormatsManager;
import org.swallow.util.SwtConstants;

/**
 * 
 * This is Manger implementation class for Message Formats screen.
 * 
 * <AUTHOR>
 */
@Component("messageFormatsManager")
public class MessageFormatsManagerImpl implements MessageFormatsManager {
	private final Log log = LogFactory.getLog(MessageFormatsManagerImpl.class);
	@Autowired
	private MessageFormatsDAO dao;

	/**
	 * @param msgFormatsDAO
	 * @throws SwtException
	 */
	public void setMessageFormatsDAO(MessageFormatsDAO dao) {
		this.dao = dao;
	}

	/**
	 * This class returns a collection of MessageFormat objects. If formatId is
	 * All it returns all the MessageFromats available for given entity.
	 * 
	 * @param hostId
	 *            String object
	 * @param entityId
	 *            String Object
	 * @return Collection of MessageFormat objects
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public Collection getMsgFormatDetailList(String hostId, String formatId,
			String entityId) throws SwtException {

		// Variable Decleration for msgFormatsDetailsList
		ArrayList msgFormatsDetailsList = null;
		// Variable Decleration for msgFormatsDetailsList
		MessageFormats msgFrmts = null;
		// Variable Decleration for msgFormatsDetailsList
		Collection collmsgFrmts = null;
		// Variable Decleration for msgFormatsDetailsList
		String pathPropFile = null;
		// Variable Decleration for msgFormatsDetailsList
		Iterator itrMessageDtls = null;
		try {
			log
					.debug("entering  MessageFormatsManagerImpl.'getMsgFormatDetailList' method");
			msgFormatsDetailsList = new ArrayList();
			msgFrmts = new MessageFormats();
			// Get Message formats details for 'All' message formats
			if (formatId.equalsIgnoreCase("All")) {
				collmsgFrmts = dao.getMsgFormatDetailList(entityId, hostId);

				itrMessageDtls = collmsgFrmts.iterator();

				while (itrMessageDtls.hasNext()) {
					msgFrmts = (MessageFormats) (itrMessageDtls.next());

					// Set the Path in the Bean
					pathPropFile = dao.getMessageFormatPath();
					msgFrmts.setPath(pathPropFile);
					if ((msgFrmts.getAuthorizeFlag() != null)
							&& msgFrmts.getAuthorizeFlag()
									.equalsIgnoreCase("N")) {
						msgFrmts.setAuthorizeFlag("");
					}

					msgFormatsDetailsList.add(msgFrmts);
				}
			} else {// Get Message formats details for selected message format
				msgFrmts = dao.getMsgFormatDetail(entityId, hostId, formatId
						.trim());

				if (msgFrmts != null) {

					// Set the Path in the Bean
					pathPropFile = dao.getMessageFormatPath();
					msgFrmts.setPath(pathPropFile);
					// Add the details in the collection
					msgFormatsDetailsList.add(msgFrmts);
				}
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getMsgFormatDetailList' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMsgFormatDetailList", MessageFormatsManagerImpl.class);
		} finally {
			log
					.debug("Exiting  MessageFormatsManagerImpl.'getMsgFormatDetailList' method");
			collmsgFrmts = null;
			pathPropFile = null;
			itrMessageDtls = null;
		}

		return msgFormatsDetailsList;
	}
	
	

	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param entityId
	 * @param hostId
	 * @param formatId
	 * @return MessageFormats object with given id
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public MessageFormats getMsgFormatDetail(String entityId, String hostId,
			String formatId) throws SwtException {
		return dao.getMsgFormatDetail(entityId, hostId, formatId);
	}
	

	/**
	 * This method is used to save the MessageFormat object along with the
	 * messageFeilds given as a collection object
	 * 
	 * @param MessageFormats
	 *            object
	 * @param Collection
	 *            of MessageFeild objects
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public void saveMsgFormatDetails(MessageFormats msgfmt,
			Collection messageFieldDetails) throws SwtException {
		log
				.debug("entering MessageFormatsManagerImpl.'saveMsgFormatDetails' method");

		final String EMPTYSTRING = "";

		try {
			String usage = msgfmt.getUsage();
			if (usage.equals(SwtConstants.FORMAT_TYPE_OTHER)) {
				msgfmt.setAuthorizeFlag(EMPTYSTRING);
				msgfmt.setOverdueTime(EMPTYSTRING);
			}
			String formatType = msgfmt.getFormatType();
			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				if ((msgfmt.getHexaFldDelimeter() != null)
						&& !(msgfmt.getHexaFldDelimeter().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaFldDelimeter("N");
				} else if (msgfmt.getHexaFldDelimeter() == null) {
					msgfmt.setHexaFldDelimeter("N");
				}

				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
				if ((msgfmt.getHexaMsgSeparator() != null)
						&& !(msgfmt.getHexaMsgSeparator().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaMsgSeparator("N");
				} else if (msgfmt.getHexaMsgSeparator() == null) {
					msgfmt.setHexaMsgSeparator("N");
				}

				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
			}

			if ((msgfmt.getAuthorizeFlag() != null)
					&& !(msgfmt.getAuthorizeFlag().equalsIgnoreCase("Y"))) {
				msgfmt.setAuthorizeFlag("N");
			} else if (msgfmt.getAuthorizeFlag() == null) {
				msgfmt.setAuthorizeFlag("N");
			}

			dao.saveMsgFormatDetails(msgfmt);

			if (messageFieldDetails != null) {
				Iterator itr = messageFieldDetails.iterator();

				while (itr.hasNext()) {
					MessageFields msgFld = (MessageFields) (itr.next());
					String fldType = msgFld.getFieldType();

					if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.saveMsgFieldDetails(msgFld);
				}
			}

			log
					.debug("Exiting MessageFormatsManagerImpl.'saveMsgFormatDetails' method");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveMsgFormatDetails] method - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMsgFormatDetails", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to save the MessageFormat object along with the
	 * messageFeilds given as a collection object
	 * 
	 * @param MessageFormats
	 *            object
	 * @param Collection
	 *            of MessageFeild objects that has been added
	 * @param Collection
	 *            of MessageFeild objects that have to be updated
	 * @param Collection
	 *            of MessageFeild objects that have to be deleted
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public void updateMsgFormatDetails(MessageFormats msgfmt,
			Collection recordsAdded, Collection recordsUpdated,
			Collection messageFieldDetailsDeleted) throws SwtException {
		log
				.debug("entering MessageFormatsManagerImpl.'updateMsgFormatDetails' method");

		final String EMPTYSTRING = "";

		try {
			String usage = msgfmt.getUsage();
			if (usage.equals(SwtConstants.FORMAT_TYPE_OTHER)) {
				msgfmt.setAuthorizeFlag(EMPTYSTRING);
				msgfmt.setOverdueTime(EMPTYSTRING);
			}

			String formatType = msgfmt.getFormatType();
			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				if ((msgfmt.getHexaFldDelimeter() != null)
						&& !(msgfmt.getHexaFldDelimeter().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaFldDelimeter("N");
				} else if (msgfmt.getHexaFldDelimeter() == null) {
					msgfmt.setHexaFldDelimeter("N");
				}

				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}
			/*
			 * Start : Refer to
			 * SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc
			 */
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)
					|| formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
				/*
				 * End : Refer to
				 * SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc
				 */
				if ((msgfmt.getHexaMsgSeparator() != null)
						&& !(msgfmt.getHexaMsgSeparator().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaMsgSeparator("N");
				} else if (msgfmt.getHexaMsgSeparator() == null) {
					msgfmt.setHexaMsgSeparator("N");
				}

				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
			}

			if ((msgfmt.getAuthorizeFlag() != null)
					&& !(msgfmt.getAuthorizeFlag().equalsIgnoreCase("Y"))) {
				msgfmt.setAuthorizeFlag("N");
			} else if (msgfmt.getAuthorizeFlag() == null) {
				msgfmt.setAuthorizeFlag("N");
			}

			dao.updateMsgFormatDetails(msgfmt);

			if (recordsAdded != null) {
				Iterator itr = recordsAdded.iterator();

				while (itr.hasNext()) {
					MessageFields msgFld = (MessageFields) (itr.next());

					if (msgFld.getFieldType().equals(
							SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.saveMsgFieldDetails(msgFld);
				}
			}

			if (recordsUpdated != null) {
				Iterator itr = recordsUpdated.iterator();

				while (itr.hasNext()) {
					MessageFields msgFld = (MessageFields) (itr.next());

					if (msgFld.getFieldType().equals(
							SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.updateMsgFieldDetails(msgFld);
				}
			}

			if (messageFieldDetailsDeleted != null) {
				Iterator itr = messageFieldDetailsDeleted.iterator();

				while (itr.hasNext()) {
					MessageFields msgFld = (MessageFields) (itr.next());
					dao.deleteMessageFieldDetail(msgFld);
				}
			}

			log
					.debug("Exiting MessageFormatsManagerImpl.'updateMsgFormatDetails' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'updateMsgFormatDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFormatDetails", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete the data from P_MESSAGE_FORMATS table. It
	 * also deletes the associated message feilds with the given format id.
	 * 
	 * @param msgfmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has written this method on 7th Aug 07
	 */
	public void deleteMsgFormatDetail(MessageFormats msgfmt)
			throws SwtException {
		try {
			log
					.debug("entering MessageFormatsManagerImpl.'deleteMsgFormatDetail' method");
			dao.deleteMsgFormatDetail(msgfmt);
			log
					.debug("exiting MessageFormatsManagerImpl.'deleteMsgFormatDetail' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'deleteMsgFormatDetail' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMsgFormatDetail", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to fetch a collection of LabelValueBean with FormatId
	 * as label and Format Name as value.
	 * 
	 * @param HostId
	 *            String Object
	 * @param EntityId
	 *            String object
	 * @param formatId
	 * 			   String object
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	/*
	 * Start:code modified by sandeepkumar for mantis 1800 on 9-Oct-12:Copy from
	 * screen from Change Sweep formats should not display the same sweep
	 * message format ID
	 */
	public Collection getMsgFormatId(String hostId, String entityId,String formatId)
			throws SwtException {
		//variable declaration for message format details list
		ArrayList msgFormatsDetailsList = null;
		//variable to hold message formats
		MessageFormats msgFrmts = null;
		//variable to hold message format collection
		Collection collmsgFrmts = null;
		//iterator instance for message formats 
		Iterator msgFormatsItr = null;

		try {
			log.debug("entering MessageFormatsManagerImpl.'getMsgFormatId' method");
			//instance for message format details list
			msgFormatsDetailsList = new ArrayList();
			//instance for message formats
			msgFrmts = new MessageFormats();
			//get the message format details from dao 
			collmsgFrmts = dao.getMsgFormatDetailList(hostId,
					entityId);
			//iterating the message formats collection
			msgFormatsItr = collmsgFrmts.iterator();
			while (msgFormatsItr.hasNext()) {
				msgFrmts = (MessageFormats) (msgFormatsItr.next());
				//checks the format id is equal.if equal no need to add to message format details list
				if(!msgFrmts.getId().getFormatId().equals(formatId)  )
				{
				msgFormatsDetailsList.add(new LabelValueBean(msgFrmts
						.getFormatName(), msgFrmts.getId().getFormatId()));
				}
				
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getMsgFormatId' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMsgFormatId", MessageFormatsManagerImpl.class);
		}finally{
			log.debug("Exiting MessageFormatsManagerImpl.'getMsgFormatId' method");
			//nullify objects
			collmsgFrmts = null;
			msgFormatsItr = null;
		}
		/*
		 * End:code modified by sandeepkumar for mantis 1800 on 9-Oct-12:Copy from
		 * screen from Change Sweep formats should not display the same sweep
		 * message format ID
		 */
		return msgFormatsDetailsList;
	}

	
	/**
	 * This method is used to get the interface Id from the Database
	 * 
	 * @return
	 * @throws SwtException
	 */

	public Collection getInterfaceId() throws SwtException {
		// Variable decleration for inputInterface
		String inputInterface = null;
		// Variable decleration for collInterfaceId
		Collection collInterfaceId = null;
		// Variable decleration for interfaceList
		ArrayList interfaceList = null;
		// Variable decleration for itr
		Iterator itrIntefaceId = null;
		try {
			log
					.debug("entering  MessageFormatsManagerImpl.'getInterfaceId' method");
			interfaceList = new ArrayList();
			// get interfaceId from DAO layer
			collInterfaceId = dao.getInterfaceId();

			itrIntefaceId = collInterfaceId.iterator();
			interfaceList.add(new LabelValueBean("", ""));
			// Iterating interface
			while (itrIntefaceId.hasNext()) {
				inputInterface = (String) (itrIntefaceId.next());

				interfaceList.add(new LabelValueBean(inputInterface,
						inputInterface));
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getInterfaceId' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInterfaceId", MessageFormatsManagerImpl.class);
		} finally {
			log
					.debug("Exiting  MessageFormatsManagerImpl.'getInterfaceId' method");
			inputInterface = null;
			itrIntefaceId = null;
			collInterfaceId = null;
		}
		return interfaceList;
	}

	/**
	 * This method is used to fetch the Message format Path from the database
	 * 
	 * @return String
	 * @throws SwtException
	 */
	public String getMessageFormatPath() throws SwtException {
		try {
			log
					.debug("entering  MessageFormatsManagerImpl.'getMessageFormatPath' method");
			// Getting Message path from the DAO layer
			return dao.getMessageFormatPath();

		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getMessageFormatPath' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMessageFormatPath", MessageFormatsManagerImpl.class);
		} finally {
			log
					.debug("Exiting  MessageFormatsManagerImpl.'getMessageFormatPath' method");
		}
	}
	
	public Collection getScenarioMsgFormatDetailList(String formatId) throws SwtException{
		

		// Variable Decleration for msgFormatsDetailsList
		ArrayList msgFormatsDetailsList = null;
		// Variable Decleration for msgFormatsDetailsList
		ScenarioMessageFormats msgFrmts = null;
		// Variable Decleration for msgFormatsDetailsList
		Collection collmsgFrmts = null;
		// Variable Decleration for msgFormatsDetailsList
		String pathPropFile = null;
		// Variable Decleration for msgFormatsDetailsList
		Iterator itrMessageDtls = null;
		try {
			log
					.debug("entering  MessageFormatsManagerImpl.'getMsgFormatDetailList' method");
			msgFormatsDetailsList = new ArrayList();
			msgFrmts = new ScenarioMessageFormats();
			// Get Message formats details for 'All' message formats
			if (formatId.equalsIgnoreCase("All")) {
				collmsgFrmts = dao.getScenarioMsgFormatDetailList();

				itrMessageDtls = collmsgFrmts.iterator();

				while (itrMessageDtls.hasNext()) {
					msgFrmts = (ScenarioMessageFormats) (itrMessageDtls.next());

					// Set the Path in the Bean
					pathPropFile = dao.getMessageFormatPath();
					msgFrmts.setPath(pathPropFile);
					if ((msgFrmts.getAuthorizeFlag() != null)
							&& msgFrmts.getAuthorizeFlag()
									.equalsIgnoreCase("N")) {
						msgFrmts.setAuthorizeFlag("");
					}

					msgFormatsDetailsList.add(msgFrmts);
				}
			} else {// Get Message formats details for selected message format
				msgFrmts = dao.getScenarioMsgFormatDetail(formatId
						.trim());

				if (msgFrmts != null) {

					// Set the Path in the Bean
					pathPropFile = dao.getMessageFormatPath();
					msgFrmts.setPath(pathPropFile);
					// Add the details in the collection
					msgFormatsDetailsList.add(msgFrmts);
				}
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getMsgFormatDetailList' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMsgFormatDetailList", MessageFormatsManagerImpl.class);
		} finally {
			log
					.debug("Exiting  MessageFormatsManagerImpl.'getMsgFormatDetailList' method");
			collmsgFrmts = null;
			pathPropFile = null;
			itrMessageDtls = null;
		}

		return msgFormatsDetailsList;
		
	}
	
	/**
	 * This method is used to fetch the message format object from the database
	 * with given format id.
	 * 
	 * @param scenarioId
	 * @param hostId
	 * @param formatId
	 * @return MessageFormats object with given id
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public ScenarioMessageFormats getScenarioMsgFormatDetail(String formatId) throws SwtException {
		return dao.getScenarioMsgFormatDetail( formatId);
	}
	
	
	
	
	
	
	
	
	/**
	 * This method is used to save the MessageFormat object along with the
	 * messageFeilds given as a collection object
	 * 
	 * @param MessageFormats
	 *            object
	 * @param Collection
	 *            of MessageFeild objects
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public void saveScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt,
			Collection messageFieldDetails) throws SwtException {
		log
				.debug("entering MessageFormatsManagerImpl.'saveMsgFormatDetails' method");

		final String EMPTYSTRING = "";

		try {
			String usage = msgfmt.getUsage();
			if (usage.equals(SwtConstants.FORMAT_TYPE_OTHER)) {
				msgfmt.setAuthorizeFlag(EMPTYSTRING);
				msgfmt.setOverdueTime(EMPTYSTRING);
			}
			String formatType = msgfmt.getFormatType();
			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				if ((msgfmt.getHexaFldDelimeter() != null)
						&& !(msgfmt.getHexaFldDelimeter().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaFldDelimeter("N");
				} else if (msgfmt.getHexaFldDelimeter() == null) {
					msgfmt.setHexaFldDelimeter("N");
				}

				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
				if ((msgfmt.getHexaMsgSeparator() != null)
						&& !(msgfmt.getHexaMsgSeparator().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaMsgSeparator("N");
				} else if (msgfmt.getHexaMsgSeparator() == null) {
					msgfmt.setHexaMsgSeparator("N");
				}

				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
			}

			if ((msgfmt.getAuthorizeFlag() != null)
					&& !(msgfmt.getAuthorizeFlag().equalsIgnoreCase("Y"))) {
				msgfmt.setAuthorizeFlag("N");
			} else if (msgfmt.getAuthorizeFlag() == null) {
				msgfmt.setAuthorizeFlag("N");
			}

			dao.saveScenarioMsgFormatDetails(msgfmt);

			if (messageFieldDetails != null) {
				Iterator itr = messageFieldDetails.iterator();

				while (itr.hasNext()) {
					ScenarioMessageFields msgFld = (ScenarioMessageFields) (itr.next());
					String fldType = msgFld.getFieldType();

					if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.saveScenarioMsgFieldDetails(msgFld);
				}
			}

			log
					.debug("Exiting MessageFormatsManagerImpl.'saveMsgFormatDetails' method");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveMsgFormatDetails] method - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMsgFormatDetails", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to save the MessageFormat object along with the
	 * messageFeilds given as a collection object
	 * 
	 * @param MessageFormats
	 *            object
	 * @param Collection
	 *            of MessageFeild objects that has been added
	 * @param Collection
	 *            of MessageFeild objects that have to be updated
	 * @param Collection
	 *            of MessageFeild objects that have to be deleted
	 * @throws SwtException
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	public void updateScenarioMsgFormatDetails(ScenarioMessageFormats msgfmt,
			Collection recordsAdded, Collection recordsUpdated,
			Collection messageFieldDetailsDeleted) throws SwtException {
		log
				.debug("entering MessageFormatsManagerImpl.'updateMsgFormatDetails' method");

		final String EMPTYSTRING = "";

		try {
			String usage = msgfmt.getUsage();
			if (usage.equals(SwtConstants.FORMAT_TYPE_OTHER)) {
				msgfmt.setAuthorizeFlag(EMPTYSTRING);
				msgfmt.setOverdueTime(EMPTYSTRING);
			}

			String formatType = msgfmt.getFormatType();
			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				if ((msgfmt.getHexaFldDelimeter() != null)
						&& !(msgfmt.getHexaFldDelimeter().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaFldDelimeter("N");
				} else if (msgfmt.getHexaFldDelimeter() == null) {
					msgfmt.setHexaFldDelimeter("N");
				}

				msgfmt.setMsgSeparator(EMPTYSTRING);
				msgfmt.setHexaMsgSeparator(EMPTYSTRING);
			}
			/*
			 * Start : Refer to
			 * SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc
			 */
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)
					|| formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
				/*
				 * End : Refer to
				 * SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc
				 */
				if ((msgfmt.getHexaMsgSeparator() != null)
						&& !(msgfmt.getHexaMsgSeparator().equalsIgnoreCase("Y"))) {
					msgfmt.setHexaMsgSeparator("N");
				} else if (msgfmt.getHexaMsgSeparator() == null) {
					msgfmt.setHexaMsgSeparator("N");
				}

				msgfmt.setFieldDelimeter(EMPTYSTRING);
				msgfmt.setHexaFldDelimeter(EMPTYSTRING);
			}

			if ((msgfmt.getAuthorizeFlag() != null)
					&& !(msgfmt.getAuthorizeFlag().equalsIgnoreCase("Y"))) {
				msgfmt.setAuthorizeFlag("N");
			} else if (msgfmt.getAuthorizeFlag() == null) {
				msgfmt.setAuthorizeFlag("N");
			}

			dao.updateScenarioMsgFormatDetails(msgfmt);

			if (recordsAdded != null) {
				Iterator itr = recordsAdded.iterator();

				while (itr.hasNext()) {
					ScenarioMessageFields msgFld = (ScenarioMessageFields) (itr.next());

					if (msgFld.getFieldType().equals(
							SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.saveScenarioMsgFieldDetails(msgFld);
				}
			}

			if (recordsUpdated != null) {
				Iterator itr = recordsUpdated.iterator();

				while (itr.hasNext()) {
					ScenarioMessageFields msgFld = (ScenarioMessageFields) (itr.next());

					if (msgFld.getFieldType().equals(
							SwtConstants.FIELD_TYPE_KEYWORD)) {
						msgFld.setValue(msgFld.getValueKeyWord());
					}

					dao.updateScenarioMsgFieldDetails(msgFld);
				}
			}

			if (messageFieldDetailsDeleted != null) {
				Iterator itr = messageFieldDetailsDeleted.iterator();

				while (itr.hasNext()) {
					ScenarioMessageFields msgFld = (ScenarioMessageFields) (itr.next());
					dao.deleteScenarioMessageFieldDetail(msgFld);
				}
			}

			log
					.debug("Exiting MessageFormatsManagerImpl.'updateMsgFormatDetails' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'updateMsgFormatDetails' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMsgFormatDetails", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete the data from P_MESSAGE_FORMATS table. It
	 * also deletes the associated message feilds with the given format id.
	 * 
	 * @param msgfmt
	 * @throws SwtException
	 * <AUTHOR> Tripathi has written this method on 7th Aug 07
	 */
	public void deleteScenarioMsgFormatDetail(ScenarioMessageFormats msgfmt)
			throws SwtException {
		try {
			log
					.debug("entering MessageFormatsManagerImpl.'deleteMsgFormatDetail' method");
			dao.deleteScenarioMsgFormatDetail(msgfmt);
			log
					.debug("exiting MessageFormatsManagerImpl.'deleteMsgFormatDetail' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'deleteMsgFormatDetail' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteMsgFormatDetail", MessageFormatsManagerImpl.class);
		}
	}

	/**
	 * This method is used to fetch a collection of LabelValueBean with FormatId
	 * as label and Format Name as value.
	 * 
	 * @param HostId
	 *            String Object
	 * @param EntityId
	 *            String object
	 * @param formatId
	 * 			   String object
	 * <AUTHOR> Tripathi has Re-Written this method on 7th Aug 07
	 */
	/*
	 * Start:code modified by sandeepkumar for mantis 1800 on 9-Oct-12:Copy from
	 * screen from Change Sweep formats should not display the same sweep
	 * message format ID
	 */
	public Collection getScenarioMsgFormatId(String formatId)
			throws SwtException {
		//variable declaration for message format details list
		ArrayList msgFormatsDetailsList = null;
		//variable to hold message formats
		ScenarioMessageFormats msgFrmts = null;
		//variable to hold message format collection
		Collection collmsgFrmts = null;
		//iterator instance for message formats 
		Iterator msgFormatsItr = null;

		try {
			log.debug("entering MessageFormatsManagerImpl.'getMsgFormatId' method");
			//instance for message format details list
			msgFormatsDetailsList = new ArrayList();
			//instance for message formats
			msgFrmts = new ScenarioMessageFormats();
			//get the message format details from dao 
			collmsgFrmts = dao.getScenarioMsgFormatDetailList();
			//iterating the message formats collection
			msgFormatsItr = collmsgFrmts.iterator();
			while (msgFormatsItr.hasNext()) {
				msgFrmts = (ScenarioMessageFormats) (msgFormatsItr.next());
				//checks the format id is equal.if equal no need to add to message format details list
				if(!msgFrmts.getId().getFormatId().equals(formatId)  )
				{
				msgFormatsDetailsList.add(new LabelValueBean(msgFrmts
						.getFormatName(), msgFrmts.getId().getFormatId()));
				}
				
			}
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getMsgFormatId' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMsgFormatId", MessageFormatsManagerImpl.class);
		}finally{
			log.debug("Exiting MessageFormatsManagerImpl.'getMsgFormatId' method");
			//nullify objects
			collmsgFrmts = null;
			msgFormatsItr = null;
		}
		/*
		 * End:code modified by sandeepkumar for mantis 1800 on 9-Oct-12:Copy from
		 * screen from Change Sweep formats should not display the same sweep
		 * message format ID
		 */
		return msgFormatsDetailsList;
	}
	
	/**
	 * This method is used to get scenario message formats from P_SCN_MESSAGE_FORMAT
	 */
	public  Collection<LabelValueBean> getScenMsgFormats() throws SwtException{
		try {
			log
					.debug("entering MessageFormatsManagerImpl.'getScenMsgFormats' method");
			return (dao.getScenMsgFormats());

		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'getScenMsgFormats' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"getScenMsgFormats", MessageFormatsManagerImpl.class);
		}
	}
	
	/**
	 * This method is used to delete scenario message formats from P_SCN_MESSAGE_FORMAT and P_SCN_MESSAGE_FIELDS
	 */
	public void deleteScenMsgFormatById(String formatId) throws SwtException{
		
		try {
			log
					.debug("entering MessageFormatsManagerImpl.'deleteScenMsgFormatById' method");
			dao.deleteScenMsgFormatById(formatId);

		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFormatsManagerImpl.'deleteScenMsgFormatById' method : "
							+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteScenMsgFormatById", MessageFormatsManagerImpl.class);
		}	
	}

}
