/**
 * @(#)ForecastMonitorTemplateManagerImpl.java 1.0 24/05/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.ForecastMonitorTemplateDAO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.maintenance.model.ForecastMonitorTemplateCol;
import org.swallow.maintenance.model.ForecastMonitorTemplateColSrc;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.service.ForecastMonitorTemplateManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.UserTemplate;

/**
 * ForecastMonitorTemplateManagerImpl.java
 * 
 * ForecastMonitorTemplateManagerImpl class is used for ForecastMonitorTemplate
 * screen that will display ForecastMonitor Templates<br>
 * 
 * 
 * 
 */
@Component("forecastMonitorTemplateManager")
public class ForecastMonitorTemplateManagerImpl implements
		ForecastMonitorTemplateManager {

	// Create a log instance for logging
	private static final Log log = LogFactory
			.getLog(ForecastMonitorTemplateManagerImpl.class);

	/*
	 * ForecastMonitorTemplateDAO Object
	 */
	@Autowired
	private ForecastMonitorTemplateDAO forecastMonitorTemplateDAO;

	/**
	 * @param forecastMonitorTemplateDAO
	 *            the forecastMonitorTemplateDAO to set
	 */
	public void setForecastMonitorTemplateDAO(
			ForecastMonitorTemplateDAO forecastMonitorTemplateDAO) {
		this.forecastMonitorTemplateDAO = forecastMonitorTemplateDAO;
	}

	/**
	 * 
	 * Method to get Forecast monitor Templates
	 * 
	 * @param hostId
	 * @param currentUserId
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplate> getForecastMonitorTemplate(
			String hostId, String currentUserId) throws SwtException {
		// Methods Local variable declaration
		List<ForecastMonitorTemplate> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplate]-Entry");
			// get template collection from dao
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplate(hostId, currentUserId);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplate] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplate]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * Method to delete Forecast monitor Template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 */
	public void deleteForecastMonitorTemplate(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// Method's loacal variable declaration
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[deleteForecastMonitorTemplate]-Entry");

			forecastMonitorTemplateCol = new ForecastMonitorTemplateCol();
			forecastMonitorTemplateColSrc = new ForecastMonitorTemplateColSrc();

			// Set Template Collection values
			forecastMonitorTemplateCol.getId().setHostId(
					forecastMonitorTemplate.getId().getHostId());
			forecastMonitorTemplateCol.setUserId(forecastMonitorTemplate
					.getUserId());
			forecastMonitorTemplateCol.getId().setTemplateId(
					forecastMonitorTemplate.getId().getTemplateId());

			// Set Tempalte collection sources
			forecastMonitorTemplateColSrc.getId().setHostId(
					forecastMonitorTemplate.getId().getHostId());
			forecastMonitorTemplateColSrc.setUserId(forecastMonitorTemplate
					.getUserId());
			forecastMonitorTemplateColSrc.getId().setTemplateId(
					forecastMonitorTemplate.getId().getTemplateId());

			// Delete all template column sources
			forecastMonitorTemplateDAO.deleteAllUserTemplate(getUserTemplates(
					forecastMonitorTemplate.getId().getTemplateId(),
					forecastMonitorTemplate.getId().getHostId()));

			// Delete all template column sources
			forecastMonitorTemplateDAO
					.deleteAllForecastMonitorTemplateColSrc(getForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc));

			// Delete all template column
			forecastMonitorTemplateDAO
					.deleteAllForecastMonitorTemplateCol(getForecastMonitorTemplateCol(forecastMonitorTemplateCol));

			// Delete template
			forecastMonitorTemplateDAO
					.deleteForecastMonitorTemplate(forecastMonitorTemplate);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteForecastMonitorTemplate] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteForecastMonitorTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[deleteForecastMonitorTemplate]-Exit");
		}
	}

	/**
	 * Method to delete Forecast monitor Template columns
	 * 
	 * @param forecastMonitorTemplatecolList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateCol(
			List<ForecastMonitorTemplateCol> forecastMonitorTemplatecolList)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "-[deleteAllForecastMonitorTemplateCol]-Entry");
			// Delete all template column sources
			forecastMonitorTemplateDAO
					.deleteAllForecastMonitorTemplateCol(forecastMonitorTemplatecolList);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAllForecastMonitorTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[deleteAllForecastMonitorTemplateCol]-Exit");
		}
	}

	/**
	 * Method to delete Forecast monitor Template sources
	 * 
	 * @param forecastMonitorTemplateSrcList
	 * @return
	 */
	public void deleteAllForecastMonitorTemplateColSrc(
			List<ForecastMonitorTemplateColSrc> forecastMonitorTemplateSrcList)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "-[deleteAllForecastMonitorTemplateColSrc]-Entry");
			// Delete all template column sources
			forecastMonitorTemplateDAO
					.deleteAllForecastMonitorTemplateColSrc(forecastMonitorTemplateSrcList);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteAllForecastMonitorTemplateColSrc] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAllForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[deleteAllForecastMonitorTemplateColSrc]-Exit");
		}
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		List<ForecastMonitorTemplateCol> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateCol]-Entry");
			// get template column collection from dao
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplateCol(forecastMonitorTemplateCol);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateCol]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * 
	 * Method to get User Templates
	 * 
	 * @param templateId
	 * @param hostId
	 * @return List of User templates
	 */
	public List<UserTemplate> getUserTemplates(String templateId, String hostId)
			throws SwtException {
		List<UserTemplate> userTemplateList = null;
		try {
			log.debug(this.getClass().getName() + "-[getUserTemplates]-Entry");
			// get user template collection from dao
			userTemplateList = forecastMonitorTemplateDAO.getUserTemplates(
					templateId, hostId);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getUserTemplates] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getUserTemplates", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[getUserTemplates]-Exit");
		}
		return userTemplateList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template column sources
	 */
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColSrc]-Entry");
			// get template column source collection from dao
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColSrc] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColSrc]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getForecastMonitorTemplateCol()
			throws SwtException {
		List<ForecastMonitorTemplateCol> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateCol]-Entry");
			// get template column collection from dao
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplateCol();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateCol]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	
	/**
	 * 
	 * Method to get Book Code details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of book codes
	 */
	public List<BookCode> getBookCollection(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {
		// local variable declaration
		// Book code instance
		BookCode bookCode = null;
		// list to hold book code list
		List<BookCode> bookCodeList = null;
		try {
			log.debug(this.getClass().getName() + "-[getBookCollection]-Entry");
			// Instantiate bookcode instance
			bookCode = new BookCode();
			// get book code collection from dao
			bookCodeList = forecastMonitorTemplateDAO.getBookCollection(hostId,
					entityId, fieldId, fieldName);
			/* Set the book code OTHERS when there is no search performed or
			 * searched with 'OTHERS' or on load 
			 * Code added by Nageswararao on 16-May-2012 for Mantis 1867*/
			if ((SwtUtil.isEmptyOrNull(fieldId) && SwtUtil
					.isEmptyOrNull(fieldName))
					|| ((!SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
							.contains(fieldId)) && (!SwtUtil
							.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
							.contains(fieldName)))
					|| ((!SwtUtil.isEmptyOrNull(fieldId)
							&& SwtUtil.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
								.contains(fieldId)) || (!SwtUtil
							.isEmptyOrNull(fieldName)
							&& SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
								.contains(fieldName)))) {
				// set book code id
				bookCode.getId().setBookCode(SwtConstants.OTHERS_VALUE);
				// set book name
				bookCode.setBookName(SwtConstants.OTHERS_VALUE);
				// add others bookCode to list
				bookCodeList.add(bookCode);
			}
			/* End of code modified for Mantis 1867 by Nageswararao on 16-May-2012 */
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getBookCollection] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getBookCollection", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[getBookCollection]-Exit");
		}
		return bookCodeList;
	}

	
	/**
	 * This is used to get the group details from P_GROUP table.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of group
	 */
	public List<Group> getGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {
		// local variable declaration
		// Group instance
		Group group = null;
		// list to hold Group list
		List<Group> groupList = null;
		try {
			log.debug(this.getClass().getName() + "-[getGroupDetails]-Entry");
			
			// Instantiate group instance
			group = new Group();
			// get group collection from dao
			groupList = forecastMonitorTemplateDAO.getGroupDetails(hostId,
					entityId, fieldId, fieldName);
			/* Set the group OTHERS when there is no search performed or
			 * searched with 'OTHERS' or on load 
			 * Code added by Nageswararao on 16-May-2012 for mantis 1867*/
			if ((SwtUtil.isEmptyOrNull(fieldId) && SwtUtil
					.isEmptyOrNull(fieldName))
					|| ((!SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
							.contains(fieldId)) && (!SwtUtil
							.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
							.contains(fieldName)))
					|| ((!SwtUtil.isEmptyOrNull(fieldId)
							&& SwtUtil.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
								.contains(fieldId)) || (!SwtUtil
							.isEmptyOrNull(fieldName)
							&& SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
								.contains(fieldName)))) {
				// set group id
				group.getId().setGroupId(SwtConstants.OTHERS_VALUE);
				// set group name
				group.setGroupName(SwtConstants.OTHERS_VALUE);
				// add others group to list
				groupList.add(group);
			}
			/* End of code modified for 1867 by Nageswararao on 16-May-2012 */			
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getGroupDetails] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getGroupDetails", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[getGroupDetails]-Exit");
		}
		return groupList;

	}

	
	/**
	 * This is used to get meta group details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param fieldId
	 * @param fieldName
	 * @return List of Meta Group
	 */
	public List<MetaGroup> getMetaGroupDetails(String hostId, String entityId,
			String fieldId, String fieldName) throws SwtException {
		// local variable declaration
		// MetaGroup instance
		MetaGroup metaGroup = null;
		// list to hold MetaGroup list
		List<MetaGroup> metaGroupList = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getMetaGroupDetails]-Entry");
			// Instantiate group instance
			metaGroup = new MetaGroup();
			// get meta group collection from dao
			metaGroupList = forecastMonitorTemplateDAO.getMetaGroupDetails(
					hostId, entityId, fieldId, fieldName);
			/* Set the metagroup OTHERS when there is no search performed or
			 * searched with 'OTHERS' or on load. 
			 * Code added by Nageswararao on 16-May-2012 for Mantis 1867*/
			if ((SwtUtil.isEmptyOrNull(fieldId) && SwtUtil
					.isEmptyOrNull(fieldName))
					|| ((!SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
							.contains(fieldId)) && (!SwtUtil
							.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
							.contains(fieldName)))
					|| ((!SwtUtil.isEmptyOrNull(fieldId)
							&& SwtUtil.isEmptyOrNull(fieldName) && SwtConstants.OTHERS_VALUE
								.contains(fieldId)) || (!SwtUtil
							.isEmptyOrNull(fieldName)
							&& SwtUtil.isEmptyOrNull(fieldId) && SwtConstants.OTHERS_VALUE
								.contains(fieldName)))) {
				// set group id
				metaGroup.getId().setMgroupId(SwtConstants.OTHERS_VALUE);
				// set group name
				metaGroup.setMgroupName(SwtConstants.OTHERS_VALUE);
				// add others group to list
				metaGroupList.add(metaGroup);
			}
			/* End of code modified for mantis 1867 by Nageswararao on 16-May-2012 */
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getMetaGroupDetails] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getMetaGroupDetails", this.getClass());
		} finally {
			log
					.debug(this.getClass().getName()
							+ "-[getMetaGroupDetails]-Exit");
		}
		return metaGroupList;

	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplateColSrc> getChangeForecastMonitorTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		// list instance to hold column source
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplatelist = null;
		// list instance to hold column source
		ArrayList<ForecastMonitorTemplateColSrc> forecastMonitorTemplatelistToDisplay = null;
		// iterator instance to hold column source
		Iterator<ForecastMonitorTemplateColSrc> itrForecastMonitorTemplatelist = null;
		//ForecastMonitorTemplateColSrc instance
		ForecastMonitorTemplateColSrc forecastTemplateColSrc = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getChangeForecastMonitorTemplateColSrc]-Entry");

			// Instantiate array list
			forecastMonitorTemplatelistToDisplay = new ArrayList<ForecastMonitorTemplateColSrc>();

			// Get change details values
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getChangeForecastMonitorTemplateColSrc(forecastMonitorTemplateColSrc);

			// Iterate values
			itrForecastMonitorTemplatelist = forecastMonitorTemplatelist
					.iterator();

			// Loop to get detail values
			while (itrForecastMonitorTemplatelist.hasNext()) {
				// Obtain forecast Template column source
				forecastTemplateColSrc = itrForecastMonitorTemplatelist
						.next();

				// Condition to check source type is book
				if (forecastTemplateColSrc.getId().getSourceType().equals(
						SwtConstants.BOOK)) {
					// Set book label
					forecastTemplateColSrc.getId().setSourceType(
							SwtConstants.BOOK_LABEL);

				} else // Condition to check source type is Group
				if (forecastTemplateColSrc.getId().getSourceType().equals(
						SwtConstants.GROUP)) {
					// Set group label
					forecastTemplateColSrc.getId().setSourceType(
							SwtConstants.GROUP_LABEL);

				} else // Condition to check source type is Meta Group
				if (forecastTemplateColSrc.getId().getSourceType().equals(
						SwtConstants.META_GROUP)) {
					// Set meta group label
					forecastTemplateColSrc.getId().setSourceType(
							SwtConstants.META_GROUP_LABEL);

				} else // Condition to check source type is Entity
				if (forecastTemplateColSrc.getId().getSourceType().equals(
						SwtConstants.ENTITY)) {
					// Set Entity label
					forecastTemplateColSrc.getId().setSourceType(
							SwtConstants.ENTITY_LABEL);

				}

				// set values in list
				forecastMonitorTemplatelistToDisplay
						.add(forecastTemplateColSrc);

			}

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getChangeForecastMonitorTemplateColSrc] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getChangeForecastMonitorTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getChangeForecastMonitorTemplateColSrc]-Exit");
		}
		return forecastMonitorTemplatelistToDisplay;
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns sources for change screen
	 * for subtotal
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of template columns
	 */
	public List<ForecastMonitorTemplateCol> getChangeForecastMonitorTemplateColSrcForSubTotal(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		// local variable declaration
		List<ForecastMonitorTemplateCol> forecastMonitorTemplatelist = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "-[getChangeForecastMonitorTemplateColSrcForSubTotal]-Entry");
			// get template column source collection from dao
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getChangeForecastMonitorTemplateColSrcForSubTotal(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - [getChangeForecastMonitorTemplateColSrcForSubTotal] - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getChangeForecastMonitorTemplateColSrcForSubTotal",
					this.getClass());
		} finally {
			log
					.debug(this.getClass().getName()
							+ "-[getChangeForecastMonitorTemplateColSrcForSubTotal]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * 
	 * Method to lock template for selected user
	 * 
	 * @param forecastMonitorTemplate
	 * @return boolean - locked result
	 */
	public void lockTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[lockTemplate]-Entry");
			// lock template
			forecastMonitorTemplateDAO.lockTemplate(forecastMonitorTemplate);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [lockTemplate] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"lockTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[lockTemplate]-Exit");
		}
	}

	/**
	 * 
	 * Method to check whether the template is locked
	 * 
	 * @param forecastMonitorTemplate
	 * @return String - locked userID
	 */
	public String checkTemplateLocked(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// Method's loacal variable declaration
		// List to get template values
		List<ForecastMonitorTemplate> foretemplateList = null;
		// Iteratir instance
		Iterator<ForecastMonitorTemplate> itrForetemplateList = null;
		// boolean to hold locked result
		String lockedBy = null;
		//ForecastMonitorTemplate instance
		ForecastMonitorTemplate forecastTemplate = null;

		try {
			log.debug(this.getClass().getName()
					+ "-[checkTemplateLocked]-Entry");
			// get foretemplateList from dao layer
			foretemplateList = forecastMonitorTemplateDAO
					.checkTemplateLocked(forecastMonitorTemplate);

			// Iterate the collection
			itrForetemplateList = foretemplateList.iterator();

			// Get values from iterating
			while (itrForetemplateList.hasNext()) {
				// get ForecastMonitorTemplate values
				forecastTemplate = itrForetemplateList
						.next();
				// Condition to check locked user is not null
				if (!SwtUtil.isEmptyOrNull(forecastTemplate.getLockedBy())) {
					// set locked result as true
					lockedBy = forecastTemplate.getLockedBy();
				}

			}
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [checkTemplateLocked] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkTemplateLocked", this.getClass());
		} finally {
			log
					.debug(this.getClass().getName()
							+ "-[checkTemplateLocked]-Exit");
		}
		return lockedBy;

	}

	/**
	 * 
	 * Method to check whether the template is exist
	 * 
	 * @param forecastMonitorTemplate
	 * @return boolean
	 */
	public boolean checkTemplateExist(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		// Method's loacal variable declaration
		// List to get template values
		List<ForecastMonitorTemplate> foretemplateList = null;
		// boolean to hold locked result
		boolean isExist = false;

		try {
			log
					.debug(this.getClass().getName()
							+ "-[checkTemplateExist]-Entry");
			// get foretemplateList from dao layer
			foretemplateList = forecastMonitorTemplateDAO
					.checkTemplateLocked(forecastMonitorTemplate);

			// Iterate the collection
			if (foretemplateList.size() > 0)
				isExist = true;

		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [checkTemplateExist] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkTemplateExist", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[checkTemplateExist]-Exit");
		}
		return isExist;

	}

	/**
	 * 
	 * Method to unlock all locked columns
	 * 
	 * @param lockedUserId
	 * 
	 * @return List
	 */
	public List<ForecastMonitorTemplate> getTemplatesUnLock(String lockedUserId)
			throws SwtException {
		// Method's loacal variable declaration
		// List to get template values
		List<ForecastMonitorTemplate> foretemplateList = null;
		// Iterator to iterate template list
		Iterator<ForecastMonitorTemplate> itrTemplateList = null;
		//ForecastMonitorTemplate instance
		ForecastMonitorTemplate forecastTemplate = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getTemplatesUnLock] - Entry");

			// Get All templates which are locked
			foretemplateList = forecastMonitorTemplateDAO
					.getAllLock(lockedUserId);

			// iterate All locked template
			itrTemplateList = foretemplateList.iterator();
			// Loop to get each template
			while (itrTemplateList.hasNext()) {
				// Get locked Template values
				forecastTemplate = itrTemplateList
						.next();

				// set locked by values as null;
				forecastTemplate.setLockedBy(null);

				// unlock values from DB
				forecastMonitorTemplateDAO.lockTemplate(forecastTemplate);

			}

		} catch (Exception exp) {
			log.error("An exception occured in " + this.getClass().getName()
					+ "- getTemplatesUnLock -" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTemplatesUnLock", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "- [getTemplatesUnLock] - Exit");
		}
		// return foretemplateList
		return foretemplateList;
	}

	/**
	 * 
	 * Method to get Forecast monitor Templates for copy from
	 * 
	 * @param hostId
	 * @param currentUserId
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplate> getTemplatesCopyFrom(String hostId,
			String currentUserId) throws SwtException {
		// Methods Local variable declaration
		List<ForecastMonitorTemplate> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getTemplatesCopyFrom]-Entry");

			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getTemplatesCopyFrom(hostId, currentUserId);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getTemplatesCopyFrom] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getTemplatesCopyFrom", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getTemplatesCopyFrom]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * 
	 * Method to save forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @return
	 * @throws SwtException
	 */
	public void saveTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[saveTemplate]-Entry");
			forecastMonitorTemplateDAO.saveTemplate(forecastMonitorTemplate);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveTemplate] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[saveTemplate]-Exit");
		}
	}

	/**
	 * 
	 * Method to save forecast template column
	 * 
	 * @param forecastMonitorTemplateColList
	 * @return
	 * @throws SwtException
	 */
	public void saveTemplateCol(
			List<ForecastMonitorTemplateCol> forecastMonitorTemplateColList)
			throws SwtException {
		// Method's local variable declaration
		Iterator<ForecastMonitorTemplateCol> forecastColItr = null;
		//ForecastMonitorTemplateCol instance
		ForecastMonitorTemplateCol forecastMonitorTemplateCol = null;
		try {
			log.debug(this.getClass().getName() + "-[saveTemplateCol]-Entry");
			// Itearate listr
			forecastColItr = forecastMonitorTemplateColList.iterator();

			// loop to get forecast col values
			while (forecastColItr.hasNext()) {
				forecastMonitorTemplateCol = forecastColItr
						.next();

				forecastMonitorTemplateDAO
						.saveTemplateCol(forecastMonitorTemplateCol);

			}
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplateCol", this.getClass());
		} finally {
			forecastColItr = null;
			log.debug(this.getClass().getName() + "-[saveTemplateCol]-Exit");
		}
	}

	/**
	 * 
	 * Method to save forecast template column sources
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void saveTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		try {
			log
					.debug(this.getClass().getName()
							+ "-[saveTemplateColSrc]-Entry");
			// save template column source from dao
			forecastMonitorTemplateDAO
					.saveTemplateColSrc(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveTemplateColSrc] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[saveTemplateColSrc]-Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template
	 * 
	 * @param forecastMonitorTemplate
	 * @throws SwtException
	 */
	public void updateTemplate(ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[updateTemplate]-Entry");
			// update template from dao
			forecastMonitorTemplateDAO.updateTemplate(forecastMonitorTemplate);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [updateTemplate] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplate", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[updateTemplate]-Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template column sources
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void updateTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "-[updateTemplateColSrc]-Entry");
			// update template column source from dao
			forecastMonitorTemplateDAO
					.updateTemplateColSrc(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [updateTemplateColSrc] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[updateTemplateColSrc]-Exit");
		}
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @param columnType
	 * @return List of template column sources
	 */
	public List<ForecastMonitorTemplateColSrc> getForecastMonitorTemplateColSrcForColumnId(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc,
			String columnType) throws SwtException {
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplatelist = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColSrcForColumnId]-Entry");
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplateColSrcForColumnId(
							forecastMonitorTemplateColSrc, columnType);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColSrcForColumnId] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getForecastMonitorTemplateColSrcForColumnId",
					this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColSrcForColumnId]-Exit");
		}
		return forecastMonitorTemplatelist;
	}

	/**
	 * 
	 * Method to delete forecast template column source
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @throws SwtException
	 */
	public void deleteTemplateColSrc(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {

		try {
			log.debug(this.getClass().getName()
					+ "-[deleteTemplateColSrc]-Entry");
			forecastMonitorTemplateDAO
					.deleteTemplateColSrc(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [deleteTemplateColSrc] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteTemplateColSrc", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ "-[deleteTemplateColSrc]-Exit");
		}

	}

	/**
	 * 
	 * Method to delete forecast template columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * @throws SwtException
	 */
	public void deleteTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[deleteTemplateCol]-Entry");
			forecastMonitorTemplateDAO
					.deleteTemplateCol(forecastMonitorTemplateCol);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [deleteTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[deleteTemplateCol]-Exit");
		}
	}

	/**
	 * 
	 * Method to update forecast template col
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return
	 * @throws SwtException
	 */
	public void updateTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[updateTemplateCol]-Entry");
			forecastMonitorTemplateDAO
					.updateTemplateCol(forecastMonitorTemplateCol);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [updateTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[updateTemplateCol]-Exit");
		}
	}

	/**
	 * 
	 * Method to save forecast template columns
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return ForecastMonitorTemplateCol
	 * @throws SwtException
	 */
	public void saveTemplateCol(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + "-[saveTemplateCol]-Entry");
			forecastMonitorTemplateDAO
					.saveTemplateCol(forecastMonitorTemplateCol);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [saveTemplateCol] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveTemplateCol", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[saveTemplateCol]-Exit");
		}
	}

	/**
	 * 
	 * Method to get Forecast monitor Template Columns to get column id
	 * 
	 * @param forecastMonitorTemplateCol
	 * @return List of templates
	 */
	public ForecastMonitorTemplateCol getForecastMonitorTemplateColForColumnId(
			ForecastMonitorTemplateCol forecastMonitorTemplateCol)
			throws SwtException {
		// Method local variable declaration
		// list to hold forecast template collection
		List<ForecastMonitorTemplateCol> forecastMonitorTemplatelist = null;
		// forecast template object to hold selected values
		ForecastMonitorTemplateCol returnForecastMonitorCol = null;
		// list to hold forecast template collection
		Iterator<ForecastMonitorTemplateCol> forecastMonitorTemplateItr = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColForColumnId]-Entry");

			// get forecast template columns for selected column id
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.getForecastMonitorTemplateColForColumnId(forecastMonitorTemplateCol);

			// iterate collection
			forecastMonitorTemplateItr = forecastMonitorTemplatelist.iterator();
			// loop to get col bean
			while (forecastMonitorTemplateItr.hasNext())
				returnForecastMonitorCol = forecastMonitorTemplateItr.next();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getForecastMonitorTemplateColForColumnId] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance()
					.handleException(exp,
							"getForecastMonitorTemplateColForColumnId",
							this.getClass());
		} finally {
			forecastMonitorTemplatelist = null;
			forecastMonitorTemplateItr = null;
			log.debug(this.getClass().getName()
					+ "-[getForecastMonitorTemplateColForColumnId]-Exit");
		}
		// return col bean
		return returnForecastMonitorCol;
	}

	/**
	 * 
	 * Method to check Forecast monitor Template Columns sources exists
	 * 
	 * @param forecastMonitorTemplateColSrc
	 * @return List of templates
	 */
	public List<ForecastMonitorTemplateColSrc> checkForecastExist(
			ForecastMonitorTemplateColSrc forecastMonitorTemplateColSrc)
			throws SwtException {
		// forecastMonitorTemplatelist to hold forecastMonitorTemplate
		List<ForecastMonitorTemplateColSrc> forecastMonitorTemplatelist = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "-[checkForecastExist]-Entry");
			// get forecastMonitorTemplatelist from DAO layer
			forecastMonitorTemplatelist = forecastMonitorTemplateDAO
					.checkForecastExist(forecastMonitorTemplateColSrc);
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [checkForecastExist] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkForecastExist", this.getClass());
		} finally {
			log.debug(this.getClass().getName() + "-[checkForecastExist]-Exit");
		}
		// return forecastMonitorTemplatelist
		return forecastMonitorTemplatelist;

	}
}