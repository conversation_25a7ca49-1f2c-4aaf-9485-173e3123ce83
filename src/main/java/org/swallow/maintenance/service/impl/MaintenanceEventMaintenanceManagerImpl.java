package org.swallow.maintenance.service.impl;

import org.swallow.maintenance.service.MaintenanceEventMaintenanceManager;
import java.lang.*;
import java.util.*;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtErrorHandler;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.maintenance.model.MaintenanceEvent;
import org.swallow.maintenance.model.MaintenanceEventDetails;
import org.swallow.maintenance.model.MaintenanceEventForm;
import org.swallow.maintenance.model.MaintenanceLogViewAuth;
import org.swallow.maintenance.dao.MaintenanceEventMaintenanceDAO;

@Component("maintenanceEventMaintenanceManager")
public class MaintenanceEventMaintenanceManagerImpl
		implements
			MaintenanceEventMaintenanceManager {

	private final Log log = LogFactory
			.getLog(MaintenanceEventMaintenanceManagerImpl.class);
	@Autowired
	private MaintenanceEventMaintenanceDAO maintenanceEventMaintenanceDAO;

	public void setMaintenanceEventMaintenanceDAO(
			MaintenanceEventMaintenanceDAO maintenanceEventMaintenanceDAO) {
		this.maintenanceEventMaintenanceDAO = maintenanceEventMaintenanceDAO;
	}

	public Long saveMaintenanceEvent(MaintenanceEvent maintenanceEvent)
			throws SwtException {
		try {
			return maintenanceEventMaintenanceDAO
					.saveMaintenanceEvent(maintenanceEvent);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveMaintenanceEvent] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveMaintenanceEvent",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public void updateMaintenanceEvent(MaintenanceEvent maintenanceEvent)
			throws SwtException {
		try {
			maintenanceEventMaintenanceDAO
					.updateMaintenanceEvent(maintenanceEvent);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateMaintenanceEvent] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMaintenanceEvent",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public void deleteMaintenanceEvent(MaintenanceEvent maintenanceEvent)
			throws SwtException {
		try {
			maintenanceEventMaintenanceDAO
					.deleteMaintenanceEvent(maintenanceEvent);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteMaintenanceEvent] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteMaintenanceEvent",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public MaintenanceEvent getMaintenanceEvent(String maintEventId)
			throws SwtException {
		MaintenanceEvent result;
		try {
			result = maintenanceEventMaintenanceDAO
					.getMaintenanceEvent(maintEventId);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getMaintenanceEvent] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMaintenanceEvent",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
		return result;
	}

	public Collection<MaintenanceEvent> getMaintenanceEventList(MaintenanceEventForm eventForm)
			throws SwtException {
		Collection<MaintenanceEvent> result;
		try {
			result = maintenanceEventMaintenanceDAO.getMaintenanceEventList(eventForm);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getMaintenanceEventList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMaintenanceEventList",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
		return result;
	}
	
	public void saveMaintenanceEventDetails(
			MaintenanceEventDetails maintenanceEventDetails)
			throws SwtException {
		try {
			maintenanceEventMaintenanceDAO
					.saveMaintenanceEventDetails(maintenanceEventDetails);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [saveMaintenanceEventDetails] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"saveMaintenanceEventDetails",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public void updateMaintenanceEventDetails(
			MaintenanceEventDetails maintenanceEventDetails)
			throws SwtException {
		try {
			maintenanceEventMaintenanceDAO
					.updateMaintenanceEventDetails(maintenanceEventDetails);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [updateMaintenanceEventDetails] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateMaintenanceEventDetails",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public void deleteMaintenanceEventDetails(
			MaintenanceEventDetails maintenanceEventDetails)
			throws SwtException {
		try {
			maintenanceEventMaintenanceDAO
					.deleteMaintenanceEventDetails(maintenanceEventDetails);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [deleteMaintenanceEventDetails] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteMaintenanceEventDetails",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
	}

	public MaintenanceEventDetails getMaintenanceEventDetails(
			String maintEventId, String tableName) throws SwtException {
		MaintenanceEventDetails result;
		try {
			result = maintenanceEventMaintenanceDAO
					.getMaintenanceEventDetails(maintEventId, tableName);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getMaintenanceEventDetails] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMaintenanceEventDetails",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
		return result;
	}

	public Collection<MaintenanceEventDetails> getMaintenanceEventDetailsList()
			throws SwtException {
		Collection<MaintenanceEventDetails> result;
		try {
			result = maintenanceEventMaintenanceDAO
					.getMaintenanceEventDetailsList();
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [getMaintenanceEventDetailsList] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getMaintenanceEventDetailsList",
					MaintenanceEventMaintenanceManagerImpl.class);
		}
		return result;
	}

	public Collection<MaintenanceLogViewAuth> getMaintenanceLogList(String maintEventId) throws SwtException {
		Collection<MaintenanceLogViewAuth> result = null;
		try {
			result = maintenanceEventMaintenanceDAO.getMaintenanceLogList(maintEventId);
		} catch (Exception e) {
			log.error(this.getClass().getName() + "- [getMaintenanceLogList] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getMaintenanceLogList",
					MaintenanceEventMaintenanceManagerImpl.class);
		}

		return result;
	}
	
	/**
	 * This method is used to get the list of records from  s_maintenance_log according to given hostId, userId, logDate, ipAddress, tableName, reference and action
	 */
	
	public Collection getViewLogDetails(String hostId, String userId, Date logDate, String ipAddress,
			String tableName, String reference, String action, Long mainEventId) throws SwtException {

		return maintenanceEventMaintenanceDAO.getViewLogDetails(hostId, userId, logDate, ipAddress, tableName, reference,
				action, mainEventId);
	}
	
	
	public boolean isRecordRelatedToMaintenanceEvent(String recordId, String facilityId) throws SwtException {
		return maintenanceEventMaintenanceDAO.isRecordRelatedToMaintenanceEvent(recordId, facilityId);
	}
	
}