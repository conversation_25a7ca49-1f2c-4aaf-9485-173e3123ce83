/*
 * Created on Dec 3, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.service.WeekdaysManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Component("weekdaysManager")
public class WeekdaysManagerImpl implements WeekdaysManager {
	private final Log log = LogFactory.getLog(WeekdaysManagerImpl.class);
	public Collection getWeekdays()  throws SwtException{
		log.debug("entering 'getWeekdays' method");

		ArrayList weekdaysList = new ArrayList();
		/* Start code: Code modified for Mantis 1150 by Karthik on 19-July-10 */		
		weekdaysList.add(new LabelValueBean("",""));
		weekdaysList.add(new LabelValueBean("Monday",SwtConstants.WEEKDAY_MON));
		weekdaysList.add(new LabelValueBean("Tuesday",SwtConstants.WEEKDAY_TUE));
		weekdaysList.add(new LabelValueBean("Wednesday",SwtConstants.WEEKDAY_WED));
		weekdaysList.add(new LabelValueBean("Thursday",SwtConstants.WEEKDAY_THU));
		weekdaysList.add(new LabelValueBean("Friday",SwtConstants.WEEKDAY_FRI));
		weekdaysList.add(new LabelValueBean("Saturday",SwtConstants.WEEKDAY_SAT));
		weekdaysList.add(new LabelValueBean("Sunday",SwtConstants.WEEKDAY_SUN));
		/* End code: Code modified for Mantis 1150 by Karthik on 19-July-10 */
		log.debug("exiting 'getWeekdays' method");
		
		return weekdaysList;
	}
}
