/*
 * @(#)CurrencyAliasManagerImpl.java 01/10/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyAliasDAO;
import org.swallow.maintenance.model.CurrencyAlias;
import org.swallow.maintenance.service.CurrencyAliasManager;

/*
 * This is manager class for Currency alias screen
 * 
 */
@Component ("CurrencyAliasManager")
public class CurrencyAliasManagerImpl implements CurrencyAliasManager {

	/**
	 * Instance of Log.java class
	 */
	private final Log log = LogFactory.getLog(CurrencyAliasManagerImpl.class);

	/**
	 * CurrencyGroupDAO object
	 */
	@Autowired
	private CurrencyAliasDAO dao;

	/**
	 * @param currencyAliasDAO -
	 *            CurrencyAliasDAO object
	 */
	public void setCurrencyAliasDAO(CurrencyAliasDAO dao) {
		this.dao = dao;
	}

	/**
	 * This method is used to fetch the CurrencyAlias obejcts from DB
	 * 
	 * @param hostId -
	 *            Host Id
	 * @param entityId -
	 *            Entity Id
	 * @return Collection object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public Collection getCurrencyAliasList(String hostId, String entityId)
			throws SwtException {
		log.debug("Entering into CurrencyAliasManagerImpl.getCurrencyAliasList() method");
		Collection currencyAliasList = new ArrayList();
		try {
			Collection coll = dao.getCurrencyAliasList(hostId, entityId);
			if (coll != null) {
				Iterator itr = coll.iterator();
				while (itr.hasNext()) {
					Object[] currencyDetail = (Object[]) (itr.next());
					CurrencyAlias ca = new CurrencyAlias();
					ca.getId().setAlias(currencyDetail[0].toString());
					ca.setCurrencyCode(currencyDetail[1].toString());
					ca.setCurrencyName(currencyDetail[2].toString());
					currencyAliasList.add(ca);
				}
			}
		} catch (Exception exp) {
			log.debug("Exception Catch in CurrencyAliasManagerImpl.'saveCurrencyAliasDetails' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyAliasDetails", CurrencyAliasManagerImpl.class);
		}
		log.debug("Exiting from CurrencyAliasManagerImpl.getCurrencyAliasList() method");
		return currencyAliasList;
	}

	/**
	 * This method is used to save CurrencyAlias obejct into DB.
	 * 
	 * @param CurrencyAlias
	 *            currencyAlias
	 */
	public void saveCurrencyAliasDetails(CurrencyAlias currencyAlias)
			throws SwtException {
		log.debug("Entering into CurrencyAliasManagerImpl.saveCurrencyAliasDetails() method");
		try {
			dao.saveCurrencyAliasDetails(currencyAlias);
			log.debug("Exiting from CurrencyAliasManagerImpl.saveCurrencyAliasDetails() method");
		} catch (Exception exp) {
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCurrencyAliasDetails] method : - "
					+ exp.getMessage());
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveCurrencyAliasDetails", CurrencyAliasManagerImpl.class);
		}
	}

	/**
	 * This method is used to delete CurrencyAlias obejct from DB.
	 * 
	 * @param CurrencyAlias
	 *            currencyAlias
	 */
	public void deleteCurrencyAliasRecord(CurrencyAlias currencyAlias)
			throws SwtException {
		log
				.debug("Entering into CurrencyAliasManagerImpl.deleteCurrencyAliasRecord() method");
		try {
			dao.deleteCurrencyAliasRecord(currencyAlias);
			log.debug("Exiting from CurrencyAliasManagerImpl.deleteCurrencyAliasRecord() method");
		} catch (Exception exp) {
			log.debug("Exception Catch in CurrencyAliasManagerImpl.'deleteCurrencyAliasRecord' method : "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance()
					.handleException(exp, "deleteCurrencyAliasRecord",
							CurrencyAliasManagerImpl.class);
		}
	}
}
