
package org.swallow.maintenance.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.service.impl.ScenMaintenanceManagerImpl;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.AcctCcyPeriodMaintenanceDAO;
import org.swallow.maintenance.model.AccountCurrencyPeriodMaintenance;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.service.AcctCcyPeriodMaintenanceManager;
import org.swallow.util.LabelValueBean;
@Component("acctCcyPeriodMaintenanceManager")
public class AcctCcyPeriodMaintenanceManagerImpl implements AcctCcyPeriodMaintenanceManager {

	/**
	 * To create a Log factory reference variable.
	 */
	private static final Log log = LogFactory.getLog(AcctCcyPeriodMaintenanceManagerImpl.class);

	/**
	 * Default DAO object associated with this managerimpl class
	 */
	@Autowired
	private  AcctCcyPeriodMaintenanceDAO acctCcyPeriodMaintenanceDAO;

	/**
	 * @param AcctCcyPeriodMaintenanceDAO
	 *            The AcctCcyPeriodMaintenanceDAO to set.
	 */
	public void setAcctCcyPeriodMaintenanceDAO(AcctCcyPeriodMaintenanceDAO acctCcyPeriodMaintenanceDAO) {
		this.acctCcyPeriodMaintenanceDAO = acctCcyPeriodMaintenanceDAO;
	}
	
	/**
	 * This method is used to get Account Currency Maintenance Period records 
	 */
	public Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecords(String hostId, String entityId, String currencyCode, Date forDate, String show)
			throws SwtException{

		Collection<AccountCurrencyPeriodMaintenance> AcctCcyList = new ArrayList<AccountCurrencyPeriodMaintenance>();

		log.debug(this.getClass().getName() + " - getAcctCcyPeriodMaintRecords () - " + " Entry ");

		AcctCcyList = acctCcyPeriodMaintenanceDAO.getAcctCcyPeriodMaintRecords(hostId, entityId, currencyCode, forDate, show);
		return AcctCcyList;
	}
	
	/**
	 * This method is used to get Account Currency Maintenance Period records 
	 */
	public Collection<AccountCurrencyPeriodMaintenance> getAcctCcyPeriodMaintRecordsPerAccount(String hostId, String entityId, String accountId, Date forDate, String show)
			throws SwtException{

		Collection<AccountCurrencyPeriodMaintenance> AcctCcyList = new ArrayList<AccountCurrencyPeriodMaintenance>();

		log.debug(this.getClass().getName() + " - getAcctCcyPeriodMaintRecords () - " + " Entry ");

		AcctCcyList = acctCcyPeriodMaintenanceDAO.getAcctCcyPeriodMaintRecordsPerAccount(hostId, entityId, accountId, forDate, show);
		return AcctCcyList;
	}
	
	
	
	/**
	 * This method is used to get Accounts ids list for a given entity and currency
	 */
	
	public Collection getAccountIdDropDown(String hostId, String entityId, String currencyCode, String screenName)
			throws SwtException {
		Collection accountIdDropDown = new ArrayList();
		accountIdDropDown.add(new LabelValueBean("", ""));

		Collection records = acctCcyPeriodMaintenanceDAO.getAccountIdDropDown(hostId, entityId, currencyCode, screenName);

		if (records != null) {
			Iterator itr = records.iterator();

			while (itr.hasNext()) {
				AcctMaintenance am = (AcctMaintenance) (itr.next());
				accountIdDropDown.add(new LabelValueBean(am.getAcctname(), am
						.getId().getAccountId()));
			}
		}
		return accountIdDropDown;
	}
	
	/**
	 * This method is used to check if the Start-End Date period overlaps another existing record having the same host/entity/account
	 */
	
	public boolean checkIfOverlaps(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException {
		log.debug(this.getClass().getName() + " - checkIfOverlaps () - " + " Entry ");
		return acctCcyPeriodMaintenanceDAO.checkIfOverlaps(hostId, entityId, accountId, startDate, endDate);
	}
	
	/**
	 * This method is used to check if record is already exists
	 */	
	
	public boolean checkIfAcctCcyPeriodExists(String hostId, String entityId, String accountId, Date startDate, Date endDate)
			throws SwtException {
		log.debug(this.getClass().getName() + " - checkIfAcctCcyPeriodExists () - " + " Entry ");
		return acctCcyPeriodMaintenanceDAO.checkIfAcctCcyPeriodExists(hostId, entityId, accountId, startDate, endDate);
	}
	
	/**
	 * This method is used to save new account currency period in P_CCY_ACC_MAINT_PERIOD
	 */

	public void saveAcctCcyPeriodRecord(AccountCurrencyPeriodMaintenance acctCcyPeriod,String action) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [saveAcctCcyPeriodRecord] - " + "Entry");

			// Pass the scenario bean to the update method of DAO
			acctCcyPeriodMaintenanceDAO.saveAcctCcyPeriodRecord(acctCcyPeriod, action);

			log.debug(this.getClass().getName() + " - [saveAcctCcyPeriodRecord] - " + "Exit");

		} catch (Exception e) {
			log.debug(this.getClass().getName() + " - Exception Catched in [saveAcctCcyPeriodRecord] method : - "
					+ e.getMessage());

			log.error(this.getClass().getName() + " - Exception Catched in [saveAcctCcyPeriodRecord] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e, "saveAcctCcyPeriodRecord",
					ScenMaintenanceManagerImpl.class);
		}

	}
	

	
	/**
	 * This method is used to delete existing account currency period from  P_CCY_ACC_MAINT_PERIOD
	 */
	
	public void deleteAcctCcyPeriodMaintRecord(String hostId, String entityId, String accountId, Date startDate, Date endDate)
    		throws SwtException {
    	
    	try {
			log.debug(this.getClass().getName()
					+ "- [deleteAcctCcyPeriodMaintRecord] - Entry");
			acctCcyPeriodMaintenanceDAO
					.deleteAcctCcyPeriodMaintRecord(hostId, entityId, accountId, startDate, endDate);
			log.debug(this.getClass().getName()
					+ "- [deleteAcctCcyPeriodMaintRecord] - Exit");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [deleteAcctCcyPeriodMaintRecord] method : - "
					+ exp.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAcctCcyPeriodMaintRecord] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctCcyPeriodMaintRecord",
					ILMGeneralMaintenanceManagerImpl.class);
		}
    	
    }
	
	
	/**
	 * This method is used to get the list of records from  VW_S_MAINTENANCE_LOG according to given fromDate, endDate and reference
	 */
	
	public Collection getMaintenanceLogList(Date fromDate, Date toDate, String reference) throws SwtException {

		return acctCcyPeriodMaintenanceDAO.getMaintenanceLogList(fromDate, toDate, reference);


	}
	
	
	/**
	 * This method is used to get the list of records from  s_maintenance_log according to given hostId, userId, logDate, ipAddress, tableName, reference and action
	 */
	
	public Collection getViewLogDetails(String hostId, String userId, Date logDate, String ipAddress,
			String tableName, String reference, String action) throws SwtException {

		return acctCcyPeriodMaintenanceDAO.getViewLogDetails(hostId, userId, logDate, ipAddress, tableName, reference,
				action);
	}
	
	/**
	 * This method is used to get account currency code from p_account table
	 */
	public String getAccountCcy(String accountId) throws SwtException {
		log.debug(this.getClass().getName() + " - getAccountCcy () - " + " Entry ");
		return acctCcyPeriodMaintenanceDAO.getAccountCcy(accountId);
	}


}