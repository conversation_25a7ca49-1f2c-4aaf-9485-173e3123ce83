/*
 * @(#)NonWorkdayManager.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.NonWorkday;
import org.swallow.util.LabelValueBean;

/**
 * <AUTHOR> A
 * 
 * Manager layer for S_NON_WORKDAY_PARAMS
 */
public interface NonWorkdayManager {

	/**
	 * Method to get Facility list for given entity and hostId
	 * 
	 * @param entity
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<NonWorkday> getFacilityList(String entityId, String hostId)throws SwtException;
	
	/**
	 * Method to get list of non working days
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getDayList()throws SwtException;
	
	/**
	 * Method to save/ delete / update Non workday
	 * 
	 * @param nonWorkday
	 * @param saveStatus
	 * @return
	 * @throws SwtException
	 */
	public void save(NonWorkday nonWorkday,String saveStatus) throws SwtException;
}
