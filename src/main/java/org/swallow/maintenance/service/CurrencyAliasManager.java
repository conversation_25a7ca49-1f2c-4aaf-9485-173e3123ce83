/*
 * @(#)CurrencyAliasManager.java 01/08/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.service;
import java.util.Collection;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyAliasDAO;
import org.swallow.maintenance.model.CurrencyAlias;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.util.SystemInfo;
public interface CurrencyAliasManager {
	/**
    *
    * @param currencyGroupDAO - CurrencyGroupDAO object
    */
   void setCurrencyAliasDAO(CurrencyAliasDAO currencyAliasDAO);
   
   /**
   * This method is used to fetch currecnyAllias records from the DB
   * @param hostId - Host Id
   * @param entityId - Entity Id
   * @return Collection object
   * @throws SwtException - SwtException object
   */
   Collection getCurrencyAliasList(String hostId, String entityId) 
   throws SwtException;

   /**
    * This method is used to save CurrencyAlias obejct into DB.
    * @param  CurrencyAlias currencyAlias
    */
   public void saveCurrencyAliasDetails(CurrencyAlias currencyAlias)
   throws SwtException;

   /**
    * This method is used to delete CurrencyAlias obejct from DB.
    * @param  CurrencyAlias currencyAlias
    */
   public void deleteCurrencyAliasRecord(CurrencyAlias currencyAlias)
   throws SwtException;
}
