/*
 * @(#)LocationMaintenanceManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.Location;

public interface LocationMaintenanceManager {

	/**
	 * Returns the details of location
	 * 
	 * @param locationId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getLocationIdDetails(String locationId)
			throws SwtException;

	/**
	 * Returns the collection of location
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getLocationDetails(String hostId, String entityId)
			throws SwtException;

	/**
	 * Save the location details in to the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void saveLocationDetail(Location location) throws SwtException;

	/**
	 * Update the location details in to the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void updateLocationDetail(Location location) throws SwtException;

	/**
	 * Delete the location details from the data base through DAO
	 * 
	 * @param location
	 * @return
	 * @throws SwtException
	 */
	public void deleteLocationDetail(Location location) throws SwtException;

	/**
	 * Get the location id alone that access rights from the database
	 * 
	 * @param roleId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getLocationIdFromDB(String hostId, String roleId)
			throws SwtException;

	/**
	 * Method to collect location details for the given hostId
	 * 
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAllLocationDetails(String hostId) throws SwtException;

}
