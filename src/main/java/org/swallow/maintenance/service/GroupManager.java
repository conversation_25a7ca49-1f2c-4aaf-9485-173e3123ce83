/*
 * @(#)GroupManager.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.GroupDAO;
import org.swallow.maintenance.model.Group;

public interface GroupManager {
    /**
     * This is used to get the group details from P_GROUP table
     * @param entityId
     * @param hostId
     * @param grpLvlCode
     * @return Collection 
     * @throws SwtException
     */
	public Collection getGroupList(String entityId, String hostId,
	        Integer grpLvlCode)
	        throws SwtException;
               

    /**
     * This is used to get the meta group details from P_METAGROUP table
     * @param hostId
     * @param entityId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMetaGroupList(String hostId, String entityId)
        throws SwtException;

    
    /**
     * This is used to save the group details in P_GROUP table
     * @param group
     * @return 
     * @throws SwtException
     */
    public void saveGroupDetail(Group group) throws SwtException;

    /**
     * This is used to save the updated group details in P_GROUP table
     * @param group
     * @return 
     * @throws SwtException
     */
    public void updateGroupDetail(Group group) throws SwtException;

    /**
     * This is used to delete the group details from  P_GROUP table
     * @param group
     * @return 
     * @throws SwtException
     */
    public void deleteGroupDetail(Group group) throws SwtException;

    /**
     * This is used to set the Group DAO
     * @param groupDAO
     */
    public void setGroupDAO(GroupDAO groupDAO);
    /**
     * This is used to edit the required fields in DB
     * @param hostId
     * @param entityId
     * @param groupId
     * @return Group
     * @throws SwtException*/   
    public Group getEditableData(String hostId, String entityId,
        String groupId)
        throws SwtException;
    
   /**This is used in Entity DAOHibernate
    * @param hostId
    * @param entityId
    * @param metaLevel
    */
    public Collection getMetaGroupLevel(String hostId, String entityId,
        Integer metaLevel) throws SwtException;
}
