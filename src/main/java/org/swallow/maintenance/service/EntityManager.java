/*
 * Created on Nov 4, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.service;

import org.swallow.exception.SwtException;

import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.maintenance.model.Entity;

import org.swallow.util.SystemInfo;

import java.util.Collection;
import java.util.HashMap;

/**
 * EntityManager.java
 * 
 * EntityManager used to get the Entity details
 * 
 * 
 */
public interface EntityManager {
	// unnecessary code Removed by Arumugam on 03-Dec-2010 for Mantis:0001296-
	// Misc Param to added the entityId field in table and check the entityId
	/**
	 * 
	 * @param entity
	 * @return
	 * @throws SwtException
	 */
	public Entity getEntityDetail(Entity entity) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getCurrencyDetail(String hostId, String entityId)
			throws SwtException;

	public Collection getGroupLevel(String hostId, String entityId)
			throws SwtException;

	public Collection getMetaGroupLevel(String hostId, String entityId)
			throws SwtException;

	public void saveEntity(Entity entity, SystemInfo systemInfo)
			throws SwtException;

	/**
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Objects
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param posLevelName -
	 *            Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	void saveEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection posLevelName,
			Collection editableData) throws SwtException;

	public Collection getEntityList(String hostId) throws SwtException;

	/**
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Objects
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param collPosLevelName -
	 *            Collection of EntityPositionLevel Objects
	 * @throws SwtException -
	 *             SwtException
	 */
	void deleteEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection collPosLevelName,
			Collection collEditableFields) throws SwtException;

	/**
	 * 
	 * @param entity -
	 *            Entity Object
	 * @param groupLevel -
	 *            Collection of GroupLevel Object
	 * @param metaGroupLevel -
	 *            Collection of MetaGroupLevel Objects
	 * @param collPosLvlDeleted -
	 *            Collection of EntityPositionLevel Objects to be deleted
	 * @param collPosLvlAdded -
	 *            Collection of EntityPositionLevel Objects to be added
	 * @param collPosLvlChanged -
	 *            Collection of EntityPositionLevel Objects to be updated
	 * @throws SwtException -
	 *             SwtException
	 */
	void updateEntityDetails(Entity entity, Collection groupLevel,
			Collection metaGroupLevel, Collection collPosLvlDeleted,
			Collection collPosLvlAdded, Collection collPosLvlChanged,
			Collection collEditableData) throws SwtException;

	public void setEntityDAO(EntityDAO entityDAO);

	/**
	 * @desc This method fetches the collection of EntityPositionLevel Objects
	 *       for the given hostId and entityId
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @return Collection
	 * @throws SwtException -
	 *             SwtException
	 */
	Collection getPosLvlNameDetails(String hostId, String entityId)
			throws SwtException;

	public Collection getEditableDataDetails(String hostId, String entityId)
			throws SwtException;

	/*--END: CODE CHNAGED AS PER SRS_MOVEMENT_AMENDMENT_0.4.DOC ON 23-JULY-2007 BY AJESH SINGH--*/
	/* Start : Code added for NIB Central Bank Monitor by Kalidass on 18-03-2010 */
	/**
	 * This method is used to get the accountId list
	 * 
	 * @param currencyCode
	 *            String
	 * @param entityId
	 *            String
	 * @param currencyCode
	 *            String
	 * @return Collection
	 */
	public Collection getAccountList(String hostId, String entityId,
			String currencyCode) throws SwtException;

	/* End : Code added for NIB Central Bank Monitor by Kalidass on 18-03-2010 */
	/*
	 * START:Code Modified by Krishna on 31-MAR-2011 for Mantis 1244:Apply
	 * "Editable Fields" functionality for matching_party, product_type and
	 * posting_date
	 */
	/**
	 * This method is used to get the movement fields from Misc params table
	 * 
	 * @param hostId
	 *            String
	 * @param entityId
	 *            String
	 * @return Collection
	 */
	public Collection getMiscDataDetails(String hostId, String entityId)
			throws SwtException;
	/*
	 * END:Code Modified by Krishna on 31-MAR-2011 for Mantis 1244:Apply
	 * "Editable Fields" functionality for matching_party, product_type and
	 * posting_date
	 */
	
	public HashMap<String, String> getTimeZoneRegionsList() throws SwtException;
	
}