/*
 * @(#)MatchQulaityManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.MatchQualityDAO;
import org.swallow.maintenance.model.MatchQuality;
import org.swallow.maintenance.model.QualityAction;


public interface MatchQualityManager {
   
   //unnecessary code Removed by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId
	/**
	 * Set MatchQualityDAO
     * @param matchQualityDAO
     */
    public void setMatchQualityDAO(MatchQualityDAO matchQualityDAO);

    /**
     * Collect the Match list from DAO and return to Action
     * @param entityId
     * @param hostId
     * @return Collection
     * @throws SwtException
     */
    public Collection getMatchList(String entityId, String hostId)
        throws SwtException;

    

    /**
     * Get Parameter Description from DAO and return the collection
     * @return Collection
     * @throws SwtException
     */
    public Collection getParamsDescAll() throws SwtException;

    /**
     * pass the matchQuality bean to the matchQualityDAO
     * @param collNatchList
     * @param qualityActionObj
     * @return None
     * @throws SwtException
     */
    public void addMatchList(Collection collNatchList,
        MatchQuality qualityActionObj) throws SwtException;

    /**
     * Update the Match Quality list through DAO
     * @param collNatchList
     * @param qualityActionObj
     * @return None
     * @throws SwtException
     */
    public void updateMatchList(Collection collNatchList,
        MatchQuality qualityActionObj) throws SwtException;

    /**
     * Collect the match QualityList from MatchQualityDAO and returns the
	 * MatchQuality bean
     * @param hostId
     * @param entityId
     * @param currencyCode
     * @param posLevel
     * @return MatchQuality
     * @throws SwtException
     */
    public MatchQuality getMatchQualityList(String hostId, String entityId,
        String currencyCode, Integer posLevel) throws SwtException;

    /**
     * Collects the Quality action from QualityActionDAO and return the
	 * collection
     * @param hostId
     * @param entityId
     * @param currencyCode
     * @param posLevel
     * @return Collection
     * @throws SwtException
     */
    public Collection getQualityAction(String hostId, String entityId,
        String currencyCode, Integer posLevel) throws SwtException;

    /**
     * Delete the MatchQuality through DAO
     * @param matchQualityObj
     * @param qualityActionObj
     * @return None
     * @throws SwtException
     */
    public void deleteMatchQuality(MatchQuality matchQualityObj,
        QualityAction qualityActionObj) throws SwtException;


    /**
     * Get the poition level value and name from DAO
     * @param hostId
     * @param entityId
     * @param currencyCode
     * @return Collection
     * @throws SwtException
     */
    public Collection getfilterPositionlevel(String hostId, String entityId,
        String currencyCode)
        throws SwtException;
}
