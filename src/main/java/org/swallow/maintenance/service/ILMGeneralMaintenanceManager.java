/*
 * @(#)ILMGeneralMaintenanceManager.java 1.0 29/11/13
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CcyProcessStatus;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMParams;
import org.swallow.model.AccountQueryResult;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;

public interface ILMGeneralMaintenanceManager {
	
	/**
	 * Used to get the list of account groups with details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param request
	 * @return ArrayList
	 * @throws SwtException
	 */
	public ArrayList getAccountGroupsDetails(String hostId,String entityId,String currencyCode,HttpServletRequest request) 
			throws SwtException;
	
	/**
	 * Used to get only the list of account groups which contains only the name and id of account groups
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountGroupsList(String hostId ,String entityId,String currencyCode) throws SwtException ;
	/**
	 * Used to get only the list of account groups which contains only the name and id of account groups
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountGroupsListSQL(String hostId ,String entityId,String currencyCode, boolean throuputOnly) throws SwtException ;
	/**
	 * Used to get only the list of account groups which contains only the name and id of account groups
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public List<LabelValueBean> getAccountInGroupListSQL(String hostId, String entityId,String currencyCode,
			String ilmAccountGroup) throws SwtException ;
	
	/**
	 * Used to get the list of accounts with details
	 * 
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountListDetails(String entityId,String currencyCode) throws SwtException ;

	/**
	 * Get the account query result though DB
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param query
	 * @return AccountQueryResult
	 * @throws SwtException
	 */
	public AccountQueryResult getAccountsQueryResult(String hostId, String entityId,String currencyCode, String query) throws SwtException ;

	/**
	 * Update the account group details through DAO
	 * 
	 * @param group
	 * @throws SwtException
	 */
	public void updateAccountGroupDetails(ILMAccountGroup group) throws SwtException;
	
	/**
	 * Save account group details Through DAO
	 * 
	 * @param group
	 * @throws SwtException
	 */
	public void saveAccountGroupDetails(ILMAccountGroup group)	throws SwtException ;
	
	/**
	 * Save account group sub account details Through DAO
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountGroupId
	 * @param accountToAdd
	 * @param accountToDelete
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean saveAccountGroupSubAccounts(String hostId, String entityId, String accountGroupId, String accountToAdd, String accountToDelete) throws SwtException ;

	/**
	 * Get the Editable fields of a account group ID
	 * 
	 * @param accountGroupId
	 * @return ILMAccountGroup
	 * @throws SwtException
	 */
	public ILMAccountGroup getEditableDataDetailList(String accountGroupId) throws SwtException;
	
	/**
	 * This is used to delete the account details
	 * 
	 * @param accountGroup
	 * @return
	 * @exception SwtException
	 */
	public void deleteAccountDetails(ILMAccountGroup accountGroup) throws SwtException ;
	
	/**
	 * This is a manager layer methods and used to get the ILM parameter details. 
	 *
	 * @param hostId
	 * @return ILMParams
	 * @throws SwtException
	 */
	public ILMParams getIlmParamsDetails(String hostId) throws SwtException;
	
	/**
	 * This is a manager layer methods and used to update the ILM parameter details.
	 *  
	 * @param ilmParams
	 * @throws SwtException
	 */
	public void updateIlmParameter(ILMParams ilmParams)throws SwtException;
	
	/**
	 * Used to get ILM Currency Parameters Details 
	 *  
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @return Collection
	 */
	public Collection<ILMCcyParameters> getILMCcyParametersDetails(String hostId, String roleId , String entityId);
	
	/**
	 * Used to save ILM Currency Parameters Details 
	 * 
	 * @param ilmCcyParameters
	 * @throws SwtException
	 */
	public void saveILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException;
	
	/**
	 * Used to update ILM Currency Parameters Details
	 *  
	 * @param ilmCcyParameters
	 * @throws SwtException
	 */
	public void updateILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException;
	
	/**
	 * Get ILM currency object for edit
	 *  
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return ILMCcyParameters
	 * @throws SwtException
	 */
	public ILMCcyParameters getILMCcyParameterEditableData(String hostId,
			String entityId, String currencyCode)throws SwtException;
	
	/**
	 * Used to delete ILM Currency Parameters Object
	 *  
	 * @param ilmCcyParameters
	 * @throws SwtException
	 */
	public void deleteILMCcyParametersDetails(ILMCcyParameters ilmCcyParameters) throws SwtException;
	/**
	 * Used to validate the filter condition clause 
	 * @param filterCondition
	 * @return
	 * @throws SwtException
	 */
	public String getFilterConditionResult(String filterCondition)throws SwtException;
	
	/**
	 * Get list of public ILM Account Groups
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public Collection getPublicAccountGroupsList(String hostId ,String entityId,String currencyCode) throws SwtException ;

	public String getGlobalGroup(String entityId, String currencyId) throws SwtException;
	
	public boolean getCurrencyGMTOffset(String hostId, String entityId, String currencyId) throws SwtException;

	public Collection getAllowedAccountGroupsListForUser(String hostId,
			String userId, String entityId, String currencyCode, String reportType) throws SwtException;
	/**
	 * Get the list of account group that they have allowReporting="Y"
	 * @param entityId
	 * @param hostId
	 * @param  currencyCode
	 * @return list
	 * @throws SwtException
	 */
	public Collection getAllowedReportAccountGroupList(
			String hostId, String entityId, String currencyCode) throws SwtException;
	/**
	 * Get only entities those are both present in the user's role-access rights and in P_ILM_CCY_PARAMETERS.
	 * @param hostId
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAllowedEntityList(String hostId, String roleId) throws SwtException;
	
	/**
	 * return the currencies that are both present in the user's role-access rights and in P_ILM_CCY_PARAMETERS.
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public Collection getAllowedCurrencyList(String hostId, String roleId, String entityId) throws SwtException;
	/**
	 * 
	 * @param dateFormat
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param userId
	 * @param startDate
	 * @param endDate
	 * @param overwrite
	 * @param processOption
	 * @return
	 * @throws SwtException
	 */
	public Object[] getccyProcessStatusDetails(
			String dateFormat, String hostId, String entityId,
			String currencyCode, String userId, Date startDate, Date endDate,
			String overwrite, String processOption) throws SwtException;
	/**
	 *  Used to launch the recalculation of ILM data for reports and screens
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param userId
	 * @param startDate
	 * @param endDate
	 * @param overwrite: determines should existing data be overwritten or only the gaps must be filled
	 * @param processOption :determines which processes should run.('A' to run all process or 'M' to update the data for screens only)
	 * @param sequenceNumber 
	 * @param CDM 
	 * @return
	 * @throws SwtException
	 */
	public String runManualCurrencyProcess(String hostId, String entityId,
			String currencyCode, String userId, Date startDate,
			Date endDate, String overwrite, String processOption, String sequenceNumber, CommonDataManager CDM)
			throws SwtException;
	
	/**
	 * Must called after each cancel operation to update  process status 
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param userId
	 * @param startDate
	 * @param endDate
	 * @param overwrite
	 * @param processOption
	 * @param proccessStatus
	 * @return
	 * @throws SwtException
	 */
	public void updateProcessStatus(String hostId, String entityId,
			String currencyCode, String userId, Date startDate,
			Date endDate,  String processOption, String proccessStatus)
					throws SwtException;

	
	/**
	 * Called to get the Account Name
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 */
	public String getAccountName(String hostId,String entityId,
		   String accountId)
		   throws SwtException;
	/**
	 * Determine if an ILM group is referenced by a P_ILM_CCY_PARAMETERS record as CENTRAL_BANK_GROUP_ID 
	 * @param groupId
	 * @return
	 * @throws SwtException
	 */
	public boolean isCentralCcyGroup(String groupId) throws SwtException ;
	
	/**
	 * Checks for actually running processes
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param userId
	 * @param startDate
	 * @param endDate
	 * @param processOption
	 * @return
	 */
	public String checkRunningProcesses(String hostId, String entityId,
			String currencyCode, String userId, Date startDate,
			Date endDate, String processOption, HttpServletRequest request) throws SwtException;
	
	/**
	 * Return if the selected currency has an central bank
	 * @param hostId
	 * @param userId
	 * @param entityId
	 * @param currencyCode
	 * @return
	 * @throws SwtException
	 */
	public boolean checkCentralBankGroupExists(String hostId,
			String userId, String entityId, String currencyCode) throws SwtException;
	
	/**
	 * get all ilm account gruoup 
	 * @param hostId
	 * @param entityId
	 * @param accounId
	 */
	public Collection <ILMAccountGroup>  getAllAccountGroupsList(String hostId) throws SwtException ;
	
	public  ILMCcyParameters getGlobalAlternativeAccount(String hostId,String entityId, String currencyCode, String groupId) throws SwtException;
}
