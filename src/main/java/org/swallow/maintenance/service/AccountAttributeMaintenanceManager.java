package org.swallow.maintenance.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountAttribute;
import org.swallow.maintenance.model.AccountAttributeFuncGroup;
import org.swallow.maintenance.model.AccountAttributeHDR;
import org.swallow.maintenance.model.FunctionalGroup;

public interface AccountAttributeMaintenanceManager {
	/**
	 *  Save an Account Attribute
	 * @param acctAttr
	 * @throws SwtException
	 */
	public void saveAcctAttribute(AccountAttribute acctAttr)throws SwtException;
	/**
	 * Get an Account Attribute by giving a specific criteria 
	 * @param hostId
	 * @param entityId
	 * @param accountId
	 * @param attributeId
	 * @param effectiveDate
	 * @return
	 * @throws SwtException
	 */
	public AccountAttribute getAcctAttribute(String sequenceId)throws SwtException;
	/**
	 * Update an Account Attribute
	 * @param accountAttribute
	 * @throws SwtException
	 */
	public void updateAcctAttribute(AccountAttribute accountAttribute)throws  SwtException;
	/**
	 * Delete an Account Attribute
	 * @param accountAttribute
	 * @throws SwtException
	 */
	public void deleteAcctAttribute(AccountAttribute accountAttribute)throws SwtException;
		
	/**
	 * Retrieve list of account attributes HDR
	 * @param params
	 * @return Collection<AccountAttributeHDR>
	 * @throws SwtException
	 */
	public Collection<AccountAttributeHDR> getAcctAttributHDRDetailList(String... params) throws SwtException;
	
	/**
	 * Save an account attribute HDR
	 * @param acctAttrHdr
	 * @throws SwtException
	 */
	public void saveAccountAttributeHDR(AccountAttributeHDR acctAttrHdr) throws SwtException;
	
	/**
	 * Get Account Attribute HDR
	 * @param attributeId
	 * @throws SwtException
	 * @return AccountAttributeHDR
	 */
	public AccountAttributeHDR getAccountAttributeHDR(String attributeId)throws SwtException; 
	
	/**
	 * Update an Account Attribute HDR
	 * @param acctAttrHdr
	 * @throws SwtException
	 */
	public void updateAccountAttributeHDR(AccountAttributeHDR acctAttrHdr) throws SwtException;
	
	/**
	 *  Delete an account attribute HDR
	 * @param acctAttrHdr
	 * @param isCascade
	 * @throws SwtException
	 */
	public void deleteAccountAttributeHDR(AccountAttributeHDR acctAttrHdr, boolean isCascade) throws SwtException;
	

	/**
	 * Returns the list of account Attribute functional group by giving as specific functinal group
	 * @param functionalGrp
	 * @param restriction
	 * @return ArrayList<AccountAttributeFuncGroup>
	 * @throws SwtException
	 */
	public ArrayList<AccountAttributeFuncGroup> getAccountAttributeFuncGroupList(String functionalGrp, String restriction) throws SwtException;

	/**
	 * Returns the list of functional groups
	 * @return ArrayList<FunctionalGroup>
	 * @throws SwtException
	 */
	public ArrayList<FunctionalGroup> getFunctionalGrpList() throws SwtException;

	/**
	 * Get a specific account attribute functional group by giving its attribute Id and its functional Group 
	 * @param attributeId
	 * @param functionalGrp
	 * @return AccountAttributeFuncGroup
	 * @throws SwtException
	 */
	public AccountAttributeFuncGroup getAccntAttributeFunctGroup(String attributeId, String functionalGrp) throws SwtException;

	/**
	 * Delete a specific account attribute functional group
	 * @param accntAttributeFuncGrp
	 * @throws SwtException
	 */
	public void deleteAccntAttributeFunctionalGrp(AccountAttributeFuncGroup accntAttributeFuncGrp) throws SwtException;
	
	/**
	 * Save new attribute usage summary object
	 * @param acctAttrFuncGrp
	 * @throws SwtException 
	 */
	public void saveAttributeUsageSummary(AccountAttributeFuncGroup acctAttrFuncGrp) throws SwtException;

	/**
	 * Update attribute usage summary details 
	 * @param acctAttrFuncGrp
	 * @throws SwtException
	 */
	public void updateAttributeUsageSummary(AccountAttributeFuncGroup acctAttrFuncGrp) throws SwtException;
	
	public Collection getAccountAttributeList(String hostId, String entityId, String currencyCode, String accountId, String attributeId, Date dateFrom, Date dateTo) throws SwtException;
	
	/**
	 * Returns a collection of open account label-value beans for use in select elements and the like
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getAccountList (String hostId, String entityId, String currencyCode) throws SwtException;
	
	/**
	 * Returns a list of accounts attributes, each with the most recent effective date
	 * @param entityId
	 * @param accountId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<AccountAttribute> getLastValuesAccountAttrList(String entityId, String accountId, String hostId) throws SwtException;
	
	/**
	 * Get a list of accounts attributes by attributeId
	 * @param attributeId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<AccountAttribute> getAccountAttributeList(String attributeId)  throws SwtException;
}
