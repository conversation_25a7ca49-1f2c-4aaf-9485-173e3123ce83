package org.swallow.maintenance.service;

import java.util.Collection;

import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CorrespondentAcct;
import org.swallow.util.LabelValueBean;

public interface CorrespondentAcctMaintenanceManager {

	/**
	 * Asks the DAO for a list of CorrespondentAcct's matching the given
	 * entitiyId and hostId
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getList(String entityId, String hostId, int startRowNumber, int endRowNumber, String messageType, String curencyCode, String userId, String selectedSort)
			throws SwtException;

	/**
	 * Asks the DAO to delete the given CorrespondentAcct
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void deleteCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException;

	/**
	 * Asks the DAO for the record matching the PK data stored in
	 * CorrespondentAcct parameter
	 * 
	 * @param acct
	 * @return CorrespondentAcct
	 * @throws SwtException
	 */
	public CorrespondentAcct getCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException;

	/**
	 * Returns a collection of open account label-value beans for use in select
	 * elements and the like
	 * 
	 * @param hostId
	 * @param entityId
	 * @param messageType
	 * @param currencyCode
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCorrespondentAcctList(String hostId, String entityId,
			String messageType, String currencyCode) throws SwtException;

	/**
	 * Asks the DAO to save a new copy of the given CorrespondentAcct object
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void saveCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException;

	/**
	 * Checks that the given form bean contains a valid accountId and if it
	 * does, asks the DAO to update it
	 * 
	 * @param acct
	 * @throws SwtException
	 */
	public void updateCorrespondentAcct(CorrespondentAcct acct)
			throws SwtException;

	/**
	 * This method is basically used to retrieve List of Account Ids for a given
	 * inputs.
	 * 
	 * @param hostId
	 * @return List of Account Ids
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getAccountIdDropDown(String hostId, String entityId,
			String currencyCode, String roleId) throws SwtException;
	/**
	 * 
	 * @return list 
	 * @throws SwtException
	 */
	public Collection<String> getMessageTypesDropdownValues() throws SwtException;
}
