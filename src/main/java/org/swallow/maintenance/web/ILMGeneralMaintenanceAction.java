/*
 * @(#)ILMGenralMaintenanceAction.java 1.0 29/11/13
 *
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.sql.CallableStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.jdbc.support.JdbcUtils;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Shortcut;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.SwtDynamicReportImpl;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CcyProcessStatus;
import org.swallow.maintenance.model.CurrencyTO;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMAccountGroupMaintenance;
import org.swallow.maintenance.model.ILMAccountInGroups;
import org.swallow.maintenance.model.ILMCcyParameters;
import org.swallow.maintenance.model.ILMParams;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.maintenance.web.form.ILMCalculationForm;
import org.swallow.model.AccountQueryResult;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.OpTimer;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.XSSUtil;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;



























import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/intraDayLiquidity", "/intraDayLiquidity.do"})
public class ILMGeneralMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("secondgriddetails", "jsp/maintenance/ilmaccountsecondgridflexdata");
		viewMap.put("displaygeneralprams", "jsp/maintenance/ilmgeneralparameters");
		viewMap.put("changeCurrency", "jsp/maintenance/ilmcurrencyparametersadd");
		viewMap.put("viewCurrency", "jsp/maintenance/ilmcurrencyparametersadd");
		viewMap.put("addCurrency", "jsp/maintenance/ilmcurrencyparametersadd");
		viewMap.put("detailsdataerror", "jsp/maintenance/ilmaccountgroupflexdataerror");
		viewMap.put("listAccountGroups", "jsp/maintenance/ilmaccountgroups");
		viewMap.put("groupdetails", "jsp/maintenance/ilmaccountgroupdetailsflexdata");
		viewMap.put("groupdetailsflex", "jsp/maintenance/ilmaccountgroupdetailsflex");
		viewMap.put("fail", "error");
		viewMap.put("updateCurrency", "jsp/maintenance/ilmcurrencyparametersadd");
		viewMap.put("ilmcalculation", "jsp/control/ilmcalculationlauncher");
		viewMap.put("success", "jsp/maintenance/ilmaccountgroups");
		viewMap.put("listCurrencyParams", "jsp/maintenance/ilmcurrencyparameters");
		viewMap.put("saveaccountgroup", "jsp/flexstatechange");
		viewMap.put("ccyprocessstatus", "jsp/control/ilmccyprocessstatus");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");

	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		if (!SwtUtil.isEmptyOrNull(method)) {
			method = method.split(",")[0].trim();
		}
		switch (method) {
			case "listAccountGroups":
				return listAccountGroups();
			case "getAccessForILMGroup":
				return getAccessForILMGroup();
			case "deleteAccountGroup":
				return deleteAccountGroup();
			case "displayGeneralPrams":
				return displayGeneralPrams();
			case "changeGeneralPrams":
				return changeGeneralPrams();
			case "updateGeneralPrams":
				return updateGeneralPrams();
			case "listCurrencyParams":
				return listCurrencyParams();
			case "addCurrency":
				return addCurrency();
			case "changeCurrency":
				return changeCurrency();
			case "viewCurrency":
				return viewCurrency();
			case "getCurrencyGMTOffset":
				return getCurrencyGMTOffset();
			case "saveCurrency":
				return saveCurrency();
			case "updateCurrency":
				return updateCurrency();
			case "deleteCurrency":
				return deleteCurrency();
			case "checkILMCurrencyAccess":
				return checkILMCurrencyAccess();
			case "accountGroupDetailsFlex":
				return accountGroupDetailsFlex();
			case "getFilterConditionTestResult":
				return getFilterConditionTestResult();
			case "getAccountGroupDetails":
				return getAccountGroupDetails();
			case "getAccountListDetails":
				return getAccountListDetails();
			case "exportAccountGrpDetails":
				return exportAccountGrpDetails();
			case "saveAccountGroupdetails":
				return saveAccountGroupdetails();
			case "updateAccountGroupdetails":
				return updateAccountGroupdetails();
			case "getGlobalGroup":
				return getGlobalGroup();
			case "ilmCalculation":
				return ilmCalculation();
			case "listCurrencyProcessStatus":
				return listCurrencyProcessStatus();
			case "runManualCCYProcess":
				return runManualCCYProcess();
			case "cancelManualCCYProcess":
				return cancelManualCCYProcess();
			case "getUserAccess":
				return getUserAccess();
		}


		return null;
	}


	private ILMParams ilmParams;
	public ILMParams getIlmParams() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmParams = RequestObjectMapper.getObjectFromRequest(ILMParams.class, request);
		return ilmParams;
	}

	public void setIlmParams(ILMParams ilmParams) {
		this.ilmParams = ilmParams;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmParams", ilmParams);
	}


	private ILMCalculationForm ilmCalcul;
	public ILMCalculationForm getIlmCalcul() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmCalcul = RequestObjectMapper.getObjectFromRequest(ILMCalculationForm.class, request);
		return ilmCalcul;
	}

	public void setIlmCalcul(ILMCalculationForm ilmCalcul) {
		this.ilmCalcul = ilmCalcul;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmCalcul", ilmCalcul);
	}


	ILMAccountGroupMaintenance intraDayMaintenanceAccountGroups;


	public ILMAccountGroupMaintenance getIntraDayMaintenanceAccountGroups() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		intraDayMaintenanceAccountGroups = RequestObjectMapper.getObjectFromRequest(ILMAccountGroupMaintenance.class, request);
		return intraDayMaintenanceAccountGroups;
	}

	public void setIntraDayMaintenanceAccountGroups(ILMAccountGroupMaintenance intraDayMaintenanceAccountGroups) {
		this.intraDayMaintenanceAccountGroups = intraDayMaintenanceAccountGroups;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("intraDayMaintenanceAccountGroups", intraDayMaintenanceAccountGroups);
	}



	private ILMCcyParameters ilmCcyParams;

	public ILMCcyParameters getIlmCcyParams() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmCcyParams = RequestObjectMapper.getObjectFromRequest(ILMCcyParameters.class, request,"ilmCcyParams");
		return ilmCcyParams;
	}

	public void setIlmCcyParams(ILMCcyParameters ilmCcyParams) {
		this.ilmCcyParams = ilmCcyParams;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmCcyParams", ilmCcyParams);
	}



	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}

	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}


	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(ILMGeneralMaintenanceAction.class);

	/**
	 * Initializing OpTimer object
	 */
	private OpTimer opTimer = new OpTimer();
	private final String menuItemId = "" + SwtConstants.SCREEN_ACCTGROUPDETAILS;

	/**
	 * Used to hold ILMGeneralMaintenanceManager reference object
	 */
	@Autowired
	private ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = null;

	/**
	 * @param ilmGeneralMaintenanceManager
	 *            the ilmGeneralMaintenanceManager to set
	 */
	public void setIlmGeneralMaintenanceManager(
			ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager) {
		this.ilmGeneralMaintenanceManager = ilmGeneralMaintenanceManager;
	}

	/**
	 * This method gets entity list from database and put them in request scope
	 *
	 * @param request
	 *            HttpServletRequest request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		// entity collection
		Collection entityColl = null;
		Entity entity = null;
		Iterator itr = null;
		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			// Convert the entity list to label value bean.
			entityColl = SwtUtil.convertEntityAcessCollectionLVL(entityColl,
					request.getSession());

			// Set the entity collection into request.
			request.setAttribute("entities", entityColl);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			entityColl = null;
		}
	}

	private void putEntityFullAccessListInReq(HttpServletRequest request)
			throws SwtException {
		putEntityFullAccessListInReq(request, true);
	}

	private void putEntityFullOrViewAccessListInReq(HttpServletRequest request)
			throws SwtException {
		putEntityFullAccessListInReq(request, false);
	}

	private void putEntityFullAccessListInReq(HttpServletRequest request, boolean fullOnly)
			throws SwtException {
		// entity collection
		Collection entityColl = null;
		Collection<EntityUserAccess> entityFullAccess = new ArrayList<EntityUserAccess>();
		Collection<LabelValueBean> colEntityLVB = null;
		EntityUserAccess entity = null;
		Iterator itr = null;
		boolean checkAccess = false;
		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			// Convert the entity list to label value bean.
			itr = entityColl.iterator();
			while (itr.hasNext()) {

				entity = (EntityUserAccess) itr.next();
				if (fullOnly == true) {
					checkAccess = entity.getAccess() == SwtConstants.ENTITY_FULL_ACCESS;
				}else {
					checkAccess = entity.getAccess() == SwtConstants.ENTITY_FULL_ACCESS || entity.getAccess() == SwtConstants.ENTITY_READ_ACCESS;
				}
				if (checkAccess) {
					entityFullAccess.add(entity);

				}
			}

			colEntityLVB = SwtUtil.convertEntityAcessCollectionLVL(
					entityFullAccess, request.getSession());
			// Set the entity collection into request.
			request.setAttribute("entities", colEntityLVB);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityFullAccessListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			entityColl = null;
			colEntityLVB = null;
		}
	}

	/**
	 * Method to define the Button status in the screen
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param viewStatus
	 * @param cancelStatus
	 * @param deleteStatus
	 * @param roleStatus
	 * @param saveStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String viewStatus, String cancelStatus,
								 String deleteStatus, String roleStatus, String saveStatus) {

		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.VIEW_BUT_STS, viewStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.ROLL_BUT_STS, roleStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);

		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/**
	 * Default method of the action returns List of account groups
	 * @return ActionForward
	 * @throws Exception
	 */
	public String unspecified()
			throws Exception {
		log.debug(this.getClass().getName() + " - [unspecified] - "
				+ "Return list Action");
		return listAccountGroups();
	}

	/**
	 * List the account groups from database
	 * @return ActionForward
	 * @throws Exception
	 */
	public String listAccountGroups() throws Exception {

		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		ArrayList accountGroupsDetail;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ILMAccountGroupMaintenance ilmAccountGroupsMaintenance= null;
		try {
			log.debug(this.getClass().getName() + " - [listAccountGroups] - "
					+ "Entry");


			ilmAccountGroupsMaintenance = getIntraDayMaintenanceAccountGroups();



			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			// Retrieve Entity Id
			entityId = ilmAccountGroupsMaintenance.getId().getEntityId();
			// Retrieve Currency Code
			currencyCode = ilmAccountGroupsMaintenance.getId()
					.getCurrencyCode();

			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				// get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			// Get the account groups detail list
			accountGroupsDetail = ilmGeneralMaintenanceManager
					.getAccountGroupsDetails(hostId, entityId, currencyCode,
							request);

			// Get the access of the currency for the user
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			ilmAccountGroupsMaintenance.getId().setEntityId(entityId);
			ilmAccountGroupsMaintenance.getId().setCurrencyCode(currencyCode);

			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, true));

			request.setAttribute("ilmAccountGroupsDetails", accountGroupsDetail);
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedCurrencyCode", currencyCode);

			/*
			 * Pass the Request parameter to set MiscParams of alert to the
			 * request attribute
			 */
			putEntityListInReq(request);
			setIntraDayMaintenanceAccountGroups(ilmAccountGroupsMaintenance);
			log.debug(this.getClass().getName() + " - [listAccountGroups] - "
					+ "Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [listAccountGroups] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [listAccountGroups] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"listAccountGroups",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			hostId = null;
			entityId = null;
			currencyCode = null;
		}
	}

	/**
	 * Used to check if the user is able to maintain any ILM group
	 * @return
	 * @throws SwtException
	 */
	public String getAccessForILMGroup() throws SwtException {

		String entityId = null;
		String currencyCode = null;
		String userId = null;
		String currentUserId = null;
		String hostId = null;
		String publicPrivate = null;
		int accessInd = 0;
		boolean access = false;
		boolean maintainAnyILMGroup = false;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					+ "- [getAccessForILMGroup] - Enter");
			currencyCode = request.getParameter("currencyCode");
			entityId = request.getParameter("entityId");
			userId = request.getParameter("userId");
			publicPrivate = request.getParameter("publicPrivate");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve current User ID
			currentUserId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId()
					.getUserId();
			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entityId,
					currencyCode);
			if (accessInd == 0) {
				maintainAnyILMGroup = SwtUtil
						.getMaintainAnyGroupILMAccess(request);
				if (maintainAnyILMGroup)
					access = true;
				else {
					if (!SwtUtil.isEmptyOrNull(currentUserId)
							&& !SwtUtil.isEmptyOrNull(userId)) {
						if (currentUserId.equals(userId)
								&& publicPrivate.equalsIgnoreCase("PRIVATE"))
							access = true;
					} else
						access = false;
				}

			}

			response.getWriter().print(access ? 0 : 1);
			log.debug(this.getClass().getName()
					+ " - [getAccessForILMGroup] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccessForILMGroup] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getAccessForILMGroup",
							ILMGeneralMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
			// Nullify Objects
			entityId = null;
			currencyCode = null;
			userId = null;
			currentUserId = null;
			hostId = null;
		}
	}

	/**
	 * This function is used to delete the scenario details
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String deleteAccountGroup() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String accountGroupId = null;
		ILMAccountGroup accountGroup;
		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		ArrayList accountGroupsDetail;
		ILMAccountGroupMaintenance ilmAccountGroupsMaintenance= null;
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteAccountGroup] - Entering");
			accountGroupId = request.getParameter("selectedAccountGroup");
			accountGroup = ilmGeneralMaintenanceManager
					.getEditableDataDetailList(accountGroupId);

			// Delete scenario details
			ilmGeneralMaintenanceManager.deleteAccountDetails(accountGroup);



			ilmAccountGroupsMaintenance = getIntraDayMaintenanceAccountGroups();

			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			// Retrieve Entity Id
			entityId = ilmAccountGroupsMaintenance.getId().getEntityId();
			// Retrieve Currency Code
			currencyCode = ilmAccountGroupsMaintenance.getId()
					.getCurrencyCode();
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				// get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}

			// Collect the Scenario list in scenarioMaintenanceDetailVO
			accountGroupsDetail = ilmGeneralMaintenanceManager
					.getAccountGroupsDetails(hostId, entityId, currencyCode,
							request);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			ilmAccountGroupsMaintenance.getId().setEntityId(entityId);
			ilmAccountGroupsMaintenance.getId().setCurrencyCode(currencyCode);

			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, true));

			request.setAttribute("ilmAccountGroupsDetails", accountGroupsDetail);
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedCurrencyCode", currencyCode);

			/*
			 * Pass the Request parameter to set MiscParams of alert to the
			 * request attribute
			 */
			putEntityListInReq(request);
			setIntraDayMaintenanceAccountGroups(ilmAccountGroupsMaintenance);
			// Put the required attribute in the request
			request.setAttribute("methodName", "listAccountGroups");
			log.debug(this.getClass().getName()
					+ "- [deleteAccountGroup] - Exiting");
			return getView("listAccountGroups");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAccountGroup] method : - "
					+ swtexp.getMessage());
			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			request.setAttribute("methodName", "list");
			log.debug(this.getClass().getName()
					+ "- [deleteAccountGroup] - Exiting");
			return getView("success");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAccountGroup] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteAccountGroup",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullify objects
			accountGroupId = null;
			accountGroup = null;
			hostId = null;
			entityId = null;
			currencyCode = null;
			accountGroupsDetail = null;
		}
	}

	/**
	 * Given a collection of label-value beans, returns the 'label' from the
	 * first occurrence with a 'value' matching the given 'value' parameter
	 *
	 * @param coll
	 * @param value
	 * @return String
	 */
	private String getLabelFromValue(Collection<LabelValueBean> coll,
									 String value) {
		log.debug("entering 'getLabelFromValue' method");
		if (coll != null) {
			Iterator<LabelValueBean> itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				if (lvb.getValue().equals(value))
					return lvb.getLabel();
			}
		}
		log.debug("exiting 'getLabelFromValue' method");
		return null;
	}

	/**
	 *
	 * This function returns collection of currencies
	 * @return - collection of currencies
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request,
									   String hostId, String entityId, boolean withAll)
			throws SwtException {

		// Role Id
		String roleId = "";
		// Currency List
		ArrayList ccyList = null;
		// Currency List with All option
		Collection currrencyList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyList ] - Entry ");
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Returns the currency Access List based on the Role
			ccyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);
			// Check for ccy List not NULL
			if (ccyList != null) {
				/*
				 * Removes the LabelValueBean object for the Key as 'Default'
				 * from the collection
				 */
				ccyList.remove(new LabelValueBean("Default", "*"));
				// Assigning the new ArrayList object to a new Collection Object
				currrencyList = new ArrayList();
				/*
				 * Adding a new LabelValueBean object with the Key as 'ALL' and
				 * value as 'ALL'
				 */
				if (withAll)
					currrencyList.add(new LabelValueBean(
							SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));

				// Adding the currencyList object to collection object
				currrencyList.addAll(ccyList);
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyList ] - Exit ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyList] - Exception -" + exp.getMessage());
		} finally {
			// nullify objects
			roleId = null;
			ccyList = null;
		}
		return currrencyList;
	}

	/**
	 * This method calls the Manager class to get the ILAAP General Parameters
	 * details and forwards the request to display it.
	 * @return
	 * @throws SwtException
	 */
	public String displayGeneralPrams() throws SwtException {

		// Variable to hold the sysParams object
		ILMParams ilmParams = null;
		// String Variable to hold the hostId
		String hostId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName()
					+ " - [displayGeneralPrams] - Enter");
			// Setting screenfield status in request
			request.setAttribute("screenFieldsStatus", "true");

			// Getting hostid from request
			hostId = getHostIdListFromReq(request);
			// Getting ILAAP parameter details from manager class
			ilmParams = ilmGeneralMaintenanceManager
					.getIlmParamsDetails(hostId);
			setIlmParams(ilmParams);

			request.setAttribute("methodName", "displayGeneralPrams");
			SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			putDataSetTimeslotListInReq(request);
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

			log.debug(this.getClass().getName()
					+ " - [displayGeneralPrams] - Exit");
			return getView("displaygeneralprams");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in ILMGeneralMaintenanceAction.'displayGeneralPrams' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in ILMGeneralMaintenanceAction.'displayGeneralPrams' method: "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayGeneralPrams",
							ILMGeneralMaintenanceAction.class), request, "");

			return getView("fail");
		} finally {
			// Nullify the objects
			ilmParams = null;
			hostId = null;
		}
	}

	/**
	 *
	 * This method is called on clicking the "Change" button on the ILAAP
	 * General parameters screen and loads change screen details.
	 * @return
	 * @throws SwtException
	 */
	public String changeGeneralPrams() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the ilmParams object
		ILMParams ilmParams = null;
		// String Variable to hold the hostId
		String hostId = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [changeGeneralPrams] - Enter");
			// Setting screenfield status in request
			request.setAttribute("screenFieldsStatus", "false");

			ilmParams = getIlmParams();




			// Getting hostid from request
			hostId = getHostIdListFromReq(request);
			// Getting ilaap parameter details from manager class
			ilmParams = ilmGeneralMaintenanceManager
					.getIlmParamsDetails(hostId);
			setIlmParams(ilmParams);
			// Setting ButtonStatus
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE);
			request.setAttribute("methodName", "changeGeneralPrams");
			putDataSetTimeslotListInReq(request);
			log.debug(this.getClass().getName()
					+ " - [changeGeneralPrams] - Exit");
			return getView("displaygeneralprams");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in ILMGeneralMaintenanceAction.'change' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in ILMGeneralMaintenanceAction.'changeGeneralPrams' method : "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"changeGeneralPrams",
							ILMGeneralMaintenanceAction.class), request, "");

			return getView("fail");
		} finally {
			// Nullify the object
			ilmParams = null;
			hostId = null;
		}
	}

	/**
	 * This method is called on clicking the "save" button on the ILAAP General
	 * parameters screen and used to update the ILAAP General Parameters details
	 * in database
	 * @return
	 * @throws SwtException
	 */
	public String updateGeneralPrams() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the ilmParams object
		ILMParams ilmParams = null;
		// String Variable to hold the hostId
		String hostId = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [updateGeneralPrams] - Enter");
			// Set screenfield status in request
			request.setAttribute("screenFieldsStatus", "true");

			ilmParams = getIlmParams();
			// Setting HostId from session
			ilmParams.setHostId(SwtUtil.getCurrentHostId(request.getSession()));
			// Update the Ilm parameter object
			ilmGeneralMaintenanceManager.updateIlmParameter(ilmParams);

			// Setting button status
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			// Getting hostid from request
			hostId = getHostIdListFromReq(request);
			// Getting ilm parameter details from manager class
			ilmParams = ilmGeneralMaintenanceManager
					.getIlmParamsDetails(hostId);
			request.setAttribute("methodName", "displayGeneralPrams");
			putDataSetTimeslotListInReq(request);
			setIlmParams(ilmParams);

			log.debug(this.getClass().getName()
					+ " - [updateGeneralPrams] - Exit");
			return getView("displaygeneralprams");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in ILMGeneralMaintenanceAction.'updateGeneralPrams' method : "
					+ swtexp.getMessage());
			request.setAttribute("screenFieldsStatus", "true");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in ILMGeneralMaintenanceAction.'updateGeneralPrams' method : "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"updateGeneralPrams",
							ILMGeneralMaintenanceAction.class), request, "");

			return getView("fail");
		} finally {
			// Nullify the object
			ilmParams = null;
			hostId = null;
		}
	}

	/**
	 * Get the host id from the request
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String getHostIdListFromReq(HttpServletRequest request)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();

		return hostId;
	}

	/**
	 * Used to initialize the 'Timeslot Size' combo box item values in ILAAP
	 * General Parameters Screen
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putDataSetTimeslotListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [putDataSetTimeslotListInReq] - " + "Entry");

		Collection<LabelValueBean> timeslotList = new ArrayList();
		timeslotList.add(new LabelValueBean("1", "1"));
		timeslotList.add(new LabelValueBean("2", "2"));
		timeslotList.add(new LabelValueBean("5", "5"));
		timeslotList.add(new LabelValueBean("10", "10"));
		timeslotList.add(new LabelValueBean("15", "15"));
		timeslotList.add(new LabelValueBean("20", "20"));
		timeslotList.add(new LabelValueBean("30", "30"));
		timeslotList.add(new LabelValueBean("60", "60"));

		// Setting timeslotList in request
		request.setAttribute("timeslotList", timeslotList);

		log.debug(this.getClass().getName()
				+ " - [putDataSetTimeslotListInReq] - " + "Exit");
	}

	/**
	 * List currency parameters from Database
	 * @return
	 * @throws Exception
	 */
	public String listCurrencyParams() throws Exception {

		String hostId = null;
		String roleId = null;
		Collection<ILMCcyParameters> currencyParamsDetails = null;
		String entityId = null;
		int accessInd;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ILMCcyParameters ilmCcyParameters= null;
		try {
			log.debug(this.getClass().getName() + " - [listCurrencyParams] - "
					+ "Entry");


			ilmCcyParameters = getIlmCcyParams();



			putEntityListInReq(request);
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve Entity Id
			entityId = ilmCcyParameters.getId().getEntityId();
			if (SwtUtil.isEmptyOrNull(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			ilmCcyParameters.getId().setEntityId(entityId);
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			// Collect the ILM Currency Parameters list in CurrencyParamsDetails
			currencyParamsDetails = ilmGeneralMaintenanceManager
					.getILMCcyParametersDetails(hostId, roleId, entityId);
			request.setAttribute("currencyParamsDetails", currencyParamsDetails);

			// Get the Access Id of the for the screen
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			if (accessInd == 0)
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			else
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			request.setAttribute("selectedEntityId", entityId);

			request.setAttribute("ilmCcyParams", ilmCcyParameters);
			setIlmCcyParams(ilmCcyParameters);
			log.debug(this.getClass().getName() + " - [listCurrencyParams] - "
					+ "Exit");
			return getView("listCurrencyParams");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listCurrencyParams] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listCurrencyParams] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listCurrencyParams] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listCurrencyParams] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"listCurrencyParams",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullify objects
			hostId = null;
			roleId = null;
			currencyParamsDetails = null;
			entityId = null;
		}
	}

	/**
	 * This method is used to add a new currency for ILM monitoring
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addCurrency()
			throws SwtException {

		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		Collection<LabelValueBean> accountGroupsList = null;
		int accessInd;
		boolean addGroupAccess = true;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ILMCcyParameters ilmCcyParameters= null;
		try {
			log.debug(this.getClass().getName() + " - [addCurrency] - "
					+ "Entry");
			/*
			 * Set the screenFieldsStatus to false because we are in the add
			 * role part. if screenFieldsStatus is true then disable the related
			 * fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);



			ilmCcyParameters = getIlmCcyParams();



			putEntityListInReq(request);
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve Currency Code
			currencyCode = ilmCcyParameters.getId().getCurrencyCode();

			if (SwtUtil.isEmptyOrNull(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				// get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// Enable Save and Cancel buttons
				request.setAttribute(SwtConstants.SAV_BUT_STS,
						SwtConstants.STR_TRUE);
				request.setAttribute(SwtConstants.CAN_BUT_STS,
						SwtConstants.STR_TRUE);
			} else {
				// set only the cancel button status as enable
				request.setAttribute(SwtConstants.SAV_BUT_STS,
						SwtConstants.STR_FALSE);
				request.setAttribute(SwtConstants.CAN_BUT_STS,
						SwtConstants.STR_TRUE);
			}

			ilmCcyParameters.getId().setHostId(hostId);
			ilmCcyParameters.getId().setEntityId(entityId);
			ilmCcyParameters.getId().setCurrencyCode(currencyCode);
			/*
			 * Collect the ILM Currency Account Group Parameters list in
			 * accountGroupsList
			 */
			accountGroupsList = new ArrayList<LabelValueBean>();
			// Showing 'the empty' value in Global Currency Account Group drop
			// down
			accountGroupsList.add(new LabelValueBean("", ""));
			accountGroupsList.addAll(ilmGeneralMaintenanceManager.getAllowedReportAccountGroupList(hostId,entityId,currencyCode));

			//Check access to the add group screen
			addGroupAccess = XSSUtil.checkILMGroupFullAccess(request, null, entityId, currencyCode, "addfromILM");

			request.setAttribute("addGroupAccess", addGroupAccess);
			// Put the accountGroupsList in the form
			request.setAttribute("accountGroupsList", accountGroupsList);
			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false));
			request.setAttribute("selectedEntityId", entityId);

			request.setAttribute("selectedCurrencyCode", currencyCode);
			request.setAttribute("ilmCcyParams", ilmCcyParameters);
			request.setAttribute("methodName", "addCurrency");
			request.setAttribute("possibleToMaintain", SwtUtil.getMaintainAnyGroupILMAccess(request));
			setIlmCcyParams(ilmCcyParameters);
			log.debug(this.getClass().getName() + " - [addCurrency] - "
					+ "Exit");
			return getView("addCurrency");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addCurrency] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"addCurrency", ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			hostId = null;
			entityId = null;
			currencyCode = null;
			accountGroupsList = null;
		}
	}

	/**
	 * Method called when the user clicks change button in ILAAP Currency
	 * Parameters maintenance screen
	 * @return ActionForward
	 * @throws Exception
	 */
	public String changeCurrency()
			throws Exception {

		ILMCcyParameters ilmCcyParameters;
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String currencyCode = null;
		String currencyName = null;
		boolean addGroupAccess = true;
		Collection<LabelValueBean> accountGroupsList = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName() + " - [changeCurrency] - "
					+ "Entry");



			ilmCcyParameters = getIlmCcyParams();


			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the entity name form request
			entityName = request.getParameter("selectedEntityName");
			// Read the currency code from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("selectedCurrencyCode")))
				currencyCode = request.getParameter("selectedCurrencyCode");
			else
				currencyCode =  request.getParameter("currencyCode");
			// Retrieve the currency name
			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, false),
					currencyCode);
			// Get the ILMCcyParameters editable data
			ilmCcyParameters = ilmGeneralMaintenanceManager
					.getILMCcyParameterEditableData(hostId, entityId,
							currencyCode);
			/*
			 * Collect the ILM Currency Account Group Parameters list in
			 * accountGroupsList
			 */
			accountGroupsList = new ArrayList<LabelValueBean>();
			// Showing 'the empty' value in Global Currency Account Group drop
			// down
//			accountGroupsList.add(new LabelValueBean("", ""));
//			accountGroupsList.addAll(putDataSetGlobalCcyAcctGroupListInReq(
//					hostId, entityId, currencyCode, request));
			if( ilmCcyParameters!= null && !SwtUtil.isEmptyOrNull(ilmCcyParameters.getPrimaryAccountId())){
				ilmCcyParameters.setPrimaryAccountName(ilmGeneralMaintenanceManager.getAccountName(hostId, entityId, ilmCcyParameters.getPrimaryAccountId()));
			}
			accountGroupsList.add(new LabelValueBean("", ""));
			accountGroupsList.addAll(ilmGeneralMaintenanceManager
					.getPublicAccountGroupsList(hostId, entityId, currencyCode));

			addGroupAccess = XSSUtil.checkILMGroupFullAccess(request, null, entityId, currencyCode, "addfromILM");

			setIlmCcyParams(ilmCcyParameters);
			// Put the required attributes in the request
			request.setAttribute("entityText", entityName);
			request.setAttribute("addGroupAccess", addGroupAccess);
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("accountGroupsList", accountGroupsList);
			request.setAttribute("primaryAccountId",ilmCcyParameters.getPrimaryAccountId() != null ? ilmCcyParameters.getPrimaryAccountId():"");
			request.setAttribute("methodName", "changeCurrency");
			request.setAttribute("possibleToMaintain", SwtUtil.getMaintainAnyGroupILMAccess(request));
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);

			log.debug(this.getClass().getName() + " - [changeCurrency] - "
					+ "Exit");
			return getView("changeCurrency");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeCurrency] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeCurrency] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeCurrency] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeCurrency] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(e, "changeCurrency",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			hostId = null;
			entityId = null;
			entityName = null;
			currencyCode = null;
			currencyName = null;
			accountGroupsList = null;
		}
	}
	/**
	 * Method called when the user clicks view button in ILAAP Currency
	 * Parameters maintenance screen
	 * @return ActionForward
	 * @throws Exception
	 */
	public String viewCurrency()
			throws Exception {

		ILMCcyParameters ilmCcyParameters;
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String currencyCode = null;
		String currencyName = null;
		boolean addGroupAccess = false;
		Collection<LabelValueBean> accountGroupsList = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {
			log.debug(this.getClass().getName() + " - [viewCurrency] - "
					+ "Entry");



			ilmCcyParameters = getIlmCcyParams();


			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the entity name form request
			entityName = request.getParameter("selectedEntityName");
			// Read the currency code from request
			if(!SwtUtil.isEmptyOrNull(request.getParameter("selectedCurrencyCode")))
				currencyCode = request.getParameter("selectedCurrencyCode");
			else
				currencyCode =  request.getParameter("currencyCode");
			// Retrieve the currency name
			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, false),
					currencyCode);
			// Get the ILMCcyParameters editable data
			ilmCcyParameters = ilmGeneralMaintenanceManager
					.getILMCcyParameterEditableData(hostId, entityId,
							currencyCode);
			/*
			 * Collect the ILM Currency Account Group Parameters list in
			 * accountGroupsList
			 */
			accountGroupsList = new ArrayList<LabelValueBean>();
			// Showing 'the empty' value in Global Currency Account Group drop
			// down
//			accountGroupsList.add(new LabelValueBean("", ""));
//			accountGroupsList.addAll(putDataSetGlobalCcyAcctGroupListInReq(
//					hostId, entityId, currencyCode, request));
			if( ilmCcyParameters!= null && !SwtUtil.isEmptyOrNull(ilmCcyParameters.getPrimaryAccountId())){
				ilmCcyParameters.setPrimaryAccountName(ilmGeneralMaintenanceManager.getAccountName(hostId, entityId, ilmCcyParameters.getPrimaryAccountId()));
			}
			accountGroupsList.add(new LabelValueBean("", ""));
			accountGroupsList.addAll(ilmGeneralMaintenanceManager
					.getPublicAccountGroupsList(hostId, entityId, currencyCode));


			setIlmCcyParams(ilmCcyParameters);
			// Put the required attributes in the request
			request.setAttribute("entityText", entityName);
			request.setAttribute("addGroupAccess", addGroupAccess);
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("accountGroupsList", accountGroupsList);
			request.setAttribute("primaryAccountId",ilmCcyParameters.getPrimaryAccountId() != null ? ilmCcyParameters.getPrimaryAccountId():"");
			request.setAttribute("methodName", "viewCurrency");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_FALSE);

			log.debug(this.getClass().getName() + " - [viewCurrency] - "
					+ "Exit");
			return getView("viewCurrency");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewCurrency] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewCurrency] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewCurrency] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewCurrency] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(e, "viewCurrency",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			ilmCcyParameters = null;
			hostId = null;
			entityId = null;
			entityName = null;
			currencyCode = null;
			currencyName = null;
			accountGroupsList = null;
		}
	}


	/**
	 * Used to check if the user is able to maintain any ILM group
	 * @return
	 * @throws SwtException
	 */
	public String getCurrencyGMTOffset() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String entityId = null;
		String currencyCode = null;
		String hostId = null;
		boolean hasGMTOffset = false;

		try {
			log.debug(this.getClass().getName()
					+ "- [getAccessForILMGroup] - Enter");
			currencyCode = request.getParameter("currencyCode");
			entityId = request.getParameter("entityId");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			hasGMTOffset = ilmGeneralMaintenanceManager.getCurrencyGMTOffset(hostId, entityId, currencyCode);

			response.getWriter().print(hasGMTOffset);
			log.debug(this.getClass().getName()
					+ " - [getAccessForILMGroup] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccessForILMGroup] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getAccessForILMGroup",
							ILMGeneralMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
			// Nullify Objects
			entityId = null;
			currencyCode = null;
			hostId = null;
		}
	}

	/**
	 * Method called when the user clicks save Button in Add ILAAP Currency
	 * Parameters maintenance screen
	 * @return
	 * @throws SwtException
	 */
	public String saveCurrency()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ILMCcyParameters ilmCcyParameters = null;
		ActionMessages errors = null;
		Collection<LabelValueBean> accountGroupsList = null;

		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [saveCurrency] - "
					+ "Entry");



			ilmCcyParameters = getIlmCcyParams();



			ilmCcyParameters.getId().setHostId(SwtUtil.getCurrentHostId());

			ilmGeneralMaintenanceManager
					.saveILMCcyParametersDetails(ilmCcyParameters);
			// If parentFromRefresh value is 'yes', then we will close the
			// opened screen and refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");

			accountGroupsList = putDataSetGlobalCcyAcctGroupListInReq(
					SwtUtil.getCurrentHostId(), ilmCcyParameters.getId()
							.getEntityId(), ilmCcyParameters.getId()
							.getCurrencyCode(), request);
			// Put the accountGroupsList in the form
			request.setAttribute("accountGroupsList", accountGroupsList);
			// Put the currencies list in the form
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmCcyParameters.getId().getEntityId(), false));
			putEntityListInReq(request);
			setIlmCcyParams(ilmCcyParameters);
			// Put the required attributes in the request
			request.setAttribute("ilmCcyParams", ilmCcyParameters);
			request.setAttribute("methodName", "saveCurrency");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveCurrency] - "
					+ "Exit");
			return getView("addCurrency");
		} catch (SwtException swtexp) {

			request.setAttribute("methodName", "addCurrency");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveCurrency] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			setIlmCcyParams(ilmCcyParameters);
			accountGroupsList = new ArrayList<LabelValueBean>();
			// Showing 'the empty' value in Global Currency Account Group drop
			// down
			accountGroupsList.add(new LabelValueBean("", ""));
			accountGroupsList.addAll(ilmGeneralMaintenanceManager
					.getAccountGroupsList(SwtUtil.getCurrentHostId(),
							ilmCcyParameters.getId().getEntityId(),
							ilmCcyParameters.getId().getCurrencyCode()));
			request.setAttribute("accountGroupsList", accountGroupsList);
			// Put the currencies list in the form
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmCcyParameters.getId().getEntityId(), false));
			putEntityListInReq(request);
			return getView("addCurrency");
		} catch (Exception exp) {
			setIlmCcyParams(ilmCcyParameters);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveCurrency] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"saveCurrency", ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			ilmCcyParameters = null;
			errors = null;
			accountGroupsList = null;
		}
	}

	/**
	 * Method called when save Button clicked on Change ILAAP Currency
	 * Parameters maintenance screen
	 * @return
	 * @throws SwtException
	 */
	public String updateCurrency()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ILMCcyParameters ilmCcyParameters = null;
		String entityId = null;
		String currencyCode = null;
		Collection<LabelValueBean> accountGroupsList = null;

		try {
			log.debug(this.getClass().getName() + " - [updateCurrency] - "
					+ "Entry");



			ilmCcyParameters = getIlmCcyParams();


			ilmCcyParameters.getId().setHostId(SwtUtil.getCurrentHostId());
			entityId = request.getParameter("selectedEntityId");
			currencyCode = request.getParameter("currencyCode");
			ilmCcyParameters.getId().setEntityId(entityId);
			ilmCcyParameters.getId().setCurrencyCode(currencyCode);

			ilmGeneralMaintenanceManager
					.updateILMCcyParametersDetails(ilmCcyParameters);
			// If parentFromRefresh value is 'yes', then we will close the
			// opened screen and refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");

			accountGroupsList = putDataSetGlobalCcyAcctGroupListInReq(
					SwtUtil.getCurrentHostId(), ilmCcyParameters.getId()
							.getEntityId(), ilmCcyParameters.getId()
							.getCurrencyCode(), request);
			// Put the accountGroupsList in the form
			request.setAttribute("accountGroupsList", accountGroupsList);
			// Put the currencies list in the form
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmCcyParameters.getId().getEntityId(), false));
			putEntityListInReq(request);
			setIlmCcyParams(ilmCcyParameters);
			// Put the required attributes in the request
			request.setAttribute("ilmCcyParams", ilmCcyParameters);
			request.setAttribute("methodName", "updateCurrency");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [updateCurrency] - "
					+ "Exit");
			return getView("addCurrency");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateCurrency] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(exp, "updateCurrency",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// Nullify objects
			ilmCcyParameters = null;
			entityId = null;
			currencyCode = null;
			accountGroupsList = null;
		}
	}

	/**
	 * Delete selected from database. It is called when the user clicks delete
	 * button after selecting an ILM currency
	 * @return
	 * @throws SwtException
	 */
	public String deleteCurrency()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		ILMCcyParameters ilmCcyParameters = null;
		String roleId = null;
		Collection<ILMCcyParameters> currencyParamsDetails = null;
		int accessInd;

		try {
			log.debug(this.getClass().getName()
					+ "- [deleteCurrency] - Entering");
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the currency code from request
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Get the ILMCcyParameters editable data
			ilmCcyParameters = ilmGeneralMaintenanceManager
					.getILMCcyParameterEditableData(hostId, entityId,
							currencyCode);
			// Delete the ILMCcyParameters object
			ilmGeneralMaintenanceManager
					.deleteILMCcyParametersDetails(ilmCcyParameters);

			putEntityListInReq(request);
			// Collect the ILM Currency Parameters list in CurrencyParamsDetails
			currencyParamsDetails = ilmGeneralMaintenanceManager
					.getILMCcyParametersDetails(hostId, roleId, entityId);

			request.setAttribute("currencyParamsDetails", currencyParamsDetails);

			request.setAttribute("methodName", "listCurrencyParams");
			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}

			setIlmCcyParams(ilmCcyParameters);
			log.debug(this.getClass().getName()
					+ "- [deleteCurrency] - Exiting");
			return getView("listCurrencyParams");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCurrency] method : - "
					+ swtexp.getMessage());
			putEntityListInReq(request);
			setIlmCcyParams(ilmCcyParameters);
			// Retrieve User's Menu,Entity and Currency Group
			// Collect the ILM Currency Parameters list in CurrencyParamsDetails
			currencyParamsDetails = ilmGeneralMaintenanceManager
					.getILMCcyParametersDetails(hostId, roleId, entityId);

			request.setAttribute("currencyParamsDetails", currencyParamsDetails);
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, null, null);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			}
			request.setAttribute("methodName", "listCurrencyParams");
			log.debug(this.getClass().getName()
					+ "- [deleteCurrency] - Exiting");
			return getView("listCurrencyParams");

		} catch (Exception exp) {
			setIlmCcyParams(ilmCcyParameters);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteCurrency] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(exp, "deleteCurrency",
									ILMGeneralMaintenanceAction.class),
					request, "");

			return getView("fail");
		} finally {
			// nullify objects
			hostId = null;
			entityId = null;
			currencyCode = null;
			ilmCcyParameters = null;
			roleId = null;
			currencyParamsDetails = null;
		}
	}

	/**
	 * Used to initialize the 'GlobalCurrencyAccount Group' combo box item
	 * values in ILAAP Currency Parameters Screen
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> putDataSetGlobalCcyAcctGroupListInReq(
			String hostId, String entityId, String currencyCode,
			HttpServletRequest request) throws SwtException {

		// Holds the Currency Account Group as LabelValueBean object
		Collection<LabelValueBean> acountGroupsLVB = null;
		// Iterate Currency Account Group list and convert them to
		// LabelValueBean list
		Iterator<ILMAccountGroup> itrCcyAcctGrp = null;
		// ilmAccountGroup details
		ILMAccountGroup ilmAccountGroup = null;
		Collection<ILMAccountGroup> colAccountGroup = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [putDataSetGlobalCcyAcctGroupListInReq] - " + "Entry");
			// Initialize collection to hold ilmAccountGroup detail as
			// LabelValueBean object
			acountGroupsLVB = new ArrayList<LabelValueBean>();
			colAccountGroup = ilmGeneralMaintenanceManager
					.getAccountGroupsDetails(hostId, entityId, currencyCode,
							request);
			// Iterate through ilmAccountGroup list and get LabelValueBean list
			if (colAccountGroup != null) {
				itrCcyAcctGrp = colAccountGroup.iterator();
				while (itrCcyAcctGrp.hasNext()) {
					ilmAccountGroup = (ILMAccountGroup) itrCcyAcctGrp.next();
					acountGroupsLVB.add(new LabelValueBean(SwtUtil
							.decreaseStringWidth(
									SwtConstants.ACCOUNT_GROUP_TITLE_MAX_WIDTH,
									ilmAccountGroup.getIlmGroupName(),
									SwtConstants.VERDANA_STYLE,
									SwtConstants.VERDANA_12P_SIZE),
							ilmAccountGroup.getId().getIlmGroupId()));
				}
			}
			log.debug("ILMGeneralMaintenanceAction - [putDataSetGlobalCcyAcctGroupListInReq] - Exit");
		} catch (Exception ex) {
			// log error message
			log.error("ILMGeneralMaintenanceAction - [putDataSetGlobalCcyAcctGroupListInReq] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"putDataSetGlobalCcyAcctGroupListInReq",
					ILMGeneralMaintenanceAction.class);
		} finally {
			// nullify objects
			itrCcyAcctGrp = null;
			ilmAccountGroup = null;
			colAccountGroup = null;
		}
		return acountGroupsLVB;

	}

	/**
	 * This method is used to check the entity and ccygrp access .
	 * @return
	 * @throws SwtException
	 */
	public String checkILMCurrencyAccess() throws SwtException {

		int ilmCcyAccess;
		String entityId = null;
		String currencyCode = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName()
					+ "- [checkILMCurrencyAccess] - Enter");
			entityId = request.getParameter("selectedEntityId");
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Get the Access for the entity and currency
			ilmCcyAccess = SwtUtil.getMenuEntityCurrGrpAccess(request,
					entityId, currencyCode);
			response.getWriter().print(ilmCcyAccess);
			log.debug(this.getClass().getName()
					+ " - [checkILMCurrencyAccess] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkILMCurrencyAccess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkILMCurrencyAccess",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullify objects
			entityId = null;
			currencyCode = null;
		}
	}

	/**
	 * This method is called to open the account group details screen
	 * @return actionForward
	 */
	public String accountGroupDetailsFlex() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					+ " - [accountGroupDetailsFlex] - " + "Entry");

			request.setAttribute("selectedAccountGroup",
					request.getParameter("selectedAccountGroup"));
			request.setAttribute("methodName",
					request.getParameter("methodName"));
			request.setAttribute("entityId", request.getParameter("entityId"));
			User user = SwtUtil.getCurrentUser(request.getSession());
			if (request.getParameter("methodName").equals("add")) {
				request.setAttribute("createdBy", user.getUserName());
				request.setAttribute("createdOn", SwtUtil.getSystemDateString());
				request.setAttribute("parentScreen", "IlmAccountGroupMaintenance");
			}
			if (request.getParameter("methodName").equals("addfromILM")){
				request.setAttribute("createdBy", user.getUserName());
				request.setAttribute("createdOn", SwtUtil.getSystemDateString());
				request.setAttribute("description", request.getParameter("description"));
				request.setAttribute("groupDefaultName",request.getParameter("groupDefaultName"));
				request.setAttribute("filter", request.getParameter("filter"));

				request.setAttribute("parentScreen","addIlmCurrencyParameter");
			}
			request.setAttribute("currencyCode",
					request.getParameter("currencyCode"));
			if (!SwtUtil.isEmptyOrNull(request.getParameter("parentScreen")))
				request.setAttribute("parentScreen",request.getParameter("parentScreen"));
			request.setAttribute("maintainAnyGroup",
					SwtUtil.getMaintainAnyGroupILMAccess(request));

			return getView("groupdetailsflex");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [accountGroupDetailsFlex] method : - "
					+ exp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "."
							+ exp.getStackTrace()[0].getMethodName() + ":"
							+ exp.getStackTrace()[0].getLineNumber());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"accountGroupDetailsFlex",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("detailsdataerror");
		}
	}

	/**
	 * This function is called when the user press button in the account group
	 * details screen. It will test the query with filter condition defined by
	 * the user in the same screen.
	 * @return
	 * @throws SwtException
	 */
	public String getFilterConditionTestResult() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String filterCondition = null;
		AccountQueryResult queryResult = null;
		String hostId = null;
		ArrayList<AcctMaintenance> accountInGroup = null;
		String entityId = null;
		String currencyCode = null;
		String isValidClause = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [getFilterConditionTestResult] - Entry");
			opTimer.start("all");
			/* Retrieve Host Id */
			hostId = SwtUtil.getCurrentHostId();

			filterCondition = request.getParameter("query");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			isValidClause = ilmGeneralMaintenanceManager
					.getFilterConditionResult(filterCondition);
			if (isValidClause.equals("SUCCESS")) {
				queryResult = ilmGeneralMaintenanceManager
						.getAccountsQueryResult(hostId, entityId, currencyCode,
								filterCondition);
				accountInGroup = queryResult.getAccountList();
			} else {
				accountInGroup = new ArrayList();
				request.setAttribute("exceptionInQuery", isValidClause);
			}
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			request.setAttribute("accountInGroup", accountInGroup);
			request.setAttribute("rowSize",
					accountInGroup != null ? accountInGroup.size() : "0");

			request.setAttribute("dynamic", SwtConstants.STR_TRUE);
			// Set column width with request attribute
			bindColumnWidthInRequest(request);
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName()
					+ " - [getFilterConditionTestResult] - Exit");
			return getView("secondgriddetails");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFilterConditionTestResult] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getFilterConditionTestResult] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getFilterConditionTestResult",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");

		} finally {
			// Nullify the objects
			filterCondition = null;
			queryResult = null;
			hostId = null;
			accountInGroup = null;
			entityId = null;
			currencyCode = null;
		}
	}

	/**
	 * List the account groups details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getAccountGroupDetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String selectedAccountGroup = null;
		ILMAccountGroup accountGroup = null;
		String hostId = null;
		ArrayList<ILMAccountInGroups> accountInGroup = null;
		String entityId = null;
		String currencyCode = null;
		String createdDateAsString = null;
		boolean getDataFromDb = true;
		ArrayList<LabelValueBean> currencyList = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountGroupDetails] - Entry");
			opTimer.start("all");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// get the selected Account Group Id form request
			selectedAccountGroup = request.getParameter("selectedAccountGroup");

			if (!SwtUtil.isEmptyOrNull(selectedAccountGroup))
				accountGroup = ilmGeneralMaintenanceManager
						.getEditableDataDetailList(selectedAccountGroup);
			else
				getDataFromDb = false;

			if (accountGroup == null)
				getDataFromDb = false;

			if (getDataFromDb) {
				accountInGroup = new ArrayList<ILMAccountInGroups>();
				accountInGroup.addAll(accountGroup.getAccountsInGroup());
				entityId = accountGroup.getEntityId();
				currencyCode = accountGroup.getCurrencyCode();
				accountGroup.setGlobal(accountGroup.getILMCcyParameters().size() > 0 ? "Y": "N");
			} else {
				accountGroup = new ILMAccountGroup();
				accountInGroup = new ArrayList();
				entityId = request.getParameter("entityId");
				currencyCode = request.getParameter("currencyCode");
				accountGroup.setEntityId(entityId);
				if (SwtUtil.isEmptyOrNull(currencyCode)
						|| currencyCode.equals("All"))
					currencyCode = SwtUtil.getDomesticCurrencyForEntity(hostId,
							entityId);
				accountGroup.setCurrencyCode(currencyCode);
			}
			// put entityList in request
			putEntityFullOrViewAccessListInReq(request);
			putAccountGroupsListInReq(request, hostId, entityId, currencyCode,
					false);
			currencyList = (ArrayList) getCurrencyFullOrViewAccessList(request,
					hostId, entityId);

			// check currencyList is not null
			if (currencyList != null) {
				// remove the default one
				currencyList.remove(new LabelValueBean("Default", "*"));
			}

			request.setAttribute("currencies", currencyList);

			if (accountGroup.getCreateDate() != null)
				createdDateAsString = SwtUtil.formatDate(
						accountGroup.getCreateDate(),
						SwtUtil.getCurrentDateFormat(request.getSession()));

			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			request.setAttribute("accountGroup", accountGroup);
			request.setAttribute("accountInGroup", accountInGroup);
			request.setAttribute("createdDate", createdDateAsString);
			request.setAttribute("maintainAnyGroup",
					SwtUtil.getMaintainAnyGroupILMAccess(request));
			request.setAttribute("rowSize",
					accountInGroup != null ? accountInGroup.size() : "0");
			// Set column width with request attribute
			bindColumnWidthInRequest(request);
			request.setAttribute("global", accountGroup.getGlobal());
			request.setAttribute("central", ilmGeneralMaintenanceManager.isCentralCcyGroup(selectedAccountGroup));
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName()
					+ " - [getAccountGroupDetails] - Exit");
			return getView("groupdetails");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupDetails] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountGroupDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getAccountGroupDetails",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");

		} finally {
			// Nullify objects
			selectedAccountGroup = null;
			accountGroup = null;
			hostId = null;
			accountInGroup = null;
			entityId = null;
			currencyCode = null;
			createdDateAsString = null;
		}
	}

	/**
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getAccountListDetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String selectedAccountGroup = null;
		String secondGridAccountGroup = null;
		ILMAccountGroup accountGroup = null;
		ILMAccountGroup secondAccountGroup = null;
		String hostId = null;
		ArrayList<AcctMaintenance> accountList = null;
		String entityId = null;
		String currencyCode = null;
		boolean getDataFromDB = true;
		String filterCondition = null;
		AccountQueryResult queryResult = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - Entry");
			opTimer.start("all");
			// Retrieve Host Id
			accountList = new ArrayList<AcctMaintenance>();
			hostId = SwtUtil.getCurrentHostId();
			// Get the selected Account Group Id form request
			selectedAccountGroup = request.getParameter("selectedAccountGroup");
			// Get the the second account group from ComboBox form request
			secondGridAccountGroup = request
					.getParameter("secondGridAccountGroup");

			if (!SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				accountGroup = ilmGeneralMaintenanceManager
						.getEditableDataDetailList(selectedAccountGroup);
				if (accountGroup == null)
					getDataFromDB = false;
			} else
				getDataFromDB = false;
			if (getDataFromDB) {
				entityId = accountGroup.getEntityId();
				currencyCode = accountGroup.getCurrencyCode();
			} else {
				entityId = request.getParameter("entityId");
				currencyCode = request.getParameter("currencyCode");
			}

			if (SwtUtil.isEmptyOrNull(secondGridAccountGroup)
					|| secondGridAccountGroup.equals("null")
					|| secondGridAccountGroup.equals("All")){
				accountList = new ArrayList<AcctMaintenance>(
						ilmGeneralMaintenanceManager.getAccountListDetails(
								entityId, currencyCode));}
			else {
				secondAccountGroup = ilmGeneralMaintenanceManager
						.getEditableDataDetailList(secondGridAccountGroup);
				filterCondition = secondAccountGroup.getFilterCondition();
				if (!SwtUtil.isEmptyOrNull(filterCondition)) {
					queryResult = ilmGeneralMaintenanceManager
							.getAccountsQueryResult(hostId, entityId,
									currencyCode, filterCondition);
					accountList = queryResult.getAccountList();
				} else {
					Iterator itr = secondAccountGroup.getAccountsInGroup()
							.iterator();
					while (itr.hasNext()) {
						ILMAccountInGroups acct = (ILMAccountInGroups) (itr
								.next());
						accountList.add(acct.getId().getAccount());
					}
				}
			}
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			request.setAttribute("accountInGroup", accountList);
			request.setAttribute("secondGridAccountGroup",
					secondGridAccountGroup);
			request.setAttribute("selectedAccountGroup", selectedAccountGroup);
			request.setAttribute("rowSize", accountList.size());

			putAccountGroupsListInReq(request, hostId, entityId, currencyCode,
					true);
			// Set column width with request attribute
			bindColumnWidthInRequest(request);
			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName()
					+ " - [getAccountListDetails] - Exit");
			return getView("secondgriddetails");
		} catch (SwtException swtExp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ swtExp.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountListDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getAccountListDetails",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullify the objects
			selectedAccountGroup = null;
			secondGridAccountGroup = null;
			accountGroup = null;
			secondAccountGroup = null;
			hostId = null;
			accountList = null;
			entityId = null;
			currencyCode = null;
		}
	}

	/**
	 * Export the account group details only in view mode
	 * @return
	 * @throws SwtException
	 */
	public String exportAccountGrpDetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		SwtDynamicReportImpl report;
		String accountGroupId = null;
		String exportType = null;
		String fileName = null;
		FilterDTO filter;
		String query = null;
		ILMAccountGroup accountGroup = new ILMAccountGroup();
		ArrayList<FilterDTO> filterData;
		Map jasperQueryParams = null;
		Map columnsInfo = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [exportAccountGrpDetails] - Begin");
			filterData = new ArrayList<FilterDTO>();
			exportType = request.getParameter("exportType");
			accountGroupId = request.getParameter("accountGrpId");
			report = new SwtDynamicReportImpl();
			accountGroup = ilmGeneralMaintenanceManager
					.getEditableDataDetailList(accountGroupId);

			filter = new FilterDTO();
			filter.setName("Entity Id");
			filter.setValue(accountGroup.getEntityId());
			filterData.add(filter);

			filter = new FilterDTO();
			filter.setName("Currency Code");
			filter.setValue(accountGroup.getCurrencyCode());
			filterData.add(filter);

			filter = new FilterDTO();
			filter.setName("Public/Private");
			filter.setValue(accountGroup.getPublicPrivate().equals("PRIVATE") ? "Private"
					: "Public");
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Type");
			filter.setValue(accountGroup.getGroupType().equals("F") ? "Fixed"
					: "Dynamic");
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Name");
			filter.setValue(accountGroup.getIlmGroupName());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Description");
			filter.setValue(accountGroup.getIlmGroupDescription());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("First Maximum");
			filter.setValue(accountGroup.getThresholdMax1());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Second Maximum");
			filter.setValue(accountGroup.getThresholdMax2());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("First Minimum");
			filter.setValue(accountGroup.getThresholdMin1());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Second Minimum");
			filter.setValue(accountGroup.getThresholdMin2());
			filterData.add(filter);
			filter = new FilterDTO();

			filter.setName("Filter Condition");
			String filterValue = null;
			if (SwtUtil.isEmptyOrNull(accountGroup.getFilterCondition()))
				filterValue = "";
			else
				filterValue = accountGroup.getFilterCondition();
			filter.setValue(filterValue);
			filterData.add(filter);

			// Get title suffix
			String titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
			if (titleSuffix == null)
				titleSuffix = "";
			// Get report file name
			fileName = request.getParameter("screen").replaceAll(" ", "");
			// Generate report based on user selection
			if (exportType.equalsIgnoreCase("excel")) {
				exportType = "XLS";
				// Set content type
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".xls");
			} else if (exportType.equalsIgnoreCase("pdf")) {
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".pdf");
			} else if (exportType.equalsIgnoreCase("csv")) {
				// Set content type
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".csv");
			}
			jasperQueryParams = new HashMap<String, Object>();
			if (!SwtUtil.isEmptyOrNull(accountGroup.getFilterCondition()))
				query = createQueryFormFilter(accountGroup);
			else {
				query = SwtConstants.ACCOUNT_GROUP_QUERY;
				jasperQueryParams.put("pILM_GROUPID", accountGroup.getId()
						.getIlmGroupId());

			}
			// Set jasper parameters
			jasperQueryParams.put("pGROUP_ID_NAME",
					SwtUtil.getMessage("ilmAccountGroupDetails.accountIdName", request));
			jasperQueryParams.put("pTYPE", SwtUtil.getMessage("ilmAccountGroupDetails.type", request));
			jasperQueryParams.put("pCLASS",
					SwtUtil.getMessage("ilmAccountGroupDetails.class", request));
			jasperQueryParams.put("pLEVEL",
					SwtUtil.getMessage("ilmAccountGroupDetails.level", request));
			jasperQueryParams.put("pCASH_TEXT", SwtConstants.CASH);
			jasperQueryParams
					.put("pCUSTODIAN_TEXT", SwtConstants.CUSTODIAN);
			jasperQueryParams.put("pCURRENT_TEXT", SwtConstants.CURRENT);
			jasperQueryParams.put("pLORO_TEXT", SwtConstants.LORO);
			jasperQueryParams.put("pNETTING_TEXT", SwtConstants.NETTING);
			jasperQueryParams.put("pNOSTRO_TEXT", SwtConstants.NOSTRO);
			jasperQueryParams.put("pOTHERS_TEXT", SwtConstants.OTHERS);
			jasperQueryParams.put("pMAIN", SwtConstants.MAIN);
			jasperQueryParams.put("pSUB", SwtConstants.SUB);

			// Set columns info as name,type
			columnsInfo = new LinkedHashMap<Object, String>();
			columnsInfo.put(SwtUtil.getMessage("ilmAccountGroupDetails.accountIdName", request), "String");
			columnsInfo.put(SwtUtil.getMessage("ilmAccountGroupDetails.type", request), "String");
			columnsInfo.put(SwtUtil.getMessage("ilmAccountGroupDetails.class", request), "String");
			columnsInfo.put(SwtUtil.getMessage("ilmAccountGroupDetails.level", request), "String");

			// call report method
			report.convertObject(request, response, null, filterData, query,
					columnsInfo, null, "Account Group Details", exportType,
					jasperQueryParams);

			log.debug(this.getClass().getName()
					+ " - [exportAccountGrpDetails] - Exit");
			return null;

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [exportAccountGrpDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [exportAccountGrpDetails] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"exportAccountGrpDetails",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// Nullifying the objects
			report = null;
			accountGroupId = null;
			exportType = null;
			fileName = null;
			filter = null;
			query = null;
			accountGroup = new ILMAccountGroup();
			filterData = null;
		}
	}

	/**
	 * Update the existed or create a new account group with details defined by
	 * the user in Add/change Account group details screen
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveAccountGroupdetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String selectedAccountGroup = null;
		String entityId = null;
		String currencyCode = null;
		String privatePublic = null;
		String type = null;
		String name = null;
		String description = null;
		String firstMinimum = null;
		String secondMinimum = null;
		String firstMaximum = null;
		String secondMaximum = null;
		String filterCondition = null;
		String accountsToDelete = null;
		String accountsToAdd = null;
		ILMAccountGroup accountGroup = null;
		String hostId = null;
		String filterClause = "SUCCESS";
		Boolean saveStatus = false;
		ActionMessages errors = null;
		String defaultLegendText = null;
		String allowReporting = null;
		String correspondentBank = null;
		String collectNetCumPos = null;
		String mainAgent = null;
		String minNcpThreshold = null;
		String maxNcpThreshold = null;
		String throughputRatio = null;
		String thresh1Time = null;
		String thresh2Time = null;
		String thresh1Percent = null;
		String thresh2Percent = null;

		try {

			log.debug(this.getClass().getName()
					+ " - [saveAccountGroupdetails] - Entry");
			errors = new ActionMessages();
			opTimer.start("all");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// get the selected Account Group Id form request
			selectedAccountGroup = request.getParameter("selectedAccountGroup");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			privatePublic = request.getParameter("privatePublic");
			if(!SwtUtil.isEmptyOrNull(privatePublic)) {
				privatePublic = privatePublic.toUpperCase();
			}else {
				privatePublic ="PRIVATE";
			}
			type = request.getParameter("type");
			name = request.getParameter("name");
			description = request.getParameter("description");
			defaultLegendText = request.getParameter("defaultLegendText");
			allowReporting = request.getParameter("allowReporting");
			correspondentBank = request.getParameter("correspondentBank");
			collectNetCumPos = request.getParameter("collectNetCumPos");
			firstMinimum = request.getParameter("firstMinimum");
			secondMinimum = request.getParameter("secondMinimum");
			firstMaximum = request.getParameter("firstMaximum");
			secondMaximum = request.getParameter("secondMaximum");
			filterCondition = request.getParameter("filterCondition");
			accountsToDelete = request.getParameter("accountsToDelete");
			accountsToAdd = request.getParameter("accountsToAdd");
			mainAgent = request.getParameter("mainAgentText");
			minNcpThreshold = request.getParameter("minNcpThreshold");
			maxNcpThreshold = request.getParameter("maxNcpThreshold");
			throughputRatio = request.getParameter("throughputRatio");
			thresh1Time = request.getParameter("thresh1Time");
			thresh2Time = request.getParameter("thresh2Time");
			thresh1Percent = request.getParameter("thresh1Percent");
			thresh2Percent = request.getParameter("thresh2Percent");
			if (!SwtUtil.isEmptyOrNull(filterCondition))
				filterClause = ilmGeneralMaintenanceManager
						.getFilterConditionResult(filterCondition);
			if (!filterClause.equals("SUCCESS")) {
				request.setAttribute("exceptionInQuery", filterClause);
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", filterClause);
			} else {

				// get the the second account group from ComboBox form request

				Date createDate = SwtUtil.getSystemDatewithTime();
				accountGroup = new ILMAccountGroup();
				accountGroup.getId().setIlmGroupId(selectedAccountGroup);
				accountGroup.setEntityId(entityId);
				accountGroup.setHostId(hostId);
				accountGroup.setCurrencyCode(currencyCode);
				accountGroup.setPublicPrivate(privatePublic);
				accountGroup.setGroupType(type);
				accountGroup.setIlmGroupName(name);
				accountGroup.setIlmGroupDescription(description);
				accountGroup.setDefaultLegendText(defaultLegendText);
				accountGroup.setAllowReporting(allowReporting);
				accountGroup.setCorrespondentBank(correspondentBank);
				accountGroup.setCollectNetCumPos(collectNetCumPos);
				accountGroup.setThresholdMin1(firstMinimum);
				accountGroup.setThresholdMin2(secondMinimum);
				accountGroup.setThresholdMax1(firstMaximum);
				accountGroup.setThresholdMax2(secondMaximum);
				accountGroup.setFilterCondition(filterCondition);
				accountGroup.setCreateDate(createDate);
				accountGroup.setCreatedByUser(SwtUtil.getCurrentUserId(request
						.getSession()));
				accountGroup.setMainAgent(mainAgent);
				accountGroup.setMinNcpThreshold(minNcpThreshold);
				accountGroup.setMaxNcpThreshold(maxNcpThreshold);
				accountGroup.setCreateThroughputRatio(throughputRatio);
				if ((!SwtUtil.isEmptyOrNull(thresh1Percent))) {
					accountGroup.setThresh1Percent(Integer.parseInt(thresh1Percent));
				}
				if ((!SwtUtil.isEmptyOrNull(thresh2Percent))) {
					accountGroup.setThresh2Percent(Integer.parseInt(thresh2Percent));
				}
				accountGroup.setThresh1Time(thresh1Time);
				accountGroup.setThresh2Time(thresh2Time);
				ilmGeneralMaintenanceManager
						.saveAccountGroupDetails(accountGroup);

				if ((!SwtUtil.isEmptyOrNull(accountsToAdd))) {
					saveStatus = ilmGeneralMaintenanceManager
							.saveAccountGroupSubAccounts(hostId, entityId,
									selectedAccountGroup, accountsToAdd, null);
				}
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Data fetch OK");
			}

			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName()
					+ " - [saveAccountGroupdetails] - Exit");
			return getView("saveaccountgroup");
		} catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAccountGroupdetails] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveCurrency] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAccountGroupdetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"saveAccountGroupdetails",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");

		} finally {
			// Nullify the objects
			selectedAccountGroup = null;
			entityId = null;
			currencyCode = null;
			privatePublic = null;
			type = null;
			name = null;
			description = null;
			firstMinimum = null;
			secondMinimum = null;
			firstMaximum = null;
			secondMaximum = null;
			filterCondition = null;
			accountsToDelete = null;
			accountsToAdd = null;
			accountGroup = null;
			hostId = null;
			allowReporting = null;
			correspondentBank = null;
			collectNetCumPos = null;
		}
	}

	public String updateAccountGroupdetails() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String selectedAccountGroup = null;
		String entityId = null;
		String currencyCode = null;
		String privatePublic = null;
		String type = null;
		String name = null;
		String description = null;
		String firstMinimum = null;
		String secondMinimum = null;
		String firstMaximum = null;
		String secondMaximum = null;
		String filterCondition = null;
		String accountsToDelete = null;
		String accountsToAdd = null;
		ILMAccountGroup accountGroup = null;
		String hostId = null;
		String filterClause = "SUCCESS";
		Boolean saveStatus = false;
		ActionMessages errors = null;
		String defaultLegendText = null;
		String allowReporting = null;
		String correspondentBank = null;
		String collectNetCumPos = null;
		String mainAgent = null;
		String minNcpThreshold = null;
		String maxNcpThreshold = null;
		String throughputRatio = null;
		String thresh1Time = null;
		String thresh2Time = null;
		String thresh1Percent = null;
		String thresh2Percent = null;

		try {

			log.debug(this.getClass().getName()
					+ " - [updateAccountGroupdetails] - Entry");
			errors = new ActionMessages();
			opTimer.start("all");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// get the selected Account Group Id form request
			selectedAccountGroup = request.getParameter("selectedAccountGroup");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			privatePublic = request.getParameter("privatePublic");
			if(!SwtUtil.isEmptyOrNull(privatePublic)) {
				privatePublic = privatePublic.toUpperCase();
			}else {
				privatePublic ="PRIVATE";
			}
			type = request.getParameter("type");
			name = request.getParameter("name");
			description = request.getParameter("description");
			defaultLegendText = request.getParameter("defaultLegendText");
			allowReporting = request.getParameter("allowReporting");
			correspondentBank = request.getParameter("correspondentBank");
			collectNetCumPos = request.getParameter("collectNetCumPos");
			firstMinimum = request.getParameter("firstMinimum");
			secondMinimum = request.getParameter("secondMinimum");
			firstMaximum = request.getParameter("firstMaximum");
			secondMaximum = request.getParameter("secondMaximum");
			filterCondition = request.getParameter("filterCondition");
			accountsToDelete = request.getParameter("accountsToDelete");
			accountsToAdd = request.getParameter("accountsToAdd");
			mainAgent = request.getParameter("mainAgentText");
			minNcpThreshold = request.getParameter("minNcpThreshold");
			maxNcpThreshold = request.getParameter("maxNcpThreshold");
			throughputRatio = request.getParameter("throughputRatio");
			thresh1Time = request.getParameter("thresh1Time");
			thresh2Time = request.getParameter("thresh2Time");
			thresh1Percent = request.getParameter("thresh1Percent");
			thresh2Percent = request.getParameter("thresh2Percent");
			if (!SwtUtil.isEmptyOrNull(filterCondition))
				filterClause = ilmGeneralMaintenanceManager
						.getFilterConditionResult(filterCondition);
			if (!filterClause.equals("SUCCESS")) {
				request.setAttribute("exceptionInQuery", filterClause);
				request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
				request.setAttribute("reply_message", filterClause);
			} else {

				// get the the second account group from ComboBox form request
				accountGroup = ilmGeneralMaintenanceManager
						.getEditableDataDetailList(selectedAccountGroup);

				if (accountGroup != null) {
					accountGroup.setEntityId(entityId);
					accountGroup.setCurrencyCode(currencyCode);
					accountGroup.setPublicPrivate(privatePublic);
					accountGroup.setGroupType(type);
					accountGroup.setIlmGroupName(name);
					accountGroup.setIlmGroupDescription(description);
					accountGroup.setDefaultLegendText(defaultLegendText);
					accountGroup.setAllowReporting(allowReporting);
					accountGroup.setCorrespondentBank(correspondentBank);
					accountGroup.setCollectNetCumPos(collectNetCumPos);
					accountGroup.setThresholdMin1(firstMinimum);
					accountGroup.setThresholdMin2(secondMinimum);
					accountGroup.setThresholdMax1(firstMaximum);
					accountGroup.setThresholdMax2(secondMaximum);
					accountGroup.setFilterCondition(filterCondition);
					accountGroup.setMainAgent(mainAgent);
					accountGroup.setMinNcpThreshold(minNcpThreshold);
					accountGroup.setMaxNcpThreshold(maxNcpThreshold);
					accountGroup.setCreateThroughputRatio(throughputRatio);
					if ((!SwtUtil.isEmptyOrNull(thresh1Percent))) {
						accountGroup.setThresh1Percent(Integer.parseInt(thresh1Percent));
					}
					if ((!SwtUtil.isEmptyOrNull(thresh2Percent))) {
						accountGroup.setThresh2Percent(Integer.parseInt(thresh2Percent));
					}
					accountGroup.setThresh1Time(thresh1Time);
					accountGroup.setThresh2Time(thresh2Time);
					ilmGeneralMaintenanceManager
							.updateAccountGroupDetails(accountGroup);
				}else {
					return saveAccountGroupdetails();
				}

				if ((!SwtUtil.isEmptyOrNull(accountsToAdd))
						|| (!SwtUtil.isEmptyOrNull(accountsToDelete))) {
					saveStatus = ilmGeneralMaintenanceManager
							.saveAccountGroupSubAccounts(hostId, entityId,
									selectedAccountGroup, accountsToAdd,
									accountsToDelete);
				}
				request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
				request.setAttribute("reply_message", "Data fetch OK");
			}

			opTimer.stop("all");
			// set the attribute for opTimes,lastRefTime in request
			request.setAttribute("opTimes", opTimer.getOpTimes());
			log.debug(this.getClass().getName()
					+ " - [updateAccountGroupdetails] - Exit");
			return getView("saveaccountgroup");
		} catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateAccountGroupdetails] method : - "
					+ swtExp.getMessage());

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute(
					"reply_location",
					swtExp.getStackTrace()[0].getClassName() + "."
							+ swtExp.getStackTrace()[0].getMethodName() + ":"
							+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("detailsdataerror");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateAccountGroupdetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"updateAccountGroupdetails",
							ILMGeneralMaintenanceAction.class), request, "");
			return getView("fail");

		} finally {
			// Nullify the objects
			selectedAccountGroup = null;
			entityId = null;
			currencyCode = null;
			privatePublic = null;
			type = null;
			name = null;
			description = null;
			firstMinimum = null;
			secondMinimum = null;
			firstMaximum = null;
			secondMaximum = null;
			filterCondition = null;
			accountsToDelete = null;
			accountsToAdd = null;
			accountGroup = null;
			hostId = null;
			allowReporting = null;
			correspondentBank = null;
			collectNetCumPos = null;
		}
	}

	/**
	 * Method called to populate request with account group list
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putAccountGroupsListInReq(HttpServletRequest request,
										   String hostId, String entityId, String currencyCode,
										   boolean putAllLabel) throws SwtException {

		log.debug(this.getClass().getName()
				+ " - [putAccountGroupsListInReq] - " + "Entry");
		ArrayList accountGroupList = null;
		accountGroupList = new ArrayList();
		Collection accountListFromDB = ilmGeneralMaintenanceManager
				.getAccountGroupsList(hostId, entityId, currencyCode);

		/*
		 * Adding a new LabelValueBean object with the Key as 'ALL' and value as
		 * 'ALL'
		 */
		if (putAllLabel)
			accountGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

		// Adding the currencyList object to collection object
		accountGroupList.addAll(accountListFromDB);

		request.setAttribute("accountGroups", accountGroupList);
		log.debug(this.getClass().getName()
				+ " - [putAccountGroupsListInReq] - " + "Exit");
	}

	/**
	 * Method to set column width in request attribute
	 *
	 * @param request
	 */
	private void bindColumnWidthInRequest(HttpServletRequest request) {

		String width = null;
		HashMap<String, String> widths = null;
		String[] props = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + "Entry");
			/* Read the user preferences for column width from property value */
			width = SwtUtil.getPropertyValue(request, menuItemId, "display",
					"column_width");
			/* Condition to set default column width */
			/* Set default width for columns */
			if (SwtUtil.isEmptyOrNull(width)) {
				/* Check if fromWorkFlow flag is set to true */
				width = "accountIdName=190,type=90,class=75,level=75";
			}
			widths = new HashMap<String, String>();
			/* Get column width for each column */
			props = width.split(",");
			/* Loop to separate column and width value in hash map */
			for (int i = 0; i < props.length; i++) {
				if (props[i].indexOf("=") != -1) {
					String[] propval = props[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}
			log.debug(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + "Exit");
			request.setAttribute("column_width", widths);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [bindColumnWidthInRequest] - " + e.getMessage());
		} finally {
			// nullify the objects
			widths = null;
			props = null;
		}
	}

	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {
		return getCurrencyFullAccessList(request, hostId, entityId, true);
	}

	private Collection<LabelValueBean> getCurrencyFullOrViewAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {
		return getCurrencyFullAccessList(request, hostId, entityId, false);
	}

	/**
	 *
	 * This function returns the collection of LabelValueBean objects for a role
	 * id and entity id which have the full access.
	 * @return - currrencyList
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId, boolean fullOnly)
			throws SwtException {

		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyMap object
		Map<String, String> currencyMap = null;
		// Variable to hold the currrencyList object
		Collection<LabelValueBean> currrencyList = null;
		// Variable to hold the itrCurrencyKey object
		Iterator<String> itrCurrencyKey = null;
		// String Variable to hold the currencyId
		String currencyId = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currrencyList = new ArrayList<LabelValueBean>();
			/* Returns the currency Access List based on the Role */
			if (fullOnly == true) {
				currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
						entityId);
			}else {
				currencyMap = SwtUtil.getCurrencyFullOrViewAccessMap(roleId, hostId,
						entityId);
			}


			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					currencyId = itrCurrencyKey.next();

					// add labelvaluebean for currency id
					currrencyList.add(new LabelValueBean((String) currencyMap
							.get(currencyId), currencyId));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Exit ");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in ILMGeneralMaintenanceAction.'getCurrencyFullAccessList' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log.error("Exception Catch in ILMGeneralMaintenanceAction.'getCurrencyFullAccessList' method : "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyMap = null;
			itrCurrencyKey = null;
			currencyId = null;
		}
		return currrencyList;
	}

	/**
	 * Return a complete query from filter of an account group
	 *
	 * @param group
	 * @return
	 */
	private String createQueryFormFilter(ILMAccountGroup group) {

		StringBuffer buffer = new StringBuffer();
		buffer.append(SwtConstants.ACCOUNT_GROUP_QUERY_CLAUSE);
		buffer.append("HOST_ID = '" + group.getHostId() + "'");
		buffer.append("AND ENTITY_ID = '" + group.getEntityId() + "'");
		buffer.append("AND CURRENCY_CODE = '" + group.getCurrencyCode() + "'");
		buffer.append("AND " + group.getFilterCondition());

		return buffer.toString();
	}

	/**
	 * Return the global group for an entity and currency combination
	 * @return
	 * @throws SwtException
	 */
	public String getGlobalGroup() throws SwtException {

		String entityId = null;
		String currencyId = null;
		String globalGrp = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + "- [getGlobalGroup] - starting ");

			entityId = request.getParameter("entityId");
			currencyId = request.getParameter("ccyCode");
			globalGrp = ilmGeneralMaintenanceManager.getGlobalGroup(entityId, currencyId);
			response.getWriter().print(globalGrp);

			log.debug(this.getClass().getName() + " - [getGlobalGroup] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGlobalGroup] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getGlobalGroup",
							ILMGeneralMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * used to display ILM Calculation Launcher Screen
	 * @return
	 * @throws Exception
	 */
	public String ilmCalculation()
			throws Exception {

		ILMCalculationForm ilmCalcul = null;
		String hostId = null;
		String roleId = null;
		String entityId = null;
		String currencyCode = null;
		Collection<LabelValueBean> entities = null;
		Collection<LabelValueBean> currenciesAvailable = null;
		Collection<LabelValueBean> currencies = null;
		Iterator<CurrencyTO> itrCcy = null;
		Iterator<LabelValueBean> itrEntity = null;
		CurrencyTO ccy = null;
		LabelValueBean ccyLVB = null;
		Collection<CurrencyTO> currenciesColl = null;
		int ccyEntityAccess;
		Boolean allIsAllowed = true;
		Boolean entityFound = false;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [ilmCalculation] - "
					+ "Entry");


			ilmCalcul = getIlmCalcul();




			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve Entity Id
			entityId = ilmCalcul.getEntityId();
			// Retrieve Currency Code
			currencyCode = ilmCalcul.getCurrencyCode();

			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			entities = ilmGeneralMaintenanceManager.getAllowedEntityList(
					hostId, roleId);
			/* The case when the entity is null, we will retrieve the default entity for the user.
			 * But this entity could be not in the list, that's why we have to select the first element instead
			 */
			if (entities != null) {
				itrEntity = entities.iterator();
				while (itrEntity.hasNext()) {
					if (itrEntity.next().getValue().equals(entityId)) {
						entityFound = true;
						break;
					}
				}
			}

			// If the entity is not found in the list, then get the first element
			if (!entityFound) {
				entityId = entities.iterator().next().getValue();
			}

			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				// get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}

			// Get the list of currencies available to add
			currenciesColl = ilmGeneralMaintenanceManager
					.getAllowedCurrencyList(hostId, roleId, entityId);
			// Put the currencies available in an arraylist that contains only label value bean
			if (currenciesColl != null) {
				itrCcy = currenciesColl.iterator();
				currenciesAvailable = new ArrayList<LabelValueBean>();
				while (itrCcy.hasNext()) {
					ccy = itrCcy.next();
					ccyLVB = new LabelValueBean(ccy.getCurrencyName(),
							ccy.getCurrencyId());
					currenciesAvailable.add(ccyLVB);
					// Don't allow to show the All option in the list if any currency does not have full access
					if (ccy.getCurrencyAccess() != 0)
						allIsAllowed = false;
				}
			}

			currencies = new ArrayList<LabelValueBean>();

			// If a currency does not have a full access, then the All option should not be shown
			if (!currenciesColl.isEmpty()) {
				if (allIsAllowed) {
					currencies.add(new LabelValueBean(SwtConstants.ALL_LABEL,
							SwtConstants.ALL_VALUE));
				} else {
					// If All is not available, then select the first element in the currency list
					currencyCode = currenciesAvailable.iterator().next().getValue();
				}
			}

			// Add the All value before if it is available, then add the other currencies
			currencies.addAll(currenciesAvailable);

			/*ReportsManager reportsManager = (ReportsManager) (SwtUtil
					.getBean("reportsManager"));
			sysDateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(
					entityId, currencyCode, SwtUtil.getSystemDateFromDB());*/


			ilmCalcul.setEntityId(entityId);
			ilmCalcul.setCurrencyCode(currencyCode);
			ilmCalcul.setProcessOption("A");
			ilmCalcul.setSingleOrRange("S");
			//ilmCalcul.setFromDateAsString(startDate);
			request.setAttribute("processOption", "A");
			request.setAttribute("singleOrRange", "S");
			request.setAttribute("ilmCalcul", ilmCalcul);
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedCurrencyCode", currencyCode);
			request.setAttribute("entities", entities);
			request.setAttribute("currencies", currencies);
			request.setAttribute("ccyIsEmpty", currencies.isEmpty());
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));

			// Get the access of the currency for the user
			ccyEntityAccess = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			request.setAttribute("ccyEntityAccess", ccyEntityAccess);
			log.debug(this.getClass().getName() + " - [ilmCalculation] - "
					+ "Exit");
			return getView("ilmcalculation");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [ilmCalculation] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ilmCalculation] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(e, "ilmCalculation",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * Get details of currency process status
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String listCurrencyProcessStatus() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		ArrayList<CcyProcessStatus> ccyProcessStatusList = null;
		Object[] ccyProcessStatusResult = null;
		String dateFormat = null;
		String hostId = null;
		String userId = null;
		String roleId = null;
		String entityId = null;
		String currencyCode = null;
		String processOption = null;
		String singleOrRange  = null;
		Date startDate = null;
		Date endDate = null;
		String selectedStartDate = null;
		String selectedEndDate = null;
		String overwrite = null;
		CommonDataManager CDM;
		String sequenceNumber = null;
		Date sysDateInCcyTimeframe = null;
		int ccyEntityAccess = 0;
		Collection<LabelValueBean> entities = null;
		Collection<LabelValueBean> currenciesAvailable = null;
		Collection<LabelValueBean> currencies = null;
		Iterator<CurrencyTO> itrCcy = null;
		Iterator<LabelValueBean> itrEntity = null;
		CurrencyTO ccy = null;
		LabelValueBean ccyLVB = null;
		Collection<CurrencyTO> currenciesColl = null;
		boolean allIsAllowed = true;
		boolean entityFound = false;
		boolean currencyCodeExist = false;
		Entity entity = null;
		Integer ilmCalcPastDaysValue = 30;
		Integer ilmRetainValue = 30;
		String fromDateAsString = null;
		String toDateAsString = null;
		String firstThresholdDay = null;
		String lastThresholdDay = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [listCurrencyProcessStatus] - starting ");

			dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());
			userId = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();

			entityId = request.getParameter("entityId");
			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			entities = ilmGeneralMaintenanceManager.getAllowedEntityList(hostId, roleId);
			if (entities != null) {
				itrEntity = entities.iterator();
				while (itrEntity.hasNext()) {
					if (itrEntity.next().getValue().equals(entityId)) {
						entityFound = true;
						break;
					}
				}
			}

			// If the entity is not found in the list, then get the first element
			if (!entityFound) {
				entityId = entities.iterator().next().getValue();
			}

			currencyCode = request.getParameter("currencyCode");
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}
			// Get the list of currencies available to add
			currenciesColl = ilmGeneralMaintenanceManager.getAllowedCurrencyList(hostId, roleId, entityId);
			// Put the currencies available in an arraylist that contains only label value bean
			if (currenciesColl != null) {
				itrCcy = currenciesColl.iterator();
				currenciesAvailable = new ArrayList<LabelValueBean>();
				while (itrCcy.hasNext()) {
					ccy = itrCcy.next();

					if(ccy.getCurrencyId().equalsIgnoreCase(currencyCode)) {
						currencyCodeExist = true;
					}
					ccyLVB = new LabelValueBean(ccy.getCurrencyName(),
							ccy.getCurrencyId());
					currenciesAvailable.add(ccyLVB);
					// Don't allow to show the All option in the list if any currency does not have full access
					if (ccy.getCurrencyAccess() != 0) {
						allIsAllowed = false;
					}
				}
			}

			currencies = new ArrayList<LabelValueBean>();

			// If a currency does not have a full access, then the All option should not be shown
			if (!currenciesColl.isEmpty()) {
				currencies.add(new LabelValueBean(SwtConstants.ALL_LABEL,
						SwtConstants.ALL_VALUE));
				if (!currencyCodeExist) {
					currencyCode = SwtConstants.ALL_VALUE;
				}
			}

			// Add the All value before if it is available, then add the other currencies
			currencies.addAll(currenciesAvailable);
			processOption = request.getParameter("processOption");
			if (SwtUtil.isEmptyOrNull(processOption)) {
				processOption = "A";
			}
			singleOrRange = request.getParameter("singleOrRange");
			if (SwtUtil.isEmptyOrNull(singleOrRange)) {
				singleOrRange = "S";
			}
			sysDateInCcyTimeframe = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			selectedStartDate = request.getParameter("selectedStartDate");
			sequenceNumber = request.getParameter("sequenceNumber");
			// If the startDate remains null then assign to it the current sysdate
			if(SwtUtil.isEmptyOrNull(selectedStartDate)){
				selectedStartDate = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -1), SwtUtil
						.getCurrentSystemFormats(request.getSession()).getDateFormatValue());
			}
			startDate = SwtUtil.parseDate(selectedStartDate, dateFormat);
			if ("R".equalsIgnoreCase(singleOrRange)) {
				selectedEndDate = request.getParameter("selectedEndDate");
				endDate = SwtUtil.parseDate(selectedEndDate, dateFormat);
			} else
				endDate = startDate;

			if(SwtUtil.isEmptyOrNull(request.getParameter("ccyEntityAccess"))) {
				ccyEntityAccess = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
						currencyCode);
			}else {
				ccyEntityAccess = Integer.parseInt(request.getParameter("ccyEntityAccess"));
			}
			overwrite = "Y";
			ccyProcessStatusResult = ilmGeneralMaintenanceManager
					.getccyProcessStatusDetails(dateFormat, hostId, entityId,
							currencyCode, userId, startDate, endDate,
							overwrite, processOption);
			ccyProcessStatusList = (ArrayList<CcyProcessStatus>)ccyProcessStatusResult[1];
			//ccyProcessStatusList = "E";
			request.setAttribute("lastRefTime",
					SwtUtil.getLastRefreshTime(request));
			request.setAttribute("ccyProcessStatusList", ccyProcessStatusList);
			request.setAttribute("allIsAllowed", SwtConstants.ALL_VALUE.equalsIgnoreCase(currencyCode) && !allIsAllowed?false:true);

			request.setAttribute("recordCount",
					ccyProcessStatusList != null ? ccyProcessStatusList.size() : "0");

			// Send the process status summary as it is calculated from oracle
			request.setAttribute("processStatus", (String)ccyProcessStatusResult[0]);

			// Running sequence number (if exists)
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			if (CDM != null
					&& CDM.getIlmScreenConnectionDetails() != null
					&& CDM.getIlmScreenConnectionDetails().containsKey(
					sequenceNumber)) {
				request.setAttribute("runningSeqNumber", sequenceNumber);
			} else {
				request.setAttribute("runningSeqNumber", "");
			}

			//moved from ilmCalculation to listCurrencyProcessStatus
			// Get the entity manager from which to get the max retain value
			EntityManager entityManger = (EntityManager) (SwtUtil
					.getBean("entityManager"));
			entity = new Entity();
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(hostId);
			entity = entityManger.getEntityDetail(entity);
			if (entity != null) {
				ilmCalcPastDaysValue = entity.getIlmCalcPastDays();
				if (ilmCalcPastDaysValue != null) {
					fromDateAsString = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -ilmCalcPastDaysValue),
							SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());

				} else {
					ilmRetainValue = entity.getIlmRetain();
					if (ilmRetainValue != null) {
						fromDateAsString = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -ilmRetainValue),
								SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());

					}
				}
			}

			// If the fromDateAsString remains null then assign to it the current sysdate
			if(SwtUtil.isEmptyOrNull(fromDateAsString)){
				fromDateAsString = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -1), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}

			// Caclulate the toDate as the sys_date - 1
			toDateAsString =SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -1), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			// Calendar last threshold date value
			lastThresholdDay = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, 0), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			// Calendar first threshold value
			if(ilmCalcPastDaysValue != null){
				firstThresholdDay = SwtUtil.formatDate(SwtUtil.dateAdd(sysDateInCcyTimeframe, -ilmCalcPastDaysValue - 1), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());
			}else{
				firstThresholdDay = lastThresholdDay;
			}

			//send in request the passed params

			request.setAttribute("selectedEndDate", selectedEndDate);
			request.setAttribute("selectedStartDate", selectedStartDate);
			request.setAttribute("fromDateAsString", fromDateAsString);
			request.setAttribute("toDateAsString", toDateAsString);
			request.setAttribute("lastThresholdDay", lastThresholdDay);
			request.setAttribute("firstThresholdDay", firstThresholdDay);
			request.setAttribute("selecteDefaultDate", toDateAsString);
			request.setAttribute("entityId", entityId);
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("processOption", processOption);
			request.setAttribute("singleOrRange", singleOrRange);
			request.setAttribute("sequenceNumber", sequenceNumber);
			request.setAttribute("ccyEntityAccess", ccyEntityAccess);
			request.setAttribute("entities", entities);
			request.setAttribute("currencies", currencies);

			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			log.debug(this.getClass().getName()
					+ " - [listCurrencyProcessStatus] - " + "Exit");
			return getView("ccyprocessstatus");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [listCurrencyProcessStatus] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"listCurrencyProcessStatus", ILMGeneralMaintenanceAction.class),
					request, "");
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", exp.getMessage());
			request.setAttribute(
					"reply_location",
					exp.getStackTrace()[0].getClassName() + "."
							+ exp.getStackTrace()[0].getMethodName() + ":"
							+ exp.getStackTrace()[0].getLineNumber());

			return getView("detailsdataerror");

		}
	}

	/**
	 * Launch the calculation of ILM Currency Process
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String runManualCCYProcess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		final String userId = SwtUtil.getCurrentUserId(request.getSession());
		final String hostId = SwtUtil.getCurrentHostId();

		final String entityId = request.getParameter("entityId");
		final String currencyCode = request.getParameter("currencyCode");
		final Date startDate = SwtUtil.parseDate(
				request.getParameter("selectedStartDate"),
				SwtUtil.getCurrentDateFormat(request.getSession()));
		final Date endDate = "R".equalsIgnoreCase(request.getParameter("singleOrRange"))?SwtUtil.parseDate(
				request.getParameter("selectedEndDate"),
				SwtUtil.getCurrentDateFormat(request.getSession())):null;

		final String processOption = request.getParameter("processOption");
		final String overwrite = "Y";
		final String sequenceNumber = request.getParameter("sequenceNumber");
		final CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
		String result = "N";
		try {

			log.debug(this.getClass().getName()
					+ "- [runManualCCYProcess] - starting ");

			if (CDM.getIlmScreenConnectionDetails() == null) {
				CDM.setIlmScreenConnectionDetails(new LinkedHashMap<String, Statement>());
			}


			// Check for existing calculations
			result = ilmGeneralMaintenanceManager.checkRunningProcesses(hostId, entityId, currencyCode, userId, startDate, endDate, processOption, request);

			if("S".equals(result))
			{
				// Run processing in a a dependent thread
				new Thread( new Runnable() {
					@Override
					public void run() {
						try {
							ilmGeneralMaintenanceManager.runManualCurrencyProcess(
									hostId, entityId, currencyCode, userId, startDate, endDate,
									overwrite, processOption, sequenceNumber, CDM);
						} catch (Exception exp) {
							log.error(this.getClass().getName()
									+ " - Exception Catched in [runManualCCYProcess] method : - "
									+ exp.getMessage());
							SwtErrorHandler.getInstance().handleException(exp,
									"runManualCCYProcess", ILMGeneralMaintenanceAction.class);
						}
					}
				}).start();

				result = "S";
			}
			log.debug(this.getClass().getName() + " - [runManualCCYProcess] - "
					+ "Exit");

			response.getWriter().print(result);
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [runManualCCYProcess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"runManualCCYProcess", ILMGeneralMaintenanceAction.class),
					request, "");
			try {
				response.getWriter().print("N");
			} catch (Exception e) {
				// TODO: handle exception
			}
		}
		return null;
	}

	/**
	 * Used to cancel calculation of ILM Currency Process
	 * @return
	 * @throws SwtException
	 */
	public String cancelManualCCYProcess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId = null;
		String userId = null;
		String entityId = null;
		String currencyCode = null;
		Date startDate = null;
		Date endDate = null;
		String processOption = null;
		String sequenceNumber = null;
		CommonDataManager CDM;
		CallableStatement cstmt = null;
		String result = "F";
		try {
			log.debug(this.getClass().getName()
					+ "- [cancelManualCCYProcess] - starting ");
			userId = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();

			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			startDate = SwtUtil.parseDate(
					request.getParameter("selectedStartDate"),
					SwtUtil.getCurrentDateFormat(request.getSession()));
			if ("R".equalsIgnoreCase(request.getParameter("singleOrRange")))
				endDate = SwtUtil.parseDate(
						request.getParameter("selectedEndDate"),
						SwtUtil.getCurrentDateFormat(request.getSession()));
			processOption = request.getParameter("processOption");
			sequenceNumber = request.getParameter("sequenceNumber");
			CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
			if (CDM != null
					&& CDM.getIlmScreenConnectionDetails() != null
					&& CDM.getIlmScreenConnectionDetails().containsKey(
					sequenceNumber)) {
				cstmt = (CallableStatement) CDM.getIlmScreenConnectionDetails()
						.get(sequenceNumber);
				CDM.getIlmScreenConnectionDetails().remove(sequenceNumber);
				try {
					cstmt.cancel();
					result = "S";
				} catch (Exception e) {
				}
				JdbcUtils.closeStatement(cstmt);
				ilmGeneralMaintenanceManager.updateProcessStatus(hostId, entityId, currencyCode, userId, startDate, endDate, processOption, "C");
			}

			log.debug(this.getClass().getName()
					+ " - [cancelManualCCYProcess] - " + "Exit");
			response.getWriter().print(result);

		} catch (Exception exp) {
			try {
				response.getWriter().print("F");
			} catch (Exception e) {
			}
			log.error(this.getClass().getName()
					+ " - Exception Catched in [cancelManualCCYProcess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"cancelManualCCYProcess", ILMGeneralMaintenanceAction.class),
					request, "");

		}
		return null;
	}

	/**
	 * Get the user access for an entity currency combination using AJAX
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getUserAccess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String entityId = null;
		String currencyCode = null;
		int ccyEntityAccess;
		try {
			log.debug(this.getClass().getName()
					+ "- [getUserAccess] - starting ");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			/* When an entity is shown in the list but we have no currency in the list, that's why we receive an empty currency code.
			 * So we have to set automatically the response as 1 in order to disable the process button
			 */
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				response.getWriter().print("1");
				return null;
			}
			ccyEntityAccess = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			log.debug(this.getClass().getName() + " - [getUserAccess] - "
					+ "Exit");
			response.getWriter().print(ccyEntityAccess + "");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getUserAccess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getUserAccess", ILMGeneralMaintenanceAction.class),
					request, "");
			try {
				response.getWriter().print("1");
			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [getUserAccess] method : - "
						+ exp.getMessage());
				SwtUtil.logException(
						SwtErrorHandler.getInstance().handleException(exp,
								"getUserAccess", ILMGeneralMaintenanceAction.class),
						request, "");
			}
		}
		return null;
	}

}
