/* @(#)PartyAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.StringTokenizer;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.springframework.context.ApplicationContext;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.*;
import org.swallow.maintenance.service.PartyManager;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;


/**
 *
 * This is action class for Party MAintenace screen used to
 * display,add,change,delete Party. Also display,Add,Change and Delete Alias for
 * a party.
 *
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;

@Scope("prototype")
@Controller
@RequestMapping(value = {"/party", "/party.do"})
public class PartyAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/partymaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("addAlias", "jsp/maintenance/partyaliaschild");
		viewMap.put("success", "jsp/maintenance/party");
		viewMap.put("change", "jsp/maintenance/partymaintenanceadd");
		viewMap.put("alias", "jsp/maintenance/partyalias");
		viewMap.put("searchParty", "jsp/maintenance/partysearch");
		viewMap.put("fail", "error");
		viewMap.put("data", "jsp/data"); // For XML/JSON responses to Angular
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "next":
				return next();
			case "displayList":
				return displayList();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "update":
				return update();
			case "delete":
				return delete();
			case "preSearchParties":
				return preSearchParties();
			case "searchParties":
				return searchParties();
			case "search":
				return search();
			case "displayAliasDetails":
				return displayAliasDetails();
			case "addAlias":
				return addAlias();
			case "saveAliasRecord":
				return saveAliasRecord();
			case "deleteAliasRecord":
				return deleteAliasRecord();
			case "checkParentId":
				return checkParentId();
			case "checkPartyUse":
				return checkPartyUse();
			// New methods for Angular
			case "displayAngular": return displayAngular();
			case "checkPartyUseAngular": return checkPartyUseAngular();
			case "deletePartyAngular": return deletePartyAngular();
			// case "savePartyAngular": return savePartyAngular(); // If Angular popups submit this way
			case "savePartyColumnOrder": return savePartyColumnOrder();
			case "savePartyColumnWidth": return savePartyColumnWidth();

		}


		return unspecified();
	}


	private Party party;
	public Party getParty() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		party = RequestObjectMapper.getObjectFromRequest(Party.class, request);
		return party;
	}

	public void setParty(Party party) {
		this.party = party;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("party", party);
	}


	@Autowired
	private PartyManager partyManager = null;
	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(PartyAction.class);
	private ApplicationContext ctx = null;

	/**
	 * This method is used to set the Party Manager
	 *
	 * @param partyManager
	 * @return
	 */
	public void setPartyManager(PartyManager partyManager) {
		this.partyManager = partyManager;
	}

	/**
	 * This method is default method in struts This method is called if no
	 * method are specified in the request
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName()
				+ "- [unspecified] - return displayList method ");
		return displayList();
	}

	/**
	 * Method added for setting the page numbers.
	 *
	 * @param EntityId
	 * @param CurrentPage
	 * @param MaxPage
	 * @param MovTotalCount
	 * @param PageSummaryList
	 *
	 */
	private void setPageSummaryList(String entityId, int currentPage,
									int maxPage, int movTotalCount,
									ArrayList<PartySummary> pageSummaryList) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ "- [setPageSummaryList] - Entering ");
			/* Class Instance declaration */
			PartySummary pSummary = new PartySummary();
			pSummary.setEntityId(entityId);
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(movTotalCount);
			pageSummaryList.add(pSummary);
			log.debug(this.getClass().getName()
					+ "- [setPageSummaryList] - Exiting ");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [setPageSummaryList] method : - "
							+ exp.getMessage());

		}
	}

	/**
	 * This method is used for pagination.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String next()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable Declaration to hold hostId
		String hostId = null;
		// Declaration to hold the partID
		String partyId = null;
		// Declaration to hold the partName
		String partyName = null;
		// Declaration to hold the entityId
		String entityId = null;
		// Declaration to hold the partyType
		String partyType = null;
		// Declaration to hold the orderBy
		String orderBy = null;
		// Declaration to hold the sorting value
		String sortBy = null;
		// Declaration to hold the page number
		String clckPageStr = null;
		// Declaration to hold the current page number
		String currPageStr = null;
		// declared to hold the access value
		int accessInd;
		// declared to hold the maximum page number
		int maxPage;
		// declared to hold the page size
		int pageSize;
		// declared hold the startIndex
		int startIndex;
		// declared to hold the total number of records
		int totalCount;
		// declared to hold the end index
		int endIndex;
		// declared to hold the current page variable
		int clickedPage;
		// declared to hold the current page variable
		int currentPage;
		// declared to hold the value which determine the next page is available
		String nextLinkStatus = null;
		// declared to hold the value which determine the previous page is
		// available
		String prevLinkStatus = null;
		// collection to hold the party list
		Collection<Party> partyList = null;
		// Declaration to hold the form
		// DynaValidatorForm dyForm = null;
		// Declaration to hold the object
		Party custodian = null;
		// Declaration to hold the filter sort values
		String filterFromSort = null;
		// Declaration to hold the filter criteria
		String filterCriteria = null;
		// Declaration to hold the filter criteria
		String[] filterCriteriaArray = null;
		// tokens to hold the filter values
		StringTokenizer tokenFilter = null;
		// variable declaration for count
		int count;
		// variable Declaration for HashMap
		HashMap<String, String> strTokenMap = null;
		// variable Declaration to hold the tokens
		StringTokenizer strToken = null;
		// Variable Declaration for countTokens
		int countToken;
		// Variable Declaration to hold filter values
		String[] strHashArray = null;
		// Variable Declaration to hold the filter criteria
		StringBuffer filterCriteriaStrBuffer = null;
		// variable Declaration to hold order by value
		int intOrderBy;
		// variable declaration to hold the tokens
		StringTokenizer strTokenFiletr = null;
		// get the filter count values
		int filterCount;
		// get the Array list
		ArrayList<PartySummary> pageSummaryList = null;
		// hold the filter values
		StringBuffer tempCurrentFilter = null;
		// hold the filter sort status
		StringBuffer filterSortStatus = null;
		// holds the current filter value
		String currentFilter = null;
		// Holds the current filter value
		String currentSort = null;
		// integer to hold the filterCriteriaArray count
		int filterCriteriaArrayCount;
		// integer to hold the HashArrayCount
		int hashArrayCount;

		try {
			log.debug(this.getClass().getName() + "- [next] - Entering ");
			/* Method's local variable declaration */
			accessInd = 0;
			currentPage = 0;
			count = 0;
			countToken = 0;
			intOrderBy = 0;
			filterCount = 0;
			hostId = CacheManager.getInstance().getHostId();
			custodian = (Party) getParty();
			/* Use to put the entity List in request */
			putEntityListInReq(request);
			/* Reading party id & party name from request */
			partyId = request.getParameter("partyId");
			partyName = request.getParameter("partyName");
			if (("".equals(partyName)) && (partyName != null)) {

				if ((request.getParameter("party.partyName") != null))
					partyName = request.getParameter("party.partyName");

			}
			// condition for checking partyName
			if (("".equals(partyId)) && (partyId != null)) {

				if ((request.getParameter("party.id.partyId") != null))
					partyId = request.getParameter("party.id.partyId");

			}
			// getting entityId from request
			entityId = request.getParameter("entityId");
			/* Condition to check entity Id equal to null */
			if (SwtUtil.isEmptyOrNull(entityId)) {
				/* Getting user current entity from Session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			/* Setting entity id from bean class */
			custodian.getId().setEntityId(entityId);
			/* Retrieve filter value from request value parameter selectedFilter */
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selected sort */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the current sort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {
				currentSort = "0|false";
			}
			filterFromSort = request.getParameter("filterFromSerach");
			/* Getting filter criteria values from request */
			filterCriteria = request.getParameter("filterCriteria");
			if (currentSort == null) {
				currentSort = "none";
			}
			/*
			 * Condition to check filter from sort is null If null then display
			 * by defauLt filtering otherwise it filtered the values by user
			 * selection.
			 */
			if ((filterFromSort != null) && filterFromSort.equals("true")) {

				tokenFilter = new StringTokenizer(filterCriteria, "|");
				count = tokenFilter.countTokens();
				filterCriteriaArray = new String[count];
				filterCriteriaArrayCount = 0;
				while (tokenFilter.hasMoreTokens()) {
					filterCriteriaArray[filterCriteriaArrayCount] = tokenFilter
							.nextToken();
					filterCriteriaArrayCount++;
				}
				/* Creating an instance for hash map */
				strTokenMap = new HashMap<String, String>(27);
				for (filterCriteriaArrayCount = 0; filterCriteriaArrayCount < count; filterCriteriaArrayCount++) {
					strToken = new StringTokenizer(
							filterCriteriaArray[filterCriteriaArrayCount], "=");
					countToken = strToken.countTokens();
					strHashArray = new String[2];
					hashArrayCount = 0;
					while (strToken.hasMoreTokens()) {
						strHashArray[hashArrayCount] = strToken.nextToken();
						hashArrayCount++;
					}

					if (countToken == 1) {
						strHashArray[hashArrayCount] = "";
					}
					strTokenMap.put(strHashArray[0], strHashArray[1]);
				}
				request.setAttribute("filterCriteria", filterCriteria);
			} else {
				/* Reading Party id from request */
				filterCriteriaStrBuffer = new StringBuffer("partyId=");
				/* Appending Party id in buffer */
				filterCriteriaStrBuffer.append(partyId + "|");
				/* Appending Party id name buffer */
				filterCriteriaStrBuffer.append("partyName=");
				filterCriteriaStrBuffer.append(partyName + "|");
				/* Appending Party type in buffer */
				partyType = request.getParameter("partyType");
				filterCriteriaStrBuffer.append("partyType=");
				filterCriteriaStrBuffer.append(partyType + "|");
				request.setAttribute("filterCriteria", filterCriteriaStrBuffer
						.toString());
			}
			/* Default setting for order by is party id */
			orderBy = "party_id";
			/* Default setting for sorting is Ascending */
			sortBy = "Asc";
			/*
			 * Condition to check Current sort by index value if sort is not
			 * null then set by index value 0-->party id 1-->party name
			 * 2-->party type 4-->alias name
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";

			}
			/* Set order by using bean class */
			custodian.setOrderBy(orderBy);
			/* Set sort by using bean class */
			custodian.setSortBy(sortBy);

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				strTokenFiletr = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (strTokenFiletr.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityId, strTokenFiletr
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								strTokenFiletr.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Form filterSortStatus from current filter */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter);
			}
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/*
			 * Retrieve and Store the user's menu,entity and currency group in
			 * collection variable
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/* Condition to check the access rights. */
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/* Setting Maximum Page value to zero */
			maxPage = 0;
			/* Reading the current page value from the request */
			currPageStr = request.getParameter("currentPage");
			/* Reading the Page number value from the request */
			clckPageStr = request.getParameter("goToPageNo");
			/*
			 * Condition to check current Page not equal to null &Page no equal
			 * to null
			 */
			if ((currPageStr != null) && (clckPageStr == null)) {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the current page value from the request */
				clickedPage = 0;
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the current maximum page value from the request */
				maxPage = Integer.parseInt(request.getParameter("maxPages"));
			} else {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				clickedPage = Integer.parseInt(request
						.getParameter("goToPageNo"));
				/* Reading the current maximum page value from the request */
				maxPage = Integer.parseInt(request.getParameter("maxPages"));
				/* Condition to check the previous link clicked */
				if (clickedPage == -2) { // Previous Link Clicked
					currentPage--;
					clickedPage = currentPage;
					/* Condition to check the Next link clicked */
				} else {
					if (clickedPage == -1) { // Next Link Clicked
						currentPage++;
						clickedPage = currentPage;
					} else {
						currentPage = clickedPage;
					}
				}
			}
			/*
			 * Condition to check the clicked page.If Clicked page greater than
			 * 1 then previous link enable. If Clicked page less than 1 then
			 * previous link disable
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check the clicked page.If Clicked page less than
			 * maximum page then next link enable. If Clicked page greater than
			 * maximum page then next link disable
			 */

			pageSize = 0;
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			/* Setting end index & Start index by doing calculation */
			endIndex = 0;
			endIndex = currentPage * pageSize;
			startIndex = 0;
			startIndex = endIndex - pageSize;
			/* Setting Party id & party name after trimming from bean class */
			custodian.getId().setPartyId(partyId.trim());
			custodian.setPartyName(partyName.trim());
			/* Setting host id ,Filter criteria from bean class */
			custodian.getId().setHostId(hostId);
			custodian.setFilterCriteria(filterSortStatus.toString());
			/* Setting total count to zero */
			totalCount = 0;

			/* Condition to check Filter criteria equal to null */
			if (filterCriteria == null) {
				/*
				 * This method is used to get the total count from DB based on
				 * the parameter values passed.
				 */
				totalCount = partyManager.getTotalCount(custodian);
			} else {
				/*
				 * This method is used to get the filter count from DB based on
				 * the parameter values passed.
				 */
				totalCount = partyManager.getFilterCount(custodian);
			}
			/*
			 * Condition to check Total count less than or equal to start index
			 * value
			 */
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			if (totalCount <= startIndex) {
				// if total count is 0 reset screen to initial values
				if (totalCount > 0) {
					currentPage = currentPage - 1;
					endIndex = currentPage * pageSize;
					startIndex = endIndex - pageSize;
				} else {
					custodian.setStartIndex(0);
					custodian.setEndIndex(pageSize);
					clickedPage = 0;
					filterCriteria = "All|All|All|All|All";
					currentFilter = "All|All|All|All|All";
					currentSort = "0|false";
					custodian.setFilterCriteria(filterCriteria);
					totalCount = partyManager.getFilterCount(custodian);
				}
			}
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* This is used to sets maximum Page */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				// if entered page no is greater than max page then
				nextLinkStatus = "false";
				// assigning maxPage value to currentPage
				currentPage = maxPage;
				// values for startIndex & endIndex
				endIndex = currentPage * pageSize;
				startIndex = endIndex - pageSize;
			}
			// setting values to bean
			custodian.setStartIndex(startIndex);
			custodian.setEndIndex(endIndex);
			/* Retrieve the custodian List in collection variable */
			partyList = partyManager.getCustodianList(custodian);
			request.setAttribute("custodianColl", partyList);
			pageSummaryList = new ArrayList<PartySummary>();
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			/*
			 * Hide pagination if no of records are "0" or records can be
			 * displayed in one page.
			 */
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("pageNoValue", clckPageStr);
			request.setAttribute("filterCriteria", filterCriteria);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			setParty(custodian);

			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "next", PartyAction.class), request, "");

			return getView("fail");
		} finally {

			hostId = null;
			partyId = null;
			partyName = null;
			entityId = null;
			partyType = null;
			orderBy = null;
			sortBy = null;
			clckPageStr = null;
			currPageStr = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			partyList = null;
			filterFromSort = null;
			filterCriteria = null;
			filterCriteriaArray = null;
			tokenFilter = null;
			strToken = null;
			strHashArray = null;
			filterCriteriaStrBuffer = null;
			strTokenFiletr = null;
			pageSummaryList = null;
			tempCurrentFilter = null;
			filterSortStatus = null;
			currentFilter = null;
			currentSort = null;
			strTokenMap = null;

			log.debug(this.getClass().getName() + "- [next] - Exiting");
		}
	}

	/**
	 * This method is used to display the party details in screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return Action forward
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declared for to hold the selected filter in screen
		String currentFilter = null;
		// variable declared for to hold the selected sort in screen
		String currentSort = null;
		// Variable Declaration for filter & sort
		String filterFromSort = null;
		// Variable Declaration for filterCriteria
		String filterCriteria = null;
		// Variable Declaration for getting filter and sort status
		StringBuffer filterSortStatus = null;
		// Declaration to get the no of tokens in the filter criteria
		int filterCriteriaCount;
		// Declaration to get the page size
		int pageSize;
		// Declaration to get the current page
		int currentPage;
		// Declaration to get the Entity and currency group access status
		int accessInd;
		// get the party list
		Collection<Party> partyList = null;
		// Declaration to get the total number of records
		int totalCount;
		// Array list Declaration to hold the page wise records
		ArrayList<PartySummary> pageSummaryList = null;
		// declaration of flag to determine available of more than one page
		boolean isNext = false;
		// String Declaration for getting order by status
		String orderBy = null;
		// String Declaration for getting sort by status
		String sortBy = null;
		// string to hold the current filter values
		StringBuffer tempCurrentFilter = null;
		// String to hold host id
		String hostId = null;
		// Object to hold the party values
		Party custodian = null;
		// String to hold the
		String entityId = null;
		// Tokenizer for current filter
		StringTokenizer filterTokenezier = null;
		// filter count for get the count of tokens
		int filterCount;
		// string array to hold the filter criteria
		String[] filterCriteriaArray = null;
		// HashMap to hold the filter tokens
		HashMap<String, String> tokenHashMap = null;
		// tokens to hold the filter sort values
		StringTokenizer filterStrToken = null;
		// variable to hold total number of tokens
		int countToken;
		// variable to hold the tokens
		String[] strHashArray = null;
		// variable to hold the party id
		String partyId = null;
		// variable to hold the filter criteria
		StringBuffer filterCriteriaStrBuffer = null;
		// variable to hold the party name
		String partyName = null;
		// variable to hold the part type
		String partyType = null;
		// integer to hold the order by column value
		int intOrderBy;
		// integer to hold the maximum page number
		int maxPage;
		// integer to hold the filterTokenizer count
		int filterTokenizerCount;
		// integer to hold the StringTokenizer count
		int StringTokenizerCount;
		// variable to hold the form
		// DynaValidatorForm dyForm = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "- [displayList] - Entering ");
			filterCriteriaCount = 0;
			pageSize = 0;
			accessInd = 0;
			totalCount = 0;
			filterCount = 0;
			countToken = 0;
			intOrderBy = 0;
			maxPage = 0;
			currentPage = 1;
			/* Method's local variable declaration */
			// Setting maximum page to zero
			/* Retrieve filter value from request value parameter selectedFilter */
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selected sort */
			currentSort = request.getParameter("selectedSort");

			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			// setting the form
			custodian = (Party) getParty();
			// getting partyId and partyName from request
			partyId = request.getParameter("partyId");
			partyName = request.getParameter("partyName");
			// condition for checking partyId
			if (("".equals(partyName)) && (partyName != null)) {

				if ((request.getParameter("party.partyName") != null))
					partyName = request.getParameter("party.partyName");

			}
			/* This method is used to put the entity value in request */
			putEntityListInReq(request);
			/* This method is used to get the entity id from user */
			/* Retrieve the entity id from bean classes */
			entityId = custodian.getId().getEntityId();
			/*
			 * Condition to check entity from request parameter If entityId is
			 * null then set the user's selected entity.
			 */
			if (SwtUtil.isEmptyOrNull(entityId)) {
				/* Retrieve the current user's entity */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the use's selected entity id */
				custodian.getId().setEntityId(entityId);
			}
			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterTokenezier = new StringTokenizer(currentFilter, "|");
				filterCount = 0;

				while (filterTokenezier.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityId, filterTokenezier
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterTokenezier.nextToken()).append("|");
					}
				}

			}

			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the current sort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {
				currentSort = "0|false";
			}
			/*
			 * Retrieve search value from request value parameter
			 * filterFromSerach
			 */
			filterFromSort = request.getParameter("filterFromSerach");
			filterCriteria = request.getParameter("filterCriteria");
			filterSortStatus = new StringBuffer();
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Form filterSortStatus from current filter */
			if (tempCurrentFilter.length() == 0) {
				filterSortStatus = filterSortStatus.append(currentFilter);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter);
			}
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Condition to check current sort is none */
			if (currentSort.equals("none")) {
				/* Set the default sorting as ascending */
				currentSort = "0|false";
			}

			/*
			 * Condition to check filter from sort is null If null then display
			 * by defauLt filtering otherwise it filtered the values by user
			 * selection.
			 */
			filterTokenizerCount = 0;
			StringTokenizerCount = 0;
			if ((filterFromSort != null) && filterFromSort.equals("true")) {
				filterCriteriaArray = null;
				filterTokenezier = new StringTokenizer(filterCriteria, "|");
				filterCriteriaCount = filterTokenezier.countTokens();
				filterCriteriaArray = new String[filterCriteriaCount];
				while (filterTokenezier.hasMoreTokens()) {
					filterCriteriaArray[filterTokenizerCount] = filterTokenezier
							.nextToken();
					filterTokenizerCount++;
				}
				tokenHashMap = new HashMap<String, String>(27);
				for (filterTokenizerCount = 0; filterTokenizerCount < filterCriteriaCount; filterTokenizerCount++) {
					filterStrToken = new StringTokenizer(
							filterCriteriaArray[filterTokenizerCount], "=");
					countToken = filterStrToken.countTokens();
					strHashArray = new String[2];
					StringTokenizerCount = 0;
					while (filterStrToken.hasMoreTokens()) {
						strHashArray[StringTokenizerCount] = filterStrToken
								.nextToken();
						StringTokenizerCount++;
					}
					if (countToken == 1) {
						strHashArray[StringTokenizerCount] = "";
					}
					tokenHashMap.put(strHashArray[0], strHashArray[1]);

				}

				request.setAttribute("filterCriteria", filterCriteria);
			} else {
				filterCriteriaStrBuffer = new StringBuffer("partyId=");
				filterCriteriaStrBuffer.append(partyId + "|");
				filterCriteriaStrBuffer.append("partyName=");
				filterCriteriaStrBuffer.append(partyName + "|");
				/* Reading party type value from the request */
				partyType = request.getParameter("partyType");
				filterCriteriaStrBuffer.append("partyType=");
				filterCriteriaStrBuffer.append(partyType + "|");
				request.setAttribute("filterCriteria", filterCriteriaStrBuffer
						.toString());

			}

			/* Default value for orderBy is set to Ascending */
			orderBy = "party_id";
			/* Default value for sorting is set to Ascending */
			sortBy = "Asc";
			/*
			 * Condition to check the current sorting If current sort is not
			 * null then set by selected details
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));

				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";
			}
			custodian.setOrderBy(orderBy);
			custodian.setSortBy(sortBy);
			custodian.setFilterCriteria(filterSortStatus.toString());
			/*
			 * Retrieve and Store the user's menu,entity and currency group from
			 * SwtUtil file
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->Full access if
			 * Value=1 ->View access
			 *
			 */
			if (accessInd == 0) {
				/* This is used to set the button status by enable "ADD" buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				/* This is used to set the button status by disable all buttons */
			} else {
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			/*
			 * Used for Pagination and replacing the Properties class to
			 * PropertiesFile Loader class. This is used to set the page size
			 * using PropertiesFileLoader class
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			/* Set the current page value to 1 */

			/*
			 * Condition to check the current page value from request parameter
			 * If current page is null then set the currentPage to 1
			 */
			if (request.getAttribute("currentPage") == null) {
				currentPage = 1;
				/*
				 * If current page is not null then set the requested page as
				 * current page
				 */
			} else {
				currentPage = Integer.parseInt(request.getAttribute(
						"currentPage").toString());
			}

			/* This is used to set the request partyId value */
			custodian.getId().setPartyId(partyId);
			// checking partyNeme is null
			if (partyName == null)
				/* This is used to set the request partyName value */
				custodian.setPartyName(partyName);
			else
				// This is used to set trimmed partyName value
				custodian.setPartyName(partyName.trim());
			/* This is used to set the request hostId value */
			custodian.getId().setHostId(hostId);
			/* This is used to set the start index value to 0 */
			custodian.setStartIndex(0);
			/* This is used to set the start index value as page size value */
			custodian.setEndIndex(pageSize);
			/*
			 * This method is used to get the custodian list from DB based on
			 * the parameter values passed.
			 */
			partyList = partyManager.getCustodianList(custodian);
			if (filterCriteria == null) {
				/*
				 * This method is used to get the total count from DB based on
				 * the parameter values passed.
				 */
				totalCount = partyManager.getTotalCount(custodian);
			} else {
				/*
				 * This method is used to get the filter count from DB based on
				 * the parameter values passed.
				 */
				totalCount = partyManager.getFilterCount(custodian);
			}
			/* Maximum page is get by calculation */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			request.setAttribute("custodianColl", partyList);
			/*
			 * Initializing pageSummaryList to arrayList pageSummaryList
			 * contains the Page Link that allowed max 10 Pages at one time
			 */
			pageSummaryList = new ArrayList<PartySummary>();
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			/*
			 * Hide pagination if no of records are "0" or records can be
			 * displayed in one page.
			 */
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			/* Condition to enable or disable the next link */
			if (maxPage > 1) {
				isNext = true;
			}
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("currentPage", "1");
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);
			putEntityListInReq(request);
			setParty(custodian);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting");
			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", PartyAction.class), request, "");
			log.debug(this.getClass().getName() + "- [displayList] - Exiting ");
			return getView("fail");
		} finally {
			currentFilter = null;
			currentSort = null;
			filterFromSort = null;
			filterCriteria = null;
			filterSortStatus = null;
			partyList = null;
			pageSummaryList = null;
			orderBy = null;
			sortBy = null;
			tempCurrentFilter = null;
			hostId = null;
			custodian = null;
			entityId = null;
			filterTokenezier = null;
			filterCriteriaArray = null;
			tokenHashMap = null;
			filterStrToken = null;
			strHashArray = null;
			partyId = null;
			filterCriteriaStrBuffer = null;
			partyName = null;
			partyType = null;
		}
	}

	/**
	 * This is used to add the party details
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// string to hold the entity id
		String entityCode = null;
		// string to hold the party id
		String partyId = null;
		// string to hold the party name
		String partyName = null;
		// string to hopld the filtervalues
		String currentFilter = null;
		// String to hold the sort
		String currentSort = null;
		// string to hold the filter sort value
		StringBuffer filterSortStatus = null;
		// string to hold the orderby value
		String orderBy = null;
		// string to hold the sorty by value
		String sortBy = null;
		// string to hold the filter criteria
		String filterCriteria = null;

		// collection to hold the parties
		Collection<LabelValueBean> parties = null;
		// Strinmg to hold the host id
		String hostId = null;
		// declaration to hol the current form
		// DynaValidatorForm dyForm = null;
		// declaration to hold the object
		Party cust = null;
		// String to hold the current filter
		StringBuffer tempCurrentFilter = null;
		// Token to hold the tokenizer
		StringTokenizer strFilter = null;
		// interger to hold the filter vount
		int filterCount;
		// Interger to hold the order by value
		int intOrderBy;
		try {
			log.debug(this.getClass().getName() + "- [add] - Entering ");
			/* Method's local variable declaration */
			filterCount = 0;

			intOrderBy = 0;
			hostId = CacheManager.getInstance().getHostId();
			/* Class instance declaration */
			cust = (Party) getParty();

			/* Reading the entity id value from request */
			entityCode = request.getParameter("entityCode");
			/* Reading the party id value from the main(Parent)screen */
			partyId = request.getParameter("parentScreenPartyId");

			/* Reading the party name value from the main(Parent)screen */
			partyName = request.getParameter("parentScreenPartyName");
			/* Added for pagination sorting and filtering */
			/* Retrieve filter value from request value parameter selectedFilter */
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sort value from request value parameter selectedSort */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				strFilter = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (strFilter.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityCode, strFilter
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								strFilter.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/* Form filterSortStatus from current filter and current Sort */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter)
						.append(",").append(currentSort);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter)
						.append(",").append(currentSort);
			}

			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the current sort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {

				currentSort = "0|false";
			}
			/* String variable to set the default orderby partyid */
			orderBy = "party_id";
			/* String variable to set the default sorting by ascending */
			sortBy = "Asc";
			/*
			 * Condition to check the current sorting Current sort is set by
			 * index value of 0-->party id 1->Party name 2-->party type 4->Party
			 * Alias name
			 *
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";
			}
			/* Set the order by using bean class */
			cust.setOrderBy(orderBy);
			/* Set the sort by using bean class */
			cust.setSortBy(sortBy);
			/* Set filter criteria by using bean class */
			cust.setFilterCriteria(filterSortStatus.toString());

			/* Reading filter criteria from request */
			filterCriteria = request.getParameter("filterCriteria");
			//parties = partyManager.getParties(hostId, entityCode);
			String resultAsXML = partyManager.getPartiesAsXML(hostId, entityCode);
			request.setAttribute("partiesAsString", SwtUtil.xmlToJson(resultAsXML));
			request.setAttribute("currentPage", request
					.getParameter("currentPage"));
			request.setAttribute("maxPage", request.getParameter("maxPage"));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);
			/* Setting entity is using bean class */
			cust.getId().setEntityId(entityCode);
			setParty(cust);
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			request.setAttribute("parentScreenPartyId", partyId);
			request.setAttribute("parentScreenPartyName", partyName);
			request.setAttribute("parentScreenEntityId", entityCode);
			request.setAttribute("methodName", "add");
			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", PartyAction.class), request, "");
			return getView("fail");
		} finally {

			entityCode = null;
			partyId = null;
			partyName = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			orderBy = null;
			sortBy = null;
			filterCriteria = null;
			parties = null;
			hostId = null;
			tempCurrentFilter = null;
			strFilter = null;

			log.debug(this.getClass().getName() + "- [add] - Exiting");
		}
	}

	/**
	 * This method is used to save the newly added details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Integer to hold the current page
		int currentPage;
		// integer to hold the maximum page value
		int maxPage;
		// boolean to hold the is next value
		boolean isNext = false;
		// string to hold the current filter values
		String currentFilter = null;
		// string to hold the current sort values
		String currentSort = null;
		// String to hold the current filte criteria
		String filterCriteria = null;

		// String to hopld the hostid
		String hostId = null;
		// integer to hold the page number
		int clickedPage;
		// String to hold the curretn page number
		String clckPageStr = null;
		// string to hoster id
		String userId = null;
		// integer to hold the total number of pages
		int pageSize;
		// String to hold the party id
		String parentScreenPartyId = null;
		// String to hold the party name
		String parentScreenPartyName = null;
		// String to hold the entity id
		String parentScreenEntityId = null;
		// String to hold the next page status
		String nextLinkStatus = null;
		// String tio hold the previous page status
		String prevLinkStatus = null;
		// Srting to hold the filter
		StringBuffer tempCurrentFilter = null;
		// String to hola the filter values
		StringBuffer filterSortStatus = null;
		// Collection to hold the parties

		Collection<LabelValueBean> parties = null;
		// Declaration for pojo class
		Party party = null;
		// Variable to hold the system information
		SystemInfo systemInfo = null;
		// declared to hold the errors
		ActionMessages errors = null;
		// declared to hold the forms
		// DynaValidatorForm dyForm = null;
		// Declaration to hold the filter tokens
		StringTokenizer filterToken = null;
		// Integer to get the filter count
		int filterCount;
		// Integer to get the order by
		int intOrderBy;
		// String to hold the currPageStr
		String currPageStr = null;
		// Declared fror sorting
		String orderBy = null;
		// Declared for the sort type
		String sortBy = null;
		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			currentPage = 0;
			maxPage = 0;
			clickedPage = 0;
			pageSize = 0;
			filterCount = 0;
			intOrderBy = 0;
			errors = new ActionMessages();

			party = new Party();
			systemInfo = new SystemInfo();
			party = (Party) getParty();

			/* Retrieve and store hostId from CacheManager class */
			hostId = CacheManager.getInstance().getHostId();
			/* Used to set the host id */
			/* Setting the host id */
			party.getId().setHostId(hostId);
			/* Retrieve Current user and user id from session */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* Get & Set the user id */
			party.setUpdateUser(userId);
			/* Getting & Setting Ip address */
			systemInfo.setIpAddress(request.getRemoteAddr());
			/*
			 * This method is used to save the custodian details from DB based
			 * on the parameter values passed.
			 */
			partyManager.saveCusdoianDetail(party);
			party = new Party();
			/* Reading the partyId value from the parnet screen */
			parentScreenPartyId = request.getParameter("parentScreenPartyId");

			/* Reading the partyName value from the parnet screen */
			parentScreenPartyName = request
					.getParameter("parentScreenPartyName");
			/* Reading the EntityId value from the parnet screen */
			parentScreenEntityId = request.getParameter("parentScreenEntityId");
			/* Set the entity id from parent screen */
			party.getId().setEntityId(parentScreenEntityId);
			/* Set the party id from parent screen */
			party.getId().setPartyId(parentScreenPartyId);
			/* Set the party name from parent screen */
			party.setPartyName(parentScreenPartyName);
			setParty(party);
			request.setAttribute("methodName", "save");
			request.setAttribute("parentFormRefresh", "yes");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Read the current filter in request */
			currentFilter = request.getParameter("selectedFilter");
			/* Read the current sort in request */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the current sort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {
				currentSort = "0|false";
			}
			filterCriteria = request.getParameter("filterCriteria");

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterToken = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (filterToken.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(parentScreenEntityId,
										filterToken.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterToken.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/* Form filterSortStatus from current filter and current Sort */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter)
						.append(",").append(currentSort);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter)
						.append(",").append(currentSort);
			}

			if (currentSort.equals("none")) {
				currentSort = "0|false";
			}
			/* Default value for order by is set to Partyid */
			orderBy = "party_id";
			/* Default value for sorting is set to Ascending */
			sortBy = "Asc";
			/*
			 * Condition to check current sort is not null then set the sorting
			 * by index values
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";
			}
			/* Set order by using bean class */
			party.setOrderBy(orderBy);
			/* Set sort by using bean class */
			party.setSortBy(sortBy);
			party.setFilterCriteria(filterSortStatus.toString());
			maxPage = 0;

			/* Retrieve current page from request parameter */
			currPageStr = request.getParameter("currentPage");
			/*
			 * Retrieve pageNoValue, that indicates the link clicked page or
			 * next(>> -1) or previous(<< -2)
			 */
			clckPageStr = request.getParameter("pageNoValue");
			/* Setting current page value to zero */
			currentPage = 0;
			/*
			 * Condition to check current page not equal to null and click page
			 * equal to null
			 */
			if ((currPageStr != null) && (clckPageStr == null)) {
				/* Retrieve current page from request parameter */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Retrieve clicked page from request parameter */
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Retrieve maximum page from request parameter */
				maxPage = Integer.parseInt(request.getParameter("maxPage"));
			}

			else {
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				clickedPage = Integer.parseInt(request
						.getParameter("pageNoValue"));
				maxPage = Integer.parseInt(request.getParameter("maxPages"));
				/* Condition to check the previous link clicked */
				if (clickedPage == -2) { // Previous Link Clicked
					currentPage--;
					clickedPage = currentPage;
				} else {
					/* Condition to check the Next link clicked */
					if (clickedPage == -1) { // Next Link Clicked
						currentPage++;
						clickedPage = currentPage;
					} else {
						currentPage = clickedPage;
					}
				}
			}
			/*
			 * Condition to check the clicked page.If Clicked page greater than
			 * 1 then previous link enable. If Clicked page less than 1 then
			 * previous link disable
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check the clicked page.If Clicked page less than
			 * maximum page then next link enable. If Clicked page greater than
			 * maximum page then next link disable
			 */
			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}
			/* Replacing Properties class to PropertiesFileLoader class */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			/* Setting hostid from request */
			party.getId().setHostId(hostId);
			/* Setting Start index by doing calculation */
			party.setStartIndex((currentPage * pageSize) - pageSize);
			/* Setting End index by doing calculation */
			party.setEndIndex(currentPage * pageSize);

			setParty(party);
			party.getId().setHostId(hostId);
			isNext = false;
			/*
			 * Condition to maximum page greater than 1 If true then next link
			 * is enable
			 */
			if (maxPage > 1) {
				isNext = true;
			}
			parties = new ArrayList();
//			parties = partyManager.getParties(hostId, parentScreenEntityId);
//			request.setAttribute("parties", parties);
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("entityId", "" + party.getId().getEntityId());
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);

			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("methodName", "add");
			request.setAttribute("currentPage", request
					.getParameter("currentPage"));
			request.setAttribute("maxPage", request.getParameter("maxPages"));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "true");
			request.setAttribute("entityId", party.getId().getEntityId());
			request.setAttribute("selectedFilter", request
					.getParameter("selectedFilter"));
			request.setAttribute("selectedSort", request
					.getParameter("selectedSort"));
			request.setAttribute("filterCriteria", request
					.getParameter("filterCriteria"));
			request.setAttribute("parentScreenPartyId", request
					.getParameter("parentScreenPartyId"));
			request.setAttribute("parentScreenPartyName", request
					.getParameter("parentScreenPartyName"));
			request.setAttribute("parentScreenEntityId", request
					.getParameter("parentScreenEntityId"));
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			setParty(party);
			String resultAsXML = partyManager.getPartiesAsXML(hostId, party.getId().getEntityId());
            try {
                request.setAttribute("partiesAsString", SwtUtil.xmlToJson(resultAsXML));
            } catch (IOException e) {
            }
//			parties = new ArrayList<LabelValueBean>();
//			parties = partyManager.getParties(hostId, party.getId()
//					.getEntityId());
//			request.setAttribute("parties", parties);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", PartyAction.class), request, "");
			return getView("fail");
		} finally {

			currentFilter = null;
			currentSort = null;
			filterCriteria = null;
			userId = null;
			parentScreenPartyId = null;
			parentScreenPartyName = null;
			parentScreenEntityId = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			tempCurrentFilter = null;
			filterSortStatus = null;
			parties = null;
			filterToken = null;
			currPageStr = null;
			clckPageStr = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			tempCurrentFilter = null;
			filterSortStatus = null;
			log.debug(this.getClass().getName() + "- [save] - Exiting");

		}
	}

	/**
	 * This is used to Chanage the party details
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// String to hold the hostId
		String hostId = null;
		// String to hold the entityId
		String entityId = null;
		// String to hold the custodianCode
		String custodianCode = null;
		// String to hold the party name
		String custodianName = null;
		// String to hold the Party type
		String partyType = null;
		// String to hold the custadion hold
		String custodianFlag = null;
		// String to hold the old value
		String oldVal = null;
		// String to hold the current filter
		String currentFilter = null;
		// String to hold the current sort
		String currentSort = null;
		// String to hold the filter sort status
		StringBuffer filterSortStatus = null;
		// String to hold the order by
		String orderBy = null;
		// String to hold the sort by
		String sortBy = null;
		// String to hold the filtre criteria
		String filterCriteria = null;
		// String to hold the entity Id
		String entityCode = null;
		// String to hold the Party Id
		String partyId = null;
		// String to hold the Party Name
		String partyName = null;

		// Collection to hold the parties
		Collection<LabelValueBean> parties = null;
		// String to hold the party Id
		String isoDecode = null;
		// String to hold party Name
		String custodianNameDecode = null;
		// Declaration to Pojo class
		Party partyDataFromDatabase = null;
		// Declaration to hold form
		// DynaValidatorForm dyForm = null;
		// Declaration to Pojo class
		Party party = null;

		// String to hold the filter values
		StringBuffer tempCurrentFilter = null;
		// declred tokenezier for filters
		StringTokenizer filterToken = null;
		// Integer to store filter counts
		int filterCount;
		// Integer to hold the order by value
		int intOrderBy;
		try {
			log.debug(this.getClass().getName() + "- [Change] - Entering ");

			filterCount = 0;

			intOrderBy = 0;
			/* Creating instance for Party class */
			StringEscapeUtils.escapeHtml(request.getParameter("partyName"));
			request.setAttribute("screenFieldsStatus", "true");
			party = (Party) getParty();
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Reading the entityId value from the request */
			entityId = request.getParameter("entityCode");
			/* Reading the Party id value from the request */
			custodianCode = request.getParameter("partyId");

			isoDecode = java.net.URLDecoder.decode(custodianCode,
					SwtConstants.ISO);
			/* Reading the party name value from the request */
			custodianName = request.getParameter("partyName");
			custodianNameDecode = java.net.URLDecoder.decode(custodianName,
					SwtConstants.ISO);

			request.setAttribute("entityName", request
					.getParameter("entityName"));
			/*
			 * This method is used to get the custodian flag list from DB based
			 * on the parameter values passed.
			 */

			partyDataFromDatabase = partyManager.getCustodianFlag(entityId,
					hostId, isoDecode);

			/* Retrieve the type of Party from Party class */
			partyType = partyDataFromDatabase.getPartyType();

			/* Retrieve the custodian flag from Party class */
			custodianFlag = partyDataFromDatabase.getCustodianFlag();
			/* Setting host id,entity id and party id */
			party.getId().setHostId(hostId);
			party.getId().setEntityId(entityId);

			party.getId().setPartyId(isoDecode);

			/* Setting party type,party name and custodian flag */
			party.setPartyType(partyType);

			party.setPartyName(custodianNameDecode);

			party.setCustodianFlag(custodianFlag);
			party.setParentParty(partyDataFromDatabase.getParentParty());
			oldVal = new StringBuffer("Party-Name=").append(
					party.getPartyName()).append("^CustodianFlag=").append(
					party.getCustodianFlag()).toString();

			/* Added for pagination sorting and filtering */
			/* Retrieve filter value from request value parameter selectedFilter */
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selected sort */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterToken = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (filterToken.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityId, filterToken
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterToken.nextToken()).append("|");
						;
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/* Form filterSortStatus from current filter and current Sort */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter)
						.append(",").append(currentSort);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter)
						.append(",").append(currentSort);
			}

			if (currentSort == null) {

				currentSort = "0|false";
			}
			/* Default setting for orderby as partyid */
			orderBy = "party_id";
			/* Setting Default sorting as ascending */
			sortBy = "Asc";
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";
			}
			party.setOrderBy(orderBy);
			party.setSortBy(sortBy);
			party.setFilterCriteria(filterSortStatus.toString());
			filterCriteria = request.getParameter("filterCriteria");
			//parties = partyManager.getParties(hostId, entityId);
//			request.setAttribute("parties", parties);
			request.setAttribute("currentPage", request
					.getParameter("currentPage"));
			request.setAttribute("maxPage", request.getParameter("maxPage"));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);

			setParty(party);
			/* Reading entity id value from the request */
			entityCode = request.getParameter("entityCode");
			/* Reading party id value from the request */
			partyId = request.getParameter("parentScreenPartyId");
			/* Reading party name value from the request */
			partyName = request.getParameter("parentScreenPartyName");
			request.setAttribute("parentScreenPartyId", partyId);
			request.setAttribute("parentScreenPartyName", partyName);
			request.setAttribute("parentScreenEntityId", entityCode);
			request.setAttribute("parentPartyId", party.getParentParty());
			if(!SwtUtil.isEmptyOrNull(party.getParentParty())) {
				Party parentParty = partyManager.getCustodianFlag(entityId,
						hostId, party.getParentParty());
				request.setAttribute("parentPartyName", parentParty.getPartyName());
			}else {
				request.setAttribute("parentPartyName", "");
			}
			request.setAttribute("methodName", "change");
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			String resultAsXML = partyManager.getPartiesAsXML(hostId, entityCode);
//			System.err.println(SwtUtil.xmlToJson(resultAsXML));

			request.setAttribute("partiesAsString", SwtUtil.xmlToJson(resultAsXML));
			request.setAttribute("oldValue", oldVal);

			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", PartyAction.class), request, "");
			return getView("fail");
		} finally {

			hostId = null;
			entityId = null;
			custodianCode = null;
			custodianName = null;
			partyType = null;
			custodianFlag = null;
			oldVal = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			orderBy = null;
			sortBy = null;
			filterCriteria = null;
			entityCode = null;
			partyId = null;
			partyName = null;
			parties = null;
			isoDecode = null;
			custodianNameDecode = null;
			tempCurrentFilter = null;
			filterToken = null;

			log.debug(this.getClass().getName() + "- [Change] - Exiting");
		}
	}

	/**
	 * This is used to Update the modified party details
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Method's local variable declaration */
		// Integer to hold the page value
		int currentPage;
		// Integer to hold the maximum page
		int maxPage;
		// flag to determine the next page
		boolean isNext = false;
		// String to hold the filter values
		String currentFilter = null;
		// Strig to hold the current sort
		String currentSort = null;
		// String to hold the filter values
		String filterCriteria = null;
		// String to hold the olds values
		String oldValue = null;
		// String to hold the hostId
		String hostId = null;
		// String to hold the new value
		String newValue = null;
		// String to hold the party Id
		String parentScreenPartyId = null;
		// String to hold the Party name
		String parentScreenPartyName = null;
		// String to hold the Entity Id
		String parentScreenEntityId = null;
		// String to hold the order by
		String orderBy = null;
		// String to hold the sort by
		String sortBy = null;
		// String to hold next button status in pagination
		String nextLinkStatus = null;
		// String to hold Previous button status in pagination
		String prevLinkStatus = null;
		// String to hold the current page
		String currPageStr = null;
		// String to hold the selcted page
		String clckPageStr = null;
		// String to hold the user Id
		String userId = null;
		// Intger to hold the selected page number
		int clickedPage;
		// String to hold the Pages size

		// Integer to hold the Page size
		int pageSize;
		// Collcetion to hold the Party list
		Collection<LabelValueBean> parties = null;

		// Delecretaion for system info
		SystemInfo systemInfo = null;
		// Declaration for pojo class
		Party party = null;
		// Declaration for Filter
		StringBuffer tempCurrentFilter = null;
		// Declaration for Filter sort status
		StringBuffer filterSortStatus = null;
		// Token Declaration
		StringTokenizer filterToken = null;
		// Integer for to store filter coults
		int filterCount;
		// Integer to hold the order by value
		int intOrderBy;

		// Declaration to hold the valiadator form
		// DynaValidatorForm dyForm = null;
		try {
			log.debug(this.getClass().getName() + "- [update] - Entering ");

			clickedPage = 0;
			pageSize = 0;
			filterCount = 0;
			intOrderBy = 0;
			systemInfo = new SystemInfo();
			party = new Party();
			putEntityListInReq(request);
			/* Reading the entityId value from the request */
			oldValue = request.getParameter("oldValue");
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			party = (Party) getParty();
			/* Setting the host id */
			party.getId().setHostId(hostId);
			newValue = new StringBuffer("Party-Name=").append(
					party.getPartyName()).append("^CustodianFlag=").append(
					party.getCustodianFlag()).toString();
			/* Retrieve the user id from the session */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			party.setUpdateUser(userId);
			systemInfo.setIpAddress(request.getRemoteAddr());
			/* Setting the old value */
			systemInfo.setOldLogString(oldValue);
			/* Setting the modified value */
			systemInfo.setNewLogString(newValue);
			/*
			 * This method is used to get the updated party details from DB
			 * based on the parameter values passed.
			 */
			partyManager.updateCustodianDetail(party);
			/* Retrieve the party id from the parnet screen */
			parentScreenPartyId = request.getParameter("parentScreenPartyId");
			/* Retrieve the party name from the parnet screen */
			parentScreenPartyName = request
					.getParameter("parentScreenPartyName");
			/* Retrieve the entity id from the parnet screen */
			parentScreenEntityId = request.getParameter("parentScreenEntityId");
			/* Setting entity id from parnetscreen */
			party.getId().setEntityId(parentScreenEntityId);
			/* Setting party id from parnetscreen */
			party.getId().setPartyId(parentScreenPartyId);
			/* Setting party name from parnetscreen */
			party.setPartyName(parentScreenPartyName);
			setParty(party);
			request.setAttribute("methodName", "displayList");
			request.setAttribute("parentFormRefresh", "yes");

			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selectedsort */
			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterToken = new StringTokenizer(currentFilter, "|");

				while (filterToken.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(parentScreenEntityId,
										filterToken.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterToken.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/* Form filterSortStatus from current filter and current Sort */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter)
						.append(",").append(currentSort);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter)
						.append(",").append(currentSort);
			}

			if (currentSort == null) {
				currentSort = "0|false";
			}
			filterCriteria = request.getParameter("filterCriteria");

			if (currentSort.equals("none")) {
				currentSort = "0|false";
			}
			/* By default we set the order by as party id */
			orderBy = "party_id";
			/* Default sorting is set to ascending */
			sortBy = "Asc";
			/*
			 * Condition to check the current sorting If current sort is not
			 * null then set by selected details
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";
			}
			party.setOrderBy(orderBy);
			party.setSortBy(sortBy);
			party.setFilterCriteria(filterSortStatus.toString());
			/* Setting max page value to 0 */
			maxPage = 0;
			/* Reading the current page value from the request */
			currPageStr = request.getParameter("currentPage");
			/* Reading the page number from the request */
			clckPageStr = request.getParameter("pageNoValue");
			/* Setting current page value to 0 */
			currentPage = 0;
			/*
			 * Condition to check Current page not equal to null and page no
			 * equal to null
			 */
			if ((currPageStr != null) && (clckPageStr == null)) {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the current page value from the request */
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the max page value from the request */
				maxPage = Integer.parseInt(request.getParameter("maxPage"));
			}

			else {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the page no value from the request */
				clickedPage = Integer.parseInt(request
						.getParameter("pageNoValue"));
				maxPage = Integer.parseInt(request.getParameter("maxPages"));
				/* Condition to check the previous link clicked */
				if (clickedPage == -2) { // Previous Link Clicked
					currentPage--;
					clickedPage = currentPage;
				}
				/* Condition to check the Next link clicked */
				else {
					if (clickedPage == -1) { // Next Link Clicked
						currentPage++;
						clickedPage = currentPage;
					} else {
						currentPage = clickedPage;
					}
				}
			}
			/*
			 * Condition to check clicked page greater than page no 1 If true
			 * previous link is enable.otherwise previouslink is disable.
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check clicked page greater than maximum page If true
			 * next link is enable.otherwise nextlink is disable.
			 */
			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}
			/* Replacing properties to propertiesFileLoader class */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			/* Setting the host id */
			party.getId().setHostId(hostId);
			/* Setting the start index by doing calculation */
			party.setStartIndex((currentPage * pageSize) - pageSize);
			/* Setting the end index by doing calculation */
			party.setEndIndex(currentPage * pageSize);
			setParty(party);
			party.getId().setHostId(hostId);

			isNext = false;
			if (maxPage > 1) {
				isNext = true;
			}
			parties = new ArrayList();
//			parties = partyManager.getParties(hostId, parentScreenEntityId);
//			request.setAttribute("parties", parties);
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("entityId", party.getId().getEntityId());
			request.setAttribute("filterCriteria", filterCriteria);
			log.debug(this.getClass().getName() + "- [update] - Exiting");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			parties = new ArrayList();
//			parties = partyManager.getParties(hostId, parentScreenEntityId);
//
//			request.setAttribute("parties", parties);
			currentPage = 0;
			currentPage = Integer.parseInt(request.getParameter("currentPage"));
			maxPage = 0;
			maxPage = Integer.parseInt(request.getParameter("maxPage"));
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("entityId", "" + party.getId().getEntityId());
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", PartyAction.class), request, "");
			return getView("fail");
		} finally {

			currentFilter = null;
			currentSort = null;
			filterCriteria = null;
			oldValue = null;
			hostId = null;
			newValue = null;
			parentScreenPartyId = null;
			parentScreenPartyName = null;
			parentScreenEntityId = null;
			orderBy = null;
			sortBy = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
			currPageStr = null;
			clckPageStr = null;
			userId = null;
			parties = null;
			tempCurrentFilter = null;
			filterSortStatus = null;
			filterToken = null;

			log.debug(this.getClass().getName() + "- [update] - Exiting");
		}
	}

	/**
	 * This is used to delete the party details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// String declared to get status of next page button
		String nextLinkStatus = null;
		// String declared to get status of previous page button
		String prevLinkStatus = null;
		// String declared to get the entity Id
		String entityId = null;
		// String declared to get the host Id
		String hostId = null;
		// String declared to get the Party Id
		String custodianCode = null;
		// String declared to get the party name
		String custodianName = null;
		// String declared to get the filter value
		String currentFilter = null;
		// String declared to get the sort values
		String currentSort = null;
		// String declared to get the party id
		String partyId = null;
		// String declared to get the party name
		String partyName = null;
		// String declared to get the order by
		String orderBy = null;
		// String declared to get the sort by
		String sortBy = null;
		// String declared to get the current page
		String currPageStr = null;
		// Integer declared to get the page size
		int maxPage;
		// Integer declared to get the clicked page
		int clickedPage;
		// Integer declared to get current page
		int currentPage;
		// Integer declared to get the page size
		int pageSize;
		// Integer declared forendIndex
		int endIndex;
		// Integer declared startIndex
		int startIndex;
		// Integer declared to get the total count of records
		int totalCount;
		// Declaration for System Info
		SystemInfo systemInfo = null;

		// Variable Declaration for validator form
		// DynaValidatorForm dyForm = null;
		// String Declaration to hold host id
		String userId = null;
		// String Declaration to hold the filter values
		String filterFromSort = null;
		// String Declaration to hold the filter values
		String filterCriteria = null;
		// String declared to get the filter sort status
		StringBuffer filterSortStatus = null;
		// String declared to get the filter values
		StringBuffer tempCurrentFilter = null;
		// String declared to get the party type
		String partyType = null;
		// Integer declared determine the menu access values
		int accessInd;
		// String declared to get the clicked page
		String clckPageStr = null;
		// Collection declared to get the party list
		Collection<Party> partyList = null;
		// Declaration for pojo class
		Party custodian = null;
		// Declaration for filterCriteriaArray
		String[] filterCriteriaArray = null;
		// Declaration for filter token
		StringTokenizer filterToken = null;
		// Integer Declaration for count
		int count;
		// Declaration for Hash map
		HashMap<String, String> hashMap = null;
		// Token declared to hold the filter criteria
		StringTokenizer strToken = null;
		// Integer to hold the no of tokens in the filter
		int countToken;
		// String array to hold the filter values
		String[] strHashArray = null;
		// Integer to hold the order by column value
		int intOrderBy;
		// Integer to hold the filter count
		int filterCount;
		// Declaration of Array List
		ArrayList<PartySummary> pageSummaryList = null;
		// Variable declared for filter criteria
		StringBuffer filterCriteriaStrBuffer = null;
		// Variable declared to keep loop count
		int counter;
		// Variable declared to keep loop count
		int hashCounter;
		try {
			log.debug(this.getClass().getName() + "- [delete] - Entry");

			clickedPage = 0;
			currentPage = 0;
			pageSize = 0;
			endIndex = 0;
			startIndex = 0;
			accessInd = 0;
			count = 0;
			countToken = 0;
			intOrderBy = 0;
			counter = 0;
			custodian = (Party) getParty();
			/* Reading the entityId value from the request */
			entityId = request.getParameter("entityCode");
			systemInfo = new SystemInfo();
			/* Reading the ip address value from SystemInfo class */
			systemInfo.setIpAddress(request.getRemoteAddr());
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Reading the Party id value from the request */
			custodianCode = request.getParameter("selectedCustodianCode");
			/* Reading the party name value from the request */
			custodianName = request.getParameter("selectedCustodianName");
			/* Setting the party id */
			custodian.getId().setPartyId(custodianCode);
			/* Condition to check entity is null */
			if (SwtUtil.isEmptyOrNull(entityId)) {
				/* Getting the entity id and set the id for entity */
				custodian.getId().setEntityId(custodian.getId().getEntityId());
			} else {
				custodian.getId().setEntityId(entityId);
			}
			/* Setting host id using bean class */
			custodian.getId().setHostId(hostId);
			/* Setting the Party name */
			//Commented by Atef as it is not needed for delete
			//custodian.setPartyName(custodianName);
			putEntityListInReq(request);
			/* Getting the current user id from the session */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* Set the id for the user */
			custodian.setUpdateUser(userId);

			/* This method is used to delete the information from the database. */
			partyManager.deleteCustodianDetail(custodian);
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selectedSort */
			currentSort = request.getParameter("selectedSort");
			/* Reading the partyId value from the request */
			partyId = request.getParameter("partyId");
			/* Reading the party name value from the request */
			partyName = (!"".equals(request.getParameter("partyName")) ? (request
					.getParameter("partyName")) : (request
					.getParameter("party.partyName")));
			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the current sort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {
				currentSort = "0|false";
			}
			/* Reading the sorting value from the request */
			filterFromSort = request.getParameter("filterFromSerach");
			filterCriteria = request.getParameter("filterCriteria");
			/* Condition to check current sort is equal to null */
			if (currentSort == null) {
				currentSort = "none";
			}
			/*
			 * Condition to check filter from sort is null If null then display
			 * by default filtering otherwise it filtered the values by user
			 * selection.
			 */
			if (!SwtUtil.isEmptyOrNull(filterFromSort)
					&& filterFromSort.equals("true")) {

				filterToken = new StringTokenizer(filterCriteria, "|");
				count = filterToken.countTokens();
				filterCriteriaArray = new String[count];
				while (filterToken.hasMoreTokens()) {
					filterCriteriaArray[counter] = filterToken.nextToken();
					counter++;
				}
				hashMap = new HashMap<String, String>(27);
				for (counter = 0; counter < count; counter++) {
					strToken = new StringTokenizer(
							filterCriteriaArray[counter], "=");
					countToken = strToken.countTokens();
					strHashArray = new String[2];
					hashCounter = 0;
					while (strToken.hasMoreTokens()) {
						strHashArray[hashCounter] = strToken.nextToken();
						hashCounter++;
					}
					if (countToken == 1) {
						strHashArray[hashCounter] = "";
					}
					hashMap.put(strHashArray[0], strHashArray[1]);
				}
				request.setAttribute("filterCriteria", filterCriteria);

			} else {
				/* Reading the partyId value from the request */
				partyId = request.getParameter("partyId");
				filterCriteriaStrBuffer = new StringBuffer("partyId=");
				filterCriteriaStrBuffer.append(partyId).append("|");
				partyName = request.getParameter("partyName");
				filterCriteriaStrBuffer.append("partyName=");
				filterCriteriaStrBuffer.append(partyName).append("|");
				partyType = request.getParameter("partyType");
				filterCriteriaStrBuffer.append("partyType=");
				filterCriteriaStrBuffer.append(partyType).append("|");
				request.setAttribute("filterCriteria", filterCriteriaStrBuffer
						.toString());
			}
			/* Default setting for order by is partyId */
			orderBy = "party_id";
			/* Default setting for sorting is ascending */
			sortBy = "Asc";
			/*
			 * Condition to check Current sort not equal to null then set by
			 * using index value
			 */
			if (currentSort != null) {
				intOrderBy = Integer.parseInt(currentSort.substring(0, 1));
				sortBy = currentSort.substring(2, currentSort.length()).trim();
				sortBy = sortBy.replace('|', ' ').trim();
				if (intOrderBy == 0)
					orderBy = "party_id";
				else if (intOrderBy == 1)
					orderBy = "party_name";
				else if (intOrderBy == 2)
					orderBy = "party_type";
				else if (intOrderBy == 3)
					orderBy = "parent_party_id";
				else if (intOrderBy == 4)
					orderBy = "alias";
				if (Boolean.parseBoolean(sortBy))
					sortBy = "Desc";
				else
					sortBy = "Asc";

			}
			/* Set order by using bean class */
			custodian.setOrderBy(orderBy);
			/* Set sort by using bean class */
			custodian.setSortBy(sortBy);
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterToken = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (filterToken.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityId, filterToken
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterToken.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Form filterSortStatus from current filter */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter);
			}
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Setting maximum page as zero */
			maxPage = 0;
			/* Reading the current page value from the request */
			currPageStr = request.getParameter("currentPage");
			/* Reading the page number value from the request */
			clckPageStr = request.getParameter("pageNoValue");
			/* Condition to check current page not null and page number null */
			if ((currPageStr != null) && (clckPageStr == null)) {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the current value from the request */
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
				/* Reading the maximum page value from the request */
				maxPage = Integer.parseInt(request.getParameter("maxPage"));
			} else {
				/* Reading the current page value from the request */
				currentPage = Integer.parseInt(request
						.getParameter("currentPage"));
				clickedPage = Integer.parseInt(request
						.getParameter("currentPage"));
				maxPage = Integer.parseInt(request.getParameter("maxPages"));
				/* Condition to check the previous link clicked */
				if (clickedPage == -2) { // Previous Link Clicked
					currentPage--;
					clickedPage = currentPage;
				} else {
					/* Condition to check the Next link clicked */
					if (clickedPage == -1) { // Next Link Clicked
						currentPage++;
						clickedPage = currentPage;
					} else {
						/*
						 * If clicked page is current page then assign it as
						 * current page
						 */
						currentPage = clickedPage;
					}
				}
			}
			/*
			 * Condition to check clicked page greater than first page If true
			 * previous link is enable.otherwise previousLink is disable.
			 */
			if (clickedPage > 1) {
				prevLinkStatus = "true";
			} else {
				prevLinkStatus = "false";
			}
			/*
			 * Condition to check clicked page greater than Maximum page If true
			 * nextLink is enable.otherwise nextLink is disable.
			 */
			if (clickedPage < maxPage) {
				nextLinkStatus = "true";
			} else {
				nextLinkStatus = "false";
			}
			custodian.setFilterCriteria(filterSortStatus.toString());
			/* Replacing Properties to PropertiesFileLoader class */
			/*
			 * Used for Pagination by retrieve methods from PropertiesFileLoader
			 * class
			 */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			/* Identify end value by doing calculations */
			endIndex = currentPage * pageSize;
			/* Identify start value by doing calculations */
			startIndex = endIndex - pageSize;
			/* Setting start index and end index */
			custodian.setStartIndex(startIndex);
			custodian.setEndIndex(endIndex);
			/* Setting party id & party name after trimming */
			custodian.getId().setPartyId(partyId.trim());
			custodian.setPartyName(partyName.trim());
			/* Setting host id */
			custodian.getId().setHostId(hostId);
			setParty(custodian);
			totalCount = 0;
			/*
			 * Condition to check Filtering.If filtering is none then all
			 * details displayed
			 */
			if (filterCriteria == null) {
				/*
				 * Retrieve Total number of records from DB based on the
				 * parameters passed.
				 */
				totalCount = partyManager.getTotalCount(custodian);
			} else {
				/*
				 * Retrieve selected records from DB based on the parameters
				 * passed.
				 */
				totalCount = partyManager.getFilterCount(custodian);
			}
			if (totalCount <= startIndex) {
				if ((currentPage - 1) != 0) {
					currentPage = currentPage - 1;
				} else {
					currentPage = 1;
				}
				startIndex = startIndex - pageSize;
				endIndex = endIndex - pageSize;
				custodian.setStartIndex(startIndex);
				custodian.setEndIndex(endIndex);
				// if total count is 0 reset screen to initial values
				if (totalCount <= 0) {
					/*
					 * Start:Code added for Mantis 1961 by Chinna on
					 * 29-Oct-2012:issue while deleting a party. The grid
					 * displays no records, but the pagination control says more
					 * than one page of records.
					 */
					custodian.setStartIndex(0);
					custodian.setEndIndex(pageSize);
					prevLinkStatus = "false";
					nextLinkStatus = "true";
					/*
					 * End:Code added for Mantis 1961 by Chinna on
					 * 29-Oct-2012:issue while deleting a party. The grid
					 * displays no records, but the pagination control says more
					 * than one page of records.
					 */
					filterCriteria = "All|All|All|All|All";
					currentFilter = "All|All|All|All|All";
					currentSort = "0|false";
					custodian.setFilterCriteria(filterCriteria);
					totalCount = partyManager.getFilterCount(custodian);
				}

			}

			/* Retrieve Party details from DB based on the parameters passed. */
			partyList = partyManager.getCustodianList(custodian);
			/* getting the entity id */
			entityId = custodian.getId().getEntityId();
			/*
			 * This is used to sets maximum Page by using function The maximum
			 * page count is done by calculation i.e. The total record value got
			 * from the DB and the pageSize is got from the properties file.
			 */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			request.setAttribute("custodianColl", partyList);
			/* Initializing pageSummaryList to arrayList */
			pageSummaryList = new ArrayList<PartySummary>();
			/* for setting the page numbers for the screen */
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			/*
			 * Hide pagination if no of records are "0" or records can be
			 * displayed in one page
			 */
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("currentPage", "" + currentPage);
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("custodianColl", partyList);
			request.setAttribute("pageNoValue", clckPageStr);
			request.setAttribute("filterCriteria", filterCriteria);
			/* Used to put the entity list in request */
			putEntityListInReq(request);

			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->Fullccess If
			 * Value=1 ->View access
			 *
			 */

			if (accessInd == 0) {
				/* This is used to set the button status by enable buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button status by disable buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "true");

			custodian = (Party) getParty();
			custodian.setPartyName("");
			custodian.getId().setPartyId("");
			setParty(custodian);
			putEntityListInReq(request);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayList();

		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", PartyAction.class), request, "");

			return getView("fail");
		} finally {
			// Nullify
			nextLinkStatus = null;
			prevLinkStatus = null;
			entityId = null;
			hostId = null;
			custodianCode = null;
			custodianName = null;
			currentFilter = null;
			currentSort = null;
			partyId = null;
			partyName = null;
			orderBy = null;
			sortBy = null;
			currPageStr = null;
			userId = null;
			filterFromSort = null;
			filterCriteria = null;
			filterSortStatus = null;
			tempCurrentFilter = null;
			partyType = null;
			clckPageStr = null;
			partyList = null;
			filterCriteriaArray = null;
			filterToken = null;
			hashMap = null;
			strToken = null;
			strHashArray = null;
			pageSummaryList = null;

			log.debug(this.getClass().getName() + "- [delete] - Exiting");
		}
	}

	/**
	 * This method is used to put the Entity in request
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering ");
		/* Method's local variable declaration */
		HttpSession session = null;
		Collection coll = null;
		session = request.getSession();
		coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", coll);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting");
	}

	/**
	 * Setting Button status for view access and full access
	 *
	 * @param request
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param cancelStatus
	 * @return
	 */

	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String cancelStatus) {
		log
				.debug(this.getClass().getName()
						+ "- [setButtonStatus] - Entering ");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		log.debug(this.getClass().getName() + "- [setButtonStatus] - Exiting ");
	}

	/**
	 * This is used in Manual Input Screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String preSearchParties() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName()
					+ "- [preSearchParties] - Entering");
			/* Method's local variable declaration */
			String entityId;
			String custodianFlag;
			String idElementName;
			String descElementName;
			Party party = new Party();
			// DynaValidatorForm Party party = new Party();
			entityId = request.getParameter("entityId");
			custodianFlag = request.getParameter("custodianFlag");
			idElementName = request.getParameter("idElementName");
			descElementName = request.getParameter("descElementName");
			/* Setting the party id using bean class */
			party.getId().setEntityId(entityId);
			if (custodianFlag == null) {
				custodianFlag = "";
			}
			party.setCustodianFlag(custodianFlag);
			Party custodian = (Party) getParty();
			setParty(party);
			request.setAttribute("isPreSearch", "Y");
			request.setAttribute("partyList", new ArrayList());
			request.setAttribute("idElementName", idElementName);
			request.setAttribute("descElementName", descElementName);
			log.debug(this.getClass().getName()
					+ "- [preSearchParties] - Exiting");
			return getView("searchParty");
		} catch (Exception exp) {
			exp.printStackTrace();

			log.error(this.getClass().getName()
					+ " - Exception Catched in [preSearchParties] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "preSearchParties", PartyAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This is used in Movement Notes Screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String searchParties()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName()
					+ "- [searchParties] - Entering");
			/* Method's local variable declaration */
			String idElementName = "";
			String descElementName = "";
			String hostId;
			Party party ;
			party= getParty();
			/* Class instance declaration */
			// DynaValidatorForm Party party = (Party) getParty();
			idElementName = request.getParameter("idElementName");
			/* Reading the party name from the request */
			descElementName = request.getParameter("descElementName");
			/* Retrieve the current hostid */
			hostId = SwtUtil.getCurrentHostId(request.getSession(false));
			/* Setting the hostid */
			party.getId().setHostId(hostId);
			log.debug(this.getClass().getName()
					+ "- [searchParties] - Party Details" + party);
			log.debug("Party Details - " + party);
			/* /*Retrieve Party details from DB based on the parameters passed. */
			Collection partyList = partyManager.getPartyList(party);
			request.setAttribute("partyList", partyList);
			request.setAttribute("isPreSearch", "N");
			request.setAttribute("idElementName", idElementName);
			request.setAttribute("descElementName", descElementName);
			setParty(party);
			log
					.debug(this.getClass().getName()
							+ "- [searchParties] - Exiting");
			return getView("searchParty");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "searchParties", PartyAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to search the particular party.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String search()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// String to hold the host id
		String hostId = null;
		// String to hold the current filter
		String currentFilter = null;
		// String to hold the sorting
		String currentSort = null;
		// Collection to hold the Party List
		Collection<Party> partyList = null;
		// String to hold the filter criteria
		String filterCriteria = null;
		// String to cold the filter & sort status
		StringBuffer filterSortStatus = null;
		// Integer declared to get the current page
		int currentPage;
		// String declared to get the entity Id
		String entityId = null;
		// Integer declared to get the page size
		int pageSize;
		// Integer declared to get the maximum page size
		int maxPage;
		// Integer declared to get the total count
		int totalCount;
		// Integer declared to get the EnitytMenu access values
		int accessInd;
		// Boolean declared to get the isNext value
		boolean isNext = false;
		// Declaration for valiadtor form
		// DynaValidatorForm dyForm = null;
		// Declaration for Pojo class
		Party party = null;
		// Declaration for filter values
		StringBuffer tempCurrentFilter = null;
		// Declaration for Filter token
		StringTokenizer filterToken = null;
		// Integer Declaration to determine filter counts
		int filterCount;
		// String declared to get the party Id
		String partyId = null;
		// String declared to get the Party name
		String partyName = null;
		// List declared to get the page summary list
		ArrayList<PartySummary> pageSummaryList = null;

		try {
			log.debug(this.getClass().getName() + "- [search] - Entering ");
			/* Method's local variable declaration */
			currentPage = 0;
			pageSize = 0;
			accessInd = 0;
			filterCount = 0;
			party = (Party) getParty();
			hostId = SwtUtil.getCurrentHostId(request.getSession(false));
			/* Retrieve filter value from request value parameter selectedFilter */
			currentFilter = request.getParameter("selectedFilter");
			/* Retrieve sorting value from request value parameter selected sort */

			currentSort = request.getParameter("selectedSort");

			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is null then set the currentSort to
			 * 0/false(ascending)
			 */
			if (currentSort == null) {
				currentSort = "0|false";
			}
			filterCriteria = request.getParameter("filterCriteria");

			/* Get the entity id from Bean class */
			entityId = party.getId().getEntityId();

			/*
			 * Condition to check the received filter from request parameter If
			 * current filter is null then set the current filter to All
			 */
			tempCurrentFilter = new StringBuffer();
			if (currentFilter == null) {
				currentFilter = "All|All|All|All|All";
			} else {

				filterToken = new StringTokenizer(currentFilter, "|");
				filterCount = 0;
				while (filterToken.hasMoreTokens()) {
					filterCount++;
					if (filterCount == 3) {
						tempCurrentFilter = tempCurrentFilter.append(
								getMiscparamKeyValue(entityId, filterToken
										.nextToken())).append("|");
					} else {
						tempCurrentFilter = tempCurrentFilter.append(
								filterToken.nextToken()).append("|");
					}
				}

			}
			filterSortStatus = new StringBuffer();
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/* Form Filter sort status from current Filter */
			if (tempCurrentFilter.length() == 0) {

				filterSortStatus = filterSortStatus.append(currentFilter);
			} else {
				filterSortStatus = filterSortStatus.append(tempCurrentFilter);
			}
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
			/*
			 * Condition to check the received sorting from request parameter If
			 * current sorting is none then set the currentSort to
			 * 0/false(ascending)
			 */
			if (currentSort.equals("none")) {
				currentSort = "0|false";
			}
			/* Default set for order by as based on party id */
			party.setOrderBy("party_id");
			/* Default sorting is set to Ascending order */
			party.setSortBy("Asc");
			party.setFilterCriteria(filterSortStatus.toString());

			/* Replace properties to PropertiesFileLoader class */
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);
			currentPage = 1;
			party.getId().setHostId(hostId);
			party.setStartIndex(0);
			party.setEndIndex(pageSize);

			setParty(party);
			party.getId().setHostId(hostId);
			/* Getting Party id and party name from bean class */
			partyId = party.getId().getPartyId();
			partyName = party.getPartyName();
			/* Condition to check party id and party name is null */
			if (partyId != null || partyName != null) {
				/* Retrieve the party details from database based on Parameters */
				partyList = partyManager.getCustodianList(party);

				if (partyList != null && partyList.size() <= 0) {
				/*	response
							.getWriter()
							.write(
									"<script>alert(\"Search Criteria Does not Match\");</script>");*/
					request.setAttribute("criteriaNotMatch", "yes");


					party.getId().setPartyId("");
					party.setPartyName("");
					/*
					 * Retrieve the party details from database based on
					 * Parameters
					 */
					partyList = partyManager.getCustodianList(party);
				}
				request.setAttribute("custodianColl", partyList);
			}
			maxPage = 0;
			totalCount = 0;

			if (filterCriteria == null) {
				/*
				 * Retrieve Total no of records from DB based on the parameters
				 * passed.
				 */
				totalCount = partyManager.getTotalCount(party);
			} else {
				/*
				 * This method is used to get the filter count from DB based on
				 * the parameter values passed.
				 */
				totalCount = partyManager.getFilterCount(party);
			}
			/* Maximum page is get by calculation */

			/*
			 * This used to sets maximum Page The maximum no of page count is
			 * done by calculation ie. The total record value got from the DB
			 * and the pageSize is got from the properties file.
			 */
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			request.setAttribute("custodianColl", partyList);
			/* Initializing pageSummaryList to arrayList */
			pageSummaryList = new ArrayList<PartySummary>();
			/* Used for setting the page no */
			setPageSummaryList(entityId, currentPage, maxPage, totalCount,
					pageSummaryList);
			/*
			 * Hide pagination if no of records are "0" or records can be
			 * displayed in one page.
			 */
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			/*
			 * Condition to check maximum page greater than one then set flag
			 * value to true
			 */
			if (maxPage > 1) {
				isNext = true;
			}
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);
			request.setAttribute("currentPage", "1");
			request.setAttribute("maxPage", maxPage + "");
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("custodianColl", partyList);
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("filterCriteria", filterCriteria);
			/*
			 * Retrieve and Store the user's menu,entity and currency group in
			 * SwtUtil
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->FullAccess If
			 * Value=1 ->View access
			 *
			 */
			if (accessInd == 0) {
				/* This is used to set the button status by enable buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button status by disable buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			setParty(party);
			log.debug(this.getClass().getName() + "- [search] - Exiting ");
			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [search] method : - "
					+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [search] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "search", PartyAction.class), request, "");

			return getView("fail");
		} finally {

			hostId = null;
			currentFilter = null;
			currentSort = null;
			partyList = null;
			filterCriteria = null;
			filterSortStatus = null;
			entityId = null;
			tempCurrentFilter = null;
			filterToken = null;
			partyId = null;
			partyName = null;
			pageSummaryList = null;
		}
	}

	/**
	 * This function allowed to load the party alias details in screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String displayAliasDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Method's local variable declaration */
		// Variable declaration for entityId
		String entityId = "";
		// Variable declaration for partyId
		String partyId = "";
		// Variable declaration for partyName
		String partyName = "";
		// Variable declaration for partyType
		String partyType = "";
		// Variable declaration for entityDesc
		String entityDesc = "";
		// Variable declaration for accessInd
		int accessInd;
		// Variable declaration for hostId
		String hostId;
		// Variable declaration for partyAliasList
		Collection partyAliasList = null;
		/* Class Instance declaration */
		Party par;
		// Variable declaration for dyForm
		// DynaValidatorForm dyForm = null;
		try {
			/* Reading selected entityid value from the request */
			entityId = request.getParameter("selectedEntityId");
			/* Reading selected partyid value from the request */
			partyId = request.getParameter("selectedPartyId");
			/* Reading selected party name value from the request */
			partyName = request.getParameter("partyName");
			/* Reading selected party type value from the request */
			partyType = request.getParameter("partyType");
			/* Reading selected entity name value from the request */
			entityDesc = request.getParameter("entityDesc");
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Retrieve Party alias details from DB based on the parameters
			 * passed.
			 */
			partyAliasList = partyManager.getPartyAliasList(hostId, entityId,
					partyId);
			request.setAttribute("partyAliasList", partyAliasList);
			par = new Party();
			/* Setting party id & entity id from bean class */
			par.getId().setPartyId(partyId);
			par.getId().setEntityId(entityId);

			request.setAttribute("partyName", partyName);
			request.setAttribute("partyType", partyType);
			request.setAttribute("entityDesc", entityDesc);
			setParty(par);
			/*
			 * Retrieve and Store the user's menu,entity and currency group in
			 * swtutil
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->Fullaccess If
			 * Value=1 ->View access
			 *
			 */
			if (accessInd == 0) {

				/* This is used to set the button staus by enable buttons */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button staus by disable buttons */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			log.debug(this.getClass().getName()
					+ "- [displayAliasDetails] - Exiting");

			return getView("alias");
		} catch (SwtException swtexp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [displayAliasDetails] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {

			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [displayAliasDetails] method : - "
							+ exp.getMessage());
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(exp, "displayAliasDetails",
									PartyAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to add alias name to parties.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addAlias()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [addAlias] - Entering ");
			String entityId = "";
			String partyId = "";
			String partyName = "";
			String entityDesc = "";
			Party par;
			// DynaValidatorForm dyForm = (DynaValidatorForm) (form);
			/* Reading selected entityid value from the request */
			entityId = request.getParameter("selectedEntityId");
			/* Reading selected party id value from the request */
			partyId = request.getParameter("selectedPartyId");
			/* Reading selected party name value from the request */
			partyName = request.getParameter("partyName");
			/* Reading selected entity name value from the request */
			entityDesc = request.getParameter("entityDesc");
			par = new Party();
			/* Setting Party id & entity Id */
			par.getId().setPartyId(partyId);
			par.getId().setEntityId(entityId);
			request.setAttribute("partyDescription", partyName);
			request.setAttribute("entityDescription", entityDesc);

			setParty(par);
			log.debug(this.getClass().getName() + "- [addAlias] - Exiting");
			return getView("addAlias");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [addAlias] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "addAlias", PartyAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to save the alias name.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String saveAliasRecord() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Method's local variable declaration */
		String hostId = "";
		String entityId = "";
		String partyAlias = "";
		/* Class Instance declaration */
		PartyAlias parAlias;
		ActionErrors errors;
		SystemInfo systemInfo;
		// Variable declaration for dyForm
		// DynaValidatorForm dyForm = null;
		try {
			errors = new ActionErrors();
			systemInfo = new SystemInfo();
			Party party = (Party) getParty();
			parAlias = new PartyAlias();
			/* Retrieve and store hostId from Properties file using CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Get entity id from bean class */
			entityId = party.getId().getEntityId();
			/* Get Party alias value from bean class */
			partyAlias = party.getPartyAlias();
			/* set host id from bean class */
			parAlias.getId().setHostId(hostId);
			/* set entity id from bean class */
			parAlias.getId().setEntityId(entityId);
			/* set party alias from bean class */
			parAlias.getId().setPartyAlias(partyAlias);
			/* set party id from bean class */
			parAlias.setPartyId(party.getId().getPartyId());
			/* Save the alias details to database */
			partyManager.saveAliasDetails(parAlias);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedPartyId", party.getId().getPartyId());
			request.setAttribute("entityDesc", request
					.getParameter("entityDesc"));
			request
					.setAttribute("partyName", request
							.getParameter("partyName"));
			setParty(party);
			request.setAttribute("parentRefreshRequired", "No");
			log.debug(this.getClass().getName()
					+ "- [saveAliasRecord] - Exiting");
			return getView("addAlias");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAliasRecord] method : - "
					+ swtexp.getMessage());

			request.setAttribute("entityDescription", request
					.getParameter("entityDesc"));
			request.setAttribute("partyDescription", request
					.getParameter("partyName"));
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("addAlias");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveAliasRecord] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "saveAliasRecord", PartyAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to delete the alias name.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteAliasRecord() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		String partyName = null;
		String entityDesc = null;
		String hostId = null;
		/* Class Instance declaration */
		ActionErrors errors;
		SystemInfo systemInfo;
		PartyAlias parAlias;
		// DynaValidatorForm dyForm = null;
		Party party = null;
		/* Retrieve and store hostId from Properties file using CacheManager */

		/* Reading entity id from bean class */
		String entityId = null;
		/* Reading selected party alias value from request */
		String partyAlias = null;
		/* Reading party id from bean class */
		String partyId = null;
		// Variable declaration for partyAliaslist
		Collection partyAliasList = null;
		// Variable declaratyion for accessInd
		int accessInd = 0;
		try {

			errors = new ActionErrors();
			systemInfo = new SystemInfo();
			/* Reading party name from request */
			partyName = request.getParameter("partyName");
			/* Reading entity name from request */
			entityDesc = request.getParameter("entityDesc");
			request.setAttribute("partyName", partyName);
			request.setAttribute("entityDesc", entityDesc);


			party = (Party) getParty();

			hostId = CacheManager.getInstance().getHostId();

			entityId = party.getId().getEntityId();

			partyAlias = request.getParameter("selectedPartyAlias");

			partyId = party.getId().getPartyId();

			parAlias = new PartyAlias();
			/* Setting host id from bean class */
			parAlias.getId().setHostId(hostId);
			/* Setting entity id from bean class */
			parAlias.getId().setEntityId(entityId);
			/* Setting party alias from bean class */
			parAlias.getId().setPartyAlias(partyAlias);
			/* Setting party id from bean class */
			parAlias.setPartyId(partyId);
			log.debug(this.getClass().getName()
					+ "- [deleteAliasRecord] -  Deleted Details: " + parAlias);
			/*
			 * Retrieve and Store the user's menu,entity and currency group in
			 * swtutil
			 */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/*
			 * Condition to check the access rights. If value=0 ->Fullaccess If
			 * Value=1 ->View access
			 *
			 */
			if (accessInd == 0) {
				/* This is used to set the button staus by enable buttons */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button staus by disable buttons */
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}

			/* Used to delete the party alias details from database */
			partyManager.deleteAliasDetails(parAlias);
			/*
			 * Retrieve Party alias details from DB based on the parameters
			 * passed.
			 */
			partyAliasList = partyManager.getPartyAliasList(hostId, entityId,
					partyId);

			request.setAttribute("partyAliasList", partyAliasList);


			setParty(party);
			log.debug(this.getClass().getName()
					+ "- [deleteAliasRecord] - Exiting");
			return getView("alias");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAliasRecord] method : - "
					+ swtexp.getMessage());
			/* Retrieve party alias List from DB based on the parameters passed. */
			partyAliasList = partyManager.getPartyAliasList(hostId, entityId,
					partyId);
			request.setAttribute("partyAliasList", partyAliasList);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAliasRecord] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "deleteAliasRecord", PartyAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		if (maxPage == 0 && totalCount > 0) { // Ensure at least one page if there's data
			maxPage = 1;
		} else if (totalCount == 0) {
			maxPage = 0; // Or 1 if you prefer to show page 1 of 1 for no records
		}
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}


	// --- New/Adapted Methods for Angular ---

	public String displayAngular() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String hostId = null;
		String entityId = null;
		String currentUserId = null;
		SystemFormats sysFormat = null;
		String dateFormat = null;

		try {
			log.debug(this.getClass().getName() + " - [displayAngular] - Entry");
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement("partyList"); // Root element

			hostId = CacheManager.getInstance().getHostId();
			currentUserId = SwtUtil.getCurrentUserId(request.getSession());
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat = sysFormat.getDateFormatValue();

			entityId = request.getParameter("entityId");
			if (SwtUtil.isEmptyOrNull(entityId) || "All".equalsIgnoreCase(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			String partyIdSearch = request.getParameter("partyId") == null ? "" : request.getParameter("partyId");
			String partyNameSearch = request.getParameter("partyName") == null ? "" : request.getParameter("partyName");
			String selectedSort = request.getParameter("selectedSort") == null ? "0|false" : request.getParameter("selectedSort");
			String selectedFilter = request.getParameter("selectedFilter") == null ? "All|All|All|All|All" : request.getParameter("selectedFilter");
			int currentPage = Integer.parseInt(request.getParameter("currentPage"));
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.PARTY_SUMMARY_SCREEN_PAGE_SIZE);

			Party criteria = new Party();
			criteria.getId().setHostId(hostId);
			criteria.getId().setEntityId(entityId);
			criteria.getId().setPartyId(partyIdSearch.trim());
			criteria.setPartyName(partyNameSearch.trim());

			// Apply sort (logic adapted from existing 'next' or 'displayList')
			if (selectedSort != null) {
				int intOrderBy = Integer.parseInt(selectedSort.substring(0, 1));
				String sortByDir = selectedSort.substring(2, selectedSort.length()).trim().replace('|', ' ').trim();
				String orderByField = "party_id"; // default
				if (intOrderBy == 1) orderByField = "party_name";
				else if (intOrderBy == 2) orderByField = "party_type";
				else if (intOrderBy == 3) orderByField = "parent_party_id";
				else if (intOrderBy == 4) orderByField = "alias"; // Assuming 'alias' is a valid sort field in manager
				criteria.setOrderBy(orderByField);
				criteria.setSortBy(Boolean.parseBoolean(sortByDir) ? "Desc" : "Asc");
			}

			// Apply filter (simplified, adapt if complex filtering like 'getMiscparamKeyValue' is needed)
			// For Angular, it's better if the filter string sent by client is already backend-ready.
			// Or, parse it here similar to how existing methods handle it.
			// Assuming filter string is directly usable or needs parsing:
			String filterForManager = parseAngularFilterToBackendFormat(selectedFilter, entityId);
			//	criteria.setFilterCriteria(filterForManager + "<split>" + selectedSort); // Or just selectedFilter if manager handles it
			criteria.setFilterCriteria(filterForManager); // Or just selectedFilter if manager handles it

			int totalCount = partyManager.getFilterCount(criteria); // Or getTotalCount if no filter applied initially
			int maxPage = setMaxPageAttribute(totalCount, pageSize);
			if (currentPage > maxPage && maxPage > 0) currentPage = maxPage;
			int endIndex = currentPage * pageSize;
			int startIndex = endIndex - pageSize;
			criteria.setStartIndex(startIndex);
			criteria.setEndIndex(endIndex); // Manager method usually takes count/pageSize not endIndex directly

			Collection<Party> partyList = partyManager.getCustodianList(criteria);

			responseConstructor.formRequestReply(true, "Data fetched successfully.");

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntityId", entityId);
			responseConstructor.createElement("partyIdSearch", partyIdSearch);
			responseConstructor.createElement("partyNameSearch", partyNameSearch);
			responseConstructor.createElement("dateFormat", dateFormat);
			int accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
			responseConstructor.createElement("menuEntityCurrGrpAccess", String.valueOf(accessInd));
			responseConstructor.createElement("swt_add_btn_sts", String.valueOf(accessInd == 0 )); // See note below
			responseConstructor.createElement("swt_chg_btn_sts", String.valueOf(accessInd == 0 ));
			responseConstructor.createElement("swt_del_btn_sts", String.valueOf(accessInd == 0 ));
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// Selects for Entity Dropdown
			Collection<LabelValueBean> entityLvbList = getEntityListForSelect(request);
			ArrayList<OptionInfo> entityOptions = new ArrayList<>();
			for (LabelValueBean lvb : entityLvbList) {
				entityOptions.add(new OptionInfo(lvb.getValue(), lvb.getLabel(), lvb.getValue().equals(entityId)));
			}
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			lstSelect.add(new SelectInfo("entityList", entityOptions));
			responseConstructor.formSelect(lstSelect);

			// Grid Data
			responseConstructor.formGridStart("grid");
			String colWidths = SwtUtil.getPropertyValue(request, entityId, SwtConstants.SCHEDULER_SCREEN_ID, "display", "column_width");
			String colOrder = SwtUtil.getPropertyValue(request, entityId, SwtConstants.SCHEDULER_SCREEN_ID, "display", "column_order");

			responseConstructor.formColumn(getPartyGridColumnsAngular(colWidths, colOrder, null, request));
			responseConstructor.formPaging(new PageInfo(maxPage, currentPage));
			responseConstructor.formRowsStart(totalCount);
			if (partyList != null) {
				for (Party p : partyList) {
					responseConstructor.formRowStart();
					responseConstructor.createRowElement("partyId", p.getId().getPartyId());
					responseConstructor.createRowElement("partyName", p.getPartyName());
					responseConstructor.createRowElement("partyTypeDesc", getMiscparamParValue(entityId,p.getPartyType())); // Using existing helper
					responseConstructor.createRowElement("parentParty", p.getParentParty());
					responseConstructor.createRowElement("noOfAliases", String.valueOf(p.getNoOfAliasesAsString())); // Assuming getNoOfAliases returns int/long
					// Add hidden elements if needed by frontend for context
					responseConstructor.createRowElement("rawPartyType", p.getPartyType());
					responseConstructor.formRowEnd();
				}
			}
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement("partyList");
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");

		} catch (SwtException swtexp) {
		log
				.error(this.getClass().getName()
					   + " - Exception Catched in [displayListBalanceTypeAngular] method : - "
					   + swtexp.getMessage());
		SwtUtil.logException(swtexp, request, "");
		return getView("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
						   + " - Exception Catched in [displayListBalanceTypeAngular] method : - "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListBalanceTypeAngular", PartyAction.class),
					request, "");
			return getView("fail");
		}
	}

	// Note on button statuses (ADD_BUT_STS_FOR_ANGULAR etc.):
	// The original JSP uses request.getAttribute(SwtConstants.ADD_BUT_STS) which are set by setButtonStatus.
	// For the initial load via displayAngular, these might need to be explicitly set or calculated
	// before being put into singletons, perhaps by calling a modified setButtonStatus that just returns the flags.
	// Or, the JSP shell can set these into initialParams as shown in revised PartyMaintenance.jsp.

	private String parseAngularFilterToBackendFormat(String angularFilter, String entityId) throws SwtException {
		if (SwtUtil.isEmptyOrNull(angularFilter) || "All|All|All|All|All".equals(angularFilter)) {
			return "All|All|All|All|All"; // Or what the manager expects for no filter
		}
		StringTokenizer st = new StringTokenizer(angularFilter, "|");
		StringBuffer backendFilter = new StringBuffer();
		int tokenIndex = 0;
		while (st.hasMoreTokens()) {
			String token = st.nextToken();
			if (tokenIndex == 2) { // Assuming Party Type is the 3rd filter field (index 2)
				backendFilter.append(getMiscparamKeyValue(entityId, token)); // Convert description to code
			} else {
				backendFilter.append(token);
			}
			if (st.hasMoreTokens()) {
				backendFilter.append("|");
			}
			tokenIndex++;
		}
		// Ensure it has 5 pipe segments if manager expects that many
		while(tokenIndex < 4) { // if less than 5 tokens, add trailing |All
			backendFilter.append("|All");
			tokenIndex++;
		}
		return backendFilter.toString();
	}


	private List<ColumnInfo> getPartyGridColumnsAngular(String widthPrefs, String orderPrefs, String hiddenColPrefs, HttpServletRequest request) throws SwtException {
		List<ColumnInfo> lstColumns = new ArrayList<>();
		LinkedHashMap<String, String> widths = new LinkedHashMap<>();
		LinkedHashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<>();
		ArrayList<String> orders = new ArrayList<>();

		// Define default order
		String[] defaultOrder = new String[] { "partyId", "partyName", "partyTypeDesc", "parentParty", "noOfAliases" };

		// Default width string
		String defaultWidthsStr = "partyId=135,partyName=300,partyTypeDesc=100,parentParty=135,noOfAliases=100";

		// === Parse Widths ===
		if (SwtUtil.isEmptyOrNull(widthPrefs)) {
			widthPrefs = defaultWidthsStr;
		}
		for (String pair : widthPrefs.split(",")) {
			if (pair.contains("=")) {
				String[] parts = pair.split("=");
				if (parts.length == 2) {
					widths.put(parts[0], parts[1]);
				}
			}
		}

		// === Parse Orders ===
		if (SwtUtil.isEmptyOrNull(orderPrefs)) {
			orders.addAll(Arrays.asList(defaultOrder));
		} else {
			orders.addAll(Arrays.asList(orderPrefs.split(",")));
		}
		for (String col : orders) {
			hiddenColumnsMap.put(col, true); // default visible
		}

		// === Parse Hidden Columns ===
		if (!SwtUtil.isEmptyOrNull(hiddenColPrefs)) {
			for (String hiddenCol : hiddenColPrefs.split(",")) {
				if (hiddenColumnsMap.containsKey(hiddenCol)) {
					hiddenColumnsMap.put(hiddenCol, false); // mark as hidden
				}
			}
		}

		int i = 0;
		for (String columnKey : orders) {
			String label = "";
			String tooltip = "";
			String type = SwtConstants.COLUMN_TYPE_STRING;
			int defaultWidth = 150;

			if ("partyId".equals(columnKey)) {
				label = SwtUtil.getMessage("party.partyId", request);
				tooltip = SwtUtil.getMessage("tooltip.sortParty", request);
				defaultWidth = 135;
			} else if ("partyName".equals(columnKey)) {
				label = SwtUtil.getMessage("party.partyName", request);
				tooltip = SwtUtil.getMessage("tooltip.sortname", request);
				defaultWidth = 300;
			} else if ("partyTypeDesc".equals(columnKey)) {
				label = SwtUtil.getMessage("party.header", request);
				tooltip = SwtUtil.getMessage("tooltip.sortByType", request);
				defaultWidth = 100;
			} else if ("parentParty".equals(columnKey)) {
				label = SwtUtil.getMessage("party.parentParty", request);
				tooltip = SwtUtil.getMessage("tooltip.sortbyParentParty", request);
				defaultWidth = 135;
			} else if ("noOfAliases".equals(columnKey)) {
				label = SwtUtil.getMessage("party.alias", request);
				tooltip = SwtUtil.getMessage("tooltip.numberOfAliases", request);
				defaultWidth = 100;
				type = SwtConstants.COLUMN_TYPE_NUMBER;
			}

			int currentWidth = parseIntSafe(widths.getOrDefault(columnKey, String.valueOf(defaultWidth)), defaultWidth);
			boolean visible = hiddenColumnsMap.getOrDefault(columnKey, true);

			ColumnInfo tmpCol = new ColumnInfo(
					label,
					columnKey,
					type,
					true,
					i,
					currentWidth,
					false,
					true,
					visible
			);

			tmpCol.setHeaderTooltip(tooltip);
			tmpCol.setEditable(false);
			lstColumns.add(tmpCol);
			i++;
		}
		return lstColumns;
	}

	private int parseIntSafe(String val, int defaultVal) {
		try {
			return Integer.parseInt(val);
		} catch (Exception e) {
			return defaultVal;
		}
	}


	public String savePartyColumnOrder() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		String columnOrder = request.getParameter("order");
		String entityId = request.getParameter("entityid"); // Usually the current entity
		try {
			if (columnOrder != null && entityId != null) {
				//	SwtUtil.setPropertyValue(request, entityId, SwtConstants.PARTY_MAINTENANCE_GRID_PREFS_ID, "display", "column_order", columnOrder);
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved.");
		} catch (Exception e) {
			log.error("Error saving party column order: " + e.getMessage(), e);
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", "Error saving column order: " + e.getMessage());
		}
		return getView("statechange");
	}

	/**
	 * This method is used to save the dataGrid's column width in the database
	 * based on the user
	 *
	 * @return - ActionForward object
	 * @throws SwtException
	 */
	public String savePartyColumnWidth() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String width = null;
		try {
			if (!SwtUtil.isEmptyOrNull(width = request.getParameter("width"))) {
				SwtUtil.setPropertyValue(request,
						SwtUtil.getUserCurrentEntity(request.getSession()),
						SwtConstants.SCHEDULER_SCREEN_ID, "display", "column_width", width);
			} else {
				throw new Exception(
						"You must send 'width' and 'entityid' parameters");
			}
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");
			log.debug(this.getClass().getName()
					  + " - [saveColumnWidth] - Exit ");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [saveColumnWidth] - Error - " + e.getMessage());
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute(
					"reply_location",
					e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"saveColumnWidth",
							AcctSpecificSweepFormatAction.class), request,
					"");
		}
		return getView("statechange");
	}


	public String checkPartyUseAngular() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String hostId = CacheManager.getInstance().getHostId();
		String entityId = request.getParameter("entityId");
		String partyId = request.getParameter("partyId");
		boolean partyInUse = false;

		try {
			log.debug(this.getClass().getName() + " - [checkPartyUseAngular] - Entry");
			partyInUse = partyManager.checkPartyUse(hostId, entityId, partyId);

			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement("partyCheckResponse");
			responseConstructor.formRequestReply(true, "Check complete.");
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("partyCheckFlag", String.valueOf(partyInUse));
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement("partyCheckResponse");

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayViewLog' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displadisplayViewLogyLog' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayViewLog", AcctCcyPeriodMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	public String deletePartyAngular() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		String hostId = CacheManager.getInstance().getHostId();
		String userId = SwtUtil.getCurrentUserId(request.getSession());
		String entityId = request.getParameter("entityId");
		String partyIdToDelete = request.getParameter("partyId");

		try {
			log.debug(this.getClass().getName() + " - [deletePartyAngular] - Deleting party: " + partyIdToDelete + " in entity: " + entityId);

			Party partyToDelete = new Party();
			partyToDelete.getId().setHostId(hostId);
			partyToDelete.getId().setEntityId(entityId);
			partyToDelete.getId().setPartyId(partyIdToDelete);
			partyToDelete.setUpdateUser(userId); // For audit purposes

			// It's assumed checkPartyUseAngular was called by frontend first.
			// If not, add the check here again for safety.
			// boolean partyInUse = partyManager.checkPartyUse(hostId, entityId, partyIdToDelete);
			// if (partyInUse) {
			//    throw new SwtException("errors.partyInUse", "Party is currently in use and cannot be deleted.");
			// }

			partyManager.deleteCustodianDetail(partyToDelete);

			// After successful deletion, return the refreshed list for the current view
			// To do this, we effectively call the core logic of displayAngular again.
			// Parameters for refresh would come from the original deletePartyAngular request
			// This is a simplified way; displayAngular might need to be refactored to allow internal calls
			// For now, we will just signal success and let Angular call displayAngular itself.
			SwtResponseConstructor responseConstructor = new SwtResponseConstructor();
			SwtXMLWriter xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement("deletePartyResponse");
			responseConstructor.formRequestReply(true, "Party deleted successfully.");
			xmlWriter.endElement("deletePartyResponse");
			request.setAttribute("data", xmlWriter.getData());
			return getView("data"); // Or "data" if client expects some specific simple payload

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctCcyPeriodMaintenanceAction.'displayViewLog' method : "
						   + swtexp.getMessage());
			SwtResponseConstructor responseConstructor = new SwtResponseConstructor();
			SwtXMLWriter xmlWriter = responseConstructor.getXMLWriter();
			xmlWriter.startElement("deletePartyResponse");
			responseConstructor.formRequestReply(true, "Party deleted successfully.");
			xmlWriter.endElement("deletePartyResponse");
			SwtUtil.logException(swtexp, request, "");
			return getView("data");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctCcyPeriodMaintenanceAction.'displadisplayViewLogyLog' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayViewLog", AcctCcyPeriodMaintenanceAction.class), request, "");
			return getView("fail");
		}
	}

	private Collection<LabelValueBean> getEntityListForSelect(HttpServletRequest request) throws SwtException {
		HttpSession session = request.getSession();
		Collection<EntityUserAccess> coll = SwtUtil.getUserEntityAccessList(session);
		return SwtUtil.convertEntityAcessCollectionLVL(coll, session);
	}

	// This method might be called by displayAngular or when formatting grid data
	public String getMiscparamParValue(String entityId, String parValue) throws SwtException {
		if (SwtUtil.isEmptyOrNull(parValue) || "All".equalsIgnoreCase(parValue.trim())) {
			return parValue; // Return "All" as is, or "" if parValue is empty
		}
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String keyValue = "";
		Collection coll = cacheManagerInst.getMiscParams("PARTYTYPE", entityId); // Assuming PARTYTYPE is the misc param category
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				MiscParams miscParamsObj = (MiscParams) itr.next();
				// This logic assumes parValue is the description and we need the key (code)
				// Or vice-versa depending on how PartyType is stored and displayed
				if (miscParamsObj.getParValue().trim().equalsIgnoreCase(parValue.trim())) {
					keyValue = miscParamsObj.getId().getKey2();
					break;
				} else if (miscParamsObj.getId().getKey2().trim().equalsIgnoreCase(parValue.trim())) {
					// If parValue was already the key, we might want the description
					keyValue = miscParamsObj.getParValue(); // Or keep it as the key if that's what's needed
					break;
				}
			}
		}
		return SwtUtil.isEmptyOrNull(keyValue) ? parValue : keyValue; // Return original if no match, or matched key
	}

	/**
	 * Method used to pass the parvalue and get the description in alert message
	 * event
	 *
	 * @param request
	 * @param parValue
	 * @return String
	 * @throws Exception
	 */
	public String getMiscparamKeyValue(String entityId, String parValue)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [alertMessageEvent] - "
				+ "Entry");

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String KeyValue = "";
		/* Collect the Misc Params of Alert messages */
		Collection coll = cacheManagerInst.getMiscParams("PARTYTYPE", entityId);
		if (coll != null) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				MiscParams miscParamsObj = (MiscParams) itr.next();
				if (miscParamsObj.getParValue().equals(parValue.trim())) {
					KeyValue = miscParamsObj.getId().getKey2();
					break;
				}
			}
		}
		log.debug(this.getClass().getName() + " - [getMiscparamKeyValue] - "
				+ "Exit");
		if (parValue.trim().equals("All")) {
			return parValue;
		} else {
			return KeyValue;
		}

	}

	/**
	 * This method is used to check wheather the changed Parent Party is forming
	 * cylic loop.If it is forming cyclic loop it will return '1' other wise it
	 * will return '0' for valid Parent Id
	 *
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public String checkParentId()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// String declered to hold the host id
		String hostId = null;
		// Integer declered to get the status
		int status;
		// String declered to get the entity ID
		String entityId = null;
		// String declred to get the party Id
		String partyId = null;
		// String declred to get the Parent Id
		String changeParentId = null;
		String error = null;
		try {
			log.debug(this.getClass().getName() + " - [checkParentId] - Entry");
			status = 0;
			// gets current hostID
			hostId = CacheManager.getInstance().getHostId();
			// gets entityId,partyId,Parent Id form request
			entityId = request.getParameter("entityId");
			partyId = request.getParameter("partyId");
			changeParentId = request.getParameter("changeParentId");
			// Get the stauis of parentId
			status = partyManager.checkParentId(hostId, entityId, partyId,
					changeParentId);
			// Returning response to AJAX request
			response.getWriter().print(status);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkParentId] method : - "
					+ e.getMessage());
			try {
				response.getWriter().print(e.getMessage());
			} catch (IOException Ioe) {

				log.error(this.getClass().getName()
						+ " - Exception Catched in [checkParentId] method : - "
						+ Ioe.getMessage());
			}

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "checkParentId", PartyAction.class), request, "");
		} finally {

			hostId = null;
			entityId = null;
			partyId = null;
			changeParentId = null;
			log.debug(this.getClass().getName() + " - [checkParentId] - Exit");
		}
		return null;
	}

	/**
	 * This method is used to check whether the given party is referred by any
	 * child party in the same entity or not, while deleting the party. If it is
	 * referred system will not allow deleting the party.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public String checkPartyUse()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration to hold the party id
		String partyId = null;
		// Variable declaration to hold the entity id
		String entityId = null;
		// Variable declaration to hold the host id
		String hostId = null;
		// Flag declared to hold the delete status of the selected party to
		// delete
		boolean partyStatus = false;
		try {
			log.debug(this.getClass().getName() + " - [checkPartyUse] - Entry");
			// assigning partyId and entitiy id
			partyId = request.getParameter("partyId");
			entityId = request.getParameter("entityId");
			// get the host id
			hostId = CacheManager.getInstance().getHostId();
			// getting party status from the mangaer class
			partyStatus = partyManager.checkPartyUse(hostId, entityId, partyId);
			// sending response in Ajax request
			response.getWriter().print(partyStatus);
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkPartyUse] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "checkParentId", PartyAction.class), request, "");
		} finally {
			partyId = null;
			entityId = null;
			hostId = null;
			log.debug(this.getClass().getName() + " - [checkPartyUse] - Exit");
		}
		return null;
	}

}
