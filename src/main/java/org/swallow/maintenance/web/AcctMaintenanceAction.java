/*
 * @(#)AcctMaintenanceAction.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccSweepSchedule;
import org.swallow.maintenance.model.AccountInterestRate;
import org.swallow.maintenance.model.AccountSpecificSweepFormat;
import org.swallow.maintenance.model.AccountSweepBalanceGroup;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Country;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.model.SweepIntermediaries;
import org.swallow.maintenance.service.AcctMaintenanceDetailVO;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.AcctSpecificSweepFormatManager;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.pcm.maintenance.model.PCMCurrencyDetailsVO;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.MovementSearchManager;

import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionErrors;
import org.swallow.util.struts.ActionMessage;




























/**
 * This AcctMaintenanceAction is used handle Account maintenance screen
 */
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/acctMaintenance", "/acctMaintenance.do"})
public class AcctMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/accountsmaintenanceadd");
		viewMap.put("addScheduleSweep", "jsp/maintenance/acctmaintschedulesweepsdetails");
		viewMap.put("addIntRate", "jsp/maintenance/accountinterestrateadd");
		viewMap.put("data", "jsp/data");
		viewMap.put("change", "jsp/maintenance/accountsmaintenanceadd");
		viewMap.put("subAccountsAdd", "jsp/maintenance/accountsmaintenance");
		viewMap.put("displayAccInterestRate", "jsp/maintenance/accountinterestrate");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("save", "jsp/maintenance/accountsmaintenanceadd");
		viewMap.put("update", "jsp/maintenance/accountsmaintenanceadd");
		viewMap.put("displayFormat", "jsp/maintenance/format");
		viewMap.put("delete", "jsp/maintenance/accountsmaintenance");
		viewMap.put("copyFrom", "jsp/maintenance/copyfromaccountId");
		viewMap.put("subAccounts", "jsp/maintenance/accountsmaintenance");
		viewMap.put("fail", "error");
		viewMap.put("view", "jsp/maintenance/accountsmaintenanceadd");
		viewMap.put("success", "jsp/maintenance/accountmaintenance");
		viewMap.put("acctsweepbalgrp", "jsp/maintenance/acctsweepbalancegrpflex");
		viewMap.put("displayContact", "jsp/maintenance/contact");
		viewMap.put("acctScheduleSweeps", "jsp/maintenance/accountsmaintenanceschedulesweeps");
		viewMap.put("partysearch", "jsp/pc/work/partysearch");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");





































































	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "displayListByEntity":
				return displayListByEntity();
			case "displayListAccountMaintenance":
				return displayListAccountMaintenance();
			case "displayListByCurrency":
				return displayListByCurrency();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "getAccsSweepSchedule":
				return getAccsSweepSchedule();
			case "update":
				return update();
			case "acctScheduleSweepDetails":
				return acctScheduleSweepDetails();
			case "accountChangedHandler":
				return accountChangedHandler();
			case "scheduleSweepDetailsData":
				return scheduleSweepDetailsData();
			case "saveUpdateAccountScheduleSweep":
				return saveUpdateAccountScheduleSweep();
			case "getAccountScheduleSweepList":
				return getAccountScheduleSweepList();
			case "deleteAccountScheduleSweep":
				return deleteAccountScheduleSweep();
			case "view":
				return view();
			case "delete":
				return delete();
			case "deleteAccountAngular":
				return deleteAccountAngular();
			case "subAccounts":
				return subAccounts();
			case "copyFrom":
				return copyFrom();
			case "copy":
				return copy();
			case "getLinkedAccounts":
				return getLinkedAccounts();
			case "displayContact":
				return displayContact();
			case "saveContactDetails":
				return saveContactDetails();
			case "displayAccInterestRate":
				return displayAccInterestRate();
			case "changeAcctInterestRate":
				return changeAcctInterestRate();
			case "updateAcctInterestRate":
				return updateAcctInterestRate();
			case "addAcctInterestRate":
				return addAcctInterestRate();
			case "saveAcctInterestRate":
				return saveAcctInterestRate();
			case "deleteAcctInterestRate":
				return deleteAcctInterestRate();
			case "getIntermediaryRecord":
				return getIntermediaryRecord();
			case "getLinkAccountList":
				return getLinkAccountList();
			case "getMainAccountList":
				return getMainAccountList();
			case "getBookCodeList":
				return getBookCodeList();
			case "getCountryList":
				return getCountryList();
			case "getFormatList":
				return getFormatList();
			case "getCopyFromAccountList":
				return getCopyFromAccountList();
			case "checkIfPartyIdExists":
				return checkIfPartyIdExists();
			case "getPartyNameForAjax":
				return getPartyNameForAjax();
			case "handleAcctSweepBalGrp":
				return handleAcctSweepBalGrp();
			case "displayAcctSweepBalGrp":
				return displayAcctSweepBalGrp();
			case "closeAcctSweepScreen":
				return closeAcctSweepScreen();
			case "saveUpdateAccountSweepBalGrp":
				return saveUpdateAccountSweepBalGrp();
			case "getAssAcctSweepBalGrpSize":
				return getAssAcctSweepBalGrpSize();
			case "checkAcctsAutoSwpFlag":
				return checkAcctsAutoSwpFlag();
			case "getUpdatedLists":
				return getUpdatedLists();
			case "displayListByEntityAngular":
				return displayListByEntityAngular();
			case "subAccountsAngular":
				return subAccountsAngular();
			case "saveColumnOrder":
				return saveColumnOrder();
			case "saveColumnWidth":
				return saveColumnWidth();
			case "checkAccountIlmDataMember":
				return checkAccountIlmDataMember();
		}


		return displayListByEntity();
	}


	private AcctMaintenance acctMaintenance;
	public AcctMaintenance getAcctMaintenance() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		acctMaintenance = RequestObjectMapper.getObjectFromRequest(AcctMaintenance.class, request,"acctMaintenance");
		return acctMaintenance;
	}

	public void setAcctMaintenance(AcctMaintenance acctMaintenance) {
		this.acctMaintenance = acctMaintenance;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("acctMaintenance", acctMaintenance);
	}



	private String selectedEntityId;
	public String getSelectedEntityId() {
		return selectedEntityId;
	}
	public void setSelectedEntityId(String selectedEntityId) {
		this.selectedEntityId = selectedEntityId;
	}


	private String selectedAccountId;
	public String getSelectedAccountId() {
		return selectedAccountId;
	}
	public void setSelectedAccountId(String selectedAccountId) {
		this.selectedAccountId = selectedAccountId;
	}

	private SweepIntermediaries sweepintermediaries;

	public SweepIntermediaries getSweepintermediaries() {
		return sweepintermediaries;
	}
	public void setSweepintermediaries(SweepIntermediaries sweepintermediaries) {
		this.sweepintermediaries = sweepintermediaries;
	}

	private AccountInterestRate acctInterestRate;

	public AccountInterestRate getAcctInterestRate() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		acctInterestRate = RequestObjectMapper.getObjectFromRequest(AccountInterestRate.class, request, "acctInterestRate");
		return acctInterestRate;
	}

	public void setAcctInterestRate(AccountInterestRate acctInterestRate) {
		this.acctInterestRate = acctInterestRate;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("acctInterestRate", acctInterestRate);
	}



	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(AcctMaintenanceAction.class);

	/**
	 * An instance of AcctMaintenanceManager to be used across the application
	 */
	@Autowired
	private AcctMaintenanceManager acctMaintenanceManager = null;

	/**
	 * This method is used across this action class to return host Id
	 *
	 * @param request
	 * @return String
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		// Cache Manager
		CacheManager cacheManagerInst = null;
		// Host Id
		String hostId = null;
		try {
			// get the CacheManager Instance
			cacheManagerInst = CacheManager.getInstance();
			// get the hostid
			hostId = cacheManagerInst.getHostId();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putHostIdListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			cacheManagerInst = null;
		}
		// return the host id
		return hostId;
	}

	/**
	 * This method is used across this action class to add the accountstatus
	 * into request
	 *
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	private void putAcctStatusListInReq(HttpServletRequest request,
										String entityId) throws SwtException {
		// Cache Manager
		CacheManager cacheManagerInst = null;
		// accountStatus Collection
		Collection accountStatusColl = null;
		try {
			// get the Cache Manager Instance
			cacheManagerInst = CacheManager.getInstance();
			// get the Account Status according to the entityId
			accountStatusColl = cacheManagerInst.getMiscParamsLVL(
					"ACCOUNTSTATUS", entityId);
			request.setAttribute("acctmaintstatuslist", accountStatusColl);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putAcctStatusListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			cacheManagerInst = null;
			accountStatusColl = null;
		}
	}

	/**
	 * This method is used across this action class to add the targetsign into
	 * request
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putTargetSignAndSettMethodListsInReq(HttpServletRequest request,
													  String entityId) throws SwtException {
		// Cache Manager
		CacheManager cacheManagerInst = null;
		// Target Sign Collection
		Collection targetSignCol = null;
		// Target Sign List
		ArrayList targetSignList = null;
		try {
			// get the Cache Manager Instance
			cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"TARGETBALANCESIGN", entityId);
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			// remove first one from list if collection size is three.
			if (targetSignCol.size() == 3) {
				targetSignList.remove(0);
			}
			request.setAttribute("targetsignlist", targetSignList);


			cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", entityId);
			// get the list of the target sign.
			targetSignList = (ArrayList) targetSignCol;
			request.setAttribute("defaultSettleMethodList", targetSignList);

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putTargetSignListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			cacheManagerInst = null;
			targetSignCol = null;
			targetSignList = null;
		}
	}

	/**
	 * This method is used across this action class to add the entities into
	 * request
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		// entity collection
		Collection entityColl = null;
		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			// Convert the entity list to label value bean.
			entityColl = SwtUtil.convertEntityAcessCollectionLVLFullName(entityColl,
					request.getSession());
			// set the entity collection into request.
			request.setAttribute("entities", entityColl);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
		}

		return entityColl;

	}

	/**
	 * This method is used across this action class to add the account type into
	 * request
	 *
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	private void putAccttypeListInReq(HttpServletRequest request,
									  String entityId) throws SwtException {
		// Acct Type collection
		Collection collAcctType = null;
		// CacheManager
		CacheManager cacheManagerInst = null;
		try {
			// get the CacheManager Instance
			cacheManagerInst = CacheManager.getInstance();
			// get the Acct Type collection
			collAcctType = cacheManagerInst.getMiscParamsLVL("ACCOUNTTYPE",
					entityId);
			// set the accttype collection into request.
			request.setAttribute("accttype", collAcctType);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putAccttypeListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			cacheManagerInst = null;
			collAcctType = null;
		}
	}

	/**
	 * This method is used across this action class to set the button status in
	 * request
	 *
	 * @param req
	 * @param changeStatus
	 * @param deleteStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String saveStatus,
								 String cancelStatus, String viewButtonStatus) {
		// set the add button status
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		// set the change button status
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		// set the delete button status
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		// set the save button status
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		// set the cancel button status
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		// set the view button status
		req.setAttribute(SwtConstants.VIEW_BUT_STS, viewButtonStatus);
	}

	/**
	 * @param acctMaintenanceManager
	 *            the acctMaintenanceManager to set
	 */
	public void setAcctMaintenanceManager(
			AcctMaintenanceManager acctMaintenanceManager) {
		this.acctMaintenanceManager = acctMaintenanceManager;
	}

	/**
	 * This method is called when the no method parameter is specified.
	 * @return a Result of type Action forward
	 */
	public String unspecified()
			throws SwtException {
		// call the displayListByEntity
		return displayListByEntity();
	}

	/**
	 *
	 * This method is used to display the account list based on the entity id
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayListByEntity() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable to hold the dyForm object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold selectedCurrencyCode
		String selectedCurrencyCode = null;
		// String variable to hold domesticCurrency
		String domesticCurrency = null;
		// String variable to hold hostId
		String hostId = null;
		/*
		 * Start:Code Modified For Mantis 1592 by Sudhakar on 22-12-2011:Account
		 * Maintenance screen allows to create account for entity that has no
		 * currency access
		 */
		// String variable to hold roleId
		String roleId = null;
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		// integer variable to hold accessInd
		int accessInd;
		// Variable to hold the acctmaintDetailsVO object
		AcctMaintenanceDetailVO acctmaintDetailsVO = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Entering ");

			acctMaintenance = getAcctMaintenance();



			// get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the entityId
			entityId = acctMaintenance.getId().getEntityId();
			// check entity id is null then set the current user entity id.
			if (SwtUtil.isEmptyOrNull(entityId)) {
				// get the current user entity id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				// set the entity id
				acctMaintenance.getId().setEntityId(entityId);
			}
			// get selectedCurrencyCode in request
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");
			// Set currencycode in Currcode bean
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				// get the domesticCurrency
				domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
				// Set the domestic currency in currcode bean
				acctMaintenance.setCurrcode(domesticCurrency == null ? ""
						: domesticCurrency);
			} else {
				acctMaintenance.setCurrcode(selectedCurrencyCode);

			}
			// set the Accttype
			acctMaintenance.setAccttype("All");
			// get the host id
			hostId = putHostIdListInReq(request);
			// get the accessInd
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			currencyList = (ArrayList) getCurrencyFullAccessList(request,
					hostId, entityId);
			if (accessInd == 0 && currencyList.size() == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else if (accessInd == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			// set the host id
			acctMaintenance.getId().setHostId(hostId);
			// get the account maintenance details
			acctmaintDetailsVO = acctMaintenanceManager.getCurrencyDetailList(
					entityId, hostId, roleId, acctMaintenance.getCurrcode() , null, "All");
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// set the acctmaintenancelist
			request.setAttribute("acctmaintenancelist", getCurrencyList(
					request, hostId, entityId));
			// set the acctmaintenanceDetails
			request.setAttribute("acctmaintenanceDetails", acctmaintDetailsVO
					.getAcctmaintenancelistDetails());
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance in the request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// set the EntityList in the request
			putEntityListInReq(request);
			// set the FilterSort in the request
			setFilterSortInReq(request);
			//clear column width and column order from session
			request.getSession().setAttribute("column_order", null);
			request.getSession().setAttribute("column_width", null);
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Exit ");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'displayListByEntity' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayListByEntity' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acctMaintenance = null;
			entityId = null;
			selectedCurrencyCode = null;
			domesticCurrency = null;
			hostId = null;
			acctmaintDetailsVO = null;
			roleId = null;
			currencyList = null;
		}
	}
	/**
	 * This method called from ILM CurrencyParameters Screen to display list of accounts
	 * based on entityId and currencyCode
	 * @return
	 * @throws SwtException
	 */
	public String displayListAccountMaintenance()throws SwtException{

		String entityId = null;
		String ccyCode = null;
		String hostId = null;
		String roleId = null;
		AcctMaintenanceDetailVO  accoutMaintList = null;
		AcctMaintenance acct;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {

			acct = getAcctMaintenance();



			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityId");
			ccyCode = request.getParameter("ccyCode");
			acct.getId().setEntityId(entityId);
			acct.setCurrcode(ccyCode);
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			accoutMaintList = acctMaintenanceManager.getCurrencyDetailList(
					entityId, hostId, roleId, ccyCode, null, "All");
			request.setAttribute("calledFromILMCcy", true);
			request.setAttribute("selectedEntity", entityId);
			request.setAttribute("selectedCurrency", ccyCode);
			request.setAttribute("accountEntityName", request.getParameter("entityName"));
			request.setAttribute("accountCurrencyName", request.getParameter("ccyName"));
			request.setAttribute("acctmaintenanceDetails", accoutMaintList.getAcctmaintenancelistDetails());
			request.setAttribute("acctMaintenance", acct);
			//clear column width and column order from session
			request.getSession().setAttribute("column_order", null);
			request.getSession().setAttribute("column_width", null);
		} catch (SwtException swtExp) {
			log.error("SwtException Catch in AcctMaintenanceAction.'displayListAccountMaintenance' method : "
					+ swtExp.getMessage());
			SwtUtil.logException(swtExp, request, "");
			return getView("fail");
		}catch (Exception exp) {
			log.error("Exception Catch in AcctMaintenanceAction.'displayListAccountMaintenance' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListAccountMaintenance", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
		return getView("success");

	}



	/**
	 * This method is used to display the account list based on the currency
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayListByCurrency() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold hostId
		String hostId = null;
		/*
		 * Start:Code Modified For Mantis 1592 by Sudhakar on 22-12-2011:Account
		 * Maintenance screen allows to create account for entity that has no
		 * currency access
		 */
		// String variable to hold roleId
		String roleId = null;
		// String variable to hold currencyCode
		String currencyCode = null;
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		// Variable to hold the acctmaintDetailsVO object
		AcctMaintenanceDetailVO accountMaintenanceDetailsVO = null;
		// Integer varable to hold Access Index
		int accessInd;
		try {
			log.debug(this.getClass().getName()
					+ "- [displayListByCurrency] - Entering ");

			// get the entityId
			entityId = acctMaintenance.getId().getEntityId();
			// get the hostId
			hostId = putHostIdListInReq(request);
			// get the currencyCode
			currencyCode = acctMaintenance.getCurrcode();
			// get the roleId from commondata manager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Getting fullaccesscurrencylist
			currencyList = (ArrayList) getCurrencyFullAccessList(request,
					hostId, entityId);
			// get the account maintenance details.
			accountMaintenanceDetailsVO = acctMaintenanceManager
					.getCurrencyDetailList(entityId, hostId, roleId,
							currencyCode, null, "All");
			// set the acctmaintenancelist in request
			request.setAttribute("acctmaintenancelist", getCurrencyList(
					request, hostId, entityId));
			// set the acctmaintenanceDetails in request
			request
					.setAttribute("acctmaintenanceDetails",
							accountMaintenanceDetailsVO
									.getAcctmaintenancelistDetails());
			// set the EntityList in request
			putEntityListInReq(request);
			// set the button status for add,change,view,delete,save,cancel
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			// get the access index
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			if (accessInd == 0 && currencyList.size() == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else if (accessInd == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess in request
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess in request
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			log.debug(this.getClass().getName()
					+ "- [displayListByCurrency] - Exit ");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'displayListByCurrency' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayListByCurrency' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify object
			acctMaintenance = null;
			entityId = null;
			hostId = null;
			currencyCode = null;
			accountMaintenanceDetailsVO = null;
			roleId = null;
			currencyList = null;
		}
	}

	/**
	 * This method is called on clicking the "ADD" button on the Account
	 * maintenance screen and loads add screen details.
	 *
	 * @return
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		// String Variable to hold the entityId
		String entityId = null;
		/*
		 * Variable added For Mantis 1592 by Sudhakar on 22-12-2011:Account
		 * Maintenance screen allows to create account for entity that has no
		 * currency access
		 */
		// String Variable to hold the hostId
		String hostId = null;
		// String Variable to hold the selectedCurrencyCode
		String selectedCurrencyCode = null;
		// String Variable to hold the currenyCode
		String currenyCode = null;
		// Variable to hold the labelValueBean object
		LabelValueBean labelValueBeanCurrencyList = null;
		try {
			log.debug(this.getClass().getName() + "- [add] - Entering ");

			acctMaintenance = getAcctMaintenance();



			// get the entityId
			entityId = request.getParameter("entityCode");
			// set the entityName in session
			request.getSession().setAttribute("entityName",
					request.getParameter("entityName"));
			// set the entityId
			acctMaintenance.getId().setEntityId(entityId);
			// set the entityId in session
			request.getSession().setAttribute("entityId", entityId);
			// get the selectedCurrencyCode
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");
			// set the Accttype
			acctMaintenance.setAccttype("All");
			/*
			 * Start:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// get the hostId
			hostId = putHostIdListInReq(request);
			// getting the fullaccescurrency list
			currencyList = (ArrayList) getCurrencyFullAccessList(request,
					hostId, entityId);
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// check currencyList is not null
			if (currencyList != null) {
				// remove the default one
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
			// set the acctmaintenancelistadd in request
			request.setAttribute("acctmaintenancelistadd", currencyList);
			// set the acctmaintenanceDetailsadd in request
			if ((currencyList.size() > 0)
					&& selectedCurrencyCode.equals(SwtConstants.ALL_VALUE)) {
				/*
				 * The default selected currency will be the first one appearing
				 * in the currency Drop down, if the "All" is selected in Main
				 * Acccount Maintenance Screen.
				 */
				labelValueBeanCurrencyList = (LabelValueBean) currencyList
						.get(0);
				currenyCode = labelValueBeanCurrencyList.getValue();
			} else {
				currenyCode = selectedCurrencyCode;
			}
			// set the currenyCode
			acctMaintenance.setCurrcode(currenyCode);
			// set the Autoswpswitch
			acctMaintenance.setAutoswpswitch("N");
			// set the Acctlevel
			acctMaintenance.setAcctlevel("M");
			// set the Acctgrpflg
			acctMaintenance.setAcctgrpflg("E");
			// set the Accttype
			acctMaintenance.setAccttype("C");
			// set the ForecastSOD
			acctMaintenance.setForecastSOD("N");
			// set the ExternalSOD
			acctMaintenance.setExternalSOD("N");
			// set the FutureBalances
			acctMaintenance.setFutureBalances("T");
			// set the Monitor
			acctMaintenance.setMonitor("Y");
			// set the AggAccount
			acctMaintenance.setAggAccount("N");
			// set the ArchiveData
			acctMaintenance.setArchiveData("Y");
			// set the AcctMonitorSum
			acctMaintenance.setAcctMonitorSum("Y");
			// set the AcctClass
			acctMaintenance.setAcctClass("O");
			// set the AutoOpenUnexpected
			acctMaintenance.setAutoOpenUnexpected(SwtConstants.NO);
			// set the AutoOpenUnsettled
			acctMaintenance.setAutoOpenUnsettled(SwtConstants.NO);
			// set the AllPreAdviceEntity
			acctMaintenance.setAllPreAdviceEntity(SwtConstants.NO);
			// set the AcctPriorityOrder
			acctMaintenance.setAcctPriorityOrder(new Integer(1));
			// set the Swpdays
			acctMaintenance.setSwpdays(0);
			// set the Acctstatusflg
			acctMaintenance.setAcctstatusflg("O");
			// set the TargetSignList in request
			putTargetSignAndSettMethodListsInReq(request, entityId);
			// set the AcctStatusList in request
			putAcctStatusListInReq(request, entityId);
			// set the attribute for
			// accInterestRatecollInSession,contactName,contactEmailAddr
			// contactPhoneNumber,newCrInternal,newCrExternal,newDrInternal,newDrExternal
			// CrExternalInt,DdExternalInt
			// in request
			request.getSession().setAttribute("accInterestRatecollInSession"+"*.*",
					new ArrayList<AccountInterestRate>());
			request.setAttribute("contactName", SwtConstants.EMPTY_STRING);
			request.setAttribute("contactEmailAddr", SwtConstants.EMPTY_STRING);
			request.setAttribute("contactPhoneNumber",
					SwtConstants.EMPTY_STRING);
			request.setAttribute("newCrInternal", SwtConstants.EMPTY_STRING);
			request.setAttribute("newCrExternal", SwtConstants.EMPTY_STRING);
			request.setAttribute("newDrInternal", SwtConstants.EMPTY_STRING);
			request.setAttribute("newDrExternal", SwtConstants.EMPTY_STRING);
			request.setAttribute("CrExternalInt", SwtConstants.EMPTY_STRING);
			request.setAttribute("DdExternalInt", SwtConstants.EMPTY_STRING);
			// set the acctMaintenance in form
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance in request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// set the button status for add,change,view,delete,save,cancel
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);


			ArrayList<AccSweepSchedule> collAcctSweepSchedule = new ArrayList<AccSweepSchedule>();

			int usedInOtherSweepCount = 0;


			request.setAttribute("usedInOtherSweepCount", usedInOtherSweepCount);
			request.getSession().setAttribute("acctSweepSchedulecoll",
					collAcctSweepSchedule);
			request.setAttribute("acctScheduleNextVal", 1);
			request.getSession().setAttribute("acctSweepSchedulecollInSession"+"*.*",
					collAcctSweepSchedule);

			int acctSweepBalCount = 0;
			// put empty account sweep balance  group list
			ArrayList<AccountSweepBalanceGroup> collAcctSweepBalGrp = new ArrayList<AccountSweepBalanceGroup>();
			request.getSession().setAttribute("acctSweepBalGrpCollInSession"+"*.*", collAcctSweepBalGrp);

			// set the methodName in request
			request.setAttribute("acctSweepBalCount", acctSweepBalCount);

			/*
			 * Code added For Mantis 1562 by Sudhakar on 12-12-2011:Account
			 * Maintenance Main screen: currency selection changes after adding
			 * a new account
			 */
			// set selectedCurrencyCode in request
			request.setAttribute("selectedCurrencyCode", selectedCurrencyCode);
			// set the statusflagmain in request
			request.setAttribute("statusflagmain", "M");
			// set the methodName in request
			request.setAttribute("methodName", "add");
			// set the accountLevel in request
			request.setAttribute("accountLevel", SwtConstants.ACCT_MAIN);
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			log.debug(this.getClass().getName() + "- [add] - Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'add' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'add' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			currencyList = null;
			acctMaintenance = null;
			entityId = null;
			selectedCurrencyCode = null;
			currenyCode = null;
			hostId = null;
		}
	}

	/**
	 * This method is used to save the account details in database
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable to hold an action errors
		ActionErrors errors = null;
		// Variable to hold the accountMaintenance object
		AcctMaintenance accountMaintenance = null;
		// Variable to hold the systemInfo object
		SystemInfo systemInfo = null;
		// Variable to hold the accountInterestRate object
		AccountInterestRate accountInterestRate = null;
		// Variable to hold the newAcctIntRateList object
		ArrayList<AccountInterestRate> newAcctIntRateList = null;
		// Variable to hold the accountInterestRateList object
		ArrayList<AccountInterestRate> accountInterestRateList = null;
		// Variable to hold the itrAccountInterestRate object
		Iterator<AccountInterestRate> itrAccountInterestRate = null;
		// String Variable to hold the entityId
		String entityId = null;
		// String Variable to hold the hostId
		String hostId = null;
		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		String csrf = null;
		//
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;

		try {
			log.debug(this.getClass().getName() + "- [save] - Entering ");
			// Instantiate the ActionErrors
			errors = new ActionErrors();


			accountMaintenance = getAcctMaintenance();


			//get Csrf to save account specific format
			csrf = request.getParameter("csrf");
			// get the entityId
			entityId = accountMaintenance.getId().getEntityId();
			// get the hostId
			hostId = putHostIdListInReq(request);
			// set the hostId
			accountMaintenance.getId().setHostId(hostId);
			if (accountMaintenance.getMansweepflg() != null) {
				if (accountMaintenance.getMansweepflg().equals("on")
						|| accountMaintenance.getMansweepflg().equals("Y")) {
					// set the Mansweepflg
					accountMaintenance.setMansweepflg("Y");
				}
			} else {
				// set the Mansweepflg
				accountMaintenance.setMansweepflg("N");
			}
			if (SwtUtil.isEmptyOrNull(accountMaintenance
					.getAllPreAdviceEntity())) {
				// set the AllPreAdviceEntity
				accountMaintenance.setAllPreAdviceEntity("N");
			}
			if (SwtUtil.isEmptyOrNull(accountMaintenance.getCutoff())) {
				// set the Cutoff
				accountMaintenance.setCutoff("00:00");
			}
			// Code added by Mefteh for Mantis 2110: to set the IBAN value in a electronic Format
			if (!SwtUtil.isEmptyOrNull(accountMaintenance.getAcctIBAN())) {
				String iban=accountMaintenance.getAcctIBAN().replace(" ", "");
				accountMaintenance.setAcctIBAN(iban);
			}
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			/*
			 * get the AccountInterestRate Collection for this Account from the
			 * session
			 */
			newAcctIntRateList = new ArrayList<AccountInterestRate>();
			accountInterestRateList = (ArrayList<AccountInterestRate>) request
					.getSession().getAttribute("accInterestRatecollInSession"+"*.*");
			// Iterate the accountInterestRateList
			itrAccountInterestRate = accountInterestRateList.iterator();
			// Instantiate the accountInterestRate
			accountInterestRate = new AccountInterestRate();
			while (itrAccountInterestRate.hasNext()) {
				// get the accountInterestRate
				accountInterestRate = itrAccountInterestRate.next();
				// set the hostId
				accountInterestRate.getId().setHostId(hostId);
				// set the entityId
				accountInterestRate.getId().setEntityId(entityId);
				// set the AccountId
				accountInterestRate.getId().setAccountId(
						accountMaintenance.getId().getAccountId());
				// set the InterestDateRate
				accountInterestRate.getId().setInterestDateRate(
						accountInterestRate.getId().getInterestDateRate());
				// add the accountInterestRate
				newAcctIntRateList.add(accountInterestRate);
			}
			/*
			 * This call will set the Contact details in the acct -
			 * AcctMaintenance Object
			 */
			setAcctContactDetails(request, accountMaintenance);
			/*
			 * This call will set the Format details in the acct -
			 * AcctMaintenance Object
			 */
			if (accountMaintenance.getPrimaryForecast() == null) {
				accountMaintenance.setPrimaryForecast("N");
			}
			if (accountMaintenance.getPrimaryExternal() == null) {
				accountMaintenance.setPrimaryExternal("N");
			}
			if (accountMaintenance.getSecondaryForecast() == null) {
				accountMaintenance.setSecondaryForecast("N");
			}
			if (accountMaintenance.getSecondaryExternal() == null) {
				accountMaintenance.setSecondaryExternal("N");
			}
			if (accountMaintenance.getForecastSOD() == null) {
				accountMaintenance.setForecastSOD("N");
			}
			if (accountMaintenance.getExternalSOD() == null) {
				accountMaintenance.setExternalSOD("N");
			}
			if (accountMaintenance.getFutureBalances() == null) {
				accountMaintenance.setFutureBalances("T");
			}
			if (SwtUtil.isEmptyOrNull(accountMaintenance.getSubAcctim())) {
				accountMaintenance.setSubAcctim("N");
			}

			if (SwtUtil.isEmptyOrNull(accountMaintenance.getSvcEntityInclBalFlag())) {
				accountMaintenance.setSvcEntityInclBalFlag("N");
			}
			/*
			 * Code Added For Mantis 1562 by Sudhakar on 12-12-2011:Account
			 * Maintenance Main screen: currency selection changes after adding
			 * a new account
			 */
			// set selectedCurrencyCode in request
			request.setAttribute("selectedCurrencyCode", request
					.getParameter("selectedCurrencyCode"));
			// save the account details
			acctMaintenanceManager.saveAcctMaintDetail(accountMaintenance,
					systemInfo, SwtUtil.getCurrentSystemFormats(request
							.getSession()), newAcctIntRateList);


			allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
					.getAttribute("acctSpecificFormatcollInSession"+"*.*");
			if(allStoredSpecificInrequest == null)
				allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();


			if(allStoredSpecificInrequest.containsKey(csrf))  {
				specificFormatList =  allStoredSpecificInrequest.get(csrf);
			}
			if(specificFormatList != null && specificFormatList.size() > 0) {
				AcctSpecificSweepFormatManager acctSpecificSweepFormatManager = (AcctSpecificSweepFormatManager) SwtUtil.getBean("acctSpecificSweepFormatManager");
				AccountSpecificSweepFormat accountSpecificSweepFormatTmp = null;
				for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) {
					accountSpecificSweepFormatTmp = iterator.next();
					accountSpecificSweepFormatTmp.getId().setAccountId(accountMaintenance.getId().getAccountId());;
					if("I".equals(accountSpecificSweepFormatTmp.getChanged())) {
						acctSpecificSweepFormatManager.saveAccountSpecificSweepFormat(accountSpecificSweepFormatTmp);
					}
				}
			}

			ArrayList<AccSweepSchedule> acctScheduleList = (ArrayList<AccSweepSchedule>) request.getSession()
					.getAttribute("acctSweepSchedulecollInSession"+"*.*");

			if(acctScheduleList != null && acctScheduleList.size()>0) {
				for (int i = 0; i < acctScheduleList.size(); i++) {
					acctScheduleList.get(i).setAccountId(accountMaintenance.getId().getAccountId());
					acctMaintenanceManager.saveOrUpdateAcctScheduleSweep(acctScheduleList.get(i), "save");
				}
			}


			// in case of add save account sweep balance group

			ArrayList<AccountSweepBalanceGroup> acctSweepBalGrpList = (ArrayList<AccountSweepBalanceGroup>) request.getSession()
					.getAttribute("acctSweepBalGrpCollInSession"+"*.*");
			if(acctSweepBalGrpList != null && acctSweepBalGrpList.size()>0) {
				for (int i = 0; i < acctSweepBalGrpList.size(); i++) {
					acctSweepBalGrpList.get(i).getId().setAccountId(accountMaintenance.getId().getAccountId());
					acctMaintenanceManager.saveAcctSweepBalGrp(acctSweepBalGrpList.get(i));
				}
			}



			// set the AccttypeList in request
			putAccttypeListInReq(request, entityId);
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the currency list
			currencyList = (ArrayList<LabelValueBean>) SwtUtil
					.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(
							roleId, entityId);
			// check currencyList is not null
			if (currencyList != null) {
				// remove the default one
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
			// set the acctmaintenancelistadd in request
			request.setAttribute("acctmaintenancelistadd", currencyList);
			// set the FormatList in request
			putFormatDetailsInRequest(request);
			// set the AcctStatusList in request
			putAcctStatusListInReq(request, entityId);
			// set the TargetSignList in request
			putTargetSignAndSettMethodListsInReq(request, entityId);
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			setAcctMaintenance(accountMaintenance);
			// set the acctMaintenance in request
			request.setAttribute("acctMaintenance", accountMaintenance);
			// set the account display details in request
			putAcctDisplayDtInReq(request);
			// set the methodName in request
			request.setAttribute("methodName", "add");
			// set the parentFormRefresh in request
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + "- [save] - Exititng ");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + "- [save] - Exception "
					+ swtexp.getMessage());
			// set the button status for add,change,delete,save,cancel
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			// set entity name in the request
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			// set the
			// AccttypeList,FormatList,AcctStatusList,TargetSignList
			// in request
			putAccttypeListInReq(request, entityId);
			putFormatDetailsInRequest(request);
			putAcctStatusListInReq(request, entityId);
			putTargetSignAndSettMethodListsInReq(request, entityId);
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			/*
			 * Code Modified For Mantis 1592 by Sudhakar on 22-12-2011:Account
			 * Maintenance screen allows to create account for entity that has
			 * no currency access
			 */
			// get the currency list
			currencyList = (ArrayList<LabelValueBean>) getCurrencyFullAccessList(
					request, hostId, entityId);
			// check currencyList is not null
			if (currencyList != null) {
				// remove the default one
				currencyList.remove(new LabelValueBean("Default", "*"));
			}
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			// set the acctmaintenancelistadd in request
			request.setAttribute("acctmaintenancelistadd", currencyList);
			// get the account manitenance details
			request.setAttribute("methodName", "add");
			// set the showLoroToPredicted in request
			if (accountMaintenance.getAcctClass().equalsIgnoreCase("L")) {
				request.setAttribute("showLoroToPredicted", "Y");
			} else {
				request.setAttribute("showLoroToPredicted", "N");
			}
			if(this.acctMaintenance != null) {
				acctMaintenance = this.acctMaintenance;
			}
			// set the acctMaintenance in request
			request.setAttribute("acctMaintenance", accountMaintenance);
			// set the account display details in request
			putAcctDisplayDtInReq(request);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [save] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			errors = null;
			accountMaintenance = null;
			systemInfo = null;
			accountInterestRate = null;
			newAcctIntRateList = null;
			accountInterestRateList = null;
			itrAccountInterestRate = null;
			entityId = null;
			hostId = null;
		}
	}

	/**
	 * This method is called on clicking the "Change" button on the Account
	 * maintenance screen and loads change screen details.
	 * @return
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable Declaration for account maintenance
		AcctMaintenance acctMaintenance = null;
		// Variable Declaration for entityId
		String entityId = null;
		// Variable Declaration for hostId
		String hostId = null;
		// Variable Declaration for systemInfo
		SystemInfo systemInfo = null;
		// Variable Declaration for accIntRate
		AccountInterestRate acctInterestRate = null;
		// Variable Declaration for collaccInterestRate
		ArrayList<AccountInterestRate> collAcctInterestRate = null;
		// Variable Declaration for oldVal
		String oldVal = null;
		// Variable Declaration for acctSubColumn
		Collection<AcctMaintenance> acctSubColumn = null;
		// Variable Declaration for sweepInter
		Collection<SweepIntermediaries> sweepIntermediariesCol = null;
		// Variable Declaration for sweep Intermediaries
		SweepIntermediaries sweepIntermediaries = null;
		// Variable Declaration for iterator Sweep Intermediaries
		Iterator<SweepIntermediaries> itrSweepIntermediaries = null;
		// Variable Declaration for bookCodeManager
		BookCodeManager bookCodeManager = null;
		String partyId = null;
		String selectedAccountId = null;
		try {
			log.debug(this.getClass().getName() + " - [change] - Enter");
			acctMaintenance = getAcctMaintenance();



			// get the entityId
			entityId = request.getParameter("entityCode");
			selectedAccountId = request.getParameter("selectedAccountId");
			// get the hostId
			hostId = putHostIdListInReq(request);
			// set the host id
			acctMaintenance.getId().setHostId(hostId);
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			// set the entityName in request
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			// set the TargetSignList,AcctStatusList in request
			putTargetSignAndSettMethodListsInReq(request, entityId);
			putAcctStatusListInReq(request, entityId);
			// get the account maintenance details
			acctMaintenance = acctMaintenanceManager.getEditableDataDetailList(
					entityId, hostId, selectedAccountId
							.toString(), systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// set the showLoroToPredicted in request
			if (acctMaintenance.getAcctClass().equalsIgnoreCase("L")) {
				request.setAttribute("showLoroToPredicted", "Y");
			} else {
				request.setAttribute("showLoroToPredicted", "N");
			}
			// set the contactName,contactEmailAddr,contactPhoneNumber in
			// request
			request.setAttribute("contactName", acctMaintenance
					.getAcctContactName());
			request.setAttribute("contactEmailAddr", ((acctMaintenance
					.getAcctEmailAddr() == null) ? "" : acctMaintenance
					.getAcctEmailAddr()));
			request.setAttribute("contactPhoneNumber", ((acctMaintenance
					.getAcctPhone() == null) ? "" : acctMaintenance
					.getAcctPhone().toString()));
			// set the
			// newCrInternal,newCrExternal,newDrInternal,newDrExternal,CrExternalInt,DdExternalInt
			// in request
			request.setAttribute("newCrInternal", ((acctMaintenance
					.getAcctNewCrInternal() == null) ? "" : acctMaintenance
					.getAcctNewCrInternal()));
			request.setAttribute("newCrExternal", ((acctMaintenance
					.getAcctNewCrExternal() == null) ? "" : acctMaintenance
					.getAcctNewCrExternal()));
			request.setAttribute("newDrInternal", ((acctMaintenance
					.getAcctNewDrInternal() == null) ? "" : acctMaintenance
					.getAcctNewDrInternal()));
			request.setAttribute("newDrExternal", ((acctMaintenance
					.getAcctNewDrExternal() == null) ? "" : acctMaintenance
					.getAcctNewDrExternal()));
			request.setAttribute("CrExternalInt", ((acctMaintenance
					.getCreditExternalInter() == null) ? "" : acctMaintenance
					.getCreditExternalInter()));
			request.setAttribute("DdExternalInt", ((acctMaintenance
					.getDebitExternalInter() == null) ? "" : acctMaintenance
					.getDebitExternalInter()));
			// This will get the Collection of AccountInterestRate and set them
			// on Session
			collAcctInterestRate = (ArrayList<AccountInterestRate>) acctMaintenanceManager
					.getAcctInterestRatecoll(hostId, entityId, acctMaintenance
							.getId().getAccountId(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			request.getSession().setAttribute("accInterestRatecollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctInterestRate);
			// This block sets the Latest Interest Rates defined for this
			// account in AccountInterestRate
			acctInterestRate = new AccountInterestRate();
			if (collAcctInterestRate.size() > 0) {
				acctInterestRate = collAcctInterestRate.get(0);

				acctMaintenance.setLatestCreditRate(acctInterestRate
						.getCreditRate().toString());
				acctMaintenance.setLatestOverDraftRate(acctInterestRate
						.getOverdraftRate().toString());

				acctMaintenance.setLatestInterestDateRate(acctInterestRate
						.getInterestDateRateAsString());
			}
			// set the oldVal
			oldVal = new StringBuffer("Account-Name=").append(
							acctMaintenance.getAcctname()).append("^Currency=").append(
							acctMaintenance.getCurrcode()).append("^Account-Type=")
					.append(acctMaintenance.getAccttype()).append(
							"^Account-Group=").append(
							acctMaintenance.getAcctgrpflg()).append(
							"^Account-Level=").append(
							acctMaintenance.getAcctlevel()).append(
							"^Main Account-Id=").append(
							acctMaintenance.getMinacctcode()).append(
							"^Account-gl-code=").append(
							acctMaintenance.getGlcode()).append(
							"^Account-Bic-Code=").append(
							acctMaintenance.getAcctbiccode()).append(
							"^Account-Corresponding Code=").append(
							acctMaintenance.getCorresacccode()).append(
							"^Account-Extra ID=").append(
							acctMaintenance.getAcctextraid()).append(
							"^Account-Status=").append(
							acctMaintenance.getAcctstatusflg()).append(
							"^Country=").append(
							acctMaintenance.getHolidaycalendar()).append(
							"^Primary-Forecast=").append(
							acctMaintenance.getPrimaryForecast()).append(
							"^Primarty-External=").append(
							acctMaintenance.getPrimaryExternal()).append(
							"^Secondary-Forecast=").append(
							acctMaintenance.getSecondaryExternal()).append(
							"^Secondary-External=").append(
							acctMaintenance.getSecondaryExternal()).append(
							"^Forecast-SOD=").append(
							acctMaintenance.getForecastSOD()).append(
							"^External-SOD=").append(
							acctMaintenance.getExternalSOD()).append(
							"^Future-Balances=").append(
							acctMaintenance.getFutureBalances()).append(
							"^AutoSweep-Switch=").append(
							acctMaintenance.getAutoswpswitch()).append(
							"^Manual-Sweep-Switch=").append(
							acctMaintenance.getMansweepflg()).append(
							"^Sweep-Time=")

					.append(acctMaintenance.getEodSweeptime()).append(
							"^Sweep Days=")
					.append(acctMaintenance.getSwpdays()).append(
							"^Min-Sweep Amount=").append(
							acctMaintenance.getMinseepamtasString()).append(
							"^Max-Sweep-Amount=").append(
							acctMaintenance.getMaxsweepamteasString()).append(
							"^Book-Code=").append(
							acctMaintenance.getSweepbookcode()).append(
							"^New-Credit-Msg-Format=").append(
							"^Debit-Msg-Format=").append(
							acctMaintenance.getNewdrfrId()).append(
							"^Credit-Interest-Rate=").append(
							acctMaintenance.getCurrcreditrateasString())
					.append("^Credit-OverDraft-Rate=").append(
							acctMaintenance.getCurroverdraftrateasString())
					.append("^Target-Balance=").append(
							acctMaintenance.getTargetbalanceasString())
					.toString().replace("\"", "&quot;");
			acctMaintenance
					.setStatusflag(acctMaintenance.getMinacctcode() != null ? "Y"
							: "N");
			// set the status flag main in request
			if (acctMaintenance.getAcctlevel() != null) {
				if (acctMaintenance.getAcctlevel().equalsIgnoreCase("M")) {
					request.setAttribute("statusflagmain", acctMaintenance
							.getAcctlevel());
				} else {
					request.setAttribute("statusflagmain", acctMaintenance
							.getAcctlevel());
				}
			} else {
				request.setAttribute("statusflagmain", "M");
			}
			// set the PrimaryForecast
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getPrimaryForecast()))
				acctMaintenance.setPrimaryForecast("N");
			// set the PrimaryExternal
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getPrimaryExternal()))
				acctMaintenance.setPrimaryExternal("N");
			// set the SecondaryForecast
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getSecondaryForecast()))
				acctMaintenance.setSecondaryForecast("N");
			// set the SecondaryExternal
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getSecondaryExternal()))
				acctMaintenance.setSecondaryExternal("N");
			// set the ForecastSOD
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getForecastSOD()))
				acctMaintenance.setForecastSOD("N");
			// set the ExternalSOD
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getExternalSOD()))
				acctMaintenance.setExternalSOD("N");
			// set the FutureBalances
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getFutureBalances()))
				acctMaintenance.setFutureBalances("T");
			setAcctMaintenance(acctMaintenance);
			// set the subAccountsPresent in request
			acctSubColumn = acctMaintenanceManager.getSubColumnDataDetailList(
					entityId, hostId, selectedAccountId
							.toString(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			if ((acctSubColumn != null) && (acctSubColumn.size() > 0)) {
				request.setAttribute("subAccountsPresent", "yes");
			}

			ArrayList<AccSweepSchedule> collAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
					.getAcctSweepScheduleList(hostId, entityId, acctMaintenance
							.getId().getAccountId(), request.getSession(), acctMaintenance.getCurrcode(), true);

			String json = new Gson().toJson(collAcctSweepSchedule );

			request.setAttribute("acctScheduleNextVal", collAcctSweepSchedule.size()+1);

//			accInterestRatecoll = (Collection) request.getSession()
//					.getAttribute("accInterestRatecollInSession");
			request.getSession().setAttribute("acctSweepSchedulecollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctSweepSchedule);

			long usedInOtherSweepCount = acctMaintenanceManager
					.getAcctSweepScheduleUsedinCount(hostId, entityId, acctMaintenance
							.getId().getAccountId());



			request.getSession().setAttribute("acctSweepSchedulecoll",
					new ArrayList<AccSweepSchedule>());


			// set the
			// acctMaintenance,methodName,accountLevel,oldValue,selectedCurrencyCode,accountLevel
			// in request
			request.setAttribute("acctMaintenance", acctMaintenance);
			request.setAttribute("usedInOtherSweepCount", usedInOtherSweepCount);

			request.setAttribute("acctSweepSchedulecollJson", json);

			//put account sweep balance groups list from DB and put it in session


			ArrayList<AccountSweepBalanceGroup> collAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) acctMaintenanceManager
					.getAcctSweepBalGrpcoll(hostId, entityId, acctMaintenance.getId().getAccountId());


			int acctSweepBalCount= collAcctSweepBalGrp.size();

			String acctSweepListJson = new Gson().toJson(collAcctSweepBalGrp );

			request.setAttribute("acctSweepBalGrpCollInSessionJson"+acctMaintenance.getId().getAccountId(), acctSweepListJson);

			request.getSession().setAttribute("acctSweepBalGrpCollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctSweepBalGrp);
			request.setAttribute("acctSweepBalCount", acctSweepBalCount);

			request.setAttribute("methodName", "change");
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			request
					.setAttribute("accountLevel", acctMaintenance
							.getAcctlevel());
			request.setAttribute("oldValue", oldVal);
			request.setAttribute("selectedCurrencyCode", request
					.getParameter("selectedCurrencyCode"));
			request
					.setAttribute("accountLevel", acctMaintenance
							.getAcctlevel());
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			// get the intermediaryRecord
			sweepIntermediariesCol = acctMaintenanceManager
					.getIntermediaryRecord(hostId, entityId, acctMaintenance
							.getCurrcode(), acctMaintenance.getAcctbiccode());
			// Instantiate the SweepIntermediaries
			sweepIntermediaries = new SweepIntermediaries();
			// Iterate the sweepInter
			itrSweepIntermediaries = sweepIntermediariesCol.iterator();
			while (itrSweepIntermediaries.hasNext()) {
				// get the SweepIntermediaries
				SweepIntermediaries swpInter = itrSweepIntermediaries.next();
				// set the Intermediary in SweepIntermediaries
				sweepIntermediaries.setIntermediary(swpInter.getIntermediary());
			}
			setSweepintermediaries(sweepIntermediaries);
			// get the country name for country code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getHolidaycalendar())) {
				request.setAttribute("countryName", acctMaintenanceManager
						.getCountryDetail(acctMaintenance.getHolidaycalendar())
						.getCountryName());
			}
			// get the currency name for currency code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getCurrcode())) {
				request.setAttribute("currencyName", acctMaintenanceManager
						.getCurrencyDetail(entityId, hostId,
								acctMaintenance.getCurrcode())
						.getCurrencyMaster().getCurrencyName());
			}
			// get the main account name for main account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getMinacctcode())) {
				request
						.setAttribute("mainAcctName", acctMaintenanceManager
								.getMainOrLinkAccount(entityId, hostId,
										acctMaintenance.getMinacctcode())
								.getAcctname());
			}
			// get the link account name for link account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getLinkAccID())) {
				request.setAttribute("linkAcctName", (acctMaintenanceManager
						.getMainOrLinkAccount(entityId, hostId,
								acctMaintenance.getLinkAccID()))!=null?acctMaintenanceManager
						.getMainOrLinkAccount(entityId, hostId,
								acctMaintenance.getLinkAccID()).getAcctname():"");
			}
			// get the book code name for book code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getSweepbookcode())) {
				bookCodeManager = (BookCodeManager) SwtUtil
						.getBean("bookCodeManager");
				request.setAttribute("bookCodeName", bookCodeManager
						.getEditableData(hostId, entityId,
								acctMaintenance.getSweepbookcode())
						.getBookName());
			}
			// Get the party name if the party ID is well defined int the selected account
			partyId = acctMaintenance.getAccountPartyId();
			if (!SwtUtil.isEmptyOrNull(partyId)) {
				request.setAttribute("partyDesc", getPartyName(hostId, entityId, partyId));
			} else {
				request.setAttribute("partyDesc", "");
			}

			log.debug(this.getClass().getName() + " - [change] - Exit");
			return getView("change");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'change' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'change' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acctMaintenance = null;
			entityId = null;
			hostId = null;
			systemInfo = null;
			acctInterestRate = null;
			bookCodeManager = null;
			collAcctInterestRate = null;
			oldVal = null;
			acctSubColumn = null;
			sweepIntermediariesCol = null;
			sweepIntermediaries = null;
			itrSweepIntermediaries = null;
		}
	}

	public String getAccsSweepSchedule()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration and initialization */
		String hostId = null;
		String entityId = null;
		String accountId = null;
		String currencyCode = null;
		try {
			log.debug(this.getClass().getName() + "- [getAccsSweepSchedule] - Entering ");
			/*
			 * Struts Framework built-in Class used to set and get the Form(JSP)
			 * values
			 */

			/* Getting the hostId from swtcommon.properties file */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * Getting the entityId, entityName, selectedReasonCode and
			 * selectedDescription from request
			 */
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyCode = request.getParameter("currencyCode");
			/*
			 * Sets the hostId,entityId,selectedReasonCode and
			 * selectedDescription in reasonMaintenance bean object
			 */
			/* Sets the entityId ,entityName and methodName in request object */
			request.setAttribute("entityId", entityId);
			request.setAttribute("accountId", accountId);
			request.setAttribute("methodName", "getAccsSweepSchedule");


			ArrayList<AccSweepSchedule> collAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
					.getAcctSweepScheduleList(hostId, entityId, accountId, request.getSession(), currencyCode, false);

			request.getSession().setAttribute("acctSweepSchedulecoll",
					collAcctSweepSchedule);
			request.setAttribute("acctSweepSchedulecoll",
					collAcctSweepSchedule);

			log.debug(this.getClass().getName() + "- [getAccsSweepSchedule] - Exiting ");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + "- [getAccsSweepSchedule] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getAccsSweepSchedule", ReasonMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			hostId = null;
			entityId = null;
			currencyCode = null;
			accountId = null;
		}
		return getView("acctScheduleSweeps");
	}

	/**
	 * This method is used to update the account details in database.
	 * @return Action Forward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variables Declaration for errors
		ActionErrors errors = null;
		// Variables Declaration for acct
		AcctMaintenance acct = null;
		// Variables Declaration for hostId
		String hostId = null;
		// Variables Declaration for entityId
		String entityId = null;
		// Variables Declaration for accountId
		String accountId = null;
		// Variables Declaration for accountLevel
		String accountLevel = null;
		// Variables Declaration for systemInfo
		SystemInfo systemInfo = null;
		// Variables Declaration for oldValue
		String oldValue = null;
		// Variables Declaration for newValue
		String newValue = null;
		// Variables Declaration for userId
		String userId = null;
		// Variables Declaration for originalaccIntRateColl
		Collection originalaccIntRateColl = null;
		// Variables Declaration for original
		Hashtable original = null;
		// Variables Declaration for itroriginal
		Iterator itroriginal = null;
		// Variables Declaration for accIntRate
		AccountInterestRate accIntRate = null;
		// Variables Declaration for accIntRateCollChanged
		ArrayList accIntRateCollChanged = null;
		// Variables Declaration for accountInterestRateList
		ArrayList accountInterestRateList = null;
		// Variables Declaration for newAcctIntRateList
		ArrayList newAcctIntRateList = null;
		// Variables Declaration for itr
		Iterator itrUpdate = null;
		// Variables Declaration for accountInterestRate
		AccountInterestRate accountInterestRate = null;
		// Variables Declaration for changed
		Hashtable changed = null;
		// Variables Declaration for itrchanged
		Iterator itrchanged = null;
		// Variables Declaration for collAcctIntRateDeleted
		ArrayList collAcctIntRateDeleted = null;
		// Variables Declaration for collAcctIntRateAdded
		ArrayList collAcctIntRateAdded = null;
		// Variables Declaration for collAcctIntRateUpdated
		ArrayList collAcctIntRateUpdated = null;
		// Variables Declaration for selectedCurrencyCode
		String selectedCurrencyCode = null;
		// Variables Declaration for entityName
		String entityName = null;
		// Variables Declaration for flag
		boolean flag = false;
		// Variables Declaration for originalInterestRateAsString
		String originalInterestRateAsString = null;
		// Variables Declaration for changedInterestDateRateAsString
		String changedInterestDateRateAsString = null;
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		String csrf = null;
		try {
			log.debug(this.getClass().getName() + " - [update] - Enter");
			// Instantiate the ActionErrors
			errors = new ActionErrors();
			// get the AcctMaintenance
			acct = getAcctMaintenance();



			// get the hostId,entityId,accountId from bean
			hostId = putHostIdListInReq(request);
			entityId = acct.getId().getEntityId();
			accountId = acct.getId().getAccountId();
			// set the Cutoff,AllPreAdviceEntity,HostId,accountLevel in the
			// account bean
			if (SwtUtil.isEmptyOrNull(acct.getCutoff()))
				acct.setCutoff("00:00");
			if (SwtUtil.isEmptyOrNull(acct.getAllPreAdviceEntity()))
				acct.setAllPreAdviceEntity("N");
			acct.getId().setHostId(hostId);
			// accountLevel = request.getParameter("accountLevel");
			csrf = request.getParameter("csrf");
			// acct.setAcctlevel(accountLevel);
			// This if condition is to set the Manual Sweep flag value in bean
			if (acct.getMansweepflg() != null) {
				if (acct.getMansweepflg().equals("on")
						|| acct.getMansweepflg().equals("Y")) {
					// set the Mansweepflg
					acct.setMansweepflg("Y");
				}
			} else {
				// set the Mansweepflg
				acct.setMansweepflg("N");
			}
			// Code added by Mefteh for Mantis 2110: to set the IBAN value in a electronic Format
			if (!SwtUtil.isEmptyOrNull(acct.getAcctIBAN())) {
				String iban=acct.getAcctIBAN().replace(" ", "");
				acct.setAcctIBAN(iban);
			}
			// Instanitate the SystemInfo
			systemInfo = new SystemInfo();
			// check the errors is empty
			if (errors.isEmpty()) {
				// get the old value
				oldValue = request.getParameter("oldValue");
				// set the newValue
				newValue = new StringBuffer("Account-Name=").append(
								acct.getAcctname()).append("^Currency=").append(
								acct.getCurrcode()).append("^Account-Type=").append(
								acct.getAccttype()).append("^Account-Group=").append(
								acct.getAcctgrpflg()).append("^Account-Level=").append(
								acct.getAcctlevel()).append("^Main Account-Id=")
						.append(acct.getMinacctcode()).append(
								"^Account-gl-code=").append(acct.getGlcode())
						.append("^Account-Bic-Code=").append(
								acct.getAcctbiccode()).append(
								"^Account-Corresponding Code=").append(
								acct.getCorresacccode()).append(
								"^Account-Extra ID=").append(
								acct.getAcctextraid()).append(
								"^Account-Status=").append(
								acct.getAcctstatusflg()).append(
								"^Primary-Forecast=").append(
								acct.getPrimaryForecast()).append(
								"^Primary-External=").append(
								acct.getPrimaryExternal()).append(
								"^Secondary-Forecast=").append(
								acct.getSecondaryForecast()).append(
								"^Secondary-External=").append(
								acct.getSecondaryExternal()).append(
								"^Forecast-SOD=").append(acct.getForecastSOD())
						.append("^External-SOD=").append(acct.getExternalSOD())
						.append("^Future-Balances=").append(
								acct.getFutureBalances()).append("^Country=")
						.append(acct.getHolidaycalendar()).append(
								"^AutoSweep-Switch=").append(
								acct.getAutoswpswitch()).append(
								"^Manual-Sweep-Switch=").append(
								acct.getMansweepflg()).append("^Sweep-Time=")

						.append(acct.getEodSweeptime()).append("^Sweep Days=")
						.append(acct.getSwpdays()).append("^Min-Sweep Amount=")
						.append(acct.getMinseepamtasString()).append(
								"^Max-Sweep-Amount=").append(
								acct.getMaxsweepamteasString()).append(
								"^Book-Code=").append(acct.getSweepbookcode())
						.append("^New-Credit-Msg-Format=").append(
								acct.getNewcrfrId()).append(
								"^Debit-Msg-Format=").append(
								acct.getNewdrfrId()).append(
								"^Credit-Interest-Rate=").append(
								acct.getCurrcreditrateasString()).append(
								"^Credit-OverDraft-Rate=").append(
								acct.getCurroverdraftrateasString()).append(
								"^Target-Balance=").append(
								acct.getTargetbalanceasString()).append(
								"^Aggregate-Account=").append(
								acct.getAggAccount()).append(
								"^Archive-Data=").append(
								acct.getArchiveData()).toString();
				// get the current userid
				userId = SwtUtil.getCurrentUserId(request.getSession());
				// set the update user as current user
				acct.setUpdateUser(userId);
				// set the current hostid
				acct.getId().setHostId(
						SwtUtil.getCurrentHostId(request.getSession()));
				// set the IpAddress,OldLogString,NewLogString in systemInfo
				// bean.
				systemInfo.setIpAddress(request.getRemoteAddr());
				systemInfo.setOldLogString(oldValue);
				systemInfo.setNewLogString(newValue);
				// get the collection of account interest rate
				originalaccIntRateColl = acctMaintenanceManager
						.getAcctInterestRatecoll(hostId, entityId, acct.getId()
								.getAccountId(), SwtUtil
								.getCurrentSystemFormats(request.getSession()));
				// Instantiate the orginal hashtable
				original = new Hashtable();
				// Iterate the originalaccIntRate collection
				itroriginal = originalaccIntRateColl.iterator();
				// Instantiate the AccountInterestRate
				accIntRate = new AccountInterestRate();
				while (itroriginal.hasNext()) {
					accIntRate = (AccountInterestRate) itroriginal.next();
					original.put(accIntRate.getInterestDateRateAsString(),
							accIntRate);
				}
				// Instantiate the accIntRateCollChanged
				accIntRateCollChanged = new ArrayList();
				// Get the AccountInterestRate Collection for this Account from
				// the session
				accountInterestRateList = (ArrayList) request.getSession()
						.getAttribute("accInterestRatecollInSession"+acct.getId().getAccountId());
				newAcctIntRateList = new ArrayList();
				itrUpdate = accountInterestRateList.iterator();
				// setting HostId, EntityId and AccountId to the objects of the
				// AccountInterest Rate Colection
				accountInterestRate = new AccountInterestRate();
				while (itrUpdate.hasNext()) {
					accountInterestRate = (AccountInterestRate) itrUpdate
							.next();
					accountInterestRate.getId().setHostId(hostId);
					accountInterestRate.getId().setEntityId(entityId);
					accountInterestRate.getId().setAccountId(
							acct.getId().getAccountId());
					newAcctIntRateList.add(accountInterestRate);
				}
				// This blocks finds the Collection of AccountInterest Rate
				// Objects to be Deleted, Added and Updated
				changed = new Hashtable();
				itrchanged = newAcctIntRateList.iterator();
				while (itrchanged.hasNext()) {
					accIntRate = (AccountInterestRate) itrchanged.next();
					changed.put(accIntRate.getInterestDateRateAsString(),
							accIntRate);
					accIntRateCollChanged.add(accIntRate);
				}
				collAcctIntRateDeleted = new ArrayList();
				// Find the deleted Account Interest Rate and popualte the
				// ArrayList collAcctIntRateDeleted with the deleted objects
				for (Enumeration enuOriginal = original.keys(); enuOriginal
						.hasMoreElements();) {
					flag = false;
					originalInterestRateAsString = (String) enuOriginal
							.nextElement();
					for (Enumeration enuChanged = changed.keys(); enuChanged
							.hasMoreElements();) {
						changedInterestDateRateAsString = (String) enuChanged
								.nextElement();
						if (originalInterestRateAsString
								.equals(changedInterestDateRateAsString)) {
							flag = true;
							break;
						}
					}
					if (!flag) {
						collAcctIntRateDeleted.add(original
								.get(originalInterestRateAsString));
					}
				}
				collAcctIntRateAdded = new ArrayList();
				// Find the added Account Interest Rate and populate the
				// ArrayList collAcctIntRateAdded with the added objects
				for (Enumeration enuChanged = changed.keys(); enuChanged
						.hasMoreElements();) {
					flag = false;
					changedInterestDateRateAsString = (String) enuChanged
							.nextElement();
					for (Enumeration enuOriginal = original.keys(); enuOriginal
							.hasMoreElements();) {
						originalInterestRateAsString = (String) enuOriginal
								.nextElement();
						if (originalInterestRateAsString
								.equals(changedInterestDateRateAsString)) {
							flag = true;

							break;
						}
					}
					if (!flag) {
						collAcctIntRateAdded.add(changed
								.get(changedInterestDateRateAsString));
					}
				}
				// Instantiate the collAcctIntRateUpdated
				collAcctIntRateUpdated = new ArrayList();
				// Find the updated Position Levels and popualte the ArrayList
				// collPosLvlUpdated with the updated objects
				for (Enumeration enuChanged = changed.keys(); enuChanged
						.hasMoreElements();) {
					flag = false;
					changedInterestDateRateAsString = (String) enuChanged
							.nextElement();
					for (Enumeration enuOriginal = original.keys(); enuOriginal
							.hasMoreElements();) {
						originalInterestRateAsString = (String) enuOriginal
								.nextElement();
						if (originalInterestRateAsString
								.equals(changedInterestDateRateAsString)) {
							AccountInterestRate acctIntRateOriginal = (AccountInterestRate) original
									.get(originalInterestRateAsString);
							AccountInterestRate acctIntRateChanged = (AccountInterestRate) changed
									.get(changedInterestDateRateAsString);
							if ((!(acctIntRateChanged.getCreditRate()).equals(acctIntRateOriginal
									.getCreditRate()))
									|| (!(acctIntRateChanged.getOverdraftRate()).equals(acctIntRateOriginal
									.getOverdraftRate()))) {
								collAcctIntRateUpdated.add(acctIntRateChanged);
							}
						}
					}
				}
				/*
				 * This call will set the Contact details in the acct -
				 * AcctMaintenance Object
				 */
				setAcctContactDetails(request, acct);
				// set the SubAcctim in account bean
				if (SwtUtil.isEmptyOrNull(acct.getSubAcctim())) {
					acct.setSubAcctim("N");
				}
				// set the Svc  in account bean
				if (SwtUtil.isEmptyOrNull(acct.getSvcEntityInclBalFlag())) {
					acct.setSvcEntityInclBalFlag("N");
				}
				// update the account details
				acctMaintenanceManager.updateAccountsDetail(acct,
						collAcctIntRateDeleted, collAcctIntRateAdded,
						collAcctIntRateUpdated, systemInfo, SwtUtil
								.getCurrentSystemFormats(request.getSession()));


				allStoredSpecificInrequest = (HashMap<String, Collection<AccountSpecificSweepFormat>>) request.getSession()
						.getAttribute("acctSpecificFormatcollInSession"+acct.getId().getAccountId());
				if(allStoredSpecificInrequest == null)
					allStoredSpecificInrequest = new HashMap<String, Collection<AccountSpecificSweepFormat>>();


				if(allStoredSpecificInrequest.containsKey(csrf))  {
					specificFormatList =  allStoredSpecificInrequest.get(csrf);
				}
				if(specificFormatList != null && specificFormatList.size() > 0) {
					AcctSpecificSweepFormatManager acctSpecificSweepFormatManager = (AcctSpecificSweepFormatManager) SwtUtil.getBean("acctSpecificSweepFormatManager");
					AccountSpecificSweepFormat accountSpecificSweepFormatTmp = null;
					for (Iterator<AccountSpecificSweepFormat> iterator = specificFormatList.iterator(); iterator.hasNext();) {
						accountSpecificSweepFormatTmp = iterator.next();
						if("D".equals(accountSpecificSweepFormatTmp.getChanged())) {
							acctSpecificSweepFormatManager.deleteAccountSpecificSweepFormat(accountSpecificSweepFormatTmp);
						}else if("C".equals(accountSpecificSweepFormatTmp.getChanged())) {
							acctSpecificSweepFormatManager.updateAccountSpecificSweepFormat(accountSpecificSweepFormatTmp);
						}else if("I".equals(accountSpecificSweepFormatTmp.getChanged())) {
							acctSpecificSweepFormatManager.saveAccountSpecificSweepFormat(accountSpecificSweepFormatTmp);
						}
					}
				}
			}

			ArrayList<AccSweepSchedule> acctScheduleList = (ArrayList<AccSweepSchedule>) request.getSession()
					.getAttribute("acctSweepSchedulecollInSession"+acct.getId().getAccountId());

			ArrayList<AccSweepSchedule> oldCollAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
					.getAcctSweepScheduleList(hostId, entityId, acct
							.getId().getAccountId(), request.getSession(), acct.getCurrcode(), true);


			ArrayList<AccSweepSchedule> toAdd = new ArrayList<AccSweepSchedule>();
			ArrayList<AccSweepSchedule> toUpdate = new ArrayList<AccSweepSchedule>();
			ArrayList<AccSweepSchedule> toDelete = new ArrayList<AccSweepSchedule>();

			AccSweepSchedule accountSchedule;
			AccSweepSchedule foundAccountSchedule;
			Iterator iterator = acctScheduleList.iterator();
			while (iterator.hasNext()) {
				// get the AccountInterestRate
				accountSchedule = (AccSweepSchedule) iterator.next();
				if (accountSchedule.getSweepScheduleId() == null) {
					toAdd.add(accountSchedule);
				}

			}

			iterator = oldCollAcctSweepSchedule.iterator();
			while (iterator.hasNext()) {
				accountSchedule = (AccSweepSchedule) iterator.next();

				final Long id = accountSchedule.getSweepScheduleId();
				if(id != null) {
					foundAccountSchedule = acctScheduleList.stream().filter(item -> id.equals(item.getSweepScheduleId()))
							.findFirst().orElse(null);
					if(foundAccountSchedule != null) {
						toUpdate.add(foundAccountSchedule);
					}else {
						toDelete.add(accountSchedule);
					}
				}

			}

			for (int i = 0; i < toDelete.size(); i++) {
				acctMaintenanceManager.deleteAcctScheduleSweep(toDelete.get(i));
			}

			for (int i = 0; i < toAdd.size(); i++) {
				acctMaintenanceManager.saveOrUpdateAcctScheduleSweep(toAdd.get(i),"save");
			}

			for (int i = 0; i < toUpdate.size(); i++) {
				acctMaintenanceManager.saveOrUpdateAcctScheduleSweep(toUpdate.get(i),"update");
			}


			//save update and delete account sweep balance groups

			ArrayList<AccountSweepBalanceGroup> newCollAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) request.getSession()
					.getAttribute("acctSweepBalGrpCollInSession"+acct.getId().getAccountId());

			ArrayList<AccountSweepBalanceGroup> OldCollAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) acctMaintenanceManager
					.getAcctSweepBalGrpcoll(hostId, entityId, acct.getId().getAccountId());

			ArrayList<AccountSweepBalanceGroup> acctToAdd = new ArrayList<AccountSweepBalanceGroup>();
			ArrayList<AccountSweepBalanceGroup> acctToDelete = new ArrayList<AccountSweepBalanceGroup>();

			AccountSweepBalanceGroup acctSweepBalGrp;
			AccountSweepBalanceGroup foundAcctSweepBalGrp;


			//loop through new list and check if some ids not exist in the old list so add them

			iterator = newCollAcctSweepBalGrp.iterator();
			while (iterator.hasNext()) {
				acctSweepBalGrp = (AccountSweepBalanceGroup) iterator.next();

				final Integer id = acctSweepBalGrp.getUniqueId();
				if(id != null) {
					foundAcctSweepBalGrp = OldCollAcctSweepBalGrp.stream().filter(item -> id.equals(item.getUniqueId()))
							.findFirst().orElse(null);
					if(foundAcctSweepBalGrp == null) {
						acctToAdd.add(acctSweepBalGrp);
					}
				}
			}

			//loop through old  list and check if some ids not exist in the new list so delete them

			iterator = OldCollAcctSweepBalGrp.iterator();
			while (iterator.hasNext()) {
				acctSweepBalGrp = (AccountSweepBalanceGroup) iterator.next();

				final Integer id = acctSweepBalGrp.getUniqueId();
				if(id != null) {
					foundAcctSweepBalGrp = newCollAcctSweepBalGrp.stream().filter(item -> id.equals(item.getUniqueId()))
							.findFirst().orElse(null);
					if(foundAcctSweepBalGrp == null) {
						acctToDelete.add(acctSweepBalGrp);
					}
				}
			}

			for (int i = 0; i < acctToDelete.size(); i++) {
				acctMaintenanceManager.deleteAcctSweepBalGrp(acctToDelete.get(i));
			}

			for (int i = 0; i < acctToAdd.size(); i++) {
				acctMaintenanceManager.saveAcctSweepBalGrp(acctToAdd.get(i));
			}


			// set the
			// AccttypeList,TargetSignList,FormatList,AcctStatusList
			// in request
			putTargetSignAndSettMethodListsInReq(request, entityId);
			putFormatDetailsInRequest(request);
			putAccttypeListInReq(request, entityId);
			putAcctStatusListInReq(request, entityId);
			// set the button status into request
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			request.setAttribute("acctmaintenancelistadd", new ArrayList());
			// set the acctMaintenance into dyForm
			setAcctMaintenance(acct);
			// set the account display details into request
			putAcctDisplayDtInReq(request);
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			// set the
			// acctMaintenance,methodName,parentFormRefresh,selectedCurrencyCode
			// in request
			request.setAttribute("acctMaintenance", acct);
			request.setAttribute("methodName", "change");
			request.setAttribute("parentFormRefresh", "yes");
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");
			request.setAttribute("selectedCurrencyCode", selectedCurrencyCode);

			return getView("change");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in AcctMaintenanceAction.'update' method : "
							+ swtexp.getMessage());
			// set the button status
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			// set the hostid
			acct.getId().setHostId(hostId);
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			// get the entityName and set into request attribute
			entityName = request.getParameter("entityName");
			request.setAttribute("entityName", entityName);
			// set the
			// AccttypeList,TargetSignList,FormatList,acctmaintenancelistadd,AcctStatusList
			// in request
			request.setAttribute("acctmaintenancelistadd", new ArrayList());
			putFormatDetailsInRequest(request);
			putAccttypeListInReq(request, entityId);
			putTargetSignAndSettMethodListsInReq(request, entityId);
			putAcctStatusListInReq(request, entityId);
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			// get the account details
			acct = acctMaintenanceManager.getEditableDataDetailList(entityId,
					hostId, accountId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// set the Statusflag in bean.
			if (acct.getMinacctcode() != null) {
				acct.setStatusflag("Y");
			} else {
				acct.setStatusflag("N");
			}
			// set the statusflagmain in request
			if (acct.getAcctlevel() != null) {
				if (acct.getAcctlevel().equalsIgnoreCase("M")) {
					request.setAttribute("statusflagmain", acct.getAcctlevel());
				} else {
					request.setAttribute("statusflagmain", acct.getAcctlevel());
				}
			} else {
				request.setAttribute("statusflagmain", "M");
			}
			// set the showLoroToPredicted into request
			if (acct.getAcctClass().equalsIgnoreCase("L")) {
				request.setAttribute("showLoroToPredicted", "Y");
			} else {
				request.setAttribute("showLoroToPredicted", "N");
			}
			if(this.acctMaintenance != null)
				setAcctMaintenance(acct);

			// set the account display details into request
			putAcctDisplayDtInReq(request);
			// set the acctMaintenance,methodName in request
			request.setAttribute("acctMaintenance", acct);
			request.setAttribute("methodName", "change");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("change");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'update' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			errors = null;
			acct = null;
			hostId = null;
			entityId = null;
			accountId = null;
			accountLevel = null;
			systemInfo = null;
			oldValue = null;
			newValue = null;
			userId = null;
			originalaccIntRateColl = null;
			original = null;
			itroriginal = null;
			accIntRate = null;
			accIntRateCollChanged = null;
			accountInterestRateList = null;
			newAcctIntRateList = null;
			itrUpdate = null;
			accountInterestRate = null;
			changed = null;
			itrchanged = null;
			collAcctIntRateDeleted = null;
			collAcctIntRateAdded = null;
			collAcctIntRateUpdated = null;
			selectedCurrencyCode = null;
			entityName = null;
			log.debug(this.getClass().getName() + " - [update] - Exit");
		}
	}


	public String acctScheduleSweepDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		request.setAttribute("method", request.getParameter("methodName"));
		request.setAttribute("parentMethodName", request.getParameter("parentMethodName"));
		request.setAttribute("entityId", request.getParameter("entityId"));
		request.setAttribute("accountId", request.getParameter("accountId"));
		request.setAttribute("seqNumber", request.getParameter("seqNumber"));
		request.setAttribute("currencyCode", request.getParameter("currencyCode"));
		request.setAttribute("defaultTargetBalance", request.getParameter("defaultTargetBalance"));

		request.setAttribute("defaultTargetBalanceType", request.getParameter("defaultTargetBalanceType"));
		request.setAttribute("defaultBookCode", request.getParameter("defaultBookCode"));
		request.setAttribute("defaultSettlementMethod", request.getParameter("defaultSettlementMethod"));
		request.setAttribute("defaultMinAmount", request.getParameter("defaultMinAmount"));
		request.setAttribute("defaultFromBalanceType", request.getParameter("defaultFromBalanceType"));


		request.setAttribute("maxAmoutValue", request.getParameter("maxAmoutValue"));



		return getView("addScheduleSweep");
	}



	public String accountChangedHandler() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String entityId = null;
		String accountId = null;
		String currencyCode = null;
		String hostId = null;
		String autoSwpFlg= null;
		AcctMaintenance acctMaintenance = null;
		try {

			log.debug(this.getClass().getName() + " - [accountChangedHandler] - " + "Entry");
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyCode = request.getParameter("currencyCode");

			hostId = SwtUtil.getCurrentHostId();


			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "acctScheduleSweep";


			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());

			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			responseConstructor.createElement(SwtConstants.CURRENCY, currencyCode);


			SystemInfo systemInfo = new SystemInfo();

			acctMaintenance = acctMaintenanceManager.getEditableDataDetailList(
					entityId, hostId, accountId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			responseConstructor.createElement("settleMethod", ((acctMaintenance != null)?!SwtUtil.isEmptyOrNull(acctMaintenance.getDefaultSettleMethod())?acctMaintenance.getDefaultSettleMethod():"":""));
			responseConstructor.createElement("bookCode", ((acctMaintenance != null)?!SwtUtil.isEmptyOrNull(acctMaintenance.getSweepbookcode())?acctMaintenance.getSweepbookcode():"":""));
			responseConstructor.createElement("autoSwpFlg", ((acctMaintenance != null)?!SwtUtil.isEmptyOrNull(acctMaintenance.getAutoswpswitch())?acctMaintenance.getAutoswpswitch():"":""));
			responseConstructor.createElement("defaultFromBalanceType", ((acctMaintenance != null)?!SwtUtil.isEmptyOrNull(acctMaintenance.getSweepFrmbal())?acctMaintenance.getSweepFrmbal():"":""));
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(componentId);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [accountChangedHandler] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [accountChangedHandler] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [accountChangedHandler] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "accountChangedHandler", AcctMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Method to display the add currency screen with its loaded values collected
	 * from parent screen and data base
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String scheduleSweepDetailsData() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		String languageId = null;
		String entityId = null;
		String accountId = null;
		String currencyCode = null;
		String hostId = null;
		String seqNumber = null;
		PCMCurrencyDetailsVO currencyList;
		Collection countryList;
		Collection sourcesList;
		Collection messageTypesList;
		Collection accountGroupList;
		Collection operatorList;
		SystemFormats systemFormats = null;
		List<SelectInfo> lstSelect = null;
		List<OptionInfo> lstOptions = null;
		AccSweepSchedule accSweepSchedule = null;
		AccSweepSchedule acctScheduleIteratorValue = null;
		String methodName =null;
		String entityName = null;
		String parentMethodName = null;
		Collection acctScheduleList = null;
		String sweepEntity = null;
		try {

			log.debug(this.getClass().getName() + " - [scheduleSweepDetailsData] - " + "Entry");

			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			languageId = SwtUtil.getCurrentUser(request.getSession()).getLanguage();
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			currencyCode = request.getParameter("currencyCode");
			seqNumber = request.getParameter("seqNumber");
			methodName = request.getParameter("calledFrom");
			parentMethodName = request.getParameter("parentMethodName");


			hostId = SwtUtil.getCurrentHostId();


			Collection collEntity = SwtUtil.getUserEntityAccessList(request.getSession());
			if (collEntity != null) {
				Iterator itr = collEntity.iterator();
				while (itr.hasNext()) {
					EntityUserAccess lvb = (EntityUserAccess) (itr.next());
					if (lvb.getEntityId().equals(entityId)) {
						entityName = lvb.getEntityName();
						break;
					}
				}
			}





			if(!SwtUtil.isEmptyOrNull(seqNumber) && !"add".equalsIgnoreCase(methodName)) {
//				accSweepSchedule = acctMaintenanceManager.getAcctSweepScheduleDetails(hostId, seqNumber);
				if("add".equals(parentMethodName)) {
					acctScheduleList = (Collection) request.getSession()
							.getAttribute("acctSweepSchedulecollInSession"+"*.*");
				}else {
					acctScheduleList = (Collection) request.getSession()
							.getAttribute("acctSweepSchedulecollInSession"+accountId);
				}

//				allRecords = request.getParameter("allRecords");
//					accountSchedule.setAccountId(accountId);
//					accountSchedule.setSweepScheduleId(Long.parseLong(seqNumber));
//					accountSchedule.setEntityId(entityId);
//					accountSchedule.setHostId(hostId);
//					accountSchedule = acctMaintenanceManager.getAcctSweepScheduleDetails(hostId, seqNumber);

				Iterator iterator = acctScheduleList.iterator();
				while (iterator.hasNext()) {
					// get the AccountInterestRate
					acctScheduleIteratorValue = (AccSweepSchedule) iterator.next();
					if (acctScheduleIteratorValue.getUniqueId() ==
							Integer.parseInt(seqNumber)) {
						accSweepSchedule = acctScheduleIteratorValue;
						break;
					}
				}
				// Sets the date as string
				if(accSweepSchedule.getTargetBalance() != null) {
					accSweepSchedule.setTargetBalanceAsString(SwtUtil.formatCurrency(currencyCode, accSweepSchedule.getTargetBalance()));
				}
				if(accSweepSchedule.getMinAmount() != null) {
					accSweepSchedule.setMinAmountAsString(SwtUtil.formatCurrency(currencyCode, accSweepSchedule.getMinAmount()));
				}

			}else {
				accSweepSchedule = new AccSweepSchedule();
				accSweepSchedule.setAccountId(accountId);
				accSweepSchedule.setEntityId(entityId);
				accSweepSchedule.setCurrencyCode(currencyCode);
				if(SwtUtil.isEmptyOrNull(seqNumber)) {
					accSweepSchedule.setUniqueId(ThreadLocalRandom.current().nextInt(1000, 1500 + 1));
				}else {
					accSweepSchedule.setUniqueId(Integer.parseInt(seqNumber));

				}



			}



			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			// Get component ID
			componentId = "acctScheduleSweep";


			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.addAttribute(PCMConstant.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());

			xmlWriter.startElement(componentId);
			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN, SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			responseConstructor.createElement(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			responseConstructor.createElement(SwtConstants.CURRENCY, currencyCode);

			String accountName = null;
			if(!SwtUtil.isEmptyOrNull(accSweepSchedule.getAccountId())) {

				Collection linkAccountFullList =  acctMaintenanceManager
						.getLinkAccountFullList(putHostIdListInReq(request), accSweepSchedule.getEntityId(),
								currencyCode);
				// options drop down list
				lstOptions = new ArrayList<OptionInfo>();

				Iterator p = linkAccountFullList.iterator();
				LabelValueBean row = null;

				while (p.hasNext()) {
					row = (LabelValueBean) p.next();
					if(row.getValue().equals(accSweepSchedule.getAccountId())) {
						accountName = row.getLabel();
					}
				}


			}

			responseConstructor.createElement("AccountId", ((accSweepSchedule != null)?accSweepSchedule.getAccountId():""));
			responseConstructor.createElement("AccountName", ((accSweepSchedule != null)?accountName:""));

			responseConstructor.createElement("AllowMultiple", ((accSweepSchedule != null)?accSweepSchedule.getAllowMultiple():"Y"));
			responseConstructor.createElement("SumAccounts", ((accSweepSchedule != null)?accSweepSchedule.getSweepOnGroupBalance():"N"));
			responseConstructor.createElement("CurrencyCode", ((accSweepSchedule != null)?accSweepSchedule.getCurrencyCode():""));
			responseConstructor.createElement("EntityId", ((accSweepSchedule != null)?accSweepSchedule.getEntityId():""));
			responseConstructor.createElement("HostId", ((accSweepSchedule != null)?accSweepSchedule.getHostId():""));
			responseConstructor.createElement("MinAmount", ((accSweepSchedule != null)?accSweepSchedule.getMinAmountAsString():""));
			responseConstructor.createElement("OtherAccSettleMethodCr", ((accSweepSchedule != null)?accSweepSchedule.getOtherAccSettleMethodCr():""));
			responseConstructor.createElement("OtherAccSettleMethodDr", ((accSweepSchedule != null)?accSweepSchedule.getOtherAccSettleMethodDr():""));
			responseConstructor.createElement("OtherAccSweepBookcodeCr", ((accSweepSchedule != null)?accSweepSchedule.getOtherAccSweepBookcodeCr():""));
			responseConstructor.createElement("OtherAccSweepBookcodeDr", ((accSweepSchedule != null)?accSweepSchedule.getOtherAccSweepBookcodeDr():""));
			responseConstructor.createElement("ScheduleFrom", ((accSweepSchedule != null)?accSweepSchedule.getScheduleFrom():""));
			responseConstructor.createElement("ScheduleTo", ((accSweepSchedule != null)?accSweepSchedule.getScheduleTo():""));
			responseConstructor.createElement("SweepAccountEntity", ((accSweepSchedule != null)?accSweepSchedule.getSweepAccountEntity():""));
			responseConstructor.createElement("SweepAccountHostId", ((accSweepSchedule != null)?accSweepSchedule.getSweepAccountHostId():""));
			responseConstructor.createElement("SweepAccountId", ((accSweepSchedule != null)?accSweepSchedule.getSweepAccountId():""));
			responseConstructor.createElement("SweepDirection", ((accSweepSchedule != null)?accSweepSchedule.getSweepDirection():"B"));
			responseConstructor.createElement("SweepFromBalanceType", ((accSweepSchedule != null)?accSweepSchedule.getSweepFromBalanceType():"P"));
			responseConstructor.createElement("OtherSweepFromBalType", ((accSweepSchedule != null)?accSweepSchedule.getOtherSweepFromBalType():"P"));
			responseConstructor.createElement("TargetBalance", ((accSweepSchedule != null)?accSweepSchedule.getTargetBalanceAsString():""));
			responseConstructor.createElement("TargetBalanceType", ((accSweepSchedule != null)?accSweepSchedule.getTargetBalanceType():"C"));
			responseConstructor.createElement("ThisAccSettleMethodCr", ((accSweepSchedule != null)?accSweepSchedule.getThisAccSettleMethodCr():""));
			responseConstructor.createElement("ThisAccSettleMethodDr", ((accSweepSchedule != null)?accSweepSchedule.getThisAccSettleMethodDr():""));
			responseConstructor.createElement("ThisAccSweepBookcodeCr", ((accSweepSchedule != null)?accSweepSchedule.getThisAccSweepBookcodeCr():""));
			responseConstructor.createElement("ThisAccSweepBookcodeDr", ((accSweepSchedule != null)?accSweepSchedule.getThisAccSweepBookcodeDr():""));
			responseConstructor.createElement("uniqueId", ((accSweepSchedule != null)?""+accSweepSchedule.getUniqueId():""));

			responseConstructor.createElement("entityName", entityName);
			responseConstructor.createElement("targetBalanceTypeId", accSweepSchedule.getTargetBalanceTypeId());
//			responseConstructor.createElement("entityId", entityId);







			xmlWriter.endElement(SwtConstants.SINGLETONS);
			if("change".equalsIgnoreCase(methodName) && !SwtUtil.isEmptyOrNull(accSweepSchedule.getSweepAccountEntity())) {
				sweepEntity= accSweepSchedule.getSweepAccountEntity();
			}else {
				sweepEntity=entityId;
			}


			// form drop down details
			lstSelect = new ArrayList<SelectInfo>();

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("P", SwtUtil.getMessageFromSession("acc.predicted", request.getSession()), false));
			lstOptions.add(new OptionInfo("E", SwtUtil.getMessageFromSession("label.entity.externalIndicator", request.getSession()), false));
			lstSelect.add(new SelectInfo("balanceType", lstOptions));

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("C", SwtUtil.getMessageFromSession("format.Credit", request.getSession()), (accSweepSchedule!= null && "C".equalsIgnoreCase(accSweepSchedule.getTargetBalanceType()))));
			lstOptions.add(new OptionInfo("D", SwtUtil.getMessageFromSession("format.Debit", request.getSession()), (accSweepSchedule!= null && "D".equalsIgnoreCase(accSweepSchedule.getTargetBalanceType()))));
			lstOptions.add(new OptionInfo("A", SwtUtil.getMessageFromSession("format.Acc.Attribute", request.getSession()), (accSweepSchedule!= null && "A".equalsIgnoreCase(accSweepSchedule.getTargetBalanceType()))));
			lstOptions.add(new OptionInfo("R", SwtUtil.getMessageFromSession("format.Rule", request.getSession()), (accSweepSchedule!= null && "R".equalsIgnoreCase(accSweepSchedule.getTargetBalanceType()))));
			lstSelect.add(new SelectInfo("targetBalanceType", lstOptions));


			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("B", SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.Both", request.getSession()), (accSweepSchedule!= null && "B".equalsIgnoreCase(accSweepSchedule.getSweepDirection()))));
			lstOptions.add(new OptionInfo("F", SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.fund", request.getSession()), (accSweepSchedule!= null && "F".equalsIgnoreCase(accSweepSchedule.getSweepDirection()))));
			lstOptions.add(new OptionInfo("D", SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.defund", request.getSession()), (accSweepSchedule!= null && "D".equalsIgnoreCase(accSweepSchedule.getSweepDirection()))));
			lstSelect.add(new SelectInfo("sweepDirection", lstOptions));

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			//lstOptions.add(new OptionInfo("", "", false));
			lstOptions.add(new OptionInfo("Y", SwtUtil.getMessageFromSession("button.yes", request.getSession()), false));
			lstOptions.add(new OptionInfo("N", SwtUtil.getMessageFromSession("button.no", request.getSession()), false));
			lstSelect.add(new SelectInfo("allowMultiple", lstOptions));

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			// Cache Manager
			CacheManager cacheManagerInst = null;
			// Target Sign Collection
			Collection targetSignCol = null;
			// other Target Sign Collection
			Collection otherTargetSignCol = null;
			//Settlement Method list
			cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", entityId);


			Iterator j = targetSignCol.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("settelmentMethod", lstOptions));

			//other settelment method
			lstOptions = new ArrayList<OptionInfo>();
			// get the target sign collection according to the entity id
			otherTargetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", sweepEntity);
			j = otherTargetSignCol.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherSettelmentMethod", lstOptions));

			//Book Code list
			MovementSearchManager movementsearchManager = (MovementSearchManager) (SwtUtil
					.getBean("movementsearchManager"));
			// Populating the bookcode combo box
			Collection booklist = movementsearchManager.getBookCodeDetails(
					SwtUtil.getCurrentHostId(), entityId);

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = booklist.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue() , row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("bookList", lstOptions));

			//Other Book Code List

			// Populating the bookcode combo box
			Collection otherBooklist = movementsearchManager.getBookCodeDetails(
					SwtUtil.getCurrentHostId(), sweepEntity);

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = otherBooklist.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherBookList", lstOptions));

			// Get the user access entity list .
			Collection entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
			// Convert the entity list to label value bean.
			entityColl = SwtUtil.convertEntityAcessCollectionLVLFullName(entityColl,
					request.getSession());

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = entityColl.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(!row.getValue().equals(accountId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));



			Collection linkAccountFullList =  acctMaintenanceManager
					.getLinkAccountFullList(putHostIdListInReq(request), sweepEntity,
							currencyCode);
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = linkAccountFullList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(!row.getValue().equals(accountId))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("accountList", lstOptions));


			//get account attributes list
			Collection acctAttributesList =  acctMaintenanceManager
					.getAcctAttributesList();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = acctAttributesList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("acctAttributeList", lstOptions));

			//get sweep rules list
			Collection sweepRuleList =  acctMaintenanceManager
					.getAllowedTrgBalRulesList();
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			j = sweepRuleList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("SweepRuleList", lstOptions));
			// Add the selects node
			responseConstructor.formSelect(lstSelect);


			xmlWriter.endElement(componentId);

			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [scheduleSweepDetailsData] - " + "Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [scheduleSweepDetailsData] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();

			log.error(this.getClass().getName() + " - Exception Catched in [scheduleSweepDetailsData] method : - " + exp.getMessage());

			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "scheduleSweepDetailsData", AcctMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}


	/**
	 * Used to add a new attribute header
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveUpdateAccountScheduleSweep() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String attributeId = null;
		String attributeName = null;
		String tooltipText = null;
		String valueType = null;
		String effectiveDateRequired = null;
		String effectiveDateAllowTime = null;
		String validateNumMin = null;
		String validateNumMax = null ;
		String validateDateAllowTime = null;
		String validateTextMinLen = null;
		String validateTextMaxLen =null ;
		String validateTextRegex = null;
		String validateTextRegexMsg = null;
		String updateUser = null;
		AccSweepSchedule accountSchedule = null;
		ActionErrors errors = null;
		String entityIdAgainstAcctComboValue = null;
		String settleMethodCRComboValue = null;
		String settleMethodDRComboValue = null;
		String accountIdLabelComboValue = null;
		String othersettleMethodCRComboValue = null;
		String othersettleMethodDRComboValue = null;
		String bookCrComboValue = null;
		String bookDrComboValue = null;
		String otherBookCrComboValue = null;
		String otherBookDrComboValue = null;
		String accountIdInputValue = null;
		String fromInputValue = null;
		String toInputValue = null;
		String targetBalanceInputValue = null;
		String minAountInputValue = null;
		String directionValue = null;
		String allowMultipleValue = null;
		String targetBalanceTypeValue = null;
		String balanceTypeValue = null;
		String entityId = null;
		String accountId = null;
		String seqNumber = null;
		String methodName = null;
		String hostId = null;
		String parentMethodName = null;
		String sumAccounts = null;
		String otherBalTypeValue = null;
		try {
			log.debug(this.getClass().getName() + " method [saveUpdateAccountScheduleSweep] - Enter ");
			errors = new ActionErrors();
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();

			//Get values from request
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			seqNumber = request.getParameter("seqNumber");
			entityIdAgainstAcctComboValue = request.getParameter("entityIdAgainstAcctComboValue");
			settleMethodCRComboValue = request.getParameter("settleMethodCRComboValue");
			settleMethodDRComboValue = request.getParameter("settleMethodDRComboValue");
			accountIdLabelComboValue = request.getParameter("accountIdLabelComboValue");
			othersettleMethodCRComboValue = request.getParameter("othersettleMethodCRComboValue");
			othersettleMethodDRComboValue = request.getParameter("othersettleMethodDRComboValue");
			bookCrComboValue = request.getParameter("bookCrComboValue");
			bookDrComboValue = request.getParameter("bookDrComboValue");
			otherBookCrComboValue = request.getParameter("otherBookCrComboValue");
			otherBookDrComboValue = request.getParameter("otherBookDrComboValue");
			accountIdInputValue = request.getParameter("accountIdInputValue");
			fromInputValue = request.getParameter("fromInputValue");
			toInputValue = request.getParameter("toInputValue");
			targetBalanceInputValue = request.getParameter("targetBalanceInputValue");
			minAountInputValue = request.getParameter("minAountInputValue");
			directionValue = request.getParameter("directionValue");
			allowMultipleValue = request.getParameter("allowMultipleValue");
			targetBalanceTypeValue = request.getParameter("targetBalanceTypeValue");
			balanceTypeValue = request.getParameter("balanceTypeValue");
			otherBalTypeValue= request.getParameter("otherBalTypeValue");
			methodName = request.getParameter("calledFrom");
			parentMethodName = request.getParameter("parentMethodName");
			sumAccounts = request.getParameter("sumAccounts");
			Collection acctScheduleList = null;


			if("add".equals(parentMethodName)) {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+"*.*");
			}else {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+accountId);
			}

			if("add".equalsIgnoreCase(methodName)) {
				accountSchedule = new AccSweepSchedule();
			}else {

				if(!SwtUtil.isEmptyOrNull(seqNumber)) {
					Iterator iterator = acctScheduleList.iterator();
					while (iterator.hasNext()) {
						// get the AccountInterestRate
						accountSchedule = (AccSweepSchedule) iterator.next();
						if (accountSchedule.getUniqueId() ==
								Integer.parseInt(seqNumber)) {
							break;
						}
					}




				}
//				accountSchedule.setSweepScheduleId(SwtUtil.isEmptyOrNull(seqNumber)?null:Long.parseLong(seqNumber));
			}
			accountSchedule.setAccountId(accountId);
			accountSchedule.setAllowMultiple(!SwtUtil.isEmptyOrNull(allowMultipleValue)?allowMultipleValue:"Y");
			accountSchedule.setSweepOnGroupBalance(!SwtUtil.isEmptyOrNull(sumAccounts)?sumAccounts:"Y");
//			accountSchedule.setCurrencyCode(String)
			accountSchedule.setEntityId(entityId);
			accountSchedule.setHostId(hostId);
			if(!SwtUtil.isEmptyOrNull(minAountInputValue)) {
				accountSchedule.setMinAmount(SwtUtil.parseCurrency(minAountInputValue, SwtUtil.getCurrentCurrencyFormat(request.getSession())));
				accountSchedule.setMinAmountAsString(minAountInputValue);
			}else {
				accountSchedule.setMinAmount(new Double(0));
				accountSchedule.setMinAmountAsString("");
			}
//			accountSchedule.setMinAmountAsString(String)
			accountSchedule.setOtherAccSettleMethodCr(othersettleMethodCRComboValue);
			accountSchedule.setOtherAccSettleMethodDr(othersettleMethodDRComboValue);
			accountSchedule.setOtherAccSweepBookcodeCr(otherBookCrComboValue);
			accountSchedule.setOtherAccSweepBookcodeDr(otherBookDrComboValue);
			accountSchedule.setScheduleFrom(fromInputValue);
			accountSchedule.setScheduleTo(toInputValue);
			accountSchedule.setSweepAccountEntity(entityIdAgainstAcctComboValue);
			accountSchedule.setSweepAccountHostId(hostId);
			accountSchedule.setSweepAccountId(accountIdLabelComboValue);
			accountSchedule.setSweepDirection(!SwtUtil.isEmptyOrNull(directionValue)?directionValue:"B");
			accountSchedule.setSweepFromBalanceType(!SwtUtil.isEmptyOrNull(balanceTypeValue)?balanceTypeValue:"P");
			accountSchedule.setOtherSweepFromBalType(!SwtUtil.isEmptyOrNull(otherBalTypeValue)?otherBalTypeValue:"P");

			if (!SwtUtil.isEmptyOrNull(targetBalanceTypeValue)) {
				if ("C".equalsIgnoreCase(targetBalanceTypeValue) || "D".equalsIgnoreCase(targetBalanceTypeValue)) {

					accountSchedule.setTargetBalance(SwtUtil.parseCurrency(targetBalanceInputValue,
							SwtUtil.getCurrentCurrencyFormat(request.getSession())));
					accountSchedule.setTargetBalanceAsString(targetBalanceInputValue);

				} else {
					accountSchedule.setTargetBalanceTypeId(targetBalanceInputValue);
					accountSchedule.setTargetBalance(0.0);
				}
			}
			accountSchedule.setTargetBalanceAsString(targetBalanceInputValue);
			accountSchedule.setTargetBalanceType(!SwtUtil.isEmptyOrNull(targetBalanceTypeValue)?targetBalanceTypeValue:"C");
			accountSchedule.setThisAccSettleMethodCr(settleMethodCRComboValue);
			accountSchedule.setThisAccSettleMethodDr(settleMethodDRComboValue);
			accountSchedule.setThisAccSweepBookcodeCr(bookCrComboValue);
			accountSchedule.setThisAccSweepBookcodeDr(bookDrComboValue);
			accountSchedule.setUniqueId(SwtUtil.isEmptyOrNull(seqNumber)?null:Integer.parseInt(seqNumber));
//			seqNumber

			if(!SwtUtil.isEmptyOrNull(accountSchedule.getSweepFromBalanceType())) {
				if("P".contentEquals(accountSchedule.getSweepFromBalanceType())){
					accountSchedule.setSweepFromBalanceTypeAsString(SwtUtil.getMessageFromSession("acc.predicted", request.getSession()));
				}else {
					accountSchedule.setSweepFromBalanceTypeAsString(SwtUtil.getMessageFromSession("label.entity.externalIndicator", request.getSession()));
				}
			}
			//Mantis 6298
			if(!SwtUtil.isEmptyOrNull(accountSchedule.getOtherSweepFromBalType())) {
				if("P".contentEquals(accountSchedule.getOtherSweepFromBalType())){
					accountSchedule.setOtherSweepFromBalTypeAsString(SwtUtil.getMessageFromSession("acc.predicted", request.getSession()));
				}else {
					accountSchedule.setOtherSweepFromBalTypeAsString(SwtUtil.getMessageFromSession("label.entity.externalIndicator", request.getSession()));
				}
			}

			if(!SwtUtil.isEmptyOrNull(accountSchedule.getTargetBalanceType())) {
				if("C".contentEquals(accountSchedule.getTargetBalanceType())){
					accountSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Credit", request.getSession()));
				}else if("D".contentEquals(accountSchedule.getTargetBalanceType())) {
					accountSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Debit", request.getSession()));
				}else if("A".contentEquals(accountSchedule.getTargetBalanceType())) {
					accountSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Acc.Attribute", request.getSession()));
				}else {
					accountSchedule.setTargetBalanceTypeAsString(SwtUtil.getMessageFromSession("format.Rule", request.getSession()));
				}
			}


			if(!SwtUtil.isEmptyOrNull(accountSchedule.getSweepDirection())) {
				if("B".contentEquals(accountSchedule.getSweepDirection())){
					accountSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.Both", request.getSession()));
				}else if("F".contentEquals(accountSchedule.getSweepDirection())){
					accountSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.fund", request.getSession()));
				}else {
					accountSchedule.setSweepDirectionAsString(SwtUtil.getMessageFromSession("account.schedSweep.sweepDirection.defund", request.getSession()));
				}
			}


			if(!SwtUtil.isEmptyOrNull(accountSchedule.getAllowMultiple())) {
				if("Y".contentEquals(accountSchedule.getAllowMultiple())){
					accountSchedule.setAllowMultipleAsString(SwtUtil.getMessageFromSession("button.yes", request.getSession()));
				}else {
					accountSchedule.setAllowMultipleAsString(SwtUtil.getMessageFromSession("button.no", request.getSession()));
				}
			}

//			if("save".equalsIgnoreCase(methodName)) {

//				acctMaintenanceManager.saveOrUpdateAcctScheduleSweep(accountSchedule, methodName);

//			}else {

//			}

//			Collection acctScheduleList = (Collection) request.getSession()
//					.getAttribute("acctSweepSchedulecollInSession");
			Collection<AccSweepSchedule> newCollection = new ArrayList<AccSweepSchedule>();
			Iterator iterator = acctScheduleList.iterator();
			AccSweepSchedule accountScheduleLocal =  null;
			while (iterator.hasNext()) {
				// get the AccountInterestRate
				accountScheduleLocal = (AccSweepSchedule) iterator.next();
				if (accountScheduleLocal.getUniqueId() !=
						Integer.parseInt(seqNumber)) {
					newCollection.add(accountScheduleLocal);
				}
			}

			newCollection.add(accountSchedule);

//			acctScheduleList.add(accountSchedule);
			if("add".equalsIgnoreCase(parentMethodName)) {
				request.getSession().setAttribute("acctSweepSchedulecollInSession"+"*.*",
						newCollection);
			}else {
				request.getSession().setAttribute("acctSweepSchedulecollInSession"+accountId,
						newCollection);
			}


			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			log.debug(this.getClass().getName() + " method [saveUpdateAccountScheduleSweep] - Exit ");

		}catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveUpdateAccountScheduleSweep] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals(
					"errors.saveUpdateAccountScheduleSweep")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveUpdateAccountScheduleSweep] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location",swtExp.getStackTrace()[0].getClassName() + "."
					+ swtExp.getStackTrace()[0].getMethodName() + ":"
					+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("dataerror");

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [saveAccountAttributeHDR] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		}

		return getView("statechange");
	}


	public String getAccountScheduleSweepList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		AccSweepSchedule accountSchedule = null;
		String entityId = null;
		String accountId = null;
		String seqNumber = null;
		String methodName = null;
		String hostId = null;
		String updateUser = null;
		String currencyCode = null;
		String allRecords = null;
		String callerMethod = null;
		Collection<AccSweepSchedule> newCollection = new ArrayList<AccSweepSchedule>();
		Collection acctScheduleList = null;
		try {
			log.debug(this.getClass().getName() + " method [getAccountScheduleSweepList] - Enter ");
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();
			//Get values from request
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			seqNumber = request.getParameter("seqNumber");
			currencyCode = request.getParameter("currencyCode");
			callerMethod = request.getParameter("callerMethod");

			if("add".equals(callerMethod)) {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+"*.*");
			}else {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+accountId);
			}


//			request.getSession().setAttribute("acctSweepSchedulecollInSession",
//					acctScheduleList);
			String json = new Gson().toJson(acctScheduleList );


			response.getWriter().print(json);
			log.debug(this.getClass().getName() + " - [getAccountScheduleSweepList] - " + "Exit");

		}catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAccountScheduleSweepList] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			try {
				response.getWriter().print(true);
			} catch (IOException e) {
			}


		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getAccountScheduleSweepList] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			try {
				response.getWriter().print(true);
			} catch (IOException ex) {
			}
		}

		return null;
	}

	/**
	 * Used to add a new attribute header
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String deleteAccountScheduleSweep() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		AccSweepSchedule accountSchedule = null;
		String entityId = null;
		String accountId = null;
		String seqNumber = null;
		String methodName = null;
		String hostId = null;
		String updateUser = null;
		String currencyCode = null;
		String allRecords = null;
		Collection<AccSweepSchedule> newCollection = new ArrayList<AccSweepSchedule>();
		Collection acctScheduleList  = null;
		String callerMethod = null;
		try {
			log.debug(this.getClass().getName() + " method [deleteAccountScheduleSweep] - Enter ");
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();
			//Get values from request
			entityId = request.getParameter("entityId");
			accountId = request.getParameter("accountId");
			seqNumber = request.getParameter("seqNumber");
			currencyCode = request.getParameter("currencyCode");
			callerMethod = request.getParameter("callerMethod");



			if("add".equals(callerMethod)) {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+"*.*");
			}else {
				acctScheduleList = (Collection) request.getSession()
						.getAttribute("acctSweepSchedulecollInSession"+accountId);
			}



//			allRecords = request.getParameter("allRecords");
			if(!SwtUtil.isEmptyOrNull(seqNumber)) {
//				accountSchedule.setAccountId(accountId);
//				accountSchedule.setSweepScheduleId(Long.parseLong(seqNumber));
//				accountSchedule.setEntityId(entityId);
//				accountSchedule.setHostId(hostId);
//				accountSchedule = acctMaintenanceManager.getAcctSweepScheduleDetails(hostId, seqNumber);

				Iterator iterator = acctScheduleList.iterator();
				while (iterator.hasNext()) {
					// get the AccountInterestRate
					accountSchedule = (AccSweepSchedule) iterator.next();
					if (accountSchedule.getUniqueId() !=
							Integer.parseInt(seqNumber)) {
						newCollection.add(accountSchedule);
					}
				}




			}


//			Gson gson = new Gson();
//			Type userListType = new TypeToken<ArrayList<AccSweepSchedule>>(){}.getType();
//
//			ArrayList<AccSweepSchedule> userArray = gson.fromJson(allRecords, userListType);



//			acctMaintenanceManager.deleteAcctScheduleSweep(accountSchedule);
//
//			ArrayList<AccSweepSchedule> collAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
//					.getAcctSweepScheduleList(hostId, entityId, accountId, request.getSession(), currencyCode, true);

			if("add".equals(callerMethod)) {
				request.getSession().setAttribute("acctSweepSchedulecollInSession"+"*.*",
						newCollection);
			}
			else {
				request.getSession().setAttribute("acctSweepSchedulecollInSession"+accountId,
						newCollection);
			}
			String json = new Gson().toJson(newCollection );


			response.getWriter().print(json);
			log.debug(this.getClass().getName() + " - [deleteAccountScheduleSweep] - " + "Exit");

		}catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAccountScheduleSweep] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			try {
				response.getWriter().print(true);
			} catch (IOException e) {
			}


		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [deleteAccountScheduleSweep] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			try {
				response.getWriter().print(true);
			} catch (IOException ex) {
			}
		}

		return null;
	}

	/**
	 * This method is called on clicking the "View" button on the Account
	 * maintenance screen and loads view screen details.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable Declaration for account maintenance
		AcctMaintenance acctMaintenance = null;
		// To get the entity id
		String entityId = null;
		// To get the host id
		String hostId = null;
		// To get the system information like ip address
		SystemInfo systemInfo = null;
		// Collection of account interest rate
		ArrayList<AccountInterestRate> collAcctInterestRate = null;
		// Variable to AccountInterestRate
		AccountInterestRate acctInterestRate = null;
		// variable for collection of account maintenance
		Collection<AcctMaintenance> acctSubColumn = null;
		// variable for collection of Sweep Intermediaries
		Collection<SweepIntermediaries> sweepInter = null;
		// To get Sweep Intermediaries
		SweepIntermediaries sweepIntermediaries = null;
		// Iterator to iterate sweep Intermediaries
		Iterator<SweepIntermediaries> itrSweepIntermediaries = null;
		// Variable to get the book code details
		BookCodeManager bookCodeManager = null;
		String partyId = null;
		String selectedAccountId= null;
		try {
			log.debug("Entering 'view' method");
			acctMaintenance = getAcctMaintenance();


			// get the entityCode,hostId,accountId
			entityId = request.getParameter("entityCode");
			selectedAccountId= request.getParameter("selectedAccountId");
			hostId = putHostIdListInReq(request);
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			// get the entityName and set it into request
			request.setAttribute("entityName", request
					.getParameter("entityName"));
			// set the
			// AccttypeList,TargetSignList,AcctStatusList
			// in request
			putAcctStatusListInReq(request, entityId);
			putTargetSignAndSettMethodListsInReq(request, entityId);
			// get the editable data list for account.
			acctMaintenance = acctMaintenanceManager.getEditableDataDetailList(
					entityId, hostId, selectedAccountId
							.toString(), systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			// set the contactName,contactEmailAddr,contactPhoneNumber in
			// request
			request.setAttribute("contactName", acctMaintenance
					.getAcctContactName());
			request.setAttribute("contactEmailAddr", ((acctMaintenance
					.getAcctEmailAddr() == null) ? "" : acctMaintenance
					.getAcctEmailAddr()));
			request.setAttribute("contactPhoneNumber", ((acctMaintenance
					.getAcctPhone() == null) ? "" : acctMaintenance
					.getAcctPhone().toString()));
			// set the
			// newCrInternal,newCrExternal,newDrInternal,newDrExternal,Credit
			// External Int,Debit External Int
			// in request
			request.setAttribute("newCrInternal", ((acctMaintenance
					.getAcctNewCrInternal() == null) ? "" : acctMaintenance
					.getAcctNewCrInternal()));
			request.setAttribute("newCrExternal", ((acctMaintenance
					.getAcctNewCrExternal() == null) ? "" : acctMaintenance
					.getAcctNewCrExternal()));
			request.setAttribute("newDrInternal", ((acctMaintenance
					.getAcctNewDrInternal() == null) ? "" : acctMaintenance
					.getAcctNewDrInternal()));
			request.setAttribute("newDrExternal", ((acctMaintenance
					.getAcctNewDrExternal() == null) ? "" : acctMaintenance
					.getAcctNewDrExternal()));
			request.setAttribute("CrExternalInt", ((acctMaintenance
					.getCreditExternalInter() == null) ? "" : acctMaintenance
					.getCreditExternalInter()));
			request.setAttribute("DdExternalInt", ((acctMaintenance
					.getDebitExternalInter() == null) ? "" : acctMaintenance
					.getDebitExternalInter()));
			// This will get the Collection of AccountInterestRate and set them
			// on Session
			collAcctInterestRate = (ArrayList) acctMaintenanceManager
					.getAcctInterestRatecoll(hostId, entityId, acctMaintenance
							.getId().getAccountId(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			request.getSession().setAttribute("accInterestRatecollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctInterestRate);
			// This block sets the corresponding Latest Rates defined for
			// this account in AccountInterestRate
			acctInterestRate = new AccountInterestRate();
			if (collAcctInterestRate.size() > 0) {
				// get the AccountInterestRate
				acctInterestRate = collAcctInterestRate.get(0);
				// set the LatestCreditRate
				acctMaintenance.setLatestCreditRate(acctInterestRate
						.getCreditRate().toString());
				// set the LatestOverDraftRate
				acctMaintenance.setLatestOverDraftRate(acctInterestRate
						.getOverdraftRate().toString());
				// setLatestInterestDateRate
				acctMaintenance.setLatestInterestDateRate(acctInterestRate
						.getInterestDateRateAsString());
			}
			// set the
			// MinseepamtasString,MaxsweepamteasString,TargetbalanceasString,CurrcreditrateasString,CurroverdraftrateasString
			// in the bean
			if (acctMaintenance.getMinseepamt() != null) {
				acctMaintenance.setMinseepamtasString(SwtUtil.formatCurrency(
						acctMaintenance.getCurrcode(), acctMaintenance
								.getMinseepamt()));
			}
			if (acctMaintenance.getMaxsweepamte() != null) {
				acctMaintenance.setMaxsweepamteasString(SwtUtil.formatCurrency(
						acctMaintenance.getCurrcode(), acctMaintenance
								.getMaxsweepamte()));
			}
			if (acctMaintenance.getTargetbalance() != null) {
				acctMaintenance.setTargetbalanceasString(SwtUtil
						.formatCurrency(acctMaintenance.getCurrcode(),
								acctMaintenance.getTargetbalance()));
			}
			if (acctMaintenance.getCurrcreditrate() != null) {
				acctMaintenance.setCurrcreditrateasString(acctMaintenance
						.getCurrcreditrate().toString());
			}
			if (acctMaintenance.getCurroverdraftrate() != null) {
				acctMaintenance.setCurroverdraftrateasString(SwtUtil
						.formatCurrency(acctMaintenance.getCurrcode(),
								acctMaintenance.getCurroverdraftrate()));
			}
			acctMaintenance
					.setStatusflag(acctMaintenance.getMinacctcode() != null ? "Y"
							: "N");
			// set the PrimaryForecast
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getPrimaryForecast()))
				acctMaintenance.setPrimaryForecast("N");
			// set the PrimaryExternal
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getPrimaryExternal()))
				acctMaintenance.setPrimaryExternal("N");
			// set the SecondaryForecast
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getSecondaryForecast()))
				acctMaintenance.setSecondaryForecast("N");
			// set the SecondaryExternal
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getSecondaryExternal()))
				acctMaintenance.setSecondaryExternal("N");
			// set the ForecastSOD
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getForecastSOD()))
				acctMaintenance.setForecastSOD("N");
			// set the ExternalSOD
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getExternalSOD()))
				acctMaintenance.setExternalSOD("N");
			// set the FutureBalances
			if (SwtUtil.isEmptyOrNull(acctMaintenance.getFutureBalances()))
				acctMaintenance.setFutureBalances("T");
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance in request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// set the button status
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			// get the sub account list
			acctSubColumn = acctMaintenanceManager.getSubColumnDataDetailList(
					entityId, hostId, selectedAccountId
							.toString(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// set the subAccountsPresent in request
			if ((acctSubColumn != null) && (acctSubColumn.size() > 0)) {
				request.setAttribute("subAccountsPresent", "yes");
			}
			request.setAttribute("linkedAccount", request
					.getParameter("linkedAccount"));
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);
			// set the methodName,accountLevel in request
			request.setAttribute("methodName", "view");
			request
					.setAttribute("accountLevel", acctMaintenance
							.getAcctlevel());
			// get the IntermediaryRecord list
			sweepInter = acctMaintenanceManager.getIntermediaryRecord(hostId,
					entityId, acctMaintenance.getCurrcode(), acctMaintenance
							.getAcctbiccode());
			// Intsantiate the SweepIntermediaries
			sweepIntermediaries = new SweepIntermediaries();
			// iterate the sweepInter
			itrSweepIntermediaries = sweepInter.iterator();
			while (itrSweepIntermediaries.hasNext()) {
				SweepIntermediaries swpInter = itrSweepIntermediaries.next();
				// set the Intermediary
				sweepIntermediaries.setIntermediary(swpInter.getIntermediary());
			}
			// get the country name for country code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getHolidaycalendar())) {
				request.setAttribute("countryName", acctMaintenanceManager
						.getCountryDetail(acctMaintenance.getHolidaycalendar())
						.getCountryName());
			}
			// get the currency name for currency code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getCurrcode())) {
				request.setAttribute("currencyName", acctMaintenanceManager
						.getCurrencyDetail(entityId, hostId,
								acctMaintenance.getCurrcode())
						.getCurrencyMaster().getCurrencyName());
			}
			// get the main account name for main account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getMinacctcode())) {
				request
						.setAttribute("mainAcctName", acctMaintenanceManager
								.getMainOrLinkAccount(entityId, hostId,
										acctMaintenance.getMinacctcode())
								.getAcctname());
			}
			// get the link account name for link account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getLinkAccID())) {
				request.setAttribute("linkAcctName", acctMaintenanceManager
						.getMainOrLinkAccount(entityId, hostId,
								acctMaintenance.getLinkAccID()).getAcctname());
			}
			// get the book code name for book code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getSweepbookcode())) {
				bookCodeManager = (BookCodeManager) SwtUtil
						.getBean("bookCodeManager");
				request.setAttribute("bookCodeName", bookCodeManager
						.getEditableData(hostId, entityId,
								acctMaintenance.getSweepbookcode())
						.getBookName());
			}

			ArrayList<AccSweepSchedule> collAcctSweepSchedule = (ArrayList<AccSweepSchedule>) acctMaintenanceManager
					.getAcctSweepScheduleList(hostId, entityId, acctMaintenance
							.getId().getAccountId(), request.getSession(), acctMaintenance.getCurrcode(), true);

			long usedInOtherSweepCount = acctMaintenanceManager
					.getAcctSweepScheduleUsedinCount(hostId, entityId, acctMaintenance
							.getId().getAccountId());

			request.getSession().setAttribute("acctSweepSchedulecoll",
					collAcctSweepSchedule);
			request.setAttribute("acctSweepSchedulecoll",
					collAcctSweepSchedule);

			request.setAttribute("usedInOtherSweepCount", usedInOtherSweepCount);

			request.getSession().setAttribute("acctSweepSchedulecollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctSweepSchedule);


			//put account sweep balance groups list from DB and put it in session

			ArrayList<AccountSweepBalanceGroup> collAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) acctMaintenanceManager
					.getAcctSweepBalGrpcoll(hostId, entityId, acctMaintenance.getId().getAccountId());

			int acctSweepBalCount= collAcctSweepBalGrp.size();

			String acctSweepListJson = new Gson().toJson(collAcctSweepBalGrp );

			request.setAttribute("acctSweepBalGrpCollInSessionJson"+acctMaintenance.getId().getAccountId(), acctSweepListJson);

			request.getSession().setAttribute("acctSweepBalGrpCollInSession"+acctMaintenance.getId().getAccountId(),
					collAcctSweepBalGrp);

			request.setAttribute("acctSweepBalCount", acctSweepBalCount);

			setSweepintermediaries(sweepIntermediaries);

			// Get the party name if the party ID is well defined int the selected account
			partyId = acctMaintenance.getAccountPartyId();
			if (!SwtUtil.isEmptyOrNull(partyId)) {
				request.setAttribute("partyDesc", getPartyName(hostId, entityId, partyId));
			} else {
				request.setAttribute("partyDesc", "");
			}
			setAcctMaintenance(acctMaintenance);
			log.debug("Exiting 'View' method");
			return getView("view");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'view' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'view' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acctMaintenance = null;
			entityId = null;
			hostId = null;
			systemInfo = null;
			bookCodeManager = null;
			collAcctInterestRate = null;
			acctInterestRate = null;
			acctSubColumn = null;
			sweepInter = null;
			sweepIntermediaries = null;
			itrSweepIntermediaries = null;
		}
	}

	/**
	 * This method is called on clicking the "Delete" button on the Account
	 * maintenance screen and delete the account details from database.
	 * @return
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the dyForm object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold hostId
		String hostId = null;
		// String variable to hold accountId
		String accountId = null;
		// String variable to hold roleId
		String roleId = null;
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		// Variable to hold the lvbCurrencyList object
		ArrayList<LabelValueBean> lvbCurrencyList = null;
		// Variable to hold the lvbCurrencyCode object
		LabelValueBean lvbCurrencyCode = null;
		// Variable to hold the accountAccessManager object
		AccountAccessManager accountAccessManager = null;
		// Variable to hold the accountInterestRateList object
		Collection<AccountInterestRate> accountInterestRateList = null;
		// Variable to hold the acctmaintDetailsVO object
		AcctMaintenanceDetailVO accountmaintenanceDetailsVO = null;
		// String variable to hold currencyCode
		String currencyCode = null;
		// Integer varable to hold accessInd index
		int accessInd;
		try {
			log.debug(this.getClass().getName() + "- [delete] - Entering ");

			acctMaintenance = getAcctMaintenance();



			// get the entityId from dyform
			//Nadia
			//entityId = (String) dyForm.get("selectedEntityId");
			// get the hostId
			hostId = putHostIdListInReq(request);
			// get the accountId from dyform
			//accountId = (String) dyForm.get("selectedAccountId");
			// set the accountId,entityId,hostId in acctMaintenance
			acctMaintenance.getId().setAccountId(accountId);
			acctMaintenance.getId().setEntityId(entityId);
			acctMaintenance.getId().setHostId(hostId);
			// get the AccountAccessManager Instance
			accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			// delete the account access details
			accountAccessManager.deleteAcctDetails(accountId);
			// get the account InterestRateList
			accountInterestRateList = acctMaintenanceManager
					.getAcctInterestRatecoll(hostId, entityId, acctMaintenance
							.getId().getAccountId(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			/*
			 * Start:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// get the roleId from commondatamanager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// delete the account details
			acctMaintenanceManager.deleteAcctDetail(acctMaintenance,
					accountInterestRateList, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			accountmaintenanceDetailsVO = acctMaintenanceManager
					.getCurrencyDetailList(entityId, hostId, roleId, "All", null, "All");
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// set the
			// acctmaintenancelist,acctmaintenanceDetails,EntityList,
			// in request
			request.setAttribute("acctmaintenancelist", getCurrencyList(
					request, hostId, entityId));
			request
					.setAttribute("acctmaintenanceDetails",
							accountmaintenanceDetailsVO
									.getAcctmaintenancelistDetails());
			putEntityListInReq(request);
			// set the acctMaintenance in dyForm
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance in request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// call the displayListByEntity method
			displayListByEntity();
			log.debug(this.getClass().getName() + "- [delete] - Exit ");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'delete' method : "
							+ swtexp.getMessage());
			// get the account details from accountmaintenancemanager
			accountmaintenanceDetailsVO = acctMaintenanceManager
					.getCurrencyDetailList(entityId, hostId, roleId, "All", null, "All");
			// get the view or full access currencyList
			currencyList = (ArrayList<LabelValueBean>) SwtUtil
					.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(
							roleId, entityId);
			// Add All currency to currencylist
			if (currencyList != null) {
				currencyList.remove(new LabelValueBean("Default", "*"));
				lvbCurrencyList = new ArrayList<LabelValueBean>();
				lvbCurrencyList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
						SwtConstants.ALL_VALUE));
				lvbCurrencyList.addAll(currencyList);
			}
			// set the
			// acctmaintenancelist,acctmaintenanceDetails,EntityList,
			// in request
			request.setAttribute("acctmaintenancelist", lvbCurrencyList);
			// Get the Currency vlaue
			if (lvbCurrencyList.size() > 0) {
				lvbCurrencyCode = (LabelValueBean) lvbCurrencyList.get(0);
				currencyCode = lvbCurrencyCode.getValue();
			}
			request
					.setAttribute("acctmaintenanceDetails",
							accountmaintenanceDetailsVO
									.getAcctmaintenancelistDetails());
			putEntityListInReq(request);
			// set the button status for add,change,view,delete,save,cancel
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			// set the EntityAccess in request
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			if (accessInd == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayListByEntity();
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'delete' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			acctMaintenance = null;
			entityId = null;
			hostId = null;
			accountId = null;
			roleId = null;
			currencyList = null;
			lvbCurrencyList = null;
			accountAccessManager = null;
			accountInterestRateList = null;
			accountmaintenanceDetailsVO = null;
			currencyCode = null;
		}
	}


	/**
	 * This method is called on clicking the "Delete" button on the Account
	 * maintenance screen and delete the account details from database.
	 * @return
	 * @throws SwtException
	 */
	public String deleteAccountAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the dyForm object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold hostId
		String hostId = null;
		// String variable to hold accountId
		String accountId = null;
		// String variable to hold roleId
		String roleId = null;
		AccountAccessManager accountAccessManager = null;
		// Variable to hold the accountInterestRateList object
		Collection<AccountInterestRate> accountInterestRateList = null;

		try {
			entityId = request.getParameter("selectedEntityId");
			accountId = request.getParameter("selectedAccountId");
			hostId = SwtUtil.getCurrentHostId();
			acctMaintenance = new AcctMaintenance();
			acctMaintenance.getId().setAccountId(accountId);
			acctMaintenance.getId().setEntityId(entityId);
			acctMaintenance.getId().setHostId(hostId);
			// get the AccountAccessManager Instance
			accountAccessManager = (AccountAccessManager) (SwtUtil
					.getBean("accountAccessManager"));
			// delete the account access details
			accountAccessManager.deleteAcctDetails(accountId);
			// get the account InterestRateList
			accountInterestRateList = acctMaintenanceManager
					.getAcctInterestRatecoll(hostId, entityId, acctMaintenance
							.getId().getAccountId(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			/*
			 * Start:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// get the roleId from commondatamanager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// delete the account details
			acctMaintenanceManager.deleteAcctDetail(acctMaintenance,
					accountInterestRateList, SwtUtil
							.getCurrentSystemFormats(request.getSession()));

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message","");
			log.debug(this.getClass().getName() + "- [delete] - Exit ");
			return getView("statechange");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'delete' method : "
							+ swtexp.getMessage());
			request.setAttribute("reply_status_ok", "false");
			if("errors.DataIntegrityViolationExceptioninDelete".equalsIgnoreCase(swtexp.getErrorCode())){
				request.setAttribute("reply_message","DataIntegrityViolationExceptioninDelete");
			}else {
				request.setAttribute("reply_message","");
			}
			SwtUtil.logException(swtexp, request, "");
			log.debug(this.getClass().getName() + "- [delete] - Exit ");
			return getView("statechange");
		}
	}

	/**
	 * This method is called on clicking the "Sub A/C" button on the Account
	 * maintenance screen and display the sub accounts details.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String subAccounts()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// AcctMaintenance
		AcctMaintenance acct = null;
		// Entity Id
		String entityId = null;
		// accountId
		String accountId = null;
		// selectedCurrCode
		String selectedCurrCode = null;
		// mainAccountId
		String mainAccountId = null;
		// hostId
		String hostId = null;
		// Sub account collection.
		Collection acctSubColumn = null;
		// CurrencyManager
		CurrencyManager currencyManagerObj = null;
		// CurrencyDetailVO
		CurrencyDetailVO currencyDetailVOObj = null;
		// ArrayList
		ArrayList arraylist = null;
		// Iterator
		Iterator itr = null;
		// Currency Code
		String currCode = null;
		// Collection of Entity
		Collection collEntity = null;
		try {
			log.debug("Entering 'Account maintenance subAccounts' method");
			// set the screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "true");
			// set the button status.
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);

			acct = getAcctMaintenance();




			// get the entityId from request
			entityId = request.getParameter("entityCode");
			if (entityId != null) {
				entityId = entityId.trim();
			}
			// get the accountId from request
			accountId = request.getParameter("selectedAccountId");
			if (accountId != null) {
				accountId = accountId.trim();
			}
			// get the selectedCurrCode
			selectedCurrCode = request.getParameter("selectedCurrencyCode");

			// set the entityId,accountId,selectedCurrCode to bean.
			acct.getId().setEntityId(entityId);
			acct.getId().setAccountId(accountId);
			acct.setCurrcode(selectedCurrCode);
			// get the mainAccountId,hostId
			mainAccountId = accountId;
			hostId = putHostIdListInReq(request);
			// get the sub account collection
			acctSubColumn = acctMaintenanceManager.getSubColumnDataDetailList(
					entityId, hostId, mainAccountId, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// set the acctmaintenanceDetails in to request.
			request.setAttribute("acctmaintenanceDetails", acctSubColumn);
			// get the CurrencyManager instance
			currencyManagerObj = (CurrencyManager) (SwtUtil
					.getBean("currencyManager"));
			// get the currency details list
			currencyDetailVOObj = currencyManagerObj.getCurrencyDetailList(
					entityId, hostId, "All");
			// get the currency list
			arraylist = (ArrayList) currencyDetailVOObj.getCurrencyList();
			arraylist.remove(0);
			// set the acctmaintenancelist in to request.
			request.setAttribute("acctmaintenancelist", arraylist);


			request.setAttribute("selectedCurrency", selectedCurrCode);
			request.setAttribute("selectedEntity", entityId);
			request.setAttribute("accountId", accountId);




			// set the accountCurrencyName in to request.
			if (arraylist != null) {
				currCode = acct.getCurrcode();
				itr = arraylist.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					if (lvb.getValue().equals(currCode)) {
						request.setAttribute("accountCurrencyName", lvb
								.getLabel());
					}
				}
			}
			// set the EntityList in request
			log.debug("Entering 'display subAccounts' subAccounts method");
			putEntityListInReq(request);
			// get the entities collection form the request.
			collEntity = (Collection) request.getAttribute("entities");
			// set the accountEntityName in to request.
			if (collEntity != null) {
				itr = collEntity.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					if (lvb.getValue().equals(entityId)) {
						request.setAttribute("accountEntityName", lvb
								.getLabel());
						break;
					}
				}
			}
			setAcctMaintenance(acct);
			// set the acctMaintenance,methodName into request
			request.setAttribute("acctMaintenance", acct);
			request.setAttribute("methodName", "subAccounts");
			//clear column width and column order from session
			request.getSession().setAttribute("column_order", null);
			request.getSession().setAttribute("column_width", null);
			log.debug("Exiting subAccounts 'list' method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'subAccounts' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'subAccounts' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "subAccounts", AcctMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
			// nullify objects
			acct = null;
			entityId = null;
			accountId = null;
			selectedCurrCode = null;
			mainAccountId = null;
			hostId = null;
			acctSubColumn = null;
			currencyManagerObj = null;
			currencyDetailVOObj = null;
			arraylist = null;
			itr = null;
			currCode = null;
		}
	}

	/**
	 * This method is called on clicking the "copyFrom" button on the Account
	 * maintenance screen and display the account details for copy.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copyFrom()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		// String Variable to hold the hostId
		String hostId = null;
		// String Variable to hold the entityId
		String entityId = null;
		try {
			log.debug(this.getClass().getName() + "- [copyFrom] - Entering ");

			acctMaintenance = getAcctMaintenance();



			// set the newAcctId into request
			request
					.setAttribute("newAcctId", request
							.getParameter("newAcctId"));
			// get the hostId
			hostId = putHostIdListInReq(request);
			// get the entityId
			entityId = request.getParameter("entityCode");
			// set the screenFieldsStatus into request
			request.setAttribute("screenFieldsStatus", "false");
			// set the hostId,entityId,currencycode into bean.
			acctMaintenance.getId().setHostId(hostId);
			acctMaintenance.getId().setEntityId(entityId);
			/*
			 * Start:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			acctMaintenance.setCurrcode(request.getParameter("currencyCode"));
			request.setAttribute("acctmaintenancelistadd",
					getCurrencyFullAccessList(request, hostId, entityId));
			request.setAttribute("selectedCurrencyCode", request
					.getParameter("selectedCurrencyCode"));
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance into request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// set the entityId into request session
			request.setAttribute("entityId", entityId);
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			return getView("copyFrom");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'copyFrom' method : "
							+ swtexp.getMessage());

			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'copyFrom' method : "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copyFrom", AcctMaintenanceAction.class), request, "");

			return getView("fail");
		} finally {
			acctMaintenance = null;
			hostId = null;
			entityId = null;
		}
	}

	/**
	 * This method is called on clicking the "OK" button on the Copy From
	 * Account maintenance screen and copy the account details into add account
	 * maintenance screen.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String copy()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold acct object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold accountId
		String accountId = null;
		// String variable to hold Host Id
		String hostId = null;
		// entityId
		String entityId = null;
		// Variable to hold systemInfo object
		SystemInfo systemInfo = null;
		// Variable to hold country object
		Country country = null;
		// Variable to hold mainAcct object
		AcctMaintenance mainAccount = null;
		// Variable to hold linkAcct object
		AcctMaintenance linkAccount = null;
		// Variable to hold bookCodeManager object
		BookCodeManager bookCodeManager = null;
		// Variable to hold bookCode object
		BookCode bookCode = null;
		try {
			log.debug(this.getClass().getName() + "- [copy] - Entering ");
			acctMaintenance = getAcctMaintenance();




			// get the selectedAccountId from dyForm
			accountId = this.selectedAccountId;
			// get the host id
			hostId = putHostIdListInReq(request);
			// set the entityName into request
			request.setAttribute("entityName", request.getSession()
					.getAttribute("entityName"));
			// set the host id into bean
			acctMaintenance.getId().setHostId(hostId);
			// get the entityId from session
			entityId = (String) request.getSession().getAttribute("entityId");
			// set the entityId into bean
			acctMaintenance.getId().setEntityId(entityId);
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			// get the account details for copy.
			acctMaintenance = acctMaintenanceManager.copyAccountIdDetails(
					hostId, entityId, accountId, systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// set the AccountId
			acctMaintenance.getId().setAccountId("");
			// set the statusflagmain into request
			if (acctMaintenance.getAcctlevel() != null) {
				if (acctMaintenance.getAcctlevel().equalsIgnoreCase("M")) {
					request.setAttribute("statusflagmain", acctMaintenance
							.getAcctlevel());
				} else {
					request.setAttribute("statusflagmain", acctMaintenance
							.getAcctlevel());
				}
			} else {
				request.setAttribute("statusflagmain", "M");

			}
			// set the AccttypeList,TargetSignList AcctStatusList in request
			putAccttypeListInReq(request, entityId);
			putAcctStatusListInReq(request, entityId);
			putTargetSignAndSettMethodListsInReq(request, entityId);

			// set the contactName,emailAddr,phone in
			// request
			request.setAttribute("contactName", acctMaintenance
					.getAcctContactName());
			request.setAttribute("emailAddr", ((acctMaintenance
					.getAcctEmailAddr() == null) ? "" : acctMaintenance
					.getAcctEmailAddr()));
			request.setAttribute("phone",
					((acctMaintenance.getAcctPhone() == null) ? ""
							: acctMaintenance.getAcctPhone().toString()));
			// set the
			// newCrInternal,newCrExternal,newDrInternal,newDrExternal,CrExternalInt,DdExternalInt
			// in request
			request.setAttribute("newCrInternal", ((acctMaintenance
					.getAcctNewCrInternal() == null) ? "" : acctMaintenance
					.getAcctNewCrInternal()));
			request.setAttribute("newCrExternal", ((acctMaintenance
					.getAcctNewCrExternal() == null) ? "" : acctMaintenance
					.getAcctNewCrExternal()));
			request.setAttribute("newDrInternal", ((acctMaintenance
					.getAcctNewDrInternal() == null) ? "" : acctMaintenance
					.getAcctNewDrInternal()));
			request.setAttribute("newDrExternal", ((acctMaintenance
					.getAcctNewDrExternal() == null) ? "" : acctMaintenance
					.getAcctNewDrExternal()));
			request.setAttribute("CrExternalInt", ((acctMaintenance
					.getCreditExternalInter() == null) ? "" : acctMaintenance
					.getCreditExternalInter()));
			request.setAttribute("DdExternalInt", ((acctMaintenance
					.getDebitExternalInter() == null) ? "" : acctMaintenance
					.getDebitExternalInter()));
			// set the showLoroToPredicted to request
			if (acctMaintenance.getAcctClass().equalsIgnoreCase("L")) {
				request.setAttribute("showLoroToPredicted", "Y");
			} else {
				request.setAttribute("showLoroToPredicted", "N");
			}
			// This will set an empty ArrayList for InterestRate Collection in
			// the Session
			request.getSession().setAttribute("accInterestRatecollInSession"+"*.*",
					new ArrayList());
			/*
			 * Code Modified For Mantis 1592 by Sudhakar on 22-12-2011:Account
			 * Maintenance screen allows to create account for entity that has
			 * no currency access
			 */
			// setting the FullAccesscurrencyList in request
			request.setAttribute("acctmaintenancelistadd",
					getCurrencyFullAccessList(request, hostId, entityId));
			// set the entity list into request
			putEntityListInReq(request);
			// set the entity id,AccountId
			acctMaintenance.getId().setEntityId(entityId);
			acctMaintenance.getId().setAccountId(
					request.getParameter("newAcctId"));
			setAcctMaintenance(acctMaintenance);
			// get the country name for country code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getHolidaycalendar())) {
				country = acctMaintenanceManager
						.getCountryDetail(acctMaintenance.getHolidaycalendar());
				request.setAttribute("countryName", country.getCountryName());
			}
			// get the main account name for main account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getMinacctcode())) {
				mainAccount = acctMaintenanceManager.getMainOrLinkAccount(
						entityId, hostId, acctMaintenance.getMinacctcode());
				request.setAttribute("mainAcctName", mainAccount.getAcctname());
			}
			// get the link account name for link account id and set into
			// request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getLinkAccID())) {
				linkAccount = acctMaintenanceManager.getMainOrLinkAccount(
						entityId, hostId, acctMaintenance.getLinkAccID());
				request.setAttribute("linkAcctName", linkAccount.getAcctname());
			}
			// get the book code name for book code and set into request
			if (!SwtUtil.isEmptyOrNull(acctMaintenance.getSweepbookcode())) {
				bookCodeManager = (BookCodeManager) SwtUtil
						.getBean("bookCodeManager");
				bookCode = bookCodeManager.getEditableData(hostId, entityId,
						acctMaintenance.getSweepbookcode());
				request.setAttribute("bookCodeName", bookCode.getBookName());
			}
			/*
			 * Code Added For Mantis 1562 by Sudhakar on 12-12-2011:Account
			 * Maintenance Main screen: currency selection changes after adding
			 * a new account
			 */
			// set selectedCurrencyCode in request
			request.setAttribute("selectedCurrencyCode", request
					.getParameter("selectedCurrencyCode"));
			// set the screenFieldsStatus,acctMaintenance,methodName into
			// request
			request.setAttribute("screenFieldsStatus", "false");
			request.setAttribute("acctMaintenance", acctMaintenance);
			request.setAttribute("methodName", "add");
			// codde Added by Mefteh for Mantis 2110 Retrieve the country list from database based on the parameters passed
			Collection countryList = acctMaintenanceManager.getCountryList();
			request.setAttribute("countryList", countryList);

			log.debug(this.getClass().getName() + "- [copy] - Exiting ");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'copy' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'copy' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "copy", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acctMaintenance = null;
			accountId = null;
			hostId = null;
			entityId = null;
			systemInfo = null;
			bookCodeManager = null;
			bookCode = null;
		}
	}

	/**
	 * This method is called on clicking the "Linked" button on the Change
	 * Account maintenance screen and display the linked account details
	 * @return
	 * @throws SwtException
	 */
	public String getLinkedAccounts() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// AcctMaintenance
		AcctMaintenance acct = null;
		// entityId
		String entityId = null;
		// accountId
		String accountId = null;
		// selectedCurrCode
		String selectedCurrCode = null;
		// hostId
		String hostId = null;
		// linkedAccountColl
		Collection linkedAccountColl = null;
		// collEntity
		Collection collEntity = null;
		// Iterator
		Iterator itr = null;
		try {
			log.debug("Entering getLinkedAccounts method");
			// set the screenFieldsStatus into request
			request.setAttribute("screenFieldsStatus", "true");
			// set the button status
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE);
			acct = getAcctMaintenance();



			// get the entityId
			entityId = request.getParameter("entityId");
			if (entityId != null) {
				entityId = entityId.trim();
			}
			// get the accountId from request
			accountId = request.getParameter("accountId");
			if (accountId != null) {
				accountId = accountId.trim();
			}
			// get the selectedCurrencyCode from request
			selectedCurrCode = request.getParameter("selectedCurrencyCode");
			// set the entity id,AccountId,Currcode
			acct.getId().setEntityId(entityId);
			acct.getId().setAccountId(accountId);
			acct.setCurrcode(selectedCurrCode);
			// get the hostId
			hostId = putHostIdListInReq(request);
			// get the collection of linked accounts
//			linkedAccountColl = acctMaintenanceManager.getLinkedAccounts(
//					hostId, entityId, accountId);
//			// set the acctmaintenanceDetails,acctmaintenancelist in request
//			request.setAttribute("acctmaintenanceDetails", linkedAccountColl);
			Collection<LabelValueBean> currencyList = getCurrencyList(
					request, hostId, entityId);
			for (LabelValueBean element : currencyList) {
				// Access each element here
				if(element.getValue().equals(selectedCurrCode)) {
					request.setAttribute("accountCurrencyName", element
							.getLabel());
				}
			}
//			request.setAttribute("acctmaintenancelist", );
			// get the entities collection form the request.
			putEntityListInReq(request);
			collEntity = (Collection) request.getAttribute("entities");
			// set the accountEntityName in to request.
			if (collEntity != null) {
				itr = collEntity.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					if (lvb.getValue().equals(entityId)) {
						request.setAttribute("accountEntityName", lvb
								.getLabel());
						break;
					}
				}
			}

			request.setAttribute("selectedCurrency", selectedCurrCode);
			request.setAttribute("selectedEntity", entityId);
			request.setAttribute("accountId", accountId);

			//clear column width and column order from session
			request.getSession().setAttribute("column_order", null);
			request.getSession().setAttribute("column_width", null);

			setAcctMaintenance(acct);
			request.setAttribute("acctMaintenance", acct);
			// set the methodName,linkedAccount into request
			request.setAttribute("methodName", "subAccounts");
			request.setAttribute("linkedAccount", "yes");
			log.debug("Exiting getLinkedAccounts method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getLinkedAccounts' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getLinkedAccounts' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "accountListByCurrency", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acct = null;
			entityId = null;
			accountId = null;
			selectedCurrCode = null;
			hostId = null;
			linkedAccountColl = null;
			collEntity = null;
			itr = null;
		}
	}

	/**
	 * This function added 'displayContact'for the new functionality of Contact
	 * for Rabo Bank
	 * @return ActionForward - mapping.findForward("displayContact");
	 * @throws Exception -
	 *             SwtException
	 */
	public String displayContact()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Contact Name
		String contactName = null;
		// Contact Phone Number
		String contactPhoneNumber = null;
		// Contact Email Address
		String contactEmailAddr = null;
		// Screen Status
		String screenStatus = null;
		// AcctMaintenance
		AcctMaintenance acct = null;
		try {
			// get the contactName from request
			contactName = request.getParameter("contactName");
			// get the contactPhoneNumber from request
			contactPhoneNumber = request.getParameter("contactPhoneNumber");
			// get the contactEmailAddr from request
			contactEmailAddr = request.getParameter("contactEmailAddr");
			// get the screenStatus from request
			screenStatus = request.getParameter("screenStatus");
			// set the screenFieldsStatus into request
			if ("view".equalsIgnoreCase(screenStatus)) {
				request.setAttribute("screenFieldsStatus",
						SwtConstants.STR_TRUE);
			} else {
				request.setAttribute("screenFieldsStatus",
						SwtConstants.STR_FALSE);
			}
			acct = getAcctMaintenance();



			// set the AcctContactName
			if (contactName == null) {
				contactName = "";
			} else {
				acct.setAcctContactName(contactName);
			}
			// set the AcctPhone
			if (contactPhoneNumber == null) {
				contactPhoneNumber = "";
			} else {
				acct.setAcctPhone(contactPhoneNumber);
			}
			// set the AcctEmailAddress
			if (contactEmailAddr == null) {
				contactEmailAddr = "";
			} else {
				acct.setAcctEmailAddr(contactEmailAddr);
			}
			setAcctMaintenance(acct);
			// set the screenStatus into request
			request.setAttribute("screenStatus", screenStatus);
			return getView("displayContact");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayContact' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayContact", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			contactName = null;
			contactPhoneNumber = null;
			contactEmailAddr = null;
			screenStatus = null;
			acct = null;
		}
	}

	/**
	 *
	 * @desc - This method 'saveContactDetails' saves the relevant data in the
	 *       hidden variables of the acctmaintennaceadd jsp
	 * @return ActionForward - mapping.findForward("displayContact");
	 * @throws Exception -
	 *             SwtException
	 */
	public String saveContactDetails() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// AcctMaintenance
		AcctMaintenance acct = null;
		try {
			acct = getAcctMaintenance();



			// set the
			// accContactName,accPhoneNumber,accEmailAddr,parentFormRefresh into
			// request
			request.setAttribute("accContactName", acct.getAcctContactName());
			request.setAttribute("accPhoneNumber", acct.getAcctPhone());
			request.setAttribute("accEmailAddr", acct.getAcctEmailAddr());
			request.setAttribute("parentFormRefresh", "yes");
			return getView("displayContact");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'saveContactDetails' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "saveContactDetails", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify object
			acct = null;
		}
	}

	/**
	 *
	 * @desc - This method 'displayAccInterestRate' takes the collection for
	 *       'accInterestRatecollInSession' and set it in 'interestRateColl'
	 *       that is used for displaying of records on the AccInterestRate jsp
	 * @return ActionForward - ActionForward
	 * @throws Exception -
	 *             SwtException
	 */
	public String displayAccInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// screenStatus
		String screenStatus = null;
		// Interest Rate Collection
		Collection interestRateColl = null;
		String accountId = null;
		try {
			log.debug("Entering displayAccInterestRate method");
			// get the screenStatus from request
			screenStatus = request.getParameter("screenStatus");
			accountId = request.getParameter("relatedToaccountId");
			// get the interestRate Collection from session.

			if("add".equals(screenStatus)) {

				interestRateColl = (Collection) request.getSession().getAttribute(
						"accInterestRatecollInSession"+"*.*");

			}else {
				interestRateColl = (Collection) request.getSession().getAttribute(
						"accInterestRatecollInSession"+accountId);
			}
			// set the interestRateColl,screenStatus into request.
			request.setAttribute("interestRateColl", interestRateColl);
			request.setAttribute("screenStatus", screenStatus);
			log.debug("Exiting displayAccInterestRate method");
			return getView("displayAccInterestRate");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayAccInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"displayAccInterestRate",
							AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify object
			screenStatus = null;
			interestRateColl = null;
		}
	}

	/**
	 * This method 'changeAcctInterestRate' sets the parameters on the form to
	 * the request attribute which are used in the parent form to populate the
	 * corresponding hidden variables
	 * @return ActionForward - mapping.findForward("addIntRate");
	 * @throws Exception -
	 *             SwtException
	 */
	public String changeAcctInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// creditIntRate
		String creditIntRate = null;
		// overdraftIntRate
		String overdraftIntRate = null;
		// interestRateDate
		String interestRateDate = null;
		// AccountInterestRate
		AccountInterestRate accountInterestRate = null;
		try {
			log.debug("Entering changeAcctInterestRate method");
			// get the creditIntRate,overdraftIntRate,interestRateDate from
			// request
			creditIntRate = request.getParameter("creditIntRate");
			overdraftIntRate = request.getParameter("overdraftIntRate");
			interestRateDate = request.getParameter("interestRateDate");
			// Instantiate the AccountInterestRate
			accountInterestRate = new AccountInterestRate();
			// set the CreditRate,OverdraftRate,InterestDateRateAsString into
			// accountInterestRate bean

			accountInterestRate.setCreditRate(new String(creditIntRate));
			accountInterestRate.setOverdraftRate(new String(overdraftIntRate));

			accountInterestRate.setInterestDateRateAsString(interestRateDate);
			// set the
			// acctInterestRate,screenStatus,creditIntRate,creditIntRate1,overdraftIntRate1,method
			// into request
			setAcctInterestRate(accountInterestRate);
			request.setAttribute("screenStatus", "change");
			request.setAttribute("creditIntRate", creditIntRate);
			request.setAttribute("overdraftIntRate", overdraftIntRate);
			request.setAttribute("creditIntRate1", creditIntRate);
			request.setAttribute("overdraftIntRate1", overdraftIntRate);
			request.setAttribute("method", "change");
			log.debug("Exiting changeAcctInterestRate method");
			return getView("addIntRate");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'changeAcctInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"changeAcctInterestRate",
							AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			creditIntRate = null;
			overdraftIntRate = null;
			interestRateDate = null;
			accountInterestRate = null;
		}
	}

	/**
	 * This method sets the Parent Window's hidden variable with the saved
	 * PositionLevelNames Details for the Corresponding entity
	 * @return ActionForward - ActionForward
	 * @throws Exception -
	 *             SwtException
	 */
	public String updateAcctInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// SystemFormats
		SystemFormats sysformat = null;
		// SimpleDateFormat
		SimpleDateFormat sdf = null;
		// AccountInterestRate
		AccountInterestRate accountInterestRate = null;
		// AccInterestRate collection
		Collection accInterestRatecoll = null;
		// NewIntRate Collection
		ArrayList newIntRateColl = null;
		// AccountInterestRate
		AccountInterestRate accIntRate = null;
		// Iterator
		Iterator itr = null;
		String callerMethod = null;
		String accountId = null;
		try {
			log.debug("Entering updateAcctInterestRate method");

			callerMethod = request.getParameter("callerMethod");
			accountId = request.getParameter("relatedToaccountId");


			accountInterestRate = getAcctInterestRate();


			// get the account interest rate collection
			if("add".equals(callerMethod)) {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+"*.*");
			}else {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+accountId);
			}
			// Instantiate the new IntRate Collection
			newIntRateColl = new ArrayList();
			// Iterate the account interest rate collection
			itr = accInterestRatecoll.iterator();
			// Instantiate the AccountInterestRate
			accIntRate = new AccountInterestRate();
			while (itr.hasNext()) {
				// get the AccountInterestRate
				accIntRate = (AccountInterestRate) itr.next();
				// get the InterestDateRate
				String interestRateDate = accIntRate
						.getInterestDateRateAsString();
				if (interestRateDate.equals(accountInterestRate
						.getInterestDateRateAsString())) {
					// set the CreditRate
					accIntRate.setCreditRate(accountInterestRate
							.getCreditRate());
					// set the OverdraftRate
					accIntRate.setOverdraftRate(accountInterestRate
							.getOverdraftRate());
					// set the UpdateDate
					accIntRate.setUpdateDate(SwtUtil.getSystemDatewithTime());
					sysformat = SwtUtil.getCurrentSystemFormats(request
							.getSession());
					// Instantiate the SimpleDateFormat
					sdf = new SimpleDateFormat("HH:mm:ss");
					// set the UpdateDate
					accIntRate.setUpdateDateAsString(SwtUtil.formatDate(
							accIntRate.getUpdateDate(), sysformat
									.getDateFormatValue())
							+ " " + sdf.format(accIntRate.getUpdateDate()));
					// set the UpdateUser
					accIntRate.setUpdateUser(UserThreadLocalHolder.getUser());
				}
				newIntRateColl.add(accIntRate);
			}

			// set the parentFormRefresh,method into request
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("method", "change");
			// set the accInterestRatecollInSession into request session
			if("add".equals(callerMethod)) {
				request.getSession().setAttribute("accInterestRatecollInSession"+"*.*",
						newIntRateColl);
			}else {
				request.getSession().setAttribute("accInterestRatecollInSession"+accountId,
						newIntRateColl);
			}
			log.debug("Exiting updateAcctInterestRate method");
			return getView("addIntRate");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'updateAcctInterestRate' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'updateAcctInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"updateAcctInterestRate",
							AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			sysformat = null;
			sdf = null;
			accountInterestRate = null;
			accInterestRatecoll = null;
			newIntRateColl = null;
			accIntRate = null;
			itr = null;
		}
	}

	/**
	 * This function 'addAcctInterestRate' is basically used to open the jsp for
	 * addAcctInterestRate
	 * @return getView("addIntRate");
	 *
	 * @throws SwtException
	 */
	public String addAcctInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering addAcctInterestRate method");
			request.setAttribute("method", "add");
			log.debug("Exiting addAcctInterestRate method");
			return getView("addIntRate");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'addAcctInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "addAcctInterestRate", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		}
	}

	/**
	 * This function is basically used to update the
	 * accInterestRatecollInSession with the value entered on the accIntRateAdd
	 * jsp.
	 * @return getView("addIntRate");
	 * @throws SwtException
	 */
	public String saveAcctInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// AccountInterestRate
		AccountInterestRate accountInterestRate = null;
		// RateDateAsString
		String rateDateAsString = null;
		// accInterestRate collection
		Collection accInterestRatecoll = null;
		// AccountInterestRate
		AccountInterestRate accIntRate = null;
		// SystemFormats
		SystemFormats sysformat = null;
		// SimpleDateFormat
		SimpleDateFormat sdf = null;
		// AccountInterestRate
		AccountInterestRate accountInterestRateColl = null;
		// Iterator
		Iterator itr = null;
		// account list
		ArrayList acctList = null;
		// Date
		Date datetemp1 = null;
		String callerMethod = null;
		String relatedToaccountId = null;
		try {
			log.debug("Entering saveAcctInterestRate method");


			accountInterestRate = getAcctInterestRate();



			// get the InterestDateRate
			rateDateAsString = accountInterestRate
					.getInterestDateRateAsString();
			// get the account interest rate collection
			callerMethod = request.getParameter("callerMethod");
			relatedToaccountId = request.getParameter("relatedToaccountId");


			if("add".equals(callerMethod)) {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+"*.*");
			}else {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+relatedToaccountId);
			}
			// Instantiate the AccountInterestRate
			accIntRate = new AccountInterestRate();
			// set the CreditRate
			accIntRate.setCreditRate(accountInterestRate.getCreditRate());
			// set the OverdraftRate
			accIntRate.setOverdraftRate(accountInterestRate.getOverdraftRate());
			// set the InterestDateRate
			accIntRate.getId().setInterestDateRate(
					SwtUtil.parseDateGeneral(rateDateAsString));
			// set the UpdateDate
			accIntRate.setUpdateDate(SwtUtil.getSystemDatewithTime());
			// set the UpdateUser
			accIntRate.setUpdateUser(UserThreadLocalHolder.getUser());
			// get the currenct sysformat
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Instantiate the SimpleDateFormat
			sdf = new SimpleDateFormat("HH:mm:ss");
			// set the UpdateDate
			accIntRate.setUpdateDateAsString(SwtUtil.formatDate(accIntRate
					.getUpdateDate(), sysformat.getDateFormatValue())
					+ " " + sdf.format(accIntRate.getUpdateDate()));
			// Instantiate the AccountInterestRate
			accountInterestRateColl = new AccountInterestRate();
			// Iterate the account interest rate collection
			itr = accInterestRatecoll.iterator();
			// set the InterestDateRate
			accIntRate.setInterestDateRateAsString(rateDateAsString);
			while (itr.hasNext()) {
				// get the AccountInterestRate
				accountInterestRateColl = (AccountInterestRate) itr.next();
				// get the InterestDateRate
				datetemp1 = accountInterestRateColl.getId()
						.getInterestDateRate();
				String datetemp2 = null;
				if (sysformat.getDateFormatValue().equalsIgnoreCase(
						"MM/dd/yyyy")) {
					// get the formatted date
					datetemp2 = SwtUtil.formatDate(datetemp1, "MM/dd/yyyy");
				} else if (sysformat.getDateFormatValue().equalsIgnoreCase(
						"dd/MM/yyyy")) {
					// get the formatted date
					datetemp2 = SwtUtil.formatDate(datetemp1, "dd/MM/yyyy");
				}
				// set the dupstatus,method into request
				if ((accIntRate.getInterestDateRateAsString().equals(datetemp2))) {
					request.setAttribute("dupstatus", "true");
					request.setAttribute("method", "add");
					return getView("addIntRate");
				} else {
					request.setAttribute("dupstatus", "false");
				}
			}
			// Instantiate the account list
			acctList = new ArrayList();
			// add the AccountInterestRate
			acctList.add(accIntRate);
			// add account interest rate collection
			acctList.addAll(accInterestRatecoll);
			// set the accInterestRatecollInSession into request session
			if("add".equals(callerMethod)) {
				request.getSession().setAttribute("accInterestRatecollInSession"+"*.*",
						acctList);
			}else {
				request.getSession().setAttribute("accInterestRatecollInSession"+relatedToaccountId,
						acctList);
			}

			// set the parentFormRefresh,method into request
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("method", "change");
			log.debug("Exiting saveAcctInterestRate method");
			return getView("addIntRate");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in AcctMaintenanceAction.'saveAcctInterestRate' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in AcctMaintenanceAction.'saveAcctInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "saveAcctInterestRate", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			accountInterestRate = null;
			rateDateAsString = null;
			accInterestRatecoll = null;
			accIntRate = null;
			sysformat = null;
			sdf = null;
			accountInterestRateColl = null;
			itr = null;
			acctList = null;
			datetemp1 = null;
		}
	}

	/**
	 * This function is for deleting the selected record from the collection
	 * 'accInterestRatecollInSession'and update the parent screen calling this
	 * function
	 *
	 * @return getView("displayAccInterestRate");
	 *
	 * @throws SwtException
	 */
	public String deleteAcctInterestRate() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// creditIntRate
		String creditIntRate = null;
		// overdraftIntRate
		String overdraftIntRate = null;
		// interestRateDate
		String interestRateDate = null;
		// AccountInterestRate
		AccountInterestRate accountInterestRateColl = null;
		// AccountInterestRate
		AccountInterestRate accountInterestRate = null;
		// New Interest rate collection
		ArrayList<AccountInterestRate> newIntRateColl = null;
		// Account Interest rate collection
		Collection<AccountInterestRate> accInterestRatecoll = null;
		// Iterator
		Iterator<AccountInterestRate> iterator = null;
		String callerMethod = null;
		String accountId = null;
		try {
			log.debug("Entering deleteAcctInterestRate method");
			// get the creditInterestRate,overdraftInterestRate,interestRateDate
			// from request
			creditIntRate = request.getParameter("creditInterestRate");
			overdraftIntRate = request.getParameter("overdraftInterestRate");
			interestRateDate = request.getParameter("interestRateDate");

			callerMethod = request.getParameter("callerMethod");
			accountId = request.getParameter("relatedToaccountId");

			// Instantiate the AccountInterestRate
			accountInterestRate = new AccountInterestRate();
			// set the CreditRate
			accountInterestRate.setCreditRate(new String(creditIntRate));
			// set the OverdraftRate
			accountInterestRate.setOverdraftRate(new String(overdraftIntRate));
			// set the InterestDateRate
			accountInterestRate.setInterestDateRateAsString(interestRateDate);
			// Instantiate the AccountInterestRate
			accountInterestRateColl = new AccountInterestRate();
			// Instantiate the new IntRate Collection
			newIntRateColl = new ArrayList();
			// get the account interest rate collection
			if("add".equals(callerMethod)) {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+"*.*");
			}else {
				accInterestRatecoll = (Collection) request.getSession()
						.getAttribute("accInterestRatecollInSession"+accountId);
			}
			// Iterate the account interest rate collection
			iterator = accInterestRatecoll.iterator();
			while (iterator.hasNext()) {
				// get the AccountInterestRate
				accountInterestRateColl = iterator.next();
				if ((accountInterestRate.getInterestDateRateAsString()
						.equals(accountInterestRateColl
								.getInterestDateRateAsString()))) {
				} else {
					newIntRateColl.add(accountInterestRateColl);
				}
			}
			// set the parentFormRefresh,method into request
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("method", "change");
			// set the accInterestRatecollInSession into request session
			if("add".equals(callerMethod)) {
				request.getSession().setAttribute("accInterestRatecollInSession"+"*.*",
						newIntRateColl);
			}else {
				request.getSession().setAttribute("accInterestRatecollInSession"+accountId,
						newIntRateColl);
			}
			// set the interestRateColl into request
			request.setAttribute("interestRateColl", newIntRateColl);
			log.debug("Exiting deleteAcctInterestRate method");
			return getView("displayAccInterestRate");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'deleteAcctInterestRate' method : "
							+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteAcctInterestRate",
							AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			creditIntRate = null;
			overdraftIntRate = null;
			interestRateDate = null;
			accountInterestRateColl = null;
			accountInterestRate = null;
			newIntRateColl = null;
			accInterestRatecoll = null;
			iterator = null;
		}
	}

	/**
	 * This Method sets the Format details for the Account in the request.
	 *
	 * @param request -
	 *            HttpServletRequest Object
	 */
	private void putFormatDetailsInRequest(HttpServletRequest request) {
		log.debug("Entering putFormatDetailsInRequest method");

		request.setAttribute("newCrInternal", request
				.getParameter("acctNewCrInternal"));
		request.setAttribute("newCrExternal", request
				.getParameter("acctNewCrExternal"));
		request.setAttribute("newDrInternal", request
				.getParameter("acctNewDrInternal"));
		request.setAttribute("newDrExternal", request
				.getParameter("acctNewDrExternal"));

		request.setAttribute("CrExternalInt", request
				.getParameter("creditExternalInter"));
		request.setAttribute("DdExternalInt", request
				.getParameter("debitExternalInter"));

		log.debug("Exiting putFormatDetailsInRequest method");
	}

	/**
	 * This will set the Contact details in the acct - AcctMaintenance Object
	 *
	 * @param request -
	 *            HttpServletRequest Object
	 * @param acct -
	 *            AcctMaintenance Object
	 *
	 */
	private void setAcctContactDetails(HttpServletRequest request,
									   AcctMaintenance acct) {
		// Contact Name
		String contactName = null;
		// Phone
		String phone = null;
		// Email
		String email = null;
		try {
			log.debug("Entering setAcctContactDetails method");
			// get the contactName,contactPhoneNumber,contactEmailAddr from
			// request
			contactName = request.getParameter("contactName");
			phone = request.getParameter("contactPhoneNumber");
			email = request.getParameter("contactEmailAddr");
			// set the AcctContactName,AcctPhone,AcctEmailAddr into acct bean.
			if (contactName.length() > 0) {
				acct.setAcctContactName(contactName);
			}
			if (phone.length() > 0) {
				acct.setAcctPhone(phone);
			}
			if (email.length() > 0) {
				acct.setAcctEmailAddr(email);
			}
			log.debug("Exiting setAcctContactDetails method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [setAcctContactDetails] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			contactName = null;
			phone = null;
			email = null;
		}
	}

	/**
	 * This method is used across this action class to add the filter,sort
	 * status into request
	 *
	 * @param request -
	 *            HttpServletRequest
	 */
	private void setFilterSortInReq(HttpServletRequest request) {
		// Selected FilterStatus
		String selectedFilterStatus = null;
		// Selected SortStatus
		String selectedSortStatus = null;
		// Selected SortDescending
		String selectedSortDescending = null;
		// StringTokenizer
		StringTokenizer strtokens = null;
		// Filter StatusFlag
		boolean filterStatusFlag = false;
		try {
			// get the selectedFilterStatus from request
			selectedFilterStatus = request.getParameter("selectedFilterStatus");
			// selectedFilterStatus is null then set empty string
			if (selectedFilterStatus == null) {
				selectedFilterStatus = SwtConstants.EMPTY_STRING;
			}
			// get the selectedSortStatus from request
			selectedSortStatus = request.getParameter("selectedSortStatus");
			// selectedSortStatus is null then set empty string
			if (selectedSortStatus == null) {
				selectedSortStatus = SwtConstants.EMPTY_STRING;
			}
			// get the selectedSortDescending from request
			selectedSortDescending = request
					.getParameter("selectedSortDescending");
			// selectedSortDescending is null then set empty string
			if (selectedSortDescending == null) {
				selectedSortDescending = SwtConstants.EMPTY_STRING;
			}
			// Instanitiate the StringTokenizer
			strtokens = new StringTokenizer(selectedFilterStatus, ",");
			filterStatusFlag = false;
			while (strtokens.hasMoreTokens()) {
				String nextToken = strtokens.nextToken();
				if (!nextToken.equals("special_all")
						&& !nextToken.equals("undefined")) {
					filterStatusFlag = true;
					break;
				}
			}
			// filterStatusFlag is false then set selectedFilterStatus to empty
			// string
			if (!filterStatusFlag) {
				selectedFilterStatus = SwtConstants.EMPTY_STRING;
			}
			// set the filterStatus,sortStatus,sortDescending into request
			request.setAttribute("filterStatus", selectedFilterStatus);
			request.setAttribute("sortStatus", selectedSortStatus);
			request.setAttribute("sortDescending", selectedSortDescending);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [setFilterSortInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// nullify objects
			selectedFilterStatus = null;
			selectedSortStatus = null;
			selectedSortDescending = null;
			strtokens = null;
		}
	}

	/**
	 * This method is used to get the intermediary record.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getIntermediaryRecord() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Intermediary Bic
		String intermediaryBic = null;
		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Acctbiccode
		String acctbiccode = null;
		// Collection of IntermediaryRecord
		Collection sweepInter = null;
		// Iterator
		Iterator itrswp = null;
		try {
			log.debug("Entering 'getIntermediaryRecord' method ");
			// get the entityId,currencyCode,acctbiccode from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			acctbiccode = request.getParameter("acctbiccode");
			if ((!SwtUtil.isEmptyOrNull(entityId))
					&& (!SwtUtil.isEmptyOrNull(acctbiccode))
					&& (!SwtUtil.isEmptyOrNull(currencyCode))) {
				// get the collection of the IntermediaryRecord
				sweepInter = acctMaintenanceManager.getIntermediaryRecord(
						SwtUtil.getCurrentHostId(), entityId, currencyCode,
						acctbiccode);
				// iterate the collection
				itrswp = sweepInter.iterator();
				while (itrswp.hasNext()) {
					// get the SweepIntermediaries
					SweepIntermediaries swpInter = (SweepIntermediaries) (itrswp
							.next());
					// get the Intermediary
					intermediaryBic = swpInter.getIntermediary();
				}
			}
			// write the intermediaryBic into response
			response.getWriter().print(intermediaryBic);
			log.debug("exiting 'getIntermediaryRecord' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getIntermediaryRecord' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getIntermediaryRecord' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getIntermediaryRecord' method : "
							+ exp.getMessage());

		}
		return null;
	}

	/**
	 *
	 * This function returns collection of currencies
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - collection of currencies
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request,
									   String hostId, String entityId) throws SwtException {
		/* Method's local variable declaration */
		// Role Id
		String roleId = "";
		// Currency List
		ArrayList currencyList = null;
		// Currrency List with All option
		Collection currrencyListWithAll = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			/* Returns the currency Access List based on the Role */
			currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcessLVL(roleId, entityId);
			/* Check for currency List not NULL */
			if (currencyList != null) {
				/*
				 * Removes the LabelValueBean object for the Key as 'Default'
				 * from the collection
				 */
				currencyList.remove(new LabelValueBean("Default", "*"));
				/* Assigning the new ArrayList object to a new Collection Object */
				currrencyListWithAll = new ArrayList();
				/*
				 * Adding a new LabelValueBean object with the Key as 'ALL' and
				 * value as 'ALL'
				 */
				currrencyListWithAll.add(new LabelValueBean(
						SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));

				/* Adding the currencyList object to collection object */
				currrencyListWithAll.addAll(currencyList);
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyList ] - Exit ");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyList] - Exception -" + exp.getMessage());
		} finally {
			// nullify objects
			roleId = null;
			currencyList = null;
		}
		return currrencyListWithAll;
	}

	/**
	 * This method is used to get the link account list
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getLinkAccountList() throws SwtException {
		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Account ID
		String currentAccountId = null;
		// linkAccount List
		ArrayList linkAccountList = null;
		// Response Text
		String responseText = "";
		// Iterator
		Iterator iterator = null;
		boolean isLinked= false;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'getLinkAccountList' method ");
			// get the entityId,currencyCode from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			currentAccountId= request.getParameter("currentAccountId");
			isLinked=  acctMaintenanceManager
					.checkAccountLinkedList(currentAccountId, entityId);

			// Instantiate the linkAccountList
			linkAccountList = new ArrayList();
			if(!isLinked) {
				// get the linkAccountList collection
				linkAccountList = (ArrayList) acctMaintenanceManager
						.getLinkAccountList(putHostIdListInReq(request), entityId,
								currencyCode);
				// Iterate the collection
				iterator = linkAccountList.iterator();
				while (iterator.hasNext()) {
					// get the LabelValueBean
					LabelValueBean lBean = (LabelValueBean) iterator.next();
					// add the label and value.
					responseText = responseText + lBean.getLabel() + "~~~"
							+ lBean.getValue() + "\n";
				}

			}else {
				responseText = null	;
			}
			// write the linkAccountList into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getLinkAccountList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getLinkAccountList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getLinkAccountList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getLinkAccountList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			linkAccountList = null;
			responseText = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * This method is used to get the main account list
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getMainAccountList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Response Text
		String responseText = "";
		// Main Account list
		Collection collMainAcctList = null;
		// Iterator
		Iterator iterator = null;
		try {
			log.debug("Entering 'getMainAccountList' method ");
			// get the entityId,currencyCode from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			// get the mainAccountList
			collMainAcctList = acctMaintenanceManager.getMainAcctListByCurr(
					putHostIdListInReq(request), entityId, currencyCode,
					new SystemInfo(), SwtUtil.getCurrentSystemFormats(request
							.getSession()));
			// Iterate the collection
			iterator = collMainAcctList.iterator();
			while (iterator.hasNext()) {
				// get the LabelValueBean
				LabelValueBean lBean = (LabelValueBean) iterator.next();
				// add the label and value.
				responseText = responseText + lBean.getLabel() + "~~~"
						+ lBean.getValue() + "\n";
			}
			// write the main account list into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getMainAccountList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getMainAccountList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getMainAccountList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getMainAccountList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			responseText = null;
			collMainAcctList = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * This method is used to get the book code list
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getBookCodeList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Entity Id
		String entityId = null;
		// Response Text
		String responseText = "";
		// Collection BookList
		Collection collBookList = null;
		// Iterator
		Iterator iterator = null;
		try {
			log.debug("Entering 'getBookCodeList' method ");
			// get the entityId from request
			entityId = request.getParameter("entityId");
			// get the collection of book list
			collBookList = acctMaintenanceManager.getBookListColl(
					putHostIdListInReq(request), entityId, new SystemInfo(),
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			// Iterate the book code collection
			iterator = collBookList.iterator();
			while (iterator.hasNext()) {
				// get the label value bean
				LabelValueBean lBean = (LabelValueBean) iterator.next();
				// add the label and value
				responseText = responseText + lBean.getLabel() + "~~~"
						+ lBean.getValue() + "\n";
			}
			// write the responseText into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getBookCodeList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getBookCodeList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			entityId = null;
			responseText = null;
			collBookList = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * This method is used to get the country list
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getCountryList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Response Text
		String responseText = "";
		// Country collection
		Collection countryColl = null;
		// Iterator
		Iterator iterator = null;
		try {
			log.debug("Entering 'getCountryList' method ");
			// get the collection of country list
			countryColl = acctMaintenanceManager.getCountryList();
			// Iterate the country collection
			iterator = countryColl.iterator();
			responseText = "~~~" + "\n";
			while (iterator.hasNext()) {
				// get the country
				Country country = (Country) iterator.next();
				// add the country name and code
				responseText = responseText + country.getCountryName() + "~~~"
						+ country.getCountryCode() + "\n";
			}
			// write the responseText into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getCountryList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getCountryList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getCountryList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getCountryList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			responseText = null;
			countryColl = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * This method is used to get the format list
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getFormatList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Response text for format
		String responseText = "";
		// format collection
		Collection collFormatList = null;
		// CurrencyManager
		CurrencyManager currencyManagerObj = null;
		// CurrencyDetailVO
		CurrencyDetailVO currencyDetailVOObj = null;
		// ArrayList
		ArrayList currencyList = null;
		// Entity Id
		String entityId = null;
		// Role Id
		String roleId = null;
		// Iterator
		Iterator iterator = null;
		try {
			log.debug("Entering 'getFormatList' method ");
			// get the entity id
			entityId = request.getParameter("entityId");
			// get the collection of format list
			collFormatList = acctMaintenanceManager.getFormatListColl(
					putHostIdListInReq(request), entityId, new SystemInfo(),
					SwtUtil.getCurrentSystemFormats(request.getSession()));
			// Iterate the format collection
			iterator = collFormatList.iterator();
			while (iterator.hasNext()) {
				// get the LabelValueBean
				LabelValueBean lBean = (LabelValueBean) iterator.next();
				// add the label and value
				responseText = responseText + lBean.getLabel() + "~~~"
						+ lBean.getValue() + "\n";
			}
			// write the responseText into response
			response.getWriter().print(responseText);
			log.debug("Exiting 'getFormatList' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getFormatList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getFormatList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getFormatList' method : "
							+ exp.getMessage());

		} finally {
			// nullify objects
			responseText = null;
			collFormatList = null;
			currencyManagerObj = null;
			currencyDetailVOObj = null;
			currencyList = null;
			entityId = null;
			roleId = null;
			iterator = null;
		}
		return null;
	}

	/**
	 * This Method sets the account display details for the Account in the
	 * request.
	 *
	 * @param request -
	 *            HttpServletRequest Object
	 */
	private void putAcctDisplayDtInReq(HttpServletRequest request) {
		log.debug("Entering putAcctDisplayDtInReq method");
		// get the country name from the request and set the same
		if (!SwtUtil.isEmptyOrNull(request.getParameter("countryName"))) {
			request.setAttribute("countryName", request
					.getParameter("countryName"));
		}
		// get the currency name from the request and set the same
		if (!SwtUtil.isEmptyOrNull(request.getParameter("currencyName"))) {
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));
		}
		// get the mainaccount name from the request and set the same
		if (!SwtUtil.isEmptyOrNull(request.getParameter("mainAcctName"))) {
			request.setAttribute("mainAcctName", request
					.getParameter("mainAcctName"));
		}
		// get the linkaccount name from the request and set the same
		if (!SwtUtil.isEmptyOrNull(request.getParameter("linkAcctName"))) {
			request.setAttribute("linkAcctName", request
					.getParameter("linkAcctName"));
		}
		// get the bookcode name from the request and set the same
		if (!SwtUtil.isEmptyOrNull(request.getParameter("bookCodeName"))) {
			request.setAttribute("bookCodeName", request
					.getParameter("bookCodeName"));
		}
		log.debug("Exiting putAcctDisplayDtInReq method");
	}

	/*
	 * Start:Code added For Mantis 1592 by Sudhakar on 22-12-2011:Account
	 * Maintenance screen allows to create account for entity that has no
	 * currency access
	 */
	/**
	 * This method is used to get the account list based on currency code
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getCopyFromAccountList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// String Variable to hold the entityId
		String entityId = null;
		// String Variable to hold the hostId
		String hostId = null;
		// String Variable to hold the currencyCode
		String currencyCode = null;
		// Variable to hold the AccountList object
		ArrayList<LabelValueBean> accountList = null;
		// String Variable to hold the responseText
		StringBuffer responseText = null;
		// Variable to hold the iterator object
		Iterator<LabelValueBean> itrAccountList = null;
		// Variable to hold the accountID object
		LabelValueBean accountID = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getCopyFromAccountList] - Entering ");
			// get the entityId,currencyCode from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			// Assign responseText as empty
			responseText = new StringBuffer("");
			// get the hostId
			hostId = putHostIdListInReq(request);
			// get the AccountList collection
			accountList = (ArrayList) acctMaintenanceManager
					.getAccountIDDropDownForCopy(hostId, entityId, currencyCode);
			// Iterate the collection
			itrAccountList = accountList.iterator();
			while (itrAccountList.hasNext()) {
				// get the LabelValueBean
				accountID = itrAccountList.next();
				// add the label and value.
				responseText.append(accountID.getLabel()).append("~~~").append(
						accountID.getValue()).append("\n");
			}
			// write the linkAccountList into response
			response.getWriter().print(responseText);
			log.debug(this.getClass().getName()
					+ "- [getCopyFromAccountList] - Exit");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'getCopyFromAccountList' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getCopyFromAccountList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;

		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getCopyFromAccountList' method : "
							+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			hostId = null;
			accountList = null;
			responseText = null;
			itrAccountList = null;
			accountID = null;
		}
		return null;
	}

	/**
	 *
	 * This function returns the collection of LabelValueBean objects for a role
	 * id and entity id which have the full access.
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - currrencyList
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {

		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyMap object
		Map<String, String> currencyMap = null;
		// Variable to hold the currrencyList object
		Collection<LabelValueBean> currrencyList = null;
		// Variable to hold the itrCurrencyKey object
		Iterator<String> itrCurrencyKey = null;
		// String Variable to hold the currencyId
		String currencyId = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currrencyList = new ArrayList<LabelValueBean>();
			/* Returns the currency Access List based on the Role */
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);
			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					currencyId = itrCurrencyKey.next();

					// add labelvaluebean for currency id
					currrencyList.add(new LabelValueBean((String) currencyMap
							.get(currencyId), currencyId));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Exit ");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'getCurrencyFullAccessList' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getCurrencyFullAccessList' method : "
							+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyMap = null;
			itrCurrencyKey = null;
			currencyId = null;
		}
		return currrencyList;
	}
	/*
	 * End:Code added For Mantis 1592 by Sudhakar on 22-12-2011:Account
	 * Maintenance screen allows to create account for entity that has no
	 * currency access
	 */

	/**
	 * This method is used to check if a given party ID exists.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkIfPartyIdExists() throws SwtException {

		String entityId = null;
		String partyId = null;
		String partyIdExists = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'checkIfPartyIdExists' method ");

			entityId = request.getParameter("entityId");
			partyId = request.getParameter("partyId");

			partyIdExists = acctMaintenanceManager.checkIfPartyIdExists(
					SwtUtil.getCurrentHostId(), entityId, partyId);
			// write the partyIdExists into response
			response.getWriter().print(partyIdExists);
			log.debug("exiting 'checkIfPartyIdExists' method");
		} catch (IOException ioException) {
			log
					.error("IOException Catch in AcctMaintenanceAction.'checkIfPartyIdExists' method : "
							+ ioException.getMessage());
			throw new SwtException(ioException.getMessage());
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'checkIfPartyIdExists' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'checkIfPartyIdExists' method : "
							+ exp.getMessage());

		}
		return null;
	}

	public String getPartyName(String hostId,
							   String entityId, String partyId) {

		String partyDesc = null;
		// collection of Party list
		Collection<Party> collParty = null;
		// Iterate the party list
		Iterator<Party> itrParty = null;

		try {
			collParty = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getMatchingPartyRecord(hostId, entityId, partyId);

			if (collParty != null) {
				itrParty = collParty.iterator();
				while (itrParty.hasNext()) {
					partyDesc = itrParty.next()
							.getPartyName();
					break;
				}
			}

		} catch (SwtException e) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'putPartyNameInRequest' method : "
							+ e.getMessage());
		}
		return partyDesc;
	}

	public String getPartyNameForAjax() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String entityId = null;
		String partyId = null;
		String partyDesc = null;

		try {
			log.debug("Entering 'getPartyNameForAjax' method ");

			entityId = request.getParameter("entityId");
			partyId = request.getParameter("partyId");

			partyDesc = getPartyName(SwtUtil.getCurrentHostId(), entityId, partyId);
			// write the partyDesc into response
			response.getWriter().print(partyDesc);
			log.debug("exiting 'getPartyNameForAjax' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'getPartyNameForAjax' method : "
							+ exp.getMessage());

		}
		return null;
	}

	/**
	 * This method is called the the user is changing the "Archvie Data" checkbox to indicate
	 * that deletion without archiving is required
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String checkAccountIlmDataMember()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the acctMaintenance object
		AcctMaintenance acctMaintenance = null;
		// String Variable to hold the hostId
		String hostId = null;
		// String Variable to hold the entityId
		String entityId = null;
		// String Variable to hold the entityId
		String currency = null;
		// String Variable to hold the entityId
		String account = null;
		String resultCheck = null;

		try {
			log.debug(this.getClass().getName() + "- [checkAccountIlmDataMember] - Entering ");

			// get the entityId
			entityId = request.getParameter("selectedEntityName");
			// get the entityId
			currency = request.getParameter("selectedCurrencyCode");
			// get the entityId
			account = request.getParameter("selectedAccountId");
			// get the hostId
			hostId = putHostIdListInReq(request);

			// check if the account is a member of an ILM group
			resultCheck = acctMaintenanceManager.checkAccountIlmDataMember(
					hostId, entityId, currency, account);
			response.getWriter().print(resultCheck);
			log.debug(this.getClass().getName() + "- [checkAccountIlmDataMember] - Exit ");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'checkAccountIlmDataMember' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		}catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'checkAccountIlmDataMember' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkAccountIlmDataMember", AcctMaintenanceAction.class), request, "");
		} finally {
			// nullify objects
			entityId = null;
			hostId = null;
			currency = null;
			account = null;
			resultCheck = null;
		}
		return null;
	}



	/**
	 * this method open specific format screen
	 * @return
	 * @throws SwtException
	 */
	public String handleAcctSweepBalGrp()
			throws SwtException {

		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String methodName = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		//get passed params
		entityId = request.getParameter("entityId");
		currencyCode = request.getParameter("currencyCode");
		accountId = request.getParameter("accountId");
		methodName = request.getParameter("methodName");

		//send in request the passed params
		request.setAttribute("entityId", entityId);
		request.setAttribute("currencyCode", currencyCode);
		request.setAttribute("menuAccessId", request
				.getParameter("menuAccessId"));
		request.setAttribute("accountId", accountId);
		request.setAttribute("methodName", methodName);
		return getView("acctsweepbalgrp");
	}

	/**
	 * This method is used to load the pre-advice input screen and it will be
	 * invoked while changing entity on screen to get the corresponding details.
	 * @return
	 * @throws SwtException
	 */
	public String displayAcctSweepBalGrp()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the role for current user
		String roleId = null;
		// to hold entity id
		String entityId = null;
		// to hold currency code
		String currencyCode = null;
		// to hold account id
		String accountId = null;
		// to hold defaultCurrency
		String defaultCurrency = null;
		//to hold acct sweep list from session
		//String acctSweepCollFromSession = null;
		Collection acctSweepCollFromSession = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String parentScreen = null;
		AccountSweepBalanceGroup  accountSweepBalanceGroup = null;
		Collection<AccountSweepBalanceGroup> listRecords = new ArrayList<AccountSweepBalanceGroup>();
		try {
			log.debug(this.getClass().getName() + " - [displayAcctSweepBalGrp()] - Entry");

			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();

			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			entityId= request.getParameter("entityId");
			currencyCode= request.getParameter("currencyCode");
			accountId= request.getParameter("accountId");
			parentScreen = request.getParameter("parentScreen");
			//get the list of records saved in  session
			if("add".equals(parentScreen)) {
				acctSweepCollFromSession= (Collection) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+"*.*");
			}else {
				acctSweepCollFromSession= (Collection) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+accountId);
			}

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.ACCT_SWEEP_BAL_GRP);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);


			/***** Account list Combo Start ***********/

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection accountList = acctMaintenanceManager
					.getLinkAccountFullList(CacheManager.getInstance().getHostId(), entityId, currencyCode);
			Iterator j = accountList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if (!row.getValue().equalsIgnoreCase("")) {
					lstOptions.add(new OptionInfo(row.getValue(), row.getValue(), false));
				}
			}
			lstSelect.add(new SelectInfo("listSweepAccts", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* acctCcyMaintPeriodLogGrid ******/
			responseConstructor.formGridStart("acctSweepBalGrpGrid");
			responseConstructor.formColumn(getAcctSweepBalGrpGridColumns(width, columnOrder, hiddenColumns, request));
			AcctMaintenanceManager	acctManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			if(acctSweepCollFromSession != null) {
				//JSONArray mvtJSONArray = new JSONArray(acctSweepCollFromSession);
				// form rows (records)
				responseConstructor.formRowsStart(acctSweepCollFromSession.size());
				Iterator iterator= acctSweepCollFromSession.iterator();
				while (iterator.hasNext()) {

					accountSweepBalanceGroup = (AccountSweepBalanceGroup)iterator.next();
					responseConstructor.formRowStart();

					responseConstructor.createRowElement(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME, accountSweepBalanceGroup.getId().getEntityId());
					responseConstructor.createRowElement(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME,accountSweepBalanceGroup.getId().getAccountId() );
					responseConstructor.createRowElement(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME, accountSweepBalanceGroup.getId().getSweepAccountId());
					responseConstructor.createRowElement(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_STATUS_TAGNAME ,  acctManager.getAccountStatus(accountSweepBalanceGroup.getId().getHostId(), accountSweepBalanceGroup.getId().getEntityId(),accountSweepBalanceGroup.getId().getSweepAccountId()));
					responseConstructor.formRowEnd();
				}
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			xmlWriter.endElement(SwtConstants.ACCT_SWEEP_BAL_GRP);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end

			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [displayAcctSweepBalGrp()] - Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'displayAcctSweepBalGrp' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayAcctSweepBalGrp' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayAcctSweepBalGrp", AcctMaintenanceAction.class), request, "");
			return getView("fail");
		} finally {
			/* null the objects created already. */
			responseHandler = null;
			responseConstructor = null;
			xmlWriter = null;
			hostId = null;
			roleId = null;
		}
	}



	private List<ColumnInfo> getAcctSweepBalGrpGridColumns(String width, String columnOrder, String hiddenColumns,
														   HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAcctSweepBalGrpGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME + "=200" + ","
						+ SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME + "=200" + ","
						+ SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME + "=200" ;
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME + ","
						+ SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME + ","
						+ SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// Entity Id column
				if (order.equals(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_HEADING, request),
							SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_ENTITY_HEADING_TOOLTIP, request));
					tmpColumnInfo.setEditable(false);
					lstColumns.add(tmpColumnInfo);
				}
				// Account Id column
				if (order.equals(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_HEADING, request),
							SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_ACCOUNT_HEADING_TOOLTIP, request));
					tmpColumnInfo.setEditable(false);
					lstColumns.add(tmpColumnInfo);
				}
				// Sweep Account Id column
				if (order.equals(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_HEADING, request),
							SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME, SwtConstants.COLUMN_TYPE_COMBO, 2,
							Integer.parseInt(widths.get(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_HEADING_TOOLTIP, request));
					tmpColumnInfo.setEditable(false);
					tmpColumnInfo.setMaxChars("40");
					tmpColumnInfo.setDataProvider("listSweepAccts");
					lstColumns.add(tmpColumnInfo);
				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getAcctSweepBalGrpGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getAcctSweepBalGrpGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getAcctSweepBalGrpGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	public String closeAcctSweepScreen() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String data = null;
		data = request.getParameter("data");
		// send in request the passed params
		return getView("data");
	}


	/**
	 * Used to add a new attribute header
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String saveUpdateAccountSweepBalGrp() throws SwtException {		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		AccountSweepBalanceGroup acctSweepBalGrp = null;
		ActionErrors errors = null;
		String entityId = null;
		String accountId = null;
		String newSweepAccountId = null;
		String oldSweepAccountId = null;
		String seqNumber = null;
		String methodName = null;
		String hostId = null;
		String updateUser=null;
		String sweepAcct= null;
		String deletedAccount= null;
		String parentScreen = null;
		int index=0;
		Collection acctSweepBalGrpList = null;
		try {
			log.debug(this.getClass().getName() + " method [saveUpdateAccountSweepBalGrp] - Enter ");
			errors = new ActionErrors();
			updateUser = SwtUtil.getCurrentUserId(request.getSession());
			hostId = SwtUtil.getCurrentHostId();

			//Get values from request
			entityId = request.getParameter("entityId");
			accountId=  request.getParameter("accountId");
			oldSweepAccountId = request.getParameter("oldSweepAccountId");
			newSweepAccountId = request.getParameter("newSweepAccountId");
			deletedAccount = request.getParameter("deletedAccount");
			methodName = request.getParameter("calledFrom");
			parentScreen = request.getParameter("parentScreen");
			if("add".equals(parentScreen)) {
				acctSweepBalGrpList = (Collection) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+"*.*");
			}else {
				acctSweepBalGrpList = (Collection) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+accountId);
			}

			if (acctSweepBalGrpList.size()>0) {
				Comparator<AccountSweepBalanceGroup> comparator = Comparator.comparing( AccountSweepBalanceGroup::getUniqueId );
				AccountSweepBalanceGroup maxObject =  (AccountSweepBalanceGroup) acctSweepBalGrpList.stream().max(comparator).get();
				index=maxObject.getUniqueId()+1;
			}

			if("add".equalsIgnoreCase(methodName)) {
				acctSweepBalGrp = new AccountSweepBalanceGroup();
				acctSweepBalGrp.getId().setHostId(hostId);
				acctSweepBalGrp.getId().setAccountId(accountId);
				acctSweepBalGrp.getId().setEntityId(entityId);
				acctSweepBalGrp.getId().setSweepAccountId(newSweepAccountId);
				acctSweepBalGrp.setUniqueId(index);
				acctSweepBalGrp.setValid("false");
				acctSweepBalGrpList.add(acctSweepBalGrp);

			}else if("delete".equalsIgnoreCase(methodName)) {
				Iterator iterator = acctSweepBalGrpList.iterator();
				while (iterator.hasNext()) {
					acctSweepBalGrp = (AccountSweepBalanceGroup) iterator.next();
					if(deletedAccount.equalsIgnoreCase(acctSweepBalGrp.getId().getSweepAccountId())) {
						iterator.remove();
					}

				}
			}else {
				Iterator iterator = acctSweepBalGrpList.iterator();
				while (iterator.hasNext()) {
					acctSweepBalGrp = (AccountSweepBalanceGroup) iterator.next();
					if(oldSweepAccountId.equalsIgnoreCase(acctSweepBalGrp.getId().getSweepAccountId())) {
						acctSweepBalGrp.getId().setSweepAccountId(newSweepAccountId);
						acctSweepBalGrp.setUniqueId(index);
						acctSweepBalGrp.setValid("true");
					}

				}

			}

			String acctSweepListJson = new Gson().toJson(acctSweepBalGrpList );


			if("add".equals(parentScreen)) {
				request.getSession().setAttribute("acctSweepBalGrpCollInSession"+"*.*",
						acctSweepBalGrpList);
				request.setAttribute("acctSweepBalGrpCollInSessionJson"+"*.*", acctSweepListJson);
			}else {
				request.getSession().setAttribute("acctSweepBalGrpCollInSession"+accountId,
						acctSweepBalGrpList);
				request.setAttribute("acctSweepBalGrpCollInSessionJson"+accountId, acctSweepListJson);
			}

			//String acctSweepListJson = new Gson().toJson(acctSweepBalGrpList );
			//request.setAttribute("acctSweepBalGrpCollInSessionJson", acctSweepListJson);

			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");

			log.debug(this.getClass().getName() + " method [saveUpdateAccountSweepBalGrp] - Exit ");

		}catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveUpdateAccountSweepBalGrp] method : - "
					+ swtExp.getMessage());
			saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			if (swtExp.getErrorCode().equals(
					"errors.saveUpdateAccountScheduleSweep")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtExp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveUpdateAccountSweepBalGrp] method swt : - "
						+ swtExp.getMessage());
				saveErrors(request, SwtUtil.logException(swtExp, request, ""));
			}

			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtExp.getMessage());
			request.setAttribute("reply_location",swtExp.getStackTrace()[0].getClassName() + "."
					+ swtExp.getStackTrace()[0].getMethodName() + ":"
					+ swtExp.getStackTrace()[0].getLineNumber());
			return getView("dataerror");

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [saveUpdateAccountSweepBalGrp] - Exception -"
					+ e.getStackTrace()[0].getClassName() + "."
					+ e.getStackTrace()[0].getMethodName() + ":"
					+ e.getStackTrace()[0].getLineNumber() + " "
					+ e.getMessage());
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());

			return getView("dataerror");
		}

		return getView("statechange");
	}

	/**
	 * This is used to get specific account sweep size
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String getAssAcctSweepBalGrpSize() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration and initialization */
		String size ="0";
		Collection<AccountSpecificSweepFormat>  specificFormatList = null;
		HashMap<String, Collection<AccountSpecificSweepFormat>> allStoredSpecificInrequest = null;
		String accountId =  null;
		String parentMethodName = null;
		try {
			log.debug(this.getClass().getName() + " - [getAssAcctSweepBalGrpSize] - " + "Entry");
			accountId = request.getParameter("accountId");
			parentMethodName = request.getParameter("parentMethodName");
			List<AccountSweepBalanceGroup> collAcctSweepBalGrp = null;

			if("add".equals(parentMethodName)) {
				collAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+"*.*");
			}else {
				collAcctSweepBalGrp = (ArrayList<AccountSweepBalanceGroup>) request.getSession()
						.getAttribute("acctSweepBalGrpCollInSession"+accountId);
			}

			//delete invalid records from the collAcctSweepBalGrp list
			collAcctSweepBalGrp = collAcctSweepBalGrp.stream().filter(item -> "true".equals(item.getValid())).collect(Collectors.toList());
			if("add".equals(parentMethodName)) {
				request.getSession().setAttribute("acctSweepBalGrpCollInSession"+"*.*", collAcctSweepBalGrp);

			}else {
				request.getSession().setAttribute("acctSweepBalGrpCollInSession"+accountId, collAcctSweepBalGrp);
			}

			if(collAcctSweepBalGrp != null)
				size = ""+collAcctSweepBalGrp.size();
			response.getWriter().print(size);
			log.debug(this.getClass().getName() + " - [getAssAcctSweepBalGrpSize] - " + "Exit");
		} catch (Exception exp) {

			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "getAssAcctSweepBalGrpSize", AcctMaintenanceAction.class), request, "");
		}

		return null;
	}




	public String checkAcctsAutoSwpFlag() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		ResponseHandler responseHandler = null;

		// To hold the array of accounts
		String acctList= null;
		// Variables Declaration for acct
		AcctMaintenance acct = null;
		String accountId= null;
		String hostId= null;
		String entityId= null;
		// Variable to hold the systemInfo object
		SystemInfo systemInfo = null;
		List<String>  accounts = null;
		String listAccts = null;
		try {
			// debug message
			log.debug(this.getClass().getName()
					+ " - [ checkAcctsAutoSwpFlag ] - Entry");
			StringBuilder listAcctsB=new StringBuilder();
			// Instantiate the SystemInfo
			systemInfo = new SystemInfo();
			//get the array of EOD sweeping grid accounts from request
			acctList = request.getParameter("acctList");

			entityId = request.getParameter("entityId");
			// get the hostId
			hostId = putHostIdListInReq(request);
			if(!SwtUtil.isEmptyOrNull(acctList)) {
				accounts = new ArrayList<String>();

				for (int i = 0; i < (acctList.split(",")).length; i++) {

					accountId= acctList.split(",")[i];
					// get the account details
					acct = acctMaintenanceManager.getEditableDataDetailList(entityId,
							hostId, accountId, systemInfo, SwtUtil
									.getCurrentSystemFormats(request.getSession()));
					if("N".equalsIgnoreCase(acct.getAutoswpswitch())) {
						//accounts.add(accountId);
						listAccts=listAcctsB.append(accountId).append(",").toString();
					}
				}
			}
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("acctsList", listAccts);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [checkAcctsAutoSwpFlag()] - Exit");

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'checkAcctsAutoSwpFlag' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'checkAcctsAutoSwpFlag' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getLists", AcctMaintenanceAction.class),
					request, "");
		} finally {
			responseConstructor = null;
			xmlWriter=null;
			log.debug(this.getClass().getName() + "- [checkAcctsAutoSwpFlag] - Exit");
		}
		return null;

	}


	public String getUpdatedLists() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Entity Id
		String entityId = null;
		// Currency Id
		String currencyCode = null;
		String accountId = null;
		String sweepEntityId = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		try {
			log.debug(this.getClass().getName() + " - [getUpdatedLists] - Entering");
			// to get the host id for application
			//hostId = CacheManager.getInstance().getHostId();
			// get the entityId from request
			sweepEntityId= request.getParameter("sweepEntityId");
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			accountId = request.getParameter("accountId");
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** Book list Combo Start ***********/
			MovementSearchManager movementsearchManager = (MovementSearchManager) (SwtUtil
					.getBean("movementsearchManager"));
			// Populating the bookcode combo box
			Collection booklist = movementsearchManager.getBookCodeDetails(
					SwtUtil.getCurrentHostId(), entityId);

			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();

			Iterator j = booklist.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherBookList", lstOptions));
			/***** Book Combo End ***********/

			/*****  Against Accounts Combo Start ***********/
			Collection againstAcctList =  acctMaintenanceManager
					.getLinkAccountColl(putHostIdListInReq(request), entityId,
							currencyCode);

			AcctMaintenance aactmaint = null;
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			j = againstAcctList.iterator();
			lstOptions.add(new OptionInfo("", "", false));
			while (j.hasNext()) {
				aactmaint = (AcctMaintenance) (j.next());
				if(!aactmaint.getId().getAccountId().equals(accountId) || (aactmaint.getId().getAccountId().equals(accountId) && !aactmaint.getEntity().getId().getEntityId().equals(sweepEntityId))) {
					lstOptions.add(new OptionInfo(aactmaint.getId().getAccountId(), aactmaint.getAcctname(), false));
				}
			}
			lstSelect.add(new SelectInfo("accountList", lstOptions));
			/*****  Against Accounts Combo End ***********/

			/*****  Settlement Method Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			// Cache Manager
			CacheManager cacheManagerInst = null;
			cacheManagerInst = CacheManager.getInstance();
			Collection targetSignCol = null;
			cacheManagerInst = CacheManager.getInstance();
			// get the target sign collection according to the entity id
			targetSignCol = cacheManagerInst.getMiscParamsLVL(
					"SWEEP_SETTLE_METHOD", entityId);
			j = targetSignCol.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("otherSettelmentMethod", lstOptions));
			/*****  Settlement Method Combo End ***********/

			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [getUpdatedLists] - Existing");
			return getView("data");

		} catch (SwtException swtexp) {
			log.error("SwtException Catch in AcctMaintenanceAction.'getUpdatedLists' method : " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error("Exception Catch in AcctMaintenanceAction.'getUpdatedLists' method : " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "getUpdatedLists", AcctMaintenanceAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
		}
		return null;
	}

	public static String getStatusDescriptionFromFlag(HttpServletRequest request, String flag) {
		String returnedValue = null;

		try {

			if (!SwtUtil.isEmptyOrNull(flag)) {
				switch (flag) {
					case "O":
						returnedValue = SwtUtil.getMessage("account.status.open", request);
						break;
					case "B":
						returnedValue = SwtUtil.getMessage("account.status.blocked", request);
						break;
					case "C":
						returnedValue = SwtUtil.getMessage("account.status.closed", request);
						break;
					default:
						returnedValue = "";
				}

			} else {
				returnedValue = "";
			}

		} catch (Exception e) {

		}

		return returnedValue;

	}
	/**
	 *
	 * This method is used to display the account list based on the entity id
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayListByEntityAngular() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Variable to hold the dyForm object
		AcctMaintenance acctMaintenance = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold selectedCurrencyCode
		String selectedCurrencyCode = null;
		// String variable to hold domesticCurrency
		String domesticCurrency = null;
		// String variable to hold hostId
		String hostId = null;
		/*
		 * Start:Code Modified For Mantis 1592 by Sudhakar on 22-12-2011:Account
		 * Maintenance screen allows to create account for entity that has no
		 * currency access
		 */
		// String variable to hold roleId
		String roleId = null;
		// Variable to hold the currencyList object
		ArrayList<LabelValueBean> currencyList = null;
		// integer variable to hold accessInd
		int accessInd;
		// Variable to hold the acctmaintDetailsVO object
		AcctMaintenanceDetailVO acctmaintDetailsVO = null;



		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		boolean quickFilterApplied = false;
//		String quickFilter = null;
		String checkedStatus = null;
		ArrayList<String> selectedStatusArray = new ArrayList<String>();
		String acctType= null;
		try {
			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Entering ");

			acctMaintenance = getAcctMaintenance();




			// get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the entityId
//			entityId = acctMaintenance.getId().getEntityId();
			entityId = request.getParameter("entityId");
			acctType = request.getParameter("acctType");
			width= (String) request.getSession().getAttribute("column_width");
			columnOrder= (String) request.getSession().getAttribute("column_order");
			checkedStatus = request.getParameter("checkedStatus");
			if(!SwtUtil.isEmptyOrNull(checkedStatus)) {
				if(checkedStatus.charAt(0) == 'Y') {
					selectedStatusArray.add("O");
				}
				if(checkedStatus.charAt(1) == 'Y') {
					selectedStatusArray.add("B");
				}
				if(checkedStatus.charAt(2) == 'Y') {
					selectedStatusArray.add("C");
				}
				if(checkedStatus.charAt(3) == 'Y') {
					selectedStatusArray.add("(EMPTY)");
				}


			}
			// check entity id is null then set the current user entity id.
			if (SwtUtil.isEmptyOrNull(entityId)) {
				// get the current user entity id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				// set the entity id
				acctMaintenance.getId().setEntityId(entityId);
			}
			// get selectedCurrencyCode in request
			selectedCurrencyCode = request.getParameter("selectedCurrencyCode");
			// Set currencycode in Currcode bean
			if (SwtUtil.isEmptyOrNull(selectedCurrencyCode)) {
				// get the domesticCurrency
				domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
				// Set the domestic currency in currcode bean
				acctMaintenance.setCurrcode(domesticCurrency == null ? ""
						: domesticCurrency);
			} else {
				acctMaintenance.setCurrcode(selectedCurrencyCode);

			}

//			quickFilter = request.getParameter("quickFilter");
			// set the Accttype
			acctMaintenance.setAccttype("All");
			// get the host id
			hostId = putHostIdListInReq(request);
			// get the accessInd
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			currencyList = (ArrayList) getCurrencyFullAccessList(request,
					hostId, entityId);
			if (accessInd == 0 && currencyList.size() == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else if (accessInd == 0) {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				// set the button status for add,change,view,delete,save,cancel
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
				// set the EntityAccess
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			// set the host id
			acctMaintenance.getId().setHostId(hostId);
			String selectedFiltredByStatus = selectedStatusArray==null || selectedStatusArray.isEmpty() ? "" : "'" + String.join("','", selectedStatusArray) + "'";
			// get the account maintenance details
			acctmaintDetailsVO = acctMaintenanceManager.getCurrencyDetailList(
					entityId, hostId, roleId, acctMaintenance.getCurrcode(), selectedFiltredByStatus, acctType);
			/*
			 * End:Code Modified For Mantis 1592 by Sudhakar on
			 * 22-12-2011:Account Maintenance screen allows to create account
			 * for entity that has no currency access
			 */
			// set the acctmaintenancelist
			request.setAttribute("acctmaintenancelist", getCurrencyList(
					request, hostId, entityId));
			// set the acctmaintenanceDetails

			request.setAttribute("acctmaintenanceDetails", acctmaintDetailsVO
					.getAcctmaintenancelistDetails());
			setAcctMaintenance(acctMaintenance);
			// set the acctMaintenance in the request
			request.setAttribute("acctMaintenance", acctMaintenance);
			// set the EntityList in the request
			putEntityListInReq(request);
			// set the FilterSort in the request
			setFilterSortInReq(request);




//
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putEntityListInReq(request);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(entityId.equals(row.getValue()))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entity", lstOptions));
			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyListReq = getCurrencyList(
					request, hostId, entityId);
			j = currencyListReq.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				if(acctMaintenance.getCurrcode().equals(row.getValue()))
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), true));
				else
					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));


			}
			lstSelect.add(new SelectInfo("currency", lstOptions));

			/***** Acct Type Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			lstOptions.add(new OptionInfo("All", "All", false));
			lstOptions.add(new OptionInfo("Cash", "C", false));
			lstOptions.add(new OptionInfo("Custodian", "U", false));
			lstSelect.add(new SelectInfo("acctType", lstOptions));

			responseConstructor.formSelect(lstSelect);
			/******* acctMaintGrid ******/
			responseConstructor.formGridStart();
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request, false));
			// form rows (records)
			responseConstructor.formRowsStart(acctmaintDetailsVO
					.getAcctmaintenancelistDetails().size());
			for (Iterator<AcctMaintenance> it = acctmaintDetailsVO
					.getAcctmaintenancelistDetails().iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				AcctMaintenance acct = (AcctMaintenance) it.next();
//				if(selectedStatusArray.contains(acct.getAcctstatusflg())){
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME, acct.getId().getEntityId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME , acct.getId().getAccountId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME , acct.getAcctname());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME , acct.getCurrcode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME , acct.getCorresacccode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME , acct.getAccttype());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME , acct.getMinacctcode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME , acct.getAcctlevel());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CUT_OFF_TAGNAME , acct.getCutoff());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_BIC_TAGNAME , acct.getAcctbiccode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME , acct.getLinkAccID());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME , acct.getAcctClass());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_IBAN_TAGNAME , acct.getAcctIBAN());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_IS_ILM_TAGNAME , !SwtUtil.isEmptyOrNull(acct.getIsIlmLiqContributor())?getIsIlmContLabel(acct.getIsIlmLiqContributor()):"No");
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME , acct.getAccountPartyId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME , getStatusDescriptionFromFlag(request, acct.getAcctstatusflg()));
				responseConstructor.formRowEnd();
//				}
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();


			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("selectedAcctType", acctType);
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			xmlWriter.endElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);
			request.setAttribute("data", xmlWriter.getData());


			log.debug(this.getClass().getName()
					+ "- [displayListByEntity] - Exit ");
//			return getView("success");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("SwtException Catch in AcctMaintenanceAction.'displayListByEntity' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			exp.printStackTrace();
			log
					.error("Exception Catch in AcctMaintenanceAction.'displayListByEntity' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", AcctMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			acctMaintenance = null;
			entityId = null;
			selectedCurrencyCode = null;
			domesticCurrency = null;
			hostId = null;
			acctmaintDetailsVO = null;
			roleId = null;
			currencyList = null;
		}
	}


	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
											HttpServletRequest request, boolean isQuickFilterGrid) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = /*SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME + "=100,"
  				 + */SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME + "=150,"
						+ SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME + "=220,"
						+ SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME + "=80,"
						+ SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME + "=125,"
						+ SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME + "=150,"
						+ SwtConstants.ACCOUNT_BIC_TAGNAME + "=120,"
						+ SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME + "=140,"
						+ SwtConstants.ACCOUNT_IBAN_TAGNAME + "=250,"
						+ SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME + "=125,"
						+ SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME + "=160,"
						+ SwtConstants.ACCOUNT_IS_ILM_TAGNAME + "=80,"
						+ SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME + "=125,"
						+ SwtConstants.ACCOUNT_CUT_OFF_TAGNAME + "=100,"
						+ SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME + "=125,"
						+ SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME + "=125" ;
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = /*SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME
+"," 				+ */SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_BIC_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_IBAN_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_IS_ILM_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_CUT_OFF_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME
						+"," 				+ SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME  ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// Entity Id column
//  				if (order.equals(SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME)) {
//  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ENTITY_ID_HEADING, request),
//  							SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
//  							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME)), !isQuickFilterGrid?true:false,
//  							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME)));
//
//  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ENTITY_ID_HEADING_TOOLTIP, request));
//  					if(isQuickFilterGrid) {
//  						tmpColumnInfo.setEditable(true);
//  						tmpColumnInfo.setMaxChars("250");
//  					}
//  					lstColumns.add(tmpColumnInfo);
//  				}
				// Account Id column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_ID_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_ID_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Account Name column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_NAME_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_NAME_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}

					lstColumns.add(tmpColumnInfo);
				}
				// Currency Code column
				if (order.equals(SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_CURRENCY_CODE_HEADING, request),
							SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_CURRENCY_CODE_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Corres Acc Id column
				if (order.equals(SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_CORRES_ACC_ID_HEADING, request),
							SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 7,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_CORRES_ACC_ID_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Account Type column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_TYPE_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 15,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_TYPE_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Main Account Id column
				if (order.equals(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_HEADING, request),
							SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 10,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Account Level column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 9,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Cut Off column
				if (order.equals(SwtConstants.ACCOUNT_CUT_OFF_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_CUT_OFF_HEADING, request),
							SwtConstants.ACCOUNT_CUT_OFF_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 18,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_CUT_OFF_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_CUT_OFF_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_CUT_OFF_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Link Account Id column
				if (order.equals(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_HEADING, request),
							SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// BIC column
				if (order.equals(SwtConstants.ACCOUNT_BIC_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_BIC_HEADING, request),
							SwtConstants.ACCOUNT_BIC_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_BIC_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_BIC_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_BIC_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Account Class column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_CLASS_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_CLASS_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}

				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_STATUS_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 14,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Iban column
				if (order.equals(SwtConstants.ACCOUNT_IBAN_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_IBAN_HEADING, request),
							SwtConstants.ACCOUNT_IBAN_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 8,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_IBAN_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_IBAN_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_IBAN_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}

				// IS ILM column
				if (order.equals(SwtConstants.ACCOUNT_IS_ILM_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_IS_ILM_HEADING, request),
							SwtConstants.ACCOUNT_IS_ILM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 11,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_IS_ILM_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_IS_ILM_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_IS_ILM_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}
				// Account Party Id column
				if (order.equals(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_HEADING, request),
							SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 12,
							Integer.parseInt(widths.get(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME)), !isQuickFilterGrid?true:false,
							!isQuickFilterGrid?true:false, hiddenColumnsMap.get(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_HEADING_TOOLTIP, request));
					if(isQuickFilterGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("250");
					}
					lstColumns.add(tmpColumnInfo);
				}



			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}



	/**
	 * This method is called on clicking the "Sub A/C" button on the Account
	 * maintenance screen and display the sub accounts details.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String subAccountsAngular()
			throws SwtException {
		// AcctMaintenance
//		AcctMaintenance acct = null;
		// Entity Id
		String entityId = null;
		// accountId
		String accountId = null;
		// selectedCurrCode
		String selectedCurrCode = null;
		// mainAccountId
		String mainAccountId = null;
		// hostId
		String hostId = null;
		// Sub account collection.
		Collection acctSubColumn = null;
		// CurrencyManager
		CurrencyManager currencyManagerObj = null;
		// CurrencyDetailVO
		CurrencyDetailVO currencyDetailVOObj = null;
		// ArrayList
		ArrayList arraylist = null;
		// Iterator
		Iterator itr = null;
		// Currency Code
		String currCode = null;
		// Collection of Entity
		Collection collEntity = null;

		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String linkedAccount = null;
		String acctType = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug("Entering 'Account maintenance subAccounts' method");
			// set the screenFieldsStatus in request
			request.setAttribute("screenFieldsStatus", "true");
			// set the button status.
			// get the entityId from request
			entityId = request.getParameter("entityId");
			acctType = request.getParameter("acctType");
			if (entityId != null) {
				entityId = entityId.trim();
			}
			// get the accountId from request
			accountId = request.getParameter("selectedAccountId");
			if (accountId != null) {
				accountId = accountId.trim();
			}
			// get the selectedCurrCode
			selectedCurrCode = request.getParameter("selectedCurrencyCode");

			linkedAccount =  request.getParameter("linkedAccount");

			width= (String) request.getSession().getAttribute("column_width");
			columnOrder= (String) request.getSession().getAttribute("column_order");

			// set the entityId,accountId,selectedCurrCode to bean.
//			acct.getId().setEntityId(entityId);
//			acct.getId().setAccountId(accountId);
//			acct.setCurrcode(selectedCurrCode);
			// get the mainAccountId,hostId
			mainAccountId = accountId;
			hostId = putHostIdListInReq(request);


			if(!"yes".equalsIgnoreCase(linkedAccount)) {
				// get the sub account collection
				acctSubColumn = acctMaintenanceManager.getSubColumnDataDetailList(
						entityId, hostId, mainAccountId, SwtUtil
								.getCurrentSystemFormats(request.getSession()));
			}else {
				acctSubColumn = acctMaintenanceManager.getLinkedAccounts(
						hostId, entityId, accountId);
			}

			// set the acctmaintenanceDetails in to request.
			request.setAttribute("acctmaintenanceDetails", acctSubColumn);
			// get the CurrencyManager instance
			currencyManagerObj = (CurrencyManager) (SwtUtil
					.getBean("currencyManager"));
			// get the currency details list
			currencyDetailVOObj = currencyManagerObj.getCurrencyDetailList(
					entityId, hostId, "All");
			// get the currency list
			arraylist = (ArrayList) currencyDetailVOObj.getCurrencyList();
			arraylist.remove(0);
			// set the acctmaintenancelist in to request.
			request.setAttribute("acctmaintenancelist", arraylist);
			// set the accountCurrencyName in to request.
			if (arraylist != null) {
				currCode = selectedCurrCode;
				itr = arraylist.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					if (lvb.getValue().equals(currCode)) {
						request.setAttribute("accountCurrencyName", lvb
								.getLabel());
					}
				}
			}
			// set the EntityList in request
			log.debug("Entering 'display subAccounts' subAccounts method");
			putEntityListInReq(request);
			// get the entities collection form the request.
			collEntity = (Collection) request.getAttribute("entities");
			// set the accountEntityName in to request.
			if (collEntity != null) {
				itr = collEntity.iterator();
				while (itr.hasNext()) {
					LabelValueBean lvb = (LabelValueBean) (itr.next());
					if (lvb.getValue().equals(entityId)) {
						request.setAttribute("accountEntityName", lvb
								.getLabel());
						break;
					}
				}
			}
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);

			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();

			responseConstructor.formSelect(lstSelect);
			/******* preAdviceInputGrid ******/
			responseConstructor.formGridStart();
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request, false));
			// form rows (records)
			responseConstructor.formRowsStart(acctSubColumn.size());

			for (Iterator<AcctMaintenance> it = acctSubColumn.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				AcctMaintenance acct = (AcctMaintenance) it.next();
//				if(selectedStatusArray.contains(acct.getAcctstatusflg())){
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ENTITY_ID_TAGNAME, acct.getId().getEntityId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_ID_TAGNAME , acct.getId().getAccountId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_NAME_TAGNAME , acct.getAcctname());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CURRENCY_CODE_TAGNAME , acct.getCurrcode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CORRES_ACC_ID_TAGNAME , acct.getCorresacccode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_TYPE_TAGNAME , acct.getAccttype());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME , acct.getMinacctcode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_LEVEL_TAGNAME , acct.getAcctlevel());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_CUT_OFF_TAGNAME , acct.getCutoff());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_LINK_ACCOUNT_ID_TAGNAME , acct.getLinkAccID());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_BIC_TAGNAME , acct.getAcctbiccode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_CLASS_TAGNAME , acct.getAcctClass());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_IBAN_TAGNAME , acct.getAcctIBAN());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_IS_ILM_TAGNAME , !SwtUtil.isEmptyOrNull(acct.getIsIlmLiqContributor())?getIsIlmContLabel(acct.getIsIlmLiqContributor()):"No");
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME , acct.getAccountPartyId());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ACCOUNT_STATUS_TAGNAME , getStatusDescriptionFromFlag(request, acct.getAcctstatusflg()));
				responseConstructor.formRowEnd();
//				}
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();


			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			xmlWriter.endElement(SwtConstants.ACCOUNT_MAINTENANCE_COMPONENT);
			request.setAttribute("data", xmlWriter.getData());

//			return getView("success");
			return getView("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in AcctMaintenanceAction.'subAccounts' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in AcctMaintenanceAction.'subAccounts' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "subAccounts", AcctMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
			// nullify objects
//			acct = null;
			entityId = null;
			accountId = null;
			selectedCurrCode = null;
			mainAccountId = null;
			hostId = null;
			acctSubColumn = null;
			currencyManagerObj = null;
			currencyDetailVOObj = null;
			arraylist = null;
			itr = null;
			currCode = null;
		}
	}


	public String saveColumnOrder() {
		log
				.debug(this.getClass().getName()
						+ "- [saveColumnOrder] - Entering ");
		String order = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			order= request.getParameter("order");
			request.getSession().setAttribute("column_order", order);
			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column order saved ok");
		} catch (Exception e) {
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		}
		log.debug(this.getClass().getName() + "- [saveColumnOrder] - Exiting ");
		return getView("statechange");
	}


	public String saveColumnWidth() {
		log
				.debug(this.getClass().getName()
						+ "- [saveColumnWidth] - Entering ");
		String width = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			width = request.getParameter("width");

			request.getSession().setAttribute("column_width", width);

			request.setAttribute("reply_status_ok", "true");
			request.setAttribute("reply_message", "Column width saved ok");

		} catch (Exception e) {
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0]
					.getClassName()
					+ "."
					+ e.getStackTrace()[0].getMethodName()
					+ ":"
					+ e.getStackTrace()[0].getLineNumber());
		}
		log.debug(this.getClass().getName() + "- [saveColumnWidth] - Exiting ");
		return getView("statechange");
	}

	private String getIsIlmContLabel(String value) {
		String label = null;

		switch (value) {
			case "Y":
				label = "Yes";
				break;
			case "N":
				label = "No";
				break;
			default:
				break;
		}

		return label;
	}

}
