/*
 * @(#)BookCodeAction.java 1.0 21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;








import org.springframework.context.ApplicationContext;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.BookCodeColumnData;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlBookCode;
import org.swallow.export.service.impl.Xml2CsvImpl;
import org.swallow.export.service.impl.Xml2PdfImpl;
import org.swallow.export.service.impl.Xml2XlsImpl;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Location;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.model.ExportObject;
import org.swallow.util.CacheManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemInfo;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * BookCodeAction.java
 *
 * This class is used to add, change, delete, view for Book Codeo details.
 */


import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/bookCode", "/bookCode.do"})
public class BookCodeAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/bookcodemaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/bookcodemaintenance");
		viewMap.put("change", "jsp/maintenance/bookcodemaintenanceadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}




	private BookCode bookCode;
	public BookCode getBookCode() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		bookCode = RequestObjectMapper.getObjectFromRequest(BookCode.class, request);
		return bookCode;
	}

	public void setBookCode(BookCode bookCode) {
		this.bookCode = bookCode;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("bookCode", bookCode);
	}
	@Autowired
	private BookCodeManager bkCodeMgr = null;

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(BookCodeAction.class);
	private ApplicationContext ctx = null;

	/**
	 * Sets the BookCodeManager instance
	 *
	 * @param bookCodeManager
	 */
	public void setBookCodeManager(BookCodeManager bookCodeManager) {
		this.bkCodeMgr = bookCodeManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "displayList":
				return displayList();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "delete":
				return delete();
			case "update":
				return update();
			case "exportBookCode":
				return exportBookCode();
		}


		return unspecified();
	}
	/**
	 * This method is default method in struts. This method is called if no
	 * method are specified in the request
	 * @return ActionForward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName()
				+ "- [unspecified] - Calls displayList method ");
		return displayList();
	}

	/**
	 * This method is used to load the book details in the screen
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayList()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [displayList] - Entering");
			/* Method's local variable declaration */
			String entityId;
			String hostId;
			int accessInd;
			Collection coll = null;
			Collection collBookCode = null;
			/* Class instance declaration */
// To remove: 			DynaValidatorForm dyForm;
			BookCode bookCode = null;
			SystemInfo systemInfo;
// To remove: 			dyForm = (DynaValidatorForm) form;
			bookCode = (BookCode) getBookCode();
			/* Getting the entity id from the bean class */
			entityId = bookCode.getId().getEntityId();
			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Get the user's selected entity */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting the entity id */
				bookCode.getId().setEntityId(entityId);
			}
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Retrieve user's entity access list in the collection variable */
			coll = SwtUtil.getUserEntityAccessList(request.getSession());

			/* Retrieve useer's menu,Entity and currency group access */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);

			/* Condition to check the access rights. */
			if (accessInd == 0) {
				/*
				 * If the user has access,Then the all the buttons will be
				 * enabled.
				 */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");

			} else {
				/* Else all the buttons will be disabled. */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/* Used to put the group level names in request */
			putGroupLevelNamesinRequest(coll, entityId, request);
			systemInfo = new SystemInfo();
			/* Setting the host id */
			bookCode.getId().setHostId(hostId);
			/* Retrieve Book code list from DB based on the parameters passed. */
			collBookCode = bkCodeMgr.getAllBookCodeList(hostId, entityId);
			/* Used to add the entity name in request */
			putEntityListInReq(request);
			request.setAttribute("bookCodeColl", collBookCode);
			request.setAttribute("methodName", "displayList");
			setBookCode(bookCode);
			log.debug(this.getClass().getName() + "- [displayList] - Exiting");
			return getView("success");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [displayList] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayList] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayList", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This method is used to put the group level name in request
	 *
	 * @Collection coll
	 * @String entityId
	 * @param request
	 * @return
	 * @throws SwtException
	 */

	private void putGroupLevelNamesinRequest(Collection coll, String entityId,
											 HttpServletRequest request) throws SwtException {

		log.debug(this.getClass().getName()
				+ "- [putGroupLevelNamesinRequest] - Entering");
		String hostId;
		String locationId;
		Collection locationIdsColl = null;
		Iterator itr;
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		locationIdsColl = bkCodeMgr.getBookLocations(hostId, entityId);
		itr = locationIdsColl.iterator();
		LabelValueBean labelValueBeanObj = null;
		locationIdsColl = new ArrayList();
		locationIdsColl.add(new LabelValueBean(SwtConstants.EMPTY_STRING,
				SwtConstants.EMPTY_STRING));
		/* Adds all the locations for the DropdownList */
		while (itr.hasNext()) {
			Location loc = (Location) (itr.next());
			/* Get the location id from bean class */
			locationId = loc.getId().getLocationId();
			locationIdsColl.add(new LabelValueBean(locationId, locationId));
		}
		request.setAttribute("locationIdsColl", locationIdsColl);
		/* END: Code changed as par SRS - Currency Monitor for ING, 03-JUL-2007 */
		String[] groupLevelNames = SwtUtil.getGroupLevelNames(coll, entityId);
		request.setAttribute("groupLevelName1", groupLevelNames[0]);
		request.setAttribute("groupLevelName2", groupLevelNames[1]);
		request.setAttribute("groupLevelName3", groupLevelNames[2]);
		log.debug(this.getClass().getName()
				+ "- [putGroupLevelNamesinRequest] - Exiting");
	}

	/**
	 * This is to add the entity in request
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Entering");
		/* Method's local variable declaration */
		HttpSession session;
		Collection coll = null;
		Collection collLVL = null;
		session = request.getSession();
		// Gets the Entity access list for the logged in user
		coll = SwtUtil.getUserEntityAccessList(session);
		// Adds the Entity list in Entity Dropdown list
		collLVL = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entities", collLVL);
		log.debug(this.getClass().getName()
				+ "- [putEntityListInReq] - Exiting");
		return coll;
	}

	/**
	 * This is used to add the book details
	 * @return
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [add] - Entering");
			/* Method's local variable declaration */
			String entityName;
			String entityId;
			Collection coll = null;
			/* Class instance declaration */
// To remove: 			DynaValidatorForm dyForm;
// To remove: 			dyForm = (DynaValidatorForm) form;
			BookCode bookCode = new BookCode();
			/* Reading entity id and entity name from request */
			entityName = request.getParameter("entityName");
			entityId = request.getParameter("entityCode");
			/* Setting the entity id using bean class */
			bookCode.getId().setEntityId(entityId);
			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve user's Current entity */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting the entity id using bean class */
				bookCode.getId().setEntityId(entityId);
			}
			/* Used to put the entity details in request */
			coll = putEntityListInReq(request);
			/* Used to put the group level id in request */
			putGroupLevelNamesinRequest(coll, entityId, request);
			/* Used to put the group level name in request */
			putgetGroupLevelsDetailInReq(request, entityId);
			request.setAttribute("methodName", "save");
			request.setAttribute("entityId", entityId);
			request.setAttribute("entityName", entityName);
			request.setAttribute("screenFieldsStatus", "true");
			setBookCode(bookCode);
			log.debug(this.getClass().getName() + "- [add] - Exiting");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to saved the newly added details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [save] - Entering");
		/* Method's local variable declaration */
		String hostId;
		String entityId;
		String bookCodeId;
		String entityName;
		Collection coll = null;
		/* Class instance declaration */
		ActionErrors errors = new ActionErrors();
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		BookCode bookCode = (BookCode) getBookCode();
		SystemInfo systemInfo = new SystemInfo();
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/* Setting the host id in bean class */
		bookCode.getId().setHostId(hostId);
		/* Reading entity id from request */
		entityId = bookCode.getId().getEntityId();
		/* Reading book code from request */
		bookCodeId = bookCode.getId().getBookCode();
		/* Reading entity name from request */
		entityName = request.getParameter("entityName");
		request.setAttribute("screenFieldsStatus", "false");
		request.setAttribute("parentFormRefresh", "yes");
		try {
			/* Save the book details in DB based on the parameters passed. */
			bkCodeMgr.saveBookCodeDetail(bookCode);
			/* Used to put the entity list in request */
			coll = putEntityListInReq(request);
			/* Used to put the group level id in request */
			putgetGroupLevelsDetailInReq(request, entityId);
			setBookCode(bookCode);
			request.setAttribute("parentFormRefresh", "yes");
			/* Used to put the group level name in request */
			putGroupLevelNamesinRequest(coll, entityId, request);
			log.debug(this.getClass().getName() + "- [save] - Exiting");
			return displayList();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [save] method : - "
					+ swtexp.getMessage());
			bookCode.getId().setHostId(hostId);
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("entityName", entityName);
			putgetGroupLevelsDetailInReq(request, entityId);
			displayList();
			request.setAttribute("methodName", "save");
			request.setAttribute("screenFieldsStatus", "true");

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				errors.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to update the book code details.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + "- [change] - Entering");
			/* Method's local variable declaration */
			String hostId;
			String entityName;
			String entityId;
			String bookCodeId;
			String oldVal;
			Collection coll = null;
			/* Class instance declaration */
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			BookCode bookCode = (BookCode) getBookCode();
			SystemInfo systemInfo = new SystemInfo();
			request.setAttribute("screenFieldsStatus", "true");
			/* Retrieve and store hostId from CacheManager */
			hostId = CacheManager.getInstance().getHostId();
			/* Setting the host id using bean class */
			bookCode.getId().setHostId(hostId);
			/* Reading entity name from request */
			entityName = request.getParameter("entityName");
			/* Reading entity id from request */
			entityId = request.getParameter("entityCode");
			/* Reading book code id from request */
			bookCodeId = request.getParameter("bookCodeId");
			/*
			 * Retrieve the editable fields from DB based on the parameters
			 * passed.
			 */
			bookCode = bkCodeMgr.getEditableData(hostId, entityId, bookCodeId);
			/* Group levels are append in the string buffer */
			oldVal = new StringBuffer("Bookcode-Name=").append(
							bookCode.getBookName()).append("^Group-Level-1=").append(
							bookCode.getGroupIdLevel1()).append("^Group-Level-2=")
					.append(bookCode.getGroupIdLevel2()).append(
							"^Group-Level-3=").append(
							bookCode.getGroupIdLevel3()).toString();

			/* Add the group level name in request */
			putgetGroupLevelsDetailInReq(request, entityId);
			/* Used to put the entity list in request */
			coll = putEntityListInReq(request);
			/* Used to put the group level in request */
			putGroupLevelNamesinRequest(coll, entityId, request);
			setBookCode(bookCode);
			request.setAttribute("methodName", "change");
			request.setAttribute("entityName", entityName);
			request.setAttribute("oldValue", oldVal);
			log.debug(this.getClass().getName() + "- [change] - Exiting");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to delete the Bookdetails from the database.

	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [delete] - Entering");
		/* Method's local variable declaration */
		String bkCode;
		String bookName;
		String hostId;
		String entityId;
		/* Class instance declaration */
		ActionErrors errors = new ActionErrors();
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		BookCode bookCode = (BookCode) getBookCode();
		SystemInfo systemInfo = new SystemInfo();
		/* Reading selected book code and book name from request */
		bkCode = request.getParameter("selectedbookCode");
		bookName = request.getParameter("selectedbookName");
		/* Setting selected bookcode and book name using bean class */
		bookCode.getId().setBookCode(bkCode);
		bookCode.setBookName(bookName);
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/* Setting the host id using the bean class */
		bookCode.getId().setHostId(hostId);

		/* Reading the entity id from bean class */
		entityId = bookCode.getId().getEntityId();
		/* Used to put the entity list in request */
		putEntityListInReq(request);
		/* Retrieve Book code details from DB based on the parameters passed. */
		Collection collBookCode = bkCodeMgr
				.getAllBookCodeList(hostId, entityId);
		request.setAttribute("bookCodeColl", collBookCode);
		request.setAttribute("delete", "delete");

		try {
			bkCodeMgr.deleteBookCodeDetail(bookCode);
			setBookCode(bookCode);
			log.debug(this.getClass().getName() + "- [delete] - Exiting");
			return displayList();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [delee] method : - "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			request.setAttribute("delete", "delete");
			return displayList();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to save the updated book details.
	 * @return
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [update] - Entering");
		/* Class instance declaraion */
		ActionErrors errors = new ActionErrors();
// To remove: 		DynaValidatorForm dyForm = (DynaValidatorForm) form;
		BookCode bookCode = (BookCode) getBookCode();
		SystemInfo systemInfo = new SystemInfo();
		/* Method's local variable declaration */
		String entityId;
		String entityName;
		String bookCodeId;
		String hostId;
		String oldValue;
		String newValue;
		String userId;
		Collection coll = null;
		entityId = bookCode.getId().getEntityId();
		entityName = request.getParameter("entityName");
		request.setAttribute("entityName", entityName);
		bookCodeId = bookCode.getId().getBookCode();
		/* Retrieve and store hostId from CacheManager */
		hostId = CacheManager.getInstance().getHostId();
		/* Setting thehostid using the bean class */
		bookCode.getId().setHostId(hostId);
		try {
			oldValue = request.getParameter("oldValue");
			/* Appending the group level in the sttring buffer */
			newValue = new StringBuffer("Bookcode-Name=").append(
							bookCode.getBookName()).append("^Group-Level-1=").append(
							bookCode.getGroupIdLevel1()).append("^Group-Level-2=")
					.append(bookCode.getGroupIdLevel2()).append(
							"^Group-Level-3=").append(
							bookCode.getGroupIdLevel3()).toString();
			/* Retrieve current user id from session */
			userId = SwtUtil.getCurrentUserId(request.getSession());
			/* Setting the updated user id */
			bookCode.setUpdateUser(userId);
			/* Setting the host id */
			bookCode.getId().setHostId(
					SwtUtil.getCurrentHostId(request.getSession()));
			/* Getting & setting the ipaddress */
			systemInfo.setIpAddress(request.getRemoteAddr());
			/* Setting the old value */
			systemInfo.setOldLogString(oldValue);
			/* Setting the new value */
			systemInfo.setNewLogString(newValue);
			/*
			 * Updated book details are stored in the DB bases on the parameter
			 * value passed
			 */
			bkCodeMgr.updateBookCodeDetail(bookCode);
			/* Used to put the entity list in request */
			coll = putEntityListInReq(request);
			/* Used to put the group level name in request */
			putgetGroupLevelsDetailInReq(request, entityId);
			request.setAttribute("parentFormRefresh", "yes");
			setBookCode(bookCode);
			/* Used to put the group level id in request */
			putGroupLevelNamesinRequest(coll, entityId, request);
			request.setAttribute("methodName", "change");
			log.debug(this.getClass().getName() + "- [update] - Exiting");
			return getView("change");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - SwtException Catched in [update] method : - "
					+ swtexp.getMessage());
			putEntityListInReq(request);
			bookCode.getId().setHostId(hostId);
			putgetGroupLevelsDetailInReq(request, entityId);
			request.setAttribute("methodName", "change");
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("screenFieldsStatus", "");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("change");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", BookCodeAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * This is used to put the group level names in request
	 *
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	private void putgetGroupLevelsDetailInReq(HttpServletRequest request,
											  String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [putgetGroupLevelsDetailInReq] - Entering");
		/* Method's local variable declaration */
		String hostId;
		Collection collBookCodeGroupLevel_1 = null;
		Collection collBookCodeGroupLevel_2 = null;
		Collection collBookCodeGroupLevel_3 = null;
		/* Class instance declaration */
		SystemInfo systemInfo;
		BookCodeManager bookCodeManager = null;
		bookCodeManager = (BookCodeManager) (SwtUtil.getBean("bookCodeManager"));

		/* Retrieve the host id from cachemanager */
		hostId = CacheManager.getInstance().getHostId();
		systemInfo = new SystemInfo();
		/* Retrieve group level1 name from DB based on the parameters passed. */
		collBookCodeGroupLevel_1 = bookCodeManager.getGroupLevelDetails(hostId,
				entityId, new Integer(1));
		/* Retrieve group level2 name from DB based on the parameters passed. */
		collBookCodeGroupLevel_2 = bookCodeManager.getGroupLevelDetails(hostId,
				entityId, new Integer(2));
		/* Retrieve group level3 name from DB based on the parameters passed. */
		collBookCodeGroupLevel_3 = bookCodeManager.getGroupLevelDetails(hostId,
				entityId, new Integer(3));

		request.setAttribute("groupLevel_1", collBookCodeGroupLevel_1);
		request.setAttribute("groupLevel_2", collBookCodeGroupLevel_2);
		request.setAttribute("groupLevel_3", collBookCodeGroupLevel_3);
		log.debug(this.getClass().getName()
				+ "- [putgetGroupLevelsDetailInReq] - Exiting");
	}

	/**
	 * This is used to set the status of the button
	 * @throws SwtException
	 */

	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String cancelStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
	}

	/**
	 * This is used to export Book details.
	 * @return
	 * @throws SwtException
	 */
	public String exportBookCode()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold the host id
		String hostId = null;
		// Variable to store entity id
		String entityId = null;
		// Variable to store what type of export is chosen
		String exportType = null;
		// Variable Declaration for bookCode
		BookCode bookCode = null;
		// Variable Declaration for dyForm */
// To remove: 		DynaValidatorForm dyForm = null;
		// Variable Declaration for collBookCode */
		ArrayList collBookCode = null;
		// Holds the file name for export
		String fileName = null;
		// Holds the title suffix of this application
		String titleSuffix = null;
		// Variable to hold excelGen
		Obj2XlsImpl excelGen = null;
		// Variable to hold pdfGen
		Obj2PdfImpl pdfGen = null;
		// Variable to hold csvGen
		Obj2CsvImpl csvGen = null;
		// Variable to hold entityColl
		Collection entityColl = null;
		// Variable to hold groupLevelNames
		String[] groupLevelNames = null;
		// Variable to hold groupLevelOneName
		String groupLevelOneName = null;
		// Variable to hold groupLevelTwoName
		String groupLevelTwoName = null;
		// Variable to hold groupLevelThreeName
		String groupLevelThreeName = null;
		// Variable to hold bookCodeColumn
		BookCodeColumnData bookCodeColumn = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// FilterDTO instance
		FilterDTO filterDTO = null;
		// Instance of Obj2XmlBookCode
		Obj2XmlBookCode convertBookCodeToXML = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		//
		ArrayList<ArrayList<ExportObject>> data = null;
		try {
			log.debug(this.getClass().getName() + "- [export] - Entering ");
			/* Gets the value from request */
			hostId = SwtUtil.getCurrentHostId();
			entityId = request.getParameter("entityCode");
			exportType = request.getParameter("exportType");
// To remove: 			/* Converts the Action Form to DynaValidatorForm */
// To remove: 			dyForm = (DynaValidatorForm) form;
			/* Used to set the value in bean object */
			bookCode = (BookCode) getBookCode();
			/* Setting host id using bean class */
			bookCode.getId().setHostId(hostId);
			/* Setting entity id using bean class */
			bookCode.getId().setEntityId(entityId);
			/* Fetches book details from DB by calling manager */
			collBookCode = (ArrayList) bkCodeMgr.getAllBookCodeList(hostId,
					entityId);
			/* Condition to check arraylist has value */
			entityColl = putEntityListInReq(request);
			groupLevelNames = SwtUtil.getGroupLevelNames(entityColl, entityId);
			groupLevelOneName = groupLevelNames[0];
			groupLevelTwoName = groupLevelNames[1];
			groupLevelThreeName = groupLevelNames[2];
			if (groupLevelOneName == null) {
				groupLevelOneName = "";
			}
			if (groupLevelTwoName == null) {
				groupLevelTwoName = "";
			}
			if (groupLevelThreeName == null) {
				groupLevelThreeName = "";
			}
			bookCodeColumn = new BookCodeColumnData(groupLevelOneName,
					groupLevelTwoName, groupLevelThreeName);
			/* Used to put the entity list in request */
			putEntityListInReq(request);

			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			filterDTO = new FilterDTO();

			filterDTO.setName("Entity");
			filterDTO.setValue(entityId);
			filterData.add(filterDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");

			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}

			convertBookCodeToXML = new Obj2XmlBookCode();

			data  = convertBookCodeToXML.getExportData(bookCodeColumn
					.getColumnData(), filterData, (ArrayList) collBookCode);
			/* To export the data in PDF,Excel and CSV format */
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			if (exportType.equalsIgnoreCase("excel")) {
				excelGen = new Obj2XlsImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".xls");
				excelGen.convertObject(request, response, bookCodeColumn
						.getColumnData(), filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("pdf")) {
				pdfGen = new Obj2PdfImpl();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".pdf");
				pdfGen.convertObject(request, response, bookCodeColumn
						.getColumnData(), filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("csv")) {
				csvGen = new Obj2CsvImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".csv");
				csvResponse = csvGen.convertObject(request, bookCodeColumn
						.getColumnData(), filterData, data, null,null, fileName);
				/*
				 * End Code modified by Chidambaranathan for include timestamp
				 * in the export function for Mantis_1513 on 04-Aug-2011
				 */
				try {
					response.getOutputStream().print(csvResponse);
				} catch (IOException e) {
					log.error(this.getClass().getName()
							+ "- [exportBookCode] - IOException "
							+ e.getMessage());
					throw new SwtException(e.getMessage());
				}
			}
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ "- [exportBookCode] - SwtException "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [exportBookCode] - Exception " + e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "exportBookCode", BookCodeAction.class), request, "");
		} finally {
			// Nullifying the already created objects
			collBookCode = null;
			hostId = null;
			entityId = null;
			exportType = null;
			bookCode = null;
// To remove: 			dyForm = null;
			fileName = null;
			titleSuffix = null;
			excelGen = null;
			pdfGen = null;
			csvGen = null;
			entityColl = null;
			groupLevelNames = null;
			groupLevelOneName = null;
			groupLevelTwoName = null;
			groupLevelThreeName = null;
			bookCodeColumn = null;
			filterData = null;
			filterDTO = null;
			convertBookCodeToXML = null;
			csvResponse = null;
		}
		return null;
	}
}