/*
 * @(#)CurrencyAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlCurrency;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.CurrencyDetailVO;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.model.ExportObject;
import org.swallow.model.User;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;







/**
 * CurrencyAction.java
 *
 * CurrencyAction class is used for Currency screen that will display currencies
 * across entities
 *
 */
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/currency", "/currency.do"})
public class CurrencyAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/currencymaintenanceadd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/currencymaintenance");
		viewMap.put("change", "jsp/maintenance/currencymaintenanceadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "displayListByEntity":
				return displayListByEntity();
			case "add":
				return add();
			case "change":
				return change();
			case "update":
				return update();
			case "save":
				return save();
			case "exportCurrency":
				return exportCurrency();
			case "delete":
				return delete();
			case "checkCurrencyAccess":
				return checkCurrencyAccess();
			case "clearSessionValues":
				return clearSessionValues();
			case "getAppropriateCurrencyOffset":
				return getAppropriateCurrencyOffset();
		}


		return displayListByEntity();
	}
	private Currency currency;
	public Currency getCurrency() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		currency = RequestObjectMapper.getObjectFromRequest(Currency.class, request);
		return currency;
	}
	public void setCurrency(Currency currency) {
		this.currency = currency;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("currency", currency);

	}


	private String selectedCurrencyCode;
	public String getSelectedCurrencyCode() {
		return selectedCurrencyCode;
	}
	public void setSelectedCurrencyCode(String selectedCurrencyCode) {
		this.selectedCurrencyCode = selectedCurrencyCode;
	}

	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}

	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}

	String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId= menuItemId;
	}


	/**
	 * Used to hold currencyManager reference object
	 */
	@Autowired
	private CurrencyManager currencyManager = null;
	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyAction.class);

	/**
	 * Set Currency manager object
	 *
	 * @param currencyManager
	 * @return
	 */
	public void setCurrencyManager(CurrencyManager currencyManager) {
		this.currencyManager = currencyManager;
	}

	/**
	 * This is the default method for this class returns displayListByEntity
	 * method
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */

	public String unspecified()
			throws SwtException {

		log.debug(this.getClass().getName() + " - [Unspecified] - "
				+ "returns to displayListByEntity");
		/* Returns displayListByEntity method */
		return displayListByEntity();
	}

	/**
	 * Set the button status according to the access of menu entity and currency
	 * group
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 *
	 */
	private void putEntityAccessInReq(HttpServletRequest request,
									  String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityAccessInReq] - "
				+ "Entry");
		/* Method's local variable declaration. */
		int accessInd;
		// Set the button status according to entity access

		/* Receives the accessInd from the getMenuEntityCurrGrpAccess of SwtUtil */
		accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);

		/* Condition to check the accessInd is zero */
		if (accessInd == 0) {

			/*
			 * Set the button status according to entity access and set the full
			 * access of entity in request attribute
			 */
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			/*
			 * Set the button status according to entity access and set the view
			 * access of entity in request attribute
			 */
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}
		log.debug(this.getClass().getName() + " - [putEntityAccessInReq] - "
				+ "Exit");
	}

	/**
	 * Collects the interest basis from miscparams of cache manager and put the
	 * values of interest basis in to the request attribute
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 *
	 */
	private void putInterestBasis(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putInterestBasis] - "
				+ "Entry");
		/* Method's local variable declaration. */
		Collection coll;
		/* Receives the collection of interest basis from misc param level */
		coll = CacheManager.getInstance().getMiscParamsLVL(
				SwtConstants.INST_BASIS, entityId);
		coll.remove(new LabelValueBean("", ""));
		/* Set the collection of interest basis in to the request attribute */
		request.setAttribute("interestBasis", coll);
		log.debug(this.getClass().getName() + " - [putInterestBasis] - "
				+ "Exit");
	}

	/**
	 * Get the currency detail list from currency manager and Put the values of
	 * currency list and details in to the request attribute
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 *
	 */
	private void putCurrencyListandDetails(HttpServletRequest request,
										   String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName()
				+ " - [putCurrencyListandDetails] - " + "Entry");
		/* Method's class instance declaration. */
		CurrencyDetailVO currencyDetailsVO;

		int totalCount = 0;
		SystemFormats sysFormat = null;
		sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
		/*
		 * Get the currency detail list from the currency manager and returns it
		 * as CurrencyDetailVO
		 */

		currencyDetailsVO = currencyManager.getCurrencyDetailList(entityId,
				hostId, "All", sysFormat);
		request.setAttribute("currencyList", currencyDetailsVO
				.getCurrencyList());
		request.setAttribute("currencyListDetails", currencyDetailsVO
				.getCurrencyListDetails());

		totalCount = currencyDetailsVO.getCurrencyListDetails().size();
		request.setAttribute("totalCount", totalCount);

		log.debug(this.getClass().getName()
				+ " - [putCurrencyListandDetails] - " + "Exit");
	}

	/**
	 * Collects the currency group details from swt maintenance cache put the
	 * values of Currency group details in to the request attribute
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @return
	 * @throws SwtException
	 *
	 */
	private void putCurrencyGroupDetails(HttpServletRequest request,
										 String hostId, String entityId) throws SwtException {
		log.debug(this.getClass().getName() + " - [putCurrencyGroupDetails] - "
				+ "Entry");

		/* Methods local variable declaration */
		String roleId;
		Collection currrencyGroupList;
		HttpSession session;
		User user;
		currrencyGroupList = new ArrayList();
		/* Collects the session from request */
		session = request.getSession();
		/* Get the current user id from SwtUtil */
		user = SwtUtil.getCurrentUser(session);
		/* Retrieve the role id of the user */
		roleId = user.getRoleId();

		/*
		 * Receives the collection of currency group list from
		 * SwtMaintenanceCache
		 */
		currrencyGroupList = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(roleId, entityId);
		request.setAttribute("currencyGroupList", currrencyGroupList);
		log.debug(this.getClass().getName() + " - [putCurrencyGroupDetails] - "
				+ "Exit");
	}

	/**
	 * Action method to display the currency details get from the data base for
	 * the default or selected entity in the screen
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String displayListByEntity() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {

			log.debug(this.getClass().getName() + " - [displayListByEntity] - "
					+ "Entry");

			/* Method's local variable and class instance declaration */
			String hostId;
			String entityId;
			Currency currency;

			currency = getCurrency();

			currency.getId().setCurrencyCode("All");

			setCurrency(currency);

			/* Retrieve and store the host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Retrieve the entity id from currency bean */
			entityId = currency.getId().getEntityId();
			/* Set the host id to the currency bean */
			currency.getId().setHostId(hostId);

			/* Condition to check entity is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * If it is null the current entity of the user is set by
				 * SwtUtil
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id to the currency bean */
				currency.getId().setEntityId(entityId);
			}
			/*
			 * Pass the request parameter to set the entity access list in to
			 * the request attribute
			 */
			putEntityListInReq(request);
			/*
			 * Pass the request, host id and entity id to set the currency list
			 * details in to the request attribute
			 */
			putCurrencyListandDetails(request, hostId, entityId);
			/* Set the button status based on the access id */
			putEntityAccessInReq(request, entityId);
			setCurrency(currency);
			log.debug(this.getClass().getName() + " - [displayListByEntity] - "
					+ "Exit");
			return getView("success");
		} catch (SwtException swtexp) {
			log

					.error(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log

					.error(this.getClass().getName()
							+ " - Exception Catched in [displayListByEntity] method : - "
							+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", CurrencyAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Method to display the add currency screen with its loaded values
	 * collected from parent screen and data base
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {

			log.debug(this.getClass().getName() + " - [add] - " + "Entry");

			/* Method's local variable declaration */
			String entityCode;
			String entityText;
			String hostId;
			Currency currency;
			String timeZoneOffset =null;
			String currencyCode ="" ;

			/* Read the value of entityCode from the request */
			entityCode = request.getParameter("entityCode");

			/* Read the value of entityText from the request */
			entityText = request.getParameter("entityText");

			/* Set the screenFieldstatus true in request attribute */
			request.setAttribute("screenFieldsStatus", "true");

			currency = new Currency();

			/* Retrieves and store the host id from the SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set the host id to the currency bean object */
			currency.getId().setHostId(hostId);

			/* Set the entity id to the currency bean object */
			currency.getId().setEntityId(entityCode);

			/* Set the TOlerance value is zero as default */
			currency.setTolerance(0L);

			setCurrency(currency);

			// Code added to populate Currency group Drop down
			/*
			 * Pass the request, host id and entity id to put the currency group
			 * in to the request attribute
			 */
			putCurrencyGroupDetails(request, hostId, entityCode);

			request.setAttribute("entityText", entityText);

			/*
			 * Pass the request attribute to set the currency list into the
			 * request attribute to populate the currency list drop down
			 */
			putCurrencyListInReq(request);

			/*
			 * Pass the request attribute to set the interest basis into the
			 * request attribute to populate the Interest basis Dropdown
			 */

			putInterestBasis(request, entityCode);
			request.setAttribute("methodName", "save");

			/*
			 * Passing the request parameter to set the multiplier list into the
			 * request attribute to populate the multiplier Dropdown
			 */
			putMultiplierInReq(request, entityCode);
			timeZoneOffset = currencyManager.getCurrencyTznameAndTzOffset(currencyCode);
			request.setAttribute("timeZoneOffset", timeZoneOffset);
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");

			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", CurrencyAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Displays the change currency screen for the selected currency, with the
	 * details given for the selected currency
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {

			log.debug(this.getClass().getName() + " - [change] - " + "Entry");

			/* Method's Local variable and class instance declaration */
			String hostId;
			String entityId;
			String entityName;
			String currencyCode;
			Currency currency;
			CurrencyDetailVO currencyDetailsVO;
			EntityManager entityManager;
			Currency curr;
			Collection collEntity;
			/* Variable Declaration for entityTimeOffset */
			String timeZoneOffset;
			//variable to hold sysformat
			SystemFormats sysformat = null;
			int totalCount = 0;


				curr = getCurrency();

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Retrieves and store the host id from the SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set the screen field status as true ion the request attribute */
			request.setAttribute("screenFieldsStatus", "true");

			/* Read the entity code from request */
			entityId = request.getParameter("entityCode");

			/* Read the entity text form request */
			entityName = request.getParameter("entityText");
			/* Setting the hostid using bean class */
			curr.getId().setHostId(hostId);

			/* Read the currency code from the DynaValidatorForm for currency */
			//Nadia
			currencyCode = request.getParameter("selectedCurrencyCode");

			/* This method is called to populate Currency group in Drop down */
			putCurrencyGroupDetails(request, hostId, entityId);

			/*
			 * The method getCurrencyDetailList of currency manager returns the
			 * CurrencyDetailVO
			 */
			currencyDetailsVO = currencyManager.getCurrencyDetailList(entityId,
					hostId, currencyCode, sysformat);

			/* The currencyList of CurrencyDetailVO is set in request attribute */
			request.setAttribute("currencyList", currencyDetailsVO
					.getCurrencyList());

			/*
			 * The currencyListDetails of CurrencyDetailVO is set in request
			 * attribute
			 */
			request.setAttribute("currencyListDetails", currencyDetailsVO
					.getCurrencyListDetails());

			totalCount = currencyDetailsVO.getCurrencyListDetails().size();
			request.setAttribute("totalCount", totalCount);

			/*
			 * Read the currency name from the request and set it in the request
			 * attribute
			 */
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));

			/*
			 * Retrieves the currency details list from currencyDeatilVo and
			 * store in to the currency bean
			 */
			curr = (Currency) (currencyDetailsVO.getCurrencyListDetails()
					.iterator().next());

			setCurrency(curr);

			request.setAttribute("currency", curr);
			request.setAttribute("methodName", "update");
			request.setAttribute("entityText", entityName);

			/*
			 * Pass the request parameter to set the interest basis into the
			 * request attribute to populate Interest Basis in drop down
			 */

			putInterestBasis(request, entityId);
			/*
			 *
			 *
			 *
			 *
			 *
			 * Pass the request parameter to set the multiplier list into the
			 * request attribute to populate Multiplier in Drop down
			 */

			putMultiplierInReq(request, entityId);
			timeZoneOffset = currencyManager.getCurrencyTznameAndTzOffset(currencyCode);
			request.setAttribute("timeZoneOffset", timeZoneOffset);
			//setCurrency(this.currency);
			log.debug(this.getClass().getName() + " - [change] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", CurrencyAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to get the currency detail for the selected currency detail to
	 * validate and return the error message if validation fails or update the
	 * currency details in database for the selected currency
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		String hostId;
		String currcode;
		String timeZoneOffset;
		Currency currency;
		String entityId = null;
		SystemFormats sysformat = null;

		try {
			log.debug(this.getClass().getName() + " - [update] - " + "Entry");
			/* Method's local variable declaration */

				currency =getCurrency();

			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			/* Condition to check Threshold is null */
			if (currency.getThreshold() == null) {
				/* If it is null then set its value as 0.0 */
				currency.setThreshold(0.0);
			}

			/* Retrieves and store the host id from the SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set the value of HostId in currency bean object */
			currency.getId().setHostId(hostId);

			/* Collects the value of currency code from Currency bean Object */
			currcode = currency.getId().getCurrencyCode();
			entityId = currency.getId().getEntityId();
			/* Set the currency code into the currency master bean object */
			currency.getCurrencyMaster().setCurrencyCode(currcode);

			/*
			 * Currency name is read from the request and set in the request
			 * attribute
			 */
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));

			/*
			 * This method is called to send the Multiplier through request
			 * attribute
			 */

			putMultiplierInReq(request, currency.getId().getEntityId());

			/* To update the values of currency bean object in to the DataBase */
			currencyManager.updateCurrencyDetail(currency, sysformat);

			request.setAttribute("currencyGroupList", new ArrayList());
			currency = new Currency();
			setCurrency(currency);
			/*
			 * Request attribute to refresh the parent from after updating the
			 * values
			 */
			request.setAttribute("parentFormRefresh", "yes");
			timeZoneOffset = currencyManager.getCurrencyTznameAndTzOffset(currcode);
			request.setAttribute("timeZoneOffset", timeZoneOffset);
			log.debug(this.getClass().getName() + " - [update] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");

			request.setAttribute("currencyGroupList", new ArrayList());
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/*
			 * Set the currency group details in request attribute, for the new
			 * value
			 */
			putCurrencyGroupDetails(request, hostId, request
					.getParameter("entityText"));
			request.setAttribute("methodName", "update");
			request.setAttribute("entityText", request
					.getParameter("entityText"));
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("change");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", CurrencyAction.class), request, "");
			return getView("fail");
		} finally {
			/* set the updated interest basis in to the request attribute */

			putInterestBasis(request, request.getParameter("entityText"));

		}
	}

	/**
	 * Method to get the currency details from add currency screen to validate
	 * and return error message if validation fails or save the currency detail
	 * in the data base
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		/* Method's local variable and class instance declaration */
		String entityId = null;
		String hostId;
		String currcode;
		Currency currency;
		SystemFormats sysFormat = null;
		ActionMessages errors = new ActionMessages();
		try {
			log.debug(this.getClass().getName() + " - [save] - " + "Entry");


			currency = getCurrency();
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			/* Condition to check Threshold is null */
			if (currency.getThreshold() == null) {
				/* If it is null set 0.0 as default value */
				currency.setThreshold(0.0);
			}
			/* Retrieves and store the host id from the SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Set the host id in currency bean object */
			currency.getId().setHostId(hostId);
			/* Get the currency code from currency bean object */
			currcode = currency.getId().getCurrencyCode();
			/* Read the entity id from currency .Id */
			entityId = currency.getId().getEntityId();
			request.setAttribute("currencyGroupList", new ArrayList());
			/* Set the currency code to the currency master */
			currency.getCurrencyMaster().setCurrencyCode(currcode);
			/*
			 * Set the multiplier value for the new value through request
			 * attribute
			 */

			putMultiplierInReq(request, entityId);


			/* Save the added values in DB through currency manager */
			currencyManager.saveCurrencyDetail(currency, sysFormat);

			setCurrency(currency);
			request.setAttribute("parentFormRefresh", "yes");
			/*
			 * Set the currency group details in request attribute, for the new
			 * value
			 */
			putCurrencyGroupDetails(request, hostId, currency.getId()
					.getEntityId());
			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "true");

			currency = getCurrency();

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Pass the request parameter to set the currency list in request */
			putCurrencyListInReq(request);
			/*
			 * Set the entity in request attribute for which the new value is
			 * created.
			 */
			putEntityListInReq(request);
			putCurrencyGroupDetails(request, hostId, currency.getId()
					.getEntityId());
			request.setAttribute("entityText", request
					.getParameter("entityText"));
			request.setAttribute("currencyName", request
					.getParameter("currencyName"));
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				errors.add("", new ActionMessage(swtexp.getErrorCode()));
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", CurrencyAction.class), request, "");
			return getView("fail");
		} finally {
			// added to retain currency group in case of error
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/*
			 * Pass the request parameter to set the currency group details in
			 * request attribute
			 */
			putCurrencyGroupDetails(request, hostId, entityId);

			/* Pass the request parameter to set the currency list in request */
			putCurrencyListInReq(request);

			/*
			 * Set the entity in request attribute for which the new value is
			 * created.
			 */
			putEntityListInReq(request);

			/* Set the Interest basis in the request attribute for the new value */

			putInterestBasis(request, entityId);

			request.setAttribute("methodName", "save");
		}
	}

	/**
	 * Method to delete the selected currency detail from the data base and
	 * return the error report if the deleted currency is not exist in the
	 * database
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 *
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		log.debug(this.getClass().getName() + " - [delete] - " + "Entry");
		/* Method's local variable and class instance declaration */
		String currencyCode;
		String hostId;
		String entityId;
		Currency currency;
		SystemFormats systemFormat = null;
		/* Read the selectedCurrencyCode from the request */
		currencyCode = request.getParameter("selectedCurrencyCode");

		currency = getCurrency();

		/* Retrieves and store the host id from the SwtUtil */
		hostId = SwtUtil.getCurrentHostId();
		/* Read the entity from the currency bean */
		entityId = currency.getId().getEntityId();
		/* Set the host id to the Currency bean */
		currency.getId().setHostId(hostId);
		/* Set the currency code to the Currency bean */
		currency.getId().setCurrencyCode(currencyCode);
		systemFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
		try {

			/* Delete the selected row from the DB */
			currencyManager.deleteCurrencyDetail(currency);
			log.debug(this.getClass().getName() + " - [delete] - " + "Exit");
			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", CurrencyAction.class), request, "");

			return getView("fail");
		} finally {
			/* Condition to check entity is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* The current entity of the user is get from Swt util */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id to the currency bean */
				currency.getId().setEntityId(entityId);
			}
			/*
			 * Pass the request attribute to set the entity list in request
			 * attribute
			 */
			putEntityListInReq(request);
			/*
			 * Pass the request attribute to set the currency list in request
			 * attribute
			 */
			putCurrencyListandDetails(request, hostId, entityId);
			/*
			 * Pass the request attribute to set the button status based on the
			 * access id
			 */
			putEntityAccessInReq(request, entityId);
			/*
			 * Pass the request attribute to set the Interest basis in request
			 * attribute
			 */

			putInterestBasis(request, entityId);

			setCurrency(currency);

		}
	}

	/**
	 * Method to collect the currency list from cache manager and set the
	 * currency list to the request attribute
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 *
	 */
	private void putCurrencyListInReq(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putCurrencyListInReq] - "
				+ "Entry");
		/* Method's local variable declaration */
		Collection coll;
		/* Collect the currencies from cache manager */
		coll = CacheManager.getInstance().getCurrencies();
		request.setAttribute("currencyMaster", coll);
		log.debug(this.getClass().getName() + " - [putCurrencyListInReq] - "
				+ "Exit");
	}

	/**
	 * Method to get the list of entities from SwtUtil that are accessed by the
	 * user and set them in to the request attribute
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");

		/* Method's local variable declaration */
		HttpSession session;
		Collection coll;
		session = request.getSession();
		/* Collects the list of entity for the user */
		coll = SwtUtil.getUserEntityAccessList(session);
		/* Collects the label value bean for entity name and entity id */
		coll = SwtUtil.convertEntityAcessCollectionLVLFullName(coll, session);
		request.setAttribute("entities", coll);
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");

	}

	/**
	 * Set the status for the buttons in the screen through request attribute
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @param saveStatus
	 * @param cancelStatus
	 * @return
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String saveStatus,
								 String cancelStatus) {
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/**
	 * Get the multiplier from label value bean and put in the request attribute
	 *
	 * @param req
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	private void putMultiplierInReq(HttpServletRequest request, String entityId)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putMultiplierInReq] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		int index = 0;
		Collection multiplierColl;
		Iterator multiplierItr;
		LabelValueBean L1;
		LabelValueBean L2;
		LabelValueBean L3;
		LabelValueBean L4;
		MiscParams miscParams;
		/* Collect the multiplier list from the Cache manager */
		multiplierColl = (Collection) CacheManager.getInstance().getMiscParams(
				"CURRENCYMULTIPLIER", entityId);

		/* Iterate the multiplierlist */
		multiplierItr = multiplierColl.iterator();
		multiplierColl = new ArrayList();
		L1 = new LabelValueBean("", "");
		L2 = new LabelValueBean("", "");
		L3 = new LabelValueBean("", "");
		L4 = new LabelValueBean("", "");
		/* Loop to set the label vale bean of multiplier values */
		while (multiplierItr.hasNext()) {
			miscParams = (MiscParams) (multiplierItr.next());
			if (index == 0) { // Billion

				L4 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 1) { // Million
				L3 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 2) { // None
				L1 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());
			} else if (index == 3) { // Thousand
				L2 = new LabelValueBean(miscParams.getParValue().trim(),
						miscParams.getId().getKey2().trim());

			}
			index++;
		}

		multiplierColl.add(L1); // None
		multiplierColl.add(L2); // Thousand
		multiplierColl.add(L3); // Million
		multiplierColl.add(L4); // Billion
		request.setAttribute("multiplierList", multiplierColl);
		log.debug(this.getClass().getName() + " - [putMultiplierInReq] - "
				+ "Exit");

	}

	/**
	 * This method is used to export the data for Currency
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public String exportCurrency()
			throws SwtException {

		/* Method's local variable and class instance declaration */
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currency
		Currency currency;
		// Variable to hold exportType
		String exportType = null;
		// Variable to hold fileName
		String fileName = null;
		// Variable to hold titleSuffix
		String titleSuffix = null;
		// Variable to hold totalCount
		int totalCount = 0;
		// Variable to hold columnData
		ArrayList<ColumnDTO> columnData = null;
		// Variable to hold cDTO
		ColumnDTO cDTO = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// Variable to hold fDTO
		FilterDTO fDTO = null;
		// Variable to hold obj2XmlCurrency
		Obj2XmlCurrency obj2XmlCurrency = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		// Variable to hold excelGen
		Obj2XlsImpl excelGen = null;
		// Variable to hold pdfGen
		Obj2PdfImpl pdfGen = null;
		// Variable to hold csvGen
		Obj2CsvImpl csvGen = null;
		//
		ArrayList<ArrayList<ExportObject>> data = null;
		SystemFormats sysformat;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug(this.getClass().getName() + " - [exportCurrency] - "
					+ "Entry");

			currency = getCurrency();

			CurrencyDetailVO currencyDetailsVO;
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			/* Read the export type from request */
			exportType = request.getParameter("exportType");
			/* Condition to check currency is null */
			if (request.getParameter("id.currencyCode") == null) {
				/* Setting the currency using bean class */
				currency.getId().setCurrencyCode("All");
			} else {
				/* Read and setting the currency */
				currency.getId().setCurrencyCode(
						request.getParameter("id.currencyCode"));
			}
			// Setting currency for form
			setCurrency(currency);
			/* Retrieve and store the host id from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Retrieve the entity id from currency bean */
			entityId = currency.getId().getEntityId();
			/* Set the host id to the currency bean */
			currency.getId().setHostId(hostId);
			/* Condition to check entity is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * If it is null the current entity of the user is set by
				 * SwtUtil
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Set the entity id to the currency bean */
				currency.getId().setEntityId(entityId);
			}
			/*
			 * Pass the request parameter to set the entity access list in to
			 * the request attribute
			 */
			putEntityListInReq(request);

			/*
			 * Get the currency detail list from the currency manager and
			 * returns it as CurrencyDetailVO
			 */
			currencyDetailsVO = currencyManager.getCurrencyDetailList(entityId,
					hostId, "All", sysformat);
			// Getting Currency list value
			request.setAttribute("currencyList", currencyDetailsVO
					.getCurrencyList());
			// Getting Currency list details value
			request.setAttribute("currencyListDetails", currencyDetailsVO
					.getCurrencyListDetails());
			/* Set the button status based on the access id */
			totalCount = currencyDetailsVO.getCurrencyListDetails().size();
			// Setting totalCount
			request.setAttribute("totalCount", totalCount);
			// Setting Entity Accesslist in request
			putEntityAccessInReq(request, entityId);

			/* To set the column headings */
			columnData = new ArrayList<ColumnDTO>();

			/* to set the column values in export format */
			cDTO = new ColumnDTO();
			cDTO.setHeading("Ccy");
			cDTO.setType("str");
			cDTO.setDataElement("ccy");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Name");
			cDTO.setType("str");
			cDTO.setDataElement("name");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Ccy Group");
			cDTO.setType("str");
			cDTO.setDataElement("ccygroup");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Order");
			cDTO.setType("num");
			cDTO.setDataElement("order");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Exch Rate");
			cDTO.setType("num");
			cDTO.setDataElement("exchrate");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Multiplier");
			cDTO.setType("str");
			cDTO.setDataElement("multiplier");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Threshold(M)");
			cDTO.setType("num");
			cDTO.setDataElement("threshold");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Int Bas");
			cDTO.setType("num");
			cDTO.setDataElement("intbas");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Tol");
			cDTO.setType("num");
			cDTO.setDataElement("tol");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Cut Off");
			cDTO.setType("num");
			cDTO.setDataElement("cutoff");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Predict");
			cDTO.setType("str");
			cDTO.setDataElement("predict");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Time Zone Offset");
			cDTO.setType("str");
			cDTO.setDataElement("currencyMaster.ccyTimeZone");
			columnData.add(cDTO);


			cDTO = new ColumnDTO();
			cDTO.setHeading("Currency Offset");
			cDTO.setType("str");
			cDTO.setDataElement("gmtOffset");
			columnData.add(cDTO);


			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			/* Set and display the data in filter column in export format */
			fDTO.setName("Entity");
			fDTO.setValue(entityId);
			filterData.add(fDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");

			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}
			obj2XmlCurrency = new Obj2XmlCurrency();
			data  = obj2XmlCurrency.getExportData(columnData
					, filterData, (ArrayList)  currencyDetailsVO.getCurrencyListDetails());
			/* To export the data in PDF,Excel and CSV format */
			if (exportType.equalsIgnoreCase("excel")) {
				excelGen = new Obj2XlsImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".xls");
				excelGen.convertObject(request, response, columnData
						, filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("pdf")) {
				pdfGen = new Obj2PdfImpl();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate()+ ".pdf");
				pdfGen.convertObject(request, response, columnData
						, filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("csv")) {
				csvGen = new Obj2CsvImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".csv");
				csvResponse = csvGen.convertObject(request, columnData
						, filterData, data, null,null, fileName);
				/*
				 * End Code modified by Chidambaranathan for include timestamp
				 * in the export function for Mantis_1513 on 04-Aug-2011
				 */
				try (ServletOutputStream out = response.getOutputStream()) {
					out.print(csvResponse);
				}
			}
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [exportCurrency] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [exportCurrency] method : - "
					+ exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "exportCurrency", CurrencyAction.class), request, "");
		} finally {
			// Nullifying the already craeted objects
			hostId = null;
			entityId = null;
			exportType = null;
			fileName = null;
			titleSuffix = null;
			columnData = null;
			pdfGen = null;
			csvGen = null;
			excelGen = null;
			cDTO = null;
			filterData = null;
			fDTO = null;
			obj2XmlCurrency = null;
			csvResponse = null;
		}
		return null;
	}

	/**
	 * This method is used to check the currency access
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public String checkCurrencyAccess() throws SwtException {
		// variable to hold the hostId
		String hostId = null;
		// variable to hold the currencyId
		String currencyId = null;
		// variable to hold the entityId
		String entityId = null;
		// variable to hold the accountId
		String accountId = null;
		// variable to hold the currency access
		int access = 0;
		// Declare AcctMaintenanceManager object
		AcctMaintenanceManager acctMaintenanceManager = null;
		// Declare AcctMaintenance object
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		AcctMaintenance acctMaintenance = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [checkCurrencyAccess] - Entering ");
			// Get the hostId
			hostId = CacheManager.getInstance().getHostId();
			// Get the currencyId
			currencyId = request.getParameter("currencyId");
			// Get the accountId
			accountId = request.getParameter("accountId");
			// Get the entityId
			entityId = request.getParameter("entityId");
			if (!SwtUtil.isEmptyOrNull(accountId)) {
				// get the AcctMaintenanceManager bean
				acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
						.getBean("acctMaintenanceManager");
				// Get the acctMaintenance object
				acctMaintenance = acctMaintenanceManager.copyAccountIdDetails(
						hostId, entityId, accountId, null, null);
				// get the currency id
				currencyId = acctMaintenance.getCurrcode();
			}
			// get the currency group access
			access = SwtUtil.getCcyGrpAccessType(request, hostId, entityId,
					currencyId);
			// set the access to response
			response.getWriter().print(access);
			log.debug(this.getClass().getName()
					+ " - [checkCurrencyAccess] - Exit");
			return null;
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [checkCurrencyAccess] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [checkCurrencyAccess] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "checkCurrencyAccess", CurrencyAction.class), request,
					"");
			return getView("fail");
		} finally {
			acctMaintenanceManager = null;
			acctMaintenance = null;
			accountId = null;
			currencyId = null;
			hostId = null;
			entityId = null;
		}
	}

	/**
	 *  Used to clean session values
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String clearSessionValues() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					+ "- [clearSessionValues] - Enter");

			// Sending the result
			response.getWriter().print("true");
			log.debug(this.getClass().getName() + " - [clearSessionValues] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [clearSessionValues] method : - "
					+ exp.getMessage());
			return getView("fail");
		}
	}









	/**
	 * This method is used to check the currency access
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public String getAppropriateCurrencyOffset() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// variable to hold the hostId
		String currencyCode = null;
		// variable to hold the currency access
		String timeZoneOffset = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getAppropriateCurrencyOffset] - Entering ");
			/* Read the value of entityCode from the request */
			currencyCode = request.getParameter("currencyCode");
			// get the currency offset
			timeZoneOffset = currencyManager.getCurrencyTznameAndTzOffset(currencyCode);
			// set the access to response
			response.getWriter().print(timeZoneOffset);
			log.debug(this.getClass().getName()
					+ " - [getAppropriateCurrencyOffset] - Exit");
			return null;
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getAppropriateCurrencyOffset] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getAppropriateCurrencyOffset] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getAppropriateCurrencyOffset", CurrencyAction.class), request,
					"");
			return getView("fail");
		} finally {
			currencyCode = null;
			timeZoneOffset = null;
		}
	}
}