package org.swallow.maintenance.web.form;

public class ILMCalculationForm {
	
	private String entityId;
	private String currencyCode;
	private String processOption;
	private String singleOrRange;
	private String fromDateAsString;
	private String toDateAsString;
	
	
	
	
	public String getEntityId() {
		return entityId;
	}
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getProcessOption() {
		return processOption;
	}
	public void setProcessOption(String processOption) {
		this.processOption = processOption;
	}
	public String getSingleOrRange() {
		return singleOrRange;
	}
	public void setSingleOrRange(String singleOrRange) {
		this.singleOrRange = singleOrRange;
	}
	public String getFromDateAsString() {
		return fromDateAsString;
	}
	public void setFromDateAsString(String fromDateAsString) {
		this.fromDateAsString = fromDateAsString;
	}
	public String getToDateAsString() {
		return toDateAsString;
	}
	public void setToDateAsString(String toDateAsString) {
		this.toDateAsString = toDateAsString;
	}
	
	
	

}
