/*
 * @(#)ILMTransScenarioMaintenanceActio.java 1.0 29/11/2013
 *
 * Copyright (c) 2006-2014 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.model.ILMTransactionSetDTL;
import org.swallow.maintenance.model.ILMTransactionSetHDR;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.model.User;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
























import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/ilmTransScenario", "/ilmTransScenario.do"})
public class ILMTransScenarioMaintenanceAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("changeScenario", "jsp/maintenance/ilmscenariosmaintenanceadd");
		viewMap.put("changeTransactionSet", "jsp/maintenance/ilmtransactionsetdisplay");
		viewMap.put("fail", "error");
		viewMap.put("addScenario", "jsp/maintenance/ilmscenariosmaintenanceadd");
		viewMap.put("viewTransaction", "jsp/maintenance/ilmtransactionsetdisplay");
		viewMap.put("addTransactionSet", "jsp/maintenance/ilmtransactionsetdisplay");
		viewMap.put("listScenarios", "jsp/maintenance/ilmscenariosmaintenance");
		viewMap.put("listTransactionSets", "jsp/maintenance/ilmtransactionsetmaintenance");
		viewMap.put("updateScenario", "jsp/maintenance/ilmscenariosmaintenanceadd");
		viewMap.put("addTransactionDetail", "jsp/maintenance/ilmtransactionsetdisplayadd");
		viewMap.put("viewScenario", "jsp/maintenance/ilmscenariosmaintenanceadd");
		viewMap.put("changeTransactionDetail", "jsp/maintenance/ilmtransactionsetdisplayadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");









































	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws Exception {
		method = String.valueOf(method);
		switch (method) {
			case "listScenarios":
				return listScenarios();
			case "listTransactionSets":
				return listTransactionSets();
			case "addTransactionSet":
				return addTransactionSet();
			case "changeTransactionSet":
				return changeTransactionSet();
			case "addTransactionDetail":
				return addTransactionDetail();
			case "saveTransaction":
				return saveTransaction();
			case "viewTransaction":
				return viewTransaction();
			case "updateTransaction":
				return updateTransaction();
			case "deleteTransaction":
				return deleteTransaction();
			case "changeTransactionDetail":
				return changeTransactionDetail();
			case "getEntityCurrencyAccessForScenario":
				return getEntityCurrencyAccessForScenario();
			case "addScenario":
				return addScenario();
			case "saveScenario":
				return saveScenario();
			case "changeScenario":
				return changeScenario();
			case "updateScenario":
				return updateScenario();
			case "viewScenario":
				return viewScenario();
			case "deleteScenario":
				return deleteScenario();
			case "checkTransAccess":
				return checkTransAccess();
			case "testFilterCondtionClause":
				return testFilterCondtionClause();
			case "checkPossibilityToDelete":
				return checkPossibilityToDelete();
		}


		return null;
	}


	private ILMScenario ilmScenario;
	public ILMScenario getIlmScenario() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmScenario = RequestObjectMapper.getObjectFromRequest(ILMScenario.class, request);
		return ilmScenario;
	}

	public void setIlmScenario(ILMScenario ilmScenario) {
		this.ilmScenario = ilmScenario;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmScenario", ilmScenario);
	}


	private ILMTransactionSetHDR ilmTransactionSetHDR;

	public ILMTransactionSetHDR getIlmTransactionSetHDR() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmTransactionSetHDR = RequestObjectMapper.getObjectFromRequest(ILMTransactionSetHDR.class, request);
		return ilmTransactionSetHDR;
	}

	public void setIlmTransactionSetHDR(ILMTransactionSetHDR ilmTransactionSetHDR) {
		this.ilmTransactionSetHDR = ilmTransactionSetHDR;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmTransactionSetHDR", ilmTransactionSetHDR);
	}


	private ILMTransactionSetDTL ilmTransactionSetDTL;

	public ILMTransactionSetDTL getIlmTransactionSetDTL() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ilmTransactionSetDTL = RequestObjectMapper.getObjectFromRequest(ILMTransactionSetDTL.class, request);
		return ilmTransactionSetDTL;
	}

	public void setIlmTransactionSetDTL(ILMTransactionSetDTL ilmTransactionSetDTL) {
		this.ilmTransactionSetDTL = ilmTransactionSetDTL;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("ilmTransactionSetDTL", ilmTransactionSetDTL);
	}



	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(ILMTransScenarioMaintenanceAction.class);

	private Set<ILMTransactionSetDTL> ilmTransactionTOAdd = null;
	private Set<ILMTransactionSetDTL> ilmTransactionTOChange = null;
	private Set<ILMTransactionSetDTL> ilmTransactionTODelete = null;

	/**
	 * Used to hold ILMTransScenarioMaintenanceManager reference object
	 */
	@Autowired
	private ILMTransScenarioMaintenanceManager ilmTransScenarioMaintenanceManager = null;

	/**
	 * @param ilmTransScenarioMaintenanceManager
	 *            the ilmTransScenarioMaintenanceManager to set
	 */
	public void setIlmTransScenarioMaintenanceManager(
			ILMTransScenarioMaintenanceManager ilmTransScenarioMaintenanceManager) {
		this.ilmTransScenarioMaintenanceManager = ilmTransScenarioMaintenanceManager;
	}

	/**
	 * This method gets entity list from database and put them in request scope
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request, boolean fullAccess, boolean... withAll)
			throws SwtException {

		// Entity collection
		Collection entityColl = new ArrayList<EntityUserAccess>();
		Collection entityColl1 = new ArrayList<EntityUserAccess>();
		LabelValueBean allEntities = null;

		try {
			// Get the user access entity list .
			entityColl = SwtUtil.getUserEntityAccessList(request.getSession());

			if (withAll.length == 0 || withAll[0]) {
				// Convert the entity list to label value bean.
				allEntities = new LabelValueBean("All Entities", "All");
				entityColl1.add(allEntities);
			}

			if(fullAccess) {
				for (Iterator iterator = entityColl.iterator(); iterator.hasNext();) {
					EntityUserAccess type = (EntityUserAccess) iterator.next();
					if(type.getAccess() != 0)
						iterator.remove();

				}
			}

			entityColl1.addAll(SwtUtil.convertEntityAcessCollectionLVL(entityColl,
					request.getSession()));

			// Set the entity collection into request.
			request.setAttribute("entities", entityColl1);
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - [putEntityListInReq] - Exception -"
					+ exp.getMessage());
		} finally {
			// Nullify objects
			entityColl = null;
		}
	}

	/**
	 * Method to define the Button status in the screen
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param viewStatus
	 * @param deleteStatus
	 * @param roleStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String viewStatus, String deleteStatus,
								 String saveStatus, String roleStatus) {

		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.VIEW_BUT_STS, viewStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		req.setAttribute(SwtConstants.SAV_BUT_STS, saveStatus);
		req.setAttribute(SwtConstants.ROLL_BUT_STS, roleStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/**
	 * Default method of the action returns List
	 * @return ActionForward
	 * @throws Exception
	 */
	public String unspecified()
			throws Exception {
		log.debug(this.getClass().getName() + " - [unspecified] - "
				+ "Return list Action");
		return listScenarios();
	}

	/**
	 * List all Scenarios from database
	 * @return ActionForward
	 * @throws Exception
	 */
	public String listScenarios()
			throws Exception {
		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		ILMScenario ilmScenariosMaintenance= null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [listScenarios] - "
					+ "Entry");

			/* Method's class instance and local variable declaration */
			ArrayList scenarioListDetail;

			/*
			 * Struts Framework built-in Class used to set and get the Form(JSP)
			 * values
			 */


			ilmScenariosMaintenance = getIlmScenario();



			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve Entity Id */
			entityId = ilmScenariosMaintenance.getEntityId();
			/* Retrieve Currency Code */
			currencyCode = ilmScenariosMaintenance.getCurrencyCode();
			/* Retrieve Host Id */
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				// Get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			}

			/* Collect the Scenario list in scenarioMaintenanceDetailVO */
			scenarioListDetail = ilmTransScenarioMaintenanceManager
					.getILMScenarioList(hostId, entityId, currencyCode, request);

			Iterator itr = scenarioListDetail.iterator();
			ILMScenario  ilmScenario = new ILMScenario();

			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in Scenario bean
				ilmScenario = (ILMScenario) (itr.next());
				if("*".equalsIgnoreCase(ilmScenario.getEntityId()))
					ilmScenario.setEntityId("All entities");
				if("*".equalsIgnoreCase(ilmScenario.getCurrencyCode()))
					ilmScenario.setCurrencyCode("All currencies");
			}
			/* Retrieve User's Menu,Entity and Currency Group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			if (accessInd == 0) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			ilmScenariosMaintenance.setEntityId(entityId);
			ilmScenariosMaintenance.setCurrencyCode(currencyCode);

			// Put the currencies list in the form
			/*request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, true));
			putEntityListInReq(request, true);*/
			putEntityListInReq(request, true);
			if("All".equalsIgnoreCase(entityId)){
				Collection	entityColl = SwtUtil.getUserEntityAccessList(request
						.getSession());
				Iterator itrEntity = entityColl.iterator();
				Set<LabelValueBean> set = new TreeSet<>(new Comparator<LabelValueBean>() {
					public int compare(LabelValueBean lvb1, LabelValueBean lvb2) {
						if (lvb1.getValue().equals("All") && !lvb2.getValue().equals("All")) {
							return -1; // lvb1 should come before lvb2
						} else if (lvb2.getValue().equals("All") && !lvb1.getValue().equals("All")) {
							return 1; // lvb2 should come before lvb1
						} else {
							return lvb1.getValue().compareTo(lvb2.getValue()); // compare normally
						}
					}
				});
				while (itrEntity.hasNext()) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity.next();
					Collection ccyList = getCurrencyList(request, hostId, entity.getEntityId(), true, false);
					if (ccyList != null) {
						Iterator itrCcy = ccyList.iterator();


						while (itrCcy.hasNext()) {
							LabelValueBean currency = (LabelValueBean) itrCcy.next();
							set.add(currency);
						}
					}
				}
				request.setAttribute("currencyList",set);
			}else
				request.setAttribute("currencyList",
						getCurrencyList(request, hostId, entityId, true, false));

			request.setAttribute("ilmScenariosDetails", scenarioListDetail);
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedCurrencyCode", currencyCode);
			setIlmScenario(ilmScenariosMaintenance);

			log.debug(this.getClass().getName() + " - [listScenarios] - "
					+ "Exit");
			return getView("listScenarios");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listScenarios] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listScenarios] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listScenarios] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listScenarios] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"listScenarios",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * List all Transaction Sets from database
	 * @return ActionForward
	 * @throws Exception
	 */
	public String listTransactionSets() throws Exception {
		int accessInd;
		String hostId = null;
		String entityId = null;
		String menuAccessId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ILMTransactionSetHDR ilmTransactionSetHDR = null;
		try {
			log.debug(this.getClass().getName() + " - [listTransactionSets] - "
					+ "Entry");

			/* Method's class instance and local variable declaration */
			ArrayList transactionSetListDetail;

			menuAccessId = request.getParameter("menuAccessId");

			ilmTransactionSetHDR = getIlmTransactionSetHDR();




			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			/* Retrieve Entity Id */
			entityId = ilmTransactionSetHDR.getId().getEntityId();
			/* Retrieve Host Id */
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			/* Collect the Scenario list in scenarioMaintenanceDetailVO */
			transactionSetListDetail = ilmTransScenarioMaintenanceManager
					.getTransactionList(hostId, null, null, request);

			if (menuAccessId.equals("0")) {
				/* This is used to set the button status by enable all buttons */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				/* This is used to set the button status by disable all buttons */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}


			ilmTransactionSetHDR.getId().setEntityId(entityId);

			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, true, false));

			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			setIlmTransactionSetHDR(ilmTransactionSetHDR);

			log.debug(this.getClass().getName() + " - [listTransactionSets] - "
					+ "Exit");
			return getView("listTransactionSets");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listTransactionSets] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listTransactionSets] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [listTransactionSets] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [listTransactionSets] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"listTransactionSets",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * List all Transaction Sets from database
	 *
	 * @return ActionForward
	 * @throws Exception
	 */
	public String addTransactionSet() throws Exception {
		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		Collection<ILMTransactionSetDTL> transactionSetListDetail;
		ILMTransactionSetHDR ilmTransactionSetHDR= null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [addTransactionSet] - "
					+ "Entry");


			ilmTransactionSetHDR = getIlmTransactionSetHDR();




			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);

			// Retrieve Entity Id
			entityId = ilmTransactionSetHDR.getId().getEntityId();
			// Retrieve currency code
			currencyCode = ilmTransactionSetHDR.getId().getCurrencyCode();
			// Retrieve transaction set id
			txnSetId = ilmTransactionSetHDR.getId().getTxnSetId();
			/* Retrieve Host Id */
			hostId = SwtUtil.getCurrentHostId();

			if (SwtUtil.isEmptyOrNull(entityId)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						hostId, entityId);
			}
			transactionSetListDetail = ilmTransScenarioMaintenanceManager
					.getTransactionDetailsList(hostId, entityId, currencyCode,
							txnSetId);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			ilmTransactionSetHDR.getId().setEntityId(entityId);
			ilmTransactionSetHDR.getId().setCurrencyCode(currencyCode);

			putEntityListInReq(request, true);
			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, true));
			request.setAttribute("methodName", "addTransactionSet");

			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			log.debug(this.getClass().getName() + " - [addTransactionSet] - "
					+ "Exit");
			return getView("addTransactionSet");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [addTransactionSet] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [addTransactionSet] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [addTransactionSet] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [addTransactionSet] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"addTransactionSet",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * List all Transaction Sets from database
	 * @return ActionForward
	 * @throws Exception
	 */
	public String changeTransactionSet() throws Exception {
		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		String entityName = null;
		String currencyName = null;
		Collection<ILMTransactionSetDTL> transactionSetListDetail;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		ILMTransactionSetHDR ilmTransactionSetHDR= null;
		try {
			log.debug(this.getClass().getName()
					+ " - [changeTransactionSet] - " + "Entry");


			ilmTransactionSetHDR = getIlmTransactionSetHDR();



			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve transaction set id
			txnSetId = request.getParameter("selectedTransactionId");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			ilmTransactionSetHDR = ilmTransScenarioMaintenanceManager
					.getEditableTransactionHDRData(hostId, entityId,
							currencyCode, txnSetId);
			Iterator itr1 =  ilmTransactionSetHDR.getIlmTransactionSetDTLs().iterator();
			ILMTransactionSetDTL  transactionset = null;
			while (itr1.hasNext()) {
				transactionset = (ILMTransactionSetDTL ) itr1.next();
				transactionset.setDebitsAsString(SwtUtil.formatCurrency(
						transactionset.getId().getCurrencyCode(), transactionset
								.getDebits()));
				transactionset.setCreditsAsString(SwtUtil.formatCurrency(
						transactionset.getId().getCurrencyCode(), transactionset
								.getCredits()));
			}
			//Get the currencyName
			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, false, true),
					currencyCode);
			// Convert the entity list to label value bean.
			Collection entityColl = SwtUtil.convertEntityAcessCollectionLVL(SwtUtil.getUserEntityAccessList(request.getSession()),
					request.getSession());
			//Get the entityName
			entityName = getLabelFromValue(entityColl, entityId);
			putEntityListInReq(request, true);
			// Put the required attributes in the form
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("entityText", entityName);
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, true));
			request.setAttribute("methodName", "changeTransactionSet");
			request.setAttribute("ilmTransactionSetsDetails",
					ilmTransactionSetHDR.getIlmTransactionSetDTLs());
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			log.debug(this.getClass().getName()
					+ " - [changeTransactionSet] - " + "Exit");
			return getView("changeTransactionSet");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeTransactionSet] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeTransactionSet] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeTransactionSet] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeTransactionSet] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"addTransactionSet",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * This method is used to add a new ILM Transaction detail for ILM
	 * monitoring.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addTransactionDetail() throws SwtException {

		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		Collection<LabelValueBean> accountList = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [addTransactionDetail] - "
					+ "Entry");

			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve Currency Code
			currencyCode = request.getParameter("selectedCurrencyCode");

			if (SwtUtil.isEmptyOrNull(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			if (SwtUtil.isEmptyOrNull(currencyCode))
				// Get the domesticCurrency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);
			accountList = putDataSetAcctListInReq(
					entityId,  currencyCode);
			// Put the required attributes in the form
			request.setAttribute("currencyCode", currencyCode);
			request.setAttribute("accountList", accountList);
			request.setAttribute("methodName", "addTransactionDetail");

			log.debug(this.getClass().getName()
					+ " - [addTransactionDetail] - " + "Exit");

			return getView("addTransactionDetail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addTransactionDetail] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"addTransactionDetail",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	private Collection<LabelValueBean> putDataSetAcctListInReq(
			String entityId, String currencyCode) throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		Collection<LabelValueBean> acountsLVB = null;
		Iterator<AcctMaintenance> itrAcct = null;
		AcctMaintenance account = null;
		Collection<AcctMaintenance> colAccount = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [putDataSetAcctListInReq] - " + "Entry");

			// Initialize collection to hold ilmAccountGroup detail as LabelValueBean object
			ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = (ILMGeneralMaintenanceManager) SwtUtil
					.getBean("ilmGeneralMaintenanceManager");
			acountsLVB = new ArrayList<LabelValueBean>();
			colAccount = ilmGeneralMaintenanceManager
					.getAccountListDetails(entityId, currencyCode);
			// Iterate through ilmAccount list and get LabelValueBean list
			if (colAccount != null) {
				itrAcct = colAccount.iterator();
				while (itrAcct.hasNext()) {
					account = (AcctMaintenance) itrAcct.next();

					acountsLVB.add(new LabelValueBean(account
							.getAcctname(), account.getId().getAccountId()));
				}
			}

		} catch (Exception ex) {
			// Log error message
			log.error("ILMGeneralMaintenanceAction - [putDataSetAcctListInReq] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"putDataSetGlobalCcyAcctGroupListInReq",
					ILMGeneralMaintenanceAction.class);
		} finally {
			// Nullify objects
			itrAcct = null;
			account = null;
			// log debug message
			log.debug("ILMGeneralMaintenanceAction - [putDataSetAcctListInReq] - Exit");
		}
		return acountsLVB;

	}

	/**
	 *  Used to treat XMl posted by the JS side
	 * @param xmlStr
	 * @param entityId
	 * @param currencyCode
	 * @param txnSetId
	 */
	private void XMLTreatment(String xmlStr, String entityId,
							  String currencyCode, String txnSetId,SystemFormats formats) {

		String operationName = null;
		String accountId = null;
		String time = null;
		String description = null;
		ILMTransactionSetDTL ilmTransactionSetDTL = null;

		try {
			if(!SwtUtil.isEmptyOrNull(xmlStr)) {
				Document doc = SwtUtil.convertStringToDocument(xmlStr);
				doc.getDocumentElement().normalize();

				NodeList nList = doc.getElementsByTagName("transaction");

				for (int i = 0; i < nList.getLength(); i++) {

					Node node = nList.item(i);

					if (node.getNodeType() == Node.ELEMENT_NODE) {

						Element element = (Element) node;
						//Get the operation name
						operationName = element
								.getElementsByTagName(SwtConstants.OPERATION)
								.item(0).getTextContent().trim();

						if (!operationName.equals("delete")) {
							/* initialise the ILMTransaction object  to be added or updated */
							accountId = element
									.getElementsByTagName(SwtConstants.ACCOUNT_ID)
									.item(0).getTextContent().trim();
							time = element.getElementsByTagName(SwtConstants.TIME)
									.item(0).getTextContent().trim();

							String creditValue = element
									.getElementsByTagName(SwtConstants.TRANS_CREDIT)
									.item(0).getTextContent().trim();
							String debitValue = element
									.getElementsByTagName(SwtConstants.TRANS_DEBIT)
									.item(0).getTextContent().trim();

							ilmTransactionSetDTL = new ILMTransactionSetDTL();
							//initialise the required fields of  ILMTransaction object
							ilmTransactionSetDTL.getId().initPrams(
									SwtUtil.getCurrentHostId(), entityId,
									currencyCode, txnSetId, accountId, time);
							// Set the credit
							if (creditValue.equalsIgnoreCase("")
									|| creditValue.equals("0")) {
								ilmTransactionSetDTL.setCredits(new Double(0.0));
							} else {
								ilmTransactionSetDTL.setCredits(SwtUtil.parseCurrency(creditValue,formats
										.getCurrencyFormat()));
							}
							// Set the debit
							if (debitValue.equalsIgnoreCase("")
									|| creditValue.equals("0")) {
								ilmTransactionSetDTL.setDebits(new Double(0.0));
							} else {
								ilmTransactionSetDTL.setDebits(SwtUtil.parseCurrency(debitValue,formats
										.getCurrencyFormat()));
							}

							// Set the description
							description = element
									.getElementsByTagName(SwtConstants.DESCRIPTION)
									.item(0).getTextContent().trim();
							ilmTransactionSetDTL.setDescription(description);
							if (operationName.equals("add"))
								ilmTransactionTOAdd.add(ilmTransactionSetDTL);
							else
								ilmTransactionTOChange.add(ilmTransactionSetDTL);

						} else {/* initialise the ILMTransaction object  to be deleted */
							accountId = element.getElementsByTagName("accountid")
									.item(0).getTextContent().trim();
							time = element.getElementsByTagName("time").item(0)
									.getTextContent().trim();

							ilmTransactionSetDTL = new ILMTransactionSetDTL();
							ilmTransactionSetDTL.getId().initPrams(
									SwtUtil.getCurrentHostId(), entityId,
									currencyCode, txnSetId, accountId, time);

							ilmTransactionTODelete.add(ilmTransactionSetDTL);
						}

					}
				}
			}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [XMLTreatment] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"XMLTreatment",
							ILMTransScenarioMaintenanceAction.class), null, "");
		}
	}
	/**
	 * Used to save a new transaction
	 * @return
	 * @throws SwtException
	 */
	public String saveTransaction() throws SwtException {
		/* Method's local variable and class instance declaration */
		ILMTransactionSetHDR ilmTransactionSetHDR = null;
		ActionMessages errors = null;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		String txnSetName = null;
		String XMLTransactions = null;
		Collection<ILMTransactionSetDTL> transactionSetListDetail = null;
		SystemFormats formats = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [saveTransaction] - "
					+ "Entry");

			ilmTransactionSetHDR = getIlmTransactionSetHDR();



			ilmTransactionSetHDR.getId().setHostId(SwtUtil.getCurrentHostId());
			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve transaction set id
			txnSetId = request.getParameter("transactionId");
			// Retrieve transaction set id
			txnSetName = request.getParameter("transactionName");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Read the current system formats from SwtUtil file
			formats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Retrieve XMLTransactions from js side */
			XMLTransactions = request.getParameter("XMLTransactions");
			XMLTransactions = SwtUtil.decode64(XMLTransactions);
			if (!SwtUtil.isEmptyOrNull(XMLTransactions)) {
				ilmTransactionTOAdd = new HashSet<ILMTransactionSetDTL>();
				// Treat XMLTransation to get the new Transactions to be added
				XMLTreatment(XMLTransactions, entityId, currencyCode, txnSetId ,formats);
			}

			ilmTransactionSetHDR.getId().setEntityId(entityId);
			ilmTransactionSetHDR.getId().setCurrencyCode(currencyCode);
			ilmTransactionSetHDR.getId().setTxnSetId(txnSetId);
			ilmTransactionSetHDR.setTxnSetName(txnSetName);

			// Collect the transaction details list
			transactionSetListDetail = ilmTransScenarioMaintenanceManager
					.getTransactionDetailsList(hostId, entityId, currencyCode,
							txnSetId);
			// Save the ILM Transaction
			ilmTransScenarioMaintenanceManager.saveILMTransaction(
					ilmTransactionSetHDR, ilmTransactionTOAdd);

			// Put the required attributes in the request

			/* If parentFromRefresh value is 'yes', then we will close the
			 opened screen and refresh the parent screen */
			request.setAttribute("parentFormRefresh", "yes");
			putEntityListInReq(request, true);
			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, true));
			// Put the transaction details list
			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));

			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveTransaction] - "
					+ "Exit");
			return getView("addTransactionSet");
		} catch (SwtException swtexp) {
			ilmTransactionSetHDR = getIlmTransactionSetHDR();
			request.setAttribute("methodName", "addTransactionSet");
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			ilmTransactionSetHDR.getId().setEntityId(entityId);
			ilmTransactionSetHDR.getId().setCurrencyCode(currencyCode);
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveTransaction] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, true));
			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			putEntityListInReq(request, true);
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			return getView("addTransactionSet");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveTransaction] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"listTransactionSetDetails",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			return getView("fail");
		} finally {
			// Nullify objects
			ilmTransactionTOAdd = null;

		}
	}

	/**
	 * Method called when view Button is clicked ILM Scenario Maintenance
	 * @return ActionForward
	 * @throws Exception
	 */
	public String viewTransaction()
			throws Exception {

		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		String entityName = null;
		String currencyName = null;
		Collection<ILMTransactionSetDTL> transactionSetListDetail;
		ILMTransactionSetHDR ilmTransactionSetHDR= null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName()
					+ " - [viewTransaction] - " + "Entry");

			ilmTransactionSetHDR = getIlmTransactionSetHDR();




			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve transaction set id
			txnSetId = request.getParameter("selectedTransactionId");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();

			/*
			 * Set the screenFieldsStatus to true because we have to display the
			 * list of scenarios. if screenFieldsStatus is true then disable the
			 * related fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);


			// This is used to set the button status by disable all buttons
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_FALSE);


			ilmTransactionSetHDR = ilmTransScenarioMaintenanceManager
					.getEditableTransactionHDRData(hostId, entityId,
							currencyCode, txnSetId);
			if(ilmTransactionSetHDR.getIlmTransactionSetDTLs() != null && ilmTransactionSetHDR.getIlmTransactionSetDTLs().size() > 0 ){
				Iterator itr1 =  ilmTransactionSetHDR.getIlmTransactionSetDTLs().iterator();
				ILMTransactionSetDTL  transactionset = null;

				while (itr1.hasNext()) {
					transactionset = (ILMTransactionSetDTL ) itr1.next();
					transactionset.setDebitsAsString(SwtUtil.formatCurrency(
							transactionset.getId().getCurrencyCode(), transactionset
									.getDebits()));
					transactionset.setCreditsAsString(SwtUtil.formatCurrency(
							transactionset.getId().getCurrencyCode(), transactionset
									.getCredits()));
				}
			}else {
				ilmTransactionSetHDR.setIlmTransactionSetDTLs(Collections.<ILMTransactionSetDTL> emptySet());
			}

			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, false, false),
					currencyCode);
			// Convert the entity list to label value bean.
			Collection entityColl = SwtUtil.convertEntityAcessCollectionLVL(SwtUtil.getUserEntityAccessList(request.getSession()),
					request.getSession());
			entityName = getLabelFromValue(entityColl, entityId);

			// Put the required attributes in the form
			putEntityListInReq(request, false);
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("entityText", entityName);
			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, false));
			request.setAttribute("methodName", "viewTransaction");
			request.setAttribute("ilmTransactionSetsDetails",
					ilmTransactionSetHDR.getIlmTransactionSetDTLs());
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			log.debug(this.getClass().getName()
					+ " - [viewTransaction] - " + "Exit");
			return getView("viewTransaction");
		} catch (SwtException swtexp) {

			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewTransaction] method : -"
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewTransaction] method : -"
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewTransaction] method : -"
					+ e.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewTransaction] method : -"
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"viewTransaction",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}


	/**
	 * Used to update ILM Transaction
	 * @return
	 * @throws SwtException
	 */
	public String updateTransaction() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Method's local variable and class instance declaration
		ILMTransactionSetHDR ilmTransactionSetHDR = null;
		Iterator itr;
		ActionMessages errors = null;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		String txnSetName = null;
		String XMLTransactions = null;
		Set<ILMTransactionSetDTL> transactionSetListDetail = null;
		Collection<ILMTransactionSetDTL> transactionSetListDetails = null;
		SystemFormats formats = null;
		boolean updatedRelatedScenarios  = false;
		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [updateTransaction] - "
					+ "Entry");
			ilmTransactionSetHDR = getIlmTransactionSetHDR();



			ilmTransactionSetHDR.getId().setHostId(SwtUtil.getCurrentHostId());

			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve transaction set id
			txnSetId = request.getParameter("transactionId");
			// Retrieve transaction set id
			txnSetName = request.getParameter("transactionName");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Read the current system formats from swtUtil file
			formats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Retrieve XMLTransactions
			XMLTransactions = request.getParameter("XMLTransactions");
			XMLTransactions = SwtUtil.decode64(XMLTransactions);
			ilmTransactionSetHDR = ilmTransScenarioMaintenanceManager
					.getEditableTransactionHDRData(hostId, entityId,
							currencyCode, txnSetId);
			if (!SwtUtil.isEmptyOrNull(XMLTransactions)) {

				ilmTransactionTOAdd = new HashSet<ILMTransactionSetDTL>();
				ilmTransactionTOChange = new HashSet<ILMTransactionSetDTL>();
				ilmTransactionTODelete = new HashSet<ILMTransactionSetDTL>();

				XMLTreatment(XMLTransactions, entityId, currencyCode, txnSetId , formats);
				// Save the new records
				if (ilmTransactionTOAdd != null)
					ilmTransScenarioMaintenanceManager
							.saveILMTransactionDetail(ilmTransactionSetHDR,
									ilmTransactionTOAdd);

				// Update the existing records
				if (ilmTransactionTOChange != null)
					ilmTransScenarioMaintenanceManager
							.updateILMTransactionDetail(ilmTransactionTOChange);

				// Delete records
				if (ilmTransactionTODelete != null)
					ilmTransScenarioMaintenanceManager
							.deleteILMTransactionDetail(ilmTransactionTODelete);

				if (ilmTransactionTODelete != null || ilmTransactionTOChange != null)
					updatedRelatedScenarios = true;

			}

			ilmTransactionSetHDR.setTxnSetName(txnSetName);
			ilmTransScenarioMaintenanceManager
					.updateILMTransaction(ilmTransactionSetHDR, updatedRelatedScenarios);
			/* If parentFromRefresh value is 'yes', then we will close the
			 opened screen and refresh the parent screen */
			request.setAttribute("parentFormRefresh", "yes");
			putEntityListInReq(request, true);
			// Put the currencies list in the form
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false , true));
			// the transaction details list
			request.setAttribute("ilmTransactionSetsDetails",
					ilmTransactionSetHDR.getIlmTransactionSetDTLs());
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveTransaction] - "
					+ "Exit");
			return getView("addTransactionSet");
		} catch (SwtException swtexp) {
			request.setAttribute("methodName", "addTransactionSet");
			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetails);
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveTransaction] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false , true));
			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			putEntityListInReq(request, true);
			return getView("addTransactionSet");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveTransaction] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"listTransactionSetDetails",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
			// Nullify objects
			ilmTransactionTOAdd = null;
			ilmTransactionTOChange = null;
			ilmTransactionTODelete = null;
		}
	}

	/**
	 * Method called when delete Button is clicked ILM Transaction Set Maintenance
	 * @return
	 * @throws Exception
	 */
	public String deleteTransaction()
			throws Exception {

		int accessInd;
		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String txnSetId = null;
		ILMTransactionSetHDR ilmTransactionSetHDR = null;
		ArrayList transactionSetListDetail = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName()
					+ "- [deleteTransaction] - Entering");

			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve currency code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve transaction set id
			txnSetId = request.getParameter("selectedTxId");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Get the editable ILM Scenario data
			ilmTransactionSetHDR = ilmTransScenarioMaintenanceManager
					.getEditableTransactionHDRData(hostId, entityId, currencyCode, txnSetId);
			// Delete the ILM Scenario object
			ilmTransScenarioMaintenanceManager.deleteILMTransaction(ilmTransactionSetHDR);
			// Get the Transaction list
			transactionSetListDetail = ilmTransScenarioMaintenanceManager
					.getTransactionList(hostId, null, null, request);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			// Put the required attributes in the request
			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			request.setAttribute("menuAccessId", request
					.getParameter("menuAccessId"));

			log.debug(this.getClass().getName()
					+ "- [deleteTransaction] - Exiting");
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			return getView("listTransactionSets");

		} catch (SwtException swtexp) {
			setIlmTransactionSetHDR(ilmTransactionSetHDR);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteTransaction] method : - "
					+ swtexp.getMessage());

			request.setAttribute("ilmTransactionSetsDetails",
					transactionSetListDetail);
			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}
			request.setAttribute("methodName", "listTransactionSets");
			log.debug(this.getClass().getName()
					+ "- [deleteTransaction] - Exiting");
			return getView("listTransactionSets");

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteTransaction] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteTransaction",
							ILMTransScenarioMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}


	/**
	 * Method called when change Button is clicked ILM Transaction Set Display Maintenance
	 * @return
	 * @throws Exception
	 */
	public String changeTransactionDetail() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		String hostId = null;
		String entityId = null;
		String currencyCode = null;
		String accountId = null;
		String time = null;
		String accoutNameText = null;
		String isNewRecord = null;
		String txnSetId = null;
		ILMTransactionSetDTL ilmTransactionSetDTL;
		Collection<LabelValueBean> accountList = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [changeTransactionDetail] - " + "Entry");

			ilmTransactionSetDTL = getIlmTransactionSetDTL();




			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve Currency Code
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve txnSetId
			txnSetId = request.getParameter("selectedtxnSetId");
			// Retrieve Account Id
			accountId = request.getParameter("selectedAccountId");
			// Retrieve time
			time = request.getParameter("selectedTime");
			isNewRecord = request.getParameter("isNewRecord");
			if (SwtUtil.isEmptyOrNull(isNewRecord)) {// if record is already exist in database
				// get record information from database
				ilmTransactionSetDTL = ilmTransScenarioMaintenanceManager
						.getEditableTransactionDTLData(hostId, entityId,
								currencyCode, txnSetId, accountId, time);
				ilmTransactionSetDTL.setDebitsAsString(SwtUtil.formatCurrency(currencyCode, ilmTransactionSetDTL.getDebits()));
				ilmTransactionSetDTL.setCreditsAsString(SwtUtil.formatCurrency(currencyCode, ilmTransactionSetDTL.getCredits()));

			} else {

				ilmTransactionSetDTL.getId().setEntityId(entityId);
				ilmTransactionSetDTL.getId().setCurrencyCode(currencyCode);
				ilmTransactionSetDTL.getId().setTxnSetId(txnSetId);
				ilmTransactionSetDTL.getId().setAccountId(accountId);
				ilmTransactionSetDTL.getId().setTime(time);

				if (!SwtUtil.isEmptyOrNull(request
						.getParameter("selectedCredit")))
					ilmTransactionSetDTL.setCreditsAsString(request
							.getParameter("selectedCredit"));
				if (!SwtUtil.isEmptyOrNull(request
						.getParameter("selectedDebit")))
					ilmTransactionSetDTL.setDebitsAsString(request
							.getParameter("selectedDebit"));
				ilmTransactionSetDTL.setDescription(request
						.getParameter("selectedDecription"));

			}
			accountList = putDataSetAcctListInReq(
					entityId,  currencyCode);

			accoutNameText = getLabelFromValue(accountList, accountId);

			// Put the required attributes in the form
			request.setAttribute("accountList", accountList);
			request.setAttribute("accoutNameText", accoutNameText);
			setIlmTransactionSetDTL(ilmTransactionSetDTL);
			request.setAttribute("methodName", "changeTransactionDetail");
			log.debug(this.getClass().getName()
					+ " - [changeTransactionDetail] - " + "Exit");
			return getView("changeTransactionDetail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeTransactionDetail] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"changeTransactionDetail",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * Returns (through HttpServletResponse) a string which contains the result
	 * of calculating access for the selected row according to entity id and
	 * currency code. Values : 0 : Full access 1 : View access 2 : No access
	 * @return
	 * @throws SwtException
	 */
	public String getEntityCurrencyAccessForScenario() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Method's local variable and class instance declaration
		String entityId;
		String currencyCode;
		String userId;
		String currentUserId;
		int accessInd = -1;
		String hostId;
		boolean access = false;
		boolean maintainAnyILMScenario = false;
		String publicPrivate = null;
		// Entity collection
		Collection entityColl = null;
		// currencies collection
		Collection ccyColl = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getEntityCurrencyAccessForScenario] - Enter");
			currencyCode = request.getParameter("currencyCode");
			entityId = request.getParameter("entityId");
			userId = request.getParameter("userId");
			publicPrivate = request.getParameter("publicPrivate");
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve current User ID
			currentUserId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId()
					.getUserId();
			if ("All entities".equalsIgnoreCase(entityId)
					&& "All currencies".equalsIgnoreCase(currencyCode)
					&& accessInd < 0) {

				// Get the user access entity list .
				entityColl = SwtUtil.getUserEntityAccessList(request
						.getSession());
				Iterator itrEntity = entityColl.iterator();

				while (itrEntity.hasNext()&& accessInd!=0) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity
							.next();

					// Getting the User's Role Id from the session object
					String roleId = ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser()
							.getRoleId();
					// Returns the currency Access List based on the Role
					Collection ccyList = (ArrayList) SwtUtil
							.getSwtMaintenanceCache()
							.getCurrencyViewORFullAcess(roleId, entity.getEntityId());
					if (ccyList != null) {
						Iterator itr = ccyList.iterator();

						while (itr.hasNext()) {
							CurrencyAccessTO currencyAccessTO = (CurrencyAccessTO) itr
									.next();
							accessInd = SwtUtil.getCcyGrpAccessType(request,
									hostId, entity.getEntityId(),
									currencyAccessTO.getCurrencyId());
							if (accessInd == 0)
								break;
						}
					}

				}
			}
			if ("All entities".equalsIgnoreCase(entityId) && accessInd != 0) {
				// Get the user access entity list .
				entityColl = SwtUtil.getUserEntityAccessList(request
						.getSession());
				Iterator itrEntity = entityColl.iterator();

				while (itrEntity.hasNext()) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity
							.next();
					accessInd = SwtUtil.getCcyGrpAccessType(request, hostId,
							entity.getEntityId(), currencyCode);
					if (accessInd == 0){
						break;
					}
				}
			} else if ("All currencies".equalsIgnoreCase(currencyCode)
					&& accessInd != 0) {
				// Getting the User's Role Id from the session object
				String roleId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				// Returns the currency Access List based on the Role
				Collection ccyList = (ArrayList) SwtUtil
						.getSwtMaintenanceCache().getCurrencyViewORFullAcess(
								roleId, entityId);

				if (ccyList != null) {
					Iterator itr = ccyList.iterator();

					while (itr.hasNext()) {
						CurrencyAccessTO currencyAccessTO = (CurrencyAccessTO) itr
								.next();
						accessInd = SwtUtil.getCcyGrpAccessType(request,
								hostId, entityId,
								currencyAccessTO.getCurrencyId());

						if (accessInd == 0)
							break;
					}
				}

			} else if (accessInd != 0) {
				// Retrieve User's Menu,Entity and Currency Group
				accessInd = SwtUtil.getCcyGrpAccessType(request, hostId,
						entityId, currencyCode);
			}
			if (accessInd == 0) {// TO DO : when Entity or Ccy equals to *
				// ,maybe just check if this scenario is
				// public or not ??

				maintainAnyILMScenario = SwtUtil
						.getMaintainAnyScenarioILMAccess(request);
				if (maintainAnyILMScenario)
					access = true;
				else {
					if (!SwtUtil.isEmptyOrNull(currentUserId)
							&& !SwtUtil.isEmptyOrNull(userId)) {
						if (currentUserId.equals(userId)
								&& publicPrivate.equalsIgnoreCase("PRIVATE"))
							access = true;
					} else
						access = false;
				}
			}

			response.getWriter().print(access?0:1);

			log.debug(this.getClass().getName()
					+ " - [getEntityCurrencyAccessForScenario] - " + "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getEntityCurrencyAccess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"getEntityCurrencyAccess",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * Given a collection of label-value beans, returns the 'label' from the
	 * first occurrence with a 'value' matching the given 'value' parameter
	 *
	 * @param coll
	 * @param value
	 * @return String
	 */
	private String getLabelFromValue(Collection<LabelValueBean> coll,
									 String value) {
		log.debug("entering 'getLabelFromValue' method");
		if (coll != null) {
			Iterator<LabelValueBean> itr = coll.iterator();

			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());
				if (lvb.getValue().equals(value))
					return lvb.getLabel();
			}
		}
		log.debug("exiting 'getLabelFromValue' method");
		return null;
	}

	/**
	 *
	 * This function returns collection of currencies
	 * @return - collection of currencies
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request, String hostId, String entityId, boolean withAll,
									   boolean fullAccess) throws SwtException {
		/* Method's local variable declaration */
		String roleId = "";
		ArrayList ccyList = null;
		Collection currrencyList = null;
		try {
			log.debug(this.getClass().getName() + " - [ getCurrencyList ] - Entry ");
			// Getting the User's Role Id from the session object
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			// Returns the currency Access List based on the Role
			if (fullAccess) {
				ccyList = (ArrayList) getCurrencyFullAccessList(request, SwtUtil.getCurrentHostId(), entityId);
			} else {
				ccyList = (ArrayList<LabelValueBean>) SwtUtil.getSwtMaintenanceCache()
						.getCurrencyViewORFullAcessLVL(roleId, entityId);
			}

			// Check for currency List not NULL
			if (ccyList != null) {
				/*
				 * Removes the LabelValueBean object for the Key as 'Default' from the
				 * collection
				 */
				ccyList.remove(new LabelValueBean("Default", "*"));
				if (withAll) {
					ccyList.add(0, new LabelValueBean("All Currencies", "All"));
				}

				// Assigning the new ArrayList object to a new Collection Object
				currrencyList = new ArrayList();

				// Adding the currencyList object to collection object
				currrencyList.addAll(ccyList);
			}
			log.debug(this.getClass().getName() + " - [ getCurrencyList ] - Exit ");
		} catch (Exception exp) {
			log.error(this.getClass().getName() + " - [getCurrencyList] - Exception -" + exp.getMessage());
		} finally {
			// Nullify objects
			roleId = null;
			ccyList = null;
		}
		return currrencyList;
	}
	/**
	 *
	 * This function returns the collection of LabelValueBean objects for a role
	 * id and entity id which have the full access.
	 * @return - currrencyList
	 * @throws SwtException
	 *             - SwtException object
	 */
	private Collection<LabelValueBean> getCurrencyFullAccessList(
			HttpServletRequest request, String hostId, String entityId)
			throws SwtException {

		// String Variable to hold the roleId
		String roleId = null;
		// Variable to hold the currencyMap object
		Map<String, String> currencyMap = null;
		// Variable to hold the currrencyList object
		Collection<LabelValueBean> currrencyList = null;
		// Variable to hold the itrCurrencyKey object
		Iterator<String> itrCurrencyKey = null;
		// String Variable to hold the currencyId
		String currencyId = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Entry ");
			/* Getting the User's Role Id from the session object */
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			currrencyList = new ArrayList<LabelValueBean>();
			/* Returns the currency Access List based on the Role */
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);

			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					currencyId = itrCurrencyKey.next();

					// add labelvaluebean for currency id
					currrencyList.add(new LabelValueBean((String) currencyMap
							.get(currencyId), currencyId));
				}
			}
			log.debug(this.getClass().getName()
					+ " - [ getCurrencyFullAccessList ] - Exit ");
		} catch (SwtException swtexp) {
			log.error("SwtException Catch in "+ILMTransScenarioMaintenanceAction.class+".'getCurrencyFullAccessList' method : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log.error("Exception Catch in "+ILMTransScenarioMaintenanceAction.class+".'getCurrencyFullAccessList' method : "
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());

		} finally {
			// nullify objects
			roleId = null;
			currencyMap = null;
			itrCurrencyKey = null;
			currencyId = null;
		}
		return currrencyList;
	}

	/**
	 * This method is used to add a new ILM Scenario for ILM monitoring.
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String addScenario()
			throws SwtException {

		String hostId = null;
		String createdByUser = null;
		String entityId = null;
		String currencyCode = null;
		int accessInd;
		boolean withAllCcyEntity;
		ILMScenario ilmScenario;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [addScenario] - "
					+ "Entry");
			/*
			 * Set the screenFieldsStatus to false because we are in the add
			 * role part. if screenFieldsStatus is true then disable the related
			 * fields which contain this attribute
			 * (disabled=${screenFieldsStatus})
			 */
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
			ilmScenario = getIlmScenario();




			withAllCcyEntity = SwtUtil.getMaintainAnyScenarioILMAccess(request);
			putEntityListInReq(request, withAllCcyEntity);

			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve user Name
			User user = SwtUtil.getCurrentUser(request.getSession());
			createdByUser = user.getId().getUserId();
			// Retrieve Entity Id
			entityId = request.getParameter("selectedEntityId");
			// Retrieve Currency Code
			currencyCode = request.getParameter("selectedCurrencyCode");

			if (SwtUtil.isEmptyOrNull(entityId))
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

			if (SwtUtil.isEmptyOrNull(currencyCode))
				// Get the domestic currency
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						SwtUtil.getCurrentHostId(), entityId);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);

			if (accessInd == 0) {
				// Enable Save and Cancel buttons
				request.setAttribute(SwtConstants.SAV_BUT_STS,
						SwtConstants.STR_TRUE);
				request.setAttribute(SwtConstants.CAN_BUT_STS,
						SwtConstants.STR_TRUE);
			} else {
				// set only the cancel button status as enable
				request.setAttribute(SwtConstants.SAV_BUT_STS,
						SwtConstants.STR_FALSE);
				request.setAttribute(SwtConstants.CAN_BUT_STS,
						SwtConstants.STR_TRUE);
			}

			ilmScenario.setHostId(hostId);
			ilmScenario.setEntityId(entityId);
			ilmScenario.setCurrencyCode(currencyCode);
			ilmScenario.setCreatedByUser(createdByUser);
			ilmScenario.setCreateDateAsString(SwtUtil.getSystemDateString());
			// Scenario will default to Private
			ilmScenario.setPublicPrivate("PRIVATE");
			ilmScenario.setDefaultLegendText("I");
			// Set values to be listed by default when adding a new ILM scenario
			ilmScenario.setCreditSuccessRate("100");
			ilmScenario.setCreditPctDelayed("0");
			ilmScenario.setCreditDelayTime("0");
			ilmScenario.setDebitSuccessRate("100");
			ilmScenario.setDebitPctDelayed("0");
			ilmScenario.setDebitDelayTime("0");
			ilmScenario.setNonScnCreditSuccessRate("100");
			ilmScenario.setNonScnDebitSuccessRate("100");
			ilmScenario.setOtherSourcesAvlbl("100");
			ilmScenario.setCollateralAvlbl("100");
			ilmScenario.setCreditlineAvlbl("100");
			ilmScenario.setUnencumberedLiqAssetAvlbl("100");
			// Put the currencies list in the form
			if("All".equalsIgnoreCase(entityId)){
				Collection	entityColl = SwtUtil.getUserEntityAccessList(request
						.getSession());
				Iterator itrEntity = entityColl.iterator();
				Set<LabelValueBean> set = new TreeSet<>(new Comparator<LabelValueBean>() {
					public int compare(LabelValueBean lvb1, LabelValueBean lvb2) {
						if (lvb1.getValue().equals("All") && !lvb2.getValue().equals("All")) {
							return -1; // lvb1 should come before lvb2
						} else if (lvb2.getValue().equals("All") && !lvb1.getValue().equals("All")) {
							return 1; // lvb2 should come before lvb1
						} else {
							return lvb1.getValue().compareTo(lvb2.getValue()); // compare normally
						}
					}
				});
				while (itrEntity.hasNext()) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity.next();
					Collection ccyList = getCurrencyList(request, hostId, entity.getEntityId(), withAllCcyEntity , true);
					if (ccyList != null) {
						Iterator itr = ccyList.iterator();

						while (itr.hasNext()) {
							LabelValueBean currency = (LabelValueBean) itr
									.next();
							set.add(currency);


						}
					}
				}
				request.setAttribute("currencyList",
						set);
			}else
				request.setAttribute("currencyList",
						getCurrencyList(request, hostId, entityId, withAllCcyEntity, true));
			// put the transaction list in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(hostId, entityId,
							currencyCode, request));
			request.setAttribute("selectedEntityId", entityId);
			request.setAttribute("selectedCurrencyCode", currencyCode);
			request.setAttribute("ilmScenario", ilmScenario);
			request.setAttribute("possibleToMaintain", !SwtUtil.getMaintainAnyScenarioILMAccess(request));
			request.setAttribute("methodName", "addScenario");
			request.setAttribute("parentScreen", request.getParameter("parentScreen"));
			setIlmScenario(ilmScenario);
			log.debug(this.getClass().getName() + " - [addScenario] - "
					+ "Exit");
			return getView("addScenario");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addScenario] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"addScenario",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * Method called when save Button clicked on Add ILM Scenario detail
	 * maintenance screen
	 * @return
	 * @throws SwtException
	 */
	public String saveScenario()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable and class instance declaration */
		ILMScenario ilmScenario = null;
		ActionMessages errors = null;
		try {
			errors = new ActionMessages();
			log.debug(this.getClass().getName() + " - [saveScenario] - "
					+ "Entry");
			ilmScenario = getIlmScenario();



			ilmScenario.setHostId(SwtUtil.getCurrentHostId());
			ilmScenario.setPublicPrivate(request.getParameter("publicPrivate"));
			if(SwtConstants.ALL_LABEL.equalsIgnoreCase(ilmScenario.getEntityId()))
				ilmScenario.setEntityId("*");
			if(SwtConstants.ALL_LABEL.equalsIgnoreCase(ilmScenario.getCurrencyCode()))
				ilmScenario.setCurrencyCode("*");
			// If the default legend text is empty then set it to ID by default
			if (SwtUtil.isEmptyOrNull(ilmScenario.getDefaultLegendText())) {
				ilmScenario.setDefaultLegendText("I");
			}
			/*
			 * TO DO : Here we need to validate the filter condition clause
			 * Thus, call a procedure that can achieve this and according to
			 * result we can save or not the scenario detail
			 */

			String currentDateFormat = SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue();
			if (!SwtUtil.isEmptyOrNull(ilmScenario.getCreateDateAsString()))
				ilmScenario
						.setCreateDate(SwtUtil.parseDate(
								ilmScenario.getCreateDateAsString(),
								currentDateFormat));
			ilmTransScenarioMaintenanceManager
					.saveILMScenarioDetails(ilmScenario);

			/* Put the required attributes in the request */

			/* If parentFromRefresh value is 'yes', then we will close the
			 opened screen and refresh the parent screen*/
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("parentScreen", request.getParameter("parentScreen"));
			// Put the currencies list in the form
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmScenario.getEntityId(), false , true));

			putEntityListInReq(request, true);
			setIlmScenario(ilmScenario);
			// put the transaction list in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(
							ilmScenario.getHostId(), ilmScenario.getEntityId(),
							ilmScenario.getCurrencyCode(), request));
			request.setAttribute("methodName", "saveScenario");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + " - [saveScenario] - "
					+ "Exit");
			return getView("addScenario");
		} catch (SwtException swtexp) {

			request.setAttribute("methodName", "addScenario");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);
			} else {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [saveScenario] method swt : - "
						+ swtexp.getMessage());
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			// Put the transaction List in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(
							ilmScenario.getHostId(), ilmScenario.getEntityId(),
							ilmScenario.getCurrencyCode(), request));
			// Put the currencies list in the form
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmScenario.getEntityId(), false , true));
			putEntityListInReq(request, true);
			return getView("addScenario");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [saveScenario] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"saveScenario",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * Method called when change Button change clicked on ILM Scenario
	 * Maintenance screen
	 * @return ActionForward
	 * @throws Exception
	 */
	public String changeScenario()
			throws Exception {
		/* Method's local variable and class instance declaration */
		ILMScenario ilmScenario;
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String currencyCode = null;
		String currencyName = null;
		String scenarioId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [changeScenario] - "
					+ "Entry");

			ilmScenario = getIlmScenario();




			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the entity name form request
			entityName = request.getParameter("selectedEntityName");
			// Read the currency code from request
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Retrieve the scenarioId
			scenarioId = request.getParameter("selectedScenarioId");
			// Get the currencyName
			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, true , true),
					"*".equalsIgnoreCase(currencyCode)?"All":currencyCode);
			// Get the editable ilmScenario data */
			ilmScenario = ilmTransScenarioMaintenanceManager
					.getEditableScenarioData(scenarioId);
			String currentDateFormat = SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue();

			ilmScenario.setCreateDateAsString(SwtUtil.formatDate(
					ilmScenario.getCreateDate(), currentDateFormat));
			// If the default legend text is empty then set it to ID by default
			if (SwtUtil.isEmptyOrNull(ilmScenario.getDefaultLegendText())) {
				ilmScenario.setDefaultLegendText("I");
			}
			if("*".equalsIgnoreCase(ilmScenario.getCurrencyCode())){
				currencyCode ="All";
				ilmScenario.setCurrencyCode("All");
			}

			if("*".equalsIgnoreCase(ilmScenario.getEntityId())){
				entityId ="All";
				ilmScenario.setEntityId("All");
			}
			// Put the required attributes in the request
			putEntityListInReq(request, true);
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false, true));
			// Put the transaction list in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(hostId, entityId,
							currencyCode, request));
			setIlmScenario(ilmScenario);
			request.setAttribute("entityText", SwtUtil.decreaseStringWidth(SwtConstants.ENTITY_NAME_MAX_WIDTH, entityName, SwtConstants.VERDANA_STYLE,SwtConstants.VERDANA_12P_SIZE));
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("possibleToMaintain", !SwtUtil.getMaintainAnyScenarioILMAccess(request));
			request.setAttribute("methodName", "changeScenario");
			request.setAttribute("parentScreen", request.getParameter("parentScreen"));
			request.setAttribute("systemValue", ilmScenario.getSystemScenario());
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);

			log.debug(this.getClass().getName() + " - [changeScenario] - "
					+ "Exit");
			return getView("changeScenario");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeScenario] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeScenario] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [changeScenario] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [changeScenario] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(e, "changeScenario",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}

	}

	/**
	 * Method called when save Button clicked on Change ILM Scenario Detail
	 * Maintenance screen
	 * @return
	 * @throws SwtException
	 */
	public String updateScenario()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable and class instance declaration */
		ILMScenario ilmScenario = null;
		String entityId = null;
		String currencyCode = null;
		String ilmScenarioId = null;

		try {
			log.debug(this.getClass().getName() + " - [updateScenario] - "
					+ "Entry");

			// Gets the instance from struts-config.xml file
			ilmScenario = getIlmScenario();



			ilmScenario.setHostId(SwtUtil.getCurrentHostId());
			entityId = request.getParameter("selectedEntityId");
			currencyCode = request.getParameter("selectedCurrencyCode");
			ilmScenario.setEntityId(SwtConstants.ALL_LABEL.equalsIgnoreCase(entityId)?"*":entityId);
			ilmScenario.setCurrencyCode(SwtConstants.ALL_LABEL.equalsIgnoreCase(currencyCode)?"*":currencyCode);
			ilmScenarioId = request.getParameter("ilmScenarioId");
			ilmScenario.getId().setIlmScenarioId(ilmScenarioId);
			ilmScenario.setPublicPrivate(request.getParameter("publicPrivate"));
			ilmScenario.setDefaultLegendText(request.getParameter("defaultLegendText"));

			String currentDateFormat = SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue();
			if (!SwtUtil.isEmptyOrNull(ilmScenario.getCreateDateAsString()))
				ilmScenario
						.setCreateDate(SwtUtil.parseDate(
								ilmScenario.getCreateDateAsString(),
								currentDateFormat));
			ilmTransScenarioMaintenanceManager
					.updateILMScenarioDetails(ilmScenario);

			// If parentFromRefresh value is 'yes', then we will close the
			// opened screen and refresh the parent screen
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("parentScreen", request.getParameter("parentScreen"));
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							ilmScenario.getEntityId(), false, true));
			putEntityListInReq(request, true);
			setIlmScenario(ilmScenario);
			// Put the transaction list in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(
							ilmScenario.getHostId(), ilmScenario.getEntityId(),
							ilmScenario.getCurrencyCode(), request));
			request.setAttribute("methodName", "updateScenario");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_TRUE);

			log.debug(this.getClass().getName() + " - [updateScenario] - "
					+ "Exit");

			return getView("addScenario");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [updateScenario] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance()
							.handleException(exp, "updateScenario",
									ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}
	}

	/**
	 * Method called when view Button is clicked ILM Scenario Maintenance
	 * @return ActionForward
	 * @throws Exception
	 */
	public String viewScenario()
			throws Exception {
		/* Method's local variable and class instance declaration */
		ILMScenario ilmScenario;
		String hostId = null;
		String entityId = null;
		String entityName = null;
		String currencyCode = null;
		String currencyName = null;
		String scenarioId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName() + " - [viewScenario] - "
					+ "Entry");
			request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
			ilmScenario = getIlmScenario();



			// Reads the hostId from SwtUtil
			hostId = SwtUtil.getCurrentHostId();
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the entity name form request
			entityName = request.getParameter("selectedEntityName");
			// Read the currency code from request
			currencyCode = request.getParameter("selectedCurrencyCode");

			// Retrieve the scenarioId
			scenarioId = request.getParameter("selectedScenarioId");

			// Get the editable ilmScenario data
			ilmScenario = ilmTransScenarioMaintenanceManager
					.getEditableScenarioData(scenarioId);
			String currentDateFormat = SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue();
			ilmScenario.setCreateDateAsString(SwtUtil.formatDate(
					ilmScenario.getCreateDate(), currentDateFormat));

			// If the default legend text is empty then set it to ID by default
			if (SwtUtil.isEmptyOrNull(ilmScenario.getDefaultLegendText())) {
				ilmScenario.setDefaultLegendText("I");
			}
			if("*".equalsIgnoreCase(ilmScenario.getCurrencyCode())){
				currencyCode ="All";
				ilmScenario.setCurrencyCode("All");
			}

			if("*".equalsIgnoreCase(ilmScenario.getEntityId())){
				entityId ="All";
				ilmScenario.setEntityId("All");
			}
			// Get the currencyName
			currencyName = getLabelFromValue(
					getCurrencyList(request, hostId, entityId, true, false),
					currencyCode);
			// Put the required attributes in the request
			putEntityListInReq(request, false);
			request.setAttribute("currencyList",
					getCurrencyList(request, hostId, entityId, false , false));
			// Put the transaction list in the form
			request.setAttribute(
					"transactionList",
					putDataSetExtraTransactionListInReq(hostId, entityId,
							currencyCode, request));
			setIlmScenario(ilmScenario);
			request.setAttribute("entityText", SwtUtil.decreaseStringWidth(SwtConstants.ENTITY_NAME_MAX_WIDTH, entityName, SwtConstants.VERDANA_STYLE,SwtConstants.VERDANA_12P_SIZE));
			request.setAttribute("currencyCodeText", currencyName);
			request.setAttribute("methodName", "viewScenario");
			request.setAttribute(SwtConstants.SAV_BUT_STS,
					SwtConstants.STR_FALSE);

			log.debug(this.getClass().getName() + " - [viewScenario] - "
					+ "Exit");
			return getView("viewScenario");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewScenario] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewScenario] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [viewScenario] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewScenario] method : - "
					+ e.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e,
							"viewScenario", ILMGeneralMaintenanceAction.class),
					request, "");
			return getView("fail");
		} finally {
		}

	}

	/**
	 * Method called when delete Button is clicked ILM Scenario Maintenance
	 * @return
	 * @throws Exception
	 */
	public String deleteScenario()
			throws Exception {
		/* Method's local variable and class instance declaration */
		int accessInd;
		String scenarioId = null;
		String entityId = null;
		String currencyCode = null;
		ILMScenario ilmScenario = null;
		ArrayList scenarioListDetail;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName()
					+ "- [deleteScenario] - Entering");
			ilmScenario = getIlmScenario();
			// Read the scenario id from request
			scenarioId = request.getParameter("selectedScenarioId");
			// Read the entity code from request
			entityId = request.getParameter("selectedEntityId");
			// Read the currency code from request
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Get the editable ILM Scenario data
			ilmScenario = ilmTransScenarioMaintenanceManager
					.getEditableScenarioData(scenarioId);
			// delete the ILM Scenario object
			ilmTransScenarioMaintenanceManager.deleteILMScenario(ilmScenario);

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}

			/* Put the required attributes in the request */
			putEntityListInReq(request, false);
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							entityId, true, false));
			scenarioListDetail = ilmTransScenarioMaintenanceManager
					.getILMScenarioList(SwtUtil.getCurrentHostId(), entityId,
							currencyCode, request);

			Iterator itr = scenarioListDetail.iterator();

			// Loop till the last value of the iterator.
			while (itr.hasNext()) {
				// Get the value in Scenario bean
				ilmScenario = (ILMScenario) (itr.next());
				if("*".equalsIgnoreCase(ilmScenario.getEntityId()))
					ilmScenario.setEntityId("All entities");
				if("*".equalsIgnoreCase(ilmScenario.getCurrencyCode()))
					ilmScenario.setCurrencyCode("All currencies");
			}
			request.setAttribute("ilmScenariosDetails", scenarioListDetail);
			setIlmScenario(ilmScenario);
			log.debug(this.getClass().getName()
					+ "- [deleteScenario] - Exiting");

			return getView("listScenarios");

		} catch (SwtException swtexp) {
			setIlmScenario(ilmScenario);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteScenario] method : - "
					+ swtexp.getMessage());
			putEntityListInReq(request, true);
			request.setAttribute(
					"currencyList",
					getCurrencyList(request, SwtUtil.getCurrentHostId(),
							entityId, true, true));

			// Retrieve User's Menu,Entity and Currency Group
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					currencyCode);
			scenarioListDetail = ilmTransScenarioMaintenanceManager
					.getILMScenarioList(SwtUtil.getCurrentHostId(), entityId,
							currencyCode, request);
			if (accessInd == 0) {
				// This is used to set the button status by enable all buttons
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			} else {
				// This is used to set the button status by disable all buttons
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE);
			}
			request.setAttribute("methodName", "listScenarios");
			request.setAttribute("ilmScenariosDetails", scenarioListDetail);
			log.debug(this.getClass().getName()
					+ "- [deleteScenario] - Exiting");
			return getView("listScenarios");

		} catch (Exception exp) {
			ilmScenario = getIlmScenario();
			setIlmScenario(ilmScenario);
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteScenario] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"deleteScenario",
							ILMTransScenarioMaintenanceAction.class), request,
					"");

			return getView("fail");
		}
	}

	/**
	 * Used to initialise the 'Extra Transaction Set' combo box item values in
	 * ILM Scenario Detail Screen
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection<LabelValueBean> putDataSetExtraTransactionListInReq(
			String hostId, String entityId, String currencyCode,
			HttpServletRequest request) throws SwtException {

		Collection<LabelValueBean> extraTransactionLVB = null;
		Iterator<ILMTransactionSetHDR> itrExtraTransGrp = null;
		ILMTransactionSetHDR ilmTransactionSetHDR = null;
		Collection<ILMTransactionSetHDR> colExtraTrans = null;
		try {

			log.debug(this.getClass().getName()
					+ " - [putDataSetExtraTransactionListInReq] - " + "Entry");

			// Initialise collection to hold ilmTransSetHDR detail as LabelValueBean object
			extraTransactionLVB = new ArrayList<LabelValueBean>();
			colExtraTrans = ilmTransScenarioMaintenanceManager
					.getTransactionList(hostId, entityId, currencyCode, request);

			// Showing ' the empty ' in Extra Transaction Set drop down
			extraTransactionLVB.add(new LabelValueBean("", ""));
			// Iterate through colExtraTrans list and get LabelValueBean list
			if (colExtraTrans != null) {
				itrExtraTransGrp = colExtraTrans.iterator();
				while (itrExtraTransGrp.hasNext()) {
					ilmTransactionSetHDR = (ILMTransactionSetHDR) itrExtraTransGrp
							.next();

					extraTransactionLVB.add(new LabelValueBean(
							ilmTransactionSetHDR.getTxnSetName(),
							ilmTransactionSetHDR.getId().getTxnSetId()));
				}
			}

		} catch (Exception ex) {
			// Log error message
			log.error("ILMTransScenarioMaintenanceAction - [putDataSetExtraTransactionListInReq] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"putDataSetExtraTransactionListInReq",
					ILMTransScenarioMaintenanceAction.class);
		} finally {
			// Nullify objects
			colExtraTrans = null;
			itrExtraTransGrp = null;
			ilmTransactionSetHDR = null;
			// Log debug message
			log.debug("ILMTransScenarioMaintenanceAction - [putDataSetExtraTransactionListInReq] - Exit");
		}
		return extraTransactionLVB;

	}


	/**
	 * This method is used to check the entity and Currency Group access .
	 * @return
	 * @throws SwtException
	 */
	public String checkTransAccess() throws SwtException {
		/* Method's local variable and class instance declaration */
		int ilmCcyAccess;
		String entityId = null;
		String currencyCode = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					+ "- [checkTransAccess] - Enter");
			entityId = request.getParameter("selectedEntityId");
			currencyCode = request.getParameter("selectedCurrencyCode");
			// Get the Access for the entity and currency
			ilmCcyAccess = SwtUtil.getCcyGrpAccessType(request, SwtUtil.getCurrentHostId(), entityId, currencyCode);
			response.getWriter().print(ilmCcyAccess);
			log.debug(this.getClass().getName() + " - [checkTransAccess] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkTransAccess] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkTransAccess",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

	/**
	 * This method is used to check the filter condition clause .
	 * @return
	 * @throws SwtException
	 */
	public String testFilterCondtionClause() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable and class instance declaration */
		String filterCondition = null;
		String resultQuery = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [testFilterCondtionClause] - Enter");
			filterCondition = SwtUtil.decode64(request.getParameter("filterCondition"));
			// Get the result
			resultQuery = ilmTransScenarioMaintenanceManager.getFilterConditionResult(filterCondition);
			response.getWriter().print(resultQuery);
			log.debug(this.getClass().getName() + " - [testFilterCondtionClause] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [testFilterCondtionClause] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"testFilterCondtionClause",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}
	/**
	 * This method is used to check the possibility to delete transaction set.
	 * @return
	 * @throws SwtException
	 */
	public String checkPossibilityToDelete() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		/* Method's local variable and class instance declaration */
		String transactionSetId = null;
		Boolean isAlreadyUsed = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [checkPossibilityToDelete] - Enter");
			transactionSetId = request.getParameter("selectedTxId");
			// Get the result
			isAlreadyUsed = ilmTransScenarioMaintenanceManager.isTransactionSetAlreadyUsed(transactionSetId);
			response.getWriter().print(!isAlreadyUsed);
			log.debug(this.getClass().getName() + " - [checkPossibilityToDelete] - "
					+ "Exit");
			return null;
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkPossibilityToDelete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp,
							"checkPossibilityToDelete",
							ILMTransScenarioMaintenanceAction.class), request,
					"");
			return getView("fail");
		}
	}

}
