/*
 * @(#)MessageFieldsAction.java  21/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

// Servlet Related classes
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.context.ApplicationContext;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.service.ErrorLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.MessageFields;
import org.swallow.maintenance.model.ScenarioMessageFields;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.maintenance.service.MessageFieldsManager;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;




















import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/messagefields", "/messagefields.do"})
public class MessageFieldsAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/messagefieldadd");
		viewMap.put("changeScenario", "jsp/maintenance/scenariomessagefieldadd");
		viewMap.put("successScenario", "jsp/maintenance/scenariomessagefieldformat");
		viewMap.put("fail", "error");
		viewMap.put("addScenario", "jsp/maintenance/scenariomessagefieldadd");
		viewMap.put("success", "jsp/maintenance/messagefieldformat");
		viewMap.put("change", "jsp/maintenance/messagefieldadd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}




	private MessageFields messageFields;
	public MessageFields getMessageFields() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		messageFields = RequestObjectMapper.getObjectFromRequest(MessageFields.class, request);

		return messageFields;
	}

	public void setMessageFields(MessageFields messageFields) {
		this.messageFields = messageFields;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("messageFields", messageFields);
	}
	private ScenarioMessageFields scenarioMessageFields;
	public ScenarioMessageFields getScenarioMessageFields() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		scenarioMessageFields = RequestObjectMapper.getObjectFromRequest(ScenarioMessageFields.class, request);

		return scenarioMessageFields;
	}

	public void setScenarioMessageFields(ScenarioMessageFields scenarioMessageFields) {
		this.scenarioMessageFields = scenarioMessageFields;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("scenarioMessageFields", scenarioMessageFields);
	}


	@Autowired
	private MessageFieldsManager messageFieldsManager = null;

	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(MessageFieldsAction.class);

	/**
	 * @param messageFieldsManager
	 */
	public void setMessageFieldsManager(
			MessageFieldsManager messageFieldsManager) {
		this.messageFieldsManager = messageFieldsManager;
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {


		method = String.valueOf(method);
		switch (method) {
			case "fields":
				return fields();
			case "display":
				return display();
			case "change":
				return change();
			case "view":
				return view();
			case "update":
				return update();
			case "delete":
				return delete();
			case "add":
				return add();
			case "save":
				return save();
			case "insert":
				return insert();
			case "insertScenario":
				return insertScenario();
			case "scenarioFormatFields":
				return scenarioFormatFields();
			case "scenarioFormatDisplay":
				return scenarioFormatDisplay();
			case "scenarioFormatChange":
				return scenarioFormatChange();
			case "scenarioFormatView":
				return scenarioFormatView();
			case "scenarioFormatUpdate":
				return scenarioFormatUpdate();
			case "scenarioFormatDelete":
				return scenarioFormatDelete();
			case "scenarioFormatAdd":
				return scenarioFormatAdd();
			case "scenarioFormatSave":
				return scenarioFormatSave();
		}










































		return null;
	}
	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {

		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		return hostId;
	}

	private ApplicationContext ctx = null;

	/*
	 * Start :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * This method is used to get the message field details collection from
	 * session and set the format type and controls the button enable and
	 * disable status
	 * @return
	 * @throws SwtException
	 */
	public String fields()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration for dyna validator form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold current format type
		String currentFormatType = null;
		// variable to hold field size
		String fieldSize = null;
		// variable to hold message field collection
		Collection collMsgFlds = null;
		// variable to hold format type
		String formatType = null;
		// variable declaration for iterator
		Iterator messageFieldItr = null;
		// variable declaration for existing message field
		MessageFields msgFldExisting = null;
		try {
			log.debug("Entering Fields method");
			// Getting the form values
			// get the size of the message fields
			fieldSize = request.getParameter("fieldSize");
			// get the message field details in session
			collMsgFlds = (Collection) request.getSession().getAttribute(
					"messageFieldDetailsInSession");
			if (collMsgFlds != null) {
				messageFieldItr = collMsgFlds.iterator();
				while (messageFieldItr.hasNext()) {
					msgFldExisting = (MessageFields) (messageFieldItr.next());
					if (msgFldExisting.getLineNoAsString() != null
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;
					} else if (msgFldExisting.getStartPos() != null
							&& !(msgFldExisting.getStartPos().equals(""))) {
						currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;
						break;
					} else {
						currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;
						break;
					}
				}
				// get format type from request
				formatType = request.getParameter("formatType");
				// checks the format id equal to current format type
				if (!formatType.equals(currentFormatType)) {
					collMsgFlds = new ArrayList();
					request.getSession().setAttribute(
							"messageFieldDetailsInSession", null);
					request.getSession().setAttribute(
							"messageFieldDetailsInSessionSize", "0");
				}

			} else
				collMsgFlds = new ArrayList();
			// checks the message field size if zero make the message details in
			// session as zero
			if (fieldSize.equals("0")) {
				collMsgFlds = new ArrayList();
				request.getSession().setAttribute(
						"messageFieldDetailsInSession", null);
				request.getSession().setAttribute(
						"messageFieldDetailsInSessionSize", "0");
			}
			formatType = request.getParameter("formatType");
			collMsgFlds = sortMsgFldDetails(request, collMsgFlds);

			// set message field collection in request
			request.setAttribute("msgFieldDetails", collMsgFlds);
			// set button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE);
			// set format type in session
			request.getSession()
					.setAttribute("formatTypeInSession", formatType);
			checkViewOperation(request);
			// put entity list in request
			putEntityListInReq(request);
			log.debug("Exiting Fields method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'fields' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'fields' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "fields", MessageFieldsAction.class), request, "");
			return getView("fail");
		} finally {
			formatType = null;
			currentFormatType = null;
			fieldSize = null;
		}
	}

	/* End :code modified by sandeepkumar for mantis 2092:Session Related issues */
	/**
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering display method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			MessageFields msgfld = (MessageFields) getMessageFields();

			Collection collMsgFlds = (Collection) request.getSession()
					.getAttribute("messageFieldDetailsInSession");
			if (collMsgFlds == null)
				collMsgFlds = new ArrayList();
			Collections.sort((List) collMsgFlds);
			String isSeqNoExisting = request.getParameter("isSeqNoExisting");
			if (isSeqNoExisting != null)
				isSeqNoExisting = isSeqNoExisting.trim();
			if (isSeqNoExisting != null && isSeqNoExisting.equals("yes"))
				request.setAttribute("isSeqNoExisting", "yes");

			fieldStatus(request);
			collMsgFlds = sortMsgFldDetails(request, collMsgFlds);
			request.setAttribute("msgFieldDetails", collMsgFlds);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE);
			checkViewOperation(request);
			return getView("success");

		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		String hostId = putHostIdListInReq(request);

		EntityManager entityManager = (EntityManager) (SwtUtil
				.getBean("entityManager"));
		Collection collEntity = entityManager.getEntityList(hostId);
		request.setAttribute("entities", collEntity);

	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'change' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			MessageFields msgField = new MessageFields();

			String hostId = putHostIdListInReq(request);
			msgField.getId().setHostId(hostId);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();
			String fldType = request.getParameter("fldType");
			fldType = fldType.trim();
			if (fldType.equals(SwtConstants.FIELD_TEXT))
				fldType = SwtConstants.FIELD_TYPE_TEXT;
			else if (fldType.equals(SwtConstants.FIELD_KEYWORD))
				fldType = SwtConstants.FIELD_TYPE_KEYWORD;
			else
				fldType = SwtConstants.FIELD_TYPE_HEXADECIMAL;
			String value = SwtUtil.decode64(request.getParameter("value"));
			if (value != null && !(value.equals(""))) {
				value = value.trim();

			}
			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();
			String endPos = request.getParameter("endPos");

			if (endPos != null && !(endPos.equals("")))
				endPos = endPos.trim();

			msgField.setLineNoAsString(lineNoAsString);
			msgField.setSeqNoAsString(seqNoAsString);
			msgField.setValue(value);
			msgField.setFieldType(fldType);
			msgField.setStartPos(startPos);
			msgField.setEndPos(endPos);

			putKeyWordsIntoRequest(request);

			if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD))
				msgField.setValueKeyWord(msgField.getValue());

			setMessageFields(msgField);

			request.setAttribute("methodName", "update");
			request.setAttribute("selectedSeqNo", seqNoAsString);
			request.setAttribute("selectedLineNo", lineNoAsString);
			request.setAttribute("selectedStartPos", startPos);
			request.setAttribute("selectedEndPos", endPos);
			request.setAttribute("selectedValue", value);
			fieldStatus(request);
			request.setAttribute("fieldType", fldType);

			log.debug("exiting 'change' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'change' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'change' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */

	public String view()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'view' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			MessageFields msgField = new MessageFields();

			String hostId = putHostIdListInReq(request);
			msgField.getId().setHostId(hostId);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();
			String fldType = request.getParameter("fldType");
			fldType = fldType.trim();
			if (fldType.equals(SwtConstants.FIELD_TEXT))
				fldType = SwtConstants.FIELD_TYPE_TEXT;
			else if (fldType.equals(SwtConstants.FIELD_KEYWORD))
				fldType = SwtConstants.FIELD_TYPE_KEYWORD;
			else
				fldType = SwtConstants.FIELD_TYPE_HEXADECIMAL;
			String value = SwtUtil.decode64(request.getParameter("value"));
			if (value != null && !(value.equals("")))
				value = value.trim();
			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();
			String endPos = request.getParameter("endPos");
			if (endPos != null && !(endPos.equals("")))
				endPos = endPos.trim();
			msgField.setLineNoAsString(lineNoAsString);
			msgField.setSeqNoAsString(seqNoAsString);
			msgField.setValue(value);
			msgField.setFieldType(fldType);
			msgField.setStartPos(startPos);
			msgField.setEndPos(endPos);
			putKeyWordsIntoRequest(request);
			if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD))
				msgField.setValueKeyWord(msgField.getValue());
			setMessageFields(msgField);
			fieldStatus(request);
			request.setAttribute("fieldType", fldType);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("isViewField", "yes");
			log.debug("exiting 'view' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'view' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'view' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}
	/*
	 * Start :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * This method is used to update the changes in change message field screen
	 *
	 * @return
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration for dyna form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message field
		MessageFields msgFld = null;
		// variable declaration for new message field details
		Collection newMessageFieldDetailsInSession = null;
		// variable to hold format type
		String formatType = null;
		// variable declaration for message field details
		Collection messageFieldDetailsInSession = null;
		// variable to hold selected sequence number
		String selectedSeqNo = null;
		// variable to hold selected start position
		String selectedStartPos = null;
		// variable to hold selected end position
		String selectedEndPos = null;
		// variable to hold new line number
		String newLineNo = null;
		// variable to hold selected line number
		String selectedLineNo = null;
		// variable to hold existing message field
		MessageFields msgFldExisting = null;
		// variable to hold existing line number
		String lineNoExisting = null;
		// variable to hold existing start position
		String startPosExisting = null;
		// variable to hold existing sequence number
		String seqNoExisting = null;
		// variable to hold existing end position number
		String endPosExisting = null;
		// variable to hold new sequence number
		String newSeqNo = null;
		//variable to hold message field iterator
		Iterator msgFieldItr = null;
		//variable declaration for is record added
		boolean isRecordAdded = false;
		try {
			log.debug("Entering 'update' method");
			// get form values to dyform
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get message field values from form
			msgFld = (MessageFields) getMessageFields();
			if(msgFld != null && !SwtUtil.isEmptyOrNull(msgFld.getValue())){
				msgFld.setValue(SwtUtil.decode64(msgFld.getValue()));
			}
			// checks sequence number ,line number ,start position,end position
			// not null and set in message field bean
			if (msgFld.getSeqNoAsString() != null)
				msgFld.setSeqNoAsString(msgFld.getSeqNoAsString().trim());
			if (msgFld.getLineNoAsString() != null)
				msgFld.setLineNoAsString(msgFld.getLineNoAsString().trim());
			if (msgFld.getStartPos() != null)
				msgFld.setStartPos(msgFld.getStartPos());
			if (msgFld.getEndPos() != null)
				msgFld.setEndPos(msgFld.getEndPos().trim());
			//checks the field type is equal to keyword
			if (msgFld.getFieldType().equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
				msgFld.setValue(msgFld.getValueKeyWord());
			}
			//instance for new message field details in session
			newMessageFieldDetailsInSession = new ArrayList();
			//put field status in request
			formatType = fieldStatus(request);
			//get message field details in session
			messageFieldDetailsInSession = (Collection) request.getSession()
					.getAttribute("messageFieldDetailsInSession");
			//get selected sequence number from request
			selectedSeqNo = request.getParameter("selectedSeqNo");
			//checks the selected sequence number not null
			if (selectedSeqNo != null)
				selectedSeqNo = selectedSeqNo.trim();
			//get selected start position from request
			selectedStartPos = request.getParameter("selectedStartPos");
			//get selected end position from request
			selectedEndPos = request.getParameter("selectedEndPos");
			//checks the selected start position not null
			if (selectedStartPos != null)
				selectedStartPos = selectedStartPos.trim();
			//checks the selected end position not null
			if (selectedEndPos != null)
				selectedEndPos = selectedEndPos.trim();
			//check the format type equal to tagged variable
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
				//get line number from bean
				newLineNo = msgFld.getLineNoAsString();
				newLineNo = newLineNo.trim();
				//get selected line number from request
				selectedLineNo = request.getParameter("selectedLineNo");
				selectedLineNo = selectedLineNo.trim();
				//iterating the message field details
				msgFieldItr = messageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {
					msgFldExisting = (MessageFields) (msgFieldItr.next());
					lineNoExisting = msgFldExisting.getLineNoAsString().trim();
					startPosExisting = msgFldExisting.getStartPos();
					seqNoExisting = msgFldExisting.getSeqNoAsString().trim();
					if (lineNoExisting.equals(selectedLineNo)
							&& startPosExisting.equals(selectedStartPos)
							&& seqNoExisting.equals(selectedSeqNo)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newMessageFieldDetailsInSession.add(msgFldExisting);

				}
			}
			else if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
				newLineNo = msgFld.getLineNoAsString();
				newLineNo = newLineNo.trim();
				selectedLineNo = request.getParameter("selectedLineNo");
				selectedLineNo = selectedLineNo.trim();
				msgFieldItr = messageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (MessageFields) (msgFieldItr.next());
					lineNoExisting = msgFldExisting.getLineNoAsString().trim();
					startPosExisting = msgFldExisting.getStartPos();
					endPosExisting = msgFldExisting.getEndPos();
					if (lineNoExisting.equals(selectedLineNo)
							&& startPosExisting.equals(selectedStartPos)
							&& endPosExisting.equals(selectedEndPos)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newMessageFieldDetailsInSession.add(msgFldExisting);

				}
			} else if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {

				msgFieldItr = messageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (MessageFields) (msgFieldItr.next());
					startPosExisting = msgFldExisting.getStartPos();
					endPosExisting = msgFldExisting.getEndPos();

					if (startPosExisting.equals(selectedStartPos)
							&& endPosExisting.equals(selectedEndPos)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newMessageFieldDetailsInSession.add(msgFldExisting);
				}
			}

			else {
				newSeqNo = msgFld.getSeqNoAsString();
				newSeqNo = newSeqNo.trim();

				msgFieldItr = messageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (MessageFields) (msgFieldItr.next());
					seqNoExisting = msgFldExisting.getSeqNoAsString().trim();
					if (seqNoExisting.equals(selectedSeqNo)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newMessageFieldDetailsInSession.add(msgFldExisting);
				}
			}
			//put message field details in session in request
			request.getSession().setAttribute("messageFieldDetailsInSession",
					newMessageFieldDetailsInSession);
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
				isRecordAdded = validateRecordMultiLineVariable(request,
						msgFld, false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
				isRecordAdded = validateRecordWithLineNo(request, msgFld, false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
				isRecordAdded = validateRecordWithoutLineNo(request, msgFld,
						false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
				isRecordAdded = validateRecordDelimited(request, msgFld, false);

			if (isRecordAdded)
				//set parent form refresh value in request
				request.setAttribute("parentFormRefresh", "yes");
			//set message field values in dyform
			setMessageFields(msgFld);
			//set field type in request
			request.setAttribute("fieldType", msgFld.getFieldType());
			//set field status in request
			fieldStatus(request);
			//put key word details in request
			putKeyWordsIntoRequest(request);
			//set method name in request
			request.setAttribute("methodName", "update");
			log.debug("exiting 'update' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'update' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("add");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'update' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MessageFieldsAction.class), request, "");
			return getView("fail");
		} finally {
			// nullifying objects
			selectedSeqNo = null;
			selectedStartPos = null;
			selectedEndPos = null;
			newLineNo = null;
			selectedLineNo = null;
			msgFldExisting = null;
			lineNoExisting = null;
			startPosExisting = null;
			seqNoExisting = null;
			endPosExisting = null;
			newSeqNo = null;
		}
	}
	/*
	 * End :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * @return
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		try {
			log.debug("entering 'delete' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MessageFields msgField = new MessageFields();
			String selectedSeqNo = (String) request
					.getParameter("selectedSeqNo");
			String selectedLineNo = (String) request
					.getParameter("selectedLineNo");
			String selectedStartPos = request.getParameter("selectedStartPos");
			String selectedEndPos = request.getParameter("selectedEndPos");
			if (selectedStartPos != null)
				selectedStartPos = selectedStartPos.trim();
			if (selectedStartPos != null)
				selectedEndPos = selectedEndPos.trim();
			if (selectedSeqNo != null)
				selectedSeqNo = selectedSeqNo.trim();
			if (selectedLineNo != null)
				selectedLineNo = selectedLineNo.trim();
			String formatType = fieldStatus(request);
			Collection newMessageFieldDetailsInSession = new ArrayList();
			Collection messageFieldDetailsInSession = (Collection) request
					.getSession().getAttribute("messageFieldDetailsInSession");
			if (messageFieldDetailsInSession != null) {
				Iterator itr = messageFieldDetailsInSession.iterator();
				while (itr.hasNext()) {
					MessageFields messageFields = (MessageFields) (itr.next());
					if (formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
						String existingStartPos = messageFields.getStartPos();

						String existingLineNo = messageFields
								.getLineNoAsString();
						if (existingStartPos.equals(selectedStartPos)
								&& existingLineNo.equals(selectedLineNo)) {

						} else
							newMessageFieldDetailsInSession.add(messageFields);
					} else if (formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
						String existingStartPos = messageFields.getStartPos();
						String existingEndPos = messageFields.getEndPos();
						String existingLineNo = messageFields
								.getLineNoAsString();
						if (existingStartPos.equals(selectedStartPos)
								&& existingEndPos.equals(selectedEndPos)
								&& existingLineNo.equals(selectedLineNo)) {

						} else
							newMessageFieldDetailsInSession.add(messageFields);
					} else if (formatType
							.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
						String existingEndPos = messageFields.getEndPos();
						String existingStartPos = messageFields.getStartPos();
						if (existingStartPos.equals(selectedStartPos)
								&& existingEndPos.equals(selectedEndPos)) {

						} else
							newMessageFieldDetailsInSession.add(messageFields);

					} else {
						String existingSeqNo = messageFields.getSeqNoAsString();
						if (existingSeqNo.equals(selectedSeqNo)) {

						} else
							newMessageFieldDetailsInSession.add(messageFields);
					}
				}
			}
			String collSize = (String) request.getSession().getAttribute(
					"messageFieldDetailsInSessionSize");
			int i = new Integer(collSize).intValue() - 1;
			String decrementedSize = new Integer(i).toString();

			request.getSession().setAttribute(
					"messageFieldDetailsInSessionSize", decrementedSize);

			request.getSession().setAttribute("messageFieldDetailsInSession",
					newMessageFieldDetailsInSession);
			display();

			log.debug("exiting 'delete' method");
			return getView("success");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'delete' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("success");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'delete' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'add' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MessageFields msgField = new MessageFields();
			String hostId = putHostIdListInReq(request);
			msgField.getId().setHostId(hostId);
			setMessageFields(msgField);
			request.setAttribute("fieldType", SwtConstants.FIELD_TYPE_TEXT);
			fieldStatus(request);
			String formatType= fieldStatus(request);
			putKeyWordsIntoRequest(request);
			// set format type in session
			request.getSession()
					.setAttribute("formatTypeInSession",formatType);
			request.setAttribute("methodName", "save");
			putEntityListInReq(request);
			log.debug("exiting 'add' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'add' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'add' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		try {
			log.debug("entering 'save' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MessageFields msgfld = (MessageFields) getMessageFields();


			if(msgfld != null && !SwtUtil.isEmptyOrNull(msgfld.getValue())){
				msgfld.setValue(SwtUtil.decode64(msgfld.getValue()));
			}
			if (msgfld.getSeqNoAsString() != null)
				msgfld.setSeqNoAsString(msgfld.getSeqNoAsString().trim());
			if (msgfld.getLineNoAsString() != null)
				msgfld.setLineNoAsString(msgfld.getLineNoAsString().trim());
			if (msgfld.getStartPos() != null)
				msgfld.setStartPos(msgfld.getStartPos());
			if (msgfld.getEndPos() != null)
				msgfld.setEndPos(msgfld.getEndPos().trim());
			if (msgfld.getFieldType().equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
				msgfld.setValue(msgfld.getValueKeyWord());
			}
			if (msgfld.getFieldType() != null)
				request.setAttribute("fieldType", msgfld.getFieldType());
			String hostId = putHostIdListInReq(request);
			msgfld.getId().setHostId(hostId);
			String formatType = fieldStatus(request);
			boolean isRecordAdded = false;
			String insertFlag = request.getParameter("insertFlag");
			boolean insertOperation = false;
			if (insertFlag != null && insertFlag.equals("Y")) {
				insertOperation = true;
			}

			if (!insertOperation) {

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
					isRecordAdded = validateRecordMultiLineVariable(request,
							msgfld, false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
					isRecordAdded = validateRecordWithLineNo(request, msgfld,
							false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
					isRecordAdded = validateRecordWithoutLineNo(request,
							msgfld, false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
					isRecordAdded = validateRecordDelimited(request, msgfld,
							false);
			} else {

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
					isRecordAdded = validateRecordMultiLineVariable(request,
							msgfld, insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
					isRecordAdded = validateRecordWithLineNo(request, msgfld,
							insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
					isRecordAdded = validateRecordWithoutLineNo(request,
							msgfld, insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
					isRecordAdded = validateRecordDelimited(request, msgfld,
							insertOperation);
				isRecordAdded = true; // always add the record
			}

			putKeyWordsIntoRequest(request);
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			request.setAttribute("methodName", "save");
			if (isRecordAdded) {
				request.setAttribute("parentFormRefresh", "yes");
				String collSize = (String) request.getSession().getAttribute(
						"messageFieldDetailsInSessionSize");
				int i = new Integer(collSize).intValue() + 1;
				String incrementedSize = new Integer(i).toString();
				request.getSession().setAttribute(
						"messageFieldDetailsInSessionSize", incrementedSize);
			}
			log.debug("exiting 'save' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'save' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'save' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus, String cancelStatus) {
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);

		req.setAttribute(SwtConstants.CAN_BUT_STS, cancelStatus);
	}

	private void putKeyWordsIntoRequest(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putKeyWordsIntoRequest' method");
		Collection keyWords = new ArrayList();
		Collection keyWords1 = messageFieldsManager.getKeyWordsList();
		keyWords.add(new LabelValueBean("key1", "key1"));
		request.setAttribute("keyWords", keyWords1);
		log.debug("exiting 'putKeyWordsIntoRequest' method");
	}

	/**
	 *
	 * @param request
	 * @return
	 */
	private String fieldStatus(HttpServletRequest request) {
		log.debug("entering 'fieldStatus' method");
		String formatType = (String) request.getSession().getAttribute(
				"formatTypeInSession");

		if (formatType == null || formatType.equals(""))
			formatType = SwtConstants.FORMAT_TYPE_FIXED;
		// log.debug("The formatType got from the session is ==>" + formatType);
		if (formatType != null
				&& (formatType.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_FIXED) || formatType
				.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_DELIMITED)))
			request.setAttribute("showLineNo", SwtConstants.NO);
		else
			request.setAttribute("showLineNo", SwtConstants.YES);

		if (formatType != null
				&& (formatType.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_FIXED)
				|| formatType
				.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_TAGGED) || formatType
				.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)))
			request.setAttribute("showPosition", SwtConstants.YES);
		else
			request.setAttribute("showPosition", SwtConstants.NO);
		if (formatType != null
				&& formatType
				.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
			request.setAttribute("disableEndPos", SwtConstants.YES);
		} else {
			request.setAttribute("disableEndPos", SwtConstants.NO);
		}

		log.debug("exiting 'fieldStatus' method");
		return formatType;
	}

	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateRecordWithLineNo(HttpServletRequest request,
											 MessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordWithLineNo method");
		String lineNo = msgfld.getLineNoAsString().trim();
		msgfld.setLineNoAsString(msgfld.getLineNoAsString());
		msgfld.setStartPos(msgfld.getStartPos().trim());
		msgfld.setEndPos(msgfld.getEndPos().trim());
		if (msgfld.getLineNoAsString() != null) {
			msgfld.setLineNo(new Integer(lineNo));
		}
		boolean isLineNoExisting = false; // This flag checks if the seq no to
		// be added already exists
		boolean isRangeOverlapping = false; // This flag checks if the range
		// overlaps with any of the previous
		// range boolean isRecordAdded =
		// false;
		boolean isCheck = false;
		boolean isRecordAdded = false;

		Collection messageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("messageFieldDetailsInSession");
		if (messageFieldDetailsInSession != null) {
			int siz = messageFieldDetailsInSession.size();

			Iterator itr = messageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				MessageFields msgField = (MessageFields) (itr.next());
				if (!isLineNoExisting) {
					if (msgField.getLineNoAsString().equals(lineNo)) {
						isLineNoExisting = true;
						// break;
					}
				}

				if (insertOperation) {
					if (msgField.getLineNo().intValue() >= msgfld.getLineNo()
							.intValue()) {
						int updatedLineNo = msgField.getLineNo().intValue() + 1;
						msgField.setLineNo(new Integer(updatedLineNo));
						msgField.setLineNoAsString(msgField.getLineNo()
								.toString());
					}
				}
			}

			if (isLineNoExisting && !insertOperation) {

				/**
				 * startPosMax[] -- This array stores the end positions of all
				 * the records. endPosMin[] -- This array stores the starting
				 * position of all the records. The end position of any new
				 * record should be either greater than all elements of array
				 * startPosMax[] or lesser than all elements of the array
				 * endPosMin[] If that does not happen ==> Range is overlapping
				 */
				int startPosMax[] = new int[siz];
				int endPosMin[] = new int[siz];
				int i = 0;

				itr = messageFieldDetailsInSession.iterator();
				while (itr.hasNext()) {
					MessageFields msgField = (MessageFields) (itr.next());
					if (msgField.getLineNoAsString().equals(lineNo)) {
						startPosMax[i] = Integer.valueOf(
								msgField.getEndPos().trim()).intValue();
						endPosMin[i] = Integer.valueOf(
								msgField.getStartPos().trim()).intValue();
						i++;
					}
				}
				int sizWrtLineNo = i;
				// og.debug("The value of the sizWrtLineNo==>" + sizWrtLineNo);

				int startPos = Integer.valueOf(msgfld.getStartPos().trim())
						.intValue();
				int endPos = Integer.valueOf(msgfld.getEndPos().trim())
						.intValue();

				for (int j = 0; j < sizWrtLineNo; j++) {
					if (startPos <= startPosMax[j]) {
						isRangeOverlapping = true;
						isCheck = true;
						break;
					}
				}
				if (isCheck) {
					for (int j = 0; j < sizWrtLineNo; j++) {
						if (endPos >= endPosMin[j]) {
							isRangeOverlapping = true;
							isCheck = true;
							break;
						} else {
							isRangeOverlapping = false;
							isCheck = false;

						}
					}
				}
				if (isCheck) {
					int temp = startPosMax[0];
					for (int k = 0; k < sizWrtLineNo; k++) {
						if (temp < startPosMax[k])
							temp = startPosMax[k];
					}

					int midIntervalArray[] = new int[temp];
					for (int k = 0; k < temp; k++) {
						midIntervalArray[k] = k + 1;
					}
					// log.debug("after putting the elements in the array upto
					// max valueof the end postion
					// is==>"+midIntervalArray.toString());

					for (int k = 0; k < sizWrtLineNo; k++) {
						for (int p = endPosMin[k] - 1; p <= startPosMax[k] - 1; p++)
							midIntervalArray[p] = -1;
					}

					int startPosIndex = 0;
					int endPosIndex = 0;
					boolean isMidIntervalRange = true;

					for (int k = 0; k < temp; k++) {
						if (midIntervalArray[k] == startPos) {
							startPosIndex = k;
							isMidIntervalRange = false;
							break;
						}
						isMidIntervalRange = true;
					}

					if (!isMidIntervalRange) {
						for (int k = 0; k < temp; k++) {
							if (midIntervalArray[k] == endPos) {
								endPosIndex = k;
								isMidIntervalRange = false;
								break;
							}
							isMidIntervalRange = true;
						}
					}

					if (isMidIntervalRange)
						isRangeOverlapping = true;
					else {
						// log.debug("Inside else====>");
						for (int k = startPosIndex; k <= endPosIndex; k++)
							if (midIntervalArray[k] == -1) {
								isRangeOverlapping = true;
								break;
							}
						isRangeOverlapping = false;
					}
				}
				if (!(isRangeOverlapping)) {
					// add the record
					messageFieldDetailsInSession.add(msgfld);
					isRecordAdded = true;
				} else {
					request.setAttribute("isRangeOverlapping", "yes");
				}

			} else {
				messageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			}
		} else {
			// add into the collection
			messageFieldDetailsInSession = new ArrayList();

			messageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;

		}
		request.getSession().setAttribute("messageFieldDetailsInSession",
				messageFieldDetailsInSession);
		log.debug("Exiting the validateRecordWithLineNo method");
		return isRecordAdded;

	}

	/* Start : Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */
	private boolean validateRecordMultiLineVariable(HttpServletRequest request,
													MessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordMultiLineVariable method");
		String lineNo = msgfld.getLineNoAsString().trim();
		msgfld.setLineNoAsString(msgfld.getLineNoAsString());
		msgfld.setStartPos(msgfld.getStartPos().trim());

		if (msgfld.getLineNoAsString() != null) {
			msgfld.setLineNo(new Integer(lineNo));
		}

		if (msgfld.getStartPos() != null
				&& !msgfld.getStartPos().equalsIgnoreCase("")) {
			msgfld.setStartPosInt(Integer.valueOf(msgfld.getStartPos()));
		}

		String seqNo = msgfld.getSeqNoAsString().trim();
		// log.debug("The seq no is==" + seqNo);

		if (msgfld.getSeqNoAsString() != null
				&& !msgfld.getSeqNoAsString().equals("")) {
			msgfld.setSeqNo(new Integer(seqNo));
		}

		boolean isLineNoExisting = false; // This flag checks if the line no
		// to
		// be added already exists
		boolean isCheck = false;
		boolean isRecordAdded = false;
		int seqNoTemp = 0;

		// boolean insertOperation = true;

		Collection messageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("messageFieldDetailsInSession");
		if (messageFieldDetailsInSession != null) {
			int siz = messageFieldDetailsInSession.size();

			Iterator itr = messageFieldDetailsInSession.iterator();
			if (msgfld.getSeqNoAsString() == null
					|| msgfld.getSeqNoAsString().equals("")) {
				while (itr.hasNext()) {
					MessageFields msgField = (MessageFields) (itr.next());

					if (msgField.getLineNoAsString().equals(lineNo)) {
						if (msgField.getSeqNo() != null
								&& msgField.getSeqNo().intValue() > seqNoTemp) {
							seqNoTemp = msgField.getSeqNo().intValue();
						}
					}
					if (insertOperation) {
						if (msgField.getLineNo().intValue() >= msgfld
								.getLineNo().intValue()) {
							int updatedLineNo = msgField.getLineNo().intValue() + 1;
							msgField.setLineNo(new Integer(updatedLineNo));
							msgField.setLineNoAsString(msgField.getLineNo()
									.toString());
						}
					}
				}
				if (insertOperation) {
					seqNoTemp = 0;
				}
				msgfld.setSeqNo(Integer.valueOf(seqNoTemp + 1));
				msgfld.setSeqNoAsString(msgfld.getSeqNo().toString());
				messageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {
				boolean isRecordExisting = false;
				while (itr.hasNext()) {
					MessageFields msgField = (MessageFields) (itr.next());

					if (msgField.getLineNoAsString().equals(lineNo)
							&& msgField.getSeqNoAsString().equals(seqNo)) {
						request.setAttribute("recordExists", "Y");
						isRecordExisting = true;
						break;
					}
				}
				if (!isRecordExisting) {
					msgfld.setSeqNo(Integer.valueOf(msgfld.getSeqNoAsString()));
					// log.debug("the record added is" + msgfld);
					messageFieldDetailsInSession.add(msgfld);
					isRecordAdded = true;
				}
			}
		} else {
			// add into the collection
			messageFieldDetailsInSession = new ArrayList();
			msgfld.setSeqNo(Integer.valueOf(1));
			msgfld.setSeqNoAsString(msgfld.getSeqNo().toString());
			messageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;

		}
		request.getSession().setAttribute("messageFieldDetailsInSession",
				messageFieldDetailsInSession);
		log.debug("Exiting the validateRecordMultiLineVariable method");
		return isRecordAdded;
	}

	/* End : Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */

	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateRecordWithoutLineNo(HttpServletRequest request,
												MessageFields msgfld, boolean insertOperation) {
		boolean isRangeOverlapping = false; // This flag checks if the range
		// overlaps with any of the previous
		// range boolean isRecordAdded =
		// false;
		boolean isCheck = false;
		boolean isRecordAdded = false;
		// boolean = true;

		Collection messageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("messageFieldDetailsInSession");
		boolean flag = false;
		int addFactor = 0;

		msgfld.setStartPosInt(Integer.valueOf(msgfld.getStartPos()));
		if (messageFieldDetailsInSession != null && insertOperation) {

			Iterator itr = messageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				MessageFields msgField = (MessageFields) (itr.next());
				if (msgfld.getStartPosInt().intValue() == msgField
						.getStartPosInt().intValue()) {
					// flag = true;
					addFactor = Integer.valueOf(msgfld.getEndPos()).intValue()
							- msgfld.getStartPosInt().intValue() + 1;
					break;
				}
			}
			// messageFieldDetailsInSession.iterator().

			Iterator itr1 = messageFieldDetailsInSession.iterator();

			while (itr1.hasNext()) {
				MessageFields msgField = (MessageFields) (itr1.next());
				msgField
						.setStartPosInt(Integer.valueOf(msgField.getStartPos()));
				if (msgField.getStartPosInt().intValue() >= msgfld
						.getStartPosInt().intValue()) {
					int updatedStartPos = msgField.getStartPosInt().intValue()
							+ addFactor;
					int updatedEndPos = Integer.valueOf(msgField.getEndPos())
							.intValue()
							+ addFactor;
					msgField.setStartPosInt(new Integer(updatedStartPos));
					msgField.setStartPos(msgField.getStartPosInt().toString());
					msgField.setEndPos(Integer.valueOf(updatedEndPos)
							.toString());
				}
			}
		}

		if (insertOperation)
			messageFieldDetailsInSession.add(msgfld);

		if (messageFieldDetailsInSession != null && !insertOperation) {
			int siz = messageFieldDetailsInSession.size();

			/**
			 * startPosMax[] -- This array stores the end positions of all the
			 * records. endPosMin[] -- This array stores the starting position
			 * of all the records. The end position of any new record should be
			 * either greater than all elements of array startPosMax[] or lesser
			 * than all elements of the array endPosMin[] If that does not
			 * happen ==> Range is overlapping
			 */
			int startPosMax[] = new int[siz];
			int endPosMin[] = new int[siz];
			int i = 0;

			Iterator itr = messageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				MessageFields msgField = (MessageFields) (itr.next());

				startPosMax[i] = Integer.valueOf(msgField.getEndPos().trim())
						.intValue();
				endPosMin[i] = Integer.valueOf(msgField.getStartPos().trim())
						.intValue();
				i++;

			}

			int startPos = Integer.valueOf(msgfld.getStartPos().trim())
					.intValue();
			int endPos = Integer.valueOf(msgfld.getEndPos().trim()).intValue();

			for (int j = 0; j < siz; j++) {
				if (startPos <= startPosMax[j]) {
					isRangeOverlapping = true;
					isCheck = true;
					break;
				}

			}
			if (isCheck) {
				for (int j = 0; j < siz; j++) {
					if (endPos >= endPosMin[j]) {
						isRangeOverlapping = true;
						isCheck = true;
						break;
					} else {
						isRangeOverlapping = false;
						isCheck = false;
					}
				}
			}

			if (isCheck) {
				int temp = startPosMax[0];
				for (int k = 0; k < siz; k++) {
					if (temp < startPosMax[k])
						temp = startPosMax[k];
				}
				int midIntervalArray[] = new int[temp];
				for (int k = 0; k < temp; k++) {
					midIntervalArray[k] = k + 1;
				}
				for (int k = 0; k < siz; k++) {
					for (int p = endPosMin[k] - 1; p <= startPosMax[k] - 1; p++)
						midIntervalArray[p] = -1;
				}
				int startPosIndex = 0;
				int endPosIndex = 0;
				boolean isMidIntervalRange = true;

				for (int k = 0; k < temp; k++) {
					if (midIntervalArray[k] == startPos) {
						startPosIndex = k;
						isMidIntervalRange = false;
						break;
					}
					isMidIntervalRange = true;
				}

				if (!isMidIntervalRange) {
					for (int k = 0; k < temp; k++) {
						if (midIntervalArray[k] == endPos) {
							endPosIndex = k;
							isMidIntervalRange = false;
							break;
						}
						isMidIntervalRange = true;
					}
				}
				if (isMidIntervalRange)
					isRangeOverlapping = true;
				else {
					for (int k = startPosIndex; k <= endPosIndex; k++)
						if (midIntervalArray[k] == -1) {
							isRangeOverlapping = true;
							break;
						}
					isRangeOverlapping = false;
				}
			}
			if (!(isRangeOverlapping)) {
				// add the record
				messageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {
				request.setAttribute("isRangeOverlapping", "yes");
			}

		} else {
			if (!insertOperation) {
				// add into the collection
				messageFieldDetailsInSession = new ArrayList();

				messageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			}
		}
		request.getSession().setAttribute("messageFieldDetailsInSession",
				messageFieldDetailsInSession);
		log.debug("Exiting the validateRecordWithoutLineNo method");
		return isRecordAdded;

	}

	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateRecordDelimited(HttpServletRequest request,
											MessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordDelimited method");
		String seqNo = msgfld.getSeqNoAsString().trim();
		msgfld.setSeqNoAsString(msgfld.getSeqNoAsString().trim());
		if (msgfld.getSeqNoAsString() != null) {
			msgfld.setSeqNo(new Integer(seqNo));
		}
		boolean isSeqNoExisting = false; // This flag checks if the seq no to
		// be
		// added already exists

		boolean isCheck = false;
		boolean isRecordAdded = false;

		// boolean insertOperation = true;

		Collection messageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("messageFieldDetailsInSession");
		if (messageFieldDetailsInSession != null) {
			int siz = messageFieldDetailsInSession.size();

			Iterator itr = messageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				MessageFields msgField = (MessageFields) (itr.next());
				// log.debug("The comparing condition shows" +
				// msgField.getSeqNoAsString() + "==" + msgField.getSeqNo())
				if (!isSeqNoExisting) {
					if (msgField.getSeqNoAsString().equals(seqNo)) {
						isSeqNoExisting = true;
						// break;
					}
				}
				/* SRS Sweep */
				if (insertOperation) {
					// log.debug("The msgField.getSeqNo().intValue() is + " +
					// msgField.getSeqNo().intValue());
					if (msgField.getSeqNo().intValue() >= msgfld.getSeqNo()
							.intValue()) {
						int updatedSeqNo = msgField.getSeqNo().intValue() + 1;
						msgField.setSeqNo(new Integer(updatedSeqNo));
						msgField.setSeqNoAsString(msgField.getSeqNo()
								.toString());
					}
				}
				/* SRS Sweep */
			}

			if (!(isSeqNoExisting)) {
				messageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {

				if (!insertOperation) {
					request.setAttribute("isSeqNoExisting", "yes");
				} else {
					messageFieldDetailsInSession.add(msgfld);
				}
			}

		} else {
			// add into the collection
			messageFieldDetailsInSession = new ArrayList();
			messageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;
		}

		request.getSession().setAttribute("messageFieldDetailsInSession",
				messageFieldDetailsInSession);
		return isRecordAdded;

	}

	/**
	 * This function checks if the FormatMaintenance Screen is opened in view
	 * mode
	 *
	 * @param request
	 */

	private void checkViewOperation(HttpServletRequest request) {
		String isViewFormat = request.getParameter("isViewFormat");
		log
				.debug("The value of the flag isViewFormat inside checkViewOperation is==>"
						+ isViewFormat);
		request.setAttribute("isViewFormat", isViewFormat);
	}

	/* Start: Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */
	// This function is called when insert button is clicked
	public String insert()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'insert' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			MessageFields msgField = new MessageFields();
			String hostId = putHostIdListInReq(request);
			msgField.getId().setHostId(hostId);
			setMessageFields(msgField);
			request.setAttribute("fieldType", SwtConstants.FIELD_TYPE_TEXT);
			fieldStatus(request);
			putKeyWordsIntoRequest(request);

			String formatType = fieldStatus(request);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();

			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)
					|| formatType
					.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {

				msgField.setLineNoAsString(lineNoAsString);
			} else if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				msgField.setSeqNoAsString(seqNoAsString);
			} else {
				msgField.setStartPos(startPos);
			}

			String selectedEndPos = request.getParameter("selectedEndPos");
			if (selectedEndPos != null)
				selectedEndPos = selectedEndPos.trim();

			request.setAttribute("methodName", "save");
			request.setAttribute("insertOperation", "Y");
			request.setAttribute("selectedSeqNo", seqNoAsString);
			request.setAttribute("selectedLineNo", lineNoAsString);
			request.setAttribute("selectedStartPos", startPos);
			request.setAttribute("selectedEndPos", selectedEndPos);
			putEntityListInReq(request);
			log.debug("exiting 'insert' method");
			return getView("add");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'insert' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'insert' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "insert", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/*
	 * This function sorts details on lineNo, seqNo respectively (only for
	 * multi-line variable type) It stores all msgflds objects in a Treemap with
	 * key for each object = an integer variable of its lineNo and seqNo
	 * concatenated respectively The combination allows objects to be sorted
	 *
	 */
	private Collection sortMsgFldDetails(HttpServletRequest request,
										 Collection inputColl) throws SwtException {
		log.debug("Entering  sortMsgFldDetails" + inputColl);
		String formatType = (String) request.getSession().getAttribute(
				"formatTypeInSession");
		Collection collTemp = new ArrayList();
		if (inputColl != null
				&& formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {

			Iterator itr = inputColl.iterator();
			Map ma = new TreeMap();
			while (itr.hasNext()) {
				MessageFields msgField = (MessageFields) (itr.next());
				Integer keyIntObj = Integer.valueOf(msgField
						.getLineNoAsString()
						+ msgField.getSeqNoAsString());
				ma.put(keyIntObj, msgField);
			}
			if (ma != null && ma.size() > 0) {
				Set keySet = ma.keySet();
				Iterator itrKey = keySet.iterator();
				while (itrKey.hasNext()) {
					Integer keyObj = (Integer) (itrKey.next());
					collTemp.add(ma.get(keyObj));
				}
			}
			inputColl = collTemp;
		}
		return inputColl;
	}
	/* End : Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */




	/**
	 * Scenario Message Format methods
	 *
	 */


	/* Start: Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */
	// This function is called when insert button is clicked
	public String insertScenario()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'insert' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			ScenarioMessageFields msgField = new ScenarioMessageFields();
			String hostId = putHostIdListInReq(request);
//			msgField.getId().setHostId(hostId);
			setScenarioMessageFields(msgField);
			request.setAttribute("fieldType", SwtConstants.FIELD_TYPE_TEXT);
			fieldStatus(request);
			putKeyWordsIntoRequest(request);

			String formatType = fieldStatus(request);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();

			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)
					|| formatType
					.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {

				msgField.setLineNoAsString(lineNoAsString);
			} else if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED)) {
				msgField.setSeqNoAsString(seqNoAsString);
			} else {
				msgField.setStartPos(startPos);
			}

			String selectedEndPos = request.getParameter("selectedEndPos");
			if (selectedEndPos != null)
				selectedEndPos = selectedEndPos.trim();

			request.setAttribute("methodName", "save");
			request.setAttribute("insertOperation", "Y");
			request.setAttribute("selectedSeqNo", seqNoAsString);
			request.setAttribute("selectedLineNo", lineNoAsString);
			request.setAttribute("selectedStartPos", startPos);
			request.setAttribute("selectedEndPos", selectedEndPos);
			//TODO:
			putEntityListInReq(request);
			log.debug("exiting 'insert' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'insert' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'insert' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "insert", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}


	/*
	 * This function sorts details on lineNo, seqNo respectively (only for
	 * multi-line variable type) It stores all msgflds objects in a Treemap with
	 * key for each object = an integer variable of its lineNo and seqNo
	 * concatenated respectively The combination allows objects to be sorted
	 *
	 */
	private Collection sortScenarioMsgFldDetails(HttpServletRequest request,
												 Collection inputColl) throws SwtException {
		log.debug("Entering  sortMsgFldDetails" + inputColl);
		String formatType = (String) request.getSession().getAttribute(
				"formatTypeInSession");
		Collection collTemp = new ArrayList();
		if (inputColl != null
				&& formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {

			Iterator itr = inputColl.iterator();
			Map ma = new TreeMap();
			while (itr.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());
				Integer keyIntObj = Integer.valueOf(msgField
						.getLineNoAsString()
						+ msgField.getSeqNoAsString());
				ma.put(keyIntObj, msgField);
			}
			if (ma != null && ma.size() > 0) {
				Set keySet = ma.keySet();
				Iterator itrKey = keySet.iterator();
				while (itrKey.hasNext()) {
					Integer keyObj = (Integer) (itrKey.next());
					collTemp.add(ma.get(keyObj));
				}
			}
			inputColl = collTemp;
		}
		return inputColl;
	}



	/*
	 * Start :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * This method is used to get the message field details collection from
	 * session and set the format type and controls the button enable and
	 * disable status
	 *
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatFields()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration for dyna validator form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold current format type
		String currentFormatType = null;
		// variable to hold field size
		String fieldSize = null;
		// variable to hold message field collection
		Collection collMsgFlds = null;
		// variable to hold format type
		String formatType = null;
		// variable declaration for iterator
		Iterator messageFieldItr = null;
		// variable declaration for existing message field
		ScenarioMessageFields msgFldExisting = null;
		try {
			log.debug("Entering Fields method");
			// Getting the form values
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get the size of the message fields
			fieldSize = request.getParameter("fieldSize");
			// get the message field details in session
			collMsgFlds = (Collection) request.getSession().getAttribute(
					"scenarioMessageFieldDetailsInSession");
			if (collMsgFlds != null) {
				messageFieldItr = collMsgFlds.iterator();
				while (messageFieldItr.hasNext()) {
					msgFldExisting = (ScenarioMessageFields) (messageFieldItr.next());
					if (msgFldExisting.getLineNoAsString() != null
							&& !(msgFldExisting.getLineNoAsString().equals(""))) {
						if (msgFldExisting.getEndPos() == null
								|| msgFldExisting.getEndPos().equals("0")
								|| msgFldExisting.getEndPos().equals(""))
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE;
						else
							currentFormatType = SwtConstants.FORMAT_TYPE_TAGGED;
						break;
					} else if (msgFldExisting.getStartPos() != null
							&& !(msgFldExisting.getStartPos().equals(""))) {
						currentFormatType = SwtConstants.FORMAT_TYPE_FIXED;
						break;
					} else {
						currentFormatType = SwtConstants.FORMAT_TYPE_DELIMITED;
						break;
					}
				}
				// get format type from request
				formatType = request.getParameter("formatType");
				// checks the format id equal to current format type
				if (!formatType.equals(currentFormatType)) {
					collMsgFlds = new ArrayList();
					request.getSession().setAttribute(
							"scenarioMessageFieldDetailsInSession", null);
					request.getSession().setAttribute(
							"scenarioMessageFieldDetailsInSessionSize", "0");
				}

			} else
				collMsgFlds = new ArrayList();
			// checks the message field size if zero make the message details in
			// session as zero
			if (fieldSize.equals("0")) {
				collMsgFlds = new ArrayList();
				request.getSession().setAttribute(
						"scenarioMessageFieldDetailsInSession", null);
				request.getSession().setAttribute(
						"scenarioMessageFieldDetailsInSessionSize", "0");
			}
			formatType = request.getParameter("formatType");
			collMsgFlds = sortScenarioMsgFldDetails(request, collMsgFlds);
			// set message field collection in request
			request.setAttribute("msgFieldDetails", collMsgFlds);
			// set button status
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE);
			// set format type in session
			request.getSession()
					.setAttribute("formatTypeInSession", formatType);
			checkViewOperation(request);
			// put entity list in request
			putEntityListInReq(request);
			log.debug("Exiting Fields method");
			return getView("successScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'fields' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'fields' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "fields", MessageFieldsAction.class), request, "");
			return getView("fail");
		} finally {
			formatType = null;
			currentFormatType = null;
			fieldSize = null;
		}
	}

	/* End :code modified by sandeepkumar for mantis 2092:Session Related issues */
	/**
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatDisplay()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering display method");

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			ScenarioMessageFields msgfld = (ScenarioMessageFields) getScenarioMessageFields();

			Collection collMsgFlds = (Collection) request.getSession()
					.getAttribute("scenarioMessageFieldDetailsInSession");
			if (collMsgFlds == null)
				collMsgFlds = new ArrayList();
			Collections.sort((List) collMsgFlds);
			String isSeqNoExisting = request.getParameter("isSeqNoExisting");
			if (isSeqNoExisting != null)
				isSeqNoExisting = isSeqNoExisting.trim();
			if (isSeqNoExisting != null && isSeqNoExisting.equals("yes"))
				request.setAttribute("isSeqNoExisting", "yes");

			fieldStatus(request);
			collMsgFlds = sortScenarioMsgFldDetails(request, collMsgFlds);
			request.setAttribute("msgFieldDetails", collMsgFlds);
			setButtonStatus(request, SwtConstants.STR_TRUE,
					SwtConstants.STR_FALSE, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE);
			checkViewOperation(request);
			return getView("successScenario");

		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'display' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}


	/**
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatChange()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'change' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			ScenarioMessageFields msgField = new ScenarioMessageFields();

			String hostId = putHostIdListInReq(request);
//			msgField.getId().setHostId(hostId);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();
			String fldType = request.getParameter("fldType");
			fldType = fldType.trim();
			if (fldType.equals(SwtConstants.FIELD_TEXT))
				fldType = SwtConstants.FIELD_TYPE_TEXT;
			else if (fldType.equals(SwtConstants.FIELD_KEYWORD))
				fldType = SwtConstants.FIELD_TYPE_KEYWORD;
			else
				fldType = SwtConstants.FIELD_TYPE_HEXADECIMAL;
			String value = SwtUtil.decode64(request.getParameter("value"));
			if (value != null && !(value.equals(""))) {
				value = value.trim();

			}
			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();
			String endPos = request.getParameter("endPos");

			if (endPos != null && !(endPos.equals("")))
				endPos = endPos.trim();

			msgField.setLineNoAsString(lineNoAsString);
			msgField.setSeqNoAsString(seqNoAsString);
			msgField.setValue(value);
			msgField.setFieldType(fldType);
			msgField.setStartPos(startPos);
			msgField.setEndPos(endPos);

			putKeyWordsIntoRequest(request);

			if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD))
				msgField.setValueKeyWord(msgField.getValue());

			setScenarioMessageFields(msgField);

			request.setAttribute("methodName", "update");
			request.setAttribute("selectedSeqNo", seqNoAsString);
			request.setAttribute("selectedLineNo", lineNoAsString);
			request.setAttribute("selectedStartPos", startPos);
			request.setAttribute("selectedEndPos", endPos);
			request.setAttribute("selectedValue", value);
			fieldStatus(request);
			request.setAttribute("fieldType", fldType);

			log.debug("exiting 'change' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'change' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'change' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 *
	 * @return
	 * @throws SwtException
	 */

	public String scenarioFormatView()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'view' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			ScenarioMessageFields msgField = new ScenarioMessageFields();

			String hostId = putHostIdListInReq(request);
//			msgField.getId().setHostId(hostId);
			String lineNoAsString = request.getParameter("selectedLineNo");
			if (lineNoAsString != null)
				lineNoAsString = lineNoAsString.trim();
			String seqNoAsString = request.getParameter("selectedSeqNo");
			if (seqNoAsString != null)
				seqNoAsString = seqNoAsString.trim();
			String fldType = request.getParameter("fldType");
			fldType = fldType.trim();
			if (fldType.equals(SwtConstants.FIELD_TEXT))
				fldType = SwtConstants.FIELD_TYPE_TEXT;
			else if (fldType.equals(SwtConstants.FIELD_KEYWORD))
				fldType = SwtConstants.FIELD_TYPE_KEYWORD;
			else
				fldType = SwtConstants.FIELD_TYPE_HEXADECIMAL;
			String value = SwtUtil.decode64(request.getParameter("value"));
			if (value != null && !(value.equals("")))
				value = value.trim();
			String startPos = request.getParameter("startPos");
			if (startPos != null && !(startPos.equals("")))
				startPos = startPos.trim();
			String endPos = request.getParameter("endPos");
			if (endPos != null && !(endPos.equals("")))
				endPos = endPos.trim();
			msgField.setLineNoAsString(lineNoAsString);
			msgField.setSeqNoAsString(seqNoAsString);
			msgField.setValue(value);
			msgField.setFieldType(fldType);
			msgField.setStartPos(startPos);
			msgField.setEndPos(endPos);
			putKeyWordsIntoRequest(request);
			if (fldType.equals(SwtConstants.FIELD_TYPE_KEYWORD))
				msgField.setValueKeyWord(msgField.getValue());
			setScenarioMessageFields(msgField);
			fieldStatus(request);
			request.setAttribute("fieldType", fldType);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("isViewField", "yes");
			log.debug("exiting 'view' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'view' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'view' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "view", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}
	/*
	 * Start :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * This method is used to update the changes in change message field screen
	 *
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatUpdate()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable declaration for dyna form
// To remove: 		DynaValidatorForm dyForm = null;
		// variable declaration for message field
		ScenarioMessageFields msgFld = null;
		// variable declaration for new message field details
		Collection newscenarioMessageFieldDetailsInSession = null;
		// variable to hold format type
		String formatType = null;
		// variable declaration for message field details
		Collection scenarioMessageFieldDetailsInSession = null;
		// variable to hold selected sequence number
		String selectedSeqNo = null;
		// variable to hold selected start position
		String selectedStartPos = null;
		// variable to hold selected end position
		String selectedEndPos = null;
		// variable to hold new line number
		String newLineNo = null;
		// variable to hold selected line number
		String selectedLineNo = null;
		// variable to hold existing message field
		ScenarioMessageFields msgFldExisting = null;
		// variable to hold existing line number
		String lineNoExisting = null;
		// variable to hold existing start position
		String startPosExisting = null;
		// variable to hold existing sequence number
		String seqNoExisting = null;
		// variable to hold existing end position number
		String endPosExisting = null;
		// variable to hold new sequence number
		String newSeqNo = null;
		//variable to hold message field iterator
		Iterator msgFieldItr = null;
		//variable declaration for is record added
		boolean isRecordAdded = false;
		try {
			log.debug("Entering 'update' method");
			// get form values to dyform
// To remove: 			dyForm = (DynaValidatorForm) form;
			// get message field values from form
			msgFld = (ScenarioMessageFields) getScenarioMessageFields();
			// checks sequence number ,line number ,start position,end position
			// not null and set in message field bean
			if(msgFld != null && !SwtUtil.isEmptyOrNull(msgFld.getValue())){
				msgFld.setValue(SwtUtil.decode64(msgFld.getValue()));
			}
			if (msgFld.getSeqNoAsString() != null)
				msgFld.setSeqNoAsString(msgFld.getSeqNoAsString().trim());
			if (msgFld.getLineNoAsString() != null)
				msgFld.setLineNoAsString(msgFld.getLineNoAsString().trim());
			if (msgFld.getStartPos() != null)
				msgFld.setStartPos(msgFld.getStartPos());
			if (msgFld.getEndPos() != null)
				msgFld.setEndPos(msgFld.getEndPos().trim());
			//checks the field type is equal to keyword
			if (msgFld.getFieldType().equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
				msgFld.setValue(msgFld.getValueKeyWord());
			}
			//instance for new message field details in session
			newscenarioMessageFieldDetailsInSession = new ArrayList();
			//put field status in request
			formatType = fieldStatus(request);
			//get message field details in session
			scenarioMessageFieldDetailsInSession = (Collection) request.getSession()
					.getAttribute("scenarioMessageFieldDetailsInSession");
			//get selected sequence number from request
			selectedSeqNo = request.getParameter("selectedSeqNo");
			//checks the selected sequence number not null
			if (selectedSeqNo != null)
				selectedSeqNo = selectedSeqNo.trim();
			//get selected start position from request
			selectedStartPos = request.getParameter("selectedStartPos");
			//get selected end position from request
			selectedEndPos = request.getParameter("selectedEndPos");
			//checks the selected start position not null
			if (selectedStartPos != null)
				selectedStartPos = selectedStartPos.trim();
			//checks the selected end position not null
			if (selectedEndPos != null)
				selectedEndPos = selectedEndPos.trim();
			//check the format type equal to tagged variable
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
				//get line number from bean
				newLineNo = msgFld.getLineNoAsString();
				newLineNo = newLineNo.trim();
				//get selected line number from request
				selectedLineNo = request.getParameter("selectedLineNo");
				selectedLineNo = selectedLineNo.trim();
				//iterating the message field details
				msgFieldItr = scenarioMessageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {
					msgFldExisting = (ScenarioMessageFields) (msgFieldItr.next());
					lineNoExisting = msgFldExisting.getLineNoAsString().trim();
					startPosExisting = msgFldExisting.getStartPos();
					seqNoExisting = msgFldExisting.getSeqNoAsString().trim();
					if (lineNoExisting.equals(selectedLineNo)
							&& startPosExisting.equals(selectedStartPos)
							&& seqNoExisting.equals(selectedSeqNo)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newscenarioMessageFieldDetailsInSession.add(msgFldExisting);

				}
			}
			else if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
				newLineNo = msgFld.getLineNoAsString();
				newLineNo = newLineNo.trim();
				selectedLineNo = request.getParameter("selectedLineNo");
				selectedLineNo = selectedLineNo.trim();
				msgFieldItr = scenarioMessageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (ScenarioMessageFields) (msgFieldItr.next());
					lineNoExisting = msgFldExisting.getLineNoAsString().trim();
					startPosExisting = msgFldExisting.getStartPos();
					endPosExisting = msgFldExisting.getEndPos();
					if (lineNoExisting.equals(selectedLineNo)
							&& startPosExisting.equals(selectedStartPos)
							&& endPosExisting.equals(selectedEndPos)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newscenarioMessageFieldDetailsInSession.add(msgFldExisting);

				}
			} else if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED)) {

				msgFieldItr = scenarioMessageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (ScenarioMessageFields) (msgFieldItr.next());
					startPosExisting = msgFldExisting.getStartPos();
					endPosExisting = msgFldExisting.getEndPos();

					if (startPosExisting.equals(selectedStartPos)
							&& endPosExisting.equals(selectedEndPos)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newscenarioMessageFieldDetailsInSession.add(msgFldExisting);
				}
			}

			else {
				newSeqNo = msgFld.getSeqNoAsString();
				newSeqNo = newSeqNo.trim();

				msgFieldItr = scenarioMessageFieldDetailsInSession.iterator();
				while (msgFieldItr.hasNext()) {

					msgFldExisting = (ScenarioMessageFields) (msgFieldItr.next());
					seqNoExisting = msgFldExisting.getSeqNoAsString().trim();
					if (seqNoExisting.equals(selectedSeqNo)) {
						msgFld.getId().setSerialNo(
								msgFldExisting.getId().getSerialNo());
					} else
						newscenarioMessageFieldDetailsInSession.add(msgFldExisting);
				}
			}
			//put message field details in session in request
			request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
					newscenarioMessageFieldDetailsInSession);
			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
				isRecordAdded = validateScenarioRecordMultiLineVariable(request,
						msgFld, false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
				isRecordAdded = validateScenarioRecordWithLineNo(request, msgFld, false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
				isRecordAdded = validateScenarioRecordWithoutLineNo(request, msgFld,
						false);

			if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
				isRecordAdded = validateScenarioRecordDelimited(request, msgFld, false);

			if (isRecordAdded)
				//set parent form refresh value in request
				request.setAttribute("parentFormRefresh", "yes");
			//set message field values in dyform
			setScenarioMessageFields(msgFld);
			//set field type in request
			request.setAttribute("fieldType", msgFld.getFieldType());
			//set field status in request
			fieldStatus(request);
			//put key word details in request
			putKeyWordsIntoRequest(request);
			//set method name in request
			request.setAttribute("methodName", "update");
			log.debug("exiting 'update' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'update' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "add");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("addScenario");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'update' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", MessageFieldsAction.class), request, "");
			return getView("fail");
		} finally {
			// nullifying objects
			selectedSeqNo = null;
			selectedStartPos = null;
			selectedEndPos = null;
			newLineNo = null;
			selectedLineNo = null;
			msgFldExisting = null;
			lineNoExisting = null;
			startPosExisting = null;
			seqNoExisting = null;
			endPosExisting = null;
			newSeqNo = null;
		}
	}
	/*
	 * End :code modified by sandeepkumar for mantis 2092:Session Related
	 * issues
	 */
	/**
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatDelete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		try {
			log.debug("entering 'delete' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			ScenarioMessageFields msgField = new ScenarioMessageFields();
			String selectedSeqNo = (String) request
					.getParameter("selectedSeqNo");
			String selectedLineNo = (String) request
					.getParameter("selectedLineNo");
			String selectedStartPos = request.getParameter("selectedStartPos");
			String selectedEndPos = request.getParameter("selectedEndPos");
			if (selectedStartPos != null)
				selectedStartPos = selectedStartPos.trim();
			if (selectedStartPos != null)
				selectedEndPos = selectedEndPos.trim();
			if (selectedSeqNo != null)
				selectedSeqNo = selectedSeqNo.trim();
			if (selectedLineNo != null)
				selectedLineNo = selectedLineNo.trim();
			String formatType = fieldStatus(request);
			Collection newscenarioMessageFieldDetailsInSession = new ArrayList();
			Collection scenarioMessageFieldDetailsInSession = (Collection) request
					.getSession().getAttribute("scenarioMessageFieldDetailsInSession");
			if (scenarioMessageFieldDetailsInSession != null) {
				Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
				while (itr.hasNext()) {
					ScenarioMessageFields messageFields = (ScenarioMessageFields) (itr.next());
					if (formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE)) {
						String existingStartPos = messageFields.getStartPos();

						String existingLineNo = messageFields
								.getLineNoAsString();
						if (existingStartPos.equals(selectedStartPos)
								&& existingLineNo.equals(selectedLineNo)) {

						} else
							newscenarioMessageFieldDetailsInSession.add(messageFields);
					} else if (formatType
							.equals(SwtConstants.FORMAT_TYPE_TAGGED)) {
						String existingStartPos = messageFields.getStartPos();
						String existingEndPos = messageFields.getEndPos();
						String existingLineNo = messageFields
								.getLineNoAsString();
						if (existingStartPos.equals(selectedStartPos)
								&& existingEndPos.equals(selectedEndPos)
								&& existingLineNo.equals(selectedLineNo)) {

						} else
							newscenarioMessageFieldDetailsInSession.add(messageFields);
					} else if (formatType
							.equals(SwtConstants.FORMAT_TYPE_FIXED)) {
						String existingEndPos = messageFields.getEndPos();
						String existingStartPos = messageFields.getStartPos();
						if (existingStartPos.equals(selectedStartPos)
								&& existingEndPos.equals(selectedEndPos)) {

						} else
							newscenarioMessageFieldDetailsInSession.add(messageFields);

					} else {
						String existingSeqNo = messageFields.getSeqNoAsString();
						if (existingSeqNo.equals(selectedSeqNo)) {

						} else
							newscenarioMessageFieldDetailsInSession.add(messageFields);
					}
				}
			}
			String collSize = (String) request.getSession().getAttribute(
					"scenarioMessageFieldDetailsInSessionSize");
			int i = new Integer(collSize).intValue() - 1;
			String decrementedSize = new Integer(i).toString();

			request.getSession().setAttribute(
					"scenarioMessageFieldDetailsInSessionSize", decrementedSize);

			request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
					newscenarioMessageFieldDetailsInSession);
			scenarioFormatDisplay();

			log.debug("exiting 'delete' method");
			return getView("successScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'delete' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "addScenario");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("successScenario");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MessageFieldsAction.'delete' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatAdd()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {
			log.debug("entering 'add' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			ScenarioMessageFields msgField = new ScenarioMessageFields();
			String hostId = putHostIdListInReq(request);
//			msgField.getId().setHostId(hostId);
			setScenarioMessageFields(msgField);
			request.setAttribute("fieldType", SwtConstants.FIELD_TYPE_TEXT);
			fieldStatus(request);
			putKeyWordsIntoRequest(request);

			request.setAttribute("methodName", "save");
			putEntityListInReq(request);
			log.debug("exiting 'add' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'add' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'add' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/**
	 * @return
	 * @throws SwtException
	 */
	public String scenarioFormatSave()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		try {
			log.debug("entering 'save' method");
// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;
			ScenarioMessageFields msgfld = (ScenarioMessageFields) getScenarioMessageFields();

			if(msgfld != null && !SwtUtil.isEmptyOrNull(msgfld.getValue())){
				msgfld.setValue(SwtUtil.decode64(msgfld.getValue()));
			}
			if (msgfld.getSeqNoAsString() != null)
				msgfld.setSeqNoAsString(msgfld.getSeqNoAsString().trim());
			if (msgfld.getLineNoAsString() != null)
				msgfld.setLineNoAsString(msgfld.getLineNoAsString().trim());
			if (msgfld.getStartPos() != null)
				msgfld.setStartPos(msgfld.getStartPos());
			if (msgfld.getEndPos() != null)
				msgfld.setEndPos(msgfld.getEndPos().trim());
			if (msgfld.getFieldType().equals(SwtConstants.FIELD_TYPE_KEYWORD)) {
				msgfld.setValue(msgfld.getValueKeyWord());
			}
			if (msgfld.getFieldType() != null)
				request.setAttribute("fieldType", msgfld.getFieldType());
			String hostId = putHostIdListInReq(request);
//			msgfld.getId().setHostId(hostId);
			String formatType = fieldStatus(request);
			boolean isRecordAdded = false;
			String insertFlag = request.getParameter("insertFlag");
			boolean insertOperation = false;
			if (insertFlag != null && insertFlag.equals("Y")) {
				insertOperation = true;
			}

			if (!insertOperation) {

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
					isRecordAdded = validateScenarioRecordMultiLineVariable(request,
							msgfld, false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
					isRecordAdded = validateScenarioRecordWithLineNo(request, msgfld,
							false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
					isRecordAdded = validateScenarioRecordWithoutLineNo(request,
							msgfld, false);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
					isRecordAdded = validateScenarioRecordDelimited(request, msgfld,
							false);
			} else {

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE))
					isRecordAdded = validateScenarioRecordMultiLineVariable(request,
							msgfld, insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_TAGGED))
					isRecordAdded = validateScenarioRecordWithLineNo(request, msgfld,
							insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_FIXED))
					isRecordAdded = validateScenarioRecordWithoutLineNo(request,
							msgfld, insertOperation);

				if (formatType.equals(SwtConstants.FORMAT_TYPE_DELIMITED))
					isRecordAdded = validateScenarioRecordDelimited(request, msgfld,
							insertOperation);
				isRecordAdded = true; // always add the record
			}

			putKeyWordsIntoRequest(request);
			String userId = SwtUtil.getCurrentUserId(request.getSession());
			request.setAttribute("methodName", "save");
			if (isRecordAdded) {
				request.setAttribute("parentFormRefresh", "yes");
				String collSize = (String) request.getSession().getAttribute(
						"scenarioMessageFieldDetailsInSessionSize");
				int i = new Integer(collSize).intValue() + 1;
				String incrementedSize = new Integer(i).toString();
				request.getSession().setAttribute(
						"scenarioMessageFieldDetailsInSessionSize", incrementedSize);
			}
			log.debug("exiting 'save' method");
			return getView("addScenario");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MessageFieldsAction.'save' method : "
							+ swtexp.getMessage());
			request.setAttribute("methodName", "addScenario");
			request.setAttribute("screenFieldsStatus", "");
			putEntityListInReq(request);
			if (swtexp.getErrorLogFlag().equals("Y")) {
				ErrorLogManager errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				ErrorLog errorLog = new ErrorLog();
				errorLog.setHostId(CacheManager.getInstance().getHostId());
				errorLog.setErrorDate(new Date());
				errorLog.setIpAddress(request.getRemoteAddr());
				errorLog.setErrorDesc(swtexp.getErrorDesc());
				errorLog.setErrorId(swtexp.getErrorId());
				errorLogManager.logError(errorLog);
			}
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("addScenario");
		} catch (Exception exp) {
			log.error("Exception Catch in MessageFieldsAction.'save' method : "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", MessageFieldsAction.class), request, "");
			return getView("fail");
		}
	}

	/* Start : Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */
	private boolean validateScenarioRecordMultiLineVariable(HttpServletRequest request,
															ScenarioMessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordMultiLineVariable method");
		String lineNo = msgfld.getLineNoAsString().trim();
		msgfld.setLineNoAsString(msgfld.getLineNoAsString());
		msgfld.setStartPos(msgfld.getStartPos().trim());

		if (msgfld.getLineNoAsString() != null) {
			msgfld.setLineNo(new Integer(lineNo));
		}

		if (msgfld.getStartPos() != null
				&& !msgfld.getStartPos().equalsIgnoreCase("")) {
			msgfld.setStartPosInt(Integer.valueOf(msgfld.getStartPos()));
		}

		String seqNo = msgfld.getSeqNoAsString().trim();
		// log.debug("The seq no is==" + seqNo);

		if (msgfld.getSeqNoAsString() != null
				&& !msgfld.getSeqNoAsString().equals("")) {
			msgfld.setSeqNo(new Integer(seqNo));
		}

		boolean isLineNoExisting = false; // This flag checks if the line no
		// to
		// be added already exists
		boolean isCheck = false;
		boolean isRecordAdded = false;
		int seqNoTemp = 0;

		// boolean insertOperation = true;

		Collection scenarioMessageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("scenarioMessageFieldDetailsInSession");
		if (scenarioMessageFieldDetailsInSession != null) {
			int siz = scenarioMessageFieldDetailsInSession.size();

			Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
			if (msgfld.getSeqNoAsString() == null
					|| msgfld.getSeqNoAsString().equals("")) {
				while (itr.hasNext()) {
					ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());

					if (msgField.getLineNoAsString().equals(lineNo)) {
						if (msgField.getSeqNo() != null
								&& msgField.getSeqNo().intValue() > seqNoTemp) {
							seqNoTemp = msgField.getSeqNo().intValue();
						}
					}
					if (insertOperation) {
						if (msgField.getLineNo().intValue() >= msgfld
								.getLineNo().intValue()) {
							int updatedLineNo = msgField.getLineNo().intValue() + 1;
							msgField.setLineNo(new Integer(updatedLineNo));
							msgField.setLineNoAsString(msgField.getLineNo()
									.toString());
						}
					}
				}
				if (insertOperation) {
					seqNoTemp = 0;
				}
				msgfld.setSeqNo(Integer.valueOf(seqNoTemp + 1));
				msgfld.setSeqNoAsString(msgfld.getSeqNo().toString());
				scenarioMessageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {
				boolean isRecordExisting = false;
				while (itr.hasNext()) {
					ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());

					if (msgField.getLineNoAsString().equals(lineNo)
							&& msgField.getSeqNoAsString().equals(seqNo)) {
						request.setAttribute("recordExists", "Y");
						isRecordExisting = true;
						break;
					}
				}
				if (!isRecordExisting) {
					msgfld.setSeqNo(Integer.valueOf(msgfld.getSeqNoAsString()));
					// log.debug("the record added is" + msgfld);
					scenarioMessageFieldDetailsInSession.add(msgfld);
					isRecordAdded = true;
				}
			}
		} else {
			// add into the collection
			scenarioMessageFieldDetailsInSession = new ArrayList();
			msgfld.setSeqNo(Integer.valueOf(1));
			msgfld.setSeqNoAsString(msgfld.getSeqNo().toString());
			scenarioMessageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;

		}
		request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
				scenarioMessageFieldDetailsInSession);
		log.debug("Exiting the validateRecordMultiLineVariable method");
		return isRecordAdded;
	}

	/* End : Refer to SRS_XMLFormat_output_0[1].2_Comments_Perot_DJB.doc */

	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateScenarioRecordWithoutLineNo(HttpServletRequest request,
														ScenarioMessageFields msgfld, boolean insertOperation) {
		boolean isRangeOverlapping = false; // This flag checks if the range
		// overlaps with any of the previous
		// range boolean isRecordAdded =
		// false;
		boolean isCheck = false;
		boolean isRecordAdded = false;
		// boolean = true;

		Collection scenarioMessageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("scenarioMessageFieldDetailsInSession");
		boolean flag = false;
		int addFactor = 0;

		msgfld.setStartPosInt(Integer.valueOf(msgfld.getStartPos()));
		if (scenarioMessageFieldDetailsInSession != null && insertOperation) {

			Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());
				if (msgfld.getStartPosInt().intValue() == msgField
						.getStartPosInt().intValue()) {
					// flag = true;
					addFactor = Integer.valueOf(msgfld.getEndPos()).intValue()
							- msgfld.getStartPosInt().intValue() + 1;
					break;
				}
			}
			// messageFieldDetailsInSession.iterator().

			Iterator itr1 = scenarioMessageFieldDetailsInSession.iterator();

			while (itr1.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr1.next());
				msgField
						.setStartPosInt(Integer.valueOf(msgField.getStartPos()));
				if (msgField.getStartPosInt().intValue() >= msgfld
						.getStartPosInt().intValue()) {
					int updatedStartPos = msgField.getStartPosInt().intValue()
							+ addFactor;
					int updatedEndPos = Integer.valueOf(msgField.getEndPos())
							.intValue()
							+ addFactor;
					msgField.setStartPosInt(new Integer(updatedStartPos));
					msgField.setStartPos(msgField.getStartPosInt().toString());
					msgField.setEndPos(Integer.valueOf(updatedEndPos)
							.toString());
				}
			}
		}

		if (insertOperation)
			scenarioMessageFieldDetailsInSession.add(msgfld);

		if (scenarioMessageFieldDetailsInSession != null && !insertOperation) {
			int siz = scenarioMessageFieldDetailsInSession.size();

			/**
			 * startPosMax[] -- This array stores the end positions of all the
			 * records. endPosMin[] -- This array stores the starting position
			 * of all the records. The end position of any new record should be
			 * either greater than all elements of array startPosMax[] or lesser
			 * than all elements of the array endPosMin[] If that does not
			 * happen ==> Range is overlapping
			 */
			int startPosMax[] = new int[siz];
			int endPosMin[] = new int[siz];
			int i = 0;

			Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());

				startPosMax[i] = Integer.valueOf(msgField.getEndPos().trim())
						.intValue();
				endPosMin[i] = Integer.valueOf(msgField.getStartPos().trim())
						.intValue();
				i++;

			}

			int startPos = Integer.valueOf(msgfld.getStartPos().trim())
					.intValue();
			int endPos = Integer.valueOf(msgfld.getEndPos().trim()).intValue();

			for (int j = 0; j < siz; j++) {
				if (startPos <= startPosMax[j]) {
					isRangeOverlapping = true;
					isCheck = true;
					break;
				}

			}
			if (isCheck) {
				for (int j = 0; j < siz; j++) {
					if (endPos >= endPosMin[j]) {
						isRangeOverlapping = true;
						isCheck = true;
						break;
					} else {
						isRangeOverlapping = false;
						isCheck = false;
					}
				}
			}

			if (isCheck) {
				int temp = startPosMax[0];
				for (int k = 0; k < siz; k++) {
					if (temp < startPosMax[k])
						temp = startPosMax[k];
				}
				int midIntervalArray[] = new int[temp];
				for (int k = 0; k < temp; k++) {
					midIntervalArray[k] = k + 1;
				}
				for (int k = 0; k < siz; k++) {
					for (int p = endPosMin[k] - 1; p <= startPosMax[k] - 1; p++)
						midIntervalArray[p] = -1;
				}
				int startPosIndex = 0;
				int endPosIndex = 0;
				boolean isMidIntervalRange = true;

				for (int k = 0; k < temp; k++) {
					if (midIntervalArray[k] == startPos) {
						startPosIndex = k;
						isMidIntervalRange = false;
						break;
					}
					isMidIntervalRange = true;
				}

				if (!isMidIntervalRange) {
					for (int k = 0; k < temp; k++) {
						if (midIntervalArray[k] == endPos) {
							endPosIndex = k;
							isMidIntervalRange = false;
							break;
						}
						isMidIntervalRange = true;
					}
				}
				if (isMidIntervalRange)
					isRangeOverlapping = true;
				else {
					for (int k = startPosIndex; k <= endPosIndex; k++)
						if (midIntervalArray[k] == -1) {
							isRangeOverlapping = true;
							break;
						}
					isRangeOverlapping = false;
				}
			}
			if (!(isRangeOverlapping)) {
				// add the record
				scenarioMessageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {
				request.setAttribute("isRangeOverlapping", "yes");
			}

		} else {
			if (!insertOperation) {
				// add into the collection
				scenarioMessageFieldDetailsInSession = new ArrayList();

				scenarioMessageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			}
		}
		request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
				scenarioMessageFieldDetailsInSession);
		log.debug("Exiting the validateRecordWithoutLineNo method");
		return isRecordAdded;

	}

	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateScenarioRecordDelimited(HttpServletRequest request,
													ScenarioMessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordDelimited method");
		String seqNo = msgfld.getSeqNoAsString().trim();
		msgfld.setSeqNoAsString(msgfld.getSeqNoAsString().trim());
		if (msgfld.getSeqNoAsString() != null) {
			msgfld.setSeqNo(new Integer(seqNo));
		}
		boolean isSeqNoExisting = false; // This flag checks if the seq no to
		// be
		// added already exists

		boolean isCheck = false;
		boolean isRecordAdded = false;

		// boolean insertOperation = true;

		Collection scenarioMessageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("scenarioMessageFieldDetailsInSession");
		if (scenarioMessageFieldDetailsInSession != null) {
			int siz = scenarioMessageFieldDetailsInSession.size();

			Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());
				// log.debug("The comparing condition shows" +
				// msgField.getSeqNoAsString() + "==" + msgField.getSeqNo())
				if (!isSeqNoExisting) {
					if (msgField.getSeqNoAsString().equals(seqNo)) {
						isSeqNoExisting = true;
						// break;
					}
				}
				/* SRS Sweep */
				if (insertOperation) {
					// log.debug("The msgField.getSeqNo().intValue() is + " +
					// msgField.getSeqNo().intValue());
					if (msgField.getSeqNo().intValue() >= msgfld.getSeqNo()
							.intValue()) {
						int updatedSeqNo = msgField.getSeqNo().intValue() + 1;
						msgField.setSeqNo(new Integer(updatedSeqNo));
						msgField.setSeqNoAsString(msgField.getSeqNo()
								.toString());
					}
				}
				/* SRS Sweep */
			}

			if (!(isSeqNoExisting)) {
				scenarioMessageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			} else {

				if (!insertOperation) {
					request.setAttribute("isSeqNoExisting", "yes");
				} else {
					scenarioMessageFieldDetailsInSession.add(msgfld);
				}
			}

		} else {
			// add into the collection
			scenarioMessageFieldDetailsInSession = new ArrayList();
			scenarioMessageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;
		}

		request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
				scenarioMessageFieldDetailsInSession);
		return isRecordAdded;

	}


	/**
	 *
	 * @param request
	 * @param msgfld
	 * @return
	 */
	private boolean validateScenarioRecordWithLineNo(HttpServletRequest request,
													 ScenarioMessageFields msgfld, boolean insertOperation) {
		log.debug("Entering the validateRecordWithLineNo method");
		String lineNo = msgfld.getLineNoAsString().trim();
		msgfld.setLineNoAsString(msgfld.getLineNoAsString());
		msgfld.setStartPos(msgfld.getStartPos().trim());
		msgfld.setEndPos(msgfld.getEndPos().trim());
		if (msgfld.getLineNoAsString() != null) {
			msgfld.setLineNo(new Integer(lineNo));
		}
		boolean isLineNoExisting = false; // This flag checks if the seq no to
		// be added already exists
		boolean isRangeOverlapping = false; // This flag checks if the range
		// overlaps with any of the previous
		// range boolean isRecordAdded =
		// false;
		boolean isCheck = false;
		boolean isRecordAdded = false;

		Collection scenarioMessageFieldDetailsInSession = (Collection) request
				.getSession().getAttribute("scenarioMessageFieldDetailsInSession");
		if (scenarioMessageFieldDetailsInSession != null) {
			int siz = scenarioMessageFieldDetailsInSession.size();

			Iterator itr = scenarioMessageFieldDetailsInSession.iterator();
			while (itr.hasNext()) {
				ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());
				if (!isLineNoExisting) {
					if (msgField.getLineNoAsString().equals(lineNo)) {
						isLineNoExisting = true;
						// break;
					}
				}

				if (insertOperation) {
					if (msgField.getLineNo().intValue() >= msgfld.getLineNo()
							.intValue()) {
						int updatedLineNo = msgField.getLineNo().intValue() + 1;
						msgField.setLineNo(new Integer(updatedLineNo));
						msgField.setLineNoAsString(msgField.getLineNo()
								.toString());
					}
				}
			}

			if (isLineNoExisting && !insertOperation) {

				/**
				 * startPosMax[] -- This array stores the end positions of all
				 * the records. endPosMin[] -- This array stores the starting
				 * position of all the records. The end position of any new
				 * record should be either greater than all elements of array
				 * startPosMax[] or lesser than all elements of the array
				 * endPosMin[] If that does not happen ==> Range is overlapping
				 */
				int startPosMax[] = new int[siz];
				int endPosMin[] = new int[siz];
				int i = 0;

				itr = scenarioMessageFieldDetailsInSession.iterator();
				while (itr.hasNext()) {
					ScenarioMessageFields msgField = (ScenarioMessageFields) (itr.next());
					if (msgField.getLineNoAsString().equals(lineNo)) {
						startPosMax[i] = Integer.valueOf(
								msgField.getEndPos().trim()).intValue();
						endPosMin[i] = Integer.valueOf(
								msgField.getStartPos().trim()).intValue();
						i++;
					}
				}
				int sizWrtLineNo = i;
				// og.debug("The value of the sizWrtLineNo==>" + sizWrtLineNo);

				int startPos = Integer.valueOf(msgfld.getStartPos().trim())
						.intValue();
				int endPos = Integer.valueOf(msgfld.getEndPos().trim())
						.intValue();

				for (int j = 0; j < sizWrtLineNo; j++) {
					if (startPos <= startPosMax[j]) {
						isRangeOverlapping = true;
						isCheck = true;
						break;
					}
				}
				if (isCheck) {
					for (int j = 0; j < sizWrtLineNo; j++) {
						if (endPos >= endPosMin[j]) {
							isRangeOverlapping = true;
							isCheck = true;
							break;
						} else {
							isRangeOverlapping = false;
							isCheck = false;

						}
					}
				}
				if (isCheck) {
					int temp = startPosMax[0];
					for (int k = 0; k < sizWrtLineNo; k++) {
						if (temp < startPosMax[k])
							temp = startPosMax[k];
					}

					int midIntervalArray[] = new int[temp];
					for (int k = 0; k < temp; k++) {
						midIntervalArray[k] = k + 1;
					}
					// log.debug("after putting the elements in the array upto
					// max valueof the end postion
					// is==>"+midIntervalArray.toString());

					for (int k = 0; k < sizWrtLineNo; k++) {
						for (int p = endPosMin[k] - 1; p <= startPosMax[k] - 1; p++)
							midIntervalArray[p] = -1;
					}

					int startPosIndex = 0;
					int endPosIndex = 0;
					boolean isMidIntervalRange = true;

					for (int k = 0; k < temp; k++) {
						if (midIntervalArray[k] == startPos) {
							startPosIndex = k;
							isMidIntervalRange = false;
							break;
						}
						isMidIntervalRange = true;
					}

					if (!isMidIntervalRange) {
						for (int k = 0; k < temp; k++) {
							if (midIntervalArray[k] == endPos) {
								endPosIndex = k;
								isMidIntervalRange = false;
								break;
							}
							isMidIntervalRange = true;
						}
					}

					if (isMidIntervalRange)
						isRangeOverlapping = true;
					else {
						// log.debug("Inside else====>");
						for (int k = startPosIndex; k <= endPosIndex; k++)
							if (midIntervalArray[k] == -1) {
								isRangeOverlapping = true;
								break;
							}
						isRangeOverlapping = false;
					}
				}
				if (!(isRangeOverlapping)) {
					// add the record
					scenarioMessageFieldDetailsInSession.add(msgfld);
					isRecordAdded = true;
				} else {
					request.setAttribute("isRangeOverlapping", "yes");
				}

			} else {
				scenarioMessageFieldDetailsInSession.add(msgfld);
				isRecordAdded = true;
			}
		} else {
			// add into the collection
			scenarioMessageFieldDetailsInSession = new ArrayList();

			scenarioMessageFieldDetailsInSession.add(msgfld);
			isRecordAdded = true;

		}
		request.getSession().setAttribute("scenarioMessageFieldDetailsInSession",
				scenarioMessageFieldDetailsInSession);
		log.debug("Exiting the validateRecordWithLineNo method");
		return isRecordAdded;

	}
}