/*
 * Created on Aug 28, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR> pandey
 *
 * This class is used for scheduling the balance_update stored procedure
 */
public class StartingBalanceProcess extends SwtBasicJob{
	private final Log log = LogFactory.getLog(StartingBalanceProcess.class);
	
	
	/* (non-Javadoc)
	 * @see org.swallow.batchScheduler.SwtBasicJob#executeJob()
	 */
	public String executeJob(Integer schedulerId)
	{
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		try
		{
			 conn = getConnection(); 
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
	            log.debug(" $$$$$$$$$  SP_BALANCE_UPDATE Called at "+SwtUtil.getTimeWithMilliseconds());
			 pstmt=conn.prepareCall("{call SP_BALANCE_UPDATE(?)}");
			pstmt.registerOutParameter(1, Types.VARCHAR);
			pstmt.executeUpdate();
			//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
            log.debug(" $$$$$$$$$  SP_BALANCE_UPDATE Ended at "+SwtUtil.getTimeWithMilliseconds());
			retValue=pstmt.getString(1);
			int result=Integer.parseInt(retValue);
			if(result==0)
				retValue="S";
			else 
				retValue="F";
			log.debug("inside BALANCE_UPDATE");
			log.debug("result of execution of ALANCE_UPDATE(?) "+retValue);
			

		}
		catch(Exception  e)
		{
			log.debug("error in BALANCE_UPDATE is===>"+e.getMessage());
		}
		finally{
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
			log.debug("connection closed successfully in BALANCE_UPDATE===>");
			
		}
		return retValue;


	}

}
