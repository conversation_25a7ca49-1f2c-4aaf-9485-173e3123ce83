package org.swallow.batchScheduler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JavaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.concurrent.*;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import jakarta.annotation.PreDestroy;


@Component
public class ExecuteJavaMethodJob {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Log log = LogFactory.getLog(ExecuteJavaMethodJob.class);
    private final ExecutorService executorService;
    private final MethodCallProcessor methodCallProcessor;


    public ExecuteJavaMethodJob(@Value("${thread.pool.size:10}") int threadPoolSize) {
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
        this.methodCallProcessor = new MethodCallProcessor(30, 60);
    }

    //@Scheduled(fixedRate = 10000)
    public void execute() {
        Connection conn = null;
        try {
            conn = ConnectionManager.getInstance().databaseCon();

            // Find pending method calls
            String sql = "SELECT CALL_SEQ, CLASS_NAME, METHOD_NAME, PARAMETERS FROM P_JAVA_METHOD_CALLS WHERE STATUS = 'PENDING'";
            List<Callable<Void>> tasks = new ArrayList<>();

            try (PreparedStatement pstmt = conn.prepareStatement(sql);
                 ResultSet rs = pstmt.executeQuery()) {

                while (rs.next()) {
                    long callSeq = rs.getLong("CALL_SEQ");
                    String className = rs.getString("CLASS_NAME");
                    String methodName = rs.getString("METHOD_NAME");
                    String parameters = rs.getString("PARAMETERS");

                    tasks.add(() -> {
                        Connection taskConn = null;
                        try {
                            System.err.println("start task " + parameters);
                            taskConn = ConnectionManager.getInstance().databaseCon();

                            // First, mark the task as in progress
                            CompletableFuture<Void> updateFuture = methodCallProcessor
                                    .updateMethodCallStatus(taskConn, callSeq, "IN_PROGRESS", null);

                            // Wait for the status update to complete
                            updateFuture.get(30, TimeUnit.SECONDS);

                            // Execute the actual method
                            Object result = executeMethod(className, methodName, parameters);

                            // Update the final status
                            CompletableFuture<Void> completionFuture = methodCallProcessor
                                    .updateMethodCallStatus(taskConn, callSeq, "COMPLETED", result);

                            // Wait for the completion update
                            completionFuture.get(30, TimeUnit.SECONDS);

                            System.err.println("end task " + parameters);
                        } catch (Exception e) {
                            log.error("Error executing method: " + e.getMessage(), e);
                            try {
                                CompletableFuture<Void> failureFuture = methodCallProcessor
                                        .updateMethodCallStatus(taskConn, callSeq, "FAILED", e.getMessage());
                                failureFuture.get(30, TimeUnit.SECONDS);
                            } catch (Exception updateError) {
                                log.error("Failed to update error status: " + updateError.getMessage(), updateError);
                            }
                        } finally {
                            JDBCCloser.close(taskConn);
                        }
                        return null;
                    });
                }
            }

            // Execute tasks concurrently
            List<Future<Void>> futures = executorService.invokeAll(tasks);

            // Wait for all tasks to complete
            for (Future<Void> future : futures) {
                try {
                    future.get(300, TimeUnit.SECONDS); // 2 minute timeout for entire task
                } catch (ExecutionException | TimeoutException e) {
                    log.error("Task execution failed: " + e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            SwtUtil.logErrorInDatabase(
                    SwtErrorHandler.getInstance().handleException(e, "executeJob", ExecuteJavaMethodJob.class)
            );
            log.debug("ExecuteJavaMethodJob: Error in executeJob - " + e.getMessage());
        } finally {
            JDBCCloser.close(conn);
        }
    }

    private Object executeMethod(String className, String methodName, String parameters) throws Exception {
        Class<?> clazz = Class.forName(className);

        JavaType paramMapType = objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class);
        Map<String, Object> paramMap = objectMapper.readValue(parameters, paramMapType);

        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName) && method.getParameterCount() == paramMap.size()) {
                Object[] args = new Object[method.getParameterCount()];
                Class<?>[] paramTypes = method.getParameterTypes();

                int i = 0;
                for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                    args[i] = convertToType(entry.getValue(), paramTypes[i]);
                    i++;
                }

                if (Modifier.isStatic(method.getModifiers())) {
                    return method.invoke(null, args);
                } else {
                    Object instance = clazz.getDeclaredConstructor().newInstance();
                    return method.invoke(instance, args);
                }
            }
        }

        throw new NoSuchMethodException("No matching method found: " + methodName);
    }

    private Object convertToType(Object value, Type targetType) throws Exception {
        TypeFactory typeFactory = objectMapper.getTypeFactory();
        JavaType javaType = typeFactory.constructType(targetType);

        if (value instanceof String) {
            String stringValue = (String) value;
            // Try to parse as JSON first
            try {
                return objectMapper.readValue(stringValue, javaType);
            } catch (JsonProcessingException e) {
                // If it's not valid JSON, treat it as a simple string
                if (targetType == String.class) {
                    return stringValue;
                } else if (targetType == Long.class || targetType == long.class) {
                    return Long.parseLong(stringValue);
                } else if (targetType == Integer.class || targetType == int.class) {
                    return Integer.parseInt(stringValue);
                } else if (targetType == Double.class || targetType == double.class) {
                    return Double.parseDouble(stringValue);
                } else if (targetType == Boolean.class || targetType == boolean.class) {
                    return Boolean.parseBoolean(stringValue);
                } else if (targetType == java.util.Date.class) {
                    if (value instanceof String) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                        return sdf.parse((String) value);
                    }
                }else if (targetType instanceof Class && ((Class<?>) targetType).isEnum()) {
                    if (value instanceof String) {
                        return Enum.valueOf((Class<Enum>) targetType, (String) value);
                    }
                }

                // For other types, try to create an instance and set properties
                try {
                    Object instance = Class.forName(targetType.getTypeName()).getDeclaredConstructor().newInstance();
                    Map<String, Object> properties = objectMapper.readValue(stringValue, Map.class);
                    for (Map.Entry<String, Object> entry : properties.entrySet()) {
                        String setterName = "set" + entry.getKey().substring(0, 1).toUpperCase() + entry.getKey().substring(1);
                        try {
                            Method setter = instance.getClass().getMethod(setterName, entry.getValue().getClass());
                            setter.invoke(instance, entry.getValue());
                        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException ignored) {
                            // Ignore if setter not found or cannot be invoked
                        }
                    }
                    return instance;
                } catch (Exception ex) {
                    throw new IllegalArgumentException("Cannot convert string to " + targetType, ex);
                }
            }
        } else if (value instanceof Map) {
            return objectMapper.convertValue(value, javaType);
        }

        return objectMapper.convertValue(value, javaType);
    }

    private synchronized void updateMethodCallStatus(Connection conn, long callSeq, String status, Object result) throws SQLException {
        conn.setAutoCommit(false);
        System.err.println("enter updateMethodCallStatus");
        try {
            String updateSql = "UPDATE P_JAVA_METHOD_CALLS SET STATUS = ?, UPDATED_AT = SYSTIMESTAMP WHERE CALL_SEQ = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(updateSql)) {
                pstmt.setString(1, status);
                pstmt.setLong(2, callSeq);
                pstmt.executeUpdate();
            }

            String insertSql = "INSERT INTO P_JAVA_METHOD_RESULTS (RESULT_SEQ, CALL_SEQ, RESULT_DATA, ERROR_MESSAGE) VALUES (P_JAVA_METHOD_RESULT_SEQ.NEXTVAL, ?, ?, ?)";
            try (PreparedStatement pstmt = conn.prepareStatement(insertSql)) {
                pstmt.setLong(1, callSeq);
                if (status.equals("COMPLETED")) {
                    pstmt.setString(2, serializeResult(result));
                    pstmt.setNull(3, Types.VARCHAR);
                } else {
                    pstmt.setNull(2, Types.CLOB);
                    pstmt.setString(3, (String) result);
                }
                pstmt.executeUpdate();
            }

            conn.commit();
            System.err.println("exit !! updateMethodCallStatus");
        } catch (Exception e) {
            conn.rollback();
            throw new SQLException("Failed to update method call status", e);
        } finally {
            conn.setAutoCommit(true);
        }
    }

    private String serializeResult(Object result) throws Exception {
        if (result == null) {
            return null;
        }
        return objectMapper.writeValueAsString(result);
    }

    @PreDestroy
    public void cleanup() {
        methodCallProcessor.shutdown();
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
        }
    }

    public class MethodCallProcessor {
        private final BlockingQueue<UpdateTask> taskQueue;
        private final Thread processingThread;
        private volatile boolean running = true;
        private final long taskTimeoutSeconds;
        private final long queueTimeoutSeconds;

        public MethodCallProcessor(long taskTimeoutSeconds, long queueTimeoutSeconds) {
            this.taskTimeoutSeconds = taskTimeoutSeconds;
            this.queueTimeoutSeconds = queueTimeoutSeconds;
            this.taskQueue = new LinkedBlockingQueue<>();
            this.processingThread = new Thread(this::processQueue);
            this.processingThread.setName("MethodCallProcessor-Thread");
            this.processingThread.start();
        }

        private static class UpdateTask {
            final Connection conn;
            final long callSeq;
            final String status;
            final Object result;
            final CompletableFuture<Void> completion;
            final long submitTime;

            UpdateTask(Connection conn, long callSeq, String status, Object result) {
                this.conn = conn;
                this.callSeq = callSeq;
                this.status = status;
                this.result = result;
                this.completion = new CompletableFuture<>();
                this.submitTime = System.currentTimeMillis();
            }

            boolean isQueueTimeoutExceeded(long timeoutSeconds) {
                return System.currentTimeMillis() - submitTime > timeoutSeconds * 1000;
            }
        }

        public CompletableFuture<Void> updateMethodCallStatus(Connection conn, long callSeq, String status, Object result) {
            UpdateTask task = new UpdateTask(conn, callSeq, status, result);
            boolean offered = taskQueue.offer(task);

            if (!offered) {
                task.completion.completeExceptionally(
                        new TimeoutException("Failed to queue task - queue is full")
                );
            }

            // Schedule a timeout for the queued task
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                if (!task.completion.isDone()) {
                    task.completion.completeExceptionally(
                            new TimeoutException("Task execution timed out after " + queueTimeoutSeconds + " seconds")
                    );
                    taskQueue.remove(task);
                }
                scheduler.shutdown();
            }, queueTimeoutSeconds, TimeUnit.SECONDS);

            return task.completion;
        }

        private void processQueue() {
            while (running || !taskQueue.isEmpty()) {
                try {
                    UpdateTask task = taskQueue.poll(1, TimeUnit.SECONDS);
                    if (task != null) {
                        // Check if task has exceeded queue timeout
                        if (task.isQueueTimeoutExceeded(queueTimeoutSeconds)) {
                            task.completion.completeExceptionally(
                                    new TimeoutException("Task expired in queue after " + queueTimeoutSeconds + " seconds")
                            );
                            continue;
                        }

                        // Execute task with timeout
                        ExecutorService executor = Executors.newSingleThreadExecutor();
                        try {
                            Future<?> future = executor.submit(() -> {
                                performUpdate(task.conn, task.callSeq, task.status, task.result);
                                return null;
                            });

                            try {
                                future.get(taskTimeoutSeconds, TimeUnit.SECONDS);
                                task.completion.complete(null);
                            } catch (TimeoutException e) {
                                future.cancel(true);
                                task.completion.completeExceptionally(
                                        new TimeoutException("Database operation timed out after " + taskTimeoutSeconds + " seconds")
                                );
                            } catch (Exception e) {
                                task.completion.completeExceptionally(e);
                            }
                        } finally {
                            executor.shutdownNow();
                        }
                    }
                } catch (InterruptedException e) {
                    if (!running) {
                        break;
                    }
                }
            }
        }

        private void performUpdate(Connection conn, long callSeq, String status, Object result) {
            System.err.println("enter updateMethodCallStatus");
            boolean originalAutoCommit = false;
            try {
                originalAutoCommit = conn.getAutoCommit();
                conn.setAutoCommit(false);

                String updateSql = "UPDATE P_JAVA_METHOD_CALLS SET STATUS = ?, UPDATED_AT = SYSTIMESTAMP WHERE CALL_SEQ = ?";
                try (PreparedStatement pstmt = conn.prepareStatement(updateSql)) {
                    pstmt.setString(1, status);
                    pstmt.setLong(2, callSeq);
                    pstmt.executeUpdate();
                }

                String insertSql = "INSERT INTO P_JAVA_METHOD_RESULTS (RESULT_SEQ, CALL_SEQ, RESULT_DATA, ERROR_MESSAGE) VALUES (P_JAVA_METHOD_RESULT_SEQ.NEXTVAL, ?, ?, ?)";
                try (PreparedStatement pstmt = conn.prepareStatement(insertSql)) {
                    pstmt.setLong(1, callSeq);
                    if (status.equals("COMPLETED")) {
                        pstmt.setString(2, serializeResult(result));
                        pstmt.setNull(3, Types.VARCHAR);
                    } else {
                        pstmt.setNull(2, Types.CLOB);
                        pstmt.setString(3, (String) result);
                    }
                    pstmt.executeUpdate();
                }

                conn.commit();
                System.err.println("exit !! updateMethodCallStatus");
            } catch (Exception e) {
                try {
                    conn.rollback();
                } catch (SQLException rollbackEx) {
                    System.err.println("Failed to rollback transaction: " + rollbackEx.getMessage());
                }
                throw new RuntimeException("Failed to update method call status", e);
            } finally {
                try {
                    conn.setAutoCommit(originalAutoCommit);
                } catch (SQLException e) {
                    System.err.println("Failed to restore auto-commit setting: " + e.getMessage());
                }
            }
        }

        private String serializeResult(Object result) {
            return result.toString();
        }

        public void shutdown() {
            running = false;
            processingThread.interrupt();
            try {
                processingThread.join(5000);
            } catch (InterruptedException e) {
                System.err.println("Shutdown interrupted");
            }
        }
    }

}
