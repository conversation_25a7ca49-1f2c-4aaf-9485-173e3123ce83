package org.swallow.batchScheduler;

import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SimpleTrigger;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.swallow.util.PropertiesFileLoader;

/**
 * 
 * <AUTHOR> ,SwallowTech Tunisia
 * This class is used to run the  'SendingEmailsJob' jobs for sending 'background' mails
 */
public class SendEmailJobScheduler {
	private static final Log log = LogFactory.getLog(SendingEmailsJob.class);
	private Scheduler scheduler = null;
	private JobDetailImpl emailJob = null;
	private SimpleTriggerImpl trigger = null;
	private final long REPEAT_INTERVAL = Long.parseLong(PropertiesFileLoader.getInstance()
			.getPropertiesValue("sendMailRepeatInterval"));
	private static SendEmailJobScheduler instance = null;
	
	public synchronized static SendEmailJobScheduler getInstance() {
		if (instance == null) {
			instance = new SendEmailJobScheduler();
		}
		return instance;
	}
	
	
	private SendEmailJobScheduler(){
		startUpJob();
	}
	
	public void startUpJob(){
		try {
			log.info("SendEmailJobScheduler  - [startUpJob] - Entering");
			
			emailJob = new JobDetailImpl();
			emailJob.setName("SendEmail Job");
			emailJob.setJobClass(SendingEmailsJob.class);
			
			//configure the scheduler time
			trigger = new SimpleTriggerImpl();
			trigger.setStartTime(new Date(System.currentTimeMillis() + 1000));
			trigger.setRepeatCount(SimpleTrigger.REPEAT_INDEFINITELY);
			trigger.setRepeatInterval(REPEAT_INTERVAL); 
			trigger.setName("Email Trigger");
			
			//schedule it
			scheduler = new StdSchedulerFactory().getScheduler();
			scheduler.start();
			scheduler.scheduleJob(emailJob, trigger);
			
			log.info("SendEmailJobScheduler  - [startUpJob] - Exit");
		} catch (Exception e) {
			
			log.error("SendEmailJobScheduler  - [startUpJob] - Exception: "
					+ e.getMessage());
		}
	}

}
