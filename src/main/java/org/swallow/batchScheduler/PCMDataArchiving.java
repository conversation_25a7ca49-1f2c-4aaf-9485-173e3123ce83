

package org.swallow.batchScheduler;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import org.hibernate.Session;


public class PCMDataArchiving extends SwtBasicJob {
	
	private final Log log = LogFactory.getLog(PCMDataArchiving.class);
	
	/**
	 * This method is used to execute the Archiving Process from database.
	 */
	public String executeJob(Integer schedulerId)
	{
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		Connection connPCM = null;
		Session session = null;
		try
		{
			conn = ConnectionManager.getInstance().databaseCon();
			String hostId= CacheManager.getInstance().getHostId();
			String archiveOption =PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PCM_ARCHIVE_OPTION);
			
			/* Checking the archive option mentioned in the Swtcommon.properties file. In case it is missing or having wrong option value,
			inserting a row in S_error_log table */
			if(!(archiveOption.equals("0")||archiveOption.equals("1")||archiveOption.equals("2")))
			{
				//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
                log.debug(" $$$$$$$$$  Sp_Error_Log Called at "+SwtUtil.getTimeWithMilliseconds());
			    pstmt=conn.prepareCall("{call  Sp_Error_Log(?,?,?,?,?)}");
			    pstmt.setString(1,"SYSTEM");
				pstmt.setString(2,"SYS_DBSERVER");
				pstmt.setString(3,"PCMDataArchiving");
				pstmt.setString(4,"0");
				pstmt.setString(5,"Invalid archive.option value");
				retValue="F";
			}else{
				String archiveDirectoryPath =PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PCM_ARCHIVE_DIRECTORY_PATH);
				//Log Added for calculating the Time taken for the stored procedure to execute
                log.debug(" $$$$$$$$$  PKG_PC_ARCHIVE.execute_process Called at "+SwtUtil.getTimeWithMilliseconds());
                session = SwtUtil.pcSessionFactory.openSession();
                connPCM = SwtUtil.connection(session);
				pstmt = connPCM.prepareCall("{call  PKG_PC_ARCHIVE.execute_process(?,?,?,?)}");
				pstmt.setString(1,hostId);
				pstmt.setString(2,archiveOption);
				pstmt.setString(3,archiveDirectoryPath);
				pstmt.registerOutParameter(4, Types.VARCHAR);
				pstmt.executeUpdate();
				//Log Added for calculating the Time taken for the stored procedure to execute
                log.debug(" $$$$$$$$$  PKG_PC_ARCHIVE.execute_process Ended at "+SwtUtil.getTimeWithMilliseconds());
				retValue=pstmt.getString(4);
				int result=Integer.parseInt(retValue);
				retValue = (result==0) ? "S" : "F";
			}
			//If all is ok, delete the reports
		}catch(Exception  e){
			log.debug("PROBLEM IN PCMDataArchiving.executeJob : " + e.getMessage());
			SwtException se = new SwtException();
			se.setUserId("SYSTEM");
			se.setErrorLogFlag("Y");
			se.setErrorDesc(e.getMessage());
			se.setSrcCodeLocation("PCMDataArchiving.executeJob");
			SwtUtil.logErrorInDatabase(se);
			retValue="F";
		}finally{
					
			JDBCCloser.close(null, pstmt, connPCM, session);
			try {
				// Returning the connection object
				if ((conn != null) && (!conn.isClosed())) {
					ConnectionManager.getInstance().retrunConnection(conn);
				}
			} catch (SQLException e1) {
				log.error(
						"org.swallow.batchScheduler.PCMDataArchiving - [executeJob] - Exception - " + e1.getMessage());
			}

		}
		return retValue;
	}	
}
