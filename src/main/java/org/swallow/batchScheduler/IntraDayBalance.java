/*
 * @(#)IntraDayBalance.java 1.0 04/09/08
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK 
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;


public class IntraDayBalance extends SwtBasicJob {

	private final Log log = LogFactory.getLog(IntraDayBalance.class);
	public static String processId;
	
	public void setProcessId() {
		try {
			IntraDayBalance.processId = SequenceFactory.getSequenceFromDb(SwtConstants.ILM_PROCESS_DRIVER_SEQUENCE_NAME);
		} catch (SwtException e) {
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance().handleException(e, "setProcessId", IntraDayBalance.class));
			log.error(this.getClass().getName()	+ " - Exception Catched in [setProcessId] method : - "+  e.getMessage());
		}
	}
	
	static {
		IntraDayBalance intraDayBalance = new IntraDayBalance();
		intraDayBalance.setProcessId();
    }
	/**This abstract method is used to execute the job 
	  * @return String  
	  */	
	public String executeJob(Integer schedulerId) {
		
	log.debug( this.getClass().getName() + "-  [executeJob] - Entry ");		
		/* Local variable declarations */
		Connection conn=null;
		CallableStatement pstmt=null;		 
		String retValue="F";
		 try {
			/* Get database connection  */ 
			conn = getConnection();
			log.debug(this.getClass().getName() + "-  [executeJob - Proc-Start-Time] - "
					+SwtUtil.getTimeWithMilliseconds());
			/* Calling procedure using prepareCall */
			pstmt=conn.prepareCall("{call SP_COLLECT_INTRADAY_STATS(?, ?)}");
			/* Receiving the output from Procedure */
			pstmt.setString(1, processId);
			pstmt.registerOutParameter(2, Types.VARCHAR);
			pstmt.executeUpdate();
			log.debug(this.getClass().getName() + "-  [executeJob - Proc-End-Time] - "
					+SwtUtil.getTimeWithMilliseconds());
			retValue=pstmt.getString(2);
			/* If output is 'Y' the status of the job is 'S'means Successful
			 * otherwise it is 'F' means Failed */			
			
			if(retValue.equalsIgnoreCase("Y")){
				retValue="S";
			}else{
				retValue="F";
			}			
			
			
		} catch (Exception e) {			
			log.debug(this.getClass().getName() + "-  [executeJob] exception " + e.getMessage());
			log.error(this.getClass().getName() + "-  [executeJob] exception " + e.getMessage());
			e.printStackTrace();
			return retValue;
		}finally{			
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
			
		}		
		log.debug( this.getClass().getName() + "-  [executeJob] - Exit ");		
		
		return retValue;
	}
}