/*
 * @(#)SwtJobInfo.java 25/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.batchScheduler;
import java.io.Serializable;
import java.util.Date;


public class SwtJobInfo implements Serializable {

	public SwtJobInfo(String programName, String jobId, String hostId,
			 String jobType, Date startingDate,
			String startingTime, Date endingDate, String endingTime,
			int durationHours, int durationMins, int durationSecs,
			Date scheduleDate, String scheduleDay,
			String scheduleTime, String monthFirst, String monthLast,
			int monthDate, Date lastExecutionTime, String lastExecutionStatus,
			Date nextExecutionTime, String currentStatus,String jobStatus, Integer schedulerId) {
		super();
		this.programName = programName;
		this.jobId = jobId;
		this.hostId = hostId;
		//this.entityId = entityId;
		this.jobType = jobType;
		this.startingDate = startingDate;
		this.startingTime = startingTime;
		this.endingDate = endingDate;
		this.endingTime = endingTime;
		this.durationHours = durationHours;
		this.durationMins = durationMins;
		this.durationSecs = durationSecs;
		this.scheduleDate = scheduleDate;
		this.scheduleDay = scheduleDay;

		this.scheduleTime = scheduleTime;
		this.monthFirst = monthFirst;
		this.monthLast = monthLast;
		this.monthDate = monthDate;
		this.lastExecutionTime = lastExecutionTime;
		this.lastExecutionStatus = lastExecutionStatus;
		this.nextExecutionTime = nextExecutionTime;
		this.currentStatus = currentStatus;
		this.jobStatus = jobStatus;
		this.schedulerId = schedulerId;
	}
	public SwtJobInfo(String programName, String jobId, String hostId,
				String jobType, Date startingDate,
				String startingTime, Date endingDate, String endingTime,
				int durationHours, int durationMins, int durationSecs,
				Date scheduleDate, String scheduleDay,
				String scheduleTime, String monthFirst, String monthLast,
				int monthDate,String jobStatus, Integer schedulerId) {
			super();
			this.programName = programName;
			this.jobId = jobId;
			this.hostId = hostId;
			//this.entityId = entityId;
			this.jobType = jobType;
			this.startingDate = startingDate;
			this.startingTime = startingTime;
			this.endingDate = endingDate;
			this.endingTime = endingTime;
			this.durationHours = durationHours;
			this.durationMins = durationMins;
			this.durationSecs = durationSecs;
			this.scheduleDate = scheduleDate;
			this.scheduleDay = scheduleDay;

			this.scheduleTime = scheduleTime;
			this.monthFirst = monthFirst;
			this.monthLast = monthLast;
			this.monthDate = monthDate;
			this.jobStatus = jobStatus;
			this.schedulerId = schedulerId;

	}


	public SwtJobInfo()
	{
       
	}


	/**
	 * @return Returns the entityId.
	 */

	/**
	 * @param entityId The entityId to set.
	 */

	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}
	/**
	 * @param hostId The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}
	public  String programName;
	public String jobId;
	public String hostId;
	public String jobType;
	public Date startingDate;
	public String startingTime;
	public Date endingDate;
	public String endingTime;
	public int durationHours;
	public int durationMins;
	public int durationSecs;
	public Date scheduleDate;
	public String scheduleDay;

	public String  scheduleTime;

	public String monthFirst;
	public String monthLast;
	public int monthDate ;
	public Date lastExecutionTime;
	public String lastExecutionStatus;
	public Date nextExecutionTime;
	public String currentStatus;
	public String jobStatus;

	private String userId;
	private Integer schedulerId;

	private boolean isClusterUpdate = false;
	private boolean runAfterChange = true;



	/**
	 * @return Returns the durationHours.
	 */
	public int getDurationHours() {
		return durationHours;
	}
	/**
	 * @param durationHours The durationHours to set.
	 */
	public void setDurationHours(int durationHours) {
		this.durationHours = durationHours;
	}
	/**
	 * @return Returns the durationMins.
	 */
	public int getDurationMins() {
		return durationMins;
	}
	/**
	 * @param durationMins The durationMins to set.
	 */
	public void setDurationMins(int durationMins) {
		this.durationMins = durationMins;
	}
	/**
	 * @return Returns the durationSecs.
	 */
	public int getDurationSecs() {
		return durationSecs;
	}
	/**
	 * @param durationSecs The durationSecs to set.
	 */
	public void setDurationSecs(int durationSecs) {
		this.durationSecs = durationSecs;
	}
	/**
	 * @return Returns the endingDate.
	 */
	public Date getEndingDate() {
		return endingDate;
	}
	/**
	 * @param endingDate The endingDate to set.
	 */
	public void setEndingDate(Date endingDate) {
		this.endingDate = endingDate;
	}
	/**
	 * @return Returns the endingTime.
	 */
	public String getEndingTime() {
		return endingTime;
	}
	/**
	 * @param endingTime The endingTime to set.
	 */
	public void setEndingTime(String endingTime) {
		this.endingTime = endingTime;
	}
	/**
	 * @return Returns the jobId.
	 */
	public String getJobId() {
		return jobId;
	}
	/**
	 * @param jobId The jobId to set.
	 */
	public void setJobId(String jobId) {
		this.jobId = jobId;
	}
	/**
	 * @return Returns the jobType.
	 */
	public String getJobType() {
		return jobType;
	}
	/**
	 * @param jobType The jobType to set.
	 */
	public void setJobType(String jobType) {
		this.jobType = jobType;
	}
	/**
	 * @return Returns the monthDate.
	 */
	public int getMonthDate() {
		return monthDate;
	}
	/**
	 * @param monthDate The monthDate to set.
	 */
	public void setMonthDate(int monthDate) {
		this.monthDate = monthDate;
	}
	/**
	 * @return Returns the monthFirst.
	 */
	public String getMonthFirst() {
		return monthFirst;
	}
	/**
	 * @param monthFirst The monthFirst to set.
	 */
	public void setMonthFirst(String monthFirst) {
		this.monthFirst = monthFirst;
	}
	/**
	 * @return Returns the monthLast.
	 */
	public String getMonthLast() {
		return monthLast;
	}
	/**
	 * @param monthLast The monthLast to set.
	 */
	public void setMonthLast(String monthLast) {
		this.monthLast = monthLast;
	}
	/**
	 * @return Returns the programName.
	 */
	public String getProgramName() {
		return programName;
	}
	/**
	 * @param programName The programName to set.
	 */
	public void setProgramName(String programName) {
		this.programName = programName;
	}
	/**
	 * @return Returns the scheduleDate.
	 */
	public Date getScheduleDate() {
		return scheduleDate;
	}
	/**
	 * @param scheduleDate The scheduleDate to set.
	 */
	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}
	/**
	 * @return Returns the scheduleDay.
	 */
	public String getScheduleDay() {
		return scheduleDay;
	}
	/**
	 * @param scheduleDay The scheduleDay to set.
	 */
	public void setScheduleDay(String scheduleDay) {
		this.scheduleDay = scheduleDay;
	}
	/**
	 * @return Returns the scheduleInterval.
	 */

	/**
	 * @param scheduleInterval The scheduleInterval to set.
	 */

	/**
	 * @return Returns the scheduleTime.
	 */
	public String getScheduleTime() {
		return scheduleTime;
	}
	/**
	 * @param scheduleTime The scheduleTime to set.
	 */
	public void setScheduleTime(String scheduleTime) {
		this.scheduleTime = scheduleTime;
	}
	/**
	 * @return Returns the startingDate.
	 */
	public Date getStartingDate() {
		return startingDate;
	}
	/**
	 * @param startingDate The startingDate to set.
	 */
	public void setStartingDate(Date startingDate) {
		this.startingDate = startingDate;
	}
	/**
	 * @return Returns the startingTime.
	 */
	public String getStartingTime() {
		return startingTime;
	}
	/**
	 * @param startingTime The startingTime to set.
	 */
	public void setStartingTime(String startingTime) {
		this.startingTime = startingTime;
	}
	/**
	 * @return Returns the currentStatus.
	 */
	public String getCurrentStatus() {
		return currentStatus;
	}
	/**
	 * @param currentStatus The currentStatus to set.
	 */
	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}
	/**
	 * @return Returns the lastExecutionStatus.
	 */
	public String getLastExecutionStatus() {
		return lastExecutionStatus;
	}
	/**
	 * @param lastExecutionStatus The lastExecutionStatus to set.
	 */
	public void setLastExecutionStatus(String lastExecutionStatus) {
		this.lastExecutionStatus = lastExecutionStatus;
	}
	/**
	 * @return Returns the lastExecutionTime.
	 */
	public Date getLastExecutionTime() {
		return lastExecutionTime;
	}
	/**
	 * @param lastExecutionTime The lastExecutionTime to set.
	 */
	public void setLastExecutionTime(Date lastExecutionTime) {
		this.lastExecutionTime = lastExecutionTime;
	}
	/**
	 * @return Returns the nextExecutionTime.
	 */
	public Date getNextExecutionTime() {
		return nextExecutionTime;
	}
	/**
	 * @param nextExecutionTime The nextExecutionTime to set.
	 */
	public void setNextExecutionTime(Date nextExecutionTime) {
		this.nextExecutionTime = nextExecutionTime;
	}
	/**
	 * @return Returns the userId.
	 */
	public String getUserId() {
		return userId;
	}
	/**
	 * @param userId The userId to set.
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	/**
	 * @return Returns the jobStatus.
	 */
	public String getJobStatus() {
		return jobStatus;
	}
	/**
	 * @param jobStatus The jobStatus to set.
	 */
	public void setJobStatus(String jobStatus) {
		this.jobStatus = jobStatus;
	}
	public Integer getSchedulerId() {
		return schedulerId;
	}
	public void setSchedulerId(Integer schedulerId) {
		this.schedulerId = schedulerId;
	}
	public boolean isClusterUpdate() {
		return isClusterUpdate;
	}
	public void setClusterUpdate(boolean isClusterUpdate) {
		this.isClusterUpdate = isClusterUpdate;
	}
	public boolean isRunAfterChange() {
		return runAfterChange;
	}
	public void setRunAfterChange(boolean runAfterChange) {
		this.runAfterChange = runAfterChange;
	}
}
