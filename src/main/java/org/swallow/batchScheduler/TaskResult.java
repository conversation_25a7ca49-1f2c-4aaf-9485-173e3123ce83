/*
 * @(#)TaskResult.java
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;



public class TaskResult {
	private final Log log = LogFactory.getLog(TaskResult.class);
	/** DOCUMENT ME! */
	private String errorCode;

	/**
	 * @return Returns the errorDesc.
	 */
	public String getErrorDesc() {
		return errorDesc;
	}
	/**
	 * @param errorDesc The errorDesc to set.
	 */
	public void setErrorDesc(String errorDesc) {
		this.errorDesc = errorDesc;
	}
	/**
	 * @return Returns the results.
	 */
	public Collection getResults() {
		return results;
	}
	/**
	 * @param results The results to set.
	 */
	public void setResults(Collection results) {
		this.results = results;
	}
	/** DOCUMENT ME! */
	private String errorDesc;

	/** DOCUMENT ME! */
	private Collection results;
	/**
	 * 
	 */
	public TaskResult() {
		super();
	}
	
	/**
	 * @param errorCode
	 * @param errorDesc
	 * @param results
	 */
	public TaskResult(String errorCode, String errorDesc, Collection results) {
		super();
		this.errorCode = errorCode;
		this.errorDesc = errorDesc;
		this.results = results;
	}
	/**
	 * @return Returns the errorCode.
	 */
	public String getErrorCode() {
		return errorCode;
	}
	/**
	 * @param errorCode The errorCode to set.
	 */
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	
	
	public String toString(){
		//log.debug("errorCode is::"+errorCode);
		//log.debug("errorDesc is::"+errorDesc);
		//log.debug("results is::"+results);
		StringBuffer buf = new StringBuffer();
		buf.append("ErrorCode - ")
			.append(errorCode)
			.append("ErrorDescription - ")
			.append(errorDesc);			
			
		return buf.toString();
	}
}