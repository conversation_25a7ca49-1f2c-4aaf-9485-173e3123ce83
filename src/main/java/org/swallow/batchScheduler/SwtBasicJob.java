/*
 * @(#)SwtBasicJob.java 30/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.batchScheduler;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.StringTokenizer;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.SchedulerException;
import org.swallow.control.dao.InterfaceInterruptDAO;
import org.swallow.control.model.JobStatus;
import org.swallow.control.model.Scheduler;
import org.swallow.control.service.SchedulerManager;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.cluster.ZkUtils;

/**
 * This class is the base class for all the jobs created for predict. This class
 * contains executeJob method which will be implemented by all the jobs. Actions
 * performed before starting and after the execution of the job are defined in
 * doJobStartLog and doJobEndLog methods.
 */
public abstract class SwtBasicJob implements Job {

	/*
	 * All the Job classes will extend this class and should implement
	 * executeJob method.
	 */
	public abstract String executeJob(Integer schedulerId) throws SQLException;

	/* Logger object to log details into server.log file */
	private final Log logger = LogFactory.getLog(SwtBasicJob.class);

	/*
	 * Class variable to share in different methods: Initiated and closed in
	 * execute method
	 */
	private JobExecutionContext jobContext;

	/* Connection instance for all jobs */
	private Connection conn = null;

	/**
	 * Method initiated before executing any jobs either manually or by Quartz
	 * scheduler. This method updates the job status for the jobs which are to
	 * be executed
	 * 
	 * @param context
	 * @throws SQLException
	 */
	public void doJobStartLog(JobExecutionContext context) throws SQLException {
		// Stores the hostId
		String hostId = null;
		// Stores the job id
		Integer schedulerId = null;
		// Statement for updating Job status
		PreparedStatement updateJobStatusStmt = null;
		// SwtException instance to log error in error log table
		SwtException swtException = null;
		// To create update query
		String updateJobStatusQuery = null;
		try {
			// Entry message
			logger.debug(this.getClass().getName() + "-[doJobStartLog]-Entry");
			// Fetch the host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// Extracting JobId from quartz scheduler
			try {
				schedulerId = (Integer)(context.getScheduler().getContext().get(context
						.getJobDetail().getKey().getName()));
			} catch (SchedulerException ex) {
				throw ex;
			}
			// Updating the S_JOB_STATUS table with the running status
			try {
				updateJobStatusQuery = "UPDATE S_JOB_STATUS SET CURRENT_STATUS=?,PROCESS_NUMBER=P_JOB_SEQUENCE.NEXTVAl WHERE HOST_ID=? AND SCHEDULE_ID=?";
				// Initialize the statement
				updateJobStatusStmt = conn.prepareStatement(updateJobStatusQuery);
				updateJobStatusStmt.setString(1, SwtConstants.JOB_STATUS_RUNNING);
				updateJobStatusStmt.setString(2, hostId);
				updateJobStatusStmt.setInt(3, schedulerId);
				updateJobStatusStmt.executeUpdate();
				conn.commit();
			} catch (Exception ex) {
				throw new Exception(
						"Updation of S_JOB_STATUS table with the for running status failed "
								+ ex.getMessage());
			}
		} catch (Exception ex) {
			logger
					.error("Exception Catch in SwtBasicJob-[doJobStartLog]- method : "
							+ ex.getMessage());
			// log exception as SwtException
			swtException = new SwtException();
			swtException.setErrorLogFlag("Y");
			swtException.setErrorDesc(ex.getMessage());
			swtException.setSrcCodeLocation("SwtBasicJob.doJobStartLogb");
			// log error in database
			SwtUtil.logErrorInDatabase(swtException);
		} finally {
			// Close statement
			JDBCCloser.close(null, updateJobStatusStmt, null, null);

			// nullify the variables
			hostId = null;
			updateJobStatusStmt = null;
			swtException = null;
			updateJobStatusQuery = null;
			// Exit message
			logger.debug(this.getClass().getName() + "-[doJobStartLog]-Exit");
		}
	}

	/**
	 * This method is invoked whenever the execution of job is completed. This
	 * method is will update the job status either with their execution result
	 * and creates a log entry into the job log table.
	 * 
	 * @param context
	 * @param result
	 * @param startDate
	 * @throws SQLException
	 */
	public void doJobEndLog(JobExecutionContext context, String result,
			Timestamp startDate) throws SQLException {
		// Statement to fetch the process number for the completed job
		PreparedStatement processNumberStmt = null;
		// Prepared statement to update job status
		PreparedStatement updateStatusPredStmt = null;
		// Stores the result set with process number of the completed job
		ResultSet processNumberResultSet = null;
		// Stores the host id
		String hostId = null;
		// Stores the user id
		String userId = null;
		// Stores the completed scheduler Id
		Integer schedulerId = null;
		// Stores the completed job name
		String jobName = null;
		// Timestamp instance to set the start, completed and next execution
		// date and time
		Timestamp nextDate = null;
		Timestamp endingDate = null;
		Timestamp previousDate = null;
		// Stores the process number of the completed job
		long processNumber = 0;
		// Tokenizer instance to check job name
		StringTokenizer jobTokens = null;
		// List to store job name split with underscore
		ArrayList<String> jobNameArrlst = null;
		// Instance to build the job status update query
		String updateJobStatusQuery = null;
		// Variables to calculate the next execution time
		long cycleTime, runTime, tempTime, nextExecutionTime = 0;
		// Instance to frame process number query
		String processNumQuery = null;
		// Instance to frame insert job log query
		String insertJobLogQuery = null;
		// SwtException instance to throw error while updating job status
		SwtException swtException = null;
		//var to hold schedulerManager
		SchedulerManager schedulerManager = null;
		//var to hold the scheduler
		Scheduler scheduler = null;
		// var to hold jobId
		String jobId = null;
		try {
			// Entry message
			logger.debug(this.getClass().getName() + "-[doJobEndLog]-Entry");
			// Fetch the host id from Cache manager
			hostId = CacheManager.getInstance().getHostId();
			// Initialize the user id with default value
			userId = SwtConstants.USER_SYSTEM;
			// Initialize the endingDate with job completed time
			endingDate = new Timestamp(SwtUtil.getTestDateFromParams(hostId)
					.getTime());
			
		
			try {
				//Get the schedulerId from context
				schedulerId =(Integer) (context.getScheduler().getContext().get(context
						.getJobDetail().getKey().getName()));
				
				// Initialize the scheduler manager
				schedulerManager = (SchedulerManager) SwtUtil
						.getBean("schedulerManager");
				
				scheduler = (Scheduler) schedulerManager.getSchedulerDetails(schedulerId , null).iterator().next();
				
				jobId = scheduler.getJobId();
				
				if (context.getScheduler().getContext().get("userId") != null) {
					userId = (String) context.getScheduler().getContext().get(
							"userId");
					context.getScheduler().getContext().remove("userId");
				}
				// Contains the information whether Job is of IMD type or not
				jobName = context.getJobDetail().getKey().getName();

			} catch (SchedulerException ex) {
				throw new Exception(
						"Extracting JobId from quartz scheduler Failed - "
								+ ex.getMessage());
			}
			// Fetch the last executed time for the job
			previousDate = new Timestamp(context.getFireTime().getTime());
			// Split the job name with underscore
			jobTokens = new StringTokenizer(jobName, "_");
			// Initialize the job name list
			jobNameArrlst = new ArrayList<String>();
			// Iterate through the split job name and store it in a list
			while (jobTokens.hasMoreTokens()) {
				jobNameArrlst.add(jobTokens.nextToken());
			}
			// Condition to check the job is of IMD type
			if (jobNameArrlst.size() >= 2
					&& jobNameArrlst.get(1).toString().equals("IMD")) {
				try {
					// Build the update query for manual jobs
					updateJobStatusQuery = "update S_job_status set LAST_EXECUTION_TIME=?, LAST_EXECUTION_STATUS=? ,NEXT_EXECUTION_TIME = NULL," +
							"CURRENT_STATUS= decode(current_status,'C','D','P')where host_id=? and schedule_id = ?";
					// Initialize the prepared statement for update
					updateStatusPredStmt = conn
							.prepareStatement(updateJobStatusQuery.toString());
					// Set the last executed date and time
					updateStatusPredStmt.setTimestamp(1, previousDate);
					updateStatusPredStmt.setString(2, result);
					updateStatusPredStmt.setString(3, hostId);
					updateStatusPredStmt.setInt(4, schedulerId);
					// Update last executed date and time, execution, current
					// status and next executed date as null for manual jobs
					updateStatusPredStmt.executeUpdate();
					conn.commit();
				} catch (Exception ex) {
					ex.printStackTrace();
					throw new Exception(
							"For IMD S_JOB_STATUS updation Failed or problem in NEXT_EXECUTION_TIME for JobId- "
									+ schedulerId + " : " + ex.getMessage());
				}
			} else {
				// Job is not of IMD type
				try {
					// Get the next execution date and time
					nextDate = context.getNextFireTime() != null ? new Timestamp(
							context.getNextFireTime().getTime())
							: null;
					// If next execution time is not null, set the current
					// status from scheduler table, else null
					if (nextDate != null) {
						if (nextDate.before(new Date())) {
							// Get the time in milliseconds between last start
							// time and next execution time
							//Start - Commented because it's creating an infinite loop
//							cycleTime = nextDate.getTime()
//									- context.getFireTime().getTime();
							// Get the time taken to run this job
//							runTime = (new Date().getTime())
//									- context.getFireTime().getTime();
							// Assign the cycle time to temp variable
//							tempTime = cycleTime;
							// Loop until the cycle time is greater than the
							// time taken to run the job
//							while (runTime > cycleTime) {
//								cycleTime += tempTime;
//							}
							//End - Commented because it's creating an infinite loop
							// Reset the next execution time by last completed
							// time + the cycle time
							nextExecutionTime = new Date().getTime() + 1000;
							nextDate.setTime(nextExecutionTime);
						}

						updateJobStatusQuery = "update S_job_status set LAST_EXECUTION_TIME=?,UPDATE_DATE =?, UPDATE_USER=?,LAST_EXECUTION_STATUS=?," +
								"NEXT_EXECUTION_TIME=?,CURRENT_STATUS=" +
								"decode((select job_status from s_scheduler where schedule_id = ?),'E','P','D') where host_id=? and schedule_id = ? ";
						// Initialize the prepared statement for update
						updateStatusPredStmt = conn
								.prepareStatement(updateJobStatusQuery);
						// Set the last execution date time, updated date and user
						// and next execution time
						updateStatusPredStmt.setTimestamp(1, previousDate);
						updateStatusPredStmt.setTimestamp(2, endingDate);
						updateStatusPredStmt.setString(3, userId);
						updateStatusPredStmt.setString(4, result);
						updateStatusPredStmt.setTimestamp(5, nextDate);
						updateStatusPredStmt.setInt(6, schedulerId);
						updateStatusPredStmt.setString(7, hostId);
						updateStatusPredStmt.setInt(8, schedulerId);

						// Execute the query
					} else {
						updateJobStatusQuery = 	"update S_job_status set LAST_EXECUTION_TIME=?, UPDATE_DATE =?,UPDATE_USER=?, LAST_EXECUTION_STATUS=?," +
								"NEXT_EXECUTION_TIME=?, CURRENT_STATUS=NULL where host_id=? and schedule_id = ?";
						// Initialize the prepared statement for update
						updateStatusPredStmt = conn
								.prepareStatement(updateJobStatusQuery);
						// Set the last execution date time, updated date and user
						// and next execution time
						updateStatusPredStmt.setTimestamp(1, previousDate);
						updateStatusPredStmt.setTimestamp(2, endingDate);
						updateStatusPredStmt.setString(3, userId);
						updateStatusPredStmt.setString(4, result);
						updateStatusPredStmt.setTimestamp(5, nextDate);
						updateStatusPredStmt.setString(6, hostId);
						updateStatusPredStmt.setInt(7, schedulerId);

					}
					
					// Execute the query
					updateStatusPredStmt.executeUpdate();
					conn.commit();
				} catch (Exception ex) {
					throw new Exception(
							"For NON-IMD S_JOB_STATUS updation Failed or problem in NEXT_EXECUTION_TIME for JobId- "
									+ schedulerId + " : " + ex.getMessage());
				}
			}

			// Extracting Process number of the job for inserting Job Log
			try {
				processNumQuery = "SELECT PROCESS_NUMBER  FROM S_job_status WHERE HOST_ID =? AND SCHEDULE_ID = ?";
				processNumberStmt = conn.prepareStatement(processNumQuery);
				processNumberStmt.setString(1, hostId);
				processNumberStmt.setInt(2, schedulerId);
				// Initialize the result set by executing query to fetch process
				// number
				// Initialize the process number statement
				processNumberResultSet = processNumberStmt.executeQuery();
				processNumberResultSet.next();
				processNumber = processNumberResultSet.getLong(1);
			} catch (Exception ex) {
				throw new Exception(
						"Extraction of PROCESS_NUMBER FAILES for JobId -"
								+ schedulerId + " : " + ex.getMessage());
			} finally {
				SQLException thrownException = null;
				thrownException = JDBCCloser.close(processNumberResultSet);
				if (thrownException != null)
					throw new SwtException(thrownException.getMessage());

			}
			try {
				// Frame the insert query for job log
				insertJobLogQuery = "insert into s_job_log(host_id,job_id, process_number,START_DATE,END_DATE, STATUS,UPDATE_DATE,UPDATE_USER, SCHEDULE_ID) VALUES(?,?,?,?,?,?,GLOBAL_VAR.SYS_DATE,?,?)";
				// Reuse the existing prepared statement for insert and
				// initialize it with insert query
				updateStatusPredStmt = conn.prepareStatement(insertJobLogQuery
						.toString());
				// Set the start and completion date time of the job
				updateStatusPredStmt.setString(1, hostId);
				updateStatusPredStmt.setString(2, jobId);
				updateStatusPredStmt.setLong(3, processNumber);
				updateStatusPredStmt.setTimestamp(4, startDate);
				updateStatusPredStmt.setTimestamp(5, endingDate);
				updateStatusPredStmt.setString(6, result);
				updateStatusPredStmt.setString(7, userId);
				updateStatusPredStmt.setInt(8, schedulerId);
				// Insert the job log record
				updateStatusPredStmt.executeUpdate();
				conn.commit();
			} catch (Exception ex) {
				ex.printStackTrace();
				throw new Exception(
						"Problem in inserting rows in s_job_log for JobId -"
								+ schedulerId + " : " + ex.getMessage());
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("PROBLEM IN SWTBasicJob.doJobEndLog : "
					+ ex.getMessage());
			swtException = new SwtException();
			swtException.setUserId(userId);
			swtException.setErrorLogFlag("Y");
			swtException.setErrorDesc(ex.getMessage());
			swtException.setSrcCodeLocation("SWTBasicJob.doJobEndLog");
			SwtUtil.logErrorInDatabase(swtException);
		} finally {

			JDBCCloser.close(updateStatusPredStmt);
			JDBCCloser.close(processNumberResultSet, processNumberStmt, null,
					null);

			// nullify the variables
			processNumberStmt = null;
			updateStatusPredStmt = null;
			processNumberResultSet = null;
			hostId = null;
			userId = null;
			jobName = null;
			nextDate = null;
			endingDate = null;
			previousDate = null;
			jobTokens = null;
			jobNameArrlst = null;
			updateJobStatusQuery = null;
			processNumQuery = null;
			insertJobLogQuery = null;
			swtException = null;
			// Exit message
			logger.debug(this.getClass().getName() + "-[doJobEndLog]-Exit");
		}
	}

	/**
	 * This is default method used to execute any job.
	 * 
	 * @param context
	 */
	public void execute(JobExecutionContext context) {
		// Stores the host id
		String hostId = null;
		// Stores the result of the job executed
		String result = null;
		// Stores the jobId of the job executed
	//	String jobId = null;
		// Stores the job status of the job executed
		String jobStatus = null;
		// Stores start date and time of the job
		Timestamp startDate = null;
		// Instance for SwtException
		SwtException swtException = null;
		// Instance of SchedulerManager
		SchedulerManager schedulerMgr = null;
		// JobStatus instance
		JobStatus jobStatusObj = null;
		// Stores the current status of a job
		String currentStatus = null;
		// InterfaceInterruptDAO instance for deleting the notifications while
		// removing the Notification scheduler.
		InterfaceInterruptDAO interfaceInterruptDAO;
		//Store the schedulerId
		Integer schedulerId = null;

		try {
			
			
			// Entry message
			logger.debug(this.getClass().getName() + "-[execute]-Entry");
			// Fetch the host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// Fetch the start time for the job
			startDate = new Timestamp(SwtUtil.getTestDateFromParams(hostId)
					.getTime());
			// Initialize the connection object
			this.conn = ConnectionManager.getInstance().databaseCon();
			// Initialize the quartz context object
			this.jobContext = context;
			// Fetch the job id
			schedulerId = (Integer) (context.getScheduler().getContext().get(context
					.getJobDetail().getKey().getName()));
			//

			String runningJobUid = ZkUtils.getProperty(ZkUtils.PROPERTY_CHOSEN_INSTANCE,30000);
			
			if(runningJobUid != null && !ZkUtils.instanceUuid.equals(runningJobUid)) {
			  return;
			}
			// Fetch the job status from context
			jobStatus = (String) context.getScheduler().getContext().get(
					schedulerId + "_jobStatus");
			// Initialize the scheduler manager
			schedulerMgr = (SchedulerManager) SwtUtil
					.getBean("schedulerManager");
			// Get the job status for the selected job id
			jobStatusObj = schedulerMgr.getJobStatus(schedulerId);
			// Initialize the current status from job status object if null, set
			// to enable status
			currentStatus = jobStatusObj.getCurrentStatus() != null ? jobStatusObj
					.getCurrentStatus()
					: SwtConstants.ADDJOB_ENABLE;
			if ((jobStatus != null)
					&& !jobStatus.equals(SwtConstants.ADDJOB_DISABLE)) {
				if (!(currentStatus.equals(SwtConstants.JOB_STATUS_RUNNING)
						|| currentStatus
								.equals(SwtConstants.JOB_STATUS_CLOSING) || currentStatus
						.equals(SwtConstants.ADDJOB_DISABLE))) {
					doJobStartLog(context);
					result = executeJob(schedulerId);
					 if(!result.equalsIgnoreCase("Cancel")){
							// Invoke actions to be performed after completion of a job
							doJobEndLog(context, result, startDate);
						}
					else{
							if(jobStatusObj.getLastExecTime()!=null){
								context.getFireTime().setTime(jobStatusObj.getLastExecTime().getTime());
							}
							doJobEndLog(context, jobStatusObj.getLastExecStatus()!=null?jobStatusObj.getLastExecStatus():"", startDate);
						} 
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			logger.error("Exception Catch in SWTBasicJob -[execute]- : "
					+ ex.getMessage());
			swtException = new SwtException();
			swtException.setErrorLogFlag("Y");
			swtException.setErrorDesc(ex.getMessage());
			swtException.setSrcCodeLocation("SWTBasicJob.execute");
			SwtUtil.logErrorInDatabase(swtException);
		} finally {

			// Close connection
			JDBCCloser.close(null, null, conn, null);

			// nullify the variables
			hostId = null;
			result = null;
			jobStatus = null;
			startDate = null;
			swtException = null;
			schedulerMgr = null;
			jobStatusObj = null;
			currentStatus = null;
			// Exit message
			logger.debug(this.getClass().getName() + "-[execute]-Exit");
		}
	}

	/**
	 * @return Returns the jobContext.
	 */
	protected JobExecutionContext getJobContext() {
		return jobContext;
	}

	/**
	 * @return Returns the connection object.
	 */
	protected Connection getConnection() {
		return conn;
	}
}