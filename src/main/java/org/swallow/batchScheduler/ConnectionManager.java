
/*
 * @(#)ConnectionManager.java 30/01/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */



package org.swallow.batchScheduler;
import java.sql.Connection;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;
public class ConnectionManager {
	private static ConnectionManager _instance=null;
	private final Log log = LogFactory.getLog(ConnectionManager.class);
	private  DataSource dataSource = null;
	private static int count = 0;
	private ConnectionManager()
	{
		if(dataSource == null)
		dataSource = (DataSource)SwtUtil.getBean("dataSource");
	}
	
	public Connection databaseCon()
	{
		Connection conn=null;
		try{
					log.debug("about to take the connection from the 'dataSource' ");
					if(dataSource != null)
					{
						conn = dataSource.getConnection();
						conn.setAutoCommit(false);
						++count;						
					}
					//conn=  DriverManager.getConnection("jdbc:oracle:thin:@*********:1521:NEWPRED","pred_usr2","password");
				}
					catch(Exception e){
						log.debug("exception in connection manager is"+e.getMessage());
			}
			log.debug("After Taken Total Connection taken by Pool - " + count);					

	return conn;
}
	public static ConnectionManager getInstance()
	{
		if(_instance==null)
			_instance= new ConnectionManager();
		return _instance;
	}
	public void retrunConnection(Connection conn) throws SQLException
	{
		--count;
		
		//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
		JDBCCloser.close(conn);
		
		log.debug("After return Total Connection taken by Pool - " + count);
	}
	

}
	