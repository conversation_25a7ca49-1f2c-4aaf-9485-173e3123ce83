package org.swallow.batchScheduler;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Timestamp;
import java.sql.Types;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 *Code for adding Auto Open Process in scheduler
 *
 */
public class AutoOpenProcess extends SwtBasicJob {

	private final Log log = LogFactory.getLog(AutoOpenProcess.class);

	public String executeJob(Integer schedulerId){
		log.debug("Start executeJob for  Auto Open process");
		String retValue="F";
		Connection conn=null;
		CallableStatement pstmt=null;
		try{
			Timestamp timeStamp=new Timestamp(new java.util.Date().getTime());
			conn = getConnection();
			String hostId= CacheManager.getInstance().getHostId();
			//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
            log.debug(" $$$$$$$$$  PK_AUTO_OPEN_PROCESS.SP_AUTO_OPEN_PROCESS Called at "+SwtUtil.getTimeWithMilliseconds());
			pstmt=conn.prepareCall("{call PK_AUTO_OPEN_PROCESS.SP_AUTO_OPEN_PROCESS(?,?)}");
			pstmt.setString(1,hostId);
			pstmt.registerOutParameter(2, Types.VARCHAR);
			pstmt.executeUpdate();
			//Log Added for calculating the Time taken for the stored procedure to execute on 25-03-2008
            log.debug(" $$$$$$$$$  PK_AUTO_OPEN_PROCESS.SP_AUTO_OPEN_PROCESS Ended at "+SwtUtil.getTimeWithMilliseconds());
			retValue=pstmt.getString(2);
			int result=Integer.parseInt(retValue);
			if(result==0)
				retValue="S";
			else
				retValue="F";
			log.debug("result of execution of AutoOpenProcess "+retValue);
		}catch(Exception  e){
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance().
			                           handleException(e, "executeJob", AutoOpenProcess.class));
			log.debug("AutoOpenProcess: Error in executeJob - " + e.getMessage());
		}finally{

			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(pstmt);
			
		}
		return retValue;
	}
}