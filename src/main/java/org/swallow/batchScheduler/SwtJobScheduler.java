/*
 * @(#)SwtJobScheduler.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.batchScheduler;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.CronTrigger;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.SchedulerFactory;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * This class is used to run the jobs in the scheduler screen
 */
public class SwtJobScheduler {
	private static SwtJobScheduler _instance = null;
	private static Hashtable triggerHashTable = new Hashtable(); // triggerHashtable
	private static Hashtable jobHastTable = new Hashtable();
	private static final String JOB_NAME = "job";
	private static final String GROUP_NAME = "group1";
	private static final String TRIGGER_NAME = "trigger";
	private static final String CYCLE_DURATION = "C";
	private static final String ONCE = "O";
	private static final String MONTHLY = "M";
	private static final String WEEKLY = "W";
	private static final String DAILY = "D";
	private final Log log = LogFactory.getLog(SwtJobScheduler.class);
	private SchedulerFactory _schedularFactoryObj;
	private Scheduler _schedularObj = null;

	private SwtJobScheduler() {

		try {
			startup();
			ArrayList AList = readJobInfoFromDB();
			Iterator jobList = AList.iterator();
			while (jobList.hasNext())

				try {
					SwtJobInfo job = (SwtJobInfo) jobList.next();
					registerJob( job);
					SwtUtil.addJobToList( ""+job.getSchedulerId());
				} catch (ArithmeticException aeex) {
					log
							.error(this.getClass().getName()
									+ " - Exception Catched in [SwtJobScheduler] method : - "
									+ aeex.getMessage());
				} catch (Exception ex) {
					log
							.error(this.getClass().getName()
									+ " - Exception Catched in [SwtJobScheduler] method : - "
									+ ex.getMessage());

				}

		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [SwtJobScheduler] method : - "
					+ e.getMessage());
		}
	}

	public synchronized static SwtJobScheduler getInstance() {
		if (_instance == null) {
			_instance = new SwtJobScheduler();
		}
		return _instance;
	}

	public void startup() // startSchedular
	{
		try {

			_schedularFactoryObj = new StdSchedulerFactory();
			_schedularObj = _schedularFactoryObj.getScheduler();
			_schedularObj.start();
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [startuo()] method : - "
					+ e.getMessage());
		}
	}

	public void shutdown() // stopSchedular
	{
		try {
			_schedularObj.shutdown();
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [shutdown()] method : - "
					+ e.getMessage());
		}
	}

	// function for housekeepingjob ends here
	public String queryBuilder(int jobTotal) {
		log.debug(this.getClass().getName() + " - [queryBuilder] - " + "Entry");
		String query = null;
		if (jobTotal > 0) {
			query = "select J.Program_Name,J.JOB_ID,J.Host_Id,S.JOB_TYPE ,S.Starting_Date,S.STARTING_TIME,S.ENDING_DATE,S.ENDING_TIME, "
					+ "S.DURATION_HOURS,S.DURATION_MINS,S.DURATION_SECS,S.SCHEDULE_DATE, "
					+ "S.SCHEDULE_DAY,S.SCHEDULE_TIME ,S.Month_First,S.Month_Last,S.Month_Date,S.JOB_STATUS,JS.LAST_EXECUTION_TIME, JS.LAST_EXECUTION_STATUS, JS.NEXT_EXECUTION_TIME,JS.CURRENT_STATUS,JS.SCHEDULE_ID"
					+ " from 	S_Job J,S_Scheduler S ,S_JOB_STATUS JS WHERE "
					+ "S.HOST_ID=J.HOST_ID AND S.JOB_ID=J.JOB_ID AND JS.HOST_ID=J.HOST_ID AND JS.JOB_ID=J.JOB_ID  AND S.SCHEDULE_ID = JS.SCHEDULE_ID AND J.SYSTEM_JOB = 'Y'";
		} else {
			query = "select J.Program_Name,J.JOB_ID,J.Host_Id,S.JOB_TYPE ,S.Starting_Date,S.STARTING_TIME,S.ENDING_DATE,S.ENDING_TIME, "
					+ " S.DURATION_HOURS,S.DURATION_MINS,S.DURATION_SECS,S.SCHEDULE_DATE, "
					+ " S.SCHEDULE_DAY,S.SCHEDULE_TIME ,S.Month_First,S.Month_Last,S.Month_Date,S.JOB_STATUS, S.SCHEDULE_ID"
					+ " from 	S_Job J,S_Scheduler S  WHERE "
					+ " S.HOST_ID=J.HOST_ID AND S.JOB_ID=J.JOB_ID AND J.SYSTEM_JOB = 'Y'";
		}

		return query;
	}

	public void initJobEntry(SwtJobInfo jobInfoObj, Trigger trigger)
			throws SQLException {
		Connection conn = null;

		ResultSet res = null;
		PreparedStatement ps = null;
		PreparedStatement stmt = null;

		try {
			if(jobInfoObj.isClusterUpdate()) {
				return;
			}

			String jobId = jobInfoObj.getJobId();
			Integer scheduledId = jobInfoObj.getSchedulerId();
			String hostId = jobInfoObj.getHostId();
			Timestamp nextExecutionDate = null;

			if ((jobInfoObj.jobType != null)
					&& !jobInfoObj.jobType.equals(SwtConstants.JOB_TYPE_MANUAL)) {
				nextExecutionDate = new Timestamp(trigger.getNextFireTime()
						.getTime());
			}

			res = null;
			conn = ConnectionManager.getInstance().databaseCon();

			ps = null;
			String query = "select host_Id from s_job_status where schedule_id=?";
			stmt = conn.prepareStatement(query);
			stmt.setInt(1, scheduledId);
			String queryInsert = null;
			res = stmt.executeQuery();
			res.next();

			int total = res.getRow();
			res.close();

			if (total == 0) {

				/**
				 * Depending upon the Enable/Disable Flag the The field
				 * CURRENT_STATUS is set into the table If job is Enabled --
				 * Current Status is 'P'ending If job is Disabled -- Current
				 * Status is 'D'isabled
				 */
				if (jobInfoObj.jobStatus.equals(SwtConstants.ADDJOB_ENABLE)) {

					queryInsert = "insert into s_job_status(PROCESS_NUMBER,JOB_ID,HOST_ID,LAST_EXECUTION_TIME,LAST_EXECUTION_STATUS,NEXT_EXECUTION_TIME,CURRENT_STATUS,SCHEDULE_ID)"
							+ " VALUES("
							+ "?"
							+ ", "
							+ "?"
							+ ", "
							+ "?"
							+ ","
							+ "NULL"
							+ ","
							+ "NULL" + "," + "?" + "," + "'P'"+ ", "+ "?"+")";
				} else if (jobInfoObj.jobStatus
						.equals(SwtConstants.ADDJOB_DISABLE)) {
					queryInsert = "insert into s_job_status(PROCESS_NUMBER,JOB_ID,HOST_ID,LAST_EXECUTION_TIME,LAST_EXECUTION_STATUS,NEXT_EXECUTION_TIME,CURRENT_STATUS,SCHEDULE_ID)"
							+ " VALUES("
							+ "?"
							+ ", "
							+ "?"
							+ ", "
							+ "?"
							+ ","
							+ "NULL"
							+ ","
							+ "NULL" + "," + "?" + "," + "'D'"+ ", "+ "?"+")";
				}

				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setString(1, null);
					ps.setString(2, jobId);
					ps.setString(3, hostId);
					ps.setTimestamp(4, nextExecutionDate);
					ps.setInt(5, scheduledId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - Exception Catched in [initJobEntry] method : - "
									+ e.getMessage());
				}
			} else {
				String jobStatus = jobInfoObj.jobStatus;
				queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=? where job_id=? and host_id=? and schedule_id=?";

				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobId);
					ps.setString(3, hostId);
					ps.setInt(4, scheduledId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [initJobEntry] method : - "
									+ e.getMessage());
				}
			}
		} catch (ArithmeticException ae) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [initJobEntry] method : - "
					+ ae.getMessage());
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [initJobEntry] method : - "
					+ e.getMessage());
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(ps);
			JDBCCloser.close(res, stmt, null, null);
			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);

			} catch (SQLException e) {

				log.error("org.swallow.batchScheduler.SwtJobScheduler - [initJobEntry] - exception :  " + e.getMessage());
			}
		}
		log.debug("Exiting from initJobEntry Method");
	}

	public ArrayList readJobInfoFromDB() throws Exception {
		log.debug("Entering in readJobInfoFromDB");
		SwtJobInfo obj;
		ResultSet res = null;
		ArrayList jobInfoList = new ArrayList();
		Connection conn = null;
		Statement stmt = null;

		int totJobStatus = 0;

		try {
			conn = ConnectionManager.getInstance().databaseCon();
			stmt = conn.createStatement();
			try {
				res = stmt.executeQuery("Select Host_Id from S_JOB_STATUS ");
				totJobStatus = 1;

			} catch (Exception e) {
				log
						.error(this.getClass().getName()
								+ " - Exception Catched in [readJobInfoFromDB] method : - "
								+ e.getMessage());
				totJobStatus = 0;
			}
			if (totJobStatus != 0) {
				res.next();
				totJobStatus = res.getRow();
				/*
				 * Here current status updated from 'C'losing to 'D'isable and
				 * 'R'unning to 'P'ending on server startup
				 */
				PreparedStatement ps = null;
				String queryInsert = "update s_job_status "
						+ "set current_status = decode(current_status,'C','D','R','P',current_status)  where  job_id in(select job_id from s_job where System_job ='Y') and host_id=?";
				ps = conn.prepareStatement(queryInsert);
				ps.setString(1, SwtUtil.getCurrentHostId());
				ps.executeUpdate();
				conn.commit();
			}

			String query = queryBuilder(totJobStatus);
			
			res = stmt.executeQuery(query);

			if (totJobStatus > 0) {
				while (res.next()) {
					obj = new SwtJobInfo(res.getString("Program_Name"), res
							.getString("JOB_ID"), res.getString("Host_Id"), res
							.getString("JOB_TYPE"), res
							.getDate("Starting_Date"), res
							.getString("STARTING_TIME"), res
							.getDate("ENDING_DATE"), res
							.getString("ENDING_TIME"), res
							.getInt("DURATION_HOURS"), res
							.getInt("DURATION_MINS"), res
							.getInt("DURATION_SECS"), res
							.getDate("SCHEDULE_DATE"), res
							.getString("SCHEDULE_DAY"), res
							.getString("SCHEDULE_TIME"), res
							.getString("MONTH_FIRST"), res
							.getString("MONTH_LAST"), res.getInt("MONTH_DATE"),
							res.getDate("LAST_EXECUTION_TIME"), res
									.getString("LAST_EXECUTION_STATUS"), res
									.getDate("NEXT_EXECUTION_TIME"), res
									.getString("CURRENT_STATUS"), res
									.getString("JOB_STATUS"),  res
									.getInt("SCHEDULE_ID")
									);

					jobInfoList.add(obj);
				}
			} else {
				while (res.next()) {
					obj = new SwtJobInfo(res.getString("Program_Name"), res
							.getString("JOB_ID"), res.getString("Host_Id"), res
							.getString("JOB_TYPE"), res
							.getDate("Starting_Date"), res
							.getString("STARTING_TIME"), res
							.getDate("ENDING_DATE"), res
							.getString("ENDING_TIME"), res
							.getInt("DURATION_HOURS"), res
							.getInt("DURATION_MINS"), res
							.getInt("DURATION_SECS"), res
							.getDate("SCHEDULE_DATE"), res
							.getString("SCHEDULE_DAY"), res
							.getString("SCHEDULE_TIME"), res
							.getString("MONTH_FIRST"), res
							.getString("MONTH_LAST"), res.getInt("MONTH_DATE"),
							res.getString("JOB_STATUS"),res
							.getInt("SCHEDULE_ID"));
					jobInfoList.add(obj);
				}
			}

			log.debug("Exit from readJobInfoFromDB");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [readJobInfoFromDB] method : - "
					+ e.getMessage());
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(res, stmt, null, null);
			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);

			} catch (SQLException e) {
				log.error("org.swallow.batchScheduler.SwtJobScheduler - [readJobInfoFromDB] - exception :  " + e.getMessage());
			}
		}
		return jobInfoList;
	}

	public synchronized void registerJob(SwtJobInfo jobInfoObj)
			throws Exception {

		Connection conn = null;

		try {
			log.debug(this.getClass().getName() + " - [registerJob] - "
					+ "Entry");
			PreparedStatement stmt = null;
			ResultSet res = null;
			String programName = null;
			String jobId = jobInfoObj.getJobId();
			Integer schedulerId = jobInfoObj.getSchedulerId();
			try {
				conn = ConnectionManager.getInstance().databaseCon();
				stmt = conn.prepareStatement("Select PROGRAM_NAME from S_JOB where JOB_ID=?");
				stmt.setString(1, jobId);
				res = stmt.executeQuery();
				res.next();
				programName = res.getString(1);

			} catch (Exception e) {
				log.error(this.getClass().getName()
						+ " - Exception Catched in [registerJob] method : - "
						+ e.getMessage());
			} finally {
				//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
				JDBCCloser.close(res, stmt, null, null);
				try {
					if ((conn != null) && (!conn.isClosed()))
						ConnectionManager.getInstance().retrunConnection(conn);

				} catch (SQLException e) {
					log.error("org.swallow.batchScheduler.SwtJobScheduler - [registerJob] - exception :  " + e.getMessage());
				}
			}
			List arraylist = new ArrayList();
			Class className = Class.forName(programName);

			JobDetailImpl job = new JobDetailImpl(jobInfoObj.getSchedulerId() + JOB_NAME, GROUP_NAME,
					className);

			_schedularObj.getContext().put(job.getName(), jobInfoObj.getSchedulerId());
			_schedularObj.getContext().put(jobInfoObj.getSchedulerId() + "_jobStatus",
					jobInfoObj.getJobStatus());
			//_schedularObj.getContext().put("schedulerId",jobInfoObj.getSchedulerId());

			Date startDate = new Date(jobInfoObj.startingDate.getTime());
			Date endDate = new Date(jobInfoObj.endingDate.getTime());

			StringTokenizer st = new StringTokenizer(jobInfoObj.startingTime,
					":");
			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			int hrs = Integer.parseInt(arraylist.get(0).toString());
			int minutes = Integer.parseInt(arraylist.get(1).toString());
			startDate.setHours(hrs);
			startDate.setMinutes(minutes);
			arraylist.clear();

			st = new StringTokenizer(jobInfoObj.endingTime, ":");
			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			hrs = Integer.parseInt(arraylist.get(0).toString());
			minutes = Integer.parseInt(arraylist.get(1).toString());
			endDate.setHours(hrs);
			endDate.setMinutes(minutes);
			arraylist.clear();

			if (jobInfoObj.jobType.equalsIgnoreCase(CYCLE_DURATION)) {

				try {
					String expression = new String();

					int durationHours = jobInfoObj.getDurationHours();
					int durationMins = jobInfoObj.getDurationMins();
					int durationSecs = jobInfoObj.getDurationSecs();

					jobInfoObj.startingDate.setTime(startDate.getTime() + 2000);

					
					if(jobInfoObj.nextExecutionTime != null && jobInfoObj.nextExecutionTime.after(jobInfoObj.startingDate))
						startDate = new Date(jobInfoObj.nextExecutionTime.getTime());
					else 
						startDate = new Date(jobInfoObj.startingDate.getTime());
								
					GregorianCalendar cal = new GregorianCalendar();
					cal.setTime(startDate);
					long repeatInterval = getCycleInterval(durationHours,
							durationMins, durationSecs);

					SimpleTriggerImpl crontrigger = new SimpleTriggerImpl(schedulerId
							+ TRIGGER_NAME, GROUP_NAME, schedulerId + JOB_NAME,
							GROUP_NAME, cal.getTime(), endDate, 1,
							repeatInterval);

					crontrigger.setRepeatCount(crontrigger
							.computeNumTimesFiredBetween(startDate, endDate));

					if (_schedularObj
							.getJobDetail(new JobKey(schedulerId + JOB_NAME, GROUP_NAME)) != null) {
						_schedularObj.rescheduleJob(new TriggerKey(schedulerId + JOB_NAME,
								GROUP_NAME), crontrigger);
					} else {
						triggerHashTable.put(schedulerId, crontrigger);
						jobHastTable.put(schedulerId, job);
						_schedularObj.scheduleJob(job, crontrigger);
					}

					initJobEntry(jobInfoObj, crontrigger);

				} catch (ArithmeticException ae) {
					ae.printStackTrace();
					log
							.error(this.getClass().getName()
									+ " - Exception Catched in [registerJob] method : - "
									+ ae.getMessage());
				}
			} else if (jobInfoObj.jobType.equalsIgnoreCase(ONCE)) {

				if (jobInfoObj.scheduleTime.lastIndexOf(":") < 0) {
					jobInfoObj.scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(jobInfoObj.scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());

				Date schedule_date = jobInfoObj.scheduleDate;
				int year = schedule_date.getYear();
				year += 1900;
				int month = schedule_date.getMonth() + 1;
				int dayOfMonth = schedule_date.getDate();

				String expression = 0 + " " + minutes + " " + hrs + " "
						+ dayOfMonth + " " + month + " ? " + year;

				CronTriggerImpl crontrigger = new CronTriggerImpl(jobInfoObj.getSchedulerId()
						+ TRIGGER_NAME, GROUP_NAME, expression);

				triggerHashTable.put(schedulerId, crontrigger);
				jobHastTable.put(schedulerId, job);
				_schedularObj.scheduleJob(job, crontrigger);
				arraylist.clear();
				initJobEntry(jobInfoObj, crontrigger);
			} else if (jobInfoObj.jobType.equalsIgnoreCase(WEEKLY)) {

				if (jobInfoObj.scheduleTime.lastIndexOf(":") < 0) {
					jobInfoObj.scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(jobInfoObj.scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());

				String expression = 0 + " " + minutes + " " + hrs + " " + "? "
						+ "* ";
				String day = jobInfoObj.scheduleDay;
				expression = expression + day;

				if(jobInfoObj.nextExecutionTime != null && jobInfoObj.nextExecutionTime.after(jobInfoObj.startingDate))
					startDate = new Date(jobInfoObj.nextExecutionTime.getTime());
				
				CronTriggerImpl crontrigger = new CronTriggerImpl(jobInfoObj.getSchedulerId()
						+ TRIGGER_NAME, GROUP_NAME,
						jobInfoObj.getSchedulerId() + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);

				triggerHashTable.put(schedulerId, crontrigger);
				jobHastTable.put(schedulerId, job);
				_schedularObj.scheduleJob(job, crontrigger);
				initJobEntry(jobInfoObj, crontrigger);
			} else if (jobInfoObj.jobType.equalsIgnoreCase(MONTHLY)) {

				String monthlyArray = new String();

				if (jobInfoObj.scheduleTime.lastIndexOf(":") < 0) {
					jobInfoObj.scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(jobInfoObj.scheduleTime, ":");
				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());

				String expression = 0 + " " + minutes + " " + hrs + " ";
				int monthlyArraysize = monthlyArray.length();
				if (jobInfoObj.monthFirst != null) {
					if (jobInfoObj.monthFirst.equalsIgnoreCase("Y")
							&& (monthlyArraysize == 0)) {
						monthlyArray = monthlyArray + 1;
						monthlyArraysize = monthlyArray.length();
					}

				}

				if ((jobInfoObj.monthDate > 0) && (monthlyArraysize == 0)) {
					monthlyArray = monthlyArray + jobInfoObj.monthDate;
					monthlyArraysize = monthlyArray.length();
					jobInfoObj.monthDate = 0;
				}

				if ((jobInfoObj.monthDate > 0) && (monthlyArraysize > 0)) {
					monthlyArray = monthlyArray + "," + jobInfoObj.monthDate;
					monthlyArraysize = monthlyArray.length();
				}

				if (jobInfoObj.monthLast != null) {
					if (jobInfoObj.monthLast.equalsIgnoreCase("Y")
							&& (monthlyArraysize == 0)) {
						monthlyArray = monthlyArray + "L";
						jobInfoObj.monthLast = "N";
					}
				}

				if (jobInfoObj.monthLast != null) {
					if (jobInfoObj.monthLast.equalsIgnoreCase("Y")
							&& (monthlyArraysize > 0)) {
						monthlyArray = monthlyArray + ",L";
					}

				}
				expression += (monthlyArray + " * ?");

				if(jobInfoObj.nextExecutionTime != null && jobInfoObj.nextExecutionTime.after(jobInfoObj.startingDate))
					startDate = new Date(jobInfoObj.nextExecutionTime.getTime());
				
				CronTriggerImpl crontrigger = new CronTriggerImpl(schedulerId
						+ TRIGGER_NAME, GROUP_NAME,
						schedulerId + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);
				triggerHashTable.put(schedulerId, crontrigger);
				jobHastTable.put(schedulerId, job);
				_schedularObj.scheduleJob(job, crontrigger);
				initJobEntry(jobInfoObj, crontrigger);
			}

			if (jobInfoObj.jobType.equalsIgnoreCase(DAILY)) {

				if (jobInfoObj.scheduleTime.lastIndexOf(":") < 0) {
					jobInfoObj.scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(jobInfoObj.scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());

				String expression = 0 + " " + minutes + " " + hrs + " * * ?";

				if(jobInfoObj.nextExecutionTime != null && jobInfoObj.nextExecutionTime.after(jobInfoObj.startingDate))
					startDate = new Date(jobInfoObj.nextExecutionTime.getTime());
				
				CronTriggerImpl crontrigger = new CronTriggerImpl(schedulerId
						+ TRIGGER_NAME, GROUP_NAME,
						schedulerId + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);
				triggerHashTable.put(schedulerId, crontrigger);
				jobHastTable.put(schedulerId, job);
				_schedularObj.scheduleJob(job, crontrigger);
				initJobEntry(jobInfoObj, crontrigger);
			} else if (jobInfoObj.jobType.equals(SwtConstants.JOB_TYPE_MANUAL)) {

				initJobEntry(jobInfoObj, null);
			}

		} catch (SchedulerException se) {
			se.printStackTrace();

			log
					.error(this.getClass().getName()
							+ " - SchedulerException Catched in [registerJob] method : - "
							+ se.getMessage());

			jobHastTable.remove(jobInfoObj.getSchedulerId());

			throw se;

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [registerJob] method : - "
					+ e.getMessage());

			jobHastTable.remove(jobInfoObj.getSchedulerId());

			throw e;
		}
		log.debug("Exiting registerJob Method");
	}

	public synchronized void SwtModifyJob(SwtJobInfo jobInfoObj)
			throws Exception {
		log.debug(this.getClass().getName() + " - [SwtModifyJob] - " + "Entry");
		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet res = null;
		String programName = null;
		PreparedStatement ps = null;
		String hostId = CacheManager.getInstance().getHostId();
		String jobId = jobInfoObj.getJobId();
		int schedulerId = jobInfoObj.getSchedulerId();
		Date startingDate = jobInfoObj.getStartingDate();
		Date endingDate = jobInfoObj.getEndingDate();
		String startingTime = jobInfoObj.getStartingTime();
		String endingTime = jobInfoObj.getEndingTime();
		String jobType = jobInfoObj.getJobType();
		String jobStatus = jobInfoObj.getJobStatus();

		/*
		 * putting the value of the jobStatus (which can be 'E'nabled of
		 * 'D'isabled) in the context scheduler context object. This is needed
		 * in the execute() function present in SwtBasicJob.java. It is required
		 * because there, whether a job will run or not is depend on the value
		 * of this flag.
		 */
		_schedularObj.getContext().put(jobInfoObj.getSchedulerId() + "_jobStatus",
				jobInfoObj.getJobStatus());

		/*
		 * If JobStatus entered through frontEnd is 'E'nabled -- CURRENT_STATUS
		 * is set to 'P'ending else if JobStatus entered through frontEnd is
		 * 'd'isabled -- CURRENT_STATUS is set to 'D'isabled
		 */
		if ((jobStatus != null) && jobStatus.equals(SwtConstants.ADDJOB_ENABLE)) {
			jobStatus = SwtConstants.JOB_STATUS_PENDING;
		}

		if ((jobStatus != null)
				&& jobStatus.equals(SwtConstants.ADDJOB_DISABLE)) {
			try {
				conn = ConnectionManager.getInstance().databaseCon();
				stmt = conn.prepareStatement("Select current_status from  s_job_status  where SCHEDULE_ID=?");
				stmt.setInt(1, schedulerId);
				res = stmt
						.executeQuery();
				res.next();

				String current_status = res.getString(1);
				res.close();
				if ((current_status != null)
						&& current_status
								.equals(SwtConstants.JOB_STATUS_RUNNING)) {
					jobStatus = SwtConstants.JOB_STATUS_CLOSING;
				}

			} catch (SQLException e) {
				log.error(" [Database Problem " + e.getMessage());
			} finally {
				//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
				JDBCCloser.close(res, stmt, null, null);
				
				try{
				if ((conn != null) && (!conn.isClosed())) {
					ConnectionManager.getInstance().retrunConnection(conn);
				}
				} catch (SQLException e) {
					log.error("org.swallow.batchScheduler.SwtJobScheduler - [SwtModifyJob] - exception :  "
							+ e.getMessage());
				}
			}

		}

		try {
			conn = ConnectionManager.getInstance().databaseCon();
			stmt = conn.prepareStatement("Select PROGRAM_NAME from S_JOB where JOB_ID=?");
			stmt.setString(1, jobId);
			res = stmt
					.executeQuery();
			res.next();
			programName = res.getString(1);
			res.close();

			String jobName = schedulerId + JOB_NAME;
			JobDetailImpl job = null;
			Class className = Class.forName(programName);
			job = (JobDetailImpl) jobHastTable.get(schedulerId);

			ArrayList arraylist = new ArrayList();
			Date startDate = new Date(startingDate.getTime());
			Date endDate = new Date(endingDate.getTime());
			String expression = null;
			Date schedule_date;
			StringTokenizer st = new StringTokenizer(startingTime, ":");

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			int hrs = Integer.parseInt(arraylist.get(0).toString());
			int minutes = Integer.parseInt(arraylist.get(1).toString());
			startDate.setHours(hrs);
			startDate.setMinutes(minutes);
			arraylist.clear();

			st = new StringTokenizer(endingTime, ":");

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());
			hrs = Integer.parseInt(arraylist.get(0).toString());
			minutes = Integer.parseInt(arraylist.get(1).toString());
			endDate.setHours(hrs);
			endDate.setMinutes(minutes);
			arraylist.clear();

			if (jobType.equalsIgnoreCase(CYCLE_DURATION)) {

				job = new JobDetailImpl(schedulerId + JOB_NAME, GROUP_NAME, className);

				int durationHours = jobInfoObj.getDurationHours();
				int durationMins = jobInfoObj.getDurationMins();
				int durationSecs = jobInfoObj.getDurationSecs();

				boolean isDeleted = _schedularObj.deleteJob(new JobKey(jobInfoObj.getSchedulerId()
						+ JOB_NAME, GROUP_NAME));

				jobInfoObj.startingDate.setTime(startDate.getTime() + 2000);
				startDate = new Date(jobInfoObj.startingDate.getTime());
				
				GregorianCalendar cal = new GregorianCalendar();
				cal.setTime(startDate);

				long repeatInterval = getCycleInterval(durationHours,
						durationMins, durationSecs);
				
				Timestamp lastExecutionTime  = null;
				Timestamp nextExec  = null;
				if(!jobInfoObj.isRunAfterChange()){
					
					try {
						ps = conn
								.prepareCall("select LAST_EXECUTION_TIME from s_job_status where job_id= ? and host_id=? and schedule_id = ?");
	
						ps.setString(1, jobId);
						ps.setString(2, hostId);
						ps.setInt(3, schedulerId);
						res = ps.executeQuery();
						while (res.next()) {
							lastExecutionTime  = res.getTimestamp(1);
				                
						}
						if(lastExecutionTime != null)
							nextExec = new Timestamp(repeatInterval + lastExecutionTime.getTime() );
	
					} catch (SQLException e) {
						log
								.error(this.getClass().getName()
										+ " - SQLException Catched in [SwtModifyJob] method : - "
										+ e.getMessage());
					}
					
					if (lastExecutionTime!= null && nextExec != null && nextExec.before(new Date())) {
						nextExec = new Timestamp(new Date().getTime());
					}
				
				}else {
					nextExec = new Timestamp(cal.getTimeInMillis());
				}
				if(nextExec == null)
					nextExec = new Timestamp(cal.getTimeInMillis());

				jobInfoObj.setRunAfterChange(true);
				SimpleTriggerImpl crontrigger = new SimpleTriggerImpl(schedulerId
						+ TRIGGER_NAME, GROUP_NAME, schedulerId + JOB_NAME,
						GROUP_NAME, nextExec, endDate, 1, repeatInterval);

				crontrigger.setRepeatCount(crontrigger
						.computeNumTimesFiredBetween(startDate, endDate));

				triggerHashTable.remove(schedulerId);
				triggerHashTable.put(schedulerId, crontrigger);
				jobHastTable.put(schedulerId, job);
				_schedularObj.scheduleJob(job, crontrigger);
				Timestamp nextExecutionDate = new Timestamp(crontrigger
						.getNextFireTime().getTime());

				if (nextExecutionDate.before(new Date())) {
					nextExecutionDate = new Timestamp(new Date().getTime());
				}

				if(!jobInfoObj.isClusterUpdate()) {
					
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id= ? and host_id=? and schedule_id = ?";
				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
			}
			}

			if (jobType.equalsIgnoreCase(ONCE)) {
				String scheduleTime = jobInfoObj.getScheduleTime();
				arraylist.clear();

				if (scheduleTime.lastIndexOf(":") < 0) {
					scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());
				schedule_date = jobInfoObj.scheduleDate;

				int year = schedule_date.getYear();
				year += 1900;
				int month = schedule_date.getMonth() + 1;
				int dayOfMonth = schedule_date.getDate();
				expression = 0 + " " + minutes + " " + hrs + " " + dayOfMonth
						+ " " + month + " ? " + year;
				CronTriggerImpl crontrg = new CronTriggerImpl(schedulerId + TRIGGER_NAME,
						GROUP_NAME, schedulerId + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);

				Date pvousFiringTime = null;
				Object obj = (Object) triggerHashTable.remove(schedulerId);
				String classNameWithoutPath = "";

				if (obj != null) {
					classNameWithoutPath = obj.getClass().getName();
					classNameWithoutPath = classNameWithoutPath.substring(11);
				}
				if ((obj != null) && classNameWithoutPath.equals("CronTrigger")) {
					pvousFiringTime = ((CronTrigger) obj).getPreviousFireTime();
				} else if ((obj != null)
						&& classNameWithoutPath.equals("SimpleTrigger")) {
					pvousFiringTime = ((SimpleTrigger) obj)
							.getPreviousFireTime();
				}

				triggerHashTable.put(schedulerId, crontrg);
				if (pvousFiringTime == null) {
					_schedularObj.rescheduleJob(new TriggerKey(crontrg.getName(), crontrg
							.getGroup()), crontrg);
				} else {
					_schedularObj.deleteJob(new JobKey(schedulerId + JOB_NAME, GROUP_NAME));

					_schedularObj.scheduleJob(job, crontrg);
				}
				arraylist.clear();
				Timestamp nextExecutionDate = null;

				if (crontrg.getNextFireTime() != null) {
					nextExecutionDate = new Timestamp(crontrg.getNextFireTime()
							.getTime());
				}
				if(!jobInfoObj.isClusterUpdate()) {
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id= ? and host_id=? and schedule_id = ?";
				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
			}

			}

			if (jobType.equalsIgnoreCase(WEEKLY)) {
				String scheduleTime = jobInfoObj.getScheduleTime();

				if (scheduleTime.lastIndexOf(":") < 0) {
					scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				String day = jobInfoObj.getScheduleDay();
				st = new StringTokenizer(scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());
				expression = 0 + " " + minutes + " " + hrs + " " + "? " + "* ";
				expression = expression + day;
				if(!jobInfoObj.isRunAfterChange()){
					startDate  = new Date();
				}else {
					Calendar cal = Calendar.getInstance();
					cal.setTime(startDate);
					cal.add(Calendar.DATE, -365);
					startDate = cal.getTime();
					
				}
				
				jobInfoObj.setRunAfterChange(true);
				
				CronTriggerImpl crontrg = new CronTriggerImpl(jobInfoObj.getSchedulerId()
						+ TRIGGER_NAME, GROUP_NAME,
						jobInfoObj.getSchedulerId() + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);
				triggerHashTable.remove(schedulerId);
				triggerHashTable.put(schedulerId, crontrg);
				_schedularObj.rescheduleJob(new TriggerKey(crontrg.getName(), crontrg
						.getGroup()), crontrg);
				arraylist.clear();

				Timestamp nextExecutionDate = new Timestamp(crontrg
						.getNextFireTime().getTime());

				if(!jobInfoObj.isClusterUpdate()) {
					
				
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id=? and host_id=? and schedule_id = ?";

				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
				
				}
			}

			// 4.trigger for the Monthly duration
			if (jobType.equalsIgnoreCase(MONTHLY)) {
				String scheduleTime = jobInfoObj.getScheduleTime();

				if (scheduleTime.lastIndexOf(":") < 0) {
					scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				int monthDate = jobInfoObj.getMonthDate();
				String monthlyArray = new String();
				st = new StringTokenizer(scheduleTime, ":");

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());
				expression = 0 + " " + minutes + " " + hrs + " ";

				
				if(!jobInfoObj.isRunAfterChange()){
					startDate  = new Date();
				}else {
					Calendar cal = Calendar.getInstance();
					cal.setTime(startDate);
					cal.add(Calendar.DATE, -365);
					startDate = cal.getTime();
					
				}
				jobInfoObj.setRunAfterChange(true);
				
				int monthlyArraysize = monthlyArray.length();

				if (jobInfoObj.monthFirst.equalsIgnoreCase("Y")
						&& (monthlyArraysize == 0)) {
					monthlyArray = monthlyArray + 1;
					monthlyArraysize = monthlyArray.length();
				}
				if ((jobInfoObj.monthDate > 0) && (monthlyArraysize == 0)) {
					monthlyArray = monthlyArray + jobInfoObj.monthDate;
					monthlyArraysize = monthlyArray.length();
					jobInfoObj.monthDate = 0;
				}

				if ((jobInfoObj.monthDate > 0) && (monthlyArraysize > 0)) {
					monthlyArray = monthlyArray + "," + jobInfoObj.monthDate;
					monthlyArraysize = monthlyArray.length();
				}
				if (jobInfoObj.monthLast.equalsIgnoreCase("Y")
						&& (monthlyArraysize == 0)) {
					monthlyArray = monthlyArray + "L";
					jobInfoObj.monthLast = "N";
				}
				if (jobInfoObj.monthLast.equalsIgnoreCase("Y")
						&& (monthlyArraysize > 0)) {
					monthlyArray = monthlyArray + ",L";
				}
				expression += (monthlyArray + " * ?");
				CronTriggerImpl crontrg = new CronTriggerImpl(schedulerId + TRIGGER_NAME,
						GROUP_NAME, schedulerId + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);

				triggerHashTable.remove(schedulerId);
				triggerHashTable.put(schedulerId, crontrg);
				_schedularObj.rescheduleJob(new TriggerKey(crontrg.getName(), crontrg
						.getGroup()), crontrg);
				arraylist.clear();

				Timestamp nextExecutionDate = new Timestamp(crontrg
						.getNextFireTime().getTime());
				if(!jobInfoObj.isClusterUpdate()) {
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id=? and host_id=? and schedule_id = ? ";
				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
			}
			}

			if (jobType.equalsIgnoreCase(DAILY)) {
				String scheduleTime = jobInfoObj.getScheduleTime();

				if (scheduleTime.lastIndexOf(":") < 0) {
					scheduleTime = jobInfoObj.scheduleTime + ":0";
				}

				st = new StringTokenizer(scheduleTime, ":");
				arraylist.clear();

				while (st.hasMoreTokens())
					arraylist.add(st.nextToken());

				hrs = Integer.parseInt(arraylist.get(0).toString());
				minutes = Integer.parseInt(arraylist.get(1).toString());
				expression = 0 + " " + minutes + " " + hrs + " * * ?";
				
				if(!jobInfoObj.isRunAfterChange()){
					startDate  = new Date();
				}else {
					Calendar cal = Calendar.getInstance();
					cal.setTime(startDate);
					cal.add(Calendar.DATE, -365);
					startDate = cal.getTime();
					
				}
				
				jobInfoObj.setRunAfterChange(true);
				
				CronTriggerImpl crontrg = new CronTriggerImpl(schedulerId + TRIGGER_NAME,
						GROUP_NAME, schedulerId + JOB_NAME, GROUP_NAME, startDate,
						endDate, expression);
				triggerHashTable.remove(schedulerId);
				triggerHashTable.put(schedulerId, crontrg);
				_schedularObj.rescheduleJob(new TriggerKey(crontrg.getName(), crontrg
						.getGroup()), crontrg);
				arraylist.clear();

				Timestamp nextExecutionDate = new Timestamp(crontrg
						.getNextFireTime().getTime());
				if(!jobInfoObj.isClusterUpdate()) {
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id=? and host_id=? and schedule_id=?";
				try {
					ps = conn.prepareStatement(queryInsert);
					ps.setTimestamp(1, nextExecutionDate);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
				}
			} else if (jobType.equalsIgnoreCase(SwtConstants.JOB_TYPE_MANUAL)) {

				boolean isDeleted = _schedularObj.deleteJob(new JobKey(schedulerId
						+ JOB_NAME, GROUP_NAME));

				triggerHashTable.remove(schedulerId);
				if(!jobInfoObj.isClusterUpdate()) {
				String queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME=?, CURRENT_STATUS=? where job_id=? and host_id=? and schedule_id = ? ";

				try {
					ps = conn.prepareStatement(queryInsert);

					ps.setTimestamp(1, null);
					ps.setString(2, jobStatus);
					ps.setString(3, jobId);
					ps.setString(4, hostId);
					ps.setInt(5, schedulerId);
					ps.executeUpdate();
					conn.commit();
					_schedularObj.getContext().put("jobStatus", jobStatus);
				} catch (SQLException e) {
					log
							.error(this.getClass().getName()
									+ " - SQLException Catched in [SwtModifyJob] method : - "
									+ e.getMessage());
				}
			}
			}
		} catch (SchedulerException se) {
			log
					.error(this.getClass().getName()
							+ " - SchedulerException Catched in [SwtModifyJob] method : - "
							+ se.getMessage());
			throw se;
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - SchedulerException Catched in [SwtModifyJob] method : - "
							+ e.getMessage());

			throw e;
		}

		finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(ps);
			JDBCCloser.close(res, stmt, null, null);

			try{
				if ((conn != null) && (!conn.isClosed()))
					ConnectionManager.getInstance().retrunConnection(conn);

			} catch (SQLException e) {
				log.error("org.swallow.batchScheduler.SwtJobScheduler - [SwtModifyJob] - exception :  "
						+ e.getMessage());
			}
		}
		log.debug("Exiting SwtModifyJob Method");
	}

	public void pauseJob(SwtJobInfo jobInfoObj) throws Exception {
		JobDetailImpl job = (JobDetailImpl) jobHastTable.get(jobInfoObj.getSchedulerId());
		_schedularObj.pauseJob(new JobKey(job.getName(), job.getGroup()));
		log.debug("Exiting  pauseJob Method");
	}

	public synchronized void killJob(SwtJobInfo jobInfoObj, String remove)
			throws Exception {
		log.debug("Entering in killJob Method");
		Connection conn = null;

		PreparedStatement stmt = null;

		try {
			if(!jobInfoObj.isClusterUpdate()) {
				
			conn = ConnectionManager.getInstance().databaseCon();


			String hostId = CacheManager.getInstance().getHostId();
			String queryInsert = new String();

			if (remove.equalsIgnoreCase("T")) {
				queryInsert = "update s_job_status "
						+ "set NEXT_EXECUTION_TIME='' where job_id=? and host_id=? and schedule_id = ? ";
			} else {
				queryInsert = "Delete from S_JOB_STATUS where job_id=? and host_id=? and schedule_id = ? ";
			}
			stmt = conn.prepareStatement(queryInsert);
			stmt.setString(1, jobInfoObj.jobId);
			stmt.setString(2, hostId);
			stmt.setInt(3, jobInfoObj.getSchedulerId());
			stmt.executeUpdate();
			conn.commit();
			}
			String jobName = jobInfoObj.getSchedulerId() + JOB_NAME;
			boolean isDeleted = _schedularObj.deleteJob(new JobKey(jobName, GROUP_NAME));
			jobHastTable.remove(jobInfoObj.getSchedulerId());
			triggerHashTable.remove(jobInfoObj.getSchedulerId());
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - SchedulerException Catched in [killJob] method : - "
					+ e.getMessage());
			throw e;
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(stmt);

			try{
			if ((conn != null) && (!conn.isClosed()))
				ConnectionManager.getInstance().retrunConnection(conn);
			} catch (SQLException e) {
				log.error("org.swallow.batchScheduler.SwtJobScheduler - [killJob] - exception :  " + e.getMessage());
			}
		}
		log.debug("Exiting in killJob Method");
	}

	public void runImmediate(SwtJobInfo jobInfoObj) throws Exception {

		log.debug("[ SwtJobScheduler.java ]"
				+ " [ Entering in runImmediate Method ] ");

		Connection conn = null;
		PreparedStatement stmt = null;
		ResultSet res = null;
		String programName = null;

//		String jobnames[] = _schedularObj.getJobNames(GROUP_NAME);

		try {
			conn = ConnectionManager.getInstance().databaseCon();
			stmt = conn.prepareStatement("Select PROGRAM_NAME from S_JOB where JOB_ID=?");
			stmt.setString(1, jobInfoObj.jobId); 
			res = stmt.executeQuery();
			res.next();
			programName = res.getString(1);
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - SchedulerException Catched in [runImmediate] method : - "
							+ e.getMessage());
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(res, stmt, null, null);

			try{
			if ((conn != null) && (!conn.isClosed()))
				ConnectionManager.getInstance().retrunConnection(conn);
			} catch (SQLException e) {
				log.error("org.swallow.batchScheduler.SwtJobScheduler - [runImmediate] - exception :  " + e.getMessage());
			}
		}

		Class runImmediateClassName = Class.forName(programName);

		JobDetailImpl runImmediateJob = new JobDetailImpl(jobInfoObj.getSchedulerId() + JOB_NAME
				+ "_IMD", GROUP_NAME, runImmediateClassName);

		SimpleTrigger runImmediateTrigger = new SimpleTriggerImpl(jobInfoObj.getSchedulerId()
				+ TRIGGER_NAME + "_IMD", GROUP_NAME, new Date());

		_schedularObj.getContext().put(runImmediateJob.getName(),
				jobInfoObj.getSchedulerId());
		_schedularObj.getContext().put(jobInfoObj.getSchedulerId() + "_jobStatus",
				SwtConstants.ADDJOB_ENABLE);
		_schedularObj.getContext().put("userId", jobInfoObj.getUserId());
		_schedularObj.scheduleJob(runImmediateJob, runImmediateTrigger);

		log.debug("Exiting runImmediate Method");
	}

	// added for the scheduler object passed from the front end
	public void addUpdate(org.swallow.control.model.Scheduler scheduler,
			String flag) throws Exception {

		log.debug(this.getClass().getName() + " - [addUpdate] - " + "Entry");

		SwtJobInfo infoObj = new SwtJobInfo();
		infoObj.jobType = scheduler.getJobType();
		infoObj.startingDate = scheduler.getStartingDate();
		infoObj.endingDate = scheduler.getEndingDate();
		infoObj.startingTime = scheduler.getStartingTime();
		infoObj.endingTime = scheduler.getEndingTime();

		infoObj.jobId = scheduler.getJobId();
		infoObj.hostId = scheduler.getHostId();
		infoObj.setJobStatus(scheduler.getJobStatus());
		if (infoObj.jobType.equalsIgnoreCase(CYCLE_DURATION)) {

			infoObj.durationHours = scheduler.getDurationHours().intValue();
			infoObj.durationMins = scheduler.getDurationMins().intValue();
			infoObj.durationSecs = scheduler.getDurationSecs().intValue();

		}

		if (infoObj.jobType.equalsIgnoreCase(ONCE)) {
			infoObj.scheduleTime = scheduler.getScheduleTime();
			infoObj.scheduleDate = scheduler.getScheduleDate();
		}

		if (infoObj.jobType.equalsIgnoreCase(WEEKLY)) {
			infoObj.scheduleTime = scheduler.getScheduleTime();
			infoObj.scheduleDay = scheduler.getScheduleDay();
		}

		if (infoObj.jobType.equalsIgnoreCase(MONTHLY)) {
			infoObj.scheduleTime = scheduler.getScheduleTime();

			if ((scheduler.getMonthDateAsString() != null)
					&& (scheduler.getMonthDateAsString().length() > 0)) {
				infoObj.monthDate = Integer.parseInt(scheduler
						.getMonthDateAsString());
			} else {
				infoObj.monthDate = 0;
			}
			infoObj.monthFirst = (scheduler.getMonthFirst() == null) ? ""
					: scheduler.getMonthFirst();
			infoObj.monthLast = (scheduler.getMonthLast() == null) ? ""
					: scheduler.getMonthLast();
		}

		if (infoObj.jobType.equalsIgnoreCase(DAILY)) {
			infoObj.scheduleTime = scheduler.getScheduleTime();
		}
		
		if(scheduler.getScheduleId() != null) {
			infoObj.setSchedulerId(scheduler.getScheduleId());
		}
		//
		infoObj.setClusterUpdate(scheduler.isClusterUpdate());
		//
		infoObj.setRunAfterChange(scheduler.isRunAfterChange());
		try {

			if (flag.equalsIgnoreCase("A")) {

				if (jobHastTable.get(scheduler.getScheduleId()) != null) {
					throw new SwtException("scheduler.recordExists", "N");
				} else {
					registerJob(infoObj);
				}

			} else {
				SwtModifyJob(infoObj);
			}
		}

		catch (Exception e1) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [addUpdate] method : - "
					+ e1.getMessage());

			throw e1;
		}

		log.debug("Exiting 'addUpdate' Method");
	}

	private String getCycleExpression(Date srtDate, int hours, int mins,
			int secs) {
		log.debug("Entering in getCycleExpression Method");
		String expr = "";
		GregorianCalendar cal = new GregorianCalendar();
		GregorianCalendar calOrig = new GregorianCalendar();
		cal.setTime(srtDate);
		calOrig.setTime(srtDate);
		cal.add(Calendar.HOUR_OF_DAY, hours);
		cal.add(Calendar.MINUTE, mins);
		cal.add(Calendar.SECOND, secs);

		long seconds = (hours * 3600) + (mins * 60) + secs;
		if (secs == 0) {
			expr += "0";
		} else {
			expr += (cal.get(Calendar.SECOND) + "/" + cal.get(Calendar.SECOND));
		}

		if (mins == 0) {
			expr += " 0";
		} else {
			expr += (" " + cal.get(Calendar.MINUTE) + "/" + cal
					.get(Calendar.MINUTE));
		}

		if (hours == 0) {
			expr += " 0";
		} else {
			expr += (" " + cal.get(Calendar.HOUR_OF_DAY) + "/" + cal
					.get(Calendar.HOUR_OF_DAY));
		}

		if (cal.get(Calendar.DAY_OF_MONTH) == calOrig
				.get(Calendar.DAY_OF_MONTH)) {
			expr += " *";
		} else {
			expr += (" " + cal.get(Calendar.DAY_OF_MONTH) + "/" + cal
					.get(Calendar.DAY_OF_MONTH));
		}

		expr += " * ?";
		log.debug("Exiting  getCycleExpression Method");
		return expr;
	}

	private long getCycleInterval(int hours, int mins, int secs) {
		long milisecs = ((hours * 3600) + (mins * 60) + secs) * 1000;
		return milisecs;
	}

	private int getRepeatCount(Date srtDate, Date endDate, int hours, int mins,
			int secs) {
		return 0;
	}

}
