package org.swallow.batchScheduler;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.swallow.control.model.ConnectionPool;
import org.swallow.control.service.ConnectionPoolControlManager;
import org.swallow.exception.SwtException;
import org.swallow.util.pcm.SwtPCDataSource;
import org.swallow.util.ApplicationContextListner;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtDataSource;
import org.swallow.util.SwtUtil;

public class ConnectionPoolMonitorJob implements Job {
	public static int countNumberPredict = 0;
	public static int countNumberPCM = 0;
	public static int numRefBeforeUpdate = 0;
	private static boolean lastActiveConnectionUpdatedPredict = true;
	private static boolean lastActiveConnectionUpdatedPCM = true;
	public static String MODULE_ID = "ModuleId";
	private String moduleId = "";
	/** The log variable is used for store the log object. */
	private static final Log log = LogFactory.getLog(ApplicationContextListner.class);
	static {
		long connectionPoolRefreshRate = 10000;
		String poolStatsRateInMinutesAsString = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.POOLSTATS_REFRESH_RATE_IN_MINUTES);

		Long poolStatsRate = Long.parseLong(poolStatsRateInMinutesAsString);
		if (poolStatsRate != null) {
			poolStatsRate = poolStatsRate * 60 * 1000;
		}
		numRefBeforeUpdate = Math.round(poolStatsRate / connectionPoolRefreshRate);
	}

	public void execute(JobExecutionContext context) throws JobExecutionException {
		// get param
		JobDetail job = context.getJobDetail();
		JobDataMap jdm = job.getJobDataMap();
		if (jdm.containsKey(MODULE_ID)) {
			moduleId = (String) jdm.get(MODULE_ID);
		} else {
			return;
		}
		try {
			ArrayList<ConnectionPool> sessionItems = null;
			ConnectionPoolControlManager connectionPoolControlManager = (ConnectionPoolControlManager) (SwtUtil
					.getBean("connectionPoolControlManager"));
			if ("PREDICT".equalsIgnoreCase(moduleId)) {
				countNumberPredict++;
				sessionItems = connectionPoolControlManager.getConnectionPoolListByModule("PREDICT");
				if (lastActiveConnectionUpdatedPredict) {
					lastActiveConnectionUpdatedPredict = false;
					try {
						((SwtDataSource) SwtDataSource.getInstance()).removeConnectionsByIds(sessionItems, false);
						lastActiveConnectionUpdatedPredict = true;
					} catch (Exception e) {
						lastActiveConnectionUpdatedPredict = true;
						((SwtDataSource) SwtDataSource.getInstance()).refreshActiveConnectionsList();
					}
					lastActiveConnectionUpdatedPredict = true;
				} else {
					lastActiveConnectionUpdatedPredict = true;
					((SwtDataSource) SwtDataSource.getInstance()).removeConnectionsByIds(sessionItems, true);
//				((SwtDataSource) SwtDataSource.getInstance()).refreshActiveConnectionsList();
				}
				if(countNumberPredict == numRefBeforeUpdate) {
					HashMap<String, String> stats =  ((SwtDataSource) SwtDataSource.getInstance()).getLastConnectionPoolStats();
					updatePoolStatTable(stats, "PREDICT");
					countNumberPredict = 0;
				}

			} else if ("PCM".equalsIgnoreCase(moduleId)) {
				countNumberPCM++;
				sessionItems = connectionPoolControlManager.getConnectionPoolListByModule("PCM");
				if (lastActiveConnectionUpdatedPCM) {
					lastActiveConnectionUpdatedPCM = false;
					try {
						// Class params[] = {ArrayList.class, Boolean.class};
						// SwtUtil.invokeStaticMethodByName("org.swallow.util.pcm.SwtPCDataSource","removeConnectionsByIds",
						// true, params, sessionItems,false);
						((SwtPCDataSource) SwtPCDataSource.getInstance()).removeConnectionsByIds(sessionItems, false);
						// ((SwtPCDataSource)
						// SwtPCDataSource.getInstance()).removeConnectionsByIds(sessionItems);
						lastActiveConnectionUpdatedPCM = true;
					} catch (Exception e) {
						lastActiveConnectionUpdatedPCM = true;
						// Class noParams[] = {};
						// SwtUtil.invokeStaticMethodByName("org.swallow.util.pcm.SwtPCDataSource","refreshActiveConnectionsList",
						// true, noParams);
						((SwtPCDataSource) SwtPCDataSource.getInstance()).refreshActiveConnectionsList();
					}
				} else {
					lastActiveConnectionUpdatedPCM = true;
					// Class params[] = {ArrayList.class, Boolean.class};
					// SwtUtil.invokeStaticMethodByName("org.swallow.util.pcm.SwtPCDataSource","removeConnectionsByIds",
					// true, params, sessionItems,true);
					((SwtPCDataSource) SwtPCDataSource.getInstance()).removeConnectionsByIds(sessionItems, true);
				}
				
				if(countNumberPCM == numRefBeforeUpdate) {
//						Class noParams[] = {};
//						stats = (HashMap<String, String>) SwtUtil.invokeStaticMethodByName("org.swallow.util.pcm.SwtPCDataSource","getLastConnectionPoolStats", true, noParams);
					HashMap<String, String> stats = ((SwtPCDataSource) SwtPCDataSource.getInstance()).getLastConnectionPoolStats();
					updatePoolStatTable(stats, "PCM");
					countNumberPCM = 0;
				}
			}

		} catch (Exception ex) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [ConnectionPoolMonitorJob.executeJob] method : - moduleId = "+moduleId
					+ ex.getMessage());
		}

	}

	private void updatePoolStatTable(HashMap<String, String> stats, String moduleId) {
		String temStr = null;
		int numActive = 0;
		int numIdle = 0;
		int maxIdle = 0;
		int maxActive = 0;
		try {
			ConnectionPoolControlManager connectionPoolControlManager = (ConnectionPoolControlManager) (SwtUtil
					.getBean("connectionPoolControlManager"));
			if (stats != null) {
				temStr = stats.get(SwtConstants.CONNECTION_POOL_ACTIVE_ID);
				if (temStr != null) {
					numActive = Integer.parseInt(temStr.split("/")[0]);
					maxActive = Integer.parseInt(temStr.split("/")[1]);
				}
				temStr = stats.get(SwtConstants.CONNECTION_POOL_IDLE_ID);
				if (temStr != null) {
					numIdle = Integer.parseInt(temStr.split("/")[0]);
					maxIdle = Integer.parseInt(temStr.split("/")[1]);
				}

				connectionPoolControlManager.updatePoolStatsTable(moduleId, numActive, numIdle, maxActive, maxIdle);

			}
		} catch (SwtException e) {
			log.error(this.getClass().getName() + "- [updatePoolStatTable] - Exception " + e.getMessage());
		}
	}

}
