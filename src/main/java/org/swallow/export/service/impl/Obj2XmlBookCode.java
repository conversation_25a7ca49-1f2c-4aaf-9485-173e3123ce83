/*
 * @(#)Obj2XmlBookCode.java 1.0 ,10/10/10
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.util.ArrayList;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.model.ExportObject;

/**
 * Obj2XmlBookCode.java
 * 
 * This class is used to form the whole data in XML format.
 */
public class Obj2XmlBookCode extends Obj2XmlImpl {
	public Obj2XmlBookCode() {

	}

	private final Log log = LogFactory.getLog(Obj2XmlBookCode.class);

	/* Used to form the whole data in XML format */
	public String convertObj(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList rowData) {
		String rtn = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<data>\n";

		rtn += getColumnData(columnData);
		rtn += getRowData(rowData);
		rtn += getFilterData(filterData);

		rtn += "</data>\n";

		return rtn;
	}

	/* Used to get the total data and form in XML format */
	private String getRowData(ArrayList rowData) {
		String rtn = null;
		BookCode bookCode = null;
		try {
			rtn = "<rows>\n";
			for (int i = 0; i < rowData.size(); i++) {
				bookCode = (BookCode) rowData.get(i);
				rtn += "<row>\n";
				rtn += "<book>"
						+ (bookCode.getId().getBookCode() == null ? " "
								: bookCode.getId().getBookCode() + " ")
						+ "</book>\n";
				/*
				 * Start:code modified by venkat on 16_mar_2011 for Mantis
				 * 1385:"Support for symbolic characters in book, group and
				 * metagroup names."
				 */
				/*
				 * The term CDATA is used about Name data that should not be
				 * parsed by the XML parser.
				 */
				rtn += "<name><![CDATA["
						+ (bookCode.getBookName() == null ? " " : bookCode
								.getBookName()
								+ " ") + "]]></name>\n";
				/*
				 * End:code modified by venkat on 16_mar_2011 for Mantis
				 * 1385:"Support for symbolic characters in book, group and
				 * metagroup names."
				 */
				rtn += "<loc>"
						+ (bookCode.getBookLocation() == null ? " " : bookCode
								.getBookLocation()
								+ " ") + "</loc>\n";
				rtn += "<gl1>"
						+ (bookCode.getGroupIdLevel1() == null ? " " : bookCode
								.getGroupIdLevel1()
								+ " ") + "</gl1>\n";
				rtn += "<gl2>"
						+ (bookCode.getGroupIdLevel2() == null ? " " : bookCode
								.getGroupIdLevel2()
								+ " ") + "</gl2>\n";
				rtn += "<gl3>"
						+ (bookCode.getGroupIdLevel3() == null ? " " : bookCode
								.getGroupIdLevel3()
								+ " ") + "</gl3>\n";
				rtn += "</row>\n";

			}
			rtn += "</rows>\n";
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRowData] method : - "
					+ exp.getMessage());
		}
		return rtn;
	}

	/**
	 * Used to form the whole data in XML format and return as string
	 * 
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @return String
	 * @throws SwtException
	 */
	public ArrayList<ArrayList<ExportObject>> getExportData(ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData, ArrayList<BookCode> rowData) throws SwtException {
			/*
			 * To Generate the list of object to export
			 */
			ArrayList<ArrayList<ExportObject>> result = null;
			ArrayList<ExportObject> data  = null;
			ExportObject export  = null;
			
			// To get the BookCode from list
			BookCode bookCode = null;
			// To iterate the BookCode list
			int rowIndex;

			try {
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Enter");
				result = new ArrayList();
				// Iterates the rowData and get the BookCode object
				for (rowIndex = 0; rowIndex < rowData.size(); rowIndex++) {
					
					bookCode = (BookCode) rowData.get(rowIndex);		
					data = new ArrayList();
					
					// Sets the book code tag
					export = new ExportObject();
					export.setValue(bookCode.getId().getBookCode() == null ? "-"
									: bookCode.getId().getBookCode());
					export.setType(columnData.get(0).getType());
					export.setColumnName(columnData.get(0).getDataElement());					
					data.add(export);					
					// Sets the book name tag
					export = new ExportObject();					
					export.setValue(bookCode.getBookName() == null ? "-"
									: bookCode.getBookName());
					export.setType(columnData.get(1).getType());
					export.setColumnName(columnData.get(1).getDataElement());
					data.add(export);
					// Set the book location tag
					export = new ExportObject();			
					export.setValue((bookCode.getBookLocation() == null ? "-"
							: bookCode.getBookLocation()));
					export.setType(columnData.get(2).getType());
					export.setColumnName(columnData.get(2).getDataElement());
					data.add(export);
					// Set the group level 1 tag
					export = new ExportObject();			
					export.setValue((bookCode.getGroupIdLevel1() == null ? "-"
							: bookCode.getGroupIdLevel1()));
					export.setType(columnData.get(3).getType());
					export.setColumnName(columnData.get(3).getDataElement());
					data.add(export);
					// Set the group level 2 tag
					export = new ExportObject();			
					export.setValue((bookCode.getGroupIdLevel2() == null ? "-"
							: bookCode.getGroupIdLevel2()));
					export.setType(columnData.get(4).getType());
					export.setColumnName(columnData.get(4).getDataElement());
					data.add(export);
					// Set the group level 3 tag
					export = new ExportObject();			
					export.setValue((bookCode.getGroupIdLevel3() == null ? "-"
							: bookCode.getGroupIdLevel3()));
					export.setType(columnData.get(5).getType());
					export.setColumnName(columnData.get(5).getDataElement());
					data.add(export);
					
					
					//add row constructed to list
					result.add(data);
					
				}
				// Close the rows xml tag
				log.debug(this.getClass().getName() + " - [getExportData] - "
						+ "Exit");
		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ " - [getExportData] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// Nullify objects
		}
		return result;
	}

}
