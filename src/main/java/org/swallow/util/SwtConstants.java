/**
 * @(#)SwtConstants.java 1.0 12/12/2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * SwtConstants.java
 * 
 * This class has constants, which are being used in various part of this
 * application
 */
public final class SwtConstants {
	private static final Map<String, Object> constantsMap = new HashMap<>();


    public static Map<String, Object> getConstantsMap() {
    	if(constantsMap.size()==0) {
    	 Field[] fields = SwtConstants.class.getDeclaredFields();
         for (Field field : fields) {
             if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                     java.lang.reflect.Modifier.isPublic(field.getModifiers()) &&
                     java.lang.reflect.Modifier.isFinal(field.getModifiers())) {
                 try {
                     constantsMap.put(field.getName(),  field.get(null));
                 } catch (IllegalAccessException e) {
                     e.printStackTrace();
                 }
             }
         }
    	}
         
        return constantsMap;
    }
	// Spring profiles for development, test and production, 
	public static final String SPRING_PROFILE_DEVELOPMENT = "dev";
	public static final String SPRING_PROFILE_PRODUCTION = "prod";
	public static final String SPRING_PROFILE_TEST = "test";
	public static final String SPRING_PROFILE_DOCKER = "docker";
	
	public static final String ADMIN = "ADMIN";
	public static final String ADD_BUT_STS = "addButtonStatus";
	public static final String CHG_BUT_STS = "changeButtonStatus";
	public static final String DEL_BUT_STS = "deleteButtonStatus";
	public static final String SAV_BUT_STS = "saveButtonStatus";
	public static final String CAN_BUT_STS = "cancelButtonStatus";
	public static final String VIEW_BUT_STS = "viewButtonStatus";
	public static final String PREV_BUT_STS = "prevButtonStatus";
	public static final String NXT_BUT_STS = "nextButtonStatus";
	public static final String MVT_BUT_STS = "mvmntButtonStatus";
	public static final String SUSPND_BUT_STS = "suspendButtonStatus";
	public static final String CONF_BUT_STS = "confirmButtonStatus";
	public static final String REMV_BUT_STS = "removeButtonStatus";
	public static final String MATCH_BUT_STS = "matchButtonStatus";
	public static final String UNMATCH_BUT_STS = "unmatchButtonStatus";
	public static final String COPYFROM_BUT_STS = "copyfromButtonStatus";
	public static final String FIELDS_BUT_STS = "fieldsButtonStatus";
	public static final String KILL_BUT_STS = "killButtonStatus";
	public static final String REFRESH_BUT_STS = "refreshButtonStatus";
	public static final String BKCODE_BUT_STS = "bookcodeButtonStatus";
	public static final String ROLL_BUT_STS = "rollButtonStatus";

	public static final String ROLLED_STATUS = "O";
	public static final String CONFRM_STATUS = "C";
	public static final String OFFERD_STATUS = "M";
	public static final String SUSPEND_STATUS = "S";
	public static final String OUTSTANDING_STATUS = "L";
	public static final String AUTHORISE_STATUS = "A";
	public static final String REFERRED_STATUS = "R";
	public static final String PREDICT_INC = "I";
	public static final String PREDICT_EXC = "E";

	public static final String EXTRACT_INC = "I";
	public static final String EXTRACT_EXC = "E";

	public static final String INPUT_HST = "HST";

	public static final String STR_TRUE = "true";
	public static final String STR_FALSE = "false";
	public static final String PLS_SELECT_VAL = "";
	public static final String STR_NEGATIVE="negative";

	public static final String PARENT_MENU_ITEM_ID = "0";
	public static final String CDM_BEAN = "CDM";
	public static final String DATE_PAT = "datePat";
	public static final String CURRENCY_PAT = "currencyPat";

	public static final String ACTION_ADD = "A";
	public static final String ACTION_UPDATE = "U";
	public static final String ACTION_DELETE = "D";

	public static final String SWEEP_STATUS_NEW = "N";
	public static final String SWEEP_STATUS_SUBMIT = "U";
	public static final String SWEEP_STATUS_CANCEL = "C";
	public static final String SWEEP_STATUS_AUTHORIZE = "A";
	public static final String SWEEP_STATUS_STP = "S";

	public static final String SWEEP_TYPE_AUTO = "A";
	public static final String SWEEP_TYPE_MANUAL = "M";
	public static final String SWEEP_TYPE_AMENDED = "N";

	public static final String SWEEP_AlRDY_SUBMIT = "sweep.alreadySubmitted";
	public static final String SWEEP_CUTOFF_EXCEEDED = "sweep.cutoffExceeded";
	public static final String SWEEP_ACCOUNT_BREACHED = "sweep.accountLimitBreached";
	public static final String SWEEP_USERLIMIT_EXCEEDED = "sweep.userLimitExceeded";
	public static final String SWEEP_AMOUNT_CHANGED = "sweep.amountChanged";
	public static final String SWEEP_SAVED = "sweep.saveSuccessfully";
	public static final String SWEEP_SAVED_ID = "sweep.saveSuccessfullyID";
	public static final String SWEEP_NOT_SAVED = "sweep.saveError";

	public static final String PREDICT_BALANCE_PRD = "PRD";
	public static final String PREDICT_BALANCE_TYPE_M = "M";
	public static final String PREDICT_BALANCE_TYPE_S = "S";

	public static final int ENTITY_FULL_ACCESS = 0;
	public static final int ENTITY_READ_ACCESS = 1;
	public static final int ENTITY_NO_ACCESS = 2;
	public static final String POSITION_LEVEL = "POSLEVEL";
	public static final String BALANCE_TYPE = "BALANCETYPE";
	public static final String QUALITY_ACTION = "QUALITYACTION";
	public static final String ROLEOUTSTANDINGSTATUS = "Y";
	public static final String ALL_TAB = "ALL";
	public static final String SYSTEM_DATE = "sysdate";
	public static final int MENU_ENTITY_CURRENCYGRP_ACCESS = 0;
	public static final int MENU_FULL_ACCESS_ID = 0;
	/*
	 */
	public static final int TAB_COUNT = 7;
	public static final String TAB_SELECTED = "Selected";
	/*
	 */

	public static final String TOTAL_PARAMETER_COUNT = "matchQuality.TotalParameterCount";
	public static final String YES = "Y";
	public static final String NO = "N";
	public static final int MAX_DECIMALS = 7;
	public static final String INST_BASIS = "INST_BASIS";
	public static final String MANUAL_MATCH_QUALITY = "Z";
	public static final String MATCH_INTERNAL_QUALITY = "A";

	// Entries for Role Maintenance
	public static final String MENUAO_BUT_STS = "menuAOButtonStatus";
	public static final String ENTITYAL_BUT_STS = "entityALButtonStatus";
	public static final String WORKQA_BUT_STS = "workQAButtonStatus";
	public static final String SWEEPLC_BUT_STS = "sweepLCButtonStatus";
	public static final String ALERT_BOTH = "0";
	public static final String ALERT_EMAIL = "1";
	public static final String ALERT_POPUP = "2";
	public static final String ALERT_EMAIL_TEXT = "Email";
	public static final String ALERT_POPUP_TEXT = "Pop-up";
	public static final String ALERT_BOTH_TEXT = "Both";

	public static final String COPY_FROM_BUT_STS = "copyFromButtonStatus";
	public static final String NOTES_BUT_STS = "notesButtonStatus";
	public static final String LOG_BUT_STS = "logButtonStatus";
	public static final String DISPLAY_BUT_STS = "displayButtonStatus";
	public static final String SEARCH_BUT_STS = "searchButtonStatus";
	public static final String MESS_BUT_STS = "messageButtonStatus";
	public static final String CLOSE_BUT_STS = "closseButtonStatus";
	public static final String MOVEMENT_TYPE_CASH = "C";
	public static final String MOVEMENT_TYPE_SECURITIES = "U";
	public static final String PREDICT_CAN = "C";
	public static final String POSITION_LEVEL_DEFAULT = "1";

	public static final String CREDIT = "C";
	public static final String DEBIT = "D";
	public static final String ACCOUNT_ATTRIBUTE = "A";
	public static final String RULE = "R";
	public static final String ALLIGNED_CREDIT = "Credit";
	public static final String ALLIGNED_DEBIT = "Debit";
	public static final String ACCT_MAIN = "M";
	public static final String ACCT_SUB = "S";
	public static final String ACCT_MAIN_VALUE = "Main";
	public static final String ACCT_SUB_VALUE = "Sub";

	public static final String USER_ENTITY_ACCESS_LIST = "entityAccessList";
	public static final String ROLE_CURRENCY_ACCESS_LIST = "ccyGrpAccessListForRole";
	public static final String PREDICT_PROPERTIES = "/predict.properties";
	public static final String DEFAULT_PREDICT_PROPERTIES = "/default_predict.properties";
	public static final String SWIFT_MESSAGE_PROPERTIES = "/messagetype.properties";
	public static final String LICENSE_PROPERTIES = "/license.properties";
	public static final String VERSION_PROPERTIES = "/version.properties";
	public static final String XSS_FILTER_PROPERTIES="/xss_filter.properties";
	
	public static final String CONNECTION_PROPERTIES="/connection.properties";
	public static final String CONNECTION_PCM_PROPERTIES="/connection-pcm.properties";
	
	public static final String DIC_EN_PROPERTIES="/dictionary_en.properties";
	public static final String DIC_PCM_EN_PROPERTIES="/context/pcm/dictionary_en.properties";
	public static final String DIC_FR_PROPERTIES="/dictionary_fr.properties";
	public static final String DIC_PCM_FR_PROPERTIES="/context/pcm/dictionary_fr.properties";
	public static final String SWT_HOST_ID = "hostid";
	public static final String SWT_MAX_USER_ID = "Users";
	public static final String SWT_EXPIRY_DATE_ID = "ExpiryDate";

	public static final String SWT_SERVER_ID = "Hostname";
	public static final String SWT_HOST = "Host";
	public static final String LICENSE_HASHCODE = "Code";

	public static final String USER_ENABLE_STATUS = "1";
	public static final String USER_DISABLE_STATUS = "2";
	
	// Version keys
	public static final String YEARS_COPYRIGHT = "yearsCopyright";
	public static final String VERSION = "version";
	public static final String RELEASE_DATE = "releaseDate";
	public static final String COPYRIGHT = "screen.copyright";

	// Entries for Message Formats
	public static final String FORMAT_TYPE_SWEEP = "S";
	public static final String FORMAT_TYPE_OTHER = "O";
	public static final String FORMAT_TYPE_FIXED = "F";
	public static final String FORMAT_TYPE_DELIMITED = "D";
	public static final String FORMAT_TYPE_TAGGED = "T";
	public static final String FIELD_TYPE_TEXT = "T";
	public static final String FIELD_TYPE_KEYWORD = "K";
	public static final String FIELD_TYPE_HEXADECIMAL = "H";
	public static final String FIELD_TEXT = "TEXT";
	public static final String FIELD_KEYWORD = "KEYWORD";
	public static final String FIELD_HEXADECIMAL = "HEXADECIMAL";
	public static final String OUTPUT_TYPE = "F";
	public static final Integer PASSWORD_NOTICE_PERIOD = new Integer(10);

	public static final String ALL_LABEL = "All";
	public static final String ALL_VALUE = "All";
	public static final String STANDARD_VALUE = "Standard";
	public static final String MESSAGE_FIXED = "FIXED";
	public static final String MESSAGE_DELMITED = "DELIMITED";
	public static final String MESSAGE_TAGGED = "MULTI-LINE";
	public static final String MESSAGE_FILE = "FILE";
	public static final String MESSAGE_MQINTER = "MQ INTERFACE";
	public static final int NOTESLENGTHTRUNCATE = 22;
	public static final String SweepOutGoingMessage = "SENT";
	
	public static final String GROUP_HIERARCHY = "Group Hierarchy";
	public static final String ACCOUNT_MONITOR_SCREEN = "Account Monitor";

	// Constants for Reports
	public static final String REPORT_PROP_FILE_NAME = "/report.properties";
	public static final String REPORT_TYPE_TURN_OVER = "T";
	public static final String REPORT_TYPE_MATCH_STATIC = "M";
	public static final String REPORT_RPTNAME_TURN_OVER = "TurnOverStaticReport.rpt";
	public static final String REPORT_RPTNAME_MATCH_STATIC = "MatchStaticReport.rpt";
	public static final String REPORT_RPTNAME_BATCH_SWEEP = "SweepMovementReport.rpt";
	public static final String REPORT_RPTNAME_BATCH_MVMT_SMRY = "MovementSummaryMovement.rpt";
	public static final String REPORT_RPTNAME_BATCH_MATCH_CNFMD = "MovementConfirmedmatchreport.rpt";
	public static final String REPORT_PDFNAME_BATCH_SWEEP = "SWPINS";
	public static final String REPORT_PDFNAME_BATCH_MVMT_SMRY = "OUTMOV";
	public static final String REPORT_PDFNAME_BATCH_MATCH_CNFMD = "CONMAT";

	public static final String ACCOUNT_STATUS_FLAG_OPEN = "O";

	public static final String ACCOUNT_STATUS_FLAG_BLOCKED = "B";

	public static final String CURRENCY_DEFAULT_LABEL = "Default";
	public static final String POSITIONLEVE_SWIFT_IO = "N";

	public static final String ALERT_MA = "Sweep Message Exceptions";

	public static final String ALERT_PA = "Sweep to be authorized";
	public static final String ALERT_PS = "Sweep to be submitted";
	public static final String ALERT_MG = "Sweep message generated";
	public static final String ALERT_MO = "Sweep Message Overdue";

	public static final String ALERT_MT = "Manual Input to be Authorised";
	public static final String ALERT_MR = "Manual Input Referred";

	public static final String BALANCE_PREDICT = "2";
	public static final String FINAL_PREDICT = "3";
	public static final String BALANCE_PREDICT_IN = "4";
	public static final String FINAL_PREDICT_IN = "5";
	public static final String BALANCE_PREDICT_OUT = "6";
	public static final String FINAL_PREDICT_OUT = "7";

	public static final String SWEEP_NEW = "New";
	public static final String SWEEP_STP = "STP";
	public static final String SWEEP_CANCELLED = "Cancelled";
	public static final String SWEEP_SUBMITTED = "Submitted";
	public static final String SWEEP_AUTHORIZED = "Authorised";
	public static final String SWEEP_AUTO = "Auto";
	public static final String SWEEP_MANUAL = "Manual";
	public static final String NOTES_SWEEP_SESSION_OBJECT = "sweepNotesDetails";

	// job scheduler constatns
	public static final String JOB_TYPE_ONCE = "O";
	public static final String JOB_TYPE_CYLIC = "C";
	public static final String JOB_TYPE_DAILY = "D";
	public static final String JOB_TYPE_WEEKLY = "W";
	public static final String JOB_TYPE_MONTHLY = "M";
	public static final String JOB_TYPE_MANUAL = "N";
	
	public static final String JOB_TYPE_PROCESS = "P";
	public static final String JOB_TYPE_REPORT = "R";
	public static final String JOB_TYPE_BOTH = "B";

	public static final String MAP_DATE_SYSTEM = "S";
	public static final String MAP_DATE_ENTITY = "E";
	public static final String OUTPUT_FILE_TYPE_PDF = "PDF";
	public static final String OUTPUT_FILE_TYPE_XLS = "Excel";
	public static final String OUTPUT_FILE_TYPE_CSV = "CSV";
	public static final String OUTPUT_FILE_TYPE_TXT = "TXT";
	
	public static final String JOB_TYPE_ONCE_DESC = "Once";
	public static final String JOB_TYPE_CYLIC_DESC = "Cyclic";
	public static final String JOB_TYPE_DAILY_DESC = "Daily";
	public static final String JOB_TYPE_WEEKLY_DESC = "Weekly";
	public static final String JOB_TYPE_MONTHLY_DESC = "Monthly";
	public static final String JOB_TYPE_MANUAL_DESC = "Manual";

	public static final String ADDJOB_ENABLE = "E";
	public static final String ADDJOB_DISABLE = "D";
	// Same as present in Misc params
	public static final String JOB_STATUS_DISABLE = "Disabled";
	// Same as present in Misc params for Key1 = "Pending"
	public static final String JOB_STATUS_PENDING = "P";
	// Same as present in Misc params for Key1 = "Running"
	public static final String JOB_STATUS_RUNNING = "R";
	// Same as present in Misc params for Key1 = "Closing"
	public static final String JOB_STATUS_CLOSING = "C";

	public static final String JOB_END_DATE = "12/12/2059";

	public static final String JOB_END_TIME = "23:00";

	public static final String JOB_STATUS_CLOSING_TEXT = "Closing";
	public static final String JOB_STATUS_PENDING_TEXT = "Pending";

	// For match tabs
	public static final String DAY_TODAY = "today";
	public static final String DAY_TOMORROW = "tomorrow";
	public static final String DAY_DAYAFTER = "dayAfter";
	public static final String DAY_ALL = "All";
	public static final String LAST_RUN_STATUS = "To Run";
	public static final String LAST_NOT_RUN_STATUS = "Not Running";
	public static final String LAST_STATUS = "Running";

	// Movement Search Pagination
	public static final String MVMT_SEARCH_QUERY = "MovementSearchQuery";
	public static final String MVMT_SEARCH_QUERY_PARAM = "MovementSearchQueryParams";
	public static final String MVMT_SEARCH_QUERY_TYPE = "MovementSearchQueryTypes";

	public static final String ALL_ACCOUNTS_TYPE = "All accounts";
	public static final String CASH_ACCOUNTS_TYPE = "Cash accounts only";
	public static final String CUSTODIAN_ACCOUNTS_TYPE = "Custodian accounts only";

	// For Password/user separator
	public static final String PSWD_SEPARATOR = "^$~(";

	public static final String LOG_MVMNT = "Movement";

	public static final int PREVIOUS_LINK = -2;
	public static final int NEXT_LINK = -1;

	public static final String POSITION_LEVEL_INTERNAL = "I";
	public static final String POSITION_LEVEL_EXTERNAL = "E";
	public static final String POSITION_LEVEL_INDICATOR = "POSLVLINDICATOR";
	public static String APP_NAME = "Application_Name";

	public static final int CURRENCYGRP_FULL_ACCESS = 0;
	public static final int CURRENCYGRP_READ_ACCESS = 1;
	public static final int CURRENCYGRP_NO_ACCESS = 2;
	public static final String LAST_REQUEST_ACCESSED = "lastRequestAccessed";
	public static final String SESSION_TIMEOUT_PERIOD = "sessionTimeOutPeriod";
	public static final String MAX_SESSION_TIMEOUT_PERIOD = "maxSessionTimeOutPeriod";
	public static final String MOVEMENT_SOURCE_DEFAULT = "MANUAL";
	public static final String MOVEMENT_SOURCE_PREADVICE = "PRE-ADVICE";
	public static final String CACHE_EXPIRY_TIME = "cacheExpiryTime";
	public static final String INACTIVE_DISABLE = "InactiveDisable";
	public static final String DEFAULT_REFRESH_RATE = "defaultRefreshRate";
	public static final String DEFAULT_REFRESH_RATE_BREAKDOWN = "defaultRefreshRateBreakDownMonitor";
	public static final String SESSION_VALIDATION_TIMEOUT_PERIOD = "sessionValidationTimeOutPeriod";
	public static final String POOLSTATS_REFRESH_RATE_IN_MINUTES = "poolStatsRefreshRate.inMinutes";

	// SwtCommon.properties
	public static final String CURRENCY_MONITOR_ID = "1";
	public static final String ACCOUNT_MONITOR_ID = "2";

	public static final String ACCOUNT_BREAKDOWN_MONITOR_ID = "4";
	public static final String WORKFLOW_MONITOR_ID = "5";
	public static final String ALERT_INSTANCE_SUMMARY_ID = "250";
	public static final String BOOK_MONITOR_ID = "3";
	public static final String GROUP_MONITOR_ID = "6";
	public static final String METAGROUP_MONITOR_ID = "7";
	public static final String CENTRAL_BANK_MONITOR_ID = "8";
	public static final String INTERFACEMONITOR_ID = "9";
	public static final String INTERFACEMONITOR_PCM_ID = "210";
	public static final String DASHBOARD_PCM_ID = "211";
	public static final String INPUTEXCEPTION_ID = "10";
	public static final String ILMANALYSIS_MONITOR_ID = "19";
	public static final String ILM_THOUPUT_MONITOR_ID = "26";
	public static final String ILM_THOUPUT_MONITOR_BREAKDOWN_ID = "27";
	public static final String ILM_OPTIONS_MONITOR_ID = "22";
	public static final String BAL_MAINTENANCE_ID = "27";
	public static final String SCHEDULER_SCREEN_ID = "49";
	// added for maxConcurrent User for parallel Matching
	public static final String CONCURRENT_MATCHING_PROCESS_USER = "maxConcurrentTasks";

	public static final String DATA_EXTRACTION_EXPORT_PATH = "dataExtractionExport.path";

	// Added to set the path print files
	public static final String PRINT_FILES_PATH = "application.directorypath";

	// Added to set the archive path
	public static final String ARCHIVE_DIRECTORY_PATH = "archive.directorypath";
	
	// Added to set the PCM archive path
	public static final String PCM_ARCHIVE_DIRECTORY_PATH = "pcm.archive.directorypath";

	// Added to set the archive option
	public static final String ARCHIVE_OPTION = "archive.option";
	
	// Added to set the PCM archive option
	public static final String PCM_ARCHIVE_OPTION = "pcm.archive.option";

	// input webservice endpoint url
	public static final String PROPERTY_INTERFACES_RPC_SERVICE = "interfaces.rpcService";
	
	// input webservice endpoint url for PCM
	public static final String PROPERTY_INTERFACES_RPC_SERVICE_PCM = "interfaces.rpcServicePCM";

	// interface reprocessing properties
	public static final String PROPERTY_INTERFACES_TABLE_TEMPORARY_NAME = "interfaces.table.temporary.name";
	public static final String PROPERTY_INTERFACES_TABLE_FAILED_NAME = "interfaces.table.failed.name";
	public static final String PROPERTY_INTERFACES_TABLE_FAILED_PK = "interfaces.table.failed.pk";

	public static final String ACCOUNT_TYPE_CASH = "C";
	public static final String ACCOUNT_TYPE_CUSTODIAN = "U";

	public static final String ACCT_MONITOR_BALTYPE_PREDICTED = "Predicted";
	public static final String ACCT_MONITOR_BALTYPE_UNSETTLED = "Unsettled";
	public static final String ACCT_MONITOR_BALTYPE_UNEXPECTED = "Unexpected";
	public static final String ACCT_MONITOR_BALTYPE_LORO = "Loro";
	public static final String ACCT_MONITOR_BALTYPE_EXTERNAL = "External";
	public static final String ACCT_MONITOR_BALTYPE_OPEN_UNEXPECTED = "OpenUnexpected";

	public static final String MOVEMENT_ALERT_STAGE_MT = "MT";
	public static final String MOVEMENT_ALERT_STAGE_MR = "MR";
	public static final String ALERT_STATUS_PENDING = "P";
	public static final String ALERT_STATUS_CANCLE = "C";

	// Added for displaying Total column in Open Queue Selection
	public static final String TOTAL_LABEL = "Total";

	// Added for integration of BookMonitor with Movement Summary display
	public static final String BOOKCODE_OTHERS = "OTHERS";

	public static final String EMPTY_STRING = "";

	public static final String ACCOUNTCLASS_LORO = "L";

	// Added for identifying session time out due to inactivity
	public static final String SESSION_TIME_OUT_INACTIVITY = "SessionTimeOut";

	// Added constants for screen specific Page Size
	public static final String MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE = "screenPageSize.movementSummaryDisplay";

	// Added constants for screen specific Page Size
	public static final String PARTY_SUMMARY_SCREEN_PAGE_SIZE = "screenPageSize.partySummaryDisplay";

	// Added constants for screen specific Page Size
	public static final String EXCHANGE__RATES_PAGE_SIZE = "screenPageSize.exchangeRates";

	public static final String INTERFACERULES_MAINTENANCE_PAGE_SIZE = "screenPageSize.interfaceRules";
	
	public static final String PARTY_SEARCH_PAGE_SIZE = "screenPageSize.partySearch";

	// Delimiter symbol/size for interface rules maintenance
	public static final String DELIMITER_SYMBOL = "~^";
	public static final int DELIMITER_SIZE = 2;

	// Processing flag wait time in minutes
	public static final String PROCESSINGFLAGWAITTIME_INMINUTES = "processingFlagWaitTime.inMinutes";

	// Added constants for screen specific Page Size
    public static final String BALANCE_MAINTENANCE_PAGE_SIZE = "screenPageSize.balanceMaintenance";
	// Added constant variable for sweep cancel queue page size
	public static final String SWEEP_CANCEL_QUEUE_PAGE_SIZE = "screenPageSize.sweepCancelQueue";
	// Constant value for checking (Empty) for Filters
	public static final String FILTER_STATUS_EMPTY = "(Empty)";
	// Constant value for checking (Not empty) for Filters
	public static final String FILTER_STATUS_NOT_EMPTY = "(Not empty)";
	// Added constants for screen specific Page Size
	public static final String SYSTEM_LOG_SCREEN_PAGE_SIZE = "screenPageSize.systemLog";
	
	// Added default constants for system screen Page Size
	public static final String DEFAULT_SYSTEM_SCREEN_PAGE_SIZE = "screenPageSize.systemDefault";

	public static final String RECONCILE_STATUS = "E";

	public static final String RECON_BUT_STS = "reconButtonStatus";

	public static final String FLAG_EXCLUDEDOUTSTANDING = "E";
	public static final String FLAG_WORKFLOWMONITOR = "W";

	public static final String STRTDAYBAL_IMPORT_INTERNAL = "I";
	public static final String STRTDAYBAL_IMPORT_MT950 = "E";
	public static final String STRTDAYBAL_PREDICTED = "P";
	public static final String STRTDAYBAL_ZERO = "Z";

	public static final String MANUAL = "MANUAL";
	public static final String SEVEN_TABS = "D1";
	public static final String TODAY_PLUS1 = "T1";
	public static final String TODAY_PLUS2 = "T2";
	public static final String ACCOUNT_CLASS = "ACCOUNTCLASS";

	public static final String ACCOUNT_MONITOR = "accountmonitor";
	public static final String ACCOUNT_BREAKDOWN_MONITOR = "accountbreakdownmonitor";
	public static final String CURRENCY_MONITOR = "currencymonitor";
	public static final String BOOK_MONITOR = "bookmonitor";
	public static final String EXCLUDED_OUTSTANDING = "ExOutstanding";

	public static final String OPEN_UNOPEN_BUT_STS = "openUnopenButtonStatus";
	public static final String OPEN_UNOPEN_FLAG = "openUnopenFlag";

	public static final String POSITION_LEVEL_NAME_EXTERNAL = "External";
	public static final String EXT_BAL_INC = "I";
	public static final String EXT_BAL_EXC = "E";

	public static final String CURR_MONITOR_DEFAULT_DAYS = "currMonitorDefaultDays";
	public static final String CURR_MONITOR_DEFAULT_MULTIPLIER = "None";

	public static final String INITIAL_LOCATION_ACCESS_LIST = "initialLocationDetailsListInSession";
	public static final String LOCATION_ACCESS_LIST = "locationDetailsListInSession";
	public static final String ALL_LOCATION_ACCESS_LIST = "allLocationDetailsListInSession";

	public static final String MOVEMENT_FIELD = "MOVEMENTFIELD";
	public static final String EDITABLE_DATA_LIST = "EditableDataList";
	public static final int EDITABLE_NO_CHANGE = 0;
	public static final int EDITABLE_MANUAL = 1;
	public static final int EDITABLE_ALL_MOVEMENTS = 2;

	public static final String SWEEP_PRIOR_CUT_OFF_ID = "6";
	public static final String MANUAL_SWEEP_SELECTED = "Y";

	public static final String OTHERS_VALUE = "OTHERS";

	public static final String ALERT_INTERVAL = "alertInterval";

	public static final String FORMAT_TYPE_TAGGED_VARIABLE = "X";
	public static final String MESSAGE_TAGGED_VARIABLE = "Multi-Line \55 Variable Fields";

	public static final int SCREEN_INPUT_EXCEPTIONS = 1;
	public static final int SCREEN_INPUT_EXCEPTIONS_PCM = 2090;
	public static final int SCREEN_INPUT_EXCEPTIONS_MESSAGES = 2;
	public static final int SCREEN_INPUT_EXCEPTIONS_MESSAGES_PCM = 2091;
	public static final int SCREEN_INPUT_EXCEPTIONS_MESSAGES_DASHBOARD = 2092;
	public static final int SCREEN_ACCOUNTMONITOR = 3;
	public static final int SCREEN_CURRENCYMONITOR = 4;
	public static final int SCREEN_INPUTCONFIGURATION = 5;
	public static final int SCREEN_INPUTCONFIGURATION_PCM = 208;
	public static final int SCREEN_INTERFACEMONITOR = 6;
	public static final int SCREEN_INTERFACEMONITOR_PCM = 210;
	public static final int SCREEN_CENTRALMONITOR = 12;
	public static final int SCREEN_SCENARIOSUMMARY = 16;
	public static final int SCREEN_WORKFLOWMONITOR = 17;
	public static final int SCREEN_ACCTGROUPDETAILS = 18;
	public static final int SCREEN_ILMANALYSISMONITOR = 19;
	public static final int SCREEN_ACCOUNT_ATTRIBUTES = 20;
	public static final int SCREEN_ACCOUNT_ATTRIBUTES_USAGE = 21;
	public static final int SCREEN_ACCOUNT_ATTRIBUTES_VALUE = 22;
	public static final int SCREEN_ACCOUNT_SPECIFIC_MAINTENANCE = 23;
	public static final int SCREEN_SCHEDULED_REPORT_HISTORY = 24;
	
	public static final int MENU_ITEM_OFFERED_QUEUE = 16;
	public static final int MENU_ITEM_SUSPENDED_QUEUE = 17;
	public static final int MENU_ITEM_CONFIRMED_QUEUE = 18;
	public static final int MENU_ITEM_MANUAL_MATCH = 19;
	public static final int MENU_ITEM_MANUAL_SWEEP = 20;
	public static final int MENU_ITEM_CURRENCY_INTERREST = 70;
	public static final int MENU_ITEM_CURRENCY_EXCHANGE = 69;
	public static final int MENU_ITEM_ACCOUNT_MAINTENANCE = 29;
	public static final int MENU_ITEM_ACCOUNT_GROUP_MAINTENANCE = 137;
	public static final int MENU_ITEM_WORKFLOW_MONITOR= 104;
	
	
	public static final int MENU_ITEM_PCM_ACCOUNT_GROUPS_MAINTENANCE = 203;
	public static final int MENU_ITEM_STOPRULE_MAINTENANCE = 204;
	public static final int MENU_ITEM_SPREAD_PROFILES_MAINTENANCE = 205;
	public static final int MENU_ITEM_CATEGORY_MAINTENANCE = 206;
	public static final int MENU_ITEM_PCM_CURRENCY_MAINTENANCE = 207;
	public static final int MENU_ITEM_PCM_DASHBOARD = 211;
	public static final int MENU_ITEM_PCM_BREKDOWN_DASHBOARD = 212;
	public static final int MENU_ITEM_PAYMENT_DISPLAY = 213;
	public static final int MENU_ITEM_PAYMENT_SEARCH= 214;
	public static final String MENU_ITEM_ALERT_SUMMARY_DISPLAY= "157";
	

	public static final String MOVEMENT_SUMMARY_DISPLAY = "10";

	public static final String BOOK_MONITORS = "9";
	public static final String GROUP_MONITORS = "8";
	public static final String METAGROUP_MONITORS = "7";

	public static final String MONITORS_TYPES = "MONITORTYPE";

	// Modified for mantis 2370 by KaisBS: The notification will have also the message type of the interface
	// Placeholders:
	//	  {1}=interfaceId (e.g. "GENERIC")
	//   {2}=messageType (e.g. " (LCH)", or NULL if not a multiple message type interface)
	//   {3}=time (e.g. "5 mins")
	public static final String INTERFACE_INTERRUPT_ALERT = "[Predict] WARNING: No message received by {1}{2} for {3}.";
	public static final String INTERFACE_INTERRUPT_ALERT_PCM = "[PCM] WARNING: No message received by {1}{2} for {3}.";
	public static final String INTERFACE_NOTIFICATION_TYPE = "Interface";
	public static final String INTERFACE_NOTIFICATION_ERRTYPE = "Alert";
	public static final String INTERFACE_NOTIFICATION_JOBID = "18";
	public static final String PCM_INTERFACE_NOTIFICATION_JOBID = "26";
	public static final String MESSAGE_NOTIFICATION_TYPE = "Message";

	public static final String USER_SYSTEM = "SYSTEM";

	//Added for Mantis 2145  : Unsettled/ Excluded Movements Reports
	public static final String EXCLUDED_MOVEMENTS_REPORT_FILE = "/jsp/reports/ExcludedMovements.jrxml";
	public static final String UNSETTLED_MOVEMENTS_FILE = "/jsp/reports/UnsettledMovements.jrxml"; 
	public static final String ILM_REPORTS_DAILY_FILE = "/jsp/reports/MasterILMReportDaily.jrxml"; 
	public static final String ILM_REPORTS_DATERANGE_FILE = "/jsp/reports/MasterILMReportDateRange.jrxml";
	public static final String SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE = "/jsp/reports/SubILMCumulativeBalanceGraph.jrxml";
	public static final String SUBREPORT_ILMREPORT_ILGROUP_DAILY_FILE = "/jsp/reports/SubILMAvailableILGroup.jrxml";
	public static final String SUBREPORT_ILMREPORT_ILGROUP_DATERANGE_FILE = "/jsp/reports/SubILMAvailableILGroupDateRange.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_A_DAILY_FILE = "/jsp/reports/SubILMAvailableILBaselA.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_A_DATERANGE_FILE = "/jsp/reports/SubILMAvailableILBaselADateRange.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_B_DAILY_FILE = "/jsp/reports/SubILMAvailableILBaselB.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_B_DATERANGE_FILE = "/jsp/reports/SubILMAvailableILBaselBDateRange.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_C_DAILY_FILE = "/jsp/reports/ILMReportBaselCDaily.jrxml";
	public static final String SUBREPORT_ILMREPORT_BASEL_C_DATERANGE_FILE = "/jsp/reports/ILMReportBaselCDateRange.jrxml";
	public static final String ILM_EXCEL_REPORTS_DATE_RANGE_TEMPLATE = "/jsp/reports/DateRange_ILM_Excel_Template.xlsx";
	public static final String ILM_EXCEL_REPORTS_SINGLE_DAY_TEMPLATE = "/jsp/reports/SingleDay_ILM_Excel_Template.xlsx";
	
	public static final String ILM_THROUGHPUT_TEMPLATE = "/jsp/reports/ILMThroughPutRatioReportMain.jrxml";
	public static final String ILM_THROUGHPUT_SUB_TEMPLATE = "/jsp/reports/ILMThroughPutRatioReportSub.jrxml";
	public static final String ILM_THROUGHPUT_SUB_GRID_CHART_TEMPLATE = "/jsp/reports/ILMThroughPutRatioReportSubGridAndChart.jrxml";
	
	public static final String INTRADAY_BALANCES_REPORT_FILE = "/jsp/reports/IntradayBalance.jrxml";
	public static final String INTRADAY_BALANCES_REPORT_FILE_GRAPH = "/jsp/reports/IntradayBalanceGraph.jrxml";

	public static final String INTEREST_CHARGES_REPORT_FILE = "/jsp/reports/InterestChargesPerAccount.jrxml";

	public static final String TURNOVER_REPORT_FILE = "/jsp/reports/TurnoverReport.jrxml";

	public static final String CURRENCY_FUNDING_REPORT_FILE = "/jsp/reports/CurrencyFunding.jrxml";
	/* START : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report: Improve performance */
	/* get the jasper object path  */
	public static final String OPPORTUNITY_COST_REPORT_FILE = "/jsp/reports/OpportunityCost.jrxml";
	public static final String OPPORTUNITY_COST_REPORT_FILE_All = "/jsp/reports/OpportunityCostAll.jrxml";
	public static final String OPPORTUNITY_COST_REPORT_XSL_CSV_FILE = "/jsp/reports/OpportunityCostXlsCSV.jrxml";
	public static final String OPPORTUNITY_COST_REPORT_XSL_CSV_FILE_All = "/jsp/reports/OpportunityCostAllXlsCSV.jrxml";
	
	
	/* End : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report: Improve performance */
	public static final String ROLE_REPORT_FILE = "/jsp/reports/RoleReport.jrxml";
	public static final String SUBREPORT_ROLE_CURRENCY_ACCESS_FILE = "/jsp/reports/subRepRoleCcyAccess.jrxml";
	public static final String SUBREPORT_ROLE_ENTITY_ACCESS_FILE = "/jsp/reports/subRepRoleEntityAccess.jrxml";
	public static final String SUBREPORT_ROLE_LOCATION_ACCESS_FILE = "/jsp/reports/subRepRoleLocationAccess.jrxml";
	public static final String SUBREPORT_ROLE_MENU_ACCESS_FILE = "/jsp/reports/subRepRoleMenuAccess.jrxml";
	public static final String SUBREPORT_ROLE_SWEEP_LIMITS_FILE = "/jsp/reports/subRepRoleSweepLimits.jrxml";
	public static final String SUBREPORT_ROLE_USERS_FILE = "/jsp/reports/subRepRoleUsers.jrxml";

	public static final String USER_REPORT_FILE = "/jsp/reports/UserReport.jrxml";

	public static final String USER_REPORT_FILE_Excel = "/jsp/reports/UserReportExcel.jrxml";
	public static final String ROLE_REPORT_FILE_EXCEL = "/jsp/reports/RoleReportExcel.jrxml";
	public static final String SUBREPORT_ROLE_CURRENCY_ACCESS_FILE_EXCEL = "/jsp/reports/subRepRoleCcyAccessExcel.jrxml";
	public static final String SUBREPORT_ROLE_ENTITY_ACCESS_FILE_EXCEL = "/jsp/reports/subRepRoleEntityAccessExcel.jrxml";
	public static final String SUBREPORT_ROLE_LOCATION_ACCESS_FILE_EXCEL = "/jsp/reports/subRepRoleLocationAccessExcel.jrxml";
	public static final String SUBREPORT_ROLE_MENU_ACCESS_FILE_EXCEL = "/jsp/reports/subRepRoleMenuAccessExcel.jrxml";
	public static final String SUBREPORT_ROLE_SWEEP_LIMITS_FILE_EXCEL = "/jsp/reports/subRepRoleSweepLimitsExcel.jrxml";
	public static final String SUBREPORT_ROLE_USERS_FILE_EXCEL = "/jsp/reports/subRepRoleUsersExcel.jrxml";

	public static final String DIFFERENT_CURRENCY = "Movement is not for the selected currency";
	public static final String NOTOUTOUTSTANDING = "Movement is not outstanding";
	public static final String DIFFERENT_VALUEDATE = "The value date should be greater than or equal to today";
	public static final String MOVEMENT_MATCH_SUMMARY_DISPLAY = "11";

	public static final String DAY_DAYBEFORE = "daybefore";

	public static final String BALTYPE_INTERNAL = "Internal";
	public static final String BALTYPE_EXTERNAL = "External";
	public static final String BALTYPE_PREDICTED = "Predicted";
	public static final String BALTYPE_ZERO = "Zero";
	public static final String BALTYPE_NONE = "None";
	public static final String BALTYPE_MANUAL = "Manual";
	public static final String Account_Access_Control = "120";
	public static final String COPY_FRM = "copyFrm";

	public static final String LABEL_PREDICTED_BAL = "label.predictedbal";
	public static final String LABEL_UNSETTLED = "label.unsettled";
	public static final String LABEL_FWD_MOVEMENTS = "label.fwdmovements";
	public static final String LABEL_UNEXPECTED = "label.unexpected";
	public static final String LABEL_EXTERNAL_BAL = "label.externalbal";
	public static final String LABEL_CUMULATIVE_EXT = "label.cumulativeext";
	public static final String LABEL_CRR_LIMIT = "label.crrlimit";
	public static final String LABEL_LIMIT_EXCESS = "label.limitexcess";
	public static final String LABEL_CORPORATE_ENTRIES = "label.corporateentries";
	public static final String LABEL_WHAT_IF_ANALYSIS = "label.whatifanalysis";
	public static final String LABEL_ADJ_EXTERNAL_BALANCE = "label.adjexternalbalance";
	public static final String LABEL_ADJ_CUMULATIVE_BALANCE = "label.adjcumulativebalance";
	public static final String LABEL_ADJ_LIMIT_EXCESS = "label.adjlimit_excess";

	public static final String WEEKEND_FLAG = "W";
	public static final String ALERT_START_CENTRALMONITOR = "alert.startMonitor";
	public static final String CENTRAL_BANK_MONITOR = "centralbankmonitor";

	// Added constants for screen specific Page Size
	public static final String EXPORT_MAX_PAGE_SIZE = "export.MaxPageSize";

	public static final String WEEKDAY_MON = "mon";
	public static final String WEEKDAY_TUE = "tue";
	public static final String WEEKDAY_WED = "wed";
	public static final String WEEKDAY_THU = "thu";
	public static final String WEEKDAY_FRI = "fri";
	public static final String WEEKDAY_SAT = "sat";
	public static final String WEEKDAY_SUN = "sun";
	public static final String DEFAULT_USE_CURRENCY_MULTIPLIER = "N";
	public static final String DEFAULT_CCY_PERSONAL_LIST = "N";
	public static final String DEFAULT_TO_PERSONAL_GROUP = "N";
	public static final String DEFAULT_HIDE_WEEKENDS = "N";
	public static final String DEFAULT_HIDE_LORO = "N";
	public static final String PROPNAME_REFRESH_RATE = "RefreshRate";
	public static final String PROPNAME_USE_CURRENCY_MULTIPLIER = "CurrencyMultiplier";
	public static final String PROPNAME_CCY_PERSONAL_LIST = "PersonalList";
	public static final String PROPNAME_HIDE_WEEKENDS = "HideWeekends";
	public static final String PROPNAME_HIDE_LORO = "HideLoro";
	public static final String PROPNAME_NUMBER_OF_DAYS = "NumberOfDays";
	public static final String PROPNAME_LIQUIDITY_CONFIG = "ILMProfile_";
	public static final String PROPNAME_ILM_CONFIG = "ILMConfig";
	public static final String PROPNAME_ILM_OPTIONS = "ILMOptions";
	public static final String PROPNAME_MSD_CONFIG = "MSDFilter_";
	public static final String PROPNAME_ILM_GENERAL_CONFIG = "ILMGeneralProperties";

	public static final String INTRADAY_BALANCES_REPORT_FILE_GRAPH_ALL = "/jsp/reports/IntradayBalanceGraphAll.jrxml";
	public static final Map<String, String> ALERTSTAGE_MENUITEMS = new HashMap<String, String>();
	static {
		ALERTSTAGE_MENUITEMS.put("MR", "75");
		ALERTSTAGE_MENUITEMS.put("MT", "72");
		ALERTSTAGE_MENUITEMS.put("MA", "107");
		ALERTSTAGE_MENUITEMS.put("MG", "22");
		ALERTSTAGE_MENUITEMS.put("MO", "107");
		ALERTSTAGE_MENUITEMS.put("PA", "26");
		ALERTSTAGE_MENUITEMS.put("PS", "25");
	}

	public static String getAlertStageMenuitems(String key) {
		return ALERTSTAGE_MENUITEMS.get(key);
	}

	// cut off sum flag
	public static final String CUTOFF_SUM_FLAG = "C";

	public static final String ENTITY_MONITOR_ID = "13";
	public static final String PROPNAME_PERSONAL_ENTITY_LIST = "PersonalEntityList";
	public static final String PROPNAME_PERSONAL_CURRENCY_LIST = "PersonalCurrencyList";
	public static final String PROPNAME_REPORT_CURRENCY = "ReportingCurrency";
	public static final String ENTITY_ID = "entityid";
	public static final String ENTITY_NAME = "entityname";
	public static final String DISPLAY = "display";
	public static final String DISPLAY_DAYS = "displaydays";
	public static final String PRIORITY = "priority";
	public static final String CURRENCY_GROUP_ALL = "All";
	public static final String OPTIMER_START_ALL = "all";
	public static final String OPTIMER_STOP_ALL = "all";
	public static final String UPDATE_PARAM_NOT_SENT = "Update parameter not sent";
	public static final String DATA_FETCH = "data-fetch";
	public static final String DATA_CLEANSE = "data-cleanse";
	public static final String FULL_DATE_FORMAT = "dd/MM/yyyy";
	public static final String DATE_MONTH = "dd/MM";
	public static final String MONTH_DATE = "MM/dd";
	public static final String PROPNAME_PERSONAL_ENTITY = "Personal_Entity";
	public static final String PROPNAME_PERSONAL_ENTITY_AGGR = "Personal_Entity_Aggr";
	public static final String PROPNAME_PERSONAL_CURRENCY = "Personal_Currency";
	public static final String SAVE = "save";
	public static final String DATA_FETCH_OK = "Data fetch ok";
	public static final String JOB_FLAG = "jobflag";
	public static final String JOB_FLAG_STATUS_TRUE = "jobFlagStatusTrue";
	public static final String HOLIDAY_FLAG = "P";
	public static final String ENTITY_MONITOR_TITLE = "EntityMonitor-SmartPredict";
	public static final String FORECAST_MONITOR_ID = "14";
	public static final String CUMULATIVE_BUCKET_TOTALS = "CumulativeTotals";
	public static final String DEFAULT_CUMULATIVE_BUCKET_TOTALS = "Y";
	public static final String HIDE_ZERO_SUM = "HideZeroSum";
	public static final String DEFAULT_HIDE_ZERO_SUM = "N";
	public static final String HIDE_ZERO_VALUE = "HideZeroValue";
	public static final String DEFAULT_HIDE_ZERO_VALUE = "N";
	public static final String DEFAULT_ENTITY = "N";
	public static final String CURRENCY = "Currency";
	public static final String DEFAULT_CURRENCY = "N";
	// varriable hold the Non Workday Facility properties file name
	public static final String FACILITY_PROPERTIES = "/nwd_facility.properties";
	public static final String FACILITY_SWEEPING = "facility_sweeping";
	public static final String FACILITY_MATCH_QUEUES_HEADER = "facility_match_queues_header";
	public static final String FACILITY_INPUT_AUTHORISE_QUEUE = "facility_input_authorise_queue";
	public static final String FACILITY_OPPORTUNITY_COST_REPORT = "facility_opportunity_cost_report";

	public static final String MATCHING_RECOVERY_SETRUN = "Matching Recovery SetRun: ";
	public static final String UPDATE = "Update";

	// Map to hold values for process status and last run status
	public static final Map<String, String> ENTITYPROCESS_STATUS = new HashMap<String, String>();
	static {
		ENTITYPROCESS_STATUS.put("N", "Not Running");
		ENTITYPROCESS_STATUS.put("R", "Running");
		ENTITYPROCESS_STATUS.put("TD", "To Disable");
		ENTITYPROCESS_STATUS.put("DG", "Disabling");
		ENTITYPROCESS_STATUS.put("D", "Disabled");
		ENTITYPROCESS_STATUS.put("TR", "To Run");
		ENTITYPROCESS_STATUS.put("S", "Success");
		ENTITYPROCESS_STATUS.put("F", "Failure");
	}

	/**
	 * 
	 * This function to pass the key and return the pair value
	 * 
	 * @param Key
	 * @param Value
	 * @return
	 */
	public static String getEntityprocessStatus(String key) {
		return ENTITYPROCESS_STATUS.get(key);
	}

	/* Variables related to user defined option to show normal or small fonts */
	public static final String PROPNAME_FONTSIZE = "FontSize";
	public static final String DEFAULT_FONT_SIZE = "defaultFontSize";
	/* Variable used to check the job flag */
	public static final String PROCESS_GROUP_MONITOR = "GROUP MONITORS: POPULATE DATA";
	/* Variable used to check the job flag */
	public static final String PROCESS_STATUS_RUNNING = "R";
	// Forecast Monitor Template
	public static final String DEFAULT = "*DEFAULT*";
	public static final String FIXED = "F";
	public static final String NORMAL = "N";
	public static final String SUB_TOTAL = "S";
	public static final String ROW_COLOR_RED = "red";
	public static final String ROW_COLOR_BLUE = "blue";
	public static final String ROW_COLOR_WHITE = "white";
	public static final String ROW_COLOR_GREY = "grey";
	public static final String FIXED_LABEL = "Fixed";
	public static final String NORMAL_LABEL = "Normal";
	public static final String SUB_TOTAL_LABEL = "SubTotal";
	public static final String BOOK = "B";
	public static final String GROUP = "G";
	public static final String META_GROUP = "M";
	public static final String ENTITY = "E";
	public static final String BOOK_LABEL = "Book";
	public static final String GROUP_LABEL = "Group";
	public static final String META_GROUP_LABEL = "Meta-Group";
	public static final String ENTITY_LABEL = "Entity";
	public static final String YES_VALUE = "Yes";
	public static final String No_VALUE = "No";
	public static final String TOTAL = "Total";
	public static final String TOTAL_COLUMN_SOURCETYPE = "C";
	public static final String PROPNAME_HIDE_TOTAL = "HideTotal";
	public static final String PROPNAME_HIDE_ASSUMPTION = "HideAssumption";
	public static final String PROPNAME_HIDE_SCENARIO = "HideScenario";

	/* Variables related to GUI changes in Predict for Smart Input v6 */
	public static final int SCREEN_INTERFACE_EXCEPTIONS = 15;
	public static final int SCREEN_INTERFACE_EXCEPTIONS_PCM = 2101;
	public static final String DEFAULT_RATE_INTERFACE_MONITOR = "defaultRefreshInterfaceMonitor";
	public static final String INTERFACE_TOTAL_COUNT = "TOTAL";
	public static final String INTERFACE_LAST_MESSAGE = "LASTMESSAGE";
	public static final String INTERFACE_PROCESSED = "PROCESSED";
	public static final String FETCH_MESSAGE = "fetch-message";
	public static final String INTERFACE_FILTERED = "FILTERED";
	public static final String INTERFACE_ACTIVE = "ACTIVE";
	public static final String INTERFACE_STATUS = "STATUS";
	public static final String INTERFACE_AWAITING = "AWAITING";
	public static final String INTERFACE_BAD = "BAD";
	public static final String QUERY_PROCESS = "query-reprocess";
	public static final String DEFAULT_FILTER_CRITERIA = "All|All|All|All";
	public static final String DEFAULT_SORT_CRITERIA = "0|true";
	public static final String INTERFACE_OPERATION_URL = "interfaces.soap.operation.url";
	public static final String INTERFACE_HEARTBEAT_METHOD = "getHeartBeat";
	public static final String INTERFACE_STATUS_DEAD = "DEAD";
	public static final String INTERFACECONFIG_STOP_INTERFACE = "stopInterface";
	public static final String INTERFACECONFIG_START_INTERFACE = "startInterface";
	public static final String INTERFACE_STATUS_STOPPED = "STOPPED";

	/* Variable used for Forecast Monitor */
	public static final String FORECAST_TOTAL = "TOTAL";
	public static final String FORECAST_SCENARIO = "SCENARIO";
	public static final String FORECAST_ASSUMPTION = "ASSUMPTION";
	public static final String FORECAST_GRAND_TOTAL = "GTOTAL";
	public static final String FORECAST_SUB = "sub";
	public static final String FORECAST_MAIN = "main";
	public static final String FORECAST_NONHIDE_HOLIDAY = "H";
	/* Variable used to set common data type */
	
	public static final String TYPE_STRING = "str";
	public static final String TYPE_INTEGER  = "num";
	public static final String TYPE_DATE = "date";

	/* Variable used to check the job flag */
	public static final String JOB_PROCESS_MONITOR = "MONITORS: POPULATE DATA";

	/* Variable is added for Input exceptions awaiting status. */
	public static final String INPUT_EXCEPTIONS_AWAITING_STATUS = "1";

	/* Variable related to Interface Monitor Stop/Start action */
	public static final String REQUEST_REPLY_STATUS = "SUCCESS";

	/*
	 * Variable related to Interface Monitor (Smart Input Engine status for
	 * Interface)
	 */
	public static final String INTERFACE_STATUS_RUNNING = "RUNNING";
	/*
	 * Variable used to make constants of ISO value
	 */
	public static final String ISO = "ISO-8859-1";
	public static final String DEFAULT_XML_ENCODING = "UTF-8";

	/*
	 * Variable declared to hold the value of total length can Sheet Name hold
	 */
	public static final int WORKSHEET_LENGTH = 31;

	/*
	 * Request reply constants
	 */
	public static final String REQ_REPLY_STATUS = "reply_status_ok";
	public static final String REQ_REPLY_MESSAGE = "reply_message";
	public static final String REQ_REPLY_LOCATION = "reply_location";

	public static final String CDM_BEAN_FOR_CHALLENGE = "CDM_CHALLENGE";

	// On ldap.properties file
	public static final String LDAP_PROPERTIES = "/ldap.properties";
	public static final String PROPERTY_DFA_URL = "dfa.url";
	public static final String PROPERTY_DFA_USERDN = "dfa.userDn";
	public static final String PROPERTY_DFA_PASSWORD = "dfa.password";
	public static final String PROPERTY_DFA_BASE = "dfa.base";
	public static final String PROPERTY_DFA_RSASERVER1_EXPR = "dfa.rsaServer1.expr";
	public static final String PROPERTY_DFA_RSASERVER2_EXPR = "dfa.rsaServer2.expr";
	public static final String PROPERTY_DFA_RSASERVER3_EXPR = "dfa.rsaServer3.expr";
	public static final String PROPERTY_DFA_SHAREDKEY_EXPR = "dfa.sharedKey.expr";
	public static final String PROPERTY_DFA_PORTAUTH_EXPR = "dfa.portAuth.expr";
	public static final String PROPERTY_DFA_PORTACCT_EXPR = "dfa.portAcct.expr";
	public static final String PROPERTY_DFA_TIMEOUT_EXPR = "dfa.timeout.expr";
	public static final String PROPERTY_DFA_AUTHPROTOCOL_EXPR = "dfa.authProtocol.expr";

	// On predict.properties file
	public static final String PROPERTY_DFA_ENABLED = "dfa.enabled";
	public static final String PROPERTY_DFA_FROMLDAP = "dfa.fromLdap";
	public static final String PROPERTY_DFA_RSASERVER1 = "dfa.rsaServer1";
	public static final String PROPERTY_DFA_RSASERVER2 = "dfa.rsaServer2";
	public static final String PROPERTY_DFA_RSASERVER3 = "dfa.rsaServer3";
	public static final String PROPERTY_DFA_SHAREDKEY = "dfa.sharedKey";
	public static final String PROPERTY_DFA_PORTAUTH = "dfa.portAuth";
	public static final String PROPERTY_DFA_PORTACCT = "dfa.portAcct";
	public static final String PROPERTY_DFA_TIMEOUT = "dfa.timeout";
	public static final String PROPERTY_DFA_AUTHPROTOCOL = "dfa.authProtocol";

	// on SwtRadiusChecker.java file
	public static final String KEY_DFA_RSASERVERS = "rsaServers";
	public static final String KEY_DFA_SHAREDKEY = "sharedKey";
	public static final String KEY_DFA_PORTAUTH = "portAuth";
	public static final String KEY_DFA_PORTACCT = "portAcct";
	public static final String KEY_DFA_TIMEOUT = "timeout";
	public static final String KEY_DFA_AUTHPROTOCOL = "authProtocol";
	/*
	 * Added by KaisBS for Mantis 1967: Improving and integrating SWFProfiler
	 * tool on all Flex screens a new constant retrieved from the
	 * predict.properties file
	 */
	public static final String PROPERTY_SWFPROFILER_ENABLED = "swfProfiler.enabled";
    
    /* Added by MedAmine  for Mantis 1405:Exports csv; pdf; xls -file name to include
	 *(Date & Time) -same as current reports 
	 */
	public static final String SwtFormatDate = "yyyyMMdd_HHmmss";
	public static final String TIME_ZERO = "00:00:00";
	public static final String TIME_FORMAT = " HH:mm:ss";

	// Added for mantis 1443 by KaisBS: enhanced alerting
	public static final String SUMMARY_GROUPING_VALUE_N = "None";
	public static final String SUMMARY_GROUPING_VALUE_E = "Entity";
	public static final String SUMMARY_GROUPING_VALUE_C = "Currency";

	//XML tags and attributes
	public static final String METADATA = "metadata";
	public static final String COLUMNS = "columns";
	public static final String COLUMN = "column";
	public static final String COLUMN_HEADER = "heading";
	public static final String COLUMN_HEADER_TOOLTIP = "headerTooltip";
	public static final String COLUMN_COLUMN_GROUP = "columnGroup";
	public static final String COLUMN_DATA = "dataelement";
	public static final String COLUMN_TYPE = "type";
	public static final String COLUMN_NUMBER = "columnNumber";
	public static final String COLUMN_WIDTH = "width";
	public static final String COLUMN_FACILITY_REF_COLUMN = "facilityRefColumn";
	public static final String COLUMN_FACILITY_REF_PARAMS= "scenarioRefParams";
	public static final String COLUMN_DRAGGABLE = "draggable";
	public static final String COLUMN_FILTERABLE = "filterable";
	public static final String COLUMN_VISIBLE = "visible";
	public static final String MAX_CHARS = "maxChars";
	public static final String COLUMN_SORT = "sort";
	public static final String XML_TAG_TIMING = "timing";
	public static final String XML_TAG_OPERATION = "operation";
	public static final String XML_TAG_GENERIC_GRID = "genericgrid";
	public static final String XML_TAG_PAGES = "pages";
	public static final String XML_TAG_PREV = "prev";
	public static final String XML_TAG_PAGE = "page";
	public static final String XML_TAG_NEXT = "next";
	public static final String XML_ATTRIBUTE_CURENT_PAGE = "currentPage";
	public static final String XML_ATTRIBUTE_MAX_PAGE = "maxPage";
	public static final String XML_ATTRIBUTE_ID = "Id";
	public static final String XML_ATTRIBUTE_ENABLED = "enabled";
	// This variable will indicate the max records per page in alert Generic Display screen
	public static final String ALERT_DISPLAY_PAGE_SIZE = "screenPageSize.alertGenericDisplay";
	public static final String PLUS_URL_ENCODED = "%2B";
	public static final String AND_URL_ENCODED = "%26";
	public static final String SEPARATOR_FACILITIES = "$#$";
	public static final String SEPARATOR_SCREEN_DETAILS = ",";
	public static final String SEPARATOR_RECORD = "$#$";
	public static final String SEPARATOR_RECORD_REGEX = "\\$#\\$";
	public static final String ZERO_TIME = "0d 0h 0m 0.0000s";
	public static final String SCENARIO_CUT_OFF= " to cut-off";
	public static final int SCENARIO_CATEGORY_MAX_SIZE= 21;
	public static final int SCENARIO_CATEGORY_MAX_WIDTH= 260;
	public static final int ACCOUNT_GROUP_TITLE_MAX_WIDTH= 400;
	public static final int ENTITY_NAME_MAX_WIDTH= 180;
	public static final int SCENARIO_TITLE_MAX_SIZE= 18;
	public static final int SCENARIO_TITLE_MAX_WIDTH= 245;
	public static final int VERDANA_12P_SIZE= 12;
	public static final String VERDANA_STYLE="verdana";
	
	
	// facility ids 
	
	public static final String FACILITY_NONE = "None";
	public static final String FACILITY_MATCH_DISPLAY_MANY = "MATCH_DISPLAY_MANY";
	public static final String FACILITY_INPUT_EXCEPTION = "INPUT_EXCEPTION";
	public static final String FACILITY_GENERIC = "GENERIC";
	public static final String FACILITY_MSD = "MSD";
	public static final int FACILITY_NO_ACCESS = 2;
	
        //Dynamic Jasper constants
	
	public static final String DYNAMIC_JASPER_TEMP_DIR = "jasperExport.tempdir";
	public static final String DYNAMIC_JASPER_SWAP_NUMBER_PAGES = "jasperExport.startSwapNumberOfPages";
	public static final String DYNAMIC_JASPER_NO_DATA_FOUND = "dynamicJasper.noDataFound";
	public static final String DYNAMIC_JASPER_NUMBER_TYPE = "NUMBER";
	public static final String DYNAMIC_JASPER_DATE_TYPE = "DATE";
	public static final String DYNAMIC_JASPER_FIRST_SHEET_NAME = "GenericDisplay-SmartPredict";
	public static final String DYNAMIC_JASPER_DB_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String OPERATION ="operation";
	public static final String ACCOUNT_ID ="accountid";
	public static final String TIME ="time";
	public static final String TRANS_CREDIT  ="credit";
	public static final String TRANS_DEBIT ="debit";
	public static final String DESCRIPTION ="description";
	
	public static final String CASH ="Cash";
	public static final String CUSTODIAN ="Custodian";
	public static final String CURRENT ="Current";
	public static final String LORO ="Loro";
	public static final String NETTING ="Netting";
	public static final String NOSTRO ="Nostro";
	public static final String OTHERS ="Others";
	public static final String MAIN ="Main";
	public static final String SUB ="Sub";
	
	public static final String  ACCOUNT_GROUP_QUERY = "SELECT a.account_id || ' - ' || a.account_name as  \"$P!{pGROUP_ID_NAME}\", DECODE(a.account_type,'C',$P{pCASH_TEXT}, 'U',$P{pCUSTODIAN_TEXT},a.account_type) as \"$P!{pTYPE}\",DECODE( a.account_class,'C',$P{pCURRENT_TEXT},'L',$P{pLORO_TEXT},'E',$P{pNETTING_TEXT},'N',$P{pNOSTRO_TEXT},'O',$P{pOTHERS_TEXT},a.account_class) as \"$P!{pCLASS}\",  DECODE(a.account_level,'M',$P{pMAIN},$P{pSUB}) as \"$P!{pLEVEL}\" FROM P_ACCOUNT a, P_ILM_ACC_IN_GROUP ag WHERE a.ACCOUNT_ID = ag.ACCOUNT_ID AND ILM_GROUP_ID = $P{pILM_GROUPID}";
	public static final String  ACCOUNT_GROUP_QUERY_CLAUSE = "SELECT a.account_id || ' - ' || a.account_name as  \"$P!{pGROUP_ID_NAME}\", DECODE(a.account_type,'C',$P{pCASH_TEXT}, 'U',$P{pCUSTODIAN_TEXT},a.account_type) as \"$P!{pTYPE}\",DECODE( a.account_class,'C',$P{pCURRENT_TEXT},'L',$P{pLORO_TEXT},'E',$P{pNETTING_TEXT},'N',$P{pNOSTRO_TEXT},'O',$P{pOTHERS_TEXT},a.account_class) as \"$P!{pCLASS}\",  DECODE(a.account_level,'M',$P{pMAIN},$P{pSUB}) as \"$P!{pLEVEL}\" FROM P_ACCOUNT a WHERE ";

	public static final int ILM_EXPORT_COLUMN_WIDTH = 160;
	public static final String INTRADAYLIQUIDITY_TIMESLOTSIZE = "intraDayLiquidity.timeSlotSize";
	public static final String  ILM_DATA_STATE_CHECK_REFRESH_TIME = "intraDayLiquidity.checkDataStateTime";
	public static final String ILM_PROCESS_DRIVER_SEQUENCE_NAME = "SEQ_P_PROCESS_DRIVER";
	public static final String ILM_DB_LINK_FAIL = "DBLink failed";
	public static final String ILM_CALCULATION_PROCESS = "SEQ_ILM_CALCUL";
	public static final String ILM_CALCULATION_PROCESS_KEY = "ILM_CALCUL_KEY";

	//public static final String ISO_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"; ==> Temporary rolled back to the previous version of the date format, for later versions on Predict this line should be uncommented
	public static final String ISO_DATE_FORMAT   = "dd/MM/yyyy HH:mm:ss";
	
	public static final String LANGUAGE_FRENCH   = "FR";
	public static final String LANGUAGE_ENGLISH   = "EN";
	
	public static final String DICTIONARY_FRENCH   = "dictionary_fr";
	public static final String DICTIONARY_ENGLISH   = "dictionary_en";

	public static final String JMX_ENABLED = "jmx.enabled";  

	public static final String NUMERIC_TYPE ="type.numeric";
	public static final String TEXT_TYPE ="type.text";
	public static final String DATE_TYPE ="type.date";
	
	public static final String SESSION_CURRENCY_DST_LIST ="sessionCurrencyDSTList";
	public static final String CURRENCY_DST_LIST_ADD ="currencyDSTListToAdd";
	public static final String CURRENCY_DST_LIST_CHANGE ="currencyDSTListToChange";
	public static final String CURRENCY_DST_LIST_DELETE ="currencyDSTListToDelete";
	
	public static final String SESSION_SYSTEM_DST_LIST ="sessionSystemDSTList";
	public static final String SYSTEM_DST_LIST_ADD ="systemDSTListToAdd";
	public static final String SYSTEM_DST_LIST_CHANGE ="systemDSTListToChange";
	public static final String SYSTEM_DST_LIST_DELETE ="systemDSTListToDelete";
	
	public static final String IS_ILM_LIQ_CONTRIBUTOR  = "IS_ILM_LIQ_CONTRIBUTOR ='Y'";
	public static final String IS_ILM_CENTRAL_BANK_MEMBER  = "IS_ILM_LIQ_CONTRIBUTOR ='Y' AND IS_ILM_CENTRAL_BANK_MEMBER ='Y'";
 	public static final String ILM_ALL_GLOBAL_GROUPS_ID  = "Global Groups (Main)";
	public static final String ILM_ALL_ALT_GLOBAL_GROUPS_ID  = "Global Groups (Main/Alt)";
	public static final String ILM_ALL_ALT_ONLY_GROUPS_ID  = "Global Groups (Alt only)"; 
	public static final String ILM_REPORT_TYPE_BASEL_A  = "ILM_BASEL_A";
	public static final String ILM_REPORT_TYPE_BASEL_B  = "ILM_BASEL_B";
	public static final String ILM_REPORT_TYPE_BASEL_C  = "ILM_BASEL_C";
	public static final String ILM_REPORT_TYPE_GROUP  = "ILM_GROUP_REPORT";
	public static final String ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM  = "ILM_IDAY_RISK_NCP";
	public static final String ILM_REPORT_TYPE_MULTI_CURRENCY_ILM_REPORTE_EXCEL  = "ILM_MULTI_CCY_EXCEL";

	public static final String DBMONITOR_ENABLED = "dbmonitor.enabled"; 
	
	public static final String MAIL_PROPERTIES = "/mail.properties"; 
	public static final String MAIL_ENABLED = "mail.enabled"; 
	public static final String MAIL_TRANSPORT_PROTOCOL = "mail.transport.protocol"; 
	public static final String MAIL_PROTOCOL_HOST = "mail.protocol.host"; 
	public static final String MAIL_PROTOCOL_PORT = "mail.protocol.port"; 
	public static final String MAIL_PROTOCOL_AUTH = "mail.protocol.auth";
	public static final String MAIL_SSL_PROTOCOL_AUTH = "mail.protocol.ssl.protocols";
	public static final String MAIL_SUBJECT_PREFIX = "mail.subject.prefix";
	public static final String MAIL_USER_EMAIL = "mail.user.email"; 
	public static final String MAIL_USER_PASSWORD = "mail.user.password"; 
	public static final String MAIL_FROM_EMAIL = "mail.from.email"; 
	public static final String MAIL_MAX_RETRY = "mail.max.retry"; 
	public static final String MAIL_BASE_DELAY = "mail.base.delay";
	public static final String MAIL_MAXACTIVE_CONNECTION = "mail.maxactive.connection";
	public static final String MAIL_MAXIDLE_CONNECTION = "mail.maxidle.connection";
	public static final String ALERT_SCENARIOS_TEMPLATE_EN = "/templates/alert_scenarios_en.html";
	public static final String ALERT_SCENARIOS_TEMPLATE_FR = "/templates/alert_scenarios_fr.html";
	public static final String INTERRUPT_NOTIF_TEMPLATE_EN = "/templates/interrupt_notification_en.html";
	public static final String INTERRUPT_NOTIF_TEMPLATE_FR = "/templates/interrupt_notification_fr.html";
	public static final String SCHEDULED_REPORTS_TEMPLATE_FR = "/templates/scheduled_reports_fr.htm";
	public static final String SCHEDULED_REPORTS_TEMPLATE_EN = "/templates/scheduled_reports_en.htm";
	
	public static final String CSRF_TOKEN_EMTPTY = "EMPTY";
	public static final String CSRF_TOKEN_WRONG = "WRONG";
	public static final int ROLE_ACCESS_CACHE_TIME_MINUTES = 1;

	// Variable String to hold the session validation time
	public static final String LAST_SESSION_VALIDATION_ACCESSED = "lastSessionValidationAccessed";
	
	public static final String TURNOVER_DATASOURCE_ACTUALS = "A";
	public static final String TURNOVER_DATASOURCE_FORECASTED = "F";
	public static final String TURNOVER_OUTPUT_PDF = "P";
	public static final String TURNOVER_OUTPUT_EXCEL = "E";
	public static final String TURNOVER_SINGLE_DAY = "S";
	public static final String TURNOVER_DATE_RANGE = "R";
	public static final String TURNOVER__REPORTS_TEMPLATE = "/jsp/reports/Turnover_Report_Template.xlsx";
	
	public static final String KEYWORD_RUNDATE = "RUN_DATE";
	public static final String KEYWORD_RUNDATE_MINUS1 = "RUN_DATE-1";
	public static final String KEYWORD_RUNDATE_MINUS2 = "RUN_DATE-2";
	public static final String KEYWORD_RUNDATE_MINUS3 = "RUN_DATE-3";
	public static final String KEYWORD_RUNDATE_MINUS4 = "RUN_DATE-4";
	public static final String KEYWORD_RUNDATE_MINUS5 = "RUN_DATE-5";
	public static final String KEYWORD_RUNDATE_MINUS6 = "RUN_DATE-6";
	public static final String KEYWORD_RUNDATE_MINUS7 = "RUN_DATE-7";
	public static final String KEYWORD_START_OF_CURRENT_WEEK = "START_OF_CURRENT_WEEK";
	public static final String KEYWORD_END_OF_CURRENT_WEEK = "END_OF_CURRENT_WEEK";
	public static final String KEYWORD_START_OF_PREVIOUS_WEEK = "START_OF_PREVIOUS_WEEK";
	public static final String KEYWORD_END_OF_PREVIOUS_WEEK = "END_OF_PREVIOUS_WEEK";
	public static final String KEYWORD_START_OF_CURRENT_MONTH = "START_OF_CURRENT_MONTH";
	public static final String KEYWORD_END_OF_CURRENT_MONTH = "END_OF_CURRENT_MONTH";
	public static final String KEYWORD_START_OF_PREVIOUS_MONTH = "START_OF_PREVIOUS_MONTH";
	public static final String KEYWORD_END_OF_PREVIOUS_MONTH = "END_OF_PREVIOUS_MONTH";
	public static final String KEYWORD_START_OF_CURRENT_QUARTER = "START_OF_CURRENT_QUARTER";
	public static final String KEYWORD_END_OF_CURRENT_QUARTER = "END_OF_CURRENT_QUARTER";
	public static final String KEYWORD_START_OF_PREVIOUS_QUARTER = "START_OF_PREVIOUS_QUARTER";
	public static final String KEYWORD_END_OF_PREVIOUS_QUARTER = "END_OF_PREVIOUS_QUARTER";
	public static final String KEYWORD_START_OF_CURRENT_YEAR = "START_OF_CURRENT_YEAR";
	public static final String KEYWORD_END_OF_CURRENT_YEAR = "END_OF_CURRENT_YEAR";
	public static final String KEYWORD_START_OF_PREVIOUS_YEAR = "START_OF_PREVIOUS_YEAR";
	public static final String KEYWORD_END_OF_PREVIOUS_YEAR = "END_OF_PREVIOUS_YEAR";
	
	public static final String SCHEDULED_REPORT_LOCATION_PROPERTIE_NAME = "ScheduledReportLocation";
	
	public static final String SUCCESS = "S";
	public static final String FAIL = "F";
	
	public static final String PCM_ENABLED = "pcmEnabled";
	
	
	/**
	 * XML ELEMENTS - ADDED BY RK START
	 */
	// Request reply tag
	public static final String REQUEST_REPLY = "request_reply";
	// Status ok tag
	public static final String STATUS_OK = "status_ok";
	// Message tag
	public static final String MESSAGE = "message";
	// Location tag
	public static final String LOCATION = "location";
	// Alert message tag
	public static final String ALERT_MESSAGE = "alertmsg";
	// Alert entity tag
	public static final String ALERT_ENTITY = "alertentity";
	// Alert message attribute
	public static final String MSG = "msg";
	// Alert message type attribute
	public static final String MSG_TYPE = "msgtype";
	// Expire days attribute
	public static final String EXPIRE_DAYS = "expiredays";
	// Root tag
	public static final String ROOT = "root";
	// Menu item tag
	public static final String MENU_ITEM = "menuitem";
    //menu Group Order - Added for separator in menu
    public static final String SEPARATOR = "separator";
	// Program name tag
	public static final String PROGRAM_NAME = "programname";
	// Dictionary name tag
	public static final String DICTIONARY_NAME = "dictionaryname";
	// User name tag
	public static final String USER_NAME = "username";
	// Grid tag
	public static final String GRID = "grid";
	
	// Grid tag
	public static final String COMMON_GRID = "common_grid";
		
	// Rows tag
	public static final String ROWS = "rows";
	// Row tag
	public static final String ROW = "row";
	// Rows tag
	public static final String TOTALS_TAG = "totals";
	// Row tag
	public static final String TOTAL_TAG = "total";
	// Details tag
	public static final String DETAILS = "details";
	// Details tag
	public static final String SELECT_ELEMENT = "select_query";
	// Total no of pages
	public static final String MAX_PAGE = "maxpage";
	// Current page no
	public static final String CURRENT_PAGE = "currentpage";
	// Total no of pages - default value
	public static final int DEFAULT_MAX_PAGE = 1;
	// Current page no - default value
	public static final int DEFAULT_PAGE = 1;
	// Paging tag
	public static final String PAGING = "paging";
	// Columns tag
	public static final String ALL_COLUMNS_TAG = "allcolumns";
	// column visual type
	public static final String COLUMN_VISUAL_TYPE = "visualtype";
	// Column data provider
	public static final String COLUMN_DATA_PROVIDER = "dataprovider";
	
	public static final String 	COLUMN_DATA_ID = "dataId";
	// Column data provider
	public static final String COLUMN_DATA_URL = "dataurl";
	// Column order
	public static final String COLUMN_ORDER = "columnorder";
	// Visible flag for default user
	public static final String COLUMN_VISIBLE_DEFAULT = "visible_default";
	// column format
	public static final String COLUMN_FORMAT_TYPE = "column_format";
	// Visible flag
	public static final String COLUMN_FORMAT = "format";
		
	// Database column length
	public static final String COLUMN_LENGTH = "length";

	// Database column precision
	public static final String COLUMN_PRECISION = "precision";

	// Column is nullable
	public static final String COLUMN_IS_NULL = "is_null";
	// Column is nullable
	public static final String COLUMN_IS_HOLIDAY = "holiday";

	// Selects tag
	public static final String SELECTS = "selects";
	// Selects tag
	public static final String IMAGES = "images";
	// Select tag
	public static final String SELECT = "select";
	// Select tag
	public static final String IMAGE = "image";
	// Option tag
	public static final String OPTION = "option";
	// Option Value
	public static final String VALUE = "value";
	// Selected flag
	public static final String SELECTED = "selected";
	// Id of the object
	public static final String ID = "id";
	// Level of the object
	public static final String LEVEL = "level";
	// Type of the object
	public static final String TYPE = "type";
	// Negative value flag
	public static final String NEGATIVE = "negative";
	// Holiday value
	public static final String HOLIDAY = "holiday";
	// Clickable flag
	public static final String CLICKABLE = "clickable";
	// Clickable flag
	public static final String EDITABLE = "editable";
	// ColorRecord flag
	public static final String BOLD = "bold";
	// Row size (no of records)
	public static final String SIZE = "size";
	// Row size (no of records by module)
	public static final String SIZE_BY_MODULE = "sizeByModule";
	// Menus tag
	public static final String MENUS = "menus";
	// Menu tag
	public static final String MENU = "menu";
	// Modules tag
	public static final String MODULES = "modules";
	// Module tag
	public static final String MODULE = "module";
	// Menu/Module icon
	public static final String ICON = "icon";
	// Label
	public static final String LABEL = "label";
	// Menu action
	public static final String MENU_ACTION = "menuaction";
	// Menu item id
	public static final String ITEM_ID = "itemid";
	// Menu access right
	public static final String MENU_ACCESS = "menuaccess";
	// Height
	public static final String HEIGHT = "height";
	// Width
	public static final String WIDTH = "width";
	// Favourites tag
	public static final String FAVOURITES = "favourites";
	// Favourite tag
	public static final String FAVOURITE = "favourite";
	// Code flag
	public static final String CODE = "code";

	// urls tag
	public static final String PICKER_URLS = "urls";
	// url tag
	public static final String PICKER_URL = "url";
	// EXCEL Version
	public static final String EXCEL_VERSION = "excelVersion";
	// EXCEL XLSX
	public static final String EXCEL_XLSX = "xlsx";
	// EXCEL XLS
	public static final String EXCEL_XLS = "xls";
	
	/**
	 * MODULE LICENSE XML TAGS - START
	 */
	// Modulelicense tag
	public static final String MODULE_LICENSE = "modulelicense";
	// Hostid tag
	public static final String HOSTID = "hostid";
	// Hostname tag
	public static final String HOSTNAME = "hostname";
	// Maxusers tag
	public static final String MAXUSERS = "maxusers";
	// ExpiryDate tag
	public static final String EXPIRES = "expiryDate";
	// Add New License tag
	public static final String ADD_NEWLICENSE = "add_license_url";
	/*
	 * MODULE LICENSE XML TAGS - END
	 */
	
	
	
	// String variable to hold amount limit
	public static final String AMOUNT_FORMAT = "amountFormat";
	// String variable to hold amount decimal
	public static final String AMOUNT_DECIMAL = "amountDecimal";
	// Date format tag
	public static final String DATE_FORMAT = "dateformat";
	// Amount delimeter tag
	public static final String AMOUNT_DELIMETER = "amountdelimeter";
	// SysParams tag
	public static final String SYS_PARAMS = "sysparams";
	// language tag
	public static final String LANGUAGE = "language";
	
	// Column type - Number
	public static final String COLUMN_TYPE_NUMBER = "num";
	// Singletons tag
	public static final String SINGLETONS = "singletons";
	
	// tabs tag
	public static final String TABS = "tabs";
		
		
	// Helpurl tag
	public static final String HELPURL = "helpurl";
	
	public static final String ALERT_SUMMARY_TAB1_NAME = "alertSummaryTab1Name";
	public static final String ALERT_SUMMARY_TAB2_NAME = "alertSummaryTab2Name";
	
	
	// Current user
	public static final String SCREEN_ID = "screenid";
	
	// Current user tag
	public static final String CURRUSER = "currentuser";
	// Column type - String
	public static final String COLUMN_TYPE_STRING = "str";
	// Column type - String
	public static final String COLUMN_TYPE_TREE = "tree";
	// Column type - Number
	public static final String COLUMN_TYPE_BOOLEAN = "bool";
	// Column type - checkbox
		public static final String COLUMN_TYPE_CHECK= "checkbox";
		// Column type - radio
		public static final String COLUMN_TYPE_RADIO= "radio";
	// Column type - date
	public static final String COLUMN_TYPE_DATE= "date";
	// Column type - link
	public static final String COLUMN_TYPE_LINK= "link";
	// Column combo
	public static final String COLUMN_TYPE_COMBO= "combo";
	
	// Column type - link-num
	public static final String COLUMN_TYPE_LINK_NUM= "link:num";
	
	//Connection Pool Stats Monitor 
	public static final int MENU_ITEM_CONNECTION_POOL = 63;
	public static final String CONNECTION_POOL_ACTIVE_ID = "ACTIVE";
	public static final String CONNECTION_POOL_IDLE_ID = "IDLE";
	/*Connection Pool Stats columns */
	public static final String CONNECTIONPOOLMONITOR = "connectionPoolMonitor";
	public static final String MODULE_LIST = "moduleList";
	public static final String CONNECTION_POOL_ACTIVE_STATS = "activeStats";
	public static final String CONNECTION_POOL_IDLE_STATS = "idleStats";
	public static final String CONNECTION_POOL_IS_FRESH_VIEWS_DATA = "freshViewsData";

	
	public static final String CONNECTION_POOL_MODULE_HEADING = "connectionPool.module";
	public static final String CONNECTION_POOL_CONNECTIONID_HEADING = "connectionPool.connectionId";
	public static final String CONNECTION_POOL_MODULE_STATUS_HEADING = "connectionPool.status";
	public static final String CONNECTION_POOL_MODULE_SQL_STATUS_HEADING = "connectionPool.sqlStatus";
	public static final String CONNECTION_POOL_STACKTRACE_HEADING = "connectionPool.stackTrace";
	public static final String CONNECTION_POOL_SQLID_HEADING = "connectionPool.sqlId";
	public static final String CONNECTION_POOL_SQLSTATEMENT_HEADING = "connectionPool.sqlStatement";
	public static final String CONNECTION_POOL_LASTACTIONTIME_HEADING = "connectionPool.lastActionTime";
	public static final String CONNECTION_POOL_SSID_HEADING = "connectionPool.sid";
	public static final String CONNECTION_POOL_AUDSID_HEADING = "connectionPool.audsid";
	public static final String CONNECTION_POOL_DURATION_HEADING = "connectionPool.duration";
	
	public static final String CONNECTION_POOL_MODULE_HEADING_TOOLTIP = "connectionPool.tooltip.module";
	public static final String CONNECTION_POOL_CONNECTIONID_HEADING_TOOLTIP =  "connectionPool.tooltip.connectionId";
	public static final String CONNECTION_POOL_MODULE_STATUS_HEADING_TOOLTIP = "connectionPool.tooltip.status";
	public static final String CONNECTION_POOL_LASTACTIONTIME_HEADING_TOOLTIP ="connectionPool.tooltip.lastActionTime";
	public static final String CONNECTION_POOL_STACKTRACE_HEADING_TOOLTIP = "connectionPool.tooltip.stackTrace";
	public static final String CONNECTION_POOL_MODULE_SQL_STATUS_HEADING_TOOLTIP =  "connectionPool.tooltip.sqlStatus";
	public static final String CONNECTION_POOL_SQLID_HEADING_TOOLTIP ="connectionPool.tooltip.sqlId";
	public static final String CONNECTION_POOL_SQLSTATEMENT_HEADING_TOOLTIP = "connectionPool.tooltip.sqlStatement";
	public static final String CONNECTION_POOL_SSID_HEADING_TOOLTIP =  "connectionPool.tooltip.sid";
	public static final String CONNECTION_POOL_AUDSID_HEADING_TOOLTIP = "connectionPool.tooltip.audsid";
	public static final String CONNECTION_POOL_DURATION_HEADING_TOOLTIP = "connectionPool.tooltip.duration";
	
	
	/*Connection Pool Stats columns */
	public static final String CONNECTION_POOL_MODULE_TAGNAME = "module";
	public static final String CONNECTION_POOL_CONNECTIONID_TAGNAME = "id";
	public static final String CONNECTION_POOL_STATUS_TAGNAME = "status";
	public static final String CONNECTION_POOL_STATUS_VALUE_TAGNAME = "statusValue";
	public static final String CONNECTION_POOL_SQL_STATUS_TAGNAME = "sqlStatus";
	public static final String CONNECTION_POOL_SQL_STATUS_VALUE_TAGNAME = "sqlStatusValue";
	public static final String CONNECTION_POOL_STACKTRACE_TAGNAME = "stackTrace";
	public static final String CONNECTION_POOL_SQLID_TAGNAME = "sqlId";
	public static final String CONNECTION_POOL_SQLSTATEMENT_TAGNAME = "sqlStatement";
	public static final String CONNECTION_POOL_LASTACTIONTIME_TAGNAME = "lastActionTime";
	public static final String CONNECTION_POOL_SSID_TAGNAME = "SSID";
	public static final String CONNECTION_POOL_AUDSID_TAGNAME = "audSid";
	public static final String CONNECTION_POOL_DURATION_TAGNAME = "duration";	
	public static final String CONNECTION_POOL_BACKGROUND_COLOR = "bgColor";	
	public static final String CONNECTION_POOL_SQL_EXEC_START = "sqlExecStart";

	
	
	
	
	
	public static final String ILM_SUMMARY_GRID_ALERTING = "alerting";
	public static final String ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TAGNAME = "currencyEntityGroupAccount";
	public static final String ILM_SUMMARY_GRID_CUTOFF_TAGNAME = "cutOff";
	public static final String ILM_SUMMARY_GRID_CUTOFF_EXCEEDED_TAGNAME = "cutOffExceeded";
	public static final String ILM_SUMMARY_GRID_PREDICTED_TAGNAME = "predicted";
	public static final String ILM_SUMMARY_GRID_EXTERNAL_TAGNAME = "external";
	public static final String ILM_SUMMARY_GRID_TURNOVER_TAGNAME = "turnover";
	public static final String ILM_SUMMARY_GRID_AVAILABLE_TAGNAME = "available";
	public static final String ILM_SUMMARY_GRID_UNSETTLED_TAGNAME = "unsettled";
	public static final String ILM_SUMMARY_GRID_UNEXPECTED_TAGNAME = "unexpected";
	public static final String ILM_SUMMARY_GRID_PREADVICE_TAGNAME = "preAdvice";
	public static final String ILM_SUMMARY_GRID_SUM_TAGNAME = "sum";
	public static final String ILM_SUMMARY_GRID_ACCOUNT_NAME_TAGNAME = "accountName";
	public static final String ILM_SUMMARY_GRID_CONFD_TAGNAME = "confD";
	public static final String ILM_SUMMARY_GRID_INC_TAGNAME = "inc";
	public static final String ILM_SUMMARY_GRID_STARTOFDAY_TAGNAME = "startOfDay";
	public static final String ILM_SUMMARY_GRID_MINBALT_TAGNAME = "minBalT";
	public static final String ILM_SUMMARY_GRID_ORDER_TAGNAME = "order";
	public static final String ILM_SUMMARY_GRID_CURRENCY_TAGNAME = "ccyCode";
	public static final String ILM_SUMMARY_GRID_ENTITY_TAGNAME = "entityId";
	public static final String ILM_SUMMARY_GRID_GROUP_ID_TAGNAME = "accountGroupId";
	public static final String ILM_SUMMARY_GRID_ROW_COLOR_TAGNAME = "rowColor";

	public static final String ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_HEADING = "ilmSummary.currencyEntityGroupAccount";
	public static final String ILM_SUMMARY_GRID_ACCOUNT_NAME_HEADING = "ilmSummary.accountName";
	public static final String ILM_SUMMARY_GRID_CUTOFF_HEADING = "ilmSummary.cutOff";
	public static final String ILM_SUMMARY_GRID_PREDICTED_HEADING = "ilmSummary.predicted";
	public static final String ILM_SUMMARY_GRID_EXTERNAL_HEADING = "ilmSummary.external";
	public static final String ILM_SUMMARY_GRID_TURNOVER_HEADING = "ilmSummary.turnover";
	public static final String ILM_SUMMARY_GRID_AVAILABLE_HEADING = "ilmSummary.available";
	public static final String ILM_SUMMARY_GRID_UNSETTLED_HEADING = "ilmSummary.unsettled";
	public static final String ILM_SUMMARY_GRID_UNEXPECTED_HEADING = "ilmSummary.unexpected";
	public static final String ILM_SUMMARY_GRID_PREADVICE_HEADING = "ilmSummary.preAdvice";
	public static final String ILM_SUMMARY_GRID_SUM_HEADING = "ilmSummary.sum";
	public static final String ILM_SUMMARY_GRID_CONFD_HEADING = "ilmSummary.confD";
	public static final String ILM_SUMMARY_GRID_INC_HEADING = "ilmSummary.inc";
	public static final String ILM_SUMMARY_GRID_STARTOFDAY_HEADING = "ilmSummary.startOfDay";
	public static final String ILM_SUMMARY_GRID_MINBALT_HEADING = "ilmSummary.minBalT";

	public static final String ILM_SUMMARY_GRID_CURRENCYENTITYGROUPACCOUNT_TOOLTIP = "ilmSummary.tooltip.currencyEntityGroupAccount";
	public static final String ILM_SUMMARY_GRID_ACCOUNT_NAME_TOOLTIP = "ilmSummary.tooltip.accountName";
	public static final String ILM_SUMMARY_GRID_CUTOFF_TOOLTIP = "ilmSummary.tooltip.cutOff";
	public static final String ILM_SUMMARY_GRID_PREDICTED_TOOLTIP = "ilmSummary.tooltip.predicted";
	public static final String ILM_SUMMARY_GRID_EXTERNAL_TOOLTIP = "ilmSummary.tooltip.external";
	public static final String ILM_SUMMARY_GRID_TURNOVER_TOOLTIP = "ilmSummary.tooltip.turnover";
	public static final String ILM_SUMMARY_GRID_AVAILABLE_TOOLTIP = "ilmSummary.tooltip.available";
	public static final String ILM_SUMMARY_GRID_UNSETTLED_TOOLTIP = "ilmSummary.tooltip.unsettled";
	public static final String ILM_SUMMARY_GRID_UNEXPECTED_TOOLTIP = "ilmSummary.tooltip.unexpected";
	public static final String ILM_SUMMARY_GRID_PREADVICE_TOOLTIP = "ilmSummary.tooltip.preAdvice";
	public static final String ILM_SUMMARY_GRID_SUM_TOOLTIP = "ilmSummary.tooltip.sum";
	public static final String ILM_SUMMARY_GRID_CONFD_TOOLTIP = "ilmSummary.tooltip.confD";
	public static final String ILM_SUMMARY_GRID_INC_TOOLTIP = "ilmSummary.tooltip.inc";
	public static final String ILM_SUMMARY_GRID_STARTOFDAY_TOOLTIP = "ilmSummary.tooltip.startOfDay";
	public static final String ILM_SUMMARY_GRID_MINBALT_TOOLTIP = "ilmSummary.tooltip.minBalT";
	
	
	
	
	
	public static final String THROUPUT_MONITOR_ENTITY_HEADING = "throuputmonitor.entity";
	public static final String THROUPUT_MONITOR_CCY_HEADING = "throuputmonitor.ccy";
	public static final String THROUPUT_MONITOR_ILM_GROUP_HEADING = "throuputmonitor.ilm_group";
	public static final String THROUPUT_MONITOR_FORC_INF_HEADING = "throuputmonitor.forcinf";
	public static final String THROUPUT_MONITOR_FORC_OUT_HEADING = "throuputmonitor.forcout";
	public static final String THROUPUT_MONITOR_ACT_INF_HEADING = "throuputmonitor.actinf";
	public static final String THROUPUT_MONITOR_ACT_OUT_HEADING = "throuputmonitor.actout";
	public static final String THROUPUT_MONITOR_UNSET_OUT_HEADING = "throuputmonitor.unsetout";
	public static final String THROUPUT_MONITOR_THRESHOLD_1_HEADING = "throuputmonitor.threshold1";
	public static final String THROUPUT_MONITOR_THRESHOLD_2_HEADING = "throuputmonitor.threshold2";
	public static final String THROUPUT_MONITOR_CURRENT_HEADING = "throuputmonitor.current";
	public static final String THROUPUT_MONITOR_ACTUALS_HEADING = "throuputmonitor.actuals";
	public static final String THROUPUT_MONITOR_FORECASTED_HEADING = "throuputmonitor.forecasted";
	public static final String THROUPUT_MONITOR_THROUGHPUT_RATIOS_HEADING = "throuputmonitor.throughputratios";
	
	
	
	public static final String THROUPUT_MONITOR_ENTITY_TOOLTIP = "throuputmonitor.tooltip.entity";
	public static final String THROUPUT_MONITOR_CCY_TOOLTIP = "throuputmonitor.tooltip.ccy";
	public static final String THROUPUT_MONITOR_ILM_GROUP_TOOLTIP = "throuputmonitor.tooltip.ilm_group";
	public static final String THROUPUT_MONITOR_FORC_INF_TOOLTIP = "throuputmonitor.tooltip.forcinf";
	public static final String THROUPUT_MONITOR_FORC_OUT_TOOLTIP = "throuputmonitor.tooltip.forcout";
	public static final String THROUPUT_MONITOR_ACT_INF_TOOLTIP = "throuputmonitor.tooltip.actinf";
	public static final String THROUPUT_MONITOR_ACT_OUT_TOOLTIP = "throuputmonitor.tooltip.actout";
	public static final String THROUPUT_MONITOR_UNSET_OUT_TOOLTIP = "throuputmonitor.tooltip.unsetout";
	public static final String THROUPUT_MONITOR_THRESHOLD_1_TOOLTIP = "throuputmonitor.tooltip.threshold1";
	public static final String THROUPUT_MONITOR_THRESHOLD_2_TOOLTIP = "throuputmonitor.tooltip.threshold2";
	public static final String THROUPUT_MONITOR_CURRENT_TOOLTIP = "throuputmonitor.tooltip.current";
	public static final String THROUPUT_MONITOR_ACTUALS_TOOLTIP = "throuputmonitor.tooltip.actuals";
	public static final String THROUPUT_MONITOR_FORECASTED_TOOLTIP = "throuputmonitor.tooltip.forecasted";
	public static final String THROUPUT_MONITOR_THROUGHPUT_RATIOS_TOOLTIP = "throuputmonitor.tooltip.throughputratios";
	
	public static final String THROUPUT_MONITOR_ALERTING_TAGNAME = "alerting";
	public static final String THROUPUT_MONITOR_ENTITY_TAGNAME = "entity";
	public static final String THROUPUT_MONITOR_CCY_TAGNAME = "ccy";
	public static final String THROUPUT_MONITOR_ILM_GROUP_TAGNAME = "ilm_group";
	public static final String THROUPUT_MONITOR_FORC_INF_TAGNAME = "forcinf";
	public static final String THROUPUT_MONITOR_FORC_OUT_TAGNAME = "forcout";
	public static final String THROUPUT_MONITOR_ACT_INF_TAGNAME = "actinf";
	public static final String THROUPUT_MONITOR_ACT_OUT_TAGNAME = "actout";
	public static final String THROUPUT_MONITOR_UNSET_OUT_TAGNAME = "unsetout";
	public static final String THROUPUT_MONITOR_THRESHOLD_1_TAGNAME = "threshold1";
	public static final String THROUPUT_MONITOR_THRESHOLD_2_TAGNAME = "threshold2";
	public static final String THROUPUT_MONITOR_CURRENT_TAGNAME = "current";
	public static final String THROUPUT_MONITOR_ACTUALS_TAGNAME = "actuals";
	public static final String THROUPUT_MONITOR_FORECASTED_TAGNAME = "forecasted";
	public static final String THROUPUT_MONITOR_THROUGHPUT_RATIOS_TAGNAME = "throughputratios";
	
	public static final String THROUPUT_THRESHOLD1_COLOR_TAGNAME = "threshold1Color";
	public static final String THROUPUT_THRESHOLD2_COLOR_TAGNAME = "threshold2Color";
	
	
	
		public static final String MSD_MOVEMENT_POSITION_HEADING = "movement.position";
	public static final String MSD_MOVEMENT_VALUE_HEADING = "movement.value";
	public static final String MSD_MOVEMENT_AMOUNT1_HEADING = "movement.amount1";
	public static final String MSD_MOVEMENT_SIGN_HEADING = "movement.sign";
	public static final String MSD_MOVEMENT_CCY_HEADING = "movement.ccy";
	public static final String MSD_MOVEMENT_REFERENCE1_HEADING = "movement.reference1";
	public static final String MSD_MOVEMENT_ACCOUNT_HEADING = "movement.account";
	public static final String MSD_MOVEMENT_INPUT_HEADING = "movement.input";
	public static final String MSD_MOVEMENT_COUNTERPARTYID_HEADING = "movement.counterPartyId";
	public static final String MSD_MOVEMENT_PRED_HEADING = "movement.pred";
	public static final String MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_HEADING = "label.movement.externalBalanceStatus";
	public static final String MSD_MOVEMENT_STATUS_HEADING = "movement.status";
	public static final String MSD_MOVEMENT_MATCHID_HEADING = "movement.matchId";
	public static final String MSD_MOVEMENT_SOURCE_HEADING = "movement.source";
	public static final String MSD_MOVEMENT_MSGFORMAT_HEADING = "movement.msgformat";
	public static final String MSD_MOVEMENT_NOTES_HEADING = "movement.notes";
	public static final String MSD_MOVEMENT_BENEFICIARY_HEADING = "movement.beneficiary";
	public static final String MSD_MOVEMENT_REFERENCE2_HEADING = "movement.reference2";
	public static final String MSD_MOVEMENT_REFERENCE3_HEADING = "movement.reference3";
	public static final String MSD_MOVEMENT_MOVEMENTID_HEADING = "movement.movementId";
	public static final String MSD_MOVEMENT_BOOKCODE_HEADING = "movement.bookcode";
	public static final String MSD_MOVEMENT_CUSTODIAN_HEADING = "movement.custodian";
	public static final String MSD_MOVEMENT_EXTRAREF_HEADING = "movement.extraRef";
	public static final String MSD_MOVEMENT_UPDATE_DATE_HEADING = "movement.update_date";
	public static final String MSD_MOVEMENT_MATCHINGPARTY_HEADING = "movement.matchingParty";
	public static final String MSD_MOVEMENT_PRODUCTTYPE_HEADING = "movement.productType";
	public static final String MSD_MOVEMENT_POSTINGDATEMSD_HEADING = "movement.postingDateMSD";
	
	
	
	
	public static final String MSD_MOVEMENT_POSITION_TAGNAME                    = "position";
	public static final String MSD_MOVEMENT_VALUE_TAGNAME                       = "value";
	public static final String MSD_MOVEMENT_AMOUNT1_TAGNAME                     = "amount1";
	public static final String MSD_MOVEMENT_SIGN_TAGNAME                        = "sign";
	public static final String MSD_MOVEMENT_CCY_TAGNAME                         = "ccy";
	public static final String MSD_MOVEMENT_REFERENCE1_TAGNAME                  = "reference1";
	public static final String MSD_MOVEMENT_ACCOUNT_TAGNAME                     = "account";
	public static final String MSD_MOVEMENT_INPUT_TAGNAME                       = "input";
	public static final String MSD_MOVEMENT_COUNTERPARTYID_TAGNAME              = "counterPartyId";
	public static final String MSD_MOVEMENT_PRED_TAGNAME                        = "pred";
	public static final String MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TAGNAME = "externalBalanceStatus";
	public static final String MSD_MOVEMENT_STATUS_TAGNAME                      = "status";
	public static final String MSD_MOVEMENT_MATCHID_TAGNAME                     = "matchId";
	public static final String MSD_MOVEMENT_SOURCE_TAGNAME                      = "source";
	public static final String MSD_MOVEMENT_MSGFORMAT_TAGNAME                   = "msgformat";
	public static final String MSD_MOVEMENT_NOTES_TAGNAME                       = "notes";
	public static final String MSD_MOVEMENT_BENEFICIARY_TAGNAME                 = "beneficiary";
	public static final String MSD_MOVEMENT_REFERENCE2_TAGNAME                  = "reference2";
	public static final String MSD_MOVEMENT_REFERENCE3_TAGNAME                  = "reference3";
	public static final String MSD_MOVEMENT_MOVEMENTID_TAGNAME                  = "movementId";
	public static final String MSD_MOVEMENT_BOOKCODE_TAGNAME                    = "bookcode";
	public static final String MSD_MOVEMENT_CUSTODIAN_TAGNAME                   = "custodian";
	public static final String MSD_MOVEMENT_EXTRAREF_TAGNAME                    = "extraRef";
	public static final String MSD_MOVEMENT_UPDATE_DATE_TAGNAME                 = "update_date";
	public static final String MSD_MOVEMENT_MATCHINGPARTY_TAGNAME               = "matchingParty";
	public static final String MSD_MOVEMENT_PRODUCTTYPE_TAGNAME                 = "productType";
	public static final String MSD_MOVEMENT_POSTINGDATEMSD_TAGNAME              = "postingDateMSD";
	
	
	

	public static final String MSD_MOVEMENT_POSITION_TOOLTIP                    = "tooltip.movement.position";
	public static final String MSD_MOVEMENT_VALUE_TOOLTIP                       = "tooltip.movement.value";
	public static final String MSD_MOVEMENT_AMOUNT1_TOOLTIP                     = "tooltip.movement.amount1";
	public static final String MSD_MOVEMENT_SIGN_TOOLTIP                        = "tooltip.movement.sign";
	public static final String MSD_MOVEMENT_CCY_TOOLTIP                         = "tooltip.movement.ccy";
	public static final String MSD_MOVEMENT_REFERENCE1_TOOLTIP                  = "tooltip.movement.reference1";
	public static final String MSD_MOVEMENT_ACCOUNT_TOOLTIP                     = "tooltip.movement.account";
	public static final String MSD_MOVEMENT_INPUT_TOOLTIP                       = "tooltip.movement.input";
	public static final String MSD_MOVEMENT_COUNTERPARTYID_TOOLTIP              = "tooltip.movement.counterPartyId";
	public static final String MSD_MOVEMENT_PRED_TOOLTIP                        = "tooltip.movement.pred";
	public static final String MSD_LABEL_MOVEMENT_EXTERNALBALANCESTATUS_TOOLTIP = "tooltip.movement.externalBalanceStatus";
	public static final String MSD_MOVEMENT_STATUS_TOOLTIP                      = "tooltip.movement.status";
	public static final String MSD_MOVEMENT_MATCHID_TOOLTIP                     = "tooltip.movement.matchId";
	public static final String MSD_MOVEMENT_SOURCE_TOOLTIP                      = "tooltip.movement.source";
	public static final String MSD_MOVEMENT_MSGFORMAT_TOOLTIP                   = "tooltip.movement.msgformat";
	public static final String MSD_MOVEMENT_NOTES_TOOLTIP                       = "tooltip.movement.notes";
	public static final String MSD_MOVEMENT_BENEFICIARY_TOOLTIP                 = "tooltip.movement.beneficiary";
	public static final String MSD_MOVEMENT_REFERENCE2_TOOLTIP                  = "tooltip.movement.reference2";
	public static final String MSD_MOVEMENT_REFERENCE3_TOOLTIP                  = "tooltip.movement.reference3";
	public static final String MSD_MOVEMENT_MOVEMENTID_TOOLTIP                  = "tooltip.movement.movementId";
	public static final String MSD_MOVEMENT_BOOKCODE_TOOLTIP                    = "tooltip.movement.bookcode";
	public static final String MSD_MOVEMENT_CUSTODIAN_TOOLTIP                   = "tooltip.movement.custodian";
	public static final String MSD_MOVEMENT_EXTRAREF_TOOLTIP                    = "tooltip.movement.extraRef";
	public static final String MSD_MOVEMENT_UPDATE_DATE_TOOLTIP                 = "tooltip.movement.update_date";
	public static final String MSD_MOVEMENT_MATCHINGPARTY_TOOLTIP               = "tooltip.movement.matchingParty";
	public static final String MSD_MOVEMENT_PRODUCTTYPE_TOOLTIP                 = "tooltip.movement.productType";
	public static final String MSD_MOVEMENT_POSTINGDATEMSD_TOOLTIP              = "tooltip.movement.postingDate";
	
	
	
	public static final String THROUPUT_CALCULATE_THROUGHPUT_AS              = "calculateAS";
	
	

	public static final String SCENARIO_DETAILS = "scenarioDetails";
	
	public static final String SCENARIO_USER = "User";
	public static final String SCENARIO_NAME = "Name";
	public static final String SCENARIO_ROLE = "Role";
	public static final String SCENARIO_EMAIL = "Email";
	public static final String SCENARIO_ACCESS = "Access";
	
	public static final String SCENARIO_GUI_SEQ = "Seq";
	public static final String SCENARIO_GUI_IDDESCRIPTION = "ID-Description";
	public static final String SCENARIO_GUI_PARAMETERS = "Parameters";
	public static final String SCENARIO_GUI_SELECT = "Select";
	public static final String SCENARIO_GUI_OTHER_ID = "Other ID";
	public static final String SCENARIO_GUI_REQUIRED_SCENARIO_INSTANCE = "reqScenInstance";
	
	public static final String SCENARIO_GUI_REQ = "Reqd";
	public static final String SCENARIO_GUI_ID = "ID";
	public static final String SCENARIO_GUI_DESCRIPTION = "Description";
	public static final String SCENARIO_GUI_TYPE = "Type";
	public static final String SCENARIO_GUI_MAP_FROM = "Map From";
	
	public static final String SCENARIO_EVENT_SEQ = "Seq";
	public static final String SCENARIO_EVENT_FACILITY_DESCRIPTION = "Description";
	public static final String SCENARIO_EVENT_FACILITY_ID = "ID";
	public static final String SCENARIO_EVENT_PARAMETERS = "Parameters";
	public static final String SCENARIO_EVENT_REPEAT = "Repeat?";
	public static final String SCENARIO_EVENT_XML = "Xml";
	public static final String SCENARIO_MAP_KEY_XML = "MapKey";
	public static final String SCENARIO_EVENT_REQ = "Reqd";
	public static final String SCENARIO_EVENT_ID = "ID";
	public static final String SCENARIO_EVENT_DESCRIPTION = "Description";
	public static final String SCENARIO_EVENT_TYPE = "Type";
	public static final String SCENARIO_EVENT_MAP_FROM = "Map From";
	public static final String SCENARIO_EVENT_MAP_FROM_VAL = "Map From Value";
	public static final String SCENARIO_EVENT_ADDITIONAL_INFO= "Additional Info";
	public static final String SCENARIO_EVENT_REGULAR_EXP= "Regular Expression";
	public static final String SCENARIO_EVENT_REGULAR_EXP_MSG= "Regular Expression Msg";
	
	public static final String SCENARIO_GENERAL_SCHED_ID = "Scheduler ID";
	public static final String SCENARIO_GENERAL_TIME = "Time";
	public static final String SCENARIO_GENERAL_PARAMS = "Parameters";
	
	public static final String SCENARIO_GENERAL_NAME = "Name";
	public static final String SCENARIO_GENERAL_DESC = "Description";
	
	public static final String SCENARIO_GENERAL_SCHED_PARAM = "Parameter";
	public static final String SCENARIO_GENERAL_SCHED_VAL = "Value";
	
	public static final String ILM_OPTIONS_CCYENTITY_HEADING = "ilm.options.ccyEntityGrp";
	public static final String ILM_OPTIONS_GLOBAL_HEADING = "ilm.options.global";
	public static final String ILM_OPTIONS_SUMMARY_HEADING = "ilm.options.summary";
	public static final String ILM_OPTIONS_ORDER_HEADING = "ilm.options.order";
	public static final String ILM_OPTIONS_ILMTAB_HEADING = "ilm.options.ilmTab";
	public static final String ILM_OPTIONS_TABDATE_HEADING = "ilm.options.TabDate";
	public static final String ILM_OPTIONS_GLOBAL_CHART_HEADING = "ilm.options.globalChart";
	public static final String ILM_OPTIONS_GROUP_ANALYSIS_HEADING = "ilm.options.groupAnalysis";
	public static final String ILM_OPTIONS_COMBINED_CHART_HEADING = "ilm.options.combinedChart";
	
	public static final String ILM_OPTIONS_CCYENTITY_HEADING_TOOLTIP = "ilm.options.ccyEntityGrp_toolTip";
	public static final String ILM_OPTIONS_GLOBAL_HEADING_TOOLTIP = "ilm.options.global_toolTip";
	public static final String ILM_OPTIONS_SUMMARY_HEADING_TOOLTIP = "ilm.options.summary_toolTip";
	public static final String ILM_OPTIONS_ORDER_HEADING_TOOLTIP = "ilm.options.order_toolTip";
	public static final String ILM_OPTIONS_ILMTAB_HEADING_TOOLTIP = "ilm.options.ilmTab_toolTip";
	public static final String ILM_OPTIONS_TABDATE_HEADING_TOOLTIP = "ilm.options.TabDate_toolTip";
	public static final String ILM_OPTIONS_GLOBAL_CHART_HEADING_TOOLTIP = "ilm.options.globalChart_toolTip";
	public static final String ILM_OPTIONS_GROUP_ANALYSIS_HEADING_TOOLTIP = "ilm.options.groupAnalysis_toolTip";
	public static final String ILM_OPTIONS_COMBINED_CHART_HEADING_TOOLTIP = "ilm.options.combinedChart_toolTip";

	/********PRE ADVICE INPUT*********/

	public static final String PRE_ADVICE_INPUT = "PreAdviceInput";
	public static final String ACCOUNT_LIST = "AccountList";
	public static final String BOOK_CODE_LIST = "BookCodeList";
	public static final String DATA_DEFINITION = "dataDefinition";
	public static final String REFRESH_COMBO_LIST = "RefreshedComboList";
	public static final String MOVEMENT = "Movement";
	public static final String PRED_STATUS = "Pred_Status";
	public static final String STATUS = "Status";
	public static final String MVT_TYPE = "Type";
	public static final String PREADVCIE_ENTITY_ID = "Entity_ID";
	public static final String CCY = "Ccy";
	public static final String ACCOUNT_ID1 = "Account_ID";
	public static final String VALUE_DATE = "Value_Date";
	public static final String AMOUNT = "Amount";
	public static final String SIGN = "Sign";
	public static final String REFERENCE = "Reference";
	public static final String COUNTER_PARTY_ID = "CounterParty_ID";
	public static final String CPAERTY_TEXT = "Cparty_Text";
	public static final String MATCH_PARTY = "Match_Party";
	public static final String PRODUCT_TYPE = "Product_Type";
	public static final String BOOK_CODE = "Book_Code";
	public static final String POST_DATE = "Post_Date";
	public static final String INPUT_BY = "Input_By";
	public static final String INPUT_AT = "Input_At";

	public static final String ENTITY_ID_HEADER  = "Entity";
	public static final String MOVEMENT_HEADER  = "ID";
	public static final String MVT_TYPE_HEADER  = "TYPE";
	public static final String PRED_STATUS_HEADER  = "Pred Status";
	public static final String STATUS_HEADER  = "Status";
	public static final String CCY_HEADER  = "Ccy";
	public static final String ACCOUNT_ID1_HEADER  = "Account ID";
	public static final String VALUE_DATE_HEADER  = "Value Date";
	public static final String AMOUNT_HEADER  = "Amount";
	public static final String SIGN_HEADER  = "Sign";
	public static final String REFERENCE_HEADER  = "Reference";
	public static final String COUNTER_PARTY_ID_HEADER  = "CounterParty ID";
	public static final String CPAERTY_TEXT_HEADER  = "Cparty Text";
	public static final String MATCH_PARTY_HEADER  = "Match Party";
	public static final String PRODUCT_TYPE_HEADER  = "Product Type";
	public static final String BOOK_CODE_HEADER  = "Book Code";
	public static final String POST_DATE_HEADER  = "Post Date";
	public static final String INPUT_BY_HEADER  = "Input By";
	public static final String INPUT_AT_HEADER  = "Input At";
	
	
	
	
	/** INSTANCE screen **/

	public static final String INSTANCE_ID_TAGNAME = "id";
	public static final String INSTANCE_SCENARIO_ID_TAGNAME = "scenarioId";
	public static final String INSTANCE_UNIQUE_IDENTIFIER_TAGNAME = "uniqueIdentifier";
	public static final String INSTANCE_STATUS_TAGNAME = "status";
	public static final String INSTANCE_RAISED_DATETIME_TAGNAME = "raisedDatetime";
	public static final String INSTANCE_RAISED_USER_TAGNAME = "raisedUser";
	public static final String INSTANCE_LAST_RAISED_DATETIME_TAGNAME = "lastRaisedDatetime";
	public static final String INSTANCE_LAST_RAISED_USER_TAGNAME = "lastRaisedUser";
	public static final String INSTANCE_RESOLVED_DATETIME_TAGNAME = "resolvedDatetime";
	public static final String INSTANCE_RESOLVED_BY_USER_TAGNAME = "resolvedByUser";
	public static final String INSTANCE_EVENTS_LAUNCH_STATUS_TAGNAME = "eventsLaunchStatus";
	public static final String INSTANCE_HOST_ID_TAGNAME = "hostId";
	public static final String INSTANCE_ENTITY_ID_TAGNAME = "entityId";
	public static final String INSTANCE_CURRENCY_CODE_TAGNAME = "currencyCode";
	public static final String INSTANCE_ACCOUNT_ID_TAGNAME = "accountId";
	public static final String INSTANCE_AMOUNT_TAGNAME = "amount";
	public static final String INSTANCE_SIGN_TAGNAME = "sign";
	public static final String INSTANCE_OVER_THRESHOLD_TAGNAME = "overThreshold";
	public static final String INSTANCE_MOVEMENT_ID_TAGNAME = "movementId";
	public static final String INSTANCE_MATCH_ID_TAGNAME = "matchId";
	public static final String INSTANCE_SWEEP_ID_TAGNAME = "sweepId";
	public static final String INSTANCE_PAYMENT_ID_TAGNAME = "paymentId";
	public static final String INSTANCE_ATTRIBUTES_JSON_TAGNAME = "attributesXml";
	public static final String INSTANCE_VALUE_DATE_TAGNAME = "valueDate";
	public static final String INSTANCE_OTHER_ID_TAGNAME = "otherId";  
	public static final String INSTANCE_OTHER_ID_TYPE_TAGNAME = "otherIdType";
	
	
    
	public static final String INSTANCE_ID_HEADER = "instancerecord.id_header";
	public static final String INSTANCE_SCENARIO_ID_HEADER = "instancerecord.scenarioId_header";
	public static final String INSTANCE_UNIQUE_IDENTIFIER_HEADER = "instancerecord.uniqueIdentifier_header";
	public static final String INSTANCE_STATUS_HEADER = "instancerecord.status_header";
	public static final String INSTANCE_RAISED_DATETIME_HEADER = "instancerecord.raisedDatetime_header";
	public static final String INSTANCE_RAISED_USER_HEADER = "instancerecord.raisedUser_header";
	public static final String INSTANCE_LAST_RAISED_DATETIME_HEADER = "instancerecord.lastRaisedDatetime_header";
	public static final String INSTANCE_LAST_RAISED_USER_HEADER = "instancerecord.lastRaisedUser_header";
	public static final String INSTANCE_RESOLVED_DATETIME_HEADER = "instancerecord.resolvedDatetime_header";
	public static final String INSTANCE_RESOLVED_BY_USER_HEADER = "instancerecord.resolvedByUser_header";
	public static final String INSTANCE_EVENTS_LAUNCH_STATUS_HEADER = "instancerecord.eventsLaunchStatus_header";
	public static final String INSTANCE_HOST_ID_HEADER = "instancerecord.hostId_header";
	public static final String INSTANCE_ENTITY_ID_HEADER = "instancerecord.entityId_header";
	public static final String INSTANCE_CURRENCY_CODE_HEADER = "instancerecord.currencyCode_header";
	public static final String INSTANCE_ACCOUNT_ID_HEADER = "instancerecord.accountId_header";
	public static final String INSTANCE_AMOUNT_HEADER = "instancerecord.amount_header";
	public static final String INSTANCE_SIGN_HEADER = "instancerecord.sign_header";
	public static final String INSTANCE_OVER_THRESHOLD_HEADER = "instancerecord.overThreshold_header";
	public static final String INSTANCE_MOVEMENT_ID_HEADER = "instancerecord.movementId_header";
	public static final String INSTANCE_MATCH_ID_HEADER = "instancerecord.matchId_header";
	public static final String INSTANCE_SWEEP_ID_HEADER = "instancerecord.sweepId_header";
	public static final String INSTANCE_PAYMENT_ID_HEADER = "instancerecord.paymentId_header";
	public static final String INSTANCE_ATTRIBUTES_JSON_HEADER = "instancerecord.attributesJson_header";
	public static final String INSTANCE_VALUE_DATE_HEADER = "instancerecord.valueDate_header";
	public static final String INSTANCE_OTHER_ID_HEADER = "instancerecord.otherId_header"; 
	public static final String INSTANCE_OTHER_ID_TYPE_HEADER = "instancerecord.otherIdType_header";
	
	public static final String INSTANCE_ID_TOOLTIP = "instancerecord.id_tooltip";
	public static final String INSTANCE_SCENARIO_ID_TOOLTIP = "instancerecord.scenarioId_tooltip";
	public static final String INSTANCE_UNIQUE_IDENTIFIER_TOOLTIP = "instancerecord.uniqueIdentifier_tooltip";
	public static final String INSTANCE_STATUS_TOOLTIP = "instancerecord.status_tooltip";
	public static final String INSTANCE_RAISED_DATETIME_TOOLTIP = "instancerecord.raisedDatetime_tooltip";
	public static final String INSTANCE_RAISED_USER_TOOLTIP = "instancerecord.raisedUser_tooltip";
	public static final String INSTANCE_LAST_RAISED_DATETIME_TOOLTIP = "instancerecord.lastRaisedDatetime_tooltip";
	public static final String INSTANCE_LAST_RAISED_USER_TOOLTIP = "instancerecord.lastRaisedUser_tooltip";
	public static final String INSTANCE_RESOLVED_DATETIME_TOOLTIP = "instancerecord.resolvedDatetime_tooltip";
	public static final String INSTANCE_RESOLVED_BY_USER_TOOLTIP = "instancerecord.resolvedByUser_tooltip";
	public static final String INSTANCE_EVENTS_LAUNCH_STATUS_TOOLTIP = "instancerecord.eventsLaunchStatus_tooltip";
	public static final String INSTANCE_HOST_ID_TOOLTIP = "instancerecord.hostId_tooltip";
	public static final String INSTANCE_ENTITY_ID_TOOLTIP = "instancerecord.entityId_tooltip";
	public static final String INSTANCE_CURRENCY_CODE_TOOLTIP = "instancerecord.currencyCode_tooltip";
	public static final String INSTANCE_ACCOUNT_ID_TOOLTIP = "instancerecord.accountId_tooltip";
	public static final String INSTANCE_AMOUNT_TOOLTIP = "instancerecord.amount_tooltip";
	public static final String INSTANCE_SIGN_TOOLTIP = "instancerecord.sign_tooltip";
	public static final String INSTANCE_OVER_THRESHOLD_TOOLTIP = "instancerecord.overThreshold_tooltip";
	public static final String INSTANCE_MOVEMENT_ID_TOOLTIP = "instancerecord.movementId_tooltip";
	public static final String INSTANCE_MATCH_ID_TOOLTIP = "instancerecord.matchId_tooltip";
	public static final String INSTANCE_SWEEP_ID_TOOLTIP = "instancerecord.sweepId_tooltip";
	public static final String INSTANCE_PAYMENT_ID_TOOLTIP = "instancerecord.paymentId_tooltip";
	public static final String INSTANCE_ATTRIBUTES_JSON_TOOLTIP = "instancerecord.attributesJson_tooltip";
	public static final String INSTANCE_VALUE_DATE_TOOLTIP = "instancerecord.valueDate_tooltip";
	public static final String INSTANCE_OTHER_ID_TOOLTIP = "instancerecord.otherId_tooltip";
	/***systFomrat************/
	public static final String CURRENCYPATTERN = "currencyPattern";
	public static final String PREADVICEINPUT_OPTIONS = "PreAdviceInputOptions";
	public static final String PREADVICEINPUT_EXCEL = "PreAdviceInputExcelOptions";
	public static final String PREADVICEINPUT_CSV = "PreAdviceInputCsvOptions";
	public static final String PREADVICEINPUT_CLIPBOARD = "PreAdviceInputClipOptions";
	public static final String PREADVICEINPUT_ID = "28";
	
	public static final String WORKFLOW_DIVIDER_OPTION = "WorkflowDividerClosed";
	public static final String WORKFLOW_APPLY_THRESHOLD_OPTION = "WorkflowApplyCcyThreshold";
	
//Scenario instance log grid
	
	public static final String INSTANCE_DATETIME_LOG_TAGNAME = "Date_time";
	public static final String INSTANCE_USER_LOG_TAGNAME = "user";
	public static final String INSTANCE_TEXT_LOG_TAGNAME = "text";

	public static final String INSTANCE_DATETIME_LOG_HEADER = "instancerecord.dateTimeLog_header";
	public static final String INSTANCE_USER_LOG_HEADER = "instancerecord.userLog_header";
	public static final String INSTANCE_TEXT_LOG_HEADER = "instancerecord.textLog_header";
		

	public static final String INSTANCE_DATETIME_LOG_TOOLTIP = "instancerecord.dateTimeLog_tooltip";
	public static final String INSTANCE_USER_LOG_TOOLTIP = "instancerecord.userLog_tooltip";
	public static final String INSTANCE_TEXT_LOG_TOOLTIP = "instancerecord.textLog_tooltip";
	
	public static final String INSTANCE_MSG_FORMATID_TAGNAME = "formatId";
	public static final String INSTANCE_MSG_MESSAGEID_TAGNAME = "messageId";
	public static final String INSTANCE_MSG_INPUTDATE_TAGNAME = "inputDate";
	public static final String INSTANCE_MSG_UPDATEDATE_TAGNAME = "updateDate";
	public static final String INSTANCE_MSG_UPDATEUSER_TAGNAME = "updateUser";
	
	public static final String INSTANCE_MSG_FORMATID_HEADER = "instancemessage.formatId_header";
	public static final String INSTANCE_MSG_MESSAGEID_HEADER = "instancemessage.messageId_header";
	public static final String INSTANCE_MSG_INPUTDATE_HEADER = "instancemessage.inputDate_header";
	public static final String INSTANCE_MSG_UPDATEDATE_HEADER = "instancemessage.updateDate_header";
	public static final String INSTANCE_MSG_UPDATEUSER_HEADER = "instancemessage.updateUser_header";
		
	public static final String INSTANCE_MSG_FORMATID_TOOLTIP = "instancemessage.formatId_tooltip";
	public static final String INSTANCE_MSG_MESSAGEID_TOOLTIP = "instancemessage.messageId_tooltip";
	public static final String INSTANCE_MSG_INPUTDATE_TOOLTIP = "instancemessage.inputDate_tooltip";
	public static final String INSTANCE_MSG_UPDATEDATE_TOOLTIP = "instancemessage.updateDate_tooltip";
	public static final String INSTANCE_MSG_UPDATEUSER_TOOLTIP = "instancemessage.updateUser_tooltip";
		
	public static final String INSTANCE_EXPAND_HEADER = "instancerecord.expand_header";
	public static final String INSTANCE_ENTITY_HEADER = "instancerecord.entityId_header";
	public static final String INSTANCE_CCY_HEADER = "instancerecord.currencyCode_header";
	public static final String INSTANCE_COUNT_HEADER = "instancerecord.count_header";
	
	public static final String INSTANCE_EXPAND_TOOLTIP = "instancerecord.expand_tooltip";
	public static final String INSTANCE_ENTITY_TOOLTIP = "instancerecord.entityId_tooltip";
	public static final String INSTANCE_CCY_TOOLTIP = "instancerecord.currencyCode_tooltip";
	public static final String INSTANCE_COUNT_TOOLTIP = "instancerecord.count_tooltip";
	
	public static final String INVALID_INSTANCE_ID = "invalid_instance_id";
	public static final String ACCESS_RESTRICTIONS = "access_restrictions";

	//Account Maintenance Angular
	public static final String ACCOUNT_MAINTENANCE_COMPONENT = "acctMaintenance";
			
	public static final String ACCOUNT_ENTITY_ID_TAGNAME = "entityId";
	public static final String ACCOUNT_ACCOUNT_ID_TAGNAME = "accountId";
	public static final String ACCOUNT_ACCOUNT_NAME_TAGNAME = "accountName";
	public static final String ACCOUNT_CURRENCY_CODE_TAGNAME = "currencyCode";
	public static final String ACCOUNT_CORRES_ACC_ID_TAGNAME = "corresAccId";
	public static final String ACCOUNT_ACCOUNT_TYPE_TAGNAME = "accountType";
	public static final String ACCOUNT_MAIN_ACCOUNT_ID_TAGNAME = "mainAccountId";
	public static final String ACCOUNT_ACCOUNT_LEVEL_TAGNAME = "accountLevel";
	public static final String ACCOUNT_CUT_OFF_TAGNAME = "cutOff";
	public static final String ACCOUNT_LINK_ACCOUNT_ID_TAGNAME = "linkAccountId";
	public static final String ACCOUNT_BIC_TAGNAME = "bic";
	public static final String ACCOUNT_ACCOUNT_CLASS_TAGNAME = "accountClass";
	public static final String ACCOUNT_IBAN_TAGNAME = "iban";
	public static final String ACCOUNT_IS_ILM_TAGNAME = "isIlm";
	public static final String ACCOUNT_ACCOUNT_PARTY_ID_TAGNAME = "accountPartyId";
	public static final String ACCOUNT_ACCOUNT_STATUS_TAGNAME = "status";

	public static final String ACCOUNT_ENTITY_ID_HEADING = "account.entityId";
	public static final String ACCOUNT_ACCOUNT_ID_HEADING = "account.accountId";
	public static final String ACCOUNT_ACCOUNT_NAME_HEADING = "account.accountName";
	public static final String ACCOUNT_CURRENCY_CODE_HEADING = "account.currencyCode";
	public static final String ACCOUNT_CORRES_ACC_ID_HEADING = "account.corresAccId";
	public static final String ACCOUNT_ACCOUNT_TYPE_HEADING = "account.accountType";
	public static final String ACCOUNT_MAIN_ACCOUNT_ID_HEADING = "account.mainAccountId";
	public static final String ACCOUNT_ACCOUNT_LEVEL_HEADING = "account.accountLevel";
	public static final String ACCOUNT_CUT_OFF_HEADING = "account.cutOff";
	public static final String ACCOUNT_LINK_ACCOUNT_ID_HEADING = "account.linkAccountId";
	public static final String ACCOUNT_BIC_HEADING = "account.bic";
	public static final String ACCOUNT_ACCOUNT_CLASS_HEADING = "account.accountClass";
	public static final String ACCOUNT_IBAN_HEADING = "account.iban";
	public static final String ACCOUNT_IS_ILM_HEADING = "account.isIlm";
	public static final String ACCOUNT_ACCOUNT_PARTY_ID_HEADING = "account.accountPartyId";
	public static final String ACCOUNT_ACCOUNT_STATUS_HEADING = "account.status";
	 
	public static final String ACCOUNT_ENTITY_ID_HEADING_TOOLTIP = "account.tooltip.entityId";
	public static final String ACCOUNT_ACCOUNT_ID_HEADING_TOOLTIP = "account.tooltip.accountId";
	public static final String ACCOUNT_ACCOUNT_NAME_HEADING_TOOLTIP = "account.tooltip.accountName";
	public static final String ACCOUNT_CURRENCY_CODE_HEADING_TOOLTIP = "account.tooltip.currencyCode";
	public static final String ACCOUNT_CORRES_ACC_ID_HEADING_TOOLTIP = "account.tooltip.corresAccId";
	public static final String ACCOUNT_ACCOUNT_TYPE_HEADING_TOOLTIP = "account.tooltip.accountType";
	public static final String ACCOUNT_MAIN_ACCOUNT_ID_HEADING_TOOLTIP = "account.tooltip.mainAccountId";
	public static final String ACCOUNT_ACCOUNT_LEVEL_HEADING_TOOLTIP = "account.tooltip.accountLevel";
	public static final String ACCOUNT_CUT_OFF_HEADING_TOOLTIP = "account.tooltip.cutOff";
	public static final String ACCOUNT_LINK_ACCOUNT_ID_HEADING_TOOLTIP = "account.tooltip.linkAccountId";
	public static final String ACCOUNT_BIC_HEADING_TOOLTIP = "account.tooltip.bic";
	public static final String ACCOUNT_ACCOUNT_CLASS_HEADING_TOOLTIP = "account.tooltip.accountClass";
	public static final String ACCOUNT_IBAN_HEADING_TOOLTIP = "account.tooltip.iban";
	public static final String ACCOUNT_IS_ILM_HEADING_TOOLTIP = "account.tooltip.isIlm";
	public static final String ACCOUNT_ACCOUNT_PARTY_ID_HEADING_TOOLTIP = "account.tooltip.accountPartyId";
	public static final String ACCOUNT_ACCOUNT_STATUS_TOOLTIP = "account.tooltip.status";
	
	//mantis 5443: Account Currency Period Maintenance
	
	public static final String CCY_ACC_MAINT_PERIOD_CCY_TAGNAME = "ccyCode";
	public static final String CCY_ACC_MAINT_PERIOD_ENTITY_ID_TAGNAME = "entityId";
	public static final String CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_TAGNAME = "accountId";
	public static final String CCY_ACC_MAINT_PERIOD_START_DATE_TAGNAME = "startDate";
	public static final String CCY_ACC_MAINT_PERIOD_END_DATE_TAGNAME = "endDate";
	public static final String CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_TAGNAME = "minimumReserve";
	public static final String CCY_ACC_MAINT_PERIOD_TIER_TAGNAME = "tier";
	public static final String CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_TAGNAME = "targetAvgBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_DAYS_TAGNAME = "fillDays";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_BALANCE_TAGNAME = "fillBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_TAGNAME = "eodBalanceSrc";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_AsSTRING_TAGNAME = "eodBalanceSrcAsSTRING";
	public static final String CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_TAGNAME = "minTargetBalance";
	public static final String CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_TAGNAME = "excludeFillDays";
	
	public static final String CCY_ACC_MAINT_PERIOD_CCY_HEADING = "ccyAccMaintPeriod.ccyCode";
	public static final String CCY_ACC_MAINT_PERIOD_ENTITY_ID_HEADING = "ccyAccMaintPeriod.entityId";
	public static final String CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_HEADING = "ccyAccMaintPeriod.accountId";
	public static final String CCY_ACC_MAINT_PERIOD_START_DATE_HEADING = "ccyAccMaintPeriod.startDate";
	public static final String CCY_ACC_MAINT_PERIOD_END_DATE_HEADING = "ccyAccMaintPeriod.endDate";
	public static final String CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_HEADING = "ccyAccMaintPeriod.minimumReserve";
	public static final String CCY_ACC_MAINT_PERIOD_TIER_HEADING = "ccyAccMaintPeriod.tier";
	public static final String CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_HEADING = "ccyAccMaintPeriod.targetAvgBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_DAYS_HEADING = "ccyAccMaintPeriod.fillDays";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_BALANCE_HEADING = "ccyAccMaintPeriod.fillBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING = "ccyAccMaintPeriod.eodBalanceSrc";
	public static final String CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_HEADING = "ccyAccMaintPeriod.minTargetBalance";
	public static final String CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_HEADING = "ccyAccMaintPeriod.excludeFillDays";

	
	public static final String CCY_ACC_MAINT_PERIOD_CCY_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.ccyCode";
	public static final String CCY_ACC_MAINT_PERIOD_ENTITY_ID_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.entityId";
	public static final String CCY_ACC_MAINT_PERIOD_ACCOUNT_ID_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.accountId";
	public static final String CCY_ACC_MAINT_PERIOD_START_DATE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.startDate";
	public static final String CCY_ACC_MAINT_PERIOD_END_DATE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.endDate";
	public static final String CCY_ACC_MAINT_PERIOD_MINIMUM_RESERVE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.minimumReserve";
	public static final String CCY_ACC_MAINT_PERIOD_TIER_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.tier";
	public static final String CCY_ACC_MAINT_PERIOD_TARGET_AVG_BALANCE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.targetAvgBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_DAYS_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.fillDays";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_BALANCE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.fillBalance";
	public static final String CCY_ACC_MAINT_PERIOD_FILL_EOD_BALANCE_SRC_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.eodBalanceSrc";
	public static final String CCY_ACC_MAINT_PERIOD_MIN_TARGET_BALANCE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.minTargetBalance";
	public static final String CCY_ACC_MAINT_PERIOD_EXCLUDE_FILL_DAYS_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.excludeFillDays";

	
	
	public static final String CCY_ACC_MAINT_PERIOD = "AcctCcyMaintPeriod";
	public static final String ALL_CURRENCY_VALUE = "All Currencies";
	public static final String ALL_ENTITY_VALUE = "All Entities";
	
	//Account currency period maintenance log subScreen 
	
	public static final String CCY_ACC_MAINT_PERIOD_LOG_DATE_TAGNAME = "date";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_TIME_TAGNAME = "time";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_USER_TAGNAME = "user";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_FACILITY_TAGNAME = "tableName";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_TAGNAME = "ipAddress";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_TAGNAME = "reference";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_ACTION_TAGNAME = "action";
	
	public static final String CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING = "maintenanceLog.logDate_Date";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_TIME_HEADING = "maintenanceLog.logDate_Time";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING = "maintenanceLog.userId";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING = "maintenanceLog.ipAddress";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_FACILITY_HEADING = "maintenanceLog.tableName";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_REFERENCE_HEADING = "maintenanceLog.reference";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_ACTION_HEADING = "maintenanceLog.action";
	 
	public static final String CCY_ACC_MAINT_PERIOD_LOG_DATE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.date";
	public static final String CCY_ACC_MAINT_PERIODv_TIME_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.time";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_USER_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.user";
	public static final String CCY_ACC_MAINT_PERIOD_LOG_IP_ADDRESS_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.ipAddress";
	public static final String CCY_ACC_MAINT_PERIODv_REFERENCE_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.reference";
	public static final String CCY_ACC_MAINT_PERIODv_ACTION_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.action";
	public static final String CCY_ACC_MAINT_PERIODLOG_FACILITY_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.tableName";
	
	
	//Account currency period maintenance log subScreen 
	
	public static final String CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME = "field";
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME = "changedFrom";
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME = "changedTo";

	public static final String CCY_ACC_MAINT_PERIOD_FIELD__HEADING = "maintenanceLog.columnName"; 
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING = "maintenanceLog.oldValue";
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING = "maintenanceLog.newValue";	 
	
	public static final String CCY_ACC_MAINT_PERIOD_FIELD_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.field";
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_FROM_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.changedFrom";
	public static final String CCY_ACC_MAINT_PERIOD_CHANGED_TO_HEADING_TOOLTIP = "ccyAccMaintPeriod.tooltip.changedTo";
	
	//Mantis 5582
	
	public static final String ACCT_SWEEP_BAL_GRP = "AcctSweepBalGrp";
	
	public static final String ACC_SWEEP_BAL_GRP_ENTITY_TAGNAME = "entityId";
	public static final String ACC_SWEEP_BAL_GRP_ACCOUNT_TAGNAME = "accountId";
	public static final String ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_TAGNAME = "sweepAccountId";
	public static final String ACC_SWEEP_BAL_GRP_ACCOUNT_STATUS_TAGNAME = "sweepAccountStatus";


	public static final String ACC_SWEEP_BAL_GRP_ENTITY_HEADING = "acctSweepBalGrp.entity"; 
	public static final String ACC_SWEEP_BAL_GRP_ACCOUNT_HEADING = "acctSweepBalGrp.account";
	public static final String ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_HEADING = "acctSweepBalGrp.sweepAccount";	 
	
	public static final String ACC_SWEEP_BAL_GRP_ENTITY_HEADING_TOOLTIP = "acctSweepBalGrp.tooltip.entity";
	public static final String ACC_SWEEP_BAL_GRP_ACCOUNT_HEADING_TOOLTIP = "acctSweepBalGrp.tooltip.account";
	public static final String ACC_SWEEP_BAL_GRP_SWEEP_ACCOUNT_HEADING_TOOLTIP = "acctSweepBalGrp.tooltip.sweepAccount";
	
	public static final String SWEEP_SETTLE_METHOD_MISC_PARAM = "SWEEP_SETTLE_METHOD";
	
	public static final String ACC_LINKED = "acctLinked";
	public static final String ACC_INTEREST_RATE= "acctInterestRate";
    //linked	
	public static final String LINKED_ACCOUNT_TAGNAME = "account";
	public static final String LINKED_NAME_TAGNAME = "name";
	public static final String LINKED_CCY_TAGNAME = "ccy";
	public static final String LINKED_TYPE_TAGNAME = "type";
	public static final String LINKED_LEVEL_TAGNAME = "level";
	public static final String LINKED_CLASS_TAGNAME = "class";
	
	public static final String LINKED_ACCOUNT_HEADING = "linked.account";
	public static final String LINKED_NAME_HEADING= "linked.name";
	public static final String LINKED_CCY_HEADING = "linked.ccy";
	public static final String LINKED_TYPE_HEADING = "linked.type";
	public static final String LINKED_LEVEL_HEADING = "linked.level";
	public static final String LINKED_CLASS_HEADING = "linked.class";
	
	public static final String LINKED_ACCOUNT_HEADING_TOOLTIP = "linked.tooltip.account";
	public static final String LINKED_NAME_HEADING_TOOLTIP= "linked.tooltip.name";
	public static final String LINKED_CCY_HEADING_TOOLTIP = "linked.tooltip.ccy";
	public static final String LINKED_TYPE_HEADING_TOOLTIP = "linked.tooltip.type";
	public static final String LINKED_LEVEL_HEADING_TOOLTIP = "linked.tooltip.level";
	public static final String LINKED_CLASS_HEADING_TOOLTIP = "linked.tooltip.class";

	   //account interest rate grid
	
		public static final String RATE_DATE_TAGNAME = "date";
		public static final String RATE_CREDIT_MARGIN_TAGNAME = "ceditMargin";
		public static final String RATE_DEBIT_MARGIN_TAGNAME = "debitMargin";
		public static final String RATE_UPDATE_DATE_TAGNAME = "updateDate";
		public static final String RATE_USER_TAGNAME = "user";
		
		public static final String RATE_DATE_HEADING = "accIntRate.Date";
		public static final String RATE_CREDIT_MARGIN_HEADING= "accIntRate.Credit";
		public static final String RATE_DEBIT_MARGIN_HEADING = "accIntRate.OverDraft";
		public static final String RATE_UPDATE_DATE_HEADING = "accIntRate.UpdateDate";
		public static final String RATE_USER_HEADING = "accIntRate.UpdateUser";
		
		public static final String RATE_DATE_HEADING_TOOLTIP = "ltooltip.accIntRateDate";
		public static final String RATE_CREDIT_MARGIN_HEADING_TOOLTIP= "tooltip.accIntCredit";
		public static final String RATE_DEBIT_MARGIN_HEADING_TOOLTIP = "tooltip.accIntRateOverDraft";
		public static final String RATE_UPDATE_DATE_TOOLTIP = "tooltip.accIntRateUpdateDate";
		public static final String RATE_USER_HEADING_TOOLTIP = "tooltip.accIntRateUpdateUser";
		
		//Sweeping Tab grid
		
		public static final String SWEEPING_ENTITY_ID_TAGNAME = "entityId";
		public static final String SWEEPING_ACCOUNT_ID_TAGNAME = "accountId";	
		public static final String SWEEPING_SCHEDULE_ID_TAGNAME = "sweepScheduleId";
		public static final String SWEEPING_UNIQUE_ID_TAGNAME = "uniqueId";	
		public static final String SWEEPING_FROM_TAGNAME = "from";
		public static final String SWEEPING_TO_TAGNAME = "to";
	    public static final String SWEEPING_BAL_TAGNAME = "sweepBalance";
	    public static final String SWEEPING_SUM_ACCT_TAGNAME = "sumAccs";
	    public static final String SWEEPING_TARGET_TYPE_TAGNAME = "targetType";
	    public static final String SWEEPING_TARGET_BAL_TAGNAME = "targetBalance";
	    public static final String SWEEPING_DIRECTION_TAGNAME = "direction";
	    public static final String SWEEPING_MIN_AMOUNT_TAGNAME= "minAmount";
	    public static final String SWEEPING_ALLOW_MULTI_TAGNAME = "allowMultiple";
	    public static final String SWEEPING_BOOK_CR_TAGNAME= "bookCR";
	    public static final String SWEEPING_BOOK_DR_TAGNAME = "bookDR";
	    public static final String SWEEPING_SETTLE_METH_CR_TAGNAME = "settleMethCR";
	    public static final String SWEEPING_SETTLE_METH_DR_TAGNAME = "settleMethDR";
	    public static final String SWEEPING_ENTITY_TAGNAME = "entity";
	    public static final String SWEEPING_ACCT_TAGNAME = "accountID";
	    public static final String SWEEPING_OTHER_BOOK_CR_TAGNAME = "otherBookCR";
	    public static final String SWEEPING_OTHER_BOOK_DR_TAGNAME = "otherBookDR";	
	    public static final String SWEEPING_OTHER_SETTLE_METH_CR_TAGNAME = "otherSettleMethCR";
	    public static final String SWEEPING_OTHER_SETTLE_METH_DR_TAGNAME = "otherSettleMethDR";	
	    
	    //Sweeping columns group
	    public static final String SWEEPING_THIS_ACCOUNT_HEADING = "account.schedSweep.thisAccount";
	    public static final String SWEEPING_AGAINST_ACCOUNT_HEADING = "account.schedSweep.againstAccount";
	    
		public static final String SWEEPING_ENTITY_ID_HEADING = "account.schedSweep.heading.entityId";
		public static final String SWEEPING_ACCOUNT_ID_HEADING = "account.schedSweep.heading.accountId";
		public static final String SWEEPING_SCHEDULE_ID_HEADING = "account.schedSweep.heading.scheduleFrom";
		public static final String SWEEPING_UNIQUE_ID_HEADING = "account.schedSweep.heading.scheduleTo";
		public static final String SWEEPING_FROM_HEADING = "account.schedSweep.heading.scheduleFrom";
		public static final String SWEEPING_TO_HEADING = "account.schedSweep.heading.scheduleTo";
	    public static final String SWEEPING_BAL_HEADING = "account.schedSweep.heading.sweepFromBalanceType";
	    public static final String SWEEPING_SUM_ACCT_HEADING = "account.schedSweep.heading.sweepOnGrpBalance";
	    public static final String SWEEPING_TARGET_TYPE_HEADING = "account.schedSweep.heading.targetBalanceType";
	    public static final String SWEEPING_TARGET_BAL_HEADING = "account.schedSweep.heading.targetBalance";
	    public static final String SWEEPING_DIRECTION_HEADING = "account.schedSweep.heading.sweepDirection";
	    public static final String SWEEPING_MIN_AMOUNT_HEADING = "account.schedSweep.heading.minAmount";
	    public static final String SWEEPING_ALLOW_MULTI_HEADING = "account.schedSweep.heading.allowMultiple";
	    public static final String SWEEPING_BOOK_CR_HEADING = "account.schedSweep.heading.thisAccSweepBookcodeCr";
	    public static final String SWEEPING_BOOK_DR_HEADING = "account.schedSweep.heading.thisAccSweepBookcodeDr";
	    public static final String SWEEPING_SETTLE_METH_CR_HEADING = "account.schedSweep.heading.thisAccSettleMethodCr";
	    public static final String SWEEPING_SETTLE_METH_DR_HEADING = "account.schedSweep.heading.thisAccSettleMethodDr";
	    public static final String SWEEPING_ENTITY_HEADING = "account.schedSweep.heading.sweepEntityId";
	    public static final String SWEEPING_ACCT_HEADING = "account.schedSweep.heading.sweepAccountId";
	    public static final String SWEEPING_OTHER_BOOK_CR_HEADING = "account.schedSweep.heading.otherAccSweepBookcodeCr";
	    public static final String SWEEPING_OTHER_BOOK_DR_HEADING = "account.schedSweep.heading.otherAccSweepBookcodeDr";
	   /* public static final String LINKED_ACCOUNT_HEADING = "account.schedSweep.tooltip.thisAccSettleMethodCr";//=Settle Method CR
	    public static final String LINKED_ACCOUNT_HEADING = "account.schedSweep.tooltip.thisAccSettleMethodDr";//=Settle Method DR*/
	    
	    
		public static final String SWEEPING_ENTITY_ID_TOOLTIP = "account.schedSweep.tooltip.entityId";
		public static final String SWEEPING_ACCOUNT_ID_TOOLTIP = "account.schedSweep.tooltip.accountId";
		public static final String SWEEPING_SCHEDULE_ID_TOOLTIP = "account.schedSweep.tooltip.scheduleFrom";
		public static final String SWEEPING_UNIQUE_ID_TOOLTIP = "account.schedSweep.tooltip.scheduleTo";
		public static final String SWEEPING_FROM_HEADING_TOOLTIP = "account.schedSweep.tooltip.scheduleFrom";
		public static final String SWEEPING_TO_HEADING_TOOLTIP = "account.schedSweep.tooltip.scheduleTo";
	    public static final String SWEEPING_BAL_HEADING_TOOLTIP = "account.schedSweep.tooltip.sweepFromBalanceType";
	    public static final String SWEEPING_SUM_ACCT_HEADING_TOOLTIP = "account.schedSweep.tooltip.sweepOnGrpBalance";
	    public static final String SWEEPING_TARGET_TYPE_HEADING_TOOLTIP = "account.schedSweep.tooltip.targetBalanceType";
	    public static final String SWEEPING_TARGET_BAL_HEADING_TOOLTIP = "account.schedSweep.tooltip.targetBalance";
	    public static final String SWEEPING_DIRECTION_HEADING_TOOLTIP = "account.schedSweep.tooltip.sweepDirection";
	    public static final String SWEEPING_MIN_AMOUNT_HEADING_TOOLTIP = "account.schedSweep.tooltip.minAmount";
	    public static final String SWEEPING_ALLOW_MULTI_HEADING_TOOLTIP = "account.schedSweep.tooltip.allowMultiple";
	    public static final String SWEEPING_BOOK_CR_HEADING_TOOLTIP = "account.schedSweep.tooltip.thisAccSweepBookcodeCr";
	    public static final String SWEEPING_BOOK_DR_HEADING_TOOLTIP = "account.schedSweep.tooltip.thisAccSweepBookcodeDr";
	    public static final String SWEEPING_SETTLE_METH_CR_HEADING_TOOLTIP = "account.schedSweep.tooltip.thisAccSettleMethodCr";
	    public static final String SWEEPING_SETTLE_METH_DR_HEADING_TOOLTIP = "account.schedSweep.tooltip.thisAccSettleMethodDr";
	    public static final String SWEEPING_ENTITY_HEADING_TOOLTIP = "account.schedSweep.tooltip.sweepEntityId";
	    public static final String SWEEPING_ACCT_HEADING_TOOLTIP = "account.schedSweep.tooltip.sweepAccountId";
	    public static final String SWEEPING_OTHER_BOOK_CR_HEADING_TOOLTIP = "account.schedSweep.tooltip.otherAccSweepBookcodeCr";
	    public static final String SWEEPING_OTHER_BOOK_DR_HEADING_TOOLTIP = "account.schedSweep.tooltip.otherAccSweepBookcodeDr";
		public static final String ACCT_MAINT_ADD = "acctMaintAdd";

		public static final String CASH_RSRV_BAL_MANAGMNT = "CashRsvrBalManagmnt";
	    public static final String CASH_RSRV_DATE_TAGNAME = "date";
	    public static final String CASH_RSRV_BAL_TAGNAME = "balanceTarget";
	    public static final String CASH_RSRV_RUN_AVG_TAGNAME = "runAvg";

	    
		public static final String CASH_RSRV_VALUE_DATE_TAGNAME = "valueDate";
		public static final String CASH_RSRV_BALANCE_TARGET_TAGNAME = "balanceTarget";	
		public static final String SCASH_RSRV_RUNNINGAVG_TAGNAME = "runningAvg";
		public static final String SCASH_RSRV_ROW_CORLOR_TAGNAME = "rowColor";
		public static final String SCASH_RSRV_MIN_BALANCE_REACHED = "minBalReached";
		public static final String SCASH_RSRV_ROW_IS_TOTAL_SWEEPTOTARGET_TAGNAME = "totalOrSweep";
		public static final String CASH_RSRV_VALUE_DATE_HEADING = "cashRsvrBal.heading.valueDate";
		public static final String CASH_RSRV_BALANCE_TARGET_HEADING = "cashRsvrBal.heading.balanceTarget";
		public static final String CASH_RSRV_RUNNINGAVG_HEADING = "cashRsvrBal.heading.runningAvg";
		public static final String CASH_RSRV_VALUE_DATE_TOOLTIP = "cashRsvrBal.tooltip.valueDate";
		public static final String CASH_RSRV_BALANCE_TARGET_TOOLTIP = "cashRsvrBal.tooltip.balanceTarget";
		public static final String CASH_RSRV_RUNNINGAVG_TOOLTIP = "cashRsvrBal.tooltip.runningAvg";

		
		// Authorization header it holds the JWT token
		public static final String AUTHORIZATION_HEADER = "Authorization";
		// Alert message tag
		public static final String BEARERTOKEN = "JBEARERTOKEN";
		// JWT session ID
		public static final String SESSION_ID = "sessionId";
		// Role Id
		public static final String ROLE_ID = "roleId";
		// Currency Group Id
		public static final String CURRENCY_GROUP_ID = "ccyGroupId";
		
		//Mantis 5819
		public static final String MOVEMENT_TABLE_TAGNAME  = "table";
		public static final String MOVEMENT_COLUMN_TAGNAME = "column";
		public static final String MOVEMENT_LABEL_TAGNAME  = "label";  
		public static final String MOVEMENT_SEQUENCE_TAGNAME  = "sequence";
		public static final String MOVEMENT_VALUE_TAGNAME  = "value";
		public static final String MOVEMENT_OPERATOR_TAGNAME = "operator"; 
		
		public static final String MOVEMENT_TABLE_HEADING  = "msd.heading.addColumns.table";
		public static final String MOVEMENT_COLUMN_HEADING = "msd.heading.addColumns.column";
		public static final String MOVEMENT_LABEL_HEADING  = "msd.heading.addColumns.label";
		public static final String MOVEMENT_SEQUENCE_HEADING  = "msd.heading.addColumns.sequence";
		public static final String MOVEMENT_VALUE_HEADING  = "msd.heading.addColumns.value";
		public static final String MOVEMENT_OPERATOR_HEADING = "msd.heading.addColumns.operator";
		
		public static final String MOVEMENT_TABLE_TOOLTIP  = "msd.tooltip.addColumns.table";
		public static final String MOVEMENT_COLUMN_TOOLTIP = "msd.tooltip.addColumns.column";
		public static final String MOVEMENT_LABEL_TOOLTIP  = "msd.tooltip.addColumns.label";
		public static final String MOVEMENT_SEQUENCE_TOOLTIP  = "msd.tooltip.addColumns.sequence";
		public static final String MOVEMENT_VALUE_TOOLTIP  = "msd.tooltip.addColumns.value";
		public static final String MOVEMENT_OPERATOR_TOOLTIP = "msd.tooltip.addColumns.operator";
		
		public static final String MOVEMENT_ADDITIONAL_COL = "mvtAdditionalCol";
		
		//mantis 5772
		public static final String SCEN_SUM_ALERT_SUBJ = "scenarioSummaryAlertsSubject";

		//Mantis 6162
		public static final String HOST_NOT_FOUND = "alertDisplay.hostNotFound";
		public static final String ENTITY_NOT_FOUND = "alertDisplay.entityNotFound";
		public static final String CURRENCY_NOT_FOUND = "alertDisplay.ccyNotFound";
		public static final String ACCOUNT_NOT_FOUND = "alertDisplay.accountNotFound";
		
		
		// Sweep Search List
		public static final String SWEEP_SEARCH_LIST = "SweepSearchList";		
//		public static final String ALL_CURRENCY_VALUE = "All Currencies";
//		public static final String ALL_ENTITY_VALUE = "All Entities";
		
		public static final String SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME = "valueDate";
		public static final String SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME = "currencyCode";
		public static final String SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME = "currentAmt";
		public static final String SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME = "NewAmt";
		public static final String SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME = "entityCr";		
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME = "accountIdCr";
		public static final String SWEEP_SEARCH_LIST_NAME_CR_TAGNAME = "NameCR";
		public static final String SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME = "entityDr";
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME = "accountIdDr";
		public static final String SWEEP_SEARCH_LIST_NAME_DR_TAGNAME = "NameDR";
		public static final String SWEEP_SEARCH_LIST_SWEEPID_TAGNAME = "sweepId";
		public static final String SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME = "crdIntMsg";
		public static final String SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME = "crdExtMsg";
		public static final String SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME = "drIntMsg";
		public static final String SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME = "drExtMsg";
		public static final String SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME = "sweepType";
		public static final String SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME = "sweepUser";
		public static final String SWEEP_SEARCH_LIST_STATUS1_TAGNAME = "status1";
		
		public static final String SWEEP_SEARCH_LIST_INPUT_USER_TAGNAME = "inputUser";
		public static final String SWEEP_SEARCH_LIST_SUBMIT_USER_TAGNAME = "SubmitUser";
		public static final String SWEEP_SEARCH_LIST_AUTHORIZED_USER_TAGNAME = "AuthorizedUser";
		public static final String SWEEP_SEARCH_LIST_MOVEMENTID_DR_TAGNAME = "MovementIdDr";
		public static final String SWEEP_SEARCH_LIST_MOVEMENTID_CR_TAGNAME = "MovementIdCr";
		public static final String SWEEP_SEARCH_LIST_ORIGINAL_SWEEP_AMT_ASSTRING_TAGNAME = "OriginalSweepAmtasstring";
		public static final String SWEEP_SEARCH_LIST_SUBMIT_SWEEP_AMT_ASSTRING_TAGNAME = "SubmitSweepAmtasstring";
		public static final String SWEEP_SEARCH_LIST_AUTHORIZE_SWEEP_AMT_ASSTRING_TAGNAME = "AuthorizeSweepAmtasstring";
		public static final String SWEEP_SEARCH_LIST_ENTITY_ID_TAGNAME = "EntityId";
		
		public static final String SWEEP_SEARCH_LIST_VALUE_DATE_HEADING = "sweepSearchList.valueDate";
		public static final String SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING = "sweepSearchList.currencyCode";
		public static final String SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING = "sweepSearchList.currentAmt";
		public static final String SWEEP_SEARCH_LIST_NEW_AMT_HEADING = "sweepSearchList.NewAmt";
		public static final String SWEEP_SEARCH_LIST_ENTITY_CR_HEADING = "sweepSearchList.entityCr";
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING = "sweepSearchList.accountIdCr";
		public static final String SWEEP_SEARCH_LIST_NAME_CR_HEADING = "sweepSearchList.Name";
		public static final String SWEEP_SEARCH_LIST_ENTITY_DR_HEADING = "sweepSearchList.entityDr";
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING = "sweepSearchList.accountIdDr";
		public static final String SWEEP_SEARCH_LIST_NAME_DR_HEADING = "sweepSearchList.Name";
		public static final String SWEEP_SEARCH_LIST_SWEEPID_HEADING = "sweepSearchList.sweepId";
		public static final String SWEEP_SEARCH_LIST_CRDINTMSG_HEADING = "sweepSearchList.crdIntMsg";
		public static final String SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING = "sweepSearchList.crdExtMsg";
		public static final String SWEEP_SEARCH_LIST_DRINTMSG_HEADING = "sweepSearchList.drIntMsg";
		public static final String SWEEP_SEARCH_LIST_DREXTMSG_HEADING = "sweepSearchList.drExtMsg";
		public static final String SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING = "sweepSearchList.sweepType";
		public static final String SWEEP_SEARCH_LIST_SWEEPUSER_HEADING = "sweepSearchList.sweepUser";
		public static final String SWEEP_SEARCH_LIST_STATUS1_HEADING = "sweepSearchList.status1";
	
		public static final String SWEEP_SEARCH_LIST_VALUE_DATE_HEADING_TOOLTIP = "tooltip.sortValueDate";
		public static final String SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING_TOOLTIP = "tooltip.sortCurrencyCode";
		public static final String SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING_TOOLTIP = "tooltip.sortSweepAmount";
		public static final String SWEEP_SEARCH_LIST_NEW_AMT_HEADING_TOOLTIP = "tooltip.sortSweepAmount";
		public static final String SWEEP_SEARCH_LIST_ENTITY_CR_HEADING_TOOLTIP = "tooltip.sortEntityId";
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING_TOOLTIP = "tooltip.sortCrAccID";
		public static final String SWEEP_SEARCH_LIST_NAME_CR_HEADING_TOOLTIP = "tooltip.accName";
		public static final String SWEEP_SEARCH_LIST_ENTITY_DR_HEADING_TOOLTIP = "tooltip.sortEntityId";
		public static final String SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING_TOOLTIP = "tooltip.sortDrAccID";
		public static final String SWEEP_SEARCH_LIST_NAME_DR_HEADING_TOOLTIP = "tooltip.accName";
		public static final String SWEEP_SEARCH_LIST_SWEEPID_HEADING_TOOLTIP = "tooltip.sortSweepID";
		public static final String SWEEP_SEARCH_LIST_CRDINTMSG_HEADING_TOOLTIP = "tooltip.crdIntMsg";
		public static final String SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING_TOOLTIP = "tooltip.crdExtMsg";
		public static final String SWEEP_SEARCH_LIST_DRINTMSG_HEADING_TOOLTIP = "tooltip.drIntMsg";
		public static final String SWEEP_SEARCH_LIST_DREXTMSG_HEADING_TOOLTIP = "tooltip.drExtMsg";
		public static final String SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING_TOOLTIP = "tooltip.sortSweeptype";
		public static final String SWEEP_SEARCH_LIST_SWEEPUSER_HEADING_TOOLTIP = "tooltip.sortSweepUser";
		public static final String SWEEP_SEARCH_LIST_STATUS1_HEADING_TOOLTIP = "tooltip.SweepStatus";	
	



		//Mantis 6168
		
		
		public static final String MAINT_LOG_VIEW_FIELD_TAGNAME = "field";
		public static final String MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME = "changedFrom";
		public static final String MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME = "changedTo";

		public static final String MAINT_LOG_VIEW_FIELD__HEADING = "maintenanceLog.columnName"; 
		public static final String MAINT_LOG_VIEW_CHANGED_FROM_HEADING = "maintenanceLog.oldValue";
		public static final String MAINT_LOG_VIEW_CHANGED_TO_HEADING = "maintenanceLog.newValue";	 
		
		public static final String MAINT_LOG_VIEW_FIELD_HEADING_TOOLTIP = "maintenanceLog.tooltip.field";
		public static final String MAINT_LOG_VIEW_CHANGED_FROM_HEADING_TOOLTIP = "maintenanceLog.tooltip.changedFrom";
		public static final String MAINT_LOG_VIEW_CHANGED_TO_HEADING_TOOLTIP = "maintenanceLog.tooltip.changedTo";
		
		public static final String MAINT_LOG_VIEW = "MaintLogView";
		
		
		//Mantis 6455
		
		public static final String MAINTENANCE_EVENT= "maintenanceEvent";
		public static final String MAINTENANCE_EVENT_DETAILS_XML_TAG= "maintenanceEventDetails";
		
		public static final String MAINT_EVENT_ID = "14";
		
		public static final String MAINT_EVENT_MAINT_EVENT_ID_TAGNAME = "maintEventId";
		public static final String MAINT_EVENT_MAINT_FACILITY_ID_TAGNAME = "maintFacilityId";
		public static final String MAINT_EVENT_MAINT_FACILITY_ID_VALUE_TAGNAME = "maintFacilityIdValue";
		public static final String MAINT_EVENT_ACTION_TAGNAME = "action";
		public static final String MAINT_EVENT_ACTION_VALUE_TAGNAME = "actionValue";
		public static final String MAINT_EVENT_RECORD_ID_TAGNAME = "recordId";
		public static final String MAINT_EVENT_STATUS_TAGNAME = "status";
		public static final String MAINT_EVENT_STATUS_VALUE_TAGNAME = "statusValue";
		public static final String MAINT_EVENT_REQUEST_USER_TAGNAME = "requestUser";
		public static final String MAINT_EVENT_REQUEST_DATE_TAGNAME = "requestDate";
		public static final String MAINT_EVENT_AUTH_USER_TAGNAME = "authUser";
		public static final String MAINT_EVENT_AUTH_DATE_TAGNAME = "authDate";
		public static final String MAINT_EVENT_PREV_ID_TAGNAME = "prevId";
		public static final String MAINT_EVENT_NEXT_ID_TAGNAME = "nextId";

		public static final String MAINT_EVENT_MAINT_EVENT_ID_HEADING = "maintEvent.maintEventId";
		public static final String MAINT_EVENT_MAINT_FACILITY_ID_HEADING = "maintEvent.maintFacilityId";
		public static final String MAINT_EVENT_ACTION_HEADING = "maintEvent.action";
		public static final String MAINT_EVENT_RECORD_ID_HEADING = "maintEvent.recordId";
		public static final String MAINT_EVENT_STATUS_HEADING = "maintEvent.status";
		public static final String MAINT_EVENT_REQUEST_USER_HEADING = "maintEvent.requestUser";
		public static final String MAINT_EVENT_REQUEST_DATE_HEADING = "maintEvent.requestDate";
		public static final String MAINT_EVENT_AUTH_USER_HEADING = "maintEvent.authUser";
		public static final String MAINT_EVENT_AUTH_DATE_HEADING = "maintEvent.authDate";
		public static final String MAINT_EVENT_PREV_ID_HEADING = "maintEvent.prevId";
		public static final String MAINT_EVENT_NEXT_ID_HEADING = "maintEvent.nextId";
		 
		public static final String MAINT_EVENT_MAINT_EVENT_ID_HEADING_TOOLTIP = "maintEvent.tooltip.maintEventId";
		public static final String MAINT_EVENT_MAINT_FACILITY_ID_HEADING_TOOLTIP = "maintEvent.tooltip.maintFacilityId";
		public static final String MAINT_EVENT_ACTION_HEADING_TOOLTIP = "maintEvent.tooltip.action";
		public static final String MAINT_EVENT_RECORD_ID_HEADING_TOOLTIP = "maintEvent.tooltip.recordId";
		public static final String MAINT_EVENT_STATUS_HEADING_TOOLTIP = "maintEvent.tooltip.status";
		public static final String MAINT_EVENT_REQUEST_USER_HEADING_TOOLTIP = "maintEvent.tooltip.requestUser";
		public static final String MAINT_EVENT_REQUEST_DATE_HEADING_TOOLTIP = "maintEvent.tooltip.requestDate";
		public static final String MAINT_EVENT_AUTH_USER_HEADING_TOOLTIP = "maintEvent.tooltip.authUser";
		public static final String MAINT_EVENT_AUTH_DATE_HEADING_TOOLTIP = "maintEvent.tooltip.authDate";
		public static final String MAINT_EVENT_PREV_ID_HEADING_TOOLTIP = "maintEvent.tooltip.prevId";
		public static final String MAINT_EVENT_NEXT_ID_HEADING_TOOLTIP = "maintEvent.tooltip.nextId";
		
		public static final String MAINT_EVENT_OLD_VALUE = "_oldValue";
		
		public static final String TABLE_ACCOUNT= "table.account";
		public static final String TABLE_MOVEMENT= "table.movement";
		public static final String TABLE_MOVEMENT_EXT= "table.movement.ext";
		
		public static final String OPPORTUNITY_COST_REPORT_OUTPUT_PDF = "P";
		public static final String OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL = "E";
		public static final String OPPORTUNITY_COST_REPORT_OUTPUT_CSV = "C";
		
		public static final String PROPERTY_ENV_TEST_ENABLED = "ENV_TEST_ENABLED";
		public static final String PROPERTY_ENV_TOMCAT_ENABLED = "ENV_TOMCAT_ENABLED";
		

		/********SWEEP QUEUE INPUT*********/

		public static final String SWEEP_VALUE_DATE = "valueDate";
		public static final String SWEEP_CCY_CODE = "currencyCode";
		public static final String SWEEP_CURRENT_AMT = "currentAmt";
		public static final String SWEEP_NEW_AMOUNT = "newAmt";
		public static final String SWEEP_SEARCH_ENTITY_CR = "entityCr";
		public static final String SWEEP_ACCOUNT_ID_CR = "accountIdCr";
		public static final String ACCOUNT_CR_NAME = "acctCrName";
		public static final String SWEEP_SEARCH_ENTITY_DR = "entityDr";
		public static final String SWEEP_ACCOUNT_ID_DR = "accountIdDr";
		public static final String ACCOUNT_DR_NAME = "acctDrName";
		public static final String SWEEP_SWEEP_ID = "sweepId";
		public static final String SWEEP_CRD_INT_MSG = "crdIntMsg";
		public static final String SWEEP_CRD_EXT_MSG = "crdExtMsg";
		public static final String SWEEP_DR_INT_MSG = "drIntMsg";
		public static final String SWEEP_DR_EXT_MSG = "drExtMsg";
		public static final String SWEEP_TYPE = "sweepType";
		public static final String SWEEP_USER = "sweepUser";
		public static final String SWEEP_STATUS = "sweepStatus";
		public static final String SWEEP_DATE_TIME_USER = "sweepDateTimeUser";
		
		public static final String SWEEP_VALUE_DATE_HEADER = "sweep.valueDate";
		public static final String SWEEP_CCY_CODE_HEADER = "sweep.currencyCode";
		public static final String SWEEP_CURRENT_AMT_HEADER= "sweep.currentAmt";
		public static final String SWEEP_NEW_AMOUNT_HEADER = "sweep.NewAmt";
		public static final String SWEEP_SEARCH_ENTITY_CR_HEADER = "sweepsearch.entityCr";
		public static final String SWEEP_ACCOUNT_ID_CR_HEADER = "sweep.accountIdCr";
		public static final String ACCOUNT_CR_NAME_HEADER = "movement.Name";
		public static final String SWEEP_SEARCH_ENTITY_DR_HEADER = "sweepsearch.entityDr";
		public static final String SWEEP_ACCOUNT_ID_DR_HEADER = "sweep.accountIdDr";
		public static final String ACCOUNT_DR_NAME_HEADER = "movement.Name";
		public static final String SWEEP_SWEEP_ID_HEADER = "sweep.sweepId";
		public static final String SWEEP_CRD_INT_MSG_HEADER = "sweep.crdIntMsg";
		public static final String SWEEP_CRD_EXT_MSG_HEADER = "sweep.crdExtMsg";
		public static final String SWEEP_DR_INT_MSG_HEADER = "sweep.drIntMsg";
		public static final String SWEEP_DR_EXT_MSG_HEADER = "sweep.drExtMsg";
		public static final String SWEEP_TYPE_HEADER = "sweep.sweepType";
		public static final String SWEEP_USER_HEADER = "sweep.sweepUser";
		public static final String SWEEP_STATUS_HEADER = "sweep.sweepStatus";
		public static final String SWEEP_DATE_TIME_USER_HEADER = "sweep.sweepDateTimeUser";
		
		public static final String SWEEP_QUEUE = "SweepQueue";
		
		public static final String SUBMIT_SWEEP_COL_VALUE_AUTO = "sweepSubmit.colValue.Auto";
		public static final String SUBMIT_SWEEP_COL_VALUE_MAN = "sweepSubmit.colValue.Man";
				
		public static final String SWEEP_AUTHORIZE_PANEL = "sweep.authorizePanel";
		public static final String SWEEP_OTHER_PANEL = "sweep.otherPanel";
		public static final String SWEEP_CANCEL_PANEL = "sweep.cancelPanel";
		public static final String SWEEP_SUBMIT_PANEL = "sweep.submitPanel";
		
		
		/* MESSAGE_FORMATS_TAGNAME */
		public static final String MESSAGE_FORMATS_LIST = "messageformatsList";	
		public static final String MESSAGE_FORMATS_LIST_ID_TAGNAME = "formatId";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_NAME_TAGNAME = "formatName";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_TYPE_TAGNAME = "formatType1";
		public static final String MESSAGE_FORMATS_LIST_AUTHORIZE_FLAG_TAGNAME = "authorizeFlag";
		public static final String MESSAGE_FORMATS_LIST_OUTPUT_TYPE_TAGNAME = "outputType1";
		public static final String MESSAGE_FORMATS_LIST_FILE_NAME_TAGNAME = "fileName";
		public static final String MESSAGE_FORMATS_LIST_PATH_TAGNAME = "path";
		public static final String MESSAGE_FORMATS_LIST_FIELD_DELIMITER_TAGNAME = "fieldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_HEXA_FIELD_DELIMITER_TAGNAME = "hexaFldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_MSG_SEPARATOR_TAGNAME = "msgSeparator";
		public static final String MESSAGE_FORMATS_LIST_HEXA_MSG_SEPARATOR_TAGNAME = "hexaMsgSeparator";
		
		/* MESSAGE_FORMATS_HEADING_TOOLTIP */
		public static final String MESSAGE_FORMATS_LIST_ID_HEADING_TOOLTIP = "messageFormats.tooltip.id";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_NAME_HEADING_TOOLTIP = "messageFormats.tooltip.formatName";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_TYPE_HEADING_TOOLTIP = "messageFormats.tooltip.formatType";
		public static final String MESSAGE_FORMATS_LIST_AUTHORIZE_FLAG_HEADING_TOOLTIP = "messageFormats.tooltip.authorizeFlag";
		public static final String MESSAGE_FORMATS_LIST_OUTPUT_TYPE_HEADING_TOOLTIP = "messageFormats.tooltip.outputType";
		public static final String MESSAGE_FORMATS_LIST_FILE_NAME_HEADING_TOOLTIP = "messageFormats.tooltip.fileName";
		public static final String MESSAGE_FORMATS_LIST_PATH_HEADING_TOOLTIP = "messageFormats.tooltip.path";
		public static final String MESSAGE_FORMATS_LIST_FIELD_DELIMITER_HEADING_TOOLTIP = "messageFormats.tooltip.fieldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_HEXA_FIELD_DELIMITER_HEADING_TOOLTIP = "messageFormats.tooltip.hexaFldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_MSG_SEPARATOR_HEADING_TOOLTIP = "messageFormats.tooltip.msgSeparator";
		public static final String MESSAGE_FORMATS_LIST_HEXA_MSG_SEPARATOR_HEADING_TOOLTIP = "messageFormats.tooltip.hexaMsgSeparator";
		
		/* MESSAGE_FORMATS_HEADING */
		public static final String MESSAGE_FORMATS_LIST_ID_HEADING = "messageFormats.id.formatId";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_NAME_HEADING = "messageFormats.formatName";
		public static final String MESSAGE_FORMATS_LIST_FORMAT_TYPE_HEADING = "messageFormats.formatType1";
		public static final String MESSAGE_FORMATS_LIST_AUTHORIZE_FLAG_HEADING = "messageFormats.authorizeFlag";
		public static final String MESSAGE_FORMATS_LIST_OUTPUT_TYPE_HEADING = "messageFormats.outputType1";
		public static final String MESSAGE_FORMATS_LIST_FILE_NAME_HEADING = "messageFormats.fileName";
		public static final String MESSAGE_FORMATS_LIST_PATH_HEADING = "messageFormats.path";
		public static final String MESSAGE_FORMATS_LIST_FIELD_DELIMITER_HEADING = "messageFormats.fieldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_HEXA_FIELD_DELIMITER_HEADING = "messageFormats.hexaFldDelimeter";
		public static final String MESSAGE_FORMATS_LIST_MSG_SEPARATOR_HEADING = "messageFormats.msgSeparator";
		public static final String MESSAGE_FORMATS_LIST_HEXA_MSG_SEPARATOR_HEADING = "messageFormats.hexaMsgSeparator";
		
		/*------ ERROR_LOG ------*/
		/* ERROR_LOG_TAGNAME */
		public static final String ERROR_LOG_LIST = "errorLogList";
		public static final String ERROR_LOG_LIST_ERROR_DATE_TAGNAME = "errorDate_Date";
		public static final String ERROR_LOG_LIST_ERROR_TIME_TAGNAME = "errorDate_Time";
		public static final String ERROR_LOG_LIST_USER_ID_TAGNAME = "userId";
		public static final String ERROR_LOG_LIST_SOURCE_TAGNAME = "source";
		public static final String ERROR_LOG_LIST_ERROR_TAGNAME = "error";
		public static final String ERROR_LOG_LIST_ERROR_FROMDATE_TAGNAME = "fromDate";
		public static final String ERROR_LOG_LIST_ERROR_TODATE_TAGNAME = "toDate";	
		/* ERROR_LOG_HEADING_TOOLTIP */
		public static final String ERROR_LOG_LIST_ERROR_DATE_HEADING_TOOLTIP = "tooltip.sortLogDate";
		public static final String ERROR_LOG_LIST_ERROR_TIME_HEADING_TOOLTIP = "tooltip.sortLogTime";
		public static final String ERROR_LOG_LIST_USER_ID_HEADING_TOOLTIP = "tooltip.sortUserId";
		public static final String ERROR_LOG_LIST_SOURCE_HEADING_TOOLTIP = "tooltip.sortFile";
		public static final String ERROR_LOG_LIST_ERROR_HEADING_TOOLTIP = "tooltip.errorDesc";
		/* ERROR_LOG_HEADING */
		public static final String ERROR_LOG_LIST_ERROR_DATE_HEADING = "errorLog.errorDate_Date";
		public static final String ERROR_LOG_LIST_ERROR_TIME_HEADING = "errorLog.errorDate_Time";
		public static final String ERROR_LOG_LIST_USER_ID_HEADING = "errorLog.userId";
		public static final String ERROR_LOG_LIST_SOURCE_HEADING = "errorLog.source";
		public static final String ERROR_LOG_LIST_ERROR_HEADING = "errorLog.error";
		
		/*------ MAINTENANCE_LOG ------*/
		public static final String MAINTENANCE_LOG_LIST = "maintenanceLogList";
		/* MAINTENANCE_LOG_TAGNAME */		
		public static final String MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME = "logDate_Date";
		public static final String MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME = "logDate_Time";
		public static final String MAINTENANCE_LOG_LIST_USER_ID_TAGNAME = "userId";
		public static final String MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME = "ipAddress";
		public static final String MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME = "tableName";
		public static final String MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME = "reference";
		public static final String MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME = "dupaction";

		/* MAINTENANCE_LOG_HEADING_TOOLTIP */
		public static final String MAINTENANCE_LOG_LIST_LOG_DATE_HEADING_TOOLTIP = "tooltip.sortLogDate";
		public static final String MAINTENANCE_LOG_LIST_LOG_TIME_HEADING_TOOLTIP = "tooltip.sortLogTime";
		public static final String MAINTENANCE_LOG_LIST_USER_ID_HEADING_TOOLTIP = "tooltip.sortUserId";
		public static final String MAINTENANCE_LOG_LIST_IP_ADDRESS_HEADING_TOOLTIP = "tooltip.sortIpAddress";
		public static final String MAINTENANCE_LOG_LIST_TABLE_NAME_HEADING_TOOLTIP = "tooltip.sortTableName";
		public static final String MAINTENANCE_LOG_LIST_REFERENCE_HEADING_TOOLTIP = "tooltip.sortReference";
		public static final String MAINTENANCE_LOG_LIST_DUP_ACTION_HEADING_TOOLTIP = "tooltip.sortDupAction";

		/* MAINTENANCE_LOG_HEADING */
		public static final String MAINTENANCE_LOG_LIST_LOG_DATE_HEADING = "maintenanceLog.logDate_Date";
		public static final String MAINTENANCE_LOG_LIST_LOG_TIME_HEADING = "maintenanceLog.logDate_Time";
		public static final String MAINTENANCE_LOG_LIST_USER_ID_HEADING = "maintenanceLog.userId";
		public static final String MAINTENANCE_LOG_LIST_IP_ADDRESS_HEADING = "maintenanceLog.ipAddress";
		public static final String MAINTENANCE_LOG_LIST_TABLE_NAME_HEADING = "maintenanceLog.tableName";
		public static final String MAINTENANCE_LOG_LIST_REFERENCE_HEADING = "maintenanceLog.reference";
		public static final String MAINTENANCE_LOG_LIST_DUP_ACTION_HEADING = "auditLog.id.action";

		
		/*------ SYSTEM_LOG ------*/
		public static final String SYSTEM_LOG_LIST = "systemLogList";
		/* SYSTEM_LOG_TAGNAME */		
		public static final String SYSTEM_LOG_LIST_LOG_DATE_TAGNAME = "logDate_Date";
		public static final String SYSTEM_LOG_LIST_LOG_TIME_TAGNAME = "logDate_Time";
		public static final String SYSTEM_LOG_LIST_USER_ID_TAGNAME = "userId";
		public static final String SYSTEM_LOG_LIST_IP_ADDRESS_TAGNAME = "ipAddress";
		public static final String SYSTEM_LOG_LIST_PROCESS_TAGNAME = "process";
		public static final String SYSTEM_LOG_LIST_ACTION_TAGNAME = "action";
		/* SYSTEM_LOG_HEADING_TOOLTIP */
		public static final String SYSTEM_LOG_LIST_LOG_DATE_HEADING_TOOLTIP = "tooltip.sortLogDate";
		public static final String SYSTEM_LOG_LIST_LOG_TIME_HEADING_TOOLTIP = "tooltip.sortLogTime";
		public static final String SYSTEM_LOG_LIST_USER_ID_HEADING_TOOLTIP = "tooltip.sortUserId";
		public static final String SYSTEM_LOG_LIST_IP_ADDRESS_HEADING_TOOLTIP = "tooltip.sortIpAddress";
		public static final String SYSTEM_LOG_LIST_PROCESS_HEADING_TOOLTIP = "tooltip.sortProcess";
		public static final String SYSTEM_LOG_LIST_ACTION_HEADING_TOOLTIP = "tooltip.sortAction";
		/* SYSTEM_LOG_HEADING */
		public static final String SYSTEM_LOG_LIST_LOG_DATE_HEADING = "systemLog.id.logDate";
		public static final String SYSTEM_LOG_LIST_LOG_TIME_HEADING = "systemLog.logTime";
		public static final String SYSTEM_LOG_LIST_USER_ID_HEADING = "systemLog.userId";
		public static final String SYSTEM_LOG_LIST_IP_ADDRESS_HEADING = "systemLog.ipAddress";
		public static final String SYSTEM_LOG_LIST_PROCESS_HEADING = "systemLog.process";
		public static final String SYSTEM_LOG_LIST_ACTION_HEADING = "systemLog.action";

		
		/*------ MOVEMENT_VALUE ------*/
		public static final String MANUAL_SWEEP_DETAILS_LIST = "manualSweepDetailsList";	
		/* MOVEMENT_VALUE_TAGNAME */
		public static final String MOVEMENT_VALUE_DATE_TAGNAME = "valueDate";
		public static final String MOVEMENT_LEVEL_TAGNAME = "level";
		public static final String ACCT_MAINTENANCE_ENTITY_ID_TAGNAME = "entityId";
		public static final String MOVEMENT_ACCOUNT_ID_TAGNAME = "accountId";
		public static final String MOVEMENT_NAME_TAGNAME = "name";
		public static final String MOVEMENT_BALANCES_TAGNAME = "balances";
		public static final String MOVEMENT_SWEEP_AMOUNT_TAGNAME = "sweepAmount";
		public static final String MOVEMENT_TARGET_BALANCE_TAGNAME = "targetBalance";
		public static final String MOVEMENT_CUT_OFF_TAGNAME = "cutOff";
		public static final String MOVEMENT_VALUE_DATE_ACHIEVABLE_TAGNAME = "isValueDateAchievable";
		public static final String MOVEMENT_DISPLAY_LEVEL_TAGNAME = "displayLevel";

		/* MOVEMENT_VALUE_HEADING */
		public static final String MOVEMENT_VALUE_DATE_HEADING = "movement.valueDate";
		public static final String MOVEMENT_LEVEL_HEADING = "movement.level";
		public static final String ACCT_MAINTENANCE_ENTITY_ID_HEADING = "acctMaintenance.entityId"; 
		public static final String MOVEMENT_ACCOUNT_ID_HEADING = "movement.accountId";
		public static final String MOVEMENT_NAME_HEADING = "movement.Name"; 
		public static final String MOVEMENT_BALANCES_HEADING = "movement.balances";
		public static final String MOVEMENT_SWEEP_AMOUNT_HEADING = "movement.sweepAmount";
		public static final String MOVEMENT_TARGET_BALANCE_HEADING = "movement.targetbalance"; 
		public static final String MOVEMENT_CUT_OFF_HEADING = "movement.cutOff";
		public static final String MOVEMENT_VALUE_DATE_ACHIEVABLE_HEADING = "movement.isValueDateAchievable";
		public static final String MOVEMENT_DISPLAY_LEVEL_HEADING = "movement.displayLevel";

		/* MOVEMENT_VALUE_TOOLTIP */
		public static final String MOVEMENT_VALUE_DATE_TOOLTIP = "tooltip.sortValueDate";
		public static final String MOVEMENT_LEVEL_TOOLTIP = "tooltip.sortMvmLevel";
		public static final String ACCT_MAINTENANCE_ENTITY_ID_TOOLTIP = "tooltip.sortEntityId"; 
		public static final String MOVEMENT_ACCOUNT_ID_TOOLTIP = "tooltip.sortAccountId";
		public static final String MOVEMENT_NAME_TOOLTIP = "tooltip.accName"; 
		public static final String MOVEMENT_BALANCES_TOOLTIP = "tooltip.sortPredictBalance"; 
		public static final String MOVEMENT_SWEEP_AMOUNT_TOOLTIP = "tooltip.sortSweepAmount";
		public static final String MOVEMENT_TARGET_BALANCE_TOOLTIP = "tooltip.sortTargetBalance"; 
		public static final String MOVEMENT_CUT_OFF_TOOLTIP = "tooltip.sortCutOffTime";

		public static final String TAB_TODAY1= "outstanding.tabs.today1";
		public static final String TAB_TODAY2= "outstanding.tabs.today2";
		
		/*------ BAL_MAINTENANCE ------*/
		/* BAL_MAINTENANCE_DETAILS_TAGNAME */
		public static final String BAL_MAINTENANCE_DETAILS_LIST = "balMaintenanceDetailsList";	
		public static final String BAL_MAINTENANCE_DETAILS_ALERTING = "alerting";
		public static final String BAL_MAINTENANCE_DETAILS_ACCOUNT_ID_TAGNAME = "accountId";
		public static final String BAL_MAINTENANCE_DETAILS_NAME_TAGNAME = "name";
		public static final String BAL_MAINTENANCE_DETAILS_USER_TAGNAME = "user";
		public static final String BAL_MAINTENANCE_DETAILS_BAL_CURRENCY_CODE_TAGNAME = "balCurrencyCode";
		public static final String BAL_MAINTENANCE_DETAILS_SCENARIO_HIGHLIGHTED_TAGNAME = "scenarioHighlighted";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_NEGATIVE_TAGNAME = "forecastSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_AS_STRING_TAGNAME = "forecastSODAsString";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_TYPE_AS_STRING_TAGNAME = "forecastSODTypeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_NEGATIVE_TAGNAME = "externalSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_AS_STRING_TAGNAME = "externalSODAsString";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_TYPE_AS_STRING_TAGNAME = "externalSODTypeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_CODE_TAGNAME = "reasonCode";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_DESC_TAGNAME = "reasonDesc";
		public static final String BAL_MAINTENANCE_DETAILS_USER_NOTES_TAGNAME = "userNotes";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_AS_STRING_TAGNAME = "inputDateAsString";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_TIME_AS_STRING_TAGNAME = "inputTimeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_TIME_AS_STRING_TAGNAME = "inputDateTimeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_CURRENCY_CODE_TAGNAME = "currencyCode";

		/* BAL_MAINTENANCE_DETAILS_HEADING_TOOLTIP */
		public static final String BAL_MAINTENANCE_DETAILS_ACCOUNT_ID_HEADING_TOOLTIP = "tooltip.sortByAccountId";
		public static final String BAL_MAINTENANCE_DETAILS_NAME_HEADING_TOOLTIP = "tooltip.sortByName";
		public static final String BAL_MAINTENANCE_DETAILS_USER_HEADING_TOOLTIP = "tooltip.sortByUserId";
		public static final String BAL_MAINTENANCE_DETAILS_BAL_CURRENCY_CODE_HEADING_TOOLTIP = "tooltip.sortByCurrencyCode";
		public static final String BAL_MAINTENANCE_DETAILS_SCENARIO_HIGHLIGHTED_HEADING_TOOLTIP = "tooltip.sortByScenarioHighlighted";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_NEGATIVE_HEADING_TOOLTIP = "tooltip.sortByForecastSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_AS_STRING_HEADING_TOOLTIP = "balmaintenance.effectiveForecastSOD";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_TYPE_AS_STRING_HEADING_TOOLTIP = "tooltip.sortBySOD";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_NEGATIVE_HEADING_TOOLTIP = "tooltip.sortByExternalSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_AS_STRING_HEADING_TOOLTIP = "balmaintenance.effectiveExternalSOD";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_TYPE_AS_STRING_HEADING_TOOLTIP = "tooltip.sortByExternalWorking";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_CODE_HEADING_TOOLTIP = "tooltip.sortByReasonCode";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_DESC_HEADING_TOOLTIP = "tooltip.sortByReasonDesc";
		public static final String BAL_MAINTENANCE_DETAILS_USER_NOTES_HEADING_TOOLTIP = "tooltip.sortByReasonNotes";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_AS_STRING_HEADING_TOOLTIP = "tooltip.sortByInputDate";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_TIME_AS_STRING_HEADING_TOOLTIP = "tooltip.sortByInputTime";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_TIME_AS_STRING_HEADING_TOOLTIP = "tooltip.sortByInputTime";
		public static final String BAL_MAINTENANCE_DETAILS_CURRENCY_CODE_HEADING_TOOLTIP = "tooltip.sortByCurrencyCode";



		/* BAL_MAINTENANCE_DETAILS_HEADING */
		public static final String BAL_MAINTENANCE_DETAILS_ACCOUNT_ID_HEADING = "balMaintenance.accountId";
		public static final String BAL_MAINTENANCE_DETAILS_NAME_HEADING = "balMaintenance.name";
		public static final String BAL_MAINTENANCE_DETAILS_USER_HEADING = "balMaintenance.user";
		public static final String BAL_MAINTENANCE_DETAILS_BAL_CURRENCY_CODE_HEADING = "balMaintenance.balCurrencyCode";
		public static final String BAL_MAINTENANCE_DETAILS_SCENARIO_HIGHLIGHTED_HEADING = "balMaintenance.scenarioHighlighted";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_NEGATIVE_HEADING = "balMaintenance.forecastSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_AS_STRING_HEADING = "balMaintenance.forecastSODAsString";
		public static final String BAL_MAINTENANCE_DETAILS_FORECAST_SOD_TYPE_AS_STRING_HEADING = "balMaintenance.forecastSODTypeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_NEGATIVE_HEADING = "balMaintenance.externalSODNegative";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_AS_STRING_HEADING = "balMaintenance.externalSODAsString";
		public static final String BAL_MAINTENANCE_DETAILS_EXTERNAL_SOD_TYPE_AS_STRING_HEADING = "balMaintenance.externalSODTypeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_CODE_HEADING = "balMaintenance.reasonCode";
		public static final String BAL_MAINTENANCE_DETAILS_REASON_DESC_HEADING = "balMaintenance.reasonDesc";
		public static final String BAL_MAINTENANCE_DETAILS_USER_NOTES_HEADING = "balMaintenance.userNotes";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_AS_STRING_HEADING = "balMaintenance.inputDateAsString";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_TIME_AS_STRING_HEADING = "balMaintenance.inputTimeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_INPUT_DATE_TIME_AS_STRING_HEADING = "balMaintenance.inputDateTimeAsString";
		public static final String BAL_MAINTENANCE_DETAILS_CURRENCY_CODE_HEADING = "balMaintenance.currencyCode";

		public static final String CRITICAL_TYPE_DESC= "criticalTypeDesc";
		public static final String CRITICAL_TYPE_CATECORY= "criticalTypeCateg";
		public static final String CRITICAL_TYPE_ORDER_IN_CATEG= "criticalTypeOrderInCateg";
		public static final String CRITICAL_TYPE_SUM_TO_CATEG= "criticalTypeSumToCateg";
		public static final String CRITICAL_TYPE_SUM_TO_TOTAL= "criticalTypeSumToTotal";
		public static final String CRITICAL_TYPE_REPORTABLE= "criticalTypeReportable";
		public static final String CRITICAL_TYPE_REPORT_INDIV_PAY= "criticalTypeReportIndivPay";
		public static final String CRITICAL_TYPE_ENABLE_UPDATE_PROCESS= "criticalTypeEnableUpdateProcess";
		public static final String CRITICAL_TYPE_SET_CLAUSE= "criticalTypeSetClause";
		public static final String CRITICAL_TYPE_WHERE_CLAUSE= "criticalTypeWhereClause";
		public static final String CRITICAL_TYPE_TIME_FRAME= "criticalTypeTimeFrame";
		public static final String CRITICAL_TYPE_WORKING_DAYS= "criticalTypeWorkingDays";
		public static final String CRITICAL_TYPE_RUN_SQL_UPDATE= "criticalTypeRunSqlUpdate";
		public static final String CRITICAL_TYPE_START_TIME= "criticalTypeStartTime";
		public static final String CRITICAL_TYPE_END_TIME= "criticalTypeEndTime";

		public static final String CRITICAL_PAYMENT_CCY = "ccy";
		public static final String CRITICAL_PAYMENT_DEFAULT_EXPECTED_TIME = "defaultExpectedTime";
		public static final String CRITICAL_PAYMENT_CCY_HEADER = "CriticalPay.ccy";
		public static final String CRITICAL_PAYMENT_DEFAULT_EXPECTED_TIME_HEADER = "CriticalPay.defaultExpectedTime";


		/****************************Critical payment type*******************/
		public static final String CRITICAL_PAY_TYPE = "CriticalPayType";
		public static final String CRITICAL_PAY_TYPE_DETAIL = "CriticalPayTypeDetail";
		public static final String CRITICAL_PAYMENT_ENTITY = "entity";
		public static final String CRITICAL_PAYMENT_TYPE = "type";
		public static final String CRITICAL_PAYMENT_DESCRIPTION = "description";
		public static final String CRITICAL_PAYMENT_CATEGORY = "category";
		public static final String CRITICAL_PAYMENT_ORDER_IN_CATEG = "orderInCateg";
		public static final String CRITICAL_PAYMENT_ENABLED = "enabled";
		public static final String CRITICAL_PAYMENT_ACCESS = "access";

		public static final String CRITICAL_PAYMENT_ENTITY_HEADER = "CriticalPay.entity";
		public static final String CRITICAL_PAYMENT_TYPE_HEADER = "CriticalPay.type";
		public static final String CRITICAL_PAYMENT_DESCRIPTION_HEADER = "CriticalPay.status";
		public static final String CRITICAL_PAYMENT_CATEGORY_HEADER = "CriticalPay.category";
		public static final String CRITICAL_PAYMENT_ORDER_IN_CATEG_HEADER = "CriticalPay.order.in.categ";
		public static final String CRITICAL_PAYMENT_ENABLED_HEADER = "CriticalPay.enabled";
		public static final String CRITICAL_PAYMENT_ACCESS_HEADER  = "access";
		public static final String MENU_ITEM_CRITICAL_PAYMENT_TYPE = "129";
		public static final String AUTHORISATION_CRIT_PAYMENT_TYPE_ID = "CRIT_PAYMENT_TYPE";
	
		public static final String CONFIG_RECIPIENTS ="configRecipients";

		public static final String CONFIG_USER_ID ="usermaintenance.userId";
		public static final String CONFIG_USER_NAME ="usermaintenance.userName";
		public static final String CONFIG_ROLE ="role.roleId";
		public static final String CONFIG_NAME ="usermaintenance.userName";
		public static final String CONFIG_ACCESS ="role.entAccessList.Access";
		public static final String CONFIG_EMAIL ="role.alerttype.email";
		public static final String ROLE_HEADER_TOOLTIP ="tooltip.sortRoleId";
		public static final String NAME_HEADER_TOOLTIP ="tooltip.sortRoleName";
		public static final String ACCESS_HEADER_TOOLTIP ="role.entAccessList.Access";
		public static final String EMAIL_HEADER_TOOLTIP ="role.alerttype.email";
		public static final String USER_ID_HEADER_TOOLTIP ="tooltip.sortUserId";
		public static final String USER_NAME_HEADER_TOOLTIP ="tooltip.sortUserNames";


	//********************
	public static final String EMAILTEMPLATE_TEMPLATE_ID_TAGNAME = "templateId";
	public static final String EMAILTEMPLATE_DESCRIPTION_TAGNAME = "description";
	public static final String EMAILTEMPLATE_SUBJECT_CONTENT_TAGNAME = "subjectContent";
	public static final String EMAILTEMPLATE_BODY_CONTENT_TAGNAME = "bodyContent";

	public static final String EMAILTEMPLATE_TEMPLATE_ID_HEADING = "emailTemplateMaintenance.templateId";
	public static final String EMAILTEMPLATE_DESCRIPTION_HEADING = "emailTemplateMaintenance.description";
	public static final String EMAILTEMPLATE_SUBJECT_CONTENT_HEADING = "emailTemplateMaintenance.subjectContent";
	public static final String EMAILTEMPLATE_BODY_CONTENT_HEADING = "emailTemplateMaintenance.bodyContent";

	public static final String EMAILTEMPLATE_TEMPLATE_ID_HEADING_TOOLTIP = "emailTemplateMaintenance.tooltip.templateId";
	public static final String EMAILTEMPLATE_DESCRIPTION_HEADING_TOOLTIP = "emailTemplateMaintenance.tooltip.description";
	public static final String EMAILTEMPLATE_SUBJECT_CONTENT_HEADING_TOOLTIP = "emailTemplateMaintenance.tooltip.subjectContent";
	public static final String EMAILTEMPLATE_BODY_CONTENT_HEADING_TOOLTIP = "emailTemplateMaintenance.tooltip.bodyContent";

	public static final String EMAILTEMPLATE = "emailTemplateMaintenance";
	public static final String EMAILTEMPLATE_KEYWORDS = "keywords";


		public static final String CONFIG_EMAIL_ADDRESS ="otherEmail.emailAddress";
		public static final String CONFIG_DESCRIPTION ="otherEmail.description";
		public static final String CONFIG_SEND ="otherEmail.send";
		public static final String CONFIG_EMAIL_ADDRESS_HEADER_TOOLTIP ="otherEmail.emailAddress.tooltip";
		public static final String CONFIG_DESCRIPTION_HEADER_TOOLTIP ="otherEmail.description.tooltip";
		public static final String CONFIG_SEND_HEADER_TOOLTIP ="otherEmail.send.tooltip";
		public static final String CONFIG_ID= "confi.id";

		public static final String  USER_LOG_DATE_TAGNAME = "logDate";
		public static final String  USER_LOG_TIME_TAGNAME = "logTime";
		public static final String  USER_LOG_USER_ID_TAGNAME = "user";
		public static final String  USER_LOG_ITEM_TAGNAME = "item";
		public static final String  USER_LOG_ITEM_ID_TAGNAME = "itemId";
		public static final String  USER_LOG_ACTION_TAGNAME = "action";

		public static final String  USER_LOG_DATE_HEADING = "userLog.date";
		public static final String  USER_LOG_TIME_HEADING = "userLog.time";
		public static final String  USER_LOG_USER_ID_HEADING = "userLog.user";
		public static final String  USER_LOG_ITEM_HEADING = "userLog.item";
		public static final String  USER_LOG_ITEM_ID_HEADING = "userLog.itemId";
		public static final String  USER_LOG_ACTION_HEADING = "userLog.action";


		public static final String  USER_LOG_DATE_HEADING_TOOLTIP = "tooltip.sortLogDate";
		public static final String  USER_LOG_TIME_HEADING_TOOLTIP = "tooltip.sortLogTime";
		public static final String  USER_LOG_USER_ID_HEADING_TOOLTIP = "tooltip.sortUserId";
		public static final String  USER_LOG_ITEM_HEADING_TOOLTIP = "tooltip.sortItem";
		public static final String  USER_LOG_ITEM_ID_HEADING_TOOLTIP = "tooltip.sortItemId";
		public static final String  USER_LOG_ITEM_NUM_HEADING_TOOLTIP = "tooltip.sortItemNum";

	public static final String  USER_LOG_ACTION_HEADING_TOOLTIP = "tooltip.sortAction";



		public static final String AUDIT_LOG_DATA= "userLogData";
		public static final String USER_LOG_VIEW = "userLogView";


	public static final String USER_LOG_VIEW_FIELD_TAGNAME = "field";
	public static final String USER_LOG_VIEW_CHANGED_FROM_TAGNAME = "changedFrom";
	public static final String USER_LOG_VIEW_CHANGED_TO_USER_TAGNAME = "changedTo";

	public static final String USER_LOG_VIEW_FIELD_HEADING = "maintenanceLog.columnName";
	public static final String USER_LOG_VIEW_CHANGED_FROM_HEADING = "maintenanceLog.oldValue";
	public static final String USER_LOG_VIEW_CHANGED_TO_HEADING = "maintenanceLog.newValue";

	public static final String USER_LOG_VIEW_FIELD_HEADING_TOOLTIP = "tooltip.sortField";
	public static final String USER_LOG_VIEW_CHANGED_FROM_HEADING_TOOLTIP = "tooltip.sortOldValue";
	public static final String USER_LOG_VIEW_CHANGED_TO_HEADING_TOOLTIP = "tooltip.sortNewValue";

	public static final String MULTI_MVT_ACTIONS= "multiMvtActions";

	public static final String MULTI_MVT_SELECT= "select";
	public static final String MULTI_MVT_ACCESS= "mvtAccess";
	public static final String MULTI_MVT_MOVEMENT_ID= "movementId";
	public static final String MULTI_MVT_ENTITY= "entity";
	public static final String MULTI_MVT_CCY = "ccy";
	public static final String MULTI_MVT_VDate = "vdate";
	public static final String MULTI_MVT_ACCOUNT= "account";
	public static final String MULTI_MVT_AMOUNT = "amount";
	public static final String MULTI_MVT_SIGN= "sign";
	public static final String MULTI_MVT_PRED = "pred";
	public static final String MULTI_MVT_EXT = "ext";
	public static final String MULTI_MVT_ILMFCAST= "ilmFcast";
	public static final String MULTI_MVT_MATCH_STATUS = "status";
	public static final String MULTI_MVT_REF1 = "ref1";
	public static final String MULTI_MVT_REF2 = "ref2";
	public static final String MULTI_MVT_EXTRA_REF = "extraRef";
	public static final String MULTI_MVT_BOOK = "book";
	public static final String MULTI_MVT_MATCH_ID = "matchId";
	public static final String MULTI_MVT_SOURCE = "source";
	public static final String MULTI_MVT_FORMAT = "format";
	public static final String MULTI_MVT_BOOK_CODE = "bookCode";
	public static final String MULTI_MVT_CPARTY= "cparty";
	public static final String MULTI_MVT_ORD_INST= "ordInst";
	public static final String MULTI_MVT_EXP_SETTLEMENT = "expSettlement";
	public static final String MULTI_MVT_ACT_SETTLEMENT = "actSettlement";
	public static final String MULTI_MVT_CRIT_PAY_TYPE="critPayType";

	public static final String MULTI_MVT_MOVEMENT_ID_HEADER= "multiMvtActions.movementId";
	public static final String MULTI_MVT_ENTITY_HEADER= "multiMvtActions.entity";
	public static final String MULTI_MVT_CCY_HEADER = "multiMvtActions.ccy";
	public static final String MULTI_MVT_VDate_HEADER = "multiMvtActions.vdate";
	public static final String MULTI_MVT_ACCOUNT_HEADER= "multiMvtActions.account";
	public static final String MULTI_MVT_AMOUNT_HEADER = "multiMvtActions.amount";
	public static final String MULTI_MVT_SIGN_HEADER= "multiMvtActions.sign";
	public static final String MULTI_MVT_PRED_HEADER = "multiMvtActions.pred";
	public static final String MULTI_MVT_EXT_HEADER = "multiMvtActions.ext";
	public static final String MULTI_MVT_ILMFCAST_HEADER= "multiMvtActions.ilmFcast";
	public static final String MULTI_MVT_MATCH_STATUS_HEADER = "multiMvtActions.status";
	public static final String MULTI_MVT_REF1_HEADER = "multiMvtActions.ref1";
	public static final String MULTI_MVT_REF2_HEADER = "multiMvtActions.ref2";
	public static final String MULTI_MVT_EXTRA_REF_HEADER = "multiMvtActions.extraRef";
	public static final String MULTI_MVT_BOOK_HEADER = "multiMvtActions.book";
	public static final String MULTI_MVT_MATCH_ID_HEADER = "multiMvtActions.matchId";
	public static final String MULTI_MVT_SOURCE_HEADER = "multiMvtActions.Source";
	public static final String MULTI_MVT_FORMAT_HEADER = "multiMvtActions.format";
	public static final String MULTI_MVT_BOOK_CODE_HEADER = "multiMvtActions.bookCode";
	public static final String MULTI_MVT_CPARTY_HEADER= "multiMvtActions.cparty";
	public static final String MULTI_MVT_ORD_INST_HEADER= "multiMvtActions.ordInst";
	public static final String MULTI_MVT_EXP_SETTLEMENT_HEADER = "multiMvtActions.expSettlement";
	public static final String MULTI_MVT_ACT_SETTLEMENT_HEADER = "multiMvtActions.actSettlement";
	public static final String MULTI_MVT_CRIT_PAY_TYPE_HEADER="multiMvtActions.critPayType";
	public static final String MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME = "MultiMovementUpdateNumber";
}
