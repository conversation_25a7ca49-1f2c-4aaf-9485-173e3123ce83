package org.swallow.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;

import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.Scenario;
import org.swallow.control.model.UserMaintenance;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.control.service.ScenMaintenanceManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AccountAttributeHDR;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.service.AccountAttributeMaintenanceManager;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.EntityManager;
import org.swallow.maintenance.service.ILMGeneralMaintenanceManager;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.model.MenuItem;
import org.swallow.model.User;
import org.swallow.work.model.Movement;
import org.swallow.work.model.SweepDetail;
import org.swallow.work.service.MovementLockManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.SweepDetailManager;
import org.swallow.work.service.SweepDetailValObj;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
public class XSSUtil {

	public static boolean checkCurrencyFullAccess(HttpServletRequest request, String entityId, String currencyId) {
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			return true;
		} else {
			int accessId;
			try {
				if(SwtUtil.isEmptyOrNull(currencyId) || currencyId.equalsIgnoreCase("all")){
					return checkEntityFullAccess(request, entityId);
				}else {
					accessId = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, currencyId);
					if (accessId == 0)
						return true;
					else
						return false;
				}

			} catch (SwtException e) {
				return true;
			}

		}
	}

	public static boolean checkCurrencyViewAccess(HttpServletRequest request, String entityId, String currencyId) {
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			return true;
		} else {
			int accessId;
			try {
				if(SwtUtil.isEmptyOrNull(currencyId) || currencyId.equalsIgnoreCase("all")){
					return checkEntityViewAccess(request, entityId);
				}else {
					accessId = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, currencyId);
					if (accessId == 0 || accessId == 1)
						return true;
					else
						return false;
				}

			} catch (SwtException e) {
				return true;
			}

		}
	}
	
	public static boolean checkChangeContactAccess(HttpServletRequest request,  String screenStatus) {
		LogonDAO logonDAO = null;
		MenuItem menuItem = null;
		int accessId = 0;
		if (SwtUtil.isEmptyOrNull(screenStatus)) {
			return false;
		} else {
			try {
				logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
				menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_ACCOUNT_MAINTENANCE+"", ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser());
				accessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
				if (accessId == 0 ) {
					return true;	
				}else {
					if("edit".equals(screenStatus) || "saveContactDetails".equalsIgnoreCase(screenStatus)) {
						return false;
					} else 
						return true;
							
				}
				
			} catch (SwtException e) {
				return true;
			}
			
		}
	}
	
	public static boolean checkCurrencyViewAccessForRole(HttpServletRequest request, String entityId, String currencyId, String roleId) {
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			return true;
		} else {
			int accessId;
			try {
				if(SwtUtil.isEmptyOrNull(currencyId) || currencyId.equalsIgnoreCase("all")){
					return checkEntityViewAccessForRole(request, entityId, roleId);
				}else {
					accessId = SwtUtil.getMenuEntityCurrGrpAccessForRole(request, entityId, currencyId, roleId);
					if (accessId == 0 || accessId == 1)
						return true;
					else
						return false;
				}
				
			} catch (SwtException e) {
				return true;
			}
			
		}
	}
	
	public static boolean checkCurrencyGrpFullAccess(HttpServletRequest request, String entityId, String currencyGrp) {
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			return true;
		} else {
			int accessId;
			boolean currencyGroupExist= false;
			try {
				if(SwtUtil.isEmptyOrNull(currencyGrp) || currencyGrp.equalsIgnoreCase("all")){
					return checkEntityFullAccess(request, entityId);
				}else {
					// Getting the User's Role Id from the session object
					String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getRoleId();
					Collection<EntityCurrencyGroupAccess> collection = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupViewORFullAcess(roleId, entityId);
					if(collection != null){
						for (EntityCurrencyGroupAccess elem : collection) {
							if(elem.getCurrencyGroupId().equals(currencyGrp)){{
									currencyGroupExist= true;
								}

							}
						}
					}
					
					if(currencyGroupExist) {
						accessId = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupAccess(roleId, entityId, currencyGrp);
						
						if (accessId == 0)
							return true;
						else
							return false;
					}else {
						return true;
					}
				}
				
			} catch (Exception e) {
				return true;
			}
			
		}
	}
	
	public static boolean checkCurrencyGrpViewAccess(HttpServletRequest request, String entityId, String currencyGrp) {
		
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			return true;
		} else {
			int accessId;
			boolean currencyGroupExist= false;
			try {
				if(SwtUtil.isEmptyOrNull(currencyGrp) || currencyGrp.equalsIgnoreCase("all")){
					return checkEntityViewAccess(request, entityId);
				}else {
					// Getting the User's Role Id from the session object
					String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getRoleId();
					Collection<EntityCurrencyGroupAccess> collection = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupViewORFullAcess(roleId, entityId);
					if(collection != null){
						for (EntityCurrencyGroupAccess elem : collection) {
							if(elem.getCurrencyGroupId().equals(currencyGrp)){{
									currencyGroupExist= true;
								}

							}
						}
					}
					
					if(currencyGroupExist) {
						accessId = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupAccess(roleId, entityId, currencyGrp);
						
						if (accessId == 0 || accessId == 1)
							return true;
						else
							return false;
					}else {
						return true;
					}
				}
				
			} catch (Exception e) {
				return true;
			}
			
		}
	}

	public static boolean checkEntityFullAccess(HttpServletRequest request, String entityId) {
		Entity entity = new Entity();
		EntityManager entityManager = null;
		try {
			// Create an instance of entity manager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(SwtUtil.getCurrentHostId());
			entity = entityManager.getEntityDetail(entity);
			
			if (entity == null || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
				return true;
			} else {
				int accessId = SwtUtil.getEntityAccessType(request, entityId);
				if (accessId == 0)
					return true;
				else
					return false;
			}
		} catch (Exception e) {
			return true;
		}
	}

	public static boolean checkEntityViewAccess(HttpServletRequest request, String entityId) {
		
		Entity entity = new Entity();
		EntityManager entityManager = null;
		try {
			// Create an instance of entity manager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(SwtUtil.getCurrentHostId());
			entity = entityManager.getEntityDetail(entity);
			
			if (entity == null || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
				return true;
			} else {
				int accessId = SwtUtil.getEntityAccessType(request, entityId);
				if (accessId == 0 || accessId == 1)
					return true;
				else
					return false;
			}
		} catch (Exception e) {
			return true;
		}
	}
	
	public static boolean checkEntityViewAccessForRole(HttpServletRequest request, String entityId, String roleId) {
		
		Entity entity = new Entity();
		EntityManager entityManager = null;
		try {
			// Create an instance of entity manager
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(SwtUtil.getCurrentHostId());
			entity = entityManager.getEntityDetail(entity);
			
			if (entity == null || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
				return true;
			} else {
				int accessId = SwtUtil.getEntityAccessTypeForRole(request, entityId, roleId);
				if (accessId == 0 || accessId == 1)
					return true;
				else
					return false;
			}
		} catch (Exception e) {
			return true;
		}
	}
	

	public static boolean checkAccountFullAccess(HttpServletRequest request, String entityId, String currencyId, String accountId, String status) {
		if (SwtUtil.isEmptyOrNull(currencyId) || SwtUtil.isEmptyOrNull(accountId)) {
			return true;
		} else {
			String roleId;
			AccountAccess acctAccess = null;
			boolean chekAccntAccessFlag = false;
			try {
				
				AccountAccessManager accountAccessManager = (AccountAccessManager) SwtUtil
						.getBean("accountAccessManager");
				acctAccess = new AccountAccess();
				roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN))
						.getUser().getRoleId();
				/*Setting role id using bean class*/
				acctAccess.getId().setRoleId(roleId);
				/*Setting entity id using bean class*/
				acctAccess.getId().setEntityId(entityId);
				/*Setting account id using bean class*/
				acctAccess.getId().setAccountId(accountId);
				/*Setting host id using bean class*/
				acctAccess.getId().setHostId(SwtUtil.getCurrentHostId());
				/*Checking role account access flag by calling manager file*/
				chekAccntAccessFlag = accountAccessManager.getRoleAccessDetails(acctAccess);
				/*Checking account access by calling manager file*/
				if(chekAccntAccessFlag){
					return accountAccessManager.checkAcctAccess(acctAccess,status);
				}
				else{
					return true;
				}

			} catch (SwtException e) {
				return true;
			} finally {
				acctAccess = null;
			}

		}
	}


	
	

	public static boolean checkSystemScenario(HttpServletRequest request, String selectedScenarioID) {
		
		try {
			if (SwtUtil.isEmptyOrNull(selectedScenarioID)) {
				return true;
			} else {
				Scenario scenario = null;
				/* Get the editable data list to the SystemAlertMesages bean */
				ScenMaintenanceManager scenMaintenanceManager = (ScenMaintenanceManager) SwtUtil
						.getBean("scenMaintenanceManager");

				scenario = scenMaintenanceManager.getEditableDataDetailList(selectedScenarioID);
				if (scenario != null) {
					if (scenario.getSystemFlag().equals(SwtConstants.YES))
						return false;
					else
						return true;
				} else {
					return true;
				}
			}
		} catch (Exception e) {
			return true;
		}
	}
	
	
	public static boolean checkILMGroupFullAccess(HttpServletRequest request, String selectedAccountGroup, String selectedEntity, String selectedCurrency, String methodName) {
		
		int accessId = 0;
		try {
			
			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			// The menu item to the relevant match
			MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_ACCOUNT_GROUP_MAINTENANCE+"", ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser());
			
			accessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
			
			// Change the menu access id of the screen if the menu access to the relevant match is not full access
			if (menuItem != null && accessId == 1 ) {
				if(!methodName.equals("view")) {
					return false;
				}
					
			}else if (menuItem != null && accessId == 2 ) {
				return false;
			}
				
			
			
			if (SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				if(SwtUtil.isEmptyOrNull(methodName)){
					return true;
				} else if(methodName.equalsIgnoreCase("add")){
					return checkCurrencyFullAccess(request, selectedEntity, selectedCurrency);
				}else {
					return true;
				}
			} else {
				String currencyCode  = null;
				String entityId  = null;
				String userId  = null;
				String hostId  = null;
				String publicPrivate = null;
				int accessInd = 0;
				boolean access = false;
				boolean maintainAnyILMGroup = false;
				String currentUserId = null;
				boolean allowCCyAccess = false; 
				String isGlobal = null; 
				
				ILMAccountGroup accountGroup = null;
				/* Get the editable data list to the SystemAlertMesages bean */
				ILMGeneralMaintenanceManager ilmGeneralMaintenanceManager = (ILMGeneralMaintenanceManager) SwtUtil
						.getBean("ilmGeneralMaintenanceManager");
				
				accountGroup = ilmGeneralMaintenanceManager
						.getEditableDataDetailList(selectedAccountGroup);
				
				currencyCode = accountGroup.getCurrencyCode();
				entityId = accountGroup.getEntityId();
				userId = accountGroup.getCreatedByUser();
				publicPrivate = accountGroup.getPublicPrivate();
				isGlobal = accountGroup.getILMCcyParameters().size() > 0 ? "Y": "N";
				// Retrieve Host Id
				hostId = SwtUtil.getCurrentHostId();
				// Retrieve current User ID
				currentUserId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId()
						.getUserId();
				// Retrieve User's Menu,Entity and Currency Group
				accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entityId,
						currencyCode);
				if(SwtUtil.isEmptyOrNull(methodName)){
					return true;
				}
				if(methodName.equals("view")){
					if(accessInd == 0 || accessInd == 1) {
						allowCCyAccess = true;
					}else {
						allowCCyAccess = false;
					}
					return allowCCyAccess;
				}
				else if (methodName.equals("add") || "addfromILM".equals(methodName) || (methodName.equals("change")) || (methodName.equals("deleteAccountGroup"))){
					if(accessInd == 0) {
						allowCCyAccess = true;
					}else {
						allowCCyAccess = false;
					}
				}
				
				
				if (allowCCyAccess) {
					maintainAnyILMGroup = SwtUtil
							.getMaintainAnyGroupILMAccess(request);
					if (maintainAnyILMGroup){
						access = true;
					}
					else {
						if (!SwtUtil.isEmptyOrNull(currentUserId)
								&& !SwtUtil.isEmptyOrNull(userId)) {
							if (currentUserId.equals(userId)
									&& publicPrivate.equalsIgnoreCase("PRIVATE")){
								access = true;
							}

						} else
							access = false;
					}

				}
				if(access && methodName.equals("deleteAccountGroup")) {
					if(isGlobal.equals("Y"))
						access = false;
				}
				return access;

			}
		} catch (Exception e) {
			return true;
		}
	}
	
	public static boolean checkILMGroupViewAccess(HttpServletRequest request, String selectedAccountGroup) {
		
		try {
			if (SwtUtil.isEmptyOrNull(selectedAccountGroup)) {
				return true;
			} else {
				Scenario scenario = null;
				/* Get the editable data list to the SystemAlertMesages bean */
				ScenMaintenanceManager scenMaintenanceManager = (ScenMaintenanceManager) SwtUtil
						.getBean("scenMaintenanceManager");
				
				scenario = scenMaintenanceManager.getEditableDataDetailList(selectedAccountGroup);
				if (scenario != null) {
					if (scenario.getSystemFlag().equals(SwtConstants.YES))
						return false;
					else
						return true;
				} else {
					return true;
				}
			}
		} catch (Exception e) {
			return true;
		}
	}
	
	public static boolean checkSystemAccountAttributeHDR(HttpServletRequest request, String selectedAccountHDRId) {
		
		try {
			if (SwtUtil.isEmptyOrNull(selectedAccountHDRId)) {
				return true;
			} else {
				AccountAttributeHDR acctAttrHDR64 = null;
				AccountAttributeHDR acctAttrHDR = null;
				String selectedAccountHDRId64 = null;
				AccountAttributeMaintenanceManager accountAttributeMaintenanceManager = (AccountAttributeMaintenanceManager) SwtUtil
						.getBean("accountAttributeMaintenanceManager");
				selectedAccountHDRId64 = SwtUtil.decode64(selectedAccountHDRId);
				//Fetch the required account attribute HDR
				acctAttrHDR64 = accountAttributeMaintenanceManager.getAccountAttributeHDR(selectedAccountHDRId64);
				acctAttrHDR = accountAttributeMaintenanceManager.getAccountAttributeHDR(selectedAccountHDRId);
				
				if (acctAttrHDR64 != null) {
					if (acctAttrHDR64.getSystemFlag().equals(SwtConstants.YES))
						return false;
					else
						return true;
				} else if (acctAttrHDR != null) { 
					if (acctAttrHDR.getSystemFlag().equals(SwtConstants.YES))
						return false;
					else
						return true;
				} else {
					return true;
				}
			}
		} catch (Exception e) {
			return true;
		}
	}

	/**
	 * Returns (through HttpServletResponse) a string which contains the result
	 * of calculating access for the selected row according to entity id and
	 * currency code. Values : 0 : Full access 1 : View access 2 : No access
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public static boolean getEntityCurrencyAccessForScenario(HttpServletRequest request, String entityId,
			String currencyCode, String scenarioId) throws SwtException {
		// Method's local variable and class instance declaration
		String currentUserId;
		int accessInd = -1;
		String hostId;
		boolean access = false;
		boolean maintainAnyILMScenario = false;
		// Entity collection
		Collection entityColl = null;
		// currencies collection
		String userId = null;
		ILMScenario ilmScenario;
		String publicPrivate = null;
		try {
			// Retrieve Host Id
			hostId = SwtUtil.getCurrentHostId();
			// Retrieve current User ID
			currentUserId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getId().getUserId();

			// Get the editable ilmScenario data */
			ILMTransScenarioMaintenanceManager ilmTransScenarioMaintenanceManager = (ILMTransScenarioMaintenanceManager) SwtUtil
					.getBean("ilmTransScenarioMaintenanceManager");
			ilmScenario = ilmTransScenarioMaintenanceManager.getEditableScenarioData(scenarioId);
			userId = ilmScenario.getCreatedByUser();
			publicPrivate = ilmScenario.getPublicPrivate();

			if ("All".equalsIgnoreCase(entityId) && "All".equalsIgnoreCase(currencyCode) && accessInd < 0) {

				// Get the user access entity list .
				entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
				Iterator itrEntity = entityColl.iterator();

				while (itrEntity.hasNext() && accessInd != 0) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity.next();

					// Getting the User's Role Id from the session object
					String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN))
							.getUser().getRoleId();
					// Returns the currency Access List based on the Role
					Collection ccyList = (ArrayList) SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcess(roleId,
							entity.getEntityId());
					if (ccyList != null) {
						Iterator itr = ccyList.iterator();

						while (itr.hasNext()) {
							CurrencyAccessTO currencyAccessTO = (CurrencyAccessTO) itr.next();
							accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entity.getEntityId(),
									currencyAccessTO.getCurrencyId());
							if (accessInd == 0)
								break;
						}
					}

				}
			}
			if ("All".equalsIgnoreCase(entityId) && accessInd != 0) {
				// Get the user access entity list .
				entityColl = SwtUtil.getUserEntityAccessList(request.getSession());
				Iterator itrEntity = entityColl.iterator();

				while (itrEntity.hasNext()) {
					EntityUserAccess entity = (EntityUserAccess) itrEntity.next();
					accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entity.getEntityId(), currencyCode);
					if (accessInd == 0) {
						break;
					}
				}
			} else if ("All".equalsIgnoreCase(currencyCode) && accessInd != 0) {
				// Getting the User's Role Id from the session object
				String roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				// Returns the currency Access List based on the Role
				Collection ccyList = (ArrayList) SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcess(roleId,
						entityId);

				if (ccyList != null) {
					Iterator itr = ccyList.iterator();

					while (itr.hasNext()) {
						CurrencyAccessTO currencyAccessTO = (CurrencyAccessTO) itr.next();
						accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entityId,
								currencyAccessTO.getCurrencyId());
						if (accessInd == 0)
							break;
					}
				}

			} else if (accessInd != 0) {
				// Retrieve User's Menu,Entity and Currency Group
				accessInd = SwtUtil.getCcyGrpAccessType(request, hostId, entityId, currencyCode);
			}
			if (accessInd == 0) {// TO DO : when Entity or Ccy equals to *
									// ,maybe just check if this scenario is
									// public or not ??

				maintainAnyILMScenario = SwtUtil.getMaintainAnyScenarioILMAccess(request);
				if (maintainAnyILMScenario)
					access = true;
				else {
					if (!SwtUtil.isEmptyOrNull(currentUserId) && !SwtUtil.isEmptyOrNull(userId)) {
						if (currentUserId.equals(userId) && publicPrivate.equalsIgnoreCase("PRIVATE"))
							access = true;
					} else
						access = false;
				}
			}
			return access;

		} catch (Exception exp) {
			return true;
		}
	}
	
	public static boolean checkCurrencyFullAccessForSweepPrior(HttpServletRequest request, String entityId, String selectedList) {
		try{
			String replacedstrselectedList = selectedList
					.replaceAll("'", "");
	

			String valuedate1 = replacedstrselectedList.substring(0,
					(replacedstrselectedList.indexOf(",") - 1));

			String tempaccountiD1 = replacedstrselectedList.substring(
					(replacedstrselectedList.indexOf(",") + 1),
					(replacedstrselectedList.indexOf("|")));

			String accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
					.indexOf(",") - 1));

			String tempcurrEntiy = tempaccountiD1.substring((tempaccountiD1
					.indexOf(",") + 1), (tempaccountiD1.length()));

			String entityId1 = tempcurrEntiy.substring(0, (tempcurrEntiy
					.indexOf(",") - 1));

			String tempCurrCode1 = tempcurrEntiy.substring((tempcurrEntiy
					.indexOf(",") + 1), tempcurrEntiy.length());

			String currCode1 = tempCurrCode1.substring(0, (tempCurrCode1
					.indexOf(",") - 1));

			String mainAccountId = tempCurrCode1.substring((tempCurrCode1
					.lastIndexOf(",") + 1), tempCurrCode1.length());
			
			return checkDoSweepAction(request, entityId1, entityId1,
					currCode1, currCode1, mainAccountId,
					accountId1);
		}catch(Exception e){
			return true;
		}
		
	}
	
	
	public static boolean checkCurrencyFullAccessManualSweep(HttpServletRequest request, String entityId, String selectedList, String calledFrom) {
		try{
			String accountId1 = "";
			String entityId1 = "";
			String tempdata2 = "";
			String valudate2 = "";
			String accountId2 = "";
			String entityId2 = "";
			String hostId = "";
			String replacedstrselectedList = "";
			String tempcurrEntiy = "";
			String tempCurrCode1 = "";
			String valuedate1 = "";
			String tempaccountiD1 = "";
			if (calledFrom.equalsIgnoreCase("manualsweeping")) {

				replacedstrselectedList = selectedList.replaceAll("'", "");
				valuedate1 = replacedstrselectedList.substring(0,
						(replacedstrselectedList.indexOf(",") - 1));

				tempaccountiD1 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf(",") + 1),
						(replacedstrselectedList.indexOf("|")));

				accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
						.lastIndexOf(",") - 1));

				entityId1 = tempaccountiD1.substring((tempaccountiD1
						.lastIndexOf(",") + 1), tempaccountiD1.length());

				tempdata2 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf("|") + 1),
						(replacedstrselectedList.lastIndexOf("|")));

				valudate2 = tempdata2
						.substring(0, (tempdata2.indexOf(",") - 1));

				accountId2 = tempdata2.substring((tempdata2.indexOf(",") + 1),
						(tempdata2.lastIndexOf(",") - 1));

				entityId2 = tempdata2.substring(
						(tempdata2.lastIndexOf(",") + 1), tempdata2.length());

			}

			else if (calledFrom.equalsIgnoreCase("sweeppriorcutoff")) {
				
				hostId = CacheManager.getInstance().getHostId();


				replacedstrselectedList = selectedList.replaceAll("'", "");
				valuedate1 = replacedstrselectedList.substring(0,
						(replacedstrselectedList.indexOf(",") - 1));

				tempaccountiD1 = replacedstrselectedList.substring(
						(replacedstrselectedList.indexOf(",") + 1),
						(replacedstrselectedList.indexOf("|")));

				accountId1 = tempaccountiD1.substring(0, (tempaccountiD1
						.indexOf(",") - 1));

				tempcurrEntiy = tempaccountiD1.substring((tempaccountiD1
						.indexOf(",") + 1), (tempaccountiD1.length()));

				entityId1 = tempcurrEntiy.substring(0, (tempcurrEntiy
						.indexOf(",") - 1));

				tempCurrCode1 = tempcurrEntiy.substring((tempcurrEntiy
						.indexOf(",") + 1), tempcurrEntiy.length());


				accountId2 = tempCurrCode1.substring((tempCurrCode1
						.lastIndexOf(",") + 1), tempCurrCode1.length());

				entityId2 = entityId1;
			}else {
				return true;
			}
			
			AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			SystemInfo systemInfo = new SystemInfo();
			
			// get the account maintenance details
			AcctMaintenance acctMaintenance1 = acctMaintenanceManager.getEditableDataDetailList(
					entityId1, SwtUtil.getCurrentHostId(), accountId1
							.toString(), systemInfo, SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// get the account maintenance details
			
			AcctMaintenance acctMaintenance2 = acctMaintenanceManager.getEditableDataDetailList(
					entityId2, SwtUtil.getCurrentHostId(), accountId2
					.toString(), systemInfo, SwtUtil
					.getCurrentSystemFormats(request.getSession()));
			return checkDoSweepAction(request, entityId1, entityId2, acctMaintenance1.getCurrcode(), acctMaintenance2.getCurrcode(), accountId1, accountId2);
			
		}catch(Exception e){
			return true;
		}
	}
	
	public static boolean checkCurrencyViewAccessManualSweep(HttpServletRequest request, String entityId, String currencyId, String entityIdChanged) {
		
		try {
			if(SwtUtil.isEmptyOrNull(entityId))
				return true;
			
			boolean isEntityIdChanged = entityIdChanged != null?entityIdChanged.equalsIgnoreCase("true"):false;  
			if (SwtUtil.isEmptyOrNull(currencyId) ||isEntityIdChanged==true) {
					currencyId = SwtUtil.getDomesticCurrencyForUser(request,
							SwtUtil.getCurrentHostId(), entityId);
			} 
			return checkCurrencyViewAccess(request, entityId, currencyId);
			
		} catch (SwtException e) {
			return true;
		}
		
	}
	
	
		public static boolean checkSweepDetails(HttpServletRequest request, String entityId, String sweepId, String qName) {
			SweepDetailValObj sweepDetailVO = null;
			// variable to hold acct1 details
			SweepDetail sweepAcct1 = null;
			// variable to hold acct2 details
			SweepDetail sweepAcct2 = null;
			// iterate sweepDetailsItr
			Iterator<SweepDetail> sweepDetailsItr = null;
			String archiveId = null;
			try {
				if (SwtUtil.isEmptyOrNull(qName)) {
					return true;
				} else {
					if (checkEntityFullAccess(request, entityId)) {
						SweepDetailManager sweepDetailManager = (SweepDetailManager) SwtUtil.getBean("sweepDetailManager");


						archiveId = request.getParameter("archiveId");
						if(SwtUtil.isEmptyOrNull(archiveId)) {
							sweepDetailVO = sweepDetailManager.getSweepDetails(request
									.getParameter("entid"), Long.valueOf(request
									.getParameter("swpid")), SwtUtil
									.getCurrentSystemFormats(request.getSession()), request
									.getParameter("qname").trim());
						}else {
							sweepDetailVO = sweepDetailManager.getSweepDetailsArchive(request
									.getParameter("entid"), Long.valueOf(request
									.getParameter("swpid")), SwtUtil
									.getCurrentSystemFormats(request.getSession()), request
									.getParameter("qname").trim(), archiveId);
						}

						// Get the account details
						sweepDetailsItr = sweepDetailVO.getAccountDetail().iterator();
						// Iterating the accounts
						sweepAcct1 = sweepDetailsItr.next();
						sweepAcct2 = sweepDetailsItr.next();
						if (qName.trim().equals(SwtConstants.SWEEP_STATUS_NEW)
								|| (qName.trim().equals(SwtConstants.SWEEP_STATUS_SUBMIT))) {
							return checkDoSweepAction(request, sweepAcct1.getEntityId(), sweepAcct2.getEntityId(),
									sweepAcct1.getCurrCode(), sweepAcct2.getCurrCode(), sweepAcct1.getAccountId(),
									sweepAcct2.getAccountId());
						} else if (qName.trim().equals(SwtConstants.SWEEP_STATUS_CANCEL)) {
							return checkCurrencyViewAccess(request, sweepAcct1.getEntityId(), sweepAcct1.getCurrCode())
									&& checkCurrencyViewAccess(request, sweepAcct2.getEntityId(), sweepAcct2.getCurrCode());
						}
	
					}
	
				}
			} catch (NumberFormatException e) {
				return true;
			} catch (SwtException e) {
				return true;
			}
	
			return true;
		}
		
		public static boolean checkSweepAuthOrSubmitAccess(HttpServletRequest request, String selectedList) {
			if (SwtUtil.isEmptyOrNull(selectedList)) 
				return true;
			
			String splitId = null;
			SweepDetailValObj sweepDetailVO = null;
			// variable to hold acct1 details
			SweepDetail sweepAcct1 = null;
			// variable to hold acct2 details
			SweepDetail sweepAcct2 = null;
			// iterate sweepDetailsItr
			Iterator<SweepDetail> sweepDetailsItr = null;
			boolean result = true;
			
			try{
				SweepDetailManager sweepDetailManager = (SweepDetailManager) SwtUtil.getBean("sweepDetailManager");
				String[] sweepList = selectedList.split(",");
				for(int i = 0; i < sweepList.length; i++){
					splitId = sweepList[i];
					sweepDetailVO = sweepDetailManager.getSweepDetails("", Long.valueOf(splitId),
							SwtUtil.getCurrentSystemFormats(request.getSession()), SwtConstants.SWEEP_STATUS_SUBMIT);
					sweepDetailsItr = sweepDetailVO.getAccountDetail().iterator();
					
					sweepAcct1 = sweepDetailsItr.next();
					sweepAcct2 = sweepDetailsItr.next();
					
					result = checkDoSweepAction(request, sweepAcct1.getEntityId(), sweepAcct2.getEntityId(),
							sweepAcct1.getCurrCode(), sweepAcct2.getCurrCode(), sweepAcct1.getAccountId(),
							sweepAcct2.getAccountId());
					
					if(!result){
						return false;
					}
				}
				
				
			}catch(Exception e){
				return true;
			}
				
			
			return true;
		}
		
		
		
		public static boolean checkDoSweepAction(HttpServletRequest request,String sweepAccount1EntityId,String sweepAccount2EntityId, String sweepAccount1CurrCode,String sweepAccount2CurrCode,String sweepAccount1AccountId,String sweepAccount2AccountId){
		
		if(SwtUtil.isEmptyOrNull(sweepAccount1EntityId) || SwtUtil.isEmptyOrNull(sweepAccount2EntityId) || SwtUtil.isEmptyOrNull(sweepAccount1CurrCode) || SwtUtil.isEmptyOrNull(sweepAccount2CurrCode))
			return true;
		
		if(checkCurrencyFullAccess(request, sweepAccount1EntityId, sweepAccount1CurrCode) && checkCurrencyFullAccess(request, sweepAccount2EntityId, sweepAccount2CurrCode)){
			if(SwtUtil.isEmptyOrNull(sweepAccount1AccountId) || SwtUtil.isEmptyOrNull(sweepAccount2AccountId))
				return true;
			else {
				boolean accountAccessFlag1 = false;
				boolean accountAccessFlag2 = false;
				
				try {
					/*Read the current user from SwtUtil file*/
					User user = SwtUtil.getCurrentUser(request.getSession());
					/*Getting host id from cache manager file*/
					String hostId = SwtUtil.getCurrentHostId();
					/*Getting role id using bean class*/
					String roleId = user.getRoleId();
					/*Getting account id,entity id,status from request*/
					
					AccountAccessManager accountAccessManager = (AccountAccessManager) SwtUtil
							.getBean("accountAccessManager");
					
					
					AccountAccess acctAccess = new AccountAccess();
					/*Setting role id using bean class*/
					acctAccess.getId().setRoleId(roleId);
					/*Setting entity id using bean class*/
					acctAccess.getId().setEntityId(sweepAccount1EntityId);
					/*Setting account id using bean class*/
					acctAccess.getId().setAccountId(sweepAccount1AccountId);
					/*Setting host id using bean class*/
					acctAccess.getId().setHostId(hostId);
					/*Checking role account access flag by calling manager file*/
						accountAccessFlag1 = accountAccessManager.getRoleAccessDetails(acctAccess);
					/*Checking account access by calling manager file*/
					if(accountAccessFlag1){
						accountAccessFlag1 = accountAccessManager.checkAcctAccess(acctAccess,"Sweeping");
					}
					else{
						accountAccessFlag1 = true;
					}
				
					
					AccountAccess acctAccess2 = new AccountAccess();
					/*Setting role id using bean class*/
					acctAccess2.getId().setRoleId(roleId);
					/*Setting entity id using bean class*/
					acctAccess2.getId().setEntityId(sweepAccount2EntityId);
					/*Setting account id using bean class*/
					acctAccess2.getId().setAccountId(sweepAccount2AccountId);
					/*Setting host id using bean class*/
					acctAccess2.getId().setHostId(hostId);
					/*Checking role account access flag by calling manager file*/
					accountAccessFlag2 = accountAccessManager.getRoleAccessDetails(acctAccess2);
					/*Checking account access by calling manager file*/
					if(accountAccessFlag2){
						accountAccessFlag2 = accountAccessManager.checkAcctAccess(acctAccess2,"Sweeping");
					}
					else{
						accountAccessFlag2 = true;
					}
					
				return (accountAccessFlag1 && accountAccessFlag2);
					
				} catch (SwtException e) {
					return true;
				}
				
			}
			
		}else {
			return false;
		}
		
	}
	
	public static boolean checkFullAccessInputAuthorise(HttpServletRequest request, String entityId, String currencyId, String movementIds) {

		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all")) {
			return true;
		} else {
			String movementId = "";
			String movementUserId = "";
			String accountId = "";
			String currentUser = "";
			Movement mvt = null;
			MovementManager movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			try {
				if (!SwtUtil.isEmptyOrNull(movementIds)) {
					String[] splitIds = movementIds.split(",");
					for(int i = 0; i < splitIds.length; i++){
						movementId = splitIds[i];
						if (!SwtUtil.isEmptyOrNull(movementId)) {
							mvt = movementManager.getMovementDetails(SwtUtil.getCurrentHostId(), Long.parseLong(movementId));
							if (mvt !=null) {
								movementUserId = mvt.getInputUser();
								accountId = mvt.getAccountId();
								currentUser= SwtUtil.getCurrentUserId(request.getSession());
								// check account access
								if (checkAccountFullAccess(request, entityId, currencyId, accountId, "Input")) {
									if(SwtUtil.isEmptyOrNull(currencyId)){
										if (!checkEntityFullAccess(request, entityId)) {
											return false;
										}
									}else {
										if (checkCurrencyFullAccess(request, entityId, currencyId)) {
											// Check if the current user and the user who created the movement are different
											if (SwtUtil.isEmptyOrNull(movementUserId) && movementUserId.equals(currentUser)) {
												return false;
											}
										} else {
											return false;
										}
									}
								} else {
									return false;
								}
							}
						}
					}
					return true;
				} else {
					return true;
				}
				
			} catch (SwtException e) {
				return true;
			}
		}
	}
	
	public static boolean checkLockedMovement(String movementId, String logged_UserId) {
		String locked_userId = "";
		try {
			MovementLockManager movementLockManager = (MovementLockManager) SwtUtil
					.getBean("movementLockManager");
	        locked_userId = movementLockManager.checkLock(Long.parseLong(movementId));
	        
	        if (locked_userId != null && !locked_userId.equals(logged_UserId) ) {
	        	return false;
	        } else {
	        	return true;
	        }
		} catch (Exception e) {
			return true;
		}
		
	}
	
	public static boolean checkSaveMyUserDetail(HttpServletRequest request, String userId, String userName, String roleId, String entityId, String currencyGroup, String sectionId) {
		
		User currentUser = null; 
		Entity entity = new Entity();
		EntityManager entityManager = null;
		Collection currencyGroupAcessColl = null;
		boolean currencyGroupFound = false;
		
		try {
			if (SwtUtil.isEmptyOrNull(userId) || SwtUtil.isEmptyOrNull(userName) || SwtUtil.isEmptyOrNull(roleId) 
					|| SwtUtil.isEmptyOrNull(entityId) || SwtUtil.isEmptyOrNull(currencyGroup)) {
				return false;
			}
			// Check if the entity exists
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(SwtUtil.getCurrentHostId());
			entity = entityManager.getEntityDetail(entity);
			if (entity == null) {
				return false;
			}
			// Check if the currency exists
			currencyGroupAcessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupAcess(roleId,
					entityId);
			if (!"All".equals(currencyGroup) && currencyGroupAcessColl != null) {
				Iterator itr = currencyGroupAcessColl.iterator();
				while (itr.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itr
							.next();
					if (currencyGroup.equals(entityCurrencyGroupAccess.getCurrencyGroupId())) {
						currencyGroupFound = true;
						break;
					}
				}
			}
			if (!"All".equals(currencyGroup) && !currencyGroupFound) {
				return false;
			}
			// Case when the disabled fields have been changed: user ID, user name, role and section 
			currentUser = SwtUtil.getCurrentUser(request.getSession());
			if (!userId.equals(currentUser.getId().getUserId()) ||
					!userName.equals(currentUser.getUserName())
					|| !roleId.equals(currentUser.getRoleId())) {
				return false;
			} else {
				if (!sectionId.equals(currentUser.getSectionId()) && !sectionId.equals("") 
						&& !SwtUtil.isEmptyOrNull(currentUser.getSectionId())) {
					return false;
				}
			}
			return checkCurrencyGrpViewAccess(request, entityId, currencyGroup);
			
		} catch (SwtException e) {
			return true;
		}
	}
	
	public static boolean checkSavUserMaintenance(HttpServletRequest request, String userId, String roleId, String entityId, String currencyGroup, String sectionId) {
			
		Entity entity = new Entity();
		EntityManager entityManager = null;
		UserMaintenanceManager usermaintenanceManager = null;
		Collection currencyGroupAcessColl = null;
		Collection userColl;
		ArrayList roleList = null;
		String hostId = null;
		boolean currencyGroupFound = false;
		boolean userFound = false;
		boolean roleFound = false;
		
		try {
			hostId = SwtUtil.getCurrentHostId();
			roleList = new ArrayList();
			if (SwtUtil.isEmptyOrNull(userId) || SwtUtil.isEmptyOrNull(roleId)
					|| SwtUtil.isEmptyOrNull(entityId) || SwtUtil.isEmptyOrNull(currencyGroup)) {
				return false;
			}
			// Check if the given user exists
			usermaintenanceManager = (UserMaintenanceManager) (SwtUtil.getBean("usermaintenanceManager"));
			userColl = usermaintenanceManager.getUserList(hostId, null);
			if (userColl != null) {
				Iterator itr = userColl.iterator();
				while (itr.hasNext()) {
					UserMaintenance user = (UserMaintenance) itr.next();
					if (userId.equals(user.getId().getUserId())) {
						userFound = true;
						break;
					}
				}
			}
			if (!userFound) {
				return false;
			}
			// Check if the given role exists
			roleList = (ArrayList) usermaintenanceManager.getRoleList(hostId);
			if (roleList != null && roleList.size() != 0) {
				for (Iterator iterator = roleList.iterator(); iterator
						.hasNext();) {
					LabelValueBean lvb = (LabelValueBean) iterator.next();
					if (roleId.equals(lvb.getValue())) {
						roleFound = true;
						break;
					}
				}
			}
			if (!roleFound) {
				return false;
			}
			// Check if the given entity exists
			entityManager = (EntityManager) (SwtUtil.getBean("entityManager"));
			entity.getId().setEntityId(entityId);
			entity.getId().setHostId(hostId);
			entity = entityManager.getEntityDetail(entity);
			if (entity == null) {
				return false;
			}
			// Check if the given currency exists
			currencyGroupAcessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyGroupAcess(roleId,
					entityId);
			if (!"All".equals(currencyGroup) && currencyGroupAcessColl != null) {
				Iterator itr = currencyGroupAcessColl.iterator();
				while (itr.hasNext()) {
					EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itr
							.next();
					if (currencyGroup.equals(entityCurrencyGroupAccess.getCurrencyGroupId())) {
						currencyGroupFound = true;
						break;
					}
				}
			}
			if (!"All".equals(currencyGroup) && !currencyGroupFound) {
				return false;
			}
			return checkCurrencyViewAccessForRole(request, entityId, currencyGroup, roleId);
			
		} catch (SwtException e) {
			return true;
		}
	}
	
	public static boolean checkSaveUpdateILMTransactionSet(HttpServletRequest request, String entityId, String currencyGroup, String XMLTransactions) {
		
		String operationName = null;
		String accountId = null;
		String hostId = null;

		try {
			hostId = SwtUtil.getCurrentHostId();
			if(!SwtUtil.isEmptyOrNull(XMLTransactions)) {
				XMLTransactions = SwtUtil.decode64(XMLTransactions);
				Document doc = SwtUtil.convertStringToDocument(XMLTransactions);
				doc.getDocumentElement().normalize();
				AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
						.getBean("acctMaintenanceManager");
				NodeList nList = doc.getElementsByTagName("transaction");
				for (int i = 0; i < nList.getLength(); i++) {
					Node node = nList.item(i);
					if (node.getNodeType() == Node.ELEMENT_NODE) {
	
						Element element = (Element) node;
						//Get the operation name
						operationName = element
								.getElementsByTagName(SwtConstants.OPERATION)
								.item(0).getTextContent().trim();
						if (!operationName.equals("delete")) {
							accountId = element
									.getElementsByTagName(SwtConstants.ACCOUNT_ID)
									.item(0).getTextContent().trim();
							// Check if the account exists in the list or not, if not return false
							if (acctMaintenanceManager.getMainOrLinkAccount(entityId, hostId, accountId) == null) {
								return false;
							}
						}
					}
				}
			}
			return checkCurrencyFullAccess(request, entityId, currencyGroup);
		} catch (SwtException e) {
			return true;
		}
	}
	
	public static boolean checkSaveUpdateDefaultAccntMaintenance(HttpServletRequest request, String entityId, String currencyGroup, String accountId) {
		
		String hostId = null;
		
		try {
			hostId = SwtUtil.getCurrentHostId();
			AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			// Check if the account exists in the list or not, if not return false
			if (acctMaintenanceManager.getMainOrLinkAccount(entityId, hostId, accountId) == null) {
				return false;
			}

			return checkCurrencyFullAccess(request, entityId, currencyGroup);
		} catch (SwtException e) {
			return true;
		}
	}
	
	
	
	public static boolean checkExpressionBuilderFullAccess(HttpServletRequest request, String calledFrom) {
		try{
			LogonDAO logonDAO = null;
			MenuItem menuItem = null;
			int accessId = 0;
			if(!SwtUtil.isEmptyOrNull(calledFrom)) {
				if (calledFrom.equalsIgnoreCase("categoryRuleAdd")) {
					logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
					menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_CATEGORY_MAINTENANCE+"", ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser());
					accessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
					if (accessId == 0 ) {
						return true;	
					}
					
				}else if (calledFrom.equalsIgnoreCase("stopRuleAdd")) {
					logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
					menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_STOPRULE_MAINTENANCE+"", ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser());
					accessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
					if (accessId == 0 ) {
						return true;	
					}
					
				}else if(calledFrom.equalsIgnoreCase("AccountGroupDetail")) {
					logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
					menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_PCM_ACCOUNT_GROUPS_MAINTENANCE+"", ((CommonDataManager) request.getSession()
							.getAttribute(SwtConstants.CDM_BEAN)).getUser());
					accessId = SwtUtil.getHierarchicalAccessId(menuItem, request);
					if (accessId == 0 ) {
						return true;	
					}
				}
				
			}
			return false;
			
		}catch(Exception e){
			return false;
		}
	}
}
