package org.swallow.util.struts;
/**
 * This class provides similar functionality to <PERSON><PERSON><PERSON> TokenHelper
 * to make migration easier while using standard Servlet API
 */

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.UUID;

public class TokenHelper {
    public static final String TOKEN_NAME_FIELD = "struts.token.name";
    public static final String DEFAULT_TOKEN_NAME = "token";

    /**
     * Generates a token and stores it in the session
     * @return the generated token value
     */
    public static String setToken() {
        return setToken(DEFAULT_TOKEN_NAME);
    }

    /**
     * Generates a token with the given name and stores it in the session
     * @param tokenName the name of the token
     * @return the generated token value
     */
    public static String setToken(String tokenName) {
        HttpServletRequest request = getRequest();
        HttpSession session = request.getSession(true);
        String token = generateToken();

        session.setAttribute(TOKEN_NAME_FIELD, tokenName);
        session.setAttribute(tokenName, token);

        return token;
    }

    /**
     * Validates that the submitted token matches the token stored in the session
     * @return true if the token is valid, false otherwise
     */
    public static boolean validToken() {
        HttpServletRequest request = getRequest();
        HttpSession session = request.getSession(true);

        String tokenName = (String) session.getAttribute(TOKEN_NAME_FIELD);
        if (tokenName == null) {
            tokenName = DEFAULT_TOKEN_NAME;
        }

        String sessionToken = (String) session.getAttribute(tokenName);
        String requestToken = request.getParameter(tokenName);

        return sessionToken != null && requestToken != null && sessionToken.equals(requestToken);
    }

    /**
     * Generates a unique token using UUID
     * @return a unique token
     */
    private static String generateToken() {
        return UUID.randomUUID().toString();
    }

    /**
     * Gets the current request from ThreadLocal storage
     * Note: You will need to implement a RequestContextHolder or similar mechanism
     * to make this work in your application
     * @return the current HttpServletRequest
     */
    private static HttpServletRequest getRequest() {
        // This is a placeholder - you'll need to implement this based on your framework
        // For example, if using Spring:
         return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // For a non-Spring application, you might pass the request explicitly to these methods
        // or store it in a ThreadLocal variable
        //throw new UnsupportedOperationException("You need to implement this method based on your application structure");
    }
}