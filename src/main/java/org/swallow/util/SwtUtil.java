/**
 * @(#)SwtUtil.java 1.0 12/12/2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.util;

import java.awt.Font;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Random;
import java.util.ResourceBundle;
import java.util.StringTokenizer;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Handler;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.util.ByteArrayDataSource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.builder.CompareToBuilder;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimplePBEConfig;
import org.json.XML;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.swallow.BeanUtil;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.cluster.ZkUtils;
import org.swallow.control.dao.EntityProcessDAO;
import org.swallow.control.dao.soap.Heartbeat;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.model.MenuAccessOptionsGui;
import org.swallow.control.model.Role;
import org.swallow.control.model.RoleTO;
import org.swallow.control.model.UserAuthDetails;
import org.swallow.control.model.UserMaintenance;
import org.swallow.control.service.ErrorLogManager;
import org.swallow.control.service.RoleManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.dao.LogonDAO;
import org.swallow.engine.EngineCall;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.service.impl.ILMReporting;
import org.swallow.maintenance.dao.HolidayDAO;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.CurrencyTO;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.model.BaseObject;
import org.swallow.model.MenuItem;
import org.swallow.model.ScreenInfo;
import org.swallow.model.User;
import org.swallow.service.LogonManager;
import org.swallow.smtp.pool.SmtpConnectionPool;
import org.swallow.smtp.transport.connection.ClosableSmtpConnection;
import org.swallow.smtp.transport.factory.SmtpConnectionFactory;
import org.swallow.util.CommonDataManager.AccessItem;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;
import org.swallow.web.RequestFilter;
import org.swallow.web.ResponseHeaderFilter;
import org.swallow.web.SessionFilter;
import org.swallow.web.XSSFilter;
import org.swallow.work.dao.SweepDetailDAO;
import org.swallow.work.model.OracleTimeDTO;
import org.swallow.work.service.MovementLockManager;
import org.swallow.work.service.WorkflowMonitorManager;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.springframework.context.ApplicationContext;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.security.AnyTypePermission;
import com.thoughtworks.xstream.security.NullPermission;
import freemarker.template.Configuration;
import freemarker.template.Template;

/**
 * This utility class defines a set of methods that performs common, often
 * re-used functions.<br>
 * Following are the few methods that are reused often by other classes:<br>
 * <ul>
 * <li>getUserCurrentEntity() - Gets the current entity for the given user.</li>
 * <li>getUserEntityAccessList() - Gets a collection of entities for the given
 * user who has full access rights.</li>
 * <li>getCurrentUserId() - Gets the current User Id</li>
 * <li>getCurrentHostId() - Gets the Host Id for the given user.</li>
 * </ul>
 *
 * <AUTHOR>
 */
public final class SwtUtil {
	public static ApplicationContext ctx = null;
	public static SessionFactory pcSessionFactory = null;
	public static SessionFactory sessionFactory = null;

	public static SmtpConnectionPool smtpConnectionPool;
	public static SmtpConnectionFactory transportFactory;

	private static final Log log = LogFactory.getLog(SwtUtil.class);
	private static HashMap weekdays = null;
	public static String appName = "swallowtech";
	public static ConcurrentMap<String, List<MenuItem>> roleMenuIdsMap = new ConcurrentHashMap<>();

	private static ArrayList<String> validLogonTokenList;
	private static HashMap<String, String> validIPadresses;
	private static HashMap<String, String> inactiveSession;

	//	public static HashMap<String, String> toRunJobs = new HashMap<String, String>();
	private static String myJobList = new String();

	public static ResourceBundle appMessages_en = null;
	public static ResourceBundle appMessages_fr = null;

	public static HashMap<String, String> dictionary_en = null;
	public static HashMap<String, String> dictionary_fr = null;
	public static String hostId = null;
	public static String appVersion="";
	private static XPath queryXPath =  null;
	private static Document queryDocument = null;
	private static XPath aclQueryXPath =  null;
	private static Document aclQueryDocument = null;
	public static String contextRealPath = null;
	public static boolean contextRealPathCorrectlySet = false;
	// a list of alpha numeric values
	public static Character[] alphaNumkeys ={' ','!','"','#','$','%','&','\'','(',')','*','+',',','-','.','/','0','1','2','3','4','5','6','7','8','9',':',';','<','=','>','?','@','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','[','\\',']','^','_','`','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','{','|','}','~'};
	// a list of width of each alpha numeric value equivalent with the alphaNumkeys list
	public static Integer[] alphaNumValues = {3, 4, 5, 10, 7, 11, 9, 3, 4, 4, 6, 10, 3, 4, 3, 4, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 4, 4, 10, 10, 10, 6, 12, 8, 8, 8, 9, 7, 6, 9, 9, 3, 3, 7, 6, 10, 8, 9, 7, 9, 8, 7, 7, 8, 8, 11, 8, 7, 8, 4, 4, 4, 10, 6, 6, 7, 7, 6, 7, 7, 4, 7, 7, 3, 3, 6, 3, 11, 7, 7, 7, 7, 4, 6, 4, 7, 7, 9, 7, 7, 6, 7, 4, 7, 10};
	public static class Dummy {}
	public static HashMap<String, XSSFCellStyle> excelStyle = null;

	public static ArrayList<LabelValueBean> keywords = new ArrayList();

	/*
	 * This variable is used to set the windows title suffix in the export
	 * content(excel, csv, pdf).
	 */
	private static String windowsTitleSuffix = null;
	// Jaxb unmarshaller instance
	private static Unmarshaller unmarshaller = null;

	public static Object getBean(String name) {
		return BeanUtil.getBean(name);
	}

	public static <T> T  getBean(Class<T> clazz) {
		return BeanUtil.getBean(clazz);
	}

	@SuppressWarnings("unchecked")
	public static ArrayList compareOldNewValue(String oldValue, String newValue) {
		ArrayList arrList = new ArrayList();
		String oldColumnValue = "";
		String newColumnValue = "";
		StringTokenizer stold = new StringTokenizer(oldValue, "^");
		StringTokenizer stnew = new StringTokenizer(newValue, "^");
		while (stold.hasMoreTokens()) {
			StringTokenizer stOldKeyValue = new StringTokenizer(stold
					.nextToken(), "=");
			StringTokenizer stNewKeyValue = new StringTokenizer(stnew
					.nextToken(), "=");
			String columnName = stOldKeyValue.nextToken();
			if (stOldKeyValue.hasMoreElements()) {
				oldColumnValue = stOldKeyValue.nextToken();
			}
			stNewKeyValue.nextToken();
			if (stNewKeyValue.hasMoreElements()) {
				newColumnValue = stNewKeyValue.nextToken();
			}
			if ((oldColumnValue != null) && (newColumnValue != null)
					&& (!oldColumnValue.equals(newColumnValue))) {
				arrList.add(new OldNewColumnValue(columnName, oldColumnValue,
						newColumnValue));
			}
		}
		return arrList;
	}

	/**
	 * <pre>
	 * This method gets current entity id for the logged-in user.
	 *
	 * If user has access right on the current entity then returns the same.
	 *
	 * If the user has no access on that entity, then get first entity from the
	 * allocated entity list and return the same as current entity
	 * </pre>
	 *
	 * @param session
	 * @return String
	 */
	public static String getUserCurrentEntity(HttpSession session) {
		return getUserCurrentEntity(session, getUserEntityAccessList(session));
	}

	public static HttpServletRequest getCurrentRequest() {
		return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
	}

	public static	HttpServletResponse getCurrentResponse() {
		return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
	}

	/**
	 * <pre>
	 * This method gets current entity id for the logged-in user.
	 *
	 * If user has access right on the current entity then returns the same.
	 *
	 * If the user has no access on that entity, then get first entity from the
	 * allocated entity list and return the same as current entity
	 * </pre>
	 *
	 * @param session
	 * @param colUserEntity
	 * @return String
	 */
	public static String getUserCurrentEntity(HttpSession session,
											  Collection<EntityUserAccess> colUserEntity) {

		if(session != null
				&& session.getAttribute(SwtConstants.CDM_BEAN) != null
				&& ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN)).getUser() != null) {

			// Get current entity id for the logged in user
			String currentEntity = ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getCurrentEntity();

			// Check the access right. If user has no access on the entity, then get
			// first entity from the list and returns the same as current entity
			if (SwtUtil.getUserEntityAccess(colUserEntity, currentEntity) == SwtConstants.ENTITY_NO_ACCESS) {
				currentEntity = colUserEntity.size() == 0 ? SwtConstants.EMPTY_STRING
						: colUserEntity.iterator().next().getEntityId();
			}
			return currentEntity;
		}else {
			return  null;
		}
	}

	/**
	 * <pre>
	 * This method returns domestic currency for the given entity. This method
	 * gets domestic currency for the given entity, then check whether role of
	 * logged-in user has access right on domestic currency. If yes returns
	 * domestic currency, otherwise returns first currency in the currency list
	 * belongs to role and entity
	 *
	 * TODO: Use this method instead of getDomesticCurrencyForUser
	 * </pre>
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @return String
	 * @throws SwtException
	 */
	public static String getEntityDomesticCurrency(HttpServletRequest request,
												   String hostId, String entityId) throws SwtException {
		// To hold list of currencies belongs to given entity and logged-in
		// user's role
		Collection<CurrencyAccessTO> colCurrency = null;
		// Role id for logged-in user
		String roleId = null;

		try {
			// log debug message
			log.debug("SwtUtil - [getEntityDomesticCurrency] - Enter");
			// Get role id to validate, whether the role has access right on
			// domestic currency
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get list of currencies belongs to logged-in user's role and given
			// entity
			colCurrency = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAccess(roleId, entityId);
			// Return domestic currency
			return getEntityDomesticCurrency(colCurrency, hostId, entityId);
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityDomesticCurrency", SwtUtil.class);
		} finally {
			// nullify objects
			colCurrency = null;
			roleId = null;
			// log debug message
			log.debug("SwtUtil - [getEntityDomesticCurrency] - Exit");
		}
	}

	/**
	 * This method returns domestic currency for the given entity. This method
	 * uses the given currency list belongs to role of logged-in user and check
	 * whether role has access right on domestic currency. If yes returns
	 * domestic currency, otherwise returns first currency in the currency list
	 *
	 * @param colCurrency
	 * @param hostId
	 * @param entityId
	 * @return String
	 * @throws SwtException
	 */
	public static String getEntityDomesticCurrency(
			Collection<CurrencyAccessTO> colCurrency, String hostId,
			String entityId) throws SwtException {
		// Iterate through currency list and validate whether role has access
		// right on domestic currency
		Iterator<CurrencyAccessTO> iterCurrency = null;
		// Domestic currency for the entity
		String domesticCurrency = null;
		// Flag denotes, whether role has access right on domestic currency
		boolean hasAccessRight;

		try {
			// log debug message
			log.debug("SwtUtil - [getEntityDomesticCurrency] - Enter");
			// Set default value
			hasAccessRight = false;
			// Get default currency for the given entity
			domesticCurrency = SwtUtil.getDomesticCurrencyForEntity(hostId,
					entityId);
			// Iterate through currency list and validate whether role has
			// access right on domestic currency
			iterCurrency = colCurrency.iterator();
			while (iterCurrency.hasNext()) {
				if (iterCurrency.next().getCurrencyId()
						.equals(domesticCurrency)) {
					// Role has access right on domestic currency, so exit loop
					hasAccessRight = true;
					break;
				}
			}
			// Return domestic currency
			return hasAccessRight ? domesticCurrency
					: (colCurrency.size() == 0 ? SwtConstants.EMPTY_STRING
					: colCurrency.iterator().next().getCurrencyId());
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getEntityDomesticCurrency] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getEntityDomesticCurrency", SwtUtil.class);
		} finally {
			// nullify objects
			iterCurrency = null;
			// log debug message
			log.debug("SwtUtil - [getEntityDomesticCurrency] - Exit");
		}
	}

	/**
	 * This method puts error message in request scope. This error message will
	 * be shown to the user at front-end
	 *
	 * @param request
	 * @param ex
	 */
	public static void putErrorInReq(HttpServletRequest request, Exception ex) {
		putReplyMessageInReq(request, SwtConstants.STR_FALSE, ex.getMessage(),
				getErrorDetails(ex));
	}

	/**
	 * This method return class name, method name and location where error
	 * occurs
	 *
	 * @param ex
	 * @return String
	 */
	public static String getErrorDetails(Exception ex) {
		return ex.getStackTrace()[0].getClassName() + "."
				+ ex.getStackTrace()[0].getMethodName() + ":"
				+ ex.getStackTrace()[0].getLineNumber();
	}

	/**
	 * This method puts request reply in request scope. This data will be shown
	 * to the user at front-end
	 *
	 * @param request
	 * @param status
	 * @param message
	 * @param location
	 */
	public static void putReplyMessageInReq(HttpServletRequest request,
											String status, String message, String location) {
		request.setAttribute(SwtConstants.REQ_REPLY_STATUS, status);
		request.setAttribute(SwtConstants.REQ_REPLY_MESSAGE, message);
		request.setAttribute(SwtConstants.REQ_REPLY_LOCATION, location);
	}

	/**
	 * This method sends given message to the user
	 *
	 * @param response
	 * @param message
	 */
	public static void sendResponse(HttpServletResponse response, String message) {
		try {
			response.getWriter().print(message);
		} catch (Exception ex) {
			// do nothing
		}
	}

	/**
	 * This method sends given message to the user
	 *
	 * @param response
	 * @param message
	 */
	public static void sendResponse(HttpServletResponse response, int message) {
		sendResponse(response, String.valueOf(message));
	}

	/**
	 * This method sends given message to the user
	 *
	 * @param response
	 * @param message
	 */
	public static void sendResponse(HttpServletResponse response, Exception ex) {
		sendResponse(response, ex.getMessage() + "\n" + getErrorDetails(ex));
	}

	/**
	 * This method is used to get the user entity access list.
	 *
	 * @param session
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Collection<EntityUserAccess> getUserEntityAccessList(
			HttpSession session) {
		// Holds the role details
		RoleTO role = null;
		try {
			log.debug("SwtUtil - getUserEntityAccessList - Entering");
			role = new RoleTO(((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId());
			log.debug("SwtUtil - getUserEntityAccessList - Exiting");
		} catch (Exception e) {
			// log the error
			log
					.error("Exception Catched in SwtUtil - [getUserEntityAccessList] "
							+ e.getMessage());
		}
		return getSwtMaintenanceCache().getEntityAccessCollection(role);
	}

	/**
	 * This method is used to get the user entity access list for a specific role.
	 *
	 * @param session
	 * @param roleId
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Collection<EntityUserAccess> getRoleEntityAccessList(
			HttpSession session, String roleId) {
		// Holds the role details
		RoleTO role = null;
		try {
			log.debug("SwtUtil - getUserEntityAccessListForRole - Entering");
			role = new RoleTO(roleId);
			log.debug("SwtUtil - getUserEntityAccessListForRole - Exiting");
		} catch (Exception e) {
			// log the error
			log
					.error("Exception Catched in SwtUtil - [getUserEntityAccessListForRole] "
							+ e.getMessage());
		}
		return getSwtMaintenanceCache().getEntityAccessCollection(role);
	}

	public static Collection getUserEntityAccessListForWorkFlow(String roleId) {
		RoleTO role = new RoleTO(roleId);
		return getSwtMaintenanceCache().getEntityAccessCollection(role);
	}

	public static String[] getGroupLevelNames(Collection coll, String entityId) {
		String[] metGrpLvlNames = new String[3];
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				EntityUserAccess entityUserAccess = (EntityUserAccess) itr
						.next();
				if (entityUserAccess.getEntityId().equals(entityId)) {
					metGrpLvlNames[0] = entityUserAccess.getGrpLevelName1();
					metGrpLvlNames[1] = entityUserAccess.getGrpLevelName2();
					metGrpLvlNames[2] = entityUserAccess.getGrpLevelName3();
				}
			}
		}
		return metGrpLvlNames;
	}

	/**
	 * <pre>
	 * This method iterate through given entity list and convert entity details
	 * as LabelValueBean object. This LabelValueBean list will be populated in
	 * the dropdown control.
	 *
	 * TODO Remove HttpSession variable as it is not being used
	 * </pre>
	 *
	 * @param colEntity
	 * @param session
	 * @return Collection<LabelValueBean>
	 * @throws SwtException
	 */
	public static Collection<LabelValueBean> convertEntityAcessCollectionLVL(
			Collection<EntityUserAccess> colEntity, HttpSession session)
			throws SwtException {
		// Holds the entity as LabelValueBean object (dropdown control)
		Collection<LabelValueBean> colEntityLVB = null;
		// Iterate entity list and convert them to LabelValueBean list
		Iterator<EntityUserAccess> itrEntity = null;
		// Entity details
		EntityUserAccess entity = null;
		String entityName = null;
		try {
			// log debug message
			log.debug("SwtUtil - [convertEntityAcessCollectionLVL] - Enter");
			// Initialize collection to hold entity detail as LabelValueBean
			// object
			colEntityLVB = new ArrayList<LabelValueBean>();

			// Iterate through entity list and get LabelValueBean list
			if (colEntity != null) {
				itrEntity = colEntity.iterator();
				while (itrEntity.hasNext()) {
					entity = (EntityUserAccess) itrEntity.next();
					// Add entity detail as LabelValueBean object
					entityName = SwtUtil.decreaseStringWidth(SwtConstants.ENTITY_NAME_MAX_WIDTH,entity.getEntityName(),SwtConstants.VERDANA_STYLE,SwtConstants.VERDANA_12P_SIZE);
					colEntityLVB.add(new LabelValueBean(entityName,
							entity.getEntityId()));
				}
			}
		} catch (Exception ex) {
			// log error message
			log
					.error("SwtUtil - [convertEntityAcessCollectionLVL] - Exception: "
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"convertEntityAcessCollectionLVL", SwtUtil.class);
		} finally {
			// nullify objects
			itrEntity = null;
			entity = null;
			// log debug message
			log.debug("SwtUtil - [convertEntityAcessCollectionLVL] - Exit");
		}
		return colEntityLVB;
	}
	/**
	 * <pre>
	 * This method iterate through given entity list and convert entity details
	 * as LabelValueBean object. This LabelValueBean list will be populated in
	 * the dropdown control.
	 *
	 * TODO Remove HttpSession variable as it is not being used
	 * </pre>
	 *
	 * @param colEntity
	 * @param session
	 * @return Collection<LabelValueBean>
	 * @throws SwtException
	 */
	public static Collection<LabelValueBean> convertEntityAcessCollectionLVLFullName(
			Collection<EntityUserAccess> colEntity, HttpSession session)
			throws SwtException {
		// Holds the entity as LabelValueBean object (dropdown control)
		Collection<LabelValueBean> colEntityLVB = null;
		// Iterate entity list and convert them to LabelValueBean list
		Iterator<EntityUserAccess> itrEntity = null;
		// Entity details
		EntityUserAccess entity = null;
		String entityName = null;
		try {
			// log debug message
			log.debug("SwtUtil - [convertEntityAcessCollectionLVL] - Enter");
			// Initialize collection to hold entity detail as LabelValueBean
			// object
			colEntityLVB = new ArrayList<LabelValueBean>();

			// Iterate through entity list and get LabelValueBean list
			if (colEntity != null) {
				itrEntity = colEntity.iterator();
				while (itrEntity.hasNext()) {
					entity = (EntityUserAccess) itrEntity.next();
					// Add entity detail as LabelValueBean object
					entityName =  entity.getEntityName();
					colEntityLVB.add(new LabelValueBean(entityName,
							entity.getEntityId()));
				}
			}
		} catch (Exception ex) {
			// log error message
			log
					.error("SwtUtil - [convertEntityAcessCollectionLVL] - Exception: "
							+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"convertEntityAcessCollectionLVL", SwtUtil.class);
		} finally {
			// nullify objects
			itrEntity = null;
			entity = null;
			// log debug message
			log.debug("SwtUtil - [convertEntityAcessCollectionLVL] - Exit");
		}
		return colEntityLVB;
	}

	public static int getUserEntityAccess(Collection coll, String inputEntityId) {
		int access = SwtConstants.ENTITY_NO_ACCESS;
		EntityUserAccess entityUserAccessObj = null;
		String entityId = null;
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				entityUserAccessObj = (EntityUserAccess) itr.next();
				if (entityUserAccessObj != null) {
					entityId = entityUserAccessObj.getEntityId();
					if (entityId.equals(inputEntityId)) {
						access = entityUserAccessObj.getAccess();
						break;
					}
				}
			}
		}

		return access;
	}

	public static User getCurrentUser(HttpSession session) throws SwtException {
		try {
			if (session.getAttribute(SwtConstants.CDM_BEAN) != null) {
				return ((CommonDataManager) session
						.getAttribute(SwtConstants.CDM_BEAN)).getUser();
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("Error on getCurrentUser, cause: "+e.getMessage(), e);
			return null;
		}
	}

	public static String getCurrentUserId(HttpSession session)
			throws SwtException {
		try {
			if (session.getAttribute(SwtConstants.CDM_BEAN) != null) {
				return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
						.getUser().getId().getUserId();
			} else {
				return null;
			}

		} catch (Exception e) {
			log.error("Error on getCurrentUserId, cause: "+e.getMessage(), e);
			return null;
		}
	}

	public static String getCurrentHostId(HttpSession session) {
		return getCurrentHostId();
	}

	public static String getCurrentHostId() {
		if (hostId == null || hostId.equals(SwtConstants.EMPTY_STRING)) {
			LogonDAO logonDAO = (LogonDAO) (SwtUtil.getBean("logonDAO"));
			hostId = logonDAO.getHostIdFromDB();
		}
		return hostId;
	}

	public static String getCurrentDateFormat(HttpSession session)
			throws SwtException {
		String dateFormat;
		if(session == null) {
			SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
			SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());

			Date systemDate = sysParams.getTestDate();

			if (systemDate == null)
				systemDate = new Date();

			dateFormat = SwtConstants.DATE_PAT + sysParams.getDateFormat();

			if (dateFormat.equals("datePat1"))
				dateFormat = "dd/MM/yyyy";
			else
				dateFormat = "MM/dd/yyyy";

			return dateFormat;
		}else {
			return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
					.getDateFormatValue();
		}
	}

	public static String getCurrentCurrencyFormat(HttpSession session)
			throws SwtException {

		String format;
		if(session == null) {
			SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
			SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());

			format = SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
			return format;
		}else {
			return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
					.getCurrencyFormat();


		}
	}

	public static SystemFormats getCurrentSystemFormats(HttpSession session)
			throws SwtException {
		return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
				.getSystemFormats();
	}

	public static void saveDataInSession(String dataName, Object data) {
		HttpSession session = UserThreadLocalHolder.getUserSession();
		try {
			if ((session != null)
					&& SessionManager.getInstance().getSessionMap()
					.containsKey(session.getId())) {
				session.setAttribute(dataName, data);
			}
		} catch (Exception e) {
			log.error("Error on saveDataInSession, cause: "+e.getMessage(), e);
		}
	}

	public static Object getDataFromSession(String dataName) {
		HttpSession session = UserThreadLocalHolder.getUserSession();
		Object data = null;
		try {
			if ((session != null)
					&& SessionManager.getInstance().getSessionMap()
					.containsKey(session.getId())) {
				data = session.getAttribute(dataName);
			}
		} catch (Exception e) {
			log.error("Error on getDataFromSession, cause: "+e.getMessage(), e);
		}
		return data;
	}

	public static void removeDataFromSession(String dataName) {
		HttpSession session = UserThreadLocalHolder.getUserSession();
		Object data = null;
		try {
			if ((session != null)
					&& SessionManager.getInstance().getSessionMap()
					.containsKey(session.getId())) {
				session.removeAttribute(dataName);
			}
		} catch (Exception e) {
			log.error("Error on removeDataFromSession, cause: "+e.getMessage(), e);
		}
	}

	public static String formatDate(Date date, String format) {
		SimpleDateFormat simpleDateFormat = null;
		if (date != null) {
			simpleDateFormat = new SimpleDateFormat(format);
		} else {
			return "";
		}
		return simpleDateFormat.format(date);
	}


	/**
	 * Formats an ISO date given as String into another String
	 * @param isoDate
	 * @param format
	 * @return
	 */
	public static String formatDate(String isoDate, String format) {

		if(isoDate != null && isoDate.contains("."))
			isoDate = isoDate.substring(0,isoDate.indexOf("."));

		isoDate=isoDate.replace("-", "/");
		SimpleDateFormat isoFormat = new SimpleDateFormat(format);
		return isoFormat.format(new Date(isoDate));
	}/**
	 * Formats String data into another String iso
	 * @param isoDate
	 * @param format
	 * @return
	 */
	public static String formatDateToIso(String Date, String format) {

		if(Date != null && Date.contains("."))
			Date = Date.substring(0,Date.indexOf("."));

		SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date;
		try {
			date = parseDate(Date,format);
			return isoFormat.format(date);
		} catch (SwtException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	public static Date parseDate(String str, String format) throws SwtException {
		Date date = null;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			date = simpleDateFormat.parse(str);
		} catch (Exception ex) {
			throw new SwtException(ex.getMessage());
		}
		return date;
	}

	public static String formatCurrency(Double value, String format) {
		DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
		String strValue = "";
		if (value != null) {
			strValue = decimalFormat.format(value);
			if (format.indexOf('2') != -1) {
				strValue = strValue.replaceAll("\\.", "^");
				strValue = strValue.replaceAll(",", ".");
				strValue = strValue.replaceAll("\\^", ",");
			}
		}
		return strValue;
	}

	/**
	 *
	 * This function is used to format the values based on the decimal places
	 * defined for currency
	 *
	 * @param currCode
	 *            as String
	 * @param value
	 *            as BigDecimal
	 * @return String - formatted value
	 * @throws SwtException
	 */
	public static String formatCurrency(String currCode, BigDecimal value)
			throws SwtException {

		// variable to hold HttpSession
		HttpSession session = null;
		// variable to hold decimals
		String decimals = null;
		// variable to hold format
		String format = null;
		// Declare CommonDataManager object
		CommonDataManager cdm = null;
		// variable to hold strValue
		String formattedVal = "";
		// DecimalFormat Instance
		DecimalFormat decimalFormat = null;
		try {
			log.debug("SwtUtil-[formatCurrency]-Entry");
			// get the user session
			session = UserThreadLocalHolder.getUserSession();
			// set default decimal values as 2
			decimals = "2";
			// get the currency format
			format = SwtConstants.CURRENCY_PAT + "1"; // take the
			// default
			// pattern
			// take the default number of places
			if (!SwtUtil.isEmptyOrNull(currCode)
					&& currCode.equals(SwtConstants.YES)) {
				decimals = "1";
			} else if (!SwtUtil.isEmptyOrNull(currCode)) {
				decimals = (String) CacheManager.getInstance()
						.getCurrencyDecimalHashTable().get(currCode);
			}
			// get the currency format
			if ((session != null)
					&& SessionManager.getInstance().getSessionMap()
					.containsKey(session.getId())) {
				cdm = (CommonDataManager) session
						.getAttribute(SwtConstants.CDM_BEAN);
				format = cdm.getCurrencyFormat();
			}else {
				SysParamsManager sysParamsManager =  (SysParamsManager)SwtUtil.getBean("sysParamsManager");
				SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());
				format = SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
			}
			// format the value to BigDecimal
			if (value != null) {

				// Condition to check dicla place is 0
				if (decimals.equals("0")) {
					decimalFormat = new DecimalFormat("#,##0");
				} else {
					decimalFormat = new DecimalFormat("#,##0."
							+ getZeroString(decimals));
				}
				// format the big decimal string
				formattedVal = decimalFormat.format(value);

				if (format.indexOf('2') != -1) {
					formattedVal = formattedVal.replaceAll("\\.", "^");
					formattedVal = formattedVal.replaceAll(",", ".");
					formattedVal = formattedVal.replaceAll("\\^", ",");
				}
			}
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [formatCurrency] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			log.debug("SwtUtil-[formatCurrency]-Exit");
			// To set null value for local variable
			cdm = null;
			// closing the session
			if (session != null) {
				session = null;
			}
			format = null;
		}
		return formattedVal;
	}
	/**
	 *
	 * This function is used to format the values based on the decimal places
	 * defined for currency
	 *
	 * @param currCode
	 *            as String
	 * @param value
	 *            as BigDecimal
	 * @return String - formatted value
	 * @throws SwtException
	 */
	public static String formatCurrency(String currCode, BigDecimal value, String format)
			throws SwtException {

		// variable to hold decimals
		String decimals = null;
		// variable to hold strValue
		String formattedVal = "";
		// DecimalFormat Instance
		DecimalFormat decimalFormat = null;
		try {
			log.debug("SwtUtil-[formatCurrency]-Entry");
			if(value == null)
				return "";
			// set default decimal values as 2
			decimals = "2";
			// default
			// pattern
			// take the default number of places
			if(!SwtUtil.isEmptyOrNull(currCode) && "All".equalsIgnoreCase(currCode)) {
				return value.toString();
			}
			if (!SwtUtil.isEmptyOrNull(currCode)
					&& currCode.equals(SwtConstants.YES)) {
				decimals = "1";
			} else if (!SwtUtil.isEmptyOrNull(currCode)) {
				decimals = (String) CacheManager.getInstance()
						.getCurrencyDecimalHashTable().get(currCode);
			}
			// format the value to BigDecimal
			if (value != null) {

				// Condition to check dicla place is 0
				if (decimals.equals("0")) {
					decimalFormat = new DecimalFormat("#,##0");
				} else {
					decimalFormat = new DecimalFormat("#,##0."
							+ getZeroString(decimals));
				}
				// format the big decimal string
				formattedVal = decimalFormat.format(value);

				if (format.indexOf('2') != -1) {
					formattedVal = formattedVal.replaceAll("\\.", "^");
					formattedVal = formattedVal.replaceAll(",", ".");
					formattedVal = formattedVal.replaceAll("\\^", ",");
				}
			}
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [formatCurrency] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			log.debug("SwtUtil-[formatCurrency]-Exit");
		}
		return formattedVal;
	}

	public static String formatBigDecimal(String bigValue, String decimals)
			throws SwtException {
		Pattern pat = Pattern.compile("(-?[0-9]+)([0-9]{3})");
		StringBuffer newStr = new StringBuffer();
		int index = bigValue.indexOf(".");
		if (index == -1) {
			newStr.append("." + getZeroString(decimals));
		} else {
			newStr.append(bigValue.substring(index, bigValue.length()));
			newStr
					.append(getZeroString(""
							+ (Integer.parseInt(decimals) - (bigValue.length()
							- index - 1))));
			bigValue = bigValue.substring(0, index);
		}

		while (pat.matcher(bigValue).matches()) {
			bigValue = bigValue.replaceFirst("(-?[0-9]+)([0-9]{3})", "$1,$2");
			newStr = new StringBuffer(bigValue.substring(bigValue.indexOf(","),
					bigValue.length())
					+ newStr.toString());
			bigValue = bigValue.substring(0, bigValue.indexOf(","));
		}

		newStr = new StringBuffer(bigValue + newStr);
		return newStr.toString();
	}

	/**
	 * Method to format the values for given currencies with its corresponding
	 * decimal places
	 *
	 * @param currCode
	 *            as String
	 * @param value
	 *            as Double
	 * @return String - formatted value
	 * @throws SwtException
	 */
	public static String formatCurrency(String currCode, Double value)
			throws SwtException {
		// local variable declaration
		// Session instance
		HttpSession session = null;
		// String to hold format
		String format = null;
		// String to hold decimal places deafult 2
		String decimals = null;
		// Common data manager instance
		CommonDataManager cdm = null;
		// Decimal format instance
		DecimalFormat decimalFormat = null;
		// String to hold formatted value
		String formattedVal = "";
		try {
			log.debug("SwtUtil-[formatCurrency]-Entry");
			session = UserThreadLocalHolder.getUserSession();

			// set default decimal value as 2
			decimals = "2";
			// Condition to check currency code is not null
			if (!SwtUtil.isEmptyOrNull(currCode)) {
				// to set the decimal places for corresponding currency code
				decimals = (String) CacheManager.getInstance()
						.getCurrencyDecimalHashTable().get(currCode);
			}

			// Condition to chcek session is not null
			if ((session != null)
					&& SessionManager.getInstance().getSessionMap()
					.containsKey(session.getId())) {
				cdm = (CommonDataManager) session
						.getAttribute(SwtConstants.CDM_BEAN);
				format = cdm.getCurrencyFormat();
			}else {
				SysParamsManager sysParamsManager =  (SysParamsManager)SwtUtil.getBean("sysParamsManager");
				SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());
				format = SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
			}

			// condition to chcek decimal is 0
			if (!SwtUtil.isEmptyOrNull(decimals) && decimals.equals("0"))
				decimalFormat = new DecimalFormat("#,##0");
			else
				decimalFormat = new DecimalFormat("#,##0."
						+ getZeroString(decimals));
			// Condition to chcek value is not null
			if (value != null) {
				// format the value based on the decimal format
				formattedVal = decimalFormat.format(value);

				if (format.indexOf('2') != -1) {
					formattedVal = formattedVal.replaceAll("\\.", "^");
					formattedVal = formattedVal.replaceAll(",", ".");
					formattedVal = formattedVal.replaceAll("\\^", ",");
				}
			}
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [formatCurrency] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			log.debug("SwtUtil-[formatCurrency]-Exit");
			// To set null value for local variable
			cdm = null;
			// closing the session
			if (session != null) {
				session = null;
			}
			format = null;
		}
		return formattedVal;
	}

	public static String formatCurrencyWithoutDecimals(String currCode,
													   Double value) throws SwtException {
		HttpSession session = UserThreadLocalHolder.getUserSession();
		String format = SwtConstants.CURRENCY_PAT + "1"; // take the default
		// pattern
		String decimals = "0"; // take the default number of places
		if ((session != null)
				&& SessionManager.getInstance().getSessionMap()
				.containsKey(session.getId())) {
			CommonDataManager cdm = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
			format = cdm.getCurrencyFormat();
		}

		DecimalFormat decimalFormat = new DecimalFormat("#,##0"
				+ getZeroString(decimals));
		String strValue = "";
		if (value != null) {
			strValue = decimalFormat.format(value);
			if (format.indexOf('2') != -1) {
				strValue = strValue.replaceAll("\\.", "^");
				strValue = strValue.replaceAll(",", ".");
				strValue = strValue.replaceAll("\\^", ",");
			}
		}
		return strValue;
	}

	/**
	 * Method to get zero decimal places currency
	 *
	 * @param decimals
	 * @return String
	 */
	private static String getZeroString(String decimals) throws SwtException {
		StringBuffer stringBuffer = null;
		int length;
		try {
			log.debug("Swtutil -  [getZeroString] - Entering ");
			// creates the string instanation
			stringBuffer = new StringBuffer();
			if (decimals != null && !decimals.equals("")) {
				length = Integer.parseInt(decimals);
				// if the length greater than 3
				if (length > 3)
					length = 3;
				for (int i = 0; i < length; i++) {
					stringBuffer.append("0");
				}
				return stringBuffer.toString();
			}
			log.debug("Swtutil -  [getZeroString] - Exit ");
		} catch (Exception exp) {
			log.error("SwtUtil  - [getZeroString] - Exception -"
					+ exp.getMessage());
			throw new SwtException(exp.getMessage());
		}
		return new String("0");
	}

	public static Double parseCurrency(String strValue, String format) {
		if (format.indexOf('2') != -1) {
			strValue = strValue.replaceAll("\\.", "");
			strValue = strValue.replaceAll(",", ".");
		}
		strValue = strValue.replaceAll(",", "");
		double dbl = Double.parseDouble(strValue);
		return new Double(dbl);
	}

	public static BigDecimal parseCurrencyBig(String strValue, String format) {
		if (format.indexOf('2') != -1) {
			strValue = strValue.replaceAll("\\.", "");
			strValue = strValue.replaceAll(",", ".");
		}
		strValue = strValue.replaceAll(",", "");
		return new BigDecimal(strValue);
	}

	public static java.sql.Date getTodaydate() {
		Calendar currenttime = Calendar.getInstance();
		java.sql.Date startdate = SwtUtil.truncateDateTime((currenttime)
				.getTime());
		return startdate;
	}

	public static java.sql.Date getBeforeAfterdate(int days) {
		Calendar currenttime = Calendar.getInstance();
		currenttime.add(Calendar.DATE, days);
		java.sql.Date startdate = SwtUtil.truncateDateTime((currenttime.getTime()));
		return startdate;
	}

	public static Collection getColumnNameValue(BaseObject baseObject)
			throws SwtException {
		ArrayList list = new ArrayList();
		OldNewColumnValue oldNewColumnValue = null;
		Field field = null;
		try {
			getInnerClassFields(baseObject, list);
		} catch (Exception ex) {
			log.error(ex);
			throw new SwtException(ex.getMessage());
		}

		return list;
	}

	private static void getInnerClassFields(Object baseObject, Collection list)
			throws Exception {
		OldNewColumnValue oldNewColumnValue = null;
		Field field = null;
		Field[] fields = baseObject.getClass().getDeclaredFields();
		Object fieldObj = null;
		for (int i = 0; i < fields.length; i++) {
			field = fields[i];
			field.setAccessible(true);
			if (!(field.getType().toString().startsWith("class java."))) {
				fieldObj = field.get(baseObject);
				if (fieldObj != null) {
					getInnerClassFields(fieldObj, list);
				}
			} else {
				oldNewColumnValue = new OldNewColumnValue(field.getName(), "",
						"" + field.get(baseObject));
				list.add(oldNewColumnValue);
			}
		}
	}

	public static Object copy(Object orig) {
		Object obj = null;
		ObjectOutputStream out = null;
		ObjectInputStream in = null;
		try {
			FastByteArrayOutputStream fbos = new FastByteArrayOutputStream();
			out = new ObjectOutputStream(fbos);
			out.writeObject(orig);
			out.flush();
			// Retrieve an input stream from the byte array and read
			// a copy of the object back in.
			in = new ObjectInputStream(fbos.getInputStream());
			obj = in.readObject();
		} catch (IOException e) {
			log.error("SwtUtil- [copy] :" + e.getMessage());
		} catch (ClassNotFoundException cnfe) {
			log.error("SwtUtil- [copy] :" + cnfe.getMessage());
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}

		return obj;
	}

	public static long getDays(int iMth, int iDay, int iYear) {
		long lDays = iDay - 1;
		int iYears = iYear - 1900;
		lDays += ((iYears * 365) + ((int) iYears / 4)); // for leap year

		for (int iM = 0; iM < iMth; iM++) {
			if (iM <= 6) { // months before July
				if ((iM % 2) == 0) {
					lDays += 31L;
				} else {
					if (iM == 1) {
						if ((iYear % 4) == 0) { // if leap year
							lDays += 29L;
						} else {
							lDays += 28L;
						}
					} else {
						lDays += 30L;
					}
				}
			} else if ((iM % 2) != 0) { // for mths with 31
				lDays += 31L;
			} else {
				lDays += 30L;
			}
		}
		return lDays;
	}

	public static String arrayToString(Object array, String seprator) {
		if (array == null) {
			return "";
		} else {
			Object obj = null;
			if (array instanceof Hashtable) {
				array = ((Hashtable) array).entrySet().toArray();
			} else if (array instanceof HashSet) {
				array = ((HashSet) array).toArray();
			} else if (array instanceof Collection) {
				array = ((Collection) array).toArray();
			}
			int length = Array.getLength(array);
			int lastItem = length - 1;
			StringBuffer sb = new StringBuffer("");

			for (int i = 0; i < length; i++) {
				obj = Array.get(array, i);

				// convert dates to String
				if(obj instanceof Date){
					obj = formatDate((Date)obj, SwtConstants.ISO_DATE_FORMAT);
				}else if(obj instanceof java.sql.Date){
					obj = formatDate(new Date(((java.sql.Date)obj).getTime()), SwtConstants.ISO_DATE_FORMAT);
				}

				// Append item
				if (obj != null) {
					sb.append(obj);
				} else {
					sb.append("");
				}

				if (i < lastItem) {
					sb.append(seprator);
				}
			}

			sb.append("");

			return sb.toString();
		}
	}

	public static String convertArrayToString(Collection coll) {
		StringBuffer retValue = new StringBuffer("");

		if (coll != null) {
			Iterator itr = coll.iterator();

			while ((itr != null) && itr.hasNext()) {
				retValue.append(itr.next()).append("\n");
			}
		}

		try {
			if (retValue.length() > 0) {
				retValue.delete(retValue.lastIndexOf("\n"), retValue.length());
			}
		} catch (ArrayIndexOutOfBoundsException ex) {
		}

		return retValue.toString();
	}

	public static String getUserAlertType(HttpSession session)
			throws SwtException {
		String alertType = "";
		CommonDataManager cdm = (CommonDataManager) session
				.getAttribute(SwtConstants.CDM_BEAN);

		if (cdm != null) {
			alertType = cdm.getUser().getAlertType();
		}

		return alertType;
	}

	public static Date parseDateGeneral(String str) throws SwtException {
		String format = null;
		HttpSession session = UserThreadLocalHolder.getUserSession();
		if (session != null) {
			CommonDataManager cdm = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
			if (cdm != null) {
				format = cdm.getDateFormatValue();
			}
		}else {
			format  = getCurrentDateFormat(null);
		}
		if (format != null) {
			return parseDate(str, format);
		} else {
			throw new SwtException("Date format is incorrect - " + format);
		}
	}

	public static final String encodePassword(String password, String user) {
		try {
			if (password != null) {
				password = password + SwtConstants.PSWD_SEPARATOR + user.trim();
				MessageDigest digest = MessageDigest.getInstance("MD5");

				digest.update(password.getBytes());

				byte[] bytes = digest.digest();
				StringBuffer buffer = new StringBuffer();

				for (int i = 0; i < bytes.length; i++) {
					int b = bytes[i] & 0xff;

					if (b < 16) {
						buffer.append("0");
					}
					buffer.append(Integer.toHexString(b));
				}
				password = buffer.toString();
			}
		} catch (Exception e) {
			log.error("Got The Exception -> " + e.getMessage());
		}
		return password.toUpperCase();
	}

	public static String getCodeLocation(Throwable ex) {
		if (null == ex) {
			return "";
		}
		StackTraceElement[] stackElements = ex.getStackTrace();
		String codeLocation = "";

		for (int lcv = 0; lcv < stackElements.length; lcv++) {
			String className = stackElements[lcv].getClassName();
			String packageName = extractPackageName(className);
			String simpleClassName = extractSimpleClassName(className);
			if (packageName.indexOf("org.swallow") >= 0) {
				codeLocation = simpleClassName + ":"
						+ stackElements[lcv].getMethodName()
						+ "  Line Number :"
						+ stackElements[lcv].getLineNumber();
				break;
			} else {
				continue;
			}
		}
		return codeLocation;
	} // End of displayStackTraceInformation().

	public static String extractPackageName(String fullClassName) {
		if ((null == fullClassName) || ("".equals(fullClassName))) {
			return "";
		}
		// The package name is everything preceding the last dot.
		// Is there a dot in the name?
		int lastDot = fullClassName.lastIndexOf('.');

		// Note that by fiat, I declare that any class name that has been
		// passed in which starts with a dot doesn't have a package name.
		if (0 >= lastDot) {
			return "";
		}
		// Otherwise, extract the package name.
		return fullClassName.substring(0, lastDot);
	}

	public static String extractSimpleClassName(String fullClassName) {
		if ((null == fullClassName) || ("".equals(fullClassName))) {
			return "";
		}
		// The simple class name is everything after the last dot.
		// If there's no dot then the whole thing is the class name.
		int lastDot = fullClassName.lastIndexOf('.');
		if (0 > lastDot) {
			return fullClassName;
		}
		// Otherwise, extract the class name.
		return fullClassName.substring(++lastDot);
	}

	public static String extractDirectClassName(String simpleClassName) {
		if ((null == simpleClassName) || ("".equals(simpleClassName))) {
			return "";
		}
		// The direct class name is everything after the last '$', if there
		// are any '$'s in the simple class name. Otherwise, it's just
		// the simple class name.
		int lastSign = simpleClassName.lastIndexOf('$');

		if (0 > lastSign) {
			return simpleClassName;
		}
		// Otherwise, extract the last class name.
		// Note that if you have a multiply-nested class, that this
		// will only extract the very last one. Extracting the stack of
		// nestings is left as an exercise for the reader.
		return simpleClassName.substring(++lastSign);
	}

	public static String unmungeSimpleClassName(String simpleClassName) {
		if ((null == simpleClassName) || ("".equals(simpleClassName))) {
			return "";
		}
		// Nested classes are set apart from top-level classes by using
		// the dollar sign '$' instead of a period '.' as the separator
		// between them and the top-level class that they sit
		// underneath. Let's undo that.
		return simpleClassName.replace('$', '.');
	}

	/**
	 * This is used to include the errors in error log file and screen
	 *
	 * @param swtexp
	 * @return none
	 * @throws SwtException
	 */
	public static void logErrorInDatabase(SwtException swtexp) {
		/* Methods local variable declaration */
		ErrorLogManager errorLogManager = null;
		ErrorLog errorLog = null;
		Date sysDate = null;
		try {
			log.debug("Swtutil -  [logErrorInDatabase] - Entering ");
			if ((swtexp.getErrorLogFlag() != null)
					&& swtexp.getErrorLogFlag().equals("Y")) {
				errorLogManager = (ErrorLogManager) (SwtUtil
						.getBean("errorLogManager"));
				errorLog = new ErrorLog();
				errorLog.setHostId(SwtUtil.getCurrentHostId());
				/*
				 * description -- error log entries containing system date
				 * instead of test date
				 */
				sysDate = SwtUtil.getSystemDatewithTime();
				errorLog.setErrorDate(sysDate);
				if((swtexp.getErrorCode() != null && ((swtexp.getErrorCode().indexOf("ILMDataReporting")>-1) || swtexp.getErrorCode().equals("MAIL_CONFIG")))){
					errorLog.setIpAddress("SYSTEM");
					errorLog.setErrorDesc(swtexp.getErrorDesc());
					errorLog.setErrorId(swtexp.getErrorId());
					errorLog.setSource(swtexp.getSrcCodeLocation());
					errorLogManager.logError(errorLog);
				}else {
					errorLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
					errorLog.setErrorDesc(swtexp.getErrorDesc());
					errorLog.setErrorId(swtexp.getErrorId());
					errorLog.setSource(swtexp.getSrcCodeLocation());
					if (errorLog.getErrorDesc() !=null && (errorLog.getErrorDesc().indexOf("ORA-00001") > 0 || errorLog.getErrorDesc().indexOf("ORA-04030") >0 || errorLog.getErrorDesc().indexOf("RPC: ") !=-1 || errorLog.getErrorDesc().indexOf("PCM MONITOR") !=-1)) {
						errorLogManager.logError(errorLog);
					}

				}

			}
			log.debug("Swtutil- [logErrorInDatabase] - Exiting ");
		} catch (Exception e) {
			// log the error
			log.error("Exception Catched in SwtUtil - [logErrorInDatabase] "
					+ e.getMessage());
		} finally {
			errorLogManager = null;
			errorLog = null;
			sysDate = null;
		}
	}

	public static ActionMessages logException(SwtException swtexp,
											  HttpServletRequest request, String modelName) {
		ActionMessages errors = new ActionMessages();
		logErrorInDatabase(swtexp);
		errors.add(modelName, new ActionMessage(swtexp.getErrorCode()));
		request.setAttribute("errordesc", swtexp.getSrcCodeLocation());
		request.setAttribute("errorCause", swtexp.getErrorDesc());
		return errors;
	}

	private static void popluateWeekDaysHashMap() {
		weekdays = new HashMap();
		weekdays.put(SwtConstants.WEEKDAY_MON, new Integer(Calendar.MONDAY));
		weekdays.put(SwtConstants.WEEKDAY_TUE, new Integer(Calendar.TUESDAY));
		weekdays.put(SwtConstants.WEEKDAY_WED, new Integer(Calendar.WEDNESDAY));
		weekdays.put(SwtConstants.WEEKDAY_THU, new Integer(Calendar.THURSDAY));
		weekdays.put(SwtConstants.WEEKDAY_FRI, new Integer(Calendar.FRIDAY));
		weekdays.put(SwtConstants.WEEKDAY_SAT, new Integer(Calendar.SATURDAY));
		weekdays.put(SwtConstants.WEEKDAY_SUN, new Integer(Calendar.SUNDAY));
	}

	public static boolean checkDateIsEntityWeekend(int todayDay,
												   String entityWeekend) {
		boolean isEntityWeekEnd = false;
		if (weekdays == null) {
			popluateWeekDaysHashMap();
		}
		Integer entityWeekEnd = (Integer) weekdays.get(entityWeekend);
		if (entityWeekEnd != null) {
			if (todayDay == entityWeekEnd.intValue()) {
				isEntityWeekEnd = true;
			}
		}
		return isEntityWeekEnd;
	}

	private static Long lastTimeSysDb  = null;
	private static Date sysDbDateAsDate  = null;

	public static Date getSystemDatewithTime() {
		// Use a thread-safe, non-blocking approach with caching
		return SystemDateHolder.INSTANCE.getSystemDate();
	}

	public static Date getSystemDatewithoutTime() {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String strNowDate = sdf.format(getSystemDatewithTime());
			return sdf.parse(strNowDate);
		} catch (Exception e) {
			return new Date();
		}
	}

	public static String getSystemDateString() {
		try {
			Date date = getSystemDatewithoutTime();
			HttpSession session = UserThreadLocalHolder.getUserSession();
			String dateFormat = SwtUtil.getCurrentDateFormat(session);
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
			String strDate = sdf.format(date);
			return strDate;
		} catch (Exception e) {
			log.error(e);
			return "";
		}
	}

	/**
	 * Method to get Previous working day Date
	 *
	 * @param entityId
	 * @param currencyCode
	 * @param facility
	 * @return String - preiuos date
	 */
	public static String getSystemDateMinusOneAsString(String entityId,
													   String currencyCode, String facility) {
		// Local variable declaration
		String hostId = null;
		HolidayDAO holidayDAO = null;
		Date date = null;
		Calendar cal = null;
		HttpSession session = null;
		String dateFormat = null;
		SimpleDateFormat sdf = null;
		String strDate = null;
		try {
			log.debug("SwtUtil -[getSystemDateMinusOneAsString]- Entry");
			hostId = getCurrentHostId();
			holidayDAO = (HolidayDAO) SwtUtil.getBean("holidayDAO");
			date = getSystemDatewithoutTime();
			cal = Calendar.getInstance();
			cal.setTime(date);

			cal = holidayDAO.getBusinessDateMinusOne(cal, entityId, hostId,
					currencyCode, facility);
			date = cal.getTime();

			session = UserThreadLocalHolder.getUserSession();
			dateFormat = SwtUtil.getCurrentDateFormat(session);

			sdf = new SimpleDateFormat(dateFormat);
			strDate = sdf.format(date);
			return strDate;
		} catch (Exception e) {
			log
					.error("Exception got in SwtUtil -[getSystemDateMinusOneAsString]- "
							+ e.getMessage());
			return "";
		} finally {
			log.debug("SwtUtil -[getSystemDateMinusOneAsString]- Exit");
			holidayDAO = null;
			hostId = null;
			entityId = null;
			sdf = null;
			cal = null;
			date = null;
			session = null;

		}
	}

	/**
	 *
	 * @param facility
	 * @param entityId
	 * @param currencyCode
	 * @param tabPosition
	 * @param accountId
	 * @return
	 */
	public static Date getSystemDatePluswithoutTime(String facility,
													String entityId, String currencyCode, int tabPosition,
													String accountId) {
		try {
			String hostId = getCurrentHostId();
			HolidayDAO holidayDAO = (HolidayDAO) SwtUtil.getBean("holidayDAO");
			/*
			 * Code modified by vivek for Mantis 1991 on 13-July-2012 Systemdate
			 * calcualted based on the entity offset
			 */
			Date systmdate = getSysParamDateWithEntityOffset(entityId);
			Calendar cal = Calendar.getInstance();
			cal.setTime(systmdate);
			cal = holidayDAO.getBusinessDate(facility, cal, entityId, hostId,
					currencyCode, tabPosition, accountId);
			return cal.getTime();
		} catch (SwtException swt) {
			log.error(swt);
		}
		return null;
	}

	/**
	 *
	 * @param facility
	 * @param entityId
	 * @param currencyCode
	 * @param tabPosition
	 * @param accountId
	 * @return
	 */
	public static String getSystemDatePluswithoutTimeAsString(String facility,
															  String entityId, String currencyCode, int tabPosition,
															  String accountId) {
		String strDate = "";
		try {
			Date date = getSystemDatePluswithoutTime(facility, entityId,
					currencyCode, tabPosition, accountId);
			HttpSession session = UserThreadLocalHolder.getUserSession();
			String dateFormat = SwtUtil.getCurrentDateFormat(session);
			SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
			strDate = sdf.format(date);
		} catch (SwtException swt) {
			log.error(swt);
		}
		return strDate;
	}

	/**
	 *
	 * @param facility
	 * @param entityId
	 * @param currencyCode
	 * @param tabPosition
	 * @param accountId
	 * @return
	 */
	public static Date getSystemDateWeekwithoutTime(String facility,
													String entityId, String currencyCode, int tabPosition,
													String accountId) {
		try {
			String hostId = getCurrentHostId();
			HolidayDAO holidayDAO = (HolidayDAO) SwtUtil.getBean("holidayDAO");
			Date systmdate = getSystemDatewithoutTime();
			Calendar cal = Calendar.getInstance();
			cal.setTime(systmdate);

			cal = holidayDAO.getBusinessDate(facility, cal, entityId, hostId,
					currencyCode, tabPosition, accountId);
			return cal.getTime();
		} catch (SwtException swt) {
			log.error(swt);
		}
		return null;
	}

	public static Date removeTime(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR, 0);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}

	public static String getDomesticCurrencyForEntity(String hostId,
													  String entityId) throws SwtException {
		LogonDAO logonDAO = (LogonDAO) (SwtUtil.getBean("logonDAO"));
		String domesticCurr = "";
		Collection coll = logonDAO.getDomesticCurrency(hostId, entityId);
		Entity ent = null;
		if (coll != null) {
			Iterator itr = coll.iterator();
			while (itr.hasNext()) {
				ent = (Entity) itr.next();
			}
		}
		if (ent != null) {
			domesticCurr = ent.getDomesticCurrency();
		}
		return domesticCurr;
	}

	public static String getDomesticCurrencyForUser(HttpServletRequest request,
													String hostId, String entityId) throws SwtException {
		String currencyId = "";
		String domesticCurrency = getDomesticCurrencyForEntity(hostId, entityId);
		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		ArrayList currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
		}
		if (domesticCurrency != null) {
			if ((currencyList != null) && (currencyList.size() > 0)) {
				Iterator itr = currencyList.iterator();
				while (itr.hasNext()) {
					LabelValueBean lb = (LabelValueBean) (itr.next());
					currencyId = lb.getValue();
					if (currencyId.equals(domesticCurrency)) {
						break;
					}
				}
				if (!currencyId.equals(domesticCurrency)) {
					itr = currencyList.iterator();
					LabelValueBean lb = (LabelValueBean) (itr.next());
					currencyId = lb.getValue();
				}
			}
		}
		return currencyId;
	}

	public static SwtMaintenanceCache getSwtMaintenanceCache() {
		return (SwtMaintenanceCache) getBean("SwtMaintenanceCache");
	}

	public static long getSessionTimeOutPeriod() {
		String sessionTimeOut = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.SESSION_TIMEOUT_PERIOD);
		long sessionTimeOutLong = Long.parseLong(sessionTimeOut);
		return sessionTimeOutLong;
	}

	public static boolean hasAcessToFacility(HttpServletRequest request) {
		CommonDataManager cdm = null;
		MenuItem menuItemToCheckAccess = null;
		MenuItem menuItemTemp = null;
		boolean foundWithParams = false;
		boolean isMenuContent = false;
		int menuListLenght  = 0;
		try {
			cdm = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN));

			if(cdm == null) {
				return true;
			}


			LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
			if(aclQueryXPath == null)
			{
				aclQueryXPath =  XPathFactory.newInstance().newXPath();
				DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
				DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
				aclQueryDocument = dBuilder.parse(ILMReporting.class.getResourceAsStream("/org/swallow/util/list_acl.xml"));
			}
			String url = ""+request.getAttribute("orginalURL");//getPathToFilter(request);

			String content = ""+request.getRequestURL() ;
			content = content.contains("/jsp/") ? content.substring(content.indexOf("/jsp/")) : "";
			if(SwtUtil.isEmptyOrNull(content)) {
				return true;
			}

			url = url != null && url.contains("/") ? url.substring(url.lastIndexOf("/") + 1) : null;
			// Return the result as string
			String menuItemIdXPath = "/acl/jspMenuAccess/menu[@url='"+url+"' and @jspPage='"+content+"']";

			NodeList menuItemNodes = (NodeList) aclQueryXPath.evaluate(menuItemIdXPath, aclQueryDocument, XPathConstants.NODESET);
			ArrayList<String> requestParameters = new ArrayList<String>();
			Enumeration params = request.getParameterNames();
			while(params.hasMoreElements()){
				String paramName = (String)params.nextElement();
				requestParameters.add(paramName+"="+request.getParameter(paramName));
			}
			menuListLenght = menuItemNodes.getLength();
			MenuItem menuItemToFind = null;
			if(menuListLenght>0 ) {
				isMenuContent  = true;
			}
			for (int i = 0; i < menuListLenght; i++) {
				Node menuItemNode = menuItemNodes.item(i);
				String menuItemId = menuItemNode.getAttributes().getNamedItem("menuItem").getNodeValue();
				// Do something with the menuItemId value
//			    menuItemToFind = logonDAO.getMenuItem(menuItemId+"", cdm.getUser());
				menuItemTemp = logonDAO.getMenuItem(menuItemId+"");
				if(menuItemTemp != null && menuItemTemp.getProgram().getProgramName() != null && menuItemTemp.getProgram().getProgramName().startsWith(url)){
					if(menuItemTemp.getProgram().getProgramName().indexOf("?") != -1) {
						String parametersStr = menuItemTemp.getProgram().getProgramName().split("\\?")[1];
						if(parametersStr!=null && parametersStr.length()>0){
							String[] list = parametersStr.split("&");
							foundWithParams = true;
							for(int j = 0; j < list.length ; j++){
								if(requestParameters.indexOf(list[j]) == -1) {
									foundWithParams = false;
								}
							}
							if(foundWithParams) {
								menuItemToFind = menuItemTemp;
								break;
							}
						}
					}else {
						menuItemToFind = menuItemTemp;
					}
				}

			}

			if(menuItemTemp != null && menuItemToFind == null) {
				if(menuListLenght>1) {
					return true;
				}else
				{
					menuItemToFind = menuItemTemp;
				}
			}

			if(menuItemToFind != null)
				menuItemToCheckAccess = logonDAO.getMenuItem(menuItemToFind.getItemId()+"",cdm.getUser());

			if(isMenuContent && (menuItemToCheckAccess == null)){
				return false;
			}else {
				return true;
			}



		} catch (Exception e) {
			e.printStackTrace();
			return true;
		}

	}

	public static String getMessage(String key, HttpServletRequest request, Object...values) {
		String message = "";
		ResourceBundle rb = appMessages_en;
		try {
			if(request != null)
			{
				if(request.getSession() != null)
				{

					User user = SwtUtil.getCurrentUser(request.getSession());
					if (user != null)
					{
						return getMessageFromSession(key, request.getSession(),values);
					}
					else if (request.getParameter("user_lang1234") != null)
					{
						String connectedUserLang = String.valueOf(request.getParameter("user_lang1234"));
						if (connectedUserLang.equalsIgnoreCase(SwtConstants.LANGUAGE_FRENCH))
						{
							rb = appMessages_fr;
						}
						else
						{
							rb = appMessages_en;
						}
					}	else
					{
						rb = appMessages_en;
					}
				}
			}
			else
			{
				rb = appMessages_en;
			}

			//Get the translated word
			if (rb != null && key != null)
			{
				message = rb.getString(key);
				if(values != null && values.length > 0) {
					message = MessageFormat.format(message, values);
				}
			}
		} catch (Exception e) {

		}
		return message;
	}

	public static String getMessageFromSession(String key, HttpSession session, Object...values) {
		String message = "";
		ResourceBundle rb = appMessages_en;
		try {
			if(session != null)
			{
				User user = SwtUtil.getCurrentUser(session);
				// From javadoc: getBundle caches instantiated resource bundles and may return the same resource bundle instance multiple times.
				if(user == null) {
					rb = appMessages_en;
				}
				else if(SwtConstants.LANGUAGE_FRENCH.equalsIgnoreCase(user.getLanguage()))
				{
					rb = appMessages_fr;//ResourceBundle.getBundle(SwtConstants.DICTIONARY_FRENCH);
				}
				else if(SwtConstants.LANGUAGE_ENGLISH.equalsIgnoreCase(user.getLanguage()))
				{
					rb = appMessages_en;//ResourceBundle.getBundle(SwtConstants.DICTIONARY_ENGLISH);
				}
			}
			else
			{
				rb = appMessages_en;
			}

			//Get the translated word
			if (rb != null && key != null)
			{
				message = rb.getString(key);
				if(values != null && values.length > 0) {
					message = MessageFormat.format(message, values);
				}
			}
		} catch (Exception e) {

		}
		return message;
	}

	public static String getInactiveDisable() {
		String inactiveDisable = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.INACTIVE_DISABLE);

		return inactiveDisable;
	}

	// code added for getting the maxCurrent User for parallel processing of
	// Matching Process
	public static String getMaxConcurrentMatchingProcess() {
		return PropertiesFileLoader.getInstance().getPropertiesValue(
				SwtConstants.CONCURRENT_MATCHING_PROCESS_USER);
	}

	/**
	 * @param dateString
	 * @return
	 * @throws ParseException
	 */
	public static Date string2SqlDate(String dateString) throws ParseException,
			SwtException {
		SimpleDateFormat myDateFormat = new SimpleDateFormat("dd/MM/yy");
		java.sql.Date date = null;

		if (dateString != null) {
			validateDateFormat(dateString);
			date = SwtUtil.truncateDateTime(myDateFormat.parse(dateString));
		}

		return date;
	}
	/**
	 * This method need an date as param and truncate the time component of this date to make it equal 00:00:00
	 * then returns and java.sql.Date object from the truncated date.
	 * @param dateString
	 * @return
	 * @throws ParseException
	 */
	public static java.sql.Date truncateDateTime(Date tempDate) {
		java.sql.Date date = null;
		try {

			if (tempDate != null) {
				Calendar cal = Calendar.getInstance();
				cal.setTime(tempDate);
				cal.set(Calendar.HOUR_OF_DAY, 0);
				cal.set(Calendar.MINUTE, 0);
				cal.set(Calendar.SECOND, 0);
				cal.set(Calendar.MILLISECOND, 0);
				date = new java.sql.Date(cal.getTime().getTime());
			}
		}catch(Exception e) {
		}
		return date;
	}

	/**
	 * This method is used to check whther user have full access on given
	 * entityId and currencyCode or not. If yes it returns true otherwise false.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public static boolean getFullAccesOnCurrencyAndEntity(
			HttpServletRequest request, String hostId, String entityId,
			String currencyId) throws SwtException {
		int entityAccess = getEntityAccessType(request, entityId);
		int ccyGrpAccess = getCcyGrpAccessType(request, hostId, entityId,
				currencyId);
		if ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS)
				&& (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS)) {
			return true;
		} else {
			return false;
		}
	}

	// START: added by KaisBS for mantis 1762 (1054_SEL_076)
	/**
	 * This method is used to check whether user have full or read access on
	 * given entityId and currencyCode or not. If yes it returns true otherwise
	 * false.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public static boolean getFullOrReadAccesOnCurrencyAndEntity(
			HttpServletRequest request, String hostId, String entityId,
			String currencyId) throws SwtException {
		int entityAccess = getEntityAccessType(request, entityId);
		int ccyGrpAccess = getCcyGrpAccessType(request, hostId, entityId,
				currencyId);
		if (((entityAccess == SwtConstants.ENTITY_FULL_ACCESS) && (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS))
				|| ((entityAccess == SwtConstants.ENTITY_FULL_ACCESS) && (ccyGrpAccess == SwtConstants.CURRENCYGRP_READ_ACCESS))
				|| ((entityAccess == SwtConstants.ENTITY_READ_ACCESS) && (ccyGrpAccess == SwtConstants.CURRENCYGRP_FULL_ACCESS))
				|| ((entityAccess == SwtConstants.ENTITY_READ_ACCESS) && (ccyGrpAccess == SwtConstants.CURRENCYGRP_READ_ACCESS))

		) {
			return true;
		} else {
			return false;
		}
	}

	// START: added by KaisBS for mantis 1762 (1054_SEL_076)

	/**
	 * This method is used to get the currencyGroup Acces of the current user
	 * for given entity and currency code.
	 *
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @return
	 * @throws SwtException
	 */
	public static int getCcyGrpAccessType(HttpServletRequest request,
										  String hostId, String entityId, String currencyId)
			throws SwtException {

		// ccyGrpAccess variable
		int ccyGrpAccess = 0;
		// variable to hold roleId
		String roleId = null;
		try {
			log.debug("SwtUtil - getCcyGrpAccessType - Enter");
			// get the roleId
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the currency access
			ccyGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
					roleId, entityId, currencyId);
			log.debug("SwtUtil - getCcyGrpAccessType - Exit");
		} catch (Exception e) {
			// log the error
			log.error("Exception Catched in SwtUtil - [getCcyGrpAccessType] "
					+ e.getMessage());
		} finally {
			roleId = null;
		}
		return ccyGrpAccess;
	}

	/**
	 * This method is used to get entity access status
	 *
	 * @param request
	 * @param entityId
	 * @return
	 */
	public static int getEntityAccessType(HttpServletRequest request,
										  String entityId) {
		Collection collEntity = SwtUtil.getUserEntityAccessList(request
				.getSession());
		int entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);

		return entityAccess;
	}

	/**
	 * This method is used to get entity access status
	 *
	 * @param request
	 * @param entityId
	 * @return
	 */
	public static int getEntityAccessTypeForRole(HttpServletRequest request,
												 String entityId, String roleId) {
		Collection collEntity = SwtUtil.getRoleEntityAccessList(request
				.getSession(), roleId);
		int entityAccess = SwtUtil.getUserEntityAccess(collEntity, entityId);

		return entityAccess;
	}

	public static boolean isAlphaNumeric(final String s) {
		final char[] chars = s.toCharArray();

		for (int x = 0; x < chars.length; x++) {
			final char c = chars[x];

			if ((c >= 'a') && (c <= 'z')) {
				continue; // lowercase
			}

			if ((c >= 'A') && (c <= 'Z')) {
				continue; // uppercase
			}

			if ((c >= '0') && (c <= '9')) {
				continue; // numeric
			}

			return false;
		}

		return true;
	}

	/**
	 * method to validate date Format and also to validate no of Days and No. of
	 * months.
	 *
	 * @param dateString
	 * @throws SwtException
	 */
	private static void validateDateFormat(String dateString)
			throws SwtException {
		if ((Integer.parseInt(dateString.substring(0, dateString.indexOf("/"))) > 31)
				|| (Integer.parseInt(dateString.substring(dateString
				.indexOf("/") + 1, dateString.lastIndexOf("/"))) > 12)) {
			throw new SwtException(
					"Invalid Date Format Please Enter date in dd/MM/yy format");
		}
	}

	/**
	 * This method returns the test date used by the system. If it is null it
	 * returns system date with time.
	 *
	 * @param hostId
	 * @return testDate Date
	 * @throws SwtException
	 */
	public static Date getTestDateFromParams(String hostId) throws SwtException {
		Date testDate = null;
		SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil
				.getBean("sysParamsManager");
		SysParams sysParams = sysParamsManager.getSysParamsDetail(hostId);
		testDate = sysParams.getTestDate();

		if (testDate != null) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(testDate);

			Calendar currentCal = Calendar.getInstance();

			cal.set(Calendar.HOUR_OF_DAY, currentCal.get(Calendar.HOUR_OF_DAY));
			cal.set(Calendar.MINUTE, currentCal.get(Calendar.MINUTE));
			cal.set(Calendar.SECOND, currentCal.get(Calendar.SECOND));
			cal.set(Calendar.MILLISECOND, currentCal.get(Calendar.MILLISECOND));

			return cal.getTime();
		} else {
			testDate = new Date();
		}

		return testDate;
	}

	/**
	 * This method sets the menu, entity and currgrp access status.
	 *
	 * @param request
	 * @param menuItemName
	 * @param parentMenuItemName
	 * @param entityId
	 * @param currencyId
	 * @return accessInd
	 * @throws SwtException
	 */
	public static int getMenuEntityCurrGrpAccess(HttpServletRequest request,
												 String entityId, String currencyId) throws SwtException {
		int accessInd = 0;
		int menuAccess = 0;
		int entityAccess = 0;
		int currGrpAccess = 0;
		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
		String menuAccessAsStr = request.getParameter("menuAccessId");
		if ((menuAccessAsStr != null) && !menuAccessAsStr.equals("")
				&& !menuAccessAsStr.equals("null")) {
			menuAccess = Integer.parseInt(menuAccessAsStr);
		}
		if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
			entityAccess = 0;
		} else {
			Collection coll = getUserEntityAccessList(request.getSession());
			entityAccess = getUserEntityAccess(coll, entityId);
		}
		if (SwtUtil.isEmptyOrNull(currencyId) || currencyId.equalsIgnoreCase("all")) {
			currGrpAccess = 0;
		} else {
			if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase("all") || entityId.equalsIgnoreCase("*DEFAULT*")) {
				currGrpAccess = 0;
			}else {
				currGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
						roleId, entityId, currencyId);
			}
		}
		if ((menuAccess == 1) || (entityAccess == 1) || (currGrpAccess == 1)) {
			accessInd = 1;
		}
		if ((menuAccess == 2) || (entityAccess == 2) || (currGrpAccess == 2)) {
			accessInd = 2;
		}
		request.setAttribute("menuAccessId", "" + menuAccessAsStr);
		request.setAttribute("menuEntityCurrGrpAccess", "" + accessInd);
		return accessInd;
	}

	/**
	 * This method sets the menu, entity and currgrp access status for a specific role.
	 *
	 * @param request
	 * @param menuItemName
	 * @param parentMenuItemName
	 * @param entityId
	 * @param currencyId
	 * @param roleId
	 * @return accessInd
	 * @throws SwtException
	 */
	public static int getMenuEntityCurrGrpAccessForRole(HttpServletRequest request,
														String entityId, String currencyId, String roleId) throws SwtException {
		int accessInd = 0;
		int menuAccess = 0;
		int entityAccess = 0;
		int currGrpAccess = 0;

		String menuAccessAsStr = request.getParameter("menuAccessId");
		if ((menuAccessAsStr != null) && !menuAccessAsStr.equals("")
				&& !menuAccessAsStr.equals("null")) {
			menuAccess = Integer.parseInt(menuAccessAsStr);
		}
		if ((entityId != null) && !entityId.equals("")) {
			Collection coll = getRoleEntityAccessList(request.getSession(), roleId);
			entityAccess = getUserEntityAccess(coll, entityId);
		}
		if ((currencyId != null) && !currencyId.equals("")) {
			currGrpAccess = SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
					roleId, entityId, currencyId);
		}
		if ((menuAccess == 1) || (entityAccess == 1) || (currGrpAccess == 1)) {
			accessInd = 1;
		}
		request.setAttribute("menuAccessId", "" + menuAccessAsStr);
		request.setAttribute("menuEntityCurrGrpAccess", "" + accessInd);
		return accessInd;
	}

	/**
	 * This method returns the property value.
	 *
	 * @param request
	 * @param entityId
	 * @param menuItemId
	 * @param classId
	 * @param PropertyName
	 * @return PropertyValue
	 * @throws
	 */

	public static String getPropertyValue(HttpServletRequest request,
										  String entityId, String menuItemId, String classId,
										  String propertyName) {

		HttpSession session = request.getSession();
		String userId = "";
		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN);
		User user = CDM.getUser();
		userId = user.getId().getUserId();

		List userPreferenceList = (List) session.getAttribute(userId);
		ScreenInfo screenInfo;
		String propertyValue = "";
		if (userPreferenceList != null && userPreferenceList.size() > 0) {
			for (int userCounter = 0; userCounter < userPreferenceList.size(); userCounter++) {
				screenInfo = (ScreenInfo) userPreferenceList.get(userCounter);
				if (screenInfo.getId().getEntityId().toUpperCase().equals(
						entityId.toUpperCase())) {
					if (screenInfo.getId().getScreenId().toUpperCase().equals(
							menuItemId.toUpperCase())) {
						if (screenInfo.getId().getClsId().toUpperCase().equals(
								classId.toUpperCase())) {
							if (screenInfo.getId().getPropertyName()
									.toUpperCase().equals(
											propertyName.toUpperCase())) {
								propertyValue = screenInfo.getPropertyValue();
								break;
							}
						}
					}
				}
			}
		}
		return propertyValue;
	}

	/**
	 * This method returns the property value.
	 *
	 * @param request
	 * @param menuItemId
	 * @param classId
	 * @param PropertyName
	 * @return PropertyValue
	 * @throws
	 */

	public static String getPropertyValue(HttpServletRequest request,
										  String menuItemId, String classId, String propertyName) {
		String entityId = SwtUtil.getUserCurrentEntity(request.getSession());
		return getPropertyValue(request, entityId, menuItemId, classId,
				propertyName);
	}

	/**
	 * This method sets the property value.
	 *
	 * @param request
	 * @param entityId
	 * @param menuItemId
	 * @param classId
	 * @param PropertyName
	 * @param PropertyValue
	 * @return
	 * @throws
	 */

	public static void setPropertyValue(HttpServletRequest request,
										String entityId, String menuItemId, String classId,
										String propertyName, String propertyValue) {
		HttpSession session = request.getSession();
		String userId = "";
		User user = null;

		CommonDataManager CDM = (CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN);
		user = CDM.getUser();
		userId = user.getId().getUserId();

		List userPreferenceList = (List) session.getAttribute(userId);
		ScreenInfo screenInfo;
		boolean newFlag = true;
		if (userPreferenceList != null) {
			for (int userCounter = 0; userCounter < userPreferenceList.size(); userCounter++) {
				screenInfo = (ScreenInfo) userPreferenceList.get(userCounter);
				if (screenInfo.getId().getEntityId().toUpperCase().equals(
						entityId.toUpperCase())) {
					if (screenInfo.getId().getScreenId().toUpperCase().equals(
							menuItemId.toUpperCase())) {
						if (screenInfo.getId().getClsId().toUpperCase().equals(
								classId.toUpperCase())) {
							if (screenInfo.getId().getPropertyName()
									.toUpperCase().equals(
											propertyName.toUpperCase())) {
								screenInfo.setPropertyValue(propertyValue);
								newFlag = false;
								break;
							}
						}
					}
				}
			}// end of for
		}// end of if

		if (newFlag) {

			screenInfo = new ScreenInfo();
			screenInfo.getId().setHostId(user.getId().getHostId());
			screenInfo.getId().setEntityId(entityId);
			screenInfo.getId().setUserId(userId);
			screenInfo.getId().setScreenId(menuItemId);
			screenInfo.getId().setClsId(classId);
			screenInfo.getId().setPropertyName(propertyName);
			screenInfo.setPropertyValue(propertyValue);
			if (userPreferenceList != null) {
				userPreferenceList.add(screenInfo);

			} else {
				userPreferenceList = new ArrayList();
				userPreferenceList.add(screenInfo);
			}

		}
		session.setAttribute(userId, userPreferenceList);
	}

	/**
	 * This method sets the property value.
	 *
	 * @param request
	 * @param menuItemId
	 * @param classId
	 * @param PropertyName
	 * @param PropertyValue
	 * @return
	 * @throws
	 */

	public static void setPropertyValue(HttpServletRequest request,
										String menuItemId, String classId, String propertyName,
										String propertyValue) {
		String entityId = SwtUtil.getUserCurrentEntity(request.getSession());
		setPropertyValue(request, entityId, menuItemId, classId, propertyName,
				propertyValue);
	}

	/**
	 * This method sets the menu, entity and currgrp access status.
	 *
	 * @param request
	 * @param menuItemName
	 * @param parentMenuItemName
	 * @param entityId
	 * @param currGrpId
	 * @return accessInd
	 * @throws SwtException
	 */
	public static int getMenuEntityCurrGrpAccessWithoutCurrency(
			HttpServletRequest request, String entityId, String currGrpId)
			throws SwtException {
		int accessInd = 0;
		int menuAccess = 0;
		int entityAccess = 0;
		int currGrpAccess = 0;

		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		String menuAccessAsStr = request.getParameter("menuAccessId");

		if ((menuAccessAsStr != null) && !menuAccessAsStr.equals("")
				&& !menuAccessAsStr.equals("null")) {
			menuAccess = Integer.parseInt(menuAccessAsStr);
		}

		if ((entityId != null) && !entityId.equals("")) {
			Collection coll = getUserEntityAccessList(request.getSession());
			entityAccess = getUserEntityAccess(coll, entityId);
		}

		if ((currGrpId != null) && !currGrpId.equals("")) {
			currGrpAccess = SwtUtil.getSwtMaintenanceCache()
					.getCurrencyGroupAccess(roleId, entityId, currGrpId);
		}

		if ((menuAccess == 1) || (entityAccess == 1) || (currGrpAccess == 1)) {
			accessInd = 1;
		}

		request.setAttribute("menuAccessId", "" + menuAccessAsStr);
		request.setAttribute("menuEntityCurrGrpAccess", "" + accessInd);
		return accessInd;
	}

	/*
	 * This method return the List of Location . @param roleId @return
	 * Collection
	 */
	public static Collection getUserLocationAccessListLVB(HttpSession session,
														  String entityId) throws SwtException {
		String roleId = ((CommonDataManager) session
				.getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
		String hostId = getCurrentHostId();

		return getSwtMaintenanceCache().getLocationAccessList(hostId, roleId,
				entityId);
	}

	/*
	 * Method returns the Time with milliseconds @return String
	 */
	public static String getTimeWithMilliseconds() {
		// To store the time with milliseconds
		String timeWithMilliseconds = "";
		// To store the new date instance
		Date date = new Date();
		// Stores the Format in which time has to be displayed.
		Format fmtDate = new SimpleDateFormat("HH:mm:ss.SSS");
		// To store the date in the Above specified Format
		timeWithMilliseconds = fmtDate.format(date);
		return timeWithMilliseconds;
	}

	/*
	 * This method return the List of Metagroup . @param entityId @return
	 * Collection
	 */
	public static Collection getMetagroupDetails(String entityId)
			throws SwtException {
		String hostId = getCurrentHostId();

		return getSwtMaintenanceCache().getMetagroupDetails(hostId, entityId);
	}

	public static Collection getGroupDetails(String entityId)
			throws SwtException {
		String hostId = getCurrentHostId();

		return getSwtMaintenanceCache().getGroupDetails(hostId, entityId);
	}

	/**
	 * This method puts all Currencies with View OR FullAcess in request.
	 *
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            currencyListName: Name of the currency List object in request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param boolean
	 *            allCurrCode : true if want to fetch "All" as currency
	 *            otherwise false
	 */
	public static void putCurrencyListInReq(HttpServletRequest request,
											String currencyListName, String hostId, String entityId,
											boolean allCurrCode) throws SwtException {
		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
		ArrayList currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);
		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
		}
		Collection currencyDetails = new ArrayList();

		if (currencyList != null && currencyList.size() > 0) {
			if (allCurrCode) {
				currencyDetails.add(new LabelValueBean(SwtConstants.ALL_LABEL,
						SwtConstants.ALL_VALUE));
				currencyDetails.addAll(currencyList);
			} else {
				currencyDetails = currencyList;
			}
		}
		request.setAttribute("currencyList", currencyDetails);
	}

	/**
	 * This method first parse the given string value with given format, then
	 * formats the value. If the value is whole no then removes the decimal
	 * part.
	 *
	 * @param strValue
	 * @param format
	 * @return String
	 */
	public static String getFormattedCurrencyBig(String strValue, String format) {
		// parse the string value with the given format
		BigDecimal bigValue = parseCurrencyBig(strValue, format);
		// If it is whole no then removes the decimal part
		if (isWholeNumber(bigValue)) {
			String tmp = bigValue.toString();
			// If the no contains decimal part then remove them else send the no
			// as it is
			if (tmp.indexOf(".") != -1) {
				strValue = tmp.substring(0, tmp.indexOf("."));
			} else {
				strValue = tmp;
			}
		} else {
			// The no has decimal value, so send the no as it is
			strValue = bigValue.toString();
		}
		return strValue;
	}

	/**
	 * This method converts the given no to absolute and check the no is whole
	 * no or not, it returns true if the given no is whole no otherwise returns
	 * false
	 *
	 * @param number
	 * @return boolean
	 */
	public static boolean isWholeNumber(BigDecimal number) {
		return (number.abs().divideAndRemainder(BigDecimal.ONE)[1]
				.doubleValue() > 0) ? false : true;
	}

	/**
	 * isEmptyOrNull
	 *
	 * @param strValue
	 * @return boolean
	 *
	 * This method returns true if the given string is null or empty string,
	 * otherwise returns false
	 */
	public static boolean isEmptyOrNull(String strValue) {
		if (strValue == null || strValue.trim().length() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * This function is used to get the system date from S_SYSTEM_PARAMETERS
	 * table
	 *
	 * @return Date
	 * @throws SwtException
	 */
	public static Date getSystemDateFromDB() throws SwtException {
		// To hold the system date value
		Date systeDateValue = null;
		// SysParamsManager instance
		SysParamsManager sysParamsManager = null;
		try {
			// get the SysParamsManager instance
			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			// get the system date from DB
			systeDateValue = sysParamsManager.getDBSytemDate();
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getSystemDateFromDB] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			sysParamsManager = null;
		}
		return systeDateValue;
	}
	/**
	 * This function is used to get the currency group of given currencyId and entityId
	 * table
	 *
	 * @return Date
	 * @throws SwtException
	 */
	public static String getCurrencyGroup(String entityId,String currencyId) throws SwtException {
		// To hold the currencyGroup value
		String currencyGroup = null;
		// SysParamsManager instance
		SysParamsManager sysParamsManager = null;
		try {
			// get the SysParamsManager instance
			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			// get the system date from DB
			currencyGroup = sysParamsManager.getCurrencyGroup(entityId, currencyId);
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getCurrencyGroup] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			sysParamsManager = null;
		}
		return currencyGroup;
	}

	/**
	 * This function is used to format the system date with given date
	 *
	 * @param sysDate
	 * @return String
	 * @throws SwtException
	 */
	public static String getSysDateWithFmt(Date sysDate) throws SwtException {
		// system date in string
		String systemDBDateString = null;
		// HttpSession instance
		HttpSession session = null;
		// To hold current dateformat
		String dateFormat = null;
		try {
			// get the user session
			session = UserThreadLocalHolder.getUserSession();
			// get the date format for current session
			dateFormat = SwtUtil.getCurrentDateFormat(session);
			// get the formatted system date
			systemDBDateString = formatDate(sysDate, dateFormat);
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getSysDateWithFmt] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			session = null;
			dateFormat = null;
		}
		return systemDBDateString;
	}

	/**
	 * This function is used to get the Date object representing the calendar
	 * time value for given date
	 *
	 * @param sysDate
	 * @return Date
	 * @throws SwtException
	 */
	public static Date getDBSysDatewithTime(Date sysDate) throws SwtException {
		// Calendar instance
		Calendar systemCal = null;
		try {
			// get the calender instance
			systemCal = Calendar.getInstance();
			// set the given date
			systemCal.setTime(sysDate);
			// set the hour
			systemCal.set(Calendar.HOUR, 0);
			// set the hour of day
			systemCal.set(Calendar.HOUR_OF_DAY, 0);
			// set the minute
			systemCal.set(Calendar.MINUTE, 0);
			// set the second
			systemCal.set(Calendar.SECOND, 0);
			// set the millisecond
			systemCal.set(Calendar.MILLISECOND, 0);
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getDBSysDatewithTime] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		}
		return systemCal.getTime();
	}

	/**
	 * This function is used to get the incremental date object representing the
	 * calendar time value for given date and incremental value
	 *
	 * @param dateIncrement
	 * @param sysDate
	 * @return Date
	 * @throws SwtException
	 */
	public static Date getDBSysDatewithoutTime(int dateIncrement, Date sysDate)
			throws SwtException {
		// System Date
		Date systemDate = null;
		// Calendar instance
		Calendar systemCal = null;
		try {
			// get the date representing the calendar time value
			systemDate = getDBSysDatewithTime(sysDate);
			// get the instance for Calendar
			systemCal = Calendar.getInstance();
			// set the systmdate in the calender
			systemCal.setTime(systemDate);
			// add the increment date
			systemCal.add(Calendar.DATE, dateIncrement);
		} catch (Exception ex) {
			log
					.error("Exception Catched in SwtUtil - [getDBSysDatewithoutTime] "
							+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			systemDate = null;
		}
		return systemCal.getTime();
	}

	/**
	 * This function is used to get the incremental date value in string
	 * representing the calendar time value for given date and incremental value
	 *
	 * @param dateIncrement
	 * @param sysDate
	 * @return String
	 * @throws SwtException
	 */
	public static String getDBSysDatewithoutTimeAsString(int dateIncrement,
														 Date sysDate) throws SwtException {
		// Date in string
		String sysDateInStr = null;
		// Date instance
		Date systemDate = null;
		// HttpSession instance
		HttpSession session = null;
		// Current Date Format
		String dateFormat = null;
		// SimpleDateFormat instance
		SimpleDateFormat sdfFormat = null;
		try {
			// get the date representing the calendar time value with given
			// dateIncrement
			systemDate = getDBSysDatewithoutTime(dateIncrement, sysDate);
			// get the currenct user sesion
			session = UserThreadLocalHolder.getUserSession();
			// get the current date format from session
			dateFormat = SwtUtil.getCurrentDateFormat(session);
			// Instantiate the SimpleDateFormat
			sdfFormat = new SimpleDateFormat(dateFormat);
			// get the formatted system date value
			sysDateInStr = sdfFormat.format(systemDate);
		} catch (Exception ex) {
			log
					.error("Exception Catched in SwtUtil - [getDBSysDatewithoutTimeAsString] "
							+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			systemDate = null;
			session = null;
			dateFormat = null;
			sdfFormat = null;
		}
		return sysDateInStr;
	}

	public static Date getSysParamDate() {
		Calendar cal = Calendar.getInstance();
		Date systeDateValue = null;
		try {
			SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			systeDateValue = sysParamsManager.getDBSytemDate();
			cal.setTime(systeDateValue);
			Calendar currentCal = Calendar.getInstance();
			cal.set(Calendar.HOUR_OF_DAY, currentCal.get(Calendar.HOUR_OF_DAY));
			cal.set(Calendar.MINUTE, currentCal.get(Calendar.MINUTE));
			cal.set(Calendar.SECOND, currentCal.get(Calendar.SECOND));
			cal.set(Calendar.MILLISECOND, currentCal.get(Calendar.MILLISECOND));
		} catch (SwtException swt) {
			log.error(swt);
		}
		return cal.getTime();
	}

	public static String getWhiteSpace(String str) {
		String value = "";
		if (str != null && str.length() > 0) {
			value = str.replace(" ", "&nbsp;");
		}
		return value;
	}

	/**
	 * This method is used to get the window title suffix from the '.properties'
	 * file and set it as the title in the export content(excel, csv, pdf) in
	 * all the screens that have export options.<br>
	 *
	 * @return String - windowsTitleSuffix <br>
	 */
	public static String getWindowsTitleSuffix() {
		// assigning the value of 'windows.title.suffix' from properties file.
		windowsTitleSuffix = PropertiesFileLoader.getInstance()
				.getPropertiesValue("windows.title.suffix");
		// Checking whether the value of windowsTitleSuffix for null/empty.
		// If null/empty, it'll be set as empty string.
		if (SwtUtil.isEmptyOrNull(windowsTitleSuffix)) {
			windowsTitleSuffix = "";
		} else {
			// if windows.title.suffix is not null/empty, sets the value to
			// windowsTitleSuffix
			windowsTitleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
		}
		return windowsTitleSuffix;
	}

	/**
	 * getReportingCurrencyForEntity() This method is used to get the reporting
	 * currency
	 *
	 * @param hostId
	 * @param entityId
	 * @return String
	 * @throws SwtException
	 */
	@SuppressWarnings("unchecked")
	public static String getReportingCurrencyForEntity(String hostId,
													   String entityId) throws SwtException {
		// variable to hold reporting currency
		String reportingCurr = "";
		// LogonDAO object
		LogonDAO logonDAO = null;
		// Collection to hold the entity details
		Collection<Entity> coll = null;
		// Entity object
		Entity ent = null;
		// Iterator used to iterate the collection
		Iterator<Entity> itr = null;
		try {
			// get the logonDAO
			logonDAO = (LogonDAO) (SwtUtil.getBean("logonDAO"));
			// get the entity details
			coll = logonDAO.getDomesticCurrency(hostId, entityId);
			if (coll != null) {
				// iterate the collection
				itr = coll.iterator();
				// Get the entity object
				while (itr.hasNext()) {
					ent = (Entity) itr.next();
				}
			}
			// get the reporting currency
			if (ent != null) {
				reportingCurr = ent.getReprotingCurrency();
			}
		} catch (Exception e) {
			// log the error
			log
					.error("Exception Catched in SwtUtil - [getReportingCurrencyForEntity] "
							+ e.getMessage());
		} finally {
			logonDAO = null;
			coll = null;
			ent = null;
			itr = null;
		}
		return reportingCurr;
	}

	/**
	 * This method to set the Tab Label value for Seven tab Screens(Account
	 * Breakdown Monitor, Exclude Outstantings, Offered Queue,Confirmed
	 * Queeue,Suspend Queue)
	 *
	 * @param request
	 * @param entityId
	 * @param hostId
	 * @param currencyCode
	 * @param sysformat
	 * @param flag
	 * @param tabFlag
	 * @return
	 */

	public static void getTabsLabel(HttpServletRequest request,
									String entityId, String hostId, String currencyCode,
									SystemFormats sysformat, Boolean flag, String tabFlag) {
		// variable to hold the todayTablabel
		String todayTablabel = null;
		// variable to hold the todayTabPlusOnelabel
		String todayTabPlusOnelabel = null;
		// variable to hold the todayTabPlusTwolabel
		String todayTabPlusTwolabel = null;
		// variable to hold the todayTabPlusThreelabel
		String todayTabPlusThreelabel = null;
		// variable to hold the todayTabPlusFourlabel
		String todayTabPlusFourlabel = null;
		// variable to hold the todayTabPlusFivelabel
		String todayTabPlusFivelabel = null;
		// variable to hold the todayTabPlusSixlabel
		String todayTabPlusSixlabel = null;
		// To get the todaySysDate
		Date todaySysDate = null;
		// To get the todaySysDate
		Date todaySysDatePlusOne = null;
		// To get the todaySysDatePlusTwo
		Date todaySysDatePlusTwo = null;
		// To get the todaySysDatePlusThree
		Date todaySysDatePlusThree = null;
		// To get the todaySysDatePlusFour
		Date todaySysDatePlusFour = null;
		// To get the todaySysDatePlusFive
		Date todaySysDatePlusFive = null;
		// To get the todaySysDatePlusSix
		Date todaySysDatePlusSix = null;
		// To get the todaySysDate
		String todaySysDate1 = null;
		// To get the todaySysDate
		String todaySysDatePlusOne1 = null;
		// To get the todaySysDatePlusTwo
		String todaySysDatePlusTwo1 = null;
		// To get the todaySysDatePlusThree
		String todaySysDatePlusThree1 = null;
		// To get the todaySysDatePlusFour
		String todaySysDatePlusFour1 = null;
		// To get the todaySysDatePlusFive
		String todaySysDatePlusFive1 = null;
		// To get the todaySysDatePlusSix
		String todaySysDatePlusSix1 = null;
		// To get the current date format
		String currentDateFormat = null;
		// To hold the system date
		Date sysDate = null;
		try {
			// get the domestic currency if currency is null
			if (currencyCode == null)
				currencyCode = SwtUtil.getDomesticCurrencyForUser(request,
						hostId, entityId);
			// get the current date format value
			currentDateFormat = sysformat.getDateFormatValue();
			// get the system date.
			// Start : Method modified by Balaji for Mantis 1991
			sysDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			// End : Method modified by Balaji for Mantis 1991
			// Get the todaySysDate value
			todaySysDate = SwtUtil.getDBSysDatewithoutTime(0, sysDate);
			// Get the todaySysDatePlusOne value
			todaySysDatePlusOne = SwtUtil.getDBSysDatewithoutTime(1, sysDate);
			// Get the todaySysDatePlusTwo value
			todaySysDatePlusTwo = SwtUtil.getDBSysDatewithoutTime(2, sysDate);
			// Get the todaySysDatePlusThree value
			todaySysDatePlusThree = SwtUtil.getDBSysDatewithoutTime(3, sysDate);
			// Get the todaySysDatePlusFour value
			todaySysDatePlusFour = SwtUtil.getDBSysDatewithoutTime(4, sysDate);
			// Get the todaySysDatePlusFive value
			todaySysDatePlusFive = SwtUtil.getDBSysDatewithoutTime(5, sysDate);
			// Get the todaySysDatePlusSix value
			todaySysDatePlusSix = SwtUtil.getDBSysDatewithoutTime(6, sysDate);
			// If currentDateFormat equals 'dd/MM/yyyy' , Get the seven tabs
			// label
			if (currentDateFormat.equals("dd/MM/yyyy")) {
				// Get the todayTablabel value
				todayTablabel = SwtUtil.formatDate(todaySysDate, "dd/MM");
				// Get the todayTabPlusOnelabel value
				todayTabPlusOnelabel = SwtUtil.formatDate(todaySysDatePlusOne,
						"dd/MM");
				// Get the todayTabPlusTwolabel value
				todayTabPlusTwolabel = SwtUtil.formatDate(todaySysDatePlusTwo,
						"dd/MM");
				// Get the todayTabPlusThreelabel value
				todayTabPlusThreelabel = SwtUtil.formatDate(
						todaySysDatePlusThree, "dd/MM");
				// Get the todayTabPlusFourlabel value
				todayTabPlusFourlabel = SwtUtil.formatDate(
						todaySysDatePlusFour, "dd/MM");
				// Get the todayTabPlusFivelabel value
				todayTabPlusFivelabel = SwtUtil.formatDate(
						todaySysDatePlusFive, "dd/MM");
				// Get the todayTabPlusSixlabel value
				todayTabPlusSixlabel = SwtUtil.formatDate(todaySysDatePlusSix,
						"dd/MM");
				// Get the todaySysDate1 value
				todaySysDate1 = SwtUtil.formatDate(todaySysDate, "dd/MM/yyyy");
				// Get the todaySysDatePlusOne1 value
				todaySysDatePlusOne1 = SwtUtil.formatDate(todaySysDatePlusOne,
						"dd/MM/yyyy");
				// Get the todaySysDatePlusTwo1 value
				todaySysDatePlusTwo1 = SwtUtil.formatDate(todaySysDatePlusTwo,
						"dd/MM/yyyy");
				// Get the todaySysDatePlusThree1 value
				todaySysDatePlusThree1 = SwtUtil.formatDate(
						todaySysDatePlusThree, "dd/MM/yyyy");
				// Get the todaySysDatePlusFour1 value
				todaySysDatePlusFour1 = SwtUtil.formatDate(
						todaySysDatePlusFour, "dd/MM/yyyy");
				// Get the todaySysDatePlusFive1 value
				todaySysDatePlusFive1 = SwtUtil.formatDate(
						todaySysDatePlusFive, "dd/MM/yyyy");
				// Get the todaySysDatePlusSix1 value
				todaySysDatePlusSix1 = SwtUtil.formatDate(todaySysDatePlusSix,
						"dd/MM/yyyy");
			} else {
				// Get the todayTablabel value
				todayTablabel = SwtUtil.formatDate(todaySysDate, "MM/dd");
				// Get the todayTabPlusOnelabel value
				todayTabPlusOnelabel = SwtUtil.formatDate(todaySysDatePlusOne,
						"MM/dd");
				// Get the todayTabPlusTwolabel value
				todayTabPlusTwolabel = SwtUtil.formatDate(todaySysDatePlusTwo,
						"MM/dd");
				// Get the todayTabPlusThreelabel value
				todayTabPlusThreelabel = SwtUtil.formatDate(
						todaySysDatePlusThree, "MM/dd");
				// Get the todayTabPlusFourlabel value
				todayTabPlusFourlabel = SwtUtil.formatDate(
						todaySysDatePlusFour, "MM/dd");
				// Get the todayTabPlusFivelabel value
				todayTabPlusFivelabel = SwtUtil.formatDate(
						todaySysDatePlusFive, "MM/dd");
				// Get the todayTabPlusSixlabel value
				todayTabPlusSixlabel = SwtUtil.formatDate(todaySysDatePlusSix,
						"MM/dd");
				// Get the todaySysDate1 value
				todaySysDate1 = SwtUtil.formatDate(todaySysDate, "MM/dd/yyyy");
				// Get the todaySysDatePlusOne1 value
				todaySysDatePlusOne1 = SwtUtil.formatDate(todaySysDatePlusOne,
						"MM/dd/yyyy");
				// Get the todaySysDatePlusTwo1 value
				todaySysDatePlusTwo1 = SwtUtil.formatDate(todaySysDatePlusTwo,
						"MM/dd/yyyy");
				// Get the todaySysDatePlusThree1 value
				todaySysDatePlusThree1 = SwtUtil.formatDate(
						todaySysDatePlusThree, "MM/dd/yyyy");
				// Get the todaySysDatePlusFour1 value
				todaySysDatePlusFour1 = SwtUtil.formatDate(
						todaySysDatePlusFour, "MM/dd/yyyy");
				// Get the todaySysDatePlusFive1 value
				todaySysDatePlusFive1 = SwtUtil.formatDate(
						todaySysDatePlusFive, "MM/dd/yyyy");
				// Get the todaySysDatePlusSix1 value
				todaySysDatePlusSix1 = SwtUtil.formatDate(todaySysDatePlusSix,
						"MM/dd/yyyy");
			}
			// set the todayTablabel to request
			request.setAttribute("todayTablabel", todayTablabel);
			// set the todayTabPlusOnelabel to request
			request.setAttribute("todayTabPlusOnelabel", todayTabPlusOnelabel);
			// set the todayTabPlusTwolabel to request
			request.setAttribute("todayTabPlusTwolabel", todayTabPlusTwolabel);
			// set the todayTabPlusThreelabel to request
			request.setAttribute("todayTabPlusThreelabel",
					todayTabPlusThreelabel);
			// set the todayTabPlusFourlabel to request
			request
					.setAttribute("todayTabPlusFourlabel",
							todayTabPlusFourlabel);
			// set the todayTabPlusFivelabel to request
			request
					.setAttribute("todayTabPlusFivelabel",
							todayTabPlusFivelabel);
			// set the todayTabPlusSixlabel to request
			request.setAttribute("todayTabPlusSixlabel", todayTabPlusSixlabel);
			// set the todayTablabel to request
			request.setAttribute("todaySysDate", todaySysDate1);
			// set the todayTabPlusOnelabel to request
			request.setAttribute("todaySysDatePlusOne", todaySysDatePlusOne1);
			// set the todayTabPlusTwolabel to request
			request.setAttribute("todaySysDatePlusTwo", todaySysDatePlusTwo1);
			// set the todayTabPlusThreelabel to request
			request.setAttribute("todaySysDatePlusThree",
					todaySysDatePlusThree1);
			// set the todayTabPlusFourlabel to request
			request.setAttribute("todaySysDatePlusFour", todaySysDatePlusFour1);
			// set the todayTabPlusFivelabel to request
			request.setAttribute("todaySysDatePlusFive", todaySysDatePlusFive1);
			// set the todayTabPlusSixlabel to request
			request.setAttribute("todaySysDatePlusSix", todaySysDatePlusSix1);
			if (tabFlag != null) {
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForToday",
						checkTabFlag(tabFlag.charAt(0)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusOne",
						checkTabFlag(tabFlag.charAt(1)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusTwo",
						checkTabFlag(tabFlag.charAt(2)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusThree",
						checkTabFlag(tabFlag.charAt(3)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusFour",
						checkTabFlag(tabFlag.charAt(4)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusFive",
						checkTabFlag(tabFlag.charAt(5)));
				// to check the Normal day or weekend and set the flag
				request.setAttribute("isBusinessDayForTodayPlusSix",
						checkTabFlag(tabFlag.charAt(6)));
			}
		} catch (Exception e) {
			log.error("Exception Catched in SwtUtil - [getTabsLabel] "
					+ e.getMessage());
		} finally {
			// nullify objects
			todayTablabel = null;
			todayTabPlusOnelabel = null;
			todayTabPlusTwolabel = null;
			todayTabPlusThreelabel = null;
			todayTabPlusFourlabel = null;
			todayTabPlusFivelabel = null;
			todayTabPlusSixlabel = null;
			todaySysDate = null;
			todaySysDatePlusOne = null;
			todaySysDatePlusTwo = null;
			todaySysDatePlusThree = null;
			todaySysDatePlusFour = null;
			todaySysDatePlusFive = null;
			todaySysDatePlusSix = null;
			todaySysDate1 = null;
			todaySysDatePlusOne1 = null;
			todaySysDatePlusTwo1 = null;
			todaySysDatePlusThree1 = null;
			todaySysDatePlusFour1 = null;
			todaySysDatePlusFive1 = null;
			todaySysDatePlusSix1 = null;
			currentDateFormat = null;
			sysDate = null;
		}
	}

	/**
	 * This method to check the weekend('W') or Normal day('N')and retrun the
	 * flag
	 *
	 * @param flag
	 * @return boolean
	 */

	public static boolean checkTabFlag(char flag) {
		// to check the Normal day
		if (flag == 'N')
			return true;
		else
			return false;
	}

	/**
	 * This method is used to get the entity access with default entity id
	 *
	 * @param inputcoll
	 * @return Collection
	 */
	@SuppressWarnings("unchecked")
	public static Collection<LabelValueBean> convertEntityAcessCollectionLVLWithDefault(
			Collection inputcoll) {
		// Holds the collection of entity access
		Collection<LabelValueBean> returnColl = null;
		// Holds the entity access values
		LabelValueBean labelValueBeanObj = null;
		// Declares the EntityUserAccess object
		EntityUserAccess entityUserAccessObj = null;
		Iterator itr = null;
		try {
			log
					.debug("SwtUtil - convertEntityAcessCollectionLVLWithDefault - Entering");
			// Creating new instance of Entity Access object
			returnColl = new ArrayList<LabelValueBean>();
			/* Set initialize the default entity id for combobox */
			labelValueBeanObj = new LabelValueBean("*DEFAULT*", "*DEFAULT*");
			/* To collect the label bean objet */
			returnColl.add(labelValueBeanObj);
			if (inputcoll != null) {
				itr = inputcoll.iterator();
				while (itr.hasNext()) {
					entityUserAccessObj = (EntityUserAccess) itr.next();
					// Instantiating Entity Access object
					labelValueBeanObj = new LabelValueBean(entityUserAccessObj
							.getEntityName(), entityUserAccessObj.getEntityId());
					/* To collect the label bean objet */
					returnColl.add(labelValueBeanObj);
				}
			}
			log
					.debug("SwtUtil - convertEntityAcessCollectionLVLWithDefault - Exiting");
		} catch (Exception e) {
			// log the error
			log
					.error("Exception Catched in SwtUtil - [convertEntityAcessCollectionLVLWithDefault] "
							+ e.getMessage());
		} finally {
			/* To set null value for method local variable's */
			labelValueBeanObj = null;
			entityUserAccessObj = null;
			itr = null;
		}
		return returnColl;
	}

	/*
	 * This Method is used to get the Last Refreshed time.
	 */
	/**
	 * This method used to Show 'Last refresh' time in screens that have refresh
	 * function
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public static String getLastRefTime(HttpServletRequest request,
										String entityId) throws SwtException {
		/* Variable Declaration for WorkflowMonitorManager */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for OracleTimeDTO */
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for strLastRefreshTime */
		String strLastRefreshTime = null;
		/* Variable Declaration for SimpleDateFormat */
		SimpleDateFormat formatter = null;
		/* Variable Declaration for serverDate */
		Date serverDate = null;
		/* Variable Declaration for lastRefresh */
		String lastRefresh = null;
		/* Variable Declaration for StringTokenizer */
		StringTokenizer st = null;
		/* Variable Declaration for entityTimeOffset */
		String entityTimeOffset = null;
		/* Variable Declaration for hourStr */
		String hourStr = null;
		/* Variable Declaration for minute */
		String minuteStr = null;
		/* Variable Declaration for hour */
		int hour = 0;
		/* Variable Declaration for minute */
		int minute = 0;
		try {

			log.debug("SwtUtil - [getLastRefTime] - " + "Entry");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			/* get Oracle System time. */
			// Start: Code modified by Balaji for mantis 1991
			// Condition to check entity id is null or all
			if (SwtUtil.isEmptyOrNull(entityId)
					|| entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// set default entity Id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			/* get Entity Time Offset for user current Entity. */
			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
			// End: Code modified by Balaji for mantis 1991
			/* set time format. */
			formatter = new SimpleDateFormat("HH:mm:ss");
			/* Server Date parsed from given time string. */
			serverDate = getSysParamDateWithEntityOffset(entityId);
			/* get Calendar instance. */
			Calendar cal = Calendar.getInstance();
			/* set server date in calendar. */
			cal.setTime(serverDate);
//			/* check whether Entity Time Offset contains "+" or "-" */
			/* set time format for display. */
			formatter = new SimpleDateFormat("HH:mm");
			/* get Last Refresh time with HH:mm format. */
			lastRefresh = formatter.format(cal.getTime());
			/* add entityTimeOffset with Last Refresh time. */
			lastRefresh = lastRefresh + " (" + entityTimeOffset + ")";
			/* Trim Last Refresh time. */
			lastRefresh = lastRefresh.trim();
			log.debug("SwtUtil - [getLastRefTime] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getLastRefTime] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			/* null the objects created already. */
			workFlowMgr = null;
			oraSystemTime = null;
			strLastRefreshTime = null;
			formatter = null;
			serverDate = null;
			st = null;
			entityTimeOffset = null;
			hourStr = null;
			minuteStr = null;
		}

		return lastRefresh;
	}

	
	/*
	 * This Method is used to get the Last Refreshed time.
	 */
	
	/**
	 * This method used to Show 'Last refresh' time in screens that have refresh
	 * function
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public static String getLastRefTimeOnGMTOffset(HttpServletRequest request,
			String entityId) throws SwtException {
		/* Variable Declaration for WorkflowMonitorManager */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for OracleTimeDTO */
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for strLastRefreshTime */
		String strLastRefreshTime = null;
		/* Variable Declaration for SimpleDateFormat */
		SimpleDateFormat formatter = null;
		/* Variable Declaration for serverDate */
		Date serverDate = null;
		/* Variable Declaration for lastRefresh */
		String lastRefresh = null;
		/* Variable Declaration for StringTokenizer */
		StringTokenizer st = null;
		/* Variable Declaration for entityTimeOffset */
		String entityTimeOffset = null;
		/* Variable Declaration for hourStr */
		String hourStr = null;
		/* Variable Declaration for minute */
		String minuteStr = null;
		/* Variable Declaration for hour */
		int hour = 0;
		/* Variable Declaration for minute */
		int minute = 0;
		try {
			
			log.debug("SwtUtil - [getLastRefTimeOnGMTOffset] - " + "Entry");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			/* get Oracle System time. */
			// Start: Code modified by Balaji for mantis 1991
			// Condition to check entity id is null or all
			if (SwtUtil.isEmptyOrNull(entityId)
					|| entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// set default entity Id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			/* get Entity Time Offset for user current Entity. */
			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
			// End: Code modified by Balaji for mantis 1991
			/* set time format. */
			formatter = new SimpleDateFormat("HH:mm:ss");
			/* Server Date parsed from given time string. */
			serverDate = getSysParamDateWithEntityOffset(entityId);
			/* get Calendar instance. */
			Calendar cal = Calendar.getInstance();
			/* set server date in calendar. */
			cal.setTime(serverDate);
//			/* check whether Entity Time Offset contains "+" or "-" */
			/* set time format for display. */
			formatter = new SimpleDateFormat("HH:mm");
			/* get Last Refresh time with HH:mm format. */
			lastRefresh = formatter.format(cal.getTime());
			/* add entityTimeOffset with Last Refresh time. */
			lastRefresh = lastRefresh + " (GMT " + entityTimeOffset + ")";
			/* Trim Last Refresh time. */
			lastRefresh = lastRefresh.trim();
			log.debug("SwtUtil - [getLastRefTimeOnGMTOffset] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getLastRefTimeOnGMTOffset] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			/* null the objects created already. */
			workFlowMgr = null;
			oraSystemTime = null;
			strLastRefreshTime = null;
			formatter = null;
			serverDate = null;
			st = null;
			entityTimeOffset = null;
			hourStr = null;
			minuteStr = null;
		}

		return lastRefresh;
	}

	/**
	 * This method used to Show 'Last refresh' time in screens that have refresh
	 * function
	 * 
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public static String getLastRefTimeOnGMTOffsetILM(HttpServletRequest request,
			String entityId) throws SwtException {
		/* Variable Declaration for WorkflowMonitorManager */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for OracleTimeDTO */
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for strLastRefreshTime */
		String strLastRefreshTime = null;
		/* Variable Declaration for SimpleDateFormat */
		SimpleDateFormat formatter = null;
		/* Variable Declaration for serverDate */
		Date serverDate = null;
		/* Variable Declaration for lastRefresh */
		String lastRefresh = null;
		/* Variable Declaration for StringTokenizer */
		StringTokenizer st = null;
		/* Variable Declaration for entityTimeOffset */
		String entityTimeOffset = null;
		/* Variable Declaration for hourStr */
		String hourStr = null;
		/* Variable Declaration for minute */
		String minuteStr = null;
		/* Variable Declaration for hour */
		int hour = 0;
		/* Variable Declaration for minute */
		int minute = 0;
		try {
			
			log.debug("SwtUtil - [getLastRefTimeOnGMTOffsetILM] - " + "Entry");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			/* get Oracle System time. */
			// Start: Code modified by Balaji for mantis 1991
			// Condition to check entity id is null or all
			if (SwtUtil.isEmptyOrNull(entityId)
					|| entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
				// set default entity Id
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
			}

			/* get Entity Time Offset for user current Entity. */
			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
			// End: Code modified by Balaji for mantis 1991
			/* set time format. */
			formatter = new SimpleDateFormat("HH:mm:ss");
			/* Server Date parsed from given time string. */
			serverDate = getSysParamDateWithEntityOffset(entityId);
			/* get Calendar instance. */
			Calendar cal = Calendar.getInstance();
			/* set server date in calendar. */
			cal.setTime(serverDate);
//			/* check whether Entity Time Offset contains "+" or "-" */
			/* set time format for display. */
			formatter = new SimpleDateFormat("HH:mm");
			/* get Last Refresh time with HH:mm format. */
			lastRefresh =  "[GMT " + entityTimeOffset + "]";
			/* Trim Last Refresh time. */
			lastRefresh = lastRefresh.trim();
			log.debug("SwtUtil - [getLastRefTimeOnGMTOffsetILM] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getLastRefTimeOnGMTOffsetILM] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			/* null the objects created already. */
			workFlowMgr = null;
			oraSystemTime = null;
			strLastRefreshTime = null;
			formatter = null;
			serverDate = null;
			st = null;
			entityTimeOffset = null;
			hourStr = null;
			minuteStr = null;
		}

		return lastRefresh;
	}
	/**
	 *
	 * This function returns the last refresh time based on the server time
	 * without adding the user default entity offset time
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public static String getLastRefreshTime(HttpServletRequest request)
			throws SwtException {
		/* Variable Declaration for WorkflowMonitorManager */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for OracleTimeDTO */
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for strLastRefreshTime */
		String strLastRefreshTime = null;
		/* Variable Declaration for SimpleDateFormat */
		SimpleDateFormat formatter = null;
		/* Variable Declaration for serverDate */
		Date serverDate = null;
		/* Variable Declaration for lastRefresh */
		String lastRefresh = null;
		/* Variable Declaration for StringTokenizer */
		StringTokenizer st = null;
		try {
			log.debug("SwtUtil - [getLastRefTime] - " + "Entry");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			/* get Oracle System time. */
			oraSystemTime = workFlowMgr.getOracleSystemTime();
			/* get Oracle System time with yyyy-MM-dd'T'HH:mm:ss format */
			strLastRefreshTime = oraSystemTime.getTimeISO8601();
			/* initialize the String Tokenizer. */
			st = new StringTokenizer(strLastRefreshTime, "T");
			/* get next token. */
			strLastRefreshTime = st.nextToken();
			strLastRefreshTime = st.nextToken();
			/* set time format. */
			formatter = new SimpleDateFormat("HH:mm");
			/* Server Date parsed from given time string. */
			serverDate = formatter.parse(strLastRefreshTime);
			/* get Last Refresh time with HH:mm format. */
			lastRefresh = formatter.format(serverDate);
			/* add entityTimeOffset with Last Refresh time. */
			lastRefresh = lastRefresh + " (Server time)";
			/* Trim Last Refresh time. */
			lastRefresh = lastRefresh.trim();
			log.debug("SwtUtil - [getLastRefTime] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getLastRefTime] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			/* null the objects created already. */
			workFlowMgr = null;
			oraSystemTime = null;
			strLastRefreshTime = null;
			formatter = null;
			serverDate = null;
			st = null;
		}

		return lastRefresh;
	}

	/**
	 *
	 * This function returns the last refresh time based on the server time
	 * without adding the user default entity offset time
	 *
	 * @param request
	 * @param entityId
	 * @return
	 * @throws SwtException
	 */
	public static String getLastRefreshServerTimeOnGMTOffset(HttpServletRequest request)
			throws SwtException {
		/* Variable Declaration for WorkflowMonitorManager */
		WorkflowMonitorManager workFlowMgr = null;
		/* Variable Declaration for OracleTimeDTO */
		OracleTimeDTO oraSystemTime = null;
		/* Variable Declaration for strLastRefreshTime */
		String strLastRefreshTime = null;
		/* Variable Declaration for SimpleDateFormat */
		SimpleDateFormat formatter = null;
		/* Variable Declaration for serverDate */
		Date serverDate = null;
		/* Variable Declaration for lastRefresh */
		String lastRefresh = null;
		/* Variable Declaration for StringTokenizer */
		StringTokenizer st = null;
		
		String serverTimeZoneOffset = null;
		try {
			log.debug("SwtUtil - [getLastRefreshServerTimeOnGMTOffset] - " + "Entry");
			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
					.getBean("workflowMonitorManager"));
			/* get Oracle System time. */
			oraSystemTime = workFlowMgr.getOracleSystemTime();
			/* get Oracle System time with yyyy-MM-dd'T'HH:mm:ss format */
			strLastRefreshTime = oraSystemTime.getTimeISO8601();
			/* initialize the String Tokenizer. */
			st = new StringTokenizer(strLastRefreshTime, "T");
			/* get next token. */
			strLastRefreshTime = st.nextToken();
			strLastRefreshTime = st.nextToken();
			/* set time format. */
			formatter = new SimpleDateFormat("HH:mm");
			/* Server Date parsed from given time string. */
			serverDate = formatter.parse(strLastRefreshTime);
			/* get Last Refresh time with HH:mm format. */
			lastRefresh = formatter.format(serverDate);
			serverTimeZoneOffset = getServerTimeZoneOffset(request);
			/* add entityTimeOffset with Last Refresh time. */
			lastRefresh = lastRefresh + " (Server time [GMT " + serverTimeZoneOffset + " ])";
			/* Trim Last Refresh time. */
			lastRefresh = lastRefresh.trim();
			log.debug("SwtUtil - [getLastRefreshServerTimeOnGMTOffset] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getLastRefreshServerTimeOnGMTOffset] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			/* null the objects created already. */
			workFlowMgr = null;
			oraSystemTime = null;
			strLastRefreshTime = null;
			formatter = null;
			serverDate = null;
			st = null;
		}

		return lastRefresh;
	}
	
	/**
	 * This function gets the ServerTimeZoneOffset
	 * 
	 * @param request
	 */
	public static String getServerTimeZoneOffset(HttpServletRequest request) throws SwtException {

		SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil
		.getBean("sysParamsManager");
		SysParams sysParams = sysParamsManager.getSysParamsDetail(getCurrentHostId());
		String timeZone = sysParams.getSysTimeZone();


		Date systemDate = sysParams.getTestDate();
		log.debug("systemDate - " + systemDate);
		if(systemDate == null)
			systemDate = new Date();

		HashMap<String, String> timeZoneRegionsList = sysParamsManager.getTimeZoneRegionsList(systemDate);
		Collection<LabelValueBean> timeZoneRegionsLblValue = null;


		timeZoneRegionsLblValue = new ArrayList<LabelValueBean>();

		for (Map.Entry<String, String> entry : timeZoneRegionsList.entrySet()) {
		String key = entry.getKey();
		String value = entry.getValue();
		if(timeZone.equals(key)){
		return value;
		}
		}

		return "";
		}
	/**
	 *
	 * This function to pass the Date and return the simple date format
	 *
	 * @param date
	 * @return String
	 * @throws SwtException
	 */
	public static String getDateAsString(Date date) throws SwtException {
		/* Method's local variable declaration */
		// Variable to hold dateAsString
		String dateAsString = null;
		// Variable to hold sdf
		SimpleDateFormat simpleDateFormat = null;
		// object to hold session
		HttpSession session = null;
		// object to hold common data manager
		CommonDataManager cdm = null;
		// object to hold system formats
		SystemFormats sys = null;
		// Variable to hold date format
		String dateFormat = null;
		try {
			log.debug("SwtUtil - [getDateAsString] - " + "Entry");
			// To set user session object
			session = UserThreadLocalHolder.getUserSession();
			cdm = (CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN);
			sys = cdm.getSystemFormats();
			// To get the date format for system format object
			dateFormat = sys.getDateFormatValue();
			// To replace the date format symbol
			dateFormat = dateFormat.replaceAll("/", "-");
			// To set the simple date format
			simpleDateFormat = new SimpleDateFormat(dateFormat + " HH:mm:ss");
			// To check the date value
			if (date != null)
				// To convert the date to simple date format
				dateAsString = simpleDateFormat.format(date);
			log.debug("SwtUtil - [getDateAsString] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getDateAsString] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// To set null value for local variable
			simpleDateFormat = null;
			session = null;
			cdm = null;
			sys = null;
			dateFormat = null;
		}

		return dateAsString;

	}

	/**
	 *
	 * This function converts the passed date based on the offset time set for
	 * the given entity
	 *
	 * @param date
	 * @param entityId
	 * @return HashMap
	 * @throws SwtException
	 */
	public static HashMap<String, Object> getOffsetDateTime(Date systemDate,
															String entityId) throws SwtException {
		/* Method's local variable declaration */
		// Object to hold system param manager
		SysParamsManager sysParamsManager = null;
		// Variable to hold entitydate and offset
		HashMap<String, Object> offsetMap = null;
		try {
			log.debug("SwtUtil - [getOffsetDateTime] - " + "Entry");
			// To get the object for system param manger
			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			// To get the entity date and offset for getEntityDate method
			offsetMap = sysParamsManager.getEntityDate(systemDate, entityId);
			log.debug("SwtUtil - [getOffsetDateTime] - " + "Exit");
			return offsetMap;
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getOffsetDateTime] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// To set null value for local variable
			sysParamsManager = null;
		}

	}

	/**
	 * <pre>
	 * 	This method invokes RPC service to InputEngine and gets interface heartbeat
	 * 	details and returns the same
	 * 	The output will be an object array inside an object array.
	 * 	The inner object array contains details of an interface
	 * 	- Index 0: Interface Id
	 * 	- Index 1: Message Status(Active Flag)
	 * 	- Index 2: Last Message Date
	 * </pre>
	 * @param returnXML - if true, return the interface heart beat details in XML format, else, return the interface heartbeat details
	 * @return Object[] - Heart beat detail
	 * @throws Exception
	 */
	public static synchronized Object[] getEngineHeartBeat(boolean returnXML, boolean ...fromPCM) throws Exception {
		// Result from input engine (XML format)
		String result = null;
		// URL for Interface RPC service
		String url = null;
		// To parse xml
		InputStream source = null;
		InterfaceProcessor processor = null;
		// Interface heartbeat details
		List<Object[]> lstInterfaceHeartBeat = new ArrayList<Object[]>();
		// Error message
		String output = null;
		// Added for mantis 1928 by KaisBS
		// will contain the XML format in the first case
		Object[] xMLFormat = new Object[1];
		try {
			output = "";
			// Get URL for Interface RPC service
			if (fromPCM != null && fromPCM.length > 0 && fromPCM[0]) {

				url = ZkUtils.getSIProperty(ZkUtils.PROPERTY_XMLRPC_URL, true);
				if(SwtUtil.isEmptyOrNull(url)) {
					url = PropertiesFileLoader.getInstance().getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_RPC_SERVICE_PCM);
				}
			} else {
				url = ZkUtils.getSIProperty(ZkUtils.PROPERTY_XMLRPC_URL, false);
				if(SwtUtil.isEmptyOrNull(url)) {
					url = PropertiesFileLoader.getInstance().getPropertiesValue(
							SwtConstants.PROPERTY_INTERFACES_RPC_SERVICE);
				}

			}
			// Validate URL
			if (url == null) {
				// URL not found in configuration file, so throw an exception
				throw new Exception(
						"The URL property was not found in the config.properties file.");
			}

			// Invoke RPC and get interface heartbeat details in XML format
			result = EngineCall.usage(url);
			// Mantis 1748: Do not parse non XML messages, Saber Chebka on 13-03-2012
			if(!result.startsWith("<"))
				return lstInterfaceHeartBeat.toArray();

			// Added for mantis 1928 by KaisBS: retrieve the XML format in the first case
			if (returnXML){
				xMLFormat[0] = result;
				lstInterfaceHeartBeat.add(xMLFormat);
			}else{
				// Initialize processor to parse XML
				processor = new InterfaceProcessor();
				// Parse the XML and gets interface heartbeat details
				source = new ByteArrayInputStream(result.getBytes());
				lstInterfaceHeartBeat = processor.process(source);
			}

		} finally {
			// nullify objects
			result = null;
			url = null;
			source = null;
			processor = null;
		}
		// System.out.println (output);
		return lstInterfaceHeartBeat.toArray();
	}

	/**
	 * Added by Mefteh for Mantis 2663 Unmarshal the XML content into java
	 * Heartbeat object
	 *
	 * @param
	 * @return HeartBeat
	 */
	@SuppressWarnings("unused")
	public synchronized static Heartbeat getHeartBeat(boolean fromPCM) throws SwtException {
		Heartbeat result = null;
		Object[] engineResponse = null;
		Object[] engineResult ;
		try{
			// send the XML sent by getHeartBeat() method in JWS
			engineResponse  = getEngineHeartBeat(true, fromPCM);
			if (engineResponse == null || engineResponse.length == 0 ) {
				// Create a dummy exception
				SwtException engineResponseError = new SwtException("Error when attempting to contact the SmartInput Engine");
				engineResponseError.setErrorId("0");
				engineResponseError.setErrorDesc("RPC: Error when attempting to contact the SmartInput Engine");
				engineResponseError.setErrorLogFlag("Y");
				engineResponseError.setSrcCodeLocation("SwtUtil.getHeartBeat");

				// log the exception in datbase
				logErrorInDatabase(engineResponseError);

				// return null heartbeat that will not be treated from interface notification class
				return null;
			}
			engineResult = (Object[]) engineResponse[0];
			// Instantiate the unmarshaller once
			if (unmarshaller == null) {
				JAXBContext jaxbContext = JAXBContext
						.newInstance("org.swallow.control.dao.soap");
				unmarshaller = jaxbContext.createUnmarshaller();
			}

			// unmarshal the XML content into java Heartbeat object
			JAXBElement<?> heartbeat = (JAXBElement<?>) unmarshaller
					.unmarshal(new ByteArrayInputStream(engineResult[0].toString()
							.getBytes()));
			result = (Heartbeat) heartbeat.getValue();
		}catch(Exception e){
			logErrorInDatabase(SwtErrorHandler.getInstance().handleException(e,	"getHeartBeat", SwtUtil.class));
			throw SwtErrorHandler.getInstance().handleException(e,
					"getHeartBeat", SwtUtil.class);
		}
		return result;
	}

	/**
	 * This method is used to get the monitor job flag. If the job is running in
	 * the background, the flag will found to be false and an error message
	 * "Data Build in Progress" will be displayed to the user.If the flag is
	 * found to be true, data will be fetched and shown to the user.
	 *
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public static boolean getMonitorJobFlag(String entityId, String jobProcess)
			throws SwtException {

		// Declare EntityProcessDAO object
		EntityProcessDAO entityProcessDAO = null;
		// Variable to hold the job status flag
		boolean jobFlag = false;
		try {
			log.debug("SwtUtil - [getMonitorJobFlag] - " + "Entry");
			// get the entityProcessDAO bean from swtUtil
			entityProcessDAO = (EntityProcessDAO) SwtUtil
					.getBean("entityProcessDAO");
			// get the monitor job flag
			jobFlag = entityProcessDAO.getMonitorJobFlag(entityId, jobProcess);
			log.debug("SwtUtil - [getMonitorJobFlag] - " + "Exit");
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getMonitorJobFlag] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// To nullify the object
			entityProcessDAO = null;
		}
		return jobFlag;
	}


	public static Map<String, String> getCurrencyFullAccessMap(String roleId,
															   String hostId, String entityId) throws SwtException {
		return getCurrencyFullAccessMap(roleId, hostId, entityId, true);
	}

	public static Map<String, String> getCurrencyFullOrViewAccessMap(String roleId,
																	 String hostId, String entityId) throws SwtException {
		return getCurrencyFullAccessMap(roleId, hostId, entityId, false);
	}

	/**
	 * This method is used to get the map for currency with full access . First
	 * it will get the whole currency group list with access and it will take
	 * currency list map which currency group having full access
	 *
	 * @param roleId
	 * @param hostId
	 * @param entityId
	 * @return Map
	 * @throws SwtException
	 */
	public static Map<String, String> getCurrencyFullAccessMap(String roleId,
															   String hostId, String entityId, boolean fullOnly) throws SwtException {
		boolean checkAccess = false;
		// To hold the currency full access map
		Map<String, String> currencyFullAccessMap = null;
		// Currency group access collection
		Collection<EntityCurrencyGroupAccess> currencyGrpAccessColl = null;
		// Iterate the currency group
		Iterator<EntityCurrencyGroupAccess> itrCurrencyGrp = null;
		// collection currency
		Collection<CurrencyTO> currencyColl = null;
		// iterate the currency
		Iterator<CurrencyTO> itrCurrency = null;
		try {
			log.debug("SwtUtil  - [getCurrencyFullAccessMap] - Entering");
			// Instantiate the currency full access
			currencyFullAccessMap = new TreeMap<String, String>();
			// get the collection of currency group collection
			currencyGrpAccessColl = getSwtMaintenanceCache()
					.getCurrencyGroupAcess(roleId, entityId);
			// iterate the currency group access collection
			itrCurrencyGrp = currencyGrpAccessColl.iterator();
			while (itrCurrencyGrp.hasNext()) {
				// get the EntityCurrencyGroupAccess instance
				EntityCurrencyGroupAccess enGroupAccess = (EntityCurrencyGroupAccess) itrCurrencyGrp
						.next();
				// check the currency group access with full Or full/view access
				if (fullOnly == true) {
					checkAccess = enGroupAccess.getAccess() == SwtConstants.CURRENCYGRP_FULL_ACCESS;
				}else {
					checkAccess = (enGroupAccess.getAccess() == SwtConstants.CURRENCYGRP_FULL_ACCESS) || (enGroupAccess.getAccess() == SwtConstants.CURRENCYGRP_READ_ACCESS);
				}
				if (checkAccess) {
					// get the currency list for currency group
					currencyColl = getSwtMaintenanceCache().getCurrencies(
							entityId, enGroupAccess.getCurrencyGroupId());
					// iterate the currency collection
					itrCurrency = currencyColl.iterator();
					while (itrCurrency.hasNext()) {
						// get the currencyTo object
						CurrencyTO currencyTO = (CurrencyTO) itrCurrency.next();
						// get the currency id from currencyTO
						String currencyId = currencyTO.getCurrencyId();
						if (!currencyId.equals("*")) {
							// add labelvaluebean for currency
							currencyFullAccessMap.put(currencyId, currencyTO
									.getCurrencyName());
						}
					}
				}
			}
			log.debug("SwtUtil  - [getCurrencyFullAccessMap] - Existing");
		} catch (Exception e) {
			log.error("SwtUtil  - [getCurrencyFullAccessMap] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyGrpAccessColl = null;
			itrCurrencyGrp = null;
			currencyColl = null;
			itrCurrency = null;
		}
		return currencyFullAccessMap;
	}

	// Start : Method added by Balaji for Mantis 1991
	/**
	 * Method to retrieve the system with entity offset
	 *
	 * @param entityId
	 * @return Date
	 * @throws SwtException
	 */
	public static Date getSysParamDateWithEntityOffset(String entityId)
			throws SwtException {
		// local variable declaration
		Calendar cal = Calendar.getInstance();
		Date systeDateValue = null;
		SysParamsManager sysParamsManager = null;
		Calendar currentCal = null;
		try {
			log.debug("SwtUtil  - [getSysParamDateWithEntityOffset] - Entry");
			// get manager bean
			sysParamsManager = (SysParamsManager) getBean("sysParamsManager");
			// get date based on entity offset
			systeDateValue = sysParamsManager
					.getDBSytemDateWithEntityOffset(entityId);
			// Condition to check date is null
			if (systeDateValue == null)
				// get db system date if offset date is null
				systeDateValue = getDBSysDatewithTime(systeDateValue);

//			// set date in calender
			cal.setTime(systeDateValue);
		} catch (SwtException swt) {
			log
					.error("SwtUtil  - [getSysParamDateWithEntityOffset] - Exception -"
							+ swt.getMessage());
			throw swt;
		} catch (Exception e) {
			log
					.error("SwtUtil  - [getSysParamDateWithEntityOffset] - Exception -"
							+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			systeDateValue = null;
			sysParamsManager = null;
			currentCal = null;
			log.debug("SwtUtil  - [getSysParamDateWithEntityOffset] - Exit");
		}
		return cal.getTime();
	}

	// Start:Code Added By ASBalaji for mantis 2032 on 15-08-2012

	/**
	 * Method is used to avoid null(as a string) being added while to
	 * concatenate two strings in which either one is null. If any of the string
	 * is null, then the null value is replaced with empty string("")
	 *
	 * @param strOne
	 * @param strTwo
	 * @return
	 */
	public static String ConcatNvl(String strOne, String strTwo) {
		return (strOne == null ? "" : strOne) + (strTwo == null ? "" : strTwo);
	}
	// End:Code Added By ASBalaji for mantis 2032 on 15-08-2012

	/**
	 * Added by Mefteh for Mantis 2016
	 * To retrieve N_DAYS_PRIOR_TO_TODAY , N_DAYS_AHEAD_TO_TODAY defined in PKG_PREDICT_JOBS
	 * @param none
	 * @return List
	 * @throws SwtException
	 */
	public static List<Integer> getPriorAndAheadDaysToToday() throws SwtException {
		// list of prior and ahead days
		List<Integer> listPriorAndAheadDays= null;
		// SysParamsManager instance
		SysParamsManager sysParamsManager = null;
		try {
			// get the SysParamsManager instance
			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			// list of prior and ahead days from DB
			listPriorAndAheadDays = sysParamsManager.getPriorAndAheadDaysToToday();
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [getPriorAndAheadDaysToToday] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		} finally {
			// nullify objects
			sysParamsManager = null;
		}
		return listPriorAndAheadDays;
	}

	/**
	 * Added by Saber for Mantis 2077
	 * Decrypt based on AES.
	 * FOR FURTHER DOCUMENTATIONS, PLEASE KEEP OUT OF RADAR
	 */
	public static String decryptAES(String salt,
									String encryptedHex) throws Exception {
		byte[] keyMaterial = Hex.decodeHex(salt.toCharArray());
		SecretKeySpec key = new SecretKeySpec(keyMaterial, "AES");
		// decrypting
		Cipher cipher = Cipher.getInstance("AES");
		cipher.init(Cipher.DECRYPT_MODE, key);
		byte[] original = cipher.doFinal(Hex.decodeHex(encryptedHex.toCharArray()));
		String originalString = new String(original,"UTF-8");
		return originalString;
	}


	/**
	 * This method is used to decrypt encrypted text.
	 * added by chiheb for angular.
	 * @param encriptedText
	 * @return
	 */
	public static String decryptCBC(String saltKey, String encriptedText)  throws Exception {
		try {
			if(saltKey.length()<16)
				saltKey = RPad(saltKey, 16, '-');
			IvParameterSpec iv = new IvParameterSpec(saltKey.getBytes("UTF-8"));
			SecretKeySpec skeySpec = new SecretKeySpec(saltKey.getBytes("UTF-8"), "AES");

			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

			byte[] original = cipher.doFinal(Base64.decodeBase64(encriptedText));

			return new String(original);
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return null;
	}

	public static String encrypt(String key, String initVector, String value) {
		try {
			IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
			SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
			cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

			byte[] encrypted = cipher.doFinal(value.getBytes());

			return Base64.encodeBase64String(encrypted);
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return null;
	}

	public static String decrypt(String key, String initVector, String encrypted) {
		try {
			IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
			SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
			cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

			byte[] original = cipher.doFinal(Base64.decodeBase64(encrypted));

			return new String(original);
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return null;
	}

	/**
	 * Added by Saber for Mantis 2077
	 * Generate an MD5 hash code and return result as Hex string
	 * @param s
	 * @return
	 */
	public static String hash(String s) throws NoSuchAlgorithmException{
		MessageDigest md = MessageDigest.getInstance("MD5");
		md.update(s.getBytes());
		byte byteData[] = md.digest();
		return new String(Hex.encodeHex(byteData, true));
	}


	/**
	 * This function converts a java bean into url params
	 * @param bean
	 * @param attrName
	 * @return
	 * @throws Exception
	 *
	 * <AUTHOR> Chebka, STTun
	 */
	public static Object convertBeanToUrlParams(Object bean, String mapAttributeName) throws Exception{
		StringBuffer urlParams = new StringBuffer();
		Map<String, Object> beanMap = convertObjectToMap(bean);
		HashMap<String, Object> map = (HashMap<String, Object>)beanMap.get(mapAttributeName);
		for (Map.Entry<String, Object> entry : map.entrySet()) {
			urlParams.append(entry.getKey()+"="+entry.getValue()+"&");
		}
		return urlParams;
	}
	/**
	 * Converts an object into a Map based on java reflection
	 * BeanUtils.getProperty(bean, name) is also using reflection, but converts it back to a string value.
	 * Better is to use convertObjectToMap(obj) to get generic result
	 *
	 * @param obj
	 * @return
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 *
	 * <AUTHOR> Chebka, STTun
	 */
	private static Map<String, Object> convertObjectToMap(Object obj) throws  IllegalAccessException,  IllegalArgumentException, InvocationTargetException {
		Method[] methods = obj.getClass().getMethods();
		Field[] fields = obj.getClass().getDeclaredFields();
		Map<String, Object> map = new HashMap<String, Object>();
		for (Method m : methods) {
			if (m.getName().startsWith("get") && !m.getName().startsWith("getClass")) {
				Object value = (Object) m.invoke(obj);
				String attribute = (""+m.getName().charAt(3)).toLowerCase()+m.getName().substring(4);
				for (Field f : fields) {
					if(f.getName().toUpperCase().equals(attribute.toUpperCase()))
						map.put(f.getName(), (Object) value);
				}

			}
		}
		return map;
	}

	/**
	 * Added By Mefteh Bouazizi for Mantis 2081
	 * return number of records shown per page
	 * @param  KeyName
	 * @return
	 * @throws SwtException
	 */
	public static int getPageSizeFromProperty(String KeyName) throws SwtException {
		if(PropertiesFileLoader.getInstance().getPropertiesValue(KeyName)!=null)
			return Integer.parseInt(PropertiesFileLoader.getInstance()
					.getPropertiesValue(KeyName));
		else
			return Integer.parseInt(PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.DEFAULT_SYSTEM_SCREEN_PAGE_SIZE));

	}

	/**
	 * Encode in base 64 with custom changes (replacing '=' and '+' chanacters with '(' and ')')
	 * @param text
	 * @return
	 */
	public static String encode64(String text) {
		String result = Base64.encodeBase64String(text.getBytes(StandardCharsets.UTF_8));
		result = result.replace("=", "(").replace("+", ")");
		return result;
	}

	/**
	 * Decode in base 64 with custom changes (replacing '(' and ')' chanacters with '=' and '+')
	 * @param text
	 * @return
	 */
	public static String decode64(String text){
		String result = text.replace("(", "=").replace(")", "+");
		result = new String(Base64.decodeBase64(result), StandardCharsets.UTF_8);
		return result;
	}
	/**
	 * Get current date time in the format "yyyyMMdd_HHmmss";
	 *
	 * @return
	 */

	public static String FormatCurrentDate()
	{
		DateFormat dateFormat = new SimpleDateFormat(SwtConstants.SwtFormatDate);
		//get current date time with Date()
		Date date = new Date();
		return  dateFormat.format(date);

	}

	public static String formatPrecisionDecimal(String decimal){

		if (decimal.indexOf(".") == 0)
			decimal = "0" + decimal;

		else if (decimal.indexOf("-.") == 0)
			decimal=decimal.replace("-", "-0");

		return decimal;
	}

	public static String decreaseStringWidth(int threasoldWidth, String text,
											 String fontName, int fontSize) {

		AffineTransform affinetransform;
		FontRenderContext frc;
		Font font;

		try {
			affinetransform = new AffineTransform();
			frc = new FontRenderContext(affinetransform, true,
					true);
			font = new Font(fontName, Font.PLAIN, fontSize);
			if(isEmptyOrNull(text))
				return "";
			else
				text = text.replaceAll("\\n|\\r", " ");
			int textwidth = (int) (font.getStringBounds(text, frc).getWidth());

			while (textwidth > threasoldWidth) {
				text = text.substring(0, (text.length() - 2));
				textwidth = (int) (font.getStringBounds(text, frc).getWidth());
			}
		} catch (Exception e) {
			affinetransform = new AffineTransform();
			frc = new FontRenderContext(affinetransform, true,
					true);
			font = new Font(fontName, Font.PLAIN, fontSize);
			HashMap<Character, Integer> alphaNumMap = new LinkedHashMap<Character, Integer>();
			for (int i = 0; i < alphaNumValues.length; i++) {
				char key = alphaNumkeys[i];
				int value = alphaNumValues[i];
				alphaNumMap.put(key, value);
			}
			int totalWidth = 0;
			String truncatedText = "";
			for (int i = 0; i < text.length(); i++) {
				char chr = text.charAt(i);
				if (totalWidth > threasoldWidth) {
					break;
				}
				truncatedText += chr;
				totalWidth += alphaNumMap.get(chr);
			}
			if (totalWidth > threasoldWidth) {
				truncatedText = truncatedText.substring(0, truncatedText.length() - 1);
			}
			text = truncatedText;
		}

		return text;
	}

	/**
	 * Replace unparsable XML chars with their respective encoded chars
	 * @param tmp
	 * @return
	 */
	public static String convertXMLChars(String tmp) {
		if (!SwtUtil.isEmptyOrNull(tmp)) {
			return tmp.replaceAll("&", "&amp;").replaceAll(">", "&gt;")
					.replaceAll("<", "&lt;").replaceAll("\"", "&quot;")
					.replaceAll("'", "&apos;");
		} else {
			return "";
		}
	}


	public static boolean getMaintainAnyGroupILMAccess(HttpServletRequest request) throws SwtException{

		String roleId;
		String maintainAnyGroupILM;
		try{
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();


			RoleManager roleManager = (RoleManager) SwtUtil
					.getBean("roleManager");

			maintainAnyGroupILM = roleManager.getMaintainAnyILMGroup(roleId);

			return maintainAnyGroupILM.equals("Y")?true:false;
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyGroupILMAccess] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyGroupILMAccess] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMaintainAnyGroupILMAccess", SwtUtil.class);
		} finally {
			// nullify objects
			roleId = null;
			maintainAnyGroupILM =null;
			// log debug message
			log.debug("SwtUtil - [getMaintainAnyGroupILMAccess] - Exit");
		}
	}

	public static boolean getMaintainAnyScenarioILMAccess(HttpServletRequest request)throws SwtException{

		String roleId;
		String maintainAnyGroupILM;
		try{
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();


			RoleManager roleManager = (RoleManager) SwtUtil
					.getBean("roleManager");

			maintainAnyGroupILM = roleManager.getMaintainAnyILMScenario(roleId);

			return maintainAnyGroupILM.equals("Y")?true:false;
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyScenarioILMAccess] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyScenarioILMAccess] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMaintainAnyScenarioILMAccess", SwtUtil.class);
		} finally {
			// nullify objects
			roleId = null;
			maintainAnyGroupILM = null;
			// log debug message
			log.debug("SwtUtil - [getMaintainAnyScenarioILMAccess] - Exit");
		}
	}
	public static boolean getMaintainAnyReportHistAccess(HttpServletRequest request)throws SwtException{

		String roleId;
		String maintainAnyReportHist;
		try{
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();


			RoleManager roleManager = (RoleManager) SwtUtil
					.getBean("roleManager");

			maintainAnyReportHist = roleManager.getMaintainAnyReportHist(roleId);

			return maintainAnyReportHist.equals("Y")?true:false;
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyReportHistAccess] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyReportHistAccess] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMaintainAnyReportHistAccess", SwtUtil.class);
		} finally {
			// nullify objects
			roleId = null;
			maintainAnyReportHist = null;
			// log debug message
			log.debug("SwtUtil - [getMaintainAnyReportHistAccess] - Exit");
		}
	}

	public static boolean getMaintainAnyPCFeature(String userId)throws SwtException{

		String roleId;
		String maintainAnyPCFeature;
		UserMaintenance usermaintenance;
		try{

			UserMaintenanceManager usermaintenanceManager = (UserMaintenanceManager) (SwtUtil
					.getBean("usermaintenanceManager"));

			usermaintenance = usermaintenanceManager.fetchUserDetail(hostId,
					userId);

			roleId = usermaintenance.getRoleId();

			RoleManager roleManager = (RoleManager) SwtUtil
					.getBean("roleManager");

			maintainAnyPCFeature = roleManager.getMaintainAnyPCFeature(roleId);

			return maintainAnyPCFeature.equals("Y")?true:false;
		} catch (SwtException ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyReportHistAccess] - SwtException: "
					+ ex.getMessage());
			// Re-throw SwtException
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error("SwtUtil - [getMaintainAnyReportHistAccess] - Exception: "
					+ ex.getMessage());
			// Re-throw as SwtException
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMaintainAnyReportHistAccess", SwtUtil.class);
		} finally {
			// nullify objects
			roleId = null;
			maintainAnyPCFeature = null;
			// log debug message
			log.debug("SwtUtil - [getMaintainAnyReportHistAccess] - Exit");
		}
	}

	/**
	 *  Used to convert String to XML
	 * @param xmlStr
	 * @return
	 */
	public static Document convertStringToDocument(String xmlStr) throws SwtException{
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		DocumentBuilder builder;
		try {
			builder = factory.newDocumentBuilder();
			Document doc = builder.parse(new InputSource(new StringReader(
					xmlStr)));
			return doc;
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [convertStringToDocument] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		}
	}

	/**
	 * Used to serialise object to xml string
	 * @param object
	 * @return
	 * @throws Exception
	 */
	public static String serializeToXML(Object object)throws Exception{
		XStream xStream = null;
		try {

			xStream = new XStream(new DomDriver(SwtConstants.ISO));
			xStream.addPermission(AnyTypePermission.ANY);
			xStream.addPermission(NullPermission.NULL);
			xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [serializeToXML] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		}
		return 	xStream.toXML(object);

	}
	/**
	 *  Used to deserialise a xml string to object
	 * @param xmlStr
	 * @return
	 * @throws Exception
	 */
	public static Object deserializeFromXML(String xmlStr) throws Exception{
		XStream xStream = null;
		try {
			if (xmlStr==null)
				return xmlStr;
			// When nor null
			xStream = new XStream(new DomDriver(SwtConstants.ISO));
			xStream.addPermission(AnyTypePermission.ANY);
			xStream.addPermission(NullPermission.NULL);
			xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		} catch (Exception ex) {
			log.error("Exception Catched in SwtUtil - [deserializeFromXML] "
					+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		}

		return 	xStream.fromXML(xmlStr);
	}

	/**
	 * Convert a document (XML) to String
	 * @param doc
	 * @return String
	 * @throws IOException
	 * @throws TransformerException
	 */
	public static String convertDocumentToString(Document doc) throws IOException, TransformerException {
		StringWriter sw = new StringWriter();
		TransformerFactory tf = TransformerFactory.newInstance();
		Transformer transformer = tf.newTransformer();
		transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
		transformer.setOutputProperty(OutputKeys.METHOD, "xml");
		transformer.setOutputProperty(OutputKeys.INDENT, "yes");
		transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
		transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
		transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");

		transformer.transform(new DOMSource(doc), new StreamResult(sw));

		return sw.toString();
	}

	/**
	 * Adds Days; hours, minutes and seconds to a provided date time
	 * @param orginal
	 * @param toAdd
	 * @return
	 */
	public static Date dateAdd(Date orginal, int...toAdd){
		if(toAdd.length==1){
			// Add days
			Calendar cal = Calendar.getInstance();
			cal.setTime(orginal);
			cal.add(Calendar.DATE, toAdd[0]);
			return cal.getTime();
		}else if(toAdd.length==2){
			// Add days and hours
			Calendar cal = Calendar.getInstance();
			cal.setTime(orginal);
			cal.add(Calendar.DATE, toAdd[0]);
			cal.add(Calendar.HOUR, toAdd[1]);
			return cal.getTime();
		}else if(toAdd.length==3){
			// Add days, hoursd and minutes
			Calendar cal = Calendar.getInstance();
			cal.setTime(orginal);
			cal.add(Calendar.DATE, toAdd[0]);
			cal.add(Calendar.HOUR, toAdd[1]);
			cal.add(Calendar.MINUTE, toAdd[2]);
			return cal.getTime();
		}else if(toAdd.length==4){
			// Add days, hours, minutes and seconds
			Calendar cal = Calendar.getInstance();
			cal.setTime(orginal);
			cal.add(Calendar.DATE, toAdd[0]);
			cal.add(Calendar.HOUR, toAdd[1]);
			cal.add(Calendar.MINUTE, toAdd[2]);
			cal.add(Calendar.SECOND, toAdd[3]);
			return cal.getTime();
		}
		return orginal;
	}
	/**
	 * Set user preferences in request
	 * @param request
	 * @return
	 */
	public static void setUserPreferences(HttpServletRequest request){
		SysParamsManager sysParamsManager = null;
		SysParams sysParams = null;
		User userdb = null;
		Role userRole = null;
		// Declares the MovementLockManager object
		MovementLockManager mlmgr = null;
		// Declares the user preference list
		List userPreferenceList = null;
		String roleId = null;
		Role role = new Role();
		Collection collRole = null;
		LogonManager logon = (LogonManager) (SwtUtil.getBean("logonManager"));
		CommonDataManager cdm = (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN);
		try {
			int alertInterval = Integer.parseInt(PropertiesFileLoader
					.getInstance().getPropertiesValue(
							SwtConstants.ALERT_INTERVAL));
			request.setAttribute("alertInterval", new Integer(alertInterval));

			sysParamsManager = (SysParamsManager) SwtUtil
					.getBean("sysParamsManager");
			sysParams = sysParamsManager.getSysParamsDetail(SwtUtil
					.getCurrentHostId());
			String sysDate = "";
			if (sysParams.getTestDate() != null) {
				sysDate = sysParams.getTestDate().toString();
			}
			request.setAttribute("sytemTestDate", sysDate);
			userdb = (User) cdm.getUser();

			userPreferenceList = logon.getUserPreference(userdb.getId()
					.getHostId(), SwtUtil.getUserCurrentEntity(request
					.getSession()), userdb.getId().getUserId());
			request.getSession().setAttribute(userdb.getId().getUserId(),
					userPreferenceList);
			/*
			 * Description : removing the Entries of p_movement_lock table of
			 * the user at the time of login
			 */

			/*
			 * Creating a MovementLockManager Instance to invoke a
			 * deleteMovementLock ()
			 */
			mlmgr = (MovementLockManager) SwtUtil
					.getBean("movementLockManager");

			/* Invoking deleteMovementLock() by passing the User ID */
			mlmgr.deleteMovementLock(userdb.getId().getUserId());
			roleId = userdb.getRoleId();
			RoleManager roleManager = (RoleManager) SwtUtil
					.getBean("roleManager");
			role = new Role();
			collRole = roleManager.getRoleDetails(role);
			Iterator<Role> itr = collRole.iterator();
			//Mantis 6891
			while (itr.hasNext()) {
				userRole = (Role) (itr.next());
				if (roleId.equals(userRole.getRoleId())) {
					String flag= ("Y".equalsIgnoreCase(userRole.getInputInterruption()) && ( "0".equalsIgnoreCase(userRole.getInputInterruptionAlertType()) ||
							"2".equalsIgnoreCase(userRole.getInputInterruptionAlertType())))?SwtConstants.YES:SwtConstants.NO;
					request.setAttribute("notifyUserFlag", flag);
					break;
				} else {
					request.setAttribute("notifyUserFlag", SwtConstants.NO);
				}
			}
		}catch (Exception e){
			// log error message
			log.error("SwtUtil - [setUserPreferences] - Exception: "
					+ e.getMessage(), e);
		}
	}

	private static String dbMonitorEnabled = null;
	public static boolean isDBMonitorEnabled(){
		// Property dbmonitor.enabled should be set to Y in predict.properties to enable starting database monitoring
		if(dbMonitorEnabled == null){
			dbMonitorEnabled = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.DBMONITOR_ENABLED);
			if(isEmptyOrNull(dbMonitorEnabled)){
				dbMonitorEnabled = "FALSE";
			}

			// Change logging level for jasper
			if(dbMonitorEnabled.equalsIgnoreCase("TRUE")){
				Handler logHandler = null;
				// Find the handler being used by common logging framework
				Enumeration<String> loggerNames1 = java.util.logging.LogManager.getLogManager().getLoggerNames();
				while(loggerNames1.hasMoreElements()){
					String loggerName = loggerNames1.nextElement();
					java.util.logging.Logger logger = java.util.logging.LogManager.getLogManager().getLogger(loggerName);
					if(logger == null){
						continue;
					}
					java.util.logging.Handler[] commonloggingHandlers = logger.getHandlers();
					for (int i = 0; i < commonloggingHandlers.length; i++) {
						// Should be a ConsoleHandler within jetty
						logHandler = commonloggingHandlers[i];
					}
				}

				// Jasper reports query executor: Level as ALL
				String jasperLoggerName = net.sf.jasperreports.engine.query.JRJdbcQueryExecuter.class.getName();
				LogFactory.getLog(jasperLoggerName);
				java.util.logging.Logger jasperLogger = java.util.logging.LogManager.getLogManager().getLogger(jasperLoggerName);
				if (jasperLogger != null) {
					jasperLogger.setLevel(java.util.logging.Level.ALL);
				}

				// Set lever for handler as ALL
				logHandler.setLevel(java.util.logging.Level.ALL);
			}
		}
		return dbMonitorEnabled.equalsIgnoreCase("TRUE");
	}

	/**
	 * Debugging method that prints the full call trace
	 * It generates an error, then prints the stack trace
	 */
	public static synchronized String getCallTrace(){
		StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();
		int lineNbr = 0;
		boolean firstLine = true;
		String result = "[CALL_TRACE]:\n";
		for (StackTraceElement ste : traceElements) {
			if(!ste.getMethodName().equals("printCallTrace")
					&& !(ste.getClassName()+"."+ste.getMethodName()).equals("java.lang.Thread.getStackTrace")
					&& (ste.toString().startsWith("org.swallow"))
					&& (!ste.toString().contains(XSSFilter.class.getName()))
					&& (!ste.toString().contains(SessionFilter.class.getName()))
					&& (!ste.toString().contains(ResponseHeaderFilter.class.getName()))
					&& (!ste.toString().contains(RequestFilter.class.getName()))
			)
			{
				// Always skip the first line as displays the printCallTrace() caller
				if(firstLine){
					firstLine = false;
					continue;
				}

				// Print the stack trace element
				result+=/*"\t> " +*/" " + ste + "\n";

				// Print only 10 lines
				lineNbr++;
				if(lineNbr>=10)
					break;
			}
		}
		return result;
	}

	/**
	 * Debugging method that prints the full call trace
	 * It generates an error, then prints the stack trace
	 */
	public static synchronized String getCallingMethod(){
		StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();
		int lineNbr = 0;
		String result = "";
		for (StackTraceElement ste : traceElements) {
			if(!ste.getMethodName().equals("printCallTrace")
					&& !(ste.getClassName()+"."+ste.getMethodName()).equals("java.lang.Thread.getStackTrace")
					&& (ste.toString().startsWith("org.swallow"))
					&& (!ste.toString().contains(XSSFilter.class.getName()))
					&& (!ste.toString().contains(SessionFilter.class.getName()))
					&& (!ste.toString().contains(ResponseHeaderFilter.class.getName()))
					&& (!ste.toString().contains(RequestFilter.class.getName()))
					&& (!ste.toString().contains(ConnectionManager.class.getName()))
			)
			{
				// Always skip the first line as displays the printCallTrace() caller
				if(lineNbr < 2){
					lineNbr++;
					continue;
				}

				// Print the stack trace element
				result+=/*"\t> " +*/" " + ste + "\n";

				// Print only 10 lines
				lineNbr++;
				if(lineNbr>=2)
					break;
			}
		}
		if(result.length()>0) {
			result = result.substring(0, result.indexOf("("));
		}
		return result;
	}

	/**
	 * Returns SQL query from XML file
	 * @param moduleId
	 * @param queryId
	 * @return
	 * @throws SwtException
	 */
	public static synchronized String getNamedQuery(String queryId) throws Exception{
		try {
			if(queryXPath == null)
			{
				queryXPath =  XPathFactory.newInstance().newXPath();
				DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
				DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
				queryDocument = dBuilder.parse(ILMReporting.class.getResourceAsStream("/org/swallow/util/queries.xml"));
			}

			// Return the result as string
			String expression = "/queries/query[@id='"+queryId+"']/text()";
			for (int i = 0; i < 5; i++) {
				try{
					return (String) queryXPath.compile(expression).evaluate(queryDocument, XPathConstants.STRING);
				}catch(Exception e){
					queryXPath =  XPathFactory.newInstance().newXPath();
					DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
					DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
					queryDocument = dBuilder.parse(ILMReporting.class.getResourceAsStream("/org/swallow/util/queries.xml"));
					Thread.sleep(1000);
					if( i>=4) {
						throw new Exception("Error extracting query from config file queries.xml: "+e.getMessage(), e);
					}
				}

			}
			return null;
		} catch (Exception e) {
			throw new Exception("Error extracting query from config file queries.xml: "+e.getMessage(), e);
		}
	}

	public static LinkedList<HashMap<String, Object>> executeNamedSelectQuery(String squeryName, Object...parameters) throws Exception{
		String squery  = SwtUtil.getNamedQuery(squeryName);
		long startTime = System.currentTimeMillis();
		LinkedList<HashMap<String, Object>> result = new LinkedList<HashMap<String, Object>>();

		result =  executeSelectQuery(squery, parameters);
		if(SwtUtil.isDBMonitorEnabled()){
			log.fatal("Executed NamedSelectQuery='"+squeryName+"', Took="+(System.currentTimeMillis()  - startTime)+" Millis, using parameters=["+SwtUtil.arrayToString(parameters, ", ")+"].");
		}
		return result;
	}

	/**
	 * General reasons execute SELECT SQL query
	 * @param squery
	 * @param parameters
	 * @return
	 * @throws Exception
	 */
	public static LinkedList<HashMap<String, Object>> executeSelectQuery(String squery, Object...parameters) throws Exception{
		LinkedList<HashMap<String, Object>> result = new LinkedList<HashMap<String, Object>>();
		HashMap<String, Object> rowMap = null;

		Connection conn = null;
		ResultSet rs = null;
		PreparedStatement  pstmt = null;
		int columnCount = 0;
		int paramsCount = parameters.length;
		long startTime = System.currentTimeMillis();
		try {
			// Initialize connection and the prepared statement
			conn  = ConnectionManager.getInstance().databaseCon();
			pstmt = conn.prepareStatement(squery);
			// Note: pstmt.getParameterMetaData(); ==> throws error "Unsupported feature" as jdbc is provided by Oracle
			// Set bind variables
			for (int i=0; i<paramsCount; i++){
				Object paramValue = parameters[i];
				if(paramValue==null)
					pstmt.setNull(i+1, Types.VARCHAR);
				else if(paramValue instanceof Integer)
					pstmt.setInt(i+1, (Integer)paramValue);
				else if (paramValue instanceof String)
					pstmt.setString(i+1, (String)paramValue);
				else if (paramValue instanceof java.util.Date)
					pstmt.setDate(i+1, SwtUtil.truncateDateTime(((java.util.Date)paramValue)));
				else
					pstmt.setObject(i+1, paramValue);
			}

			// Execute the statement and fill results
			rs = pstmt.executeQuery();

			ResultSetMetaData metadata = rs.getMetaData();
			int colCount = metadata.getColumnCount();
			int idx = 1;

			ArrayList<String> colNames = new ArrayList<String>();
			ArrayList<Integer> colTypes= new ArrayList<Integer>();
			for (int i = idx; i <= colCount; i++) {
				colNames.add(metadata.getColumnName(i));
				colTypes.add(metadata.getColumnType(i));
			}
			if (rs != null) {
				while (rs.next()) {
					rowMap = new HashMap<String, Object>();
					for (int i = idx; i <= colCount; i++) {
						// check if CLOB type
						if (colTypes.get(i -idx) == 2005)
						{
							rowMap.put(colNames.get(i - idx).toLowerCase(),
									rs.getString(i));
						}else
						{
							Object colValue = rs.getObject(i);
							if(colValue!=null){
								if(colValue.getClass().getCanonicalName().equals(BigDecimal.class.getName())){
									rowMap.put(colNames.get(i - idx).toLowerCase(), ((BigDecimal)colValue).doubleValue());
								}
								else if (colValue.getClass().getCanonicalName().equals(Timestamp.class.getName())){
									rowMap.put(colNames.get(i - idx).toLowerCase(), new Date(((Timestamp)colValue).getTime()));
								}
								else{
									rowMap.put(colNames.get(i - idx).toLowerCase(),
											rs.getObject(i));
								}
							}else {
								rowMap.put(colNames.get(i - idx).toLowerCase(),
										rs.getObject(i));
							}
						}

					}
					result.add(rowMap);
				}
			}
			return result;
		} finally {
			try{if(rs!=null)rs.close();}catch(SQLException e){}
			try{if(pstmt!=null)pstmt.close();}catch(SQLException e){}
			try{if(conn!=null)conn.close();}catch(SQLException e){}
		}
	}

	/**
	 * General reasons execute SELECT SQL query
	 * @param squery
	 * @param parameters
	 * @return
	 * @throws Exception
	 */
	public static LinkedList<LinkedList<Object>> executeSelectQueryAsList(String squery, Object...parameters) throws Exception{
		LinkedList<LinkedList<Object>> result = new LinkedList<LinkedList<Object>>();
		LinkedList<Object> rowMap = null;

		Connection conn = null;
		ResultSet rs = null;
		PreparedStatement  pstmt = null;
		int columnCount = 0;
		int paramsCount = parameters.length;
		long startTime = System.currentTimeMillis();
		try {
			// Initialize connection and the prepared statement
			conn  = ConnectionManager.getInstance().databaseCon();
			pstmt = conn.prepareStatement(squery);
			// Note: pstmt.getParameterMetaData(); ==> throws error "Unsupported feature" as jdbc is provided by Oracle
			// Set bind variables
			for (int i=0; i<paramsCount; i++){
				Object paramValue = parameters[i];
				if(paramValue==null)
					pstmt.setNull(i+1, Types.VARCHAR);
				else if(paramValue instanceof Integer)
					pstmt.setInt(i+1, (Integer)paramValue);
				else if (paramValue instanceof String)
					pstmt.setString(i+1, (String)paramValue);
				else if (paramValue instanceof java.util.Date)
					pstmt.setDate(i+1, SwtUtil.truncateDateTime(((java.util.Date)paramValue)));
				else
					pstmt.setObject(i+1, paramValue);
			}

			// Execute the statement and fill results
			rs = pstmt.executeQuery();

			ResultSetMetaData metadata = rs.getMetaData();
			int colCount = metadata.getColumnCount();
			int idx = 1;

			ArrayList<String> colNames = new ArrayList<String>();
			ArrayList<Integer> colTypes= new ArrayList<Integer>();
			for (int i = idx; i <= colCount; i++) {
				colNames.add(metadata.getColumnName(i));
				colTypes.add(metadata.getColumnType(i));
			}
			if (rs != null) {
				while (rs.next()) {
					rowMap = new LinkedList< Object>();
					for (int i = idx; i <= colCount; i++) {
						// check if CLOB type
						if (colTypes.get(i -idx) == 2005)
						{
							rowMap.add(	rs.getString(i));
						}else
						{
							Object colValue = rs.getObject(i);
							if(colValue!=null){
								if(colValue.getClass().getCanonicalName().equals(BigDecimal.class.getName())){
									rowMap.add( ((BigDecimal)colValue).doubleValue());
								}
								else if (colValue.getClass().getCanonicalName().equals(Timestamp.class.getName())){
									rowMap.add( new Date(((Timestamp)colValue).getTime()));
								}
								else{
									rowMap.add(
											rs.getObject(i));
								}
							}else {
								rowMap.add(
										rs.getObject(i));
							}
						}

					}
					result.add(rowMap);
				}
			}
			return result;
		} finally {
			try{if(rs!=null)rs.close();}catch(SQLException e){}
			try{if(pstmt!=null)pstmt.close();}catch(SQLException e){}
			try{if(conn!=null)conn.close();}catch(SQLException e){}
		}
	}

	/**
	 * Executes a callable statement
	 * TODO: Register output parameters dynamically
	 * @param updateCall
	 * @param parameters
	 */
	public static void executeUpdate(String updateCall, Object...parameters){
		log.debug("method executeUpdate() start");
		Connection conn=null;
		CallableStatement pstmt=null;
		int paramsCount = parameters.length;
		try{
			conn  = ConnectionManager.getInstance().databaseCon();
			pstmt=conn.prepareCall(updateCall);

			// Set bind variables
			for (int i=0; i<paramsCount; i++){
				Object paramValue = parameters[i];
				if(paramValue==null)
					pstmt.setNull(i+1, Types.VARCHAR);
				else if(paramValue instanceof Integer)
					pstmt.setInt(i+1, (Integer)paramValue);
				else if (paramValue instanceof String)
					pstmt.setString(i+1, (String)paramValue);
				else if (paramValue instanceof java.util.Date)
					pstmt.setDate(i+1, SwtUtil.truncateDateTime(((java.util.Date)paramValue)));
				else
					pstmt.setObject(i+1, paramValue);
			}

			// Perform the DB call
			pstmt.executeUpdate();
			log.debug("method executeUpdate() end without errors");
		}catch(Exception  e){
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance().handleException(e,"executeUpdate", SwtUtil.class));
			log.error("method executeUpdate() finished with errors: "+e.getMessage());
		}finally{
			JDBCCloser.close(pstmt);
			JDBCCloser.close(conn);
		}
	}


	public static boolean sendEmail(ArrayList<String> recipientsTo, ArrayList<String> recipientsCC,
									String subject, String body, HashMap<String, InputStream> attachments) throws SwtException {
		if (!isMailEnabled()) {
			log.error("Could not send mail since 'Send mail' property is inactive ");
			logErrorInDatabase(new SwtException("Could not send mail since 'Send mail' property is inactive"));
			return false;
		}

		int maxRetries = getIntProperty(SwtConstants.MAIL_MAX_RETRY, 5);
		int baseDelaySeconds = getIntProperty(SwtConstants.MAIL_BASE_DELAY, 10);
		String mailSubjectPrefix = PropertiesFileLoader.getInstance().getMailPropertyValue(SwtConstants.MAIL_SUBJECT_PREFIX);
		String fromEmail = PropertiesFileLoader.getInstance().getMailPropertyValue(SwtConstants.MAIL_FROM_EMAIL);

		for (int retryCount = 0; retryCount < maxRetries; retryCount++) {
			try {
				waitForPoolReadiness();
				try (ClosableSmtpConnection connection = SwtUtil.smtpConnectionPool.borrowObject()) {
					MimeMessage message = createMimeMessage(connection, fromEmail, recipientsTo, recipientsCC, subject, mailSubjectPrefix, body, attachments);
					connection.sendMessage(message, message.getAllRecipients());
					return true; // Email sent successfully
				}
			} catch (Exception e) {
				handleSendEmailException(e, retryCount, maxRetries);
				if (shouldRetry(e)) {
					int delaySeconds = (int) Math.pow(2, retryCount) * baseDelaySeconds;
					sleepBeforeRetry(delaySeconds);
				} else {
					break; // Don't retry for certain exceptions (e.g., invalid address)
				}
			}
		}

		log.error("Failed to send email after " + maxRetries + " retries.");
		return false;
	}

	private static void waitForPoolReadiness() throws RuntimeException {
		int maxWaitTime = 10000; // 10 seconds
		int retryInterval = 500; // 0.5 seconds
		int elapsedTime = 0;

		while ((SwtUtil.smtpConnectionPool == null || !SwtUtil.smtpConnectionPool.isReady()) && elapsedTime < maxWaitTime) {
			try {
				Thread.sleep(retryInterval);
				elapsedTime += retryInterval;
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				throw new RuntimeException("Thread was interrupted while waiting for smtpConnectionPool to be ready", e);
			}
		}

		if (!SwtUtil.smtpConnectionPool.isReady()) {
			throw new RuntimeException("SmtpConnectionPool was not ready within the specified time");
		}
	}

	private static MimeMessage createMimeMessage(ClosableSmtpConnection connection, String fromEmail,
												 ArrayList<String> recipientsTo, ArrayList<String> recipientsCC,
												 String subject, String mailSubjectPrefix,
												 String body, HashMap<String, InputStream> attachments) throws MessagingException, IOException {
		MimeMessage message = new MimeMessage(connection.getSession());

		// Set the "From" address
		message.setFrom(new InternetAddress(fromEmail != null ? fromEmail :
				PropertiesFileLoader.getInstance().getMailPropertyValue(SwtConstants.MAIL_USER_EMAIL)));

		// Set recipients
		for (String recipientTo : recipientsTo) {
			message.addRecipient(Message.RecipientType.TO, new InternetAddress(recipientTo));
		}
		if (recipientsCC != null) {
			for (String recipientCC : recipientsCC) {
				message.addRecipient(Message.RecipientType.CC, new InternetAddress(recipientCC));
			}
		}

		// Set subject
		message.setSubject(SwtUtil.isEmptyOrNull(mailSubjectPrefix) ? subject : mailSubjectPrefix + " " + subject);

		// Create multipart message
		Multipart multipart = new MimeMultipart();

		// Add body part
		BodyPart messageBodyPart = new MimeBodyPart();
		messageBodyPart.setContent(body, "text/html");
		multipart.addBodyPart(messageBodyPart);

		// Add attachments
		if (attachments != null && !attachments.isEmpty()) {
			for (Map.Entry<String, InputStream> entry : attachments.entrySet()) {
				messageBodyPart = new MimeBodyPart();
				
				//TODO:SBR Migrate !
				/*
				DataSource attachment = new ByteArrayDataSource(entry.getValue(), "application/octet-stream");
				messageBodyPart.setDataHandler(new DataHandler(attachment));
				messageBodyPart.setFileName(entry.getKey());
				*/
				multipart.addBodyPart(messageBodyPart);
			}
		}

		message.setContent(multipart);
		return message;
	}

	private static void handleSendEmailException(Exception e, int retryCount, int maxRetries) {
		String errorDesc;
		if (e instanceof MessagingException && e.getMessage().contains("Invalid Addresses")) {
			SendFailedException sfe = (SendFailedException) e;
			String invalidAddresses = String.join(",", Arrays.stream(sfe.getInvalidAddresses())
					.map(Address::toString)
					.toArray(String[]::new));
			errorDesc = "Could not send mail: Invalid recipient address(es): " + invalidAddresses;
		} else {
			errorDesc = "Could not reach mail server or send mail. Please check error log for more details.";
		}

		log.error("SwtUtil - [sendEmail] - Exception: " + errorDesc, e);

		if (retryCount < maxRetries - 1) {
			log.error("SwtUtil - [sendEmail] Retrying... Attempt: " + (retryCount + 1));
		}
	}

	private static boolean shouldRetry(Exception e) {
		return !(e instanceof MessagingException && e.getMessage().contains("Invalid Addresses"));
	}

	private static void sleepBeforeRetry(int delaySeconds) {
		try {
			Thread.sleep(delaySeconds * 1000L);
		} catch (InterruptedException ie) {
			Thread.currentThread().interrupt();
		}
	}

	private static int getIntProperty(String key, int defaultValue) {
		String value = PropertiesFileLoader.getInstance().getMailPropertyValue(key);
		if (!SwtUtil.isEmptyOrNull(value)) {
			try {
				return Integer.parseInt(value);
			} catch (NumberFormatException e) {
				// Log the error and return the default value
				log.warn("Invalid value for property " + key + ". Using default: " + defaultValue);
			}
		}
		return defaultValue;
	}


	public static boolean isMailEnabled() {
		String mailEnabled = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.MAIL_ENABLED);

		return mailEnabled.equalsIgnoreCase("TRUE");
	}

	public static String createDummyPassword(String password) {
		String dummyPass = "";
		if(password == null)
			return "";

		for (int i = 0; i < password.length(); i++) {
			dummyPass += "*";
		}
		return dummyPass;
	}


	public static String generateNonce(HttpServletRequest request) throws NoSuchAlgorithmException {
		String token  = null;
		try {
			// Create a secure random number generator
//			SecureRandom sr = SecureRandom.getInstance("SHA1PRNG");
//			sr.setSeed(java.lang.System.currentTimeMillis());
//			byte[] pseudoRandom = new byte[40];
//			sr.nextBytes(pseudoRandom);
//			String sb = Arrays.toString(pseudoRandom);
//			token = ""+sb.hashCode()+RandomStringUtils.randomAlphabetic(3);
			boolean isLogonRequest = XSSFilter.getPathToFilter(request).startsWith("login.jsp")|| XSSFilter.getPathToFilter(request).startsWith("prelogin.jsp");
			boolean isSessionExpiredURL = XSSFilter.getPathToFilter(request).startsWith("sessionexpire.jsp");
			boolean isErrorURL = XSSFilter.getPathToFilter(request).startsWith("error.jsp");
			CommonDataManager cdm = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN));
			boolean isWhiteListedRequest = (isSessionExpiredURL || "true".equals(request.getParameter("isExpiredSession")) || isErrorURL);
			token = SwtUtil.addToken( cdm, isLogonRequest, isWhiteListedRequest);
			return token;
		}catch(Exception e) {
			e.printStackTrace();
			log.error("SwtUtil - [generateNonce] - Exception: " + e.getMessage());
			return hash(Math.random() * 10000 + "F");
		}

	}

	public static String getMenuAccessValue(String menuId, HttpServletRequest request) throws SwtException {
		String accessId = null;
		try {
			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");

			MenuItem item = logonDAO.getMenuItem(menuId,  SwtUtil.getCurrentUser(request.getSession()));
			if(item != null)
				accessId = item.getAccessId();
			else
				accessId = "2";
		} catch (SwtException e) {
			log.error("SwtUtil - [getMenuAccessValue] - Exception: " + e.getMessage());
		}
		return accessId;
	}

	public static boolean getMenuHasFullAccessForRoleFromRequest(String menuId, HttpServletRequest request) throws SwtException {
		try {
			Collection<MenuAccessOptionsGui> menuAccessGui = (Collection) request
					.getSession().getAttribute("itemDescList");

			if(menuAccessGui != null) {
				MenuAccessOptionsGui menuAccessOptionsGui = menuAccessGui.stream().filter(gui -> gui.getItemId().equals(menuId)).findFirst().orElse(null);
				if(menuAccessOptionsGui != null) {

					if(menuAccessOptionsGui.getParentId() != null
							&& getParentWithId(menuAccessGui, menuAccessOptionsGui.getParentId()) != null
							&& getParentWithId(menuAccessGui, menuAccessOptionsGui.getParentId()).getMenuAccessHTMLNoAccess() != null
							&& getParentWithId(menuAccessGui, menuAccessOptionsGui.getParentId()).getMenuAccessHTMLNoAccess().equals("checked")) {
						return false;
					}

					return !SwtUtil.isEmptyOrNull(menuAccessOptionsGui.getMenuAccessHTMLFullAccess());
				}
				else
					return false;
			}else
				return false;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("SwtUtil - [getMenuAccessValue] - Exception: " + e.getMessage());
			return false;
		}
	}



	public static boolean checkAccessToHigherMenuItemsFromRequest(HttpServletRequest request) throws SwtException {
		try {

			Collection<MenuAccessOptionsGui> menuAccessGui = (Collection) request
					.getSession().getAttribute("itemDescList");
			if(menuAccessGui != null) {
				boolean result =  menuAccessGui.stream()
						.anyMatch(gui -> (!SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLFullAccess()) || !SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLViewAccess()))
								&& gui.getParentId() != null
								&& getParentWithId(menuAccessGui, gui.getParentId()) != null
								&& getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess() != null
								&& getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess().equals("checked"));

				return result;
//				List<MenuAccessOptionsGui> itemsWithIssue = menuAccessGui.stream()
//					    .filter(gui -> (!SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLFullAccess()) || !SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLViewAccess()))
//					            && gui.getParentId() != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()) != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess() != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess().equals("checked"))
//					    .collect(Collectors.toList());


//				boolean hasItem = menuAccessGui.stream()
//					    .anyMatch(gui -> gui.getMenuAccessHTMLFullAccess() != null
//					            && gui.getParentId() != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()) != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLViewAccess() != null);



			}else {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("SwtUtil - [getMenuAccessValue] - Exception: " + e.getMessage());
			return true;
		}
	}


	public static String checkAccessToHigherMenuItemsFromRequestList(HttpServletRequest request) throws SwtException {
		String result = "";
		try {

			Collection<MenuAccessOptionsGui> menuAccessGui = (Collection) request
					.getSession().getAttribute("itemDescList");
			if(menuAccessGui != null) {

				List<MenuAccessOptionsGui> itemsWithIssue = menuAccessGui.stream()
						.filter(gui -> (!SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLFullAccess()) || !SwtUtil.isEmptyOrNull(gui.getMenuAccessHTMLViewAccess()))
								&& gui.getParentId() != null
								&& getParentWithId(menuAccessGui, gui.getParentId()) != null
								&& getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess() != null
								&& getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLNoAccess().equals("checked"))
						.collect(Collectors.toList());


//				boolean hasItem = menuAccessGui.stream()
//					    .anyMatch(gui -> gui.getMenuAccessHTMLFullAccess() != null
//					            && gui.getParentId() != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()) != null
//					            && getParentWithId(menuAccessGui, gui.getParentId()).getMenuAccessHTMLViewAccess() != null);


				result = itemsWithIssue.stream()
						.map(MenuAccessOptionsGui::getDescription)
						.collect(Collectors.joining(", "));
				if(SwtUtil.isEmptyOrNull(result)) {
					result =  "";
				}

			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("SwtUtil - [getMenuAccessValue] - Exception: " + e.getMessage());
			result =  "";
		}
		return result;
	}

	private static MenuAccessOptionsGui getParentWithId(Collection<MenuAccessOptionsGui> menuAccessGui, String parentId) {
		return menuAccessGui.stream()
				.filter(gui -> gui.getItemId().equalsIgnoreCase(parentId))
				.findFirst()
				.orElse(null);
	}


	public static String getMenuAccessValueForRole(String menuId, String roleId) throws SwtException {
		String accessId = null;
		try {
			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			MenuItem item = logonDAO.getMenuItemForRole(menuId,  roleId);
			if(item != null)
				accessId = item.getAccessId();
			else
				accessId = "2";
		} catch (SwtException e) {
			log.error("SwtUtil - [getMenuAccessValue] - Exception: " + e.getMessage());
		}
		return accessId;
	}


	public static boolean getAuthorizationAccess(HttpServletRequest request) throws Exception {
		try {
			String userAccessId = null;
			if(aclQueryXPath == null)
			{
				aclQueryXPath =  XPathFactory.newInstance().newXPath();
				DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
				DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
				aclQueryDocument = dBuilder.parse(SwtUtil.class.getResourceAsStream("/org/swallow/util/list_acl.xml"));
			}
			String url = getPathToFilter(request);
			String method = request.getParameter("method");
			if(SwtUtil.isEmptyOrNull(method)) {
				return true;
			}else {
				// Return the result as string
				String accessXPath = "/acl/url[text()='"+url+"' and @method='"+method+"']/@access";
				String menuItemIdXPath = "/acl/url[text()='"+url+"' and @method='"+method+"']/@menuItemId";
				String aclAccessValue =  (String) aclQueryXPath.compile(accessXPath).evaluate(aclQueryDocument, XPathConstants.STRING);
				String menuItemId =  (String) aclQueryXPath.compile(menuItemIdXPath).evaluate(aclQueryDocument, XPathConstants.STRING);

				if(SwtUtil.isEmptyOrNull(menuItemId)) {
					return getUserRoleAccess(request);
				}else {
					LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
					MenuItem item = logonDAO.getMenuItem(menuItemId,  SwtUtil.getCurrentUser(request.getSession()));

					if(item == null)
						return true;
					//Check the parents access if it's lower then the screen access then access will be forbidden to the functionality
					// Example if the user has full access to the Movement display but has only view access to Work menu
					// the screen access will be set as view and user will not have access to changes actions
					int accessId  = -1;
					if(item.getParentId() != null){
						User user = ((CommonDataManager) request.getSession()
								.getAttribute(SwtConstants.CDM_BEAN)).getUser();
						MenuItem parentMenuItem = logonDAO.getMenuItem(item.getItemId(), user);

						int prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());
						accessId = Integer.parseInt(item.getAccessId());

						if(prentAccessId > accessId)
							accessId = prentAccessId;

						while(!SwtUtil.isEmptyOrNull(parentMenuItem.getParentId()) && !parentMenuItem.getParentId().equals("0")){
							parentMenuItem = logonDAO.getMenuItem(parentMenuItem.getParentId(),user);
							prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());

							if(prentAccessId > accessId)
								accessId = prentAccessId;
						}
					}

					userAccessId = ""+accessId;

					if(isEmptyOrNull(userAccessId))
						return true;
					if("w".equals(aclAccessValue)) {
						if(userAccessId.equals("0")) {
							return getUserRoleAccess(request);
						}else {
							return false;
						}
					}else {
						if(userAccessId.equals("0") || userAccessId.equals("1")) {
							return getUserRoleAccess(request);
						}else {
							return false;
						}
					}
				}

			}



		} catch (Throwable e) {
			return true;
			//throw new Exception("Error extracting query from config file acllist.xml: "+e.getMessage(), e);
		}
	}
	public static boolean getUserRoleAccess(HttpServletRequest request) throws Exception {
		String roleAccessMethod = null;
		try {
			String url = getPathToFilter(request);
			String method = request.getParameter("method");
			String roleMethodXPath = "/acl/url[text()='"+url+"' and @method='"+method+"']/@role_validation";

			if(aclQueryXPath == null)
			{
				aclQueryXPath =  XPathFactory.newInstance().newXPath();
				DocumentBuilderFactory dbFactory  = DocumentBuilderFactory.newInstance();
				DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
				aclQueryDocument = dBuilder.parse(SwtUtil.class.getResourceAsStream("/org/swallow/util/list_acl.xml"));
			}

			roleAccessMethod =  (String) aclQueryXPath.compile(roleMethodXPath).evaluate(aclQueryDocument, XPathConstants.STRING);
			if(request != null && request.getSession() != null){

				String paramListAsString = null;
				String[] paramsList = null;
				Object[] paramsValues = null;
				Class[] classList = null;
				String methodName = null;
				String paramsValuesAsString = null;
				if(!isEmptyOrNull(roleAccessMethod)) {
					if(roleAccessMethod.indexOf(")") != -1 && roleAccessMethod.indexOf("(") != -1 ){
						paramListAsString = roleAccessMethod.substring(roleAccessMethod.indexOf("(") + 1, roleAccessMethod.indexOf(")"));
						methodName = roleAccessMethod.substring(0 , roleAccessMethod.indexOf("("));
						paramsList = paramListAsString.split("(\\s)*,(\\s)*");
						paramsValues = new Object[paramsList.length + 1];
						paramsValues[0] = request;
						paramsValuesAsString = new String();
						for(int i = 0; i < paramsList.length ; i++){
							paramsValues[i+1] = (Object) request.getParameter(paramsList[i]);
							paramsValuesAsString+=paramsValues[i+1]+",";
						}
						if(paramsValuesAsString.length()>0){
							paramsValuesAsString= paramsValuesAsString.substring(0, paramsValuesAsString.length()-1);
							paramsValuesAsString= methodName+"("+paramsValuesAsString+")";
						}

						CommonDataManager cdm = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN));
						if(cdm != null && cdm.getRoleAccessFromCache(paramsValuesAsString) != null) {
							AccessItem acessItem = cdm.getRoleAccessFromCache(paramsValuesAsString);

							if(acessItem.getTime() != null) {
								long diff = new Date().getTime() - acessItem.getTime().getTime();
								long seconds = TimeUnit.MILLISECONDS.toSeconds(diff);
								if(seconds < (0 * 60))
									return acessItem.isAllowAccess();
							}
						}

						if(paramsValues.length> 0 ){
							classList = new Class[paramsValues.length];
							classList[0] = HttpServletRequest.class;
							for(int i = 1; i < paramsValues.length ; i++){
								classList[i] = String.class;
							}
						}

						boolean result =  (Boolean) invokeStaticMethod("org.swallow.util.XSSUtil", methodName, paramsValues, classList);
						AccessItem item = cdm.new AccessItem();
						item.setAllowAccess(result);
						item.setTime(new Date());
						cdm.addRoleAccessCache(paramsValuesAsString,  item);

						return result;

					}else {
						return true;
					}

				}
			}else {
				return true;
			}

			return true;
		} catch (Throwable e) {
			return true;
			//throw new Exception("Error extracting query from config file acllist.xml: "+e.getMessage(), e);
		}
	}


	public static String getPathToFilter(HttpServletRequest httpservletrequest) {
		String pathStr = "";
		pathStr = httpservletrequest.getServletPath();
		return pathStr.startsWith("/")?pathStr.substring(1):pathStr;
	}

	public static synchronized boolean getCallTrace(int...maxLines){
		try{
			StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();
			int lineNbr = 0;
			boolean firstLine = true;
			for (StackTraceElement ste : traceElements) {
				if(!ste.getMethodName().equals("printCallTrace")
						&& !(ste.getClassName()+"."+ste.getMethodName()).equals("java.lang.Thread.getStackTrace")
						//&& (ste.toString().startsWith("org.swallow"))
						&& (!ste.toString().contains(XSSFilter.class.getName()))
						&& (!ste.toString().contains(SessionFilter.class.getName()))
						&& (!ste.toString().contains(ResponseHeaderFilter.class.getName()))
						&& (!ste.toString().contains(RequestFilter.class.getName()))
				)
				{
					// Always skip the first line as displays the printCallTrace() caller
					if(firstLine){
						firstLine = false;
						continue;
					}
					// Print the stack trace element
					if(ste.getClassName().contains("org.swallow."))
						return true;
					else if(lineNbr == maxLines[0])
						return false;
					else
						lineNbr++;
				}
			}
			return false;
		}catch(Exception e) {
		}
		return false;

	}

	public static synchronized boolean isJSPCalled(int...maxLines){
		StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();
		int lineNbr = 0;
		boolean firstLine = true;
		for (StackTraceElement ste : traceElements) {
			if(!ste.getMethodName().equals("printCallTrace")
					&& !(ste.getClassName()+"."+ste.getMethodName()).equals("java.lang.Thread.getStackTrace")
					//&& (ste.toString().startsWith("org.swallow"))
					&& (!ste.toString().contains(XSSFilter.class.getName()))
					&& (!ste.toString().contains(SessionFilter.class.getName()))
					&& (!ste.toString().contains(ResponseHeaderFilter.class.getName()))
					&& (!ste.toString().contains(RequestFilter.class.getName()))
			)
			{
				// Always skip the first line as displays the printCallTrace() caller
				if(firstLine){
					firstLine = false;
					continue;
				}
				// Print the stack trace element
				if(ste.getClassName().equals("jakarta.servlet.jsp.el.ScopedAttributeELResolver"))
					return true;
				else if(lineNbr == maxLines[0])
					return false;
				else
					lineNbr++;
			}
		}
		return false;

	}

	public static String uncommentSQLQuery(String query) throws Exception{
		String result = null;
		try{
			if(isEmptyOrNull(query)){
				return null;
			}else {
				result = query.replaceAll("(/\\*([^*]|[\\r\\n]|(\\*+([^*/]|[\\r\\n])))*\\*+/)|(--[^\\r\\n]*)|(^[\\r\\n\\s]*)", "");
				result= result.replace("\n", " ");
				return result;
			}

		}catch(Exception e){
		}


		return result;

	}

	public static  boolean checkToken(String token, CommonDataManager cdm, boolean isLogonRequest)
	{
		if(isLogonRequest){
			if(validLogonTokenList != null && validLogonTokenList.size() > 0){
				return (validLogonTokenList.indexOf(token) != -1);
			}
		}else if (cdm != null) {
			if(cdm.getValidTokenList() != null && cdm.getValidTokenList().size() > 0){
				return (cdm.getValidTokenList().indexOf(token) != -1);
			}
		}
		return false;
	}

	public static String addToken(CommonDataManager cdm, boolean isLogonRequest, boolean isWhiteListedRequest){
		String token = null;
		try {
//		if (isLogonRequest || cdm == null) {
			if(validLogonTokenList==null) {
				validLogonTokenList = new ArrayList<String>();
			}
//			validLogonTokenList.add(token);
//
//			if(!isLogonRequest && !isWhiteListedRequest && cdm == null) {
//				log.error("SwtUtil - [addToken] - Exception: cannot generate token for null CommonDataManager, a CSRF attack may be fired");
//			}
//		}else if (cdm != null) {
//			if(cdm.getValidTokenList()==null) {
//				cdm.setValidTokenList(new ArrayList<String>());
//			}
//			cdm.getValidTokenList().add(token);
//		}
			Random random = new Random();
			int randomNumber = random.nextInt( 20);

			if (cdm != null) {
				if(cdm.getValidTokenList()!=null) {
					token = cdm.getValidTokenList().get(randomNumber);
				}else {
					LogonManager logonMngr = (LogonManager) (SwtUtil
							.getBean("logonManager"));
					if(cdm.getUser() != null) {
						User userToken = logonMngr.getUserDetail(SwtUtil.getCurrentHostId(), cdm.getUser().getId().getUserId());
						String tokenList = SwtUtil.generateCsrfTokens(cdm);;
						if(userToken.getUserAuthDetails() == null) {
							userToken.setUserAuthDetails(new UserAuthDetails());
							userToken.getUserAuthDetails().getId().setHostId(SwtUtil.getCurrentHostId());
							userToken.getUserAuthDetails().getId().setUserId(userToken.getId().getUserId());
						}
						userToken.getUserAuthDetails().setCsrfTokens(tokenList);
						logonMngr.updateUserDetail(userToken);
					}
				}
			}else {
				if (isLogonRequest || cdm == null) {
					token  = null;
					// Create a secure random number generator
					SecureRandom sr = null;
					try {
						sr = SecureRandom.getInstance("SHA1PRNG");
					} catch (NoSuchAlgorithmException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					sr.setSeed(java.lang.System.currentTimeMillis());
					byte[] pseudoRandom = new byte[40];
					sr.nextBytes(pseudoRandom);
					String sb = Arrays.toString(pseudoRandom);
					token = ""+sb.hashCode()+RandomStringUtils.randomAlphabetic(3);
					validLogonTokenList.add(token);
				}
			}

		}catch(Exception e) {
			e.printStackTrace();
		}
		return token;
	}


	public static String generateCsrfTokens(CommonDataManager cdm){
		String result = null;
		try {
			if(validLogonTokenList==null) {
				validLogonTokenList = new ArrayList<String>();
			}
			StringBuffer bufferToken = new StringBuffer();
			for (int i = 0; i < 20; i++) {
				String token  = null;
				// Create a secure random number generator
				SecureRandom sr = null;
				try {
					sr = SecureRandom.getInstance("SHA1PRNG");
				} catch (NoSuchAlgorithmException e) {
				}
				sr.setSeed(java.lang.System.currentTimeMillis());
				byte[] pseudoRandom = new byte[40];
				sr.nextBytes(pseudoRandom);
				String sb = Arrays.toString(pseudoRandom);
				token = ""+sb.hashCode()+RandomStringUtils.randomAlphabetic(3);
				bufferToken.append(token+",");
				validLogonTokenList.add(token);
			}
			bufferToken.deleteCharAt(bufferToken.length() - 1);
			result = bufferToken.toString();


			if(cdm != null)
				cdm.setValidTokenList(new ArrayList<String>(Arrays.asList(bufferToken.toString().split(","))));


		}catch(Exception e) {
			e.printStackTrace();
		}


		return result;
	}

	public static void addIPAdress(String ipAdress) {
		if(validIPadresses==null) {
			validIPadresses = new HashMap<String, String>();
		}

		validIPadresses.put(ipAdress, ipAdress);
	}

	public static boolean checkIPadressExist(String ipAdress) {
		if(validIPadresses != null && validIPadresses.size() >  0 ) {
			if(validIPadresses.containsKey(ipAdress)) {
				return true;
			}else {
				return false;
			}
		}else {
			return false;
		}
	}


	public static void addInactiveSession(String sessionId) {
		if(inactiveSession==null) {
			inactiveSession = new HashMap<String, String>();
		}

		inactiveSession.put(sessionId, sessionId);
	}

	public static boolean checkInactiveSession(String sessionId) {
		if(inactiveSession != null && inactiveSession.size() >  0 ) {
			if(inactiveSession.containsKey(sessionId)) {
				return true;
			}else {
				return false;
			}
		}else {
			return false;
		}
	}
	public static void removeInactiveSession(String sessionId) {
		if(inactiveSession != null) {
			if(inactiveSession.get(sessionId) != null)
				inactiveSession.remove(sessionId);

		}
	}

	/**
	 * Invokes a static method based on java class name, this will create a new instance from the bean class
	 * @param beanClassName
	 * @param methodName
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public static Object invokeStaticMethod(String beanClassName, String methodName, Object[]params , Class[] classList) throws Exception
	{
		try {
			Class clazz = Class.forName(beanClassName);
			if((classList == null || classList.length == 0) && (params != null && params.length > 0) ){
				classList = new Class[params.length];

				classList[0] = HttpServletRequest.class;
				for(int i = 1; i < params.length ; i++){
					if(params[i] != null)
						classList[i] = params[i].getClass();
					else
						classList[i] = String.class;
				}
				Method method = clazz.getMethod(methodName, classList);
				return method.invoke(null, params);
			}else if(classList.length == params.length){
				Method method = clazz.getMethod(methodName, classList);
				return method.invoke(null, params);
			}else {
				return true;
			}
		}catch(Throwable th) {
			return true;
		}
	}

	public static HashMap<String, String> getStyleMap() {
		HashMap<String, String> StyleMap = new HashMap<String, String>();


		StyleMap.put("subtitleFontColor", "black");
		StyleMap.put("subtitleForegroundColor", "#d6e3fe");
		StyleMap.put("subtitleBackgroundColor", "#d6e3fe");
		StyleMap.put("subtitleFontHeightPoints", "14");
		StyleMap.put("subtitleFontWeight", "bold");

		return StyleMap;
	}


	/**
	 * Used to send support mail
	 * @param recipient
	 * @param templateMapping
	 * @param IsScenario
	 * @throws SwtException
	 */
	public static boolean sendReportAsEmail(SwtRecipient recipient, HashMap<String, Object> templateMapping, File reportFile) throws SwtException {

		// Email's properties
		String templateFilename = null;
		String subject = null;
		String recipientsLang = null;
		HashMap<String, InputStream> attachments = null;
		try {
			log.info("SendingEmailsJob  - [sendReportAsEmail] - Entering");

			templateMapping.put("recipient_name", recipient.getRecipientName());
			//templateMapping.put("report_name", "BASEL A");
			recipientsLang = recipient.getRecipientLang();
			attachments = new HashMap<String, InputStream>();

			if (!SwtUtil.isEmptyOrNull(recipientsLang)) {
				if ("EN".equalsIgnoreCase(recipientsLang)) {
					subject = "SMART Scheduled Report:"+ templateMapping.get("job_name") +" - "+templateMapping.get("report_name");//according to user language.
					templateFilename = SwtUtil.contextRealPath+SwtConstants.SCHEDULED_REPORTS_TEMPLATE_EN;

				} else if ("FR".equalsIgnoreCase(recipientsLang)) {
					subject = "SMART Scheduled Report:"+ templateMapping.get("job_name") +" - "+templateMapping.get("report_name");//according to user language.
					templateFilename = SwtUtil.contextRealPath+SwtConstants.SCHEDULED_REPORTS_TEMPLATE_FR;
				}
			} else {
				templateFilename = SwtUtil.contextRealPath+SwtConstants.SCHEDULED_REPORTS_TEMPLATE_EN;
			}


			// Write content into HTML mail template
			Configuration cfg = new Configuration();
			File templateFile = new File(templateFilename);
			cfg.setDirectoryForTemplateLoading(new  File(SwtUtil.contextRealPath+"/templates"));
			Template template = cfg.getTemplate(templateFile.getName());

			Writer out = new StringWriter();
			template.process(templateMapping, out);

			ArrayList<String> recipients = new ArrayList<String>();
			recipients.add(recipient.getRecipientEmail());

			attachments.put(reportFile.getName()  ,new FileInputStream(reportFile));
			//Send Email
			log.info("SendingEmailsJob  - [sendReportAsEmail] - Exit");
			return SwtUtil.sendEmail(recipients, null, subject, out.toString(), attachments) ;

		} catch (Exception e) {
			log.error("SwtUtil- [executeJob] - Exception " + e.getMessage());
			SwtException e1 = new SwtException(e.getMessage());

			return false;
		} finally {
			templateFilename = null;
			subject = null;
		}
	}

	public static String convertKeywordToDate(HttpSession session, String keyword, boolean evaluteAsntityDate,
											  String entityId, String runDateWorkdays) {

		String dateAsString = null;
		String dateFormat = null;
		Date dbDate = null;
		try {
			if (!SwtUtil.isEmptyOrNull(keyword)) {
				if (session != null)
					dateFormat = getCurrentDateFormat(session);
				else
					dateFormat = getCurrentDateFormat(null);

				if (evaluteAsntityDate) {
					dbDate = getSysParamDateWithEntityOffset(entityId);
				} else {
					dbDate = getSystemDateFromDB();
				}
				SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
				dbDate = sysParamsManager.applicateKeywordOnDate(keyword, dbDate, runDateWorkdays);
				dateAsString = formatDate(dbDate, dateFormat);

			}
		} catch (SwtException e) {
			log.error("Exception Catched in SwtUtil - [convertKeywordToDate] " + e.getMessage());
		}

		return dateAsString;
	}

	// Inner class is used when converting a string value that contains a valid XML to a hash map using XStream library
	public static class MapEntryConverter implements Converter {

		public boolean canConvert(Class clazz) {
			return AbstractMap.class.isAssignableFrom(clazz);
		}

		public void marshal(Object value, HierarchicalStreamWriter writer, MarshallingContext context) {

			AbstractMap map = (AbstractMap) value;
			for (Object obj : map.entrySet()) {
				Map.Entry entry = (Map.Entry) obj;
				writer.startNode(entry.getKey().toString());
				Object val = entry.getValue();
				if ( null != val ) {
					writer.setValue(val.toString());
				}
				writer.endNode();
			}

		}

		public Object unmarshal(HierarchicalStreamReader reader, UnmarshallingContext context) {

			Map<String, String> map = new HashMap<String, String>();

			while(reader.hasMoreChildren()) {
				reader.moveDown();

				String key = reader.getNodeName();
				String value = reader.getValue();
				map.put(key, value);

				reader.moveUp();
			}

			return map;
		}
	}

	public static String getPrettyXML(String xml) throws SwtException{
		try {
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			dbf.setValidating(false);
			DocumentBuilder db = dbf.newDocumentBuilder();
			Document doc = db.parse(new InputSource(new StringReader(xml)));
			Transformer transformer = TransformerFactory.newInstance().newTransformer();
			transformer.setOutputProperty(OutputKeys.INDENT, "yes");
			transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
			transformer.setOutputProperty("omit-xml-declaration", "yes");
			//initialize StreamResult with File object to save to file
			StreamResult result = new StreamResult(new StringWriter());
			DOMSource source = new DOMSource(doc);
			transformer.transform(source, result);
			return result.getWriter().toString();
		} catch (Exception e) {
			// log the error
			log.error("Exception Catched in SwtUtil - [getPrettyXML] " + e.getMessage());
		}
		return xml;
	}

	// Array list of keywords is used in the ILM report screen drilling down Add job details, when adding/amending report job type
	public static ArrayList<LabelValueBean> getKeywords(HttpServletRequest request) {
		keywords = new ArrayList<LabelValueBean>();
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE, getMessage("ilmreport.keyword.label.runDate", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS1, getMessage("ilmreport.keyword.label.runDateMinus1", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS2, getMessage("ilmreport.keyword.label.runDateMinus2", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS3, getMessage("ilmreport.keyword.label.runDateMinus3", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS4, getMessage("ilmreport.keyword.label.runDateMinus4", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS5, getMessage("ilmreport.keyword.label.runDateMinus5", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS6, getMessage("ilmreport.keyword.label.runDateMinus6", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_RUNDATE_MINUS7,  getMessage("ilmreport.keyword.label.runDateMinus7", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_CURRENT_WEEK, getMessage("ilmreport.keyword.label.startOfCurrentWeek", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_CURRENT_WEEK, getMessage("ilmreport.keyword.label.endOfCurrentWeek", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_PREVIOUS_WEEK, getMessage("ilmreport.keyword.label.startOfPreviousWeek", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_PREVIOUS_WEEK, getMessage("ilmreport.keyword.label.endOfPreviousWeek", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_CURRENT_MONTH, getMessage("ilmreport.keyword.label.startOfCurrentMonth", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_CURRENT_MONTH, getMessage("ilmreport.keyword.label.endOfCurrentMonth", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_PREVIOUS_MONTH, getMessage("ilmreport.keyword.label.startOfPreviousMonth", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_PREVIOUS_MONTH, getMessage("ilmreport.keyword.label.endOfPreviousMonth", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_CURRENT_QUARTER, getMessage("ilmreport.keyword.label.startOfCurrentQuarter", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_CURRENT_QUARTER, getMessage("ilmreport.keyword.label.endOfCurrentQuarter", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_PREVIOUS_QUARTER, getMessage("ilmreport.keyword.label.startOfPreviousQuarter", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_PREVIOUS_QUARTER, getMessage("ilmreport.keyword.label.endOfPreviousQuarter", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_CURRENT_YEAR, getMessage("ilmreport.keyword.label.startOfCurrentYear", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_CURRENT_YEAR, getMessage("ilmreport.keyword.label.endOfCurrentYear", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_START_OF_PREVIOUS_YEAR, getMessage("ilmreport.keyword.label.startOfPreviousYear", request)));
		keywords.add(new LabelValueBean(SwtConstants.KEYWORD_END_OF_PREVIOUS_YEAR, getMessage("ilmreport.keyword.label.endOfPreviousYear", request)));
		return keywords;
	}

	/**
	 * This function converts xml to Json
	 * return String
	 */
	public static String xmlToJson(String content) throws IOException{
		try {
			if(!SwtUtil.isEmptyOrNull(content)) {
				Object jsonObj = XML.toJSONObject(content, true);
				content = jsonObj.toString();
			}
		} catch (Exception e) {
			throw new IOException("XML to Json conversion error: "+e.getMessage(), e);
		}
		return content;
	}

	/**
	 * This method returns the accessId of a menu item depending on his Hierarchical parents if one his parent has no access
	 * then it returns no access
	 * @param item
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	public static int getHierarchicalAccessId(MenuItem item, HttpServletRequest request) throws SwtException {
		LogonDAO logonDAO = null;
		User user = null;
		int accessId = 2;
		try {
			logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");

			if(request != null) {
				user = SwtUtil.getCurrentUser(request.getSession());
				if(item.getParentId() != null){
					MenuItem parentMenuItem = logonDAO.getMenuItem(item.getItemId(), user);

					int prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());
					accessId = Integer.parseInt(item.getAccessId());

					if(prentAccessId > accessId)
						accessId = prentAccessId;

					while(!SwtUtil.isEmptyOrNull(parentMenuItem.getParentId()) && !parentMenuItem.getParentId().equals("0")){
						parentMenuItem = logonDAO.getMenuItem(parentMenuItem.getParentId(),user);
						prentAccessId = Integer.parseInt(parentMenuItem.getAccessId());

						if(prentAccessId > accessId)
							accessId = prentAccessId;
					}
				}
			}
		}catch(SwtException e) {

		}
		return accessId;

	}



	/**
	 * Invokes a static method based on java class name, this will create a new instance from the bean class
	 * @param beanClassName
	 * @param methodName
	 * @param content
	 * @return
	 * @throws Exception
	 */
	public static Object invokeStaticMethod(String beanClassName, String methodName, String content) throws SwtException
	{
		try {
			Class clazz = Class.forName(beanClassName);
			Method method = clazz.getMethod(methodName, String.class);
			return method.invoke(null, content);
		} catch (Exception e) {
			throw new SwtException("Error invoking method '"+beanClassName+"."+methodName+"()' with parameters "+content, e);
		}
	}


	/**
	 * Invokes a static method based on java class name, this will create a new instance from the bean class
	 * @param beanClassName
	 * @param methodName
	 * @param content
	 * @return
	 * @throws Exception
	 */
	public static Object invokeStaticMethodByName(String beanClassName, String methodName, boolean isGetInstanceRequired, Class paramsClass[], Object...paramsValues) throws SwtException
	{
		try {

			if(isGetInstanceRequired) {
				Class noparams[] = {};
				Class clazz = Class.forName(beanClassName);
				Method method = clazz.getMethod("getInstance", noparams);
				Object obj =  method.invoke(null);

				Method method2 = clazz.getMethod(methodName, paramsClass);
				Object result = method2.invoke(obj, paramsValues);
				return result;
			}else {
				Class clazz = Class.forName(beanClassName);
				Method method = clazz.getMethod(methodName, paramsClass);
				return method.invoke(null, paramsValues);
			}


		} catch (Exception e) {
			e.printStackTrace();
			throw new SwtException("Error invoking method '"+beanClassName+"."+methodName+"()' with parameters "+paramsValues, e);
		}
	}



	/**
	 * Invokes a method
	 * @param beanName
	 * @param methodName
	 * @param arguments
	 * @return
	 */
	public static Object invokeMethod(Object beanAsObj, String methodName, Object...params) throws Exception
	{
		Class beanClass = beanAsObj.getClass();
		Class cls = Class.forName(beanClass.getName());
		Method method = null;

		//
		if (params==null){
			params = new Object[]{};
		}

		// Find the adequate method name
		Method[] methods = cls.getDeclaredMethods();
		for(int i = 0; i<methods.length; i++){
			Method declaredMethod = methods[i];
			int paramsCount = 0;
			if(declaredMethod.getName().equalsIgnoreCase(methodName) && declaredMethod.getParameterTypes().length == params.length)
			{
				for(int j=0; j < declaredMethod.getParameterTypes().length; j++){

					if(params[j] == null){
						paramsCount++;
						continue;
					}

					Class declaredParamType = declaredMethod.getParameterTypes()[j];
					Class passedParamType = params[j].getClass();

					if(declaredParamType==passedParamType || declaredParamType == java.lang.Object.class){
						paramsCount++;
					}else{
						// If declared parameter type is "Integer", then accept the passed parameter if it is "int"
						if (declaredParamType == int.class && passedParamType ==  Integer.class){
							paramsCount++;
							continue;
						}

						// If declared parameter type is "Boolean", then accept the passed parameter if it is "boolean"
						if (declaredParamType == boolean.class && passedParamType ==  Boolean.class){
							paramsCount++;
							continue;
						}

						// If declared parameter type is "Collection", then accept the passed parameter if it is "ArrayList"
						if (declaredParamType==Collection.class && passedParamType== ArrayList.class){
							paramsCount++;
							continue;
						}

					}
				}

				// If the same number of parameters being passed as those declared inside the method, then OK accept this method
				if (declaredMethod.getParameterTypes().length == paramsCount){
					method = declaredMethod;
					break;
				}
			}
		}

		// Throw a programming error when method is not found
		if(method == null){
			String paramTypes = "";
			for (Object param:params){
				paramTypes += ", "+((param==null)?"null":param.getClass().getName());
			}
			paramTypes = SwtUtil.trimLeft(paramTypes, ", ");

			throw new Exception("Programming Error: No such method called '"+methodName+ "' that has parameters: "+paramTypes);
		}

		// invoke method
		try {
			// Return the result object
			return method.invoke(beanAsObj, params);
		} finally {
			// clean ups
		}
	}
	private static String developerMode = null;
	// Developer mode
	public static final String DEVELOPER_MODE = "developerMode";

	/**
	 * Is mode developer or Prod
	 * @return
	 */
	public static boolean isDeveloperMode(){
		boolean devMode = false;
		if(isEmptyOrNull(developerMode)){
			developerMode =  PropertiesFileLoader.getInstance().getPropertiesValue(DEVELOPER_MODE);
			if(isEmptyOrNull(developerMode)){
				developerMode="N";
			}
		}

		devMode = developerMode.equalsIgnoreCase(SwtConstants.YES) || developerMode.toUpperCase().equalsIgnoreCase(SwtConstants.STR_TRUE);
		return devMode;
	}

	/**
	 * Remove trailing whitespace
	 * @param source
	 * @return
	 */
	public static String trimLeft(String source, String...trailer) {
		if(trailer.length>0)
		{
			if(trailer[0] != null)
				return source.replaceAll("^"+fixRegex(trailer[0])+"+", "");
		}
		return source.replaceAll("^\\s+", "");
	}
	/**
	 * Remove trailing whitespace
	 * @param source
	 * @return
	 */
	public static String trimRight(String source, String...trailer) {
		if(trailer.length>0)
		{
			if(trailer[0] != null)
				return source.replaceAll(fixRegex(trailer[0])+"+$", "");
		}
		return source.replaceAll("\\s+$", "");
	}


	//---------------------------Java reflexion usage:END-----------------------------------//

	/**
	 * Dynamically sorting a list of objects (Java bean classes), example: sortList(pizzas, "size", false, "name", "price", false);
	 * ==> This sorts pizzas list by: size DESC, then by name ASC finally by price DESC
	 *
	 * @param list
	 * @param fields
	 * <AUTHOR> Chebka, SwallowTech Tunisia
	 */
	public static <T> void  sortList(List<T> list, Object...fields) throws SwtException{
		// If no specific fields to use fields
		if(list==null || list.size() == 0){
    	/*Collections.sort(list, new Comparator<T>() {
    		    @Override
    		    public int compare(T lhs, T rhs) {
    		    	return new CompareToBuilder().reflectionCompare(lhs, rhs);
    		    }
    		});*/
			return;
		}

		// Use reflection
		final Field[] reflectFields = list.get(0).getClass().getDeclaredFields();
		AccessibleObject.setAccessible(reflectFields, true);
		final List<Field> reflectFieldsList = (reflectFields != null || reflectFields.length != 0) ? Arrays.asList(reflectFields) : Collections.EMPTY_LIST;

		// Lists of included fields and their sort directions
		final List<String> includedFieldList = new ArrayList<String>();
		final List<Boolean> includedFieldDirection = new ArrayList<Boolean>();

		Object lastObj = null;
		for(Object field:fields){
			if (field instanceof java.lang.String){
				if(lastObj instanceof java.lang.String){
					includedFieldDirection.add(true);
				}
				includedFieldList.add((String)field);
			}else if(field instanceof java.lang.Boolean){
				includedFieldDirection.add((Boolean)field);
			}else{
				new SwtException("Uncompatible type: "+field.getClass()+" for parameter :"+field);
			}
			lastObj = field;
		}

		if(includedFieldDirection.size() == includedFieldList.size()-1){
			includedFieldDirection.add(true);
		}
		if(includedFieldList.size() != includedFieldDirection.size()){
			throw new SwtException("Uncompatible paramters size");
		}

		// Make a call to apache commons lang CompareToBuilder utiltity
		Collections.sort(list, new Comparator<T>() {
			@Override
			public int compare(T lhs, T rhs) {
				CompareToBuilder builder = new CompareToBuilder();
				for(int i=0; i<includedFieldList.size(); i++){
					String field = includedFieldList.get(i);
					boolean direction = includedFieldDirection.get(i);
					for(Field f:reflectFieldsList){
						if (f.getName().equalsIgnoreCase(field)) {
							try {
								if(direction){
									builder.append(f.get(lhs), f.get(rhs));
								}else{
									builder.append(f.get(rhs), f.get(lhs));
								}
							}catch(Exception e){
								e.printStackTrace();
							}
						}
					}
				}
				return builder.toComparison();
			}
		});
	}

	/**
	 * This is a function for determining whether the specified character is the
	 * high 16 bits in a UTF-16 surrogate pair.
	 *
	 * @param ch : character to check
	 * @return true if the character is a high surrogate, false otherwise
	 */
	private static boolean  isHighSurrogate(char ch) {
		return ch >= 0xD800 && ch <= 0xDBFF;
	}



	/**
	 * This is a function for determining whether the specified character is the low 16 bits in a UTF-16 surrogate pair.
	 *
	 * @param ch : character to check
	 * @return true if the character is a low surrogate, false otherwise
	 */
	private static boolean  isLowSurrogate(char ch) {
		return ch >= 0xDC00 && ch <= 0xDFFF;
	}


	/**
	 * This is a utility function for determining whether a specified
	 * character is a character according to production 2 of the
	 * XML 1.0 specification.
	 *
	 * @param c <code>char</code> to check for XML compliance
	 * @return <code>boolean</code> true if it's a character,
	 *                                false otherwise
	 */
	public static boolean isXMLCharacter(int c) {
		if (c == '\n') return true;
		if (c == '\r') return true;
		if (c == '\t') return true;

		// do not accept the FS character, this causes problem when coloring using jasper in html markup enabled
		if (c==0x1C)
			return false;

		if (c < 0x20) return false;  if (c <= 0xD7FF) return true;
		if (c < 0xE000) return false;  if (c <= 0xFFFD) return true;
		if (c < 0x10000) return false;  if (c <= 0x10FFFF) return true;

		return false;
	}


	/**
	 * Formats the data into XML valid characters
	 * Committed code is for predicted issues
	 *
	 * @param value
	 * @return
	 * @throws SwtException
	 */
	public static String XmlFormat (String value, boolean...leftTrimData) throws SwtException{

		try{
			// log debug message
			log.debug("SwtUtil - [ XmlFormat ] - Entry");

			if (SwtUtil.isEmptyOrNull(value))
				return value;

			// if left trim string
			if(leftTrimData.length == 0 || leftTrimData[0] == true)
			{
				value = value.trim();
			}
			else{
				// Only trim the trail
				value = trimRight(value);
			}

			// Stripping out non XML characters
			final StringBuilder content = new StringBuilder();
			int i = 0;
			for (int len = value.length(); i < len; ++i)
			{
				final char ch = value.charAt(i);

				if (isHighSurrogate(ch))
				{
					++i; // make double skips
					if (i < len) {
						char low = ch;
						if (!(isLowSurrogate(low))) {
							content.append('?');
							continue;
						}
					}
					else {
						content.append('?');
						continue;
					}
				}
				if (!(isXMLCharacter(ch)))
				{
					content.append('?');
					continue; // do not append to content
				}
				content.append(ch);
			}

			return content.toString();

		}catch(Exception e){
			// log error message
			log.error("SwtUtil"
					+ " - Exception Caught in [ XmlFormat ] method : - "
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}

	/**
	 * This part of code is used to ignore reserved chars inside a regex
	 * @param regex
	 * @return
	 */
	public static String fixRegex(String regex){
		String rtn = regex;
		char[] rsv={'$','(',')','*','+','-','.','?','[',']','^','{','|','}'};
		if(regex!=null){
			for (int i=0;i<rsv.length;i++)
				rtn= rtn.replace(""+rsv[i], "\\"+rsv[i]);
		}
		return rtn;
	}
	/**
	 * Added By FMrad
	 * return default refresh rate for breakdown monitor screen
	 * @param
	 * @return
	 * @throws SwtException
	 */
	public static int geDefaultRefreshrate() throws SwtException {
		if(!SwtUtil.isEmptyOrNull(PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.DEFAULT_REFRESH_RATE_BREAKDOWN)))
			return Integer.parseInt(PropertiesFileLoader.getInstance()
					.getPropertiesValue(SwtConstants.DEFAULT_REFRESH_RATE_BREAKDOWN));
		else return 60;

	}

	/**
	 * Added By Asridi
	 * return if pcm is enabled by getting the value from the propertie file than check if the connection is valid
	 * @param
	 * @return
	 * @throws SwtException
	 */
	public static boolean getPcmEnabled() throws SwtException {
		boolean pcmEnabled = false;
		if (!SwtUtil.isEmptyOrNull(PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PCM_ENABLED))) {
			pcmEnabled = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PCM_ENABLED).equals("true")	? true : false;
			if (pcmEnabled) {
				if (pcSessionFactory != null) {
					Connection conn = null;
					org.hibernate.Session sess = null;
					try {
						sess = SwtUtil.pcSessionFactory.openSession();
						conn = SwtUtil.connection(sess);
					} catch (Exception e) {
						return false;
					}finally{
						JDBCCloser.close(null, null, conn, sess);
					}
					return true;
				} else {
					return false;
				}
			}
		}
		return pcmEnabled;

	}

	/**
	 * Added By Asridi Rebuild the PCM views using java reflexion to make sure it
	 * compiles if pcm is not enabled
	 *
	 * @param
	 * @return
	 * @throws SwtException
	 */
	public static void updatePCMViews() throws SwtException {
		// log debug message
		log.debug("SwtUtil - [ updatePCMViews ] - Entry");
		if (getPcmEnabled()) {
			// load the AppTest at runtime
			Class cls;
			try {
				cls = Class.forName("org.swallow.pcm.util.PCMUtil");
				Object obj = cls.newInstance();
				Class noparams[] = {};
				// invoque update pcm views
				Method methodStats = cls.getDeclaredMethod("updateViews", noparams);
				methodStats.invoke(obj);

			} catch (Exception e) {
				log.error(SwtUtil.class.getName()
						+ " - Exception Catched in [updatePCMViews] method : - " + e.getMessage());
			}
		}
		// log debug message
		log.debug("SwtUtil - [ updatePCMViews ] - Exit");

	}

	public static String RPad(String str, Integer length, char car) {
		return (str + String.format("%" + length + "s", "").replace(" ", String.valueOf(car))).substring(0, length);
	}

	public static String LPad(String str, Integer length, char car) {
		return (String.format("%" + length + "s", "").replace(" ", String.valueOf(car)) + str).substring(str.length(), length + str.length());
	}


	/**
	 * Scenario list of different Alert servility ordered
	 */
	public static HashMap<String, Integer> scenarioAlertServility  = new HashMap<String, Integer>() ;
	static {
		scenarioAlertServility.put(null, 0);
		scenarioAlertServility.put("N", 1);
		scenarioAlertServility.put("Y", 2);
		scenarioAlertServility.put("C", 3);

		// Override system properties from environement variables used to override DB connection settings
		if("YES".equalsIgnoreCase(System.getenv(ZkUtils.PROPERTY_ENV_DOCKER))) {
			SwtUtil.overrideSysPropertyFromEnvironement("datasource.driver");
			SwtUtil.overrideSysPropertyFromEnvironement("datasource.url");
			SwtUtil.overrideSysPropertyFromEnvironement("datasource.username");
			SwtUtil.overrideSysPropertyFromEnvironement("datasource.password");

			SwtUtil.overrideSysPropertyFromEnvironement("pcm.datasource.driver");
			SwtUtil.overrideSysPropertyFromEnvironement("pcm.datasource.url");
			SwtUtil.overrideSysPropertyFromEnvironement("pcm.datasource.username");
			SwtUtil.overrideSysPropertyFromEnvironement("pcm.datasource.password");
		}
	}

	public static String getScenarioAlertingValue(String value, String previousValue) {
		if(scenarioAlertServility.get(value) > scenarioAlertServility.get(previousValue) ) {
			return value;
		}else {
			return previousValue;
		}

	}

	public static synchronized String getMyJobList () {
		return myJobList;
	}
	public static  void setMyJobList(String jobList) {
		myJobList = jobList;
		try {
			ZkUtils.setPropertyAsyncWithTimeout(ZkUtils.PROPERTY_RUNNING_LIST_JOBS+"---"+ZkUtils.instanceUuid,myJobList,30000);
		} catch (Exception e) {
		}
	}

	public static void addJobToList(String jobId) {
		myJobList+=myJobList.length()==0?jobId:","+jobId;
		myJobList = Arrays.stream(myJobList.split(",")).distinct().collect(Collectors.joining(","));
		try {
			ZkUtils.setPropertyAsyncWithTimeout(ZkUtils.PROPERTY_RUNNING_LIST_JOBS+"---"+ZkUtils.instanceUuid,myJobList,30000);
		} catch (Exception e) {
		}
	}

	public static  void removeJobFromList(String jobId) {

		String[] filteredArray = (String[]) ArrayUtils.removeElement(myJobList.split(","), jobId);
		myJobList = String.join(",", filteredArray);
		try {
			ZkUtils.setPropertyAsyncWithTimeout(ZkUtils.PROPERTY_RUNNING_LIST_JOBS+"---"+ZkUtils.instanceUuid,myJobList,30000);
		} catch (Exception e) {
		}
	}

	public static boolean isSucessfulMFARequest(HttpServletRequest request) {
		if(!SwtUtil.isEmptyOrNull(request.getParameter("mfaLogin")) && "success".equalsIgnoreCase(request.getParameter("mfaLogin"))){
			return true;
		}else
			return false;
	}


	public static String getMFAToken(HttpSession session) {
		String mfaToken = null;
		try {
			mfaToken = ((CommonDataManager) session
					.getAttribute(SwtConstants.CDM_BEAN)).getMfatoken();
		} catch (Exception e) {
		}


		return mfaToken;
	}

	//start mantis 5785

	public static MenuItem getMenuItem(HttpServletRequest request) throws Exception {

		String roleId = null;
		User user = null;
		HashMap parametersMap = new HashMap<String, String>();
		List<MenuItem> menuItems = new ArrayList<>();

		try {
			// Get role id associated with the logged-in user
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			user = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser();
			List<MenuItem> userMenuItems = roleMenuIdsMap.get(roleId);
			LogonDAO logonDAO = (LogonDAO) SwtUtil.getBean("logonDAO");
			if (userMenuItems == null) {

				ArrayList<MenuItem> menuList = (ArrayList) logonDAO.getMenuListUpdated(user);
				roleMenuIdsMap.put(roleId, menuList);
				userMenuItems = menuList;
			}


			// get the url path (till.do)  from the request
			String urlPath = !SwtUtil.isEmptyOrNull(getPathToFilter(request)) ? getPathToFilter(request) : "";

			// First filtering by url "Path"
			menuItems = userMenuItems.stream().filter(item -> !SwtUtil.isEmptyOrNull(item.getProgramName()))
					.filter(item -> item.getProgramName().startsWith(urlPath)).collect(Collectors.toList());



			ArrayList<String> requestParameters = new ArrayList<String>();
			Enumeration params2 = request.getParameterNames();
			while(params2.hasMoreElements()){
				String paramName = (String)params2.nextElement();
				requestParameters.add(paramName+"="+request.getParameter(paramName));
			}

			boolean foundWithParams = false;
			MenuItem menuItemToFind = null;
			int h = 0;
			while( h < menuItems.size() && foundWithParams == false) {
				menuItemToFind = menuItems.get(h);
				if(menuItemToFind.getProgramName() != null && menuItemToFind.getProgramName().startsWith(SwtUtil.getPathToFilter(request))){
					if(menuItemToFind.getProgramName().indexOf("?") != -1) {
						String parametersStr = menuItemToFind.getProgramName().split("\\?")[1];
						if(parametersStr!=null && parametersStr.length()>0){
							String[] list = parametersStr.split("&");
							foundWithParams = true;
							for(int j = 0; j < list.length ; j++){
								if(requestParameters.indexOf(list[j]) == -1) {
									foundWithParams = false;
								}
							}
							if(foundWithParams) {
								break;
							}
						}
					}
				}
				h++;
			}
			if(!foundWithParams) {
				h = 0;
				boolean foundWithoutPrams = false;
				while( h < menuItems.size() && foundWithoutPrams == false) {
					menuItemToFind = menuItems.get(h);
					if(menuItemToFind.getProgramName() != null && menuItemToFind.getProgramName().startsWith(SwtUtil.getPathToFilter(request))){
						if(menuItemToFind.getProgramName().indexOf("?") == -1) {
							foundWithoutPrams = true;
						}
					}
					h++;
				}
			}

			return menuItemToFind;

		} catch (Exception e) {
			e.printStackTrace();
			return null;
			// throw new Exception("Error extracting query from config file acllist.xml:
			// "+e.getMessage(), e);
		}
	}


	public static HashMap extratUrlParams(String url) {
		HashMap<String, String> parametersMap = new HashMap<String, String>();
		String[] params = null;
		try {
			// url="systemlog.do?menuAccessId=1&ismenuItem=true&user_lang1234=EN";
			String urlPath = url.split("\\?")[0];
			parametersMap.put("urlPath", urlPath);
			params = url.split("\\?").length > 1 ? (url.split("\\?")[1]).split("&") : null;

			if (params != null) {
				for (int i = 0; i < params.length; i++) {
					String paramName = params[i].split("=")[0];
					String paramValue = params[i].split("=")[1];
					parametersMap.put(paramName, paramValue);
				}
			}
		} catch (Exception e) {
		}
		return parametersMap;
	}



	public static HashMap extratUrlParameters(String url) {
		HashMap<String, String> parametersMap = new HashMap<String, String>();
		String[] params = null;
		Pattern pattern = Pattern.compile("\\[(.*?)\\]");
		try {
			params = url.split(",").length > 0 ? url.split(",") : null;
			if (params != null) {
				for (int i = 0; i < params.length; i++) {
					String paramName = params[i].split("=")[0];

					Matcher matcher = pattern.matcher(params[i].split("=")[1]);
					if( matcher.find() ) {
						String paramValue = matcher.group(1);
						parametersMap.put(paramName, paramValue);
					}
				}
			}
		} catch (Exception e) {
		}

		return parametersMap;
	}
	/**
	 * Method used to set cookie value based on a cookie name and http servelet response
	 */
	public static void setCookieValueByName(HttpServletResponse response, String cookieName, String value ) {
		ArrayList<String> paths = new ArrayList<String>();

		if(isEmptyOrNull(value)) {
			paths.add("/");
			paths.add("/swallowtech");
			paths.add("/swallowtech/style");
			paths.add("/swallowtech/images/Alert");
			paths.add("/swallowtech/angularSources");
			paths.add("/swallowtech/images");
			paths.add("/swallowtech/js");
			paths.add("/swallowtech/images/menu");
			for (int i = 0; i < paths.size(); i++) {
				Cookie uiColorCookie = new Cookie(cookieName, value);
				uiColorCookie.setSecure(true);
				uiColorCookie.setMaxAge(0);
				uiColorCookie.setPath(paths.get(i));
				uiColorCookie.setHttpOnly(true);
				response.addCookie(uiColorCookie);
			}
		}
		else {
			Cookie uiColorCookie = new Cookie(cookieName, value);
			uiColorCookie.setSecure(true);
			uiColorCookie.setPath("/swallowtech");
			uiColorCookie.setHttpOnly(true);
			response.addCookie(uiColorCookie);
		}
	}

	public static String removeAllNonAlphaNumeric(String s) {
		if (s == null) {
			return null;
		}
		return s.replaceAll("[^A-Za-z0-9]", "");
	}

	//end start mantis 5785

	/**
	 * Gets a system property either using -D, being provided in config files or as Env variable
	 *
	 * @param key
	 */
	public static void overrideSysPropertyFromEnvironement(String key) {
		try {
			// Passed as -Dproperty=value
			if(!SwtUtil.isEmptyOrNull(System.getProperty(key))) {
				return;
			}

			// Environement variable
			if(!SwtUtil.isEmptyOrNull(System.getenv(key))) {
				System.setProperty(key, System.getenv(key));
				return;
			}
			if(!SwtUtil.isEmptyOrNull(System.getenv(key.replace(".", "_")))) {
				System.setProperty(key, System.getenv(key.replace(".", "_")));
				return;
			}
			if(!SwtUtil.isEmptyOrNull(System.getenv(key.replace(".", "_").toUpperCase()))) {
				System.setProperty(key, System.getenv(key.replace(".", "_").toUpperCase()));
				return;
			}
		} catch (Exception e) {
			log.error("SwtUtil - [overrideSysPropertyFromEnvironement] error with parameter '"+key+"', cause: "+e.getMessage());
		}
	}

	/**
	 * Override properties entry with values read from Env variable or -D jaav system property
	 *
	 * @param props
	 */
	public static void overridePropertiesFromEnvironement(Properties props) {
		props.keySet().stream().map(String::valueOf).forEach(key ->{
			try {
				// Passed as -Dproperty=value
				if(!SwtUtil.isEmptyOrNull(System.getProperty(key))) {
					props.setProperty(key, System.getProperty(key));
					return;
				}

				// Environement variable
				if(!SwtUtil.isEmptyOrNull(System.getenv(key))) {
					props.setProperty(key, System.getenv(key));
					return;
				}
				if(!SwtUtil.isEmptyOrNull(System.getenv(key.replace(".", "_")))) {
					props.setProperty(key, System.getenv(key.replace(".", "_")));
					return;
				}
				if(!SwtUtil.isEmptyOrNull(System.getenv(key.replace(".", "_").toUpperCase()))) {
					props.setProperty(key, System.getenv(key.replace(".", "_").toUpperCase()));
					return;
				}
			} catch (Exception e) {
				log.error("SwtUtil - [overridePropertiesFromEnvironement] error with parameter '"+key+"', cause: "+e.getMessage());
			}
		});
	}
	public static String getNotificationMessage(HttpSession session) {
		StringBuilder builder = new StringBuilder();
		Date lastLoginDate = null;
		Date lastFailedLoginDate = null;
		String lastFailedLoginDateAsString = null;
		Integer lastLoginAttempts = null;
		User user = null;
		try {
			if (session.getAttribute(SwtConstants.CDM_BEAN) != null) {
				user =  ((CommonDataManager) session
						.getAttribute(SwtConstants.CDM_BEAN)).getUser();
				lastLoginDate = ((CommonDataManager) session
						.getAttribute(SwtConstants.CDM_BEAN)).getLastLoginDateBeforeThisAttempt();
				if(lastLoginDate != null) {

					String lastLoginAsString = formatDate(lastLoginDate,
							getCurrentSystemFormats(session)
									.getDateFormatValue()+ " HH:mm:ss");


					builder.append("<table cellpadding=\"0\" style=\"color: white;\" cellspacing=\"0\" border=\"0\">"
							+ "	<tr><td style=\"width:140px\">"+getMessageFromSession("login.notification.lastSuccessfulLoginTime",session)+"</td><td>"+lastLoginAsString+"</td></tr>");

					lastLoginAttempts = ((CommonDataManager) session
							.getAttribute(SwtConstants.CDM_BEAN)).getLastLoginAttemptsBeforeThis();

					if( lastLoginAttempts != null && lastLoginAttempts >0 ) {
						lastFailedLoginDate = user.getLastLoginFailed();

						if(lastFailedLoginDate != null) {
							lastFailedLoginDateAsString =  formatDate(lastFailedLoginDate,
									getCurrentSystemFormats(session)
											.getDateFormatValue()+ " HH:mm:ss");;
							builder.append("<tr><td>"+getMessageFromSession("login.notification.lastFailedLogin",session)+"</td><td>"+lastFailedLoginDateAsString+"</td></tr>");
						}
						builder.append("<tr><td >"+getMessageFromSession("login.notification.failedLoginAttempts",session)+"</td><td>"+lastLoginAttempts+"</td></tr>");
						if(!SwtUtil.isEmptyOrNull(user.getLastLoginFailedIp())) {
							builder.append("<tr><td >"+getMessageFromSession("login.notification.lastFailedLoginIp",session)+"</td><td>"+user.getLastLoginFailedIp()+"</td></tr>");
						}
					}
					builder.append("</table>");

				}
				return builder.toString();
			} else {
				return null;
			}
		} catch (Exception e) {
			log.error("Error on getCurrentUser, cause: "+e.getMessage(), e);
			return null;
		}
	}

	public static boolean compareObjects(Object obj1, Object obj2) {
		if (obj1 == null && obj2 == null) {
			return true;
		}
		if (obj1 == null || obj2 == null) {
			return false;
		}
		if (!obj1.getClass().equals(obj2.getClass())) {
			return false;
		}
		try {
			for (Field field : obj1.getClass().getDeclaredFields()) {
				field.setAccessible(true);
				Object value1 = field.get(obj1);
				Object value2 = field.get(obj2);
				if (value1 == null && value2 == null) {
					continue;
				}
				if (value1 == null || value2 == null) {
					return false;
				}
				if (!value1.getClass().equals(value2.getClass())) {
					return false;
				}
				if (value1 instanceof Comparable) {
					if (!(((Comparable) value1).compareTo(value2) == 0)) {
						return false;
					}
				} else if (value1.getClass().getDeclaredFields().length > 0) {
					if (!compareObjects(value1, value2)) {
						return false;
					}
				}
			}
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		return true;
	}

	private int compare(Object o1, Object o2, int level) {
		if (level >= 2) {
			return 0;
		}
		int result = 0;
		Class<?> clazz1 = o1.getClass();
		Class<?> clazz2 = o2.getClass();
		Field[] fields1 = clazz1.getDeclaredFields();
		Field[] fields2 = clazz2.getDeclaredFields();
		for (Field f1 : fields1) {
			f1.setAccessible(true);
			for (Field f2 : fields2) {
				if (f1.getName().equals(f2.getName())) {
					f2.setAccessible(true);
					try {
						Object value1 = f1.get(o1);
						Object value2 = f2.get(o2);
						if (!value1.equals(value2)) {
							result = compare(value1, value2, level + 1);
						}
					} catch (IllegalAccessException e) {
						e.printStackTrace();
					}
				}
			}
		}
		return result;
	}

	public static String getEntityOffsetTime(String hostId, String entityId)	throws SwtException {
		// System Date
		String offset = null;
		try {
			SweepDetailDAO sweepDetailDAO = (SweepDetailDAO) SwtUtil.getBean("sweepDetailDAO");
			// get the entity offset time
			offset = sweepDetailDAO.getOffsetTime(entityId, hostId);
		} catch (Exception ex) {
			log
					.error("Exception Catched in SwtUtil - [getEntityOffsetTime] "
							+ ex.getMessage());
			throw new SwtException(ex.getMessage());
		}
		return offset;
	}

	/**
	 * Cleans XSS Reflected possible intrusions
	 * @param paramString
	 * @return
	 */
	public static String cleanXSSReflected(String paramString) {
		String str = null;
		try{
			if (paramString == null) {
				return null;
			}
			str = paramString;

			//str = str.replaceAll("\000", "");
			str = str.replaceAll("\\(", "\\\\u0028").replaceAll("\\)", "\\\\u0029");
			str = str.replaceAll("'", "\\\\u0027").replaceAll("\"", "\\\\u0022");
			str = str.replaceAll("<", "\\\\u003C").replaceAll(">", "\\\\u003E");
			//str = str.replaceAll(";", "\\\\u003B");

			return str;
		}catch(Exception e) {
		}
		return str;
	}
	/**
	 * Returns a connection from the session
	 * @param session
	 * @return
	 * @throws SQLException
	 */
	public static Connection connection(org.hibernate.Session session) throws SQLException{
		boolean isConnectionCanBeOpened = ((SwtDataSource) SwtDataSource.getInstance()).checkIfConnectionCanBeCreated();
		if(!isConnectionCanBeOpened)
			return null;
		final Single<Connection> conn = Single.of(null);
		session.doWork(connection -> conn.setFirst(connection));

		// Preserve the old behaviour as autocommit=false
		conn.getFirst().setAutoCommit(false);

		Connection toRetun = conn.getFirst();

		try {
			toRetun = ((SwtDataSource) SwtDataSource.getInstance()).updateConnectionCount(toRetun);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return toRetun;
	}

    /**
     * Gets a system property
     * 
     * @param key
     * @param defValue
     * @return
     */
    public static String getSystemProperty(String key, String defValue) {
        try {
            // As env variable
            if(!isEmptyOrNull(System.getenv(key))) {
                return System.getenv(key);
            }
            
            // Passed as -Dproperty=value
            if(!isEmptyOrNull(System.getProperty(key))) {
                return System.getProperty(key);
            }
            
            // if docker is enabled, then read from env variables
            if("YES".equalsIgnoreCase(System.getenv(ZkUtils.PROPERTY_ENV_DOCKER))) {
                // Environement variable
                if(!isEmptyOrNull(System.getenv(key))) {
                    return System.getenv(key);
                }
                if(!isEmptyOrNull(System.getenv(key.replace(".", "_")))) {
                    return System.getenv(key.replace(".", "_"));
                }
                if(!isEmptyOrNull(System.getenv(key.replace(".", "_").toUpperCase()))) {
                    return System.getenv(key.replace(".", "_").toUpperCase());
                }
            }
            // In table .properties file
            if(!isEmptyOrNull(PropertiesFileLoader.getInstance().getPropertiesValue(key))) {
                return PropertiesFileLoader.getInstance().getPropertiesValue(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(key.contains("_")) {
            String valueKeyLower = getSystemProperty(key.replace("_", ".").toLowerCase(), null);
            return valueKeyLower != null ?valueKeyLower: defValue;
        }
        return defValue;
    }

    /**
     * Is Test environement enabled ?
     * @return
     */
    private static Boolean envTestEnabledCached = null;
    public static boolean envTestEnabled() {
        if(envTestEnabledCached != null) {
            return envTestEnabledCached;
        }
        String envTestStr = SwtUtil.getSystemProperty(SwtConstants.PROPERTY_ENV_TEST_ENABLED, "NO");
        envTestEnabledCached = "YES".equalsIgnoreCase(envTestStr) || "TRUE".equalsIgnoreCase(envTestStr) || "Y".equalsIgnoreCase(envTestStr);
        return envTestEnabledCached;
    }

	private static class SystemDateHolder {
		private static final SystemDateHolder INSTANCE = new SystemDateHolder();
		private volatile Date sysDbDateAsDate;
		private volatile long lastTimeSysDb;

		private SystemDateHolder() {
			updateSystemDate();
		}

		public Date getSystemDate() {
			long currentTime = System.currentTimeMillis();

			// Check if cache needs refresh (every second)
			if (sysDbDateAsDate == null || (currentTime - lastTimeSysDb > 1000)) {
				updateSystemDate();
			}

			return sysDbDateAsDate != null ? sysDbDateAsDate : new Date();
		}

		private void updateSystemDate() {
			try {
				Date fetchedDate = SwtUtil.executeSelectQuery("select GLOBAL_VAR.SYS_DATE from dual")
						.stream()
						.findFirst()
						.map(item -> (Date) item.get("sys_date"))
						.orElse(new Date());

				// Atomic update
				sysDbDateAsDate = fetchedDate;
				lastTimeSysDb = System.currentTimeMillis();
			} catch (Exception e) {
				// Log the error or handle it as needed
				sysDbDateAsDate = new Date();
				lastTimeSysDb = System.currentTimeMillis();
			}
		}
	}

}