package org.swallow.util;

/**
 * A Single of things.
 * 
 * <AUTHOR>
 *
 * @param <S>
 */
public final class Single<S> {

	private  S first;

	public Single(S first) {
		this.first = first;
	}

	/**
	 * Creates a new {@link Single} for the given elements.
	 *
	 * @param first must not be {@literal null}.
	 * @return
	 */
	public static <S> Single<S> of(S first) {
		return new Single<S>(first);
	}

	/**
	 * Returns the first element of the {@link Single}.
	 *
	 * @return
	 */
	public S getFirst() {
		return first;
	}


	/**
	 * Sets the first element of the {@link Single}.
	 *
	 * @return
	 */
	public void setFirst(S first) {
		this.first = first;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("Pair [first=");
		builder.append(first);
		builder.append("]");
		return builder.toString();
	}
}
