package org.swallow.util;

public class SwtRecipient {
	private String recipientName;
	private String recipientEmail;
	private String recipientRole;
	private String recipientLang;

	
	public SwtRecipient() {
		super();
	}
	public SwtRecipient(String recipientName, String recipientEmail,
			String recipientRole, String recipientLang) {
		super();
		this.recipientName = recipientName;
		this.recipientEmail = recipientEmail;
		this.recipientRole = recipientRole;
		this.recipientLang = recipientLang;
	}
	public String getRecipientName() {
		return recipientName;
	}
	public void setRecipientName(String recipientName) {
		this.recipientName = recipientName;
	}
	public String getRecipientEmail() {
		return recipientEmail;
	}
	public void setRecipientEmail(String recipientEmail) {
		this.recipientEmail = recipientEmail;
	}
	public String getRecipientLang() {
		return recipientLang;
	}
	public void setRecipientLang(String recipientLang) {
		this.recipientLang = recipientLang;
	}
	public String getRecipientRole() {
		return recipientRole;
	}
	public void setRecipientRole(String recipientRole) {
		this.recipientRole = recipientRole;
	}
	
	
	
}
