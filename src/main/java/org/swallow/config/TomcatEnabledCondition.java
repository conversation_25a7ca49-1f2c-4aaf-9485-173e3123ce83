package org.swallow.config;

import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class TomcatEnabledCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        // By default PROPERTY_ENV_TOMCAT_ENABLED = TRUE from Eclipse
        String tomcatEnabledstr = SwtUtil.getSystemProperty(SwtConstants.PROPERTY_ENV_TOMCAT_ENABLED, "true");
        return "YES".equalsIgnoreCase(tomcatEnabledstr) || "TRUE".equalsIgnoreCase(tomcatEnabledstr) || "Y".equalsIgnoreCase(tomcatEnabledstr);
    }
}
