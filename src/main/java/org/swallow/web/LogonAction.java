/*
 * @(#)LogonAction.java
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.web;

import java.io.IOException;
import java.net.Inet4Address;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.cluster.RegisterZookeeper;
import org.swallow.cluster.ZkUtils;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Password;
import org.swallow.control.model.PasswordHistory;
import org.swallow.control.model.Role;
import org.swallow.control.model.SystemLog;
import org.swallow.control.model.UserAuthDetails;
import org.swallow.control.model.UserStatus;
import org.swallow.control.service.ChangePasswordManager;
import org.swallow.control.service.RoleManager;
import org.swallow.control.service.SystemLogManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.exception.SamlError;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SystemExceptionHandler;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.mfa.RegisterMFA;
import org.swallow.mfa.SamlService;
import org.swallow.model.SamlUserDTO;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.service.UserService;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SequenceFactory;
import org.swallow.util.SessionManager;
import org.swallow.util.StlHash;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtRadiusChecker;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;

import org.swallow.work.model.Sweep;
import org.swallow.work.service.MovementLockManager;

import com.google.gson.Gson;

import net.jradius.exception.RadiusException;


/**
 * This action class deals with the license validation and root through into
 * application.
 *
 */


import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/logon", "/logon.do"})
public class LogonAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("preloginscreen", "prelogin");
		viewMap.put("fail", "login");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "main");
		viewMap.put("error", "fault");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	/*
	 * Instance of LogonManger that contains the business logic for user related
	 * activities
	 */
	@Autowired
	private LogonManager logonMngr = null;


	@Autowired
	UserService userService ;

	/* Instance of Logger object */
	private final Log logger = LogFactory.getLog(LogonAction.class);
	/* Instance of license string */
	private static String license;

	User user;
	public User getUser() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		user = RequestObjectMapper.getObjectFromRequest(User.class, request);
		return user;
	}
	public void setUser(User user) {
		this.user = user;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("user", user);
	}


	/**
	 * The setLogonManager function is used to set logonmanager to local
	 * variable mgr
	 *
	 * @param logonManager
	 *            reference. return void throws Exception
	 */
	public void setLogonManager(LogonManager logonManager) {
		logger.debug("Enter into LogonAction.setLogonManager method");
		this.logonMngr = logonManager;
		logger.debug("Exit from LogonAction.setLogonManager method");
	}

	/**
	 * The reLogin function is called when user create/update shortcut and want
	 * changes to reflect in current session.
	 *
	 * @param ActionMapping
	 *            reference.
	 * @param ActionForm
	 *            reference.
	 * @param HttpServletRequest
	 *            reference.
	 * @param HttpServletResponse
	 *            reference. return ActionForward object throws Exception
	 */
	public String reLogin()
			throws SwtException {
		logger.debug("Enter into LogonAction.relogin method");
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		CommonDataManager CDM = new CommonDataManager();
		/* Getting CDM reference from session */
		CDM = (CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN);
		LogonBean logBean = new LogonBean();
		request.getSession().setAttribute("afterLogon", "Y");
		/*
		 * Processing all event required after verifying user (Not Re-verifying
		 * the user again)
		 */
		logBean.afterLogonProcess(request, CDM.getUser(), logonMngr);
		logger.debug("Exit from LogonAction.relogin method");
		return getView("success");
	}

	/**
	 * This method is called when user provides the user name and password and
	 * hit the screen to login in to the application. *
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward object
	 * @throws Exception
	 */
	public String login()
			throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		// Sets the password change flag
		boolean passWordChangedFrmMenuOption = false;
		// Declares the login processing flag
		boolean loginProcessFlag = false;
		// Initializes the expire day
		int expireDays = 0;
		// Initializes the new user value
		int isNewUser = 0;
		// Initializes the database date
		long dbDays = 0l;
		// Initializes the today date value
		long todayDays = 0l;
		// Initializes the difference in days
		long daysDiff = 0l;
		// Initializes the days notice
		long daysNotice = 0l;
		// Declares the maximum user
		String maxUser = null;
		// Declares the Host Id
		String hostId = null;
		// Declares the User Id
		String userId = null;
		// Declares the role id
		String roleId = null;
		// Declares the license host name
		String licenseHostName = null;
		// Declares the Object
		Object obj = null;
		// Declares the ArrayList
		ArrayList arr = null;
		// Declares the Collection object
		Collection coll = null;
		// Declares the user preference list
		List userPreferenceList = null;
		// Declares the Calendar object
		Calendar lastPassCngCalendar = null;
		// Declares the test date calendar
		Calendar testDateCalendar = null;
		// Declares the Date when the password got changed recently
		Date lastPassCngDate = null;
		// Declares ActionMessages object
		ActionMessages errors = null;
		// Declares CommonDataManager objects
		CommonDataManager commonDataMngr = null;
		// Declares User objects
		User userdb = null;
		// Declares the Role Object
		Role role = null;
		// Declares the Role object
		Role userRole = null;
		// Declares the Collection of Role
		Collection<Role> collRole = null;
		// Declares the MovementLockManager object
		MovementLockManager mlmgr = null;
		// Declares the Config object
		Config utilsInst = null;
		// Declares the User object
		User user = null;
		String screenPassword = null;
		String dbPassword = null;
		Password pwdRules = null;
		LogonBean logBean = null;
		SysParamsManager sysParamsManager = null;
		SysParams sysParams = null;
		boolean challengeMode =false;
		Boolean dfaEnabled    = false;
		String mfaToken = "";
		// Added for mantis 1650: to retrieve the list of sessions in map
		ConcurrentHashMap<String, Object> sessionMap;
		boolean isMFARequest = false;
		String clearPassword = ""; // This variable must be cleared at the end of authentification process and should not stored anywhere
		Date lastLoginDateBeforeThisAttempt  = null;
		Integer lastLoginAttemptsBeforeThis = null;


		try {
			logger.debug("Enter into LogonAction.login method");
			// Creates a new instance of ActionMessages object
			errors = new ActionMessages();
			// Creates a new instance of CommonDataManager object
			commonDataMngr = new CommonDataManager();
			// Sets the login process flag to true
			loginProcessFlag = true;
			/*
			 * Start:code added by venkat for mantis 1267:"License programs and
			 * application checking of dates must be consistent" on 3-Dec.
			 */
			maxUser = new String();
			// Gets an instance of Config object
			utilsInst = Config.getInstance();
			licenseHostName = new String();
			// Code modified for Mantis1354: Check of Code in
			// license.properties file should not be case sensitive - by Marshal
			// on 21-Mar-2011
			// Gets the maximum users from Config
			maxUser = utilsInst.get("Users").trim();
			/*
			 * End:code added by venkat for mantis 1267:"License programs and
			 * application checking of dates must be consistent" on 3-Dec.
			 */

			/*
			 * logonFlag is parameter that is used to prevent the relogin while
			 * handling login process in application flows. Do login if this
			 * flag is available otherwise do nothing.
			 */
			if (request.getParameter("logonflag") == null) {

				if(SwtUtil.isSucessfulMFARequest(request)){
					isMFARequest = true;
					boolean isUserDetailsChanged = false;
					boolean isNewCreatedUser = false;
					String generateduserId = null;
					user = null;
					UserMaintenanceManager usermaintenanceManager = null;
					// validate the token
					mfaToken = request.getParameter("auth");
					SamlService samlService = new SamlService();
					SamlUserDTO verifyResult = samlService.verifySamlToken(mfaToken);
					if(verifyResult == null) {
						errors.add("invalidUser", new ActionMessage(
								"errors.logon.verify.exception"));
						// Calling the method to save the error messages
						saveErrors(request, errors);
						return getView("preloginscreen");
					}
					usermaintenanceManager = (UserMaintenanceManager) SwtUtil.getBean("usermaintenanceManager");
					//PredictUserID
					if(!SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SMART_USER_ID"))) {
						user = logonMngr.getUserDetailByExtAuthId(SwtUtil.getCurrentHostId(), verifyResult.getAttributeValueByName("SMART_USER_ID"));

					}else {

						logger.debug("Invalid login via MFA: No mapping for User");
						errors.add("invalidRole", new ActionMessage(
								"errors.logon.verify.smartUserId"));
						// Calling the method to save the error messages
						saveErrors(request, errors);

						SystemLogManager systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
						SystemLog systemLog = new SystemLog();
						systemLog.setHostId(SwtUtil.getCurrentHostId());
						systemLog.setUserId("SYSTEM");
						systemLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
						systemLog.setProcess("Invalid login via MFA: No mapping for User");
						systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
						systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
						systemLog.setUpdateUser("SYSTEM");
						systemLogManager.saveSystemLog(systemLog);

						return getView("fail");
					}

					/*	Data from AD that are NOT allowed to override those in Predict:
						����? LANG=> On new user creation, default to 'EN' if not mapped from Saml (advised to be mapped)
						����? CURRENT_ENTITY => On new user creation, if not mapped, use the first entity in role entity access (table S_ENTITY_ACCESS). Advised to be mapped to provide correct value
						����? CURRENT_CURRENCY_GROUP_ID => On new user creation, use domestic currency for entity previously defined (table S_ENTITY)
						����? AMOUNT_DELIMITER => On new user creation, if not mapped, default to sysparamsdelimiter, (table S_SYSTEM_PARAMETERS)
						����? DATE_FORMAT => On new user creation, if not mapped, default to sysparams date format(table S_SYSTEM_PARAMETERS)
						����
						Data from AD that are allowed to override those in Predict
						����? FIRST_NAME => Must be mapped from Saml and will be used as user_name in table s_users,column user_name must not be null
						����? SMART_ROLE_ID => Must be mapped from Saml, no default value, must not be null
						����? SECTION_ID => default to '' if not mapped, null values are accepted in table s_users
						����? PHONE_NUMBER => default to '' if not mapped, null values are accepted in table s_users
						����? EMAIL_ADDRESS => default to '' if not mapped, null values are accepted in table s_users
						����? STATUS => '1' if not mapped from Saml => always active user => So better to be mapped
			 		*/

					if(user == null) {


						if(!RegisterMFA.getInstance().isAllowUserCreationWithSmartAuthenticator() && RegisterMFA.getInstance().isUseSmartAuthenticator() ) {
							/*
							 * Error Condition if user is logged off due to
							 * inactivity i.e. user is suspended
							 */
							errors.add("userCreationDisabled", new ActionMessage(
									"errors.logon.userCreationDisabled"));
							saveErrors(request, errors);
							return getView("fail");
						}

						isNewCreatedUser = true;
						isUserDetailsChanged = true;
						Long userIdSeq = SequenceFactory.getSequenceFromDbAsLong("SEQ_USER_ID_ADFS");
						if(verifyResult.getUsername() != null && verifyResult.getUsername().length()>10) {
							generateduserId = verifyResult.getUsername().substring(0, 10)+userIdSeq;
						}else {
							generateduserId = verifyResult.getUsername()+userIdSeq;
						}
						generateduserId = SwtUtil.removeAllNonAlphaNumeric(generateduserId) ;
						user = new User();
						/*
						 * Setting the host id,user id,user name,section is and language
						 * using bean class
						 */


						user.getId().setHostId(SwtUtil.getCurrentHostId());
						user.getId().setUserId(generateduserId);
						if(!SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SMART_USER_ID"))){
							user.setExtAuthId(verifyResult.getAttributeValueByName("SMART_USER_ID"));
						}


					}

					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("FIRST_NAME"))){
						if(SwtUtil.isEmptyOrNull(user.getUserName())) {
							user.setUserName(generateduserId);
							isUserDetailsChanged = true;
						}
					}else {
						user.setUserName(verifyResult.getAttributeValueByName("FIRST_NAME"));
						isUserDetailsChanged = true;
					}

					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SECTION"))){
						if(SwtUtil.isEmptyOrNull(user.getSectionId())) {
							user.setSectionId("");
						}
					}else {
						user.setSectionId(verifyResult.getAttributeValueByName("SECTION"));
						isUserDetailsChanged = true;
					}



//						/*
//						 * Setting the phone number,email id,role id and password using bean
//						 * class
//						 */
//						user.setPhoneNumber(verifyResult.getAttributeValueByName("PHONE_NUMBER"));

					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("PHONE_NUMBER"))){
						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("PHONE"))){
							if(SwtUtil.isEmptyOrNull(user.getPhoneNumber()))
								user.setPhoneNumber("");
						}else {
							user.setPhoneNumber(verifyResult.getAttributeValueByName("PHONE"));
							isUserDetailsChanged = true;
						}
					}else {
						user.setPhoneNumber(verifyResult.getAttributeValueByName("PHONE_NUMBER"));
						isUserDetailsChanged = true;
					}


					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("EMAIL_ADDRESS"))){
						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("EMAIL"))){
							if(SwtUtil.isEmptyOrNull(user.getEmailId()))
								user.setEmailId("");
						}else {
							user.setEmailId(verifyResult.getAttributeValueByName("EMAIL"));
							isUserDetailsChanged = true;
						}
					}else {
						user.setEmailId(verifyResult.getAttributeValueByName("EMAIL_ADDRESS"));
						isUserDetailsChanged = true;
					}



					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SMART_ROLE_ID"))){

						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("ROLE"))){
							if(SwtUtil.isEmptyOrNull(user.getRoleId()))
								user.setRoleId("");

							logger.debug("Invalid login via MFA: No mapping for Role");
							errors.add("invalidRole", new ActionMessage(
									"errors.logon.verify.smartUserId"));
							// Calling the method to save the error messages
							saveErrors(request, errors);

							SystemLogManager systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
							SystemLog systemLog = new SystemLog();
							systemLog.setHostId(SwtUtil.getCurrentHostId());
							systemLog.setUserId("SYSTEM");
							systemLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
							systemLog.setProcess("Invalid login via MFA: No mapping for Role");
							systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateUser("SYSTEM");
							systemLogManager.saveSystemLog(systemLog);


							return getView("fail");
						}else {
							user.setRoleId(verifyResult.getAttributeValueByName("ROLE"));
							RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
							Role roleTmp = new Role();
							roleTmp.setRoleId(user.getRoleId());
							Collection<Role> collection =  roleManager.getRoleDetails(roleTmp);
							if(collection.isEmpty())
							{
								logger.debug("Invalid login via MFA: Not a valid Role");
								errors.add("invalidRole", new ActionMessage(
										"errors.logon.verify.smartRoleId"));
								// Calling the method to save the error messages
								saveErrors(request, errors);

								SystemLogManager systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
								SystemLog systemLog = new SystemLog();
								systemLog.setHostId(SwtUtil.getCurrentHostId());
								systemLog.setUserId("SYSTEM");
								systemLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
								systemLog.setProcess("Invalid login via MFA: No linked Role was found in the application");
								systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
								systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
								systemLog.setUpdateUser("SYSTEM");
								systemLogManager.saveSystemLog(systemLog);


								return getView("fail");
							}
							isUserDetailsChanged = true;
						}
					}else {
						user.setRoleId(verifyResult.getAttributeValueByName("SMART_ROLE_ID"));

						RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
						Role roleTmp = new Role();
						roleTmp.setRoleId(user.getRoleId());
						Collection<Role> collection =  roleManager.getRoleDetails(roleTmp);
						if(collection.isEmpty())
						{
							logger.debug("Invalid login via MFA: Not a valid Role");
							errors.add("invalidRole", new ActionMessage(
									"errors.logon.verify.smartRoleId"));
							// Calling the method to save the error messages
							saveErrors(request, errors);

							SystemLogManager systemLogManager = (SystemLogManager) SwtUtil.getBean("systemLogManager");
							SystemLog systemLog = new SystemLog();
							systemLog.setHostId(SwtUtil.getCurrentHostId());
							systemLog.setUserId("SYSTEM");
							systemLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());
							systemLog.setProcess("Invalid login via MFA: No linked Role was found in the application");
							systemLog.setLogDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateDate(SwtUtil.getSystemDatewithTime());
							systemLog.setUpdateUser("SYSTEM");
							systemLogManager.saveSystemLog(systemLog);


							return getView("fail");
						}
						isUserDetailsChanged = true;

					}

					//user.setStatus("1");

					if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("STATUS"))){
						if(SwtUtil.isEmptyOrNull(user.getStatus())) {
							user.setStatus("1");
						}else {
							if(!user.getStatus().equals("")
							   && (Integer.valueOf(user.getStatus())
										   .intValue() != 1)) {
								if (user.isInactiveDisable() == true) {
									/*
									 * Error Condition if user is logged off due to
									 * inactivity i.e. user is suspended
									 */
									errors.add("disableUser", new ActionMessage(
											"errors.logon.inactiveDisable"));
								} else {
									errors.add("disableUser", new ActionMessage(
											"errors.logon.disableUser"));
								}
								saveErrors(request, errors);
								return getView("fail");
							}
						}
					}else {
						user.setStatus(verifyResult.getAttributeValueByName("STATUS"));
						isUserDetailsChanged = true;
					}
//
					user.setMfaUser(true);

					if(SwtUtil.isEmptyOrNull(user.getPassword())) {
						user.setPassword("DUMMYPassWord");
						user.setPasswordChangeDate(SwtUtil.getSystemDatewithTime());
						user.setInvPassAttempt(new Integer(0));

					}

					if(isNewCreatedUser) {
						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("LANG"))){
							if(SwtUtil.isEmptyOrNull(user.getLanguage()))
								user.setLanguage("EN");
						}else {
							user.setLanguage(verifyResult.getAttributeValueByName("LANG"));
							isUserDetailsChanged = true;

						}
						//						/*
						//						 * Setting current entity,password change date,invalid password
						//						 * attempt using bean class
						//						 */

						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("CURRENT_ENTITY"))){
							if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("ENTITY"))){

								if(SwtUtil.isEmptyOrNull(user.getCurrentEntity())) {
									Collection<EntityUserAccess> list = SwtUtil.getRoleEntityAccessList(request.getSession(),user.getRoleId());
									if(!list.isEmpty()) {
										user.setCurrentEntity(list.iterator().next().getEntityId());
									}
								}
							}else {
								user.setCurrentEntity(verifyResult.getAttributeValueByName("ENTITY"));
								isUserDetailsChanged = true;
							}
						}else {
							user.setCurrentEntity(verifyResult.getAttributeValueByName("CURRENT_ENTITY"));
							isUserDetailsChanged = true;
						}





						//						;
						//						/* Setting currency group id using bean class */
						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("CURRENT_CURRENCY_GROUP_ID"))){
							if(SwtUtil.isEmptyOrNull(user.getCurrentCcyGrpId())) {
								String ccy = SwtUtil.getDomesticCurrencyForEntity(SwtUtil.getCurrentHostId(), user.getCurrentEntity());
								user.setCurrentCcyGrpId(SwtUtil.getCurrencyGroup(user.getCurrentEntity(), ccy));
							}
						}else {
							user.setCurrentCcyGrpId(verifyResult.getAttributeValueByName("CURRENT_CURRENCY_GROUP_ID"));
							isUserDetailsChanged = true;
						}



						sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
						sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());


						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("AMOUNT_DELIMITER"))){
							if(SwtUtil.isEmptyOrNull(user.getAmountDelimiter())) {
								user.setAmountDelimiter(sysParams.getAmountDelimiter());
							}
						}else {
							user.setAmountDelimiter(verifyResult.getAttributeValueByName("AMOUNT_DELIMITER"));
							isUserDetailsChanged = true;
						}


						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("DATE_FORMAT"))){
							if(SwtUtil.isEmptyOrNull(user.getDateFormat())) {
								user.setDateFormat(sysParams.getDateFormat());
							}
						}else {
							user.setDateFormat(verifyResult.getAttributeValueByName("DATE_FORMAT"));
							isUserDetailsChanged = true;
						}
					}

					if(isNewCreatedUser) {
//							/* Save the records by calling manager class */
						SystemInfo systemInfo =  new SystemInfo();
						PasswordHistory pwdHist = new PasswordHistory();
						SystemFormats systemFormats =  new SystemFormats();

						systemFormats.setCurrencyFormat(SwtUtil.getCurrentCurrencyFormat(null));

//							String dateFormat= null;
//							if ((SwtConstants.DATE_PAT + userMaintenance.getDateFormat()).equals("datePat1"))
//								dateFormat = "dd/MM/yyyy";
//							else
//								dateFormat = "MM/dd/yyyy";*

						pwdHist = new PasswordHistory();
						/* Setting user id,host id and password using bean class */
						pwdHist.getId().setHostId(SwtUtil.getCurrentHostId());
						pwdHist.getId().setUserId(generateduserId);
						pwdHist.setPassword(SwtUtil.encodePassword("DUMMYPassWord", generateduserId));

						systemFormats.setDateFormatValue(SwtUtil.getCurrentDateFormat(null));
						systemInfo.setIpAddress(request.getRemoteAddr());
						user.setUpdateUser("SYSTEM");
						usermaintenanceManager.saveUserDetail(user, systemInfo, systemFormats, pwdHist);




					}else {
//							user.setPassword(usermaintenanceManager.fetchUserDetail(hostId, user.getId().getUserId()).getPassword());
//							SystemInfo systemInfo = new SystemInfo();
//							systemInfo.setIpAddress(request.getRemoteAddr());
//							usermaintenanceManager.updateUserDetails(useroptions, userMaintenance, systemInfo, SwtUtil.getCurrentSystemFormats(request.getSession()));
						if(isUserDetailsChanged)
							user.setUpdateUser("SYSTEM");

						logonMngr.updateUserDetail(user);
					}

//			        }
				}else if(!SwtUtil.isEmptyOrNull(request.getParameter("fromPreLogin"))){
				}else {
					request.setAttribute("isFromPreLoginScreen", "true");
					if(!RegisterMFA.getInstance().isAllowInternalAuthentication() && RegisterMFA.getInstance().isUseSmartAuthenticator() ) {
						/*
						 * Error Condition if user is logged off due to
						 * inactivity i.e. user is suspended
						 */
						errors.add("onlyADFS", new ActionMessage(
								"errors.logon.MFAOnlyIsEnabled"));
						saveErrors(request, errors);
						return getView("fail");
					}
					/* BEGIN: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
				   	   and recovered within one minute*/
					user = (User) getUser();
					user.getId().setHostId(CacheManager.getInstance().getHostId());
					// BEGIN: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
					String encryptedPwd=request.getParameter("encpasswd");
					try{
						/* Start: Code modified to avoid bad padding exception on logon by M.Bouraoui */

						// Set the client session id from request parameters
						String clientSession = request
								.getParameter("clientSession");
						// Added by Saber Chebka: Keep the key creation out of radar.. (TODO: add obfuscation for this class to prevent reverse engineering)
						String pass = clientSession.substring(0, clientSession.length() > 12 ? 12 : clientSession.length());
						/* End : Code modified to avoid bad padding exception on logon by M.Bouraoui */

						pass+=user.getId().getUserId().substring(0,user.getId().getUserId().length()>4?4:user.getId().getUserId().length());
						//					pass = SwtUtil.hash(pass);
						//					user.setPassword(SwtUtil.decryptAES(pass, encryptedPwd));
						user.setPassword(SwtUtil.decryptCBC(pass, encryptedPwd));
					} catch (Exception e) {
						logger.error("Exception caught on password validation: "+e);
						return getView("fail");
						//TODO: Calling the method to save the error messages
						//errors.add("invalidUser", new ActionMessage("errors.logon.invalid"));
						//saveErrors(request, errors);
					}
					// END: Added by Meftah Bouazizi for Mantis 2077 : To decrypt the encrypted password...
					clearPassword = user.getPassword();
					//put clear password in password1 attribute to check if the 32bit or the 64bits encryption was used in verifyUser function
					user.setPassword1(clearPassword);
					// Changes to add userId into encrypted password
					user.setPassword(StlHash.getStlHash(user.getId().getHostId(), user.getId().getUserId(), user.getPassword()));
				}

				if(user != null) {
					User userBeforeLogin = logonMngr.getUserDetail(SwtUtil.getCurrentHostId(), user.getId().getUserId());
					if(userBeforeLogin != null) {
						lastLoginDateBeforeThisAttempt = userBeforeLogin.getLastLogin();
						if(lastLoginDateBeforeThisAttempt == null) {
							lastLoginDateBeforeThisAttempt = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
						}
						lastLoginAttemptsBeforeThis = userBeforeLogin.getInvPassAttempt();
					}
				}


				if((request.getParameter("killSession")==null)||(request.getParameter("killSession").equals("N")))
				{
				/* END: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
				   and recovered within one minute*/

					/*
					 * Code added by Mansour Blanco & Saber Chebka to enable DFA-based authentication - START
					 */
					String dfaEnabledProperty = PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.PROPERTY_DFA_ENABLED);
					if (dfaEnabledProperty != null) {
						dfaEnabled = dfaEnabledProperty.equalsIgnoreCase(SwtConstants.STR_TRUE) && !SwtUtil.isSucessfulMFARequest(request);
					}

					String userChallenged = SessionManager.getInstance().getChallengedUserList().get(
							user.getId().getUserId());
					if (userChallenged != null&&request.getParameter("challenge").equalsIgnoreCase("Y")) {
						challengeMode = true;
					}
					if (userChallenged != null&&!request.getParameter("challenge").equalsIgnoreCase("Y")) {
						challengeMode = false;
						removeRadiusChecker(user);
					}


					/*
					 * Code added by Mansour Blanco & Saber Chebka to enable DFA-based authentication - END
					 */
					if (!challengeMode) // Regardless to dfaEnabled or not.
					{

						/*
						 * Verifying User details from the information stored in the
						 * database.
						 */
						try {
							arr = logonMngr.verifyUser(user, request.getRemoteAddr());
						} catch (NullPointerException npe) {
							System.out
									.println("Login Problem : Given hostId is incorrect");
							return getView("fail");
						}

						if (arr != null && arr.size() > 0) {
							// Extracting User object stored in the database
							userdb = (User) arr.get(0);
							screenPassword = user.getPassword().toUpperCase();
							dbPassword = userdb.getPassword().toUpperCase();
							if (screenPassword.equalsIgnoreCase(dbPassword)) {
								//Commented as 2 Radius server requests are sent for each form submit only one is needed
//							if(dfaEnabled){
//								ActionForward af = radiusCheck(user, false, clearPassword.length(), mapping, request);
//								if(af!=null)
//									return af;
//							}

								/*
								 * Checking If User is already Logged-In set the error message
								 * and exit
								 */

								if(RegisterZookeeper.getInstance().isClusterEnabled()) {
									obj = SessionManager.getInstance().getLoggedUserListFromCluster().get(
											user.getId().getUserId());
								}else {
									obj = SessionManager.getInstance().getLoggedUserList().get(
											user.getId().getUserId());
								}

								if (obj != null && !SwtUtil.envTestEnabled()) {
									logonMngr.enterLoggedInUserStatus(user, request
											.getRemoteAddr());
									/* BEGIN: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
								   	   and recovered within one minute*/
									String dummypass = "";
									for(int i=0;i<clearPassword.length();i++)
										dummypass+="x";
									request.setAttribute("dummypass1", dummypass);
									request.setAttribute("killSessionStatus","K");
									request.setAttribute("mfaToken", mfaToken);
									request.setAttribute("mfaUserId", user.getId().getUserId());
									String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
									SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
									if(isMFARequest)
										request.setAttribute("mfaLogin", "success");
									/* BEGIN: Added by KaisBS and Meftah for mantis 165
									 * 0 : Orphaned or dead sessions are now detected
								   	   and recovered within one minute*/
									setUser(user);
									if(dfaEnabled){
										String af = radiusCheck(user, false, clearPassword.length(), request);
										if(af!=null) {
											return af;
										}
									}
									return getView("fail");
								}

								if(!SwtUtil.isSucessfulMFARequest(request)) {

									if ((userdb.getStatus() != null)
										&& (!userdb.getStatus().equals(""))
										&& (Integer.valueOf(userdb.getStatus())
													.intValue() != 1)) {
										System.out
												.println("Login Problem : User is disabled. Please check the user status in the database");
										user.setPassword("");
										if (userdb.isInactiveDisable() == true) {
											/*
											 * Error Condition if user is logged off due to
											 * inactivity i.e. user is suspended
											 */
											errors.add("disableUser", new ActionMessage(
													"errors.logon.inactiveDisable"));
										} else {
											errors.add("disableUser", new ActionMessage(
													"errors.logon.disableUser"));
										}
										if(dfaEnabled){
											String af = radiusCheck(user, false, clearPassword.length(), request);
											if(af!=null) {
												return af;
											}
										}
										setUser(user);
										saveErrors(request, errors);
										return getView("fail");
									}


									/*
									 * Extracting the password information of the user
									 * trying to login
									 */
									pwdRules = (Password) (arr.get(1));
									// Gets the expire days
									expireDays = pwdRules.getExpireDays().intValue();
									// Gets the last password change
									lastPassCngDate = userdb.getPasswordChangeDate();
									// Getting the instance of Calendar object
									lastPassCngCalendar = Calendar.getInstance();
									// Sets the last password change date
									if (lastPassCngDate != null) {
										lastPassCngCalendar.setTime(lastPassCngDate);
									}
									// Assigns the database date from SwtUtil.getDays()
									dbDays = SwtUtil.getDays(lastPassCngCalendar
											.get(Calendar.MONTH), lastPassCngCalendar
											.get(Calendar.DATE), lastPassCngCalendar
											.get(Calendar.YEAR));
									// Gets the instance of Calendar object
									testDateCalendar = Calendar.getInstance();
									// Sets the test date to the calendar instance
									testDateCalendar.setTime(SwtUtil
											.getTestDateFromParams(userdb.getId()
													.getHostId()));
									todayDays = SwtUtil.getDays(testDateCalendar
											.get(Calendar.MONTH), testDateCalendar
											.get(Calendar.DATE), testDateCalendar
											.get(Calendar.YEAR));
									// Gets the difference in days
									daysDiff = todayDays - dbDays;
									daysNotice = pwdRules.getExpiryDaysNotice().longValue();
									// Gets the Host Id
									hostId = user.getId().getHostId();
									// Gets the User Id
									userId = user.getId().getUserId();
									isNewUser = logonMngr.isNewUser(hostId, userId);
									passWordChangedFrmMenuOption = false;
									passWordChangedFrmMenuOption = logonMngr
											.isZeroSeqNoExisting(hostId, userId);
								}
								/*
								 * Start: Code added by venkat for mantis 1267 on
								 * 16-Dec-2010.
								 */



								if(RegisterZookeeper.getInstance().isClusterEnabled()) {
									coll = SessionManager.getInstance()
											.getLoggedUserStatusListFromCluster();
								}else {
									coll = SessionManager.getInstance()
											.getLoggedUserStatusList();
								}


								if (coll.size() < Integer.parseInt(maxUser)) {
									if(!SwtUtil.isSucessfulMFARequest(request)) {

										/*
										 * End: Code added by venkat for mantis 1267 on
										 * 16-Dec-2010.
										 */
										if ((todayDays - dbDays) > expireDays) {
											logger.debug("Password Expired");
											// Setting Y (Change Password) in request
											request.setAttribute("changepassword", "Y");
											request.getSession().setAttribute("changepassword", "Y");
											String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
											SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
										} else if (daysDiff > (expireDays - daysNotice)) {
											logger.debug("Password Will Expire");
											request
													.setAttribute(
															"changepassworddays",
															String
																	.valueOf((expireDays - daysDiff)));
											// Setting E (Change Password) in request
											request.setAttribute("changepassword", "E");
											request.getSession().setAttribute("changepassword", "E");
											String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
											SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
										} else if (isNewUser == 1) {
											logger
													.debug("New User!! Please change your password");
											// Setting N (New User in request)
											request.setAttribute("changepassword", "N");
											request.getSession().setAttribute("changepassword", "N");
											String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
											SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
										} else if (passWordChangedFrmMenuOption) {
											logger
													.debug("password is changed using menu option");
											// Setting M (Password modified from menu
											// option) in request
											request.setAttribute("changepassword", "M");
											request.getSession().setAttribute("changepassword", "M");
											String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
											SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
										} else {
											logger
													.debug("User is Enabled and Password is also not in Expiry condition");
											loginProcessFlag = false;
										}
									}else {
										logger
												.debug("User is Enabled and Password is also not in Expiry condition");
										loginProcessFlag = false;
									}
								}
								/*
								 * Code added by venkat for Mantis 1267 on 16-Dec-2010
								 */
							} else {
								user.setPassword("");
								logger.debug("Wrong Password Entered");
								errors.add("invalidUser", new ActionMessage(
										"errors.logon.invalidPassword"));
								// Calling the method to save the error messages
								saveErrors(request, errors);
								setUser(user);
								return getView("fail");
							}
						} else {
							logger.debug("User not Found");
							errors.add("invalidUser", new ActionMessage(
									"errors.logon.invalid"));
							// Calling the method to save the error messages
							saveErrors(request, errors);
							setUser(user);
							return getView("fail");
						}

						// Check against radius server before validating license (only if DFA is enabled), added by Saber Chebka
						if(dfaEnabled){
							String af = radiusCheck(user, false, clearPassword.length(), request);
							String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
							SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
							if(af!=null) {
								request.removeAttribute("changepassword");
								return af;
							}
						}
					} else if(dfaEnabled){// In case of challenge and DFA is enabled

						// Process challenge
						String af = radiusCheck(user, true, clearPassword.length(), request);
						if(af!=null)
							return af;

						// Test if user is already logged in even on challenge mode because it is a separate login process
						if(RegisterZookeeper.getInstance().isClusterEnabled()) {
							obj = SessionManager.getInstance().getLoggedUserListFromCluster().get(
									user.getId().getUserId());
						}else {
							obj = SessionManager.getInstance().getLoggedUserList().get(
									user.getId().getUserId());
						}

						if (obj != null) {
							logonMngr.enterLoggedInUserStatus(user, request
									.getRemoteAddr());
							/* BEGIN: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
						   	   and recovered within one minute*/
							String dummypass2 = "";
							for(int i=0;i<user.getPassword().length();i++)
								dummypass2+="x";
							request.setAttribute("dummypass1", dummypass2);

							String dummysecurId = "";
							for(int i=0;i<user.getSecureId().length();i++)
								dummysecurId+="x";
							request.setAttribute("dummySecureId", dummysecurId);

							request.setAttribute("killSessionStatus","K");
							String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
							SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
							/* END: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
						   	   and recovered within one minute*/
							// Remove radius checker before leaving with user already logged in
							if(dfaEnabled)
								removeRadiusChecker(user);
							setUser(user);
							return getView("fail");
						}

						// Refill the userdb once again
						try {
							arr = logonMngr.verifyUser(user, request.getRemoteAddr());
						} catch (NullPointerException npe) {
							setUser(user);
							System.out
									.println("Login Problem : Given hostId is incorrect");
							return getView("fail");
						}
						if (arr != null && arr.size() > 0) {
							// Extracting User object stored in the database
							userdb = (User) arr.get(0);
						}

						loginProcessFlag = false;
					}

					/* BEGIN: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
					   and recovered within one minute*/
				}else {
					if(!TokensProvider.getInstance().validateToken(request)){
						logger.debug("Wrong Password Entered");
						errors.add("invalidUser", new ActionMessage(
								"errors.logon.invalidPassword"));
						// Calling the method to save the error messages
						saveErrors(request, errors);
						setUser(user);
						return getView("fail");
					}
					if(!SwtUtil.isSucessfulMFARequest(request)) {
						/*
						 * Extracting the password information of the user
						 * trying to login
						 */


						if(user == null) {
							commonDataMngr = (CommonDataManager) (request.getSession()
									.getAttribute(SwtConstants.CDM_BEAN));
							String mfaUserId = request.getParameter("mfaUserId");
							if ((commonDataMngr != null) && (!SwtUtil.isEmptyOrNull(mfaUserId) && commonDataMngr.getUser() != null)) {
								user = commonDataMngr.getUser();
							}
						}

						if(user != null){

							ChangePasswordManager changePasswordManager = (ChangePasswordManager) SwtUtil.getBean("changePasswordManager");
							Collection collPassRules=changePasswordManager.getPasswordRules(user);
							if(userdb == null) {
								try {
									arr = logonMngr.verifyUser(user, request.getRemoteAddr());
								} catch (NullPointerException npe) {
									System.out
											.println("Login Problem : Given hostId is incorrect");
									setUser(user);
									return getView("fail");
								}
								if (arr != null && arr.size() > 0) {
									// Extracting User object stored in the database
									userdb = (User) arr.get(0);
								}
							}

							pwdRules =(Password) collPassRules.iterator().next();
							// Gets the expire days
							expireDays = pwdRules.getExpireDays().intValue();
							// Gets the last password change
							lastPassCngDate = userdb.getPasswordChangeDate();
							// Getting the instance of Calendar object
							lastPassCngCalendar = Calendar.getInstance();
							// Sets the last password change date
							if (lastPassCngDate != null) {
								lastPassCngCalendar.setTime(lastPassCngDate);
							}
							// Assigns the database date from SwtUtil.getDays()
							dbDays = SwtUtil.getDays(lastPassCngCalendar
									.get(Calendar.MONTH), lastPassCngCalendar
									.get(Calendar.DATE), lastPassCngCalendar
									.get(Calendar.YEAR));
							// Gets the instance of Calendar object
							testDateCalendar = Calendar.getInstance();
							// Sets the test date to the calendar instance
							testDateCalendar.setTime(SwtUtil
									.getTestDateFromParams(userdb.getId()
											.getHostId()));
							todayDays = SwtUtil.getDays(testDateCalendar
									.get(Calendar.MONTH), testDateCalendar
									.get(Calendar.DATE), testDateCalendar
									.get(Calendar.YEAR));
							// Gets the difference in days
							daysDiff = todayDays - dbDays;
							daysNotice = pwdRules.getExpiryDaysNotice().longValue();
							// Gets the Host Id
							hostId = user.getId().getHostId();
							// Gets the User Id
							userId = user.getId().getUserId();
							isNewUser = logonMngr.isNewUser(hostId, userId);
							passWordChangedFrmMenuOption = false;
							passWordChangedFrmMenuOption = logonMngr
									.isZeroSeqNoExisting(hostId, userId);
						}
						/*
						 * Start: Code added by venkat for mantis 1267 on
						 * 16-Dec-2010.
						 */





						if(!SwtUtil.isSucessfulMFARequest(request)) {

							/*
							 * End: Code added by venkat for mantis 1267 on
							 * 16-Dec-2010.
							 */
							if ((todayDays - dbDays) > expireDays) {
								logger.debug("Password Expired");
								// Setting Y (Change Password) in request
								request.setAttribute("changepassword", "Y");
								request.getSession().setAttribute("changepassword", "Y");
								String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
								SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
								//return getView("fail");
							} else if (daysDiff > (expireDays - daysNotice)) {
								logger.debug("Password Will Expire");
								request
										.setAttribute(
												"changepassworddays",
												String
														.valueOf((expireDays - daysDiff)));
								// Setting E (Change Password) in request
								request.setAttribute("changepassword", "E");
								request.getSession().setAttribute("changepassword", "E");
								String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
								SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
								//	return getView("fail");
							} else if (isNewUser == 1) {
								logger
										.debug("New User!! Please change your password");
								// Setting N (New User in request)
								request.setAttribute("changepassword", "N");
								request.getSession().setAttribute("changepassword", "N");
								String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
								SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
								//	return getView("fail");
							} else if (passWordChangedFrmMenuOption) {
								logger
										.debug("password is changed using menu option");
								// Setting M (Password modified from menu
								// option) in request
								request.setAttribute("changepassword", "M");
								request.getSession().setAttribute("changepassword", "M");
								String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
								SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
								//return getView("fail");
							} else {
								logger
										.debug("User is Enabled and Password is also not in Expiry condition");
								loginProcessFlag = false;
							}
						}else {
							logger
									.debug("User is Enabled and Password is also not in Expiry condition");
							loginProcessFlag = false;
						}
					}


					// Set login process flag to false. If true then we still in the login page (return mapping forward fail)
					loginProcessFlag=false;
					HttpSession session = null;
					// retrieve the map which contains the list of opened sessions
					sessionMap = SessionManager.getInstance().getSessionMap();
					// The iterator the session map
					Iterator itr = sessionMap.values().iterator();
					// Iterate through sessionMap to recupere the common data manager of the session
					while (itr.hasNext()) {
						session = (HttpSession) itr.next();
						if (session != null) {
							commonDataMngr = (CommonDataManager) (session
									.getAttribute(SwtConstants.CDM_BEAN));
							if((request.getParameter("fromPreLogin")!=null)&&(request.getParameter("fromPreLogin").equals("Y"))){
								String mfaUserId = request.getParameter("mfaUserId");
								if ((commonDataMngr!=null) && (!SwtUtil.isEmptyOrNull(mfaUserId) && commonDataMngr.getUser() !=null && commonDataMngr.getUser().getId().getUserId().equals(mfaUserId))){
									// Kill the session using the host id and the user id attributes. They are the attributes before crash
									if(RegisterZookeeper.getInstance().isClusterEnabled()) {
										SessionManager.getInstance().killSessionFromCluster(SwtUtil.getCurrentHostId(), commonDataMngr.getUser().getId().getUserId(), commonDataMngr.getUser().getId().getUserId() );
									}else {
										SessionManager.getInstance().killSession(SwtUtil.getCurrentHostId(), commonDataMngr.getUser().getId().getUserId(), commonDataMngr.getUser().getId().getUserId() );
									}
									break;
								}
							}else if ((commonDataMngr!=null) && commonDataMngr.getUser() !=null && commonDataMngr.getUser().getId().getUserId() != null && (commonDataMngr.getUser().getId().getUserId().equals(user.getId().getUserId()))){
								// Kill the session using the host id and the user id attributes. They are the attributes before crash
								if(RegisterZookeeper.getInstance().isClusterEnabled()) {
									SessionManager.getInstance().killSessionFromCluster(user.getId().getHostId(), user.getId().getUserId(), user.getId().getUserId() );
								}else {
									SessionManager.getInstance().killSession(user.getId().getHostId(), user.getId().getUserId(), user.getId().getUserId() );
								}
								break;
							}
						}
					}

					if(RegisterZookeeper.getInstance().isClusterEnabled()) {
						List <UserStatus> list = SessionManager.getInstance().getLoggedUserStatusListFromCluster();
						UserStatus status  = null;
						Iterator itr2 = list.iterator();

						String mfaUserId = request.getParameter("mfaUserId");
						// Iterate through sessionMap to recupere the common data manager of the session
						while (itr2.hasNext()) {
							status = (UserStatus) itr2.next();
							if(!SwtUtil.isEmptyOrNull(mfaUserId)) {
								if(status.getUsers().getId().getUserId().equals(mfaUserId)){
									SessionManager.getInstance().killSessionFromCluster(user.getId().getHostId(), user.getId().getUserId(), user.getId().getUserId() );
								}
							}else {
								if(status.getUsers().getId().getUserId().equals(user.getId().getUserId())){
									SessionManager.getInstance().killSessionFromCluster(user.getId().getHostId(), user.getId().getUserId(), user.getId().getUserId() );
								}
							}
						}

					}


					// retrieve the userdb from the common data manager before instantiate
					if(commonDataMngr==null){
						errors.add("disableUser", new ActionMessage(
								"error.killSession"));
						saveErrors(request, errors);
						setUser(user);
						return getView("fail");

					}else{
						userdb = (User) commonDataMngr.getUser();
					}
				}
				/* END: Added by KaisBS and Meftah for mantis 1650 : Orphaned or dead sessions are now detected
				   and recovered within one minute*/

				/*
				 * Start:code added by venkat for mantis 1267:"License programs
				 * and application checking of dates must be consistent" on
				 * 3-Dec-2010.
				 */
				licenseHostName = Inet4Address.getLocalHost().getHostName();
				// Validating user information with license file info.
				if (!(validate(request, response))) {
					if (license.equals("U")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.exceednoofusers"));
					}
					if (license.equals("D")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.expirydate"));
					}
					/*
					 * Start:code added by venkat for mantis 1267:"License
					 * programs and application checking of dates must be
					 * consistent" on 3-Dec-2010.
					 */
					if (license.equals("H")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.hostName", licenseHostName));
					}
					/*
					 * End:code added by venkat for mantis 1267:"License
					 * programs and application checking of dates must be
					 * consistent" on 3-Dec-2010.
					 */
					if (license.equals("I")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.reg"));
					}
					/*
					 * Start:code added by venkat for mantis 1267:"License
					 * programs and application checking of dates must be
					 * consistent" on 2-Dec-2010.
					 */
					if (license.equals("Code")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.licenseCode"));
					}
					if (license.equals("Host")) {
						errors.add("disableUser", new ActionMessage(
								"errors.logon.host", utilsInst.get("Host")));
					}
					/*
					 * End:code added by venkat for mantis 1267:"License
					 * programs and application checking of dates must be
					 * consistent" on 2-Dec-2010.
					 */
					saveErrors(request, errors);
					setUser(user);
					return getView("fail");
				}
				/*
				 * End:code added by venkat for mantis 1267:"License programs
				 * and application checking of dates must be consistent" on
				 * 2-Dec-2010.
				 */
				// Setting the CommonDataManager in Session
				if(userdb == null) {
					try {
						arr = logonMngr.verifyUser(user, request.getRemoteAddr());
					} catch (NullPointerException npe) {
						System.out
								.println("Login Problem : Given hostId is incorrect");
						return getView("fail");
					}
					if (arr != null && arr.size() > 0) {
						// Extracting User object stored in the database
						userdb = (User) arr.get(0);
					}
				}

				commonDataMngr = new CommonDataManager();
				request.getSession().setAttribute(SwtConstants.CDM_BEAN,
						commonDataMngr);
				commonDataMngr.setUser(userdb);
				String referer= request.getScheme()+"://"+request.getServerName()+':'+request.getServerPort()+request.getContextPath();
				commonDataMngr.setReferer(referer);
				commonDataMngr.setMfatoken(mfaToken);
				if(lastLoginDateBeforeThisAttempt == null) {
					if(userdb != null) {
						lastLoginDateBeforeThisAttempt = userdb.getLastLogin();
						if(lastLoginDateBeforeThisAttempt == null) {
							lastLoginDateBeforeThisAttempt = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
						}
						lastLoginAttemptsBeforeThis = userdb.getInvPassAttempt();
					}
				}
				commonDataMngr.setLastLoginDateBeforeThisAttempt(lastLoginDateBeforeThisAttempt);
				commonDataMngr.setLastLoginAttemptsBeforeThis(lastLoginAttemptsBeforeThis);
				logBean = new LogonBean();
				logBean.afterLogonProcess(request, userdb, logonMngr);
				if (loginProcessFlag) {
					setUser(user);
					return getView("fail");
				}
			} else {
				commonDataMngr = (CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN);
				userdb = (User) commonDataMngr.getUser();
				request.getSession().setAttribute("afterLogon", "Y");
			}
			User userToken = logonMngr.getUserDetail(SwtUtil.getCurrentHostId(), userdb.getId().getUserId());
			String tokenList = SwtUtil.generateCsrfTokens(commonDataMngr);
			if(userToken.getUserAuthDetails() == null) {
				userToken.setUserAuthDetails(new UserAuthDetails());
				userToken.getUserAuthDetails().getId().setHostId(SwtUtil.getCurrentHostId());
				userToken.getUserAuthDetails().getId().setUserId(userdb.getId().getUserId());
			}
			userToken.getUserAuthDetails().setCsrfTokens(tokenList);
			logonMngr.updateUserDetail(userToken);

			// Generate JWT token
			String bearerToken = TokensProvider.getInstance().createToken(userdb, (request.getSession() != null?request.getSession().getId():null));
			commonDataMngr.setBearerToken(bearerToken);

			// Set User preferences and remove Movement locks
			SwtUtil.setUserPreferences(request);
			logger.debug("Exit from LogonAction.login method");
			setUser(user);
			return getView("success");
		} catch (SwtException swtexp) {
			errors.add("errorInLogin", new ActionMessage("error.unexpectedError.duringauthentification"));
			saveErrors(request, errors);
			logger.error("SwtException Catch in LogonAction.'login' method : "
						 + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			setUser(user);
			return getView("fail");
		} catch (Exception e) {
			errors.add("errorInLogin", new ActionMessage("error.unexpectedError.duringauthentification"));
			saveErrors(request, errors);
			logger.debug("Exception : LogonAction.'login' method");
			SystemExceptionHandler.logError(e);
			setUser(user);
			return getView("fail");
		}finally{
			clearPassword = null;
		}
	}

	/**
	 * Method that checks user against radius server
	 * @param user
	 * @param challengeMode
	 * @param mapping
	 * @param request
	 * @return
	 *
	 * @authors Saber Chebka & Mansour Blanco
	 */
	public String radiusCheck(User user, boolean challengeMode, int passwordLn, HttpServletRequest request){
		int result=0;
		SwtRadiusChecker radiusChecker = null;
		ActionMessages errors = new ActionMessages();
		String bearerToken = null;
		HttpServletResponse response = null;
		try
		{
			response = SwtUtil.getCurrentResponse();

			radiusChecker = SessionManager.getInstance().getRadiusCheckers().get(user.getId().getUserId());
			if (radiusChecker==null) {
				radiusChecker = new SwtRadiusChecker();
			}
			if(!challengeMode) {
				result = radiusChecker.check(user.getId().getUserId(), user.getSecureId());
			}
			else {
				result = radiusChecker.processChallenge(user.getSecureId());
			}
		} catch (Exception commExp) {
			logger.error("Exception occurred when requesting RSA server: "+commExp);
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
					.handleException(commExp, "radiusCheck", LogonAction.class));
			removeRadiusChecker(user);
			errors.add("rsaserver.error", new ActionMessage("rsaserver.error","Unable to connect to LDAP to read RSA server configuration"));
			saveErrors(request, errors);
			setUser(user);
			return getView("fail");
		}
		/*
		 * Now check result:
		 * -2: Error on radius server
		 * -1: timeout
		 *  0: authetication failed
		 *  1: authetication succesful
		 *  2: challenge needed
		 */
		switch (result) {
			case -2: //org.swallow.radius.SwtRadius.ACCESS_ERROR
				logger.error("Exception occurred when connecting to RSA server\n"+radiusChecker.getLastReplyMessage());
				SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance()
						.handleException(new RadiusException(radiusChecker.getLastReplyMessage()), "radiusCheck", LogonAction.class));
				removeRadiusChecker(user);
				errors.add("rsaserver.error", new ActionMessage("rsaserver.error",radiusChecker.getLastReplyMessage()));
				saveErrors(request, errors);
				setUser(user);
				return getView("fail");

			case -1: //org.swallow.radius.SwtRadius.ACCESS_TIMEOUT
				try {
					logonMngr.verifyUser(user, request.getRemoteAddr(), 9);
				} catch (Exception e) {	}
				removeRadiusChecker(user);
				errors.add("invalidUser", new ActionMessage("secureid.timeout"));
				saveErrors(request, errors);
				return getView("fail");

			case 1: //org.swallow.radius.SwtRadius.ACCESS_ACCEPT
				bearerToken = TokensProvider.getInstance().createToken(user, (request.getSession() != null?request.getSession().getId():null));
				SwtUtil.setCookieValueByName(response, "JBEARERTOKEN" , bearerToken);
				removeRadiusChecker(user);
				break;

			case 2: //org.swallow.radius.SwtRadius.ACCESS_CHALLENGE
				CommonDataManager commonDataMngrChallenge = new CommonDataManager();
				commonDataMngrChallenge.setSwtRadiusChecker(radiusChecker);

				commonDataMngrChallenge.setChallengedUser(user);
				request.getSession().setAttribute(SwtConstants.CDM_BEAN_FOR_CHALLENGE, commonDataMngrChallenge);

				String dummypass = "";
				for(int i = 0; i < passwordLn; i++)
					dummypass += "x";
				request.setAttribute("dummypass", dummypass);
				request.setAttribute("dfastatus", "C");
				setUser(user);
				return getView("fail");

			case 0: //org.swallow.radius.SwtRadius.ACCESS_REJECT
				logger.debug("SecureId does not match");
				removeRadiusChecker(user);
				try {
					logonMngr.verifyUser(user, request.getRemoteAddr(), 10);
				} catch (Exception e) {	}
				errors.add("invalidUser", new ActionMessage("secureid.invalid"));
				saveErrors(request, errors);
				setUser(user);
				return getView("fail");
		}
		return null;
	}


	/**
	 * Removes and clean up radius checkers
	 * @param user
	 */
	public void removeRadiusChecker(User user){
		if (SessionManager.getInstance().getChallengedUserList() != null) {
			SessionManager.getInstance().getChallengedUserList().remove(user.getId().getUserId());
		}
		if (SessionManager.getInstance().getRadiusCheckers()!=null){
			if(SessionManager.getInstance().getRadiusCheckers().get(user.getId().getUserId())!=null)
				SessionManager.getInstance().getRadiusCheckers().get(user.getId().getUserId()).recycle();
			SessionManager.getInstance().getChallengedUserList().remove(user.getId().getUserId());
		}
	}

	/**
	 * This method is called when user provides the user name and password and
	 * hit the screen to login in to the application.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward object
	 * @throws Exception
	 */
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {

		logger.debug("Enter into logonActon.'unspecified' method");
		request.setAttribute("isFromPreLoginScreen", request.getParameter("isFromPreLoginScreen"));
		request.setAttribute("isLogoutRedirect", request.getParameter("isLogoutRedirect"));


		method = String.valueOf(method);
		switch (method) {
			case "reLogin":
				return reLogin();
			case "login":
				return login();
			case "loginFail":
				return loginFail();
			case "preLoginScreen":
				return preLoginScreen();
			case "preLoginScreenData":
				return preLoginScreenData();
		}

		return getView("fail");

	}

	/**
	 * This method is called when user provides the user name and password and
	 * hit the screen to login in to the application.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return actionForward object
	 * @throws Exception
	 */
	private boolean validate(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		// Variable to hold machine match status
		boolean machineFlag = false;
		// Variable to hold maximum number of users
		String maxUser = null;
		// Variable to hold host name
		String licenseHost = null;
		// Variable to hold hash code
		String licenseHashcode = null;
		// Variable to hold secret code used to generate hash code
		StringBuffer secretCode = null;
		// Variable to hold hash code
		String securityHashCode = null;
		// Variable to hold host name
		String hostNameTemp = null;
		// Variable to hold license parameters
		StringTokenizer licenseST = null;
		// Variable to hold machine host id
		StringTokenizer hostIdTemp = null;
		// Variable to hold the current date temporarily
		String currentDateTemp = null;
		// To hold license expiryDate
		String expiryDate = null;
		// To hold server name
		String serverName = null;
		// Variable to hold list of host names
		String myHostId = null;
		// Variable to hold collection of user status
		Collection<UserStatus> userColl = null;
		// Variable to hold list of host names
		ArrayList<String> hostNameList = null;
		// Variable to hold ip address
		ArrayList<String> utilIpAddress = null;
		// To hold license date,current date
		Date licenseDate, currentDate = null;
		// Variable to hold license parameters
		Config licenseParam = null;
		try {
			logger.debug("Entering into LogonAction.validate method.");
			// Sets the Server Name
			serverName = "HostId";
			/*
			 * Start:Code modified by venkat for mantis 1267:"License programs
			 * and application checking of dates must be consistent" on
			 * 2-Dec-2010.
			 */

			// Assign value to secret code that for generating hash code
			secretCode = new StringBuffer("^$~(^");
			// get current date and format into yyyy-mm-dd format
			currentDateTemp = SwtUtil.formatDate(new Date(), "yyyy-MM-dd");
			currentDate = SwtUtil.parseDate(currentDateTemp, "yyyy-MM-dd");
			licenseParam = Config.getInstance();
			/*
			 * Start: Code modified for Mantis1354: Check of Code in
			 * license.properties file should not be case sensitive - by Marshal
			 * on 21-Mar-2011
			 */
			// to get license parameters from license.properties file
			licenseHost = licenseParam.get("Host").trim();
			// Gets the maximum users from the license parameter
			maxUser = licenseParam.get("Users").trim();
			// Gets the expire date of the license
			expiryDate = licenseParam.get("ExpiryDate").trim();
			// Gets the license hash code
			licenseHashcode = licenseParam.get("Code").trim();
			/*
			 * End: Code modified for Mantis1354: Check of Code in
			 * license.properties file should not be case sensitive - by Marshal
			 * on 21-Mar-2011
			 */
			// Gets the expire date format
			licenseDate = SwtUtil.parseDate(expiryDate, "yyyy-MM-dd");
			// throw error message for empty license parameters
			if (maxUser.equals("") || (expiryDate.equals(""))
				|| (serverName.equals("")) || (licenseHost.equals(""))
				|| (licenseHashcode.equals(""))) {
				System.out
						.println("License Problem : Code or Host or maxUser or expiryDate or serverName is not valid");
				license = "I";
				return false;
			}
			/*
			 * End:code added by venkat for mantis 1267:"License programs and
			 * application checking of dates must be consistent" on 2-Dec-2010.
			 */
			// Gets the logged in user details


			if(RegisterZookeeper.getInstance().isClusterEnabled()) {
				userColl = SessionManager.getInstance().getLoggedUserStatusListFromCluster();
			}else {
				userColl = SessionManager.getInstance().getLoggedUserStatusList();
			}
			/*
			 * Start:code added/modified by venkat for mantis 1267:"License
			 * programs and application checking of dates must be consistent" on
			 * 2-Dec-2010.
			 */
			// to form hash code by passing secret code,host name
			secretCode.append(licenseHost.toUpperCase() + "^");
			secretCode.append(maxUser.toUpperCase() + "^");
			secretCode.append(expiryDate.toUpperCase() + "^");
			// Gets the list of IP address
			hostNameList = licenseParam.getIPAddress("Hostname");
			// Checks the availability of the list of host names
			if (hostNameList != null && hostNameList.size() > 0) {
				for (int ipCounter = 0; ipCounter < hostNameList.size(); ipCounter++) {
					hostNameTemp = new String(hostNameList.get(ipCounter));
					secretCode
							.append((hostNameTemp.trim().toUpperCase()) + "^");
				}
			}
			// Appends the special characters to StringBuffer object
			secretCode.append("^$~(");
			// Calling the method to encrypt the secret code
			securityHashCode = crypt(secretCode.toString());
			// Code modified for Mantis1354: Check of Code
			// in license.properties file should not be case sensitive - by
			// Marshal on 21-Mar-2011
			// Compare license hash code ignoring case
			if (!licenseHashcode.equalsIgnoreCase((securityHashCode))) {
				System.out
						.println("License Violation : Security Code is invalid.");
				license = "Code";
				return false;
				// Prints an error message if the host Id is not a valid one
			} else if (!licenseHost
					.equalsIgnoreCase(SwtUtil.getCurrentHostId())) {
				System.out
						.println("License Violation : Not valid for this host.");
				license = "Host";
				return false;
				// Prints an error message if the number of users exceeds the
				// limit
			} else if (userColl.size() >= Integer.parseInt(maxUser)) {
				System.out
						.println("License Violation : Max user count exceeded");
				license = "U";
				return false;
			} else {
				// Prints an error message if the license expires
				if (currentDate.after(licenseDate)) {
					System.out.println("License Violation : Licence expired");
					license = "D";
					return false;
				} else {

					if(!RegisterZookeeper.getInstance().isClusterEnabled() && (!"YES".equalsIgnoreCase(System.getenv(ZkUtils.PROPERTY_ENV_DOCKER))) ) {
						// Assigns the local host name to myHostId
						myHostId = Inet4Address.getLocalHost().getHostName();

						/*
						 * code modified by venkat for mantis 1267:"License programs
						 * and application checking of dates must be consistent" on
						 * 02-Dec-2010.
						 */
						utilIpAddress = licenseParam.getIPAddress("Hostname");

						// Checks the availability of IP address
						if (utilIpAddress != null && utilIpAddress.size() > 0) {
							for (int ipCounter = 0; ipCounter < utilIpAddress
									.size(); ipCounter++) {
								serverName = new String(utilIpAddress
										.get(ipCounter));
								/*
								 * Start:code modified by venkat on 21_jan_2011 for
								 * issues found on V1051 beta testing- license
								 * issues.
								 */
								// Creates an instance of StringTokenizer
								licenseST = new StringTokenizer(serverName, ".");
								// Gets the server name
								serverName = licenseST.nextToken().trim();
								// Sets the server name to upper case
								serverName = serverName.toUpperCase();
								hostIdTemp = new StringTokenizer(myHostId, ".");
								// Gets the host id from the token value
								myHostId = hostIdTemp.nextToken();
								// Sets the host id to upper case
								myHostId = myHostId.toUpperCase();
								/*
								 * End:code modified by venkat on 21_jan_2011 for
								 * issues found on V1051 beta testing- license
								 * issues.
								 */
								if (myHostId.equals(serverName)) {
									machineFlag = true;
									license = "H";
									return machineFlag;
								}
							}
							logger.debug("Exit from  LogonAction.validate method");
							if (!machineFlag) {
								System.out
										.println("License Violation : Mismatch in machine name - "
												 + myHostId);
								license = "H";
								return machineFlag;
							}
						}
					}
				}
				return true;
			}
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - Exception Catched in [validate] method : - "
						 + exp.getMessage());
			SystemExceptionHandler.logError(exp);
			return false;
		}
		/*
		 * End:code added/modified by venkat for mantis 1267:"License programs
		 * and application checking of dates must be consistent" on 2-Dec-2010.
		 */
	}

	/*
	 * Start: Code added by venkat for mantis:1267:"License programs and
	 * application checking of dates must be consistent" on 2-Dec-2010
	 */
	/**
	 * This method is used to encrypt the given input String value.<br>
	 * Encryption is achieved by using MD5, A one-way hashing algorithm that
	 * produces a 128-bit hash.<br>
	 *
	 * @param inputString
	 * @return String
	 */
	public String crypt(String inputString) {
		// Declares the MessageDigest object
		MessageDigest msgDigest = null;
		// Declares the byte array object
		byte[] hashValue = null;
		// Declares the hexString StringBuffer object
		StringBuffer hexString = null;
		try {
			logger.debug("Enter into  LogonAction.crypt method");
			// Creates a new instance of hexString
			hexString = new StringBuffer();
			if (inputString == null || inputString.length() == 0) {
				throw new IllegalArgumentException(
						"Code to encript cannot be null or zero length");
			}
			// Assigns the Message Digest algorithm 5 to msgDigest
			msgDigest = MessageDigest.getInstance("MD5");
			// Updates the byte value of the given input string
			msgDigest.update(inputString.getBytes());
			// Populates the hash value in the byte array
			hashValue = msgDigest.digest();
			for (int i = 0; i < hashValue.length; i++) {
				if ((0xff & hashValue[i]) < 0x10) {
					hexString.append("0"
									 + Integer.toHexString((0xFF & hashValue[i])));
				} else {
					hexString.append(Integer.toHexString(0xFF & hashValue[i]));
				}
			}
			logger.debug("Exit from  LogonAction.crypt method");
		} catch (NoSuchAlgorithmException e) {
			logger
					.error(this.getClass().getName()
						   + " - NoSuchAlgorithmException Catched in [crypt] method : - "
						   + e.getMessage());
			throw new RuntimeException("" + e);
		} catch (Exception exp) {
			logger.error(this.getClass().getName()
						 + " - Exception Catched in [crypt] method : - "
						 + exp.getMessage());
			SystemExceptionHandler.logError(exp);
		}
		return hexString.toString();
	}
	/*
	 * End: Code added by venkat for mantis:1267:"License programs and
	 * application checking of dates must be consistent" on 2-Dec-2010
	 */

	/**
	 * This method will be used to log errors coming from MFA (for example ADFS)
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	public String loginFail()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {


			if(RegisterMFA.getInstance().isUseSmartAuthenticator()) {
				String samlErrAsString = request.getParameter("samlError");
				if(!SwtUtil.isEmptyOrNull(samlErrAsString)){
					samlErrAsString = SwtUtil.decode64(request.getParameter("samlError"));

					Gson g = new Gson();
					SamlError err = g.fromJson(samlErrAsString, SamlError.class);
//					request.setAttribute("ErrorMessage", err.getMessage());
//					request.setAttribute("ErrorTitle", err.getType());
					logger.error("SwtException Catch in LogonAction.'login' method : "+err.getType() + "  "
								 + err.getMessage());

				}
			}
			return getView("error");
		} catch (Exception e) {
			logger.debug("Exception : LogonAction.'login' method");
			SystemExceptionHandler.logError(e);
			return getView("fail");
		}finally{
		}
	}

	public String initiateLogin() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {


			Enumeration attributeNames = request.getSession().getAttributeNames();
			HashMap<String,Object> storedAttributes=new HashMap<String,Object>();
			while (attributeNames.hasMoreElements())
			{
				String key = (String)attributeNames.nextElement();
				storedAttributes.put(key,request.getSession().getAttribute(key));
				//	      request.getSession().removeAttribute(key);
			}
			try {
				if(SwtUtil.getCurrentUser(request.getSession()) != null){
					request.getSession().setAttribute("KilledBy", SwtUtil.getCurrentUser(request.getSession()).getId().getUserId());
				}
			}catch(Exception e) {
			}
			request.getSession().invalidate();
			request.getSession(true);

			SwtUtil.setCookieValueByName(response, "dXNl-cm5h-bWU" , "");
			SwtUtil.setCookieValueByName(response, "JSESSIONID" , "");


			response.getWriter().print("true");
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			try {
				response.getWriter().print("false");
			} catch (IOException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
		}

		return null;
	}

	public String preLoginScreen() throws SwtException {

		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		Enumeration attributeNames = request.getSession().getAttributeNames();
		HashMap<String,Object> storedAttributes=new HashMap<String,Object>();
		while (attributeNames.hasMoreElements())
		{
			String key = (String)attributeNames.nextElement();
			storedAttributes.put(key,request.getSession().getAttribute(key));
//	      request.getSession().removeAttribute(key);
		}
		try {
			if(SwtUtil.getCurrentUser(request.getSession()) != null){
				request.getSession().setAttribute("KilledBy", SwtUtil.getCurrentUser(request.getSession()).getId().getUserId());
			}
		}catch(Exception e) {
		}
		request.getSession().invalidate();
		request.getSession(true);

		SwtUtil.setCookieValueByName(response, "dXNl-cm5h-bWU" , "");

		request.setAttribute("method", request.getParameter("methodName"));
		request.setAttribute("isLogoutRedirect", request.getParameter("isLogoutRedirect"));
		request.setAttribute("killSessionStatus", request.getParameter("killSessionStatus"));
		request.setAttribute("mfaUserId", request.getParameter("mfaUserId"));
		request.setAttribute("errorsAtLogin", request.getParameter("errorsAtLogin"));
		return getView("preloginscreen");
	}



	public String preLoginScreenData() throws SwtException {
		/* Method's local variable and class instance declaration */
		String isLogoutRedirect = "";
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		boolean allowInternalAuthentication = false;
		boolean isUseSmartAuthenticator = false;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			logger.debug(this.getClass().getName() + "- [preLoginScreen] - Enter");
			isLogoutRedirect = request.getParameter("isLogoutRedirect");
//			isLogoutRedirect =""+ request.getAttribute("isLogoutRedirect");
			if(SwtUtil.isEmptyOrNull(isLogoutRedirect) || !"true".equalsIgnoreCase(isLogoutRedirect)) {
				isLogoutRedirect ="false";
			}


			SwtUtil.setCookieValueByName(response, "dXNl-cm5h-bWU" , "");
			SwtUtil.setCookieValueByName(response, "JSESSIONID" , "");

			//When the session is valid (the user is connected successfully) and the CommonDataManager is not loaded create a new CommonDataManager and reset settings

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			allowInternalAuthentication = RegisterMFA.getInstance().isAllowInternalAuthentication();
			isUseSmartAuthenticator = RegisterMFA.getInstance().isUseSmartAuthenticator();
			if(isUseSmartAuthenticator) {
				lstOptions.add(new OptionInfo("external", SwtUtil.getMessageFromSession("mfa.externalSSO", null), false));
			}

			if(allowInternalAuthentication) {
				lstOptions.add(new OptionInfo("internal", SwtUtil.getMessageFromSession("mfa.internlUserPass", null), false));
			}
			lstSelect.add(new SelectInfo("authMethod", lstOptions));



			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement("Logon");

			xmlWriter.clearAttribute();
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			responseConstructor.createElement("allowInternalAuthentication", ""+allowInternalAuthentication);
			responseConstructor.createElement("isLogoutRedirect", ""+isLogoutRedirect);
			CommonDataManager    cdm = (CommonDataManager)request.getSession().getAttribute(SwtConstants.CDM_BEAN);
//
			if(cdm != null) {

				Enumeration attributeNames = request.getSession().getAttributeNames();
				HashMap<String,Object> storedAttributes=new HashMap<String,Object>();
				while (attributeNames.hasMoreElements())
				{
					String key = (String)attributeNames.nextElement();
					storedAttributes.put(key,request.getSession().getAttribute(key));
					//request.getSession().removeAttribute(key);
				}
				request.getSession().invalidate();
				request.getSession(true);

			}
			responseConstructor.createElement("userStillConnected", ""+false);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			// Add the selects node
			responseConstructor.formSelect(lstSelect);

			//lstOptions.add(new OptionInfo("", "", false));


			xmlWriter.endElement("Logon");
			request.setAttribute("data", xmlWriter.getData());
			logger.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("data");
			// return null;
		} catch (Exception exp) {
			exp.printStackTrace();
			logger.error(this.getClass().getName() + " - Exception Catched in [preLoginScreen] method : - "
						 + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "preLoginScreen",
					LogonAction.class), request, "");
			return getView("fail");
		}
	}


}