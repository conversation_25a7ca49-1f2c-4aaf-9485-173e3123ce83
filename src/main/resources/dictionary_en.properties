# About Menu
screen.aboutProject=About&nbsp;-&nbsp;SMART-Predict
screen.releaseDate=Release date: 
screen.version=Version:
screen.copyright=Copyright (c) %s SwallowTech


# Repetitive resources
screen.alert.error=Error
screen.alert.warning=Warning
screen.buildInProgress=DATA BUILD IN PROGRESS
screen.cancel=Cancel
screen.connectionError=CONNECTION ERROR
screen.lastRefresh=Last Refresh:
screen.notValidParent=This grid does not have a valid parent
screen.requestForFlashPlayer=This page requires a recent version of Flash Player.
screan.screanVersion=Screen Version
screen.showXML=Show XML
scenario.events.button.configuration=Configure Recipients 
screen.showJSON=Show JSON
alert.noMovemensAvailable=No Movements Available
error.contactAdmin=Please contact your System Administrator...
error.unexpectedError=An unexpected error has occurred.
error.unexpectedError.duringauthentification=An unexpected error occurred during authentication.
error.errorDateTime=Date and time of error:

# Messages used for labels, errors and success messages
user.id=User*
user.hostId=Host
user.password=Password*
user.secureId=SecurID*
user.login=Login
user.askForUserID=Please enter your User ID
user.askForPassword=Please enter your Password
user.titleBtnLogin=Click here to log in
user.titleBtnCancel1=Back to first screen
user.titleBtnCancel2=Close window
report.repGen=Details


user.titletext=Welcome to SMART-Predict
user.accessmessage=Unauthorised Access Prohibited
user.copyright=&#169; 2006 - 2024 SwallowTech
report.repGen=Details
alert.Viewonlyaccess=&nbsp;So restricted to view only access
alert.passwordExpired=&nbsp;Your password has expired, please change it
alert.passwordExpiresInFewDays=Your password will expire in ${changepassworddays} days. Do you want to change it now?
alert.DateAmountFormatChanged=Your date or amount format has been changed. Please log off and logon again
main.warningMsg=WARNING: Test date set to 
main.refreshDesktop=Refresh desktop
main.showPreviousLogin=Show Previous Login Details
main.alerts=Alerts
main.alert.changeProfile=Are you sure you want to change the screen profile?
main.alert.loggedOff=Do you really want to log off?
main.alert.disable=Do you want to disable alerts?
main.alert.enable=Do you want to enable alerts?
tooltip.saveProfil=Save profile
tooltip.enableDisableAlartPopups=Left click to view alert messages & right click to enable/disable Alert pop-ups
tooltip.viewNotificationMessages=Left click to view notification messages
tooltip.saveProfile=Save profile
tooltip.refreshDesktop=Refresh desktop
label.difference=Difference:
screen.breakdown=Breakdown

#-- Section Maintenance --
section.sectionId=Section
section.sectionName=Name
addSection.title.window=Add Section - SMART-Predict
section.title.window=Section Setup - SMART-Predict
changeSection.title.window=Change Section - SMART-Predict

#-- Unique keys for tooltips --
tooltip.clickLogin=Click here to log in
tooltip.clickBack=Back to first screen
tooltip.closeWindow=Close window
tooltip.decimalplaces=Decimal places
tooltip.add=Add
tooltip.view=View
tooltip.change=Change
tooltip.delete= delete
tooltip.enterStartDate=Enter start date
tooltip.enterStartTime=Enter start time
tooltip.enterEndDate=Enter end date
tooltip.enterEndTime=Enter end time
tooltip.recordCheck= Check record instance
tooltip.selectCategory=Select Category
tooltip.selectGrouping=Select Grouping
tooltip.selectFacility=Select Facility
tooltip.selectAccessRequired=Select Access Required
tooltip.selectPopUp=Notify by Pop-Up
tooltip.selectFlashIcon=Notify by Flash Icon
tooltip.selectMail=Notify by Mail
tooltip.selectFullInstAccess=Allow full access to alert instances
tooltip.SelectReportType=Select report type
tooltip.selectScenario=Select Scenario
tooltip.enterScenario=Enter Scenario ID
tooltip.enterScenarioTitle=Enter Scenario Title
tooltip.enterDescription=Enter Scenario Description
tooltip.scenarioDisplayOrder=Enter Display Order
tooltip.scenarioRunEvery=Enter Run Every (hh:mm:ss)
tooltip.scenarioStartTime=Enter Start Time (hh:mm)
tooltip.scenarioEndTime=Enter End Time (hh:mm)
tooltip.scenarioEmailWhenDiff=Enter Email When Difference
tooltip.scenarioCyclic= Select cyclic
tooltip.scenarioScheduled= Select Scheduled
tooltip.scenarioCreateInst= Select create instance
toolTip.scenarioAdd= Add
toolTip.scenarioChange= Change
toolTip.scenarioDelete= Delete
toolTip.scenarioConfig= Configure
toolTip.scenarioDefParams= Define Parameters
tooltip.scenarioQueryText=Enter Query Text
tooltip.scenarioHostColumn=Enter Scenario Host Column
tooltip.scenarioEntityColumn=Enter Scenario Entity Column
tooltip.scenarioCurrencyColmun=Enter Scenario Currency Column
tooltip.scenarioAmountColumn=Enter Scenario Amount Column
tooltip.selectUseGeneric=Select Use Generic Display
tooltip.scenarioFacilityReferenceColumn=Enter Facility Reference Columns
tooltip.scenarioFacilityParameter=Enter Facility Parameters
tooltip.scenarioAlertInstanceColumn=Alert Instance Columns
scenario.unresolvedInstancesAlert = WARNING: Unresolved alert instances exist for this scenario. Some fields will be locked to help avoid introducing incompatibilities. <BR> You may override the locked fields, but are advised to liaise with your dev team to analyse the impact of any change on existing instances.
scenario.tooltip.uniqueExp= Enter unqiue expression
scenario.tooltip.acctIdCombo= Enter Scenario Account ID column
scenario.tooltip.valueDateCombo=Enter Scenario Value Date column
scenario.tooltip.signColCombo=Enter Scenario Sign column
scenario.tooltip.mvtColCombo=Enter Scenario Movement ID column
scenario.tooltip.matchColCombo=Enter Scenario Match ID column
scenario.tooltip.sweepColCombo=Enter Scenario Sweep ID column
scenario.tooltip.payColCombo=Enter Scenario Payment ID column
scenario.tooltip.otherIdColCombo=Enter Scenario Other ID column
scenario.tooltip.otherIdTypeCombo=Enter Scenario Other ID type column
scenario.tooltip.treeBreakDown1Combo=Enter Scenario TreeBreakDown1 column
scenario.tooltip.treeBreakDown2Combo=Enter Scenario TreeBreakDown2 column
scenario.tooltip.instExpTxt=Enter instance expiry value (mins)
scenario.tooltip.radioNo=No
scenario.tooltip.radioAfter=After an interval of
scenario.tooltip.afterMinTxt= Enter interval value (mins)
tooltip.systemFlag=System Flag
tooltip.activeFlag=Active Flag
tooltip.once=Once
tooltip.cyclic=Cyclic
tooltip.daily=Daily
tooltip.weekly=Weekly
tooltip.enterDate=Enter date
tooltip.enterTime=Enter time
tooltip.durationHours=Enter duration (Hrs)
tooltip.durationMins=Enter duration (Min)
tooltip.durationSecs=Enter duration (Sec)
tooltip.enterDayMonth=Enter day of month
tooltip.printScreen=Print screen content
tooltip.save=Save changes and exit
tooltip.cancel=Cancel changes and exit
tooltip.close=Close window


inputException.title=Input Exception Monitor
inputException.notANumber=Not a number
inputException.rateSelected=Refresh rate selected was below minimum.\\nSet to 5 seconds
inputException.startDate=Start Date
inputException.endDate=End Date
inputException.inputDate=Input Date
inputException.enterFromDate=Enter from date
inputException.refresh=Refresh
inputException.rate=Rate
label.from=From
inputException.close=Close
inputException.showValue=Show value must be 1 or greater
tooltip.test=Test scenario
tooltip.goTo=Go to
tooltip.details= Details
genericDisplayMonitor.menu.showXML=Show XML
genericDisplayMonitor.lastRefresh=Last Refresh:
genericDisplayMonitor.labelPage=page
genericDisplayMonitor.labelOf=of
genericDisplayMonitor.contactAdmin=Error occurred, Please contact your System Administrator:
genericDisplayMonitor.error=Error
genericDisplayMonitor.connectionError=CONNECTION ERROR
genericDisplayMonitor.errorServerSide=SERVER SIDE ERROR

tooltip.defaultCcyGrp=Select a default Currency Group
tooltip.sortAlertStage=Sort by alert stage
tooltip.sortRoleId=Sort by role ID
tooltip.sortRoleName=Sort by role name
tooltip.sortEntityId=Sort by entity ID
tooltip.sortAlertMsgs=Sort by alert messages
tooltip.sortStatus=Sort by status
tooltip.msgs=Sweep messages
tooltip.changeMsg=Change selected alert message
tooltip.viewMsg=View selected alert message
tooltip.enterAlertMsg=Enter alert message
tooltip.statusActive=Select status as Active
tooltip.statusInactive=Select status as Inactive
tooltip.selectUserId=Select a user ID
tooltip.fromDate=Enter from date
tooltip.selectFromDate=Select from date
tooltip.toDate=Enter to date
tooltip.selectToDate=Select to date
tooltip.sortLogDate=Sort by log date
tooltip.sortLogTime=Sort by log time
tooltip.sortUserId=Sort by user ID
tooltip.sortItem=Sort by item
tooltip.sortItemNum=Sort by item number
tooltip.sortAction=Sort by action
tooltip.refreshUserLog=Refresh user log
tooltip.viewLogDetails=View log details
tooltip.sortField=Sort by field
tooltip.sortOldValue=Sort by old value
tooltip.sortNewValue=Sort by new value
tooltip.sortScheduledId=Sort by scheduled id
tooltip.sortJobName=Sort by job name
tooltip.sortLastExeTime=Sort by last execution time
tooltip.sortLastExeStatus=Sort by last execution status
tooltip.refreshJobDetail=Refresh job details
tooltip.refreshProcessStatus=Refresh currency process status details
tootltip.cancelCcyProcessStatus=Cancel calculation of the selected currency process
tooltip.addJob=Add job
tooltip.exeJob=Execute a manual job
tooltip.enable=Enable job
tooltip.disable=Disable job
tooltip.changeJob=Change job
tooltip.removeJob=Remove job
tooltip.oldPwd=Old password
tooltip.newPwd=New password
tooltip.confirmNewPwd=Confirm new password
tooltip.ok=OK
tooltip.entityId=Entity ID
tooltip.entityName=Entity name
tooltip.fullAccess=Full access
tooltip.viewAccess=View access
tooltip.dataField=Field
tooltip.noChange=No Change
tooltip.manualOnly=Manual Only
tooltip.allMovements=All Movements
tooltip.readOnlyAccess=Read only access
tooltip.noAccess=No access
tooltip.sortFile=Sort by file
tooltip.errorDesc=Sort by error description
tooltip.refreshErrorLog=Refresh error log
tooltip.sortJobId=Sort by job ID
tooltip.sortName=Sort by job name
tooltip.addNewJob=Add new job
tooltip.changeSelectedJob=Change selected job
tooltip.viewSelectedJob=View selected job
tooltip.deleteSelectedJob=Delete selected job
tooltip.sortIpAddress=Sort by IP address
tooltip.sortFacility=Sort by facility
tooltip.refreshMainLog=Refresh maintenance log
tooltip.viewSelBal=View selected balance
tooltip.editReason=Edit Reason
tooltip.viewBalanceLog=View balance log details
tooltip.selectBalanceDate=Select balance date
tooltip.viewSelectedLog=View selected log entry
tooltip.newValue=Sort by new value
tooltip.menuLevel1Desc=Menu Item Level1 description
tooltip.menuLevel2Desc=Menu Item Level2 description
tooltip.menuLevel3Desc=Menu Item Level3 description
tooltip.selectAll=Select all
tooltip.selectUsers=Select user(s)
tooltip.selectAllUsers=Select All Users
tooltip.selectAllRoles=Select All Roles
tooltip.selectRole=Select role(s)
tooltip.typeMessage=Type message
tooltip.sendMessage=Send message
tooltip.selectEntityid=Select an entity ID
tooltip.sortMvmId=Sort by movement ID
tooltip.sortUpdateUser=Sort by update user
tooltip.sortLockTime=Sort by lock time
tooltip.sortPositionLevel=Sort by position level
tooltip.sortValueDate=Sort by value date
tooltip.sortAmount=Sort by amount
tooltip.sortCurrencyCode=Sort by currency code
tooltip.sortAccountId=Sort by account ID
tooltip.sortBookcodeId=Sort by bookcode ID
tooltip.sortBookcode=Sort by bookcode
tooltip.accessrequired=Sort by Access Required
tooltip.deliverPopup=Sort by Deliver Popup
tooltip.flashIcon=Sort by Flash Icon
tooltip.sendMail=Sort by Email
tooltip.fullInstanceAccess=Sort by Full Instance Access
tooltip.sortMatchStatus=Sort by match status
tooltip.changeSelectedRole=Change selected role
tooltip.minAlphaChars=Minimum number of alpha characters
tooltip.minNumChars=Minimum number of numeric characters
tooltip.minSpecialChars=Minimum number of special characters
tooltip.minPwdlength=Minimum password length
tooltip.pwdExpDays=Number of days before password expires
tooltip.noRecentlyUsedPwds=Number of recently used passwords to be disallowed
tooltip.noInvalidAttempts=Number of invalid attempts before user is blocked
tooltip.changePwdRules=Change password rules
tooltip.SaveChanges=Save changes
tooltip.SaveUpdates=Save updates
tooltip.CancelChanges=Cancel changes
tooltip.sortCurrency=Sort by currency
tooltip.sortLastRun=Sort by last run
tooltip.deleteSeletedParty=Delete selected party
tooltip.deleteSelectedPartyAlias=Delete selected party alias
tooltip.addNewRole=Add new role
tooltip.viewSelectedRole=View selected role
tooltip.deleteSelectedRole=Delete selected role
tooltip.enterRoleId=Enter role ID
tooltip.enterRoleName=Enter role name
tooltip.menuAccess=Menu access
tooltip.entityAccess=Entity access
tooltip.workQueueAccess=Work queue access
tooltip.sweepingLimitsCurr=Sweeping limits by currency
tooltip.alertTypeEmail=Alert type email
tooltip.alertType=Alert type
tooltip.alertTypePopUp=Alert type popup
tooltip.copyFrom=Copy from
tooltip.sortSection=Sort by section
tooltip.addSection=Add new section
tooltip.changeSelectedSection=Change selected section
tooltip.deleteSelectedSection=Delete selected section
tooltip.enterSectionId=Enter section ID
tooltip.enterSectionName=Enter section name
tooltip.sortShortCutId=Sort by shortcut ID
tooltip.sortSortCutName=Sort by shortcut name
tooltip.sortScreenName=Sort by screen name
tooltip.addNewShortCut=Add new shortcut
tooltip.changeSelectedShortcut=Change selected shortcut
tooltip.delSelShortcut=Delete selected shortcut
tooltip.shortCutId=Shortcut ID
tooltip.shortCutName=Shortcut name
tooltip.screenName=Screen name
tooltip.sortSweepLimit=Sort by sweep limit
tooltip.addSwpLmtCurr=Add new sweep limits by currency
tooltip.ChngSwpLmtCurr=Change selected sweep limits by currency
tooltip.viewSwpLmtCurr=View selected sweep limits by currency
tooltip.delSwpLmtCurr=Delete selected sweep limits by currency
tooltip.selectCurrencyId=Select currency code
tooltip.enterSwpLimit=Enter sweep limit
tooltip.sortProcess=Sort by process
tooltip.refreshSystemLog=Refresh system log
tooltip.sortItemId=Sort by item ID
tooltip.sortUserName=Sort by user name
tooltip.addNewUser=Add new user
tooltip.changeSelectedUser=Change selected user
tooltip.viewSelectedUser=View selected user
tooltip.delSelectedUser=Delete selected user
tooltip.enterNewUserId=Enter new user ID
tooltip.enterUserName=Enter user name
tooltip.enterUserPwd=Enter user password
tooltip.confirmPassword=Enter confirm password
tooltip.currentCcyGrpId=Select the default currency group
tooltip.selectUserStatus=Select user status
tooltip.selectRoleId=Select role ID
tooltip.defEntity=Select default entity ID
tooltip.selectSectionId=Select a section ID
tooltip.selectLanguage=Select a language
tooltip.contactName=Enter contact name
tooltip.enterPhNo=Enter phone number
tooltip.emailId=Enter email ID
tooltip.enterLanguage=Enter language
tooltip.changeUserDetails=Change your user details
tooltip.enterProfileOption=Select profile option
tooltip.enterProfileName=Enter profile name
tooltip.defaultProfile=Select to make it default profile
tooltip.saveProfile=Save profile
tooltip.overwriteExistingProfile=Select to overwrite existing profile
tooltip.sortLogonTime=Sort by logon date and time
tooltip.refreshUserStatus=Refresh user status
tooltip.killSelectedUser=Kill selected user
tooltip.viewDetailsSelUser=View details for selected user
tooltip.selectCuurencyGrp=Select currency group
tooltip.sortCurrencyId=Sort by currency code
tooltip.sortWorkQueue=Sort by work queue
tooltip.sortQualityA=Sort by quality A
tooltip.sortQualityB=Sort by quality B
tooltip.sortQualityC=Sort by quality C
tooltip.sortQualityD=Sort by quality D
tooltip.sortQualityE=Sort by quality E
tooltip.sortQualityZ=Sort by quality Z
tooltip.sortOutstanding=Sort by outstanding
tooltip.addNewWrkQAcclist=Add new work queue access list
tooltip.changeWrkQAcclist=Change selected work queue access list
tooltip.changeQAcclist=Change selected queue access
tooltip.viewWrkQAcclist=View selected work queue access list
tooltip.viewQAcclist=View selected queue access
tooltip.deleteWrkQAcclist=Delete selected work queue access list
tooltip.deleteQAcclist=Delete selected queue access
tooltip.sortLastRun=Sort by last run
tooltip.sortname=Sort by name
tooltip.qualityA=Quality A
tooltip.qualityB=Quality B
tooltip.qualityC=Quality C
tooltip.qualityD=Quality D
tooltip.qualityE=Quality E
tooltip.qualityZ=Quality Z
tooltip.changeUserDetails=Change your user details
tooltip.sortname=Sort by name
tooltip.setRun=Run pending job
tooltip.AddNewCurrency=Add new currency
tooltip.ChangeSelectedCurrency=Change selected currency
tooltip.enterUserPwd=Enter user password
tooltip.selectUserStatus=Select user status
tooltip.selectRoleId=Select a role ID
tooltip.selectSectionId=Select section ID
tooltip.selectLanguage=Select a language
tooltip.enterPhNo=Enter phone number
tooltip.enterUserName=Enter user name
tooltip.monthly=Monthly
tooltip.manual=Manual
tooltip.currentstatus=Current status
tooltip.sortNextExeStatus=Sort by next execution time
tooltip.sortBycurrentStatus=Sort by current status
tooltip.sortbyReference=Sort by reference
tooltip.unlockMovement=Unlock movement
tooltip.mvmntnotes=Movement notes
tooltip.sortbysign=Sort by sign
tooltip.reference1=Sort by reference 1
tooltip.roleId=Select the role ID
tooltip.sortbybeneficiaryid=Sort by beneficiary ID
tooltip.sortbycustodianid=Sort by custodian ID
tooltip.sortbyextraText1=Sort by EXTRA_TEXT1
tooltip.sortByType=Sort by Type
tooltip.sortbyParentParty=Sort by parent ID
tooltip.sortbyreference2=Sort by reference 2
tooltip.sortbyreference3=Sort by reference 3
tooltip.sortbymvmnt=Sort by movement type
tooltip.sortByMT950Sod=Sort by MT950 SOD Balance
tooltip.sortByReasonCode=Sort by Reason Code
tooltip.sortByReasonDesc=Sort by Reason Description
tooltip.sortByReasonNotes=Sort by Reason Notes
tooltip.SelectreasonCode=Select Reason Code for Start of Day Balance
tooltip.enterNotes=Enter Notes for Reason
tooltip.sortByDate=Sort by date
tooltip.sortByTime=Sort by time
tooltip.AddNotes=Add notes
tooltip.ViewSelectedNote=View selected note
tooltip.DeleteSeletedNote=Delete selected note
tooltip.SaveNotesandExit=Save notes and exit
tooltip.search=Search movement details
tooltip.SortByBookCode=Sort by bookcode name
tooltip.GrpLvlName1=Sort by 
tooltip.GrpLvlName2=Sort by 
tooltip.GrpLvlName3=Sort by 
tooltip.AddNewBookcode=Add new bookcode
tooltip.ChangeSelectedBookcode=Change selected bookcode
tooltip.DeleteSelectedBookcode=Delete selected bookcode
tooltip.EnterBookcodeID=Enter bookcode ID
tooltip.EnterBookcodeName=Enter bookcode name
tooltip.EnterGroupIDLvl1=Enter group ID level 1
tooltip.EnterGroupIDLvl2=Enter group ID level 2
tooltip.EnterGroupIDLvl3=Enter group ID level 3
tooltip.ClickSelectGroupIDLvl1=Click to select group ID level 1
tooltip.ClickSelectGroupIDLvl2=Click to select group ID level 2
tooltip.ClickSelectGroupIDLvl3=Click to select group ID level 3
tooltip.SelectFormatID=Select format ID
tooltip.CopyMatchQualityProfile=Copy this match quality
tooltip.selectEntity=Select entity
tooltip.addNewField=Add new message field
tooltip.changemsgfield=Change message field
tooltip.viewselectedmsgfield=View selected message field
tooltip.deleteselectedmsgfield=Delete selected message field
tooltip.addnewmsgformat=Add new message format
tooltip.changeselectedmsgformat=Change selected message format
tooltip.viesselectedmsgformat=View selected message format
tooltip.deleteselectedmsgformat=Delete selected message format
tooltip.sortByjobType=Sort by job type
tooltip.jobtype=Job Type
addjob.jobStatus=Status
tooltip.enable=Enable
tooltip.disable=Disable
addjob.enable=Enable
addjob.disable=Disable
changeJob.alert=Job is running, cannot change job specifications
tooltip.sortByjobDetail=Sort by job detail
tooltip.jobdetail=Job Detail
tooltip.enterInterestRate=Enter Interest rate
tooltip.enterInputDate=Select Input date
tooltip.selectMonitor=Select Monitor Type
bookMonitor.monitor=Monitor Type
tooltip.sortMatchId=Sort by match ID
tooltip.sortMsgFormat=Sort by Message Format
tooltip.reconMatch=Reconcile Match
tooltip.sortBIC=Sort by BIC
tooltip.mixedCase=Mixed Case
tooltip.scenarioID=Sort by Scenario ID
tooltip.scenarioEntity=Sort by Entity
tooltip.scenarioCcy=Sort by Ccy
tooltip.scenarioName=Sort by Name
tooltip.scenarioDescriptions=Sort by Description
tooltip.scenarioPublicPrivate=Sort by Public?
tooltip.scenarioCreator=Sort by Creator
tooltip.scenarioTitle=Sort by Scenario Title
tooltip.scenarioCategory=Sort by Scenario Category
tooltip.scenarioCheckInterval=Sort by Check Interval
tooltip.scenarioSystem=Sort by System Flag
tooltip.scenarioActive=Sort by Active Flag
tooltip.scenarioType=Sort by Scenario Type
tooltip.saveAdvanced=Apply advanced details
tooltip.criteria=Open additional search criteria screen
tooltip.addScenario=Add new scenario
tooltip.changeScenario=Change selected scenario
tooltip.viewScenario=View selected scenario
tooltip.roleScenario=Open role maintenance screen
tooltip.deleteScenario=Delete selected scenario
tooltip.category=Open scenario category screen
tooltip.sortCategoryId=Sort by Category ID 
tooltip.sortCategoryDescription=Sort by Category Description
tooltip.sortCategoryTitle=Sort by Category title 
tooltip.sortCategorySystemFlag=Sort by System Flag
tooltip.sortCategoryDisplayorder=Sort by Display Order
tooltip.sortCategoryDisplayName=Sort by Display Tab Name
tooltip.enterCategoryId=Enter Category ID
tooltip.enterCategoryTitle=Enter Title
tooltip.enterCategoryDisplayorder=Enter Display Order
tooltip.addCategory=Add Scenario Category
tooltip.changeCategory=Change Selected Scenario Category
tooltip.deleteCategory=Delete Selected Scenario Category
tooltip.addRole=Add Role
tooltip.changeRole=Change Selected Role
tooltip.deleteRole=Delete Selected Role
addjob.alert.configChanged=A configuration change has been made, Do you wish for this report to be immediately triggered?
#-- Intraday Balance --
intradayBalances.mainScreen=Intraday Balance Report - SMART-Predict
intradayBalances.entity=Entity
intradayBalances.currency=Currency
intradayBalances.date=Report Date
tooltip.intradayBalancesDate=Enter report date
tooltip.calendarintradayBalancesdate=Select report date
intradayBalances.rawData=Raw Data
button.ok=Ok
intradayBalances.accountId=Account ID
intradayBalances.accountScreen=Intraday Balance Account
button.report=Report
tooltip.report=Generate the report
tooltip.okbutton=Generate the report

#-- Turnover Report --
turnoverReport.mainScreen=Turnover Report - SMART-Predict
turnoverReport.entity=Entity
turnoverReport.date=Date
turnoverReport.levelBreakdown=Level Breakdown
turnoverReport.forecasted=Forecasted
turnoverReport.actuals=Actuals
turnoverReport.pdf=PDF
turnoverReport.excel=Excel
turnoverReport.dataSource=Data Source
turnoverReport.outputFormat=Output Format
tooltip.turnoverReportDate=Enter report date
tooltip.calendarturnoverReportdate=Select report date
turnoverReport.showMain=Show Main
turnoverReport.showSub=Show Sub
tooltip.showMain=Select Show Main option
tooltip.showSub=Select Show Sub option
tooltip.reportbutton=Generate the report
turnoverReport.templateSheetName=Turnover Report
button.report=Report
turnoverReport.debit=Debit
turnoverReport.credit=Credit
turnoverReport.currency=Currency
turnoverReport.turnoverdic=Turnover
turnoverReport.reportingperiod=Reporting Period
turnoverReport.reportTitle=Turnover by Currency (Nostro)
turnoverReport.main=Main
turnoverReport.sub=Sub
turnoverReport.to=to

turnoverReport.startdayTooltip=Enter report Start date
turnoverReport.enddayTooltip=Enter report End date

#-- Currency Funding Report --
currencyFunding.mainScreen=Currency Funding Report - SMART-Predict
currencyFunding.entity=Entity
currencyFunding.currency=Currency
currencyFunding.date=Value Date
tooltip.currencyFundingDate=Enter value date
tooltip.calendarcurrencyFundingdate=Select value date
currencyFunding.account=Account
tooltip.selectAccount=Select account
currencyFunding.threshold=Threshold
tooltip.currencyFundingThreshold=Enter threshold value
currencyFunding.showDR=Show DR
currencyFunding.showCR=Show CR
tooltip.selectShowDR=Select show DR option
tooltip.selectShowCR=Select show CR option
button.report=Report
tooltip.report=Generate the report
button.cancel=Cancel
tooltip.cancelbutton=Cancel changes and exit
email.configRecipients.title= Configure Recipients  - SMART-Predict
scenario.role.selectAll.label= Select All
scenario.role.selectAll.tooltip= Select All

#-- Reason Maintenance --
reasonMaintenance.mainTitle=Reason Maintenance - SMART-Predict
reasonMaintenance.entity=Entity
reasonMaintenance.reasonCode=Reason Code
reasonMaintenance.description=Description
reasonCodeTooltip.sortreasonCode=Sort by reason code
descriptionTooltip.sortdescription=Sort by description
tooltip.AddNewReasonCode=Add new reason
tooltip.ChangeSelectedReasonCode=Change selected reason
tooltip.DeleteSelectedReasonCode=Delete selected reason
reasonMaintenance.addScreen=Add Reason Maintenance
reasonMaintenance.changeScreen=Change Reason Maintenance
tooltip.ReasonCode=Enter reason code
tooltip.Description=Enter description

sweepDetails.title.window=Sweep Details - SMART-Predict
sweepDetails.alert.amount=Sweep Amount should be greater than 0
sweepDetails.alert.amountLimits=Proposed Sweep Amount exceeds the maximum single payment limit for account
sweepDetails.alert.microsoft=Microsoft Internet Explorer
sweepDetails.alert.sweepamount=Sweep amount less than minimum sweep amount of account
sweepDetails.alert.sweeptarget=Align account to target?
sweepDetails.alert.sweeperror=.<BR> Do you want to continue?
sweepDetails.alert.sweeperror1=Cut off time violation.<BR>
sweepDetails.alert.fieldChanged=WARNING: Changing this field could severely impact the business process.<BR>Are you sure?
sweepDetails.unlockAll=Unlock All
tooltip.sweepAccount=Account Details
tooltip.sweepCheck=Change align to target
tooltip.sweepOk=OK
SweepDisplay.msg=Msgs

#-- Message Display Format --
tooltip.Message=Format
tooltip.GeneratedOnDate=Date
tooltip.GeneratedOnTime=Time
tooltip.Status=Status
SweepMsgDisplay.Message=Format
SweepMsgDisplay.GeneratedOnDate=Date
SweepMsgDisplay.GeneratedOnTime=Time
SweepMsgDisplay.Status=Status
SweepMsgDisplay.title.mainWindow=Sweep Message Summary
MovMsgDisplay.title.mainWindow=Movement Message Summary
MovMsgDisplay.Message=Message

#-- Unique keys for tooltips of Maintenance --
tooltip.sortCutOffOffset=Sort by cut off offset
tooltip.addNewAccount=Add new account
tooltip.reqconoffered=N - Not Required, C - Confirmed, M - Offered
tooltip.enterNewAcId=Enter new account ID
tooltip.enterAcName=Enter account name
tooltip.enterNewAcName=Enter new account name
tooltip.changeSelAcName=Change selected account name
tooltip.enterNewCurrId=Enter new currency code
tooltip.cash=Cash
tooltip.custodain=Custodian
tooltip.ext=Ext
tooltip.int=Int
tooltip.main=Main
tooltip.sub=Sub
tooltip.subAc=Sub-account
tooltip.assAcct=Define additional accounts to be summed when calculating a sweep based on an aggregated balance
tooltip.acctSpecBtn=Click to view account specific
tooltip.enterNewExtraID=Enter new Extra ID
tooltip.changeSelExtraID=Change selected Extra ID
tooltip.enterNewAcGL=Enter new account GL code
tooltip.changeSelAcGL=Change selected account GL code
tooltip.selectNewAcStatus=Select a new account status
tooltip.viewAcStatus=View an account status
tooltip.selectAcStatus=Select an account status
tooltip.selectNewAcStatus=Select a new account status
tooltip.enterNewAcBic=Enter new account BIC
tooltip.changeSelAcBic=Change selected account BIC
tooltip.enterNewAcBic=Enter new account BIC
tooltip.enterNewCountry=Enter new country
tooltip.viewSelCountry=View selected country
tooltip.changeSelCountry=Change selected country
tooltip.selectNewCountry=Select new country
tooltip.enterNewCorrCode=Enter new correspondent code
tooltip.changeSelCorrCode=Change selected correspondent code
tooltip.sortAcType=Sort by account type
tooltip.sortAcLevel=Sort by account level
tooltip.changeSelAc=Change selected account
tooltip.viewSelAc=View selected account
tooltip.deleteSelAc=Delete selected account
tooltip.viewCurrAc=View current account
tooltip.submit=Submit
tooltip.stp=STP
tooltip.notincluded=Not included
tooltip.manSweep=Manual sweeping
tooltip.enterSweepTimeHM=Enter sweep time (hh:mm)
tooltip.changeSweepTimeHM=Change sweep time (hh:mm)
tooltip.enterNewSweepDay=Enter new sweep day
tooltip.changeSelSweepDay=Change selected sweep day
tooltip.enterMinSweepAmount=Enter minimum sweep amount
tooltip.changeMinSweepAmount=Change minimum sweep amount
tooltip.enterMaxSweepAmount=Enter maximum sweep amount
tooltip.enterTarBal=Enter target balance
tooltip.changeTarBal=Change target balance sign
tooltip.selectTarBalSign=Select target balance sign
tooltip.enterCutOffTime=Enter cut off time (hh:mm)
tooltip.changeCutOffTime=Change cut off time (hh:mm)
tooltip.selectNewCrMsg=Select new credit message format
tooltip.selectCrMsg=Select credit message format
tooltip.selectNewCrMsg=Select new credit message format
tooltip.selectNewCrMsgCancel=Select new credit message cancel
tooltip.selectCrMsgCancel=Select credit message cancel
tooltip.selectNewCrMsgCancel=Select new credit message cancel
tooltip.selectNewDrMsg=Select new debit message format
tooltip.selectDrMsg=Select debit message format
tooltip.selectNewDrMsg=Select new debit message format
tooltip.selectNewDrMsgCancel=Select new debit message cancel
tooltip.enterNewCrInt=Enter new credit interest rates
tooltip.changeCrInt=Change credit interest rates
tooltip.enterNewOverdraft=Enter new overdraft rates
tooltip.changeOverdraft=Change overdraft rates
tooltip.selectBalType=Select balance type
tooltip.selectBalDate=Enter balance date
tooltip.sortByIdentifier=Sort by identifier
tooltip.sortByName=Sort by name
tooltip.sortByBalance=Sort by balance
tooltip.sortByUserId=Sort by user ID
tooltip.sortByInputDate=Sort by input date
tooltip.sortByInputTime=Sort by input time
tooltip.changeSelBal=Change selected balance
tooltip.EnterBalSelBalParam=Enter balance for selected balance parameter
tooltip.sortAccountName=Sort by account name
tooltip.CurrencyIdentifier=Currency identifier
tooltip.DeleteSelectedCurrency=Delete selected currency if unused
tooltip.sortBookcodeName=Sort by bookcode name
tooltip.sortGrLevelName1=Sort by 
tooltip.sortGrLevelName2=Sort by 
tooltip.sortGrLevelName3=Sort by 
tooltip.addNewBookcode=Add new bookcode
tooltip.changeSelBookcode=Change selected bookcode
tooltip.deleteSelBookcode=Delete selected bookcode
tooltip.EntergrIdLevel1=Enter group ID level 1
tooltip.clickSelGrLevel1=Click to select group ID level 1
tooltip.EntergrIdLevel2=Enter group ID level 2
tooltip.clickSelGrLevel2=Click to select group ID level 2
tooltip.EntergrIdLevel3=Enter group ID level 3
tooltip.clickSelGrLevel3=Click to select group ID level 3
tooltip.accountId=Account ID
tooltip.clickSelAcId=Click to select account ID
tooltip.selectFormatId=Select format ID
tooltip.copySelFormat=Copy selected format
tooltip.selectCurr=Select currency
tooltip.copyMatchQualityProfile=Copy this match quality profile
tooltip.sortCurrName=Sort by currency name
tooltip.sortExchRate=Sort by exchange rate
tooltip.sortTolerance=Sort by tolerance
tooltip.sortPreflag=Sort by Predict flag
tooltip.addNewCurr=Add new currency
tooltip.changeSelCurr=Change selected currency
tooltip.deleteSelCurr=Delete selected currency
tooltip.currencyIdentifier=Currency identifier
tooltip.interestBasis=Interest basis
tooltip.tolerance=Tolerance
tooltip.cutOffTimeHM=Cut off time (hh:mm)
tooltip.enableCurrPredict=Enable this currency in Predict
tooltip.disableCurrPredict=Disable this currency in Predict
tooltip.enterEntityId=Enter entity ID
tooltip.mgrpLvlID1=Metagroup Level
tooltip.partyId=Party ID
tooltip.postionLevel=Select the position level
tooltip.ccyGrp=Currency Group
tooltip.entityId=Entity ID
tooltip.entityName=Enter entity name
tooltip.domesticCurr=Domestic currency
tooltip.reportingCurr=Reporting currency
tooltip.countryCode=Country code
tooltip.entityOffSetCentralServer=Entity offset time to central server
tooltip.firstWeekendDay=First weekend day
tooltip.secondWeekendDay=Second weekend day
tooltip.selectDomCCY=Select domestic ccy/ccy
tooltip.selectCCyDom=Select ccy/domestic ccy
tooltip.movRetParam=Movement retention parameter
tooltip.inputRetParam=Input retention parameter
tooltip.outputRetParam=Output retention parameter
tooltip.cashFilter=Cash filter
tooltip.securitiesFilter=Securities filter
tooltip.metaLevel1=Metagroup level 1 name
tooltip.metaLevel2=Metagroup level 2 name
tooltip.metaLevel3=Metagroup level 3 name
tooltip.groupLevel1=Group level 1 name
tooltip.groupLevel2=Group level 2 name
tooltip.groupLevel3=Group level 3 name
tooltip.addEntity=Add entity
tooltip.changeEntity=Change entity
tooltip.deleteEntity=Delete entity
tooltip.selectDateFormatDMY=Select date format (DD/MM/YYYY)
tooltip.selectDateFormatMDY=Select date format (MM/DD/YYYY)
tooltip.selectAmountFormat=Select amount format
tooltip.systemDateTesting=System date for testing
tooltip.systemGmtOffset=Define the database server time offset relative to GMT. Used in liquidity analysis functionality for determining currency timeframe
tooltip.systemEnableDst=Apply daylight saving adjustments when calculating ILM time series data
tooltip.systemDstStartDate=Date on which summertime daylight saving period begins
tooltip.systemDstEndDate=Date on which summertime daylight saving period ends
tooltip.enterDaysRetainSysLog=Enter number of days to retain system log
tooltip.enterDaysRetainMaintenanceLog=Enter number of days to retain maintenance log
tooltip.enterDaysRetainErrorLog=Enter number of days to retain error log
tooltip.enterDaysRejectedSuppressedInput=Enter number of days to retain rejected suppressed input
tooltip.changeGenSysParam=Change general system parameters
tooltip.selectEntityId=Select an entity ID
tooltip.sortGroupId=Sort by group ID
tooltip.sortGroupName=Sort by group name
tooltip.sortMetaGroupId=Sort by metagroup ID
tooltip.sortCutOff Offset=Sort by cut off offset
tooltip.addNewGroup=Add new group
tooltip.changeSelGroup=Change selected group
tooltip.deleteSelGroup=Delete selected group
tooltip.viewBookSelGroup=View books for selected group
tooltip.enterGroupIdentifier=Enter group identifier
tooltip.enterGroupName=Enter group name
tooltip.positionLevel=Select position level
tooltip.debit=Debit
tooltip.addAccountCredit=Credit
tooltip.date=Date
tooltip.selectMetaGroupLevel=Select metagroup level
tooltip.sortBookId=Sort by book ID
tooltip.sortBookName=Sort by book name
tooltip.sortBookLocation=Sort by location
tooltip.sortSwiftInOutDirection=Sort by SWIFT input/output direction
tooltip.none=None
tooltip.incoming=Incoming
tooltip.outgoing=Outgoing
tooltip.sort InternalFlag=Sort by internal flag
tooltip.sortGrId=Sort by group ID
tooltip.sortGrName=Sort by group name
tooltip.sortTotalNoBooks=Sort by total no. of books
tooltip.sortParty=Sort by party
tooltip.changeSelParty=Change selected party
tooltip.enterPartyId=Enter party ID
tooltip.enterPartyName=Enter party name
tooltip.selectCustFlag=Select for custodian flag
tooltip.sortAuthFlag=Sort by authorisation flag
tooltip.sortOutputType=Sort by output type
tooltip.sortFileName=Sort by file name
tooltip.sortPath=Sort by path
tooltip.sortMGId=Sort by metagroup ID
tooltip.sortMGName=Sort by metagroup Name
tooltip.sortFinTrade=Sort by Finance/Trade
tooltip.sortNoGroups=Sort by no. of groups
tooltip.addNewMetaGroup=Add new metagroup
tooltip.saveMG=Save metagroup
metagroupmaintenance.alert.validString=Please enter a valid string
tooltip.changeSelMG=Change selected metagroup
tooltip.deleteSelMetaGroup=Delete selected metagroup
tooltip.viewGroup=View group
tooltip.enterMGID=Enter new metagroup ID
tooltip.enterMGName=Enter metagroup name
tooltip.selectMGCategory=Select metagroup category
tooltip.sortMsgId=Sort by message ID
tooltip.sortMsgName=Sort by message name
tooltip.sortCountryCode=Sort by country code
tooltip.sortHolidayDate=Sort by holiday date
tooltip.addNewHoliday=Add new holiday
tooltip.deleteSelHoliday=Delete selected holiday
tooltip.selectCurr=Select currency
tooltip.notReqConOffered=N - Not Required, C - Confirmed, M - Offered
tooltip.cpyMatchQuality=Copy match quality
tooltip.addNewMatchQuality=Add new match quality
tooltip.changeNewMatchQuality=Change selected match quality
tooltip.viewSelMatchQuality=View selected match quality
tooltip.deleteSelMAtchQuality=Delete selected match quality
tooltip.enterSeqNo=Enter sequence number
tooltip.selectText=Select text
tooltip.selectKeyword=Select keyword
tooltip.selectHexa=Select hexadecimal
tooltip.enterValue=Enter value
tooltip.enterStartPosition=Enter start position
tooltip.enterEndPosition=Enter end position
tooltip.sortSeqNo=Sort by sequence no.
tooltip.sortLineNo=Sort by line no.
tooltip.sortStartPosition=Sort by start position
tooltip.sortEndPosition=Sort by end position
tooltip.sortFieldType=Sort by field type
tooltip.sortValue=Sort by value
tooltip.selectFixed=Select fixed
tooltip.selectSubmit=Select submit
tooltip.selectDelimite=Select delimited
tooltip.selectTagged=Select tagged
tooltip.selectSweep = Select Sweep
tooltip.selectOther = Select Other
tooltip.selectFormatReqAuth=Select if format type requires authorisation
tooltip.enterFieldDelimiter=Enter field delimiter
tooltip.enterHexaFieldDelimiter=Enter hex field delimiter
tooltip.enterMsgSeparator=Enter message separator
tooltip.enterHexMsgSeparator=Enter hex message separator
tooltip.selectFile=Select file
tooltip.selectMQInterface=Select MQ interface
tooltip.SortByExchangeRate=Sort by exchange rate
tooltip.SortByInterestBasis=Sort by interest basis
tooltip.SortByTolerance=Sort by tolerance
tooltip.SortByCutofftime=Sort by cut off time
tooltip.sortByTimeZoneOffset=Sort by Time Zone Offset
tooltip.SortByGmtOffset=Sort by GMT offset
tooltip.SortByEnableDst=Sort by Enable Daylight Saving
tooltip.SortByDstStartDate=Sort by Start Date
tooltip.SortByDstEndDate=Sort by End Date
tooltip.SortByPreflag=Sort by Predict flag
entity.CloseConfirm=You have chosen to close the window. All unsaved changes will be lost. Are you sure?
entity.EnterEntityID=Enter entity ID
entity.EnterEntityName=Enter entity name
entity.CountryCode=Country code
entity.EntityOffsetTimetoCentralServer=Entity offset time to central server
entity.FirstWeekendDay=First weekend day
entity.SecondWeekendDay=Second weekend day
entity.SelectDomesticCCY/CCY=Select domestic ccy/ccy
entity.SelectCCY/DomesticCCY=Select ccy/domestic ccy
entity.MovementRetentionParameter=Movement retention parameter
entity.InputRetentionParameter=Input retention parameter
entity.OutputRetentionParameter=Output retention parameter
entity.ilmRetain=ILM Data
entity.MetagroupLevel1Name=Metagroup level 1 name
entity.MetagroupLevel2Name=Metagroup level 2 name
entity.MetagroupLevel3Name=Metagroup level 3 name
entity.GroupLevel1Name=Group level 1 name
entity.GroupLevel2Name=Group level 2 name
entity.GroupLevel3Name=Group level 3 name
entity.AddEntity=Add entity
entity.ChangeEntity=Change entity
entity.DeleteEntity=Delete entity
tooltip.enterHolidayDate=Enter holiday date
tooltip.selectHolidayDate=Select holiday date
tooltip.sortHolidayDay=Sort by holiday day
tooltip.sortGroupLevel=Sort by group level
tooltip.enterCutOffHM=Enter cut off offset (hh:mm)
tooltip.saveEntityId=Save entity ID
tooltip.selectDelimited=Select delimited
tooltip.changeMaxSweepAmount=Change maximum sweep amount
tooltip.EnterNewCrInterestRates=Enter new credit interest rates
tooltip.ChangeCrInterestRates=Change credit interest rates
tooltip.EnterNewOverdraftRates=Enter new overdraft rates
tooltip.ChangeOverdraftRates=Change overdraft rates
tooltip.selectLine=Enter Line
tooltip.selectInterestRateDate=Select interest rate date
tooltip.selectFromDate=Select from date
tooltip.selectToDate=Select to date
tooltip.sortByCurrencyCode=Sort by currency code
tooltip.sortByInterestRateDate=Sort by date of interest rate
tooltip.addNewCurrencyInterest=Add new currency interest
tooltip.changeSelCurrencyInterest=Change selected currency interest
tooltip.delSelCurrencyInterest=Delete selected currency interest
tooltip.ilmRetain=Enter number of days to retain ILM datasets
CurrencyInterestmaintenance.title.window=Currency Interest Rate Maintenance - SMART-Predict
CurrencyInterest.addScreen=Add Currency Interest - SMART-Predict
CurrencyInterest.changeScreen=Change Currency Interest - SMART-Predict
interest.tooltip.sortByName=Currency Name
interest.tooltip.sortByInterestRateDate=Sort by interest rate date
interest.tooltip.sortByInterestRate=Interest Rate
interest.tooltip.updateDate=Update date & time
interest.tooltip.sortByUserId=User ID
label.internalBalance=Internal Balance

button.ratesAdd=Add Rate
button.ratesChange=Change Rate
button.ratesDelete=Delete Rate
title.rates=Account Interest Rate
title.rates.add=Add Account Interest Rate
title.rates.change=Change Account Interest Rate
movSearch.title.mainWindow=Movement Search - SMART-Predict
movementsearch.criteria.title= Additional Search Criteria  - SMART-Predict
movSearch.alert.amountunder=Amount Under should be greater than Amount Over
movSearch.alert.amountoverLess=Amount Over must be less than Amount Under
movSearch.alert.time=From time must be earlier than To time
movSearch.alert.valueFrom=Value From Date should be equal to or greater than system date
movSearch.alert.dateComparison=should be greater than or equal to
movSearch.alert.dateComparison1='
tooltip.movStatus=Select match status
tooltip.movPredict=Select Predict status
tooltip.movSign=Select sign
tooltip.movMetagroup=Select metagroup category
tooltip.movType=Select movement type
tooltip.movCustodian=Click to select custodian ID
tooltip.movBeneficiary=Click to select beneficiary ID

tooltip.movCurrency=Select currency ID
label.movUnder=Under
label.diff=Diff
tooltip.clickSelMetaGroupId=Click to select metagroup ID
movementsearch.status.authorise=Authorise
movementsearch.status.referred=Referred
movementsearch.status.reconcilled=Reconciled
movementsearch.account.nostro=Nostro
movementsearch.account.current=Current
movementsearch.account.loro=Loro
movementsearch.account.other=Other
movementsearch.account.fielset=Account Class
movementsearch.criteria.fielset= Additional Search Criteria
movementsearch.account.selectaccountclass=Select Account Class
tooltip.accountClass=Select Account Class
#-- Unique keys for tooltips of Work --
ShowErrMsgWindowWithBtn.errorMessage1=Microsoft Internet Explorer
ShowErrMsgWindowWithBtn.errorMessage2=Sweep has already been generated for the selected accounts
ShowErrMsgWindowWithBtn.errorMessage3=Do you want to continue?
alert.mvmQSelectionStChange=Status has changed. Do you want to refresh?
ShowErrMsgWindowWithBtn.errorMessage1=
msg.title.notesAvailable=Notes available
msg.title.alertAvailable=Alert available
tooltip.selectArchiveId=Select the Archive ID
tooltip.selectCurrencyCode=Select currency code
tooltip.selectAccountType=Select account type
tooltip.sortAccountId=Sort by account ID
tooltip.sortPredictBalance=Sort by Predict balance
tooltip.sortFinalBalance=Sort by final balance
tooltip.file=Enter file name
tooltip.msgID=Enter message ID
tooltip.msgname=Enter message name
tooltip.selectMQInterface=Select MQ interface
tooltip.enterpath=Enter path
tooltip.sortbyformattype=Sort by format type
tooltip.sortOutPredictStatus=Sort by out Predict balance
tooltip.sortOutFinalBalance=Sort by out final balance
tooltip.sortStartingBalance=Sort by starting balance
tooltip.sortInPredictStatus=Sort by in Predict balance
tooltip.sortInFinalBalance=Sort by in final balance
tooltip.printScreen=Print screen content
tooltip.refreshWindow=Refresh window
tooltip.resolveButton=Resolve alert instance
tooltip.reActivateButton=Re-activate
tooltip.manualSweep=Manual sweep
tooltip.printDetails=Print details
tooltip.enterMvmId=Enter movement ID
tooltip.ok=OK
tooltip.selectFixed=Select fixed
tooltip.selectMultiline=Select multi-line
tooltip.selectMultilineVariablesFields=Select Multi-Line - Variable Fields
tooltip.searchMvmId=Search movement ID
tooltip.displayMvmDetails=Display movement details
tooltip.notesAvailable=Notes available
tooltip.MvmID=Movement ID
tooltip.MatchID=Match ID
tooltip.bookCode=Bookcode ID
tooltip.includeMvmInMonitors=Include movement in dealer monitors
tooltip.selectInclMvmInMonitors=Select include movement in dealer monitors
tooltip.exlMvmDealerMons=Exclude movement from dealer monitors
tooltip.selectExlMvmDealerMons=Select exclude movement from dealer monitors
tooltip.cancelMvm=Cancel movement
tooltip.selectCancelMvm=Select cancel movement
tooltip.includeMvmInDataEx=Include movement in data extract
tooltip.selectIncMvmInDataEx=Select include movement in data extract
tooltip.excMvmInDataEx=Exclude movement from data extract
tooltip.selectExcMvmInDataEx=Select exclude movement from data extract

tooltip.includeMvmInIlmFcast=Include in ILM forecast
tooltip.exlMvmFromIlmFcast=Exclude from ILM forecast

tooltip.includeMvmInInitMonitors=Include movement in dealer monitors
tooltip.exlMvmInitMonitors=Exclude movement from dealer monitors

tooltip.changeSelectMvm=Change selected movement
tooltip.viewMatchSelMvm=View match of selected movement
tooltip.enterNotes=Enter notes
tooltip.logSelMvm=Log of selected movement
tooltip.msgSelMvm=Messages on selected movement
tooltip.SaveChanges=Save changes
tooltip.CancelChanges=Cancel changes
tooltip.copyExMvm=Copy details from an existing movement
tooltip.viewMatchSelMvm=View match of selected movements
tooltip.cancel=Cancel changes and exit
tooltip.enterValueDate=Enter value date (DD/MM/YYYY)
tooltip.ValueDateMMDDYY=Enter value date (MM/DD/YYYY)
tooltip.selectFromDateDDMMYY=Enter From date (DD/MM/YYYY)
tooltip.selectToDateDDMMYY=Enter To date (DD/MM/YYYY)
tooltip.selectFromDateMMDDYY=Enter From date (MM/DD/YYYY)
tooltip.selectToDateMMDDYY=Enter To date (MM/DD/YYYY)
tooltip.clickValueDate=Click to select value date
tooltip.enterAmount=Enter amount
tooltip.selectAmountSign=Select amount sign
tooltip.cash=Cash
tooltip.securities=Securities
tooltip.accountID=Account ID

tooltip.bookCode=Bookcode
tooltip.enterMvmRef1=Enter movement reference 1
tooltip.enterMvmRef2=Enter movement reference 2
tooltip.enterMvmRef3=Enter movement reference 3
tooltip.enterMvmExtraText=Enter movement extra text
tooltip.selectPosLevel=Select position level
tooltip.includeMvmInMonitors=Include movement in dealer monitors
tooltip.exlMvmDealerMons=Exclude movement from dealer monitors
tooltip.cancelMvm=Cancel movement
tooltip.includeMvmInDataEx=Include movement in data extract
tooltip.excMvmInDataEx=Exclude movement from data extract
tooltip.counterPartId=Enter Counterparty ID
tooltip.enterCPartytext=Enter Counterparty text
tooltip.enterCPartytext1=Enter Counterparty text 1
tooltip.enterCPartytext2=Enter Counterparty text 2
tooltip.enterCPartytext3=Enter Counterparty text 3
tooltip.enterCPartytext4=Enter Counterparty text 4 
tooltip.enterCPartytext5=Enter Counterparty text 5
tooltip.benID=Enter Beneficiary Institution ID
tooltip.enterBenText1=Enter Beneficiary Institution text 1
tooltip.enterBenText2=Enter Beneficiary Institution text 2
tooltip.enterBenText3=Enter Beneficiary Institution text 3
tooltip.enterBenText4=Enter Beneficiary Institution text 4
tooltip.enterBenText5=Enter Beneficiary Institution text 5
tooltip.custId=Enter Custodian ID
tooltip.enterCustText=Enter Custodian text
tooltip.enterCustText1=Enter Custodian text 1
tooltip.enterCustText2=Enter Custodian text 2
tooltip.enterCustText3=Enter Custodian text 3
tooltip.enterCustText4=Enter Custodian text 4
tooltip.enterCustText5=Enter Custodian text 5
tooltip.copyExMvm=Copy details from an existing movement
tooltip.viewMatchSelMvm=View match of selected movements
tooltip.copyExMvm=Copy details from an existing movement
tooltip.SaveChanges=Save changes
tooltip.cancel=Cancel changes and exit
tooltip.sortMvmLevel=Sort by movement level
tooltip.sortSweepAmount=Sort by sweep amount
tooltip.sortTargetBalance=Sort by target balance
tooltip.sortCutOffTime=Sort by cut off time
tooltip.sortMvmLevel=Sort by movement level
tooltip.sortAccountId=Sort by account ID
tooltip.sortPredictBalance=Sort by Predict balance
tooltip.sortSweepAmount=Sort by sweep amount
tooltip.sortTargetBalance=Sort by target balance
tooltip.sortCutOffTime=Sort by cut off time
tooltip.sortMvmLevel=Sort by movement level
tooltip.sortAccountId=Sort by account ID
tooltip.refreshWindow=Refresh window
tooltip.sweep=Sweep
tooltip.matchNotes=Match notes
tooltip.previousMatch=Previous match
tooltip.nextMatch=Next match
tooltip.showSelMovDetail=Show selected movement in detail
tooltip.suspMatch=Suspend match
tooltip.unMatch=Unmatch all movements and delete this match
tooltip.ConfMatch=Confirm match
tooltip.removeSelMov=Remove selected movement
tooltip.addMov=Add movement
tooltip.selSortOrder=Select sort order
tooltip.amountOver=Enter amount over
tooltip.amountUnder=Enter amount under
tooltip.canceelSelSweep=Cancel selected sweep(s)
tooltip.searchSweep=Search sweep
tooltip.user=Sort by user
tooltip.refreshScreen=Refresh screen
tooltip.canceelSelSweep=Cancel selected sweep(s)
tooltip.sortSweepDTU=Sort by sweep date/time/user
tooltip.sortCrAccID=Sort by CR account ID
tooltip.sortDrAccID=Sort by DR account ID
tooltip.sortCrAccMsgType=Sort by CR account msg type
tooltip.sortDrAccMsgType=Sort by DR account msg type
tooltip.BookBrkdown=Select Book to view Book Monitor
tooltip.MvmntBrkdown=Select Movement to view Movement Summary Detail
tooltip.AcuntBrkdown=Select Account to view Account Monitor

#-- Archive search --
archiveSearch.title.mainWindow=Archive Search - SMART-Predict
tooltip.clickSelCurrId=Click to select currency ID
tooltip.enterCurrency=Enter a currency ID
tooltip.clickSelMsgId=Click to select message type


tooltip.enterCounterId=Enter a counter ID
tooltip.enterBeneId=Enter a beneficiary ID
tooltip.enterCustodianId=Enter a custodian ID
tooltip.enterBookId=Enter a book ID
tooltip.enterGroupId=Enter a group ID
tooltip.enterMetaGrpId=Enter a metagroup ID
tooltip.enterActId=Enter a account ID
tooltip.enterposLevel=Enter a position level
tooltip.clickSelPosId=Click to select position level
tooltip.crdIntMsg=Credit internal message
tooltip.crdExtMsg=Credit external message
tooltip.drIntMsg=Debit internal message
tooltip.drExtMsg=Debit external message
tooltip.sortSweeptype=Sort by sweep type
tooltip.sortSweepUser=Sort by sweep user
tooltip.sortSweepID=Sort by sweep ID
tooltip.sortFirstPositionLevel=Sort by first position level
tooltip.sortInterimPositionLevel=Sort by interim position level
tooltip.sortFinalPositionLevel=Sort by final position level
tooltip.sortTotal=Sort by total
tooltip.sortCurrencyName=Sort by currency name
tooltip.changeAlignTarget=Change align to target
tooltip.changeOriginalSweepAmount=Change original sweep amount
tooltip.changeSubmitSweepAmount=Change submit sweep amount
tooltip.changeAuthSweepAmount=Change authorise sweep amount
tooltip.submit=Submit
tooltip.authorize=Authorise
tooltip.notes=Notes
tooltip.sweepNotes=Sweep Notes
tooltip.executeSearch=Execute Search
tooltip.showSweepDetails=Show sweep details
tooltip.SubmitSelSweep=Submit selected sweep(s)
tooltip.AuthorizeSelSweep=Authorise selected sweep(s)
tooltip.enterMatchId=Enter match ID
tooltip.BIC=Sort by BIC/Network
tooltip.enterValueDateTo=Enter value date to
tooltip.enterValueDateFrom=Enter value date from
tooltip.enterTomeFrom=Enter time from
tooltip.enterTimeTo=Enter time to
tooltip.enterMsgType=Enter a message type
tooltip.selectMsgFormat=Select a message format
tooltip.clickSelCounterId=Click to select counter ID
tooltip.enterBIC=Enter the BIC/Network
tooltip.counterId=Counter ID
tooltip.clickSelMatchingParty=Click to select matching party
tooltip.clickSelAccountParty=Click to select account party
tooltip.bookId=Book ID
tooltip.clickSelBookId=Click to select book ID
tooltip.groupId=Group ID
tooltip.clickSelGroupId=Click to select group ID
tooltip.metaGroupId=Metagroup ID
tooltip.clickSelMetaGroupId=Click to select metagroup ID
tooltip.enterInputDate=Enter input date
tooltip.selectInputDate=Select input date
tooltip.enterRef=Enter a reference
tooltip.enterRefIncl=Include movements having references found with this value
tooltip.enterRefExcl=Exclude movements having references found with this value
tooltip.selectPostionLevel=Select a position level
tooltip.selectMovType=Select movement type
tooltip.selectMetagroupCat=Select metagroup category
tooltip.selectSign=Select sign
tooltip.sortCounterPartyId=Sort by counterparty ID
tooltip.viewSweepDisp=View sweep display
tooltip.sortMsgCrAc=Sort by message CR A/C
tooltip.sortMsgDrAc=Sort by message DR A/C
tooltip.EnterSweepID=Enter sweep ID
tooltip.sortByNote=Sort by note
tooltip.Sweep=Sweep
tooltip.SweepStatus=Sort by sweep status
tooltip.SortBySweepValueDate=Sort by sweep value date
tooltip.CreditAct=Sort by CR account
tooltip.DebitAct=Sort by DR account
tooltip.shwmvmntdetails=Show movement details
tooltip.deletemvmntdetails=Delete selected movement
tooltip.authmvmntdetails=Authorise selected movement
tooltip.refmvmntdetails=Refer selected movement
tooltip.submitmvmntdetails=Refer selected movement
tooltip.copy=Copy
tooltip.srchmvmnt=Search movements
tooltip.makeOfferedMatch=Make Offered Match
tooltip.displayMatch=Display Match
tooltip.sortbynotes=Sort by notes
tooltip.addCols= Configure additional columns

tooltip.generateReport=Generate report
tooltip.CopySelectedFormat=Copy selected format
tooltip.SelectCurrency=Select currency
tooltip.msgfield=Message field
tooltip.addnewparty=Add new party
tooltip.selectAccountId=Click to select account ID
tooltip.sortbyParty=Sort by party ID
tooltip.sortbyPname=Sort by party name

tooltip.movAccount=Click to select account ID
tooltip.movBeneficiary=Click to select beneficiary ID
tooltip.BeneficiaryID=Beneficiary ID
tooltip.movCustodian=Click to select custodian ID
tooltip.matchQuality.paramDesc=Sort by parameter
tooltip.matchQuality.matchQuaA=Sort by quality A
tooltip.matchQuality.matchQuaB=Sort by quality B
tooltip.matchQuality.matchQuaC=Sort by quality C
tooltip.matchQuality.matchQuaD=Sort by quality D
tooltip.matchQuality.matchQuaE=Sort by quality E
tooltip.RateWindow=Change refresh rate
tooltip.enterMatchingParty=Enter matching party
tooltip.enterProductType=Enter product type
tooltip.enterPostingDateFrom=Enter posting date from
tooltip.enterPostingDateTo=Enter posting date to
tooltip.enterPostingDate=Enter posting date
tooltip.selectDate=Select date
tooltip.expectedSettlement=Enter Expected Settlement
tooltip.actualSettlment=Enter Actual Settlement
tooltip.criticalPaymentType=Enter Critical Payment Type

tooltip.HideAccountsAfterCutoff=Hide Accounts After Cut-off
button.tooltip.hidecutoffcutoff=Do not display accounts that have past the cut-off time
button.tooltip.clearAccounts=Clear Selected Accounts

#-- ILM analysis Monitor tooltips --
tooltip.timeframe=Timeframe of the currency
tooltip.currencytimeframe=Click to show scale in entity timeframe
tooltip.entitytimeframe=Click to show scale in currency timeframe
tooltip.refreshevery=Enter Refresh Every 
tooltip.maintainGrpButton=Maintain Selected Group
tooltip.maintainScnButton=Maintain Selected Scenario
tooltip.setStyleButton=Change the style of visible series

#-- Unique keys for buttons --
button.close=Close
button.add=Add
button.advanced=Advanced
button.change=Change
button.view=View
button.delete=Delete
button.role=Role
button.cpyFrom=CpyFrom
button.save=Save
button.test=Test
button.cancel=Cancel
button.ok=OK
button.include=Include
button.exclude=Exclude
button.yes=Yes
button.no=No
button.copy=Copy
button.books=Books
button.fields=Fields
button.groups=Groups
button.previous=Previous
button.notes=Notes
button.mvmnt=Mvmnt
button.suspend=Suspend
button.unmatch=Unmatch
button.confirm=Confirm
button.remove=Remove
button.parms=Parms
button.match=Match
button.search=Search
button.submit=Submit
button.auth=Auth
button.Refer=Refer
button.Refresh=Refresh
button.ManSwp=ManSwp
button.display=Display
button.log=Log
button.editreason=Reason
button.message=Message
button.copy=Copy
button.details=Details
button.sweep=Sweep
button.execute=Execute
button.send=Send
button.unlock=Unlock
button.setRun=SetRun
button.menu=Menu*
button.queue=Queue
button.limits=Limits
button.kill=Kill
button.send=Send
button.execute=Execute
button.entity=Entity*
button.send=Send
button.execute=Execute
button.next=Next
button.susprnd=Suspend
button.sweep=Sweep
button.details=Details
button.enable=Enable
button.disable=Disable
button.rate=Rate
button.Filter=Filter
button.addCols= Additional Columns
button.NAK=NAK
button.reconcile=Recon
button.print=Print
button.printall=PrintAll
button.category=Category
button.attributes=Attributes
button.maintain=Maintain
button.go=Go
button.update=Update

cpyFromManualInput.title.window=Copy From Manual Input - SMART-Predict
cpyFromManualInput.alert.mvmNotOnFile=Movement not on file
cpyFromManualInput.copyFrm=copyFrm
cpyFromManualInput.alert.rights=Rights for the entity of selected movement do not exist. Please select another movement
cpyFromManualInput.alert.rightsCcyGrp=Rights for the currency of selected movement do not exist. Please select another movement
mvmDisplay.title.window=Movement Display - SMART-Predict
manualInput.title.window=Manual Input - SMART-Predict
manualInput.alert.mvmSaved=Movement created with ID 
manualInput.alert.chkStatus=WARNING: Item queued for Authorisation
manualInput.alert.mvmNotonFile=Enter a valid Movement ID
manualInput.alert.mvmIdamended=Movement ID field has been amended - please choose action again after screen refreshes
movementAdd.title.Window=Add Movement - SMART-Predict
manSweeping.title.window=Manual Sweep - SMART-Predict
movementDisplay.newValueDate=New Value Date
tooltip.rollSelectMvm=Roll selected movement
button.roll=Roll
movementrollover.title.window=Movement Rollover - SMART-Predict
tooltip.enterNewValueDate=Enter new value date
tooltip.clickNewValueDate=Click to select new value date
movementDisplay.originalValueDate=Original
movementRolledNoteText=Movement rolled over from movement ?, original value date ?
movementOriginalNoteText=Movement rolled over to movement ?, new value date ?
movementValueDate.alert.Validate=The value date entered falls on defined holiday or weekend. Please change
tooltip.clickNewValueDate=Click to select new value date
matchRolledNoteText=Match rolled over from match ?
matchOriginalNoteText=Match rolled over to match ?
movementDisplay.openStatus=OPEN
button.Open=Open
button.Unopen=Unopen
tooltip.openSelectMvm=Open selected movement
tooltip.unopenSelectMvm=Unopen selected movement
confirm.open=Are you sure you want to set the selected movement to Open?
confirm.unopen=Are you sure you want to set the selected movement to Unopen?
confirm.delete=Are you sure you want to delete?
confirm.openGeneric=Do you want display data for the given base query?

#-- Tooltip for Notes --
notes.date=Date
notes.time=Time
notes.user=User
notes.note=Notes
movNote.title.window=Movement Notes - SMART-Predict
sweepNotes.title.window=Sweep Notes - SMART-Predict
matchNotes.title.window=Match Notes - SMART-Predict
viewNotes.title.window=View Notes - SMART-Predict
addNotes.title.window=Add Notes - SMART-Predict

#-- Internal Message --
internalmessage.send=Send message to
internalmessage.user=User
internalmessage.role=Role
internalMesgsFrame.title.window=Error
internalMesgs.title.window=Internal Message - SMART-Predict
internalMesgs.alert.logoffUsers1=Message cannot be sent to following users 
internalMesgs.alert.logoffUsers2=because they are not logged on
mvmmatqsel.title.authorised=Offered Match Queue - SMART-Predict
mvmmatqsel.title.suspend=Suspended Match Queue - SMART-Predict
mvmmatqsel.title.confirmed=Confirmed Match Queue - SMART-Predict

#-- Party Maintenance --
party.entityId=Entity
party.partyId=Party
party.partyName=Name
party.custodian=Custodian

#-- General System Parameters --
generalsystem.generalSettings=General
general.closeConfirm=You have chosen to close the window. All unsaved changes will be lost. Are you sure?
generalsystem.retentionParameters=Retention
party.alert.partyname=Please enter a valid party name
party.alert.partyid=Please enter a valid party ID

#-- User Profile --
userprofile.option=Profile Option
userprofile.name=Profile Name
userprofile.default=Default profile
screenProfile.title.window=Screen Profile - SMART-Predict
screenProfile.alert.profileChanged=Profile has been changed

manualMatch.warning.messageForAmtTotals=This match will have different amount totals across position levels. Do you want to continue?
manualMatch.warning.messageForPredictStatus=There are included items at multiple position levels. Do you want to continue?
manualMatch.warning.messageforOnePositionLevel=Only 1 position level is available. Do you want to continue?
manualMatch.warning.messageForAmtTotalsOnLoad=This match will have different amount totals across position levels
manualMatch.warning.messageForPredictStatusOnLoad=There are included items at multiple position levels

#-- Match Quality --
matchQuality.title.mainWindow=Copy From Match Quality - SMART-Predict
matchQuality.changeScreen=Change Match Quality - SMART-Predict
matchQuality.addScreen=Add Match Quality - SMART-Predict
matchQuality.viewScreen=View Match Quality - SMART-Predict
matchQuality.entityId=Entity
matchQuality.currencyCode=Currency
matchQuality.ccyTolerance=Currency tolerance:
matchQuality.difference= Difference:
matchQuality.posLevel=Position Level
matchQuality.posLeveladd=Position Level
matchQuality.currency.idadd=Currency
matchQuality.paramCode=Param Code
matchQuality.paramDesc=Parameter
matchQuality.matchQuaA=A
matchQuality.matchQuaB=B
matchQuality.matchQuaC=C
matchQuality.matchQuaD=D
matchQuality.matchQuaE=E

#-- Bookcode Maintenance --
copyFromFormatId.title.mainWindow=Copy From Format ID - SMART-Predict
book.bookCode=Book
book.bookCodeadd=Book
book.bookName=Name
book.bookNameadd=Book Name
bookCode.entity=Entity
bookCode.bookCode=Book
bookCode.bookName=Name
bookCode.groupIdLevel1=Grp Lev 1
bookCode.add.groupIdLevel1=Group Level 1
bookCode.groupIdLevel2=Grp Lev 2
bookCode.add.groupIdLevel2=Group Level 2
bookCode.groupIdLevel3=Grp Lev 3
bookCode.add.groupIdLevel3=Group Level 3
bookCode.addScreen=Bookcode Add Pop up
bookCode.changeScreen=Bookcode Change Pop up
positionLevel.id.accountId=A/C ID

#-- Pre-advice Display --
preAdviceDisplay.title.window=Balance Maintenance - SMART-Predict
title.preadvice.inputWindow=Pre-advice Input - SMART-Predict
title.preadvice.displayWindow=Pre-advice Display - SMART-Predict
title.preadvice.changeWindow=Change Pre-advice - SMART-Predict
label.preadvice.movement=Movement ID
tooltip.preadvice.cash=Select cash movement type
tooltip.preadvice.securities=Select securities movement type

#-- Balance Maintenance --
tooltip.EnterBalSelBalParam=Enter balance for selected balance parameter
balMaintenance.title.MainWindow=Start of Day Balance Maintenance - SMART-Predict
balanceType=Balance Type
balmaintenance.balanceParamter=Name
balmaintenance.startBalance=Balance
balmaintenance.startOfDayBalance=Start of day balance
balmaintenance.MT950Sod=MT950 SOD
balmaintenance.reasonCode=Reason Code
balmaintenance.reasonDesc=Reason Description
balmaintenance.userNotes=User Notes
startofday.editreason=Edit Reason
balmaintenance.updateUser=User
balmaintenance.updateTime=Input Time
balmaintenance.updateDate=Input Date
type=Type
balance=Balance
date=Date
balance.currency=Currency
balancetype=Type
name=Name
updatedBy=Updated By
identifiers=Identifier
replacebalanceDate=Date
balparameter=Name
balance=Balance
user=Updated By
inputdate=Input Date
inputtime=Input Time
balanceTypeId=Identifiers

#-- General Parameters Maintenance --
dateFormat=Date Format
dateFormatDDMMYY=DD/MM/YYYY
dateFormatMMDDYY=MM/DD/YYYY
amountDelimiter=Amount Format
amountcomabeforedecimal=999,999.99
amountcomaafterdecimal=999.999,99
testDateAsString=System Test Date
systemGmtOffset=System GMT Offset
enableDst=Enable Daylight Saving Adjustment
dstStartDateAsString=Start Date
dstEndDateAsString=End Date
retention=Retention
systemlog=System Log
maintenanceLog=Maintenance Log
errorLog=Error Log
rejectedSuppressedInput=Rejected/Suppressed Input
days=days

#-- ILAAP General Parameters --#
ilaapgeneral.generalSettings=General
ilaapgeneral.entity=Entity
ilaapgeneral.currency=Currency
timeslotSize=Timeslot Size (min)
systemLoggingLevel=System Logging
datasetUpdateInterval=Update Interval
tooltip.timeslotSize=Specify a timeslot size (time interval between data points) that will be used for aggregation of movement data
tooltip.systemLoggingLevel=Specify level of logging required by ILM system processes
tooltip.datasetUpdateInterval=Time in minutes representing how frequently the standard dataset (and associated scenarios) are to be updated
tooltip.changeIlmParam=Change ILM General Parameters

#-- ILAAP Currency Parameter Maintenance --#
ilaapccyparams.entity=Entity
ilaapccyparams.currency=Currency
ilaapccyparams.currencyGrid=Ccy
ilaapccyparams.LVPSGrid=LVPS
ilaapccyparams.CBGroupId=CB Group ID
ilaapccyparams.primaryAccId=Primary Acc. ID
ilaapccyparams.clearingStartGrid=Start
ilaapccyparams.clearingStartTime=Clearing Start Time
ilaapccyparams.clearingEndGrid=End
ilaapccyparams.clearingEndTime=Clearing End Time
ilaapccyparams.globalCcyAcctGrp=Global Currency Account Group
ilaapccyparams.altGlobalGrp=Alternative Global Ccy Group
ilaapccyparams.globalGrp=Global Group
ilaapccyparams.defaultMapTimeGrid=Default
ilaapccyparams.defaultMapTime=Default Map Time
ilaapccyparams.lvpsName=LVPS Name
ilaapccyparams.centralBankGroupId=Central Bank Group ID
ilaapccyparams.primaryAccountId=Primary Account ID
tooltip.entity=Sort by Entity
tooltip.currency=Sort by Currency
tooltip.globalCcyAcctGrp=Sort by Global Currency Account Group
tooltip.defaultMapTime=Sort by Default Map Time
tooltip.LVPSGrid=Sort by LVPS Name
tooltip.CBGroupId=Sort by Central Bank Group ID
tooltip.primaryAccId=Sort by Primary Account ID
tooltip.clearingStartGrid=Sort by Clearing Start Time
tooltip.clearingEndGrid=Sort by Clearing End Time
tooltip.addILMCcy=Add new ILM currency
tooltip.changeILMCcy=Change selected ILM currency
tooltip.viewILMCcy=View selected ILM currency
tooltip.deleteILMCcy=Delete selected ILM currency
ilmccyparams.title.window=ILM Currency Parameter Maintenance
ilmccyparamsAdd.title.window.addScreen=Add ILM Currency Parameter Maintenance
ilmccyparamsAdd.title.window.changeScreen=Change ILM Currency Parameter Maintenance
ilmccyparamsAdd.title.window.viewScreen=View ILM Currency Parameter Maintenance
ilmccyparamsAdd.alert.noCcyOffset=GMT offset is not set for the selected currency
ilmccyparamsAdd.alert.dbLinkFailed=Error: Archive database link is incorrectly set
ilmccyparamsAdd.toolip.globalCcyAcctGrpSelect=Select Global Currency Account Group
ilmccyparamsAdd.toolip.altGlobalGroup=Alternative global currency group. This will be selectable in the Global View tab of the ILM Monitor
ilmccyparamsAdd.toolip.defaultMapTime=Default time to use for credit movements not mentioning a settlement date-time. Specify as HH:MM in currency timeframe.
ilmccyparamsAdd.toolip.LVPSName=Name of the large value payment system for this currency
ilmccyparamsAdd.toolip.centralBankGrpId=ILM Account group relating to central bank account(s)
ilmccyparamsAdd.toolip.primaryAccountId=This field should normally be the main nostro account for the currency.
ilmccyparamsAdd.toolip.clearingStartTime=Indicates the beginning of the business day for the currency. Specify as HH:MM in currency timeframe.
ilmccyparamsAdd.toolip.clearingEndTime=Indicates the end of the business day for the currency. Specify as HH:MM in currency timeframe.
ilmccyparamsAdd.alert.clearingTime=Clearing end time must be later than start time
ilmccyparamsAdd.alert.defaultMapTime=Default map time, when specified must be between clearing start and end times
#-- Entity Maintenance --
entity.id=Entity
entity.entityName=Entity Name
entity.id.entityId=Entity
entity.generalTab=General
entity.predictTab=Predict
entity.general.domesticCurrency=Domestic Currency
entity.general.reportingCurrency=Reporting Currency
entity.general.country=Country
entity.general.serverTomeOffSet=Server Time Offset
entity.general.weekEnd1=Weekend Day 1
entity.general.weekEnd2=Weekend Day 2
entity.general.exchangeRateFormat=Exchange Rate Format
entity.general.exchangeRateFormat.domestic.ccy=Domestic ccy/ccy
entity.general.exchangeRateFormat.ccy.domestic=ccy/Domestic ccy
entity.general.retentionFlag=Retention flag
entity.general.retentionFlag.yes=Yes
entity.general.retentionFlag.no=No
entity.predict.retentionParameter=Retention
entity.predict.retentionParam.movement=Large Movements
entity.predict.retentionParam.input=Input
entity.predict.retentionParam.output=Output
entity.predict.thresholdParameter=Thresholds
entity.predict.thresholdParam.cashFilter=Cash Filter
entity.predict.thresholdParam.securitiesFilter=Securities Filter
entity.predict.metagroupLevelNames=Metagroup Level Names
entity.predict.metagroupLevelNames.level1=Level 1
entity.predict.metagroupLevelNames.level2=Level 2
entity.predict.metagroupLevelNames.level3=Level 3
entity.predict.groupLevelNames=Group Level Names
entity.predict.groupLevelNames.level1=Level 1
entity.predict.groupLevelNames.level2=Level 2
entity.predict.groupLevelNames.level3=Level 3
entity.input=Input
entity.output=Output
entity.allowRecalculation= Allow Re-Calculation for past
tooltip.entity.input=Input Retention Period
tooltip.entity.output=Output Retention Period

#-- Metagroup Maintenance --
metaGroup.entityId=Entity
metaGroup.addpopup=Metagroup Add Popup
metaGroup.id.mgroupId1=Metagroup ID
metaGroup.id.mgroupId1add=Metagroup
metaGroup.id.mgroupId=Metagroup
metaGroup.mgroupName1=Metagroup Name
metaGroup.mgroupName1add=Metagroup Name
metaGroup.mgroupName=Name
metaGroup.mgrpLvlCode1=Metagroup Level
metaGroup.mgrpLvlCode=Level
metaGroup.mgrpLvlID1=Metagroup Level
metaGroup.mgrpLvlID=MetaGrp Level ID
metaGroup.finance=Finance
metaGroup.trade=Trade
metaGroup.financeTrade=Fin/Trade
metaGroup.noofGroups=# Groups
metagroup.groupId=Group
metagroup.groupName=Name
metagroup.groupNameTotal=# Books
metaGroup.category=Category
group.groupId=Group
group.groupName=Name
group.groupIdadd=Group
group.groupNameadd=Name
group.noOfBookCode=Total Book

#-- Group Maintenance --
groupmaintenance.alert.validString=Please enter a valid string
group.groupmaintenanceScreen=Group Maintenance - SMART-Predict
group.entityId=Entity
group.groupId=Group
group.groupName=Name
group.groupLvlCode=Level
group.addgroupLvlCode=Group Level
group.mgroupId=Metagroup
group.cutoffOffset=Offset
group.add.cutoffOffset=Cut off offset
group.addScreen=Add Group - SMART-Predict
group.changeScreen=Change Group - SMART-Predict

#-- Accounts Maintenance --
acctmaintenance.title.cpyfrom=Copy From Account Maintenance - SMART-Predict
accountmaintenance.MaximumMinimum=Maximum sweep amount cannot be less than minimum sweep amount
accountmaintenance.NoSubAc=No sub a/c are defined for this main account
accountmaintenance.AccountLevelCannotChanged=There are sub a/c defined for this main account, so the account level cannot be changed
accountmaintenance.Sweeping=Sweeping
accountmaintenance.title.mainWindow=Account Maintenance - SMART-Predict
accountmaintenance.title.selectAnAccount=Select an Account - SMART-Predict
accountmaintenance.title.addWindow=Add Account - SMART-Predict
accountmaintenance.alert.amountCompare=Maximum sweep amount cannot be less than minimum sweep amount
accountmaintenance.alert.subAccount=No sub a/c are defined for this main account
accountmaintenance.alert.subAcctMainAcct=There are sub a/c defined for this main account, so the account level cannot be changed
accountmaintenance.legend.GenParam=General Parameters
accountmaintenance.legend.Sweeping=Sweeping
accountmaintenance.currentInterestRates=Current Interest Rates
acctMaintenance.entityId=Entity
acctMaintenance.accountId=Account
acctMaintenance.name=Name
acctMaintenance.acctname=Account Name
acctMaintenance.aggAccount=Aggregate Account
acctMaintenance.currcode=Ccy
acctMaintenance.accttype=Type
acctMaintenance.acctlevel=Level
acctMaintenance.acctClass=Class
acctMaintenance.archiveData=Archive data
accountmaintenance.DeletionWithoutArchivingIsRequired=Flagging accounts not to be archived may result in data loss for ILM functionality. Please ensure this account\u2019s data is not required in the archive when unchecking this checkbox
accountmaintenance.AccountIsIlmDataMember=This account is a member of an ILM group and it is therefore likely that its data will be require archiving. Please ensure this account\u2019s data is not required in the archive when unchecking this checkbox
accttype=Account Type
acct.id=Account
acct.currency.id=Currency
acct.name=Account Name
cash=Cash
accgroup=Account Group
ext=EXT
int=INT
acclevel=Account Level
main=Main
sub=Sub
mainacctId=Main Account
generalparameters=General Parameters
acctglcode=Account GL Code
acctextraid=Account Extra ID
acctstatus=Account Status
acctbiccode=Account BIC
country=Country
corrscode=Correspondent Code
swpparameters=Sweeping Parameters
swptime=Sweep Time
swpdays=Sweep Days
minswpamnt=Min Amount
maxswpamnt=Max Amount

targetbalance=Target Balance*
cutofftime=Cut off Time
bookcode=Book
crmsgfrmt=Credit Msg Format
crmsgcancel=Credit Msg Cancel
drmsgfrmt=Debit Msg Format
drmsgcancel=Debit Msg Cancel
crintrates=Current Interest Rates
creditintrates=Credit Interest Rate
overdraftrate=Overdraft Rate
custodian=Custodian
autoswpswitch=Auto Sweep Switch
notincl=Not Included
accountmaintenanceadd.earliestSweepTime=Earliest Sweep Time
accountmaintenanceadd.defaultSettMethod=Default Settlement Method
accountmaintenanceadd.defaulttargetBalance=Default Target Balance
accountmaintenanceadd.assocForSweepBalnce=Assoc. Accs for Sweep Balance
tooltip.assocForSweepBalnce=Number of additional accounts to be summed when  calculating a sweep based on an aggregated balance

acctMaintenance.tooltip.selectAcctType= Select account type
acctMaintenance.label.acctType= Account Type
#account.schedSweep.heading.entityId=Entity ID
#account.schedSweep.heading.accountId=Account ID
#account.schedSweep.heading.scheduleFrom=From
#account.schedSweep.heading.scheduleTo=To
#account.schedSweep.heading.sweepFromBalanceType=Sweep Balance
#account.schedSweep.heading.sweepOnGrpBalance=Sum Accs
#account.schedSweep.heading.targetBalanceType=Target Type
#account.schedSweep.heading.targetBalance=Target Balance
#account.schedSweep.heading.sweepDirection=Direction
#account.schedSweep.heading.minAmount=Min Amount
#account.schedSweep.heading.allowMultiple=Allow Multiple
#account.schedSweep.heading.thisAccSweepBookcodeCr=Book CR
#account.schedSweep.heading.thisAccSweepBookcodeDr=Book DR
#account.schedSweep.heading.thisAccSettleMethodCr=Settle Method CR
#account.schedSweep.heading.thisAccSettleMethodDr=Settle Method DR
#account.schedSweep.heading.sweepEntityId=Entity
#account.schedSweep.heading.sweepAccountId=Account ID
#account.schedSweep.heading.otherAccSweepBookcodeCr=Book CR
#account.schedSweep.heading.otherAccSweepBookcodeDr=Book DR
#account.schedSweep.heading.thisAccSettleMethodCr=Settle Method CR
#account.schedSweep.heading.thisAccSettleMethodDr=Settle Method DR
#account.schedSweep.heading.entityId=Entity
#account.schedSweep.heading.account=Account

account.schedSweep.heading.scheduleFrom=From<br>
account.schedSweep.heading.scheduleTo=To<br>
account.schedSweep.heading.sweepFromBalanceType=Sweep<br>Balance
account.schedSweep.heading.sweepOnGrpBalance=Sum<br>Accs
account.schedSweep.heading.targetBalanceType=Target<br>Type
account.schedSweep.heading.targetBalance=Target<br>Balance
account.schedSweep.heading.sweepDirection=Direction<br>
account.schedSweep.heading.minAmount=Min<br>Amount
account.schedSweep.heading.allowMultiple=Allow<br>Multiple
account.schedSweep.heading.thisAccSweepBookcodeCr=Book<br>CR
account.schedSweep.heading.thisAccSweepBookcodeDr=Book<br>DR
account.schedSweep.heading.thisAccSettleMethodCr=Settle Method<br>CR
account.schedSweep.heading.thisAccSettleMethodDr=Settle Method<br>DR
account.schedSweep.heading.sweepEntityId=Entity<br>
account.schedSweep.heading.sweepAccountId=Account ID<br>
account.schedSweep.heading.otherSweepFromBalType= Sweep<br>Balance
account.schedSweep.heading.otherAccSweepBookcodeCr=Book<br>CR
account.schedSweep.heading.otherAccSweepBookcodeDr=Book<br>DR
account.schedSweep.heading.thisAccSettleMethodCr=Settle Method<br>CR
account.schedSweep.heading.thisAccSettleMethodDr=Settle Method<br>DR
account.schedSweep.heading.entityId=Entity<br>
account.schedSweep.heading.account=Account<br>
account.schedSweep.heading.scheduleFrom=From<br>
account.schedSweep.heading.scheduleTo=To<br>
account.schedSweep.heading.sweepFromBalanceType=Sweep<br>&nbsp;&nbsp;&nbsp;Balance
account.schedSweep.heading.sweepOnGrpBalance=Sum<br>&nbsp;&nbsp;&nbsp;Accs
account.schedSweep.heading.targetBalanceType=Target<br>&nbsp;&nbsp;&nbsp;Type
account.schedSweep.heading.targetBalance=Target<br>&nbsp;&nbsp;&nbsp;&nbsp;Balance
account.schedSweep.heading.sweepDirection=Direction<br>
account.schedSweep.heading.minAmount=Min<br>&nbsp;&nbsp;&nbsp;&nbsp;Amount
account.schedSweep.heading.allowMultiple=Allow<br>&nbsp;&nbsp;&nbsp;&nbsp;Multiple
account.schedSweep.heading.thisAccSweepBookcodeCr=Book CR<br>
account.schedSweep.heading.thisAccSweepBookcodeDr=Book DR<br>
account.schedSweep.heading.thisAccSettleMethodCr=Settle Method<br>CR
account.schedSweep.heading.thisAccSettleMethodDr=Settle Method<br>DR
account.schedSweep.heading.sweepEntityId=Entity<br>
account.schedSweep.heading.sweepAccountId=Account ID<br>
account.schedSweep.heading.otherSweepFromBalType= Sweep<br>&nbsp;&nbsp;&nbsp;&nbsp;Balance
account.schedSweep.heading.otherAccSweepBookcodeCr=Book CR<br>
account.schedSweep.heading.otherAccSweepBookcodeDr=Book DR<br>
account.schedSweep.heading.thisAccSettleMethodCr=Settle Method<br>CR
account.schedSweep.heading.thisAccSettleMethodDr=Settle Method<br>DR
account.schedSweep.heading.entityId=Entity<br>
account.schedSweep.heading.account=Account<br>
account.schedSweep.tooltip.entityId=Entity ID
account.schedSweep.tooltip.accountId=Account ID
account.schedSweep.tooltip.scheduleFrom=From
account.schedSweep.tooltip.scheduleTo=To
account.schedSweep.tooltip.sweepFromBalanceType=Sweep Balance
account.schedSweep.tooltip.sweepOnGrpBalance=Sum Accs
account.schedSweep.tooltip.targetBalanceType=Target Type
account.schedSweep.tooltip.targetBalance=Target Balance
account.schedSweep.tooltip.sweepDirection=Direction
account.schedSweep.tooltip.minAmount=Min Amount
account.schedSweep.tooltip.allowMultiple=Allow Multiple
account.schedSweep.tooltip.thisAccSweepBookcodeCr=Book CR
account.schedSweep.tooltip.thisAccSweepBookcodeDr=Book DR
account.schedSweep.tooltip.thisAccSettleMethodCr=SettleMethod CR
account.schedSweep.tooltip.thisAccSettleMethodDr=SettleMethod DR
account.schedSweep.tooltip.sweepEntityId=Entity
account.schedSweep.tooltip.sweepAccountId=Account ID
account.schedSweep.tooltip.otherAccSweepBookcodeCr=Book CR
account.schedSweep.tooltip.otherAccSweepBookcodeDr=Book DR
account.schedSweep.tooltip.thisAccSettleMethodCr=Settle Method CR
account.schedSweep.tooltip.thisAccSettleMethodDr=Settle Method DR
account.schedSweep.tooltip.otherSweepFromBalType= Sweep Balance

account.schedSweep.againstAccount=Against Account
account.schedSweep.thisAccount=This Account

accountmaintenanceadd.defaultSettleMethodTooltip=Select the default Settlement Method
account.schedSweep.sweepDirection.fund=Only fund this account
account.schedSweep.sweepDirection.defund=Only defund this account
account.schedSweep.sweepDirection.Both=Both (fund or defund)

account.schedSweep.usedInOther=Used in other schedules:
tooltip.schedSweep.usedInOtherSched= Number of other sweep schedules that refer to this account
tooltip.schedSweep.show=Click to view other sweep schedules that refer to this account
account.schedSweep.show=Show
account.schedulesweep.label.fromLabel=From
account.schedulesweep.label.tolabel=To
account.schedulesweep.label.balanceTypeLabel=Balance Type
account.schedulesweep.label.otherBalTypeLabel=Balance Type
account.schedulesweep.label.targetBalanceLabel=Target Balance
account.schedulesweep.label.targetBalanceTypeLabel=Target Balance Type
account.schedulesweep.label.directionLabel=Direction
account.schedulesweep.label.minAountLabel=Min Amount
account.schedulesweep.label.allowMultipleLabel=Allow Multiple
account.schedulesweep.label.bookCrLabel=Book Cr
account.schedulesweep.label.bookDrLabel=Book Dr

account.schedulesweep.label.sweepAccountLabel=Sweep Account
account.schedulesweep.label.entityIdLabel=Entity ID
account.schedulesweep.label.leftaccountIdLabel=Account Id
account.schedulesweep.label.bookCrLabel=Book CR
account.schedulesweep.label.bookDrLabel=Book DR
account.schedulesweep.label.settleMethodCRLabel=Settle Method CR
account.schedulesweep.label.settleMethodDRLabel=Settle Method DR
account.schedulesweep.label.againstAccountLabel=Against Account
account.schedulesweep.label.sweepIntervalLabel=Sweep Interval
account.schedulesweep.label.sumAccountLabel= Sum Accounts

account.schedulesweep.tooltip.bookCrCombo=Select the book for Credit Sweep
account.schedulesweep.tooltip.bookDrCombo=Select the book for Debit Sweep
account.schedulesweep.tooltip.settleMethodCRCombo=Select the Settlement Method for Credit Sweep
account.schedulesweep.tooltip.settleMethodDRCombo=Select the Settlement Method for Debit Sweep
account.schedulesweep.tooltip.accountIdLabelCombo=Please select the other account Id
account.schedulesweep.tooltip.entityIdAgainstAccountCombo=Please select the other entity Id
account.schedulesweep.tooltip.otherBookCrCombo=Select the book for other account Credit Sweep
account.schedulesweep.tooltip.otherBookDrCombo=Select the book for other account Debit Sweep
account.schedulesweep.tooltip.othersettleMethodCRCombo=Select the Settlement Method for other account  Credit Sweep
account.schedulesweep.tooltip.othersettleMethodDRCombo=Select the Settlement Method for other account Credit Sweep
account.schedulesweep.label.balanceTypeP=Predicted
account.schedulesweep.label.balanceTypeE=External
account.schedulesweep.label.targetBalanceTypeC=Credit
account.schedulesweep.label.targetBalanceTypeD=Debit
account.schedulesweep.label.targetBalanceTypeA=Acc. Attribute
account.schedulesweep.label.targetBalanceTypeR=Rule
account.schedulesweep.label.allowMultipleY=Yes
account.schedulesweep.label.allowMultipleN=No
account.schedulesweep.label.sumAccountsY=Yes
account.schedulesweep.label.sumAccountsN=No
account.schedulesweep.label.directionB=Both
account.schedulesweep.label.directionF=Fund
account.schedulesweep.label.directionD=Defund

account.schedulesweep.tooltip.fromInput=Time which sweep schedule window starts (hh24:mi)
account.schedulesweep.tooltip.toInput=Time at which sweep schedule window ends (hh24:mi)
account.schedulesweep.tooltip.sumAccountsY= Indicate whether to sum associated accounts when calculating the sweep balance
account.schedulesweep.tooltip.sumAccountsN= Indicate whether to sum associated accounts when calculating the sweep balance
account.schedulesweep.tooltip.balanceTypeP=Predicted balance type from which sweeping will be performed
account.schedulesweep.tooltip.balanceTypeE=External balance type from which sweeping will be performed
account.schedulesweep.tooltip.otherBalanceTypeP=Predicted balance type from which sweeping will be performed
account.schedulesweep.tooltip.otherBalanceTypeE=External balance type from which sweeping will be performed
account.schedulesweep.tooltip.targetBalanceTypeC=Credit target balance type
account.schedulesweep.tooltip.targetBalanceTypeD=Debit target balance type
account.schedulesweep.tooltip.targetBalanceTypeA=Indicates that the target balance field will contain the name of a numeric account attribute whose value will be used at run time
account.schedulesweep.tooltip.targetBalanceTypeR=Indicates that the target balance field will contain the name of a pre-defined rule which will be evaluated at run time according to relevant configuration parameters in place for that rule
account.schedulesweep.tooltip.allowMultipleY=Indicate whether to allow multiple 
account.schedulesweep.tooltip.allowMultipleN=Indicate whether to allow multiple 
account.schedulesweep.tooltip.directionB=Sweeping should align the account to its target balance by applying a credit or debit as necessary, subject to the usual logic relating to the minimum sweep amount
account.schedulesweep.tooltip.directionF=Sweeping is only required when the balance is below target and consequently needs to be credited
account.schedulesweep.tooltip.directionD=Sweeping is only required when the balance is above target and consequently needs to be debited
account.schedulesweep.tooltip.minAountInput=Sweeping is only required when the balance is above target and consequently needs to be debited
account.schedulesweep.tooltip.targetBalanceInput=Target Balance
account.schedulesweep.fieldSet1.legendText= Sweep Account
account.schedulesweep.fieldSet2.legendText= Against Account
account.settleMethodSweep=Settlement Method
account.debitLeg=Debit Leg
account.creditLeg=Credit Leg

sweep.sweepuetr1=UETR
sweep.sweepuetr2=Extra UETR
tooltip.sweep.uetr1=Unique reference (SWEEP_UETR1), will be written to sweep and movement records
tooltip.sweep.uetr2=Additional unique reference (SWEEP_UETR2) will be written to sweep  record only

sweepDetail.additionalRef=Additional Reference 
sweepDetail.additionalRefCut=Additional Ref.
sweepDetail.sweepSetting=Sweep Settings
sweepDetail.sweepSetting.useSchedule.tooltip=Choose sweep settings from relative sweep schedule (if found) or use account defaults
sweepDetail.sweepSetting.useSchedule=Use settings from schedule
sweepDetail.sweepSetting.useAccountDefault=Use account default settings 
sweepDetail.sweepSetting.warning.bookandsettReverted=WARNING: Manual changes to Book and Settlement Method fields will be reverted.  Are you sure?
sweepDetail.sweepSetting.noUseSchedule=No sweep schedule found 
acctMaintenance.alert.screenOpened= Sub screen with the same account ID is already opened. Please close it before continue!
#-- Currency Maintenance --
currency.entityId=Entity
currency.currencyCode=Currency
currency.name=Name
currency.exchangeRate1=Exchange Rate
currency.exchangeRate=Exch Rate
currency.exchangeRate2=Exch Rate
currency.decimalPlaces=Dec
currency.decimalPlaces1=Number of Decimals
currency.interestBasis=Int Bas
currency.interestBasis1=Interest Basis
currency.tolerance=Tol
currency.toleranceAdd=Tolerance
currency.cutOffTime=Cut Off
currency.gmtOffset=Currency GMT Offset
currency.enableDst=Enable Daylight Saving
ccurrency.dstStartDateAsString=Start Date
currency.dstEndDateAsString=End Date
currency.cutOffTime1=Cut Off Time
currency.preFlag=Predict
currency.preFlag.active=Active
currency.preFlag.InActive=Inactive
id.currencyCode=Currency
currencyMaintenance.title.mainWindow=Currency Maintenance - SMART-Predict
currency.CutoffTime=Cut off time (hh:mm)
currency.EnableCurrencyForPredict=Enable this currency for Predict
currency.DisableCurrencyForPredict=Disable this currency for Predict
currencyInterest.interestRate.label=Rate
currencyInterest.interestRateDate=Date
currencyInterest.fromDate=Date From
currencyInterest.toDate=To
currencyInterest.inputDate=Input Date-Time
currencymaintenance.alert.validDecimal=Please enter a valid decimal number

#-- Holidays Maintenance --
holiday.holidayDate_Date=Date
holiday.holidayDay=Day
holiday.entityId=Entity ID
holiday.countryId=Country
holiday.holidayDate=Holiday Date

#-- Message Formats Maintenance --
copyFromFormatId.title.mainWindow=Copy From Format ID - SMART-Predict
message.entityId=Entity
messageFormats.id.formatId=Format
messageFormats.id.formatId1=Format ID*
messageFormats.id.formatIdcopy=Format
messageFormats.formatName=Name
messageFormats.formatName1=Format Name*
messageFormats.formatType=Format Type
messageFormats.formatType1=Type
messageFormats.fieldDelimeter=Field Del
messageFormats.fieldDelimeter1=Field Delimiter
messageFormats.hexaFldDelimeter=(in Hexadecimal)
messageFormats.hexaFldDelimeter1=Field Delimiter (Hex)
messageFormats.msgSeparator=Msg Separator
messageFormats.msgSeparator1=Message Separator
messageFormats.hexaMsgSeparator=Msg Separator (Hex)
messageFormats.hexaMsgSeparator1=Message Separator (Hex)
messageFormats.outputType=O/P Type
messageFormats.outputType1=Output
messageFormats.path=Path
messageFormats.lineNoDisplay=Line No
messageFields.lineNoDisplay=Line Number
messageFormats.fileName=File Name
messageFormats.authorizeFlag=Authorise
messageFormats.msgFormatDetails=Details
messageFormats.msgStructure=Message Structure
messageFormats.outputParameter=Output
messageFormats.overdue=ACK Overdue
messageFormats.alert.overdueFormat=ACK Overdue field must be formatted as HH:MM:SS
messageFields.id.serialNo=Sequence No
messageFields.id.serialNo1=Seq No
messageFields.fieldType=Field Type
messageFields.value=Value
messageFields.sequenceNo=Sequence Number
messageFields.sequenceNoDisplay=Seq No
messageFields.startPosFormatTypeFixed=Start Position
messageFields.startPos=Start Position
messageFields.endPosFormatTypeFixed=End Position
messageFields.startPos1=Start Pos
messageFields.endPos=End Position
messageFields.endPos1=End Pos
messageFields.fieldType.text=Text
messageFields.fieldType.keyword=Keyword
messageFields.fieldType.hexaDecimal=Hexadecimal
messagefieldadd.alert.LineNumber=Line Number cannot be equal to 0
messagefieldadd.alert.StartPosition=Start Position cannot be equal to 0
messagefieldadd.alert.EPSP=End Position cannot be less than Start Position
messagefieldadd.alert.space=Space between Start and End position is less than the length of the text in Value field
messagefieldadd.alert.EndPositon=End Position cannot be equal to 0
messagefieldadd.alert.sequenceNo=Sequence Number cannot be equal to 0
messagefieldadd.alert.duplicateSeqNo=Duplicate Seq No
messagefieldadd.alert.rangeOverlapping=Range overlapping
messageField.Title.mainWindow=Message Fields Maintenance - SMART-Predict
messagefieldadd.alert.duplicateRecord=A record with this sequence number already exists
messageFormat.title.MainWindow=Sweep Message Format Maintenance - SMART-Predict
messageFormatScenario.title.MainWindow=Scenario Message Format Maintenance - SMART-Predict
messageFormats.usage.sweep = Sweep
messageFormats.usage.other = Other
messageFormats.formatType.fixed=Fixed
messageFormats.formatType.delimited=Delimited
messageFormats.formatType.tagged=Multi-line
messageFormats.outputType.file=File
messageFormats.outputType.mqInterface=MQ interface
messageFormatAdd.alert.defineFields=Please define fields for the message
messageFormatAdd.alert.fieldsRemoved=All the fields for the previous format type will be removed. Do you want to continue?
messageFormats.interfaceId=Interface ID
tooltip.sweepMsgFormat.selectInterfaceId=Select Interface ID

#-- Message Fields Maintenance --
message.SequenceNumber=Sequence Number
message.FieldType=Field Type
message.Value=Value
message.StartPosition=Start Position
message.EndPosition=End Position

#-- Custodian Maintenance --
custodian.entityId=Entity
custodian.custodianId=Party ID*
custodian.custodianName=Party Name
custodian.custodianFlag=Custodian Flag

#-- User Maintenance --
userSetup.viewScreen=View User Details - SMART-Predict
userSetup.addScreen=Add User - SMART-Predict
userSetup.changeScreen=Change User - SMART-Predict
usermaintenance.userId=User
usermaintenance.userName=Name
usermaintenance.userId*=User
usermaintenance.userName*=User Name
usermaintenance.language=Language
usermaintenance.emailId=Email
usermaintenance.section=Section
usermaintenance.lastLogin=Last Login
usermaintenance.lastLogout=Last Logout
usermaintenance.status=Status
usermaintenance.phoneNo=Phone Number
usermaintenance.roleId=Role Profile
tooltip.lastlog=Last Login
tooltip.lastpass=Last Password Change
tooltip.lastlogout=Last Logout
tooltip.overdue=Enter ACK overdue time as HH:MM:SS
usermaintenance.entity=Default Entity
usermaintenance.currGrp=Default Ccy Group
usermaintenance.pwdDate=Last Password Change
usermaintenance.password=Password
usermaintenance.user=User
usermaintenance.profile=Profile
usermaintenance.userdetails=User Details
usermaintenance.info=Info
usermaintenance.Personal=Personal
userSetup.Label.Enabled=Enabled
userSetup.Label.Disabled=Disabled
userSetup.title.window=User Setup - SMART-Predict
usermaintenance.role=Role
usermaintenance.select=Select

#-- Audit Logs --
auditLog.viewScreen=View User Audit Log Details - SMART-Predict
userAuditLog.title.window=My User Audit Log - SMART-Predict
userLog.viewScreen=View User Log Details - SMART-Predict
errorLog.errorDate_Date=Date
errorLog.errorDate_Time=Time
errorLog.userId=User
errorLog.source=File
errorLog.error=Error
errorLog.dateRange=Error Log
ErrorLog.title.window=Error Log - SMART-Predict
title.exportErrors=Export Error Log - SMART-Predict
tooltip.exportErrors=Export
tooltip.exportErrors_csv=Export to CSV
tooltip.exportErrors_excel=Export to Excel
tooltip.exportErrors_pdf=Export to PDF

#-- Help--
tooltip.help=Help Screen Content

button.exportErrors=Export
systemLog.id.logDate=Date
systemLog.logTime=Time
systemLog.userId=User
systemLog.ipAddress=IPAddress
systemLog.process=Process
systemLog.action=Action
systemLog.logDate_Date=Date
systemLog.logDate_Time=Time
systemLog.dateRange=System Log
systemLog.title.window=System Log - SMART-Predict

maintenanceLog.logDate_Date=Date
maintenanceLog.logDate_Time=Time
maintenanceLog.userId=User
maintenanceLog.ipAddress=IP Address
maintenanceLog.tableName=Facility
maintenanceLog.reference=Reference
maintenanceLog.action=Action
maintenanceLog.oldValue=Changed From
maintenanceLog.newValue=Changed To
maintenanceLog.columnName=Field
maintenanceLog.dateRange=Maintenance Log
MaintenanceLog.title.window=Maintenance Log - SMART-Predict
MaintenanceLogView.title.window=View Maintenance Log - SMART-Predict
auditLog.userId=User
auditLog.dateRange=User Log
auditLog.sysLog=System Log
auditLog.mainLog=Maintenance Log
auditLog.errorLog=Error Log
auditLog.from=From
auditLog.to=To
auditLog.logDate_Date=Date
auditLog.logDate_Time=Time
auditLog.id.ipAddress=IP Address
auditLog.id.reference=Item
auditLog.id.referenceId=Item ID
auditLog.id.action=Action
auditLog.userLog=User Log - SMART-Predict
auditLog.auditLog=Audit Log - SMART-Predict

#-- User Status --
userStatus.title.window=User Status - SMART-Predict
userStatus.header.user=User
userStatus.header.name=Name
userStatus.header.role=Role
userStatus.header.logonDtTime=LogOn Date & Time
userStatus.header.IPAddress=IP Address
viewUserStatus.title.window=View User Status - SMART-Predict
status.userId=User ID
status.userName=UserName
status.roleId=Role ID
status.logOnTime=LogOnTime
status.ipAddress=IPAddress
status.userId=UserId
status.userName=UserName
status.sectionId=SectionId
status.lang=Language
status.phoneNumber=Phone No.
status.emailId=EmailId
status.roleId=RoleId
status.currentEntity=CurrentEntity
status.lastLogin=LastLogin
status.pwdChangeDate=LastPasswordChange
status.pwdChangeDT=Password History
status.lastLogout=LastLogout
changeUserDetails.title.window=Change User Details - SMART-Predict
myUserDetails.title.window=My User Detail - SMART-Predict
userOptions.alert.me=methodName
userOptions.confirm.close=You have chosen to close the window. All unsaved changes will be lost. Are you sure?
userOptions.Label.enabled=Enabled
userOptions.Label.disabled=Disabled
reportsmatch.title.window=Match Statistics - SMART-Predict
reportsturnover.title.window=Turnover Statistics - SMART-Predict

#-- Shortcut --
shortcut.changeScreen=Change Shortcut - SMART-Predict
shortcut.addScreen=Add Shortcut - SMART-Predict
shortcut.title.window=Shortcut Maintenance - SMART-Predict
shortcuts.id.shortcutId=Shortcut
shortcuts.shortcutName=Name
shortcuts.id.shortcutIdadd=Shortcut
shortcuts.shortcutNameadd=Shortcut Name
shortcuts.menuItemId=Menu Option

#-- Alert Messages --
alert.changeScreen=Change Alert Message - SMART-Predict
alert.viewScreen=View Alert Message - SMART-Predict
alertMessages.title.window=Alert Message - SMART-Predict
alertMessage.alertstage=Alert Event
alertMessage.alertstageadd=Alert Event
alertMessage.roleId=Role
alertMessage.alertmessage=Alert Message
alertMessage.enableflg=Activation Flag
alertstage=Alert Stage
roleId=Role ID
alertmsg=Alert Message
activationflg=Activation Flag
active=Active
inactive=Inactive
submit=Submit
stp=STP
stp1=STP
stp2=New
manswp=Manual Sweep
menuaccessoptions.alert.menuOption=At least one menu option must be selected
menuaccessoptions.alert.changePassword=Password maintenance is not granted
menuaccessoptions.error.changePassword=ERROR: Password change is required but user's role does not allow access to the facility
menuaccessoptions.warning.changePassword= WARNING: This role does not grant access to the password change facility
#-- Scenario Maintenance --
scenario.changeScreen.title.window=Change Scenario - SMART-Predict
scenario.viewScreen.title.window=View Scenario - SMART-Predict
scenario.addScreen.title.window=Add Scenario - SMART-Predict
scenario.title.window=Scenario Maintenance - SMART-Predict
scenario.Advanced.window=Scenario - Advanced Details - SMART-Predict
scenarioRoleAssignment.title.window=Scenario - Role Assignment
scenarioAmendAssignment.title.window=Amend Role Assignment
scenario.displayOrder.alert=Display order must be between 1 - 999
scenario.startEndTime.alert=You need to fill start time and end time
scenario.testQuery.error=The scenario query failed testing. See error details below \\n
scenario.testQueryEmpty.error=Please fill Base Query field before clicking on Test button
scenario.colType.error=Please fill Base Query field before clicking on Test button
scenario.id=ID
scenario.selectAll=Select All
scenario.scenarioId=Scenario ID
scenario.runAt=Run at
scenario.runAtDesc=(Specify in system time frame)
scenario.schedule.tooltip.scenarioId=Scenario ID
scenario.schedule.runAt=The scheduled scenario will Run At (hh:mm)
scenario.paramDesc= Define Parameters to be used in scheduled scenario checking
tooltip.defineParams.delete= Delete parameter
tooltip.defineParams.add = Add parameter
scenario.apiTypeLbl=API Type
scenario.configFieldSet.legendText=Required parameters
scenario.hostLbl=HOST_ID
scenario.entityLbl=ENTITY_ID
scenario.ccyLbl=CURRENCY_CODE
scenario.accountLbl=ACCOUNT_ID
scenario.valDateLbl=VALUE_DATE
scenario.amountLbl=AMOUNT
scenario.signLbl=SIGN
scenario.mvtLbl=MOVEMENT_ID
scenario.matchLbl=MATCH_ID
scenario.sweepLbl=SWEEP_ID
scenario.payLbl=PAYMENT_ID
scenario.allLbl=All attributes'name-value pairs
scenario.tooltip.apiType= Select API Type
scenario.tooltip.hostId= Check HOST ID
scenario.tooltip.entityId=Check ENTITY ID
scenario.tooltip.ccy=Check CURRENCY CODE
scenario.tooltip.accountId=Check ACCOUNT ID
scenario.tooltip.valDate=Check VALUE DATE
scenario.tooltip.amount=Check AMOUNT
scenario.tooltip.sign=Check SIGN
scenario.tooltip.mvt=Check MOVEMENT ID
scenario.tooltip.match=Check MATCH ID
scenario.tooltip.sweep=Check SWEEP ID
scenario.tooltip.payment=Check PAYMENT ID
scenario.tooltip.checkedAll=Check all attributes

scenario.title=Title
scenario.recordInsLbl=Record Instances
scenario.category=Category
scenario.checkinterval=Check Interval
scenario.system=System
scenario.active=Active
scenario.description=Description
scenario.displayOrder=Display Order
scenario.runEvery=Run Every
scenario.startTime=Start Time
scenario.endTime=End Time
scenario.emailWhenDiff=Email When Diff.
scenario.cyclic=Cyclic
scenario.scheduled=Scheduled
scenario.createInst=Create Instances using API
scenario.scheduledDesc=Scheduled parameter values will be used at run-time in the scenario base query
scenario.CreateInsDesc=Requires parameters: Not yet configured
scenario.CreateInsDescFull=Requires parameters
scenario.addLabel=Add
scenario.changeLabel=Change
scenario.deleteLabel=Delete
scenario.configLabel=Configure
scenario.defParamsLabel=Define Parameters
scenario.schedParamsDesc= (Note: Parameters in the query needs to be in "P{param}" format)
scenario.fieldSet.legendText= Type
scenario.sytemScenarioAlert=INVALID: Scenario ID should not start with 'SYS_'. \\nPlease specify a different scenario ID
scenario.existingConfigAlert=Existing schedule entries will lose their configuration.\n \u0020 Do you want to continue?
scenario.missingRunAtValue=Please fill run at field
scenario.timeAlreadyExists=Scheduler with the same time was already configured
scenarioNotification.roleID=Role ID
scenarioNotification.role=Role
scenarioNotification.entity=Entity
scenarioNotification.entityID=Entity ID
scenarioNotification.access=Access
scenarioNotification.accessRequired=Access Required?
scenarioNotification.popup=Pop-up
scenarioNotification.email=Email
scenarioNotification.fullInstanceAccess=Full Instance Access
scenarioNotification.flash=Flash
scenarioNotification.flashIcon=Flash Icon
scenarioNotification.button.distributionList=Dist. List
tooltip.distributionList=Distribution List
scenarioAdvanced.baseQuery=Base Query
scenarioAdvanced.hostColumn=HOST Column
scenarioAdvanced.entityColumn=ENTITY Column
scenarioAdvanced.currencyColumn=CURRENCY Column
scenarioAdvanced.amountColumn=AMOUNT Column
scenarioAdvanced.summary=Summary
scenarioAdvanced.defaultGrouping=Default Grouping
scenarioAdvanced.display=Display
scenarioAdvanced.useGenericDisplay=Use Generic Display
scenarioAdvanced.facilityDetail=Facility Detail
scenarioAdvanced.facilityID=Facility ID
scenarioAdvanced.refTable=Reference table
scenarioAdvanced.reqRefCols=Required Ref Cols
scenarioAdvanced.refColumns=Ref Columns
scenarioAdvanced.alertInstanceColumns= Alert Instance Cols
scenarioAdvanced.FacilityParams=Facility Parameters
scenarioAdvanced.parameterValues=Parameter values
scenarioCategory.categoryId=Category ID
scenarioCategory.categoryDescription=Description
scenarioCategory.categoryTitle=Title
scenarioCategory.categorySystemFlag=System
scenarioCategory.categoryDisplayorder=Order
scenarioCategory.categoryDisplayTabName=Display Tab
scenario.distributionlist=Distribution List
scenario.distributionlist.tilte=Users
scenario.distributionlist.selectall=Select All

scenario.tab.general = General
scenario.tab.identification = Identification
scenario.tab.instances = Instances
scenario.tab.guiHighlight = GUI Highlighting
scenario.tab.events = Events
tab.users=Users
tab.roles=Roles
tab.otherEmail= Other Email
scenario.recordInstance = Record Scenario Instances
scenario.uniqueExpression = Unique Expression
scenario.accountIdColumn = ACCOUNT_ID Column
scenario.valueDateColumn = Value Date Column
scenario.signColumn = SIGN Column
scenario.mvtIdColumn = MOVEMENT_ID Column
scenario.matchColumn = MATCH_ID Column
scenario.sweepIdColumn = SWEEP_ID Column
scenario.payIdColumn = PAYMENT_ID Column
scenario.treeBreakDown1 = Tree Breakdown 1
scenario.treeBreakDown2 = Tree Breakdown 2
scenario.otherIdColumn= OTHER_ID Column
scenario.otherIdType= OTHER_ID Type
scenario.instanceExpiry = Instance Expiry (mins)
scenario.reRaiseAfter = Re-raise after expiry
scenario.no = No
scenario.immediately = Immediately
scenario.afterInterval = After an interval of
scenario.minAfter = mins after expiry
scenario.eventTab.alert.missingFacility= Please select event facility
scenario.eventTab.alert.missingMsgFormat= Please choose a valid message format
scenario.eventTab.alert.facilityExists= Event facility already exists
scenario.events.afterLaunch = After Launching Events
scenario.events.remainactive = Instance will remain Active
scenario.events.resolved = Set Instance as Resolved
scenario.events.pending = Set Instance as Pending Resolution
scenario.events.refColumn = Ref Columns <br>(Bind variables)
scenario.events.resolutionQuery = Resolution Query Text:
scenario.events.resolutionAfter = Resolution Overdue after
scenario.events.mandatoryFiels = Please fill all mandatory fields
scenario.events.mins = mins
scenario.events.eventSeq = Event Sequence
scenario.events.eventFacility = Event Facility ID
scenario.events.executeWhen = Execute when
scenario.events.allowRepeat = Allow repeat on Re-raise
scenario.events.addFormat = Add message Format
scenario.events.changeFormat = Change message Format
scenario.events.value = Value
scenario.events.msgFormat = Maintain
scenario.events.instAttr = Instance Attribute
scenario.events.literal = Literal
scenario.events.ignore = Ignore
scenario.events.null = Null
scenario.events.usersLbl= Users
scenario.events.rolesLbl= Roles
scenario.events.emailLbl= Email
scenario.events.tooltip.add=Add event
scenario.events.tooltip.change=Change event
scenario.events.tooltip.view=View event
scenario.events.tooltip.delete=Delete event
scenario.events.tooltip.remainactive=Instance will remain Active
scenario.events.tooltip.resolved=Set instance as Resolved
scenario.events.tooltip.pending=Set instance as Pending Resolution
scenario.events.tooltip.refColumn=Select reference columns
scenario.events.tooltip.resolutionQuery=Enter resolution query
scenario.events.tooltip.mins=Enter resolution overdue after value
scenario.events.never= Never
scenario.events.after= After
scenario.events.tooltip.never=Never
scenario.events.tooltip.after=Resolution overdue after (mins)
scenario.events.tooltip.scenarioId=Scenario ID
scenario.events.tooltip.executeWhen=Select execute when value
scenario.events.tooltip.allowRepeat=Allow repeat on re raise
scenario.events.tooltip.eventSeq=Event sequence
scenario.events.tooltip.eventFacility=Choose a facility
scenario.events.tooltip.instAttr=Instance Attribute
scenario.events.tooltip.literal=Literal
scenario.events.tooltip.ignore=Ignore
scenario.events.tooltip.null=Null
scenario.events.tooltip.value=Choose a map from value 
scenario.events.tooltip.msgCombo= Choose a scenario message format
scenario.events.tooltip.emailFormatCombo= Choose an email format
scenario.events.tooltip.eventFacilityDesc= Please give a meaningful text to describe what/why of the event
scenario.events.eventFacilityDesc= Description
scenario.guiHighlight.facilityId = GUI Facility ID
scenario.guiHighlight.reqScenario = Requires Scenario Instances
scenario.guiHighlight.paramId = Parameter ID
scenario.guiHighlight.decription = Description
scenario.guiHighlight.mapFrom = Map From
scenario.events.info= Info
scenario.events.emailFormat= Email Format
scenario.events.tooltip.info= Useful information about the type of data
scenario.events.instanceAttribute.info= Select Values from list of attributes below
scenario.guiHighlight.note.title= Note:
scenario.guiHighlight.note.text=Grey rows indicates unavailable  facilities due to requiring scenario instances, or where this <br/>scenario's instance parameters are not sufficient for the facility to operate.
scenario.guiHighlight.critGuiHighLbl.text=Use Red icon to indicate critical importance
#scenario.guiHighlight.critGuiHighCheck.title= Use Red icon to indicate critical importance
scenario.alert.uncheckingRecordInstances= Warning. Unchecking Record Instances will cause configuration parameters to be lost in the Instances and Events tabs. Are you sure?
scenario.alert.uncheckingRecordInstances1= Warning. Unchecking Record Instances will cause configuration parameters to be lost in the General, Instances and Events tabs. Are you sure?
scenario.alert.emptyRowAlreadyAdded= Please fill the empty row first.
label.alertSummaryTooltip.facility=Facility
label.alertSummaryTooltip.parameters= Parameters
scenarioTooltipSummary.closeButton=Close
scenarioTooltipSummary.displayListButton=Display List
scenarioTooltipSummary.linkToSpecificButton= Link to specific

#-- Scenario Summary screen
scenariosummary.title.window=Scenario Summary
scenarioSummary.currencyCode=Ccy
scenarioSummary.Entity=Entity
scenarioSummary.resolvedOn= Resolved On
scenarioSummary.Count=Count
scenarioSummary.title.xml=Summary Details XML
scenarioSummary.context.xml=Show summary details XML 
Summary.title.xml=Screen Details XML
Summary.context.xml=Show screen details XML 
alert.scenarioSummary.noData=No data to display
alert.scenarioSummary.noData.title=Warning - Scenario Summary Details
alert.Summary.noData.title=Warning - Summary Details
scenarioSummary.applyCcy=Apply Currency Threshold
scenarioSummary.activeInstance=Active instances
scenarioSummary.amountThreshold=Apply amount threshold
scenarioSummary.all=All
scenarioSummary.active=Active
scenarioSummary.resolved=Resolved
scenarioSummary.pending=Pending
scenarioSummary.overdue=Overdue
scenarioSummary.allOpen=All open
scenarioSummary.status=Status
scenarioSummary.zeroTotals=Hide Zero Totals
scenarioSummary.alertableScen=Show Alertable Scenarios Only
scenarioSummary.popupScen=Pop Up
scenarioSummary.flashScen=Flash
scenarioSummary.scenTotals=Scenario Totals

scenarioSummary.selectedScenLastRan=Selected scenario last ran:	
scenarioSummary.selectedScen=Selected Scenario Summary	
scenarioSummary.title=Scenario Summary
tooltip.scenTotals=Total Scenarios Alerts Count
tooltip.selectedScen=Selected Scenario Alerts

scenarioSummary.title.listemails=List Emails
tooltip.resolvedOnDate= Instance resolved on date

#-- Role Maintenance --
role.roleId=Role ID
role.roleName=Name
role.Name=Name
currencyGroupAccess.addScreen=Add Currency Group Access - SMART-Predict
currencyGroupAccess.changeScreen=Change Currency Group Access - SMART-Predict
currencyGroupAccess.viewScreen=View Currency Group Access - SMART-Predict
tooltip.currencyGroupId=Currency group ID
tooltip.currencyGroupName=Currency group name
button.ccyGrp=Ccy Group 
button.4EyesGrp=4-Eyes
role.authorization.authorizationInput=Authorisation
tooltip.authorizationInput=Require another user to authorise a manually entered movement
tooltip.inputNotification=Notify if any interfaces report problems or there are delays in messages arriving
tooltip.ccyGrpAccess=Currency group access
tooltip.4EyesAccess=4 eyes access
confirm.changeCcyGroupAccessEntityId=Changes made for the selected entity will be lost
alert.error.creationWithAllRole=INVALID: ALL is not a valid Role ID. Please specify a different ID 

#-- Scenario Category --
scenarioCategoryAdd.title.window.addScreen=Add Scenario Category
scenarioCategoryAdd.title.window.changeScreen=Change Scenario Category
scenarioCategory.title.window=Scenario Category
scenarioCategory.categoryid=Category ID
scenarioCategory.description=Description
scenarioCategory.title=Title
scenarioCategory.systemflag=System Flag
scenarioCategory.displayorder=Display Order
scenarioCategory.displayTab=Display Tabs


#-- Movement Input --
manualInput.entity.id=Entity
manualInput.amount=Amount
manualInput.id.movementId=Movement
movementDisplay.source=Source
movementDisplay.movementparam=Main Details
movementDisplay.reference=Referred
movementDisplay.status=Status
movementDisplay.predict=Predict
movementDisplay.predictStatus=Predict Status
movementDisplay.extract=Extract
movementDisplay.counterPartyCustDetails=Party Details
movementDisplay.originalMessage=Original Message
movementDisplay.cash=Cash
movementDisplay.securities=Securities
movementDisplay.included=Included
movementDisplay.excluded=Excluded
movementDisplay.cancelled=Cancelled
#Mantis 6250 start
movementDisplay.initPredStatus= Init Pred Status
movementDisplay.ilmFcast= ILM Fcast Status
#Mantis 6250 end
match.addMvmt.mvmtIdempty=Please enter movement ID
movementDisplay.alert.noAccess=Invalid: your role does not provide access to this movement

#-- Sweep Queue --
sweepSubmitQueue.title.window=Sweep Submit Queue - SMART-Predict
sweepAuthQueue.title.window=Sweep Authorise Queue - SMART-Predict
sweepSubmit.colValue.Auto=Auto
sweepSubmit.colValue.Man=Man
sweepSubmit.colValue.Auto=Auto
sweepSubmit.colValue.Man=Man
sweepSubmit.colValue.Auto=Auto
sweepSubmit.colValue.Man=Man
sweepSubmit.colValue.Auto=Auto
sweepSubmit.colValue.Man=Man
sweep.entity=Entity
sweep.currency=Currency
sweep.currencyGroup=Currency Group
sweep.accountType=Account Type
sweep.sweepId=Sweep
sweep.valueDate=Value
sweep.accountIdCr=CR Account
sweep.accountIdDr=DR Account
sweep.currencyCode=Ccy
sweep.sweepAmt=Sweep Amount
sweep.currentAmt=Original Amount
sweep.NewAmt=New Amount
sweep.messageType=Message Type
sweep.crdIntMsg=CR INT MSG
sweep.crdExtMsg=CR EXT MSG
sweep.drIntMsg=DR INT MSG
sweep.drExtMsg=DR EXT MSG
sweep.accountCr=CR A/C Msg
sweep.accountDr=DR A/C Msg
sweep.sweepType=Type
sweep.sweepUser=User
sweep.sweepStatus=Status
sweep.sweepDateTimeUser=Date/Time/Original User
sweep.submitPanel=Submit Panel
sweep.authorizePanel=Authorise Panel
sweep.cancelPanel=Cancel Panel
sweep.otherPanel=View Panel
sweepsearch.currency=Currency
sweepsearch.accType=Account Type
sweep.id=Sweep
sweep.value=Value
sweep.crAccount=CR Account
sweep.drAccount=DR Account
sweep.amount=Sweep Amount
sweep.crMsg=CR A/C Msg
sweep.drMsg=DR A/C Msg
sweep.type1=Type
sweep.user=User
sweep.status1=Status
sweep.genTime=Sweep Time
sweepcancelQ.title.window=Sweep Cancel Queue - SMART-Predict
sweepCancel.colValue.Auto=Auto
sweepCancel.colValue.Man=Man
sweep.subTime=Submit Time
sweep.authTime=Authorised Time
sweep.postCut=Post Cut Off
sweep.postsubmitCut=Post Submit Cut Off
sweep.postauthorizeCut=Post Authorise Cut Off
sweep.confirm.cancelMsg=Are you sure you want to cancel this sweep?

#-- Buttons --
sweep.submit=Submit
sweep.authorize=Auth
sweep.cancel=Cancel
sweep.refresh=Refresh
sweep.search=Search
sweep.close=Close

#-- Messages Displayed --
sweep.alreadySubmitted=Sweep ID(s) <b>{0}</b> already {1} by another user
sweep.cutoffExceeded=The cut-off time for one or more selected sweeps <b>{0}</b> has been breached.<br>Do you want to go back and review these sweeps?
sweep.accountLimitBreached=Sweep amount(s) outside account limits for one or more selected sweeps <b>{0}</b>.<br>Do you want to go back and review these sweeps?
sweep.userLimitExceeded=Sweep amount for sweep ID(s) <b>{0}</b> exceeds your limit for this currency
sweep.amountChanged=The proposed amount (based on EOD target balance) of one or more selected sweeps <b>{0}</b> has changed. <br> Do you want to go back and review these sweeps?
sweep.saveSuccessfully=Sweep ID(s) <b>{0}</b> successfully {1}
sweep.saveSuccessfullyID=Sweep(s) <b>{0}</b> successfully {1}
sweep.saveError=Error occurred in {1} sweep ID(s) <b>{0}</b>.<br>Please contact your System Administrator!
currency.id=Ccy

#-- Role Maintenance --
role.roleId=Role
role.roleId1=Role ID*
role.roleName=Name
role.roleName1=Role Name*
role.alerttype=Alert Type*
role.alerttype.email=Email
role.alerttype.popup=Pop-up
role.alerttype.both=Both
role.menuaccess.menuItemDesc=Menu Item Description
role.menuaccess.access=Access
role.entAccessList.name=Name
role.entAccessList.fullAccess=Full Access
role.entAccessList.Access=Access
role.entAccessList.readOnly=View Access
role.entAccessList.noAccess=No Access
role.workQueueAccess.currency=Ccy
role.workQueueAccess.matchStatus=Work Queue
role.workQueueAccess.currency1=Currency
role.workQueueAccess.entity1=Entity
role.workQueueAccess.qualityA=A
role.workQueueAccess.qualityB=B
role.workQueueAccess.qualityC=C
role.workQueueAccess.qualityD=D
role.workQueueAccess.qualityE=E
role.workQueueAccess.qualityZ=Z
role.workQueueAccess.outStanding=Outstanding
role.workQueueAccessAdd.currCode=Currency
role.workQueueAccessAdd.offered=Offered
role.workQueueAccessAdd.suspended=Suspended
role.workQueueAccessAdd.confirmed=Confirmed
role.workQueueAccessAdd.outstanding=Outstanding
workqueueaccess.alert.access=Access to at least one queue should be given
workqueueaccess.recordPresent=The record is already present
workqueueaccess.confirm.selectedCurrency1=All the entries for the selected currency will be deleted
workqueueaccess.confirm.selectedCurrency2=Are you sure?
role.sweepLimits.currency=Currency*
role.sweepLimits.sweepLimits=Sweep Limits
role.sweepLimits.sweep=Sweeping
role.copyFrom.roleId=Role
role.sweepLimits.currencyCode=Currency
role.sweepLimits.currencyCode1=Currency Code*
role.sweepLimits.sweepLimit=Sweep Limit
role.sweepLimits.sweepLimit1=Sweep Limit*
role.menuaccess.level1=Level1
role.menuaccess.level2=Level2
role.menuaccess.level3=Level3
role.printbutton=Print
role.printallbutton=PrintAll

#-- Password Rules --
pwd.alphaChar=Alpha Char
pwd.numericChar=Numeric Char
pwd.specialChar=Special Char
pwd.minPasswordLen=Minimum length
pwd.ExpireDays=Expire in
pwd.recentuserdPwd=Recently used
pwd.unsuccLogin=Invalid attempts
pwd.chars=chars
pwd.days=days
pwd.mixedCase=Mixed Case
passwordRules.title.window=Password Rule - SMART-Predict
passwordRules.confirm.close=You have chosen to close the window. All unsaved changes will be lost - are you sure?
passwordRules.legend.MinNum=Minimum number of
passwordRules.label.aToz=[a-z,A-Z]
passwordRules.label.0to9=[0-9]
passwordRules.label.spChars=[~!@#$%^&*()-_=+;:'",<.>/?]
passwordRules.legend.passParams=Password parameters

#-- Change Login & Password Screen --
changePassword.title.window=Change Password - SMART-Predict
changePassword.alert.newpass=New password and confirmed password do not match
changePassword.alert.confirmPass=Password and confirmed password do not match
password.old=Old Password
password.new=New Password
password.retypeNew=Confirm new password
password.confirmPassword=Confirm password

#-- Validator Errors --
errors.required={0} is required
errors.minlength={0} cannot be less than {1} characters
errors.maxlength={0} cannot be greater than {1} characters
errors.invalid={0} is invalid
errors.password.passwordRules=Password rules not satisfied
errors.password.minLength=Invalid password: must be {0} or more characters in length
errors.password.maxLength=Invalid password: must be less than {0} characters in length
errors.password.inHistory=Invalid password: you have used this password before
errors.password.incorrect=Old password incorrect
errors.password.alphaChars=Invalid password: {0} or more alpha characters are required
errors.password.mixedCase=Invalid password: both upper case and lower case characters are required
errors.password.numbers=Invalid password: {0} or more numeric characters are required
errors.password.specialChar=Invalid password: {0} or more symbol characters are required
errors.password.checkUserId=Invalid password: should not contain user ID
errors.byte={0} must be a byte
errors.short={0} must be a short
errors.integer={0} must be an integer
errors.long={0} must be a long
errors.float={0} must be a float
errors.double={0} must be a double
errors.date={0} is not a date
errors.range={0} is not in the range {1} through {2}
errors.creditcard={0} is an invalid credit card number
errors.email={0} is an invalid email address
errors.invalidLogin=User ID or password is incorrect. Please try again.
errors.entity.selectEntity=Please select a entity
errors.entity.entityId.required=Entity ID is required.<BR>
errors.entity.entityId.minlength=Entity ID cannot be greater than 12 characters.<BR>
errors.entity.entityIdSelect.required=Please select a entity.<BR>
errors.entity.entityName.required=Entity name is required.<BR>
errors.entity.entityName.minlength=Entity name cannot be greater than 30 characters.<BR>
errors.entity.domesticCurr.required=Domestic currency is required.<BR>
errors.entity.reportingCurr.required=Reporting currency is required.<BR>
errors.databaseexp=A database problem occurred
errors.javaRuntimeException=RuntimeException
errors.DataAccessResourceFailureException=DataAccessResourceFailureException
errors.DataIntegrityViolationException=A data integrity violation occurred
errors.DataIntegrityViolationExceptioninDelete=Record cannot be deleted as other transactions depend on it
errors.DataIntegrityViolationExceptioninAdd=Record already exists
errors.CouldNotSaveJobReportWithSameNameExceptioninAdd=Report record with Same name and type already exists
errors.CouldNotSaveJobReportWithSameLocationPrefixExceptioninAdd=Report record with same combination of filename prefix and output location already exists
errors.DataRetrievalFailureException=DataRetrievalFailureException
errors.InvalidDataAccessApiUsageException=InvalidDataAccessApiUsageException
errors.InvalidDataAccessResourceUsageException=InvalidDataAccessResourceUsageException
errors.DataAccessException=DataAccessException
errors.SwtRecordNotExist=Record does not exist
errors.Dst.overlappingDateRangesException=INVALID: This date range overlaps with an existing DST range
errors.match.unmatched=Match has been unmatched by other user
matchQueue.currency=Currency
matchQueue.CcyName=Name
matchQueue.A=A
matchQueue.B=B
matchQueue.C=C
matchQueue.D=D
matchQueue.E=E
matchQueue.Z=Z
matchQuality.matchId=Match
matchQuality.pos1=1st
matchQuality.Int=Int
matchQuality.pos9=Fin
matchQuality.posTotal=Position Totals
qualityTab.today=Today
qualityTab.tomorr=Today+1
qualityTab.dayAfter=Today+2
qualityTab.all=All
outstanding.tabs.today=Today
outstanding.tabs.today1=Today+1
outstanding.tabs.today2=Today+2
outstanding.tabs.all=All
outstanding.tabs.todayminusone=Today-1

#-- Movement Summary Display --
movement.movementId=Movement
movement.party=Party
movement.date=Value
movement.amount=Amount
movement.currency=Ccy
movement.secondEntity=Second Entity
movement.reference1=Reference(1)
movement.bookcode=Book
movement.pos=Position
movement.predict=Predict
movement.payChannek=Pay Channel
movement.reference2=Reference(2)
movement.reference3=Reference(3)
movement.CcyName=Name
movement.movementType=Type
movement.counterPartyId=CParty
movement.notes=Notes
movement.account=Account
movement.pred=Pred
positionlevel.first=First
positionlevel.interim=Intermed
positionlevel.final=Final
positionlevel.total=Total
movement.value=Value
movement.sign=Sign
movement.ccy=Ccy
movement.id.entityId=Entity
movement.beneficiary=Beneficiary
movement.custodian=Custodian
movement.position=Pos
movement.alerting=alert
movement.pred=Pred
movement.status=Status
movement.type=MT
movement.matchChanged=This match has been changed
movement.mostUnmatched=This movement must be unmatched before it can be amended
movement.id.movement=Movement
movement.notes=Notes
movement.newreference1=Ref 1
movement.newreference2=Ref 2
movement.source=Source
movement.input=Input
movement.matchId=MatchId
movement.msgformat=Format
movement.matchingParty=Matching Party
movement.productType=Product Type
movement.Other=Other
movement.postingDate=Posting Date
movement.postingDateMSD=Post Date
movement.extraRef=Extra Ref
movement.extraText1=Extra Text 1
movement.expectedSettlement=Expected Settlement
movement.actualSettlement=Actual Settlement
movement.criticalPaymentType=Critical Payment Type
movement.ilmFcast=ILM FCast
movement.uetr= UETR

movement.orderingCustomer=Ordering Customer
tooltip.orderCus=Enter Ordering Customer ID
tooltip.movOrderingCustomer=Click to select ordering Customer
tooltip.enterOrderCusText1=Enter Ordering Customer text 1
tooltip.enterOrderCusText2=Enter Ordering Customer text 2
tooltip.enterOrderCusText3=Enter Ordering Customer text 3
tooltip.enterOrderCusText4=Enter Ordering Customer text 4
tooltip.enterOrderCusText5=Enter Ordering Customer text 5

movement.orderingInstitution=Ordering Institution
tooltip.enterOrderInstText1=Enter Ordering Institution text 1
tooltip.enterOrderInstText2=Enter Ordering Institution text 2
tooltip.enterOrderInstText3=Enter Ordering Institution text 3
tooltip.enterOrderInstText4=Enter Ordering Institution text 4
tooltip.enterOrderInstText5=Enter Ordering Institution text 5
tooltip.orderIns=Enter Ordering Institution ID
tooltip.movOrderingInst=Click to select an ordering institution


movement.senderCorrespondent=Sender's Correspondent
tooltip.sendCorrs=Enter Sender's Correspondent ID
tooltip.enterSenderCorrText1=Enter Sender's Correspondent text 1 
tooltip.enterSenderCorrText2=Enter Sender's Correspondent text 2
tooltip.enterSenderCorrText3=Enter Sender's Correspondent text 3
tooltip.enterSenderCorrText4=Enter Sender's Correspondent text 4
tooltip.enterSenderCorrText5=Enter Sender's Correspondent text 5
tooltip.movSenderCorres=Click to select an Sender's Correspondent


movement.receiverCorrespondent=Receiver's Correspondent
tooltip.recCorrs=Enter Receiver's Correspondent ID
tooltip.enterreceiverCorrText1=Enter Receiver's Correspondent text 1
tooltip.enterreceiverCorrText2=Enter Receiver's Correspondent text 2
tooltip.enterreceiverCorrText3=Enter Receiver's Correspondent text 3
tooltip.enterreceiverCorrText4=Enter Receiver's Correspondent text 4
tooltip.enterreceiverCorrText5=Enter Receiver's Correspondent text 5
tooltip.movReceiverCorres=Click to select an Receiver's Correspondent


movement.intermediaryInstitution=Intermediary Institution 
tooltip.interInstit=Enter Intermediary Institution ID
tooltip.intermediaryInstitutionText1=Enter Intermediary Institution text 1
tooltip.intermediaryInstitutionText2=Enter Intermediary Institution text 2
tooltip.intermediaryInstitutionText3=Enter Intermediary Institution text 3
tooltip.intermediaryInstitutionText4=Enter Intermediary Institution text 4
tooltip.intermediaryInstitutionText5=Enter Intermediary Institution text 5
tooltip.movintermediaryInstitution=Click to select an Intermediary Institution


movement.accountWithInstitution=Account with Institution
tooltip.accInstit=Enter Account with Institution ID
tooltip.enterAccountWithInstitutionText1=Enter Account with Institution Text 1
tooltip.enterAccountWithInstitutionText2=Enter Account with Institution Text 2
tooltip.enterAccountWithInstitutionText3=Enter Account with Institution Text 3
tooltip.enterAccountWithInstitutionText4=Enter Account with Institution Text 4
tooltip.enterAccountWithInstitutionText5=Enter Account with Institution Text 5
tooltip.moviaccountWithInstitution=Click to select an Account with Institution

movement.beneficiaryCustomer=Beneficiary Customer
tooltip.benCustomer=Enter Beneficiary Customer ID
tooltip.enterBeneficiaryCustomerText1=Enter Beneficiary Customer Text 1
tooltip.enterBeneficiaryCustomerText2=Enter Beneficiary Customer Text 2
tooltip.enterBeneficiaryCustomerText3=Enter Beneficiary Customer Text 3
tooltip.enterBeneficiaryCustomerText4=Enter Beneficiary Customer Text 4
tooltip.enterBeneficiaryCustomerText5=Enter Beneficiary Customer Text 5
tooltip.movBeneficiaryCustomer=Click to select an Beneficiary Customer

movement.senderToReceiverInfo=Sender/Receiver Info
tooltip.senderToReceiverText=Enter Sender/Receiver Info 1
tooltip.senderToReceiverText1=Enter Sender/Receiver Info 2
tooltip.senderToReceiverText2=Enter Sender/Receiver Info 3
tooltip.senderToReceiverText3=Enter Sender/Receiver Info 4
tooltip.senderToReceiverText4=Enter Sender/Receiver Info 5
tooltip.senderToReceiverText5=Enter Sender/Receiver Info 6

#-- Movement Search --
movementsearch.matchingParty=Matching Party
movementsearch.productType=Product Type
movementsearch.postingDateFrom=Posting Date From
tooltip.enterMatchingParty=Enter matching party
tooltip.enterProductType=Enter product type
tooltip.enterPostingDateFrom=Enter posting date from
tooltip.enterPostingDateTo=Enter posting date to
tooltip.movementSearch.uetr=Please enter UETR
movementsearch.entity=Entity
movementsearch.archive=Archive
movementsearch.sortorder=Sort Order
movementsearch.amount=Amount
movementsearch.valuedate=Value Date
movementsearch.reference1=Reference 1
movementsearch.reference2=Reference 2
movementsearch.reference3=Reference 3
movementsearch.credit/debit=Credit/Debit
movementsearch.swiftmessagetype=SWIFT Message Type
movementsearch.positionlevel=Position Level
movementsearch.uetr=UETR
movementsearch.bookcode=Book
movementsearch.matchstatus=Match Status
movementsearch.accountid=Account
movementsearch.status=Status
movementsearch.outstanding=Outstanding
movementsearch.offered=Offered
movementsearch.suspended=Suspended
movementsearch.confirmed=Confirmed
movementsearch.review=Review
movementsearch.allmatched=All Matched
movementsearch.allstatuses=All
movementsearch.amountover=Amount From
movementsearch.amountunder=Amount Under
movementsearch.debit/credit=Sign
movementsearch.debit=Debit
movementsearch.credit=Credit
movementsearch.both=Both
movementsearch.cash/sec=Type
movementsearch.cash=Cash
movementsearch.sec=Securities
movementsearch.valuefrom=Value Date From
movementsearch.valueto=To
movementsearch.timefrom=Input Time From
movementsearch.timeto=To
movementsearch.currency=Currency
movementsearch.counterparty=Counterparty
movementsearch.beneficiary=Beneficiary
movementsearch.custodian=Custodian
movementsearch.group=Group
movementsearch.metagroup=Metagroup
movementsearch.inputdate=Input Date
movementsearch.reference=Reference
movementsearch.include=Include
movementsearch.exclude=Exclude
movementsearch.postingDateFrom=Posting Date From
movementsearch.messageId=Message Format
movementsearch.predictstatus=Predict Status
movementsearch.included=Included
movementsearch.excluded=Excluded
movementsearch.cancelled=Cancelled
movementsearch.all=All
movementsearch.finance/trade=Finance/Trade
movementsearch.finance=Finance
movementsearch.trade=Trade
movementsearch.uetr= UETR

#-- Account Monitor Screen --
accountmonitor.title.window=Account Monitor - SMART-Predict
accountmonitor.today=Today
accountmonitor.today1=Today+1
accountmonitor.today2=Today+2
accountmonitor.month=1 Month
accountmonitor.date=Value Date
accountmonitor.acctId=Account
accountmonitor.prbalance=Predicted Balance
accountmonitor.fnbalance=Final Bal
accountmonitor.prbalin=Predicted Bal(In)
accountmonitor.fnbalin=Final Bal(In)
accountmonitor.prbalout=Predicted Bal(Out)
accountmonitor.fnbalout=Final Bal(Out)
accountmonitor.startbal=Starting Bal
alert.sumCutOffWarn=This change will not update accounts that are flagged to sum according to cut-off
currencymonitor.currId=Currency
currencyMonitor.title.window=Currency Monitor - SMART-Predict

#-- Account Breakdown Monitor Screen --
title.acctBreakdown.window=Account Breakdown Monitor - SMART-Predict
label.acctBreakdown.entity=Entity
label.acctBreakdown.date=Date
label.acctBreakdown.currency=Currency
label.acctBreakdown.ccy=Ccy
label.acctBreakdown.balance=Balance
label.acctBreakdown.acctClass=Account Class
label.acctBreakdown.hideZeroBalances=Hide Zero Balances
label.acctBreakdown.applyCurrencyThreshold=Apply Currency Threshold
label.acctBreakdown.acct=Account
label.acctBreakdown.acctName=Name
label.acctBreakdown.openUnexpected=Open Unexpected
label.acctBreakdown.startBalance=Start balance
label.acctBreakdown.sum=Sum
label.acctBreakdown.total=Total
alert.acctBreakdown.exceedDate=Selected date should be within 30 days from current date
alert.acctBreakdown.noMovements=No movements available
alert.acctBreakdown.date=Please enter a valid date
confirm.acctBreakdown.excludeFromTotal=Exclude this account from the totals?
confirm.acctBreakdown.includeInTotal=Include this account in the totals?
confirm.acctBreakdown.includeExcludeFromTotal=Include/Exclude this account from the totals?
confirm.acctBreakdown.moveToLoro=Move balance to Loro/Curr column?
confirm.acctBreakdown.moveFromLoro=Move balance to Predicted column?

tooltip.acctBreakdown.refresh=Refresh Account Breakdown Grid
tooltip.acctBreakdown.sum=Add Account Balance to Currency Total
tooltip.acctBreakdown.move=Move Loro Balance to Predicted Balance
tooltip.acctBreakdown.sum=Sum
button.sum=Sum
button.move=Move

confirm.title=Microsoft Internet Explorer

#-- Manual Input Screen --
manualInput.id.movementId=Movement*
movementDisplay.id.movementId=Movement
manualInput.id.movementId=Movement
manualInput.entity.id=Entity
movementDisplay.entity.id=Entity
manualInput.amount=Amount
movementDisplay.amount=Amount
movement.counterParty=Counterparty
movement.date=Value
movement.amount=Amount*
movement.amount1=Amount
movement.currency1=Currency
movement.reference1=Reference
movement.bookCode=Bookcode
movement.pos=Position
movement.predict=Predict
movementDisplay.parties=Parties

movement.reference1=Ref 1
movement.reference2=Ref 2
movement.reference3=Ref 3
movement.movementType=Type
movement.extraText=Extra Text
movement.positionLevel=Position Level
movement.accountId=Account
movement.beneficiary=Beneficiary
movement.beneficiaryInstitution=Beneficiary Institution
movement.custodian=Custodian
movement.messageId=Message ID
movement.messageFormat=Message
movement.valueDate=Value
movement.entity.id=Entity ID*
movementDisplaly.source=Source
notes.noteText=Note Text*
movement.pos1=Pos
movement.pred=Pred
movement.ccy=Ccy
movement.sign=Sign
notes.alert.recordNotFound=Record does not exist
errors.logon.invalid=Invalid login
errors.logon.invalidPassword=Invalid login
errors.logon.disableUser=This user ID has been locked. Please contact your System Administrator.
errors.logon.alreadyLoggedIn=This user is already logged in
errors.logon.exceednoofusers=Licensing Error: Maximum number of users exceeded
errors.logon.expirydate=Licensing Error: Date expired
errors.logon.hostName=Licensing Error: Not valid for this hostname ({0})
errors.logon.reg=Product is not registered
errors.logon.host=Licensing Error: Not valid for this host ({0}).
errors.logon.licenseCode=Licensing Error: Security Code is invalid.
errors.logon.MFAOnlyIsEnabled=You can login to the application only using Multi-Factor Authentication authentication, please contact your Administrator
errors.logon.userCreationDisabled=No linked user was found in the application, please contact your Administrator
#-- Manual Sweep Screen --
alert.MvmUnlockSysAdm=Movement cannot be unlocked. Please contact your System Administrator.
alertRemovingDeletematch=Removing these movements will delete the match. Continue?
# Added to visually indicate accounts past cut-off or subject to non-working day
manualSweeping.warning.messageForNonWorkingDays=WARNING - Sweeping may not be achievable for value-date for at least one of the selected accounts due to cut-off / non-working days. Do you wish to continue?
tooltip.signField=Sort by Sign
tooltip.sortByType=Sort by Type
tooltip.referenceField=Sort by reference 1
tooltip.partyField=Sort by party
tooltip.predictField=Sort by Predict status

tooltip.accountField=Sort by account
tooltip.access=Access
tooltip.bookcodeField=Sort by bookcode
tooltip.referenceField2=Sort by reference 2
button.prev=Prev
button.next=Next
tooltip.alertMatchAnotherProcess=Match is in use by another process
tooltip.alertMnmtDislay=This match has been changed
# movement.predictedBalance=Predicted Balance

movement.balances=Balance
movement.sweepAmount=Sweep Amount
movement.targetbalance=Target Balance
movement.level=Level
movement.cutOff=Cut Off
movement.isValueDateAchievable= Is Value Date Achievable
movement.displayLevel= Display Level
manual.doesnotmatch=You don't have access
manualSweep.tabs.all=1 Week
manualSweep.tabs.selectedDate=Selected Date
movement.Name=Name
tooltip.accName=Account Name
tooltip.sortAccName=Sort by account name

#-- Sweep Detail Screen --
sweepDetail.authQueue=Auth Queue
sweepDetail.minSweepAmt=Min Sweep Amount
sweepDetail.maxSweepAmt=Max Sweep Amount
sweepDetail.realignBal=Re-aligned Predicted Balance
sweepDetail.alignToTarget=Align to Target
sweepDetail.orgSweepAmt=Original Sweep Amount
sweepDetail.subSweepAmt=Submitted Sweep Amount
sweepDetail.authSwepAmt=Authorise Sweep Amount
sweepDetail.valueDate=Value Date
sweepDetail.trbalance=Target Balance
sweepDetail.trbalancetype=Target Balance Type
sweep.limitExceeded=Sweep amount exceeds your limit
sweep.limitNotDefined=Sweep limit not defined for this currency
sweep.Submitted=Sweep already submitted
sweep.alreadyAuthorised=Sweep already authorised
sweep.cutoffExceed=The cut off time has passed for this sweep
sweep.notSuccess=Sweep could not be saved successfully.<br>Please contact your System Administrator!
sweep.cutOffnotDefined=Invalid cut-off time defined
sweep.invalidDirectoryPath=Invalid directory path defined.<br>Refer to the error log for details!
sweep.accountTypeNotFound=Account type not found.<br>Refer to the error log for details!
sweep.errorinPredictBal=Error calculating predict balance.<br>Refer to the error log for details!
sweep.acctLevelNotFound=Account level not found.<br>Refer to the error log for details!
sweep.mainAcctNotFound=Main account not found.<br>Refer to the error log for details!
sweep.messgeFormatNotDefined=Message format not defined.<br>Refer to the error log for details!
sweep.errorInsertingAlert=Error inserting alerts.<br>Refer to the error log for details!
sweep.messageGenerationError=Error in message generation.<br>Refer to the error log for details!
sweepdetail.accountattribute.label=Acct Attr.
sweepdetail.rule.label=Rule
#-- Match display --
movement.doesnotexist=Movement is not for the selected entity
movement.diffcurrency=Movement is not for the selected currency
movement.notoutstanding=Movement is not outstanding
movement.diffvaluedate=The value date should be greater than or equal to today
movement.locked=Movement is in use by another process
alert.MovementInUse=Movement is in use by 
bookCode.entity=Entity
match.doesnotexist=Match not on file
alert.SmartPredict=SMART-Predict
alert.AccessNotAvl=Access not available for 
alert.ContactSysAdm=. Please contact your System Administrator.

#-- Errors for Movement Summary Display --
alert.MovementLocked=Some of the movements in a match (of the selected movements) are locked by another user.

#-- Sweep Display --
sweepDisplay.title.window=Sweep Display - SMART-Predict
sweepsearch.sweepId=Sweep
sweepsearch.debited=Account Debited
sweepsearch.credited=Account Credited
sweepsearch.entity=Entity
sweepsearch.sweepamt=Amount
sweepsearch.generatedby=Generated By
sweepsearch.originalamt=Original Amount
sweepsearch.submittedby=Submitted By
sweepsearch.submittedamt=Submitted Amount
sweepsearch.authorizedby=Authorised By
sweepsearch.authorizedamt=Authorised Amount
sweepsearch.bookcode=Book
sweepsearch.mvmntId=Movement
sweepsearch.matchId=Match
sweepsearch.postlevel=Position Level
sweepsearch.predictstatus=Predict Status
sweepsearch.entityCr=Entity CR
sweepsearch.entityDr=Entity DR
# external balance status
sweepsearch.externalstatus=External Status
sweepsearch.outgoingmsgs=Outgoing Message
sweepsearch.sweepdetails=Sweep Details
sweepsearch.sweephistory=Sweep History
sweepsearch.mvmntdetails=Movement Status
sweepsearch.time=Time
sweepsearch.status=Status
recovery.lastRun=Last Run
recovery.title.window=Matching Recovery
recovery.confrim.continue=Do you want to continue?

#-- Code Batch Jobs --
batchScheduler.title.window=Scheduler - SMART-Predict
batchScheduler.alert.executeDenied=Execute request denied: this process is already running
batchScheduler.alert.terminateDenied=Terminate request denied: this process is currently not running
batchScheduler.alert.removeDenied=Remove request denied: this process is currently running
batchScheduler.confirm.removeJob=Are you sure you want to remove the job?
batchScheduler.confirm.removeJobInfo=reports will be deleted too
batchScheduler.confirm.execute=Are you sure you want to execute?
batchScheduler.confirm.enable=This will enable the selected process
batchScheduler.confirm.disable=This will disable the selected process
batchScheduler.header.scheduledId=Id
batchScheduler.header.jobName=Job Name
batchScheduler.header.LastExe=Last Executed
batchScheduler.header.LastExeStatus=Last Status
batchScheduler.header.nextExeTime=Next Executed
batchScheduler.header.currentStatus=Current Status
batchScheduler.jobStatus.Pending=Pending
batchScheduler.jobStatus.Closing=Closing
batchScheduler.scheduledJobType=Scheduled Job Type
Jobmaintenance.JobId=Job ID
Jobmaintenance.JobName=Job Name
Jobmaintenance.Job=Job
Jobmaintenance.Name=Name
jobSetup.title.window=Job Setup - SMART-Predict
addjob.Startdate =Start date
addjob.StartTime =Start Time
addjob.Enddate   =End date
addjob.EndTime   =End Time
addjob.JobType   =Job Type
addjob.Cyclic    =Cyclic
addjob.Once      =Once
addjob.Daily     =Daily
addjob.Daily2    =Daily
addjob.Weekly    =Weekly
addjob.Weekly2   =Weekly
addjob.Monthly   =Monthly
addjob.Monthly2  =Monthly
addjob.Manual    =Manual
addjob.Date      =Date
addjob.Time      =Time
addjob.Hours     =Hrs
addjob.Minutes   =Min
addjob.Seconds   =Sec
addjob.CycleDuration=Cycle Duration
addjob.All       =All
addjob.Sunday    =Sunday
addjob.Monday    =Monday
addjob.Tuesday   =Tuesday
addjob.Wednesday =Wednesday
addjob.Thursday  =Thursday
addjob.Friday    =Friday
addjob.Saturday  =Saturday
addjob.FirstDate =First Day
addjob.LastDate  =Last Day
addjob.DayMonth  =Day of Month
addJob.alert.selectDays=Please select day(s)
addJob.alert.StartTimeGreater=Start date-time should be greater than or equal to current date-time
addJob.alert.EndTimeLesser=End date-time should be greater than or equal to current date-time
addJob.alert.SchDateTime=Scheduled date-time should be greater than or equal to current date-time
addJobDetails.title.Window=Add Job Details - SMART-Predict
changeJob.title.Window=Change Job Detail - SMART-Predict
viewJob.title.Window=View Job Detail - SMART-Predict
scheduler.rangeNotCovered=Based on the configured schedule, the job will never run.<br>Please check the details.
tooltip.selectJobName=Specify a name for display in the scheduler
tooltip.jobDescription=Enter a description and/or notes relating to the scheduled report
tooltip.jobEvaluateRunDate=Indicate whether RUN_DATE is evaluated in system time-frame or entity time-frame
tooltip.jobExecuteAsRole=Indicate the role-access privileges to use when generating the report
tooltip.jobOutputFileType=Choose the output file type from the available options
tooltip.jobFileLocation=Specify the output file location (beneath the system configured ScheduledReportLocation)
tooltip.jobNamePrefix=Specify the text that will precede the date and time in the name of the report file generated
tooltip.jobRetainFilesFor=Specify how long files are to be remain on the system before being removed during archiving
tooltip.jobAccessList=Indicate which users/roles will have access to reports in the Scheduled Report History screen, plus receive reports as email attachments

addJob.tab.scheduling=Scheduling
addJob.tab.reportSettings=Report Settings
addJob.title.frequency=Frequency
addJob.title.jobType=Job Type
addJob.title.reportType=Report Type
addJob.title.reportName=Report Name
addJob.title.reportDecription=Report Description
addJob.title.evaluateRunDateAs=Evaluate RUN_DATE as
addJob.title.evaluateRunDateAs.value1=System Date
addJob.title.evaluateRunDateAs.value2=Entity Date
addJob.title.executeAsRole=Execute As Role
addJob.title.parameters=Parameters
addJob.button.configure=Configure
addJob.title.outputFileType=Output File Type
addJob.title.outputFileLocation=Output File Location
addJob.title.fileNamePrefix=File Name Prefix
addJob.title.retainFilesFor=Retain Files For
addJob.title.days=Days
addJob.title.accessList=Access List
addJob.title.showXML=Report Configuration parameters XML
addJob.title.mail=Mail (Attachment)
addJob.combo.report=Report
addJob.combo.process=Process
tooltip.selectJobType=Select Job Type
tooltip.sortByFrequency=Sort by Frequency
addJob.tab.role=Roles
addJob.tab.user=Users
addJob.title.accessList=Access List
addJob.button.DistList=Dist List
addjob.title.distlist=Distribution List
addjob.distlist.alert=Based on selection, the mail will not be sent to any user. Please select at least one user/role.
reportScheduler.title.window=Report Scheduler - SMART-Predict
processScheduler.title.window=Process Scheduler - SMART-Predict
addjob.alert.invalidFileNamePrefix=Please enter a valid File Name Prefix
addjob.alert.invalidOutputFileLocation=Please enter a valid Output File Location
addjob.alert.invalidRetainFiles=Please enter a valid number of days for Retains Files
addjob.alert.noConfigBeforeSave=No configuration is supplied for the report job parameters, please define before saving
addjob.alert.paramConfigNotCorrectBeforeSave=Report parameter configuration values are not correct please verify before saving.\\nCause: 
addjob.label.configParamStatusNoConfig=No configuration is supplied
addjob.label.configParamStatusCorrectConfig=Configuration is correctly supplied
addjob.label.configParamStatusIncorrectConfig=Configuration is NOT correctly supplied
addjob.label.noAccessinAllEntity=The selected role does not have access in 'All' entity
addjob.label.noAccessinEntity=The selected role does not have access in {0} entity
addjob.label.noAccessinEntityCurrency=The selected role does not have access in combination {0} entity and {1} currency
addjob.label.noAccessinILMGroup=\\nThe ILM group {0} does not exists
addjob.label.noAccessinILMScenario=\\nThe ILM scenario {0} does not exists
ilmreport.keyword.label.runDate=the date that report is being run
ilmreport.keyword.label.runDateMinus1=RunDate minus 1 day
ilmreport.keyword.label.runDateMinus2=RunDate minus 2 days
ilmreport.keyword.label.runDateMinus3=RunDate minus 3 days
ilmreport.keyword.label.runDateMinus4=RunDate minus 4 days
ilmreport.keyword.label.runDateMinus5=RunDate minus 5 days
ilmreport.keyword.label.runDateMinus6=RunDate minus 6 days
ilmreport.keyword.label.runDateMinus7=RunDate minus 7 days
ilmreport.keyword.label.startOfCurrentWeek=Start of the current week
ilmreport.keyword.label.endOfCurrentWeek=End of the current week
ilmreport.keyword.label.startOfPreviousWeek=Start of the previous week
ilmreport.keyword.label.endOfPreviousWeek=End of the previous week
ilmreport.keyword.label.startOfCurrentMonth=Start of the current month
ilmreport.keyword.label.endOfCurrentMonth=End of the current month
ilmreport.keyword.label.startOfPreviousMonth=Start of the previous month
ilmreport.keyword.label.endOfPreviousMonth=End of the previous month
ilmreport.keyword.label.startOfCurrentQuarter=Start of the current quarter
ilmreport.keyword.label.endOfCurrentQuarter=End of the current quarter
ilmreport.keyword.label.startOfPreviousQuarter=Start of the previous quarter
ilmreport.keyword.label.endOfPreviousQuarter=End of the previous quarter
ilmreport.keyword.label.startOfCurrentYear=Start of the current year
ilmreport.keyword.label.endOfCurrentYear=End of the current year
ilmreport.keyword.label.startOfPreviousYear=Start of the previous year
ilmreport.keyword.label.endOfPreviousYear=End of the previous year


#-- Sweep Search --
sweepSearch.title.window=Sweep Search - SMART-Predict
sweepSearch.alert.amount=Amount Under should be greater than Amount Over
sweepSearch.alert.datecomparison=From date must be earlier than To date
tooltip.selectType=Select type
tooltip.selectClick=Click to select account ID
tooltip.selectBookcode=Select a bookcode
tooltip.selectPostcutoff=Select post cut off
sweep.sort=Sort
sweep.amount=Amount
sweep.valuedate=Value Date
sweep.creditaccountId=Account ID CR
sweep.debitaccountId=Account ID DR
sweep.debitedmessage=Msg Type DR
sweep.creditedmessage=Msg Type CR
sweep.amountover=Amount From
sweep.amountunder=Amount To
sweep.valudatefor=Value Date From
sweep.to=To
sweep.accountId=Account ID
sweep.bookcode=Book
sweep.generatedby=Generated By
sweep.submittedby=Submitted By
sweep.authorby=Authorised By
sweep.postcutoff=Post cut off
sweep.postcutoff1=Post cut off
sweep.postlevel=Position Level
sweep.accounttype=Account Type
sweep.status=Status
sweep.submitted=Submitted
sweep.authorized=Authorised
sweep.cancelled=Cancelled
sweep.auto=Auto
sweep.manual=Manual
sweep.msgformat=Message Format
sweep.type=Type
sweep.CcyName=Ccy Name

#-- Movement Recovery --
movementRecovery.updateUser=User
movementRecovery.lockTime=Lock Time
MovementRecovery.title.window=Movement Recovery - SMART-Predict
MovementRecovery.confirm.unlock=Are you sure you want to unlock?

#-- Code For Title of all screens --
metaGroup.title.mainWindow=Metagroup Maintenance - SMART-Predict

partySearch.title.mainWindow=Party Search - SMART-Predict
positionLevel.title.mainWindow=Position Level Maintenance - SMART-Predict
positionLevel.alert.mandatoryFields=Please fill all mandatory fields (marked with *)
matchquamaintenance.title.MatchQuality=Match Quality Maintenance - SMART-Predict
party.title.mainWindow=Party Maintenance - SMART-Predict
groupmaintenance.title.MainWindow=Group Maintenance - SMART-Predict
groupmaintenance.title.bookMain=Book Maintenance - SMART-Predict
holidays.title.mainWindow=Holiday Maintenance - SMART-Predict
mvmMatchSummDisplay.title.window=Movement Match Summary Display - SMART-Predict
manualMatch.title.window=Manual Match - SMART-Predict
bookMaintenance.title.mainWindow=Book Maintenance - SMART-Predict
sweepSearchResults.title.window=Sweep Search Results - SMART-Predict
balMaintenance.title.addWindow=Change Balance - SMART-Predict
balMaintenance.title.viewWindow=View Balance - SMART-Predict
movementSummDisplay.title.Window=Movement Summary Display {0} - SMART-Predict
entitymaintenance.changeScreen=Change Entity - SMART-Predict
entitymaintenance.addScreen=Add Entity - SMART-Predict
entitymaintenance.mainScreen=Entity Maintenance - SMART-Predict
generalsysparam.changescreen=Change General System Parameters - SMART-Predict
generalsysparam.mainScreen=System Parameter Maintenance - SMART-Predict
currencymaitenance.addScreen=Add Currency - SMART-Predict
currencymaitenance.changeScreen=Change Currency - SMART-Predict
metagrpmaintenance.addScreen=Add Metagroup - SMART-Predict
metagrpmaintenance.changeScreen=Change Metagroup - SMART-Predict
bookmaintenance.addScreen=Add Book
bookmaintenance.changeScreen=Change Book
holidays.addScreen=Add Holiday - SMART-Predict
partymaintenance.addScreen=Add Party - SMART-Predict
partymaintenance.changeScreen=Change Party - SMART-Predict
messagefieldFormats.viewScreen=View Message Field - SMART-Predict
messagefieldformat.addScreen=Add Message Field - SMART-Predict
messagefieldformat.changeScreen=Change Message Field - SMART-Predict
positionlevel.addScreen=Add Position Level - SMART-Predict
positionlevel.changeScreen=Change Position Level - SMART-Predict
openQueue.screen=Excluded Outstanding Queue - SMART-Predict
movement.screen=Movement Audit Log - SMART-Predict
user.screen=User Log - SMART-Predict
messageFormats.addScreen=Add Sweep Message Format - SMART-Predict
messageFormats.changeScreen=Change Sweep Message Format - SMART-Predict
messageFormats.viewScreen=View Sweep Message Format - SMART-Predict
messageScenarioFormats.addScreen=Add Scenario Message Format - SMART-Predict
messageScenarioFormats.changeScreen=Change Scenario Message Format - SMART-Predict
messageScenarioFormats.viewScreen=View Scenario Message Format - SMART-Predict
rolemaintenance.mainScreen=Role Setup - SMART-Predict
rolemaintenance.addScreen=Add Role - SMART-Predict
rolemaintenance.changeScreen=Change Role - SMART-Predict
rolemaintenance.viewScreen=View Role - SMART-Predict
menuaccessoptions.addScreen=Add Menu Access Options - SMART-Predict
menuaccessoptions.changeScreen=Change Menu Access Options - SMART-Predict
menuaccessoptions.viewScreen=View Menu Access Options - SMART-Predict
entityaccesslist.addScreen=Add Entity Access - SMART-Predict
entityaccesslist.changeScreen=Change Entity Access - SMART-Predict
entityaccesslist.viewScreen=View Entity Access - SMART-Predict
#Mantis 6455
roleBasedControl.addScreen=Add Role Based Control - SMART-Predict
roleBasedControl.changeScreen=Change Role Based Control - SMART-Predict
roleBasedControl.viewScreen=View Role Based Control - SMART-Predict
roleBasedControl.column.facility.tooltip=Facility or screen
roleBasedControl.column.reqAuth.tooltip=Specify whether this role user requires authorisation by another
roleBasedControl.column.authOther.tooltip=Specify whether this role user can authorise other use
#Mantis 6455
copyFromRole.alert.oneEntity=At least one entity is required for full or view only access
copyFromRole.alert.deleteWorkQ1=Please delete Work Queue Access records for entity 
copyFromRole.alert.deleteWorkQ2=before setting No Access
workqueueaccess.mainScreen=Work Queue Access - SMART-Predict
workqueueaccess.addScreen=Add Work Queue Access - SMART-Predict
workqueueaccess.changeScreen=Change Work Queue Access - SMART-Predict
workqueueaccess.viewScreen=View Work Queue Access - SMART-Predict
sweepinglimits.mainScreen=Sweeping Limits - SMART-Predict
sweepinglimits.addScreen=Add Sweeping Limits - SMART-Predict
sweepinglimits.changeScreen=Change Sweeping Limits - SMART-Predict
sweepinglimits.viewScreen=View Sweeping Limits - SMART-Predict
rolemaintenance.copyFromScreen=Copy Role Details - SMART-Predict
sweepSearchResults.title.window=Sweep Search Results - SMART-Predict
ilaapgeneralsysparam.changescreen=Change ILM General Parameters- SMART-Predict
ilaapgeneralsysparam.mainScreen=ILM General Parameters - SMART-Predict
#-- Alert Message --
sweepId.doesnotmatch=Please enter a valid sweep ID
sweepId.alert.noAccess=Invalid: your role does not provide access to this sweep
sweepId.alert.sweepIdamended=Sweep ID field has been amended - Please choose action again after screen refreshes
sweepsearch.id.sweepId=Sweep ID is not proper
monitor.nomovements=No movements available
mainaccountId.doesnotmatch=Not a valid main account ID
matchId.alreadyexist=Unable to generate match.<br>Please try again.<br>If the problem persists contact your System Administrator.
relogin.warning=Do you want to refresh the menu window?

movementSummaryDisplay.drilldownTitlePredicted=Predicted
movementSummaryDisplay.drilldownTitleUnsettled=Unsettled
movementSummaryDisplay.drilldownTitleUnexpected=Unexpected
movementSummaryDisplay.drilldownTitleLoro=Loro
movementSummaryDisplay.drilldownTitleExternal=External
movementSummaryDisplay.drilldownTitleOpenUnexpected=OpenUnexpected

#-- Entity Maintenance --
poslevel.indicator=Int/Ext
entity.PosLvl=Pos Lvl
tooltip.1positionLevel=First position level
tooltip.1positionName=First position name
tooltip.2positionLevel=Second position level
tooltip.2positionName=Second position name
tooltip.3positionLevel=Third position level
tooltip.3positionName=Third position name
tooltip.4positionLevel=Fourth position level
tooltip.4positionName=Fourth position name
tooltip.5positionLevel=Fifth position level
tooltip.5positionName=Fifth position name
tooltip.6positionLevel=Sixth position level
tooltip.6positionName=Sixth position name
tooltip.7positionLevel=Seventh position level
tooltip.7positionName=Seventh position name
tooltip.8positionLevel=Eighth position level
tooltip.8positionName=Eighth position name
tooltip.9positionLevel=Final position level
tooltip.9positionName=Final position name
entity.general.BIC=BIC
tooltip.entity.general.BIC=BIC
interestRate=Ccy Interest Rate
exchangeRate=Ccy Exchange Rate
entity.currencyInterstRate=Ccy interest rate
entity.currencyExchangeRate=Ccy exchange rate
entity.positionLevel=Add position level
button.positionLevel=Pos Lvl
entity.predict.sweepposition=Positions
entity.sweepPoslvl=Sweep
entity.preadv=Pre advice
tooltip.changeposLvl=Change position level
ent.deleteSelectedPosLvl=Selected position level is defined as sweep/pre-advice position.\\n Are you sure you want to delete?
ent.posLvlRecordExist=Record Already Exists

#-- Currency Group Maintenance --
tooltip.sortCurrencyGroup=Sort by currency group
tooltip.sortCurrencyGroupName=Sort by Name
tooltip.sortOrder=Sort by order
currency.group=Currency Group
currency.order=Order
currency.currencyGroup=Currency Group
tooltip.AddNewCurrencyGroup=Add new currency group
tooltip.ChangeSelectedCurrencyGroup=Change selected currency group
tooltip.DeleteSelectedCurrencyGroup=Delete selected currency group
tooltip.Currencies=Currencies
tooltip.currencyGroupIdentifier=Enter currency group ID
tooltip.currencyGroupName=Enter currency group name
currencyGroupMaintenance.title.currencies=Currencies - SMART-Predict
currencyGroup.addScreen=Add Currency Group - SMART-Predict
currencyGroup.changeScreen=Change Currency Group - SMART-Predict
currencyGroupChild.alert.IdasAll=Currency Group 'All' not permitted

#-- Currency Group Maintenance --
currencygroup.group=Group
currencygroup.ccy=# Ccy
tooltip.sortCurrencygroup=Sort by currency group
tooltip.sortCurrency=Sort by currency
button.currency=Ccy
tooltip.Currency=Add currency
currencyGroupMaintenance.title.mainWindow=Currency Group Maintenance - SMART-Predict
currencygroup.currencies=Currency
currencygroupmaintenance.title.currencies=Currencies - SMART-Predict
tooltip.group=Enter currency group ID
tooltip.groupName=Enter currency group name
tooltip.currency.gmtOffset=Define the currency's working day relative to GMT - Used in liquidity analysis functionality for determining currency timeframe. No value indicate to use the entity-local timeframe.

#-- Account Maintenance --
account.accclass=Account Class
account.linkacc=Link Account To
button.linked=Linked
tooltip.button.linked=Show Linked Accounts
account.fieldset=Monitor
account.monitor=Monitor
account.sum=Sum
account.fieldsetBalance=Start of Day Balance
button.contact=Contact
button.format=Format
button.rates=Rates
account.correspondentBIC=Correspondent BIC
account.nostrotype=Nostro
acc.lorotype=Loro
acc.currenttype=Current
acc.othertype=Other
acc.netting=Netting
acc.importinternal=Import Internal
acc.importMT950=Import MT950
tooltip.acc.importinternal=Select Import Internal
tooltip.acc.importMT950=Select Import MT950
acc.predicted=Predicted
acc.zero=Zero
tooltip.acc.predicted=Select Predicted
tooltip.acc.zero=Select Zero
acc.credit=Credit
acc.overdraft=Debit
button.rates=Rates
tooltip.linkAc=Link-account
tooltip.contact=Add contact
tooltip.format=Add format
tooltip.rates=Add rates
tooltip.overdraft=Enter overdraft
fielset.genDeatils=General
section.sectionName=Name
contact.phone=Phone
contact.email=Email
contact.fieldet=Details
title.accContacts=Contact Details - SMART-Predict
title.addContacts=Add Contact Details - SMART-Predict
title.changeContacts=Change Contact Details - SMART-Predict
title.viewContacts=View Contact Details - SMART-Predict
title.linkedaccount=Linked Account Details - SMART-Predict
title.subaccounts=Sub Account Details - SMART-Predict
title.addFormats=Add Format Details - SMART-Predict
title.changeFormats=Change Format Details - SMART-Predict
title.viewFormats=View Format Details - SMART-Predict
tooltip.archiveData=Enable Data Archiving process to move this account's data to the archive database instead of deleting it only

#-- Format Account Maintenance --
format.fieldset=New
format.cancel=Cancel
format.cdInt=Credit Internal
fomat.cdExt=Credit External
fomat.ddInt=Debit Internal
format.ddExt=Debit External
format.cancdInt=Cancel Credit Internal
fomat.cancdExt=Cancel Credit External
fomat.canddInt=Cancel Debit Internal
format.canddExt=Cancel Debit External
tooltip.newcdint=Enter new credit internal
tooltip.newcdext=Enter new credit external
tooltip.newddint=Enter new debit internal
tooltip.newddext=Enter new debit external
tooltip.cancdint=Enter cancel credit internal
tooltip.cancdext=Enter cancel credit external
tooltip.canddint=Enter cancel debit internal
tooltip.canddext=Enter cancel debit external
title.accountformat=Sweep Messages Format - SMART-Predict

#-- Account Interest Maintenance --
accint.timedate=Update Date/Time
tooltip.credit=Sort by credit
tooltip.overdraft=Sort by overdraft
title.accountInterest=Account Interest Rate - SMART-Predict
tooltip.addinterest=Add new account interest
tooltip.changeinterest=Change selected account interest
tooltip.deleteinterest=Delete selected account interest

#-- Party Maintenance --
party.header=Type
party.typecust=Custodian
party.branch=Branch
party.current=Current
party.other=Other
party.parentParty=Parent ID
party.parentId=Parent Party
tooltip.party.selectParentParty=Select a parent party
party.type=Select a Party Type

#-- Currency Exchange Maintenance --
contact.dom.exchange=FX Rate
contact.ccy.exchange=FX Rate
exchange.date=Enter exchange date
exchange.rate=Enter FX Rate
exchange.add=Add new exchange rate
exchange.change=Change selected exchange rate
exchange.delete=Delete selected exchange rate
exchange.cal=Select exchange date
exchange.tooltip.currName=Sort by currency name
exchange.tooltip.updateDate=Sort by update date & time
exchange.tooltip.user=Sort by user
exchange.mainTitle=Currency Exchange Rate Maintenance - SMART-Predict
exchange.addTitle=Add Currency Exchange Rates - SMART-Predict
exchange.changeTitle=Change Currency Exchange Rates - SMART-Predict
tooltip.currencyname=Currency Name
tooltip.exchangerate=FX Rate
tooltip.updatedatetime=Update Date Time
tooltip.user=User
alert.currencyExchangeRate.invalidRate=Invalid amount: Allowed format is 20 digits with up to 7 digits after the decimal place
alert.currencyExchangeRate.invalidCreditRate=Invalid credit margin: Positive value is not allowed.
alert.currencyExchangeRate.invalidDraftRate=Invalid debit margin: Negative value is not allowed.
#-- Balance Log --
startBalLog.title.MainWindow=Starting Balance Audit Log - SMART-Predict
startBalBefore=Change
startBalAfter=New Balance
tooltip.sortByBalanceBefore=Sort by balance before
tooltip.sortByBalanceAfter=Sort by balance after
sessionTimeOutMessage=Your Session has been timed out
sessionInactiveMessage=Your Session has been logged off due to inactivity
errors.logon.inactiveDisable=Your user ID has been disabled due to inactivity. Please contact your System Administrator.
errors.content.notAllowed=WARNING: Malicious content detected in request!
errors.content.notAllowed.description=As a security measure, your request is blocked as it contains the word(s):
errors.content.notAllowed.urlDescription=As a security measure, your request is blocked for security reasons
errors.csrf.attack=As a security measure, your request is blocked, a CSRF Attack detected!
errors.csrf.attack.wrongToken=As a security measure, your request is blocked, a CSRF Attack detected! -  wrong CSRF token
errors.authorization.attack=As a security measure, your request is blocked, an authorization bypass is detected!
errors.authorization.attack.session=As a security measure, your request is blocked, an session hijacking was detected!
errors.csrf.attack.log=SECURITY WARNING - Client request blocked: A CSRF attack detected
errors.csrf.attack.wrongToken.log=SECURITY WARNING - Client request blocked: A CSRF attack detected -  wrong CSRF token
errors.authorization.attack.log=SECURITY WARNING - Client request blocked: An authorization bypass was detected
errors.csrf.attack.RefererError=Reason: Referer header tag is Empty or does not fit server URL
errors.csrf.attack.invalidSession=Reason: User Session in invalid
errors.csrf.attack.emptyToken=Reason : Empty CSRF token
errors.user.log=User
errors.ipAddress.log=IP Adress
errors.requestURI.log=Request URI
#-- Account Interest Rate --
tooltip.accIntRateDate=Account Interest Rate Date
tooltip.accIntCredit=Account Interest Rate Credit Margin
tooltip.accIntRateOverDraft=Account Interest Rate Debit Margin
tooltip.accIntRateUpdateDate=Account Interest Rate Update Date/Time
tooltip.accIntRateUpdateUser=Account Interest Rate Update User
tooltip.enteraccIntRateCredit=Enter Credit Margin
tooltip.enteraccIntRateOverDraft=Enter Debit Margin
accIntRate.Date=Date
accIntRate.Credit=Credit Margin
accIntRate.OverDraft=Debit Margin
accIntRate.UpdateDate=Update Date/Time
accIntRate.UpdateUser=User
movsearch.timefrom=Time From
tooltip.positionlevel=Sort by position level
tooltip.entityu.indicator=Sort by indicator
button.entity.add=Add new position level
buttton.entity.change=Change selected position level
button.entity.delete=Delete selected position level
label.entity.positionName=Enter position name
label.entity.indicator=Select indicator
label.entity.internalIndicator=Internal
label.entity.externalIndicator=External

#-- Match Display --
matchQuality.posTotalInternal=Internal
matchQuality.posTotalExternal=External
matchAuditLog.screen=Match Audit Log - SMART-Predict
matchAuditLog.movement=Movement
tooltip.sortItemNum=Sort by movement ID

#-- Refresh Rate --
autoRefreshRate=Auto-refresh rate
tooltip.enterRate=Enter refresh rate
refreshRate.title.window=Auto-refresh Rate
label.refreshRate.seconds=&nbsp;seconds
alert.refreshRate.confirmMinRate=Refresh rate selected was below minimum.\\nSet it to 5 seconds.

#-- Currency Monitor --
currMonitor.breakdown=Breakdown
currMonitor.crrGrp=Currency Group
currMonitor.alert.fromDateValidation=From date should be within 180 days of system date
currMonitor.alert.dateRangeValidation=Date range should not be greater than 30 days
tooltip.ChangeSweep=Change selected sweep

#-- Archive Maintenance --
title.archive=Archive Setup - SMART-Predict
title.archiveadd=Archive Details Add - SMART-Predict
title.archivechange=Archive Details Update - SMART-Predict
tooltip.archiveId=Enter Archive ID
tooltip.archiveName=Enter Archive Name
tooltip.db_Link=Enter DB_LINK
tooltip.schemaName=Enter Schema Name
tooltip.defaultDb=Current Database
archive.id.archiveId=Archive ID
archive.module=Module
archive.archiveName=Archive Name
archive.db_link=DB Link
archive.schemaName=Schema Name
archive.defaultDb=Current?
archive.type=Type
archive.typeD=DB Link
archive.typeS=Schema
button.testcon=TestCon
tooltip.testconnection=Test Connection
tooltip.addArchive=Add new Archive
tooltip.changeSelectedArchive=Change Selected Archive
tooltip.deleteSelectedArchive=Delete Selected Archive
errors.archive.add=Please Select the Current Database
errors.archive.delete=Cannot delete the Current Database
connection.passed=Connection successful
connection.failed=Connection failed! Please contact the DBA to check the database link settings
archive.databaseLink=Database Link
tooltip.sortArchiveId=Sort by Archive ID
tooltip.sortModule=Sort by module ID
tooltip.chooseModule=Choose Module ID
tooltip.sortArchiveName=Sort by Archive Name
tooltip.sortDatabaseLink=Sort by Database Link
tooltip.sortCurrentArchive=Sort by Current Archive
tooltip.sortTypeArchive=Sort by Archive Type
#-- Account Monitor New --
tooltip.accounId=Sort by Account ID
tooltip.sortBalance=Sort by Balance
tooltip.sortStartingBalance=Sort by Starting Balance
tooltip.totalBalance=Total Balance
tooltip.totalStartingBalance=Total Starting Balance
tooltip.totalOpenUnexpectedBalance=Total Open Unexpected Balance
tooltip.RateButton=Rate Window
tooltip.sumButton=Manipulate Sum flag
tooltip.selectAccountId=Select Account ID
tooltip.selectBalanceType=Select balance Type
accountmonitorBrkDown.title.window=Account Breakdown Monitor - SMART-Predict
accountMonitorNew.date=Date
tooltip.accountMonitorNew.date=Select date
accountmonitorbutton.Rate=Rate
accountmonitorbutton.Sum=Sum
accountmonitor.confirm.title=Microsoft Internet Explorer
accountmonitor.selected=Selected
accountmonitor.confirm.testMessageY=Exclude this account and all linked accounts from the totals?
accountmonitor.confirm.testMessageN=Include this account and all linked accounts in the totals?
accountmonitor.confirm.testMessageP=Include/Exclude this account and all linked accounts from the totals?
tooltip.sortPredictedBalance=Sort by Predicted balance
tooltip.sortUnsettledBalance=Sort by Unsettled balance
tooltip.sortOpenUnexpectedBalance=Sort by Open Unexpected balance
tooltip.sortLoroBalance=Sort by Loro balance
tooltip.sortExternalBalance=Sort by External balance
tooltip.sortSum=Sort by Sum
tooltip.rateButton=Rate window
tooltip.sumButton=Sum window
accountmonitorNew.Predicted=Predicted
accountmonitorNew.Unsettled=Unsettled
accountmonitorNew.Unexpected=Unexpected
tooltip.accountMonitorNew.accumulatedSODBalAsString=Start of Day Balance
tooltip.accountMonitorNew.openUnexpectedBalTotalAsString=Show open unexpected balance
tooltip.AcctBreakDown=Select Account Breakdown to view Account Breakdown Monitor
tooltip.MoveSummaryDisplay=Select Movement to view movement summary display
accountmonitorNew.Loro=Loro/Curr
accountmonitorNew.External=External
accountmonitorNew.Sum=Sum
accountmonitorNew.Total=Total
accountMonitorNew.alert.date=Please select date

#- book Monitor
bookMonitor.title.window=Book Monitor - SMART-Predict
bookMonitor.currency=Currency
bookMonitor.date=Date
bookMonitor.book=Book
bookMonitor.bookName=Book Name
bookMonitor.name=Name
bookMonitor.balance=Predicted Balance
bookMonitor.today1=Today + 1
bookMonitor.today=Today
bookMonitor.today1=Today+1
bookMonitor.today2=Today+2
bookMonitor.selected=Selected
bookMonitor.total=Total
button.rate=Rate
tooltip.movSearchRolled=Rolled
title.AddaccContacts=Add Contact Details - SMART-Predict
title.ChangeaccContacts=Change Contact Details - SMART-Predict
changepasswordRules.title.window=Change Password Rules - SMART-Predict

#-- Movement Roll Over --
movementroll.dateValidateOriginal=The Value date should be greater than original value date
movementroll.dateValidateSystemDate=The Value date cannot be earlier than current system date
movementroll.rollNotAllowed=This movement cannot be Rolled Over
movementroll.dateValidatePreviousDate=The Value date should be greater than previous value date

#-- My User Details --
tooltip.user.id=User ID
tooltip.userPassword=Enter password (Acceptable characters:[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+;:'&quot;,<.>/?])
tooltip.user.name=Change User Name
tooltip.userpassword=Password
tooltip.status=User Status
tooltip.userrole=User Role
tooltip.user.lastlog=Last Login
tooltip.user.lastpass=Last Password Change
tooltip.user.lastlogout=Last Logout
tooltip.selectlevel=Select Group Level
acctextraid=Extra ID
acctglcode=GL Code
acctbiccode=Correspondent BIC
autoswpswitch=Auto Sweep
monitor.norecords=No records available
autoswpswitch=Auto Sweep
cannotdelete.message=Cannot delete your own User ID
cannotdelete.admin=Cannot delete ADMIN ID
cannotkill.message=Cannot kill your own session
schedulerError=Control --> Scheduler --> Matching process
matchQualityNotDefined=Match quality not defined for 
partyAlias.alias=Alias
party.alias=#Aliases
button.alias=Alias
partyalias.title.mainWindow=Party Alias Summary - SMART-Predict
partymaintenance.addAliasScreen=Add Party Alias - SMART-Predict
tooltip.enterPartyAlias=Enter party alias
party.partyAlias=Alias
preadviceInput.reference=Reference
manualInput.reference=References
tooltip.numberOfAliases=Number of Aliases
tooltip.partyAlias=Alias

#-- Sweep NAK --
sweep.NAKs=NAKs
tooltip.sortNAks=Sort by NAKs
tooltip.viewSelMessage=View selected message
sweepNAKQueue.title.window=Sweep Exceptions Queue - SMART-Predict
sweepNAKSummary.title.window=Sweep Exceptions Summary - SMART-Predict
sweepNAKQueue.overduetime=Overdue
tooltip.viewSweepDetails=Sweep Display
tooltip.viewNAKmessage=View NAK message

#-- Input Exceptions --
inputexceptions.title.window=Input Exception - SMART-Predict
inputexceptionsmessages.title.window=Input Exceptions Message Details - SMART-Predict

#-- Workflow Monitor Main Titles --
workflowmonitor.title.window=Workflow Monitor - SMART-Predict
workflowmonitor.reconciled=Reconciled
workflowmonitor.entity.title=Entity
workflowmonitor.curGrp.title=Currency Group
workflowmonitor.date.title=Date
workflowmonitor.timet.title=Time
workflowmonitor.movMent.title=Movements
workflowmonitor.unsYestday.title=Unsettled yesterday
workflowmonitor.unxYestday.title=Unexpected Yesterday
workflowmonitor.bkVal.title=Back valued
workflowmonitor.input.title=Input
workflowmonitor.excep.title=Exceptions
workflowmonitor.auth.title=Authorise (All days)
workflowmonitor.reff.title=Referred (All days)
workflowmonitor.matches.title=Matches (Total / No-Included)
workflowmonitor.offered.title=Offered
workflowmonitor.suspended.title=Suspended
workflowmonitor.confirmed.title=Confirmed
workflowmonitor.sweeps.title=Sweeps
workflowmonitor.submit.title=Submit
workflowmonitor.authorise.title=Authorise
workflowmonitor.submit.Exception=Exceptions
workflowmonitor.incMovmnts.title=Included Movements: Today
workflowmonitor.totalMov.title=Total included movements today
workflowmonitor.exclOut.title=Excluded Outstandings: Today
workflowmonitor.system.title=System
workflowmonitor.logon.title=Logged on
workflowmonitor.errors.title=Errors
workflowmonitor.refresh.title=Refresh
workflowmonitor.rate.title=Rate
workflowmonitor.close.title=Close
workflowmonitor.context.xml=Show workflow details XML
workflowmonitor.title.xml=Workflow Details XML
colors.context.xml=Change colours

workflowmonitor.option.applied=Applied - Checkbox checked
workflowmonitor.option.notApplied=Not Applied - Checkbox unchecked
workflowmonitor.option.optionLabel=On entry, the amount currency threshold will be


#-- Generic Display Monitor --
genericdisplaymonitor.title.window=Generic Display - SMART-Predict
breakdownmonitor.title.window=PCM Breakdown Monitor - SMART-Predict
button.genericdisplaymonitor.refresh=Refresh
button.genericdisplaymonitor.close=Close
button.genericdisplaymonitor.goTo=Go to 
button.genericdisplaymonitor.reActivate=Re-Activate
button.genericdisplaymonitor.resolve=Resolve
button.genericdisplaymonitor.details=Details
#-- Help Screens --
tooltip.helpScreen=Help screen content

#-- Legends for Maintenance --
changeBalance.legend.Balance=Balance
changeBalance.legend.userDetail=User Details
format.legend.new=New
format.legend.cancel=Cancel

#-- Alerts for Maintenance --
contactDetails.alert.emailaddinvalidchar=Email address contains invalid characters
contactDetails.alert.emailaddcontainsnonasciichar=Email address contains non ASCII characters
contactDetails.alert.emailaddcontain@=Email address must contain an @
contactDetails.alert.notstrtemailaddcontain@=Email address must not start with @
contactDetails.alert.cntainonceemailaddcontain@=Email address must contain only one @
contactDetails.alert.emailaddcontainperiod=Email address must contain a period in the domain name
contactDetails.alert.emailaddntfollow@=Period must not immediately follow @ in email address
contactDetails.alert.emailaddntprecede@=Period must not immediately precede @ in email address
contactDetails.alert.twoperiodntadjacentemailadd=Two periods must not be adjacent in email address
contactDetails.alert.invaildprimrydomain=Invalid primary domain in email address
contactDetails.alert.fillnamefield=Please fill in the name field
contactDetails.alert.phonenocontain**********=Phone number should contain **********-
accountmaintenace.alert.selectAccount=Please select Main Account
accountmaintenace.alert.PositiveSweepDays=Invalid Sweep Days, positive Sweep Days is not supported
accountmaintenace.alert.partyId=The Account Party does not refer to an existing record in the Party table. Click OK to continue or Cancel
accountmaintenace.alert.pcmAccount=WARNING: If this account is to be used in the Payment Control module, it will need to be associated with a PCM account group.

#-- Tooltips for Maintenance --
tooltip.sortbyalias=Sort by Alias
tooltip.CurrentCrInterestRates=Current Credit Interest Rate
tooltip.CurrentOverdraftInterestRates=Current Overdraft Interest Rate
tooltip.UpdatedDate=Updated Date
tooltip.AccountID=Account ID
tooltip.ClicktoSelectA/cId=Click here to select account ID

currencyGroup.access=No Currency Group defined for full access
connection.dbError=Cannot change the current DB
connection.confirmDBChange=Are you sure you want to change the current DB?
changePassword.alert.changed=Your password has been changed
account.options=Options
account.openunexpected=Open Unexpected Adjustment
acctbiccode=BIC
acctiban=IBAN
account.accountParty=Account Party
references=References
button.sub=Sub A/C
accountmaintenance.changeaccountdetails.addWindow=Change Account Details - SMART-Predict
accountmaintenance.viewaccountdetails.addWindow=View Account Details - SMART-Predict
accountMonitorNew.startOfDayBalance=Start of day balance
accountMonitorNew.openUnexpectedTotal=Open Unexpected
accountMonitorNew.currencyTextToday=Today
accountMonitorNew.screenSelectorText=Breakdown
acountMonitorNew.AccountBreakDownScreen=Account Breakdown
acountMonitorNew.MovementSummaryScreen=Movement

#-- Balance Maintenance --
tooltip.sortByBalanceSource=Sort by Balance Source
tooltip.sortByBalanceSourceId=Sort by Balance Source ID
balmaintenance.balanceSource=Balance Source
balanceSource=Source
balanceSourceId=Source ID
updated=Updated
queue.authorizePanel=Authorise Panel
queue.refferedPanel=Referred Panel
tooltip.moveButton=Move the balance from Loro/Curr account between the Predicted and Loro/Curr columns
accountmonitorbutton.move=Move
accountmonitor.confirm.move.testMessageY=Move balance to Loro/Curr column?
accountmonitor.confirm.move.testMessageN=Move balance to Predicted column?
confirm.recon=Are you sure you want to reconcile the selected movement?
account.loroToPredicted=Include Loro in Prediction
tooltip.account.loroToPredicted=Include Account Class Loro in Prediction
monitor.dataInsertionJobRunning=Data build in progress, please wait...
sweepCode=Sweep Code
tooltip.enterSweepCode=Enter Sweep Code
tooltip.changeSweepCode=Change Sweep Code
confirm.refreshMainWindow=Menu level changes will be applicable after re-login
alert.closeScreen=Please close the opened screen!
tooltip.account.autoopenunsettled=Automatically hold open unsettled movements at end of day?
tooltip.account.autoopenunexpected=Automatically hold open unexpected movements at end of day?
account.autoopenunsettled=Auto Open Unsettled
account.autoopenunexpected=Auto Open Unexpected
confirm.addMovementsTomatch=The amounts of the selected movements differ. Do you want to continue?
confirm.addMovementsToExistingmatch=The amount(s) of the selected movement(s) differ from movements already present in the match. Do you want to continue?
tooltip.selectLink=Select Link Account
tooltip.selectMain=Select Main Account
book.bookLocation=Book Location
bookCode.bookLocation=Location
tooltip.selectBookLocation=Select Book Location
currency.multiplier=Multiplier
tooltip.selectMultiplier=Select Multiplier
alert.movementsummaryWhileExport=An Error occurred while exporting, Please contact your System Administrator


currency.threshold=Threshold ( millions )
queue.applyCurrencyThreshold=Apply Currency Threshold
mvmt.applyCurrencyThreshold=Apply Currency Threshold
tooltip.applyCurrencyThreshold=Apply Currency Threshold
location.locationId=Location ID
location.locationName=Name
location.mainScreen=Location Maintenance - SMART-Predict
tooltip.enterLocationName=Enter Location Name
tooltip.enterLocationId=Enter Location ID
tooltip.sortLocationId=Sort by Location ID
tooltip.sortLocationName=Sort by Location Name
tooltip.addNewLocation=Add New Location
tooltip.deleteSelectedLocation=Delete selected location
tooltip.changeSelectedLocation=Change selected location
movement.external=External
currencyMonitor.options.title=Monitor Options
ccyMonitorOptions.useCcyMultiplier=Use Currency Multiplier
ccyMonitorOptions.hideWeekends=Hide Weekends
ccyMonitorOptions.hideLoro=Hide Loro
ccyMonitorOptions.defaultDays=Default Days
ccyMonitorOptions.enterDefaultDays=Enter default days
ccyMonitorOptions.usePersonalCurrencyList=Use Personal Currency List
button.option=Options
currencyMonitor.locations=Location
tooltip.location=Location ID
tooltip.option=Change options
currencyMonitor.alert.defDays=Default days should be between 1 and 14
currencyMonitor.alert.dateRange=The data for this date range selection may not be available in the cache and will take time to be calculated next time. \\n Do you want to continue?
currencyMonitor.alert.defDayschanged=changed default days will not be applied \\n until you restart the screen

#-- Personal Currency Maintenance --
personalCurrencyMaintenance.title.mainWindow=Personal Currency List - SMART-Predict
personalCurrency.personalCurrency=Currency
personalCurrency.personalCurrencyName=Name
personalCurrency.priority=Priority
tooltip.personalCurrency=Sort by currency code
tooltip.personalCurrencyName=Sort by currency name
tooltip.priority=Sort by priority
tooltip.AddNewPersonalCurrency=Add new personal currency
tooltip.ChangePersonalCurrency=Change selected personal currency
tooltip.RemovePersonalCurrency=Remove selected personal currency
tooltip.RemovePersonalCurrency=Remove selected personal currency
tooltip.enterPriority=Enter Priority
role.perCurrrency.currency=Currency
role.perCurrrency.priority=Priority
personalCurrency.childscreenName=Personal Currency Definition - SMART-Predict
confirm.remove=Are you sure you want to remove?
personalCurrency.alert.priority=Priority order should be between 1 and 99

locationaccess.location=Location
locationaccess.locationName=Name
locationaccess.access=Access
tooltip.sortByLocation=Sort by Location
tooltip.sortByName=Sort by Name
role.restrictLocations=Restrict locations
tooltip.restrictLocations=Open Restrict Locations window
role.Locations=Location
locationAccess.title.mainWindow=Change Location Access
addlocationmaintenanace.title.mainWindow=Add Location definition
changelocationmaintenanace.title.mainWindow=Change Location definition
locationAccess.title.addWindow=Add Location Access - SMART-Predict
locationAccess.title.changeWindow=Change Location Access - SMART-Predict
locationAccess.title.viewWindow=View Location Access - SMART-Predict

#-- Currency Maintenance --
tooltip.multiplier=Sort by multiplier
tooltip.threshold=Sort by threshold
currency.thresholdMainScreen=Threshold (M)
entity.field=Field
entity.nochange=No Change
entity.manualonly=Manual Only
entity.allmovement=All Movements
entity.editablefield=Editable Field
defineEditableFields.viewScreen=Editable Fields
defineEditableFields.addScreen=Add Editable Fields
defineEditableFields.changeScreen=Change Editable Fields
entity.defineEditableFields=Define
tooltip.defineEditableFields=Define Editable Fields
account.allpreadviceentity=Allow Pre-Advice Entry
tooltip.account.allpreadviceentity=Allow Pre-Advice Entry?
bookMonitor.location=Location
tooltip.selectLocationId=Select location ID
tooltip.sortLocationId=Sort by location ID

#-- Change Movement Display --
mvmDisplay.title.changewindow=Change Movement - SMART-Predict
mvmDisplay.tooltip.enterValueDate=Enter Value date
mvmDisplay.tooltip.currencyCode=Currency code
mvmDisplay.tooltip.enterAccountId=Enter Account
mvmDisplay.tooltip.ref1=Movement reference 1
mvmDisplay.tooltip.ref2=Movement reference 2
mvmDisplay.tooltip.ref3=Movement reference 4
mvmDisplay.tooltip.ref4=Movement extra text
mvmDisplay.tooltip.custText=Custodian text

#-- Default Account Maintenance --
defaultaccountmaintenance.title.mainWindow=Default Account Maintenance - SMART-Predict
defaultaccountmaintenance.title.addWindow=Add Default Account - SMART-Predict
defaultaccountmaintenance.title.changeWindow=Change Default Account - SMART-Predict
defaultaccountmaintenance.currencyCode=Ccy
defaultaccountmaintenance.BIC=BIC/Network
defaultaccountmaintenance.accountId=Account
defaultaccountmaintenance.accountName=Name
defaultaccountmaintenance.alert.xrefCode=BIC/Network is empty
defaultaccountmaintenance.alert.currencyCode=Currency Code cannot be set to 'All'
defaultaccountmaintenance.alert.account=Account field is empty


fomat.cdExtInt=Ext via Intermediary
fomat.ddExtInt=Debit Ext via intermediary

intermediary=Intermediary
sweepInter.currency=Currency
sweepInter.curName=Name
sweepInter.targetBIC=Target BIC
sweepInter.InterBIC=Intermediary BIC
sweepInter.mainScreen=Sweep Intermediary Maintenance - SMART-Predict
tooltip.currency=Sort by Currency code
tooltip.curName=Sort by Currency Name
tooltip.targetBIC=Sort by Target BIC
tooltip.InterBIC=Sort by Intermediary BIC
tooltip.addNewSweepIntermediary=Add New Sweep Intermediary
tooltip.changeNewSweepIntermediary=Change Sweep Intermediary
tooltip.deleteSelectedSweepIntermediary=Delete selected Sweep Intermediary
sweepInter.childScreen=Add Sweep Intermediary - SMART-Predict
sweepInter.changeChildScreen=Change Sweep Intermediary - SMART-Predict
sweepIntermediaries.currencyCode=Currency Code
sweepIntermediaries.currencyName=Currency Name
sweepIntermediaries.targetBic=Target BIC
tooltip.enterTargetBic=Enter target BIC
sweepIntermediaries.intermediary=Intermediary BIC
tooltip.enterIntermediary=Enter intermediary BIC
sweepIntermediaries.accountId=Account
tooltip.enterAccount=Enter Account
tooltip.sortByAccount=Sort by Account

entity.predict.thresholdParam.sweepCutoffLeadTime=Sweep cut-off lead time
minutes=minutes
sweeppriorcutoff.accountClass=Account Class
sweeppriorcutoff.ccy=Ccy
sweeppriorcutoff.cutOff=Cut-off
sweeppriorcutoff.valueDate=Value Date
sweeppriorcutoff.leadTime=Cut-Off Lead Time (mins)
sweeppriorcutoff.time=Time
tooltip.minutesAfterCutOff=Minutes After Cut-Off
tooltip.extendDisplayTo=Change Extend Display to
sweepPrior.title.window=Sweeps Prior To Cut-Off - SMART-Predict
login.newUSer.continue=New User! Please change your password.
login.newPage.generated=The server may have been rebooted, for security reasons, please login again

errors.logon.verify.exception=An error occurred on server side, please see logs, or try to login again
errors.logon.verify.role=Your user is missing a Role, Please contact your System Administrator.
errors.logon.verify.smartUserId=Invalid login: Mandatory data is missing. Please contact your System Administrator.
errors.logon.verify.smartRoleId=Invalid login: No linked Role was found in the application, please contact your Administrator

sweepPrior.extendDisplay=Extend Display to
sweepPrior.minutesAfterCoutOff=Minutes After Cut-Off

#-- Currency Alias Maintenance --
currencyAliasChild.alert.IdasAll=Currency Alias 'All' not permitted
currencyalias.alias=Alias
currencyalias.currency=Currency
currencyalias.name=Name
currencyalias.addScreen=Currency Alias Add - SMART-Predict
aliasTooltip.sortalias=Sort by currency alias
aliasTooltip.sortcurrency=Sort by currency code
aliasTooltip.sortname=Sort by currency name
aliasTooltip.CurrencyAliasAdd=Add currency alias
aliasTooltip.CurrencyName=Delete currency alias
currencyAliasMaintenance.title.mainWindow=Currency Alias Maintenance - SMART-Predict
tooltip.ChangeSelectedCurrencyAlias=Change selected currency alias
tooltip.DeleteSelectedCurrencyAlias=Delete selected currency alias
tooltip.AddNewCurrencyAlias=Add new currency alias
tooltip.AliasName=Enter currency alias
tooltip.AliasIdentifier=Select currency code
#-- Metagroup Monitor --
metagroupMonitor.metagroup=Metagroup
metagroupMonitor.level=Level
BookgroupMonitor.title.window=Book Group Monitor - SMART-Predict
tooltip.sortMetagroupId=Sort by Metagroup ID
tooltip.sortMetagroupName=Sort by Metagroup Name
tooltip.sortLevel=Sort by Level

#-- Group Monitor --
groupMonitor.title.window=Group Monitor - SMART-Predict
groupMonitor.currency=Currency
groupMonitor.location=Location
groupMonitor.date=Date
groupMonitor.group=Group
groupMonitor.name=Name
groupMonitor.level=Level
groupMonitor.balance=Balance
groupMonitor.today1=Today + 1
groupMonitor.today=Today
groupMonitor.today1=Today+1
groupMonitor.today2=Today+2
groupMonitor.selected=Selected
groupMonitor.total=Total
groupMonitor.metagroup=Metagroup
groupCode.entity=Entity
tooltip.selectMetagroupId=Select metagroup ID
tooltip.sortGroupCode=Sort by group code
tooltip.sortGroupName=Sort by group name
tooltip.sortLevel=Sort by level
tooltip.sortTotal=Sort by total
groupMonitor.dateMMDDYY=MM/dd/yyyy

#-- Book Monitor --
bookMonitor.group=Group
tooltip.selectGroupId=Select group ID

#-- Currency Monitor --
tooltip.viewGroupMonitor=Select Group to view Group Monitor
tooltip.viewMetagroupMonitor=Select Metagroup to view Metagroup Monitor
currMonitor.group=Group
currMonitor.metagroup=Metagroup
role.inputInterruption=Interface Interruption
role.notification.inputNotification=Notifications
role.allEntityOption=Workflow / Currency Monitor: 'All' entity option
role.advancedUser=Show advanced user configuration
role.maintainAnyIlmGroup=Maintain any ILM account group
role.maintainAnyPCFeature=Override four eyes principle
role.maintainAnyIlmScenario=Maintain any ILM scenario
tooltip.maintainAnyIlmScenario=Allow user to change any defined scenarios and make scenarios public
tooltip.maintainAnyIlmGroup=Allow user to change any defined ILM account group and make account groups public
tooltip.maintainAnyPCFeature=Allow user to change any Payment Control Action that required 4 Eyes
role.maintainSchedulerReportHist=Allow admin access to scheduled report history
tooltip.maintainSchedulerReportHist=Allow the user to delete reports and re-send Emails from scheduled report history
acctclass=Account Class
tooltip.selectAccountClass=Select account class
hidezerobalances=Hide Zero Balances
tooltip.hideZeroBalances=Hide Zero Balances
acc.SOD=SOD
acc.Zero=Zero
account.ExternalBalance=External Balance
tooltip.includeMvmInDataExternal=Include movement in data external
tooltip.excMvmInDataExternal=Exclude movement in data external
tooltip.acc.SOD=Start of Day
tooltip.acc.Zero=Zero
queue.noIncludedMovementMatches=No-Included Movement Matches
tooltip.noIncludedMovementMatches=No-Included Movement Matches
label.actual=Actual

#-- Input Exceptions --
inputexceptions.header.interface=Interface
inputexceptions.header.awaiting=Awaiting
inputexceptions.header.accepted=Accepted
inputexceptions.header.rejected=Rejected
inputexceptions.header.submitted=Submitted
inputexceptions.header.suppressed=Suppressed
inputexceptions.header.received=Received
inputexceptions.header.repair=Repair
inputexceptions.messages.header.prid=PR ID
inputexceptions.messages.header.messageid=Message ID
inputexceptions.messages.header.messageType=Interface ID
inputexceptions.messages.header.accountId=Account
inputexceptions.messages.header.statusnotes=Status Notes
inputexceptions.messages.header.inputdate=Input Date
inputexceptions.messages.header.valuedate=Value Date
inputexceptions.messages.header.currency=Currency
inputexceptions.messages.header.amount=Amount
inputexceptions.messages.header.sign=Sign
inputexceptions.messages.header.reference1=Reference 1
inputexceptions.messages.header.reference=Reference
entity.smallMovementRetain=Small Movements
entity.largeSmallMovementThresholdAsString=Large/Small Movements
tooltip.entity.smallMovementRetain=Small Movements Retention
tooltip.entity.largeSmallMovementThresholdAsString=Large/Small Movements Threshold
messagefieldadd.alert.isStartPosExisting=Record already existing
tooltip.InsertNewField=Insert new message field
messageFormats.formatType.tagged.variable=Multi-Line - Variable Fields
login.password.modified=Password Expired today
login.password.noaccess.to.facility=ERROR: Password change is required but user's role does not allow access to the facility
role.password.noaccess.to.facility=WARNING: This role does not grant access to the password change facility
role.noaccess.higherlevel=WARNING:A menu option is granted but access to higher levels in the tree has not been granted, thus preventing access to the desired option
role.button.showList=Show List


#-- Interface Settings (input configuration) --
inputconfig.header.beginTime=Alerts Start
inputconfig.header.endTime=Alerts End
inputconfig.header.threshold=Alerts Threshold (min)
inputconfig.header.interface=Interface ID
inputconfig.header.inputdir=Input Directory
inputconfig.header.outputdir=Recovery Directory
inputconfig.header.spinterval=Proc Cycle
inputconfig.header.engineactive=Active
inputconfig.header.mqactive=Read MQ
inputconfig.title.window=Interface Setting - SMART-Predict
inputconfig.tooltip.change=Change Input Configuration
inputconfig.header.emaillogs=Email Logs
inputconfig.header.emaillogsto=Email Logs To
inputconfig.header.class=Class
inputconfig.header.property=Property
inputconfig.header.value=Value
inputconfig.header.enabled=Enabled




#sweepSearchList
sweepSearchList.valueDate = Value
sweepSearchList.currencyCode = Ccy
sweepSearchList.currentAmt = Original Amount 
sweepSearchList.NewAmt = New Amount
sweepSearchList.entityCr = Entity CR
sweepSearchList.accountIdCr = CR Account
sweepSearchList.Name = Name
sweepSearchList.entityDr = Entity DR
sweepSearchList.accountIdDr = DR Account
sweepSearchList.Name = Name
sweepSearchList.sweepId = Sweep
sweepSearchList.crdIntMsg = CR INT MSG
sweepSearchList.crdExtMsg = CR EXT MSG
sweepSearchList.drIntMsg = DR INT MSG
sweepSearchList.drExtMsg = DR EXT MSG
sweepSearchList.sweepType = Type
sweepSearchList.sweepUser = User
sweepSearchList.status1 = Status

access_restrictions= Access restrictions : User not allowed


#-- Start For Interface Monitor Screen --
interfacemonitor.header.interface=Interface
interfacemonitor.header.filemanagers=File Managers
interfacemonitor.header.commits=Commits/hr
interfacemonitor.header.last=Last
interfacemonitor.header.proc_running=Proc running
interfacemonitor.header.proc_result=Proc result
interfacemonitor.header.health=Health
interfacemonitor.header.active=Active
interfacemonitor.header.busy=Busy
interfacemonitor.sub.threadname=Name
interfacemonitor.sub.threadhb=Heartbeat
interfacemonitor.details.text.commandsocket=Command Socket Listener
interfacemonitor.details.text.interfacemanager=Interface Manager
interfacemonitor.details.text.directorymanager=Directory Manager
interfacemonitor.details.text.storedprocedure=Stored Procedure
interfacemonitor.details.text.storedprocrunning=Running
interfacemonitor.details.text.storedproclastresult=Last Result
interfacemonitor.details.text.storedproclaststarttime=Last Start Time
interfacemonitor.details.text.storedproclastexectime=Last Execution Time
interfacemonitor.details.text.generaldetails=General Details
interfacemonitor.details.text.storedprocdetails=Stored Procedure Details
interfacemonitor.details.text.interfacedetails=Interface Details
interfacemonitor.date=Date

opportunityCostReport.mainScreen=Opportunity Cost Report - SMART-Predict
unsettledMovements.mainScreen=Unsettled Movements Report - SMART-Predict
excludedMovements.mainScreen=Excluded Movements Report -SMART-Predict
reports.threshold=Threshold
reports.thresholdAmount=Amount Threshold
reports.thresholdAmountType=Amount Threshold Type
reports.thresholdTime=Time Threshold
reports.amountThresholdTypeAbs=Absolute
report.amountThresholdTypeAbs=Absolute
amountThresholdTypeAbs=Domestic Currency
reports.submit=Report
reports.process=Process
reports.pdf=PDF
reports.excel=Excel
reports.csv=CSV
reports.outputFormat=Output Format
tooltip.reportHostId=User Host ID
tooltip.reportEntity=Select the entity
tooltip.reportDate=Enter Report date
tooltip.calendarreportdate=Select Report date
tooltip.selectthreshold=Select Currency Threshold
tooltip.selectReference=Select Like Condition for Reference
tooltip.submitbutton=Generate the report
tooltip.processButton=Process the report
tooltip.cancelbutton=Cancel changes and exit
tooltip.currency=Select the Currency
opportunityCostReport.currencyLabel=Currency
tooltip.generateReportAll=Generate report for all users
tooltip.generateUserReport=Generate report
tooltip.generateReportforSingleRole=Generate report for a Selected role
tooltip.generateReportforAllRoles=Generate report for all roles
movementsearch.like=Like
movementsearch.ref1=Ref1
movementsearch.ref2=Ref2
movementsearch.ref3=Ref3
movementsearch.extra=Extra
movementsearch.openMovements=Include open
tooltip.likeFlag=When Checked, search for references containing the specified value. When unchecked, search for exact value.
tooltip.refFlag=Check to consider the reference in the search
tooltip.openMovementFlag=Include open movements
tooltip.sortbyupdate_date=Sort by update date
movement.update_date=Update Date
entity.balance=Balance
entity.balancelog=Balance Log
tooltip.entity.balance=Account balance retention period
tooltip.entity.balancelog=Account balance log retention period
interestCharges.mainScreen=Interest Charge per Account Report - SMART-Predict
interestCharges.entity=Entity
interestCharges.currency=Currency
interestCharges.account=Account
interestCharges.report=Report
tooltip.reportButton=Generate the report
alert.interestCharges.toDate=To Date should be greater than or equal to From Date
alert.interestCharges.fromDate=From Date should be less than or equal to To Date
alert.interestCharges.dateRange=INVALID: The selected date range must not exceed 90 days
changeBalance.acctId=Account ID
changeBalance.updateUser=Update User
changeBalance.userId=User ID
changeBalance.updateDate=Update Date
changeBalance.legend.acctBalance=Account Balance Details
changeBalance.legend.suppSods=Supplied SODs
changeBalance.internal=Internal
changeBalance.external=External
changeBalance.preditInternal=Predicted(internal)
changeBalance.preditExternal=Predicted(external)
changeBalance.legend.backValAdj=Back Value Adjustments
changeBalance.legend.workingBal=Working SOD Balances
changeBalance.lastMov=Last Movement
balmaintenance.accountId=Account ID
tooltip.sortByAccountId=Sort by Account ID
balmaintenance.SODType=SOD Type
balmaintenance.forecastSOD=Forecast SOD
tooltip.sortByForecast=Sort by Forecast SOD
balmaintenance.forecastSODType=Forecast SOD Type
balmaintenance.externalSODType=External SOD Type
balmaintenance.externalSOD=External SOD
tooltip.sortBySOD=Sort by Forecast SOD
balmaintenance.externalWorkingSod=External SOD
tooltip.sortByExternalWorking=Sort by External SOD Type
tooltip.internalSOD=Enter internal SOD
tooltip.externalSOD=Enter external SOD
tooltip.allEntityOption=Implements Workflow / Currency Monitor: 'All' entity option
tooltip.selectRestrictLocation=Select a Restrict Location
tooltip.advancedUser=Implements Show advanced user configuration
logBalance.dateandtime=Date/Time
tooltip.sortByDateandTime=Sort by Date/Time
logBalance.SODChange=SOD Change
tooltip.sortBySODChange=Sort by SOD Change
logBalance.workingSOD=Working SOD
tooltip.sortByWorkingSOD=Sort by Working SOD
logBalance.bvAdjustChange=BV Adjust Change
tooltip.sortByBvAdjustChange=Sort by BV Adjust Change
logBalance.bvAdj=BV Adj
tooltip.sortByBvAdj=Sort by BV Adjust
logBalance.id=ID
tooltip.sortById=Sort by ID
logBalance.forecast=Forecast
logBalance.external=External

logBalance.suppliedSODChange=Supplied SOD Change
logBalance.suppliedSODSource=Supplied SOD Source
logBalance.suppliedSOD=Supplied SOD
logBalance.workingSODType=Working SOD Type
logBalance.workingSODChange=Working SOD Change
logBalance.suppliedInternal=Internal
logBalance.suppliedExterna=External



changeBalance.source=(Source)
tooltip.sortByInputTime=Sort by update Date
acctmaintenance.forecast=Forecast
acctmaintenance.predict=Prediction
acctmaintenance.external=External
acctmaintenance.primary=Primary Source
acctmaintenance.secondary=Secondary Source
acctmaintenance.backvalue=Back-Value Adjustments
acctmaintenance.forecastsod=Prediction SOD
acctmaintenance.externalsod=External SOD
acctmaintenance.useinternal=Use Internal
acctmaintenance.useexternal=Use External
acctmaintenance.usepredicted=Use Predicted
acctmaintenance.usenone=None
tooltip.addPrimaryForecast=Select Primary Forecast
tooltip.addPrimaryExternal=Select Primary External
tooltip.addSecondaryForecast=Select Secondary Forecast
tooltip.addSecondaryExternal=Select Secondary External
tooltip.changePrimaryForecast=Change Primary Forecast
tooltip.changePrimaryExternal=Change Primary External
tooltip.changeSecondaryForecast=Change Secondary Forecast
tooltip.changeSecondaryExternal=Change Secondary External
tooltip.viewPrimaryForecast=View Primary Forecast
tooltip.viewPrimaryExternal=View Primary External
tooltip.viewSecondaryForecast=View Secondary Forecast
tooltip.viewSecondaryExternal=View Secondary External
scheduler.recordExists=Record already exists
tooltip.amountFrom=Enter amount from
tooltip.amountTo=Enter amount to

balmaintenance.effectiveForecastSOD=Effective Forecast SOD
balmaintenance.effectiveExternalSOD=Effective External SOD

role.roleAccounts=Accounts
tooltip.roleAccounts=Maintain account based controls
role.roleAccountsLabel=Apply account access controls
tooltip.roleAccountsCheck=Implement account-based controls on manual input, matching and sweeping
tooltip.roleAccountsButton=Maintain account based controls
role.accountaccess.title=Account Access Control
role.accountaccess.viewTitle=View Account Access Control
role.accountaccess.changeTitle=Change Account Access Control
role.accountaccess.addTitle=Add Account Access Control
role.applytoalldisacct=Apply to all displayed accounts:
role.roleInput=Input
role.roleMatching=Matching
role.roleSweeping=Sweeping
tooltip.sortByInput=Input
tooltip.sortByMatching=Matching
tooltip.sorBySweeping=Sweeping
tooltip.selectInput=Select Input
tooltip.selectMatching=Select Matching
tooltip.selectSweeping=Select Sweeping
tooltip.selectAllInput=Select All Input
tooltip.selectAllMatching=Select All Matching
tooltip.selectAllSweeping=Select All Sweeping
tooltip.cancel=Cancel
tooltip.sortAcClass=Sort by account class

message.alert.acctAccessInput=Access controls prevent you from inputting movements on this account

message.alert.accountAccess=Account access settings will be saved subject to currency group access settings
message.alert.noRolesAssign=You must have account access controls enabled for at least one role to use this facility

interfaceNotificationAlert=[Predict] Error when attempting to contact the SmartInput engine
interfaceNotificationAlertPCM=[PCM] Error when attempting to contact the SmartInput engine

centralMonitor.title.window=Central Bank Monitor - SMART-Predict
centralMonitor.closingBalances=Closing Balances
centralMonitor.currencylimit=Currency Limit
tooltip.currencyLimit=Enter the currency limit
centralMonitor.alert.defDays=Default days should be between 2 and 14

centralMonitorOptions.fontSizeSmall=Small
centralMonitorOptions.fontSizeNormal=Normal
tooltip.centralMonitorOptions.selectFontSizeSmall=Select Small Font Size
tooltip.centralMonitorOptions.selectFontSizeNormal=Select Normal Font Size
centralMonitorOptions.fontSize=Font Size

label.predictedbal=Predicted Balance
label.unsettled=Unsettled
label.fwdmovements=Forward Movements
label.unexpected=Unexpected
label.externalbal=External Balance
label.cumulativeext=Cumulative
label.crrlimit=CRR Limit
label.limitexcess=Limit Excess
label.corporateentries=Corporate Entries
label.whatifanalysis=What If Analysis
label.adjexternalbalance=Adj. External Balance
label.adjcumulativebalance=Adj. Cumulative
label.adjlimitexcess=Limit Excess
alert.startMonitor=Please define Central Bank parameters in Entity Maintenance before starting this monitor.
centralMonitor.fromDate=Week Commencing
limit.from=Limit From
limit.to=To
tooltip.fromDateDDMMYY=Select From date (DD/MM/YYYY)
tooltip.toDateDDMMYY=Select To date (DD/MM/YYYY)
tooltip.fromDateMMDDYY=Select From date (MM/DD/YYYY)
tooltip.toDateMMDDYY=Select To date (MM/DD/YYYY)

tooltip.showdays=Number of days to show
text.showdays=show
text.day=day
text.days=days

entity.predict.centralBank=Central Bank
entity.accountId=Account ID
entity.dateFrom=Date From
entity.dateTo=Date To
entity.crrLimit=CRR Limit
entity.startDay=Start Day
tooltip.entity.accountId=Central Bank Account ID
tooltip.entity.dateFrom=Date From
tooltip.entity.dateTo=Date To
tooltip.entity.crrLimit=CRR Limit
tooltip.entity.startDay=Start Day


movementsearch.open=Open
tooltip.open=Select Open Status
acctbreakdown.alert.datechange=Server date changed at midnight. Your date selection will be updated.
accountaccess.closeConfirm=Changes have been made. Are you sure you want to close without saving?
entity.crrlimit.zerovalue=CRR Limit should be greater than zero
preadvice.alert.notfound=Pre-Advice not found in file
preAdvice.alert.noAccess=Invalid: your role does not provide access to this pre-advice
corporateAccount.title.window=Corporate Entries - SMART-Predict
corporate.name=Corporate Name
corporate.amount=Amount
preadvice.movetoauthorise=There will be change in balance as this movement is moved to the authorize queue

# Correspondent Account --
correspondentaccountmaintenance.title.mainWindow=Correspondent Account Alias Maintenance - SMART-Predict
correspondentaccountmaintenance.title.addWindow=Add Correspondent Account Alias - SMART-Predict
correspondentaccountmaintenance.title.changeWindow=Change Correspondent Account Alias - SMART-Predict
correspondentaccountmaintenance.currencyCode=Ccy
correspondentaccountmaintenance.messageType=Message Type
correspondentaccountmaintenance.correspondentAccId=Correspondent Acc ID
correspondentaccountmaintenance.accountId=Account ID
correspondentaccountmaintenance.accountName=Account Name
correspondentaccountmaintenance.add.entityId=Entity
correspondentaccountmaintenance.add.currencyCode=Currency
correspondentaccountmaintenance.add.messageType=Message Type
correspondentaccountmaintenance.add.correspondentAccID=Correspondent Acc ID
correspondentaccountmaintenance.add.accountID=Account ID*
correspondentaccountmaintenance.predefinedMessageType=Predefined
correspondentaccountmaintenance.manualMessageType=Manual
tooltip.manualMessageType=Message Type (Search texts containing % as wild card are supported)
tooltip.messageType=Message Type
tooltip.enterMessageType=Enter Message Type
tooltip.selectMessageType=Select Message Type
tooltip.selectAccountId=Select Account ID
tooltip.enterCorrespondentAcctId=Enter Correspondent Account ID
tooltip.sortMessageType=Sort by Message Type
tooltip.sortCorrespondentAccId=Sort by Correspondent Acc ID
tooltip.sortAccountId=Sort by Account ID
tooltip.sortAccountName=Sort by Account Name
tooltip.addNewCorrespondentAccount=Add new Correspondent Account
tooltip.changeSelCorrespondentAcct=Change selected Correspondent Account
tooltip.deleteSelCorrespondentAcct=Delete selected Correspondent Account
tooltip.messageTypeSearchCheckbox=Select Check box to enter message type Search String
correspondentaccountmaintenance.alert.messageType=Message Type is empty
correspondentaccountmaintenance.alert.currencyCode=Currency Code cannot be set to 'All'
correspondentaccountmaintenance.alert.corresAccId=Correspondent Acc ID is empty
correspondentaccountmaintenance.alert.accountId=Account ID field is empty
alert.weekend=Selected Date is Weekend
intradayBalances.accountScreenForGraph=Intraday Balance Graph
screenheader.alertsummary=There are a number of alerts to be actioned
title.alertsummary=Alert Summary Display

# Interface Rules --
interfacerulesmaintenance.title.mainWindow=Interface Rule Maintenance - SMART-Predict
interfacerulesmaintenance.messageType=Message Type
interfacerulesmaintenance.predefinedMessageType=Predefined
interfacerulesmaintenance.manualMessageType=Manual
interfacerulesmaintenance.ruleId=Rule ID
interfacerulesmaintenance.ruleKey=Rule Key
interfacerulesmaintenance.ruleValue=Rule Value
interfacerulesmaintenance.add.messageType=Message Type
interfacerulesmaintenance.add.ruleID=Rule ID
interfacerulesmaintenance.add.ruleKey=Rule Key
interfacerulesmaintenance.add.ruleValue=Rule Value
interfacerulesmaintenance.partialRuleId=Partial Rule ID
tooltip.partialRuleId=Check Partial ID check box to search on Partial Rule ID
tooltip.enterRuleId=Enter Rule ID
tooltip.addNewInterfaceRule=Add new Interface Rule
tooltip.changeSelInterfaceRule=Change selected Interface Rule
tooltip.deleteSelInterfaceRule=Delete selected Interface Rule
# interface Rules
tooltip.Predefined=Select Predefined
tooltip.Manual=Select Manual
tooltip.sortRuleId=Sort by Rule ID
tooltip.sortRuleKey=Sort by Rule Key
tooltip.sortRuleValue=Sort by Rule Value
tooltip.enterInterfaceRuleId=Enter Interface Rule ID
tooltip.enterInterfaceRuleKey=Enter Interface Rule Key
tooltip.enterInterfaceRuleValue=Enter Interface Rule Value
interfacerulesmaintenance.title.addWindow=Add Interface Rules Maintenance - SMART-Predict
interfacerulesmaintenance.title.changeWindow=Change Interface Rules Maintenance - SMART-Predict
interfacerulesmaintenance.alert.messageType=Message Type is empty
interfacerulesmaintenance.alert.ruleId=Rule ID is empty
interfacerulesmaintenance.alert.ruleKey=Rule Key is empty
alert.mvmQSelectionStMatchIdChange=The status of the match has changed, the screen will be refreshed
acctmain.balances=Balance Calculation
acctmain.futurebalances=Future Balances
acctmain.usetodaysod=Use Today's SOD
acctmain.usenearestsod=Use Nearest SOD
acctmain.usezero=Use Zero
acctmain.ilmParameters=ILM Parameters
acctmain.isIlmContributer=Is ILM Liquidity Contributor
acctmain.isCentralBank=Is Central Bank Member Account
acctmain.isCustomerAccount=Is Customer Account
workflowmonitor.excluded=Excluded
workflowmonitor.outstandings=Outstandings
workflowmonitor.included=Included
workflowmonitor.movements=Movements
workflowmonitor.total=Total
ExchangeRate.confirm.IntoNew=The Exchange Rate setting is designed to be set once and not changed. All current exchange rates are stored in the originally specified format. If you do want to change this setting, please contact your support team. Continue with change (not recommended)?
errors.sweep.account.changes=Account sweep configuration is missing required parameters

#-- Interface Rules --
interfacerules.alert.ruleValueLengthValidation=Please enter 250 characters only
interfacerules.alert.ruleIdLengthValidation=Please enter 100 characters only for Rule ID
interfacerules.alert.ruleKeyLengthValidation=Please enter 100 characters only for Rule Key

interfacerulesmaintenance.alert.ruleValidation=Invalid characters entered. Only alphanumeric characters and the symbols (.,%&<>/|\\\\-_+=^;:[]*()?#) are accepted.

#-- Party Maintenance screen tool tip --
party.alert.pagination=Please enter a valid page no
tooltip.enterPageNo=Enter page no
tooltip.addnewpartyAlias=Add new party alias
tooltip.sortPartyAlias=Sort by party alias
tooltip.sortbypartyType=Sort by type

party.alert.criteriaNotMatch=Search Criteria Does not Match

#-- Input Exceptions --
inputexceptions.label.message_status=Message Status:
inputexceptions.label.message_type=Interface:
inputException.reprocess=Reprocess
inputexceptions.tooltip.button_rep=Reprocess the selected Message
inputException.reject=Reject
inputexceptions.tooltip.button_rej=Move messages to rejected queue
inputException.suppress=Suppress
inputexceptions.tooltip.button_supp=Move messages to suppressed queue
inputexceptions.label.autoFormatXML=Auto-format XML
inputexceptions.tooltip.autoFormatXML=When appropriate, display formatted XML messages. When unchecked, show raw message

#-- Cross Reference --
button.crossReference=XRefs
tooltip.viewCrossReference=View Cross Reference
crossReference.movement=Movement
crossReference.sourceOfReference=Source Of Ref
crossReference.businessSource=Business Source
crossReference.crossReference=Cross Reference
crossReference.updateDate=Update Date
crossReferenceDisplay.title.window=Cross Reference Display - SMART-Predict
moveDisplay.reference=References

#-- Account Maintenance Screen --
Account.generalTab=General
Account.monitorTab=Balances
Account.sweepTab=Sweeping
SweepFromBalance=Sweep From Balance
tooltip.selectSweepFromBalance=Select Sweep from Balance
accountmaintenance.intradaySweeping=Intra-Day Sweeping
accountmaintenance.EODSweeping=Sweep Schedule
subacctim=Use Timing of Only This Account
subacctim.tooltip=When checked, only determine whether sweeping is possible using parameters of this account. Otherwise, also use parameters on other account involved in the sweep
format.default=Default
format.accountSpecific=Account Specific
tooltip.accountSpecific=Account Specific
format.Credit=Credit
format.Debit=Debit
format.Acc.Attribute=Acc. Attribute
format.Rule=Rule
label.format.Int=Internal
label.format.Ext=External
accountmaintenance.Format=Formats
account.sweepfrmbal=Choose to calculate sweep on Predicted or external balance. External will cause sweep movement to be count towards external balance.
accountmonitor.erBalance=External Balance
sweep.swpFrm=Sweep From
tooltip.Usesubacc=When selected, auto-sweeping between sub and main account will only use sweep time and cut-off from sub-account
account.sweep.scheduleused.title=Account Used in other sweep schedules
tooltip.earliestSweepTime=Earliest time at which sweeping can take place. Used in auto sweeping when "Use Timings of Only This Account" is not checked.
label.accountschedulesweepdetails.title.window=Account Schedule Sweep Details

#-- Sweep Detail Screen --
sweepDetail.exalignToTarget=Re-aligned External Balance
tooltip.aggAcct=Aggregate Account
tooltip.loroToPredicted=Include Loro in Prediction
tooltip.autoOpenUnsettled=Auto Open Unsettled
tooltip.autoOpenUnexpected=Auto Open Unexpected
tooltip.allPreAdviceEntity=Allow Pre Advice Entry

#-- Variables related to Entity Monitor Options --
label.entityMonitor.title.window=Entity Monitor - SMART-Predict
label.entityMonitor.entityOffset=Today (Entity)
tooltip.entityMonitor.entityOffset=Use Entity Offset Time for each entity 

label.entityMonitorOptions.title.window=Monitor Options
label.entityMonitorOptions.usePersonalEntityList=Use Personal Entity List
tooltip.entityMonitorOptions.usePersonalEntityList=Personal Entity List
label.entityMonitorOptions.hideWeekends=Hide Weekends
tooltip.entityMonitorOptions.hideWeekends=Hide Weekends
label.entityMonitorOptions.autoSizeColumns=Auto Size Columns
tooltip.entityMonitorOptions.autoSizeColumns=Auto Size Columns
label.entityMonitorOptions.reportingCcy=Reporting Currency
tooltip.entityMonitorOptions.reportingCcy=Reporting Currency
label.entityMonitorOptions.useCurrencyMultiplier=Use Currency Multiplier
tooltip.entityMonitorOptions.useCurrencyMultiplier=Currency Multiplier
label.entityMonitorOptions.defaultDays=Default Days
tooltip.entityMonitorOptions.defaultDays=Default Days
label.entityMonitorOptions.usePersonalCcyList=Use Personal Currency List
tooltip.entityMonitorOptions.usePersonalCcyList=Personal Currency List
label.entityMonitorOptions.save=Save
tooltip.entityMonitorOptions.save=Save changes and Exit
label.entityMonitorOptions.cancel=Cancel
tooltip.entityMonitorOptions.cancel=Cancel Changes and Exit
label.entityMonitorOptions.entity=Entity
tooltip.entityMonitorOptions.entity=Click to open Personal Entity List screen
label.entityMonitorOptions.currency=Ccy
tooltip.entityMonitorOptions.currency=Click to open Personal Currency List screen

#-- Variables related to Personal Entity List --
label.personalEntityList.title.window=Personal Entity List - SMART-Predict
label.personalEntityList.entityId=Entity
label.personalEntityList.entityName=Name
label.personalEntityList.display=Display
label.personalEntityList.defaultDays=Display Days
label.personalEntityList.priorityOrder=Order of Display
label.personalEntityList.button.ok=OK
tooltip.personalEntityList.button.ok=Save changes and Exit
label.personalEntityList.button.cancel=Cancel
tooltip.personalEntityList.button.cancel=Cancel changes and Exit
label.personalEntityList.title.addsum=Add Sum Entity
label.personalEntityList.button.addsum=Add Sum
tooltip.personalEntityList.button.addsum=Add Sum Entity
label.personalEntityList.button.modifysum=Change
tooltip.personalEntityList.button.modifysum=Change Sum Entity
label.personalEntityList.button.deletesum=Delete
tooltip.personalEntityList.button.deletesum=Delete Sum Entity
alert.personalEntityList.deletesum=Are you sure to delete sum Entity?
alert.personalEntityList.entityExists=Sum entity Already exists
alert.personalEntityList.sumEntityNotSelected=Please, select at least one entity to be summed before saving
label.personalEntityList.sumEntityName=Sum Entity *


#-- Variables related to Personal Currency List --
label.personalCurrencyList.title.window=Personal Currency List - SMART-Predict
label.personalCurrencyList.currency=Currency
label.personalCurrencyList.ccyName=Name
label.personalCurrencyList.priorityOrder=Order of Display
label.personalCurrencyList.entity=Entity
label.personalCurrencyList.button.ok=OK
tooltip.personalCurrencyList.button.ok=Save changes List and Exit
label.personalCurrencyList.button.cancel=Cancel
tooptip.personalCurrencyList.button.cancel=Cancel changes and Exit
label.personalCurrencyList.applyToAllCurrency=Apply to All Currencies:
tooltip.entityMonitor.date=Enter/Select date

#-- Variables related to Entity Monitor --
label.entityMonitor.currency.group=Currency Group
tooltip.entityMonitor.selectCcyGroup=Select Currency Group
label.entityMonitor.date=Date
label.entityMonitor.breakdown=Breakdown
label.entityMonitor.accountId=Account
tooltip.entityMonitor.accountMonitor=Select Account to view Account Monitor
label.entityMonitor.movementId=Movement
tooltip.entityMonitor.mvmntBrkdown=Select Movement to view Movement Summary Detail
label.entityMonitor.bookCode=Book
tooltip.entityMonitor.bookBrkdown=Select Book to view Book Monitor
label.entityMonitor.group=Group
tooltip.entityMonitor.viewGroupMonitor=Select Group to view Group Monitor
label.entityMonitor.metagroup=Metagroup
tooltip.entityMonitor.viewMetagroupMonitor=Select Metagroup to view Metagroup Monitor
button.entityMonitor.refresh=Refresh
tooltip.entityMonitor.refreshWindow=Refresh window
label.entityMonitor.rate=Rate
tooltip.entityMonitor.rateButton=Click to open Refresh Rate Window
button.entityMonitor.option=Options
tooltip.entityMonitor.option=Change Options
button.entityMonitor.close=Close
tooltip.entityMonitor.close=Close window
label.accountMonitor.title.window=Account Monitor - SMART-Predict
alert.entityMonitor.smartPredict=SMART-Predict
alert.entityMonitor.accessNotAvl=Access not available for
alert.entityMonitor.contactSysAdm=Please contact your System Administrator.
alert.entityMonitor.noMovements=No movements available

#-- Country Override Screen --
country.title.mainWindow=Country Maintenance
label.country.countryCode=Country
label.country.countryName=Country Name
tooltip.sortCountryName=Sort by country name
label.country.defaultWeekend1=Default <br> Weekend 1
label.country.defaultWeekends1=Default Weekend 1
tooltip.defaultWeekend1=Default Weekend 1
tooltip.sortDefaultWeekend1=Sort by default weekend 1
label.country.defaultWeekend2=Default <br> Weekend 2
label.country.defaultWeekends2=Default Weekend 2
tooltip.defaultWeekend2=Default Weekend 2
tooltip.sortDefaultWeekend2=Sort by default weekend 2
label.country.overrideWeekend1=Override <br> Weekend 1
label.country.overrideWeekends1=Override Weekend 1
tooltip.overrideWeekend1=Select override weekend 1
tooltip.sortOverrideWeekend1=Sort by override weekend 1
label.country.overrideWeekend2=Override <br> Weekend 2
label.country.overrideWeekends2=Override Weekend 2
tooltip.overrideWeekend2=Select override weekend 2
tooltip.sortOverrideWeekend2=Sort by override weekend 2
tooltip.changeSelCountry=Change selected country
country.title.changeScreen=Change Country Maintenance

#-- Non Workday Screen Label Tooltip --
nonworkday.title.mainWindow=Non Workday Maintenance
label.nonworkday.applyEntityCountry=Apply Entity Country
tooltip.sortApplyEntityCountry=Sort by apply entity country
label.nonworkday.applyAccountCountry=Apply Account Country
tooltip.sortApplyAccountCountry=Sort by apply account country
label.nonworkday.applyCurrencyCountry=Apply Currency Country
tooltip.sortApplyCurrencyCountry=Sort by apply currency country
nonworkday.title.changeScreen=Change Non Workday Maintenance
nonworkday.title.addScreen=Add Non workday Maintenance
label.nonworkday.facility=Facility
tooltip.sortFacility=Sort by facility
tooltip.facility=Select facility
label.nonworkday.optionYes=Yes
label.nonworkday.optionNo=No
tooltip.optionYes=Select Yes
tooltip.optionNo=Select No
tooltip.nonworkday.addButton=Add new facility
tooltip.nonworkday.changeButton=Change selected facility
tooltip.nonworkday.deleteButton=Delete selected facility

#-- Variable Related to Last Reference Time --
label.lastRefTime=Last Refresh:
label.refreshevery=Refresh Every

#-- Entity Maintenance Screen --
tooltip.entity.general.domesticCurrency=Select Domestic Currency
tooltip.entity.general.reportingCurrency=Select Reporting Currency
tooltip.entity.countryCode=Select Country Code
tooltip.entity.movementRetentionParameter=Large Movements retention period
tooltip.entity.smallMovementRetentionParameter=Small Movements retention period
tooltip.exchangeRateRetention=Ccy Exchange Rate retention period
tooltip.interestRateRetention=Ccy Interest Rate retention period
tooltip.sweepPosition=Sweep position level
tooltip.preAdvicePosition=Pre advice position level
tooltip.internalPosLvlName=Internal Balance position level
tooltip.externalPosLvlName=External Balance position level
tooltip.delete=Delete selected position level
entitymaintenance.alert.entitySaved=Entity saved:  Please note that access to this new entity must now be granted to roles

#-- Variables related to Font Setting --
button.options=Options
button.groupMonitor.options=Options
ccyMonitorOptions.fontSize=Font Size
ccyMonitorOptions.fontSizeSmall=Small
ccyMonitorOptions.fontSizeNormal=Normal
ccyMonitorOptions.refreshRate=Auto-refresh rate
tooltip.ccyMonitorOptions.selectFontSizeNormal=Select Normal Font Size
tooltip.ccyMonitorOptions.selectFontSizeSmall=Select Small Font Size
tooltip.options=Change Options
tooltip.groupMonitor.options=Change Options
label.entityMonitorOptions.rate=Auto-refresh rate
tooltip.entityMonitorOptions.rate=Enter refresh rate
label.entityMonitorOptions.font=Font Size
label.entityMonitorOptions.fontnormal=Normal
label.entityMonitorOptions.fontsmall=Small
tooltip.entityMonitorOptions.fontnormal=Select Normal Font Size
tooltip.entityMonitorOptions.fontsmall=Select Small Font Size
alert.currencyMonitor.refreshRate=Enter a valid refresh rate
alert.currencyMonitor.confirmRefreshRate=Refresh rate selected was below minimum.\\nSet it to 5 seconds.
tooltip.ccyMonitorOptions.enterRefreshRate=Enter refresh rate
tooltip.canceloption=Cancel changes and exit
alert.entityMonitorOptions.refreshRate=Refresh rate selected was below minimum.\\nSet to 5 seconds.
alert.entityMonitorOptions.notANumber=Not a Number

#-- Variable Related to External Balance Status --
label.movement.externalBalanceStatus=Ext

#-- Tooltips for MSD screen headers --
tooltip.movement.alerting=Alerting
tooltip.movement.position=Position Level
tooltip.movement.value=Value Date
tooltip.movement.amount1=Amount
tooltip.movement.sign=Sign
tooltip.movement.ccy=Currency Code
tooltip.movement.entityId=Entity ID
tooltip.movement.reference1=Reference 1
tooltip.movement.account=Account ID
tooltip.movement.input=Input Date
tooltip.movement.counterPartyId=Counterparty ID
tooltip.movement.pred=Predict Status
tooltip.movement.externalBalanceStatus=External Status
tooltip.movement.status=Movement Status
tooltip.movement.matchId=Match ID
tooltip.movement.source=Movement Source
tooltip.movement.msgformat=Message Format
tooltip.movement.notes=Movement Notes
tooltip.movement.beneficiary=Beneficiary ID
tooltip.movement.reference2=Reference 2
tooltip.movement.reference3=Reference 3
tooltip.movement.movementId=Movement ID
tooltip.movement.bookcode=Bookcode
tooltip.movement.custodian=Custodian ID
tooltip.movement.update_date=Updated Date
tooltip.movement.matchingParty=Matching party
tooltip.movement.productType=Product Type
tooltip.movement.postingDate=Posting Date
tooltip.movement.extra_text1=Extra Text 1
tooltip.movement.extraRef=Extra Reference
tooltip.movement.ilmFcast=ILM forecast status
tooltip.movement.uetr=UETR
label.currenctFilter=Current Filter
msd.noneFilter=<None>
msd.adhocFilter=Ad hoc
msd.title.filterName=Filter Name
msd.reloadFilterImageTooltip=Reload Filter
msd.saveFilterImageTooltip=Save Filter
msd.deleteFilterImageTooltip=Delete Filter
msd.filterComboTooltip=Select Filter
msd.alertDeleteProfile=Are you sure you want to delete the filter
msd.alertOverwriteProfile=Are you sure you want to overwrite this filter
msd.alertFillMandatoryFields=Please fill the filter name before saving
msd.alertFilterSaved=The filter was successfully saved
msd.alertFilterDeleted=The filter was successfully deleted
msd.dateradio.label=Search Date behaviour
msd.dateradio.option1.text=Fixed
msd.dateradio.option1.tooltip=Date parameters will be saved as absolute values
msd.dateradio.option2.text=Relative
msd.dateradio.option2.tooltip=Date parameters will be saved as relative to current date


#-- Variable Related to External Balance Status --
label.movementsearch.externalBalanceStatus=Ext Bal Status
tooltip.movExternalBalanceStatus=Select External Balance status

# Tooltip For Account Maintenance Add/Change/View --
tooltip.acctId=Account ID
tooltip.acctName=Account Name
tooltip.currencyCode=Currency Code
tooltip.linkAcctId=Link Account ID
tooltip.link.linkAcctId=Click here to select Link Account ID
tooltip.acctStatus=Account Status
tooltip.countryName=Country Name
tooltip.glCode=General Ledger Code
tooltip.corrCode=Correspondent Code
tooltip.acctBic=Bank Identifier Code
tooltip.acctIBAN=The IBAN can be entered with or without spaces. For legibility it will be reformatted if necessary to display spaces, but on the database it is stored without spaces
tooltip.accountParty=Predict Party ID of the party which holds or services this account
tooltip.isIlmContributer=Accounts flagged as ILM liquidity contributors can be easily selected to be member of the ILM global currency group for the purpose of Basel reporting
tooltip.isCentralBank=Accounts flagged as central bank members can be easily selected to be member of the ILM central bank group for the purpose of Basel reporting
tooltip.isCustomerAccount=Accounts flagged as customer accounts can be easily selected for the purpose of Basel reporting
tooltip.extraID=Extra ID
tooltip.primaryForecast=Primary Forecast
tooltip.primaryExternal=Primary External
tooltip.secondaryForecast=Secondary Forecast
tooltip.secondaryExternal=Secondary External
tooltip.mainAcctID=Main Account ID
tooltip.cutOffTime=Cut Off Time (hh:mm)
tooltip.bookCode=Book Code
tooltip.account.mainAcctID=Click here to select Main Account ID
tooltip.account.bookCode=Click here to select Book Code
tooltip.sweepTime=Sweep Time (hh:mm)
tooltip.tarBalAmount=Target Balance Amount
tooltip.tarBalSign=Target Balance Sign
tooltip.minSweepAmount=Minimum Sweep Amount
tooltip.maxSweepAmount=Maximum Sweep Amount
tooltip.usedInOtherSchedulers=Number of other sweep schedules that refer to this account
tooltip.creditIntMsg=Credit Internal Message Format
tooltip.debitIntMsg=Debit Internal Message Format
tooltip.creditExtMsg=Credit External Message Format
tooltip.debitExtMsg=Debit External Message Format
tooltip.creditInterMsg=Credit Intermediary Message Format
tooltip.debitInterMsg=Debit Intermediary Message Format
tooltip.contactDt=Contact Details
tooltip.accountInterestRate=Account Interest Rate

#-- Tooltip For Screen Add/Change Job Details --
tooltip.all=All
tooltip.sunday=Sunday
tooltip.monday=Monday
tooltip.tuesday=Tuesday
tooltip.wednesday=Wednesday
tooltip.thursday=Thursday
tooltip.friday=Friday
tooltip.saturday=Saturday
tooltip.firstDay=First Day
tooltip.lastDay=Last Day

#-- Variable Related to Filter Movement Search in MSD screen --
tooltip.clear=Clear
button.clear=Clear
alert.movementSearch.compareTwoTime=To Time must be greater than From Time
alert.movementSearch.reference=Reference searches will have no effect where no reference fields have been specified. Do you want to continue?

#-- Entity Process Information Screen Label And Tooltip --
entityprocess.title.window=Entity Process Information
label.entityprocess.process=Process
tooltip.process=Select process
label.entityprocess.processType=Process Type
label.entityprocess.defaultRunTime=Default Run Time
tooltip.defaultRunTime=Enter default run time
label.entityprocess.specTimeZone=(specified in Entity time zone)
label.entityprocess.timeDisplay=Time Display
label.entityprocess.entityLocalTime=Entity Local Time
tooltip.entityLocalTime=Select entity local time
label.entityprocess.sysTime=System Time
tooltip.sysTime=Select system time
label.entityprocess.entity=Entity
label.entityprocess.runTimeEntity=Run Time<br>(Entity Time)
label.entityprocess.runOrder=Run Order
label.entityprocess.status=Status
label.entityprocess.lastStarted=Last Started
label.entityprocess.databaseSession=Database<br>Session
label.entityprocess.lastHeartbeat=Last Heartbeat
label.entityprocess.lastEnded=Last Ended
label.entityprocess.lastStatus=Last Status
button.setRunProcess=Set Run
tooltip.setRunProcess=Set Run
button.recoverProcess=Recover
tooltip.recoverProcess=Recover
tooltip.entityProcess=Launch Entity Process Information screen
button.entityProcess=Entity
alert.entityProcess.recover=Are you sure that you want to set the status of this process to Disabled? You may want to verify with your database team that the database process is no longer running.
alert.entityProcess.save=Change have been made. Are you sure you want to save?
label.sweepPriorCuttoff.entityTime=Entity Time
alert.entityprocess.unSaved=All unsaved changes will be lost. Do you want to continue?
alert.entityprocess.defTime=Please enter a valid time

#-- New Alert For Currency Access Check --
alert.currencyAccess=Invalid: your role does not specify access to currencies/groups for this entity
alert.acctbreakdownmonitor.date=Please enter a valid date


#--alert displayed in Currency monitor when the user choose a date not on range according to default days
alert.toDateNotOnRange=To Date is not on default days range

#-- Variables related to Forecast Monitor --
label.forecastMonitor.currency=Currency
label.forecastMonitorGrid.currency=Ccy
tooltip.forecastMonitor.selectCurrency=Select Currency
label.forecastMonitor.entity=Entity
tooltip.forecastMonitor.selectEntity=Select Entity
label.forecastMonitor.breakdown=Breakdown
label.forecastMonitor.movementId=Movement
tooltip.forecastMonitor.mvmntBrkdown=Select Movement to view Movement Summary Detail
label.forecastMonitor.bookCode=Book
tooltip.forecastMonitor.bookBrkdown=Select Book to view Book Monitor
button.forecastMonitor.refresh=Refresh
tooltip.forecastMonitor.refreshWindow=Refresh window
button.forecastMonitor.rate=Rate
tooltip.forecastMonitor.rateButton=Click to open Refresh Rate Window
button.forecastMonitor.option=Options
tooltip.forecastMonitor.option=Click to open Options Window
button.forecastMonitor.close=Close
tooltip.forecastMonitor.close=Close window
alert.forecastMonitor.smartPredict=SMART-Predict
alert.forecastMonitor.accessNotAvl=Access not available for
alert.forecastMonitor.contactSysAdm=Please contact your System Administrator
alert.forecastMonitor.noMovements=No movements available
button.forecastMonitor.add=Add
tooltip.forecastMonitor.add=Add new record
button.forecastMonitor.change=Change
tooltip.forecastMonitor.change=Change selected record
button.forecastMonitor.delete=Delete
tooltip.forecastMonitor.delete=Delete selected record
button.forecastMonitor.cancel=Cancel
tooltip.forecastMonitor.cancel=Cancel changes and exit
button.forecastMonitor.save=Save
tooltip.forecastMonitor.save=Save changes and exit
button.forecastMonitor.ok=OK
tooltip.forecastMonitor.ok=Save changes and exit
label.forecastMonitor.title.window=Forecast Monitor - SMART-Predict
label.forecastMonitor.templateId=Template ID:
label.hideButtonBar=Hide Button Bar
label.showButtonBar=Show Button Bar
label.noMessageSelected=No Message Selected
label.updateResponse=Update response
label.areyouSure=Are you sure?
label.deleteMessages=Delete messages

#-- Variables related to Forecast Monitor Options --
label.forecastMonitorOptions.applyCurrencyMultiplier=Apply Currency Multiplier
tooltip.forecastMonitorOptions.applyCurrencyMultiplier=Apply Currency Multiplier
label.forecastMonitorOptions.hideweekend=Hide Weekend
tooltip.forecastMonitorOptions.hideweekend=Hide Weekend
label.forecastMonitorOptions.cumulativetotal=Cumulative Bucket totals
tooltip.forecastMonitorOptions.cumulativetotal=Cumulative Bucket totals
label.forecastMonitorOptions.hidezerovalue=Hide Zero Value Columns
tooltip.forecastMonitorOptions.hidezerovalue=Hide Zero Value Columns
label.forecastMonitorOptions.hidezerosum=Hide Zero Sum Columns
tooltip.forecastMonitorOptions.hidezerosum=Hide Zero Sum Columns
label.forecastMonitorOptions.title.window=Forecast Monitor Options - SMART-Predict
label.forecastTemplateOption.bucketNo=Bucket No
label.forecastTemplateOption.endsAt=End at
label.forecastTemplateOption.expand=Expand
label.forecastMonitorOptions.usertemplate=User Templates
label.forecastMonitorOptions.userbucket=User Buckets
label.forecastMonitorOptions.hidetotal=Hide Total
tooltip.forecastMonitorOptions.hidetotal=Hide Total
label.forecastMonitorOptions.hideassumption=Hide Assumption
tooltip.forecastMonitorOptions.hideassumption=Hide Assumption
label.forecastMonitorOptions.hidescenario=Hide Scenario
tooltip.forecastMonitorOptions.hidescenario=Hide Scenario

#-- Variables related to Forecast Template Options --
label.forecastTemplateOption.template=Template
tooltip.forecastTemplateOption.template=Template
label.forecastTemplateOptions.title.window=Forecast Monitor User Template Options - SMART-Predict

#-- Variables related to Forecast Assumptions --
label.forecastAssumptions.title.window=Forecast Monitor Assumptions - SMART-Predict
label.forecastAssumptionsAdd.title.window=Forecast Monitor Assumptions (Add) - SMART-Predict
label.forecastAssumptionsChange.title.window=Forecast Monitor Assumptions (Change) - SMART-Predict
label.forecastAssumptionAdd.date=Date
tooltip.forecastAssumptionAdd.date=Date
label.forecastAssumptionAdd.assumption=Assumption
tooltip.forecastAssumptionAdd.assumption=Enter the Assumption
label.forecastAssumptionAdd.amount=Amount
tooltip.forecastAssumptionAdd.amount=Enter the Amount

#-- ForecastMonitorTemplate --
label.forecastMonitorTemplate.title.window=Forecast Monitor Template Maintenance - SMART-Predict
label.forecastMonitorTemplateAdd.title.window=Add Forecast Monitor Template - SMART-Predict
label.forecastMonitorTemplateChange.title.window=Change Forecast Monitor Template - SMART-Predict
label.forecastMonitorTemplateAddDetail.title.window=Add a Column to Forecast Monitor Template - SMART-Predict
label.forecastMonitorTemplateChangeDetail.title.window=Change a Column of Forecast Monitor Template - SMART-Predict
label.findoraddpopup.title.window=Find / Add pop up (Normal) - SMART-Predict
label.findoraddpopupsubtotal.title.window=Find / Add pop up (Sub-Total) - SMART-Predict
button.forecastMonitor.find=Find
button.forecastmonitor.suggest=Suggest
label.copyforecasttemplate.title.window=Copy From Forecast Monitor Template - SMART-Predict
label.forecastMonitorTemplateAdd.templateId=Template ID
label.forecastMonitorTemplateAdd.templateName=Template Name *
label.forecastMonitorTemplateAdd.userId=User ID
label.forecastMonitorTemplateAdd.public=Public
tooltip.forecastMonitorTemplateAdd.templateId=Enter Template ID
tooltip.forecastMonitorTemplateAdd.templateName=Enter Template Name
tooltip.forecastMonitorTemplateAdd.userId=Select User ID
tooltip.forecastMonitorTemplateAdd.public=Select Public Status
tooltip.forecastMonitorTemplateAdd.moveUp=To move up the selected record
tooltip.forecastMonitorTemplateAdd.moveDown=To move down the selected record
label.forecastMonitorTemplateAddDetail.columnType=Column Type
label.forecastMonitorTemplateAddDetail.columnId=Column No *
label.forecastMonitorTemplateAddDetail.shortName=Short Name *
label.forecastMonitorTemplateAddDetail.description=Description *
label.forecastMonitorTemplateAddDetail.contributeCheck=Contributes to Total with Multiplier
tooltip.forecastMonitorTemplateAddDetail.columnType=Select a column type
tooltip.forecastMonitorTemplateAddDetail.columnId=Enter column number
tooltip.forecastMonitorTemplateAddDetail.shortName=Enter short name
tooltip.forecastMonitorTemplateAddDetail.description=Enter description
tooltip.forecastMonitorTemplateAddDetail.contributeCheck=Select to Contribute to total
tooltip.forecastMonitorTemplateAddDetail.contributeText=Enter multiplier to contribute total
tooltip.forecastMonitorTemplateCopy.templateId=Select a Template ID
label.findoraddpopup.id=ID
label.findoraddpopup.name=Name
label.findoraddpopup.type=Type
label.findoraddpopup.entity=Entity
button.findoraddpopup.Search=Search

tooltip.findoraddpopup.id=Enter ID
tooltip.findoraddpopup.name=Enter Name
tooltip.findoraddpopup.type=Select Type
tooltip.findoraddpopup.entity=Select Entity
tooltip.findoraddpopup.Search=Execute Search

alert.forecastMonitor.connectionLost=Unable to save server \\nPossible loss of connection
alert.forecastMonitor.refreshRate=Refresh rate selected was below minimum.\\nSet to 5 seconds
alert.forecastMonitor.totalValues=Total values cannot be updated
alert.forecastMonitor.enterNumber=Please enter a number
alert.forecastMonitor.validNumber=Please enter a valid number
alert.forecastMonitor.notNumber=Not a Number

alert.forecastMonitorOption.maxValue=Maximum value 30
alert.forecastMonitorOption.greaterThanPrevious=The value should be greater than the previous value
alert.forecastMonitorOption.lesserThanNext=The value should be lesser than the next value
alert.forecastMonitorOption.bucketExist=Selected bucket already exists

alert.templateOption.templateLock=Template ID is locked by
alert.templateOption.recordExist=Record already exists
alert.templateOption.duplicateTemplate=Template already defined for the same entity & currency
alert.assumption.deleteConfirm=Are you sure you want to delete?
alert.assumptionAdd.validAmount=Please enter a valid amount

label.forecasttemplate.column.id=ID
label.forecasttemplate.column.name=Name
label.forecasttemplate.column.columnno=Column No
label.forecasttemplate.column.displayname=Display Name
label.forecasttemplate.column.description=Description
label.forecasttemplate.column.multiplier=Multiplier
label.forecasttemplate.column.templateid=Template ID
label.forecasttemplate.column.templatename=Template Name
label.forecasttemplate.column.userid=User ID
label.forecasttemplate.column.public=Public
label.forecasttemplate.column.type=Type
label.forecasttemplate.column.entity=Entity

alert.forecasttemplate.templatelocked=Template ID is locked by 
alert.forecasttemplate.templatedelete=Are you sure you want to delete this template?
alert.forecasttemplate.savetemplate=Do you want to save the changes for template?
alert.forecasttemplate.templateexist=Template already exists
alert.forecasttemplate.mandatory=Please enter mandatory field marked with *
alert.forecasttemplate.columndelete=Are you sure you want to remove this record?
alert.forecasttemplate.totalmultiplier=Please enter Total Multiplier
alert.forecasttemplate.columnnumbers=Column number should be in the range 3 to 959
alert.forecasttemplate.validnumber=Please enter a valid number

#-- Variables related to Interface Monitor Screen --
label.interfaceMonitor.title=Interface Monitor
label.interfaceMonitor.header.interfaceId=Interface ID
label.interfaceMonitor.header.activeInterface=Enabled
label.interfaceMonitor.header.interfaceStatus=Engine Status
label.interfaceMonitor.header.lastMessageRecieved=Last Message
label.interfaceMonitor.header.totalMessages=Total
label.interfaceMonitor.header.totalMessagesProcessed=Processed
label.interfaceMonitor.header.totalMessagesAwaiting=Awaiting
label.interfaceMonitor.header.totalFilteredMessages=Filtered
label.interfaceMonitor.header.totalBadMessages=Bad
label.interfaceMonitor.bottomGrid.header.msgType=Message Type
label.interfaceMonitor.bottomGrid.header.msgStatus=Database Status
label.interfaceMonitor.bottomGrid.header.lastExecution=Last Execution
interfaceMonitor.title.window=Interface Monitor - SMART-Predict
interfaceExceptionsMonitor.title.window=Interface Exceptions Monitor - SMART-Predict
label.interfaceExceptions.messageType=Interface:
label.interfaceExceptions.messageStatus=Message Status:
tooltip.interfaceExceptions.Reprocess=Reprocess the selected Message
label.interfaceExceptions.header.messageId=Message ID
label.interfaceExceptions.header.exception=Exception
label.interfaceExceptions.header.description=Description
label.interfaceExceptions.header.inputDate=Input Date
tooltip.interfaceMonitor.interfaceId=Interface ID
tooltip.interfaceMonitor.active=Enabled
tooltip.interfaceMonitor.status=Engine Status
tooltip.interfaceMonitor.lastMessage=Last Message
tooltip.interfaceMonitor.total=Total number of messages
tooltip.interfaceMonitor.processed=Number of messages in processed state
tooltip.interfaceMonitor.awaiting=Number of messages in awaiting state
tooltip.interfaceMonitor.filtered=Number of messages in filtered state
tooltip.interfaceMonitor.bad=Number of messages in bad state
tooltip.interfaceMonitor.messageType=Message Type
tooltip.interfaceMonitor.messageStatus=Database Status
tooltip.interfaceMonitor.lastExecution=Last Execution Date
label.interfaceExceptions.movement.message=Movement Message
tooltip.interfaceExceptions.messageId=Message ID
tooltip.interfaceExceptions.exception=Exception
tooltip.interfaceExceptions.description=Description
tooltip.interfaceExceptions.inputDate=Input Date
button.interfaceMonitor.startInterface=Start
tooltip.interfaceMonitor.startInterface=Click to start interface
button.interfaceMonitor.stopInterface=Stop
tooltip.interfaceMonitor.stopInterface=Click to stop interface
alert.interfaceMonitor.rateNAN=Not a Number
alert.interfaceMonitor.rateBelowMin=Refresh rate selected was below minimum.<br>Set to 5 seconds.
alert.interfaceMonitor.noData=No data to display
alert.interfaceMonitor.fromDate=From date should be less than to date
alert.interfaceMonitor.toDate=To date should be greater than From date
alert.interfaceMonitor.showSummary=Show XML - Interface Summary Details
alert.interfaceMonitor.showStrdProc=Show XML - Stored Procedure Details
alert.interfaceMonitor.internal=Show XML - Internal
alert.interfaceExceptions.noMessage=No Message Selected
alert.interfaceExceptions.sure=Are you sure?
alert.warning.unavailableSI=Input Engine not available.


#-- Interface Settings (configuration) Screen --
tooltip.interfaceSettings.interfaceId=Interface ID
tooltip.interfaceSettings.enabled=Enabled
tooltip.interfaceSettings.emaillogs=Email Logs
tooltip.interfaceSettings.emaillogsto=Email Logs To
tooltip.interfaceSettings.beginTime=Alert Start
tooltip.interfaceSettings.endTime=Alert End
tooltip.interfaceSettings.threshold=Alert Threshold (s)
tooltip.interfaceSettings.class=Class
tooltip.interfaceSettings.property=Property
tooltip.interfaceSettings.value=Value
alert.interfaceSettings.save=Your changes will not take effect until the input engine is restarted
alert.interfaceSettings.emaillogsto=Email logs type is None, you cannot enter an email 
alert.interfaceSettings.number=Please enter a number
alert.interfaceSettings.posnumber=Please enter a positive number
alert.interfaceSettings.emails=You cannot enter more than 20 email addresses
alert.interfaceSettings.emaillog=Enter a valid Email
alert.interfaceSettings.requiresEmailLogsTo=Please enter an email
alert.interfaceSettings.text=Please enter a text
alert.interfaceSettings.time=Please enter a valid time
alert.interfaceSettings.sec=Please enter a number for seconds [1 - 99999]
alert.interfaceSettings.directory=Please enter a valid directory
alert.interfaceSettings.url=Please enter a valid URL
alert.interfaceSettings.password=Please enter a valid string
alert.interfaceSettings.startendtime=Enter the Alert start and end time for the following interface(s):
alert.interfaceSettings.savecancelconfirmation=You have already selected an interface for modification. Do you want to Save or Cancel the changes?
alert.interfaceSettings.requiresChannelName=Please provide MQ Channel Name
alert.interfaceSettings.requiresHostName=Please provide MQ Host Name
alert.interfaceSettings.requiresPortNumber=Please provide MQ Port Number
alert.interfaceSettings.requiresAlertThreshold=Please enter threshold for 
alert.interfaceSettings.alertThresholdRange=Please enter threshold greater than or equal to one minute

#-- Variables related to Interface Monitor screen --
label.interfaceMonitor.engineActionStart=START
label.interfaceMonitor.engineActionStop=STOP
label.interfaceMonitor.buttonStart=Start button
label.interfaceMonitor.buttonStop=Stop button

# Alert for Date Field in Match audit log screen
alert.matchAuditLog.toDate=To date should be greater than From date
# Alert for Parent Party
alert.changeParty.parentParty=Invalid: specified Parent ID causes an endless loop, please choose a different Parent ID
alert.checkParty=Invalid: This party cannot be deleted because other parties refer to it as a parent
# Alert for Excluded outstanding and Match queues
accountMonitorNew.alert.exceedDate=Selected date should be within 30 days from current date

# DFA errors
secureid.invalid=Invalid login
secureid.challenged=The SecurID has been challenged, please re-enter
secureid.timeout=The connection to the RSA server has timed out
rsaserver.error=<b>Exception occurred when connecting to RSA server</b><br>{0}


alert.killSession=User is already logged in, do you want to kill the existing session and continue?
error.killSession=Unable to kill session, please contact your Administrator

# Input Queue
inputauthorise.title.window=Input Authorise Queue - SMART-Predict
inputreffered.title.window=Input Referred Queue - SMART-Predict
inputAuth.title.window=Input Authorise - SMART-Predict

# Jasper Export
dynamicJasper.noDataFound="No data for this report";

# Intra Day Liquidity Account Group
accountGroup.id=Group ID
accountGroup.ilmGroupName=Name
accountGroup.currencyCode=Ccy
accountGroup.groupType=Dynamic?
accountGroup.createdByUser=Creator
accountGroup.publicPrivate=Public?
accountGroup.global=Global
accountGroup.accs=Accs
tooltip.addAccountGroup=Add new account group
tooltip.addGlobalAccountGroup=Click to create a new dynamic group containing all accounts flagged as ILM liquidity contributors
tooltip.addCentralAccountGroup=Click to create a new dynamic group containing all ILM liquidity contributor accounts flagged as central bank members
tooltip.changeAccountGroup=Change selected account group
tooltip.viewAccountGroup=View selected account group
tooltip.deleteAccountGroup=Delete selected account group
tooltip.accountGroupId=Sort by Group ID
tooltip.accountGroup.ilmGroupName=Sort by Name
tooltip.accountGroup.currencyCode=Sort by Currency
tooltip.accountGroup.groupType=Sort by Dynamic
tooltip.accountGroup.createdByUser=Sort by Creator
tooltip.accountGroup.publicPrivate=Sort by Public
tooltip.accountGroup.global=Sort by Global
tooltip.accountGroup.accs=Sort by Accounts
accountGroup.entityId=Entity ID
ilmAccountGrpAdd.title.window.addScreen=Add Account Group Details - SMART-Predict
ilmAccountGrpAdd.title.window.changeScreen=Change Account Group Details - SMART-Predict
ilmAccountGrpAdd.title.window.viewScreen=View Account Group Details - SMART-Predict

# ILM Analysis Monitor
ilmanalysismonitor.title.window=Liquidity Monitor - SMART-Predict
ilmanalysismonitor.entity.title=Entity
ilmanalysismonitor.ccy.title=Currency
ilmanalysismonitor.date.title=Value Date
ilmanalysismonitor.timeframe.title=Ccy Timeframe
ilmanalysismonitor.globalview.title=Global View
ilmanalysismonitor.groupanalysis.title=Group Analysis
ilmanalysismonitor.combinedview.title=Combined View
ilmanalysismonitor.groups.title=Groups
ilmanalysismonitor.scenarios.title=Scenarios
ilmanalysismonitor.balances.title=Balances
ilmanalysismonitor.showActual=Show Actual Datasets Only
ilmanalysismonitor.expandall=Expand All
ilmanalysismonitor.collapseall=Collapse All
ilmanalysismonitor.balance=Balance
ilmanalysismonitor.accumDC=Accumulated D/C
ilmanalysismonitor.time=Time
ilmanalysismonitor.interval=Interval
ilmanalysismonitor.showscale=Show Scale in Entity Timeframe
ilmanalysismonitor.includeMvnts=Include Open Movements
ilmanalysismonitor.sumByCutOff=Sum by Cut-off
ilmanalysismonitor.includeSOD=Include SOD
ilmanalysismonitor.maintain=Maintain
ilmanalysismonitor.setStylePopupName=Change Styles
ilmanalysismonitor.exportLabelWaiting=Export in progress, please wait...

ilmanalysismonitor.tree.thresholds=Thresholds
ilmanalysismonitor.tree.actBalance=Actual Balance
ilmanalysismonitor.tree.forebasic=Forecast (basic)
ilmanalysismonitor.tree.forecIncludeAct=Forecast (incl. actuals)
ilmanalysismonitor.tree.accTotals=Accumulated Totals
ilmanalysismonitor.tree.accActualCD=Accum. Actual C/D
ilmanalysismonitor.tree.accForeCD=Accum. Forecast C/D
ilmanalysismonitor.tree.AllGlobalGroups=All Global Groups

ilmanalysismonitor.legend.accActualC=Accum. Actual Credit
ilmanalysismonitor.legend.accActualC.Title=ActIn.
ilmanalysismonitor.legend.accActualD=Accum. Actual Debit
ilmanalysismonitor.legend.accActualD.Title=ActOut.
ilmanalysismonitor.legend.accForeC=Accum. Forecast Credit
ilmanalysismonitor.legend.accForeC.Title=FCIn.
ilmanalysismonitor.legend.accForeD=Accum. Forecast Debit
ilmanalysismonitor.legend.accForeD.Title=FCOut.
ilmanalysismonitor.legend.actBalance=Actual Balance
ilmanalysismonitor.legend.actBalance.Title=Act.
ilmanalysismonitor.legend.forebasic=Forecast (basic)
ilmanalysismonitor.legend.forebasic.Title=FC.
ilmanalysismonitor.legend.forecIncludeAct=Forecast (inc. actuals)
ilmanalysismonitor.legend.forecIncludeAct.Title=FC*.

ilmanalysismonitor.grid.use=Use
ilmanalysismonitor.grid.group=Group
ilmanalysismonitor.grid.extsod=Ext.SOD
ilmanalysismonitor.grid.exteod=Ext.EOD
ilmanalysismonitor.grid.fcastsod=Fcast.SOD
ilmanalysismonitor.grid.fcasteod=Fcast.EOD
ilmanalysismonitor.grid.openunexp=Open Unexp.
ilmanalysismonitor.grid.openunsett=Open Unsett.
ilmanalysismonitor.grid.thresholds=Thresholds
ilmanalysismonitor.grid.lastupdate=Last Update
ilmanalysismonitor.grid.type=Type
ilmanalysismonitor.grid.creator=Creator
ilmanalysismonitor.grid.scenario=Scenario ID
ilmanalysismonitor.grid.name=Name
ilmanalysismonitor.grid.use.tooltip=Use
ilmanalysismonitor.grid.group.tooltip=Group
ilmanalysismonitor.grid.extsod.tooltip=External Start Of Day
ilmanalysismonitor.grid.exteod.tooltip=External End Of Day
ilmanalysismonitor.grid.fcastsod.tooltip=Forecast Start Of Day
ilmanalysismonitor.grid.fcasteod.tooltip=Forecast End Of Day
ilmanalysismonitor.grid.openunexp.tooltip=Open Unexpected Movement Adjustment 
ilmanalysismonitor.grid.openunsett.tooltip=Open Unsettled Movement Adjustment 
ilmanalysismonitor.grid.thresholds.tooltip=Thresholds
ilmanalysismonitor.grid.lastupdate.tooltip=Last Update
ilmanalysismonitor.grid.type.tooltip=Type
ilmanalysismonitor.grid.creator.tooltip=Creator
ilmanalysismonitor.grid.scenario.tooltip=Scenario ID
ilmanalysismonitor.grid.name.tooltip=Name Of Scenario ID 

ilmanalysismonitor.requestRecalculation=Request recalculation of ILM data
ilmanalysismonitor.recalculationInProgress=Calculation in progress
ilmanalysismonitor.nodata=No ILM data exists for the selected entity, currency and date.
ilmanalysismonitor.nodata.forexport=No data is available for the export
ilmanalysismonitor.noglobalgroup=No global group exists for the selected entity and currency.
ilmanalysismonitor.recalculationError=Recalculation failed, please see logs
ilmanalysismonitor.recalculationWindowTitle=ILM data Recalculation
ilmanalysismonitor.continueWithoutRecalculate=Continue without recalculation of ILM data
ilmanalysismonitor.errorOnServerSide=An error occurred on server side, please see logs
ilmanalysismonitor.errorAllOptionNotAvailable=There are no appropriate currencies available. Currencies must share the same ccy-GMT offset in each entity to enable aggregation over all entities
ilmanalysismonitor.errorAllOptionCcyNotAvailable=This currency cannot be shown with Entity 'All'. Currencies must share the same ccy-GMT offset in each entity to enable aggregation over all entities
ilmanalysismonitor.recalculateConfirm=Are you sure? This may take some time 
ilmanalysismonitor.recalculateConfirmAlertTitle=Recalculate data
ilmanalysismonitor.errorDataNotUpToDate=Existing ILM data is not entirely up-to-date. Current data will be shown.<br> Hover over the red/orange icon for more information.
ilmanalysismonitor.alertNodataForSelection=The selected date is outside the retention period, No updated data will be displayed
ilmanalysismonitor.alertRecalculateRunning=A calculation process is already running please wait...
ilmanalysismonitor.alertDateOutsideRange=The selected date is outside the range, please chose another date
ilmanalysismonitor.alertNoCcyEntity=There are no entity/currencies appropriately configured for your role
ilmanalysismonitor.lastProfile=<Last>
ilmanalysismonitor.noneProfile=<None>
ilmanalysismonitor.alertDeleteProfile = Are you sure you want to delete the profile?
ilmanalysismonitor.alertOverwriteProfile = Are you sure you want to overwrite this profile? 
ilmanalysismonitor.alertFillMandatoryFields = Please fill the profile name before saving
ilmanalysismonitor.alertRevertProfile = Are you sure you want to reload this profile?
ilmanalysismonitor.alertProfileSaved = The profile was successfully saved
ilmanalysismonitor.alertProfileDeleted = The profile was successfully deleted
ilmanalysismonitor.profileComboTooltip = Select profile
ilmanalysismonitor.deleteProfileImageTooltip = Delete profile
ilmanalysismonitor.saveProfileImageTooltip = Save profile
ilmanalysismonitor.reloadProfileImageTooltip = Reload profile

ilmanalysismonitor.sourcesOfLiquidity = Sources of Liquidity
ilmanalysismonitor.groupComboTooltip = Select a group Id
ilmanalysismonitor.scenarioComboTooltip = Select a Scenario
ilmanalysismonitor.groupComboLabel = Group
ilmanalysismonitor.scenarioComboLabel = Scenario

ilmanalysismonitor.saveAsComboprofilePopupTooltip = Save As
ilmanalysismonitor.saveAsButtonprofilePopupLabel = Save

ilmanalysismonitor.tooltip.allEntityGlobalSummation=Summation of the entities using the main currency global account groups
ilmanalysismonitor.tooltip.allEntityGlobalAlternSummation=Summation of the entities using the alternative global groups, or main global group when no alternative is defined
ilmanalysismonitor.tooltip.allEntityAlternSummation=Summation of entities using ONLY alternative global groups

tooltip.saveExit=Save changes and Exit
tooltip.cancelExit=Cancel changes and Exit
alert.microsoftInternetExplorer=Microsoft Internet Explorer: Warning
alert.toTimeGreaterThanFromTimethe=To time should be greater than From time

# Intra Day Liquidity Scenario Maintenance

ilmScenario.id=Scenario ID
ilmScenario.scenarioName=Name
ilmScenario.currencyCode=Ccy
ilmScenario.scenarioDescription=Description
ilmScenario.createdByUser=Creator
ilmScenario.entity=Entity
ilmScenario.currency=Currency
ilmScenario.public=Public
ilmScenario.private=Private
ilmScenario.filterCondition=Filter Condition
ilmScenario.createdBy=Created by
ilmScenario.on=On
ilmScenario.succesRate=Success Rate
ilmScenario.delayRate=Delay Rate
ilmScenario.extraTransaction=Extra Transaction Set
ilmScenario.minutes=mins
ilmScenario.mins=mins
ilmScenario.credits=Credits
ilmScenario.debits=Debits
ilmScenario.percent=%
ilmScenario.percentBy=% by
ilmScenario.publicPrivate=Public?
tooltip.privatePublic=Specify if scenario is public and available to all users or private and only available to the creator of the scenario
tooltip.scenarioId=Enter Scenario ID
tooltip.scenarioName=Enter Scenario Name
tooltip.scenarioDescription=Enter Scenario Description
tooltip.filterCondition=Define an SQL \&#34;where clause\&#34; condition to specify which movements would be affected by the scenario.\\nColumns may be referenced from tables P_MOVEMENT, P_MOVEMENT_EXT, S_ENTITY and P_ACCOUNT.
tooltip.excludedCondition=Define an SQL \&#34;where clause\&#34; condition to specify which movements would not be affected by the scenario.\\nColumns may be referenced from tables P_MOVEMENT, P_MOVEMENT_EXT, S_ENTITY and P_ACCOUNT.
tooltip.successRateCreditPct=Enter percentage of credits success rate (0-999%)
tooltip.successRateDebitPct=Enter percentage of debits success rate (0-999%)
tooltip.delayRateCreditPct=Enter percentage of credits delayed (0-100%)
tooltip.delayRateDebitPct=Enter percentage of debits delayed (0-100%)
tooltip.delayTime=Enter Delay time in minutes (+/- 0-999) (positive/negative integer to indicate late/early delay)
tooltip.extraTransactionSet=Select Extra Transaction Set
ilmScenarioAdd.title.window.addScreen=Add ILM Scenario Detail - SMART-Predict
ilmScenarioAdd.title.window.changeScreen=Change ILM Scenario Detail - SMART-Predict
ilmScenarioAdd.title.window.viewScreen=View ILM Scenario Detail - SMART-Predict
ilmScenario.title.window=ILM Scenario Maintenance - SMART-Predict

ilmScenario.legend.mainDetails=Main Details
ilmScenario.legend.excludedMovements=Excluded Movements
ilmScenario.legend.scenarioMovements=Scenario Movements
ilmScenario.legend.remainingMovements=Remaining Movements
ilmScenario.legend.sourcesOfFunds=Sources of Funds Availability

ilmScenario.tab.general=General
ilmScenario.tab.movements=Movements
ilmScenario.tab.sources=Sources
ilmScenario.label.privatePublic=Private/Public
ilmScenario.label.systemScen=System Scenario
ilmScenario.label.joinToMvt=Join to P_MOVEMENT_EXT
ilmScenario.label.activeScen=Active Scenario
ilmScenario.label.AllowReporting=Allow ILM Reporting
ilmScenario.label.throughputMonitoring=Throughput Monitoring
ilmScenario.tooltip.throughputMonitoring=Throughput Monitoring
ilmScenario.label.dynamicQuery=Dynamic Query
ilmScenario.label.collateralAvlbl=Collateral Availability
ilmScenario.label.creditlineAvlbl=Credit-line Availability
ilmScenario.label.unencumberedLiqAssetAvlbl=Unencumbered Liquid Assets Availability
ilmScenario.label.otherSourcesAvlbl=Other Liquid Assets Availability
ilmScenario.tooltip.systemScen=System Scenario
ilmScenario.tooltip.joinMvt=Allows use of P_MOVEMENT_EXT columns in scenario filter and exclusion expressions. This can incur a performance cost.
ilmScenario.tooltip.activeScen=Active Scenario
ilmScenario.tooltip.allowReporting=Allow ILM Reporting
ilmScenario.tooltip.dynamicQuery=SQL query to generate a dynamic extra transaction set which can be used in ILM scenario calculations. \\nThis is an advanced feature - Please consult STL support for guidelines
ilmScenario.tooltip.collateralAvlbl=Enter percentage of Collateral Availability (0-100%)
ilmScenario.tooltip.creditlineAvlbl=Enter percentage of Credit-line Availability (0-100%)
ilmScenario.tooltip.unencumberedLiqAssetAvlbl=Enter percentage of Unencumbered Liquid Assets Availability (0-100%)
ilmScenario.tooltip.otherSourcesAvlbl=Enter percentage of Other Liquid Assets Availability (0-100%)
ilmScenario.alert.SourcesValue=Value must be between 0 and 100
ilmScenario.alert.allowReporting=Specifying \u2019Allow ILM Reporting\u2019 for this  \\n group/scenario will consume more server \\n resources. Do you wish to continue?

# Intra Day Liquidity Transaction Set Maintenance

ilmTransactionSet.entityId=Entity ID
ilmTransactionSet.currencyCode=Ccy
ilmTransactionSet.currency=Currency
ilmTransactionSet.transactionSetId=Transaction Set ID
ilmTransactionSet.transactionSetName=Name
ilmTransactionSet.accountId=Account ID
ilmTransactionSet.time=Time
ilmTransactionSet.credits=Credits
ilmTransactionSet.debits=Debits
ilmTransactionSet.description=Description
tooltip.accountId=Select Account ID
tooltip.transactionSetId=Enter Transaction Set ID
tooltip.transactionSetName=Enter Transaction Set Name
tooltip.sort.transactionEntityId=Sort by Entity ID
tooltip.sort.transactionCcy=Sort by Ccy
tooltip.sort.transactionSetId=Sort by Transaction Set ID
tooltip.sort.transactionName=Sort by Name
tooltip.add.TransactionSet=Add New Transaction
tooltip.change.TransactionSet=Change Selected Transaction
tooltip.view.TransactionSet=View Selected Transaction
tooltip.delete.TransactionSet=Delete Selected Transaction
tooltip.time=Enter Time
tooltip.debits=Enter Debits
tooltip.credits=Enter Credits
tooltip.description=Enter Description
tooltip.sort.accountId=Sort by Account ID
tooltip.sort.time=Sort by Time
tooltip.sort.credits=Sort by Credits
tooltip.sort.debits=Sort by Debits
ilmtransSetAdd.title.window.addScreen=Add ILM Transaction Set Display - SMART-Predict
ilmtransSetAdd.title.window.changeScreen=Change ILM Transaction Set Display - SMART-Predict
ilmtransSetAdd.title.window.viewScreen=View ILM Transaction Set Display - SMART-Predict
ilmtransSetDetailAdd.title.window.changeScreen=Change ILM Transaction Detail Display - SMART-Predict
ilmtransSetDetailAdd.title.window.addScreen=Add ILM Transaction Detail Display - SMART-Predict
ilmtransaction.title.window=ILM Transaction Set Maintenance - SMART-Predict

# Intra Day Liquidity Account Group Details
ilmaccountgroup.title.window=ILM Account Group Maintenance - SMART-Predict
ilmAccountGroupDetails.accountIdName=Account ID - Name
ilmAccountGroupDetails.type=Type
ilmAccountGroupDetails.class=Class
ilmAccountGroupDetails.level=Level
ilmAccountGroupDetails.entity=Entity
ilmAccountGroupDetails.currency=Currency
ilmAccountGroupDetails.grpId=Group ID
ilmAccountGroupDetails.privPub=Public/Private
ilmAccountGroupDetails.type=Type
ilmAccountGroupDetails.name=Name
ilmAccountGroupDetails.fixed=Fixed
ilmAccountGroupDetails.dynamic=Dynamic
ilmAccountGroupDetails.pub=Public
ilmAccountGroupDetails.priv=Private
ilmAccountGroupDetails.description=Description
ilmAccountGroupDetails.absoluteThresholds=Absolute Thresholds
ilmAccountGroupDetails.netThresholds=Net Cumulative Position Thresholds
ilmAccountGroupDetails.maximum=Maximum
ilmAccountGroupDetails.minimum=Minimum

ilmAccountGroupDetails.firstMin=First Minimum
ilmAccountGroupDetails.secondMin=Second Minimum
ilmAccountGroupDetails.firstMax=First Maximum
ilmAccountGroupDetails.secondMax=Second Maximum
ilmAccountGroupDetails.filterCondition=Filter Condition:
ilmAccountGroupDetails.grpMemberAccount=Group Member Accounts
ilmAccountGroupDetails.quickSearch=Quick Search
ilmAccountGroupDetails.listFromGrp=List from group
ilmAccountGroupDetails.accountInGrp=Accounts in this group
ilmAccountGroupDetails.accountNotInGrp=Accounts not in this group
ilmAccountGroupDetails.createdBy=Created by
ilmAccountGroupDetails.createdOn=On
ilmAccountGroupDetails.accntInGrp=Accounts in this group
ilmAccountGroupDetails.accntNotInGrp=Accounts not in this group
ilmAccountGroupDetails.ilmAccountgrpdetailsTitle=Account Group Details Screen
ilmAccountGroupDetails.showXMLRightGrid=Show Right Grid XML
ilmAccountGroupDetails.showXMLLeftGrid=Show Left Grid XML
ilmAccountGroupDetails.invalidAmountAlert=Please enter a valid amount
ilmAccountGroupDetails.recordExistsAlert=Record already exists
ilmAccountGroupDetails.syntaxAlert=Query syntax is incorrect, please verify your query:
ilmAccountGroupDetails.syntaxAlertTitle=Filter condition error
ilmAccountGroupDetails.nbOfRecords=No of Records:
ilmAccountGroupDetails.acctGrpRequired=Account Group ID is required
ilmAccountGroupDetails.labelId=ID
ilmAccountGroupDetails.labelName=Name
ilmAccountGroupDetails.labelLegendText=Default Legend Text
ilmAccountGroupDetails.tootlipLegendText=Define default legend text for the liquidity monitor screen
ilmAccountGroupDetails.allowReporting=Allow Reporting
ilmAccountGroupDetails.tootlip.allowReporting=When checked this group will have ILM report data built on a daily basis and will be selectable for ILM reporting
ilmAccountGroupDetails.netCum=Create Net Cumulative Position Data
ilmAccountGroupDetails.tootlip.netCum=Generates net cumulative position data for use in 'All' Entity ILM reports and for display in ILM report charts.  This data could grow to be large so only specify it on groups where the feature is necessary.
ilmAccountGroupDetails.correspBank=Correspondent Bank
ilmAccountGroupDetails.tootlip.correspBank=When checked, this group will be selectable as a correspondent bank for the purpose of Basel reporting
ilmAccountGroupDetails.defaultDynamic.descriptionGlobalCurrencyGroup=Automatically created global currency group based on accounts having IS_ILM_LIQ_CONTRIBUTOR='Y'
ilmAccountGroupDetails.defaultDynamic.descriptionCentralBankGroup=Automatically created CB group based on accts having IS_ILM_LIQ_CONTRIB and S_ILM_CENTRAL_BANK_MEMBE
ilmAccountGroupDetails.mainAgentText=Main Agent 
ilmAccountGroupDetails.tootlip.mainAgentText=Enter Main Agent
ilmAccountGroupDetails.alert.netMaximum=The maximum net cumulative position threshold must be positive 
ilmAccountGroupDetails.alert.netMinimum=The minimum net cumulative position threshold must be negative 
tooltip.ILMgroupId=Enter a group ID
tooltip.name=Enter group name
tooltip.privPub=Specify if group is public and available to all users or private and only available to the creator of the group 
tooltip.ilmGrpType=Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table 
tooltip.acctGrpFilter=Specify a filter condition to identify the accounts which are to be members of a dynamic group
tooltip.firstMin=Enter First Minimum
tooltip.secondMin=Enter Second Minimum
tooltip.Max=Maximum net cumulative position threshold (must be positive)
tooltip.Min=Minimum net cumulative position threshold (must be negative)
tooltip.listFromGrp=Select an account group 
tooltip.ILMTestButton=Test a filter condition 
tooltip.quickSearch=Quick Search an account ID
tooltip.listFromGrp=Select a group from list
ilmAccountGroupDetails.createThroughput =Create Throughput Ratio Data
ilmAccountGroupDetails.first = First
ilmAccountGroupDetails.second = Second
ilmAccountGroupDetails.time = Time
ilmAccountGroupDetails.target = Target %

title.user.userId=Please enter your user ID
title.user.password=Please enter your password
user.label.clickHeretoLogIn=Click here to log in
user.label.login=Login
user.label.cancel=Cancel
user.label.closeWindow=Close window
user.label.backtoFirstScreen=Back to first screen
login.html.internalerror=HTML internal error occurred: 
login.user.alert.passwordExpired=Your password has expired, please change it
login.user.passwordExpired1=Your password will expire in
login.user.passwordExpired2=&nbsp;days. Do you want to change it now?
login.user.secureId=Please enter your SecurID
title.login.successPage=Success
title.login.welcomeMessagePage=Welcome to SMART-Predict
pwdchange.password.bothBoxes=The passwords you typed do not match. Type the new passwords in both text boxes.
pwdchange.html.internalerror=HTML internal error occurred: 
pwdchange.welcomePredict=Welcome to SMART-Predict
pwdchange.enterOldPassword=Please enter your old password
pwdchange.enterAcceptableCharacter=Please enter password (Acceptable characters:[a-z,A-Z],[0-9],[~!@#$%^&*()-_=+[]:;'&ldquo;,<.>/?])
pwdchange.reenterNewPassword=Please re-enter your new password
title.failPage=Fail
title.failMessagePage=You have entered wrong userid/password
autologgedoff.msg.loggedoffIn=Your session will be automatically logged off in 
autologgedoff.msg.time=seconds.
autologgedoff.msg.stayLoggedIn=Click OK to stay logged in.

# Account Monitor
accountMonitor.title.accountMonitor=Account Monitor
accountMonitor.alert.label.notANumber=Not a number
accountMonitor.alert.label.refreshRateSelected=Refresh rate selected was below minimum.\\nSet to 5 seconds
accountMonitor.label.entity=Entity
accountMonitor.label.accountType=Account Type
accountMonitor.label.accountClass=Account Class
accountMonitor.label.startDayBalance=Start of Day Balance
accountMonitor.label.openUnexpected=Open Unexpected
accountMonitor.label.hideZeroBalances=Hide Zero Balance
accountMonitor.label.currencyThresold=Currency Threshold

accountMonitor.label.screenMode=Screen Mode
accountMonitor.label.group=Group
accountMonitor.label.notSum=Inc 'Do not Sum'
accountMonitor.label.incSum=Inc 'Always Sum'
accountMonitor.label.sumCutOff=Inc 'Sum by Cutoff'

# Group Monitor
groupMonitor.title.bookGroupMonitor=Book Group Monitor
groupMonitor.notANumber=Not a number
groupMonitor.refreshRateSelected=Refresh rate selected was below minimum.\\nSet to 5 seconds


# Central Bank Monitor
screenTitle.centralBankMonitor=Central Bank Monitor
centralBankMonitor.showValue='Show' value must be between 2 and 14. 
centralBankMonitor.whatIfAnalysis=Central Bank Monitor - What If Analysis
centralBankMonitor.validNumber=Not Valid Number
centralBankMonitor.refreshRateSelected=Refresh Rate Selected
centralBankMonitor.entity=Entity
centralBankMonitor.currencyLimit=Currency Limit
centralBankMonitor.currencyCode=Currency Code
centralBankMonitor.currencyMultiplier=Currency Multiplier
centralBankMonitor.fromDate=From Date
centralBankMonitor.toDate=To Date
centralBankMonitor.amountGreaterThanZero=Amount should be greater than zero
centralBankMonitor.validAmount=Please enter a valid amount
centralBankMonitor.fromToOutsideRange=From date and To date are outside the defined range
centralBankMonitor.fromOutsideRange=From date is outside the defined range
centralBankMonitor.ToOutsideRange=To date is outside the defined range

label.currencyMonitor=Currency Monitor

# Corporate screen
corporateAccount.title.corporateEntries=Corporate Entries
corporateAccount.recordExists=Record already exists
corporateAccount.addCorporateEntries=Add Corporate Entries
corporateAccount.wantToDelete=Are you sure you want to delete?
corporateAccount.alertDelete=Delete
corporateAccount.changeCorporateEntries=Change Corporate Entries
corporateAccount.fillMondatoryFields=Please fill all Mandatory Fields (*)
corporateAccount.alert.mandatory=Mandatory
corporateAccount.label.save=Save
corporateAccount.tooltip.save=Save
corporateAccount.label.close=Close
corporateAccount.tooltip.close=Close
corporateAccount.corporateName=Corporate Name*
corporateAccount.amount=Amount*
corporateAccount.validAmount=Please enter a valid amount

corporateAccount.label.add=Add
corporateAccount.tooltip.add=Add
corporateAccount.label.change=Change
corporateAccount.tooltip.change=Change
corporateAccount.labelDelete=Delete
corporateAccount.tooltipDelete=Delete

# Entity Monitor screen
entityMonitor.title=Entity Monitor screen

# personalEntityList screen
personalEntityList.title=Personal Entity List Screen

# Personal Currency List Screen
personalCurrency.title=Personal Currency List Screen

tooltip.hideControlBar=Hide control bar

genericDisplayMonitor.labelPage=Page


interfaceSettings.title=Input Configuration Screen
interfaceSettings.save=Save
interfaceSettings.summaryDetails=Show XML - Summary Details
interfaceSettings.bottomDetails=Show XML - Bottom grid Details
alert.interfaceSettings.nothingToSave=Nothing to save
label.lastRefTime2=Last Refresh:
screen.warning=Warning
screen.error=Error
screen.invalid=Invalid
label.forecastMonitorOption.userTemplates=User Templates
label.forecastMonitorOption.userBuckets=User Buckets

workflowmonitor.refreshRate=Refresh Rate
tooltip.saveRefresh=Save refresh rate
label.movementCannotBeUnlocked=Movement cannot be unlocked. Please contact your system administrator 
label.movementSummaryDisplay=Movement Summary Display
label.selectedOnlyMatchedItems=INVALID: You may only select matched items that are all in the SAME match
label.amountsDiffer=The amounts of the selected movements differ. Do you want to continue?
label.movementChanged=This movement has been changed
label.workflowMonitor=Workflow Monitor
label.refreshRateSelectedMonimum=Refresh rate selected was below minimum.\\nSet to 5 seconds
label.matchIDFiledAmended=Match ID field has been amended - Please choose action again after screen refreshes 
label.matchUserByAnotherProcess=Match is in use by another process 
label.bothAccountSelectedAre=Both accounts selected are 
label.accounts=&nbsp;accounts.
label.sweepSavedWithID=Sweep saved with ID: 
label.sweepSuccessfullySubmitted=successfully submitted
label.selected=Selected
label.preadviceCreatedWithMovement=Pre-advice created with Movement ID
label.getAdobeFlashPlayer=Get Adobe Flash Player
alert.showValue=Value must be between 2 and 14.
alert.serverTime=Server time
alert.passwordExpiresInFewDaysPart1=Your password will expire in 
alert.passwordExpiresInFewDaysPart2=&nbsp;days. Do you want to change it now?
alert.pleaseFillAllMandatoryFields=Please fill all mandatory fields (marked with *)
alert.validationmsg=Validation Message
alert.missingBodyOrSubject = Do you want to send this email without a body or subject?
genericDisplayMonitor.validPageNumber=Please enter a valid page number
personalCurrencyList.numberBetweenRange=Please enter a number between 1 - 14 
personalCurrencyList.numberBetweenBigRange=Please enter a number between 1 - 999
alert.refreshRateSelectedMonimum=Refresh rate selected was below minimum.<br>Set to 5 seconds
label.serverHasRunOutOfMemory=Server has run out of memory. Please contact your system administrator. The following parameters should be reviewed, 
label.maxNumberPages=, maximum number of pages:
label.amountSelectedMovementsDiffer=The amounts of the selected movements differ. Do you want to continue?
label.matchingMovementWillChange=Matching these movements will change composition and status of an existing match. Are you sure that you wish to continue?
label.movementCannotBeUnlocked=Movement cannot be unlocked. Please contact your system administrator
label.matchDifferentAmountTotalsAcrossPositionLevels=This match has different amount totals across position levels
label.includedItemsAtMultiplePositionLevels=There are included items at multiple position levels.
label.includedItemsForExternalBalance=There are included items for external balance at multiple position levels.
label.matchHasBeenChanged=This match has been changed
label.matchIsInUseByanotherProcess=Match is in use by another process

label.totalSelected=Selected:
label.totalInPages=Total in this page:
label.report.totalInPages=Amount total in this report
label.labelTotalOverPages=Total over all pages:
label.report.labelTotalOverPages=Grand total from Movement Summary Display

label.workflowXML=Workflow XML
label.exportPDF=Export to PDF
label.lossConnection=Unable to save server. Possible loss of connection
label.multipleMessageSelected=Multiple message selected
label.warningInternal=Warning - Internal
label.warningSummary=Warning - Summary
label.warningSoredProcedureDetails=Warning - Stored Procedure Details
label.resourceBusy=Resource is busy, please wait for current request to complete
label.validMatchId=Please enter a valid Match ID
label.mvmtAreBusy=Movement(s) are in use by
label.matchHasBeenChanged=This match has been changed 
label.matchChangedByanotherUser=Match has been changed by another user 
label.movementCannotBeUnlocked=Movement cannot be unlocked. Please contact your system administrator
label.isInBusyBy=is in use by
alert.cyclicInervalCannotBeZero=Cyclic Interval cannot be Zero
alert.dateShouldBeGreater=To Date should be greater than or equal to From Date
alert.fromDateShouldBeLess=From Date should be less than or equal to To Date
alert.enterValidDate=Please enter a valid date.
alert.enterValidFromDate=Please enter from date.
alert.enterValidToDate=Please enter to date.
alert.enterValidPageNumber=Please enter a valid page number
alert.htmlInternalErrorOccurred=HTML internal error occurred: 
alert.categoryAssociatedScenarios=This category has some associated scenarios
alert.deleteSystemScenario=You can't delete a System Scenario
alert.recordAlreadyExists=Record already exists
alert.scenarioQueryWasTestedSuccessfully=The scenario query was tested successfully, identifying no records in 
alert.ibanFormatInvalid=The IBAN format is invalid, please (re)check
alertDateShouldNotBeEarlierEhanFromDate=To Date should not be earlier than From Date
alert.priorityNotBeZero=Priority should not be zero
interfacerules.alert.ruleValueLengthValidation2=Please enter 50 characters only.
alert.enterValideReasonDescription=Please enter Valid Reason Description
alert.groupSpecifiedAsCurrencyGlobal=This group is specified as a currency global group and cannot be removed at this time
alert.transactionSetSpecified=This transaction set specified as 'Extra Transaction Set' for an ILM scenario and cannot be removed at this time
alert.validExchangeRate=Please enter a valid exchange rate
alert.nonZeroExchangeRate=Please enter a non-zero value for exchange rate
alert.balanceLogRententionperiod=Balance Log Retention period should not be higher than Balance Retention period
alert.delayTimeInterval=Delay time must be in (+/- 0-999) interval
alert.delayRateBetween=Delay Rate must be between 0 and 100
alert.changeScenarioId=Please change the scenario ID, since 'STANDARD' will be reserved for the standard dataset
alert.dateFromYesterday=Please enter a date from yesterday to last 7 days
alert.atleastAnyOneLevelBreakdown=Please select at least one Level Breakdown
alert.atlestAnyOneOption=Please select at least one option (Show DR/Show CR)
alert.selectDate=Please select a date
alert.noArchiveDefined=No archive defined
alert.accessToFacility=You don't have access to this facility
alert.FacilityMissingValues=Facility cannot be launched - missing values
alert.scenarioQueryTested=The scenario query was tested successfully, identifying
alert.scenarioQueryTested2=\\n Do you want to display data for the given base query?
label.in=in
label.fromTotalOf=from a total of
label.record=record
label.records=records
label.unableSave=Unable to save to server\\nPossible loss of connection
label.operationTakeTime= Note: this operation may take several minutes
label.recalculateILMData=Recalculate ILM data for the selected currency and date
label.existingILMData=Existing ILM data is not entirely up to date
label.incomplete=Incomplete
label.inconsistent=Inconsistent
label.newDataExistFor=New data exist for
label.numberAccounts=Number of accounts
label.dataFrom=Data from
label.dataTo=Data To
label.dataFor=Data For
label.errorContactSystemAdmin=Error occurred, Please contact your System Administrator:
ilmAccountGroupDetails.titleScreen=Account Group Details Screen
ilmanalysismonitor.title.screenName=Intra-Day Liquidity Monitor - Main Screen 
label.ConnectionError=Connection Error
label.taking=taking
label.seconds=seconds
label.milliseconds=milliseconds
label.preadviceCreatedWithMovementID=Pre-advice created with Movement ID
label.preadviceUpdated=Pre-advice updated successfully
label.exportMultiPages=First {0} pages will be exported. Do you want to continue?
label.movementHasBeenPlaced=. This movement has been placed in queue for authorisation by another user.
alert.sweepDetail.saved=Sweep saved with ID: 
alert.sweepDetail.submitted=&nbsp;successfully submitted
alert.sweepDetail.authorised=&nbsp;successfully authorised
alert.comboBox.invalidValue=Please enter a valid value
label.currentPage=Current Page
tooltip.thresholds=Thresholds
label.setStyle=Set Style
alert.ilmanalysis.enableToMaintain=Invalid: your role does not specify access to scenario/groups for this entity or for this screen
alert.ilmanalysis.nonValidValue=Please enter a valid value (minutes)
alert.noValidParent=This grid does not have a valid parent

## ILM Reporting
ilmReport.title=ILM Report Request
ilmReport.ilmGroup.title=ILM Group
ilmReport.currencyGlobalGroup=Currency Global Group
ilmReport.singleDay=Single Day
ilmReport.dateRange=Date Range
ilmReport.titleGroupReportDaily=Daily Liquidity Management Report - ILM Group
ilmReport.titleBaselAReportDaily=Daily Liquidity Management Report - Basel A: Direct Participant
ilmReport.titleBaselBReportDaily=Daily Liquidity Management Report - Basel B: Correspondent
ilmReport.titleBaselCReport=BCBS 248 Report C: Banks that provide correspondent banking services
ilmReport.titleGroupReportDateRange=Date Range Liquidity Management Report - ILM Group
ilmReport.titleBaselAReportDateRange=Date Range Liquidity Management Report - Basel A: Direct Participant
ilmReport.titleBaselBReportDateRange=Date Range Liquidity Management Report - Basel B: Correspondent
ilmReport.titleIntraDayRisk=Intra-Day Risk: Net Cumulative Positions
ilmReport.reportDate=Report Date:
ilmReport.reportingPeriod=Reporting Period
ilmReport.startOfDay=Start of Day
ilmReport.endOfDay=End of Day
ilmReport.ILUsage=Intraday Liquidity Usage
ilmReport.ILUsageAll=Intraday Liquidity Usage (Not available for entity 'All')
ilmReport.largestPositivePosition=Largest positive net cumulative position
ilmReport.largestNegativePosition=Largest negative net cumulative position
ilmReport.availableIL=Available Intraday Liquidity at SOD
ilmReport.total=Total
ilmReport.centralBankReserves=Central bank balance
ilmReport.collateralPledgedCentralBank=Collateral pledged at the central bank
ilmReport.unencumberedLiquidAssets=Unencumbered liquid assets on bank's balance sheet
ilmReport.TotalCreditLinesAvailable=Total credit lines available
ilmReport.ofWhichSecured=of which secured:
ilmReport.ofWhichCommitted=of which committed:
ilmReport.balancesWithOtherBanks=Balances with other banks
ilmReport.other=Other
ilmReport.totalPayments=Total Payments
ilmReport.dailyTotalInflow=Daily total inflow
ilmReport.dailyTotalOutflow=Daily total outflow
ilmReport.timeSpecific=Time Specific / Critical Payments
ilmReport.intradayThroughput=Intraday Throughput
ilmReport.inflow=Inflow
ilmReport.outflow=Outflow
ilmReport.endOfReport=*** End of Report ***
ilmReport.dailyIntradayLiquidityUsage=Daily Intraday Liquidity Usage
ilmReport.dailyIntradayLiquidityUsageAll=Daily Intraday Liquidity Usage (Not available for entity 'All')
ilmReport.largestPositiveNetCumulativePosition=Largest positive net cumulative position
ilmReport.max=Max
ilmReport.2ndMax=2nd max
ilmReport.3ndMax=3rd max
ilmReport.average=Average
ilmReport.average2=Average
ilmReport.min=Min
ilmReport.2ndMin=2nd min
ilmReport.3ndMin=3rd min
ilmReport.largestNegativeNetCumulativePosition=Largest negative net cumulative position
ilmReport.totalPayments=Total Payments
ilmReport.largestDailyTotalInflow=Largest daily total inflow
ilmReport.largestDailyTotalOutflow=Largest daily total outflow
ilmReport.timeSpecificCriticalPayments=Time Specific / Critical Payments
ilmReport.intradayThroughput=Intraday Throughput
ilmReport.time=Time
tooltip.ilmReport.selectGroupILM=Select an ILM Group
ilmReport.throughputGraph=Throughput Graph
ilmReport.netCumulativeBalance=Net Cumulative Balance
ilmReport.accumulatedCR=Accumulated CR
ilmReport.accumulatedDR=Accumulated DR
ilmReport.accumulatedTotal=Accumulated Total
ilmReport.netCumulativePositionDataCannotBeShown=Data not available - All groups must have 'Create Net Cumulative Position Data' checked.
ilmReport.noDataFound=NO DATA FOUND
ilmReport.dataState=ILM data state
ilmReport.noData=No Data
ilmReport.incomplete=Incomplete
ilmReport.complete=Complete
ilmReport.reportType=Report Type
ilmReport.reportType.ilmGrpReport=ILM Group Report
ilmReport.reportType.multiCurrencyILMReportExcel=Multi-Currency ILM Report - Excel
ilmReport.reportType.intradayRisk.netCumulative=Intraday Risk: Net Cumulative Positions
ilmReport.reportType.baselA=Basel A - Direct Participants
ilmReport.reportType.baselB=Basel B - Banks That Use Correspondent Banks
ilmReport.reportType.baselC=Basel C - Banks That Provide Correspondent Banking Services
ilmReport.scenario=Scenario
ilmReport.sumInOutflows=Sum Inflows/Outflows by
ilmReport.globalCcygrp=Currency Global Group
ilmReport.correspondent=Correspondent
ilmReport.sumInOutflows.tooltip=Indicate which group should be the source of data for 'Intraday liquidity usage', 'Total Payments', 'Critical Payment' and 'Throughput'
ilmReport.centralBankNotFound=Warning: Central bank group is not defined for one or more selected currencies

ilmReport.ccyMultiplierLabel=Currency Multiplier
ilmReport.ccyMultiplierNone=None
ilmReport.ccyMultiplierT=Thousands
ilmReport.ccyMultiplierM=Millions
ilmReport.ccyMultiplierB=Billions

ilmReport.balances=Balances
ilmReport.collateral=Collateral
ilmReport.totalCreditLineAvailable=Total credit lines available
ilmReport.ofWhich=Of which:
ilmReport.creditLineSecured=Secured
ilmReport.creditLineCommitted=Committed
ilmReport.centralbankcreditlines=Central bank credit lines
ilmReport.otherbankcreditlines=Other banks credit lines
ilmReport.unencumberedLiquidAssets=Unencumbered liquid assets
ilmReport.other=Other
ilmReport.correspondentBankGroup=Correspondent Bank Group
ilmReport.correspondentBankGroups=Correspondent Bank Groups
ilmReport.mainCorrespondentBank=Main Correspondent Banks
ilmReport.globalCurrencyGroup=Global Currency Group
ilmReport.centralBankGroup=Central Bank Group
ilmReport.LVPSName=LVPS Name
ilmReport.InflowOutflowSummation=Inflow/Outflow Summation
ilmReport.Correspondent=Correspondent
ilmReport.centralBank=Central Bank
ilmReport.correspondentBalance=Correspondent balance
ilmReport.correspondentCollateral=Correspondent collateral 
ilmReport.centralBankReserves=Central bank balance
ilmReport.CentralBankCollateral=Central bank collateral
ilmReport.BalancesOtherBanks=Balances other banks
ilmReport.otherSystemsCollateral=Other systems collateral
ilmReport.totalCreditLinesfromCorr=Total credit lines available from correspondent
ilmReport.TotalCreditLines=Total credit lines
ilmReport.CorrespondentTotalCreditLines=Correspondent credit lines
ilmReport.ValuePayements=B(i) Value of payments made on behalf of\n correspondent banking customers
ilmReport.totalGrossValuePayements=1. Total gross value of payments made\n on behalf of correspondent banking\n customers
ilmReport.intraDayCreditLines=B(ii) Intraday credit lines extended to \ncustomers
ilmReport.totalValueofCredit=1. Total value of credit lines extended to\n customers

ilmReport.ofWhichSecured=1a. Of which secured
ilmReport.ofWhichCommitted=1b. Of which committed
ilmReport.ofWhichPeakUsage=1c. Of which used at peak usage
ilmReport.ofWhichLargestCustomer=Largest Customer

ilmReport.criticaltransactions=Critical Transactions
ilmReport.showonlypayments=Show Only Payments (Debits)
ilmReport.showonlypayments.tooltip=Show Only Payments (Debits)
ilmReport.showall=Show All (Debits and Credits)
ilmReport.showall.tooltip=Show All (Debits and Credits)






ilmReport.costumerPayments=Customer Payments
ilmReport.UsePartyCashflowData=Use party cashflow data
ilmReport.UsePartyCashflowData.tooltip=Display payments made on behalf of correspondent banking customers using party cashflow data (payments in currency global group have counterparty related to party of an ILM customer account)
ilmReport.UsePaymentTypeData=Use payment type data
ilmReport.UsePaymentTypeData.tooltip= Display payments made on behalf of correspondent banking customers using movement payment type data (summing payments in currency global group)
ilmReport.LoroClient =LORO Client 
ilmReport.alertSelectPayment = At least one of the four checkBoxes must be selected
ilmReport.Branch = Branch
ilmReport.CorporateClient = Corporate Client 
ilmReport.Other = Other
ilmReport.ownEntity = Own Entity
ilmReport.include = Include:

#ILM Excel Report
ilmExcelReport.titleIntradayLiquidityManagementReport=Intraday Liquidity Management Report
ilmExcelReport.entity=Entity
ilmExcelReport.fromDate=From Date
ilmExcelReport.date=Date
ilmExcelReport.toDate=To Date
ilmExcelReport.multiplier=Multiplier
ilmExcelReport.scenario=Scenario
ilmExcelReport.partyInfo=Party Information
ilmExcelReport.firstLargestCounterParty=Largest Counterparty
ilmExcelReport.secondLargestCounterParty=2nd Largest Counterparty
ilmExcelReport.thirdLargestCounterParty=3rd Largest Counterparty
ilmExcelReport.firstLargestCustomer=Largest Customer
ilmExcelReport.secondLargestCustomer=2nd Largest Customer
ilmExcelReport.thirdLargestCustomer=3rd Largest Customer

ilmExcelReport.dailyMaximumLiquidityUsage=Daily Maximum Liquidity Usage
ilmExcelReport.largestPositiveNetCumulativePosition=Largest Positive Net Cumulative Position
ilmExcelReport.largestNegativeNetCumulativePosition=Largest Negative Net Cumulative Position
ilmExcelReport.correspondingTime=Corresponding Time
ilmExcelReport.threshold=Threshold
ilmExcelReport.totalPayments=Payments
ilmExcelReport.unsettled=Unsettled
ilmExcelReport.actuals=Actuals
ilmExcelReport.unsettledCredits=Credits
ilmExcelReport.unsettledDebits=Debits
ilmExcelReport.netCreditDebit=Net (credits - debits)
ilmExcelReport.summaryBalance=Balance
ilmExcelReport.summaryMaximumbalance=Maximum balance
ilmExcelReport.summaryMinimumBalance=Minimum balance
ilmExcelReport.unmonitoredTypes=Unmonitored types
ilmExcelReport.monitoredTypes=Monitored types

ilmExcelReport.standard=Standard
ilmExcelReport.group=Group
ilmExcelReport.currencyGlobalGroup=Currency Global Groups
tooltip.selectILMExcelAccountGroup=Select an ILM account group. The report output will be based solely on this group (No central bank data)
ilmExcelReport.standardScenarioNameDescription=Standard dataset
ilmExcelReport.dailyTotalInflow=Actuals: Daily Total Inflow
ilmExcelReport.dailyTotalOutflow=Actuals: Daily Total Outflow
ilmExcelReport.netInflowsOutflows=Actuals: Net (Inflow - Outflow)
ilmExcelReport.summaryDailyTotalInflow=Daily Total Inflow
ilmExcelReport.summaryDailyTotalOutflow=Daily Total Outflow
ilmExcelReport.summaryNetInflowsOutflows=Net (Inflow - Outflow)
ilmExcelReport.unsettledCredit=Unsettled: Credits
ilmExcelReport.unsettledDebit=Unsettled: Debits
ilmExcelReport.unsettledNet=Unsettled: Net (Credits - Debits)
ilmExcelReport.unconfirmedBalance=Unconfirmed Balance
ilmExcelReport.availableIntradayLiquidity=Available Intraday Liquidity
ilmExcelReport.sourcesOfIntradayLiquidityExclIncomingPayments=Sources of Intraday Liquidity (excl. incoming payments)
ilmExcelReport.centralBankBalance=Central Bank Balance
ilmExcelReport.centralBankCollateral=Central Bank Collateral
ilmExcelReport.balancesOtherBanks=Balances Other Banks
ilmExcelReport.otherSystemsCollateral=Other Systems Collateral
ilmExcelReport.totalCreditLines=Total Credit Lines
ilmExcelReport.ofWhich=Of which:
ilmExcelReport.creditLineSecured=Secured
ilmExcelReport.creditLineCommitted=Committed
ilmExcelReport.creditLinesSecured=Credit lines secured
ilmExcelReport.creditLinesCommitted=Credit lines committed
ilmExcelReport.unencumberedLiquidAssets=Unencumbered Liquid Assets
ilmExcelReport.other=Other
ilmExcelReport.gold=Gold (+)
ilmExcelReport.centralBankCollateralBlockedForCriticalPayments=Central bank collateral blocked for critical payments (-)
ilmExcelReport.totalAvailableIntradayLiquidityInclIncomingPayments=Total Available Intraday Liquidity (incl. incoming payments)
ilmExcelReport.riskAppetite=Risk Appetite
ilmExcelReport.outgoingPaymentsComparedToAvailableIntradayLiquidity=Outgoing payments compared to available intraday liquidity
ilmExcelReport.asOfTotalAvailableLiquidity=as % of total available liquidity
ilmExcelReport.asOfAvailableLiquidityExclIncomingPayments=as % of available liquidity (excl. incoming payments)
ilmExcelReport.intradayThroughput=Intraday Throughput
ilmExcelReport.intradayThroughputInflows=Intraday Throughput - Inflows
ilmExcelReport.intradayThroughputInflowsCcyTimeframe=Intraday throughput - Inflows (CCY timeframe)
ilmExcelReport.intradayThroughputOutflows=Intraday Throughput - Outflows
ilmExcelReport.intradayThroughputOutflowsCcyTimeframe=Intraday throughput - Outflows (CCY timeframe)
ilmExcelReport.criticalOutgoingPayments=Critical Outgoing Payments
ilmExcelReport.totalCriticalPaymentsOutflows=Total Critical Payments - Outflows
ilmExcelReport.asOfDailyTotalOutflow=as % of daily total outflow
ilmExcelReport.asOfDailyTotalInflow=as % of daily total inflow
ilmExcelReport.veryCriticalPayments=Very critical Payments
ilmExcelReport.otherCriticalPayments=Other critical Payments
ilmExcelReport.criticalIncomingPayments=Critical Incoming Payments
ilmExcelReport.totalCriticalPaymentsInflows=Total Critical Payments - Inflows
ilmExcelReport.asOfIncomingPayments=as % of incoming payments
ilmExcelReport.totMonitoredPayTypesInf=Total Monitored Critical Payment Types - Inflows
ilmExcelReport.totMonitoredPayTypesOutf=Total Monitored Critical Payment Types - Outflows
ilmExcelReport.tabNameAverageSummary = Average Summary
ilmExcelReport.tabNameDailyLiquidity = Daily Liquidity
ilmExcelReport.tabNameTotalPayments = Payments
ilmExcelReport.tabNameAvailableLiquidity = Available Liquidity
ilmExcelReport.tabNameThroughputIN = Throughput IN
ilmExcelReport.tabNameThroughputOUT = Throughput OUT
ilmExcelReport.tabNameCriticalOutflows = Critical Outflows
ilmExcelReport.tabNameCriticalInflows = Critical Inflows
ilmExcelReport.tabNameCriticalInflowsDetail = Critical Inflows Detail
ilmExcelReport.tabNameCriticalOutflowsDetail = Critical Outflows Detail
ilmExcelReport.ccyMultiplierEnabled = Enabled
ilmExcelReport.ccyMultiplierDisabled = None
ilmExcelReport.dailyLiqMaximumbalance=Maximum Balance
ilmExcelReport.dailyLiqMinimumBalance=Minimum Balance

ilmExcelReport.average=Average
ilmExcelReport.high=High
ilmExcelReport.secondHigh=Second High
ilmExcelReport.thirdHigh=Third High
ilmExcelReport.low=Low
ilmExcelReport.secondLow=Second Low
ilmExcelReport.thirdLow=Third Low

ilmExcelReport.averageThroughput=Average Throughput (CCY timeframe)

ilmExcelReport.veryCriticalPaymentsOutflows=Very Critical Payments - Outflows
ilmExcelReport.criticalPaymentsOutflows=Critical Payments - Outflows
ilmExcelReport.otherCriticalPaymentsOutflows=Other Critical Payments - Outflows
ilmExcelReport.outgoingCriticalPaymentsComparedToDailyTotalOutflow=Outgoing Critical Payments Compared to Daily Total Outflow
ilmExcelReport.outgoingCriticalPaymentsAsOfTotalAvailableLiquidity=Outgoing Critical Payments as % of Total Available Liquidity
ilmExcelReport.outgoingCriticalPaymentsAsOfAvailableLiquidityExclIncomingPayments=Outgoing Critical Payments as % of Available Liquidity (excl. incoming payments)
ilmExcelReport.veryCriticalPaymentsInflows=Very Critical Payments - Inflows
ilmExcelReport.criticalPaymentsInflows=Critical Payments - Inflows
ilmExcelReport.otherCriticalPaymentsInflows=Other Critical Payments - Inflows
ilmExcelReport.incomingCriticalPaymentsComparedToDailyTotalOutflow=Incoming Critical Payments Compared to Daily Total Inflow
ilmExcelReport.incomingCriticalPaymentsAsOfTotalAvailableLiquidity=Incoming Critical Payments as % of Total Available Liquidity
ilmExcelReport.incomingCriticalPaymentsAsOfAvailableLiquidityExclIncomingPayments=Incoming Critical Payments as % of Available Liquidity (excl. incoming payments)

ilmExcelReport.expectedTime=Expected time
ilmExcelReport.actualTime=Actual time
ilmExcelReport.amount=Amount
ilmExcelReport.sign=Sign
ilmExcelReport.cls=CLS
ilmExcelReport.ebs=EBS
ilmExcelReport.lch=LCH
ilmExcelReport.critical=Critical
ilmExcelReport.expectedSettlement=Expected Settlement
ilmExcelReport.actualSettlement=Actual Settlement
ilmExcelReport.cpType1=CP_Type1
ilmExcelReport.abc=ABC
ilmExcelReport.xyz=XYZ



ilmReport.warningMissingData=Warning: Report data is incomplete. Records are missing in the range {0} to {1}, do you wish to continue?
ilmReport.warningIncompletedData=Report data is incomplete. Records are missing in the range
ilmReport.onDayIncompletedData=Report data is incomplete. Records are missing for 
ilmreport.dateGreaterThanCcyTimeframeDate=Report date must be <= {0} (T-1 in the currency timeframe date)


ilmReport.oneDayMissingData=Warning: Report data is incomplete. Records are missing for {0}, do you wish to continue?
ilmReport.errorMissingData=An error occurred while checking missing data, please see logs.
ilmExcelReport.total=Total
ilmExcelReport.totalCriticalPayment= All Critical Payments (Monitored and Unmonitored)
ilmExcelReport.monitoredCriticalpaymentTypes=Monitored Critical Payment Types
ilmExcelReport.veryCritical=Very Critical
ilmExcelReport.critical= Critical
ilmExcelReport.other= Other
ilmExcelReport.asOfTotalAvailableLiquidityTitle=as % of Total Available Liquidity
ilmExcelReport.asOfAvailableLiquidityExclIncomingPaymentsTitle=as % of Available Liquidity (excl. incoming payments)
ilmExcelReport.asOfDailyTotalOutflowTitle=as % of Daily Total Outflow
ilmExcelReport.at= at
ilmExcelReport.throughputAt = Throughput at
ilmExcelReport.ccytimeframe = CCY timeframe


ilaapgeneral.account=Account
link.backtoPreviousPage= Back to previous page
link.closePage= Close page


# Account Attribute Maintenance
attributeusagesummary.title.window=Attribute Usage Summary - SMART-Predict
attributeusagesummaryadd.title.window=Define Attribute Usage
attributeusagesummary.attributeNotFound=There is no Account Attribute Definition
functGrp.id=Functional Group
restriction.id=Restriction
attribute.id=Attribute
displayOrder.id=Display Order
tooltip.functGrp=Select a functional group
tooltip.restriction=restriction of the selected functional group
tooltip.attribute=Select an attribute
alert.columndelete=Are you sure you want to remove this record?
attributeusagesummary.attributeId=Attribute ID
attributeusagesummary.name=Name
attributeusagesummary.type=Type
attributeusagesummary.display=Display
label.attributeusagesummaryadd.grandTotal= Grand Total
attributeusagesummaryadd.grandTotal.add=Add
attributeusagesummaryadd.grandTotal.substract=Subtract
attributeusagesummaryadd.grandTotal.noContribution=No contribution
attributeusagesummaryadd.grandTotal.tooltip=Specify if and how the attribute will contribute to a grand total (where relevant)
attributeusagesummaryadd.noAttribute=There are no attributes associated with this functional group. Please add an attribute to define its usage
type.text=Text
type.numeric=Numeric
type.date=Date
label.accountattribute.effectivedateTime=Effective Date/Time
label.accountattribute.accountId=Account
label.accountattribute.entity=Entity
tip.accountattribute.entity=Select an entity ID
label.accountattribute.currency=Currency
tip.accountattribute.currency=Select currency code
tip.accountattribute.account=Select Account ID
label.accountattribute.attribute=Attribute
tip.accountattribute.attribute=Select attribute ID
label.accountattribute.startdate=Start Date
tip.accountattribute.startdate=Select start Date
label.accountattribute.enddate=End Date
label.accountattribute.value=Value
tip.accountattribute.enddate=Select end Date
label.accountattribute.title.window=Account Attribute Maintenance - SMART-Predict
label.selectAccountAtt.prompt=Please select ...

label.accountattributeadd.time=Time
tip.accountattributeadd.time=Select time
label.accountattributeadd.effectivedate=Effective Date
tip.accountattributeadd.effectivedate=Select the effective date
label.accountattributeadd.value=Value
tip.accountattributeadd.value=Specify a value
label.accountattributeadd.type=Type
tip.accountattributeadd.type=Select a type
errors.effectiveDateRequired=Please select a value for effective date
errors.valueDateRequired=Please select a value date
errors.valuenotmatchpatteren=The given value does not match against attribute pattern.
errors.valuetoolarge=Value is higher than the allowed maximum of 
errors.valuetoosmall=Value is lower than the allowed minimum of 
errors.valueoutsideRange=The given amount is outside of range
errors.stringistoolarge=The text is longer than the allowed maximum of !!! characters
errors.stringistoosmall= The text is shorter than the allowed minimum !!! characters
errors.invalidNumber=Please enter a valid number
label.accountattributeadd.title.window=Account Attribute Maintenance details - SMART-Predict

#Account Attribute Definitions
# ---Label---#
label.accountattributedefinition.title.window=Account Attribute Definitions - SMART-Predict
label.accountattributedefinition.column.attributeid=Attribute ID
label.accountattributedefinition.column.attributename=Attribute Name
label.accountattributedefinition.column.type=Type
label.accountattributedefinition.column.updated=Updated
label.accountattributedefinition.column.updatedTime=Updated Date/Time
label.accountattributedefinition.column.user=User
label.accountattributedefinition.column.systemFlag=System
# ---alert---#
alert.accountattributehdr.acctdelete=Are you sure you want to delete this attribute definition?
alert.accountattribute.acctdelete=Do you wish to also delete account attribute data?
alert.deletion.confirm=Confirm Deletion
alert.change.existingattribute.data=Data exists for this attribute. If you change the definition of the attribute you must ensure that the existing data is consistent with the new settings
alert.validation.dateRange=Start Date must be lower than End Date
alert.warning.emptyAcctAttribute=Please select an Account Attribute
###################
#Define Attribute screen
# ---Label---#
label.accountattributehdr.title.window=Define Attribute - SMART-Predict
label.accountattributehdr.attributeid=Attribute ID
label.accountattributehdr.attributename=Name
label.accountattributehdr.tooltiptext=Tooltip Text
label.accountattributehdr.effectivedate=Effective Date
label.accountattributehdr.requireddate=Required 
label.accountattributehdr.norequireddate=Not Required
label.accountattributehdr.allowentrytime=Allow Entry of Time
label.accountattributehdr.defaulttime=Default Time to 00:00
label.accountattributehdr.type=Type
label.accountattributehdr.minvalue=Minimum Value
label.accountattributehdr.maxvalue=Maximum Value
label.accountattributehdr.minlength=Minimum Length
label.accountattributehdr.maxlength=Maximum Length
label.accountattributehdr.datevalue=Date Value
label.accountattributehdr.regexvalidation=Regex Validation
label.accountattributehdr.validationmsg=Validation Message
# ---alert---#
alert.accountattributehdr.acctattridrequired=Account Attribute ID is required
alert.accountattributehdr.checkminmax=Maximum value must be greater than minimum
alert.accountattributehdr.checkregex=Please enter a valid Regex
alert.accountattributehdr.checknumbervalue=is wrong value. Please enter a valid Number
# ---tooltip---#
tooltip.accountattributehdr.attributeid=Enter attribute id
tooltip.accountattributehdr.attributename=Enter attribute name
tooltip.accountattributehdr.tooltiptext=Enter tooltip text
tooltip.accountattributehdr.type=Select a type
tooltip.accountattributehdr.minvalue=Enter minimum value
tooltip.accountattributehdr.maxvalue=Enter maximum value
tooltip.accountattributehdr.minlength=Enter minimum length
tooltip.accountattributehdr.maxlength=Enter maximum length
tooltip.accountattributehdr.regexvalidation=Enter regex validation
tooltip.accountattributehdr.validationmsg=Enter validation message
# Account Attribute Latest value Screen
label.accountattributelastvalues.title.window=Account Attribute Latest Values - SMART-Predict


# Daylight saving period Screens
label.dststartdate=Start Date
label.dstenddate=End Date
label.dailysavingperiodsystem.title.window=Daylight Saving Period Summary - System
label.dailysavingperiodcurrency.title.window=Daylight Saving Period Summary - Currency
label.dailysavingperiodsystemdetail.title.window=Daylight Saving Period Detail - System
label.dailysavingperiodcurrencydetail.title.window=Daylight Saving Period Detail - Currency
tooltip.sort.dststartdate=Sort by Start Date
tooltip.sort.dstenddate=Sort by End Date
tooltip.add.dst=Add new daylight saving period 
tooltip.change.dst=Change the selected daylight saving period 
tooltip.delete.dst=Delete the selected daylight saving period 
tooltip.maintainDST=maintain the daylight saving periods.


# ILM Calculation Launcher Screen
label.ilmcalculation.title.window=ILM Calculation Launcher 
label.calculate=Calculate
label.ilmdatascreens=ILM data for screens
label.ilmdataandreportscreens=ILM data for screens and reports
label.ilmcalculationsuccess=ILM calculation completed successfully
label.ilmcalculationfailed=ILM calculation completed with errors
label.ilmcalculationsuccesswitherrors=ILM calculation completed successfully with errors
tooltip.ilmdatascreen=Build cash flow data and ILM time series data necessary for ILM monitor screen
tooltip.ilmdatascreenreports=Build full cash flow, ILM time series and reporting data
warn.calculationstatus=These calculations may take a long time. Do you wish to continue?
warn.outsideRange=Limit date has been exceeded, please change the chosen date
label.ilmccyprocess.processid=Process ID
label.ilmccyprocess.valuedate=Value Date
label.ilmccyprocess.laststarted=Last Started
label.ilmccyprocess.lastended=Last Ended
label.ilmccyprocess.currentstatus=Current Status
label.ilmccyprocess.currentstatus.running=Running
label.ilmccyprocess.currentstatus.notRunning=Not Running
label.ilmccyprocess.currentstatus.toRun=To Run
label.ilmccyprocess.currentstatus.skipped=Skipped
label.ilmccyprocess.lastexecutestatus=Last Execute Status
label.ilmccyprocess.lastexecutestatus.successful=Successful
label.ilmccyprocess.lastexecutestatus.failed=Failed
label.ilmccyprocess.lastexecutestatus.canceled=Cancelled
label.ilmccyprocess.calculinprogress=Data build in progress, please wait
label.ilmccyprocess.calculationalreadyinprogress=Manual calculation could not be launched for the current <br>selection. Cause: A current calculation for {0} for value date of {1} is actually in progress
label.ilmccyprocess.calculationlaunchfailed=Error occurred when launching the processing
label.ilmccyprocess.calculationcancelled=Calculation cancelled


label.accountspecificsweepformat.column.hostId=Host Id
label.accountspecificsweepformat.column.entityId=Entity Id
label.accountspecificsweepformat.column.accountId=Account ID
label.accountspecificsweepformat.column.specifiedAccountId=Account ID
label.accountspecificsweepformat.column.accountName=Name
label.accountspecificsweepformat.column.newInternalCrFormat=Internal C
label.accountspecificsweepformat.column.newInternalDrFormat=Internal D
label.accountspecificsweepformat.column.newExternalCrFormat=External C
label.accountspecificsweepformat.column.newExternalDrFormat=External D
label.accountspecificsweepformat.column.newExternalCrFormatInt=External C(Int)
label.accountspecificsweepformat.column.newExternalDrFormatINt=External D(Int)
label.accountspecificsweepformat.text.entity=Entity
label.accountspecificsweepformat.text.currency=Currency
label.accountspecificsweepformat.text.accountId=Account ID
label.accountspecificsweepformat.text.specAccountId=Specific Account ID*
label.accountspecificsweepformat.text.specEntityId=Specific Entity ID*
label.accountspecificsweepformat.text.credit=Credit
label.accountspecificsweepformat.text.debit=Debit
label.accountspecificsweepformat.text.internal=Internal
label.accountspecificsweepformat.text.external=External
label.accountspecificsweepformat.text.extViaIntermediary=Ext Via Intermediary
label.accountspecificsweepformat.tooltip.accountID=Select Account ID
label.accountspecificsweepformat.tooltip.specAccountID=Select Specific Account ID
label.accountspecificsweepformat.tooltip.internalCredit=Select Internal Credit
label.accountspecificsweepformat.tooltip.internalDebit=Select Internal Debit
label.accountspecificsweepformat.tooltip.externalCredit=Select External Credit
label.accountspecificsweepformat.tooltip.externalDebit=Select External Debit
label.accountspecificsweepformat.tooltip.extViaIntermediaryCredit=Select Ext Via Intermediary Credit
label.accountspecificsweepformat.tooltip.extViaIntermediaryDebit=Select Ext Via Intermediary Debit
label.accountSpecificSweepFormat.title.window=Account-Specific Sweep Format Display
label.accountSpecificSweepFormatAdd.title.window=Define Account-Specific Sweep Formats
label.accountspecificsweepformat.alert.warningExistAccountData=Data exists for this account. If you change the definition of the account <br>you must ensure that the existing data is consistent with the new settings
label.accountspecificsweepformat.alert.specAccountIdRequired=Specific Account ID is required
label.accountspecificsweepformat.alert.specEntityIdRequired=Specific Entity ID is required


scheduledReportHist.status.failed=Fail
scheduledReportHist.status.success=Success
scheduledReportHist.status.notAvailable=N/A


label.schedReportHist.fileId=File Id
tip.schedReportHist.fileId=Selected File Id
label.schedReportHist.jobId=Job Id
tip.schedReportHist.jobId=Selected Job Id
label.schedReportHist.reportTypeId=Report Type Id
tip.schedReportHist.reportTypeId=Selected Type Id
label.schedReportHist.scheduleId=Schedule Id
tip.schedReportHist.scheduleId=Selected Schedule Id
label.schedReportHist.runDate=Run Date
tip.schedReportHist.runDate=The Date where the report was executed
label.schedReportHist.elapsedTime=Elapsed Time
tip.schedReportHist.elapsedTime=Time took by the report to finish
label.schedReportHist.fileName=File Name
tip.schedReportHist.fileName=Report File Name
label.schedReportHist.outputLocation=Output Location
tip.schedReportHist.outputLocation= File location on the server
label.schedReportHist.fileSize=File Size
tip.schedReportHist.fileSize=File Size on KB
label.schedReportHist.exportStatus=Export Status
tip.schedReportHist.exportStatus='Success' if the report was properly generated to the output location or otherwise 'Fail'
label.schedReportHist.mailStatus=Mail Status
tip.schedReportHist.mailStatus='Success' : Mail successfully sent, 'N/A' : No send mail required, 'Fail' : Error occurred when sending the mail
label.schedReportHist.exportError=Export Error
tip.schedReportHist.exportError=If any error was occurred during the report export
label.schedReportHist.mailRsult=Mail Distribution List
tip.schedReportHist.mailRsult=The list of users to whom an email is actually sent 
label.schedReportHist.errorDescription=Error Description
tip.schedReportHist.errorDescription=Description of the error got when sending mail
label.schedreporthist.column.fileId=File Id
label.schedreporthist.column.runDate=Run Date/Time
label.schedreporthist.column.reportName=Report Name
label.schedreporthist.column.elapsedTime=Elapsed
label.schedreporthist.column.fileName=File Name
label.schedreporthist.column.exportStatus=Status
label.schedreporthist.column.mailStatus=Mail
alert.schedreporthist.noAccessToFeature=your role does not provide access to this feature, please contact your administrator for more details
tooltip.schedreporthist.fileId= Unique file identifier
tooltip.schedreporthist.runDate= Run Date/Time
tooltip.schedreporthist.reportName= Report Name
tooltip.schedreporthist.elapsedTime=Time Taken
tooltip.schedreporthist.fileName= File Name
tooltip.schedreporthist.exportStatus=Export Status
tooltip.schedreporthist.mailStatus=Mail Status

schedReportHist.mainScreen.title=Scheduled Reports History
schedReportHist.detailsScreen.title=Scheduled Reports History Details
tooltip.deleteSeletedFile=Delete selected file from history
button.tooltip.schedreporthist.details=Show file history details
button.schedreporthist.details=Details
button.tooltip.schedreporthist.download=Download report
button.schedreporthist.download=Download
button.tooltip.schedreporthist.resendMail=Resend mail to the distribution list
button.schedreporthist.resendMail=Resend Mail

button.schedReportHist.reportjob=Report Job
button.tooltip.schedReportHist.reportjob =Select a Report Job
button.schedReportHist.reporttype=Report Type
button.tooltip.schedReportHist.reporttype =Select a Report Type
button.schedReportHist.date=Date
button.schedReportHist.singleDate =Single Date
button.tooltip.schedReportHist.singleDate =Choose Single Date
button.schedReportHist.dateRange =Date Range
button.tooltip.schedReportHist.dateRange =Choose Date Range
button.schedReportHist.stratDate =  From
button.tooltip.schedReportHist.startDate =Select a From Date
button.schedReportHist.endDate =To
button.tooltip.schedReportHist.endDate =Select an End Date
alert.mail.mailSentWithSuccess=All mails has been sent successfully 
alert.mail.fileNoutFound=Report file was not found on the server, please contact your administrator
alert.mail.errorWhenSendingMail=Error occurred when sending the mail, please contact your administrator
alert.mail.oneOrManyMailsNotSent=One, or many mails has not been sent. Please check logs for more details or contact your administrator
alert.mail.connectionProblem=Unable to save server, Possible loss of connection! Please contact your administrator  




# ----- Mail Send Screen properties -----
label.mail.from=From
label.mail.to=To*
label.mail.cc=Cc
label.mail.subject=Subject
label.mail.emailAddress=Email Address
label.mail.messageBody=Message Body
label.mail.attachments=Attachments
label.mail.popUpTitle=Distribution list
alert.mail.sendSuccess=Mail sent successfully
alert.mail.sendFail=Mail sending failure
tooltip.mail.recipientsListTo=Select originals recipients
tooltip.mail.recipientsListCc=Select recipients in Cc
tooltip.mail.attachment=Attaching files
sendmail.title.window=Send Mail


menulabel.itemid.1=Monitors
menulabel.itemid.2=Work
menulabel.itemid.3=Input
menulabel.itemid.4=Match
menulabel.itemid.5=Search
menulabel.itemid.6=Account Monitor
menulabel.itemid.7=Currency Monitor
menulabel.itemid.8=Excluded Outstandings
menulabel.itemid.9=Archive
menulabel.itemid.10=Maintenance
menulabel.itemid.11=Controls
menulabel.itemid.12=System
menulabel.itemid.13=Manual Input
menulabel.itemid.14=Movement Display
menulabel.itemid.15=Movement Search
menulabel.itemid.16=Offered Queue
menulabel.itemid.17=Suspended Queue
menulabel.itemid.18=Confirmed Queue
menulabel.itemid.19=Manual Match
menulabel.itemid.20=Manual Sweep
menulabel.itemid.22=Sweep Search
menulabel.itemid.23=Sweep Display
menulabel.itemid.24=Cancel Queue
menulabel.itemid.25=Submit Queue
menulabel.itemid.26=Authorise Queue
menulabel.itemid.27=Start of Day Balance
menulabel.itemid.28=Party
menulabel.itemid.29=Account
menulabel.itemid.31=Sweep Message Format
menulabel.itemid.32=Match Quality
menulabel.itemid.34=Metagroup
menulabel.itemid.35=Group
menulabel.itemid.36=Book
menulabel.itemid.37=Entity
menulabel.itemid.38=Currency
menulabel.itemid.39=Holiday
menulabel.itemid.40=System Parameter
menulabel.itemid.42=Shortcut
menulabel.itemid.43=Change Password
menulabel.itemid.44=My User Audit Log
menulabel.itemid.45=My User Detail
menulabel.itemid.46=Role
menulabel.itemid.47=User
menulabel.itemid.48=Section
menulabel.itemid.49=Scheduler
menulabel.itemid.50=Audit Log
menulabel.itemid.51=Reports
menulabel.itemid.53=Internal Message
menulabel.itemid.54=User Status
menulabel.itemid.55=Password Rule
menulabel.itemid.56=Error
menulabel.itemid.57=Maintenance
menulabel.itemid.58=System
menulabel.itemid.59=User
menulabel.itemid.61=Help
menulabel.itemid.62=About
menulabel.itemid.63=Connection Pool Monitor
menulabel.itemid.65=Matching
menulabel.itemid.66=Movement
menulabel.itemid.67=Recovery
menulabel.itemid.68=Currency Group
menulabel.itemid.69=Currency Exchange Rate
menulabel.itemid.70=Currency Interest Rate
menulabel.itemid.71=Pre-advice Input
menulabel.itemid.72=Authorise Queue
menulabel.itemid.75=Referred Queue
menulabel.itemid.76=Pre-advice Display
menulabel.itemid.77=Sweep
menulabel.itemid.78=Account Breakdown Monitor
menulabel.itemid.95=Create
menulabel.itemid.96=Archive Search
menulabel.itemid.97=Opportunity Cost
menulabel.itemid.98=Turnover Report - Payment Pattern
menulabel.itemid.99=Intraday Balance - Main Currency
menulabel.itemid.100=Interest Charge per Account
menulabel.itemid.104=Workflow Monitor
menulabel.itemid.105=Input Exception
menulabel.itemid.106=Interface Setting
menulabel.itemid.107=Exceptions Queue
menulabel.itemid.108=Location
menulabel.itemid.109=Default Account
menulabel.itemid.110=Sweep Intermediary
menulabel.itemid.111=Sweeps Prior To Cut-Off
menulabel.itemid.112=Currency Alias
menulabel.itemid.113=Book Group Monitor
menulabel.itemid.115=Interface Monitor
menulabel.itemid.117=Reason
menulabel.itemid.119=Currency Funding
menulabel.itemid.120=Account Access Control
menulabel.itemid.121=Central Bank Monitor
menulabel.itemid.122=Correspondent Account Alias
menulabel.itemid.123=Interface Rule
menulabel.itemid.124=Entity Monitor
menulabel.itemid.125=Country
menulabel.itemid.126=Non Workday
menulabel.itemid.127=Forecast Monitor
menulabel.itemid.128=Forecast Monitor Template
menulabel.itemid.130=Unsettled Movements
menulabel.itemid.131=Excluded Movements 
menulabel.itemid.132=Scenario
menulabel.itemid.133=Scenarios
menulabel.itemid.134=Role Assignment
menulabel.itemid.135=Category
menulabel.itemid.136=ILM
menulabel.itemid.137=Account Groups
menulabel.itemid.138=General
menulabel.itemid.139=Currency Parameters
menulabel.itemid.140=ILM Scenarios
menulabel.itemid.141=Transaction Set
menulabel.itemid.142=Liquidity Monitor
menulabel.itemid.143=ILM Report
menulabel.itemid.144=Account Attributes
menulabel.itemid.145=Attribute Definition
menulabel.itemid.146=Attribute Values
menulabel.itemid.147=Attribute Usage
menulabel.itemid.148=ILM Calculation Launcher
menulabel.itemid.149=Report Scheduler
menulabel.itemid.150=Scheduled Report History
menulabel.itemid.151=PCM Report 
menulabel.itemid.60=PC
menulabel.itemid.201=Dashboard
menulabel.itemid.202=PCM
menulabel.itemid.203=Account Groups
menulabel.itemid.204=Stop Rule
menulabel.itemid.205=Spread Profiles
menulabel.itemid.206=Payment Categories
menulabel.itemid.207=Currency
menulabel.itemid.208=Interface Setting (PCM)
menulabel.itemid.209=Input Exception (PCM)
menulabel.itemid.210=Interface Monitor (PCM)
menulabel.itemid.211=PCM Monitor
menulabel.itemid.212=PCM Breakdown Monitor
menulabel.itemid.213=Payment Request Display
menulabel.itemid.214=Payment Request Search
menulabel.itemid.215=Archive Search (PCM)
menulabel.itemid.152=ILM ThroughPut Monitor
menulabel.itemid.153=Account Currency Maintenance Period
menulabel.itemid.1002=Scenario Message Format
menulabel.itemid.154= Cash Reserve Balance Management
menulabel.itemid.155= Alert Instance Display
menulabel.itemid.156= Scenario Message Format
menulabel.itemid.157= Alert Instance Summary
menulabel.itemid.159= 4-Eyes Review
menulabel.itemid.160= Sweep Archive Search

# Mantis 3015: Logon Screen: "Resource dictionary_en_GB.properties Not Found" warning message
org.apache.struts.taglib.bean.format.int=######
org.apache.struts.taglib.bean.format.float=######,####
org.apache.struts.taglib.bean.format.sql.timestamp=hh 'o''clock' a, zzzz
org.apache.struts.taglib.bean.format.sql.date=EEE, MMM d, ''yy
org.apache.struts.taglib.bean.format.sql.time=h:mm a
org.apache.struts.taglib.bean.format.date=hh 'o''clock' a, zzzz
errors.header =
errors.footer =
errors.prefix =
errors.suffix =

####################################### For PCM
spreadProfilesMaintenance.processPoint.All=All
spreadProfilesMaintenance.processPoint.Only=Only
spreadProfilesMaintenance.processPoint.ExceptAll=All Except
stopRuleMaintenance.title.window = Stop Rule Maintenance
stopRuleMaintenanceDetails.title.window = Stop Rule Details
queryBuilderScreen.title.window = Query Builder
PCMReport.BlockedPayments=Blocked Payments

#Connection Pool screen
connectionPool.title.window=Connection Pool Monitor Screen
connectionPoolDetails.title.window=Connection pool details
connectionPool.module=Module
connectionPool.Connections=Connections
connectionPool.poolStats=Pool Stats
connectionPool.activeConnections=Active Connections
connectionPool.idleConnections=Idle Connections
connectionPool.connectionPool=Connection Pool
connectionPool.connectionId=Connection ID
connectionPool.status=Java Status
connectionPool.duration=Open For (s)
connectionPool.stackTrace=StackTrace
connectionPool.lastActionTime=Opened at
connectionPool.sqlExecStartTime=Last SQL Start
connectionPool.sqlStatus=DB Status
connectionPool.sid=SID
connectionPool.audsid=AUD SID
connectionPool.sqlId=SQL ID
connectionPool.sqlStatement=SQL Statement


connectionPool.tooltip.module=Module (obtained from database v$session)
connectionPool.tooltip.connectionId=Java pool connection ID
connectionPool.tooltip.status=Java connection status
connectionPool.tooltip.duration=Time passed since opening connection
connectionPool.tooltip.stackTrace=Java stack trace for opening connection
connectionPool.tooltip.lastActionTime=Time connection opened
connectionPool.tooltip.sqlExecStartTime=Last SQL Start Statement Time
connectionPool.tooltip.sqlStatus=Database session status
connectionPool.tooltip.sid=Database session ID
connectionPool.tooltip.audsid=Database audit session ID
connectionPool.tooltip.sqlId=SQL ID of last statement
connectionPool.tooltip.sqlStatement= Last SQL statement

connectionPool.connectionSQLActive=Active
connectionPool.connectionSQLInActive=Inactive
connectionPool.connectionSQLKilled=Killed
connectionPool.connectionSQLSniped=Sniped

connectionPool.connectionJDBCClosed=Closed
connectionPool.connectionJDBCOpen=Open
connectionPool.alertDetailsChanged=This connection appears to have updated since the grid was last refreshed.<br>Are you sure you wish to Kill this connection?
connectionPool.alertKillingConsequences=Killing a connection can have severe consequences. DBA and Admin staff should be consulted.<br>Do you wish to continue
connectionPool.alertConnectionKilled=This connection appears to have been killed. The screen will be refreshed for more updated data.
connectionPool.alertDBConnectionErr=Database connection error, cannot get fresh informations from Database views,<br> if connection pool is exausted please kill some unused sessions.
connectionPool.alertDBGrantsNeeded=The screen cannot function for this chosen module until access is granted to the necessary views

ilmSummary.currencyEntityGroupAccount=Ccy-Entity-Group-Acc
ilmSummary.accountName= Account Name
ilmSummary.cutOff=Cut-off   
ilmSummary.predicted=Forecast 
ilmSummary.external=Actual  
ilmSummary.turnover=Turnover  
ilmSummary.available=Available 
ilmSummary.unsettled=Unsettled 
ilmSummary.unexpected=Unexpected
ilmSummary.preAdvice=Pre-advice
ilmSummary.sum=Sum
ilmSummary.confD=Pay % Conf. 
ilmSummary.inc=Inc %
ilmSummary.startOfDay=Start of Day                   
ilmSummary.minBalT=Min Bal / T                    


ilmSummary.tooltip.currencyEntityGroupAccount=Currency, Entity, ILM Group, Account Hierarchy. Use Options button to configure content and default sorting. Right click tree for context menu.
ilmSummary.tooltip.accountName= Account Name
ilmSummary.tooltip.cutOff=Cut-off                                                                                                                                     
ilmSummary.tooltip.predicted=Forecast balance                                                                                 
ilmSummary.tooltip.external=Actual balance                                                                                      
ilmSummary.tooltip.turnover=Turnover
ilmSummary.tooltip.available=Available balance
ilmSummary.tooltip.unsettled=Unsettled movements
ilmSummary.tooltip.unexpected=Unexpected movements
ilmSummary.tooltip.preAdvice=Pre-advice movements
ilmSummary.tooltip.sum=Monitor Summation: 'N' - Never; 'C' - by Cut-off
ilmSummary.tooltip.confD=Confirmed Debits as a percentage.
ilmSummary.tooltip.inc=Confirmed Credits as a percentage.
ilmSummary.tooltip.startOfDay=Start of Day Balance (actual)
ilmSummary.tooltip.minBalT=The lowest balance and latest time of its occurrence on the current value date, determined intraday from actual/external movements

ilmSummary.label.currentDateSummary=Current Date Summary

ilmSummary.alert.groupStagedNotFound=Warning: Group-staged data not found. The ILM calculation Launcher must be run for the relevant Entity, Currency and date.
ilmSummary.alert.dataFromArchiveNotFound=Warning: Retrieval of data from archive will be derived from ILM account-based time-series data. This may take a little longer than usual.

ilmthroughput.title.window=ILM ThroughPut Monitor
ilmthroughput.report.sheet1.title=ThroughPut Report
ilmthroughput.entity=Entity
ilmthroughput.date=Date
ilmthroughput.Today=Today
ilmthroughput.currency=Currency
ilmthroughput.group=Account Group
ilmthroughputbreakdown.currentFilter=Current Filter
ilmthroughputActTvsForecT=Actuals[t] vs Forecast [t]
ilmthroughputActTvsForecL=Actuals[t] vs Forecast [latest]
ilmthroughputActTvsActT=Actuals[t] vs Actuals [latest]
ilmthroughputCalculateAs=Calculate % throughput as

ilmthroughputbreakdown.title.window=ILM ThroughPut Breakdown Monitor - SMART-Predict
ilmthroughputbreakdown.foreOutlflowsCheckbox=Forecasted Outflows
ilmthroughputbreakdown.actOutlflowsCheckbox=Actuals Outflows
ilmthroughputbreakdown.foreIntlflowsCheckbox=Forecasted Inflows
ilmthroughputbreakdown.actInlflowsCheckbox=Actuals Inflows
ilmthroughputbreakdown.unsettledOutflows=Unsettled Outflows
ilmthroughputbreakdown.ccyThresholdCheckbox=Currency Threshold

throuputmonitor.entity=Entity
throuputmonitor.ccy=Ccy
throuputmonitor.ilm_group=ILM Group
throuputmonitor.forcinf=Inflows
throuputmonitor.forcout=Outflows
throuputmonitor.actinf=Inflows
throuputmonitor.actout=Outflows
throuputmonitor.unsetout=Unsettled Out.
throuputmonitor.threshold1=Threshold 1
throuputmonitor.threshold2=Threshold 2
throuputmonitor.current=Latest
throuputmonitor.actuals=Actuals
throuputmonitor.forecasted=Forecasted
throuputmonitor.throughputratios=Throughput



throuputmonitor.tooltip.entity=Entity Id
throuputmonitor.tooltip.ccy=Currency Code
throuputmonitor.tooltip.ilm_group=ILM Account Group
throuputmonitor.tooltip.forcinf=Forecasted Inflows
throuputmonitor.tooltip.forcout=Forecasted Outflows
throuputmonitor.tooltip.actinf=Actuals Inflows
throuputmonitor.tooltip.actout=Actuals Outflows
throuputmonitor.tooltip.unsetout=Unsettled Outflows
throuputmonitor.tooltip.threshold1=Threshold 1
throuputmonitor.tooltip.threshold2= Threshold 2
throuputmonitor.tooltip.current= Current Throughput Ratio
throuputmonitor.tooltip.actuals=
throuputmonitor.tooltip.forecasted=
throuputmonitor.tooltip.throughputratios=
alert.throuputbreakdown.atleastOneFilter = at least one filter needs to be selected

ilm.options.ccyEntityGrp = Ccy-Entity-Group
ilm.options.global= Global ?
ilm.options.summary= Summary
ilm.options.order= Order
ilm.options.ilmTab= ILM Tab
ilm.options.TabDate= Tab Date
ilm.options.globalChart= Global Chart
ilm.options.groupAnalysis= Group Analysis
ilm.options.combinedChart= Combined Chart
ilm.entityFilter = Entity Filter
ilm.ccyFilter = Currency Filter
button.option = Options
ilmoption.title.window = ILM Options
ilmsummary.title.window = ILM Summary


ilm.options.ccyEntityGrp_toolTip = Currency-Entity-Account Group Id
ilm.options.global_toolTip =Global group status: Y=Main global group; A=Alternative global group
ilm.options.summary_toolTip=Check box fro group to be included in Summary tab
ilm.options.order_toolTip=Numeric values indicate sorting of currencies and entities
ilm.options.ilmTab_toolTip=Check box for including an Entity/Currency tab
ilm.options.TabDate_toolTip=Select Date to be used in entity/currency tab
ilm.options.globalChart_toolTip=Check box to include Global View chart in entity/currency tab
ilm.options.groupAnalysis_toolTip=Check box to include Group Analysis chart in entity/currency tab
ilm.options.combinedChart_toolTip=Check box to include Combined View chart in entity/currency tab
# ---------- ExportPages -------------------------
exportPages.title = Pages to export
exportPages.label.current = Current Page
exportPages.label.allPages = All Pages
datagrid.context_menu.text.reset_column=Reset Column

#---------------------------------------------------------------------------
# ---------- Variables related to Pre Advice Input -------------------------

movement.inputBy=Input By
preAdviceInput.fieldSet1.legendText=Input Pre-Advice
preAdviceInput.fieldSet2.legendText=Pre-Advices Input in Last 24 hours
tooltip.preadvice.allUsers=All Users
tooltip.preadvice.currentUser=Current User
button.import=Import
button.move=Movement
button.load=Process
button.chooseFile= Choose file
button.reset=Reset Header
toolTip.import= Import
tooltip.move= Move
tooltip.load=Process
tooltip.chooseFile= choose a file from your local machine 
tooltip.reset=Reset configuration to defaults
preAdviceInput.dataSource = Data source
preAdviceInput.dataSourceDesc = Clipboard - (Values can be edited in grid after import)
preAdviceInput.headerLabel = My data has headers
preAdviceInput.chkHeader = If no headers, drag columns to represent order of appearance in dataset.
preAdviceInput.headerValue = Source header value:
preAdviceInput.headerType = Mandatory / Optional:
preAdviceInput.sourceFormat = Source format:
preAdviceInput.fieldSetImport1.legendText = Data Definition 
preAdviceInput.fieldSetImport2.legendText = Data Preview
preAdviceInput.importStatusLbl = Import status:
preAdviceInput.importInitStatus = No import performed yet
preAdviceInput.importInProgress = Import in progress
preAdviceInput.importComplete = Import complete
preAdviceInput.importFailed = Import failed
preAdviceInput.importFrom = Imported from
preAdviceInput.allRows = All rows
preAdviceInput.validRows = Valid rows
preAdviceInput.invalidRows = Invalid rows
preAdviceInput.showLabel = Show:
preAdviceInput.unsavedData = Grid contains unsaved data which will be lost if you continue.\n \u0020 Do you wish to continue?
preAdviceInput.validateAgain = All rows will be validated again, changes will be lost.\n \u0020 Do you wish to continue?
preAdviceInput.closeAlert = The grid contains unsaved data. \n \u0020 Continue without saving?
preAdviceInput.resetAlert=This will reset the configuration to default settings. \n \u0020  Are you sure?
preAdviceInput.invalidRowsAlert = The dataset contains invalid rows.\n \u0020  Continue and ignore invalid rows?
preAdviceInput.invalidHeaderAlert = There is something wrong with the imported data.\n \u0020  Please check if you don't forget to copy movement attributes columns .
preAdviceInput.rowStillInvalid =Warning, this row is still invalid.
preAdviceInput.ClipboardAlert= Clipboard is missing in the server... Try with paste
preAdviceInput.invalidImportedData =Some fields values are missing.\n \u0020 Please check if you respect the provided header.
preAdviceInput.extraHeader= Extra movement attributes columns  detected.\n \u0020 Please check the above checkBox and retry.
alert_header.error =Error
PreAdviceInput.title.window= Pre Advice Input - SMART-Predict
#---------- Grid columns--------
preAdviceInput.column.movement=ID;
preAdviceInput.column.preStatus=Pred.Status;
preAdviceInput.column.status=Status;
preAdviceInput.column.ccy=Ccy;
preAdviceInput.column.accountId=AccountID;
preAdviceInput.column.date=Value Date;
preAdviceInput.column.amount=Amount;
preAdviceInput.column.sign=Sign;
preAdviceInput.column.preference=Reference;
preAdviceInput.column.partyId=Counterparty ID;
preAdviceInput.column.cPartyTxt=Cparty Text;
preAdviceInput.column.matchParty=Match Party;
preAdviceInput.column.productType=Product Type;
preAdviceInput.column.bookCode=Book Code;
preAdviceInput.column.postDate=Post Date;
preAdviceInput.column.inputBy=Input by;
preAdviceInput.column.inputAt=Input at;

tooltip.chooseFile= Upload file...


instancerecord.id_header=ID
instancerecord.scenarioId_header=Scenario ID
instancerecord.uniqueIdentifier_header=Unique ID
instancerecord.status_header=Status
instancerecord.raisedDatetime_header=Raised Date
instancerecord.raisedUser_header=Raised User
instancerecord.lastRaisedUser_header=Last Raised User
instancerecord.lastRaisedDatetime_header=Last Raised Date
instancerecord.resolvedDatetime_header=Resolved Date
instancerecord.resolvedByUser_header=Resolved By
instancerecord.eventsLaunchStatus_header=Events Status
instancerecord.hostId_header=Host
instancerecord.entityId_header=Entity
instancerecord.currencyCode_header=Ccy
instancerecord.accountId_header=Account ID
instancerecord.amount_header=Amount
instancerecord.sign_header=Sign
instancerecord.overThreshold_header=Threshold
instancerecord.movementId_header=Movement Id
instancerecord.matchId_header=Match Id
instancerecord.sweepId_header=Sweep Id
instancerecord.paymentId_header=Payment Id
instancerecord.attributesJson_header=Attribute JSON
instancerecord.valueDate_header=Value Date
instancerecord.otherId_header=Other Id
instancerecord.otherIdType_header=Other Id Type

instancerecord.id_tooltip=ID
instancerecord.scenarioId_tooltip=Scenario ID
instancerecord.uniqueIdentifier_tooltip=Unique ID
instancerecord.status_tooltip=Status
instancerecord.raisedDatetime_tooltip=Raised Date
instancerecord.raisedUser_tooltip=Raised User
instancerecord.lastRaisedDatetime_tooltip=Last Raised Date
instancerecord.lastRaisedUser_tooltip=Last Raised User
instancerecord.resolvedDatetime_tooltip=Resolved Date
instancerecord.resolvedByUser_tooltip=Resolved By
instancerecord.eventsLaunchStatus_tooltip=Events Status
instancerecord.hostId_tooltip=Host
instancerecord.entityId_tooltip=Entity
instancerecord.currencyCode_tooltip=Ccy
instancerecord.accountId_tooltip=Account ID
instancerecord.amount_tooltip=Amount
instancerecord.sign_tooltip=Sign
instancerecord.overThreshold_tooltip=>Threshold
instancerecord.movementId_tooltip=Movement Id
instancerecord.matchId_tooltip=Match Id
instancerecord.sweepId_tooltip=Sweep Id
instancerecord.paymentId_tooltip=Payment Id
instancerecord.attributesJson_tooltip=Attribute JSON
instancerecord.valueDate_tooltip=Value Date
instancerecord.otherId_tooltip=Other Id
instancerecord.otherIdType_tooltip=Other Id Type

alertinstancesummary.title.window=Alert Instance Summary

tooltip.displayList= Display List
button.displayList= Display List
tooltip.refresh= Refresh
button.refresh=Refresh
tooltip.rate=Rate
button.rate=Rate

alertInstance.scenarioId= Scenario ID
alertInstance.instanceId= Instance ID
alertInstance.status= Status
alertInstance.eventStatus= Event Status
alertInstance.firstRaised= First Raised Date
alertInstance.lastRaised= Last Raised Date
alertInstance.resolved= Resolved Date/User
tooltip.alertInstanceScenario= Scenario ID
alertInstance.alertInstanceFieldSet= Attributes
alertInstance.alertInstanceFieldSetLog= Log
alertInstance.tab.attributes=Attributes
alertInstance.tab.json=Json
alertInstance.tab.message=Message
alertInstance.tab.logGrid=Log
alertInstance.uniqueIdentifier= Unique Identifier
alertInstance.hostId= HOST_ID
alertInstance.entityId= ENTITY_ID
alertInstance.currencyCode= CURRENCY_CODE
alertInstance.accountId=ACCOUNT_ID
alertInstance.valueDate=VALUE DATE
alertInstance.amount= AMOUNT
alertInstance.sign= SIGN
alertInstance.mvtId=MOVEMENT_ID
alertInstance.matchId=MATCH_ID
alertInstance.sweepId=SWEEP_ID
alertInstance.paymentId=PAYMENT_ID
alertInstance.otherId=OTHER_ID
alertInstance.otherIdType=OTHER_ID Type
button.alertInstance.msg= Messages
alertInstance.tooltip.msgBody= Message Body
instancerecord.dateTimeLog_tooltip= Date Time
instancerecord.userLog_tooltip= User
instancerecord.textLog_tooltip=Text

instancerecord.dateTimeLog_header= Date Time
instancerecord.userLog_header= User
instancerecord.textLog_header=Text

instancerecord.count_header = Count
instancerecord.expand_header = Expand
instancerecord.expand_tooltip = Expand
instancerecord.count_tooltip = Count

instancemessage.formatId_tooltip= Format ID
instancemessage.messageId_tooltip= Message ID
instancemessage.inputDate_tooltip= Input Date
instancemessage.updateDate_tooltip= Update Date 
instancemessage.updateUser_tooltip= Update User

instancemessage.formatId_header = Format ID
instancemessage.messageId_header = Message ID
instancemessage.inputDate_header = Input Date
instancemessage.updateDate_header = Update Date
instancemessage.updateUser_header = Update User

invalid_instance_id= Enter a valid Instance ID


changePassword.userId=User Id
changePassword.tooltip.userId=User ID
pwdchange.userId=User Id
pwdchange.tooltip.userId=User ID



account.tooltip.entityId=Entity Id
account.tooltip.accountId=Sort by Account ID
account.tooltip.accountName=Sort by Account Name
account.tooltip.currencyCode=Sort by Currency Code
account.tooltip.corresAccId=Sort by Correspondent Code
account.tooltip.accountType=Sort by Account Type
account.tooltip.mainAccountId=Sort by Main Account ID
account.tooltip.accountLevel=Sort by Account Level
account.tooltip.cutOff=Sort by Cut Off
account.tooltip.linkAccountId=Sort by Link Account ID
account.tooltip.bic=Sort by BIC
account.tooltip.accountClass=Sort by Account Class
account.tooltip.iban=Sort by IBAN
account.tooltip.isIlm=Sort by Is ILM Liquidity Contributor
account.tooltip.accountPartyId=Sort by Account Party ID
account.tooltip.status=Sort by Account Status

account.entityId=Entity Id
account.accountId=Account
account.accountName=Name
account.currencyCode=Ccy
account.corresAccId=Corres. Code
account.accountType=Type
account.mainAccountId=Main Account ID
account.accountLevel=Level
account.cutOff=Cut Off
account.linkAccountId=Link Account ID
account.bic=BIC
account.accountClass=Class
account.iban=IBAN
account.isIlm=isILM
account.accountPartyId=Party ID
account.status=Status
account.status.blocked=Blocked
account.status.open=Open
account.status.closed=Closed


mfa.externalSSO=External (Single-sign-on)
mfa.internlUserPass=User/Password

ccyAccMaintPeriod.entity.id= Entity
ccyAccMaintPeriod.currency.id= Currency Code
ccyAccMaintPeriod.show= Show
ccyAccMaintPeriod.current= Current
ccyAccMaintPeriod.all= All
ccyAccMaintPeriod.forDate= For Date:
ccyAccMaintPeriod.tooltip.change= Change
ccyAccMaintPeriod.tooltip.delete = Delete
ccyAccMaintPeriod.tooltip.save = Save
ccyAccMaintPeriod.tooltip.cancel = Cancel
ccyAccMaintPeriod.tooltip.log = Log
ccyAccMaintPeriod.tooltip.view = View
ccyAccMaintPeriod.tooltip.ccyCode=Currency Code
ccyAccMaintPeriod.tooltip.entityId=Entity Id
ccyAccMaintPeriod.tooltip.accountId=Account Id
ccyAccMaintPeriod.tooltip.startDate=Start date of the maintenance period
ccyAccMaintPeriod.tooltip.endDate=End date of the maintenance period
ccyAccMaintPeriod.tooltip.minimumReserve=The minimum reserve requirement for this maintenance period
ccyAccMaintPeriod.tooltip.tier=The volume of reserve holdings in excess of minimum reserve requirements which will be exempt from the deposit facility rate. (determined as a multiple the minimum reserve)
ccyAccMaintPeriod.tooltip.targetAvgBalance=Required average balance over the maintenance period
ccyAccMaintPeriod.tooltip.fillBalance=Balance to use in the 'Fill Days' period
ccyAccMaintPeriod.tooltip.eodBalanceSrc=Specify to use supplied internal balance or supplied external balance when determining EOD balances for days in the past
ccyAccMaintPeriod.ccyCode=Currency
ccyAccMaintPeriod.entityId=Entity
ccyAccMaintPeriod.accountId=Account
ccyAccMaintPeriod.startDate=Start Date
ccyAccMaintPeriod.endDate=End Date
ccyAccMaintPeriod.minimumReserve=Minimum Reserve
ccyAccMaintPeriod.tier=Tier
ccyAccMaintPeriod.targetAvgBalance=Target Avg Balance
ccyAccMaintPeriod.fillDays=Fill Days
ccyAccMaintPeriod.tooltip.fillDays=The number of days at end of the period where a specific target balance is required.
ccyAccMaintPeriod.fillBalance=Fill Balance
ccyAccMaintPeriod.eodBalanceSrc= EOD Balance Source
ccyAccMaintPeriod.chargeThreshold= Charge Threshold
ccyAccMaintPeriod.tooltip.chargeThreshold=Balance above which charges may be incurred
ccyAccMaintPeriod.chargeThresDesc= (Tier+1) * Minimum Reserve 
ccyAccMaintPeriod.start= Start
ccyAccMaintPeriod.end= End
ccyAccMaintPeriod.internal= Internal 
ccyAccMaintPeriod.external= External 
ccyAccMaintPeriod.eodBalSrc= EOD Balance Source
ccyAccMaintPeriod.excludeFillPeriodFromAvgLabel=Exclude Fill Period from average calculation
ccyAccMaintPeriod.minTargetBalanceLabel=Minimum Account Balance
ccyAccMaintPeriod.tooltip.minTargetBalanceTooltip=Prevent the sweeping process from causing this account's balance to fall below the specified value.
acctCcyPeriodMaint.addScreen= Account Currency Maintenance Period ADD - SMART-Predict
acctCcyPeriodMaint.changeScreen= Account Currency Maintenance Period CHANGE - SMART-Predict
alert.ccyAccMaintPeriod.overlapOfExistingRecord=  you will overlap existing record with the same Host, Entity and account
alert.ccyAccMaintPeriod.fillDaysvalidInput= Fill days should not exceed End Date - Start Date
alert.ccyAccMaintPeriod.FieldIsEmpty= Account ID, Fill Days and Fill Balance should not be empty
alert.ccyAccMaintPeriod.minTargetFillBalance= Fill balance cannot be lower than the Minimum Target Balance;
ccyAccMaintPeriod.tooltip.date= Date
ccyAccMaintPeriod.tooltip.time=Time
ccyAccMaintPeriod.tooltip.user= User
ccyAccMaintPeriod.tooltip.ipAddress = IP Address
ccyAccMaintPeriod.tooltip.reference = Reference
ccyAccMaintPeriod.tooltip.action = Action
ccyAccMaintPeriod.tooltip.from = From
ccyAccMaintPeriod.tooltip.to = To
ccyAccMaintPeriod.tooltip.field = Field
ccyAccMaintPeriod.tooltip.changedFrom = Changed From
ccyAccMaintPeriod.tooltip.changedTo = Changed To
ccyAccMaintPeriod.tooltip.tableName = Facility Name
ccyAccMaintPeriod.alert.updateValue = Tier/Minimum Reserve has changed. \n \u0020 Do you wish to update Target Avg Balance to the new Charge Threshold?
ccyAccMaintPeriod.minTargetBalance=Minimum Account Balance
ccyAccMaintPeriod.excludeFillDays=Exclude Fill Days
ccyAccMaintPeriod.tooltip.minTargetBalance= Minimum Target Balance
ccyAccMaintPeriod.tooltip.excludeFillDays=Exclude Fill Period from average calculation
ccyAccMaintPeriod.tooltip.addButton=Add Account Currency Maintenance Period
ccyAccMaintPeriod.tooltip.changeButton=Change Account Currency Maintenance Period
ccyAccMaintPeriod.tooltip.deleteButton=Delete Account Currency Maintenance Period
ccyAccMaintPeriod.tooltip.logButton=Log Account Currency Maintenance Period

acctSweepBalGrp.entity= Entity ID
acctSweepBalGrp.account= Account ID
acctSweepBalGrp.accountStatus= Account Status
acctSweepBalGrp.sweepAccount= Sweep Account ID
acctSweepBalGrp.tooltip.entity= Entity ID
acctSweepBalGrp.tooltip.account= Account ID
acctSweepBalGrp.tooltip.accountStatus= Account Status
acctSweepBalGrp.tooltip.sweepAccount= Sweep Account ID
acctSweepBalGrp.alert.sameAccount= Account ID and Sweep Account ID should not be the same
acctSweepBalGrp.alert.changeAccount= Record with same Sweep Account ID is already exist
acctSweepBalGrp.alert.invalidComboValue = Please select a valid sweep account
acctSweepBalGrp.alert.chooseSweep= Please choose the previous Sweep Account ID before adding new record
acctSweepBalGrp.alert.deleteAcctSweep= Existing account sweep balance will be removed.\n \u0020 Do you want to continue?

button.copyFrom= CpyFrom
acctMaintenance.tab.general= General
acctMaintenance.tab.balances= Balances
acctMaintenance.tab.sweeping= Sweeping
acctMaintenance.useSubAcctTim= Use Sub-Account Timing
acctMaintenance.targetBalance=  Target Balance
acctMaintenance.autoSwpfFlgAlert=  account does not have automatic sweeping enabled.
acctMaintenance.autoSwpfFlgAlertSave= not having automatic sweeping enabled.\n \u0020 Do you want to continue? 

linked.account= Account
linked.name= Name
linked.ccy= Ccy
linked.type=Type
linked.level= Level
linked.class= Class

linked.tooltip.account=Account
linked.tooltip.name= Name
linked.tooltip.ccy= Ccy
linked.tooltip.type= Type
linked.tooltip.level= Level
linked.tooltip.class= Class

#-- Mantis 5709 --
usermaintenance.extAuthId= External Auth ID
tooltip.extAuthId= ID of user in external authenticating system

#-- Mantis 5622 --
cashRsvrBal.entity= Entity
cashRsvrBal.currency= Currency
cashRsvrBal.account= Account
cashRsvrBal.accountType= Account Status
cashRsvrBal.startDate= Start
cashRsvrBal.endDate= End
cashRsvrBal.targtAvgBal= Target Avg Bal
cashRsvrBal.fillDays= Fill Days
cashRsvrBal.fillBal= Fill Balance
cashRsvrBal.minTargetBal= Min Target Balance
cashRsvrBal.tooltip.entity= Entity ID
cashRsvrBal.tooltip.currency= Currency Code
cashRsvrBal.tooltip.account= Account ID
cashRsvrBal.tooltip.accountType= Account Status
cashRsvrBal.tooltip.startDate= Click to choose start date
cashRsvrBal.tooltip.endDate= Click to choose end date
cashRsvrBal.tooltip.targtAvgBal= Target AVG Balance
cashRsvrBal.tooltip.fillDays= Fill Days
cashRsvrBal.tooltip.fillBal= Fill Balance

cashRsvrBal.heading.valueDate=Date         
cashRsvrBal.heading.balanceTarget=Balance/Target 
cashRsvrBal.heading.runningAvg=Running Avg.
cashRsvrBal.tooltip.valueDate=Value Date 
cashRsvrBal.tooltip.balanceTarget=Balance Value
cashRsvrBal.tooltip.runningAvg=Average Balance per Account

cashRsvrBal.rowTotalLabel=Total
cashRsvrBal.rowSweepToToTarget=Sweep To Target



#mantis 5702
acctMaintenance.fmi= FMI
acctMaintenance.servicingEntityId= Servicing Entity
acctMaintenance.accNameInSvcEntity= Name in Svc Entity
acctMaintenance.inThisEntity= In this entity...
acctMaintenance.inServEntity= In servicing entity...
acctMaintenance.applyBetween= Apply between
acctMaintenance.and= and
acctMaintenance.oneNostro= One-Nostro
acctMaintenance.includeLoro= Include Loro/Predict Options
acctMaintenance.tooltip.includeLoro=include Loro
acctMaintenance.tooltip.fmi=Indicate Financial Market Infrastructure relevant to this account
acctMaintenance.tooltip.servicingEntityId=Specify the entity in which this account would appear as a Loro account
acctMaintenance.tooltip.accNameInSvcEntity=Specify the account name to use when this account appears in monitors under the servicing entity
acctMaintenance.tooltip.applyBetween= Apply between
acctMaintenance.tooltip.and= and 
acctMaintenance.tooltip.inThisEntity=In this entity
acctMaintenance.tooltip.inServEntity= In this servicing entity


#Mantis 6162
alertDisplay.hostNotFound=<Host not found>
alertDisplay.entityNotFound=<Entity not found>
alertDisplay.ccyNotFound=<Currency not found>
alertDisplay.accountNotFound=<Account not found>

#Mantis 6168
maintenanceLog.tooltip.field = Field
maintenanceLog.tooltip.changedFrom = Changed From
maintenanceLog.tooltip.changedTo = Changed To

#Mantis 6275
tooltip.interBalEodDate= Select Internal Balance EOD Date 
tooltip.extBalEodDate= Select External Balance EOD Date 
tooltip.balance.interBalEodDate= Business day on which the supplied internal balance was evaluated
tooltip.balance.extBalEodDate= Business day on which the supplied external balance was evaluated
message.alert.emptySuppliedInterBalance= A supplied internal must be provided before entering a value for this field
message.alert.emptySuppliedExtBalance= A supplied external must be provided before entering a value for this field

#Mantis 6135
maintenanceLogView.facility= Facility
maintenanceLogView.date= Date
maintenanceLogView.ipAddress= IP Address
maintenanceLogView.user= User
maintenanceLogView.recordRef= Record reference
maintenanceLogView.action= Action
maintenanceLogView.fullDetails= Full Details
maintenanceLogView.oldVal= Old Value
maintenanceLogView.newVal= New Value



#Mantis 5819
msd.addCols.title= Additional Columns  - SMART-Predict
msd.heading.addColumns.table= Table
msd.heading.addColumns.column= Column
msd.heading.addColumns.label= Label
msd.heading.addColumns.sequence= Sequence
msd.heading.addColumns.value= Value
msd.heading.addColumns.operator= Operator

msd.tooltip.addColumns.table= Table
msd.tooltip.addColumns.column= Column
msd.tooltip.addColumns.label= Label
msd.tooltip.addColumns.sequence= Sequence
msd.tooltip.addColumns.value= Value
msd.tooltip.addColumns.operator= Operator
button.msd.configure.label= Configure

button.msd.saveAs= Save as
button.tooltip.msd.saveAs= Save as
button.tooltip.msd.save= Save
button.tooltip.msd.addColumn= Add MSD additional column
button.tooltip.msd.deleteColumn= Delete MSD additional column
button.tooltip.msd.configure= Configure profile
msd.generalProfile.label= General Profile
msd.profileId.label= Profile ID
additionalColumns.alert.emptyValue= Please enter a valid value
additionalColumns.alert.emptyField= Please choose a valid
additionalColumns.alert.emptyLabel= Please enter a label
additionalColumns.alert.deleteColumn= Existing additional column will be removed.\n \u0020 Do you want to continue?
additionalColumns.alert.changeValues= Record with same Label is already exist
additionalColumns.alert.alreadyOpened= Please close opened additional columns screen first
additionalColumns.alertProfileSaved = The profile was successfully saved
additionalColumns.alertDeleteProfile = Are you sure you want to delete the profile?
additionalColumns.alertOverwriteProfile = Are you sure you want to overwrite this profile? 

msdAdditionalColumns.deleteProfileImageTooltip = Delete profile
msdAdditionalColumns.saveProfileImageTooltip = Save profile
msdAdditionalColumns.reloadProfileTooltip = Reload profile
msdAdditionalColumns.noneProfile=<None>
msdAdditionalColumns.profileComboTooltip=Choose a profile
msdAdditionalColumns.generalProfileCheckTooltip= Use general profile
msdAdditionalColumns.useAddColsCheckTooltip= Use additional columns to update MSD main grid

additionalColumns.label.table=Table
additionalColumns.label.column=Column
additionalColumns.label.colLabel=Label

table.account= Account
table.movement= Movement
table.movement.ext= Movement_Ext

#Mantis6455
label.roleBasedControl.facility= Facility
label.roleBasedControl.ReqAuth= Req.Auth?
label.roleBasedControl.ReqOthers= Auth. Others?

label.roleBasedControl.facility.tooltip= Facility Screen
label.roleBasedControl.ReqAuth.tooltip= Changes require authorisation by another user?
label.roleBasedControl.ReqOthers.tooltip= User can authorise another's change?

maintenanceEvent.alert.cannotBeAmended=This record cannot be amended because a pending change requires authorisation
maintenanceevent.summary.seearchbutton.label=Search
maintenanceevent.summary.seearchbutton.tooltip=Search the list of event based on the specified criteria
maintenanceevent.summary.dispalybutton.tooltip=Display Details of the selected Maintenenance Event
maintenanceevent.summary.dispalybutton.label=Display

maintenanceevent.summary.checkbox.pending=Show pending maintenance events
maintenanceevent.summary.checkbox.accepted=Show accepted maintenance events
maintenanceevent.summary.checkbox.rejected=Show rejected maintenance events
maintenanceevent.summary.dateselection.all=Show All maintenance events without date restriction
maintenanceevent.summary.dateselection.forDateSelection=Show All maintenance for a period
maintenanceevent.summary.dateselection.from=Show Events From Date
maintenanceevent.summary.dateselection.to=Show Events To Date
maintenanceevent.summary.userselection=Show Events related to User


maintenanceevent.details.button.viewinfacility.label=View In Facility
maintenanceevent.details.button.viewinfacility.tooltip=Launch Maintenance facility relevant to the maintenance event,  in 'View'/'Display-only' mode
maintenanceevent.details.button.amendwinfacility.label=Amend In Facility
maintenanceevent.details.button.amendwinfacility.tooltip=Launch Maintenance facility relevant to the maintenance event,  in 'Change' mode


maintenanceevent.details.button.accept.label=Accept
maintenanceevent.details.button.accept.tooltip=Accept the change
maintenanceevent.details.button.reject.label=Reject
maintenanceevent.details.button.reject.tooltip=Reject the change

maintenanceevent.details.button.amend.label=Amend
maintenanceevent.details.button.amend.tooltip=Amend the change

maintenanceevent.details.alert.areyousuretoaccept=Are you sure you want to accept the changes?
maintenanceevent.details.alert.areyousuretoreject=Are you sure you want to reject the changes?
maintenanceevent.details.alert.actionperfermored=The action is successfully performed
maintenanceevent.details.alert.actionneedauthorisation=This action needs second user authorisation

maintEvent.tooltip.maintEventId=Maint Event Id
maintEvent.tooltip.maintFacilityId=Maint Facility Id
maintEvent.tooltip.action=Action
maintEvent.tooltip.recordId=Record Id
maintEvent.tooltip.status=Status
maintEvent.tooltip.requestUser=Request User
maintEvent.tooltip.requestDate=Request Date
maintEvent.tooltip.authUser=Auth User
maintEvent.tooltip.authDate=Auth Date
maintEvent.tooltip.prevId=Prev Id
maintEvent.tooltip.nextId=Next Id

maintEvent.maintEventId=Maint Event Id
maintEvent.maintFacilityId=Facility
maintEvent.action=Type
maintEvent.recordId=Record
maintEvent.status=Status
maintEvent.requestUser=Requester
maintEvent.requestDate=Requested On
maintEvent.authUser=Authorisor
maintEvent.authDate=Authorised On
maintEvent.prevId=Prev Id
maintEvent.nextId=Next Id

#Mantis 6441
accountmaintenance.alreadyLinked=Other account is already linked to current account

#Mantis 6278
title.timeZoneOffset= Time Zone Offset
tooltip.timeZoneRegion= Time Zone Region
generalParams.timeZoneRegion.alert= Changing the time zone will not update system time-frame values already stored in the database.  This could introduce inconsistencies.

button.search.criteria= Criteria
tooltip.advanced= Configure Search Filter

#Mantis #6348
login.notification.lastSuccessfulLoginTime=Last Successful Login
login.notification.lastFailedLogin=Last Failed Login
login.notification.failedLoginAttempts=Failed Login Attempts
login.notification.lastFailedLoginIp=Last Failed Login IP
login.notification.tooltip.lastLoginIp=IP address of last successful login
login.notification.tooltip.lastFailedLoginIp=IP address of last failed login attempt


reports.submitR=Report

scheduledReportParams.tooltip.runDateEntity = Specify the entity whose time frame will be used to evaluate RUN_DATE

scheduledReportParams.runDateWorkdays = Working days only
scheduledReportParams.tooltip.runDateWorkdays = Check box to indicate that only working days should be used when evaluating RUN_DATE


balMaintenance.accountId=Account ID
balMaintenance.name=Name
balMaintenance.user=User ID
balMaintenance.balCurrencyCode=Balance Currency Code
balMaintenance.scenarioHighlighted=Scenario Highlighted
balMaintenance.forecastSODNegative=Forecast SOD Negative
balMaintenance.forecastSODAsString=Effective Forecast SOD
balMaintenance.forecastSODTypeAsString=Forecast SOD Type as String
balMaintenance.externalSODNegative=External SOD Negative
balMaintenance.externalSODAsString=Effective External SOD
balMaintenance.externalSODTypeAsString=External SOD Type
balMaintenance.reasonCode=Reason Code
balMaintenance.reasonDesc=Reason Description
balMaintenance.userNotes=User Notes
balMaintenance.inputDateAsString=Update Date
balMaintenance.inputTimeAsString=Input Time as String



criticalMvtUpdateMaint.title= Critical Movement Update Maintenance
CriticalPay.entity= Entity
CriticalPay.type= critical Payment Type
CriticalPay.status= Description
CriticalPay.category= Category
CriticalPay.order.in.categ= Order in Categ
CriticalPay.enabled= Enabled?
CriticalPay.ccy= Currency
CriticalPay.defaultExpectedTime=Default Epected time
criticalMvtUpdate.entityId= Entity
criticalMvtUpdate.criticalTypeId=Critical Type
criticalMvtUpdate.desc=Description
criticalMvtUpdate.workingDayOnly=Working Days Only
criticalMvtUpdate.scheduledTimeFrame=Scheduling Timeframe
criticalMvtUpdate.runSqlUpdate=Run SQL Update every
criticalMvtUpdate.unit=minutes
criticalMvtUpdate.activeBetween=Active Between
criticalMvtUpdate.and=and
criticalMvtUpdate.lastExec=Last Executed (system time):
criticalMvtUpdate.category=Category
criticalMvtUpdate.orderInCategory=Order in Category
criticalMvtUpdate.sumToTotal=Sum to Total
criticalMvtUpdate.sumToCateg=Sum to Category
criticalMvtUpdate.reportable=Reportable
criticalMvtUpdate.reportIndivPay=Report Individual Payments
criticalMvtUpdate.enableUpdateProcessing=Enable Update processing
criticalMvtUpdate.sqlUpdateQuery=SQL Update Query:
criticalMvtUpdate.updateMvt=UPDATE P_MOVEMENT
criticalMvtUpdate.set=SET
criticalMvtUpdate.where=WHERE
criticalMvtUpdate.toolTip.set=Enter the SET clause of an UPATE P_MOVEMENT SQL
criticalMvtUpdate.toolTip.where=Enter the WHERE clause of an SQL UPDATE'
criticalMvtUpdate.toolTip.entity=; Select entity ID
criticalMvtUpdate.toolTip.category=Select a Category
criticalMvtUpdate.toolTip.criticalType= Critical Payment Type ID 
criticalMvtUpdate.toolTip.desc=Description for Critical payment type
criticalMvtUpdate.toolTip.runSqlUpdate= Specify how frequently (in minutes) the SQL update should run
criticalMvtUpdate.toolTip.save=Save
criticalMvtUpdate.toolTip.cancel=Cancel
criticalMvtUpdate.toolTip.validateQuery=Click to validate the SQL is valid
criticalMvtUpdate.save=Save
criticalMvtUpdate.cancel=Cancel
criticalMvtUpdate.validateQuery=Validate Query
criticalMvtUpdate.toolTip.add=Add
criticalMvtUpdate.toolTip.change=Change
criticalMvtUpdate.toolTip.delete=Delete
criticalMvtUpdate.add=Add
criticalMvtUpdate.change=Change
criticalMvtUpdate.delete=Delete
criticalMvtUpdate.toolTip.startTime=Specify the time period in which the SQL update should run
criticalMvtUpdate.toolTip.endTime=Specify the time period in which the SQL update should run
criticalMvtUpdate.toolTip.orderInCategory=Order of display within the category (used in ILM reporting)
criticalMvtUpdate.radioEntity=Entity
criticalMvtUpdate.radioSystem=System
criticalMvtUpdate.toolTip.radioEntity= Evaluate scheduling in entity timeframe'
criticalMvtUpdate.toolTip.radioSystem= Evaluate scheduling in system timeframe
criticalMvtUpdate.toolTip.timeFrameGroup=Specify the timeframe to be used when scheduling the SQL update
criticalMvtUpdate.toolTip.workingDayOnlyCheck=Only run the SQL update on working days
criticalMvtUpdate.toolTip.sumToCategCheck=Check to contribute to category total (used in ILM reporting)
criticalMvtUpdate.toolTip.sumToTotalCheck=Check to contribute to grand total (used in ILM reporting)
criticalMvtUpdate.toolTip.reportableCheck=Check to be listed in ILM reporting
criticalMvtUpdate.toolTip.reportIndivPayCheck=Check to include individual payments (used in ILM reporting) 
criticalMvtUpdate.toolTip.enableUpdateProcessingCheck=Check box to enable cyclic running of an SQL update
alert.criticalPaymentType.whereClauseIsEmpty= Where clause should not be empty
criticalMvtUpdate.currencyId= Currency
criticalMvtUpdate.expectedTime= Default Expected Time
criticalMvtUpdate.tooltip.expectedTime= Please enter default expected time
criticalMvtUpdate.tooltip.currencyId= Please choose a currency
alert.criticalPaymentType.emptyExpectedTime= Please fill default Expected Time field
alert.criticalPaymentType.activeBetween = Please fill both Start/End time fields
alert.criticalPaymentType.ccyAlreadyExists= Record with same currency code is already exists
alert.criticalPaymentType.valueLessThanMin= At least 5 mins should be set
alert.criticalPaymentType.valueHigherThanMax= No more than 1440 should be set
alert.criticalPaymentType.updateQuery= An update query must be specified when Enable Process is checked
alert.criticalPaymentType.runSqlUpdateEvery= Run sql update every field should be filled when Enable Process is checked

tooltip.Critical.Mvt.delete= Delete



emailTemplateMaintenance.tooltip.templateId=Template Id
emailTemplateMaintenance.tooltip.description=Description
emailTemplateMaintenance.tooltip.subjectContent=Subject Content
emailTemplateMaintenance.tooltip.bodyContent=Body Content

emailTemplateMaintenance.templateId=Template Id
emailTemplateMaintenance.description=Description
emailTemplateMaintenance.subjectContent=Subject Content
emailTemplateMaintenance.bodyContent=Body Content
emailTemplateMaintenance.keywordsCombo=Keywords



otherEmail.emailAddress= Email Address
otherEmail.description=Description
otherEmail.send=Send
otherEmail.emailAddress.tooltip=Sort by email address
otherEmail.description.tooltip=Sort by description
otherEmail.send.tooltip=Send
confi.id= ID
userLog.date=Date
userLog.time=Time
userLog.user=User
userLog.item=Item
userLog.itemId=Item ID
userLog.action=Action

userLog.tooltip.date=Date
userLog.tooltip.time=Time
userLog.tooltip.user=User
userLog.tooltip.item=Item
userLog.tooltip.itemId=Item ID
userLog.tooltip.action=Action

label.to= To

multipleMvtActions.label.dataSource=Data Source
multipleMvtActions.label.total=Total
multipleMvtActions.label.selected=Selected
multipleMvtActions.label.bookLbl=Book
multipleMvtActions.label.ordInstLbl=Ordering Institution
multipleMvtActions.label.critPayTypeLbl=Critical payment Type
multipleMvtActions.label.counterPartyLbl=Counterparty ID
multipleMvtActions.label.expSettlLbl=Expected Settlement
multipleMvtActions.label.actualSettlLbl=Actual Settlement
multipleMvtActions.label.noteLbl=Note Text
multipleMvtActions.label.mvtIdLocationLbl=Movement ID location

multipleMvtActions.label.importButton=Import
multipleMvtActions.label.processButton=Process
multipleMvtActions.label.closeButton=Close
multipleMvtActions.label.xButton=X
multipleMvtActions.tooltip.dataSrcCombo=Please choose a data source
multipleMvtActions.tooltip.bookCombo=Please choose a bookCode

multipleMvtActions.addNoteRadio=Add a movement note only
multipleMvtActions.updateStsRadio=Update status(es)
multipleMvtActions.includedRadio=Included
multipleMvtActions.excludedRadio=Excluded
multipleMvtActions.notUpdateRadio=Do not update
multipleMvtActions.cancelledRadio=Cancelled
multipleMvtActions.yesRadio=Yes
multipleMvtActions.noRadio=No
multipleMvtActions.unmatchRadio=Unmatch
multipleMvtActions.reconcileRadio=Reconcile
multipleMvtActions.updateOtherRadio=Update other settlement details
multipleMvtActions.colNameRadio=Column Name
multipleMvtActions.colNumberRadio=Column Number

multipleMvtActions.dataDefFieldSet=Data Definition
multipleMvtActions.mvtTotalFieldSet=Movement Totals
multipleMvtActions.MvtsFieldSet=Movements
multipleMvtActions.actionFieldSet=Action (For all updates, matched movements will first be unmatched)
multipleMvtActions.noteFieldSet=Notes
multipleMvtActions.predictFieldSet=Predict Status
multipleMvtActions.externalFieldSet=External Status
multipleMvtActions.ilmFieldSet=ILM Fcast Status
multipleMvtActions.internalSttlmFieldSet=Internal Settlement

MultiMvtActions.title.window=Multiple Movement Action

multiMvtActions.movementId=MovementID
multiMvtActions.entity=Entity
multiMvtActions.ccy=Ccy
multiMvtActions.vdate=Vdate
multiMvtActions.account=Account
multiMvtActions.amount=Amount
multiMvtActions.sign=Sign
multiMvtActions.pred=Pred
multiMvtActions.ext=Ext
multiMvtActions.ilmFcast=ILM Fcast
multiMvtActions.status=Match Status
multiMvtActions.ref1=Ref1
multiMvtActions.ref2=Ref2
multiMvtActions.extraRef=ExtraRef
multiMvtActions.book=Book
multiMvtActions.matchId=MatchID
multiMvtActions.Source=Source
multiMvtActions.format=Format
multiMvtActions.bookCode=Book code
multiMvtActions.cparty=Cparty
multiMvtActions.ordInst=Ord Inst
multiMvtActions.expSettlement=Exp Settlement
multiMvtActions.actSettlement=Act Settlement
multiMvtActions.critPayType= Crit Pay Type

multiMvtActions.importFailed = Import failed
multipleMvtActions.importFile?= Import movements from a file?

role.fieldset.mvtInputUpdate=Movement input/update
label.manualInput= Manual input requires authorisation
label.allowMultiMvtUpdatesFromMsd=Allow multi-movement updates from Movement Summary
tooltip.allowMultiMvtUpdatesFromMsd=Allow the user to update up to <the specified Number> movements selected from the Movement Summary Display
tooltip.button.multiMvtUpdate= Perform multiple movement action with 2 or more selected movements

tooltip.update=Perform multiple movement action with 2 or more selected movements

alert.emptyMovementIdLocation= Please specify the movement ID location in your file
multipleMvtActions.confirmProcess=This action will affect <
multipleMvtActions.confirmProcess1=> movements and is not reversible. Are you sure?
multiMvtActions.processFailed= Process Failed
alert.movementIdNotInHeader= Entered movement ID not found in the excel
alert.movementIdNotFilled= Movement ID location not filled
multipleMvtActions.label.closeExitButton=Close and exit
multipleMvtActions.label.closeReturnButton=Close and return
multipleMvtActions.label.status=Status
multipleMvtActions.fieldset.process=Process
alert.invalidColumnPosition= Invalid column position
alert.invalidDataFound= No valid data found in the excel for the given Movement ID location