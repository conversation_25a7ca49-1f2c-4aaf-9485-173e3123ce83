
# -------------------------------------------------------------------------
# SECTION: Protocol
# -------------------------------------------------------------------------

# Outbound Transport Protocol in lowercase (e.g. smtp, http)
mail.transport.protocol=smtp

# Host Name of Mail Server
mail.protocol.host=smtp.gmail.com

# Port Number of Mail Server for specified protocol
# e.g. Port 25 for default SMTP
mail.protocol.port=587


# -------------------------------------------------------------------------
# SECTION: Authentication
# -------------------------------------------------------------------------

# User ID/name (sometimes in the form of an email address)
mail.user.email=predictswallow1

# Password: Uses the same encryption algorithm as Predict, so use the same
# tool to generate the password, e.g.
# =ENC(...) for standard encryption, or =ENC2(...) for advanced
mail.user.password=iqraqdwchoachyfk

# <AUTHOR> <EMAIL>)
mail.from.email=<EMAIL>
#Specifies the SSL protocols that will be enabled for SSL connections (e.g. TLSv1,TLSv1.1,TLSv1.2)
mail.protocol.ssl.protocols=
# Prefix for the subject of all mails sent
#mail.subject.prefix=MailSubjPrefix:


# Maximum total of active Mail connection to mail server default to 10
mail.maxactive.connection=8
# Maximum total of Idle Mail connection to mail server default to 5
#mail.maxidle.connection=7
# Maximum number of retries default to 5 
#mail.max.retry=10
#Initial delay in seconds default to 10s then the delay will be calculated based on 
#exponential backoff  = Math.pow(2, retryCount - 1) * baseDelaySeconds
#mail.base.delay=20
