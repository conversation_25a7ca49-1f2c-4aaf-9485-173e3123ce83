############################################################
# i18n settings for the Axis Web-Application
#

#################### [i18n global setting] #################
#
locales=en ja

#################### [index.jsp] ###########################
#

### Header ###
#
language=Language
welcomeMessage=Hello! Welcome to Apache-Axis.

### Operation list ###
#
operationType=What do you want to do today?

# Validation
validation=Validation
validationURL=happyaxis.jsp
validationFootnote00=Validate the local installation's configuration
validationFootnote01=see below if this does not work.

# List
serviceList=List
serviceListURL=servlet/AxisServlet
serviceListFootnote=View the list of deployed Web services 

# Call
callAnEndpoint=Call
callAnEndpointURL=EchoHeaders.jws?method=list
callAnEndpointFootnote00=Call a local endpoint that list's the caller's http headers
callAnEndpointFootnote01=(or see its <a href="EchoHeaders.jws?wsdl">WSDL</a>).

# Visit
visit=Visit
visitURL=http://ws.apache.org/axis/
visitFootnote=Visit the Apache-Axis Home Page

# Admin
admin=Administer Axis
adminURL=servlet/AdminServlet
adminFootnote=[disabled by default for security reasons]

# SOAPMonitor
soapMonitor=SOAPMonitor 
soapMonitorURL=SOAPMonitor
soapMonitorFootnote=[disabled by default for security reasons]

# Sidenote
sideNote=To enable the disabled features, uncomment the appropriate declarations in WEB-INF/web.xml in the webapplication and restart it. 

### Validating Axis ###
#

# Title
validatingAxis=Validating Axis

# Note 0
validationNote00=If the "happyaxis" validation page displays an exception instead of a status page, the likely cause is that you have multiple XML parsers in your classpath. Clean up your classpath by eliminating extraneous parsers.

# Note 1
validationNote01=If you have problems getting Axis to work, consult the Axis <a href="http://wiki.apache.org/ws/FrontPage/Axis">Wiki</a> and then try the Axis user mailing list. 

#
#################### [index.jsp] ###########################

#################### [happyaxis.jsp] #######################
#

pageTitle=Axis Happiness Page
pageRole=Examining webapp configuration

### Needed Components ###
#
neededComponents=Needed Components
error=Error
warning=Warning
criticalErrorMessage=Axis will not work.
uncertainErrorMessage=Axis may not work.
# parameters = url, name
seeHomepage=<br> See <a href="{0}">{0}</a>
# parameters = category, classname, jarFile, errorText, url
couldNotFound=<p> {0}: could not find class {1} from file <b>{2}</b><br> {3} {4}<p>
# parameters = description, classname
foundClass00=Found {0} ( {1} )
# parameters = description, classname
foundClass01=Found {0} ( {1} ) at {2}
# parameters = category, classname, errorText, url
couldNotFoundDep=<p> {0}: could not find a dependency of class {1} from file <b>{2}</b><br> {3} {4}
# parameters = ncdfe.getMessage(), classname
theRootCause=<br>The root cause was: {0}<br>This can happen e.g. if {1} is in the 'common' classpath, but a dependency like activation.jar is only in the webapp classpath.<p>
# parameters = location
invalidSAAJ=<b>Error:</b> Invalid version of SAAJ API found in {0}. Make sure that Axis' saaj.jar precedes {0} in CLASSPATH.<br>
axisInstallation=Axis installation instructions

### Optional Components ###
#

optionalComponents=Optional Components
attachmentsError=Attachments will not work.
xmlSecurityError=XML Security is not supported.
httpsError=https is not supported.

happyResult00=<i>The core axis libraries are present.</i>
happyResult01=<i>The optional components are present.</i>
# parameters = needed(num of missing libraries)
unhappyResult00=<i>{0} core axis library(ies) are missing</i>
# parameters = wanted(num of missing libraries)
unhappyResult01=<i>{0} wanted optional axis librar(ies) are missing</i>

hintString=<B><I>Note:</I></B> On Tomcat 4.x and Java1.4, you may need to put libraries that contain java.* or javax.* packages into CATALINA_HOME/common/lib <br>jaxrpc.jar and saaj.jar are two such libraries.<p/>
noteString=<B><I>Note:</I></B> Even if everything this page probes for is present, there is no guarantee your web service will work, because there are many configuration options that we do not check for. These tests are <i>necessary</i> but not <i>sufficient</i><hr>

### Examining Application Server ###
#
apsExamining=Examining Application Server
recommendedParser=<b>We recommend <a href="http://xml.apache.org/xerces2-j/">Xerces 2</a> over Crimson as the XML parser for Axis</b>
couldNotCreateParser=Could not create an XML Parser

### Examining System Properties ###
#
sysExamining=Examining System Properties
sysPropError=System properties are not accessible.<p>
classFoundError=an unknown location
apsPlatform=Platform

#
#################### [happyaxis.jsp] #######################