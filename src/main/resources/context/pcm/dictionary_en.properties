# ----------  generic label --------------------
label.moreItems = ...
label.entity = Entity
label.date= Date
label.Currency= Currency
label.genericException=An error is occurred, Please contact Administrator
label.fourEyes=Four Eyes Validation
label.selectAll=Select All
label.pdf=PDF
label.excel=Excel
label.csv=CSV
label.to=To
label.valueDate = Value Date

# ----------  generic buttons --------------------
button.cancel= Cancel
button.close= Close
button.save= Save
button.add= Add
button.change = Change
button.view = View
button.delete = Delete
button.ok = OK
button.reset= Reset
button.undo = Undo
button.replace = Replace
button.refresh = Refresh
button.rate = Rate
button.options = Options
button.report = Report
button.search=Search
button.release=Release
button.spread=Spread
button.unstop=Unstop
button.Category=category
button.log=Log
button.message=Message
button.response=Response
button.stopped=Stopped
button.retry=Retry
button.stopped=Stopped
button.help=Help


# ----------  generic alert --------------------
alert.delete = Do you wish to delete this row?
alert.mandatoryField = Please fill all mandatory fields (marked with '*')
alert.yes.label = Yes
alert.no.label = No
alert.validTime = Please enter a valid time
alert.TimeAlreadyExist = Time already exists
alert.validAmount = Please enter a valid amount
alert.replaceQuery = Do you want to replace all constraints or only change values for existing constraints?
alert.likeOperator =  added without %. Do you wish to edit?
alert.generic_exception = Generic exception error
# ----------  generic error --------------------
error.contactAdmin=Please contact your System Administrator...
error.unexpectedError=An unexpected error has occurred.
errors.DataIntegrityViolationExceptioninAdd=Record already exists
errors.DataIntegrityViolationException=A data integrity violation occurred
errors.DataIntegrityViolationExceptioninDelete=Record cannot be deleted as other transactions depend on it

# ----------  generic tooltip --------------------
tooltip.cancel=Cancel changes and exit
tooltip.close=Close window
tooltip.save=Save changes and exit
tooltip.add=Add new record
tooltip.change=Change selected record
tooltip.view= Display selected record
tooltip.delete=Delete selected record
tooltip.ok=Save changes and exit
tooltip.undo = Cancel changes
tooltip.replace = Replace
tooltip.saveRefresh=Save refresh rate
tooltip.refresh = Refresh window
tooltip.options = Change Options
tooltip.print =print
tooltip.help =Help
tooltip.pdf = pdf
tooltip.csv = csv
tooltip.excel = excel
tooltip.from=From
tooltip.to=To
tooltip.rate = Rate window


# ---------- spreadProfilesMaintenance --------------------
spreadProfilesMaintenance.processPoint.All=All
spreadProfilesMaintenance.processPoint.Only=Only
spreadProfilesMaintenance.processPoint.ExceptAll=All Except

# ---------- stopRuleMaintenance --------------------
stopRuleMaintenance.title.window = Stop Rule Maintenance
stopRuleMaintenanceDetails.title.window = Stop Rule Details
stopRuleMaintenance.title.window = Stop Rule Maintenance
stopRuleMaintenance.title.window = Stop Rule Details
stopRuleMaintenance.label.statusGroup = Status
stopRuleMaintenance.label.activeRadioButton = Active
stopRuleMaintenance.label.inactiveRadioButton = Inactive
stopRuleMaintenance.label.bothRadioButton = Both
stopRuleMaintenance.activateButton.label = Activate
stopRuleMaintenance.deactivateButton.label = Deactivate
alert.stopRuleMaintenance.ProcessRunning = Stop rule Process is running on the selected Rule , the selected Rule cannot be deleted or deactivated for the moment
alert.stopRuleMaintenance.recordDepend = Cannot delete a STOP rule that has previously stopped a Payment Request
alert.stopRuleMaintenance.errorOcurred = Error occurred, Please contact your System Administrator
alert.stopRuleMaintenance.deactivatePayment = When deactivated the attached Payments will be
alert.stopRuleMaintenance.wantContinue = . Do you want to continue?
alert.stopRuleMaintenance.deleteStopRule =Cannot delete Stop Rule: Stop Rule has stopped Payment Request(s).

stopRuleMaintenanceDetails.ruleId.label = Stop Rule ID*
stopRuleMaintenanceDetails.ruleId.tooltip = Enter a unique ID for the STOP Rule
stopRuleMaintenanceDetails.ruleName.label = Stop Rule Name*
stopRuleMaintenanceDetails.ruleName.tooltip = Enter the STOP Rule Name
stopRuleMaintenanceDetails.active.label = Active
stopRuleMaintenanceDetails.active.tooltip = Select whether Rule is Active or not
stopRuleMaintenanceDetails.rulesActionOnDeactivation.label = Action On Deactivation
stopRuleMaintenanceDetails.radioSetWaiting.label = Set To Initial Status
stopRuleMaintenanceDetails.radioLeaveStoped.label =Leave Stopped
stopRuleMaintenanceDetails.rulesType.label = Type
stopRuleMaintenanceDetails.radioquickExpression.label = Quick Expression
stopRuleMaintenanceDetails.radioAdvancedExpression.label = Advanced Expression
stopRuleMaintenanceDetails.valueDates.label = Apply to Value Dates
stopRuleMaintenanceDetails.from.label = From
stopRuleMaintenanceDetails.from.tooltip = Enter start date (if applicable)
stopRuleMaintenanceDetails.to.label = To
stopRuleMaintenanceDetails.to.tooltip = Enter end date (if applicable)
stopRuleMaintenanceDetails.stopAllCheckbox.tooltip= Click if all Payment Requests are to be stopped
stopRuleMaintenanceDetails.stopAllCheckbox.label = Stop ALL
stopRuleMaintenanceDetails.quickExpressionPanel.title = Quick Expression
stopRuleMaintenanceDetails.currency.label = Currency
stopRuleMaintenanceDetails.currency.toolTip = Select for Currency
stopRuleMaintenanceDetails.amountOperatorCombo.label = Amount
stopRuleMaintenanceDetails.amountOperatorCombo.toolTip = Select for Amount Operator
stopRuleMaintenanceDetails.amountOperatorText.toolTip = Enter an Amount
stopRuleMaintenanceDetails.country.label = Country
stopRuleMaintenanceDetails.country.toolTip = Select for Country
stopRuleMaintenanceDetails.partyBic.label = Party BIC
stopRuleMaintenanceDetails.partyBic.toolTip = Enter an Party BIC
stopRuleMaintenanceDetails.source.label = Source
stopRuleMaintenanceDetails.source.toolTip = Select for Payment Source
stopRuleMaintenanceDetails.messageType.label = Message Type
stopRuleMaintenanceDetails.messageType.toolTip = Select for Message Type
stopRuleMaintenanceDetails.accountGroup.label = Account Group
stopRuleMaintenanceDetails.accountGroup.toolTip = Select for Account Group
stopRuleMaintenanceDetails.ruleTextPanel.title = Rule Text
stopRuleMaintenanceDetails.ruleExpression.button.label= Rule Builder
stopRuleMaintenanceDetails.activationInfoPanel.title = Activation Info
stopRuleMaintenanceDetails.activated.label= Activated At 
stopRuleMaintenanceDetails.by.label= By
stopRuleMaintenanceDetails.deactivated.label = De-Activated At
# ---------- queryBuilder --------------------
queryBuilderScreen.title.window = Query Builder
queryBuilderScreen.panel.search = Search Criteria
queryBuilderScreen.label.filter = Filter
queryBuilderScreen.panel.values = Values
queryBuilderScreen.panel.rule = Rule to add
queryBuilderScreen.alert.unexprectedError = An unexpected error has occurred.  Please call support
queryBuilderScreen.alert.missingProperty = Missing value for property
queryBuilderScreen.alert.checkEnteredValue = Please check entered value
queryBuilderScreen.alert.dateTimeFormat = Please Enter date time in the format
queryBuilderScreen.alert.dateFormat = Please Enter date in the format
queryBuilderScreen.alert.invalidQuery = Invalid Query <br> Reason

# ---------- CurrencyMaintenance --------------------
currencyMaintenance.title.window = Currency Maintenance
currencyMaintenanceDetails.title.window = Currency Maintenance Details
currencyMaintenanceDetails.reorder.alert = This order already exists with another currency, do you wish to reorder?
currencyMaintenanceDetails.amount.alert = Please enter a valid amount
currencyMaintenanceDetails.label.ccy = Currency*
currencyMaintenanceDetails.tooltip.ccy = Enter the Currency Code
currencyMaintenanceDetails.label.name = Currency Name
currencyMaintenanceDetails.label.ordinal = Ordinal
currencyMaintenanceDetails.tooltip.ordinal = Enter the Processing Order for the Currency
currencyMaintenanceDetails.label.multiplier = Multiplier
currencyMaintenanceDetails.tooltip.multiplier = Select the appropriate multiplier from the dropdown list
currencyMaintenanceDetails.label.largeAmount = Large Amount Threshold
currencyMaintenanceDetails.tooltip.largeAmount = Enter the Amount above which Payment Requests are considered \u2018Large\u2019
currencyMaintenanceDetails.notSaved.alert = Currency could not be saved successfully.<br>Please contact your System Administrator!
# ---------- CategoryMaintenance --------------------
categoryMaintenance.title.window = Payment Categories Maintenance
categoryMaintenanceDetails.title.window =Payment Category Details
categoryMaintenanceDetails.label.id = Category ID*
categoryMaintenanceDetails.tooltip.id = Enter an ID for this Category
categoryMaintenanceDetails.label.name = Name*
categoryMaintenanceDetails.tooltip.name = Enter the Category Name
categoryMaintenanceDetails.label.active = Active
categoryMaintenanceDetails.tooltip.active = If Active, click the box
categoryMaintenanceDetails.label.order = Processing Order*
categoryMaintenanceDetails.label.ruleAssignmentPriority = Rule Assignment Priority*
categoryMaintenanceDetails.tooltip.order = Enter the processing order
categoryMaintenanceDetails.tooltip.ruleAssignmentPriority = Enter the rule assignment priority
categoryMaintenanceDetails.label.type = Type
categoryMaintenanceDetails.tooltip.type = Click whether the Category is Urgent or Spread
categoryMaintenanceDetails.label.urgent = Urgent
categoryMaintenanceDetails.label.spreadable = Spreadable
categoryMaintenanceDetails.label.releaseTime = Release time
categoryMaintenanceDetails.tooltip.releaseTime = Click for how to process timed payment requests
categoryMaintenanceDetails.label.kickOff = Kick-off
categoryMaintenanceDetails.label.froPaymentRequest = From Payment Request
categoryMaintenanceDetails.label.immediate = Immediate
categoryMaintenanceDetails.label.specificTime = Specific time
categoryMaintenanceDetails.tooltip.time = Time
categoryMaintenanceDetails.label.daysOffset = Release value date offset
categoryMaintenanceDetails.tooltip.daysOffset  = Number of days ahead of value date to release
categoryMaintenanceDetails.label.assignmentMethod = Assignment Method
categoryMaintenanceDetails.tooltip.assignmentMethod = Click for Manual, System or Both who can use this Category
categoryMaintenanceDetails.label.manual = Manual
categoryMaintenanceDetails.label.system = System
categoryMaintenanceDetails.label.both = Both
categoryMaintenanceDetails.label.liqCheck = Use Liquidity Check
categoryMaintenanceDetails.tooltip.liqCheck = Click if Liquidity Check is required
categoryMaintenanceDetails.label.target = Include in Spreading Release Target
categoryMaintenanceDetails.tooltip.target = Click if the Payment Requests are to be included in the spreading release
categoryMaintenanceDetails.label.incAvailLiq = Include in avail. Liquidity
categoryMaintenanceDetails.tooltip.incAvailLiq = Click where Payment Requests are included in the debit calculation for balance determination
categoryMaintenanceDetails.rulePanel.title = Category Rules
categoryMaintenanceDetails.spreadPanel.title = Associated Spread Profiles
alert.contactAdminForcategoryMaintenance = Category could not be saved successfully.<br>Please contact your System Administrator!
alert.CategoryMaintenanceAddRulePriority = An other category has the same rule assignment priority. Do you want to continue?

# ---------- CategoryRuleMaintenance --------------------
categoryRuleDetails.title.window = Category Rule Details
categoryRuleDetails.label.name = Name*
categoryRuleDetails.tooltip.name = Enter the Rule Name
categoryRuleDetails.label.order = Processing Order*
categoryRuleDetails.tooltip.order = Enter the processing order for this Rule
categoryRuleDetails.label.source = Apply to Source
categoryRuleDetails.tooltip.source = If this Rule only applies to a specific Source then select from the dropdown list
categoryRuleDetails.label.entity = Apply to Entity
categoryRuleDetails.tooltip.entity = If this Rule only applies to a specific Entity then select from the dropdown list
categoryRuleDetails.label.ccy = Apply to Ccy
categoryRuleDetails.tooltip.ccy = If this Rule only applies to a specific Currency then select from the dropdown list
categoryRuleDetails.rule.expression.title = Payment Category Rule Expression
categoryRuleDetails.rule.expression.button.label = Rule Builder
errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd = Category rule record with Same name already exists
alert.CategoryRuleNameCannotEmpty = Category Rule Name cannot be empty!
alert.CategoryRuleReorder = This order already exists with another Category rule, do you wish to reorder?

# ---------- Dashboard --------------------
dashboard.title.window = PCM Monitor - SMART-Predict
dashboard.refreshRate.alert = Enter a valid refresh rate
dashboard.lastRefresh.label = Last Refresh:
dashboard.selected.label = Selected
dashboard.entity.tooltip = select an entity ID
dashboard.currencyThreshold.label = Apply currency threshold
dashboard.currencyThreshold.tooltip = Apply currency threshold
dashboard.currencyMultiplier.label = Apply currency multiplier
dashboard.currencyMultiplier.tooltip = Apply currency multiplier
dashboard.spreadOnly.label = Spread Only
dashboard.spreadOnly.tooltip = Select whether display the totals for Spread only
dashboard.displayValue.label = Value/Volume
dashboard.displayValue.tooltip = This will Toggle between the Value or Volume of PR\u2019s for each cell.
dashboard.radioValue.label = Value
dashboard.radioVolume.label = Volume


# ---------- Dashboard Details --------------------
dashboardDetails.title.window = PCM Breakdown Monitor - SMART-Predict
dashboardDetails.currency.label = Currency
dashboardDetails.currency.tooltip = select currency code
dashboardDetails.entity.label = Entity
dashboardDetails.entity.tooltip = select entity
dashboardDetails.accountGroup.label = Account Group
dashboardDetails.accountGroup.tooltip = select account group
dashboardDetails.account.label = Account
dashboardDetails.account.tooltip = select account
dashboardDetails.status.label = Status
dashboardDetails.status.tooltip = select status
dashboardDetails.currencyThreshold.label = Apply Ccy Threshold
dashboardDetails.currencyMultiplier.label = Apply Ccy Multiplier
dashboardDetails.timeFrame.label= Time-frame:
dashboardDetails.radioCurrency.label= Currency
dashboardDetails.radioEntity.label= Entity
dashboardDetails.radiosystem.label= System
dashboardDetails.startDayBalance.label= Start of Day Balance
dashboardDetails.confirmedCredits.label= Confirmed Credits
dashboardDetails.creditLine.label= Credit Line
dashboardDetails.releasedPayments.label= Released Payments
dashboardDetails.otherPayments.label= Other Payments
dashboardDetails.availableLiquidityEx.label= Available Liquidity (ex Credit Line)
dashboardDetails.availableLiquidityInc.label= Available Liquidity (inc Credit Line)
dashboardDetails.reserve.label= Reserve
dashboardDetails.buttonDisplay.label = Display Payment
dashboardDetails.buttonDisplay.tooltip = Display Payment
dashboardDetails.buttonRelease.label = Release Payment
dashboardDetails.buttonRelease.tooltip = Release Payment
dashboardDetails.spreadDisplay.label = Spread Display
dashboardDetails.spreadDisplay.tooltip = Spread Display
dashboardDetails.buttonRelease.label = Release Payment
dashboardDetails.buttonRelease.tooltip = Release Payment
dashboardDetails.unStopButton.label = Unstop
dashboardDetails.unStopButton.tooltip = Unstop
dashboardDetails.changeCatButton.label = Change Category
dashboardDetails.changeCatButton.tooltip = Change Category
dashboardDetails.blocked.label = Blocked
dashboardDetails.blocked.tooltip = Select blocked 
dashboardDetails.date.label = Value Date
dashboardDetails.date.tooltip = Enter value Date 
dashboardDetails.inputSince.label = Input Since
dashboardDetails.inputSince.tooltip = Enter Input Since
dashboardDetails.alert.dateFormat = Please enter date in the format 
dashboardDetails.alert.releaseSelectedPay = Do you want to release the selected payment?
dashboardDetails.alert.releaseBlockedPay = This payment is blocked. Do you wish to release?
dashboardDetails.alert.unstopPay = Do you want to unstop the selected payment?
dashboardDetails.alert.unstopPays = Do you want to unstop the selected payments?
dashboardDetails.alert.inputEngineError = Fail when sending response to the payment engine. Please check error logs for more details
dashboardDetails.alert.error = An exception is raised
dashboardDetails.title.errorPay = Payments Errors
dashboardDetails.alert.lockedPay = This payment is locked
dashboardDetails.title.releaseConfirmation = Release Confirmation
dashboardDetails.alert.noSpread = The selected Account Group is not provided with Spread Profile ID
dashboardDetails.alert.noAccount = No account Group is displayed
dashboardDetails.genericError= Generic exception error
dashboardDetails.paymentStopped= This payment is stopped by
dashboardDetails.paymentWasStopped= This payment was stopped by 
dashboardDetails.until= until 
dashboardDetails.wishContinue= Do you wish to continue? 
dashboardDetails.stopProcessRun= Stop rule application in progress


# ---------- Account Groups Maintenance -----------------
acctGroupsMaintenance.title.window = Account Groups Maintenance - SMART-Predict
acctGroupsMaintenance.title.add = Account Groups Maintenance Add - SMART-Predict
acctGroupsMaintenance.title.change = Account Groups Maintenance Change - SMART-Predict
acctGroupsMaintenance.title.view = Account Groups Maintenance View - SMART-Predict
acctGroupsMaintenance.tooltip.add = Add new account group
acctGroupsMaintenance.tooltip.change = Change the selected account group
acctGroupsMaintenance.tooltip.view = View the selected account group
acctGroupsMaintenance.tooltip.delete = Delete the selected account group
acctGroupsMaintenanceDetails.label.accountGrpId = Account Group ID*
acctGroupsMaintenanceDetails.tooltip.accountGrpId = Enter a unique ID for the Account Group
acctGroupsMaintenanceDetails.label.accountGrpName = Account Group Name*
acctGroupsMaintenanceDetails.tooltip.accountGrpName = Enter a name to be associated with the Account Group
acctGroupsMaintenanceDetails.label.currency = Currency*
acctGroupsMaintenanceDetails.tooltip.currency = Enter Currency Code for the Account Group
acctGroupsMaintenanceDetails.label.processingOrder = Processing Order
acctGroupsMaintenanceDetails.tooltip.processingOrder = Enter the Processing Order of the Account Group within the Currency

#---------------Alerts in Account Groups Add-----------
alert.deleteAccountGroup = Are you sure you wish to delete the selected account group?
alert.changeCurrency = Review of Account Group settings recommended following currency change
alert.validateAccountId= Please enter a valid alphanumeric value, the underscore and the hypen characters are allowed
alert.accountIDExist = Account ID already exists
alert.validateAccountName = Only alphanumeric characters and the symbols ( !"#$%&'()*+,-/.:;<=>?@[]^_`{|}~ ) are accepted
alert.accountNameWithoutSpaces = Account Group Name can not be saved as just spaces
alert.zeroAccountsInclude = No account is included in this group
alert.noSpread = Are you sure you want to save this account group without a spread profile?
alert.eodMandatory = Please fill End of Intraday Release Phase
alert.orderHigherThanZero = Order must be higher than 0
alert.categoryMandatory = Quick Category or Default Category must be filled
alert.errorSavingActGrp = Error while Saving the account group
alert.contactAdminForActGrp = Account Group could not be saved successfully.<br>Please contact your System Administrator!
alert.cutOffRangeTime = Cut-off time must be during End of Day phase
alert.cutOffSupKickOff = CutOff time must be later or equal to Kick Off time
alert.processsupKickOff =  Kick-off time must be earlier or equal than process point time
alert.eodSupProcess = End of Intraday Release Phase time must be later than process point time
#---------Alerts in liquidity tab----------
alert.selectCurrency =Please select a currency

#--------Alerts in cut off tab--------
alert.eodSupKickOff = End of Intraday Release Phase time must be later than Kickoff time
alert.cobSupEod = COB time must be later or equal to End of Intraday Release Phase time
alert.cobSupKickOff = COB time must be later than Kick off time







# ---------- PCThresholdAmounts --------------------
thresholdAmounts.label.time = Time*
thresholdAmounts.tooltip.time = Enter the time
thresholdAmounts.label.reserve = Reserve*
thresholdAmounts.tooltip.reserve = Enter the Reserve amount
thresholdAmounts.label.creditLine = Use Credit Line
thresholdAmounts.tooltip.creditLine = If use of the Credit Limit when calculating the Reserve is required, click the box else leave blank
#-------Tabs-------------
tab.general = General
tab.cutOff = Cut-off
tab.spreading = Spreading
tab.liquidity = Liquidity

# ---------- PCSpreadingTab --------------------
spreadingTab.label.spreadProfile = Spread Profile
spreadingTab.tooltip.spreadProfile = Select a Spread Profile from the drop down list
spreadingTab.label.targetCalculation = Target Release Calculation Method
spreadingTab.tooltip.targetCalculation = Enter the method for the release calculation (All or Outstanding)


# ---------- PCLiquidityTab --------------------
liquidityTab.alert.selectCurrency = Please select currency
liquidityTab.error.changingRow = error while changing row
liquidityTab.title.addReserve = Add Reserve
liquidityTab.title.chnageReserve = Change Reserve


# ---------- PCGeneralTab --------------------
generalTab.label.quickCategory = Quick Category ID
generalTab.tooltip.quickCategory = Enter a quick Category to use for all accounts within this Account Group
generalTab.label.defaultCategory = Default Category ID
generalTab.tooltip.defaultCategory =  Enter a default Category to use when no other Category can be determined
generalTab.label.countIDNotInGroup = Accounts Not Assigned to a Group
generalTab.tooltip.countIDNotInGroup = Select Accounts to include in the Account Group
generalTab.tooltip.processingOrder  = Enter the Processing Order of the Account Group within the Currency
generalTab.toolTip.buttonMoveRight = Moves selected account(s) into the Account Group
generalTab.toolTip.buttonMoveAllRight = Moves all displayed accounts into the Account Group
generalTab.toolTip.buttonMoveLeft = Moves selected account(s) out of the Account Group
generalTab.toolTip.buttonMoveAllLeft = Moves all displayed accounts out of the Account Group
generalTab.label.countIDInGroup = Accounts in this Account Group
generalTab.tooltip.countIDInGroup  = Select Accounts to remove from this Account Group
generalTab.label.filterText = Quick Search
generalTab.tooltip.filterText  = Enter text to help find the account(s)
generalTab.alert.orderZero = Order must be higher than 0
generalTab.alert.existOrder =This order already exists with the selected currency
generalTab.error.changingRow = error while changing row
generalTab.error.movingLeft = error in Move left

#--------CutOffTab---------
cutOffTab.label.kickOff = Kick-off (Ccy time)
cutOffTab.tooltip.kickOff = Enter the Kick-off time for the Account Group
cutOffTab.label.intradayReleasePhase = End of Intraday Release Phase (Ccy time)
cutOffTab.tooltip.intradayReleasePhase =Enter the time of End of Intraday Release Phase
cutOffTab.label.cob = Close of Business (Ccy time)*
cutOffTab.tooltip.cob = Enter the time for Cutoff for this Account Group
cutOff.label.intraday = Intraday Release Phase
cutOff.label.endOfDay = End of Day Phase
cutOff.title.addRule = Cut-off Add Rule
cutOff.alert.time = please validate time fields
cutOff.title.changeRule = Cut-off Change Rule
cutOff.title.viewRule = Cut-off View Rule
cutOff.alert.time = please validate time fields
cutOff.alert.deleteRow = Do you wish to delete this row?

#--------CutOffBuildQuery---------
cutOff.label.testOrder = Test Order*
cutOff.tooltip.testOrder =Enter the Processing Order for the Rule being created
cutOff.label.logText = Log Text
cutOff.tooltip.logText = Enter the Log Text
cutOff.label.cutOffTime = Cut-off Time*
cutOff.tooltip.cutOffTime = Enter the Cut-off Time for the Rule
cutOff.label.cutOffExpressionBuilder = Cut-off Expression Rule*
cutOff.rule.expression.button.label = Rule Builder
cutOff.rule.expression.button.tooltip = Create the Rule
cutOff.alert.testOrder = Test order must be unique


# ---------- Spread Profiles --------------------
spreadProfiles.alert.UnableToDelete=Unable to delete, this spread profile is linked to an existing account group
spreadProfiles.alert.ConfirmDelete=Are you sure to delete selected spread profile ID ? : \n
spreadProfilesAdd.title.processPointsPanel=Spread Process Points
spreadProfilesAdd.title.accountGroupsPanel=Associated Account Groups
spreadProfilesAdd.label.spreadId=Spread Id
spreadProfilesAdd.label.SpreadName=Spread Name
spreadProfilesAdd.label.currency=Currency
spreadProfilesAdd.tooltip.spreadId=Spread Id
spreadProfilesAdd.tooltip.SpreadName=Spread Name
spreadProfilesAdd.tooltip.currency=Currency
spreadProfilesAdd.alert.SpreadProfileWithSpaces=Spread Profile Name can not be saved as just spaces
spreadProfilesAdd.alert.SpreadProfileWithoutProcess=Cannot add a spread profile without any process point
spreadProfilesAdd.alert.SpreadAlreadyExists=Unable to save, spread Profile Id already exists
spreadProfilesAdd.alert.TimeOutsideOfRange=Could not add a spread process point with time outside <br> kick-off and End of Intraday Release Phase for the following account groups : <br>
spreadDetails.add.title=Spread Process Points - Add
spreadDetails.change.title=Spread Process Points - Change
spreadDetails.view.title=Spread Process Points - View
spreadDetails.label.processName=Process Name
spreadDetails.label.time=Time
spreadDetails.label.target=Target %
spreadDetails.label.processCategory=Process Category
spreadDetails.label.AllExceptSelected=Please select all categories which you do NOT wish to be processed
spreadDetails.label.onlySelected=Please select all categories which you wish to be processed
spreadDetails.tooltip.processName=Process Name
spreadDetails.tooltip.time=Time
spreadDetails.tooltip.target=Target %
spreadDetails.tooltip.processCategory=Select Process Category
spreadDetails.title.categoriesPanel=Categories
spreadDetails.alert.NameWithSpaces=Process Name can not be saved as just spaces
spreadDetails.alert.OnlyWithnoCategorySelected=Based on selected process, you need to choose at least one category
spreadDetails.alert.AllExceptWithAllCategoriesSelected=Based on selected process category, you need to exclude at least one category
spreadDetails.alert.sameTime=Cannot have two process points being defined with same time
spreadDetails.alert.sameName=Cannot have two process points being defined with same name

# ---------- PCM Report -------------------------
pcmReport.label.repoortType=Report Type
pcmReport.label.entity=Entity
pcmReport.label.currency=Currency
pcmReport.label.accountGroup=Account Group
pcmReport.label.source=Source
pcmReport.label.date=Date
pcmReport.label.date.singleDay=Single Day
pcmReport.label.date.dateRange=Date Range
pcmReport.label.useCcyMultiplier=Use Ccy Multiplier
pcmReport.label.exportType=Export type
pcmReport.tooltip.repoortType=Select Report Type
pcmReport.tooltip.entity=Select Entity
pcmReport.tooltip.currency=Select Currency Code
pcmReport.tooltip.accountGroup=Select Account Group
pcmReport.tooltip.source=Select Source
pcmReport.tooltip.date=Select Date
pcmReport.tooltip.date.singleDay=Single Day
pcmReport.tooltip.date.dateRange=Date Range
pcmReport.tooltip.useCcyMultiplier=Use Ccy Multiplier
pcmReport.tooltip.exportType=Export type

# ---------- CategoryList -------------------------
categoryList.label.paymentCategory = Payment Category
categoryList.tooltip.paymentCategory = Select payment category


# ---------- ConfirmRelease -------------------------
confirmRelease.label.paymentSelectedForRelease = payments are selected for release
confirmRelease.label.waiting = Waiting
confirmRelease.label.stopped =Stopped
confirmRelease.label.blocked = Blocked
confirmRelease.label.backValued = Input as Back Valued
confirmRelease.label.inputCutOff = Input after Cut-off
confirmRelease.label.stoppedCutOff = Stopped and cut-off passed
confirmRelease.label.cutOffPassed = Cut-off passed
confirmRelease.label.blockedStopped = Blocked and Stopped
confirmRelease.label.blockedPreviosulyStopped = Blocked and Previously Stopped
confirmRelease.label.cancelled =Cancelled (will not be released)
confirmRelease.label.releasedAlready = Released already
confirmRelease.buttonRelease.label = Release

# ---------- ErrorsUpdatePayments -------------------------
errorsUpdatePayments.releaseAllButton.label = Release All
errorsUpdatePayments.releaseButton.label = Release
errorsUpdatePayments.unStopButton.label = Unstop

# ---------- ExportPages -------------------------
exportPages.title = Pages to export
exportPages.label.current = Current Page
exportPages.label.allPages = All Pages

# ---------- FourEyesProcess -------------------------
fourEyesProcess.label.userName = Approver ID*
fourEyesProcess.label.password = Password*

# ---------- OptionsPopUp -------------------------
optionsPopUp.label.refresh = Auto-refresh rate
optionsPopUp.label.seconds = seconds


# ---------- PartySearch -------------------------
partySearch.label.partyId = Party Id
partySearch.tooltip.partyId = Party Id
partySearch.label.partyName = Party Name
partySearch.tooltip.partyName = Party Name
partySearch.label.searchButton = Search

# ---------- Payment Archive Search ----------------------
paymentArchiveSearch.label.archive=Archive
paymentArchiveSearch.label.amoutFrom=Amount From
paymentArchiveSearch.label.valueDateFrom=Value Date From
paymentArchiveSearch.label.entity=Entity
paymentArchiveSearch.label.currency=Currency
paymentArchiveSearch.label.messageFormat=Message Format
paymentArchiveSearch.label.source=Source
paymentArchiveSearch.label.category=Category
paymentArchiveSearch.label.sender=Sender
paymentArchiveSearch.label.receiver=Receiver
paymentArchiveSearch.label.orderingInst=Ordering Inst
paymentArchiveSearch.label.beneficiaryInst=Beneficiary Inst
paymentArchiveSearch.label.Account Group =Account Group
paymentArchiveSearch.label.Account =Account
paymentArchiveSearch.label.Input Date =Input Date
paymentArchiveSearch.label.Time From =Time From
paymentArchiveSearch.label.Reference =Reference
paymentArchiveSearch.label.Include =Include
paymentArchiveSearch.label.Exclude =Exclude
paymentArchiveSearch.label.Like =Like
paymentArchiveSearch.label.Source =Source  
paymentArchiveSearch.label.Front =Front
paymentArchiveSearch.label.Back =Back
paymentArchiveSearch.label.Payment =Payment 
paymentArchiveSearch.label.Related =Related
paymentArchiveSearch.label.beneficiaryInst=Beneficiary
paymentArchiveSearch.tooltip.archive=Select Archive Database
paymentArchiveSearch.tooltip.amoutFrom=Enter the From Amount
paymentArchiveSearch.tooltip.amoutTo=Enter the To Amount
paymentArchiveSearch.tooltip.valueDateFrom=Enter value Date From in {1}
paymentArchiveSearch.tooltip.valueDateTo=Enter value Date To in {1}
paymentArchiveSearch.tooltip.entity=Select Entity
paymentArchiveSearch.tooltip.currency=Select Currency Code
paymentArchiveSearch.tooltip.messageFormat=Select Message Format
paymentArchiveSearch.tooltip.source=Select Source
paymentArchiveSearch.tooltip.category=Select Category
paymentArchiveSearch.tooltip.sender=Select Sender
paymentArchiveSearch.tooltip.receiver=Select Receiver
paymentArchiveSearch.tooltip.orderingInst=Select Ordering Inst
paymentArchiveSearch.tooltip.beneficiaryInst=Select Beneficiary Inst
paymentArchiveSearch.tooltip.accountGroup=Select Account Group
paymentArchiveSearch.tooltip.account=Select Account
paymentArchiveSearch.tooltip.inputDate=From
paymentArchiveSearch.tooltip.timeFrom=From Time
paymentArchiveSearch.tooltip.timeTo=To Time
paymentArchiveSearch.tooltip.reference=Reference
paymentArchiveSearch.tooltip.include=Enter Include Value
paymentArchiveSearch.tooltip.exclude=Enter Exclude Value

paymentArchiveSearch.statusPanel.title=Status
paymentArchiveSearch.statusPanel.label.waitingRadio=Waiting
paymentArchiveSearch.statusPanel.label.blockedRadio=Blocked
paymentArchiveSearch.statusPanel.label.releasedRadio=Released
paymentArchiveSearch.statusPanel.label.cancelledRadio=Cancelled
paymentArchiveSearch.statusPanel.label.stoppedRadio=Stopped
paymentArchiveSearch.statusPanel.label.allRadio=All
paymentArchiveSearch.typePanel.title=Type
paymentArchiveSearch.typePanel.label.cashRadio=Cash
paymentArchiveSearch.typePanel.label.securitiesRadio=Securities
paymentArchiveSearch.typePanel.label.bothRadio=Both
paymentArchiveSearch.timeframePanel.title=Time-Frame
paymentArchiveSearch.timeframePanel.label.entityRadio=Entity
paymentArchiveSearch.timeframePanel.label.currencyRadio=Currency
paymentArchiveSearch.timeframePanel.label.systemRadio=System

paymentArchiveSearch.alert.endDateMustbeLater=End Date must be later than Start date
paymentArchiveSearch.alert.amountToMustbeLater=Amount To must be greater than Amount From


# ---------- Payment Request Display ---------------------
paymentRequestDisplay.tabs.title.additionalInfo=Add\u2019l Info
paymentRequestDisplay.tabs.title.parties=Parties  
paymentRequestDisplay.tabs.title.parties2=Parties2
paymentRequestDisplay.label.accountGroup =Account Group 
paymentRequestDisplay.label.paymentReq=Payment Req*
#--General Tab
paymentRequestDisplay.tabs.title.general=General 
paymentRequestDisplay.label.mainDetailsPanel=Main Details
paymentRequestDisplay.label.entity=Entity 
paymentRequestDisplay.label.currency=Currency 
paymentRequestDisplay.label.accountID=Account ID  
paymentRequestDisplay.label.type=Type 
paymentRequestDisplay.label.categoryID=Category ID 
paymentRequestDisplay.label.valueDate=Value Date 
paymentRequestDisplay.label.gPI=GPI
paymentRequestDisplay.label.paymentType=Payment Type
paymentRequestDisplay.label.cut-OffTime=Cut-Off Time
paymentRequestDisplay.label.urgentSpreadable=Urgent / Spreadable
paymentRequestDisplay.label.categoryRule=Category Rule
paymentRequestDisplay.label.amount=Amount
paymentRequestDisplay.label.paymentStatus=Payment Status
paymentRequestDisplay.label.paymentReferences=Payment References
paymentRequestDisplay.label.rELEASED=RELEASED
paymentRequestDisplay.label.by=By
paymentRequestDisplay.label.wAITING=WAITING
paymentRequestDisplay.label.requiredReleased=Required Released
paymentRequestDisplay.label.cancelled=Cancelled
paymentRequestDisplay.label.stopped=Stopped
paymentRequestDisplay.label.unstopped=Unstopped
paymentRequestDisplay.label.blocked=Blocked
paymentRequestDisplay.label.cancelled=Cancelled
paymentRequestDisplay.label.paymentRef=Payment Ref.
paymentRequestDisplay.label.relatedRef=Related Ref.
paymentRequestDisplay.label.sourceRef=Source Ref.
paymentRequestDisplay.label.fORef=FO Ref.
paymentRequestDisplay.label.bORef=BO Ref.
paymentRequestDisplay.label.reason=Reason 
paymentRequestDisplay.label.originalMessagePanel=Original Message
paymentRequestDisplay.label.source=Source
paymentRequestDisplay.label.messageType=Message Type
paymentRequestDisplay.label.message=Message
#--Additional info Tab
paymentRequestDisplay.label.Action=Action
paymentRequestDisplay.label.FrontOffice_Reference=Front Office Reference
paymentRequestDisplay.label.InputDate=Input Date
paymentRequestDisplay.label.Department=Department
paymentRequestDisplay.label.Source=Source
paymentRequestDisplay.label.SourceUrgencyIndicator=Source Urgency Indicator
paymentRequestDisplay.label.MessageType=Message Type
paymentRequestDisplay.label.SubEntityId=Sub Entity Id
paymentRequestDisplay.label.SenderReceiverInfo=Sender Receiver Info
paymentRequestDisplay.label.PaymentMessage=Payment Message
#--Parties
paymentRequestDisplay.label.senderBIC=Sender BIC
paymentRequestDisplay.label.receiverBIC=Receiver BIC
paymentRequestDisplay.label.orderingCustomerBIC=Ordering Customer BIC
paymentRequestDisplay.label.orderingCustomerAccount=Ordering Customer Account
paymentRequestDisplay.label.orderingCustomerName=Ordering Customer Name
paymentRequestDisplay.label.orderingInstitutionBIC=Ordering Institution BIC
paymentRequestDisplay.label.orderingInstitutionAccount=Ordering Institution Account
paymentRequestDisplay.label.orderingInstitutionName=Ordering Institution Name
paymentRequestDisplay.label.sendersCorrespondentBIC=Senders Correspondent BIC
paymentRequestDisplay.label.sendersCorrespondentAccount=Senders Correspondent Account
paymentRequestDisplay.label.sendersCorrespondentName=Senders Correspondent Name
paymentRequestDisplay.label.receiversCorrespondentBIC=Receivers Correspondent BIC
paymentRequestDisplay.label.receiversCorrespondentAccount=Receivers Correspondent Account
paymentRequestDisplay.label.receiversCorrespondentName=Receivers Correspondent Name
paymentRequestDisplay.label.intermediaryBIC=Intermediary BIC
paymentRequestDisplay.label.intermediaryAccount=Intermediary Account
paymentRequestDisplay.label.intermediaryName=Intermediary Name
paymentRequestDisplay.label.accountWithInstitutionBIC=Account With Institution BIC
paymentRequestDisplay.label.accountWithInstitutionAccount=Account With Institution Account
paymentRequestDisplay.label.accountWithInstitutionName=Account With Institution Name
paymentRequestDisplay.label.beneficiaryCustomerBIC=Beneficiary Customer BIC
paymentRequestDisplay.label.beneficiaryCustomerAccount=Beneficiary Customer Account
paymentRequestDisplay.label.beneficiaryCustomerName=Beneficiary Customer Name
#--Parties 2
paymentRequestDisplay.label.beneficiaryInstitutionBIC=Beneficiary Institution BIC
paymentRequestDisplay.label.beneficiaryInstitutionAccount=Beneficiary Institution Account
paymentRequestDisplay.label.beneficiaryInstitutionName=Beneficiary Institution Name
paymentRequestDisplay.label.sellerBIC=Seller BIC
paymentRequestDisplay.label.sellerAccount=Seller Account
paymentRequestDisplay.label.sellerName=Seller Name
paymentRequestDisplay.label.deliverersCustodianBIC=Deliverers Custodian BIC
paymentRequestDisplay.label.deliverersCustodianAccount=Deliverers Custodian Account
paymentRequestDisplay.label.deliverersCustodianName=Deliverers Custodian Name
paymentRequestDisplay.label.deliveryAgentBIC=Delivery Agent BIC
paymentRequestDisplay.label.deliveryAgentAccount=Delivery Agent Account
paymentRequestDisplay.label.deliveryAgentName=Delivery Agent Name
paymentRequestDisplay.label.placeOfSettlementBIC=Place Of Settlement BIC
paymentRequestDisplay.label.placeOfSettlementAccount=Place Of Settlement Account
paymentRequestDisplay.label.placeOfSettlementName=Place Of Settlement Name
paymentRequestDisplay.label.thirdReimbursementBIC=Third Reimbursement BIC
paymentRequestDisplay.label.thirdReimbursementAccount=Third Reimbursement Account
paymentRequestDisplay.label.thirdReimbursementName=Third Reimbursement Name
paymentRequestDisplay.label.timeFrame=TimeFrame
paymentRequestDisplay.label.EntityRadio=Entity
paymentRequestDisplay.label.currencyRadio=Currency
paymentRequestDisplay.label.systemRadio=System

# ---------- PC Message Details --------------------------
pcMessageDetails.title=Message Details