<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>
<configuration scan="true">
    <property name="LOG_FILE_SERVER" value="${jboss.server.log.dir:-./logback}/server.log"/>
    <property name="NOHUP_FILE" value="nohup.out"/>

    <!-- Include Spring Boot defaults, but we'll override them -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>

    <!-- Appender for nohup.out -->
    <appender name="NOHUP_FILE" class="ch.qos.logback.core.FileAppender">
        <file>${NOHUP_FILE}</file>
        <append>true</append>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Appender for server.log -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>${LOG_FILE_SERVER}</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Console appender -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Root logger set to ERROR, applying to all appenders -->
    <root level="ERROR">
        <appender-ref ref="FILE"/>
        <appender-ref ref="NOHUP_FILE"/>
        <appender-ref ref="STDOUT"/>
    </root>

    <!-- Explicitly set all specific loggers to ERROR to override any lower levels -->
    <logger name="javax.activation" level="ERROR"/>
    <logger name="org.swallow" level="ERROR"/>
    <logger name="javax.mail" level="ERROR"/>
    <logger name="javax.management.remote" level="ERROR"/>
    <logger name="javax.xml.bind" level="ERROR"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="com.ryantenney" level="ERROR"/>
    <logger name="com.sun" level="ERROR"/>
    <logger name="com.zaxxer" level="ERROR"/>
    <logger name="io.undertow" level="ERROR"/>
    <logger name="io.undertow.websockets.jsr" level="ERROR"/>
    <logger name="org.ehcache" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="ERROR"/>
    <logger name="org.hibernate.validator" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="org.hibernate.ejb.HibernatePersistence" level="OFF"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.springframework.web" level="ERROR"/>
    <logger name="org.springframework.security" level="ERROR"/>
    <logger name="org.springframework.cache" level="ERROR"/>
    <logger name="org.springframework.beans" level="ERROR"/>
    <logger name="org.thymeleaf" level="ERROR"/>
    <logger name="org.xnio" level="ERROR"/>
    <logger name="springfox" level="ERROR"/>
    <logger name="sun.rmi" level="ERROR"/>
    <logger name="liquibase" level="ERROR"/>
    <logger name="LiquibaseSchemaResolver" level="ERROR"/> <!-- Was INFO, now ERROR -->
    <logger name="springfox.documentation.schema.property" level="ERROR"/>
    <logger name="sun.rmi.transport" level="ERROR"/>
    <logger name="org.quartz" level="ERROR"/>
    <logger name="spring.jpa" level="ERROR"/>
    <logger name="stdout" level="ERROR"/>
    <logger name="org.wildfly.extension.undertow" level="ERROR"/>
    <logger name="org.jboss.as.server" level="ERROR"/>
    <logger name="org.jboss.as" level="ERROR"/>
    <logger name="com.arjuna.ats.jbossatx" level="ERROR"/>
    <logger name="com.opensymphony.xwork2.util.DomHelper" level="ERROR"/>
    <logger name="com.opensymphony.xwork2.spring.SpringObjectFactory" level="ERROR"/>
    <logger name="com.opensymphony.xwork2.ognl.SecurityMemberAccess" level="ERROR"/>
    <logger name="org.springframework.boot.autoconfigure.orm.jpa.JpaBaseConfiguration$JpaWebConfiguration" level="ERROR"/>
    <logger name="com.whirlycott.cache.CacheManager" level="ERROR"/>

    <!-- Shutdown hook and JUL reset -->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
</configuration>