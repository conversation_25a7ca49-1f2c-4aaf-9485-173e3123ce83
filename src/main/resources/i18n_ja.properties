############################################################
# Japanese settings for the Axis Web-Application
#

#################### [index.jsp] ###########################
#

### Header ###
#
language=\u8a00\u8a9e
welcomeMessage=\u3053\u3093\u306b\u3061\u306f\uff01 Apache-Axis\u3078\u3088\u3046\u3053\u305d 

### Operation list ###
#
operationType=\u4eca\u65e5\u306f\u4f55\u3092\u3057\u305f\u3044\u3067\u3059\u304b\uff1f

# Validation
validation=\u691c\u8a3c
validationURL=happyaxis.jsp
validationFootnote00=\u30ed\u30fc\u30ab\u30eb\u306e\u30a4\u30f3\u30b9\u30c8\u30fc\u30eb\u72b6\u6cc1\u3092\u691c\u8a3c\u3059\u308b
validationFootnote01=\u3046\u307e\u304f\u6a5f\u80fd\u3057\u306a\u3044\u5834\u5408\u306f\u3001\u4e0b\u8a18\u300cAxis\u306e\u691c\u8a3c\u300d\u3092\u53c2\u7167

# List
serviceList=\u30ea\u30b9\u30c8
serviceListURL=servlet/AxisServlet
serviceListFootnote=\u30c7\u30d7\u30ed\u30a4\u6e08\u307f\u306eWeb\u30b5\u30fc\u30d3\u30b9\u30ea\u30b9\u30c8\u3092\u898b\u308b 

# Call
callAnEndpoint=\u547c\u51fa\u3057
callAnEndpointURL=EchoHeaders.jws?method=list
callAnEndpointFootnote00=HTTP\u30d8\u30c3\u30c0\u306e\u4e00\u89a7\u8868\u793a\u3092\u884c\u3046\u30a8\u30f3\u30c9\u30dd\u30a4\u30f3\u30c8\u3092\u8d77\u52d5\u3059\u308b
callAnEndpointFootnote01=(\u3082\u3057\u304f\u306f<a href="EchoHeaders.jws?wsdl">WSDL</a>\u306e\u53c2\u7167)

# Visit
visit=\u8a2a\u554f
visitURL=http://ws.apache.org/axis/ja/index.html
visitFootnote=Apache Axis\u30db\u30fc\u30e0\u30da\u30fc\u30b8\u3092\u8a2a\u554f\u3059\u308b

# Admin
admin=Axis\u306e\u7ba1\u7406
adminURL=servlet/AdminServlet
adminFootnote=[\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u4e0a\u306e\u7406\u7531\u304b\u3089\u30c7\u30d5\u30a9\u30eb\u30c8\u3067\u306f\u5229\u7528\u4e0d\u53ef]

# SOAPMonitor
soapMonitor=SOAP\u30e2\u30cb\u30bf
soapMonitorURL=SOAPMonitor
soapMonitorFootnote=[\u30bb\u30ad\u30e5\u30ea\u30c6\u30a3\u4e0a\u306e\u7406\u7531\u304b\u3089\u30c7\u30d5\u30a9\u30eb\u30c8\u3067\u306f\u5229\u7528\u4e0d\u53ef]

# Sidenote
sideNote=\u4e0a\u8a18\u306e\u30c7\u30d5\u30a9\u30eb\u30c8\u3067\u5229\u7528\u3067\u304d\u306a\u3044\u6a5f\u80fd\u3092\u6709\u52b9\u306b\u3059\u308b\u306b\u306f\u3001web\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u5185\u306eWEB-INF/web.xml\u30d5\u30a1\u30a4\u30eb\u306e\u8a72\u5f53\u3059\u308b\u5ba3\u8a00\u306e\u30b3\u30e1\u30f3\u30c8\u3092\u5916\u3057\u3001\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u3092\u518d\u8d77\u52d5\u3057\u3066\u4e0b\u3055\u3044\u3002 

### Validating Axis ###
#

# Title
validatingAxis=Axis\u306e\u691c\u8a3c

# Note 0
validationNote00="happyaxis"\u691c\u8a3c\u30da\u30fc\u30b8\u304c\u72b6\u614b\u8868\u793a\u3067\u306f\u306a\u304f\u4f8b\u5916\u3092\u8868\u793a\u3059\u308b\u5834\u5408\u3001\u539f\u56e0\u3068\u3057\u3066\u306f\u30af\u30e9\u30b9\u30d1\u30b9\u5185\u306b\u8907\u6570\u306eXML\u30d1\u30fc\u30b5\u3092\u6307\u5b9a\u3057\u3066\u3044\u308b\u3053\u3068\u304c\u8003\u3048\u3089\u308c\u307e\u3059\u3002\u95a2\u4fc2\u306a\u3044\u30d1\u30fc\u30b5\u3092\u30af\u30e9\u30b9\u30d1\u30b9\u304b\u3089\u53d6\u308a\u9664\u3044\u3066\u307f\u3066\u4e0b\u3055\u3044\u3002 

# Note 1
validationNote01=Axis\u3092\u52d5\u4f5c\u3055\u305b\u308b\u4e0a\u3067\u554f\u984c\u3092\u62b1\u3048\u3066\u3044\u308b\u5834\u5408\u306f\u3001\u307e\u305a<a href="http://wiki.apache.org/ws/ja/axis">Axis Wiki</a>\u3092\u53c2\u8003\u306b\u3057\u3001\u305d\u306e\u5f8c\u3067Axis\u30e6\u30fc\u30b6\u30e1\u30fc\u30ea\u30f3\u30b0\u30ea\u30b9\u30c8\u306b\u6295\u7a3f\u3057\u3066\u307f\u3066\u4e0b\u3055\u3044\u3002 

#
#################### [index.jsp] ###########################

#################### [happyaxis.jsp] #######################
#
pageTitle=Axis Happiness Page
pageRole=webapp\u306e\u69cb\u6210\u306b\u95a2\u3059\u308b\u8abf\u67fb

### Needed Components ###
#
neededComponents=\u5fc5\u9808\u30b3\u30f3\u30dd\u30fc\u30cd\u30f3\u30c8
error=\u30a8\u30e9\u30fc
warning=\u8b66\u544a
criticalErrorMessage=\u304a\u305d\u3089\u304fAxis\u306f\u52d5\u304d\u307e\u305b\u3093\u3002
uncertainErrorMessage=Axis\u306f\u52d5\u304b\u306a\u3044\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059\u3002
# parameters = url, name
seeHomepage=<br> <a href="{0}">{0}</a>\u3092\u898b\u3066\u4e0b\u3055\u3044\u3002
# parameters = category, classname, jarFile, errorText, url
couldNotFound=<p> {0}: <b>{2}</b>\u30d5\u30a1\u30a4\u30eb\u304c\u63d0\u4f9b\u3059\u308b{1}\u30af\u30e9\u30b9\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093\u3002<br> {3} {4}<p>
# parameters = description, classname
foundClass00={0} ( {1} ) \u304c\u898b\u3064\u304b\u308a\u307e\u3057\u305f\u3002
# parameters = description, classname, location
foundClass01={0} ( {1} ) \u304c{2}\u3067\u898b\u3064\u304b\u308a\u307e\u3057\u305f\u3002
# parameters = category, classname, errorText, url
couldNotFoundDep=<p> {0}: <b>{2}</b>\u30d5\u30a1\u30a4\u30eb\u304c\u63d0\u4f9b\u3059\u308b{1}\u30af\u30e9\u30b9\u306e\u4f9d\u5b58\u95a2\u4fc2\u304c\u89e3\u6c7a\u3067\u304d\u307e\u305b\u3093\u3002<br> {3} {4}
# parameters = ncdfe.getMessage(), classname
theRootCause=<br>\u6839\u672c\u539f\u56e0: {0}<br>\u3053\u306e\u30a8\u30e9\u30fc\u306f\u6b21\u306e\u3088\u3046\u306a\u5834\u5408\u306b\u767a\u751f\u3059\u308b\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059\u3002\u300c\u5171\u901a\u306e\u300d\u30af\u30e9\u30b9\u30d1\u30b9\u306b{1}\u304c\u8a2d\u5b9a\u3055\u308c\u3066\u3044\u308b\u306b\u3082\u304b\u304b\u308f\u3089\u305a\u3001activation.jar \u306e\u3088\u3046\u306a\u4f9d\u5b58\u3059\u308b\u30e9\u30a4\u30d6\u30e9\u30ea\u304cwebapp\u306e\u30af\u30e9\u30b9\u30d1\u30b9\u3060\u3051\u306b\u3057\u304b\u8a2d\u5b9a\u3055\u308c\u3066\u3044\u306a\u3044\u3088\u3046\u306a\u5834\u5408\u3067\u3059\u3002<p>
# parameters = location
invalidSAAJ=<b>\u30a8\u30e9\u30fc:</b> {0}\u306b\u9069\u5207\u3067\u306a\u3044\u30d0\u30fc\u30b8\u30e7\u30f3\u306eSAAJ API\u304c\u898b\u3064\u304b\u308a\u307e\u3057\u305f\u3002Axis\u306esaaj.jar\u3092\u3001CLASSPATH\u306b\u8a2d\u5b9a\u3055\u308c\u3066\u3044\u308b{0} \u3088\u308a\u3082\u524d\u65b9\u306b\u8a2d\u5b9a\u3057\u3066\u304f\u3060\u3055\u3044\u3002<br>
axisInstallation=Axis\u30a4\u30f3\u30b9\u30c8\u30fc\u30eb\u624b\u9806

### Optional Components ###
#
optionalComponents=\u30aa\u30d7\u30b7\u30e7\u30ca\u30eb\uff65\u30b3\u30f3\u30dd\u30fc\u30cd\u30f3\u30c8
attachmentsError=\u304a\u305d\u3089\u304fAttachments\u306f\u6a5f\u80fd\u3057\u307e\u305b\u3093\u3002
xmlSecurityError=XML Security\u306f\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u307e\u305b\u3093\u3002
httpsError=https\u306f\u30b5\u30dd\u30fc\u30c8\u3055\u308c\u307e\u305b\u3093\u3002

happyResult00=<i>axis\u306e\u30b3\u30a2\uff65\u30e9\u30a4\u30d6\u30e9\u30ea\u306f\u5168\u3066\u5b58\u5728\u3057\u3066\u3044\u307e\u3059\u3002</i>
happyResult01=<i>\u30aa\u30d7\u30b7\u30e7\u30ca\u30eb\uff65\u30b3\u30f3\u30dd\u30fc\u30cd\u30f3\u30c8\u306f\u5b58\u5728\u3057\u3066\u3044\u307e\u3059\u3002</i>
# parameters = needed(num of missing libraries)
unhappyResult00=<i>axis\u306e\u30b3\u30a2\uff65\u30e9\u30a4\u30d6\u30e9\u30ea\u304c{0}\u3064\u6b20\u3051\u3066\u3044\u307e\u3059\u3002</i>
# parameters = wanted(num of missing libraries)
unhappyResult01=<i>axis\u306e\u30aa\u30d7\u30b7\u30e7\u30ca\u30eb\uff65\u30e9\u30a4\u30d6\u30e9\u30ea\u304c{0}\u3064\u6b20\u3051\u3066\u3044\u307e\u3059\u3002</i>

hintString=<B><I>\u6ce8\u610f:</I></B> Tomcat 4.x \u3068 Java1.4 \u4e0a\u3067\u306f\u3001CATALINA_HOME/common/lib \u306b\u3001java.* \u3082\u3057\u304f\u306f javax.* \u30d1\u30c3\u30b1\u30fc\u30b8\u3092\u542b\u3080\u30e9\u30a4\u30d6\u30e9\u30ea\u3092\u914d\u7f6e\u3059\u308b\u5fc5\u8981\u304c\u3042\u308b\u304b\u3082\u3057\u308c\u307e\u305b\u3093\u3002<br>\u4f8b\u3048\u3070 jaxrpc.jar \u3068 saaj.jar \u306f\u3001\u305d\u306e\u3088\u3046\u306a\u30e9\u30a4\u30d6\u30e9\u30ea\u3067\u3059\u3002<p/>
noteString=<B><I>\u6ce8\u610f:</I></B> \u30da\u30fc\u30b8\u306b\u5168\u3066\u306e\u8abf\u67fb\u7d50\u679c\u304c\u8868\u793a\u3055\u308c\u305f\u3068\u3057\u3066\u3082\u3001\u30c1\u30a7\u30c3\u30af\u3067\u304d\u306a\u3044\u69cb\u6210\u30aa\u30d7\u30b7\u30e7\u30f3\u3082\u591a\u3044\u305f\u3081\u3001\u3042\u306a\u305f\u306eWeb\u30b5\u30fc\u30d3\u30b9\u304c\u6b63\u5e38\u306b\u6a5f\u80fd\u3059\u308b\u4fdd\u969c\u306f\u3042\u308a\u307e\u305b\u3093\u3002\u3053\u308c\u3089\u306e\u30c6\u30b9\u30c8\u306f<i>\u5fc5\u8981</i>\u306a\u3082\u306e\u3067\u3059\u304c\u3001<i>\u5341\u5206</i>\u306a\u3082\u306e\u3067\u306f\u3042\u308a\u307e\u305b\u3093\u3002

### Examining Application Server ###
#
apsExamining=\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\uff65\u30b5\u30fc\u30d0\u30fc\u306b\u95a2\u3059\u308b\u8abf\u67fb
recommendedParser=<b>Axis\u3067\u4f7f\u7528\u3059\u308bXML\u30d1\u30fc\u30b5\u30fc\u306b\u306f Crimson \u3067\u306f\u306a\u304f\u3001<a href="http://xml.apache.org/xerces2-j/">Xerces 2</a> \u3092\u63a8\u5968\u3057\u3066\u3044\u307e\u3059\u3002</b>
couldNotCreateParser=XML Parser\u3092\u751f\u6210\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u305b\u3093\u3002

### Examining System Properties ###
#
sysExamining=\u30b7\u30b9\u30c6\u30e0\uff65\u30d7\u30ed\u30d1\u30c6\u30a3\u306b\u95a2\u3059\u308b\u8abf\u67fb
sysPropError=\u30b7\u30b9\u30c6\u30e0\uff65\u30d7\u30ed\u30d1\u30c6\u30a3\u306b\u30a2\u30af\u30bb\u30b9\u3067\u304d\u307e\u305b\u3093\u3002<p>

classFoundError=\u4e0d\u660e\u306a\u5834\u6240
apsPlatform=\u30d7\u30e9\u30c3\u30c8\u30d5\u30a9\u30fc\u30e0

#
#################### [happyaxis.jsp] #######################
