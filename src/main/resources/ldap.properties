# LDAP URL
dfa.url=LDAP://SERVER_NAME:PORT/

# LDAP User
dfa.userDn=uid=admin,ou=system

# LDAP User Password
# This can either be in clear text, or encrypted form enclosed within 'ENC()'
# To encrypt the password, use the same generator for the database password
dfa.password=ENC("use_password_generator")

# LDAP Base Dn
dfa.base=cn=rest,ou=config,o=radius


# ------------------------------------------------------------------------------
# This section contains regular expressions to define the field structure.
# It is used to facilitate the field parsing and should not be changed.

# RSA Servers (2 and 3 are for replication)
dfa.RSAServer1.expr=/javaReferenceAddress/(?<=# 0# aceServer1# ).*
dfa.RSAServer2.expr=/javaReferenceAddress/(?<=# 1# aceServer2# ).*
dfa.RSAServer3.expr=/javaReferenceAddress/(?<=# 2# aceServer3# ).*

# RSA Shared Key
dfa.sharedKey.expr=/javaReferenceAddress/(?<=# 3# sharedKey# ).*

# RSA Authentication Port Number
dfa.portAuth.expr=/javaReferenceAddress/(?<=# 4# portAuth# ).*

# RSA Accounting Port Number
dfa.portAcct.expr=/javaReferenceAddress/(?<=# 5# portAcct# ).*

# RSA Timeout
dfa.timeout.expr=/javaReferenceAddress/(?<=# 6# timeout# ).*

# RSA Authentication Protocol
dfa.authProtocol.expr=/javaReferenceAddress/(?<=# 7# authProtocol# ).*
# ------------------------------------------------------------------------------
