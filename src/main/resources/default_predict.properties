# ----- NOTICE -----
# This file contains the default or master factory settings for Predict,
# and will be replaced with each new release, so do not modify this file!
# 
# If you need to change the value any of these settings, copy the setting
# into the customer specific predict.properties file instead.
# Any settings present in the predict.properties file will override the
# setting of the same name in this file.

# NOTE: Add variables in alphabetic order within each section!


# -------------------------------------------------------------------------
# SECTION: Common/Default Settings
# -------------------------------------------------------------------------

# Alert interval (in milliseconds)
alertInterval=300000

# Archive option (0=Delete only; 1=Archive to File; 2=Archive to Database)
archive.option=0

# PCM Archive option (0=Delete only; 1=Archive to File; 2=Archive to Database)
pcm.archive.option=0

# Oracle Directory name for 'Archive to File' option (see archive.option
# in the predict.properties file)
archive.directorypath=ARCHIVE_DIR

# Oracle Directory name for 'Archive to File' option (see PCM archive.option
# in the predict.properties file)
pcm.archive.directorypath=PCM_ARCHIVE_DIR

# Default number of days to show in Currency Monitor
currMonitorDefaultDays=7

# Enable or disable the database monitoring. Valid options: FALSE|TRUE
dbmonitor.enabled=FALSE

# Default Font Size
defaultFontSize=Normal

# Default autorefresh rate (in seconds)
defaultRefreshRate=30

# Default auto refresh rate for Interface Monitor screen (in seconds)
defaultRefreshInterfaceMonitor=120

# Is Dual Factor Authentication (DFA) enabled or not?
# Unless you have the infrastructure to support this, it should be FALSE.
# Valid Options: FALSE|TRUE
dfa.enabled=FALSE

# Read DFA configuration from LDAP (or from predict.properties file)
# Valid Options: FALSE|TRUE
dfa.fromLdap=TRUE

# Maximum pages to be exported in reports
export.MaxPageSize=300

# Number of days of user inactivity before user is locked/disabled
InactiveDisable=100

# Enable or disable sending email functionality. Valid options: FALSE|TRUE
mail.enabled=TRUE

# Concurrent match processes (keep less than the number of processors)
maxConcurrentTasks=3

# Number of minutes of session duration before showing auto logoff message
maxSessionTimeOutPeriod=600

# At the start of a matching just before it is run, reset the processing
# flag to N if the flag is Y and it last ran more than 1 hour ago.
# processingFlagWaitTime (in minutes) 
processingFlagWaitTime.inMinutes=60

# Large screen pagination (number of items shown per page)
screenPageSize.systemDefault=100
screenPageSize.alertGenericDisplay=100
screenPageSize.partySearch=100
#screenPageSize.balanceMaintenance=100
#screenPageSize.exchangeRates=100
#screenPageSize.interfaceRules=100
#screenPageSize.movementSummaryDisplay=100
#screenPageSize.partySummaryDisplay=100
#screenPageSize.sweepCancelQueue=100
#screenPageSize.systemLog=100

# Number of minutes of session inactivity before user is timed out
sessionTimeOutPeriod=60

# Number of seconds of session inactivity before the session become invalid
sessionValidationTimeOutPeriod=120

# Enable or disable the Flash SWF profiler. Valid options: FALSE|TRUE
swfProfiler.enabled=FALSE

# To help distinguish between multiple installations, an optional text
# suffix can be added to the Windows title. To enable, uncomment (by
# removing the hash sign '#' below) and change the value as required.
# windows.title.suffix=(PROD)


# -------------------------------------------------------------------------
# SECTION: Internal Settings (Do Not Change Without Consultation)
# -------------------------------------------------------------------------

# Internal cache refresh rate (in minutes)
cacheExpiryTime=30

# URL for Interface Monitor
# For extra portability, try to use 127.0.0.1 (localhost) as the server
interfaces.rpcService=http://127.0.0.1:8090/RPC2

# URL for Interface Monitor PCM
# For extra portability, try to use 127.0.0.1 (localhost) as the server
interfaces.rpcServicePCM=http://127.0.0.1:8091/RPC2

# Default URL for creating a SOAP request
interfaces.soap.operation.url=http://soapinterop.org/

# Interface reprocessing tables
interfaces.table.temporary.name=I_MESSAGEDETAILSTEMP
interfaces.table.failed.name=I_MESSAGEDETAILS_FAILED
interfaces.table.failed.pk=ROW_ID

# Intraday liquidity Monitor data state check time (in seconds)
intraDayLiquidity.checkDataStateTime=30

# Intraday liquidity Monitor Scale Zoom
# Specify distance between data points as function of time interval displayed on chart
# Example: intraDayLiquidity.timeSlotSize=01:00=1,02:00=2,03:30=3,04:00=5,08:00=6,12:00=10,15:00=12,20:00=15,24:01=15
intraDayLiquidity.timeSlotSize=

# Dynamic Jasper Report Temp (swap) Directory
jasperExport.startSwapNumberOfPages=50
jasperExport.tempdir=../standalone/tmp/exports/

# Match quality deletion flag
matchQuality.TotalParameterCount=10

# Location for storing scheduled report files
# (must be configured in predict.properties to enable scheduled reporting)
# paths should be written using "/" event on windows System like "c:/savedReport/"
ScheduledReportLocation=

#Enable or disable the payment control module, possible values are true or false
pcmEnabled=false

# Scenario Summary Tab names
alertSummaryTab1Name=Predict
alertSummaryTab2Name=PCM
#refresh rate for Breakdown monitor screen
defaultRefreshRateBreakDownMonitor =60
#Refresh S_POOL_STATS rate in minutes
poolStatsRefreshRate.inMinutes=15

#MFA params

mfa.fromPropertieFileSettings=false
mfa.loginUrl=
mfa.verifyUrl=
mfa.loginSuccessUrl=
mfa.loginFailUrl=
mfa.logoutUrl=
mfa.logoutSuccessUrl=
mfa.samlConfigId=
mfa.authenticationProtocol=

#Allow Internal Authentication used when ADFS or other MFA method is enabled,
# this will allow internal user id (S_USER) to connect via login page
allowInternalAuthentication=false
#This will allow authentication using other methods such as ADFS
useSmartAuthenticator=false
#Allow user creation with SmartAuthenticator,
allowUserCreationWithSmartAuthenticator=true

#Possibilty to change email subject for Alert Notifications
scenarioSummaryAlertsSubject= Scenario Summary Alerts

# MultiMovementUpdateNumber defines the maximum number of movements a user can update in a single operation.
# This is a safeguard to prevent excessive load on the system.
MultiMovementUpdateNumber=50
