# ----- NOTICE -----
# This file contains customer specific settings.
# Any settings present in this file will override the default setting
# of the same name in default_predict.properties file.


# -------------------------------------------------------------------------
# SECTION: More Common Settings
# -------------------------------------------------------------------------
alertInterval=30000
# Archive option (0=Delete only; 1=Archive to File; 2=Archive to Database)
archive.option=2

# PCM Archive option (0=Delete only; 1=Archive to File; 2=Archive to Database)
pcm.archive.option=2

# URL for Interface Monitor
# For extra portability, try to use 127.0.0.1 (localhost) as the server
interfaces.rpcService=http://*************:8915/RPC2
interfaces.rpcServicePCM=http://*************:18915/RPC2


# To help distinguish between multiple installations, an optional text
# suffix can be added to the Windows title.  To enable, uncomment (by
# removing the hash sign '#' below) and change the value as required.
# windows.title.suffix=(PROD)


# -------------------------------------------------------------------------
# SECTION: Less Common Settings
# -------------------------------------------------------------------------

# Is Dual Factor Authentication (DFA) enabled or not?
# Unless you have the infrastructure to support this, it should be FALSE.
# Valid options: FALSE|TRUE
dfa.enabled=false

# Read DFA configuration from LDAP (or predict.properties file)
# Valid Options: FALSE|TRUE
dfa.fromLdap=FALSE

dfa.rsaServer1=**************
dfa.rsaServer2=**************
dfa.rsaServer3=
dfa.sharedKey=testing123
dfa.portAuth=1812
dfa.portAcct=-1
dfa.timeout=15
dfa.authProtocol=PAP

# Number of days of user inactivity before user is locked/disabled
InactiveDisable=100

# Concurrent match processes (keep less than the number of processors)
maxConcurrentTasks=3

# Number of minutes of session duration before showing auto logoff message
maxSessionTimeOutPeriod=600

# Number of minutes of session inactivity before user is timed out
sessionTimeOutPeriod=60

# Number of seconds of session inactivity before the session become invalid
sessionValidationTimeOutPeriod=
pcmEnabled=true
ScheduledReportLocation=C:/GitWorkspace/java-migration/ScheduledReports
mfa.fromPropertieFileSettings=false
useSmartAuthenticator=true
allowInternalAuthentication=true

developerMode=TRUE

MultiMovementUpdateNumber=10