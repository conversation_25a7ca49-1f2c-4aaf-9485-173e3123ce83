## 
#  Anti-XSS (Cross Site Scripting) filter configuration file
#
#  Notes: 
#  - 'filter.xxx' keys used for filtering and denying requests matching the regex
#  - The key names are not fixed but are dynamically read from this file
#  - All regular expressions are case insensitive
#  - Use \ as an escape character
#
##

# Various forms of scripts, examples below:
# 1) <script>, <script/>, <script .../>
# 2) javascript:
# 3) vbscript:
# 4) onload=
# 
filter.script=(</?script/?>)|(<script\\s.*?/>)|(javascript:)|(vbscript:)|(onload\\s*=)|src\s*=\s*['"]([^'"]+)['"]|src\s*=\s*([^'"\s-]+)
filter.javascriptMethod=(((onabort|onafterprint|onbeforeonload|onbeforeprint|onblur|oncanplay|oncanplaythrough|onchange|onclick|oncontextmenu|ondblclick|ondrag|ondragend|ondragenter|ondragleave|ondragover|ondragstart|ondrop|ondurationchange|onemptied|onended|onerror|onfocus|onformchange|onforminput|onhaschange|oninput|oninvalid|onkeydown|onkeypress|onkeyup|onload|onloadeddata|onloadedmetadata|onloadstart|onmessage|onmousedown|onmousemove|onmouseout|onmouseover|onmouseup|onmousewheel|onoffline|onoine|ononline|onpagehide|onpageshow|onpause|onplay|onplaying|onpopstate|onprogress|onratechange|onreadystatechange|onredo|onresize|onscroll|onseeked|onseeking|onselect|onstalled|onstorage|onsubmit|onsuspend|ontimeupdate|onundo|onunload|onvolumechange|onwaiting))(\\s*)=)
notallowedTags=html, head, title, meta, link, style, script, h1, h2, h3, h4, h5, h6, p, br, hr, a, strong, b, em, i, u, blockquote, pre, code, ul, ol, li, dl, dt, dd, table, caption, thead, tbody, tfoot, tr, th, td, col, form, input, textarea, button, select, option, img, audio, video, iframe, canvas, svg, math, header, footer, nav, main, article, section, aside, figure, figcaption, details, time, mark, meter, progress, ruby, rt, rp, wbr, bdi, bdo, s, samp, small, sub, sup, del, ins, kbd, var, cite, abbr, address, fieldset, legend, q, blockquote