spring.application.name=smart-predict
server.servlet.context-path=/swallowtech
server.port=8080
#logging.config=classpath:logback-spring.xml
spring.profiles.active=prod

spring.cloud.discovery.enabled=false
spring.cloud.zookeeper.discovery.register=false
spring.cloud.zookeeper.enabled=false
# TODO Zookeeper errors are annoying !
logging.level.ROOT=INFO
spring.main.allow-circular-references=false
spring.main.allow-bean-definition-overriding=true
spring.mvc.view.prefix=/
spring.mvc.view.suffix=.jsp
#tomcat.util.scan.StandardJarScanFilter.jarsToSkip=*.jar
spring.jpa.properties.hibernate.connection.autocommit=false

# DISABLE ON PROD
spring.devtools.restart.enabled=false
#server.tomcat.basedir=docs
spring.servlet.multipart.max-file-size = 50MB
spring.servlet.multipart.max-request-size = 50MB
server.tomcat.max-http-form-post-size=52428800

# Ensure multipart support is enabled
spring.servlet.multipart.enabled=true

