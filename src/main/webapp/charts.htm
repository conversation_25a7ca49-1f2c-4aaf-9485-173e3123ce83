<html>
<head>
    <title>Highcharts Tutorial</title>
    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="ILMHighcharts/js/highcharts.js"></script>
    <script src="ILMHighcharts/js/exporting.js"></script>

    <script src="ILMHighcharts/js/highcharts-more.js"></script>
    <script src="ILMHighcharts/js/pattern-fill.js"></script>
    <script src="ILMHighcharts/js/export-csv.js"></script>

    <script src="ILMHighcharts/js/boost.js"></script>


    <script src="ILMHighcharts/js/stackblur.min.js"></script>
    <script src="ILMHighcharts/js/rgbcolor.min.js"></script>
    <script src="ILMHighcharts/js/canvg.min.js"></script>

    <style>
        .circle:before {
            content: ' \25CF';
            font-size: 12px;
        }

        .circle-dotted:before {
            content: ' \25CC';
            font-size: 12px;
        }

        .circle-dashed:before {
            content: ' \25CD';
            font-size: 12px;
        }

        .button-link {
            background: none !important;
            border: none;

            /*optional*/
            font-family: arial, sans-serif; /*input has OS specific font-family*/
            color: #069;
            cursor: pointer;
        }

        .thetooltip {
            border: 0.5px solid black;
            background-color: #fff;
            opacity: 0.8500000000000001;
            padding: 4px 12px;
            border-radius: 3px;
            position: absolute;
            top: 100px;
            left: 50px;
            box-shadow: 1px 1px 3px #666;
        }

        .highcharts-container {
            position: inherit !important;
        }

        .highcharts-tooltip > span {
            background-color: white;
            border-radius: 5px !important;
            opacity: 1;
            z-index: 9999 !important;
            box-shadow: 1px 1px 2px black !important;
            /*border: 1px solid black !important;*/

        }

        .tooltip {
            padding: 5px !important;
            margin-top: -20px !important;
        }

        .label {
            z-index: 1 !important;
        }

        .highcharts-axis-title {
            top: 620px !important;
        }

        #container {
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
    </style>
</head>
<!--<body>-->
<!-- <body onload="firstLoad(parent.getJSONData(window.frameElement.id), true);" style="overflow: hidden ;margin: 5px 0px 0px 0px;"> -->
<body style="overflow: hidden ;margin: 0px 0px 0px 0px;">

<!-- <div id="tooltip"> -->

<!--     <span class="circle" style="color:red"></span> 0 &nbsp;<span class="circle-dotted" style="color:#B1221C"></span> 0 -->
<!--     &nbsp;<span class="circle-dashed" style="color:green"></span> 0&nbsp;<span class="circle" style="color:#80D0D0"></span> 0 &nbsp;<span class="circle" style="color:#FFB6B8"></span> 0&nbsp;<span class="circle-dashed" style="color:#9370db"></span> 0 &nbsp;<span class="circle-dashed" style="color:#E70739"></span> 0 -->
<!-- </div> -->

<div id="container"></div>
<canvas id="canvas" style="display: none"></canvas>

<!-- <input type="checkbox" id="checkedLiquiditySource" name="feature" -->
<!--        value="sourceLiquidity" style="margin-top: 200px"/> -->
<!-- <label for="sourceLiquidity">Source of Liquidity</label> -->
<!-- <input type="checkbox" id="multiplier" name="feature" -->
<!--        value="multiplierCurrency" -->
<!--        style="margin-top: 200px; margin-right: 100 px"/> -->
<!-- <label for="showSerie">Use Currency Multiplier (M)</label> -->
<!-- <input type="checkbox" id="showSerie" name="feature" -->
<!--        value="showSerie" -->
<!--        style="margin-top: 200px; margin-right: 200 px" checked/> -->
<!-- <input type="button" id="highlightSerie" class="button-link" value="ActOut.RABONL2UGlobalEUR"/> -->
<!-- <input id="reset" type="button" value="Zoom Out"/> -->
<!-- <input id="zoom" type="button" value="Zoom In"/> -->
<!--     <p align="left"> -->
<!--         <label>ActOut.RABONL2UGlobalEUR</label> -->
<!--         <select id="chartType"> -->
<!--             <option value="pink" selected>Pink Area</option> -->
<!--             <option value="black">Black Area</option> -->
<!--             <option value="red">Bold Red Area</option> -->
<!--             <option value="green">Green Area</option> -->
<!--             <option value="dashed-blue">Dashed Blue Area</option> -->
<!--             <option value="dotted-green">Dotted Green Area</option> -->
<!--         </select> -->
<!--     </p> -->
<!--     <div class="control"> -->
<!--         <label for="from-time">From:</label> -->
<!--         <input type="time" id="from-time" name="from-time"/> -->

<!--         <label for="to-time">To:</label> -->
<!--         <input type="time" id="to-time" name="to-time"/> -->
<!--     </div> -->


<!--     <input type="checkbox" id="dataSetOnly" name="feature" -->
<!--            value="dataSetOnly"/> -->
<!--     <label for="dataSetOnly">Show Actual Datasets only</label> -->
<!--     <input type="checkbox" id="sod" name="feature" -->
<!--            value="sod" checked/> -->
<!--     <label for="sod">Include SOD</label> -->
<!--     <input type="checkbox" id="hideThresholders" name="feature" -->
<!--            value="RABONL2UGlobalEUR"/> -->
<!--     <label for="hideThresholders">Hide Thresholders</label> -->

<!--     <input type="button" id="updateData" class="button-link" value=" Update Data Provider"/> -->

<!--     <div id="tooltip_bands" class="thetooltip"> -->
<!--         <p id="tooltiptext" style="margin:0"></p>  -->
<!--      </div>  -->


<script language="JavaScript">
// Highcharts.seriesTypes.line.prototype.drawLegendSymbol= Highcharts.seriesTypes.area.prototype.drawLegendSymbol;  


 	document.addEventListener("contextmenu", function(e){
		    e.preventDefault();
		}, false);
	document.addEventListener("keydown", function(e){
	    e.preventDefault();
	}, false);
		
		
	window.addEventListener('message', receiver, false);
	function receiver(e) {
		if(e && e.data){
			var methodName = e.data[0];
			var arguments = e.data[1];
			if(this[methodName]){
			    this[methodName].apply(this, arguments);
			}
		}

	}

    var mousePosition = [];

    var invisibleLegend = [];
    var tooltipValues = [];
    var sourceOfLiquidityChecked = false;
    var useCcyMulitplierChecked = false;
    var isEntityTimeFrameChecked = false;
    var showActualDatasetOnlyChecked = false;
    var entityTimeDifference = 0;
    var firstLoadDataZones = [];
    var zoomFromTime = null;
    var zoomToTime = null;
    var inData = null;
    var _redraw;
    var redrawFunctionIsEmpty = false;
    var visibleLinesInChart = [];
    var allThresholds = [];
    var dataZones;
    var updateTempVariable = false;
    var currencyFormat = '';
    var currencyMutiplierValue = 1;
    var currencyDecimalPlaces = 2 ;
    var dateFormatAsString ="";
    var saveHighligtedCharts = false;
    var highlightedSeries = [];
    var callerTabName = '';

    function setILMData(newData, mergeData, updateTempVar, tabName) {
            if (newData != "" && newData != null && newData.length > 0) {
                if (mergeData) {
                    this.inData = this.inData.concat(JSON.parse(newData));
                    mergeNewCharts(JSON.parse(newData));
                }
                else {
                    firstLoad(newData, true, tabName);
//                     firstLoad(parent.getJSONData(window.frameElement.id), true);
                    return;
                }
            }
        this.updateTempVariable = updateTempVar;

    }

    function firstLoad(dataFromParent, resetAll,tabName) {
        invisibleLegend = [];
        tooltipValues = [];
        isSODClicked = false;
        sourceOfLiquidityChecked = false;
        useCcyMulitplierChecked = false;
        isEntityTimeFrameChecked = false;
        showActualDatasetOnlyChecked = false;
        entityTimeDifference = 0;
        firstLoadDataZones = [];
        zoomFromTime = null;
        zoomToTime = null;
        inData = null;

        visibleLinesInChart = [];
        allThresholds = [];
        callerTabName = tabName;
        if (dataFromParent) {
            if (dataFromParent[0])
                this.inData = JSON.parse(dataFromParent[0]);

            
            
            this.isSODClicked = dataFromParent[1];
            this.sourceOfLiquidityChecked = dataFromParent[2];
            this.firstLoadDataZones = dataFromParent[3];
            this.useCcyMulitplierChecked = dataFromParent[4];
            this.isEntityTimeFrameChecked = dataFromParent[5];
            this.zoomFromTime = dataFromParent[6];
            

            this.zoomToTime = dataFromParent[7];
            this.showActualDatasetOnlyChecked = dataFromParent[8];
            this.currencyFormat  = dataFromParent[9];
            this.currencyMutiplierValue  = dataFromParent[10];
            this.currencyDecimalPlaces =  dataFromParent[11];
            this.dateFormatAsString = dataFromParent[12];
            this.saveHighligtedCharts = dataFromParent[13];
            this.highlightedSeries = dataFromParent[14].split(',');
            this.saveUncheckedItems = dataFromParent[15];
            this.uncheckedItemsFromParent = dataFromParent[16];

            this.updateTempVariable = false;
            var chart = $("#container").highcharts();
            if(chart) {
                $('#container').unbind('click');
                chart.destroy();
            }
            createILMChart();

        }
//         window.parent.postMessage({
//             'func': 'parentFuncName',
//             'message': 'Message text from iframe.'
//         }, "*");

    }


    function setILMDataZones(dataZonesJSON) {
        this.dataZones = dataZonesJSON;
    }
    function destroyChart() {
    	 var chart = $("#container").highcharts();
         if(chart) {
             $('#container').unbind('click');
             chart.destroy();
         }
    	
    }

    function getData() {

        return [this.inData, this.dataZones, this.updateTempVariable];
    }

    var seriesMouseover = [];
    var symbol = [];
    var markers = [];
    var timer;

    function mergeNewCharts(newDataAsJSON){
        var chart = $("#container").highcharts();
        var serieNumber,
            timestamp,
            VALUE = 0,
            type = '',
            color = '',
            yAxis = 1,
            fillColor = {},
            pointInterval = 0,
            pointStart = 0,
            dashStyle = '',
            isClicked = false,
            zIndex = 999,
            isAreaDashed = '';

        for (serieNumber = 0; serieNumber < newDataAsJSON.length; serieNumber++) {

            if (newDataAsJSON[serieNumber].type == 'area') {
                yAxis = 0;
            } else {
                yAxis = 1;
            }
            if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            } else if (newDataAsJSON[serieNumber].type == 'line' && newDataAsJSON[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            } else {
                dashStyle = '';
            }

            if (newDataAsJSON[serieNumber].type == 'area' && newDataAsJSON[serieNumber].color.indexOf('png') != -1) {
                fillColor = {
                    pattern: 'ILMHighcharts/assets/' + newDataAsJSON[serieNumber].color,
                    width: 2,
                    height: 6,
                    opacity: 0.6,
                    //color: inData[serieNumber].borderColor
                }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';

            }

            if (newDataAsJSON[serieNumber].type == 'line') {
                series.push({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                     "zIndex": 2,
                     "lineWidth": 1.5
                });

                chart.addSeries({
                    "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                    "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                    "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                    "data": newDataAsJSON[serieNumber].data.slice(),
                    "type": newDataAsJSON[serieNumber].type,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                    "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": newDataAsJSON[serieNumber].color,
                    "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                     "zIndex": 2
                });

            } else if (newDataAsJSON[serieNumber].type == 'area') {
                if (newDataAsJSON[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });

                    chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
            
                } else {
                    series.push({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });

                    chart.addSeries({
                        "valueId": newDataAsJSON[serieNumber].name,
                        "name": newDataAsJSON[serieNumber].chartlegendDisplayName,
                        "tooltipName": newDataAsJSON[serieNumber].chartDisplayLabel,
                        "chartID": newDataAsJSON[serieNumber].chartDisplayName,
                        "data": newDataAsJSON[serieNumber].data.slice(),
                        "type": newDataAsJSON[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? newDataAsJSON[serieNumber].pointStart : newDataAsJSON[serieNumber].pointStartEntity,
                        "pointInterval": newDataAsJSON[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": newDataAsJSON[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(newDataAsJSON[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(newDataAsJSON[serieNumber].color.slice(-2), 16) + ',0.7)',

                        "isAreaDashed": isAreaDashed,
                        "visible": (newDataAsJSON[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });

                }


            }

            else if (newDataAsJSON[serieNumber].type == 'Threshold') {
                if (newDataAsJSON[serieNumber].visibility == "true") {
                    visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name,
                        "tooltipText": newDataAsJSON[serieNumber].name, 
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": newDataAsJSON[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                        // "className": 'highcharts-color-1'
                    });
                    visibleLinesInChart.push({
                        "group": newDataAsJSON[serieNumber].name.split('.')[0],
                        "id": newDataAsJSON[serieNumber].name + '.Double',
                        "valueId": newDataAsJSON[serieNumber].name,
                        "value": newDataAsJSON[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name,
                    "tooltipText": newDataAsJSON[serieNumber].name,
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": newDataAsJSON[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"

                    // "className": 'highcharts-color-1'
                });
                allThresholds.push({
                    "group": newDataAsJSON[serieNumber].name.split('.')[0],
                    "id": newDataAsJSON[serieNumber].name + '.Double',
                    "valueId": newDataAsJSON[serieNumber].name,
                    "value": newDataAsJSON[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": newDataAsJSON[serieNumber].visibility == "true"
                });
            }



            if (newDataAsJSON[serieNumber].visibility != 'true') {

                invisibleLegend.push(newDataAsJSON[serieNumber].name);
            }





        }

        disableAutoRedraw();
        if (!isSODClicked) {
            uncheckSODandalignScale();
        }

       /* if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
            showLiquiditySource(true, null);
        }*/

        updateVisibleThresholds();


        chart.yAxis[1].update({
            plotLines: visibleLinesInChart.slice()
        });
        enableAutoredrawAndRedrawChart();
             /*  if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
                    showLiquiditySource(true, firstLoadDataZones);
                }

                enableAutoredrawAndRedrawChart();*/

    }
    var series = [];
    function createILMChart() {
        series = [];
        // $("#container").empty();
        var $tooltipBands = $('#tooltip_bands');
        $tooltipBands.hide();
        var $text = $('#tooltiptext');
        displayTooltip = function (text, left, x, y) {
            $text.text(text);
            $tooltipBands.show();
            $tooltipBands.css('left', parseInt(x) + 'px');
            $tooltipBands.css('top', parseInt(y) + 100 + 'px');
        };

        hideTooltip = function () {
            clearTimeout(timer);
            timer = setTimeout(function () {
                $tooltipBands.fadeOut();
            }, 1000);
        };


        var inData = getData()[0];

        var serieNumber,
            timestamp,
            VALUE = 0,
            type = '',
            color = '',
            yAxis = 1,
            fillColor = {},

            pointInterval = 0,
            pointStart = 0,
            dashStyle = '',
            isClicked = false,
            zIndex = 999,
            isAreaDashed = '';
        data = [];
        // marker = []
        marker = {
            symbol: 'circle',
            states: {
                hover: {enabled: false}
            }
        },
            visibleLinesInChart = [],
            allThresholds =[];


        for (serieNumber = 0; serieNumber < inData.length; serieNumber++) {
            if (inData[serieNumber].type == 'area') {
                yAxis = 0;
            } else {
                yAxis = 1;
            }
            if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "3") {
                dashStyle = 'Dash';
            } else if (inData[serieNumber].type == 'line' && inData[serieNumber].typeDetails == "2") {
                dashStyle = 'shortdot';
            } else {
                dashStyle = '';
            }

            if (inData[serieNumber].type == 'area' && inData[serieNumber].color.indexOf('png') != -1) {
                fillColor = {
                    pattern: 'ILMHighcharts/assets/' + inData[serieNumber].color,
                    width: 2,
                    height: 6,
                    opacity: 0.5,
                    //color: inData[serieNumber].borderColor
                }
                isAreaDashed = 'dashed';
            }
            else {
                fillColor = {};
                isAreaDashed = '';

            }

            	var isVisibleItem = (inData[serieNumber].visibility == 'true' && uncheckedItemsFromParent.indexOf(inData[serieNumber].name) == -1);
            	if(isVisibleItem){
            		inData[serieNumber].visibility = 'true'
            	}else {
            		inData[serieNumber].visibility = 'false'
            	}
            if (inData[serieNumber].type == 'line') {
                series.push({
                    "name": inData[serieNumber].chartlegendDisplayName,
                    "tooltipName": inData[serieNumber].chartDisplayLabel,
                    "chartID": inData[serieNumber].chartDisplayName,
                    "data": inData[serieNumber].data.slice(),
                    "type": inData[serieNumber].type,
                    "valueId": inData[serieNumber].name,
                    "pointStart": !isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                    "pointInterval": inData[serieNumber].pointInterval,
                    "yAxis": yAxis,
                    "dashStyle": dashStyle,
                    "isClicked": isClicked,
                    "color": inData[serieNumber].color,
                    "visible": (inData[serieNumber].visibility == 'true'),
                     "zIndex": 2,
                     "lineWidth": 1.5
                });
            } else if (inData[serieNumber].type == 'area') {
                if (inData[serieNumber].chartStyleName.indexOf('Dashed') != -1) {
                    series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].borderColor,
                        "fillColor": fillColor,
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1
                    });
                } else {
                    series.push({
                        "valueId": inData[serieNumber].name,
                        "name": inData[serieNumber].chartlegendDisplayName,
                        "tooltipName": inData[serieNumber].chartDisplayLabel,
                        "chartID": inData[serieNumber].chartDisplayName,
                        "data": inData[serieNumber].data.slice(),
                        "type": inData[serieNumber].type,
                        "pointStart": !isEntityTimeFrameChecked ? inData[serieNumber].pointStart : inData[serieNumber].pointStartEntity,
                        "pointInterval": inData[serieNumber].pointInterval,
                        "yAxis": yAxis,
                        "dashStyle": dashStyle,
                        "isClicked": isClicked,
                        "color": inData[serieNumber].color,
                        "fillColor": 'rgba(' + parseInt(inData[serieNumber].color.slice(-6, -4), 16) + ',' + parseInt(inData[serieNumber].color.slice(-4, -2), 16) + ',' + parseInt(inData[serieNumber].color.slice(-2), 16) + ',0.7)',
                        "isAreaDashed": isAreaDashed,
                        "visible": (inData[serieNumber].visibility == 'true'),
                        "zIndex": 1,
                    });

                }


            }

            else if (inData[serieNumber].type == 'Threshold') {
            	var groupName = inData[serieNumber].name.split('.')[0];
            	var isVisibleItem = (inData[serieNumber].visibility == 'true' && uncheckedItemsFromParent.indexOf(groupName+".Threshold") == -1);
            	if(isVisibleItem){
            		inData[serieNumber].visibility = 'true'
            	}else {
            		inData[serieNumber].visibility = 'false'
            	}
            	
                if (inData[serieNumber].visibility == "true") {
                    visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name,
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": inData[serieNumber].color,
                        "width": 4,
                        "zIndex": 10,
                        // "className": 'highcharts-color-1'
                    });
                    visibleLinesInChart.push({
                        "group": inData[serieNumber].name.split('.')[0],
                        "id": inData[serieNumber].name + '.Double',
                        "tooltipText": inData[serieNumber].name,
                        "valueId": inData[serieNumber].name,
                        "value": inData[serieNumber].data,
                        "color": 'black',
                        // "className": 'highcstepharts-color-1',
                        "dashStyle": 'shortDash',
                        "width": 1,
                        "zIndex": 10
                    });
                }
                allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name,
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": inData[serieNumber].color,
                    "width": 4,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"

                    // "className": 'highcharts-color-1'
                });
                allThresholds.push({
                    "group": inData[serieNumber].name.split('.')[0],
                    "id": inData[serieNumber].name + '.Double',
                    "tooltipText": inData[serieNumber].name,
                    "valueId": inData[serieNumber].name,
                    "value": inData[serieNumber].data,
                    "color": 'black',
                    // "className": 'highcharts-color-1',
                    "dashStyle": 'shortDash',
                    "width": 1,
                    "zIndex": 10,
                    "visible": inData[serieNumber].visibility == "true"
                });
            }



            if (inData[serieNumber].visibility != 'true') {

                invisibleLegend.push(inData[serieNumber].name);
            }

        }

        Highcharts.setOptions({
           // lang: {numericSymbols: ['k', 'M', 'B', 'T', 'P', 'E']},
            time: {
                useUTC: false
            }
        });

        var colorSerie1 = localStorage.getItem("colorSerie1");

        var markersSeries;
        var chart = Highcharts.chart('container', {
            chart: {
            	//animation: false,
                alignTicks: false,
                spacingLeft: 0,
                spacingRight: 0,
                //spacingBottom: 0,
                // marginTop: 150,
            },
            title: {
                text: ''
            },
            legend: {
                enabled: false,
                layout: 'horizontal',
                align: 'center',
                verticalAlign: 'bottom',
                floating: true,
                symbolWidth: 30,
                backgroundColor: 'transparent',
                x: 0,
                y: 20
            },
            boost: {
                useGPUTranslations: true,
                usePreAllocated: true,
            },

            xAxis: [{
                type: 'datetime',
                labels: {
                    formatter: function () {
                        return Highcharts.dateFormat('%H:%M', this.value);
                    },
                    style: {
                        fontSize: "10px"
                    },
                },
                lineWidth: 2,
                gridLineWidth: 1,
                ordinal: false,
                startOnTick: false,
                endOnTick: false,
                minPadding: 0,
                maxPadding: 0,
                tickPixelInterval: 55,
                /*tickInterval: 3600 * 1000,
                maxTickInterval: 3600 * 1000,*/
//                         title: {
//                             useHTML: true,
//                             text: "Time" + " " + "<button id ='xAxisCurrency' class='button-link'>(Currency)</button>"

//                         }

                title: {
                    useHTML: false,
                    text: ""
                }
                ,
            }],

            yAxis: [{ // Primary yAxis
            	lineWidth: 3,
                offset: 0,
                tickWidth: 1,
            	title: {
                    text: 'Accumulated D/C',
                    margin: 0,
                    style: {
                    	color: "black"	
                    }
                   
                },
                tickPixelInterval: 40,
                opposite: true,
                showEmpty: false,
                labels: {
                    formatter: function () {
                        return rightVerticalAxisFormatter(this.value);
                    },
                    style: {
                        fontSize: "10px",
                        color: "black"
                    },
                },


            }, { // Secondary yAxis
                gridLineWidth: 0,
                showEmpty: false,
                lineWidth: 3,
                offset: 0,//I put offset here because ilm zones at 00:00 hide a part of this zone     
                tickWidth: 1,
                title: {
                    text: 'Balance',
                    margin: 0,
                    style: {
                    	color: "black"	
                    }

                },
                tickPixelInterval: 40,
                plotLines: visibleLinesInChart.slice(),
                labels: {
                    formatter: function () {
                        return leftVerticalAxisFormatter(this.value);
                    },
                    style: {
                        fontSize: "10px",
                        color: "black"
                    },
                },


            }

            ],
                optimize: {
                    enableTooltip: true
                },
                tooltip: {
                    useHTML: true,
                    borderWidth: 0,
                    backgroundColor: "rgba(255,255,255,0)",
                    borderRadius: 0,
                    shadow: false,
                    padding: 2,

                    positioner : function (labelWidth, labelHeight, point) {
                        var tooltipX,
                            tooltipY;

                        mousePosition = [];
                        if (point.plotX + labelWidth > chart.plotWidth) {
                            tooltipX = point.plotX + chart.plotLeft - labelWidth - 20;
                        } else {
                            tooltipX = point.plotX + chart.plotLeft + 20;
                        }
                        tooltipY = point.plotY + chart.plotTop - 20;

                        if(tooltipY + labelHeight > $('#container').height()) {
                            tooltipY = 5;
                        }
                        mousePosition.push ({"positionX": point.plotX , "positionY": point.plotY});
                        return {
                            x : tooltipX,
                            y : tooltipY
                        };
                    },

                    formatter: function () {
                        var t = [];
                        var values = [];
                        var isDashed = '\u25CF';
                        var yAxisNumber;
                        tooltipArrays = [];
                        var xMax = chart.plotWidth;
                        var DataTh = [];
                        var formattedValue;
                        var dateAsString;


                        var tooltipArrays = [];

                        $.each(this.points, function(i, point) {


                            if (point.series.userOptions.type == 'line' && invisibleLegend.indexOf(point.series.userOptions.valueId) ==-1) {
                                tooltipArrays.push({"y": point.series.yData[this.point.index], "name": point.series.userOptions.valueId});
                                if(dateAsString == null) {
                                    if(dateFormatAsString.toUpperCase() == "DD/MM/YYYY") {
                                        dateAsString = Highcharts.dateFormat('%d/%m/%Y %H:%M:%S', this.x);
                                    }else {
                                        dateAsString = Highcharts.dateFormat('%m/%d/%Y %H:%M:%S', this.x);
                                    }
                                }
                            }

                            if (this.point.series.userOptions.type == 'area') {
                                yAxisNumber = 0;
                                formattedValue = rightVerticalAxisFormatter(point.y);
                            } else {
                                yAxisNumber = 1;
                                if (useCcyMulitplierChecked == true) {
                                	formattedValue = commonAxisFormatter(point.y, 3);
                                } else {
                                	formattedValue = leftVerticalAxisFormatter(point.y);
                                }
                                
                            }


                            if (point.series.userOptions.dashStyle == "Dash" || (point.series.userOptions.isAreaDashed == 'dashed')) {
                                //Dashed
                                isDashed = '\u25CD';
                            } else if (point.series.userOptions.dashStyle == "shortdot") {
                                //Dotted  \u25CC
                                isDashed = '\u2687';
                            } else {
                                isDashed = '\u25CF';
                            }
                            if ( mousePosition !== "undefined" && !isEmpty(mousePosition)  && point.point.plotX > mousePosition[0].positionX -10 && point.point.plotX < mousePosition[0].positionX +10 && point.point.plotY > mousePosition[0].positionY -10 && point.point.plotY < mousePosition[0].positionY +10 ) {
                                t.push('<span style="color:' + point.color +  '">'+isDashed+'</span> '+ point.series.options.tooltipName +' ('+ Highcharts.dateFormat('%H:%M', this.x) + ';'+
                                    formattedValue +')' +'<span>'+ '</br>');

                            }
                        });
                        //thresholders markers and tooltip
                        for (var i = 0; i < chart.yAxis[1].plotLinesAndBands.length; i = i + 2) {
                            DataTh = chart.yAxis[1].plotLinesAndBands[i].options.value;
                           var space = (chart.yAxis[1].plotLinesAndBands[i].options.id == "plot-line-1") ? '<br/>' : '';
                            if (mousePosition !== "undefined" && !isEmpty(mousePosition) && chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && mousePosition[0].positionX > 0 && mousePosition[0].positionX < 10
                                && mousePosition[0].positionY + chart.plotTop -10 <= chart.yAxis[1].toPixels(DataTh) && chart.yAxis[1].toPixels(DataTh) <= mousePosition[0].positionY + chart.plotTop +  10) {
                         
                                t.push('<span class="circle" style="color:black"></span> '+ chart.yAxis[1].plotLinesAndBands[i].options.tooltipText+ space+'('+ toDate(chart.xAxis[0].toValue(mousePosition[0].positionX +chart.plotLeft) ) + '; '+
                                		leftVerticalAxisFormatter(DataTh) +')' +'<span>'+ '</br>');

                                if (symbol[chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] == null) {

                                    symbol[chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'left'] = chart.renderer.label('\u29BF', chart.plotLeft -5 , chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                            fontSize: '9pt',
                                            color: 'black'
                                        }).attr({
                                            zIndex: 999,
                                        }).add();
                                }
                            }
                            else if (mousePosition !== "undefined" && !isEmpty(mousePosition) && chart.yAxis[1].plotLinesAndBands[i].options.id !== "undefined" && mousePosition[0].positionY + chart.plotTop <= chart.yAxis[1].toPixels(DataTh) && mousePosition[0].positionY + chart.plotTop >= chart.yAxis[1].toPixels(DataTh) - 10 && mousePosition[0].positionX > xMax - 20 && mousePosition[0].positionX <= xMax) {
                                t.push('<span class="circle" style="color: black"></span> '+ chart.yAxis[1].plotLinesAndBands[i].options.tooltipText+ space+'('+ toDate(chart.xAxis[0].toValue(mousePosition[0].positionX +chart.plotLeft) ) + '; '+
                                		leftVerticalAxisFormatter(DataTh) +')' +'<span>'+ '</br>');
                                if (symbol[chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] == null) {

                                    symbol[chart.yAxis[1].plotLinesAndBands[i].options.tooltipText + 'right'] = chart.renderer.label('\u29BF', xMax + chart.plotLeft - 10, chart.yAxis[1].toPixels(DataTh) - 10)
                                        .css({
                                            fontSize: '9pt',
                                            color: 'black'
                                        }).attr({
                                            zIndex: 999,
                                        }).add();
                                }
                            }
                        }
                        for (var j in symbol) {
                            var value = symbol[j];
                            if (!isEmpty(value) && value !== 'undefined' && value !== null) {
                                if (mousePosition[0].positionX +chart.plotLeft   >= value.x +15   || mousePosition[0].positionX +chart.plotLeft   <= value.x -5
                                    || mousePosition[0].positionY  + chart.plotTop >= value.y + 15 || mousePosition[0].positionY  + chart.plotTop <= value.y - 10) {
                                    value.destroy();
                                    delete symbol[j];

                                }
                            }
                        }
//                         parent.updateChartsLiveValues(window.frameElement.id,tooltipArrays, tooltipArrays.length, dateAsString);

//                         window.parent.postMessage({
//                             'func': 'updateChartsLiveValues',
//                             'message': 'Message text from iframe.'
//                         }, "*");
                        window.parent.postMessage(['updateChartsLiveValues', [callerTabName,tooltipArrays, tooltipArrays.length, dateAsString]], "*");
                        
                        
                        return t;
                    },
                    shared: true


                },

            credits: {
                enabled: false
            },

            plotOptions: {
                series: {
                	animation: {
                        duration: 200
                    },
                	point: {
                        events: {
                            mouseOver: function() {
                            	seriesMouseover = this.series;	
                            },
                            mouseOut: function () {
                                seriesMouseover = [];
                                this.series.chart.tooltip.hide();
                            }
                        }
                    },
                    states: {
                        hover: {
                        	halo: {
                                size: 0
                            },
                            lineWidthPlus: 0
                        }
                    },
                    marker: {
                        states: {
                            hover: {enabled: true}
                        },
                        symbol: 'circle',
                        radius: 2,
                        enabled: false
                    },

                    fillOpacity: 0.3
                },


            },


            exporting: {
                enabled : false
            },
            series: series,

        });

        //updateClock();
        $('#container').on('click', function (e) {
            if (seriesMouseover.length != 0) {
                if (seriesMouseover.userOptions.isClicked == false) {
                    if (seriesMouseover.userOptions.type == 'line') {
                        seriesMouseover.update({
                            lineWidth: 3
                        });

                    } else if (seriesMouseover.userOptions.type == 'area') {
                        if (seriesMouseover.userOptions.isAreaDashed == 'dashed') {
                            seriesMouseover.area.attr("fill", seriesMouseover.color);
                        }
                        else {
                            var rgbaCol = 'rgba(' + parseInt(seriesMouseover.color.slice(-6, -4), 16) + ',' + parseInt(seriesMouseover.color.slice(-4, -2), 16) + ',' + parseInt(seriesMouseover.color.slice(-2), 16) + ',1)';
                            seriesMouseover.area.attr("fill", rgbaCol);

                        }
                    }
                    seriesMouseover.userOptions.isClicked = true;
//                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover.userOptions.valueId, true);
                    window.parent.postMessage(['highlightLegendFromHighChart', [callerTabName,seriesMouseover.userOptions.valueId, true]], "*");
                }
                else if (seriesMouseover.userOptions.isClicked == true) {
                    if (seriesMouseover.userOptions.type == 'line') {
                        seriesMouseover.update({
                            lineWidth: 1.5
                        });
                        // seriesMouseover.options.lineWidth = 0

                    } else if (seriesMouseover.userOptions.type == 'area') {
                        if (seriesMouseover.userOptions.isAreaDashed == 'dashed') {
                            seriesMouseover.area.attr("fill", seriesMouseover.userOptions.fillColor);
                        }
                        else {
                            var rgbaCol = 'rgba(' + parseInt(seriesMouseover.color.slice(-6, -4), 16) + ',' + parseInt(seriesMouseover.color.slice(-4, -2), 16) + ',' + parseInt(seriesMouseover.color.slice(-2), 16) + ',0.7)';
                            seriesMouseover.area.attr("fill", rgbaCol);

                        }
                    }
                    seriesMouseover.userOptions.isClicked = false;
//                     parent.highlightLegendFromHighChart(window.frameElement.id,seriesMouseover.userOptions.valueId,false);
                    window.parent.postMessage(['highlightLegendFromHighChart', [callerTabName,seriesMouseover.userOptions.valueId, false]], "*");
                }
            }
            
            $(window).focus();
        });

        if (zoomFromTime && zoomToTime) {
            zoom(zoomFromTime, zoomToTime);
        } else {
            // Do nothing,no zooming is required
        }
        disableAutoRedraw();
        if (!isSODClicked) {
            uncheckSODandalignScale();
        }

        if (sourceOfLiquidityChecked && this.firstLoadDataZones) {
            showLiquiditySource(true, firstLoadDataZones);
        }
        
        if(saveHighligtedCharts) {
        	if(highlightedSeries) {
        		for (var i = 0; i < highlightedSeries.length; i++) {
        			highlightSerie(highlightedSeries[i]);
				}
        	}
        	
        }
        enableAutoredrawAndRedrawChart();
    }
    
    (function (H) {
        Highcharts.Chart.prototype.callbacks.push(function (chart) {
            H.addEvent(chart.xAxis[0], 'afterSetExtremes', function (e) {
                window.parent.postMessage(['updateNowDate', [callerTabName]], "*");
            });
        });
    }(Highcharts));
    

    //List of variables
    var notChecked = false;
    var isSODClicked = true;
    var chart = $("#container").highcharts();

    var isThresholderClicked = true;
    var hasPlotBand = false;
    var tooltipText;
    var hasCheckedCurrency = false;


    function updateClock(dateAsDateStamp) {

        if (dateAsDateStamp) {
            var chart = $('#container').highcharts();
            if (chart) {
                var now = new Date(dateAsDateStamp); // current date

                var isCreated = true;
                var decimalPart = "";
                var time = addZero(now.getHours()) + ':' + addZero(now.getMinutes());

                //var newDate = new Date(2008, 9, 1, now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds()).getTime();
                chart.xAxis[0].removePlotLine(this.id);
                chart.xAxis[0].addPlotLine({
                    color: 'red',
                    value: now,
                    width: 1.5,
                    zIndex: 20,
                    label: {
                        text: time,
                        style: {
                            color: 'red',
                            fontSize: "11px",
                            fontWeight: "Normal"
                        },
                        y: 10,
                        x: 10,
                        rotation: 0,
                        verticalAlign: 'top',
                        textAlign: 'left',
                        id: 'plot-line'
                    },


                });
            }
        }


    }

    function showHideActualDataSet(showHide, visibleYFields) {
        disableAutoRedraw();
        if (showHide) {
            checkDataSet(visibleYFields);

        } else {
            unCheckDataSet(visibleYFields)
        }
        enableAutoredrawAndRedrawChart();
        
        if (!isSODClicked)
            alignScale();

    }

    function showHideThreshold(showHide, groupId) {
        if (!showHide) {
            hideThreshold(groupId);
        } else {
            showThreshold(groupId);
        }
    }


    function alignScaleWithSOD(alignScale) {
        disableAutoRedraw();

        if (!alignScale) {
            uncheckSODandalignScale();
        } else {
            applySODandUnalignScale();
        }

        enableAutoredrawAndRedrawChart();
    }


    function showHideSourcesOfLiquidity(showHide, valuesUpdated, dataZonesJSON, visibleItemsInTree) {
        disableAutoRedraw();

        if (showHide) {
            showLiquiditySource(valuesUpdated, dataZonesJSON);
        } else {
            hideLiquiditySource(visibleItemsInTree);
        }

        enableAutoredrawAndRedrawChart();

    }


    ///List of functions
    //Hide thresholdes
    function hideThreshold(groupName) {
        var chart = $('#container').highcharts();
        for (var i = 0; i < allThresholds.length; i++) {
            if (allThresholds[i].group == groupName) {
                chart.yAxis[1].removePlotLine(allThresholds[i].id);
                allThresholds[i].visible = false;
            }
        }
        updateVisibleThresholds();

    }

    function removeThreshold(groupName) {
        var chart = $('#container').highcharts();
        for (var i = 0; i < allThresholds.length; i++) {
            if (allThresholds[i].group == groupName) {
                chart.yAxis[1].removePlotLine(allThresholds[i].id);
                allThresholds.splice(i, 1);
                i--;
            }
        }
        updateVisibleThresholds();

    }

    //Show Thresholdes
    function showThreshold(groupName) {
        var chart = $('#container').highcharts();
        for (var i = 0; i < allThresholds.length; i++) {
            if (allThresholds[i].group == groupName) {
                allThresholds[i].visible = true;

            }
        }
        updateVisibleThresholds();
        chart.yAxis[1].update({
            plotLines: visibleLinesInChart.slice()
        })
    }

    //DataSetOnly
    function checkDataSet(visibleYFields) {
        var chart = $('#container').highcharts();
        for (var i = 0; i < chart.series.length; i++) {
            if (invisibleLegend.indexOf(chart.series[i].userOptions.valueId) == -1) {
                if (chart.series[i].userOptions.chartID == 'afc' || chart.series[i].userOptions.chartID == 'afd' || chart.series[i].userOptions.chartID == 'fbb' || chart.series[i].userOptions.chartID == 'fbia') {
                    chart.series[i].hide();
                    unHighlightSerieFunction(chart.series[i]);
                    invisibleLegend.push(chart.series[i].userOptions.valueId);
                }
            }
        }


        showActualDatasetOnlyChecked = true;

    }

    function unCheckDataSet(visibleYFields) {
        var chart = $('#container').highcharts();
        if(invisibleLegend.length > 0 && visibleYFields != null && visibleYFields.length>0) {
            for (var i = 0; i < chart.series.length; i++) {
                if (invisibleLegend.indexOf(chart.series[i].userOptions.valueId) != -1) {
                    //  afc  afd fbb fbia
                    //  if(!sourceOfLiquidityChecked) {
                    if (visibleYFields.indexOf(chart.series[i].userOptions.valueId) > -1) {

                        chart.series[i].show();

                        for (var j = 0; j < invisibleLegend.length; j++) {

                            if (invisibleLegend[j] == chart.series[i].userOptions.valueId) {
                                invisibleLegend.splice(j, 1);
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (sourceOfLiquidityChecked)
            showLiquiditySource(true, null);



        showActualDatasetOnlyChecked = false;
    }




    function enableAutoredrawAndRedrawChart() {

        var chart = $('#container').highcharts();
        var axesChanged = false;
        chart.redraw = _redraw;
        chart.redraw();
        redrawFunctionIsEmpty = false;

        if(chart.yAxis[1].min == undefined && chart.yAxis[1].max == undefined){
        	chart.yAxis[1].update({
                offset: 10
            });
        	
        } else {
        	chart.yAxis[1].update({
                offset: 2
            });
        }
        if(chart.yAxis[0].min == undefined && chart.yAxis[0].max == undefined){
      	  
        	chart.yAxis[0].update({
                offset: 10
            });
        	
        }else {
        	chart.yAxis[0].update({
                offset: 2
            });
        }

            
        if (isSODClicked){
        	chart.yAxis[0].update({
                min: null,
                max: null
            });
            chart.yAxis[1].update({
                min: null,
                max: null
            });
           
        }
        adjutMinMax();

    }
    
    
    function adjutMinMax() {
    	 var chart = $('#container').highcharts();
    	 if(chart.yAxis[0].min < 0 && chart.yAxis[0].max <= 0) {
              chart.yAxis[0].update({
                  max: chart.yAxis[0].tickInterval
              });
          }
       
          if(chart.yAxis[0].min >= 0 && chart.yAxis[0].max > 0) {
              chart.yAxis[0].update({
                  min: 0 - chart.yAxis[0].tickInterval
              });
          }

          if(chart.yAxis[1].min < 0 && chart.yAxis[1].max <= 0) {
              chart.yAxis[1].update({
                  max: chart.yAxis[1].tickInterval
              });
          }
       
          if(chart.yAxis[1].min >= 0 && chart.yAxis[1].max > 0) {

              chart.yAxis[1].update({
                  min: 0 - chart.yAxis[1].tickInterval
              });

          }
    }

    function enableAutoredrawOnly() {
        var chart = $('#container').highcharts();
        chart.redraw = _redraw;
        redrawFunctionIsEmpty = false;
    }


    function disableAutoRedraw() {
        var chart = $('#container').highcharts();
        _redraw = chart.redraw;
        chart.redraw = function () {
        };
        redrawFunctionIsEmpty = true;
    }


    function calculateStatistics() {
        var chart = $('#container').highcharts();
        var maxAxe1= 0;
        var minAxe1 = 0;

        var maxAxe2= 0;
        var minAxe2 = 0;

        var isLine = true;
        var pointYInChart = 0;

        var firstIndex = -1;
        var lastIndex  = -1;
        //series.userOptions.type == 'line'

        for (var i = 0; i < chart.series.length; i++) {
            if(chart.series[i].visible){
                chart.series[i].data.forEach(function(point) {
                    // Add to sum if within x-axis visible range
                    if (point.x >= chart.xAxis[0].min && point.x <= chart.xAxis[0].max) {
                        if(firstIndex == -1){
                            firstIndex =    point.index;
                        }
                        lastIndex = point.index;
                    }
                });
                if(firstIndex != -1 && lastIndex != -1)
                    break;

            }
        }



        for (var i = 0; i < chart.series.length; i++) {
            pointYInChart = 0;
            if (chart.series[i].userOptions.type == 'line') {
                isLine = true;
            } else {
                isLine = false;
            }


            if(chart.series[i].visible) {
                for (var h = firstIndex; h < lastIndex; h++) {
                    // chart.series[i].yData[this.point.index]
                    pointYInChart = chart.series[i].yData[h];
                    if (isLine) {
                        if (pointYInChart > maxAxe1) {
                            maxAxe1 = pointYInChart;
                        }
                        if (pointYInChart < minAxe1) {
                            minAxe1 = pointYInChart;
                        }
                    } else {
                        if (pointYInChart > maxAxe2) {
                            maxAxe2 = pointYInChart;
                        }
                        if (pointYInChart < minAxe2) {
                            minAxe2 = pointYInChart;
                        }
                    }

                }
            }

        }


        return [Math.max(Math.abs(maxAxe1), Math.abs(minAxe1)) ,Math.max(Math.abs(maxAxe2), Math.abs(minAxe2))];

    }

    function alignScale(forceCalculation) {
        var chart = $('#container').highcharts();

        var calculatedExtremes =  calculateStatistics();
        if(calculatedExtremes[0] == 0 || calculatedExtremes[1] == 0 && !forceCalculation) {
            var maxValueForAxis0;
            var maxValueForAxis1;

            try {

                chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                chart.yAxis[1].update({
                    min: null,
                    max: null
                });

                if(redrawFunctionIsEmpty){
                    enableAutoredrawAndRedrawChart();
                }

                var max0 = chart.yAxis[0].getExtremes().max;
                var min0 = chart.yAxis[0].getExtremes().min;
                var max1 = chart.yAxis[1].getExtremes().max;
                var min1 = chart.yAxis[1].getExtremes().min;

                maxValueForAxis0 = Math.max(Math.abs(max0),  Math.abs(min0));
                maxValueForAxis1 = Math.max(Math.abs(max1),  Math.abs(min1));
				if (maxValueForAxis0) {
                chart.yAxis[0].update({
				         min: -maxValueForAxis0 - (maxValueForAxis0/100),
				         max: maxValueForAxis0 +(maxValueForAxis0/100) 
                });
				}
				if (maxValueForAxis1) {
                chart.yAxis[1].update({
				         min: -maxValueForAxis1 - (maxValueForAxis1/100),
				         max: maxValueForAxis1 +(maxValueForAxis1/100) 
                });
				}

                if(redrawFunctionIsEmpty){
                    disableAutoRedraw();
                }

            }catch(err) {
                console.log(err.message);
                console.log(err.stack)
            }
        }else {
        	if(calculatedExtremes[0]) {
            chart.yAxis[1].update({
	                min: -calculatedExtremes[0] - (calculatedExtremes[0]/100),
	                max: calculatedExtremes[0] + (calculatedExtremes[0]/100)
            });
        	}
        	if(calculatedExtremes[1]) {
            chart.yAxis[0].update({
	                min: -calculatedExtremes[1] -(calculatedExtremes[1]/100),
	                max: calculatedExtremes[1] + (calculatedExtremes[1]/100)
            });
        }
    }
    }

    //SOD
    function applySODandUnalignScale() {
        var chart = $('#container').highcharts();

        var j = 0;
        var k = 0;
        var l = 1;
        var maxValueForAxis0;
        var maxValueForAxis1;
        try {

            for (var i = 0; i < inData.length; i++) {
                if (inData[i].type !== 'Threshold') {
                    chart.series[j].setData(inData[i].data.slice());
                    j++
                }

                else {
                    if (allThresholds && allThresholds.length > 0) {
                        allThresholds[k].value = inData[i].data.slice();
                        allThresholds[l].value = allThresholds[k].value; //duplicate the value for the dashed balck threshold
                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            isSODClicked = true;
            if (sourceOfLiquidityChecked)
                showLiquiditySource(true, null);
            else {
                //update all thresholds first , then create function to clean thresholds
                updateVisibleThresholds();
            }

            chart.yAxis[1].update({
                plotLines: visibleLinesInChart.slice()
            });


            chart.yAxis[0].update({
                min: null,
                max: null
            });
            chart.yAxis[1].update({
                min: null,
                max: null
            });

        } catch (err) {
            console.log(err.message);
            console.log(err.stack)
        }


    }

    /*
     function to update the list of visible thresholds from all passed thresholds
     */
    function updateVisibleThresholds() {
        visibleLinesInChart = [];

        for (var i = 0; i < allThresholds.length; i++) {
            if (allThresholds[i].visible == true) {
                visibleLinesInChart.push(allThresholds[i]);
            }
        }
        if (sourceOfLiquidityChecked == true && dataZones) {
            visibleLinesInChart.push({
                value: dataZones[4].data[0],
                color: dataZones[4].color,
                fillColor: dataZones[4].color,
                width: 3,
                id: 'plot-line-1',
                zIndex: 20,
                tooltipText: dataZones[4].name,

            });
        }

    }

    function uncheckSODandalignScale() {
        var chart = $('#container').highcharts();

        var j = 0;
        var k = 0;
        var l = 1;
        var maxValueForAxis0;
        var maxValueForAxis1;
        try {
            for (var i = 0; i < inData.length; i++) {
                if (inData[i].type !== 'Threshold') {
                    chart.series[j].setData(inData[i].dataSOD.slice());
                    j++
                }

                else {
                    if (allThresholds && allThresholds.length > 0) {
                        allThresholds[k].value = inData[i].dataSOD.slice();
                        allThresholds[l].value = allThresholds[k].value; //duplicate the value for the dashed balck threshold

                        k = k + 2;
                        l = l + 2;
                    }
                }
            }
            isSODClicked = false;

            if (sourceOfLiquidityChecked)
                showLiquiditySource(true, null);
            else
                updateVisibleThresholds();

            alignScale();

            chart.yAxis[1].update({
                plotLines: visibleLinesInChart.slice()
            });
        } catch (err) {
            console.log(err.message);
            console.log(err.stack)
        }


    }

    //timeStamp to hh:mm
    function toDate(timestamp) {
        var a = new Date((timestamp));
        var hour = addZero(a.getHours());
        var min = addZero(a.getMinutes());
        var dateFormat = hour + ':' + min;
        return dateFormat;

    };


    //var invisbleChartsBySourceOfLiquidity = [];
    function showLiquiditySource(valuesUpdated, dataZonesAsJSONString) {

        if (sourceOfLiquidityChecked && valuesUpdated == false) {
            return;
        }
      //  invisbleChartsBySourceOfLiquidity = [];
        try {
            var dataZonesJSON = null;
            var createNewDataZones = false;

            var chart = $('#container').highcharts();
            for (var i = 0; i < chart.series.length; i++) {
                if (chart.series[i].type == 'area' && invisibleLegend.indexOf(chart.series[i].userOptions.valueId) == -1) {
                    chart.series[i].hide();
                    unHighlightSerieFunction(chart.series[i]);
                    invisibleLegend.push(chart.series[i].userOptions.valueId);
                }
            }
            chart.yAxis[0].update({
                labels: {
                    enabled: false
                },
                title: {
                    text: null
                }
            });


            if (dataZonesAsJSONString) {
                dataZonesJSON = JSON.parse(dataZonesAsJSONString);
                this.dataZones = dataZonesJSON;
            }


            if (dataZones != null) {

                chart.yAxis[1].removePlotBand('plot-band-0');
                chart.yAxis[1].removePlotBand('plot-band-1');
                chart.yAxis[1].removePlotBand('plot-band-2');
                chart.yAxis[1].removePlotBand('plot-band-3');
                chart.yAxis[1].removePlotLine('plot-line-1');


                for (i = 0; i < dataZones.length; i++) {

                    if (!isSODClicked) {
                        dataZones[i].data = dataZones[i].dataSOD;
                    } else {

                        dataZones[i].data = dataZones[i].dataNoSOD;
                    }

                    if (i < dataZones.length - 1) {
                        chart.yAxis[1].addPlotBand({
                            "tooltipText": dataZones[i].name,
                            "from": dataZones[i].data[0],
                            "to": dataZones[i].data[1],
                            "color": dataZones[i].color,
                            "zIndex": 2,
                            "id": 'plot-band-' + i,
                        });
                    }

                }


            }

            // Reset Extreme points when removing the right axis and SOD is checked
             if (!isSODClicked) {
			alignScale();
			} else {  
                chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                chart.yAxis[1].update({
                    min: null,
                    max: null
                });
		}
            sourceOfLiquidityChecked = true;
            updateVisibleThresholds();
            chart.yAxis[1].update({
                plotLines: visibleLinesInChart.slice()
            });


        } catch (err) {
            console.log(err.message);
            console.log(err.stack)

            chart.yAxis[1].removePlotBand('plot-band-0');
            chart.yAxis[1].removePlotBand('plot-band-1');
            chart.yAxis[1].removePlotBand('plot-band-2');
            chart.yAxis[1].removePlotBand('plot-band-3');
            chart.yAxis[1].removePlotLine('plot-line-1');

        }


    }


    function hideOrShowBandOrLine(band, hideOrshow) {
        if (hideOrshow) {
            band.hidden = false;
            band.svgElem.show();
        } else {
            band.hidden = true;
            band.svgElem.hide();
        }
    }

    var listOfBandsIds = ['plot-band-0', 'plot-band-1', 'plot-band-2', 'plot-band-3', 'plot-line-1'];

    function hideLiquiditySource(visibleItemsInTree) {
        var chart = $('#container').highcharts();

        chart.yAxis[1].removePlotBand('plot-band-0');
        chart.yAxis[1].removePlotBand('plot-band-1');
        chart.yAxis[1].removePlotBand('plot-band-2');
        chart.yAxis[1].removePlotBand('plot-band-3');
        chart.yAxis[1].removePlotLine('plot-line-1');



      //  if (chart.series[i].userOptions.chartID != 'ab' && chart.series[i].userOptions.chartID != 'aac' && chart.series[i].userOptions.chartID != 'aad') {
        if(invisibleLegend.length > 0 && visibleItemsInTree != null && visibleItemsInTree.length>0) {
            for (var i = 0; i < chart.series.length; i++) {
                if (visibleItemsInTree.indexOf(chart.series[i].userOptions.valueId) != -1) {
                    chart.series[i].show();
                    for (var j = 0; j < invisibleLegend.length; j++) {
                        if (invisibleLegend[j] == chart.series[i].userOptions.valueId) {
                            invisibleLegend.splice(j, 1); 
                            j--
                            //break;
                        }
                    }

                }

            }

        }

        chart.yAxis[0].update({
            labels: {
                enabled: true
            },
            title: {
                text: 'Accumulated D/C'
            }
        });

        sourceOfLiquidityChecked = false;
        if (!isSODClicked)
            alignScale();

    }

    function getPlotBandById(plotBandId) {
        var chart = $('#container').highcharts();

        for (var i = 0; i < chart.yAxis[1].plotLinesAndBands.length; i++) {
            if (chart.yAxis[1].plotLinesAndBands[i].id === plotBandId) {
                return chart.yAxis[1].plotLinesAndBands[i];
            }
        }
    }


    function checkMultiplierCurrenyMultiplier() {
        useCcyMulitplierChecked = true;
        var chart = $('#container').highcharts();
        chart.yAxis[0].update({
            labels: {
                formatter: function () {
                    //return this.axis.defaultLabelFormatter.call(this);
                    return rightVerticalAxisFormatter(this.value);
                }
            }
        });


        chart.yAxis[1].update({
            labels: {
                formatter: function () {
                    return leftVerticalAxisFormatter(this.value);
                }
            }

        });

    }

    function uncheckMultiplierCurrenyMultiplier() {
        useCcyMulitplierChecked = false;
        var chart = $('#container').highcharts();
        chart.yAxis[0].update({
            labels: {
                formatter: function () {
                    return rightVerticalAxisFormatter(this.value);
                }
            }
        });

        chart.yAxis[1].update({
            labels: {
                formatter: function () {
                    return leftVerticalAxisFormatter(this.value);
                }
            }

        });


    }


    /**
     * Returns the decimal places for the most significant digit for the smallet label in the axis
     */
    function smallestLabelSigDigitDcPlaces (smallestLabel)
    { 
    	 var decSeparator = this.currencyFormat=='currencyPat1'?".":",";
         if(smallestLabel == 0 && smallestLabel== NaN)
             smallestLabel = 0;

         var smallestSigDigits = getFirstSignificantDigit(smallestLabel, 3 , false ,(this.currencyFormat=='currencyPat2'));
         if (smallestSigDigits != 0) {
        	 var sigDigitDcPlaces = smallestSigDigits.indexOf(decSeparator)!=-1?(smallestSigDigits.length - 2 ):0;	 
         }
    
         return sigDigitDcPlaces;

    }

    /**
     * Right vertical axis formatter
     */
    function rightVerticalAxisFormatter(labelValue){
        var chart = $('#container').highcharts();
        var sigDigitDcPlaces = smallestLabelSigDigitDcPlaces((chart.yAxis[0].tickInterval)/ (useCcyMulitplierChecked?currencyMutiplierValue:1));
        return commonAxisFormatter(labelValue, sigDigitDcPlaces);
    }

    /**
     * Left vertical axis formatter
     */
    function leftVerticalAxisFormatter(labelValue){
        var chart = $('#container').highcharts();
        var sigDigitDcPlaces = smallestLabelSigDigitDcPlaces((chart.yAxis[1].tickInterval)/ (useCcyMulitplierChecked?currencyMutiplierValue:1));
        return commonAxisFormatter(labelValue, sigDigitDcPlaces);
    }


    /**
     * Formats a label value, when sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     */
    function commonAxisFormatter(labelValue, sigDigitDcPlaces) {
        var formattedAmount = "";

        // Apply the currency multiplier
        labelValue = labelValue / (useCcyMulitplierChecked ? currencyMutiplierValue : 1);

        if (Math.abs(labelValue) >= 1) {
            if (useCcyMulitplierChecked && currencyMutiplierValue != 1) {
            	
            	if(currencyFormat == 'currencyPat2')
            		formattedAmount = formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? currencyDecimalPlaces : sigDigitDcPlaces, ",", ".");
                else {
                	formattedAmount = formatMoney(labelValue, (sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0) ? currencyDecimalPlaces : sigDigitDcPlaces, ".", ",");
                }
            
            }
                
            else {
                if(currencyFormat == 'currencyPat2')
                    formattedAmount = formatMoney(labelValue, 0, ",", ".");
                else {
                    formattedAmount = formatMoney(labelValue, 0, ".", ",");
                }
               
            }

            // format the amount, if sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            this.currencyFormat = currencyFormat;

        } else {
            // Format the amount based on the most significant number,eg: 0.00014 ==> is formatted into 0.0001.
            // If sigDigitDcPlaces is not NaN or 0, then number of decimal places will be forced to sigDigitDcPlaces
            sigDigitDcPlaces = sigDigitDcPlaces == NaN || sigDigitDcPlaces == 0 ? 3 : sigDigitDcPlaces;
            formattedAmount = getFirstSignificantDigit(labelValue, sigDigitDcPlaces, true, (currencyFormat == 'currencyPat2'));
        }
        return formattedAmount;
    }

        function formatMoney(n, c, d, t) {
            var c = isNaN(c = Math.abs(c)) ? 2 : c,
                d = d == undefined ? "." : d,
                t = t == undefined ? "," : t,
                s = n < 0 ? "-" : "",
                i = String(parseInt(n = Math.abs(Number(n) || 0).toFixed(c))),
                j = (j = i.length) > 3 ? j % 3 : 0;

            return s + (j ? i.substr(0, j) + t : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) + (c ? d + Math.abs(n - i).toFixed(c).slice(2) : "");
        };


    /**
     * This function allow getting the significant digit after the decimal
     * When sigDigitDcPlaces is not NaN or 0, the number of decimal places is forced to sigDigitDcPlaces
     */
    function getFirstSignificantDigit(number, maxDecimals, forceDecimals, siStyle)
    {
        try{

        maxDecimals = typeof maxDecimals !== 'undefined' ? maxDecimals : 2;
        forceDecimals = typeof forceDecimals !== 'undefined' ? forceDecimals : false;
        siStyle = typeof siStyle !== 'undefined' ? siStyle : true;


        var i = 0;
        var inc = Math.pow(10, maxDecimals);
        var str = String(Math.round(inc * number)/inc);
        var sep;

        if(str != "0"){
            var hasSep = str.indexOf(".") == -1, sep = hasSep ? str.length : str.indexOf(".");
            var ret = (hasSep && !forceDecimals ? "" : (siStyle ? "," : ".")) + str.substr(sep+1);
            if (forceDecimals) {
                for (var j = 0; j <= maxDecimals - (str.length - (hasSep ? sep-1 : sep)); j++) ret += "0";
            }

            while (i + 3 < (str.substr(0, 1) == "-" ? sep-1 : sep)) ret = (siStyle ? "." : ",") + str.substr(sep - (i += 3), 3) + ret;

            return str.substr(0, sep - i) + ret;
        }else {
            return 0;
        }

        }catch(err) {
            console.log(err.message);
            console.log(err.stack)
        }

    }

     function firstSignificant(value) {
        return  Math.ceil(-Math.log10(value));
    }


    function ccyMultiplierEventHandler(ccyMuliplierSelected) {
        disableAutoRedraw();
        if (ccyMuliplierSelected == true) {
            checkMultiplierCurrenyMultiplier();
        }
        else {
            uncheckMultiplierCurrenyMultiplier();
        }
        enableAutoredrawAndRedrawChart();
    }

    function highlightSerie(serieName, forcedValueState) {
        var chart = $('#container').highcharts();
        for (var i = 0; i < chart.series.length; i++) {
            if (chart.series[i].userOptions.valueId == serieName) {
				if(forcedValueState) {
					if (forcedValueState == false) {
	                	unHighlightSerieFunction(chart.series[i]);
	                }
	                else if (forcedValueState == true) {
	                	highlightSerieFunction(chart.series[i]);
	                }
				}else {
	                if (chart.series[i].userOptions.isClicked == false) {
	                	highlightSerieFunction(chart.series[i]);
	
	                }
	                else if (chart.series[i].userOptions.isClicked == true) {
	                	unHighlightSerieFunction(chart.series[i]);
	                }
				}
            }
        }
        chart.redraw();
    }
    
    function highlightSerieFunction(serie) {
    	 if (serie.options.type == 'line') {
    		 serie.update({
                 lineWidth: 3
             });

         } else if (serie.options.type == 'area') {

             if (serie.options.isAreaDashed == 'dashed') {
            	 serie.area.attr("fill", serie.options.color);
             }
             else {
                 var rgbaCol = 'rgba(' + parseInt(serie.options.color.slice(-6, -4), 16) + ',' + parseInt(serie.options.color.slice(-4, -2), 16) + ',' + parseInt(serie.options.color.slice(-2), 16) + ',1)';
                 serie.area.attr("fill", rgbaCol);
             }
         }
    	 serie.userOptions.isClicked = true;
            
    }
    
    function unHighlightSerieFunction(serie) {
            if (serie.options.type == 'line') {
            	serie.update({
                    lineWidth: 1.5
                });

            } else if (serie.options.type == 'area') {
                if (serie.options.isAreaDashed == 'dashed') {
                	serie.area.attr("fill", serie.options.fillColor);
                }
                else {
                    var rgbaCol = 'rgba(' + parseInt(serie.options.color.slice(-6, -4), 16) + ',' + parseInt(serie.options.color.slice(-4, -2), 16) + ',' + parseInt(serie.options.color.slice(-2), 16) + ',0.7)';
                    serie.area.attr("fill", rgbaCol);
                }
            }
            serie.userOptions.isClicked = false;
    }

    function showSerie(serieName, showHide) {
        disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        var lineAxeIsInvisible = false;
        var areaAxeIsInvisible = false;
        var redrawAxes = false;


        if (serieName) {
            var groupFromChartId;
            var chart = $('#container').highcharts();

            if(chart.yAxis[1].getExtremes().max == undefined ){
                lineAxeIsInvisible = true;
            }

            if(chart.yAxis[0].getExtremes().max == undefined ){
                areaAxeIsInvisible = true;
            }



            if (serieName.indexOf('.Thresholds') != -1) {
                groupFromChartId = serieName.split('.')[0];
                showHideThreshold(showHide, groupFromChartId);


            } else {
                for (var i = 0; i < chart.series.length; i++) {
                    if (chart.series[i].userOptions.valueId == serieName) {
                        if (!showHide) {
                            chart.series[i].hide();
                            //unHighlightSerieFunction(chart.series[i]);
                            invisibleLegend.push(chart.series[i].userOptions.valueId);
                        } else {
                            if(chart.series[i].type == 'line' && lineAxeIsInvisible) {
                                redrawAxes = true;
                            }else  if(chart.series[i].type == 'area' && areaAxeIsInvisible) {
                                redrawAxes = true;
                            }
                            chart.series[i].show();

                            for (var j = 0; j < invisibleLegend.length; j++) {

                                if (invisibleLegend[j] == chart.series[i].userOptions.valueId) {
                                    invisibleLegend.splice(j, 1);
                                    j--;
                                }
                            }


                        }

                    }
                }
            }
        }

            if (!isSODClicked)
                alignScale();
            else {
                chart.yAxis[0].update({
                    min: null,
                    max: null
                });
                chart.yAxis[1].update({
                    min: null,
                    max: null
                });
            }
        
        enableAutoredrawAndRedrawChart();
    }


    function showOrHideMultipleSeries(seriesToShow, showHide) {
        disableAutoRedraw();
	    //invisibleLegend is temporary variable
        //Hide ans show the first chart
        var listOfSeries;
        var serieName;
        if (seriesToShow) {
            listOfSeries = seriesToShow.split(',');

            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                var groupFromChartId;
                var chart = $('#container').highcharts();
                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    showHideThreshold(showHide, groupFromChartId);
                } else {
                    for (var i = 0; i < chart.series.length; i++) {
                        if (chart.series[i].userOptions.valueId == serieName) {
                            if (!showHide) {
                                chart.series[i].hide();
                                unHighlightSerieFunction(chart.series[i]);
                                invisibleLegend.push(chart.series[i].userOptions.valueId)
                            } else if (sourceOfLiquidityChecked && chart.series[i].type == 'area') { 
                             	chart.series[i].hide();
                             	unHighlightSerieFunction(chart.series[i]);
                             	invisibleLegend.push(chart.series[i].userOptions.valueId);
 
                             }
                            
                            else {
                                chart.series[i].show();
                            
                                for (var h = 0; h < invisibleLegend.length; h++) {
                                        if (invisibleLegend[h] == chart.series[i].userOptions.valueId) {
                                            invisibleLegend.splice(h, 1);
                                            h--;
                                        }
                                }
                            }

                        }
                    }
                }
            }
        }
        /* if (getData()[2] == true) {
             invisibleLegend = [];
         }*/
        if (!isSODClicked)
            alignScale();

        enableAutoredrawAndRedrawChart();
    }


    function removeMultipleCharts(seriesToRemove) {
        disableAutoRedraw();
        //invisibleLegend is temporary variable
        //Hide ans show the first chart
        var listOfSeries;
        var chart = $('#container').highcharts();
        var serieName;
        if (seriesToRemove) {
            listOfSeries = seriesToRemove.split(',');

            for (var j = 0; j < listOfSeries.length; j++) {
                serieName = listOfSeries[j];
                var groupFromChartId;

                if (serieName.indexOf('.Thresholds') != -1) {
                    groupFromChartId = serieName.split('.')[0];
                    removeThreshold(groupFromChartId);
                } else {
                    for (var i = 0; i < chart.series.length; i++) {
                        if (chart.series[i].userOptions.valueId == serieName) {
                                chart.series[i].remove();

                            for(var h = invisibleLegend.length - 1; h >= 0; h--) {
                                if(invisibleLegend[h] === serieName) {
                                    invisibleLegend.splice(h, 1);
                                }
                            }
                        }
                    }
                }
            }

            for (serieNumber = 0; serieNumber < inData.length; serieNumber++) {
                if(seriesToRemove.indexOf(inData[serieNumber].name) != -1){
                    inData.splice(serieNumber, 1);
                    serieNumber--;
                }else if(inData[serieNumber].type == 'Threshold') {
                    if( seriesToRemove.indexOf(inData[serieNumber].name.split('.')[0]+"."+'Thresholds') != -1){
                        inData.splice(serieNumber, 1);
                        serieNumber--;
                    }

                }
            }

        }
        enableAutoredrawAndRedrawChart();
    }


    function setEntityOrCurrencyTimeFrame(isCurrencySelected,timeStampFrom, timeStampTo) {

        disableAutoRedraw();
        var j = 0;
        var chart = $("#container").highcharts();
        for (var i = 0; i < inData.length; i++) {
            if (inData[i].type != 'Threshold') {
                $(chart.series[j]).each(function () {
                    this.update({
                        pointStart: isCurrencySelected ? inData[i].pointStart : inData[i].pointStartEntity,
                    }, false);
                });
                j++;
            }
        }
        zoom(timeStampFrom, timeStampTo);
        enableAutoredrawAndRedrawChart();
    }


    function timeConverter(UNIX_timestamp) {
        var a = new Date(UNIX_timestamp);
        var months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        var year = a.getFullYear();
        var month = months[a.getMonth()];
        var date = a.getDate();
        var hour = a.getHours();
        var min = a.getMinutes();
        var sec = a.getSeconds();
        var time = date + ' ' + month + ' ' + year + ' ' + hour + ':' + min + ':' + sec;
        return time;
    }


    function zoom(timeStampFrom, timeStampTo) {
        var chart = $('#container').highcharts();
        
        chart.xAxis[0].setExtremes(Number(timeStampFrom),Number(timeStampTo));
      
    	chart.yAxis[1].update({
			min:null,   		
            max: null
        });
      
    	chart.yAxis[0].update({
			min:null, 		
            max: null
        });

    	
        if (!isSODClicked)
            alignScale();
    
          if(chart.yAxis[0].min < 0 && chart.yAxis[0].max <= 0) {
   
            chart.yAxis[0].update({
                max: chart.yAxis[0].tickInterval
            });
        }
        

        if(chart.yAxis[0].min >= 0 && chart.yAxis[0].max > 0) {

            chart.yAxis[0].update({
                min: 0 - chart.yAxis[0].tickInterval
            });
        }
        
       

        if(chart.yAxis[1].min < 0 && chart.yAxis[1].max <= 0) {
            chart.yAxis[1].update({
                max: chart.yAxis[1].tickInterval
            });
        }
        
        
     
        if(chart.yAxis[1].min >= 0 && chart.yAxis[1].max > 0) {
            chart.yAxis[1].update({
                min: 0 - chart.yAxis[1].tickInterval
            });

        }
          

    }

    function resetZoom() {
        var chart = $('#container').highcharts();
        chart.zoomOut();

        if (!isSODClicked)
            alignScale();

    }

    function exportChart(chartSnapshot, legendSnapshot, dataXML, exportType, entityId, currencyId,selectedDate, timeFrame) {

        var chart = $('#container').highcharts();
        var svgChart = chart.getSVG()
        var dataAsXML ;
        var dataCSV ;
        canvg('canvas', svgChart , {renderCallback: function() {
            var theImage = canvas.toDataURL('image/png');
            var stringImange = theImage.split(",")[1];
            if(exportType == "pdf")
                dataAsXML = "";
            else {
                dataCSV = chart.getCSV();
                if(dataCSV)
                    dataAsXML = convertToXML(chart.getCSV());
                else
                    dataAsXML = "<dataprovider userId=\"\" lastUpdate=\"\">\n" +
                        "  <result> <timeSlot></timeSlot></result></dataprovider>";
            }
            
            

            parent.onExport(stringImange, legendSnapshot, dataAsXML, exportType, entityId, currencyId,selectedDate, timeFrame);
        }});
    }

    function convertToXML(csvData) {
        var dataAsXML ="";
        var dataArr = csvData.split("\n");
        var heading = dataArr[0].split(",");
        for (var h = 0 ; h< heading.length ; h++){

            heading[h] = heading[h].replace(/['"]+/g, '');
            if(heading[h] == "DateTime"){
                heading[h]  = "timeSlot";
            }
           	heading[h] = heading[h].replace(/ /g, '').replace(/\(|\)/g, "").replace(/\//g, '-');
        }
        var valueTag;
        var data = dataArr.splice(1, dataArr.length - 1);
        dataAsXML+="<dataprovider userId=\"\" lastUpdate=\"\">\n" ;
        for (var i = 0; i < data.length; i++) {
            var d = data[i].split(",");
            dataAsXML+="<result>";
            for (var j = 0; j < d.length; j++) {
                if(heading[j] != "timeSlot") {
                    dataAsXML+="<"+heading[j]+">"+d[j]+"</"+heading[j]+">";
                }
                else{
                    valueTag= d[j].slice(1, -1);
                    dataAsXML+="<"+heading[j]+">"+valueTag+"</"+heading[j]+">";
                }
            }
            dataAsXML+="</result>";
        }
        dataAsXML+="</dataprovider>";
        return dataAsXML;
    }


    function changeChartsStyle(newStyles) {
        disableAutoRedraw();
        var stylesAsJson;
        var yField, styleName, colorChart, borderColor, chartsType, chartTypeDetailsID, dashStyle;
        var fillColor;
        var isAreaDashed;
        var chart = $("#container").highcharts();
        var associativeArray = new Array();

        if(newStyles) {
            stylesAsJson =  JSON.parse(newStyles);
            for (var j = 0; j < stylesAsJson.length; j++) {
                yField =  stylesAsJson[j].name;
                styleName =  stylesAsJson[j].chartStyleName;
                colorChart =  stylesAsJson[j].color;
                borderColor =  stylesAsJson[j].borderColor;
                chartTypeDetailsID =  stylesAsJson[j].typeDetails;
                chartsType =  stylesAsJson[j].type;


                if (chartsType == 'line' && chartTypeDetailsID == "3") {
                    dashStyle = 'Dash';
                } else if (chartsType == 'line' && chartTypeDetailsID == "2") {
                    dashStyle = 'shortdot';
                } else {
                    dashStyle = '';
                }

                if (chartsType == 'area' && colorChart.indexOf('png') != -1) {
                    fillColor = {
                        pattern: 'ILMHighcharts/assets/' + colorChart,
                        width: 2,
                        height: 6,
                        opacity: 0.6,
                        //color: inData[serieNumber].borderColor
                    }
                    isAreaDashed = 'dashed';
                }
                else {
                    fillColor = {};
                    isAreaDashed = '';

                }
                for (var i = 0; i < chart.series.length; i++) {
                    if (chart.series[i].userOptions.valueId == yField) {

                        if(chart.series[i].type == 'area') {
                            if(isAreaDashed) {
                                chart.series[i].update({
                                    fillColor: fillColor,
                                    color: borderColor,
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, true);
                            }else {
                                chart.series[i].update({
                                    color: borderColor,
                                    fillColor: 'rgba(' + parseInt(borderColor.slice(-6, -4), 16) + ',' + parseInt(borderColor.slice(-4, -2), 16) + ',' + parseInt(borderColor.slice(-2), 16) + ',0.7)',
                                    dashStyle: dashStyle,
                                    isAreaDashed: isAreaDashed,
                                }, false);
                            }
                        }else if(chart.series[i].type == 'line') {
                            chart.series[i].update({
                                color: colorChart,
                                dashStyle : dashStyle,
                            }, false);
                        }
                    }
                }
            }
            //chart.redraw();
        }
        enableAutoredrawAndRedrawChart();
    }

    function isEmpty(obj) {
        for (var key in obj) {
            if (obj.hasOwnProperty(key))
                return false;
        }
        return true;
    }

    function addZero(i) {
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }



    var each = Highcharts.each,
        pick = Highcharts.pick,
        seriesTypes = Highcharts.seriesTypes,
        downloadAttrSupported = document.createElement('a').download !== undefined;

    Highcharts.Chart.prototype.getDataRows = function () {
        var options = (this.options.exporting || {}).csv || {},
            xAxis,
            xAxes = this.xAxis,
            rows = {},
            rowArr = [],
            dataRows,
            names = [],
            i,
            x,
            xTitle,
            // Options
            dateFormat = options.dateFormat || '%Y-%m-%d %H:%M:%S',
            columnHeaderFormatter = options.columnHeaderFormatter || function (item, key, keyLength) {
                if (item instanceof Highcharts.Axis) {
                    return (item.options.title && item.options.title.text) ||
                        (item.isDatetimeAxis ? 'DateTime' : 'Category');
                }
                return item ?
                    item.options.valueId + (keyLength > 1 ? ' ('+ key + ')' : '') :
                    'Category';
            },
            xAxisIndices = [];

        // Loop the series and index values
        i = 0;
        each(this.series, function (series) {
            var keys = series.options.keys,
                pointArrayMap = keys || series.pointArrayMap || ['y'],
                valueCount = pointArrayMap.length,
                requireSorting = series.requireSorting,
                categoryMap = {},
                xAxisIndex = Highcharts.inArray(series.xAxis, xAxes),
                j;

            // Map the categories for value axes
            each(pointArrayMap, function (prop) {
                categoryMap[prop] = (series[prop + 'Axis'] && series[prop + 'Axis'].categories) || [];
            });

            if (series.options.includeInCSVExport !== false && series.visible !== false) { // #55

                // Build a lookup for X axis index and the position of the first
                // series that belongs to that X axis. Includes -1 for non-axis
                // series types like pies.
                if (!Highcharts.find(xAxisIndices, function (index) {
                    return index[0] === xAxisIndex;
                })) {
                    xAxisIndices.push([xAxisIndex, i]);
                }

                // Add the column headers, usually the same as series names
                j = 0;
                while (j < valueCount) {
                    names.push(columnHeaderFormatter(series, pointArrayMap[j], pointArrayMap.length));
                    j = j + 1;
                }

                each(series.points, function (point, pIdx) {
                    var key = requireSorting ? point.x : pIdx,
                        prop,
                        val;

                    j = 0;

                    if (!rows[key]) {
                        // Generate the row
                        rows[key] = [];
                        // Contain the X values from one or more X axes
                        rows[key].xValues = [];
                    }
                    rows[key].x = point.x;
                    rows[key].xValues[xAxisIndex] = point.x;

                    // Pies, funnels, geo maps etc. use point name in X row
                    if (!series.xAxis || series.exportKey === 'name') {
                        rows[key].name = point.name;
                    }

                    while (j < valueCount) {
                        prop = pointArrayMap[j]; // y, z etc
                        val = point[prop];
                        rows[key][i + j] = pick(categoryMap[prop][val], val); // Pick a Y axis category if present
                        j = j + 1;
                    }

                });
                i = i + j;
            }
        });

        // Make a sortable array
        for (x in rows) {
            if (rows.hasOwnProperty(x)) {
                rowArr.push(rows[x]);
            }
        }

        var binding, xAxisIndex, column;
        dataRows = [names];

        i = xAxisIndices.length;
        while (i--) { // Start from end to splice in
            xAxisIndex = xAxisIndices[i][0];
            column = xAxisIndices[i][1];
            xAxis = xAxes[xAxisIndex];

            // Sort it by X values
            rowArr.sort(function (a, b) {
                return a.xValues[xAxisIndex] - b.xValues[xAxisIndex];
            });

            // Add header row
            xTitle = columnHeaderFormatter(xAxis);
            //dataRows = [[xTitle].concat(names)];
            dataRows[0].splice(column, 0, xTitle);

            // Add the category column
            each(rowArr, function (row) {

                var category = row.name;
                if (!category) {
                    if (xAxis.isDatetimeAxis) {
                        if (row.x instanceof Date) {
                            row.x = row.x.getTime();
                        }
                        category = Highcharts.dateFormat(dateFormat, row.x);
                    } else if (xAxis.categories) {
                        category = pick(
                            xAxis.names[row.x],
                            xAxis.categories[row.x],
                            row.x
                        )
                    } else {
                        category = row.x;
                    }
                }

                // Add the X/date/category
                row.splice(column, 0, category);
            });
        }
        dataRows = dataRows.concat(rowArr);

        return dataRows;
    };








</script>
</body>

</html>