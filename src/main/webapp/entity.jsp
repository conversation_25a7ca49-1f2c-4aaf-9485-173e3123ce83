<%@ page import="java.util.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>

<%
    response.setHeader("Pragma", "no-cache"); //HTTP 1.0
    response.setHeader("Cache-Control", "no-cache"); //HTTP 1.1
    response.setDateHeader("Expires", 0); //prevents caching at the proxy server
%>

<html>
<body>
This is Entity JSP page. <br>

<label><fmt:message key="entity.domestic_currency"/>:</label>
${actionError}

<form action="entity.do" method="post">
    <c:set var="entityId" value="${sessionScope.entity}" />
    <c:set var="currencyMast" value="${sessionScope.currencyMaster}" />
    <c:set var="countries" value="${sessionScope.countries}" />

    <select name="entityId.domesticCurrency">
        <c:forEach items="${currencyMast}" var="currency">
            <option value="${currency.value}" ${entityId.domesticCurrency == currency.value ? 'selected' : ''}>${currency.label}</option>
        </c:forEach>
    </select>

    <select name="entityId.reprotingCurrency">
        <c:forEach items="${currencyMast}" var="currency">
            <option value="${currency.value}" ${entityId.reprotingCurrency == currency.value ? 'selected' : ''}>${currency.label}</option>
        </c:forEach>
    </select>

    <select name="entityId.countryId">
        <c:forEach items="${countries}" var="country">
            <option value="${country.value}" ${entityId.countryId == country.value ? 'selected' : ''}>${country.label}</option>
        </c:forEach>
    </select>

</form>

</body>
</html>