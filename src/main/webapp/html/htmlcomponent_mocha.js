    // Support for Mocha UI Windows
    // Copyright 2008 www.flexcapacitor.com, www.drumbeatinsight.com 
    // Version 4.0
	
	// cascade mocha ui windows
	function fcCascadeWindows(id) {
		if(document.mochaUI) {
			document.mochaUI.arrangeCascade();
		}
	}
	
	// close mocha ui window
	function fcCloseWindow(id) {
		var el = fcGetElement(id);
		if(document.mochaUI) {
			document.mochaUI.closeWindow(el);
		}
	}
	
	// close all mocha ui windows
	function fcCloseWindows() {
		if(document.mochaUI) {
			document.mochaUI.closeAll();
		}
	}
	
	// focus mocha ui window
	function fcFocusWindow(id) {
		var el = fcGetElement(id);
		if(document.mochaUI) {
			document.mochaUI.focusWindow(el);
		}
	}
	
	// maximize all mocha ui windows
	function fcMaximizeWindows() {
		if(document.mochaUI) {
			document.mochaUI.maximizeAll();
		}
	}

	// minimize mocha ui window
	function fcMinimizeWindow(id) {
		if(document.mochaUI) {
			document.mochaUI.minimizeWindow(id);
		}
	}

	// minimize all mocha ui windows
	function fcMinimizeWindows() {
		if(document.mochaUI) {
			document.mochaUI.minimizeAll();
		}
	}
	
	// maximize mocha ui window
	function fcMaximizeWindow(id) {
		var el = fcGetElement(id);
		if(document.mochaUI) {
			document.mochaUI.maximizeWindow(el);
		}
	}

	// restore minimized mocha ui window
	function fcRestoreMinimizedWindow(id) {
		var el = fcGetElement(id);
		if(document.mochaUI) {
			document.mochaUI.restoreMinimized(el);
		}
	}

	// restore size of mocha ui window
	function fcRestoreWindow(id) {
		var el = fcGetElement(id);
		if(document.mochaUI) {
			document.mochaUI.restoreWindow(el);
		}
	}

	// toggle mocha ui dock
    function fcToggleDock() {
		if(document.mochaUI) {
			document.mochaUI.toggleDock();
		}
	}

	// set visibility of mocha ui dock
    function fcSetDockVisibility(value) {
		if(document.mochaUI) {
			document.mochaUI.setDockVisibility(value);
		}
	}