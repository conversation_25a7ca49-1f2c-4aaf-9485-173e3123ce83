<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en" dir="ltr">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<!--  BEGIN Browser History required section -->
<link rel="stylesheet" type="text/css" href="history/history.css" />
<!--  END Browser History required section -->

<title>${title}</title>
<script src="AC_OETags.js" language="javascript"></script>

<!--  BEGIN Browser History required section -->
<script src="history/history.js" language="javascript"></script>
<!--  END Browser History required section -->

<!--  B<PERSON>IN HTML Component required section -->
<!--  Be sure to add wmode = opaque or wmode = transparent to the swf embed code below -->
<script language="JavaScript" type="text/javascript" src="htmlcomponent.js"></script>
<!--  END HTML Component required section -->

<!--  BEGIN HTML Component Alert and Mocha UI section -->
<link rel="stylesheet" href="css/mocha.css" type="text/css" />
<link rel="stylesheet" href="css/style.css" type="text/css" />
<script type="text/javascript" src="scripts/mootools-trunk-1475.js" charset="utf-8"></script>
<!--[if IE]>
	<script type="text/javascript" src="scripts/excanvas-compressed.js"></script>		
<![endif]-->
<script type="text/javascript" src="scripts/mocha-events.js" charset="utf-8"></script>	
<script type="text/javascript" src="scripts/mocha.js" charset="utf-8"></script>
<script language="JavaScript" type="text/javascript" src="htmlcomponent/htmlcomponent_mocha.js"></script>
<!--  END HTML Component Alert and Mocha UI section -->

<!--  BEGIN HTML Component editor sections -->
<!-- Read the products documentation for their usage -->
<!-- Uncomment to include the class for your editor -->
<!--<script type="text/javascript">
    _editor_url  = "xinha/"  // (preferably absolute) URL (including trailing slash) where Xinha is installed
    _editor_lang = "en";      // And the language we need to use in the editor.
    _editor_skin = "silva";   // If you want use skin, add the name here
</script>-->
<!--<script type="text/javascript" src="xinha/XinhaLoader.js"></script>-->
<!--<script type="text/javascript" src="xinha/XinhaConfig.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="fckeditor/fckeditor.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="tinymce/jscripts/tiny_mce/tiny_mce.js"></script>-->
<!--  END HTML Component editor section -->

<style>
html { overflow:hidden }
body { margin: 0px; overflow:hidden }
</style>
<script language="JavaScript" type="text/javascript">
<!--
// -----------------------------------------------------------------------------
// Globals
// Major version of Flash required
var requiredMajorVersion = ${version_major};
// Minor version of Flash required
var requiredMinorVersion = ${version_minor};
// Minor version of Flash required
var requiredRevision = ${version_revision};
// -----------------------------------------------------------------------------
// -->
</script>

</head>
<body scroll="no" >

<div id="mochaDesktop">

<div id="mochaDock">
	<div id="mochaDockPlacement"></div>
	<div id="mochaDockAutoHide"></div>
</div>

<!-- BEGIN Mocha UI Test Section - you can remove this section -->
<div style="display:none; position: absolute; top: 20px; right: 20px; width: 160px; z-index: 200; background: #fff; border: 1px solid #000; padding: 10px;">
	<ul>
		<li><a id="ajaxpageLink" href="pages/lipsum.html">Ajax/XHR Demo</a></li>			
		<li><a id="mootoolsLink" href="http://forum.mootools.net/">Iframe: Mootools</a></li>
		<li><a id="spirographLink" href="pages/spirograph.html">Iframe: Spirograph</a></li>
		<li><a id="youTubeLink" href="pages/youtube.html">Iframe: YouTube</a></li>
		<li><a id="anon1Link" href="pages/lipsum.html">No ID</a></li>
		<li><a href="javascript:void(0);" onclick="document.mochaUI.toggleDock()">Toggle Dock</a></li>
		<li><a href="javascript:void(0);" onclick="fcHTMLTest('popup')">New Window</a></li>		
	</ul>
</div>
<!-- END Mocha UI Test Section -->

<script language="JavaScript" type="text/javascript">
<!--
// Version check for the Flash Player that has the ability to start Player Product Install (6.0r65)
var hasProductInstall = DetectFlashVer(6, 0, 65);

// Version check based upon the values defined in globals
var hasRequestedVersion = DetectFlashVer(requiredMajorVersion, requiredMinorVersion, requiredRevision);

if ( hasProductInstall && !hasRequestedVersion ) {
	// DO NOT MODIFY THE FOLLOWING FOUR LINES
	// Location visited after installation is complete if installation is required
	var MMPlayerType = (isIE == true) ? "ActiveX" : "PlugIn";
	var MMredirectURL = window.location;
    document.title = document.title.slice(0, 47) + " - Flash Player Installation";
    var MMdoctitle = document.title;

	AC_FL_RunContent(
		"src", "playerProductInstall",
		"FlashVars", "MMredirectURL="+MMredirectURL+'&MMplayerType='+MMPlayerType+'&MMdoctitle='+MMdoctitle+"",
		"width", "${width}",
		"height", "${height}",
		"align", "middle",
		"id", "${application}",
		"quality", "high",
		"bgcolor", "${bgcolor}",
		"name", "${application}",
		"allowScriptAccess","sameDomain",
		"type", "application/x-shockwave-flash",
		"pluginspage", "http://www.adobe.com/go/getflashplayer"
	);
} else if (hasRequestedVersion) {
	// if we've detected an acceptable version
	// embed the Flash Content SWF when all tests are passed
	AC_FL_RunContent(
			"src", "${swf}",
			"width", "${width}",
			"height", "${height}",
			"align", "middle",
			"id", "${application}",
			"quality", "high",
			"bgcolor", "${bgcolor}",
			"name", "${application}",
			"allowScriptAccess","sameDomain",
			"type", "application/x-shockwave-flash",
			"pluginspage", "http://www.adobe.com/go/getflashplayer",
			"wmode", "opaque"
	);
  } else {  // flash is too old or we can't detect the plugin
    var alternateContent = 'Alternate HTML content should be placed here. '
  	+ 'This content requires the Adobe Flash Player. '
   	+ '<a href=http://www.adobe.com/go/getflash/>Get Flash</a>';
    document.write(alternateContent);  // insert non-flash content
  }
// -->
</script>
<noscript>
  	<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"
			id="${application}" width="${width}" height="${height}"
			codebase="http://fpdownload.macromedia.com/get/flashplayer/current/swflash.cab">
			<param name="movie" value="${swf}.swf" />
			<param name="quality" value="high" />
			<param name="bgcolor" value="${bgcolor}" />
			<param name="allowScriptAccess" value="sameDomain" />
			<embed src="${swf}.swf" quality="high" bgcolor="${bgcolor}"
				width="${width}" height="${height}" name="${application}" align="middle"
				play="true"
				loop="false"
				quality="high"
				allowScriptAccess="sameDomain"
				type="application/x-shockwave-flash"
				pluginspage="http://www.adobe.com/go/getflashplayer">
			</embed>
	</object>
</noscript>
</div><!-- mochaDesktop end -->
</body>
</html>