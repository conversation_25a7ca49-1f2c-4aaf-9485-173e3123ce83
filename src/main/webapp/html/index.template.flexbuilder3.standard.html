<!-- saved from url=(0014)about:internet -->
<html lang="en">

<!-- 
Smart developers always View Source. 

This application was built using Adobe Flex, an open source framework
for building rich Internet applications that get delivered via the
Flash Player or to desktops via Adobe AIR. 

Learn more about Flex at http://flex.org 
// -->

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<!--  BEGIN Browser History required section -->
<link rel="stylesheet" type="text/css" href="history/history.css" />
<!--  END Browser History required section -->

<title>${title}</title>
<script src="AC_OETags.js" language="javascript"></script>

<!--  BEGIN Browser History required section -->
<script src="history/history.js" language="javascript"></script>
<!--  END Browser History required section -->

<!--  BEGIN HTML Component required section -->
<!--  Be sure to add wmode = opaque or wmode = transparent to the swf embed code below -->
<script language="JavaScript" type="text/javascript" src="htmlcomponent/htmlcomponent.js"></script>
<!--  END HTML Component required section -->

<!--  BEGIN HTML Component Alert and MochaUI section -->
<!--  You can comment this section out if you do not use html alert or mocha ui pop up windows -->
<link rel="stylesheet" href="css/mocha.css" type="text/css" />
<!--<link rel="stylesheet" href="css/style.css" type="text/css" />-->
<script type="text/javascript" src="scripts/mootools-trunk-1475.js" charset="utf-8"></script>
<!--[if IE]>
	<script type="text/javascript" src="scripts/excanvas-compressed.js"></script>		
<![endif]-->
<script type="text/javascript" src="scripts/mocha-events.js" charset="utf-8"></script>	
<script type="text/javascript" src="scripts/mocha.js" charset="utf-8"></script>
<script language="JavaScript" type="text/javascript" src="htmlcomponent/htmlcomponent_mocha.js"></script>
<!--  END HTML Component Alert and MochaUI section -->

<!--  BEGIN HTML Component editor sections -->
<!-- Read the products documentation for their usage -->
<!-- Uncomment to include the class for your editor -->
<!--<script type="text/javascript">
    _editor_url  = "xinha/"  // (preferably absolute) URL (including trailing slash) where Xinha is installed
    _editor_lang = "en";      // And the language we need to use in the editor.
    _editor_skin = "silva";   // If you want use skin, add the name here
</script>-->
<!--<script language="JavaScript" type="text/javascript" src="xinha/XinhaLoader.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="xinha/XinhaConfig.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="fckeditor/fckeditor.js"></script>-->
<!--<script language="JavaScript" type="text/javascript" src="tinymce/jscripts/tiny_mce/tiny_mce.js"></script>-->
<!--  END HTML Component editor section -->

<style>
body { margin: 0px; overflow:hidden }
</style>
<script language="JavaScript" type="text/javascript">
<!--
// -----------------------------------------------------------------------------
// Globals
// Major version of Flash required
var requiredMajorVersion = ${version_major};
// Minor version of Flash required
var requiredMinorVersion = ${version_minor};
// Minor version of Flash required
var requiredRevision = ${version_revision};
// -----------------------------------------------------------------------------
// -->
</script>
</head>

<body scroll="no">
<script language="JavaScript" type="text/javascript">
<!--
// Version check for the Flash Player that has the ability to start Player Product Install (6.0r65)
var hasProductInstall = DetectFlashVer(6, 0, 65);

// Version check based upon the values defined in globals
var hasRequestedVersion = DetectFlashVer(requiredMajorVersion, requiredMinorVersion, requiredRevision);

if ( hasProductInstall && !hasRequestedVersion ) {
	// DO NOT MODIFY THE FOLLOWING FOUR LINES
	// Location visited after installation is complete if installation is required
	var MMPlayerType = (isIE == true) ? "ActiveX" : "PlugIn";
	var MMredirectURL = window.location;
    document.title = document.title.slice(0, 47) + " - Flash Player Installation";
    var MMdoctitle = document.title;

	AC_FL_RunContent(
		"src", "playerProductInstall",
		"FlashVars", "MMredirectURL="+MMredirectURL+'&MMplayerType='+MMPlayerType+'&MMdoctitle='+MMdoctitle+"",
		"width", "${width}",
		"height", "${height}",
		"align", "middle",
		"id", "${application}",
		"quality", "high",
		"bgcolor", "${bgcolor}",
		"name", "${application}",
		"allowScriptAccess","sameDomain",
		"type", "application/x-shockwave-flash",
		"pluginspage", "http://www.adobe.com/go/getflashplayer"
	);
} else if (hasRequestedVersion) {
	// if we've detected an acceptable version
	// embed the Flash Content SWF when all tests are passed
	AC_FL_RunContent(
			"src", "${swf}",
			"width", "${width}",
			"height", "${height}",
			"align", "middle",
			"id", "${application}",
			"quality", "high",
			"bgcolor", "${bgcolor}",
			"name", "${application}",
			"allowScriptAccess","sameDomain",
			"type", "application/x-shockwave-flash",
			"pluginspage", "http://www.adobe.com/go/getflashplayer",
			"wmode", "opaque"
	);
  } else {  // flash is too old or we can't detect the plugin
    var alternateContent = 'Alternate HTML content should be placed here. '
  	+ 'This content requires the Adobe Flash Player. '
   	+ '<a href=http://www.adobe.com/go/getflash/>Get Flash</a>';
    document.write(alternateContent);  // insert non-flash content
  }
// -->
</script>
<noscript>
  	<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"
			id="${application}" width="${width}" height="${height}"
			codebase="http://fpdownload.macromedia.com/get/flashplayer/current/swflash.cab">
			<param name="movie" value="${swf}.swf" />
			<param name="quality" value="high" />
			<param name="bgcolor" value="${bgcolor}" />
			<param name="allowScriptAccess" value="sameDomain" />
			<embed src="${swf}.swf" quality="high" bgcolor="${bgcolor}"
				width="${width}" height="${height}" name="${application}" align="middle"
				play="true"
				loop="false"
				quality="high"
				allowScriptAccess="sameDomain"
				type="application/x-shockwave-flash"
				pluginspage="http://www.adobe.com/go/getflashplayer">
			</embed>
	</object>
</noscript>
</body>
</html>