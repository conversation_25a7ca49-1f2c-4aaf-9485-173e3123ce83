/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * monaco-json version: 2.7.0(f3afc1b721188b32506d5b8a924561e3ec7534e3)
 * Released under the MIT license
 * https://github.com/Microsoft/monaco-json/blob/master/LICENSE.md
 *-----------------------------------------------------------------------------*/
!function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-languageserver-types/main",["require","exports"],e)}((function(e,t){"use strict";var n,r,o,i,a,s,c,u,f,l,d,p,h;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.create=function(e,t){return{line:e,character:t}},e.is=function(e){var t=e;return j.objectLiteral(t)&&j.number(t.line)&&j.number(t.character)}}(n=t.Position||(t.Position={})),function(e){e.create=function(e,t,r,o){if(j.number(e)&&j.number(t)&&j.number(r)&&j.number(o))return{start:n.create(e,t),end:n.create(r,o)};if(n.is(e)&&n.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+r+", "+o+"]")},e.is=function(e){var t=e;return j.objectLiteral(t)&&n.is(t.start)&&n.is(t.end)}}(r=t.Range||(t.Range={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return j.defined(t)&&r.is(t.range)&&(j.string(t.uri)||j.undefined(t.uri))}}(o=t.Location||(t.Location={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return j.defined(t)&&r.is(t.targetRange)&&j.string(t.targetUri)&&(r.is(t.targetSelectionRange)||j.undefined(t.targetSelectionRange))&&(r.is(t.originSelectionRange)||j.undefined(t.originSelectionRange))}}(t.LocationLink||(t.LocationLink={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return j.number(t.red)&&j.number(t.green)&&j.number(t.blue)&&j.number(t.alpha)}}(i=t.Color||(t.Color={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return r.is(t.range)&&i.is(t.color)}}(t.ColorInformation||(t.ColorInformation={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return j.string(t.label)&&(j.undefined(t.textEdit)||u.is(t))&&(j.undefined(t.additionalTextEdits)||j.typedArray(t.additionalTextEdits,u.is))}}(t.ColorPresentation||(t.ColorPresentation={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(t.FoldingRangeKind||(t.FoldingRangeKind={})),function(e){e.create=function(e,t,n,r,o){var i={startLine:e,endLine:t};return j.defined(n)&&(i.startCharacter=n),j.defined(r)&&(i.endCharacter=r),j.defined(o)&&(i.kind=o),i},e.is=function(e){var t=e;return j.number(t.startLine)&&j.number(t.startLine)&&(j.undefined(t.startCharacter)||j.number(t.startCharacter))&&(j.undefined(t.endCharacter)||j.number(t.endCharacter))&&(j.undefined(t.kind)||j.string(t.kind))}}(t.FoldingRange||(t.FoldingRange={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return j.defined(t)&&o.is(t.location)&&j.string(t.message)}}(a=t.DiagnosticRelatedInformation||(t.DiagnosticRelatedInformation={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(t.DiagnosticSeverity||(t.DiagnosticSeverity={})),function(e){e.create=function(e,t,n,r,o,i){var a={range:e,message:t};return j.defined(n)&&(a.severity=n),j.defined(r)&&(a.code=r),j.defined(o)&&(a.source=o),j.defined(i)&&(a.relatedInformation=i),a},e.is=function(e){var t=e;return j.defined(t)&&r.is(t.range)&&j.string(t.message)&&(j.number(t.severity)||j.undefined(t.severity))&&(j.number(t.code)||j.string(t.code)||j.undefined(t.code))&&(j.string(t.source)||j.undefined(t.source))&&(j.undefined(t.relatedInformation)||j.typedArray(t.relatedInformation,a.is))}}(s=t.Diagnostic||(t.Diagnostic={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={title:e,command:t};return j.defined(n)&&n.length>0&&(o.arguments=n),o},e.is=function(e){var t=e;return j.defined(t)&&j.string(t.title)&&j.string(t.command)}}(c=t.Command||(t.Command={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return j.objectLiteral(t)&&j.string(t.newText)&&r.is(t.range)}}(u=t.TextEdit||(t.TextEdit={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return j.defined(t)&&m.is(t.textDocument)&&Array.isArray(t.edits)}}(f=t.TextDocumentEdit||(t.TextDocumentEdit={})),function(e){e.create=function(e,t){var n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"create"===t.kind&&j.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||j.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||j.boolean(t.options.ignoreIfExists)))}}(l=t.CreateFile||(t.CreateFile={})),function(e){e.create=function(e,t,n){var r={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(r.options=n),r},e.is=function(e){var t=e;return t&&"rename"===t.kind&&j.string(t.oldUri)&&j.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||j.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||j.boolean(t.options.ignoreIfExists)))}}(d=t.RenameFile||(t.RenameFile={})),function(e){e.create=function(e,t){var n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"delete"===t.kind&&j.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||j.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||j.boolean(t.options.ignoreIfNotExists)))}}(p=t.DeleteFile||(t.DeleteFile={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return j.string(e.kind)?l.is(e)||d.is(e)||p.is(e):f.is(e)})))}}(h=t.WorkspaceEdit||(t.WorkspaceEdit={}));var m,g,v,y,b=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(u.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(u.replace(e,t))},e.prototype.delete=function(e){this.edits.push(u.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}(),x=function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach((function(e){if(f.is(e)){var n=new b(e.edits);t._textEditChanges[e.textDocument.uri]=n}})):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new b(e.changes[n]);t._textEditChanges[n]=r})))}return Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(m.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t=e;if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:o=[]};this._workspaceEdit.documentChanges.push(n),r=new b(o),this._textEditChanges[t.uri]=r}return r}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var o=[];this._workspaceEdit.changes[e]=o,r=new b(o),this._textEditChanges[e]=r}return r},e.prototype.createFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(l.create(e,t))},e.prototype.renameFile=function(e,t,n){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(d.create(e,t,n))},e.prototype.deleteFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(p.create(e,t))},e.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")},e}();t.WorkspaceChange=x,function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return j.defined(t)&&j.string(t.uri)}}(t.TextDocumentIdentifier||(t.TextDocumentIdentifier={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return j.defined(t)&&j.string(t.uri)&&(null===t.version||j.number(t.version))}}(m=t.VersionedTextDocumentIdentifier||(t.VersionedTextDocumentIdentifier={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return j.defined(t)&&j.string(t.uri)&&j.string(t.languageId)&&j.number(t.version)&&j.string(t.text)}}(t.TextDocumentItem||(t.TextDocumentItem={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(g=t.MarkupKind||(t.MarkupKind={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(g=t.MarkupKind||(t.MarkupKind={})),function(e){e.is=function(e){var t=e;return j.objectLiteral(e)&&g.is(t.kind)&&j.string(t.value)}}(v=t.MarkupContent||(t.MarkupContent={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(t.CompletionItemKind||(t.CompletionItemKind={})),function(e){e.PlainText=1,e.Snippet=2}(t.InsertTextFormat||(t.InsertTextFormat={})),function(e){e.create=function(e){return{label:e}}}(t.CompletionItem||(t.CompletionItem={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(t.CompletionList||(t.CompletionList={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return j.string(t)||j.objectLiteral(t)&&j.string(t.language)&&j.string(t.value)}}(y=t.MarkedString||(t.MarkedString={})),function(e){e.is=function(e){var t=e;return!!t&&j.objectLiteral(t)&&(v.is(t.contents)||y.is(t.contents)||j.typedArray(t.contents,y.is))&&(void 0===e.range||r.is(e.range))}}(t.Hover||(t.Hover={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(t.ParameterInformation||(t.ParameterInformation={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={label:e};return j.defined(t)&&(o.documentation=t),j.defined(n)?o.parameters=n:o.parameters=[],o}}(t.SignatureInformation||(t.SignatureInformation={})),function(e){e.Text=1,e.Read=2,e.Write=3}(t.DocumentHighlightKind||(t.DocumentHighlightKind={})),function(e){e.create=function(e,t){var n={range:e};return j.number(t)&&(n.kind=t),n}}(t.DocumentHighlight||(t.DocumentHighlight={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(t.SymbolKind||(t.SymbolKind={})),function(e){e.create=function(e,t,n,r,o){var i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(t.SymbolInformation||(t.SymbolInformation={}));var S=function(){};t.DocumentSymbol=S,function(e){e.create=function(e,t,n,r,o,i){var a={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(a.children=i),a},e.is=function(e){var t=e;return t&&j.string(t.name)&&j.number(t.kind)&&r.is(t.range)&&r.is(t.selectionRange)&&(void 0===t.detail||j.string(t.detail))&&(void 0===t.deprecated||j.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))}}(S=t.DocumentSymbol||(t.DocumentSymbol={})),t.DocumentSymbol=S,function(e){e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports"}(t.CodeActionKind||(t.CodeActionKind={})),function(e){e.create=function(e,t){var n={diagnostics:e};return null!=t&&(n.only=t),n},e.is=function(e){var t=e;return j.defined(t)&&j.typedArray(t.diagnostics,s.is)&&(void 0===t.only||j.typedArray(t.only,j.string))}}(t.CodeActionContext||(t.CodeActionContext={})),function(e){e.create=function(e,t,n){var r={title:e};return c.is(t)?r.command=t:r.edit=t,void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&j.string(t.title)&&(void 0===t.diagnostics||j.typedArray(t.diagnostics,s.is))&&(void 0===t.kind||j.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||c.is(t.command))&&(void 0===t.edit||h.is(t.edit))}}(t.CodeAction||(t.CodeAction={})),function(e){e.create=function(e,t){var n={range:e};return j.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return j.defined(t)&&r.is(t.range)&&(j.undefined(t.command)||c.is(t.command))}}(t.CodeLens||(t.CodeLens={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return j.defined(t)&&j.number(t.tabSize)&&j.boolean(t.insertSpaces)}}(t.FormattingOptions||(t.FormattingOptions={}));var C=function(){};t.DocumentLink=C,function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return j.defined(t)&&r.is(t.range)&&(j.undefined(t.target)||j.string(t.target))}}(C=t.DocumentLink||(t.DocumentLink={})),t.DocumentLink=C,t.EOL=["\n","\r\n","\r"],function(e){e.create=function(e,t,n,r){return new k(e,t,n,r)},e.is=function(e){var t=e;return!!(j.defined(t)&&j.string(t.uri)&&(j.undefined(t.languageId)||j.string(t.languageId))&&j.number(t.lineCount)&&j.func(t.getText)&&j.func(t.positionAt)&&j.func(t.offsetAt))},e.applyEdits=function(e,t){for(var n=e.getText(),r=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0;var o=t.slice(0,r);var i=t.slice(r);e(o,n);e(i,n);var a=0;var s=0;var c=0;for(;a<o.length&&s<i.length;){var u=n(o[a],i[s]);t[c++]=u<=0?o[a++]:i[s++]}for(;a<o.length;)t[c++]=o[a++];for(;s<i.length;)t[c++]=i[s++];return t}(t,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=n.length,i=r.length-1;i>=0;i--){var a=r[i],s=e.offsetAt(a.range.start),c=e.offsetAt(a.range.end);if(!(c<=o))throw new Error("Overlapping edit");n=n.substring(0,s)+a.newText+n.substring(c,n.length),o=s}return n}}(t.TextDocument||(t.TextDocument={})),function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(t.TextDocumentSaveReason||(t.TextDocumentSaveReason={}));var j,k=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=null}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=null},e.prototype.getLineOffsets=function(){if(null===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),r=0,o=t.length;if(0===o)return n.create(0,e);for(;r<o;){var i=Math.floor((r+o)/2);t[i]>e?o=i:r=i+1}var a=r-1;return n.create(a,e-t[a])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(j||(j={}))})),define("vscode-languageserver-types",["vscode-languageserver-types/main"],(function(e){return e})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/scanner",["require","exports"],e)}((function(e,t){"use strict";function n(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function r(e){return 10===e||13===e||8232===e||8233===e}function o(e){return e>=48&&e<=57}Object.defineProperty(t,"__esModule",{value:!0}),t.createScanner=function(e,t){void 0===t&&(t=!1);var i=0,a=e.length,s="",c=0,u=16,f=0,l=0,d=0,p=0,h=0;function m(t,n){for(var r=0,o=0;r<t||!n;){var a=e.charCodeAt(i);if(a>=48&&a<=57)o=16*o+a-48;else if(a>=65&&a<=70)o=16*o+a-65+10;else{if(!(a>=97&&a<=102))break;o=16*o+a-97+10}i++,r++}return r<t&&(o=-1),o}function g(){if(s="",h=0,c=i,l=f,p=d,i>=a)return c=a,u=17;var t=e.charCodeAt(i);if(n(t)){do{i++,s+=String.fromCharCode(t),t=e.charCodeAt(i)}while(n(t));return u=15}if(r(t))return i++,s+=String.fromCharCode(t),13===t&&10===e.charCodeAt(i)&&(i++,s+="\n"),f++,d=i,u=14;switch(t){case 123:return i++,u=1;case 125:return i++,u=2;case 91:return i++,u=3;case 93:return i++,u=4;case 58:return i++,u=6;case 44:return i++,u=5;case 34:return i++,s=function(){for(var t="",n=i;;){if(i>=a){t+=e.substring(n,i),h=2;break}var o=e.charCodeAt(i);if(34===o){t+=e.substring(n,i),i++;break}if(92!==o){if(o>=0&&o<=31){if(r(o)){t+=e.substring(n,i),h=2;break}h=6}i++}else{if(t+=e.substring(n,i),++i>=a){h=2;break}switch(o=e.charCodeAt(i++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var s=m(4,!0);s>=0?t+=String.fromCharCode(s):h=4;break;default:h=5}n=i}}return t}(),u=10;case 47:var g=i-1;if(47===e.charCodeAt(i+1)){for(i+=2;i<a&&!r(e.charCodeAt(i));)i++;return s=e.substring(g,i),u=12}if(42===e.charCodeAt(i+1)){i+=2;for(var y=a-1,b=!1;i<y;){var x=e.charCodeAt(i);if(42===x&&47===e.charCodeAt(i+1)){i+=2,b=!0;break}i++,r(x)&&(13===x&&10===e.charCodeAt(i)&&i++,f++,d=i)}return b||(i++,h=1),s=e.substring(g,i),u=13}return s+=String.fromCharCode(t),i++,u=16;case 45:if(s+=String.fromCharCode(t),++i===a||!o(e.charCodeAt(i)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return s+=function(){var t=i;if(48===e.charCodeAt(i))i++;else for(i++;i<e.length&&o(e.charCodeAt(i));)i++;if(i<e.length&&46===e.charCodeAt(i)){if(!(++i<e.length&&o(e.charCodeAt(i))))return h=3,e.substring(t,i);for(i++;i<e.length&&o(e.charCodeAt(i));)i++}var n=i;if(i<e.length&&(69===e.charCodeAt(i)||101===e.charCodeAt(i)))if((++i<e.length&&43===e.charCodeAt(i)||45===e.charCodeAt(i))&&i++,i<e.length&&o(e.charCodeAt(i))){for(i++;i<e.length&&o(e.charCodeAt(i));)i++;n=i}else h=3;return e.substring(t,n)}(),u=11;default:for(;i<a&&v(t);)i++,t=e.charCodeAt(i);if(c!==i){switch(s=e.substring(c,i)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return s+=String.fromCharCode(t),i++,u=16}}function v(e){if(n(e)||r(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){i=e,s="",c=0,u=16,h=0},getPosition:function(){return i},scan:t?function(){var e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:function(){return u},getTokenValue:function(){return s},getTokenOffset:function(){return c},getTokenLength:function(){return i-c},getTokenStartLine:function(){return l},getTokenStartCharacter:function(){return c-p},getTokenError:function(){return h}}}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/format",["require","exports","./scanner"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./scanner");function r(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function o(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}t.format=function(e,t,i){var a,s,c,u,f;if(t){for(u=t.offset,f=u+t.length,c=u;c>0&&!o(e,c-1);)c--;for(var l=f;l<e.length&&!o(e,l);)l++;s=e.substring(c,l),a=function(e,t){var n=0,r=0,o=t.tabSize||4;for(;n<e.length;){var i=e.charAt(n);if(" "===i)r++;else{if("\t"!==i)break;r+=o}n++}return Math.floor(r/o)}(s,i)}else s=e,a=0,c=0,u=0,f=e.length;var d,p=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(i,e),h=!1,m=0;d=i.insertSpaces?r(" ",i.tabSize||4):"\t";var g=n.createScanner(s,!1),v=!1;function y(){return p+r(d,a+m)}function b(){var e=g.scan();for(h=!1;15===e||14===e;)h=h||14===e,e=g.scan();return v=16===e||0!==g.getTokenError(),e}var x=[];function S(t,n,r){!v&&n<f&&r>u&&e.substring(n,r)!==t&&x.push({offset:n,length:r-n,content:t})}var C=b();if(17!==C){var j=g.getTokenOffset()+c;S(r(d,a),c,j)}for(;17!==C;){for(var k=g.getTokenOffset()+g.getTokenLength()+c,T=b(),O="";!h&&(12===T||13===T);){S(" ",k,g.getTokenOffset()+c),k=g.getTokenOffset()+g.getTokenLength()+c,O=12===T?y():"",T=b()}if(2===T)1!==C&&(m--,O=y());else if(4===T)3!==C&&(m--,O=y());else{switch(C){case 3:case 1:m++,O=y();break;case 5:case 12:O=y();break;case 13:O=h?y():" ";break;case 6:O=" ";break;case 10:if(6===T){O="";break}case 7:case 8:case 9:case 11:case 2:case 4:12===T||13===T?O=" ":5!==T&&17!==T&&(v=!0);break;case 16:v=!0}!h||12!==T&&13!==T||(O=y())}S(O,k,g.getTokenOffset()+c),C=T}return x},t.isEOL=o})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/parser",["require","exports","./scanner"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,r=e("./scanner");function o(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}function i(e,t,o){void 0===o&&(o=n.DEFAULT);var i=r.createScanner(e,!1);function a(e){return e?function(){return e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}var c=a(t.onObjectBegin),u=s(t.onObjectProperty),f=a(t.onObjectEnd),l=a(t.onArrayBegin),d=a(t.onArrayEnd),p=s(t.onLiteralValue),h=s(t.onSeparator),m=a(t.onComment),g=s(t.onError),v=o&&o.disallowComments,y=o&&o.allowTrailingComma;function b(){for(;;){var e=i.scan();switch(i.getTokenError()){case 4:x(14);break;case 5:x(15);break;case 3:x(13);break;case 1:v||x(11);break;case 2:x(12);break;case 6:x(16)}switch(e){case 12:case 13:v?x(10):m();break;case 16:x(1);break;case 15:case 14:break;default:return e}}}function x(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),g(e),t.length+n.length>0)for(var r=i.getToken();17!==r;){if(-1!==t.indexOf(r)){b();break}if(-1!==n.indexOf(r))break;r=b()}}function S(e){var t=i.getTokenValue();return e?p(t):u(t),b(),!0}function C(){switch(i.getToken()){case 3:return function(){l(),b();for(var e=!1;4!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||x(4,[],[]),h(","),b(),4===i.getToken()&&y)break}else e&&x(6,[],[]);C()||x(4,[],[4,5]),e=!0}return d(),4!==i.getToken()?x(8,[4],[]):b(),!0}();case 1:return function(){c(),b();for(var e=!1;2!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||x(4,[],[]),h(","),b(),2===i.getToken()&&y)break}else e&&x(6,[],[]);(10!==i.getToken()?(x(3,[],[2,5]),0):(S(!1),6===i.getToken()?(h(":"),b(),C()||x(4,[],[2,5])):x(5,[],[2,5]),1))||x(4,[],[2,5]),e=!0}return f(),2!==i.getToken()?x(7,[2],[]):b(),!0}();case 10:return S(!0);default:return function(){switch(i.getToken()){case 11:var e=0;try{"number"!=typeof(e=JSON.parse(i.getTokenValue()))&&(x(2),e=0)}catch(e){x(2)}p(e);break;case 7:p(null);break;case 8:p(!0);break;case 9:p(!1);break;default:return!1}return b(),!0}()}}return b(),17===i.getToken()||(C()?(17!==i.getToken()&&x(9,[],[]),!0):(x(4,[],[]),!1))}function a(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";default:return"null"}}!function(e){e.DEFAULT={allowTrailingComma:!1}}(n||(n={})),t.getLocation=function(e,t){var n=[],r=new Object,o=void 0,s={value:{},offset:0,length:0,type:"object",parent:void 0},c=!1;function u(e,t,n,r){s.value=e,s.offset=t,s.length=n,s.type=r,s.colonOffset=void 0,o=s}try{i(e,{onObjectBegin:function(e,i){if(t<=e)throw r;o=void 0,c=t>e,n.push("")},onObjectProperty:function(e,o,i){if(t<o)throw r;if(u(e,o,i,"property"),n[n.length-1]=e,t<=o+i)throw r},onObjectEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onArrayBegin:function(e,i){if(t<=e)throw r;o=void 0,n.push(0)},onArrayEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onLiteralValue:function(e,n,o){if(t<n)throw r;if(u(e,n,o,a(e)),t<=n+o)throw r},onSeparator:function(e,i,a){if(t<=i)throw r;if(":"===e&&o&&"property"===o.type)o.colonOffset=i,c=!1,o=void 0;else if(","===e){var s=n[n.length-1];"number"==typeof s?n[n.length-1]=s+1:(c=!0,n[n.length-1]=""),o=void 0}}})}catch(e){if(e!==r)throw e}return{path:n,previousNode:o,isAtPropertyKey:c,matches:function(e){for(var t=0,r=0;t<e.length&&r<n.length;r++)if(e[t]===n[r]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}},t.parse=function(e,t,r){void 0===t&&(t=[]),void 0===r&&(r=n.DEFAULT);var o=null,a=[],s=[];function c(e){Array.isArray(a)?a.push(e):o&&(a[o]=e)}return i(e,{onObjectBegin:function(){var e={};c(e),s.push(a),a=e,o=null},onObjectProperty:function(e){o=e},onObjectEnd:function(){a=s.pop()},onArrayBegin:function(){var e=[];c(e),s.push(a),a=e,o=null},onArrayEnd:function(){a=s.pop()},onLiteralValue:c,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},r),a[0]},t.parseTree=function(e,t,r){void 0===t&&(t=[]),void 0===r&&(r=n.DEFAULT);var o={type:"array",offset:-1,length:-1,children:[],parent:void 0};function s(e){"property"===o.type&&(o.length=e-o.offset,o=o.parent)}function c(e){return o.children.push(e),e}i(e,{onObjectBegin:function(e){o=c({type:"object",offset:e,length:-1,parent:o,children:[]})},onObjectProperty:function(e,t,n){(o=c({type:"property",offset:t,length:-1,parent:o,children:[]})).children.push({type:"string",value:e,offset:t,length:n,parent:o})},onObjectEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,s(e+t)},onArrayBegin:function(e,t){o=c({type:"array",offset:e,length:-1,parent:o,children:[]})},onArrayEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,s(e+t)},onLiteralValue:function(e,t,n){c({type:a(e),offset:t,length:n,parent:o,value:e}),s(t+n)},onSeparator:function(e,t,n){"property"===o.type&&(":"===e?o.colonOffset=t:","===e&&s(t))},onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},r);var u=o.children[0];return u&&delete u.parent,u},t.findNodeAtLocation=function(e,t){if(e){for(var n=e,r=0,o=t;r<o.length;r++){var i=o[r];if("string"==typeof i){if("object"!==n.type||!Array.isArray(n.children))return;for(var a=!1,s=0,c=n.children;s<c.length;s++){var u=c[s];if(Array.isArray(u.children)&&u.children[0].value===i){n=u.children[1],a=!0;break}}if(!a)return}else{var f=i;if("array"!==n.type||f<0||!Array.isArray(n.children)||f>=n.children.length)return;n=n.children[f]}}return n}},t.getNodePath=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var o=t.parent.children.indexOf(t);-1!==o&&n.push(o)}return n},t.getNodeValue=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,o=t.children;r<o.length;r++){var i=o[r],a=i.children[1];a&&(n[i.children[0].value]=e(a))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}},t.contains=o,t.findNodeAtOffset=function e(t,n,r){if(void 0===r&&(r=!1),o(t,n,r)){var i=t.children;if(Array.isArray(i))for(var a=0;a<i.length&&i[a].offset<=n;a++){var s=e(i[a],n,r);if(s)return s}return t}},t.visit=i,t.stripComments=function(e,t){var n,o,i=r.createScanner(e),a=[],s=0;do{switch(o=i.getPosition(),n=i.scan()){case 12:case 13:case 17:s!==o&&a.push(e.substring(s,o)),void 0!==t&&a.push(i.getTokenValue().replace(/[^\r\n]/g,t)),s=i.getPosition()}}while(17!==n);return a.join("")}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/edit",["require","exports","./format","./parser"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./format"),r=e("./parser");function o(e,t,n,o,a){for(var s,c=t.slice(),u=r.parseTree(e,[]),f=void 0,l=void 0;c.length>0&&(l=c.pop(),void 0===(f=r.findNodeAtLocation(u,c))&&void 0!==n);)"string"==typeof l?((s={})[l]=n,n=s):n=[n];if(f){if("object"===f.type&&"string"==typeof l&&Array.isArray(f.children)){var d=r.findNodeAtLocation(f,[l]);if(void 0!==d){if(void 0===n){if(!d.parent)throw new Error("Malformed AST");var p=f.children.indexOf(d.parent),h=void 0,m=d.parent.offset+d.parent.length;if(p>0)h=(S=f.children[p-1]).offset+S.length;else if(h=f.offset+1,f.children.length>1)m=f.children[1].offset;return i(e,{offset:h,length:m-h,content:""},o)}return i(e,{offset:d.offset,length:d.length,content:JSON.stringify(n)},o)}if(void 0===n)return[];var g=JSON.stringify(l)+": "+JSON.stringify(n),v=a?a(f.children.map((function(e){return e.children[0].value}))):f.children.length,y=void 0;return i(e,y=v>0?{offset:(S=f.children[v-1]).offset+S.length,length:0,content:","+g}:0===f.children.length?{offset:f.offset+1,length:0,content:g}:{offset:f.offset+1,length:0,content:g+","},o)}if("array"===f.type&&"number"==typeof l&&Array.isArray(f.children)){if(-1===l){g=""+JSON.stringify(n),y=void 0;if(0===f.children.length)y={offset:f.offset+1,length:0,content:g};else y={offset:(S=f.children[f.children.length-1]).offset+S.length,length:0,content:","+g};return i(e,y,o)}if(void 0===n&&f.children.length>=0){var b=l,x=f.children[b];y=void 0;if(1===f.children.length)y={offset:f.offset+1,length:f.length-2,content:""};else if(f.children.length-1===b){var S,C=(S=f.children[b-1]).offset+S.length;y={offset:C,length:f.offset+f.length-2-C,content:""}}else y={offset:x.offset,length:f.children[b+1].offset-x.offset,content:""};return i(e,y,o)}throw new Error("Array modification not supported yet")}throw new Error("Can not add "+("number"!=typeof l?"index":"property")+" to parent of type "+f.type)}if(void 0===n)throw new Error("Can not delete in empty document");return i(e,{offset:u?u.offset:0,length:u?u.length:0,content:JSON.stringify(n)},o)}function i(e,t,r){var o=a(e,t),i=t.offset,s=t.offset+t.content.length;if(0===t.length||0===t.content.length){for(;i>0&&!n.isEOL(o,i-1);)i--;for(;s<o.length&&!n.isEOL(o,s);)s++}for(var c=n.format(o,{offset:i,length:s-i},r),u=c.length-1;u>=0;u--){var f=c[u];o=a(o,f),i=Math.min(i,f.offset),s=Math.max(s,f.offset+f.length),s+=f.content.length-f.length}return[{offset:i,length:e.length-(o.length-s)-i,content:o.substring(i,s)}]}function a(e,t){return e.substring(0,t.offset)+t.content+e.substring(t.offset+t.length)}t.removeProperty=function(e,t,n){return o(e,t,void 0,n)},t.setProperty=o,t.applyEdit=a,t.isWS=function(e,t){return-1!=="\r\n \t".indexOf(e.charAt(t))}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/main",["require","exports","./impl/format","./impl/edit","./impl/scanner","./impl/parser"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./impl/format"),r=e("./impl/edit"),o=e("./impl/scanner"),i=e("./impl/parser");t.createScanner=o.createScanner,t.getLocation=i.getLocation,t.parse=i.parse,t.parseTree=i.parseTree,t.findNodeAtLocation=i.findNodeAtLocation,t.findNodeAtOffset=i.findNodeAtOffset,t.getNodePath=i.getNodePath,t.getNodeValue=i.getNodeValue,t.visit=i.visit,t.stripComments=i.stripComments,t.printParseErrorCode=function(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"},t.format=function(e,t,r){return n.format(e,t,r)},t.modify=function(e,t,n,o){return r.setProperty(e,t,n,o.formattingOptions,o.getInsertionIndex)},t.applyEdits=function(e,t){for(var n=t.length-1;n>=0;n--)e=r.applyEdit(e,t[n]);return e}})),define("jsonc-parser",["jsonc-parser/main"],(function(e){return e})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/objects",["require","exports"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.equals=function e(t,n){if(t===n)return!0;if(null==t||null==n)return!1;if(typeof t!=typeof n)return!1;if("object"!=typeof t)return!1;if(Array.isArray(t)!==Array.isArray(n))return!1;var r,o;if(Array.isArray(t)){if(t.length!==n.length)return!1;for(r=0;r<t.length;r++)if(!e(t[r],n[r]))return!1}else{var i=[];for(o in t)i.push(o);i.sort();var a=[];for(o in n)a.push(o);if(a.sort(),!e(i,a))return!1;for(r=0;r<i.length;r++)if(!e(t[i[r]],n[i[r]]))return!1}return!0},t.isNumber=function(e){return"number"==typeof e},t.isDefined=function(e){return void 0!==e},t.isBoolean=function(e){return"boolean"==typeof e},t.isString=function(e){return"string"==typeof e}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/jsonLanguageTypes",["require","exports","vscode-languageserver-types"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("vscode-languageserver-types");t.Range=n.Range,t.TextEdit=n.TextEdit,t.Color=n.Color,t.ColorInformation=n.ColorInformation,t.ColorPresentation=n.ColorPresentation,t.FoldingRange=n.FoldingRange,t.FoldingRangeKind=n.FoldingRangeKind,t.SelectionRange=n.SelectionRange,function(e){e[e.Undefined=0]="Undefined",e[e.EnumValueMismatch=1]="EnumValueMismatch",e[e.UnexpectedEndOfComment=257]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=258]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=259]="UnexpectedEndOfNumber",e[e.InvalidUnicode=260]="InvalidUnicode",e[e.InvalidEscapeCharacter=261]="InvalidEscapeCharacter",e[e.InvalidCharacter=262]="InvalidCharacter",e[e.PropertyExpected=513]="PropertyExpected",e[e.CommaExpected=514]="CommaExpected",e[e.ColonExpected=515]="ColonExpected",e[e.ValueExpected=516]="ValueExpected",e[e.CommaOrCloseBacketExpected=517]="CommaOrCloseBacketExpected",e[e.CommaOrCloseBraceExpected=518]="CommaOrCloseBraceExpected",e[e.TrailingComma=519]="TrailingComma",e[e.DuplicateKey=520]="DuplicateKey",e[e.CommentNotPermitted=521]="CommentNotPermitted",e[e.SchemaResolveError=768]="SchemaResolveError"}(t.ErrorCode||(t.ErrorCode={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[n.MarkupKind.Markdown,n.MarkupKind.PlainText]}}}}}(t.ClientCapabilities||(t.ClientCapabilities={}))})),define("vscode-nls/vscode-nls",["require","exports"],(function(e,t){"use strict";function n(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];return function(e,t){return 0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,n){var r=n[0];return void 0!==t[r]?t[r]:e}))}(t,n)}function r(e){return n}Object.defineProperty(t,"__esModule",{value:!0}),t.loadMessageBundle=r,t.config=function(e){return r}})),define("vscode-nls",["vscode-nls/vscode-nls"],(function(e){return e}));var __extends=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/parser/jsonParser",["require","exports","jsonc-parser","../utils/objects","../jsonLanguageTypes","vscode-nls","vscode-languageserver-types"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("jsonc-parser"),r=e("../utils/objects"),o=e("../jsonLanguageTypes"),i=e("vscode-nls"),a=e("vscode-languageserver-types"),s=i.loadMessageBundle(),c={"color-hex":{errorMessage:s("colorHexFormatWarning","Invalid color format. Use #RGB, #RGBA, #RRGGBB or #RRGGBBAA."),pattern:/^#([0-9A-Fa-f]{3,4}|([0-9A-Fa-f]{2}){3,4})$/},"date-time":{errorMessage:s("dateTimeFormatWarning","String is not a RFC3339 date-time."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},date:{errorMessage:s("dateFormatWarning","String is not a RFC3339 date."),pattern:/^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/i},time:{errorMessage:s("timeFormatWarning","String is not a RFC3339 time."),pattern:/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60)(\.[0-9]+)?(Z|(\+|-)([01][0-9]|2[0-3]):([0-5][0-9]))$/i},email:{errorMessage:s("emailFormatWarning","String is not an e-mail address."),pattern:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/}},u=function(){function e(e,t,n){this.offset=t,this.length=n,this.parent=e}return Object.defineProperty(e.prototype,"children",{get:function(){return[]},enumerable:!0,configurable:!0}),e.prototype.toString=function(){return"type: "+this.type+" ("+this.offset+"/"+this.length+")"+(this.parent?" parent: {"+this.parent.toString()+"}":"")},e}();t.ASTNodeImpl=u;var f=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="null",r.value=null,r}return __extends(t,e),t}(u);t.NullASTNodeImpl=f;var l=function(e){function t(t,n,r){var o=e.call(this,t,r)||this;return o.type="boolean",o.value=n,o}return __extends(t,e),t}(u);t.BooleanASTNodeImpl=l;var d=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="array",r.items=[],r}return __extends(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.items},enumerable:!0,configurable:!0}),t}(u);t.ArrayASTNodeImpl=d;var p=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="number",r.isInteger=!0,r.value=Number.NaN,r}return __extends(t,e),t}(u);t.NumberASTNodeImpl=p;var h=function(e){function t(t,n,r){var o=e.call(this,t,n,r)||this;return o.type="string",o.value="",o}return __extends(t,e),t}(u);t.StringASTNodeImpl=h;var m=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="property",r.colonOffset=-1,r}return __extends(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.valueNode?[this.keyNode,this.valueNode]:[this.keyNode]},enumerable:!0,configurable:!0}),t}(u);t.PropertyASTNodeImpl=m;var g=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.type="object",r.properties=[],r}return __extends(t,e),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties},enumerable:!0,configurable:!0}),t}(u);function v(e){return r.isBoolean(e)?e?{}:{not:{}}:e}t.ObjectASTNodeImpl=g,t.asSchema=v,function(e){e[e.Key=0]="Key",e[e.Enum=1]="Enum"}(t.EnumMatch||(t.EnumMatch={}));var y=function(){function e(e,t){void 0===e&&(e=-1),void 0===t&&(t=null),this.focusOffset=e,this.exclude=t,this.schemas=[]}return e.prototype.add=function(e){this.schemas.push(e)},e.prototype.merge=function(e){var t;(t=this.schemas).push.apply(t,e.schemas)},e.prototype.include=function(e){return(-1===this.focusOffset||C(e,this.focusOffset))&&e!==this.exclude},e.prototype.newSub=function(){return new e(-1,this.exclude)},e}(),b=function(){function e(){}return Object.defineProperty(e.prototype,"schemas",{get:function(){return[]},enumerable:!0,configurable:!0}),e.prototype.add=function(e){},e.prototype.merge=function(e){},e.prototype.include=function(e){return!0},e.prototype.newSub=function(){return this},e.instance=new e,e}(),x=function(){function e(){this.problems=[],this.propertiesMatches=0,this.propertiesValueMatches=0,this.primaryValueMatches=0,this.enumValueMatch=!1,this.enumValues=null}return e.prototype.hasProblems=function(){return!!this.problems.length},e.prototype.mergeAll=function(e){for(var t=0,n=e;t<n.length;t++){var r=n[t];this.merge(r)}},e.prototype.merge=function(e){this.problems=this.problems.concat(e.problems)},e.prototype.mergeEnumValues=function(e){if(!this.enumValueMatch&&!e.enumValueMatch&&this.enumValues&&e.enumValues){this.enumValues=this.enumValues.concat(e.enumValues);for(var t=0,n=this.problems;t<n.length;t++){var r=n[t];r.code===o.ErrorCode.EnumValueMismatch&&(r.message=s("enumWarning","Value is not accepted. Valid values: {0}.",this.enumValues.map((function(e){return JSON.stringify(e)})).join(", ")))}}},e.prototype.mergePropertyMatch=function(e){this.merge(e),this.propertiesMatches++,(e.enumValueMatch||!e.hasProblems()&&e.propertiesMatches)&&this.propertiesValueMatches++,e.enumValueMatch&&e.enumValues&&1===e.enumValues.length&&this.primaryValueMatches++},e.prototype.compare=function(e){var t=this.hasProblems();return t!==e.hasProblems()?t?-1:1:this.enumValueMatch!==e.enumValueMatch?e.enumValueMatch?-1:1:this.primaryValueMatches!==e.primaryValueMatches?this.primaryValueMatches-e.primaryValueMatches:this.propertiesValueMatches!==e.propertiesValueMatches?this.propertiesValueMatches-e.propertiesValueMatches:this.propertiesMatches-e.propertiesMatches},e}();function S(e){return n.getNodeValue(e)}function C(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}t.ValidationResult=x,t.newJSONDocument=function(e,t){return void 0===t&&(t=[]),new j(e,t,[])},t.getNodeValue=S,t.getNodePath=function(e){return n.getNodePath(e)},t.contains=C;var j=function(){function e(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]),this.root=e,this.syntaxErrors=t,this.comments=n}return e.prototype.getNodeFromOffset=function(e,t){if(void 0===t&&(t=!1),this.root)return n.findNodeAtOffset(this.root,e,t)},e.prototype.visit=function(e){if(this.root){var t=function(n){var r=e(n),o=n.children;if(Array.isArray(o))for(var i=0;i<o.length&&r;i++)r=t(o[i]);return r};t(this.root)}},e.prototype.validate=function(e,t){if(this.root&&t){var n=new x;return k(this.root,t,n,b.instance),n.problems.map((function(t){var n=a.Range.create(e.positionAt(t.location.offset),e.positionAt(t.location.offset+t.location.length));return a.Diagnostic.create(n,t.message,t.severity,t.code)}))}return null},e.prototype.getMatchingSchemas=function(e,t,n){void 0===t&&(t=-1),void 0===n&&(n=null);var r=new y(t,n);return this.root&&e&&k(this.root,e,new x,r),r.schemas},e}();function k(e,t,n,i){if(e&&i.include(e)){switch(e.type){case"object":!function(e,t,n,o){for(var i=Object.create(null),c=[],u=0,f=e.properties;u<f.length;u++){var l=(T=f[u]).keyNode.value;i[l]=T.valueNode,c.push(l)}if(Array.isArray(t.required))for(var d=0,p=t.required;d<p.length;d++){var h=p[d];if(!i[h]){var m=e.parent&&"property"===e.parent.type&&e.parent.keyNode,g=m?{offset:m.offset,length:m.length}:{offset:e.offset,length:1};n.problems.push({location:g,severity:a.DiagnosticSeverity.Warning,message:s("MissingRequiredPropWarning",'Missing property "{0}".',h)})}}var y=function(e){for(var t=c.indexOf(e);t>=0;)c.splice(t,1),t=c.indexOf(e)};if(t.properties)for(var S=0,C=Object.keys(t.properties);S<C.length;S++){h=C[S];y(h);var j=t.properties[h];if(V=i[h])if(r.isBoolean(j))if(j)n.propertiesMatches++,n.propertiesValueMatches++;else{var T=V.parent;n.problems.push({location:{offset:T.keyNode.offset,length:T.keyNode.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{var O=new x;k(V,j,O,o),n.mergePropertyMatch(O)}}if(t.patternProperties)for(var A=0,E=Object.keys(t.patternProperties);A<E.length;A++)for(var I=E[A],P=new RegExp(I),w=0,N=c.slice(0);w<N.length;w++){h=N[w];if(P.test(h))if(y(h),V=i[h]){j=t.patternProperties[I];if(r.isBoolean(j))if(j)n.propertiesMatches++,n.propertiesValueMatches++;else{T=V.parent;n.problems.push({location:{offset:T.keyNode.offset,length:T.keyNode.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}else{O=new x;k(V,j,O,o),n.mergePropertyMatch(O)}}}if("object"==typeof t.additionalProperties)for(var M=0,D=c;M<D.length;M++){h=D[M];if(V=i[h]){O=new x;k(V,t.additionalProperties,O,o),n.mergePropertyMatch(O)}}else if(!1===t.additionalProperties&&c.length>0)for(var F=0,_=c;F<_.length;F++){var V;h=_[F];if(V=i[h]){T=V.parent;n.problems.push({location:{offset:T.keyNode.offset,length:T.keyNode.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("DisallowedExtraPropWarning","Property {0} is not allowed.",h)})}}r.isNumber(t.maxProperties)&&e.properties.length>t.maxProperties&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("MaxPropWarning","Object has more properties than limit of {0}.",t.maxProperties)});r.isNumber(t.minProperties)&&e.properties.length<t.minProperties&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("MinPropWarning","Object has fewer properties than the required number of {0}",t.minProperties)});if(t.dependencies)for(var R=0,L=Object.keys(t.dependencies);R<L.length;R++){l=L[R];if(i[l]){var $=t.dependencies[l];if(Array.isArray($))for(var q=0,W=$;q<W.length;q++){var U=W[q];i[U]?n.propertiesValueMatches++:n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("RequiredDependentPropWarning","Object is missing property {0} required by property {1}.",U,l)})}else if(j=v($)){O=new x;k(e,j,O,o),n.mergePropertyMatch(O)}}}var K=v(t.propertyNames);if(K)for(var B=0,J=e.properties;B<J.length;B++){var H=J[B];(l=H.keyNode)&&k(l,K,n,b.instance)}}(e,t,n,i);break;case"array":!function(e,t,n,o){if(Array.isArray(t.items)){for(var i=t.items,c=0;c<i.length;c++){var u=v(i[c]),f=new x;(m=e.items[c])?(k(m,u,f,o),n.mergePropertyMatch(f)):e.items.length>=i.length&&n.propertiesValueMatches++}if(e.items.length>i.length)if("object"==typeof t.additionalItems)for(var l=i.length;l<e.items.length;l++){f=new x;k(e.items[l],t.additionalItems,f,o),n.mergePropertyMatch(f)}else!1===t.additionalItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("additionalItemsWarning","Array has too many items according to schema. Expected {0} or fewer.",i.length)})}else{var d=v(t.items);if(d)for(var p=0,h=e.items;p<h.length;p++){var m=h[p];f=new x;k(m,d,f,o),n.mergePropertyMatch(f)}}var g=v(t.contains);if(g){e.items.some((function(e){var t=new x;return k(e,g,t,b.instance),!t.hasProblems()}))||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("requiredItemMissingWarning","Array does not contain required item.")})}r.isNumber(t.minItems)&&e.items.length<t.minItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("minItemsWarning","Array has too few items. Expected {0} or more.",t.minItems)});r.isNumber(t.maxItems)&&e.items.length>t.maxItems&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("maxItemsWarning","Array has too many items. Expected {0} or fewer.",t.maxItems)});if(!0===t.uniqueItems){var y=S(e);y.some((function(e,t){return t!==y.lastIndexOf(e)}))&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("uniqueItemsWarning","Array has duplicate items.")})}}(e,t,n,i);break;case"string":!function(e,t,n,o){r.isNumber(t.minLength)&&e.value.length<t.minLength&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("minLengthWarning","String is shorter than the minimum length of {0}.",t.minLength)});r.isNumber(t.maxLength)&&e.value.length>t.maxLength&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("maxLengthWarning","String is longer than the maximum length of {0}.",t.maxLength)});if(r.isString(t.pattern)){new RegExp(t.pattern).test(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.patternErrorMessage||t.errorMessage||s("patternWarning",'String does not match the pattern of "{0}".',t.pattern)})}if(t.format)switch(t.format){case"uri":case"uri-reference":var i=void 0;if(e.value){var u=/^(([^:\/?#]+?):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/.exec(e.value);u?u[2]||"uri"!==t.format||(i=s("uriSchemeMissing","URI with a scheme is expected.")):i=s("uriMissing","URI is expected.")}else i=s("uriEmpty","URI expected.");i&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.patternErrorMessage||t.errorMessage||s("uriFormatWarning","String is not a URI: {0}",i)});break;case"color-hex":case"date-time":case"date":case"time":case"email":var f=c[t.format];e.value&&f.pattern.exec(e.value)||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.patternErrorMessage||t.errorMessage||f.errorMessage})}}(e,t,n);break;case"number":!function(e,t,n,o){var i=e.value;r.isNumber(t.multipleOf)&&i%t.multipleOf!=0&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("multipleOfWarning","Value is not divisible by {0}.",t.multipleOf)});function c(e,t){return r.isNumber(t)?t:r.isBoolean(t)&&t?e:void 0}function u(e,t){if(!r.isBoolean(t)||!t)return e}var f=c(t.minimum,t.exclusiveMinimum);r.isNumber(f)&&i<=f&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("exclusiveMinimumWarning","Value is below the exclusive minimum of {0}.",f)});var l=c(t.maximum,t.exclusiveMaximum);r.isNumber(l)&&i>=l&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("exclusiveMaximumWarning","Value is above the exclusive maximum of {0}.",l)});var d=u(t.minimum,t.exclusiveMinimum);r.isNumber(d)&&i<d&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("minimumWarning","Value is below the minimum of {0}.",d)});var p=u(t.maximum,t.exclusiveMaximum);r.isNumber(p)&&i>p&&n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("maximumWarning","Value is above the maximum of {0}.",p)})}(e,t,n);break;case"property":return k(e.valueNode,t,n,i)}!function(){function c(t){return e.type===t||"integer"===t&&"number"===e.type&&e.isInteger}Array.isArray(t.type)?t.type.some(c)||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("typeArrayMismatchWarning","Incorrect type. Expected one of {0}.",t.type.join(", "))}):t.type&&(c(t.type)||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:t.errorMessage||s("typeMismatchWarning",'Incorrect type. Expected "{0}".',t.type)}));if(Array.isArray(t.allOf))for(var u=0,f=t.allOf;u<f.length;u++){var l=f[u];k(e,v(l),n,i)}var d=v(t.not);if(d){var p=new x,h=i.newSub();k(e,d,p,h),p.hasProblems()||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,message:s("notSchemaWarning","Matches a schema that is not allowed.")});for(var m=0,g=h.schemas;m<g.length;m++){var y=g[m];y.inverted=!y.inverted,i.add(y)}}var b=function(t,r){for(var o=[],c=null,u=0,f=t;u<f.length;u++){var l=v(f[u]),d=new x,p=i.newSub();if(k(e,l,d,p),d.hasProblems()||o.push(l),c)if(r||d.hasProblems()||c.validationResult.hasProblems()){var h=d.compare(c.validationResult);h>0?c={schema:l,validationResult:d,matchingSchemas:p}:0===h&&(c.matchingSchemas.merge(p),c.validationResult.mergeEnumValues(d))}else c.matchingSchemas.merge(p),c.validationResult.propertiesMatches+=d.propertiesMatches,c.validationResult.propertiesValueMatches+=d.propertiesValueMatches;else c={schema:l,validationResult:d,matchingSchemas:p}}return o.length>1&&r&&n.problems.push({location:{offset:e.offset,length:1},severity:a.DiagnosticSeverity.Warning,message:s("oneOfWarning","Matches multiple schemas when only one must validate.")}),null!==c&&(n.merge(c.validationResult),n.propertiesMatches+=c.validationResult.propertiesMatches,n.propertiesValueMatches+=c.validationResult.propertiesValueMatches,i.merge(c.matchingSchemas)),o.length};Array.isArray(t.anyOf)&&b(t.anyOf,!1);Array.isArray(t.oneOf)&&b(t.oneOf,!0);var C=function(t){var r=new x,o=i.newSub();k(e,v(t),r,o),n.merge(r),n.propertiesMatches+=r.propertiesMatches,n.propertiesValueMatches+=r.propertiesValueMatches,i.merge(o)},j=v(t.if);j&&function(t,n,r){var o=v(t),a=new x,s=i.newSub();k(e,o,a,s),i.merge(s),a.hasProblems()?r&&C(r):n&&C(n)}(j,v(t.then),v(t.else));if(Array.isArray(t.enum)){for(var T=S(e),O=!1,A=0,E=t.enum;A<E.length;A++){var I=E[A];if(r.equals(T,I)){O=!0;break}}n.enumValues=t.enum,n.enumValueMatch=O,O||n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,code:o.ErrorCode.EnumValueMismatch,message:t.errorMessage||s("enumWarning","Value is not accepted. Valid values: {0}.",t.enum.map((function(e){return JSON.stringify(e)})).join(", "))})}if(r.isDefined(t.const)){T=S(e);r.equals(T,t.const)?n.enumValueMatch=!0:(n.problems.push({location:{offset:e.offset,length:e.length},severity:a.DiagnosticSeverity.Warning,code:o.ErrorCode.EnumValueMismatch,message:t.errorMessage||s("constWarning","Value must be {0}.",JSON.stringify(t.const))}),n.enumValueMatch=!1),n.enumValues=[t.const]}t.deprecationMessage&&e.parent&&n.problems.push({location:{offset:e.parent.offset,length:e.parent.length},severity:a.DiagnosticSeverity.Warning,message:t.deprecationMessage})}(),i.add({node:e,schema:t})}}t.JSONDocument=j,t.parse=function(e,t){var i=[],c=-1,u=e.getText(),v=n.createScanner(u,!1),y=t&&t.collectComments?[]:void 0;function b(){for(;;){var t=v.scan();switch(C(),t){case 12:case 13:Array.isArray(y)&&y.push(a.Range.create(e.positionAt(v.getTokenOffset()),e.positionAt(v.getTokenOffset()+v.getTokenLength())));break;case 15:case 14:break;default:return t}}}function x(t,n,r,o,s){if(void 0===s&&(s=a.DiagnosticSeverity.Error),0===i.length||r!==c){var u=a.Range.create(e.positionAt(r),e.positionAt(o));i.push(a.Diagnostic.create(u,t,s,n,e.languageId)),c=r}}function S(e,t,n,r,o){void 0===n&&(n=null),void 0===r&&(r=[]),void 0===o&&(o=[]);var i=v.getTokenOffset(),a=v.getTokenOffset()+v.getTokenLength();if(i===a&&i>0){for(i--;i>0&&/\s/.test(u.charAt(i));)i--;a=i+1}if(x(e,t,i,a),n&&k(n,!1),r.length+o.length>0)for(var s=v.getToken();17!==s;){if(-1!==r.indexOf(s)){b();break}if(-1!==o.indexOf(s))break;s=b()}return n}function C(){switch(v.getTokenError()){case 4:return S(s("InvalidUnicode","Invalid unicode sequence in string."),o.ErrorCode.InvalidUnicode),!0;case 5:return S(s("InvalidEscapeCharacter","Invalid escape character in string."),o.ErrorCode.InvalidEscapeCharacter),!0;case 3:return S(s("UnexpectedEndOfNumber","Unexpected end of number."),o.ErrorCode.UnexpectedEndOfNumber),!0;case 1:return S(s("UnexpectedEndOfComment","Unexpected end of comment."),o.ErrorCode.UnexpectedEndOfComment),!0;case 2:return S(s("UnexpectedEndOfString","Unexpected end of string."),o.ErrorCode.UnexpectedEndOfString),!0;case 6:return S(s("InvalidCharacter","Invalid characters in string. Control characters must be escaped."),o.ErrorCode.InvalidCharacter),!0}return!1}function k(e,t){return e.length=v.getTokenOffset()+v.getTokenLength()-e.offset,t&&b(),e}function T(t,n){var r=new m(t,v.getTokenOffset()),i=O(r);if(!i){if(16!==v.getToken())return null;S(s("DoubleQuotesExpected","Property keys must be doublequoted"),o.ErrorCode.Undefined);var c=new h(r,v.getTokenOffset(),v.getTokenLength());c.value=v.getTokenValue(),i=c,b()}r.keyNode=i;var u=n[i.value];if(u?(x(s("DuplicateKeyWarning","Duplicate object key"),o.ErrorCode.DuplicateKey,r.keyNode.offset,r.keyNode.offset+r.keyNode.length,a.DiagnosticSeverity.Warning),"object"==typeof u&&x(s("DuplicateKeyWarning","Duplicate object key"),o.ErrorCode.DuplicateKey,u.keyNode.offset,u.keyNode.offset+u.keyNode.length,a.DiagnosticSeverity.Warning),n[i.value]=!0):n[i.value]=r,6===v.getToken())r.colonOffset=v.getTokenOffset(),b();else if(S(s("ColonExpected","Colon expected"),o.ErrorCode.ColonExpected),10===v.getToken()&&e.positionAt(i.offset+i.length).line<e.positionAt(v.getTokenOffset()).line)return r.length=i.length,r;var f=A(r,i.value);return f?(r.valueNode=f,r.length=f.offset+f.length-r.offset,r):S(s("ValueExpected","Value expected"),o.ErrorCode.ValueExpected,r,[],[2,5])}function O(e){if(10!==v.getToken())return null;var t=new h(e,v.getTokenOffset());return t.value=v.getTokenValue(),k(t,!0)}function A(e,t){return function(e){if(3!==v.getToken())return null;var t=new d(e,v.getTokenOffset());b();for(var n=0,r=!1;4!==v.getToken()&&17!==v.getToken();){if(5===v.getToken()){r||S(s("ValueExpected","Value expected"),o.ErrorCode.ValueExpected);var i=v.getTokenOffset();if(b(),4===v.getToken()){r&&x(s("TrailingComma","Trailing comma"),o.ErrorCode.TrailingComma,i,i+1);continue}}else r&&S(s("ExpectedComma","Expected comma"),o.ErrorCode.CommaExpected);var a=A(t,n++);a?t.items.push(a):S(s("PropertyExpected","Value expected"),o.ErrorCode.ValueExpected,null,[],[4,5]),r=!0}return 4!==v.getToken()?S(s("ExpectedCloseBracket","Expected comma or closing bracket"),o.ErrorCode.CommaOrCloseBacketExpected,t):k(t,!0)}(e)||function(e){if(1!==v.getToken())return null;var t=new g(e,v.getTokenOffset()),n=Object.create(null);b();for(var r=!1;2!==v.getToken()&&17!==v.getToken();){if(5===v.getToken()){r||S(s("PropertyExpected","Property expected"),o.ErrorCode.PropertyExpected);var i=v.getTokenOffset();if(b(),2===v.getToken()){r&&x(s("TrailingComma","Trailing comma"),o.ErrorCode.TrailingComma,i,i+1);continue}}else r&&S(s("ExpectedComma","Expected comma"),o.ErrorCode.CommaExpected);var a=T(t,n);a?t.properties.push(a):S(s("PropertyExpected","Property expected"),o.ErrorCode.PropertyExpected,null,[],[2,5]),r=!0}return 2!==v.getToken()?S(s("ExpectedCloseBrace","Expected comma or closing brace"),o.ErrorCode.CommaOrCloseBraceExpected,t):k(t,!0)}(e)||O(e)||function(e){if(11!==v.getToken())return null;var t=new p(e,v.getTokenOffset());if(0===v.getTokenError()){var n=v.getTokenValue();try{var i=JSON.parse(n);if(!r.isNumber(i))return S(s("InvalidNumberFormat","Invalid number format."),o.ErrorCode.Undefined,t);t.value=i}catch(e){return S(s("InvalidNumberFormat","Invalid number format."),o.ErrorCode.Undefined,t)}t.isInteger=-1===n.indexOf(".")}return k(t,!0)}(e)||function(e){switch(v.getToken()){case 7:return k(new f(e,v.getTokenOffset()),!0);case 8:return k(new l(e,!0,v.getTokenOffset()),!0);case 9:return k(new l(e,!1,v.getTokenOffset()),!0);default:return null}}(e)}var E=null;return 17!==b()&&((E=A(null))?17!==v.getToken()&&S(s("End of file expected","End of file expected."),o.ErrorCode.Undefined):S(s("Invalid symbol","Expected a JSON object, array or literal."),o.ErrorCode.Undefined)),new j(E,i,y)}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/json",["require","exports"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stringifyObject=function e(t,n,r){if(null!==t&&"object"==typeof t){var o=n+"\t";if(Array.isArray(t)){if(0===t.length)return"[]";for(var i="[\n",a=0;a<t.length;a++)i+=o+e(t[a],o,r),a<t.length-1&&(i+=","),i+="\n";return i+=n+"]"}var s=Object.keys(t);if(0===s.length)return"{}";for(i="{\n",a=0;a<s.length;a++){var c=s[a];i+=o+JSON.stringify(c)+": "+e(t[c],o,r),a<s.length-1&&(i+=","),i+="\n"}return i+=n+"}"}return r(t)}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/strings",["require","exports"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startsWith=function(e,t){if(e.length<t.length)return!1;for(var n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0},t.endsWith=function(e,t){var n=e.length-t.length;return n>0?e.lastIndexOf(t)===n:0===n&&e===t},t.convertSimple2RegExpPattern=function(e){return e.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")},t.repeat=function(e,t){for(var n="";t>0;)1==(1&t)&&(n+=e),e+=e,t>>>=1;return n}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonCompletion",["require","exports","../parser/jsonParser","jsonc-parser","../utils/json","../utils/strings","../utils/objects","vscode-languageserver-types","vscode-nls"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("../parser/jsonParser"),r=e("jsonc-parser"),o=e("../utils/json"),i=e("../utils/strings"),a=e("../utils/objects"),s=e("vscode-languageserver-types"),c=e("vscode-nls").loadMessageBundle(),u=function(){function e(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=Promise),void 0===r&&(r={}),this.schemaService=e,this.contributions=t,this.promiseConstructor=n,this.clientCapabilities=r,this.templateVarIdCounter=0}return e.prototype.doResolve=function(e){for(var t=this.contributions.length-1;t>=0;t--)if(this.contributions[t].resolveCompletion){var n=this.contributions[t].resolveCompletion(e);if(n)return n}return this.promiseConstructor.resolve(e)},e.prototype.doComplete=function(e,t,r){var o=this,i={items:[],isIncomplete:!1},a=e.offsetAt(t),c=r.getNodeFromOffset(a,!0);if(this.isInComment(e,c?c.offset:0,a))return Promise.resolve(i);var u=this.getCurrentWord(e,a),f=null;if(!c||"string"!==c.type&&"number"!==c.type&&"boolean"!==c.type&&"null"!==c.type){var l=a-u.length;l>0&&'"'===e.getText()[l-1]&&l--,f=s.Range.create(e.positionAt(l),t)}else f=s.Range.create(e.positionAt(c.offset),e.positionAt(c.offset+c.length));var d={},p={add:function(e){var t=d[e.label];t?t.documentation||(t.documentation=e.documentation):(d[e.label]=e,f&&(e.textEdit=s.TextEdit.replace(f,e.insertText)),i.items.push(e))},setAsIncomplete:function(){i.isIncomplete=!0},error:function(e){console.error(e)},log:function(e){console.log(e)},getNumberOfProposals:function(){return i.items.length}};return this.schemaService.getSchemaForResource(e.uri,r).then((function(t){var l=[],h=!0,m="",g=null;if(c&&"string"===c.type){var v=c.parent;v&&"property"===v.type&&v.keyNode===c&&(h=!v.valueNode,g=v,m=e.getText().substr(c.offset+1,c.length-2),v&&(c=v.parent))}if(c&&"object"===c.type){if(c.offset===a)return i;c.properties.forEach((function(e){g&&g===e||(d[e.keyNode.value]=s.CompletionItem.create("__"))}));var y="";h&&(y=o.evaluateSeparatorAfter(e,e.offsetAt(f.end))),t?o.getPropertyCompletions(t,r,c,h,y,p):o.getSchemaLessPropertyCompletions(r,c,m,p);var b=n.getNodePath(c);o.contributions.forEach((function(t){var n=t.collectPropertyCompletions(e.uri,b,u,h,""===y,p);n&&l.push(n)})),!t&&u.length>0&&'"'!==e.getText().charAt(a-u.length-1)&&(p.add({kind:s.CompletionItemKind.Property,label:o.getLabelForValue(u),insertText:o.getInsertTextForProperty(u,null,!1,y),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""}),p.setAsIncomplete())}var x={};return t?o.getValueCompletions(t,r,c,a,e,p,x):o.getSchemaLessValueCompletions(r,c,a,e,p),o.contributions.length>0&&o.getContributedValueCompletions(r,c,a,e,p,l),o.promiseConstructor.all(l).then((function(){if(0===p.getNumberOfProposals()){var t=a;!c||"string"!==c.type&&"number"!==c.type&&"boolean"!==c.type&&"null"!==c.type||(t=c.offset+c.length);var n=o.evaluateSeparatorAfter(e,t);o.addFillerValueCompletions(x,n,p)}return i}))}))},e.prototype.getPropertyCompletions=function(e,t,n,r,o,a){var c=this;t.getMatchingSchemas(e.schema,n.offset).forEach((function(e){if(e.node===n&&!e.inverted){var t=e.schema.properties;t&&Object.keys(t).forEach((function(e){var n=t[e];if("object"==typeof n&&!n.deprecationMessage&&!n.doNotSuggest){var u={kind:s.CompletionItemKind.Property,label:c.sanitizeLabel(e),insertText:c.getInsertTextForProperty(e,n,r,o),insertTextFormat:s.InsertTextFormat.Snippet,filterText:c.getFilterTextForValue(e),documentation:c.fromMarkup(n.markdownDescription)||n.description||""};i.endsWith(u.insertText,"$1"+o)&&(u.command={title:"Suggest",command:"editor.action.triggerSuggest"}),a.add(u)}}))}}))},e.prototype.getSchemaLessPropertyCompletions=function(e,t,n,r){var o=this,i=function(e){e.properties.forEach((function(e){var t=e.keyNode.value;r.add({kind:s.CompletionItemKind.Property,label:o.sanitizeLabel(t),insertText:o.getInsertTextForValue(t,""),insertTextFormat:s.InsertTextFormat.Snippet,filterText:o.getFilterTextForValue(t),documentation:""})}))};if(t.parent)if("property"===t.parent.type){var a=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e!==t.parent&&e.keyNode.value===a&&e.valueNode&&"object"===e.valueNode.type&&i(e.valueNode),!0}))}else"array"===t.parent.type&&t.parent.items.forEach((function(e){"object"===e.type&&e!==t&&i(e)}));else"object"===t.type&&r.add({kind:s.CompletionItemKind.Property,label:"$schema",insertText:this.getInsertTextForProperty("$schema",null,!0,""),insertTextFormat:s.InsertTextFormat.Snippet,documentation:"",filterText:this.getFilterTextForValue("$schema")})},e.prototype.getSchemaLessValueCompletions=function(e,t,r,o,i){var a=this,c=r;if(!t||"string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(c=t.offset+t.length,t=t.parent),!t)return i.add({kind:this.getSuggestionKind("object"),label:"Empty object",insertText:this.getInsertTextForValue({},""),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""}),void i.add({kind:this.getSuggestionKind("array"),label:"Empty array",insertText:this.getInsertTextForValue([],""),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""});var u=this.evaluateSeparatorAfter(o,c),f=function(e){n.contains(e.parent,r,!0)||i.add({kind:a.getSuggestionKind(e.type),label:a.getLabelTextForMatchingNode(e,o),insertText:a.getInsertTextForMatchingNode(e,o,u),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""}),"boolean"===e.type&&a.addBooleanValueCompletion(!e.value,u,i)};if("property"===t.type&&r>t.colonOffset){var l=t.valueNode;if(l&&(r>l.offset+l.length||"object"===l.type||"array"===l.type))return;var d=t.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===d&&e.valueNode&&f(e.valueNode),!0})),"$schema"===d&&t.parent&&!t.parent.parent&&this.addDollarSchemaCompletions(u,i)}if("array"===t.type)if(t.parent&&"property"===t.parent.type){var p=t.parent.keyNode.value;e.visit((function(e){return"property"===e.type&&e.keyNode.value===p&&e.valueNode&&"array"===e.valueNode.type&&e.valueNode.items.forEach(f),!0}))}else t.items.forEach(f)},e.prototype.getValueCompletions=function(e,t,n,r,o,i,a){var s=this,c=r,u=null,f=null;if(!n||"string"!==n.type&&"number"!==n.type&&"boolean"!==n.type&&"null"!==n.type||(c=n.offset+n.length,f=n,n=n.parent),n){if("property"===n.type&&r>n.colonOffset){var l=n.valueNode;if(l&&r>l.offset+l.length)return;u=n.keyNode.value,n=n.parent}if(n&&(null!==u||"array"===n.type)){var d=this.evaluateSeparatorAfter(o,c);t.getMatchingSchemas(e.schema,n.offset,f).forEach((function(e){if(e.node===n&&!e.inverted&&e.schema){if("array"===n.type&&e.schema.items)if(Array.isArray(e.schema.items)){var t=s.findItemAtOffset(n,o,r);t<e.schema.items.length&&s.addSchemaValueCompletions(e.schema.items[t],d,i,a)}else s.addSchemaValueCompletions(e.schema.items,d,i,a);if(e.schema.properties){var c=e.schema.properties[u];c&&s.addSchemaValueCompletions(c,d,i,a)}}})),"$schema"!==u||n.parent||this.addDollarSchemaCompletions(d,i),a.boolean&&(this.addBooleanValueCompletion(!0,d,i),this.addBooleanValueCompletion(!1,d,i)),a.null&&this.addNullValueCompletion(d,i)}}else this.addSchemaValueCompletions(e.schema,"",i,a)},e.prototype.getContributedValueCompletions=function(e,t,r,o,i,a){if(t){if("string"!==t.type&&"number"!==t.type&&"boolean"!==t.type&&"null"!==t.type||(t=t.parent),"property"===t.type&&r>t.colonOffset){var s=t.keyNode.value,c=t.valueNode;if(!c||r<=c.offset+c.length){var u=n.getNodePath(t.parent);this.contributions.forEach((function(e){var t=e.collectValueCompletions(o.uri,u,s,i);t&&a.push(t)}))}}}else this.contributions.forEach((function(e){var t=e.collectDefaultCompletions(o.uri,i);t&&a.push(t)}))},e.prototype.addSchemaValueCompletions=function(e,t,n,r){var o=this;"object"==typeof e&&(this.addEnumValueCompletions(e,t,n),this.addDefaultValueCompletions(e,t,n),this.collectTypes(e,r),Array.isArray(e.allOf)&&e.allOf.forEach((function(e){return o.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.anyOf)&&e.anyOf.forEach((function(e){return o.addSchemaValueCompletions(e,t,n,r)})),Array.isArray(e.oneOf)&&e.oneOf.forEach((function(e){return o.addSchemaValueCompletions(e,t,n,r)})))},e.prototype.addDefaultValueCompletions=function(e,t,n,r){var o=this;void 0===r&&(r=0);var i=!1;if(a.isDefined(e.default)){for(var u=e.type,f=e.default,l=r;l>0;l--)f=[f],u="array";n.add({kind:this.getSuggestionKind(u),label:this.getLabelForValue(f),insertText:this.getInsertTextForValue(f,t),insertTextFormat:s.InsertTextFormat.Snippet,detail:c("json.suggest.default","Default value")}),i=!0}Array.isArray(e.examples)&&e.examples.forEach((function(a){for(var c=e.type,u=a,f=r;f>0;f--)u=[u],c="array";n.add({kind:o.getSuggestionKind(c),label:o.getLabelForValue(u),insertText:o.getInsertTextForValue(u,t),insertTextFormat:s.InsertTextFormat.Snippet}),i=!0})),Array.isArray(e.defaultSnippets)&&e.defaultSnippets.forEach((function(c){var u,f,l=e.type,d=c.body,p=c.label;if(a.isDefined(d)){e.type;for(var h=r;h>0;h--)d=[d],"array";u=o.getInsertTextForSnippetValue(d,t),f=o.getFilterTextForSnippetValue(d),p=p||o.getLabelForSnippetValue(d)}else if("string"==typeof c.bodyText){var m="",g="",v="";for(h=r;h>0;h--)m=m+v+"[\n",g=g+"\n"+v+"]",v+="\t",l="array";u=m+v+c.bodyText.split("\n").join("\n"+v)+g+t,p=p||o.sanitizeLabel(u),f=u.replace(/[\n]/g,"")}n.add({kind:o.getSuggestionKind(l),label:p,documentation:o.fromMarkup(c.markdownDescription)||c.description,insertText:u,insertTextFormat:s.InsertTextFormat.Snippet,filterText:f}),i=!0})),i||"object"!=typeof e.items||Array.isArray(e.items)||this.addDefaultValueCompletions(e.items,t,n,r+1)},e.prototype.addEnumValueCompletions=function(e,t,n){if(a.isDefined(e.const)&&n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(e.const),insertText:this.getInsertTextForValue(e.const,t),insertTextFormat:s.InsertTextFormat.Snippet,documentation:this.fromMarkup(e.markdownDescription)||e.description}),Array.isArray(e.enum))for(var r=0,o=e.enum.length;r<o;r++){var i=e.enum[r],c=this.fromMarkup(e.markdownDescription)||e.description;e.markdownEnumDescriptions&&r<e.markdownEnumDescriptions.length&&this.doesSupportMarkdown()?c=this.fromMarkup(e.markdownEnumDescriptions[r]):e.enumDescriptions&&r<e.enumDescriptions.length&&(c=e.enumDescriptions[r]),n.add({kind:this.getSuggestionKind(e.type),label:this.getLabelForValue(i),insertText:this.getInsertTextForValue(i,t),insertTextFormat:s.InsertTextFormat.Snippet,documentation:c})}},e.prototype.collectTypes=function(e,t){if(!Array.isArray(e.enum)&&!a.isDefined(e.const)){var n=e.type;Array.isArray(n)?n.forEach((function(e){return t[e]=!0})):t[n]=!0}},e.prototype.addFillerValueCompletions=function(e,t,n){e.object&&n.add({kind:this.getSuggestionKind("object"),label:"{}",insertText:this.getInsertTextForGuessedValue({},t),insertTextFormat:s.InsertTextFormat.Snippet,detail:c("defaults.object","New object"),documentation:""}),e.array&&n.add({kind:this.getSuggestionKind("array"),label:"[]",insertText:this.getInsertTextForGuessedValue([],t),insertTextFormat:s.InsertTextFormat.Snippet,detail:c("defaults.array","New array"),documentation:""})},e.prototype.addBooleanValueCompletion=function(e,t,n){n.add({kind:this.getSuggestionKind("boolean"),label:e?"true":"false",insertText:this.getInsertTextForValue(e,t),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""})},e.prototype.addNullValueCompletion=function(e,t){t.add({kind:this.getSuggestionKind("null"),label:"null",insertText:"null"+e,insertTextFormat:s.InsertTextFormat.Snippet,documentation:""})},e.prototype.addDollarSchemaCompletions=function(e,t){var n=this;this.schemaService.getRegisteredSchemaIds((function(e){return"http"===e||"https"===e})).forEach((function(r){return t.add({kind:s.CompletionItemKind.Module,label:n.getLabelForValue(r),filterText:n.getFilterTextForValue(r),insertText:n.getInsertTextForValue(r,e),insertTextFormat:s.InsertTextFormat.Snippet,documentation:""})}))},e.prototype.sanitizeLabel=function(e){return(e=e.replace(/[\n]/g,"↵")).length>57&&(e=e.substr(0,57).trim()+"..."),e},e.prototype.getLabelForValue=function(e){return this.sanitizeLabel(JSON.stringify(e))},e.prototype.getFilterTextForValue=function(e){return JSON.stringify(e)},e.prototype.getFilterTextForSnippetValue=function(e){return JSON.stringify(e).replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1")},e.prototype.getLabelForSnippetValue=function(e){var t=JSON.stringify(e);return t=t.replace(/\$\{\d+:([^}]+)\}|\$\d+/g,"$1"),this.sanitizeLabel(t)},e.prototype.getInsertTextForPlainText=function(e){return e.replace(/[\\\$\}]/g,"\\$&")},e.prototype.getInsertTextForValue=function(e,t){var n=JSON.stringify(e,null,"\t");return"{}"===n?"{$1}"+t:"[]"===n?"[$1]"+t:this.getInsertTextForPlainText(n+t)},e.prototype.getInsertTextForSnippetValue=function(e,t){return o.stringifyObject(e,"",(function(e){return"string"==typeof e&&"^"===e[0]?e.substr(1):JSON.stringify(e)}))+t},e.prototype.getInsertTextForGuessedValue=function(e,t){switch(typeof e){case"object":return null===e?"${1:null}"+t:this.getInsertTextForValue(e,t);case"string":var n=JSON.stringify(e);return n=n.substr(1,n.length-2),'"${1:'+(n=this.getInsertTextForPlainText(n))+'}"'+t;case"number":case"boolean":return"${1:"+JSON.stringify(e)+"}"+t}return this.getInsertTextForValue(e,t)},e.prototype.getSuggestionKind=function(e){if(Array.isArray(e)){var t=e;e=t.length>0?t[0]:null}if(!e)return s.CompletionItemKind.Value;switch(e){case"string":return s.CompletionItemKind.Value;case"object":return s.CompletionItemKind.Module;case"property":return s.CompletionItemKind.Property;default:return s.CompletionItemKind.Value}},e.prototype.getLabelTextForMatchingNode=function(e,t){switch(e.type){case"array":return"[]";case"object":return"{}";default:return t.getText().substr(e.offset,e.length)}},e.prototype.getInsertTextForMatchingNode=function(e,t,n){switch(e.type){case"array":return this.getInsertTextForValue([],n);case"object":return this.getInsertTextForValue({},n);default:var r=t.getText().substr(e.offset,e.length)+n;return this.getInsertTextForPlainText(r)}},e.prototype.getInsertTextForProperty=function(e,t,n,r){var o=this.getInsertTextForValue(e,"");if(!n)return o;var i,s=o+": ",c=0;if(t){if(Array.isArray(t.defaultSnippets)){if(1===t.defaultSnippets.length){var u=t.defaultSnippets[0].body;a.isDefined(u)&&(i=this.getInsertTextForSnippetValue(u,""))}c+=t.defaultSnippets.length}if(t.enum&&(i||1!==t.enum.length||(i=this.getInsertTextForGuessedValue(t.enum[0],"")),c+=t.enum.length),a.isDefined(t.default)&&(i||(i=this.getInsertTextForGuessedValue(t.default,"")),c++),0===c){var f=Array.isArray(t.type)?t.type[0]:t.type;switch(f||(t.properties?f="object":t.items&&(f="array")),f){case"boolean":i="$1";break;case"string":i='"$1"';break;case"object":i="{$1}";break;case"array":i="[$1]";break;case"number":case"integer":i="${1:0}";break;case"null":i="${1:null}";break;default:return o}}}return(!i||c>1)&&(i="$1"),s+i+r},e.prototype.getCurrentWord=function(e,t){for(var n=t-1,r=e.getText();n>=0&&-1===' \t\n\r\v":{[,]}'.indexOf(r.charAt(n));)n--;return r.substring(n+1,t)},e.prototype.evaluateSeparatorAfter=function(e,t){var n=r.createScanner(e.getText(),!0);switch(n.setPosition(t),n.scan()){case 5:case 2:case 4:case 17:return"";default:return","}},e.prototype.findItemAtOffset=function(e,t,n){for(var o=r.createScanner(t.getText(),!0),i=e.items,a=i.length-1;a>=0;a--){var s=i[a];if(n>s.offset+s.length)return o.setPosition(s.offset+s.length),5===o.scan()&&n>=o.getTokenOffset()+o.getTokenLength()?a+1:a;if(n>=s.offset)return a}return 0},e.prototype.isInComment=function(e,t,n){var o=r.createScanner(e.getText(),!1);o.setPosition(t);for(var i=o.scan();17!==i&&o.getTokenOffset()+o.getTokenLength()<n;)i=o.scan();return(12===i||13===i)&&o.getTokenOffset()<=n},e.prototype.fromMarkup=function(e){if(e&&this.doesSupportMarkdown())return{kind:s.MarkupKind.Markdown,value:e}},e.prototype.doesSupportMarkdown=function(){if(!a.isDefined(this.supportsMarkdown)){var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(s.MarkupKind.Markdown)}return this.supportsMarkdown},e}();t.JSONCompletion=u})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonHover",["require","exports","../parser/jsonParser","vscode-languageserver-types"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("../parser/jsonParser"),r=e("vscode-languageserver-types"),o=function(){function e(e,t,n){void 0===t&&(t=[]),this.schemaService=e,this.contributions=t,this.promise=n||Promise}return e.prototype.doHover=function(e,t,o){var a=e.offsetAt(t),s=o.getNodeFromOffset(a);if(!s||("object"===s.type||"array"===s.type)&&a>s.offset+1&&a<s.offset+s.length-1)return this.promise.resolve(null);var c=s;if("string"===s.type){var u=s.parent;if(u&&"property"===u.type&&u.keyNode===s&&!(s=u.valueNode))return this.promise.resolve(null)}for(var f=r.Range.create(e.positionAt(c.offset),e.positionAt(c.offset+c.length)),l=function(e){return{contents:e,range:f}},d=n.getNodePath(s),p=this.contributions.length-1;p>=0;p--){var h=this.contributions[p].getInfoContribution(e.uri,d);if(h)return h.then((function(e){return l(e)}))}return this.schemaService.getSchemaForResource(e.uri,o).then((function(e){if(e){var t=o.getMatchingSchemas(e.schema,s.offset),r=null,a=null,c=null,u=null;t.every((function(e){if(e.node===s&&!e.inverted&&e.schema&&(r=r||e.schema.title,a=a||e.schema.markdownDescription||i(e.schema.description),e.schema.enum)){var t=e.schema.enum.indexOf(n.getNodeValue(s));e.schema.markdownEnumDescriptions?c=e.schema.markdownEnumDescriptions[t]:e.schema.enumDescriptions&&(c=i(e.schema.enumDescriptions[t])),c&&"string"!=typeof(u=e.schema.enum[t])&&(u=JSON.stringify(u))}return!0}));var f="";return r&&(f=i(r)),a&&(f.length>0&&(f+="\n\n"),f+=a),c&&(f.length>0&&(f+="\n\n"),f+="`"+function(e){if(-1!==e.indexOf("`"))return"`` "+e+" ``";return e}(u)+"`: "+c),l([f])}return null}))},e}();function i(e){if(e)return e.replace(/([^\n\r])(\r?\n)([^\n\r])/gm,"$1\n\n$3").replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}t.JSONHover=o}));__extends=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-uri/index",["require","exports"],e)}((function(e,t){"use strict";var n;if(Object.defineProperty(t,"__esModule",{value:!0}),"object"==typeof process)n="win32"===process.platform;else if("object"==typeof navigator){var r=navigator.userAgent;n=r.indexOf("Windows")>=0}var o=/^\w[\w\d+.-]*$/,i=/^\//,a=/^\/\//,s=!0;t.setUriThrowOnMissingScheme=function(e){var t=s;return s=e,t};var c="",u="/",f=/^(([^:\/?#]+?):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,l=function(){function e(e,t,n,r,f,l){void 0===l&&(l=!1),"object"==typeof e?(this.scheme=e.scheme||c,this.authority=e.authority||c,this.path=e.path||c,this.query=e.query||c,this.fragment=e.fragment||c):(this.scheme=function(e,t){return t||s?e||c:(e||(e="file"),e)}(e,l),this.authority=t||c,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==u&&(t=u+t):t=u}return t}(this.scheme,n||c),this.query=r||c,this.fragment=f||c,function(e,t){if(!e.scheme&&(t||s))throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!o.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!i.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(a.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,l))}return e.isUri=function(t){return t instanceof e||!!t&&("string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString)},Object.defineProperty(e.prototype,"fsPath",{get:function(){return y(this)},enumerable:!0,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,o=e.query,i=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=c),void 0===n?n=this.authority:null===n&&(n=c),void 0===r?r=this.path:null===r&&(r=c),void 0===o?o=this.query:null===o&&(o=c),void 0===i?i=this.fragment:null===i&&(i=c),t===this.scheme&&n===this.authority&&r===this.path&&o===this.query&&i===this.fragment?this:new h(t,n,r,o,i)},e.parse=function(e,t){void 0===t&&(t=!1);var n=f.exec(e);return n?new h(n[2]||c,decodeURIComponent(n[4]||c),decodeURIComponent(n[5]||c),decodeURIComponent(n[7]||c),decodeURIComponent(n[9]||c),t):new h(c,c,c,c,c)},e.file=function(e){var t=c;if(n&&(e=e.replace(/\\/g,u)),e[0]===u&&e[1]===u){var r=e.indexOf(u,2);-1===r?(t=e.substring(2),e=u):(t=e.substring(2,r),e=e.substring(r)||u)}return new h("file",t,e,c,c)},e.from=function(e){return new h(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),b(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new h(t);return n._formatted=t.external,n._fsPath=t._sep===p?t.fsPath:null,n}return t},e}();t.URI=l;var d,p=n?1:void 0,h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return __extends(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=y(this)),this._fsPath},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?b(this,!0):(this._formatted||(this._formatted=b(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=p),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(l),m=((d={})[58]="%3A",d[47]="%2F",d[63]="%3F",d[35]="%23",d[91]="%5B",d[93]="%5D",d[64]="%40",d[33]="%21",d[36]="%24",d[38]="%26",d[39]="%27",d[40]="%28",d[41]="%29",d[42]="%2A",d[43]="%2B",d[44]="%2C",d[59]="%3B",d[61]="%3D",d[32]="%20",d);function g(e,t){for(var n=void 0,r=-1,o=0;o<e.length;o++){var i=e.charCodeAt(o);if(i>=97&&i<=122||i>=65&&i<=90||i>=48&&i<=57||45===i||46===i||95===i||126===i||t&&47===i)-1!==r&&(n+=encodeURIComponent(e.substring(r,o)),r=-1),void 0!==n&&(n+=e.charAt(o));else{void 0===n&&(n=e.substr(0,o));var a=m[i];void 0!==a?(-1!==r&&(n+=encodeURIComponent(e.substring(r,o)),r=-1),n+=a):-1===r&&(r=o)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function v(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=m[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function y(e){var t;return t=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?e.path[1].toLowerCase()+e.path.substr(2):e.path,n&&(t=t.replace(/\//g,"\\")),t}function b(e,t){var n=t?v:g,r="",o=e.scheme,i=e.authority,a=e.path,s=e.query,c=e.fragment;if(o&&(r+=o,r+=":"),(i||"file"===o)&&(r+=u,r+=u),i){var f=i.indexOf("@");if(-1!==f){var l=i.substr(0,f);i=i.substr(f+1),-1===(f=l.indexOf(":"))?r+=n(l,!1):(r+=n(l.substr(0,f),!1),r+=":",r+=n(l.substr(f+1),!1)),r+="@"}-1===(f=(i=i.toLowerCase()).indexOf(":"))?r+=n(i,!1):(r+=n(i.substr(0,f),!1),r+=i.substr(f))}if(a){if(a.length>=3&&47===a.charCodeAt(0)&&58===a.charCodeAt(2))(d=a.charCodeAt(1))>=65&&d<=90&&(a="/"+String.fromCharCode(d+32)+":"+a.substr(3));else if(a.length>=2&&58===a.charCodeAt(1)){var d;(d=a.charCodeAt(0))>=65&&d<=90&&(a=String.fromCharCode(d+32)+":"+a.substr(2))}r+=n(a,!0)}return s&&(r+="?",r+=n(s,!1)),c&&(r+="#",r+=t?c:g(c,!1)),r}})),define("vscode-uri",["vscode-uri/index"],(function(e){return e})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonSchemaService",["require","exports","jsonc-parser","vscode-uri","../utils/strings","../parser/jsonParser","vscode-nls"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("jsonc-parser"),r=e("vscode-uri"),o=e("../utils/strings"),i=e("../parser/jsonParser"),a=e("vscode-nls").loadMessageBundle(),s=function(){function e(e){try{this.patternRegExp=new RegExp(o.convertSimple2RegExpPattern(e)+"$")}catch(e){this.patternRegExp=null}this.schemas=[]}return e.prototype.addSchema=function(e){this.schemas.push(e)},e.prototype.matchesPattern=function(e){return this.patternRegExp&&this.patternRegExp.test(e)},e.prototype.getSchemas=function(){return this.schemas},e}(),c=function(){function e(e,t,n){this.service=e,this.url=t,this.dependencies={},n&&(this.unresolvedSchema=this.service.promise.resolve(new u(n)))}return e.prototype.getUnresolvedSchema=function(){return this.unresolvedSchema||(this.unresolvedSchema=this.service.loadSchema(this.url)),this.unresolvedSchema},e.prototype.getResolvedSchema=function(){var e=this;return this.resolvedSchema||(this.resolvedSchema=this.getUnresolvedSchema().then((function(t){return e.service.resolveSchemaContent(t,e.url,e.dependencies)}))),this.resolvedSchema},e.prototype.clearSchema=function(){this.resolvedSchema=null,this.unresolvedSchema=null,this.dependencies={}},e}(),u=function(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t};t.UnresolvedSchema=u;var f=function(){function e(e,t){void 0===t&&(t=[]),this.schema=e,this.errors=t}return e.prototype.getSection=function(e){return i.asSchema(this.getSectionRecursive(e,this.schema))},e.prototype.getSectionRecursive=function(e,t){if(!t||"boolean"==typeof t||0===e.length)return t;var n=e.shift();if(t.properties&&(t.properties[n],1))return this.getSectionRecursive(e,t.properties[n]);if(t.patternProperties)for(var r=0,o=Object.keys(t.patternProperties);r<o.length;r++){var i=o[r];if(new RegExp(i).test(n))return this.getSectionRecursive(e,t.patternProperties[i])}else{if("object"==typeof t.additionalProperties)return this.getSectionRecursive(e,t.additionalProperties);if(n.match("[0-9]+"))if(Array.isArray(t.items)){var a=parseInt(n,10);if(!isNaN(a)&&t.items[a])return this.getSectionRecursive(e,t.items[a])}else if(t.items)return this.getSectionRecursive(e,t.items)}return null},e}();t.ResolvedSchema=f;var l=function(){function e(e,t,n){this.contextService=t,this.requestService=e,this.promiseConstructor=n||Promise,this.callOnDispose=[],this.contributionSchemas={},this.contributionAssociations={},this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={}}return e.prototype.getRegisteredSchemaIds=function(e){return Object.keys(this.registeredSchemasIds).filter((function(t){var n=r.URI.parse(t).scheme;return"schemaservice"!==n&&(!e||e(n))}))},Object.defineProperty(e.prototype,"promise",{get:function(){return this.promiseConstructor},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){for(;this.callOnDispose.length>0;)this.callOnDispose.pop()()},e.prototype.onResourceChange=function(e){for(var t=this,n=!1,r=[e=this.normalizeId(e)],o=Object.keys(this.schemasById).map((function(e){return t.schemasById[e]}));r.length;)for(var i=r.pop(),a=0;a<o.length;a++){var s=o[a];s&&(s.url===i||s.dependencies[i])&&(s.url!==i&&r.push(s.url),s.clearSchema(),o[a]=void 0,n=!0)}return n},e.prototype.normalizeId=function(e){try{return r.URI.parse(e).toString()}catch(t){return e}},e.prototype.setSchemaContributions=function(e){if(e.schemas){var t=e.schemas;for(var n in t){var r=this.normalizeId(n);this.contributionSchemas[r]=this.addSchemaHandle(r,t[n])}}if(e.schemaAssociations){var o=e.schemaAssociations;for(var i in o){var a=o[i];this.contributionAssociations[i]=a;for(var s=this.getOrAddFilePatternAssociation(i),c=0,u=a;c<u.length;c++){var f=u[c];n=this.normalizeId(f);s.addSchema(n)}}}},e.prototype.addSchemaHandle=function(e,t){var n=new c(this,e,t);return this.schemasById[e]=n,n},e.prototype.getOrAddSchemaHandle=function(e,t){return this.schemasById[e]||this.addSchemaHandle(e,t)},e.prototype.getOrAddFilePatternAssociation=function(e){var t=this.filePatternAssociationById[e];return t||(t=new s(e),this.filePatternAssociationById[e]=t,this.filePatternAssociations.push(t)),t},e.prototype.registerExternalSchema=function(e,t,n){void 0===t&&(t=null);var r=this.normalizeId(e);if(this.registeredSchemasIds[r]=!0,t)for(var o=0,i=t;o<i.length;o++){var a=i[o];this.getOrAddFilePatternAssociation(a).addSchema(r)}return n?this.addSchemaHandle(r,n):this.getOrAddSchemaHandle(r)},e.prototype.clearExternalSchemas=function(){for(var e in this.schemasById={},this.filePatternAssociations=[],this.filePatternAssociationById={},this.registeredSchemasIds={},this.contributionSchemas)this.schemasById[e]=this.contributionSchemas[e],this.registeredSchemasIds[e]=!0;for(var t in this.contributionAssociations)for(var n=this.getOrAddFilePatternAssociation(t),r=0,o=this.contributionAssociations[t];r<o.length;r++){var i=o[r];e=this.normalizeId(i);n.addSchema(e)}},e.prototype.getResolvedSchema=function(e){var t=this.normalizeId(e),n=this.schemasById[t];return n?n.getResolvedSchema():this.promise.resolve(null)},e.prototype.loadSchema=function(e){if(!this.requestService){var t=a("json.schema.norequestservice","Unable to load schema from '{0}'. No schema request service available",d(e));return this.promise.resolve(new u({},[t]))}return this.requestService(e).then((function(t){if(!t){var r=a("json.schema.nocontent","Unable to load schema from '{0}': No content.",d(e));return new u({},[r])}var o,i=[];o=n.parse(t,i);var s=i.length?[a("json.schema.invalidFormat","Unable to parse content from '{0}': Parse error at offset {1}.",d(e),i[0].offset)]:[];return new u(o,s)}),(function(e){var t=e.toString(),n=e.toString().split("Error: ");return n.length>1&&(t=n[1]),new u({},[t])}))},e.prototype.resolveSchemaContent=function(e,t,n){var r=this,o=e.errors.slice(0),i=e.schema,s=this.contextService,c=function(e,t,n,r){var i=function(e,t){if(!t)return e;var n=e;return"/"===t[0]&&(t=t.substr(1)),t.split("/").some((function(e){return!(n=n[e])})),n}(t,r);if(i)for(var s in i)i.hasOwnProperty(s)&&!e.hasOwnProperty(s)&&(e[s]=i[s]);else o.push(a("json.schema.invalidref","$ref '{0}' in '{1}' can not be resolved.",r,n))},u=function(e,t,n,i,u){s&&!/^\w+:\/\/.*/.test(t)&&(t=s.resolveRelativePath(t,i)),t=r.normalizeId(t);var f=r.getOrAddSchemaHandle(t);return f.getUnresolvedSchema().then((function(r){if(u[t]=!0,r.errors.length){var i=n?t+"#"+n:t;o.push(a("json.schema.problemloadingref","Problems loading reference '{0}': {1}",i,r.errors[0]))}return c(e,r.schema,t,n),l(e,r.schema,t,f.dependencies)}))},l=function(e,t,n,o){if(!e||"object"!=typeof e)return Promise.resolve(null);for(var i=[e],a=[],s=[],f=function(e){for(var r=[];e.$ref;){var a=e.$ref,f=a.split("#",2);if(delete e.$ref,f[0].length>0)return void s.push(u(e,f[0],f[1],n,o));-1===r.indexOf(a)&&(c(e,t,n,f[1]),r.push(a))}!function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];"object"==typeof o&&i.push(o)}}(e.items,e.additionalProperties,e.not,e.contains,e.propertyNames,e.if,e.then,e.else),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];if("object"==typeof o)for(var a in o){var s=o[a];"object"==typeof s&&i.push(s)}}}(e.definitions,e.properties,e.patternProperties,e.dependencies),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=0,r=e;n<r.length;n++){var o=r[n];if(Array.isArray(o))for(var a=0,s=o;a<s.length;a++){var c=s[a];"object"==typeof c&&i.push(c)}}}(e.anyOf,e.allOf,e.oneOf,e.items)};i.length;){var l=i.pop();a.indexOf(l)>=0||(a.push(l),f(l))}return r.promise.all(s)};return l(i,i,t,n).then((function(e){return new f(i,o)}))},e.prototype.getSchemaForResource=function(e,t){if(t&&t.root&&"object"===t.root.type){var n=t.root.properties.filter((function(e){return"$schema"===e.keyNode.value&&e.valueNode&&"string"===e.valueNode.type}));if(n.length>0){var r=i.getNodeValue(n[0].valueNode);if(r&&o.startsWith(r,".")&&this.contextService&&(r=this.contextService.resolveRelativePath(r,e)),r){var a=this.normalizeId(r);return this.getOrAddSchemaHandle(a).getResolvedSchema()}}}for(var s=Object.create(null),c=[],u=0,f=this.filePatternAssociations;u<f.length;u++){var l=f[u];if(l.matchesPattern(e))for(var d=0,p=l.getSchemas();d<p.length;d++){var h=p[d];s[h]||(c.push(h),s[h]=!0)}}return c.length>0?this.createCombinedSchema(e,c).getResolvedSchema():this.promise.resolve(null)},e.prototype.createCombinedSchema=function(e,t){if(1===t.length)return this.getOrAddSchemaHandle(t[0]);var n="schemaservice://combinedSchema/"+encodeURIComponent(e),r={allOf:t.map((function(e){return{$ref:e}}))};return this.addSchemaHandle(n,r)},e}();function d(e){try{var t=r.URI.parse(e);if("file"===t.scheme)return t.fsPath}catch(e){}return e}t.JSONSchemaService=l})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonValidation",["require","exports","./jsonSchemaService","vscode-languageserver-types","../jsonLanguageTypes","vscode-nls","../utils/objects"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./jsonSchemaService"),r=e("vscode-languageserver-types"),o=e("../jsonLanguageTypes"),i=e("vscode-nls"),a=e("../utils/objects"),s=i.loadMessageBundle(),c=function(){function e(e,t){this.jsonSchemaService=e,this.promise=t,this.validationEnabled=!0}return e.prototype.configure=function(e){e&&(this.validationEnabled=e.validate,this.commentSeverity=e.allowComments?void 0:r.DiagnosticSeverity.Error)},e.prototype.doValidation=function(e,t,i,c){var l=this;if(!this.validationEnabled)return this.promise.resolve([]);var d=[],p={},h=function(e){var t=e.range.start.line+" "+e.range.start.character+" "+e.message;p[t]||(p[t]=!0,d.push(e))},m=function(n){var c=i?f(i.trailingCommas):r.DiagnosticSeverity.Error,u=i?f(i.comments):l.commentSeverity;if(n){if(n.errors.length&&t.root){var p=t.root,m="object"===p.type?p.properties[0]:null;if(m&&"$schema"===m.keyNode.value){var g=m.valueNode||m,v=r.Range.create(e.positionAt(g.offset),e.positionAt(g.offset+g.length));h(r.Diagnostic.create(v,n.errors[0],r.DiagnosticSeverity.Warning,o.ErrorCode.SchemaResolveError))}else{v=r.Range.create(e.positionAt(p.offset),e.positionAt(p.offset+1));h(r.Diagnostic.create(v,n.errors[0],r.DiagnosticSeverity.Warning,o.ErrorCode.SchemaResolveError))}}else{var y=t.validate(e,n.schema);y&&y.forEach(h)}(function e(t){if(t&&"object"==typeof t){if(a.isBoolean(t.allowComments))return t.allowComments;if(t.allOf)for(var n=0,r=t.allOf;n<r.length;n++){var o=r[n],i=e(o);if(a.isBoolean(i))return i}}return})(n.schema)&&(u=void 0),function e(t){if(t&&"object"==typeof t){if(a.isBoolean(t.allowsTrailingCommas))return t.allowsTrailingCommas;if(t.allOf)for(var n=0,r=t.allOf;n<r.length;n++){var o=r[n],i=e(o);if(a.isBoolean(i))return i}}return}(n.schema)&&(c=void 0)}for(var b=0,x=t.syntaxErrors;b<x.length;b++){var S=x[b];if(S.code===o.ErrorCode.TrailingComma){if("number"!=typeof c)continue;S.severity=c}h(S)}if("number"==typeof u){var C=s("InvalidCommentToken","Comments are not permitted in JSON.");t.comments.forEach((function(e){h(r.Diagnostic.create(e,C,u,o.ErrorCode.CommentNotPermitted))}))}return d};if(c){var g=c.id||"schemaservice://untitled/"+u++;return this.jsonSchemaService.resolveSchemaContent(new n.UnresolvedSchema(c),g,{}).then((function(e){return m(e)}))}return this.jsonSchemaService.getSchemaForResource(e.uri,t).then((function(e){return m(e)}))},e}();t.JSONValidation=c;var u=0;function f(e){switch(e){case"error":return r.DiagnosticSeverity.Error;case"warning":return r.DiagnosticSeverity.Warning;case"ignore":return}}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/utils/colors",["require","exports"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=48,r=57,o=65,i=97,a=102;function s(e){return e<n?0:e<=r?e-n:(e<i&&(e+=i-o),e>=i&&e<=a?e-i+10:0)}t.hexDigit=s,t.colorFromHex=function(e){if("#"!==e[0])return null;switch(e.length){case 4:return{red:17*s(e.charCodeAt(1))/255,green:17*s(e.charCodeAt(2))/255,blue:17*s(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*s(e.charCodeAt(1))/255,green:17*s(e.charCodeAt(2))/255,blue:17*s(e.charCodeAt(3))/255,alpha:17*s(e.charCodeAt(4))/255};case 7:return{red:(16*s(e.charCodeAt(1))+s(e.charCodeAt(2)))/255,green:(16*s(e.charCodeAt(3))+s(e.charCodeAt(4)))/255,blue:(16*s(e.charCodeAt(5))+s(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*s(e.charCodeAt(1))+s(e.charCodeAt(2)))/255,green:(16*s(e.charCodeAt(3))+s(e.charCodeAt(4)))/255,blue:(16*s(e.charCodeAt(5))+s(e.charCodeAt(6)))/255,alpha:(16*s(e.charCodeAt(7))+s(e.charCodeAt(8)))/255}}return null},t.colorFrom256RGB=function(e,t,n,r){return void 0===r&&(r=1),{red:e/255,green:t/255,blue:n/255,alpha:r}}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonDocumentSymbols",["require","exports","../parser/jsonParser","../utils/strings","../utils/colors","vscode-languageserver-types"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("../parser/jsonParser"),r=e("../utils/strings"),o=e("../utils/colors"),i=e("vscode-languageserver-types"),a=function(){function e(e){this.schemaService=e}return e.prototype.findDocumentSymbols=function(e,t){var o=this,a=t.root;if(!a)return null;var c=e.uri;if(("vscode://defaultsettings/keybindings.json"===c||r.endsWith(c.toLowerCase(),"/user/keybindings.json"))&&"array"===a.type){var u=[];return a.items.forEach((function(t){if("object"===t.type)for(var r=0,o=t.properties;r<o.length;r++){var a=o[r];if("key"===a.keyNode.value&&a.valueNode){if(a.valueNode){var c=i.Location.create(e.uri,s(e,t));u.push({name:n.getNodeValue(a.valueNode),kind:i.SymbolKind.Function,location:c})}return}}})),u}var f=function(t,n,r){return"array"===n.type?n.items.forEach((function(e){return f(t,e,r)})):"object"===n.type&&n.properties.forEach((function(n){var a=i.Location.create(e.uri,s(e,n)),c=n.valueNode;if(c){var u=r?r+"."+n.keyNode.value:n.keyNode.value;t.push({name:o.getKeyLabel(n),kind:o.getSymbolKind(c.type),location:a,containerName:r}),f(t,c,u)}})),t};return f([],a,void 0)},e.prototype.findDocumentSymbols2=function(e,t){var o=this,a=t.root;if(!a)return null;var c=e.uri;if(("vscode://defaultsettings/keybindings.json"===c||r.endsWith(c.toLowerCase(),"/user/keybindings.json"))&&"array"===a.type){var u=[];return a.items.forEach((function(t){if("object"===t.type)for(var r=0,o=t.properties;r<o.length;r++){var a=o[r];if("key"===a.keyNode.value){if(a.valueNode){var c=s(e,t),f=s(e,a.keyNode);u.push({name:n.getNodeValue(a.valueNode),kind:i.SymbolKind.Function,range:c,selectionRange:f})}return}}})),u}var f=function(t,n){return"array"===n.type?n.items.forEach((function(n,r){if(n){var i=s(e,n),a=i,c=String(r),u=f([],n);t.push({name:c,kind:o.getSymbolKind(n.type),range:i,selectionRange:a,children:u})}})):"object"===n.type&&n.properties.forEach((function(n){var r=n.valueNode;if(r){var i=s(e,n),a=s(e,n.keyNode),c=f([],r);t.push({name:o.getKeyLabel(n),kind:o.getSymbolKind(r.type),range:i,selectionRange:a,children:c})}})),t};return f([],a)},e.prototype.getSymbolKind=function(e){switch(e){case"object":return i.SymbolKind.Module;case"string":return i.SymbolKind.String;case"number":return i.SymbolKind.Number;case"array":return i.SymbolKind.Array;case"boolean":return i.SymbolKind.Boolean;default:return i.SymbolKind.Variable}},e.prototype.getKeyLabel=function(e){var t=e.keyNode.value;return t&&(t=t.replace(/[\n]/g,"↵")),t&&t.trim()?t:'"'+t+'"'},e.prototype.findDocumentColors=function(e,t){return this.schemaService.getSchemaForResource(e.uri,t).then((function(r){var i=[];if(r)for(var a={},c=0,u=t.getMatchingSchemas(r.schema);c<u.length;c++){var f=u[c];if(!f.inverted&&f.schema&&("color"===f.schema.format||"color-hex"===f.schema.format)&&f.node&&"string"===f.node.type){var l=String(f.node.offset);if(!a[l]){var d=o.colorFromHex(n.getNodeValue(f.node));if(d){var p=s(e,f.node);i.push({color:d,range:p})}a[l]=!0}}}return i}))},e.prototype.getColorPresentations=function(e,t,n,r){var o,a=[],s=Math.round(255*n.red),c=Math.round(255*n.green),u=Math.round(255*n.blue);function f(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}return o=1===n.alpha?"#"+f(s)+f(c)+f(u):"#"+f(s)+f(c)+f(u)+f(Math.round(255*n.alpha)),a.push({label:o,textEdit:i.TextEdit.replace(r,JSON.stringify(o))}),a},e}();function s(e,t){return i.Range.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length))}t.JSONDocumentSymbols=a})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/configuration",["require","exports","vscode-nls"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("vscode-nls").loadMessageBundle();t.schemaContributions={schemaAssociations:{},schemas:{"http://json-schema.org/draft-04/schema#":{title:n("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),$schema:"http://json-schema.org/draft-04/schema#",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},positiveInteger:{type:"integer",minimum:0},positiveIntegerDefault0:{allOf:[{$ref:"#/definitions/positiveInteger"},{default:0}]},simpleTypes:{type:"string",enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},minItems:1,uniqueItems:!0}},type:"object",properties:{id:{type:"string",format:"uri"},$schema:{type:"string",format:"uri"},title:{type:"string"},description:{type:"string"},default:{},multipleOf:{type:"number",minimum:0,exclusiveMinimum:!0},maximum:{type:"number"},exclusiveMaximum:{type:"boolean",default:!1},minimum:{type:"number"},exclusiveMinimum:{type:"boolean",default:!1},maxLength:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minLength:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},pattern:{type:"string",format:"regex"},additionalItems:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minItems:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},uniqueItems:{type:"boolean",default:!1},maxProperties:{allOf:[{$ref:"#/definitions/positiveInteger"}]},minProperties:{allOf:[{$ref:"#/definitions/positiveIntegerDefault0"}]},required:{allOf:[{$ref:"#/definitions/stringArray"}]},additionalProperties:{anyOf:[{type:"boolean"},{$ref:"#"}],default:{}},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{anyOf:[{type:"string",enum:["date-time","uri","email","hostname","ipv4","ipv6","regex"]},{type:"string"}]},allOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},anyOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},oneOf:{allOf:[{$ref:"#/definitions/schemaArray"}]},not:{allOf:[{$ref:"#"}]}},dependencies:{exclusiveMaximum:["maximum"],exclusiveMinimum:["minimum"]},default:{}},"http://json-schema.org/draft-07/schema#":{title:n("schema.json","Describes a JSON file using a schema. See json-schema.org for more info."),definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0}}};var r={id:n("schema.json.id","A unique identifier for the schema."),$schema:n("schema.json.$schema","The schema to verify this document against."),title:n("schema.json.title","A descriptive title of the element."),description:n("schema.json.description","A long description of the element. Used in hover menus and suggestions."),default:n("schema.json.default","A default value. Used by suggestions."),multipleOf:n("schema.json.multipleOf","A number that should cleanly divide the current value (i.e. have no remainder)."),maximum:n("schema.json.maximum","The maximum numerical value, inclusive by default."),exclusiveMaximum:n("schema.json.exclusiveMaximum","Makes the maximum property exclusive."),minimum:n("schema.json.minimum","The minimum numerical value, inclusive by default."),exclusiveMinimum:n("schema.json.exclusiveMininum","Makes the minimum property exclusive."),maxLength:n("schema.json.maxLength","The maximum length of a string."),minLength:n("schema.json.minLength","The minimum length of a string."),pattern:n("schema.json.pattern","A regular expression to match the string against. It is not implicitly anchored."),additionalItems:n("schema.json.additionalItems","For arrays, only when items is set as an array. If it is a schema, then this schema validates items after the ones specified by the items array. If it is false, then additional items will cause validation to fail."),items:n("schema.json.items","For arrays. Can either be a schema to validate every element against or an array of schemas to validate each item against in order (the first schema will validate the first element, the second schema will validate the second element, and so on."),maxItems:n("schema.json.maxItems","The maximum number of items that can be inside an array. Inclusive."),minItems:n("schema.json.minItems","The minimum number of items that can be inside an array. Inclusive."),uniqueItems:n("schema.json.uniqueItems","If all of the items in the array must be unique. Defaults to false."),maxProperties:n("schema.json.maxProperties","The maximum number of properties an object can have. Inclusive."),minProperties:n("schema.json.minProperties","The minimum number of properties an object can have. Inclusive."),required:n("schema.json.required","An array of strings that lists the names of all properties required on this object."),additionalProperties:n("schema.json.additionalProperties","Either a schema or a boolean. If a schema, then used to validate all properties not matched by 'properties' or 'patternProperties'. If false, then any properties not matched by either will cause this schema to fail."),definitions:n("schema.json.definitions","Not used for validation. Place subschemas here that you wish to reference inline with $ref."),properties:n("schema.json.properties","A map of property names to schemas for each property."),patternProperties:n("schema.json.patternProperties","A map of regular expressions on property names to schemas for matching properties."),dependencies:n("schema.json.dependencies","A map of property names to either an array of property names or a schema. An array of property names means the property named in the key depends on the properties in the array being present in the object in order to be valid. If the value is a schema, then the schema is only applied to the object if the property in the key exists on the object."),enum:n("schema.json.enum","The set of literal values that are valid."),type:n("schema.json.type","Either a string of one of the basic schema types (number, integer, null, array, object, boolean, string) or an array of strings specifying a subset of those types."),format:n("schema.json.format","Describes the format expected for the value."),allOf:n("schema.json.allOf","An array of schemas, all of which must match."),anyOf:n("schema.json.anyOf","An array of schemas, where at least one must match."),oneOf:n("schema.json.oneOf","An array of schemas, exactly one of which must match."),not:n("schema.json.not","A schema which must not match."),$id:n("schema.json.$id","A unique identifier for the schema."),$ref:n("schema.json.$ref","Reference a definition hosted on any location."),$comment:n("schema.json.$comment","Comments from schema authors to readers or maintainers of the schema."),readOnly:n("schema.json.readOnly","Indicates that the value of the instance is managed exclusively by the owning authority."),examples:n("schema.json.examples","Sample JSON values associated with a particular schema, for the purpose of illustrating usage."),contains:n("schema.json.contains",'An array instance is valid against "contains" if at least one of its elements is valid against the given schema.'),propertyNames:n("schema.json.propertyNames","If the instance is an object, this keyword validates if every property name in the instance validates against the provided schema."),const:n("schema.json.const","An instance validates successfully against this keyword if its value is equal to the value of the keyword."),contentMediaType:n("schema.json.contentMediaType","Describes the media type of a string property."),contentEncoding:n("schema.json.contentEncoding","Describes the content encoding of a string property."),if:n("schema.json.if",'The validation outcome of the "if" subschema controls which of the "then" or "else" keywords are evaluated.'),then:n("schema.json.then",'The "if" subschema is used for validation when the "if" subschema succeeds.'),else:n("schema.json.else",'The "else" subschema is used for validation when the "if" subschema fails.')};for(var o in t.schemaContributions.schemas){var i=t.schemaContributions.schemas[o];for(var a in i.properties){var s=i.properties[a];!0===s&&(s=i.properties[a]={});var c=r[a];c?s.description=c:console.log(a+": localize('schema.json."+a+'\', "")')}}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonFolding",["require","exports","vscode-languageserver-types","jsonc-parser","../jsonLanguageTypes"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("vscode-languageserver-types"),r=e("jsonc-parser"),o=e("../jsonLanguageTypes");t.getFoldingRanges=function(e,t){var i=[],a=[],s=[],c=-1,u=r.createScanner(e.getText(),!1),f=u.scan();function l(e){i.push(e),a.push(s.length)}for(;17!==f;){switch(f){case 1:case 3:var d={startLine:m=e.positionAt(u.getTokenOffset()).line,endLine:m,kind:1===f?"object":"array"};s.push(d);break;case 2:case 4:var p=2===f?"object":"array";if(s.length>0&&s[s.length-1].kind===p){d=s.pop();var h=e.positionAt(u.getTokenOffset()).line;d&&h>d.startLine+1&&c!==d.startLine&&(d.endLine=h-1,l(d),c=d.startLine)}break;case 13:var m=e.positionAt(u.getTokenOffset()).line,g=e.positionAt(u.getTokenOffset()+u.getTokenLength()).line;1===u.getTokenError()&&m+1<e.lineCount?u.setPosition(e.offsetAt(n.Position.create(m+1,0))):m<g&&(l({startLine:m,endLine:g,kind:o.FoldingRangeKind.Comment}),c=m);break;case 12:var v=e.getText().substr(u.getTokenOffset(),u.getTokenLength()).match(/^\/\/\s*#(region\b)|(endregion\b)/);if(v){h=e.positionAt(u.getTokenOffset()).line;if(v[1]){d={startLine:h,endLine:h,kind:o.FoldingRangeKind.Region};s.push(d)}else{for(var y=s.length-1;y>=0&&s[y].kind!==o.FoldingRangeKind.Region;)y--;if(y>=0){d=s[y];s.length=y,h>d.startLine&&c!==d.startLine&&(d.endLine=h,l(d),c=d.startLine)}}}}f=u.scan()}var b=t&&t.rangeLimit;if("number"!=typeof b||i.length<=b)return i;for(var x=[],S=0,C=a;S<C.length;S++){(A=C[S])<30&&(x[A]=(x[A]||0)+1)}var j=0,k=0;for(y=0;y<x.length;y++){var T=x[y];if(T){if(T+j>b){k=y;break}j+=T}}var O=[];for(y=0;y<i.length;y++){var A;"number"==typeof(A=a[y])&&(A<k||A===k&&j++<b)&&O.push(i[y])}return O}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/services/jsonSelectionRanges",["require","exports","vscode-languageserver-types","jsonc-parser"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("vscode-languageserver-types"),r=e("jsonc-parser");t.getSelectionRanges=function(e,t,o){function i(t,r){return n.Range.create(e.positionAt(t),e.positionAt(r))}var a=r.createScanner(e.getText(),!0);function s(e,t){return a.setPosition(e),a.scan()===t?a.getTokenOffset()+a.getTokenLength():-1}return t.map((function(t){for(var r=e.offsetAt(t),a=o.getNodeFromOffset(r,!0),c=[];a;){switch(a.type){case"string":case"object":case"array":var u=a.offset+1,f=a.offset+a.length-1;u<f&&r>=u&&r<=f&&c.push(i(u,f)),c.push(i(a.offset,a.offset+a.length));break;case"number":case"boolean":case"null":case"property":c.push(i(a.offset,a.offset+a.length))}if("property"===a.type||a.parent&&"array"===a.parent.type){var l=s(a.offset+a.length,5);-1!==l&&c.push(i(a.offset,l))}a=a.parent}for(var d=void 0,p=c.length-1;p>=0;p--)d=n.SelectionRange.create(c[p],d);return d||(d=n.SelectionRange.create(n.Range.create(t,t))),d}))}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-json-languageservice/jsonLanguageService",["require","exports","vscode-languageserver-types","./services/jsonCompletion","./services/jsonHover","./services/jsonValidation","./services/jsonDocumentSymbols","./parser/jsonParser","./services/configuration","./services/jsonSchemaService","./services/jsonFolding","./services/jsonSelectionRanges","jsonc-parser","./jsonLanguageTypes"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("vscode-languageserver-types");t.TextDocument=n.TextDocument,t.Position=n.Position,t.CompletionItem=n.CompletionItem,t.CompletionList=n.CompletionList,t.Hover=n.Hover,t.Range=n.Range,t.SymbolInformation=n.SymbolInformation,t.Diagnostic=n.Diagnostic,t.TextEdit=n.TextEdit,t.FormattingOptions=n.FormattingOptions,t.MarkedString=n.MarkedString;var r=e("./services/jsonCompletion"),o=e("./services/jsonHover"),i=e("./services/jsonValidation"),a=e("./services/jsonDocumentSymbols"),s=e("./parser/jsonParser"),c=e("./services/configuration"),u=e("./services/jsonSchemaService"),f=e("./services/jsonFolding"),l=e("./services/jsonSelectionRanges"),d=e("jsonc-parser");!function(e){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])}(e("./jsonLanguageTypes")),t.getLanguageService=function(e){var t=e.promiseConstructor||Promise,p=new u.JSONSchemaService(e.schemaRequestService,e.workspaceContext,t);p.setSchemaContributions(c.schemaContributions);var h=new r.JSONCompletion(p,e.contributions,t,e.clientCapabilities),m=new o.JSONHover(p,e.contributions,t),g=new a.JSONDocumentSymbols(p),v=new i.JSONValidation(p,t);return{configure:function(e){p.clearExternalSchemas(),e.schemas&&e.schemas.forEach((function(e){p.registerExternalSchema(e.uri,e.fileMatch,e.schema)})),v.configure(e)},resetSchema:function(e){return p.onResourceChange(e)},doValidation:v.doValidation.bind(v),parseJSONDocument:function(e){return s.parse(e,{collectComments:!0})},newJSONDocument:function(e,t){return s.newJSONDocument(e,t)},doResolve:h.doResolve.bind(h),doComplete:h.doComplete.bind(h),findDocumentSymbols:g.findDocumentSymbols.bind(g),findDocumentSymbols2:g.findDocumentSymbols2.bind(g),findColorSymbols:function(e,t){return g.findDocumentColors(e,t).then((function(e){return e.map((function(e){return e.range}))}))},findDocumentColors:g.findDocumentColors.bind(g),getColorPresentations:g.getColorPresentations.bind(g),doHover:m.doHover.bind(m),getFoldingRanges:f.getFoldingRanges,getSelectionRanges:l.getSelectionRanges,format:function(e,t,r){var o=void 0;if(t){var i=e.offsetAt(t.start);o={offset:i,length:e.offsetAt(t.end)-i}}var a={tabSize:r?r.tabSize:4,insertSpaces:!r||r.insertSpaces,eol:"\n"};return d.format(e.getText(),o,a).map((function(t){return n.TextEdit.replace(n.Range.create(e.positionAt(t.offset),e.positionAt(t.offset+t.length)),t.content)}))}}}})),define("vscode-json-languageservice",["vscode-json-languageservice/jsonLanguageService"],(function(e){return e})),define("vs/language/json/jsonWorker",["require","exports","vscode-json-languageservice","vscode-languageserver-types"],(function(e,t,n,r){"use strict";var o;Object.defineProperty(t,"__esModule",{value:!0}),"undefined"!=typeof fetch&&(o=function(e){return fetch(e).then((function(e){return e.text()}))});var i=function(){function e(e){this.wrapped=new Promise(e)}return e.prototype.then=function(e,t){return this.wrapped.then(e,t)},e.prototype.getWrapped=function(){return this.wrapped},e.resolve=function(e){return Promise.resolve(e)},e.reject=function(e){return Promise.reject(e)},e.all=function(e){return Promise.all(e)},e}(),a=function(){function e(e,t){this._ctx=e,this._languageSettings=t.languageSettings,this._languageId=t.languageId,this._languageService=n.getLanguageService({schemaRequestService:t.enableSchemaRequest&&o,promiseConstructor:i}),this._languageService.configure(this._languageSettings)}return e.prototype.doValidation=function(e){var t=this._getTextDocument(e);if(t){var n=this._languageService.parseJSONDocument(t);return this._languageService.doValidation(t,n)}return Promise.resolve([])},e.prototype.doComplete=function(e,t){var n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n);return this._languageService.doComplete(n,t,r)},e.prototype.doResolve=function(e){return this._languageService.doResolve(e)},e.prototype.doHover=function(e,t){var n=this._getTextDocument(e),r=this._languageService.parseJSONDocument(n);return this._languageService.doHover(n,t,r)},e.prototype.format=function(e,t,n){var r=this._getTextDocument(e),o=this._languageService.format(r,t,n);return Promise.resolve(o)},e.prototype.resetSchema=function(e){return Promise.resolve(this._languageService.resetSchema(e))},e.prototype.findDocumentSymbols=function(e){var t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentSymbols(t,n);return Promise.resolve(r)},e.prototype.findDocumentColors=function(e){var t=this._getTextDocument(e),n=this._languageService.parseJSONDocument(t),r=this._languageService.findDocumentColors(t,n);return Promise.resolve(r)},e.prototype.getColorPresentations=function(e,t,n){var r=this._getTextDocument(e),o=this._languageService.parseJSONDocument(r),i=this._languageService.getColorPresentations(r,o,t,n);return Promise.resolve(i)},e.prototype.provideFoldingRanges=function(e,t){var n=this._getTextDocument(e),r=this._languageService.getFoldingRanges(n,t);return Promise.resolve(r)},e.prototype._getTextDocument=function(e){for(var t=0,n=this._ctx.getMirrorModels();t<n.length;t++){var o=n[t];if(o.uri.toString()===e)return r.TextDocument.create(e,this._languageId,o.version,o.getValue())}return null},e}();t.JSONWorker=a,t.create=function(e,t){return new a(e,t)}}));