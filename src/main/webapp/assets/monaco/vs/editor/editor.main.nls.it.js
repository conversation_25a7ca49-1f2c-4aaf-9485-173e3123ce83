/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.18.1(d7a26172c5955d29d2a8cca4377b53b28925c766)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.it",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (nuova occorrenza)","{0} ( accaduto {1} volte)"],"vs/base/browser/ui/findinput/findInput":["Input"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Maiuscole/minuscole","Parola intera","Usa espressione regolare"],"vs/base/browser/ui/findinput/replaceInput":["Input","Mantieni maiuscole/minuscole"],"vs/base/browser/ui/inputbox/inputBox":["Errore: {0}","Avviso: {0}","Informazioni: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Non associato"],"vs/base/browser/ui/list/listWidget":["{0}. utilizzare i tasti di navigazione per navigare."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Cancella","Disabilita filtro sul tipo","Abilita filtro sul tipo","Non sono stati trovati elementi","Abbinamento di {0} su {1} elementi"],
"vs/base/common/keybindingLabels":["CTRL","MAIUSC","ALT","Windows","CTRL","MAIUSC","ALT","Super","CTRL","MAIUSC","ALT","Comando","CTRL","MAIUSC","ALT","Windows","CTRL","MAIUSC","ALT","Super"],"vs/base/common/severity":["Errore","Avviso","Info"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, selezione","selezione"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Selezione rapida. Digitare per ridurre il numero di risultati.","Selezione rapida","{0} risultati"],"vs/editor/browser/controller/coreCommands":["&&Seleziona tutto","&&Annulla","&&Ripeti"],"vs/editor/browser/widget/codeEditorWidget":["Il numero di cursori è stato limitato a {0}."],"vs/editor/browser/widget/diffEditorWidget":["Non è possibile confrontare i file perché uno è troppo grande."],
"vs/editor/browser/widget/diffReview":["Chiudi","nessuna linea","1 linea","{0} linee","Differenza {0} di {1}: originale {2}, {3}, modificate {4}, {5}","vuota","originali {0}, modificate {1}: {2}","+ modificate {0}: {1}","- originali {0}: {1}","Vai alla differenza successiva","Vai alla differenza precedente"],"vs/editor/browser/widget/inlineDiffMargin":["Copia le righe eliminate","Copia la riga eliminata","Copia la riga eliminata ({0})","Ripristina questa modifica","Copia la riga eliminata ({0})"],
"vs/editor/common/config/commonEditorConfig":["Editor","Controlla la famiglia di caratteri.","Controlla lo spessore del carattere.","Controlla le dimensioni del carattere in pixel.","Controlla l'altezza della riga. Usare 0 per calcolare l'altezza della riga dalle dimensioni del carattere.","Controlla la spaziatura tra le lettere in pixel.","I numeri di riga non vengono visualizzati.","I numeri di riga vengono visualizzati come numeri assoluti.","I numeri di riga vengono visualizzati come distanza in linee alla posizione del cursore.","I numeri di riga vengono visualizzati ogni 10 righe.","Controlla la visualizzazione dei numeri di riga.","Controlla il numero minimo di righe iniziali e finali visibili che circondano il cursore. Noto come 'scrollOff' o `scrollOffset` in altri editor.","Esegue il rendering dell'ultimo numero di riga quando il file termina con un carattere di nuova riga.","Esegue il rendering dei righelli verticali dopo un certo numero di caratteri a spaziatura fissa. Usare più valori  per più righelli. Se la matrice è vuota, non vengono disegnati righelli.","Caratteri che verranno usati come separatori di parola quando si eseguono operazioni o spostamenti correlati a parole.","Numero di spazi a cui equivale una tabulazione. Quando `#editor.detectIndentation#` è attivo, questa impostazione viene sostituita in base al contenuto del file.","Inserisce spazi quando viene premuto TAB. Quando `#editor.detectIndentation#` è attivo, questa impostazione viene sostituita in base al contenuto del file.","Controlla se `#editor.tabSize#` e `#editor.insertSpaces#` verranno rilevati automaticamente quando un file viene aperto in base al contenuto del file.","Controlla se le selezioni devono avere gli angoli arrotondati.","Controlla se l'editor scorrerà oltre l'ultima riga.","Controlla il numero di caratteri aggiuntivi oltre i quali l'editor scorrerà orizzontalmente.","Controlla se per lo scorrimento dell'editor verrà usata un'animazione.","Controlla se la minimappa è visualizzata.","Definisce il lato in cui eseguire il rendering della minimappa.","Controlla se il cursore della minimappa viene nascosto automaticamente.","Esegue il rendering dei caratteri effettivi di una riga in contrapposizione ai blocchi colore.","Limita la larghezza della minimappa in modo da eseguire il rendering al massimo di un certo numero di colonne.","Controlla se mostrare l'area sensibile al passaggio del mouse.","Controlla il ritardo in millisecondi dopo il quale viene mostrato il passaggio del mouse.","Controlla se l'area sensibile al passaggio del mouse deve rimanere visibile quando vi si passa sopra con il puntatore del mouse","Controlla se inizializzare la stringa di ricerca nel Widget Trova con il testo selezionato nell'editor","Controlla se l'operazione di ricerca viene eseguita all'interno del testo selezionato o in tutto il file aperto nell'editor.","Controlla se il widget Trova deve leggere o modificare gli appunti di ricerca condivisi in macOS.","Controlla se il widget Trova deve aggiungere altre righe nella parte superiore dell'editor. Quando è true, è possibile scorrere oltre la prima riga quando il widget Trova è visibile.","Il wrapping delle righe non viene eseguito.","Verrà eseguito il wrapping delle righe in base alla larghezza del viewport.","Verrà eseguito il wrapping delle righe alla posizione corrispondente a `#editor.wordWrapColumn#`.","Verrà eseguito il wrapping delle righe alla posizione minima del viewport e di `#editor.wordWrapColumn#`.","Controlla il wrapping delle righe.","Controlla la colonna di wrapping dell'editor quando il valore di `#editor.wordWrap#` è `wordWrapColumn` o `bounded`.","Nessun rientro. Le righe con ritorno a capo iniziano dalla colonna 1. ","Le righe con ritorno a capo hanno lo stesso rientro della riga padre.","Le righe con ritorno a capo hanno un rientro di +1 rispetto alla riga padre.","Le righe con ritorno a capo hanno un rientro di +2 rispetto alla riga padre.","Controlla il rientro delle righe con ritorno a capo.","Moltiplicatore da usare sui valori `deltaX` e `deltaY` degli eventi di scorrimento della rotellina del mouse.","Moltiplicatore della velocità di scorrimento quando si preme `Alt`.","Rappresenta il tasto 'Control' in Windows e Linux e il tasto 'Comando' in macOS.","Rappresenta il tasto 'Alt' in Windows e Linux e il tasto 'Opzione' in macOS.","Modificatore da usare per aggiungere più cursori con il mouse. I gesti del mouse Vai alla definizione e Apri il collegamento si adatteranno in modo da non entrare in conflitto con il modificatore di selezione multipla. [Altre informazioni](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Unire i cursori multipli se sovrapposti.","Abilita i suggerimenti rapidi all'interno di stringhe.","Abilita i suggerimenti rapidi all'interno di commenti.","Abilita i suggerimenti rapidi all'esterno di stringhe e commenti.","Controlla se visualizzare automaticamente i suggerimenti durante la digitazione.","Controlla il ritardo in millisecondi dopo il quale verranno visualizzati i suggerimenti rapidi.","Abilita un popup che mostra documentazione sui parametri e informazioni sui tipi mentre si digita.","Controlla se il menu dei suggerimenti per i parametri esegue un ciclo o si chiude quando viene raggiunta la fine dell'elenco.","Utilizza le configurazioni del linguaggio per determinare la chiusura automatica delle parentesi.","Chiudi automaticamente le parentesi solo quando il cursore si trova alla sinistra di uno spazio vuoto.","Controlla se l'editor deve chiudere automaticamente le parentesi quadre dopo che sono state aperte.","Utilizza le configurazioni del linguaggio per determinare la chiusura automatica delle virgolette.","Chiudi automaticamente le virgolette solo quando il cursore si trova alla sinistra di uno spazio vuoto.","Controlla se l'editor deve chiudere automaticamente le citazioni dopo che sono state aperte.","Digita sempre su virgolette o parentesi quadre.","Digita sopra le virgolette o le parentesi quadre di chiusura solo se sono state inserite automaticamente.","Non digitare mai su virgolette o parentesi quadre.","Controlla se l'editor deve digitare su virgolette o parentesi quadre.","Usa le configurazioni del linguaggio per determinare quando racchiudere automaticamente le selezioni tra parentesi quadre o virgolette.","Racchiude la selezione tra parentesi quadre ma non tra virgolette.","Racchiude la selezione tra virgolette ma non tra parentesi quadre.","Controlla se l'editor deve racchiudere automaticamente le selezioni tra parentesi quadre o virgolette.","Controlla se l'editor deve formattare automaticamente la riga dopo la digitazione.","Controlla se l'editor deve formattare automaticamente il contenuto incollato. Deve essere disponibile un formattatore che deve essere in grado di formattare un intervallo in un documento.","Controlla se l'editor deve correggere automaticamente il rientro mentre l'utente digita, incolla o sposta le righe. Devono essere disponibili le estensioni con le regole di rientro della lingua.","Controlla se i suggerimenti devono essere visualizzati automaticamente durante la digitazione dei caratteri trigger.","Accetta un suggerimento con 'Invio' solo quando si apporta una modifica al testo.","Controlla se i suggerimenti devono essere accettati con 'INVIO' in aggiunta a 'TAB'. In questo modo è possibile evitare ambiguità tra l'inserimento di nuove righe e l'accettazione di suggerimenti.","Controlla se accettare i suggerimenti con i caratteri di commit. Ad esempio, in JavaScript il punto e virgola (';') può essere un carattere di commit che accetta un suggerimento e digita tale carattere.","Visualizza i suggerimenti del frammento prima degli altri suggerimenti.","Visualizza i suggerimenti del frammento dopo gli altri suggerimenti.","Visualizza i suggerimenti del frammento insieme agli altri suggerimenti.","Non mostrare i suggerimenti del frammento.","Controlla se i frammenti di codice sono visualizzati con altri suggerimenti e il modo in cui sono ordinati.","Consente di controllare se, quando si copia senza aver effettuato una selezione, viene copiata la riga corrente.","Controlla se l'evidenziazione della sintassi deve essere copiata negli Appunti.","Controlla se calcolare i completamenti in base alle parole presenti nel documento.","Consente di selezionare sempre il primo suggerimento.","Consente di selezionare suggerimenti recenti a meno che continuando a digitare non ne venga selezionato uno, ad esempio `console.| -> console.log` perché `log` è stato completato di recente.","Consente di selezionare i suggerimenti in base a prefissi precedenti che hanno completato tali suggerimenti, ad esempio `co -> console` e `con -> const`.","Controlla la modalità di preselezione dei suggerimenti durante la visualizzazione dell'elenco dei suggerimenti.","Dimensione del carattere per il widget dei suggerimenti. Se impostato su '0', viene utilizzato il valore di '#editor.fontSize#'.","Altezza della riga per il widget dei suggerimenti. Se impostato su '0', viene utilizzato il valore '#editor.lineHeight#'.","La funzionalità di completamento con tasto TAB inserirà il migliore suggerimento alla pressione del tasto TAB.","Disabilita le funzionalità di completamento con tasto TAB.","Completa i frammenti con il tasto TAB quando i rispettivi prefissi corrispondono. Funziona in modo ottimale quando 'quickSuggestions' non è abilitato.","Abilità la funzionalità di completamento con tasto TAB.","Controlla se i suggerimenti di filtro e ordinamento valgono per piccoli errori di battitura","Controlla se l'ordinamento privilegia le parole che appaiono più vicine al cursore.","Controlla se condividere le selezioni dei suggerimenti memorizzati tra aree di lavoro e finestre (richiede `#editor.suggestSelection#`).","Controlla se un frammento attivo previene i suggerimenti rapidi.","Consente di controllare se mostrare o nascondere le icone nei suggerimenti.","Consente di controllare il numero di suggerimenti mostrati da IntelliSense prima di visualizzare una barra di scorrimento (massimo 15).","Consente di controllare se filtrare alcuni tipi di suggerimenti in IntelliSense. L'elenco dei tipi di suggerimenti è disponibile in: https://code.visualstudio.com/docs/editor/intellisense#_types-of-completions.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `method`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `function`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `constructor`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `field`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `variable`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `class`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `struct`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `interface`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `module`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `property`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `event`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `operator`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `unit`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `value`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `constant`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `enum`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `enumMember`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `keyword`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `text`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `color`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `file`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `reference`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `customcolor`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `folder`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `typeParameter`.","Se è impostato su `false`, IntelliSense non visualizza mai i suggerimenti per `snippet`.","Controlla il comportamento di comandi di tipo 'Vai a', come Vai alla definizione, quando esistono più posizioni di destinazione.","Mostra la visualizzazione rapida dei risultati (impostazione predefinita)","Passa al risultato principale e mostra una visualizzazione rapida","Passa al risultato principale e abilita l'esplorazione senza anteprima per gli altri","Controlla se l'editor deve evidenziare gli elementi corrispondenti simili alla selezione.","Controlla se l'editor deve evidenziare le occorrenze di simboli semantici.","Controlla il numero di effetti che possono essere visualizzati nella stessa posizione nel righello delle annotazioni.","Controlla se deve essere disegnato un bordo intorno al righello delle annotazioni.","Controllo dello stile di animazione del cursore.","Ingrandisce il carattere dell'editor quando si usa la rotellina del mouse e si tiene premuto 'CTRL'.","Controlla se l'animazione del cursore con anti-aliasing deve essere abilitata.","Controlla lo stile del cursore.","Controlla la larghezza del cursore quando `#editor.cursorStyle#` è impostato su `line`.","Abilita/Disabilita i caratteri legatura.","Controlla se il cursore deve essere nascosto nel righello delle annotazioni.","Render whitespace characters except for single spaces between words.","Esegui il rendering dei caratteri di spazio vuoto solo nel testo selezionato.","Controlla in che modo l'editor deve eseguire il rendering dei caratteri di spazio vuoto.","Controlla se l'editor deve eseguire il rendering dei caratteri di controllo.","Controlla se l'editor deve eseguire il rendering delle guide con rientro.","Controlla se l'editor deve evidenziare la guida con rientro attiva","Mette in evidenza sia la barra di navigazione sia la riga corrente.","Consente di controllare in che modo l'editor deve eseguire il rendering dell'evidenziazione di riga corrente.","Controlla se l'editor visualizza CodeLens.","Controlla se per l'editor è abilitata la riduzione del codice.","Controlla in che modo vengono calcolati gli intervalli di riduzione. Con 'auto' viene usata la strategia di riduzione specifica della lingua. Con 'indentation' viene usata forzatamente la strategia di riduzione basata sui rientri.","Controlla se i controlli di riduzione sul margine della barra di scorrimento sono automaticamente nascosti.","Evidenzia le parentesi corrispondenti quando se ne seleziona una.","Controlla se l'editor deve eseguire il rendering del margine verticale del glifo. Il margine del glifo viene usato principalmente per il debug.","Inserimento ed eliminazione dello spazio vuoto dopo le tabulazioni.","Rimuovi gli spazi finali inseriti automaticamente.","Mantiene aperti gli editor rapidi anche quando si fa doppio clic sul contenuto o si preme 'ESC'.","Controlla se l'editor deve consentire lo spostamento di selezioni tramite trascinamento della selezione.","L'editor utilizzerà API della piattaforma per rilevare quando è collegata un'utilità per la lettura dello schermo.","L'editor sarà definitivamente ottimizzato per l'utilizzo con un'utilità per la lettura dello schermo.","L'editor non sarà mai ottimizzato per l'utilizzo con un'utilità per la lettura dello schermo.","Controlla se l'editor deve essere eseguito in una modalità ottimizzata per le utilità per la lettura dello schermo.","Controllo dissolvenza del codice inutilizzato.","Controlla se l'editor deve individuare i collegamenti e renderli selezionabili.","Controlla se l'editor deve eseguire il rendering del selettore di colore e degli elementi Decorator di tipo colore inline.","Abilita la lampadina delle azioni codice nell'editor.","Per motivi di prestazioni le righe di lunghezza superiore non verranno tokenizzate","Controlla se l'azione di organizzazione delle impostazioni deve essere eseguita al salvataggio del file.","Controlla se eseguire l'azione di correzione automatica al salvataggio del file.","Tipi di azione codice da eseguire durante il salvataggio.","Timeout in millisecondi dopo il quale le azioni codice eseguite al salvataggio vengono annullate.","Controlla se gli appunti primari di Linux devono essere supportati.","Controlla se l'editor diff mostra le differenze affiancate o incorporate.","Controlla se l'editor diff mostra come differenze le modifiche relative a spazi vuoti iniziali e finali.","Gestione speciale dei file di grandi dimensioni per disabilitare alcune funzionalità che fanno un uso intensivo della memoria.","Consente di controllare se l'editor diff mostra gli indicatori +/- per le modifiche aggiunte/rimosse."],
"vs/editor/common/config/editorOptions":["L'editor non è accessibile in questo momento. Premere Alt+F1 per le opzioni.","Contenuto editor"],"vs/editor/common/modes/modesRegistry":["Testo normale"],
"vs/editor/common/standaloneStrings":["Nessuna selezione","Riga {0}, colonna {1} ({2} selezionate)","Riga {0}, colonna {1}","{0} selezioni ({1} caratteri selezionati)","{0} selezioni","Modifica dell'impostazione 'accessibilitySupport' su 'on' in corso.","Apertura della pagina di documentazione sull'accessibilità dell'editor in corso.","in un riquadro di sola lettura di un editor diff.","in un riquadro di un editor diff."," in un editor di codice di sola lettura"," in un editor di codice","Per configurare l'editor da ottimizzare per l'utilizzo con un'utilità per la lettura dello schermo, premere Comando+E.","Per configurare l'editor da ottimizzare per l'utilizzo con un'utilità per la lettura dello schermo, premere CTRL+E.","L'editor è configurato per essere ottimizzato per l'utilizzo con un'utilità per la lettura dello schermo.","L'editor è configurato per non essere ottimizzato per l'utilizzo con un'utilità per la lettura dello schermo, che non viene usata in questo momento.","Premere TAB nell'editor corrente per spostare lo stato attivo sull'elemento con stato attivabile successivo. Per attivare/disattivare questo comportamento, premere {0}.","Premere TAB nell'editor corrente per spostare lo stato attivo sull'elemento con stato attivabile successivo. Il comando {0} non può essere attualmente attivato con un tasto di scelta rapida.","Premere TAB nell'editor corrente per inserire il carattere di tabulazione. Per attivare/disattivare questo comportamento, premere {0}.","Premere TAB nell'editor corrente per inserire il carattere di tabulazione. Il comando {0} non può essere attualmente attivato con un tasto di scelta rapida.","Premere Comando+H per aprire una finestra del browser contenente maggiori informazioni correlate all'accessibilità dell'editor.","Premere CTRL+H per aprire una finestra del browser contenente maggiori informazioni correlate all'accessibilità dell'editor.","Per chiudere questa descrizione comando e tornare all'editor, premere ESC o MAIUSC+ESC.","Visualizza la Guida sull'accessibilità","Sviluppatore: Controlla token","Vai a riga {0} e carattere {1}","Vai a riga {0}","Digitare un numero di riga a cui passare compreso tra 1 e {0}","Digitare un carattere compreso tra 1 e {0} a cui passare","Riga corrente: {0}. Passa a riga {1}.","Digitare un numero di riga, seguito da due punti facoltativi e da un numero di carattere a cui passare","Vai alla riga...","{0}, {1}, comandi","{0}, comandi","Digitare il nome di un'azione da eseguire","Riquadro comandi","{0}, simboli","Digitare il nome di un identificatore a cui passare","Vai al simbolo...","simboli ({0})","moduli ({0})","classi ({0})","interfacce ({0})","metodi ({0})","funzioni ({0})","proprietà ({0})","variabili ({0})","variabili ({0})","costruttori ({0})","chiamate ({0})","Contenuto editor","Premere CTRL+F1 per le opzioni di accessibilità.","Premere ALT+F1 per le opzioni di accessibilità.","Attiva/disattiva tema a contrasto elevato","Effettuate {0} modifiche in {1} file"],
"vs/editor/common/view/editorColorRegistry":["Colore di sfondo per l'evidenziazione della riga alla posizione del cursore.","Colore di sfondo per il bordo intorno alla riga alla posizione del cursore.","Colore di sfondo degli intervalli evidenziati, ad esempio dalle funzionalità Quick Open e Trova. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore di sfondo del bordo intorno agli intervalli selezionati.","Colore del cursore dell'editor.","Colore di sfondo del cursore editor. Permette di personalizzare il colore di un carattere quando sovrapposto da un blocco cursore.","Colore dei caratteri di spazio vuoto nell'editor.","Colore delle guide per i rientri dell'editor.","Colore delle guide di indentazione dell'editor attivo","Colore dei numeri di riga dell'editor.","Colore dei numeri per la riga attiva dell'editor","Id è deprecato. In alternativa utilizzare 'editorLineNumber.activeForeground'.","Colore dei numeri per la riga attiva dell'editor","Colore dei righelli dell'editor.","Colore primo piano delle finestre di CodeLens dell'editor","Colore di sfondo delle parentesi corrispondenti","Colore delle caselle di parentesi corrispondenti","Colore del bordo del righello delle annotazioni.","Colore di sfondo della barra di navigazione dell'editor. La barra contiene i margini di glifo e i numeri di riga.","Colore del bordo del codice sorgente non necessario (non usato) nell'editor.","Opacità del codice sorgente non necessario (non usato) nell'editor. Ad esempio, con \"#000000c0\" il rendering del codice verrà eseguito con il 75% di opacità. Per i temi a contrasto elevato, usare il colore del tema 'editorUnnecessaryCode.border' per sottolineare il codice non necessario invece di opacizzarlo.","Colore del marcatore del righello delle annotazioni per gli errori.","Colore del marcatore del righello delle annotazioni per gli avvisi.","Colore del marcatore del righello delle annotazioni per i messaggi di tipo informativo."],
"vs/editor/contrib/bracketMatching/bracketMatching":["Colore del marcatore del righello delle annotazioni per la corrispondenza delle parentesi.","Vai alla parentesi","Seleziona fino alla parentesi","Vai alla parentesi &&quadra"],"vs/editor/contrib/caretOperations/caretOperations":["Sposta il punto di inserimento a sinistra","Sposta il punto di inserimento a destra"],"vs/editor/contrib/caretOperations/transpose":["Trasponi lettere"],"vs/editor/contrib/clipboard/clipboard":["Taglia","&&Taglia","Copia","&&Copia","Incolla","&&Incolla","Copia con evidenziazione sintassi"],
"vs/editor/contrib/codeAction/codeActionCommands":["Correzione rapida...","Azioni codice non disponibili","Azioni codice non disponibili","Effettua refactoring...","Refactoring non disponibili","Azione origine...","Azioni origine non disponibili","Organizza gli Imports","Azioni di organizzazione Imports non disponibili","Correggi tutto","Non è disponibile alcuna azione Correggi tutto","Correzione automatica...","Non sono disponibili correzioni automatiche"],"vs/editor/contrib/codeAction/lightBulbWidget":["Mostra correzioni ({0})","Mostra correzioni"],"vs/editor/contrib/comment/comment":["Attiva/disattiva commento per la riga","Attiva/Disattiva commento per &&riga","Aggiungi commento per la riga","Rimuovi commento per la riga","Attiva/Disattiva commento per il blocco","Attiva/Disattiva commento per &&blocco"],"vs/editor/contrib/contextmenu/contextmenu":["Mostra il menu di scelta rapida editor"],"vs/editor/contrib/cursorUndo/cursorUndo":["Annullamento temporaneo"],
"vs/editor/contrib/find/findController":["Trova","&&Trova","Trova nella selezione","Trova successivo","Trova successivo","Trova precedente","Trova precedente","Trova selezione successiva","Trova selezione precedente","Sostituisci","&&Sostituisci"],"vs/editor/contrib/find/findWidget":["Trova","Trova","Corrispondenza precedente","Corrispondenza successiva","Trova nella selezione","Chiudi","Sostituisci","Sostituisci","Sostituisci","Sostituisci tutto","Attiva/Disattiva modalità sostituzione","Solo i primi {0} risultati vengono evidenziati, ma tutte le operazioni di ricerca funzionano su tutto il testo.","{0} di {1}","Nessun risultato","{0} trovato","{0} trovato per {1}","{0} trovato per {1} a riga {2}","{0} trovato per {1}","Il tasto di scelta rapida CTRL+INVIO ora consente di inserire l'interruzione di linea invece di sostituire tutto. Per eseguire l'override di questo comportamento, è possibile modificare il tasto di scelta rapida per editor.action.replaceAll."],
"vs/editor/contrib/folding/folding":["Espandi","Espandi in modo ricorsivo","Riduci","Riduci in modo ricorsivo","Riduci tutti i blocchi commento","Riduci tutte le regioni","Espandi tutte le regioni","Riduci tutto","Espandi tutto","Livello riduzione {0}"],"vs/editor/contrib/fontZoom/fontZoom":["Zoom In del Font Editor","Zoom Reset del Font Editor","Reset dello Zoom del Font Editor"],"vs/editor/contrib/format/format":["È stata apportata 1 modifica di formattazione a riga {0}","Sono state apportate {0} modifiche di formattazione a riga {1}","È stata apportata 1 modifica di formattazione tra le righe {0} e {1}","Sono state apportate {0} modifiche di formattazione tra le righe {1} e {2}"],"vs/editor/contrib/format/formatActions":["Formatta documento","Formatta selezione"],
"vs/editor/contrib/goToDefinition/goToDefinitionCommands":["Non è stata trovata alcuna definizione per '{0}'","Non è stata trovata alcuna definizione"," - Definizioni di {0}","Vai a definizione","Apri definizione lateralmente","Visualizza la definizione","Non è stata trovata alcuna dichiarazione per '{0}'","Dichiarazione non trovata","– {0} dichiarazioni","Vai a dichiarazione","Non è stata trovata alcuna dichiarazione per '{0}'","Dichiarazione non trovata","– {0} dichiarazioni","Anteprima dichiarazione","Non sono state trovate implementazioni per '{0}'","Non sono state trovate implementazioni","- {0} implementazioni","Vai all'implementazione","Anteprima implementazione","Non sono state trovate definizioni di tipi per '{0}'","Non sono state trovate definizioni di tipi"," - {0} definizioni di tipo","Vai alla definizione di tipo","Anteprima definizione di tipo","Vai alla &&definizione","Vai alla &&definizione di tipo","&&Vai all'implementazione"],
"vs/editor/contrib/goToDefinition/goToDefinitionMouse":["Fare clic per visualizzare {0} definizioni."],"vs/editor/contrib/goToDefinition/goToDefinitionResultsNavigation":["Simbolo {0} di {1}, {2} per il successivo","Simbolo {0} di {1}"],"vs/editor/contrib/gotoError/gotoError":["Vai al problema successivo (Errore, Avviso, Informazioni)","Vai al problema precedente (Errore, Avviso, Informazioni)","Vai al problema successivo nei file (Errore, Avviso, Informazioni)","Vai al problema precedente nei file (Errore, Avviso, Informazioni)","&&Problema successivo","&&Problema precedente"],"vs/editor/contrib/gotoError/gotoErrorWidget":["{0} di {1} problemi","{0} di {1} problema","Colore per gli errori del widget di spostamento tra marcatori dell'editor.","Colore per gli avvisi del widget di spostamento tra marcatori dell'editor.","Colore delle informazioni del widget di navigazione marcatori dell'editor.","Sfondo del widget di spostamento tra marcatori dell'editor."],
"vs/editor/contrib/hover/hover":["Visualizza passaggio del mouse"],"vs/editor/contrib/hover/modesContentHover":["Caricamento...","Posiziona puntatore sul problema","Verifica disponibilità correzioni rapide...","Non sono disponibili correzioni rapide","Correzione rapida..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Sostituisci con il valore precedente","Sostituisci con il valore successivo"],
"vs/editor/contrib/linesOperations/linesOperations":["Copia la riga in alto","&&Copia riga in alto","Copia la riga in basso","Co&&pia riga in basso","Sposta la riga in alto","Sposta riga in &&alto","Sposta la riga in basso","Sposta riga in &&basso","Ordinamento righe crescente","Ordinamento righe decrescente","Taglia spazio vuoto finale","Elimina riga","Imposta un rientro per la riga","Riduci il rientro per la riga","Inserisci la riga sopra","Inserisci la riga sotto","Elimina tutto a sinistra","Elimina tutto a destra","Unisci righe","Trasponi caratteri intorno al cursore","Converti in maiuscolo","Converti in minuscolo","Trasforma in Tutte Iniziali Maiuscole"],"vs/editor/contrib/links/links":["Esegui il comando","selezionare il collegamento","CMD+clic","CTRL+clic","Opzione+clic","ALT+clic","Non è stato possibile aprire questo collegamento perché il formato non è valido: {0}","Non è stato possibile aprire questo collegamento perché manca la destinazione.","Apri il collegamento"],
"vs/editor/contrib/message/messageController":["Non è possibile modificare nell'editor di sola lettura"],"vs/editor/contrib/multicursor/multicursor":["Aggiungi cursore sopra","&&Aggiungi cursore sopra","Aggiungi cursore sotto","A&&ggiungi cursore sotto","Aggiungi cursore alla fine delle righe","Aggiungi c&&ursori a fine riga","Aggiungi cursori alla fine","Aggiungi cursori all'inizio","Aggiungi selezione a risultato ricerca successivo","Aggiungi &&occorrenza successiva","Aggiungi selezione a risultato ricerca precedente","Aggiungi occorrenza &&precedente","Sposta ultima selezione a risultato ricerca successivo","Sposta ultima selezione a risultato ricerca precedente","Seleziona tutte le occorrenze del risultato ricerca","Seleziona &&tutte le occorrenze","Cambia tutte le occorrenze"],"vs/editor/contrib/parameterHints/parameterHints":["Attiva i suggerimenti per i parametri"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, suggerimento"],
"vs/editor/contrib/referenceSearch/peekViewWidget":["Chiudi"],"vs/editor/contrib/referenceSearch/referenceSearch":[" - Riferimenti di {0}","Anteprima riferimenti"],"vs/editor/contrib/referenceSearch/referencesController":["Caricamento..."],"vs/editor/contrib/referenceSearch/referencesModel":["simbolo in {0} alla riga {1} colonna {2}","1 simbolo in {0}, percorso completo {1}","{0} simboli in {1}, percorso completo {2}","Non sono stati trovati risultati","Trovato 1 simbolo in {0}","Trovati {0} simboli in {1}","Trovati {0} simboli in {1} file"],"vs/editor/contrib/referenceSearch/referencesTree":["Non è stato possibile risolvere il file.","{0} riferimenti","{0} riferimento"],
"vs/editor/contrib/referenceSearch/referencesWidget":["anteprima non disponibile","Riferimenti","Nessun risultato","Riferimenti","Colore di sfondo dell'area del titolo della visualizzazione rapida.","Colore del titolo della visualizzazione rapida.","Colore delle informazioni del titolo della visualizzazione rapida.","Colore dei bordi e della freccia della visualizzazione rapida.","Colore di sfondo dell'elenco risultati della visualizzazione rapida.","Colore primo piano dei nodi riga nell'elenco risultati della visualizzazione rapida.","Colore primo piano dei nodi file nell'elenco risultati della visualizzazione rapida.","Colore di sfondo della voce selezionata nell'elenco risultati della visualizzazione rapida.","Colore primo piano della voce selezionata nell'elenco risultati della visualizzazione rapida.","Colore di sfondo dell'editor di visualizzazioni rapide.","Colore di sfondo della barra di navigazione nell'editor visualizzazione rapida.","Colore dell'evidenziazione delle corrispondenze nell'elenco risultati della visualizzazione rapida.","Colore dell'evidenziazione delle corrispondenze nell'editor di visualizzazioni rapide.","Bordo dell'evidenziazione delle corrispondenze nell'editor di visualizzazioni rapide."],
"vs/editor/contrib/rename/rename":["Nessun risultato.","Si è verificato un errore sconosciuto durante la risoluzione del percorso di ridenominazione","Correttamente rinominato '{0}' in '{1}'. Sommario: {2}","L'esecuzione dell'operazione di ridenominazione non è riuscita.","Rinomina simbolo"],"vs/editor/contrib/rename/renameInputField":["Consente di rinominare l'input. Digitare il nuovo nome e premere INVIO per eseguire il commit."],"vs/editor/contrib/smartSelect/smartSelect":["Espandi selezione","Espan&&di selezione","Riduci selezione","&&Riduci selezione"],"vs/editor/contrib/snippet/snippetVariables":["Domenica","Lunedì","Martedì","Mercoledì","Giovedì","Venerdì","Sabato","Dom","Lun","Mar","Mer","Gio","Ven","Sab","Gennaio","Febbraio","Marzo","Aprile","Mag","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre","Gen","Feb","Mar","Apr","Mag","Giu","Lug","Ago","Set","Ott","Nov","Dic"],
"vs/editor/contrib/suggest/suggestController":["In seguito all'accettazione di '{0}' sono state apportate altre {1} modifiche","Attiva suggerimento"],"vs/editor/contrib/suggest/suggestWidget":["Colore di sfondo del widget dei suggerimenti.","Colore del bordo del widget dei suggerimenti.","Colore primo piano del widget dei suggerimenti.","Colore di sfondo della voce selezionata del widget dei suggerimenti.","Colore delle evidenziazioni corrispondenze nel widget dei suggerimenti.","Altre informazioni...{0}","Meno informazioni... {0}","Caricamento...","Caricamento...","Non ci sono suggerimenti.","Elemento {0}, documenti: {1}"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Attiva/Disattiva l'uso di TAB per spostare lo stato attivo","Se si preme TAB, lo stato attivo verrà spostato sull'elemento con stato attivabile successivo.","Se si preme TAB, verrà inserito il carattere di tabulazione"],"vs/editor/contrib/tokenization/tokenization":["Sviluppatore: Forza retokenizzazione"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Colore di sfondo di un simbolo durante l'accesso in lettura, ad esempio durante la lettura di una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore di sfondo di un simbolo durante l'accesso in scrittura, ad esempio durante la scrittura in una variabile. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del bordo di un simbolo durante l'accesso in lettura, ad esempio durante la lettura di una variabile.","Colore del bordo di un simbolo durante l'accesso in scrittura, ad esempio durante la scrittura in una variabile.","Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del marcatore del righello delle annotazioni per le evidenziazioni dei simboli di accesso in scrittura. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Vai al prossimo simbolo evidenziato","Vai al precedente simbolo evidenziato","Attiva/disattiva evidenziazione simbolo"],
"vs/platform/configuration/common/configurationRegistry":["Override configurazione predefinita","Consente di configurare le impostazioni dell'editor di cui eseguire l'override per un linguaggio.","Non è possibile registrare '{0}'. Corrisponde al criterio di proprietà '\\\\[.*\\\\]$' per la descrizione delle impostazioni dell'editor specifiche del linguaggio. Usare il contributo 'configurationDefaults'.","Non è possibile registrare '{0}'. Questa proprietà è già registrata."],"vs/platform/keybinding/common/abstractKeybindingService":["È stato premuto ({0}). In attesa del secondo tasto...","La combinazione di tasti ({0}, {1}) non è un comando."],
"vs/platform/list/browser/listService":["Workbench","Rappresenta il tasto 'Control' in Windows e Linux e il tasto 'Comando' in macOS.","Rappresenta il tasto 'Alt' in Windows e Linux e il tasto 'Opzione' in macOS.","Il modificatore da utilizzare per aggiungere un elemento di alberi e liste ad una selezione multipla con il mouse (ad esempio in Esplora Risorse, apre gli editor e le viste scm). Le gesture del mouse 'Apri a lato' - se supportate - si adatteranno in modo da non creare conflitti con il modificatore di selezione multipla.","Controlla l'apertura degli elementi di alberi ed elenchi tramite il mouse (se supportato). Per i nodi con figli, questa impostazione ne controlla l'apertura tramite singolo o doppio clic. Si noti che alcuni alberi ed elenchi potrebbero scegliere di ignorare questa impostazione se non applicabile.","Controlla se elenchi e alberi supportano lo scorrimento orizzontale in Workbench.","Controlla se gli alberi supportano lo scorrimento orizzontale in Workbench.","Questa impostazione è deprecata. In alternativa, usare '{0}'.","Controlla il rientro dell'albero in pixel.","Controlla se l'albero deve eseguire il rendering delle guide per i rientri.","Con lo stile di spostamento da tastiera simple lo stato attivo si trova sugli elementi che corrispondono all'input da tastiera. L'abbinamento viene effettuato solo in base ai prefissi.","Con lo stile di spostamento da tastiera highlight vengono evidenziati gli elementi corrispondenti all'input da tastiera. Spostandosi ulteriormente verso l'alto o verso il basso ci si sposterà solo negli elementi evidenziati.","Con lo stile di spostamento da tastiera filter verranno filtrati e nascosti tutti gli elementi che non corrispondono all'input da tastiera.","Controlla lo stile di spostamento da tastiera per elenchi e alberi nel workbench. Le opzioni sono: simple, highlight e filter.","Controlla se gli spostamenti da tastiera per elenchi e alberi vengono attivati semplicemente premendo un tasto. Se è impostato su `false`, gli spostamenti da tastiera vengono attivati solo durante l'esecuzione del comando `list.toggleKeyboardNavigation`, al quale è possibile assegnare un tasto di scelta rapida."],
"vs/platform/markers/common/markers":["Errore","Avviso","Info"],
"vs/platform/theme/common/colorRegistry":["Colore primo piano generale. Questo colore viene usato solo se non è sostituito da quello di un componente.","Colore primo piano globale per i messaggi di errore. Questo colore è utilizzato solamente se non viene sottoposto a override da un componente.","Colore dei bordi degli elementi evidenziati. Questo colore è utilizzato solo se non viene sovrascritto da un componente.","Un bordo supplementare attorno agli elementi per contrastarli maggiormente rispetto agli altri.","Un bordo supplementare intorno agli elementi attivi per contrastarli maggiormente rispetto agli altri.","Colore primo piano dei link nel testo.","Colore di sfondo per i blocchi di codice nel testo.","Colore ombreggiatura dei widget, ad es. Trova/Sostituisci all'interno dell'editor.","Sfondo della casella di input.","Primo piano della casella di input.","Bordo della casella di input.","Colore del bordo di opzioni attivate nei campi di input.","Colore di sfondo di opzioni attivate nei campi di input.","Colore di sfondo di convalida dell'input di tipo Informazione.","Colore primo piano di convalida dell'input di tipo Informazione.","Colore del bordo della convalida dell'input di tipo Informazione.","Colore di sfondo di convalida dell'input di tipo Avviso.","Colore primo piano di convalida dell'input di tipo Avviso.","Colore del bordo della convalida dell'input di tipo Avviso.","Colore di sfondo di convalida dell'input di tipo Errore.","Colore primo piano di convalida dell'input di tipo Errore.","Colore del bordo della convalida dell'input di tipo Errore.","Sfondo dell'elenco a discesa.","Primo piano dell'elenco a discesa.","Colore di sfondo dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.","Colore primo piano dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.","Colore di sfondo dell'elenco/albero per l'elemento selezionato quando l'elenco/albero è attivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.","Colore primo piano Elenco/Struttura ad albero per l'elemento selezionato quando l'Elenco/Struttura ad albero è attivo. Un Elenco/Struttura ad albero attivo\nha il focus della tastiera, uno inattivo no.","Colore di sfondo dell'elenco/albero per l'elemento selezionato quando l'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.","Colore primo piano dell'elenco/albero per l'elemento selezionato quando l'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, a differenza di uno inattivo.","Colore di sfondo dell'elenco/albero per l'elemento con lo stato attivo quando l'elenco/albero è inattivo. Un elenco/albero attivo ha lo stato attivo della tastiera, uno inattivo no.","Sfondo Elenco/Struttura ad albero al passaggio del mouse sugli elementi.","Primo piano Elenco/Struttura ad albero al passaggio del mouse sugli elementi.","Sfondo Elenco/Struttura ad albero durante il trascinamento degli elementi selezionati.","Colore primo piano Elenco/Struttura ad albero delle occorrenze trovate durante la ricerca nell'Elenco/Struttura ad albero.","Colore di sfondo del widget del filtro per tipo in elenchi e alberi.","Colore del contorno del widget del filtro per tipo in elenchi e alberi.","Colore del contorno del widget del filtro per tipo in elenchi e alberi quando non sono presenti corrispondenze.","Colore del tratto dell'albero per le guide per i rientri.","Colore di selezione rapida per il raggruppamento delle etichette.","Colore di selezione rapida per il raggruppamento dei bordi.","Colore di sfondo del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati di una ricerca.","Colore primo piano del badge. I badge sono piccole etichette informative, ad esempio per mostrare il conteggio dei risultati di una ricerca.","Ombra della barra di scorrimento per indicare lo scorrimento della visualizzazione.","Colore di sfondo del cursore della barra di scorrimento.","Colore di sfondo del cursore della barra di scorrimento al passaggio del mouse.","Colore di sfondo del cursore della barra di scorrimento quando si fa clic con il mouse.","Colore di sfondo dell'indicatore di stato che può essere mostrato per operazioni a esecuzione prolungata.","Colore del bordo del menu.","Colore di primo piano delle voci di menu.","Colore di sfondo delle voci di menu.","Colore di primo piano della voce di menu selezionata nei menu.","Colore di sfondo della voce di menu selezionata nei menu.","Colore del bordo della voce di menu selezionata nei menu.","Colore di un elemento separatore delle voci di menu.","Colore primo piano degli indicatori di errore nell'editor.","Colore del bordo delle caselle di errore nell'editor.","Colore primo piano degli indicatori di avviso nell'editor.","Colore del bordo delle caselle di avviso nell'editor.","Colore primo piano degli indicatori di informazioni nell'editor.","Colore del bordo delle caselle informative nell'editor.","Colore primo piano degli indicatori di suggerimento nell'editor.","Colore del bordo delle caselle dei suggerimenti nell'editor.","Colore di sfondo dell'editor.","Colore primo piano predefinito dell'editor.","Colore di sfondo dei widget dell'editor, ad esempio Trova/Sostituisci.","Colore primo piano dei widget dell'editor, ad esempio Trova/Sostituisci.","Colore del bordo dei widget dell'editor. Il colore viene usato solo se il widget sceglie di avere un bordo e se il colore non è sottoposto a override da un widget.","Colore del bordo della barra di ridimensionamento dei widget dell'editor. Il colore viene utilizzato solo se il widget sceglie di avere un bordo di ridimensionamento e se il colore non è sottoposto ad override da un widget.","Colore della selezione dell'editor.","Colore del testo selezionato per il contrasto elevato.","Colore della selezione in un editor inattivo. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore delle aree con lo stesso contenuto della selezione. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del bordo delle regioni con lo stesso contenuto della selezione.","Colore della corrispondenza di ricerca corrente.","Colore degli altri risultati della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore dell'intervallo di limite della ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del bordo della corrispondenza della ricerca corrente.","Colore del bordo delle altre corrispondenze della ricerca.","Colore del bordo dell'intervallo che limita la ricerca. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Evidenziazione sotto la parola per cui è visualizzata un'area sensibile al passaggio del mouse. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore di sfondo dell'area sensibile al passaggio del mouse dell'editor.","Colore del bordo dell'area sensibile al passaggio del mouse dell'editor.","Colore di sfondo della barra di stato sensibile al passaggio del mouse dell'editor.","Colore dei collegamenti attivi.","Colore di sfondo per il testo che è stato inserito. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore di sfondo per il testo che è stato rimosso. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del contorno del testo che è stato inserito.","Colore del contorno del testo che è stato rimosso.","Colore del bordo tra due editor di testo.","Colore di sfondo dell'evidenziazione della tabulazione di un frammento.","Colore del bordo dell'evidenziazione della tabulazione di un frammento.","Colore di sfondo dell'evidenziazione della tabulazione finale di un frammento.","Colore del bordo dell'evidenziazione della tabulazione finale di un frammento.","Colore del marcatore del righello delle annotazioni per la ricerca di corrispondenze. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del marcatore del righello delle annotazioni per le evidenziazioni delle selezioni. Il colore non deve essere opaco per evitare di nascondere le decorazioni sottostanti.","Colore del marcatore della minimappa per la ricerca delle corrispondenze."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.it.js.map