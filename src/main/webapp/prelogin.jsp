<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ page import="org.swallow.mfa.RegisterMFA"  %>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<style>
	body {
		margin: 0px;
		overflow: hidden
	}
</style>
<body >

	<script type="text/javascript">
// 		var screenTitle = "";
// 		screenTitle = getMessage("label.accountschedulesweepdetails.title.window", null);
// 		document.title = screenTitle;
			ShowErrMsgWindow('${errorsAtLogin}');

			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			requestURL=requestURL.substring(0,idy+1) ;
			var screenRoute = "LogonScreen";


			var callerMethod= '${requestScope.callerMethod}';
			var isLogoutRedirect= '${requestScope.isLogoutRedirect}';





			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();
			};

			window.onunload = call;



			/**
			 * Close window
			 */
			function closeWindow() {
				window.close();
			}

			function doRedirectToMFA(){

			}

			function doRedirectToLoginPage(){

				var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;

				var sURL = requestURL + appName+"/logon.do?isFromPreLoginScreen=true";
				window.location.replace(sURL);
			}



			function doRedirectToMFAPage(){

				var signInMFAUrl = "<%=RegisterMFA.getInstance().getLoginUrl()%>";
				window.location.replace(signInMFAUrl);
			}


			/**
			 * help
			 * This function opens the help screen
			 */
			function help() {
				openWindow(
						buildPrintURL('print', 'Logon Screen'),
						'sectionprintdwindow',
						'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
						'true')
			}

			function killYes() {
				   document.forms[0].elements['killSession'].value='Y';
				   document.forms[0].submit();

// 				   var sURL = requestURL + appName+"/logon.do?killSession=Y";
// 					window.location.replace(sURL);

			}
			function killNo() {
			}


	</script>
	<form action="logon.do?">
	<!-- Mantis 2183: Added by M.Bouraoui to pass the hidden session id  "clientSession" -->
	<input type=hidden name="method" value="login" />
	<!-- Added by Meftah & Kais to pass the hidden variable "killSession" -->
	<input type="hidden" name="killSession" value="N"/>
	<input type="hidden" name="fromPreLogin" value="Y"/>
	 <input type="hidden" name="mfaUserId"  value="${mfaUserId}" />
	</form>
    <%@ include file="/angularscripts.jsp"%>

</body>
<!-- If killSessionStatus is 'K', the value of killSession variable become Y then on next login the existing session will be killed and new session will be created -->
<c:if test="${requestScope.killSessionStatus == 'K'}">
    <!--On this case the session will be killed and  new session will be created-->
    <script>
    //set the password with the dummy password recuved and make userId and pasword Field disabled:

     ShowErrMsgWindowWithBtn("", "<fmt:message key='alert.killSession'/>", YES_NO, killYes, killNo);
   </script>

</c:if>
</html>