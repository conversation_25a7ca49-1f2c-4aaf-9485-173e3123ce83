(window.webpackJsonp=window.webpackJsonp||[]).push([[71],{"5A4j":function(e,t,a){"use strict";a.r(t);var n=a("CcnG"),i=a("mrSG"),l=a("ZYCi"),r=a("447K"),o=a("R1Kr"),d=a("xRo1"),s=function(e){function t(t,a){var n=e.call(this,a,t)||this;return n.commonService=t,n.element=a,n.deletedRow=-1,n.paramArray=[],n.sceduleGridData=[],n.xml="",n.enabledRow=-1,n.defParamsData=[],n.parentGridData=[],n.nameChangedFlag=!1,n.rowDeletedFlag=!1,n.paramsList=[],n.swtalert=new r.bb(t),n}return i.d(t,e),t.prototype.ngOnInit=function(){this.paramGrid=this.paramCanvas.addChild(r.hb),this.scenarioIdLbl.text=r.Wb.getPredictMessage("scenario.scenarioId",null),this.paramDesc.text=r.Wb.getPredictMessage("scenario.paramDesc",null),this.cancelButton.label=r.Wb.getPredictMessage("button.cancel",null),this.addButton.label=r.Wb.getPredictMessage("button.add",null),this.deleteButton.label=r.Wb.getPredictMessage("button.delete",null),this.okButton.label=r.Wb.getPredictMessage("button.ok",null),this.scenarioIdtxt.toolTip=r.Wb.getPredictMessage("scenario.scenarioId",null),this.cancelButton.toolTip=r.Wb.getPredictMessage("tooltip.cancelbutton",null),this.addButton.toolTip=r.Wb.getPredictMessage("tooltip.defineParams.add",null),this.deleteButton.toolTip=r.Wb.getPredictMessage("tooltip.defineParams.delete",null),this.okButton.toolTip=r.Wb.getPredictMessage("tooltip.ok",null),this.paramGrid.editable=!0},t.prototype.onLoad=function(){var e=this;this.defParamsData=[],window.opener&&window.opener.instanceElement&&(this.paramsEventsFromParent=window.opener.instanceElement.sendGeneralDataToDefParams(),this.scenarioIdtxt.text=this.paramsEventsFromParent.scenarioId,this.paramGrid.CustomGrid(this.paramsEventsFromParent.gridData.metadata),this.gridParams=this.paramsEventsFromParent.xmlData,this.methodName=this.paramsEventsFromParent.methodName,this.parentGridData=this.paramsEventsFromParent.genaralGridData),this.gridParams&&this.populateData(),this.paramGrid.ITEM_CHANGED.subscribe(function(t){e.alertUser(t)}),this.paramGrid.onRowClick=function(t){e.cellClickEventHandler()}},t.prototype.cellClickEventHandler=function(){this.paramGrid.selectedIndex>=0?(this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0):(this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1)},t.prototype.alertUser=function(e){if(this.rowIndex=e.rowIndex,this.dataField=e.dataField,this.oldVal=e.listData.oldValue,this.newVal=e.listData.newValue,"change"==this.methodName&&"name"==this.dataField&&this.oldVal&&this.parentGridData.length>0){r.c.yesLabel=r.Wb.getPredictMessage("alert.yes.label"),r.c.noLabel=r.Wb.getPredictMessage("alert.no.label");var t=r.Z.substitute(r.Wb.getPredictMessage("scenario.existingConfigAlert",null));this.swtalert.confirm(t,r.Wb.getPredictMessage("alert_header.confirm"),r.c.YES|r.c.NO,null,this.updateParam.bind(this))}},t.prototype.updateParam=function(e){e.detail==r.c.YES?this.nameChangedFlag=!0:(this.paramGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content=this.oldVal,this.paramGrid.dataProvider[this.rowIndex][this.dataField]=this.oldVal,this.paramGrid.refresh())},t.prototype.populateData=function(){this.convertXml();for(var e=this.paramsEventsFromParent.gridData.metadata,t=0;t<e.columns.column.length;t++)e.columns.column[t].editable=!0;this.paramGrid.CustomGrid(e),this.paramGrid.gridData={size:this.defParamsData.length,row:this.defParamsData},this.paramGrid.enableDisableCells=function(e){return!1},this.paramGrid.refresh()},t.prototype.convertXml=function(){if(this.gridParams){var e=d.xml2js(this.gridParams,{object:!1,reversible:!1,coerce:!1,sanitize:!0,trim:!0,arrayNotation:!1,alternateTextNode:!1,compact:!0}).requiredParameters.parameter;if(e){e.length||(e=[e]);for(var t=0;t<e.length;t++)this.defParamsData.push({name:{clickable:!1,content:e[t].name._cdata,negative:!1},description:{clickable:!1,content:e[t].description._cdata,negative:!1}})}}},t.prototype.enableRow=function(){var e=this;try{this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0;var t="add"==this.methodName&&-1!=this.enabledRow?this.enabledRow:this.paramGrid.gridData.length-1;if(this.paramGrid.gridData.length>0&&this.paramGrid.gridData[t]&&""==this.paramGrid.gridData[t].name)this.swtalert.error(r.Wb.getPredictMessage("scenario.alert.emptyRowAlreadyAdded",null));else{var a=this.paramsEventsFromParent.gridData.metadata;if(this.deletedRow=-1,this.enabledRow++,"add"==this.methodName)if(0==this.enabledRow&&0==this.paramGrid.gridData.length){for(var n=0;n<a.columns.column.length;n++)a.columns.column[n].editable=!0;this.paramGrid.CustomGrid(a),this.paramGrid.gridData={row:this.paramsEventsFromParent.gridData.rows.row,size:this.paramsEventsFromParent.gridData.rows.size},this.paramGrid.enableDisableCells=function(t,a){return e.enableDisableRow(t,a)},this.paramGrid.refresh(),this.paramGrid.selectedIndex=this.enabledRow}else{var i=void 0;i={name:{content:""},description:{content:""}},this.paramGrid.appendRow(i,!0),this.paramGrid.enableDisableCells=function(t,a){return e.enableDisableRow(t,a)},this.paramGrid.refresh(),this.paramGrid.selectedIndex=this.paramGrid.gridData.length-1,this.enabledRow=this.paramGrid.gridData.length-1}else if(this.paramGrid.gridData<1){for(var l=0;l<a.columns.column.length;l++)a.columns.column[l].editable=!0;this.paramGrid.CustomGrid(a),this.paramGrid.gridData={row:this.paramsEventsFromParent.gridData.rows.row,size:this.paramsEventsFromParent.gridData.rows.size},this.paramGrid.enableDisableCells=function(e,t){return 0==e.id},this.paramGrid.refresh(),this.paramGrid.selectedIndex=0}else{i=void 0;i={name:{content:""},description:{content:""}},this.paramGrid.appendRow(i,!0),this.paramGrid.selectedIndex=this.paramGrid.gridData.length-1,this.paramGrid.refresh(),this.paramGrid.enableDisableCells=function(t){return e.enableRowByIndex(t)}}}}catch(o){}},t.prototype.enableRowByIndex=function(e){this.deletedRow,this.paramGrid.gridData.length;return e.id==this.paramGrid.gridData.length-1},t.prototype.enableDisableRow=function(e,t){return e.id==this.enabledRow},t.prototype.deleteRow=function(){if(window.opener.instanceElement.schedGrid.gridData.length>0){r.c.yesLabel=r.Wb.getPredictMessage("alert.yes.label"),r.c.noLabel=r.Wb.getPredictMessage("alert.no.label");var e=r.Z.substitute(r.Wb.getPredictMessage("scenario.existingConfigAlert",null));this.swtalert.confirm(e,r.Wb.getPredictMessage("alert_header.confirm"),r.c.YES|r.c.NO,null,this.proceedWithDelete.bind(this))}else this.refreshParamGrid(!1)},t.prototype.proceedWithDelete=function(e){e.detail==r.c.YES&&this.refreshParamGrid(!0)},t.prototype.refreshParamGrid=function(e){try{this.rowDeletedFlag=e,this.deletedRow=this.paramGrid.selectedIndex,this.paramGrid.removeSelected(),this.paramGrid.enableDisableCells=function(e,t){return""==e.description&&""==e.name},this.paramGrid.refresh(),this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1}catch(t){console.log("DefineParams -> refreshParamGrid -> error",t)}},t.prototype.closePopup=function(){window.close()},t.prototype.gridParamsToXml=function(){if(this.paramGrid.gridData.length>0){this.xml="<requiredParameters>";for(var e=0;e<this.paramGrid.gridData.length;e++)this.paramGrid.gridData[e].name&&(this.sceduleGridData.push({parameter:{clickable:!1,content:this.paramGrid.gridData[e].name,negative:!1},value:{clickable:!1,content:"",negative:!1}}),this.xml+="<parameter>",this.xml+="<name><![CDATA["+this.paramGrid.gridData[e].name+"]]></name>",this.xml+="<description><![CDATA["+this.paramGrid.gridData[e].description+"]]></description>",this.xml+="</parameter>");this.xml+="</requiredParameters>"}this.xml=o.pd.xml(this.xml),window.opener&&window.opener.instanceElement&&(window.opener.instanceElement.xmlParams=this.xml,window.opener.instanceElement.scheduleGridData=this.sceduleGridData),(this.nameChangedFlag||this.rowDeletedFlag)&&(window.opener.instanceElement.schedGrid.gridData={size:0,row:[]},window.opener.instanceElement.schedGrid.refresh(),window.opener.instanceElement.oldSchedRows=[]),this.nameChangedFlag=!1,this.rowDeletedFlag=!1,window.close()},t.prototype.scanQueryForParams=function(){if(window.opener&&window.opener.instanceElement){var e=window.opener.instanceElement.parentDocument.identificationTab.baseQuery.text;if(e){this.paramsList=[];for(var t=/p{([^}]+)}/gi,a=void 0;a=t.exec(e);)-1==this.paramsList.indexOf(a[1])&&this.paramsList.push(a[1]);if(this.paramsList.length>0){this.swtalert.confirm("Scenario Query contain some parameters, do you want to override the actual Parameters Definition?",r.Wb.getPredictMessage("alert_header.confirm"),r.c.YES|r.c.NO,null,this.confirmListener.bind(this))}}}},t.prototype.confirmListener=function(e){var t=this;try{e.detail==r.c.YES&&(this.paramGrid.gridData={size:0,row:[]},setTimeout(function(){for(var e=0;e<t.paramsList.length;e++){var a;a={name:{content:t.paramsList[e]},description:{content:""}},t.paramGrid.appendRow(a,!0),t.enabledRow++}t.deletedRow=-1,t.paramGrid.selectedIndex=0,t.paramGrid.editable=!0,t.paramGrid.editedItemPosition={rowIndex:0,columnIndex:0},t.paramGrid.editedItemPosition={rowIndex:0,columnIndex:1},t.methodName="change"},300))}catch(a){console.log("\ud83d\ude80 ~ file: DefineParams.ts ~ line 333 ~ confirmListener ~ error",a)}},t}(r.yb),c=[{path:"",component:s}],u=(l.l.forChild(c),function(){return function(){}}()),h=a("pMnS"),b=a("RChO"),m=a("t6HQ"),p=a("WFGK"),g=a("5FqG"),w=a("Ip0R"),f=a("gIcY"),R=a("t/Na"),G=a("sE5F"),D=a("OzfB"),v=a("T7CS"),P=a("S7LP"),C=a("6aHO"),I=a("WzUx"),x=a("A7o+"),y=a("zCE2"),k=a("Jg5P"),B=a("3R0m"),T=a("hhbb"),L=a("5rxC"),E=a("Fzqc"),F=a("21Lb"),S=a("hUWP"),M=a("3pJQ"),_=a("V9q+"),W=a("VDKW"),J=a("kXfT"),z=a("BGbe");a.d(t,"DefineParamsModuleNgFactory",function(){return A}),a.d(t,"RenderType_DefineParams",function(){return O}),a.d(t,"View_DefineParams_0",function(){return V}),a.d(t,"View_DefineParams_Host_0",function(){return H}),a.d(t,"DefineParamsNgFactory",function(){return Z});var A=n.Gb(u,[],function(e){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[h.a,b.a,m.a,p.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,Z]],[3,n.n],n.J]),n.Rb(4608,w.m,w.l,[n.F,[2,w.u]]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.p,f.p,[]),n.Rb(4608,R.j,R.p,[w.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(e){return[e,new r.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,G.c,G.c,[]),n.Rb(4608,G.g,G.b,[]),n.Rb(5120,G.i,G.j,[]),n.Rb(4608,G.h,G.h,[G.c,G.g,G.i]),n.Rb(4608,G.f,G.a,[]),n.Rb(5120,G.d,G.k,[G.h,G.f]),n.Rb(5120,n.b,function(e,t){return[D.j(e,t)]},[w.c,n.O]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,P.a,P.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,P.a,n.g]),n.Rb(4608,I.c,I.c,[n.n,n.g,n.B]),n.Rb(4608,I.e,I.e,[I.c]),n.Rb(4608,x.l,x.l,[]),n.Rb(4608,x.h,x.g,[]),n.Rb(4608,x.c,x.f,[]),n.Rb(4608,x.j,x.d,[]),n.Rb(4608,x.b,x.a,[]),n.Rb(4608,x.k,x.k,[x.l,x.h,x.c,x.j,x.b,x.m,x.n]),n.Rb(4608,I.i,I.i,[[2,x.k]]),n.Rb(4608,I.r,I.r,[I.L,[2,x.k],I.i]),n.Rb(4608,I.t,I.t,[]),n.Rb(4608,I.w,I.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,w.b,w.b,[]),n.Rb(1073742336,f.n,f.n,[]),n.Rb(1073742336,f.l,f.l,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,x.i,x.i,[]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,G.e,G.e,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,D.c,D.c,[]),n.Rb(1073742336,E.a,E.a,[]),n.Rb(1073742336,F.d,F.d,[]),n.Rb(1073742336,S.c,S.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,_.a,_.a,[[2,D.g],n.O]),n.Rb(1073742336,W.b,W.b,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,z.b,z.b,[]),n.Rb(1073742336,r.Tb,r.Tb,[]),n.Rb(1073742336,u,u,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,x.m,void 0,[]),n.Rb(256,x.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:s}]]},[])])}),N=[[""]],O=n.Hb({encapsulation:0,styles:N,data:{}});function V(e){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{scenarioIdLbl:0}),n.Zb(402653184,3,{paramDesc:0}),n.Zb(402653184,4,{scenarioIdtxt:0}),n.Zb(402653184,5,{paramCanvas:0}),n.Zb(402653184,6,{okButton:0}),n.Zb(402653184,7,{cancelButton:0}),n.Zb(402653184,8,{addButton:0}),n.Zb(402653184,9,{deleteButton:0}),n.Zb(402653184,10,{scanQuery:0}),(e()(),n.Jb(10,0,null,null,43,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,a){var n=!0,i=e.component;"creationComplete"===t&&(n=!1!==i.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(11,4440064,null,0,r.yb,[n.r,r.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),n.Jb(12,0,null,0,41,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(13,4440064,null,0,r.ec,[n.r,r.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),n.Jb(14,0,null,0,23,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(15,4440064,null,0,r.db,[n.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(16,0,null,0,21,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(17,4440064,null,0,r.ec,[n.r,r.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(18,0,null,0,17,"Grid",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,g.Cc,g.H)),n.Ib(19,4440064,null,0,r.z,[n.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),n.Jb(20,0,null,0,9,"GridRow",[["paddingLeft","10"]],null,null,null,g.Bc,g.J)),n.Ib(21,4440064,null,0,r.B,[n.r,r.i],{paddingLeft:[0,"paddingLeft"]},null),(e()(),n.Jb(22,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),n.Ib(23,4440064,null,0,r.A,[n.r,r.i],{width:[0,"width"]},null),(e()(),n.Jb(24,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(25,4440064,[[2,4],["scenarioIdLbl",4]],0,r.vb,[n.r,r.i],null,null),(e()(),n.Jb(26,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),n.Ib(27,4440064,null,0,r.A,[n.r,r.i],null,null),(e()(),n.Jb(28,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,g.kd,g.sb)),n.Ib(29,4440064,[[4,4],["scenarioIdtxt",4]],0,r.Rb,[n.r,r.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(e()(),n.Jb(30,0,null,0,5,"GridRow",[["paddingLeft","10"]],null,null,null,g.Bc,g.J)),n.Ib(31,4440064,null,0,r.B,[n.r,r.i],{paddingLeft:[0,"paddingLeft"]},null),(e()(),n.Jb(32,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),n.Ib(33,4440064,null,0,r.A,[n.r,r.i],null,null),(e()(),n.Jb(34,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,g.Yc,g.fb)),n.Ib(35,4440064,[[3,4],["paramDesc",4]],0,r.vb,[n.r,r.i],{fontWeight:[0,"fontWeight"]},null),(e()(),n.Jb(36,0,null,0,1,"SwtCanvas",[["height","75%"],["id","paramCanvas"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(37,4440064,[[5,4],["paramCanvas",4]],0,r.db,[n.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),n.Jb(38,0,null,0,15,"SwtCanvas",[["height","10%"],["paddingTop","8"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(39,4440064,null,0,r.db,[n.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),n.Jb(40,0,null,0,5,"HBox",[["horizontalGap","5"],["paddingLeft","10"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(41,4440064,null,0,r.C,[n.r,r.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(e()(),n.Jb(42,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(e,t,a){var n=!0,i=e.component;"click"===t&&(n=!1!==i.gridParamsToXml()&&n);return n},g.Mc,g.T)),n.Ib(43,4440064,[[6,4],["okButton",4]],0,r.cb,[n.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(e()(),n.Jb(44,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(e,t,a){var n=!0,i=e.component;"click"===t&&(n=!1!==i.closePopup()&&n);return n},g.Mc,g.T)),n.Ib(45,4440064,[[7,4],["cancelButton",4]],0,r.cb,[n.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(e()(),n.Jb(46,0,null,0,7,"HBox",[["horizontalAlign","right"],["horizontalGap","5"],["paddingRight","10"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(47,4440064,null,0,r.C,[n.r,r.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"],paddingRight:[3,"paddingRight"]},null),(e()(),n.Jb(48,0,null,0,1,"SwtButton",[["enabled","true"],["label","Scan Query"]],null,[[null,"click"]],function(e,t,a){var n=!0,i=e.component;"click"===t&&(n=!1!==i.scanQueryForParams()&&n);return n},g.Mc,g.T)),n.Ib(49,4440064,[[10,4],["scanQuery",4]],0,r.cb,[n.r,r.i],{enabled:[0,"enabled"],label:[1,"label"]},{onClick_:"click"}),(e()(),n.Jb(50,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(e,t,a){var n=!0,i=e.component;"click"===t&&(n=!1!==i.enableRow()&&n);return n},g.Mc,g.T)),n.Ib(51,4440064,[[8,4],["addButton",4]],0,r.cb,[n.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(e()(),n.Jb(52,0,null,0,1,"SwtButton",[["enabled","false"]],null,[[null,"click"]],function(e,t,a){var n=!0,i=e.component;"click"===t&&(n=!1!==i.deleteRow()&&n);return n},g.Mc,g.T)),n.Ib(53,4440064,[[9,4],["deleteButton",4]],0,r.cb,[n.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"})],function(e,t){e(t,11,0,"100%","100%");e(t,13,0,"100%","100%","5","5","5","5");e(t,15,0,"100%","90%");e(t,17,0,"100%","100%");e(t,19,0,"100%","20%","10");e(t,21,0,"10");e(t,23,0,"25%"),e(t,25,0),e(t,27,0);e(t,29,0,"200","false");e(t,31,0,"10"),e(t,33,0);e(t,35,0,"normal");e(t,37,0,"paramCanvas","100%","75%");e(t,39,0,"100%","10%","8");e(t,41,0,"5","100%","10");e(t,43,0,"true");e(t,45,0,"true");e(t,47,0,"5","right","100%","10");e(t,49,0,"true","Scan Query");e(t,51,0,"true");e(t,53,0,"false")},null)}function H(e){return n.dc(0,[(e()(),n.Jb(0,0,null,null,1,"app-define-params",[],null,null,null,V,O)),n.Ib(1,4440064,null,0,s,[r.i,n.r],null,null)],function(e,t){e(t,1,0)},null)}var Z=n.Fb("app-define-params",s,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);