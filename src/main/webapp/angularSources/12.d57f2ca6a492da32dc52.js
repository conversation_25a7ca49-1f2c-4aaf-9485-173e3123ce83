(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{mIHf:function(t,e,n){"use strict";n.r(e);var i=n("CcnG"),r=n("mrSG"),o=n("447K"),c=n("ZYCi"),a=function(t){function e(e,n){var i=t.call(this,n,e)||this;return i.commonService=e,i.element=n,i.jsonReader=new o.L,i.inputData=new o.G(i.commonService),i.logicUpdate=new o.G(i.commonService),i.requestParams=[],i.baseURL=o.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.moduleName="Currency Maintenance",i.versionNumber="1.00.00",i.releaseDate="20 February 2019",i.menuAccess=2,i.helpURL=null,i.message=null,i.title=null,i.errorLocation=0,i.moduleId="",i.ccy=null,i.order=0,i.swtAlert=new o.bb(e),i}return r.d(e,t),e.prototype.ngOnInit=function(){this.addButton.label=o.Wb.getPredictMessage("button.add",null),this.changeButton.label=o.Wb.getPredictMessage("button.change",null),this.viewButton.label=o.Wb.getPredictMessage("button.view",null),this.deleteButton.label=o.Wb.getPredictMessage("button.delete",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.addButton.setFocus(),this.message=o.Wb.getPredictMessage("alert.delete",null)},e.prototype.onLoad=function(){var t=this;this.currencyGrid=this.canvasGrid.addChild(o.hb),this.currencyGrid.uniqueColumn="ccy",this.currencyGrid.onFilterChanged=this.disableButtons.bind(this),this.currencyGrid.onSortChanged=this.disableButtons.bind(this);try{this.title="Currency Maintenace",this.actionMethod="method=display",this.actionPath="currencyPCM.do?",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.currencyGrid.onRowClick=function(e){t.cellClickEventHandler(e)}}catch(e){console.log(e,this.moduleId,"CurrencyMaintenance","onLoad")}},e.prototype.inputDataResult=function(t){var e,n=null;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if(this.jsonReader.getRequestReplyStatus()){if(this.helpURL=this.jsonReader.getSingletons().helpurl,this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,!this.jsonReader.isDataBuilding()){this.menuAccess=Number(this.jsonReader.getScreenAttributes().menuaccess),this.enableAddButton(0==this.menuAccess),this.disableOrEnableButtons(!1),n=this.jsonReader.getColumnData();for(var i=0;i<n.column.length;i++)e=o.Wb.getAMLMessages(n.column[i].heading),n.column[i].heading=e;var r={columns:n};null!==this.currencyGrid&&void 0!==this.currencyGrid||(this.currencyGrid.componentID=this.jsonReader.getSingletons().screenid),this.currencyGrid.doubleClickEnabled=!0,this.currencyGrid.currencyFormat=this.currencyPattern,this.currencyGrid.CustomGrid(r),this.jsonReader.getGridData().size>0?(this.order=Number(this.jsonReader.getSingletons().maxOrdinal),this.listOrder=this.jsonReader.getSingletons().ordinaList,this.listOrder=this.listOrder.replace("[","").replace("]",""),this.listOrder=this.listOrder.split(",").map(Number),this.currencyGrid.dataProvider=null,this.currencyGrid.gridData=this.jsonReader.getGridData(),this.currencyGrid.setRowSize=this.jsonReader.getRowSize(),this.currencyGrid.doubleClickEnabled=!0):(this.currencyGrid.dataProvider=null,this.currencyGrid.selectedIndex=-1)}this.jsonReader.getRowSize()<1?this.enableReportButton(!1):this.enableReportButton(!0),this.prevRecievedJSON=this.lastRecievedJSON}else this.swtAlert.error(this.jsonReader.getRequestReplyMessage())}catch(c){o.Wb.logError(c,this.moduleId,"CurrencyMaintenance","inputDataResult",this.errorLocation)}},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(t){o.Wb.logError(t,this.moduleId,"CurrencyMaintenance","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){o.Wb.logError(t,this.moduleId,"CurrencyMaintenance","endOfComms",this.errorLocation)}},e.prototype.inputDataFault=function(t){try{var e=o.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(e)}catch(n){o.Wb.logError(n,this.moduleId,"CurrencyMaintenance","inputDataFault",this.errorLocation)}},e.prototype.logicUpdateResult=function(t){try{var e=t,n=new o.L;n.setInputJSON(e),n.getRequestReplyStatus()?(this.updateData(),this.disableOrEnableButtons(!1),this.addButton.setFocus()):this.swtAlert.error(n.getRequestReplyMessage())}catch(i){o.Wb.logError(i,this.moduleId,"CurrencyMaintenance","logicUpdateResult",this.errorLocation)}},e.prototype.updateData=function(){var t=this;try{this.currencyGrid.selectedIndex=-1,this.requestParams=[],this.actionPath="currencyPCM.do?",this.actionMethod="method=display",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.send(this.requestParams)}catch(e){o.Wb.logError(e,o.Wb.AML_MODULE_ID,"CurrencyMaintenance","updateData",this.errorLocation)}},e.prototype.refreshScreen=function(){try{this.currencyGrid.selectedIndex=-1,this.disableOrEnableButtons(!1),this.requestParams=[]}catch(t){o.Wb.logError(t,o.Wb.AML_MODULE_ID,"CurrencyMaintenance","updateData",this.errorLocation)}},e.prototype.moduleReadyEventHandler=function(t,e){var n=this;this.win.addChild(t.target),this.win.display(),this.win.onClose.subscribe(function(){console.log("res",n.win.getChild().result)},function(t){console.log(t)})},e.prototype.doAddCurrency=function(t){var e=this;try{this.currencyGrid.selectedIndex=-1,this.disableOrEnableButtons(!1),this.maxOrder=this.order+1,this.win=o.Eb.createPopUp(this),this.win.title="Add Currency - Payment Control",this.win.isModal=!0,this.win.maxOrder=this.maxOrder,this.win.listOrder=this.listOrder,this.win.width="450",this.win.height="280",this.win.id="currencyMaintenanceAdd",this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="add",this.win.maxOrder=this.maxOrder,this.win.listOrder=this.listOrder;var n=new o.T(this.commonService);n.addEventListener(o.S.READY,function(t){return e.moduleReadyEventHandler(t,"add")}),n.loadModule("currencyMaintenanceAdd")}catch(i){o.Wb.logError(i,this.moduleId,"CurrencyMaintenance","doAddCurrency",this.errorLocation)}},e.prototype.doChangeCurrency=function(t){var e=this;try{this.maxOrder=this.order+1,this.win=o.Eb.createPopUp(this),this.win.title="Change Currency - Payment Control",this.win.isModal=!0,this.win.maxOrder=this.maxOrder,this.win.listOrder=this.listOrder,this.win.width="450",this.win.height="280",this.win.id="currencyMaintenanceAdd",this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="change",this.win.ccy=this.currencyGrid.selectedItem.ccy.content,this.win.ccyName=this.currencyGrid.selectedItem.ccyName.content,this.win.maxOrder=this.maxOrder,this.win.listOrder=this.listOrder;var n=new o.T(this.commonService);n.addEventListener(o.S.READY,function(t){return e.moduleReadyEventHandler(t,"change")}),n.loadModule("currencyMaintenanceAdd")}catch(i){o.Wb.logError(i,this.moduleId,"CurrencyMaintenance","doChangeCurrency",this.errorLocation)}},e.prototype.doViewCurrency=function(t){var e=this;try{this.win=o.Eb.createPopUp(this),this.win.title="Details Currency - Payment Control",this.win.isModal=!0,this.win.maxOrder=this.maxOrder,this.win.listOrder=this.listOrder,this.win.width="450",this.win.height="280",this.win.id="currencyMaintenanceAdd",this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="view",this.win.ccy=this.currencyGrid.selectedItem.ccy.content,this.win.ccyName=this.currencyGrid.selectedItem.ccyName.content;var n=new o.T(this.commonService);n.addEventListener(o.S.READY,function(t){return e.moduleReadyEventHandler(t,"view")}),n.loadModule("currencyMaintenanceAdd")}catch(i){o.Wb.logError(i,this.moduleId,"CurrencyMaintenance","doViewCurrency",this.errorLocation)}},e.prototype.doDeleteCurrency=function(t){try{o.c.yesLabel="Yes",o.c.noLabel="No",this.swtAlert.confirm(this.message,"Alert",o.c.OK|o.c.CANCEL,null,this.deleteCurrency.bind(this))}catch(e){o.Wb.logError(e,this.moduleId,"CurrencyMaintenance","doDeleteCurrency",this.errorLocation)}},e.prototype.deleteCurrency=function(t){try{t.detail===o.c.OK&&(this.requestParams=[],this.actionMethod="method=remove",this.requestParams.ccy=this.ccy,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.currencyGrid.selectedIndex=-1,this.disableOrEnableButtons(!1))}catch(e){o.Wb.logError(e,this.moduleId,"CurrencyMaintenance","deleteCurrency",this.errorLocation)}},e.prototype.popupClosedEventHandler=function(t){try{this.currencyGrid.selectable=!0,this.currencyGrid.doubleClickEnabled=!0,1===this.currencyGrid.selectedIndices.length&&this.currencyGrid.selectable&&this.disableOrEnableButtons(!0)}catch(e){o.Wb.logError(e,this.moduleId,"CurrencyMaintenance","popupClosedEventHandler",this.errorLocation)}},e.prototype.doHelp=function(){try{o.x.call("help")}catch(t){o.Wb.logError(t,this.moduleId,"CurrencyMaintenance","doHelp",this.errorLocation)}},e.prototype.cellClickEventHandler=function(t){try{this.currencyGrid.selectedIndex>=0&&this.currencyGrid.selectable?(this.ccy=this.currencyGrid.selectedItem.ccy.content,this.disableOrEnableButtons(!0),t.stopPropagation(),this.addButton.setFocus()):this.disableOrEnableButtons(!1)}catch(e){o.Wb.logError(e,this.moduleId,"CurrencyMaintenance","cellClickEventHandler",this.errorLocation)}},e.prototype.report=function(t){var e=null,n="";try{e=""!==this.currencyGrid.filteredGridColumns?this.currencyGrid.getFilteredGridColumns():"",n=this.currencyGrid.getSortedGridColumn(),this.actionPath="currencyPCM.do?",this.actionMethod="method=exportCurrency",this.requestParams.type=t,this.requestParams.selectedFilter=e,this.requestParams.selectedSort=n,this.requestParams.print="ALL",this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod}catch(i){console.log(i,this.moduleId,"CurrencyMaintenance","report")}},e.prototype.keyDownEventHandler=function(t){try{var e=Object(o.ic.getFocus()).name;t.keyCode===o.N.ENTER&&("addButton"===e?this.doAddCurrency(t):"changeButton"===e?this.doChangeCurrency(t):"viewButton"===e?this.doViewCurrency(t):"closeButton"===e?this.closeCurrentTab(t):"csv"===e?this.report("csv"):"excel"===e?this.report("xls"):"pdf"===e?this.report("pdf"):"helpIcon"===e?this.doHelp():"deleteButton"===e?this.doDeleteCurrency(t):"cancelButton"===e?this.reset(t):"printButton"===e&&this.printPage())}catch(n){o.Wb.logError(n,this.moduleId,"CurrencyMaintenance","keyDownEventHandler",this.errorLocation)}},e.prototype.reset=function(t){try{this.actionMethod="method=display",this.actionPath="currencyPCM.do?",this.requestParams=[],this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=this.inputDataResult,this.inputData.send(this.requestParams)}catch(e){o.Wb.logError(e,this.moduleId,"CurrencyMaintenance","reset",this.errorLocation)}},e.prototype.closeCurrentTab=function(t){try{this.dispose()}catch(e){o.Wb.logError(e,o.Wb.SYSTEM_MODULE_ID,"CurrencyMaintenance","closeCurrentTab",this.errorLocation)}},e.prototype.dispose=function(){try{this.currencyGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccess=null,this.lastRecievedJSON=null,this.prevRecievedJSON=null,this.child=null,o.x.call("close")}catch(t){o.Wb.logError(t,this.moduleId,"CurrencyMaintenance","dispose",this.errorLocation)}},e.prototype.disableOrEnableButtons=function(t){t?(this.enableChangeButton(0==this.menuAccess),this.enableViewButton(this.menuAccess<2),this.enableDeleteButton(0==this.menuAccess)):(this.enableChangeButton(!1),this.enableViewButton(!1),this.enableDeleteButton(!1))},e.prototype.disableButtons=function(){-1==this.currencyGrid.selectedIndex?this.disableOrEnableButtons(!1):this.disableOrEnableButtons(!0)},e.prototype.enableReportButton=function(t){this.csv.enabled=t,this.csv.buttonMode=t,this.excel.enabled=t,this.excel.buttonMode=t,this.pdf.enabled=t,this.pdf.buttonMode=t,this.settingButton.enabled=t,this.settingButton.buttonMode=t,this.enablePrintButton(t)},e.prototype.printPage=function(){try{this.actionPath="currencyPCM.do?",this.actionMethod="type=pdf",this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&currentModuleId="+this.moduleId,this.actionMethod=this.actionMethod+"&print=PAGE",o.x.call("getReports",this.actionPath+this.actionMethod)}catch(t){o.Wb.logError(t,this.moduleId,"CurrencyMaintenance","printPage",this.errorLocation)}},e.prototype.enableAddButton=function(t){this.addButton.enabled=t,this.addButton.buttonMode=t},e.prototype.enableChangeButton=function(t){this.changeButton.enabled=t,this.changeButton.buttonMode=t},e.prototype.enableViewButton=function(t){this.viewButton.enabled=t,this.viewButton.buttonMode=t},e.prototype.enableDeleteButton=function(t){this.deleteButton.enabled=t,this.deleteButton.buttonMode=t},e.prototype.enablePrintButton=function(t){this.printButton.enabled=t,this.printButton.buttonMode=t},e}(o.yb),s=[{path:"",component:a}],l=(c.l.forChild(s),function(){return function(){}}()),d=n("pMnS"),u=n("RChO"),h=n("t6HQ"),b=n("WFGK"),p=n("5FqG"),y=n("Ip0R"),g=n("gIcY"),m=n("t/Na"),w=n("sE5F"),R=n("OzfB"),C=n("T7CS"),f=n("S7LP"),M=n("6aHO"),v=n("WzUx"),B=n("A7o+"),D=n("zCE2"),k=n("Jg5P"),O=n("3R0m"),E=n("hhbb"),I=n("5rxC"),L=n("Fzqc"),S=n("21Lb"),G=n("hUWP"),P=n("3pJQ"),x=n("V9q+"),A=n("VDKW"),_=n("kXfT"),W=n("BGbe");n.d(e,"CurrencyMaintenanceModuleNgFactory",function(){return N}),n.d(e,"RenderType_CurrencyMaintenance",function(){return H}),n.d(e,"View_CurrencyMaintenance_0",function(){return J}),n.d(e,"View_CurrencyMaintenance_Host_0",function(){return F}),n.d(e,"CurrencyMaintenanceNgFactory",function(){return q});var N=i.Gb(l,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[d.a,u.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,q]],[3,i.n],i.J]),i.Rb(4608,y.m,y.l,[i.F,[2,y.u]]),i.Rb(4608,g.c,g.c,[]),i.Rb(4608,g.p,g.p,[]),i.Rb(4608,m.j,m.p,[y.c,i.O,m.n]),i.Rb(4608,m.q,m.q,[m.j,m.o]),i.Rb(5120,m.a,function(t){return[t,new o.tb]},[m.q]),i.Rb(4608,m.m,m.m,[]),i.Rb(6144,m.k,null,[m.m]),i.Rb(4608,m.i,m.i,[m.k]),i.Rb(6144,m.b,null,[m.i]),i.Rb(4608,m.f,m.l,[m.b,i.B]),i.Rb(4608,m.c,m.c,[m.f]),i.Rb(4608,w.c,w.c,[]),i.Rb(4608,w.g,w.b,[]),i.Rb(5120,w.i,w.j,[]),i.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),i.Rb(4608,w.f,w.a,[]),i.Rb(5120,w.d,w.k,[w.h,w.f]),i.Rb(5120,i.b,function(t,e){return[R.j(t,e)]},[y.c,i.O]),i.Rb(4608,C.a,C.a,[]),i.Rb(4608,f.a,f.a,[]),i.Rb(4608,M.a,M.a,[i.n,i.L,i.B,f.a,i.g]),i.Rb(4608,v.c,v.c,[i.n,i.g,i.B]),i.Rb(4608,v.e,v.e,[v.c]),i.Rb(4608,B.l,B.l,[]),i.Rb(4608,B.h,B.g,[]),i.Rb(4608,B.c,B.f,[]),i.Rb(4608,B.j,B.d,[]),i.Rb(4608,B.b,B.a,[]),i.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),i.Rb(4608,v.i,v.i,[[2,B.k]]),i.Rb(4608,v.r,v.r,[v.L,[2,B.k],v.i]),i.Rb(4608,v.t,v.t,[]),i.Rb(4608,v.w,v.w,[]),i.Rb(1073742336,c.l,c.l,[[2,c.r],[2,c.k]]),i.Rb(1073742336,y.b,y.b,[]),i.Rb(1073742336,g.n,g.n,[]),i.Rb(1073742336,g.l,g.l,[]),i.Rb(1073742336,D.a,D.a,[]),i.Rb(1073742336,k.a,k.a,[]),i.Rb(1073742336,g.e,g.e,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,B.i,B.i,[]),i.Rb(1073742336,v.b,v.b,[]),i.Rb(1073742336,m.e,m.e,[]),i.Rb(1073742336,m.d,m.d,[]),i.Rb(1073742336,w.e,w.e,[]),i.Rb(1073742336,E.b,E.b,[]),i.Rb(1073742336,I.b,I.b,[]),i.Rb(1073742336,R.c,R.c,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,S.d,S.d,[]),i.Rb(1073742336,G.c,G.c,[]),i.Rb(1073742336,P.a,P.a,[]),i.Rb(1073742336,x.a,x.a,[[2,R.g],i.O]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,_.a,_.a,[]),i.Rb(1073742336,W.b,W.b,[]),i.Rb(1073742336,o.Tb,o.Tb,[]),i.Rb(1073742336,l,l,[]),i.Rb(256,m.n,"XSRF-TOKEN",[]),i.Rb(256,m.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,B.m,void 0,[]),i.Rb(256,B.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,c.i,function(){return[[{path:"",component:a}]]},[])])}),T=[[""]],H=i.Hb({encapsulation:0,styles:T,data:{}});function J(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{canvasGrid:0}),i.Zb(402653184,3,{loadingImage:0}),i.Zb(402653184,4,{entityLabel:0}),i.Zb(402653184,5,{addButton:0}),i.Zb(402653184,6,{changeButton:0}),i.Zb(402653184,7,{viewButton:0}),i.Zb(402653184,8,{exportButton:0}),i.Zb(402653184,9,{printButton:0}),i.Zb(402653184,10,{deleteButton:0}),i.Zb(402653184,11,{closeButton:0}),i.Zb(402653184,12,{settingButton:0}),i.Zb(402653184,13,{csv:0}),i.Zb(402653184,14,{excel:0}),i.Zb(402653184,15,{pdf:0}),i.Zb(402653184,16,{helpIcon:0}),(t()(),i.Jb(16,0,null,null,27,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var i=!0,r=t.component;"creationComplete"===e&&(i=!1!==r.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(17,4440064,null,0,o.yb,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(18,0,null,0,25,"VBox",[["height","100%"],["paddingBottom","10"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","10"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(19,4440064,null,0,o.ec,[i.r,o.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),i.Jb(20,0,null,0,1,"SwtCanvas",[["height","90%"],["id","canvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(21,4440064,[[2,4],["canvasGrid",4]],0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(22,0,null,0,21,"SwtCanvas",[["id","canvasButtons"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(23,4440064,null,0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),i.Jb(24,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(25,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"]},null),(t()(),i.Jb(26,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(27,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(28,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.doAddCurrency(n)&&i);"keyDown"===e&&(i=!1!==r.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(29,4440064,[[5,4],["addButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),i.Jb(30,0,null,0,1,"SwtButton",[["id","changeButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.doChangeCurrency(n)&&i);"keyDown"===e&&(i=!1!==r.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(31,4440064,[[6,4],["changeButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),i.Jb(32,0,null,0,1,"SwtButton",[["id","deleteButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.doDeleteCurrency(n)&&i);"keyDown"===e&&(i=!1!==r.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(33,4440064,[[10,4],["deleteButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),i.Jb(34,0,null,0,1,"SwtButton",[["id","viewButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.doViewCurrency(n)&&i);"keyDown"===e&&(i=!1!==r.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(35,4440064,[[7,4],["viewButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),i.Jb(36,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.closeCurrentTab(n)&&i);"keyDown"===e&&(i=!1!==r.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(37,4440064,[[11,4],["closeButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),i.Jb(38,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","5"]],null,null,null,p.Dc,p.K)),i.Ib(39,4440064,null,0,o.C,[i.r,o.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),i.Jb(40,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),i.Ib(41,114688,[[3,4],["loadingImage",4]],0,o.xb,[i.r],null,null),(t()(),i.Jb(42,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var i=!0,r=t.component;"click"===e&&(i=!1!==r.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(43,4440064,null,0,o.rb,[i.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){t(e,17,0,"100%","100%");t(e,19,0,"100%","100%","10","10","10","10");t(e,21,0,"canvasGrid","100%","90%");t(e,23,0,"canvasButtons","100%");t(e,25,0,"100%");t(e,27,0,"100%","5");t(e,29,0,"addButton",!0);t(e,31,0,"changeButton",!0);t(e,33,0,"deleteButton",!0);t(e,35,0,"viewButton",!0);t(e,37,0,"closeButton",!0);t(e,39,0,"right","5"),t(e,41,0);t(e,43,0,"helpIcon","true",!0,"groups-of-rules")},null)}function F(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-pccurrency-maintenance",[],null,null,null,J,H)),i.Ib(1,4440064,null,0,a,[o.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var q=i.Fb("app-pccurrency-maintenance",a,F,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);