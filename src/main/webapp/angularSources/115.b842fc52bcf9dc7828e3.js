(window.webpackJsonp=window.webpackJsonp||[]).push([[115],{LZZQ:function(e,l,n){"use strict";n.r(l);var t=n("CcnG"),o=n("mrSG"),i=n("447K"),r=n("ZYCi"),a=function(e){function l(l,n){var t=e.call(this,n,l)||this;return t.commonService=l,t.element=n,t.saveProfileCollection=[],t.selectedProfileItem="",t.temporarySavedProfile=null,t.swtAlert=new i.bb(l),t}return o.d(l,e),l.prototype.ngOnInit=function(){this.saveButton.label=i.x.call("getBundle","text","button-saveProfile","Save"),this.saveButton.toolTip=i.x.call("getBundle","text","button-saveProfile","Save"),this.cancelButton.label=i.x.call("getBundle","text","button-close","Cancel"),this.cancelButton.toolTip=i.x.call("getBundle","tip","button-close","Cancel changes"),this.profileNameLabel.text=i.Wb.getPredictMessage("userprofile.name")},l.prototype.onLoad=function(){this.profileCombo.dataProvider=this.saveProfileCollection,this.profileCombo.selectedLabel=this.selectedProfileItem},l.prototype.saveProfileClickHandlerFromIframe=function(){var e=this.profileCombo.selectedLabel;this.temporarySavedProfile=e,""==e?this.swtAlert.show(i.x.call("getBundle","text","alert-fillMandatoryFields","Please fill the profile name before saving")):-1!=this.msdAddColsCombo.getIndexOf(e)||-1!=this.msdAddColsCombo.getIndexOf("*"+e)?this.swtAlert.show(i.x.call("getBundle","text","alert-overwriteProfile","Are you sure you want to overwrite this profile?"),i.x.call("getBundle","text","alert-warning","Warning"),i.c.OK|i.c.CANCEL,null,this.overwriteAlertListenerIFrame.bind(this),i.c.CANCEL):(this.parentDocument.saveNewProfile(e),this.popupClosed())},l.prototype.keyDownEventHandler=function(e){try{var l=Object(i.ic.getFocus()).name;e.keyCode==i.N.ENTER&&("saveButton"==l?this.saveProfileClickHandlerFromIframe():"cancelButton"==l?this.popupClosed():"helpIcon"===l&&this.doHelp())}catch(n){console.log(n,"Predict","CurrencyMaintenance","keyDownEventHandler")}},l.prototype.doHelp=function(){try{i.x.call("help")}catch(e){i.Wb.logError(e,"Predict","CurrencyMaintenance","doHelp",0)}},l.prototype.popupClosed=function(){this.dispose()},l.prototype.dispose=function(){try{this.titleWindow?this.close():window.close()}catch(e){console.log(e,"Predict","CurrencyMaintenance","dispose")}},l.prototype.overwriteAlertListenerIFrame=function(e){e.detail==i.c.OK&&(this.parentDocument.saveProfile(this.temporarySavedProfile),this.popupClosed()),this.temporarySavedProfile=null},l}(i.yb),u=[{path:"",component:a}],c=(r.l.forChild(u),function(){return function(){}}()),b=n("pMnS"),d=n("RChO"),h=n("t6HQ"),p=n("WFGK"),s=n("5FqG"),g=n("Ip0R"),m=n("gIcY"),f=n("t/Na"),R=n("sE5F"),w=n("OzfB"),v=n("T7CS"),C=n("S7LP"),y=n("6aHO"),k=n("WzUx"),B=n("A7o+"),I=n("zCE2"),P=n("Jg5P"),x=n("3R0m"),S=n("hhbb"),L=n("5rxC"),_=n("Fzqc"),D=n("21Lb"),M=n("hUWP"),T=n("3pJQ"),A=n("V9q+"),H=n("VDKW"),J=n("kXfT"),F=n("BGbe");n.d(l,"SaveMsdProfilePopupModuleNgFactory",function(){return N}),n.d(l,"RenderType_SaveMsdProfilePopup",function(){return E}),n.d(l,"View_SaveMsdProfilePopup_0",function(){return G}),n.d(l,"View_SaveMsdProfilePopup_Host_0",function(){return z}),n.d(l,"SaveMsdProfilePopupNgFactory",function(){return K});var N=t.Gb(c,[],function(e){return t.Qb([t.Rb(512,t.n,t.vb,[[8,[b.a,d.a,h.a,p.a,s.Cb,s.Pb,s.r,s.rc,s.s,s.Ab,s.Bb,s.Db,s.qd,s.Hb,s.k,s.Ib,s.Nb,s.Ub,s.yb,s.Jb,s.v,s.A,s.e,s.c,s.g,s.d,s.Kb,s.f,s.ec,s.Wb,s.bc,s.ac,s.sc,s.fc,s.lc,s.jc,s.Eb,s.Fb,s.mc,s.Lb,s.nc,s.Mb,s.dc,s.Rb,s.b,s.ic,s.Yb,s.Sb,s.kc,s.y,s.Qb,s.cc,s.hc,s.pc,s.oc,s.xb,s.p,s.q,s.o,s.h,s.j,s.w,s.Zb,s.i,s.m,s.Vb,s.Ob,s.Gb,s.Xb,s.t,s.tc,s.zb,s.n,s.qc,s.a,s.z,s.rd,s.sd,s.x,s.td,s.gc,s.l,s.u,s.ud,s.Tb,K]],[3,t.n],t.J]),t.Rb(4608,g.m,g.l,[t.F,[2,g.u]]),t.Rb(4608,m.c,m.c,[]),t.Rb(4608,m.p,m.p,[]),t.Rb(4608,f.j,f.p,[g.c,t.O,f.n]),t.Rb(4608,f.q,f.q,[f.j,f.o]),t.Rb(5120,f.a,function(e){return[e,new i.tb]},[f.q]),t.Rb(4608,f.m,f.m,[]),t.Rb(6144,f.k,null,[f.m]),t.Rb(4608,f.i,f.i,[f.k]),t.Rb(6144,f.b,null,[f.i]),t.Rb(4608,f.f,f.l,[f.b,t.B]),t.Rb(4608,f.c,f.c,[f.f]),t.Rb(4608,R.c,R.c,[]),t.Rb(4608,R.g,R.b,[]),t.Rb(5120,R.i,R.j,[]),t.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),t.Rb(4608,R.f,R.a,[]),t.Rb(5120,R.d,R.k,[R.h,R.f]),t.Rb(5120,t.b,function(e,l){return[w.j(e,l)]},[g.c,t.O]),t.Rb(4608,v.a,v.a,[]),t.Rb(4608,C.a,C.a,[]),t.Rb(4608,y.a,y.a,[t.n,t.L,t.B,C.a,t.g]),t.Rb(4608,k.c,k.c,[t.n,t.g,t.B]),t.Rb(4608,k.e,k.e,[k.c]),t.Rb(4608,B.l,B.l,[]),t.Rb(4608,B.h,B.g,[]),t.Rb(4608,B.c,B.f,[]),t.Rb(4608,B.j,B.d,[]),t.Rb(4608,B.b,B.a,[]),t.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),t.Rb(4608,k.i,k.i,[[2,B.k]]),t.Rb(4608,k.r,k.r,[k.L,[2,B.k],k.i]),t.Rb(4608,k.t,k.t,[]),t.Rb(4608,k.w,k.w,[]),t.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),t.Rb(1073742336,g.b,g.b,[]),t.Rb(1073742336,m.n,m.n,[]),t.Rb(1073742336,m.l,m.l,[]),t.Rb(1073742336,I.a,I.a,[]),t.Rb(1073742336,P.a,P.a,[]),t.Rb(1073742336,m.e,m.e,[]),t.Rb(1073742336,x.a,x.a,[]),t.Rb(1073742336,B.i,B.i,[]),t.Rb(1073742336,k.b,k.b,[]),t.Rb(1073742336,f.e,f.e,[]),t.Rb(1073742336,f.d,f.d,[]),t.Rb(1073742336,R.e,R.e,[]),t.Rb(1073742336,S.b,S.b,[]),t.Rb(1073742336,L.b,L.b,[]),t.Rb(1073742336,w.c,w.c,[]),t.Rb(1073742336,_.a,_.a,[]),t.Rb(1073742336,D.d,D.d,[]),t.Rb(1073742336,M.c,M.c,[]),t.Rb(1073742336,T.a,T.a,[]),t.Rb(1073742336,A.a,A.a,[[2,w.g],t.O]),t.Rb(1073742336,H.b,H.b,[]),t.Rb(1073742336,J.a,J.a,[]),t.Rb(1073742336,F.b,F.b,[]),t.Rb(1073742336,i.Tb,i.Tb,[]),t.Rb(1073742336,c,c,[]),t.Rb(256,f.n,"XSRF-TOKEN",[]),t.Rb(256,f.o,"X-XSRF-TOKEN",[]),t.Rb(256,"config",{},[]),t.Rb(256,B.m,void 0,[]),t.Rb(256,B.n,void 0,[]),t.Rb(256,"popperDefaults",{},[]),t.Rb(1024,r.i,function(){return[[{path:"",component:a}]]},[]),t.Rb(256,"saveMsdProfilePopup",a,[])])}),O=[[""]],E=t.Hb({encapsulation:0,styles:O,data:{}});function G(e){return t.dc(0,[t.Zb(402653184,1,{_container:0}),t.Zb(402653184,2,{profileCombo:0}),t.Zb(402653184,3,{saveButton:0}),t.Zb(402653184,4,{cancelButton:0}),t.Zb(402653184,5,{profileNameLabel:0}),(e()(),t.Jb(5,0,null,null,31,"SwtModule",[["height","100%"],["width","100%"]],[[8,"title",0]],[[null,"close"],[null,"creationComplete"]],function(e,l,n){var t=!0,o=e.component;"close"===l&&(t=!1!==o.popupClosed()&&t);"creationComplete"===l&&(t=!1!==o.onLoad()&&t);return t},s.ad,s.hb)),t.Ib(6,4440064,null,0,i.yb,[t.r,i.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),t.Jb(7,0,null,0,29,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,s.od,s.vb)),t.Ib(8,4440064,null,0,i.ec,[t.r,i.i,t.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(e()(),t.Jb(9,0,null,0,13,"SwtCanvas",[["height","60%"],["width","100%"]],null,null,null,s.Nc,s.U)),t.Ib(10,4440064,null,0,i.db,[t.r,i.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),t.Jb(11,0,null,0,11,"Grid",[["height","100%"],["width","100%"]],null,null,null,s.Cc,s.H)),t.Ib(12,4440064,null,0,i.z,[t.r,i.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),t.Jb(13,0,null,0,9,"GridRow",[],null,null,null,s.Bc,s.J)),t.Ib(14,4440064,null,0,i.B,[t.r,i.i],null,null),(e()(),t.Jb(15,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,s.Ac,s.I)),t.Ib(16,4440064,null,0,i.A,[t.r,i.i],{width:[0,"width"]},null),(e()(),t.Jb(17,0,null,0,1,"SwtLabel",[],null,null,null,s.Yc,s.fb)),t.Ib(18,4440064,[[5,4],["profileNameLabel",4],["profileName",4]],0,i.vb,[t.r,i.i],null,null),(e()(),t.Jb(19,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,s.Ac,s.I)),t.Ib(20,4440064,null,0,i.A,[t.r,i.i],{width:[0,"width"]},null),(e()(),t.Jb(21,0,null,0,1,"SwtEditableComboBox",[],null,null,null,s.Uc,s.bb)),t.Ib(22,4440064,[[2,4],["profileCombo",4]],0,i.mb,[t.r,i.i],null,null),(e()(),t.Jb(23,0,null,0,13,"SwtCanvas",[["height","40%"],["id","canvasButtons"],["width","100%"]],null,null,null,s.Nc,s.U)),t.Ib(24,4440064,null,0,i.db,[t.r,i.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),t.Jb(25,0,null,0,11,"HBox",[["height","100%"],["width","100%"]],null,null,null,s.Dc,s.K)),t.Ib(26,4440064,null,0,i.C,[t.r,i.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),t.Jb(27,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,s.Dc,s.K)),t.Ib(28,4440064,null,0,i.C,[t.r,i.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(e()(),t.Jb(29,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,l,n){var t=!0,o=e.component;"click"===l&&(t=!1!==o.saveProfileClickHandlerFromIframe()&&t);"keyDown"===l&&(t=!1!==o.keyDownEventHandler(n)&&t);return t},s.Mc,s.T)),t.Ib(30,4440064,[[3,4],["saveButton",4]],0,i.cb,[t.r,i.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),t.Jb(31,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,l,n){var t=!0,o=e.component;"click"===l&&(t=!1!==o.popupClosed()&&t);"keyDown"===l&&(t=!1!==o.keyDownEventHandler(n)&&t);return t},s.Mc,s.T)),t.Ib(32,4440064,[[4,4],["cancelButton",4]],0,i.cb,[t.r,i.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),t.Jb(33,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","5"]],null,null,null,s.Dc,s.K)),t.Ib(34,4440064,null,0,i.C,[t.r,i.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(e()(),t.Jb(35,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpIcon"]],null,[[null,"click"]],function(e,l,n){var t=!0,o=e.component;"click"===l&&(t=!1!==o.doHelp()&&t);return t},s.Wc,s.db)),t.Ib(36,4440064,null,0,i.rb,[t.r,i.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"})],function(e,l){e(l,6,0,"100%","100%");e(l,8,0,"vBox1","100%","100%","5","5","5","5");e(l,10,0,"100%","60%");e(l,12,0,"100%","100%"),e(l,14,0);e(l,16,0,"40%"),e(l,18,0);e(l,20,0,"60%"),e(l,22,0);e(l,24,0,"canvasButtons","100%","40%");e(l,26,0,"100%","100%");e(l,28,0,"100%","5");e(l,30,0,"saveButton");e(l,32,0,"cancelButton","true");e(l,34,0,"right","5");e(l,36,0,"helpIcon","true",!0)},function(e,l){var n=l.component;e(l,5,0,t.Lb(1,"",n.title,""))})}function z(e){return t.dc(0,[(e()(),t.Jb(0,0,null,null,1,"SaveMsdProfilePopup",[],null,null,null,G,E)),t.Ib(1,4440064,null,0,a,[i.i,t.r],null,null)],function(e,l){e(l,1,0)},null)}var K=t.Fb("SaveMsdProfilePopup",a,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);