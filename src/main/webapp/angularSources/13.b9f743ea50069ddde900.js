(window.webpackJsonp=window.webpackJsonp||[]).push([[13],{nR6S:function(t,e,n){"use strict";n.r(e);var l=n("CcnG"),i=n("mrSG"),r=n("447K"),o=n("ZYCi"),u=function(t){function e(e,n){var l=t.call(this,n,e)||this;return l.commonService=e,l.element=n,l.jsonReader=new r.L,l.inputData=new r.G(l.commonService),l.logicUpdate=new r.G(l.commonService),l.baseURL=r.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.message=null,l.screenName=null,l.helpURL=null,l.title=null,l.searchQuery="",l.queryToDisplay="",l.errorLocation=0,l.ccy="",l.ccyName="",l.moduleName="PC Currency Maintenance Details",l.versionNumber="1.00.00",l.releaseDate="20 Feb 2019",l.moduleId="",l.ordinal=null,l.reOrder=!1,l.swtAlert=new r.bb(e),l}return i.d(e,t),e.prototype.ngOnInit=function(){try{this.currencyComboBox.toolTip=r.Wb.getPredictMessage("currencyMaintenanceDetails.tooltip.ccy",null),this.currencyLabel.text=r.Wb.getPredictMessage("currencyMaintenanceDetails.label.ccy",null),this.currencyNameLabel.text=r.Wb.getPredictMessage("currencyMaintenanceDetails.label.name",null),this.ordinalNumInput.toolTip=r.Wb.getPredictMessage("currencyMaintenanceDetails.tooltip.ordinal",null),this.ordinalLabel.text=r.Wb.getPredictMessage("currencyMaintenanceDetails.label.ordinal",null),this.multiplierLabel.text=r.Wb.getPredictMessage("currencyMaintenanceDetails.label.multiplier",null),this.multiplierComboBox.toolTip=r.Wb.getPredictMessage("currencyMaintenanceDetails.tooltip.multiplier",null),this.largeAmtLabel.text=r.Wb.getPredictMessage("currencyMaintenanceDetails.label.largeAmount",null),this.largeAmtTextInput.toolTip=r.Wb.getPredictMessage("currencyMaintenanceDetails.tooltip.largeAmount",null),this.saveButton.label=r.Wb.getPredictMessage("button.save",null),this.cancelButton.label=r.Wb.getPredictMessage("button.cancel",null),this.currencyNameTxtInput.enabled=!1,this.currencyComboBox.enabled=!1,this.saveButton.visible=!0,"add"==this.screenName?(this.currencyComboBox.enabled=!0,this.currencyNameTxtInput.setFocus(),this.multiplierComboBox.enabled=!0,this.ordinalNumInput.enabled=!0,this.ordinalNumInput.text=this.maxOrder,this.saveButton.enabled=!1,this.saveButton.buttonMode=!1):"change"==this.screenName?(this.multiplierComboBox.enabled=!0,this.ordinalNumInput.enabled=!0):"view"==this.screenName&&(this.multiplierComboBox.enabled=!1,this.ordinalNumInput.enabled=!1,this.largeAmtTextInput.enabled=!1,this.saveButton.visible=!1,this.saveButton.includeInLayout=!1)}catch(t){r.Wb.logError(t,this.moduleId,"CurrencyMaintenance","ngOnInit",this.errorLocation)}},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.actionPath="currencyPCM.do?","add"!=this.screenName?(this.actionMethod="method=view",this.requestParams.ccy=this.ccy,this.requestParams.ccyName=this.ccyName):this.actionMethod="method=add",this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){r.Wb.logError(e,this.moduleId,"CurrencyMaintenance","onLoad",this.errorLocation)}},e.prototype.inputDataResult=function(t){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&(this.jsonReader.getRequestReplyStatus()?this.jsonReader.isDataBuilding()||(this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,this.currencyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(),!1,""),this.multiplierComboBox.setComboData(this.jsonReader.getSelects(),!0),"add"!==this.screenName&&(this.ordinal=Number(this.jsonReader.getSingletons().ordinal),this.currencyComboBox.selectedLabel=this.jsonReader.getSingletons().ccy,this.currencyComboBox.selectedItem.content=this.jsonReader.getSingletons().ccy,this.currencyNameTxtInput.text=String(this.jsonReader.getSingletons().ccyName),"change"===this.screenName?""==String(this.jsonReader.getSingletons().ordinal)?this.ordinalNumInput.text=this.maxOrder:this.ordinalNumInput.text=Number(this.jsonReader.getSingletons().ordinal):this.ordinalNumInput.text=this.jsonReader.getSingletons().ordinal,this.multiplierComboBox.selectedLabel=this.jsonReader.getSingletons().multiplier,""!=String(this.jsonReader.getSingletons().largeAmountThr)&&(this.largeAmtTextInput.text=this.jsonReader.getSingletons().largeAmountThr))):this.swtAlert.error(this.jsonReader.getRequestReplyMessage())))}catch(e){r.Wb.logError(e,this.moduleId,"CurrencyMaintenance","inputDataResult",this.errorLocation)}},e.prototype.save=function(){var t=this;this.requestParams=[];try{if(!this.currencyComboBox.selectedItem)return void this.swtAlert.error(r.Wb.getPredictMessage("alert.mandatoryField",null));if(!validateCurrencyPlaces(this.largeAmtTextInput,this.currencyPattern,this.currencyComboBox.selectedLabel))return void this.swtAlert.warning(r.Wb.getPredictMessage("alert.validAmount",null));"add"==this.screenName?this.actionMethod="method=save":this.actionMethod="method=update",this.logicUpdate.cbResult=function(e){t.logicUpdateResult(e)},this.requestParams.ordinal=this.ordinalNumInput.text,this.requestParams.multiplier=this.multiplierComboBox.selectedItem.value,this.requestParams.ccy=this.currencyComboBox.selectedItem.content,this.requestParams.largeAmountThr=this.largeAmtTextInput.text,this.requestParams.order=null==this.ordinal?this.maxOrder:this.ordinal,this.requestParams.reOrder=this.reOrder,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams)}catch(e){console.error("",e),r.Wb.logError(e,this.moduleId,"CurrencyMaintenance","save",this.errorLocation)}},e.prototype.logicUpdateResult=function(t){try{var e=t,n=new r.L;n.setInputJSON(e),"RECORD_EXIST"==n.getRequestReplyMessage()?this.swtAlert.error(r.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null)):"ERROR_SAVE"==n.getRequestReplyMessage()?this.swtAlert.error(r.Wb.getPredictMessage("currencyMaintenanceDetails.notSaved.alert",null)):(this.updateData(),this.popupClosed())}catch(l){r.Wb.logError(l,this.moduleId,"CurrencyMaintenance","logicUpdateResult",this.errorLocation)}},e.prototype.updateData=function(){try{this.parentDocument.updateData()}catch(t){r.Wb.logError(t,r.Wb.AML_MODULE_ID,"CurrencyMaintenance","updateData",this.errorLocation)}},e.prototype.changeComboCurrency=function(t){this.currencyNameTxtInput.text=this.currencyComboBox.selectedItem.value,this.saveButton.enabled=""!==this.currencyComboBox.selectedItem.content,this.saveButton.buttonMode=""!==this.currencyComboBox.selectedItem.content},e.prototype.focusOutOrdinalInput=function(){try{this.ordinal!==Number(this.ordinalNumInput.text)&&(r.c.yesLabel=r.Wb.getPredictMessage("alert.yes.label",null),r.c.noLabel=r.Wb.getPredictMessage("alert.no.label",null),"add"==this.screenName?Number(this.ordinalNumInput.text)>=this.maxOrder||-1!==this.listOrder.indexOf(Number(this.ordinalNumInput.text))&&this.swtAlert.confirm(r.Wb.getPredictMessage("currencyMaintenanceDetails.reorder.alert",null),"Alert",r.c.YES|r.c.NO,null,this.ordinalAlertListener.bind(this)):-1!==this.listOrder.indexOf(this.ordinalNumInput.text)&&this.swtAlert.confirm(r.Wb.getPredictMessage("currencyMaintenanceDetails.reorder.alert",null),"Alert",r.c.YES|r.c.NO,null,this.ordinalAlertListener.bind(this)))}catch(t){console.log(t,this.moduleId,"CurrencyMaintenance","focusOutOrdinalInput")}},e.prototype.ordinalAlertListener=function(t){try{if(t.detail===r.c.YES)this.reOrder=!0;else if(this.reOrder=!1,"add"==this.screenName)this.ordinalNumInput.text=this.maxOrder;else{var e=String(this.jsonReader.getSingletons().ordinal);this.ordinalNumInput.text=""==e?this.maxOrder:this.jsonReader.getSingletons().ordinal}}catch(n){console.log(n,this.moduleId,"CurrencyMaintenance","ordinalAlertListener")}},e.prototype.validateAmount=function(){return!!validateCurrencyPlaces(this.largeAmtTextInput,this.currencyPattern,this.currencyComboBox.selectedLabel)||(this.swtAlert.warning(r.Wb.getPredictMessage("alert.validAmount",null)),!1)},e.prototype.doHelp=function(){try{r.x.call("help")}catch(t){r.Wb.logError(t,this.moduleId,"CurrencyMaintenance","doHelp",this.errorLocation)}},e.prototype.keyDownEventHandler=function(t){try{var e=Object(r.ic.getFocus()).name;t.keyCode==r.N.ENTER&&("saveButton"==e?this.save():"cancelButton"==e?this.popupClosed():"helpIcon"===e&&this.doHelp())}catch(n){console.log(n,this.moduleId,"CurrencyMaintenance","keyDownEventHandler")}},e.prototype.popupClosed=function(){this.dispose()},e.prototype.dispose=function(){try{this.requestParams=null,this.baseURL=null,this.actionMethod=null,this.actionPath=null,this.parentDocument.refreshScreen(),this.titleWindow?this.close():window.close()}catch(t){console.log(t,this.moduleId,"CurrencyMaintenance","dispose")}},e}(r.yb),a=[{path:"",component:u}],c=(o.l.forChild(a),function(){return function(){}}()),s=n("pMnS"),d=n("RChO"),h=n("t6HQ"),b=n("WFGK"),m=n("5FqG"),p=n("Ip0R"),g=n("gIcY"),y=n("t/Na"),w=n("sE5F"),R=n("OzfB"),I=n("T7CS"),x=n("S7LP"),C=n("6aHO"),f=n("WzUx"),v=n("A7o+"),B=n("zCE2"),A=n("Jg5P"),L=n("3R0m"),M=n("hhbb"),N=n("5rxC"),S=n("Fzqc"),D=n("21Lb"),O=n("hUWP"),P=n("3pJQ"),J=n("V9q+"),k=n("VDKW"),T=n("kXfT"),W=n("BGbe");n.d(e,"CurrencyMaintenanceAddModuleNgFactory",function(){return _}),n.d(e,"RenderType_CurrencyMaintenanceAdd",function(){return q}),n.d(e,"View_CurrencyMaintenanceAdd_0",function(){return G}),n.d(e,"View_CurrencyMaintenanceAdd_Host_0",function(){return j}),n.d(e,"CurrencyMaintenanceAddNgFactory",function(){return H});var _=l.Gb(c,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[s.a,d.a,h.a,b.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,H]],[3,l.n],l.J]),l.Rb(4608,p.m,p.l,[l.F,[2,p.u]]),l.Rb(4608,g.c,g.c,[]),l.Rb(4608,g.p,g.p,[]),l.Rb(4608,y.j,y.p,[p.c,l.O,y.n]),l.Rb(4608,y.q,y.q,[y.j,y.o]),l.Rb(5120,y.a,function(t){return[t,new r.tb]},[y.q]),l.Rb(4608,y.m,y.m,[]),l.Rb(6144,y.k,null,[y.m]),l.Rb(4608,y.i,y.i,[y.k]),l.Rb(6144,y.b,null,[y.i]),l.Rb(4608,y.f,y.l,[y.b,l.B]),l.Rb(4608,y.c,y.c,[y.f]),l.Rb(4608,w.c,w.c,[]),l.Rb(4608,w.g,w.b,[]),l.Rb(5120,w.i,w.j,[]),l.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),l.Rb(4608,w.f,w.a,[]),l.Rb(5120,w.d,w.k,[w.h,w.f]),l.Rb(5120,l.b,function(t,e){return[R.j(t,e)]},[p.c,l.O]),l.Rb(4608,I.a,I.a,[]),l.Rb(4608,x.a,x.a,[]),l.Rb(4608,C.a,C.a,[l.n,l.L,l.B,x.a,l.g]),l.Rb(4608,f.c,f.c,[l.n,l.g,l.B]),l.Rb(4608,f.e,f.e,[f.c]),l.Rb(4608,v.l,v.l,[]),l.Rb(4608,v.h,v.g,[]),l.Rb(4608,v.c,v.f,[]),l.Rb(4608,v.j,v.d,[]),l.Rb(4608,v.b,v.a,[]),l.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),l.Rb(4608,f.i,f.i,[[2,v.k]]),l.Rb(4608,f.r,f.r,[f.L,[2,v.k],f.i]),l.Rb(4608,f.t,f.t,[]),l.Rb(4608,f.w,f.w,[]),l.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),l.Rb(1073742336,p.b,p.b,[]),l.Rb(1073742336,g.n,g.n,[]),l.Rb(1073742336,g.l,g.l,[]),l.Rb(1073742336,B.a,B.a,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,g.e,g.e,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,v.i,v.i,[]),l.Rb(1073742336,f.b,f.b,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,y.d,y.d,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,M.b,M.b,[]),l.Rb(1073742336,N.b,N.b,[]),l.Rb(1073742336,R.c,R.c,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,D.d,D.d,[]),l.Rb(1073742336,O.c,O.c,[]),l.Rb(1073742336,P.a,P.a,[]),l.Rb(1073742336,J.a,J.a,[[2,R.g],l.O]),l.Rb(1073742336,k.b,k.b,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,W.b,W.b,[]),l.Rb(1073742336,r.Tb,r.Tb,[]),l.Rb(1073742336,c,c,[]),l.Rb(256,y.n,"XSRF-TOKEN",[]),l.Rb(256,y.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,v.m,void 0,[]),l.Rb(256,v.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,o.i,function(){return[[{path:"",component:u}]]},[]),l.Rb(256,"currencyMaintenanceAdd",u,[])])}),E=[[".labelForm[_ngcontent-%COMP%]{width:170px!important;height:19px!important;padding-left:5px!important}.numericInput[_ngcontent-%COMP%]{font-size:11px!important;height:22px!important}"]],q=l.Hb({encapsulation:0,styles:E,data:{}});function G(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{ordinalNumInput:0}),l.Zb(402653184,3,{currencyNameTxtInput:0}),l.Zb(402653184,4,{largeAmtTextInput:0}),l.Zb(402653184,5,{multiplierComboBox:0}),l.Zb(402653184,6,{currencyComboBox:0}),l.Zb(402653184,7,{loadingImage:0}),l.Zb(402653184,8,{saveButton:0}),l.Zb(402653184,9,{cancelButton:0}),l.Zb(402653184,10,{currencyLabel:0}),l.Zb(402653184,11,{currencyNameLabel:0}),l.Zb(402653184,12,{ordinalLabel:0}),l.Zb(402653184,13,{multiplierLabel:0}),l.Zb(402653184,14,{largeAmtLabel:0}),(t()(),l.Jb(14,0,null,null,71,"SwtModule",[["height","100%"],["width","100%"]],[[8,"title",0]],[[null,"close"],[null,"creationComplete"]],function(t,e,n){var l=!0,i=t.component;"close"===e&&(l=!1!==i.popupClosed()&&l);"creationComplete"===e&&(l=!1!==i.onLoad()&&l);return l},m.ad,m.hb)),l.Ib(15,4440064,null,0,r.yb,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(16,0,null,0,69,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(17,4440064,null,0,r.ec,[l.r,r.i,l.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),l.Jb(18,0,null,0,53,"SwtCanvas",[["height","85%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(19,4440064,null,0,r.db,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(20,0,null,0,51,"Grid",[["height","100%"],["width","100%"]],null,null,null,m.Cc,m.H)),l.Ib(21,4440064,null,0,r.z,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(22,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(23,4440064,null,0,r.B,[l.r,r.i],null,null),(t()(),l.Jb(24,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(25,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(26,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(27,4440064,[[10,4],["currencyLabel",4]],0,r.vb,[l.r,r.i],null,null),(t()(),l.Jb(28,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,m.Ac,m.I)),l.Ib(29,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(30,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","currencyComboBox"],["required","true"],["width","250"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,r=t.component;"window:mousewheel"===e&&(i=!1!==l.Tb(t,31).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==r.changeComboCurrency(n)&&i);return i},m.Pc,m.W)),l.Ib(31,4440064,[[6,4],["currencyComboBox",4]],0,r.gb,[l.r,r.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],required:[3,"required"]},{change_:"change"}),(t()(),l.Jb(32,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(33,4440064,null,0,r.B,[l.r,r.i],null,null),(t()(),l.Jb(34,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(35,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(36,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(37,4440064,[[11,4],["currencyNameLabel",4]],0,r.vb,[l.r,r.i],null,null),(t()(),l.Jb(38,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,m.Ac,m.I)),l.Ib(39,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(40,0,null,0,1,"SwtTextInput",[["id","currencyNameTxtInput"],["width","250"]],null,null,null,m.kd,m.sb)),l.Ib(41,4440064,[[3,4],["currencyNameTxtInput",4]],0,r.Rb,[l.r,r.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(42,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(43,4440064,null,0,r.B,[l.r,r.i],null,null),(t()(),l.Jb(44,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(45,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(46,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(47,4440064,[[12,4],["ordinalLabel",4]],0,r.vb,[l.r,r.i],null,null),(t()(),l.Jb(48,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,m.Ac,m.I)),l.Ib(49,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(50,0,null,0,1,"SwtNumericInput",[["class","numericInput"],["height","22"],["id","ordinalNumInput"],["textAlign","right"],["width","60"]],null,[[null,"focusOut"]],function(t,e,n){var l=!0,i=t.component;"focusOut"===e&&(l=!1!==i.focusOutOrdinalInput()&&l);return l},m.cd,m.jb)),l.Ib(51,4440064,[[2,4],["ordinalNumInput",4]],0,r.Ab,[l.r,r.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],height:[3,"height"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(52,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(53,4440064,null,0,r.B,[l.r,r.i],null,null),(t()(),l.Jb(54,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(55,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(56,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(57,4440064,[[13,4],["multiplierLabel",4]],0,r.vb,[l.r,r.i],null,null),(t()(),l.Jb(58,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,m.Ac,m.I)),l.Ib(59,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(60,0,null,0,1,"SwtComboBox",[["dataLabel","multiplierList"],["id","multiplierComboBox"],["width","250"]],null,[["window","mousewheel"]],function(t,e,n){var i=!0;"window:mousewheel"===e&&(i=!1!==l.Tb(t,61).mouseWeelEventHandler(n.target)&&i);return i},m.Pc,m.W)),l.Ib(61,4440064,[[5,4],["multiplierComboBox",4]],0,r.gb,[l.r,r.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},null),(t()(),l.Jb(62,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),l.Ib(63,4440064,null,0,r.B,[l.r,r.i],null,null),(t()(),l.Jb(64,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),l.Ib(65,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(66,0,null,0,1,"SwtLabel",[],null,null,null,m.Yc,m.fb)),l.Ib(67,4440064,[[14,4],["largeAmtLabel",4]],0,r.vb,[l.r,r.i],null,null),(t()(),l.Jb(68,0,null,0,3,"GridItem",[["width","60%"]],null,null,null,m.Ac,m.I)),l.Ib(69,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(t()(),l.Jb(70,0,null,0,1,"SwtTextInput",[["class","numericInput"],["height","22"],["id","largeAmtNumInput"],["textAlign","right"],["width","250"]],null,[[null,"focusOut"]],function(t,e,n){var l=!0,i=t.component;"focusOut"===e&&(l=!1!==i.validateAmount()&&l);return l},m.kd,m.sb)),l.Ib(71,4440064,[[4,4],["largeAmtTextInput",4]],0,r.Rb,[l.r,r.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],height:[3,"height"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(72,0,null,0,13,"SwtCanvas",[["height","15%"],["id","canvasButtons"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(73,4440064,null,0,r.db,[l.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(74,0,null,0,11,"HBox",[["height","100%"],["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(75,4440064,null,0,r.C,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(76,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(77,4440064,null,0,r.C,[l.r,r.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(78,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.save()&&l);"keyDown"===e&&(l=!1!==i.keyDownEventHandler(n)&&l);return l},m.Mc,m.T)),l.Ib(79,4440064,[[8,4],["saveButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),l.Jb(80,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.popupClosed()&&l);"keyDown"===e&&(l=!1!==i.keyDownEventHandler(n)&&l);return l},m.Mc,m.T)),l.Ib(81,4440064,[[9,4],["cancelButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),l.Jb(82,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","5"]],null,null,null,m.Dc,m.K)),l.Ib(83,4440064,null,0,r.C,[l.r,r.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),l.Jb(84,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.doHelp()&&l);return l},m.Wc,m.db)),l.Ib(85,4440064,null,0,r.rb,[l.r,r.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"})],function(t,e){t(e,15,0,"100%","100%");t(e,17,0,"vBox1","100%","100%","5","5","5","5");t(e,19,0,"100%","85%");t(e,21,0,"100%","100%"),t(e,23,0);t(e,25,0,"40%"),t(e,27,0);t(e,29,0,"60%");t(e,31,0,"currencyList","250","currencyComboBox","true"),t(e,33,0);t(e,35,0,"40%"),t(e,37,0);t(e,39,0,"60%");t(e,41,0,"currencyNameTxtInput","250"),t(e,43,0);t(e,45,0,"40%"),t(e,47,0);t(e,49,0,"60%");t(e,51,0,"ordinalNumInput","right","60","22"),t(e,53,0);t(e,55,0,"40%"),t(e,57,0);t(e,59,0,"60%");t(e,61,0,"multiplierList","250","multiplierComboBox"),t(e,63,0);t(e,65,0,"40%"),t(e,67,0);t(e,69,0,"60%");t(e,71,0,"largeAmtNumInput","right","250","22");t(e,73,0,"canvasButtons","100%","15%");t(e,75,0,"100%","100%");t(e,77,0,"100%","5");t(e,79,0,"saveButton");t(e,81,0,"cancelButton","true");t(e,83,0,"right","5");t(e,85,0,"helpIcon","true",!0)},function(t,e){var n=e.component;t(e,14,0,l.Lb(1,"",n.title,""))})}function j(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-currency-maintenance-add",[],null,null,null,G,q)),l.Ib(1,4440064,null,0,u,[r.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var H=l.Fb("app-currency-maintenance-add",u,j,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);