(window.webpackJsonp=window.webpackJsonp||[]).push([[63],{V1wf:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),l=i("447K"),o=i("ZYCi"),s=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.moduleId="Predict",n.errorLocation=0,n.jsonReader=new l.L,n.inputData=new l.G(n.commonService),n.deleteData=new l.G(n.commonService),n.checkAuthData=new l.G(n.commonService),n.baseURL=l.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.requireAuthorisation=!0,n.doDeleterecordAction=!1,n.faciltiyId=null,n.screenNameWindow=null,n.logger=new l.R("Critical Movement Update Maintenance Screen",n.commonService.httpclient),n.swtAlert=new l.bb(e),n}return a.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.criticalPayTypeGrid=this.gridCanvas.addChild(l.hb),this.entityLabel.text=l.Wb.getPredictMessage("entity.id",null),this.entityCombo.toolTip=l.Wb.getPredictMessage("tooltip.selectEntity",null),this.addButton.label=l.Wb.getPredictMessage("button.add",null),this.changeButton.label=l.Wb.getPredictMessage("button.change",null),this.viewButton.label=l.Wb.getPredictMessage("button.view",null),this.deleteButton.label=l.Wb.getPredictMessage("button.delete",null),this.closeButton.label=l.Wb.getPredictMessage("button.close",null),this.addButton.toolTip=l.Wb.getPredictMessage("tooltip.add",null),this.changeButton.toolTip=l.Wb.getPredictMessage("tooltip.change",null),this.viewButton.toolTip=l.Wb.getPredictMessage("tooltip.view",null),this.deleteButton.toolTip=l.Wb.getPredictMessage("tooltip.Critical.Mvt.delete",null),this.closeButton.toolTip=l.Wb.getPredictMessage("tooltip.close",null)},e.prototype.updateData=function(){var t=this;this.requestParams=[],this.doDeleterecordAction=!1,this.menuAccessId=l.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="criticalPaymentType.do?",this.actionMethod="method=display",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.entityId=this.entityCombo.selectedLabel,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.cellClickEventHandler=function(t){this.criticalPayTypeGrid.selectedItem.access.content;"0"==this.userRoleAccessId?t?(this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1):(this.changeButton.enabled=!0,this.changeButton.buttonMode=!0,this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0):(this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1),this.viewButton.enabled=!0,this.viewButton.buttonMode=!0},e.prototype.doOpenChildWindow=function(t){this.screenNameWindow=t,l.x.call("openDetailScreen","subScreen",t)},e.prototype.inputDataResult=function(t){try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON){if(this.doDeleterecordAction&&(l.Z.isTrue(this.requireAuthorisation)&&this.swtAlert.show("This action needs second user authorisation","Warning",l.c.OK),this.doDeleterecordAction=!1),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.entityCombo.selectedLabel=this.jsonReader.getSingletons().defaultEntity,this.userRoleAccessId=this.jsonReader.getSingletons().userAccess,this.selectedEntity.text=this.entityCombo.selectedValue,!this.jsonReader.isDataBuilding()){var e={columns:this.lastRecievedJSON.CriticalPayType.criticalPayTypeGrid.metadata.columns};this.criticalPayTypeGrid.CustomGrid(e);var i=this.lastRecievedJSON.CriticalPayType.criticalPayTypeGrid.rows;i.size>0?(this.criticalPayTypeGrid.gridData=i,this.criticalPayTypeGrid.setRowSize=this.jsonReader.getRowSize()):this.criticalPayTypeGrid.gridData={size:0,row:[]},this.requireAuthorisation=this.jsonReader.getScreenAttributes().requireAuthorisation,this.faciltiyId=this.jsonReader.getScreenAttributes().faciltiyId}this.prevRecievedJSON=this.lastRecievedJSON}}catch(n){console.log("error in inputData",n)}},e.prototype.doDelete=function(){try{l.c.yesLabel="Yes",l.c.noLabel="No";var t=l.Wb.getPredictMessage("confirm.delete");this.swtAlert.confirm(t,"Alert",l.c.OK|l.c.CANCEL,null,this.deleteType.bind(this))}catch(e){l.Wb.logError(e,this.moduleId,"CriticalMvtUpdateMaintenance","doDeleteAccount",this.errorLocation)}},e.prototype.deleteType=function(t){try{t.detail===l.c.OK&&this.deleteAfterCheckFourEyes()}catch(e){l.Wb.logError(e,this.moduleId,"CriticalMvtUpdateMaintenance","deleteType",this.errorLocation)}},e.prototype.deleteAfterCheckFourEyes=function(){var t=this;this.doDeleterecordAction=!0,this.actionMethod="method=deleteType",this.actionPath="criticalPaymentType.do?",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.requestParams.method="delete",this.requestParams.CpEntityId=this.criticalPayTypeGrid.selectedItem.entity.content,this.requestParams.criticalPayId=this.criticalPayTypeGrid.selectedItem.type.content,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.criticalPayTypeGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.viewButton.enabled=!1,this.deleteButton.enabled=!1},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.menuAccessId=l.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="criticalPaymentType.do?",this.actionMethod="method=display",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.entityId="All",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.criticalPayTypeGrid.onRowClick=function(e){t.checkIfMaintenanceEventExist(e)}},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.closeHandler=function(){l.x.call("close")},e.prototype.deleteDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReader.setInputJSON(t),this.jsonReader.getRequestReplyStatus()?this.updateData():"DataIntegrityViolationExceptioninDelete"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error(l.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninDelete")+l.Wb.getPredictMessage("alert.ContactSysAdm"),"Error"):this.swtAlert.error("Error occurred, Please contact your System Administrator: \n"+this.jsonReader.getRequestReplyMessage(),"Error"))},e.prototype.checkIfMaintenanceEventExist=function(t){var e=this;if(this.criticalPayTypeGrid.selectedIndex>=0)try{this.checkAuthData.cbStart=this.startOfComms.bind(this),this.checkAuthData.cbStop=this.endOfComms.bind(this),this.checkAuthData.cbResult=function(t){e.checkResult(t)},this.requestParams.recordId=this.criticalPayTypeGrid.selectedItem.type.content,this.requestParams.facilityId=this.faciltiyId,this.checkAuthData.cbFault=this.inputDataFault.bind(this),this.checkAuthData.encodeURL=!1,this.checkAuthData.url=this.baseURL+"maintenanceEvent.do?method=checkIfMaintenenanceEventExist",this.checkAuthData.send(this.requestParams)}catch(i){console.log(i)}else this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.viewButton.enabled=!1,this.viewButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1},e.prototype.checkResult=function(t){try{if(this.checkAuthData&&this.checkAuthData.isBusy())this.checkAuthData.cbStop();else if(this.jsonReader.setInputJSON(t),"RECOD_EXIST"==this.jsonReader.getRequestReplyMessage()){var e=l.Wb.getPredictMessage("maintenanceEvent.alert.cannotBeAmended",null);this.swtAlert.error(e),this.cellClickEventHandler(!0)}else this.cellClickEventHandler(!1)}catch(i){console.log("error in inputData",i)}},e.prototype.updateDataFromChild=function(){var t=this;try{this.requestParams=[],this.actionMethod="method=display",this.doDeleterecordAction=!1,this.actionPath="criticalPaymentType.do?",this.requestParams.method="display",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.entityId=this.entityCombo.selectedLabel,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(e){t.inputDataResult(e),t.criticalPayTypeGrid.refresh(),t.criticalPayTypeGrid.refreshFilters()},this.criticalPayTypeGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.changeButton.buttonMode=!1,this.viewButton.enabled=!1,this.viewButton.buttonMode=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1,this.inputData.send(this.requestParams)}catch(e){}},e.prototype.getParams=function(){return"add"==this.screenNameWindow?[this.screenNameWindow]:[this.screenNameWindow,this.criticalPayTypeGrid.selectedItem.type.content,this.criticalPayTypeGrid.selectedItem.entity.content]},e}(l.yb),c=[{path:"",component:s}],d=(o.l.forChild(c),function(){return function(){}}()),u=i("pMnS"),r=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),g=i("gIcY"),y=i("t/Na"),R=i("sE5F"),w=i("OzfB"),f=i("T7CS"),v=i("S7LP"),C=i("6aHO"),I=i("WzUx"),B=i("A7o+"),D=i("zCE2"),P=i("Jg5P"),A=i("3R0m"),T=i("hhbb"),M=i("5rxC"),S=i("Fzqc"),k=i("21Lb"),L=i("hUWP"),W=i("3pJQ"),G=i("V9q+"),O=i("VDKW"),E=i("kXfT"),J=i("BGbe");i.d(e,"CriticalMvtUpdateMaintenanceModuleNgFactory",function(){return x}),i.d(e,"RenderType_CriticalMvtUpdateMaintenance",function(){return N}),i.d(e,"View_CriticalMvtUpdateMaintenance_0",function(){return _}),i.d(e,"View_CriticalMvtUpdateMaintenance_Host_0",function(){return U}),i.d(e,"CriticalMvtUpdateMaintenanceNgFactory",function(){return F});var x=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[u.a,r.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,F]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,g.c,g.c,[]),n.Rb(4608,g.p,g.p,[]),n.Rb(4608,y.j,y.p,[m.c,n.O,y.n]),n.Rb(4608,y.q,y.q,[y.j,y.o]),n.Rb(5120,y.a,function(t){return[t,new l.tb]},[y.q]),n.Rb(4608,y.m,y.m,[]),n.Rb(6144,y.k,null,[y.m]),n.Rb(4608,y.i,y.i,[y.k]),n.Rb(6144,y.b,null,[y.i]),n.Rb(4608,y.f,y.l,[y.b,n.B]),n.Rb(4608,y.c,y.c,[y.f]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.g,R.b,[]),n.Rb(5120,R.i,R.j,[]),n.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),n.Rb(4608,R.f,R.a,[]),n.Rb(5120,R.d,R.k,[R.h,R.f]),n.Rb(5120,n.b,function(t,e){return[w.j(t,e)]},[m.c,n.O]),n.Rb(4608,f.a,f.a,[]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,v.a,n.g]),n.Rb(4608,I.c,I.c,[n.n,n.g,n.B]),n.Rb(4608,I.e,I.e,[I.c]),n.Rb(4608,B.l,B.l,[]),n.Rb(4608,B.h,B.g,[]),n.Rb(4608,B.c,B.f,[]),n.Rb(4608,B.j,B.d,[]),n.Rb(4608,B.b,B.a,[]),n.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),n.Rb(4608,I.i,I.i,[[2,B.k]]),n.Rb(4608,I.r,I.r,[I.L,[2,B.k],I.i]),n.Rb(4608,I.t,I.t,[]),n.Rb(4608,I.w,I.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,g.n,g.n,[]),n.Rb(1073742336,g.l,g.l,[]),n.Rb(1073742336,D.a,D.a,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,A.a,A.a,[]),n.Rb(1073742336,B.i,B.i,[]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,y.e,y.e,[]),n.Rb(1073742336,y.d,y.d,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,M.b,M.b,[]),n.Rb(1073742336,w.c,w.c,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,k.d,k.d,[]),n.Rb(1073742336,L.c,L.c,[]),n.Rb(1073742336,W.a,W.a,[]),n.Rb(1073742336,G.a,G.a,[[2,w.g],n.O]),n.Rb(1073742336,O.b,O.b,[]),n.Rb(1073742336,E.a,E.a,[]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,l.Tb,l.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,y.n,"XSRF-TOKEN",[]),n.Rb(256,y.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,B.m,void 0,[]),n.Rb(256,B.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:s}]]},[])])}),q=[[""]],N=n.Hb({encapsulation:0,styles:q,data:{}});function _(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{loadingImage:0}),n.Zb(402653184,3,{gridCanvas:0}),n.Zb(402653184,4,{entityCombo:0}),n.Zb(402653184,5,{entityLabel:0}),n.Zb(402653184,6,{selectedEntity:0}),n.Zb(402653184,7,{addButton:0}),n.Zb(402653184,8,{changeButton:0}),n.Zb(402653184,9,{viewButton:0}),n.Zb(402653184,10,{deleteButton:0}),n.Zb(402653184,11,{closeButton:0}),(t()(),n.Jb(11,0,null,null,49,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,a=t.component;"creationComplete"===e&&(n=!1!==a.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(12,4440064,null,0,l.yb,[n.r,l.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(13,0,null,0,47,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(14,4440064,null,0,l.ec,[n.r,l.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),n.Jb(15,0,null,0,19,"SwtCanvas",[["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(16,4440064,null,0,l.db,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(17,0,null,0,17,"Grid",[["height","100%"],["verticalGap","2"],["width","100%"]],null,null,null,p.Cc,p.H)),n.Ib(18,4440064,null,0,l.z,[n.r,l.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(19,0,null,0,15,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(20,4440064,[["gridRowEntity",4]],0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(21,0,null,0,3,"GridItem",[["width","80"]],null,null,null,p.Ac,p.I)),n.Ib(22,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(23,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","entityLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(24,4440064,[[5,4],["entityLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(25,0,null,0,5,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),n.Ib(26,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(27,0,null,0,3,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(28,4440064,[["entityComboHBox",4]],0,l.C,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(29,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,l=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,30).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==l.updateData()&&a);return a},p.Pc,p.W)),n.Ib(30,4440064,[[4,4],["entityCombo",4]],0,l.gb,[n.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(31,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(32,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(33,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["textAlign","left"]],null,null,null,p.Yc,p.fb)),n.Ib(34,4440064,[[6,4],["selectedEntity",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],textAlign:[1,"textAlign"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(35,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["marginTop","10"],["minHeight","100"],["paddingBottom","5"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(36,4440064,[[3,4],["gridCanvas",4]],0,l.db,[n.r,l.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],minHeight:[3,"minHeight"],paddingBottom:[4,"paddingBottom"],marginTop:[5,"marginTop"],border:[6,"border"]},null),(t()(),n.Jb(37,0,null,0,23,"SwtCanvas",[["height","35"],["id","canvasButtons"],["marginTop","5"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(38,4440064,null,0,l.db,[n.r,l.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),n.Jb(39,0,null,0,21,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(40,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(41,0,null,0,11,"HBox",[["paddingLeft","5"],["width","70%"]],null,null,null,p.Dc,p.K)),n.Ib(42,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(43,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doOpenChildWindow("add")&&n);return n},p.Mc,p.T)),n.Ib(44,4440064,[[7,4],["addButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(45,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doOpenChildWindow("change")&&n);return n},p.Mc,p.T)),n.Ib(46,4440064,[[8,4],["changeButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(47,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doOpenChildWindow("view")&&n);return n},p.Mc,p.T)),n.Ib(48,4440064,[[9,4],["viewButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(49,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doDelete()&&n);return n},p.Mc,p.T)),n.Ib(50,4440064,[[10,4],["deleteButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(51,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(52,4440064,[[11,4],["closeButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(53,0,null,0,7,"HBox",[["paddingRight","5"],["width","30%"]],null,null,null,p.Dc,p.K)),n.Ib(54,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(55,0,null,0,1,"HBox",[["width","80%"]],null,null,null,p.Dc,p.K)),n.Ib(56,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(57,0,null,0,3,"HBox",[["paddingTop","2"],["width","20%"]],null,null,null,p.Dc,p.K)),n.Ib(58,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),n.Jb(59,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(60,114688,[[2,4],["loadingImage",4]],0,l.xb,[n.r],null,null)],function(t,e){t(e,12,0,"100%","100%");t(e,14,0,"100%","100%","5","5","5");t(e,16,0,"100%");t(e,18,0,"2","100%","100%"),t(e,20,0);t(e,22,0,"80");t(e,24,0,"entityLabel","bold");t(e,26,0,"150");t(e,28,0,"100%");t(e,30,0,"entity","135","entityCombo");t(e,32,0,"10%");t(e,34,0,"selectedEntity","left","normal");t(e,36,0,"canvasWithGreyBorder","100%","100%","100","5","10","false");t(e,38,0,"canvasButtons","100%","35","5");t(e,40,0,"100%");t(e,42,0,"70%","5");t(e,44,0,"addButton");t(e,46,0,"changeButton","false");t(e,48,0,"viewButton","false");t(e,50,0,"deleteButton","false");t(e,52,0,"closeButton");t(e,54,0,"30%","5");t(e,56,0,"80%");t(e,58,0,"20%","2"),t(e,60,0)},null)}function U(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-critical-mvt-update-maintenance",[],null,null,null,_,N)),n.Ib(1,4440064,null,0,s,[l.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var F=n.Fb("app-critical-mvt-update-maintenance",s,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);