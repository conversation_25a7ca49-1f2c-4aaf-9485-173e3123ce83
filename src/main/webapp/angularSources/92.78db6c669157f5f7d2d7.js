(window.webpackJsonp=window.webpackJsonp||[]).push([[92],{GrQm:function(t,e,n){"use strict";n.r(e);var l=n("CcnG"),i=n("mrSG"),a=n("447K"),o=n("ZYCi"),c=function(t){function e(e,n){var l=t.call(this,n,e)||this;return l.commonService=e,l.element=n,l.baseURL=a.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.jsonReader=new a.L,l.screenVersion=new a.V(l.commonService),l.comboChange=!1,l.menuAccessIdParent=0,l.ccyList=null,l.entityList=null,l.templateList=null,l.screenName="Template Options",l.versionNumber="1.1.0002",l.releaseDate="30 May 2019",l.inputData=new a.G(l.commonService),l.refreshData=new a.G(l.commonService),l.saveData=new a.G(l.commonService),l.invalidComms="",l.swtAlert=new a.bb(e),window.Main=l,l}return i.d(e,t),e.prototype.ngOnDestroy=function(){},e.prototype.ngOnInit=function(){this.entityLabel.text=a.Wb.getPredictMessage("label.forecastMonitor.entity",null),this.cbEntity.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.selectEntity",null),this.currencyLabel.text=a.Wb.getPredictMessage("label.forecastMonitor.currency",null),this.cbCurrency.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.selectCurrency",null),this.templateLabel.text=a.Wb.getPredictMessage("label.forecastTemplateOption.template",null),this.cbTemplate.toolTip=a.Wb.getPredictMessage("tooltip.forecastTemplateOption.template",null),this.btnCancel.label=a.Wb.getPredictMessage("button.forecastMonitor.cancel",null),this.btnCancel.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.cancel",null),this.btnSave.label=a.Wb.getPredictMessage("button.forecastMonitor.ok",null),this.btnSave.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.ok",null),this.btnAdd.label=a.Wb.getPredictMessage("button.forecastMonitor.add",null),this.btnAdd.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.add",null),this.btnChange.label=a.Wb.getPredictMessage("button.forecastMonitor.change",null),this.btnChange.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitor.change",null)},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.menuAccessIdParent=a.x.call("eval","menuAccessIdParent"),1==this.menuAccessIdParent&&(this.btnSave.enabled=!1),this.initializeMenus(),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.refreshData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.refreshData.cbFault=this.inputDataFault.bind(this),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.encodeURL=!1,this.actionPath="forecastMonitor.do?",this.actionMethod="method=displayUserTemplateOptions","undefined"==a.x.call("eval","entityId")?this.requestParams["forecastTemplateOptions.id.entityId"]="":this.requestParams["forecastTemplateOptions.id.entityId"]=a.x.call("eval","entityId"),this.requestParams["forecastTemplateOptions.id.currencyCode"]=a.x.call("eval","currencyCode"),this.requestParams["forecastTemplateOptions.templateId"]=a.x.call("eval","templateId"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.inputData.send(this.requestParams),window.opener&&window.opener.instanceElement&&(this.optionXML=window.opener.instanceElement.templateData)},e.prototype.saveDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&a.x.call("closeWindow"))},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var t=new a.n("Show JSON");t.MenuItemSelect=this.showJSONSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showJSONSelect=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="200",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.inputDataResult=function(t){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&(this.cbEntity.setComboData(this.jsonReader.getSelects(),!1),this.cbCurrency.setComboData(this.jsonReader.getSelects(),!1),this.cbTemplate.setComboData(this.jsonReader.getSelects(),!1),""!=a.Z.trim(this.cbCurrency.selectedItem.content)&&(this.selectedCurrency.text=this.cbCurrency.selectedItem.value),""!=a.Z.trim(this.cbEntity.selectedItem.content)&&(this.selectedEntity.text=this.cbEntity.selectedItem.value),""!=a.Z.trim(this.cbTemplate.selectedItem.content)&&(this.selectedTemplate.text=this.cbTemplate.selectedItem.value),this.ccyList=this.jsonReader.getSelects().select.find(function(t){return"currency"==t.id}).option,this.entityList=this.jsonReader.getSelects().select.find(function(t){return"entity"==t.id}).option,this.templateList=this.jsonReader.getSelects().select.find(function(t){return"template"==t.id}).option,"undefined"!=a.x.call("eval","currencyCode")&&(this.cbCurrency.selectedItem.content=a.x.call("eval","currencyCode"),this.cbEntity.selectedItem.content=a.x.call("eval","entityId"),this.cbTemplate.selectedItem.content=a.x.call("eval","templateId"),this.cbCurrency.enabled=!1,this.cbEntity.enabled=!1),this.setSelectedValue()))}catch(e){console.log("error input datarespt",e)}},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.addTemplate=function(){this.actionMethod="displayAddMonitorTemplate",this.actionMethod=this.actionMethod+"&callFrom=FTO",a.x.call("openTemplateAddWindow",this.actionMethod)},e.prototype.changeTemplate=function(){var t=null,e=null,n=null,l=a.x.call("eval","userId");"*DEFAULT*"==this.cbTemplate.selectedLabel&&(l="*DEFAULT*"),""!=(t=a.x.call("getTemplateDetails",this.cbTemplate.selectedLabel,l))&&2==t.split("_DELIMETER_").length&&(e="Y"==a.Z.trim(t.split("_DELIMETER_")[1])?"Yes":"No",null==(n=a.x.call("lockTemplate",this.cbTemplate.selectedLabel,t.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[0],t.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[1],e))||""==a.Z.trim(n)?(this.actionMethod="displayChangeMonitorTemplate",this.actionMethod=this.actionMethod+"&templateId="+this.cbTemplate.selectedLabel,this.actionMethod=this.actionMethod+"&userId="+t.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[0],this.actionMethod=this.actionMethod+"&templateName="+t.split("_DELIMETER_")[0].split("_DELIMETERFORUSER_")[1],this.actionMethod=this.actionMethod+"&isPublic="+e,this.actionMethod=this.actionMethod+"&callFrom=FTO",a.x.call("openTemplateChangeWindow",this.actionMethod)):this.swtAlert.warning(a.x.call("getBundle","text","templateLock","Template ID is locked by")+" "+n,"Warning"))},e.prototype.changeCombo=function(t){this.selectedCurrency.text=this.cbCurrency.selectedItem.value,this.selectedEntity.text=this.cbEntity.selectedItem.value,this.selectedTemplate.text=this.cbTemplate.selectedItem.value,this.comboChange=!0,this.updateData("yes")},e.prototype.changeCurrency=function(t){this.selectedCurrency.text=this.cbCurrency.selectedItem.value},e.prototype.changeComboTemplate=function(t){this.selectedTemplate.text=this.cbTemplate.selectedItem.value},e.prototype.setSelectedValue=function(){this.selectedCurrency.text=this.cbCurrency.selectedItem.value,this.selectedEntity.text=this.cbEntity.selectedItem.value,this.selectedTemplate.text=this.cbTemplate.selectedItem.value},e.prototype.updateData=function(t){var e=!0;if("no"==t){if("undefined"==a.x.call("eval","currencyCode"))for(var n=0;n<this.optionXML.length;n++){if(this.optionXML[n].currency==String(this.cbCurrency.selectedItem.content)&&this.optionXML[n].entity==String(this.cbEntity.selectedItem.content)&&this.optionXML[n].templateid==String(this.cbTemplate.selectedItem.content)&&"delete"!=this.optionXML[n].modifystate){e=!1,this.swtAlert.warning(a.Wb.getPredictMessage("alert.templateOption.recordExist",null),"Warning");break}if(this.optionXML[n].currency==String(this.cbCurrency.selectedItem.content)&&this.optionXML[n].entity==String(this.cbEntity.selectedItem.content)&&"delete"!=this.optionXML[n].modifystate){this.swtAlert.warning(a.Wb.getPredictMessage("alert.templateOption.duplicateTemplate",null),"Warning"),e=!1;break}}if(e){this.btnSave.enabled=!1;var l=new Object;l.entity=String(this.cbEntity.selectedItem.content),l.currency=String(this.cbCurrency.selectedItem.content),l.template=String(this.cbTemplate.selectedItem.content),a.x.call("unloadCloseWindow",l)}}else this.requestParams=[],this.requestParams["forecastTemplateOptions.id.entityId"]=this.cbEntity.selectedItem.content,this.requestParams["forecastTemplateOptions.templateId"]=this.cbTemplate.selectedItem.content,this.refreshData.encodeURL=!1,this.actionPath="forecastMonitor.do?",this.actionMethod="method=displayUserTemplateOptions",this.refreshData.url=this.baseURL+this.actionPath+this.actionMethod,this.refreshData.send(this.requestParams)},e.prototype.doHelp=function(t){a.x.call("help")},e.prototype.closeHandler=function(t){a.x.call("closeWindow")},e}(a.yb),s=[{path:"",component:c}],h=(o.l.forChild(s),function(){return function(){}}()),d=n("pMnS"),u=n("RChO"),r=n("t6HQ"),b=n("WFGK"),p=n("5FqG"),m=n("Ip0R"),g=n("gIcY"),w=n("t/Na"),y=n("sE5F"),f=n("OzfB"),R=n("T7CS"),I=n("S7LP"),T=n("6aHO"),C=n("WzUx"),v=n("A7o+"),M=n("zCE2"),S=n("Jg5P"),L=n("3R0m"),E=n("hhbb"),D=n("5rxC"),O=n("Fzqc"),J=n("21Lb"),x=n("hUWP"),N=n("3pJQ"),k=n("V9q+"),P=n("VDKW"),_=n("kXfT"),A=n("BGbe");n.d(e,"TemplateOptionsModuleNgFactory",function(){return W}),n.d(e,"RenderType_TemplateOptions",function(){return G}),n.d(e,"View_TemplateOptions_0",function(){return F}),n.d(e,"View_TemplateOptions_Host_0",function(){return U}),n.d(e,"TemplateOptionsNgFactory",function(){return Z});var W=l.Gb(h,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,u.a,r.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,Z]],[3,l.n],l.J]),l.Rb(4608,m.m,m.l,[l.F,[2,m.u]]),l.Rb(4608,g.c,g.c,[]),l.Rb(4608,g.p,g.p,[]),l.Rb(4608,w.j,w.p,[m.c,l.O,w.n]),l.Rb(4608,w.q,w.q,[w.j,w.o]),l.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),l.Rb(4608,w.m,w.m,[]),l.Rb(6144,w.k,null,[w.m]),l.Rb(4608,w.i,w.i,[w.k]),l.Rb(6144,w.b,null,[w.i]),l.Rb(4608,w.f,w.l,[w.b,l.B]),l.Rb(4608,w.c,w.c,[w.f]),l.Rb(4608,y.c,y.c,[]),l.Rb(4608,y.g,y.b,[]),l.Rb(5120,y.i,y.j,[]),l.Rb(4608,y.h,y.h,[y.c,y.g,y.i]),l.Rb(4608,y.f,y.a,[]),l.Rb(5120,y.d,y.k,[y.h,y.f]),l.Rb(5120,l.b,function(t,e){return[f.j(t,e)]},[m.c,l.O]),l.Rb(4608,R.a,R.a,[]),l.Rb(4608,I.a,I.a,[]),l.Rb(4608,T.a,T.a,[l.n,l.L,l.B,I.a,l.g]),l.Rb(4608,C.c,C.c,[l.n,l.g,l.B]),l.Rb(4608,C.e,C.e,[C.c]),l.Rb(4608,v.l,v.l,[]),l.Rb(4608,v.h,v.g,[]),l.Rb(4608,v.c,v.f,[]),l.Rb(4608,v.j,v.d,[]),l.Rb(4608,v.b,v.a,[]),l.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),l.Rb(4608,C.i,C.i,[[2,v.k]]),l.Rb(4608,C.r,C.r,[C.L,[2,v.k],C.i]),l.Rb(4608,C.t,C.t,[]),l.Rb(4608,C.w,C.w,[]),l.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),l.Rb(1073742336,m.b,m.b,[]),l.Rb(1073742336,g.n,g.n,[]),l.Rb(1073742336,g.l,g.l,[]),l.Rb(1073742336,M.a,M.a,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,g.e,g.e,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,v.i,v.i,[]),l.Rb(1073742336,C.b,C.b,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,w.d,w.d,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,E.b,E.b,[]),l.Rb(1073742336,D.b,D.b,[]),l.Rb(1073742336,f.c,f.c,[]),l.Rb(1073742336,O.a,O.a,[]),l.Rb(1073742336,J.d,J.d,[]),l.Rb(1073742336,x.c,x.c,[]),l.Rb(1073742336,N.a,N.a,[]),l.Rb(1073742336,k.a,k.a,[[2,f.g],l.O]),l.Rb(1073742336,P.b,P.b,[]),l.Rb(1073742336,_.a,_.a,[]),l.Rb(1073742336,A.b,A.b,[]),l.Rb(1073742336,a.Tb,a.Tb,[]),l.Rb(1073742336,h,h,[]),l.Rb(256,w.n,"XSRF-TOKEN",[]),l.Rb(256,w.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,v.m,void 0,[]),l.Rb(256,v.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,o.i,function(){return[[{path:"",component:c}]]},[])])}),B=[[""]],G=l.Hb({encapsulation:0,styles:B,data:{}});function F(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{btnCancel:0}),l.Zb(402653184,3,{btnSave:0}),l.Zb(402653184,4,{btnAdd:0}),l.Zb(402653184,5,{btnChange:0}),l.Zb(402653184,6,{helpIcon:0}),l.Zb(402653184,7,{loadingImage:0}),l.Zb(402653184,8,{cbEntity:0}),l.Zb(402653184,9,{cbCurrency:0}),l.Zb(402653184,10,{cbTemplate:0}),l.Zb(402653184,11,{entityLabel:0}),l.Zb(402653184,12,{selectedEntity:0}),l.Zb(402653184,13,{currencyLabel:0}),l.Zb(402653184,14,{selectedCurrency:0}),l.Zb(402653184,15,{templateLabel:0}),l.Zb(402653184,16,{selectedTemplate:0}),(t()(),l.Jb(16,0,null,null,73,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,n){var l=!0,i=t.component;"creationComplete"===e&&(l=!1!==i.onLoad()&&l);return l},p.ad,p.hb)),l.Ib(17,4440064,null,0,a.yb,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(18,0,null,0,71,"VBox",[["height","100%"],["id","appContainer"],["paddingBottom","20"],["paddingLeft","20"],["paddingRight","20"],["paddingTop","20"],["width","100%"]],null,null,null,p.od,p.vb)),l.Ib(19,4440064,null,0,a.ec,[l.r,a.i,l.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),l.Jb(20,0,null,0,53,"SwtCanvas",[["height","75%"],["id","swtControlBar"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(21,4440064,null,0,a.db,[l.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(22,0,null,0,51,"Grid",[["height","100%"],["paddingLeft","15"],["paddingTop","10"],["width","100%"]],null,null,null,p.Cc,p.H)),l.Ib(23,4440064,null,0,a.z,[l.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},null),(t()(),l.Jb(24,0,null,0,13,"GridRow",[["height","100%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(25,4440064,null,0,a.B,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(26,0,null,0,3,"GridItem",[["height","100%"],["paddingTop","4"],["width","15%"]],null,null,null,p.Ac,p.I)),l.Ib(27,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),l.Jb(28,0,null,0,1,"SwtLabel",[["paddingRight","10"],["styleName","labelBold"]],null,null,null,p.Yc,p.fb)),l.Ib(29,4440064,[[11,4],["entityLabel",4]],0,a.vb,[l.r,a.i],{styleName:[0,"styleName"],paddingRight:[1,"paddingRight"]},null),(t()(),l.Jb(30,0,null,0,3,"GridItem",[["height","100%"],["width","21%"]],null,null,null,p.Ac,p.I)),l.Ib(31,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(32,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","cbEntity"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,a=t.component;"window:mousewheel"===e&&(i=!1!==l.Tb(t,33).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==a.changeCombo(n)&&i);return i},p.Pc,p.W)),l.Ib(33,4440064,[[8,4],["cbEntity",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(34,0,null,0,3,"GridItem",[["height","100%"],["width","21%"]],null,null,null,p.Ac,p.I)),l.Ib(35,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(36,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["styleName","labelLeft"],["width","239"]],null,null,null,p.Yc,p.fb)),l.Ib(37,4440064,[[12,4],["selectedEntity",4]],0,a.vb,[l.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],fontWeight:[3,"fontWeight"]},null),(t()(),l.Jb(38,0,null,0,13,"GridRow",[["height","100%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(39,4440064,null,0,a.B,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(40,0,null,0,3,"GridItem",[["height","100%"],["paddingTop","2"],["width","15%"]],null,null,null,p.Ac,p.I)),l.Ib(41,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),l.Jb(42,0,null,0,1,"SwtLabel",[["paddingRight","10"],["styleName","labelBold"]],null,null,null,p.Yc,p.fb)),l.Ib(43,4440064,[[13,4],["currencyLabel",4]],0,a.vb,[l.r,a.i],{styleName:[0,"styleName"],paddingRight:[1,"paddingRight"]},null),(t()(),l.Jb(44,0,null,0,3,"GridItem",[["height","100%"],["width","21%"]],null,null,null,p.Ac,p.I)),l.Ib(45,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(46,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","cbCurrency"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,a=t.component;"window:mousewheel"===e&&(i=!1!==l.Tb(t,47).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==a.changeCurrency(n)&&i);return i},p.Pc,p.W)),l.Ib(47,4440064,[[9,4],["cbCurrency",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(48,0,null,0,3,"GridItem",[["height","100%"],["width","21%"]],null,null,null,p.Ac,p.I)),l.Ib(49,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(50,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCurrency"],["styleName","labelLeft"],["width","239"]],null,null,null,p.Yc,p.fb)),l.Ib(51,4440064,[[14,4],["selectedCurrency",4]],0,a.vb,[l.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],fontWeight:[3,"fontWeight"]},null),(t()(),l.Jb(52,0,null,0,21,"GridRow",[["height","100%"],["width","100%"]],null,null,null,p.Bc,p.J)),l.Ib(53,4440064,null,0,a.B,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(54,0,null,0,3,"GridItem",[["height","100%"],["width","15%"]],null,null,null,p.Ac,p.I)),l.Ib(55,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(56,0,null,0,1,"SwtLabel",[["paddingRight","10"],["styleName","labelBold"]],null,null,null,p.Yc,p.fb)),l.Ib(57,4440064,[[15,4],["templateLabel",4]],0,a.vb,[l.r,a.i],{styleName:[0,"styleName"],paddingRight:[1,"paddingRight"]},null),(t()(),l.Jb(58,0,null,0,3,"GridItem",[["height","100%"],["width","21%"]],null,null,null,p.Ac,p.I)),l.Ib(59,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(60,0,null,0,1,"SwtComboBox",[["dataLabel","template"],["id","cbTemplate"],["shiftUp","40"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,n){var i=!0,a=t.component;"window:mousewheel"===e&&(i=!1!==l.Tb(t,61).mouseWeelEventHandler(n.target)&&i);"change"===e&&(i=!1!==a.changeComboTemplate(n)&&i);return i},p.Pc,p.W)),l.Ib(61,4440064,[[10,4],["cbTemplate",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},{change_:"change"}),(t()(),l.Jb(62,0,null,0,3,"GridItem",[["height","100%"],["width","31%"]],null,null,null,p.Ac,p.I)),l.Ib(63,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(64,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedTemplate"],["styleName","labelLeft"],["width","239"]],null,null,null,p.Yc,p.fb)),l.Ib(65,4440064,[[16,4],["selectedTemplate",4]],0,a.vb,[l.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],fontWeight:[3,"fontWeight"]},null),(t()(),l.Jb(66,0,null,0,3,"GridItem",[["height","100%"],["width","11%"]],null,null,null,p.Ac,p.I)),l.Ib(67,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(68,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnAdd"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.addTemplate()&&l);return l},p.Mc,p.T)),l.Ib(69,4440064,[[4,4],["btnAdd",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(70,0,null,0,3,"GridItem",[["height","100%"],["width","10%"]],null,null,null,p.Ac,p.I)),l.Ib(71,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(72,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnChange"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.changeTemplate()&&l);return l},p.Mc,p.T)),l.Ib(73,4440064,[[5,4],["btnChange",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(74,0,null,0,15,"SwtCanvas",[["height","20%"],["id","cvSaveOptions"],["marginTop","10"],["width","100%"]],null,null,null,p.Nc,p.U)),l.Ib(75,4440064,null,0,a.db,[l.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),l.Jb(76,0,null,0,13,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),l.Ib(77,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(78,0,null,0,5,"HBox",[["width","90%"]],null,null,null,p.Dc,p.K)),l.Ib(79,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(80,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnSave"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.updateData("no")&&l);return l},p.Mc,p.T)),l.Ib(81,4440064,[[3,4],["btnSave",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(82,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnCancel"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.closeHandler(n)&&l);return l},p.Mc,p.T)),l.Ib(83,4440064,[[2,4],["btnCancel",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(84,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","5%"]],null,null,null,p.Dc,p.K)),l.Ib(85,4440064,null,0,a.C,[l.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),l.Jb(86,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,n){var l=!0,i=t.component;"click"===e&&(l=!1!==i.doHelp(n)&&l);return l},p.Wc,p.db)),l.Ib(87,4440064,[[6,4],["helpIcon",4]],0,a.rb,[l.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(88,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),l.Ib(89,114688,[[7,4],["loadingImage",4]],0,a.xb,[l.r],null,null)],function(t,e){t(e,17,0,"100%","100%");t(e,19,0,"appContainer","100%","100%","20","20","20","20");t(e,21,0,"swtControlBar","100%","75%");t(e,23,0,"100%","100%","10","15");t(e,25,0,"100%","100%");t(e,27,0,"15%","100%","4");t(e,29,0,"labelBold","10");t(e,31,0,"21%","100%");t(e,33,0,"entity","135","cbEntity");t(e,35,0,"21%","100%");t(e,37,0,"selectedEntity","labelLeft","239","normal");t(e,39,0,"100%","100%");t(e,41,0,"15%","100%","2");t(e,43,0,"labelBold","10");t(e,45,0,"21%","100%");t(e,47,0,"currency","135","cbCurrency");t(e,49,0,"21%","100%");t(e,51,0,"selectedCurrency","labelLeft","239","normal");t(e,53,0,"100%","100%");t(e,55,0,"15%","100%");t(e,57,0,"labelBold","10");t(e,59,0,"21%","100%");t(e,61,0,"template","135","cbTemplate","40");t(e,63,0,"31%","100%");t(e,65,0,"selectedTemplate","labelLeft","239","normal");t(e,67,0,"11%","100%");t(e,69,0,"btnAdd","true");t(e,71,0,"10%","100%");t(e,73,0,"btnChange","true");t(e,75,0,"cvSaveOptions","100%","20%","10");t(e,77,0,"100%");t(e,79,0,"90%");t(e,81,0,"btnSave","true");t(e,83,0,"btnCancel","true");t(e,85,0,"right","5%");t(e,87,0,"helpIcon"),t(e,89,0)},null)}function U(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-template-options",[],null,null,null,F,G)),l.Ib(1,4440064,null,0,c,[a.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var Z=l.Fb("app-template-options",c,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);