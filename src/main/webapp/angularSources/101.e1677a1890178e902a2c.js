(window.webpackJsonp=window.webpackJsonp||[]).push([[101],{eqgG:function(t,e,l){"use strict";l.r(e);var i=l("CcnG"),n=l("mrSG"),u=l("447K"),a=l("ZYCi"),r=l("wd/R"),o=l.n(r),h=function(t){function e(e,l){var i=t.call(this,l,e)||this;return i.commonService=e,i.element=l,i.ALL_STRING="All",i.ASCII_RESTRICT_PATTERN="A-Za-z0-9d_ !\"#$%&'()*+,-./:;<=>?@[\\]^`{|}~",i.ACCOUNT_NAME_RESTRICT_PATTERN="a-zA-Z0-9d .,:;#()*?[]%>_+=^|\\+/",i.jsonReader=new u.L,i.jsonReader2=new u.L,i.waitingRefreshLeft=!1,i.inputData=new u.G(i.commonService),i.saveData=new u.G(i.commonService),i.baseURL=u.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.accountInGroupToDelete=[],i.accountInGroupToAdd=[],i.accountAlreadyInGroup=[],i.methodName=null,i.entityId=null,i.currencyCode=null,i.alreadyOpened=!1,i.description=null,i.groupDefaultName=null,i.filterConditionText=null,i.leftTextOfInput="",i.rightTextOfInput="",i.allowReportingOnLoad=!1,i.isChangedQuickSearchLeft=!1,i.isChangedQuickSearchRight=!1,i.waitingRefresh=!1,i.swtAlert=new u.bb(e),i}return n.d(e,t),e.prototype.ngOnInit=function(){this.leftGrid=this.otherGridCanvas.addChild(u.hb),this.rightGrid=this.ownGridCanvas.addChild(u.hb),this.accountInThisGroupLabel.text=u.x.call("getBundle","text","label-accntInGrp","Accounts in this group"),this.accountNotInThisGroupLabel.text=u.x.call("getBundle","text","label-accntNotInGrp","Accounts not in this group"),this.saveButton.toolTip=u.x.call("getBundle","text","button-save","Save"),this.cancelButton.toolTip=u.x.call("getBundle","text","button-cancel","Save"),this.testButton.toolTip=u.x.call("getBundle","text","button-test","Save"),this.secondGridGroupsCombo.toolTip=u.x.call("getBundle","tip","label-listFromGrp","Select an account group"),this.leftGridQuickSearch.toolTip=u.x.call("getBundle","tip","label-quickSearch","Quick Search an account id"),this.filterCondition.toolTip=u.x.call("getBundle","tip","label-filterCondition","Specify a filter condition to identify the accounts which are to be members of a dynamic account"),this.netMaximum.toolTip=u.x.call("getBundle","tip","label-Max","Maximum net cumulative positions threshold (must be positive)"),this.netMinimum.toolTip=u.x.call("getBundle","tip","label-Min","Minimum net cumulative positions threshold (must be negative)"),this.secondMaximum.toolTip=u.x.call("getBundle","tip","label-secondMax",""),this.secondMinimum.toolTip=u.x.call("getBundle","tip","label-secondMin","Enter Second Minimum"),this.firstMaximum.toolTip=u.x.call("getBundle","tip","label-firstMax","Enter First Maximum"),this.firstMinimum.toolTip=u.x.call("getBundle","tip","label-firstMin","Enter First Minimum"),this.mainAgentText.toolTip=u.x.call("getBundle","tip","label-mainAgent","Enter main agent"),this.nameLegend.toolTip=u.x.call("getBundle","tip","label-legendText","Define default legend text for the liquidity monitor screen"),this.idLegend.toolTip=u.x.call("getBundle","tip","label-legendText","Define default legend text for the liquidity monitor screen"),this.groupDescription.toolTip=u.x.call("getBundle","tip","label-description","Enter Description"),this.groupName.toolTip=u.x.call("getBundle","tip","label-name","Enter group name"),this.correspondentBankCheckBox.toolTip=u.x.call("getBundle","tip","label-correspBank","When checked, this group will be selectable as a correspondent bank for the purpose of Basel reporting"),this.collectNetCumPosCheckBox.toolTip=u.x.call("getBundle","tip","label-netCum","When checked, the ILM group report will include a chart of net cumulative position..  This data could grow to be large so only specify it on accounts where the feature is necessary.  Data will be stored with 15 minute timeslot intervals"),this.allowReportingCheckBox.toolTip=u.x.call("getBundle","tip","label-allowReporting","When checked this group will have ILM report data built on a daily basis and will be selectable for ILM reporting"),this.typeDynamic.toolTip=u.x.call("getBundle","tip","label-type","Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table"),this.typeFixed.toolTip=u.x.call("getBundle","tip","label-type","Specify if the group will contain a fixed list of accounts, or has dynamic content determined by selection filters applied to the account table"),this.publicRadio.toolTip=this.privateRadio.toolTip=u.x.call("getBundle","tip","label-pubPriv","Specify if group is public and available to all users or private and only available to the creator of the group"),this.groupIdTextInput.toolTip=u.x.call("getBundle","tip","label-groupId","Enter a group Id"),this.ccyCombo.toolTip=u.x.call("getBundle","tip","label-currency","Select currency code"),this.entityCombo.toolTip=u.x.call("getBundle","tip","label-entity","Select an entity ID")},e.prototype.onLoad=function(){var t=this;switch(this.leftGrid.onFilterChanged=this.updateFilter.bind(this),this.rightGrid.onFilterChanged=this.updateFilterRight.bind(this),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.selectedAccountGroup=u.x.call("eval","selectedAccountGroup"),this.entityId=u.x.call("eval","entityId"),this.currencyCode=u.x.call("eval","currencyCode"),this.description=u.t.decode64(u.x.call("eval","description")),this.groupDefaultName=u.x.call("eval","groupDefaultName"),this.filterConditionText=u.t.decode64(u.x.call("eval","filter")),this.methodName=u.x.call("eval","methodName"),this.typeDynamic.enabled=!1,this.typeFixed.enabled=!1,this.publicRadio.enabled=!1,this.privateRadio.enabled=!1,this.idLegend.enabled=!1,this.nameLegend.enabled=!1,this.disableMoveButtons(),this.methodName){case"view":this.dataExport.enabled=!0;break;case"add":this.createdByLabelText.text=u.x.call("eval","createdBy"),this.createdOnLabelText.text=u.x.call("eval","createdOn"),this.dataExport.enabled=!1;break;case"change":this.dataExport.enabled=!1;break;case"addfromILM":this.createdByLabelText.text=u.x.call("eval","createdBy"),this.createdOnLabelText.text=u.x.call("eval","createdOn"),this.dataExport.enabled=!1}this.actionPath="intraDayLiquidity.do?",this.actionMethod="",this.rightGrid.onRowClick=function(e){t.cellLogic(e)},this.leftGrid.onRowClick=function(e){t.cellLogic(e)},this.actionMethod="method=getAccountGroupDetails",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.selectedAccountGroup=this.selectedAccountGroup,this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.description=this.description,this.requestParams.groupDefaultName=this.groupDefaultName,this.groupName.restrict=this.ASCII_RESTRICT_PATTERN,this.inputData.send(this.requestParams),u.v.subscribe(function(e){t.report(e)})},e.prototype.inputDataFault=function(){this.swtAlert.error("alert.generic_exception")},e.prototype.cellLogic=function(t){"view"!=this.methodName&&this.enableMoveButtons()},e.prototype.formatAmount=function(t){var e=this,l=null,i=null,n=u.x.call("eval","ccyPattern");try{null!=(l=t.text.toString())&&""!=l&&(l=u.Z.unformatAmount(l,Number(n)),"NaN"!=(i=u.Z.formatAmount(l,Number(2),Number(n)))?(t.text=i,null==l&&""==l||""!=i?t.id==this.netMinimum.id&&-1==i.toString().indexOf("-")?this.swtAlert.warning(u.x.call("getBundle","text","alert-netMinimum","The minimum net cumulative positions threshold must be negative"),u.x.call("getBundle","text","alert-warning","Warning"),u.c.OK,null,function(l){e.setFocusField(l,t)}):t.id==this.netMaximum.id&&-1!=i.toString().indexOf("-")&&this.swtAlert.warning(u.x.call("getBundle","text","alert-netMaximum","The maximum net cumulative positions threshold must be positive"),u.x.call("getBundle","text","alert-warning","Warning"),u.c.OK,null,function(l){e.setFocusField(l,t)}):this.swtAlert.warning(u.x.call("getBundle","text","label-invalidAmountAlert","Please enter a valid amount"),u.x.call("getBundle","text","alert-warning","Warning"),u.c.OK,null,function(l){e.setFocusField(l,t)})):(t.text="",this.swtAlert.warning(u.x.call("getBundle","text","label-invalidAmountAlert","Please enter a valid amount"),u.x.call("getBundle","text","alert-warning","Warning"),u.c.OK,null,function(l){e.setFocusField(l,t)})))}catch(a){this.swtAlert.warning(u.x.call("getBundle","text","label-invalidAmountAlert","Please enter a valid amount"),u.x.call("getBundle","text","alert-warning","Warning"))}},e.prototype.setFocusField=function(t,e){t.detail==u.c.OK&&e.setFocusAndSelect()},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},e.prototype.saveDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),"Data fetch OK"==this.jsonReader.getRequestReplyMessage()?(-1!=this.groupDefaultName.indexOf("Central")&&u.x.call("window.opener.setStoredParam","CentralGroupId",this.groupIdTextInput.text),u.x.call("refreshParent"),u.x.call("close")):"errors.DataIntegrityViolationExceptioninAdd"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.warning(u.x.call("getBundle","text","label-recordExistsAlert","Record already exists"),u.x.call("getBundle","text","alert-warning","Warning")):this.swtAlert.warning(u.x.call("getBundle","text","label-syntaxAlert","Query syntax is not correct, Please verfiy your query :")+this.jsonReader.getRequestReplyMessage(),u.x.call("getBundle","text","label-syntaxAlertTitle","Filter condition error")))},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&!this.jsonReader.isDataBuilding()){this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedValue,this.ccyCombo.setComboData(this.jsonReader.getSelects()),this.selectedCcy.text=this.ccyCombo.selectedValue,this.groupIdTextInput.text=this.selectedAccountGroup;var e=this.lastRecievedJSON.ilmaccountgroupdetails.grid.rows.row;if(e)for(var l=0;l<e.length;l++)this.accountAlreadyInGroup.push(e[l].account_id.content);var i={columns:this.jsonReader.getColumnData()};this.rightGrid.CustomGrid(i),this.rightGrid.gridData=this.jsonReader.getGridData(),this.rightGrid.setRowSize=this.jsonReader.getRowSize(),this.alreadyOpened||(this.groupName.text=this.jsonReader.getSingletons().groupname,this.groupDescription.text=this.jsonReader.getSingletons().groupdescription,this.firstMinimum.text=this.jsonReader.getSingletons().firstminimum,this.formatAmount(this.firstMinimum),this.firstMaximum.text=this.jsonReader.getSingletons().firstmaximum,this.formatAmount(this.firstMaximum),this.secondMinimum.text=this.jsonReader.getSingletons().secondminimum,this.formatAmount(this.secondMinimum),this.secondMaximum.text=this.jsonReader.getSingletons().secondmaximum,this.mainAgentText.text=this.jsonReader.getSingletons().mainAgent,this.formatAmount(this.secondMaximum),this.filterCondition.text=this.jsonReader.getSingletons().filtercondition,this.netMinimum.text=this.jsonReader.getSingletons().minNcpThreshold,this.formatAmount(this.netMinimum),this.netMaximum.text=this.jsonReader.getSingletons().maxNcpThreshold,this.formatAmount(this.netMaximum),this.privateRadio.enabled=u.x.call("eval","maintainAnyGroup"),this.publicRadio.enabled=u.x.call("eval","maintainAnyGroup"),this.typeDynamic.enabled=!0,this.typeFixed.enabled=!0,this.idLegend.enabled=!0,this.nameLegend.enabled=!0,this.allowReportingCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!1,this.groupIdTextInput.required=!0,this.startTime.text=this.jsonReader.getSingletons().thresh1Time,this.endTime.text=this.jsonReader.getSingletons().thresh2Time,this.startTarget.text=this.jsonReader.getSingletons().thresh1Percent,this.endTarget.text=this.jsonReader.getSingletons().thresh2Percent,"add"!=this.methodName&&"addfromILM"!=this.methodName&&(this.createdByLabelText.text=this.jsonReader.getSingletons().createdByUser,this.createdOnLabelText.text=this.jsonReader.getSingletons().createDate),this.jsonReader.getSingletons().grouptype&&(this.type.selectedValue=this.jsonReader.getSingletons().grouptype),this.jsonReader.getSingletons().publicprivate&&(this.publicprivate.selectedValue=this.jsonReader.getSingletons().publicprivate),this.jsonReader.getSingletons().idname&&(this.idName.selectedValue=this.jsonReader.getSingletons().idname),this.allowReportingCheckBox.selected="Y"==this.jsonReader.getSingletons().allowReporting,this.collectNetCumPosCheckBox.selected="Y"==this.jsonReader.getSingletons().collectNetCumPos,this.correspondentBankCheckBox.selected="Y"==this.jsonReader.getSingletons().correspondentBank,this.throughputCheckBox.selected="Y"==this.jsonReader.getSingletons().createThroughputRatio,this.accountInThisGroupTextLabel.text=" ("+this.rightGrid.dataProvider.length+")","addfromILM"==this.methodName?(this.groupDescription.text=this.description,this.type.selectedValue="D",this.groupName.text=this.groupDefaultName,this.filterCondition.enabled=!0,this.filterCondition.text=this.filterConditionText,this.publicprivate.selectedValue="Public",this.publicRadio.enabled=!1,this.privateRadio.enabled=!1,this.allowReportingCheckBox.enabled=!1,this.allowReportingCheckBox.selected=!0,this.testButton.enabled=!0,this.nameLegend.selected=!0,this.idLegend.selected=!1):"change"==this.methodName?(this.entityCombo.enabled=!1,this.ccyCombo.enabled=!1,this.groupIdTextInput.enabled=!1,this.type.enabled=!1,1==this.jsonReader.getSingletons().maintainAnyGroup&&"Y"!=this.jsonReader.getSingletons().global&&1!=this.jsonReader.getSingletons().central?this.publicprivate.enabled=!0:this.publicprivate.enabled=!1,"D"==this.type.selectedValue?(this.filterCondition.enabled=!0,this.ownGridCanvas.enabled=!1,this.otherGridCanvas.enabled=!0,this.secondGridGroupsCombo.enabled=!1,this.disableMoveButtons(),this.rightGridQuickSearch.enabled=!1,this.leftGridQuickSearch.enabled=!0,this.testButton.enabled=!0,this.allowReportingCheckBox.selected?(this.correspondentBankCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!0):(this.correspondentBankCheckBox.enabled=!1,this.collectNetCumPosCheckBox.enabled=!1,this.collectNetCumPosCheckBox.selected=!1)):(this.ownGridCanvas.enabled=!0,this.otherGridCanvas.enabled=!0,this.secondGridGroupsCombo.enabled=!0,this.rightGridQuickSearch.enabled=!0,this.leftGridQuickSearch.enabled=!0,this.filterCondition.enabled=!1,this.allowReportingCheckBox.selected?(this.correspondentBankCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!0):(this.correspondentBankCheckBox.enabled=!1,this.collectNetCumPosCheckBox.enabled=!1,this.collectNetCumPosCheckBox.selected=!1))):"view"==this.methodName?(this.entityCombo.enabled=!1,this.ccyCombo.enabled=!1,this.groupIdTextInput.enabled=!1,this.type.enabled=!1,this.publicprivate.enabled=!1,this.groupDescription.enabled=!1,this.idName.enabled=!1,this.groupName.enabled=!1,this.firstMaximum.enabled=!1,this.firstMinimum.enabled=!1,this.secondMaximum.enabled=!1,this.secondMinimum.enabled=!1,this.netMaximum.enabled=!1,this.netMinimum.enabled=!1,this.filterCondition.enabled=!1,this.mainAgentText.enabled=!1,this.disableMoveButtons(),this.throughputCheckBox.enabled=!1,this.startTarget.enabled=!1,this.endTarget.enabled=!1,this.startTime.enabled=!1,this.endTime.enabled=!1):(this.publicprivate.enabled=this.jsonReader.getSingletons().maintainAnyGroup,this.type.enabled=!0,this.secondGridGroupsCombo.enabled=!0,this.rightGridQuickSearch.enabled=!0,this.leftGridQuickSearch.enabled=!0,this.filterCondition.enabled=!1,this.groupIdTextInput.setFocus()))}this.allowReportingOnLoad=this.allowReportingCheckBox.selected,this.showSecondGroupGrid()},e.prototype.entityChangeCombo=function(){this.updateData()},e.prototype.leftGridGroupComboChange=function(){var t=this;this.inputData.cbResult=function(e){t.inputDataSecondGridResult(e)},this.actionMethod="method=getAccountListDetails",this.leftGridQuickSearch.text="",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.selectedAccountGroup=this.selectedAccountGroup,this.requestParams.secondGridAccountGroup=this.secondGridGroupsCombo.selectedValue,this.inputData.send(this.requestParams),"view"==this.methodName?this.disableMoveButtons():this.enableMoveButtons()},e.prototype.enableInterface=function(){this.typeDynamic.enabled=!0,this.typeFixed.enabled=!0,this.idLegend.enabled=!0,this.nameLegend.enabled=!0,"view"!=this.methodName&&(this.allowReportingCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!0,this.allowReportingCheckBox.selected?(this.correspondentBankCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!0):(this.correspondentBankCheckBox.enabled=!1,this.collectNetCumPosCheckBox.enabled=!1,this.collectNetCumPosCheckBox.selected=!1),this.idLegend.enabled=!0,this.nameLegend.enabled=!0),this.rightGridQuickSearch.enabled=!0,this.leftGridQuickSearch.enabled=!0,this.saveButton.enabled=!0,"add"==this.methodName&&(this.ccyCombo.enabled=!0,this.entityCombo.enabled=!0),this.secondGridGroupsCombo.enabled=!0},e.prototype.disableInterface=function(){this.saveButton.enabled=!1,this.ccyCombo.enabled=!1,this.entityCombo.enabled=!1,this.secondGridGroupsCombo.enabled=!1,this.typeDynamic.enabled=!1,this.typeFixed.enabled=!1,this.idLegend.enabled=!1,this.nameLegend.enabled=!1,this.allowReportingCheckBox.enabled=!1,this.collectNetCumPosCheckBox.enabled=!1,this.correspondentBankCheckBox.enabled=!1,this.rightGridQuickSearch.enabled=!1,this.leftGridQuickSearch.enabled=!1},e.prototype.moveToRight=function(t,e){var l=this;this.leftGrid.dataviewObj.beginUpdate(),this.rightGrid.dataviewObj.beginUpdate();var i,n=this.rightGrid.dataset.slice(),u=this.leftGrid.getFilteredItems().slice();try{if(e){i=u.length;for(var a=[],r=0;r<u.length;r++)n.push(u[r]),a.push(this.leftGrid.angularGridInstance.gridService.getDataItemByRowNumber(r));this.leftGrid.angularGridInstance.gridService.deleteItems(a);for(r=0;r<n.length;r++)n[r].id=r;this.rightGrid.gridData={row:n,size:n.length}}else{a=[];i=this.leftGrid.selectedIndices.length,a.push(this.leftGrid.angularGridInstance.gridService.getDataItemByRowNumber(this.leftGrid.selectedIndex)),this.leftGrid.angularGridInstance.gridService.deleteItems(a),n.push(this.leftGrid.selectedItem);for(r=0;r<n.length;r++)n[r].id=r;this.rightGrid.gridData={row:n,size:n.length}}var o=Number(this.accountInThisGroupTextLabel.text.replace("(","").replace(")",""));this.accountInThisGroupTextLabel.text="("+(o+i)+")";var h=Number(this.accountNotInThisGroupTextLabel.text.replace("(","").replace(")",""));this.accountNotInThisGroupTextLabel.text="("+(h-i)+")",this.leftGrid.selectedIndex=-1}catch(d){console.log("erorrrr--------",d)}this.leftGrid.dataviewObj.endUpdate(),this.rightGrid.dataviewObj.endUpdate(),this.enableMoveButtons(),this.waitingRefreshLeft||(this.waitingRefreshLeft=!0,setTimeout(function(){l.refreshLeftHeader()},2e3))},e.prototype.moveToLeft=function(t,e){var l=this;this.leftGrid.dataviewObj.beginUpdate(),this.rightGrid.dataviewObj.beginUpdate();var i,n=this.rightGrid.getFilteredItems().slice(),u=this.leftGrid.dataset.slice();try{if(e){var a=[];i=n.length;for(var r=0;r<n.length;r++)u.push(n[r]),a.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber(r));this.rightGrid.angularGridInstance.gridService.deleteItems(a);for(r=0;r<u.length;r++)u[r].id=r;this.leftGrid.gridData={row:u,size:u.length}}else{a=[];i=this.rightGrid.selectedIndices.length,a.push(this.rightGrid.angularGridInstance.gridService.getDataItemByRowNumber(this.rightGrid.selectedIndex)),this.rightGrid.angularGridInstance.gridService.deleteItems(a),u.push(this.rightGrid.selectedItem);for(r=0;r<u.length;r++)u[r].id=r;this.leftGrid.gridData={row:u,size:u.length}}var o=Number(this.accountInThisGroupTextLabel.text.replace("(","").replace(")",""));this.accountInThisGroupTextLabel.text="("+(o-i)+")";var h=Number(this.accountNotInThisGroupTextLabel.text.replace("(","").replace(")",""));this.accountNotInThisGroupTextLabel.text="("+(h+i)+")",this.rightGrid.selectedIndex=-1}catch(d){console.log("error in Move left",d)}this.leftGrid.dataviewObj.endUpdate(),this.rightGrid.dataviewObj.endUpdate(),this.enableMoveButtons(),this.waitingRefresh||(this.waitingRefresh=!0,setTimeout(function(){l.refreshRightHeader()},2e3))},e.prototype.updateCountGroupMember=function(){null!=this.rightGrid.dataProvider&&(this.accountInThisGroupTextLabel.text=" ("+this.rightGrid.getFilteredData().length+")"),null!=this.leftGrid.dataProvider&&(this.accountNotInThisGroupTextLabel.text=" ("+this.leftGrid.getFilteredData().length+")")},e.prototype.accountsAddDelete=function(){var t=[],e=[];if(this.rightGrid.dataProvider&&this.rightGrid.originalDataprovider){for(var l=0;l<this.rightGrid.dataProvider.length;l++)e.push(this.rightGrid.dataProvider[l].account_id);for(l=0;l<this.rightGrid.originalDataprovider.length;l++)t.push(this.rightGrid.originalDataprovider[l].account_id);if("add"==this.methodName||"addfromILM"==this.methodName)for(l=0;l<this.rightGrid.dataProvider.length;l++)this.accountInGroupToAdd.push(this.rightGrid.dataProvider[l].account_id);else{for(l=0;l<this.rightGrid.dataProvider.length;l++)-1==t.indexOf(this.rightGrid.dataProvider[l].account_id)&&this.accountInGroupToAdd.push(this.rightGrid.dataProvider[l].account_id);for(l=0;l<this.rightGrid.originalDataprovider.length;l++)-1==e.indexOf(this.rightGrid.originalDataprovider[l].account_id)&&this.accountInGroupToDelete.push(this.rightGrid.originalDataprovider[l].account_id)}}},e.prototype.showSecondGroupGrid=function(){var t=this;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataSecondGridResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="intraDayLiquidity.do?",this.actionMethod="",this.rightGrid.onRowClick=function(e){t.cellLogic(e)},this.leftGrid.onRowClick=function(e){t.cellLogic(e)},this.filterCondition.text.length>0?(this.entityId=this.entityCombo.selectedLabel,this.currencyCode=this.ccyCombo.selectedLabel,this.actionMethod="method=getFilterConditionTestResult",this.requestParams.query=this.filterCondition.text,this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode):(this.actionMethod="method=getAccountListDetails",this.requestParams.selectedAccountGroup=this.selectedAccountGroup,"add"!=this.methodName&&"addfromILM"!=this.methodName||(this.requestParams.entiyId=this.entityCombo.selectedLabel,this.requestParams.currencyCode=this.ccyCombo.selectedLabel)),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.inputDataSecondGridResult=function(t){var e=this;if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON2=t,this.jsonReader2.setInputJSON(this.lastRecievedJSON2),this.jsonReader2.getRequestReplyStatus()){if(this.lastRecievedJSON2!=this.prevRecievedJSON2&&!this.jsonReader2.isDataBuilding())if(String(this.jsonReader2.getSingletons().exceptioninquery).length>0)this.swtAlert.warning(u.x.call("getBundle","text","label-syntaxAlert","Query syntax is not correct, Please verfiy your query :")+this.jsonReader2.getSingletons().exceptioninquery,u.x.call("getBundle","text","label-syntaxAlertTitle","Filter condition error"));else if("D"==this.type.selectedValue&&""!=this.filterCondition.text){var l={columns:this.jsonReader2.getColumnData()};this.leftGrid.CustomGrid(l),this.rightGrid.CustomGrid(l),this.rightGrid.gridData=this.jsonReader2.getGridData(),this.rightGrid.setRowSize=this.jsonReader2.getRowSize(),this.accountInThisGroupTextLabel.text="("+this.rightGrid.dataProvider.length+")",this.otherGridCanvas.enabled=!1,this.ownGridCanvas.enabled=!0,this.secondGridGroupsCombo.enabled=!1,this.rightGridQuickSearch.enabled=!0,this.leftGridQuickSearch.enabled=!1}else{this.secondGridGroupsCombo.setComboData(this.jsonReader2.getSelects(),!0);l={columns:this.jsonReader2.getColumnData()};this.leftGrid.CustomGrid(l),this.leftGrid.gridData=this.jsonReader2.getGridData(),this.enableMoveButtons(),this.leftGrid.setRowSize=this.jsonReader2.getRowSize(),"add"!=this.methodName&&(this.leftGrid.dataviewObj.beginUpdate(),setTimeout(function(){for(var t=e.rightGrid.getFilteredItems().slice(),l=function(l){var i=e.leftGrid.getFilteredItems().find(function(e){return e.account_id==t[l].account_id});i&&e.leftGrid.angularGridInstance.gridService.deleteItem(i)},i=0;i<t.length;i++)l(i)},0),this.leftGrid.dataviewObj.endUpdate(),this.refreshLeftHeader()),this.accountNotInThisGroupTextLabel.text=" ("+this.leftGrid.dataProvider.length+")"}}else this.swtAlert.warning(u.x.call("getBundle","text","label-syntaxAlert","Query syntax is not correct, Please verfiy your query :"),u.x.call("getBundle","text","alert-warning","Warning"));"view"==this.methodName&&(this.disableMoveButtons(),this.saveButton.enabled=!1)},e.prototype.updateData=function(){var t=this;this.alreadyOpened=!0,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.selectedAccountGroup=this.groupIdTextInput.text,this.entityId=this.entityCombo.selectedLabel,this.currencyCode=this.ccyCombo.selectedLabel,this.methodName=u.x.call("eval","methodName"),this.actionPath="intraDayLiquidity.do?",this.actionMethod="method=getAccountGroupDetails",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.selectedAccountGroup=this.selectedAccountGroup,this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.inputData.send(this.requestParams)},e.prototype.saveButton_clickHandler=function(t){var e=this;try{if(this.groupIdTextInput.text.length>0){if(!this.validateGroupId())return;if(!this.isTimeValid())return;this.saveData.cbStart=this.startOfComms.bind(this),this.saveData.cbStop=this.endOfComms.bind(this),this.saveData.cbResult=function(t){e.saveDataResult(t)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.selectedAccountGroup=u.x.call("eval","selectedAccountGroup"),this.actionPath="intraDayLiquidity.do?","add"==this.methodName?this.actionMethod="method=saveAccountGroupdetails":"change"!=this.methodName&&"addfromILM"!=this.methodName||(this.actionMethod="method=updateAccountGroupdetails"),this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,"add"!=this.methodName&&"addfromILM"!=this.methodName||(this.selectedAccountGroup=this.groupIdTextInput.text),this.requestParams.fromFlex="true",this.requestParams.selectedAccountGroup=this.selectedAccountGroup,this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.currencyCode=this.ccyCombo.selectedLabel,this.requestParams.privatePublic=this.publicprivate.selectedValue,this.requestParams.type=this.type.selectedValue,this.requestParams.name=this.groupName.text,this.requestParams.mainAgentText=this.mainAgentText.text,this.requestParams.description=this.groupDescription.text,this.requestParams.defaultLegendText=this.idName.selectedValue,this.requestParams.allowReporting=this.allowReportingCheckBox.selected?"Y":"N",this.requestParams.collectNetCumPos=this.collectNetCumPosCheckBox.selected?"Y":"N",this.requestParams.correspondentBank=this.correspondentBankCheckBox.selected?"Y":"N",this.requestParams.throughputRatio=this.throughputCheckBox.selected?"Y":"N",this.requestParams.thresh1Time=this.startTime.text,this.requestParams.thresh2Time=this.endTime.text,this.requestParams.thresh1Percent=this.startTarget.text,this.requestParams.thresh2Percent=this.endTarget.text;var l=u.x.call("eval","ccyPattern");if(this.firstMinimum.text=u.Z.unformatAmount(this.firstMinimum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.firstMinimum=this.firstMinimum.text,this.secondMinimum.text=u.Z.unformatAmount(this.secondMinimum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.secondMinimum=this.secondMinimum.text,this.firstMaximum.text=u.Z.unformatAmount(this.firstMaximum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.firstMaximum=this.firstMaximum.text,this.secondMaximum.text=u.Z.unformatAmount(this.secondMaximum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.secondMaximum=this.secondMaximum.text,this.netMinimum.text=u.Z.unformatAmount(this.netMinimum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.minNcpThreshold=this.netMinimum.text,this.netMaximum.text=u.Z.unformatAmount(this.netMaximum.text,Number(l)).replace(".00","").replace(",00",""),this.requestParams.maxNcpThreshold=this.netMaximum.text,this.requestParams.filterCondition=this.filterCondition.text,this.typeFixed.selected&&(this.accountsAddDelete(),this.requestParams.accountsToDelete=this.accountInGroupToDelete.toString(),this.requestParams.accountsToAdd=this.accountInGroupToAdd.toString()),this.allowReportingCheckBox.selected){if("change"==this.methodName&&this.allowReportingOnLoad)return void this.saveData.send(this.requestParams);this.swtAlert.warning(u.Wb.getPredictMessage("ilmScenario.alert.allowReporting",null),"Warning",u.c.OK|u.c.CANCEL,null,this.okHandler.bind(this))}else this.saveData.send(this.requestParams)}else this.swtAlert.warning(u.x.call("getBundle","text","label-acctGrpRequired","Account Group Id is required"),u.x.call("getBundle","text","alert-warning","Warning"))}catch(i){console.log("error",i)}},e.prototype.okHandler=function(t){t.detail==u.c.OK&&this.saveData.send(this.requestParams)},e.prototype.export=function(t){u.x.call("onExport",this.groupIdTextInput.text,t.data.toString())},e.prototype.enableMoveButtons=function(){1==this.ownGridCanvas.enabled&&1==this.otherGridCanvas.enabled&&(this.buttonsContainer.enabled=!0,this.leftGrid.getFilteredData().length>0||this.leftGrid.getFilteredItems().length>0?(this.buttonMoveRight.enabled=this.leftGrid.selectedIndex>=0,this.buttonMoveAllRight.enabled=!0,this.buttonMoveLeft.enabled=!1,this.buttonMoveAllLeft.enabled=!1):(this.buttonMoveAllRight.enabled=!1,this.buttonMoveRight.enabled=!1),(this.rightGrid.getFilteredData().length>0||this.rightGrid.getFilteredItems().length>0)&&(this.buttonMoveLeft.enabled=this.rightGrid.selectedIndex>=0,this.buttonMoveAllLeft.enabled=!0))},e.prototype.disableMoveButtons=function(){this.buttonMoveRight.enabled=!1,this.buttonMoveAllRight.enabled=!1,this.buttonMoveLeft.enabled=!1,this.buttonMoveAllLeft.enabled=!1,this.buttonsContainer.enabled=!1},e.prototype.enableAllButtons=function(){this.leftGrid.selectedIndex},e.prototype.filterConditionValidation=function(){var t=this;this.filterCondition.text.length>0&&(this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataSecondGridResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.entityId=this.entityCombo.selectedLabel,this.currencyCode=this.ccyCombo.selectedLabel,this.actionMethod="method=getFilterConditionTestResult",this.requestParams.query=this.filterCondition.text,this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.filtringLeftGrid=function(t){try{this.isChangedQuickSearchLeft=!0,this.leftTextOfInput=t,this.updateFilter()}catch(e){console.log("error",e)}},e.prototype.updateFilter=function(){try{this.leftGrid.dataviewObj.beginUpdate(),this.leftGrid.dataviewObj.setItems(this.leftGrid.gridData),this.leftGrid.dataviewObj.setFilterArgs({searchString:this.leftTextOfInput,currentFilter:this.leftGrid.getFilteredGridColumns()}),this.leftGrid.dataviewObj.setFilter(this.filterFunction),this.leftGrid.dataviewObj.endUpdate(),this.leftGrid.dataviewObj.refresh(),"view"!=this.methodName&&this.enableMoveButtons(),this.updateCountGroupMember()}catch(t){console.log("error in update",t)}},e.prototype.refreshLeftHeader=function(){this.isChangedQuickSearchLeft&&(this.leftGrid.refrehHeaderFiters(),this.waitingRefreshLeft=!1)},e.prototype.refreshRightHeader=function(){this.isChangedQuickSearchRight&&(this.rightGrid.refrehHeaderFiters(),this.waitingRefresh=!1)},e.prototype.filtringRightGrid=function(t){try{this.isChangedQuickSearchRight=!0,this.rightTextOfInput=t,this.updateFilterRight()}catch(e){console.log("error",e)}},e.prototype.updateFilterRight=function(){this.rightGrid.dataviewObj.beginUpdate(),this.rightGrid.dataviewObj.setItems(this.rightGrid.gridData),this.rightGrid.dataviewObj.setFilterArgs({searchString:this.rightTextOfInput,currentFilter:this.rightGrid.getFilteredGridColumns()}),this.rightGrid.dataviewObj.setFilter(this.filterFunction),this.rightGrid.dataviewObj.endUpdate(),this.rightGrid.dataviewObj.refresh(),this.updateCountGroupMember(),"view"!=this.methodName&&this.enableMoveButtons()},e.prototype.filterFunction=function(t,e){var l=e.currentFilter.split("@||@");if(e.currentFilter)return"All"!=l[0]?t.account_id_name===l[0]&&-1!=t.account_id_name.toLowerCase().indexOf(e.searchString.toLowerCase())&&(("All"==l[1]||t.type===l[1])&&(("All"==l[2]||t.class===l[2])&&("All"==l[3]||t.level===l[3]))):("All"==l[1]||t.type===l[1])&&(("All"==l[2]||t.class===l[2])&&(("All"==l[3]||t.level===l[3])&&-1!=t.account_id_name.toLowerCase().indexOf(e.searchString.toLowerCase())));try{return-1!=t.account_id_name.toLowerCase().indexOf(e.searchString.toLowerCase())}catch(i){console.log("ee",i)}},e.prototype.enableDisableRNCPC_CorrBank=function(t){this.allowReportingCheckBox.selected?(this.correspondentBankCheckBox.enabled=!0,this.collectNetCumPosCheckBox.enabled=!0):(this.correspondentBankCheckBox.enabled=!1,this.collectNetCumPosCheckBox.enabled=!1,this.collectNetCumPosCheckBox.selected=!1,this.correspondentBankCheckBox.selected=!1)},e.prototype.closeHandler=function(){u.x.call("close")},e.prototype.validateGroupId=function(){return!!/^[a-zA-Z0-9\-_]+$/.test(this.groupIdTextInput.text)||(this.swtAlert.confirm("Please enter a valid alphanumeric value, the underscore and the hypen characters are allowed","Alert",u.c.OK,null),!1)},e.prototype.radioTypeChangeHandler=function(){"D"==this.type.selectedValue?(this.filterCondition.enabled=!0,this.ownGridCanvas.enabled=!1,this.otherGridCanvas.enabled=!1,this.secondGridGroupsCombo.enabled=!1,this.disableMoveButtons(),this.leftGridQuickSearch.enabled=!1,this.leftGrid.dataProvider=[],this.rightGrid.dataProvider=[],this.testButton.enabled=!0,this.accountNotInThisGroupTextLabel.text=" ("+this.leftGrid.dataProvider.length+")"):(this.filterCondition.text="",this.ownGridCanvas.enabled=!0,this.otherGridCanvas.enabled=!0,this.secondGridGroupsCombo.enabled=!0,this.buttonsContainer.enabled=!0,this.rightGridQuickSearch.enabled=!0,this.leftGridQuickSearch.enabled=!0,this.filterCondition.enabled=!1,this.testButton.enabled=!1,this.rightGrid.dataProvider=[],this.accountInThisGroupTextLabel.text=" ("+this.rightGrid.dataProvider.length+")",this.showSecondGroupGrid())},e.prototype.doHelp=function(){u.x.call("help")},e.prototype.report=function(t){u.x.call("onExport",this.groupIdTextInput.text,t.toString())},e.prototype.validateTime=function(t){var e=u.Wb.getPredictMessage("alert.validTime",null);return t.text.endsWith(":")&&(t.text=t.text+"00"),t.text&&0==validateFormatTime(t)?(this.swtAlert.warning(e,null),t.text="",!1):(t.text=t.text.substring(0,5),this.isTimeValid(),!0)},e.prototype.isTimeValid=function(){var t,e;return this.startTime.text&&(t=o()(this.startTime.text,"HH:mm")),this.endTime.text&&(e=o()(this.endTime.text,"HH:mm")),void 0===t||void 0===e||!e.isBefore(t)||(this.swtAlert.warning("Second Time must be greater than First time"),!1)},e}(u.yb),d=[{path:"",component:h}],s=(a.l.forChild(d),function(){return function(){}}()),c=l("pMnS"),b=l("RChO"),m=l("t6HQ"),g=l("WFGK"),p=l("5FqG"),w=l("Ip0R"),x=l("gIcY"),f=l("t/Na"),I=l("sE5F"),v=l("OzfB"),G=l("T7CS"),C=l("S7LP"),R=l("6aHO"),A=l("WzUx"),y=l("A7o+"),T=l("zCE2"),B=l("Jg5P"),S=l("3R0m"),L=l("hhbb"),D=l("5rxC"),J=l("Fzqc"),M=l("21Lb"),k=l("hUWP"),N=l("3pJQ"),P=l("V9q+"),O=l("VDKW"),F=l("kXfT"),Z=l("BGbe");l.d(e,"IlmAccountGroupDetailslModuleNgFactory",function(){return q}),l.d(e,"RenderType_IlmAccountGroupDetails",function(){return _}),l.d(e,"View_IlmAccountGroupDetails_0",function(){return H}),l.d(e,"View_IlmAccountGroupDetails_Host_0",function(){return E}),l.d(e,"IlmAccountGroupDetailsNgFactory",function(){return Y});var q=i.Gb(s,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[c.a,b.a,m.a,g.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,Y]],[3,i.n],i.J]),i.Rb(4608,w.m,w.l,[i.F,[2,w.u]]),i.Rb(4608,x.c,x.c,[]),i.Rb(4608,x.p,x.p,[]),i.Rb(4608,f.j,f.p,[w.c,i.O,f.n]),i.Rb(4608,f.q,f.q,[f.j,f.o]),i.Rb(5120,f.a,function(t){return[t,new u.tb]},[f.q]),i.Rb(4608,f.m,f.m,[]),i.Rb(6144,f.k,null,[f.m]),i.Rb(4608,f.i,f.i,[f.k]),i.Rb(6144,f.b,null,[f.i]),i.Rb(4608,f.f,f.l,[f.b,i.B]),i.Rb(4608,f.c,f.c,[f.f]),i.Rb(4608,I.c,I.c,[]),i.Rb(4608,I.g,I.b,[]),i.Rb(5120,I.i,I.j,[]),i.Rb(4608,I.h,I.h,[I.c,I.g,I.i]),i.Rb(4608,I.f,I.a,[]),i.Rb(5120,I.d,I.k,[I.h,I.f]),i.Rb(5120,i.b,function(t,e){return[v.j(t,e)]},[w.c,i.O]),i.Rb(4608,G.a,G.a,[]),i.Rb(4608,C.a,C.a,[]),i.Rb(4608,R.a,R.a,[i.n,i.L,i.B,C.a,i.g]),i.Rb(4608,A.c,A.c,[i.n,i.g,i.B]),i.Rb(4608,A.e,A.e,[A.c]),i.Rb(4608,y.l,y.l,[]),i.Rb(4608,y.h,y.g,[]),i.Rb(4608,y.c,y.f,[]),i.Rb(4608,y.j,y.d,[]),i.Rb(4608,y.b,y.a,[]),i.Rb(4608,y.k,y.k,[y.l,y.h,y.c,y.j,y.b,y.m,y.n]),i.Rb(4608,A.i,A.i,[[2,y.k]]),i.Rb(4608,A.r,A.r,[A.L,[2,y.k],A.i]),i.Rb(4608,A.t,A.t,[]),i.Rb(4608,A.w,A.w,[]),i.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),i.Rb(1073742336,w.b,w.b,[]),i.Rb(1073742336,x.n,x.n,[]),i.Rb(1073742336,x.l,x.l,[]),i.Rb(1073742336,T.a,T.a,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,x.e,x.e,[]),i.Rb(1073742336,S.a,S.a,[]),i.Rb(1073742336,y.i,y.i,[]),i.Rb(1073742336,A.b,A.b,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,f.d,f.d,[]),i.Rb(1073742336,I.e,I.e,[]),i.Rb(1073742336,L.b,L.b,[]),i.Rb(1073742336,D.b,D.b,[]),i.Rb(1073742336,v.c,v.c,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,M.d,M.d,[]),i.Rb(1073742336,k.c,k.c,[]),i.Rb(1073742336,N.a,N.a,[]),i.Rb(1073742336,P.a,P.a,[[2,v.g],i.O]),i.Rb(1073742336,O.b,O.b,[]),i.Rb(1073742336,F.a,F.a,[]),i.Rb(1073742336,Z.b,Z.b,[]),i.Rb(1073742336,u.Tb,u.Tb,[]),i.Rb(1073742336,s,s,[]),i.Rb(256,f.n,"XSRF-TOKEN",[]),i.Rb(256,f.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,y.m,void 0,[]),i.Rb(256,y.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,a.i,function(){return[[{path:"",component:h}]]},[])])}),j=[[""]],_=i.Hb({encapsulation:0,styles:j,data:{}});function H(t){return i.dc(0,[i.Zb(*********,1,{_container:0}),i.Zb(*********,2,{buttonsContainer:0}),i.Zb(*********,3,{lblEntity:0}),i.Zb(*********,4,{entityCombo:0}),i.Zb(*********,5,{selectedEntity:0}),i.Zb(*********,6,{lblCurrency:0}),i.Zb(*********,7,{ccyCombo:0}),i.Zb(*********,8,{selectedCcy:0}),i.Zb(*********,9,{lblGrpId:0}),i.Zb(*********,10,{groupIdTextInput:0}),i.Zb(*********,11,{startTime:0}),i.Zb(*********,12,{endTime:0}),i.Zb(*********,13,{startTarget:0}),i.Zb(*********,14,{endTarget:0}),i.Zb(*********,15,{lblPub:0}),i.Zb(*********,16,{publicprivate:0}),i.Zb(*********,17,{privateRadio:0}),i.Zb(*********,18,{publicRadio:0}),i.Zb(*********,19,{lblType:0}),i.Zb(*********,20,{type:0}),i.Zb(*********,21,{typeFixed:0}),i.Zb(*********,22,{typeDynamic:0}),i.Zb(*********,23,{lblCreatedBy:0}),i.Zb(*********,24,{createdByLabelText:0}),i.Zb(*********,25,{lblOn:0}),i.Zb(*********,26,{createdOnLabelText:0}),i.Zb(*********,27,{lblRep:0}),i.Zb(*********,28,{allowReportingCheckBox:0}),i.Zb(*********,29,{lblRepNet:0}),i.Zb(*********,30,{collectNetCumPosCheckBox:0}),i.Zb(*********,31,{lblCorr:0}),i.Zb(*********,32,{correspondentBankCheckBox:0}),i.Zb(*********,33,{throughputCheckBox:0}),i.Zb(*********,34,{lblName:0}),i.Zb(*********,35,{groupName:0}),i.Zb(*********,36,{lblDesc:0}),i.Zb(*********,37,{groupDescription:0}),i.Zb(*********,38,{lblDef:0}),i.Zb(*********,39,{idName:0}),i.Zb(*********,40,{nameLegend:0}),i.Zb(*********,41,{idLegend:0}),i.Zb(*********,42,{lblAgent:0}),i.Zb(*********,43,{mainAgentText:0}),i.Zb(*********,44,{lblFistMin:0}),i.Zb(*********,45,{firstMinimum:0}),i.Zb(*********,46,{lblSecMin:0}),i.Zb(*********,47,{secondMinimum:0}),i.Zb(*********,48,{lblFirstMax:0}),i.Zb(*********,49,{firstMaximum:0}),i.Zb(*********,50,{lblSecMax:0}),i.Zb(*********,51,{secondMaximum:0}),i.Zb(*********,52,{lblMinNet:0}),i.Zb(*********,53,{netMinimum:0}),i.Zb(*********,54,{lblMaxNet:0}),i.Zb(*********,55,{netMaximum:0}),i.Zb(*********,56,{lblCond:0}),i.Zb(*********,57,{filterCondition:0}),i.Zb(*********,58,{accountNotInThisGroupLabel:0}),i.Zb(*********,59,{accountNotInThisGroupTextLabel:0}),i.Zb(*********,60,{otherGridCanvas:0}),i.Zb(*********,61,{quickSearchLabel:0}),i.Zb(*********,62,{leftGridQuickSearch:0}),i.Zb(*********,63,{lblListFromGrp:0}),i.Zb(*********,64,{secondGridGroupsCombo:0}),i.Zb(*********,65,{buttonMoveRight:0}),i.Zb(*********,66,{buttonMoveAllRight:0}),i.Zb(*********,67,{buttonMoveLeft:0}),i.Zb(*********,68,{buttonMoveAllLeft:0}),i.Zb(*********,69,{accountInThisGroupLabel:0}),i.Zb(*********,70,{accountInThisGroupTextLabel:0}),i.Zb(*********,71,{ownGridCanvas:0}),i.Zb(*********,72,{quickSearchLabel2:0}),i.Zb(*********,73,{rightGridQuickSearch:0}),i.Zb(*********,74,{saveButton:0}),i.Zb(*********,75,{cancelButton:0}),i.Zb(*********,76,{testButton:0}),i.Zb(*********,77,{loadingImage:0}),i.Zb(*********,78,{dataExport:0}),(t()(),i.Jb(78,0,null,null,386,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var i=!0,n=t.component;"creationComplete"===e&&(i=!1!==n.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(79,4440064,null,0,u.yb,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(80,0,null,0,384,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(81,4440064,null,0,u.ec,[i.r,u.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),i.Jb(82,0,null,0,362,"SwtCanvas",[["height","95%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(83,4440064,null,0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(84,0,null,0,360,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(85,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(86,0,null,0,208,"VBox",[["height","40%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(87,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(88,0,null,0,206,"Grid",[["height","100%"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(89,4440064,null,0,u.z,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(90,0,null,0,21,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(91,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(92,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(93,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(94,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(95,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(96,0,null,0,1,"SwtLabel",[["text","Entity"]],null,null,null,p.Yc,p.fb)),i.Ib(97,4440064,[[3,4],["lblEntity",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(98,0,null,0,5,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(99,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(100,0,null,0,1,"SwtComboBox",[["dataLabel","entity"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,101).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.entityChangeCombo()&&n);return n},p.Pc,p.W)),i.Ib(101,4440064,[[4,4],["entityCombo",4]],0,u.gb,[i.r,u.i],{dataLabel:[0,"dataLabel"]},{change_:"change"}),(t()(),i.Jb(102,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,p.Yc,p.fb)),i.Ib(103,4440064,[[5,4],["selectedEntity",4]],0,u.vb,[i.r,u.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(104,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(105,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(106,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(107,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(108,0,null,0,1,"SwtLabel",[["text","Created by"],["textAlign","right"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(109,4440064,[[23,4],["lblCreatedBy",4]],0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],width:[1,"width"],text:[2,"text"]},null),(t()(),i.Jb(110,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,p.Yc,p.fb)),i.Ib(111,4440064,[[24,4],["createdByLabelText",4]],0,u.vb,[i.r,u.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(112,0,null,0,21,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(113,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(114,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(115,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(116,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(117,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(118,0,null,0,1,"SwtLabel",[["text","Currency"]],null,null,null,p.Yc,p.fb)),i.Ib(119,4440064,[[6,4],["lblCurrency",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(120,0,null,0,5,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(121,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(122,0,null,0,1,"SwtComboBox",[["dataLabel","currency"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,123).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.entityChangeCombo()&&n);return n},p.Pc,p.W)),i.Ib(123,4440064,[[7,4],["ccyCombo",4]],0,u.gb,[i.r,u.i],{dataLabel:[0,"dataLabel"]},{change_:"change"}),(t()(),i.Jb(124,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,p.Yc,p.fb)),i.Ib(125,4440064,[[8,4],["selectedCcy",4]],0,u.vb,[i.r,u.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(126,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(127,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(128,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(129,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(130,0,null,0,1,"SwtLabel",[["text","On"],["textAlign","right"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(131,4440064,[[25,4],["lblOn",4]],0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],width:[1,"width"],text:[2,"text"]},null),(t()(),i.Jb(132,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,p.Yc,p.fb)),i.Ib(133,4440064,[[26,4],["createdOnLabelText",4]],0,u.vb,[i.r,u.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(134,0,null,0,19,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(135,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(136,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(137,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(138,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(139,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(140,0,null,0,1,"SwtLabel",[["text","Group ID"]],null,null,null,p.Yc,p.fb)),i.Ib(141,4440064,[[9,4],["lblGrpId",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(142,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(143,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(144,0,null,0,1,"SwtTextInput",[["maxChars","20"],["width","275"]],null,null,null,p.kd,p.sb)),i.Ib(145,4440064,[[10,4],["groupIdTextInput",4]],0,u.Rb,[i.r,u.i],{maxChars:[0,"maxChars"],width:[1,"width"]},null),(t()(),i.Jb(146,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(147,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(148,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(149,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(150,0,null,0,1,"SwtLabel",[["text","Allow Reporting"],["textAlign","right"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(151,4440064,[[27,4],["lblRep",4]],0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],width:[1,"width"],text:[2,"text"]},null),(t()(),i.Jb(152,0,null,0,1,"SwtCheckBox",[],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.enableDisableRNCPC_CorrBank(l)&&i);return i},p.Oc,p.V)),i.Ib(153,4440064,[[28,4],["allowReportingCheckBox",4]],0,u.eb,[i.r,u.i],null,{change_:"change"}),(t()(),i.Jb(154,0,null,0,24,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(155,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(156,0,null,0,14,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(157,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(158,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(159,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(160,0,null,0,1,"SwtLabel",[["text","Public/Private"]],null,null,null,p.Yc,p.fb)),i.Ib(161,4440064,[[15,4],["lblPub",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(162,0,null,0,8,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(163,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(164,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["height","28"],["id","publicprivate"]],null,null,null,p.ed,p.lb)),i.Ib(165,4440064,[[16,4],["publicprivate",4]],1,u.Hb,[f.c,i.r,u.i],{id:[0,"id"],height:[1,"height"],align:[2,"align"]},null),i.Zb(603979776,79,{radioItems:1}),(t()(),i.Jb(167,0,null,0,1,"SwtRadioItem",[["groupName","publicprivate"],["id","privateRadio"],["label","Private"],["selected","true"],["value","PRIVATE"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(168,4440064,[[79,4],[17,4],["privateRadio",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"],selected:[5,"selected"]},null),(t()(),i.Jb(169,0,null,0,1,"SwtRadioItem",[["groupName","publicprivate"],["id","publicRadio"],["label","Public"],["value","PUBLIC"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(170,4440064,[[79,4],[18,4],["publicRadio",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),i.Jb(171,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(172,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(173,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(174,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(175,0,null,0,1,"SwtLabel",[["text","Create Net Cumulative Position Data"],["textAlign","right"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(176,4440064,[[29,4],["lblRepNet",4]],0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],width:[1,"width"],text:[2,"text"]},null),(t()(),i.Jb(177,0,null,0,1,"SwtCheckBox",[],null,null,null,p.Oc,p.V)),i.Ib(178,4440064,[[30,4],["collectNetCumPosCheckBox",4]],0,u.eb,[i.r,u.i],null,null),(t()(),i.Jb(179,0,null,0,24,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(180,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(181,0,null,0,14,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(182,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(183,0,null,0,3,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(184,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(185,0,null,0,1,"SwtLabel",[["text","Type"]],null,null,null,p.Yc,p.fb)),i.Ib(186,4440064,[[19,4],["lblType",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(187,0,null,0,8,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(188,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(189,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["height","28"],["id","type"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.radioTypeChangeHandler()&&i);return i},p.ed,p.lb)),i.Ib(190,4440064,[[20,4],["type",4]],1,u.Hb,[f.c,i.r,u.i],{id:[0,"id"],height:[1,"height"],align:[2,"align"]},{change_:"change"}),i.Zb(603979776,80,{radioItems:1}),(t()(),i.Jb(192,0,null,0,1,"SwtRadioItem",[["groupName","type"],["id","typeFixed"],["label","Fixed"],["selected","true"],["value","F"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(193,4440064,[[80,4],[21,4],["typeFixed",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"],selected:[5,"selected"]},null),(t()(),i.Jb(194,0,null,0,1,"SwtRadioItem",[["groupName","type"],["id","typeDynamic"],["label","Dynamic"],["value","D"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(195,4440064,[[80,4],[22,4],["typeDynamic",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),i.Jb(196,0,null,0,7,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(197,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(198,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(199,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(200,0,null,0,1,"SwtLabel",[["text","Correspondent Bank"],["textAlign","right"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(201,4440064,[[31,4],["lblCorr",4]],0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],width:[1,"width"],text:[2,"text"]},null),(t()(),i.Jb(202,0,null,0,1,"SwtCheckBox",[],null,null,null,p.Oc,p.V)),i.Ib(203,4440064,[[32,4],["correspondentBankCheckBox",4]],0,u.eb,[i.r,u.i],null,null),(t()(),i.Jb(204,0,null,0,11,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(205,4440064,null,0,u.B,[i.r,u.i],null,null),(t()(),i.Jb(206,0,null,0,1,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(207,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(208,0,null,0,7,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(209,4440064,null,0,u.A,[i.r,u.i],null,null),(t()(),i.Jb(210,0,null,0,5,"HBox",[],null,null,null,p.Dc,p.K)),i.Ib(211,4440064,null,0,u.C,[i.r,u.i],null,null),(t()(),i.Jb(212,0,null,0,1,"SwtLabel",[["textAlign","right"],["textDictionaryId","ilmAccountGroupDetails.createThroughput"],["width","400"]],null,null,null,p.Yc,p.fb)),i.Ib(213,4440064,null,0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],textDictionaryId:[1,"textDictionaryId"],width:[2,"width"]},null),(t()(),i.Jb(214,0,null,0,1,"SwtCheckBox",[],null,null,null,p.Oc,p.V)),i.Ib(215,4440064,[[33,4],["throughputCheckBox",4]],0,u.eb,[i.r,u.i],null,null),(t()(),i.Jb(216,0,null,0,9,"GridRow",[["height","10%"]],null,null,null,p.Bc,p.J)),i.Ib(217,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(218,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(219,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(220,0,null,0,1,"SwtLabel",[["text","Name"]],null,null,null,p.Yc,p.fb)),i.Ib(221,4440064,[[34,4],["lblName",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(222,0,null,0,3,"GridItem",[["width","85%"]],null,null,null,p.Ac,p.I)),i.Ib(223,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(224,0,null,0,1,"SwtTextInput",[["maxChars","50"],["width","100%"]],null,null,null,p.kd,p.sb)),i.Ib(225,4440064,[[35,4],["groupName",4]],0,u.Rb,[i.r,u.i],{maxChars:[0,"maxChars"],width:[1,"width"]},null),(t()(),i.Jb(226,0,null,0,9,"GridRow",[["height","20%"]],null,null,null,p.Bc,p.J)),i.Ib(227,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(228,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(229,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(230,0,null,0,1,"SwtLabel",[["marginRight","8"],["text","Description"]],null,null,null,p.Yc,p.fb)),i.Ib(231,4440064,[[36,4],["lblDesc",4]],0,u.vb,[i.r,u.i],{marginRight:[0,"marginRight"],text:[1,"text"]},null),(t()(),i.Jb(232,0,null,0,3,"GridItem",[["width","85%"]],null,null,null,p.Ac,p.I)),i.Ib(233,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(234,0,null,0,1,"SwtTextArea",[["height","40"],["maxChars","100"],["width","100%"]],null,null,null,p.jd,p.rb)),i.Ib(235,4440064,[[37,4],["groupDescription",4]],0,u.Qb,[i.r,u.i,i.L],{maxChars:[0,"maxChars"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(236,0,null,0,58,"GridRow",[["height","20%"]],null,null,null,p.Bc,p.J)),i.Ib(237,4440064,null,0,u.B,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(238,0,null,0,28,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(239,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(240,0,null,0,26,"VBox",[["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(241,4440064,null,0,u.ec,[i.r,u.i,i.T],{width:[0,"width"]},null),(t()(),i.Jb(242,0,null,0,14,"GridItem",[["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(243,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(244,0,null,0,3,"GridItem",[["width","43%"]],null,null,null,p.Ac,p.I)),i.Ib(245,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(246,0,null,0,1,"SwtLabel",[["text","Default Legend Text"]],null,null,null,p.Yc,p.fb)),i.Ib(247,4440064,[[38,4],["lblDef",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(248,0,null,0,8,"GridItem",[["width","57%"]],null,null,null,p.Ac,p.I)),i.Ib(249,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(250,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["height","28"],["id","idName"]],null,null,null,p.ed,p.lb)),i.Ib(251,4440064,[[39,4],["idName",4]],1,u.Hb,[f.c,i.r,u.i],{id:[0,"id"],height:[1,"height"],align:[2,"align"]},null),i.Zb(603979776,81,{radioItems:1}),(t()(),i.Jb(253,0,null,0,1,"SwtRadioItem",[["groupName","idName"],["id","idLegend"],["label","ID"],["selected","true"],["value","I"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(254,4440064,[[81,4],[41,4],["idLegend",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"],selected:[5,"selected"]},null),(t()(),i.Jb(255,0,null,0,1,"SwtRadioItem",[["groupName","idName"],["id","nameLegend"],["label","Name"],["value","N"],["width","80"]],null,null,null,p.fd,p.mb)),i.Ib(256,4440064,[[81,4],[40,4],["nameLegend",4]],0,u.Ib,[i.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),i.Jb(257,0,null,0,9,"GridItem",[["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(258,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(259,0,null,0,3,"GridItem",[["width","43%"]],null,null,null,p.Ac,p.I)),i.Ib(260,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(261,0,null,0,1,"SwtLabel",[["text","Main Agent"]],null,null,null,p.Yc,p.fb)),i.Ib(262,4440064,[[42,4],["lblAgent",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(263,0,null,0,3,"GridItem",[["width","57%"]],null,null,null,p.Ac,p.I)),i.Ib(264,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(265,0,null,0,1,"SwtTextInput",[["maxChars","30"],["width","275"]],null,null,null,p.kd,p.sb)),i.Ib(266,4440064,[[43,4],["mainAgentText",4]],0,u.Rb,[i.r,u.i],{maxChars:[0,"maxChars"],width:[1,"width"]},null),(t()(),i.Jb(267,0,null,0,1,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(268,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(269,0,null,0,25,"SwtFieldSet",[["legendText","Throughput Ratio"],["style","height: 88%; width: 100%"]],null,null,null,p.Vc,p.cb)),i.Ib(270,4440064,null,0,u.ob,[i.r,u.i],{legendText:[0,"legendText"]},null),(t()(),i.Jb(271,0,null,0,23,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(272,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(273,0,null,0,11,"GridItem",[["height","60%"]],null,null,null,p.Ac,p.I)),i.Ib(274,4440064,null,0,u.A,[i.r,u.i],{height:[0,"height"]},null),(t()(),i.Jb(275,0,null,0,1,"SwtLabel",[["textDictionaryId","ilmAccountGroupDetails.first"],["width","30%"]],null,null,null,p.Yc,p.fb)),i.Ib(276,4440064,null,0,u.vb,[i.r,u.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"]},null),(t()(),i.Jb(277,0,null,0,1,"SwtLabel",[["paddingRight","10"],["textAlign","right"],["textDictionaryId","ilmAccountGroupDetails.time"],["width","10%"]],null,null,null,p.Yc,p.fb)),i.Ib(278,4440064,null,0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],textDictionaryId:[1,"textDictionaryId"],width:[2,"width"],paddingRight:[3,"paddingRight"]},null),(t()(),i.Jb(279,0,null,0,1,"SwtTextInput",[["height","20"],["maxChars","5"],["width","15%"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.validateTime(i.Tb(t,280))&&n);return n},p.kd,p.sb)),i.Ib(280,4440064,[[11,4],["startTime",4]],0,u.Rb,[i.r,u.i],{maxChars:[0,"maxChars"],width:[1,"width"],height:[2,"height"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(281,0,null,0,1,"SwtLabel",[["paddingRight","10"],["textAlign","right"],["textDictionaryId","ilmAccountGroupDetails.target"],["width","30%"]],null,null,null,p.Yc,p.fb)),i.Ib(282,4440064,null,0,u.vb,[i.r,u.i],{textAlign:[0,"textAlign"],textDictionaryId:[1,"textDictionaryId"],width:[2,"width"],paddingRight:[3,"paddingRight"]},null),(t()(),i.Jb(283,0,null,0,1,"SwtNumericInput",[["height","20"],["maxChars","3"],["maximum","100"],["minimum","0"],["textAlign","right"],["width","10%"]],null,null,null,p.cd,p.jb)),i.Ib(284,4440064,[[13,4],["startTarget",4]],0,u.Ab,[i.r,u.i],{maxChars:[0,"maxChars"],textAlign:[1,"textAlign"],width:[2,"width"],height:[3,"height"],maximum:[4,"maximum"],minimum:[5,"minimum"]},null),(t()(),i.Jb(285,0,null,0,9,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(286,4440064,null,0,u.A,[i.r,u.i],null,null),(t()(),i.Jb(287,0,null,0,1,"SwtLabel",[["textDictionaryId","ilmAccountGroupDetails.second"],["width","40%"]],null,null,null,p.Yc,p.fb)),i.Ib(288,4440064,null,0,u.vb,[i.r,u.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"]},null),(t()(),i.Jb(289,0,null,0,1,"SwtTextInput",[["height","20"],["maxChars","5"],["width","15%"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.validateTime(i.Tb(t,290))&&n);return n},p.kd,p.sb)),i.Ib(290,4440064,[[12,4],["endTime",4]],0,u.Rb,[i.r,u.i],{maxChars:[0,"maxChars"],width:[1,"width"],height:[2,"height"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(291,0,null,0,1,"GridItem",[["width","30%"]],null,null,null,p.Ac,p.I)),i.Ib(292,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(293,0,null,0,1,"SwtNumericInput",[["height","20"],["maxChars","3"],["maximum","100"],["minimum","0"],["textAlign","right"],["width","10%"]],null,null,null,p.cd,p.jb)),i.Ib(294,4440064,[[14,4],["endTarget",4]],0,u.Ab,[i.r,u.i],{maxChars:[0,"maxChars"],textAlign:[1,"textAlign"],width:[2,"width"],height:[3,"height"],maximum:[4,"maximum"],minimum:[5,"minimum"]},null),(t()(),i.Jb(295,0,null,0,47,"SwtFieldSet",[["legendText","Absolute Thresholds"],["style","height:11%"]],null,null,null,p.Vc,p.cb)),i.Ib(296,4440064,null,0,u.ob,[i.r,u.i],{legendText:[0,"legendText"]},null),(t()(),i.Jb(297,0,null,0,45,"Grid",[["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(298,4440064,null,0,u.z,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(299,0,null,0,21,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(300,4440064,null,0,u.B,[i.r,u.i],null,null),(t()(),i.Jb(301,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(302,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(303,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(304,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(305,0,null,0,1,"SwtLabel",[["text","First Minimum"]],null,null,null,p.Yc,p.fb)),i.Ib(306,4440064,[[44,4],["lblFistMin",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(307,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(308,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(309,0,null,0,1,"SwtTextInput",[["id","firstMinimum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,310))&&n);return n},p.kd,p.sb)),i.Ib(310,4440064,[[45,4],["firstMinimum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(311,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(312,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(313,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(314,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(315,0,null,0,1,"SwtLabel",[["text","First Maximum"]],null,null,null,p.Yc,p.fb)),i.Ib(316,4440064,[[48,4],["lblFirstMax",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(317,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(318,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(319,0,null,0,1,"SwtTextInput",[["id","firstMaximum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,320))&&n);return n},p.kd,p.sb)),i.Ib(320,4440064,[[49,4],["firstMaximum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(321,0,null,0,21,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(322,4440064,null,0,u.B,[i.r,u.i],null,null),(t()(),i.Jb(323,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(324,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(325,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(326,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(327,0,null,0,1,"SwtLabel",[["text","Second Minimum"]],null,null,null,p.Yc,p.fb)),i.Ib(328,4440064,[[46,4],["lblSecMin",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(329,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(330,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(331,0,null,0,1,"SwtTextInput",[["id","secondMinimum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,332))&&n);return n},p.kd,p.sb)),i.Ib(332,4440064,[[47,4],["secondMinimum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(333,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(334,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(335,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(336,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(337,0,null,0,1,"SwtLabel",[["text","Second Maximum"]],null,null,null,p.Yc,p.fb)),i.Ib(338,4440064,[[50,4],["lblSecMax",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(339,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(340,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(341,0,null,0,1,"SwtTextInput",[["id","secondMaximum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,342))&&n);return n},p.kd,p.sb)),i.Ib(342,4440064,[[51,4],["secondMaximum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(343,0,null,0,25,"SwtFieldSet",[["legendText","Net Cumulative Position Thresholds"],["style","height:7%"]],null,null,null,p.Vc,p.cb)),i.Ib(344,4440064,null,0,u.ob,[i.r,u.i],{legendText:[0,"legendText"]},null),(t()(),i.Jb(345,0,null,0,23,"Grid",[["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(346,4440064,null,0,u.z,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(347,0,null,0,21,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(348,4440064,null,0,u.B,[i.r,u.i],null,null),(t()(),i.Jb(349,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(350,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(351,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(352,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(353,0,null,0,1,"SwtLabel",[["text","Minimum"]],null,null,null,p.Yc,p.fb)),i.Ib(354,4440064,[[52,4],["lblMinNet",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(355,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(356,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(357,0,null,0,1,"SwtTextInput",[["id","netMinimum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,358))&&n);return n},p.kd,p.sb)),i.Ib(358,4440064,[[53,4],["netMinimum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(359,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(360,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(361,0,null,0,3,"GridItem",[["width","29%"]],null,null,null,p.Ac,p.I)),i.Ib(362,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(363,0,null,0,1,"SwtLabel",[["text","Maximum"]],null,null,null,p.Yc,p.fb)),i.Ib(364,4440064,[[54,4],["lblMaxNet",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(365,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,p.Ac,p.I)),i.Ib(366,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(367,0,null,0,1,"SwtTextInput",[["id","netMaximum"],["restrict","0-9.,-bBtTmM"],["width","180"]],null,[[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"focusOut"===e&&(n=!1!==u.formatAmount(i.Tb(t,368))&&n);return n},p.kd,p.sb)),i.Ib(368,4440064,[[55,4],["netMaximum",4]],0,u.Rb,[i.r,u.i],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(369,0,null,0,11,"Grid",[["height","10%"],["paddingTop","5"]],null,null,null,p.Cc,p.H)),i.Ib(370,4440064,null,0,u.z,[i.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(t()(),i.Jb(371,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(372,4440064,null,0,u.B,[i.r,u.i],null,null),(t()(),i.Jb(373,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(374,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(375,0,null,0,1,"SwtLabel",[["text","Filter Condition:"]],null,null,null,p.Yc,p.fb)),i.Ib(376,4440064,[[56,4],["lblCond",4]],0,u.vb,[i.r,u.i],{text:[0,"text"]},null),(t()(),i.Jb(377,0,null,0,3,"GridItem",[["width","85%"]],null,null,null,p.Ac,p.I)),i.Ib(378,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(379,0,null,0,1,"SwtTextArea",[["enabled","false"],["height","70"],["width","100%"]],null,null,null,p.jd,p.rb)),i.Ib(380,4440064,[[57,4],["filterCondition",4]],0,u.Qb,[i.r,u.i,i.L],{width:[0,"width"],height:[1,"height"],enabled:[2,"enabled"]},null),(t()(),i.Jb(381,0,null,0,63,"SwtFieldSet",[["legendText","Group member accounts"],["style","height:37%"]],null,null,null,p.Vc,p.cb)),i.Ib(382,4440064,null,0,u.ob,[i.r,u.i],{legendText:[0,"legendText"]},null),(t()(),i.Jb(383,0,null,0,61,"HBox",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(384,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(385,0,null,0,27,"HBox",[["height","100%"],["width","45%"]],null,null,null,p.Dc,p.K)),i.Ib(386,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(387,0,null,0,25,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(388,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(389,0,null,0,5,"HBox",[["height","8%"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(390,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(391,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(392,4440064,[[58,4],["accountNotInThisGroupLabel",4]],0,u.vb,[i.r,u.i],null,null),(t()(),i.Jb(393,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(394,4440064,[[59,4],["accountNotInThisGroupTextLabel",4]],0,u.vb,[i.r,u.i],null,null),(t()(),i.Jb(395,0,null,0,3,"HBox",[["height","62%"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(396,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(397,0,null,0,1,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(398,4440064,[[60,4],["otherGridCanvas",4]],0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(399,0,null,0,13,"VBox",[["height","20%"],["paddingTop","10"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(400,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"]},null),(t()(),i.Jb(401,0,null,0,5,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(402,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(403,0,null,0,1,"SwtLabel",[["text","Quick Search"],["width","29%"]],null,null,null,p.Yc,p.fb)),i.Ib(404,4440064,[[61,4],["quickSearchLabel",4]],0,u.vb,[i.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),i.Jb(405,0,null,0,1,"SwtTextInput",[["width","70%"]],null,[[null,"change"],[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"change"===e&&(n=!1!==u.filtringLeftGrid(i.Tb(t,406).text)&&n);"focusOut"===e&&(n=!1!==u.refreshLeftHeader()&&n);return n},p.kd,p.sb)),i.Ib(406,4440064,[[62,4],["leftGridQuickSearch",4]],0,u.Rb,[i.r,u.i],{width:[0,"width"]},{onFocusOut_:"focusOut",change_:"change"}),(t()(),i.Jb(407,0,null,0,5,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(408,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(409,0,null,0,1,"SwtLabel",[["text","List from group"],["width","29%"]],null,null,null,p.Yc,p.fb)),i.Ib(410,4440064,[[63,4],["lblListFromGrp",4]],0,u.vb,[i.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),i.Jb(411,0,null,0,1,"SwtComboBox",[["dataLabel","secondgridgroups"],["shiftUp","100"],["width","277"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,412).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.leftGridGroupComboChange()&&n);return n},p.Pc,p.W)),i.Ib(412,4440064,[[64,4],["secondGridGroupsCombo",4]],0,u.gb,[i.r,u.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],shiftUp:[2,"shiftUp"]},{change_:"change"}),(t()(),i.Jb(413,0,null,0,11,"HBox",[["height","100%"],["width","10%"]],null,null,null,p.Dc,p.K)),i.Ib(414,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(415,0,null,0,9,"VBox",[["height","76%"],["horizontalAlign","center"],["verticalAlign","middle"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(416,4440064,[[2,4],["buttonsContainer",4]],0,u.ec,[i.r,u.i,i.T],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"]},null),(t()(),i.Jb(417,0,null,0,1,"SwtButton",[["enabled","false"],["id","buttonMoveRight"],["label",">"],["width","40"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.moveToRight(l,!1)&&i);return i},p.Mc,p.T)),i.Ib(418,4440064,[[65,4],["buttonMoveRight",4]],0,u.cb,[i.r,u.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click"}),(t()(),i.Jb(419,0,null,0,1,"SwtButton",[["enabled","false"],["id","buttonMoveAllRight"],["label",">>"],["width","40"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.moveToRight(l,!0)&&i);return i},p.Mc,p.T)),i.Ib(420,4440064,[[66,4],["buttonMoveAllRight",4]],0,u.cb,[i.r,u.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click"}),(t()(),i.Jb(421,0,null,0,1,"SwtButton",[["enabled","false"],["id","buttonMoveLeft"],["label","<"],["width","40"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.moveToLeft(l,!1)&&i);return i},p.Mc,p.T)),i.Ib(422,4440064,[[67,4],["buttonMoveLeft",4]],0,u.cb,[i.r,u.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click"}),(t()(),i.Jb(423,0,null,0,1,"SwtButton",[["enabled","false"],["id","buttonMoveAllLeft"],["label","<<"],["width","40"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.moveToLeft(l,!0)&&i);return i},p.Mc,p.T)),i.Ib(424,4440064,[[68,4],["buttonMoveAllLeft",4]],0,u.cb,[i.r,u.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click"}),(t()(),i.Jb(425,0,null,0,19,"HBox",[["height","100%"],["width","45%"]],null,null,null,p.Dc,p.K)),i.Ib(426,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(427,0,null,0,17,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(428,4440064,null,0,u.ec,[i.r,u.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(429,0,null,0,5,"HBox",[["height","8%"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(430,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(431,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(432,4440064,[[69,4],["accountInThisGroupLabel",4]],0,u.vb,[i.r,u.i],null,null),(t()(),i.Jb(433,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(434,4440064,[[70,4],["accountInThisGroupTextLabel",4]],0,u.vb,[i.r,u.i],null,null),(t()(),i.Jb(435,0,null,0,3,"HBox",[["height","62%"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(436,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(437,0,null,0,1,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(438,4440064,[[71,4],["ownGridCanvas",4]],0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(439,0,null,0,5,"HBox",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(440,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),i.Jb(441,0,null,0,1,"SwtLabel",[["text","Quick Search"],["width","30%"]],null,null,null,p.Yc,p.fb)),i.Ib(442,4440064,[[72,4],["quickSearchLabel2",4]],0,u.vb,[i.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),i.Jb(443,0,null,0,1,"SwtTextInput",[["width","70%"]],null,[[null,"change"],[null,"focusOut"]],function(t,e,l){var n=!0,u=t.component;"change"===e&&(n=!1!==u.filtringRightGrid(i.Tb(t,444).text)&&n);"focusOut"===e&&(n=!1!==u.refreshRightHeader()&&n);return n},p.kd,p.sb)),i.Ib(444,4440064,[[73,4],["rightGridQuickSearch",4]],0,u.Rb,[i.r,u.i],{width:[0,"width"]},{onFocusOut_:"focusOut",change_:"change"}),(t()(),i.Jb(445,0,null,0,19,"SwtCanvas",[["height","5%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(446,4440064,null,0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(447,0,null,0,17,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(448,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"]},null),(t()(),i.Jb(449,0,null,0,7,"HBox",[["paddingLeft","5"],["width","80%"]],null,null,null,p.Dc,p.K)),i.Ib(450,4440064,null,0,u.C,[i.r,u.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(451,0,null,0,1,"SwtButton",[["label","Save"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.saveButton_clickHandler(l)&&i);return i},p.Mc,p.T)),i.Ib(452,4440064,[[74,4],["saveButton",4]],0,u.cb,[i.r,u.i],{label:[0,"label"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(453,0,null,0,1,"SwtButton",[["label","Cancel"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.closeHandler()&&i);return i},p.Mc,p.T)),i.Ib(454,4440064,[[75,4],["cancelButton",4]],0,u.cb,[i.r,u.i],{label:[0,"label"]},{onClick_:"click"}),(t()(),i.Jb(455,0,null,0,1,"SwtButton",[["enabled","false"],["label","Test"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.filterConditionValidation()&&i);return i},p.Mc,p.T)),i.Ib(456,4440064,[[76,4],["testButton",4]],0,u.cb,[i.r,u.i],{enabled:[0,"enabled"],label:[1,"label"]},{onClick_:"click"}),(t()(),i.Jb(457,0,null,0,7,"HBox",[["horizontalAlign","right"],["top","4"],["width","20%"]],null,null,null,p.Dc,p.K)),i.Ib(458,4440064,null,0,u.C,[i.r,u.i],{top:[0,"top"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"]},null),(t()(),i.Jb(459,0,null,0,1,"DataExport",[["id","dataExport"]],null,null,null,p.Sc,p.Z)),i.Ib(460,4440064,[[78,4],["dataExport",4]],0,u.kb,[u.i,i.r],{id:[0,"id"]},null),(t()(),i.Jb(461,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(462,4440064,[["helpIcon",4]],0,u.rb,[i.r,u.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(463,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),i.Ib(464,114688,[[77,4],["loadingImage",4]],0,u.xb,[i.r],null,null)],function(t,e){t(e,79,0,"100%","100%");t(e,81,0,"100%","100%","5","5","5","5");t(e,83,0,"100%","95%");t(e,85,0,"0","100%","100%");t(e,87,0,"0","100%","40%");t(e,89,0,"100%","100%");t(e,91,0,"10%");t(e,93,0,"50%");t(e,95,0,"30%");t(e,97,0,"Entity");t(e,99,0,"70%");t(e,101,0,"entity");t(e,103,0,"normal");t(e,105,0,"50%"),t(e,107,0);t(e,109,0,"right","400","Created by");t(e,111,0,"normal");t(e,113,0,"10%");t(e,115,0,"50%");t(e,117,0,"30%");t(e,119,0,"Currency");t(e,121,0,"70%");t(e,123,0,"currency");t(e,125,0,"normal");t(e,127,0,"50%"),t(e,129,0);t(e,131,0,"right","400","On");t(e,133,0,"normal");t(e,135,0,"10%");t(e,137,0,"50%");t(e,139,0,"30%");t(e,141,0,"Group ID");t(e,143,0,"70%");t(e,145,0,"20","275");t(e,147,0,"50%"),t(e,149,0);t(e,151,0,"right","400","Allow Reporting"),t(e,153,0);t(e,155,0,"10%");t(e,157,0,"50%");t(e,159,0,"30%");t(e,161,0,"Public/Private");t(e,163,0,"70%");t(e,165,0,"publicprivate","28","horizontal");t(e,168,0,"privateRadio","80","publicprivate","Private","PRIVATE","true");t(e,170,0,"publicRadio","80","publicprivate","Public","PUBLIC");t(e,172,0,"50%"),t(e,174,0);t(e,176,0,"right","400","Create Net Cumulative Position Data"),t(e,178,0);t(e,180,0,"10%");t(e,182,0,"50%");t(e,184,0,"30%");t(e,186,0,"Type");t(e,188,0,"70%");t(e,190,0,"type","28","horizontal");t(e,193,0,"typeFixed","80","type","Fixed","F","true");t(e,195,0,"typeDynamic","80","type","Dynamic","D");t(e,197,0,"50%"),t(e,199,0);t(e,201,0,"right","400","Correspondent Bank"),t(e,203,0),t(e,205,0);t(e,207,0,"50%"),t(e,209,0),t(e,211,0);t(e,213,0,"right","ilmAccountGroupDetails.createThroughput","400"),t(e,215,0);t(e,217,0,"10%");t(e,219,0,"15%");t(e,221,0,"Name");t(e,223,0,"85%");t(e,225,0,"50","100%");t(e,227,0,"20%");t(e,229,0,"15%");t(e,231,0,"8","Description");t(e,233,0,"85%");t(e,235,0,"100","100%","40");t(e,237,0,"20%");t(e,239,0,"50%");t(e,241,0,"100%");t(e,243,0,"100%");t(e,245,0,"43%");t(e,247,0,"Default Legend Text");t(e,249,0,"57%");t(e,251,0,"idName","28","horizontal");t(e,254,0,"idLegend","80","idName","ID","I","true");t(e,256,0,"nameLegend","80","idName","Name","N");t(e,258,0,"100%");t(e,260,0,"43%");t(e,262,0,"Main Agent");t(e,264,0,"57%");t(e,266,0,"30","275");t(e,268,0,"50%");t(e,270,0,"Throughput Ratio");t(e,272,0,"0","100%","100%");t(e,274,0,"60%");t(e,276,0,"ilmAccountGroupDetails.first","30%");t(e,278,0,"right","ilmAccountGroupDetails.time","10%","10");t(e,280,0,"5","15%","20");t(e,282,0,"right","ilmAccountGroupDetails.target","30%","10");t(e,284,0,"3","right","10%","20","100","0"),t(e,286,0);t(e,288,0,"ilmAccountGroupDetails.second","40%");t(e,290,0,"5","15%","20");t(e,292,0,"30%");t(e,294,0,"3","right","10%","20","100","0");t(e,296,0,"Absolute Thresholds");t(e,298,0,"100%"),t(e,300,0);t(e,302,0,"50%");t(e,304,0,"29%");t(e,306,0,"First Minimum");t(e,308,0,"70%");t(e,310,0,"0-9.,-bBtTmM","firstMinimum","180");t(e,312,0,"50%");t(e,314,0,"29%");t(e,316,0,"First Maximum");t(e,318,0,"70%");t(e,320,0,"0-9.,-bBtTmM","firstMaximum","180"),t(e,322,0);t(e,324,0,"50%");t(e,326,0,"29%");t(e,328,0,"Second Minimum");t(e,330,0,"70%");t(e,332,0,"0-9.,-bBtTmM","secondMinimum","180");t(e,334,0,"50%");t(e,336,0,"29%");t(e,338,0,"Second Maximum");t(e,340,0,"70%");t(e,342,0,"0-9.,-bBtTmM","secondMaximum","180");t(e,344,0,"Net Cumulative Position Thresholds");t(e,346,0,"100%"),t(e,348,0);t(e,350,0,"50%");t(e,352,0,"29%");t(e,354,0,"Minimum");t(e,356,0,"70%");t(e,358,0,"0-9.,-bBtTmM","netMinimum","180");t(e,360,0,"50%");t(e,362,0,"29%");t(e,364,0,"Maximum");t(e,366,0,"70%");t(e,368,0,"0-9.,-bBtTmM","netMaximum","180");t(e,370,0,"10%","5"),t(e,372,0);t(e,374,0,"15%");t(e,376,0,"Filter Condition:");t(e,378,0,"85%");t(e,380,0,"100%","70","false");t(e,382,0,"Group member accounts");t(e,384,0,"100%","100%","10");t(e,386,0,"45%","100%");t(e,388,0,"0","100%","100%");t(e,390,0,"100%","8%"),t(e,392,0),t(e,394,0);t(e,396,0,"100%","62%");t(e,398,0,"100%","100%");t(e,400,0,"0","100%","20%","10");t(e,402,0,"100%");t(e,404,0,"29%","Quick Search");t(e,406,0,"70%");t(e,408,0,"100%");t(e,410,0,"29%","List from group");t(e,412,0,"secondgridgroups","277","100");t(e,414,0,"10%","100%");t(e,416,0,"center","middle","100%","76%");t(e,418,0,"buttonMoveRight","40","false",">");t(e,420,0,"buttonMoveAllRight","40","false",">>");t(e,422,0,"buttonMoveLeft","40","false","<");t(e,424,0,"buttonMoveAllLeft","40","false","<<");t(e,426,0,"45%","100%");t(e,428,0,"0","100%","100%");t(e,430,0,"100%","8%"),t(e,432,0),t(e,434,0);t(e,436,0,"100%","62%");t(e,438,0,"100%","100%");t(e,440,0,"100%","20%","10");t(e,442,0,"30%","Quick Search");t(e,444,0,"70%");t(e,446,0,"100%","5%");t(e,448,0,"100%");t(e,450,0,"80%","5");t(e,452,0,"Save",!0);t(e,454,0,"Cancel");t(e,456,0,"false","Test");t(e,458,0,"4","right","20%");t(e,460,0,"dataExport");t(e,462,0,"helpIcon"),t(e,464,0)},null)}function E(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-ilm-account-group-details",[],null,null,null,H,_)),i.Ib(1,4440064,null,0,h,[u.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var Y=i.Fb("app-ilm-account-group-details",h,E,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);