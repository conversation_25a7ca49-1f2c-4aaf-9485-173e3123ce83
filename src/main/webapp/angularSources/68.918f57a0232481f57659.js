(window.webpackJsonp=window.webpackJsonp||[]).push([[68],{"1KK/":function(e,t,i){"use strict";i.r(t);var n=i("CcnG"),o=i("mrSG"),a=i("ZYCi"),s=i("447K"),r=i("anmp"),l=i("eL+A"),u=(i("EVdn"),i("xRo1"),i("R1Kr"),function(e){function t(t,i,n){var o=e.call(this,i,t)||this;return o.commonService=t,o.element=i,o.router=n,o.jsonReader=new s.L,o.inputData=new s.G(o.commonService),o.baseURL=s.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.requestParams=[],o.moduleId="Predict",o.swtalert=new s.bb(t),o}return o.d(t,e),t.prototype.ngOnInit=function(){this.okButton.label=s.Wb.getPredictMessage("button.ok",null),this.cancelButton.label=s.Wb.getPredictMessage("button.cancel",null),this.cancelButton.toolTip=s.Wb.getPredictMessage("tooltip.cancelbutton",null)},t.prototype.onLoad=function(){var e=this;this.requestParams=[],this.menuAccessId=s.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="scenMaintenance.do?",this.actionMethod="method=displayConfigRecipientsData",this.requestParams.menuAccessId=this.menuAccessId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.dynamicCreation()},t.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},t.prototype.inputDataFault=function(e){this._invalidComms=e.fault.faultString+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail,this.swtalert.show("fault "+this._invalidComms)},t.prototype.closeHandler=function(){s.x.call("close")},t.prototype.inputDataResult=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&(this.jsonReader.isDataBuilding()||(this.fillRolesTabDetails(),this.fillUsersTabDetails())))},t.prototype.fillRolesTabDetails=function(){this.rolesTab.rolesGrid.CustomGrid(this.lastRecievedJSON.configRecipients.rolesGrid.metadata);var e=this.lastRecievedJSON.configRecipients.rolesGrid.rows;e&&e.size>0?(this.rolesTab.rolesGrid.gridData=e,this.rolesTab.rolesGrid.setRowSize=this.jsonReader.getRowSize()):this.rolesTab.rolesGrid.gridData={size:0,row:[]};var t=window.opener.instanceElement.selectedRoles;if(t){for(var i=0;i<this.rolesTab.rolesGrid.dataProvider.length;i++)for(var n=0;n<t.length;n++)this.rolesTab.rolesGrid.gridData[i].role==t[n]&&(this.rolesTab.rolesGrid.dataProvider[i].send="Y");this.rolesTab.rolesGrid.refresh()}},t.prototype.fillUsersTabDetails=function(){this.usersTab.usersGrid.CustomGrid(this.lastRecievedJSON.configRecipients.usersGrid.metadata);var e=this.lastRecievedJSON.configRecipients.usersGrid.rows;e&&e.size>0?(this.usersTab.usersGrid.gridData=e,this.usersTab.usersGrid.setRowSize=this.jsonReader.getRowSize()):this.usersTab.usersGrid.gridData={size:0,row:[]};var t=window.opener.instanceElement.selectedUsers;if(t){for(var i=0;i<this.usersTab.usersGrid.dataProvider.length;i++)for(var n=0;n<t.length;n++)this.usersTab.usersGrid.gridData[i].user==t[n]&&(this.usersTab.usersGrid.dataProvider[i].send="Y");this.usersTab.usersGrid.refresh()}},t.prototype.saveHandler=function(){for(var e=[],t=[],i=[],n=0;n<this.rolesTab.rolesGrid.gridData.length;n++){if("Y"==this.rolesTab.rolesGrid.gridData[n].send){var o=this.rolesTab.rolesGrid.gridData[n].role?this.rolesTab.rolesGrid.gridData[n].role:"",a=this.rolesTab.rolesGrid.gridData[n].name?this.rolesTab.rolesGrid.gridData[n].name:"";i.push({roleId:{clickable:!1,content:o,negative:!1},name:{clickable:!1,content:a,negative:!1}}),e.push(this.rolesTab.rolesGrid.gridData[n].role)}}window.opener.instanceElement.selectedRoles=e;var r=[];for(n=0;n<this.usersTab.usersGrid.gridData.length;n++){if("Y"==this.usersTab.usersGrid.gridData[n].send){var l=this.usersTab.usersGrid.gridData[n].user?this.usersTab.usersGrid.gridData[n].user:"",u=this.usersTab.usersGrid.gridData[n].name?this.usersTab.usersGrid.gridData[n].name:"";r.push({userId:{clickable:!1,content:l,negative:!1},name:{clickable:!1,content:u,negative:!1}}),t.push(this.usersTab.usersGrid.gridData[n].user)}}window.opener.instanceElement.selectedUsers=t,window.opener.instanceElement.refreshParent(r,i),s.x.call("close")},t.prototype.doHelp=function(){s.x.call("help")},t.prototype.printPage=function(){try{s.x.call("printPage")}catch(e){s.Wb.logError(e,this.moduleId,"className","printPage",0)}},t.prototype.keyDownEventHandler=function(e){try{var t=Object(s.ic.getFocus()).name;e.keyCode==s.N.ENTER&&("saveButton"==t?this.saveHandler():"cancelButton"==t&&s.x.call("close"))}catch(i){}},t.prototype.dynamicCreation=function(){this.roles=this.configRecipientsNavigator.addChild(s.Xb),this.users=this.configRecipientsNavigator.addChild(s.Xb),this.roles.label=this.roles.id=s.Wb.getPredictMessage("tab.roles",null),this.users.label=this.users.id=s.Wb.getPredictMessage("tab.users",null),this.rolesTab=this.roles.addChild(r.a),this.usersTab=this.users.addChild(l.a),this.roles.id="roles",this.users.id="users",this.usersTab.parentDocument=this.rolesTab.parentDocument=this,this.rolesTab.height="100%",this.usersTab.height="100%"},t}(s.yb)),c=[{path:"",component:u}],d=(a.l.forChild(c),function(){return function(){}}()),b=i("pMnS"),h=i("RChO"),p=i("t6HQ"),g=i("WFGK"),m=i("5FqG"),R=i("Ip0R"),f=i("gIcY"),w=i("t/Na"),v=i("sE5F"),T=i("OzfB"),D=i("T7CS"),k=i("S7LP"),y=i("6aHO"),C=i("WzUx"),G=i("A7o+"),I=i("zCE2"),S=i("Jg5P"),B=i("3R0m"),_=i("hhbb"),N=i("5rxC"),O=i("Fzqc"),M=i("21Lb"),x=i("hUWP"),L=i("3pJQ"),P=i("V9q+"),J=i("VDKW"),z=i("kXfT"),H=i("BGbe");i.d(t,"ConfigureRecipientsModuleNgFactory",function(){return E}),i.d(t,"RenderType_ConfigureRecipients",function(){return F}),i.d(t,"View_ConfigureRecipients_0",function(){return W}),i.d(t,"View_ConfigureRecipients_Host_0",function(){return K}),i.d(t,"ConfigureRecipientsNgFactory",function(){return U});var E=n.Gb(d,[],function(e){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[b.a,h.a,p.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,U]],[3,n.n],n.J]),n.Rb(4608,R.m,R.l,[n.F,[2,R.u]]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.p,f.p,[]),n.Rb(4608,w.j,w.p,[R.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(e){return[e,new s.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,v.c,v.c,[]),n.Rb(4608,v.g,v.b,[]),n.Rb(5120,v.i,v.j,[]),n.Rb(4608,v.h,v.h,[v.c,v.g,v.i]),n.Rb(4608,v.f,v.a,[]),n.Rb(5120,v.d,v.k,[v.h,v.f]),n.Rb(5120,n.b,function(e,t){return[T.j(e,t)]},[R.c,n.O]),n.Rb(4608,D.a,D.a,[]),n.Rb(4608,k.a,k.a,[]),n.Rb(4608,y.a,y.a,[n.n,n.L,n.B,k.a,n.g]),n.Rb(4608,C.c,C.c,[n.n,n.g,n.B]),n.Rb(4608,C.e,C.e,[C.c]),n.Rb(4608,G.l,G.l,[]),n.Rb(4608,G.h,G.g,[]),n.Rb(4608,G.c,G.f,[]),n.Rb(4608,G.j,G.d,[]),n.Rb(4608,G.b,G.a,[]),n.Rb(4608,G.k,G.k,[G.l,G.h,G.c,G.j,G.b,G.m,G.n]),n.Rb(4608,C.i,C.i,[[2,G.k]]),n.Rb(4608,C.r,C.r,[C.L,[2,G.k],C.i]),n.Rb(4608,C.t,C.t,[]),n.Rb(4608,C.w,C.w,[]),n.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(1073742336,R.b,R.b,[]),n.Rb(1073742336,f.n,f.n,[]),n.Rb(1073742336,f.l,f.l,[]),n.Rb(1073742336,I.a,I.a,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,G.i,G.i,[]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,v.e,v.e,[]),n.Rb(1073742336,_.b,_.b,[]),n.Rb(1073742336,N.b,N.b,[]),n.Rb(1073742336,T.c,T.c,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,M.d,M.d,[]),n.Rb(1073742336,x.c,x.c,[]),n.Rb(1073742336,L.a,L.a,[]),n.Rb(1073742336,P.a,P.a,[[2,T.g],n.O]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,z.a,z.a,[]),n.Rb(1073742336,H.b,H.b,[]),n.Rb(1073742336,s.Tb,s.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,G.m,void 0,[]),n.Rb(256,G.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:u}]]},[])])}),A=[[""]],F=n.Hb({encapsulation:0,styles:A,data:{}});function W(e){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{configRecipientsNavigator:0}),n.Zb(402653184,3,{loadingImage:0}),n.Zb(402653184,4,{okButton:0}),n.Zb(402653184,5,{cancelButton:0}),n.Zb(402653184,6,{usersTab:0}),n.Zb(402653184,7,{rolesTab:0}),n.Zb(402653184,8,{swtModule:0}),n.Zb(402653184,9,{printButton:0}),(e()(),n.Jb(9,0,null,null,22,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var n=!0,o=e.component;"creationComplete"===t&&(n=!1!==o.onLoad()&&n);return n},m.ad,m.hb)),n.Ib(10,4440064,[[8,4],["swtModule",4]],0,s.yb,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),n.Jb(11,0,null,0,20,"VBox",[["height","95%"],["id","vbox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(12,4440064,null,0,s.ec,[n.r,s.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(e()(),n.Jb(13,0,null,0,2,"SwtTabNavigator",[["height","90%"],["width","100%"]],null,null,null,m.id,m.pb)),n.Ib(14,4440064,[[2,4],["configRecipientsNavigator",4]],1,s.Ob,[n.r,s.i,n.k],{width:[0,"width"],height:[1,"height"]},null),n.Zb(603979776,10,{tabChildren:1}),(e()(),n.Jb(16,0,null,0,15,"SwtCanvas",[["id","canvasContainer"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(17,4440064,null,0,s.db,[n.r,s.i],{id:[0,"id"],width:[1,"width"]},null),(e()(),n.Jb(18,0,null,0,5,"HBox",[["height","100%"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(19,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(20,0,null,0,1,"SwtButton",[["id","okButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var n=!0,o=e.component;"click"===t&&(n=!1!==o.saveHandler()&&n);"keyDown"===t&&(n=!1!==o.keyDownEventHandler(i)&&n);return n},m.Mc,m.T)),n.Ib(21,4440064,[[4,4],["okButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),n.Jb(22,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["marginLeft","5"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var n=!0,o=e.component;"click"===t&&(n=!1!==o.closeHandler()&&n);"keyDown"===t&&(n=!1!==o.keyDownEventHandler(i)&&n);return n},m.Mc,m.T)),n.Ib(23,4440064,[[5,4],["cancelButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],marginLeft:[1,"marginLeft"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),n.Jb(24,0,null,0,7,"HBox",[["horizontalAlign","right"],["paddingTop","5"],["width","50%"]],null,null,null,m.Dc,m.K)),n.Ib(25,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(e()(),n.Jb(26,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","configure-recipients"],["id","helpIcon"]],null,[[null,"click"]],function(e,t,i){var n=!0,o=e.component;"click"===t&&(n=!1!==o.doHelp()&&n);return n},m.Wc,m.db)),n.Ib(27,4440064,null,0,s.rb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"}),(e()(),n.Jb(28,0,null,0,1,"SwtButton",[["id","printButton"],["styleName","printIcon"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var n=!0,o=e.component;"click"===t&&(n=!1!==o.printPage()&&n);"keyDown"===t&&(n=!1!==o.keyDownEventHandler(i)&&n);return n},m.Mc,m.T)),n.Ib(29,4440064,[[9,4],["printButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),n.Jb(30,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),n.Ib(31,114688,[[3,4],["loadingImage",4]],0,s.xb,[n.r],null,null)],function(e,t){e(t,10,0,"100%","100%");e(t,12,0,"vbox1","100%","95%","5","5","5","5");e(t,14,0,"100%","90%");e(t,17,0,"canvasContainer","100%");e(t,19,0,"100%","100%");e(t,21,0,"okButton");e(t,23,0,"cancelButton","5","true");e(t,25,0,"right","50%","5");e(t,27,0,"helpIcon","true",!0,"configure-recipients");e(t,29,0,"printButton","printIcon",!0),e(t,31,0)},null)}function K(e){return n.dc(0,[(e()(),n.Jb(0,0,null,null,1,"app-configure-recipients",[],null,null,null,W,F)),n.Ib(1,4440064,null,0,u,[s.i,n.r,a.k],null,null)],function(e,t){e(t,1,0)},null)}var U=n.Fb("app-configure-recipients",u,K,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);