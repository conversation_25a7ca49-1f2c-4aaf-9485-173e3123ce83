(window.webpackJsonp=window.webpackJsonp||[]).push([[126],{B6Wn:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),l=i("ZYCi"),o=i("447K"),r=i("wd/R"),s=i.n(r),u=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.actionMethod="",n.jsonReader=new o.L,n.inputData=new o.G(n.commonService),n.baseURL=o.Wb.getBaseURL(),n.moduleId="Predict",n.logger=null,n.errorLocation=0,n.lastNumber=0,n.swtAlert=new o.bb(e),n}return a.d(e,t),e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.mainGrid=this.dataGridContainer.addChild(o.hb),this.mainGrid.allowMultipleSelection=!0,this.closeButton.label=o.Wb.getPredictMessage("sweep.close",null),this.refreshButton.label=o.Wb.getPredictMessage("button.refresh",null),this.refreshButton.toolTip=o.Wb.getPredictMessage("tooltip.refreshWindow",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.close",null),this.lastRefTimeLabel.text=o.Wb.getPredictMessage("screen.lastRefresh",null),this.startDateLabel.text=o.Wb.getPredictMessage("auditLog.from",null)+"*",this.endDateLabel.text=o.Wb.getPredictMessage("auditLog.to",null)+"*",this.startDateLabel.toolTip=o.Wb.getPredictMessage("tooltip.fromDate",null),this.endDateLabel.toolTip=o.Wb.getPredictMessage("tooltip.toDate",null),this.mainGrid.clientSideSort=!1,this.mainGrid.clientSideFilter=!1,this.mainGrid.lockedColumnCount=1,this.mainGrid.onPaginationChanged=function(e){t.paginationChanged(e)}},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.totalCount=o.x.call("eval","totalCount"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="systemlog.do?",this.actionMethod="method=displayAngular",this.requestParams.selectedSort=null,this.requestParams.selectedFilter=null,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.mainGrid.onFilterChanged=this.updateData.bind(this),this.mainGrid.onSortChanged=this.updateData.bind(this),o.v.subscribe(function(e){t.report(e)})},e.prototype.inputDataResult=function(t){var e,i=0;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&(this.mainGrid.selectable=!1,i=10,this.numstepper.value=Number(t.systemLogList.grid.paging.currentpage),e=t.systemLogList.grid.paging.maxpage,this.numstepper.maximum=Number(e),this.mainGrid.paginationComponent=this.numstepper,e>1?(this.pageBox.visible=!0,this.numstepper.minimum=1,this.numstepper.maximum=e):this.pageBox.visible=!1,i=20,this.dateFormat=this.jsonReader.getSingletons().dateFormat,this.displayedDate=this.jsonReader.getSingletons().displayedDate,this.fromDateChooser.formatString=this.dateFormat.toLowerCase(),this.fromDateChooser.text=this.jsonReader.getSingletons().fromDate.content,i=30,this.displayedDate=this.jsonReader.getSingletons().displayedDate,this.toDateChooser.formatString=this.dateFormat.toLowerCase(),this.toDateChooser.text=this.jsonReader.getSingletons().toDate.content,this.lastRefTime.text=this.jsonReader.getSingletons().lastRefTime.content,!this.jsonReader.isDataBuilding())){console.log("(this.jsonReader.getGridData().row      ",this.jsonReader.getGridData().row);var n={columns:this.lastRecievedJSON.systemLogList.grid.metadata.columns};this.createColumnFilter(n),this.mainGrid.CustomGrid(n),this.mainGrid.refreshFilters();var a=this.lastRecievedJSON.systemLogList.grid.rows;a.size>0?(this.mainGrid.gridData=a,this.mainGrid.setRowSize=this.jsonReader.getSingletons().totalCount):this.mainGrid.gridData={size:0,row:[]},this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")}catch(l){o.Wb.logError(l,o.Wb.PREDICT_MODULE_ID,"SystemLog.ts","inputDataResult",i)}},e.prototype.paginationChanged=function(t){this.numstepper.processing=!0,this.doPaginationChanged()},e.prototype.doPaginationChanged=function(){var t=this;this.requestParams=[];var e=this.lastRecievedJSON.systemLogList.grid.paging.maxpage,i=null,n=this.mainGrid.getFilteredGridColumns(),a=this.mainGrid.getSortedGridColumn();try{this.numstepper.value>0&&this.numstepper.value<=this.numstepper.maximum&&this.numstepper.value!=this.lastNumber&&0!=this.numstepper.value&&(i=this.numstepper.value.toString(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="systemlog.do?",this.actionMethod="method=displayAngular",this.requestParams.currentPage=i,this.requestParams.selectedFromDateChooser=this.fromDateChooser.text,this.requestParams.selectedToDateChooser=this.toDateChooser.text,this.requestParams.maxPage=e,this.requestParams.selectedSort=a,this.requestParams.selectedFilter=n,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)),this.logger.info("method [doPaginationChanged] - END")}catch(l){o.Wb.logError(l,this.moduleId,"ClassName","SystemLog",0)}},e.prototype.updateData=function(){var t=this;this.requestParams=[];var e=this.mainGrid.getFilteredGridColumns(),i=this.mainGrid.getSortedGridColumn(),n=this.lastRecievedJSON.systemLogList.grid.paging.maxpage;this.menuAccessId=o.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="systemlog.do?",this.actionMethod="method=displayAngular",this.requestParams.currentPage=1,this.requestParams.selectedFromDateChooser=this.fromDateChooser.text,this.requestParams.selectedToDateChooser=this.toDateChooser.text,this.requestParams.maxPage=n,this.requestParams.selectedSort=i,this.requestParams.selectedFilter=e,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.setFocusDateField=function(t){var e=0;try{t.setFocus(),e=10}catch(i){this.logger.error("method [setFocusDateField] - error: ",i,"errorLocation: ",e),o.Wb.logError(i,o.Wb.PREDICT_MODULE_ID,"ErrorLocation.ts","setFocusDateField",e)}},e.prototype.validateDateField=function(t){var e=this,i=0;try{var n=void 0,a=o.Wb.getPredictMessage("alert.enterValidDate",null);if(i=10,!t.text)return this.swtAlert.error(a,null,null,null,function(){i=50,e.setFocusDateField(t)}),!1;if(i=20,n=s()(t.text,this.dateFormat.toUpperCase(),!0),i=30,!n.isValid())return this.swtAlert.error(a,null,null,null,function(){i=40,e.setFocusDateField(t)}),!1;if(!this.checkDates())return void this.swtAlert.warning("End Date must be later than Start date");i=60,t.selectedDate=n.toDate(),this.updateData()}catch(l){o.Wb.logError(l,o.Wb.PREDICT_MODULE_ID,"SystemLog.ts","validateDateField",i)}return!0},e.prototype.closeHandler=function(){o.x.call("close")},e.prototype.doHelp=function(){o.x.call("help")},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.enablePrintButton=function(t){this.printButton.enabled=t,this.printButton.buttonMode=t},e.prototype.printPage=function(){try{o.x.call("printPage")}catch(t){o.Wb.logError(t,this.moduleId,"className","printPage",0)}},e.prototype.report=function(t){try{o.x.call("buildExportErrorsURL","exportErrors",t)}catch(e){o.Wb.logError(e,this.moduleId,"ClassName","report",0)}},e.prototype.checkDates=function(){try{var t,e;return this.fromDateChooser.text&&(t=s()(this.fromDateChooser.text,this.dateFormat.toUpperCase(),!0)),this.toDateChooser.text&&(e=s()(this.toDateChooser.text,this.dateFormat.toUpperCase(),!0)),!(!t&&e)&&!(t&&e&&e.isBefore(t))}catch(i){o.Wb.logError(i,this.moduleId,"className","checkDates",this.errorLocation)}},e.prototype.keyDownEventHandler=function(t){},e.prototype.getDataProvider=function(t,e){if(t){t.length||(t=[t]);var i=[];return t.forEach(function(t){var n=t[e]&&t[e].content?t[e].content:"";i.some(function(t){return t.value===n})||i.push({value:n,label:n})}),i.sort(function(t,e){return t.label.localeCompare(e.label)}),i}return[]},e.prototype.createColumnFilter=function(t){var e=t.columns.column.find(function(t){return"logDate_Date"===t.dataelement}),i=this.getDataProvider(this.jsonReader.getGridData().row,"logDate_Date");e.FilterType="MultipleSelect",e.dataProvider=i,e.FilterInputSearch=!0;var n=t.columns.column.find(function(t){return"logDate_Time"===t.dataelement}),a=this.getDataProvider(this.jsonReader.getGridData().row,"logDate_Time");n.FilterType="MultipleSelect",n.dataProvider=a,n.FilterInputSearch=!0;var l=t.columns.column.find(function(t){return"userId"===t.dataelement}),o=this.getDataProvider(this.jsonReader.getGridData().row,"userId");l.FilterType="MultipleSelect",l.dataProvider=o,l.FilterInputSearch=!0;var r=t.columns.column.find(function(t){return"ipAddress"===t.dataelement}),s=this.getDataProvider(this.jsonReader.getGridData().row,"ipAddress");r.FilterType="MultipleSelect",r.dataProvider=s,r.FilterInputSearch=!0;var u=t.columns.column.find(function(t){return"process"===t.dataelement});u&&(u.FilterType="InputSearch");var d=t.columns.column.find(function(t){return"action"===t.dataelement}),h=this.getDataProvider(this.jsonReader.getGridData().row,"action");d.FilterType="MultipleSelect",d.dataProvider=h,d.FilterInputSearch=!0},e}(o.yb),d=[{path:"",component:u}],h=(l.l.forChild(d),function(){return function(){}}()),c=i("pMnS"),b=i("RChO"),p=i("t6HQ"),g=i("WFGK"),m=i("5FqG"),f=i("Ip0R"),R=i("gIcY"),D=i("t/Na"),v=i("sE5F"),C=i("OzfB"),w=i("T7CS"),y=i("S7LP"),S=i("6aHO"),L=i("WzUx"),I=i("A7o+"),P=i("zCE2"),T=i("Jg5P"),F=i("3R0m"),B=i("hhbb"),x=i("5rxC"),G=i("Fzqc"),k=i("21Lb"),J=i("hUWP"),N=i("3pJQ"),_=i("V9q+"),O=i("VDKW"),W=i("kXfT"),M=i("BGbe");i.d(e,"SystemLogModuleNgFactory",function(){return A}),i.d(e,"RenderType_SystemLog",function(){return q}),i.d(e,"View_SystemLog_0",function(){return j}),i.d(e,"View_SystemLog_Host_0",function(){return U}),i.d(e,"SystemLogNgFactory",function(){return z});var A=n.Gb(h,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,b.a,p.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,z]],[3,n.n],n.J]),n.Rb(4608,f.m,f.l,[n.F,[2,f.u]]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.p,R.p,[]),n.Rb(4608,D.j,D.p,[f.c,n.O,D.n]),n.Rb(4608,D.q,D.q,[D.j,D.o]),n.Rb(5120,D.a,function(t){return[t,new o.tb]},[D.q]),n.Rb(4608,D.m,D.m,[]),n.Rb(6144,D.k,null,[D.m]),n.Rb(4608,D.i,D.i,[D.k]),n.Rb(6144,D.b,null,[D.i]),n.Rb(4608,D.f,D.l,[D.b,n.B]),n.Rb(4608,D.c,D.c,[D.f]),n.Rb(4608,v.c,v.c,[]),n.Rb(4608,v.g,v.b,[]),n.Rb(5120,v.i,v.j,[]),n.Rb(4608,v.h,v.h,[v.c,v.g,v.i]),n.Rb(4608,v.f,v.a,[]),n.Rb(5120,v.d,v.k,[v.h,v.f]),n.Rb(5120,n.b,function(t,e){return[C.j(t,e)]},[f.c,n.O]),n.Rb(4608,w.a,w.a,[]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,S.a,S.a,[n.n,n.L,n.B,y.a,n.g]),n.Rb(4608,L.c,L.c,[n.n,n.g,n.B]),n.Rb(4608,L.e,L.e,[L.c]),n.Rb(4608,I.l,I.l,[]),n.Rb(4608,I.h,I.g,[]),n.Rb(4608,I.c,I.f,[]),n.Rb(4608,I.j,I.d,[]),n.Rb(4608,I.b,I.a,[]),n.Rb(4608,I.k,I.k,[I.l,I.h,I.c,I.j,I.b,I.m,I.n]),n.Rb(4608,L.i,L.i,[[2,I.k]]),n.Rb(4608,L.r,L.r,[L.L,[2,I.k],L.i]),n.Rb(4608,L.t,L.t,[]),n.Rb(4608,L.w,L.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,f.b,f.b,[]),n.Rb(1073742336,R.n,R.n,[]),n.Rb(1073742336,R.l,R.l,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,F.a,F.a,[]),n.Rb(1073742336,I.i,I.i,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,D.e,D.e,[]),n.Rb(1073742336,D.d,D.d,[]),n.Rb(1073742336,v.e,v.e,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,C.c,C.c,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,k.d,k.d,[]),n.Rb(1073742336,J.c,J.c,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,_.a,_.a,[[2,C.g],n.O]),n.Rb(1073742336,O.b,O.b,[]),n.Rb(1073742336,W.a,W.a,[]),n.Rb(1073742336,M.b,M.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,h,h,[]),n.Rb(256,D.n,"XSRF-TOKEN",[]),n.Rb(256,D.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,I.m,void 0,[]),n.Rb(256,I.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:u}]]},[])])}),E=[[""]],q=n.Hb({encapsulation:0,styles:E,data:{}});function j(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{startDateLabel:0}),n.Zb(402653184,3,{endDateLabel:0}),n.Zb(402653184,4,{lastRefTimeLabel:0}),n.Zb(402653184,5,{lastRefTime:0}),n.Zb(402653184,6,{dataGridContainer:0}),n.Zb(402653184,7,{refreshButton:0}),n.Zb(402653184,8,{closeButton:0}),n.Zb(402653184,9,{printButton:0}),n.Zb(402653184,10,{loadingImage:0}),n.Zb(402653184,11,{fromDateChooser:0}),n.Zb(402653184,12,{toDateChooser:0}),n.Zb(402653184,13,{numstepper:0}),n.Zb(402653184,14,{pageBox:0}),(t()(),n.Jb(14,0,null,null,53,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,a=t.component;"creationComplete"===e&&(n=!1!==a.onLoad()&&n);return n},m.ad,m.hb)),n.Ib(15,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(16,0,null,0,51,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(17,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(18,0,null,0,21,"SwtCanvas",[["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(19,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(20,0,null,0,19,"HBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(21,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(t()(),n.Jb(22,0,null,0,17,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(23,4440064,null,0,o.ec,[n.r,o.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(24,0,null,0,15,"HBox",[["height","25"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(25,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(26,0,null,0,9,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(27,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(28,0,null,0,1,"SwtLabel",[["id","startDateLabel"],["styleName","labelBold"]],null,null,null,m.Yc,m.fb)),n.Ib(29,4440064,[[2,4],["startDateLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),n.Jb(30,0,null,0,1,"SwtDateField",[["id","fromDateChooser"],["width","70"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.validateDateField(n.Tb(t,31))&&a);return a},m.Tc,m.ab)),n.Ib(31,4308992,[[11,4],["fromDateChooser",4]],0,o.lb,[n.r,o.i,n.T],{id:[0,"id"],width:[1,"width"]},{changeEventOutPut:"change"}),(t()(),n.Jb(32,0,null,0,1,"SwtLabel",[["id","endDateLabel"],["styleName","labelBold"]],null,null,null,m.Yc,m.fb)),n.Ib(33,4440064,[[3,4],["endDateLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),n.Jb(34,0,null,0,1,"SwtDateField",[["id","toDateChooser"],["width","70"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.validateDateField(n.Tb(t,35))&&a);return a},m.Tc,m.ab)),n.Ib(35,4308992,[[12,4],["toDateChooser",4]],0,o.lb,[n.r,o.i,n.T],{id:[0,"id"],width:[1,"width"]},{changeEventOutPut:"change"}),(t()(),n.Jb(36,0,null,0,3,"HBox",[["horizontalAlign","right"],["visible","false"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(37,4440064,[[14,4],["pageBox",4]],0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],visible:[2,"visible"]},null),(t()(),n.Jb(38,0,null,0,1,"SwtCommonGridPagination",[],null,null,null,m.Qc,m.Y)),n.Ib(39,2211840,[[13,4],["numstepper",4]],0,o.ib,[D.c,n.r],null,null),(t()(),n.Jb(40,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","dataGridContainer"],["marginTop","10"],["paddingBottom","5"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(41,4440064,[[6,4],["dataGridContainer",4]],0,o.db,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],paddingBottom:[4,"paddingBottom"],marginTop:[5,"marginTop"],border:[6,"border"]},null),(t()(),n.Jb(42,0,null,0,25,"SwtCanvas",[["height","35"],["id","canvasButtons"],["marginTop","5"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(43,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),n.Jb(44,0,null,0,23,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(45,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(46,0,null,0,5,"HBox",[["paddingLeft","5"],["paddingTop","2"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(47,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"]},null),(t()(),n.Jb(48,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.updateData()&&n);return n},m.Mc,m.T)),n.Ib(49,4440064,[[7,4],["refreshButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(50,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.closeHandler()&&n);return n},m.Mc,m.T)),n.Ib(51,4440064,[[8,4],["closeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(52,0,null,0,15,"HBox",[["horizontalAlign","right"],["paddingTop","2"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(53,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),n.Jb(54,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,m.Yc,m.fb)),n.Ib(55,4440064,[["dataBuildingText",4]],0,o.vb,[n.r,o.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(56,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,m.Yc,m.fb)),n.Ib(57,4440064,[["lostConnectionText",4]],0,o.vb,[n.r,o.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(58,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,m.Yc,m.fb)),n.Ib(59,4440064,[[4,4],["lastRefTimeLabel",4]],0,o.vb,[n.r,o.i],{fontWeight:[0,"fontWeight"]},null),(t()(),n.Jb(60,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,m.Yc,m.fb)),n.Ib(61,4440064,[[5,4],["lastRefTime",4]],0,o.vb,[n.r,o.i],{fontWeight:[0,"fontWeight"]},null),(t()(),n.Jb(62,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"],["paddingTop","2"]],null,null,null,m.Dc,m.K)),n.Ib(63,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(64,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doHelp()&&n);return n},m.Wc,m.db)),n.Ib(65,4440064,[["helpIcon",4]],0,o.rb,[n.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(66,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),n.Ib(67,114688,[[10,4],["loadingImage",4]],0,o.xb,[n.r],null,null)],function(t,e){t(e,15,0,"100%","100%");t(e,17,0,"100%","100%","5","5","5","5");t(e,19,0,"100%");t(e,21,0,"100%","100%","5","5");t(e,23,0,"0","100%","100%");t(e,25,0,"100%","25");t(e,27,0,"100%");t(e,29,0,"startDateLabel","labelBold");t(e,31,0,"fromDateChooser","70");t(e,33,0,"endDateLabel","labelBold");t(e,35,0,"toDateChooser","70");t(e,37,0,"right","100%","false"),t(e,39,0);t(e,41,0,"dataGridContainer","canvasWithGreyBorder","100%","100%","5","10","false");t(e,43,0,"canvasButtons","100%","35","5");t(e,45,0,"100%");t(e,47,0,"100%","2","5");t(e,49,0,"refreshButton");t(e,51,0,"closeButton","70");t(e,53,0,"right","100%","2");t(e,55,0,"false","red");t(e,57,0,"false","red");t(e,59,0,"normal");t(e,61,0,"normal");t(e,63,0,"right","2","10");t(e,65,0,"helpIcon"),t(e,67,0)},null)}function U(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-error-log",[],null,null,null,j,q)),n.Ib(1,4440064,null,0,u,[o.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var z=n.Fb("app-error-log",u,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);