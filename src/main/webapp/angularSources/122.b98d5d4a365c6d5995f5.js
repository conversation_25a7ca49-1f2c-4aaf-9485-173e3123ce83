(window.webpackJsonp=window.webpackJsonp||[]).push([[122],{NLGN:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),n=i("mrSG"),s=i("447K"),a=i("ZYCi"),o=i("wd/R"),d=i.n(o),r=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.logger=null,l.moduleId="Predict",l.jsonReader=new s.L,l.menuAccessId=null,l.maintainAnyReportHistAccess=!1,l.fileId=null,l.comboOpen=!1,l.screenName="Scheduled Report History",l.versionNumber="1.00.00",l.releaseDate="10 September 2019",l.screenVersion=new s.V(l.commonService),l.inputData=new s.G(l.commonService),l.checkAccess=new s.G(l.commonService),l.baseURL=s.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.previousSelectedIndex=-1,l.logic=new s.h,l.swtAlert=new s.bb(e),l.logger=new s.R("SchedReportHistory",e.httpclient),l.logger.info("method [constructor] - START/END"),window.Main=l,l}return n.d(e,t),e.prototype.onLoad=function(){var t=this;try{this.logger.info("method [onLoad] - START "),this.schedReportHistGrid=this.canvasGrid.addChild(s.hb),this.schedReportHistGrid.uniqueColumn="fileId",this.menuAccessId=s.x.call("eval","menuAccessId"),this.maintainAnyReportHistAccess="true"==s.x.call("eval","maintainAnyReportHistAccess"),this.dateFormat=s.x.call("eval","dateFormat"),this.dateFormatValue=s.x.call("eval","dateFormatValue"),this.testDate=s.x.call("eval","dbDate"),this.startDate.formatString=this.dateFormatValue,this.endDate.formatString=this.dateFormatValue,this.schedReportHistGrid.onFilterChanged=this.disableButtons.bind(this),this.schedReportHistGrid.onSortChanged=this.disableButtons.bind(this),this.schedReportHistGrid.onRowClick=function(e){t.cellLogic()},this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="schedReportHist.do?method=",this.actionMethod="displaySchedReportHistList",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){this.logger.error("method [onLoad] - error ",e)}},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.inputDataFault=function(){var t=s.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(t)},e.prototype.inputDataResult=function(t){this.logger.info("method [inputDataResult] - START ");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus())if(this.jsonReader.isDataBuilding())this.swtAlert.error(s.Wb.getPredictMessage("alert.generic_exception"));else{var e=t.schedreporthist.grid.metadata;this.schedReportHistGrid.moduleId=this.moduleId,this.schedReportHistGrid.CustomGrid(e),this.schedReportHistGrid.gridData=this.jsonReader.getGridData(),this.schedReportHistGrid.setRowSize=this.jsonReader.getRowSize(),this.schedReportHistGrid.colWidthURL(this.baseURL+"schedReportHist.do?&screenName=schedReportHist"),this.schedReportHistGrid.colOrderURL(this.baseURL+"schedReportHist.do?&screenName=schedReportHist"),this.schedReportHistGrid.saveWidths=!0,this.schedReportHistGrid.saveColumnOrder=!0,this.reportJobsCombo.setComboData(this.jsonReader.getSelects(),!1),this.reportTypesCombo.setComboData(this.jsonReader.getSelects(),!1),this.startDate.text=this.testDate,this.endDate.text=this.testDate,this.cellLogic(),this.endDateLabel.visible=this.endDateLabel.includeInLayout=!1,this.dateItem.visible=!1}}catch(i){this.logger.error("method [inputDataResult] - error ",i)}},e.prototype.inputDataRefresh=function(t){this.logger.info("method [inputDataRefresh] - START enterrr");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)){if(this.jsonReader.getRequestReplyStatus())if(this.jsonReader.isDataBuilding()){var e=s.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(e)}else null!=this.jsonReader.getSingletons().deleteResult&&"SUCCESS"==this.jsonReader.getSingletons().deleteResult?this.swtAlert.show("File deleted succesfully"):null!=this.jsonReader.getSingletons().deleteResult&&"NO_ACCESS"==this.jsonReader.getSingletons().deleteResult&&(this.swtAlert.invalid(s.Wb.getPredictMessage("alert.schedreporthist.noAccessToFeature",null)),this.maintainAnyReportHistAccess=!1),this.reportTypesCombo.setComboData(this.jsonReader.getSelects(),!1),this.schedReportHistGrid.gridData=this.jsonReader.getGridData(),this.schedReportHistGrid.setRowSize=this.jsonReader.getRowSize(),-1===this.schedReportHistGrid.selectedIndex?this.disableOrEnableButtons(!1):this.fileId=this.schedReportHistGrid.dataProvider[this.schedReportHistGrid.selectedIndex].fileId;this.prevRecievedJSON=this.lastRecievedJSON}}catch(i){this.logger.error("method [inputDataRefresh] - error ",i)}},e.prototype.refreshDetails=function(t){var e=this;this.logger.info("method [refreshDetails] - START ");var i=!1;null!=t&&t.target.id==this.reportJobsCombo.id&&(i=!0);var l=this.reportJobsCombo.selectedValue,n=this.reportTypesCombo.selectedValue;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="schedReportHist.do?method=",this.actionMethod="displaySchedReportHistList",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams=[],this.requestParams.reportJobId=l,this.requestParams.reportTypeId=n,this.requestParams.reportJobChanged=i,this.requestParams.fromDate=this.startDate.text,this.requestParams.toDate=this.endDate.text,this.requestParams.isDateRange="D"==this.dateSingleOrRange.selectedValue,this.inputData.send(this.requestParams),this.previousSelectedIndex=this.schedReportHistGrid.selectedIndex},e.prototype.changeViewSchedReportHist=function(t){this.actionMethod="displaySchedReportHist",this.actionMethod=this.actionMethod+"&fileId="+this.fileId,this.screenName=t?"changeScreen":"viewScreen",this.actionMethod=this.actionMethod+"&screenName="+this.screenName,s.x.call("openChildWindow",this.actionMethod)},e.prototype.deleteSchedReportHist=function(){this.swtAlert.confirm(s.Wb.getPredictMessage("confirm.delete",null),s.Wb.getPredictMessage("alert.deletion.confirm",null),s.bb.YES|s.bb.NO,null,this.deletionDecision.bind(this),null)},e.prototype.deletionDecision=function(t){var e=this;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionMethod="deleteSchedReportHist",this.requestParams.fileId=this.fileId,t.detail==s.bb.YES&&(this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.closeHandler=function(){s.x.call("close")},e.prototype.cellLogic=function(){this.logger.info("method [cellLogic] - START "),this.schedReportHistGrid.selectedIndex>-1?(this.fileId=this.schedReportHistGrid.selectedItem.fileId.content,this.disableOrEnableButtons(!0)):this.disableOrEnableButtons(!1),this.logger.info("method [cellLogic] - END ")},e.prototype.disableOrEnableButtons=function(t){this.logger.info("method [disableOrEnableButtons] - START ");var e=parseInt(this.menuAccessId,10);this.schedReportHistGrid.selectedIndex;if(t){var i=this.schedReportHistGrid.selectedItem.fileName.content;s.Z.isEmpty(i)?(this.resendMailButton.enabled=!1,this.downloadButton.enabled=!1):(this.resendMailButton.enabled=0==e&&1==this.maintainAnyReportHistAccess,this.downloadButton.enabled=e<2),this.deleteButton.enabled=0==e&&1==this.maintainAnyReportHistAccess,this.viewButton.enabled=e<2}else this.deleteButton.enabled=!1,this.viewButton.enabled=!1,this.resendMailButton.enabled=!1,this.downloadButton.enabled=!1;this.logger.info("method [disableOrEnableButtons] - END ")},e.prototype.disableButtons=function(){-1==this.schedReportHistGrid.selectedIndex?this.disableOrEnableButtons(!1):this.disableOrEnableButtons(!0)},e.prototype.validateDateFieldValue=function(t){this.validateDateField(this.startDate)?"D"==this.dateSingleOrRange.selectedValue?this.checkDateRangeValueAndUpdateGrid():this.refreshDetails(null):console.log("cccc")},e.prototype.validateDateField=function(t){try{var e=void 0,i=s.Wb.getPredictMessage("dashboardDetails.alert.dateFormat",null);if(t.text&&!(e=d()(t.text,this.dateFormatValue.toUpperCase(),!0)).isValid())return this.swtAlert.warning(i+this.dateFormatValue.toUpperCase()),!1;t.selectedDate=e.toDate()}catch(l){}return!0},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.dateTypeChanged=function(t){this.logger.info("method [dateTypeChanged] - START "),"singleDate"==t?(this.startDate.selectedDate=new Date(s.j.parseDate(this.testDate,this.dateFormatValue.toUpperCase())),this.endDate.selectedDate=null,this.endDateLabel.visible=!1,this.dateItem.visible=!1,this.dateSingleOrRange.selectedValue="S"):"dateRange"==t&&(this.startDate.selectedDate=new Date(s.j.parseDate(this.testDate,this.dateFormatValue.toUpperCase())),this.endDate.selectedDate=new Date(s.j.parseDate(this.testDate,this.dateFormatValue.toUpperCase())),this.endDateLabel.visible=!0,this.dateItem.visible=!0,this.dateSingleOrRange.selectedValue="D"),this.refreshDetails(event),this.logger.info("method [dateTypeChanged] - END ")},e.prototype.checkDateRangeValueAndUpdateGrid=function(){var t=!0;null!=this.startDate.selectedDate&&null!=this.endDate.selectedDate&&(t=s.x.call("comparedates",this.startDate.text,this.endDate.text,this.dateFormat,"Start Date","End Date")),t&&this.refreshDetails(null)},e.prototype.checkDates=function(){try{var t=void 0,e=void 0,i=!0;return this.startDate.text&&(t=d()(this.startDate.text,this.dateFormatValue.toUpperCase(),!0)),this.endDate.text&&(e=d()(this.endDate.text,this.dateFormatValue.toUpperCase(),!0)),!t&&e&&(i=!1),t&&e&&e.isBefore(t)&&(i=!1),i}catch(l){}},e.prototype.resendMailButton_clickHandler=function(t){var e=this;this.checkAccess.cbStart=this.startOfComms.bind(this),this.checkAccess.cbStop=this.endOfComms.bind(this),this.checkAccess.cbResult=function(t){e.resendMailDataResult(t)},this.checkAccess.cbFault=this.inputDataFault.bind(this),this.checkAccess.encodeURL=!1,this.actionMethod="checkFullAccessToTheScreen",this.checkAccess.url=this.baseURL+this.actionPath+this.actionMethod,this.checkAccess.send()},e.prototype.resendMailDataResult=function(t){this.logger.info("method [resendMailDataResult] - START ");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if(this.jsonReader.getRequestReplyStatus()){this.schedReportHistGrid.selectedIndex;var e=this.schedReportHistGrid.selectedItem.scheduleId.content,i=this.schedReportHistGrid.selectedItem.outputLocation.content,l=this.schedReportHistGrid.selectedItem.fileName.content,n=this.schedReportHistGrid.selectedItem.fileId.content;s.Z.isEmpty(i)||s.Z.isEmpty(l)||s.x.call("openDistListScreen",e,s.t.encode64(i+"\\"+l),n)}else this.swtAlert.invalid(s.Wb.getPredictMessage("alert.schedreporthist.noAccessToFeature",null)),this.maintainAnyReportHistAccess=!1}catch(a){this.logger.error("method [resendMailDataResult] - error ",a)}},e.prototype.downloadButton_clickHandler=function(t){var e=this,i=[];this.schedReportHistGrid.selectedIndex>-1&&(this.fileId=this.schedReportHistGrid.selectedItem.fileId.content,this.checkAccess.cbStart=this.startOfComms.bind(this),this.checkAccess.cbStop=this.endOfComms.bind(this),this.checkAccess.cbResult=function(t){e.startDownloadAction(t)},this.checkAccess.cbFault=this.inputDataFault.bind(this),this.checkAccess.encodeURL=!1,this.actionMethod="checkIfFileExist",this.checkAccess.url=this.baseURL+this.actionPath+this.actionMethod,i.fileId=this.fileId,this.checkAccess.send(i))},e.prototype.startDownloadAction=function(t){this.logger.info("method [startDownloadAction] - START ");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if(this.jsonReader.getRequestReplyStatus())this.schedReportHistGrid.selectedIndex>-1&&(this.fileId=this.schedReportHistGrid.selectedItem.fileId.content,this.actionMethod="downloadSchedReportHist",s.x.call("onExport",this.fileId,this.actionMethod));else this.swtAlert.error(s.Wb.getPredictMessage("alert.mail.fileNoutFound",null))}catch(e){this.logger.error("method [startDownloadAction] - error ",e)}},e.prototype.ngOnInit=function(){this.refreshButton.label=s.Wb.getPredictMessage("button.genericdisplaymonitor.refresh",null),this.refreshButton.toolTip=s.Wb.getPredictMessage("tooltip.refreshWindow",null),this.downloadButton.label=s.Wb.getPredictMessage("button.schedreporthist.download",null),this.downloadButton.toolTip=s.Wb.getPredictMessage("button.tooltip.schedreporthist.download",null),this.resendMailButton.label=s.Wb.getPredictMessage("button.schedreporthist.resendMail",null),this.resendMailButton.toolTip=s.Wb.getPredictMessage("button.schedreporthist.resendMail",null),this.viewButton.label=s.Wb.getPredictMessage("button.details",null),this.viewButton.toolTip=s.Wb.getPredictMessage("button.tooltip.schedreporthist.details",null),this.deleteButton.label=s.Wb.getPredictMessage("button.delete",null),this.deleteButton.toolTip=s.Wb.getPredictMessage("tooltip.deleteSeletedFile",null),this.closeButton.label=s.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=s.Wb.getPredictMessage("tooltip.close",null),this.singleDate.label=s.Wb.getPredictMessage("button.schedReportHist.singleDate",null),this.singleDate.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.singleDate",null),this.dateRange.label=s.Wb.getPredictMessage("button.schedReportHist.dateRange",null),this.dateRange.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.dateRange",null),this.dateLabel.text="Date",this.startDateLabel.text="From",this.startDate.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.startDate",null),this.endDateLabel.text="To",this.endDate.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.endDate",null),this.reportJobsCombo.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.reportjob",null),this.reportTypesCombo.toolTip=s.Wb.getPredictMessage("button.tooltip.schedReportHist.reporttype",null),this.initializeMenus()},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var t=new s.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(){this.showJSONPopup=s.Eb.createPopUp(this,s.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.doHelp=function(){s.x.call("help")},e}(s.yb),h=[{path:"",component:r}],c=(a.l.forChild(h),function(){return function(){}}()),u=i("pMnS"),b=i("RChO"),p=i("t6HQ"),g=i("WFGK"),m=i("5FqG"),R=i("Ip0R"),w=i("gIcY"),f=i("t/Na"),D=i("sE5F"),S=i("OzfB"),I=i("T7CS"),v=i("S7LP"),y=i("6aHO"),B=i("WzUx"),J=i("A7o+"),C=i("zCE2"),A=i("Jg5P"),N=i("3R0m"),O=i("hhbb"),T=i("5rxC"),H=i("Fzqc"),L=i("21Lb"),M=i("hUWP"),k=i("3pJQ"),G=i("V9q+"),x=i("VDKW"),P=i("kXfT"),F=i("BGbe");i.d(e,"SchedReportHistoryModuleNgFactory",function(){return W}),i.d(e,"RenderType_SchedReportHistory",function(){return V}),i.d(e,"View_SchedReportHistory_0",function(){return E}),i.d(e,"View_SchedReportHistory_Host_0",function(){return U}),i.d(e,"SchedReportHistoryNgFactory",function(){return j});var W=l.Gb(c,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[u.a,b.a,p.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,j]],[3,l.n],l.J]),l.Rb(4608,R.m,R.l,[l.F,[2,R.u]]),l.Rb(4608,w.c,w.c,[]),l.Rb(4608,w.p,w.p,[]),l.Rb(4608,f.j,f.p,[R.c,l.O,f.n]),l.Rb(4608,f.q,f.q,[f.j,f.o]),l.Rb(5120,f.a,function(t){return[t,new s.tb]},[f.q]),l.Rb(4608,f.m,f.m,[]),l.Rb(6144,f.k,null,[f.m]),l.Rb(4608,f.i,f.i,[f.k]),l.Rb(6144,f.b,null,[f.i]),l.Rb(4608,f.f,f.l,[f.b,l.B]),l.Rb(4608,f.c,f.c,[f.f]),l.Rb(4608,D.c,D.c,[]),l.Rb(4608,D.g,D.b,[]),l.Rb(5120,D.i,D.j,[]),l.Rb(4608,D.h,D.h,[D.c,D.g,D.i]),l.Rb(4608,D.f,D.a,[]),l.Rb(5120,D.d,D.k,[D.h,D.f]),l.Rb(5120,l.b,function(t,e){return[S.j(t,e)]},[R.c,l.O]),l.Rb(4608,I.a,I.a,[]),l.Rb(4608,v.a,v.a,[]),l.Rb(4608,y.a,y.a,[l.n,l.L,l.B,v.a,l.g]),l.Rb(4608,B.c,B.c,[l.n,l.g,l.B]),l.Rb(4608,B.e,B.e,[B.c]),l.Rb(4608,J.l,J.l,[]),l.Rb(4608,J.h,J.g,[]),l.Rb(4608,J.c,J.f,[]),l.Rb(4608,J.j,J.d,[]),l.Rb(4608,J.b,J.a,[]),l.Rb(4608,J.k,J.k,[J.l,J.h,J.c,J.j,J.b,J.m,J.n]),l.Rb(4608,B.i,B.i,[[2,J.k]]),l.Rb(4608,B.r,B.r,[B.L,[2,J.k],B.i]),l.Rb(4608,B.t,B.t,[]),l.Rb(4608,B.w,B.w,[]),l.Rb(10********,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(10********,R.b,R.b,[]),l.Rb(10********,w.n,w.n,[]),l.Rb(10********,w.l,w.l,[]),l.Rb(10********,C.a,C.a,[]),l.Rb(10********,A.a,A.a,[]),l.Rb(10********,w.e,w.e,[]),l.Rb(10********,N.a,N.a,[]),l.Rb(10********,J.i,J.i,[]),l.Rb(10********,B.b,B.b,[]),l.Rb(10********,f.e,f.e,[]),l.Rb(10********,f.d,f.d,[]),l.Rb(10********,D.e,D.e,[]),l.Rb(10********,O.b,O.b,[]),l.Rb(10********,T.b,T.b,[]),l.Rb(10********,S.c,S.c,[]),l.Rb(10********,H.a,H.a,[]),l.Rb(10********,L.d,L.d,[]),l.Rb(10********,M.c,M.c,[]),l.Rb(10********,k.a,k.a,[]),l.Rb(10********,G.a,G.a,[[2,S.g],l.O]),l.Rb(10********,x.b,x.b,[]),l.Rb(10********,P.a,P.a,[]),l.Rb(10********,F.b,F.b,[]),l.Rb(10********,s.Tb,s.Tb,[]),l.Rb(10********,c,c,[]),l.Rb(256,f.n,"XSRF-TOKEN",[]),l.Rb(256,f.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,J.m,void 0,[]),l.Rb(256,J.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:r}]]},[])])}),_=[[""]],V=l.Hb({encapsulation:0,styles:_,data:{}});function E(t){return l.dc(0,[l.Zb(*********,1,{_container:0}),l.Zb(*********,2,{endDateLabel:0}),l.Zb(*********,3,{selectedAccount:0}),l.Zb(*********,4,{lblReportType:0}),l.Zb(*********,5,{selectedAttribute:0}),l.Zb(*********,6,{startDateLabel:0}),l.Zb(*********,7,{dateLabel:0}),l.Zb(*********,8,{refreshButton:0}),l.Zb(*********,9,{downloadButton:0}),l.Zb(*********,10,{resendMailButton:0}),l.Zb(*********,11,{viewButton:0}),l.Zb(*********,12,{deleteButton:0}),l.Zb(*********,13,{closeButton:0}),l.Zb(*********,14,{helpButton:0}),l.Zb(*********,15,{loadingImage:0}),l.Zb(*********,16,{canvasGrid:0}),l.Zb(*********,17,{reportJobsCombo:0}),l.Zb(*********,18,{reportTypesCombo:0}),l.Zb(*********,19,{startDate:0}),l.Zb(*********,20,{endDate:0}),l.Zb(*********,21,{dateSingleOrRange:0}),l.Zb(*********,22,{singleDate:0}),l.Zb(*********,23,{dateRange:0}),l.Zb(*********,24,{dateItem:0}),(t()(),l.Jb(24,0,null,null,98,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,n=t.component;"creationComplete"===e&&(l=!1!==n.onLoad()&&l);return l},m.ad,m.hb)),l.Ib(25,4440064,null,0,s.yb,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(26,0,null,0,96,"VBox",[["height","100%"],["minWidth","670"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(27,4440064,null,0,s.ec,[l.r,s.i,l.T],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),l.Jb(28,0,null,0,68,"SwtCanvas",[["height","15%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(29,4440064,null,0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(30,0,null,0,66,"VBox",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(31,4440064,null,0,s.ec,[l.r,s.i,l.T],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),l.Jb(32,0,null,0,64,"Grid",[["height","100%"],["paddingLeft","10"],["width","100%"]],null,null,null,m.Cc,m.H)),l.Ib(33,4440064,null,0,s.z,[l.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),l.Jb(34,0,null,0,13,"GridRow",[["height","25%"],["width","100%"]],null,null,null,m.Bc,m.J)),l.Ib(35,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(36,0,null,0,3,"GridItem",[["width","150"]],null,null,null,m.Ac,m.I)),l.Ib(37,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(38,0,null,0,1,"SwtLabel",[["id","lblReportJob"],["styleName","labelBold"],["text","Report Job"]],null,null,null,m.Yc,m.fb)),l.Ib(39,4440064,[["lblReportJob",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"]},null),(t()(),l.Jb(40,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,m.Ac,m.I)),l.Ib(41,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(42,0,null,0,1,"SwtComboBox",[["dataLabel","reportJobs"],["id","reportJobsCombo"],["width","550"]],null,[[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,s=t.component;"window:mousewheel"===e&&(n=!1!==l.Tb(t,43).mouseWeelEventHandler(i.target)&&n);"close"===e&&(n=!1!==s.closedCombo(i)&&n);"change"===e&&(n=!1!==s.refreshDetails(i)&&n);return n},m.Pc,m.W)),l.Ib(43,4440064,[[17,4],["reportJobsCombo",4]],0,s.gb,[l.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{close_:"close",change_:"change"}),(t()(),l.Jb(44,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,m.Ac,m.I)),l.Ib(45,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(46,0,null,0,1,"SwtLabel",[["id","selectedAccount"],["styleName","labelLeft"]],null,null,null,m.Yc,m.fb)),l.Ib(47,4440064,[[3,4],["selectedAccount",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),l.Jb(48,0,null,0,13,"GridRow",[["height","25%"],["width","100%"]],null,null,null,m.Bc,m.J)),l.Ib(49,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(50,0,null,0,3,"GridItem",[["height","100%"],["width","150"]],null,null,null,m.Ac,m.I)),l.Ib(51,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(52,0,null,0,1,"SwtLabel",[["id","lblReportType"],["styleName","labelBold"],["text","Report Type"]],null,null,null,m.Yc,m.fb)),l.Ib(53,4440064,[[4,4],["lblReportType",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"]},null),(t()(),l.Jb(54,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,m.Ac,m.I)),l.Ib(55,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(56,0,null,0,1,"SwtComboBox",[["dataLabel","reportTypes"],["id","reportTypesCombo"],["width","400"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,s=t.component;"window:mousewheel"===e&&(n=!1!==l.Tb(t,57).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==s.refreshDetails(i)&&n);return n},m.Pc,m.W)),l.Ib(57,4440064,[[18,4],["reportTypesCombo",4]],0,s.gb,[l.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(58,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,m.Ac,m.I)),l.Ib(59,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(60,0,null,0,1,"SwtLabel",[["id","selectedAttribute"],["styleName","labelLeft"]],null,null,null,m.Yc,m.fb)),l.Ib(61,4440064,[[5,4],["selectedAttribute",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),l.Jb(62,0,null,0,14,"GridRow",[["height","25%"],["width","100%"]],null,null,null,m.Bc,m.J)),l.Ib(63,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(64,0,null,0,3,"GridItem",[["height","100%"],["width","150"]],null,null,null,m.Ac,m.I)),l.Ib(65,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(66,0,null,0,1,"SwtLabel",[["id","dateLabel"],["styleName","labelBold"],["text","Date"]],null,null,null,m.Yc,m.fb)),l.Ib(67,4440064,[[7,4],["dateLabel",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"]},null),(t()(),l.Jb(68,0,null,0,8,"GridItem",[["height","100%"],["width","100%"]],null,null,null,m.Ac,m.I)),l.Ib(69,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(70,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","dateSingleOrRange"]],null,null,null,m.ed,m.lb)),l.Ib(71,4440064,[[21,4],["dateSingleOrRange",4]],1,s.Hb,[f.c,l.r,s.i],{id:[0,"id"],align:[1,"align"]},null),l.Zb(603979776,25,{radioItems:1}),(t()(),l.Jb(73,0,null,0,1,"SwtRadioItem",[["groupName","dateSingleOrRange"],["id","singleDate"],["selected","true"],["value","S"],["width","200"]],null,[[null,"change"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.dateTypeChanged("singleDate")&&l);return l},m.fd,m.mb)),l.Ib(74,4440064,[[25,4],[22,4],["singleDate",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},{change_:"change"}),(t()(),l.Jb(75,0,null,0,1,"SwtRadioItem",[["groupName","dateSingleOrRange"],["id","dateRange"],["value","D"],["width","200"]],null,[[null,"change"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.dateTypeChanged("dateRange")&&l);return l},m.fd,m.mb)),l.Ib(76,4440064,[[25,4],[23,4],["dateRange",4]],0,s.Ib,[l.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},{change_:"change"}),(t()(),l.Jb(77,0,null,0,19,"GridRow",[["height","25%"],["width","100%"]],null,null,null,m.Bc,m.J)),l.Ib(78,4440064,null,0,s.B,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(79,0,null,0,1,"GridItem",[["height","100%"],["width","150"]],null,null,null,m.Ac,m.I)),l.Ib(80,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(81,0,null,0,3,"GridItem",[["width","40"]],null,null,null,m.Ac,m.I)),l.Ib(82,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(83,0,null,0,1,"SwtLabel",[["id","startDateLabel"],["styleName","labelBold"]],null,null,null,m.Yc,m.fb)),l.Ib(84,4440064,[[6,4],["startDateLabel",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"]},null),(t()(),l.Jb(85,0,null,0,3,"GridItem",[["width","14%"]],null,null,null,m.Ac,m.I)),l.Ib(86,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(87,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.validateDateFieldValue(i)&&l);return l},m.Tc,m.ab)),l.Ib(88,4308992,[[19,4],["startDate",4]],0,s.lb,[l.r,s.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(89,0,null,0,3,"GridItem",[["width","2%"]],null,null,null,m.Ac,m.I)),l.Ib(90,4440064,null,0,s.A,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(91,0,null,0,1,"SwtLabel",[["id","endDateLabel"],["styleName","labelBold"],["visible","false"]],null,null,null,m.Yc,m.fb)),l.Ib(92,4440064,[[2,4],["endDateLabel",4]],0,s.vb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],visible:[2,"visible"]},null),(t()(),l.Jb(93,0,null,0,3,"GridItem",[["visible","false"],["width","13%"]],null,null,null,m.Ac,m.I)),l.Ib(94,4440064,[[24,4],["dateItem",4]],0,s.A,[l.r,s.i],{width:[0,"width"],visible:[1,"visible"]},null),(t()(),l.Jb(95,0,null,0,1,"SwtDateField",[["id","endDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.validateDateFieldValue(i)&&l);return l},m.Tc,m.ab)),l.Ib(96,4308992,[[20,4],["endDate",4]],0,s.lb,[l.r,s.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(97,0,null,0,1,"SwtCanvas",[["height","80%"],["id","canvasGrid"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(98,4440064,[[16,4],["canvasGrid",4]],0,s.db,[l.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(99,0,null,0,23,"SwtCanvas",[["height","5%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(100,4440064,null,0,s.db,[l.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(101,0,null,0,21,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(102,4440064,null,0,s.C,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(103,0,null,0,13,"HBox",[["width","80%"]],null,null,null,m.Dc,m.K)),l.Ib(104,4440064,null,0,s.C,[l.r,s.i],{width:[0,"width"]},null),(t()(),l.Jb(105,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.refreshDetails(i)&&l);return l},m.Mc,m.T)),l.Ib(106,4440064,[[8,4],["refreshButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(107,0,null,0,1,"SwtButton",[["enabled","false"],["id","downloadButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.downloadButton_clickHandler(i)&&l);return l},m.Mc,m.T)),l.Ib(108,4440064,[[9,4],["downloadButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(109,0,null,0,1,"SwtButton",[["enabled","false"],["id","resendMailButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.resendMailButton_clickHandler(i)&&l);return l},m.Mc,m.T)),l.Ib(110,4440064,[[10,4],["resendMailButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(111,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.changeViewSchedReportHist(!1)&&l);return l},m.Mc,m.T)),l.Ib(112,4440064,[[11,4],["viewButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(113,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.deleteSchedReportHist()&&l);return l},m.Mc,m.T)),l.Ib(114,4440064,[[12,4],["deleteButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(115,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.closeHandler()&&l);return l},m.Mc,m.T)),l.Ib(116,4440064,[[13,4],["closeButton",4]],0,s.cb,[l.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(117,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingTop","5"],["width","20%"]],null,null,null,m.Dc,m.K)),l.Ib(118,4440064,null,0,s.C,[l.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),l.Jb(119,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpButton"],["styleName","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.doHelp()&&l);return l},m.Wc,m.db)),l.Ib(120,4440064,[[14,4],["helpButton",4]],0,s.rb,[l.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(121,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),l.Ib(122,114688,[[15,4],["loadingImage",4]],0,s.xb,[l.r],null,null)],function(t,e){t(e,25,0,"100%","100%");t(e,27,0,"100%","100%","670","5","5","5","5");t(e,29,0,"100%","15%");t(e,31,0,"100%","100%","10");t(e,33,0,"100%","100%","10");t(e,35,0,"100%","25%");t(e,37,0,"150");t(e,39,0,"lblReportJob","labelBold","Report Job");t(e,41,0,"100%");t(e,43,0,"reportJobs","550","reportJobsCombo");t(e,45,0,"100%");t(e,47,0,"selectedAccount","labelLeft");t(e,49,0,"100%","25%");t(e,51,0,"150","100%");t(e,53,0,"lblReportType","labelBold","Report Type");t(e,55,0,"100%","100%");t(e,57,0,"reportTypes","400","reportTypesCombo");t(e,59,0,"100%");t(e,61,0,"selectedAttribute","labelLeft");t(e,63,0,"100%","25%");t(e,65,0,"150","100%");t(e,67,0,"dateLabel","labelBold","Date");t(e,69,0,"100%","100%");t(e,71,0,"dateSingleOrRange","horizontal");t(e,74,0,"singleDate","200","dateSingleOrRange","S","true");t(e,76,0,"dateRange","200","dateSingleOrRange","D");t(e,78,0,"100%","25%");t(e,80,0,"150","100%");t(e,82,0,"40");t(e,84,0,"startDateLabel","labelBold");t(e,86,0,"14%");t(e,88,0,"0-9/","startDate",!1,"70");t(e,90,0,"2%");t(e,92,0,"endDateLabel","labelBold","false");t(e,94,0,"13%","false");t(e,96,0,"0-9/","endDate",!1,"70");t(e,98,0,"canvasGrid","100%","80%");t(e,100,0,"100%","5%");t(e,102,0,"100%");t(e,104,0,"80%");t(e,106,0,"refreshButton");t(e,108,0,"downloadButton","false");t(e,110,0,"resendMailButton","false");t(e,112,0,"viewButton","false");t(e,114,0,"deleteButton","false");t(e,116,0,"closeButton");t(e,118,0,"right","20%","5");t(e,120,0,"helpButton","helpIcon","true"),t(e,122,0)},null)}function U(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-sched-report-history",[],null,null,null,E,V)),l.Ib(1,4440064,null,0,r,[s.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var j=l.Fb("app-sched-report-history",r,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);