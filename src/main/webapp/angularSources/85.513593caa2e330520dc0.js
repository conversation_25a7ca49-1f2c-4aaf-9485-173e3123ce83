(window.webpackJsonp=window.webpackJsonp||[]).push([[85],{LJ6J:function(t,n,l){"use strict";l.r(n);var e=l("CcnG"),i=l("mrSG"),o=l("447K"),u=l("ZYCi"),a=function(t){function n(n,l){var e=t.call(this,l,n)||this;return e.commonService=n,e.element=l,e.baseURL=o.Wb.getBaseURL(),e.actionPath="",e.actionMethod="",e.inputData=new o.G(e.commonService),e.saveData=new o.G(e.commonService),e.jsonReader=new o.L,e.requestParams=[],e.menuAccessIdParent=0,e.screenName="Forecast Assumption Add",e.versionNumber="1.1.0001",e.screenVersion=new o.V(e.commonService),e.isValidAmount=!0,e.swtAlert=new o.bb(n),e}return i.d(n,t),n.prototype.ngOnInit=function(){this.btnSave.label=o.Wb.getPredictMessage("button.forecastMonitor.save",null),this.btnSave.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.save",null),this.btnCancel.label=o.Wb.getPredictMessage("button.forecastMonitor.cancel",null),this.btnCancel.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.cancel",null),this.cbEntity.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.selectEntity",null),this.cbCurrency.toolTip=o.Wb.getPredictMessage("tooltip.forecastMonitor.selectCurrency",null),this.txtDate.toolTip=o.Wb.getPredictMessage("tooltip.forecastAssumptionAdd.date",null)},n.prototype.onLoad=function(){var t=this;this.requestParams=[],this.menuAccessIdParent=o.x.call("eval","menuAccessIdParent"),1==this.menuAccessIdParent&&(this.btnSave.enabled=!1),this.initializeMenus(),this.cbCurrency.enabled=!1,this.inputData.cbResult=function(n){t.inputDataResult(n)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="forecastMonitor.do?",this.actionMethod="method=displayAddAssumption",this.actionMethod=this.actionMethod+"&selectedCurrencyCode="+o.x.call("eval","currencyCode")+"&selectedEntityId="+o.x.call("eval","entityId")+"&currencyName="+o.x.call("eval","currencyName"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.cbResult=function(n){t.saveDataResult(n)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.inputData.send(this.requestParams)},n.prototype.saveDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()&&o.x.call("unloadCloseWindow"))},n.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,null);var t=new o.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},n.prototype.showGridJSON=function(){this.showJSON=o.Eb.createPopUp(this,o.M,{jsonData:this.lastReceivedJSON}),this.showJSON.width="500",this.showJSON.title="Last Received JSON",this.showJSON.height="170",this.showJSON.enableResize=!1,this.showJSON.showControls=!0,this.showJSON.isModal=!0,this.showJSON.display()},n.prototype.inputDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastReceivedJSON!=this.prevRecievedJSON&&(this.cbEntity.setComboData(this.jsonReader.getSelects()),this.cbCurrency.setComboData(this.jsonReader.getSelects()),""!=this.cbEntity.selectedLabel.trim()&&(this.selectedEntity.text=this.cbEntity.selectedValue),""!=this.cbCurrency.selectedLabel&&(this.selectedCurrency.text=this.cbCurrency.selectedValue),"All"!=o.x.call("eval","entityId")&&(this.cbEntity.selectedLabel=o.x.call("eval","entityId"),this.cbEntity.enabled=!1),this.txtDate.text=o.x.call("eval","date").toString(),null!=o.x.call("eval","assumption")&&(this.txtAssumption.text=o.x.call("eval","assumption")),null!=o.x.call("eval","amount")&&(this.txtAmount.text=o.x.call("eval","amount")),this.changeEntityCombo()))},n.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},n.prototype.onAmountChange=function(){var t,n=!1;(t=this.txtAmount.text).indexOf("-")>=0&&(n=!0,t=t.substr(1,t.length));var l=o.x.call("formatCurrency",t),e=Object(o.ic.getFocus()).id;if(null==e||"btnCancel"==e);else{if("invalid"==l)return this.isValidAmount=!1,void this.swtAlert.warning(o.Wb.getPredictMessage("alert.assumptionAdd.validAmount",null),"Warning",o.c.OK,null,this.focusListener.bind(this),null);this.isValidAmount=!0,this.txtAmount.text=n?"-"+l:l}},n.prototype.focusListener=function(){this.txtAmount.setFocus()},n.prototype.changeEntityCombo=function(){this.selectedEntity.text=this.cbEntity.selectedValue},n.prototype.updateData=function(){if(this.isValidAmount){this.requestParams=[],this.actionMethod="method=saveForecastAssumption";var t=this.cbEntity.selectedItem.content;this.requestParams["forecastAssumption.currencyCode"]=this.cbCurrency.selectedItem.content,this.requestParams["forecastAssumption.entityId"]=t,this.requestParams["forecastAssumption.valueDateAsString"]=this.txtDate.text,this.requestParams["forecastAssumption.templateId"]=o.x.call("eval","templateId"),""!=this.txtAmount.text.trim()&&(this.requestParams.assumptionsAmount=this.txtAmount.text);var n=o.x.call("eval","assumptionId");this.requestParams["forecastAssumption.assumptionId"]=null!=n&&"undefined"!==n?n:"",this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.send(this.requestParams),this.btnSave.enabled=!1}else this.onAmountChange()},n.prototype.closeHandler=function(){o.x.call("closeWindow")},n.prototype.help=function(){o.x.call("help")},n}(o.yb),s=[{path:"",component:a}],r=(u.l.forChild(s),function(){return function(){}}()),c=l("pMnS"),d=l("RChO"),b=l("t6HQ"),h=l("WFGK"),m=l("5FqG"),p=l("Ip0R"),g=l("gIcY"),w=l("t/Na"),f=l("sE5F"),R=l("OzfB"),y=l("T7CS"),I=l("S7LP"),A=l("6aHO"),x=l("WzUx"),v=l("A7o+"),C=l("zCE2"),S=l("Jg5P"),J=l("3R0m"),D=l("hhbb"),M=l("5rxC"),L=l("Fzqc"),k=l("21Lb"),O=l("hUWP"),N=l("3pJQ"),B=l("V9q+"),P=l("VDKW"),T=l("kXfT"),G=l("BGbe");l.d(n,"ForecastMonitorAssumptionsAddModuleNgFactory",function(){return E}),l.d(n,"RenderType_ForecastMonitorAssumptionsAdd",function(){return _}),l.d(n,"View_ForecastMonitorAssumptionsAdd_0",function(){return F}),l.d(n,"View_ForecastMonitorAssumptionsAdd_Host_0",function(){return q}),l.d(n,"ForecastMonitorAssumptionsAddNgFactory",function(){return V});var E=e.Gb(r,[],function(t){return e.Qb([e.Rb(512,e.n,e.vb,[[8,[c.a,d.a,b.a,h.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,V]],[3,e.n],e.J]),e.Rb(4608,p.m,p.l,[e.F,[2,p.u]]),e.Rb(4608,g.c,g.c,[]),e.Rb(4608,g.p,g.p,[]),e.Rb(4608,w.j,w.p,[p.c,e.O,w.n]),e.Rb(4608,w.q,w.q,[w.j,w.o]),e.Rb(5120,w.a,function(t){return[t,new o.tb]},[w.q]),e.Rb(4608,w.m,w.m,[]),e.Rb(6144,w.k,null,[w.m]),e.Rb(4608,w.i,w.i,[w.k]),e.Rb(6144,w.b,null,[w.i]),e.Rb(4608,w.f,w.l,[w.b,e.B]),e.Rb(4608,w.c,w.c,[w.f]),e.Rb(4608,f.c,f.c,[]),e.Rb(4608,f.g,f.b,[]),e.Rb(5120,f.i,f.j,[]),e.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),e.Rb(4608,f.f,f.a,[]),e.Rb(5120,f.d,f.k,[f.h,f.f]),e.Rb(5120,e.b,function(t,n){return[R.j(t,n)]},[p.c,e.O]),e.Rb(4608,y.a,y.a,[]),e.Rb(4608,I.a,I.a,[]),e.Rb(4608,A.a,A.a,[e.n,e.L,e.B,I.a,e.g]),e.Rb(4608,x.c,x.c,[e.n,e.g,e.B]),e.Rb(4608,x.e,x.e,[x.c]),e.Rb(4608,v.l,v.l,[]),e.Rb(4608,v.h,v.g,[]),e.Rb(4608,v.c,v.f,[]),e.Rb(4608,v.j,v.d,[]),e.Rb(4608,v.b,v.a,[]),e.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),e.Rb(4608,x.i,x.i,[[2,v.k]]),e.Rb(4608,x.r,x.r,[x.L,[2,v.k],x.i]),e.Rb(4608,x.t,x.t,[]),e.Rb(4608,x.w,x.w,[]),e.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),e.Rb(1073742336,p.b,p.b,[]),e.Rb(1073742336,g.n,g.n,[]),e.Rb(1073742336,g.l,g.l,[]),e.Rb(1073742336,C.a,C.a,[]),e.Rb(1073742336,S.a,S.a,[]),e.Rb(1073742336,g.e,g.e,[]),e.Rb(1073742336,J.a,J.a,[]),e.Rb(1073742336,v.i,v.i,[]),e.Rb(1073742336,x.b,x.b,[]),e.Rb(1073742336,w.e,w.e,[]),e.Rb(1073742336,w.d,w.d,[]),e.Rb(1073742336,f.e,f.e,[]),e.Rb(1073742336,D.b,D.b,[]),e.Rb(1073742336,M.b,M.b,[]),e.Rb(1073742336,R.c,R.c,[]),e.Rb(1073742336,L.a,L.a,[]),e.Rb(1073742336,k.d,k.d,[]),e.Rb(1073742336,O.c,O.c,[]),e.Rb(1073742336,N.a,N.a,[]),e.Rb(1073742336,B.a,B.a,[[2,R.g],e.O]),e.Rb(1073742336,P.b,P.b,[]),e.Rb(1073742336,T.a,T.a,[]),e.Rb(1073742336,G.b,G.b,[]),e.Rb(1073742336,o.Tb,o.Tb,[]),e.Rb(1073742336,r,r,[]),e.Rb(256,w.n,"XSRF-TOKEN",[]),e.Rb(256,w.o,"X-XSRF-TOKEN",[]),e.Rb(256,"config",{},[]),e.Rb(256,v.m,void 0,[]),e.Rb(256,v.n,void 0,[]),e.Rb(256,"popperDefaults",{},[]),e.Rb(1024,u.i,function(){return[[{path:"",component:a}]]},[])])}),W=[[""]],_=e.Hb({encapsulation:0,styles:W,data:{}});function F(t){return e.dc(0,[e.Zb(402653184,1,{_container:0}),e.Zb(402653184,2,{cbEntity:0}),e.Zb(402653184,3,{cbCurrency:0}),e.Zb(402653184,4,{selectedEntity:0}),e.Zb(402653184,5,{selectedCurrency:0}),e.Zb(402653184,6,{txtDate:0}),e.Zb(402653184,7,{txtAmount:0}),e.Zb(402653184,8,{txtAssumption:0}),e.Zb(402653184,9,{btnSave:0}),e.Zb(402653184,10,{btnCancel:0}),(t()(),e.Jb(10,0,null,null,77,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,n,l){var e=!0,i=t.component;"creationComplete"===n&&(e=!1!==i.onLoad()&&e);return e},m.ad,m.hb)),e.Ib(11,4440064,null,0,o.yb,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),e.Jb(12,0,null,0,75,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),e.Ib(13,4440064,null,0,o.ec,[e.r,o.i,e.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),e.Jb(14,0,null,0,61,"SwtCanvas",[["height","80%"],["width","100%"]],null,null,null,m.Nc,m.U)),e.Ib(15,4440064,null,0,o.db,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(16,0,null,0,59,"Grid",[["height","100%"],["width","100%"]],null,null,null,m.Cc,m.H)),e.Ib(17,4440064,null,0,o.z,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(18,0,null,0,13,"GridRow",[["height","20%"],["width","100%"]],null,null,null,m.Bc,m.J)),e.Ib(19,4440064,null,0,o.B,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(20,0,null,0,3,"GridItem",[["width","16%"]],null,null,null,m.Ac,m.I)),e.Ib(21,4440064,null,0,o.A,[e.r,o.i],{width:[0,"width"]},null),(t()(),e.Jb(22,0,null,0,1,"SwtLabel",[["textDictionaryId","label.forecastMonitor.entity"]],null,null,null,m.Yc,m.fb)),e.Ib(23,4440064,null,0,o.vb,[e.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),e.Jb(24,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(25,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(26,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","cbEntity"],["width","150"]],null,[["window","mousewheel"]],function(t,n,l){var i=!0;"window:mousewheel"===n&&(i=!1!==e.Tb(t,27).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),e.Ib(27,4440064,[[2,4],["cbEntity",4]],0,o.gb,[e.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},null),(t()(),e.Jb(28,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(29,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(30,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"]],null,null,null,m.Yc,m.fb)),e.Ib(31,4440064,[[4,4],["selectedEntity",4]],0,o.vb,[e.r,o.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),e.Jb(32,0,null,0,13,"GridRow",[["height","20%"],["width","100%"]],null,null,null,m.Bc,m.J)),e.Ib(33,4440064,null,0,o.B,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(34,0,null,0,3,"GridItem",[["width","16%"]],null,null,null,m.Ac,m.I)),e.Ib(35,4440064,null,0,o.A,[e.r,o.i],{width:[0,"width"]},null),(t()(),e.Jb(36,0,null,0,1,"SwtLabel",[["textDictionaryId","label.forecastMonitor.currency"]],null,null,null,m.Yc,m.fb)),e.Ib(37,4440064,null,0,o.vb,[e.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),e.Jb(38,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(39,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(40,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","cbCurrency"],["width","150"]],null,[["window","mousewheel"]],function(t,n,l){var i=!0;"window:mousewheel"===n&&(i=!1!==e.Tb(t,41).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),e.Ib(41,4440064,[[3,4],["cbCurrency",4]],0,o.gb,[e.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},null),(t()(),e.Jb(42,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(43,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(44,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,m.Yc,m.fb)),e.Ib(45,4440064,[[5,4],["selectedCurrency",4]],0,o.vb,[e.r,o.i],{fontWeight:[0,"fontWeight"]},null),(t()(),e.Jb(46,0,null,0,9,"GridRow",[["height","20%"],["width","100%"]],null,null,null,m.Bc,m.J)),e.Ib(47,4440064,null,0,o.B,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(48,0,null,0,3,"GridItem",[["width","16%"]],null,null,null,m.Ac,m.I)),e.Ib(49,4440064,null,0,o.A,[e.r,o.i],{width:[0,"width"]},null),(t()(),e.Jb(50,0,null,0,1,"SwtLabel",[["textDictionaryId","label.forecastAssumptionAdd.date"]],null,null,null,m.Yc,m.fb)),e.Ib(51,4440064,null,0,o.vb,[e.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),e.Jb(52,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(53,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(54,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","txtDate"],["textAlign","left"],["width","140"]],null,null,null,m.kd,m.sb)),e.Ib(55,4440064,[[6,4],["txtDate",4]],0,o.Rb,[e.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"]},null),(t()(),e.Jb(56,0,null,0,9,"GridRow",[["height","20%"],["width","100%"]],null,null,null,m.Bc,m.J)),e.Ib(57,4440064,null,0,o.B,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(58,0,null,0,3,"GridItem",[["width","16%"]],null,null,null,m.Ac,m.I)),e.Ib(59,4440064,null,0,o.A,[e.r,o.i],{width:[0,"width"]},null),(t()(),e.Jb(60,0,null,0,1,"SwtLabel",[["textDictionaryId","label.forecastAssumptionAdd.amount"]],null,null,null,m.Yc,m.fb)),e.Ib(61,4440064,null,0,o.vb,[e.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),e.Jb(62,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(63,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(64,0,null,0,1,"SwtTextInput",[["id","txtAmount"],["maxChars","22"],["restrict","0-9.,-bBtTmM"],["textAlign","right"],["tooltipDictionaryId","tooltip.forecastAssumptionAdd.amount"],["width","210"]],null,[[null,"focusOut"]],function(t,n,l){var e=!0,i=t.component;"focusOut"===n&&(e=!1!==i.onAmountChange()&&e);return e},m.kd,m.sb)),e.Ib(65,4440064,[[7,4],["txtAmount",4]],0,o.Rb,[e.r,o.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],textAlign:[3,"textAlign"],textDictionaryId:[4,"textDictionaryId"],width:[5,"width"]},{onFocusOut_:"focusOut"}),(t()(),e.Jb(66,0,null,0,9,"GridRow",[["height","20%"],["width","100%"]],null,null,null,m.Bc,m.J)),e.Ib(67,4440064,null,0,o.B,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(68,0,null,0,3,"GridItem",[["width","16%"]],null,null,null,m.Ac,m.I)),e.Ib(69,4440064,null,0,o.A,[e.r,o.i],{width:[0,"width"]},null),(t()(),e.Jb(70,0,null,0,1,"SwtLabel",[["textDictionaryId","label.forecastAssumptionAdd.assumption"]],null,null,null,m.Yc,m.fb)),e.Ib(71,4440064,null,0,o.vb,[e.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),e.Jb(72,0,null,0,3,"GridItem",[],null,null,null,m.Ac,m.I)),e.Ib(73,4440064,null,0,o.A,[e.r,o.i],null,null),(t()(),e.Jb(74,0,null,0,1,"SwtTextInput",[["id","txtAssumption"],["maxChars","100"],["tooltipDictionaryId","tooltip.forecastAssumptionAdd.assumption"],["width","400"]],null,null,null,m.kd,m.sb)),e.Ib(75,4440064,[[8,4],["txtAssumption",4]],0,o.Rb,[e.r,o.i],{maxChars:[0,"maxChars"],id:[1,"id"],textDictionaryId:[2,"textDictionaryId"],width:[3,"width"]},null),(t()(),e.Jb(76,0,null,0,11,"SwtCanvas",[["height","18%"],["width","100%"]],null,null,null,m.Nc,m.U)),e.Ib(77,4440064,null,0,o.db,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(78,0,null,0,5,"HBox",[["height","100%"],["width","100%"]],null,null,null,m.Dc,m.K)),e.Ib(79,4440064,null,0,o.C,[e.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),e.Jb(80,0,null,0,1,"SwtButton",[["id","btnSave"]],null,[[null,"click"]],function(t,n,l){var e=!0,i=t.component;"click"===n&&(e=!1!==i.updateData()&&e);return e},m.Mc,m.T)),e.Ib(81,4440064,[[9,4],["btnSave",4]],0,o.cb,[e.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),e.Jb(82,0,null,0,1,"SwtButton",[["id","btnCancel"]],null,[[null,"click"]],function(t,n,l){var e=!0,i=t.component;"click"===n&&(e=!1!==i.closeHandler()&&e);return e},m.Mc,m.T)),e.Ib(83,4440064,[[10,4],["btnCancel",4]],0,o.cb,[e.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),e.Jb(84,0,null,0,3,"HBox",[["horizontalAlign","right"]],null,null,null,m.Dc,m.K)),e.Ib(85,4440064,null,0,o.C,[e.r,o.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),e.Jb(86,0,null,0,1,"SwtHelpButton",[],null,[[null,"click"]],function(t,n,l){var e=!0,i=t.component;"click"===n&&(e=!1!==i.help()&&e);return e},m.Wc,m.db)),e.Ib(87,4440064,null,0,o.rb,[e.r,o.i],null,{onClick_:"click"})],function(t,n){t(n,11,0,"100%","100%");t(n,13,0,"100%","100%","5","5","5","5");t(n,15,0,"100%","80%");t(n,17,0,"100%","100%");t(n,19,0,"100%","20%");t(n,21,0,"16%");t(n,23,0,"label.forecastMonitor.entity"),t(n,25,0);t(n,27,0,"entity","150","cbEntity"),t(n,29,0);t(n,31,0,"selectedEntity","normal");t(n,33,0,"100%","20%");t(n,35,0,"16%");t(n,37,0,"label.forecastMonitor.currency"),t(n,39,0);t(n,41,0,"currency","150","cbCurrency"),t(n,43,0);t(n,45,0,"normal");t(n,47,0,"100%","20%");t(n,49,0,"16%");t(n,51,0,"label.forecastAssumptionAdd.date"),t(n,53,0);t(n,55,0,"txtDate","left","140","false");t(n,57,0,"100%","20%");t(n,59,0,"16%");t(n,61,0,"label.forecastAssumptionAdd.amount"),t(n,63,0);t(n,65,0,"22","0-9.,-bBtTmM","txtAmount","right","tooltip.forecastAssumptionAdd.amount","210");t(n,67,0,"100%","20%");t(n,69,0,"16%");t(n,71,0,"label.forecastAssumptionAdd.assumption"),t(n,73,0);t(n,75,0,"100","txtAssumption","tooltip.forecastAssumptionAdd.assumption","400");t(n,77,0,"100%","18%");t(n,79,0,"100%","100%");t(n,81,0,"btnSave");t(n,83,0,"btnCancel");t(n,85,0,"right"),t(n,87,0)},null)}function q(t){return e.dc(0,[(t()(),e.Jb(0,0,null,null,1,"app-forecast-monitor-assumptions-add",[],null,null,null,F,_)),e.Ib(1,4440064,null,0,a,[o.i,e.r],null,null)],function(t,n){t(n,1,0)},null)}var V=e.Fb("app-forecast-monitor-assumptions-add",a,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);