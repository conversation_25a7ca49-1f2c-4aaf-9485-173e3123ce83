/* DEFAULT STYLES */

body {
	background-color : #E8F3FE;
	font-family	: verdana,helvetica;
	font-size : 9pt;
	color : #000000;
	margin : 0,0,0,0px;
}


TABLE {
	font-size : 9pt;
	color:#22266B;
	margin-top: 0px;
	margin-left: 0px;	

	
}

table .menulinks{
	font-size : 9pt;
	background:#C5C7C7;
	width:100%;
	margin-top: 0px;
	text-align:left;
	float:center;
}
table .shortcuts{
	font-family: verdana,helvetica;
	font-size : 8pt;
	width:50;
	height:50;
	background:#799FC5;
	cellspacing:0px;
	cellpadding:0px;
	margin-top: 0px;
	text-align:left;
	float:center;
}

table .content{
	background:#E8F3FE;
	cellspacing: 1px;
	cellpadding:2px;
}

.copyright{
	font-family: verdana,helvetica;
	background:#00689C;
	color:"white";
	font-size:7pt;
	font-weight:bold;
	height:20px;
	text-align:center;
}



table .topbar{
	background:#1E62A5;
	cellspacing: 1px;
	cellpadding:2px;
	color:#ffffff;
	font-size:10pt;

}
table .topbar1{
	background:#1E62A5;
	cellspacing: 1px;
	cellpadding:2px;
	color:#ffffff;
	font-size:8pt;

}



INPUT, TEXTAREA, SELECT{
	font-family: verdana,helvetica;
	font-size: 8pt;
}



a.main:link{
	font-family: verdana,helvetica;
	font-size:9pt; 
	font-weight: bold; 
	color: #FFFFFF;
	text-decoration: none;
}

a.main:visited{
	font-family: verdana,helvetica;
	font-size:9pt; 
	font-weight: bold; 
	color: #FFFFFF;
	text-decoration: none;
}


.button{
  color: #000000;
  background-color: #D8E9FB;
  font-size: 8pt;
  line-height: 18px;
  width:55px;
  text-align: center;
  text-decoration: none;

}

.gobutton{
  color: #000000;
  background-color: #D8E9FB;
  font-size: 8pt;
  line-height:15px;
  width:22px;
  text-align: center;
  text-decoration: none;

}


input[type='submit'] {
  border: 1px solid #000000;
}

.al {
	text-align: left;
	font-size:9pt;
}

.ar {
	text-align: right;
	font-size:9pt;
}

.ac {
	text-align: center;
	font-size:9pt;
}


a.navi:link {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}


a.navi:visted {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}





a.menu:link {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}

a.menu:hover {
	color: white;
	FONT-WEIGHT: bold;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}

a.menu:visited {
	color: white;
	FONT-WEIGHT:bold;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}


#Work{
	position:absolute;
	left:0px;
	top:80;
	width:125px;
	height:88px;
	z-index:1;
	visibility: hidden;
}

#Movements{
	position:absolute;
	left:0;
	top:54px;
	width:125px;
	height:90px;
	z-index:1;
	visibility: hidden;

}

#Match{
	position:absolute;
	left:0;
	top:54px;
	width:125px;
	height:90px;
	z-index:1;
	visibility: hidden;



}

#Enquiry{
	position:absolute;
	left:180;
	top:80;
	width:125px;
	height:30px;
	z-index:1;
	visibility: hidden;
}


#Maintenance{
	position:absolute;
	left:467;
	top:80;
	width:125px;
	height:1px;
	z-index:1;
	visibility: hidden;

}

#Controls{
	position:absolute;
	left:350;
	top:54px;
	width:125;
	height:1px;
	z-index:1;
	visibility: hidden;

}


#Work .links{
  width:125px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #F2F4F6;
  border-bottom:outset 2px #F2F4F6;
  }

#Movements .links{
  width:125px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}

#Match .links{
  width:125px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}


#Enquiry .links{
  width:125px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}


#Maintenance .links{
  width:137px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}



#Manager .links{
  width:125px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}

#Controls .links{
  width:132px;
  font-size:11px;
  background:#8F9397;
  margin-left:0px;
  line-height:20px;
  border-right: outset 2px #C5C7C7;
  border-bottom:outset 2px #C5C7C7;
}

a.menulnk:link{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	line-height:50px;
	vertical-align:top;
	padding-left:5px;
	
}


a.menulnk:hover{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	vertical-align:top;
	background:#8F9397;
	line-height:20px;
	text-decoration:none;
	width:115px;
	/*border: #3E4B83 thin solid 1px;*/
	position:static;
	padding-left:5px;
}

a.menulnk:visited{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	line-height:20px;
	vertical-align:top;
	padding-left:5px;
	
}
a.menulnk_1:link{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	line-height:50px;
	vertical-align:top;
	padding-left:5px;
	
}


a.menulnk_1:hover{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	vertical-align:top;
	background:#8F9397;
	line-height:20px;
	text-decoration:none;
	width:125px;
	/*border: #3E4B83 thin solid 1px;*/
	position:static;
	padding-left:5px;
}

a.menulnk_1:visited{
	color: black;
	FONT-WEIGHT: normal;
	font-size:11px;
	font-family: verdana,helvetica;
	text-decoration: none;
	line-height:20px;
	vertical-align:top;
	padding-left:5px;
	
}



/*
a:visited 
{
	font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size:11; 
	font-weight: bold; 
	color: #003399;
	text-decoration:none 
}

a:active 
{
	font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size:11; 
	font-weight: ; 
	color: #003399;
	text-decoration: 
}

a:hover 
{
	font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size:11; 
	font-weight: ; 
	color: #000000;
	text-decoration: 
}

 */
