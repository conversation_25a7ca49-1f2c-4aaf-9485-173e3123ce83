.sort-table {
	font-family	: verdana,helvetica;
	font-size : 9pt;
	table-layout: fixed;
	background: #DEDFE0;
	cellspacing: 0px;
	cellpadding:0px;
	color:yellow;
	font-size:9pt;
	border: 0px ;
}

.sort-table thead {
	background: #1A75D3;
	
}

.sort-table .header-title {
	border: 	0;
	vertical-align:	middle;
	text-align: center;
}

.sort-table .header-container {
	text-align: 	left;
	padding-top: 0px;
	padding-bottom:0px;
	padding-right:0px;
	padding-left:0px;
}

.sort-table tbody td {
	border-right:	1px;
	border-bottom:	1px;
	/*border-color:	gray;
	padding:	2px 5px;
	border-right-color:gray;*/
	color:black;
	padding:	2px 5px;
}



.sort-table thead td {
	background-color: #4da1f2;
	border-top: 0px ;
	border-left: 1.25px outset #696969  ;
	border-bottom: 0px ;
	border-right: 0px ;
	font-family: verdana,helvetica;
	font-size:9pt;
	cellspacing: 0px;
	cellpadding:0px;
	color:#ffffff;
	padding:3px 4px 3px 6px;

}

.sort-table tfoot tr {
	background:	#E8F3FE;
}
.sort-table tfoot td {
	background:	white;
}

.sort-table thead td:active {
	border-color:	ButtonShadow ButtonHighlight
					ButtonHighlight ButtonShadow;
}

.sort-table thead td[_sortType=None]:active {
	border-color:	ButtonHighlight ButtonShadow
					ButtonShadow ButtonHighlight;
	padding:2px 5px;

	
}

.sort-arrow {
	width:	11px;
	height:	11px;
	background-position:center center;
	background-repeat:no-repeat;
	margin:	0 2px;
}

.sort-arrow.descending {
	background-image:	url("../images/arrowwhite_down.gif");

}

.sort-arrow.ascending {
	background-image:	url("../images/arrowwhite_up.gif");
}



.select	{	
	font-family	: verdana,helvetica;
	font-size : 9pt;
	cursor: default;
}

.selected {
	background: window;
	padding: 0;
	font-family	: verdana,helvetica;
	font-size : 9pt;
}

.selectTable {
	height: 100%;
	width: 100%;
	background: buttonface;
}

.simpleSelectTable {
	width: 100%;
	font-family	: verdana,helvetica;
	font-size : 9pt;
}

.option {
	font-family	: verdana,helvetica;
	font-size : 9pt;
	padding: 1;
	padding-left: 3;
	padding-right: 3;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
}

.dropDown {
	position: absolute;
	visibility: hidden;
	width: 100%;
	height: 500%;
	overflow-x: hidden;
    overflow-y: auto;
	background-color: #ffffff;
	color: black;
	border: 1px solid windowtext;
	padding: 0;
}
.small-filter .dropDown {
	position: absolute;
	visibility: hidden;
	width: 100%;
	height: 150px;
	overflow: auto;
	background-color: #ffffff;
	color: black;
	border: 1px solid windowtext;
	padding: 0;	
}
.select .button	{
	width: 15px;
	height: 18px;
	font-family: webdings;
	font-weigth: bold;
	padding: 0;
	font-size: 8px;
	border-top: 1px  buttonhighlight;
	border-left: 1px  buttonhighlight;
	border-bottom: 1px  buttonhighlight;
	/*border-right: 2px outset buttonhighlight;*/
	background: #B8DAFB;
}

.even {
	background:	white;
	height: 17px;
}

.odd {
	background:	#E0F0FF;
	height: 17px;
}

.accp {
	background:	#dddddd;
	height: 17px;
}

.accn {
	background:	#aaaaaa;
	height: 17px;
}

.evencheckbox {
	background:	white;
	height: 21px;
}
.oddcheckbox {
	background:	#E0F0FF;
	height: 21px;
}
.selectrow {
    background: #FFC134;
    border-bottom: 1px solid #C0C0C0;
    border-top: 1px solid #C0C0C0;
    color: #000000;
    cursor: pointer;
    height: 17px;
}
.evenselect {
    background: #FFC134;
    border-bottom: 1px solid #C0C0C0;
    border-top: 1px solid #C0C0C0;
    color: #000000;
    cursor: pointer;
    height: 17px;
}
.oddselect {
    background: #FFC134;
    border-bottom: 1px solid #C0C0C0;
    border-top: 1px solid #C0C0C0;
    color: #000000;
    cursor: pointer;
	height: 17px;
}
