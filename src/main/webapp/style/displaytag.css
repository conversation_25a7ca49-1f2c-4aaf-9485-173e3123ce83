span.pagebanner {
    display: block;
    margin: 10px 0px 0px 10px;
    padding: 2px 4px 2px 0px;
    width: 79%;
}

span.pagelinks {
    display: block;
    font-size: .95em;
    margin-bottom: 5px;
    margin-top: -18px;
    padding: 2px 0px 2px 0px;
    text-align: right;
    width: 80%;
}

table.list {
    border: 1px solid black;
    margin-top: 10px;
    width: 100%;
    background: #C0DBF6;
	color: white;
}

table.list td {
    padding-left: 3px;
}

table.list th {
    background-color: #1E62A5;
    padding: 2px;
    padding-left: 3px;
    border-bottom: 1px solid white;
    text-align: center;
    color: white;
	font-size:10pt;
}

/* The following 3 rules are for Tapestry's contrib:Table */
table.list th table {
    width: 100%;
    margin: 0px;
    padding: 0px;
}

table.list th table td {
    text-align: left;
}

table.list th table td:hover, table.list th table td a:hover {
    background-color: #ffd;
}

table.list th table tr:hover {
    background-color: #ffd;
    border: 1px solid white;
}

table.list tr.even {
    background: #eee;
    border-top: 1px solid silver;
}

table.list tr.odd {
    background: white;
    border-top: 1px solid silver;
}

table.list th a, table.list th a:visited {
    background-color: transparent;
    color: #000000;
    text-decoration: none;
}

th.sorted a, th.sortable a {
    background-position: right;
    display: block;
    padding: 0px 3px;
}

table.list th.order1 a {
	background-image: url(images/arrow_down.png) !important;
}

table.list th.order2 a {
    background-image: url(images/arrow_up.png) !important;
}

table.list th.sortable a {
    background-image: url(images/arrow_off.png);
}

table.list th.sorted {
    background-color: #eee;
    color: #000000;
}

table.list th.sorted a, table.list th.sortable a {
    background-position: right;
    background-repeat: no-repeat;
    display: block;
}

table.list th.sortable:hover {
    background-color: #ffd;
}

table.list thead tr {
    background-color: #FFCC00;
}

table.list tr.even {
    background: white;
    border-top: 1px solid #C0C0C0;
    color: #000000;
}

table.list tr.odd {
    border-top: 1px solid #C0C0C0;
    color: #000000;
	background: #C0DBF6;
}

/* highlight table row onmouseover */
table.list tr:hover, table.list tr.over {
    background: #FFFF40;
    border-bottom: 1px solid #C0C0C0;
    border-top: 1px solid #C0C0C0;
    color: #000000;
    cursor: pointer;
    cursor: hand; /* IE 5.5 non-compliant workaround */
}

/* Labels for validation and label tag */
label {
    font-weight: normal;
    cursor: pointer;
    cursor: hand;
}

label.required {
    font-weight: bold;
}

label.error {
    font-weight: bold;
    background: transparent;
    color: red;
}

/* These are the styles for detail screen tables */
table.detail {
    margin-left: 10px;
    padding: 5px;
    font-weight: normal;
}

table.detail th {
    text-align: right;
    padding: 0px 3px 0px 0px;
}