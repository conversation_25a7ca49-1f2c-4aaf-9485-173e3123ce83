#ddimagetabs{
}

#ddimagetabs a{
   display: block;
   text-decoration: none;
   font: 11px Verdana; /* tab font */
   color: black; /* font color */
   width: 83px; /* width of tab image */
   height: 22px; /* height of tab image */
   float: left;
   display: inline;
   margin-left: 0px; /* spacing between tabs */
   padding-top: 4px; /* vertical offset of tab text from top of tab */
   background-image: url(../images/new_bluetabover.gif); /* URL to tab image */
   background-repeat: no-repeat;
   text-align: center;
   border: 2px;
   cursor: hand;
   cursor: pointer;
   box-sizing:border-box;
}

#ddimagetabs a.default{
   background-image: url(../images/new_bluetabover.gif); /* URL to tab image */
   color: black;
   cursor: hand;
   cursor: pointer;
} 

#ddimagetabs a.current{
   background-image: url(../images/new_bluetab.gif); /* URL to tab image */
   color: black;
   cursor: hand;
   cursor: pointer;
} 

#ddimagetabs a.hoverall{
   background-image: url(../images/new_bluetabover_glow.gif); /* URL to tab image */
   color: darkblue; /* font color */
   cursor: hand;
   cursor: pointer;
}

#tabcontentcontainer{
   width: 630px; /* width of 2nd level content */
   height: 400px; /* height of 2nd level content. Set to largest content height to avoid jitter */
   border-top: 1px outset;
   border-left: 1px outset gray;
   border-right: 2px outset;
   border-bottom: 2px outset;
   display: block;
}

.tabcontent{
   display: none;
}

.modalButton{
   display: block;
   text-decoration: none;
   font: 11px Verdana; /* tab font */
   color: black; /* font color */
   width: 70px; /* width of tab image */
   height: 21px; /* height of tab image */
   float: left;
   display: inline;
   margin-left: 3px; /* spacing between tabs */
   margin-right: 3px;
   margin-top: 3px;
   padding-top: 3px; /* vertical offset of tab text from top of tab */
   background-image: url(../images/skyButtonUp.png); /* URL to tab image */
   background-repeat: no-repeat;
   border: 2px;
   text-align: center;
}

.centerButtons {
  display: flex;
  padding-top : 20px;
  justify-content: center;
  align-items: center;
}

.modalButton:hover{
   background-image: url(../images/skyButtonOver.png); /* URL to tab image */
   color: darkblue; /* font color */
   cursor: hand;
   cursor: pointer;
}

.modalButton:current{
   background-image: url(../images/skyButtonDown.png); /* URL to tab image */
   color: darkblue; /* font color */
   padding-left: 3px;
   padding-top: 3px; /* vertical offset of tab text from top of tab */
   cursor: hand;
   cursor: pointer;
}

.modalButton:disabled{
   background-image: url(../images/skyButtonDisabled.png); /* URL to tab image */
}


#ddimagebuttons a{
   display: block;
   text-decoration: none;
   font: 11px Verdana; /* tab font */
   color: black; /* font color */
   width: 70px; /* width of tab image */
   height: 21px; /* height of tab image */
   float: left;
   display: inline;
   margin-left: 3px; /* spacing between tabs */
   margin-right: 3px;
   margin-top: 3px;
   padding-top: 3px; /* vertical offset of tab text from top of tab */
   background-image: url(../images/skyButtonUp.png); /* URL to tab image */
   background-repeat: no-repeat;
   border: 2px;
   text-align: center;
   outline:none;
}

#ddimagebuttons a.hover{
   background-image: url(../images/skyButtonOver.png); /* URL to tab image */
   color: darkblue; /* font color */
   cursor: hand;
   cursor: pointer;
}

#ddimagebuttons a.current{
   background-image: url(../images/skyButtonDown.png); /* URL to tab image */
   color: darkblue; /* font color */
   padding-left: 3px;
   padding-top: 3px; /* vertical offset of tab text from top of tab */
   cursor: hand;
   cursor: pointer;
}

#ddimagebuttons a.disabled{
   background-image: url(../images/skyButtonDisabled.png); /* URL to tab image */
}
