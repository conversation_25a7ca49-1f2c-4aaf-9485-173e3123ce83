/* DEFAULT STYLES */

body {
	background-color : #D6E3FE;
	font-family	: verdana,helvetica;
	font-size : 9pt;
	color : #000000;
	margin : 0 0 0 0;
}
body.login {
	background-color : #F6F9FB;
	font-family	: verdana,helvetica;
	font-size : 9pt;
	color : #000000;
	margin : 0 0 0 0;
}

div{
	font-family: verdana,helvetica;
	font-size : 9pt;
	outline: none;
}
a{
	outline: none;
}


legend{
	background-color : #D6E3FE;
	font-family	: verdana,helvetica;
	font-size : 8pt;
	color : #000000;
	margin : 0 0 0 0;
}

TABLE {
	font-family: verdana,helvetica;
	font-size : 9pt;
	margin-top: 0px;
	margin-left: 0px;	

	
}

table .menulinks{
	font-size : 9pt;
	background:#E8F3FE;
	width:100%;
	margin-top: 0px;
	text-align:left;
	float:center;
}
table .shortcuts{
	font-family: verdana,helvetica;
	font-size : 8pt;
	width:40px;
	background:#DBDBD7;
	cellspacing:0px;
	cellpadding:0px;
	text-align:left;
	float:center;
}

table .content{
	font-family: verdana,helvetica;
	font-size:9pt;
	background:#E8F3FE;
	cellspacing: 1px;
	cellpadding:2px;
	
}

.copyright{
	font-family: verdana,helvetica;
	background:#00689C;
	color: black;
	font-size:9pt;
	font-weight:bold;
	height:20px;
	text-align:center;
}

table .topbar {
	background-color: #4da1f2;
	border-top: 0px outset white;
	border-left: 2px outset white;
	border-bottom: 2px outset #5382B1;
	border-right: 1px outset #5382B1;
	font-family: verdana,helvetica;
	font-size:9pt;
	cellspacing: 1px;
	cellpadding:0px;
	color:#ffffff;
	/*background: #187EE7;*/
	vertical-align:	middle;
	text-align: center;
}

table .topbarleft {
	/*background-color: #187EE7;*/
	border-top: 2px outset white;
	border-left: 2px outset white;
	border-bottom: 2px outset #5382B1;
	border-right: 1px outset #5382B1;
	font-family: verdana,helvetica;
	font-size:9pt;
	cellspacing: 1px;
	cellpadding:0px;
	color:#ffffff;
	background: #187EE7;
	vertical-align:	middle;
	text-align: left;
}

input
{
outline:none;

}


input.textNumeric{
	font-family: verdana,helvetica;
	font-size: 9pt;
	text-align:right;
}

input.textAlpha{
	font-family: verdana,helvetica;
	font-size: 9pt;

}

.htmlTextAlpha{

	font-family: verdana,helvetica;
	font-size: 9pt;	
}

.htmlTextFixed{

	font-family: Courier New;
	font-size: 9pt;	
}

.htmlTextNumeric{

	font-family: verdana,helvetica;
	font-size: 9pt;	
	text-align:right;
}


select{
	font-family: verdana,helvetica;
	font-size: 9pt;
	border-top: #00689C 2px solid ;
	border-left: gray 2px solid ; 
	width:110px;
	layout-grid-mode : both;
	outline: none;
}

textarea{
	font-family: verdana,helvetica;
	font-size: 9pt;
    border-color: 696969;
    border-style:solid; 
    border-width:1px;
    outline: none;
       
}
spantext{
	font-family: verdana,helvetica;
	font-size: 9pt;	
}

.textlabel{
	border:thin none;
	background-color:transparent;
}
.textlabel1{
	border:thin none;
	background-color:transparent;
	font-family: verdana,helvetica;
	font-size: 9pt;	
	text-align:right;
}
.textlabelalpha{
	border:thin none;
	background-color:transparent;
	font-family: verdana,helvetica;
	font-size: 9pt;
}	
a.main:link{
	font-family: verdana,helvetica;
	font-size:9pt; 
	font-weight: bold; 
	color: #FFFFFF;
	text-decoration: none;
}

a.main:visited{
	font-family: verdana,helvetica;
	font-size:9pt; 
	font-weight: bold; 
	color: #FFFFFF;
	text-decoration: none;
}


.button{
  color: #000000;
  background-color: #D8E9FB;
  font-size: 8pt;
  line-height: 10px;
  width:55px;
  text-align: center;
  text-decoration: none;

}

.gobutton{
  color: #000000;
  background-color: #D8E9FB;
  font-size: 8pt;
  line-height:15px;
  width:22px;
  text-align: center;
  text-decoration: none;

}


input[type='submit'] {
  border: 1px solid #000000;
}

.al {
	text-align: left;
	font-size:9pt;
}

.ar {
	text-align: right;
	font-size:9pt;
}

.ac {
	text-align: center;
	font-size:9pt;
}


a.navi:link {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}


a.navi:visted {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}





a.menu:link {
	color: black;
	FONT-WEIGHT: normal;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}

a.menu:hover {
	color: white;
	FONT-WEIGHT: bold;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}

a.menu:visited {
	color: white;
	FONT-WEIGHT:bold;
	font-size:9pt;
	font-family: verdana,helvetica;
	text-decoration: none;
}


#ddscrolltable{

   text-overflow:ellipsis; 
   scrollbar-highlight-color:white; 
   scrollbar-darkshadow-color:#7E97AF; 
   scrollbar-face-color:#C0DBF6; 
   scrollbar-arrow-color:#1E62A5; 
   scrollbar-shadow-color:white;
   scrollbar-track-color:white;
   overflow-y:scroll;  
}

#ddscrolltableTodayPlusOne{

   text-overflow:ellipsis; 
   scrollbar-highlight-color:white; 
   scrollbar-darkshadow-color:#7E97AF; 
   scrollbar-face-color:#C0DBF6; 
   scrollbar-arrow-color:#1E62A5; 
   scrollbar-shadow-color:white;
   scrollbar-track-color:white;
   overflow-y:scroll;  
}

#ddscrolltableTodayPlusTwo{

   text-overflow:ellipsis; 
   scrollbar-highlight-color:white; 
   scrollbar-darkshadow-color:#7E97AF; 
   scrollbar-face-color:#C0DBF6; 
   scrollbar-arrow-color:#1E62A5; 
   scrollbar-shadow-color:white;
   scrollbar-track-color:white;
   overflow-y:scroll;  
}

#ddscrolltableAllTab{

   text-overflow:ellipsis; 
   scrollbar-highlight-color:white; 
   scrollbar-darkshadow-color:#7E97AF; 
   scrollbar-face-color:#C0DBF6; 
   scrollbar-arrow-color:#1E62A5; 
   scrollbar-shadow-color:white;
   scrollbar-track-color:white;
   overflow-y:scroll;  
}
label {
	outline: none; /*for label of radio button*/
}
/*  Style Added For Scrollbar Tooltip */

#tip{
position: absolute;
width: 130px;
border: 1px solid black;
padding: 1px;
background-color: lightyellow;
visibility: hidden;
z-index: 100;
}
/*Start Roll-over effect on  icon (defect raised by Antony) , date :02/05/07 */
#shadowImg{
position: relative;
top: 0px;
left: 0px;
filter:progid:DXImageTransform.Microsoft.MotionBlur(Strength=0, Direction=315, Add="true")
}
/*End Roll-over effect on  icon (defect raised by Antony) , date :02/05/07 */