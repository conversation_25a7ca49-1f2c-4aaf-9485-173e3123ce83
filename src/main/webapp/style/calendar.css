	.CALcpYearNavigation
	{
			font-family: verdana,helvetica;
			font-size: 9pt;
			width:55px;
			background-color:#6677DD;
			text-align:center;
			vertical-align:center;
			text-decoration:none;
			color:#FFFFFF;
			font-weight:bold;
	}
	.CALcpMonthNavigation
			{
			font-family: verdana,helvetica;
			font-size: 9pt;
			width:86px;
			background-color:#6677DD;
			text-align:center;
			vertical-align:center;
			text-decoration:none;
			color:#FFFFFF;
			font-weight:bold;
	}
	.CALcpDayColumnHeader,
	.CALcpYearNavigation,
	.CALcpMonthNavigation,
	.CALcpCurrentMonthDate,
	.CALcpCurrentMonthDateDisabled,
	.CALcpOtherMonthDate,
	.CALcpOtherMonthDateDisabled,
	.CALcpCurrentDate,
	.CALcpCurrentDateDisabled,
	.CALcpTodayText,
	.CALcpTodayTextDisabled,
	.CALcpText
			{
			font-family:arial;
			font-size:8pt;
			}
	TD.CALcpDayColumnHeader
			{
			text-align:right;
			border:solid thin #6677DD;
			border-width:0 0 1 0;
			}
	.CALcpCurrentMonthDate,
	.CALcpOtherMonthDate,
	.CALcpCurrentDate
			{
			text-align:right;
			text-decoration:none;
			}
	.CALcpCurrentMonthDateDisabled,
	.CALcpOtherMonthDateDisabled,
	.CALcpCurrentDateDisabled
			{
			color:#D0D0D0;
			text-align:right;
			text-decoration:line-through;
			}
	.CALcpCurrentMonthDate
			{
			color:#6677DD;
			font-weight:bold;
			}
	.CALcpCurrentDate
			{
			color: #FFFFFF;
			font-weight:bold;
			}
	.CALcpOtherMonthDate
			{
			color:#808080;
			}
	TD.CALcpCurrentDate
			{
			color:#FFFFFF;
			background-color: #6677DD;
			border-width:1;
			border:solid thin #000000;
			}
	TD.CALcpCurrentDateDisabled
			{
			border-width:1;
			border:solid thin #FFAAAA;
			}
	TD.CALcpTodayText,
	TD.CALcpTodayTextDisabled
			{
			border:solid thin #6677DD;
			border-width:1 0 0 0;
			}
	A.CALcpTodayText,
	SPAN.CALcpTodayTextDisabled
			{
			height:20px;
			}
	A.CALcpTodayText
			{
			color:#6677DD;
			font-weight:bold;
			}
	SPAN.CALcpTodayTextDisabled
			{
			color:#D0D0D0;
			}
	.CALcpBorder
			{
			border:solid thin #6677DD;
			}