
	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

	<%@ include file="/taglib.jsp"%>
<c:choose>
    <c:when test="${requestScope.fromFlex != 'true'}">


	<html>
	<head>
	<title>Error &nbsp;-&nbsp;Smart Predict</title>
	
	<SCRIPT language="JAVASCRIPT">

	
	var reason = '<%=request.getParameter("reason")%>';
	
	var attackSessionMsg="<fmt:message key='errors.authorization.attack.session'/>";
	var securityReasonMsg="<fmt:message key='errors.content.notAllowed.urlDescription'/>";
	$(document).ready(function(){
		if(reason == "diffrentUser"){
			document.getElementById("errorDescription").innerHTML = attackSessionMsg;
		}else if(reason == "securityReason"){
			document.getElementById("errorDescription").innerHTML = securityReasonMsg;
		}
		if (history.length == 0){
			document.getElementById("closePage").style.visibility='visible';
			document.getElementById("goBack").style.visibility='hidden';
		}
		else{
			document.getElementById("closePage").style.visibility='hidden';
			document.getElementById("goBack").style.visibility='visible';
		}
	});
	function gobackOrClose(){
		if (history.length == 0) {
		    window.close();
		} else {
		    history.back();
		}
	}
	</SCRIPT>
	</head>

	<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onUnload="call()">

		<h3  style="padding: 20px"><fmt:message key="errors.content.notAllowed"/></h3>
		<p id="errorDescription" style="padding: 15px"><fmt:message key="errors.content.notAllowed.urlDescription"/>
	<br>
	<br>
		</p>

		<p style="padding-left: 15px">
			<a  href="javascript:gobackOrClose()">
				<div id="closePage" style="visibility: hidden ;padding-left: 5px"> <fmt:message key="link.closePage"/></div>
				<div id="goBack" style="visibility: hidden;padding-left: 5px"> <fmt:message key="link.backtoPreviousPage"/></div>
			</a>
		</p>

	</body>
	</html>
 </c:when>
    <c:otherwise>
	<?xml version="1.0" encoding="UTF-8"?>
	<predict>
			<message_xss><fmt:message key="errors.content.notAllowed"/></message_xss>
			<description ><fmt:message key="errors.content.notAllowed.urlDescription"/></description>
			
	</predict>

   </c:otherwise>
</c:choose>





