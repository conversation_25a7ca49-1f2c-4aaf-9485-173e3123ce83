<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
 "http://www.w3.org/TR/html4/strict.dtd">
<%-- <%@ include file="/taglib.jsp"%> --%>
<%-- <%@ taglib uri="/WEB-INF/tags-swallow" prefix="swallow" %> --%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="Content-Script-Type" content="text/javascript">
<meta name="Content-Style-Type" content="text/css">
<title>Session Expire</title>

<script type="text/javascript">
	var timeInSecs;
	var ticker;
	
	
	function startTimer(secs) {
		timeInSecs = parseInt(secs) - 1;
		ticker = setInterval("tick()", 1000); // every second
	}

	function tick() {
		var secs = timeInSecs;
		if (secs > 0) {
			timeInSecs--;
		} else {
			clearInterval(ticker);
			// stop counting at zero
			window.close();
			window.opener.onUnLoad();
		}

		document.getElementById("countdown").innerHTML = secs;
	}

	startTimer(60); // 60 seconds 
	function resetTimer() {
		
		window.close();
		window.opener.startSessiontTimer();
	}
	function sessionExpired() {
		window.close();
		window.opener.onUnLoad();

	}
</script>

</head>
<body style="background:#ECE9DB" onunload="resetTimer()" > 
<div  style="position:absolute;left:0px;top:0px;width:100%;height:60%;overflow: auto;" >
    <table  style="position:absolute;left:0px;width:100%;bgcolor:red">
	<tr><td>&nbsp;&nbsp;</td></tr>
        <tr><td width="100%" id='errMessage' style="text-align:center;font-family:Tahoma; font-size :8pt" ><fmt:message key="autologgedoff.msg.loggedoffIn"/></td></tr>
         <tr><td width="100%" id='errMessage' style="text-align:center;font-family:Tahoma;font-weight:bold; font-size :8pt"><span id="countdown" >60</span> <fmt:message key="autologgedoff.msg.time"/>.</td></tr>
       <tr><td width="100%" id='errMessage' style="text-align:center;font-family:Tahoma; font-size :8pt"> <fmt:message key="autologgedoff.msg.stayLoggedIn"/></td></tr>
    </table>
</div>

<div id='divYesNo' style="position:absolute;width:100%;top:70%;">
<table  width="100%" border="0" cellspacing="0" cellspacing="0">
  <tr style="width:100%;">
       <td width="50%" >
        <input type="button" value="OK" name="btnYes" class="bolButton" style="width:60px;height:21px;font-family:Tahoma; font-size :8pt;float:right;margin-right:20px;" onclick="resetTimer()">
        </td>
    <td width="50%" >
        <input type="button" value="Logout" name="btnNo" class="bolButton" style="width:60px;height:21px;font-family:Tahoma; float:left;margin-left:10px;font-size :8pt" onclick="sessionExpired()">
    </td>
  </tr>
</table>
</div>

</body>
</html>
