<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title>Error &nbsp;-&nbsp;Smart Predict</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">


var message = '${ErrorMessage}';


function gobackOrClose(){
	if (history.length == 0) {
	    window.close();
	} else {
// 	    history.back();

		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;

		var sURL = requestURL + appName+"/logon.do";
		window.location.replace(sURL);

	}
}

</SCRIPT>
</head>

<body leftmargin="0" style="width: 900px" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();onBodyload();" onunload="call()">
<table >
<tr><BR><BR><h3><fmt:message key="error.unexpectedError.duringauthentification"/><BR><fmt:message key="error.contactAdmin" /></h3></tr>

<tr><BR><BR><BR><H2> &nbsp;&nbsp;&nbsp;&nbsp;${ErrorTitle}:<BR>&nbsp;&nbsp;&nbsp;</H2></tr>

<tr><BR><H4> &nbsp;&nbsp;&nbsp;&nbsp;${ErrorMessage}.<BR>&nbsp;&nbsp;&nbsp;</H4></tr>


</table>

<p style="padding-left: 15px">
			<a  href="javascript:gobackOrClose()">
				<div id="closePage" style="padding-left: 5px"> <fmt:message key="link.closePage"/></div>
				<div id="goBack" style="padding-left: 5px"> <fmt:message key="link.backtoPreviousPage"/></div>
			</a>
		</p>
</body>
</html>



