/**
* Export combo box: This is a jQuery component that creates a combo box to export different report
* Export combobox is first introduced as a solution for Mantis 2096
*
* <AUTHOR> Tunisia( Med <PERSON><PERSON>)
* @version 1.0
* /



/**
 * Function to be launched on startup
 */
(function($){
	var selectedIndex = 0;
	var selectedRow = 0;
	var currentlyHighlightedObject = null;
    var headerData = [,];
	// The comboButton
	var comboButton;
	// The comboIcon
	var comboIcon;

	// Global options
	var options ;

	//style maps to manage style updates in one place
	var selectedTr = {
      'background-color' : '#A8C6EE',
      'color' : '#000'
    }
	var deselectedTr = {
      'background-color' : '#fff',
      'color' : '#000'
      
    }
	var hoverTr = {
	  'background-color' : '#CEDBEF',
	  'color' : '#000'
		       }
	
	var objStyle = {
		"background":"#fff",
		"position":"absolute",
		"z-index":"2000",	
		"border":"1px solid",
		"margin":"0px",
	    "border-color":"#696969",
		"margin-left":"0"	
		
	}
	var overflowStyle = {
		"height":"200px",
		"overflow-x": "hidden",
		"overflow-y": "scroll"
	}
	var overflowAuto = {
		"overflow": "auto"
	}

	var images = {
		// Images to be used for the button
		buttonImage:"images/swtExportButtonUp.png",  
		buttonImageHover:"images/swtExportButtonDown.png",
		buttonImageDown:"images/swtExportButtonDown.png", 
        buttonImageDisable:"images/swtExportButtonDisabled.png", 
        // Images to be used for the export icon
        pdfImage:"images/pdfUp.jpg",
        csvImage:"images/csvUp.jpg",
        excelImage:"images/excelUp.jpg",
        pdfImageOver:"images/pdfOver.jpg",
        csvImageOver:"images/csvOver.jpg",
        excelImageOver:"images/excelOver.jpg",
        pdfImageDisabled:"images/pdfDisabled.jpg",
        csvImageDisabled:"images/csvDisabled.jpg",
        excelImageDisabled:"images/excelDisabled.jpg"
	}
	var defaults = {
			// The column to be used to display value for the textbox
			valueCol:0  
	};
	 /**
     *  Start : create an exportSelect
     **/
	$.fn.exportselect = function(optionsHtml) {
		var obj = $(this);
		var table = null;
		var preselectedTr;
		selectedIndex = optionsHtml.selectedIndex?optionsHtml.selectedIndex:0;
		
		// General options
		options = $.extend(defaults, optionsHtml);

		// Styling
		//TODO: Think on styles objects
		var style = "<style>\
			table{\
				font-size:11px; font-family:Verdana;\
				cursor: default;\
				margin: 1px;    \
			    border-color: #696969;\
			    }\
			.columnHeader\
			{\
				border: 1px solid white;\
				padding-right: 10px;\
				background-color: #d6e3fe;\
			}\
			.row0{\
				background-color: #CC9999; color: black;\
			}\
			.row1{\
				background-color: #9999CC; \color: black;\
			}\
			.selectedTr\
			{\
				background-color: #b0c4de;\
				color: #fff;\
			}\
			.deselected\
			{\
				background-color: #fff;\
				color: #000;\
			}\
			.comboButton\
			{ \
			    float:left;\
				margin-left: 0;\
				border: none;\
				height: 17;\
	        	width: 18;\
	        	background-repeat:no-repeat;\
			}\
            .comboIconDisabled\
            {\
                height:17;\
                width :17;\
                cursor:default;\
                background-repeat:no-repeat;\
            }\
            .comboIcon\
            {\
                height:17;\
                width :18;\
                cursor:default;\
                float:left;\
                border-color:696969;\
                border-style:solid;\
                border-width:0;\
            }\
		</style>";
		obj.append(style);
	
		return this.each( 
			function() {		
				obj.hide();
				
				//adding the image and combo button
                obj.before("<div>" +
		                		"<div style='float:left;' id='expsel'></div>" +
		                		"<button  id='expselbtn'/>" +
                			"</div>");                     
				
                comboIcon = obj.prev().find("div[id='expsel']");
				comboButton = obj.prev().find("button");
				
				comboButtonStyle();
                comboIconStyle();
				var tableWidth =65;
				//adding the table that will contain the data
				obj.after("<div>"+
							"<table id='tab' cellspacing='0' width='"+tableWidth+"'  onselectstart='return false' ondragstart='return false'>"+
							"<tbody/>"+
							"</table>"+
						"</div>");
				
				table = obj.next().find("table[id='tab']");
				
				//create the table dynamically
				var tbody = table.find("tbody");
				createTable(headerData, options.dataprovider, tbody);
				obj.append(table);

				//this obj style
				obj.css(objStyle);
				if (options.dataprovider.length > 10){
					obj.css(overflowStyle);
				}else{
					obj.css(overflowAuto);
					if(options.dataprovider.length==2){
						if(isIEunder10){
							obj.css('margin-top','-57');
						}else {
							obj.css("margin-top","-40");
						}
						
					}else {
						if(isIEunder10){
							obj.css('margin-top','-75');
						}else {
							obj.css("margin-top","-58");
						}
					}
				
				}
				
				// Adjust the table's width
				if (table.width() < comboIcon.width()+comboButton.width()){
					table.width(comboIcon.width() + maxLength*4);	
				}
				
				// When mouse is over a <tr>
				obj.find('tr').on('hover',function(){ 
					currentlyHighlightedObject = $(this);
					$(this).css(hoverTr);
				},function(){
					var rowNumber = $(this).find("td:eq(0)").text();
					$(this).css(deselectedTr);
				});
				
				//this code allows us to hide the 1st column
				obj.find("tr").each(function(){
					$(this).find("td:eq(0)").css("display","none");
					$(this).find("th:eq(0)").css("display","none");		
				});		

				//change the image with the selected icon
				obj.find('tr').on( 'click', function(){	
					//if the mouse target is the header, don't do anything
					if($(this).find("td:eq("+options.valueCol+")").text() != "")
					{	
						// Should skip column 1				   
                        var selectItem=$(this).find("td:eq("+options.valueCol+1+")").text();
                        var itemSelected='images/'+$.trim(selectItem).toLowerCase()+'Up.jpg';
                        comboIcon.find('img').attr('src',itemSelected);
                   
						selectedIndex = $(this).find("td:eq(0)").text();						
						obj.hide();	
						for(i=0;i<headerData.length;i++){
							var tdNumber = i+1;
							selectedRow[i] = $(this).find("td:eq("+tdNumber+")").text();
						}
						// Execute the change callback
						if(options.change!=undefined&&options.change!=null){
							options.change();
						}			
					}				
				});	
				
			
				//Execute the change if we click on combo image
				comboIcon.on( 'click', function(){				
				    var rowNumber = 0;
					// Find a preselected Tr
					preselectedTr = $('tr:has(td:containsExact('+selectedIndex+'))');
					preselectedTr.css(selectedTr);currentlyHighlightedObject = preselectedTr;
								
					for(i=0;i<headerData.length;i++){
						var tdNumber = i+1;
						selectedRow[i] = currentlyHighlightedObject.find("td:eq("+tdNumber+")").text();
									}
		 
					// Execute the change callback
					if(options.change!=undefined&&options.change!=null){
						options.change();
						}
					//close the Drop Down if it's open
					if(obj.is(':visible')){
						obj.hide();
					}
                      $(this).blur();
                    
				
				});	
				
				comboIcon.on('hover',function(){
					var selectItem=obj.getSelected(0);
                    var imgsrcOver='images/'+$.trim(selectItem).toLowerCase()+'Over.jpg';
		            comboIcon.find('img').attr('src',imgsrcOver);
		        }, function(){ 
		        	var selectItem=obj.getSelected(0);
                    var imgsrcOut='images/'+$.trim(selectItem).toLowerCase()+'Up.jpg';
		        	comboIcon.find('img').attr('src',imgsrcOut); 
		     
		        });
				
				// the button click handler
				comboButton.on( 'click', function(event){
					event.preventDefault();//only the browsers default action is stopped but the comboButton's click handler still fires.
					if(obj.is(':visible')){
						obj.hide();
					}
					else
					{
						obj.show();
						obj.find("tr").each(function(){
							$(this).css(deselectedTr);			
						});
						$('tr:has(td:containsExact('+selectedIndex+'))').css(selectedTr);
					}
                   $(this).blur();
				});
				
				comboButton.on('hover',function(){//Over
					if (obj.is(':visible')==false)
					comboButton.css('background-image', 'url('+images.buttonImageHover+')');
		        }, function(){ //Out
		        	comboButton.css('background-image', 'url('+images.buttonImage+')');
		        });
				
				
				//hide the object when we click outside the dropdown
				$('html').on( 'click', function(e) {
					var event = (window.event|| e);
					var target = (event.srcElement || event.target);
					if (obj.is(':visible') && target.className != 'columnHeader')
						obj.hide();
					
				});
				
				obj.prev().on( 'click', function(event){
					event.stopPropagation();
				});	

				$('html').on('keydown', function(event){
					try{
						if(preselectedTr!=undefined&&preselectedTr!=[])
							currentlyHighlightedObject = preselectedTr;
						else 
							currentlyHighlightedObject = obj.find('tr').first();

						if (obj.is(':visible'))
						{
							var rowNumber = 0;
							switch (event.which)
							{
								case 38://When the up key is pressed
									obj.focus();
									var previous = currentlyHighlightedObject;
									if(typeof(currentlyHighlightedObject.prev)!='undefined')
										previous=currentlyHighlightedObject.prev();
									
									if(typeof(previous.find)!='undefined')									
										rowNumber = previous.find("td:eq(0)").text();
									if(rowNumber!=""){
										currentlyHighlightedObject = previous;
										preselectedTr = currentlyHighlightedObject;
										currentlyHighlightedObject.css(selectedTr);	
										currentlyHighlightedObject.next().css(deselectedTr);		
									
									}
									
									break;
									
								case 40://When the down key is pressed
									obj.focus();
									var next = currentlyHighlightedObject;
									if(typeof(currentlyHighlightedObject.next)!='undefined')
										next=currentlyHighlightedObject.next();
										
									if(typeof(next.find)!='undefined')
										rowNumber = next.find("td:eq(0)").text();
									if(rowNumber!=""){
										currentlyHighlightedObject = next;
										preselectedTr = currentlyHighlightedObject;
										currentlyHighlightedObject.prev().css(deselectedTr);
										currentlyHighlightedObject.css(selectedTr);
																	
									}
									break;
									
								case 13: //When the return key is pressed	
									if(typeof(currentlyHighlightedObject.find)=='undefined'){
										obj.hide();
										return;
									}
									rowNumber = currentlyHighlightedObject.find("td:eq(0)").text();
									selectedIndex = rowNumber;
									for(i=0;i<headerData.length;i++){
										var tdNumber = i+1;
										selectedRow[i] = currentlyHighlightedObject.find("td:eq("+tdNumber+")").text();
									}
									obj.hide();
									var selectItem=obj.getSelected(0);
                                    var itemSelected='images/'+$.trim(selectItem).toLowerCase()+'Up.jpg';
                                    comboIcon.find('img').attr('src',itemSelected);
									// Execute the change callback
									if(options.change!=undefined&&options.change!=null){
										options.change();
									}	
									break;
									case 27: //When the return key is pressed	
									obj.hide();	
									break;
									
							}
						}
					}catch(error){
						//console.log(error.message+'\n'+error.stack);
					}
				});
			}
		);
	 };
	 
 /**
 *  Start :create a disabled exportSelect
 **/
 $.fn.disabled = function(_disabled) {
		return this.each( 
			function() {
		
				// Disabled == true
				if(_disabled==true){
					comboButton.attr("disabled", "disabled");
					comboIcon.attr("disabled", "disabled");
					comboIconDisabledStyle();
					comboButtonDisabledStyle();
				}
				else{ // Disabled == false
					comboButton.removeAttr("disabled");
					comboIcon.removeAttr("disabled"); 
					comboButtonStyle();
					comboIconStyle();
				}
		});
 }; 
 /**
 * Returns the slected cell based on the given column number
 */
 $.fn.getSelected = function(){
	return options.dataprovider[selectedIndex][headerData[0]];
 }
/**
* Create the body of the table
**/
function createTable(headers, dataprovider, element)
{
	for (var i=0; i < dataprovider.length; i++)
	{
		var tr = document.createElement('tr');
		// Always create the line number column
		var lineNumber = document.createElement('td');
		lineNumber.innerHTML =i;
		tr.appendChild(lineNumber);   
        var td = document.createElement('td');
		td.innerHTML=('<img style="height:16px;vertical-align:bottom;" src="images/'+(dataprovider[i][headers[i]]).toLowerCase()+'Up.jpg"/> '+dataprovider[i][headers[i]]+'');
        tr.appendChild(td);
		// Add tr to the table body
		element.append(tr);
	}
}

/**
 * Creates the combobotton 
 * @param comboButton
 * @param options
 */
function comboButtonStyle()
{
	// Apply style object
    comboButton.removeClass();
	comboButton.addClass('comboButton');
	// Add background
	comboButton.css('background-image', 'url('+images.buttonImage+')');
    // Add hover images
	/*comboButton.on('hover',function(){//over
		comboButton.css('background-image', 'url('+images.buttonImageHover+')');	
	}, function(){//out
		comboButton.css('background-image', 'url('+images.buttonImage+')');
	});*/
}

 /**
 * TODO: add js doc here !!
 */
function comboButtonDisabledStyle()// TODO: change method name into comboButtonDisabledStyle
{
	comboButton.css('background-image', 'url('+images.buttonImageDisable+')');
}

/**
 * TODO: add js doc here !!
 */
function comboIconDisabledStyle() // TODO: change method name into comboIconDisabledStyle
{   comboIcon.removeClass();
    comboIcon.addClass('comboIconDisabled')
	// Set the correct image according to the selected index
	var imageSrc;
	if(selectedIndex==0)
		imageSrc = images.pdfImageDisabled;
	else if(selectedIndex==1)
		imageSrc = images.csvImageDisabled;
	else
		imageSrc = images.excelImageDisabled;
	comboIcon.append('<img  style ="float:left;position :absolute; height:16" src="'+ imageSrc + '"/>');

}

/**
 * Adjust style for the comboIcon component
 */
function comboIconStyle()
{   comboIcon.removeClass();
    comboIcon.addClass('comboIcon');
	// Set the correct image according to the selected index
	var imageSrc;
	if(selectedIndex==0)
		imageSrc = images.pdfImage;
	else if(selectedIndex==1)
		imageSrc = images.csvImage;
	else
		imageSrc = images.excelImage;
	comboIcon.append('<img  style ="float:left;position :absolute; height:16;" src="'+ imageSrc + '"/>');

}

})(jQuery);