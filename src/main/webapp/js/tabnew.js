/***********************************************
* DD Tab Menu II script- � Dynamic Drive DHTML code library (www.dynamicdrive.com)
* This notice MUST stay intact for legal use
* Visit Dynamic Drive at http://www.dynamicdrive.com/ for full source code
***********************************************/

//Set tab to intially be selected when page loads:
//[which tab (1=first tab), ID of tab content to display]:
var initialtab = [1, "sc1"];
var user_language = 'EN';
//Turn menu into single level image tabs (completely hides 2nd level)?
var turntosingle = 0; //0 for no (default), 1 for yes

//Disable hyperlinks in 1st level tab images?
var disabletablinks = 0; //0 for no (default), 1 for yes

////////Stop editting////////////////

var previoustab = "";
var selectedtab = initialtab[1];

if (turntosingle == 1)
	document.write('<style type="text/css">\n#tabcontentcontainer{display: none;}\n</style>');

function expandcontent(cid, aobject){
	if (disabletablinks == 1)
		aobject.onclick = new Function("return false");
		
	if (document.getElementById && turntosingle == 0){
		highlighttab(aobject);
		
		if (previoustab != "")
			document.getElementById(previoustab).style.display = "none";
			if(document.getElementById(cid)!=undefined)
				document.getElementById(cid).style.display = "block";
			previoustab = cid;
	}
}

function changecontent(name, aObj) {
	if(selectedtab == name) {
		if(aObj.className != "current") {
			aObj.className = "current";
		}
	} else {
		if(aObj.className != "hoverall") {
			aObj.className = "hoverall";
		}
	}	
}

function revertback(name, aObj) {
	if(selectedtab == name) {
		if(aObj.className != "current") {
			aObj.className = "current";
		}
	} else {
		if(aObj.className != "default") {
			aObj.className = "default";
		}
	}
}

function changeselected(name) {
	selectedtab = name;
}	

function getselectedtab() {
	return selectedtab;
}	

function getSelectedTabIndex() {
	var tabIndex = 0 ;
	if(typeof tabNames != 'undefined' && tabNames != null && tabNames.length > 0)
		for(var i = 0 ; i < tabNames.length; ++i) {
			if(tabNames[i] == selectedtab) {
				tabIndex = i + 1;
				break;
			}
		}
	return tabIndex;
}


function highlighttab(aobject) {
	if (typeof tabobjlinks == "undefined")
		collectddimagetabs();
	for (i = 0; i < tabobjlinks.length; i++)
		tabobjlinks[i].className = "";
	aobject.className="current";
}

function collectddimagetabs() {
	var tabobj = document.getElementById("ddimagetabs");
	tabobjlinks = tabobj.getElementsByTagName("A");
}

/**
 * START: Added by RK on 13-Mar-2012 for Mantis 1645
 */

/**
 * This function shows tooltip when mouse over on grid scroll bar
 */
function showTooltip() {
	//Get datagrid container
	table = document.getElementById ("ddscrolltable");
	if(table != null) {
		//Create a tooltip container if it does not exist
		var box = document.forms[0];
		if (document.getElementById("div") == null) {
			var a = document.createElement('div');
			box.appendChild(a);
			a.id = "tip";
		}
		//Add listeners to show tooltip
		table.onmouseover = mousein;
		table.onmouseout = mouseout;
		table.onclick = mouseout;
		document.onmousemove = function(event) { mousemove(event)};
	}
}

/**
 * This funcion reset tooltip listener
 */
function resetTooltip() {
	document.onmousemove = function() {};
}

/**
 * This funciton paint tabs and tooltip
 */
function do_onload() {
	// Scroll bar tool tip
	showTooltip();
	if(window.dynamicTab != 'undefined' && window.dynamicTab) {
		do_onload_dynamic_tab();
	} else {
		// check if screen has the tabs
		var objTab = document.getElementById("ddimagetabs");
		if(typeof objTab != "undefined" &&  objTab != null) {
			collectddimagetabs();
			expandcontent(initialtab[1], tabobjlinks[initialtab[0] - 1]);
		}
	}
}

/**
 * END: Added by RK on 13-Mar-2012 for Mantis 1645
 */

function do_onload_dynamic_tab() {
	// This function is used to implement dynamic tabbing where form is sumbmitted each time the tab icon is clicked
	var tabobj = document.getElementById("ddimagetabs");
	if(typeof tabobj != "undefined" &&  tabobj != null ) {
		collectddimagetabs();
		expandcontent(tabNames[0], tabobjlinks[initialtab[0] - 1]);
	}
}

if (window.addEventListener)
	window.addEventListener("load", do_onload, false);
else if (window.attachEvent)
	window.attachEvent("onload", do_onload);
else if (document.getElementById)
	window.onload = do_onload;

//Start---  scroll bar tool tip
var enable = false;
function mousein() {
	enable = true;
}

function mouseout() {
	document.getElementById ("tip").style.visibility = "hidden";
	enable = false;
}

function mousemove(e) {
	var event = (e||window.event);
	var offsetX = (event.offsetX || (($(event.target).offset() != undefined)&& (event.pageX - $(event.target).offset().left + document.getElementById("ddscrolltable").scrollLeft)));
	var offsetY = (event.offsetY || (($(event.target).offset() != undefined) && (event.pageY - $(event.target).offset().top + document.getElementById("ddscrolltable").scrollTop)));
	
	var hightmax = document.getElementById("ddscrolltable").offsetHeight + document.getElementById("ddscrolltable").scrollTop;
	var hightmin = hightmax - 20;
	
	var widthmax = document.getElementById("ddscrolltable").offsetWidth+document.getElementById("ddscrolltable").scrollLeft;
	var widthmin = widthmax - 20;
	if(offsetY > hightmax) {
		mouseout();
	}
	if(hightmin > offsetY && offsetX < widthmin) {
		mouseout();
	}
	if(offsetX > widthmax) {
		mouseout();
	}
	if(enable) {
		tipobj = document.getElementById("tip");
		var offsetxpoint = 0; //Customize x offset of tooltip
		var offsetypoint = 20; //Customize y offset of tooltip
		var rightedge = document.body.clientWidth - event.clientX - offsetxpoint ;
		var bottomedge = document.body.clientHeight - event.clientY - offsetypoint; 
		var leftedge = (offsetxpoint < 0) ? offsetxpoint * (-1) : -1000;
		
		if (rightedge < tipobj.offsetWidth) {
			tipobj.style.left = document.body.scrollLeft + event.clientX - tipobj.offsetWidth + "px"; 
		} else if (event.clientX < leftedge) {
			tipobj.style.left="5px";
		} else {
			tipobj.style.left = offsetX + offsetxpoint - document.getElementById("ddscrolltable").scrollLeft + "px";
		}
		
		if (bottomedge < tipobj.offsetHeight)
			tipobj.style.top = document.body.scrollTop + event.clientY - tipobj.offsetHeight - offsetypoint + "px"; 
		else
			tipobj.style.top = event.clientY+offsetypoint + "px";
		
		tipobj.style.visibility = "visible"
		user_language = getMenuWindow().user_language; 
		  if(user_language == "FR")
		   tipobj.innerHTML = "Nombre des enregistrements : "+ record();
		else
		  tipobj.innerHTML = "No of Records : "+ record();

		  tipobj.style.zIndex = '200';
	}
}
/**
 * START: Modified by RK on 13-Mar-2012 for Mantis 1645
 */
/**
 * This function returns no of records in the grid
 *
 * @return int
 */
function record() {
	if (typeof totalCount != "undefined") {
		return totalCount;
	} else {
		var table = document.getElementById ("ddscrolltable");
		return (table != null) ? (table.getElementsByTagName('tr').length - 1) : 0;
	}
}
/**
 * END: Modified by RK on 13-Mar-2012 for Mantis 1645
 */