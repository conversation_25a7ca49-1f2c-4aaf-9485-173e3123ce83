/**
 * Extending jquery to implement the containsExact
 * 
 * @addedby Saber Chebka see
 *          http://wowmotty.blogspot.com/2010/05/jquery-selectors-adding-contains-exact.html
 */
var require = function(jsfile) {
  var ary = document.getElementsByTagName('script');
  for (var i=0;i<ary.length;i++) {
    if (ary[i].getAttribute('src').match(/import\.js$/)) {
      var jspath = ary[i].getAttribute('src').match(/.+\//);
    }
  }
  var elem = document.createElement('script');
  elem.setAttribute('type', 'text/javascript');
  elem.setAttribute('src', jspath+jsfile);
  document.getElementsByTagName('head')[0].appendChild(elem);
}

$.extend($.expr[":"], {
	// StartsWith: Added by Saber Chebka
	startsWith : $.expr.createPseudo ? $.expr.createPseudo(function(text) {
		return function(elem) {
			if (elem == undefined || text == undefined)
				return false;
			return $.trim(elem.innerHTML.toLowerCase()).indexOf(
					$.trim(text.toLowerCase())) == 0;
		};
	}) :
	// support: jQuery <1.8
	function(elem, i, match) {
		return $.trim(elem.innerHTML.toLowerCase()) === match[3].toLowerCase();
	},
	
	// endsWith extension for jQuery
	endsWith : $.expr.createPseudo ? $.expr.createPseudo(function(text) {
         return function(elem) {
             var index=-1;
           
             if(elem==undefined||text==undefined)
                 return false; 
             index=$.trim(elem.innerText.toLowerCase()).indexOf((text.toLowerCase()));
             return (index!=-1&&index==($.trim(elem.innerText.toLowerCase())).length-1);
         };
     }) :
     // support: jQuery <1.8
     function(elem, i, match) {
         return $.trim(elem.innerHTML.toLowerCase()) === match[3].toLowerCase();
     },
	containsExact : $.expr.createPseudo ? $.expr.createPseudo(function(text) {
		return function(elem) {
			if (elem == undefined || text == undefined)
				return false;
			return $.trim(elem.innerHTML.toLowerCase()) === $.trim(text
					.toLowerCase());
		};
	}) :
	// support: jQuery <1.8
	function(elem, i, match) {
		return $.trim(elem.innerHTML.toLowerCase()) === match[3].toLowerCase();
	},

	containsExactCase : $.expr.createPseudo ? $.expr
			.createPseudo(function(text) {
				return function(elem) {
					if (elem == undefined || text == undefined)
						return false;
					return $.trim(elem.innerHTML) === text;
				};
			}) :
	// support: jQuery <1.8
	function(elem, i, match) {
		return $.trim(elem.innerHTML) === match[3];
	},

	containsRegex : $.expr.createPseudo ? $.expr.createPseudo(function(text) {
		var reg = /^\/((?:\\\/|[^\/]) )\/([mig]{0,3})$/.exec(text);
		return function(elem) {
			return RegExp(reg[1], reg[2]).test($.trim(elem.innerHTML));
		};
	}) :
	// support: jQuery <1.8
	function(elem, i, match) {
		var reg = /^\/((?:\\\/|[^\/]) )\/([mig]{0,3})$/.exec(match[3]);
		return RegExp(reg[1], reg[2]).test($.trim(elem.innerHTML));
	}
});

/**
 * Cookies plugin
 * Based on the Klaus Hartl initial version, found at: https://github.com/carhartl/jquery-cookie
 */
$.cookie = function (key, value, options) {

    // key and at least value given, set cookie...
    if (arguments.length > 1 && String(value) !== "[object Object]") {
        options = jQuery.extend({}, options);

        if (value === null || value === undefined) {
            options.expires = -1;
        }

        if (typeof options.expires === 'number') {
            var days = options.expires, t = options.expires = new Date();
            t.setDate(t.getDate() + days);
        }

        value = String(value);

        return (document.cookie = [
            encodeURIComponent(key), '=',
            options.raw ? value : encodeURIComponent(value),
            options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE
            options.path ? '; path=' + options.path : '',
            options.domain ? '; domain=' + options.domain : '',
            options.secure ? '; secure' : ''
        ].join(''));
    }

    // key and possibly options given, get cookie...
    options = value || {};
    var result, decode = options.raw ? function (s) { return s; } : decodeURIComponent;
    return (result = new RegExp('(?:^|; )' + encodeURIComponent(key) + '=([^;]*)').exec(document.cookie)) ? decode(result[1]) : null;
};

/**
* Example of use: $("div:styleEquals('visibility:hidden')").css("display","none");   
* Added by Saber Chebka
* Updated by Med Amine Ben Ahmed
**/
// TODO: move to the top...
$.extend(jQuery.expr[':'], {
	    styleEquals: function(a, i, m){
	    	var stylesAttr = $(a).attr("style");
	    	// Avoid null or undefined styles for some elements
	    	if(typeof stylesAttr=='undefined'||stylesAttr==null)
	    		return;
	    	
	    	var styles = stylesAttr.split(";");
	    	var found = false;
	    	var style = "";
	    	for (var i = 0; i < styles.length; i++) {
	    		style=styles[i].replace(/\s/g,'');
	    		if (style===m[3]) {
	    			found = true;
	    			break;
	    		}
	    	}
	    	return found;
	    }
	});

