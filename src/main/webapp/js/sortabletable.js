
 /*  Flag Added to find sortType  is Number or other Data Type*/
var numFlag = false;
function SortableTable(oTable,oHeaderTable,sortedValues,totalDivElm)
{
	
	this.element = oTable;
	this.headerElement = oHeaderTable;
	
	this.tHead = oHeaderTable.tHead;

	this.tBody = oTable.tBodies[0];
	this.totalDivElm = totalDivElm;

	this.document = oTable.ownerDocument || oTable.document;
	this.titles = new Array();

	//alert("type sortValues===>"+typeof sortedValues);
	if(typeof sortedValues != 'undefined'){
		this.sortColumn = sortedValues[0];
		//alert("sortedValues[1] - " + sortedValues[1]);
		this.descending = sortedValues[1];
		//alert("this.descending - " + this.descending);
		//this.descending =false;
		// Refer to  UAT_Defects_RABO_Perot_Internal2.xls defect no. 105
		//new flag 'customDescending' is added, its value is changed accordingly.
		//added  for  custom  sorting 
		customDescending = this.descending ;
	}else{
		this.sortColumn = null;
		this.descending = null;
		//added  for  custom  sorting 
		customDescending=false;
	}

	if(this.headerElement.parentElement)
	this.headerElement.parentElement.style.overflowX = "hidden";

	var oThis = this;
	this._headerOnclick = function (e) {
		oThis.headerOnclick(e);
	};
	
	this.element.parentElement.onscroll = function () {
		oThis.onParentScroll();
		hideShownDropDowns(null);
	};
	
	if(this.element.parentElement.parentElement)
	{
		this.element.parentElement.parentElement.onscroll = function () {
			oThis.onParentScroll();
			hideShownDropDowns(null);
		};	
	}
	// only IE needs this
	var win = this.document.defaultView || this.document.parentWindow;
	this._onunload = function () {
		oThis.destroy();
	};
	if (win && typeof win.attachEvent != "undefined") {
		win.attachEvent("onunload", this._onunload);
	}
}

SortableTable.gecko = navigator.product == "Gecko";
SortableTable.msie = /msie/i.test(navigator.userAgent);
// Mozilla is faster when doing the DOM manipulations on
// an orphaned element. MSIE is not
SortableTable.removeBeforeSort = SortableTable.gecko;

SortableTable.prototype.onsort = function () {};

// default sort order. true -> descending, false -> ascending
SortableTable.prototype.defaultDescending = false;

//added  for  custom  sorting 
//customDescending = false ;

// shared between all instances. This is intentional to allow external files
// to modify the prototype
SortableTable.prototype._sortTypeInfo = {};

// adds arrow containers and events
// also binds sort type to the header cells so that reordering columns does
// not break the sort types
SortableTable.prototype.initHeader = function (oSortTypes)
{

	
	var cells = this.tHead.rows[0].cells;
	var l = cells.length;
	var img, c;
	for (var i = 0; i < l; i++)
	{
		// c = cells[i];
		c = searchChildByClass(cells[i], "header-title");
		if (c != null)
		{
			this.titles[i] = c.innerHTML;
			if (oSortTypes[i] != null && oSortTypes[i] != "None")
			{
				c.setAttribute("_sortIndex", i);
				c._sortIndex=i;
				img = this.document.createElement("IMG");
				img.src = "images/blank.png";
				c.appendChild(img);
				if (oSortTypes[i] != null)
					c._sortType = oSortTypes[i];
				if (typeof c.addEventListener != "undefined")
					c.addEventListener("click", this._headerOnclick, false);
				else if (typeof c.attachEvent != "undefined")
					c.attachEvent("onclick", this._headerOnclick);
				else{
					c.onclick = this._headerOnclick;
				}
			}
			else
			{
				c.setAttribute( "_sortType", oSortTypes[i] );
				c._sortType = "None";
			}
		}
	}
	this.updateHeaderArrows();
};

SortableTable.prototype.doDefaultSorting = function() {

	var cells = this.tHead.rows[0].cells;
	var l = cells.length;
	var img, c;
	if (l > 0) {
		c = searchChildByClass(cells[0], "header-title");
		if (typeof c != "undefined" && c != null) {
			// Dispatch a click event
			if (document.createEvent) { // all browsers except IE before version 9
				var event = document.createEvent("HTMLEvents");
				event.initEvent("click", true, false);
				c.dispatchEvent(event);
			} else { // IE browsers before version 9
				c.fireEvent("onclick");
			}
		}

	}

}
// remove arrows and events
SortableTable.prototype.uninitHeader = function () {
	// Start : Code modified for 1054_STL_081 on 23-07-2012 by Vivekanandan
	// Condition added to check this.tHead.rows[0] is not null, 
	//because account break down monitor grid was called through ajax request   
	if(this.tHead.rows[0] != null){
	// End : Code modified for 1054_STL_081 on 23-07-2012 by Vivekanandan
		var cells = this.tHead.rows[0].cells;
		var l = cells.length;
		var c;
		for (var i = 0; i < l; i++) {
			// c = cells[i];
			c = searchChildByClass(cells[i], "header-title");
			if (c != null)
			{
				if (c._sortType != null && c._sortType != "None") {
					c.removeChild(c.lastChild);
					if (typeof c.removeEventListener != "undefined")
						c.removeEventListener("click", this._headerOnclick, false);
					else if (typeof c.detachEvent != "undefined")
						c.detachEvent("onclick", this._headerOnclick);
					c._sortType = null;
					c.removeAttribute( "_sortType" );
				}
			}
		}
	}
};

SortableTable.prototype.updateHeaderArrows = function () {
	var cells = this.tHead.rows[0].cells;
	var l = cells.length;
	var img, c;
	for (var i = 0; i < l; i++) {
		// c = cells[i];
		c = searchChildByClass(cells[i], "header-title");
		if (c != null)
		{
			if (c._sortType != null && c._sortType != "None") {
				img = c.lastChild;
				if (i == this.sortColumn)
				{
					if(typeof sortedValues != 'undefined'){
						if(this.descending=="true")
							img.className = "sort-arrow descending";
						else
							img.className = "sort-arrow ascending";
					}else{
						img.className = "sort-arrow " + (this.descending==true? "descending" : "ascending");
					}
				}
				else
					img.className = "sort-arrow";

			}
		}
	}
};

/*Start : Modified for Mantis 1262 - to validate the date field when drop down button is selected - by Marshal on 03-12-2010*/
SortableTable.prototype.headerOnclick = function (e) {
	// find TD element
	var el = e.target || e.srcElement;
	while (el.tagName != "TD")
		el = el.parentNode;
	if (window.dateFlag === undefined) {  
	this.sort(el._sortIndex);
	} else {
	if (dateFlag == true) {
		var flag = sortDateValidation()
		if (flag == true) {
			this.sort(el._sortIndex);
		}
	}
	}
	//alert("header click");
	// this.sort(SortableTable.msie ? SortableTable.getCellIndex(el) : el.cellIndex);
	//this.sort(el._sortIndex);
};
/*End : Modified for Mantis 1262 - to validate the date field when drop down button is selected - by Marshal on 03-12-2010*/

// IE returns wrong cellIndex when columns are hidden
SortableTable.getCellIndex = function (oTd) {
	var cells = oTd.parentNode.childNodes
	var l = cells.length;
	var i;
	for (i = 0; cells[i] != oTd && i < l; i++)
		;
	return i;
};

SortableTable.prototype.getSortType = function (nColumn) {
	// var c = this.tHead.rows[0].cells[nColumn];
	var cells = this.tHead.rows[0].cells;
	var c = searchChildByClass(cells[nColumn], "header-title");

	if (c != null)
	{
		var val = c._sortType;
		if (val != "")
			return val;
	}
	return "String";
};

SortableTable.prototype.setSortedData = function (aData)
{
	this.sortedData = aData;
};

// only nColumn is required
// if bDescending is left out the old value is taken into account
// if sSortType is left out the sort type is found from the sortTypes array

SortableTable.prototype.sort = function (nColumn, bDescending, sSortType) {
 
	if (sSortType == null)
		sSortType = this.getSortType(nColumn);
//alert("sSortType-"+sSortType);
	// exit if None
	if (sSortType == "None")
		return;
	//alert("selected column-->"+nColumn);
//alert("this.column-->"+this.sortColumn);
	//alert("desccccc-->"+this.descending);

	//alert("filterValues ==>"+typeof filterValues);
	//alert("sorting-->"+nColumn+"  " +"  "+ bDescending+"  "+sSortType);

	if(typeof filterValues != 'undefined'){
	//	alert("before -- > "+this.descending);

		if(nColumn != this.sortColumn){
			this.descending="true";
		}
		
		if(this.descending == "true"){
			this.descending = "false";
		}else
			this.descending = "true";

		//alert("after -- > "+this.descending);
		this.updateHeaderArrows();
		optionClick_server_filter_JSP(nColumn,this.descending,"sort");
		
	}else{

	if (bDescending == null) {
		if (this.sortColumn != nColumn)
			this.descending = this.defaultDescending;
		else
			this.descending = !this.descending;

			
			
			customDescending=this.descending ;

	
	}
	else
		this.descending = bDescending;

	this.sortColumn = nColumn;


	if (typeof this.onbeforesort == "function")
		this.onbeforesort();

	var a;
	if (this.sortedData && this.sortedData[nColumn])
	{
		a = this.sortedData[nColumn];
	}
	else
	{
		var f = this.getSortFunction(sSortType, nColumn);
		a = this.getCache(sSortType, nColumn);
		a.sort(f);
		/*
		if (this.descending)
			a.reverse();
		*/
	}

	var tBody = this.tBody;
	if (SortableTable.removeBeforeSort) {
		// remove from doc
		var nextSibling = tBody.nextSibling;
		var p = tBody.parentNode;
		p.removeChild(tBody);
	}

	// insert in the new order
	var l = a.length;
	for (var i = 0; i < l; i++)
	{
		var idx = this.descending?l-i-1:i;
		tBody.appendChild(a[idx].element);
	}

	if (SortableTable.removeBeforeSort) {
		// insert into doc
		p.insertBefore(tBody, nextSibling);
	}

	this.updateHeaderArrows();

	if (!this.sortedData || !this.sortedData[nColumn])
	{
		this.destroyCache(a);
	}

	if (typeof this.onsort == "function")
		this.onsort();
	}
};

SortableTable.prototype.asyncSort = function (nColumn, bDescending, sSortType) {
	var oThis = this;
	this._asyncsort = function () {
		oThis.sort(nColumn, bDescending, sSortType);
	};
	window.setTimeout(this._asyncsort, 1);
};

SortableTable.prototype.getCache = function (sType, nColumn) {
	var rows = this.tBody.rows;
	var l = rows.length;
	var a = new Array(l);
	var r;
	for (var i = 0; i < l; i++) {
		r = rows[i];
		a[i] = {
			value:		this.getRowValue(r, sType, nColumn),
			element:	r
		};
	};
	return a;
};

SortableTable.prototype.destroyCache = function (oArray) {
	var l = oArray.length;
	for (var i = 0; i < l; i++) {
		oArray[i].value = null;
		oArray[i].element = null;
		oArray[i] = null;
	}
};

SortableTable.prototype.getRowValue = function (oRow, sType, nColumn) {
	// if we have defined a custom getRowValue use that
	if (this._sortTypeInfo[sType] && this._sortTypeInfo[sType].getRowValue)
		return this._sortTypeInfo[sType].getRowValue(oRow, nColumn);

	var s;
	var c = oRow.cells[nColumn];
	if ( typeof c.textContent != "undefined" ||  typeof c.innerText != "undefined")
 		s = c.textContent || c.innerText;
	else
		s = SortableTable.getInnerText(c);
	return this.getValueFromString(s, sType);
};

SortableTable.getInnerText = function (oNode) {
	var s = "";
	var cs = oNode.childNodes;
	var l = cs.length;
	for (var i = 0; i < l; i++) {
		switch (cs[i].nodeType) {
			case 1: //ELEMENT_NODE
				s += SortableTable.getInnerText(cs[i]);
				break;
			case 3:	//TEXT_NODE
				s += cs[i].nodeValue;
				break;
		}
	}
	return s;
};

SortableTable.prototype.getValueFromString = function (sText, sType) {
	if (this._sortTypeInfo[sType])
		return this._sortTypeInfo[sType].getValueFromString( sText );
	return sText;
	/*
	switch (sType) {
		case "Number":
			return Number(sText);
		case "CaseInsensitiveString":
			return sText.toUpperCase();
		case "Date":
			var parts = sText.split("-");
			var d = new Date(0);
			d.setFullYear(parts[0]);
			d.setDate(parts[2]);
			d.setMonth(parts[1] - 1);
			return d.valueOf();
	}
	return sText;
	*/
	};

SortableTable.prototype.getSortFunction = function (sType, nColumn) {
	//Start: Modified by Bala on 16-03-2010 for Mantis: 851 - sorting issue
	// sType is Number (or) currencyPat1 (or) currencyPat2, set the numFlag value is true. 
	if(sType == "Number" || sType == "currencyPat1" || sType == "currencyPat2")
		numFlag = true;
	//End: Modified by Bala on 16-03-2010 for Mantis: 851 - sorting issue	
	if (this._sortTypeInfo[sType]){
		 return this._sortTypeInfo[sType].compare;
	}
	return SortableTable.basicCompare;
};

SortableTable.prototype.destroy = function () {	
	this.uninitHeader();
	var win = this.document.parentWindow;
	if (win && typeof win.detachEvent != "undefined") {	// only IE needs this
		win.detachEvent("onunload", this._onunload);
	}
	this._onunload = null;
	this.element = null;
	this.tHead = null;
	this.tBody = null;
	this.document = null;
	this._headerOnclick = null;
	this.sortTypes = null;
	this._asyncsort = null;
	this.onsort = null;
};

// Adds a sort type to all instance of SortableTable
// sType : String - the identifier of the sort type
// fGetValueFromString : function ( s : string ) : T - A function that takes a
//    string and casts it to a desired format. If left out the string is just
//    returned
// fCompareFunction : function ( n1 : T, n2 : T ) : Number - A normal JS sort
//    compare function. Takes two values and compares them. If left out less than,
//    <, compare is used
// fGetRowValue : function( oRow : HTMLTRElement, nColumn : int ) : T - A function
//    that takes the row and the column index and returns the value used to compare.
//    If left out then the innerText is first taken for the cell and then the
//    fGetValueFromString is used to convert that string the desired value and type

SortableTable.prototype.addSortType = function (sType, fGetValueFromString, fCompareFunction, fGetRowValue) {
	this._sortTypeInfo[sType] = {
		type:				sType,
		getValueFromString:	fGetValueFromString || SortableTable.idFunction,
		compare:			fCompareFunction || SortableTable.basicCompare,
		getRowValue:		fGetRowValue
	};
};

// this removes the sort type from all instances of SortableTable
SortableTable.prototype.removeSortType = function (sType) {
	delete this._sortTypeInfo[sType];
};

SortableTable.prototype.onParentScroll = function () {
		
		if(this.headerElement && this.headerElement.parentElement && this.element && this.element.parentElement && this.element.parentElement.parentElement)
		{
			this.headerElement.parentElement.scrollLeft = this.element.parentElement.parentElement.scrollLeft;
		}

		if(this.totalDivElm && this.headerElement && this.headerElement.parentElement)
		this.totalDivElm.scrollLeft = this.headerElement.parentElement.scrollLeft;

};


// added  for  custom  sorting 
SortableTable.prototype.getCustom =function () {

return  customDescending;
}


SortableTable.basicCompare = function compare(n1, n2) {
// added  for  custom  sorting 
var  des = SortableTable.prototype.getCustom();
	  /* To Sort Numbers form 0 to largest*/
		if (n1.value >= 0 && numFlag == true && n2.value >= 0){
			var time1 = ""+n1.value
			var time = time1.split(":");
			//alert(time.length);
			if(time.length <= 1){
				return n1.value - n2.value;
			}else{
				return 1;
			}
		  /* To Sort Numbers form 0 to largest*/
		}
//	}
	else{
		if(des==false)
		{
		if(n1.value==0)
			return 1;
		if(n2.value==0)
			return -1;
		}
		else if(des==true)
		{

		if(n1.value==0)
		return -1;
		if(n2.value==0)
		return 1;
		}
		// added  for  custom  sorting 

			if (n1.value < n2.value)
				return -1;
			if (n2.value < n1.value)
				return 1;
			return 0;
	}
};

SortableTable.idFunction = function (x) {
	return x;
};

SortableTable.toUpperCase = function (s) {
	return s.toUpperCase();
};

// Accepts the date in dd/MM/yyyy format
SortableTable.toDate = function (si) {
	var s = trim(si);
	if (! s)
	{
		return "";
	}

	var parts = s.split("/");
	var d = new Date(0);

	// european format: dd-MM-yyyy
	d.setFullYear(parts[2]);
	d.setDate(parts[0]);
	d.setMonth(parts[1] - 1);

	return d.valueOf();
};

// Accepts the date in dd/MM/yyyy format

SortableTable.toDate1 = function (si) {
	var s = trim(si);
	if (! s)
	{
		return "";
	}

	var parts = s.split("/");
	var d = new Date(0);

	// Take the format: dd/MM/yyyy
	d.setFullYear(parts[2]);
	d.setDate(parts[0]);
	d.setMonth(parts[1] - 1);

	return d.valueOf();
};

SortableTable.toDate1Time = function (si) {
//	alert("si " + si);

	var s = trim(si);
	if (! s)
	{
//		alert("it is empty , returning balnk");
		return "";
	}
	var dateTime = s.split(" ");

	var d = new Date(0);

	if(dateTime.length > 0 )
	{
		var parts = dateTime[0].split("/");

		// Take the format: dd/MM/yyyy
		d.setFullYear(parts[2]);
		d.setDate(parts[0]);
		d.setMonth(parts[1] - 1);
	}
	if(dateTime.length > 1)
	{
		var parts = dateTime[1].split(":");
		d.setHours(parts[0],parts[1],parts[2]);
	}
	//alert(d.valueOf());
	return d.valueOf();
};

SortableTable.toDate2Time = function (si) {
	var s = trim(si);
	if (! s)
	{
		return "";
	}
	var dateTime = s.split(" ");

	var d = new Date(0);

	if(dateTime.length > 0 )
	{
		var parts = dateTime[0].split("/");

		// Take the format: dd/MM/yyyy
		d.setFullYear(parts[2]);
		d.setDate(parts[1]);
		d.setMonth(parts[0] - 1);
	}
	if(dateTime.length > 1)
	{
		var parts = dateTime[1].split(":");
		d.setHours(parts[0],parts[1],parts[2]);
	}

	return d.valueOf();
};

// Accepts the date in MM/dd/yyyy format
SortableTable.toDate2 = function (si) {
	var s = trim(si);
	if (! s)
	{
		return "";
	}

	var parts = s.split("/");
	var d = new Date(0);

	// european format: dd-MM-yyyy
	d.setFullYear(parts[2]);
	d.setDate(parts[1]);
	d.setMonth(parts[0] - 1);

	return d.valueOf();
};
 /*  Method Added to find sortType  is Number or other Data Type*/
SortableTable.Number = function (si) {
	alert("");
	numFlag = true;	
};
 /*  Method Added to find sortType  is Number or other Data Type*/
// Accepts the amount in 999,999,999.99 format
SortableTable.toAmount1 = function (si) {

	var s = new String(trim(si));
	if (! s)
	{
		return "";
	}
	s = s.replace(/,/g,"");
	return new Number(s).valueOf();
};

// Accepts the amount in 999.999.999,99 format
SortableTable.toAmount2 = function (si) {

	var s = new String(trim(si));
	
	if (! s)
	{
		return "";
	}
	s = s.replace(/\./g,"");
	s = s.replace(/,/g,".");
	return new Number(s).valueOf();
};


SortableTable.prototype.doManualSorting = function (columnNumber)
{
	
	var cells = this.tHead.rows[0].cells;
	var l = cells.length;
	var img, c;
	if (l > 0)
	{
		c = searchChildByClass(cells[columnNumber], "header-title");
		// Dispatch a click event
		if (document.createEvent) { // all browsers except IE before version 9
			var event = document.createEvent("HTMLEvents");
			event.initEvent("click", true, false);
			c.dispatchEvent(event);
		} else { // IE browsers before version 9
			c.fireEvent("onclick");
		}
	}
}

// Accepts the amount in 999,999,999.99 format
SortableTable.toIPAddress = function (si) {
	var s = new String(trim(si));
	if (! s)
	{
		return "";
	}
	s = s.replace(/\./g,"");
	return new Number(s).valueOf();
};

// add sort types
SortableTable.prototype.addSortType("Number", Number);
SortableTable.prototype.addSortType("CaseInsensitiveString", SortableTable.toUpperCase);
SortableTable.prototype.addSortType("Date", SortableTable.toDate);  // it accepts the date in dd/MM/yyyy format
SortableTable.prototype.addSortType("datePat1", SortableTable.toDate1); // it accepts the date in dd/MM/yyyy format
SortableTable.prototype.addSortType("datePat2", SortableTable.toDate2); // it accepts the date in MM/dd/yyyy format
SortableTable.prototype.addSortType("String");
SortableTable.prototype.addSortType("currencyPat1", SortableTable.toAmount1); // Accepts the amount in 999,999,999.99 format
SortableTable.prototype.addSortType("currencyPat2", SortableTable.toAmount2); // Accepts the amount in 999.999.999,99 format
SortableTable.prototype.addSortType("IPAddress", SortableTable.toIPAddress); // Accepts the amount in 999.999.999,99 format
SortableTable.prototype.addSortType("datePat1Time", SortableTable.toDate1Time); // it accepts the date in dd/MM/yyyy format
SortableTable.prototype.addSortType("datePat2Time", SortableTable.toDate2Time); // it accepts the date in dd/MM/yyyy format
// None is a special case
// None is a special case
