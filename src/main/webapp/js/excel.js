/*----------------------------------------------------------------------------\
|                             XLSheet class 0.1                               |
| Ada<PERSON>                                 http://www.adasasistemas.com |
|-----------------------------------------------------------------------------|
| Created 2004-04-15 | <EMAIL>       | Updated 2004-04-16 |
\----------------------------------------------------------------------------*/



//
function XLSheet(tableId,headertableId, sortSpec,filterSpec,isDafaultSort,filterValues,sortedValues,totalDivId)
{

	var tableElm = document.getElementById(tableId);

	// Hearder  Table
	var headertableElm = document.getElementById(headertableId);


	var totalDivElm = document.getElementById(totalDivId);


	var tableObj = new SortableTable(tableElm,headertableElm,sortedValues,totalDivElm);

	// properties
	this.dataTable = tableObj;

	this.tableId = tableId;
	
	this.numColumns = 0;
	if(this.dataTable.tBody.rows.length > 0 )
		this.numColumns = this.dataTable.tBody.rows[0].cells.length;
	

	this.filterControls = new Array();
	this.sortedData = new Array();
	this.filters = new Array();
        var numRow=this.dataTable.tBody.rows.length;
	// initializations
	for (var i=0; i<this.numColumns; i++)
	{
		if (!filterSpec || (filterSpec.charAt(i) == "1"))
		{
			var selectId = tableId + "__select_" + i;
			var headerCellElm = this.dataTable.tHead.rows[0].cells[i];
			var filterTitle = getFilterTitle(headerCellElm.title);			
           // Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
			resizeColHeader(i,numRow,tableId,headerCellElm);
			createHeaderCell(headerCellElm, selectId,filterTitle);
			var selectElm = document.getElementById(selectId);
			this.filterControls[i] = selectElm;
			if (selectElm)
			{
				if(typeof filterValues != 'undefined'){
					if(filterValues != "all"){
						if(filterValues[i] != "All")
							int_changeStyle(selectElm,true);
					}
				}
				
				initSelectBox(selectElm);
				selectElm.xl = this;
			}
		}
		if (!filterSpec || (filterSpec.charAt(i) == "2"))
		{
			var selectId = tableId + "__select_" + i;
			var headerCellElm = this.dataTable.tHead.rows[0].cells[i];
            //Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
			resizeColHeader(i,numRow,tableId,headerCellElm);
			var filterTitle = "none";
			
			createHeaderCell(headerCellElm, selectId,filterTitle);
			var selectElm = document.getElementById(selectId);
			
			this.filterControls[i] = selectElm;
		}
	}
	this.dataTable.initHeader(sortSpec || []);

	for (var i=0; i<this.filterControls.length; i++)
	{
		var selectElm = this.filterControls[i];
		if (selectElm )
		{
			if(typeof filterValues == 'undefined'){
				this.sortedData[i] = sortValues(this.dataTable, i);
				selectElm.populate(this.sortedData[i], i);
				
			}		
			else{
				this.sortedData[i] = sortValues(this.dataTable, i);
				selectElm.populate(this.sortedData[i], i);
				}
		}
		else
		{
			this.sortedData[i] = sortValues(this.dataTable, i);
		}
	}
	this.dataTable.setSortedData(this.sortedData);
	var xlobj = this;
	this.dataTable.onsort = function () { xlobj.onsort(); };
	if(this.dataTable.tBody.rows.length > 0 )
	{
		this.adjustHeaderTableColumnWidth();
	}
	else
	{
		// table is empty
		var cellArr =  this.dataTable.tHead.rows[0].cells;
		for(var idx = 0 ; idx < cellArr.length; ++idx)
		{
			cellArr[idx].title = "";
			cellArr[idx].align = "center";
		}
	}

	// set the table cursor as hand
	this.dataTable.tBody.style.cursor="pointer";
	if(typeof isDafaultSort == 'undefined' || isDafaultSort == 'true')
	{
		if(typeof filterValues == 'undefined'){
			this.dataTable.doDefaultSorting();
		}
		this.resetColors();
	}
}


XLSheet.prototype.recreateFilters = function (tableId,headertableId, sortSpec,filterSpec,isDafaultSort,filterValues,sortedValues,totalDivId){
var tableElm = document.getElementById(tableId);

	// Hearder  Table
	var headertableElm = document.getElementById(headertableId);


	var totalDivElm = document.getElementById(totalDivId);


	this.tableId = tableId;
	
	this.numColumns = 0;
	if(this.dataTable.tBody.rows.length > 0 )
		this.numColumns = this.dataTable.tBody.rows[0].cells.length;
	

	this.filterControls = new Array();
	this.sortedData = new Array();
	this.filters = new Array();
        var numRow=this.dataTable.tBody.rows.length;
	// initializations
	for (var i=0; i<this.numColumns; i++)
	{
		if (!filterSpec || (filterSpec.charAt(i) == "1"))
		{
			var selectId = tableId + "__select_" + i;
			var headerCellElm = this.dataTable.tHead.rows[0].cells[i];
			var filterTitle = getFilterTitle(headerCellElm.title);			
           // Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
//			resizeColHeader(i,numRow,tableId,headerCellElm);
//			createHeaderCell(headerCellElm, selectId,filterTitle);
			var selectElm = document.getElementById(selectId);
			this.filterControls[i] = selectElm;
			if (selectElm)
			{
				if(typeof filterValues != 'undefined'){
					if(filterValues != "all"){
						if(filterValues[i] != "All")
							int_changeStyle(selectElm,true);
					}
				}
				
				initSelectBox(selectElm);
				selectElm.xl = this;
			}
		}
		if (!filterSpec || (filterSpec.charAt(i) == "2"))
		{
			var selectId = tableId + "__select_" + i;
			var headerCellElm = this.dataTable.tHead.rows[0].cells[i];
            //Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
			resizeColHeader(i,numRow,tableId,headerCellElm);
			var filterTitle = "none";
			
			createHeaderCell(headerCellElm, selectId,filterTitle);
			var selectElm = document.getElementById(selectId);
			
			this.filterControls[i] = selectElm;
		}
	}
	this.dataTable.initHeader(sortSpec || []);

	for (var i=0; i<this.filterControls.length; i++)
	{
		var selectElm = this.filterControls[i];
		if (selectElm )
		{
			if(typeof filterValues == 'undefined'){
				this.sortedData[i] = sortValues(this.dataTable, i);
				selectElm.populate(this.sortedData[i], i);
				
			}		
			else{
				this.sortedData[i] = sortValues(this.dataTable, i);
				selectElm.populate(this.sortedData[i], i);
				}
		}
		else
		{
			this.sortedData[i] = sortValues(this.dataTable, i);
		}
	}
	this.dataTable.setSortedData(this.sortedData);
	var xlobj = this;
	this.dataTable.onsort = function () { xlobj.onsort(); };
	if(this.dataTable.tBody.rows.length > 0 )
	{
		this.adjustHeaderTableColumnWidth();
	}
	else
	{
		// table is empty
		var cellArr =  this.dataTable.tHead.rows[0].cells;
		for(var idx = 0 ; idx < cellArr.length; ++idx)
		{
			cellArr[idx].title = "";
			cellArr[idx].align = "center";
		}
	}

	// set the table cursor as hand
	this.dataTable.tBody.style.cursor="pointer";
	if(typeof isDafaultSort == 'undefined' || isDafaultSort == 'true')
	{
		if(typeof filterValues == 'undefined'){
			this.dataTable.doDefaultSorting();
		}
		this.resetColors();
	}
	this.dataTable.doDefaultSorting();
};
/* -----------------------
    METHODS
   ----------------------- */


XLSheet.prototype.isRowVisible = function(idx)
{

	var row = this.dataTable.tBody.rows[idx];
	return (row.style.display != "none");
};


XLSheet.prototype.onsort = function() {};


XLSheet.prototype.onfilter = function() {};


XLSheet.prototype.filterChange = function(selectElm)
{
	var filterInfo = selectElm.options[selectElm.selectedIndex].getAttribute("value");
	var column = selectElm.id.split("__")[1].split("_")[1];
	var filterObj = createFilter(filterInfo, this.sortedData[column], this.filters[column], this.dataTable.titles[column]);
	if (filterObj)
	{
		this.filterData(selectElm, filterObj);
	}
	else
	{
		this.filterElement = selectElm;
	}
}


XLSheet.prototype.filterData = function(selectElm, filterObj)
{
	var filterInfo = selectElm.options[selectElm.selectedIndex].getAttribute("value");
	var column = selectElm.id.split("__")[1].split("_")[1];
	var tableObj = this.dataTable;
	var data = tableObj.tBody.rows;
	var datalen = data.length;
	this.filters[column] = filterObj;

	for (var i=0; i<datalen; i++)
	{
		var row = data[i];
		var value = tableObj.getRowValue(row, tableObj.getSortType(column), column);
		filter(row, column, value, this.filters[column]);
	}
	selectElm.changeStyle(filterInfo != "special_all");

	for (var i=0; i<this.filterControls.length; i++)
	{
		var fctrl = this.filterControls[i];
		if (fctrl)
		{
			fctrl.populate(this.sortedData[i], i);
		}
	}

	this.onfilter();
}


/* -----------------------
    PRIVATE FUNCTIONS
   ----------------------- */


function createFilter(filterInfo, values, filterObj, title)
{
	var result = null;
	var filterFunc = filterInfo.split("_");

	if (filterFunc[0] == "value")
	{
		var filterValue = filterInfo.substring(6);
		result = new CustomFilter("==", filterValue, false);
	}
	else if (filterFunc[0] == "special")
	{
		if (filterFunc[1] == "all")
		{
			result = new DummyFilter();
		}
		
		else if (filterFunc[1] == "empties")
		{
			result = new CustomFilter("==", "", true);
		}
		else if (filterFunc[1] == "nonempties")
		{
			result = new CustomFilter("!=", "", true);
		}
	}

	return result;
}


function createHeaderCell(cellElm, selectId,filterTitle)
{
	var title = cellElm.innerHTML;
	
	if(filterTitle=="none"){
		var content = "";
			content += "<table class=\"simpleSelectTable\" style=\"background:white\" cellspacing=\"0\" cellpadding=\"0\">";
				content += "<tr style=\"background:white\">";
					content += "<td class=\"header-title\" nowrap=\"true\">" + title + "</td>";
				
				content += "</tr>";
			content += "</table>";
			
		

	}else{
		var cellWidth = cellElm.width;
		/* Start : Code modified for Mantis 2073 by M.Bouraoui on 28/03/2013 */
		//use &#x25BC; to replace "6" in firefox
		var content = "";
				content += "<span class=\"select\" size=\"1\" id=\"" + selectId + "\" onchange=\"this.xl.filterChange(this);\"  onkeypress=\"keyPressed(this)\">";
			content += "<table class=\"simpleSelectTable\" style=\"background:white\" cellspacing=\"0\" cellpadding=\"0\">";
				content += "<tr style=\"background:white\">";
					content += "<td class=\"header-title\" nowrap=\"true\">" + title + "</td>";
					content += "<td align=\"center\" valign=\"middle\" title=\""+filterTitle+"\" class=\"button\" onclick=\"toggleDropDown(searchParentByClass(this, 'select'))\" onmousedown=\"this.style.border='2px inset buttonhighlight'\" onmouseup=\"this.style.borderTop='1px buttonhighlight';this.style.borderBottom='1px buttonhighlight';this.style.borderLeft='1px buttonhighlight';this.style.borderRight='2px outset buttonhighlight';\">";
					content += "<span style=\"position: relative; left: 0; top: -2; width: 100%;\">"+(isFirefox?"&#x25BC;":"6")+"</span>";
					content += "</td>";
				content += "</tr>";
			content += "</table>";
			content += "<div class=\"dropDown\" onclick=\"optionClick(event)\" onmouseover=\"optionOver(event)\" onmouseout=\"optionOut(event)\" onmousedown=\"optionDown(event)\" style=\"width: "+cellWidth+";\" ></div>";
		content += "</span>";
		/* End : Code modified for Mantis 2073 by M.Bouraoui on 28/03/2013 */
	}

	cellElm.className = "header-container";
	cellElm.innerHTML = content;
}


function sortValues(tableObj, nColumn)
{
	var sSortType = tableObj.getSortType(nColumn);
	var f = tableObj.getSortFunction(sSortType, nColumn);
	var a = tableObj.getCache(sSortType, nColumn);
	a.sort(f);
	return a;
}


function filter(GUIElm, columnIdx, value, filterObj)
{
	if (typeof(GUIElm.filteredby) == "undefined")
	{
		GUIElm["filteredby"] = new Array();
	}

	GUIElm.filteredby.remove(columnIdx);
	if (filterObj.filters(value))
	{
		GUIElm.filteredby.push(columnIdx);
	}

	GUIElm.style.display = (GUIElm.filteredby.length == 0)?"":"none";
}


function doCustomFilter(elmid)
{
	var elm = document.getElementById(elmid);
	var xl = elm.xl;
	var frmname = elm.id + "-frm";
	var frm = document.forms[frmname];

	var tableObj = xl.dataTable;
	var column = xl.filterElement.id.split("__")[1].split("_")[1];
	var sSortType = tableObj.getSortType(column);

	var op1 = frm.op1.options[frm.op1.selectedIndex].value;
	var val1 = tableObj.getValueFromString(frm.val1.value, sSortType);
	var nexo = frm.nexo[0].checked?frm.nexo[0].value:frm.nexo[1].value;
	var op2 = frm.op2.options[frm.op2.selectedIndex].value;
	var val2 = tableObj.getValueFromString(frm.val2.value, sSortType);

	if (! op1)
	{
		alert("Debe escoger el tipo del primer filtro");
		return;
	}

	var filterObj;
	var f1 = new CustomFilter(op1, val1, false);
	if (op2)
	{
		var f2 = new CustomFilter(op2, val2, false);
		filterObj = new GroupFilter(f1, nexo, f2);
	}
	else
	{
		filterObj = f1;
	}

	setDialogVisible(elm, false);
	xl.filterData(xl.filterElement, filterObj);
}


function showCustomFilter(elm, title, filterinfo)
{
	setText(elm, "title", title);

	var frmname = elm.id + "-frm";
	var frm = document.forms[frmname];

	var f1, f2, nexoIdx;
	if (filterinfo && filterinfo.vfilters)

	{
		f1 = filterinfo.vfilters[0];
		f2 = filterinfo.vfilters[1];
		nexoIdx = (filterinfo.operator=="and")?0:1;
	}
	else
	{
		f1 = filterinfo;
		f2 = null;
		nexoIdx = 0;
	}

	frm.op1.selectedIndex = 1;
	frm.val1.value = "";
	frm.nexo[nexoIdx].checked = true;
	frm.op2.selectedIndex = 0;
	frm.val2.value = "";

	if (f1 && f1.operator)
	{
		setSelectValue(frm.op1, f1.operator);
		frm.val1.value = f1.value;
	}
	if (f2 && f2.operator)
	{
		setSelectValue(frm.op2, f2.operator);
		frm.val2.value = f2.value;
	}

	setDialogVisible(elm, true);
}

XLSheet.prototype.adjustHeaderTableColumnWidth = function()
{
	var dataTableCellsColl = this.dataTable.tBody.rows[0].cells;
		
	var headerTableCellsColl = this.dataTable.tHead.rows[0].cells;
		
	for(var idx = 0 ; idx < headerTableCellsColl.length ; ++idx)
	{
		if(dataTableCellsColl[idx] && headerTableCellsColl[idx])
			headerTableCellsColl[idx].width = dataTableCellsColl[idx].width;
	}
}

XLSheet.prototype.getFilterStatus = function()
{
	var filterStatus,f="",filtervalue="";
	var filterVlaueStatus=""; 
	for(var idx = 0 ; idx < this.numColumns ; ++idx)
	{
		
		var selectId = this.tableId + "__select_" + idx;

		var selectElement = document.getElementById(selectId);


		if(selectElement != null){
			var selectedIndex = selectElement.selectedIndex;
	        var selectedValue =  selectElement.options[selectElement.selectedIndex].getAttribute('value');
		}

		filtervalue +=  selectedValue + "|";
		


	}

	filterVlaueStatus = filtervalue.split("|");
	return filterVlaueStatus ; 
};

XLSheet.prototype.setColumnFilterStatus = function(filterArray)
{
}

XLSheet.prototype.getNextScrollIntoView = function(movId)
{
	var rowsColl = this.dataTable.tBody.rows;
	for(var idx = 0 ; idx < rowsColl.length; ++idx)
	{
		
		
		var hiddenElement = this.dataTable.tBody.rows[idx].getElementsByTagName("input")[0];
		var movementId = hiddenElement.value;
		
		if(movementId == movId ){
		
			this.dataTable.tBody.rows[idx].scrollIntoView();
			break;
		}
		
		
	}
	return ; 
}

XLSheet.prototype.getNextUnselectedRow = function()
{
	

	var rowsColl = this.dataTable.tBody.rows;

	var lastSelectedRowIndex ;

	for(var idx = 0 ; idx < rowsColl.length; ++idx)
	{
		
		if(isRowSelected(rowsColl[idx]))
		{
			lastSelectedRowIndex = idx ;
		}
	}
	
	
	var retValue = this.getNextUnselectedRow1(lastSelectedRowIndex);

	if(retValue == -1)
		retValue = this.getPrevUnselectedRow(lastSelectedRowIndex);

	return retValue ;

}

XLSheet.prototype.getNextUnselectedRow1 = function(index)
{

	var rowsColl = this.dataTable.tBody.rows;

	var lastSelectedRowIndex ;

	for(var idx = index+1 ; idx < rowsColl.length; ++idx)
	{
		if(this.isRowVisible(idx) && isRowSelected(rowsColl[idx]) == false)
		{

		
		var hiddenElement = this.dataTable.tBody.rows[idx].getElementsByTagName("input")[0];
		var movementId = hiddenElement.value;

		return movementId ; 
		}
	}

	return -1;

}

XLSheet.prototype.getPrevUnselectedRow = function(index)
{
	var rowsColl = this.dataTable.tBody.rows;

	var lastSelectedRowIndex ;

	for(var idx = index-1 ; idx > 0 ; --idx)
	{
		if(this.isRowVisible(idx) && isRowSelected(rowsColl[idx]) == false)
		{
			var hiddenElement = this.dataTable.tBody.rows[idx].getElementsByTagName("input")[0];
			var movementId = hiddenElement.value;

			return movementId ;
			
		}
	}

	return -1;

}

XLSheet.prototype.resetAllFilters = function()
{
	for(var idx = 0 ; idx < this.numColumns ; ++idx)
	{
		var selectId = this.tableId + "__select_" + idx;

		var selectElement = document.getElementById(selectId);
		/* Start:code Modified by Sudhakar on 25-May-2011 for Mantis 1400:Sweeps Prior to Cut-off: Filter should not be lost on refresh of grid data */
		/*Check the selectelement is null*/
		if(selectElement != null){
		
		var parentElement = searchChildByClass(selectElement,"option").parentElement;
		var divElementsColl = searchChildByClass(selectElement,"option").parentElement.children;
		}
		/* End:code Modified by Sudhakar on 25-May-2011 for Mantis 1400:Sweeps Prior to Cut-off: Filter should not be lost on refresh of grid data */

		if(divElementsColl.length > 0  && divElementsColl[0].value == "special_all")
		{
				optionClickManual(divElementsColl[0]);	
		}
		else 
			alert("problem in code ");
	}
}


XLSheet.prototype.setFilterStatus = function(filterArray)
{

	for(var idx = 0 ; idx < this.numColumns ; ++idx)
	{

		if( filterArray[idx] == "special_all" || filterArray[idx] == "undefined") {	
			continue;
		}
		var selectId = this.tableId + "__select_" + idx;

		var selectElement = document.getElementById(selectId);

		if(selectElement != null){
			var selectedIndex = selectElement.selectedIndex;
	
			var selectedValue =  selectElement.options[selectElement.selectedIndex].getAttribute('value');
		
		
		var parentElement = searchChildByClass(selectElement,"option").parentElement;
		var divElementsColl = searchChildByClass(selectElement,"option").parentElement.children;

		}
	
		for(var i = 0 ; i < divElementsColl.length ; ++i)
		{
			if(divElementsColl[i].getAttribute('value') == filterArray[idx])
			{
			optionClickManual(divElementsColl[i]);				
				break;
			}
		}
		if(i == divElementsColl.length ) 
		{
			alert("Filter criteria will be refreshed. Please select new filter criteria.")
			this.resetAllFilters();
			break;
		}
	}

}

XLSheet.prototype.setFilterStatus1 = function(filterArray)
{

	var flag="true"	 ;
	for(var idx = 0 ; idx < this.numColumns ; ++idx)
	{
		if( filterArray[idx] == "special_all")
			continue;

		var selectId = this.tableId + "__select_" + idx;

		var selectElement = document.getElementById(selectId);


		var selectedIndex = selectElement.selectedIndex;

		var selectedValue =  selectElement.options[selectElement.selectedIndex].value;

		
		var parentElement = searchChildByClass(selectElement,"option").parentElement;
		var divElementsColl = searchChildByClass(selectElement,"option").parentElement.children;
	
		for(var i = 0 ; i < divElementsColl.length ; ++i)
		{
			if(divElementsColl[i].value == filterArray[idx])
			{
				
				break;
			}
		}	
		if(i == divElementsColl.length)			
		{
			flag="false";

		}
	}
	if(flag == "true")
	{

	for(var idx = 0 ; idx < this.numColumns ; ++idx)
	{
		if( filterArray[idx] == "special_all")
			continue;

		var selectId = this.tableId + "__select_" + idx;

		var selectElement = document.getElementById(selectId);


		var selectedIndex = selectElement.selectedIndex;

		var selectedValue =  selectElement.options[selectElement.selectedIndex].value;

		
		var parentElement = searchChildByClass(selectElement,"option").parentElement;
		var divElementsColl = searchChildByClass(selectElement,"option").parentElement.children;
	
		for(var i = 0 ; i < divElementsColl.length ; ++i)
		{
			if(divElementsColl[i].value == filterArray[idx])
			{
				divElementsColl[i].setAttribute("selected", 1);
				optionClickManual(divElementsColl[i]);
				
				break;
			}
		}	
		
	}
	
}
}


XLSheet.prototype.resetColors = function()
{
	var rows = this.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (this.isRowVisible(i))
		{
			setClassName(rows[i], count % 2 ? "odd" : "even");
			count++;
		}
	}	
}
/**
 * Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
 *
 * this Function used to resize the Column when the titleHeader is very wide
 *  and it exceeded the width reserved. 
 * 
 * @param numColumn
 * @param numRow
 * @param tableId
 * @param headerCellElm
 */
function resizeColHeader(numColumn,numRow,tableId,headerCellElm)
{     
	// get the initial header width of the column as being set in the JSP 
	var maxwidth=document.getElementById(tableId).rows[0].cells[numColumn].width;
	var extraWidth=(getStyleWidth(".sort-table THEAD TD:active")+getStyleWidth(".sort-arrow")+getStyleWidth(".sort-table THEAD TD"));
	//retrieved extra space from the initial header 
	var limWidth=(maxwidth-extraWidth)+1;
	// create a temporary span to get the width of title
	var spanTag = document.createElement("span");
	spanTag.id = "span1";
	document.body.appendChild(spanTag);
	var widthTitle =headerCellElm.innerHTML.visualLength("span1");

	if(widthTitle>limWidth)
		
{		for(var j=0;j<numRow;j++) 
	 		document.getElementById(tableId).rows[j].cells[numColumn].width=(String(widthTitle+extraWidth)+"px");  
}
	//remove the Span
	spanTag.parentNode.removeChild(spanTag);

} 
/**
 *Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
 * To get the Width
 * 
 */
String.prototype.visualLength = function(id) {
	var ruler = document.getElementById(id);
	ruler.innerHTML = this;
	return ruler.offsetWidth;
}
/**
 * Added by Med Amine for Mantis 2027:Excluded Outstandings queue summary screen: remove column filters etc.
 *   
 * Used to get the width of the sorting icon (v) + padding +  border + 
 * margin of headerCell
 * 
 * @param className
 * @returns {Number}
 */
function getStyleWidth(className) {
	var padding=0;
	var width=0;
    var border=0;
    var margin=0;
    var classes = document.styleSheets[2].rules || document.styleSheets[2].cssRules;
    for(var x=0;x<classes.length;x++) {
 
       
   if(classes[x].selectorText==className) {
	   
	   if(parseInt(classes[x].style.paddingRight)+parseInt(classes[x].style.paddingLeft))
             padding= parseInt(classes[x].style.paddingRight)+parseInt(classes[x].style.paddingLeft) ;
	   
	   if(parseInt(classes[x].style.width))
             width=parseInt(classes[x].style.width);
	   
	   if(parseInt(classes[x].style.borderRightWidth)+parseInt(classes[x].style.borderLeftWidth))
             border=parseInt(classes[x].style.borderRightWidth)+parseInt(classes[x].style.borderLeftWidth) ;
	   
	   if(parseInt(classes[x].style.marginRight)+parseInt(classes[x].style.marginLeft))
             margin=parseInt(classes[x].style.marginRight)+parseInt(classes[x].style.marginLeft);
        
        }
    }
    return (padding+width+border+margin);
  
}

