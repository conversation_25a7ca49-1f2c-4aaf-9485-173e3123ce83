fixMozillaZIndex=true; //Fixes Z-Index problem  with Mozilla browsers but causes odd scrolling problem, toggle to see if it helps
_menuCloseDelay=500;
_menuOpenDelay=150;
_subOffsetTop=2;
_subOffsetLeft=-7;

with(menuStyle=new mm_style()){
bordercolor="black";
borderstyle="solid";
borderwidth=0;
fontfamily="Verdana,Helvetica";
fontsize="90%";
fontstyle="normal";
menubgcolor="#DBDBD7";
menucolor="#CDCDC7";
offbgcolor="#CDCDC7";
offcolor="#CDCDC7";
onbgcolor="#8F9397";
oncolor="#8F9397";
overfilter="Fade(duration=0.5);Alpha(opacity=100);";
overflow="scroll";
padding=0;
pagebgcolor="#CDCDC7";
pagecolor="#8F9397";
separatorsize=0;
separatorcolor="gray";
subimagepadding=2;
}

with(menuLogoffStyle=new mm_style()){
borderstyle="solid";
borderwidth=0;
fontfamily="Verdana,Helvetica";
fontsize="90%";
fontstyle="normal";
oncolor="red";
overfilter="Fade(duration=0.5);Alpha(opacity=100);";
overflow="scroll";
padding=0;
separatorsize=0;
subimagepadding=2;
}

with(menuItemStyle=new mm_style()){
bordercolor="#8F9397";
borderstyle="solid";
borderwidth=1;
fontfamily="Verdana,Helvetica";
fontsize="90%";
fontstyle="normal";
offbgcolor="white";
offcolor="black";
onbgcolor="#1F64AF";
onborder="0px solid #E8F3FE";
oncolor="white";
overfilter="Shadow(color=#777777', Direction=135, Strength=5);Alpha(opacity=100);";
padding=5;
pagebgcolor="#C5C7C7";
pagecolor="black";
separatorcolor="#C5C7C7";
separatorsize=0;
subimage="images/arrow.gif";
subimagepadding=7;
swap3d=true;
}
with(milonic=new menuname("LogOff Menu")){
alwaysvisible=1;
/*Start  Defect no.39 (defect raised 12/20/2006 )<sumit> defect sheet no 15/04/2007 , date :1/05/07 */
screenposition="right";
style=menuLogoffStyle;
left="offset=-20"; 
/*End Defect no.39 (defect raised 12/20/2006 )<sumit> defect sheet no 15/04/2007 , date :1/05/07 */
top=1;
menuwidth="10%";
menualign="right";
orientation="horizontal";
aI("image=images/Logoff_"+logoffButtonLang+".gif;overimage=images/Logoff_R_"+logoffButtonLang+".gif;subimageposition=0;clickfunction=logout();");
}
//dynamic part starts