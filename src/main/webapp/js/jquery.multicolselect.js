/**
* Multicolumn combo box: This is a jQuery component that creates a combo box with multi-colmns dropdown list
* Multicolumn combobox is first introduced as a solution for Mantis 1556
* Source code is based on jquery.multiselect.js found at: 
* http://code.google.com/p/jquerymulticolumnselectbox/source/browse/trunk/jquerymulticolselect/?r=3
* <AUTHOR> SwallowTech Tunisia
* @adapted by Mansour Blanco, SwallowTech  Tunisia
* @version 1.0
*/


/**
 * Function to be launched on startup
 */
(function($){
	var selectedIndex = 0;
	var selectedRow = new Array();
	var currentlyHighlightedObject = null;

	//style maps to manage style updates in one place
	var selectedTr = {
      'background-color' : '#3399ff',
      'color' : '#fff'
    }
	var deselectedTr = {
      'background-color' : '#fff',
      'color' : '#000'
    }
	var objStyle = {
		"background":"#fff",
		"position":"absolute",
		"z-index":"2000",	
		"border":"1px solid"
	}
	//The height needs to be a multiple of row height to
	//avoid having partially displayed items
	var overflowStyle = {
		//15 (height of row) * 13 + border (1px * 2) 
		"height":"135px",
		"overflow-x": "visible",
		"overflow-y": "scroll"
	}
	var overflowAuto = {
		"overflow": "auto"
	}
	var textInputStyle = {
		'height':'20px',
		'border-top':'1px solid #abadb3',
		'border-right': 'none',
		'border-left': 'none',
		'border-bottom': 'none'
	}
	var comboboxName = null;
	var calendarTextInput = null;
	var defaults = {
			// Images to be used for the button
			buttonImage:"images/swtComboButtonUp.png",  
			buttonImageHover:"images/swtComboButtonOver.png",
			buttonImageDown:"images/swtComboButtonDown.png", 
			// The column to be used to display value for the textbox
			valueCol:1  
	};
	$.fn.multicolselect = function(options) {
		var obj = $(this);
		var table = null;
		var preselectedTr;
        var rowNumber = 0;
		var options = $.extend(defaults, options);

		selectedIndex = options.selectedIndex?options.selectedIndex:0;

		// Styling
		obj.append("<style>"+
				".multiColumnCombo{"+
				"	font-size:11px; font-family:Verdana;"+
				"	cursor: default"+
				"}"+
				".columnHeader"+
				"{"+
				"	border: 1px solid white;"+
				"	padding-right: 10px;"+
				"	background-color: #d6e3fe;"+
				"}"+
				".row0{"+
				"	background-color: #CC9999; color: black;"+
				"}"+
				".row1{"+
				"	background-color: #9999CC; color: black;"+
				"}"+
				".comboButton"+
				"{"+
				"	position: absolute;"+
				"	margin-top: 1;"+
				"	border: none;"+
				"	width: 19;"+
				"	height: 20;"+
				"	clear: both;"+
				"       background-repeat:no-repeat;"+
				"}"+
			+"</style>");
		return this.each( 
			function() {	
				obj.hide();
				
				//adding the text input and combo button
				obj.before("<div><input type='text'  name='"+options.name+"' id='mltsel' value='' >" /*readonly='readonly'*/
					+ "<input id='mltselbtn' type='button'/></div>");
				calendarTextInput = options.calendarTextInput;
				comboboxName  = options.name; 
				var textInput = obj.prev().find("input[type='text']");
				textInput.css(textInputStyle);
				var comboButton = obj.prev().find("input[type='button']");
				comboButton.addClass('comboButton');
				createComboButton(comboButton, options);

				//adding the table that will contain the data
				obj.after("<div>"+
					"<table id='tab' class='multiColumnCombo' cellspacing='1' onselectstart='return false' ondragstart='return false'>"+
					"<tbody/>"+
					"</table>"+
					"</div>");
				
				//create the table dynamically
				table = obj.next().find("table[id='tab']");
				var tbody = table.find("tbody");
				tbody.append(createHeaders(options.headerData));
				createTable(options.headerData, options.dataprovider, tbody);
				obj.append(table);

				//this obj style
				obj.css(objStyle);
				if (options.dataprovider.length > 10){
					obj.css(overflowStyle);
				}else{
					obj.css(overflowAuto);
				}
				
				// Adjust the table's width
				if (obj.width() < textInput.width()+comboButton.width()){
					obj.width(textInput.width() +comboButton.width());	
				}

				// Update textinput
				preselectedTr = $('#tab tr:has(td:containsExact('+selectedIndex+'))');

				textInput.val(preselectedTr.find("td:eq("+options.valueCol+")").text());
				for(i=0;i<options.headerData.length;i++){
					var tdNumber = i+1;
					selectedRow[i] = preselectedTr.find("td:eq("+tdNumber+")").text();
				}
				if (typeof options.descriptionElement != 'undefined'){
					options.descriptionElement.text(obj.getSelected(1));
				}

				// When mouse is over a <tr>
				obj.find('tr').on('hover',function(){
					//if the mouse target is the header, don't do anything on hover
					if($(this).find("td:eq("+options.valueCol+")").text() == "")
						return;
					clearTr(obj);
					preselectedTr = $(this)
					preselectedTr.css(selectedTr);
				},function(){
					// TODO.............	dynamically set class of a tr			
					//$("tr:odd").addClass("row0");
					//$("tr:even").addClass("row1");
					/*var columnIndex = $(this).find("td:eq(0)").text();
					$(this).addClass(function(index, currentClass) {
						var addedClass;
						if(columnIndex%2==0){
							addedClass = "row0";
						}else{
							addedClass = "row1";
						}
						return addedClass;
					  });*/
					rowNumber = $(this).find("td:eq(0)").text();
					//if(rowNumber==selectedIndex)
					//	return;
					$(this).css(deselectedTr);
					preselectedTr.css(selectedTr);
				});
				
				//this code allows us to hide the 1st column
				obj.find("tr").each(function(){
					$(this).find("td:eq(0)").css("display","none");
					$(this).find("th:eq(0)").css("display","none");		
				});		

				//fill the text input with the selected value
				obj.find('tr').on( 'click', function(){
					//if the mouse target is the header, don't do anything
					if($(this).find("td:eq("+options.valueCol+")").text() != "")
					{	
						// Should skip column 1
						textInput.val($(this).find("td:eq("+options.valueCol+")").text());
						
						selectedIndex = $(this).find("td:eq(0)").text();						
						obj.hide();	
						for(i=0;i<options.headerData.length;i++){
							var tdNumber = i+1;
							selectedRow[i] = $(this).find("td:eq("+tdNumber+")").text();
						}
						if (typeof options.descriptionElement != 'undefined'){
							options.descriptionElement.text(obj.getSelected(1));
						}
						// Execute the change callback
						if(options.change!=undefined&&options.change!=null){
							options.change();
						}
					}	
					 calendarTextInput.value = textInput.val();
				});	
				
				// Implement the on change event: The first row that contains the written text will be highlighthened
				textInput.keyup(function(event){
					// Do not take any action for buttons keyup,keydown and enter key
					if(event.which==38||event.which==40||event.which==13){
						return;
					}
						
					if(!obj.is(':visible')){
						obj.show();
					}
					obj.find("tr").each(function(){
						$(this).css(deselectedTr);
					});

					var trSelected = $('#tab tr:has(td:eq(1):startsWith('+textInput.val()+')):first');
					while (trSelected.find("td:eq(0)").text() == '')
					{	
						var str = textInput.val().slice(0, -1)
						textInput.val(str);
						trSelected = $('#tab tr:has(td:eq(1):startsWith('+textInput.val()+')):first');
					}
					// Find a preselected Tr
					preselectedTr = trSelected;
					preselectedTr.css(selectedTr);


					if (event.which != 8 && event.which != 16) {
						var longeur = textInput.val().length;
						textInput.val(preselectedTr.find("td:eq("+options.valueCol+")").text());
						setInputSelection(textInput, longeur, textInput.val().length);
					}

					rowNumber = preselectedTr.find("td:eq(0)").text();

					var trPos = $('#tab tr').index(preselectedTr) * preselectedTr.height();
					//we need to take the header height into account
					if (trPos + 2* preselectedTr.height() >= obj.height())
						obj.scrollTop(trPos);
					else
						obj.scrollTop(0);
				});

				//display the dropdown if we click on text input or on button
				textInput.on( 'click', function(){
					if(obj.is(':visible')){
						obj.hide();
					}
					else
					{	
						obj.show();
						obj.find("tr").each(function(){
							$(this).css(deselectedTr);		
						});	
						// Find a preselected Tr
						clearTr(obj);
						preselectedTr = $('#tab tr:has(td:startsWith('+textInput.val()+')):first');
						preselectedTr.css(selectedTr);
					}
				});		
				
				textInput.focusout(function(){
					textInput.val(preselectedTr.find("td:eq("+options.valueCol+")").text());
					for(i=0;i<options.headerData.length;i++){
						var tdNumber = i+1;
						selectedRow[i] = preselectedTr.find("td:eq("+tdNumber+")").text();
					}
					if (typeof options.descriptionElement != 'undefined'){
						options.descriptionElement.text(obj.getSelected(1));
					}
				});

				// the button click handler
				comboButton.mousedown(function(){
					if(obj.is(':visible')){
						obj.hide();
					}
					else
					{
						//$(this).css('background-image', 'url('+options.buttonImageDown+')');
						obj.show();
						obj.find("tr").each(function(){
							$(this).css(deselectedTr);
						});
						clearTr(obj);
						preselectedTr = $('#tab tr:has(td:containsExact('+selectedIndex+'))');
						preselectedTr.css(selectedTr);
					}
				});

				//hide the object when we click outside the dropdown
				$('html').on('click', function(e) {
					var event = (window.event|| e);
					var target = (event.srcElement || event.target);
					if (obj.is(':visible') && target.className != 'columnHeader')
						obj.hide();
				});
				obj.prev().on('click', function(event){
					event.stopPropagation();
				});

				$('html').on('keydown', function(event){
					try{
						if(preselectedTr!=undefined&&preselectedTr!=[])
							currentlyHighlightedObject = preselectedTr;
						else 
							currentlyHighlightedObject = obj.find('tr').first();

						if (obj.is(':visible'))
						{
							var rowNumber = 0;
							switch (event.which)
							{
								case 38://When the up key is pressed
									var previous = currentlyHighlightedObject;
									if(currentlyHighlightedObject.prev().is('tr'))
										previous=currentlyHighlightedObject.prev();
									
									if(previous.find("td:eq("+options.valueCol+")").text() == "")
										return;
									clearTr(obj);
									rowNumber = previous.find("td:eq(0)").text();
									obj.scrollTop(parseInt(rowNumber) * currentlyHighlightedObject.height())
									currentlyHighlightedObject = previous;
									preselectedTr = currentlyHighlightedObject;
									currentlyHighlightedObject.css(selectedTr);	
									currentlyHighlightedObject.next().css(deselectedTr);		
									textInput.val(currentlyHighlightedObject.find("td:eq("+options.valueCol+")").text());
									break;
									
								case 40://When the down key is pressed
									var next = currentlyHighlightedObject;
									if(currentlyHighlightedObject.next().is('tr'))
										next=currentlyHighlightedObject.next();
									else
										return;
									clearTr(obj);
									rowNumber = next.find("td:eq(0)").text();
									obj.scrollTop(parseInt(rowNumber) * currentlyHighlightedObject.height())
									currentlyHighlightedObject = next;
									preselectedTr = currentlyHighlightedObject;
									currentlyHighlightedObject.prev().css(deselectedTr);
									currentlyHighlightedObject.css(selectedTr);
									textInput.val(currentlyHighlightedObject.find("td:eq("+options.valueCol+")").text());											
									break;
									
								case 13: //When the return key is pressed	
									if(typeof(currentlyHighlightedObject.find)=='undefined'){
										obj.hide();
										return;
									}
									textInput.val(currentlyHighlightedObject.find("td:eq("+options.valueCol+")").text());
									rowNumber = currentlyHighlightedObject.find("td:eq(0)").text();
									selectedIndex = rowNumber;
									for(i=0;i<options.headerData.length;i++){
										var tdNumber = i+1;
										selectedRow[i] = currentlyHighlightedObject.find("td:eq("+tdNumber+")").text();
									}
									if (typeof options.descriptionElement != 'undefined'){
										options.descriptionElement.text(obj.getSelected(1));
									}
									obj.hide();
									// Execute the change callback
									if(options.change!=undefined&&options.change!=null){
										options.change();
									}	
									break;
							}
						}
					}catch(error){
						//console.log(error.message+'\n'+error.stack);
					}
				});
			}
		);
	 };

	 /**
	 * Returns the slected cell based on the given column number
	 */
	 $.fn.getSelected = function(column){
		return selectedRow[column];
	 }
	
	/* This function is used to clear all highlighted rows to prevent 
	 * the problem of having multiple rows selected simultaneously*/
	function clearTr(object)
	{
		object.find('tr').each(function() {
			$(this).css(deselectedTr);
		})
	}

	function updateTextInput(textInput, preselectedTr, options, object) {

	}

	function setInputSelection(input, startPos, endPos) {
        if (typeof input.selectionStart != "undefined") {
            input.selectionStart = startPos;
            input.selectionEnd = endPos;
        } else if (document.selection && document.selection.createRange) {
            // IE branch
            input.focus();
            input.select();
            var range = document.selection.createRange();
            range.collapse(true);
            range.moveEnd("character", endPos);
            range.moveStart("character", startPos);
            range.select();
        }
    }
	/**
	* Create the headers
	**/
	function createHeaders(headers)
	{
		var tr = document.createElement('tr');
		
		var lineNumber = document.createElement('th');
		tr.appendChild(lineNumber);
		for (var j = 0; j < headers.length; j++)
		{
			var th = document.createElement('th');
			th.className = "columnHeader"; 
			th.innerHTML = headers[j];
			tr.appendChild(th);
		}
		return tr;
	}

	/**
	* Create the body of the table
	**/
	function createTable(headers, dataprovider, element)
	{
		for (var i=0; i < dataprovider.length; i++)
		{
			var tr = document.createElement('tr');
			
			// Always create the line number column
			var lineNumber = document.createElement('td');
			lineNumber.innerHTML = i;
			tr.appendChild(lineNumber);
			
			// Calculate the correct style based on row number
			/*
			if(i%2==0)
				tr.className="row0";
			else
				tr.className="row1";
			*/
			for (var j = 0; j < headers.length; j++)
			{
				var td = document.createElement('td');
				td.innerHTML = dataprovider[i][headers[j]];
				td.style.whiteSpace = 'nowrap';
				td.title = headers[j];
				tr.appendChild(td);
			}
			// Add tr to the table body
			element.append(tr);
		}
	}

	/**
	 * Creates the combobotton
	 * @param comboButton
	 * @param options
	 */
	function createComboButton(comboButton, options)
	{
		comboButton.css('background-image', 'url('+options.buttonImage+')');
		comboButton.on('hover',function(){
			comboButton.css('background-image', 'url('+options.buttonImageHover+')');
		}, function(){
			comboButton.css('background-image', 'url('+options.buttonImage+')');
		});
	}
})(jQuery);
