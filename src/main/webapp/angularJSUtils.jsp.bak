<script type="text/javascript">



var method = '${requestScope.method}';
document.write('<base href="' + window.location.href  + '"" />');

$( window ).on( "load", function() {
	try{
		var f = document.createElement("form");
		f.setAttribute('id',"formAngular");
		document.body.appendChild(f);

		<% Enumeration<String> parameterNames = request.getParameterNames();

			while (parameterNames.hasMoreElements()) {

		    String paramName = SwtUtil.cleanXSSReflected(parameterNames.nextElement());
		    String paramValue = request.getParameter(paramName);
		    %>
		    var paramName = '<%= paramName%>';
		    var paramValue = '<%=paramValue%>';
		    $('<input>', {
			    type: 'hidden',
			    name: paramName,
			    value: paramValue
			}).appendTo('#formAngular');
			<%
		    }
		%>
		
		  $('<input>', {
			    type: 'hidden',
			    id: 'csrf',
			    name: 'csrf',
			    value: $('meta[name=csrf_token]').attr("content")
			}).appendTo('#formAngular');
		  
		  
		  
		}catch(e){
			console.log('e',e)
		}
		$( "iframe" ).each(function( index ) {
			if($( this ).width() == 0){
				$( this ).css('visibility', 'hidden');	
			}
		});
});

window.onload = function () {
	
    setTitleSuffix(document.forms[0]);
	setParentChildsFocus();	
};

document.onkeydown = rewriteUrl;
//document.onkeyup = rewriteUrl;

function rewriteUrl(e) {
	  e = e || window.event;
	 if((e.which || e.keyCode)==116){
	        e.preventDefault();
	        if(method){
	        	 var currentUrl = window.location.href;
	       	  if(method && currentUrl.indexOf("method=") == -1)
	       		$( "#formAngular" ).submit();
	        }else 
	        	$( "#formAngular" ).submit();
	    }
}


function getMessage(label, lang){
    console.log('label,',label, lang)
    if(window.opener == null)
        return label;


	return	getMenuWindow().getMessage(label, lang);
}

function exportData(type , str) {
	
	document.getElementById('exportDataForm').action='flexdataexport.do?method=' + type ;
	document.getElementById('exportData').value= str;
	document.getElementById('exportDataForm').submit();
	
}

</script>