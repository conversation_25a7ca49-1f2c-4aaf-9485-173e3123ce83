<whirlycache>
   <default-cache>maintenance</default-cache>

   <cache name="maintenance">
      <backend>com.whirlycott.cache.impl.ConcurrentHashMapImpl</backend>

      <tuner-sleeptime>300</tuner-sleeptime>

      <!-- evicts oldest items when pruning -->
      <!-- <policy>com.whirlycott.cache.policy.FIFOMaintenancePolicy</policy> -->

      <!-- evicts least recently used items when pruning -->
      <!-- <policy>com.whirlycott.cache.policy.LRUMaintenancePolicy</policy> -->

      <!-- evicts least frequently used items when pruning -->
      <policy>com.whirlycott.cache.policy.LFUMaintenancePolicy</policy>

      <maxsize>2000</maxsize>
   </cache>

   <cache name="monitor">
      <backend>com.whirlycott.cache.impl.ConcurrentHashMapImpl</backend>

      <tuner-sleeptime>300</tuner-sleeptime>

      <!-- evicts oldest items when pruning -->
      <!-- <policy>com.whirlycott.cache.policy.FIFOMaintenancePolicy</policy> -->

      <!-- evicts least recently used items when pruning -->
      <!-- <policy>com.whirlycott.cache.policy.LRUMaintenancePolicy</policy> -->

      <!-- evicts least frequently used items when pruning -->
      <policy>com.whirlycott.cache.policy.LFUMaintenancePolicy</policy>

      <maxsize>2000</maxsize>
   </cache>

</whirlycache>