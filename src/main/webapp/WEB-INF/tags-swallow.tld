<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE taglib
    PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
    "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">

<taglib>
 <!-- after this the default space is
 "http://java.sun.com/j2ee/dtds/Web-jsptaglibrary_1_1.dtd"
  -->

 <!-- The version number of this tag library -->
 <tlib-version>1.0</tlib-version>

 <!-- The JSP specification version required to function -->
 <jsp-version>1.1</jsp-version>

 <!-- The short name of this tag library -->
 <short-name>tags_swallow</short-name>

 <!-- Public URI that uniquely identifies this version of the tag library -->
 <uri>/WEB-INF/tags-swallow</uri>

 <!-- General information about this tag library -->
 <description>
    Custom Tag libaray for Swallow Tech project.
 </description>

 <!-- ******************** Defined Custom Tags *************************** -->

 <!-- Menu tag -->
 <tag>
  <name>menu</name>
  <tag-class>org.swallow.web.MenuTag</tag-class>
  <body-content>empty</body-content>
 </tag>
 <tag>
  <name>openProfile</name>
  <tag-class>org.swallow.web.OpenProfileTag</tag-class>
  <body-content>empty</body-content>
 </tag>
 <tag>
  <name>simplemenu</name>
  <tag-class>org.swallow.web.SimpleMenuTag</tag-class>
  <body-content>empty</body-content>
 </tag>
</taglib>
