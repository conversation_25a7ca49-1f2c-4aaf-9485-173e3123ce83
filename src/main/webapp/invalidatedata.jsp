

	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	
	<%@ include file="/taglib.jsp"%>
	<c:if test="${requestScope.fromFlex ne 'true'}">
	<html>
	<head>
	<title>Error &nbsp;-&nbsp;Smart Predict</title>
	
	<SCRIPT language="JAVASCRIPT">

	$(document).ready(function(){
		if (history.length == 0){
			document.getElementById("closePage").style.visibility='visible';
			document.getElementById("goBack").style.visibility='hidden';
		}
		else{
			document.getElementById("closePage").style.visibility='hidden';
			document.getElementById("goBack").style.visibility='visible';
		}
	});
	function gobackOrClose(){
		if (history.length == 0) {
		    window.close();
		} else {
		    history.back();
		}
	}
	</SCRIPT>
	</head>
	
	<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onUnload="call()">
	
		<h3  style="padding: 20px">${ErrorMessage}</h3>
		<p style="padding: 15px"><fmt:message key="errors.content.notAllowed.description"/>
	<br>
	<br>
		<c:forEach var="maliciousContent" items="${requestScope.maliciousContents}">
			- " <c:out value="${maliciousContent}" /> "<br>
		</c:forEach>
		</p>

		<p style="padding-left: 15px">
			<a  href="javascript:gobackOrClose()">
				<div id="closePage" style="visibility: hidden ;padding-left: 5px"> <fmt:message key="link.closePage"/></div>
				<div id="goBack" style="visibility: hidden;padding-left: 5px"> <fmt:message key="link.backtoPreviousPage"/></div>
			</a>
		</p>

	</body>
	</html>
</c:if>
<c:if test="${requestScope.fromFlex eq 'true'}">
	<?xml version="1.0" encoding="UTF-8"?>
	<predict>
			<message_xss>${ErrorMessage}</message_xss>
			<description><fmt:message key="errors.content.notAllowed.description"/></description>
			<cause>
				<![CDATA[
					<c:forEach var="maliciousContent" items="${requestScope.maliciousContents}">
						- " <c:out value="${maliciousContent}" /> "<br>
					</c:forEach>
					]]>
			
			</cause>
			
	</predict>

</c:if>





