<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Payment Archive Search - SMART-Predict</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

	<script type="text/javascript">
		var screenRoute = "paymentArchiveSearch";
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');

		
		function openChildWindow(methodName){
			var param = '/' + appName + '/dashboardPCM.do?method=dashboardDetails';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=1270,height=780,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		function openChildPartyWindow(methodName){
			var param = '/' + appName + '/paymentSearchPCM.do?method=partysearch';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=580,height=700,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		
		/**
		  * help
		  * This function opens the help screen 
		  * @return none
		  */
		function help(){
			openWindow(buildPrintURL('print','Party Search PCM'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
		}
	</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>
<body>