<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Payment Request Display - SMART-Predict</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

	<script type="text/javascript">
	var screenRoute = "PaymentDisplay";
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');

		
		function openChildMessageOut(methodName){
			var param = '/' + appName + '/paymentDisplayPCM.do?method=messageOut';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=800,height=490,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		
		function openChildMessageList(methodName){
			var param = '/' + appName + '/paymentDisplayPCM.do?method=messageList';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=850,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		
		function openChildStopRulesList(methodName){
			var param = '/' + appName + '/paymentDisplayPCM.do?method=stopRulesList';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=900,height=490,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		
		function openChildLogs(methodName){
			var param = '/' + appName + '/paymentDisplayPCM.do?method=logs';
			var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=800,height=505,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
			return false;
		}
		
		
		function openChildSpreadProfileAdd(methodName){
			var param = '/' + appName + '/spreadProfilesPCM.do?method=' + methodName;
			param += "&screenName=view";
			var mainWindow = openWindow(
							param,
							'spreadProfiles',
							'left=10,top=230,width=900,height=495,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
			return false;
		}
		
		
		
		
	</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>
<body>