<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title> PCM Breakdown Monitor - SMART-Predict
	</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "DashboardDetails";
	/**
	 * help
	 * This function opens the help screen
	 * @return none
	 */
	function help() {
		openWindow(
				buildPrintURL('print', 'Dashboard Details'),
				'sectionprintdwindow',
				'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
				'true')
	}

	function printPage() {
		window.print();
	}
	function paymentDisplay() {
		var param = '/' + appName + '/paymentDisplayPCM.do?method=paymentDisplay';
		var mainWindow = openWindow(
				param,
				'paymentDisplay',
				'left=10,top=230,width=1000,height=800,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}
	function spreadDisplay() {
		var param = '/' + appName + '/dashboardPCM.do?method=spreadDisplay';
		var mainWindow = openWindow(
				param,
				'spreadDisplay',
				'left=10,top=230,width=920,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}

	function report(entityId, entityName, exportType, dateFormat, date,ccyCode, accountGroup, account, status, ccyThreshold, useCcyMultiplier, blockedReason, spreadOnly,fromScreen, userFilter, order, ascDesc, initialFilter,refFilter,archive, rowBegin, rowEnd, sod, confirmedCredit, creditLine, releasedPayments,otherPayments, exCreditLine, inCreditLine, reserve,  pagesToExport, ccyTime, inputSince  ){
		document.getElementById('exportDataForm').action='reportPCM.do?method=dashbordBreakDownReport';
		document.getElementById('exportType').value=exportType;
		document.getElementById('entityId').value=entityId;
		document.getElementById('entityName').value=entityName;
		document.getElementById('valueDate').value=date;
		document.getElementById('dateformat').value=dateFormat;
		document.getElementById('ccyCode').value=ccyCode;
		document.getElementById('accountGroup').value=accountGroup;
		document.getElementById('account').value=account;
		document.getElementById('status').value=status;
		document.getElementById('useThreshold').value=ccyThreshold;
		document.getElementById('useCcyMultiplier').value=useCcyMultiplier;
		document.getElementById('blockedReason').value=blockedReason;
		document.getElementById('useCcyMultiplier').value=useCcyMultiplier;
		document.getElementById('spreadOnly').value=spreadOnly;
		document.getElementById('fromScreen').value=fromScreen;
		document.getElementById('userFilter').value=userFilter;
		document.getElementById('order').value = order;
		document.getElementById('ascDesc').value=ascDesc;
		document.getElementById('initialFilter').value=initialFilter;
		document.getElementById('refFilter').value=refFilter;
		document.getElementById('archive').value=archive;
		document.getElementById('rowBegin').value=rowBegin;
		document.getElementById('rowEnd').value=rowEnd;
		document.getElementById('sod').value=sod;
		document.getElementById('confirmedCredit').value=confirmedCredit;
		document.getElementById('creditLine').value=creditLine;
		document.getElementById('releasedPayments').value=releasedPayments;
		document.getElementById('otherPayments').value=otherPayments;
		document.getElementById('exCreditLine').value=exCreditLine;
		document.getElementById('inCreditLine').value=inCreditLine;
		document.getElementById('reserve').value=reserve;
		document.getElementById('pagesToExport').value=pagesToExport;
		document.getElementById('ccyTime').value=ccyTime;
		document.getElementById('inputSince').value=inputSince;

		document.getElementById('mybutton').click();
	}
</script>
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" method="post" target="tmp">
	<input name="exportType" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="entityName" type="hidden" value="">
	<input name="valueDate" type="hidden" value="">
	<input name="dateformat" type="hidden" value="">
	<input name="useCcyMultiplier" type="hidden" value="">
	<input name="useThreshold" type="hidden" value="">
	<input name="blockedReason" type="hidden" value="">
	<input name="spreadOnly" type="hidden" value="">
	<input name=ccyCode type="hidden" value="">
	<input name="accountGroup" type="hidden" value="">
	<input name="account" type="hidden" value="">
	<input name="status" type="hidden" value="">
	<input name="fromScreen" type="hidden" value="">
	<input name="userFilter" type="hidden" value="">
	<input name="order" type="hidden" value="">
	<input name="ascDesc" type="hidden" value="">
	<input name="initialFilter" type="hidden" value="">
	<input name="refFilter" type="hidden" value="">
	<input name="archive" type="hidden" value="">
	<input name="rowBegin" type="hidden" value="">
	<input name="rowEnd" type="hidden" value="">
	<input name="sod" type="hidden" value="">
	<input name="confirmedCredit" type="hidden" value="">
	<input name="creditLine" type="hidden" value="">
	<input name="releasedPayments" type="hidden" value="">
	<input name="otherPayments" type="hidden" value="">
	<input name="exCreditLine" type="hidden" value="">
	<input name="inCreditLine" type="hidden" value="">
	<input name="reserve" type="hidden" value="">
	<input name="pagesToExport" type="hidden" value="">
	<input name="ccyTime" type="hidden" value="">
	<input name="inputSince" type="hidden" value="">
	<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="breakdownmonitor.title.window"/>" />
	<input name="mybutton" type="submit" value="post" style="visibility: hidden;" />
	<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;" />
</form>
</body>
</html>