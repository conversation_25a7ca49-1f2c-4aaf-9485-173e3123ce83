<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var screenTitle = "";
	screenTitle = getMessage("dashboard.title.window", null);
	document.title = screenTitle;
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "PCMMonitor";
	function openChildWindow(methodName) {
		var param = '/' + appName + '/dashboardPCM.do?method=dashboardAdd';
		var mainWindow = openWindow(
				param,
				'dashboardDetails',
				'left=10,top=230,width=1270,height=780,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}


	function openChildWindowExceptions(url) {
		var param = '/' +  appName + '/' + url;
		var mainWindow = openWindow(
				param,
				'Input Exception - SMART-Predict (PCM)',
				'left=10,top=230,width=1260,height=750,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}

	/**
	 * help
	 * This function opens the help screen
	 * @return none
	 */
	function help() {
		openWindow(
				buildPrintURL('print', 'Dashboard'),
				'sectionprintdwindow',
				'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
				'true')
	}

	function printPage() {
		window.print();
	}

	function report(entityId, entityName, exportType,dateFormat,date,useThreshold,useCcyMultiplier,spreadOnly,value){
		document.getElementById('exportDataForm').action='reportPCM.do?method=dashboardReport';
		document.getElementById('exportType').value=exportType;
		document.getElementById('entityId').value=entityId;
		document.getElementById('entityName').value=entityName;
		document.getElementById('fromDate').value=date;
		document.getElementById('dateformat').value=dateFormat;
		document.getElementById('useThreshold').value=useThreshold;
		document.getElementById('useCcyMultiplier').value=useCcyMultiplier;
		document.getElementById('spreadOnly').value=spreadOnly;
		document.getElementById('valueVolume').value = value;
		document.getElementById('mybutton').click();
	}
</script>
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" method="post" target="tmp">
	<input name="exportType" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="entityName" type="hidden" value="">
	<input name="fromDate" type="hidden" value="">
	<input name="dateformat" type="hidden" value="">
	<input name="useCcyMultiplier" type="hidden" value="">
	<input name="useThreshold" type="hidden" value="">
	<input name="valueVolume" type="hidden" value="">
	<input name="spreadOnly" type="hidden" value="">
	<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="genericdisplaymonitor.title.window"/>" />
	<input name="mybutton" type="submit" value="post" style="visibility: hidden;" />
	<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;" />
</form>
</body>
</html>