<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>PCM Report - SMART-Predict</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>

<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var screenRoute = "pcmReport";
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');


	function report(exportType, reportType, entityId, currencyCode, accountGroup, source, fromDate, toDate, useCcyMultiplier, singleOrRange,
					accountGroupName, ccyCodeName, entityName, sourceName){
		document.getElementById('exportDataForm').action='reportPCM.do?method=report';
		document.getElementById('exportType').value=exportType;
		document.getElementById('reportType').value=reportType;
		document.getElementById('entityId').value=entityId;
		document.getElementById('currencyCode').value=currencyCode;
		document.getElementById('accountGroup').value=accountGroup;
		document.getElementById('source').value=source;
		document.getElementById('fromDate').value=fromDate;
		document.getElementById('toDate').value=toDate;
		document.getElementById('useCcy').value=useCcyMultiplier;
		document.getElementById('singleOrRange').value=singleOrRange;
		document.getElementById('accountGroupName').value=accountGroupName;
		document.getElementById('ccyCodeName').value=ccyCodeName;
		document.getElementById('entityName').value=entityName;
		document.getElementById('sourceName').value=sourceName;
		document.getElementById('mybutton').click();
	}

	/**
	 * help
	 * This function opens the help screen
	 * @return none
	 */
	function help(){
		openWindow(buildPrintURL('print','PCM Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}

</script>
<%@ include file="/angularscripts.jsp"%>
<body>
<form id="exportDataForm" method="post" target="tmp">
	<input name="exportType" type="hidden" value="">
	<input name="reportType" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">
	<input name="accountGroup" type="hidden" value="">
	<input name="source" type="hidden" value="">
	<input name="fromDate" type="hidden" value="">
	<input name="toDate" type="hidden" value="">
	<input name="useCcy" type="hidden" value="">
	<input name="singleOrRange" type="hidden" value="">
	<input name="accountGroupName" type="hidden" value="">
	<input name="ccyCodeName" type="hidden" value="">
	<input name="entityName" type="hidden" value="">
	<input name="sourceName" type="hidden" value="">
	<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="genericdisplaymonitor.title.window"/>" />
	<input name="mybutton" type="submit" value="post" style="visibility: hidden;" />
	<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;"/>
</form>
</body>
</html>