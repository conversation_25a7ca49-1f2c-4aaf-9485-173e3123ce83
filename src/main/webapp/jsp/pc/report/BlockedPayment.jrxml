<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BlockedPayment" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ae778413-a1e6-4d88-9756-825fdbe32bbd">
	<property name="ireport.zoom" value="1.****************"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_selectedCurrency" class="java.lang.String"/>
	<parameter name="p_selectedCurrencyName" class="java.lang.String"/>
	<parameter name="p_selectedAccountGroup" class="java.lang.String"/>
	<parameter name="p_selectedAccountGrpName" class="java.lang.String"/>
	<parameter name="p_selectedEntity" class="java.lang.String"/>
	<parameter name="p_selectedSource" class="java.lang.String"/>
	<parameter name="p_selectedSourceName" class="java.lang.String"/>
	<parameter name="p_selectedEntityName" class="java.lang.String"/>
	<parameter name="p_ReportDateTime" class="java.lang.String"/>
	<parameter name="p_selectedFromDateAsString" class="java.lang.String"/>
	<parameter name="p_selectedToDateAsString" class="java.lang.String"/>
	<parameter name="p_selectedToDate" class="java.util.Date"/>
	<parameter name="p_UseCcyMultiplier" class="java.lang.String"/>
	<parameter name="p_showRemainingCutoff" class="java.lang.String"/>
	<parameter name="p_dateFormat" class="java.lang.String"/>
	<parameter name="swtUtil" class="org.swallow.util.SwtUtil"/>
	<parameter name="SUBREPORT_DIR" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString language="SQL">
		<![CDATA[SELECT pg.acc_grp_id,
         PR.CURRENCY_CODE,
         PR.VALUE_DATE,
         PR.ENTITY_ID,
         PR.SOURCE_ID,
         PR.ACCOUNT_ID,
         NVL(AMOUNT, 0) / DECODE($P{p_UseCcyMultiplier}, 'Y', DECODE (DISPLAY_MULTIPLIER, 'T', 1000, 'M', 1000000, 'B', **********, 1), 1) as AMOUNT,
         PR.SOURCE_REFERENCE,
         PR.PAYREQ_ID,
         PR.MESSAGE_TYPE,
         CCY.DISPLAY_MULTIPLIER,
         PKG_PC_Tools.GetStopBlockReason (PR.PAYREQ_ID, $P{p_dateFormat})  AS REASON
    FROM VW_PC_PAYMENT_REQUEST_ALL pr
         INNER JOIN p_account a
            ON (    pr.host_id = a.host_id
                AND pr.entity_id = a.entity_id
                AND pr.account_id = a.account_id)
         LEFT JOIN pc_account_in_group pg ON a.account_id = pg.account_id
         LEFT JOIN PC_CCY ccy ON pr.CURRENCY_CODE = ccy.CURRENCY_CODE
   WHERE     status in( 'S', 'B')
         AND NVL ( $P{p_selectedCurrency}, 'All') IN ('All', PR.CURRENCY_CODE)
         AND NVL ( $P{p_selectedEntity}, 'All') IN ('All', PR.ENTITY_ID)
         AND NVL ( $P{p_selectedSource}, 'All') IN ('All', PR.SOURCE_ID)
         AND NVL ( $P{p_selectedAccountGroup}, 'All') IN ('All', pg.acc_grp_id)
         AND PR.VALUE_DATE >= NVL ( TO_DATE($P{p_selectedFromDateAsString}, $P{p_dateFormat}) , PR.VALUE_DATE)
         AND PR.VALUE_DATE <= NVL ( TO_DATE($P{p_selectedToDateAsString}, $P{p_dateFormat}), PR.VALUE_DATE)
ORDER BY CCY.ORDINAL,
         pr.currency_code,
         pr.value_date,
         pr.source_id,
         pg.acc_grp_id]]>
	</queryString>
	<field name="ACC_GRP_ID" class="java.lang.String"/>
	<field name="DISPLAY_MULTIPLIER" class="java.lang.String"/>
	<field name="CURRENCY_CODE" class="java.lang.String"/>
	<field name="VALUE_DATE" class="java.sql.Timestamp"/>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ACCOUNT_ID" class="java.lang.String"/>
	<field name="SOURCE_ID" class="java.lang.String"/>
	<field name="AMOUNT" class="java.math.BigDecimal"/>
	<field name="SOURCE_REFERENCE" class="java.lang.String"/>
	<field name="PAYREQ_ID" class="java.math.BigDecimal"/>
	<field name="MESSAGE_TYPE" class="java.lang.String"/>
	<field name="REASON" class="java.lang.String"/>
	<variable name="totalPledge" class="java.math.BigDecimal" resetType="Group" resetGroup="CURRENCY_CODE" calculation="Sum">
		<variableExpression><![CDATA[$F{AMOUNT}]]></variableExpression>
	</variable>
	<variable name="totalPerDate" class="java.math.BigDecimal" resetType="Group" resetGroup="VALUE_DATE" calculation="Sum">
		<variableExpression><![CDATA[$F{AMOUNT}]]></variableExpression>
	</variable>
	<variable name="totalPerSource" class="java.math.BigDecimal" resetType="Group" resetGroup="SOURCE_ID" calculation="Sum">
		<variableExpression><![CDATA[$F{AMOUNT}]]></variableExpression>
	</variable>
	<group name="CURRENCY_CODE">
		<groupExpression><![CDATA[$F{CURRENCY_CODE}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<rectangle>
					<reportElement uuid="f7ab6983-2b61-4048-9d60-cea2397ace1b" mode="Opaque" x="-1" y="0" width="802" height="23" forecolor="#D6E3FE" backcolor="#D6E3FE"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="601" y="0" width="200" height="23"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pgrandTotal")+": " + $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalPledge}) + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="CURRENCY_CODE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="0" y="0" width="219" height="20"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")+": "  + $F{CURRENCY_CODE} + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="VALUE_DATE">
		<groupExpression><![CDATA[$F{VALUE_DATE}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<rectangle>
					<reportElement uuid="d6c7edf4-b70e-4b95-80c5-008b54ccadd0" mode="Opaque" x="0" y="0" width="802" height="23" forecolor="#D6E3FE" backcolor="#D6E3FE"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="VALUE_DATE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="581" y="0" width="221" height="23"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("subTotal")+": " + $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalPerDate}) + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="VALUE_DATE">
					<reportElement uuid="401c7b3b-af73-4d40-8982-9c1692eb7085" x="0" y="0" width="219" height="23"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pValueDate")+": " + new SimpleDateFormat("dd/MM/yyyy").format($F{VALUE_DATE})]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="SOURCE_ID">
		<groupExpression><![CDATA[$F{SOURCE_ID}]]></groupExpression>
		<groupHeader>
			<band height="44">
				<rectangle>
					<reportElement uuid="c497e4c1-496a-40e7-8874-2ecd23b356f4" mode="Opaque" x="0" y="0" width="802" height="23" forecolor="#CCCCCC" backcolor="#CCCCCC"/>
				</rectangle>
				<textField evaluationTime="Group" evaluationGroup="SOURCE_ID">
					<reportElement uuid="ec10af60-d06f-4265-8a50-8d1d35764f8f" x="581" y="0" width="221" height="23"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("subTotal")+": " + $P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$V{totalPerSource}) + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
				<textField evaluationTime="Group" evaluationGroup="SOURCE_ID">
					<reportElement uuid="09314321-d0ee-40eb-9cb2-fd9a14a6687e" x="0" y="0" width="219" height="23"/>
					<textElement verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSource")+": " + $F{SOURCE_ID}]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement uuid="c497e4c1-496a-40e7-8874-2ecd23b356f4" mode="Opaque" x="0" y="21" width="802" height="23" forecolor="#F2F2F2" backcolor="#F2F2F2"/>
				</rectangle>
				<textField>
					<reportElement uuid="6f0091bb-be4c-4b90-be26-8042deb8c6d1" x="-1" y="20" width="103" height="24"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="298d0e02-c250-47e6-91d9-9e5e63320a88" x="102" y="21" width="68" height="22"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="8d9bfc2d-2373-41e0-a2dd-d3e621b43f1f" x="170" y="21" width="103" height="22"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="9b035907-6457-4c58-93ae-5763992f9dc2" x="273" y="20" width="97" height="23"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAmount") + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="34b7c28c-dee8-4126-8b95-386fd1d2aae4" x="370" y="20" width="103" height="23"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSourceRef")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="adb81fce-a42c-4a20-b29f-d5755783b913" x="473" y="21" width="68" height="23"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pPRID")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="b0b9946d-8e4c-45e2-8b1a-e60752b2eb8d" x="541" y="20" width="60" height="23"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pMsgType")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="a40ae1ca-7a02-4218-aaed-65ef57311a09" x="601" y="20" width="170" height="23"/>
					<textElement verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pStopBlockRule")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="ACC_GRP_ID">
		<groupExpression><![CDATA[$F{ACC_GRP_ID}]]></groupExpression>
		<groupFooter>
			<band height="15">
				<rectangle>
					<reportElement uuid="f7ab6983-2b61-4048-9d60-cea2397ace1b" mode="Opaque" x="-1" y="0" width="802" height="15" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				</rectangle>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="85" splitType="Stretch">
			<textField>
				<reportElement uuid="8eb14b75-64f7-47e3-923b-77ddc7103537" mode="Opaque" x="0" y="0" width="801" height="25" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportTitleBlockedPayment")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="85" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrency}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" mode="Opaque" x="2" y="25" width="83" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="2" y="45" width="83" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="85" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGroup}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="185" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrencyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="185" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGrpName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="370" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntity}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="470" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="370" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedSource}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="288" y="45" width="83" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="470" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedSourceName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="288" y="25" width="83" height="20"/>
				<textElement>
					<font isBold="true" isItalic="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSource")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="601" y="45" width="100" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("valueDate")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="601" y="25" width="100" height="20"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportDateTime")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="701" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedFromDateAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="701" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ReportDateTime}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="701" y="65" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedToDate} != null ? $P{p_selectedToDateAsString} : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="288" y="65" width="378" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCuttOffNotReached")]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="30" splitType="Stretch">
			<textField>
				<reportElement uuid="6f0091bb-be4c-4b90-be26-8042deb8c6d1" x="-1" y="5" width="103" height="24"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="298d0e02-c250-47e6-91d9-9e5e63320a88" x="102" y="5" width="83" height="22"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="8d9bfc2d-2373-41e0-a2dd-d3e621b43f1f" x="185" y="5" width="103" height="22"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="9b035907-6457-4c58-93ae-5763992f9dc2" x="288" y="5" width="82" height="23"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAmount") + ("Y".equals($P{p_UseCcyMultiplier})?( $F{DISPLAY_MULTIPLIER} != null? "("+ $F{DISPLAY_MULTIPLIER} +")" :"" ):"")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="34b7c28c-dee8-4126-8b95-386fd1d2aae4" x="370" y="5" width="103" height="23"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSourceRef")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="adb81fce-a42c-4a20-b29f-d5755783b913" x="473" y="5" width="68" height="23"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pPRID")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="b0b9946d-8e4c-45e2-8b1a-e60752b2eb8d" x="541" y="5" width="81" height="23"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pMsgType")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="a40ae1ca-7a02-4218-aaed-65ef57311a09" x="622" y="5" width="149" height="23"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pStopBlockRule")]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="20">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="5b325da6-7c56-4357-8808-911dad16ec53" x="0" y="0" width="102" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ACC_GRP_ID}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="0bc06b28-7b8c-4af9-997a-714d1599def1" x="473" y="0" width="68" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{PAYREQ_ID}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="e5504bb9-c3c0-4135-94c6-7ea935f97cb6" x="273" y="0" width="97" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{AMOUNT})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="5b325da6-7c56-4357-8808-911dad16ec53" x="102" y="0" width="68" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ENTITY_ID}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="5b325da6-7c56-4357-8808-911dad16ec53" x="170" y="1" width="103" height="19">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ACCOUNT_ID}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="e5504bb9-c3c0-4135-94c6-7ea935f97cb6" x="370" y="0" width="103" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{SOURCE_REFERENCE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="0bc06b28-7b8c-4af9-997a-714d1599def1" x="541" y="0" width="60" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement/>
				<textFieldExpression><![CDATA[$F{MESSAGE_TYPE}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="0bc06b28-7b8c-4af9-997a-714d1599def1" x="601" y="0" width="200" height="20">
                    <property name="net.sf.jasperreports.print.keep.full.text" value="true"/>
                    <property name="net.sf.jasperreports.export.xls.wrap.text" value="false"/>
                    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
                </reportElement>
				<textElement markup="none"/>
				<textFieldExpression><![CDATA[$F{REASON}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="150">
			<printWhenExpression><![CDATA[$P{p_showRemainingCutoff}.equals("true")]]></printWhenExpression>
			<rectangle>
				<reportElement uuid="d6c7edf4-b70e-4b95-80c5-008b54ccadd0" mode="Opaque" x="-1" y="0" width="802" height="20" forecolor="#D6E3FE" backcolor="#D6E3FE"/>
			</rectangle>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="0" y="0" width="378" height="20"/>
				<textElement verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pcurrencyNotReachedCutOff")]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement uuid="afb0219d-1f9f-4011-90dc-0bd80f0fb816" x="0" y="20" width="702" height="130"/>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="120" splitType="Stretch">
			<textField>
				<reportElement uuid="8eb14b75-64f7-47e3-923b-77ddc7103537" mode="Opaque" x="0" y="0" width="801" height="25" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportTitleBlockedPayment")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="85" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrency}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="2" y="25" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="2" y="45" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="85" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGroup}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="185" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrencyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="185" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGrpName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="370" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntity}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="470" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="370" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedSource}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="288" y="45" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="470" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedSourceName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="288" y="25" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSource")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="618" y="45" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("valueDate")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="618" y="25" width="83" height="20"/>
				<textElement>
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportDateTime")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="701" y="45" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedFromDateAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="701" y="25" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ReportDateTime}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="701" y="65" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedToDateAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="288" y="65" width="378" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCuttOffNotReached")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="8eb14b75-64f7-47e3-923b-77ddc7103537" mode="Opaque" x="2" y="80" width="801" height="25" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[NO DATA FOUND]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
