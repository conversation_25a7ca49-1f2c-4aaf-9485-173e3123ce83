<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Spread Profiles Maintenance - SMART-Predict</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

	<script type="text/javascript">
		var screenRoute = "spreadProfiles";
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');

		
		function openChildWindow(methodName, screenName) {
			var param = '/' + appName + '/spreadProfilesPCM.do?method=' + methodName;
			param += "&screenName=" + screenName;
			var mainWindow = openWindow(
							param,
							'spreadProfiles',
							'left=10,top=230,width=900,height=495,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
			return false;
		}
		/**
		  * help
		  * This function opens the help screen 
		  * @return none
		  */
		function help(){
			openWindow(buildPrintURL('print','Spread Profiles Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
		}
	</script>
    <%@ include file="/angularscripts.jsp"%>
</body>
</html>