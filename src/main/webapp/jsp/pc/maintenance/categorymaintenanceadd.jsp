<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var screenTitle = "";
screenTitle = getMessage("categoryMaintenanceDetails.title.window", null);
document.title = screenTitle;
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "categoryAdd";
	function openChildWindow(methodName) {
		var param = '/' + appName + '/categoryRulesPCM.do?method=categoryRuleAdd'
		var mainWindow = openWindow(
						param,
						'catgoryRuleAdd',
						'left=10,top=230,width=850,height=450,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}
	
	
	function openSpreadProfile(methodName, screenName) {
		var param = '/' + appName + '/spreadProfilesPCM.do?method=' + methodName;
		param += "&sreenName=" + screenName;
		var mainWindow = openWindow(
						param,
						'spreadProfilesAdd',
						'left=10,top=230,width=900,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}
	
	
</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>