<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>
    <c:choose>
        <c:when test="${'add' == requestScope.screenName}">
            Account Groups Maintenance Add - SMART-Predict
        </c:when>
        <c:when test="${'change' == requestScope.screenName}">
            Account Groups Change - SMART-Predict
        </c:when>
        <c:otherwise>
            Account Groups Maintenance View - SMART-Predict
        </c:otherwise>
    </c:choose>
</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "AccountGroupDetail";


function openExpressionBuilder(){
	var param = '/' + appName + '/expressionBuilderPCM.do?method=expressionBuilder&calledFrom=AccountGroupDetail';
	var 	mainWindow = openWindow (param, 'Expression Builder','left=10,top=230,width=1100,height=550,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}
 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>