<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<request_reply>
    <status_ok>
        <c:out value="${requestScope.reply_status_ok}" />
    </status_ok>
    <message>
        <c:out value="${requestScope.reply_message}" />
    </message>
    <location>
        <c:out value="${requestScope.reply_location}" />
    </location>

    <c:if test="${not empty requestScope.opTimes}">
        <timing>
            <c:forEach items="${requestScope.opTimes}" var="opTime">
                <operation id="${opTime.key}">${opTime.value}</operation>
            </c:forEach>
        </timing>
    </c:if>
</request_reply>