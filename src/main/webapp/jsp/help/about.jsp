<%@page import="org.swallow.util.SwtUtil"%>
<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.PropertiesFileLoader" %>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ include file="/taglib.jsp"%>
<html>
   <head>
      <title><fmt:message key="screen.aboutProject"/></title>
      <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
      <script type="text/javascript" src="../../js/commonJS.js"></script>
      <script type="text/javascript" src="../../js/common.js"></script>
      <link rel="stylesheet" type="text/css" href="../../style/Style.css" >
   </head>

   <script>
      // Check periodically if we should close
      const checkClose = setInterval(() => {
         console.log(localStorage.getItem('shouldCloseAbout'))
         if (localStorage.getItem('shouldCloseAbout') === 'true') {
            window.close();
            clearInterval(checkClose);
         }
      }, 1000);

      // Cleanup when window closes
      window.onunload = () => {
         clearInterval(checkClose);
      };



      function getTitleSuffix() {
		return '<%= titleSuffix %>';
	  }
      function setTitle()
      {
         var titleSuffix = getTitleSuffix();
         document.title = document.title + " " + titleSuffix;
      }
      setTitle();
   </script>

   <body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onunload="call();">
      <div id="copyright" style="position:absolute; left:35px; top:40px; width:300px; height:100px;">
         <b>
         	<%
	         	String copyrightLabel = SwtUtil.getMessage(SwtConstants.COPYRIGHT, request);
	        	   out.print(String.format(copyrightLabel, PropertiesFileLoader.getInstance().getPropertiesVersionValue(SwtConstants.YEARS_COPYRIGHT)));
	         %>
         </b>

         <br><br><br>

         <b><fmt:message key="screen.version"/></b>
         <font color="#0000FF">
            <%=PropertiesFileLoader.getInstance().getPropertiesVersionValue(SwtConstants.VERSION)%>
         </font>

         <br><br>

         <b><fmt:message key="screen.releaseDate"/></b>
         <font color="#0000FF">
            <%=PropertiesFileLoader.getInstance().getPropertiesVersionValue(SwtConstants.RELEASE_DATE)%>
         </font>
   </div>
   </body>

</html>