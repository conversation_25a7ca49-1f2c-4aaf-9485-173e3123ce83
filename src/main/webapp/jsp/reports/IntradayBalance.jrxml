<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="IntradayBalance"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="780"
		 pageHeight="4500"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="pHost_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pReport_Date" isForPrompting="true" class="java.util.Date"/>
	<parameter name="pAccount_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Code" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" isForPrompting="false" class="java.lang.String"/>
	<queryString><![CDATA[SELECT  nvl(sodbal.SOD_Balance,0) AS SOD_Balance,
         decode(interval_start_time,sodbal.interval_end_time,sodbal.interval_end_time||' *',sodbal.interval_end_time) as interval_end_time,
         sodbal.debit_sum, sodbal.credit_sum,nvl(sodbal.Balance,0) as Balance, acn.account_name 
  FROM ( SELECT bal.host_id, bal.entity_id, bal.bal_type_id as account_id, 
                TO_CHAR(sod.interval_end,'HH24:MI') AS interval_end_time,
                TO_CHAR(sod.interval_start,'HH24:MI') AS interval_start_time,
                (NVL(bal.working_external_sod, 0) + NVL(bal.bv_external_adjust, 0)) SOD_Balance,
                sod.external_amount_dr AS debit_sum, sod.external_amount_cr AS credit_sum,
                (NVL(bal.working_external_sod, 0) + NVL(bal.bv_external_adjust, 0)) - SUM (bal) OVER (ORDER BY interval_end) AS Balance
           FROM (SELECT host_id, entity_id, account_id, interval_end, TRUNC(interval_start) intr_dt,interval_start,
                        external_amount_dr, external_amount_cr,
                        (external_amount_dr - external_amount_cr) AS bal,
                        ROW_NUMBER () OVER (ORDER BY interval_end) AS rowno
                   FROM p_intraday_stats
                  WHERE host_id =$P{pHost_Id}
                    AND entity_id = $P{pEntity_Id}
                    AND account_id = $P{pAccount_Id}
                    AND TRUNC (interval_start) = $P{pReport_Date}) sod,
                (SELECT *
                   FROM p_balance 
                  WHERE host_id = $P{pHost_Id}
                    AND entity_id = $P{pEntity_Id}
                    AND balance_date =$P{pReport_Date}
                    AND bal_type_id = $P{pAccount_Id}) bal
          WHERE bal.host_id = sod.host_id(+)
            AND bal.entity_id = sod.entity_id(+)
            AND bal.balance_date = sod.intr_dt(+)
            AND bal.bal_type_id = sod.account_id(+)
       ) sodbal,
       ( SELECT host_id,entity_id,account_id,account_name            
           FROM p_account
          WHERE account_id = $P{pAccount_Id}
       ) acn
  WHERE SODBAL.host_id(+) = acn.host_id
    AND SODBAL.entity_id(+) = acn.entity_id
    AND SODBAL.account_id(+) = acn.account_id]]></queryString>

	<field name="SOD_BALANCE" class="java.math.BigDecimal"/>
	<field name="INTERVAL_END_TIME" class="java.lang.String"/>
	<field name="DEBIT_SUM" class="java.math.BigDecimal"/>
	<field name="CREDIT_SUM" class="java.math.BigDecimal"/>
	<field name="BALANCE" class="java.math.BigDecimal"/>
	<field name="ACCOUNT_NAME" class="java.lang.String"/>

	<variable name="Test" class="java.math.BigDecimal" resetType="Report" calculation="Nothing">
	</variable>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="0"  splitType="Stretch" >
			</band>
		</title>
		
		<pageHeader>
			<band height="206"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="8"
						y="32"
						width="512"
						height="33"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font size="24"/>
					</textElement>
				<text><![CDATA[Intra-Day Balance Fluctuation]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="8"
						y="130"
						width="152"
						height="27"
						key="staticText-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Date]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="8"
						y="65"
						width="152"
						height="33"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Account]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="8"
						y="157"
						width="152"
						height="22"
						key="staticText-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[SOD]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="160"
						y="65"
						width="181"
						height="33"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pAccount_Id}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="dd-MMM-yyyy" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="160"
						y="130"
						width="181"
						height="27"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[$P{pReport_Date}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="160"
						y="157"
						width="181"
						height="22"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{SOD_BALANCE}):
 new DecimalFormat("#,##0.00").format($F{SOD_BALANCE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="8"
						y="179"
						width="152"
						height="22"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[INTERVAL_END_TIME]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="160"
						y="179"
						width="181"
						height="22"
						key="staticText-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[DEBIT_SUM]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="341"
						y="179"
						width="179"
						height="22"
						key="staticText-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[CREDIT_SUM]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="520"
						y="179"
						width="168"
						height="22"
						key="staticText-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[BALANCE]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="341"
						y="65"
						width="179"
						height="33"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ACCOUNT_NAME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="8"
						y="98"
						width="152"
						height="26"
						key="staticText-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Currency]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="160"
						y="98"
						width="181"
						height="26"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="341"
						y="130"
						width="179"
						height="27"
						key="staticText-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="520"
						y="130"
						width="168"
						height="27"
						key="staticText-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font pdfFontName="Helvetica" size="10" isBold="false"/>
					</textElement>
				<text><![CDATA[* 00:00 Early Booking]]></text>
				</staticText>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
			<band height="43"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="8"
						y="19"
						width="152"
						height="19"
						key="textField-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{INTERVAL_END_TIME}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="###0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="160"
						y="19"
						width="181"
						height="19"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{DEBIT_SUM}!=null)?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{DEBIT_SUM}): new DecimalFormat("#,##0.00").format($F{DEBIT_SUM}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="341"
						y="19"
						width="179"
						height="19"
						key="textField-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{CREDIT_SUM}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{CREDIT_SUM}):new DecimalFormat("#,##0.00").format($F{CREDIT_SUM}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="520"
						y="19"
						width="168"
						height="19"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[($F{BALANCE}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{BALANCE}): new DecimalFormat("#,##0.00").format($F{BALANCE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  splitType="Stretch" >
			</band>
		</summary>
</jasperReport>
