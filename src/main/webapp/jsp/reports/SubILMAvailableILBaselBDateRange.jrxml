<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SubReportILMIntraliquitityB" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="0" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1">
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppGC_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN1" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN2" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN3" class="java.util.Date"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDB_LINK" class="java.lang.String"/>
		<parameter name="ppILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
		<parameter name="ppCURRENCY_PATTERN" class="java.lang.String"/>
		<parameter name="ppCCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT  	min1.attribute_id,
						        min1.attribute_name||' ('||DECODE(min1.contribute_total, 'PLUS', '+', 'MINUS', '-', '')||')' ATTRIBUTE_LABEL,
						        min1.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN1 ,
						        min2.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN2 ,
						        min3.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} AS  VALUE_MIN3 ,
						        avg.avg_num_value/$P{ppCCY_MULTIPLIER_VALUE}  AS  AVG_VALUE
					 FROM       TABLE (pkg_ilm_rep.fn_avg_attrs (
					 				$P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN1},
						            $P{ppVALUE_DATE_MIN1},
						            $P{ppROLE_ID})) min1,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						        	$P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN2},
						            $P{ppVALUE_DATE_MIN2},
						            $P{ppROLE_ID})) min2,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						            $P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN3},
						            $P{ppVALUE_DATE_MIN3},
						            $P{ppROLE_ID})) min3,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						            $P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE},
						            $P{ppVALUE_DATE_END},
						            $P{ppROLE_ID})) avg
						WHERE   min1.attribute_id=min2.attribute_id
						        AND min1.attribute_id=min3.attribute_id
						        AND min1.attribute_id=avg.attribute_id]]>
		</queryString>
		<field name="ATTRIBUTE_LABEL" class="java.lang.String"/>
		<field name="VALUE_MIN1" class="java.lang.Double"/>
		<field name="VALUE_MIN2" class="java.lang.Double"/>
		<field name="VALUE_MIN3" class="java.lang.Double"/>
		<field name="AVG_VALUE" class="java.lang.Double"/>
	</subDataset>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE_MIN1" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN2" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN3" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="p_CCY" class="java.lang.String"/>
	<parameter name="p_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_ILM_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_CB_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_GC_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<parameter name="p_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="p_DB_LINK" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT      min1_cor.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN1_SOD_CORR,
				            min1_cor.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN1_COLL_CORR,
				            min1_cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN1_SOD_CB,
				            min1_cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN1_COLL_CB,
				            (min1_gc.AVG_EXTERNAL_SOD - nvl(min1_cb.AVG_EXTERNAL_SOD,0) - min1_cor.AVG_EXTERNAL_SOD)/$P{p_CCY_MULTIPLIER_VALUE} MIN1_BOB,
				            (min1_gc.AVG_COLLATERAL - nvl(min1_cb.AVG_COLLATERAL,0) - min1_cor.AVG_COLLATERAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN1_OSC,
				            min1_cor.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CORR_TOTAL,
				            min1_cor.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CORR_SECURED,
				            min1_cor.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CORR_COMMITTED,
                                            min1_cb.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CB_TOTAL,
                                            (min1_gc.AVG_CREDIT_LINE_TOTAL - min1_cb.AVG_CREDIT_LINE_TOTAL - min1_cor.AVG_CREDIT_LINE_TOTAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN1_OTHER_TOTAL,
				            min1_gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} MIN1_GC_UNENC,
				            min1_gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN1_GC_OTHER,
				            min2_cor.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN2_SOD_CORR,
				            min2_cor.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN2_COLL_CORR,
				            min2_cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN2_SOD_CB,
				            min2_cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN2_COLL_CB,
				            (min2_gc.AVG_EXTERNAL_SOD - nvl(min2_cb.AVG_EXTERNAL_SOD,0) - min2_cor.AVG_EXTERNAL_SOD)/$P{p_CCY_MULTIPLIER_VALUE} MIN2_BOB,
				            (min2_gc.AVG_COLLATERAL - nvl(min2_cb.AVG_COLLATERAL,0) - min2_cor.AVG_COLLATERAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN2_OSC,
				            min2_cor.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CORR_TOTAL,
				            min2_cor.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CORR_SECURED,
				            min2_cor.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CORR_COMMITTED,
                                            min2_cb.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CB_TOTAL,
                                            (min2_gc.AVG_CREDIT_LINE_TOTAL - min2_cb.AVG_CREDIT_LINE_TOTAL - min2_cor.AVG_CREDIT_LINE_TOTAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN2_OTHER_TOTAL,
				            min2_gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} MIN2_GC_UNENC,
				            min2_gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN2_GC_OTHER,
				            min3_cor.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN3_SOD_CORR,
				            min3_cor.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN3_COLL_CORR,
				            min3_cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} MIN3_SOD_CB,
				            min3_cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} MIN3_COLL_CB,
				            (min3_gc.AVG_EXTERNAL_SOD - nvl(min3_cb.AVG_EXTERNAL_SOD,0) - min3_cor.AVG_EXTERNAL_SOD)/$P{p_CCY_MULTIPLIER_VALUE} MIN3_BOB,
				            (min3_gc.AVG_COLLATERAL - nvl(min3_cb.AVG_COLLATERAL,0) - min3_cor.AVG_COLLATERAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN3_OSC,
				            min3_cor.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CORR_TOTAL,
				            min3_cor.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CORR_SECURED,
				            min3_cor.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CORR_COMMITTED,
                                            min3_cb.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CB_TOTAL,
                                            (min3_gc.AVG_CREDIT_LINE_TOTAL - min3_cb.AVG_CREDIT_LINE_TOTAL - min3_cor.AVG_CREDIT_LINE_TOTAL)/$P{p_CCY_MULTIPLIER_VALUE} MIN3_OTHER_TOTAL,
				            min3_gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} MIN3_GC_UNENC,
				            min3_gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} MIN3_GC_OTHER,
				            avg_cor.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AVG_SOD_CORR,
				            avg_cor.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AVG_COLL_CORR,
				            avg_cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AVG_SOD_CB,
				            avg_cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AVG_COLL_CB,
				            (avg_gc.AVG_EXTERNAL_SOD - nvl(avg_cb.AVG_EXTERNAL_SOD,0) - avg_cor.AVG_EXTERNAL_SOD)/$P{p_CCY_MULTIPLIER_VALUE} AVG_BOB,
				            (avg_gc.AVG_COLLATERAL - nvl(avg_cb.AVG_COLLATERAL,0) - avg_cor.AVG_COLLATERAL)/$P{p_CCY_MULTIPLIER_VALUE} AVG_OSC,
				            avg_cor.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AVG_CORR_TOTAL,
				            avg_cor.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AVG_CORR_SECURED,
				            avg_cor.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AVG_CORR_COMMITTED,
                                            avg_cb.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AVG_CB_TOTAL,
                                            (avg_gc.AVG_CREDIT_LINE_TOTAL - avg_cb.AVG_CREDIT_LINE_TOTAL - avg_cor.AVG_CREDIT_LINE_TOTAL)/$P{p_CCY_MULTIPLIER_VALUE} AVG_OTHER_TOTAL,
				            avg_gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AVG_GC_UNENC,
				            avg_gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AVG_GC_OTHER,
							
							min1_cb.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CB_SECURED,
				            min1_cb.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN1_CB_COMMITTED,
				            
				            min2_cb.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CB_SECURED,
				            min2_cb.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN2_CB_COMMITTED,
				            
				            min3_cb.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CB_SECURED,
				            min3_cb.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} MIN3_CB_COMMITTED,
				            
				            avg_cb.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AVG_CB_SECURED,
				            avg_cb.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AVG_CB_COMMITTED,
							
							
							(min1_gc.AVG_CREDIT_LINE_SECURED - nvl(min1_cb.AVG_CREDIT_LINE_SECURED,0) - nvl(min1_cor.AVG_CREDIT_LINE_SECURED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN1_OTHER_SECURED,
							(min1_gc.AVG_CREDIT_LINE_COMMITTED - nvl(min1_cb.AVG_CREDIT_LINE_COMMITTED,0) - nvl(min1_cor.AVG_CREDIT_LINE_COMMITTED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN1_OTHER_COMMITTED, 
							
							(min2_gc.AVG_CREDIT_LINE_SECURED - nvl(min2_cb.AVG_CREDIT_LINE_SECURED,0) - nvl(min2_cor.AVG_CREDIT_LINE_SECURED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN2_OTHER_SECURED,
							(min2_gc.AVG_CREDIT_LINE_COMMITTED - nvl(min2_cb.AVG_CREDIT_LINE_COMMITTED,0) - nvl(min2_cor.AVG_CREDIT_LINE_COMMITTED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN2_OTHER_COMMITTED, 
							
							(min3_gc.AVG_CREDIT_LINE_SECURED - nvl(min3_cb.AVG_CREDIT_LINE_SECURED,0) - nvl(min3_cor.AVG_CREDIT_LINE_SECURED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN3_OTHER_SECURED,
							(min3_gc.AVG_CREDIT_LINE_COMMITTED - nvl(min3_cb.AVG_CREDIT_LINE_COMMITTED,0) - nvl(min3_cor.AVG_CREDIT_LINE_COMMITTED,0))/ $P{p_CCY_MULTIPLIER_VALUE} MIN3_OTHER_COMMITTED, 							
																					
							(avg_gc.AVG_CREDIT_LINE_SECURED - nvl(avg_cb.AVG_CREDIT_LINE_SECURED,0) - nvl(avg_cor.AVG_CREDIT_LINE_SECURED,0))/ $P{p_CCY_MULTIPLIER_VALUE} AVG_OTHER_SECURED,
							(avg_gc.AVG_CREDIT_LINE_COMMITTED - nvl(avg_cb.AVG_CREDIT_LINE_COMMITTED,0) - nvl(avg_cor.AVG_CREDIT_LINE_COMMITTED,0))/ $P{p_CCY_MULTIPLIER_VALUE} AVG_OTHER_COMMITTED
							
							
				 FROM       TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) min1_cb,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) min1_gc,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_ILM_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) min1_cor,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN2}, $P{p_VALUE_DATE_MIN2},$P{P_ROLE_ID})) min2_cb,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN2}, $P{p_VALUE_DATE_MIN2},$P{P_ROLE_ID})) min2_gc,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_ILM_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN2}, $P{p_VALUE_DATE_MIN2},$P{P_ROLE_ID})) min2_cor,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN3}, $P{p_VALUE_DATE_MIN3},$P{P_ROLE_ID})) min3_cb,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN3}, $P{p_VALUE_DATE_MIN3},$P{P_ROLE_ID})) min3_gc,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_ILM_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN3}, $P{p_VALUE_DATE_MIN3},$P{P_ROLE_ID})) min3_cor,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE}, $P{p_VALUE_DATE_END},$P{P_ROLE_ID})) avg_cb,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE}, $P{p_VALUE_DATE_END},$P{P_ROLE_ID})) avg_gc,
				            TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_ILM_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE}, $P{p_VALUE_DATE_END},$P{P_ROLE_ID})) avg_cor]]>
	</queryString>
	<field name="MIN1_SOD_CORR" class="java.lang.Double"/>
	<field name="MIN1_COLL_CORR" class="java.lang.Double"/>
	<field name="MIN1_SOD_CB" class="java.lang.Double"/>
	<field name="MIN1_COLL_CB" class="java.lang.Double"/>
	<field name="MIN1_BOB" class="java.lang.Double"/>
	<field name="MIN1_OSC" class="java.lang.Double"/>
	<field name="MIN1_CORR_TOTAL" class="java.lang.Double"/>
	<field name="MIN1_CORR_COMMITTED" class="java.lang.Double"/>
	<field name="MIN1_CORR_SECURED" class="java.lang.Double"/>
	<field name="MIN1_CB_COMMITTED" class="java.lang.Double"/>
	<field name="MIN1_CB_SECURED" class="java.lang.Double"/>
	<field name="MIN1_CB_TOTAL" class="java.lang.Double"/>
	<field name="MIN1_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN1_GC_UNENC" class="java.lang.Double"/>
	<field name="MIN1_GC_OTHER" class="java.lang.Double"/>
	<field name="MIN1_OTHER_COMMITTED" class="java.lang.Double"/>
	<field name="MIN1_OTHER_SECURED" class="java.lang.Double"/>
	<field name="MIN2_SOD_CORR" class="java.lang.Double"/>
	<field name="MIN2_COLL_CORR" class="java.lang.Double"/>
	<field name="MIN2_SOD_CB" class="java.lang.Double"/>
	<field name="MIN2_COLL_CB" class="java.lang.Double"/>
	<field name="MIN2_BOB" class="java.lang.Double"/>
	<field name="MIN2_OSC" class="java.lang.Double"/>
	<field name="MIN2_CORR_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_CORR_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_CORR_SECURED" class="java.lang.Double"/>
	<field name="MIN2_CB_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_CB_SECURED" class="java.lang.Double"/>
	<field name="MIN2_CB_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_GC_UNENC" class="java.lang.Double"/>
	<field name="MIN2_GC_OTHER" class="java.lang.Double"/>
	<field name="MIN2_OTHER_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_OTHER_SECURED" class="java.lang.Double"/>
	<field name="MIN3_SOD_CORR" class="java.lang.Double"/>
	<field name="MIN3_COLL_CORR" class="java.lang.Double"/>
	<field name="MIN3_SOD_CB" class="java.lang.Double"/>
	<field name="MIN3_COLL_CB" class="java.lang.Double"/>
	<field name="MIN3_BOB" class="java.lang.Double"/>
	<field name="MIN3_OSC" class="java.lang.Double"/>
	<field name="MIN3_CORR_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_CORR_COMMITTED" class="java.lang.Double"/>
	<field name="MIN3_CORR_SECURED" class="java.lang.Double"/>
	<field name="MIN3_CB_COMMITTED" class="java.lang.Double"/>
	<field name="MIN3_CB_SECURED" class="java.lang.Double"/>
	<field name="MIN3_CB_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_GC_UNENC" class="java.lang.Double"/>
	<field name="MIN3_GC_OTHER" class="java.lang.Double"/>
	<field name="MIN3_OTHER_COMMITTED" class="java.lang.Double"/>	
	<field name="MIN3_OTHER_SECURED" class="java.lang.Double"/>
	<field name="AVG_SOD_CORR" class="java.lang.Double"/>
	<field name="AVG_COLL_CORR" class="java.lang.Double"/>
	<field name="AVG_SOD_CB" class="java.lang.Double"/>
	<field name="AVG_COLL_CB" class="java.lang.Double"/>
	<field name="AVG_BOB" class="java.lang.Double"/>
	<field name="AVG_OSC" class="java.lang.Double"/>
	<field name="AVG_CORR_TOTAL" class="java.lang.Double"/>
	<field name="AVG_CORR_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_CORR_SECURED" class="java.lang.Double"/>
	<field name="AVG_CB_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_CB_SECURED" class="java.lang.Double"/>
	<field name="AVG_CB_TOTAL" class="java.lang.Double"/>
	<field name="AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="AVG_GC_UNENC" class="java.lang.Double"/>
	<field name="AVG_GC_OTHER" class="java.lang.Double"/>
	<field name="AVG_OTHER_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_OTHER_SECURED" class="java.lang.Double"/>
	
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="341" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="820" height="126" backcolor="#D9D9D9">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="14" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="14" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCORRESPONDENT_BALANCE")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_SOD_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_SOD_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_SOD_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_SOD_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_SOD_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_SOD_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_SOD_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_SOD_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="30" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCORRESPONDENT_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="30" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_COLL_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_COLL_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="30" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_COLL_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_COLL_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="30" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_COLL_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_COLL_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="30" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_COLL_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_COLL_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="46" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_RESERVES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="46" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_SOD_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_SOD_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="46" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_SOD_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_SOD_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="46" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_SOD_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_SOD_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="46" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_SOD_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_SOD_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="62" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="62" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_COLL_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_COLL_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="62" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_COLL_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_COLL_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="62" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_COLL_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_COLL_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="62" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_COLL_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_COLL_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="78" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pBALANCES_OTHER_BANKS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="78" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="78" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="78" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="78" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="94" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_SYSTEMS_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="94" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="94" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="94" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="94" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="110" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pTOTAL_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="110" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CORR_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CORR_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="110" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CORR_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CORR_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="110" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CORR_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CORR_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="110" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CORR_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CORR_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="126" width="820" height="46" backcolor="#F2F2F2">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="14" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="14" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CORR_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CORR_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CORR_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CORR_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CORR_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CORR_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CORR_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CORR_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="30" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CORR_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CORR_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CORR_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CORR_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CORR_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CORR_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CORR_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CORR_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
            </frame>
				<frame>
                <reportElement positionType="Float" mode="Opaque" x="0" y="172" width="820" height="18" backcolor="#D9D9D9">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<textField>
						<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement>
							<font fontName="SansSerif" size="10" isBold="false"/>
						</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_CREDIT_LINES")]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="318" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CB_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="433" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CB_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CB_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="548" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CB_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CB_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="663" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CB_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CB_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					
			</frame>
					

			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="190" width="820" height="46" backcolor="#F2F2F2">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="14" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="14" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CB_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CB_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CB_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CB_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="30" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CB_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CB_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CB_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CB_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
            </frame>				
									
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="236" width="820" height="52" backcolor="#D9D9D9"/>
					<textField>
						<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement>
							<font fontName="SansSerif" size="10" isBold="false"/>
						</textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_BANK_CREDIT_LINES")]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="318" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="433" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                   	<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="548" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="663" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>

			</frame>
					

			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="260" width="820" height="46" backcolor="#F2F2F2">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="14" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="14" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_OTHER_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_OTHER_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_OTHER_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_OTHER_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_OTHER_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_OTHER_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="14" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OTHER_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OTHER_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="30" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_OTHER_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_OTHER_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_OTHER_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="548" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_OTHER_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_OTHER_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="663" y="30" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OTHER_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OTHER_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
            </frame>				
									
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="305" width="820" height="36" backcolor="#D9D9D9"/>
                <textField>
                    <reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement>
                        <font fontName="SansSerif" size="10" isBold="false"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pUNENCUMBERED_LIQUID_ASSETS")]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="318" y="0" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_UNENC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_UNENC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="433" y="0" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_UNENC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_UNENC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="548" y="0" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_UNENC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_UNENC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="663" y="0" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_UNENC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_UNENC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField" positionType="Float" x="5" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement>
                        <font fontName="SansSerif" size="10" isBold="false"/>
                    </textElement>
                    <textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER")]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="318" y="16" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_OTHER}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_OTHER},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="433" y="16" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_OTHER}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_OTHER},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="548" y="16" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
                    <textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_OTHER}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_OTHER},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
                </textField>
                <textField>
                    <reportElement key="textField-1" x="663" y="16" width="142" height="16">
                        <property name="local_mesure_unitx" value="pixel"/>
                    </reportElement>
                    <box>
                        <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                        <bottomPen lineWidth="0.0" lineColor="#000000"/>
                        <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                    </box>
                    <textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_OTHER}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_OTHER},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
				</frame>
		</band>
		<band height="32">
			<printWhenExpression><![CDATA[$P{p_SERIES_IDENTIFIER}.equals("Standard")]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="820" height="32" backcolor="#F2F2F2">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<componentElement>
					<reportElement positionType="Float" x="20" y="16" width="800" height="16">
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="dataset1">
							 <datasetParameter name="ppHOST_ID">
								<datasetParameterExpression><![CDATA[$P{P_HOST_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppENTITY_ID">
								<datasetParameterExpression><![CDATA[$P{p_ENTITY_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY">
								<datasetParameterExpression><![CDATA[$P{p_CCY}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_END">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_END}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppGC_GROUP_ID">
								<datasetParameterExpression><![CDATA[$P{p_GC_GROUP_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppSERIES_IDENTIFIER">
								<datasetParameterExpression><![CDATA[$P{p_SERIES_IDENTIFIER}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN1">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN1}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN2">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN2}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN3">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN3}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY_MULTIPLIER_VALUE">
								<datasetParameterExpression><![CDATA[$P{p_CCY_MULTIPLIER_VALUE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppDB_LINK">
								<datasetParameterExpression><![CDATA[$P{p_DB_LINK}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_UTIL">
								<datasetParameterExpression><![CDATA[$P{p_ILM_UTIL}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCURRENCY_PATTERN">
								<datasetParameterExpression><![CDATA[$P{p_CURRENCY_PATTERN}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppROLE_ID">
								<datasetParameterExpression><![CDATA[$P{P_ROLE_ID}]]></datasetParameterExpression>
							</datasetParameter>
						</datasetRun>
						<jr:listContents height="16" width="800">
							<textField>
								<reportElement x="0" y="0" width="286" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement>
									<font fontName="SansSerif" size="10" isBold="false"/>
								</textElement>
								<textFieldExpression class="java.lang.String"><![CDATA[$F{ATTRIBUTE_LABEL}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="298" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN1}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN1},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="413" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN2}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN2},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="528" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="local_mesure_unitheight" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN3}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN3},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="643" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
									<property name="local_mesure_unitheight" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="px"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_VALUE}!=null)?($P{ppILM_UTIL}.formatCurrency($F{AVG_VALUE},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
