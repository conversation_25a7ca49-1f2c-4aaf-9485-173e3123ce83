<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.EntityUserAccess"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title>
<fmt:message key="excludedMovements.mainScreen"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT">
var dateSelected = false;
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];
function bodyOnLoad()
{
   var dropBox1 = new SwSelectBox(document.forms[0].elements["reports.entityId"],document.getElementById("entityName"));
   document.forms[0].elements["reports.thresholdType"][0].checked=true;
  <% Collection<EntityUserAccess> userEntityList=(Collection<EntityUserAccess>)request.getAttribute("userEntity"); %>
   getEntityDomesticCurrency();

	$('#selectEntity').on('change', function() {
		submitFormByEnity('getMovementsParameters');
	 });
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["reports.fromDateAsString"];
  return validate(elementsRef);
 }


 var appName = "<%=SwtUtil.appName%>";
 var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;

function checkWeekend(entityId,selectedDate){

	var oXMLHTTP = new XMLHttpRequest();

	var sURL = requestURL + appName+"/reports.do?method=isWeekend";

	sURL = sURL + "&entityId="+entityId+"&selectedDate="+selectedDate;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;

}

function submitForm(){

        var thresholdValue=validateCurrency(document.forms[0].elements['reports.threshold'],'reports.threshold',currencyFormat);
        if(document.forms[0].elements["reports.thresholdTime"].value == "")
    	    document.forms[0].elements["reports.thresholdTime"].value = "00:00:00";

     	document.forms[0].entityId.value = document.forms[0].elements["reports.entityId"].value;
    	document.forms[0].entityText.value = document.getElementById("entityName").innerText;
		if(thresholdValue){
        var oldTarget = document.forms[0].target;
        document.forms[0].method.value = "ExcludedMovementsReport";
        document.forms[0].target = 'tmp';
		if(document.forms[0].elements["reports.threshold"].value == "")
	       document.forms[0].elements["reports.threshold"].value = "0.0";
		if(validateForm(document.forms[0]) ){
		var dateFormat = '${sessionScope.CDM.dateFormat}';
		if(validateDateField(document.forms[0].elements['reports.fromDateAsString'],'date',dateFormat)) {
		var str = checkWeekend(document.forms[0].elements["reports.entityId"].value,document.forms[0].elements["reports.fromDateAsString"].value);
		 if(str!='false'){
			document.forms[0].submit();
			document.forms[0].target = oldTarget;
			setParentChildsFocus();
			}else{
			alert('<fmt:message key = "alert.weekend"/>');
				}
			}
		}
			else
				{
		  		document.forms[0].elements['reports.threshold'].focus();
				}
		 }
}

function submitFormByEnity(methodName,status){

	document.forms[0].method.value = methodName;
	document.forms[0].entityId.value = document.forms[0].elements["reports.entityId"].value;
	document.forms[0].status.value =status;
	document.forms[0].submit();
}
var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var cal = new CalendarPopup("caldiv",false,"calFrame");
  cal.offsetX = -3;
  cal.offsetY = -97;

function validateExclMovmntDateField(){
	var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(document.forms[0].elements['reports.fromDateAsString']!=null && document.forms[0].elements['reports.fromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['reports.fromDateAsString'],'date',dateFormat)){
		}
	}
}
function getEntityDomesticCurrency(){

	var selectElmt = document.getElementById('reports.entityId');
	var selectedValueOption = selectElmt.options[selectElmt.selectedIndex].value;
	<%
	Iterator itr = userEntityList.iterator();
	while (itr.hasNext()) {
		EntityUserAccess entityObj = (EntityUserAccess) itr.next();
		%>
		var entityIdd='<%=entityObj.getEntityId() %>';
		var enityDomestic='<%=entityObj.getDomesticCurrency() %>';
		if (selectedValueOption==entityIdd) {
			 document.forms[0].elements["reports.thresholdType"][1].value=enityDomestic;
			document.getElementById("labelThresholType").innerText="Entity Domestic Currency ["+enityDomestic+"] Equiv";
		}
	<%
	}
	%>
}

</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);"  onunload="call();" >
<form action="excludedMovementsReport.do" method="post">
<input name="method" type="hidden" value="ExcludedMovementsReport">
<input name="applyCurrencyThreshold" type="hidden" >
<input name="applyCurrencyThresholdInd" type="hidden" value="0">
<input name="entityId" type="hidden" value="">
<input name="status" type="hidden" value="">
<input name="entityText" type="hidden" value="">


<div id="ExcludedMovements" style="position:absolute; left:20px; top:20px; width:600px; height:200px; border:2px outset;" color="#7E97AF">
   <div id="ExcludedMovements" style="position:absolute; left:8px; top:4px; width:590px; height:165;">
   <div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>
	  <table width="580px" border="0" cellpadding="0" cellspacing="0" height="104" class="content">
			<tr height="28" style="padding-top:20px;">
				<td  width="120px" style="padding-left: 10px;"><b><fmt:message key="entity.id"/></b></td>
				<td width="5px">&nbsp;</td>
				<td  width="125px" >
					<select id="selectEntity" name="reports.entityId" tabindex="2" titleKey="tooltip.reportEntity" class="htmlTextAlpha" onchange="getEntityDomesticCurrency()" style="width:145px">
						<c:forEach items="${requestScope.userEntity}" var="entity">
							<option value="${entity.entityId}"
								<c:if test="${reports.entityId == entity.entityId}">selected="selected"</c:if>>
								${entity.entityName}
							</option>
						</c:forEach>
					</select>

				</td>
                 <td width="140px" style="float:right;position:absolute;left:390;top:3;">
					<span id="entityName"  name="reports.entityName" class="spantext">
				</td>

			</tr>

			<tr height="28" style="padding-top:10px;padding-bottom:5px;">
			  <td  width="120px" style="padding-left: 10px;"><b><fmt:message key="date"/></b>*</td>
			  <td width="5px">&nbsp;</td>
			  <td width="125px" >
				  <input type="text" tabindex="5" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.fromDateAsString" value="${reports.fromDateAsString}"  style="width:80px; margin-bottom:5px;height:20px"   maxlength="10" onchange="if(validateForm(document.forms[0])){validateExclMovmntDateField();}" onmouseout="dateSelected=false" />
				  <A   title='<fmt:message key="tooltip.calendarreportdate"/>' tabindex="6" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['reports.fromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>

			  </td>
			</tr>

		   <tr height="28">
				<td  width="220px" style="padding-left: 10px;"><b><fmt:message key="reports.thresholdAmount"/></b></td>
			    <td width="5px">&nbsp;</td>
				<td  width="125px">
					<input type="text" cssClass="htmlTextNumeric"  maxlength="28" name="reports.threshold" value="${reports.threshold}"   onchange="return validateCurrency(this,'reports.threshold',currencyFormat)" />
				</td>
			</tr>

			<tr height="28">
			<td  width="250px" style="padding-left: 10px;"><b><fmt:message key="reports.thresholdAmountType"/></b></td>
			    <td width="5px">&nbsp;</td>
				<td  width="455px">
					<input type="radio" name="reports.thresholdType" ${reports.thresholdType == 'ABS' ? 'checked="checked"' : ''} value="${'ABS'}" titleKey="report.amountThresholdTypeAbs" style="width:13;"	cssClass="htmlTextAlpha"   />
					<label tabindex="4" for="1"><fmt:message key="reports.amountThresholdTypeAbs" /> </label>
							 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio" name="reports.thresholdType" ${reports.thresholdType == 'ABS' ? 'checked="checked"' : ''} value="${'ABS'}" titleKey="report.amountThresholdTypeAbs" style="width:13;"	cssClass="htmlTextAlpha"   />
					<label id="labelThresholType" tabindex="5" for="2">  </label>
				</td>
			</tr>
			<tr height="28">
				<td  width="220px" style="padding-left: 10px;"><b><fmt:message key="reports.thresholdTime"/></b></td>
			    <td width="5px">&nbsp;</td>
				<td  width="125px">
					<input type="text" cssClass="htmlTextNumeric"  maxlength="8" name="reports.thresholdTime" value="${reports.thresholdTime}"   value="" onchange="return validateFormatTime(this,'reports.thresholdTime')" />
				</td>
			</tr>
		</table>
	</div>
  </div>

<div id="excludedMovements" style="position:absolute; left:545; top:235; width:70px; height:29px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			<a tabindex="14" href=# onclick="javascript:openWindow(buildPrintURL('print','Excluded Movements'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		    </td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:20; top:225; border:2px outset; width:600px; height:40px; visibility:visible;">
  <div id="excludedMovements" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		  <td title='<fmt:message key="tooltip.submitbutton"/>'><a tabindex="12" width="70" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitForm();"><fmt:message key="button.report"/></a></td>
		  <td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
				<a tabindex="13" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
	</tr>
   </table>
</div>
</div>
<blockquote>&nbsp;</blockquote>
<p>&nbsp;</p>
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>