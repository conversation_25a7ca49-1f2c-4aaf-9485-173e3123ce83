<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="UserReportExcel"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="P_USER" isForPrompting="true" class="java.lang.String"/>
	<parameter name="p_dateFormat" class="java.lang.String"/>
	<queryString><![CDATA[SELECT S_USERS.USER_ID,
       S_USERS.USER_NAME,
       decode(S_USERS.STATUS,1,'Enabled','Disabled') STATUS,
       S_USERS.ROLE_ID,
       S_ROLE.ROLE_NAME,
       S_USERS.CURRENT_ENTITY,
       S_ENTITY.ENTITY_NAME,
       S_USERS.CURRENT_CURRENCY_GROUP_ID CurrencyGroup,
       S_USERS.CURRENT_CURRENCY_GROUP_ID CurrencyGroup,
       nvl((Select CURRENCY_GROUP_NAME from S_CURRENCY_GROUP where  
          CURRENCY_GROUP_ID= S_USERS.CURRENT_CURRENCY_GROUP_ID and  
          ENTITY_ID=S_USERS.CURRENT_ENTITY) ,'All') CURRENCY_GROUP_NAME,
       S_USERS.SECTION_ID,
       (Select SECTION_NAME from S_SECTION where  SECTION_ID=S_USERS.SECTION_ID)  SECTION_NAME,
       pk_application.FN_GET_PARAMETER_VALUE(S_USERS.CURRENT_ENTITY,'LANG',S_USERS.LANG)  LANG,
       S_USERS.PHONE_NUMBER,
       S_USERS.EMAIL_ADDRESS,
       (select TO_CHAR(max(u.LOGON_DT_TIME),$P{p_dateFormat})       
      from S_USER_STATUS u where
      u.host_Id=S_USERS.host_id and u.user_Id=S_USERS.user_id  )Last_Login,
       
       (Select TO_CHAR(update_date,$P{p_dateFormat}) from S_PASSWORD_HISTORY p
       where p.host_Id =S_USERS.host_id and p.user_Id =S_USERS.user_id and 
      p.seq_No = (Select max(p.seq_No) from S_PASSWORD_HISTORY p where
      p.host_Id =S_USERS.host_id and p.user_Id =S_USERS.user_id)) pwd_change_date ,
      
      (select TO_CHAR(max(u.LOGOUT_DT_TIME),$P{p_dateFormat})       
      from S_USER_STATUS u where
      u.host_Id=S_USERS.host_id and u.user_Id=S_USERS.user_id and u.LOGOUT_DT_TIME is not null )Last_Logout
      FROM       S_USERS 
        LEFT OUTER JOIN
           S_ROLE
        ON (S_ROLE.ROLE_ID = S_USERS.ROLE_ID)
     LEFT OUTER JOIN
        S_ENTITY
     ON (S_USERS.CURRENT_ENTITY = S_ENTITY.ENTITY_ID)
WHERE $P{P_USER} IS NULL OR S_USERS.USER_ID = $P{P_USER}
ORDER BY USER_ID]]></queryString>

	<field name="USER_ID" class="java.lang.String"/>
	<field name="USER_NAME" class="java.lang.String"/>
	<field name="STATUS" class="java.lang.String"/>
	<field name="ROLE_ID" class="java.lang.String"/>
	<field name="ROLE_NAME" class="java.lang.String"/>
	<field name="CURRENT_ENTITY" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CURRENCYGROUP" class="java.lang.String"/>
	<field name="CURRENCY_GROUP_NAME" class="java.lang.String"/>
	<field name="SECTION_ID" class="java.lang.String"/>
	<field name="SECTION_NAME" class="java.lang.String"/>
	<field name="LANG" class="java.lang.String"/>
	<field name="PHONE_NUMBER" class="java.lang.String"/>
	<field name="EMAIL_ADDRESS" class="java.lang.String"/>
	<field name="LAST_LOGIN" class="java.lang.String"/>
	<field name="PWD_CHANGE_DATE" class="java.lang.String"/>
	<field name="LAST_LOGOUT" class="java.lang.String"/>

		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="72"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="0"
						y="8"
						width="525"
						height="32"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font pdfFontName="Helvetica" size="24" isBold="false"/>
					</textElement>
				<text><![CDATA[User Report]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="325"
						y="40"
						width="105"
						height="20"
						key="staticText-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Report Date:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="dd-MMM-yyyy" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="430"
						y="40"
						width="95"
						height="20"
						key="textField"/>
					<box></box>				
					<textElement textAlignment="Right" verticalAlignment="Middle">				
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
			</band>
		</title>
		<pageHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
			<band height="207"  splitType="Prevent" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="17"
						width="195"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{USER_NAME}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="17"
						width="130"
						height="18"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Name:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="35"
						width="195"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{STATUS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="35"
						width="130"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Status:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="51"
						width="195"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_ID}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="51"
						width="130"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Role:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="325"
						y="51"
						width="200"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ROLE_NAME}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="67"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{CURRENT_ENTITY}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="67"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Default Entity:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="325"
						y="67"
						width="200"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ENTITY_NAME}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="82"
						width="195"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{CURRENCYGROUP}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="82"
						width="130"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Default Ccy Group:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="113"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box></box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{LANG}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="113"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Language:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="128"
						width="195"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{PHONE_NUMBER}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="128"
						width="130"
						height="17"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Phone:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="145"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{EMAIL_ADDRESS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="145"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Email:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="160"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{LAST_LOGIN}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="160"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Last Login:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="175"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{PWD_CHANGE_DATE}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="175"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Password Change:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="190"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{LAST_LOGOUT}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="190"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Last Logout:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						mode="Opaque"
						x="0"
						y="0"
						width="525"
						height="17"
						forecolor="#FFFFFF"
						backcolor="#000000"
						key="textField"
						positionType="Float"/>
					<box></box>
					<textElement>
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{USER_ID}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="130"
						y="98"
						width="195"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{SECTION_ID}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="98"
						width="130"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-4"
						positionType="Float"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<text><![CDATA[Section:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="325"
						y="82"
						width="200"
						height="16"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{CURRENCY_GROUP_NAME}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="325"
						y="98"
						width="200"
						height="15"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{SECTION_NAME}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  splitType="Stretch" >
			</band>
		</summary>
</jasperReport>
