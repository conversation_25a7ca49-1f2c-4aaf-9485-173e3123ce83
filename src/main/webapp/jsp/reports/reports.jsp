<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>


<html>
<head>
<title>
    <c:choose>
        <c:when test="${'M' == requestScope.methodName}">
            <fmt:message key="reportsmatch.title.window"/>
        </c:when>
        <c:when test="${'T' == requestScope.methodName}">
            <fmt:message key="reportsturnover.title.window"/>
        </c:when>
    </c:choose>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script language="JAVASCRIPT" src="js/datavalidation.js"></script>

 
<SCRIPT language="JAVASCRIPT">

var dateFormat = '${sessionScope.CDM.dateFormat}';

function submitForm(methodName){
if(validateForm(document.forms[0])){   
 document.forms[0].method.value = methodName;
 document.forms[0].submit();
}
	
}
function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["reports.fromDateAsString"];
  elementsRef[1] = objForm.elements["reports.toDateAsString"];
  if(validate(elementsRef))
  {
    if(comparedates(document.forms[0].elements['reports.fromDateAsString'].value,document.forms[0].elements['reports.toDateAsString'].value,dateFormat,'From Date','To Date'))
     {
      return true;
     }    
   }
   return false;
}	
</script> 
<script language="JAVASCRIPT">
 var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';;
    	var cal = new CalendarPopup("caldiv",false,"calFrame"); 
      cal.showNavigationDropdowns();
     // alert("between");
      cal.setCssPrefix("CAL");
      cal.offsetX = 20;
      cal.offsetY = -47;
	  var cal2 = new CalendarPopup("caldiv",false,"calFrame"); 
      cal2.showNavigationDropdowns();
      cal2.setCssPrefix("CAL");
      cal2.offsetX = 20;
      cal2.offsetY = -67;
  </script>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">

<form action="reports.do"  method="post">
<input name="method" type="hidden" value="">
<input type="hidden" name="reports.reportType" value="${reports.reportType}" />
<c:set var="CDM" value="${sessionScope.CDM}" />
<DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
	</iframe>
<!-----------------------------------end of caldiv----------------------------------------------->
<div id="Report Gen" style="position:absolute; left:20px; top:20px; width:385px; height:108px; border:2px outset;" color="#7E97AF">
<div id="Report Gen" style="z-index:99;position:absolute; left:8px; top:4px; width:375px; height:70px;">
<!------------------------------first fieldset------------------------------------------->
<div style="left:8px; top:4px;">
<fieldset style="width:370px;border:2px groove;height:70px;">

<legend>
<fmt:message key="report.repGen"/>
</legend>
<table width="320" border="0" cellpadding="0" cellspacing="0" height="63">      
	 
	 <tr height="25">
	  <td width="50px">&nbsp;<b><fmt:message key="usermaintenance.userId"/></b></td>
	  <td width="20px">&nbsp;</td>
	   <td width="160px">
      
        <select id="reports.id.userId" name="reports.id.userId" style="width:160px" tabindex="1">
			<c:forEach items="${requestScope.userList}" var="user">
				<option value="${user.value}" ${reports.id.userId == user.value ? 'selected' : ''}>${user.label}</option>
			</c:forEach>
		</select>
		    
          </td>
    </tr>
	<tr height="25">
	  <td width="50px">&nbsp;<b><fmt:message key="auditLog.from"/>&nbsp;<fmt:message key="date"/></b>*</td>
	  <td width="20px">&nbsp;</td>
	  <td width="200px">

		  <input type="text" name="reports.fromDateAsString" style="width:80px;" tabindex="2" title="<fmt:message key='tooltip.fromDate'/>" value="${reports.fromDateAsString}" />
 

	
&nbsp;<A name="datelink" ID="datelink" title='<fmt:message key="tooltip.selectFromDate"/>' onClick="cal.select(document.forms[0].elements['reports.fromDateAsString'],'datelink',dateFormatValue); return false;"><img src="images/calendar-16.gif"></A>
	</td>
	</tr>
	<tr height="25px">
	  
 <td width="50px">&nbsp;<b><fmt:message key="auditLog.to"/>&nbsp;<fmt:message key="date"/></b>*</td>
<td width="28px">&nbsp;</td>
<td width="200px">  <input type="text" name="reports.toDateAsString" style="width:80px;" tabindex="3" title="<fmt:message key='tooltip.toDate'/>" value="${reports.toDateAsString}" />

&nbsp;<A name="datelink2" ID="datelink2"  title='<fmt:message key="tooltip.selectToDate"/>' onClick="cal2.select(document.forms[0].elements['reports.toDateAsString'],'datelink2',dateFormatValue); return false;"><img src="images/calendar-16.gif"></A>
</td>
</tr>
	  </table>
  </fieldset>
  </div>
</div>
</div>
<div id="UserStatusView" style="position:absolute; left:8; top:141; width:385; height:15px; visibility:visible;">
	<table width="385" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:135; width:385; height:39px; visibility:visible;">
  <div id="Report Gen" style="position:absolute; left:6; top:4; width:385; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
  <tr>
  <td id="okbutton" width="70"  >	
  <a  title='<fmt:message key="tooltip.ok"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('parameterReport')"><fmt:message key="button.ok"/></a>
  </td>
   <td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>	
  <a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.close"/></a></td>

</body>
</form>


</html>