<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report2" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="d95fca00-a26e-42fa-a536-be941333545e">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#FBFDFF"/>
		</conditionalStyle>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#FBFDFF"/>
		</conditionalStyle>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="thresholdStyle" mode="Opaque" backcolor="#FFFFFF">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD1_COL}.equals( "G" )]]></conditionExpression>
			<style mode="Opaque" backcolor="#00CC00"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD1_COL}.equals( "R" )]]></conditionExpression>
			<style backcolor="#FF3300"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD1_COL}.equals( "W" )]]></conditionExpression>
			<style backcolor="#CCCCCC"/>
		</conditionalStyle>
	</style>
	<style name="threshold2Style" mode="Opaque">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD2_COL}.equals( "G" )]]></conditionExpression>
			<style mode="Opaque" backcolor="#00CC00"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD2_COL}.equals( "R" )]]></conditionExpression>
			<style mode="Opaque" backcolor="#FF3300"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESHOLD2_COL}.equals( "W" )]]></conditionExpression>
			<style mode="Opaque" backcolor="#CCCCCC"/>
		</conditionalStyle>
	</style>
	<subDataset name="New Dataset 2" uuid="d4a2ccc8-00a2-45da-9cfa-148e0f381b8a">
		<parameter name="p_t_Dictionary_Data" class="java.util.HashMap"/>
		<parameter name="p_t_selectedCurrency" class="java.lang.String"/>
		<parameter name="p_t_selectedCurrencyName" class="java.lang.String"/>
		<parameter name="p_t_ApplyCcyThreshold" class="java.lang.String"/>
		<parameter name="p_t_selectedScenario" class="java.lang.String"/>
		<parameter name="p_t_selectedEntity" class="java.lang.String"/>
		<parameter name="p_t_selectedAccountGroup" class="java.lang.String"/>
		<parameter name="p_t_selectedAccountGrpName" class="java.lang.String"/>
		<parameter name="p_t_ApplyCcyThresholdAsString" class="java.lang.String"/>
		<parameter name="p_t_ApplyCcyMultiplierAsString" class="java.lang.String"/>
		<parameter name="p_t_selectedScenarioName" class="java.lang.String"/>
		<parameter name="p_t_SpreadOnlyAsString" class="java.lang.String"/>
		<parameter name="p_t_selectedUserId" class="java.lang.String"/>
		<parameter name="p_t_selectedEntityName" class="java.lang.String"/>
		<parameter name="p_t_ReportDateTime" class="java.lang.String"/>
		<parameter name="p_t_selectedFromDate" class="java.lang.String"/>
		<parameter name="p_t_selectedDateAsString" class="java.lang.String"/>
		<parameter name="p_t_selectedDateISO" class="java.lang.String"/>
		<parameter name="p_t_hostID" class="java.lang.String"/>
		<parameter name="p_t_dbLink" class="java.lang.String"/>
		<parameter name="p_t_roleId" class="java.lang.String"/>
		<parameter name="swtUtil" class="org.swallow.util.SwtUtil"/>
		<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
		<parameter name="p_currencyFormat" class="java.lang.String"/>
		<parameter name="p_t_CalculateAs" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT * from table(pkg_ilm_rep.FN_GET_THRU_RATIO_REPORT($P{p_t_hostID}, $P{p_t_selectedEntity}, $P{p_t_selectedCurrency}, TO_DATE($P{p_t_selectedDateISO}, 'YYYY-MM-DD'), $P{p_t_selectedAccountGroup} , $P{p_t_selectedScenario},$P{p_t_roleId} , $P{p_t_dbLink}, $P{p_t_CalculateAs}))]]>
		</queryString>
		<field name="ENTITY_ID" class="java.lang.String"/>
		<field name="CCY_CODE" class="java.lang.String"/>
		<field name="ILM_GRP_ID" class="java.lang.String"/>
		<field name="SERIES_IDENTIFIER" class="java.lang.String"/>
		<field name="INFLOW_FC" class="java.math.BigDecimal"/>
		<field name="OUTFLOW_FC" class="java.math.BigDecimal"/>
		<field name="ACT_INFLOW" class="java.math.BigDecimal"/>
		<field name="ACT_OUTFLOW" class="java.math.BigDecimal"/>
		<field name="UNSETTL_OUTFLOW" class="java.math.BigDecimal"/>
		<field name="CURRENT_VAL" class="java.lang.String"/>
		<field name="THRESHOLD1" class="java.lang.String"/>
		<field name="THRESHOLD1_COL" class="java.lang.String"/>
		<field name="THRESHOLD2" class="java.lang.String"/>
		<field name="THRESHOLD2_COL" class="java.lang.String"/>
		<field name="GRAPH" class="oracle.jdbc.OracleClob"/>
	</subDataset>
	<parameter name="Test" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap" isForPrompting="false"/>
	<parameter name="p_selectedCurrency" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedCurrencyName" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_ApplyCcyThreshold" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedScenario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedEntity" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedAccountGroup" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedAccountGrpName" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_ApplyCcyThresholdAsString" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_ApplyCcyMultiplierAsString" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedScenarioName" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_SpreadOnlyAsString" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedUserId" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedEntityName" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_ReportDateTime" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedFromDate" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedDateAsString" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_selectedDateISO" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_hostID" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_dbLink" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_roleId" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="net.sf.jasperreports.engine.JasperReport" isForPrompting="false"/>
	<parameter name="SUBREPORT_CHART_GRID_DIR" class="net.sf.jasperreports.engine.JasperReport" isForPrompting="false"/>
	<parameter name="p_ChartsData" class="java.util.HashMap" isForPrompting="false"/>
	<parameter name="chartImageBase64" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_currencyFormat" class="java.lang.String"/>
	<parameter name="p_CalculateAs" class="java.lang.String"/>
	<queryString>
		<![CDATA[Select  1 from dual]]>
	</queryString>
	<field name="1" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="105" splitType="Stretch">
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="83" y="37" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntity}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="1" y="37" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="620" y="57" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("valueDate")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="703" y="37" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_ReportDateTime}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="620" y="37" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportDateTime")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="410" y="37" width="146" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedScenario}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="183" y="37" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="be368d8c-309f-4fa7-b290-dbb3429be748" mode="Opaque" x="1" y="0" width="801" height="26" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pReportTitle")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="703" y="57" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedDateAsString}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="290" y="37" width="120" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pScenario")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="410" y="77" width="266" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGrpName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="1" y="77" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="83" y="77" width="202" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedAccountGroup}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="185" y="57" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrencyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="3" y="57" width="83" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="3ea258d1-3316-484f-afbc-04bde25446e7" x="85" y="57" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_selectedCurrency}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="37b92e0d-a801-44f5-8a47-fcddf7633c35" x="290" y="57" width="120" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pcalculateAs")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="2c3a0aa0-6b0f-4c1b-9479-5348d1134f18" x="410" y="57" width="210" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pcalculateAsValue")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="c84f16be-2f5d-4120-9e46-b18f99f8ffdd" x="290" y="77" width="120" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount_Grp_Desc")]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="113" splitType="Stretch">
			<componentElement>
				<reportElement uuid="eac3ba26-af7b-4064-97b2-1df2a7564a23" key="table 4" style="table 4" mode="Transparent" x="3" y="0" width="796" height="113"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="New Dataset 2" uuid="1671d870-31f7-4ae9-a8d9-4acb5e98b35c">
						<datasetParameter name="p_t_selectedCurrency">
							<datasetParameterExpression><![CDATA[$P{p_selectedCurrency}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_selectedScenario">
							<datasetParameterExpression><![CDATA[$P{p_selectedScenario}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_selectedEntity">
							<datasetParameterExpression><![CDATA[$P{p_selectedEntity}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_selectedAccountGroup">
							<datasetParameterExpression><![CDATA[$P{p_selectedAccountGroup}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_selectedDateISO">
							<datasetParameterExpression><![CDATA[$P{p_selectedDateISO}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_hostID">
							<datasetParameterExpression><![CDATA[$P{p_hostID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_dbLink">
							<datasetParameterExpression><![CDATA[$P{p_dbLink}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_roleId">
							<datasetParameterExpression><![CDATA[$P{p_roleId}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_Dictionary_Data">
							<datasetParameterExpression><![CDATA[$P{p_Dictionary_Data}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_currencyFormat">
							<datasetParameterExpression><![CDATA[$P{p_currencyFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_t_CalculateAs">
							<datasetParameterExpression><![CDATA[$P{p_CalculateAs}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:column uuid="1060e3d2-43ea-4c81-9ec3-2804d26e510e" width="52">
						<jr:columnHeader style="table 4_CH" height="67" rowSpan="2">
							<textField>
								<reportElement uuid="5de6660d-4dc8-461e-8b09-a96544a76c1e" x="0" y="0" width="52" height="67"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_entity")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="4f65818f-8291-44c1-a277-************" x="0" y="0" width="52" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ENTITY_ID}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="81bc95c7-3d72-49b0-a9f6-340f76600c10" width="25">
						<jr:columnHeader style="table 4_CH" height="67" rowSpan="2">
							<textField>
								<reportElement uuid="ec5fab86-5491-4de2-ad6f-51d46a5765cc" x="0" y="0" width="25" height="67"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_ccy")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="b62eb4c3-46f7-4a6d-be1d-2557ab384817" x="0" y="0" width="25" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{CCY_CODE}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="64c3bc0c-f7d8-4945-8b49-40229697c5d1" width="90">
						<jr:columnHeader style="table 4_CH" height="67" rowSpan="2">
							<textField>
								<reportElement uuid="39c00621-21d5-4529-afff-5a00dee44185" x="0" y="0" width="90" height="67"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_ilm_group")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="80f0187a-4b7f-4916-b5d8-ce91b7bd3e31" x="0" y="0" width="90" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ILM_GRP_ID}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:columnGroup uuid="ffe3507a-9c02-4ccb-a58b-59589a7fc940" width="163">
						<jr:columnHeader height="30" rowSpan="1">
							<textField>
								<reportElement uuid="20f7f12d-6814-4350-b2da-2477be451707" style="table 4_CH" x="0" y="0" width="163" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forecasted")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:column uuid="30bff030-877f-410f-a896-c1feb4e128e5" width="82">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="43c5b79c-baf7-49f3-bcfc-b9de22df753b" x="2" y="0" width="80" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcinf")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="102baa54-ca00-4a91-9207-5f32be05c235" x="0" y="0" width="82" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CCY_CODE},$F{INFLOW_FC}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="bf8348bb-088c-421c-a1ab-3023e5f7ce94" width="81">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="6a0d2b1f-c0a8-443d-8a91-ce03dfe3f761" x="0" y="0" width="80" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcout")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="5e60d1b0-3c0f-484f-b3f4-aa2875510ef4" x="0" y="0" width="80" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CCY_CODE},$F{OUTFLOW_FC}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:columnGroup uuid="5fa05505-ae1c-4953-aa57-9c8480b7de11" width="160">
						<jr:columnHeader height="30" rowSpan="1">
							<textField>
								<reportElement uuid="69364e15-1148-43fd-a2bb-eb9b1248323b" style="table 4_CH" x="0" y="0" width="160" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_actuals")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:column uuid="cfc845a7-3b61-45f3-82c9-b6d5084fd731" width="80">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="fe933c2c-a02b-4950-a0d7-9dbc847337a5" x="0" y="0" width="80" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcinf")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="0da1d156-e1ee-42dd-879b-0201ee32489f" x="0" y="0" width="80" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CCY_CODE},$F{ACT_INFLOW}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="62ef5b6c-e2dc-426e-baae-fe0270495f44" width="80">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="2ee72df6-93ab-4858-a974-54d75f82af20" x="0" y="0" width="80" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcout")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="e82f4bd7-5d08-473f-8b7e-358186c4ae40" x="0" y="0" width="80" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CCY_CODE},$F{ACT_OUTFLOW}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:column uuid="53555b11-ec72-4509-8f52-0dc4ce8dabf0" width="72">
						<jr:columnHeader style="table 4_CH" height="67" rowSpan="2">
							<textField>
								<reportElement uuid="caffb9f6-162b-464e-b49b-3e4e35349250" x="0" y="0" width="72" height="67"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_unsetout")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="256c817d-8244-471d-9801-870e66ac828e" x="0" y="0" width="72" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CCY_CODE},$F{UNSETTL_OUTFLOW}, $P{p_currencyFormat})]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:columnGroup uuid="03ba2431-e463-427d-872c-72af7f8764e7" width="254">
						<jr:columnHeader height="30" rowSpan="1">
							<textField>
								<reportElement uuid="33eab539-2959-4baa-9cc8-7e7790e23d87" style="table 4_CH" x="0" y="0" width="254" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_throughputratios")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:column uuid="9377ec91-6ece-41b6-a1b7-8b06a9a998d4" width="81">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="bf6071aa-2e76-4585-bf7b-4eaaaa558ce5" x="0" y="0" width="81" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_threshold1")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement uuid="758561dd-af4b-4e68-8183-bed530042bf6" style="thresholdStyle" x="0" y="0" width="81" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{THRESHOLD1}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="2e82d4d4-d160-49fc-a03b-5e4b33eecfc7" width="83">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="9e2e862c-4589-4a3e-b334-8ee902cc4cb4" x="0" y="0" width="83" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_threshold2")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement uuid="8bcdffc7-0920-4997-b601-e72c20f385c4" style="threshold2Style" x="0" y="0" width="83" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{THRESHOLD2}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="9dd30174-6b58-4446-8483-5135d37fec23" width="90">
							<jr:columnHeader style="table 4_CH" height="37" rowSpan="1">
								<textField>
									<reportElement uuid="5b3fa273-306a-44c5-8c97-4202a0f18146" x="0" y="0" width="90" height="37"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_current")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 4_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="086e7b51-2006-4a4d-8f49-62b79a2bf8b3" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{CURRENT_VAL}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
				</jr:table>
			</componentElement>
			<break>
				<reportElement uuid="dd02d5bc-fb07-4747-a7c4-5eebe68fb799" x="0" y="112" width="99" height="1"/>
			</break>
		</band>
		<band height="210">
			<subreport>
				<reportElement uuid="3217d47a-14de-4eab-9e34-9c63883d2c6d" x="0" y="0" width="799" height="210"/>
				<subreportParameter name="p_ChartsData">
					<subreportParameterExpression><![CDATA[$P{p_ChartsData}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_CHART_GRID_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_CHART_GRID_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedDateISO">
					<subreportParameterExpression><![CDATA[$P{p_selectedDateISO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedDateAsString">
					<subreportParameterExpression><![CDATA[$P{p_selectedDateAsString}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedEntity">
					<subreportParameterExpression><![CDATA[$P{p_selectedEntity}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_dbLink">
					<subreportParameterExpression><![CDATA[$P{p_dbLink}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedScenario">
					<subreportParameterExpression><![CDATA[$P{p_selectedScenario}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedCurrency">
					<subreportParameterExpression><![CDATA[$P{p_selectedCurrency}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_currencyFormat">
					<subreportParameterExpression><![CDATA[$P{p_currencyFormat}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CalculateAs">
					<subreportParameterExpression><![CDATA[$P{p_CalculateAs}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="chartImageBase64">
					<subreportParameterExpression><![CDATA[$P{chartImageBase64}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_Dictionary_Data">
					<subreportParameterExpression><![CDATA[$P{p_Dictionary_Data}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_hostID">
					<subreportParameterExpression><![CDATA[$P{p_hostID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_roleId">
					<subreportParameterExpression><![CDATA[$P{p_roleId}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_selectedAccountGroup">
					<subreportParameterExpression><![CDATA[$P{p_selectedAccountGroup}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
</jasperReport>
