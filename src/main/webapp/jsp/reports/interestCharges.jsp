<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@page import="org.swallow.util.LabelValueBean"%>
<html>
<head>
<title><fmt:message key="interestCharges.mainScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
<script language="JAVASCRIPT">
/* Start:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012: Interest Charges by Account Report: From && To date range instead of Month/Year  */
//To hold the dateFormatValue from commonDatamanager
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
//To hold the dateFormat from commonDatamanager
var dateFormat = '${sessionScope.CDM.dateFormat}';
//To hold the dateSelected flag
var dateSelected = false;
//To hold the return flag
var returnFlag=false;

var configScheduler ="${requestScope.configScheduler}";
var newInterestChargesReportSchedConfig = "${requestScope.newInterestChargesReportSchedConfig}";
var schedulerConfigXML = "${requestScope.schedulerConfigXML}";


mandatoryFieldsArray = ["*"];
function bodyOnLoad()
{
   var dropBox1 = new SwSelectBox(document.forms[0].elements["interestcharges.id.entityId"],document.getElementById("entityName"));
   var dropBox2 = new SwSelectBox(document.forms[0].elements["interestcharges.currencyCode"],document.getElementById("currencyName"));
   var dropBox3 = new SwSelectBox(document.forms[0].elements["interestcharges.accountName"],document.getElementById("account"));
   if (configScheduler != "true"){
	   if(document.forms[0].elements["interestcharges.accountName"].length!=0 && document.forms[0].elements["interestcharges.accountName"].length>0)
			document.getElementById("reportbutton").innerHTML = document.getElementById("reportenablebutton").innerHTML;
		else
			document.getElementById("reportbutton").innerHTML = document.getElementById("reportdisablebutton").innerHTML;
   } else {
	   document.forms[0].newInterestChargesReportSchedConfig.value=newInterestChargesReportSchedConfig;
   }

   document.forms[0].entityText.value=document.getElementById("entityName").innerText;

	if (configScheduler == "true") {
		loadCalendarWithKeyords();
	} else {

		//instantiate the CalendarPopup for Fromdate
		cal = new CalendarPopup("caldiv",true);
		//Set the Fromdate Calendar image position
		cal.offsetX = -7;
		cal.offsetY = -150;
// 		//instantiate the CalendarPopup for Todate
		cal2 = new CalendarPopup("caldiv",true);
		//Set the Todate Calendar image position
		cal2.offsetX = -7;
		cal2.offsetY = -150;
	}

}
//instantiate the CalendarPopup for Fromdate
var cal;
var cal2;


function loadCalendarWithKeyords()
{
	try {


	<%
		ArrayList<LabelValueBean> keyWords = (ArrayList<LabelValueBean>)request.getAttribute("keywords");
		Iterator it = keyWords.iterator();
		while (it.hasNext())
		{
			LabelValueBean lvb = (LabelValueBean) it.next();
	%>
		var newElement = {};
		newElement[headers[1]] = "<%=lvb.getValue()%>";
		newElement[headers[0]] = "<%=lvb.getLabel()%>";
		dataprovider.push(newElement);
	<%
		}

	%>
	cal = new CalendarPopup("caldiv", false, "calFrame");
	cal.offsetX = -7;
	cal.offsetY = -150
	cal.withKeyWord= true;
	cal.comboboxId= 'interrestChargeStartDay';
	cal.inputKeywordName= 'keywordInput';
	cal.keyWordDataProvider = dataprovider;



	cal2 = new CalendarPopup("caldiv", false, "calFrame");
	cal2.offsetX = -7;
	cal2.offsetY = -150;
	cal2.withKeyWord= true;
	cal2.comboboxId= 'interrestChargeEndDay';
	cal2.inputKeywordName= 'keywordInput';
	cal2.keyWordDataProvider = dataprovider;

	} catch (e) {
		console.log('--',e)
	}
}

var headers = ["Keyword", "Description"];


var dataprovider = new Array();


/**
  * compareTwoDates
  *
  * This method is used to compare the from,To date range && if range between two date exceeds more than 90 days,
  * the alert message will be displayed && set focus to From Date
  *
  * @param fromDate
  * @param toDate
  * @param dateFormatValue
  * @return boolean
  */
function compareTwoDates(fromDate,toDate,dateFormatValue){
    //Total time for one day
    var oneDay=1000*60*60*24;
    //split the inputed dates to convert them into standard format for further execution
    var x=fromDate.split("/");
    var y=toDate.split("/");
    //Check date format value as dd/MM/yyyy
	if(dateFormatValue=="<%=SwtConstants.FULL_DATE_FORMAT%>"){
	 	//date format(Fullyear,month,date)
		fromDate=new Date(x[2],(x[1]-1),x[0]);
		toDate=new Date(y[2],(y[1]-1),y[0]);
    }else{
    	//date format(Fullyear,month,date)
		fromDate=new Date(x[2],x[0],(x[1]-1));
		toDate=new Date(y[2],y[0],(y[1]-1));
	}
    //Calculate difference between the two dates, && convert to days
    var dateRange=Math.ceil((toDate.getTime()-fromDate.getTime())/(oneDay));
	if(dateRange>=90){
		alert('<fmt:message key="alert.interestCharges.dateRange"/>');
		//set the focus to from date field
		document.forms[0].elements['interestcharges.fromDate'].focus();
		return false;
	}
	return true;
}

/**
  * buildGetAccountDetails
  *
  * This method is used to submit the form to retrive the interest charges details while clicking on the report button.
  * @param methodName
  */
function buildGetAccountDetails(methodName){
//Set the date flag
dateSelected = false;
//Set returnFlag
returnFlag=false;
 var elementsRef = new Array(2);
 //Assign from,Todate in array reference to validate which is blank || not
  elementsRef[0] = document.forms[0].elements['interestcharges.fromDate'];
  elementsRef[1] = document.forms[0].elements['interestcharges.toDate'];
  	//Validate the From,To date that mandatory fields are blank
	  if(validate(elementsRef))
	  {
		if(validateDateField(document.forms[0].elements['interestcharges.fromDate'],'auditLog.from',dateFormat))
		{
			if(validateDateField(document.forms[0].elements['interestcharges.toDate'],'auditLog.to',dateFormat))
			{
				if(comparedates(document.forms[0].elements['interestcharges.fromDate'].value,document.forms[0].elements['interestcharges.toDate'].value,dateFormat,'From','To'))
				{
					if(compareTwoDates(document.forms[0].elements['interestcharges.fromDate'].value,document.forms[0].elements['interestcharges.toDate'].value,dateFormatValue)){
						returnFlag=true;
						//Set the entityId,entityName,currencyCode,currencyName,accountName,account,fromDate,toDate in form objects
						document.forms[0].method.value = "report";
						document.forms[0].entityId.value = document.forms[0].elements["interestcharges.id.entityId"].value;
						document.forms[0].entityText.value = document.getElementById('entityName').innerText
						document.forms[0].currencyId.value = document.forms[0].elements["interestcharges.currencyCode"].value;
						document.forms[0].currencyText.value =document.getElementById('currencyName').innerText;
						document.forms[0].accountId.value = document.forms[0].elements["interestcharges.accountName"].value;
						document.forms[0].accountText.value = document.getElementById("account").innerText;
						document.forms[0].fromDate.value = document.forms[0].elements['interestcharges.fromDate'].value;
						document.forms[0].toDate.value = document.forms[0].elements['interestcharges.toDate'].value;
						document.forms[0].submit();
						setParentChildsFocus();
					}
				}
			}
		}
	  }
	  return returnFlag;
}
/**
  * storeToDate
  *
  * This method is used to store the ToDate in form elements while clicking on the To date calendar image.
  *
  */
function storeToDate(){
//Assign ToDate in form elements which date will be reassigned to Todate field while selecting Todate is less than Fromdate
document.forms[0].elements['preToDateAsString'].value = document.forms[0].elements['interestcharges.toDate'].value;
}

/**
  * onToDateChange
  *
  * This method is used to validate whether the selected Todate is valid || not && Compares the Todate with FromDate,
  * if Todate is less than From Date then it will return false && set the focus to Todate field
  *
  */
function onToDateChange(){
	if(dateSelected){
		//To hold the previous toDate
		var preToDate = document.forms[0].elements['preToDateAsString'].value;
		//To hold Selected FromDate
		var fromDate=document.forms[0].elements['interestcharges.fromDate'].value;
		//To hold Selected ToDate
		var toDate=document.forms[0].elements['interestcharges.toDate'].value;
		//Set the date flag
		dateSelected = false;
		//Set returnFlag
		returnFlag=false;
		if(fromDate != "" && toDate != "") {
				//Validate the toDate is valid || not
				if(validateField(document.forms[0].elements['interestcharges.toDate'],'auditLog.to',dateFormat)){
					cal2.hideCalendar()
					//Validate the FromDate is valid || not
					if(validateField(document.forms[0].elements['interestcharges.fromDate'],'auditLog.from',dateFormat)){
						//Compare the from date && Todate,if it returns integer as 1,it will show the alert messgage
						var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
						if(compareDate == 1){
							document.forms[0].elements['interestcharges.toDate'].value=preToDate;
							cal2.hideCalendar()
							alert('<fmt:message key="alert.interestCharges.toDate"/>');
							document.forms[0].elements['interestcharges.toDate'].focus();
						}
						returnFlag= true;
					}
				}
			} else {
						//set the focus to from date field
						document.forms[0].elements['interestcharges.fromDate'].focus();
				}
	} return returnFlag;
}
/**
  * storeFromDate
  *
  * This method is used to store the FromDate in form elements while clicking on the From date calendar image.
  *
  */
function storeFromDate(){
//Assign FromDate in form elements which date will be reassigned to FromDate field while selecting FromDate is greater than Todate
document.forms[0].elements['preFromDateAsString'].value = document.forms[0].elements['interestcharges.fromDate'].value;
}

/**
  * onFromDateChange
  *
  * This method is used to validate whether the selected From date is valid || not && Compares the FromDate with Todate,
  * if FromDate is greater than Todate then it will return false && set the focus to From date field
  *
  */
function onFromDateChange(){
	if(dateSelected){
		//To hold the previous FromDate
		var preFromDate = document.forms[0].elements['preFromDateAsString'].value;
		//To hold Selected FromDate
		var fromDate=document.forms[0].elements['interestcharges.fromDate'].value;
		//To hold Selected ToDate
		var toDate=document.forms[0].elements['interestcharges.toDate'].value;
		//Set the date flag
		dateSelected = false;
		//Set the reurn flag
		returnFlag=false;
		if(fromDate != "" && toDate != "") {
				//Validate the FromDate is valid || not
				if(validateField(document.forms[0].elements['interestcharges.fromDate'],'auditLog.from',dateFormat)){
					cal.hideCalendar()
					//Validate the toDate is valid || not
					if(validateField(document.forms[0].elements['interestcharges.toDate'],'auditLog.to',dateFormat)){
						var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
						//Compare the from date && Todate,if it returns integer as 1,it will show the alert messgage
						if(compareDate == 1){
							document.forms[0].elements['interestcharges.fromDate'].value=preFromDate;
							cal.hideCalendar()
							alert('<fmt:message key="alert.interestCharges.fromDate"/>');
							document.forms[0].elements['interestcharges.fromDate'].focus();
						}
							returnFlag= true;
					}
				}
		} else {
			//set the focus to Todate field
			document.forms[0].elements['interestcharges.toDate'].focus();
	}
	}return returnFlag;
}

function submitFormByEnity(methodName,status){
	document.forms[0].method.value = methodName;
	document.forms[0].entityId.value = document.forms[0].elements["interestcharges.id.entityId"].value;
	document.forms[0].currencyId.value = document.forms[0].elements["interestcharges.currencyCode"].value;
	document.forms[0].status.value =status;
	if (configScheduler == "true") {
		document.forms[0].configScheduler.value = configScheduler;
		document.forms[0].newInterestChargesReportSchedConfig.value = true;
		document.forms[0].schedulerConfigXML.value = schedulerConfigXML;
		document.forms[0].jobId.value = '${jobId}';
	}
	document.forms[0].submit();
}


function saveSchedulerReportOptions(){

	if(validateDatesFromReportConfig()){
	 var elementsRef = new Array(2);
	 //Assign from,Todate in array reference to validate which is blank || not
	  elementsRef[0] = document.forms[0].elements['interestcharges.fromDate'];
	  elementsRef[1] = document.forms[0].elements['interestcharges.toDate'];
	  	//Validate the From,To date that mandatory fields are blank
		  if(validate(elementsRef))
		  {
				var schedulerConfigXML;
				var reportSchedulerData = new Object();
				//Set the entityId,entityName,currencyCode,currencyName,accountName,account,fromDate,toDate in reportSchedulerData object
				reportSchedulerData.reportType =  '${reportType}';
				reportSchedulerData.entityId = document.forms[0].elements["interestcharges.id.entityId"].value;
				reportSchedulerData.entityName = document.getElementById('entityName').innerText;
				reportSchedulerData.currencyCode = document.forms[0].elements["interestcharges.currencyCode"].value;
				reportSchedulerData.currencyName =document.getElementById('currencyName').innerText;
				reportSchedulerData.accountId = document.forms[0].elements["interestcharges.accountName"].value;
				reportSchedulerData.account =document.getElementById("account").innerText;
				reportSchedulerData.fromDate = document.forms[0].elements['interestcharges.fromDate'].value;
				reportSchedulerData.toDate = document.forms[0].elements['interestcharges.toDate'].value;
				// setParentChildsFocus();

				reportSchedulerData.jobId = '${jobId}';
				schedulerConfigXML = convertConfigObjectToXML(reportSchedulerData);
				this.opener.document.forms[0].schedulerConfigXML.value = getMenuWindow().encode64(schedulerConfigXML.innerHTML);
				this.opener.updateSchedulerConfigParams("");
				window.close();
		  }
	}
}


function validateDatesFromReportConfig(){
			 	if(document.forms[0].elements['interestcharges.fromDate'].value == ""){
			 		alert('<fmt:message key="alert.enterValidFromDate"/>');
			 	}else if (document.forms[0].elements['interestcharges.toDate'].value == ""){
			 		alert('<fmt:message key="alert.enterValidToDate"/>');
			}else{

				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/' + appName + '/');

				requestURL = requestURL.substring(0, idy + 1);
				requestURL = requestURL + appName
						+ "/ilmReport.do?method=validateDates";
				requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["interestcharges.id.entityId"].value +"&fromDate=" + document.forms[0].elements['interestcharges.fromDate'].value + "&toDate=" + document.forms[0].elements['interestcharges.toDate'].value;

				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open("POST", requestURL, false);
				oXMLHTTP.send();
				var result = new String(oXMLHTTP.responseText);
				if(result == "true") {
					  return true;
				}else {
					alert('<fmt:message key="alertDateShouldNotBeEarlierEhanFromDate"/>');
					  return false;
				}

	  }

}


/**
*
* Used to create XML Data that contain information
* about scheduler report configuration
*
**/
function convertConfigObjectToXML(reportSchedulerData){
	var schedulerConfigXML = document.createElement("div");
	var transactionNode = document.createElement("schedConfig");

	var jobIdNode = document.createElement("jobid");
	jobIdNode.appendChild(document.createTextNode(reportSchedulerData.jobId));

	var reportTypeNode = document.createElement("reporttype");
	reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));

	var entityIdNode = document.createElement("entityid");
	entityIdNode.appendChild(document.createTextNode(reportSchedulerData.entityId));

	var entityNameNode = document.createElement("entityname");
	entityNameNode.appendChild(document.createTextNode(reportSchedulerData.entityName));

	var currencyCodeNode = document.createElement("currencycode");
	currencyCodeNode.appendChild(document.createTextNode(reportSchedulerData.currencyCode));

	var currencyNameNode = document.createElement("currencyName");
	currencyNameNode.appendChild(document.createTextNode(reportSchedulerData.currencyName));

	var accountIdNode = document.createElement("accountId");
	accountIdNode.appendChild(document.createTextNode(reportSchedulerData.accountId));

	var accountNode = document.createElement("account");
	accountNode.appendChild(document.createTextNode(reportSchedulerData.account));

	var fromDateNode = document.createElement("fromDate");
	fromDateNode.appendChild(document.createTextNode(reportSchedulerData.fromDate));

	var toDateNode = document.createElement("toDate");
	toDateNode.appendChild(document.createTextNode(reportSchedulerData.toDate));

	var dateFormatNode = document.createElement("dateformatasstring");
	dateFormatNode.appendChild(document.createTextNode(dateFormatValue));


	transactionNode.appendChild(jobIdNode);
	transactionNode.appendChild(reportTypeNode);
	transactionNode.appendChild(entityIdNode);
	transactionNode.appendChild(entityNameNode);
	transactionNode.appendChild(currencyCodeNode);
	transactionNode.appendChild(currencyNameNode);
	transactionNode.appendChild(accountIdNode);
	transactionNode.appendChild(accountNode);
	transactionNode.appendChild(fromDateNode);
	transactionNode.appendChild(toDateNode);
	transactionNode.appendChild(dateFormatNode);
	schedulerConfigXML.appendChild(transactionNode);
	return schedulerConfigXML;
}


</SCRIPT>

	<style>
#interrestChargeStartDay{
   margin-left: -200px;
    margin-top: -150px;
}
#interrestChargeEndDay{
   margin-left: -200px;
    margin-top: -150px;
}
input[name ="keywordInput"] {
	width: 120px
}

</style>

</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);"
	onunload="call();">
<form action="interestCharges.do" method="post">
	<input name="method" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="entityText" type="hidden" value="">
	<input name="currencyId" type="hidden" value="">
	<input name="currencyText" type="hidden" value="">
	<input name="accountId" type="hidden" value="">
	<input name="accountText" type="hidden" value="">
	<input name="fromDate" type="hidden" value="">
	<input name="toDate" type="hidden" value="">
	<input name="status" type="hidden" value="">
	<input name="preFromDateAsString" type="hidden" value="">
	<input name="preToDateAsString" type="hidden" value="">
	<input name="configScheduler" type="hidden" value="">
    <input name="reportType" type="hidden" value="">
    <input name="jobId" type="hidden" value="">
    <input name="newInterestChargesReportSchedConfig" type="hidden" value="">
    <input name="schedulerConfigXML" type="hidden" value="">

	<div id="InterestCharges" style="position: absolute; left: 20px; top: 20px; width: 660px; height: 140px; border: 2px outset;" color="#7E97AF">
	<div id="InterestCharges" style="position: absolute; left: 8px; top: 8px; width: 660px; height: 130;">
	<div id="caldiv" style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position: absolute; top: 0px; left: 0px; display: none;"></iframe>
	<table width="660px" border="0" cellpadding="0" cellspacing="0" height="120" class="content">		<tr height="13">
			<td width="70px"><b><fmt:message key="interestCharges.entity"/></b></td>

			<td width="200px">
					<select id="interestcharges.id.entityId" name="interestcharges.id.entityId" class="htmlTextAlpha"
						  titleKey="tooltip.selectEntityid" onchange="submitFormByEnity('displayList','entity')" style="width:120px" tabindex="1">
					  <c:forEach items="${requestScope.entities}" var="item">
						  <option value="${item.value}" <c:if test="${interestcharges.id.entityId == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
					</select>
			</td>
			<td width="320px">
				<span id="entityName" name="entityName"	class="spantext">
			</td>

		</tr>

		<tr height="13">
			<td width="70px"><b><fmt:message key="interestCharges.currency"/></b></td>

			<td width="200px">
					<select id="interestcharges.currencyCode" name="interestcharges.currencyCode" class="htmlTextAlpha"
						  titleKey="tooltip.selectCurr" style="width:120px" titleKey="tooltip.selectCurrencyId"	tabindex="2" onchange="submitFormByEnity('displayList','currency')">
					  <c:forEach items="${requestScope.currencies}" var="item">
						  <option value="${item.value}" <c:if test="${interestcharges.currencyCode == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
					</select>
			</td>
			<td width="320px">
				<span id="currencyName" name="currencyName"	class="spantext">
			</td>

		</tr>
		<tr height="13">
			<td width="70px"><b><fmt:message key="interestCharges.account"/></b></td>

			<td width="200px">
					<select id="interestcharges.accountName" name="interestcharges.accountName" class="htmlTextAlpha"
						  style="width:245px" titleKey="tooltip.selectAccountId" tabindex="3">
					  <c:forEach items="${requestScope.accountdetails}" var="item">
						  <option value="${item.value}" <c:if test="${interestcharges.accountName == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
					</select>
			</td>
			<td width="320px">
				<span id="account" name="account" class="spantext">
			</td>

		</tr>
		<tr height="13">
			  <td  width="70px"><b><fmt:message key="auditLog.from"/></b>*</td>
			  <td width="200px" >
					<input type="text" tabindex="4" title="tooltip.fromDate" cssClass="htmlTextNumeric" name="interestcharges.fromDate" value="${interestcharges.fromDate}"  style="width:79px;margin-bottom: 5px;margin-top: 2px;height:21;"   maxlength="10" onblur="onFromDateChange();" onmouseout="dateSelected=false" />
					<A   title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="5" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['interestcharges.fromDate'],'datelink',dateFormatValue);storeFromDate();dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
			  </td>
			  <td  width="320px"><span style="width: 30px; height: 20px;"><b><fmt:message key="auditLog.to"/></b>*</span>&nbsp;&nbsp;
					<input type="text" tabindex="6" titleKey="tooltip.toDate" cssClass="htmlTextNumeric" name="interestcharges.toDate" value="${interestcharges.toDate}"  style="width:79px;margin-bottom: 5px;margin-top: 2px;height:21;"   maxlength="10" onblur="onToDateChange();" onmouseout="dateSelected=false" />
					<A   title='<fmt:message key="tooltip.selectToDate"/>' tabindex="7" name="datelink2" ID="datelink2" onClick="cal2.select(document.forms[0].elements['interestcharges.toDate'],'datelink2',dateFormatValue);storeToDate();dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>
			  </td>
			</tr>
	</table>
	</div>
	</div>
	</div>

	<div id="InterestCharges"
		style="position: absolute; left: 600; top: 175; width: 70px; height: 29px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right">
				<a tabindex="10" href=# onclick="javascript:openWindow(buildPrintURL('print','Interest Charges per Account Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
					<img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
				</a>
			</td>

		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF" style="position: absolute; left: 20; top: 165; border: 2px outset; width: 660px; height: 40px; visibility: visible;">
	<div id="InterestCharges" style="position: absolute; left: 6; top: 5; width: 425; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<c:if test="${'true' == requestScope.configScheduler}">

				<td><a title='<fmt:message key="tooltip.save"/>' width="70" onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"  onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
								onclick="saveSchedulerReportOptions('interestChargesReport');"><fmt:message key="button.save"/></a></td>

</c:if>
			<c:if test="${'true' != requestScope.configScheduler}">

				<td id="reportbutton" width="70"></td>

</c:if>
			<td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
				<a tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');">
					<fmt:message key="button.cancel"/>
				</a>
		</tr>
	</table>
	</div>
	</div>


	<div id="InterestCharges"
		style="position: absolute; left: 6; top: 4; width: 425; height: 15px; visibility: hidden;">
	<table width="200" border="0" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="reportenablebutton"><a tabindex="8"	title='<fmt:message key="tooltip.reportButton"/>' onMouseOut="collapsebutton(this)"	onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
				onClick="javascript:buildGetAccountDetails('showAccountDetails');"><fmt:message key="interestCharges.report"/></a></td>
			<td id="reportdisablebutton"><a class="disabled" disabled="disabled"><fmt:message key="interestCharges.report"/></a></td>


		</tr>
	</table>
	</div>

<!-- End:Code Modified For Mantis 1622 by Sudhakar on 03-09-2012:Interest Charges by Account Report: From && To date range instead of Month/Year -->

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
<DIV ID="caldiv"
	STYLE="position: absolute; visibility: hidden; background-color: white; layer-background-color: white;"></DIV>
<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position: absolute; top: 0px; left: 0px; display: none;"></iframe>
</body>
</html>