<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version last-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ExcludedMovements" pageWidth="670" pageHeight="842" columnWidth="502" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="de04f64a-3131-47df-bb16-1bc9be1a2a30">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="RABO_1055"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHOST" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pENTITY" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pREPORT_DATE" class="java.util.Date"/>
	<parameter name="pTHRESHOLD" class="java.lang.Double"/>
	<parameter name="pTHRESHOLD_TYPE" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pTHRESHOLD_TIME" class="java.lang.String"/>
	<parameter name="pENTITY_NAME" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pCurrencyPattern" class="java.lang.String" isForPrompting="false"/>
	<queryString>
		<![CDATA[SELECT SUBQ.HOST_ID,
       SUBQ.ENTITY_ID,
       SUBQ.CURRENCY_CODE CCY,
       SUBQ.ACCOUNT_ID, --account id (main nostro)
       SUBQ.ACCOUNT_NAME,
       SUBQ.EQUIV_THRSH,
       SUBQ.TIMECAT,
       SUBQ.TOT_COUNT TOT_MVMT, -- count of all pos level 8 movements in the account
       SUBQ.COUNT_EXCL_OS, -- count of excluded outstanding pos level 8 movements in the account
       ROUND ( (SUBQ.COUNT_EXCL_OS / SUBQ.TOT_COUNT) * 100, 2) PCT_COUNT_EXCL_OS, -- percent (by count) of excluded outstanding movements compared with total pos 8 moves in the account
       SUBQ.COUNT_EXCL_OFF, -- count of excluded offered pos level 8 movements in the account
       ROUND ( (SUBQ.COUNT_EXCL_OFF / SUBQ.TOT_COUNT) * 100, 2) PCT_COUNT_EXCL_OFF -- percent (by count) of excluded offered movements compared with total pos 8 moves in the account
  FROM (WITH ENTACC
             AS (SELECT $P{pREPORT_DATE} AS REPORT_DATE,
                        $P{pTHRESHOLD_TIME} AS PTIME,
                        $P{pTHRESHOLD} AS PTHRESH,
                        $P{pTHRESHOLD_TYPE} AS PTHRESHOLD_TYPE,
                        A.HOST_ID,
                        A.ENTITY_ID,
                        A.ACCOUNT_ID,
                        A.ACCOUNT_NAME,
                        NVL (
                           (SELECT S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE
                              FROM S_CURRENCY_EXCHANGE_RATE
                             WHERE S_CURRENCY_EXCHANGE_RATE.HOST_ID = A.HOST_ID
                               AND S_CURRENCY_EXCHANGE_RATE.ENTITY_ID = A.ENTITY_ID
                               AND S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE =
                                      A.CURRENCY_CODE
                               AND S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE =
                                      (SELECT MAX (S.EXCHANGE_RATE_DATE)
                                         FROM S_CURRENCY_EXCHANGE_RATE S
                                        WHERE S.HOST_ID = A.HOST_ID
                                          AND S.ENTITY_ID = A.ENTITY_ID
                                          AND S.CURRENCY_CODE = A.CURRENCY_CODE
                                          AND S.EXCHANGE_RATE_DATE <= (PKG_NON_WORKDAY.GETPREVBUSINESSDATE (
                                                                          'OPPORTUNITY_COST_REPORT',
                                                                          TO_DATE (
                                                                             $P{pREPORT_DATE}),
                                                                          A.ENTITY_ID,
                                                                          A.ACCOUNT_ID,
                                                                          A.CURRENCY_CODE,
                                                                          1)))),
                           1)
                           EXRATE,
                        E.EXCHANGE_RATE_FORMAT,
                        E.BALANCE_EXT_POSITION
                   FROM P_ACCOUNT A
                        INNER JOIN S_ENTITY E
                           ON (A.HOST_ID = E.HOST_ID
                           AND A.ENTITY_ID = E.ENTITY_ID)
                  WHERE A.ACCOUNT_LEVEL = 'M'
                    AND A.ACCOUNT_CLASS = 'N')
          SELECT M.HOST_ID,
                 M.ENTITY_ID,
                 M.CURRENCY_CODE,
                 M.ACCOUNT_ID,
                 ENTACC.ACCOUNT_NAME,
                 DECODE (
                    ENTACC.PTHRESHOLD_TYPE,
                    'ABS', ENTACC.PTHRESH,
                    (  ENTACC.PTHRESH
                     * DECODE (ENTACC.EXCHANGE_RATE_FORMAT,
                               2, ENTACC.EXRATE,
                               1 / ENTACC.EXRATE)))
                    EQUIV_THRSH,
                 CASE
                    WHEN GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT (M.INPUT_DATE, M.ENTITY_ID) >=
                            (  TRUNC (ENTACC.REPORT_DATE)
                             + (  TO_NUMBER (
                                     TO_CHAR (
                                        TO_DATE (NVL (ENTACC.PTIME, '00:00:00'), 'HH24:MI:SS'),
                                        'SSSSS'))
                                / 86400))
                    THEN
                       '>=' || ENTACC.PTIME
                    ELSE
                       '<' || ENTACC.PTIME
                 END
                    TIMECAT,
                 COUNT (*) TOT_COUNT,
                 SUM (CASE
                         WHEN (M.PREDICT_STATUS = 'E'
                           AND M.MATCH_STATUS = 'L'
                           AND M.POSITION_LEVEL = ENTACC.BALANCE_EXT_POSITION)
                         THEN
                            1
                         ELSE
                            0
                      END)
                    COUNT_EXCL_OS,
                 SUM (CASE
                         WHEN (M.PREDICT_STATUS = 'E'
                           AND M.MATCH_STATUS = 'M'
                           AND M.POSITION_LEVEL = ENTACC.BALANCE_EXT_POSITION)
                         THEN
                            1
                         ELSE
                            0
                      END)
                    COUNT_EXCL_OFF
            FROM P_MOVEMENT M
                 INNER JOIN P_ACCOUNT A
                    ON (A.HOST_ID = M.HOST_ID
                    AND A.ENTITY_ID = M.ENTITY_ID
                    AND A.ACCOUNT_ID = M.ACCOUNT_ID)
                 INNER JOIN P_POSITION_LEVEL_NAME P
                    ON (P.HOST_ID = M.HOST_ID
                    AND P.ENTITY_ID = M.ENTITY_ID
                    AND P.POSITION_LEVEL = M.POSITION_LEVEL)
                 INNER JOIN ENTACC
                    ON (ENTACC.HOST_ID = M.HOST_ID
                    AND ENTACC.ENTITY_ID = M.ENTITY_ID
                    AND ENTACC.ACCOUNT_ID = M.ACCOUNT_ID)
           WHERE M.HOST_ID = $P{pHOST}
             AND M.ENTITY_ID = $P{pENTITY}
             AND ($P{pTHRESHOLD} = 0
               OR M.AMOUNT >=
                     DECODE (
                        ENTACC.PTHRESHOLD_TYPE,
                        'ABS', ENTACC.PTHRESH,
                        (  ENTACC.PTHRESH
                         * DECODE (ENTACC.EXCHANGE_RATE_FORMAT,
                                   2, ENTACC.EXRATE,
                                   1 / ENTACC.EXRATE))))
             AND M.VALUE_DATE = $P{pREPORT_DATE}
             AND M.PREDICT_STATUS != 'C'
             AND M.MATCH_STATUS IN ('L', 'M', 'S', 'C', 'E')
             AND M.POSITION_LEVEL = ENTACC.BALANCE_EXT_POSITION
             AND A.ACCOUNT_LEVEL = 'M'
             AND A.ACCOUNT_CLASS = 'N'
        GROUP BY M.HOST_ID,
                 M.ENTITY_ID,
                 M.CURRENCY_CODE,
                 M.ACCOUNT_ID,
                 ENTACC.ACCOUNT_NAME,
                 
                 DECODE (
                    ENTACC.PTHRESHOLD_TYPE,
                    'ABS', ENTACC.PTHRESH,
                    (  ENTACC.PTHRESH
                     * DECODE (ENTACC.EXCHANGE_RATE_FORMAT,
                               2, ENTACC.EXRATE,
                               1 / ENTACC.EXRATE))),
                 
                 CASE
                    WHEN GLOBAL_VAR.FN_GET_OFFSET_DATE_ENT (M.INPUT_DATE, M.ENTITY_ID) >=
                            (  TRUNC (ENTACC.REPORT_DATE)
                             + (  TO_NUMBER (
                                     TO_CHAR (
                                        TO_DATE (NVL (ENTACC.PTIME, '00:00:00'), 'HH24:MI:SS'),
                                        'SSSSS'))
                                / 86400))
                    THEN
                       '>=' || ENTACC.PTIME
                    ELSE
                       '<' || ENTACC.PTIME
                 END
        ORDER BY M.HOST_ID,
                 M.ENTITY_ID,
                 M.CURRENCY_CODE,
                 M.ACCOUNT_ID) SUBQ]]>
	</queryString>
	<field name="CCY" class="java.lang.String"/>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ACCOUNT_ID" class="java.lang.String"/>
	<field name="ACCOUNT_NAME" class="java.lang.String"/>
	<field name="TIMECAT" class="java.lang.String"/>
	<field name="EQUIV_THRSH" class="java.lang.Double"/>
	<field name="TOT_MVMT" class="java.lang.Double"/>
	<field name="COUNT_EXCL_OS" class="java.lang.Double"/>
	<field name="COUNT_EXCL_OFF" class="java.lang.Double"/>
	<field name="PCT_COUNT_EXCL_OS" class="java.lang.Double"/>
	<field name="PCT_COUNT_EXCL_OFF" class="java.lang.Double"/>
	<variable name="Total_Mvt" class="java.lang.Double" resetType="Group" resetGroup="ACCOUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{TOT_MVMT} != null ? $F{TOT_MVMT} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="Total_Excl_OFF" class="java.lang.Double" resetType="Group" resetGroup="ACCOUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{COUNT_EXCL_OFF} != null ? $F{COUNT_EXCL_OFF} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="Total_Excl_OS" class="java.lang.Double" resetType="Group" resetGroup="ACCOUNT" calculation="Sum">
		<variableExpression><![CDATA[$F{COUNT_EXCL_OS} != null ? $F{COUNT_EXCL_OS} : new BigDecimal(0)]]></variableExpression>
	</variable>
	<variable name="Total_PCT_Excl_OFF" class="java.lang.Double" resetType="Group" resetGroup="ACCOUNT">
		<variableExpression><![CDATA[$V{Total_Excl_OFF} != null ? (($V{Total_Excl_OFF} )/ $V{Total_Mvt}) : new java.lang.Double(0)]]></variableExpression>
	</variable>
	<variable name="Total_PCT_Excl_OS" class="java.lang.Double" resetType="Group" resetGroup="ACCOUNT">
		<variableExpression><![CDATA[$V{Total_Excl_OS} != null ? (($V{Total_Excl_OS} ) / $V{Total_Mvt}): new java.lang.Double(0)]]></variableExpression>
	</variable>
	<group name="CURRENCY" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{CCY}]]></groupExpression>
		<groupHeader>
			<band height="35" splitType="Stretch">
				<staticText>
					<reportElement uuid="8afa173c-657d-4e19-9319-8d0e43cf8743" key="staticText-16" x="394" y="12" width="60" height="23">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Threshold]]></text>
				</staticText>
				<textField pattern="###0.0" isBlankWhenNull="true">
					<reportElement uuid="6f9e7517-522d-48d3-9f32-b2d2c0705de3" key="textField-9" x="459" y="12" width="130" height="16">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{EQUIV_THRSH}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement uuid="8afa173c-657d-4e19-9319-8d0e43cf8743" key="staticText-16" x="44" y="12" width="61" height="23">
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="helvetica-bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[Currency]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement uuid="6f9e7517-522d-48d3-9f32-b2d2c0705de3" key="textField-9" x="104" y="12" width="47" height="18">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="12"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CCY}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="ACCOUNT">
		<groupExpression><![CDATA[$F{ACCOUNT_ID}]]></groupExpression>
		<groupHeader>
			<band height="40" splitType="Stretch">
				<staticText>
					<reportElement uuid="e4d24baf-594f-4f6a-83b4-1d37d855e039" key="staticText-1" x="196" y="21" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold" pdfEncoding=""/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[All Movements]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="e4d24baf-594f-4f6a-83b4-1d37d855e039" key="staticText-1" x="328" y="21" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[Excluded Offered]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="e4d24baf-594f-4f6a-83b4-1d37d855e039" key="staticText-1" x="476" y="21" width="80" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[Excluded O/S]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="e4d24baf-594f-4f6a-83b4-1d37d855e039" key="staticText-1" x="74" y="21" width="76" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[Input Time]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="8afa173c-657d-4e19-9319-8d0e43cf8743" key="staticText-16" x="78" y="1" width="73" height="23">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[Account]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement uuid="bc7f5357-dc24-408f-9b0b-66207311c4f1" key="textField" x="170" y="3" width="405" height="16">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitheight" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) && ($F{ACCOUNT_NAME}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement/>
					<textFieldExpression><![CDATA[$F{ACCOUNT_ID}+"  "+$F{ACCOUNT_NAME}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="25" splitType="Stretch">
				<staticText>
					<reportElement uuid="8afa173c-657d-4e19-9319-8d0e43cf8743" key="staticText-16" x="130" y="1" width="20" height="18">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top">
						<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<text><![CDATA[All]]></text>
				</staticText>
				<textField pattern="###" isBlankWhenNull="true">
					<reportElement uuid="bc7f5357-dc24-408f-9b0b-66207311c4f1" key="staticText-16" x="200" y="1" width="80" height="18">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Total_Mvt}]]></textFieldExpression>
				</textField>
				<textField pattern="###" isBlankWhenNull="true">
					<reportElement uuid="bc7f5357-dc24-408f-9b0b-66207311c4f1" key="staticText-16" x="317" y="1" width="70" height="18">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Total_Excl_OFF}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.0 %" isBlankWhenNull="true">
					<reportElement uuid="bc7f5357-dc24-408f-9b0b-66207311c4f1" key="textField" x="386" y="1" width="41" height="18">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Total_PCT_Excl_OFF}]]></textFieldExpression>
				</textField>
				<textField pattern="###" isBlankWhenNull="true">
					<reportElement uuid="bc7f5357-dc24-408f-9b0b-66207311c4f1" key="staticText-16" x="460" y="1" width="49" height="18">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Total_Excl_OS}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.0 %" isBlankWhenNull="true">
					<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="512" y="1" width="41" height="18">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{Total_PCT_Excl_OS}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement uuid="c91ef1fa-62e2-42e3-aec0-b2447a81d719" key="staticText-19" x="200" y="7" width="167" height="18">
						<property name="local_mesure_unity" value="pixel"/>
						<printWhenExpression><![CDATA[($F{CCY}==null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="12"/>
					</textElement>
					<text><![CDATA[No records to display]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="36" splitType="Prevent">
			<staticText>
				<reportElement uuid="e4d24baf-594f-4f6a-83b4-1d37d855e039" key="staticText-1" x="100" y="0" width="412" height="34" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="24"/>
				</textElement>
				<text><![CDATA[Excluded Movements Report]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="56" splitType="Stretch">
			<line>
				<reportElement uuid="325cd07f-fc1a-4e03-91ed-33b70db7e2e9" key="line-1" x="35" y="55" width="540" height="1">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement uuid="bee5a3a9-a582-4af7-b3b7-59a2f6044076" key="staticText-3" x="40" y="5" width="39" height="23">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Entity]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="55e732a0-999f-4fe1-ad51-55339cc668e1" key="textField-2" x="105" y="5" width="90" height="23">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pENTITY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="31b4bec6-1102-4718-9269-db77cd7c7a2d" key="staticText-4" x="375" y="5" width="91" height="23">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Report Date:]]></text>
			</staticText>
			<textField pattern="dd MMMMM yyyy" isBlankWhenNull="false">
				<reportElement uuid="9f34ed32-0ba4-495e-b182-66a3eba9deb7" key="textField-3" x="470" y="5" width="128" height="20">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pREPORT_DATE}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="8afa173c-657d-4e19-9319-8d0e43cf8743" key="staticText-16" x="40" y="30" width="64" height="23">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Threshold]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement uuid="6f9e7517-522d-48d3-9f32-b2d2c0705de3" key="textField-9" x="105" y="30" width="345" height="20">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}):
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ","))+"  "+$P{pTHRESHOLD_TYPE}:""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="55e732a0-999f-4fe1-ad51-55339cc668e1" key="textField-2" x="220" y="5" width="150" height="23">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" markup="none">
					<font size="12"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pENTITY_NAME}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="325cd07f-fc1a-4e03-91ed-33b70db7e2e9" key="line-1" x="35" y="0" width="540" height="1">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
			</line>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="20" splitType="Prevent">
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="200" y="2" width="80" height="18">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{TOT_MVMT}]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="312" y="2" width="75" height="18">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{COUNT_EXCL_OFF}]]></textFieldExpression>
			</textField>
			<textField pattern="##0.0 %" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="387" y="2" width="40" height="18">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{PCT_COUNT_EXCL_OFF}/100]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="450" y="2" width="59" height="18">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{COUNT_EXCL_OS}]]></textFieldExpression>
			</textField>
			<textField pattern="##0.0 %" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="512" y="2" width="41" height="18">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<printWhenExpression><![CDATA[($F{ACCOUNT_ID}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{PCT_COUNT_EXCL_OS}/100]]></textFieldExpression>
			</textField>
			<textField pattern="###0.0" isBlankWhenNull="true">
				<reportElement uuid="ccf32410-a0b0-49da-af8d-34c27434dc03" key="textField" x="90" y="2" width="60" height="18">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{TIMECAT}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="31" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement uuid="1bbe159a-0bbc-43f4-8f79-0e1e091effaa" key="textField-4" x="38" y="5" width="100" height="21" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="8ab76443-2d84-41cb-947b-ec9c5bbf991b" key="textField-5" x="385" y="6" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement uuid="80d6b0ec-9b3d-428b-b889-b0cf7ccbb083" key="textField-6" x="553" y="6" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="113" splitType="Stretch">
			<staticText>
				<reportElement uuid="a9e8e349-8196-4c88-bee1-dcaecb5ee0cd" key="staticText-18" x="230" y="89" width="154" height="18">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="14"/>
				</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
