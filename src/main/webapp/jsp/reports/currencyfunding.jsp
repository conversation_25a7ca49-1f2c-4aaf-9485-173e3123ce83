<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.util.LabelValueBean"%>
<html>
<head>
<title>
<fmt:message key="currencyFunding.mainScreen"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
<script language="JAVASCRIPT">
/*Start : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
var dateSelected = false;
var dateFormat = '${sessionScope.CDM.dateFormat}';
/*End : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var cancelcloseElements = new Array(1);
var configScheduler ="${requestScope.configScheduler}";
var schedulerConfigXML = "${requestScope.schedulerConfigXML}";
var reportType = '${requestScope.reportType}'
var jobId = '${jobId}'
var newCurrencyFundingReportSchedConfig = "${requestScope.newCurrencyFundingReportSchedConfig}";
mandatoryFieldsArray = ["*"];

function bodyOnLoad()
{
   var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyfunding.id.entityId"],document.getElementById("entityName"));
   var dropBox2 = new SwSelectBox(document.forms[0].elements["currencyfunding.currencyCode"],document.getElementById("currencyName"));
   var dropBox3 = new SwSelectBox(document.forms[0].elements["currencyfunding.accountId"],document.getElementById("accountName"));
    document.forms[0].entityText.value=document.getElementById("entityName").innerText;
    document.forms[0].currencyText.value=document.getElementById("currencyName").innerText;
    document.forms[0].accountText.value=document.getElementById("accountName").innerText;
    if (configScheduler != "true") {
		document.forms[0].elements["currencyfunding.showDR"].checked = false;
		document.forms[0].elements["currencyfunding.showCR"].checked = true;
    }
	 if (configScheduler != "true"){
		 /* Start:Vivekanandan:14-10-2009:Mantis 1031:to diable report button when no account Id given for currency*/
			if(document.forms[0].elements["currencyfunding.accountId"].length !=0 && document.forms[0].elements["currencyfunding.accountId"].length>0)
				document.getElementById("reportbutton").innerHTML = document.getElementById("reportenablebutton").innerHTML;
			else
				document.getElementById("reportbutton").innerHTML = document.getElementById("reportdisablebutton").innerHTML;
			/* End:Vivekanandan:14-10-2009:Mantis 1031:to diable report button when no account Id given for currency*/
	 } else {
		    document.forms[0].newCurrencyFundingReportSchedConfig.value = newCurrencyFundingReportSchedConfig;
	   }
		if (configScheduler == "true") {
			loadCalendarWithKeyords();
		} else {
			cal =  new CalendarPopup("caldiv",true,"calFrame");
		 	cal.offsetX = -3;
		  	cal.offsetY = -97;
		}

}


function loadCalendarWithKeyords()
{
	try {


	<%
		ArrayList<LabelValueBean> keyWords = (ArrayList<LabelValueBean>)request.getAttribute("keywords");
		Iterator it = keyWords.iterator();
		while (it.hasNext())
		{
			LabelValueBean lvb = (LabelValueBean) it.next();
	%>
		var newElement = {};
		newElement[headers[1]] = "<%=lvb.getValue()%>";
		newElement[headers[0]] = "<%=lvb.getLabel()%>";
		dataprovider.push(newElement);
	<%
		}

	%>
	cal = new CalendarPopup("caldiv", false, "calFrame");
	cal.offsetX = -3;
	cal.offsetY = -97;
	cal.withKeyWord= true;
	cal.comboboxId= 'ilmStartDay';
	cal.inputKeywordName= 'keywordInput';
	cal.keyWordDataProvider = dataprovider;

	} catch (e) {
		console.log('--',e)
	}
}

var headers = ["Keyword", "Description"];


var dataprovider = new Array();


/*Start: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/
function currencyFundingReport(methodName){
	if (document.forms[0].elements["currencyfunding.dateAsString"].value != "") {
	/*Start : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	if(validateDateField(document.forms[0].elements['currencyfunding.dateAsString'],'currencyFunding.date',dateFormat)){
	/*End : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	if(document.forms[0].elements["currencyfunding.showDR"].checked == false)
	{
		document.forms[0].elements["currencyfunding.showDR"].value ="N";
	}else{
		document.forms[0].elements["currencyfunding.showDR"].value ="Y";
	}

	if(document.forms[0].elements["currencyfunding.showCR"].checked == false){
		document.forms[0].elements["currencyfunding.showCR"].value ="N";
	}else{
		document.forms[0].elements["currencyfunding.showCR"].value ="Y";
	}

	if( document.forms[0].elements["currencyfunding.threshold"].value == ""){
	    document.forms[0].elements["currencyfunding.threshold"].value = "0.0";
	}
	var amount = validateCurrency(document.forms[0].elements['currencyfunding.threshold'],'currencyfunding.threshold',currencyFormat);
	if(amount){
	/* START:code commented by Mahesh on 19-feb-2010 for Mantis 1111: removed the "All" currency option in UI */
	/*if(document.forms[0].elements["currencyfunding.currencyCode"].value != "All")
	{*/
	/* END:code commented by Mahesh on 19-feb-2010 for Mantis 1111: removed the "All" currency option in UI */
		if ((document.forms[0].elements["currencyfunding.showDR"].checked == true) || (document.forms[0].elements["currencyfunding.showCR"].checked == true))
		{
		document.forms[0].method.value = 'report';
		document.forms[0].entityId.value = document.forms[0].elements["currencyfunding.id.entityId"].value;
	    document.forms[0].entityText.value =document.forms[0].entityText.value;
		document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["currencyfunding.currencyCode"].value;
	    document.forms[0].currencyText.value = document.getElementById("accountName").innerText;
		document.forms[0].selectedValueDate.value = document.forms[0].elements["currencyfunding.dateAsString"].value;
		document.forms[0].selectedAcctId.value = document.forms[0].elements["currencyfunding.accountId"].value;
		document.forms[0].accountText.value = document.getElementById("accountName").innerText;
		document.forms[0].thresholdValue.value = document.forms[0].elements['currencyfunding.threshold'].value;
		document.forms[0].selectedShowDR.value = document.forms[0].elements["currencyfunding.showDR"].value;
		document.forms[0].selectedShowCR.value = document.forms[0].elements["currencyfunding.showCR"].value;
		document.forms[0].submit();
		}else{
			alert("<fmt:message key="alert.atlestAnyOneOption"/>");
		}
	/* START:code commented by Mahesh on 19-feb-2010 for Mantis 1111: removed the "All" currency option in UI */
	/*}else{

		alert("Please select particular currency in curreny drop down box");
	}*/
	/* END:code commented by Mahesh on 19-feb-2010 for Mantis 1111: removed the "All" currency option in UI */
	}else{
		document.forms[0].elements['currencyfunding.threshold'].focus();
	}

	setParentChildsFocus();
	/*Start : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	}
	/*End : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	} else {
		alert("<fmt:message key="alert.enterValidDate"/>");
	}
}
/*End: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/

function submitFormByEnity(methodName,status){

	document.forms[0].method.value = methodName;
	document.forms[0].status.value = status;
	document.forms[0].entityId.value = document.forms[0].elements["currencyfunding.id.entityId"].value;
	document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["currencyfunding.currencyCode"].value;
	document.forms[0].selectedAcctId.value = document.forms[0].elements["currencyfunding.accountId"].value;
	document.forms[0].jobId.value = jobId;
	document.forms[0].accountText.value =document.getElementById("accountName").innerText;
	if (configScheduler == "true") {
		document.forms[0].configScheduler.value = configScheduler;
		document.forms[0].newCurrencyFundingReportSchedConfig.value = true;
		document.forms[0].schedulerConfigXML.value = schedulerConfigXML;
	}
	document.forms[0].submit();
}

var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var cal ;

/* Start:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */
function validateCurrDateField(){
var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(document.forms[0].elements['currencyfunding.dateAsString']!=null && document.forms[0].elements['currencyfunding.dateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['currencyfunding.dateAsString'],'currencyFunding.date',dateFormat)){
		}
	}
}

function disableEnterKey(e) {
     var key;
     if(window.event)
          key = window.event.keyCode; //IE
     else
          key = e.which; //firefox

     return (key != 13);
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["currencyfunding.dateAsString"];
  return validate(elementsRef);
 }


function validateDatesFromReportConfig(){
		if(document.forms[0].elements['currencyfunding.dateAsString'].value == "" ){
			alert('<fmt:message key="alert.enterValidDate"/>');
		}else{
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName
					+ "/ilmReport.do?method=validateDates";
			requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["currencyfunding.id.entityId"].value +"&fromDate=" + document.forms[0].elements['currencyfunding.dateAsString'].value + "&toDate=";
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			oXMLHTTP.send();
			var result = new String(oXMLHTTP.responseText);

			if(result == "true") {
				  return true;
			}else {
				alert('<fmt:message key="alert.enterValidToDate"/>');
				return false;
			}
			//return validateDateField('reports.fromDateAsString','fromDateAsString');
		}

}

function saveSchedulerReportOptions(){

	if(validateDatesFromReportConfig()){
		var schedulerConfigXML;
		var reportSchedulerData = new Object();
		reportSchedulerData.jobId = '${jobId}';

		if (document.forms[0].elements["currencyfunding.dateAsString"].value != "") {

			if(document.forms[0].elements["currencyfunding.showDR"].checked == false)
			{
				document.forms[0].elements["currencyfunding.showDR"].value ="N";
			}else{
				document.forms[0].elements["currencyfunding.showDR"].value ="Y";
			}

			if(document.forms[0].elements["currencyfunding.showCR"].checked == false){
				document.forms[0].elements["currencyfunding.showCR"].value ="N";
			}else{
				document.forms[0].elements["currencyfunding.showCR"].value ="Y";
			}

			if( document.forms[0].elements["currencyfunding.threshold"].value == ""){
			    document.forms[0].elements["currencyfunding.threshold"].value = "0.0";
			}
			var amount = validateCurrency(document.forms[0].elements['currencyfunding.threshold'],'currencyfunding.threshold',currencyFormat);
			if(amount){
				if ((document.forms[0].elements["currencyfunding.showDR"].checked == true) || (document.forms[0].elements["currencyfunding.showCR"].checked == true)){

				document.forms[0].entityId.value = document.forms[0].elements["currencyfunding.id.entityId"].value;
			    document.forms[0].entityText.value = document.getElementById("entityName").innerText;
				document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["currencyfunding.currencyCode"].value;
			    document.forms[0].currencyText.value = document.getElementById("currencyName").innerText;
				document.forms[0].selectedValueDate.value = document.forms[0].elements["currencyfunding.dateAsString"].value;
				document.forms[0].selectedAcctId.value = document.forms[0].elements["currencyfunding.accountId"].value;
				document.forms[0].accountText.value =document.getElementById("accountName").innerText;
				document.forms[0].thresholdValue.value = document.forms[0].elements['currencyfunding.threshold'].value;
				document.forms[0].selectedShowDR.value = document.forms[0].elements["currencyfunding.showDR"].value;
				document.forms[0].selectedShowCR.value = document.forms[0].elements["currencyfunding.showCR"].value;

				reportSchedulerData.reportType =  reportType;
				reportSchedulerData.entityId = document.forms[0].entityId.value ;
				reportSchedulerData.entityName = document.forms[0].entityText.value;
				reportSchedulerData.currencyCode = document.forms[0].selectedCurrencyCode.value;
				reportSchedulerData.currencyName = document.forms[0].currencyText.value;
				reportSchedulerData.dateAsString =  document.forms[0].selectedValueDate.value;
				reportSchedulerData.selectedAcctId = document.forms[0].selectedAcctId.value;
				reportSchedulerData.accountText = document.forms[0].accountText.value;
				reportSchedulerData.threshold = document.forms[0].thresholdValue.value;
				reportSchedulerData.selectedShowDR = document.forms[0].selectedShowDR.value
				reportSchedulerData.selectedShowCR = document.forms[0].selectedShowCR.value

				}else{
					alert("<fmt:message key="alert.atlestAnyOneOption"/>");
					return;
				}

			}else{
				document.forms[0].elements['currencyfunding.threshold'].focus();
				return;

			}

			setParentChildsFocus();


			} else {
				alert("<fmt:message key="alert.enterValidDate"/>");
				return;
			}

		schedulerConfigXML = convertConfigObjectToXML(reportSchedulerData);
		this.opener.document.forms[0].schedulerConfigXML.value = getMenuWindow().encode64(schedulerConfigXML.innerHTML);
		this.opener.updateSchedulerConfigParams("");
		window.close();
	}
}


function convertConfigObjectToXML(reportSchedulerData){
	var schedulerConfigXML = document.createElement("div");
	var transactionNode = document.createElement("schedConfig");

	var jobIdNode = document.createElement("jobid");
	jobIdNode.appendChild(document.createTextNode(reportSchedulerData.jobId));

	var reportTypeNode = document.createElement("reporttype");
	reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));

	var entityIdNode = document.createElement("entityid");
	entityIdNode.appendChild(document.createTextNode(reportSchedulerData.entityId));

	var entityNameNode = document.createElement("entityname");
	entityNameNode.appendChild(document.createTextNode(reportSchedulerData.entityName));

	var currencyCodeNode = document.createElement("currencycode");
	currencyCodeNode.appendChild(document.createTextNode(reportSchedulerData.currencyCode));

	var currencyNameNode = document.createElement("currencyname");
	currencyNameNode.appendChild(document.createTextNode(reportSchedulerData.currencyName));

	var dateasstringNode = document.createElement("valuedate");
    dateasstringNode.appendChild(document.createTextNode(reportSchedulerData.dateAsString));

	var selectedAcctIdNode = document.createElement("selectedacctid");
	selectedAcctIdNode.appendChild(document.createTextNode(reportSchedulerData.selectedAcctId));

	var accountTextNode = document.createElement("account");
	accountTextNode.appendChild(document.createTextNode(reportSchedulerData.accountText));

	var thresholdNode = document.createElement("threshold");
	thresholdNode.appendChild(document.createTextNode(reportSchedulerData.threshold));

	var selectedShowDRNode = document.createElement("selectedshowdr");
	selectedShowDRNode.appendChild(document.createTextNode(reportSchedulerData.selectedShowDR));

	var selectedShowCRNode = document.createElement("selectedshowcr");
	selectedShowCRNode.appendChild(document.createTextNode(reportSchedulerData.selectedShowCR));

	var dateFormatNode = document.createElement("dateformatasstring");
	dateFormatNode.appendChild(document.createTextNode(dateFormatValue));


	transactionNode.appendChild(jobIdNode);
	transactionNode.appendChild(reportTypeNode);
	transactionNode.appendChild(entityIdNode);
	transactionNode.appendChild(entityNameNode);
	transactionNode.appendChild(currencyCodeNode);
	transactionNode.appendChild(currencyNameNode);
	transactionNode.appendChild(dateasstringNode);
	transactionNode.appendChild(selectedAcctIdNode);
	transactionNode.appendChild(accountTextNode);
	transactionNode.appendChild(thresholdNode);
	transactionNode.appendChild(selectedShowDRNode);
	transactionNode.appendChild(selectedShowCRNode);
	transactionNode.appendChild(dateFormatNode);

	schedulerConfigXML.appendChild(transactionNode);
	return schedulerConfigXML;
}



/* End:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */



</SCRIPT>

	<style>
#ilmStartDay{
   margin-left: -200px;
    margin-top: -170px;
}
input[name ="keywordInput"] {
	width: 120px
}
</style>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);" onunload="call();">
<form action="currencyFunding.do" method="post">
<input name="method" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="entityText" type="hidden" value="">
<input name="currencyText" type="hidden" value="">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="selectedValueDate" type="hidden" value="">
<input name="selectedAcctId" type="hidden" value="">
<input name="accountText" type="hidden" value="">
<input name="thresholdValue" type="hidden" value="">
<input name="selectedShowDR" type="hidden" value="">
<input name="selectedShowCR" type="hidden" value="">
<input name="status" type="hidden" value="">
<input name="jobId" type="hidden" value="">
<input name="configScheduler" type="hidden" value="">
<input name="reportType" type="hidden" value="">
<input name="newCurrencyFundingReportSchedConfig" type="hidden" value="">
<input name="schedulerConfigXML" type="hidden" value="">


<div id="CurrencyFunding" style="position:absolute; left:20px; top:20px; width:570px; height:160px; border:2px outset;" color="#7E97AF">
   <div id="CurrencyFunding" style="position:absolute; left:8px; top:5px; width:560px; height:200;">
    <div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>
	  <table width="559px" border="0" cellpadding="0" cellspacing="0" height="150" class="content">

			  <tr height="22">
				<td  width="80px"><b><fmt:message key="currencyFunding.entity"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="130px" >
				<!--START:code modified by Mahesh on 18-feb-2010 for Mantis 1111: modified the tooltip-->
					<select id="currencyfunding.id.entityId" name="currencyfunding.id.entityId" class="htmlTextAlpha"
						  titleKey="tooltip.selectEntityid" style="width:130px" tabindex="1"  onchange="submitFormByEnity('displayList','entity')">
					  <c:forEach items="${requestScope.entities}" var="item">
						  <option value="${item.value}" <c:if test="${currencyfunding.id.entityId == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
				  </select>
				</td>
				<td width="275px">
					<span id="entityName" name="entityName" class="spantext">
				</td>

			</tr>

			<tr height="22">
				<td  width="80px"><b><fmt:message key="currencyFunding.currency"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="130px" >
				<!--START:code modified by Mahesh on 18-feb-2010 for Mantis 1111: modified the tooltip-->
					<select id="currencyfunding.currencyCode" name="currencyfunding.currencyCode" class="htmlTextAlpha"
						  titleKey="tooltip.selectCurr" style="width:130px" tabindex="2"  onchange="submitFormByEnity('displayList','currency')"">
					  <c:forEach items="${requestScope.currencies}" var="item">
						  <option value="${item.value}" <c:if test="${currencyfunding.currencyCode == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
				  </select>
				</td>
				<td width="275px">
					<span id="currencyName" name="currencyName" class="spantext">
				</td>
		    </tr>
			<!--START:Code Modified by Krishna on 02-DEC-2010 for Mantis 1249:To Extend account id to 20 on screen && 35 on database -->
			<tr height="22">
				<td  width="80px"><b><fmt:message key="currencyFunding.account"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="230px" >
					<select id="currencyfunding.accountId" name="currencyfunding.accountId" class="htmlTextAlpha"
						  titleKey="tooltip.selectCurr" style="width:200px;margin-right: 20px;" tabindex="3">
					  <c:forEach items="${requestScope.accountIdList}" var="item">
						  <option value="${item.value}" <c:if test="${currencyfunding.accountId == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
				  </select>
				</td>
				<td width="225px">
					<span id="accountName" name="accountName" class="spantext">
				</td>
			</tr>
			<!--END:Code Modified by Krishna on 02-DEC-2010 for Mantis 1249:To Extend account id to 20 on screen && 35 on database -->
			<tr height="22">
				  <td width="90px"><b><fmt:message key="currencyFunding.date"/></b>*</td>
				  <td width="28px">&nbsp;</td>
				  <td width="120px" >
					<!-- Start:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 -->
					<input type="text" tabindex="4" titleKey="tooltip.currencyFundingDate" cssClass="htmlTextNumeric" name="currencyfunding.dateAsString" value="${currencyfunding.dateAsString}"     style="width:79px;margin-bottom: 5px;margin-top: 2px;height:21;" maxlength="10" onchange="if(validateForm(document.forms[0]) ){validateCurrDateField();}" onkeypress="return disableEnterKey(event)" onmouseout="dateSelected=false" />
					<A  title='<fmt:message key="tooltip.calendarcurrencyFundingdate"/>' tabindex="5" name="datelink" ID="datelink"  onClick="cal.select(document.forms[0].elements['currencyfunding.dateAsString'],'datelink',dateFormatValue);dateSelected=true; return false;"><img src="images/calendar-16.gif"> </A>
					<!-- End:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 -->
					  </td><td width="265px"></td>
			</tr>

			<tr height="18">
				  <td width="80px"><b><fmt:message key="currencyFunding.threshold"/></b></td>
				  <td width="28px">&nbsp;</td>
				  <td width="120px" >
				    <!--START:code modified by Mahesh on 18-feb-2010 for Mantis 1111: Mentioned max length for input text box-->
					<input type="text" tabindex="6" maxlength="18" titleKey="tooltip.currencyFundingThreshold" cssClass="htmlTextNumeric" name="currencyfunding.threshold" value="${currencyfunding.threshold}"   style="width:130px;" onchange="return validateCurrency(this,'currencyfunding.threshold',currencyFormat)" />
                    <!--END:code modified by Mahesh on 18-feb-2010 for Mantis 1111: Mentioned max length for input text box-->
                  </td><td width="265px"></td>
			</tr>

			<tr height="18">
					<td width="80px"></td>
					<td width="28px">&nbsp;</td>
					<td width="120px">
						 <input type="checkbox"  name="currencyfunding.showDR" value="Y" ${requestScope.currencyfunding.showDR == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.currencyfunding.showDR == "Y"}' style="width:13px;" titleKey="tooltip.selectShowDR" cssClass="htmlTextAlpha" tabindex="7" />
						 <b><fmt:message key="currencyFunding.showDR"/></b>
					</td>
					<td width="120px">
						 <input type="checkbox"  name="currencyfunding.showCR" value="Y" ${requestScope.currencyfunding.showCR == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.currencyfunding.showCR == "Y"}' style="width:13px;" titleKey="tooltip.selectShowCR" cssClass="htmlTextAlpha" tabindex="8" />
						 <b><fmt:message key="currencyFunding.showCR"/></b>
					</td>
			</tr>
		</table>
	</div>
  </div>
  </div>

<div id="CurrencyFunding" style="position:absolute; left:515; top:195; width:70px; height:29px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			<a tabindex="11" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Funding Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
		    </td>

		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:20; top:185; border:2px outset; width:570px; height:40px; visibility:visible;">
  <div id="CurrencyFunding" style="position:absolute; left:6; top:6; width:425; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
	<c:if test="${'true' == requestScope.configScheduler}">

		<td><a title='<fmt:message key="tooltip.save"/>' width="70" onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"  onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
								onclick="saveSchedulerReportOptions('currencyFundingReport');"><fmt:message key="button.save"/></a></td>

</c:if>
	<c:if test="${'true' != requestScope.configScheduler}">

		<td id="reportbutton" width="70"> </td>

</c:if>
		  <td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
				<a tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
	</tr>
   </table>
</div>
</div>

 <div id="CurrencyFunding" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:hidden;">
    <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="reportenablebutton" >
		<a tabindex="9" title='<fmt:message key="tooltip.reportbutton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:currencyFundingReport('report');"><fmt:message key="button.report"/></a>
		</td>
		<td id="reportdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.report"/></a>
		</td>
	</tr>
    </table>
  </div>

<blockquote>&nbsp;</blockquote>
<p>&nbsp;</p>
</form>
<DIV ID="caldiv" STYLE="position:absolute;visibility:hidden;background-color:white;layer-background-color:white;"></DIV>
<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;"></iframe>
</body>
</html>