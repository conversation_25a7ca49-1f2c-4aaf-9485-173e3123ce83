<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="subRepRoleMenuAccess"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="535"
		 columnSpacing="0"
		 leftMargin="30"
		 rightMargin="30"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="P_ROLE" isForPrompting="true" class="java.lang.String"/>
	
	<queryString><![CDATA[  SELECT   LPAD (' ', 5 * (LEVEL - 1)) || a.menu_item_desc item_desc,
                    a.access_name access_name 
             FROM   (SELECT   mi.menu_item_id,
                              mi.parent_menu_item_id,
                              mi.menu_group_order,
                              mi.menu_order,
                              mi.menu_item_desc,
                              pa.access_name
                       FROM   p_menu_item mi
                              INNER JOIN
                              p_menu_access ma
                              ON (ma.menu_item_id = mi.menu_item_id)
                              INNER JOIN
                              p_access pa
                              ON (pa.access_id = ma.access_id)
                      WHERE   ma.role_id = $P{P_ROLE}
                      UNION
                     SELECT   mib.menu_item_id,
                              mib.parent_menu_item_id,
                              mib.menu_group_order,
                              mib.menu_order,
                              menu_item_desc,
                              'DUMMY' access_name
                       FROM   p_menu_item mib
                      WHERE   mib.menu_item_id = TO_CHAR(0)) A
            WHERE   LEVEL != 1
       START WITH   a.menu_item_id = TO_CHAR(0)
       CONNECT BY   NOCYCLE PRIOR a.menu_item_id = a.parent_menu_item_id
ORDER SIBLINGS BY   a.menu_group_order, a.menu_order]]></queryString>

	<field name="ITEM_DESC" class="java.lang.String"/>
	<field name="ACCESS_NAME" class="java.lang.String"/>

		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="23"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="70"
						y="3"
						width="170"
						height="20"
						key="staticText-1"/>
					<box></box>
					<textElement verticalAlignment="Middle">
						<font size="12"/>
					</textElement>
				<text><![CDATA[Menu Access:]]></text>
				</staticText>
			</band>
		</title>
		<pageHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="20"  splitType="Stretch" >
				<rectangle radius="0" >
					<reportElement
						mode="Opaque"
						x="70"
						y="1"
						width="465"
						height="17"
						forecolor="#000000"
						backcolor="#999999"
						key="element-22"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="0.25" lineStyle="Solid"/>
</graphicElement>
				</rectangle>
				<staticText>
					<reportElement
						x="70"
						y="1"
						width="197"
						height="16"
						forecolor="#FFFFFF"
						key="element-90"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<text><![CDATA[Menu Option]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="267"
						y="1"
						width="267"
						height="16"
						forecolor="#FFFFFF"
						key="element-90"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="12"/>
					</textElement>
				<text><![CDATA[Access Level]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="16"  splitType="Stretch" >
				<line direction="TopDown">
					<reportElement
						x="70"
						y="14"
						width="465"
						height="0"
						forecolor="#808080"
						key="line"
						positionType="FixRelativeToBottom"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="0.25" lineStyle="Solid"/>
</graphicElement>
				</line>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="70"
						y="1"
						width="197"
						height="15"
						key="textField"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ITEM_DESC}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="267"
						y="1"
						width="267"
						height="15"
						key="textField"/>
					<box leftPadding="2" rightPadding="2">					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>						
						<font fontName="SansSerif" size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ACCESS_NAME}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  splitType="Stretch" >
			</band>
		</summary>
</jasperReport>
