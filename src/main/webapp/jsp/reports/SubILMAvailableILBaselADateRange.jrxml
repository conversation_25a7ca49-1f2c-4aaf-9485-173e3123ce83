<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pSubRepILMIntradayLiquidity" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="0" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1">
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppGC_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN1" class="java.util.Date"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDB_LINK" class="java.lang.String"/>
		<parameter name="ppILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
		<parameter name="ppCURRENCY_PATTERN" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE_MIN2" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN3" class="java.util.Date"/>
		<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT  	 min1.attribute_id,
						         min1.attribute_name||' ('||DECODE(min1.contribute_total, 'PLUS', '+', 'MINUS', '-', '')||')' ATTRIBUTE_LABEL,
						         min1.avg_num_value/$P{p_CCY_MULTIPLIER_VALUE} AS  VALUE_MIN1 ,
						         min2.avg_num_value/$P{p_CCY_MULTIPLIER_VALUE} AS  VALUE_MIN2 ,
						         min3.avg_num_value/$P{p_CCY_MULTIPLIER_VALUE} AS  VALUE_MIN3 ,
						         avg.avg_num_value/$P{p_CCY_MULTIPLIER_VALUE}  AS  AVG_VALUE
					  FROM       TABLE (pkg_ilm_rep.fn_avg_attrs (
					  				$P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN1},
						            $P{ppVALUE_DATE_MIN1},
						            $P{ppROLE_ID})) min1,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						            $P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN2},
						            $P{ppVALUE_DATE_MIN2},
						            $P{ppROLE_ID})) min2,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						            $P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE_MIN3},
						            $P{ppVALUE_DATE_MIN3},
						            $P{ppROLE_ID})) min3,
						        TABLE (pkg_ilm_rep.fn_avg_attrs (
						            $P{ppHOST_ID},
						            $P{ppENTITY_ID},
						            $P{ppCCY},
						            $P{ppGC_GROUP_ID},
						            $P{ppSERIES_IDENTIFIER},
						            $P{ppDB_LINK},
						            $P{ppVALUE_DATE},
						            $P{ppVALUE_DATE_END},
						            $P{ppROLE_ID})) avg
						WHERE   min1.attribute_id=min2.attribute_id
						        AND min1.attribute_id=min3.attribute_id
						        AND min1.attribute_id=avg.attribute_id]]>
		</queryString>
		<field name="ATTRIBUTE_LABEL" class="java.lang.String"/>
		<field name="VALUE_MIN1" class="java.lang.Double"/>
		<field name="VALUE_MIN2" class="java.lang.Double"/>
		<field name="VALUE_MIN3" class="java.lang.Double"/>
		<field name="AVG_VALUE" class="java.lang.Double"/>
	</subDataset>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE_MIN1" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN2" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_MIN3" class="java.util.Date"/>
	<parameter name="p_CCY" class="java.lang.String"/>
	<parameter name="p_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_CB_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_GC_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="p_DB_LINK" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT     min1cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_CB_AVG_EXTERNAL_SOD,
							min2cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_CB_AVG_EXTERNAL_SOD,
							min3cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_CB_AVG_EXTERNAL_SOD,
							avgcb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CB_AVG_EXTERNAL_SOD,
	                        min1cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_CB_AVG_COLLATERAL,
	                        min2cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_CB_AVG_COLLATERAL,
	                        min3cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_CB_AVG_COLLATERAL,
	                        avgcb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CB_AVG_COLLATERAL,
	                        min1gc.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_COLLATERAL,
	                        min2gc.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_COLLATERAL,
	                        min3gc.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_COLLATERAL,
	                        avggc.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_COLLATERAL,
	                        (min1gc.AVG_EXTERNAL_SOD - nvl(min1cb.AVG_EXTERNAL_SOD,0))/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1BOB,
	                        (min2gc.AVG_EXTERNAL_SOD - nvl(min2cb.AVG_EXTERNAL_SOD,0))/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2BOB,
	                        (min3gc.AVG_EXTERNAL_SOD - nvl(min3cb.AVG_EXTERNAL_SOD,0))/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3BOB,
	                        (avggc.AVG_EXTERNAL_SOD - nvl(avgcb.AVG_EXTERNAL_SOD,0))/$P{p_CCY_MULTIPLIER_VALUE} AS AVGBOB,
	                        (min1gc.AVG_COLLATERAL - nvl(min1cb.AVG_COLLATERAL,0))/$P{p_CCY_MULTIPLIER_VALUE}     AS MIN1OSC,
	                        (min2gc.AVG_COLLATERAL - nvl(min2cb.AVG_COLLATERAL,0))/$P{p_CCY_MULTIPLIER_VALUE}     AS MIN2OSC,
	                        (min3gc.AVG_COLLATERAL - nvl(min3cb.AVG_COLLATERAL,0))/$P{p_CCY_MULTIPLIER_VALUE}     AS MIN3OSC,
	                        (avggc.AVG_COLLATERAL - nvl(avgcb.AVG_COLLATERAL,0))/$P{p_CCY_MULTIPLIER_VALUE}       AS AVGOSC,
	                        min1gc.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_TOTAL,
	                        min1gc.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_SECURED,
	                        min2gc.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_TOTAL,
	                        min2gc.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_SECURED,
	                        min3gc.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_TOTAL,
	                        min3gc.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_SECURED,
	                        avggc.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_SECURED,
	                        avggc.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_TOTAL,
	                        min1gc.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_COMMITTED,
	                        min2gc.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_COMMITTED,
	                        min3gc.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_COMMITTED,
	                        avggc.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_COMMITTED,
	                        min1gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_ASSETS,
	                        min2gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_ASSETS,
	                        min3gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_ASSETS,
	                        avggc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_ASSETS,
	                        min1gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN1_GC_AVG_OTHER_TOTAL,
	                        min2gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN2_GC_AVG_OTHER_TOTAL,
							min3gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS MIN3_GC_AVG_OTHER_TOTAL,
							avggc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_GC_AVG_OTHER_TOTAL
 				   FROM
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1}, $P{P_ROLE_ID})) min1cb,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN2}, $P{p_VALUE_DATE_MIN2}, $P{P_ROLE_ID})) min2cb,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN3}, $P{p_VALUE_DATE_MIN3}, $P{P_ROLE_ID})) min3cb,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE}, $P{p_VALUE_DATE_END}, $P{P_ROLE_ID})) avgcb,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1}, $P{P_ROLE_ID})) min1gc,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN2}, $P{p_VALUE_DATE_MIN2}, $P{P_ROLE_ID})) min2gc,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN3}, $P{p_VALUE_DATE_MIN3}, $P{P_ROLE_ID})) min3gc,
							TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE}, $P{p_VALUE_DATE_END}, $P{P_ROLE_ID})) avggc]]>
	</queryString>
	<field name="MIN1_CB_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN2_CB_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN3_CB_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="AVG_CB_AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="MIN1_CB_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN2_CB_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN3_CB_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="AVG_CB_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="MIN1BOB" class="java.lang.Double"/>
	<field name="MIN2BOB" class="java.lang.Double"/>
	<field name="MIN3BOB" class="java.lang.Double"/>
	<field name="AVGBOB" class="java.lang.Double"/>
	<field name="MIN1OSC" class="java.lang.Double"/>
	<field name="MIN2OSC" class="java.lang.Double"/>
	<field name="MIN3OSC" class="java.lang.Double"/>
	<field name="AVGOSC" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_TOTAL" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_TOTAL" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_SECURED" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_SECURED" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_SECURED" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_SECURED" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_COMMITTED" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_COMMITTED" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_COMMITTED" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_ASSETS" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_ASSETS" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_ASSETS" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_ASSETS" class="java.lang.Double"/>
	<field name="MIN1_GC_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN2_GC_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="MIN3_GC_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<field name="AVG_GC_AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="255" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="820" height="100" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_RESERVES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CB_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CB_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CB_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CB_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CB_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CB_AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CB_AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_CB_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_CB_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_CB_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_CB_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_CB_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_CB_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CB_AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CB_AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="48" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pBALANCES_OTHER_BANKS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVGBOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVGBOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="64" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_SYSTEMS_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVGOSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVGOSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="80" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pTOTAL_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_AVG_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_AVG_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_AVG_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_AVG_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_AVG_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_AVG_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_AVG_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_AVG_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="100" width="820" height="92" backcolor="#F2F2F2"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_AVG_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_AVG_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_AVG_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_AVG_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_AVG_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_AVG_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_AVG_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_AVG_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="318" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_AVG_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_AVG_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="433" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_AVG_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_AVG_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="547" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_AVG_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_AVG_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="662" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_AVG_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_AVG_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" mode="Opaque" x="0" y="48" width="820" height="33" backcolor="#D9D9D9"/>
					<textField>
						<reportElement key="textField" positionType="Float" x="5" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement>
							<font size="10"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pUNENCUMBERED_LIQUID_ASSETS")]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="318" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_AVG_ASSETS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_AVG_ASSETS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="433" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_AVG_ASSETS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_AVG_ASSETS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="662" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
							<property name="local_mesure_unity" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_AVG_ASSETS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_AVG_ASSETS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="547" y="0" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_AVG_ASSETS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_AVG_ASSETS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField" positionType="Float" x="5" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement>
							<font size="10"/>
						</textElement>
						<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER")]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="433" y="16" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_GC_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN2_GC_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="547" y="16" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
							<property name="local_mesure_unity" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_GC_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN3_GC_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="318" y="16" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
							<property name="local_mesure_unity" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_GC_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{MIN1_GC_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement key="textField-1" x="662" y="16" width="142" height="16">
							<property name="local_mesure_unitx" value="pixel"/>
						</reportElement>
						<box>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineColor="#000000"/>
							<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
						<textElement textAlignment="Right" verticalAlignment="Middle"/>
						<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_GC_AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_GC_AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
					</textField>
				</frame>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="-1" y="182" width="820" height="32" backcolor="#F2F2F2">
					<printWhenExpression><![CDATA[$P{p_SERIES_IDENTIFIER}.equals("Standard")]]></printWhenExpression>
				</reportElement>
				<textField>
					<reportElement key="textField" positionType="Float" x="5" y="0" width="269" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement positionType="Float" x="20" y="16" width="790" height="16"/>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="dataset1">
							<datasetParameter name="ppENTITY_ID">
								<datasetParameterExpression><![CDATA[$P{p_ENTITY_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY">
								<datasetParameterExpression><![CDATA[$P{p_CCY}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_END">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_END}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN1">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN1}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN2">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN2}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN3">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN3}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppGC_GROUP_ID">
								<datasetParameterExpression><![CDATA[$P{p_GC_GROUP_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppSERIES_IDENTIFIER">
								<datasetParameterExpression><![CDATA[$P{p_SERIES_IDENTIFIER}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="p_CCY_MULTIPLIER_VALUE">
								<datasetParameterExpression><![CDATA[$P{p_CCY_MULTIPLIER_VALUE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppDB_LINK">
								<datasetParameterExpression><![CDATA[$P{p_DB_LINK}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_UTIL">
								<datasetParameterExpression><![CDATA[$P{p_ILM_UTIL}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCURRENCY_PATTERN">
								<datasetParameterExpression><![CDATA[$P{p_CURRENCY_PATTERN}]]></datasetParameterExpression>
							</datasetParameter>
						</datasetRun>
						<jr:listContents height="16" width="790">
							<textField>
								<reportElement x="0" y="0" width="259" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement>
									<font fontName="SansSerif" size="10" isBold="false"/>
								</textElement>
								<textFieldExpression class="java.lang.String"><![CDATA[$F{ATTRIBUTE_LABEL}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="298" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN1}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN1},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="413" y="0" width="142" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN2}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN2},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="528" y="0" width="142" height="16"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN3}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN3},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="643" y="0" width="142" height="16"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_VALUE}!=null)?($P{ppILM_UTIL}.formatCurrency($F{AVG_VALUE},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
			</frame>
		</band>
	</detail>
</jasperReport>
