<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SubReportILMIntraliquitityA" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="2" bottomMargin="2" uuid="0798721d-6c63-4c21-83e7-1761d44deffd">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1" uuid="7958d266-c6ad-4973-bb3a-2769eb677c4b">
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppGC_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN1" class="java.util.Date"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDB_LINK" class="java.lang.String"/>
		<parameter name="ppILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
		<parameter name="ppCURRENCY_PATTERN" class="java.lang.String"/>
		<parameter name="ppCCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT attribute_id,
							attribute_name||'('||DECODE(min1.contribute_total, 'PLUS', '+', 'MINUS', '-', '')||')' ATTRIBUTE_LABEL,
						    avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} VALUE_MIN1
					FROM TABLE (pkg_ilm_rep.fn_avg_attrs (
							$P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppGC_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE_MIN1},
							$P{ppVALUE_DATE_MIN1},
							$P{ppROLE_ID})) min1]]>
		</queryString>
		<field name="ATTRIBUTE_LABEL" class="java.lang.String"/>
		<field name="VALUE_MIN1" class="java.lang.Double"/>
	</subDataset>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE_MIN1" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="p_CCY" class="java.lang.String"/>
	<parameter name="p_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_ILM_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_CB_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_GC_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="p_DB_LINK" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT  	cor.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} SOD_CORR,
					        cor.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} COLL_CORR,
					        cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} SOD_CB,
					        cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} COLL_CB,
					        (gc.AVG_EXTERNAL_SOD - nvl(cb.AVG_EXTERNAL_SOD,0) - cor.AVG_EXTERNAL_SOD)/$P{p_CCY_MULTIPLIER_VALUE} BOB,
					        (gc.AVG_COLLATERAL - nvl(cb.AVG_COLLATERAL,0) - cor.AVG_COLLATERAL)/$P{p_CCY_MULTIPLIER_VALUE} OSC,
					        cor.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} CORR_TOTAL,
					        cor.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} CORR_SECURED,
					        cor.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} CORR_COMMITTED,
							cb.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} CB_TOTAL,
							(gc.AVG_CREDIT_LINE_TOTAL - cb.AVG_CREDIT_LINE_TOTAL - cor.AVG_CREDIT_LINE_TOTAL)/$P{p_CCY_MULTIPLIER_VALUE} OTHER_TOTAL,
							
							(gc.AVG_CREDIT_LINE_SECURED - nvl(cb.AVG_CREDIT_LINE_SECURED,0) - nvl(cor.AVG_CREDIT_LINE_SECURED,0))/ $P{p_CCY_MULTIPLIER_VALUE} OTHER_SECURED,
							(gc.AVG_CREDIT_LINE_COMMITTED - nvl(cb.AVG_CREDIT_LINE_COMMITTED,0) - nvl(cor.AVG_CREDIT_LINE_COMMITTED,0))/ $P{p_CCY_MULTIPLIER_VALUE} OTHER_COMMITTED, 
							
							cb.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} CB_SECURED,
                            cb.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} CB_COMMITTED,
					        gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} GC_UNENC,
					        gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} GC_OTHER
  				FROM  	TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) cb,
       					TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) gc,
        				TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID},$P{p_ENTITY_ID}, $P{p_CCY}, $P{p_ILM_GROUP_ID}, $P{p_SERIES_IDENTIFIER}, $P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1},$P{P_ROLE_ID})) cor]]>
	</queryString>
	<field name="SOD_CORR" class="java.lang.Double"/>
	<field name="COLL_CORR" class="java.lang.Double"/>
	<field name="SOD_CB" class="java.lang.Double"/>
	<field name="COLL_CB" class="java.lang.Double"/>
	<field name="BOB" class="java.lang.Double"/>
	<field name="OSC" class="java.lang.Double"/>
	<field name="CORR_TOTAL" class="java.lang.Double"/>
	<field name="CORR_COMMITTED" class="java.lang.Double"/>
	<field name="CORR_SECURED" class="java.lang.Double"/>
	<field name="CB_COMMITTED" class="java.lang.Double"/>
	<field name="CB_SECURED" class="java.lang.Double"/>
	<field name="CB_TOTAL" class="java.lang.Double"/>
	<field name="OTHER_TOTAL" class="java.lang.Double"/>
	<field name="OTHER_SECURED" class="java.lang.Double"/>
	<field name="OTHER_COMMITTED" class="java.lang.Double"/>
	<field name="GC_UNENC" class="java.lang.Double"/>
	<field name="GC_OTHER" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="353" splitType="Stretch">
			<frame>
				<reportElement uuid="e54b2804-75ba-4dea-baa9-f72f91235591" positionType="Float" mode="Opaque" x="0" y="0" width="534" height="128" backcolor="#D9D9D9"/>
				<textField>
					<reportElement uuid="2c70f9c3-db09-43d0-8ecf-94f60f4962cf" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="8274a635-662f-4cdd-9187-6048e09607db" key="textField" positionType="Float" x="7" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCORRESPONDENT_BALANCE")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="269e6108-cfa8-4a6e-9ffd-7678c1e9cc14" key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{SOD_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{SOD_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="47f9f304-b3e9-4888-89f1-7ae173584f23" key="textField" positionType="Float" x="7" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCORRESPONDENT_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="c7031cb0-dc02-46e5-8966-54fa47082071" key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitheight" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{COLL_CORR}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{COLL_CORR},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="f1d6972b-69fe-4763-afcf-2d0904c858d7" key="textField" positionType="Float" x="7" y="48" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_RESERVES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="008509f9-62ba-4276-91df-104252fd2598" key="textField-1" x="380" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{SOD_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{SOD_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="416e1f8b-09bc-4a58-96a8-68309e2d6b54" key="textField" positionType="Float" x="7" y="64" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="43fb4522-0a51-4c93-89d6-ef02d3868796" key="textField-1" x="380" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{COLL_CB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{COLL_CB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="8ddd51fa-8632-4e32-b268-9719d26b0fb5" key="textField" positionType="Float" x="7" y="80" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pBALANCES_OTHER_BANKS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2c663aca-f83b-4eba-94c6-817ab71a2f2b" key="textField-1" x="380" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="797459f3-4672-4fa0-802d-4754277f57e0" key="textField" positionType="Float" x="7" y="96" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_SYSTEMS_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="70c41c87-e49b-45c3-8a61-e928776cd533" key="textField-1" x="380" y="96" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="c1ba9248-688e-49ec-a76c-5d6d719b654e" key="textField" positionType="Float" x="7" y="112" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pTOTAL_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="b5ba6746-22d6-4a89-a1b2-c5ec01d69098" key="textField-1" x="380" y="112" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CORR_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CORR_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement uuid="c93073cb-90ac-4ea1-ad4f-b4576488141b" positionType="Float" mode="Opaque" x="0" y="128" width="534" height="80" backcolor="#F2F2F2"/>
				<textField>
					<reportElement uuid="673f10b7-0f78-47cb-931d-fd8c5b32c2fa" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2b9a6a44-bac7-45f7-8cca-45c877788ffa" key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="d817b788-36d0-4f2c-9a89-99714a9968b5" key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CORR_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CORR_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="599191da-6a51-4bd0-88b9-e1f802b5ec82" key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="59ae27a4-0bff-4847-8f88-83108b2cfe6e" key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CORR_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CORR_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement uuid="1653412e-970f-4ca3-8c91-cf4775810eaa" positionType="Float" mode="Opaque" x="0" y="176" width="534" height="18" backcolor="#D9D9D9">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textField>
					<reportElement uuid="31bf3e89-b322-4035-9d91-9e71e85c4243" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="46ea7164-01de-45ea-830a-255b0e2e8e32" key="textField-1" x="380" y="0" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CB_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CB_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				</frame>

				<frame>
				<reportElement uuid="c93073cb-90ac-4ea1-ad4f-b4576488141b" positionType="Float" mode="Opaque" x="0" y="194" width="534" height="80" backcolor="#F2F2F2"/>
				<textField>
					<reportElement uuid="673f10b7-0f78-47cb-931d-fd8c5b32c2fa" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2b9a6a44-bac7-45f7-8cca-45c877788ffa" key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="d817b788-36d0-4f2c-9a89-99714a9968b5" key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CB_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="599191da-6a51-4bd0-88b9-e1f802b5ec82" key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="59ae27a4-0bff-4847-8f88-83108b2cfe6e" key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{CB_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="245" width="534" height="35" backcolor="#D9D9D9"/>
				<textField>
					<reportElement uuid="35bc9c47-1c95-45e8-b2f0-5d4111d5e914" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_BANK_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="c1a4dd2a-9044-4055-8cca-1c1e4a9fb430" key="textField-1" x="380" y="0" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
		</frame>

			<frame>
				<reportElement uuid="c93073cb-90ac-4ea1-ad4f-b4576488141b" positionType="Float" mode="Opaque" x="0" y="263" width="534" height="80" backcolor="#F2F2F2"/>
				<textField>
					<reportElement uuid="673f10b7-0f78-47cb-931d-fd8c5b32c2fa" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="2b9a6a44-bac7-45f7-8cca-45c877788ffa" key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="d817b788-36d0-4f2c-9a89-99714a9968b5" key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CB_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{OTHER_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="599191da-6a51-4bd0-88b9-e1f802b5ec82" key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement uuid="59ae27a4-0bff-4847-8f88-83108b2cfe6e" key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression><![CDATA[($F{CB_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{OTHER_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="313" width="534" height="40" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="2" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pUNENCUMBERED_LIQUID_ASSETS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="2" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{GC_UNENC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{GC_UNENC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="18" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="18" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{GC_OTHER}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{GC_OTHER},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="32">
			<printWhenExpression><![CDATA[$P{p_SERIES_IDENTIFIER}.equals("Standard")]]></printWhenExpression>
			<frame>
				<reportElement uuid="524a0db6-6404-4662-a6cd-51186434174b" positionType="Float" mode="Opaque" x="0" y="0" width="534" height="32" backcolor="#F2F2F2">
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<componentElement>
					<reportElement uuid="4b976cde-b2c7-463a-a6b3-691ef2d0e6f7" positionType="Float" x="20" y="16" width="505" height="16">
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="dataset1" uuid="72a89f01-3bf1-4817-8d13-8bc905cfb8ee">
						    <datasetParameter name="ppHOST_ID">
								<datasetParameterExpression><![CDATA[$P{P_HOST_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppENTITY_ID">
								<datasetParameterExpression><![CDATA[$P{p_ENTITY_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY">
								<datasetParameterExpression><![CDATA[$P{p_CCY}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_END">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_END}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppGC_GROUP_ID">
								<datasetParameterExpression><![CDATA[$P{p_GC_GROUP_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppSERIES_IDENTIFIER">
								<datasetParameterExpression><![CDATA[$P{p_SERIES_IDENTIFIER}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN1">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN1}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppDB_LINK">
								<datasetParameterExpression><![CDATA[$P{p_DB_LINK}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_UTIL">
								<datasetParameterExpression><![CDATA[$P{p_ILM_UTIL}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCURRENCY_PATTERN">
								<datasetParameterExpression><![CDATA[$P{p_CURRENCY_PATTERN}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY_MULTIPLIER_VALUE">
								<datasetParameterExpression><![CDATA[$P{p_CCY_MULTIPLIER_VALUE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppROLE_ID">
								<datasetParameterExpression><![CDATA[$P{P_ROLE_ID}]]></datasetParameterExpression>
							</datasetParameter>
						</datasetRun>
						<jr:listContents height="16" width="505">
							<textField>
								<reportElement uuid="41cf5b3f-9d48-4f7f-a835-0b6405070f26" x="0" y="0" width="230" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement>
									<font fontName="SansSerif" size="10" isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ATTRIBUTE_LABEL}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement uuid="0ba4069e-3700-49eb-b225-8bace7a81694" x="272" y="0" width="230" height="16">
									<property name="local_mesure_unitx" value="pixel"/>
								</reportElement>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[($F{VALUE_MIN1}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN1},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
				<textField>
					<reportElement uuid="891ceef1-53b6-459c-b965-159162519c5c" key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
