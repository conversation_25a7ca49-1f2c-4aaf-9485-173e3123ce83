<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TurnoverReport" pageWidth="654" pageHeight="942" columnWidth="594" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="fc13c680-9f26-40c6-8287-61e0508e4181">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.4641000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pEntity_Name" class="java.lang.String"/>
	<parameter name="pShow_Main" class="java.lang.String"/>
	<parameter name="pShow_Sub" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pReport_DateFrom" class="java.util.Date"/>
	<parameter name="pReport_DateTo" class="java.util.Date"/>
	<parameter name="pUseDataSource" class="java.lang.String"/>
	<parameter name="pDataSourceName" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT ACC.CURRENCY_CODE,
       SUM(DECODE (MOV.SIGN, 'C', AMOUNT, 0)) CREDIT,
       SUM(DECODE (MOV.SIGN, 'D', AMOUNT, 0)) DEBIT,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'M', DECODE (MOV.SIGN, 'C', MOV.AMOUNT, 0),0)) CR_Main_value,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'M', DECODE (MOV.SIGN, 'D', MOV.AMOUNT, 0),0)) DR_Main_value,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'S', DECODE (MOV.SIGN, 'C', MOV.AMOUNT, 0),0)) CR_Sub_value,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'S', DECODE (MOV.SIGN, 'D', MOV.AMOUNT, 0),0)) DR_Sub_value ,
       SUM(DECODE (MOV.SIGN, 'C', MOV.AMOUNT, 0)) + SUM(DECODE (MOV.SIGN, 'D', MOV.AMOUNT, 0)) sum_turnover,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'M', DECODE (MOV.SIGN, 'C', MOV.AMOUNT, 0),0)) + SUM(DECODE (ACC.ACCOUNT_LEVEL, 'M', DECODE (MOV.SIGN, 'D', MOV.AMOUNT, 0),0)) sum_main_value,
       SUM(DECODE (ACC.ACCOUNT_LEVEL, 'S', DECODE (MOV.SIGN, 'C', MOV.AMOUNT, 0),0)) + SUM(DECODE (ACC.ACCOUNT_LEVEL, 'S', DECODE (MOV.SIGN, 'D', MOV.AMOUNT, 0),0)) sum_sub_value
  FROM P_MOVEMENT MOV, P_ACCOUNT ACC
 WHERE MOV.HOST_ID = ACC.HOST_ID
   AND MOV.ENTITY_ID = ACC.ENTITY_ID
   AND MOV.ACCOUNT_ID = ACC.ACCOUNT_ID
   AND MOV.CURRENCY_CODE = ACC.CURRENCY_CODE
   AND ACC.HOST_ID = $P{pHost_Id}
   AND ACC.ENTITY_ID = $P{pEntity_Id}
   AND ACC.ACCOUNT_CLASS = 'N'
   AND (   ACC.ACCOUNT_LEVEL = $P{pShow_Main}
        OR ACC.ACCOUNT_LEVEL = $P{pShow_Sub}
       )
   AND MOV.VALUE_DATE BETWEEN $P{pReport_DateFrom} AND $P{pReport_DateTo}
   AND ( ($P{pUseDataSource} = 'E' AND MOV.EXT_BAL_STATUS = 'I')
       OR ($P{pUseDataSource} = 'I' AND MOV.PREDICT_STATUS = 'I')
       )
 GROUP BY ACC.CURRENCY_CODE
 ORDER BY ACC.CURRENCY_CODE]]>
	</queryString>
	<field name="CURRENCY_CODE" class="java.lang.String"/>
	<field name="CREDIT" class="java.math.BigDecimal"/>
	<field name="DEBIT" class="java.math.BigDecimal"/>
	<field name="sum_turnover" class="java.math.BigDecimal"/>
	<field name="CR_Main_value" class="java.math.BigDecimal"/>
	<field name="DR_Main_value" class="java.math.BigDecimal"/>
	<field name="CR_Sub_value" class="java.math.BigDecimal"/>
	<field name="DR_Sub_value" class="java.math.BigDecimal"/>
	<field name="sum_main_value" class="java.math.BigDecimal"/>
	<field name="sum_sub_value" class="java.math.BigDecimal"/>
	<variable name="Test" class="java.math.BigDecimal"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="36" splitType="Prevent">
			<staticText>
				<reportElement uuid="2edc0d46-e88b-4e75-a5d7-2438d6d5a0bb" key="staticText-1" x="93" y="3" width="405" height="33" isPrintInFirstWholeBand="true" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="24"/>
				</textElement>
				<text><![CDATA[Turnover by Currency (Nostro) ]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="96" splitType="Stretch">
			<staticText>
				<reportElement uuid="2ceb22c0-02b0-4e78-8a57-0bde746fc861" key="staticText-3" x="35" y="6" width="43" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Entity ]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="e95a083d-9495-4632-8bdf-302780404cbf" key="textField-1" x="122" y="8" width="81" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="6611026d-2549-4324-ab00-07705630e4ee" key="textField-8" x="281" y="8" width="164" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$P{pEntity_Name}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="9488562f-0846-42b6-8cdd-ba9735d0d476" key="line-1" x="34" y="3" width="518" height="1"/>
			</line>
			<line>
				<reportElement uuid="a27870ab-892c-4866-85a8-4c7a439ea55e" key="line-2" x="34" y="26" width="518" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="b5a94d71-7b08-4973-b932-34ec2c62564d" key="staticText-5" x="35" y="62" width="82" height="17">
					<printWhenExpression><![CDATA[($F{CURRENCY_CODE}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Currency]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="ff2d53c1-4b80-4eda-808a-97ff1aced117" key="staticText-17" x="117" y="62" width="138" height="17">
					<printWhenExpression><![CDATA[($F{CURRENCY_CODE}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[                            Credit]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="a02f7057-df84-42e1-83a2-03fafea56f16" key="staticText-18" x="257" y="62" width="142" height="17">
					<printWhenExpression><![CDATA[($F{CURRENCY_CODE}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[                            Debit]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="926181e9-7de1-4b9f-bfa8-eca468fe79bb" key="staticText-19" x="401" y="62" width="149" height="17">
					<printWhenExpression><![CDATA[($F{CURRENCY_CODE}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10" isBold="true" isUnderline="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[                    Turnover]]></text>
			</staticText>
			<line>
				<reportElement uuid="b80e11e0-fc85-4ada-8bde-284883a8f155" key="line-2" x="35" y="50" width="518" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="d8379ddb-5f66-44ef-92f0-60485e2e6143" key="staticText-9" x="281" y="30" width="150" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Reporting Period]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="bf73d7ca-ae2e-4d18-a019-1331c5721366" key="staticText-3" x="36" y="30" width="120" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Source ]]></text>
			</staticText>
			<textField pattern="dd MMMMM yyyy" isBlankWhenNull="true">
				<reportElement uuid="17402be3-0371-42cd-a770-1c2ace728c8a" key="textField-9" x="399" y="31" width="75" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pReport_DateFrom})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="3edc8c7e-b84c-4816-8f76-5b4b520f081e" key="textField-8" x="122" y="32" width="180" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[$P{pDataSourceName}]]></textFieldExpression>
			</textField>
			<textField pattern="dd MMMMM yyyy" isBlankWhenNull="true">
				<reportElement uuid="dc10ef73-a3e8-4015-9d88-ddcd54d367bf" key="textField-9" x="484" y="31" width="75" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pReport_DateTo})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="dbdc55a1-6ad4-4e9e-b3f8-f4b1bbeb22b4" key="staticText-9" x="466" y="31" width="15" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[to]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="74" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="6b94cce0-bcb1-4951-8c83-206dcbbe2c2f" key="textField-4" x="35" y="6" width="82" height="19"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left">
					<font size="12" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CURRENCY_CODE}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="458e23f6-7368-4a4c-9012-29972263617e" key="textField-5" x="117" y="6" width="138" height="19"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{CREDIT}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{CREDIT}): new DecimalFormat("#,##0.00").format($F{CREDIT}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="1f8952da-9aaa-4845-9af5-6f03dca354eb" key="textField-6" x="257" y="6" width="142" height="21"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{DEBIT}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{DEBIT}): new DecimalFormat("#,##0.00").format($F{DEBIT}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="a8bc3b8c-702e-4c07-bfb2-68bcd0d651ed" key="textField-7" x="401" y="6" width="149" height="21"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{sum_turnover}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{sum_turnover}): new DecimalFormat("#,##0.00").format($F{sum_turnover}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="e2e8ab19-8c29-49a9-88bd-31cbf4b76f86" key="textField-10" x="117" y="27" width="138" height="20">
					<printWhenExpression><![CDATA[($P{pShow_Main} == "M") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{CR_Main_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{CR_Main_value}): new DecimalFormat("#,##0.00").format($F{CR_Main_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="62882c73-c620-4dd2-a757-345f21604fdb" key="textField-11" x="257" y="28" width="142" height="19">
					<printWhenExpression><![CDATA[($P{pShow_Main} == "M") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{DR_Main_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{DR_Main_value}): new DecimalFormat("#,##0.00").format($F{DR_Main_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="8fceafa0-db15-4875-8cac-925fc8488ebd" key="textField-12" x="401" y="28" width="149" height="19">
					<printWhenExpression><![CDATA[($P{pShow_Main} == "M") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{sum_main_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{sum_main_value}): new DecimalFormat("#,##0.00").format($F{sum_main_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="a2d45d6b-a794-4ce3-9e77-ed76b2d021ee" key="textField-13" x="117" y="47" width="138" height="19">
					<printWhenExpression><![CDATA[($P{pShow_Sub} == "S") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{CR_Sub_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{CR_Sub_value}): new DecimalFormat("#,##0.00").format($F{CR_Sub_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="c9ab19a1-7fdd-4e1f-9c4f-d31a0aeb1054" key="textField-14" x="257" y="47" width="142" height="19">
					<printWhenExpression><![CDATA[($P{pShow_Sub} == "S") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{DR_Sub_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{DR_Sub_value}): new DecimalFormat("#,##0.00").format($F{DR_Sub_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="5e5be6ba-2c55-49b7-a9f0-6b90e5c51456" key="textField-15" x="401" y="47" width="149" height="19">
					<printWhenExpression><![CDATA[($P{pShow_Sub} == "S") ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($F{sum_sub_value}!=null) ?(($P{pCurrencyPattern}.equals("currencyPat1")) ? new DecimalFormat("#,##0.00").format($F{sum_sub_value}): new DecimalFormat("#,##0.00").format($F{sum_sub_value}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="23a55c66-b920-4b45-8045-b1f47c09556d" key="staticText-14" x="89" y="27" width="28" height="20">
					<printWhenExpression><![CDATA[(($P{pShow_Main}=="M") && ($F{CR_Main_value}!=null))?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Main]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="d850f425-a980-4751-80ae-db6ced20d65c" key="staticText-15" x="89" y="48" width="28" height="18">
					<printWhenExpression><![CDATA[(($P{pShow_Sub} == "S") && ($F{CR_Sub_value}!=null))?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sub]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="5cda684b-0ce3-4805-8bfa-9134bbdb56b9" key="staticText-12" stretchType="RelativeToBandHeight" x="209" y="33" width="151" height="18">
					<printWhenExpression><![CDATA[($F{CURRENCY_CODE}==null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12"/>
				</textElement>
				<text><![CDATA[     No records to display]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="37" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="345902d5-e784-4994-9390-9d878e6cbb57" key="textField-16" x="366" y="15" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement uuid="af0537fe-15ae-43fb-b964-facb03d53edb" key="textField-17" x="534" y="15" width="37" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="26dc1c08-f18f-40ba-9276-18cbcd6f479d" key="textField-18" x="35" y="13" width="100" height="21" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement uuid="1a677644-a53d-4f1d-954a-728e41993fdf" key="staticText-16" positionType="FixRelativeToBottom" x="217" y="31" width="157" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="14"/>
				</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
