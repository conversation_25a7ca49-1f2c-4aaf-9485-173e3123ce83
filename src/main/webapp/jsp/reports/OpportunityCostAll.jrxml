<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="OpportunityCostAll" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="782" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="42330de4-823c-4660-b5a3-e9d64f9c463a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="TEST" forecolor="#000000" isBold="false" isItalic="false">
		<conditionalStyle>
			<conditionExpression><![CDATA[java.lang.Boolean.valueOf($F{MAIN_OR_SUB}.equalsIgnoreCase("M"))]]></conditionExpression>
			<style forecolor="#000000" isBold="true" isItalic="false" isUnderline="true"/>
		</conditionalStyle>
	</style>
	<parameter name="pHOST" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pENTITY" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="pREPORT_DATE" class="java.util.Date"/>
	<parameter name="pTHRESHOLD" class="java.lang.Double"/>
	<parameter name="pCurrencyPattern" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pREP_CCY" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[WITH PARMS
     AS (SELECT $P{pREPORT_DATE} PREPORT_DATE,
                $P{pHOST} PHOST,
                $P{pENTITY} PENTITY,
                $P{pTHRESHOLD} PTHRESHOLD
           FROM DUAL)
--SELECT * FROM PARMS;
,
     ACCS
     AS (SELECT P_ACCOUNT.HOST_ID,
                P_ACCOUNT.ENTITY_ID,
                P_ACCOUNT.ACCOUNT_ID,
                P_ACCOUNT.ACCOUNT_CLASS,
                P_ACCOUNT.ACCOUNT_LEVEL,
                P_ACCOUNT.ACCOUNT_NAME,
                P_ACCOUNT.ACCOUNT_TYPE,
                P_ACCOUNT.CURRENCY_CODE,
                P_ACCOUNT.MAIN_ACCOUNT_ID,
                PKG_NON_WORKDAY.GETPREVBUSINESSDATE (
                                 'OPPORTUNITY_COST_REPORT',
                                  $P{pREPORT_DATE},
                                  $P{pENTITY},
                                  P_ACCOUNT.ACCOUNT_ID,
                                  P_ACCOUNT.CURRENCY_CODE) PREV_BUSINESS_DATE
           FROM P_ACCOUNT
          WHERE P_ACCOUNT.HOST_ID = $P{pHOST}
            AND P_ACCOUNT.ENTITY_ID = $P{pENTITY}
            AND P_ACCOUNT.ACCOUNT_ID != '*'
            AND P_ACCOUNT.ACCOUNT_CLASS = 'N'
            AND P_ACCOUNT.ACCOUNT_TYPE IN ('C', 'U')
            AND PKG_NON_WORKDAY.ISDATEAHOLIDAY ('A',
                                                $P{pREPORT_DATE},
                                                $P{pENTITY},
                                                P_ACCOUNT.ACCOUNT_ID,
                                                NULL) = 'N'
         )
--SELECT * FROM ACCS;
,
     ACC_CCY_EXCH_RATE           -- exchange rate applicable for account's ccy
     AS (SELECT S_CURRENCY_EXCHANGE_RATE.HOST_ID,
                S_CURRENCY_EXCHANGE_RATE.ENTITY_ID,
                S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE,
                ACCS.ACCOUNT_ID,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE
           FROM PARMS
                CROSS JOIN ACCS
                INNER JOIN S_CURRENCY_EXCHANGE_RATE
                   ON (    S_CURRENCY_EXCHANGE_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_EXCHANGE_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE =
                              (SELECT MAX (S.EXCHANGE_RATE_DATE)
                                 FROM S_CURRENCY_EXCHANGE_RATE S
                                WHERE S.HOST_ID = PARMS.PHOST
                                  AND S.ENTITY_ID = PARMS.PENTITY
                                  AND S.CURRENCY_CODE = ACCS.CURRENCY_CODE
                                  AND S.EXCHANGE_RATE_DATE <= ACCS.PREV_BUSINESS_DATE)
                       )
         )
--SELECT * FROM ACC_CCY_EXCH_RATE;
,
     EREP_CCY_EXCH_RATE -- exchange rate applicable for entity's reporting ccy
     AS (SELECT S_CURRENCY_EXCHANGE_RATE.HOST_ID,
                S_CURRENCY_EXCHANGE_RATE.ENTITY_ID,
                S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE,
                ACCS.ACCOUNT_ID,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE,
                S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE
           FROM PARMS
                CROSS JOIN ACCS
                INNER JOIN S_ENTITY
                   ON (    S_ENTITY.HOST_ID = PARMS.PHOST
                       AND S_ENTITY.ENTITY_ID = PARMS.PENTITY)
                INNER JOIN S_CURRENCY_EXCHANGE_RATE
                   ON (    S_CURRENCY_EXCHANGE_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_EXCHANGE_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_EXCHANGE_RATE.CURRENCY_CODE = S_ENTITY.REPORT_CURRENCY
                       AND S_CURRENCY_EXCHANGE_RATE.EXCHANGE_RATE_DATE =
                              (SELECT MAX (S.EXCHANGE_RATE_DATE)
                                 FROM S_CURRENCY_EXCHANGE_RATE S
                                WHERE     S.HOST_ID = PARMS.PHOST
                                      AND S.ENTITY_ID = PARMS.PENTITY
                                      AND S.CURRENCY_CODE =
                                             S_ENTITY.REPORT_CURRENCY
                                      AND S.EXCHANGE_RATE_DATE <=
                                             ACCS.PREV_BUSINESS_DATE)
                       )
         )
--SELECT * FROM EREP_CCY_EXCH_RATE;
,
     ACCDATA
     AS (SELECT ACCS.HOST_ID,
                ACCS.ENTITY_ID,
                ACCS.ACCOUNT_ID,
                ACCS.ACCOUNT_NAME,
                ACCS.CURRENCY_CODE,
                ACCS.ACCOUNT_LEVEL,
                ACCS.PREV_BUSINESS_DATE,
                ACCS.MAIN_ACCOUNT_ID,
                P_BALANCE.BALANCE_DATE,
                P_BALANCE.SUPPLIED_EXTERNAL_BALANCE,
                P_BALANCE.USER_NOTES,
                P_BALANCE.REASON_CODE,
                P_REASON_CODES.DESCRIPTION,
                P_ACCOUNT_INTEREST_RATE.INTEREST_RATE_DATE  ACC_INT_RATE_DATE,
                P_ACCOUNT_INTEREST_RATE.CREDIT_RATE         ACC_INT_CREDIT_RATE,
                P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE      ACC_INT_DEBIT_RATE,
                S_CURRENCY.INTEREST_BASIS,
                S_CURRENCY_INTEREST_RATE.INTEREST_RATE      CCY_INT_RATE,
                NVL(ACC_CCY_EXCH_RATE.EXCHANGE_RATE, 1)     ACC_CCY_EX_RATE,
                NVL(EREP_CCY_EXCH_RATE.EXCHANGE_RATE, 1)    EREP_CCY_EX_RATE,

                S_ENTITY.EXCHANGE_RATE_FORMAT,
                S_ENTITY.DOMESTIC_CURRENCY,
                S_ENTITY.REPORT_CURRENCY,

                PREPORT_DATE - ACCS.PREV_BUSINESS_DATE DIFF_IN_DAYS,

                CASE
                   WHEN SIGN (S_CURRENCY_INTEREST_RATE.INTEREST_RATE) = -1
                   THEN
                      --return effective rate of margin rate when mkt rate is -ve
                      P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                   ELSE
                      -- else return sum of mkt and margin (zero if that sum is -ve)
                      CASE
                            WHEN SIGN (  P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                                       + S_CURRENCY_INTEREST_RATE.INTEREST_RATE
                                       ) = -1
                            THEN 0
                            ELSE (  P_ACCOUNT_INTEREST_RATE.CREDIT_RATE
                                  + S_CURRENCY_INTEREST_RATE.INTEREST_RATE
                                  )
                         END
                END CR_RATE,

                CASE
                   WHEN SIGN (S_CURRENCY_INTEREST_RATE.INTEREST_RATE) = -1
                   THEN
                      P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE
                   ELSE
                      (  P_ACCOUNT_INTEREST_RATE.OVERDRAFT_RATE
                       + S_CURRENCY_INTEREST_RATE.INTEREST_RATE)
                END DB_RATE                                   -- rate for DEBIT balance
           FROM PARMS
                INNER JOIN S_ENTITY
                   ON (    S_ENTITY.HOST_ID = PARMS.PHOST
                       AND S_ENTITY.ENTITY_ID = PARMS.PENTITY)
                CROSS JOIN ACCS
                INNER JOIN S_CURRENCY
                   ON (    S_CURRENCY.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY.CURRENCY_CODE = ACCS.CURRENCY_CODE)
                INNER JOIN P_BALANCE
                   ON (    ACCS.HOST_ID = P_BALANCE.HOST_ID
                       AND ACCS.ENTITY_ID = P_BALANCE.ENTITY_ID
                       AND ACCS.ACCOUNT_ID = P_BALANCE.BAL_TYPE_ID
                       AND BALANCE_DATE =
                              (SELECT MAX (D.BALANCE_DATE) -- Latest start balance date
                                 FROM P_BALANCE D
                                WHERE D.HOST_ID = PARMS.PHOST
                                  AND D.ENTITY_ID = PARMS.PENTITY
                                  AND D.BAL_TYPE_ID = ACCS.ACCOUNT_ID
                                  AND D.SUPPLIED_EXTERNAL_BALANCE IS NOT NULL
                                  AND D.BALANCE_DATE <= (ACCS.PREV_BUSINESS_DATE + 1)
                               )
                       )
                INNER JOIN P_ACCOUNT_INTEREST_RATE -- Latest ACCOUNT interest rate
                   ON (    ACCS.HOST_ID = P_ACCOUNT_INTEREST_RATE.HOST_ID
                       AND ACCS.ENTITY_ID = P_ACCOUNT_INTEREST_RATE.ENTITY_ID
                       AND ACCS.ACCOUNT_ID = P_ACCOUNT_INTEREST_RATE.ACCOUNT_ID
                       AND P_ACCOUNT_INTEREST_RATE.INTEREST_RATE_DATE =
                              (SELECT MAX (AI.INTEREST_RATE_DATE)
                                 FROM P_ACCOUNT_INTEREST_RATE AI
                                WHERE ACCS.HOST_ID = AI.HOST_ID
                                  AND ACCS.ENTITY_ID = AI.ENTITY_ID
                                  AND ACCS.ACCOUNT_ID = AI.ACCOUNT_ID
                                  AND (AI.INTEREST_RATE_DATE) <= ACCS.PREV_BUSINESS_DATE)
                       )
                INNER JOIN S_CURRENCY_INTEREST_RATE -- Latest CURRENCY interest rate
                   ON (    S_CURRENCY_INTEREST_RATE.HOST_ID = PARMS.PHOST
                       AND S_CURRENCY_INTEREST_RATE.ENTITY_ID = PARMS.PENTITY
                       AND S_CURRENCY_INTEREST_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND S_CURRENCY_INTEREST_RATE.INTEREST_RATE_DATE =
                              (SELECT MAX (I.INTEREST_RATE_DATE)
                                 FROM S_CURRENCY_INTEREST_RATE I
                                WHERE I.HOST_ID = PARMS.PHOST
                                  AND I.ENTITY_ID = PARMS.PENTITY
                                  AND I.CURRENCY_CODE = ACCS.CURRENCY_CODE
                                  AND I.INTEREST_RATE_DATE <=ACCS.PREV_BUSINESS_DATE)
                       )
                LEFT OUTER JOIN ACC_CCY_EXCH_RATE
                   ON (    ACC_CCY_EXCH_RATE.HOST_ID = PARMS.PHOST
                       AND ACC_CCY_EXCH_RATE.ENTITY_ID = PARMS.PENTITY
                       AND ACC_CCY_EXCH_RATE.CURRENCY_CODE = ACCS.CURRENCY_CODE
                       AND ACC_CCY_EXCH_RATE.ACCOUNT_ID = ACCS.ACCOUNT_ID)
                LEFT OUTER JOIN EREP_CCY_EXCH_RATE
                   ON (    EREP_CCY_EXCH_RATE.HOST_ID = PARMS.PHOST
                       AND EREP_CCY_EXCH_RATE.ENTITY_ID = PARMS.PENTITY
                       AND EREP_CCY_EXCH_RATE.CURRENCY_CODE = S_ENTITY.REPORT_CURRENCY
                       AND EREP_CCY_EXCH_RATE.ACCOUNT_ID = ACCS.ACCOUNT_ID)
                LEFT OUTER JOIN P_REASON_CODES
                   ON (    P_REASON_CODES.HOST_ID = PARMS.PHOST
                       AND P_REASON_CODES.ENTITY_ID = PARMS.PENTITY
                       AND P_REASON_CODES.REASON_CODE = P_BALANCE.REASON_CODE)
         )
--SELECT * FROM ACCDATA;
,
FULL_CALCS
AS(SELECT A.CURRENCY_CODE                      AS CCY,
          A.ACCOUNT_LEVEL                      AS MAIN_OR_SUB,
          A.ACCOUNT_ID                         AS ACCOUNT_ID,
          A.ACCOUNT_NAME                       AS ACCOUNT_NAME,
          A.MAIN_ACCOUNT_ID                    AS MAIN_ACCOUNT_ID,
          NVL(A.SUPPLIED_EXTERNAL_BALANCE, 0) AS START_BALANCE,

          -- ACCOUNT RATE use cedit/credit rate according to balance sign
          -- If -ve balance and mkt rate also -ve, then use value of 0
          CASE
             WHEN SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1
             THEN A.DB_RATE
             ELSE CASE WHEN SIGN (A.CR_RATE) = -1
                       THEN 0
                       ELSE A.CR_RATE
                  END
          END AS ACCOUNT_RATE,

          CCY_INT_RATE AS MARKET_RATE,

          -- if market rate negative and balance is credit then interest amt = 0
          CASE
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (NVL(A.SUPPLIED_EXTERNAL_BALANCE, 0)) = 1)
             THEN 0
             ELSE NVL( (  A.SUPPLIED_EXTERNAL_BALANCE -- market interest rate for currency (Credit rate)
                        * CASE
                             WHEN SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1
                             THEN A.DB_RATE
                             ELSE A.CR_RATE
                          END
                        * 0.01 / A.INTEREST_BASIS
                        * A.DIFF_IN_DAYS)
                      , 0  )
          END AS ACC_INTEREST_AMOUNT,

          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END AS  OPP_MKT_MINUS_ACC, -- Market rate interest minus Account interest

          ACC_CCY_EX_RATE AS EX_RATE, -- Exchange rate between acc ccy and domestic ccy

          -- Domestic equivalent = oppcost applied with exch rate of dom ccy
          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END
          *
          CASE EXCHANGE_RATE_FORMAT
             WHEN '2'
             THEN -- FORMAT CCY/DOM
                  1 / ACC_CCY_EX_RATE
             ELSE -- FORMAT DOM/CCY
                  ACC_CCY_EX_RATE
          END
          AS DOM_EQUIV,
          DOMESTIC_CURRENCY DOM_CURR,

          -- REP_CCY_EQUIV. CALC USING OPP_MKT_MINUS_ACC x SUITABLE RATE
                      -- Domestic equiv calcn same as oppcost x exch rate
          CASE
             -- if market rate negative and balance is credit then opp cost = -(mkt rate)
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = 1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.CCY_INT_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             -- if market rate negative and balance is debit then opp cost = margin rate
             WHEN (    SIGN (A.CCY_INT_RATE) = -1
                   AND SIGN (A.SUPPLIED_EXTERNAL_BALANCE) = -1)
             THEN NVL( ( -(  A.SUPPLIED_EXTERNAL_BALANCE
                           * A.DB_RATE
                           * 0.01 / A.INTEREST_BASIS)
                           * A.DIFF_IN_DAYS)
                      , 0)
             ELSE NVL( ( (  A.SUPPLIED_EXTERNAL_BALANCE
                          * CCY_INT_RATE
                          * 0.01 / A.INTEREST_BASIS)

                        -(  A.SUPPLIED_EXTERNAL_BALANCE
                          * CASE SIGN (A.SUPPLIED_EXTERNAL_BALANCE)
                               WHEN -1
                               THEN A.DB_RATE
                               ELSE A.CR_RATE
                            END
                        * 0.01 / A.INTEREST_BASIS))
                      * A.DIFF_IN_DAYS
                      , 0)
          END
          *
          CASE EXCHANGE_RATE_FORMAT
             WHEN '2'
             THEN -- FORMAT CCY/DOM
                  EREP_CCY_EX_RATE / ACC_CCY_EX_RATE
             ELSE -- FORMAT DOM/CCY
                  ACC_CCY_EX_RATE / EREP_CCY_EX_RATE
          END
          AS REP_CCY_EQUIV,

          REPORT_CURRENCY REP_CCY,
          EREP_CCY_EX_RATE AS REP_CCY_EX_RATE, -- Exchange rate between reporting ccy and domestic ccy

          REASON_CODE,
          DESCRIPTION REASON_DESCRIPTION,
          USER_NOTES
     FROM ACCDATA A
   )
SELECT CCY,
       MAIN_OR_SUB,
       ACCOUNT_ID,
       ACCOUNT_NAME,
       START_BALANCE,
       ACCOUNT_RATE,
       MARKET_RATE,
       ACC_INTEREST_AMOUNT,
       OPP_MKT_MINUS_ACC, -- Market rate interest minus Account interest
       EX_RATE, -- Exchange rate between acc ccy and domestic ccy
       DOM_EQUIV,
       DOM_CURR,
       REP_CCY_EQUIV,
       REP_CCY,
       REP_CCY_EX_RATE, -- Exchange rate between reporting ccy and domestic ccy
       REASON_CODE,
       REASON_DESCRIPTION,
       USER_NOTES
  FROM PARMS
       CROSS JOIN FULL_CALCS
WHERE (PARMS.PTHRESHOLD > 0 AND REP_CCY_EQUIV >= $P{pTHRESHOLD})
   OR (PARMS.PTHRESHOLD = 0 AND REP_CCY_EQUIV > 0)
ORDER BY FULL_CALCS.CCY,
      NVL (FULL_CALCS.main_account_id, FULL_CALCS.account_id), --GRP MAIN A/C
      FULL_CALCS.MAIN_OR_SUB,   -- ORDER MAIN ACC FIRST
      FULL_CALCS.account_id       -- ORDER SUB ACCS]]>
	</queryString>
	<field name="CCY" class="java.lang.String"/>
	<field name="MAIN_OR_SUB" class="java.lang.String"/>
	<field name="ACCOUNT_ID" class="java.lang.String"/>
	<field name="ACCOUNT_NAME" class="java.lang.String"/>
	<field name="START_BALANCE" class="java.math.BigDecimal"/>
	<field name="ACCOUNT_RATE" class="java.math.BigDecimal"/>
	<field name="MARKET_RATE" class="java.math.BigDecimal"/>
	<field name="ACC_INTEREST_AMOUNT" class="java.math.BigDecimal"/>
	<field name="OPP_MKT_MINUS_ACC" class="java.math.BigDecimal"/>
	<field name="EX_RATE" class="java.math.BigDecimal"/>
	<field name="DOM_EQUIV" class="java.math.BigDecimal"/>
	<field name="DOM_CURR" class="java.lang.String"/>
	<field name="REASON_CODE" class="java.lang.String"/>
	<field name="REASON_DESCRIPTION" class="java.lang.String"/>
	<field name="USER_NOTES" class="java.lang.String"/>
	<field name="REP_CCY_EQUIV" class="java.lang.Double"/>
	<field name="REP_CCY" class="java.lang.String"/>
	<variable name="SUM_OPP_DOM" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[java.lang.Double.valueOf($F{DOM_EQUIV}.doubleValue())]]></variableExpression>
	</variable>
	<variable name="SUM_OPP_REP" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[java.lang.Double.valueOf($F{REP_CCY_EQUIV}.doubleValue())]]></variableExpression>
	</variable>
	<group name="CURRENCY">
		<groupExpression><![CDATA[$F{CCY}]]></groupExpression>
		<groupHeader>
			<band height="37" splitType="Stretch">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement uuid="ea0e31eb-5605-46e8-a763-68b8f7da5fda" key="textField-7" x="2" y="9" width="98" height="23" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="18"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{CCY}]]></textFieldExpression>
				</textField>
				<line direction="BottomUp">
					<reportElement uuid="e201934a-a412-44db-a836-c70c85d16c73" key="line-6" x="0" y="5" width="782" height="1" forecolor="#003333">
						<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="1.0" lineStyle="Dashed"/>
					</graphicElement>
				</line>
				<line direction="BottomUp">
					<reportElement uuid="d0f029df-410c-41fd-a142-4da5653682b7" key="line-8" x="0" y="32" width="782" height="1">
						<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="1.0" lineStyle="Dashed"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="21" splitType="Stretch">
				<staticText>
					<reportElement uuid="688f0dc8-e2b1-4506-8aea-8158d321ff08" key="staticText-19" x="245" y="0" width="167" height="18">
						<printWhenExpression><![CDATA[($F{CCY}==null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font size="12"/>
					</textElement>
					<text><![CDATA[No records to display]]></text>
				</staticText>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="82" splitType="Stretch">
			<staticText>
				<reportElement uuid="bd9aab9a-ab2d-47ca-9367-6167fae9919d" key="staticText-1" x="170" y="6" width="412" height="40" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="24"/>
				</textElement>
				<text><![CDATA[Opportunity Cost Report]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="95aa975c-0345-48bf-bd64-7ee3f72a997c" key="textField-2" x="55" y="56" width="100" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pENTITY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="91e4c4ac-50b0-41d8-b0e9-7f913bf60b5d" key="staticText-3" x="8" y="56" width="45" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Entity:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="7c3760fb-288a-4b63-8322-a926af62208f" key="staticText-4" x="542" y="56" width="91" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Report Date:]]></text>
			</staticText>
			<textField pattern="dd MMMMM yyyy" isBlankWhenNull="false">
				<reportElement uuid="d04e144c-7b30-4cb3-8c6a-6379274c3064" key="textField-3" x="636" y="56" width="127" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{pREPORT_DATE}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="0ecb149a-c757-47ac-9089-15764c3a4666" key="line-1" x="2" y="50" width="782" height="1"/>
			</line>
			<staticText>
				<reportElement uuid="a37cc2c9-109d-4a82-a477-b4123df1af85" key="staticText-16" x="162" y="56" width="95" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Threshold]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement uuid="a56db37b-24e4-4559-979d-118c4c7514fd" key="textField-9" x="282" y="56" width="200" height="23">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}):
 new DecimalFormat("#,##0.00").format($P{pTHRESHOLD}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="90c5d841-caca-4c70-8266-96cf15afedd2" key="textField-7" x="235" y="56" width="46" height="23" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<printWhenExpression><![CDATA[($P{pREP_CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[java.lang.String.valueOf("(").concat(($P{pREP_CCY})).concat(") :")]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="50" splitType="Stretch">
			<staticText>
				<reportElement uuid="041b4e88-8c43-49b8-8938-2436446599a6" key="staticText-5" positionType="Float" x="7" y="6" width="93" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Account]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="4f645430-5776-4065-9c44-947bbce81ddc" key="staticText-6" positionType="Float" x="113" y="6" width="111" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[  SOD Balance]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="aabc057d-d0ec-47ea-8367-c41639865e5f" key="staticText-7" positionType="Float" x="233" y="6" width="48" height="32" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Interest Rate %]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="0e43faba-d884-48b6-8e5e-008b9cde04d1" key="staticText-8" positionType="Float" x="288" y="6" width="44" height="32" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Market Rate %]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="4fb6a3a7-117a-4aeb-b65d-c5750e8e5657" key="staticText-9" positionType="Float" x="391" y="6" width="48" height="32" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Interest Amount]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="ae226056-e010-436f-b8ef-d1a0171d1fb9" key="staticText-10" positionType="Float" x="488" y="6" width="75" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Opportunity]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="1ecf21d9-9605-416a-967d-c8ae008e2d17" key="staticText-11" positionType="Float" x="588" y="6" width="56" height="27" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Exchange Rate]]></text>
			</staticText>
			<line direction="BottomUp">
				<reportElement uuid="938131dd-3f90-4c21-90d3-65ac41fffa3b" key="line-2" x="2" y="3" width="782" height="1"/>
			</line>
			<line direction="BottomUp">
				<reportElement uuid="0fa3637a-c93f-4a5a-97ce-ff25c9186e33" key="line-3" x="0" y="43" width="782" height="1">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
			</line>
			<staticText>
				<reportElement uuid="4f0591eb-d06f-4063-9912-77fa9195b930" key="staticText-12" positionType="Float" x="667" y="6" width="101" height="32" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Equiv Opportunity]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="38" splitType="Prevent">
			<textField pattern="###0.0000" isBlankWhenNull="true">
				<reportElement uuid="e77ef090-eaec-4917-8381-cd5a3db32829" key="textField" x="288" y="0" width="44" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{MARKET_RATE}):
 new DecimalFormat("#,##0.00").format($F{MARKET_RATE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="99f91fc4-0b3e-4673-9729-1f4d05d28bc0" key="textField" x="2" y="19" width="199" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ACCOUNT_NAME}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="0b0a3ee6-a585-49ec-b06f-e39de0d0b1c7" key="textField" x="113" y="0" width="111" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{START_BALANCE}):
 new DecimalFormat("#,##0.00").format($F{START_BALANCE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="###0.0000" isBlankWhenNull="true">
				<reportElement uuid="ee654f3b-69b7-4a89-a034-7086b5c2a743" key="textField" x="237" y="0" width="50" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{ACCOUNT_RATE}):
 new DecimalFormat("#,##0.00").format($F{ACCOUNT_RATE}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement uuid="064fd518-2d95-4bec-80b6-d7fed3237a44" key="textField" x="343" y="0" width="96" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{ACC_INTEREST_AMOUNT}):
 new DecimalFormat("#,##0.00").format($F{ACC_INTEREST_AMOUNT}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00" isBlankWhenNull="true">
				<reportElement uuid="08abaa23-0759-49c5-afad-3669fc7d656f" key="textField" x="452" y="0" width="111" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{OPP_MKT_MINUS_ACC}):
 new DecimalFormat("#,##0.00").format($F{OPP_MKT_MINUS_ACC}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.0000" isBlankWhenNull="true">
				<reportElement uuid="d8828e15-e8e0-45f0-9b11-dc2131ca6437" key="textField" x="572" y="0" width="57" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.000").format( new java.math.BigDecimal($F{EX_RATE}.toString()).divide(new java.math.BigDecimal("1"),java.math.RoundingMode.CEILING)):
 new DecimalFormat("#,##0.000").format( new java.math.BigDecimal($F{EX_RATE}.toString()).divide(new java.math.BigDecimal("1"),java.math.RoundingMode.CEILING)).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="faffe761-7567-45de-9931-64cdcf6ca2f8" key="textField" x="634" y="0" width="100" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{DOM_EQUIV}):
 new DecimalFormat("#,##0.00").format($F{DOM_EQUIV}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="4c21aec7-57fb-4915-b5ef-25be83517711" key="textField" x="736" y="0" width="35" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement/>
				<textFieldExpression><![CDATA[$F{DOM_CURR}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="1473d6cd-b962-4eb1-92c7-4a9326b4c445" key="textField" style="TEST" x="3" y="0" width="100" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ACCOUNT_ID}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="a26be6d6-e7af-4697-9bcd-fee9623ce5cc" key="textField" x="634" y="19" width="100" height="18">
					<printWhenExpression><![CDATA[($F{DOM_CURR}).equalsIgnoreCase($F{REP_CCY}) ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($F{REP_CCY_EQUIV}):
 new DecimalFormat("#,##0.00").format($F{REP_CCY_EQUIV}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="f24a263f-0670-4ef1-bd7b-599b97d6466c" key="textField" x="736" y="19" width="35" height="18">
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[($F{DOM_CURR}).equalsIgnoreCase($F{REP_CCY}) ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REP_CCY}]]></textFieldExpression>
			</textField>
		</band>
		<band height="27">
			<printWhenExpression><![CDATA[($F{REASON_CODE}!= null)]]></printWhenExpression>
			<textField isBlankWhenNull="true">
				<reportElement uuid="d56f587b-2a78-4fb7-aad2-dd3fa444aab3" key="textField-11" x="113" y="1" width="70" height="18" forecolor="#00009C">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{REASON_CODE}!= null)? "Reason       :":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="a570e3f5-5f96-4998-adff-24bae9dda06d" key="textField-8" x="183" y="1" width="585" height="18" forecolor="#00009C">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REASON_DESCRIPTION}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="535dc37b-92eb-4bc1-802b-2d4be5eca007" key="textField" mode="Transparent" x="180" y="20" width="350" height="6" isPrintInFirstWholeBand="true" forecolor="#00009C">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{USER_NOTES}!= null)? $F{USER_NOTES}:""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="657f3d3d-b3ac-43f3-a1f4-2d8e3c6a03dc" key="textField-10" x="113" y="20" width="66" height="2" forecolor="#00009C">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<textElement>
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{USER_NOTES}!= null)? "User Notes :":""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="31" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="3046a4f9-4396-48ae-abcb-5059930abbed" key="textField-4" x="2" y="5" width="100" height="21" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement uuid="3db7b697-e819-4db9-ae9e-d73a1dbcb195" key="textField-5" x="567" y="8" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement uuid="d3b65ada-ef37-424e-a9e5-f075ddc9c8b6" key="textField-6" x="735" y="8" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="113" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="14b51c09-2590-44c1-8181-c256472f73c6" key="textField" x="599" y="6" width="135" height="18">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($V{SUM_OPP_DOM}):
 new DecimalFormat("#,##0.00").format($V{SUM_OPP_DOM}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="38842b1e-a79e-4874-b088-c245ed85e972" key="staticText-17" positionType="Float" x="558" y="6" width="48" height="32" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[($F{CCY}!=null) ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top">
					<font size="10"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="26cfa658-7054-4bea-88f0-c65f545c58b5" key="staticText-18" x="300" y="89" width="154" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="14"/>
				</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement uuid="7b592853-b804-4258-8f60-a3f4019bfe30" key="textField-12" mode="Opaque" x="736" y="6" width="35" height="18"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement/>
				<textFieldExpression><![CDATA[$F{DOM_CURR}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement uuid="e047aea3-72d6-4c5a-b64f-c5b39409ede1" key="textField" x="599" y="25" width="135" height="18">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<printWhenExpression><![CDATA[($F{DOM_CURR}).equalsIgnoreCase($F{REP_CCY}) ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[($P{pCurrencyPattern} != null)?(
($P{pCurrencyPattern}.equals("currencyPat1")) ?
 new DecimalFormat("#,##0.00").format($V{SUM_OPP_REP}):
 new DecimalFormat("#,##0.00").format($V{SUM_OPP_REP}).replaceAll("\\.", "^").replaceAll(",", ".").replaceAll("\\^", ",")):""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="0dbaa838-c940-4c3b-ab1e-6432a4dda9f2" key="textField-12" mode="Opaque" x="736" y="25" width="35" height="18">
					<printWhenExpression><![CDATA[($F{DOM_CURR}).equalsIgnoreCase($F{REP_CCY}) ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{REP_CCY}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
