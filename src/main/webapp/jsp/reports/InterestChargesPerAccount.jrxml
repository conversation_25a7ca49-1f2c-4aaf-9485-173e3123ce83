<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!-- xml namespace changed,deprecated 'issplittype' attribute replaced with 'splitType' attribute for Mantis 2035 by <PERSON> on 18-Sep-2012 -->
<jasperReport 
  xmlns="http://jasperreports.sourceforge.net/jasperreports"
		 name="InterestChargesPerAccount"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="770"
		 pageHeight="595"
		 columnWidth="696"
		 columnSpacing="0"
		 leftMargin="22"
		 rightMargin="22"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="pHost_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pEntity_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pAccount_Name" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pAccount_Id" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pStart_Date" isForPrompting="true" class="java.sql.Date"/>
	<parameter name="pEnd_Date" isForPrompting="true" class="java.sql.Date"/>
	<parameter name="pCurrency_Code" isForPrompting="true" class="java.lang.String"/>
	<parameter name="pCurrency_Name" isForPrompting="false" class="java.lang.String"/>
	<parameter name="pDay_Diff" isForPrompting="false" class="java.lang.Long"/>
	<parameter name="pdateFormat" class="java.lang.String"/>
	<queryString><![CDATA[WITH bal_dt AS
     (SELECT $P{pHost_Id} AS host_id, $P{pEntity_Id} AS entity_id,
             $P{pAccount_Id} AS bal_type_id,
             ($P{pStart_Date} + lvl) AS balance_date,
             ($P{pStart_Date}- 1) + lvl AS disp_balance_date
        FROM (SELECT LEVEL lvl FROM dual CONNECT BY LEVEL <= $P{pDay_Diff})
       WHERE ($P{pStart_Date} - 1) + lvl <= $P{pEnd_Date}),
     bal AS
     (SELECT d.host_id, d.entity_id, d.bal_type_id, d.balance_date,
             b.balance_type, NVL (b.supplied_external_balance, 0),
             d.disp_balance_date
        FROM p_balance b, bal_dt d
       WHERE b.host_id(+) = d.host_id
         AND b.entity_id(+) = d.entity_id
         AND b.bal_type_id(+) = d.bal_type_id
         AND b.balance_date(+) = d.balance_date
         AND 0 <>
                (SELECT COUNT (NVL (l.supplied_external_balance, 0))
                   FROM p_balance l
                  WHERE l.balance_date <= $P{pEnd_Date}
                    AND l.host_id = d.host_id
                    AND l.entity_id = d.entity_id
                    AND l.bal_type_id = d.bal_type_id))
SELECT   TO_CHAR (ROWNUM) DAY,
         TO_CHAR (disp_balance_date, $P{pdateFormat}) balance_date,
         bal_type_id ACCOUNT,
         fn_get_bal (bal.host_id,
                     bal.entity_id,
                     acc.currency_code,
                     bal.balance_date,
                     bal.bal_type_id,
                     bal.balance_type
                    ) supplied_external_balance,
         (  accrate.overdraft_rate
          + fn_get_curr_interest_rate (bal.host_id,
                                       bal.entity_id,
                                       acc.currency_code,
                                       bal.disp_balance_date
                                      )
         ) dr,
         DECODE (SIGN (  accrate.credit_rate
                       + fn_get_curr_interest_rate (bal.host_id,
                                                    bal.entity_id,
                                                    acc.currency_code,
                                                    bal.disp_balance_date
                                                   )
                      ),
                 -1, 0,
                 (  accrate.credit_rate
                  + fn_get_curr_interest_rate (bal.host_id,
                                               bal.entity_id,
                                               acc.currency_code,
                                               bal.disp_balance_date
                                              )
                 )
                ) cr,
         
         -- Number of days the balance remained unchanged
         1 number_of_days,
         
         -- Debit interest = balance * DR interest rate / 100 * NoOfDays / 360
         NVL(DECODE
            (SIGN (fn_get_bal (bal.host_id,
                               bal.entity_id,
                               acc.currency_code,
                               bal.balance_date,
                               bal.bal_type_id,
                               bal.balance_type
                              )
                  ),
             -1, ABS (fn_get_bal (bal.host_id,
                                  bal.entity_id,
                                  acc.currency_code,
                                  bal.balance_date,
                                  bal.bal_type_id,
                                  bal.balance_type
                                 )
                     )
              * (  accrate.overdraft_rate
                 + fn_get_curr_interest_rate (bal.host_id,
                                              bal.entity_id,
                                              acc.currency_code,
                                              bal.disp_balance_date
                                             )
                )
              / 100
              / cur.interest_basis,
             NULL
            ),0) debit_interest_rate,
         
         -- Credit interest = balance * CR interest rate / 100 * NoOfDays / 360
         NVL(DECODE
            (SIGN (fn_get_bal (bal.host_id,
                               bal.entity_id,
                               acc.currency_code,
                               bal.balance_date,
                               bal.bal_type_id,
                               bal.balance_type
                              )
                  ),
             -1, NULL,
               ABS (fn_get_bal (bal.host_id,
                                bal.entity_id,
                                acc.currency_code,
                                bal.balance_date,
                                bal.bal_type_id,
                                bal.balance_type
                               )
                   )
             * (  DECODE (SIGN (  accrate.credit_rate
                       + fn_get_curr_interest_rate (bal.host_id,
                                                    bal.entity_id,
                                                    acc.currency_code,
                                                    bal.disp_balance_date
                                                   )
                      ),
                 -1, 0,
                 (  accrate.credit_rate
                  + fn_get_curr_interest_rate (bal.host_id,
                                               bal.entity_id,
                                               acc.currency_code,
                                               bal.disp_balance_date
                                              )
                 )
                )
               )
             / 100
             / cur.interest_basis
            ),0) credit_interest_rate
    FROM bal, p_account acc, p_account_interest_rate accrate, s_currency cur
   WHERE bal.host_id = acc.host_id
     AND bal.entity_id = acc.entity_id
     AND bal.bal_type_id = acc.account_id
     AND acc.host_id = cur.host_id
     AND acc.entity_id = cur.entity_id
     AND acc.currency_code = cur.currency_code
     AND acc.host_id = accrate.host_id
     AND acc.entity_id = accrate.entity_id
     AND acc.account_id = accrate.account_id
     AND accrate.interest_rate_date =
            (SELECT MAX (ai.interest_rate_date)
               FROM p_account_interest_rate ai
              WHERE acc.host_id = ai.host_id
                AND acc.entity_id = ai.entity_id
                AND acc.account_id = ai.account_id
                AND TRUNC (ai.interest_rate_date) <= bal.disp_balance_date)
     AND acc.account_class = 'N'
ORDER BY bal.disp_balance_date]]></queryString>

	<field name="DAY" class="java.lang.Integer"/>
	<field name="BALANCE_DATE" class="java.lang.String"/>
	<field name="SUPPLIED_EXTERNAL_BALANCE" class="java.math.BigDecimal"/>
	<field name="ACCOUNT" class="java.lang.String"/>
	<field name="DR" class="java.math.BigDecimal"/>
	<field name="CR" class="java.math.BigDecimal"/>
	<field name="NUMBER_OF_DAYS" class="java.lang.Integer"/>
	<field name="DEBIT_INTEREST_RATE" class="java.math.BigDecimal"/>
	<field name="CREDIT_INTEREST_RATE" class="java.math.BigDecimal"/>

	<variable name="dr_sub_total" class="java.math.BigDecimal" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[$F{DEBIT_INTEREST_RATE} != null ? $F{DEBIT_INTEREST_RATE} : new BigDecimal(0.00)]]></variableExpression>
	</variable>
	<variable name="cr_sub_total" class="java.math.BigDecimal" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[$F{CREDIT_INTEREST_RATE} != null ? $F{CREDIT_INTEREST_RATE} : new BigDecimal(0.00)]]></variableExpression>
	</variable>
	<variable name="grand_total" class="java.lang.String" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[new DecimalFormat("#,##0.0000").format($V{cr_sub_total}.subtract( $V{dr_sub_total})).toString().replaceAll("-","")]]></variableExpression>
	</variable>
	<variable name="grand_total_cr" class="java.lang.String" resetType="Report" calculation="Sum">
		<variableExpression><![CDATA[($V{dr_sub_total}==$V{cr_sub_total})?" CR":((($V{dr_sub_total}.doubleValue())>($V{cr_sub_total}.doubleValue()))? " DR" : " CR" )]]></variableExpression>
	</variable>
	<variable name="test" class="java.math.BigDecimal" resetType="Report" calculation="Sum">
	</variable>
	<variable name="testcrdr" class="java.lang.String" resetType="Report" calculation="Sum">
	</variable>
		<background>
			<band height="0"  splitType="Stretch" >
			</band>
		</background>
		<title>
			<band height="35"  splitType="Prevent" >
				<staticText>
					<reportElement
						x="8"
						y="2"
						width="710"
						height="33"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font size="24"/>
					</textElement>
				<text><![CDATA[Interest Charges per Account]]></text>
				</staticText>
			</band>
		</title>
		<pageHeader>
			<band height="89"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="326"
						y="34"
						width="65"
						height="20"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Account]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="326"
						y="14"
						width="77"
						height="20"
						key="staticText-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[From]]></text>			
				</staticText>
				<staticText>
					<reportElement
						x="553"
						y="14"
						width="77"
						height="20"
						key="staticText-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[To :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="393"
						y="36"
						width="160"
						height="20"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pAccount_Id}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="dd MMMMM yyyy" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="393"
						y="15"
						width="107"
						height="21"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.sql.Timestamp"><![CDATA[$P{pStart_Date}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="dd MMMMM yyyy" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="579"
						y="15"
						width="107"
						height="21"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.sql.Timestamp"><![CDATA[$P{pEnd_Date}]]></textFieldExpression>
				</textField>
				
				<staticText>
					<reportElement
						x="1"
						y="14"
						width="65"
						height="20"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Entity]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="553"
						y="36"
						width="219"
						height="20"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pAccount_Name}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="1"
						y="35"
						width="65"
						height="20"
						key="staticText-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true"/>
					</textElement>
				<text><![CDATA[Currency]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="66"
						y="37"
						width="90"
						height="20"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="156"
						y="36"
						width="151"
						height="20"
						key="textField-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pCurrency_Name}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="66"
						y="15"
						width="90"
						height="20"
						key="textField-15"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="156"
						y="15"
						width="139"
						height="20"
						key="textField-16"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{pEntity_Name}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="1"
						y="8"
						width="695"
						height="0"
						key="line-1"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<staticText>
					<reportElement
						x="55"
						y="13"
						width="11"
						height="20"
						key="staticText-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="55"
						y="34"
						width="11"
						height="20"
						key="staticText-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="381"
						y="14"
						width="11"
						height="20"
						key="staticText-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="381"
						y="34"
						width="11"
						height="20"
						key="staticText-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font pdfFontName="Helvetica-Bold" size="12" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="307"
						y="63"
						width="54"
						height="21"
						key="staticText-8">
							<printWhenExpression><![CDATA[($F{DR}!=null) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[        DR %]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="374"
						y="63"
						width="54"
						height="21"
						key="staticText-12">
							<printWhenExpression><![CDATA[($F{CR}!=null) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[       CR %]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="460"
						y="63"
						width="123"
						height="21"
						key="staticText-13">
							<printWhenExpression><![CDATA[($F{BALANCE_DATE}!=null) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box rightPadding="2">					<pen lineWidth="0.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[           DR Interest]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="564"
						y="63"
						width="133"
						height="21"
						key="staticText-14">
							<printWhenExpression><![CDATA[($F{BALANCE_DATE}!=null) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[           CR Interest]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="148"
						y="63"
						width="151"
						height="21"
						key="staticText-25">
							<printWhenExpression><![CDATA[($F{BALANCE_DATE}!=null) ? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[MT950 Closing Balance]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="63"
						width="90"
						height="21"
						key="staticText-26">
							<printWhenExpression><![CDATA[($F{BALANCE_DATE}!=null) ? new Boolean(true) : new Boolean(false)


]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[Balance Date]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="1"
						y="63"
						width="41"
						height="21"
						key="staticText-27">
							<printWhenExpression><![CDATA[($F{BALANCE_DATE}!=null) ? new Boolean(true) : new Boolean(false)
]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="true"/>
					</textElement>
				<text><![CDATA[Day]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="1"
						y="57"
						width="695"
						height="0"
						key="line-2"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnHeader>
		<detail>
			<band height="19"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="0"
						y="1"
						width="39"
						height="18"
						key="textField-4"/>
					<box></box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.Integer"><![CDATA[$F{DAY}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="###0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="79"
						y="1"
						width="70"
						height="18"
						key="textField-5"/>
					<box></box>
					<textElement textAlignment="Left">
						<font/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{BALANCE_DATE}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="126"
						y="1"
						width="164"
						height="18"
						key="textField-6"/>
					<box></box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{SUPPLIED_EXTERNAL_BALANCE}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.0000" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="300"
						y="1"
						width="60"
						height="18"
						key="textField-7"/>
					<box></box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{DR}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.0000" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="370"
						y="1"
						width="57"
						height="18"
						key="textField-11"/>
					<box></box>
					<textElement textAlignment="Right">
						<font/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{CR}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="430"
						y="1"
						width="153"
						height="18"
						forecolor="#000000"
						key="textField-12"/>
					<box></box>
					<textElement textAlignment="Right">
						<font isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{DEBIT_INTEREST_RATE}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="550"
						y="1"
						width="149"
						height="18"
						forecolor="#000000"
						key="textField-13"/>
					<box></box>
					<textElement textAlignment="Right">
						<font isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$F{CREDIT_INTEREST_RATE}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  splitType="Stretch" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="34"  splitType="Stretch" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="5"
						y="5"
						width="100"
						height="21"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="510"
						y="5"
						width="170"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="676"
						y="5"
						width="32"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-26"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
			</band>
		</pageFooter>
		<lastPageFooter>
			<band height="23"  splitType="Prevent" >
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="510"
						y="0"
						width="170"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right">
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " of "+" "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Report" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="676"
						y="0"
						width="32"
						height="19"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["  " + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="5"
						y="0"
						width="100"
						height="21"
						forecolor="#000000"
						backcolor="#FFFFFF"
						key="textField-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="10"/>
					</textElement>
				<textFieldExpression   class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
				</textField>
			</band>
		</lastPageFooter>
		<summary>
			<band height="83"  splitType="Stretch" >
				<staticText>
					<reportElement
						x="244"
						y="24"
						width="167"
						height="18"
						key="staticText-15"
						stretchType="RelativeToBandHeight">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(true) :new Boolean(false)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement>
						<font size="12" isUnderline="false"/>
					</textElement>
				<text><![CDATA[     No records to display]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="258"
						y="63"
						width="145"
						height="18"
						key="staticText-16"
						positionType="FixRelativeToBottom"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font size="14"/>
					</textElement>
				<text><![CDATA[*** End of Report ***]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="288"
						y="8"
						width="112"
						height="16"
						forecolor="#000000"
						key="staticText-18">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Sub Total:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="432"
						y="8"
						width="153"
						height="18"
						forecolor="#000000"
						key="textField">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$V{dr_sub_total}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="551"
						y="8"
						width="149"
						height="18"
						forecolor="#000000"
						key="textField">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[$V{cr_sub_total}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="288"
						y="31"
						width="112"
						height="16"
						forecolor="#000000"
						key="staticText-19">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font pdfFontName="Helvetica-Bold" size="10" isBold="true" isUnderline="false"/>
					</textElement>
				<text><![CDATA[Grand Total:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="#,##0.00" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="479"
						y="31"
						width="126"
						height="18"
						forecolor="#000000"
						key="textField">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.math.BigDecimal"><![CDATA[($V{cr_sub_total}.subtract( $V{dr_sub_total} )).abs()]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="608"
						y="31"
						width="77"
						height="18"
						forecolor="#000000"
						key="textField">
							<printWhenExpression><![CDATA[$F{DAY}==null ?  new Boolean(false) :new Boolean(true)]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isUnderline="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[(($V{cr_sub_total}.subtract( $V{dr_sub_total} )).signum() == 1 ? "CR" : "DR")]]></textFieldExpression>
				</textField>
			</band>
		</summary>
</jasperReport>
