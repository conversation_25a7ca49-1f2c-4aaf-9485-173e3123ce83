<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ThroughputRatio_subreport2" pageWidth="802" pageHeight="555" orientation="Landscape" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="7edc5444-5348-43c5-aa08-a62453879613">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="SUBREPORT_CHART_GRID_DIR" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="p_VALUE_DATE" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="p_ILM_GROUP" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="Test" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_selectedCurrency" class="java.lang.String"/>
	<parameter name="p_selectedCurrencyName" class="java.lang.String"/>
	<parameter name="p_ApplyCcyThreshold" class="java.lang.String"/>
	<parameter name="p_selectedScenario" class="java.lang.String"/>
	<parameter name="p_selectedEntity" class="java.lang.String"/>
	<parameter name="p_selectedAccountGroup" class="java.lang.String"/>
	<parameter name="p_selectedAccountGrpName" class="java.lang.String"/>
	<parameter name="p_ApplyCcyThresholdAsString" class="java.lang.String"/>
	<parameter name="p_ApplyCcyMultiplierAsString" class="java.lang.String"/>
	<parameter name="p_selectedScenarioName" class="java.lang.String"/>
	<parameter name="p_SpreadOnlyAsString" class="java.lang.String"/>
	<parameter name="p_selectedUserId" class="java.lang.String"/>
	<parameter name="p_selectedEntityName" class="java.lang.String"/>
	<parameter name="p_ReportDateTime" class="java.lang.String"/>
	<parameter name="p_selectedDateISO" class="java.lang.String"/>
	<parameter name="p_hostID" class="java.lang.String"/>
	<parameter name="p_dbLink" class="java.lang.String"/>
	<parameter name="p_selectedDateAsString" class="java.lang.String"/>
	<parameter name="p_roleId" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="p_ChartsData" class="java.util.HashMap"/>
	<parameter name="chartImageBase64" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\temp\\ThrouhputReport\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="p_currencyFormat" class="java.lang.String"/>
	<parameter name="p_CalculateAs" class="java.lang.String"/>
	<queryString>
		<![CDATA[select * from table(pkg_ilm_rep.FN_GET_THRU_RATIO_REPORT($P{p_hostID}, $P{p_selectedEntity}, $P{p_selectedCurrency}, TO_DATE($P{p_selectedDateISO}, 'YYYY-MM-DD'), $P{p_selectedAccountGroup} , $P{p_selectedScenario},$P{p_roleId} , $P{p_dbLink}, $P{p_CalculateAs}))]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="CCY_CODE" class="java.lang.String"/>
	<field name="ILM_GRP_ID" class="java.lang.String"/>
	<field name="SERIES_IDENTIFIER" class="java.lang.String"/>
	<field name="INFLOW_FC" class="java.math.BigDecimal"/>
	<field name="OUTFLOW_FC" class="java.math.BigDecimal"/>
	<field name="ACT_INFLOW" class="java.math.BigDecimal"/>
	<field name="ACT_OUTFLOW" class="java.math.BigDecimal"/>
	<field name="UNSETTL_OUTFLOW" class="java.math.BigDecimal"/>
	<field name="CURRENT_VAL" class="java.lang.String"/>
	<field name="THRESHOLD1" class="java.lang.String"/>
	<field name="THRESHOLD1_COL" class="java.lang.String"/>
	<field name="THRESHOLD2" class="java.lang.String"/>
	<field name="THRESHOLD2_COL" class="java.lang.String"/>
	<field name="GRAPH" class="oracle.jdbc.OracleClob"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="149" splitType="Stretch">
			<textField>
				<reportElement uuid="89e89f8c-3f8b-4239-918c-3adceb8bac78" x="101" y="39" width="107" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ENTITY_ID}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="396658f9-1439-406d-8e66-7686680ff837" x="646" y="39" width="160" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{ILM_GRP_ID}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="7691dbdb-2d51-481c-8dac-8b55a8a4b1d8" x="390" y="39" width="93" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$F{CCY_CODE}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement uuid="5eeae1cb-1afb-46dd-ad29-f166de4788f6" x="3" y="73" width="806" height="76"/>
				<subreportParameter name="p_VALUE_DATE">
					<subreportParameterExpression><![CDATA[$P{p_selectedDateISO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_SCENARIO_ID">
					<subreportParameterExpression><![CDATA[$F{SERIES_IDENTIFIER}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_role">
					<subreportParameterExpression><![CDATA[$P{p_roleId}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ChartsData">
					<subreportParameterExpression><![CDATA[$P{p_ChartsData}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_currencyFormat">
					<subreportParameterExpression><![CDATA[$P{p_currencyFormat}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ILM_GRP">
					<subreportParameterExpression><![CDATA[$F{ILM_GRP_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CalculateAs">
					<subreportParameterExpression><![CDATA[$P{p_CalculateAs}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="chartImageBase64">
					<subreportParameterExpression><![CDATA[$P{chartImageBase64}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_Dictionary_Data">
					<subreportParameterExpression><![CDATA[$P{p_Dictionary_Data}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_CHART_GRID_DIR}]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement uuid="e60ef525-9e95-4646-a497-55dc2e8fde66" x="5" y="39" width="96" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pEntity")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="78c5f0fe-9ed7-41e5-bcb8-e855847084db" x="290" y="39" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pCurrency")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="428dd7fa-0509-4054-bf7d-bb1569eadd05" x="546" y="39" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pAccount Grp")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="be368d8c-309f-4fa7-b290-dbb3429be748" mode="Opaque" x="0" y="0" width="802" height="26" backcolor="#D6E3FE"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("pSubReportTitle")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
