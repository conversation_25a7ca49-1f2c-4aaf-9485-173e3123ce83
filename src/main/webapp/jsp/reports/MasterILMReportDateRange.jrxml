<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DateRangeILMReport" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="61"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="org.swallow.util.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1">
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppILM_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDBLink" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT
							TO_CHAR(TO_DATE(EXTRACT( HOUR FROM timedata)||':'||EXTRACT( MINUTE FROM timedata), 'HH24:MI'), 'HH24:MI') TIME,
						   	TO_CHAR(AVG_IN_PERCENT, '990D00','NLS_NUMERIC_CHARACTERS = ''.,''')||'%' AS INFLOW,
						   	TO_CHAR(AVG_OUT_PERCENT, '990D00','NLS_NUMERIC_CHARACTERS = ''.,''')||'%' AS OUTFLOW

					FROM TABLE (pkg_ilm_rep.fn_avg_thruput (
					            $P{ppHOST_ID},
						        $P{ppENTITY_ID},
						        $P{ppCCY},
						        $P{ppILM_GROUP_ID},
						        $P{ppSERIES_IDENTIFIER},
						        $P{ppDBLink},
						        $P{ppVALUE_DATE},
						        $P{ppVALUE_DATE_END},
						        $P{ppROLE_ID}))]]>
		</queryString>
		<field name="TIME" class="java.lang.String"/>
		<field name="INFLOW" class="java.lang.String"/>
		<field name="OUTFLOW" class="java.lang.String"/>
	</subDataset>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pCurrency_Code" class="java.lang.String"/>
	<parameter name="pILMGroup_Id" class="java.lang.String"/>
	<parameter name="pDBLink" class="java.lang.String"/>
	<parameter name="pRoleId" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String"/>
	<parameter name="pValue_Date" class="java.util.Date"/>
	<parameter name="pValue_DateEnd" class="java.util.Date"/>
	<parameter name="sdf" class="java.text.SimpleDateFormat"/>
	<parameter name="pSeriesIdentifier" class="java.lang.String"/>
	<parameter name="pILMReportType" class="java.lang.String"/>
	<parameter name="pInflow_Outflow_Sum" class="java.lang.String"/>
	<parameter name="pUseCcyMultiplier" class="java.lang.String"/>
	<parameter name="pDictionary_Data" class="java.util.HashMap"/>
	<parameter name="pIlmUtil" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="pSubRepILMCumulativeBalanceGraph" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="pSubRepILMIntradayLiquidity" class="net.sf.jasperreports.engine.JasperReport"/>
	<queryString>
		<![CDATA[WITH headers AS ( SELECT   HOST_ID, ENTITY_ID, ENTITY_NAME, CCY, CCY_NAME,ILM_GROUP_ID,
                           ILM_GROUP_NAME,ILM_SCENARIO_ID,ILM_SCENARIO_NAME,GLOBAL_GROUP_ID,GLOBAL_GROUP_NAME,
                           CENTRAL_BANK_GROUP_ID,CENTRAL_BANK_GROUP_NAME,LVPS_NAME,
                           MISSING_D_START,MISSING_D_END,TOTAL_ROWS,COLLECT_NET_CUM_POS, DECODE( $P{pInflow_Outflow_Sum}, 'GC',global_group_id, 'CB', central_bank_group_id, 'CORR', ilm_group_id, ilm_group_id) INFLOW_OUTFLOW_SUM_GROUP_ID,
                           $P{pDateFormat} pDateFormat,
                           $P{pSeriesIdentifier} pSeriesIdentifier,
                           $P{pDBLink} pDBLink,
                           $P{pValue_Date} pValue_Date,
                           $P{pValue_DateEnd} pValue_DateEnd,
                           $P{pRoleId} pRoleId,
                           DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER, 'N' ) CCY_MULTIPLIER,
                           DECODE ($P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER_VALUE, 1 ) CCY_MULTIPLIER_VALUE,
                           $P{pInflow_Outflow_Sum} pInflow_Outflow_Sum
                        FROM
                           TABLE (pkg_ilm_rep.fn_header (
                            $P{pHost_Id},
                            $P{pEntity_Id},
                            $P{pCurrency_Code},
                            $P{pILMGroup_Id},
                            $P{pSeriesIdentifier},
                            $P{pRoleId},
                            $P{pDBLink},
                            $P{pValue_Date},
                            $P{pValue_DateEnd}
                            ))
                     )
    SELECT h.ENTITY_ID,ENTITY_NAME,h.CCY,CCY_NAME,h.ilm_group_id,h.inflow_outflow_sum_group_id,
                            ILM_GROUP_NAME,h.ILM_SCENARIO_ID,ILM_SCENARIO_NAME,GLOBAL_GROUP_ID,GLOBAL_GROUP_NAME,
                            CENTRAL_BANK_GROUP_ID,CENTRAL_BANK_GROUP_NAME,LVPS_NAME,
                            rownum current_row, total_rows,
                            -- End header
                            -- Begin
                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX1_POS_NET_CUM_D,data.MAX1_POS_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS  MAX1_POS_NET_CUM_D,
                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX2_POS_NET_CUM_D,data.MAX2_POS_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS  MAX2_POS_NET_CUM_D,
                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX3_POS_NET_CUM_D,data.MAX3_POS_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS  MAX3_POS_NET_CUM_D,

                            DECODE(h.ENTITY_ID, 'All', R.MAX1_POS_NET_CUM_V,data.MAX1_POS_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX1_POS_NET_CUM_V,
                            DECODE(h.ENTITY_ID, 'All', R.MAX2_POS_NET_CUM_V,data.MAX2_POS_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX2_POS_NET_CUM_V,
                            DECODE(h.ENTITY_ID, 'All', R.MAX3_POS_NET_CUM_V,data.MAX3_POS_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX3_POS_NET_CUM_V,


                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX1_NEG_NET_CUM_D,data.MAX1_NEG_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS MAX1_NEG_NET_CUM_D,
                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX2_NEG_NET_CUM_D,data.MAX2_NEG_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS MAX2_NEG_NET_CUM_D,
                            to_char(DECODE(h.ENTITY_ID, 'All', R.MAX3_NEG_NET_CUM_D,data.MAX3_NEG_NET_CUM_D),upper(pDateFormat)||' HH24:MI') AS MAX3_NEG_NET_CUM_D,

                            DECODE(h.ENTITY_ID, 'All', R.MAX1_NEG_NET_CUM_V,data.MAX1_NEG_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX1_NEG_NET_CUM_V,
                            DECODE(h.ENTITY_ID, 'All', R.MAX2_NEG_NET_CUM_V,data.MAX2_NEG_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX2_NEG_NET_CUM_V,
                            DECODE(h.ENTITY_ID, 'All', R.MAX3_NEG_NET_CUM_V,data.MAX3_NEG_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS MAX3_NEG_NET_CUM_V,


                            DECODE(h.ENTITY_ID, 'All', R.AVG_POS_NET_CUM_V,data.AVG_POS_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS AVG_POS_NET_CUM_V,
                            DECODE(h.ENTITY_ID, 'All', R.AVG_NEG_NET_CUM_V,data.AVG_NEG_NET_CUM_V)/CCY_MULTIPLIER_VALUE AS AVG_NEG_NET_CUM_V,

                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN1_AVLBL_ASSETS_V, gc_data.MIN1_AVLBL_ASSETS_V)/CCY_MULTIPLIER_VALUE AS MIN1_AVLBL_ASSETS_V,
                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN2_AVLBL_ASSETS_V, gc_data.MIN2_AVLBL_ASSETS_V)/CCY_MULTIPLIER_VALUE AS MIN2_AVLBL_ASSETS_V,
                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN3_AVLBL_ASSETS_V, gc_data.MIN3_AVLBL_ASSETS_V)/CCY_MULTIPLIER_VALUE AS MIN3_AVLBL_ASSETS_V,

                            DECODE(pInflow_Outflow_Sum, NULL, data.AVG_AVLBL_ASSETS_V, gc_data.AVG_AVLBL_ASSETS_V)/CCY_MULTIPLIER_VALUE AS AVG_AVLBL_ASSETS_V,

                            data.MAX1_DAY_ACC_INFLOW_V/CCY_MULTIPLIER_VALUE AS MAX1_DAY_ACC_INFLOW_V,
                            data.MAX1_DAY_ACC_OUTFLOW_V/CCY_MULTIPLIER_VALUE AS MAX1_DAY_ACC_OUTFLOW_V,
                            data.MAX2_DAY_ACC_INFLOW_V/CCY_MULTIPLIER_VALUE AS MAX2_DAY_ACC_INFLOW_V,
                            data.MAX2_DAY_ACC_OUTFLOW_V/CCY_MULTIPLIER_VALUE AS MAX2_DAY_ACC_OUTFLOW_V,
                            data.MAX3_DAY_ACC_INFLOW_V/CCY_MULTIPLIER_VALUE AS MAX3_DAY_ACC_INFLOW_V,
                            data.MAX3_DAY_ACC_OUTFLOW_V/CCY_MULTIPLIER_VALUE AS MAX3_DAY_ACC_OUTFLOW_V,

                            data.AVG_DAY_ACC_INFLOW_V/CCY_MULTIPLIER_VALUE AS AVG_DAY_ACC_INFLOW_V,
                            data.AVG_DAY_ACC_OUTFLOW_V/CCY_MULTIPLIER_VALUE AS AVG_DAY_ACC_OUTFLOW_V,

                            DECODE(pSeriesIdentifier, 'Standard', data.MAX1_DAY_ACC_CRIT_INFLOW_V, std_data.MAX1_DAY_ACC_CRIT_INFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX1_DAY_ACC_CRIT_INFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.MAX1_DAY_ACC_CRIT_OUTFLOW_V, std_data.MAX1_DAY_ACC_CRIT_OUTFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX1_DAY_ACC_CRIT_OUTFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.MAX2_DAY_ACC_CRIT_INFLOW_V, std_data.MAX2_DAY_ACC_CRIT_INFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX2_DAY_ACC_CRIT_INFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.MAX2_DAY_ACC_CRIT_OUTFLOW_V, std_data.MAX2_DAY_ACC_CRIT_OUTFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX2_DAY_ACC_CRIT_OUTFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.MAX3_DAY_ACC_CRIT_INFLOW_V, std_data.MAX3_DAY_ACC_CRIT_INFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX3_DAY_ACC_CRIT_INFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.MAX3_DAY_ACC_CRIT_OUTFLOW_V, std_data.MAX3_DAY_ACC_CRIT_OUTFLOW_V)/CCY_MULTIPLIER_VALUE AS MAX3_DAY_ACC_CRIT_OUTFLOW_V,


                            DECODE(pSeriesIdentifier, 'Standard', data.AVG_DAY_ACC_CRIT_INFLOW_V, std_data.AVG_DAY_ACC_CRIT_INFLOW_V)/CCY_MULTIPLIER_VALUE AS AVG_DAY_ACC_CRIT_INFLOW_V,
                            DECODE(pSeriesIdentifier, 'Standard', data.AVG_DAY_ACC_CRIT_OUTFLOW_V, std_data.AVG_DAY_ACC_CRIT_OUTFLOW_V)/CCY_MULTIPLIER_VALUE AS AVG_DAY_ACC_CRIT_OUTFLOW_V,

                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN1_AVLBL_ASSETS_D, gc_data.MIN1_AVLBL_ASSETS_D) AS MIN1_AVLBL_ASSETS_D,
                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN2_AVLBL_ASSETS_D, gc_data.MIN2_AVLBL_ASSETS_D) AS MIN2_AVLBL_ASSETS_D,
                            DECODE(pInflow_Outflow_Sum, NULL, data.MIN3_AVLBL_ASSETS_D, gc_data.MIN3_AVLBL_ASSETS_D) AS MIN3_AVLBL_ASSETS_D,

                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX1_DAY_ACC_CRIT_INFLOW_D, std_data.MAX1_DAY_ACC_CRIT_INFLOW_D),upper(pDateFormat))  AS  MAX1_DAY_ACC_CRIT_INFLOW_D,
                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX2_DAY_ACC_CRIT_INFLOW_D, std_data.MAX2_DAY_ACC_CRIT_INFLOW_D),upper(pDateFormat))  AS     MAX2_DAY_ACC_CRIT_INFLOW_D,
                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX3_DAY_ACC_CRIT_INFLOW_D, std_data.MAX3_DAY_ACC_CRIT_INFLOW_D),upper(pDateFormat))  AS     MAX3_DAY_ACC_CRIT_INFLOW_D,

                            to_char(data.MAX1_DAY_ACC_OUTFLOW_D,upper(pDateFormat))  AS MAX1_DAY_ACC_OUTFLOW_D,
                            to_char(data.MAX2_DAY_ACC_OUTFLOW_D,upper(pDateFormat))  AS MAX2_DAY_ACC_OUTFLOW_D,
                            to_char(data.MAX3_DAY_ACC_OUTFLOW_D,upper(pDateFormat))  AS MAX3_DAY_ACC_OUTFLOW_D,


                            to_char(data.MAX1_DAY_ACC_INFLOW_D,upper(pDateFormat))  AS MAX1_DAY_ACC_INFLOW_D,
                            to_char(data.MAX2_DAY_ACC_INFLOW_D,upper(pDateFormat))  AS MAX2_DAY_ACC_INFLOW_D,
                            to_char(data.MAX3_DAY_ACC_INFLOW_D,upper(pDateFormat))  AS MAX3_DAY_ACC_INFLOW_D,


                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX1_DAY_ACC_CRIT_OUTFLOW_D, std_data.MAX1_DAY_ACC_CRIT_OUTFLOW_D),upper(pDateFormat))  AS MAX1_DAY_ACC_CRIT_OUTFLOW_D,
                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX2_DAY_ACC_CRIT_OUTFLOW_D, std_data.MAX2_DAY_ACC_CRIT_OUTFLOW_D),upper(pDateFormat))  AS MAX2_DAY_ACC_CRIT_OUTFLOW_D,
                            to_char(DECODE(pSeriesIdentifier, 'Standard', data.MAX3_DAY_ACC_CRIT_OUTFLOW_D, std_data.MAX3_DAY_ACC_CRIT_OUTFLOW_D),upper(pDateFormat))  AS MAX3_DAY_ACC_CRIT_OUTFLOW_D,

                            CCY_MULTIPLIER,CCY_MULTIPLIER_VALUE,

                            CASE WHEN data.COUNT_DATA=0 THEN 'NO_DATA'
                            ELSE 'OK'
                              END AS DATA_STATE,
                              h.missing_d_start AS MISSING_D_START,
                              h.missing_d_end AS MISSING_D_END,
                              COLLECT_NET_CUM_POS
    FROM headers h CROSS JOIN TABLE (pkg_ilm_rep.fn_main_data (
                            h.host_id,
                            h.entity_id,
                            h.ccy,
                            h.inflow_outflow_sum_group_id,
                            pSeriesIdentifier,
                            pDBLink,
                            pValue_Date,
                            pValue_DateEnd,
                            pRoleId)) data
                    CROSS JOIN  TABLE (pkg_ilm_rep.fn_main_data (
                            h.host_id,
                            h.entity_id,
                            h.ccy,
                            h.global_group_id,
                            pSeriesIdentifier,
                            pDBLink,
                            pValue_Date,
                            pValue_DateEnd,
                            pRoleId)) gc_data
                        LEFT JOIN TABLE(pkg_ilm_rep.fn_rank_net_cum_pos(h.host_id,h.entity_id,h.ccy,h.global_group_id,
                            pSeriesIdentifier,
                            pDBLink,
                            pValue_Date,
                            pValue_DateEnd,
                            pRoleId,'N')) R ON (h.entity_id='All')
						LEFT JOIN TABLE (pkg_ilm_rep.fn_main_data (
                            h.host_id,
                            h.entity_id,
                            h.ccy,
                            h.inflow_outflow_sum_group_id,
                            'Standard',
                            pDBLink,
                            pValue_Date,
                            pValue_DateEnd,
                            pRoleId)) std_data ON (pSeriesIdentifier!='Standard')]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CCY" class="java.lang.String"/>
	<field name="CCY_NAME" class="java.lang.String"/>
	<field name="ILM_GROUP_ID" class="java.lang.String"/>
	<field name="ILM_GROUP_NAME" class="java.lang.String"/>
	<field name="ILM_SCENARIO_ID" class="java.lang.String"/>
	<field name="ILM_SCENARIO_NAME" class="java.lang.String"/>
	<field name="CENTRAL_BANK_GROUP_ID" class="java.lang.String"/>
	<field name="GLOBAL_GROUP_ID" class="java.lang.String"/>
	<field name="GLOBAL_GROUP_NAME" class="java.lang.String"/>
	<field name="CENTRAL_BANK_GROUP_NAME" class="java.lang.String"/>
	<field name="LVPS_NAME" class="java.lang.String"/>
	<field name="CCY_MULTIPLIER" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX2_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX3_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX2_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX3_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="AVG_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX1_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX2_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX3_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX2_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX3_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="AVG_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX1_DAY_ACC_INFLOW_D" class="java.lang.String"/>
	<field name="MAX2_DAY_ACC_INFLOW_D" class="java.lang.String"/>
	<field name="MAX3_DAY_ACC_INFLOW_D" class="java.lang.String"/>
	<field name="MAX1_DAY_ACC_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX2_DAY_ACC_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX3_DAY_ACC_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX1_DAY_ACC_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX2_DAY_ACC_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX3_DAY_ACC_INFLOW_V" class="java.lang.Double"/>
	<field name="AVG_DAY_ACC_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX1_DAY_ACC_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MAX2_DAY_ACC_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MAX3_DAY_ACC_OUTFLOW_V" class="java.lang.Double"/>
	<field name="AVG_DAY_ACC_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MAX1_DAY_ACC_CRIT_INFLOW_D" class="java.lang.String"/>
	<field name="MAX2_DAY_ACC_CRIT_INFLOW_D" class="java.lang.String"/>
	<field name="MAX3_DAY_ACC_CRIT_INFLOW_D" class="java.lang.String"/>
	<field name="MAX1_DAY_ACC_CRIT_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX2_DAY_ACC_CRIT_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX3_DAY_ACC_CRIT_OUTFLOW_D" class="java.lang.String"/>
	<field name="MAX1_DAY_ACC_CRIT_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX2_DAY_ACC_CRIT_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX3_DAY_ACC_CRIT_INFLOW_V" class="java.lang.Double"/>
	<field name="AVG_DAY_ACC_CRIT_INFLOW_V" class="java.lang.Double"/>
	<field name="MAX1_DAY_ACC_CRIT_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MAX2_DAY_ACC_CRIT_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MAX3_DAY_ACC_CRIT_OUTFLOW_V" class="java.lang.Double"/>
	<field name="AVG_DAY_ACC_CRIT_OUTFLOW_V" class="java.lang.Double"/>
	<field name="MIN1_AVLBL_ASSETS_D" class="java.util.Date"/>
	<field name="MIN1_AVLBL_ASSETS_V" class="java.lang.Double"/>
	<field name="MIN2_AVLBL_ASSETS_D" class="java.util.Date"/>
	<field name="MIN2_AVLBL_ASSETS_V" class="java.lang.Double"/>
	<field name="MIN3_AVLBL_ASSETS_D" class="java.util.Date"/>
	<field name="MIN3_AVLBL_ASSETS_V" class="java.lang.Double"/>
	<field name="AVG_AVLBL_ASSETS_V" class="java.lang.Double"/>
	<field name="CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<field name="DATA_STATE" class="java.lang.String"/>
	<field name="CURRENT_ROW" class="java.lang.Integer"/>
	<field name="TOTAL_ROWS" class="java.lang.Integer"/>
	<field name="MISSING_D_START" class="java.util.Date"/>
	<field name="MISSING_D_END" class="java.util.Date"/>
	<field name="COLLECT_NET_CUM_POS" class="java.lang.String"/>
	<field name="INFLOW_OUTFLOW_SUM_GROUP_ID" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="53" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="30" forecolor="#000000" backcolor="#E6E6FA"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pRAPORT_TITLE_MONTHLY_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="600" y="33" width="136" height="20">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORT_DATE_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-1" x="720" y="33" width="98" height="20">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}+" HH:mm:ss").format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="2" splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="32" splitType="Prevent">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pENTITY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="1" width="180" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ENTITY_ID}!=null)? $F{ENTITY_ID} :""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="1" width="430" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ENTITY_NAME}!=null)?$F{ENTITY_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="16" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCURRENCY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="16" width="180" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{CCY}!=null)? $F{CCY}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="16" width="460" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{CCY_NAME}!=null)?$F{CCY_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="16" splitType="Prevent">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pScenario_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="0" width="200" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ILM_SCENARIO_ID}!=null)?$F{ILM_SCENARIO_ID}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="0" width="460" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ILM_SCENARIO_NAME}!=null)?$F{ILM_SCENARIO_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="16" splitType="Prevent">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null && $P{pILMReportType}.equals("ILM_GROUP_REPORT")]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pGROUP_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="0" width="180" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[("All".equals($F{ENTITY_ID})) ? ($P{pDictionary_Data}.get("pGLOBAL_CURRENCY_GROUP")) : (($F{ILM_GROUP_ID}!=null)?$F{ILM_GROUP_ID}:"")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="0" width="460" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ILM_GROUP_NAME}!=null)?$F{ILM_GROUP_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="16">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null && $P{pILMReportType}.equals("ILM_BASEL_B")]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCORRESPONDENT_BANK_GROUP")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="0" width="180" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ILM_GROUP_ID}!=null)? $F{ILM_GROUP_ID} :""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="0" width="460" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{ILM_GROUP_NAME}!=null)?$F{ILM_GROUP_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="32">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null && !$P{pILMReportType}.equals("ILM_GROUP_REPORT")]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pGLOBAL_CURRENCY_GROUP")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="0" width="180" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{GLOBAL_GROUP_ID}!=null)? $F{GLOBAL_GROUP_ID}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="380" y="0" width="460" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{GLOBAL_GROUP_NAME}!=null)?$F{GLOBAL_GROUP_NAME}:""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="16" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCENTRAL_BANK_GROUP")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="16" width="180" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{CENTRAL_BANK_GROUP_ID}!=null)? $F{CENTRAL_BANK_GROUP_ID}:""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="380" y="16" width="460" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{CENTRAL_BANK_GROUP_NAME}!=null)? $F{CENTRAL_BANK_GROUP_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="16">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null && $P{pILMReportType}.equals("ILM_BASEL_A")]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLVPS_NAME")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="170" y="0" width="180" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{LVPS_NAME}!=null)? $F{LVPS_NAME}:""]]></textFieldExpression>
			</textField>
		</band>
		<band height="33">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null]]></printWhenExpression>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="0" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORTING_PERIOD_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="170" y="0" width="80" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_Date})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="170" y="16" width="120" height="17">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_DateEnd})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="486" y="16" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="676" y="16" width="80" height="16">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_"+$F{CCY_MULTIPLIER})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="486" y="0" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[!$P{pILMReportType}.equals("ILM_GROUP_REPORT")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pINFLOW_OUTFLOW_SUMMATION")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="676" y="0" width="80" height="16">
					<printWhenExpression><![CDATA[!$P{pILMReportType}.equals("ILM_GROUP_REPORT")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pINFLOW_OUTFLOW_SUMMATION_"+$P{pInflow_Outflow_Sum})]]></textFieldExpression>
			</textField>
		</band>
		<band height="16">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS}!= null]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="10" y="0" width="170" height="16" forecolor="#000000"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pDATA_STATE")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="170" y="0" width="460" height="16" forecolor="#000000">
					<printWhenExpression><![CDATA[$F{MISSING_D_START} != null && $F{MISSING_D_END} != null && (!($F{MISSING_D_END}).equals($F{MISSING_D_START}))]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pINCOMPLETED_DATA") +" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_START})+" "+$P{pDictionary_Data}.get("pTo_Value")+" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_END})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="200" y="0" width="460" height="16" forecolor="#000000">
					<printWhenExpression><![CDATA[$F{MISSING_D_START} != null && $F{MISSING_D_END} != null && (($F{MISSING_D_END}).equals($F{MISSING_D_START}))]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pONE_DAY_MISSING") +" "+new SimpleDateFormat($P{pDateFormat}).format($F{MISSING_D_START})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="200" y="0" width="460" height="16" forecolor="#000000">
					<printWhenExpression><![CDATA[$F{MISSING_D_START} == null && $F{MISSING_D_END} == null]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCOMPLETE")]]></textFieldExpression>
			</textField>
		</band>
		<band height="114" splitType="Stretch">
			<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="18" forecolor="#000000" backcolor="#E6E6FA">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box leftPadding="6">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pDAILY_INTRADAY_LIQUIDITY_USAGE_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="54" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_POSITIVE_NET_CUMULATIVE_POSITION_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="350" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="465" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="580" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="695" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_D}!=null)?$F{MAX1_POS_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_POS_NET_CUM_D}!=null)?$F{MAX2_POS_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_POS_NET_CUM_D}!=null)?$F{MAX3_POS_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_POS_NET_CUM_V}!=null && (($F{MAX1_POS_NET_CUM_D}!=null) ||($F{MAX2_POS_NET_CUM_D}!=null)||($F{MAX3_POS_NET_CUM_D}!=null)))?($P{pIlmUtil}.formatCurrency($F{AVG_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_D}!=null)?$F{MAX1_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_NEG_NET_CUM_D}!=null)?$F{MAX2_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_NEG_NET_CUM_D}!=null)?$F{MAX3_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="97" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_POSITIVE_NET_CUMULATIVE_NEGATIVE_LABEL")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_NEG_NET_CUM_V}!=null && (($F{MAX1_NEG_NET_CUM_D}!=null) ||($F{MAX2_NEG_NET_CUM_D}!=null)||($F{MAX3_NEG_NET_CUM_D}!=null)))?($P{pIlmUtil}.formatCurrency($F{AVG_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
		</band>
		<band height="114" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="18" forecolor="#000000" backcolor="#E6E6FA">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box leftPadding="6">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTOTAL_PAYMENTS_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="350" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="465" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="580" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="695" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_INFLOW_D}!=null)?$F{MAX1_DAY_ACC_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_INFLOW_D}!=null)?$F{MAX2_DAY_ACC_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_INFLOW_D}!=null)?$F{MAX3_DAY_ACC_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="54" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_DAILY_TOTAL_INFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_DAY_ACC_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_DAY_ACC_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_DAY_ACC_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_DAY_ACC_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_DAY_ACC_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_OUTFLOW_D}!=null)?$F{MAX3_DAY_ACC_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_OUTFLOW_D}!=null)?$F{MAX2_DAY_ACC_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_OUTFLOW_D}!=null)?$F{MAX1_DAY_ACC_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="97" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_DAILY_TOTAL_OUTFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_DAY_ACC_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_DAY_ACC_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_DAY_ACC_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_DAY_ACC_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_DAY_ACC_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
		</band>
		<band height="114" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="18" forecolor="#000000" backcolor="#E6E6FA">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box leftPadding="6">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTIME_SPECIFIC_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="350" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="465" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="580" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="695" y="20" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_CRIT_INFLOW_D}!=null)?$F{MAX1_DAY_ACC_CRIT_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_CRIT_INFLOW_D}!=null)?$F{MAX2_DAY_ACC_CRIT_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="37" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_CRIT_INFLOW_D}!=null)?$F{MAX3_DAY_ACC_CRIT_INFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="54" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_DAILY_TOTAL_INFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_CRIT_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_DAY_ACC_CRIT_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_CRIT_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_DAY_ACC_CRIT_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_CRIT_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_DAY_ACC_CRIT_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="54" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_DAY_ACC_CRIT_INFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_DAY_ACC_CRIT_INFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_CRIT_OUTFLOW_D}!=null)?$F{MAX1_DAY_ACC_CRIT_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_CRIT_OUTFLOW_D}!=null)?$F{MAX2_DAY_ACC_CRIT_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="80" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_CRIT_OUTFLOW_D}!=null)?$F{MAX3_DAY_ACC_CRIT_OUTFLOW_D}:"-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="97" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="10" isUnderline="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_DAILY_TOTAL_OUTFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_DAY_ACC_CRIT_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_DAY_ACC_CRIT_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_DAY_ACC_CRIT_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_DAY_ACC_CRIT_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_DAY_ACC_CRIT_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_DAY_ACC_CRIT_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="695" y="97" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_DAY_ACC_CRIT_OUTFLOW_V}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_DAY_ACC_CRIT_OUTFLOW_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
			</textField>
		</band>
		<band height="105" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="20" forecolor="#000000" backcolor="#E6E6FA">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box leftPadding="6">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pAVAILABLE_IL_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="350" y="25" width="110" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[((java.lang.String)$P{pDictionary_Data}.get("pMIN_LABEL"))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="465" y="25" width="110" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[((java.lang.String)$P{pDictionary_Data}.get("p2EME_MIN_LABEL"))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="580" y="25" width="110" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[((java.lang.String)$P{pDictionary_Data}.get("p3EME_MIN_LABEL"))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="695" y="25" width="110" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[((java.lang.String)$P{pDictionary_Data}.get("pAVERAGE_LABEL"))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="350" y="42" width="110" height="16">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVLBL_ASSETS_D}!=null)?new SimpleDateFormat($P{pDateFormat}).format($F{MIN1_AVLBL_ASSETS_D}):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="465" y="42" width="110" height="16">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVLBL_ASSETS_D}!=null)?new SimpleDateFormat($P{pDateFormat}).format($F{MIN2_AVLBL_ASSETS_D}):"-"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField" x="580" y="42" width="110" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVLBL_ASSETS_D}!=null)?new SimpleDateFormat($P{pDateFormat}).format($F{MIN3_AVLBL_ASSETS_D}):"-"]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="59" width="820" height="20" backcolor="#BFBFBF">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
				</reportElement>
				<textField>
					<reportElement key="textField" x="5" y="0" width="810" height="20" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTOTAL_LABEL")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="350" y="0" width="110" height="17"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN1_AVLBL_ASSETS_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MIN1_AVLBL_ASSETS_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="466" y="0" width="110" height="17"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN2_AVLBL_ASSETS_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MIN2_AVLBL_ASSETS_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField pattern="###" isBlankWhenNull="true">
					<reportElement key="textField" x="580" y="0" width="110" height="17"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MIN3_AVLBL_ASSETS_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MIN3_AVLBL_ASSETS_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField pattern="###" isBlankWhenNull="true">
					<reportElement key="textField" x="695" y="0" width="110" height="17"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_AVLBL_ASSETS_V}!=null)?($P{pIlmUtil}.formatCurrency($F{AVG_AVLBL_ASSETS_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" positionType="Float" x="0" y="68" width="816" height="16">
					<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="p_ENTITY_ID">
					<subreportParameterExpression><![CDATA[$F{ENTITY_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE">
					<subreportParameterExpression><![CDATA[$P{pValue_Date}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE_END">
					<subreportParameterExpression><![CDATA[$P{pValue_DateEnd}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CCY">
					<subreportParameterExpression><![CDATA[$F{CCY}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ILM_GROUP_ID">
					<subreportParameterExpression><![CDATA[$F{ILM_GROUP_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CCY_MULTIPLIER_VALUE">
					<subreportParameterExpression><![CDATA[$F{CCY_MULTIPLIER_VALUE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_Dictionary_Data">
					<subreportParameterExpression><![CDATA[$P{pDictionary_Data}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ILM_UTIL">
					<subreportParameterExpression><![CDATA[$P{pIlmUtil}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CURRENCY_PATTERN">
					<subreportParameterExpression><![CDATA[$P{pCurrencyPattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_SERIES_IDENTIFIER">
					<subreportParameterExpression><![CDATA[$P{pSeriesIdentifier}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE_MIN1">
					<subreportParameterExpression><![CDATA[$F{MIN1_AVLBL_ASSETS_D}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE_MIN2">
					<subreportParameterExpression><![CDATA[$F{MIN2_AVLBL_ASSETS_D}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE_MIN3">
					<subreportParameterExpression><![CDATA[$F{MIN3_AVLBL_ASSETS_D}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_DB_LINK">
					<subreportParameterExpression><![CDATA[$P{pDBLink}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_CB_GROUP_ID">
					<subreportParameterExpression><![CDATA[$F{CENTRAL_BANK_GROUP_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_GC_GROUP_ID">
					<subreportParameterExpression><![CDATA[$F{GLOBAL_GROUP_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_HOST_ID">
					<subreportParameterExpression><![CDATA[$P{pHost_Id}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_ROLE_ID">
					<subreportParameterExpression><![CDATA[$P{pRoleId}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{pSubRepILMIntradayLiquidity}]]></subreportExpression>
			</subreport>
		</band>
		<band height="75" splitType="Prevent">
			<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="INTRADAY_THROUGHPUT_LABEL" mode="Opaque" x="0" y="0" width="450" height="18" forecolor="#000000" backcolor="#E6E6FA">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box leftPadding="6">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pINTRADAY_THROUGHPUT_LABEL")]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement key="INTRADAY_THROUGHPUT_LABEL_GRP" mode="Opaque" x="240" y="0" width="578" height="18" forecolor="#CCCCFF" backcolor="#E6E6FA">
					<printWhenExpression><![CDATA[!$P{pILMReportType}.equals("ILM_GROUP_REPORT")]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Dotted"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement key="textField" positionType="Float" x="20" y="25" width="60" height="17" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTIME_LABEL")]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement positionType="Float" x="18" y="46" width="190" height="16">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
					<datasetRun subDataset="dataset1">
						<datasetParameter name="ppENTITY_ID">
							<datasetParameterExpression><![CDATA[$F{ENTITY_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppCCY">
							<datasetParameterExpression><![CDATA[$F{CCY}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppILM_GROUP_ID">
							<datasetParameterExpression><![CDATA[$F{INFLOW_OUTFLOW_SUM_GROUP_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppSERIES_IDENTIFIER">
							<datasetParameterExpression><![CDATA[$P{pSeriesIdentifier}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppDBLink">
							<datasetParameterExpression><![CDATA[$P{pDBLink}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppVALUE_DATE">
							<datasetParameterExpression><![CDATA[$P{pValue_Date}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppVALUE_DATE_END">
							<datasetParameterExpression><![CDATA[$P{pValue_DateEnd}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppHOST_ID">
							<datasetParameterExpression><![CDATA[$P{pHost_Id}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="ppROLE_ID">
							<datasetParameterExpression><![CDATA[$P{pRoleId}]]></datasetParameterExpression>
						</datasetParameter>
					</datasetRun>
					<jr:listContents height="16" width="190">
						<textField>
							<reportElement x="0" y="0" width="64" height="16"/>
							<textElement textAlignment="Center" verticalAlignment="Middle"/>
							<textFieldExpression class="java.lang.String"><![CDATA[$F{TIME}]]></textFieldExpression>
						</textField>
						<textField pattern="##0.0 %">
							<reportElement x="54" y="0" width="63" height="16"/>
							<textElement textAlignment="Center" verticalAlignment="Middle"/>
							<textFieldExpression class="java.lang.String"><![CDATA[$F{INFLOW}]]></textFieldExpression>
						</textField>
						<textField pattern="##0.0 %">
							<reportElement x="127" y="0" width="63" height="16"/>
							<textElement textAlignment="Center" verticalAlignment="Middle"/>
							<textFieldExpression class="java.lang.String"><![CDATA[$F{OUTFLOW}]]></textFieldExpression>
						</textField>
					</jr:listContents>
				</jr:list>
			</componentElement>
			<textField>
				<reportElement key="textField" positionType="Float" x="61" y="25" width="90" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pINFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="131" y="25" width="90" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pOUTFLOW_LABEL")]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" positionType="Float" x="240" y="42" width="450" height="19">
					<printWhenExpression><![CDATA[$P{pILMReportType}.equals("ILM_GROUP_REPORT") && $F{COLLECT_NET_CUM_POS}.equals("Y")]]></printWhenExpression>
				</reportElement>
				<subreportParameter name="P_HOST_ID">
					<subreportParameterExpression><![CDATA[$P{pHost_Id}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ENTITY_ID">
					<subreportParameterExpression><![CDATA[$F{ENTITY_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE">
					<subreportParameterExpression><![CDATA[$P{pValue_Date}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_VALUE_DATE_END">
					<subreportParameterExpression><![CDATA[$P{pValue_DateEnd}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_DBLink">
					<subreportParameterExpression><![CDATA[$P{pDBLink}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_CCY">
					<subreportParameterExpression><![CDATA[$F{CCY}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ILM_GROUP_ID">
					<subreportParameterExpression><![CDATA[$F{ILM_GROUP_ID}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_Dictionary_Data">
					<subreportParameterExpression><![CDATA[$P{pDictionary_Data}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="p_ILM_UTIL">
					<subreportParameterExpression><![CDATA[$P{pIlmUtil}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_CURRENCY_PATTERN">
					<subreportParameterExpression><![CDATA[$P{pCurrencyPattern}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_CCY_MULTIPLIER_VALUE">
					<subreportParameterExpression><![CDATA[$F{CCY_MULTIPLIER_VALUE}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="P_ROLE_ID">
					<subreportParameterExpression><![CDATA[$P{pRoleId}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{pSubRepILMCumulativeBalanceGraph}]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement key="textField" positionType="Float" x="240" y="59" width="582" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[$P{pILMReportType}.equals("ILM_GROUP_REPORT") && $F{COLLECT_NET_CUM_POS}.equals("N") && $F{ENTITY_ID}.equals("All")]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pNET_CUMULATIVE_POSITION_DATA_CANNOT_BE_SHOWN")]]></textFieldExpression>
			</textField>
		</band>
		<band height="50">
			<printWhenExpression><![CDATA[$F{TOTAL_ROWS} == null ||$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="50" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pNO_DATA_FOUND")]]></textFieldExpression>
			</textField>
		</band>
		<band height="1">
			<printWhenExpression><![CDATA[$F{CURRENT_ROW} != $F{TOTAL_ROWS}]]></printWhenExpression>
			<break>
				<reportElement x="0" y="0" width="100" height="1"/>
			</break>
		</band>
	</detail>
	<pageFooter>
		<band height="27" splitType="Stretch">
			<line>
				<reportElement key="line" x="0" y="3" width="820" height="1">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="615" y="8" width="170" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " " +$P{pDictionary_Data}.get("pOF_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="790" y="8" width="36" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="10" y="8" width="209" height="19" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement key="textField-3" x="310" y="0" width="240" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL")]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
