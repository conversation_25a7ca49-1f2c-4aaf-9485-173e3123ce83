<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IntradayBalanceGraph" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="P_CCY" class="java.lang.String"/>
	<parameter name="P_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_ILM_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_DBLINK" class="java.lang.String"/>
	<parameter name="P_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="P_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<parameter name="P_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="P_MAX_POS_NET_CUM_D" class="java.lang.String"/>
	<parameter name="P_MAX_POS_NET_CUM_V" class="java.lang.Double"/>
	<parameter name="P_MIN_NEG_NET_CUM_D" class="java.lang.String"/>
	<parameter name="P_MIN_NEG_NET_CUM_V" class="java.lang.Double"/>
	<queryString>
		<![CDATA[SELECT DECODE (TIME, '00:00', '24:00', TIME) AS TS_END_TXT, avg_net_cum_pos AS ACTUAL_NET_CUM_POS,
		 				null AS ACTUAL_CR_TOTAL, 
                        null AS ACTUAL_DR_TOTAL, 
                        $P{p_ILM_GROUP_ID} AS ILM_GROUP_ID
					FROM (
						SELECT TO_CHAR(TO_DATE(EXTRACT( HOUR FROM timedata)||':'||EXTRACT( MINUTE FROM timedata), 'HH24:MI'), 'HH24:MI') TIME , avg_net_cum_pos, count_days FROM TABLE (pkg_ilm_rep.fn_avg_net_cum_pos (
						        $P{P_HOST_ID} ,
						        $P{p_ENTITY_ID},
			                    $P{P_CCY},
			                    $P{p_ILM_GROUP_ID},
			                    $P{P_SERIES_IDENTIFIER},
			                    $P{p_DBLINK},
			                    $P{p_VALUE_DATE},
			                    $P{p_VALUE_DATE_END},
			                    $P{P_ROLE_ID}  
						    ))
                    )]]>
	</queryString>
	<field name="TS_END_TXT" class="java.lang.String"/>
	<field name="ACTUAL_NET_CUM_POS" class="java.math.BigDecimal"/>
	<field name="ACTUAL_CR_TOTAL" class="java.math.BigDecimal"/>
	<field name="ACTUAL_DR_TOTAL" class="java.math.BigDecimal"/>
	<field name="ILM_GROUP_ID" class="java.lang.String"/>
	<group name="Group1" isStartNewPage="true">
		<groupExpression><![CDATA[$F{ILM_GROUP_ID}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="Group2">
		<groupExpression><![CDATA[$F{TS_END_TXT}]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="31" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="535" height="30"/>
				<textElement textAlignment="Center">
					<font size="14" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{P_Dictionary_Data}.get("pNet_Cumulative_Balance_LABEL")]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="304" splitType="Stretch">
			<multiAxisChart>
				<chart isShowLegend="false" evaluationTime="Group" evaluationGroup="Group1">
					<reportElement key="element-1" stretchType="RelativeToTallestObject" mode="Transparent" x="1" y="0" width="534" height="300">
						<printWhenExpression><![CDATA[1==1/*($F{ILM_GROUP_ID}!=null) ?  new Boolean(true) :new Boolean(false)*/]]></printWhenExpression>
					</reportElement>
					<chartTitle/>
					<chartSubtitle/>
					<chartLegend textColor="#000000" backgroundColor="#FFFFFF" position="Bottom"/>
				</chart>
				<multiAxisPlot>
					<plot/>
					<axis>
						<timeSeriesChart>
							<chart isShowLegend="false" evaluationTime="Group" evaluationGroup="Group1" customizerClass="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.AreaChartCustomizer">
								<reportElement key="element-351" positionType="Float" x="-1800" y="-20000" width="270" height="275" backcolor="#FFFFFF"/>
								<chartTitle color="#000000"/>
								<chartSubtitle color="#000000"/>
								<chartLegend textColor="#000000" backgroundColor="#FFFFFF" position="Bottom"/>
							</chart>
							<timeSeriesDataset timePeriod="Minute">
								<dataset resetType="Group" resetGroup="Group1" incrementType="Group" incrementGroup="Group2"/>
								<timeSeries>
									<seriesExpression><![CDATA[$P{P_Dictionary_Data}.get("pNet_Cumulative_Balance_LABEL")]]></seriesExpression>
									<timePeriodExpression><![CDATA[$P{p_ILM_UTIL}.parseDate($F{TS_END_TXT})]]></timePeriodExpression>
									<valueExpression><![CDATA[$F{ACTUAL_NET_CUM_POS}]]></valueExpression>
								</timeSeries>
							</timeSeriesDataset>
							<timeSeriesPlot isShowShapes="false">
								<plot>
									<seriesColor seriesOrder="0" color="#000000"/>
								</plot>
								<timeAxisLabelExpression><![CDATA[$P{P_Dictionary_Data}.get("pTIME_LABEL")]]></timeAxisLabelExpression>
								<timeAxisFormat>
									<axisFormat axisLineColor="#000000"/>
								</timeAxisFormat>
								<valueAxisLabelExpression><![CDATA[$P{P_Dictionary_Data}.get("pBALANCE_LABEL")]]></valueAxisLabelExpression>
								<valueAxisFormat>
									<axisFormat labelColor="#000000" tickLabelColor="#000000" axisLineColor="#000000"/>
								</valueAxisFormat>
							</timeSeriesPlot>
						</timeSeriesChart>
					</axis>
				</multiAxisPlot>
			</multiAxisChart>
		</band>
	</pageHeader>
</jasperReport>
