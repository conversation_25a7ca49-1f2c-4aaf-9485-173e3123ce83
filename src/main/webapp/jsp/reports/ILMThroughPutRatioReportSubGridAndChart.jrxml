<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ThroughputRatio_subreport2_subreport1" pageWidth="802" pageHeight="555" orientation="Landscape" columnWidth="802" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="a7848e64-9f77-4774-8eda-843d4f56e543">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new Boolean($V{REPORT_COUNT}.intValue()%2==0)]]></conditionExpression>
			<style backcolor="#EFF7FF"/>
		</conditionalStyle>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 5_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 6">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 6_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 6_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 6_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 7">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 7_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 7_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 7_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 8">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 8_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 8_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 8_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="style1" mode="Opaque">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESH_COLOR}.equals("R")]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#FF3300"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESH_COLOR}.equals("G")]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#009900" fill="Solid" hAlign="Left" vAlign="Top" pattern=""/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{THRESH_COLOR}.equals("Y")]]></conditionExpression>
			<style mode="Opaque" forecolor="#FFFFFF" backcolor="#CCCCCC"/>
		</conditionalStyle>
	</style>
	<subDataset name="New Dataset 1" uuid="57f94574-1230-4390-9928-6379815e110f">
		<parameter name="ilm_group" class="java.lang.String">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="valueDateISO" class="java.lang.String"/>
		<parameter name="scenario_id" class="java.lang.String"/>
		<parameter name="swtUtil" class="org.swallow.util.SwtUtil"/>
		<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
		<parameter name="p_currencyFormat" class="java.lang.String"/>
		<parameter name="p_CalculateAs" class="java.lang.String"/>
		<parameter name="p_role" class="java.lang.String">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<queryString>
			<![CDATA[SELECT ENTITY_ID,
       CCY_CODE as CURRENCY_CODE,
       ILM_GRP_ID as ILM_GROUP_ID,
       ACT_INFLOW as INFLOW,
       ACT_OUTFLOW as OUTFLOW,
       INFLOW_FC,
       OUTFLOW_FC,
       UNSETTL_OUTFLOW AS UNSETTLED,
       TIMESLOT_END,
       OUT_PERCENT,
       --gr.THRESH1_PERCENT,
       THRESHOLD1_TIME AS THRESHOLD1_TIME,
       THRESHOLD2_TIME AS THRESHOLD2_TIME,
       THRESHOLD, THRESH_COLOR
  FROM TABLE (pkg_ilm_rep.FN_THRU_RATIO_REPORT_DETAIL ($P{ilm_group}, $P{scenario_id}, TO_DATE ($P{valueDateISO}, 'YYYY-MM-DD'),$P{p_role} , NULL, $P{p_CalculateAs}))]]>
		</queryString>
		<field name="ENTITY_ID" class="java.lang.String"/>
		<field name="CURRENCY_CODE" class="java.lang.String"/>
		<field name="ILM_GROUP_ID" class="java.lang.String"/>
		<field name="INFLOW" class="java.math.BigDecimal"/>
		<field name="OUTFLOW" class="java.math.BigDecimal"/>
		<field name="INFLOW_FC" class="java.math.BigDecimal"/>
		<field name="OUTFLOW_FC" class="java.math.BigDecimal"/>
		<field name="UNSETTLED" class="java.math.BigDecimal"/>
		<field name="TIMESLOT_END" class="java.lang.String"/>
		<field name="OUT_PERCENT" class="java.math.BigDecimal"/>
		<field name="THRESHOLD1_TIME" class="java.lang.String"/>
		<field name="THRESHOLD2_TIME" class="java.lang.String"/>
		<field name="THRESHOLD" class="java.math.BigDecimal"/>
		<field name="THRESH_COLOR" class="java.lang.String"/>
	</subDataset>
	<parameter name="p_ILM_GRP" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="p_SCENARIO_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE" class="java.lang.String"/>
	<parameter name="chartImageBase64" class="java.lang.String"/>
	<parameter name="legendImageBase64" class="java.lang.String" isForPrompting="false"/>
	<parameter name="p_ChartsData" class="java.util.HashMap"/>
	<parameter name="swtUtil" class="org.swallow.util.SwtUtil"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_currencyFormat" class="java.lang.String"/>
	<parameter name="p_CalculateAs" class="java.lang.String"/>
	<parameter name="p_role" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[select 1 from dual]]>
	</queryString>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="54" splitType="Stretch">
			<componentElement>
				<reportElement uuid="abcc89e0-d67a-4afc-b0c0-c1b1bc2b00db" key="table 8" style="table 8" x="0" y="0" width="802" height="47"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="New Dataset 1" uuid="a691515c-27d7-4ebb-b77e-6e7ef098aca7">
						<datasetParameter name="ilm_group">
							<datasetParameterExpression><![CDATA[$P{p_ILM_GRP}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="valueDateISO">
							<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="scenario_id">
							<datasetParameterExpression><![CDATA[$P{p_SCENARIO_ID}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_Dictionary_Data">
							<datasetParameterExpression><![CDATA[$P{p_Dictionary_Data}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_currencyFormat">
							<datasetParameterExpression><![CDATA[$P{p_currencyFormat}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_CalculateAs">
							<datasetParameterExpression><![CDATA[$P{p_CalculateAs}]]></datasetParameterExpression>
						</datasetParameter>
						<datasetParameter name="p_role">
							<datasetParameterExpression><![CDATA[$P{p_role}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:column uuid="992145d0-add4-4877-b7d7-780daa3f2202" width="72">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="a0e9df91-243f-404e-a1a2-23cbfcb10966" x="0" y="0" width="72" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_entity")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="6ade2190-5a27-4c87-9cc4-5002153380b0" style="style1" x="0" y="0" width="72" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ENTITY_ID}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="783a7a3d-9706-4b3c-a603-fe254978f223" width="47">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="5000d021-e7ce-41e5-a2f6-c47e4804a2f4" x="0" y="0" width="47" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_ccy")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="438c31bf-154f-4666-8978-831735e88def" style="style1" x="0" y="0" width="47" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{CURRENCY_CODE}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="48643663-a717-4340-a9f8-31acef85f5a7" width="117">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="b21c183f-7f09-4e8e-ae65-36978f15bba0" x="0" y="0" width="117" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_ilm_group")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="63caa4dc-ee96-47eb-81e4-78ed75c5b2e2" style="style1" x="0" y="0" width="117" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{ILM_GROUP_ID}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:columnGroup uuid="1d58da2a-1c04-4ec9-babc-87fbacd11056" width="180">
						<jr:columnHeader height="30" rowSpan="1">
							<textField>
								<reportElement uuid="ca8e0925-7d5c-41b8-aac1-d4f7ea2d529f" style="table 8_CH" x="0" y="0" width="180" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forecasted")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:column uuid="5a20e51f-49af-4ac9-97a1-1d544ce0b8b5" width="90">
							<jr:columnHeader style="table 8_CH" height="30" rowSpan="1">
								<textField>
									<reportElement uuid="a19ff422-2b51-400d-a5ff-ed76ff588a9b" x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcinf")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="3401c151-6f3f-4486-b3fe-f4b792c55170" style="style1" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{INFLOW_FC}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="dc2483ab-f6ae-44f9-88e9-0384afee80cc" width="90">
							<jr:columnHeader style="table 8_CH" height="30" rowSpan="1">
								<textField>
									<reportElement uuid="b71c1cf0-30c0-45bd-89c7-6f15056b7e0d" x="0" y="0" width="90" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcout")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="5d90d87a-c2fa-4799-af7e-a3090c409033" style="style1" x="0" y="0" width="90" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{OUTFLOW_FC}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:columnGroup uuid="64fcdced-5ea4-4453-bb3d-a71c925cae5f" width="161">
						<jr:columnHeader height="30" rowSpan="1">
							<textField>
								<reportElement uuid="ebdd39a2-2212-4c62-9448-d3baf9ac2c14" style="table 8_CH" x="0" y="0" width="161" height="30"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_actuals")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:column uuid="8a09d860-fa9c-4839-8f05-1be09a381302" width="80">
							<jr:columnHeader style="table 8_CH" height="30" rowSpan="1">
								<textField>
									<reportElement uuid="3f405143-192e-4213-ad96-1d691ef7b36b" x="0" y="0" width="80" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcinf")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="41705030-60e6-4d68-ac9e-a05e1b298aa7" style="style1" x="0" y="0" width="80" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{INFLOW}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column uuid="77a87463-345b-4cf7-b0e5-97ac7335f64c" width="81">
							<jr:columnHeader style="table 8_CH" height="30" rowSpan="1">
								<textField>
									<reportElement uuid="e59d747c-968b-42b6-8d11-728a746ed6bf" x="0" y="0" width="81" height="30"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_forcout")]]></textFieldExpression>
								</textField>
							</jr:columnHeader>
							<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
								<textField>
									<reportElement uuid="7e6942ee-6509-4b82-9792-8b7232e3ae6e" style="style1" x="0" y="0" width="80" height="20"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{OUTFLOW}, $P{p_currencyFormat})]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:columnGroup>
					<jr:column uuid="c578e63e-4916-4cbf-8f5c-e29c2204e357" width="80">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="acd0175a-82ef-42b2-b47e-070d7f03dd15" x="0" y="0" width="80" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_unsetout")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="7195f4fa-5ab5-4c9b-a4cc-ada5e5869dfa" style="style1" x="0" y="0" width="80" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{swtUtil}.formatCurrency($F{CURRENCY_CODE},$F{UNSETTLED}, $P{p_currencyFormat})]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="7b06768d-9803-44fc-b51b-55f8e58ba508" width="45">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="8d8d6648-5d17-4963-b655-e6493712710d" x="0" y="0" width="45" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_timeslot")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField>
								<reportElement uuid="0ec9f8e2-adb9-438c-aa89-5df4ac8545f6" style="style1" x="0" y="0" width="45" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{TIMESLOT_END}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="9c66967c-75a6-42b1-bff2-a08c88ad36f5" width="61">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="cd3af590-e0c3-4ef8-8981-8ff32dbde2a7" x="0" y="0" width="61" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_out_perc")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField pattern="###0.00">
								<reportElement uuid="a2f08269-6d9a-42b0-9c10-99a057901c4c" style="style1" x="0" y="0" width="61" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{OUT_PERCENT} == null?"-":$F{OUT_PERCENT}.toString()+"%"]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column uuid="687096dd-05e3-4d0e-a7d4-119b16946975" width="47">
						<jr:columnHeader style="table 8_CH" height="60" rowSpan="2">
							<textField>
								<reportElement uuid="8afe5b68-4ac5-4ee8-8abc-46d1138e71ca" x="0" y="0" width="47" height="60"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$P{p_Dictionary_Data}.get("p_threshold")]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="table 8_TD" height="20" rowSpan="1">
							<textField pattern="###0.00">
								<reportElement uuid="034282c1-a433-48eb-89c3-c2d4336719ab" style="style1" x="3" y="0" width="44" height="20"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="8"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{THRESHOLD} == null?"-":$F{THRESHOLD}.toString()+"%"]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
		<band height="443">
			<image scaleImage="FillFrame">
				<reportElement uuid="f53cdec8-f66b-4dd1-bb59-b4bd7eb623df" x="0" y="0" width="802" height="443"/>
				<imageExpression><![CDATA[new ByteArrayInputStream(new org.apache.commons.codec.binary.Base64().decodeBase64($P{p_ChartsData}.get($P{p_ILM_GRP}).toString().getBytes("UTF-8")))]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
