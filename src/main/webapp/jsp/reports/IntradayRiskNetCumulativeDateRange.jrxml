<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DateRangeLiquidityRiskReport" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="800" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="61"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="org.swallow.util.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="pHost_Id" class="java.lang.String"/>
	<parameter name="pEntity_Id" class="java.lang.String"/>
	<parameter name="pCurrency_Code" class="java.lang.String"/>
	<parameter name="pILMGroup_Id" class="java.lang.String"/>
	<parameter name="pDBLink" class="java.lang.String"/>
	<parameter name="pRoleId" class="java.lang.String"/>
	<parameter name="pDateFormat" class="java.lang.String"/>
	<parameter name="pCurrencyPattern" class="java.lang.String"/>
	<parameter name="pValue_Date" class="java.util.Date"/>
	<parameter name="pValue_DateEnd" class="java.util.Date"/>
	<parameter name="sdf" class="java.text.SimpleDateFormat"/>
	<parameter name="pSeriesIdentifier" class="java.lang.String"/>
	<parameter name="pILMReportType" class="java.lang.String"/>
	<parameter name="pInflow_Outflow_Sum" class="java.lang.String"/>
	<parameter name="pCurrencyName" class="java.lang.String"/>
	<parameter name="pEntityName" class="java.lang.String"/>
	<parameter name="pUseCcyMultiplier" class="java.lang.String"/>
	<parameter name="pScenarioName" class="java.lang.String"/>
	<parameter name="pDictionary_Data" class="java.util.HashMap"/>
	<parameter name="pIlmUtil" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<queryString>
		<![CDATA[WITH headers
     AS (SELECT HOST_ID,
                ENTITY_ID,
                ENTITY_NAME,
                CCY,
                CCY_NAME,
                ILM_SCENARIO_ID,
                ILM_SCENARIO_NAME,
                DECODE ( $P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER, 'N')
                   CCY_MULTIPLIER,
                DECODE ( $P{pUseCcyMultiplier}, 'Y', CCY_MULTIPLIER_VALUE, 1)
                   CCY_MULTIPLIER_VALUE,
                MISSING_D_START,
                MISSING_D_END,
                COLLECT_NET_CUM_POS,
                TOTAL_ROWS,
				$P{pSeriesIdentifier} pSeriesIdentifier,
				$P{pDBLink} pDBLink,
				$P{pRoleId} pRoleId,
				$P{pValue_Date} pValue_Date,
				$P{pValue_DateEnd} pValue_DateEnd,
				$P{pDateFormat} pDateFormat
           FROM TABLE (pkg_ilm_rep.fn_header ( $P{pHost_Id},
                            $P{pEntity_Id},
                            $P{pCurrency_Code},
                            $P{pILMGroup_Id},
                            $P{pSeriesIdentifier},
                            $P{pRoleId},
                            $P{pDBLink},
                            $P{pValue_Date},
                            $P{pValue_DateEnd}))),
                                         
DATA AS(SELECT headers.ENTITY_ID,
       ENTITY_NAME,
       headers.CCY,
       CCY_NAME,
       headers.ILM_SCENARIO_ID,TOTAL_ROWS,
       ILM_SCENARIO_NAME,
       data.main_agent MAIN_AGENT,
       TO_CHAR (data.MAX1_POS_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX1_POS_NET_CUM_D,
       TO_CHAR (data.MAX2_POS_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX2_POS_NET_CUM_D,
       TO_CHAR (data.MAX3_POS_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX3_POS_NET_CUM_D,
       data.MAX1_POS_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX1_POS_NET_CUM_V,
       data.MAX2_POS_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX2_POS_NET_CUM_V,
       data.MAX3_POS_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX3_POS_NET_CUM_V,
       TO_CHAR (data.MAX1_NEG_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX1_NEG_NET_CUM_D,
       TO_CHAR (data.MAX2_NEG_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX2_NEG_NET_CUM_D,
       TO_CHAR (data.MAX3_NEG_NET_CUM_D, UPPER ( pDateFormat) || ' HH24:MI')
          AS MAX3_NEG_NET_CUM_D,
       data.MAX1_NEG_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX1_NEG_NET_CUM_V,
       data.MAX2_NEG_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX2_NEG_NET_CUM_V,
       data.MAX3_NEG_NET_CUM_V / CCY_MULTIPLIER_VALUE AS MAX3_NEG_NET_CUM_V,
       data.AVG_POS_NET_CUM_V / CCY_MULTIPLIER_VALUE AS AVG_POS_NET_CUM_V,
       data.AVG_NEG_NET_CUM_V / CCY_MULTIPLIER_VALUE AS AVG_NEG_NET_CUM_V,
       CCY_MULTIPLIER,
       CCY_MULTIPLIER_VALUE,
       rownum current_row, 
       DECODE (MAIN_AGENT, NULL, 'NO_DATA', 'OK') DATA_STATE,
       MIN(headers.missing_d_start) OVER () AS MISSING_D_START, 
       MAX(headers.missing_d_end) OVER() AS MISSING_D_END, 
       COUNT(*) OVER (PARTITION BY DECODE (MAIN_AGENT, NULL, 'NO_DATA', 'OK')) GL_DATA_STATE,
       COUNT(*) OVER () GL_DATA_ALL
	
  FROM headers LEFT JOIN TABLE (pkg_ilm_rep.fn_rank_net_cum_pos (
                                               headers.host_id,
                                               headers.entity_id,
                                               headers.ccy,
                                               NULL,
                                               pSeriesIdentifier,
                                               pDBLink,
                                               pValue_Date,
                                               pValue_DateEnd, 
                                               pRoleId, 
                                               'Y')) data ON (1=1)
      )
SELECT * FROM DATA
-- Keep only data where MAIN_AGENT is not null OR keep the last row if ALL MAIN_AGENTs are NULL (Refering to GL_DATA_STATE and GL_DATA_ALL)                                               
WHERE MAIN_AGENT IS NOT NULL 
  OR (GL_DATA_STATE=GL_DATA_ALL AND ROWNUM<2)
 ORDER BY CCY,MAIN_AGENT]]>
	</queryString>
	<field name="ENTITY_ID" class="java.lang.String"/>
	<field name="ENTITY_NAME" class="java.lang.String"/>
	<field name="CCY" class="java.lang.String"/>
	<field name="CCY_NAME" class="java.lang.String"/>
	<field name="ILM_SCENARIO_ID" class="java.lang.String"/>
	<field name="ILM_SCENARIO_NAME" class="java.lang.String"/>
	<field name="CCY_MULTIPLIER" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX2_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX3_POS_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX2_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX3_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="AVG_POS_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX1_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX2_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX3_NEG_NET_CUM_D" class="java.lang.String"/>
	<field name="MAX1_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX2_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="MAX3_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="AVG_NEG_NET_CUM_V" class="java.lang.Double"/>
	<field name="CURRENT_ROW" class="java.lang.Integer"/>
	<field name="MISSING_D_START" class="java.util.Date"/>
	<field name="MISSING_D_END" class="java.util.Date"/>
	<field name="MAIN_AGENT" class="java.lang.String"/>
	<field name="DATA_STATE" class="java.lang.String"/>
	<field name="TOTAL_ROWS" class="java.lang.Integer"/>
	<group name="NetCumulativePosition" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{CCY}+""+$F{MAIN_AGENT}]]></groupExpression>
		<groupHeader>
			<band height="120" splitType="Stretch">
				<printWhenExpression><![CDATA[!$F{DATA_STATE}.equals("NO_DATA")]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="20" forecolor="#000000" backcolor="#E6E6FA">
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box leftPadding="6">
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="11" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$F{CCY}+": "+$F{MAIN_AGENT}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="59" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isUnderline="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_POSITIVE_NET_CUMULATIVE_POSITION_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="350" y="25" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font fontName="SansSerif" size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pMAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="465" y="25" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font fontName="SansSerif" size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p2EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="580" y="25" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font fontName="SansSerif" size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("p3EME_MAX_LABEL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="695" y="25" width="110" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
						<property name="local_mesure_unity" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right">
						<font fontName="SansSerif" size="12"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pAVERAGE_LABEL")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="350" y="42" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_D}!=null)?$F{MAX1_POS_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="465" y="42" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_POS_NET_CUM_D}!=null)?$F{MAX2_POS_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="580" y="42" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_POS_NET_CUM_D}!=null)?$F{MAX3_POS_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="350" y="59" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="465" y="59" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="580" y="59" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_POS_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="695" y="59" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_POS_NET_CUM_V}!=null && (($F{MAX1_POS_NET_CUM_D}!=null) ||($F{MAX2_POS_NET_CUM_D}!=null)||($F{MAX3_POS_NET_CUM_D}!=null)))?($P{pIlmUtil}.formatCurrency($F{AVG_POS_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="350" y="85" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_D}!=null)?$F{MAX1_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="465" y="85" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_NEG_NET_CUM_D}!=null)?$F{MAX2_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="580" y="85" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_NEG_NET_CUM_D}!=null)?$F{MAX3_NEG_NET_CUM_D}:"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="102" width="320" height="17" forecolor="#000000" backcolor="#FFFFFF">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isUnderline="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pLARGEST_POSITIVE_NET_CUMULATIVE_NEGATIVE_LABEL")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="350" y="102" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX1_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX1_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="465" y="102" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX2_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX2_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="580" y="102" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{MAX3_NEG_NET_CUM_V}!=null)?($P{pIlmUtil}.formatCurrency($F{MAX3_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField" x="695" y="102" width="110" height="17">
						<property name="local_mesure_unity" value="pixel"/>
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font isBold="false" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_NEG_NET_CUM_V}!=null && (($F{MAX1_NEG_NET_CUM_D}!=null) ||($F{MAX2_NEG_NET_CUM_D}!=null)||($F{MAX3_NEG_NET_CUM_D}!=null)))?($P{pIlmUtil}.formatCurrency($F{AVG_NEG_NET_CUM_V},$P{pCurrencyPattern})):"-"]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="140" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" mode="Opaque" x="0" y="0" width="820" height="30" forecolor="#000000" backcolor="#E6E6FA"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pTitleIntraDayRisk")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="564" y="30" width="136" height="20"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORT_DATE_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="dd-MMM-yyyy HH:mm:ss" isBlankWhenNull="false">
				<reportElement key="textField-1" x="700" y="30" width="120" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}+" HH:mm:ss").format(new Date())]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="40" width="140" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pENTITY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="40" width="159" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pEntity_Id}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="360" y="40" width="195" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pEntityName}.equals("All")?"":$P{pEntityName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="56" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCURRENCY_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="56" width="159" height="16">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pCurrency_Code}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="360" y="56" width="195" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pCurrencyName}.equals("All")?"":$P{pCurrencyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="72" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pScenario_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="72" width="200" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pSeriesIdentifier}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="360" y="72" width="225" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pScenarioName}.equals("Standard")?"":$P{pScenarioName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="88" width="157" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pGROUP_LABEL")]]></textFieldExpression>
			</textField>
			<textField pattern="###" isBlankWhenNull="true">
				<reportElement key="textField" x="150" y="88" width="159" height="16"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCorrespondentBankGroups")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="10" y="104" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pREPORTING_PERIOD_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="150" y="104" width="80" height="16">
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_Date})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="150" y="120" width="120" height="17">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format($P{pValue_DateEnd})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField" positionType="Float" x="486" y="120" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_LABEL")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="textField-1" x="676" y="120" width="120" height="16">
					<property name="local_mesure_unitwidth" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{pDictionary_Data}.get("pCCY_MULTIPLIER_"+$F{CCY_MULTIPLIER})]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<pageFooter>
		<band height="18" splitType="Stretch">
			<line>
				<reportElement key="line" x="0" y="0" width="820" height="1">
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
			</line>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="615" y="2" width="170" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Page " + $V{PAGE_NUMBER} + " " +$P{pDictionary_Data}.get("pOF_LABEL")]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" pattern="" isBlankWhenNull="false">
				<reportElement key="textField" x="790" y="2" width="36" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="10" y="2" width="209" height="16" forecolor="#000000" backcolor="#FFFFFF">
					<property name="local_mesure_unity" value="pixel"/>
					<property name="local_mesure_unitheight" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[new SimpleDateFormat($P{pDateFormat}).format(new Date())]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="36">
			<textField>
				<reportElement key="textField-3" x="295" y="4" width="240" height="32"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font size="12"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[($F{DATA_STATE}.equals("NO_DATA")) ? ($P{pDictionary_Data}.get("pNO_DATA_FOUND")+ "\r\n" +$P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL")) : ($P{pDictionary_Data}.get("pEND_OF_REPORT_LABEL"))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
