<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>


<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">

	var screenTitle = "";
	screenTitle = getMessage("schedReportHist.mainScreen.title", null);
	document.title = screenTitle;
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "schedReportHistory";

	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');

	var dbDate= "<%=SwtUtil.getSystemDateString() %>"
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var dateFormat = '${sessionScope.CDM.dateFormat}';

	var mainWindow= null;
	requestURL=requestURL.substring(0,idy+1) ;

	var menuAccessId = '${requestScope.menuAccessId}';
	var maintainAnyReportHistAccess = '${requestScope.maintainAnyReportHistAccess}';

	// This method is called when onload
	window.onload = function () {
		setTitleSuffix(document.forms[0]);
		setParentChildsFocus();
	};

	window.onunload = call;
	var menuAccessId = '${requestScope.menuAccessId}';
	// Set the label values
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();
	label["alert"] = new Array ();

	label["text"]["screen-title"] = "<fmt:message key="label.schedReportHist.title.window"/>";

	// Add button
	label["text"]["button-add"] = "<fmt:message key="button.add"/>";
	label["tip"]["button-add"] = "<fmt:message key="button.add"/>";

	// Change button
	label["text"]["button-change"] = "<fmt:message key="button.change"/>";
	label["tip"]["button-change"] = "<fmt:message key="button.change"/>";

	// View button
	label["text"]["button-view"] = "<fmt:message key="button.view"/>";
	label["tip"]["button-view"] = "<fmt:message key="button.view"/>";

	// Delete button
	label["text"]["button-delete"] = "<fmt:message key="button.delete"/>";
	label["tip"]["button-delete"] = "<fmt:message key="button.delete"/>";

	// Close button
	label["text"]["button-close"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

	// Alert messages
	label["alert"]["delete-confirm"] = "<fmt:message key="alert.deletion.confirm"/>";
	label["alert"]["delete-record"] = "<fmt:message key="confirm.delete"/>";
	label["alert"]["alert-noAccessToFeature"] = "<fmt:message key="alert.schedreporthist.noAccessToFeature"/>";
	label["alert"]["alert-fileNotFound"] = "<fmt:message key="alert.mail.fileNoutFound"/>";

	label["text"]["alert.validation.dateRange"] = "<fmt:message key="alert.validation.dateRange"/>";

	label["text"]["label-reportjob"] = "<fmt:message key="button.schedReportHist.reportjob"/>";
	label["tip"]["label-reportjob"] = "<fmt:message key="button.tooltip.schedReportHist.reportjob"/>";


	label["text"]["label-reporttype"] = "<fmt:message key="button.schedReportHist.reporttype"/>";
	label["tip"]["label-reporttype"] = "<fmt:message key="button.tooltip.schedReportHist.reporttype"/>";


	label["text"]["label-date"] = "<fmt:message key="button.schedReportHist.date"/>";
	label["tip"]["label-date"] = "<fmt:message key="tip.schedReportHist.runDate"/>";


	label["text"]["label-daterange"] = "<fmt:message key="button.schedReportHist.dateRange"/>";
	label["tip"]["label-daterange"] = "<fmt:message key="button.tooltip.schedReportHist.dateRange"/>";

	label["text"]["label-singledate"] = "<fmt:message key="button.schedReportHist.singleDate"/>";
	label["tip"]["label-singledate"] = "<fmt:message key="button.tooltip.schedReportHist.singleDate"/>";

	label["text"]["label-startDate"] = "<fmt:message key="button.schedReportHist.stratDate"/>";
	label["tip"]["label-startDate"] = "<fmt:message key="button.tooltip.schedReportHist.startDate"/>";

	label["text"]["label-endDate"] = "<fmt:message key="button.schedReportHist.endDate"/>";
	label["tip"]["label-endDate"] = "<fmt:message key="button.tooltip.schedReportHist.endDate"/>";


	label["text"]["button-download"] = "<fmt:message key="button.schedreporthist.download"/>";
	label["tip"]["button-download"] = "<fmt:message key="button.tooltip.schedreporthist.download"/>";

	label["text"]["button-resendmail"] = "<fmt:message key="button.schedreporthist.resendMail"/>";
	label["tip"]["button-resendmail"] = "<fmt:message key="button.tooltip.schedreporthist.resendMail"/>";

	label["text"]["button-details"] = "<fmt:message key="button.schedreporthist.details"/>";
	label["tip"]["button-details"] = "<fmt:message key="button.tooltip.schedreporthist.details"/>";

	label["text"]["button-deleteButton"] = "<fmt:message key="button.delete"/>";
	label["tip"]["button-deleteButton"] = "<fmt:message key="tooltip.deleteSeletedFile"/>";

	label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
	label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

	label["text"]["button-close"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

	/**
	 * openChildWindow
	 * @param methodName
	 * Method to load child screens
	 */
	function openChildWindow(actionMethod){

		var param = '/' + appName + '/schedReportHist.do?method='+actionMethod;
		mainWindow = window.open (param, 'schedReportHist','left=10,top=230,width=750,height=645,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');
		return false;
	}

	/**
	 * openChildWindow
	 * @param methodName
	 * Method to load child screens
	 */
	function openDistListScreen(scheduleId, filePath, fileId){
		var param = '/' + appName + '/scheduler.do?method=displayDistList';
		param+= '&scheduleId='+scheduleId;
		param+= '&fromSchedReportHist=true';
		param+= '&filePath='+filePath;
		param+= '&fileId='+fileId;
		mainWindow = window.open (param, 'schedReportHist','left=50,top=190,width=690,height=455,toolbar=0, resizable=yes, scrollbars=yes');
		return false;
	}

	/**
	 * Used to export report as  pdf, xls,csv format
	 */
	function onExport(fileId,actionMethod){

		document.getElementById('exportDataForm').action = 'schedReportHist.do?method='+ actionMethod;
		document.getElementById('fileId').value=fileId;
		document.forms[0].submit();
	}

	/**
	 * help
	 * This function opens the help screen
	 * @return none
	 */
	function help(){
		openWindow(buildPrintURL('print','SchedReportHist'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}

	/**
	 * callBack
	 * This function used refresh data grid
	 */
	function refreshGridData(){
		Main.refreshdetails();
	}

	/**
	 * closeChild
	 * This function used to close the child window
	 */
	function closeChild(){
		if(typeof(mainWindow) == window)
			mainWindow.close();
	}
</script>

<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" method="post" target="tmp">
	<input type="hidden" name="data" id="exportData" />
	<input type="hidden" name="screen" id="exportDataScreen"
		   value="<fmt:message key="label.schedreporthist.title.window"/>" />
	<input type="hidden" name="fileId" value="" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;" />
</body>
</html>