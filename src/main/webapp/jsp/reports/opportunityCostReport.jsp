<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<%@page import="org.swallow.util.LabelValueBean"%>
<html>
<head>
<title>
<fmt:message key="opportunityCostReport.mainScreen"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
<script language="JAVASCRIPT">
var dateSelected = false;
var cancelcloseElements = new Array(1);
var configScheduler ="${requestScope.configScheduler}";
var schedulerConfigXML = "${requestScope.schedulerConfigXML}";
var newOpportunityCostReportSchedConfig ="${requestScope.newOpportunityCostReportSchedConfig}";
var reportType = '${requestScope.reportType}'
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];

var headers = ["Keyword", "Description"];
var dataprovider = new Array();
function bodyOnLoad()
{
   if (configScheduler == "true"){
	   document.forms[0].newOpportunityCostReportSchedConfig.value=newOpportunityCostReportSchedConfig;
	   document.forms[0].reportType.value= reportType;
   }
   var dropBox1 = new SwSelectBox(document.forms[0].elements["reports.entityId"],document.getElementById("entityName"));
   var dropBox2 = new SwSelectBox(document.forms[0].elements["reports.id.currencyCode"],document.getElementById("currencyName"));
	if (configScheduler == "true") {
		loadCalendarWithKeyords();
	} else {
	  cal = new CalendarPopup("caldiv",false,"calFrame");
	  cal.offsetX = -3;
	  cal.offsetY = -97;
	}
}


function validateDatesFromReportConfig(){
	if(document.forms[0].elements['reports.fromDateAsString'].value == "" ){
		alert('<fmt:message key="alert.enterValidDate"/>');
	}else{
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/ilmReport.do?method=validateDates";
		requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["reports.entityId"].value +"&fromDate=" + document.forms[0].elements['reports.fromDateAsString'].value + "&toDate=";
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		var result = new String(oXMLHTTP.responseText);

		if(result == "true") {
			  return true;
		}else {
			alert('<fmt:message key="alert.enterValidToDate"/>');
			return false;
		}
		//return validateDateField('reports.fromDateAsString','fromDateAsString');
	}

}



function loadCalendarWithKeyords()
{
try {


<%
	ArrayList<LabelValueBean> keyWords = (ArrayList<LabelValueBean>)request.getAttribute("keywords");
	Iterator it = keyWords.iterator();
	while (it.hasNext())
	{
		LabelValueBean lvb = (LabelValueBean) it.next();
%>
	var newElement = {};
	newElement[headers[1]] = "<%=lvb.getValue()%>";
	newElement[headers[0]] = "<%=lvb.getLabel()%>";
	dataprovider.push(newElement);
<%
	}

%>
cal = new CalendarPopup("caldiv", false, "calFrame");
cal.offsetX = -3;
cal.offsetY = -97;
cal.withKeyWord= true;
cal.comboboxId= 'ilmStartDay';
cal.inputKeywordName= 'keywordInput';
cal.keyWordDataProvider = dataprovider;

} catch (e) {
	console.log('--',e)
}
}




function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["reports.fromDateAsString"];
  return validate(elementsRef);
 }


 var appName = "<%=SwtUtil.appName%>";
 var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;

function checkWeekend(entityId,currencyId,selectedDate){

	var oXMLHTTP = new XMLHttpRequest();

	var sURL = requestURL + appName+"/reports.do?method=isWeekend";

	sURL = sURL + "&entityId="+entityId+"&currencyId="+currencyId+"&selectedDate="+selectedDate;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;

}
	/*Start:code modified by sandeepkumar for mantis 1766 on 07-Nov-2012*/
 	/**
	 * submitForm
	 *
	 * This method is used to submit the form while perform button operation on Opprtunity cost screen.
	 **/
function submitForm(){
        var thresholdValue=validateCurrency(document.forms[0].elements['reports.threshold'],'reports.threshold',currencyFormat);
        document.forms[0].currencyCode.value = document.forms[0].elements["reports.id.currencyCode"].value;
		document.forms[0].currencyText.value =document.getElementById('currencyName').innerText;
		document.forms[0].entityId.value = document.forms[0].elements["reports.entityId"].value;
		if (document.forms[0].elements["reports.pdfOrExcelorCSV"][0].checked) {
		    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_PDF%>";
		} else if (document.forms[0].elements["reports.pdfOrExcelorCSV"][1].checked) {
		    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL%>";
		} else if (document.forms[0].elements["reports.pdfOrExcelorCSV"][2].checked) {
		    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_CSV%>";
		}

		if(thresholdValue){
        var oldTarget = document.forms[0].target;
        document.forms[0].method.value = "OpportunityCostReport";
        document.forms[0].target = 'tmp';
		if( document.forms[0].elements["reports.threshold"].value == "")
	    document.forms[0].elements["reports.threshold"].value = "0.0";
		if(validateForm(document.forms[0]) ){
		var dateFormat = '${sessionScope.CDM.dateFormat}';
		if(validateDateField(document.forms[0].elements['reports.fromDateAsString'],'date',dateFormat)) {
		var str = checkWeekend(document.forms[0].elements["reports.entityId"].value,document.forms[0].currencyCode.value,document.forms[0].elements["reports.fromDateAsString"].value);
		 if(str!='false'){
			document.forms[0].submit();
			document.forms[0].target = oldTarget;
			setParentChildsFocus();
			}else{
			alert('<fmt:message key="alert.weekend"/>');
				}
			}
		}
			else
				{
		  		document.forms[0].elements['reports.threshold'].focus();
				}
		 }
}
	/*End:code modified by sandeepkumar for mantis 1766 on 07-Nov-2012*/
function submitFormByEnity(methodName,status){

	document.forms[0].method.value = methodName;
	document.forms[0].entityId.value = document.forms[0].elements["reports.entityId"].value;
	document.forms[0].status.value =status;
	if (configScheduler == "true") {
		document.forms[0].configScheduler.value = configScheduler;
		document.forms[0].newOpportunityCostReportSchedConfig.value = true;
		document.forms[0].schedulerConfigXML.value = schedulerConfigXML;
		document.forms[0].reportType.value = reportType;
		document.forms[0].jobId.value = '${jobId}';
	}
	document.forms[0].submit();
}

var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var cal = null;

function validateCostDateField(){
var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(document.forms[0].elements['reports.fromDateAsString']!=null && document.forms[0].elements['reports.fromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['reports.fromDateAsString'],'date',dateFormat)){
		}
	}
}


function saveSchedulerReportOptions(){
	if(validateDatesFromReportConfig()){
	    var thresholdValue = validateCurrency(document.forms[0].elements['reports.threshold'],'reports.threshold',currencyFormat);
	    //document.forms[0].currencyCode.value = document.forms[0].elements["reports.id.currencyCode"].value;
		//document.forms[0].currencyText.value = document.getElementById('currencyName').innerText;
		//document.forms[0].entityId.value = document.forms[0].elements["reports.entityId"].value;
		if(thresholdValue){
			var schedulerConfigXML;
			var reportSchedulerData = new Object();

			if( document.forms[0].elements["reports.threshold"].value == "")
	    		document.forms[0].elements["reports.threshold"].value = "0.0";
			if(validateForm(document.forms[0]) ){
				var dateFormat = '${sessionScope.CDM.dateFormat}';
				var str = checkWeekend(document.forms[0].elements["reports.entityId"].value,document.forms[0].currencyCode.value,document.forms[0].elements["reports.fromDateAsString"].value);
	 			if(str!='false'){
				}else{
					alert('<fmt:message key="alert.weekend"/>');
					return;
				}
	 			if (document.forms[0].elements["reports.pdfOrExcelorCSV"][0].checked) {
	 			    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_PDF%>";
	 			} else if (document.forms[0].elements["reports.pdfOrExcelorCSV"][1].checked) {
	 			    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL%>";
	 			} else if (document.forms[0].elements["reports.pdfOrExcelorCSV"][2].checked) {
	 			    document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_CSV%>";
	 			}
				reportSchedulerData.pdfOrExcelorCSV = document.forms[0].selectedOutputFormat.value;
				reportSchedulerData.reportType =  reportType;
				reportSchedulerData.entityId = document.forms[0].elements["reports.entityId"].value;
				reportSchedulerData.entityName = document.getElementById("entityName").innerText;
				reportSchedulerData.fromdateasstring = document.forms[0].elements["reports.fromDateAsString"].value;
				reportSchedulerData.currencyCode = document.forms[0].elements["reports.id.currencyCode"].value;
				reportSchedulerData.currencyName =document.getElementById('currencyName').innerText;
				reportSchedulerData.threshold = document.forms[0].elements["reports.threshold"].value;

			} else {
		  		document.forms[0].elements['reports.threshold'].focus();
		  		return;
			}

			reportSchedulerData.jobId = '${jobId}';
			schedulerConfigXML = convertConfigObjectToXML(reportSchedulerData);
			this.opener.document.forms[0].schedulerConfigXML.value = getMenuWindow().encode64(schedulerConfigXML.innerHTML);
			var savedOutputFormat = document.forms[0].selectedOutputFormat.value;
			if (savedOutputFormat=="P") {
			    savedOutputFormat = "PDF";
			}else if (savedOutputFormat=="C") {
			    savedOutputFormat = "CSV";
			}else {
			    savedOutputFormat = "EXCEL";
			}
			this.opener.updateSchedulerConfigParams(savedOutputFormat);
			window.close();
		 }
	}
}


/**
*
* Used to create XML Data that contain information
* about scheduler report configuration
*
**/
function convertConfigObjectToXML(reportSchedulerData){
	var schedulerConfigXML = document.createElement("div");
	var transactionNode = document.createElement("schedConfig");

	var jobIdNode = document.createElement("jobid");
	jobIdNode.appendChild(document.createTextNode(reportSchedulerData.jobId));

	var reportTypeNode = document.createElement("reporttype");
	reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));

	var entityIdNode = document.createElement("entityid");
	entityIdNode.appendChild(document.createTextNode(reportSchedulerData.entityId));

	var entityNameNode = document.createElement("entityname");
	entityNameNode.appendChild(document.createTextNode(reportSchedulerData.entityName));

	var fromdateasstringNode = document.createElement("fromdateasstring");
	fromdateasstringNode.appendChild(document.createTextNode(reportSchedulerData.fromdateasstring));

	var currencyCodeNode = document.createElement("currencycode");
	currencyCodeNode.appendChild(document.createTextNode(reportSchedulerData.currencyCode));

	var currencyNameNode = document.createElement("currencyName");
	currencyNameNode.appendChild(document.createTextNode(reportSchedulerData.currencyName));


	var thresholdNode = document.createElement("threshold");
	thresholdNode.appendChild(document.createTextNode(reportSchedulerData.threshold));


	var dateFormatNode = document.createElement("dateformatasstring");
	dateFormatNode.appendChild(document.createTextNode(dateFormatValue));


	var pdfOrExcelorCSVNode = document.createElement("pdfOrExcelorCSV");
	pdfOrExcelorCSVNode.appendChild(document.createTextNode(reportSchedulerData.pdfOrExcelorCSV));

	transactionNode.appendChild(jobIdNode);
	transactionNode.appendChild(reportTypeNode);
	transactionNode.appendChild(entityIdNode);
	transactionNode.appendChild(entityNameNode);
	transactionNode.appendChild(fromdateasstringNode);
	transactionNode.appendChild(currencyCodeNode);
	transactionNode.appendChild(currencyNameNode);
	transactionNode.appendChild(thresholdNode);
	transactionNode.appendChild(dateFormatNode);
	transactionNode.appendChild(pdfOrExcelorCSVNode);
	schedulerConfigXML.appendChild(transactionNode);
	return schedulerConfigXML;
}

</SCRIPT>

	<style>
#ilmStartDay{
   margin-left: -200px;
    margin-top: -170px;
}
input[name ="keywordInput"] {
	width: 120px
}

</style>
</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);"  onunload="call();" >
<form action="reports.do" method="post"  >
<input name="method" type="hidden" value="OpportunityCostReport">
<input name="applyCurrencyThreshold" type="hidden" >
<input name="applyCurrencyThresholdInd" type="hidden" value="0">
<input name="currencyCode" type="hidden" value="">
<input name="currencyText" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="status" type="hidden" value="">
<input name="configScheduler" type="hidden" value="">
<input name="reportType" type="hidden" value="">
<input name="jobId" type="hidden" value="">
<input name="newOpportunityCostReportSchedConfig" type="hidden" value="">
<input name="schedulerConfigXML" type="hidden" value="">
<input name="selectedOutputFormat" type="hidden" value="">
<div id="OpportunityCost" style="border-style: outset; position:absolute; left:20px; top:20px; width:570px; height:170px; border:2px outset;" >
   <div id="OpportunityCost" style="position:absolute; left:8px; top:4px; width:570px; height:165;">
   <div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>
	  <table width="500px" border="0" cellpadding="0" cellspacing="0" height="104" class="content">
		    <tr height="28">
				<td  width="120px"><b><fmt:message key="user.hostId"/></b></td>
				<td width="5px">&nbsp;</td>
				<td  width="125px" >
				<input type="text" cssClass="htmlTextAlpha" name="reports.id.hostId" value="${reports.id.hostId}"  titleKey="tooltip.reportHostId" tabindex="1" style="width:120px;"  disabled="true" />
				</td>
				 <td width="240px"></td>
			</tr>
			<tr height="28">
				<td  width="120px"><b><fmt:message key="entity.id"/></b></td>
				<td width="5px">&nbsp;</td>
				<td  width="125px" >
					<select name="reports.entityId" class="htmlTextAlpha" titleKey="tooltip.reportEntity" style="width:120px" tabindex="2" onchange="submitFormByEnity('getOpportunityCostparameters', 'onEntityChange')">
						<c:forEach items="${requestScope.userEntity}" var="entity">
							<option value="${entity.entityId}" <c:if test="${reports.entityId == entity.entityId}">selected</c:if> >
								${entity.entityName}
							</option>
						</c:forEach>
					</select>
				</td>
				<td width="240px">
					<span id="entityName"  name="reports.entityName" class="spantext">
				</td>

			</tr>

			<tr height="28">
			  <td  width="120px"><b><fmt:message key="date"/></b>*</td>
			  <td width="5px">&nbsp;</td>
			  <td width="125px" >
					<input type="text" tabindex="5" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="reports.fromDateAsString" value="${reports.fromDateAsString}"  style="width:80px;margin-bottom: 5px;margin-top: 2px;height:21"   maxlength="10" onchange="if(validateForm(document.forms[0])){validateCostDateField();}" onmouseout="dateSelected=false" />
					<A   title='<fmt:message key="tooltip.calendarreportdate"/>' tabindex="6" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['reports.fromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A>

			 <td width="240px"></td>
			</tr>

			<tr height="28">
				<td  width="120px"><b><fmt:message key="opportunityCostReport.currencyLabel"/></b></td>
				<td width="5px">&nbsp;</td>
				<td  width="125px" >
<%--				<form:select id="reports.id.currencyCode" path="reports.id.currencyCode" cssClass="htmlTextAlpha" titleKey="tooltip.currency"  style="width:120px" tabindex="2" items="${requestScope.currencies}" itemValue="value" itemLabel="label" />--%>
				<select name="reports.id.currencyCode" class="htmlTextAlpha" titleKey="tooltip.currency" style="width:120px" tabindex="2">
					<c:forEach items="${requestScope.currencies}" var="currency">
						<option value="${currency.value}" <c:if test="${reports.id.currencyCode == currency.value}">selected</c:if> >
							${currency.label}
						</option>
					</c:forEach>
				</select>

				</td>
				<td width="240px">
					<span id="currencyName" name="currencyName"	class="spantext">
				</td>

			</tr>

		   <tr height="28">
				<td  width="120px"><b><fmt:message key="reports.threshold"/> (${requestScope.reportingCcy})</b></td>
			    <td width="5px">&nbsp;</td>
				<td  width="125px">
					 <input type="text" cssClass="htmlTextNumeric"  style="width:120px" maxlength="28" name="reports.threshold" value="${reports.threshold}"   onchange="return validateCurrency(this,'reports.threshold',currencyFormat)" />
				</td><td width="240px"></td>
			</tr>

		</table>
		<table>
					<tr>
    <td width="130" style="box-sizing: border-box;">
        <b style="position:relative;">
            <fmt:message key="reports.outputFormat" />
        </b>
    </td>
    <td width="32">
        <input type="radio" name="reports.pdfOrExcelorCSV"
               value="<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_PDF%>"
               id="pdfOption"
               tabindex="7"
               style="margin-right: 10px;"
               ${reports.pdfOrExcelorCSV == 'P' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="pdfOption" title='<fmt:message key="reports.pdf"/>'>
            <fmt:message key="reports.pdf" />
        </label>&nbsp;
    </td>
    <td width="32">
        <input type="radio" name="reports.pdfOrExcelorCSV"
               value="<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL%>"
               id="excelOption"
               tabindex="8"
               style="margin-right: 10px;"
               ${reports.pdfOrExcelorCSV == 'E' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="excelOption" title='<fmt:message key="reports.excel"/>'>
            <fmt:message key="reports.excel" />
        </label>&nbsp;
    </td>
    <td width="32">
        <input type="radio" name="reports.pdfOrExcelorCSV"
               value="<%=SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_CSV%>"
               id="csvOption"
               tabindex="9"
               style="margin-right: 10px;"
               ${reports.pdfOrExcelorCSV == 'C' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="csvOption" title='<fmt:message key="reports.csv"/>'>
            <fmt:message key="reports.csv"/>
        </label>&nbsp;
    </td>
</tr>

		</table>
	</div>
  </div>

<div id="opportunityCost" style="position:absolute; left:515; top:183; width:70px; z-index:99; height:29px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="60">
		<tr>
			<td align="Right">
			<a tabindex="14" href=# onclick="javascript:openWindow(buildPrintURL('print','Opportunity Cost Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		    </td>


		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:20; top:198; border:2px outset #FFFFFF;  width:570px; height:40px; visibility:visible;">
  <div id="opportunityCost" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<c:if test="${'true' == requestScope.configScheduler}">

			<td title='<fmt:message key="tooltip.save"/>'><a tabindex="14" width="70" onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"  onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
								onclick="saveSchedulerReportOptions('opportunityCostReport');"><fmt:message key="button.save"/></a></td>

</c:if>
		<c:if test="${'true' != requestScope.configScheduler}">

		  <td title='<fmt:message key="tooltip.submitbutton"/>'><a tabindex="12" width="70" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitForm();"><fmt:message key="button.report"/></a></td>

</c:if>
		  <td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
				<a tabindex="13" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>

	</tr>
   </table>
</div>
</div>
<blockquote>&nbsp;</blockquote>
<p>&nbsp;</p>
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>