<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>




<c:set var="recordCount" value="${requestScope.schedReportHistList.size()}"/>
<schedreporthist >
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<singletons>
		<deleteResult>${requestScope.deleteResult}</deleteResult>
	</singletons>
	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order}" var="order" >
					<c:if test="${'fileId' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.fileId"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.fileId"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								sort="true"
								type="num"
								dataelement="fileId"
								width="${requestScope.column_width.fileId}"/>

					</c:if>
					<c:if test="${'runDate' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.runDate"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.runDate"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="date"
								sort="true"
								dataelement="runDate"
								width="${requestScope.column_width.runDate}"/>

					</c:if>
					<c:if test="${'reportName' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.reportName"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.reportName"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="str"
								sort="true"
								dataelement="reportName"
								width="${requestScope.column_width.reportName}"/>

					</c:if>
					<c:if test="${'elapsedTime' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.elapsedTime"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.elapsedTime"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="date"
								sort="true"
								dataelement="elapsedTime"
								width="${requestScope.column_width.elapsedTime}"/>

					</c:if>
					<c:if test="${'fileName' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.fileName"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.fileName"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="str"
								sort="true"
								dataelement="fileName"
								width="${requestScope.column_width.fileName}"/>

					</c:if>
					<c:if test="${'exportStatus' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.exportStatus"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.exportStatus"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="str"
								sort="true"
								dataelement="exportStatus"
								width="${requestScope.column_width.exportStatus}"/>

					</c:if>
					<c:if test="${'mailStatus' == order}">

						<column heading="<fmt:message key="label.schedreporthist.column.mailStatus"/>"
								tooltip = "<fmt:message key="tooltip.schedreporthist.mailStatus"/>"
								clickable="true"
								draggable="true"
								filterable="true"
								type="str"
								sort="true"
								dataelement="mailStatus"
								width="${requestScope.column_width.mailStatus}"/>

					</c:if>
				</c:forEach>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach var="schedReportHistList" items="${requestScope.schedReportHistList}">
				<row>
					<fileId clickable="false">${schedReportHistList.id.fileId}</fileId>
					<hostId clickable="false">${schedReportHistList.hostId}</hostId>
					<jobId clickable="false">${schedReportHistList.jobId}</jobId>
					<reportName clickable="false">${schedReportHistList.reportName}</reportName>
					<reportTypeId clickable="false">${schedReportHistList.reportTypeId}</reportTypeId>
					<scheduleId clickable="false">${schedReportHistList.scheduleId}</scheduleId>
					<runDate clickable="false">${schedReportHistList.runDateAsString}</runDate>
					<elapsedTime clickable="false">${schedReportHistList.elapsedTimeAsString}</elapsedTime>
					<fileName clickable="false">${schedReportHistList.fileName}</fileName>
					<outputLocation clickable="false">${schedReportHistList.outputLocation}</outputLocation>
					<fileSize clickable="false">${schedReportHistList.fileSize}</fileSize>
					<exportStatus clickable="false">${schedReportHistList.exportStatus}</exportStatus>
					<mailStatus clickable="false">${schedReportHistList.mailStatus}</mailStatus>
					<exportError clickable="false">${schedReportHistList.exportError}</exportError>
					<mailRsult clickable="false">${schedReportHistList.mailRsult}</mailRsult>
				</row>
			</c:forEach>
		</rows>


	</grid>
	<selects>
		<select id="reportJobs">
			<c:forEach items="${requestScope.reportJobs}" var="job" >
				<option value="${job.value}"
						selected="<c:if test="${job.value == requestScope.selectedReportJob}">
    1
</c:if><c:if test="${job.value != requestScope.selectedReportJob}">
    0
</c:if>">
						${job.label}</option>
			</c:forEach>
		</select>
		<select id="reportTypes">
			<c:forEach items="${requestScope.reportTypes}" var="type" >
				<option value="${type.value}"
						selected="<c:if test="${type.value == requestScope.selectedReportType}">
    1
</c:if><c:if test="${type.value != requestScope.selectedReportType}">
    0
</c:if>">
						${type.label}</option>
			</c:forEach>
		</select>
	</selects>
</schedreporthist>