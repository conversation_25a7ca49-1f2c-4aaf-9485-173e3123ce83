<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<fmt:message key="intradayBalances.mainScreen"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';
var dateRetain ;
mandatoryFieldsArray = ["*"];
function bodyOnLoad()
{
   var dropBox1 = new SwSelectBox(document.forms[0].elements["intradaybalances.id.entityId"],document.getElementById("entityName"));
    var dropBox2 = new SwSelectBox(document.forms[0].elements["intradaybalances.currencyCode"],document.getElementById("currencyName"));
	document.forms[0].elements["intradaybalances.rawData"].checked = false;
	document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
	dateRetain = document.forms[0].elements["intradaybalances.dateAsString"].value ;
	document.forms[0].entityText.value=document.getElementById("entityName").innerText;


}

var today = "${requestScope.today}";
/*Start: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/
function buildGetAccountDetails(methodName){

if (document.forms[0].elements['intradaybalances.dateAsString'].value != "") {
/*Start : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
if(validateDateField(document.forms[0].elements['intradaybalances.dateAsString'],'intradayBalances.date',dateFormat)){
/*End : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
var days1 = today;
var days2 = document.forms[0].elements['intradaybalances.dateAsString'].value;
/* Start: Code commented/modified by Kalidass G for Mantis 827 on 09-08-2010 */
	 var flag=true;
	var param = 'intradayBalances.do?method='+methodName+'&entityId=';
	param +=  document.forms[0].elements["intradaybalances.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	param += '&currencyText=' + document.getElementById('currencyName').innerText;
	param += '&selectedCurrencyCode=' + document.forms[0].elements["intradaybalances.currencyCode"].value ;
	param += '&selectedReportDate=' + document.forms[0].elements["intradaybalances.dateAsString"].value ;

	if(flag && document.forms[0].elements["intradaybalances.rawData"].checked){
		param += '&selectedRawdata=Y';
	}else{
		param += '&selectedRawdata=N';
	}
	openWindow(param,'intradaybalanceaccountWindow','left=50,top=190,width=445,height=135,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
	setParentChildsFocus();
    /* End: Code commented/modified by Kalidass G for Mantis 827 on 09-08-2010 */
    /*Start : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	}
	/*End : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	} else {
    	alert("<fmt:message key="alert.enterValidDate"/>");
    }
}
/*End: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/

function uncheckRawdata(){
if (document.forms[0].elements["intradaybalances.rawData"].checked == false)
{

 document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;
 }
 else{
 document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
 }
}

var dateSelected= false;


function onDateChange(obj){

var days1 = today;
var days2 = document.forms[0].elements['intradaybalances.dateAsString'].value;

var xx = document.forms[0].elements['intradaybalances.dateAsString'];

	 if(dateSelected){

	     if(dateFormat == "datePat2")
			{

				// Take the format: MM/dd/yyyy
				var parts = days1.split("/");

				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[1]);
				date1.setMonth(parts[0] - 1);

				var parts = days2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[1]);
				date2.setMonth(parts[0] - 1);

				var pat2diff=(date1-date2)/(60*60*1000)/24;

				if(pat2diff > 7 || pat2diff < 1 ){

							alert("<fmt:message key="alert.dateFromYesterday"/>");
							document.forms[0].elements['intradaybalances.dateAsString'].value = dateRetain;


						}
			}

	if(dateFormat == "datePat1")
			{

				// Take the format: dd/MM/yyyy
				var parts = days1.split("/");
				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[0]);
				date1.setMonth(parts[1] - 1);

				var parts = days2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[0]);
				date2.setMonth(parts[1] - 1);

				var pat1diff=(date1-date2)/(60*60*1000)/24;

				if(pat1diff > 7 || pat1diff < 1 ){

							alert("<fmt:message key="alert.dateFromYesterday"/>");
							document.forms[0].elements['intradaybalances.dateAsString'].value = dateRetain;
				}


		   }
	}


}

function submitFormByEnity(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].entityId.value = document.forms[0].elements["intradaybalances.id.entityId"].value;
	document.forms[0].submit();
}

var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var cal = new CalendarPopup("caldiv",true,"calFrame");
  cal.offsetX = -3;
  cal.offsetY = -97;

/* Start:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */
function validateIntraDateField(){
	if(document.forms[0].elements['intradaybalances.dateAsString']!=null && document.forms[0].elements['intradaybalances.dateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['intradaybalances.dateAsString'],'intradayBalances.date',dateFormat)){
		}
	}
}

function disableEnterKey(e) {
     var key;
     if(window.event)
          key = window.event.keyCode; //IE
     else
          key = e.which; //firefox

     return (key != 13);
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["intradaybalances.dateAsString"];
  return validate(elementsRef);
 }
/* End:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */

</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);" onunload="call();">
<form action="intradayBalances.do" method="post">
<input name="method" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="entityText" type="hidden" value="">
<input name="currencyText" type="hidden" value="">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="selectedReportDate" type="hidden" value="">
<input name="selectedRawdata" type="hidden" value="">


<div id="IntradayBalances" style="position:absolute; left:20px; top:20px; width:570px; height:110px; border:2px outset;" color="#7E97AF">
   <div id="IntradayBalances" style="position:absolute; left:8px; top:4px; width:570px; height:154px;">
    <div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>
	  <table width="559px" border="0" cellpadding="0" cellspacing="0" height="100" class="content">

			  <tr height="13">
				<td  width="90px"><b><fmt:message key="intradayBalances.entity"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="130px" >
				<select id="intradaybalances.id.entityId" name="intradaybalances.id.entityId" class="htmlTextAlpha" style="width:120px" titleKey="intradayBalances.entity" onchange="submitFormByEnity('displayList')" tabindex="2">
					<c:forEach var="item" items="${requestScope.entities}">
						<option value="${item.value}" <c:if test="${item.value == intradaybalances.id.entityId}">selected</c:if>>${item.label}</option>
					</c:forEach>
				</select>


				</td>
				<td width="275px">
					<span id="entityName" name="entityName" class="spantext">
				</td>

			</tr>

	 	<tr height="13">
				<td  width="90px"><b><fmt:message key="intradayBalances.currency"/></b></td>
				<td width="28px">&nbsp;</td>
				<td  width="130px" >
				<select id="intradaybalances.currencyCode" name="intradaybalances.currencyCode" class="htmlTextAlpha" style="width:55px" titleKey="tooltip.currencyIdentifier" tabindex="2">
					<c:forEach var="currency" items="${requestScope.currencies}">
						<option value="${currency.value}" <c:if test="${currency.value == intradaybalances.currencyCode}">selected</c:if>>${currency.label}</option>
					</c:forEach>
				</select>



				</td>
				<td width="275px">
					<span id="currencyName" name="currencyName" class="spantext">
				</td>

			</tr>

			<tr height="13">
				  <td  width="90px"><b><fmt:message key="intradayBalances.date"/></b>*</td>
				  <td width="28px">&nbsp;</td>
				  <td width="120px" >
					<!-- Start : Marshal : Modified for Mantis 1262 -->
					<input type="text" tabindex="5" titleKey="tooltip.intradayBalancesDate" cssClass="htmlTextNumeric" name="intradaybalances.dateAsString" value="${intradaybalances.dateAsString}"   maxlength="10" style="width:79px;margin-bottom:6px;" onchange="if(validateForm(document.forms[0]) ){validateIntraDateField();}" onkeypress="return disableEnterKey(event)" onmouseout="dateSelected=false" />
					<!-- End : Marshal : Modified for Mantis 1262 -->
					<A   title='<fmt:message key="tooltip.calendarintradayBalancesdate"/>' tabindex="6" name="datelink" ID="datelink"  onClick="cal.select(document.forms[0].elements['intradaybalances.dateAsString'],'datelink',dateFormatValue);dateSelected=true; return false;"><img src="images/calendar-16.gif"></A>

					  </td><td width="275px"></td>
					</tr>


			<tr height="13">
							<td  width="90px"><b><fmt:message key="intradayBalances.rawData"/></b></td>
						    <td width="28px">&nbsp;</td>
							<td  width="120px">
								 <input type="checkbox"  name="intradaybalances.rawData" value="Y" ${requestScope.intradaybalances.rawData == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.intradaybalances.rawData == "Y"}' style="width:13px;"   titleKey="tooltip.selectthreshold" cssClass="htmlTextAlpha" tabindex="2" />
							</td><td width="275px"></td>
			</tr>

		</table>
	</div>
  </div>
  </div>

<div id="IntradayBalances" style="position:absolute; left:515; top:145; width:70px; height:29px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			<a tabindex="14" href=# onclick="javascript:openWindow(buildPrintURL('print','Intraday Balance Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		    </td>

		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:20; top:135; border:2px outset; width:570px; height:40px; visibility:visible;">
  <div id="IntradayBalances" style="position:absolute; left:6; top:6; width:425; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="okbutton" width="70"> </td>

		  <td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
				<a tabindex="13" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>

	</tr>
   </table>
</div>
</div>


 <div id="IntradayBalances" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:hidden;">
    <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="okenablebutton" >
		<a tabindex="1" title='<fmt:message key="tooltip.okbutton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:buildGetAccountDetails('showAccountDetails');"><fmt:message key="button.ok"/></a>
		</td>
		<td id="okdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.ok"/></a>
		</td>



	</tr>
    </table>
  </div>



<blockquote>&nbsp;</blockquote>
<p>&nbsp;</p>
</form>
<DIV ID="caldiv" STYLE="position:absolute;visibility:hidden;background-color:white;layer-background-color:white;"></DIV>
<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;"></iframe>
</body>
</html>