<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 4.0.2-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SubReportILMIntraliquitityA" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="2" bottomMargin="2">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="dataset1">
		<parameter name="ppENTITY_ID" class="java.lang.String"/>
		<parameter name="ppCCY" class="java.lang.String"/>
		<parameter name="ppILM_GROUP_ID" class="java.lang.String"/>
		<parameter name="ppVALUE_DATE" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_END" class="java.util.Date"/>
		<parameter name="ppVALUE_DATE_MIN1" class="java.util.Date"/>
		<parameter name="ppSERIES_IDENTIFIER" class="java.lang.String"/>
		<parameter name="ppDB_LINK" class="java.lang.String"/>
		<parameter name="ppILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
		<parameter name="ppCURRENCY_PATTERN" class="java.lang.String"/>
		<parameter name="ppCCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
		<parameter name="ppHOST_ID" class="java.lang.String"/>
		<parameter name="ppROLE_ID" class="java.lang.String"/>
		<queryString>
			<![CDATA[SELECT attribute_id,
							attribute_name||'('||DECODE(min1.contribute_total, 'PLUS', '+', 'MINUS', '-', '')||')' ATTRIBUTE_LABEL,
						    avg_num_value/$P{ppCCY_MULTIPLIER_VALUE} VALUE_MIN1
					FROM TABLE (pkg_ilm_rep.fn_avg_attrs (
							$P{ppHOST_ID},
							$P{ppENTITY_ID},
							$P{ppCCY},
							$P{ppILM_GROUP_ID},
							$P{ppSERIES_IDENTIFIER},
							$P{ppDB_LINK},
							$P{ppVALUE_DATE_MIN1},
							$P{ppVALUE_DATE_MIN1},
							$P{ppROLE_ID})) min1]]>
		</queryString>
		<field name="ATTRIBUTE_LABEL" class="java.lang.String"/>
		<field name="VALUE_MIN1" class="java.lang.Double"/>
	</subDataset>
	<parameter name="p_ENTITY_ID" class="java.lang.String"/>
	<parameter name="p_VALUE_DATE_MIN1" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE" class="java.util.Date"/>
	<parameter name="p_VALUE_DATE_END" class="java.util.Date"/>
	<parameter name="p_CCY" class="java.lang.String"/>
	<parameter name="p_CURRENCY_PATTERN" class="java.lang.String"/>
	<parameter name="p_ILM_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_CB_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_GC_GROUP_ID" class="java.lang.String"/>
	<parameter name="p_SERIES_IDENTIFIER" class="java.lang.String"/>
	<parameter name="p_DB_LINK" class="java.lang.String"/>
	<parameter name="p_Dictionary_Data" class="java.util.HashMap"/>
	<parameter name="p_ILM_UTIL" class="org.swallow.reports.dao.hibernate.ReportsDAOHibernate.ILMReportUtils"/>
	<parameter name="p_CCY_MULTIPLIER_VALUE" class="java.lang.Double"/>
	<parameter name="P_HOST_ID" class="java.lang.String"/>
	<parameter name="P_ROLE_ID" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT cb.AVG_EXTERNAL_SOD/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_EXTERNAL_SOD, cb.AVG_COLLATERAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_COLLATERAL,
						(gc.AVG_EXTERNAL_SOD - nvl(cb.AVG_EXTERNAL_SOD,0))/$P{p_CCY_MULTIPLIER_VALUE} AS BOB,(gc.AVG_COLLATERAL - nvl(cb.AVG_COLLATERAL,0))/$P{p_CCY_MULTIPLIER_VALUE} AS OSC,
						gc.AVG_CREDIT_LINE_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_TOTAL,gc.AVG_CREDIT_LINE_SECURED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_SECURED,
						gc.AVG_CREDIT_LINE_COMMITTED/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_CREDIT_LINE_COMMITTED,gc.AVG_UNENCUMBERED_LIQUID_ASSETS/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_UNENCUMBERED_LIQUID_ASSETS,gc.AVG_OTHER_TOTAL/$P{p_CCY_MULTIPLIER_VALUE} AS AVG_OTHER_TOTAL
 				 FROM 	TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_CB_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1}, $P{P_ROLE_ID})) cb,
  						TABLE (pkg_ilm_rep.fn_avg_main_data ($P{P_HOST_ID}, $P{p_ENTITY_ID}, $P{p_CCY}, $P{p_GC_GROUP_ID}, $P{p_SERIES_IDENTIFIER},$P{p_DB_LINK}, $P{p_VALUE_DATE_MIN1}, $P{p_VALUE_DATE_MIN1}, $P{P_ROLE_ID})) gc]]>
	</queryString>
	<field name="AVG_EXTERNAL_SOD" class="java.lang.Double"/>
	<field name="AVG_COLLATERAL" class="java.lang.Double"/>
	<field name="BOB" class="java.lang.Double"/>
	<field name="OSC" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_TOTAL" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_SECURED" class="java.lang.Double"/>
	<field name="AVG_CREDIT_LINE_COMMITTED" class="java.lang.Double"/>
	<field name="AVG_UNENCUMBERED_LIQUID_ASSETS" class="java.lang.Double"/>
	<field name="AVG_OTHER_TOTAL" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="180" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="534" height="100" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_RESERVES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_EXTERNAL_SOD}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_EXTERNAL_SOD},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCENTRAL_BANK_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_COLLATERAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_COLLATERAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="48" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pBALANCES_OTHER_BANKS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="48" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{BOB}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{BOB},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="64" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER_SYSTEMS_COLLATERAL")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="64" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{OSC}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{OSC},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="80" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pTOTAL_CREDIT_LINES")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="80" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="100" width="534" height="80" backcolor="#F2F2F2"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_SECURED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_SECURED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_SECURED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="20" y="32" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pCREDIT_LINES_COMMITTED")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="32" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_CREDIT_LINE_COMMITTED}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_CREDIT_LINE_COMMITTED},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="0" y="148" width="534" height="32" backcolor="#D9D9D9"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pUNENCUMBERED_LIQUID_ASSETS")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="0" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_UNENCUMBERED_LIQUID_ASSETS}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_UNENCUMBERED_LIQUID_ASSETS},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="16" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="SansSerif" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOTHER")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement key="textField-1" x="380" y="16" width="142" height="16">
						<property name="local_mesure_unitx" value="pixel"/>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle"/>
					<textFieldExpression class="java.lang.String"><![CDATA[($F{AVG_OTHER_TOTAL}!=null)?($P{p_ILM_UTIL}.formatCurrency($F{AVG_OTHER_TOTAL},$P{p_CURRENCY_PATTERN})):"-"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
		<band height="35">
			<printWhenExpression><![CDATA[$P{p_SERIES_IDENTIFIER}.equals("Standard")]]></printWhenExpression>
			<frame>
				<reportElement positionType="Float" mode="Opaque" x="1" y="0" width="534" height="35" backcolor="#F2F2F2"/>
				<textField>
					<reportElement key="textField" positionType="Float" x="7" y="0" width="190" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font size="10" isBold="false" isItalic="true" pdfFontName="Helvetica-Oblique"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$P{p_Dictionary_Data}.get("pOF_WHICH")]]></textFieldExpression>
				</textField>
				<componentElement>
					<reportElement positionType="Float" x="20" y="16" width="514" height="16">
						<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
						<property name="local_mesure_unitwidth" value="pixel"/>
					</reportElement>
					<jr:list xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" printOrder="Vertical">
						<datasetRun subDataset="dataset1">
							<datasetParameter name="ppENTITY_ID">
								<datasetParameterExpression><![CDATA[$P{p_ENTITY_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY">
								<datasetParameterExpression><![CDATA[$P{p_CCY}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_END">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_END}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_GROUP_ID">
								<datasetParameterExpression><![CDATA[$P{p_ILM_GROUP_ID}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppSERIES_IDENTIFIER">
								<datasetParameterExpression><![CDATA[$P{p_SERIES_IDENTIFIER}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppVALUE_DATE_MIN1">
								<datasetParameterExpression><![CDATA[$P{p_VALUE_DATE_MIN1}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppDB_LINK">
								<datasetParameterExpression><![CDATA[$P{p_DB_LINK}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppILM_UTIL">
								<datasetParameterExpression><![CDATA[$P{p_ILM_UTIL}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCURRENCY_PATTERN">
								<datasetParameterExpression><![CDATA[$P{p_CURRENCY_PATTERN}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ppCCY_MULTIPLIER_VALUE">
								<datasetParameterExpression><![CDATA[$P{p_CCY_MULTIPLIER_VALUE}]]></datasetParameterExpression>
							</datasetParameter>
						</datasetRun>
						<jr:listContents height="16" width="514">
							<textField>
								<reportElement x="0" y="0" width="257" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement>
									<font fontName="SansSerif" size="10" isBold="false"/>
								</textElement>
								<textFieldExpression class="java.lang.String"><![CDATA[$F{ATTRIBUTE_LABEL}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="251" y="0" width="250" height="16"/>
								<box>
									<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
									<bottomPen lineWidth="0.0" lineColor="#000000"/>
									<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textElement textAlignment="Right" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String"><![CDATA[($F{VALUE_MIN1}!=null)?($P{ppILM_UTIL}.formatCurrency($F{VALUE_MIN1},$P{ppCURRENCY_PATTERN})):"-"]]></textFieldExpression>
							</textField>
						</jr:listContents>
					</jr:list>
				</componentElement>
			</frame>
		</band>
	</detail>
</jasperReport>
