<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.util.LabelValueBean"%>
<html>
<head>
	<title>
		<fmt:message key="turnoverReport.mainScreen"/>
	</title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
	<script language="JAVASCRIPT">
		/*Start : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
		var dateSelected = false;
		/*End : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
		var dateFormat = '${sessionScope.CDM.dateFormat}';
		var configScheduler ="${requestScope.configScheduler}";
		var newTurnoverReportSchedConfig  ="${requestScope.newTurnoverReportSchedConfig}";
		var reportType = '${requestScope.reportType}'
		var schedulerConfigXML = "${requestScope.schedulerConfigXML}";
		mandatoryFieldsArray = ["*"];
		var isSingleDate = true;


		$(window).on("load", function() {

			$("input[type='radio']").on('click', function() {
				if ($(this).val() == "R") {
					$("#divTo").css("visibility", "visible");
					$("#divToTime").css("visibility", "visible");
					document.forms[0].elements['turnoverreport.dateAsString'].value ="${requestScope.yesterday}";
					document.forms[0].elements['turnoverreport.toDateAsString'].value ="${requestScope.yesterday}";
					document.getElementById("dateInput").title = "<fmt:message key="turnoverReport.startdayTooltip"/>";
					document.getElementById("datelink").title = "<fmt:message key="turnoverReport.startdayTooltip"/>";
					document.getElementById("dateInput1").title = "<fmt:message key="turnoverReport.enddayTooltip"/>";
					document.getElementById("datelink1").title = "<fmt:message key="turnoverReport.enddayTooltip"/>";



					isSingleDate=false;
				} else if ($(this).val() == "S") {
					$("#divTo").css("visibility", "hidden");
					$("#divToTime").css("visibility", "hidden");
					document.forms[0].elements['turnoverreport.dateAsString'].value ="${requestScope.yesterday}";
					isSingleDate=true;
					document.getElementById("dateInput").title ="<fmt:message key="tooltip.reportDate"/>";
					document.getElementById("datelink").title = "<fmt:message key="tooltip.reportDate"/>";
				}

			})
		});

		var cal ;
		function bodyOnLoad()
		{
			if (configScheduler == "true") {
				document.forms[0].newTurnoverReportSchedConfig.value = newTurnoverReportSchedConfig;
				document.forms[0].reportType.value= reportType;
			}
			var dropBox1 = new SwSelectBox(document.forms[0].elements["turnoverreport.id.entityId"],document.getElementById("entityName"));

			if (configScheduler != "true") {
				document.forms[0].elements["turnoverreport.showMain"].checked = true;
				document.forms[0].elements["turnoverreport.showSub"].checked = true;
			}
			document.getElementById("reportbutton").innerHTML = document.getElementById("reportenablebutton").innerHTML;
			document.forms[0].entityText.value=document.getElementById("entityName").innerText;

			if (document.forms[0].elements["turnoverreport.singleOrRange"][0].checked) {
				$("#divTo").css("visibility", "hidden");
				$("#divToTime").css("visibility", "hidden");
				if("${requestScope.changeEntity}" == "Y"){
					document.forms[0].elements['turnoverreport.dateAsString'].value ="${requestScope.yesterday}";
					document.getElementById("dateInput").title ="<fmt:message key="tooltip.reportDate"/>";
					document.getElementById("datelink").title = "<fmt:message key="tooltip.reportDate"/>";
				}
			} else if (document.forms[0].elements["turnoverreport.singleOrRange"][1].checked) {
				$("#divTo").css("visibility", "visible");
				$("#divToTime").css("visibility", "visible");
				isSingleDate=false;

				if("${requestScope.changeEntity}" == "Y"){
					document.forms[0].elements['turnoverreport.dateAsString'].value ="${requestScope.yesterday}";
					document.forms[0].elements['turnoverreport.toDateAsString'].value ="${requestScope.yesterday}";
					document.getElementById("dateInput").title = "<fmt:message key="turnoverReport.startdayTooltip"/>";
					document.getElementById("datelink").title = "<fmt:message key="turnoverReport.startdayTooltip"/>";
					document.getElementById("dateInput1").title = "<fmt:message key="turnoverReport.enddayTooltip"/>";
					document.getElementById("datelink1").title = "<fmt:message key="turnoverReport.enddayTooltip"/>";
				}
			}


			$('#selectEntity').on('change', function() {
				submitFormByEnity('displayList');
			});


			if (configScheduler == "true") {
				loadCalendarWithKeyords();
			} else {

				cal = new CalendarPopup("caldiv",true,"calFrame");


				var firstThresholdDay = "${firstThresholdDay}";
				var lastThresholdDay = "${lastThresholdDay}";
				if (dateFormat == 'datePat1'){
					var firstDate = firstThresholdDay.split('/');
					firstThresholdDay = firstDate[1]+"/"+firstDate[0]+"/"+firstDate[2];
					var secondDate = lastThresholdDay.split('/');
					lastThresholdDay = secondDate[1]+"/"+secondDate[0]+"/"+secondDate[2];
				}

				// When (firstThresholdDay == lastThresholdDay) then only right side should be disabled: This case appears only when ILMRetain property is not defined for the entity
				if (firstThresholdDay != ""  && firstThresholdDay != lastThresholdDay) {
					cal.addDisabledDates(null, firstThresholdDay);
				}
				cal.addDisabledDates(lastThresholdDay, null);



				cal.offsetX = -3;
				cal.offsetY = -97;
			}


		}


		function loadCalendarWithKeyords()
		{

			try {


				<%
                    ArrayList<LabelValueBean> keyWords = (ArrayList<LabelValueBean>)request.getAttribute("keywords");
                    Iterator it = keyWords.iterator();
                    while (it.hasNext())
                    {
                        LabelValueBean lvb = (LabelValueBean) it.next();
                %>
				var newElement = {};
				newElement[headers[1]] = "<%=lvb.getValue()%>";
				newElement[headers[0]] = "<%=lvb.getLabel()%>";
				dataprovider.push(newElement);
				<%
                    }

                %>
				cal = new CalendarPopup("caldiv", false, "calFrame");
				var firstThresholdDay = "${firstThresholdDay}";
				var lastThresholdDay = "${lastThresholdDay}";
				if (dateFormat == 'datePat1'){
					var firstDate = firstThresholdDay.split('/');
					firstThresholdDay = firstDate[1]+"/"+firstDate[0]+"/"+firstDate[2];
					var secondDate = lastThresholdDay.split('/');
					lastThresholdDay = secondDate[1]+"/"+secondDate[0]+"/"+secondDate[2];
				}

				// When (firstThresholdDay == lastThresholdDay) then only right side should be disabled: This case appears only when ILMRetain property is not defined for the entity
				if (firstThresholdDay != ""  && firstThresholdDay != lastThresholdDay) {
					cal.addDisabledDates(null, firstThresholdDay);
				}
				cal.addDisabledDates(lastThresholdDay, null);



				cal.offsetX = -3;
				cal.offsetY = -97;
				cal.withKeyWord= true;
				cal.comboboxId= 'ilmStartDay';
				cal.inputKeywordName= 'keywordInput';
				cal.keyWordDataProvider = dataprovider;

			} catch (e) {
				console.log('--',e)
			}
		}
		var headers = ["Keyword", "Description"];


		var dataprovider = new Array();
		/*Start: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/
		function checkLevelBreakdown(methodName){
			if(document.forms[0].elements["turnoverreport.dateAsString"].value != "") {
				/*Start : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
				if(validateDateField('turnoverreport.dateAsString','turnoverReport.date')){
					/*End : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
					if(document.forms[0].elements["turnoverreport.showMain"].checked == false)
					{
						document.forms[0].elements["turnoverreport.showMain"].value ="N";
					}else{
						document.forms[0].elements["turnoverreport.showMain"].value ="Y";
					}

					if(document.forms[0].elements["turnoverreport.showSub"].checked == false){
						document.forms[0].elements["turnoverreport.showSub"].value ="N";
					}else{
						document.forms[0].elements["turnoverreport.showSub"].value ="Y";
					}

					if ((document.forms[0].elements["turnoverreport.showMain"].checked == true) || (document.forms[0].elements["turnoverreport.showSub"].checked == true))
					{

						document.forms[0].method.value = "report";
						document.forms[0].entityId.value = document.forms[0].elements["turnoverreport.id.entityId"].value;
						document.forms[0].entityText.value =document.forms[0].entityText.value;

						document.forms[0].selectedFromDate.value = document.forms[0].elements["turnoverreport.dateAsString"].value;
						document.forms[0].selectedToDate.value = document.forms[0].elements["turnoverreport.toDateAsString"].value;

						document.forms[0].selectedShowMain.value = document.forms[0].elements["turnoverreport.showMain"].value;
						document.forms[0].selectedShowSub.value = document.forms[0].elements["turnoverreport.showSub"].value;


						if(document.forms[0].elements["turnoverreport.pdfOrExcel"][0].checked)
							document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.TURNOVER_OUTPUT_PDF%>";
						else
							document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.TURNOVER_OUTPUT_EXCEL%>";

						if(document.forms[0].elements["turnoverreport.forecastedOrActuals"][0].checked)
							document.forms[0].selectedDatasource.value = "<%=SwtConstants.TURNOVER_DATASOURCE_FORECASTED%>";
						else
							document.forms[0].selectedDatasource.value = "<%=SwtConstants.TURNOVER_DATASOURCE_ACTUALS%>";

						if(document.forms[0].elements["turnoverreport.singleOrRange"][0].checked)
							document.forms[0].singleOrDateRange.value = "<%=SwtConstants.TURNOVER_SINGLE_DAY%>";
						else
							document.forms[0].singleOrDateRange.value = "<%=SwtConstants.TURNOVER_DATE_RANGE%>";


						document.forms[0].submit();
					}else{
						alert("<fmt:message key="alert.atleastAnyOneLevelBreakdown"/>");
					}
					setParentChildsFocus();
					/*Start : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
				}
				/*End : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
			} else {
				alert("<fmt:message key="alert.enterValidDate"/>");
			}
		}
		/*End: Marshal: Modified by Marshal for Mantis 1262 on 20-11-2010*/


		function submitFormByEnity(methodName){
			document.forms[0].method.value = methodName;
			document.forms[0].changeEntity.value = "Y";
			document.forms[0].entityId.value = document.forms[0].elements["turnoverreport.id.entityId"].value;
			if (configScheduler == "true") {
				document.forms[0].configScheduler.value = configScheduler;
				document.forms[0].newTurnoverReportSchedConfig.value = true;
				document.forms[0].schedulerConfigXML.value = schedulerConfigXML;
				document.forms[0].reportType.value = reportType;
				document.forms[0].jobId.value = '${jobId}';
			}
			document.forms[0].submit();

		}


		var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';


		/* Start:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */
		function validateTurnOverDateField(){
			if(document.forms[0].elements['turnoverreport.dateAsString']!=null && document.forms[0].elements['turnoverreport.dateAsString'].value != ""){
				if(validateDateField(document.forms[0].elements['turnoverreport.dateAsString'],'turnoverReport.date',dateFormat)){
				}
			}
		}



		function validateDatesFromReportConfig(){
			if(isSingleDate){
				if(document.forms[0].elements['turnoverreport.dateAsString'].value == "" ){
					alert('<fmt:message key="alert.enterValidDate"/>');
				}else{
					document.forms[0].elements['turnoverreport.toDateAsString'].value ="";

					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');
					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/ilmReport.do?method=validateDates";
					requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["turnoverreport.id.entityId"].value +"&fromDate=" + document.forms[0].elements['turnoverreport.dateAsString'].value + "&toDate=" + document.forms[0].elements['turnoverreport.toDateAsString'].value;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var result = new String(oXMLHTTP.responseText);


					if(result == "true") {
						return true;
					}else {
						alert('<fmt:message key="alert.enterValidToDate"/>');
						return false;
					}
					//return validateDateField('reports.fromDateAsString','fromDateAsString');
				}
			}else{
				if(document.forms[0].elements['turnoverreport.dateAsString'].value == ""){
					alert('<fmt:message key="alert.enterValidFromDate"/>');
				}else if (document.forms[0].elements['turnoverreport.toDateAsString'].value == ""){
					alert('<fmt:message key="alert.enterValidToDate"/>');
				}else{

					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/' + appName + '/');

					requestURL = requestURL.substring(0, idy + 1);
					requestURL = requestURL + appName
							+ "/ilmReport.do?method=validateDates";
					requestURL = requestURL + "&dateFormat="+dateFormat+"&entityId=" + document.forms[0].elements["turnoverreport.id.entityId"].value +"&fromDate=" + document.forms[0].elements['turnoverreport.dateAsString'].value + "&toDate=" + document.forms[0].elements['turnoverreport.toDateAsString'].value;

					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var result = new String(oXMLHTTP.responseText);
					if(result == "true") {
						return true;
					}else {
						alert('<fmt:message key="alertDateShouldNotBeEarlierEhanFromDate"/>');
						return false;
					}

				}
			}

		}



		function disableEnterKey(e) {
			var key;
			if(window.event)
				key = window.event.keyCode; //IE
			else
				key = e.which; //firefox

			return (key != 13);
		}

		function validateForm(objForm){
			var elementsRef = new Array(1);
			elementsRef[0] = objForm.elements["turnoverreport.dateAsString"];
			return validate(elementsRef);
		}

		function validateDateField(elem,fieldName){
			var isValid = false;
			if(dateFormat == 'datePat2'){
				isValid = (validateField(document.forms[0].elements[elem],fieldName,'datePat4'));
			} else if(dateFormat == 'datePat1'){
				isValid = (validateField(document.forms[0].elements[elem],fieldName,'datePat1'));
			}
			if(!isValid)
				document.forms[0].elements[elem].value="";

			var from_date=document.forms[0].elements['turnoverreport.dateAsString'].value;
			var to_date=document.forms[0].elements['turnoverreport.toDateAsString'].value;


			if (document.forms[0].elements["turnoverreport.singleOrRange"][1].checked) {
				var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
				if(compare_date == 1)
				{

					alert("<fmt:message key="alert.dateShouldBeGreater"/>");
					setTimeout(function(){document.getElementById(elem).focus();}, 1);
					document.forms[0].elements[elem].value = "";
					return false;
				}
			}

			return isValid;
		}


		function saveSchedulerReportOptions(){

			if(validateDatesFromReportConfig()){
				var schedulerConfigXML;
				var reportSchedulerData = new Object();
				reportSchedulerData.jobId = '${jobId}';

				if(document.forms[0].elements["turnoverreport.dateAsString"].value != "") {
					if(document.forms[0].elements["turnoverreport.showMain"].checked == false){
						document.forms[0].elements["turnoverreport.showMain"].value ="N";
					}else{
						document.forms[0].elements["turnoverreport.showMain"].value ="Y";
					}

					if(document.forms[0].elements["turnoverreport.showSub"].checked == false){
						document.forms[0].elements["turnoverreport.showSub"].value ="N";
					}else{
						document.forms[0].elements["turnoverreport.showSub"].value ="Y";
					}

					if ((document.forms[0].elements["turnoverreport.showMain"].checked == true) || (document.forms[0].elements["turnoverreport.showSub"].checked == true)){

						document.forms[0].method.value = "report";
						document.forms[0].entityId.value = document.forms[0].elements["turnoverreport.id.entityId"].value;
						document.forms[0].entityText.value =document.forms[0].entityText.value;

						document.forms[0].selectedFromDate.value = document.forms[0].elements["turnoverreport.dateAsString"].value;
						document.forms[0].selectedToDate.value = document.forms[0].elements["turnoverreport.toDateAsString"].value;

						document.forms[0].selectedShowMain.value = document.forms[0].elements["turnoverreport.showMain"].value;
						document.forms[0].selectedShowSub.value = document.forms[0].elements["turnoverreport.showSub"].value;


						if(document.forms[0].elements["turnoverreport.pdfOrExcel"][0].checked)
							document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.TURNOVER_OUTPUT_PDF%>";
						else
							document.forms[0].selectedOutputFormat.value = "<%=SwtConstants.TURNOVER_OUTPUT_EXCEL%>";

						if(document.forms[0].elements["turnoverreport.forecastedOrActuals"][0].checked)
							document.forms[0].selectedDatasource.value = "<%=SwtConstants.TURNOVER_DATASOURCE_FORECASTED%>";
						else
							document.forms[0].selectedDatasource.value = "<%=SwtConstants.TURNOVER_DATASOURCE_ACTUALS%>";

						if(document.forms[0].elements["turnoverreport.singleOrRange"][0].checked)
							document.forms[0].singleOrDateRange.value = "<%=SwtConstants.TURNOVER_SINGLE_DAY%>";
						else
							document.forms[0].singleOrDateRange.value = "<%=SwtConstants.TURNOVER_DATE_RANGE%>";

						reportSchedulerData.reportType =  reportType;
						reportSchedulerData.entityId = document.forms[0].entityId.value ;
						reportSchedulerData.entityName = document.forms[0].entityText.value;
						reportSchedulerData.singleOrDateRange =  document.forms[0].singleOrDateRange.value;
						reportSchedulerData.dateAsString =  document.forms[0].elements["turnoverreport.dateAsString"].value;
						reportSchedulerData.toDateAsString = document.forms[0].elements["turnoverreport.toDateAsString"].value;
						reportSchedulerData.singleOrRange = document.forms[0].singleOrDateRange.value;
						reportSchedulerData.pdfOrExcel = document.forms[0].selectedOutputFormat.value;
						reportSchedulerData.forecastedOrActuals = document.forms[0].selectedDatasource.value;
						reportSchedulerData.selectedShowMain = document.forms[0].selectedShowMain.value
						reportSchedulerData.selectedShowSub = document.forms[0].selectedShowSub.value
						window.close();
						schedulerConfigXML = convertConfigObjectToXML(reportSchedulerData);
					}else{
						alert("<fmt:message key="alert.atleastAnyOneLevelBreakdown"/>");
					}


				} else {
					alert("<fmt:message key="alert.enterValidDate"/>");
				}


				this.opener.document.forms[0].schedulerConfigXML.value = getMenuWindow().encode64(schedulerConfigXML.innerHTML);
				var savedOutputFormat = document.forms[0].selectedOutputFormat.value;
				if (savedOutputFormat=="P") {
					savedOutputFormat = "PDF";
				} else {
					savedOutputFormat = "EXCEL";
				}
				this.opener.updateSchedulerConfigParams(savedOutputFormat);

			}
		}


		/**
		 *
		 * Used to create XML Data that contain information
		 * about scheduler report configuration
		 *
		 **/
		function convertConfigObjectToXML(reportSchedulerData){
			var schedulerConfigXML = document.createElement("div");
			var transactionNode = document.createElement("schedConfig");

			var jobIdNode = document.createElement("jobid");
			jobIdNode.appendChild(document.createTextNode(reportSchedulerData.jobId));

			var reportTypeNode = document.createElement("reporttype");
			reportTypeNode.appendChild(document.createTextNode(reportSchedulerData.reportType));

			var entityIdNode = document.createElement("entityid");
			entityIdNode.appendChild(document.createTextNode(reportSchedulerData.entityId));

			var entityNameNode = document.createElement("entityname");
			entityNameNode.appendChild(document.createTextNode(reportSchedulerData.entityName));

			var singleOrDateRangeNode = document.createElement("singleorrange");
			singleOrDateRangeNode.appendChild(document.createTextNode(reportSchedulerData.singleOrDateRange));

			var dateasstringNode = document.createElement("dateasstring");
			dateasstringNode.appendChild(document.createTextNode(reportSchedulerData.dateAsString));

			var dateasstringNode = document.createElement("dateasstring");
			dateasstringNode.appendChild(document.createTextNode(reportSchedulerData.dateAsString));

			var todateasstringNode = document.createElement("todateasstring");
			todateasstringNode.appendChild(document.createTextNode(reportSchedulerData.toDateAsString));

			var pdfOrExcelNode = document.createElement("pdfOrExcel");
			pdfOrExcelNode.appendChild(document.createTextNode(reportSchedulerData.pdfOrExcel));

			var forecastedOrActualsNode = document.createElement("forecastedoractuals");
			forecastedOrActualsNode.appendChild(document.createTextNode(reportSchedulerData.forecastedOrActuals));

			var selectedShowMainNode = document.createElement("selectedshowmain");
			selectedShowMainNode.appendChild(document.createTextNode(reportSchedulerData.selectedShowMain));

			var selectedShowSubNode = document.createElement("selectedshowsub");
			selectedShowSubNode.appendChild(document.createTextNode(reportSchedulerData.selectedShowSub));

			var dateFormatNode = document.createElement("dateformatasstring");
			dateFormatNode.appendChild(document.createTextNode(dateFormatValue));

			transactionNode.appendChild(jobIdNode);
			transactionNode.appendChild(reportTypeNode);
			transactionNode.appendChild(entityIdNode);
			transactionNode.appendChild(entityNameNode);
			transactionNode.appendChild(singleOrDateRangeNode);
			transactionNode.appendChild(dateasstringNode);
			transactionNode.appendChild(todateasstringNode);
			transactionNode.appendChild(pdfOrExcelNode);
			transactionNode.appendChild(forecastedOrActualsNode);
			transactionNode.appendChild(selectedShowMainNode);
			transactionNode.appendChild(selectedShowSubNode);
			transactionNode.appendChild(dateFormatNode);
			schedulerConfigXML.appendChild(transactionNode);
			return schedulerConfigXML;
		}


		/* End:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */


	</SCRIPT>

	<style>
		#ilmStartDay{
			margin-left: -200px;
			margin-top: -170px;
		}
		input[name ="keywordInput"] {
			width: 120px
		}

	</style>

</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setFocus(document.forms[0]);setParentChildsFocus();" onunload="call();">
<form action="turnoverReport.do" method="post">
	<input name="method" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="entityText" type="hidden" value="">
	<input name="selectedFromDate" type="hidden" value="">
	<input name="selectedToDate" type="hidden" value="">
	<input name="selectedShowMain" type="hidden" value="">
	<input name="selectedShowSub" type="hidden" value="">
	<input name="selectedOutputFormat" type="hidden" value="">
	<input name="selectedDatasource" type="hidden" value="">
	<input name="singleOrDateRange" type="hidden" value="">
	<input name="changeEntity" type="hidden" value="">
	<input name="configScheduler" type="hidden" value="">
	<input name="reportType" type="hidden" value="">
	<input name="jobId" type="hidden" value="">
	<input name="newTurnoverReportSchedConfig" type="hidden" value="">
	<input name="schedulerConfigXML" type="hidden" value="">

	<div id="TurnoverReport" style="position:absolute; left:20px; top:22px; width:570px; height:210px; border:2px outset;" color="#7E97AF">
		<div id="TurnoverReport" style="position:absolute; left:8px; top:4px; width:560px; height:149px;">
			<div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
			<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
			</iframe>
			<table width="559px" border="0" cellpadding="0" cellspacing="0" height="30" class="content">

				<tr height="13">
					<td  width="300px"><b><fmt:message key="turnoverReport.entity"/></b></td>
					<td width="18px">&nbsp;</td>
					<td  width="185px" >
						<!--START:code modified by Mahesh on 04-Mar-2010 for Mantis 1112: modified the tooltip-->
						<select id="selectEntity" name="turnoverreport.id.entityId" class="htmlTextAlpha" titleKey="tooltip.selectEntityid" style="width:120px" tabindex="1">
							<c:forEach items="${requestScope.entities}" var="entity">
								<option value="${entity.value}" <c:if test="${turnoverreport.id.entityId == entity.value}">selected</c:if> >
						  ${entity.label}
				  </option>
							</c:forEach>
						</select>
					</td>

					<td width="775px">
						<span id="entityName" name="entityName" class="spantext"></span>
					</td>
			</table>
			<div style="position: absolute; top: 25; margin-top: 10">
				<table style="position: absolute; width: 520">
					<tr>
    <td width="110" style="box-sizing: border-box;">
        <b style="position:relative;">
            <fmt:message key="label.entityMonitor.date" />
        </b>
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.singleOrRange"
               value="<%=SwtConstants.TURNOVER_SINGLE_DAY%>"
               id="singleDayOption"
               tabindex="7"
               style="margin-right: 10px;"
               ${turnoverreport.singleOrRange == 'S' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="singleDayOption" title='<fmt:message key="ilmReport.singleDay"/>'>
            <fmt:message key="ilmReport.singleDay" />
        </label>&nbsp;
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.singleOrRange"
               value="<%=SwtConstants.TURNOVER_DATE_RANGE%>"
               id="dateRangeOption"
               tabindex="8"
               style="margin-right: 10px;"
               ${turnoverreport.singleOrRange == 'R' ? 'checked="checked"' : ''} />
    </td>
    <td width="150">
        <label for="dateRangeOption" title='<fmt:message key="ilmReport.dateRange"/>'>
            <fmt:message key="ilmReport.dateRange" />
        </label>&nbsp;
    </td>
</tr>

				</table>

				<div style="position: absolute; width: 350; left: 130; top: 30">
					<div style="position: absolute;">
						<input type="text" tabindex="9" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="turnoverreport.dateAsString" value="${turnoverreport.dateAsString}"  style="width:80px;margin-bottom:5px;height:21"  maxlength="10" id="dateInput" onchange="validateDateField('turnoverreport.dateAsString','turnoverReport.date');" onmouseout="dateSelected=false" />
						<A title='<fmt:message key="tooltip.calendarreportdate"/>'
						   tabindex="10"  name="datelink" id="datelink"
						   onClick="cal.select(document.forms[0].elements['turnoverreport.dateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img
								src="images/calendar-16.gif"></A>
					</div>
					<div  id="divTo" style="position: absolute;left: 120; top: 5;visibility: hidden;" >
						<label ><fmt:message key="movementsearch.valueto"/></label>
					</div>
					<div id="divToTime" style="position: absolute;left: 153;visibility: hidden;">
						<input type="text" tabindex="11" id="dateInput1" titleKey="tooltip.reportDate" cssClass="htmlTextNumeric" name="turnoverreport.toDateAsString" value="${turnoverreport.toDateAsString}"  style="width:80px;margin-bottom:5px;height:21;"  maxlength="10" onchange="validateDateField('turnoverreport.toDateAsString','turnoverReport.date');" onmouseout="dateSelected=false" />
						<A title='<fmt:message key="tooltip.calendarreportdate"/>'
						   tabindex="12" name="datelink1" id="datelink1"
						   onClick="cal.select(document.forms[0].elements['turnoverreport.toDateAsString'],'datelink1',dateFormatValue);dateSelected=true;return false;"><img
								src="images/calendar-16.gif"/></A>

					</div>
				</div>
			</div>

			<div style="position: absolute; top: 90; margin-top: 10">
				<table style="position: absolute; width: 470">
					<tr>
    <td width="130" style="box-sizing: border-box;">
        <b style="position:relative;">
            <fmt:message key="turnoverReport.dataSource" />
        </b>
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.forecastedOrActuals"
               value="<%=SwtConstants.TURNOVER_DATASOURCE_FORECASTED%>"
               id="forecastedOption"
               tabindex="13"
               style="margin-right: 10px;"
               ${turnoverreport.forecastedOrActuals == 'F' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="forecastedOption" title='<fmt:message key="turnoverReport.forecasted"/>'>
            <fmt:message key="turnoverReport.forecasted" />
        </label>&nbsp;
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.forecastedOrActuals"
               value="<%=SwtConstants.TURNOVER_DATASOURCE_ACTUALS%>"
               id="actualsOption"
               tabindex="14"
               style="margin-right: 10px;"
               ${turnoverreport.forecastedOrActuals == 'A' ? 'checked="checked"' : ''} />
    </td>
    <td width="150">
        <label for="actualsOption" title='<fmt:message key="turnoverReport.actuals"/>'>
            <fmt:message key="turnoverReport.actuals" />
        </label>&nbsp;
    </td>
</tr>


				</table>
			</div>

			<div style="position: absolute; top: 120; margin-top: 10">
				<table style="position: absolute; width: 470">
					<tr>
    <td width="130" style="box-sizing: border-box;">
        <b style="position:relative;">
            <fmt:message key="turnoverReport.outputFormat" />
        </b>
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.pdfOrExcel"
               value="<%=SwtConstants.TURNOVER_OUTPUT_PDF%>"
               id="pdfOutputOption"
               tabindex="15"
               style="margin-right: 10px;"
               ${turnoverreport.pdfOrExcel == 'P' ? 'checked="checked"' : ''} />
    </td>
    <td width="115">
        <label for="pdfOutputOption" title='<fmt:message key="turnoverReport.pdf"/>'>
            <fmt:message key="turnoverReport.pdf" />
        </label>&nbsp;
    </td>
    <td width="32">
        <input type="radio" name="turnoverreport.pdfOrExcel"
               value="<%=SwtConstants.TURNOVER_OUTPUT_EXCEL%>"
               id="excelOutputOption"
               tabindex="16"
               style="margin-right: 10px;"
               ${turnoverreport.pdfOrExcel == 'E' ? 'checked="checked"' : ''} />
    </td>
    <td width="150">
        <label for="excelOutputOption" title='<fmt:message key="turnoverReport.excel"/>'>
            <fmt:message key="turnoverReport.excel" />
        </label>&nbsp;
    </td>
</tr>


				</table>
			</div>
			<div style="position: absolute; top: 150; margin-top: 10">
				<table style="position: absolute; width: 473">
					<tr height="13">
						<td width="130px"><b><fmt:message key="turnoverReport.levelBreakdown"/></b></td>
						<td width="32px"><input type="checkbox"  name="turnoverreport.showMain" value="Y" ${requestScope.turnoverreport.showMain == 'Y' ? 'checked' : ''} titleKey="tooltip.showMain" fieldValue="Y" value='${requestScope.turnoverreport.showMain == "Y"}' cssClass="htmlTextAlpha" tabindex="17" /></td>
						<td width="115"><label> <fmt:message key="turnoverReport.showMain"/>
						</label>&nbsp;</td>
						<td width="36px"><input type="checkbox"  name="turnoverreport.showSub" value="Y" ${requestScope.turnoverreport.showSub == 'Y' ? 'checked' : ''} titleKey="tooltip.showSub" fieldValue="Y" value='${requestScope.turnoverreport.showSub == "Y"}' cssClass="htmlTextAlpha" tabindex="18" /></td>
						<td width="150"><label> <fmt:message key="turnoverReport.showSub"/>
						</label>&nbsp;</td>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>

	<div id="TurnoverReport" style="position:absolute;z-index:99; left:515; top:245; width:70px; height:29px; visibility:visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td align="Right">
					<a tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','Turnover Report'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
				</td>
			</tr>
		</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:20; top:235; border:2px outset; width:570px; height:40px; visibility:visible;">
		<div id="TurnoverReport" style="position:absolute; left:6; top:6; width:425; height:15px; visibility:visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td id="reportbutton" width="70"> </td>

					<td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
						<a tabindex="20" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>

				</tr>
			</table>
		</div>
	</div>


	<div id="TurnoverReport" style="position:absolute; left:6; top:4; width:425; height:15px; visibility:hidden;">
		<table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
			<tr>
				<c:if test="${'true' == requestScope.configScheduler}">

					<td id="reportenablebutton"><a tabindex="20" title='<fmt:message key="tooltip.save"/>' width="70" onMouseOut="collapsebutton(this)"
												   onMouseOver="highlightbutton(this)"  onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
												   onclick="saveSchedulerReportOptions('turnoverReport');"><fmt:message key="button.save"/></a></td>

</c:if>
				<c:if test="${'true' != requestScope.configScheduler}">

					<td id="reportenablebutton" >
						<a tabindex="20" title='<fmt:message key="tooltip.reportbutton"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:checkLevelBreakdown('report');"><fmt:message key="button.report"/></a>
					</td>
					<td id="reportdisablebutton">
						<a class="disabled" disabled="disabled"><fmt:message key="button.report"/></a>
					</td>

</c:if>

			</tr>
		</table>
	</div>



	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
<DIV ID="caldiv" STYLE="position:absolute;visibility:hidden;background-color:white;layer-background-color:white;"></DIV>
<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;"></iframe>
</body>
</html>