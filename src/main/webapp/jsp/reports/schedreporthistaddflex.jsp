<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">


<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<!--<head>
		<title>
			<fmt:message key="schedReportHist.detailsScreen.title"/>
		</title>
		<style>
			body {
				margin: 0px;
				overflow: hidden
			}
		</style>-->
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">

	var screenTitle = "";
	screenTitle = getMessage("schedReportHist.detailsScreen.title", null);
	document.title = screenTitle;
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "schedReportHistAdd";

	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	var mainWindow= null;
	requestURL=requestURL.substring(0,idy+1) ;

	var dbDate= "<%=SwtUtil.getSystemDateString() %>"
	var dateFormat = '${sessionScope.CDM.dateFormatValue}';


	var fileId = '${requestScope.fileId}';			var menuAccessId = '${requestScope.menuAccessId}';
	var screenName = '${requestScope.screenName}';
	// This method is called when onload
	window.onload = function () {
		setTitleSuffix(document.forms[0]);
		setParentChildsFocus();
	};

	window.onunload = call;
	var menuAccessId = '${requestScope.menuAccessId}';
	// Set the label values
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();
	label["alert"] = new Array ();

	label["text"]["screen-title"] = "<fmt:message key="label.schedreporthist.title.window"/>";
	// label  fields

	//tooltip fileds

	// Save button
	label["text"]["button-save"] = "<fmt:message key="button.save"/>";
	label["tip"]["button-save"] = "<fmt:message key="button.save"/>";

	// Cancel button
	label["text"]["button-cancel"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-cancel"] = "<fmt:message key="tooltip.close"/>";

	// Alert messages
	label["alert"]["delete-confirm"] = "<fmt:message key="alert.deletion.confirm"/>";
	label["alert"]["delete-record"] = "<fmt:message key="confirm.delete"/>";

	label["text"]["label-fileId"] = "<fmt:message key="label.schedReportHist.fileId"/>";
	label["tip"]["label-fileId"] = "<fmt:message key="tip.schedReportHist.fileId"/>";

	label["text"]["label-jobId"] = "<fmt:message key="label.schedReportHist.jobId"/>";
	label["tip"]["label-jobId"] = "<fmt:message key="tip.schedReportHist.jobId"/>";

	label["text"]["label-reportTypeId"] = "<fmt:message key="label.schedReportHist.reportTypeId"/>";
	label["tip"]["label-reportTypeId"] = "<fmt:message key="tip.schedReportHist.reportTypeId"/>";

	label["text"]["label-reportName"] = "<fmt:message key="label.schedreporthist.column.reportName"/>";
	label["tip"]["label-reportName"] = "<fmt:message key="label.schedreporthist.column.reportName"/>";

	label["text"]["label-scheduleId"] = "<fmt:message key="label.schedReportHist.scheduleId"/>";
	label["tip"]["label-scheduleId"] = "<fmt:message key="tip.schedReportHist.scheduleId"/>";


	label["text"]["label-runDate"] = "<fmt:message key="label.schedReportHist.runDate"/>";
	label["tip"]["label-runDate"] = "<fmt:message key="tip.schedReportHist.runDate"/>";


	label["text"]["label-elapsedTime"] = "<fmt:message key="label.schedReportHist.elapsedTime"/>";
	label["tip"]["label-elapsedTime"] = "<fmt:message key="tip.schedReportHist.elapsedTime"/>";

	label["text"]["label-fileName"] = "<fmt:message key="label.schedReportHist.fileName"/>";
	label["tip"]["label-fileName"] = "<fmt:message key="tip.schedReportHist.fileName"/>";

	label["text"]["label-outputLocation"] = "<fmt:message key="label.schedReportHist.outputLocation"/>";
	label["tip"]["label-outputLocation"] = "<fmt:message key="tip.schedReportHist.outputLocation"/>";

	label["text"]["label-fileSize"] = "<fmt:message key="label.schedReportHist.fileSize"/>";
	label["tip"]["label-fileSize"] = "<fmt:message key="tip.schedReportHist.fileSize"/>";

	label["text"]["label-exportStatus"] = "<fmt:message key="label.schedReportHist.exportStatus"/>";
	label["tip"]["label-exportStatus"] = "<fmt:message key="tip.schedReportHist.exportStatus"/>";

	label["text"]["label-mailStatus"] = "<fmt:message key="label.schedReportHist.mailStatus"/>";
	label["tip"]["label-mailStatus"] = "<fmt:message key="tip.schedReportHist.mailStatus"/>";

	label["text"]["label-exportError"] = "<fmt:message key="label.schedReportHist.exportError"/>";
	label["tip"]["label-exportError"] = "<fmt:message key="tip.schedReportHist.exportError"/>";

	label["text"]["label-mailRsult"] = "<fmt:message key="label.schedReportHist.mailRsult"/>";
	label["tip"]["label-mailRsult"] = "<fmt:message key="tip.schedReportHist.mailRsult"/>";

	label["text"]["label-errorDescription"] = "<fmt:message key="label.schedReportHist.errorDescription"/>";
	label["tip"]["label-errorDescription"] = "<fmt:message key="tip.schedReportHist.errorDescription"/>";

	// Used to check if the object have an existing data
	function checkExistingDataMethod(fileId){

		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');

		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/schedReportHist.do?method=checkExistingDataMethod";
		requestURL =     requestURL + "&fileId="+fileId;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		var res = new String(oXMLHTTP.responseText);
		return res == "N" ? false : true;
	}


	/**
	 * Close window
	 */
	function closeWindow() {
		//refresh data grid in parent screen
		window.opener.refreshGridData();
		window.close();
	}

	/**
	 * help
	 * This function opens the help screen
	 */
	function help() {
		openWindow(
				buildPrintURL('print', 'Define SchedReportHist'),
				'sectionprintdwindow',
				'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
				'true')
	}
</script>
<!--</head>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
		<form id="exportDataForm" target="tmp" method="post">
			<div id="swf">
				<object id='mySwf'
						classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='100%'
						width='100%'>
					<param name='src' value='jspreports/schedreporthistadd.swf?version=<%= SwtUtil.appVersion %>' />
					<param name='flashVars' value='' />
					<embed name='mySwf' src='jsp/reports/schedreporthistadd.swf?version=<%= SwtUtil.appVersion %>'
						   height='100%' width='100%' flashVars='' />
				</object>
			</div>
			<input type="hidden" name="data" id="exportData" />
			<input type="hidden" name="screen" id="exportDataScreen"
				   value="<fmt:message key="label.schedReportHist.title.window"/>" />
		</form>-->
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="<fmt:message key="label.schedreporthist.title.window"/>" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>