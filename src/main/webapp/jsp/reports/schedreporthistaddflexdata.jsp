<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>



<schedreporthist>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<singletons>
		<c:if test="${'addScreen' != requestScope.screenName}">

			<fileId>${requestScope.schedReportHist.id.fileId}</fileId>
			<hostId>${requestScope.schedReportHist.hostId}</hostId>
			<jobId>${requestScope.schedReportHist.jobId}</jobId>
			<jobName>${requestScope.schedReportHist.jobName}</jobName>
			<reportTypeId>${requestScope.schedReportHist.reportTypeId}</reportTypeId>
			<reportTypeName>${requestScope.schedReportHist.reportTypeName}</reportTypeName>
			<reportName>${requestScope.schedReportHist.reportName}</reportName>
			<scheduleId>${requestScope.schedReportHist.scheduleId}</scheduleId>
			<scheduleName>${requestScope.schedReportHist.scheduleName}</scheduleName>
			<runDate>${requestScope.schedReportHist.runDateAsString}</runDate>
			<elapsedTime>${requestScope.schedReportHist.elapsedTimeAsString}</elapsedTime>
			<fileName>${requestScope.schedReportHist.fileName}</fileName>
			<outputLocation>${requestScope.schedReportHist.outputLocation}</outputLocation>
			<fileSize>${requestScope.schedReportHist.fileSize}</fileSize>
			<exportStatus>${requestScope.schedReportHist.exportStatus}</exportStatus>
			<mailStatus>${requestScope.schedReportHist.mailStatus}</mailStatus>
			<mailStatusAsString>${requestScope.schedReportHist.mailStatusAsString}</mailStatusAsString>
			<exportError>${requestScope.schedReportHist.exportError}</exportError>
			<mailRsult>${requestScope.schedReportHist.mailRsult}</mailRsult>
			<mailError>${requestScope.schedReportHist.mailError}</mailError>

		</c:if>
	</singletons>

</schedreporthist>