<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<!-- Start: Code modified by Kalidass G for Mantis 827 on 09-08-2010 -->
<c:if test="${'Y' == requestScope.rawData}">

<title>
	<fmt:message key="intradayBalances.accountScreen"/>
</title>

</c:if>
<c:if test="${'N' == requestScope.rawData}">

<title>
	<fmt:message key="intradayBalances.accountScreenForGraph"/>
</title>

</c:if>
<!-- End: Code modified by Kalidass G for Mantis 827 on 09-08-2010 -->
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<SCRIPT language="JAVASCRIPT">
var entityId= "${requestScope.entityId}";
var reportDate="${requestScope.selectedReportDate}";
var accountIdlen= "${requestScope.accountIdListSize}";
var rawData= "${requestScope.rawData}";
function bodyOnLoad()
{

	if(accountIdlen > 0){
	document.getElementById("reportbutton").innerHTML = document.getElementById("reportenablebutton").innerHTML;
	}
	else{
	//alert(" No account(s) for this currency" );
	//self.close();
	document.getElementById("reportbutton").innerHTML = document.getElementById("reportdisablebutton").innerHTML;
	}
}

function submitForm(methodName){
	   /* Start: Code added/modified/commented by Kalidass G for Mantis 827 on 09-08-2010 */
	   document.forms[0].entityId.value = '${requestScope.entityId}';
	   document.forms[0].entityText.value ='${requestScope.entityName}';
	   document.forms[0].currencyText.value ='${requestScope.currencyName}';
	   document.forms[0].selectedReportDate.value = '${requestScope.selectedReportDate}';
	   document.forms[0].selectedCurrencyCode.value = '${requestScope.selectedCurrencyCode}';
		if(rawData == "Y"){
			var oldTarget = document.forms[0].target;
	    	document.forms[0].method.value = methodName;
	    	document.forms[0].target = 'tmp';
	    	document.forms[0].selectedRawdata.value = '${requestScope.rawData}';
			document.forms[0].submit();
			document.forms[0].target = oldTarget;
		}else{
		   document.forms[0].method.value = "reportForGraph";
		   document.forms[0].selectedRawdata.value = "N";
		   document.forms[0].submit();
		   setParentChildsFocus();
		}
		 /* End: Code added/modified/commented by Kalidass G for Mantis 827 on 09-08-2010 */
}

</SCRIPT>
<body style ="overflow-y: hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call();" >
<form action="intradayBalances.do" method="post"   >
<input name="method" type="hidden" value="">
<input name="hostId" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="entityText" type="hidden" value="">
<input name="currencyText" type="hidden" value="">
<input name="selectedReportDate" type="hidden" value="">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="selectedRawdata" type="hidden" value="">

  <div id="IntradayBalancesChild" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:400px; height:50px;">

 <div style="position:absolute; left:3px; top:10px; width:400px; height:80px;">
	<!--START:Code Modified by Krishna on 02-DEC-2010 for Mantis 1249:To Extend account id to 20 on screen && 35 on database -->
	<table width="390" border="0" cellpadding="0" cellspacing="0" >
		<tr height="23" >
			<td  width="110px"><b style="padding-left: 5px;"><fmt:message key="intradayBalances.accountId"/></b></td>

			<td  width="30px" >
				<select id="intradaybalances.id.accountId" name="intradaybalances.id.accountId" class="htmlTextAlpha" tabindex="2" style="width:245px" titleKey="tooltip.selectAccountId">
					<c:forEach var="item" items="${requestScope.accountIdList}">
						<option value="${item.value}" <c:if test="${item.value == intradaybalances.id.accountId}">selected</c:if>>${item.label}</option>
					</c:forEach>
				</select>


			</td>


		</tr>

		</table>

	<!--END:Code Modified by Krishna on 02-DEC-2010 for Mantis 1249:To Extend account id to 20 on screen && 35 on database -->
</div>
</div>

 <div id="IntradayBalancesChild" style="position:absolute; left:345px; top:83; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

			<td align="Right">
				 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Intraday Balance Account'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			 </td>


		</tr>
	</table>
</div>
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:75px; width:400px; height:39px; visibility:visible;">
 <div id="IntradayBalancesChild" style="position:absolute; left:6; top:4; width:400; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" >
		<tr>
		 <td id="reportbutton" width="70">

		</td>
		  <td  width="70px" id="cancelbutton">
               <a  tabindex="5" title='<fmt:message key="tooltip.cancel"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" ><fmt:message key="button.cancel"/></a>
		  </td>
        </tr>
    </table>
</div>
</div>
<div id="IntradayBalancesChild" style="position:absolute; left:6; top:4; width:400; height:15px; visibility:visible;">
    <table width="140" border="0" cellspacing="0" cellpadding="0"  style="visibility:hidden">
	<tr>
		<td id="reportenablebutton" >
		<a  tabindex="4" title='<fmt:message key="tooltip.report"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"  onclick="javascript:submitForm('report');" onMouseUp="highlightbutton(this)" ><fmt:message key="button.report"/></a>
		</td>
		<td id="reportdisablebutton">
			<!-- Start:Code modified by Kalidass for Mantis 827 on 12-08-2010 -->
			<a class="disabled" disabled="disabled"><fmt:message key="button.report"/></a>
			<!-- End:Code modified by Kalidass for Mantis 827 on 12-08-2010 -->
		</td>

	</tr>
    </table>
  </div>

</form>
<!-- Start:03-02-2009:Kalidass : code modified to avoid security alert for mantis 893 -->
<iframe name="tmp" width="0%" height="0%" src="#"/>
<!-- End:03-02-2009:Kalidass : code modified to avoid security alert for mantis 893 -->
</body>
</html>