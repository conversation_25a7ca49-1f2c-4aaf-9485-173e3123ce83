<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<%
	//variable declaration
	String params = "";
	String fromMenu = "";

	//get the params from request attribute
	if (request.getAttribute("params") != null) {
		params = request.getAttribute("params")
				.toString();
	}


	//get the fromMenu from request attribute
	if (request.getAttribute("fromMenu") != null) {
		fromMenu = request.getAttribute("fromMenu")
				.toString();
	}


%>




<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Alert Instance Display  - SMART-Predict</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var params = "<%= params %>";
	var fromMenu = "<%= fromMenu %>";
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "AlertInstanceDisplay";


	/**
	 * Open the facility screen. It depends on scenario && the related properties of the selected count
	 *
	 **/
	function openFacility(scenarioTitle, useGeneric, facilityId,facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold,count){

		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1);
		// If the use geneic is 'Y' then we have to display the generic display else a facility screen will be opened
		if (useGeneric == 'Y'){
			// Get the base query of the scenario id from data base through AJAX
			requestURL = requestURL + appName+"/scenMaintenance.do?method=getScenPropertiesForGeneric";
			requestURL = requestURL + "&scenarioId=" + scenarioId;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			oXMLHTTP.send();
			var scenProperties = new String(oXMLHTTP.responseText);
			var baseQuery = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[0];
			var entityColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[1];
			var ccyColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[2];
			var refColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[3];
			var facilityrefColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[4];
			var refParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[5];
			var facilityRefParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[6];
			// Encode the base query do not have problems with special characters
			baseQuery = getMenuWindow().encode64(baseQuery);

			// We have to filter the data in the opened window, generic display screen
			var filter = "";
			if (entityId.toUpperCase() == 'ALL')
				filter == 'All' + '|';
			else
				filter = entityColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + entityId +"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str|';

			if (currencyId.toUpperCase() == 'ALL')
				filter += 'All';
			else
				filter += ccyColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + currencyId+"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str';

			// Encode the defined filter value as it may contains double quote (")
			filter = getMenuWindow().encode64(filter);
			var param = 'genericdisplay.do?method=genericDisplay';
			param+='&scenarioID='+scenarioId;
			param+='&fromSummaryScreen='+"true";
			param+='&scenarioTitle='+scenarioTitle;
			param+='&refColumns='+refColumns;
			param+='&facilityRefColumns='+ facilityrefColumns;
			param+='&refParams='+ refParams;
			param+='&facilityRefParams='+ facilityRefParams;
			param+='&applyCurrencyThreshold='+ applyCurrencyThreshold;
			param+='&facilityID='+facilityId;
			param+='&facilityName='+facilityName;
			param+='&basequery='+baseQuery;
			param+= '&filter=' + filter;

			// Open the generic screen
			openWindow(param,'genericdisplay','left=50,top=190,width=1230,height=480,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else{
			// Open the movement summary screen if the facility id is 'MSD'
			if (facilityId == "MSD"){
				var sysdate = "<%=SwtUtil.getSystemDateString()%>";
				var url="outstandingmovement.do?";
				url += "method=flex";
				// Define the initial input screen as X for scenario summary
				url += "&initialinputscreen=X";
				url += "&totalFlag=Y";
				// the position level is 9, the hightest level.
				url += "&posLvlId=9";
				url += "&currencyCode="+currencyId;
				url += "&entityId=" + entityId;
				url += "&date=" + sysdate;
				url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
				url += "&workflow=";
				url += "&scenarioId=" + scenarioId;
				openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
			}// Open the movement match summary display screen if the facility id is 'MATCH_DISPLAY_MANY'
			else if (facilityId == "MATCH_DISPLAY_MANY"){
				var sysdate = "<%=SwtUtil.getSystemDateString()%>";
				var url="movementmatchdisplay.do?";
				url += "&status=M";
				url += "&quality=D";
				url += "&matchCount="+count;
				url += "&currencyCode="+currencyId;
				url += "&entityId=" + entityId;
				url += "&date=";
				url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
				url += "&day=NotAll";
				url += "&scenarioId=" + scenarioId;
				url += "&dateTabIndFlag=N";
				url += "&dateTabInd=0";
				openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
			}else {
				requestURL = new String('<%=request.getRequestURL()%>');
				idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1);
				requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
				requestURL = requestURL + "&facilityId=" + facilityId;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var screenDetails=new String(oXMLHTTP.responseText);
				var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
				programName = screenDetailsList[0];
				var actionPathStr = programName.replace('?', '.');
				var menuAccessIdChild = getMenuAccessIdOfChildWindow(actionPathStr);
				if (programName.indexOf("?") == -1)
					programName += '?';
				width = screenDetailsList[1];
				height = screenDetailsList[2];
				appName = "<%=SwtUtil.appName%>";
				requestURL = new String('<%=request.getRequestURL()%>');
				idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1);
				requestURL = requestURL + appName+"/scenMaintenance.do?method=getAdditionalInfo";
				requestURL = requestURL + "&scenarioId=" + scenarioId;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var additionalParams=new String(oXMLHTTP.responseText);
				var key = "&entityId="+ entityId +"&selectedEntityId=" + entityId;
				key += "&currencyId=" + currencyId;
				key += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
				key += "&calledFrom=generic";
				key += "&menuAccessId="+menuAccessIdChild;
				key +=additionalParams;
				// Open the facility screen
				openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
			}
		}
	}

	/**
	 * method to open the facility screen related to the facility id of a selected scenario
	 **/
	function goTo(facilityID, hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){

		if (hostID == "" || entityID == "") {
			alert("<fmt:message key="alert.FacilityMissingValues"/>");
		} else {
			if (!facilityAccess(hostID, entityID, facilityID, currencyCodeKey))
				alert("<fmt:message key="alert.accessToFacility"/>");
			else{
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
				requestURL = requestURL + "&facilityId=" + facilityID;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var screenDetails=new String(oXMLHTTP.responseText);

				var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
				var programName = screenDetailsList[0];
				if (programName.indexOf("?") == -1)
					programName += '?';
				var width = screenDetailsList[1];
				var height = screenDetailsList[2];

				var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
				key += "&matchId=" + matchIdKey;
				key += "&calledFrom=generic";
				key += "&currencyId=" + currencyCodeKey;
				key += "&selectedMovementId=" + movementIdKey;
				key += "&selectedSweepId=" + sweepIdKey;
				key +=getMenuWindow().decode64(additionalParams);
				openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
			}
		}
	}

	/**
	 * Return true || false, if the user has access to a facility screen || not
	 *
	 * @param hostId
	 * @param entityId
	 * @param facilityId
	 * @param currencyCode
	 **/
	function facilityAccess(hostId, entityId, facilityId, currencyCode){


		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
		requestURL = requestURL + "&hostId=" + hostId;
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&facilityId=" + facilityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		var access=new String(oXMLHTTP.responseText);
		return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
	}


	function openInstMsgScreen(methodName, instanceId){
		var param = '/' + appName + '/scenarioSummary.do?method='+methodName;
		param += '&instanceId=' + instanceId;
		var 	mainWindow = openWindow (param, 'alertInstDisplay','left=10,top=230,width=750,height=350,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}


</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>