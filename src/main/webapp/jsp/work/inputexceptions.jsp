<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title><fmt:message key="inputexceptions.title.window"/><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
	var refresh = false;
	var testDate= "<%=SwtUtil.getSystemDateString() %>";
	var screenRoute = "InputException";
	//code added for 1053_STL_2 issue by karthik on 20110921
	//To hold window object for  input exceptions message details
	var winObject = null;
	var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";


	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();
	label["tip"]["datefromDDMMYY"] = "<fmt:message key="tooltip.selectFromDateDDMMYY"/>";

	label["tip"]["datefromMMDDYY"] = "<fmt:message key="tooltip.selectFromDateMMDDYY"/>";

	label["tip"]["showdays"] = "<fmt:message key="tooltip.showdays"/>";
	label["text"]["showdays"] = "<fmt:message key="text.showdays"/>";
	label["text"]["day"] = "<fmt:message key="text.day"/>";
	label["text"]["days"] = "<fmt:message key="text.days"/>";

	label["tip"]["refresh_button"] = "<fmt:message key="tooltip.refreshWindow"/>";
	label["tip"]["rate_button"] = "<fmt:message key="tooltip.RateWindow"/>";
	label["tip"]["close_button"] = "<fmt:message key="tooltip.close"/>";

	/*Start : Added By Imed B on 19-02-2014*/
	label["text"]["label-inputExceptionMonitor"] = "<fmt:message key="inputException.title"/>";
	label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
	label["text"]["alert-notANumber"] = "<fmt:message key="inputException.notANumber"/>";
	label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
	label["text"]["alert-rateSelected"] = "<fmt:message key="inputException.rateSelected"/>";
	label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
	label["text"]["label-startDate"] = "<fmt:message key="inputException.startDate"/>";
	label["text"]["label-endDate"] = "<fmt:message key="inputException.endDate"/>";
	label["text"]["label-inputDate"] = "<fmt:message key="inputException.inputDate"/>";
	label["text"]["label-enterFromDate"] = "<fmt:message key="inputException.enterFromDate"/>";
	label["text"]["label-refresh"] = "<fmt:message key="inputException.refresh"/>";
	label["text"]["label-rate"] = "<fmt:message key="inputException.rate"/>";
	label["text"]["label-close"] = "<fmt:message key="inputException.close"/>";
	label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
	label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
	label["text"]["label-from"]="<fmt:message key="label.from"/>";
	label["text"]["label-dataBuildInProgress"]="<fmt:message key="screen.buildInProgress"/>";
	label["text"]["label-connectionError"]="<fmt:message key="screen.connectionError"/>";
	label["text"]["label-showValue"]="<fmt:message key="inputException.showValue"/>";
	/*End : Added By Imed B on 19-02-2014*/

	var dateFormat = '${sessionScope.CDM.dateFormatValue}';

	window.onload = function () {
		setTitleSuffix(document.forms[0]);
		setParentChildsFocus();
<%-- 		var so = new SWFObject("jsp/work/inputexceptions.swf?version=<%= SwtUtil.appVersion %>", "inputExceptions", "100%", "100%", "9", "#D6E3FE"); --%>
// 		so.write("inputExceptionscontent");
	}
	var userId = '${requestScope.userId}';
	var itemId = '${requestScope.itemId}';
	var fromPCM = '${requestScope.fromPCM}';

	 // Start:added by Mefteh for Mantis 2016
	<% List<Integer> listPriorAheadDays=SwtUtil.getPriorAndAheadDaysToToday();%>
    var nDaysPriorToToday =<%=listPriorAheadDays.get(0)%>;
	var nDaysAheadToToday =<%=listPriorAheadDays.get(1)%>;
     // End: added by Mefteh for Mantis 2016

	//flash uses this to determine what to request to save a new refresh rate
	function getUpdateRefreshRequest (rate) {
 		return "screenOption.do?method=save&screenOption.id.hostId=" + "<%=SwtUtil.getCurrentHostId()%>" + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + "<%=SwtConstants.INPUTEXCEPTION_ID%>" + "&screenOption.propertyValue=" + rate;
	}

	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}

	/* Start:code added for 1053_STL_2 issue by karthik on 20110921 */
	/**
 	  * clickInterface
      *
      * This method is used to open the input exceptions message details monitor
      */
	function clickInterface(urlString) {
		//if window is already opened then close that window
		if(winObject != null) {
			//call the window close
			winObject.close();
		}
		//open the input exceptions message details monitor
		var winTop = window.screenTop ? window.screenTop : window.screenY;
		var winLeft = window.screenLeft ? window.screenLeft : window.screenX;
		winObject = window.open(urlString,'exceptions','left='+winLeft+',top='+winTop+',width=1100,height=680,toolbar=0, resizable=yes, status=yes,scrollbars=yes','true');
	}
	function help(){
		openWindow(buildPrintURL('print','Input Exception'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
    }

	/* End:code added for 1053_STL_2 issue by karthik on 20110921 */
</script>
<%@ include file="/angularscripts.jsp"%>


<!-- <form action="inputexceptions.do"  style="display:none;"> -->
<!-- 	<input name="method" type="hidden" value=""> -->
<!-- </form> -->
<form id="exportDataForm" target="tmp" method="post"><input
	type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<fmt:message key="inputexceptions.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" /> 
</body>
</html>