<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="SweepMsgDisplay.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
function disableAllButtons()
{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
}

function enableAllButtons()
{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
}
function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function bodyOnLoad()
{
	xl = new XLSheet("SweepMsgDisplaycoll","table_2", ["String","String", "String","String"],"1111");
	xl.onsort = xl.onfilter = onFilterandSort;
	disableAllButtons();
	highlightTableRows("SweepMsgDisplaycoll");

}
/* START : Code change at offshore for Integration */
function onSelectTableRow(rowElement, isSelected){

	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedMessageId.value = hiddenElement.value;
	//alert(document.forms[0].selectedMessageId.value);


	if(isSelected) {
		enableAllButtons();
	}	else{
			disableAllButtons();
	}

}

function openMessageView(method){
	var param = 'inputexceptionsmessages.do?method=single&seqid=';
    param += document.forms[0].selectedMessageId.value;

	return param;
}
/* END: Code change at offshore for Integration */

</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="sweepdetail.do" method="post">
<input name="selectedMessageId" type="hidden">

<div id="SweepMsgDisplay" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:505px; height:353px;">
<div id="SweepMsgDisplayHeader" style="position:absolute;z-index:99;left:0px; top:0px; width:500; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="480" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr height="20px">

			<td title='<fmt:message key="tooltip.GeneratedOnDate"/>'width="80px"><b><fmt:message key="SweepMsgDisplay.GeneratedOnDate"/></b></td>

			<td title='<fmt:message key="tooltip.GeneratedOnTime"/>'width="80px"><b><fmt:message key="SweepMsgDisplay.GeneratedOnTime"/></b></td>

			<td title='<fmt:message key="tooltip.Message"/>' width="120px" ><b><fmt:message key="SweepMsgDisplay.Message"/></b></td>


			<td title='<fmt:message key="tooltip.Status"/>' width="200px"><b><fmt:message key="SweepMsgDisplay.Status"/></b></td>



		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:502;height:350px">
	<div id="SweepMsg" style="position:absolute;z-index:99;left:0px; top:21px; width:485; height:10px;">
	<table class="sort-table" id="SweepMsgDisplaycoll" width="485" border="0" cellspacing="1" cellpadding="0" height="329px">
	<tbody>
		<%int count = 0; %>

		<c:forEach  items="${requestScope.sweepMessageList}" var='sweepMessageList' >
		<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %>
		<tr class="odd"> <%}++count; %>
		<input type="hidden" name="sweepMessageList.messageId" value="${sweepMessageList.messageId}" />
			<td width="80">${sweepMessageList.updateDateAsString}&nbsp;</td>

			<td width="80">${sweepMessageList.updateTime}&nbsp;</td>

			<td width="120px">${sweepMessageList.msgFormatId}&nbsp;</td>

			<td width="200px">${sweepMessageList.status}&nbsp;</td>

		</tr>
		</c:forEach>
	</tbody>
<tfoot><tr><td colspan="4" ></td></tr></tfoot>
</table>
	</div>
  </div>
</div>


<div id="SweepingQueues" style="position:absolute; left:450; top:386; width:70; height:15px; visibility:visible;">
	<table width="60" cellspacing="0" cellpadding="0" height="20" border="0">
		<tr>


		<td align="Right">
			<a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Sweep Message Summary'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		 </td>

			<td align="right" valign="middle" id="Print">
				<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif" name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>' ></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:379; width:505; height:39px; visibility:visible;">
  <div id="SweepMsgDisplay" style="position:absolute; left:6; top:4; width:505; height:15px; visibility:visible;">


  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70px"  id="viewbutton" ></td>
			<td id="closebutton">
				<a tabindex="2" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>


	</div>
	<div style="position:absolute; left:6; top:4; width:505; height:15px; visibility:hidden;">
    <table width="140" border="1" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="viewenablebutton">
					<a title='<fmt:message key="tooltip.viewSelAc"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(openMessageView('view'),'accountsmaintenanceview','left=50,top=190,width=450,height=500,toolbar=0, resizable=yes, scrollbars=yes','true')" tabindex="1"><fmt:message key="button.view"/></a>
				</td>

		</td>
		<td id="viewdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>

			</tr>
    </table>
  </div>

</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>


<script type="text/javascript">
</script>
</form>
</body>
</html>
 