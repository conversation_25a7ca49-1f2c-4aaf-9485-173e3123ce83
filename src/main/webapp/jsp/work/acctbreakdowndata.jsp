<?xml version="1.0" encoding="UTF-8"?>
<%--
    acctbreakdowndata.jsp

    <AUTHOR> R / 14-Mar-2012
    Modified By <PERSON><PERSON><PERSON> for Mantis 2142

    This file generates data for controls in XML format. This will be invoked by
    AJAX request on change of control value.
--%>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<acctbreakdown>
	<selects>
			<%-- START: CURRENCY DROPDOWN --%>
		<c:if test="${not empty requestScope.currencies}">
			<dropdown id="ddCurrencyCode">
				<select name="acctBreakdownMonitor.currencyCode" tabindex="3"
						onchange="onDataChange('currencyChanged')" style="width:55px"
						class="htmlTextAlpha" title="${tooltip.selectCurrencyCode}">
					<c:forEach items="${requestScope.currencies}" var="currency">
						<option value="${currency.value}"
								<c:if test="${requestScope.acctBreakdownMonitor.currencyCode == currency.value}">
									selected="selected"
								</c:if>>
								${currency.label}
						</option>
					</c:forEach>
				</select>
			</dropdown>
		</c:if>
			<%-- END: CURRENCY DROPDOWN --%>

			<%-- START: ACCOUNT CLASS DROPDOWN --%>
		<c:if test="${not empty requestScope.accountClasses}">
			<dropdown id="ddAcctClass">
				<select name="acctBreakdownMonitor.acctClass" tabindex="5"
						onchange="onDataChange('acctClassChanged')" style="width:92px"
						class="htmlTextAlpha" title="${tooltip.selectAccountClass}">
					<c:forEach items="${requestScope.accountClasses}" var="acctClass">
						<option value="${acctClass.value}"
								<c:if test="${requestScope.acctBreakdownMonitor.acctClass == acctClass.value}">
									selected="selected"
								</c:if>>
								${acctClass.label}
						</option>
					</c:forEach>
				</select>
			</dropdown>
		</c:if>
			<%-- END: ACCOUNT CLASS DROPDOWN --%>

			<%-- START: ACCOUNT DROPDOWN --%>
		<c:if test="${not empty requestScope.accountIds}">
			<dropdown id="ddAcctId">
				<select name="acctBreakdownMonitor.acctId" tabindex="7"
						onchange="javascript: refresh();" style="width:240px"
						class="htmlTextAlpha" title="${tooltip.selectAccountId}">
					<c:forEach items="${requestScope.accountIds}" var="accountId">
						<option value="${accountId.value}"
								<c:if test="${requestScope.acctBreakdownMonitor.acctId == accountId.value}">
									selected="selected"
								</c:if>>
								${accountId.label}
						</option>
					</c:forEach>
				</select>
			</dropdown>
		</c:if>
			<%-- END: ACCOUNT DROPDOWN --%>
	</selects>
</acctbreakdown>
