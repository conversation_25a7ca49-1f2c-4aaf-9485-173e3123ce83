<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="/taglib.jsp"%>

<!DOCTYPE html>
<html>
<head>
    <title>Account Breakdown Grid</title>
    <style>
        /* Styles remain the same */
        .accp { background-color: #some-color; }
        .accn { background-color: #another-color; }
        .even { background-color: #f2f2f2; }
        .odd { background-color: #ffffff; }
    </style>
</head>
<body>
    <div id="ddimagetabsgrid" style="position: absolute; left: 10px; top: 132px; width: 832px; height: 30px;">
        <c:set var="tabIndex" value="9" />
        <c:forEach items="${requestScope.tabDates}" var="tabInfo" varStatus="status">
            <a id="${tabInfo[0]}"
               onmouseout="revertback('${tabInfo[0]}', this);"
               onmouseover="changecontent('${tabInfo[0]}', this)"
               onkeydown="submitEnter(this, event)"
               tabindex="${tabIndex + status.index}"
               onclick="checkTabIndex(this)">
                <b>${tabInfo[1]}</b>
            </a>
        </c:forEach>
    </div>

    <%-- GRID HEADER --%>
    <div id="pnlGridHeader" style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1160px; height: 10px;">
        <table class="sort-table" id="tblGridHeader" bgcolor="#B0AFAF" width="1150px"
               border="1" cellspacing="1" cellpadding="0" height="25px" style="margin-top: 0px;">
            <thead>
                <tr height="20px">
                    <td width="25px" align="center">
                        <img src="images/Alert/alert.png" name="alert" border="0">
                    </td>
                    <td width="70px" align="center" title='<fmt:message key="tooltip.sortCurrencyCode"/>'>
                        <b><fmt:message key="label.acctBreakdown.ccy"/></b>
                    </td>
                    <td width="160px" align="center" title='<fmt:message key="tooltip.accounId"/>'>
                        <b><fmt:message key="label.acctBreakdown.acct"/></b>
                    </td>
                    <td width="270px" align="center" title='<fmt:message key="tooltip.sortAccountName"/>'>
                        <b><fmt:message key="label.acctBreakdown.acctName"/></b>
                    </td>
                    <td width="180px" align="center" title='<fmt:message key="tooltip.sortBalance"/>'>
                        <b><fmt:message key="label.acctBreakdown.balance"/></b>
                    </td>
                    <td width="180px" align="center" title='<fmt:message key="tooltip.sortOpenUnexpectedBalance"/>'>
                        <b><fmt:message key="label.acctBreakdown.openUnexpected"/></b>
                    </td>
                    <td width="180px" align="center" title='<fmt:message key="tooltip.sortStartingBalance"/>'>
                        <b><fmt:message key="label.acctBreakdown.startBalance"/></b>
                    </td>
                    <td width="90px" align="center" title='<fmt:message key="tooltip.acctBreakdown.sum"/>'>
                        <b><fmt:message key="label.acctBreakdown.sum"/></b>
                    </td>
                </tr>
            </thead>
        </table>
    </div>

<%-- GRID DATA --%>
<div id="ddscrolltable" style="position: absolute; left: 0px; top: 0px; width: 1168px; height: 455px; overflowY: scroll">
	<div id="pnlGrid" style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 1150px; height: 110px;">
		<table class="sort-table" id="tblGrid" width="1150px" border="0" cellspacing="1" cellpadding="0" height="432px">
			<tbody>
			<c:set var="count" value="0"/>
			<c:forEach items="${requestScope.acctBreakdownDetails}" var="acctBreakdownDetail">
				<c:choose>
					<c:when test="${acctBreakdownDetail.sum eq 'P'}">
						<c:set var="rowClass" value="accp"/>
					</c:when>
					<c:when test="${acctBreakdownDetail.sum eq 'N' or acctBreakdownDetail.summable eq 'N'}">
						<c:set var="rowClass" value="accn"/>
					</c:when>
					<c:otherwise>
						<c:set var="rowClass" value="${count % 2 == 0 ? 'even' : 'odd'}"/>
						<c:set var="count" value="${count + 1}"/>
					</c:otherwise>
				</c:choose>

				<tr class="${rowClass}">
						<%-- Alerting --%>
					<td width="25px" align="left">
						<c:if test="${acctBreakdownDetail.scenarioHighlighted eq 'C'}">
							<img onclick="setTimeout(() => { openAlertingWindow() }, 100);"
								 src="images/Alert/scenario/critical.png" align="left"
								 name="imgLoro" width="15" border="0">
						</c:if>
						<c:if test="${acctBreakdownDetail.scenarioHighlighted eq 'Y'}">
							<img onclick="setTimeout(() => { openAlertingWindow() }, 100);"
								 src="images/Alert/scenario/normal.png" align="left"
								 name="imgLoro" width="15" border="0">
						</c:if>
					</td>

						<%-- Currency Code --%>
					<td width="70px" align="left">${acctBreakdownDetail.currencyCode}</td>

						<%-- Account ID --%>
					<td width="160px" align="left">${acctBreakdownDetail.acctId}</td>

						<%-- Account Name --%>
					<td width="270px">${acctBreakdownDetail.acctName}</td>

						<%-- Predict Balance --%>
					<c:choose>
						<c:when test="${acctBreakdownDetail.includeLoroInPredictedColor eq 'L'}">
							<c:set var="cellClass" value="accp"/>
						</c:when>
						<c:when test="${acctBreakdownDetail.includeLoroInPredictedColor eq 'D'}">
							<c:set var="cellClass" value="accn"/>
						</c:when>
						<c:otherwise>
							<c:set var="cellClass" value=""/>
						</c:otherwise>
					</c:choose>

					<td width="180px" class="${cellClass}" align="right">
						<c:if test="${acctBreakdownDetail.includeLoroInPredictedIndicator eq 'P+'}">
							<img style="height:17px" src="images/P+.png" align="left" name="imgLoro" border="0">
						</c:if>
						<c:if test="${acctBreakdownDetail.includeLoroInPredictedIndicator eq 'P-'}">
							<img style="height:16px" src="images/P-.png" align="left" name="imgLoro" border="0">
						</c:if>
						<c:if test="${acctBreakdownDetail.includeLoroInPredictedIndicator eq 'L+'}">
							<img style="height:16px" src="images/L+.png" align="left" name="imgLoro" border="0">
						</c:if>
						<c:if test="${acctBreakdownDetail.includeLoroInPredictedIndicator eq 'L-'}">
							<img style="height:16px" src="images/L-.png" align="left" name="imgLoro" border="0">
						</c:if>

						<a href="#" onclick="return clickPredictedLink(this, event);"
						   style="${acctBreakdownDetail.predBalanceSign ne 'true' ? 'color:red' : ''}">
								${acctBreakdownDetail.predBalance}
						</a>
					</td>

						<%-- Unexpected Balance --%>
					<td width="180px" align="right">
						<a href="#" onclick="return clickUnexpectedLink(this, event);"
						   style="${acctBreakdownDetail.unexpectedBalanceSign ne 'true' ? 'color:red' : ''}">
								${acctBreakdownDetail.unexpectedBalance}
						</a>
					</td>

						<%-- Starting Balance --%>
					<td width="180px" align="right">
						<a href="#" onclick="return clickSODLink(this, event);"
						   style="${acctBreakdownDetail.startingBalanceSign ne 'true' ? 'color:red' : ''}">
								${acctBreakdownDetail.startingBalance}
						</a>
					</td>

						<%-- Sum Flag --%>
					<td width="90px" align="center">${acctBreakdownDetail.sum}</td>

						<%-- Hidden fields --%>
					<input type="hidden" id="isLoroOrCurrAcct" name="isLoroOrCurrAcct" value="${acctBreakdownDetail.isLoroOrCurrAcct}" />
					<input type="hidden" id="loroToPredictedFlag" name="loroToPredictedFlag" value="${acctBreakdownDetail.loroToPredictedFlag}" />
					<td style="display: none">${acctBreakdownDetail.accountStatus}</td>
					<td style="display: none">${acctBreakdownDetail.summable}</td>
				</tr>
			</c:forEach>
			</tbody>
		</table>
	</div>
</div>

<form id="acctbreakdownmonitor" action="acctbreakdownmonitor.do" method="post">
        <%-- BALANCE TOTAL --%>
        <div id="pnlTotal" style="position: absolute; z-index: 99; left: 0px; top: 460px; border: 3px; width: 1150px; height: 10px;">
            <table id="monitorDetailsTotal" width="1150px" border="0" cellspacing="1" cellpadding="0" height="25">
                <tbody>
                    <tr>
                        <td align="right" width="500px"><b><fmt:message key="label.acctBreakdown.total"/></b></td>
                        <td width="180px">
                            <input type="text"
                                   name="acctBreakdownMonitor.predBalanceTotal"
                                   id="acctBreakdownMonitor.predBalanceTotal"
                                   class="htmlTextNumeric"
                                   maxlength="10"
                                   tabindex="18"
                                   titleKey="tooltip.totalBalance"
								   style="width:180px; ${requestScope.acctBreakdownMonitor.predBalanceTotalSign == 'true' ? '':'color:red;' }"
								   readonly="readonly"
                                   value="${requestScope.acctBreakdownMonitor.predBalanceTotal}"/>

                        </td>
                        <td width="180px">
                            <input type="text"
                                   name="acctBreakdownMonitor.unexpectedBalanceTotal"
                                   id="acctBreakdownMonitor.unexpectedBalanceTotal"
                                   class="htmlTextNumeric"
                                   maxlength="10"
                                   tabindex="19"
                                   titleKey="tooltip.totalOpenUnexpectedBalance"
                                   style="width:180px; ${requestScope.acctBreakdownMonitor.unexpectedBalanceTotalSign == 'true' ? '' : 'color:red'}"
                                   readonly="readonly"
                                   value="${requestScope.acctBreakdownMonitor.unexpectedBalanceTotal}"/>
                        </td>
                        <td width="180px">
                            <input type="text"
                                   name="acctBreakdownMonitor.startingBalanceTotal"
                                   id="acctBreakdownMonitor.startingBalanceTotal"
                                   class="htmlTextNumeric"
                                   maxlength="10"
                                   tabindex="20"
                                   titleKey="tooltip.totalStartingBalance"
                                   style="width:180px; ${requestScope.acctBreakdownMonitor.startingBalanceTotalSign == 'true' ? '' : 'color:red'}"
                                   readonly="readonly"
                                   value="${requestScope.acctBreakdownMonitor.startingBalanceTotal}"/>
                        </td>
                        <td width="90px">&nbsp;</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <%-- Hidden fields remain the same --%>
        <input type="hidden" id="acctBreakdownMonitor.tabFlag" name="acctBreakdownMonitor.tabFlag" value="${requestScope.acctBreakdownMonitor.tabFlag}" />
        <input type="hidden" id="acctBreakdownMonitor.dataBuildStatus" name="acctBreakdownMonitor.dataBuildStatus" value="${requestScope.acctBreakdownMonitor.dataBuildStatus}" />
        <input type="hidden" id="acctBreakdownMonitor.lastRefreshTime" name="acctBreakdownMonitor.lastRefreshTime" value="${requestScope.acctBreakdownMonitor.lastRefreshTime}" />

        <input type="hidden" id="menuEntityCurrGrpAccess"
               name="menuEntityCurrGrpAccess"
               value="${requestScope.menuEntityCurrGrpAccess}" />
        <input type="hidden" id="refreshValueDate"
               name="refreshValueDate"
               value="${requestScope.refreshValueDate}" />
        <input type="hidden" id="acctBreakdownMonitorCurrentEntity"
               name="acctBreakdownMonitorCurrentEntity"
               value="${requestScope.acctBreakdownMonitorCurrentEntity}" />
    </form>
</body>
</html>