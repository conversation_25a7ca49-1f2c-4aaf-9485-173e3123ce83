<?xml version="1.0" encoding="UTF-8" ?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for Forecast Monitor Options screen.
-
- Author(s): Bala .D
- Date: 08-05-2011
-->
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>








<c:set var="bucketCount" value="${requestScope.userBucket.size()}" />
<c:set var="templateCount" value="${requestScope.userTemplate.size()}" />

<monitoroptions>
    <request_reply>
        <status_ok>${requestScope.reply_status_ok}</status_ok>
        <message>${requestScope.reply_message}</message>
        <location />
    </request_reply>

    <templategrid>
        <metadata>
            <columns>
                <column
                        heading="<fmt:message key="label.forecastMonitor.entity"/>"
                        draggable="false"
                        filterable="true"
                        type="str"
                        dataelement="entity"
                        width="140"
                        sortable="true"
                />
                <column
                        heading="<fmt:message key="label.forecastMonitorGrid.currency"/>"
                        draggable="false"
                        filterable="true"
                        type="str"
                        dataelement="currency"
                        width="85"
                        sortable="true"
                />
                <column
                        heading="<fmt:message key="label.forecastTemplateOption.template"/>"
                        draggable="false"
                        filterable="true"
                        type="str"
                        dataelement="templateid"
                        width="180"
                        sortable="true"
                />
            </columns>
        </metadata>
        <rows size="${templateCount}">
            <c:forEach items="${requestScope.userTemplate}" var="template">
                <row>
                    <entity clickable="false">${template.id.entityId}</entity>
                    <currency clickable="false">${template.id.currencyCode}</currency>
                    <templateid clickable="false">${template.templateId}</templateid>
                    <modifystate clickable="false">true</modifystate>
                </row>
            </c:forEach>
        </rows>
    </templategrid>

    <bucketgrid>
        <metadata>
            <columns>
                <column
                        heading="<fmt:message key="label.forecastTemplateOption.bucketNo"/>"
                        draggable="false"
                        type="str"
                        dataelement="bucketid"
                        width="110"
                        filterable="false"
                        sortable="false"
                />
                <column
                        heading="<fmt:message key="label.forecastTemplateOption.endsAt"/>"
                        draggable="false"
                        type="combo"
                        dataprovider="type"
                        dataelement="daysto"
                        width="160"
                        filterable="false"
                        sort="false"
                        clickable="true"
                />
                <column
                        heading="<fmt:message key="label.forecastTemplateOption.expand"/>"
                        draggable="false"
                        type="bool"
                        dataelement="bucketstate"
                        width="110"
                        filterable="false"
                        sortable="false"
                />
            </columns>
        </metadata>
        <rows size="${bucketCount}">
            <c:forEach items="${requestScope.userBucket}" var="bucket">
                <row>
                    <bucketid clickable="false">${bucket.id.bucketId}</bucketid>
                    <daysto expandenable="true" clickable="true">${bucket.daysTo}</daysto>
                    <bucketstate expandenable="true">
                        <c:choose>
                            <c:when test="${bucket.bucketState != 'N'}">true</c:when>
                            <c:otherwise>false</c:otherwise>
                        </c:choose>
                    </bucketstate>
                    <modifystate>true</modifystate>
                    <expandenable>true</expandenable>
                </row>
            </c:forEach>
        </rows>
    </bucketgrid>

    <options>
        <c:set var="options" value="${requestScope.forecastMonitorOptions}" />
        <multiplier>
                ${options.useCurrencyMultiplier}
        </multiplier>

        <hideweekend>
                ${options.hideWeekends}
        </hideweekend>

        <hidezerosum>
                ${options.hideZeroSum}
        </hidezerosum>

        <hidezerovalue>
                ${options.hideZeroValue}
        </hidezerovalue>

        <cumulativetotal>
                ${options.cumulativeBucketTotal}
        </cumulativetotal>

        <entity>
                ${options.entity}
        </entity>

        <currency>
                ${options.currency}
        </currency>

        <hidetotal>
                ${options.hideTotal}
        </hidetotal>

        <hideassumption>
                ${options.hideAssumption}
        </hideassumption>

        <hidescenario>
                ${options.hideScenario}
        </hidescenario>

    </options>

    <selects>
        <select id="type">
            <c:forEach items="${requestScope.endTo}" var="endDate">
                <option value="${endDate.label}">${endDate.label}</option>
            </c:forEach>
        </select>
        <select id="entity">
            <c:forEach items="${requestScope.reportingCurr}" var="entity">
                <option
                        value="${entity.value}"
                        selected="${options.entity == entity.value ? '1' : '0'}"
                >${entity.label}</option>
            </c:forEach>
        </select>
        <select id="currency">
            <c:forEach items="${requestScope.currencyList}" var="currency">
                <option
                        value="${currency.value}"
                        selected="${options.currency == currency.value ? '1' : '0'}"
                >${currency.label}</option>
            </c:forEach>
        </select>
    </selects>
</monitoroptions>