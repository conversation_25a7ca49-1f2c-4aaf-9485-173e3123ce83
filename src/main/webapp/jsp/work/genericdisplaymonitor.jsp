<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.model.MenuItem"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>
<html lang="en">

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title><fmt:message key="genericdisplaymonitor.title.window" /></title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<script type="text/javascript" src="js/js.cookie.min.js"></script>
		<script type="text/javascript">
			var screenRoute = "genericDisplay";

			// timer used for cheking if download has been finished
			var fileDownloadCheckTimer;
			window.onload = function () {
				
				var encode = '${basequery}';
				document.forms[0].basequery.value = getMenuWindow().decode64(encode);
				setParentChildsFocus();
				setTitleSuffix(document.forms[0]); 
			}
			
			/**
			 * if the document is ready then add a listener to exportDataForm in order to capture when 
			 * the download will be finished after exporting
			 */
			$(document).ready(function () {
			    $('#exportDataForm').submit(function () {
			    	blockUIForDownload();
			    });
			});
			
			/**
			 * This is used to capture when the download has been finished in order to close the popup in flex part
			 */
			function blockUIForDownload() {
				// Use the current timestamp as the token value
			    var token = new Date().getTime(); 
			    // Set the hidden input download_token_value_id as token. It will be sent to the action part.
				$('#download_token_value_id').val(token);
			   	/* The setInterval() method calls a function at specified intervals (in milliseconds). 
			   	   If we received the same token (from cookie) then the download is finished */
			    fileDownloadCheckTimer = window.setInterval(function () {
			      var downloadToken;
				  var downloadError;
			      var cookieValue = Cookies.get('fileDownloadToken');
			      downloadToken=downloadError=cookieValue;  
			      if (cookieValue!= null){
				      var fileDownloadTokenValue= cookieValue.split("|");
				      downloadToken=fileDownloadTokenValue[0];
				      downloadError=fileDownloadTokenValue[1] ;
			      }
			      if (downloadToken == token)
			       downloadFinished();
			      if (downloadError == "KO")
			     	 errorFileDownload() ;
			    }, 300); // the intervals (in milliseconds) on how often to execute the code  
			}
			
			/**
			 * Clear the interval, clear the token and then close the popup in flex part using call back method 
			 */
			function downloadFinished() {
				try{
				  window.clearInterval(fileDownloadCheckTimer);
				  Cookies.set('fileDownloadToken', null); //clears this cookie value
				  instanceElement.closePopup();
				}catch(e){
					console.log('e',e);
				}
			}
			function errorFileDownload(){
				try{

				 window.clearInterval(fileDownloadCheckTimer);	
				 Cookies.set('fileDownloadToken', null); //clears this cookie value
				 instanceElement.downloadError();
			}catch(e){
				console.log('e',e);
			}
			}
			
		</script>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
			var scenarioID = '${scenarioID}';
			var scenarioTitle = "${scenarioTitle}";
			var fromSummaryScreen = '${fromSummaryScreen}';
			var refColumns = getMenuWindow().encode64('${refColumns}'); 
			var facilityRefColumns = '${facilityRefColumns}';
			var refParams = '${refParams}';
			var facilityRefParams = '${facilityRefParams}';
			var encode = '${basequery}';
			var basequery=getMenuWindow().decode64(encode);
			var facilityID = '${facilityID}';
			var facilityName = "${facilityName}";
			var exportMaxPages = '${requestScope.exportMaxPages}';
			var plusEncoded = "<%=SwtConstants.PLUS_URL_ENCODED%>";
			var filter = '${filter}';
			var selectedCurrencyGroup = '${selectedCurrencyGroup}';
			var applyCurrencyThreshold = '${applyCurrencyThreshold}';
			var msdFacilityId = "<%=SwtConstants.FACILITY_MSD%>";
			var genericFacilityId = "<%=SwtConstants.FACILITY_GENERIC%>";
			var matchDisplayManyFacilityId = "<%=SwtConstants.FACILITY_MATCH_DISPLAY_MANY%>";
			var inputExceptionFacilityId = "<%=SwtConstants.FACILITY_INPUT_EXCEPTION%>";
			var NoneFacility = "<%=SwtConstants.FACILITY_NONE%>";
						
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["button-refresh"] = "<fmt:message key="button.genericdisplaymonitor.refresh"/>";
			label["text"]["button-close"] = "<fmt:message key="button.genericdisplaymonitor.close"/>";
			label["text"]["button-goto"] = "<fmt:message key="button.genericdisplaymonitor.goTo"/>";
			label["tip"]["button-goto-tooltip"] = "<fmt:message key="button.genericdisplaymonitor.goTo"/> "+facilityName;
			label["text"]["label-page"] = "<fmt:message key="text.days"/>";		
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
			label["tip"]["button-goto"] = "<fmt:message key="tooltip.goTo"/>";
			//Added By Imed B to remove hard-coded text from flex side
			label["text"]["label-genericDisplay"] = "<fmt:message key="genericDisplayMonitor.title.window"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["label-of"] = "<fmt:message key="genericDisplayMonitor.labelOf"/>";
			label["text"]["alert-contactAdmin"] = "<fmt:message key="genericDisplayMonitor.contactAdmin"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["alert-errorServerSide"] = "<fmt:message key="genericDisplayMonitor.errorServerSide"/>";
                        label["text"]["label-validPageNumber"] = "<fmt:message key="genericDisplayMonitor.validPageNumber"/>";
                        label["text"]["label-entity"] = "<fmt:message key="groupCode.entity"/>";
                        label["tip"]["label-selectedEntity"] = "<fmt:message key="tooltip.selectEntityId"/>";
                        label["text"]["label-currency"] = "<fmt:message key="groupMonitor.currency"/>";
                        label["tip"]["label-selectedCurrency"] = "<fmt:message key="tooltip.selectCurrencyId"/>";
		        label["text"]["label-location"] = "<fmt:message key="bookMonitor.location"/>";
                        label["tip"]["label-selectedLocation"] = "<fmt:message key="tooltip.selectLocationId"/>";
                        label["text"]["label-date"] = "<fmt:message key="bookMonitor.date"/>";
                        label["text"]["label-monitor"] = "<fmt:message key="bookMonitor.monitor"/>";
                        label["tip"]["label-selectMonitor"] = "<fmt:message key="tooltip.selectMonitor"/>";
                        label["text"]["label-predictedBalance"] = "<fmt:message key="accountmonitor.prbalance"/>";
                        label["tip"]["label-total"] = "<fmt:message key="positionlevel.total"/>";
			//End Added By Imed B to remove hard-coded text from flex side
			
			function help(){
				  openWindow(buildPrintURL('print','Generic display'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
			}
			
			function onExport(baseQuery, selectedFilter, selectedSort, exportType, noOfPages, currentPage, filter,fromSummaryScreen,selectedCurrencyGroup,applyCurrencyThreshold){
 				document.getElementById('exportDataForm').action='genericdisplay.do?method=exportGenericDetails';
				document.getElementById('scenarioID').value=document.forms[0].scenarioID.value;
				document.getElementById('scenarioTitle').value=document.forms[0].scenarioTitle.value;
				document.getElementById('basequery').value=baseQuery;
				document.getElementById('selectedFilter').value=selectedFilter;
				document.getElementById('selectedSort').value=selectedSort;
				document.getElementById('exportType').value=exportType;
				document.getElementById('pageCount').value=noOfPages;
				document.getElementById('currentPage').value=currentPage;
				document.getElementById('filter').value=filter;
				document.getElementById('selectedCurrencyGroup').value=selectedCurrencyGroup;
				document.getElementById('applyCurrencyThreshold').value=applyCurrencyThreshold;
				document.getElementById('fromSummaryScreen').value=fromSummaryScreen;
				document.getElementById('mybutton').click();
			}
			
			/**
			 * method to open the facility screen related to the facility id of a selected scenario
			 **/
			function goTo(hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){
				
				if (hostID == "" || entityID == "") {
					alert("<fmt:message key='alert.FacilityMissingValues'/>");
				} else {
					var facilityId = document.forms[0].facilityID.value;
					
					if (!facilityAccess(hostID, entityID, facilityId, currencyCodeKey))
						alert("<fmt:message key='alert.accessToFacility'/>");
					else{
							
						var appName = "<%=SwtUtil.appName%>";
						var requestURL = new String('<%=request.getRequestURL()%>');
						var idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1) ; 
						requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
						requestURL = requestURL + "&facilityId=" + facilityId;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var screenDetails=new String(oXMLHTTP.responseText);
						if(screenDetails == ""){
							return;
						}
						var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
						var programName = screenDetailsList[0];
						if (programName.indexOf("?") == -1)
							programName += '?';
						var width = screenDetailsList[1];
						var height = screenDetailsList[2];
						
						var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
						key += "&matchId=" + matchIdKey; 
						key += "&calledFrom=generic";
						key += "&currencyId=" + currencyCodeKey;
						key += "&selectedMovementId=" + movementIdKey;
						key += "&selectedSweepId=" + sweepIdKey;
						key +=getMenuWindow().decode64(additionalParams);
						openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}
				}
			}
			
			/**
			 * Return true or false, if the user has access to a facility screen or not
			 * 
			 * @param hostId
			 * @param entityId
			 * @param facilityId
			 * @param currencyCode
			 **/
			function facilityAccess(hostId, entityId, facilityId, currencyCode){
				
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ; 
				requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
				requestURL = requestURL + "&hostId=" + hostId;
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&facilityId=" + facilityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var access=new String(oXMLHTTP.responseText);
				return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
			}
		</script>
	</head>

	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
        <%@ include file="/angularscripts.jsp"%>
	
		<form id="exportDataForm" method="post" target="tmp">
			<input name="method" type="hidden" value="">
			<input name="scenarioID" type="hidden" value="${scenarioID}">
			<input name="scenarioTitle" type="hidden" value="${scenarioTitle}">
			<input name="facilityID" type="hidden" value="${facilityID}">
			<input name="facilityName" type="hidden" value="${facilityName}">
			<input name="refColumns" type="hidden" value="${refColumns}">
			<input name="basequery" type="hidden" value="${basequery}">
			<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
			<input name="selectedCurrencyGroup" type="hidden" value='${selectedCurrencyGroup}'>
			<input name="applyCurrencyThreshold" type="hidden" value='${applyCurrencyThreshold}'>
			<input name="selectedSort" type="hidden" value='${selectedSort}'>
			<input name="exportType" type="hidden" value="">
			<input name="pageCount" type="hidden" value=""> 
			<input name="currentPage" type="hidden" value=""> 
			<input name="filter" type="hidden" value=""> 
			<input name="fromSummaryScreen" type="hidden" value=""> 
			<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
			<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="genericdisplaymonitor.title.window" />" />
			
<!-- 				<div id="swf"> -->
<!-- 				<object id='mySwf' height='100%' width='100%' -->
<!-- 						classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000'> -->
<%-- 					<param name='src' value='jsp/work/genericdisplay.swf?version=<%= SwtUtil.appVersion %>' /> --%>
<!-- 					<param name='flashVars' value='' /> -->
<%-- 					<embed name='mySwf' src='jsp/work/genericdisplay.swf?version=<%= SwtUtil.appVersion %>' --%>
<!-- 					height='100%' width='100%' flashVars=''/> -->
<!-- 				</object> -->
<!-- 			</div> -->
			
			
			<input name="mybutton" type="submit" value="post" style="visibility: hidden;" />
			<iframe name="tmp" width="0%" height="0%" src="#" />
		</form>
	</body>
</html>