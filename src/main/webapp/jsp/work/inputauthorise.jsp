<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>

<head>
	<STYLE type="text/css">
		.sort-table tbody td
		{
			padding-right: 0px !important;
		}
		<!--
		A:link {text-decoration: none;}
		A:visited {text-decoration: none;}
		-->
	</STYLE>

	<title><fmt:message key="inputauthorise.title.window"/></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

	<link rel="stylesheet" type="text/css" href="style/displaytag.css" >

	<SCRIPT language="JAVASCRIPT">
		var isRefresfFromParent = "false";
		var appName = "<%=SwtUtil.appName%>";
		var dynamicTab = true;
		var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];

		var tabindex=${requestScope.selectedTabIndex};
		var tabname="${requestScope.selectedTabName}";
		var lastRefTime = "${requestScope.lastRefTime}";
		var selectedtab1 = initialtab[1];
		var previoustab=""

		var tabNames = new Array( 'MatchQueueTodayParent','MatchQueueParent');
		/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
		mandatoryFieldsArray= "undefined" ;

		function setTabInfo()
		{
			document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
			document.forms[0].elements["selectedTabName"].value = getselectedtab();
		}

		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;





		function call2(){
			if(isRefresfFromParent == "false")
			{
				call();
			}
		}

		var x5;

		function updateColors5()
		{
			var rows = x5.dataTable.tBody.rows;
			var l = rows.length;
			var count = 0;
			for (var i=0; i<l; i++)
			{
				if (x5.isRowVisible(i))
				{
					removeClassName(rows[i], count % 2 ? "odd" : "even");
					addClassName(rows[i], count % 2 ? "even" : "odd");
					count++;
				}
			}
		}

		var x2;

		function updateColors2()
		{
			var rows = x2.dataTable.tBody.rows;
			var l = rows.length;
			var count = 0;
			for (var i=0; i<l; i++)
			{
				if (x2.isRowVisible(i))
				{
					removeClassName(rows[i], count % 2 ? "odd" : "even");
					addClassName(rows[i], count % 2 ? "even" : "odd");
					count++;
				}
			}
		}

		var x3;

		function updateColors3()
		{
			var rows = x3.dataTable.tBody.rows;
			var l = rows.length;
			var count = 0;
			for (var i=0; i<l; i++)
			{
				if (x3.isRowVisible(i))
				{
					removeClassName(rows[i], count % 2 ? "odd" : "even");
					addClassName(rows[i], count % 2 ? "even" : "odd");
					count++;
				}
			}
		}

		var x4;

		function updateColors4()
		{
			var rows = x4.dataTable.tBody.rows;
			var l = rows.length;
			var count = 0;
			for (var i=0; i<l; i++)
			{
				if (x4.isRowVisible(i))
				{
					removeClassName(rows[i], count % 2 ? "odd" : "even");
					addClassName(rows[i], count % 2 ? "even" : "odd");
					count++;
				}
			}
		}
		function bodyOnLoad()
		{

			var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.id.entityId"],document.getElementById("entityDesc"));

			var dropBox2 = new SwSelectBox(document.forms[0].elements["movement.currencyCode"],document.getElementById("currencyDesc"));

			x5 = new XLSheet("inputAuthoriseListToday","table_1", ["String", "String", "String", "Number", "Number"],"22222");


			x5.onsort = x5.onfilter = updateColors5;



			changeselected('MatchQueueTodayParent');
			expandcontent('MatchQueueTodayParent', this);
			bringMyChildOnTop(window.name);
			<%int count = 0; %>

			document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
			document.getElementById("lastRefTime").innerText = lastRefTime;
		}


		function onSelectTableRow(rowElement, isSelected){


		}


		function refreshScreen(methodName){
			document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
			setTabInfoOnRefresh();
			document.forms[0].method.value = methodName;
			// Added by KaisBS for mantis 2099 to retain the selected entity after refresh
			document.forms[0].selectedEntityId.value=document.forms[0].elements["movement.id.entityId"].value;
			document.forms[0].currencyGroup.value = document.forms[0].elements["movement.currencyCode"].value;
			document.forms[0].submit();

		}
		function setTabInfoOnRefresh()
		{
			document.forms[0].elements["selectedTabIndex"].value = tabindex;
			document.forms[0].elements["selectedTabName"].value = tabname;
		}
		function showEntityDropDown()
		{
			document.getElementById("dropdowndiv_1").style.visibility="visible";
		}

		function clickLink(element,selectedMatchStatus,e)
		{
			var event = (window.event || e);
			document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

			var state="statuscheck";
			var oXMLHTTP = new XMLHttpRequest();
			var sURL = element.href;
			sURL = sURL +"&selectedMatchStatus="+selectedMatchStatus;
			sURL = sURL +"&menuAccessId="+document.forms[0].menuAccessId.value;
			sURL = sURL.replace("+", "%20");
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var str=new String(oXMLHTTP.responseText);
			if(str == "false"){

				ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.mvmQSelectionStChange"/>', YES_NO, yesFunc);

			}else{
				openWindow(sURL,'movementmatchdisplayWindow','left=0,top=55,width=1350,height=630,toolbar=0, resizable=yes, scrollbars=yes,status=yes');

			}
			if(event.preventDefault) {
				event.preventDefault();
			}else{
				window.event.returnValue = false;
			}
			return false;
		}
		function yesFunc(){
			setTabInfo();
			document.forms[0].method.value="displayListByEntity";
			document.forms[0].submit();
		}
		function submitForm(methodName){
			document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
			setTabInfo();
			document.forms[0].method.value = methodName;
			document.forms[0].selectedEntityId.value=document.forms[0].elements["movement.id.entityId"].value;
			document.forms[0].currencyGroup.value = document.forms[0].elements["movement.currencyCode"].value;
			document.forms[0].submit();
		}

		function submitFormEntityList(methodName){
			document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
			setTabInfo();
			document.forms[0].method.value = methodName;
			document.forms[0].selectedEntityId.value=document.forms[0].elements["movement.id.entityId"].value;
			document.forms[0].submit();
		}


		//This function is called when 'All' tab is pressed.
		function clickSelectedTab(object)
		{

			changeselected('MatchQueueParent');
			var tabIndex = getSelectedTabIndex();
			var originalTabIndex = "${requestScope.selectedTabIndex}";
			if(originalTabIndex != tabIndex)
			{
				expandcontent('MatchQueueTodayParent', object);
				submitForm("displayInputAuthorise");
			}

		}

		//This function is called when 'Today' tab is pressed.
		function checkTabIndexToday(obj) {

			changeselected('MatchQueueTodayParent');
			var tabIndex = getSelectedTabIndex();
			var originalTabIndex = "${requestScope.selectedTabIndex}";
			if(tabIndex != originalTabIndex) {
				expandcontent('MatchQueueTodayParent', obj);
				submitForm('displayInputAuthorise');
			}
		}





		function  changecontentMOR(name,aObj) {
			if(selectedtab1==name){
				if(aObj.className != "current" ) {
					aObj.className="current"
				}
			} else {
				if(aObj.className != "hoverall")  {
					aObj.className="hoverall"
				}
			}
		}
		function revertbackMOT(name,aObj) {
			if(selectedtab1==name){
				if(aObj.className != "current")  {
					aObj.className="current"
				}
			}else{
				if(aObj.className != "default") {
					aObj.className="default"
				}
			}
		}


	</SCRIPT>


</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad()" onunload="call2()">

<form action="preadviceinput.do?value=A" method="post" >
	<input name="method" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value="GBP">
	<input name="selectedTabIndex" type="hidden" value="1">
	<input name="selectedTabName" type="hidden" value="MatchQueueTodayParent">
	<input name="currencyGroup" type="hidden" >
	<input name="searchDate" type="hidden" value="3">


	<input name="selectedCurrencyCode" type="hidden" >
	<input name="selectedEntityId" type="hidden">

	<input name="menuAccessId" type="hidden" >

	<div id="MovementMatch" style="position:absolute; left:20px; top:20px; width:658; height:58px; border:2px outset;" color="#7E97AF">

		<div id="MovementMatch" style="position:absolute; left:8px; top:2px; width:658px; height:400;">
			<table width="525" border="0" cellpadding="0" cellspacing="0" height="30">

				<tr color="black" border="0"  height="24">
					<td width="170"><b><fmt:message key="matchQuality.entityId"/></b></td>
					<td width="28">&nbsp;</td>
					<td width="140">
						<select name="movement.id.entityId" titleKey="tooltip.selectEntityId" onchange="submitFormEntityList('displayInputAuthorise')" style="width:140px" tabindex="1">
							<c:forEach var="entity" items="${requestScope.entities}">
								<option value="${entity.value}" <c:if test="${entity.value == movement.id.entityId}">selected</c:if>>${entity.label}</option>
							</c:forEach>
						</select>

					</td>
					<td width="20">&nbsp;</td>
					<td width="490">
		<span id="entityDesc" class="spantext">
					</td>

				</tr>
				<tr  height="24">
					<td width="100"><b style="width: 110px;"><fmt:message key="currency.group"/></b></td>
					<td width="28">&nbsp;</td>
					<td width="50">
						<select name="movement.currencyCode" tabindex="2" style="width:140px" onchange="submitForm('displayInputAuthorise')" titleKey="tooltip.selectCuurencyGrp">
							<c:forEach var="currency" items="${requestScope.currencyGroupList}">
								<option value="${currency.value}"
									<c:if test="${currency.value == movement.currencyCode}">selected</c:if>>
									${currency.label}
								</option>
							</c:forEach>
						</select>

					</td>
					</td>
					<td width="20">&nbsp;</td>
					<td width="490">
		<span id="currencyDesc" class="spantext">
					</td>
				</tr>
			</table>
		</div>
	</div>




	<div id="ddimagetabs" style="position:absolute; left:20px; top:85px; width:532px; height:20px;">

		<a onmouseout="revertbackMOT('MatchQueueTodayParent',this);" onmouseover="changecontentMOR('MatchQueueTodayParent',this)"  onClick="checkTabIndexToday(this);"><b>${requestScope.dateToDisplay}</b></a>
		<a onmouseout="revertbackMOT('MatchQueueParent',this);" onmouseover="changecontentMOR('MatchQueueParent',this)"  onClick="clickSelectedTab(this);"><b><fmt:message key="qualityTab.all"/></b></a>
	</div>


	<!-- Start code modified by Vivekanandan A for mantis 1991-->
	<div id="Line" style="position:absolute; left:182px; top:103px; width:500px; height:20px;">
		<!-- End code modified by Vivekanandan A for mantis 1991-->
		<table width="100%"><tr>
			<td ><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
		</table>
	</div>

	<div id="tabcontentcontainer" style="position:absolute; left:20px; top:106px; width:658px;height:437px;">
		<div id="MatchQueueTodayParent" color="#7E97AF" style="position:absolute; border:0px outset; left:0px; top:1px; width:660px; height:434px;display:none">
			<div id="MatchQueueToday" style="position:absolute;z-index:99;left:0px; top:0px; width:637px; height:10px;">
				<table  class="sort-table" id="table_1" bgcolor="#B0AFAF" width="640px" border="0" cellspacing="1" cellpadding="0"  height="20">
					<thead>
					<tr height="20px">
						<td  width="70" style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortCurrencyCode"/>' align="center"><b><fmt:message key="currency.id"/></b></td>
						<td  width="280" title='<fmt:message key="tooltip.sortCurrencyName"/>' align="left"><b><fmt:message key="matchQueue.CcyName"/></b></td>
						<td  width="100"align="left"><b><fmt:message key="movementDisplay.source"/></b></td>
						<td  width="90" align="left"><b><fmt:message key="messageFormats.authorizeFlag"/></b></td>
						<td  width="90" align="left"><b><fmt:message key="movementDisplay.reference"/></b></td>
					</tr>
					</thead>
				</table>
			</div>

			<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:657px; height:434px;overflowY:scroll">
				<div id="MatchQueueToday" style="position:absolute;z-index:99;left:0px; top:21px; width:640px; height:10px;">
					<table class="sort-table" id="inputAuthoriseListToday" width="638px" border="0" cellspacing="1" cellpadding="0" height="409" style="table-layout:inherit !important" >
						<tbody><% count = 0; %>
						<c:forEach  items="${requestScope.inputAuthoriseListToday}" var="inputAuthoriseListToday">
							<% if( count%2 == 0 ) {%>
							<tr  class="even"><% }else  { %> <tr class="odd"> <%}++count; %>
							<td width="70px" align="left">

									${inputAuthoriseListToday.currencyId}

							</td>
							<td  width="280px">
									${inputAuthoriseListToday.currencyName}

							</td>
							<td width="100px" align="left">
									${inputAuthoriseListToday.source}

							</td>
							<td width="90px" align="right">
							<a onclick="clickLink(this,'A',event);" href="inputauthorise.do${inputAuthoriseListToday.urlParams}">${inputAuthoriseListToday.authorise}</a>
							</td>
							<td width="90px" align="right" style="width: 85px;">
								<a onclick="clickLink(this,'R',event);" href="inputauthorise.do${inputAuthoriseListToday.urlParams}">${inputAuthoriseListToday.reffered}</a>
							</td>
						</tr>

						</c:forEach>
						</tbody>
						<tfoot>
						<tr>
							<td colspan="6" ></td>
						</tr>
						</tfoot>
					</table>
				</div>
			</div>
		</div>
	</div>

	</DIV>

	<div id="MovementMatch" style="position:absolute; left:600; top:557; width:70; height:15px; visibility:visible;">
		<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>

				<td align="Right">
					<a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Input Authorise Queue '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img style="padding-left: 20px" src="images/help_default.GIF " name="Help"  border="0" ></a>
				</td>

				<td align="right" id="Print">
					<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
				</td>
			</tr>
		</table>
	</div>


	<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:548; width:658; height:38px; visibility:visible;">
		<div id="Currency" style="position:absolute; left:6; top:4; width:658; height:15px; visibility:visible;">

			<table width="140" border="0" cellspacing="0" cellpadding="0" height="10">
				<tr>

					<td id="refreshbutton" width="70" title='<fmt:message key="tooltip.refreshWindow"/>'>
						<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:refreshScreen('displayInputAuthorise')"><fmt:message key="button.Refresh"/></a>
					</td>
					<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>
						<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.close"/></a>
					</td>

				</tr>
			</table>
		</div>
		<table height="30"><tr>
			<td id="lastRefTimeLable" width="466px" align="right" style="padding-top: 6px;">
				<fmt:message key="label.lastRefTime"/>
			</td>
			<td id="lastRefTime" style="padding-top: 6px;" >
				<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">
			</td>
		</tr>
		</table>
		<div style="position:absolute; left:6; top:4; width:658px; height:15px; visibility:hidden;">
			<table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
				<tr>

					<td id="refreshdisablebutton">
						<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
					</td>
				</tr>
			</table>
		</div>

	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>