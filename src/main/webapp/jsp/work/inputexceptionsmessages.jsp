<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html lang="en">
<head>
<title><fmt:message key="inputexceptionsmessages.title.window"/><%="yes".equals(request.getAttribute("fromPCM")) ? " (PCM)":""%></title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<script type="text/javascript">


	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}

	window.onload = function () {
		setParentChildsFocus();
		//Aliveni: Calling setTitleSuffix() function in CommonJS.js file to distinguish multiple
		//Implementations (Mantis 857), on 27-AUG-2010
		setTitleSuffix(document.forms[0]);
	}

	function refreshParent () {
		opener.refresh = true;
	}
</script>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>

<style>
body {
	margin: 0px;
	overflow: hidden
}

#workFlowMonitorcontent {
	border: solid 0px #000;
	width: 100%;
	height: 100%;
	float: left;
	margin: 0px 0px;
}
</style>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<form action="inputexceptions.do" method="post">
	<input name="method" type="hidden" value="">
</form>

<script type="text/javascript">
		 /* Start : Added by Vivekanandan For Matis 1364 : Input Exceptiosn Issue on 22-02-2011 */

		var screenRoute = "MessageDetails";
		var label = new Array ();
		label["text"] = new Array ();
		label["tip"] = new Array ();

		label["text"]["label_message_type"] = "<fmt:message key="inputexceptions.label.message_type"/>"
		label["text"]["label_message_status"] = "<fmt:message key="inputexceptions.label.message_status"/>"
		label["text"]["label-close"] = "<fmt:message key="inputException.close"/>"
		label["tip"]["close_button"] = "<fmt:message key="tooltip.close"/>"
		label["text"]["label-of"] = "<fmt:message key="genericDisplayMonitor.labelOf"/>"
		label["text"]["label-rep"] = "<fmt:message key="inputException.reprocess"/>"
		label["tip"]["rep_button"] = "<fmt:message key="inputexceptions.tooltip.button_rep"/>"
		label["text"]["label-rej"] = "<fmt:message key="inputException.reject"/>"
		label["tip"]["rej_button"] = "<fmt:message key="inputexceptions.tooltip.button_rej"/>"
		label["text"]["label-supp"] = "<fmt:message key="inputException.suppress"/>"
		label["tip"]["supp_button"] = "<fmt:message key="inputexceptions.tooltip.button_supp"/>"
		label["text"]["label-inputExceptionMessage"] = "<fmt:message key="inputexceptionsmessages.title.window"/>"
		label["text"]["label-noMessage"] = "<fmt:message key="alert.interfaceExceptions.noMessage"/>"
		label["text"]["label-multipleMessageSelected"] = "<fmt:message key="label.multipleMessageSelected"/>"
		label["text"]["label-messageID"] = "<fmt:message key="movement.messageId"/>"
		label["text"]["label-noMessageSelected"] = "<fmt:message key="label.noMessageSelected"/>"
		label["text"]["label-updateResponse"] = "<fmt:message key="label.updateResponse"/>"
		label["text"]["label-areyouSure"] = "<fmt:message key="label.areyouSure"/>"
		label["text"]["label-deleteMessages"] = "<fmt:message key="label.deleteMessages"/>"
		label["text"]["label-page"] = "<fmt:message key="genericDisplayMonitor.labelPage"/>";
		label["text"]["label-autoFormat"] = "<fmt:message key="inputexceptions.label.autoFormatXML"/>";
		label["tip"]["label-autoFormat"] = "<fmt:message key="inputexceptions.tooltip.autoFormatXML"/>";
		var fromPCM = '${requestScope.fromPCM}';
		
		var fromDashboard = '${requestScope.fromDashboard}';
		var m = '${requestScope.m}';
		/* End : Added by Vivekanandan For Matis 1364 : Input Exceptiosn Issue on 22-02-2011 */
	</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>
