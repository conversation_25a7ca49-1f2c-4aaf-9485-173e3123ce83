<?xml version="1.0" encoding="UTF-8" ?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<monitoroptions>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<c:set var="templateOptions" value="${requestScope.forecastMonitorForm.forecastAssumption}" scope="page"/>
	<c:set var="templateOptions" value="${requestScope.forecastAssumption}"/>
	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option
						value="${entity.value}"
						selected="${templateOptions.entityId == entity.value ? '1' : '0'}"
				>${entity.label}</option>
			</c:forEach>
		</select>
		<select id="currency">
			<option
					value="${requestScope.currencyCode}"
					selected="1"
			>${requestScope.currencyName}</option>
		</select>
	</selects>
</monitoroptions>