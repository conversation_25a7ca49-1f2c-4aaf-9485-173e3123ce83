<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>





<c:set var="recordCount" value="${requestScope.accountMonitorNew.accountMonitorDetails.size()}" />

<accountmonitor
		databuilding="${requestScope.accountMonitorJobStatusFlag != null ? 'true' : 'false'}"
		refresh="${requestScope.autoRefreshRate}"
		currencythreshold="${requestScope.applyCurrencyThreshold == 'Y' ? 'true' : 'false'}"
		hidezerobalances="${requestScope.hideZeroBalances == 'Y' ? 'true' : 'false'}"
		datefrom="${requestScope.accountMonitorNew.dateAsString}"
		currencyformat="${sessionScope.CDM.currencyFormat}"
		dateformat="${sessionScope.CDM.dateFormatValue}"
		dateComparing="${SwtConstants.YES == requestScope.dateComparing ? 'true' : 'false'}"
		menuentitycurrgrpaccess="${requestScope.menuEntityCurrGrpAccess}"
		tabindex="${requestScope.selectedTabIndex}"
		isautorefresh="${requestScope.isautorefresh == SwtConstants.YES ? 'true' : 'false'}"
		sysDateFrmSession="${requestScope.sysDateFrmSession}"
		lastRefTime="${requestScope.lastRefTime}"
		tabIndexOnload="${requestScope.tabIndexOnload}"
		existingEntityId="${requestScope.existingEntityId}"
		manualSweepAccessId="${requestScope.manualSweepAccessId}"
		currfontsize="${requestScope.fontSize}"
>
	<c:forEach items="${requestScope.weekends}" var="weekend" varStatus="status">
		<c:choose>
			<c:when test="${not empty weekend}">
				weekend${status.count}="${weekend}"
			</c:when>
			<c:otherwise>
				weekend${status.count}=""
			</c:otherwise>
		</c:choose>
	</c:forEach>

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>

	<singletons>
		<sod negative="${requestScope.accountMonitorNew.signFlagForAccumulatedSODBal == 'true' ? 'true' : 'false'}">
				${requestScope.accountMonitorNew.accumulatedSODBalAsString}
		</sod>
		<openunexpected negative="${requestScope.accountMonitorNew.signFlagForOpenUnexpectedBalTotal == 'true' ? 'true' : 'false'}">
				${requestScope.accountMonitorNew.openUnexpectedBalTotalAsString}
		</openunexpected>
		<scenarioAlerting>
				${requestScope.accountMonitorNew.scenarioHighlighted}
		</scenarioAlerting>
	</singletons>

	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order}" var="order">
					<c:choose>
						<c:when test="${order == 'alerting'}">
							<column heading=""
									draggable="false" visible="true" filterable="true" type="str" dataelement="alerting" sort="true"
									width="${requestScope.column_width.alerting}" />
						</c:when>
						<c:when test="${order == 'ccy'}">
							<column heading="<fmt:message key='sweep.currencyCode'/>"
									draggable="false" visible="true" filterable="true" type="str" dataelement="ccy" sort="true"
									width="${requestScope.column_width.ccy}" />
						</c:when>
						<c:when test="${order == 'account'}">
							<column heading="<fmt:message key='accountmonitor.acctId'/>"
									draggable="false" visible="true" filterable="true" type="str" dataelement="account" sort="true"
									width="${requestScope.column_width.account}" />
						</c:when>
						<c:when test="${order == 'name'}">
							<column heading="<fmt:message key='usermaintenance.userName'/>"
									draggable="false" visible="true" filterable="true" type="str" dataelement="name" sort="true"
									width="${requestScope.column_width.name}" />
						</c:when>
						<c:when test="${order == 'predicted'}">
							<column heading="<fmt:message key='accountmonitorNew.Predicted'/>"
									draggable="true" visible="true" filterable="false" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" dataelement="predicted"
									width="${requestScope.column_width.predicted}" />
						</c:when>
						<c:when test="${order == 'unsettled'}">
							<column heading="<fmt:message key='accountmonitorNew.Unsettled'/>"
									draggable="true" visible="true" filterable="false" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_UNSETTLED%>"
									type="num" dataelement="unsettled"
									width="${requestScope.column_width.unsettled}" />
						</c:when>
						<c:when test="${order == 'unexpected'}">
							<column heading="<fmt:message key='accountmonitorNew.Unexpected'/>"
									draggable="true" visible="true" filterable="false" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_UNEXPECTED%>"
									type="num" dataelement="unexpected"
									width="${requestScope.column_width.unexpected}" />
						</c:when>
						<c:when test="${order == 'lorocurr'}">
							<column heading="<fmt:message key='accountmonitorNew.Loro'/>"
									draggable="true" visible="true" filterable="false" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_LORO%>" type="num"
									dataelement="lorocurr"
									width="${requestScope.column_width.lorocurr}" />
						</c:when>
						<c:when test="${order == 'external'}">
							<column heading="<fmt:message key='accountmonitorNew.External'/>"
									draggable="true" visible="true" filterable="false" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_EXTERNAL%>"
									type="num" dataelement="external"
									width="${requestScope.column_width.external}" />
						</c:when>
						<c:when test="${order == 'sum'}">
							<column heading="<fmt:message key='accountmonitorNew.Sum'/>"
									draggable="true" visible="true" filterable="true" type="str" dataelement="sum" sort="true"
									width="${requestScope.column_width.sum}" />
						</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${recordCount}">
			<c:forEach items="${requestScope.accountMonitorNew.accountMonitorDetails}" var="record">
				<row
						accountclass="${record.accountClass}"
						lorotopredictedflag="${record.loroToPredictedFlag}"
						colorflag="${record.colorFlag}">
					<alerting clickable="false">${record.scenarioHighlighted}</alerting>
					<ccy clickable="false">${record.currencyCode}</ccy>
					<account clickable="false">${record.accountId}</account>
					<name clickable="false">${record.accountName}</name>
					<predicted
							linked="${record.iconIndicatorFlag ? 'true' : 'false'}"
							negative="${record.signFlagForPredictedBal ? 'true' : 'false'}"
							clickable="true">${record.predictedBalAsString}</predicted>
					<unsettled
							negative="${record.signFlagForUnsettledBal ? 'true' : 'false'}"
							clickable="true">${record.unsettledBalAsString}</unsettled>
					<unexpected
							negative="${record.signFlagForUnexpectedBal ? 'true' : 'false'}"
							clickable="true">${record.unexpectedBalAsString}</unexpected>
					<lorocurr
							negative="${record.signFlagForLoroBal ? 'true' : 'false'}"
							clickable="true">${record.loroBalAsString}</lorocurr>
					<external
							negative="${record.signFlagForExternalBal ? 'true' : 'false'}"
							clickable="true">${record.externalBalAsString}</external>

					<c:choose>
						<c:when test="${empty record.summable}">
							<sum clickable="false">${record.sum}</sum>
						</c:when>
						<c:otherwise>
							<sum clickable="false" summable="${record.summable}">${record.sum}</sum>
						</c:otherwise>
					</c:choose>

					<accountStatus clickable="false">${record.accountStatus}</accountStatus>
					<includeLoroInPredictedIndicator clickable="false">${record.includeLoroInPredictedIndicator}</includeLoroInPredictedIndicator>
					<includePredictedInLoroIndicator clickable="false">${record.includePredictedInLoroIndicator}</includePredictedInLoroIndicator>

					<includePredictedInLoroColor clickable="false">${record.includePredictedInLoroColor}</includePredictedInLoroColor>
					<includeLoroInPredictedColor clickable="false">${record.includeLoroInPredictedColor}</includeLoroInPredictedColor>
				</row>
			</c:forEach>
		</rows>

		<totals>
			<c:forEach items="${requestScope.accountMonitorNew.totalDetails}" var="totalRecord">
				<total>
					<name draggable="false"><fmt:message key="accountmonitorNew.Total" /></name>
					<predicted
							negative="${requestScope.accountMonitorNew.signFlagForPredBalTotal ? 'true' : 'false'}"
							draggable="false">${totalRecord.predictedBalTotalAsString}</predicted>
					<unsettled
							negative="${requestScope.accountMonitorNew.signFlagForUnsettledBalTotal ? 'true' : 'false'}"
							draggable="false">${totalRecord.unsettledBalTotalAsString}</unsettled>
					<unexpected
							negative="${requestScope.accountMonitorNew.signFlagForUnexpectedBalTotal ? 'true' : 'false'}"
							draggable="false">${totalRecord.unexpectedBalTotalAsString}</unexpected>
					<lorocurr
							negative="${requestScope.accountMonitorNew.signFlagForLoroBalTotal ? 'true' : 'false'}"
							draggable="false">${totalRecord.loroBalTotalAsString}</lorocurr>
					<external
							negative="${requestScope.accountMonitorNew.signFlagForExternalBalTotal ? 'true' : 'false'}"
							draggable="false">${totalRecord.externalBalTotalAsString}</external>
					<sum draggable="false">-</sum>
				</total>
			</c:forEach>
		</totals>
	</grid>

	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<c:choose>
					<c:when test="${requestScope.accountMonitorNew.entityId == entity.value}">
						<option value="${entity.value}" selected="1">${entity.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${entity.value}" selected="0">${entity.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="currency">
			<c:forEach items="${requestScope.currencies}" var="currency">
				<c:choose>
					<c:when test="${requestScope.accountMonitorNew.currencyCode == currency.value}">
						<option value="${currency.value}" selected="1">${currency.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${currency.value}" selected="0">${currency.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="accounttype">
			<c:forEach items="${requestScope.accountTypeList}" var="accounttype">
				<c:choose>
					<c:when test="${requestScope.accountMonitorNew.accountType == accounttype.value}">
						<option value="${accounttype.value}" selected="1">${accounttype.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${accounttype.value}" selected="0">${accounttype.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="accountclass">
			<c:forEach items="${requestScope.accountClassList}" var="accountclass">
				<c:choose>
					<c:when test="${requestScope.accountMonitorNew.accountClass == accountclass.value}">
						<option value="${accountclass.value}" selected="1">${accountclass.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${accountclass.value}" selected="0">${accountclass.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>
	</selects>

	<tabs>
		<c:forEach items="${requestScope.tabDetails}" var="tab">
			<predictdate
					businessday="${tab.businessDay}"
					dateLabel="${tab.tabDateLabel}">
					${tab.tabDateAsString}
			</predictdate>
		</c:forEach>
	</tabs>
</accountmonitor>