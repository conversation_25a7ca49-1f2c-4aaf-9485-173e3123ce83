<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>



<c:set var="recordCount" value="${requestScope.personalCurrencyList.entityRecords.size()}"/>
<c:set var="recordCountCurr" value="${requestScope.personalCurrencyList.personalCurrencyRecords.size()}"/>

<personalcurrency>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key="label.personalCurrencyList.currency"/>"
						draggable="false"
						filterable="false"
						type="str"
						dataelement="ccy"
						width="80"
						sort="true"
				/>
				<column
						heading="<fmt:message key="label.personalCurrencyList.ccyName"/>"
						draggable="false"
						filterable="false"
						type="str"
						dataelement="name"
						width="200"
				/>
				<column
						heading="<fmt:message key="label.personalCurrencyList.priorityOrder"/>"
						draggable="false"
						allowSplit="true"
						filterable="false"
						type="num"
						dataelement="order"
						width="80"
						editable="true"
						maxChars = "2"
				/>

				<c:if test="${recordCount > 0}">
					<group heading="<fmt:message key="label.personalCurrencyList.entity"/>" collapsable="false">
						<c:forEach items="${requestScope.personalCurrencyList.entityRecords}" var="group" varStatus="status">
							<column
									heading="${group.entityId}"
									draggable="false"
									filterable="false"
									type="bool"
									dataelement="entity${status.count}"
									width="125"
									clickable="true"
							/>
						</c:forEach>
					</group>
				</c:if>
				<column width="1"></column>
			</columns>
		</metadata>

		<rows size="${recordCountCurr}">
			<c:forEach items="${requestScope.personalCurrencyList.personalCurrencyRecords}" var="currencyRecord">
			<row>
				<ccy clickable="false">${currencyRecord.id.currencyCode}</ccy>
				<c:forEach items="${currencyRecord.personalCurrencyRecords}" var="personalCurrency" varStatus="status">
				<c:if test="${status.first}">
					<name clickable="false">${fn:escapeXml(personalCurrency.currencyName)}</name>
					<order clickable="false">${personalCurrency.priorityOrder}</order>
				</c:if>
				<c:set var="entityIndex" value="${status.count}"/>
				<c:set var="holidayFlag" value="${personalCurrency.accessFlag != 'N' ? 'false' : 'true'}"/>
				<c:set var="entityFlag" value="${personalCurrency.entityFlag != 'N' ? 'true' : 'false'}"/>

				<c:if test="${!status.first}">
				<c:set var="entityColumn" value="entity${entityIndex}"/>
				<${entityColumn}
						id="${personalCurrency.entityId}"
				holiday="${holidayFlag}"
				>${entityFlag}</${entityColumn}>
			</c:if>
			</c:forEach>
		</row>
		</c:forEach>
	</rows>

	<totals>
		<total>
			<order draggable="false"><fmt:message key="label.personalCurrencyList.applyToAllCurrency"/></order>
			<c:forEach items="${requestScope.personalCurrencyList.entityRecords}" var="group" varStatus="status">
			<c:set var="entityColumn" value="entity${status.count}"/>
			<${entityColumn} id="${group.entityId}">
				${group.entityAllFlag != 'N' ? 'true' : 'false'}
		</${entityColumn}>
		</c:forEach>
	</total>
</totals>
</grid>
</personalcurrency>