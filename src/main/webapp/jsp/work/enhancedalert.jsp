<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "EnhancedAlertJsp";
var tooltipEntityId = '${requestScope.tooltipEntityId}';
var tooltipCurrencyCode = '${requestScope.tooltipCurrencyCode}';
var tooltipFacilityId = '${requestScope.tooltipFacilityId}';
var tooltipSelectedDate = '${requestScope.tooltipSelectedDate}';
var tooltipSelectedAccount = '${requestScope.tooltipSelectedAccount}';
var tooltipMvtId = '${requestScope.tooltipMvtId}';
var tooltipOtherParams = '${requestScope.tooltipOtherParams}';

var fromJSP = true;


 </script>
 <%@ include file="/angularscripts.jsp"%>
</body>
</html>