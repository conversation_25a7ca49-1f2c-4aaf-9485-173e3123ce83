<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<c:if test="${'N'==requestScope.sweepQueue.queueName}">

	<title><fmt:message key="sweepSubmitQueue.title.window"/></title>

</c:if>
<c:if test="${'U'==requestScope.sweepQueue.queueName}">

	<title><fmt:message key="sweepAuthQueue.title.window"/></title>

</c:if>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<SCRIPT language="JAVASCRIPT">
var menuAccessId = "${requestScope.menuAccessId}";
var queueName = '${requestScope.sweepQueue.queueName}';
var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/' + appName + '/');
requestURL = requestURL.substring(0, idy + 1);

function openSweepQueueDetail(selectedSwpId, entityId, queueName)
{
	openWindow('sweepdetail.do?method=displayQueue&swpid='+selectedSwpId+'&entid='+entityId+'&qname='+queueName,'SweepDtlWindow','width=1021,height=635,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
}

function openSearch(methodName, entityId, acctType, currency, queueName, parentScreen){
    // variable declared the menu access
    var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Sweep Search");
	// variable declared the Menu Name
	var menuName = new String('<fmt:message key="sweepSearch.title.window"/>');
	//variable declared the smart predict
    var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
    menuName = menuName.substr(0,smrtPredPos-3);
	if (menuAccessIdOfChildWindow == 2) {
		alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');

	} else {
	    var param='sweepsearch.do?method='+methodName+'&entityCode='+entityId;
	    param +='&actType='+acctType;
	    param +='&status='+queueName;
	    param +='&currency='+currency;
	    param +='&menuAccessId='+menuAccessIdOfChildWindow;
        param+='&parentScreen='+parentScreen;

	          /*Start:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width && height */
        openWindow(param,'SweepSearchWindow','width=911,height=488,toolbar=0, status=yes,resizable=yes, scrollbars=yes','true');
                  /*End:Code Modified for Mantis 1577 on 19-01-2011:The purpose Alignment width && height */
	}
}

function accountAccessConfirm(accountId, entity) {
    var oXMLHTTP = new XMLHttpRequest();
    var sURL = requestURL + appName + "/accountAccess.do?method=acctAccessConfirm";
    sURL = sURL + "&accountId=" + accountId;
    sURL = sURL + "&entityId=" + entity;
    sURL = sURL + "&status=Sweeping";
    oXMLHTTP.open("POST", sURL, false);
    oXMLHTTP.send();
    var str = oXMLHTTP.responseText;
    return str;
}


function getCurrencyAccess(entityId, ccyCode)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName +"/sweepqueue.do?method=setCurrencyAccess"
	sURL= sURL+"&selectedCurrency="+ccyCode;
	sURL=sURL+"&entityId="+entityId;
	oXMLHTTP.open( "POST", sURL, false );
	//sending request
	oXMLHTTP.send();
	//getting response
	currEntityAccess=oXMLHTTP.responseText;
	return currEntityAccess;
}

function getAccess(entityId, entityIdCr, entityIdDr, ccyCode, accountIdCr, accountIdDr)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName +"/sweepqueue.do?method=setSweepAccess"
	sURL= sURL+"&currencyCode="+ccyCode;
	sURL = sURL + "&entityId=" + entityId;
	sURL=sURL+"&entityIdCr="+entityIdCr;
	sURL=sURL+"&entityIdDr="+entityIdDr;
	sURL=sURL+"&accountId="+accountIdCr;
	sURL=sURL+"&accountId="+accountIdDr;
	oXMLHTTP.open( "POST", sURL, false );
	//sending request
	oXMLHTTP.send();
	//getting response
	sweepAccess=oXMLHTTP.responseText;
	return sweepAccess;
}

</script>
</head>

<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "sweepQueue";


 </script>
<%@ include file="/angularscripts.jsp"%>
</body>

</html>