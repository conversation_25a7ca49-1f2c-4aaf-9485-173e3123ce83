<?xml version="1.0" encoding="UTF-8"?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for Workflow Monitor screen.
-->

<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="java.util.*" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<workflowmonitor
        databuilding="${requestScope.workbean.jobFlagStatus ? 'true' : 'false'}"
        refresh="${requestScope.autoRefreshRate}"
        currencythreshold="${requestScope.applyCurrencyThreshold == 'Y' ? 'true' : 'false'}"
        servertime="${requestScope.serverTime.timeISO8601}"
        servertimezone="${requestScope.serverTime.timeZoneOffset}"
        lastRefTime="${requestScope.lastRefTime}"
        entityoffset="${requestScope.entityOffset}"
        isClosed="${requestScope.isClosed}"
        existingEntityId="${requestScope.existingEntityId}"
        dateFormat="${requestScope.dateFormat}"
        currencyFormat="${requestScope.currencyFormat}"
        isThresholdToBeApplied="${requestScope.isThresholdToBeApplied}"
        displayedDate="${requestScope.displayedDate}"
>
    <request_reply>
        <status_ok>${requestScope.reply_status_ok}</status_ok>
        <message>${requestScope.reply_message}</message>
        <location />
    </request_reply>

    <timing>
        <c:forEach items="${requestScope.opTimes}" var="opTime">
            <operation id="${opTime.key}">${opTime.value}</operation>
        </c:forEach>
    </timing>

    <systemsummary>
        <system name="Logged on Users" id="loggedonusers">${requestScope.workbean.loggedOnUsers}</system>
    </systemsummary>

    <positions includedtotal="${requestScope.workbean.totalIncluded}" excludedtotal="${requestScope.workbean.totalExcluded}">
        <c:if test="${not empty requestScope.workbean.position}">
            <c:forEach items="${requestScope.workbean.position}" var="position" varStatus="status">
                <position num="${status.index + 1}" level="${position.position}"
                          included="${position.included == 0 ? '0' : position.included}"
                          excluded="${position.excluded == 0 ? '0' : position.excluded}" />
            </c:forEach>
        </c:if>
    </positions>

    <selects>
        <select id="entity">
            <c:forEach items="${requestScope.entitys}" var="workflowentity">
                <option value="${workflowentity.value}" ${requestScope.workbean.selectedEntity == workflowentity.value ? 'selected="1"' : 'selected="0"'}>
                        ${workflowentity.label}
                </option>
            </c:forEach>
        </select>

        <select id="currency">
            <c:forEach items="${requestScope.crrgrp}" var="workflowcrrgrp">
                <option value="${workflowcrrgrp.value}" ${requestScope.workbean.selectedCurr == workflowcrrgrp.value ? 'selected="1"' : 'selected="0"'}>
                        ${workflowcrrgrp.label}
                </option>
            </c:forEach>
        </select>
    </selects>

    <tabs>
        <c:forEach items="${requestScope.tabDetails}" var="tab">
            <predictdate businessday="${tab.businessDay}">
                    ${tab.tabDateAsString}
            </predictdate>
        </c:forEach>
        <selectedIndex tabindex="${requestScope.selectedTabIndex}" index="${requestScope.selectedIndex}">
        </selectedIndex>
    </tabs>

    <tabsCategory>
        <c:forEach items="${requestScope.tabCategoryDetails}" var="tab">
            <displaytab count="${tab.count}" tabName="${tab.tabName}" tabId="${tab.tabId}">
                    ${tab.tabNameAsString}
            </displaytab>
        </c:forEach>
        <selectedIndex tabindex="${requestScope.selectedTabIndexCategory}" index="${requestScope.selectedIndexCategory}">
        </selectedIndex>
    </tabsCategory>
</workflowmonitor>
