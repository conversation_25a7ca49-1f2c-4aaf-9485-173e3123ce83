<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><fmt:message key="workflowmonitor.title.window"/></title>
	</head>
	<body>
		<style>
			body { margin: 0px; overflow:hidden }
		</style>

		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		var screenRoute = "workFlowMonitor";
			window.onload = function () {

				setParentChildsFocus();
				setTitleSuffix(document.forms[0]);
				// var so = new SWFObject ("jsp/work/workflowmonitor.swf?version=<%= SwtUtil.appVersion %>", "accountmonitor", "100%", "100%", "9", "#D6E3FE");
				// so.write("swf");
			};
			isDoNotCloseMyChilds = true;
			window.onunload = call;

			var calledFromParent = <%=(""+request.getAttribute("callFromParent")).equalsIgnoreCase("Y")?"true":"false"%>;

			/* var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["entity"] = "<fmt:message key="workflowmonitor.entity.title"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";

			label["text"]["currency"] = "<fmt:message key="workflowmonitor.curGrp.title"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.selectCurrencyCode"/>";

			label["text"]["tab1"] = "<fmt:message key="accountmonitor.today"/>";

			label["text"]["tab2"] = "<fmt:message key="accountmonitor.today1"/>";

			label["text"]["tab3"] = "<fmt:message key="accountmonitor.today2"/>";
			label["text"]["threshold"] = "<fmt:message key="mvmt.applyCurrencyThreshold"/>";
			label["tip"]["threshold"] = "<fmt:message key="tooltip.applyCurrencyThreshold"/>";


			label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

			label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

			// Movement Titles
			label["text"]["movement"] = "<fmt:message key="workflowmonitor.movMent.title"/>";
			label["text"]["movement-unsettled"] = "<fmt:message key="workflowmonitor.unsYestday.title"/>";
			label["text"]["movement-backval"] = "<fmt:message key="workflowmonitor.bkVal.title"/>";

			// Input Titles
			label["text"]["input"] = "<fmt:message key="workflowmonitor.input.title"/>";
			label["text"]["input-exception"] = "<fmt:message key="workflowmonitor.excep.title"/>";
			label["text"]["input-auth"] = "<fmt:message key="workflowmonitor.auth.title"/>";
			label["text"]["input-referred"] = "<fmt:message key="workflowmonitor.reff.title"/>";

			// Matches Titles
			label["text"]["matches"] = "<fmt:message key="workflowmonitor.matches.title"/>";
			label["text"]["matches-offered"] = "<fmt:message key="workflowmonitor.offered.title"/>";
			label["text"]["matches-suspended"] = "<fmt:message key="workflowmonitor.suspended.title"/>";
			label["text"]["matches-confirmed"] = "<fmt:message key="workflowmonitor.confirmed.title"/>";

			// Sweeps Titles
			label["text"]["sweep"] = "<fmt:message key="workflowmonitor.sweeps.title"/>";
			label["text"]["sweep-submit"] = "<fmt:message key="workflowmonitor.submit.title"/>";
			label["text"]["sweep-authorise"] = "<fmt:message key="workflowmonitor.authorise.title"/>";
			label["text"]["sweep-exception"] = "<fmt:message key="workflowmonitor.submit.Exception"/>";

			//  Included Movements Titles
			label["text"]["incmovements"] = "<fmt:message key="workflowmonitor.incMovmnts.title"/>";
			label["text"]["totalmovements"] = "<fmt:message key="workflowmonitor.totalMov.title"/>";

			//  Excluded Outstandings Titles
			label["text"]["execout"] = "<fmt:message key="workflowmonitor.exclOut.title"/>";

			// System Titles
			label["text"]["system"] = "<fmt:message key="workflowmonitor.system.title"/>";
			label["text"]["system-logon"] = "<fmt:message key="workflowmonitor.logon.title"/>";
			label["text"]["system-error"] = "<fmt:message key="workflowmonitor.errors.title"/>";


			label["text"]["excluded"] = "<fmt:message key="workflowmonitor.excluded"/>";
			label["text"]["outstandings"] = "<fmt:message key="workflowmonitor.outstandings"/>";
			label["text"]["included"] = "<fmt:message key="workflowmonitor.included"/>";
			label["text"]["movements"] = "<fmt:message key="workflowmonitor.movements"/>";
			label["text"]["total"] = "<fmt:message key="workflowmonitor.total"/>";
			label["text"]["label-selectedScenLastRan"] = "<fmt:message key="scenarioSummary.selectedScenLastRan"/>";
			label["text"]["label-selectedScen"] = "<fmt:message key="scenarioSummary.selectedScen"/>";
			label["text"]["label-scenTotals"] = "<fmt:message key="scenarioSummary.scenTotals"/>";
			label["tip"]["label-scenTotals"] = "<fmt:message key="tooltip.scenTotals"/>";
			label["tip"]["label-selectedScen"] = "<fmt:message key="tooltip.selectedScen"/>"

			label["text"]["alert.currencyAccess"] = "<fmt:message key="alert.currencyAccess"/>";

			label["text"]["label-workflowShowXML"] = "<fmt:message key="workflowmonitor.context.xml"/>";
			label["text"]["label-workflowXML"] = "<fmt:message key="workflowmonitor.title.xml"/>";
			label["text"]["label-summaryShowXML"] = "<fmt:message key="scenarioSummary.context.xml"/>";
			label["text"]["label-summaryXML"] = "<fmt:message key="scenarioSummary.title.xml"/>";
			label["text"]["label-changeColors"] = "<fmt:message key="colors.context.xml"/>";
			label["text"]["label-lastRefTime"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["label-workflowMonitor"] = "<fmt:message key="label.workflowMonitor"/>";
			label["text"]["label-noData"] = "<fmt:message key="alert.interfaceMonitor.noData"/>";
			label["text"]["label-sumNoDataTitle"] = "<fmt:message key="alert.Summary.noData.title"/>";
			label["text"]["label-refreshRateSelectedMonimum"] = "<fmt:message key="alert.refreshRateSelectedMonimum"/>";
			label["text"]["label-workflowXML"] = "<fmt:message key="label.workflowXML"/>";
			label["text"]["label-exportPDF"] = "<fmt:message key="label.exportPDF"/>";
			label["text"]["label-taking"] = "<fmt:message key="label.taking"/>";
			label["text"]["label-seconds"] = "<fmt:message key="label.seconds"/>";*/

			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';
			var roleId = '${requestScope.roleId}';
			var systemDate = '${requestScope.sysDateAsString}';


			var refreshPending = false;

			var appName = "<%=SwtUtil.appName%>";
			var testDate= "<%=SwtUtil.getSystemDateString() %>";
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			var currencyFormat = '${sessionScope.CDM.dateFormatValue}';

			//flash uses this to determine what to request to save a new refresh rate
			function getUpdateRefreshRequest (rate) {

				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + rate;
			}

/*Start:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' && 'Change', where the buttons ought to be disabled*/
/**
*This method is used to find the Menuaccess Id for Input athorise queue && Sweep Authorise queue using their Menu itemId
*@return menuAccessIdOfChildWindow
**/
	function getMenuAccessIdByItemId(a) {
	//get the Menu accesss ID
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindowByItemId(a);
		var fwdSlashPos = new String(a).search("/");
		var gifPos = new String(a).search("gif");
		//to get the menu name
		var menuName = new String(a).substr(fwdSlashPos+1,gifPos-fwdSlashPos-2);
		//to get the position of the Menu name
		var QPos = new String(menuName).search("Queue");
		var SPos = new String(menuName).search("Status");
		var EPos = new String(menuName).search("Exception");
		if (QPos != -1) {
		    menuName = new String(menuName).substr(0,QPos)+" "+new String(menuName).substr(QPos);
		} else if (SPos != -1) {
		    menuName = new String(menuName).substr(0,SPos)+" "+new String(menuName).substr(SPos);
		} else if (EPos != -1) {
		    menuName = new String(menuName).substr(0,EPos)+" "+new String(menuName).substr(EPos);
		}
		//condition for checking the MenuAccess is available || not
		if (menuAccessIdOfChildWindow == 2) {
			alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		}

		return menuAccessIdOfChildWindow;
    }
	/*End:Code Modified by Chinniah on 15-Sep-2011 for Mantis 1483: Grant the role only view access to this menu option, I find that I am still able to 'Auth' && 'Change', where the buttons ought to be disabled*/
	function getMenuAccessId(a) {
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow(a);
		var fwdSlashPos = new String(a).search("/");
		var gifPos = new String(a).search("gif");
		var menuName = new String(a).substr(fwdSlashPos+1,gifPos-fwdSlashPos-2);
		var QPos = new String(menuName).search("Queue");
		var SPos = new String(menuName).search("Status");
		var EPos = new String(menuName).search("Exception");
		if (QPos != -1) {
		    menuName = new String(menuName).substr(0,QPos)+" "+new String(menuName).substr(QPos);
		} else if (SPos != -1) {
		    menuName = new String(menuName).substr(0,SPos)+" "+new String(menuName).substr(SPos);
		} else if (EPos != -1) {
		    menuName = new String(menuName).substr(0,EPos)+" "+new String(menuName).substr(EPos);
		}
		if (menuAccessIdOfChildWindow == 2) {
			alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		}

		return menuAccessIdOfChildWindow;
    }


function sendRequest(sURL)
{
	var oXMLHTTP = new XMLHttpRequest();

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var count=new String(oXMLHTTP.responseText);

	return count;
}


		function openJavaWindow(a, left,top,width,height) {

        openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
    }

		/**
		 * Close the window
		 **/
    function closeWindow(a) {
		window.close();
    }

	function openRefreshRateWindow(methodName){
		var param = 'screenOption.do?method='+methodName;
		param +='&parentRefreshMethod=unspecified';
		param +='&screenId='+<%=SwtConstants.WORKFLOW_MONITOR_ID%>;
		openWindow(param,'rolemaintenancechangeWindow','left=50,top=190,width=360,height=150,toolbar=0, resizable=yes, scrollbars=yes,status=yes');

		return param;
	}

	function help(){

		openWindow(buildPrintURL('print','Workflow Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
         }

	function openHelpWindow(methodName){

		openWindow(buildPrintURL('print','Workflow Monitor '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true');
	}


	function submitForm(method, flag) {

	document.forms[0].submit();
	}

		/**
		 * Added for mantis 1443
		 * Open the facility screen. It depends on scenario && the related properties of the selected count
		 *
		 **/
		function openFacility(scenarioTitle, useGeneric, facilityId,facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold,count,selectedCurrencyGroup){

			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1);
			// If the use geneic is 'Y' then we have to display the generic display else a facility screen will be opened
			if (useGeneric == 'Y'){
				// Get the base query of the scenario id from data base through AJAX
					requestURL = requestURL + appName+"/scenMaintenance.do?method=getScenPropertiesForGeneric";
				requestURL = requestURL + "&scenarioId=" + scenarioId;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
					var scenProperties = new String(oXMLHTTP.responseText);
					var baseQuery = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[0];
					var entityColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[1];
					var ccyColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[2];
					var refColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[3];
					var facilityrefColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[4];
					var refParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[5];
					var facilityRefParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[6];
				// Encode the base query do not have problems with special characters
					baseQuery = getMenuWindow().encode64(baseQuery);

					// We have to filter the data in the opened window, generic display screen
					var filter = "";
					if (entityId.toUpperCase() == 'ALL')
						filter == 'All' + '|';
					else
						filter = entityColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + entityId +"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str|';

					if (currencyId.toUpperCase() == 'ALL')
						filter += 'All';
					else
						filter += ccyColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + currencyId+"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str';

					// Encode the defined filter value as it may contains double quote (")
					filter = getMenuWindow().encode64(filter);
				  	var param = 'genericdisplay.do?method=genericDisplay';
					param+='&scenarioID='+scenarioId;
					param+='&fromSummaryScreen='+"true";
					param+='&scenarioTitle='+scenarioTitle;
					param+='&refColumns='+refColumns;
					param+='&facilityRefColumns='+ facilityrefColumns;
					param+='&refParams='+ refParams;
					param+='&facilityRefParams='+ facilityRefParams;
					param+='&facilityID='+facilityId;
					param+='&facilityName='+facilityName;
					param+='&basequery='+baseQuery;
					param+= '&filter=' + filter;
					param+= '&selectedCurrencyGroup=' + selectedCurrencyGroup;
					param+='&applyCurrencyThreshold='+ applyCurrencyThreshold;


				// Open the generic screen
				openWindow(param,'genericdisplay','left=50,top=190,width=1230,height=480,toolbar=0, resizable=yes, scrollbars=yes','true');
			}else{
				// Open the movement summary screen if the facility id is 'MSD'
				if (facilityId == "MSD"){
					var sysdate = "<%=SwtUtil.getSystemDateString()%>";
					var url="outstandingmovement.do?";
					url += "method=flex";
					// Define the initial input screen as X for scenario summary
					url += "&initialinputscreen=X";
					url += "&totalFlag=Y";
					// the position level is 9, the hightest level.
					url += "&posLvlId=9";
					url += "&currencyCode="+currencyId;
					url += "&entityId=" + entityId;
					url += "&currGrp="+selectedCurrencyGroup;
					url += "&date=" + sysdate;
					url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					url += "&workflow=";
					url += "&scenarioId=" + scenarioId;
					openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}// Open the movement match summary display screen if the facility id is 'MATCH_DISPLAY_MANY'
				else if (facilityId == "MATCH_DISPLAY_MANY"){
					var sysdate = "<%=SwtUtil.getSystemDateString()%>";
					var url="movementmatchdisplay.do?";
					url += "&status=M";
					url += "&quality=D";
					url += "&matchCount="+count;
					url += "&currencyCode="+currencyId;
					url += "&entityId=" + entityId;
					url += "&date=";
					url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					url += "&day=NotAll";
					url += "&currGrp="+selectedCurrencyGroup;
					url += "&scenarioId=" + scenarioId;
					url += "&dateTabIndFlag=N";
					url += "&dateTabInd=0";
					openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}else {
						 appName = "<%=SwtUtil.appName%>";
						 requestURL = new String('<%=request.getRequestURL()%>');
						 idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1);
					requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
					requestURL = requestURL + "&facilityId=" + facilityId;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open( "POST", requestURL, false );
					oXMLHTTP.send();
					var screenDetails=new String(oXMLHTTP.responseText);
					if(screenDetails == ""){
						return;
					}
					var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
					programName = screenDetailsList[0];
					var actionPathStr = programName.replace('?', '.');
					var menuAccessIdChild = getMenuAccessIdOfChildWindow(actionPathStr);
					if (programName.indexOf("?") == -1)
						programName += '?';
					width = screenDetailsList[1];
					height = screenDetailsList[2];
						appName = "<%=SwtUtil.appName%>";
						requestURL = new String('<%=request.getRequestURL()%>');
						idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1);
						requestURL = requestURL + appName+"/scenMaintenance.do?method=getAdditionalInfo";
						requestURL = requestURL + "&scenarioId=" + scenarioId;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var additionalParams=new String(oXMLHTTP.responseText);
					var key = "&entityId="+ entityId +"&selectedEntityId=" + entityId;
					key += "&currencyId=" + currencyId;
						key += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
					key += "&calledFrom=generic";
					key += "&menuAccessId="+menuAccessIdChild;
						key +=additionalParams;
					// Open the facility screen
					openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}
			}
		}

		/**
		 * method to open the facility screen related to the facility id of a selected scenario
		 **/
		function goTo(facilityID, hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){

			if (hostID == "" || entityID == "") {
				alert("<fmt:message key="alert.FacilityMissingValues"/>");
			} else {
				if (!facilityAccess(hostID, entityID, facilityID, currencyCodeKey))
					alert("<fmt:message key="alert.accessToFacility"/>");
				else{
					var appName = "<%=SwtUtil.appName%>";
					var requestURL = new String('<%=request.getRequestURL()%>');
					var idy = requestURL.indexOf('/'+appName+'/');
					requestURL=requestURL.substring(0,idy+1) ;
					requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
					requestURL = requestURL + "&facilityId=" + facilityID;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open( "POST", requestURL, false );
					oXMLHTTP.send();
					var screenDetails=new String(oXMLHTTP.responseText);
					if(screenDetails == ""){
						return;
					}
					var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
					var programName = screenDetailsList[0];
					if (programName.indexOf("?") == -1)
						programName += '?';
					var width = screenDetailsList[1];
					var height = screenDetailsList[2];

					var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
					key += "&matchId=" + matchIdKey;
					key += "&calledFrom=generic";
					key += "&currencyId=" + currencyCodeKey;
					key += "&selectedMovementId=" + movementIdKey;
					key += "&selectedSweepId=" + sweepIdKey;
					key +=getMenuWindow().decode64(additionalParams);
					openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
				}
			}
		}

		/**
		 * Return true || false, if the user has access to a facility screen || not
		 *
		 * @param hostId
		 * @param entityId
		 * @param facilityId
		 * @param currencyCode
		 **/
		function facilityAccess(hostId, entityId, facilityId, currencyCode){


			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;
			requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
			requestURL = requestURL + "&hostId=" + hostId;
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&facilityId=" + facilityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			oXMLHTTP.send();
			var access=new String(oXMLHTTP.responseText);
			return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
		}

		function openInstDetails(methodName, params){
			var param = '/' + appName + '/scenarioSummary.do?method='+methodName;
			param += '&allParams=' + params;
			var 	mainWindow = openWindow (param, 'alertInstanceDisplay','left=10,top=230,width=900,height=920,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
			return false;
		}
		</script>
        <%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="workflowmonitor.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>