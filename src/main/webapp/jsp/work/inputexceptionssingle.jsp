<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html lang="en">
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<title>Movement Message</title>
</head>
<style>
body { margin: 0px; overflow:hidden }
#workFlowMonitorcontent {
		border: solid 0px #000;
		width: 100%;
		height: 100%;
		float: left;
		margin: 0px 0px;
	}
</style>
<script type="text/javascript">
var screenRoute = "plainMessage";
	function openJavaWindow(a, left,top,width,height) {
		openWindow(a,'rolemaintenancechangeWindow','left='+left+',top='+top+',width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}
	 
	function refreshParent () {
		opener.refresh = true;	 
	}
</script>
<%@ include file="/angularscripts.jsp"%>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onload="setParentChildsFocus();setTitleSuffix(document.forms[0]);">
<html:form action="inputexceptions.do">
	<input name="method" type="hidden" value="">
</html:form>
</body>
</html>
