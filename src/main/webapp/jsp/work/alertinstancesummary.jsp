<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html lang="en">
<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title><fmt:message key="alertinstancesummary.title.window" />
			<c:if test="${'Y' != requestScope.popupScreen}">(Pop-up)</c:if></title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
	<script type="text/javascript">
		
			window.onload = function () {
				var encode = '${basequery}';
				document.forms[0].basequery.value =getMenuWindow().decode64(encode);
				setTitleSuffix(document.forms[0]); 
				isDoNotCloseMyChilds = true;
				setParentChildsFocus();
			}
		</script>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
			var scenarioID = '${scenarioID}';
			var scenarioTitle = '${scenarioTitle}';
			var callerMethod = '${callerMethod}';
			var encode = '${basequery}';
			var basequery = getMenuWindow().decode64(encode) ;
			var facilityID = '${facilityID}';
			var selectedNodeId = '${selectedNodeId}';
			var treeLevelValue = '${treeLevelValue}';
			var exportMaxPages = '${requestScope.exportMaxPages}';
			var plusEncoded = "<%=SwtConstants.PLUS_URL_ENCODED%>";
			var screenRoute = "AlertInstanceSummary";	
			var appName = "<%=SwtUtil.appName%>"; 
			 /*var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["button-refresh"] = "<fmt:message key="button.genericdisplaymonitor.refresh"/>";
			label["text"]["label-entity"] = "<fmt:message key="scenarioSummary.Entity"/>";
			label["text"]["button-close"] = "<fmt:message key="button.genericdisplaymonitor.close"/>";
			
			label["text"]["label-noData"] = "<fmt:message key="alert.scenarioSummary.noData"/>"; 
			
			label["text"]["label-sumNoDataTitle"] = "<fmt:message key="alert.Summary.noData.title"/>"; 
			label["text"]["label-scenSumNoDataTitle"] = "<fmt:message key="alert.scenarioSummary.noData.title"/>"; 
			
			label["text"]["label-scensummaryXML"] = "<fmt:message key="scenarioSummary.title.xml"/>"; 
			label["text"]["label-scensummaryShowXML"] = "<fmt:message key="scenarioSummary.context.xml"/>"; 
			label["text"]["label-summaryXML"] = "<fmt:message key="Summary.title.xml"/>"; 
			label["text"]["label-summaryShowXML"] = "<fmt:message key="Summary.context.xml"/>"; 
			label["text"]["label-applyCcy"] = "<fmt:message key="scenarioSummary.applyCcy"/>"; 
			label["text"]["label-zeroTotals"] = "<fmt:message key="scenarioSummary.zeroTotals"/>"; 
			label["text"]["label-alertableScen"] = "<fmt:message key="scenarioSummary.alertableScen"/>"; 
			label["text"]["label-popupScen"] = "<fmt:message key="scenarioSummary.popupScen"/>";
			label["text"]["label-flashScen"] = "<fmt:message key="scenarioSummary.flashScen"/>";
			label["text"]["label-scenTotals"] = "<fmt:message key="scenarioSummary.scenTotals"/>"; 
			label["text"]["label-selectedScenLastRan"] = "<fmt:message key="scenarioSummary.selectedScenLastRan"/>"; 
			label["text"]["label-selectedScen"] = "<fmt:message key="scenarioSummary.selectedScen"/>"; 
			
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";
			label["tip"]["label-entity"] = "<fmt:message key="tooltip.selectEntityid"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
			label["tip"]["label-scenTotals"] = "<fmt:message key="tooltip.scenTotals"/>";
			label["tip"]["label-selectedScen"] = "<fmt:message key="tooltip.selectedScen"/>";
			
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>"; 
			label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>"; 
			label["text"]["label-scenarioSummaryTitle"] = "<fmt:message key="scenarioSummary.title"/>"; 
			label["text"]["alert-contactAdmin"] = "<fmt:message key="error.contactAdmin"/>"; 
			label["text"]["alert-error"] = "<fmt:message key="screen.alert.error"/>"; 
			label["text"]["label-taking"] = "<fmt:message key="label.taking"/>";
			label["text"]["label-seconds"] = "<fmt:message key="label.seconds"/>";*/
						
			function help(){
				  openWindow(buildPrintURL('print','Scenario Summary'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true');
			}
			/**
			 * Open the facility screen. It depends on scenario and the related properties of the selected count
			 *
			 **/
			function openFacility(scenarioTitle, useGeneric, facilityId,facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold,count){

				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1); 
				// If the use geneic is 'Y' then we have to display the generic display else a facility screen will be opened
				if (useGeneric == 'Y'){
					// Get the base query of the scenario id from data base through AJAX
					requestURL = requestURL + appName+"/scenMaintenance.do?method=getScenPropertiesForGeneric";
					requestURL = requestURL + "&scenarioId=" + scenarioId;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open( "POST", requestURL, false );
					oXMLHTTP.send();
					var scenProperties = new String(oXMLHTTP.responseText);
					var baseQuery = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[0];
					var entityColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[1];
					var ccyColumn = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[2];
					var refColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[3];
					var facilityrefColumns = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[4];
					var refParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[5];
					var facilityRefParams = scenProperties.split('<%=SwtConstants.SEPARATOR_RECORD%>')[6];
					// Encode the base query do not have problems with special characters
					baseQuery = getMenuWindow().encode64(baseQuery);
					
					// We have to filter the data in the opened window, generic display screen
					var filter = "";
					if (entityId.toUpperCase() == 'ALL')
						filter == 'All' + '|';
					else
						filter = entityColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + entityId +"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str|';
					
					if (currencyId.toUpperCase() == 'ALL')
						filter += 'All';
					else
						filter += ccyColumn + "<%=SwtConstants.SEPARATOR_RECORD%>" + currencyId+"<%=SwtConstants.SEPARATOR_RECORD%>"+ 'str';
					
					// Encode the defined filter value as it may contains double quote (")
					filter = getMenuWindow().encode64(filter);
				  	var param = 'genericdisplay.do?method=genericDisplay';
					param+='&scenarioID='+scenarioId;
					param+='&fromSummaryScreen='+"true";
					param+='&scenarioTitle='+scenarioTitle;		
					param+='&refColumns='+refColumns;
					param+='&facilityRefColumns='+ facilityrefColumns;
					param+='&refParams='+ refParams;
					param+='&facilityRefParams='+ facilityRefParams;
					param+='&applyCurrencyThreshold='+ applyCurrencyThreshold;
					param+='&facilityID='+facilityId;
					param+='&facilityName='+facilityName;
					param+='&basequery='+baseQuery;
					param+= '&filter=' + filter;
					
					// Open the generic screen
					openWindow(param,'genericdisplay','left=50,top=190,width=1230,height=480,toolbar=0, resizable=yes, scrollbars=yes','true');
				}else{
					// Open the movement summary screen if the facility id is 'MSD'
					if (facilityId == "MSD"){
						var sysdate = "<%=SwtUtil.getSystemDateString()%>";
						var url="outstandingmovement.do?";
						url += "method=flex";
						// Define the initial input screen as X for scenario summary
						url += "&initialinputscreen=X";
						url += "&totalFlag=Y";
						// the position level is 9, the hightest level. 
						url += "&posLvlId=9";
						url += "&currencyCode="+currencyId;
						url += "&entityId=" + entityId;
						url += "&date=" + sysdate;
						url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
						url += "&workflow=";
						url += "&scenarioId=" + scenarioId;
						openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}// Open the movement match summary display screen if the facility id is 'MATCH_DISPLAY_MANY'
					else if (facilityId == "MATCH_DISPLAY_MANY"){
						var sysdate = "<%=SwtUtil.getSystemDateString()%>";
						var url="movementmatchdisplay.do?";
						url += "&status=M";
						url += "&quality=D";
						url += "&matchCount="+count;
						url += "&currencyCode="+currencyId;
						url += "&entityId=" + entityId;
						url += "&date=";
						url += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
						url += "&day=NotAll";
						url += "&scenarioId=" + scenarioId;
						url += "&dateTabIndFlag=N";
						url += "&dateTabInd=0";
						openWindow(url, 'mvntSummaryScreenWindow','left=50,top=190,width=1280,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}else {
						 requestURL = new String('<%=request.getRequestURL()%>');
						 idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1); 
						requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
						requestURL = requestURL + "&facilityId=" + facilityId;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var screenDetails=new String(oXMLHTTP.responseText);
						
						if(screenDetails == ""){
							return;
						}
						var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
						programName = screenDetailsList[0];
						var actionPathStr = programName.replace('?', '.');
						var menuAccessIdChild = getMenuAccessIdOfChildWindow(actionPathStr);
						if (programName.indexOf("?") == -1)
							programName += '?';
						width = screenDetailsList[1];
						height = screenDetailsList[2];
						 appName = "<%=SwtUtil.appName%>";
						 requestURL = new String('<%=request.getRequestURL()%>');
						 idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1); 
						requestURL = requestURL + appName+"/scenMaintenance.do?method=getAdditionalInfo";
						requestURL = requestURL + "&scenarioId=" + scenarioId;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var additionalParams=new String(oXMLHTTP.responseText);
						var key = "&entityId="+ entityId +"&selectedEntityId=" + entityId;
						key += "&currencyId=" + currencyId;
						key += "&applyCurrencyThreshold=" + applyCurrencyThreshold;
						key += "&calledFrom=generic";
	                                        key += "&menuAccessId="+menuAccessIdChild;
						key +=additionalParams;
						// Open the facility screen 
						openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}
				}
			}
			
			
			/**
			 * method to open the facility screen related to the facility id of a selected scenario
			 **/
			function goTo(facilityID, hostID, entityID, matchIdKey, currencyCodeKey, movementIdKey, sweepIdKey,additionalParams){
				
				if (hostID == "" || entityID == "") {
					alert("<fmt:message key='alert.FacilityMissingValues'/>");
				} else {
					if (!facilityAccess(hostID, entityID, facilityID, currencyCodeKey))
						alert("<fmt:message key='alert.accessToFacility'/>");
					else{							
						var appName = "<%=SwtUtil.appName%>";
						var requestURL = new String('<%=request.getRequestURL()%>');
						var idy = requestURL.indexOf('/'+appName+'/');
						requestURL=requestURL.substring(0,idy+1) ; 
						requestURL = requestURL + appName+"/genericdisplay.do?method=getScreenDetails";
						requestURL = requestURL + "&facilityId=" + facilityID;
						var oXMLHTTP = new XMLHttpRequest();
						oXMLHTTP.open( "POST", requestURL, false );
						oXMLHTTP.send();
						var screenDetails=new String(oXMLHTTP.responseText);
						if(screenDetails == ""){
							return;
						}
						var screenDetailsList = screenDetails.split("<%=SwtConstants.SEPARATOR_SCREEN_DETAILS%>");
						var programName = screenDetailsList[0];
						if (programName.indexOf("?") == -1)
							programName += '?';
						var width = screenDetailsList[1];
						var height = screenDetailsList[2];
						
						var key = "&hostId=" + hostID + "&entityId="+ entityID +"&selectedEntityId=" + entityID;
						key += "&matchId=" + matchIdKey; 
						key += "&calledFrom=generic";
						key += "&currencyId=" + currencyCodeKey;
						key += "&selectedMovementId=" + movementIdKey;
						key += "&selectedSweepId=" + sweepIdKey;
						key +=getMenuWindow().decode64(additionalParams);
						openWindow(programName + key, 'facilityScreenWindow','left=0,top=55,width='+width+',height='+height+',toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}
				}
			}
			
			/**
			 * Return true or false, if the user has access to a facility screen or not
			 * 
			 * @param hostId
			 * @param entityId
			 * @param facilityId
			 * @param currencyCode
			 **/
			function facilityAccess(hostId, entityId, facilityId, currencyCode){


				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ; 
				requestURL = requestURL + appName+"/genericdisplay.do?method=getFacilityAccess";
				requestURL = requestURL + "&hostId=" + hostId;
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&facilityId=" + facilityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var access=new String(oXMLHTTP.responseText);
				return (parseInt(access) == <%=SwtConstants.FACILITY_NO_ACCESS%>) ? false: true;
			}
			
			/**
			* Called from main.jsp to refresh the scenario summary flex screen
			* Added by Saber Chebka
			**/
			function refreshFlexContent(){
				Main.refreshFromJsp();
			}
			
			function showMvmnt(methodName,movementId,entityId){
				var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
			    var menuName = new String('<fmt:message key="mvmDisplay.title.window"/>');
				var param = 'movement.do?method='+methodName;
			    param += '&entityCode=' + entityId;
			    param += '&movementId=' + movementId;
				openWindow(param,'movementWindow','left=50,top=190,width=985,height=787,toolbar=0, resizable=yes, scrollbars=yes','true');
				return false;
				
			}
			
			
			function openInstDetails(methodName, params){
				var param = '/' + appName + '/scenarioSummary.do?method='+methodName;
				param += '&allParams=' + params;
				var 	mainWindow = openWindow (param, 'alertInstanceDisplay','left=10,top=230,width=900,height=920,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
				return false;
			}
			
		</script>
        <%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
			<input name="method" type="hidden" value="">
			<input name="scenarioID" type="hidden" value="${scenarioID}">
			<input name="scenarioTitle" type="hidden" value="${scenarioTitle}">
			<input name="facilityID" type="hidden" value="${facilityID}">
			<input name="basequery" type="hidden" value="${basequery}">
			<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
			<input name="selectedSort" type="hidden" value='${selectedSort}'>
			<input name="exportType" type="hidden" value="">
			<input name="pageCount" type="hidden" value=""> 
			<input name="currentPage" type="hidden" value=""> 
			<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="genericdisplaymonitor.title.window" />" />
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>