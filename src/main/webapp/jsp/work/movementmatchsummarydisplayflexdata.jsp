<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="java.util.*" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>




<!-- Start: Source modified for Mantis 1691:"Movement Match Summary Display: poor performance in Offered queue" by Marshal on 20-June-2012 -->
<movementmatchsummarydisplay
		fullAccess="${requestScope.fullAccess}"
		entityid="${requestScope.movement.id.entityId}"
		entityname="${requestScope.movementDetail.entityName}"
		matchid="${requestScope.movement.matchId}"
		matchindex="${requestScope.movementDetail.matchIndex}"
		totalmatches="${requestScope.movementDetail.totalMatches}"
		mvmntstatus="${requestScope.movement.matchStatus}"
		matchstatus="${requestScope.movementDetail.matchStatus}"
		matchquality="${requestScope.movementDetail.matchQuality}"
		updatedate="${requestScope.movement.updateDateAsString}"
		updateuser="${requestScope.movement.updateUser}"
		currencycode="${requestScope.movement.currencyCode}"
		usernotes="${requestScope.movementDetail.notes}"
		posintlvl="${requestScope.movementDetail.posLevelInternal}"
		posextlvl="${requestScope.movementDetail.posLevelExternal}"
		scenarioAlerting="${requestScope.movement.matchcenarioHighlighted}"
		currfontsize="${requestScope.fontSize}"
		acctaccessstatus="${requestScope.accountAccessStatus}"
		matchHash="${requestScope.matchHash}"
		ccyTolerance="${requestScope.ccyTolerance}"
		suppMatchDiffWarning="${requestScope.suppMatchDiffWarning}"
		currencyPattern="${requestScope.currencyPattern}">



	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<!-- Alerting -->
				<column
						heading=""
						draggable="false"
						filterable="false"
						type="str"
						dataelement="alerting"
						width="10"
						sort="true"
						clickable="true"
						resizable="false" />

				<!-- Iterate over column order -->
				<c:forEach var="order" items="${requestScope.column_order}">
					<c:choose>
						<c:when test="${order == 'pos'}">
							<column
									heading="<fmt:message key='movement.position'/>"
									draggable="false"
									filterable="true"
									columnNumber="0"
									type="str"
									sort="true"
									dataelement="pos"
									width="${requestScope.column_width['pos']}" />
						</c:when>
						<c:when test="${order == 'value'}">
							<column
									heading="<fmt:message key='movement.value'/>"
									draggable="true"
									filterable="true"
									columnNumber="1"
									type="str"
									sort="true"
									dataelement="value"
									width="${requestScope.column_width['value']}" />
						</c:when>
						<c:when test="${order == 'amount'}">
							<column
									heading="<fmt:message key='movement.amount1'/>"
									draggable="true"
									filterable="false"
									columnNumber="2"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num"
									sort="true"
									dataelement="amount"
									width="${requestScope.column_width['amount']}" />
						</c:when>
						<c:when test="${order == 'sign'}">
							<column
									heading="<fmt:message key='movement.sign'/>"
									draggable="true"
									filterable="true"
									columnNumber="3"
									type="str"
									sort="true"
									dataelement="sign"
									width="${requestScope.column_width['sign']}" />
						</c:when>
						<c:when test="${order == 'ref1'}">
							<column
									heading="<fmt:message key='movement.reference1'/>"
									draggable="true"
									filterable="false"
									columnNumber="4"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="ref1"
									width="${requestScope.column_width['ref1']}" />
						</c:when>
						<c:when test="${order == 'ref2'}">
							<column
									heading="<fmt:message key='movement.reference2'/>"
									draggable="true"
									filterable="false"
									columnNumber="12"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="ref2"
									width="${requestScope.column_width['ref2']}" />
						</c:when>
						<c:when test="${order == 'ref3'}">
							<column
									heading="<fmt:message key='movement.reference3'/>"
									draggable="true"
									filterable="false"
									columnNumber="13"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="ref3"
									width="${requestScope.column_width['ref3']}" />
						</c:when>
						<c:when test="${order == 'account'}">
							<column
									heading="<fmt:message key='movement.account'/>"
									draggable="true"
									filterable="true"
									columnNumber="5"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="account"
									width="${requestScope.column_width['account']}" />
						</c:when>
						<c:when test="${order == 'cparty'}">
							<column
									heading="<fmt:message key='movement.counterPartyId'/>"
									draggable="true"
									filterable="true"
									columnNumber="6"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="cparty"
									width="${requestScope.column_width['cparty']}" />
						</c:when>
						<c:when test="${order == 'pred'}">
							<column
									heading="<fmt:message key='movement.pred'/>"
									draggable="true"
									filterable="true"
									columnNumber="7"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="pred"
									width="${requestScope.column_width['pred']}" />
						</c:when>
						<c:when test="${order == 'notes'}">
							<column
									heading="<fmt:message key='movement.notes'/>"
									draggable="true"
									filterable="false"
									columnNumber="8"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="notes"
									width="${requestScope.column_width['notes']}" />
						</c:when>
						<c:when test="${order == 'source'}">
							<column
									heading="<fmt:message key='movement.source'/>"
									draggable="true"
									filterable="true"
									columnNumber="9"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="source"
									width="${requestScope.column_width['source']}" />
						</c:when>
						<c:when test="${order == 'input'}">
							<column
									heading="<fmt:message key='movement.input'/>"
									draggable="true"
									filterable="true"
									columnNumber="10"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="input"
									width="${requestScope.column_width['input']}" />
						</c:when>
						<c:when test="${order == 'beneficiary'}">
							<column
									heading="<fmt:message key='movement.beneficiary'/>"
									draggable="true"
									filterable="true"
									columnNumber="11"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="beneficiary"
									width="${requestScope.column_width['beneficiary']}" />
						</c:when>
						<c:when test="${order == 'movement'}">
							<column
									heading="<fmt:message key='movement.movementId'/>"
									draggable="true"
									filterable="false"
									columnNumber="14"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num"
									sort="true"
									dataelement="movement"
									width="${requestScope.column_width['movement']}" />
						</c:when>
						<c:when test="${order == 'book'}">
							<column
									heading="<fmt:message key='movement.bookcode'/>"
									draggable="true"
									filterable="true"
									columnNumber="15"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="book"
									width="${requestScope.column_width['book']}" />
						</c:when>
						<c:when test="${order == 'custodian'}">
							<column
									heading="<fmt:message key='movement.custodian'/>"
									draggable="true"
									filterable="true"
									columnNumber="16"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="custodian"
									width="${requestScope.column_width['custodian']}" />
						</c:when>
						<c:when test="${order == 'matchingparty'}">
							<column
									heading="<fmt:message key='movement.matchingParty'/>"
									draggable="true"
									filterable="true"
									columnNumber="18"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="matchingparty"
									width="${requestScope.column_width['matchingparty']}" />
						</c:when>
						<c:when test="${order == 'producttype'}">
							<column
									heading="<fmt:message key='movement.productType'/>"
									draggable="true"
									filterable="true"
									columnNumber="19"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="producttype"
									width="${requestScope.column_width['producttype']}" />
						</c:when>
						<c:when test="${order == 'postingdate'}">
							<column
									heading="<fmt:message key='movement.postingDate'/>"
									draggable="true"
									filterable="true"
									columnNumber="20"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str"
									sort="true"
									dataelement="postingdate"
									width="${requestScope.column_width['postingdate']}" />
						</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${recordCount}">
			<c:forEach var="movementSummaryDetails" items="${requestScope.movementSummaryDetails}">
				<row>
					<alerting clickable="false">${movementSummaryDetails.scenarioHighlighted}</alerting>

					<pos clickable="false" positionlevel="${movementSummaryDetails.positionLevel}">
							${movementSummaryDetails.positionLevelName}
					</pos>

					<value clickable="false">${movementSummaryDetails.valueDateAsString}</value>

					<amount clickable="false" negative="${movementSummaryDetails.sign == 'D'}">
							${movementSummaryDetails.amountAsString}
					</amount>

					<sign clickable="false">${movementSummaryDetails.sign}</sign>
					<ref1 clickable="false">${movementSummaryDetails.reference1}</ref1>
					<ref2 clickable="false">${movementSummaryDetails.reference2}</ref2>
					<ref3 clickable="false">${movementSummaryDetails.reference3}</ref3>
					<account clickable="false">${movementSummaryDetails.accountId}</account>
					<cparty clickable="false">${movementSummaryDetails.counterPartyId}</cparty>
					<pred clickable="false">${movementSummaryDetails.predictStatus}</pred>
					<notes clickable="false">${movementSummaryDetails.noOfNotesAttached}</notes>
					<source clickable="false">${movementSummaryDetails.inputSource}</source>
					<input clickable="false">${movementSummaryDetails.inputDateAsString}</input>
					<beneficiary clickable="false">${movementSummaryDetails.beneficiaryId}</beneficiary>
					<movement clickable="false">${movementSummaryDetails.id.movementId}</movement>
					<book clickable="false">${movementSummaryDetails.bookCode}</book>
					<custodian clickable="false">${movementSummaryDetails.custodianId}</custodian>
					<matchingparty clickable="false">${movementSummaryDetails.matchingParty}</matchingparty>
					<producttype clickable="false">${movementSummaryDetails.productType}</producttype>
					<postingdate clickable="false">${movementSummaryDetails.postingDateAsString}</postingdate>
					<externalBalStatus clickable="false">${movementSummaryDetails.extBalStatus}</externalBalStatus>
				</row>
			</c:forEach>
		</rows>

	</grid>
<!-- End: Source modified for Mantis 1691:"Movement Match Summary Display: poor performance in Offered queue" by Marshal on 20-June-2012 -->
	<selects>
		<select id="entity">
			<c:forEach var="entity" items="${requestScope.entities}">
				<option value="${entity.value}"
						selected="${entity.value == requestScope.movement.id.entityId ? '1' : '0'}">
						${entity.label}
				</option>
			</c:forEach>
		</select>
	</selects>
</movementmatchsummarydisplay>