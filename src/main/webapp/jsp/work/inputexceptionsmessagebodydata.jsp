<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!-- Setting locale and resource bundle -->



<inputexceptions>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">
				<c:out value="${opTime.value}" />
			</operation>
		</c:forEach>
	</timing>

	<message>
		<c:out value="${requestScope.message}" />
	</message>
</inputexceptions>