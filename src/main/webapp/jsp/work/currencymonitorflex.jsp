<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<title><fmt:message key="currencyMonitor.title.window"/></title>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">

<script type="text/javascript">
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();
			};

			window.onunload = call;

			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["entity"] = "<fmt:message key="entity.id"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";

			label["text"]["currency"] = "<fmt:message key="currency.group"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.selectCuurencyGrp"/>";


			label["text"]["datefrom"] = "<fmt:message key="currencyInterest.fromDate"/>";
			label["tip"]["datefrom"] = "<fmt:message key="tooltip.selectFromDate"/>";

			label["tip"]["showdays"] = "<fmt:message key="tooltip.showdays"/>";
			label["text"]["showdays"] = "<fmt:message key="text.showdays"/>";
			label["text"]["day"] = "<fmt:message key="text.day"/>";
			label["text"]["days"] = "<fmt:message key="text.days"/>";

			label["text"]["breakdown"] = "<fmt:message key="currMonitor.breakdown"/>";

			label["text"]["acctbrkdown"] = "<fmt:message key="movement.accountId"/>";
			label["tip"]["acctbrkdown"] = "<fmt:message key="tooltip.AcuntBrkdown"/>";
			label["text"]["mvmntbrkdown"] = "<fmt:message key="manualInput.id.movementId"/>";
			label["tip"]["mvmntbrkdown"] = "<fmt:message key="tooltip.MvmntBrkdown"/>";
			label["text"]["bookbrkdown"] = "<fmt:message key="movement.bookcode"/>";
			label["tip"]["bookbrkdown"] = "<fmt:message key="tooltip.BookBrkdown"/>";
			label["text"]["grpmonitor"] = "<fmt:message key="currMonitor.group"/>";
			label["tip"]["grpmonitor"] = "<fmt:message key="tooltip.viewGroupMonitor"/>";
			label["text"]["mtagrpmonitor"] = "<fmt:message key="currMonitor.metagroup"/>";
			label["tip"]["mtagrpmonitor"] = "<fmt:message key="tooltip.viewMetagroupMonitor"/>";

			label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

			label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

			label["text"]["button-options"] = "<fmt:message key="button.option"/>";
			label["tip"]["button-options"] = "<fmt:message key="tooltip.option"/>";

			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

			label["text"]["alert.currencyAccess"] = "<fmt:message key="alert.currencyAccess"/>";
			label["text"]["alert.toDateNotOnRange"] = "<fmt:message key="alert.toDateNotOnRange"/>";

			label["text"]["lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["label-currencyMonitor"] = "<fmt:message key="label.currencyMonitor"/>";
			label["tip"]["datefromDDMMYY"] = "<fmt:message key="tooltip.enterValueDate"/>";

			label["text"]["alert-warning"] = "<fmt:message key="screen.alert.warning"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";

		// ]]>
		</script>

<script type="text/javascript">
			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';

			var dateSelected = false;
			var today = "${requestScope.today}";
			var existingDate = "";

			var refreshPending = false;

			var appName = "<%=SwtUtil.appName%>";
			var testDate= "<%=SwtUtil.getSystemDateString() %>";

			var baseURL = new String('<%=request.getRequestURL()%>');
			var screenRoute = "currencyMonitor";
		   /*
 			* Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
 			*/
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';
			var currentFontSize = "<%=request.getAttribute("fontSize")%>";

			 // Start:added by Mefteh for Mantis 2016
			<% List<Integer> listPriorAheadDays=SwtUtil.getPriorAndAheadDaysToToday();%>
            var nDaysPriorToToday =<%=listPriorAheadDays.get(0)%>;
			var nDaysAheadToToday =<%=listPriorAheadDays.get(1)%>;
		     // End: added by Mefteh for Mantis 2016
			var alertOrangeImage = "images/Alert/scenario/normal.png";
			var alertRedImage = "images/Alert/scenario/critical.png";
			/**
			 * clickLoro
			 * @param sEntityId
			 * @param sCurrencyCode
			 * @param sFromDate
			 * Method to load Account Break down monitor
			 */
			function clickLoro (sEntityId, sCurrencyCode, sFromDate) {

				var menuAccIdForAcctBrkdown = getMenuAccessIdOfChildWindow("acctbreakdownmonitor.do");
				var requestURL = "acctbreakdownmonitor.do?method=display&menuAccessId=" + menuAccIdForAcctBrkdown;

				var callstatus=true;
				arrtributString="left=50,top=190,width=1200,height=700,toolbar=0, resizable=yes, scrollbars=yes,status=yes";

				requestURL = requestURL + "&entityId=" + sEntityId;
				requestURL = requestURL + "&currencyCode=" + sCurrencyCode;
				requestURL = requestURL + "&selectedBalanceType=<%=SwtConstants.ACCT_MONITOR_BALTYPE_LORO%>";
				requestURL = requestURL + "&accountId=<%=SwtConstants.ALL_LABEL%>";
				requestURL = requestURL + "&selectedValueDate=" + sFromDate;
				requestURL = requestURL + "&callstatus=" +callstatus;

				var menuName = new String('<fmt:message key="accountmonitorBrkDown.title.window"/>');
				var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
				menuName = menuName.substr(0,smrtPredPos-3);
				if (menuAccIdForAcctBrkdown == 2) {
					alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
				} else {
					openWindow(requestURL,'bookMonitorJsp',arrtributString);
				}
				if (window.event){
				   	window.event.returnValue = false;
					window.event.cancelBubble = true;
				}

				return false;
			}

			function clickLink (sEntityId, sCurrencyCode, sColumnDate, sActionCode) {
				var menuAccessIdChild = 0;
				var paramString="";
				paramString = paramString + "&entityId=" + sEntityId;
				paramString = paramString + "&currencyCode=" + sCurrencyCode;
				paramString = paramString + "&valueDate=" + sColumnDate;

				var requestURL = new String ('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf ('/'+appName+'/');
				var arrtributString;

				requestURL=requestURL.substring (0, idy+1) ;

				if (sActionCode == "A") {

					menuAccessIdChild = getMenuAccessIdOfChildWindow("acctmonitornew.do");
					var menuName = new String('<fmt:message key="accountmonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					}
					requestURL = requestURL +appName +"/acctmonitornew.do?method=callFromCurrencyMonitor&menuAccessId="+menuAccessIdChild+"&callFrom=CM";
					arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes";

				} else if (sActionCode == "M") {

					requestURL = requestURL +appName +"/currmonitorNew.do?method=countMovements&breakdown=All";
					requestURL += paramString;

					var noOfMovements = sendRequest (requestURL);

					if(noOfMovements == 0) {
						alert('<fmt:message key="monitor.nomovements"/>');
						return false;
					} else {

						var requestURL = 'outstandingmovement.do?method=flexCurrency';
						arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
						requestURL = requestURL + "&initialinputscreen=" + "currencymonitor";
					}
				} else if (sActionCode == "B") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					var menuName = new String('<fmt:message key="bookMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Book";
					arrtributString="left=50,top=190,width=860,height=655,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				} else if (sActionCode == "G") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					var menuName = new String('<fmt:message key="groupMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Group";
					arrtributString="left=50,top=190,width=860,height=655,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				} else if (sActionCode == "MG") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					// Start : Code Modified for Mantis 2123 by M.BOURAOUI on 29/11/2012 */
					var menuName = new String('<fmt:message key="BookgroupMonitor.title.window"/>');
					// End : Code Modified for Mantis 2123 by M.BOURAOUI on 29/11/2012 */
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Metagroup";
					arrtributString="left=50,top=190,width=850,height=655,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				}

				requestURL += "&entityId=" + sEntityId;
				requestURL += "&currencyCode=" + sCurrencyCode;
				requestURL += "&valueDate=" + sColumnDate;
				requestURL += "&callfromcurrency=y";


				if (menuAccessIdChild != 2) {
					openWindow (requestURL, 'bookMonitorJsp', arrtributString);
				}
				if (window.event){
					window.event.returnValue = false;
					window.event.cancelBubble = true;
				}
				return false;
			}

			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();

				var count = new Number (oXHR.responseText);
				return count;
			}
			function openOptionsWindow () {
				var param = '/' + appName + '/currmonitorNew.do?method=displayCurrencyMonitorOptions';

				openWindow (param, 'currencyMonitorOptions', 'width=550,height=280,toolbar=0, resizable=yes, scrollbars=no', 'true');
			}
			function help(){
					openWindow(buildPrintURL('print','Currency Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
                      }

			function callApp(refreshStart){
				instanceElement.startAutoRefresh(refreshStart);
			}


			/**
			* This function is used to send the request to save the font size set by the user
			* && sets it in the database.
			*
			* @param fontSize - set by the user.
			*
			*/
			function getUpdateFontSize(fontSize) {
				return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + fontSize;
			}


			function onDateKeyPress(obj,e){
				var event = (window.event|| e);
				document.forms[0].elements["groupMonitor.selectedTabIndex"].value = '4';
				document.forms[0].elements["groupMonitor.selectedTabName"].value = 'groupMonitorSelectedDateParent';
				if(event.keyCode == 9)
				{
				   if(validateField(obj,'date',dateFormat))
				   {
					   var days2=(parseDate(document.forms[0].elements['groupMonitor.date'].value,dateFormat).getTime())/(60*60*1000);
						var days1=(parseDate(today,dateFormat).getTime())/(60*60*1000);

						if(((days2-days1)/24)>29){
								alert("<fmt:message key="currMonitor.alert.dateRangeValidation"/>");
								document.forms[0].elements['groupMonitor.date'].value=existingDate;
								return false;
						}else{
								document.forms[0].method.value = "displayGroupMonitorDetails";
								document.forms[0].submit();
								//submitForm('displayBookMonitorDetails');
								dateSelected = false;
								return true;
						}
				   }else{
						return false;
				   }
				}
				if(event.keyCode == 13)
				{
				  if(validateField(obj,'date',dateFormat))
				  {
					  var days2=(parseDate(document.forms[0].elements['groupMonitor.date'].value,dateFormat).getTime())/(60*60*1000);
						var days1=(parseDate(today,dateFormat).getTime())/(60*60*1000);

						if(((days2-days1)/24)>29){
								alert("<fmt:message key="currMonitor.alert.dateRangeValidation"/>");
								document.forms[0].elements['groupMonitor.date'].value=existingDate;
								return false;
						}else{
								document.forms[0].method.value = "displayGroupMonitorDetails";
								document.forms[0].submit();
								//submitForm('displayBookMonitorDetails');
								dateSelected = false;
								return true;
						}
				  }else{
					return false;
				  }
				}
			}



		</script>
</head>

<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
	value="<fmt:message key="currencyMonitor.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>