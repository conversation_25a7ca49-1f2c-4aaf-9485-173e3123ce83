<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<scenarioSummaryScreen
		currencythreshold="${requestScope.currencythreshold}"
		popupScenarios="${requestScope.popupScenarios}"
		flashScenarios="${requestScope.flashScenarios}"
		emailScenarios="${requestScope.emailScenarios}"
		hidezerocounts="${requestScope.hidezerocounts}"
		alertablescenarios="${requestScope.alertablescenarios}"
		lastRefTime="${requestScope.lastRefTime}"
		dateFormat="${requestScope.dateFormat}"
		currencyFormat="${requestScope.currencyFormat}"
		autoRefreshRate="${requestScope.autoRefreshRate}"
		displayedDate="${requestScope.displayedDate}"
>
	<request_reply>
		<status_ok>
			<c:out value="${requestScope.reply_status_ok}" />
		</status_ok>
		<message>
			<c:out value="${requestScope.reply_message}" />
		</message>
		<location />
	</request_reply>
	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>
	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option
						value="${entity.value}"
						selected="${entity.value eq requestScope.entityId ? '1' : '0'}"
				>${entity.label}</option>
			</c:forEach>
		</select>
	</selects>

	<tabsCategory size="${requestScope.tabSize}">
		<c:forEach items="${requestScope.tabCategoryDetails}" var="tab">
			<displaytab
					count="${tab.count}"
					tabName="${tab.tabName}"
					tabId="${tab.tabId}"
			>${tab.tabNameAsString}</displaytab>
		</c:forEach>
		<selectedIndex
				tabindex="${requestScope.selectedTabIndexCategory}"
				index="${requestScope.selectedIndexCategory}"
		/>
	</tabsCategory>

</scenarioSummaryScreen>
