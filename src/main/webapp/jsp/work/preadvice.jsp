<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@page import="org.swallow.work.model.Movement"%>
<%@ page import="org.swallow.util.struts.TokenHelper"%>
<%
	//variable declaration
	//Initialize screenIdentifier,valueDateEditStatus,
	//amountEditStatus,screenTitle,accountId,
	//bookCodeId,accountName,bookCodeName with empty value
	String screenIdentifier = "";
	String valueDateEditStatus = "";
	String amountEditStatus = "";
	String screenTitle = "";
	String accountId = "";
	String bookCodeId = "";
	String accountName = "";
	String bookCodeName = "";
	//Instantiate movement with null
	Movement movement = null;
	//get the screenIdentifier from request attribute 
	if (request.getAttribute("inputscreen") != null) {
		screenIdentifier = request.getAttribute("inputscreen")
				.toString();
	}
	//get the valueDateEditStatus from request attribute
	if ((request.getAttribute("valueDateEditStatus") != null)) {
		valueDateEditStatus = request.getAttribute(
				"valueDateEditStatus").toString();
	}
	//get the amountEditStatus from request attribute
	if ((request.getAttribute("amountEditStatus") != null)) {
		amountEditStatus = request.getAttribute("amountEditStatus")
				.toString();
	}
	//set the screen title based on the which screen has opened
	if (!SwtUtil.isEmptyOrNull(screenIdentifier)) {
		//set screen title for pre advice input screen
		if (screenIdentifier.equals("insert"))
			screenTitle = SwtUtil
					.getMessage("title.preadvice.inputWindow", request);
		//set screen title for pre advice update screen
		else if (screenIdentifier.equals("update"))
			screenTitle = SwtUtil
					.getMessage("title.preadvice.changeWindow", request);
		//set screen title for pre advice display screen
		else
			screenTitle = SwtUtil
					.getMessage("title.preadvice.displayWindow", request);
	}
	//get the movement from request
	if (request.getAttribute("movement") != null) {
		//set the Movement bean
		movement = (Movement) request.getAttribute("movement");
		//get the account id 
		accountId = movement.getAccountId();
		//check account name not null
		if (request.getAttribute("accountDesc") != null)
			//get the account name from request
			accountName = request.getAttribute("accountDesc")
					.toString();
		//get the book code id 
		bookCodeId = movement.getBookCode();
		//check account name not null
		if (request.getAttribute("bookCodeDesc") != null)
			//get the account name from request
			bookCodeName = request.getAttribute("bookCodeDesc")
					.toString();

	}
%>
<html>
<head>
<title><%=screenTitle%></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
	var dateSelected = false;
	var cancelcloseElements = new Array(2);
	cancelcloseElements[0] = "cancelbutton";
	cancelcloseElements[1] = "closebutton";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');	
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';;
	var cal = new CalendarPopup("caldiv",false,"calFrame"); 
	cal.setCssPrefix("CAL");
	cal.offsetX = 10;
	cal.offsetY = 15;
	var cal1 = new CalendarPopup("caldiv",false,"calFrame");
	cal1.setCssPrefix("CAL");
	cal1.offsetX = 20;
	cal1.offsetY = -130;
	//Following variable's value assigned from request attribute which are used in the javascipt function. 
	var screenIdentifier ="<%= screenIdentifier %>";
	var valueDateEditStatus = "<%= valueDateEditStatus %>";
	var amountEditStatus =  "<%= amountEditStatus %>";
	var accountEditStatus = "${requestScope.accountEditStatus}";
	var bookCodeEditStatus = "${requestScope.bookCodeEditStatus}";
	var refEditStatus = "${requestScope.refEditStatus}";
	var cpartyEditStatus = "${requestScope.cpartyEditStatus}";
	var matchPartyEditStatus = "${requestScope.MatchPartyEditStatus}";
	var prdTypeEditStatus = "${requestScope.PrdTypeEditStatus}";
	var postDateEditStatus = "${requestScope.PostDateEditStatus}";
	var posLevelEditStatus = "${requestScope.posLevelEditStatus}";
	var preStatusEditStatus = "${requestScope.preStatusEditStatus}";
	var menuAccessId = "${requestScope.menuAccessId}";
	//Following variables are used to populate the collection while clicking on account/bookcode select box   
	var accountClk = true; 
	var acctSelectElement = null;
	var acctSwSelectBox = null;
	var bookCodeClk = true;
	var bookCodeSelElement = null;
	var bookCodeSwSelBox = null;
	var accountId = "<%= accountId %>";
	var accountName = "<%= accountName %>";
	var bookCodeId = "<%= bookCodeId %>";
	var bookCodeName = "<%= bookCodeName %>";
	/**
  	  *	These 2 variables are used to define the date format and currency format on the screen.
      */
	var dateFormat = '${sessionScope.CDM.dateFormat}';
	var currencyFormat = '${sessionScope.CDM.currencyFormat}';
	
	var enteredMovId = null;
	var currencyAccessInd="${requestScope.currGrpAccess}";
	var movementAmendedFlag = false;
	 mandatoryFieldsArray=["*"]
	/**
	 * validateFormDate
	 *
	 * This method is used to validate the value date while performing onchange event on this element   
	 **/
	function validateFormDate(objForm) {
		var elementsRef = new Array(1);
		elementsRef[0] = objForm.elements["movement.valueDateAsString"];
		return validate(elementsRef);
	}

	/**
	 * buildMovement
	 *
	 * 	Method called from notes button click. If u will pass existing movementId, It will automatically add notes to the P_MOVEMETN_NOTES table
	 * 	otherwise will save the added notes to the session 
	 **/
	function buildMovement(methodName) {
		var notesURL = 'notes.do?method='+methodName+'&movId=';			 
		notesURL += document.forms[0].elements['movement.id.movementId'] != null ? document.forms[0].elements['movement.id.movementId'].value:
		'';
		notesURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
		notesURL += '&screenName=' + 'manualInputScreenViewMode';
		return  notesURL;
	}

	/**
	 * validateForm
	 *
	 * This method is used to validate the form elements while saving the movement on pre advice screen.   
	 **/
	function validateForm(objForm) {
		var elementsRef = new Array(3);
		elementsRef[0] = objForm.elements["movement.positionLevelAsString"];
		elementsRef[1] = objForm.elements["movement.amountAsString"];	
		elementsRef[2] = objForm.elements["movement.currencyCode"];
		var otherFieldStatusBlank = false; // This variable is false till all other mandatory fields are filled
		for (var i = 0; i<3; i++) {
			if ((new String(elementsRef[i].value)).trim() == "") {
				otherFieldStatusBlank = true;
			}
		}
		if(document.forms[0].elements["movement.accountId"].value =="" && !otherFieldStatusBlank) {
			alert("<fmt:message key="positionLevel.alert.mandatoryFields"/>");	
			document.getElementById("dropdownbutton_2").focus();
			return false;
		}
		return validate(elementsRef);
	}

	/**
	 * enableFields
	 *
	 * This method is used to enable the form elements which was in disable previously.
	 * If elements are in enable state then only we can get the value for that element 
	 * so that this function will be called before form getting submit       
	 *     
	 **/
	function enableFields() {
		document.forms[0].elements["movement.id.entityId"].disabled = "";
		document.forms[0].elements["movement.id.movementId"].disabled = "";
		if (screenIdentifier == "update") {
			document.forms[0].elements['movement.positionLevelAsString'].disabled = "";
			document.forms[0].elements['movement.currencyCode'].disabled = "";
			document.forms[0].elements['movement.valueDateAsString'].disabled = "";
			document.forms[0].elements['movement.amountAsString'].disabled = "";
			document.forms[0].elements['movement.sign'].disabled = "";
	        document.forms[0].elements["movement.movementType"][0].disabled = "";
	        document.forms[0].elements["movement.movementType"][1].disabled = "";
	        document.forms[0].elements['movement.reference1'].disabled = "";
	        document.forms[0].elements['movement.counterPartyId'].disabled = "";
	        document.forms[0].elements['movement.counterPartyText1'].disabled = "";
	        document.forms[0].elements["movement.predictStatus"][0].disabled = "";
	        document.forms[0].elements["movement.predictStatus"][1].disabled = "";
	        //Code Added For Mantis 1827 by Sudhakar on 26-09-2012:Pre Advice display should not accept more than 12 characters And Pre-advice should display cancel radio button in Predict Status
	        document.forms[0].elements["movement.predictStatus"][2].disabled = "";
	        document.forms[0].elements['movement.matchingParty'].disabled = "";
			document.forms[0].elements['movement.productType'].disabled = "";
	        document.forms[0].elements["movement.accountId"].disabled = "";
	        document.forms[0].elements["movement.bookCode"].disabled = "";
	    }
	}
	function tooltipDate() {
		if (dateFormat == 'datePat2') {
			document.getElementById("movement.valueDateAsString").title="<fmt:message key="tooltip.ValueDateMMDDYY"/>";	
		}else{
			document.getElementById("movement.valueDateAsString").title="<fmt:message key="tooltip.enterValueDate"/>";	
		}	
	}
	

	/**
 	 * populateDropBoxes
 	 * 			
 	 * This method is invoked on onload event of window
 	 */
	function populateDropBoxes() {
		populateDropDowns();
		bodyOnLoad();
	}

	/**
	 * set the function which are to be invoked when window onload event is fired.
	 */
	window.onload = function () {
		onLoadStatus();
		setParentChildsFocus();
		tooltipDate();
		setFocus(document.forms[0]);
		populateDropBoxes();
		ShowErrMsgWindow('${actionError}'); 
	};

	/**
 	 * confirmClose
 	 * 			
 	 * This method is invoked while clicking on close button to release the movement lock
 	 */
	function confirmClose(methodName) {
		if(screenIdentifier == "update")
			releaseLock();
		self.close();
	}
    // Start:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre-advice Display should not accept more than 12 characters for Movement Id
	/**
 	 * movementLog
 	 * 			
 	 * This method is used to open the movement log screen which having all log details for given movement id  
 	 */
	function movementLog(methodName) { 
		var auditLogURL = 'auditlog.do?method='+methodName;
		auditLogURL += '&entityCode=';
		auditLogURL += document.forms[0].elements['movement.id.entityId'].value;
		auditLogURL += '&movId=';
		auditLogURL +=removeLeadingZeros(document.forms[0].elements['movement.id.movementId'].value);
		return auditLogURL;
	}
   // End:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre-advice Display should not accept more than 12 characters for Movement Id		
	/**
	 * bodyUnLoad()
	 * 
	 * This method is invoked when window onunload event is fired.
	 **/	
	function bodyUnLoad(methodName) {
		if (screenIdentifier == "update") {
		     releaseLock();
		} 
	 	call();
	}

	/**
	 * parentScreenRefresh()
	 *
	 * This function is used in integration of Notes' jsp pages. and call from the same by using window.opner() function.
	 */
	function parentScreenRefresh() {
		var parentWindow = window.opener;
		var notesValue = document.forms[0].isNotesPresent.value;
		var notesScreenOpened = document.forms[0].notesScreenOpened.value;
		if(notesScreenOpened != "Y")
			notesValue = "${requestScope.isNotesPresent}";	
		if(notesValue != "Y") {
			notesValue = "";
		}
		var isNotesPresent = document.forms[0].isNotesPresent.value;
		if(isNotesPresent == 'Y') {
			notesValue = "Y";
		}
		enableFields();
	   	var movId = document.forms[0].elements["movement.id.movementId"].value;
	  	if ( parentWindow.refreshMovmentNote != 'undefined') {
			parentWindow.refreshMovmentNote(movId,notesValue);
		}
	}

	/**
	 * refreshMovmentNoteImage
	 *
	 * This method is used to update the notes image if there any notes available 
	 * for movement and it will be invoked from notes window 
	 **/
	function refreshMovmentNoteImage() {
		if(screenIdentifier == "update" || screenIdentifier == "display") {
			var isNotesPresent = document.forms[0].isNotesPresent.value;
			var notesIndicator =  document.getElementById("notesIndicator");
			if(isNotesPresent == 'Y') {
				notesIndicator.src = "images/notes.gif"
				notesIndicator.title = '<fmt:message key= "msg.title.notesAvailable"/>';
			}
			if(isNotesPresent != 'Y') {
				notesIndicator.src = "images/blank.png"
				notesIndicator.title = "";
			} 
		}	
	}

	/**
	 * party
	 *
	 * This method is used to open the party search screen when click on the 
	 * counterparty/matching party drop down button
	 **/
	function party(flag,elementId,elementName) {
		var entityId = document.forms[0].elements['movement.id.entityId'].value;
		var partyURL = 'party.do?method=preSearchParties&entityId='+entityId;
		partyURL += '&custodianFlag='+flag;
		partyURL += '&idElementName='+elementId;
		partyURL += '&descElementName='+elementName;
		openWindow(partyURL,'SearchParty','left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes','true');
	}
	
	/**
	 * clearCounterPartyDesc
	 *
	 * This method is called on change of the counter party and used to clear the 
	 * description of the counterparty 
	 **/
	function clearCounterPartyDesc() {
		document.getElementById("counterPartyDesc").innerText = "";
	}
	
	/**
	 * clearMatchingPartyDesc
	 *
	 * This method is called on change of the matching party and used to clear the 
	 * description of the matchingparty 
	 **/
	function clearMatchingPartyDesc() {
		document.getElementById("matchingPartyDesc").innerText = "";
	}

	 
    /**
 	 * populateDropDowns
 	 * 			
 	 * This method is used to add account/bookcode in the select box while displaying pre advice input/change screen.
 	 */
	function populateDropDowns() {
		var acctElement = document.getElementById("accountId")
	    //set the accountId and accountName
	    if (accountId != "" && accountId != "null" && acctElement != null) {
	        acctSelectElement = document.forms[0].elements["movement.accountId"];
	        var optionElement = document.createElement("option");
	        optionElement.text = accountName;
	        optionElement.value = accountId;
	        acctSelectElement.options.add(optionElement);
	        acctSelectElement.selectedIndex = 0;
	        acctElement.value = accountId;
	    }
	    if (accountName != "" && accountName != "null") 
	        document.getElementById("accountDesc").value = accountName;
	    var bookElement = document.getElementById("bookCode")
	    //set the bookCodeId and bookCodeName
	    if (bookCodeId != "" && bookCodeId != "null" && bookElement != null) {
      		bookSelectElement = document.forms[0].elements["movement.bookCode"];
      		var optionElement = document.createElement("option");
      		optionElement.text = bookCodeName;
      		optionElement.value = bookCodeId;
       		bookSelectElement.options.add(optionElement);
       		bookSelectElement.selectedIndex = 0;
        	bookElement.value = bookCodeId;
	     }
	     if (bookCodeName != "" && bookCodeName != "null") 
	     	document.getElementById("bookCodeDesc").value = bookCodeName;
	}
  
    /**
	 * onLoadStatus
	 * 			
	 * This method is used to decide the components properties based on which screen it is calling from
	 */
	function onLoadStatus() {
		if (screenIdentifier == "update") {
			document.getElementById("divMovement").style.height = '380px';
			document.getElementById("ddimagebuttons").style.top = '395px';
			document.getElementById("divPrintHelpBar").style.top = '405px';
			document.getElementById("buttonTable").style.width = '350';
			document.getElementById("dropdowndiv_2").style.top = '185px';
		    document.getElementById("dropdowndiv_3").style.top = '211px';
		    document.forms[0].elements['movement.id.entityId'].disabled="disabled";
			document.forms[0].elements['movement.id.movementId'].disabled="disabled";
			document.getElementById("movement.currencyCode").disabled="true";
			document.getElementById("accountId").disabled="true";
			document.getElementById("bookCode").disabled="true";
			if(valueDateEditStatus == "true") { 
				document.getElementById("movement.valueDateAsString").disabled="true";
				document.getElementById("valueDateLink").style.display="none";
			}	
			if (amountEditStatus == "true") 
				document.getElementById("movement.amountAsString").disabled="true";
		    
			if (amountEditStatus == "true") 
				document.getElementById("movement.amountAsString").disabled="true";
		    
			var radioMovType = document.forms[0].elements["movement.movementType"];
		    radioMovType[0].disabled = "true";
		    radioMovType[1].disabled = "true";
			if(accountEditStatus == "true")
				document.getElementById("dropdownbutton_2").disabled="true";
			if(bookCodeEditStatus == "true")
				document.getElementById("dropdownbutton_3").disabled="true";
			if(refEditStatus == "true")
				document.getElementById("movement.reference1").disabled="true";
			if(cpartyEditStatus == "true") {
				document.getElementById("movement.counterPartyId").disabled="true";
				document.getElementById("counterPartyLink").disabled="true";	
				document.getElementById("movement.counterPartyText1").disabled="true";	
			}	
			if(matchPartyEditStatus == "true") {
				document.getElementById("movement.matchingParty").disabled="true";
				document.getElementById("matchingPartyLink").disabled="true";
			}	
			if(prdTypeEditStatus == "true")	
				setStyleElement(["movement.productType"])
			if(postDateEditStatus == "true") {	
				setStyleElement(["movement.postingDateAsString"])	
				document.getElementById("postingDateLink").style.display="none";
			}	
			if(preStatusEditStatus == "true") {
			/*Start: Code Modified For Mantis 1827 by Sudhakar on 26-09-2012:Pre Advice display should not accept more than 12 characters  And Pre-advice should display cancel radio button in Predict Status*/
				var radioPredictStatus = document.forms[0].elements["movement.predictStatus"];
				radioPredictStatus[0].disabled = "true";
				radioPredictStatus[1].disabled = "true";
				radioPredictStatus[2].disabled = "true";
			}				
		} else if(screenIdentifier == "insert") {
			document.getElementById("entityMandatory").innerText = "*";
		} else {
			document.getElementById("divMovement").style.height = '380px';
			document.getElementById("ddimagebuttons").style.top = '395px';
			document.getElementById("divPrintHelpBar").style.top = '405px';
			setStyleElement(["movement.id.entityId","movement.valueDateAsString"])
			setStyleElement(["movement.amountAsString","movement.sign","movement.reference1"])
			setStyleElement(["movement.counterPartyId","movement.counterPartyText1","movement.matchingParty"])
			setStyleElement(["movement.productType","movement.postingDateAsString","movement.reference1"])
			
			document.getElementById("valueDateLink").style.display="none";
			
			document.getElementById("counterPartyLink").style.visibility = 'hidden';
			document.getElementById("matchingPartyLink").style.visibility = 'hidden';
			document.getElementById("postingDateLink").style.visibility = 'hidden';
			
			var radioMovType = document.forms[0].elements["movement.movementType"];
		    radioMovType[0].disabled = "true";
		    radioMovType[1].disabled = "true";
		    radioMovType[0].title = "";
		    radioMovType[1].title = "";
		    
		    var radioPredictStatus = document.forms[0].elements["movement.predictStatus"];
			radioPredictStatus[0].disabled = "true";
			radioPredictStatus[1].disabled = "true";
			radioPredictStatus[2].disabled = "true";
			radioPredictStatus[0].title = "";
			radioPredictStatus[1].title = "";
			radioPredictStatus[2].title = "";
			/*End: Code Modified For Mantis 1827 by Sudhakar on 26-09-2012:Pre Advice display should not accept more than 12 characters And Pre-advice should display cancel radio button in Predict Status*/
		}
		if ((screenIdentifier == "insert") || (valueDateEditStatus == "false")) {
			document.getElementById("valueMandatory").innerText = "*";
		}		
		if ((screenIdentifier == "insert") || (amountEditStatus == "false")) {
			document.getElementById("amtMandatory").innerText = "*";
		}	
		if ((screenIdentifier == "insert") || (accountEditStatus == "false")) {
			document.getElementById("acctMandatory").innerText = "*";
		}		
   }
  
    /**
 	 * setStyleElement
  	 * 			
 	 * This method is used to set disable property for given element.
 	 */
	function setStyleElement(elementArr) {
	    for (var i = 0; i < elementArr.length; i++) {
	        var element = document.getElementById(elementArr[i]);
	        if (element != null) {
	            element.style.background = 'transparent';
	            element.style.border = "thin";
	            element.readOnly = true;
	            element.disabled = '';
	            element.title = '';
	            element.tabIndex = -1;
	        }
	    }
	}
  
  	/**
 	 * bodyOnLoad
 	 * 			
 	 * This method is called when window onloading and used to set form values in the page and enable/disable the button
 	 */  
	function bodyOnLoad() {

		if (screenIdentifier == "insert" || screenIdentifier == "update") {
			changeBorderColor();
			$(document).on('change', '#accountDropDown', function () {
				changeBorderColor();
			});
			var entityBox = new SwSelectBox(document.forms[0].elements["movement.id.entityId"], document.getElementById("entityDesc"));
			var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.positionLevelAsString"], document.getElementById("positionName"));
			if (document.forms[0].elements['movement.amountAsString'].value == "") {
				document.forms[0].elements['movement.amountAsString'].style.borderColor = "red";
			}
		}
		if (screenIdentifier == "update") {
			document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("enableupdatebutton").innerHTML;
		}
		if (screenIdentifier == "insert") {
			if (menuAccessId == '0')
				document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("enablesavebutton").innerHTML;
			else
				document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("disablesavebutton").innerHTML;
		}
		<c:if test="${requestScope.save == 'true'}">
		alert("${requestScope.saveStatus}");
		if (menuAccessId == '0')
			document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("enablesavebutton").innerHTML;
		else
			document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("disablesavebutton").innerHTML;
		</c:if>
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		document.getElementById("counterPartyDesc").innerText = "${counterPartyDesc}";
		document.getElementById("matchingPartyDesc").innerText = "${matchingPartyDesc}";
		// Settings for opening UI in Update mode . Most of the elements has to be disabled
		if (screenIdentifier == "display") {
			var matchStatus = document.forms[0].elements['movement.matchStatus'].value;
			if (document.getElementById("enableopenbutton") != null) {
				if (matchStatus == "<%=SwtConstants.AUTHORISE_STATUS%>" || matchStatus == "<%=SwtConstants.REFERRED_STATUS%>" || matchStatus == "<%=SwtConstants.RECONCILE_STATUS%>") {
					<c:if test="${requestScope.openFlag != 'Y'}">
					document.getElementById("enableopenbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>';
					</c:if>
				}
			}
			var accessFlag = accountAccessConfirm(document.getElementById("movement.accountId").value, document.getElementById("movement.id.entityId").value);
			document.getElementById("accountDesc").innerText = "${accountDesc}";
			document.getElementById("bookCodeDesc").innerText = "${bookCodeDesc}";
			document.getElementById("entityDesc").innerText = "${entityDesc}";
			document.getElementById("counterPartyDesc").innerText = "${counterPartyDesc}";
			document.getElementById("matchingPartyDesc").innerText = "${matchingPartyDesc}";
			document.getElementById("positionName").innerText = "${requestScope.posLevelName}";
			if (accessFlag == "true") {
				document.getElementById("openbutton").innerHTML = document.getElementById("enableopenbutton").innerHTML;
				document.getElementById("changebutton").innerHTML = document.getElementById("enablechangebutton").innerHTML;
				<c:if test="${requestScope.deleteButton == 'true'}">
				document.getElementById("deletebutton").innerHTML = document.getElementById("enabledeletebutton").innerHTML;
				</c:if>
				<c:if test="${requestScope.deleteButton != 'true'}">
				document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
				</c:if>
			} else {
				document.getElementById("changebutton").innerHTML = document.getElementById("disablechangebutton").innerHTML;
				document.getElementById("openbutton").innerHTML = document.getElementById("disableopenbutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
			}
			document.getElementById("canclebutton").innerHTML = document.getElementById("enablecanclebutton").innerHTML;
			/*  Enable the xrefsbuttons. */
			document.getElementById("xrefsbutton").innerHTML = document.getElementById("enablexrefsbutton").innerHTML;
			document.forms[0].elements["movement.id.entityId"].title = document.forms[0].elements["movement.id.entityId"].value;
			document.forms[0].elements["movement.accountId"].title = document.forms[0].elements["movement.accountId"].value;
			document.forms[0].elements["movement.bookCode"].title = document.forms[0].elements["movement.bookCode"].value;
		}
		if (screenIdentifier == "search") {
			//Set the focus to the movement of text field in loading of screen
			this.window.focus();
			document.forms[0].elements["movement.id.movementId"].focus();

			document.getElementById("openbutton").innerHTML = document.getElementById("disableopenbutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
			if (document.forms[0].elements['movement.id.movementId'].value == "") {
				document.getElementById("changebutton").innerHTML = document.getElementById("disablechangebutton").innerHTML;
				document.getElementById("logbutton").innerHTML = document.getElementById("disablelogbutton").innerHTML;
				document.getElementById("notebutton").innerHTML = document.getElementById("disablenotebutton").innerHTML;
				document.getElementById("canclebutton").innerHTML = document.getElementById("disablecanclebutton").innerHTML;
				/*  Disable the xrefsbutton */
				document.getElementById("xrefsbutton").innerHTML = document.getElementById("disablexrefsbutton").innerHTML;
			} else {
				searchMovement(document.forms[0].elements['movement.id.movementId']);
			}


		}

		// Display search status message to the ui
		<c:if test="${requestScope.search == 'true'}">
			document.forms[0].elements['movement.id.movementId'].value = "";
		alert('<fmt:message key="${requestScope.searchStatus}"/>');
		</c:if>

		<c:if test="${requestScope.error == 'true'}">
			document.forms[0].elements['movement.id.movementId'].value = "";
		</c:if>

		<c:if test="${requestScope.hasNotes == 'Y'}">
			document.getElementById("notesIndicator").src = "images/notes.gif";
			document.getElementById("notesIndicator").title = '<fmt:message key="msg.title.notesAvailable"/>';
		</c:if>

		if (document.forms[0].elements['movement.id.movementId'] != null)
			enteredMovId = document.forms[0].elements['movement.id.movementId'].value;
		document.forms[0].openFlag.value = "${requestScope.openFlag}";
		if ((document.forms[0].elements["movement.id.movementId"] != null && document.forms[0].elements["movement.id.movementId"].value != "")
				&& screenIdentifier != "update") {
			var lockVal = checkLock();
			if (lockVal != "true") {
				disablebuttonsforlock(lockVal);
			}
		}
		if (currencyAccessInd == 1) {
			document.getElementById("changebutton").innerHTML = document.getElementById("disablechangebutton").innerHTML;
			document.getElementById("openbutton").innerHTML = document.getElementById("disableopenbutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
			document.getElementById("canclebutton").innerHTML = document.getElementById("disablecanclebutton").innerHTML;
		} else if (currencyAccessInd == 2) {
			document.getElementById("changebutton").innerHTML = document.getElementById("disablechangebutton").innerHTML;
			document.getElementById("openbutton").innerHTML = document.getElementById("disableopenbutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
			document.getElementById("logbutton").innerHTML = document.getElementById("disablelogbutton").innerHTML;
			document.getElementById("notebutton").innerHTML = document.getElementById("disablenotebutton").innerHTML;
			document.getElementById("canclebutton").innerHTML = document.getElementById("disablecanclebutton").innerHTML;
			document.getElementById("xrefsbutton").innerHTML = document.getElementById("disablexrefsbutton").innerHTML;
		}
	}


	/**
	 * accountAccess
	 *
	 * This method is called when select the account id on the account drop down and
	 * used to get the input access for selected account and based on the access it will
	 * allow to select the account otherwise display the alert meessage to user
	 */
	function accountAccess() {
		var accountId = document.forms[0].elements["movement.accountId"].value;
		if (accountId.trim() != "") {
			var entity = document.forms[0].elements["movement.id.entityId"].value;
			var accessFlag = accountAccessConfirm(accountId,entity);
			if (accessFlag == "false") {	
				alert('<fmt:message key="message.alert.acctAccessInput"/>');
				return false;
			} else {
				return true
			}
		} else {
			return false;
		}
	}
 
 	/**
 	 * accountAccessConfirm
 	 * 			
 	 * This method is used to get the input access for given account id from database
 	 */
	function accountAccessConfirm(accountId,entity) {
		var oXMLHTTP = new XMLHttpRequest();
		var acctAccessURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";
		acctAccessURL = acctAccessURL + "&accountId="+accountId;
		acctAccessURL = acctAccessURL + "&entityId="+entity;
		acctAccessURL = acctAccessURL + "&status=Input";
		oXMLHTTP.open( "POST", acctAccessURL, false );
		oXMLHTTP.send();
		var acctAccessVal=oXMLHTTP.responseText;
		return acctAccessVal;
	}	

    /**
 	 * submitFormButton
 	 * 			
 	 * This Function called on submission of the page and Sets the mandatory parameters of the form. 
 	 * This method is only called when we submit the form from button click
 	 */
	function submitFormButton(methodName) {
		if(validateField(document.forms[0].elements['movement.valueDateAsString'],'valueDateAsString',dateFormat)) {
			if(validateField(document.forms[0].elements['movement.postingDateAsString'],'postingDateAsString',dateFormat)) {
				var amount = validateCurrency(document.forms[0].elements['movement.amountAsString'],'movement.amountAsString',currencyFormat,document.forms[0].elements['movement.currencyCode'].value);
				if(amount) {
					if(validateForm(document.forms[0]) ) {
				   		if (document.forms[0].elements['movement.valueDateAsString'].value != "") {
				   			if(screenIdentifier == "insert")
				   				document.forms[0].displayPreadvice.value = "";
				   			if(screenIdentifier == "update") {
				   				document.forms[0].displayPreadvice.value = "true";
								document.forms[0].movementId.value = document.forms[0].elements['movement.id.movementId'].value;
				   			}		
							document.forms[0].currencyCode.value = document.forms[0].elements['movement.currencyCode'].value;
							document.forms[0].method.value = methodName;
							if(accountAccess()){
								 elementTrim(document.forms[0]);
			                      clearMatchingPartyDesc();
			                       clearCounterPartyDesc();
							// if authorization in the role is yes, alert message appears when update the preadvice movement
								if(screenIdentifier == "update") {
									var authorizeStatus = "${requestScope.authorizeStatus}";
									if (authorizeStatus == 'Y')
										alert('<fmt:message key="preadvice.movetoauthorise"/>');
									enableFields();		
								}
								if(methodName == "save"){
									document.forms[0].elements['movement.positionLevelAsString'].disabled = "";
									document.getElementById("saveorupdatebutton").innerHTML = document.getElementById("disablesavebutton").innerHTML;
								}
								document.forms[0].submit();
							}
						} else {
							alert('<fmt:message key="alert.acctbreakdownmonitor.date"/>');
							document.forms[0].elements['movement.valueDateAsString'].focus();
							return false;
						}
	 				}
	 			} else {
			 		document.forms[0].elements['movement.amountAsString'].focus();
	 			}
			} else {
				document.forms[0].elements['movement.postingDateAsString'].focus();
		 	}
		} else {
		 	document.forms[0].elements['movement.valueDateAsString'].focus();
		}
	}

    /**
 	 * submitForm
 	 * 			
 	 * This Function called on submission of the page and sets the mandatory parameters of the form. 
 	 * This method is only called when we submit the form by changing the drop down boxes for entity and do the cancel operation.
 	 */
	function submitForm(methodName)	{
		if(screenIdentifier == "insert")
			document.forms[0].displayPreadvice.value = "";
		if(screenIdentifier == "update" || screenIdentifier == "display") {	
			document.forms[0].displayPreadvice.value = "true";
			document.forms[0].movementId.value = document.forms[0].elements['movement.id.movementId'].value;
		}	
		document.forms[0].currencyCode.value = document.forms[0].elements['movement.currencyCode'].value;
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}


    /**
 	 * submitFormEntity
 	 * 			
 	 * This Function called on submition of the page and Sets the mandatory parameters of the form. 
 	 * This method is only called when we submit the form by changing the drop down boxes values movement types and entity value
 	 */
	function submitFormEntity(methodName) {
		if(screenIdentifier == "insert")
			document.forms[0].displayPreadvice.value="";
		if(screenIdentifier == "update") {
			document.forms[0].displayPreadvice.value	=	"true";
			document.forms[0].movementId.value = document.forms[0].elements['movement.id.movementId'].value;
		}
		document.forms[0].currencyCode.value = '';
		document.forms[0].entityChanged.value = 'true';
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
 
    /**
 	 * onCancle
 	 * 			
 	 * This Function called on while clicking the cancel button on pre advice input / update screen to get the form with empty details.
 	 */
	function onCancle() {
		releaseLock();
		// Refresh the Screen with reset values
		if(screenIdentifier == "insert") {
			document.forms[0].cancleStatus.value = "true";
			submitForm("display");
		}
		// Go back to the Search screen that is PreAdvice Display Screen
		if(screenIdentifier == "update") {
			//document.forms[0].elements['movement.id.movementId'].value = "0"
			submitForm("search");
		}
	}

	/**
	 * releaseLock
	 *
	 * This function is used to release lock for given movement.
	 * once user done changes in the movement then it should get the release from lock
	 **/
	function releaseLock() {
		if(document.forms[0].elements["movement.id.movementId"] != null &&
				document.forms[0].elements["movement.id.movementId"].value !="") {


			beaconUnlock(document.forms[0].elements["movement.id.movementId"].value);

		}
	}

	// For page unload cases
	function beaconUnlock(movementIds) {
		try {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

			// Create the data to be sent
			const formData = new FormData();
			formData.append('movementIds', movementIds);
			formData.append('method', 'unlockMovements');

			// Send using beacon API
			const success = navigator.sendBeacon(sURL, formData);
			return success ? "success" : "error";
		} catch (error) {
			console.error('Error during beacon unlock:', error);
			return "error";
		}
	}

	/**
 	 * buildMovementFrmSearch
     * 			
     * This method is called while clicking the notes button in the pre advice search screen and 
     * used to show the notes details for movement   
     */
	function buildMovementFrmSearch(methodName,disBtnLock) {
		var notesURL = 'notes.do?method='+methodName+'&movId=';			 
		notesURL += document.forms[0].elements['movement.id.movementId'].value;
		notesURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
		notesURL += '&screenName=' + 'manualInputScreenViewMode';
		notesURL += '&currencyAccess=' + currencyAccessInd;
		notesURL += '&unLock=true'
		notesURL += '&disBtnLock='+disBtnLock;
		return  notesURL;
	}

	/**
	 * openNotes
	 *
	 * This method is called when click on the notes button in the pre advice display screen 
	 * and used to open movement notes window to display the notes details for movement 
	 **/
	function openNotes() {
		if (validateMovementId()) {
			var lockVal=checkLock();
			if (lockVal=="true") {
				lockVal=acquireLock();
				openWindow(buildMovementFrmSearch('notes','N'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
			} else {
				openWindow(buildMovementFrmSearch('notes','Y'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');		
			}
		}
	}

	/**
	 * clickOpenButton
	 *
	 * This method is used to set the open status for movement while clicking open button on pre advice display 
	 **/
	function clickOpenButton(methodName) {
		if(validateMovementId()) {
	 		var lockVal=checkLock();
	 		if (lockVal=="true") {
	 			acquireLock();
				if(document.forms[0].openFlag.value == 'Y') {
			    	var confirmState = window.confirm('<fmt:message key="confirm.unopen"/>');	
				} else {
		    		var confirmState = window.confirm('<fmt:message key="confirm.open"/>');
				}
				if (confirmState == true) { 
			    	document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementId"].value
		        	document.forms[0].method.value = "updateOpenFlag";
					document.forms[0].submit();
				}
		  		releaseLock();	
	  		} else {
		 		disablebuttonsforlock(lockVal);
	 		}
		}
	}



  	/**
 	 * cancleMovement
 	 * 			
 	 * This method is called while clicking on cancel button on pre advice search screen to get the empty form details.
 	 */
	function cancleMovement(methodName) {
		document.forms[0].method.value = methodName;
		document.forms[0].movementId.value = "0";
		document.forms[0].submit();
	}

    /**
 	 * searchMovement
 	 * 			
 	 * This method is invoked while pressing enter key on movement textbox and validate the entered movement id and 
 	 * doing form submit to get the movement details  
 	 * @Param  element
 	 */
	function searchMovement(element,e) {
 		var event = (window.event|| e); 
		if((movementAmendedFlag == true)|| (event && event.keyCode == 13) || (event && event.keyCode == 9)) {
			if((movementAmendedFlag == true)|| (validateField(element,'movementId','numberPat') == true)) {
				document.forms[0].movementId.value = document.forms[0].elements['movement.id.movementId'].value;
				submitForm("search");
			} else {
				document.forms[0].elements['movement.id.movementId'].value = "";
				//calling the emptyMovementDetails method to reload the pre display screen
				emptyMovementDetails('search');
			}
		}
	}
	/**
	 *
	 * This method is used to delete the movement while clicking delete button on pre advice display 
	 *@param methodName
	 **/
	function onDelete(methodName) {
	    //Validate the movement id is available in db
		if (validateMovementId()) {
		 	var lockVal=checkLock();
				//validate the movement id is locked
				if (lockVal=="true") {
					acquireLock();
					var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
					if (yourstate==true) { 
						releaseLock();			
						submitForm(methodName);
					} else {
						releaseLock();
					}
				} else {
					disablebuttonsforlock(lockVal);
				}
		}
	}


    /**
  	 * updateMovement
 	 * 			
 	 * This function is called when click on change button in the pre advice display screen and used to change the details for entered movement 
 	 */
	function updateMovement(methodName) {
		if(validateMovementId()) {
			document.forms[0].method.value = methodName;
			document.forms[0].movementId.value = document.forms[0].elements['movement.id.movementId'].value;
			document.forms[0].currencyCode.value = document.forms[0].elements['movement.currencyCode'].value;
			var lockedUser=checkLock();
			if (lockedUser=="true") {	
				acquireLock();
				document.forms[0].submit();
			} else {
				document.forms[0].method.value = "search";
				disablebuttonsforlock(lockedUser);	 	
			}
	 	}
	}

	/**
	 * checkLock
	 *
	 * This function is used to check the lock for given movement.
	 * if movement is used by some other user then return that user id otherwise return true  
	 **/
	function checkLock() {
		var lockVal="true";
		var chkLockURL = requestURL + appName+"/movementLock.do?method=checkLock";		    
		chkLockURL = chkLockURL + "&movementId="+document.forms[0].elements['movement.id.movementId'].value;	
		var oXMLHTTP =  new XMLHttpRequest();
		oXMLHTTP.open( "POST", chkLockURL, false );
		oXMLHTTP.send();
		lockVal=new String(oXMLHTTP.responseText);
		return lockVal;   
	}

	/**
	 * acquireLock
	 *
	 * This function is used to acquire lock for given movement.
	 * If user going to access the movement then this function provide lock for that movement with specified user 
	 *   
	 **/
	function acquireLock() {
		var lockVal="true";
		var lockMovURL = requestURL + appName+"/movementLock.do?method=lockMovement";		    
		lockMovURL = lockMovURL + "&movementId="+document.forms[0].elements["movement.id.movementId"].value;
		var oXMLHTTP =  new XMLHttpRequest();
		oXMLHTTP.open( "POST", lockMovURL, false );
		oXMLHTTP.send();
		lockVal=new String(oXMLHTTP.responseText);
		return lockVal;   
	}

    /**
 	 * disablebuttonsforlock
 	 * 			
 	 * This function is used to disable the change,open,unoopen,delete button when entered movement is in lock
 	 */
	function disablebuttonsforlock(lockUser) {
		alert('<fmt:message key="alert.MovementInUse"/>' + lockUser + ' ' + '<fmt:message key="alert.Viewonlyaccess"/>');
		document.getElementById("changebutton").innerHTML = document.getElementById("disablechangebutton").innerHTML;
		<c:if test="${requestScope.openFlag == 'Y'}">
			document.getElementById("openbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Unopen"/></a>';
		</c:if>

		<c:if test="${requestScope.openFlag != 'Y'}">
			document.getElementById("openbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>';
		</c:if>
		document.getElementById("deletebutton").innerHTML = document.getElementById("disabledeletebutton").innerHTML;
	}

	/**
	 * validateMovementId
	 *
	 * This function is used to validate the movement if entered movement is not
	 * in the database then display the alet msg to user
	 **/
	function validateMovementId() {
		if (validateField(document.forms[0].elements["movement.id.movementId"], 'movementId', 'numberPat')) {
			//Check the movement Id is available in the table
			if (document.forms[0].elements["movement.id.movementId"].value.trim() == "") {
				alert('<fmt:message key="preadvice.alert.notfound"/>');
				 //calling the emptyMovementDetails method to reload the pre display screen 
				emptyMovementDetails('search');
				return false;
			} else {
				//Frame the Ajax request url
				var oXMLHTTP = new XMLHttpRequest();	
				var chkMovURL=requestURL + appName+"/preadviceinput.do?method=checkMovementId";
				chkMovURL = chkMovURL + "&movId="+document.forms[0].elements["movement.id.movementId"].value;	
				oXMLHTTP.open( "POST", chkMovURL, false );
				oXMLHTTP.send();
				var lockVal=new String(oXMLHTTP.responseText);
				if (lockVal == "false"  || lockVal == "null") {
					alert('<fmt:message key="preadvice.alert.notfound"/>');
					//calling the emptyMovementDetails method to reload the pre display screen
					emptyMovementDetails('search');
					return false;
				}
			 // Start:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre advice display:Pre-advice Display should not accept more than 12 characters for Movement Id	
				else if(enteredMovId != null && enteredMovId != removeLeadingZeros(document.forms[0].elements["movement.id.movementId"].value)) {
					alert('<fmt:message key="manualInput.alert.mvmIdamended"/>');
					movementAmendedFlag = true;
					searchMovement('search');
					movementAmendedFlag = false;
					return false;
				} 
		    // End:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre advice display:Pre-advice Display should not accept more than 12 characters for Movement Id			
				else {
					return true;
				}
			}
		}
		//calling the emptyMovementDetails method to reload the pre display screen
		emptyMovementDetails('search');
	}
    // Start:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre advice display:Pre-advice Display should not accept more than 12 characters for Movement Id	
	/** 
	*
	* This method is used to remove leading Zeros from entered movement Id
	* @param movementId
	*/
	function removeLeadingZeros(movementId) {
		return movementId.replace(/^[0]+/g,"");
	}
    // End:code modified by Prasenjit Maji for Mantis 1827 on 25/09/2012: Pre advice display:Pre-advice Display should not accept more than 12 characters for Movement Id
	/** 
	*
	* This method is used to submit empty form for reloading preadvice display screen when movement id is invalid
	* @param method
	*/
	function emptyMovementDetails(method){
		 document.forms[0].method.value = method;
		 //Submitting Form to empty the movement details when moventId is invalid
		 document.forms[0].submit();
	}
	
	/**
	 * openCrossRef
	 *
	 * This function is used to open the Cross refrence display screen and 
	 * used to display the cross reference for corresponding movement
	 **/
	function openCrossRef() {
		if (validateMovementId()) {
			//framing URL for th erequest
			var crossRefURL = 'movement.do?method=crossReference&movId=';			 
			crossRefURL += document.forms[0].elements['movement.id.movementId'].value;
			crossRefURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
			crossRefURL += '&screenName=' + 'manualInputScreenViewMode';
			//opening the new window Cross Referenc Display
			openWindow(crossRefURL,'movementNotesWindow','left=170,top=210,width=987,height=405,toolbar=0, resizable=yes, scrollbars=yes');
		}
	}

	/**
	 * openLog
	 *
	 * This function is used to open the movement log screen and 
	 * used to display the log details for corresponding movement
	 **/
	function openLog() {
		if (validateMovementId()) {
			openWindow(movementLog('movementLog'),'movementLogWindow','left=50,top=190,width=1240,height=555,toolbar=0, resizable=yes, scrollbars=yes','true')
		}
	}

	/**
 	  * getAccountList
      * 			
      * This method is used to get the collection of account list while 
      * clicking the account drop down button and populate the collection in the drop down box  
      */
	function getAccountList(event) {
	    if (accountClk) {
	        //get the all elements for selectbox
	        var divElement = document.getElementById("dropdowndiv_2");
	        var idElement = document.forms[0].elements["accountId"];
	        var descElement = document.forms[0].elements["accountDesc"];
	        var arrowElement = document.forms[0].elements["dropdownbutton_2"];
	        acctSelectElement = document.forms[0].elements["movement.accountId"];
	        var movType;
	        var radioMovType = document.forms[0].elements["movement.movementType"];
	        if (radioMovType[0].checked == true) {
	            movType = radioMovType[0].value;
	        } else {
	            movType = radioMovType[1].value;
	        }
	        var accountURL = requestURL + appName + "/movement.do?method=getAccountsList";
	        accountURL = accountURL + "&entityId=" + document.forms[0].elements["movement.id.entityId"].value;
	        accountURL = accountURL + "&currencyCode=" + document.forms[0].elements["movement.currencyCode"].value;
	        accountURL = accountURL + "&movType=" + movType;
	        var oXMLHTTP =  new XMLHttpRequest();
	        oXMLHTTP.open("POST", accountURL, false);
	        //send the request for list
	        oXMLHTTP.send();
	        //get the response text
	        var listValue = new String(oXMLHTTP.responseText);
	        listValue = listValue.split('\n');
	        if (acctSelectElement.options.length > 0) {
	            acctSelectElement.remove(0);
	        }
	        var index = 0;
	        //add the main account into option
	        for (var i = 0; i < listValue.length - 1; i++) {
	            var lblValue = listValue[i].split('~~~');
	            var optionElement = document.createElement("option");
	            optionElement.text = lblValue[0];
	            optionElement.value = lblValue[1];
	            if (optionElement.value == accountId && (idElement.value.length > 0)) index = i;
	            acctSelectElement.options.add(optionElement);
	        }
	        //set the selected index
	        acctSelectElement.selectedIndex = index;
	        //frame the selectbox component
	        acctSwSelectBox = new SwMainSelectBox(divElement, acctSelectElement, idElement, descElement, arrowElement, 12, 30);
	        acctSwSelectBox.setClickFlag();
	        //call to populate the list in the list box
	        acctSwSelectBox.arrowElementOnClick(event);
	        accountClk = false;
	    }
	}
	
	/**
 	  * getBookCodeList
      * 			
      * This method is used to get the collection of book code list while 
      * clicking the book code drop down button and populate the collection in the drop down box   
      */
	function getBookCodeList(event) {
	    if (bookCodeClk) {
	        //get the all elements for selectbox
	        var divElement = document.getElementById("dropdowndiv_3");
	        var idElement = document.forms[0].elements["bookCode"];
	        var descElement = document.forms[0].elements["bookCodeDesc"];
	        var arrowElement = document.forms[0].elements["dropdownbutton_3"];
	        bookCodeSelElement = document.forms[0].elements["movement.bookCode"];
	        var bookCodeURL = requestURL + appName + "/movement.do?method=getBookCodeList";
	        bookCodeURL = bookCodeURL + "&entityId=" + document.forms[0].elements["movement.id.entityId"].value;
	        var oXMLHTTP =  new XMLHttpRequest();
	        oXMLHTTP.open("POST", bookCodeURL, false);
	        //send the request for list
	        oXMLHTTP.send();
	        //get the response text
	        var listValue = new String(oXMLHTTP.responseText);
	        listValue = listValue.split('\n');
	        if (bookCodeSelElement.options.length > 0) {
	            bookCodeSelElement.remove(0);
	        }
	        var index = 0;
	        //add the main account into option
	        for (var i = 0; i < listValue.length - 1; i++) {
	            var lblValue = listValue[i].split('~~~');
	            var optionElement = document.createElement("option");
	            optionElement.text = lblValue[0];
	            optionElement.value = lblValue[1];
	            if (optionElement.value == bookCodeId) index = i;
	            bookCodeSelElement.options.add(optionElement);
	        }
	        //set the selected index
	        bookCodeSelElement.selectedIndex = index;
	        //frame the selectbox component
	        bookCodeSwSelBox = new SwMainSelectBox(divElement, bookCodeSelElement, idElement, descElement, arrowElement, 12, 30);
	        bookCodeSwSelBox.setClickFlag();
	        //call to populate the list in the list box
	        bookCodeSwSelBox.arrowElementOnClick(event);
 	        bookCodeClk = false;
	    }
	}
	
	/**
 	  * onChangeCurrencyOrType
      * 			
      * This method is called while changing the currency code/movement type to empty the collection in list box.  
      */
	function onChangeCurrencyOrType() {
	    accountClk = true;
	    //clear the account list and value
	    if (acctSelectElement != null && acctSelectElement.options != null) acctSelectElement.options.length = 0;
	    if (acctSwSelectBox != null) {
	        acctSwSelectBox.arrowElement.onclick = getAccountList;
	    }
	    // Change decimal format for the value based on selected currency
	    if(document.forms[0].elements['movement.amountAsString'].value!="")
	    validateCurrency(document.forms[0].elements['movement.amountAsString'],'movement.amountAsString',currencyFormat,document.forms[0].elements['movement.currencyCode'].value);
		
	    document.forms[0].elements["accountId"].value = "";
	    document.forms[0].elements["accountDesc"].value = "";
	    //clear the book code list and value
	    if (bookCodeSelElement != null && bookCodeSelElement.options != null) bookCodeSelElement.selectedIndex = 0;
	    if (bookCodeSwSelBox != null) {
	        bookCodeSwSelBox.idElement.value = "";
	        bookCodeSwSelBox.descElement.value = "";
	    }
	}
	 function changeBorderColor() {
		 if (screenIdentifier=="insert" || screenIdentifier=="update"){
			 
				 var element_ = document.getElementById('accountId');
				 if(element_!=null&&typeof element_!='undefined'){
				         element_.style.border = element_.value==""?"red 1px solid":"#a9a9a9 1px solid";
				 }
		          
			  	 var elementAmount= document.forms[0].elements['movement.amountAsString'];
			 	 if(elementAmount!=null&&typeof elementAmount!='undefined')
				   elementAmount.style.borderColor = elementAmount.value==""?"red":"";
					 
			 	 var elementDateValue=document.forms[0].elements['movement.valueDateAsString'];
			  	 if(elementDateValue!=null&&typeof elementDateValue!='undefined')
			    	 elementDateValue.style.borderColor = elementDateValue.value==""?"red":"";	    
		 } 
	    }
	
</SCRIPT>
</head>
<form action="preadviceinput.do" method="post">
	<%
	if (request.getSession().getAttribute(org.swallow.util.struts.StrutsConstants.TOKEN_NAME_FIELD) != null
				&& screenIdentifier.equals("insert")) {
		String tokenValue = ""+request.getSession().getAttribute(org.swallow.util.struts.StrutsConstants.TOKEN_NAME_FIELD);
	%>
	<input type="hidden" name="<%=org.swallow.util.struts.StrutsConstants.TOKEN_NAME_FIELD%>"
		value="<%=org.swallow.util.struts.StrutsConstants.DEFAULT_TOKEN_NAME%>">
	<input type="hidden" name="<%=org.swallow.util.struts.StrutsConstants.DEFAULT_TOKEN_NAME%>"
		value="<%=tokenValue%>">
	<%
		}
	%>
	<input name="method" type="hidden" value="display">
	<input name="isNotesPresent" type="hidden" value="">
	<input name="notesScreenOpened" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">
	<input name="entityChanged" type="hidden" value="">
	<input name="displayPreadvice" type="hidden" value="">
	<input name="movementId" type="hidden" value="">
	<input name="cancleStatus" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<input name="openFlag" type="hidden">
	<input name="noOfNotes" type="hidden" value="">

	<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
		marginheight="0" onunload="bodyUnLoad('clearSession')">
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>
	<%
		if (screenIdentifier.equals("insert")
				|| screenIdentifier.equals("update")) {
	%>
	<div id="dropdowndiv_2"
		style="position: absolute; width: 165px; left: 135px; top: 160px; visibility: hidden; z-index: 99"
		class="swdropdown">
			<select id="accountDropDown" class="htmlTextFixed" name="movement.accountId" size="10" style="width:329px;z-index:99" onclick="accountAccess()">
				<c:forEach var="account" items="${requestScope.accounts}">
					<option value="${account.value}" ${account.value == movement.accountId ? 'selected="selected"' : ''}>${account.label}</option>
				</c:forEach>
			</select>
		</div>

		<div id="dropdowndiv_3"
				style="position: absolute; width: 165px; left: 135px; top: 186px; visibility: hidden; z-index: 99"
				class="swdropdown">
			<select class="htmlTextFixed" id="movement.bookCode" name="movement.bookCode" size="10" style="width:256px;z-index:99">
				<c:forEach var="book" items="${requestScope.books}">
					<option value="${book.value}" ${book.value == movement.bookCode ? 'selected="selected"' : ''}>${book.label}</option>
				</c:forEach>
			</select>
		</div>

	<%
		}
	%>
	<div id="divMovement"
		style="position: absolute; left: 10px; top: 8px; width: 915px; height: 352px; border: 2px outset;"
		color="#7E97AF">
	<div id="MovementDisplay"
		style="position: absolute; left: 8px; top: 4px; width: 905px; height: 250px;">
	<table width="813px" border="0" cellpadding="0" cellspacing="1"
		height="23">
		<tr height="23px">
			<td width="118px"><b>&nbsp;<fmt:message key="entity.id" /><span
				id="entityMandatory" /></b></td>
			<td width="120px">
			<%
				if (screenIdentifier.equals("insert")
						|| screenIdentifier.equals("update")) {
			%> <select tabindex="1" id="movement.id.entityId" name="movement.id.entityId" titleKey="tooltip.selectEntity" onchange="submitFormEntity('display');" style="width:140px">
			<c:forEach var="entity" items="${requestScope.entities}">
				<option value="${entity.value}" ${entity.value == movement.id.entityId ? 'selected="selected"' : ''}>${entity.label}</option>
			</c:forEach>
		</select>
 <%
 	} else {
 %> <input type="text" name="movement.id.entityId" class="htmlTextAlpha" readonly="readonly"
		   style="width:140px;background:transparent;border:thin;"
		   value="${movement.id.entityId}" /><%
 	}
 %>
			</td>
			<td width="18px">&nbsp;</td>
			<td width="549px"><span id="entityDesc" name="entityDesc"
				class="spantext" style="width: 380px;"></td>
		</tr>
	</table>

	<%
		if (!screenIdentifier.equals("insert")) {
	%>
	<table width="573px" border="0" cellpadding="0" cellspacing="1"
		height="23">
		<tr height="23px">
			<td width="118px"><b>&nbsp;<fmt:message key="label.preadvice.movement" /></b></td>
			<!-- Start:code modified by Prasenjit Maji for Mantis 1827 on 18/09/12: Pre-advice Display should not accept more than 12 characters for Movement Id -->
			<td width="120px">
				<input type="text" name="movement.id.movementId" maxlength="12"
					   titleKey="tooltip.enterMvmId"
					   class="htmlTextNumeric" onkeydown="searchMovement(this,event);"
					   style="width:120px" tabindex="2"
					   value="${movement.id.movementId}" />
			</td>
			<td width="39px">&nbsp;</td>
			<td width="280px">
				<b>
					<input type="text" name="movement.matchStatusDesc"
						   class="htmlTextAlpha" readonly="readonly"
						   style="width:280px;background:transparent;color:green;border:thin;"
						   value="${movement.matchStatusDesc}" />
				</b>
			</td>

			<c:choose>
				<c:when test="${requestScope.hasNotes == 'Y'}">
					<td width="20px"><img id="notesIndicator" src="images/notes.gif" border="0" title=""></td>
				</c:when>
				<c:otherwise>
					<td width="20px"><img id="notesIndicator" src="images/blank.png" border="0" title=""></td>
				</c:otherwise>
			</c:choose>

		</tr>
	</table>
	<%
		}
	%>
	<table width="530" border="0" cellpadding="0" cellspacing="1"
		height="10">
		<tr height="24px">
			<td width="100px"><b>&nbsp;<fmt:message key="movement.positionLevel" /></b></td>
			<td width="7px">&nbsp;</td>
			<td width="40px">
			<%
				if (screenIdentifier.equals("insert")
						|| screenIdentifier.equals("update")) {
			%> <select class="htmlTextAlpha" tabindex="17" titleKey="tooltip.selectPosLevel"
        id="movement.positionLevelAsString" name="movement.positionLevelAsString"
        disabled="disabled" style="width:40px;">
			<c:forEach var="positionLevel" items="${requestScope.positionLevelList}">
				<option value="${positionLevel.value}"
					<c:if test="${positionLevel.value == movement.positionLevelAsString}">selected="selected"</c:if>>
					${positionLevel.label}
				</option>
			</c:forEach>
		</select>
 <%
 	} else {
 %><input type="text" class="textlabel1" maxlength="16" title=""
		  name="movement.positionLevelAsString"
		  value="${movement.positionLevelAsString}"
		  style="width:40px;text-align:left" />
				<%
 	}
 %>
			</td>
			<td width="110px">&nbsp;</td>
			<td width="243"><span id="positionName" name="positionName"
				class="spantext" style="width: 130px;"></td>
		</tr>
	</table>
	<table width="415px" border="0" cellpadding="0" cellspacing="1">
		<tr height="23px">
			<td width="88px"><b>&nbsp;<fmt:message key="movement.valueDate" /><span id="valueMandatory" /></b></td>
			<td width="26px">&nbsp;</td>
			<td width="130px" style="padding-left: 5px"><input type="text" class="htmlTextAlpha" maxlength="10" tabindex="3"
															   name="movement.valueDateAsString" value="${movement.valueDateAsString}"
															   style="width:80px;margin-bottom: 5px;height: 20px;"
															   onchange="if(validateField(this,'movement.valueDateAsString',dateFormat)){return changeBorderColor();}" />

				<A title='<fmt:message key="tooltip.clickValueDate"/>'
				name="valueDateLink" onKeyDown="submitEnter(this,event)"
				ID="valueDateLink" tabindex="4"
				onClick="cal.select(document.forms[0].elements['movement.valueDateAsString'],'valueDateLink',dateFormatValue);dateSelected=true;return false;"><img
				src="images/calendar-16.gif" /></A></td>
			<%if ( SwtConstants.YES.equals(request.getAttribute("openFlag"))	) {	%>
				<td width="53px">&nbsp;</td>
				<td width="147px" readonly="true"
					style="width: 147px; background: transparent; color: green; border: thin;">
				<fmt:message key="movementDisplay.openStatus" /></td>
			<%} else {%>
				<td width="200px">&nbsp;</td>
			<%}%>
		</tr>
	</table>
	<table width="402px" border="0" cellpadding="0" cellspacing="1">
		<tr height="22px">
			<td width="88px"><b>&nbsp;<fmt:message key="movementDisplay.amount" /><span id="amtMandatory" /></b></td>
			<td width="20px">&nbsp;</td>
			<td width="54px">
			<%
				if (screenIdentifier.equals("insert")
						|| screenIdentifier.equals("update")) {
			%> <select style="width:54px;" tabindex="5" class="htmlTextAlpha" titleKey="tooltip.selectCurrencyCode"
        id="movement.currencyCode" name="movement.currencyCode"
        disabled="${attr.screenFieldsStatus}" onchange="onChangeCurrencyOrType()">
    <c:forEach var="currency" items="${requestScope.currencies}">
        <option value="${currency.value}" ${currency.value == movement.currencyCode ? 'selected="selected"' : ''}>
            ${currency.label}
        </option>
    </c:forEach>
</select>
 <%
 	} else {
 %> <input type="text" name="movement.currencyCode"
		   class="htmlTextAlpha" readonly="readonly"
		   style="width:54px;background:transparent;border:thin;"
		   value="${movement.currencyCode}" />
				<%
 	}
 %>
			</td>
			<td width="10px">&nbsp;</td>
			<td width="165px" ><input type="text" class="htmlTextNumeric"
									  tabindex="6" maxlength="28" name="movement.amountAsString"
									  onchange="if(validateCurrency(this,'movement.amountAsString',currencyFormat,document.forms[0].elements['movement.currencyCode'].value)){return changeBorderColor();}"
									  titleKey="tooltip.enterAmount"
									  readonly="${screenFieldsStatus}" style="width:165px;height:22px"
									  value="${movement.amountAsString}" />

			</td>
			<td width="10px">&nbsp;</td>
			<td width="35px">
			<%
				if (screenIdentifier.equals("insert")
						|| amountEditStatus.equals("false")) {
			%> <select id="movement.sign" name="movement.sign" class="htmlTextAlpha" tabindex="7" titleKey="tooltip.selectAmountSign" disabled="${screenFieldsStatus}" style="width:35px;">
				<c:forEach var="sign" items="${requestScope.signList}">
					<option value="${sign.value}" ${sign.value == movement.sign ? 'selected="selected"' : ''}>${sign.label}</option>
				</c:forEach>
			</select>
 <%
 	} else {
 %> <input type="text" class="htmlTextAlpha" readonly="readonly"  name="movement.sign" disabled="true" style="width:35px;height:22px" value="${movement.sign}" />
				<%
 	}
 %>
			</td>
		</tr>
	</table>
	<table width="550px" border="0" cellspacing="1" cellpadding="0">
		<tr height="18px">
			<td width="92px"><b>&nbsp;<fmt:message key="movement.movementType" /></b></td>
			<td width="150px">
				<input type="radio" id="1" style="width:13px" tabindex="8" titleKey="tooltip.preadvice.cash"
					name="movement.movementType" onclick="onChangeCurrencyOrType()" value="C"
					${movement.movementType == 'C' ? 'checked="checked"' : ''} />
				<label for="1"><fmt:message key="movementDisplay.cash" />&nbsp;&nbsp;&nbsp;</label>

				<input type="radio" id="2" tabindex="9" name="movement.movementType" titleKey="tooltip.preadvice.securities"
					value="U" onclick="onChangeCurrencyOrType()"
					${movement.movementType == 'U' ? 'checked="checked"' : ''} />
				<label for="2"><fmt:message key="movementDisplay.securities" /></label>
			</td>
			<td width="20px">&nbsp;</td>
			<td width="140px">&nbsp;</td>
			<td width="15px">&nbsp;</td>
			<td width="30px">&nbsp;</td>
		</tr>

	</table>
	<table width="835px" border="0" height="50px" cellspacing="1"
		cellpadding="0">
		<tr height="25px">
			<td width="115px"><b>&nbsp;<fmt:message key="movement.accountId" /><span id="acctMandatory" /></b></td>
			<%
				if (screenIdentifier.equals("insert")
						|| screenIdentifier.equals("update")) {
			%>
			<td width="20px">&nbsp;</td>
			<td width="280px"><input styleclass="textAlpha" name="accountId" id="accountId"
				 style="width: 220px;border-color: red; border-style:solid; height: 22px;" readonly="readonly" 
				title='<fmt:message key="tooltip.accountId" />'> <input
				tabindex="10" id="dropdownbutton_2" style="margin-bottom:-1px"
				title='<fmt:message key="tooltip.movAccount"/>' type="button"
				value="..." onclick="getAccountList(event)"></td>
			<%
				} else {
			%>
			<td width="20px">&nbsp;</td>
			<td width="280px"><input type="text" name="movement.accountId"
									 class="htmlTextAlpha" readonly="readonly"
									 style="width: 270px; background: transparent; border: thin;"
									 value="${movement.accountId}" />
			</td>
			<%
				}
			%>

			<td width="325px"><input class="textAlpha" value="${accountDesc}" 
				style="width: 325px; background: transparent; border: thin;"
				readonly name="accountDesc" size="20"  onpropertychange="changeBorderColor()"/></td>
		</tr>
		<tr height="25px">
			<td><b>&nbsp;<fmt:message key="movement.bookCode" /></b></td>
			<%
				if (screenIdentifier.equals("insert")
						|| screenIdentifier.equals("update")) {
			%>
			<td>&nbsp;</td>
			<td><input styleclass="textAlpha" name="bookCode" readonly="readonly"
				style="width: 100px;height: 22px;" title='<fmt:message key="tooltip.bookCode"/>'>
			<input tabindex="11" id="dropdownbutton_3"
				title='<fmt:message key="tooltip.clickSelBookId"/>' type="button" style="margin-bottom:-1px"
				value="..." onclick="getBookCodeList(event)">
			<td width="540"><input class="textAlpha" value="${bookCodeDesc}"
				style="width: 325px; background: transparent; border: thin;"
				readonly name="bookCodeDesc"></td>
			<%
				} else {
			%>
			<td width="28px">&nbsp;</td>
			<td width="120px"><input type="text" name="movement.bookCode"
									 class="htmlTextAlpha" readonly="readonly"
									 style="width:230px; background: transparent; border: thin;"
									 value="${movement.bookCode}" /></td>
			<td width="540"><input class="textAlpha" value="${bookCodeDesc}"
				style="width: 325px; background: transparent; border: thin;"
				readonly="readonly"  name="bookCodeDesc" size="20"></td>
			<%
				}
			%>
		</tr>
		<tr height="22px">
			<td><b>&nbsp;<fmt:message key="preadviceInput.reference" /></b>
			</td>
			<td>&nbsp;</td>
			<td><input type="text" class="htmlTextAlpha" tabindex="12"
					   maxlength="16" titleKey="tooltip.enterMvmRef1"
					   name="movement.reference1" style="width:152px;height:22px"
					   value="${movement.reference1}" /></td>
		</tr>
	</table>
	<table width="565px" border="0" cellspacing="1" cellpadding="0">
		<tr height="21px">
			<td width="113px"><b>&nbsp;<fmt:message key="movement.counterParty" /></b></td>
			<td width="120px"><input type="text" class="htmlTextAlpha"
									 maxlength="12" onchange="clearCounterPartyDesc()"
									 name="movement.counterPartyId" tabindex="13"
									 titleKey="tooltip.counterPartId" disabled
									 style="width:120px;height:22px;"
									 value="${movement.counterPartyId}" />

			</td>
			<td width="50px"> &nbsp;<input id="counterPartyLink" type="button"
				tabindex="14" title='<fmt:message key="tooltip.clickSelCounterId"/>'
				value="..."
				onClick="javascript:party('N','movement.counterPartyId','counterPartyDesc')" />&nbsp;&nbsp;
			</td>
			<td width="280px"><span id="counterPartyDesc"
				name="counterPartyDesc" class="spantext" /></td>
			<td><input type="hidden" name="movement.matchStatus" value="${movement.matchStatus}" /></td>
		</tr>
	</table>
	<table width="445px" border="0" cellspacing="1" cellpadding="0">
		<tr height="21px">
			<td width="110px"></td>
			<td width="319px"><input type="text" name="movement.counterPartyText1" class="htmlTextAlpha"
									 maxlength="35" tabindex="15" titleKey="tooltip.enterCPartytext"
									 style="width:321px;height:22px" value="${movement.counterPartyText1}" />
			</td>
		</tr>
	</table>
	<table width="758px" border="0" cellspacing="1" cellpadding="0">
		<tr height="21px">
			<td width="106px"><b>&nbsp;<fmt:message key="movement.matchingParty" /></b></td>
			<td width="4px">&nbsp;</td>
			<td width="300px"><input type="text" class="htmlTextAlpha"
									 maxlength="35" onchange="clearMatchingPartyDesc()"
									 name="movement.matchingParty" tabindex="16"
									 titleKey="tooltip.enterMatchingParty" disabled=""
									 style="width:300px;height:22px;" value="${movement.matchingParty}" />
			</td>
			<td width="50px"><input id="matchingPartyLink" type="button"
				tabindex="17"
				title='<fmt:message key="tooltip.clickSelMatchingParty"/>'
				value="..."
				onClick="javascript:party('M','movement.matchingParty','matchingPartyDesc')" />&nbsp;&nbsp;
			</td>
			<td width="280px"><span id="matchingPartyDesc"
				name="matchingPartyDesc" class="spantext"></td>
		</tr>
	</table>
	<table width="280px" border="0" cellpadding="0" cellspacing="0"
		height="">
		<tr height="25">
			<td width="104px">&nbsp;<B><fmt:message key="movement.productType" /></B></td>
			<td width="100px"><input type="text" name="movement.productType"
									 maxlength="16" class="htmlTextAlpha"
									 titleKey="tooltip.enterProductType" tabindex="18"
									 style="width:150px;height:22px" value="${movement.productType}" />
			</td>
		</tr>
	</table>
	<table width="264px" border="0" cellpadding="0" cellspacing="1"
		height="">
		<tr height="25">
			<td width="123px">&nbsp;<B><fmt:message key="movement.postingDate" /></B></td>
			<td width="160px" style="margin-bottom: 20px; "><input type="text" titleKey="tooltip.enterPostingDate"
																   tabindex="19" class="htmlTextAlpha"
																   name="movement.postingDateAsString" style="width:80px;margin-bottom: 5px;height: 20px;"
																   onchange="return validateField(this,'movement.postingDateAsString',dateFormat );"
																   value="${movement.postingDateAsString}" />
			<A title='<fmt:message key="tooltip.selectDate"/>' tabindex="20" 
				name="postingDateLink" ID="postingDateLink"
				onClick="cal1.select(document.forms[0].elements['movement.postingDateAsString'],'postingDateLink',dateFormatValue);return false;"><img
				src="images/calendar-16.gif"></A></td>
		</tr>
	</table>
	</div>
	</div>
	<div id="MovementDisplay"
		style="position: absolute; left: 800px; top: 0px; width: 100px; height: 70px;">
	<div
		style="position: absolute; left: 8px; top: 20px; width: 50px; height: 4px;">
		<div style="left:8px; top:4px;height: 94px;">
		
	<fieldset style="width: 106px; border: 2px groove; ">
	<legend><fmt:message key="movementDisplay.predict" />&nbsp;<fmt:message key="movementDisplay.status" /></legend>
	<table width="100px" border="0" cellspacing="2" cellpadding="0"
		height="20">
		<tr height="23px">
    <td width="100px">
        <input type="radio" titleKey="tooltip.selectInclMvmInMonitors" id="3"
               style="width:23px" tabindex="21" name="movement.predictStatus"
               value="I" ${movement.predictStatus == 'I' ? 'checked="checked"' : ''}
               ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
        <label for="3"><fmt:message key="movementDisplay.included" /></label>
    </td>
</tr>
<tr height="23px">
    <td width="100px">
        <input type="radio" titleKey="tooltip.selectExlMvmDealerMons" id="4"
               style="width:23px" tabindex="22" name="movement.predictStatus"
               value="E" ${movement.predictStatus == 'E' ? 'checked="checked"' : ''}
               ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
        <label for="4"><fmt:message key="movementDisplay.excluded" /></label>
    </td>
</tr>
<tr height="23px">
    <td width="100px">
        <input type="radio" titleKey="tooltip.cancelMvm" id="5"
               style="width:23px" tabindex="23" name="movement.predictStatus"
               value="C" ${movement.predictStatus == 'C' ? 'checked="checked"' : ''}
               ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
        <label for="5"><fmt:message key="movementDisplay.cancelled" /></label>
    </td>
</tr>

	</table>
	</fieldset>
	</div>
	</div>
	</div>
	<div id="divPrintHelpBar"
		style="position: absolute; left: 850; top: 380px; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="32" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Pre-advice Input'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()" onKeyDown="submitEnter(this,event)"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
			<td align="right" id="Print"><a onclick="printPage();"
				onblur="setFocus(document.forms[0])" onKeyDown="submitEnter(this,event)"
				tabindex="33" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 370; width: 915px; height: 40px; visibility: visible;">
	<div id="MovementDisplay"
		style="position: absolute; left: 6; top: 4; width: 430px; height: 15px; visibility: visible;">
	<%
		if (screenIdentifier.equals("insert")
				|| screenIdentifier.equals("update")) {
	%>
	<table id="buttonTable" width="280" border="0" cellspacing="0"
		cellpadding="0" height="20">
		<tr>
			<td><a title='<fmt:message key="tooltip.enterNotes"/>'
				tabindex="24" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildMovement('notes'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')"><fmt:message key="button.notes" /></a></td>
			<c:if test="${requestScope.inputscreen == 'update'}">

				<td><a title='<fmt:message key="tooltip.logSelMvm"/>'
					tabindex="25" onMouseOut="collapsebutton(this)"
					onKeyDown="submitEnter(this,event)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onClick="javascript:openWindow(movementLog('movementLog'),'movementLogWindow','left=50,top=190,width=1240,height=555,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.log" /></a></td>
			</c:if>
			<td id="saveorupdatebutton"></td>
			<td id="cancelbutton"><a tabindex="27"
				title='<fmt:message key="tooltip.CancelChanges"/>'
				onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="javascript:onCancle();"><fmt:message key="button.cancel" /></a></td>
			<td id="closebutton"><a tabindex="28"
				title='<fmt:message key="tooltip.close"/>'
				onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>
	<div id="MovementDisplay"
		style="position: absolute; left: 6; top: 4; width: 430px; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" width="260px">
		<tr>
			<td id="enablesavebutton"><a tabindex="26"
				title='<fmt:message key="tooltip.SaveChanges"/>'
				onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:submitFormButton('save');"><fmt:message key="button.save" /></a></td>
			<td id="disablesavebutton"><a class="disabled" 
				title='<fmt:message key="tooltip.SaveChanges"/>' disabled="disabled"><fmt:message key="button.save" /></a></td>
			<td id="enableupdatebutton"><a tabindex="26" 
				title='<fmt:message key="tooltip.SaveChanges"/>'
				onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:submitFormButton('update');"><fmt:message key="button.save" /></a></td>
		</tr>
	</table>
	</div>
	<%
		} else {
	%>
	<table width="550" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<!-- Change Button : Onclick enable saveorupdatebutton as enableupdatebutton-->
			<td id="changebutton"><a tabindex="24"  
				title='<fmt:message key="tooltip.changeSelectMvm"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:updateMovement('displayPreadvice');"><fmt:message key="button.change" /></a></td>
			<td id="openbutton" ><a tabindex="25"   
				title='<fmt:message key="tooltip.openSelectMvm"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:clickOpenButton('updateOpenFlag');"><fmt:message key="button.open" /></a></td>
			<!-- Dlete Button : Only for display preadvice-->
			<td id="deletebutton" ><a tabindex="26" 
				title='<fmt:message key="tooltip.deletemvmntdetails"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:onDelete('delete');"><fmt:message key="button.delete" /></a></td>
			<!-- Notes Button : Always Display-->
			<td id="notebutton"><a tabindex="27" 
				title='<fmt:message key="tooltip.enterNotes"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="javascript:openNotes();"><fmt:message key="button.notes" /></a></td>
			<!-- Log Button : Only for display preadvice -->
			<td id="logbutton"><a tabindex="28"  
				title='<fmt:message key="tooltip.logSelMvm"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="javascript:openLog();"><fmt:message key="button.log" /></a></td>
			<!-- xRefs Button for Cross Reference Display -->
			<td id="xrefsbutton"><a tabindex="29"  
				title='<fmt:message key="tooltip.viewCrossReference"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:openCrossRef();"><fmt:message key="button.crossReference" /></a></td>
			<!-- Cancel Button : Always Display -->
			<td id="canclebutton"><a tabindex="30" 
				title='<fmt:message key="tooltip.CancelChanges"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:cancleMovement('search');"><fmt:message key="button.cancel" /></a></td>
			<!-- Close Button : Always display -->
			<td id="closebutton"><a tabindex="31"
				title='<fmt:message key="tooltip.close"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>
	<div id="MovementDisplay"
		style="position: absolute; left: 6; top: 4; width: 0px; height: 15px; visibility: hidden; display:none;">
	<table border="0" cellspacing="0" cellpadding="0" width="420px">
		<tr>
			<!-- Change Button : Onclick enable saveorupdatebutton as enableupdatebutton-->
			<c:if test="${requestScope.SaveButton == 'true'}">
				<td id="enablechangebutton">
					<a tabindex="24"
					   title='<fmt:message key="tooltip.changeSelectMvm"/>'
					   onKeyDown="submitEnter(this,event)"
					   onMouseOut="collapsebutton(this)"
					   onMouseOver="highlightbutton(this)"
					   onMouseDown="expandbutton(this)"
					   onMouseUp="highlightbutton(this)"
					   onclick="javascript:updateMovement('displayPreadvice');">
						<fmt:message key="button.change"/>
					</a>
				</td>

				<td id="enableopenbutton">
					<!-- If movement value date belongs to today or past -->
					<c:if test="${requestScope.valueDateInd == '1'}">
						<c:choose>
							<c:when test="${requestScope.openFlag == 'Y'}">
								<a title='<fmt:message key="tooltip.unopenSelectMvm"/>'
								   tabindex="25"
								   onKeyDown="submitEnter(this,event)"
								   onMouseOut="collapsebutton(this)"
								   onMouseOver="highlightbutton(this)"
								   onMouseDown="expandbutton(this)"
								   onMouseUp="highlightbutton(this)"
								   onclick="javascript:clickOpenButton('updateOpenFlag');">
									<fmt:message key="button.Unopen"/>
								</a>
							</c:when>
							<c:otherwise>
								<a title='<fmt:message key="tooltip.openSelectMvm"/>'
								   tabindex="25"
								   onKeyDown="submitEnter(this,event)"
								   onMouseOut="collapsebutton(this)"
								   onMouseOver="highlightbutton(this)"
								   onMouseDown="expandbutton(this)"
								   onMouseUp="highlightbutton(this)"
								   onclick="javascript:clickOpenButton('updateOpenFlag');">
									<fmt:message key="button.Open"/>
								</a>
							</c:otherwise>
						</c:choose>
					</c:if>

					<!-- If movement value date belongs to future -->
					<c:if test="${requestScope.valueDateInd != '1'}">
						<c:choose>
							<c:when test="${requestScope.openFlag == 'Y'}">
								<a class="disabled" disabled="disabled">
									<fmt:message key="button.Unopen"/>
								</a>
							</c:when>
							<c:otherwise>
								<a class="disabled" disabled="disabled">
									<fmt:message key="button.Open"/>
								</a>
							</c:otherwise>
						</c:choose>
					</c:if>
				</td>
			</c:if>

			<c:if test="${requestScope.SaveButton != 'true'}">
				<td id="enablechangebutton">
					<a class="disabled" disabled="disabled">
						<fmt:message key="button.change"/>
					</a>
				</td>
				<td id="enableopenbutton">
					<c:choose>
						<c:when test="${requestScope.openFlag == 'Y'}">
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.Unopen"/>
							</a>
						</c:when>
						<c:otherwise>
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.Open"/>
							</a>
						</c:otherwise>
					</c:choose>
				</td>
			</c:if>

			<td id="disablechangebutton"><a class="disabled"
				disabled="disabled"  ><fmt:message key="button.change" /></a></td>
			<td id="disableopenbutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.Open" /></a></td>
			<!-- Dlete Button : Only for display preadvice-->
			<td id="enabledeletebutton"><a tabindex="26" 
				title='<fmt:message key="tooltip.deletemvmntdetails"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:onDelete('delete');"><fmt:message key="button.delete" /></a></td>
			<td id="disabledeletebutton"><a class="disabled"
				disabled="disabled"  ><fmt:message key="button.delete" /></a></td>
			<!-- Notes Button : Always Display-->
			<td id="enablenotebutton"><a tabindex="27" 
				title='<fmt:message key="tooltip.enterNotes"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="javascript:openNotes();"><fmt:message key="button.notes" /></a></td>
			<td id="disablenotebutton"><a class="disabled"
				disabled="disabled"  ><fmt:message key="button.notes" /></a></td>
			<!-- Log Button : Note in use but not removed b'cuse it may cause create problem in SCREEN fitting -->
			<td id="enablelogbutton"><a tabindex="28" 
				title='<fmt:message key="tooltip.enterNotes"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildMovementFrmSearch('notes'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')">Log</a>
			</td>
			<td id="disablelogbutton"><a class="disabled"
				disabled="disabled" ><fmt:message key="button.log" /></a></td>
			<!-- Cancel Button : Always Display -->
			<td id="enablecanclebutton"><a tabindex="30" 
				title='<fmt:message key="tooltip.CancelChanges"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:cancleMovement('search');"><fmt:message key="button.cancel" /></a></td>
			<td id="disablecanclebutton"><a class="disabled"
				disabled="disabled"  ><fmt:message key="button.cancel" /></a></td>
			<!-- Xrefs Button for Update function (PreAdviceDisplay)-->
			<td id="enablexrefsbutton"><a tabindex="29" 
				title='<fmt:message key="tooltip.viewCrossReference"/>'
				onKeyDown="submitEnter(this,event)" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onClick="openCrossRef()"><fmt:message key="button.crossReference" /></a></td>
			<td id="disablexrefsbutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.crossReference" /></a>
			</td>
			<!-- End:Code Modified For Mantis 1827 by Sudhakar on 26-09-2012:Pre Advice display should not accept more than 12 characters And Pre-advice should display cancel radio button in Predict Status -->
		</tr>
	</table>
	</div>
	<%
		}
	%>
	</div>
	</body>
</form>