<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="java.util.*" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>


<c:set var="recordCount" value="${requestScope.metagroupMonitor.predictedBalances.size()}"/>

<groupmonitor
        databuilding="${requestScope.bkMonitorJobStatusFlag == 'Y' ? 'true' : 'false'}"
        refresh="${requestScope.autoRefreshRate}"
        datefrom="${requestScope.metagroupMonitor.date}"
        currencyformat="${sessionScope.CDM.currencyFormat}"
        dateformat="${sessionScope.CDM.dateFormatValue}"
        tabindex="${requestScope.selectedTabIndex}"
        lastRefTime="${requestScope.lastRefTime}"
        sysDateFrmSession="${requestScope.sysDateFrmSession}"
        dateComparing="${requestScope.dateComparing == 'Y' ? 'true' : 'false'}"
        currfontsize="${requestScope.fontSize}"
        existingEntityId="${requestScope.existingEntityId}"
>

    <c:if test="${requestScope.itemId == '7'}">
        <request_reply>
            <status_ok>${requestScope.reply_status_ok}</status_ok>
            <message>${requestScope.reply_message}</message>
            <location/>
        </request_reply>
        <singletons>
            <sod negative="${requestScope.metagroupMonitor.grandTotalNegative ? 'true' : 'false'}">${requestScope.metagroupMonitor.total}</sod>
        </singletons>

        <grid>
            <metadata>
                <columns>
                    <c:forEach var="order" items="${requestScope.column_order}">
                        <c:choose>
                            <c:when test="${order == 'group'}">
                                <column
                                        heading="<fmt:message key='metagroupMonitor.metagroup'/>"
                                        draggable="false"
                                        filterable="true"
                                        type="str"
                                        dataelement="group"
                                        sort="true"
                                        width="${requestScope.column_width.group}"
                                />
                            </c:when>
                            <c:when test="${order == 'name'}">
                                <column
                                        heading="<fmt:message key='bookMonitor.name'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        dataelement="name"
                                        sort="true"
                                        width="${requestScope.column_width.name}"
                                />
                            </c:when>
                            <c:when test="${order == 'level'}">
                                <column
                                        heading="<fmt:message key='metagroupMonitor.level'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        dataelement="level"
                                        sort="true"
                                        width="${requestScope.column_width.level}"
                                />
                            </c:when>
                            <c:when test="${order == 'total'}">
                                <column
                                        heading="<fmt:message key='positionlevel.total'/>"
                                        draggable="true"
                                        filterable="true"
                                        sort="true"
                                        balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
                                        type="num"
                                        dataelement="total"
                                        width="${requestScope.column_width.total}"
                                />
                            </c:when>
                        </c:choose>
                    </c:forEach>
                </columns>
            </metadata>
            <rows size="${recordCount}">
                <c:forEach var="monitorDetails" items="${requestScope.metagroupMonitor.predictedBalances}">
                    <row>
                        <group clickable="false">${monitorDetails.metagroupCode}</group>
                        <name clickable="false">${monitorDetails.metagroupName}</name>
                        <level clickable="false">${monitorDetails.levelName}</level>
                        <total
                                negative="${!monitorDetails.predictedBalanceNegative ? 'false' : 'true'}"
                                clickable="true"
                        >${monitorDetails.predictedBalance}</total>
                    </row>
                </c:forEach>
            </rows>
        </grid>

        <selects>
            <select id="parent-container" label="${requestScope.monitorType}"/>
            <select id="entity">
                <c:forEach var="entity" items="${requestScope.entities}">
                    <option
                            value="${entity.value}"
                            selected="${requestScope.metagroupMonitor.entityId == entity.value ? '1' : '0'}"
                    >${entity.label}</option>
                </c:forEach>
            </select>

            <select id="currency">
                <c:forEach var="currency" items="${requestScope.currencies}">
                    <option
                            value="${currency.value}"
                            selected="${requestScope.metagroupMonitor.currencyId == currency.value ? '1' : '0'}"
                    >${currency.label}</option>
                </c:forEach>
            </select>

            <select id="location">
                <c:forEach var="location" items="${requestScope.locations}">
                    <option
                            value="${location.value}"
                            selected="${requestScope.metagroupMonitor.locationId == location.value ? '1' : '0'}"
                    >${location.label}</option>
                </c:forEach>
            </select>

            <select id="monitor">
                <c:forEach var="monitor" items="${requestScope.monitors}">
                    <option
                            value="${monitor.value}"
                            selected="${requestScope.metagroupMonitor.monitorType == monitor.value ? '1' : '0'}"
                    >${monitor.label}</option>
                </c:forEach>
            </select>
        </selects>
    </c:if>

    <c:if test="${requestScope.itemId == '8'}">
        <atef>8888888888888</atef>
        <request_reply>
            <status_ok>${requestScope.reply_status_ok}</status_ok>
            <message>${requestScope.reply_message}</message>
            <location/>
        </request_reply>
        <singletons>
            <sod negative="${requestScope.metagroupMonitor.grandTotalNegative ? 'true' : 'false'}">${requestScope.metagroupMonitor.total}</sod>
        </singletons>

        <grid>
            <metadata>
                <columns>
                    <c:forEach var="order" items="${requestScope.column_order}">
                        <c:choose>
                            <c:when test="${order == 'group'}">
                                <column
                                        heading="<fmt:message key='groupMonitor.group'/>"
                                        draggable="false"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="group"
                                        width="${requestScope.column_width.group}"
                                />
                            </c:when>
                            <c:when test="${order == 'name'}">
                                <column
                                        heading="<fmt:message key='bookMonitor.name'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="name"
                                        width="${requestScope.column_width.name}"
                                />
                            </c:when>
                            <c:when test="${order == 'level'}">
                                <column
                                        heading="<fmt:message key='metagroupMonitor.level'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="level"
                                        width="${requestScope.column_width.level}"
                                />
                            </c:when>
                            <c:when test="${order == 'total'}">
                                <column
                                        heading="<fmt:message key='positionlevel.total'/>"
                                        draggable="true"
                                        filterable="true"
                                        sort="true"
                                        balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
                                        type="num"
                                        dataelement="total"
                                        width="${requestScope.column_width.total}"
                                />
                            </c:when>
                        </c:choose>
                    </c:forEach>
                </columns>
            </metadata>
            <rows size="${recordCount}">
                <c:forEach var="monitorDetails" items="${requestScope.metagroupMonitor.predictedBalances}">
                    <row>
                        <group clickable="false">${monitorDetails.groupCode}</group>
                        <name clickable="false">${monitorDetails.groupName}</name>
                        <level clickable="false">${monitorDetails.levelName}</level>
                        <total
                                negative="${!monitorDetails.predictedBalanceNegative ? 'false' : 'true'}"
                                clickable="true"
                        >${monitorDetails.predictedBalance}</total>
                    </row>
                </c:forEach>
            </rows>
        </grid>

        <selects>
            <select id="parent-container" label="${requestScope.monitorType}"/>
            <select id="entity">
                <c:forEach var="entity" items="${requestScope.entities}">
                    <option
                            value="${entity.value}"
                            selected="${requestScope.metagroupMonitor.entityId == entity.value ? '1' : '0'}"
                    >${entity.label}</option>
                </c:forEach>
            </select>

            <select id="currency">
                <c:forEach var="currency" items="${requestScope.currencies}">
                    <option
                            value="${currency.value}"
                            selected="${requestScope.metagroupMonitor.currencyId == currency.value ? '1' : '0'}"
                    >${currency.label}</option>
                </c:forEach>
            </select>

            <select id="location">
                <c:forEach var="location" items="${requestScope.locations}">
                    <option
                            value="${location.value}"
                            selected="${requestScope.metagroupMonitor.locationId == location.value ? '1' : '0'}"
                    >${location.label}</option>
                </c:forEach>
            </select>

            <select id="monitor">
                <c:forEach var="monitor" items="${requestScope.monitors}">
                    <option
                            value="${monitor.value}"
                            selected="${requestScope.metagroupMonitor.monitorType == monitor.value ? '1' : '0'}"
                    >${monitor.label}</option>
                </c:forEach>
            </select>

            <select id="metagroup">
                <c:forEach var="metagroup" items="${requestScope.metagroups}">
                    <option
                            value="${metagroup.value}"
                            selected="${requestScope.metagroupMonitor.metagroupId == metagroup.value ? '1' : '0'}"
                    >${metagroup.label}</option>
                </c:forEach>
            </select>
        </selects>
    </c:if>

    <c:if test="${requestScope.itemId == '9'}">
        <atef>99999999999999999</atef>
        <request_reply>
            <status_ok>${requestScope.reply_status_ok}</status_ok>
            <message>${requestScope.reply_message}</message>
            <location/>
        </request_reply>
        <singletons>
            <sod negative="${requestScope.metagroupMonitor.grandTotalNegative ? 'true' : 'false'}">${requestScope.metagroupMonitor.total}</sod>
        </singletons>

        <grid>
            <metadata>
                <columns>
                    <c:forEach var="order" items="${requestScope.column_order}">
                        <c:choose>
                            <c:when test="${order == 'group'}">
                                <column
                                        heading="<fmt:message key='bookMonitor.book'/>"
                                        draggable="false"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="group"
                                        width="${requestScope.column_width.group}"
                                />
                            </c:when>
                            <c:when test="${order == 'name'}">
                                <column
                                        heading="<fmt:message key='bookMonitor.name'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="name"
                                        width="${requestScope.column_width.name}"
                                />
                            </c:when>
                            <c:when test="${order == 'level'}">
                                <column
                                        heading="<fmt:message key='bookMonitor.location'/>"
                                        draggable="true"
                                        filterable="true"
                                        type="str"
                                        sort="true"
                                        dataelement="level"
                                        width="${requestScope.column_width.level}"
                                />
                            </c:when>
                            <c:when test="${order == 'total'}">
                                <column
                                        heading="<fmt:message key='positionlevel.total'/>"
                                        draggable="true"
                                        filterable="true"
                                        balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
                                        type="num"
                                        sort="true"
                                        dataelement="total"
                                        width="${requestScope.column_width.total}"
                                />
                            </c:when>
                        </c:choose>
                    </c:forEach>
                </columns>
            </metadata>
            <rows size="${recordCount}">
                <c:forEach var="monitorDetails" items="${requestScope.metagroupMonitor.predictedBalances}">
                    <row>
                        <group clickable="false">${monitorDetails.bookCode}</group>
                        <name clickable="false">${monitorDetails.bookName}</name>
                        <level clickable="false">${monitorDetails.locationName}</level>
                        <total
                                negative="${!monitorDetails.predictedBalanceNegative ? 'false' : 'true'}"
                                clickable="true"
                        >${monitorDetails.predictedBalance}</total>
                    </row>
                </c:forEach>
            </rows>

            <totals>
                <total>
                    <group draggable="false"><fmt:message key="accountmonitorNew.Total"/></group>
                    <name>-</name>
                    <level>-</level>
                    <total
                            negative="${requestScope.metagroupMonitor.grandTotalNegative ? 'true' : 'false'}"
                            draggable="false"
                    >${requestScope.metagroupMonitor.grandTotalAsString}</total>
                </total>
            </totals>
        </grid>

        <selects>
            <select id="parent-container" label="${requestScope.monitorType}"/>
            <select id="entity">
                <c:forEach var="entity" items="${requestScope.entities}">

                    <option
                            value="${entity.value}"
                            selected="${requestScope.metagroupMonitor.entityId == entity.value ? '1' : '0'}"
                    >${entity.label}</option>
                </c:forEach>
            </select>

            <select id="currency">
                <c:forEach var="currency" items="${requestScope.currencies}">
                    <option
                            value="${currency.value}"
                            selected="${requestScope.metagroupMonitor.currencyId == currency.value ? '1' : '0'}"
                    >${currency.label}</option>
                </c:forEach>
            </select>

            <select id="location">
                <c:forEach var="location" items="${requestScope.locations}">
                    <option
                            value="${location.value}"
                            selected="${requestScope.metagroupMonitor.locationId == location.value ? '1' : '0'}"
                    >${location.label}</option>
                </c:forEach>
            </select>

            <select id="monitor">
                <c:forEach var="monitor" items="${requestScope.monitors}">
                    <option
                            value="${monitor.value}"
                            selected="${requestScope.metagroupMonitor.monitorType == monitor.value ? '1' : '0'}"
                    >${monitor.label}</option>
                </c:forEach>
            </select>

            <select id="groupCode">
                <c:forEach var="groupCode" items="${requestScope.groups}">
                    <option
                            value="${groupCode.value}"
                            selected="${requestScope.metagroupMonitor.groupCode == groupCode.value ? '1' : '0'}"
                    >${groupCode.label}</option>
                </c:forEach>
            </select>
        </selects>
    </c:if>

    <tabs>
        <c:forEach var="tab" items="${requestScope.tabDetails}">
            <predictdate
                    businessday="${tab.businessDay}"
                    dateLabel="${tab.tabDateLabel}"
                    tabDateAsString="${tab.tabDateAsString}"
            >${tab.tabDateAsString}</predictdate>
        </c:forEach>
    </tabs>
</groupmonitor>