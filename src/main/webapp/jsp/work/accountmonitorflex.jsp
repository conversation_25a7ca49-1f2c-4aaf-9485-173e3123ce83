<!doctype html>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>

<script type="text/javascript">
	var screenTitle = "";
	screenTitle = "Account  Monitor"; //getMessage("label.accountattributedefinition.title.window", null);
	document.title = screenTitle;
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "accountMonitor";

	// 			window.onload = function () {
	// 				setTitleSuffix(document.forms[0]);
	// 				setParentChildsFocus();
	// 			};

	// 			window.onunload = call;
	var calledFromParent = <%=("" + request.getAttribute("callFromParent")).equalsIgnoreCase("Y") ? "true" : "false"%>;
	var menuAccessId = '${requestScope.menuAccessId}';
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();

	label["text"]["entity"] = "<fmt:message key="entity.id"/>";
	label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";

	label["text"]["currency"] = "<fmt:message key="currencymonitor.currId"/>";
	label["tip"]["currency"] = "<fmt:message key="tooltip.selectCurrencyCode"/>";

	label["text"]["accounttype"] = "<fmt:message key="accttype"/>";
	label["tip"]["accounttype"] = "<fmt:message key="tooltip.selectAccountType"/>";

	label["text"]["accountclass"] = "<fmt:message key="acctclass"/>";
	label["tip"]["accountclass"] = "<fmt:message key="tooltip.selectAccountClass"/>";

	label["text"]["date"] = "<fmt:message key="accountMonitorNew.date"/>";
	label["tip"]["date"] = "<fmt:message key="tooltip.enterValueDate"/>";
	label["tip"]["dateMMDDYY"] = "<fmt:message key="tooltip.ValueDateMMDDYY"/>";

	label["text"]["tab1"] = "<fmt:message key="accountmonitor.today"/>";

	label["text"]["tab2"] = "<fmt:message key="accountmonitor.today1"/>";

	label["text"]["tab3"] = "<fmt:message key="accountmonitor.today2"/>";

	label["text"]["tab4"] = "<fmt:message key="accountmonitor.selected"/>";

	label["text"]["sodlegendtoday"] = "<fmt:message key="accountMonitorNew.currencyTextToday"/>";

	label["text"]["breakdownlegend"] = "<fmt:message key="accountMonitorNew.screenSelectorText"/>";

	label["text"]["hidezero"] = "<fmt:message key="hidezerobalances"/>";
	label["tip"]["hidezero"] = "<fmt:message key="tooltip.hideZeroBalances"/>";

	label["text"]["threshold"] = "<fmt:message key="mvmt.applyCurrencyThreshold"/>";
	label["tip"]["threshold"] = "<fmt:message key="tooltip.applyCurrencyThreshold"/>";

	label["text"]["sod"] = "<fmt:message key="accountMonitorNew.startOfDayBalance"/>";
	label["tip"]["sod"] = "<fmt:message key="tooltip.accountMonitorNew.accumulatedSODBalAsString"/>";

	label["text"]["openunexpected"] = "<fmt:message key="accountMonitorNew.openUnexpectedTotal"/>";
	label["tip"]["openunexpected"] = "<fmt:message key="tooltip.accountMonitorNew.openUnexpectedBalTotalAsString"/>";

	label["text"]["accountbreakdown"] = "<fmt:message key="acountMonitorNew.AccountBreakDownScreen"/>";
	label["tip"]["accountbreakdown"] = "<fmt:message key="tooltip.AcctBreakDown"/>";

	label["text"]["movement"] = "<fmt:message key="acountMonitorNew.MovementSummaryScreen"/>";
	label["tip"]["movement"] = "<fmt:message key="tooltip.MoveSummaryDisplay"/>";

	label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
	label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

	label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
	label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

	label["text"]["button-sum"] = "<fmt:message key="accountmonitorbutton.Sum"/>";
	label["tip"]["button-sum"] = "<fmt:message key="tooltip.sumButton"/>";

	label["text"]["button-move"] = "<fmt:message key="accountmonitorbutton.move"/>";
	label["tip"]["button-move"] = "<fmt:message key="tooltip.moveButton"/>";

	label["text"]["button-manswp"] = "<fmt:message key="button.ManSwp"/>";
	label["tip"]["button-manswp"] = "<fmt:message key="tooltip.manualSweep"/>";

	label["text"]["button-close"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

	label["text"]["autoRefreshRate"] = "<fmt:message key="ccyMonitorOptions.refreshRate"/>";
	label["text"]["label-refreshRate-seconds"] = "<fmt:message key="label.refreshRate.seconds"/>";
	label["text"]["label-font-size"] = "<fmt:message key="centralMonitorOptions.fontSize"/>";
	label["text"]["label-normal"] = "<fmt:message key="ccyMonitorOptions.fontSizeNormal"/>";
	label["tip"]["label-normal"] = "<fmt:message key="tooltip.ccyMonitorOptions.selectFontSizeNormal"/>";
	label["text"]["label-small"] = "<fmt:message key="ccyMonitorOptions.fontSizeSmall"/>";
	label["tip"]["label-small"] = "<fmt:message key="tooltip.ccyMonitorOptions.selectFontSizeSmall"/>";
	label["text"]["button-ok"] = "<fmt:message key="button.ok"/>";
	label["tip"]["button-ok"] = "<fmt:message key="tooltip.forecastMonitor.save"/>";
	label["text"]["button-cancel"] = "<fmt:message key="label.personalEntityList.button.cancel"/>";
	label["tip"]["button-cancel"] = "<fmt:message key="tooltip.forecastMonitor.cancel"/>";

	label["text"]["alert.currencyAccess"] = "<fmt:message key="alert.currencyAccess"/>";
	/*Start : Added by Imed B on 20-02-2014 */
	label["text"]["label-accountMonitor"] = "<fmt:message key="accountMonitor.title.accountMonitor"/>";
	label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
	label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
	label["text"]["label-notANumber"] = "<fmt:message key="accountMonitor.alert.label.notANumber"/>";
	label["text"]["label-refreshRateSelected"] = "<fmt:message key="accountMonitor.alert.label.refreshRateSelected"/>";
	label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
	label["text"]["label-entity"] = "<fmt:message key="accountMonitor.label.entity"/>";
	label["text"]["label-accountType"] = "<fmt:message key="accountMonitor.label.accountType"/>";
	label["text"]["label-accountClass"] = "<fmt:message key="accountMonitor.label.accountClass"/>";
	label["text"]["label-startDayBalance"] = "<fmt:message key="accountMonitor.label.startDayBalance"/>";
	label["text"]["label-openUnexpected"] = "<fmt:message key="accountMonitor.label.openUnexpected"/>";
	label["text"]["label-hideZeroBalances"] = "<fmt:message key="accountMonitor.label.hideZeroBalances"/>";
	label["text"]["label-currencyThresold"] = "<fmt:message key="accountMonitor.label.currencyThresold"/>";
	label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
	label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
	label["text"]["label-seconds"] = "<fmt:message key="label.refreshRate.seconds"/>";
	label["text"]["label-lastRefresh"] = "<fmt:message key="label.lastRefTime"/>";

	label["text"]["label-screenMode"] = "<fmt:message key="accountMonitor.label.screenMode"/>";
	label["text"]["label-group"] = "<fmt:message key="accountMonitor.label.group"/>";
	label["text"]["label-noSum"] = "<fmt:message key="accountMonitor.label.notSum"/>";
	label["text"]["label-incSum"] = "<fmt:message key="accountMonitor.label.incSum"/>";
	label["text"]["label-sumCutOff"] = "<fmt:message key="accountMonitor.label.sumCutOff"/>";
	/*End : Added by Imed B on 20-02-2014 */


	label["text"]["button-options"] = "<fmt:message key="button.options"/>";
	label["tip"]["button-options"] = "<fmt:message key="tooltip.options"/>";
	var currentFontSize = "<%=request.getAttribute("fontSize")%>";


	var itemId = '${requestScope.itemId}';
	var hostId = '${requestScope.hostId}';
	var userId = '${requestScope.userId}';

	var callFrom = '${requestScope.callFrom}';
	var refreshPending = false;

	var appName = "<%=SwtUtil.appName%>";
	var testDate= "<%=SwtUtil.getSystemDateString()%>";
	var dbDate='${requestScope.dbBusinessDate}';
	var dateFormat = '${sessionScope.CDM.dateFormatValue}';
	var currencyFormat = '${sessionScope.CDM.currencyFormatValue}';
	var alertOrangeImage = "images/Alert/scenario/normal.png";
	var alertRedImage = "images/Alert/scenario/critical.png";
	var blankImage = "images/blank.png";

	function refreshParent () {
		if (calledFromParent)
			window.opener.refreshPending = true;
	}

	//flash uses this to determine what to request to save a new refresh rate
	function getUpdateRefreshRequest (rate) {
		return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + rate;
	}

	function getNextBusinessDate (entityId,currencyCode,selectedTabIndex) {
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/acctmonitornew.do?method=getNextBusinessDate";
		sURL = sURL + "&entityId="+entityId;
		sURL = sURL + "&currencyCode="+currencyCode;
		sURL = sURL + "&selectedTabIndex="+selectedTabIndex;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}

	function openAccountBrkDownMonitor (entityId, accountId, currencyCode, balanceType, applyCurrencyThreshold, hideZeroBalances, date,callstatus) {
		var menuAccessIdChild = getMenuAccessIdOfChildWindow("acctbreakdownmonitor.do");

		var requestURL = "acctbreakdownmonitor.do";
		requestURL += "?method=display";
		requestURL += "&entityId=" + entityId;
		requestURL += "&accountId=" + accountId;
		requestURL += "&selectedValueDate=" + date;
		requestURL += "&selectedBalanceType=" + balanceType;
		requestURL += "&currencyCode=" + currencyCode;
		requestURL += "&fromMenu=N";
		requestURL += "&menuAccessId=" + menuAccessIdChild;
		requestURL += "&applyCurrencyThreshold=" + (applyCurrencyThreshold?"Y":"N");
		requestURL += "&hideZeroBalances=" + (hideZeroBalances?"Y":"N");
		requestURL += "&callstatus=" + callstatus;

		var menuName = new String('<fmt:message key="accountmonitorBrkDown.title.window"/>');
		var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
		menuName = menuName.substr(0,smrtPredPos-3);

		if (menuAccessIdChild == 2) {
			alert ('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		} else {
			openWindow (requestURL,'accountBreakDownMonitorJsp','left=50,top=190,width=1200,height=700,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
		}
		if (window.event){
			window.event.returnValue = false;
			window.event.cancelBubble = true;
		}
	}

	/* Function moved from java script */
	function openManualsweep (entityId, currencyCode, accountType) {
		var menuAccessIdChild = getMenuAccessIdOfChildWindow("sweep.do");

		var menuName = new String('<fmt:message key="manSweeping.title.window"/>');
		var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
		menuName = menuName.substr(0,smrtPredPos-3);

		if (menuAccessIdChild == 2) {
			alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		} else {
			var param="sweep.do";
			param += "?method=manual";
			param += "&entityCode=" + entityId;
			param += "&selectedCurrency=" + currencyCode;
			param += "&selectedAccountType=" + accountType;
			param += "&menuAccessId="+menuAccessIdChild;
			openWindow (param, 'manualSweepWindow', 'left=50,top=190,width=1180,height=670,toolbar=0, resizable=yes, scrollbars=yes', 'true');
		}
		return false;
	}
	/**
	 * This function is used for opening the Movement summury Display  from the Account monitor screen.
	 *@param:entityId
	 *@param:accountId
	 *@param:date
	 *@param:applyCurrencyThreshold
	 *@param:hideZeroBalances
	 *@param:selectScreen
	 *@param:balanceType
	 **/
	function clickLink (entityId, currencyCode, accountId, date, applyCurrencyThreshold, hideZeroBalances, selectScreen, balanceType) {
		// Start:Code Modified For Mantis 1849 by Narasimhulu.P on 11-Sept-2012 to preserve threshold setting when drilling from Account Monitor to Movement Summary Display.
		// Set apply currency threshold false by default for all other balances
		var final_applyCurrencyThreshold = false;
		var callstatus=true;

		//override the currency threshold filter for unsettled && unexpected balances when currency thresold is set to true
		if (((balanceType == "<%=SwtConstants.ACCT_MONITOR_BALTYPE_UNSETTLED%>") || (balanceType == "<%=SwtConstants.ACCT_MONITOR_BALTYPE_UNEXPECTED%>"))
				&& (applyCurrencyThreshold == "true")) {

			final_applyCurrencyThreshold = true;

		}
		// End:Code Modified For Mantis 1849 by Narasimhulu.P on 11-Sept-2012 to preserve threshold setting when drilling from Account Monitor to Movement Summary Display.
		if (selectScreen == "a") {
			openAccountBrkDownMonitor (entityId, accountId, currencyCode, balanceType, applyCurrencyThreshold, hideZeroBalances, date,callstatus);
		} else {
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL = requestURL.substring(0,idy+1) ;

			requestURL += appName+"/acctmonitornew.do";
			requestURL += "?method=countMovements";
			requestURL += "&entityId=" + entityId;
			requestURL += "&currencyCode=" + currencyCode;
			requestURL += "&accountId=" + accountId;
			requestURL += "&valueDate=" + date;
			requestURL += "&balanceType=" + balanceType;
			requestURL += "&applyCurrencyThreshold=" + (final_applyCurrencyThreshold?"Y":"N");

			var noOfMovements = sendRequest (requestURL);

			if (noOfMovements == 0) {
				alert('<fmt:message key="alert.noMovemensAvailable"/>');
			} else {
				var requestURL = "outstandingmovement.do";

				requestURL += "?method=flexAccount";
				requestURL += "&entityId=" + entityId;
				requestURL += "&accountId=" + accountId;
				requestURL += "&valueDate=" + date;
				requestURL += "&balanceType=" + balanceType;
				requestURL += "&currencyCode=" + currencyCode;
				requestURL += "&initialinputscreen=accountmonitor";
				requestURL += "&applyCurrencyThreshold=" + (final_applyCurrencyThreshold?"Y":"N");
				openWindow (requestURL,'movementSummaryDisplayWindow','left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes');

			}
		}
		if (window.event){
			window.event.returnValue = false;
			window.event.cancelBubble = true;
		}
	}

	function sendRequest (sURL) {
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open ("POST", sURL, false);
		oXMLHTTP.send ();
		var count = new Number (oXMLHTTP.responseText);

		return count;
	}

	function help(){
		openWindow(buildPrintURL('print','Account Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}


	function showOpenUnexpectedMovs (entityId, currencyCode, accountId, date, accountType, applyCurrencyThreshold) {
		var balanceType = "<%=SwtConstants.ACCT_MONITOR_BALTYPE_OPEN_UNEXPECTED%>";

		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);

		requestURL += appName + "/acctmonitornew.do";
		requestURL += "?method=countMovements";
		requestURL += "&entityId=" + entityId;
		requestURL += "&currencyCode=" + currencyCode;
		requestURL += "&accountId=" + accountId;
		requestURL += "&valueDate=" + date;
		requestURL += "&balanceType=" + balanceType;
		requestURL += "&accountType=" + accountType;
		requestURL += "&applyCurrencyThreshold=" + applyCurrencyThreshold;

		var noOfMovements = sendRequest(requestURL);

		if (noOfMovements == 0) {
			ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.noMovemensAvailable"/>', null);
		} else {
			var requestURL = "outstandingmovement.do";
			requestURL += "?method=flexAccount";
			requestURL += "&entityId=" + entityId;
			requestURL += "&accountId=" + accountId;
			requestURL += "&valueDate=" + date;
			requestURL += "&balanceType=" + balanceType;
			requestURL += "&currencyCode=" + currencyCode;
			requestURL += "&accountType=" + accountType;
			requestURL += "&initialinputscreen=accountmonitor";
			requestURL += "&applyCurrencyThreshold=N";
			openWindow(requestURL,'movementSummaryDisplayWindow','left=50,top=190,width=1280,height=685,toolbar=0,resizable=yes,scrollbars=yes,status=yes');
		}
	}


	/**
	 * This function is used for calling the saveFontSize() method from ScreenOption
	 * to store the Font size of the screen data grid
	 *
	 * @param fontSize - set by the user.
	 *
	 */
	function getUpdateFontSize(fontSize) {
		return "screenOption.do?method=saveFontSize&screenOption.id.hostId="
				+ hostId
				+ "&screenOption.id.userId="
				+ userId
				+ "&screenOption.id.screenId="
				+ itemId
				+ "&screenOption.propertyValue=" + fontSize;
	}
</script>
<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" />
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="accountmonitor.title.window"/>" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>