<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>

<head>

<!-- To remove under lines in hypherlink  -->
<STYLE type="text/css">
<!--
A:link {
	text-decoration: none;
}

A:visited {
	text-decoration: none;
}
-->
</STYLE>



<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="style/displaytag.css">


<SCRIPT language="JAVASCRIPT">
 
var closeFlag=true;
var isRefresfFromParent = "false";
var appName = "<%=SwtUtil.appName%>";
var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];
var selectedtab = initialtab[1];
var previoustab=""

var dynamicTab = true;
var dateTabInd; //holds 0,1,2,3 respectively according to the tab clicked

var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;


var todaySysDate = "${requestScope.todaySysDate}";	 
var todaySysDatePlusOne = "${requestScope.todaySysDatePlusOne}";	 
var todaySysDatePlusTwo = "${requestScope.todaySysDatePlusTwo}";	 
var todaySysDatePlusThree = "${requestScope.todaySysDatePlusThree}";	 
var todaySysDatePlusFour = "${requestScope.todaySysDatePlusFour}";	 
var todaySysDatePlusFive = "${requestScope.todaySysDatePlusFive}";
var todaySysDatePlusSix = "${requestScope.todaySysDatePlusSix}";	
// Start:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991
var dbDate="${requestScope.dbDate}";
var existingEntityId="${requestScope.existingEntityId}";
// End:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991

// variable to hold dateSelected flag
var dateSelected = false;
// variable to get the date format value
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
// variable to get the date format
var dateFormat = '${sessionScope.CDM.dateFormat}';
// Initialize the CalendarPopup
var cal = new CalendarPopup("caldiv",true); 
// set the offset values
cal.offsetX = 22;
cal.offsetY = 0;
// varible to hold the date when click the calender date
var valueDateInForm = "";
// variable to hold the today date
var today = "${requestScope.today}";	  
var lastRefTime = "${requestScope.lastRefTime}";
// varibale to hold the tab names
var tabNames = new Array( 'MatchQueueTodayParent','MatchQueueTodayPlusOneParent','MatchQueueTodayPlusTwoParent','MatchQueueTodayPlusThreeParent',
				'MatchQueueTodayPlusFourParent','MatchQueueTodayPlusFiveParent','MatchQueueTodayPlusSixParent','MatchQueueAllParent','MatchQueueSelectedParent');
// variable to hold the flag value
var alertFlag = "false";				
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
  
function submitForm(methodName){
		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;	

	// if dateSelected true, empty the tabindex and tabname value
	if (dateSelected == false){
		setTabInfo();
	}
	else{
		document.forms[0].elements["selectedTabIndex"].value = "";
		document.forms[0].elements["selectedTabName"].value = "";	
	}
	// set the selectedValueDate value
	if (document.forms[0].elements["matchQueue.valueDateAsString"].value != "")
		document.forms[0].selectedValueDate.value = document.forms[0].elements["matchQueue.valueDateAsString"].value;
	document.forms[0].method.value = methodName;
	document.forms[0].elements["dbDate"].value = dbDate;
	// Start:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991
	document.forms[0].elements["existingEntityId"].value = existingEntityId;
	// End:Code modified by Vivekanandan A on 17/07/2012 for mantis 1991
	document.forms[0].status.value = '${requestScope.status}';
	document.forms[0].submit();
}

function call2(){
	if(isRefresfFromParent == "false")
	{
		call();
	}
}

var x5;

function updateColors5()
{
	var rows = x5.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x5.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}

var x2;

function updateColors2()
{
	var rows = x2.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x2.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}

var x3;

function updateColors3()
{
	var rows = x3.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x3.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}

var x4;

function updateColors4()
{
	var rows = x4.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x4.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}	
}
function tooltipDate() {
	if (dateFormat == 'datePat2') {
		document.getElementById("matchQueue.valueDateAsString").title="<fmt:message key="tooltip.ValueDateMMDDYY"/>";	
	}else{
		document.getElementById("matchQueue.valueDateAsString").title="<fmt:message key="tooltip.enterValueDate"/>";	
	}	
}
function bodyOnLoad()
{

	x5 = new XLSheet("matchQualityDetailsToday","table_1", ["String", "String", "Number", "Number", "Number","Number","Number","Number"],"22222222","false");

	x5.onsort = x5.onfilter = updateColors5;


	var dropBox1 = new SwSelectBox(document.forms[0].elements["match.id.entityId"],document.getElementById("entityDesc"));

	var dropBox2 = new SwSelectBox(document.forms[0].elements["matchQueue.currencyGrpCode"],document.getElementById("currencyDesc"));

	
	bringMyChildOnTop(window.name);
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";
	document.forms[0].dateTabInd.value = "${requestScope.dateTabInd}";
	

	document.forms[0].noIncludedMovementMatches.value = "${requestScope.noIncludedMovementMatches}";
	setTabInfo();
	// set the valueDateInForm values
	valueDateInForm = document.forms[0].elements['matchQueue.valueDateAsString'].value;	
	// If SelectedTab is All, disabled the selected tab 
	if (getSelectedTabIndex() == "8"){
		selectedTab.disabled = true;
		document.forms[0].elements["matchQueue.valueDateAsString"].value = "";
	}
	// set the selectedValueDate value
	document.forms[0].selectedValueDate.value = document.forms[0].elements["matchQueue.valueDateAsString"].value;
	document.getElementById("lastRefTime").innerText = lastRefTime;
	tooltipDate();
}

function refreshScreen(methodName){
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;	
	setTabInfo();
	var currency=document.forms[0].elements["matchQueue.currencyGrpCode"].value;
		document.forms[0].method.value="display";
	    document.forms[0].status.value = '${requestScope.status}';
		document.forms[0].elements["dbDate"].value = dbDate;
		document.forms[0].submit();

}

function refreshAfterApplyCurrencyThreshold(methodName){
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;	
	setTabInfo();
	// set the date selected value
	if (document.forms[0].elements["matchQueue.valueDateAsString"].value != "")
		document.forms[0].selectedValueDate.value = document.forms[0].elements["matchQueue.valueDateAsString"].value;
	var currency=document.forms[0].elements["matchQueue.currencyGrpCode"].value;
	document.forms[0].method.value="display";
	document.forms[0].status.value = '${requestScope.status}';
	document.forms[0].applyCurrencyThresholdInd.value = "1";
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";	  
	document.forms[0].dateTabInd.value = "${requestScope.dateTabInd}";	
	document.forms[0].elements["dbDate"].value = dbDate;
	document.forms[0].submit();
}

function refreshAfterNoIncludedMovementMatches(methodName){
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;	
	setTabInfo();
	// set the date selected value
	if (document.forms[0].elements["matchQueue.valueDateAsString"].value != "")
		document.forms[0].selectedValueDate.value = document.forms[0].elements["matchQueue.valueDateAsString"].value;
	var currency=document.forms[0].elements["matchQueue.currencyGrpCode"].value;
	document.forms[0].method.value="display";
	document.forms[0].status.value = '${requestScope.status}';
	document.forms[0].dateTabInd.value = '${requestScope.dateTabInd}';
	document.forms[0].noIncludedMovementMatchesInd.value = "1";
	if (document.forms[0].elements["matchQueue.applyCurrencyThreshold"].checked) {
	document.forms[0].applyCurrencyThreshold.value = "Y";	 
	} else {
		document.forms[0].applyCurrencyThreshold.value = "N";	
	}	
	document.forms[0].elements["dbDate"].value = dbDate;
	document.forms[0].submit();
}

function setTabInfo()
{
	document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
	document.forms[0].elements["selectedTabName"].value = getselectedtab();
}

function showEntityDropDown()
{
	document.getElementById("dropdowndiv_1").style.visibility="visible";
}

function clickLink(element,e)
{  	
	var event = (window.event|| e);
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";
	var tabIndex = getSelectedTabIndex();
	document.forms[0].dateTabInd.value = tabIndex ;
	document.forms[0].elements["valueDate"].value = document.forms[0].elements["matchQueue.valueDateAsString"].value;
	document.forms[0].elements["dateTabInd"].value = tabIndex;
	document.forms[0].noIncludedMovementMatches.value = "${requestScope.noIncludedMovementMatches}";

	var state="statuscheck";
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = element.href;
	sURL = sURL + "&menuAccessId="+document.forms[0].menuAccessId.value;
	sURL = sURL + "&valueDate="+document.forms[0].elements["valueDate"].value;
    sURL = sURL + "&applyCurrencyThreshold="+document.forms[0].applyCurrencyThreshold.value;
	
    sURL = sURL + "&noIncludedMovementMatches="+document.forms[0].noIncludedMovementMatches.value;
    sURL = sURL + "&dateTabIndFlag=N";
	
	sURL = sURL + "&dateTabInd="+(tabIndex-1);
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	
	var str=new String(oXMLHTTP.responseText);

	if(str == "false"){

		 ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.mvmQSelectionStChange"/>', YES_NO, yesFunc);
				
			}
	else if(str == "matchIdStatus")
	{
			alert('<fmt:message key="alert.mvmQSelectionStMatchIdChange"/>')
			setTabInfo();
			document.forms[0].method.value="display";
	        document.forms[0].status.value = '${requestScope.status}';
			document.forms[0].submit();
	}	
	else{


	openWindow(sURL,'movementmatchdisplayWindow','left=0,top=55,width=1350,height=570,toolbar=0, resizable=yes, scrollbars=yes,status=yes');

	}
    if(event.preventDefault) {
        event.preventDefault();
    }else{
        window.event.returnValue = false;
    } 
	return false;
}

<c:if test='${status == "M"}' >
	document.title="<fmt:message key='mvmmatqsel.title.authorised'/>";
</c:if>
<c:if test='${status == "S"}' >
	document.title="<fmt:message key='mvmmatqsel.title.suspend'/>";
</c:if>
<c:if test='${status == "C"}' >
	document.title="<fmt:message key='mvmmatqsel.title.confirmed'/>";
</c:if>

function yesFunc() {
	setTabInfo();
	document.forms[0].method.value="display";
    document.forms[0].status.value = '${requestScope.status}';
    document.forms[0].elements["dbDate"].value = dbDate;
	document.forms[0].submit();
}


function checkTabIndexToday(obj) {
	changeselected('MatchQueueTodayParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {		
		expandcontent('MatchQueueTodayParent', obj);
		dateTabInd = 0;
		// set the valueDateAsString value
		document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDate;
		submitForm('display');
		}

 }






 //This function is called when 'Today + 1' tab is pressed.

function checkTabIndexTodayPlusOne(obj) {
		changeselected('MatchQueueTodayPlusOneParent');
		var tabIndex = getSelectedTabIndex();
		var originalTabIndex = "${requestScope.selectedTabIndex}";
		document.forms[0].dateTabInd.value = tabIndex;
		if(tabIndex != originalTabIndex) {
				expandcontent('MatchQueueTodayParent', this);

				// set the valueDateAsString value
				document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusOne;
				submitForm('display');
			}
}





//This function is called when 'Today + 2' tab is pressed.

function checkTabIndexTodayPlusTwo(obj) {
	changeselected('MatchQueueTodayPlusTwoParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {
			expandcontent('MatchQueueTodayParent', this);	
			// set the valueDateAsString value
			document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusTwo;		
			submitForm('display');
			}
}

//This function is called when TodayPlusThree date tab is pressed.
function checkTabIndexTodayPlusThree(obj) {
	changeselected('MatchQueueTodayPlusThreeParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {
		expandcontent('MatchQueueTodayParent', this);
		document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusThree;
		submitForm('display');
	}
}


//This function is called when TodayPlusFour date tab is pressed.
function checkTabIndexTodayPlusFour(obj) {
	changeselected('MatchQueueTodayPlusFourParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {
		expandcontent('MatchQueueTodayParent', this);
		document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusFour;
		submitForm('display');
	}
}



//This function is called when TodayPlusFive date tab is pressed.
function checkTabIndexTodayPlusFive(obj) {
	changeselected('MatchQueueTodayPlusFiveParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {
		expandcontent('MatchQueueTodayParent', this);
		document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusFive;
		submitForm('display');
	}
}

//This function is called when TodayPlusSix date tab is pressed.
function checkTabIndexTodayPlusSix(obj) {
	changeselected('MatchQueueTodayPlusSixParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	document.forms[0].dateTabInd.value = tabIndex;
	if(tabIndex != originalTabIndex) {
		expandcontent('MatchQueueTodayParent', this);
		document.forms[0].elements["matchQueue.valueDateAsString"].value =todaySysDatePlusSix;
		submitForm('display');
	}
}




//This function is called when 'All' tab is pressed.
function clickAllTab(object)
{		

		var originalTabIndex = "${requestScope.selectedTabIndex}";	
		
			changeselected('MatchQueueAllParent');
			var tabIndex = getSelectedTabIndex();
			if(originalTabIndex != tabIndex)
			 {
				expandcontent('MatchQueueTodayParent', object);			
				// set the valueDateAsString value
				document.forms[0].elements["matchQueue.valueDateAsString"].value ="";	
				submitForm('display');				
			}
}

//This function is called when 'Selected' tab is pressed.
function clickSelectedTab(object)
{		
	if(document.forms[0].elements['matchQueue.valueDateAsString'].value == ""){
		alert('<fmt:message key="accountMonitorNew.alert.date"/>');
		return false
	}
	else{
		var originalTabIndex = "${requestScope.selectedTabIndex}";	
		changeselected('MatchQueueSelectedParent');
		var tabIndex = getSelectedTabIndex();
		if(originalTabIndex != tabIndex)
		{
			expandcontent('MatchQueueTodayParent', object);
			submitForm("display");				
		}
	}
}
 

var dateNewFlag = true;
/**
*  This function is called when theris is change in date feild
*  this funtion also validates the date field and submits the form
*  @param obj
*  @returns boolean
*/
function onDateChange(obj){
	if(dateNewFlag){
		//date validation
		return dateForValidation();
	} else {  
		dateNewFlag = true;
	}
}


/**
*  This function is called when calender key press for validating the date feild in editable mode.
*  @param obj
*  @returns boolean
*/
function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	//check for tab key
	if(event.keyCode == 9 && alertFlag != "false"){
		//date validation
		return dateForValidation();
	}
	//check for enter key
	if(event.keyCode == 13) {  
		//date validation
		dateNewFlag = false;
		return dateForValidation();
	}
}
/**
*  This function is used to validate the date
*  @returns boolean
*/
function dateForValidation(){
	var bodyrect = window.document.body.getClientRects()[0];
	if (closeFlag == true){
		if(document.forms[0].elements['matchQueue.valueDateAsString'].value != "" && validateField(document.forms[0].elements['matchQueue.valueDateAsString'],'date',dateFormat)){
		//changing the date format
			var dateNew = document.forms[0].elements['matchQueue.valueDateAsString'].value;
			if(dateFormat == "datePat2"){
				var valueDate = new Array();
				valueDate = dateNew.split("/");
				var temp = valueDate[0];
				valueDate[0] = valueDate[1];
				valueDate[1] = temp;
				dateNew = valueDate.join("/");
			}
			var days2 = (parseDate(dateNew,dateFormat).getTime())/(60*60*1000);
			//get the today date
			var days1 = (parseDate(today,dateFormat).getTime())/(60*60*1000);
			//Check for wehether the selected date exceeds the range 30 days from the todays date
			if(((days2-days1)/24) > 29){
				if (alertFlag == "false"){
				alert('<fmt:message key="accountMonitorNew.alert.exceedDate"/>');
				} else {
				alertFlag = "false";
				}
				//determine the value of date field
				document.forms[0].elements['matchQueue.valueDateAsString'].value=valueDateInForm;
				return false;
			} else {
				dateSelected = true;
				submitForm('display');
				return true;
			}
		} else {
			return false;
		}
	} else {
		closeFlag = true;
	}
}
</SCRIPT>


</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad()"
	onunload="call2()">

<form action="movementmatch.do" style="width: 1020px;" method="post">>
	<input name="method" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value="GBP">
	<input name="selectedTabIndex" type="hidden" value="1">
	<input name="selectedTabName" type="hidden"
		value="MatchQueueTodayParent">
	<input name="dbDate" type="hidden" value="">
	<!-- Start:Code modified by Vivekanandan A on 17/07/2012 for mantis1991 -->
	<input name="existingEntityId" type="hidden" value="">
	<!-- End:Code modified by Vivekanandan A on 17/07/2012 for mantis1991 -->
	<input name="status" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<input name="applyCurrencyThreshold" type="hidden">
	<input name="dateTabInd" type="hidden">
	<input name="valueDate" type="hidden">
	<input name="applyCurrencyThresholdInd" type="hidden" value="0">
	<c:set value="${sessionScope.CDM}" var="CDM" />

	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<input name="selectedValueDate" type="hidden" value="">
	<input name="noIncludedMovementMatches" type="hidden">
	<input name="noIncludedMovementMatchesInd" type="hidden" value="0">

	<input type="hidden" name="matchQueue.status" value="${matchQueue.status}" />

	<div id="MovementMatch"
		style="position: absolute; left: 20px; top: 20px; width: 1003; height: 58px; border: 2px outset;"
		color="#7E97AF">

	<div id="MovementMatch"
		style="position: absolute; left: 5px; top: 2px; width: 995px; height: 400;">
	<table width="990" border="0" cellpadding="0" cellspacing="0"
		height="30">

		<tr color="black" border="0" height="24">
			<td width="140"><b><fmt:message key="matchQuality.entityId" /></b></td>

			<td width="140">
				<select name="match.id.entityId" onchange="submitForm('display')" style="width:140px" title="${tooltip.entityId}" tabindex="1">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option value="${entity.value}" <c:if test="${entity.value == requestScope.match.id.entityId}">selected</c:if>>${entity.label}</option>
			</c:forEach>
				</select>
			</td>
			<td width="25">&nbsp;</td>
			<td width="321"><span id="entityDesc" class="spantext"></td>
			<td width="50"></td>

			<td width="150"></td>
			<td width="300" align="right"><b><fmt:message key="queue.noIncludedMovementMatches" /></b></td>
			<td width="10">&nbsp;</td>
			<td width="14">
				<input
				type="checkbox"
				name="matchQueue.noIncludedMovementMatches"
				value="Y"
				<c:if test="${requestScope.matchQueue.noIncludedMovementMatches == 'Y'}">checked</c:if>
				style="width:13px;"
				title="${tooltip.noIncludedMovementMatches}"
				class="htmlTextAlpha"
				tabindex="2"
				onclick="javascript:refreshAfterNoIncludedMovementMatches('refresh')"
			/>
			</td>
		</tr>
		<tr height="24">
			<td width="140" style="padding-bottom: 25px;"><b><fmt:message key="sweep.currencyGroup" /></b></td>

			<td width="140" style="padding-bottom: 25px;">
				<select
					tabindex="2"
					style="width:140px;"
					name="matchQueue.currencyGrpCode"
					title="${tooltip.selectCuurencyGrp}"
					onchange="submitForm('display')">
					<c:forEach var="currency" items="${requestScope.currencyGroupList}">
						<option value="${currency.value}"
							<c:if test="${currency.value == requestScope.matchQueue.currencyGrpCode}">selected</c:if>>
							${currency.label}
						</option>
					</c:forEach>
				</select>
			</td>

			<td width="25">&nbsp;</td>
			<td width="321" style="padding-bottom: 25px;"><span id="currencyDesc" class="spantext"></td>
			<td width="50" style="padding-bottom: 25px;"><b><fmt:message key="date" /></b></td>
			<td style="width: 150px; padding-bottom: 20px;">
				<input
					type="text"
					name="matchQueue.valueDateAsString"
					class="htmlTextNumeric"
					maxlength="10"
					value="${matchQueue.valueDateAsString}"
					tabindex="2"
					onkeydown="return onDateKeyPress(this,event);"
					onblur="return onDateChange(this);"
					style="width:80px; margin-bottom: 5px; height: 22px;" />
				<a
					name="datelink"
					id="datelink"
					tabindex="5"
					onclick="cal.select(document.forms[0].elements['matchQueue.valueDateAsString'], 'datelink', dateFormatValue); return false;">
					<img
						title="<fmt:message key='tooltip.selectDate'/>"
						src="images/calendar-16.gif" />
				</a>
			</td>


			<td width="300" style="padding-bottom: 25px;" align="right">
					<b><fmt:message key="queue.applyCurrencyThreshold" /></b>
				</td>
				<td width="10">&nbsp;</td>
				<td width="14" style="padding-bottom: 25px;">
					<input
						type="checkbox"
						name="matchQueue.applyCurrencyThreshold"
						value="Y"
						<c:if test="${requestScope.matchQueue.applyCurrencyThreshold == 'Y'}">checked</c:if>
						style="width:13px;"
						title="<fmt:message key='tooltip.applyCurrencyThreshold'/>"
						class="htmlTextAlpha"
						tabindex="2"
						onclick="javascript:refreshAfterApplyCurrencyThreshold('refresh');" />
				</td>
		</tr>
	</table>
	</div>
	</div>


	<div id="ddimagetabs"
		style="position: absolute; left: 20px; top: 85px; width: 900px; height: 20px;">

			<c:choose>
			<c:when test="${requestScope.isBusinessDayForToday}">
				<a id="todayTab"
				   onmouseout="revertback('MatchQueueTodayParent',this);"
				   onmouseover="changecontent('MatchQueueTodayParent',this)"
				   tabindex="4"
				   onClick="checkTabIndexToday(this);">
				   <b>${requestScope.todayTablabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a id="todayTab"
				   onmouseout="revertback('MatchQueueTodayParent',this);"
				   onmouseover="changecontent('MatchQueueTodayParent',this)"
				   style="color: #A0A0A0"
				   tabindex="4"
				   onClick="checkTabIndexToday(this);">
				   <b>${requestScope.todayTablabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusOne}">
				<a onmouseout="revertback('MatchQueueTodayPlusOneParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusOneParent',this)"
				   tabindex="5"
				   onClick="checkTabIndexTodayPlusOne(this);">
				   <b>${requestScope.todayTabPlusOnelabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusOneParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusOneParent',this)"
				   style="color: #A0A0A0"
				   tabindex="5"
				   onClick="checkTabIndexTodayPlusOne(this);">
				   <b>${requestScope.todayTabPlusOnelabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusTwo}">
				<a onmouseout="revertback('MatchQueueTodayPlusTwoParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusTwoParent',this)"
				   tabindex="6"
				   onClick="checkTabIndexTodayPlusTwo(this);">
				   <b>${requestScope.todayTabPlusTwolabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusTwoParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusTwoParent',this)"
				   style="color: #A0A0A0"
				   tabindex="6"
				   onClick="checkTabIndexTodayPlusTwo(this);">
				   <b>${requestScope.todayTabPlusTwolabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusThree}">
				<a onmouseout="revertback('MatchQueueTodayPlusThreeParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusThreeParent',this)"
				   tabindex="7"
				   onClick="checkTabIndexTodayPlusThree(this);">
				   <b>${requestScope.todayTabPlusThreelabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusThreeParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusThreeParent',this)"
				   style="color: #A0A0A0"
				   tabindex="7"
				   onClick="checkTabIndexTodayPlusThree(this);">
				   <b>${requestScope.todayTabPlusThreelabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusFour}">
				<a onmouseout="revertback('MatchQueueTodayPlusFourParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusFourParent',this)"
				   tabindex="8"
				   onClick="checkTabIndexTodayPlusFour(this);">
				   <b>${requestScope.todayTabPlusFourlabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusFourParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusFourParent',this)"
				   style="color: #A0A0A0"
				   tabindex="8"
				   onClick="checkTabIndexTodayPlusFour(this);">
				   <b>${requestScope.todayTabPlusFourlabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusFive}">
				<a onmouseout="revertback('MatchQueueTodayPlusFiveParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusFiveParent',this)"
				   tabindex="9"
				   onClick="checkTabIndexTodayPlusFive(this);">
				   <b>${requestScope.todayTabPlusFivelabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusFiveParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusFiveParent',this)"
				   style="color: #A0A0A0"
				   tabindex="9"
				   onClick="checkTabIndexTodayPlusFive(this);">
				   <b>${requestScope.todayTabPlusFivelabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<c:choose>
			<c:when test="${requestScope.isBusinessDayForTodayPlusSix}">
				<a onmouseout="revertback('MatchQueueTodayPlusSixParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusSixParent',this)"
				   tabindex="10"
				   onClick="checkTabIndexTodayPlusSix(this);">
				   <b>${requestScope.todayTabPlusSixlabel}</b>
				</a>
			</c:when>
			<c:otherwise>
				<a onmouseout="revertback('MatchQueueTodayPlusSixParent',this);"
				   onmouseover="changecontent('MatchQueueTodayPlusSixParent',this)"
				   style="color: #A0A0A0"
				   tabindex="10"
				   onClick="checkTabIndexTodayPlusSix(this);">
				   <b>${requestScope.todayTabPlusSixlabel}</b>
				</a>
			</c:otherwise>
		</c:choose>

		<a id="allTab"
		   onmouseout="revertback('MatchQueueAllParent',this);"
		   onmouseover="changecontent('MatchQueueAllParent',this)"
		   tabindex="11"
		   onClick="clickAllTab(this);">
		   <b><fmt:message key="addjob.All" /></b>
		</a>


	<a id="selectedTab"
		onmouseout="revertback('MatchQueueSelectedParent',this);"
		onmouseover="changecontent('MatchQueueSelectedParent',this)"
		tabindex="12" onClick="clickSelectedTab(this);"><b><fmt:message key="accountmonitor.selected" /></b></a></div>
	<div id="Line"
		style="position: absolute; left: 349px; top: 103px; width: 397px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
	</div>


	<DIV id="tabcontentcontainer"
		style="position: absolute; left: 20px; top: 106px; width: 1003px; height: 434px;">
	<div id="MatchQueueTodayParent" color="#7E97AF"
		style="position: absolute; border: 0px outset; left: 0px; top: 1px; width: 1003px; height: 432px; display: none">
	<div id="MatchQueueToday"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1003px; height: 10px;">
	<table class="sort-table" id="table_1" bgcolor="#B0AFAF" width="981px"
		border="0" cellspacing="1" cellpadding="0" height="20">
		<thead>
			<tr>
				<td height="20px" width="70px" style ="border-left-width: 0px; padding-left: 15px"
					title='<fmt:message key="tooltip.sortCurrencyCode"/>'
					align="center"><b><fmt:message key="currency.id" /></b></td>
				<td height="20px" width="280px" style ="padding-left: 55px"
					title='<fmt:message key="tooltip.sortCurrencyName"/>'
					align="center"><b><fmt:message key="matchQueue.CcyName" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityA"/>' align="center"><b><fmt:message key="matchQueue.A" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityB"/>' align="center"><b><fmt:message key="matchQueue.B" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityC"/>' align="center"><b><fmt:message key="matchQueue.C" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityD"/>' align="center"><b><fmt:message key="matchQueue.D" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityE"/>' align="center"><b><fmt:message key="matchQueue.E" /></b></td>
				<td height="20px" width="57px" style ="padding-left: 17px"
					title='<fmt:message key="tooltip.sortQualityZ"/>' align="center"><b><fmt:message key="matchQueue.Z" /></b></td>
			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 1px; width: 998px; height: 425px; overflowY: scroll">
	<div id="MatchQueueToday"
		style="position: absolute; z-index: 99; left: 1px; top: 20px; width: 981px; height: 10px;">
	<table class="sort-table" id="matchQualityDetailsToday" width="981" border="0" cellspacing="1" cellpadding="0" height="405">
			<tbody>
				<c:forEach var="matchQualityDetailsToday" items="${requestScope.matchQualityDetailsToday}" varStatus="status">
					<tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">
						<input type="hidden" name="matchQualityDetailsToday.currencyCode" value="${matchQualityDetailsToday.currencyCode}" />
						<td align="left" width="70px">${matchQualityDetailsToday.currencyCode}&nbsp;</td>
						<td width="285px">${matchQualityDetailsToday.currencyName}&nbsp;</td>
						<td align="right" width="60px">
							<c:if test="${not empty matchQualityDetailsToday.qualityA}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryAurlParams}">
									${matchQualityDetailsToday.qualityA}&nbsp;
								</a>
							</c:if>
						</td>
						<td align="right" width="60px">
							<c:if test="${not empty matchQualityDetailsToday.qualityB}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryBurlParams}">
									${matchQualityDetailsToday.qualityB}&nbsp;
								</a>
							</c:if>
						</td>
						<td align="right" width="60px">
							<c:if test="${not empty matchQualityDetailsToday.qualityC}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryCurlParams}">
									${matchQualityDetailsToday.qualityC}&nbsp;
								</a>
							</c:if>
						</td>
						<td align="right" width="60px">
							<c:if test="${not empty matchQualityDetailsToday.qualityD}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryDurlParams}">
									${matchQualityDetailsToday.qualityD}&nbsp;
								</a>
							</c:if>
						</td>
						<td align="right" width="57px">
							<c:if test="${not empty matchQualityDetailsToday.qualityE}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryEurlParams}">
									${matchQualityDetailsToday.qualityE}&nbsp;
								</a>
							</c:if>
						</td>
						<td align="right" width="60px">
							<c:if test="${not empty matchQualityDetailsToday.qualityZ}">
								<a onclick="clickLink(this,event);" href="movementmatchdisplay.do?${matchQualityDetailsToday.summaryZurlParams}">
									${matchQualityDetailsToday.qualityZ}&nbsp;
								</a>
							</c:if>
						</td>
					</tr>
				</c:forEach>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="8"></td>
				</tr>
			</tfoot>
		</table>

	</div>
	</div>
	</div>

	</div>
	<div id="MovementMatch"
		style="position: absolute; left: 955; top: 552; width: 70; height: 15px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a tabindex="5" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Movement Match Queue '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " style="padding-left: 5px"  name="Help" border="0"></a></td>

			<td align="right" id="Print"><a tabindex="5"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>


	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 545; width: 1003; height: 38px; visibility: visible;">
	<div id="Currency"
		style="position: absolute; left: 6; top: 4; width: 817; height: 15px; visibility: visible;">

	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="10">
		<tr>

			<td id="refreshbutton" width="70"
				title='<fmt:message key="tooltip.refreshWindow"/>'><a
				tabindex="3" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:refreshScreen('refresh')"><fmt:message key="button.Refresh" /></a></td>
			<td id="closebutton" width="70"
				title='<fmt:message key="tooltip.close"/>'><a tabindex="4"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this);closeFlag=false;"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><fmt:message key="button.close" /></a></td>

		</tr>
	</table>
	</div>
	<table height="33"><tr>
		<td id="lastRefTimeLable" width="812px" align="right" >
		<b><fmt:message key="label.lastRefTime"/></b>
		</td>			
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">				
		</td>
		</tr>
	</table>
	<div
		style="position: absolute; left: 6; top: 4; width: 705px; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>

			<td id="refreshdisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.Refresh" /></a></td>


		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>

</html>
