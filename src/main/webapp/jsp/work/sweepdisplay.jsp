<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key = "sweepDisplay.title.window"/> </title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

 
<SCRIPT language="JAVASCRIPT">
	var dateFormat = "${sessionScope.CDM.dateFormat}";
var requestURL = new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";  
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
 var oXMLHTTP = new XMLHttpRequest();

var enteredSweepId=null; 
var currencyAccessInd="${requestScope.currGrpAccess}";
var movementAmendedFlag=false;
var archiveId =  '<%=SwtUtil.isEmptyOrNull(request.getParameter("archiveId"))?"":request.getParameter("archiveId")%>';
function bodyOnLoad() {
	//Checks the archive Id value, if it is have any value then disable the msg button
	if (archiveId != "" && archiveId != "null") {

		<c:if test="${requestScope.fromArchive != 'yes'}">
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
		</c:if>
	}


	<c:if test="${requestScope.ifNosweepDetailsPresent == 'true'}">
	document.forms[0].elements['sweepsearch.id.sweepId'].value = "";

	var entityId = "${requestScope.entityId}";

	if (entityId == null || entityId == "") {
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

		for (i = 0; i < document.forms[0].elements.length; i++) {
			document.forms[0].elements[i].value = "";
		}
	}
	alert('<fmt:message key="sweepId.doesnotmatch"/>');
	</c:if>

	<c:if test="${requestScope.sweepDoesNotHaveAccess == 'true'}">

	document.forms[0].elements['sweepsearch.id.sweepId'].value = "";

	var entityId = "${requestScope.entityId}";

	if (entityId == null || entityId == "") {
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

		for (i = 0; i < document.forms[0].elements.length; i++) {
			document.forms[0].elements[i].value = "";
		}
	}

	</c:if>


	<c:if test="${requestScope.methodName == 'view'}">
	var entityId = "${requestScope.entityId}";
	document.forms[0].selectedentityId.value = entityId;
	if (currencyAccessInd == 2) {
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

	}
	</c:if>

	<c:if test="${requestScope.methodName != 'view'}">
	//Set the focus to the sweep id of text field in loading of screen
	this.window.focus();
	document.forms[0].elements["sweepsearch.id.sweepId"].focus();

	var entityId = "${requestScope.entityId}";

	if (entityId != null) {
		document.forms[0].selectedentityId.value = entityId;

	}
	if (entityId == null || entityId == "") {
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
	} else {

		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsenablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgenablebutton").innerHTML;

	}
	if (currencyAccessInd == 2) {
		document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsdisablebutton").innerHTML;
		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

	}
	</c:if>

	enteredSweepId = document.forms[0].elements['sweepsearch.id.sweepId'].value;

}

/**
 * This function is used to validate the Sweep id if entered sweep id is not
  * in the database then display the alet msg to user  
  * 
  * @param methodName
  *           
  */

  function submitForm(methodName,e){
   var event = (window.event|| e);
  //Getting sweep id from form elements
  var element = document.forms[0].elements["sweepsearch.id.sweepId"];
	if((movementAmendedFlag == true)||(element.value.length > 0 && event.keyCode && (event.keyCode == 9 || event.keyCode == 13)))
	{
		var thePat = PatternsDict['numberPat'];
		var gotIt = thePat.exec(element.value);
		if( gotIt != null) {
			document.forms[0].method.value = methodName;
			document.forms[0].selectedSweepId.value = element.value;
			//Enable the details,notes, msgs button based on sweep id
			if(document.forms[0].selectedSweepId.value != null){

			  document.getElementById("detailsbutton").innerHTML = document.getElementById("detailsenablebutton").innerHTML;
			  document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
			  document.getElementById("msgbutton").innerHTML = document.getElementById("msgenablebutton").innerHTML;
			 }
			document.forms[0].submit();
		} else {
			if (window.event)
				window.event.returnValue = false;
			alert("Please enter a valid "+PatternsMetaData['numberPat']);
			/* Start:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			document.forms[0].elements["sweepsearch.id.sweepId"].value="";
		   //calling the emptySweepDetails method to reload the Sweep display screen 
			emptySweepDetails('populate');
			/* End:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			element.focus();
			return false;
		}	
		
	}
}

function sweep(){
		var param='sweepdetail.do?method=displayQueue&entid=';
		param +=document.forms[0].selectedentityId.value;
		param +='&swpid='+document.forms[0].elements["sweepsearch.id.sweepId"].value;
		param += '&currencyAccess=' + currencyAccessInd;
		param +='&qname= C';
		param += '&archiveId='+ document.forms[0].archiveId.value;
		return  param;
}

function showSweepNotes(methodName){
var param = 'notes.do?method='+methodName+'&selectedSweepId=';			 
param += document.forms[0].elements["sweepsearch.id.sweepId"].value;
param += '&entityCode=' + document.forms[0].selectedentityId.value;
param += '&currencyAccess=' + currencyAccessInd;
param += '&archiveId='+ document.forms[0].archiveId.value;
return  param;
	
}
/**
  * This method is used to validate the sweep Id is valid or not
  *
  *@Param buttonValue
*/

function checkingMovementId(buttonValue) {
	/*Condition to validate the sweep id is integer or not*/
	if (validateField(document.forms[0].elements["sweepsearch.id.sweepId"],'sweepId','numberPat')){
		/*Condition to validate the sweep id is empty*/
		if (document.forms[0].elements["sweepsearch.id.sweepId"].value.trim() == ""){
			/*Throws the alert message*/	
			alert('<fmt:message key="sweepId.doesnotmatch"/>');
			/* Start:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			 //calling the emptySweepDetails method to reload the Sweep display screen 
			emptySweepDetails('populate');
			return false;
		}
		else{
			var oXMLHTTP = new XMLHttpRequest();	
			var sURL=requestURL + appName+"/sweepsearch.do?method=checkSweepId";
			
			sURL = sURL + "&sweepId="+document.forms[0].elements["sweepsearch.id.sweepId"].value;
			if(archiveId)
				sURL = sURL + "&archiveId="+archiveId;
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var sweepStatus=new String(oXMLHTTP.responseText);
			/*Conditon to validate the sweep id is valid or not*/
			if (sweepStatus == "false"){
				alert('<fmt:message key="sweepId.doesnotmatch"/>');
				 //calling the emptySweepDetails method to reload the Sweep display screen 
				emptySweepDetails('populate');
				return false;
			}
			
		else if(enteredSweepId != null && enteredSweepId != document.forms[0].elements["sweepsearch.id.sweepId"].value) {
			alert('<fmt:message key="sweepId.alert.sweepIdamended"/>');
			movementAmendedFlag = true;
			submitForm('populate',e);
			movementAmendedFlag = false;
			return false;
				
		}
			else{
				/*Condition to check the clicked button is ''Search'*/
				if(buttonValue =='searchbutton')
					/*Open Sweep Search screen*/
					  	javascript:openWindow(sweepSearch(),'sweepsearchWindow','left=50,top=190,width=911,height=488,toolbar=0, resizable=yes, scrollbars=yes','true');
				     /*Condition to check the clicked button is ''Detils'*/
				else if(buttonValue == 'detailsbutton')
					/*Open Sweep Details screen*/
					javascript:openWindow(sweep(),'sweepdetailsWindow','left=50,top=190,width=1031,height=635,toolbar=0, resizable=yes, scrollbars=yes','true');
				/*Condition to check the clicked button is 'Notes'*/
				else if(buttonValue == 'notesbutton')
				/*Open Sweep Notes window*/
					javascript:openWindow(showSweepNotes('showSweepNotes'),'sweepNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
				/*Condition to check the clicked button is ''Msgs'*/
				else if(buttonValue == 'msgbutton')
				/*Open Sweep Message button*/
				javascript:openWindow(sweepMessage(),'sweepMessageWindow','left=50,top=190,width=560,height=445,toolbar=0, resizable=yes, scrollbars=yes','true');
				return true;
			}
		}
	}
	 //calling the emptySweepDetails method to reload the Sweep display screen 
	emptySweepDetails('populate');
	
}
/** 
 *
 * This method is used to submit empty form for reloading Sweep display screen when Sweep id is invalid
 * @param method
 */
function emptySweepDetails(method){
		//Empty all form elements
		 for(i = 0;i < document.forms[0].elements.length;i++)
		{
		  document.forms[0].elements[i].value = "";					 	
		  }
		 document.forms[0].method.value = method;
		 //Submitting Form to empty the sweep details when sweepId is invalid
		 document.forms[0].submit();
	}
	/* End:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
function sweepSearch(){

	var param='sweepsearch.do?method=display';
	return  param;
}

function sweepMessage(){

 var param='sweepdetail.do?method=sweepMessageDisplay';
 param += '&sweepId='+document.forms[0].elements['sweepsearch.id.sweepId'].value;
 return  param;
}

</SCRIPT>

</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="sweepsearch.do" method="post">

<input name="method" type="hidden" value="display">
<input name="selectedSweepId" type="hidden" value="">
<input name="selectedentityId" type="hidden" value="">
<input name="archiveId" type="hidden" value="${archiveId}">

<c:set var="CDM" value="${sessionScope.CDM}" />

<div id="SweepSearch" style="position:absolute; left:20px; top:20px; width:740px; height:650px; border:2px outset;"  color="#7E97AF">
<div id="SweepSearch" style="position:absolute; left:8px; top:4px; width:735px; height:100px;">


<div id="SweepSearch" style="position:absolute; left:0px; top:0px; width:720px; height:85px;">
   <div style="left:8px; top:4px;height: 345px;">

    <fieldset style="border:2px groove;"> <legend><fmt:message key="sweepsearch.sweepdetails"/></legend>

      <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
       <tr height="25">
    
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.sweepId"/></b></td>
		   <td width="160px" align="right">
		 <c:choose>
			<c:when test="${requestScope.methodName == 'view'}">
				<input type="text" title="<fmt:message key='tooltip.EnterSweepID'/>" name="sweepsearch.id.sweepId"
					   style="width:120px;" tabindex="1" class="htmlTextNumeric" readonly="readonly"
					   value="${sweepsearch.id.sweepId}" />
			</c:when>
			<c:otherwise>
				<input type="text" title="<fmt:message key='tooltip.EnterSweepID'/>" name="sweepsearch.id.sweepId"
					   style="width:120px;" tabindex="1" class="htmlTextNumeric" maxlength="12"
					   onkeydown="javascript:submitForm('populate',event)" value="${sweepsearch.id.sweepId}" />
			</c:otherwise>
		</c:choose>

		<td width="100px" align="right">
			<c:choose>
				<c:when test="${requestScope.methodName == 'view'}">
					<input type="text" class="textlabel" name="sweepsearch.sweepStatus"
						   style="width:80px;" readonly="readonly"  value="${sweepsearch.sweepStatus}" />
				</c:when>
				<c:otherwise>
					<input type="text" class="textlabel" name="sweepsearch.sweepStatus"
						   style="width:80px;" value="${sweepsearch.sweepStatus}" />
				</c:otherwise>
			</c:choose>
	   </td>
	   <td width="132px"></td>
	   <td width="160px"></td>
	   </tr>
	    <tr height="25">
	<td width="132px">&nbsp;<b><fmt:message key="sweep.valueDate"/></b></td>
           <td width="160px" align="right">
			<input type="text" class="textlabel1" name="sweepsearch.valueDateAsString"
       				style="width:80px;" readonly="readonly"  value="${sweepsearch.valueDateAsString}" />
		  </td>
		  <td width="100px" ></td>
		<td width="132px"></td>
	   <td width="160px"></td>
	</tr>
	    <tr height="25"> 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.sweepamt"/></b></td>
			<td width="160px" align="right">
				<input type="text" class="textlabel1" name="sweepsearch.amt" style="width:120px;" readonly="readonly"  value="${sweepsearch.amt}" />
			</td>
			<td width="100px" align="right">
				<input type="text" class="textlabel" name="sweepsearch.currencyCode" style="width:80px;" readonly="readonly"  value="${sweepsearch.currencyCode}" />
			</td>
			<td width="132px"></td>
	   <td width="160px"></td>
		  </tr>
		
    
		   <tr height="25">
    	  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.generatedby"/></b></td>
           <td width="160px" align="right">
			<input type="text" class="textlabel1" name="sweepsearch.inputUser" style="width:144px;" readonly="readonly"  value="${sweepsearch.inputUser}" />
		  </td>
	 <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
        </tr>
		    <tr height="25">
		 <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.time"/></b></td>
		<td width="160px" align="right">
			<input type="text" class="textlabel1" name="sweepsearch.inputtime" style="width:160px;" readonly="readonly"  value="${sweepsearch.inputtime}" />
	   </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
		   </tr>
		     <tr height="25" >
      <td width="132px">&nbsp;<b><fmt:message key="sweep.postcutoff1"/></b></td>
		<td width="160px" align="right">
			<input type="checkbox" name="sweepsearch.generatedpostcutoffflg" value="Y" style="width:13px;" disabled="true"
			<c:if test="${requestScope.sweepsearch.generatedpostcutoffflg == 'Y'}">checked</c:if> />
          </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
	    
		  </tr> 
		   <tr height="25" >
      <td width="132px">&nbsp;<b><fmt:message key="sweepDetail.additionalRefCut"/></b></td>
		<td width="160px" align="right">
			<input type="text" class="textlabel1" name="sweepsearch.additionalReference" style="width:144px;" readonly="readonly"  value="${sweepsearch.additionalReference}" />

          </td> <td width="100px"></td>
	  <td width="132px"></td>
	   <td width="160px"></td>
	    
		  </tr>
		  </table>
		  
		   <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
		  <tr height="25">	 
		  <td width="105px" >&nbsp;<b><fmt:message key="sweep.sweepuetr1"/></b></td>
           <td width="350px"  align="left">
			<input type="text" class="textlabelalpha" titleKey="tooltip.sweep.uetr1" name="sweepsearch.sweepUetr1" style="width:350px;" readonly="readonly"  value="${sweepsearch.sweepUetr1}" />
          </td>
		  </tr>
		  
		  <tr height="25">	          
          <td width="105px">&nbsp;<b><fmt:message key="sweep.sweepuetr2"/></b></td>
           <td width="350px" align="left">
			<input		type="text"		class="textlabelalpha"		titleKey="tooltip.sweep.uetr2"		name="sweepsearch.sweepUetr2"
				style="width:350px;"		readonly="readonly" 		value="${sweepsearch.sweepUetr2}"	/>
          </td>
		  </tr>
		  </table>
		  
		   <table width="700px" border="0" cellpadding="0"  cellspacing="0" class="content">
		   <tr height="25">	 
		  <td width="132px" >&nbsp;<b style="text-decoration: underline;"><fmt:message key="account.debitLeg"/></b></td>
            <td width="160px" align="right">
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px" style="text-decoration: underline;" ><b><fmt:message key="account.creditLeg"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
           </td>
		  </tr>
		     <tr height="25">
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.entity"/></b></td>
           <td width="160px" align="right">
			<input
					type="text"
					class="textlabel1"
					name="sweepsearch.entityIdDr"
					style="width:120px;"
					readonly="readonly"
					value="${sweepsearch.entityIdDr}"
				/>
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.entity"/></b></td>
		  <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
			<input
					type="text"
					class="textlabel1"
					name="sweepsearch.entityIdCr"
					style="width:120px;"
					readonly="readonly"
					value="${sweepsearch.entityIdCr}"
				/>
		  </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b ><fmt:message key="sweepsearch.debited"/></b></td>
          <td width="160px" align="right">
			<input
					type="text"
					class="textlabel1"
					name="sweepsearch.accountIdDr"
					style="width:120px;"
					readonly="readonly"
					value="${sweepsearch.accountIdDr}"
				/>
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.credited"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
				<input
					type="text"
					class="textlabel1"
					name="sweepsearch.accountIdCr"
					style="width:120px;"
					readonly="readonly"
					value="${sweepsearch.accountIdCr}"
				/>
           </td>
        </tr>
			
  			<tr height="25">	 
		  <td width="132px">&nbsp;<b><fmt:message key="account.settleMethodSweep"/></b></td>
          <td width="160px" align="right">
			<input
				type="text"
				class="textlabel1"
				name="sweepsearch.settleMethodDR"
				style="width:120px;"
				readonly="readonly"
				value="${sweepsearch.settleMethodDR}"
			/>
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="account.settleMethodSweep"/></b></td>
		   <td width="160px" align="right">&nbsp;&nbsp;&nbsp;
				<input
					type="text"
					class="textlabel1"
					name="sweepsearch.settleMethodCR"
					style="width:120px;"
					readonly="readonly"
					value="${sweepsearch.settleMethodCR}"
				/>
           </td>
        </tr>
        
		  <tr height = "1"><td colspan="5"/></tr>
      </table>
       </fieldset>
       </div>
<div style="left:8px; top:0px ;width:720px;height: 170px;">
    <fieldset style="border:2px groove;height: 200px;"> <legend><fmt:message key="sweepsearch.mvmntdetails"/></legend>
      <table width="700" border="0" cellpadding="0" cellspacing="0" class="content">
		   <tr height="25">
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.bookcode"/></b></td>
           <td width="160px" align="right">
<input class="textlabel1"  value="${bookcodedr}" name="bookCodeDr" style="width:144px;" readonly="readonly" />
          </td>
		<td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.bookcode"/></b></td>
		  <td width="160px" align="right">
           <input class ="textlabel1" value="${bookcodecr}" name="bookCodeCr"  readonly="readonly" />
          </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.mvmntId"/></b></td>
          <td width="160px" align="right">
			<input
				type="text"
				class="textlabel1"
				name="sweepsearch.movementIdDr"
				style="width:120px;"
				readonly="readonly"
				value="${sweepsearch.movementIdDr}"
			/>
		  </td>
		   <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.mvmntId"/></b></td>
		   <td width="160px" align="right">
			<input
				type="text"
				class="textlabel1"
				name="sweepsearch.movementIdCr"
				style="width:120px;"
				readonly="readonly"
				value="${sweepsearch.movementIdCr}"
			/>

		  </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.matchId"/></b></td>
          <td width="160px" align="right">
		<input class ="textlabel1" value="${matchiddr}" name="matchIdDr"  style="width:120px;" readonly="readonly" />
          </td>
		    <td width="100px">&nbsp;</td>
                <td width="132px"><b><fmt:message key="sweepsearch.matchId"/></b></td>
		    <td width="160px" align="right">
			<input class ="textlabel1" value="${matchidcr}" name="matchIdDr" style="width:120px;" readonly="readonly" />
		 </td>
        </tr>
		  <tr height="25">	 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.postlevel"/></b></td>
          <td width="160px" align="right">
			<input  class ="textlabel1" value="${positionleveldr}" name="poslevelDr" style="width:140px;"  readonly="readonly" />
          </td>
		    <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.postlevel"/></b></td>
     	   <td width="160px" align="right">
		  <input class ="textlabel1" value="${positionlevelcr}" name="poslevelCr"  style="width:140px;" readonly="readonly" />
		  </td>
		</tr>
		  <tr height="25"> 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.predictstatus"/></b></td>
           <td width="160px" align="right">
 <input  class ="textlabel1" value="${predictstatusdr}" name="predictstatusdr"  style="width:144px;"  readonly="readonly" />
          </td>
            <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.predictstatus"/></b></td>
		   <td width="160px" align="right">
		   <input class ="textlabel1" value="${predictstatuscr}" name="predictstcr" style="width:144px;" readonly="readonly" />
		  </td>
        </tr>
        <tr height="25"> 
		  <td width="132px">&nbsp;<b><fmt:message key="sweepsearch.externalstatus"/></b></td>
           <td width="160px" align="right">
 <input  class ="textlabel1" value="${externalstatusdr}" name="externalstatusdr"  style="width:144px;"  readonly="readonly" />
          </td>
            <td width="100px">&nbsp;</td>
          <td width="132px"><b><fmt:message key="sweepsearch.externalstatus"/></b></td>
		   <td width="160px" align="right">
		   <input class ="textlabel1" value="${externalstatuscr}" name="externalstatuscr" style="width:144px;" readonly="readonly" />
		  </td>
        </tr>
		<tr height = "1"><td colspan="5"/></tr>
      </table>
       </fieldset>
	   </div>
   	<div style="left:8px; top:4px;height: 120px;">
  	  <fieldset style="border:2px groove;">
			<legend><fmt:message key="sweepsearch.sweephistory"/></legend>

			<table width="700" border="0" cellpadding="0" cellspacing="0" class="content">
				<tr height="25">
					<td width="132px">&nbsp;<b><fmt:message key="sweepsearch.submittedby"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.submitUser"
							style="width:144px;"
							readonly="readonly"
							value="${sweepsearch.submitUser}"
						/>
					</td>
					<td width="100px">&nbsp;</td>
					<td width="132px"><b><fmt:message key="sweepsearch.authorizedby"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.authorizedUser"
							style="width:120px;"
							readonly="readonly"
							value="${sweepsearch.authorizedUser}"
						/>
					</td>
				</tr>
				<tr height="25px">
					<td width="132px">&nbsp;<b><fmt:message key="sweepsearch.sweepamt"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.submitSweepAmtasstring"
							style="width:120px;"
							readonly="readonly"
							value="${sweepsearch.submitSweepAmtasstring}"
						/>
					</td>
					<td width="100px">&nbsp;</td>
					<td width="132px"><b><fmt:message key="sweepsearch.sweepamt"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.authorizeSweepAmtasstring"
							style="width:120px;padding-right:5px;"
							readonly="readonly"
							value="${sweepsearch.authorizeSweepAmtasstring}"
						/>
					</td>
				</tr>
				<tr height="25px">
					<td width="132px">&nbsp;<b><fmt:message key="sweepsearch.time"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.submittedtime"
							style="width:160px;"
							readonly="readonly"
							value="${sweepsearch.submittedtime}"
						/>
					</td>
					<td width="100px">&nbsp;</td>
					<td width="132px"><b><fmt:message key="sweepsearch.time"/></b></td>
					<td width="160px" align="right">
						<input
							type="text"
							class="textlabel1"
							name="sweepsearch.authorisetime"
							style="width:160px;"
							readonly="readonly"
							value="${sweepsearch.authorisetime}"
						/>
					</td>
				</tr>
				<tr height="25px">
					<td width="132px">&nbsp;<b><fmt:message key="sweep.postcutoff1"/></b></td>
					<td width="160px" align="right">
						<input
							type="checkbox"
							name="sweepsearch.submittedpostcutoffflg"
							value="Y"
							style="width:13px;"
							<c:if test="${requestScope.sweepsearch.submittedpostcutoffflg == 'Y'}">checked</c:if>
							disabled="true"
						/>
					</td>
					<td width="100px">&nbsp;</td>
					<td width="132px"><b><fmt:message key="sweep.postcutoff1"/></b></td>
					<td width="160px" align="right">
						<input
							type="checkbox"
							name="sweepsearch.authorisedpostcutoffflg"
							style="width:13px;"
							value="Y"
							<c:if test="${requestScope.sweepsearch.authorisedpostcutoffflg == 'Y'}">checked</c:if>
							disabled="true"
						/>
					</td>
				</tr>
				<tr height="1">
					<td colspan="5"></td>
				</tr>
			</table>
		</fieldset>

	  </div>
</div>
</div>

<div id="MovementSearch" style="position:absolute ;left:650; top:665px; width:70px; height:39px; visibility:visible;">

	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Sweep Display'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
		 </td>

			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key = "tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:-2; top:660; width:740px; height:39px; visibility:visible;">

<div id="manual" style="position:absolute; left:0; top:4; width:715px; height:15px; visibility:visible;">
    <c:choose>
        <c:when test="${requestScope.methodName != 'view'}">
            <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
        </c:when>
        <c:otherwise>
            <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
        </c:otherwise>
    </c:choose>
        <tr>
            <td id="detailsbutton" width="70px">
                <c:if test="${requestScope.methodName == 'view'}">
                    <a tabindex="2" title="<fmt:message key='tooltip.showSweepDetails'/>"
                       onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                       onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                       onClick="checkingMovementId('detailsbutton');">
                        <fmt:message key="button.details"/>
                    </a>
                </c:if>
            </td>
            <c:if test="${requestScope.methodName != 'view'}">
                <td id="searchbutton" width="70px">
                    <a title="<fmt:message key='tooltip.executeSearch'/>" tabindex="3"
                       onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                       onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                       onClick="javascript:openWindow(sweepSearch(),'sweepsearchWindow','left=50,top=190,width=911,height=488,toolbar=0,resizable=yes,scrollbars=yes','true');">
                        <fmt:message key="button.search"/>
                    </a>
                </td>
            </c:if>
            <td id="notesbutton" width="70px">
                <c:if test="${requestScope.methodName == 'view'}">
                    <a title="<fmt:message key='tooltip.sweepNotes'/>" tabindex="4"
                       onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                       onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                       onClick="checkingMovementId('notesbutton');">
                        <fmt:message key="button.notes"/>
                    </a>
                </c:if>
            </td>
            <td id="msgbutton">
                <a title="<fmt:message key='tooltip.msgs'/>" tabindex="5"
                   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                   onClick="checkingMovementId('msgbutton');">
                    <fmt:message key="SweepDisplay.msg"/>
                </a>
            </td>
            <td width="70px">
                <a title="<fmt:message key='tooltip.close'/>" tabindex="5"
                   onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
                   onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                   onClick="confirmClose('C');">
                    <fmt:message key="button.close"/>
                </a>
            </td>
        </tr>
    </table>
</div>


<div style="position:absolute; left:6; top:4; width:705px; height:15px; visibility:hidden;">  	
		<table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
			<tr>
				<td id="detailsenablebutton">		
					
					<a  title='<fmt:message key = "tooltip.showSweepDetails"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('detailsbutton')";><fmt:message key = "button.details"/></a>
					
				</td>		
				<td id="detailsdisablebutton">
					<a class="disabled" disabled="disabled">Details</a>
				</td>
				<td id="notesenablebutton">		
					
					<a  title='<fmt:message key = "tooltip.sweepNotes"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementId('notesbutton')";><fmt:message key = "button.notes"/></a>
					
				</td>		
				<td id="notesdisablebutton">
					<a class="disabled" disabled="disabled"><fmt:message key = "button.notes"/></a>
				</td>


			<td id="msgdisablebutton">
					<a class="disabled" disabled="disabled"><fmt:message key = "SweepDisplay.msg"/></a>
				</td>
				<td id="msgenablebutton" width="70px">
			
			<a title='<fmt:message key = "tooltip.msgs"/>' tabindex="5"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="checkingMovementId('msgbutton')";><fmt:message key = "SweepDisplay.msg"/></a>			
			
			</td>
			
				 
			</tr>
		</table>
	</div>
</div>

</form>
</body>
</html>