<?xml version="1.0" encoding="UTF-8"?>
<%@page import="org.swallow.util.SwtUtil"%>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<intradayliquidity dataState="${dataState}">
    <request_reply>
        <status_ok>${requestScope.reply_status_ok}</status_ok>
        <message>${requestScope.reply_message}</message>
        <location />
    </request_reply>
    <data>
        <c:forEach items="${requestScope.processStatus}" var="entry">
            <element name="${entry.key}">${entry.value}</element>
        </c:forEach>
    </data>
</intradayliquidity>