<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>




<inputexceptions
		from="${requestScope.fromDate}"
		refresh="${requestScope.autoRefreshRate}"
		to="${requestScope.toDate}"
		dateformat="${requestScope.session.CDM.dateFormatValue}"
		dateComparing="${requestScope.dateComparing == 'Y' ? 'true' : 'false'}"
		sysDateFrmSession="${requestScope.sysDateFrmSession}"
		lastRefTime="${requestScope.lastRefTime}"
		sessionToDate="${requestScope.sessionToDate}"
>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">
				<c:out value="${opTime.value}" />
			</operation>
		</c:forEach>
	</timing>

	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key='inputexceptions.header.interface' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="str"
						dataelement="interface"
						width="${requestScope.column_width['interface']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.header.awaiting' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="awaiting"
						width="${requestScope.column_width['awaiting']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.header.accepted' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="accepted"
						width="${requestScope.column_width['accepted']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.header.rejected' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="rejected"
						width="${requestScope.column_width['rejected']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.header.submitted' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="submitted"
						width="${requestScope.column_width['submitted']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.header.suppressed' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="suppressed"
						width="${requestScope.column_width['suppressed']}"
				/>
				<c:if test="${requestScope.fromPCM == 'yes'}">
					<column
							heading="<fmt:message key='inputexceptions.header.repair' />"
							draggable="false"
							filterable="false"
							sort="true"
							type="num"
							dataelement="repair"
							width="${requestScope.column_width['repair']}"
					/>
				</c:if>
			</columns>
		</metadata>

		<rows size="${requestScope.recordCount}">
			<c:forEach items="${requestScope.result}" var="record">
				<row>
					<interface clickable="false">
						<c:out value="${record.key}" />
					</interface>
					<accepted status="3" clickable="${record.value['3'] == '0' ? 'false' : 'true'}">
						<c:out value="${record.value['3']}" />
					</accepted>
					<submitted status="7" clickable="${record.value['7'] == '0' ? 'false' : 'true'}">
						<c:out value="${record.value['7']}" />
					</submitted>
					<suppressed status="9" clickable="${record.value['9'] == '0' ? 'false' : 'true'}">
						<c:out value="${record.value['9']}" />
					</suppressed>
					<rejected status="4" clickable="${record.value['4'] == '0' ? 'false' : 'true'}">
						<c:out value="${record.value['4']}" />
					</rejected>
					<awaiting status="1" clickable="false">
						<c:out value="${record.value['1']}" />
					</awaiting>
					<repair status="10" clickable="${record.value['10'] == '0' ? 'false' : 'true'}">
						<c:out value="${record.value['10']}" />
					</repair>
				</row>
			</c:forEach>
		</rows>

		<totals />
	</grid>
</inputexceptions>
