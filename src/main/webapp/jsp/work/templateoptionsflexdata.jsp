<?xml version="1.0" encoding="UTF-8" ?>
<!--
- The main purpose of this JSP file is to load the resultant XML data for Forecast Template Options screen.
-
- Author(s): Bala .D
- Date: 08-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<monitoroptions>
    <request_reply>
        <status_ok>${requestScope.reply_status_ok}</status_ok>
        <message>${requestScope.reply_message}</message>
        <location />
    </request_reply>

    <c:set var="templateOptions" value="${requestScope.forecastTemplateOptions}" />

    <selects>
        <select id="currency">
            <option value="*" selected="0"> </option>
            <c:forEach var="currency" items="${requestScope.currencyList}">
                <option value="${currency.value}"
                        <c:if test="${templateOptions.id.currencyCode == currency.value}">selected="1"</c:if>
                        <c:if test="${templateOptions.id.currencyCode != currency.value}">selected="0"</c:if>
                >${currency.label}</option>
            </c:forEach>
        </select>

        <select id="entity">
            <c:forEach var="entity" items="${requestScope.entities}">
                <option value="${entity.value}"
                        <c:if test="${requestScope.forecastMonitorForm.forecastTemplateOptions.id.entityId == entity.value}">selected="1"</c:if>
                        <c:if test="${requestScope.forecastMonitorForm.forecastTemplateOptions.id.entityId != entity.value}">selected="0"</c:if>
                >${entity.label}</option>
            </c:forEach>
        </select>

        <select id="template">
            <c:forEach var="template" items="${requestScope.templateList}">
                <option value="${template.value}"
                        <c:if test="${requestScope.forecastMonitorForm.forecastTemplateOptions.templateId == template.value}">selected="1"</c:if>
                        <c:if test="${requestScope.forecastMonitorForm.forecastTemplateOptions.templateId != template.value}">selected="0"</c:if>
                >${template.label}</option>
            </c:forEach>
        </select>
    </selects>
</monitoroptions>
