<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><fmt:message key="movementAdd.title.Window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" type="text/css" href="style/displaytag.css" >


<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";

var entityId = "${requestScope.entityId}";
var currencyCode = "${requestScope.currencyCode}";
var manualMatch = "${requestScope.screen}";
var matchCount = "${requestScope.matchCount}";
var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1);


<%-- Start: Code modified by sandeepkumar for Mantis 1691 on 28-june-12  --%>
function unLoad()
{
	call();
	
}
<%-- End: Code modified by sandeepkumar for Mantis 1691 on 28-june-12  --%>

function bodyOnLoad()
{	
	document.forms[0].entityId.value=entityId;
	document.forms[0].currencyCode.value=currencyCode;
	document.forms[0].matchType.value=manualMatch;
}

var selectedMovementId = "${requestScope.selectedMovementId}";

<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.opener.opener.document.forms[0].method.value = "add";
	window.opener.opener.opener.document.forms[0].copiedMovementId.value = selectedMovementId;

	window.opener.opener.opener.document.forms[0].entityId.value=window.opener.opener.document.forms[0].entityId.value;
	
	
	
	window.opener.opener.close();
	window.opener.close();
	self.close();
	
</c:if>

function checkingMovementid()
{
    
   var movementidValue=validateField(document.forms[0].elements['movement.id.movementId'],'movement.id.movementId','numberPat');
    if(movementidValue)
   {
   	 	
     javascript:openWindow(searchmovements('searchFromMatch'),'SearchWindow','left=50,top=190,width=1000,height=650,toolbar=0, resizable=yes, scrollbars=yes','true');
    
   }
   else
   {
    document.forms[0].elements['movementidValue'].focus();  
   }
}

function submitForm(methodName){
	
	var movementId=validateField(document.forms[0].elements['movement.id.movementId'],'movement.id.movementId','numberPat');
	
	var mvmtID = document.forms[0].elements['movement.id.movementId'].value;
	
	if(movementId)
	{

	if(mvmtID.trim() == ""){
		ShowErrMsgWindowWithBtn("", "<fmt:message key="match.addMvmt.mvmtIdempty"/>", null,closeWin );
	} else{
		
			var oXMLHTTP = new XMLHttpRequest();
			var sURL = requestURL + appName +"/movementmatchdisplay.do?method=checkMovementExists";
			sURL = sURL + "&movementId="+document.forms[0].elements['movement.id.movementId'].value;
						
			sURL = sURL + "&entityId="+ '${requestScope.entityId}';
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var str=new String(oXMLHTTP.responseText);
			
			if(str != "true"){
				ShowErrMsgWindowWithBtn("", "Movement does not exist", null,closeWin);
				//self.close();
				
			}
			
		    else {
			var oXMLHTTP = new XMLHttpRequest();
			var sURL = requestURL + appName +"/movementLock.do?method=lockMovementForMMSD";
			sURL = sURL + "&movementId="+document.forms[0].elements['movement.id.movementId'].value;

			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var str=new String(oXMLHTTP.responseText);
			if(str != "true"){
				ShowErrMsgWindowWithBtn("", "Movement already exists", null, closeWin);
				
			}
			else{
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName +"/movementmatchdisplay.do?method=checkMovementMatchStatus";
				sURL = sURL + "&movementId="+document.forms[0].elements['movement.id.movementId'].value;
				sURL = sURL + "&entityId="+ '${requestScope.entityId}';
				sURL = sURL + "&currencyCode="+document.forms[0].currencyCode.value ;
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=new String(oXMLHTTP.responseText);
				if(str != "true")
				{
					window.opener.unlockMovementOnServer(document.forms[0].elements['movement.id.movementId'].value)
					ShowErrMsgWindowWithBtn("",str, null );
				}  
				else {
                
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName +"/movementmatchdisplay.do?method=checkMovementAmount";
				sURL = sURL + "&movementId="+document.forms[0].elements['movement.id.movementId'].value;
				sURL = sURL + "&entityId="+ '${requestScope.entityId}';
				sURL = sURL + "&isAmountDiffer="+ '${requestScope.isAmountDiffer}';
				sURL = sURL + "&selectedMovementsAmount="+ '${requestScope.selectedMovementsAmount}';

				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=new String(oXMLHTTP.responseText);
				
				if (str == "false") {
					var yourstate=window.confirm('<fmt:message key="confirm.addMovementsToExistingmatch"/>');
					
					if (yourstate==true){ //Boolean variable. Sets to true if user pressed "OK" versus "Cancel."
						window.opener.callApp("flexadd","'"+document.forms[0].elements['movement.id.movementId'].value+"'," , matchCount);
						
						self.close();
					}
					 else{
					
						 window.opener.unlockMovementOnServer(document.forms[0].elements['movement.id.movementId'].value);
						 self.close();
					}
					
				} else {

					
						window.opener.callApp("flexadd","'"+document.forms[0].elements['movement.id.movementId'].value+"'," , matchCount);
						self.close();
				}
				
				
			   } 			
			}
		}

	}
	
	}
	else
	{
	   document.forms[0].elements['movement.id.movementId'].focus();
	}
	}


function closeWin() {
	  self.close();
}
function searchmovements(methodName){
	var param = 'movementsearch.do?method='+methodName;
	param += '&initialinputscreen=matchSearch' ;
	param += '&entityId=' + entityId ;
	param += '&currencyCode=' + currencyCode ;
	param += '&isAmountDiffer=' + '${requestScope.isAmountDiffer}' ;
	param += '&selectedMovementsAmount=' + '${requestScope.selectedMovementsAmount}' ;
	return param;
}

</SCRIPT> 

</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad();" onunload="unLoad();">
<form action="movementmatchdisplay.do">
<input name="method" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="currencyCode" type="hidden" value="">
<input name="matchType" type="hidden" value="">
<input name="test" value="" style="visibility:hidden">


<div id="MessageFormats" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:10px; width:250px; height:36px;">
<div id="MessageFormats" color="#7E97AF" style="position:absolute; left:4px; top:4px; width:230px; height:35px;">
<table width="220" border="0" cellspacing="0" cellpadding="0">
	 <tr height="25">
	 
		  <td width="80"><b><fmt:message key="manualInput.id.movementId"/></b></td>
		  <td width="28px">&nbsp;</td>
		  <td width="120px">
			  <input
					  type="text"
					  tabindex="1"
					  title="${tooltip.enterMvmId}"
					  maxlength="12"
					  name="movement.id.movementId"
					  style="width:120px;"
					  onchange="return validateField(this, 'movementId', 'numberPat');"
					  value="${movement.id.movementId}" />
		  </td>

	 </tr>
     </table> 
	 </div>
</div>
<div id="printButtonDiv" style="position:absolute; left:1;top:59; width:250px; height:15px; visibility:visible;">
	<table width="255px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="right" id="Print">
				<a tabindex="40" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title="Print screen content"></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:50; width:250; height:39px; visibility:visible;">
<div id="Currency" style="position:absolute; left:6; top:2; width:100; height:15px; visibility:visible;">
<table >
    <tr>
    	<td id="okbutton">		
			<a title='<fmt:message key="tooltip.ok"/>' onMouseOut="collapsebutton(this)" tabindex="10" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('add')"><fmt:message key="button.ok"/></a>
		</td>	
<td id="cancelbutton">		
			<a  title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" tabindex="20" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
		</td>
		<td id="searchbutton">		
			<a title='<fmt:message key="tooltip.search"/>' onMouseOut="collapsebutton(this)"  tabindex="30" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="checkingMovementid()";><fmt:message key="button.search"/></a>
		</td>
		
	</tr>
</table>
</div>
<div style="position:absolute; left:6; top:4; width:100; height:15px; visibility:hidden;">  	
	<table >
    <tr>
    	<td id="addenablebutton">		
			<a  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('add')"><fmt:message key="button.ok"/></a>
		</td>		
		<td id="prevdisablebutton">
			<a  class="disabled" ><fmt:message key="button.previous"/></a>
		</td>
	</tr>
</table>
</div>
</div>
 
</form>
</body>
</html>
