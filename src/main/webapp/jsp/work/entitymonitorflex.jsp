

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<title><fmt:message key="label.entityMonitor.title.window"/></title>
</head>
<body>
		<script type="text/javascript">

			// This method is called when onload
// 			window.onload = function () {
// 			    setTitleSuffix(document.forms[0]);
// 				setParentChildsFocus();
// 			};

// 			window.onunload = call;
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["currency"] = "<fmt:message key="label.entityMonitor.currency.group"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.entityMonitor.selectCcyGroup"/>";

			label["text"]["date"] = "<fmt:message key="label.entityMonitor.date"/>";
			label["tip"]["date"] = "<fmt:message key="tooltip.entityMonitor.date"/>";

			label["text"]["breakdown"] = "<fmt:message key="label.entityMonitor.breakdown"/>";
			label["text"]["acctbrkdown"] = "<fmt:message key="label.entityMonitor.accountId"/>";
			label["tip"]["acctbrkdown"] = "<fmt:message key="tooltip.entityMonitor.accountMonitor"/>";
			label["text"]["mvmntbrkdown"] = "<fmt:message key="label.entityMonitor.movementId"/>";
			label["tip"]["mvmntbrkdown"] = "<fmt:message key="tooltip.entityMonitor.mvmntBrkdown"/>";
			label["text"]["bookbrkdown"] = "<fmt:message key="label.entityMonitor.bookCode"/>";
			label["tip"]["bookbrkdown"] = "<fmt:message key="tooltip.entityMonitor.bookBrkdown"/>";
			label["text"]["grpmonitor"] = "<fmt:message key="label.entityMonitor.group"/>";
			label["tip"]["grpmonitor"] = "<fmt:message key="tooltip.entityMonitor.viewGroupMonitor"/>";
			label["text"]["mtagrpmonitor"] = "<fmt:message key="label.entityMonitor.metagroup"/>";
			label["tip"]["mtagrpmonitor"] = "<fmt:message key="tooltip.entityMonitor.viewMetagroupMonitor"/>";
			label["text"]["button-refresh"] = "<fmt:message key="button.entityMonitor.refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.entityMonitor.refreshWindow"/>";
			label["text"]["button-rate"] = "<fmt:message key="label.entityMonitor.rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.entityMonitor.rateButton"/>";
			label["text"]["button-options"] = "<fmt:message key="button.entityMonitor.option"/>";
			label["tip"]["button-options"] = "<fmt:message key="tooltip.entityMonitor.option"/>";
			label["text"]["button-close"] = "<fmt:message key="button.entityMonitor.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.entityMonitor.close"/>";

			/*Start Added by Imed B on 20-02-2014*/
			label["text"]["label-entityMonitor"] = "<fmt:message key="entityMonitor.title"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-dataBuildProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["breakdown"] = "<fmt:message key="screen.breakdown"/>";
			label["tip"]["label-hideControlBar"] = "<fmt:message key="tooltip.hideControlBar"/>";
			label["text"]["label-hideButtonBar"] = "<fmt:message key="label.hideButtonBar"/>";
			label["tip"]["label-showButtonBar"] = "<fmt:message key="label.showButtonBar"/>";
			label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			label["text"]["label-entitycurrencyGrp"] = "<fmt:message key="label.entityMonitor.currency.group"/>";
			label["tip"]["label-selectCurrencyGrp"] = "<fmt:message key="tooltip.entityMonitor.selectCcyGroup"/>";
                        label["text"]["label-entityDate"] = "<fmt:message key="label.entityMonitor.date"/>";
			label["tip"]["label-tooltipEntityDate"] = "<fmt:message key="tooltip.entityMonitor.date"/>";
                        label["text"]["label-entityOffset"] = "<fmt:message key="label.entityMonitor.entityOffset"/>";
			label["tip"]["label-tipEntityOffset"] = "<fmt:message key="tooltip.entityMonitor.entityOffset"/>";
                        label["text"]["label-breakdown"] = "<fmt:message key="label.entityMonitor.breakdown"/>";
                        /*End Added by Imed B on 20-02-2014*/

                        label["text"]["label-acctbrkdown"] = "<fmt:message key="label.entityMonitor.accountId"/>";
                        label["tip"]["label-tipAcctbrkdown"] = "<fmt:message key="tooltip.entityMonitor.accountMonitor"/>";
                        label["text"]["label-mvmntbrkdown"] = "<fmt:message key="label.entityMonitor.movementId"/>";
                        label["tip"]["label-tipMvmntbrkdown"] = "<fmt:message key="tooltip.entityMonitor.mvmntBrkdown"/>";;
                        label["tip"]["label-tipBookbrkdown"] = "<fmt:message key="tooltip.entityMonitor.bookBrkdown"/>";
                        label["text"]["label-bookbrkdown"] = "<fmt:message key="label.entityMonitor.bookCode"/>";
                        label["tip"]["label-tipGrpmonitor"] = "<fmt:message key="tooltip.entityMonitor.viewGroupMonitor"/>";
                        label["text"]["label-grpmonitor"] = "<fmt:message key="label.entityMonitor.group"/>";
                        label["tip"]["label-tipMtagrpmonitor"] = "<fmt:message key="tooltip.entityMonitor.viewMetagroupMonitor"/>";
                        label["text"]["label-mtagrpmonitor"] = "<fmt:message key="label.entityMonitor.metagroup"/>";
                        label["text"]["label-lossConnection"] = "<fmt:message key="label.lossConnection"/>";

 			// Code modified by Vivekanandan a on 20120710 for Mantis 1991 Start
			label["text"]["entityOffset"] = "<fmt:message key="label.entityMonitor.entityOffset"/>";
			label["tip"]["entityOffset"] = "<fmt:message key="tooltip.entityMonitor.entityOffset"/>";

			var alertOrangeImage = "images/Alert/scenario/normal.png";
			var alertRedImage = "images/Alert/scenario/critical.png";
			var screenRoute = "entityMonitor";
 			// get the system date
			var dbDate='${requestScope.dbDate}';
			// Code modified by Vivekanandan a on 20120710 for Mantis 1991 END


			// get the date format
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			// get the item id
			var itemId = '${requestScope.itemId}';
			// get the host id
			var hostId = '${requestScope.hostId}';
			// get the user id
			var userId = '${requestScope.userId}';
			// get the font size from request
			var currentFontSize = '${requestScope.fontSize}';

			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			// option window
			var optionWindow = "";
			// This function is called when click the link in entity monitor details
			function clickLink (sEntityId, sCurrencyCode, sColumnDate, sActionCode) {
				var menuAccessIdChild = 0;
				var paramString="";
				if(sCurrencyCode.indexOf (' ') > -1){
					sCurrencyCode = sCurrencyCode.split(' ')[0];
				}
				paramString = paramString + "&entityId=" + sEntityId;
				paramString = paramString + "&currencyCode=" + sCurrencyCode;
				paramString = paramString + "&valueDate=" + sColumnDate;
				var requestURL = new String ('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf ('/'+appName+'/');
				var arrtributString;
				requestURL=requestURL.substring (0, idy+1) ;
				// If select the account monitor
				if (sActionCode == "A") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("acctmonitornew.do");
					var menuName = new String('<fmt:message key="label.accountMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.entityMonitor.smartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.entityMonitor.accessNotAvl"/>' + menuName + '<fmt:message key="alert.entityMonitor.contactSysAdm"/>');
					}
					requestURL = requestURL +appName +"/acctmonitornew.do?method=callFromEntityMonitor&menuAccessId="+menuAccessIdChild+"&callFrom=CM";
					arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes";

				}
				// If select the movement Display
				else if (sActionCode == "M") {
					requestURL = requestURL +appName +"/currmonitorNew.do?method=countMovements&breakdown=All";
					requestURL += paramString;
					var noOfMovements = sendRequest (requestURL);
					if(noOfMovements == 0) {
						alert('<fmt:message key="alert.entityMonitor.noMovements"/>');
						return false;
					} else {
						var requestURL = 'outstandingmovement.do?method=flexCurrency';
						arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
						requestURL = requestURL + "&initialinputscreen=" + "entitymonitor";
					}
				}
				// If select the Book group
				else if (sActionCode == "B") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					var menuName = new String('<fmt:message key="bookMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.entityMonitor.smartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.entityMonitor.accessNotAvl"/>' + menuName + '<fmt:message key="alert.entityMonitor.contactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Book";
					arrtributString="left=50,top=190,width=860,height=690,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				}
				// If select the Group
				 else if (sActionCode == "G") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					var menuName = new String('<fmt:message key="groupMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.entityMonitor.smartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.entityMonitor.accessNotAvl"/>' + menuName + '<fmt:message key="alert.entityMonitor.contactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Group";
					arrtributString="left=50,top=190,width=860,height=665,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				}
				// If select the Meta Group
				else if (sActionCode == "MG") {
					menuAccessIdChild = getMenuAccessIdOfChildWindow("metagroupmonitor.do");
					var menuName = new String('<fmt:message key="MetagroupMonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.entityMonitor.smartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.entityMonitor.accessNotAvl"/>' + menuName + '<fmt:message key="alert.entityMonitor.contactSysAdm"/>');
					}
					var requestURL = "metagroupmonitor.do?method=callfromcurrency&monitortype=Metagroup";
					arrtributString="left=50,top=190,width=850,height=655,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
				}
				requestURL += "&entityId=" + sEntityId;
				requestURL += "&currencyCode=" + sCurrencyCode;
				requestURL += "&valueDate=" + sColumnDate;
				requestURL += "&callfromcurrency=y";
				if (menuAccessIdChild != 2) {
					openWindow (requestURL, 'bookMonitorJsp', arrtributString);
				}
				if (window.event){
					window.event.returnValue = false;
					window.event.cancelBubble = true;
				}
				return false;
			}

			// This method is used to send the request
			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();
				var count = new Number (oXHR.responseText);
				return count;
			}

			// This function opens the Entity Monitor Option Window
            function openEntityOptionsWindow () {
				var param = '/' + appName + '/entityMonitor.do?method=flexOption';
				openWindow (param, 'entityMonitorOptions', 'width=780,height=265,toolbar=0, resizable=yes, scrollbars=no, left=240, top=185', 'true');
			}

			// This function opens the help screen
			function help(){
				openWindow(buildPrintURL('print','Entity Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

			// This function used to refresh the screen
			function callApp(refreshStart){

	   		var event = new CustomEvent("entityMonitor.refresh",
	            {
	                detail: refreshStart,
	                bubbles: true,
	                cancelable: true
	            }
	        );
	        window.dispatchEvent(event);

			}

			// This function used to close the child window
			function closeChild(){
				// Added By KaisBS for issue 1054_STL_038 : avoid the javascript error if we aren't open the option screen
				if(typeof(optionWindow) == window)
				optionWindow.close();
			}

			//Used to determine what to request to save a new refresh rate
			function getUpdateRefreshRequest (rate,item_id) {
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + rate;
			}
		</script>
        <%@ include file="/angularscripts.jsp"%>

		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.entityMonitor.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />

</html>