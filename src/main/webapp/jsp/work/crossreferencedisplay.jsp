<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<title><fmt:message key="crossReferenceDisplay.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
/* This Method is used for close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "close"){
		confirmClose('P');
		}
	}
}
</SCRIPT>
</head>
<form action="notes.do" method="post">
<input name="method" type="hidden" value="crossReference">  

<body  onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()"  >
 <div id="CrossReference" style="position:absolute; left:10px; top:10px; width:950px; height:40px; border:2px outset;" color="#7E97AF">
 
 <div id="CrossReference" style="position:absolute; left:8px; top:4px; width:950px; height:380;">
 <table width="768" border="0" cellpadding="0" cellspacing="0" height="30">
 	<tr color="black" border="0">
 	  <td width="38"><b><fmt:message key="crossReference.movement"/></b></td>
 	  <td width="8">&nbsp;</td>
 	  <td align="right" width="122" >
		  <input type="text" name="crossReference.movementId" tabindex="5" style="width:122px;" maxlength="40" value="${crossReference.movementId}" disabled="disabled" />
	  </td>
 	  <td width="310">&nbsp;</td>	 
 	 </tr>
 </table>
</div>
</div>

<div color="#7E97AF" style="position:absolute; border:2px outset; left:10px; top:58px; width:950px; height:280;">
<div id="Notes" style="position:absolute;z-index:99; left:0px; top:0px; width:929px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="929" border="0" cellspacing="1" cellpadding="0" height="20">
	<thead>
		<tr height="20px">
			<td align="center"  width="160px"><b><fmt:message key="crossReference.sourceOfReference"/></b></td>
			<td  align="center" width="210px"><b><fmt:message key="crossReference.businessSource"/></b></td>
			<td  align="center" width="310px"><b><fmt:message key="crossReference.crossReference"/></b></td>
			<td  align="center" width="150px"><b><fmt:message key="crossReference.updateDate"/></b></td>			
		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:946px; height:275">
<div id="Notes" style="position:absolute;left:0px; top:20px; width:929px; height:10px;">
<table class="sort-table" id="notesDetails" width="928" border="0" cellspacing="1" cellpadding="0" height="255">
    <tbody>
        <c:forEach var="listViewData" items="${requestScope.listViewData}" varStatus="status">
            <tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">
                <td width="160px">${listViewData.sourceOfReference}&nbsp;</td>
                <td width="210px">${listViewData.businessSource}&nbsp;</td>
                <td width="310px">${listViewData.crossReference}&nbsp;</td>
                <td width="150px" style="text-overflow: ellipsis; overflow: hidden">
                    <nobr>${listViewData.updateDateTime}</nobr>
                </td>
            </tr>
        </c:forEach>
    </tbody>
    <tfoot>
        <tr>
            <td colspan="4"></td>
        </tr>
    </tfoot>
</table>
</div>
</div>
</div>
<div id="Notes" style="position:absolute ;left:887; top:350px; width:70px; height:37px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		     <td align="Right">
			   <a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Cross Reference'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
		  </td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10px; top:344px; width:950px; height:39px; visibility:visible;">
<div id="notes" style="position:absolute; left:6; top:1; width:524; height:14px; visibility:visible;">
   <table width="280" border="0" cellspacing="0" cellpadding="0" height="17">
	<tr>
		<td id="closebutton"  width="70px">		
			<a  title='<fmt:message key="tooltip.close"/>' tabindex="4" onKeydown = "onKeyEnterSubmit('close',event);" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>				
		</td>
	</tr>
	</table>
</div>
  <!--/table-->
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

<script type="text/javascript">
</script>
</form>
</body>
</html>
