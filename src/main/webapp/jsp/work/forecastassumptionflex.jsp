<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Forecast assumption screen. 
  - Also, to load the label values for this screen.	
  - Author(s): Bala .D
  - Date: 08-06-2011
  -->
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>


<html>
	<head>
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
	</head>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="init();setParentChildsFocus();" onunload="unloadCloseWindow();">
		<title><fmt:message key="label.forecastAssumptions.title.window"/></title>
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		    var screenRoute = "ForecastAssumption";
			// get the request variable values
			// get the entity Id
			var entityId="${requestScope.entityId}";
			// get the assumption Id
			var assumptionId="${requestScope.assumptionId}";
			// get the currency code
			var currencyCode="${requestScope.currencyCode}";
			// get the currency name
			var currencyName="${requestScope.currencyName}";
			// get the template id
			var templateId="${requestScope.templateId}";
			// get the date
			var date="${requestScope.valueDate}";
			// Start: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			// variable assumption 
			var assumption = "";
			// variable amount 
			var amount = "";
			// End: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			// Set the labels
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["add"] = "<fmt:message key="button.forecastMonitor.add"/>";
			label["tip"]["add"] = "<fmt:message key="tooltip.forecastMonitor.add"/>";
			label["text"]["change"] = "<fmt:message key="button.forecastMonitor.change"/>";
			label["tip"]["change"] = "<fmt:message key="tooltip.forecastMonitor.change"/>";
			label["text"]["delete"] = "<fmt:message key="button.forecastMonitor.delete"/>";
			label["tip"]["delete"] = "<fmt:message key="tooltip.forecastMonitor.delete"/>";
			label["text"]["close"] = "<fmt:message key="button.forecastMonitor.close"/>";
			label["tip"]["close"] = "<fmt:message key="tooltip.forecastMonitor.close"/>";
			label["text"]["templateid"] = "<fmt:message key="label.forecastMonitor.templateId"/>";	
			label["text"]["deleteConfirm"] = "<fmt:message key="alert.assumption.deleteConfirm"/>";
			label["text"]["connection-error"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-validPageNumber"] = "<fmt:message key="genericDisplayMonitor.validPageNumber"/>";
			
			// get the menuAccessId 
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("forecastMonitor.do");
			/*
 			* Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
 			*/
			// get the system date
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			// get the date format			
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';;
			// get the application name			
			var appName = "<%=SwtUtil.appName%>";
			//variable personal entity
			var assumptionAdd = "";
			// boolean variable
			var flag = false;
			
			/**
             * closeWindow
             * This function used to close the window
 			 * @return none
			 */
			function closeWindow() {
				window.close();
			}  
			
			/**
             * callApp
             * This function used to refresh the screen
 			 * @return none
			 */
			function callApp(refreshStart){		
				window.reload();
			}
			
			/**
             * help
             * This function opens the help screen
 			 * @return none
			 */			
			function help(){
				openWindow(buildPrintURL('print','Forecast Monitor Assumptions'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
            
            /**
			 * openModifyAssumptionWindow
			 * Method to open the Assumption add window
			 * @param assumptions
			 * @return none
			 */
            function openModifyAssumptionWindow (assumptions) {	
            	if(assumptions.amount != "undefined"){
					var param = '/' + appName + '/forecastMonitor.do?method=flexModifyAssumption&entity='+assumptions.entity+'&currency='+assumptions.currency+'&currencyName='+assumptions.currencyName+'&date='+assumptions.date+'&assumptionId='+assumptions.assumptionId+'&templateId='+assumptions.templateId;
					assumptionAdd = window.open (param, 'forecastAssumption', 'width=520,height=185,toolbar=0, resizable=yes, scrollbars=no, left=10, top=214', 'true');
				} else{
					var param = '/' + appName + '/forecastMonitor.do?method=flexModifyAssumption&entity='+assumptions.entity+'&currency='+assumptions.currency+'&date='+assumptions.date+'&currencyName='+assumptions.currencyName+'&templateId='+assumptions.templateId;
					assumptionAdd = window.open (param, 'modifyForecastAssumption', 'width=520,height=185,toolbar=0, resizable=yes, scrollbars=no, left=10, top=214', 'true');
				}
				assumption = assumptions.assumption;
				amount = assumptions.amount;
				// End: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			}
			
			/**
             * unloadCloseWindow
             * This function is called, when unload the window
 			 * @return none
			 */
			function unloadCloseWindow(){
				if (!flag)
					window.close();
				if (assumptionAdd != "")
					assumptionAdd.close();
				window.opener.callApp("Y");
			}			
			
			// Start : Code modified by Bala for 1053 Beta3 testing issues on 14-Sep-2011- Once assumption screen closed, parent screen should be refreshed
			/**
             * refreshParent
             * This function is used to refresh the parent screen 
 			 * @return none
			 */
			function refreshParent(){
				window.opener.callApp("Y");
			}
			// End : Code modified by Bala for 1053 Beta3 testing issues on 14-Sep-2011- Once assumption screen closed, parent screen should be refreshed
			
			
			/**
             * reloadAssumption
             * This method is used to reload the screen
 			 * @return none
			 */
			function reloadAssumption(){
				   var url= document_location_href();				
				   submitFormFromURL(url,window);			
			}
			
			/**
             * init
             * This method is called when onload
 			 * @return none
			 */
			function init(){
				flag = true;
			}
            
		</script>
        <%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.forecastAssumptions.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>