/*
////////////////////////////////////////////////////////////////////////////////
//
// Copyright (C) 2003-2006 Adobe Macromedia Software LLC and its licensors.
// All Rights Reserved.
// The following is Sample Code and is subject to all restrictions on such code
// as contained in the End User License Agreement accompanying this product.
// If you have received this file from a source other than Adobe,
// then your use, modification, or distribution of it requires
// the prior written permission of Adobe.
//
////////////////////////////////////////////////////////////////////////////////
*/
Application
{
   backgroundGradientColors:#D6E3FE, #D6E3FE; /*#666699, #CCCCFF, ;*/
}


Panel {
	dropShadowEnabled: false;

}
ApplicationControlBar{
	dropShadowEnabled: false;

}
PieSeries {
    fills: #EF7651, #E9C836, #6FB35F, #A1AECF, #FF00FF, #00FFFF, #999966, #ffff80, #9966FF , #ff99cc;
}

global
{
   color: #000000;
	font-family: verdana,helvetica;
	font-size: 12pt;
}
Button {
        overSkin: Embed("assets/bg.png");
        upSkin: Embed("assets/bg.png");       
		downSkin: Embed("assets/bg.png");
		font-size: 12pt;
		fontWeight: normal;
		
    }
ComboBox{
		textRollOverColor:#ffffff;
		rollOverColor:#0000a0;
		selectionColor: #0000a0;
		textSelectedColor:#ffffff;
		fillColors: #ffffff, #ffffff;
		fillAlphas: 1.0, 1.0;
		cornerRadius:0;
	}

Alert{
		backgroundGradientColors:#D6E3FE, #D6E3FE;
	}


