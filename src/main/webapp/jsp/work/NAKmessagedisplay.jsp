<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="sweepNAKSummary.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

var dateFormat = '${sessionScope.CDM.dateFormat}';
var currencyFormat = '${sessionScope.CDM.currencyFormat}';

function disableAllButtons()
{
	document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepdisablebutton").innerHTML;

	document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

	document.getElementById("NAKbutton").innerHTML = document.getElementById("NAKdisablebutton").innerHTML;

}


function onFilterandSort()
{
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{
	xl = new XLSheet("SweepMsgDisplaycoll","table_2", ["dateFormat","String", "String","Number","dateFormat", "String","currencyFormat"],"1111111");
	xl.onsort = xl.onfilter = onFilterandSort;
	disableAllButtons();
	highlightTableRows("SweepMsgDisplaycoll");

}
function onSelectTableRow(rowElement, isSelected){
	var hiddenElement1 = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedMessageId.value = hiddenElement1.value;
	var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
	document.forms[0].selectedNAKMessageId.value = hiddenElement2.value;
	document.forms[0].selectedsweepId.value = rowElement.cells[3].innerText;
	if(isSelected) {
		if(hiddenElement1.value!=""){
			document.getElementById("msgbutton").innerHTML = document.getElementById("msgenablebutton").innerHTML;
		} else {
			document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
		}
	    if(hiddenElement2.value!=""){
		    document.getElementById("NAKbutton").innerHTML = document.getElementById("NAKenablebutton").innerHTML;
	    } else {
		    document.getElementById("NAKbutton").innerHTML = document.getElementById("NAKdisablebutton").innerHTML;
	    }

	    document.getElementById("sweepbutton").innerHTML = document.getElementById("sweepenablebutton").innerHTML;
    }else{
	    disableAllButtons();
    }
}
function openNAKMessageView(method){
	var param = 'inputexceptionsmessages.do?method=single&seqid=';

    param += document.forms[0].selectedNAKMessageId.value;

	return param;
}


function openMessageView(method){
	var param = 'inputexceptionsmessages.do?method=single&seqid=';
    param += document.forms[0].selectedMessageId.value;

	return param;
}

function openSweepDisplay(method){
	var param = 'sweepsearch.do?method=populate&selectedSweepId=';
	param +=  document.forms[0].selectedsweepId.value;

	return param;
}


</SCRIPT>
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="sweepdetail.do" method="post">
<input name="selectedMessageId" type="hidden">
<input name="selectedsweepId" type="hidden">
<input name="selectedNAKMessageId" type="hidden">

<div id="SweepMsgDisplay" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:748px; height:353px;">
<div id="SweepMsgDisplayHeader" style="position:absolute;z-index:99;left:0px; top:0px; width:735; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="715" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr height="20px">

			<td title='<fmt:message key="tooltip.sortByDate"/>'width="80px"><b><fmt:message key="SweepMsgDisplay.GeneratedOnDate"/></b></td>

			<td title='<fmt:message key="tooltip.sortByTime"/>'width="80px"><b><fmt:message key="SweepMsgDisplay.GeneratedOnTime"/></b></td>

			<td title='<fmt:message key="tooltip.sortbyformattype"/>' width="120px" ><b><fmt:message key="SweepMsgDisplay.Message"/></b></td>


			<td title='<fmt:message key="tooltip.sortSweepID"/>' width="120px"><b><fmt:message key="sweep.sweepId"/></b></td>
			<td title='<fmt:message key="tooltip.sortValueDate"/>'width="80px"><b><fmt:message key="sweep.valueDate"/></b></td>

			<td title='<fmt:message key="tooltip.sortCurrencyCode"/>'width="70px"><b><fmt:message key="sweep.currencyCode"/></b></td>

			<td title='<fmt:message key="tooltip.sortSweepAmount"/>' width="170px" ><b><fmt:message key="sweep.sweepAmt"/></b></td>



		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:743;height:350px;overflow-x:hidden ">
	<div id="SweepMsg" style="position:absolute;z-index:99;left:0px; top:21px; width:735; height:10px;">
	<table class="sort-table" id="SweepMsgDisplaycoll" width="720" border="0" cellspacing="1" cellpadding="0" height="329px">
	<tbody>
		<%int count = 0; %>

		<c:forEach items="${requestScope.NAKMessageColl}" var='NAKMessageColl' >
		<% if( count%2 == 0 ) {%><tr class="even"><% }else  { %>
		<tr class="odd"> <%}++count; %>
		<input type="hidden" name="NAKMessageColl.messageId" value="${NAKMessageColl.messageId}" />
		<input type="hidden" name="NAKMessageColl.ACKNAKmessageId" value="${NAKMessageColl.ACKNAKmessageId}" />
			<td width="80">${NAKMessageColl.updateDateAsString}&nbsp;</td>

			<td width="80">${NAKMessageColl.updateTime}&nbsp;</td>

			<td width="120px">${NAKMessageColl.msgFormatId}&nbsp;</td>

			<td width="120px">${NAKMessageColl.sweepId}&nbsp;</td>

			<td width="80">${NAKMessageColl.valueDateAsString}&nbsp;</td>

			<td width="70px">${NAKMessageColl.currcode}&nbsp;</td>

			<td width="170px" align="right">${NAKMessageColl.sweepAmountAsString}&nbsp;</td>

		</tr>
		</c:forEach>
	</tbody>
<tfoot><tr><td colspan="7" ></td></tr></tfoot>
</table>
	</div>
  </div>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:379; width:748; height:39px; visibility:visible;">
  <div id="SweepMsgDisplay" style="position:absolute; left:6; top:4; width:505; height:15px; visibility:visible;">


  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70px"  id="sweepbutton" ></td>
			<td width="70px"  id="msgbutton" ></td>
			<td width="70px"  id="NAKbutton" ></td>
			<!-- Start: Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure" -->
			<td id="closebutton">
				<a tabindex="1" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>


	</div>
	<div style="position:absolute; left:6; top:4; width:748; height:15px; visibility:hidden;">
    <table width="140" border="1" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>

		<td id="sweepenablebutton">
					<a title='<fmt:message key="tooltip.viewSweepDetails"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(openSweepDisplay('view'),'messageViewScreen','left=50,top=190,width=796,height=745,toolbar=0, resizable=yes, scrollbars=yes','true')" tabindex="5"><fmt:message key="button.sweep"/></a>
				</td>

		<td id="sweepdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.sweep"/></a>
		</td>


		<td id="msgenablebutton">
					<a title='<fmt:message key="tooltip.viewSelMessage"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(openMessageView('view'),'messageViewScreen','left=50,top=190,width=450,height=500,toolbar=0, resizable=yes, scrollbars=yes','true')" tabindex="6"><fmt:message key="button.message"/></a>
				</td>

		<td id="msgdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.message"/></a>
		</td>


		<td id="NAKenablebutton">
					<a title='<fmt:message key="tooltip.viewNAKmessage"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"  onClick="javascript:openWindow(openNAKMessageView('view'),'messageViewScreen','left=50,top=190,width=450,height=500,toolbar=0, resizable=yes, scrollbars=yes','true')" tabindex="7"><fmt:message key="button.NAK"/></a>
				</td>

		<td id="NAKdisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.NAK"/></a>
		</td>
		<!-- End:  Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure".  -->

			</tr>
    </table>
  </div>

</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>


<script type="text/javascript">
</script>
</form>
</body>
</html>
 
