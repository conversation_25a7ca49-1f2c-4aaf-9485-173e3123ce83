<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Personal Entity List screen. 
  - Also, to load the label values for this screen.	
  - Author(s): Bala .D
  - Date: 08-03-2011
  -->
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/taglib.jsp"%>

<html>
	<head>
		<title><fmt:message key="label.personalEntityList.title.window"/></title>
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<link rel="icon" type="image/x-icon" href="favicon.ico">
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		</head>
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
<!-- 		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script> -->
		<script type="text/javascript">
		  var screenRoute = "personalEntityList";
			// Set the labels
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["ok"] = "<fmt:message key="label.personalEntityList.button.ok"/>";
			label["tip"]["ok"] = "<fmt:message key="tooltip.personalEntityList.button.ok"/>";
			label["text"]["cancel"] = "<fmt:message key="label.personalEntityList.button.cancel"/>";
			label["tip"]["cancel"] = "<fmt:message key="tooltip.personalEntityList.button.cancel"/>";
			/*Start Added by Imed b on 20-02-2014*/
			label["text"]["label-personalEntityList"] = "<fmt:message key="personalEntityList.title"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["label-number"] = "<fmt:message key="alert.forecastMonitor.enterNumber"/>";
			label["text"]["label-positiveNumber"] = "<fmt:message key="alert.interfaceSettings.posnumber"/>";
			label["text"]["label-numberBtweenRange"] = "<fmt:message key="personalCurrencyList.numberBetweenRange"/>";
			label["text"]["label-numberBigRange"] = "<fmt:message key="personalCurrencyList.numberBetweenBigRange"/>";

			label["text"]["label-sum-entity-id"] = "<fmt:message key="label.personalEntityList.sumEntityId"/>";
			label["text"]["label-sum-entity-name"] = "<fmt:message key="label.personalEntityList.sumEntityName"/>";
			label["text"]["label-addsumtitle"] = "<fmt:message key="label.personalEntityList.title.addsum"/>";
			label["text"]["add-sum"] = "<fmt:message key="label.personalEntityList.button.addsum"/>";
			label["tip"]["add-sum"] = "<fmt:message key="tooltip.personalEntityList.button.addsum"/>";
			label["text"]["modify-sum"] = "<fmt:message key="label.personalEntityList.button.modifysum"/>";
			label["tip"]["modify-sum"] = "<fmt:message key="tooltip.personalEntityList.button.modifysum"/>";
			label["text"]["delete-sum"] = "<fmt:message key="label.personalEntityList.button.deletesum"/>";
			label["tip"]["delete-sum"] = "<fmt:message key="tooltip.personalEntityList.button.deletesum"/>";
			label["alert"]["delete-confirm"] = "<fmt:message key="alert.deletion.confirm"/>";
			label["alert"]["delete-sum"] = "<fmt:message key="alert.personalEntityList.deletesum"/>";
			label["alert"]["label-validvalue"] = "<fmt:message key="alert.comboBox.invalidValue"/>";
			label["alert"]["label-validvalue"] = "<fmt:message key="alert.comboBox.invalidValue"/>";
			label["alert"]["label-entityExists"] = "<fmt:message key="alert.personalEntityList.entityExists"/>";
			label["alert"]["label-sumEntityNotSelected"] = "<fmt:message key="alert.personalEntityList.sumEntityNotSelected"/>";
			/*End Added by Imed b on 20-02-2014*/
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("entityMonitor.do");
		    /*
 			 * Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
 			 */
			// get the system date
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			// get the date format
			var dateFormat ='${sessionScope.CDM.dateFormatValue}';
			// get the application name
			var appName = "<%=SwtUtil.appName%>";


			// This method is used to close the window
			function closeWindow() {
				window.close();
			}

			// This function opens the help screen
			function help(){
				openWindow(buildPrintURL('print','Personal Entity List'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

		</script>
    <%@ include file="/angularscripts.jsp"%>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
		<form id="exportDataForm" target="tmp" method="post">


		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.personalEntityList.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>
i_