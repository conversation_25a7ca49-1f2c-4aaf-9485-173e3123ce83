<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="archiveSearch.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
#partyLink {
	width:20px !important;

}</style>
<SCRIPT language="JAVASCRIPT">
var appName = "<%=SwtUtil.appName%>";
var dateSelected = false;
var initialscreen = "${requestScope.initialinputscreen}";


var extraFilter="";
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

var currencyFormat = "${sessionScope.CDM.currencyFormat}";
var dateFormat = "${sessionScope.CDM.dateFormat}";
var sysDateAsstring = "${requestScope.sysDateAsstring}";
mandatoryFieldsArray = ["movementsearchValueFromDateAsString"] ;
function submitForm(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].extraFilter.value = extraFilter;
	document.forms[0].submit();

	}

function getAmtValue(amt){
var amtvalue='';
 for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);
	if(el!=',' && el!='.'){
	 amtvalue += el;
	}
 }
 return amtvalue;
}

function validateAmount(amtover,amtunder){

  if(amtover !="" && amtunder !="")
    {
	var amountOver = new Number(getAmtValue(amtover)) ;
	var amountUnder = new Number(getAmtValue(amtunder)) ;
	if(amountUnder >= amountOver){
	return true;
	}
	else{
	return false;
	}

   }
   else{
	return true;
   }

}

function checkAccountType(methodName)
{
	enableFields();
	document.forms[0].method.value = methodName;

	document.forms[0].submit();

}

function enableFields(){
	document.forms[0].elements["movementsearch.id.entityId"].disabled = "";
	document.forms[0].elements['movementsearch.valueFromDateAsString'].disabled="";
	document.forms[0].elements['movementsearch.valueToDateAsString'].disabled="";
	document.forms[0].elements["movementsearch.matchStatus"].disabled = "";
	document.forms[0].elements["movementsearch.accountClass"].disabled = "";
	document.forms[0].elements['movementsearch.currencyCode'].disabled="";

}
function getRadioButtonValue(button){
var d = button;
var len = d.length ;
for(i=0;i<len;i++)
{
	if(d[i].checked)
	 return d[i].value;
}
if(i == len)
 return "null";
}
/**
* This method is used to open the Change Movement screen with corresponding movement details, when click the change button.
*
* @param methodName
* @return param
*/
function getMovementDetails(methodName){
    if (document.forms[0].elements['movementsearch.valueFromDateAsString'].value != "") {

        var retValue="true";

		if(document.forms[0].elements['movementsearch.archiveId'].value == "")
		{
				alert("<fmt:message key='alert.enterValidPageNumber'/>");
				retValue=false;
		}

		if(retValue=="true"){

			if(validateCurrency(document.forms[0].elements['movementsearch.amountoverasstring'],'movementsearch.movementsearch.amountoverasstring',currencyFormat))
			{

				if(validateCurrency(document.forms[0].elements['movementsearch.amountunderasstring'],'movementsearch.movementsearch.amountunderasstring',currencyFormat))
				{



				var amountover = document.forms[0].elements['movementsearch.amountoverasstring'].value ;
				var amountunder = document.forms[0].elements['movementsearch.amountunderasstring'].value;
				if(validateAmount(amountover,amountunder)){
				if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'movementsearch.valuefrom',dateFormat)){
				if( comparedates(document.forms[0].elements['movementsearch.valueFromDateAsString'].value,document.forms[0].elements['movementsearch.valueToDateAsString'].value,dateFormat,'From Date','To Date'))
				{


					if(validateField(document.forms[0].elements['movementsearch.timefrom'],'movementsearch.timefrom','timePat'))
					{
						if(validateField(document.forms[0].elements['movementsearch.timeto'],'movementsearch.timeto','timePat'))
						{

							if(validateField(document.forms[0].elements['movementsearch.reference'],'movementsearch.reference','alphaNumPatWithoutPercentage'))
							{
								if(validateField(document.forms[0].elements['movementsearch.productType'],'movementsearch.productType','alphaNumPatWithoutPercentage'))
								{

									if(validateField(document.forms[0].elements['movementsearch.matchingParty'],'movementsearch.matchingParty','alphaNumPatWithoutPercentage'))
									{


										elementTrim(document.forms[0]);
										var movementType = getRadioButtonValue(document.forms[0].elements['movementsearch.movementType']);
										var sign = getRadioButtonValue(document.forms[0].elements['movementsearch.sign']);
										var predictStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.predictStatus']);

										var extBalStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.extBalStatus']);
										var currencyCode = document.forms[0].elements["movementsearch.currencyCode"].value;


										var beneficiaryId = document.forms[0].elements["movementsearch.beneficiaryId"].value;
										var custodianId = document.forms[0].elements["movementsearch.custodianId"].value;

										var positionlevel = document.forms[0].elements["movementsearch.positionLevelAsString"].value;

										var accountId = document.getElementById("accountId").value;

										var group = document.getElementById("groupId").value ;

										var metaGroup = document.getElementById("metagroupId").value;

										var bookCode =document.getElementById("bookId").value;

										var valueFromDateAsString = document.forms[0].elements['movementsearch.valueFromDateAsString'].value;
										var valueToDateAsString = document.forms[0].elements['movementsearch.valueToDateAsString'].value;
										 var timefrom = document.forms[0].elements['movementsearch.timefrom'].value;
										var timeto = document.forms[0].elements['movementsearch.timeto'].value;
										var reference = document.forms[0].elements['movementsearch.reference'].value;
										var messageId = document.forms[0].elements['movementsearch.messageId'].value;
										var inputDateAsString = document.forms[0].elements['movementsearch.inputDateAsString'].value;
										var counterPartyId = document.forms[0].elements['movementsearch.counterPartyId'].value;
										var matchStatus = getRadioButtonValue(document.forms[0].elements['movementsearch.matchStatus']);
										var accountClass = getRadioButtonValue(document.forms[0].elements['movementsearch.accountClass']);
										if(document.forms[0].elements['movementsearch.referenceflag'].checked == false){
											 referenceFlag = "N";
										 }
										 else{
											 referenceFlag = "Y";
										 }

										if (inputDateAsString != "") {
											if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat)){
											} else {
												document.forms[0].elements['movementsearch.inputDateAsString'].focus();
												return false;
												}
										}

										var openFlag=getRadioButtonValue(document.forms[0].elements["movementsearch.openFlag"]);

										var userid="<%=SwtUtil.getCurrentUserId(request.getSession())%>";
										if (currencyCode=="All")
											currencyCode="All:"+userid;

										var uetr = document.forms[0].elements['movementsearch.uetr'].value;
										var matchingParty = document.forms[0].elements['movementsearch.matchingParty'].value;
										var productType = document.forms[0].elements['movementsearch.productType'].value;
										var postingDateFrom = document.forms[0].elements['movementsearch.postingFromDateAsString'].value;
										var postingDateTo = document.forms[0].elements['movementsearch.postingToDateAsString'].value;

										var compare_date = 0;

										if (postingDateFrom == "" && postingDateTo == ""){
											compare_date = 1;
										}
										else{
											if(validateDateField(document.forms[0].elements['movementsearch.postingFromDateAsString'],'fromDateAsString',dateFormat)){
											if(document.forms[0].elements['movementsearch.postingToDateAsString'].value != ""){
												if (validateDateField(document.forms[0].elements['movementsearch.postingToDateAsString'],'toDateAsString',dateFormat)){
													compare_date = comparedates(document.forms[0].elements['movementsearch.postingFromDateAsString'].value,document.forms[0].elements['movementsearch.postingToDateAsString'].value,dateFormat,'From Date','To Date');
												}
											}else if(document.forms[0].elements['movementsearch.postingToDateAsString'].value == ""){
												compare_date =1;
											}
										}
										}
										if (document.forms[0].elements['movementsearch.uetr'].value){
						                 	if(!validateUETR(document.forms[0].elements['movementsearch.uetr'].value))
						                 	return ;
						                 	}
										if (compare_date == 1) {
											document.forms[0].method.value = methodName;
											var param = 'outstandingmovement.do?method='+methodName;
											param += '&entityId=' + document.forms[0].elements['movementsearch.id.entityId'].value;
											param += '&movementType=' + movementType ;
											param += '&sign=' + sign ;
											param += '&predictStatus=' + predictStatus ;

											param += '&extBalStatus=' + extBalStatus ;
											param += '&amountover=' + amountover ;
											param += '&amountunder=' + amountunder;
											param += '&currencyCode=' + currencyCode  ;
											param += '&beneficiaryId=' + beneficiaryId ;
											param += '&custodianId=' + custodianId ;
											param += '&positionlevel=' + positionlevel ;
											param += '&accountId=' + accountId ;
											param += '&group=' + group  ;
											param += '&metaGroup=' + metaGroup ;
											param += '&bookCode=' + bookCode ;
											param += '&valueFromDateAsString=' + valueFromDateAsString ;
											param += '&valueToDateAsString=' + valueToDateAsString
											 param += '&timefrom=' + timefrom  ;
											 param += '&timeto=' + timeto ;
											param += '&reference=' + escape(encodeURIComponent(reference));
											param += '&messageId=' + messageId ;
											param += '&inputDateAsString=' + inputDateAsString ;
											param += '&counterPartyId=' + counterPartyId ;
											param += '&matchStatus=' + matchStatus  ;
											param += '&initialinputscreen=' + initialscreen  ;
											param += '&accountClass=' + accountClass  ;
											param += '&archiveId='+ document.forms[0].elements['movementsearch.archiveId'].value;
											param += '&referenceFlag=' + referenceFlag ;

											param += '&uetr=' + uetr;
											param += '&matchingParty=' + matchingParty;
											param += '&productType=' + productType;
											param += '&postingDateFrom=' + postingDateFrom;
											param += '&postingDateTo=' + postingDateTo;
											param += '&extraFilter=' + extraFilter;

													param += '&openFlag=' + openFlag ;

											openWindow(param,'movementsearchlistWindow','left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
											setParentChildsFocus();
											return  param;
										}
									}else
									{
										document.forms[0].elements['movementsearch.matchingParty'].focus();
									}
								}else
								{
									document.forms[0].elements['movementsearch.productType'].focus();
								}
							}else
							{
								document.forms[0].elements['movementsearch.reference'].focus();
							}
						}else
						{
						document.forms[0].elements['movementsearch.timeto'].focus();
						}
					}else
					{
					document.forms[0].elements['movementsearch.timefrom'].focus();
					}
				}
			}
			}else
			{
				alert('<fmt:message key="movSearch.alert.amountunder"/>') ;
				document.forms[0].elements['movementsearch.amountunderasstring'].focus();
			}
			}else
			{
			document.forms[0].elements['movementsearch.amountunderasstring'].focus();
			}
		}else
		{
		document.forms[0].elements['movementsearch.amountoverasstring'].focus();

		}
		}
	} else {
		alert("<fmt:message key='alert.acctbreakdownmonitor.date'/>");
		document.forms[0].elements['movementsearch.valueFromDateAsString'].focus();
	}
}

function validateFormAmount(objForm){
  var elementsRef = new Array(2);


  elementsRef[0] = objForm.elements["movementsearch.amountoverasstring"];
  elementsRef[1] = objForm.elements["movementsearch.amountunderasstring"];


  if(elementsRef[0].value != "" && elementsRef[1].value != "")
{
  var amountover=parseFloat(elementsRef[0].value);
  var amountunder=parseFloat(elementsRef[1].value);

  if(amountover > amountunder)
	{
	  alert('<fmt:message key="movSearch.alert.amountoverLess"/>');
	  return false;
	}
	else
    return true;
}
else
	return true;
}



function checkDate()
{

var enteredDate = document.forms[0].elements['movementsearch.valueFromDateAsString'].value

if(enteredDate!=""){

if(compareTwoDates(sysDateAsstring,enteredDate,dateFormat)== true)
		{

		getMovementDetails('flex');
		}
	else
		{
		alert('<fmt:message key="movSearch.alert.valueFrom"/>');
		return false;
		}
	}
else{
	document.forms[0].elements['movementsearch.valueFromDateAsString'].value = sysDateAsstring ;
	getMovementDetails('flex')
}
}

/**
* This method is used to call when load the file, set the default values.
*
* @return none
*/
function bodyOnLoad()
{
extraFilter= "${requestScope.extraFilter}";
var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.id.entityId"],document.getElementById("entityName"));
var archiveDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.archiveId"],document.getElementById("archiveName"));
var currencyCodeDropBoxElement = new SwSelectBox(document.forms[0].elements["movementsearch.currencyCode"],document.getElementById("currencyName"));

var positionLevelDropBoxElement= new SwSelectBox(document.forms[0].elements["movementsearch.positionLevelAsString"],document.getElementById("positionName"));
var divElement = document.getElementById("dropdowndiv_5");
	var selectElement = document.forms[0].elements["movementsearch.bookCode"];
	var idElement = document.forms[0].elements["bookId"];
	var descElement = document.forms[0].elements["bookCodeName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_5"];
	var idLength = 12;
	var descLength = 30;

	var bookCodeDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

	bookCodeDropBoxElement.idElementMouseDown = null;
	bookCodeDropBoxElement.idElementKeyDown = null;

	bookCodeDropBoxElement.idElement.onmousedown = null;
	bookCodeDropBoxElement.idElement.onkeydown = null;


var divElement = document.getElementById("dropdowndiv_6");
	var selectElement = document.forms[0].elements["movementsearch.group"];
	var idElement = document.forms[0].elements["groupId"];
	var descElement = document.forms[0].elements["groupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_6"];
	var idLength = 12;
	var descLength = 50;

	var groupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

	groupDropBoxElement.idElementMouseDown = null;
	groupDropBoxElement.idElementKeyDown = null;

	groupDropBoxElement.idElement.onmousedown = null;
	groupDropBoxElement.idElement.onkeydown = null;

var divElement = document.getElementById("dropdowndiv_7");
	var selectElement = document.forms[0].elements["movementsearch.metaGroup"];
	var idElement = document.forms[0].elements["metagroupId"];
	var descElement = document.forms[0].elements["metaGroupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_7"];
	var idLength = 12;
	var descLength = 50;
	var metaGroupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength,"true");

    document.getElementById("metaGroupName").value=document.getElementById("metaGroupName").value.replace(new RegExp("(&nbsp;)","g")," ");
    document.getElementById("groupName").value=document.getElementById("groupName").value.replace(new RegExp("(&nbsp;)","g")," ");

	metaGroupDropBoxElement.idElementMouseDown = null;
	metaGroupDropBoxElement.idElementKeyDown = null;

	metaGroupDropBoxElement.idElement.onmousedown = null;
	metaGroupDropBoxElement.idElement.onkeydown = null;

var divElement = document.getElementById("dropdowndiv_8");
	var selectElement = document.forms[0].elements["movementsearch.accountId"];
	var idElement = document.forms[0].elements["accountId"];
	var descElement = document.forms[0].elements["Acctname"];
	var arrowElement = document.forms[0].elements["dropdownbutton_8"];
	var idLength = 12;
	var descLength = 30;
	var accountDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

	accountDropBoxElement.idElementMouseDown = null;
	accountDropBoxElement.idElementKeyDown = null;

	accountDropBoxElement.idElement.onmousedown = null;
	accountDropBoxElement.idElement.onkeydown = null;

document.forms[0].initialinputscreen.value = initialscreen ;
if(initialscreen == "matchSearch") {
	<c:if test="${requestScope.methodName != 'searchAccFromMatch'}">
	document.forms[0].elements["movementsearch.sign"][2].checked = true;
	document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;
	document.forms[0].elements["movementsearch.movementType"][2].checked = true;
	document.forms[0].elements["movementsearch.matchStatus"][0].checked = true;
	document.forms[0].elements["movementsearch.accountClass"][5].checked = true;
	</c:if>
	<c:if test="${requestScope.methodName == 'searchAccFromMatch'}">
	document.forms[0].elements["movementsearch.matchStatus"][0].checked = true;
	</c:if>


} else {

	<c:if test="${requestScope.methodName != 'populateAcc'}">

	document.forms[0].elements["movementsearch.sign"][2].checked = true;
	document.forms[0].elements["movementsearch.predictStatus"][3].checked = true;

	//default value of extbalstatus set to all.
	document.forms[0].elements['movementsearch.extBalStatus'][2].checked = true;
	document.forms[0].elements["movementsearch.movementType"][2].checked = true;
	document.forms[0].elements["movementsearch.openFlag"][1].checked = true;

	document.forms[0].elements["movementsearch.matchStatus"][8].checked = true;

	document.forms[0].elements["movementsearch.accountClass"][5].checked = true;
	</c:if>
}


	var selectedEntityId = document.forms[0].elements['movementsearch.id.entityId'].value;
if(selectedEntityId=="All"){
	//disable the party search and only allow the user to enter free text for the party field
	document.getElementById('partyLink1').disabled= "true";
	document.getElementById('partyLink2').disabled= "true";
	document.getElementById('partyLink3').disabled= "true";

	//disable the Book, Group, Metagroup, and Account fields
	document.forms[0].elements["bookId"].disabled= "true";
	document.forms[0].elements["groupId"].disabled= "true";
	document.forms[0].elements["metagroupId"].disabled= "true";
	document.forms[0].elements["accountId"].disabled= "true";
	document.getElementById('dropdownbutton_5').disabled= "true";
	document.getElementById('dropdownbutton_6').disabled= "true";
	document.getElementById('dropdownbutton_7').disabled= "true";
	document.getElementById('dropdownbutton_8').disabled= "true";
}


var selectedEntityId= document.forms[0].elements['movementsearch.id.entityId'].value;
if(selectedEntityId=="All"){
	//disable the party search and only allow the user to enter free text for the party field
	document.getElementById('partyLink1').disabled= "true";
	document.getElementById('partyLink2').disabled= "true";
	document.getElementById('partyLink3').disabled= "true";

	//disable the Book, Group, Metagroup, and Account fields
	document.forms[0].elements["bookId"].disabled= "true";
	document.forms[0].elements["groupId"].disabled= "true";
	document.forms[0].elements["metagroupId"].disabled= "true";
	document.forms[0].elements["accountId"].disabled= "true";
	document.getElementById('dropdownbutton_5').disabled= "true";
	document.getElementById('dropdownbutton_6').disabled= "true";
	document.getElementById('dropdownbutton_7').disabled= "true";
	document.getElementById('dropdownbutton_8').disabled= "true";
}

}


function populateDropBoxes()
{
	ShowErrMsgWindow('${actionError}');
	bodyOnLoad();
}

function comparedates(date1,date2,format,date1Caption,date2Caption)
{
	var retValue = "true";
	var strdate1 = new String(date1);
	var strdate2 = new String(date2);

	if( typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null)
	{
		strdate1 = strdate1.trim();
		strdate2 = strdate2.trim();
		if(strdate1.length > 0 && strdate2.length > 0)
		{
			if(format == "datePat1")
			{
				var parts = date1.split("/");
				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[0]);
				date1.setMonth(parts[1] - 1);

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[0]);
				date2.setMonth(parts[1] - 1);

				if(date2 < date1)
					retValue = "false";
			}
			if(format == "datePat2")
			{
				var parts = date1.split("/");

				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[1]);
				date1.setMonth(parts[0] - 1);

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[1]);
				date2.setMonth(parts[0] - 1);

				if(date2 < date1)
					retValue = "false";

			}
		}

	}
	if(retValue =="false"){
	alert('' + date2Caption + " " + '<fmt:message key="movSearch.alert.dateComparison"/>' + " "+ date1Caption+'');
	 return false;
	 }else{
	 return true;
	 }
}


function party(flag,elementId,elementName){
var entityId = document.forms[0].elements['movementsearch.id.entityId'].value;
var url='party.do?method=preSearchParties&entityId='+entityId;
url += '&custodianFlag='+flag;
url += '&idElementName='+elementId;
url += '&descElementName='+elementName;
openWindow(url,'SearchParty','left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes','true');
}

function clearCounterPartyDesc()
{
 document.getElementById("partyName").innerText = "";

}

function clearBeneficiaryDesc()
{
 document.getElementById("partyName1").innerText = "";

}
function clearCustodianDesc()
{
 document.getElementById("partyName2").innerText = "";

}

function clearBookCodeDesc()
{
 document.getElementById("bookCodeName").innerText = "";

}
function clearGroupDesc()
{
 document.getElementById("groupName").innerText = "";

}
function clearMetaGroupDesc()
{
 document.getElementById("metaGroupName").innerText = "";

}
function clearAccountDesc()
{
 document.getElementById("Acctname").innerText = "";
}
function clearCcyDesc()
{
 document.getElementById("currencyName").innerText = "";
}



function clearPositionDesc()
{
 document.getElementById("positionName").innerText = "";
}

//  This function is called when we have 3 columns in drop down and on selection only first two columns to be displayed
function populateValues_Jsp(mainobject)
{
	var selectedIndex = mainobject.selectElement.selectedIndex;
	if(selectedIndex < 0 ) selectedIndex = 0;

	var selectedOption = mainobject.selectElement.options[selectedIndex];
	var showText = selectedOption.text ;
	showText = showText.substring(12,42);
	mainobject.idElement.value = selectedOption.value;

	mainobject.descElement.value = showText ;

	return mainobject ;
}

function validateUETR() {
	  var pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-(8|9|a|b)[0-9a-f]{3}-[0-9a-f]{12}$/;
	  if (pattern.test(document.getElementById("movementsearch.uetr").value)) {
	    return true ;
	  }
	  else
	  {
		  alert("Invalid UETR format!");
		  return false;
	  }
}

</SCRIPT>

<SCRIPT language="JAVASCRIPT">
	var dateFormatValue = "${sessionScope.CDM.dateFormatValue}";
	var cal = new CalendarPopup("caldiv",false,"calFrame");
    cal.setCssPrefix("CAL");
	cal.offsetX = 18;
    cal.offsetY = 0;

	var cal2 = new CalendarPopup("caldiv",false,"calFrame");
    cal2.setCssPrefix("CAL");
    cal2.offsetX = 18;
    cal2.offsetY = 0;

	var cal3 = new CalendarPopup("caldiv",false,"calFrame");
    cal3.setCssPrefix("CAL");
    cal3.offsetX = 18;
    cal3.offsetY = 0;

	var cal4 = new CalendarPopup("caldiv",false,"calFrame");
    cal4.setCssPrefix("CAL");
    cal4.offsetX = 20;
    cal4.offsetY = -88;

	var cal5 = new CalendarPopup("caldiv",false,"calFrame");
    cal5.setCssPrefix("CAL");
    cal5.offsetX = 20;
    cal5.offsetY = -88;
function validateToDateField(){
	if(document.forms[0].elements['movementsearch.valueToDateAsString']!=null && document.forms[0].elements['movementsearch.valueToDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueToDateAsString'],'movementsearch.valueto',dateFormat )){
		}
	}
}

function validateFromDateField(){
	if(document.forms[0].elements['movementsearch.valueFromDateAsString']!=null && document.forms[0].elements['movementsearch.valueFromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.valueFromDateAsString'],'movementsearch.valueFrom',dateFormat )){
		}
	}
}

function validateArchiveDateField(){
	if(document.forms[0].elements['movementsearch.inputDateAsString']!=null && document.forms[0].elements['movementsearch.inputDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['movementsearch.inputDateAsString'],'movementsearch.inputdate',dateFormat )){
		}
	}
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["movementsearch.valueFromDateAsString"];

  return validate(elementsRef);
}

function openAddColsScreen(methodName){
	var param = '/' + appName + '/outstandingmovement.do?method='+methodName;
	var mainWindow = openWindow (param, 'addColumns','left=10,top=230,width=710,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
	return false;
}

</SCRIPT>

</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();"
	onUnload="call()">



<form action="movementsearch.do" method="post">
	<input name="method" type="hidden" value="display">
	<input name="selectedEntityName" type="hidden" value="">
	<input name="initialinputscreen" type="hidden" value="">
    <input name="extraFilter" type="hidden" value="">


	<c:set var="CDM" value="${sessionScope.CDM}" />


	<!----------------------------------caldiv----------------------------------------------------------------->
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>
	<!-----------------------------------end of caldiv----------------------------------------------->



	<div id="dropdowndiv_5"
    style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 339px; visibility: hidden"
    class="swdropdown">
    <select class="htmlTextFixed" id="movementsearch.bookCode" name="movementsearch.bookCode" size="10"
        style="width:329px;z-index:99;">
        <c:forEach var="book" items="${requestScope.bookdetails}">
            <option value="${book.value}" <c:if test="${book.value == movementsearch.bookCode}">selected</c:if>>
                ${book.label}
            </option>
        </c:forEach>
    </select>
</div>

<div id="dropdowndiv_6"
    style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 370px; visibility: hidden"
    class="swdropdown">
    <select class="htmlTextFixed" id="movementsearch.group" name="movementsearch.group" size="10"
        style="width:473px;z-index:99;">
        <c:forEach var="group" items="${requestScope.groupdetails}">
            <option value="${group.value}" <c:if test="${group.value == movementsearch.group}">selected</c:if>>
                ${group.label}
            </option>
        </c:forEach>
    </select>
</div>

<div id="dropdowndiv_7"
    style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 403px; visibility: hidden"
    class="swdropdown">
    <select class="htmlTextFixed" id="movementsearch.metaGroup" name="movementsearch.metaGroup" size="10"
        style="width:473px;z-index:99;">
        <c:forEach var="metaGroup" items="${requestScope.metagroupdetails}">
            <option value="${metaGroup.value}" <c:if test="${metaGroup.value == movementsearch.metaGroup}">selected</c:if>>
                ${metaGroup.label}
            </option>
        </c:forEach>
    </select>
</div>

<div id="dropdowndiv_8"
    style="z-index: 99; position: absolute; width: 200px; left: 367px; top: 463px; visibility: hidden"
    class="swdropdown">
    <select class="htmlTextFixed" id="movementsearch.accountId" name="movementsearch.accountId" size="10"
        style="width:329px;z-index:99;">
        <c:forEach var="account" items="${requestScope.accountdetails}">
            <option value="${account.value}" <c:if test="${account.value == movementsearch.accountId}">selected</c:if>>
                ${account.label}
            </option>
        </c:forEach>
    </select>
</div>
	<!--------------entity drop down---------------->

	<div id="MovementSearch"
		style="position: absolute; left: 20px; top: 19px; width: 941px; height: 62px; border: 2px outset;"
		color="#7E97AF">
	<div id="MovementSearch"
		style="position: absolute; left: -5px; top: 4px; width: 780px; height: 300;">
	<table width="725" border="0" cellpadding="0" cellspacing="0" height="50">
		<tr>
			<td width="47"><b>&nbsp;&nbsp;&nbsp;<fmt:message key="movementsearch.archive" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="140">
				<select titleKey="tooltip.selectArchiveId" class="htmlTextAlpha" tabindex="1" id="movementsearch.archiveId" name="movementsearch.archiveId" style="width:140px;">
					<c:forEach var="archive" items="${requestScope.archives}">
						<option value="${archive.value}" <c:if test="${archive.value == movementsearch.archiveId}">selected</c:if>>
							${archive.label}
						</option>
					</c:forEach>
				</select>
			</td>
			<td width="20">&nbsp;</td>
			<td width="490"><span id="archiveName" class="spantext"></span></td>
		</tr>
		<tr>
			<td width="47"><b>&nbsp;&nbsp;&nbsp;<fmt:message key="movementsearch.entity" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="140">
				<select titleKey="tooltip.selectEntityId" class="htmlTextAlpha" tabindex="2" id="movementsearch.id.entityId" name="movementsearch.id.entityId" onchange="submitForm('archiveSearch')" style="width:140px;">
					<c:forEach var="entity" items="${requestScope.entities}">
						<option value="${entity.value}" <c:if test="${entity.value == movementsearch.id.entityId}">selected</c:if>>
							${entity.label}
						</option>
					</c:forEach>
				</select>
			</td>
			<td width="20">&nbsp;</td>
			<td width="490"><span id="entityName" class="spantext"></span></td>
		</tr>
	</table>

	</div>
	</div>

	<!----------------end of entity drop down------------------>

	<div id="movementsearchparam"
		style="position: absolute; left: 20px; top: 85px; width: 941px; height: 542px; border: 2px outset;"
		color="#7E97AF">
	<div id="movementsearchparam"
		style="position: absolute; left: 0px; top: 0px; width: 591px; height: 38px;">


	<!--------------------start of extra fieldset------------------------------------------>
	<div style="position: absolute; left: 187px; height: 525px; top: 10px;">
	<fieldset style="width: 622px; border: 2px groove; height: 525px;">
	<div style="position: absolute; left: 8px; top: 5px;">
	<table width="569px" border="0" cellpadding="0" cellspacing="1"
		height="">
		<!------value date---->
		<tr height="27px">
				<td width="120px"><b><fmt:message key="movementsearch.amountover" /></b></td>
				<td width="28px">&nbsp;</td>
				<td width="160px">
					<input type="text" tabindex="5" name="movementsearch.amountoverasstring"
						style="width:160px;height: 22px;"
						titleKey="tooltip.amountFrom"
						onchange="return validateCurrency(this, 'movementsearch.amountover', currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)"
						class="htmlTextNumeric" maxlength="28"
						value="${movementsearch.amountoverasstring}" />
				</td>

				<td width="20px">&nbsp;</td>
				<td width="45px"><b><fmt:message key="movementsearch.valueto" /></b></td>
				<td width="28px">&nbsp;</td>
				<td width="160px" height="28">
					<input type="text" titleKey="tooltip.amountTo" tabindex="6"
						class="htmlTextNumeric" name="movementsearch.amountunderasstring"
						style="width:160px;height: 22px;"
						onchange="return validateCurrency(this, 'movementsearch.amountunder', currencyFormat, document.forms[0].elements['movementsearch.currencyCode'].value)"
						maxlength="28" value="${movementsearch.amountunderasstring}" />
				</td>
			</tr>

	</table>

	<table width="622px" border="0" cellpadding="0" cellspacing="1"
		height="80">
		<tr height="27">
			<td width="127px"><b><fmt:message key="movementsearch.valuefrom" />*</b></td>
			<td width="30px">&nbsp;</td>
			<td width="160px" id="movementsearchValueFromDateAsString">
				<c:if test="${requestScope.initialinputscreen ne 'matchSearch'}">
					<input type="text" titleKey="tooltip.enterValueDateFrom"
						maxlength="10" tabindex="7" class="htmlTextAlpha"
						name="movementsearch.valueFromDateAsString"
						style="width:80px;margin-bottom: 5px;height: 22px;"
						onchange="if(validateForm(document.forms[0])) {validateFromDateField();}"
						onmouseout="dateSelected=false;"
						value="${movementsearch.valueFromDateAsString}" />
					<a title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="8"
						name="datelink" id="datelink"
						onclick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;">
						<img src="images/calendar-16.gif">
					</a>
				</c:if>
				<c:if test="${requestScope.initialinputscreen eq 'matchSearch'}">
					<input type="text" titleKey="tooltip.enterValueDateFrom"
						maxlength="10" tabindex="7" class="htmlTextAlpha"
						name="movementsearch.valueFromDateAsString"
						style="width:80px;margin-bottom: 5px;height: 22px;"
						onchange="if(validateForm(document.forms[0])) {validateFromDateField();}"
						onmouseout="dateSelected=false;"
						value="${movementsearch.valueFromDateAsString}" />
					<a title='<fmt:message key="tooltip.selectFromDate"/>' tabindex="8"
						name="datelink" id="datelink"
						onclick="cal.select(document.forms[0].elements['movementsearch.valueFromDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;">
						<img src="images/calendar-16.gif">
					</a>
				</c:if>
			</td>
			<td width="20px">&nbsp;</td>
			<td width="79px"><b><fmt:message key="movementsearch.valueto" /></b></td>
			<td width="220px">
				<c:if test="${requestScope.initialinputscreen ne 'matchSearch'}">
					<input type="text" titleKey="tooltip.enterValueDateTo"
						maxlength="10" tabindex="9" class="htmlTextAlpha"
						name="movementsearch.valueToDateAsString"
						style="width:80px;margin-bottom: 5px;height: 22px;"
						onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();"
						onmouseout="dateSelected=false;"
						value="${movementsearch.valueToDateAsString}" />
					<a title='<fmt:message key="tooltip.selectToDate"/>' tabindex="10"
						name="datelink2" id="datelink2"
						onclick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;">
						<img src="images/calendar-16.gif">
					</a>
				</c:if>
				<c:if test="${requestScope.initialinputscreen eq 'matchSearch'}">
					<input type="text" titleKey="tooltip.enterValueDateTo"
						maxlength="10" tabindex="9" class="htmlTextAlpha"
						name="movementsearch.valueToDateAsString"
						style="width:80px;margin-bottom: 5px;height: 22px;"
						onchange="return validateField(this,'movementsearch.valueto',dateFormat);validateToDateField();"
						onmouseout="dateSelected=false;"
						value="${movementsearch.valueToDateAsString}" />
					<td width="152px">&nbsp;
						<a title='<fmt:message key="tooltip.selectToDate"/>' tabindex="10"
							name="datelink2" id="datelink2"
							onclick="cal2.select(document.forms[0].elements['movementsearch.valueToDateAsString'],'datelink2',dateFormatValue);dateSelected=true;return false;">
							<img src="images/calendar-16.gif">
						</a>
					</td>
				</c:if>
			</td>
		</tr>



		<!-------------currency------>
		<tr height="25">
			<td width="120px"><B><fmt:message key="movementsearch.currency" /></B></td>
			<td width="28px">&nbsp;</td>
			<!-- Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency change should not change Status and Account class to All,The parameter of submitForm method is changed as archivePopulateAcc instead of archiveSearch  -->
			<td width="140px">
				<select titleKey="tooltip.movCurrency" class="htmlTextAlpha" tabindex="11" id="movementsearch.currencyCode" name="movementsearch.currencyCode" style="width:56px" onchange="submitForm('archivePopulateAcc');">
					<c:forEach var="currency" items="${requestScope.currencydetails}">
						<option value="${currency.value}" ${currency.value == movementsearch.currencyCode ? 'selected="selected"' : ''}>
							${currency.label}
						</option>
					</c:forEach>
				</select>
			</td>

			<td width="20px">&nbsp;</td>
			<td width="280px" colspan="4"><span id="currencyName"
				class="spantext"></span></td>
		</tr>

		<!-----Swift message type------->
		<tr height="25">
			<td width="120px"><B><fmt:message key="movementsearch.messageId" /></B></td>
			<td width="28px">&nbsp;</td>
			<td width="140px">
				<select titleKey="tooltip.selectMsgFormat" class="htmlTextAlpha" tabindex="12" id="movementsearch.messageId" name="movementsearch.messageId" style="width:140px;">
					<c:forEach var="swiftMessage" items="${requestScope.swiftMessages}">
						<option value="${swiftMessage.value}" ${swiftMessage.value == movementsearch.messageId ? 'selected="selected"' : ''}>
							${swiftMessage.label}
						</option>
					</c:forEach>
				</select>
			</td>

			<td width="20px">&nbsp;</td>
			<td width="280px" colspan="4">&nbsp;</td>
		</tr>
		<div style="z-index: 99; position: absolute; left: 0px; top: 0px;">
		<table width="620px" border="0" cellpadding="0" cellspacing="1"
			height="190">

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.counterparty" /></B></td>
				<td width="30px">&nbsp;</td>

			<td width="150px" style="padding-left: 10px;">
					<input class="htmlTextAlpha"
						   type="text"
						   name="movementsearch.counterPartyId"
						   onchange="clearCounterPartyDesc()"
						   tabindex="14"
						   style="width:120px; margin-bottom: 2px; margin-right: 5px; height: 22px;"
						   titleKey="tooltip.counterId"
						   value="${movementsearch.counterPartyId}" />

					<input title="<fmt:message key='tooltip.clickSelCounterId'/>"
						   id="partyLink1"
						   type="button"
						   value="..."
						   tabindex="15"
						   onClick="javascript:party('N','movementsearch.counterPartyId','partyName')" />
				</td>



				<td width="47px">&nbsp;</td>

				<td width="280px"><span id="partyName" name="partyName"
					class="spantext"></span></td>




			</tr>

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.beneficiary" /></B></td>
				<td width="30px">&nbsp;</td>

				<td width="150px" style="padding-left: 10px;">
					<input class="htmlTextAlpha"
						   type="text"
						   name="movementsearch.beneficiaryId"
						   onchange="clearBeneficiaryDesc()"
						   tabindex="16"
						   style="width:120px; margin-bottom: 2px; margin-right: 5px; height: 22px;"
						   titleKey="tooltip.BeneficiaryID"
						   value="${movementsearch.beneficiaryId}" />

					<input title="<fmt:message key='tooltip.movBeneficiary'/>"
						   id="partyLink2"
						   type="button"
						   value="..."
						   tabindex="17"
						   onClick="javascript:party('N','movementsearch.beneficiaryId','partyName1')" />
				</td>

				<td width="47px">&nbsp;</td>
				<td width="280px"><span id="partyName1" name="partyName1"
					class="spantext"></span></td>


			</tr>

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.custodian" /></B></td>
				<td width="30px">&nbsp;</td>

				<td width="152px" style="padding-left: 10px;">
					<input class="htmlTextAlpha"
						   type="text"
						   name="movementsearch.custodianId"
						   tabindex="18"
						   onchange="clearCustodianDesc()"
						   style="width:120px; margin-bottom: 2px; margin-right: 5px; height: 22px;"
						   titleKey="tooltip.custId"
						   value="${movementsearch.custodianId}" />

					<input title="<fmt:message key='tooltip.movCustodian'/>"
						   id="partyLink3"
						   type="button"
						   value="..."
						   tabindex="19"
						   onClick="javascript:party('Y','movementsearch.custodianId','partyName2')" />
				</td>


				<td width="47px">&nbsp;</td>
				<td width="280px"><span id="partyName2" name="partyName2"
					class="spantext"></span></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.bookcode" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="htmlTextAlpha" id="bookId"
					name="bookId" style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  onchange="clearBookCodeDesc()"
					title='<fmt:message key="tooltip.bookId"/>' readonly="readonly" /><input
					title='<fmt:message key="tooltip.clickSelBookId"/>'
					id="dropdownbutton_5" type="button" style="width:20px;" tabindex="20" value="...">

				</td>
				<td width="7px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="bookCodeName" size="20" readonly="readonly" ></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.group" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="textAlpha" id="groupId"
					name="groupId" style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"  onchange="clearGroupDesc()"
					title='<fmt:message key="tooltip.groupId"/>' readonly="readonly"><input
					title='<fmt:message key="tooltip.clickSelGroupId"/>'
					id="dropdownbutton_6" type="button" tabindex="21" style="width:20px;" value="..."></td>
				<td width="7px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="groupName" size="20" readonly="readonly" ></td>

			</tr>

			<tr height="25">
				<td width="123px"><B><fmt:message key="movementsearch.metagroup" /></B></td>
				<td width="28px">&nbsp;</td>
				<td width="150px" style="padding-left: 10px;"><input styleclass="textAlpha"
					onchange="clearMetaGroupDesc()" id="metagroupId" name="metagroupId"
					style="width:120px; margin-bottom: 2px;margin-right: 5px;height: 22px;"
					title='<fmt:message key="tooltip.metaGroupId"/>' readonly="readonly"><input
					title='<fmt:message key="tooltip.clickSelMetaGroupId"/>'
					id="dropdownbutton_7" type="button" tabindex="22" style="width:20px;" value="..."></td>
				<td width="17px">&nbsp;</td>
				<td><input styleclass="textAlpha" tabindex="-1"
					style="width: 280px; background: transparent; border: thin;"
					name="metaGroupName" size="20" readonly="readonly" ></td>

			</tr>
		</table>
		<table width="680px" cellspacing="0" cellpadding="0" border="0">

			<tr height="25">
				<td width="125px"><B><fmt:message key="movementsearch.inputdate" /></B></td>
				<td width="21px">&nbsp;</td>
				<td width="160px" style="padding-left: 7px;">
						<input type="text"
							   name="movementsearch.inputDateAsString"
							   maxlength="10"
							   class="htmlTextAlpha"

							   titleKey="tooltip.enterInputDate"
							   tabindex="23"
							   style="width:80px; margin-bottom: 5px; height: 22px;"
							   onchange="return validateField(this, 'movementsearch.inputdate', dateFormat); validateArchiveDateField();"
							   onmouseout="dateSelected=false;"
							   value="${movementsearch.inputDateAsString}" />&nbsp;

						<a title="<fmt:message key='tooltip.selectInputDate'/>"
						   tabindex="24"
						   name="datelink3"
						   id="datelink3"
						   onClick="cal3.select(document.forms[0].elements['movementsearch.inputDateAsString'], 'datelink3', dateFormatValue); dateSelected=true; return false;">
							<img src="images/calendar-16.gif" />
						</a>
					</td>

				<td width="80px" colspan="5">
				<td width="80px"><B><fmt:message key="movsearch.timefrom" /></B>
				</td>
				<td width="10px">&nbsp;</td>
				<td width="44px">
					<input type="text" name="movementsearch.timefrom" class="htmlTextNumeric"
						   titleKey="tooltip.enterTomeFrom" tabindex="25"
						   value="${movementsearch.timefrom}"
						   style="width:44px;height: 22px;"
						   onchange="return validateField(this,'movementsearch.timefrom','timePat')" />
				</td>
				<td width="10px">&nbsp;</td>
				<td width="15px"><b><fmt:message key="movementsearch.timeto" /></b></td>
				<td width="10px">&nbsp;</td>
				<td width="30px">
					<input type="text" name="movementsearch.timeto" class="htmlTextNumeric"
						   titleKey="tooltip.enterTimeTo" tabindex="26"
						   style="width:44px;height: 22px;"
						   value="${movementsearch.timeto}"
						   onchange="return validateField(this,'movementsearch.timeto','timePat')" />
				</td>
				<td width="120px">&nbsp;</td>

				</td>
			</tr>
		</table>
		<table width="614px" border="0" cellpadding="0" cellspacing="1"
			height="">

			<tr height="25">
				<td width="150px"><B><fmt:message key="movementsearch.accountid" /></B></td>
				<td width="240px" style="width: 272px; padding-left: 45px;"><input styleclass="textAlpha"
					onchange="clearAccountDesc()" id="accountId" name="accountId"
					style="width:220px; margin-right: 5px;height: 22px;"
					title='<fmt:message key="tooltip.accountID"/>' readonly="readonly"><input
					title='<fmt:message key="tooltip.clickSelAcId"/>'
					id="dropdownbutton_8" type="button" tabindex="27" style="width:20px;"" value="..."></td>
				<td width="10px">&nbsp;</td>
				<td><input tabindex="-1" styleclass="textAlpha"
					style="width: 200px; background: transparent; border: thin;"
					name="Acctname" size="16" readonly="readonly" ></td>

			</tr>
		</table>
			<table width="630px" border="0" cellpadding="0" cellspacing="1" height="">
				<tr height="25">
					<td width="170px"><b><fmt:message key="movementsearch.reference" /></b></td>
					<td width="18px">&nbsp;</td>
					<td width="450px" colspan="6">
						<input type="text"
							   name="movementsearch.reference"
							   class="htmlTextAlpha"
							   titleKey="tooltip.enterRef"
							   tabindex="28"
							   style="width:320px;height: 22px;"
							   maxlength="35"
							   value="${movementsearch.reference}"
							   onchange="return validateField(this, 'movementsearch.reference', 'alphaNumPatWithoutPercentage');" />
					</td>
					<td width="28px">&nbsp;</td>
					<td width="50px"><b><fmt:message key="movementsearch.like" /></b></td>
					<td width="0px">&nbsp;</td>
					<td width="120px">
						<input type="checkbox"
							   name="movementsearch.referenceflag"
							   value="N"
							   ${movementsearch.referenceflag == "Y" ? "checked" : ""}
							   class="htmlTextAlpha"
							   titleKey="tooltip.selectReference"
							   style="width:13px;"
							   tabindex="28" />
					</td>
				</tr>
			</table>

			<table width="475px" border="0" cellpadding="0" cellspacing="1" height="">
				<tr height="25">
					<td width="155px"><b><fmt:message key="movementsearch.uetr"/></b></td>
					<td width="320px">
						<input type="text"
							   name="movementsearch.uetr"
							   class="htmlTextAlpha"
							   titleKey="tooltip.movementSearch.uetr"
							   tabindex="29"
							   style="width:320px; height: 22px;"
							   value="${movementsearch.uetr}" />
					</td>
				</tr>
			</table>

		<table width="402" border="0" cellpadding="0" cellspacing="1" height="">
			<tr height="25">
				<td width="100px"><b><fmt:message key="movementsearch.positionlevel" /></b></td>
				<td width="28px">&nbsp;</td>
				<td width="51px">
					<select class="htmlTextAlpha"
							id="movementsearch.positionLevelAsString"
							name="movementsearch.positionLevelAsString"
							titleKey="tooltip.selectPostionLevel"
							tabindex="29"
							style="width:38px;">
						<c:forEach var="item" items="${requestScope.collLang}">
							<option value="${item.value}" ${item.value == movementsearch.positionLevelAsString ? 'selected' : ''}>${item.label}</option>
						</c:forEach>
					</select>
				</td>
				<td width="28px">&nbsp;</td>
				<td width="133px"><span id="positionName" class="spantext"></span></td>
			</tr>
		</table>

		<table width="348px" border="0" cellpadding="0" cellspacing="1" height="">
			<tr height="25">
				<td width="115px"><b><fmt:message key="movementsearch.matchingParty" /></b></td>
				<td width="150px">
					<input type="text"
						   name="movementsearch.matchingParty"
						   class="htmlTextAlpha"
						   titleKey="tooltip.enterMatchingParty"
						   tabindex="30"
						   style="width:150px;height: 22px;"
						   value="${movementsearch.matchingParty}"
						   onchange="return validateField(this, 'movementsearch.matchingParty', 'alphaNumPatWithoutPercentage');" />
				</td>
			</tr>
			<tr height="25">
				<td width="115px"><b><fmt:message key="movementsearch.productType" /></b></td>
				<td width="150px">
					<input type="text"
						   name="movementsearch.productType"
						   class="htmlTextAlpha"
						   titleKey="tooltip.enterProductType"
						   tabindex="31"
						   style="width:150px;height: 22px;"
						   value="${movementsearch.productType}"
						   onchange="return validateField(this, 'movementsearch.productType', 'alphaNumPatWithoutPercentage');" />
				</td>
			</tr>
		</table>

		<table width="540px" border="0" cellpadding="0" cellspacing="1" height="">
			<tr height="25">
				<td width="122px" style="padding-bottom: 5px;"><b><fmt:message key="movementsearch.postingDateFrom" /></b></td>
				<td width="180px" style="padding-left: 15px;">
					<input type="text"
						   name="movementsearch.postingFromDateAsString"
						   class="htmlTextAlpha"
						   titleKey="tooltip.enterPostingDateFrom"
						   maxlength="10"
						   tabindex="32"

						   style="width:80px;margin-bottom: 5px;height: 21px;"
						   value="${movementsearch.postingFromDateAsString}"
						   onchange="return validateField(this, 'movementsearch.valuefrom', dateFormat);validateFromDateField();"
						   onmouseout="dateSelected=false;" />
					<a title='<fmt:message key="tooltip.selectFromDate"/>'
					   tabindex="32"
					   name="datelink4"
					   id="datelink4"
					   onClick="cal4.select(document.forms[0].elements['movementsearch.postingFromDateAsString'], 'datelink4', dateFormatValue); dateSelected=true; return false;">
						<img src="images/calendar-16.gif">
					</a>
				</td>
				<td width="30px"><b><fmt:message key="movementsearch.valueto" /></b></td>
				<td width="135px">
					<input type="text"
						   name="movementsearch.postingToDateAsString"
						   class="htmlTextAlpha"
						   titleKey="tooltip.enterPostingDateTo"
						   maxlength="10"
						   tabindex="32"

						   style="width:80px;margin-bottom: 5px;height: 21px;"
						   value="${movementsearch.postingToDateAsString}"
						   onchange="return validateField(this, 'movementsearch.valueto', dateFormat);validateToDateField();"
						   onmouseout="dateSelected=false;" />
					<a title='<fmt:message key="tooltip.selectToDate"/>'
					   tabindex="32"
					   name="datelink5"
					   id="datelink5"
					   onClick="cal5.select(document.forms[0].elements['movementsearch.postingToDateAsString'], 'datelink5', dateFormatValue); dateSelected=true; return false;">
						<img src="images/calendar-16.gif">
					</a>
				</td>
			</tr>
		</table>

	</table>
	</div>
	</fieldset>
	</div>
	<!---------------------end of extra fieldset------------------------------------------>

	<!--------------Cash/Sec fieldset--------------------------------------------------->
	<div style="position: absolute; left: 817px; top: 110px;">
	<fieldset style="width: 113px; border: 2px groove; height: 86px;">
	<legend><fmt:message key="movementsearch.cash/sec" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0" height="66">
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="34"
						   id="43"
						   name="movementsearch.movementType"
						   value="C"
						   ${movementsearch.movementType == 'C' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movType" />
					<label title='<fmt:message key="tooltip.movType"/>' for="43">
						<fmt:message key="movementsearch.cash" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="34"
						   id="44"
						   name="movementsearch.movementType"
						   value="U"
						   ${movementsearch.movementType == 'U' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movType" />
					<label title='<fmt:message key="tooltip.movType"/>' for="44">
						<fmt:message key="movementsearch.sec" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="34"
						   id="45"
						   name="movementsearch.movementType"
						   value="B"
						   ${movementsearch.movementType == 'B' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movType" />
					<label title='<fmt:message key="tooltip.movType"/>' for="45">
						<fmt:message key="movementsearch.both" />
					</label>
				</td>
			</tr>
		</table>

	</fieldset>
	</div>
	<!------------------end of cash/sec fieldset--------------------------------------->


	<!----------------------Debit/Credit fieldset--------------------------->

	<div style="position: absolute; left: 817px; top: 5px;">
	<fieldset style="width: 113px; border: 2px groove; height: 80px;">
	<legend><fmt:message key="movementsearch.debit/credit" /> </legend>
	<table width="80" border="0" cellpadding="0" cellspacing="1" height="30">
			<tr height="10">
				<td>
					<input type="radio"
						   tabindex="33"
						   id="40"
						   name="movementsearch.sign"
						   value="D"
						   ${movementsearch.sign == 'D' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movSign" />
					<label title='<fmt:message key="tooltip.movSign"/>' for="40">
						<fmt:message key="movementsearch.debit" />
					</label>
				</td>
			</tr>
			<tr height="10">
				<td>
					<input type="radio"
						   tabindex="33"
						   id="41"
						   name="movementsearch.sign"
						   value="C"
						   ${movementsearch.sign == 'C' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movSign" />
					<label title='<fmt:message key="tooltip.movSign"/>' for="41">
						<fmt:message key="movementsearch.credit" />
					</label>
				</td>
			</tr>
			<tr height="10">
				<td>
					<input type="radio"
						   tabindex="33"
						   id="42"
						   name="movementsearch.sign"
						   value="B"
						   ${movementsearch.sign == 'B' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movSign" />
					<label title='<fmt:message key="tooltip.movSign"/>' for="42">
						<fmt:message key="movementsearch.both" />
					</label>
				</td>
			</tr>
		</table>

	</fieldset>
	</div>
	<!----------------------end of debit/credit fieldset------------------->

	<!-----------------------Predict status fieldset---------------------->

	<div style="position: absolute; left: 817px; top: 225px;">
	<fieldset style="width: 113px; border: 2px groove; height: 107px;">

	<legend><fmt:message key="movementsearch.predictstatus" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0" height="88">
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="35"
						   id="46"
						   name="movementsearch.predictStatus"
						   value="I"
						   ${movementsearch.predictStatus == 'I' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movPredict" />
					<label title='<fmt:message key="tooltip.movPredict"/>' for="46">
						<fmt:message key="movementsearch.included" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="35"
						   id="47"
						   name="movementsearch.predictStatus"
						   value="E"
						   ${movementsearch.predictStatus == 'E' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movPredict" />
					<label title='<fmt:message key="tooltip.movPredict"/>' for="47">
						<fmt:message key="movementsearch.excluded" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="35"
						   id="48"
						   name="movementsearch.predictStatus"
						   value="C"
						   ${movementsearch.predictStatus == 'C' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movPredict" />
					<label title='<fmt:message key="tooltip.movPredict"/>' for="48">
						<fmt:message key="movementsearch.cancelled" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="35"
						   id="49"
						   name="movementsearch.predictStatus"
						   value="A"
						   ${movementsearch.predictStatus == 'A' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movPredict" />
					<label title='<fmt:message key="tooltip.movPredict"/>' for="49">
						<fmt:message key="movementsearch.all" />
					</label>
				</td>
			</tr>
		</table>

	</fieldset>
	</div>
	<!------------------------end of predict status fieldset--------------->

	<!-----------------------Start External Balance status fieldset---------------------->

	<div style="position: absolute; left: 817px; top: 355px;">
	<fieldset style="width: 113px; border: 2px groove; height: 92px;">

	<legend><fmt:message key="label.movementsearch.externalBalanceStatus" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0" height="66">
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="36"
						   id="50"
						   name="movementsearch.extBalStatus"
						   value="I"
						   ${movementsearch.extBalStatus == 'I' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movExternalBalanceStatus" />
					<label title='<fmt:message key="tooltip.movExternalBalanceStatus"/>' for="50">
						<fmt:message key="movementsearch.included" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="36"
						   id="51"
						   name="movementsearch.extBalStatus"
						   value="E"
						   ${movementsearch.extBalStatus == 'E' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movExternalBalanceStatus" />
					<label title='<fmt:message key="tooltip.movExternalBalanceStatus"/>' for="51">
						<fmt:message key="movementsearch.excluded" />
					</label>
				</td>
			</tr>
			<tr height="22">
				<td>
					<input type="radio"
						   tabindex="36"
						   id="52"
						   name="movementsearch.extBalStatus"
						   value="A"
						   ${movementsearch.extBalStatus == 'A' ? 'checked' : ''}
						   style="width:25px;"
						   titleKey="tooltip.movExternalBalanceStatus" />
					<label title='<fmt:message key="tooltip.movExternalBalanceStatus"/>' for="52">
						<fmt:message key="movementsearch.all" />
					</label>
				</td>
			</tr>
		</table>

	</fieldset>
	</div>
	<!------------------------End External Balance status fieldset--------------->

	<!--------------open movements fieldset------------>
	<div style="position: absolute; left: 817px; top: 470px;">
	<fieldset style="width: 113px; border: 2px groove; height: 65px;">

	<legend><fmt:message key="movementsearch.open" /> </legend>
	<table width="100" border="0" cellpadding="0" cellspacing="0" height="47">
		<tr height="22">
			<td>
				<input type="radio"
					   tabindex="37"
					   id="53"
					   name="movementsearch.openFlag"
					   value="O"
					   ${movementsearch.openFlag == 'O' ? 'checked' : ''}
					   style="width:25px;"
					   titleKey="tooltip.open" />
				<label title='<fmt:message key="tooltip.open"/>' for="53">
					<fmt:message key="movementsearch.open" />
				</label>
			</td>
		</tr>
		<tr height="22">
			<td>
				<input type="radio"
					   tabindex="37"
					   id="54"
					   name="movementsearch.openFlag"
					   value="A"
					   ${movementsearch.openFlag == 'A' ? 'checked' : ''}
					   style="width:25px;"
					   titleKey="tooltip.open" />
				<label title='<fmt:message key="tooltip.open"/>' for="54">
					<fmt:message key="movementsearch.all" />
				</label>
			</td>
		</tr>
	</table>

	</fieldset>
	</div>
	<!--------------End open movements fieldset ------------> <!----------------------second fieldset--------------------------------------------------->
	<div style="position: absolute; left: 8px; top: 6px;">
	<fieldset style="width: 125px; border: 2px groove; height: 143px;">
	<legend><fmt:message key="movementsearch.status" /> </legend>
	<table width="170" border="0" cellpadding="0" cellspacing="2"
		height="154">
		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="3" id="3" name="movementsearch.matchStatus" value="A"
						   ${movementsearch.matchStatus == 'A' ? 'checked' : ''}
						   titleKey="tooltip.movStatus" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="3" id="3" name="movementsearch.matchStatus" value="A"
						   ${movementsearch.matchStatus == 'A' ? 'checked' : ''}
						   disabled="true" titleKey="tooltip.movStatus" />
				</c:if>
				<label title='<fmt:message key="tooltip.movStatus"/>' for="3">
					<fmt:message key="movementsearch.status.authorise" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="3" id="5" name="movementsearch.matchStatus" value="R"
						   ${movementsearch.matchStatus == 'R' ? 'checked' : ''}
						   titleKey="tooltip.movStatus" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="3" id="5" name="movementsearch.matchStatus" value="R"
						   ${movementsearch.matchStatus == 'R' ? 'checked' : ''}
						   disabled="true" titleKey="tooltip.movStatus" />
				</c:if>
				<label title='<fmt:message key="tooltip.movStatus"/>' for="5">
					<fmt:message key="movementsearch.status.referred" />
				</label>
			</td>
		</tr>


		<tr height="22">
				<td>
					<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
						<input type="radio" tabindex="3" id="6" name="movementsearch.matchStatus" value="L"
							   ${movementsearch.matchStatus == 'L' ? 'checked' : ''}
							   titleKey="tooltip.movStatus" />
					</c:if>
					<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
						<input type="radio" tabindex="3" id="6" name="movementsearch.matchStatus" value="L"
							   ${movementsearch.matchStatus == 'L' ? 'checked' : ''}
							   disabled="true" titleKey="tooltip.movStatus" />
					</c:if>
					<label title='<fmt:message key="tooltip.movStatus"/>' for="6">
						<fmt:message key="movementsearch.outstanding" />
					</label>
				</td>
			</tr>

			<tr height="22">
				<td>
					<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
						<input type="radio" tabindex="3" id="7" name="movementsearch.matchStatus" value="M"
							   ${movementsearch.matchStatus == 'M' ? 'checked' : ''}
							   titleKey="tooltip.movStatus" />
					</c:if>
					<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
						<input type="radio" tabindex="3" id="7" name="movementsearch.matchStatus" value="M"
							   ${movementsearch.matchStatus == 'M' ? 'checked' : ''}
							   disabled="true" titleKey="tooltip.movStatus" />
					</c:if>
					<label title='<fmt:message key="tooltip.movStatus"/>' for="7">
						<fmt:message key="movementsearch.offered" />
					</label>
				</td>
			</tr>

			<tr height="22">
				<td>
					<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
						<input type="radio" tabindex="3" id="8" name="movementsearch.matchStatus" value="S"
							   ${movementsearch.matchStatus == 'S' ? 'checked' : ''}
							   titleKey="tooltip.movStatus" />
					</c:if>
					<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
						<input type="radio" tabindex="3" id="8" name="movementsearch.matchStatus" value="S"
							   ${movementsearch.matchStatus == 'S' ? 'checked' : ''}
							   disabled="true" titleKey="tooltip.movStatus" />
					</c:if>
					<label title='<fmt:message key="tooltip.movStatus"/>' for="8">
						<fmt:message key="movementsearch.suspended" />
					</label>
				</td>
			</tr>

			<tr height="22">
				<td>
					<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
						<input type="radio" tabindex="3" id="9" name="movementsearch.matchStatus" value="C"
							   ${movementsearch.matchStatus == 'C' ? 'checked' : ''}
							   titleKey="tooltip.movStatus" />
					</c:if>
					<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
						<input type="radio" tabindex="3" id="9" name="movementsearch.matchStatus" value="C"
							   ${movementsearch.matchStatus == 'C' ? 'checked' : ''}
							   disabled="true" titleKey="tooltip.movStatus" />
					</c:if>
					<label title='<fmt:message key="tooltip.movStatus"/>' for="9">
						<fmt:message key="movementsearch.confirmed" />
					</label>
				</td>
			</tr>

		<!-- Changed Review filed into Reconcilled -->
		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="3" id="10" name="movementsearch.matchStatus" value="E"
						   ${movementsearch.matchStatus == 'E' ? 'checked' : ''}
						   titleKey="tooltip.movStatus" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="3" id="10" name="movementsearch.matchStatus" value="E"
						   ${movementsearch.matchStatus == 'E' ? 'checked' : ''}
						   disabled="true" titleKey="tooltip.movStatus" />
				</c:if>
				<label title='<fmt:message key="movementsearch.status.reconcilled"/>' for="10">
					<fmt:message key="movementsearch.status.reconcilled" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="3" id="11" name="movementsearch.matchStatus" value="D"
						   ${movementsearch.matchStatus == 'D' ? 'checked' : ''}
						   titleKey="tooltip.movStatus" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="3" id="11" name="movementsearch.matchStatus" value="D"
						   ${movementsearch.matchStatus == 'D' ? 'checked' : ''}
						   disabled="true" titleKey="tooltip.movStatus" />
				</c:if>
				<label title='<fmt:message key="tooltip.movStatus"/>' for="11">
					<fmt:message key="movementsearch.allmatched" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="3" id="12" name="movementsearch.matchStatus" value="X"
						   ${movementsearch.matchStatus == 'X' ? 'checked' : ''}
						   titleKey="tooltip.movStatus" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="3" id="12" name="movementsearch.matchStatus" value="X"
						   ${movementsearch.matchStatus == 'X' ? 'checked' : ''}
						   disabled="true" titleKey="tooltip.movStatus" />
				</c:if>
				<label title='<fmt:message key="tooltip.movStatus"/>' for="12">
					<fmt:message key="movementsearch.allstatuses" />
				</label>
			</td>
		</tr>

	</table>
	</fieldset>
	</div>
	<!-------------------------end of second fieldset--------------------------------------->

	<!------------------------- Account Class fieldset added----------------------------------->

	<div style="position: absolute; left: 8px; top: 290px;">
	<fieldset style="width: 125px; border: 2px groove; height: 120px;">
	<legend><fmt:message key="movementsearch.account.fielset" /> </legend>
	<table width="170" border="0" cellpadding="0" cellspacing="2"
		height="88">
		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="13" name="movementsearch.accountClass" value="N"
						   ${movementsearch.accountClass == 'N' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="4" id="13" name="movementsearch.accountClass" value="N"
						   ${movementsearch.accountClass == 'N' ? 'checked' : ''}
						   onclick="checkAccountType('searchAccFromMatch')"
						   titleKey="tooltip.accountclass" />
				</c:if>
				<label title='<fmt:message key="movementsearch.account.nostro"/>' for="13">
					<fmt:message key="movementsearch.account.nostro" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="14" name="movementsearch.accountClass" value="C"
						   ${movementsearch.accountClass == 'C' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="4" id="14" name="movementsearch.accountClass" value="C"
						   ${movementsearch.accountClass == 'C' ? 'checked' : ''}
						   onclick="checkAccountType('searchAccFromMatch')"
						   titleKey="tooltip.accountclass" />
				</c:if>
				<label title='<fmt:message key="movementsearch.account.current"/>' for="14">
					<fmt:message key="movementsearch.account.current" />
				</label>
			</td>
		</tr>

		<tr height="22px">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="15" name="movementsearch.accountClass" value="L"
						   ${movementsearch.accountClass == 'L' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="4" id="15" name="movementsearch.accountClass" value="L"
						   ${movementsearch.accountClass == 'L' ? 'checked' : ''}
						   onclick="checkAccountType('searchAccFromMatch')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<label title='<fmt:message key="movementsearch.account.loro"/>' for="15">
					<fmt:message key="movementsearch.account.loro" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="16" name="movementsearch.accountClass" value="E"
						   ${movementsearch.accountClass == 'E' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<input type="radio" tabindex="4" id="16" name="movementsearch.accountClass" value="E"
						   ${movementsearch.accountClass == 'E' ? 'checked' : ''}
						   onclick="checkAccountType('searchAccFromMatch')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<label title='<fmt:message key="acc.netting"/>' for="16">
					<fmt:message key="acc.netting" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="17" name="movementsearch.accountClass" value="O"
						   ${movementsearch.accountClass == 'O' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<label title='<fmt:message key="tooltip.accountClass"/>' for="17">
					<fmt:message key="movementsearch.account.other" />
				</label>
			</td>
		</tr>

		<tr height="22">
			<td>
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<input type="radio" tabindex="4" id="18" name="movementsearch.accountClass" value="All"
						   ${movementsearch.accountClass == 'All' ? 'checked' : ''}
						   onclick="checkAccountType('archivePopulateAcc')"
						   titleKey="tooltip.accountClass" />
				</c:if>
				<label title='<fmt:message key="tooltip.accountClass"/>' for="18">
					<fmt:message key="qualityTab.all" />
				</label>
			</td>
		</tr>




	</table>
	</fieldset>
	</div>
	<!------------------ End of Account class fieldset--------------------------------------------------------->



		<!------------------ Start of additional search criteria button--------------------------------------------------------->
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; left:8px; top:460px; visibility:visible;">
		<fieldset style="width:175px;border:2px groove;height:140px;">
		<legend><fmt:message key="movementsearch.criteria.fielset"/></legend>
		<table width="60px"  height= "50px" border="0" cellspacing="0" cellpadding="0" height="20">
		   <tr>

		    <td width="30px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="70" id="advacedButton">
				<a title='<fmt:message key="tooltip.criteria"/>' tabindex="53" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="openAddColsScreen('openAddColsScreen');"><fmt:message key="button.search.criteria"/></a>
			</td>

			</tr>
		</table>
		</fieldset>
	   </div>
	   <!------------------ End of additional search criteria button--------------------------------------------------------->

	</div>
	</div>

	<!-----------------------Print button------------------------->
	<div id="MovementSearch"
		style="position: absolute; left: 880; top: 640px; width: 70; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td align="Right"><a tabindex="55" href=#
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildPrintURL('print','Archieve Search '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.help"/>'></a></td>

			<td align="right" id="Print"><a tabindex="55"
				onKeyDown="submitEnter(this,event)" onClick="printPage();"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'</a></td>
		</tr>
	</table>
	</div>

	<!--------------------------end of print button-------------------------->


	<!-----------------Ok and close buttons-------------------------------->

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 631; width: 941px; height: 39px; visibility: visible;">
	<div id="MovementSearchParam"
		style="position: absolute; left: 6; top: 4; width: 810; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td width="70" id="okbutton">
				<c:if test="${requestScope.initialinputscreen != 'matchSearch'}">
					<a title='<fmt:message key="tooltip.executeSearch"/>' tabindex="53"
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)"
						onMouseUp="highlightbutton(this)"
						onKeyDown="submitEnter(this,event)"
						onClick="javascript:getMovementDetails('flex')">
						<fmt:message key="button.search" />
					</a>
				</c:if>
				<c:if test="${requestScope.initialinputscreen == 'matchSearch'}">
					<a title='<fmt:message key="tooltip.executeSearch"/>' tabindex="53"
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)"
						onMouseUp="highlightbutton(this)"
						onClick="javascript:checkDate()">
						<fmt:message key="button.search" />
					</a>
				</c:if>
			</td>

			<td width="70" id="closebutton">
				<a title='<fmt:message key="tooltip.close"/>' tabindex="54"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)"
					onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)"
					onClick="confirmClose('P');">
					<fmt:message key="button.close" />
				</a>
			</td>
		</tr>

	</table>
	</div>
	<div><!-----------------------end buttons---------------------------------->
</form>
</body>
</html>