<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/taglib.jsp"%>

<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<title><fmt:message key="label.entityMonitorOptions.title.window"/></title>
	</head>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<script type="text/javascript">

			// set the label
			var label = new Array ();
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("entityMonitor.do");
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["usepersonalentitylist"] = "<fmt:message key="label.entityMonitorOptions.usePersonalEntityList"/>";
			label["tip"]["usepersonalentitylist"] = "<fmt:message key="tooltip.entityMonitorOptions.usePersonalEntityList"/>";
			label["text"]["hideweekends"] = "<fmt:message key="label.entityMonitorOptions.hideWeekends"/>";
			label["tip"]["hideweekends"] = "<fmt:message key="tooltip.entityMonitorOptions.hideWeekends"/>";
			label["text"]["autosizecolumns"] = "<fmt:message key="label.entityMonitorOptions.autoSizeColumns"/>";
			label["tip"]["autosizecolumns"] = "<fmt:message key="tooltip.entityMonitorOptions.autoSizeColumns"/>";
			label["text"]["reportingccy"] = "<fmt:message key="label.entityMonitorOptions.reportingCcy"/>";
			label["tip"]["reportingccy"] = "<fmt:message key="tooltip.entityMonitorOptions.reportingCcy"/>";
			label["text"]["useccymultiplier"] = "<fmt:message key="label.entityMonitorOptions.useCurrencyMultiplier"/>";
			label["tip"]["useccymultiplier"] = "<fmt:message key="tooltip.entityMonitorOptions.useCurrencyMultiplier"/>";
			label["text"]["defaultdays"] = "<fmt:message key="label.entityMonitorOptions.defaultDays"/>";
			label["tip"]["defaultdays"] = "<fmt:message key="tooltip.entityMonitorOptions.defaultDays"/>";
			label["text"]["usepersonalccylist"] = "<fmt:message key="label.entityMonitorOptions.usePersonalCcyList"/>";
			label["tip"]["usepersonalccylist"] = "<fmt:message key="tooltip.entityMonitorOptions.usePersonalCcyList"/>";
			label["text"]["save"] = "<fmt:message key="label.entityMonitorOptions.save"/>";
			label["tip"]["save"] = "<fmt:message key="tooltip.entityMonitorOptions.save"/>";
			label["text"]["cancel"] = "<fmt:message key="label.entityMonitorOptions.cancel"/>";
			label["tip"]["cancel"] = "<fmt:message key="tooltip.entityMonitorOptions.cancel"/>";
			label["text"]["entity"] = "<fmt:message key="label.entityMonitorOptions.entity"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.entityMonitorOptions.entity"/>";
			label["text"]["ccy"] = "<fmt:message key="label.entityMonitorOptions.currency"/>";
			label["tip"]["ccy"] = "<fmt:message key="tooltip.entityMonitorOptions.currency"/>";
			/*Start: Modified for Mantis 1386:"User defined option to show normal || small fonts" by Marshal on 19-May-2011*/
			label["text"]["text-rate"] = "<fmt:message key="label.entityMonitorOptions.rate"/>";
			label["tip"]["text-rate"] = "<fmt:message key="tooltip.entityMonitorOptions.rate"/>";
			label["text"]["text-font"] = "<fmt:message key="label.entityMonitorOptions.font"/>";
			label["text"]["label-fontnormal"] = "<fmt:message key="label.entityMonitorOptions.fontnormal"/>";
			label["tip"]["label-fontnormal"] = "<fmt:message key="tooltip.entityMonitorOptions.fontnormal"/>";
			label["text"]["label-fontsmall"] = "<fmt:message key="label.entityMonitorOptions.fontsmall"/>";
			label["tip"]["label-fontsmall"] = "<fmt:message key="tooltip.entityMonitorOptions.fontsmall"/>";
			label["text"]["label-ratealert"] = "<fmt:message key="alert.entityMonitorOptions.refreshRate"/>";
			label["text"]["label-notnumber"] = "<fmt:message key="alert.entityMonitorOptions.notANumber"/>";
			label["text"]["label-monitorOptions"] = "<fmt:message key="label.entityMonitorOptions.title.window"/>";
			/*End: Modified for Mantis 1386:"User defined option to show normal || small fonts" by Marshal on 19-May-2011*/
			// If parent refresh, close the window
			<c:if test="${'Y'==requestScope.parentFormRefresh}">

				window.opener.refreshPending = true;
				self.close();

</c:if>

			//variable personal currency
			var personalcurrency = "";
			//variable personal entity
			var personalentity = "";
			// get the application name
			var appName = "<%=SwtUtil.appName%>";

			/*Start: Added for Mantis 1386:"User defined option to show normal && small fonts" by Marshal on 17-05-2011*/
			// get the item id
			var itemId = '${requestScope.itemId}';
			// get the host id
			var hostId = '${requestScope.hostId}';
			// get the user id
			var userId = '${requestScope.userId}';
			// get the font size from request
			var currentFontSize = '${requestScope.fontSize}';
			var screenRoute = "entityMonitorOptions";
			/*End: Added for Mantis 1386:"User defined option to show normal && small fonts" by Marshal on 17-05-2011*/

			// This method is used to send the request
			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();
				var count = new Number (oXHR.responseText);
				return count;
			}
			// This function opens the help screen
			function help(){
				openWindow(buildPrintURL('print','Entity Monitor Options'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

            // This function opens the Personal entity list Window
            function openPersonalEntityWindow () {
				var param = '/' + appName + '/entityMonitor.do?method=personalEntityFlex';
				personalentity = window.open (param, 'PersonalEntityList', 'width=770,height=590,toolbar=0, resizable=yes, scrollbars=no, left=10, top=274', 'true');
			}

			// This function opens the Personal currency list Window
            function openPersonalCurrencyWindow () {
				var param = '/' + appName + '/entityMonitor.do?method=personalCurrencyFlex';
				personalcurrency = window.open (param, 'PersonalCurrencyList', 'width=1155,height=780,toolbar=0, resizable=yes, scrollbars=no, left=10, top=274', 'true');
			}

			// This function used to close the window
            function closeWindow() {
				window.close();
			}

			// This function is called, when unload the window
			function unloadCloseWindow(){
				window.close();
				if (personalcurrency != "")
					personalcurrency.close();
				if (personalentity != ""){
					personalentity.close();
				}
				window.opener.callApp("Y");
			}

			//Used to determine what to request to save a new refresh rate
			function getUpdateRefreshRequest (rate,item_id) {
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + rate;
			}

			/*Start: Added for Mantis 1386: "User defined option to show normal || small fonts" by Marshal on 05-May-2011*/
			/**
  			  * This function is used for calling the saveFontSize() method from ScreenOption
  			  * to store the Font size of the screen data grid
  			  *
  			  * @param fontSize - set by the user.
  			  *
  			  */
			function getUpdateFontSize(fontSize, item_id) {
				return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + fontSize;
			}
			/*End: Added for Mantis 1386: "User defined option to show normal || small fonts" by Marshal on 05-May-2011*/
            			
		</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();" onunload="unloadCloseWindow();">
<form id="exportDataForm" target="tmp" method="post">

<!-- <div id="swf"><object id='mySwf' -->
<!-- 	classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='100%' -->
<!-- 	width='100%'> -->
<%-- 	<param name='src' value='jsp/work/entitymonitoroptions.swf?version=<%= SwtUtil.appVersion %>' /> --%>
<!-- 	<param name='flashVars' value='' /> -->
<%-- 	<embed name='mySwf' src='jsp/work/entitymonitoroptions.swf?version=<%= SwtUtil.appVersion %>' --%>
<!-- 		height='100%' width='100%' flashVars='' /></object></div> -->
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
