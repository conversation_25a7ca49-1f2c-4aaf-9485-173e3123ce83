<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<%@page import="org.swallow.work.model.MovementNote"%>
<%@page import="org.swallow.work.model.SweepNote"%>
<%@page import="org.swallow.work.model.MatchNote"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.method != 'showMatchNotes' && requestScope.method != 'showSweepNotes'}">
        <title><fmt:message key="movNote.title.window"/></title>
    </c:when>
    <c:when test="${requestScope.method != 'showMatchNotes' && requestScope.method == 'showSweepNotes'}">
        <title><fmt:message key="sweepNotes.title.window"/></title>
    </c:when>
    <c:when test="${requestScope.method != 'showSweepNotes' && requestScope.method == 'showMatchNotes'}">
        <title><fmt:message key="matchNotes.title.window"/></title>
    </c:when>
</c:choose>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

//Variable for changeFlag
var changeFlag=false;
var entityCode="${requestScope.entityCode}";
var movId="${requestScope.movId}";
var matchId="${requestScope.matchId}";
var sweepId="${requestScope.sweepId}";
var screenName = '${requestScope.screenName}';
var isNotesPresent = '${requestScope.isNotesPresent}';
var currencyCode = '${requestScope.currencyCode}';
var date = '${requestScope.date}';
var disBtnLock = "<%= request.getParameter("disBtnLock") %>";
var matchLock ="false" ;
var unLock="<%= request.getParameter("unLock") %>";
var archiveId =  '<%=SwtUtil.isEmptyOrNull(request.getParameter("archiveId"))?"":request.getParameter("archiveId")%>';
var currencyAccess='${requestScope.currencyAccess}';
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
mandatoryFieldsArray= "undefined" ;

	var appName = "<%=SwtUtil.appName%>";
	var initialscreen=window.opener.initialscreen;	
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
    requestURL=requestURL.substring(0,idy+1) ;
    var oXMLHTTP = new XMLHttpRequest();
    
 
function releaseLock()
{

	if (unLock=="true" && disBtnLock != "Y")
	{

		beaconUnlock(movId);

	}
	}
// For page unload cases
    function beaconUnlock(movementIds) {
        try {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
   			 const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

            // Create the data to be sent
            const formData = new FormData();
            formData.append('movementIds', movementIds);
            formData.append('method', 'unlockMovements');

            // Send using beacon API
            const success = navigator.sendBeacon(sURL, formData);
            return success ? "success" : "error";
        } catch (error) {
            console.error('Error during beacon unlock:', error);
            return "error";
        }
    }

function selectTableRow(e)
	{
              var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
	while(srcEl.tagName != 'TD')
	{
		srcEl = srcEl.parentElement;
	}
	var rowElement = srcEl.parentElement;
	var tblElement = srcEl.parentElement.parentElement.parentElement;
	var isRowSel = isRowSelected(rowElement);
	resetTableRowsStyle(tblElement);	
	if(isRowSel == false)
		rowElement.className = 'selectrow' ;
	onSelectTableRow(rowElement,!isRowSel);

}
/*
* Start:Code Modified for Mantis 1270 by Chinniah on
* 29-Feb-2012:Data Archive setup: Remove redundant fields from
* Archive setup screen
*/
/** 
function bodyOnLoad

Calls bodyOnLoad on the load event of body tag. 
This function sets the default properties of the screen.

**/ 
function bodyOnLoad()
{
	xl = new XLSheet("notesDetails","table_2", ["String","String", "String", "String"],"2111");
	//Calls the funtion onFilterAndSort when sort and filter event dispatches.
	xl.onsort = xl.onfilter = onFilterAndSort;
	// highlights the selected record
	highlightTableRows("notesDetails");	
	// disables the view and delete 
	disableAllButtons();
	//Sets the button lock value
	document.forms[0].disBtnLock.value = disBtnLock;
	//checks the lock value if Y the disables add and delete buttons
	if(disBtnLock == 'Y')
	{	
		<c:if test="${requestScope.fromArchive != 'yes'}">
			document.getElementById("addbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>';
		</c:if>
	}
	//Gets the lock for matching
	
	//While notes screen is opened for a Movement, then dont check for the lock. Check lock only for notes screen for a match.For match screen, the match id will not be null and the length greater than 0
	if(matchId != null && matchId != "") {	
		matchLock = checkLockForMatch(matchId);
	}
	
	// checks the parent screen
	if(window.opener.calledFrom=="mvmntdisplay" && window.opener.parentScreen=="movementSummaryDisplay")
	   matchLock = "false";
	// Checks the matchlock, if is not false, disables the add and delete buttons  
	if(matchLock != 'false'){
		document.getElementById("addbutton").innerHTML ='<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>';	
	}
	// Checks the recordFound value, if it is N, then displays alert message
		<c:if test="${requestScope.recordFound == 'N'}">
			alert('<fmt:message key="notes.alert.recordNotFound" />');
		</c:if>

		// Checks the archiveId value, if it has any value, disables the add button
		if (archiveId !== "" && archiveId !== "null") {
			<c:if test="${requestScope.fromArchive != 'yes'}">
				document.getElementById("addbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.add" /></a>';
			</c:if>
		}
}

/** 
function disableAllButtons

Disables the view and delete buttons

**/ 
function disableAllButtons()
{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	<c:if test="${requestScope.fromArchive != 'yes'}">
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	</c:if>
}
/*
* End:Code Modified for Mantis 1270 by Chinniah on
* 29-Feb-2012:Data Archive setup: Remove redundant fields from
* Archive setup screen
*/

/** 
function onFilterAndSort

This function calls on sort and filter events. 
This will update the color of the column filter and
Disables the view and delete buttons.

**/ 
function onFilterAndSort()
{
	updateColors();
	disableAllButtons();

}


function checkLockForMatch(matchId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkLockForMatch";
	sURL = sURL + "&matchId="+matchId;
	sURL = sURL + "&entityId="+entityCode;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var lockUserId=oXMLHTTP.responseText;	
	return lockUserId;
}

function onBodyUnload()
{
 var isParentScreenRefresh = document.forms[0].isParentScreenRefresh.value;
 parentScreenRefresh();
 if(isParentScreenRefresh == "Y")	
    releaseLock();
	 
}

function addMatchNotes(methodName){
	var param = 'notes.do?method='+methodName+'&selectedDate=';
	param += document.forms[0].selectedDate.value;
	param += '&entityCode=';
	param += entityCode;
	param += '&matchId=';
	param += matchId;
	param += '&screenName=';
	param += screenName;
	return param;

}

function addSweepNotes(methodName){

	var param = 'notes.do?method='+methodName+'&selectedDate=';
	param += document.forms[0].selectedDate.value;
	param += '&entityCode=';
	param += entityCode;
	param += '&currencyAccess=' + currencyAccess;
	param += '&sweepId=';
	param += sweepId;
	
	return param;

}
function submitForm(methodName){
	changeFlag=true;
	var param = 'notes.do?method='+methodName+'&selectedDate=';
	param += document.forms[0].selectedDate.value;
	param += '&entityCode=';
	param += entityCode;
	param += '&movId=';
	param += movId;
	param += '&screenName=';
	param += screenName;
	param += '&isNotesPresent=';
	param += isNotesPresent;
	param += '&currencyCode=';
	param += currencyCode;
	
	param += '&currencyAccess=' + currencyAccess;
	param += '&date=';
	param += date;
	param += '&archiveId=${archiveId}';
	param += '&unLock=';
	param += unLock;
	param += '&disBtnLock=';
	param += disBtnLock;
	return param;
	
}

function viewMatchNotes(methodName){

	var param = 'notes.do?method='+methodName+'&selectedDate=';
	param += document.forms[0].selectedDate.value;
	param += '&entityCode=';
	param += entityCode;
	param += '&matchId=';
	param += matchId;
	param += '&screenName=';
	param += screenName;
	param+='&archiveId='+archiveId;
	return param;
	
}

function viewSweepNotes(methodName){
	var param = 'notes.do?method='+methodName+'&selectedDate=';
	param += document.forms[0].selectedDate.value;
	param += '&entityCode=';
	param += entityCode;
	param += '&sweepId=';
	param += sweepId;	
	param+='&archiveId='+archiveId;
	return param;	
}



function submitFormDelete(methodName){

	document.forms[0].method.value = methodName;	
	
	document.forms[0].selectedEntityId.value = entityCode ;
	document.forms[0].selectedMovId.value = movId
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){
		changeFlag = true;
		document.forms[0].isParentScreenRefresh.value = "N";	
		document.forms[0].screenName.value = screenName;
		document.forms[0].unLock.value = unLock;
		document.forms[0].disBtnLock.value = disBtnLock;
		document.forms[0].isNotesPresent.value = isNotesPresent;
		document.forms[0].currencyCode.value = currencyCode;
		document.forms[0].date.value = date;
		document.forms[0].submit();
	}
	}
	

function deleteMatchNotes(methodName){
	
	document.forms[0].method.value = methodName;	
	document.forms[0].selectedEntityId.value = entityCode ;
	document.forms[0].selectedMatchId.value = matchId;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){
	document.forms[0].screenName.value = screenName;
	document.forms[0].isParentScreenRefresh.value = "N";	
	document.forms[0].submit();
	}
	}

function deleteSweepNotes(methodName){
	
	document.forms[0].method.value = methodName;	
	document.forms[0].selectedEntityId.value = entityCode ;
	document.forms[0].selectedSweepId.value = sweepId;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){
	document.forms[0].isParentScreenRefresh.value = "N";	
	document.forms[0].submit();
	}
	}

/** 
function onSelectTableRow

This function calls on select the record
This will enables/disables the view and delete buttons.

**/ 
function onSelectTableRow(rowElement, isSelected)
{
	// Gets the date
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedDate.value = hiddenElement.value;
	// Gets the Entity id
	hiddenElement = rowElement.getElementsByTagName("input")[1];
	document.forms[0].selectedEntityId.value = hiddenElement.value;
	// Gets the Notes
	hiddenElement = rowElement.getElementsByTagName("input")[2];
	document.forms[0].selectedNoteText.value = hiddenElement.value;
	//Gets the selected user id 
	document.forms[0].selectedUserId.value = rowElement.cells[2].innerText;
	
	var selectedUserId=rowElement.cells[2].innerText;
	
	selectedUserId = new String(selectedUserId).trim().valueOf();	
	var loggedInUserId = '${currentUserId}';
	loggedInUserId=  new String(loggedInUserId).trim().valueOf();
	/*
	* Start:Code Modified for Mantis 1270 by Chinniah on
	* 29-Feb-2012:Data Archive setup: Remove redundant fields from
	* Archive setup screen
	*/
	// Checks the row selected
	if(isSelected)
	{
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		<c:if test="${requestScope.fromArchive != 'yes'}">
				// Checks the button lock and match lock
				if (disBtnLock === "Y" || matchLock !== "false") {
					document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
				} else if (selectedUserId === loggedInUserId && (archiveId === "" || archiveId === "null")) {
					document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
				} else if (selectedUserId !== loggedInUserId) {
					document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
				}

				if (currencyAccess !== "" && currencyAccess !== "null" && currencyAccess == 1) {
					document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
				}
		</c:if>
	}
	else{

		// disables the view and delete buttons
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	<c:if test="${requestScope.fromArchive != 'yes'}">
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		</c:if>
	}
	/*
	* End:Code Modified for Mantis 1270 by Chinniah on
	* 29-Feb-2012:Data Archive setup: Remove redundant fields from
	* Archive setup screen
	*/	
}

function parentScreenRefresh()
{
 if(screenName == 'searchParams' || screenName == 'outstanding')
 {
	 var parentWindow = window.opener;
	 var notesValue = "";
	if(document.getElementById("notesDetails").tBodies[0].rows.length > 0)
	{
		notesValue = document.getElementById("notesDetails").tBodies[0].rows.length
	}
	if ( parentWindow.refreshMovmentNote != 'undefined')
	{
		parentWindow.refreshMovmentNote(movId,notesValue);
	}
 }

 if(screenName == 'searchParams' && changeFlag == false)
	window.opener.CallBackApp();
 
if(screenName == 'manualInputScreenViewMode')
{
	window.opener.document.forms[0].notesScreenOpened.value = "Y";
	if(document.getElementById("notesDetails").tBodies[0].rows.length > 0)
	{
	
	window.opener.document.forms[0].noOfNotes.value = document.getElementById("notesDetails").tBodies[0].rows.length;
	window.opener.document.forms[0].isNotesPresent.value = "Y";
	}else
	{
	window.opener.document.forms[0].isNotesPresent.value = "N";
	}
	 var parentWindow = window.opener;
	if ( parentWindow.refreshMovmentNoteImage != 'undefined')
	{
		parentWindow.refreshMovmentNoteImage();
	}

}

if(screenName == 'matchScreen')
{
	
	if(document.getElementById("notesDetails").tBodies[0].rows.length > 0)
	{
	window.opener.callApp("notes","","Y");
	
	
	}else
	{
		
	window.opener.callApp("notes","","N");
	}

}
}
</SCRIPT>
</head>
<form action="notes.do" method="post">
<input name="method" type="hidden" value="display">  
<input name="selectedUserId" type="hidden"> 
<input name="selectedDate" type="hidden" value="" > 
<input name="selectedNoteText" type="hidden" value="GBP" > 
<input name="selectedEntityId" type="hidden" value="" >
<input name="selectedMovId" type="hidden" >
<input name="movId" type="hidden" >
<input name="selectedMatchId" type="hidden" value="">
<input name="selectedSweepId" type="hidden" value="" >
<input name="entityCode" type="hidden" >
<input name="screenName" type="hidden" value="">
<input name="unLock" type="hidden" value="">
<input name="disBtnLock" type="hidden" value="">
<input name="isNotesPresent" type="hidden" value="">
<input name="currencyCode" type="hidden" value="">
<input name="date" type="hidden" value="">
<input name="isParentScreenRefresh" type="hidden" value="Y">
<input name="recordFound" type="hidden" value="">

<input name="currencyAccess" type="hidden" value="">

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="onBodyUnload();call();" >

<div color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:528px; height:187;">

<div class="small-filter" id="Notes" style="position:absolute;z-index:99; left:0px; top:0px; width:515px; height:10px;">

<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="500" border="0" cellspacing="1" cellpadding="0" height="20">
	<thead>
		<tr height="20px">
			<td align="center"  title='<fmt:message key="tooltip.sortByDate"/>' width="80px"><b><fmt:message key="notes.date"/></b></td>
			<td  align="center"  title='<fmt:message key="tooltip.sortByTime"/>' width="80px"><b><fmt:message key="notes.time"/></b></td>
			<td  align="center" title='<fmt:message key="tooltip.user"/>' width="144px"><b><fmt:message key="notes.user"/></b></td>
			<td  align="center" title='<fmt:message key="tooltip.sortByNote"/>' width="200px"><b><fmt:message key="notes.note"/></b></td>
		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:525px; height:182;overflow-x:hidden ">
<div id="Notes" style="position:absolute;left:0px; top:20px; width:508px; height:10px;">
<table class="sort-table" id="notesDetails" width="508" border="0" cellspacing="1" cellpadding="0" height="160">
   <tbody>
  <c:forEach items="${requestScope.notesDetails}" var="notesDetails" varStatus="status">
    <tr class="${status.count % 2 == 0 ? 'even' : 'odd'}">
      <input type="hidden" name="id.updateDateIso" value="${notesDetails.id.updateDateIso}" />
      <input type="hidden" name="id.entityId" value="${notesDetails.id.entityId}" />
      <input type="hidden" name="noteText" value="${notesDetails.noteText}" />

      <td width="80px">
        <input type="hidden" name="id.updateDateAsString" value="${notesDetails.id.updateDateAsString}" />
        ${notesDetails.id.updateDateAsString}&nbsp;
      </td>

      <td width="80px">
        <input type="hidden" name="id.updateTimeAsString" value="${notesDetails.id.updateTimeAsString}" />
        ${notesDetails.id.updateTimeAsString}&nbsp;
      </td>

      <td width="144px">
        <input type="hidden" name="updateUser" value="${notesDetails.updateUser}" />
        ${notesDetails.updateUser}&nbsp;
      </td>

      <td width="200px" style="text-overflow: ellipsis; overflow: hidden">
        <input type="hidden" name="compressedNoteText" value="${notesDetails.noteText}" />
        <nobr>${empty notesDetails.noteText ? '' : notesDetails.noteText.replace(' ', '&nbsp;')}&nbsp;</nobr>
      </td>
    </tr>
  </c:forEach>
</tbody>
    <tfoot>
        <tr>
            <td colspan="4"></td>
        </tr>
    </tfoot>
</table>

</div>
</div>
</div>
<div id="Notes" style="position:absolute ;left:475; top:220px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		     <td align="Right">
			   <a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Notes Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key = "tooltip.helpScreen"/>'></a> 
		  </td>

			<td align="right" id="Print">
				<a tabindex="5" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:212; width:528; height:39px; visibility:visible;">
<c:if test="${requestScope.method == 'showMatchNotes'}">
  <div id="notes" style="position:absolute; left:6; top:4; width:524; height:15px; visibility:visible;">
   <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70px">
			<a tabindex="1" title='<fmt:message key="tooltip.AddNotes"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(addMatchNotes('addMatchNotes'),'notesaddWindow','left=50,top=190,width=540,height=198,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
	</td>
		
		<td id="viewbutton"  width="70px"></td>
		<td id="deletebutton"  width="70px" ></td>
		<td id="closebutton"  width="70px">		
			<a  title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="releaseLock();confirmClose('P');"><fmt:message key="button.close"/></a>				
		</td>
	</tr>
	</table>
</div>
	<div style="position:absolute; left:6; top:4; width:524px; height:15px; visibility:hidden;">  	
	<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		
		
		<td id="viewenablebutton">		
			<a tabindex="2" title='<fmt:message key="tooltip.ViewSelectedNote"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(viewMatchNotes('viewMatchNotes'),'notesaddWindow','left=50,top=190,width=540,height=258,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
		</td>		
		<td id="viewdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>
		<td id="deleteenablebutton">		
			<a  title='<fmt:message key="tooltip.DeleteSeletedNote"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:deleteMatchNotes('deleteMatchNotes')"><fmt:message key="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>							
	</tr>
	</table>
  </div>
 </c:if>
	<c:if test="${requestScope.method != 'showMatchNotes'}">
		<c:if test="${requestScope.method == 'showSweepNotes'}">

<div id="notes" style="position:absolute; left:6; top:4; width:524; height:15px; visibility:visible;">
   <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton">
		
			<a  title='<fmt:message key="tooltip.AddNotes"/>' onMouseOut="collapsebutton(this)" tabindex="1" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(addSweepNotes('addSweepNotes'),'notesaddWindow','left=50,top=190,width=540,height=198,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
	</td>
		
		<td id="viewbutton"></td>
		<td id="deletebutton"></td>
		<td id="closebutton">		
			<a  title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>	
		</td>
	</tr>
	</table>
</div>
	<div style="position:absolute; left:6; top:4; width:524px; height:15px; visibility:hidden;">  	
	<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		
		
		<td id="viewenablebutton">		
			<a title='<fmt:message key="tooltip.ViewSelectedNote"/>' onMouseOut="collapsebutton(this)" tabindex="2" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(viewSweepNotes('viewSweepNotes'),'notesviewWindow','left=50,top=190,width=540,height=258,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
		</td>		
		<td id="viewdisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
		</td>
		<td id="deleteenablebutton">		
			<a title='<fmt:message key="tooltip.DeleteSeletedNote"/>' onMouseOut="collapsebutton(this)" tabindex="3" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:deleteSweepNotes('deleteSweepNotes')"><fmt:message key="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>							
	</tr>
	</table>
  </div>
 </c:if>
<c:if test="${requestScope.method != 'showSweepNotes'}">
    <div id="notes" style="position:absolute; left:6; top:4; width:524; height:15px; visibility:visible;">
        <c:choose>
            <c:when test="${requestScope.fromArchive != 'yes'}">
                <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
            </c:when>
            <c:otherwise>
                <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
            </c:otherwise>
        </c:choose>
        <tr>
            <c:if test="${requestScope.fromArchive != 'yes'}">
                <td id="addbutton">
                    <a title='<fmt:message key="tooltip.AddNotes"/>' onMouseOut="collapsebutton(this)" tabindex="1" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(submitForm('add'),'notesaddWindow','left=50,top=190,width=535,height=198,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
                </td>
                <td id="viewbutton"></td>
                <td id="deletebutton"></td>
                <td id="closebutton">
                    <a title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="releaseLock();confirmClose('P');"><fmt:message key="button.close" /></a>
                </td>
            </c:if>
            <c:if test="${requestScope.fromArchive == 'yes'}">
                <td id="viewbutton"></td>
                <td id="closebutton">
                    <a title='<fmt:message key="tooltip.close"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="releaseLock();confirmClose('P');"><fmt:message key="button.close" /></a>
                </td>
            </c:if>
        </tr>
        </table>
    </div>
    <div style="position:absolute; left:6; top:4; width:524px; height:15px; visibility:hidden;">
        <table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
            <tr>
                <td id="viewenablebutton">
                    <a title='<fmt:message key="tooltip.ViewSelectedNote"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(submitForm('view'),'notesaddWindow','left=50,top=190,width=540,height=258,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
                </td>
                <td id="viewdisablebutton">
                    <a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
                </td>
                <c:if test="${requestScope.fromArchive != 'yes'}">
                    <td id="deleteenablebutton">
                        <a title='<fmt:message key="tooltip.DeleteSeletedNote"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitFormDelete('delete')"><fmt:message key="button.delete"/></a>
                    </td>
                    <td id="deletedisablebutton">
                        <a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
                    </td>
                </c:if>
            </tr>
        </table>
    </div>
</c:if>
</c:if>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

<script type="text/javascript">
</script>
</form>
</body>
</html>
