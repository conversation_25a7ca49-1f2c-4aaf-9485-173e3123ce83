<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>

<html>
<head>
<title><fmt:message key="sweepSearch.title.window" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "closebutton";

var initialscreen = "${requestScope.initialinputscreen}";
var currencyFormat = "${sessionScope.CDM.currencyFormat}";
var dateFormat = "${sessionScope.CDM.dateFormat}";
mandatoryFieldsArray = ["movementsearchValuefrom"] ; 
var flag=0;

/**
	 * submitForm
	 *
	 * This method is used to submit the form while perform button operation on full input/movement display screen.
	 **/
function submitForm(methodName){
	<!-- start:code modified by <PERSON><PERSON> for mantis 1597 on ********--!>
	document.forms[0].elements['preAmountover'].value = document.forms[0].elements['sweepsearch.amountoverAsString'].value;
	document.forms[0].elements['preAmountunder'].value = document.forms[0].elements['sweepsearch.amountunderAsString'].value;
	document.forms[0].elements["preSystemFromDateAsString"].value = document.forms[0].elements["sweepsearch.valueFromDateAsString"].value;
	document.forms[0].status.value=getRadioButtonValue(document.forms[0].elements["sweepsearch.sweepStatus"]);
	document.forms[0].parentScr.value='<%=request.getAttribute("parentScreen")%>';
	document.forms[0].acctType.value=getRadioButtonValue(document.forms[0].elements["sweepsearch.accType"]);
	setStoredParam('acctType',document.forms[0].acctType.value);
	document.forms[0].method.value = methodName;
	
	 if(document.forms[0].elements['sweepsearch.amountoverAsString'].value !=""){
		    
	    	var amount = validateCurrency(document.forms[0].elements['sweepsearch.amountoverAsString'], 'movement.amountAsString', currencyFormat,document.forms[0].elements['sweepsearch.currencyCode'].value);
			
			document.forms[0].elements['preAmountover'].value = document.forms[0].elements['sweepsearch.amountoverAsString'].value;
	   }
	   if(document.forms[0].elements['sweepsearch.amountunderAsString'].value !=""){
			var amount = validateCurrency(document.forms[0].elements['sweepsearch.amountunderAsString'], 'movement.amountAsString', currencyFormat,document.forms[0].elements['sweepsearch.currencyCode'].value);
			
			document.forms[0].elements['preAmountunder'].value = document.forms[0].elements['sweepsearch.amountunderAsString'].value;
			
	    }
      <!-- end:code modified by N.Rao for mantis 1597 on ********--!>
	if (flag==0)
	{
		document.forms[0].selectedCurrencyGroup.value = document.forms[0].elements['sweepsearch.currencyGroup'].value;
	}
	else
	{
	     document.forms[0].selectedCurrencyGroup.value = "All";
	}

	document.forms[0].submit();
}

function getAmtValue(amt){
var amtvalue='';
for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);	
	if(el!=',' && el!='.'){	
	 amtvalue += el;
	}	 
 }
 return amtvalue; 
}

function validateAmount(amtover,amtunder){

  if(amtover !="" && amtunder !="")
    {
	var amountOver = new Number(getAmtValue(amtover)) ;
	var amountUnder = new Number(getAmtValue(amtunder));	
	if(amountUnder >= amountOver){
		return true;
	} 
	else{
		return false;
	}

   }
   else{
		return true;
   }

}
function getRadioButtonValue(button){
	var d = button;
	var len = d.length ;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		 return d[i].value;
	}
	if(i == len)
	 return "null";
}

var dateSelected= false;

var toDateSelected= false;

function storeFromDate(){
document.forms[0].elements['preSystemFromDateAsString'].value = document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
}


function onFromDateChange(){
	
	if(dateSelected){
		var preFromDate = document.forms[0].elements['preSystemFromDateAsString'].value;
		var from_date=document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
		var to_date=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
		var dateFormatValue = "${sessionScope.CDM.dateFormatValue}";
		
		 if(to_date !=""){

			if(validateDateField(document.forms[0].elements['sweepsearch.valueFromDateAsString'],'fromDateAsString',dateFormat)){
				if(document.forms[0].elements['sweepsearch.valueToDateAsString'].value != ""){
					if (validateDateField(document.forms[0].elements['sweepsearch.valueToDateAsString'],'toDateAsString',dateFormat)){
						var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
						if(compare_date == 1)
							{
								document.forms[0].elements['sweepsearch.valueFromDateAsString'].value=preFromDate;
								cal.hideCalendar()

					           alert("From Date should be lesser than or equal to To Date");
				
								

							}else{					
								document.forms[0].elements['sweepsearch.valueFromDateAsString'].value =document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
							}
						}
					}

				}
			}
		}
	}


function storeToDate(){
document.forms[0].elements['preSystemToDateAsString'].value = document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
}

function onToDateChange(){

	if(toDateSelected){
		var preToDate = document.forms[0].elements['preSystemToDateAsString'].value;
		var from_date=document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
		var to_date=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
		var dateFormatValue = "${sessionScope.CDM.dateFormatValue}";
		
		 if(to_date !=""){

			if(validateDateField(document.forms[0].elements['sweepsearch.valueFromDateAsString'],'fromDateAsString',dateFormat)){
					if(document.forms[0].elements['sweepsearch.valueToDateAsString'].value != ""){
						if (validateDateField(document.forms[0].elements['sweepsearch.valueToDateAsString'],'toDateAsString',dateFormat)){
							var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
							if(compare_date == 1)
								{
									document.forms[0].elements['sweepsearch.valueToDateAsString'].value=preToDate;
									cal2.hideCalendar()
									alert("To Date should be greater than or equal to From Date");
								}else{					
									document.forms[0].elements['sweepsearch.valueToDateAsString'].value=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;			 
									
								}
						}
					}
				}
			
		}	
	}
}
/**
	* openSweepWindow
	*
	* This method is used to open the SweepWindow  screen when click on ok button.
	* @ param mathodname
	* @ param windowName
	* @ param attr
	* @ param isCascade
	**/
function openSweepWindow(methodName,windowName,attr,isCascade)
{
 	var url = "";

	var todate = "";
	var fromdate = "";
	<!-- Strat: code modified by nageswararao on ******** for mantis 1597--> 
	if(validateCurrency(document.forms[0].elements['sweepsearch.amountoverAsString'],'sweep.amountover',currencyFormat,document.forms[0].elements['sweepsearch.currencyCode'].value))
	{
	     if(validateCurrency(document.forms[0].elements['sweepsearch.amountunderAsString'],'sweep.amountover',currencyFormat,document.forms[0].elements['sweepsearch.currencyCode'].value))
		 
		 {

			var amountover = document.forms[0].elements['sweepsearch.amountoverAsString'].value ;
			var amountunder = document.forms[0].elements['sweepsearch.amountunderAsString'].value;
			<!-- End: code modified by nageswararao on 27-Feb-2012 for mantis 1597--> 
			if(validateAmount(amountover,amountunder)){
				if(validateDateField(document.forms[0].elements['sweepsearch.valueFromDateAsString'],'fromDateAsString',dateFormat)){
					fromdate = document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
					
					if(document.forms[0].elements['sweepsearch.valueToDateAsString'].value != ""){
						if (validateDateField(document.forms[0].elements['sweepsearch.valueToDateAsString'],'toDateAsString',dateFormat)){
							todate = document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
							
							if(comparedates(document.forms[0].elements['sweepsearch.valueFromDateAsString'].value,document.forms[0].elements['sweepsearch.valueToDateAsString'].value,dateFormat,'From Date','To Date'))
							{

								url = buildSweep(methodName);
								openWindow(url,windowName,attr,isCascade);
							}
						}
					}else if(document.forms[0].elements['sweepsearch.valueToDateAsString'].value == ""){
						if(comparedates(fromdate,todate,dateFormat,'From Date','To Date'))
							{
								url = buildSweep(methodName);
								openWindow(url,windowName,attr,isCascade);
							}
					}
				}
					
			
			}else{
				alert('<fmt:message key = "sweepSearch.alert.amount"/>') ;
				//code modified by nageswararao on ******** for mantis 1597
				document.forms[0].elements['sweepsearch.amountunderAsString'].focus();
			}

		}else{

			document.forms[0].elements['sweepsearch.amountunderAsString'].focus();
		}

	}else{
	//code modified by nageswararao on ******** for mantis 1597
		document.forms[0].elements['sweepsearch.amountoverAsString'].focus();
	}
}
/**
	* buildSweep
	*
	* This method is used to open the  Sweepearch screen when click on ok button.
	**/
function buildSweep(methodName){

<c:if test="${requestScope.parentScreen == 'queue'}">
    document.forms[0].elements["sweepsearch.accType"][0].disabled = "";
    document.forms[0].elements["sweepsearch.accType"][1].disabled = "";
    document.forms[0].elements["sweepsearch.accType"][2].disabled = "";

    document.forms[0].elements["sweepsearch.sweepStatus"][0].disabled = "";
    document.forms[0].elements["sweepsearch.sweepStatus"][1].disabled = "";
    document.forms[0].elements["sweepsearch.sweepStatus"][2].disabled = "";
    document.forms[0].elements["sweepsearch.sweepStatus"][3].disabled = "";
    document.forms[0].elements["sweepsearch.sweepStatus"][4].disabled = "";

    <c:if test="${requestScope.disableEntity == 'yes'}">
        document.forms[0].elements["sweepsearch.entityId"].disabled = "";
    </c:if>

    <c:if test="${requestScope.disableCcy == 'yes'}">
        document.forms[0].elements["sweepsearch.currencyCode"].disabled = "";
        document.forms[0].elements["sweepsearch.currencyGroup"].disabled = "";
    </c:if>
</c:if>


 <!--start:code modified by nageswararao on ******** for mantis 1597-->
	var amountover = document.forms[0].elements['sweepsearch.amountoverAsString'].value ;
	var amountunder = document.forms[0].elements['sweepsearch.amountunderAsString'].value;
	<!--End:code modified by nageswararao on ******** for mantis 1597-->
	var currencyCode = document.forms[0].elements['sweepsearch.currencyCode'].value;
	var currencyGroup = document.forms[0].elements['sweepsearch.currencyGroup'].value;

	var accountId = document.forms[0].elements["sweepsearch.accountId"].value;
	var bookCode = document.forms[0].elements["sweepsearch.book"].value; 
	var valueFromDateAsString = document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
	var valueToDateAsString = document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
	 
	if(document.forms[0].elements["sweepsearch.postcutoff"].checked == true){
	var postcutoff ="Y";
	}else{
	var postcutoff ="N";
	}
	
	var generatedby = document.forms[0].elements["sweepsearch.inputUser"].value;
	
	var submittedby = document.forms[0].elements["sweepsearch.submitUser"].value;
	var authorisedby = document.forms[0].elements["sweepsearch.authorizedUser"].value;
 

	var accounttype  = getRadioButtonValue(document.forms[0].elements["sweepsearch.accType"]);
	var sweeptype = getRadioButtonValue(document.forms[0].elements["sweepsearch.sweepType"]);
	var status = getRadioButtonValue(document.forms[0].elements["sweepsearch.sweepStatus"]);
	var msgFormat = document.forms[0].elements['sweepsearch.msgFormat'].value

	var param = 'sweepsearch.do?method='+methodName;
	param += '&entityId=' + document.forms[0].elements['sweepsearch.entityId'].value;
	 
	param += '&amountover=' + amountover ;
	param += '&amountunder=' + amountunder;
	param += '&currencyCode=' + currencyCode;
	param += '&currencyGroup=' + currencyGroup;

	param += '&accountId=' + accountId ;
	 
	param += '&bookCode=' + bookCode ;
	 
	param += '&valueFromDateAsString=' + valueFromDateAsString ;
	param += '&valueToDateAsString=' + valueToDateAsString;

	
	param += '&generatedby=' + generatedby ;
	param += '&postcutoff=' + postcutoff;
	param += '&submittedby=' + submittedby  ;
	 
	param += '&authorisedby=' + authorisedby ;

	 
	param += '&accounttype=' + accounttype ;
	 
	param += '&sweeptype=' + sweeptype ;
	param += '&status=' + status;
	param += '&msgFormat=' + msgFormat;
	
	<c:if test="${requestScope.parentScreen == 'queue'}">
		document.forms[0].elements["sweepsearch.accType"][0].disabled = true;
		document.forms[0].elements["sweepsearch.accType"][1].disabled = true;
		document.forms[0].elements["sweepsearch.accType"][2].disabled = true;

		document.forms[0].elements["sweepsearch.sweepStatus"][0].disabled = true;
		document.forms[0].elements["sweepsearch.sweepStatus"][1].disabled = true;
		document.forms[0].elements["sweepsearch.sweepStatus"][2].disabled = true;
		document.forms[0].elements["sweepsearch.sweepStatus"][3].disabled = true;
		document.forms[0].elements["sweepsearch.sweepStatus"][4].disabled = true;

		<c:if test="${requestScope.disableEntity == 'yes'}">
			document.forms[0].elements["sweepsearch.entityId"].disabled = true;
		</c:if>

		<c:if test="${requestScope.disableCcy == 'yes'}">
			document.forms[0].elements["sweepsearch.currencyCode"].disabled = true;
			document.forms[0].elements["sweepsearch.currencyGroup"].disabled = true;
		</c:if>
	</c:if>

	return  param;
}

function validateFormDate(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["sweepsearch.valueFromDateAsString"];
  elementsRef[1] = objForm.elements["sweepsearch.valueToDateAsString"];

     if((compareDates(document.forms[0].elements["sweepsearch.valueFromDateAsString"].value,  document.forms[0].elements["sweepsearch.valueToDateAsString"].value))==0)
     {
         return true;
     }else
	{
      alert('<fmt:message key = "sweepSearch.alert.datecomparison"/>');
	  return false;
	}
}	

function populateDropBoxes(){
	ShowErrMsgWindow('${actionError}');
	bodyOnLoad1();
}

function bodyOnLoad1(){

	var preSystemFromDateAsString = "${requestScope.preSystemFromDateAsString}";
	var preAmountover = "${requestScope.preAmountover}";
	var preAmountunder = "${requestScope.preAmountunder}";
	var parentScr='<%=request.getAttribute("parentScreen")%>';
	var acctType='<%=request.getParameter("actType")%>';
	var entityDisable='<%=request.getAttribute("disableEntity")%>';
	var statusScr='<%=request.getParameter("status")%>';
	if(parentScr!=null)
	setStoredParam('parentScreen',parentScr);
	if(entityDisable!=null)
	setStoredParam('disableEntity',entityDisable);
	if(statusScr!=null){
	setStoredParam('status',statusScr);
	}
	if (preSystemFromDateAsString != ""){
		document.forms[0].elements["sweepsearch.valueFromDateAsString"].value = preSystemFromDateAsString;
	}
	<!-- start:code modified by nageswararao on ******** for mantis 1597-->
	if (preAmountover != ""){
	 document.forms[0].elements['sweepsearch.amountoverAsString'].value = preAmountover;
	}
	if (preAmountunder != "")
	{
	
		document.forms[0].elements['sweepsearch.amountunderAsString'].value = preAmountunder;
	}
	<!-- End:code modified by nageswararao on ******** for mantis 1597-->
	
	if(parentScr!=getStoredParam('parentScreen'))
	 {
    document.forms[0].elements["sweepsearch.sweepStatus"][4].checked = true;
	 document.forms[0].elements["sweepsearch.accType"][2].checked = true;
	 }
    if("sweepauthorisequeue"==getStoredParam('parentScreen') ||"sweepsubmitqueue"==getStoredParam('parentScreen')){
      if(getStoredParam('status')== "S"||getStoredParam('status') =="N")
	 {
	  document.forms[0].elements["sweepsearch.sweepStatus"][1].checked = true;
	 }
     if(getStoredParam('status')== "U")
	 {
	  document.forms[0].elements["sweepsearch.sweepStatus"][2].checked = true;
	 }
	 if(getStoredParam('acctType')=="A"){
     document.forms[0].elements["sweepsearch.accType"][2].checked = true;
     }
     else if(getStoredParam('acctType')=="C"){
     document.forms[0].elements["sweepsearch.accType"][0].checked = true;
     }
     else if(getStoredParam('acctType')=="U"){
     document.forms[0].elements["sweepsearch.accType"][1].checked = true;
     }
     document.forms[0].elements["sweepsearch.sweepType"][2].checked = true;
	 document.forms[0].elements["sweepsearch.accType"][0].disabled = true;
	 document.forms[0].elements["sweepsearch.accType"][1].disabled = true;
	 document.forms[0].elements["sweepsearch.accType"][2].disabled= true;

	  document.forms[0].elements["sweepsearch.sweepStatus"][0].disabled = true;
	  document.forms[0].elements["sweepsearch.sweepStatus"][1].disabled = true;
	  document.forms[0].elements["sweepsearch.sweepStatus"][2].disabled = true;
	  document.forms[0].elements["sweepsearch.sweepStatus"][3].disabled = true;
	  document.forms[0].elements["sweepsearch.sweepStatus"][4].disabled = true;
		  document.forms[0].elements["sweepsearch.entityId"].disabled = true;
   }
   else{
	     document.forms[0].elements["sweepsearch.sweepStatus"][4].checked = true;
	     document.forms[0].elements["sweepsearch.accType"][2].checked = true;
		 document.forms[0].elements["sweepsearch.sweepType"][2].checked = true;
   }
		<c:if test="${requestScope.disableCcy == 'yes'}">
			  document.forms[0].elements["sweepsearch.currencyCode"].disabled = true;
				document.forms[0].elements["sweepsearch.currencyGroup"].disabled = true;
		</c:if>

  
	var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["sweepsearch.entityId"],document.getElementById("entityName"));
	var currencyDropBoxElement = new SwSelectBox(document.forms[0].elements["sweepsearch.currencyCode"],document.getElementById("currencyName"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["sweepsearch.currencyGroup"],document.getElementById("currencyDesc"));

	var divElement = document.getElementById("dropdowndiv_1");
	var selectElement = document.forms[0].elements["sweepsearch.accountId"];
	var idElement = document.forms[0].elements["accountId"];
	var descElement = document.forms[0].elements["Acctname"];
	var arrowElement = document.forms[0].elements["dropdownbutton_1"];
	var idLength = 12;
	var descLength = 30;
	var accountDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);

	var messageDropBoxElement = new SwSelectBox(document.forms[0].elements["sweepsearch.msgFormat"],document.getElementById("messageName"));
		


	var divElement = document.getElementById("dropdowndiv_2");
	var selectElement = document.forms[0].elements["sweepsearch.book"];
	var idElement = document.forms[0].elements["bookId"];
	var descElement = document.forms[0].elements["bookName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_2"];
	var idLength = 12;
	var descLength = 30;
	var bookDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	
}


</SCRIPT>

<SCRIPT language="JAVASCRIPT">
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';

	var cal = new CalendarPopup("caldiv",true,"calFrame");
    cal.setCssPrefix("CAL");
	cal.offsetX = 18;
    cal.offsetY = 0;
	
	var cal2 = new CalendarPopup("caldiv",true,"calFrame"); 
    cal2.setCssPrefix("CAL");
    cal2.offsetX = 18;
    cal2.offsetY = 0;


	var cal3 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal3.setCssPrefix("CAL");
    cal3.offsetX = 18;
    cal3.offsetY = 0;
    

function validateToDateField(){

	var preToDate = document.forms[0].elements['preSystemToDateAsString'].value;
	var from_date=document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
	var to_date=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';

	if(document.forms[0].elements['sweepsearch.valueToDateAsString']!=null && document.forms[0].elements['sweepsearch.valueToDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['sweepsearch.valueToDateAsString'],'movementsearch.valueto',dateFormat )){
		var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
		if(compare_date == 1) {
			document.forms[0].elements['sweepsearch.valueToDateAsString'].value=preToDate;
			cal2.hideCalendar()
			alert("To Date should be greater than or equal to From Date");
			}else{					
				document.forms[0].elements['sweepsearch.valueToDateAsString'].value=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;			 
			}
		}
	}	
}

function validateFromDateField(){

	var preFromDate = document.forms[0].elements['preSystemFromDateAsString'].value;
	var from_date=document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
	var to_date=document.forms[0].elements['sweepsearch.valueToDateAsString'].value;
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';

	if(document.forms[0].elements['sweepsearch.valueFromDateAsString']!=null && document.forms[0].elements['sweepsearch.valueFromDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['sweepsearch.valueFromDateAsString'],'movementsearch.valuefrom',dateFormat )){
		var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
			if(compare_date == 1) {
				document.forms[0].elements['sweepsearch.valueFromDateAsString'].value=preFromDate;
				cal.hideCalendar()
				
				alert("From Date should be lesser than or equal to To Date");
			
				}else {					
					document.forms[0].elements['sweepsearch.valueFromDateAsString'].value =document.forms[0].elements['sweepsearch.valueFromDateAsString'].value;
				}
		}
	}	
}

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["sweepsearch.valueFromDateAsString"];

  return validate(elementsRef);
}

</SCRIPT>

</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]); populateDropBoxes();"
	onunload="call()">

<form action="sweepsearch.do" method="post">
	<input name="method" type="hidden" value="display">

	<input name="selectedCurrencyGroup" type="hidden" value="">

	<input name="preAmountover" type="hidden" value="">
	<input name="preAmountunder" type="hidden" value="">
        <input name="parentScr" type="hidden" value="">
	<input name="preSystemFromDateAsString" type="hidden" value="">
	<input name="preSystemToDateAsString" type="hidden" value="">
        <input name="status" type="hidden" value="">
        <input name="acctType" type="hidden" value="">
        <input name="archiveId" type="hidden" value="${archiveId}">
	<bean:define id="CDM" name="CDM"
		type="org.swallow.util.CommonDataManager" scope="session" />


	<!----------------------------------caldiv----------------------------------------------------------------->
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>
	
	<!-----------------------------------end of caldiv----------------------------------------------->

	<div id="dropdowndiv_1"
			 style="z-index: 99; position: absolute; width: 200px; left: 322px; top: 251px; visibility: hidden"
			 class="swdropdown">
			<select class="htmlTextFixed" id="sweepsearch.accountId" name="sweepsearch.accountId" size="10" style="width:329px;z-index:99;">
				<c:forEach var="account" items="${requestScope.accountdetails}">
					<option value="${account.value}" ${account.value == sweepsearch.accountId ? 'selected="selected"' : ''}>
						${account.label}
					</option>
				</c:forEach>
			</select>
		</div>

		<div id="dropdowndiv_2"
			 style="z-index: 99; position: absolute; width: 200px; left: 322px; top: 279px; visibility: hidden"
			 class="swdropdown">
			<select class="htmlTextFixed" id="sweepsearch.book" name="sweepsearch.book" size="10" style="width:329px;z-index:99;">
				<c:forEach var="book" items="${requestScope.bookdetails}">
					<option value="${book.value}" ${book.value == sweepsearch.book ? 'selected="selected"' : ''}>
						${book.label}
					</option>
				</c:forEach>
			</select>
		</div>


	<!--------------entity drop down---------------->
	<div id="MovementSearch"
		style="position: absolute; left: 20px; top: 19px; width: 854px; height: 42px; border: 2px outset;"
		color="#7E97AF">
	<div id="MovementSearch"
		style="position: absolute; left: -5px; top: 4px; width: 780px; height: 42;">
	<table width="725" border="0" cellpadding="0" cellspacing="0"
		height="30">
		<tr>
			<td width="47"><B>&nbsp;&nbsp;&nbsp;<fmt:message key="movementsearch.entity" /></B></td>
			<td width="28">&nbsp;</td>
			<td width="140px">
				<select class="htmlTextAlpha" tabindex="1" id="sweepsearch.entityId" name="sweepsearch.entityId" onchange="flag=1;submitForm('display')" style="width:140px;">
					<c:forEach var="entity" items="${requestScope.entitydetails}">
						<option value="${entity.value}" ${entity.value == sweepsearch.entityId ? 'selected="selected"' : ''}>
							${entity.label}
						</option>
					</c:forEach>
				</select>
			</td>

			<td width="20">&nbsp;</td>
			<td width="490"><span id="entityName" class="spantext"></td>
		</tr>
	</table>
	</div>
	</div>

	<!----------------end of entity drop down------------------>
	<div id="movementsearchparam"
		style="position: absolute; left: 20px; top: 66px; width: 854px; height: 359px; border: 2px outset;"
		color="#7E97AF">
	<div id="movementsearchparam"
		style="position: absolute; left: 0px; top: 3px; width: 611px; height: 38px;">



	<!---------------------first fieldset---------------------------------------------------------->
	<div style="position: absolute; left: 6px; top: 0px;">

	<fieldset style="width: 138px; border: 2px groove; height: 100;">
	<legend><fmt:message key="sweep.status" /> </legend>
	<table width="98" border="0" cellpadding="0" cellspacing="0" height="115">
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.statusActive'/>" id="46" name="sweepsearch.sweepStatus" value="N" ${sweepsearch.sweepStatus == 'N' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.statusActive'/>" tabindex="2" for="46">
						<fmt:message key="stp1" />/<fmt:message key="stp2" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.statusActive'/>" id="47" name="sweepsearch.sweepStatus" value="S" ${sweepsearch.sweepStatus == 'S' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.statusActive'/>" tabindex="3" for="47">
						<fmt:message key="sweep.submitted" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.statusActive'/>" id="48" name="sweepsearch.sweepStatus" value="U" ${sweepsearch.sweepStatus == 'U' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.statusActive'/>" tabindex="4" for="48">
						<fmt:message key="sweep.authorized" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.statusActive'/>" id="49" name="sweepsearch.sweepStatus" value="B" ${sweepsearch.sweepStatus == 'B' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.statusActive'/>" tabindex="5" for="49">
						<fmt:message key="sweep.cancelled" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.selectAll'/>" id="50" name="sweepsearch.sweepStatus" value="A" ${sweepsearch.sweepStatus == 'A' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.selectAll'/>" tabindex="6" for="50">
						<fmt:message key="qualityTab.all" />
					</label>
				</td>
			</tr>
		</table>

	</fieldset>
	</div>
	<div style="position: absolute; left: 6px; top: 142px;">

	<fieldset style="width: 138px; border: 2px groove; height: 64px;">
		<legend><fmt:message key="sweep.type" /></legend>
		<table width="100" border="0" cellpadding="0" cellspacing="0" height="69">
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.selectType'/>" id="51" name="sweepsearch.sweepType" value="U" ${sweepsearch.sweepType == 'U' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.selectType'/>" tabindex="7" for="51">
						<fmt:message key="sweep.auto" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.selectType'/>" id="52" name="sweepsearch.sweepType" value="M" ${sweepsearch.sweepType == 'M' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.selectType'/>" tabindex="8" for="52">
						<fmt:message key="sweep.manual" />
					</label>
				</td>
			</tr>
			<tr height="23px">
				<td>
					<input type="radio" style="width:25px;" title="<fmt:message key='tooltip.selectAll'/>" id="53" name="sweepsearch.sweepType" value="A" ${sweepsearch.sweepType == 'A' ? 'checked' : ''}/>
					<label title="<fmt:message key='tooltip.selectAll'/>" tabindex="9" for="53">
						<fmt:message key="qualityTab.all" />
					</label>
				</td>
			</tr>
		</table>
	</fieldset>

	</div>


	<!-----------------------end of first fieldset-------------------------------------------->

	<!------------------------start of amount fieldset-------------------------------------->

	<div style="position: absolute; left: 149px; top: 4px;">
	<fieldset style="width: 696px; border: 2px groove; height: 35px;">
	<table width="635" border="0" cellpadding="0" cellspacing="0" height="30">
		<tr height="20">
			<td></td>
			<td width="116px" style="padding-left: 10px;">
				<b><fmt:message key="sweep.amountover" /></b>
			</td>
			<td width="26px"></td>
			<td width="152px">
				<input type="text" title="<fmt:message key='tooltip.amountFrom'/>" tabindex="13"
					name="sweepsearch.amountoverAsString" style="width:160px; height:20px;"
					onchange="return validateCurrency(this,'sweep.amountover',currencyFormat, document.forms[0].elements['sweepsearch.currencyCode'].value)"
					class="htmlTextNumeric" maxlength="28"
					value="${sweepsearch.amountoverAsString}" />
			</td>
			<td width="30px"></td>
			<td width="104px">
				<b><fmt:message key="sweep.amountunder" /></b>
			</td>
			<td width="15px"></td>
			<td width="176px">
				<input type="text" title="<fmt:message key='tooltip.amountTo'/>" tabindex="14"
					name="sweepsearch.amountunderAsString" style="width:160px; height:20px;"
					onchange="return validateCurrency(this,'sweep.amountunder',currencyFormat, document.forms[0].elements['sweepsearch.currencyCode'].value)"
					class="htmlTextNumeric" maxlength="28"
					value="${sweepsearch.amountunderAsString}" />
			</td>
		</tr>
	</table>

	</fieldset>
	</div>

	<!----------------------end of amount fieldset------------------------------------------>

	<!--------------------start of extra fieldset------------------------------------------>
	<div style="position: absolute; left: 149px; top: 45px;height: 295px;">
	<fieldset style="width: 696px; border: 2px groove; height: 300px;">
	<div style="padding-left:12px; left: 7px; top: 6px;">
	<table width="601px" border="0" cellpadding="0" cellspacing="0" height="20">
			<tr height="25px">
				<td width="123px"><b><fmt:message key="movementsearch.valuefrom" /></b>*</td>
				<td width="110px" style="margin-left: 5px;" id="movementsearchValuefrom">
					<input type="text" title="<fmt:message key='tooltip.enterValueDateFrom'/>"
						class="htmlTextAlpha" tabindex="15"
						name="sweepsearch.valueFromDateAsString"
						maxlength="10" style="width:80px;margin-bottom: 5px;height:20px;"
						onchange="if(validateForm(document.forms[0])) {validateFromDateField();}"
						onmouseout="dateSelected=false;"
						value="${sweepsearch.valueFromDateAsString}" />
					<a title="<fmt:message key='tooltip.selectFromDate'/>" tabindex="16"
						name="datelink" id="datelink"
						onclick="cal.select(document.forms[0].elements['sweepsearch.valueFromDateAsString'],'datelink',dateFormatValue); storeFromDate(); dateSelected=true; return false;">
						<img src="images/calendar-16.gif">
					</a>
				</td>
				<td width="94px"></td>
				<td width="61px" style="padding-left: 15px;">
					<b style="padding-left: 5px;"><fmt:message key="movementsearch.valueto" /></b>
				</td>
				<td width="110px">
					<input type="text" class="htmlTextAlpha" title="<fmt:message key='tooltip.enterValueDateTo'/>"
						tabindex="17" name="sweepsearch.valueToDateAsString"
						style="width:80px;margin-bottom: 5px;height:20px;"
						maxlength="10" onchange="validateToDateField();"
						onmouseout="toDateSelected=false;"
						value="${sweepsearch.valueToDateAsString}" />
					<a title="<fmt:message key='tooltip.selectToDate'/>" tabindex="18"
						name="datelink2" id="datelink2"
						onclick="cal2.select(document.forms[0].elements['sweepsearch.valueToDateAsString'],'datelink2',dateFormatValue); storeToDate(); toDateSelected=true; return false;">
						<img src="images/calendar-16.gif">
					</a>
				</td>
				<td width="18px"></td>
			</tr>
		</table>

	<table width="680px" border="0" cellpadding="0" cellspacing="0" height="75">
    <tr height="27px">
        <td width="119px"><b><fmt:message key="sweep.currencyGroup" /></b></td>
        <td width="12px"></td>
        <td width="128px" style="padding-left: 7px;">
            <select titleKey="tooltip.selectCurrencyId" tabindex="19" id="sweepsearch.currencyGroup" name="sweepsearch.currencyGroup" onchange="submitForm('display')" class="htmlTextAlpha" style="width:160px;">
                <c:forEach var="currencyGroup" items="${requestScope.currencyGroupList}">
                    <option value="${currencyGroup.value}" ${currencyGroup.value == sweepsearch.currencyGroup ? 'selected="selected"' : ''}>
                        ${currencyGroup.label}
                    </option>
                </c:forEach>
            </select>
        </td>
        <td width="91px"></td>
        <td width="303px" colspan="4">
            <span class="textAlpha" style="width: 308px; background: transparent; border: thin;" readonly id="currencyDesc" size="20"></span>
        </td>
    </tr>

    <tr height="25px">
        <td width="119px"><b><fmt:message key="movementsearch.currency" /></b></td>
        <td width="12px"></td>
        <td width="128px" style="padding-left: 7px;">
            <select titleKey="tooltip.selectCurrencyId" tabindex="19" id="sweepsearch.currencyCode" name="sweepsearch.currencyCode" onchange="submitForm('display')" class="htmlTextAlpha" style="width:56px;">
                <c:forEach var="currency" items="${requestScope.currencydetails}">
                    <option value="${currency.value}" ${currency.value == sweepsearch.currencyCode ? 'selected="selected"' : ''}>
                        ${currency.label}
                    </option>
                </c:forEach>
            </select>
        </td>
        <td width="91px"></td>
        <td width="302px" colspan="4">
            <span class="textAlpha" style="width: 308px; background: transparent; border: thin;" readonly id="currencyName" size="20"></span>
        </td>
    </tr>

    <tr height="27px">
        <td width="119px"><b><fmt:message key="sweep.msgformat" /></b></td>
        <td width="12px"></td>
        <td width="128px" style="padding-left: 7px;">
            <select titleKey="tooltip.selectCurrencyId" tabindex="20" id="sweepsearch.msgFormat" name="sweepsearch.msgFormat" class="htmlTextAlpha" style="width:160px;">
                <c:forEach var="msgFormat" items="${requestScope.msgdetails}">
                    <option value="${msgFormat.value}" ${msgFormat.value == sweepsearch.msgFormat ? 'selected="selected"' : ''}>
                        ${msgFormat.label}
                    </option>
                </c:forEach>
            </select>
        </td>
        <td width="91px"></td>
        <td width="302px" colspan="4">
            <span class="textAlpha" style="width: 308px; background: transparent; border: thin;" readonly id="messageName" size="20"></span>
        </td>
    </tr>
</table>


	<table width="680px" border="0" cellpadding="1" cellspacing="0" >
			<tr height="25px">
				<td width="240px"><b><fmt:message key="movementsearch.accountid" /></b></td>
				<td width="17px">&nbsp;</td>
				<td width="290px" style="padding-left: 22px;">
					<input class="textAlpha" name="accountId" tabindex="21" style="width: 197px;margin-right: 10px; height: 20px;" title="<fmt:message key='sweep.accountId'/>">
					<input title="<fmt:message key='tooltip.selectClick'/>" id="dropdownbutton_1" type="button" value="...">
				</td>
				<td width="5px">&nbsp;</td>
				<td>
					<input class="textAlpha" style="width: 302px; background: transparent; border: thin;padding-left: 10px;" readonly name="Acctname" size="20">
				</td>
			</tr>

			<tr height="29px">
				<td width="192px"><b><fmt:message key="movementsearch.bookcode" /></b></td>
				<td width="17px">&nbsp;</td>
				<td width="254px" style="padding-left: 22px;">
					<input class="textAlpha" name="bookId" tabindex="22" style="width: 197px;margin-right: 10px; height: 20px;" title="<fmt:message key='tooltip.bookId'/>">
					<input title="<fmt:message key='tooltip.clickSelBookId'/>" id="dropdownbutton_2" type="button" value="...">
				</td>
				<td width="5px">&nbsp;</td>
				<td width="200">
					<input class="textAlpha" style="width: 302px; background: transparent; border: thin; padding-left: 10px;" readonly name="bookName" size="20">
				</td>
			</tr>
</table>
<table width="680px" border="0" cellpadding="1" cellspacing="0" >
			<tr height="26px">
				<td width="95px"><b><fmt:message key="sweep.generatedby" /></b></td>
				<td width="18px">&nbsp;</td>
				<td width="238px" style="padding-left: 22px;">
					<select id="sweepsearch.inputUser" name="sweepsearch.inputUser" titleKey="tooltip.selectBookcode" tabindex="23" class="htmlTextAlpha" style="width:160px;">
						<c:forEach var="genUser" items="${requestScope.gendetails}">
							<option value="${genUser.value}" ${genUser.value == sweepsearch.inputUser ? 'selected="selected"' : ''}>${genUser.label}</option>
						</c:forEach>
					</select>
				</td>
				<td width="20px" colspan="5">&nbsp;</td>
			</tr>

			<tr height="26px">
				<td width="95px"><b><fmt:message key="sweep.submittedby" /></b></td>
				<td width="17px">&nbsp;</td>
				<td width="238px" style="padding-left: 22px;">
					<select id="sweepsearch.submitUser" name="sweepsearch.submitUser" titleKey="tooltip.selectBookcode" tabindex="24" class="htmlTextAlpha" style="width:160px;">
						<c:forEach var="subUser" items="${requestScope.subdetails}">
							<option value="${subUser.value}" ${subUser.value == sweepsearch.submitUser ? 'selected="selected"' : ''}>${subUser.label}</option>
						</c:forEach>
					</select>
				</td>
				<td width="20px" colspan="5">&nbsp;</td>
			</tr>

			<tr height="27px">
				<td width="95px"><b><fmt:message key="sweep.authorby" /></b></td>
				<td width="17px">&nbsp;</td>
				<td width="238px" style="padding-left: 22px;">
					<select id="sweepsearch.authorizedUser" name="sweepsearch.authorizedUser" titleKey="tooltip.selectBookcode" tabindex="25" class="htmlTextAlpha" style="width:160px;">
						<c:forEach var="authUser" items="${requestScope.authdetails}">
							<option value="${authUser.value}" ${authUser.value == sweepsearch.authorizedUser ? 'selected="selected"' : ''}>${authUser.label}</option>
						</c:forEach>
					</select>
				</td>
				<td width="20px" colspan="5">&nbsp;</td>
			</tr>

			<tr height="12px">
				<td width="95px"><b><fmt:message key="sweep.postcutoff" /></b></td>
				<td width="17px">&nbsp;</td>
				<td width="238px" style="padding-left: 22px;">
					<input type="checkbox" name="sweepsearch.postcutoff" value="Y" ${sweepsearch.postcutoff == 'Y' ? 'checked="checked"' : ''} class="htmlTextAlpha" style="width:13px;" titleKey="tooltip.selectPostcutoff" tabindex="26">
				</td>
				<td width="10px">&nbsp;</td>
				<td width="20px" colspan="4">&nbsp;</td>
			</tr>
		</table>


	</div>

	</fieldset>
	</div>

	<!---------------------end of extra fieldset------------------------------------------>
	<!----------------------second fieldset--------------------------------------------------->
	<div style="position: absolute; left: 6px; top: 239px;">
	<fieldset style="width: 138px; border: 2px groove; height: 69px;">

	<legend><fmt:message key="sweep.accounttype" /> </legend>

	<table width="100" border="0" cellpadding="0" cellspacing="0" height="69">
		<tr height="30">
			<td>
				<input type="radio" class="htmlTextAlpha" title="<fmt:message key='tooltip.selectAccountType'/>"
					   tabindex="17" id="14" name="sweepsearch.accType" value="C"
					   ${sweepsearch.accType == 'C' ? 'checked="checked"' : ''} />
				<label title="<fmt:message key='tooltip.selectAccountType'/>" tabindex="10" for="14">
					<fmt:message key="cash" />
				</label>
			</td>
		</tr>
		<tr height="30">
			<td>
				<input type="radio" class="htmlTextAlpha" title="<fmt:message key='tooltip.selectAccountType'/>"
					   id="15" name="sweepsearch.accType" value="U"
					   ${sweepsearch.accType == 'U' ? 'checked="checked"' : ''} />
				<label title="<fmt:message key='tooltip.selectAccountType'/>" tabindex="11" for="15">
					<fmt:message key="party.custodian" />
				</label>
			</td>
		</tr>
		<tr height="30">
			<td>
				<input type="radio" class="htmlTextAlpha" title="<fmt:message key='tooltip.selectAll'/>"
					   id="16" name="sweepsearch.accType" value="A"
					   ${sweepsearch.accType == 'A' ? 'checked="checked"' : ''} />
				<label title="<fmt:message key='tooltip.selectAll'/>" tabindex="12" for="16">
					<fmt:message key="qualityTab.all" />
				</label>
			</td>
		</tr>
	</table>

	</fieldset>
	</div>
	</div>
	</div>
	</div>

	<!-------------------------end of second fieldset--------------------------------------->

	<!-----------------------Print button------------------------->
	<div id="MovementSearch"
		style="position: absolute; left: 800px; top: 437px; width: 70px; height: 39px; visibility: visible;">
	<table width="50px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="30" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Sweep Search'),'sectionprintdwindow','left=50,top=190px,width=360,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"></a></td>

			<td align="right" id="Print"><a tabindex="30"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key = "tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<!--------------------------end of print button-------------------------->

	<!--------------------------start of ok/close button-------------------------->
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 430px; width: 854px; height: 39px; visibility: visible;">
	<div id="SweepSearch"
		style="position: absolute; left: 6; top: 4; width: 788; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">

		<tr>
			<td width="70px" id="okbutton"><a tabindex="28"
				title='<fmt:message key = "tooltip.ok"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="openSweepWindow('search','sweepsearchlistWindow','left=50,top=190,width=1020,height=472,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.ok" /></a></td>
			<td width="70px" id="closebutton"><a tabindex="29"
				title='<fmt:message key = "tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<!--------------------------End of ok/close button-------------------------->
</form>
</body>
</html>