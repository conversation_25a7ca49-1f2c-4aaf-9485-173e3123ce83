<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml" %>
<%@ page import="java.util.*"  %>
<%@ page import="org.swallow.util.OpTimer"  %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<corporateaccount
		valueDate="${requestScope.valueDate}"
		selectedEntityId="${requestScope.selectedEntityId}"
		selectedEntityName="${requestScope.selectedEntityName}">

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key='corporate.name'/>"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="name"
						editable="false"
						width="275"
						sort="true"
				/>
				<column
						heading="<fmt:message key='corporate.amount'/>"
						draggable="false"
						filterable="true"
						type="num"
						dataelement="amount"
						editable="false"
						width="150"
						sort="true"
				/>
			</columns>
		</metadata>
		<rows size="${requestScope.recordCount}">
			<c:forEach items="${requestScope.corporateAccountDetails}" var="corporateAccountDetails">
				<row>
					<name clickable="false" id="${corporateAccountDetails.corporateSeqNo}">${corporateAccountDetails.corporateSeqNo}</name>
					<amount clickable="false">${corporateAccountDetails.amountAsString}</amount>
				</row>
			</c:forEach>
		</rows>
	</grid>
</corporateaccount>
