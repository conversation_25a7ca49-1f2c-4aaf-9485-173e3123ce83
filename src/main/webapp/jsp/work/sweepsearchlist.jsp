<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="sweepSearchResults.title.window"/></title>
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script language="JAVASCRIPT" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">

var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var dateFormat = '${sessionScope.CDM.dateFormat}';
var screenRoute = "SweepSearchList";



var archiveId = '${requestScope.archiveId}';
var entityId = '${requestScope.entityId}';
var amountover = '${requestScope.amountover}';
var amountunder = '${requestScope.amountunder}';
var currencyCode = '${requestScope.currencyCode}';
var currencyGroup = '${requestScope.currencyGroup}';
var accountId = '${requestScope.accountId}';
var bookCode = '${requestScope.bookCode}';
var valueFromDateAsString = '${requestScope.valueFromDateAsString}';
var valueToDateAsString = '${requestScope.valueToDateAsString}';
var generatedby = '${requestScope.generatedby}';
var postcutoff = '${requestScope.postcutoff}';
var submittedby = '${requestScope.submittedby}';
var authorisedby = '${requestScope.authorisedby}';
var accounttype = '${requestScope.accounttype}';
var sweeptype = '${requestScope.sweeptype}';
var status = '${requestScope.status}';
var msgFormat = '${requestScope.msgFormat}';



	function buildViewSweepDisplayURL(methodName, sweepId, valueDate, accountIdCr, accountIdDr, originalSweepAmt, sweepType, status1, inputUser, submitUser, authorizedUser, movementIdCr, movementIdDr, submitSweepAmt, authorizeSweepAmt, entityId, archiveId) {
	    var param = 'sweepsearch.do?method=' + methodName + '&sweepId=' + sweepId;
	    param += '&valueDate=' + valueDate;
	    param += '&accountIdCr=' + accountIdCr;
	    param += '&accountIdDr=' + accountIdDr;
	    param += '&originalamt=' + originalSweepAmt;
	    param += '&sweepType=' + sweepType;
	    param += '&sweepStatus=' + status1;
	    param += '&inputUser=' + inputUser;
	    param += '&submitUser=' + submitUser;
	    param += '&authorizedUser=' + authorizedUser;
	    param += '&movementIdCr=' + movementIdCr;
	    param += '&movementIdDr=' + movementIdDr;
	    param += '&submitamt=' + submitSweepAmt;
	    param += '&authorizeamt=' + authorizeSweepAmt;
	    param += '&entityid=' + entityId;
	    param += '&archiveId=' + "";
	    
	    return openWindow(param,'sweepsearchlistWindow','left=50,top=190,width=800,height=745,toolbar=0, resizable=yes, scrollbars=yes','true');
	}	
</SCRIPT>
</head>

<body>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
</script>

</body>
</html>