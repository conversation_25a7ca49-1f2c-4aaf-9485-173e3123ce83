<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title><fmt:message key="corporateAccount.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">

</head>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<script type="text/javascript">

   window.onload = function () {
   }
  	 window.onunload = call;
  	 var refreshPending = false;

	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	var dateFormat = '${sessionScope.CDM.dateFormatValue}';
	var currencyFormat = '${sessionScope.CDM.currencyFormat}';
	var testDate= "<%=SwtUtil.getSystemDateString() %>";
	var selectedDate ="${requestScope.selectedDate}";
	var selectedEntityId ="${requestScope.selectedEntityId}";
	var selectedEntityName ="${requestScope.selectedEntityName}";
	var menuAccessIdParent = getMenuAccessIdOfChildWindow("centralBankMonitor.do");
	var screenRoute = "corporateAccount";
	/**
      * formatCurrency
      *
	  * This method is used to format the amount in system currency format (called by flex)
	 */
	function formatCurrency(amount){
		var strAmount=formatCurrency_centralMonitor(amount, currencyFormat, null);
		return strAmount;
	 }

	/*Start : Added by Imed B on 20-02-2014*/
	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();

	label["text"]["label-corporateEntries"] = "<fmt:message key="corporateAccount.title.corporateEntries"/>";
	label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
	label["text"]["label-recordExists"] = "<fmt:message key="corporateAccount.recordExists"/>";
	label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
	label["text"]["label-addCorporateEntries"] = "<fmt:message key="corporateAccount.addCorporateEntries"/>";
	label["text"]["label-wantToDelete"] = "<fmt:message key="corporateAccount.wantToDelete"/>";
	label["text"]["alert-delete"] = "<fmt:message key="corporateAccount.alertDelete"/>";
	label["text"]["label-changeCorporateEntries"] = "<fmt:message key="corporateAccount.changeCorporateEntries"/>";
	label["text"]["label-fillMondatoryFields"] = "<fmt:message key="corporateAccount.fillMondatoryFields"/>";
	label["text"]["alert-mandatory"] = "<fmt:message key="corporateAccount.alert.mandatory"/>";
	label["text"]["button-save"] = "<fmt:message key="corporateAccount.label.save"/>";
	label["tip"]["button-save"] = "<fmt:message key="corporateAccount.tooltip.save"/>";
	label["text"]["button-close"] = "<fmt:message key="corporateAccount.label.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="corporateAccount.tooltip.close"/>";
	label["text"]["label-corporateName"] = "<fmt:message key="corporateAccount.corporateName"/>";
	label["text"]["label-amount"] = "<fmt:message key="corporateAccount.amount"/>";
	label["text"]["label-validAmount"] = "<fmt:message key="corporateAccount.validAmount"/>";
	label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
	label["text"]["button-add"] = "<fmt:message key="corporateAccount.label.add"/>";
	label["tip"]["button-add"] = "<fmt:message key="corporateAccount.tooltip.add"/>";
	label["text"]["button-change"] = "<fmt:message key="corporateAccount.label.change"/>";
	label["tip"]["button-change"] = "<fmt:message key="corporateAccount.tooltip.change"/>";
	label["text"]["button-delete"] = "<fmt:message key="corporateAccount.labelDelete"/>";
	label["tip"]["button-delete"] = "<fmt:message key="corporateAccount.tooltipDelete"/>";
	label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
	label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
	label["text"]["label-valueDate"] = "<fmt:message key="sweepDetail.valueDate"/>";
	/*End : Added by Imed B on 20-02-2014*/		

	 /**
      * help
      * 			
	  * This method is used to open the help window for central bank monitor    
	  */
		function help(){
				openWindow(buildPrintURL('print','Corporate Account'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
          }
          
          function CallBackApp(){
  			window.opener.CallBackApp();
		  } 

</script>
<%@ include file="/angularscripts.jsp"%>

<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();" onunload="CallBackApp();">
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>