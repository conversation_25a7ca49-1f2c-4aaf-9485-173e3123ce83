<?xml version="1.0" encoding="UTF-8" ?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for Entity Monitor Options screen.
-
- Author(s): Bala .D
- Date: 08-03-2011
- Converted to JSTL: [Current Date]
-->

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<monitoroptions refresh="${autoRefreshRate}"
				currfontsize="${fontSize}"
>
	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>

	<c:set var="usepersonalentity" value="${entityMonitorOptions}" />
	<options>
		<usepersonalentitylist>
			<c:out value="${usepersonalentity.personalEntityList}" />
		</usepersonalentitylist>
		<zzz>aaaa</zzz>
		<hideweekend>
			<c:out value="${usepersonalentity.hideWeekends}" />
		</hideweekend>

		<usepersonalcurrency>
			<c:out value="${usepersonalentity.personalCurrencyList}" />
		</usepersonalcurrency>
	</options>

	<selects>
		<select id="ccylist">
			<c:forEach items="${currencyList}" var="currency">
				<option
						value="${currency.value}"
						selected="${usepersonalentity.reportCurrency == currency.value ? '1' : '0'}"
				>${fn:escapeXml(currency.label)}</option>
			</c:forEach>
		</select>

		<select id="ccymultiplier">
			<option value="" selected="0"> </option>
			<c:forEach items="${reportingCurr}" var="multiplier">
				<option
						value="${multiplier.value}"
						selected="${usepersonalentity.useCurrencyMultiplier == multiplier.value ? '1' : '0'}"
				>${fn:escapeXml(multiplier.label)}</option>
			</c:forEach>
		</select>
	</selects>
</monitoroptions>