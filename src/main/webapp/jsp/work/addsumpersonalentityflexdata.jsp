<?xml version="1.0" encoding="UTF-8"?>
<!--
-
- Author(s): KBenSlama
- Date: 28-03-2018
-->

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>





<c:set var="recordCount" value="${fn:length(requestScope.personalEntityList)}" />

<personalentity>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key='label.personalEntityList.entityId'/>"
						draggable="false"
						type="str"
						dataelement="entityid"
						width="125"
						sortable="true"
				/>
				<column
						heading="<fmt:message key='label.personalEntityList.entityName'/>"
						draggable="false"
						type="str"
						dataelement="entityname"
						width="180"
				/>
				<column
						heading="<fmt:message key='label.personalEntityList.button.addsum'/>"
						draggable="false"
						type="bool"
						dataelement="addsum"
						width="100"
				/>
			</columns>
		</metadata>

		<rows size="${recordCount}">
			<c:forEach var="rowrecord" items="${requestScope.personalEntityList}">
				<row>
					<entityid clickable="false">${rowrecord.id.entityId}</entityid>
					<entityname clickable="false">${rowrecord.entityName}</entityname>
					<addsum selected="${rowrecord.yesAggrgEntity == 'Y' ? 'true' : 'false'}">
							${rowrecord.yesAggrgEntity == 'Y' ? 'Y' : 'N'}
					</addsum>
				</row>
			</c:forEach>
		</rows>
	</grid>
</personalentity>
