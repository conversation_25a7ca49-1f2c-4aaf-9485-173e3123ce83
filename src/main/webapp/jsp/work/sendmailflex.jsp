<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.model.MenuItem"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html lang="en">

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title><fmt:message key="sendmail.title.window" /></title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		
		<script type="text/javascript">
		
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			label["text"]["from"] = "<fmt:message key="label.mail.from"/>";
			label["text"]["to"] = "<fmt:message key="label.mail.to"/>";
			label["text"]["cc"] = "<fmt:message key="label.mail.cc"/>";
			label["text"]["subject"] = "<fmt:message key="label.mail.subject"/>";
			label["text"]["emailAddress"] = "<fmt:message key="label.mail.emailAddress"/>";
			label["text"]["messageBody"] = "<fmt:message key="label.mail.messageBody"/>";
			label["text"]["attachments"] = "<fmt:message key="label.mail.attachments"/>";
			label["text"]["popUp-title"] = "<fmt:message key="label.mail.popUpTitle"/>";
			label["text"]["send-success"] = "<fmt:message key="alert.mail.sendSuccess"/>";
			label["text"]["send-fail"] = "<fmt:message key="alert.mail.sendFail"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.alert.error"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.alert.warning"/>";
			label["text"]["alert-info"] = "<fmt:message key="usermaintenance.info"/>";
			label["text"]["button-send"] = "<fmt:message key="button.send"/>";
			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["text"]["alert-contactAdmin"] = "<fmt:message key="error.contactAdmin"/>";
			label["text"]["alert-mandatoryFields"] = "<fmt:message key="alert.pleaseFillAllMandatoryFields"/>";
			label["text"]["alert-validationmsg"] = "<fmt:message key="alert.validationmsg"/>";
			label["text"]["alert-missingBodyOrSubject"] = "<fmt:message key="alert.missingBodyOrSubject"/>";
			
			label["tip"]["button-To"] = "<fmt:message key="tooltip.mail.recipientsListTo"/>";
			label["tip"]["button-Cc"] = "<fmt:message key="tooltip.mail.recipientsListCc"/>";
			label["tip"]["button-attachment"] = "<fmt:message key="tooltip.mail.attachment"/>";
		
			var senderEmail="<%=PropertiesFileLoader.getInstance().getMailPropertyValue(SwtConstants.MAIL_USER_EMAIL)%>";
		</script>
	</head>
	
	 <body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
		<form id="DataForm" method="post" target="tmp" enctype="multipart/form-data"> 
			<input name="method" type="hidden" value="">
			<input name="timeFrame" type="hidden" value=""> 
			<input name="menuAccessId" type="hidden" >
	
			<div id="swf">
				<object id='mySwf' height='100%' width='100%'
						classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000'>
					<param name='src' value='jsp/work/sendmail.swf?version=<%= SwtUtil.appVersion %>' />
					<param name='flashVars' value='' />
					<embed name='mySwf' src='jsp/work/sendmail.swf?version=<%= SwtUtil.appVersion %>'
					height='100%' width='100%' flashVars=''/>
				</object>
			</div>
			<input name="mybutton" type="submit" value="post" style="visibility: hidden;"/>
			<iframe name="tmp" width="0%" height="0%" src="#" />
		</form>
	</body>

</html>