<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@page import="org.swallow.work.model.Movement"%>

<html>
<head>
	<title>Pre-Advice Input - SMART-Predict</title>
	<%@ include file="/angularJSUtils.jsp"%>
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>";
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "PreAdviceInput";

	/**
	 *	This section is used to handle calender button on the screen && is used to set the position of the same.
	 */
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var menuAccessId = "${requestScope.menuAccessId}";

	/**
	 * party
	 *
	 * This method is used to open the party search screen when click on the
	 * counterparty/matching party drop down button
	 **/
	function party(flag,elementId,elementName,entityId) {
		//var entityId = document.forms[0].elements['movement.id.entityId'].value;
		var partyURL = 'party.do?method=preSearchParties&entityId='+entityId;
		partyURL += '&custodianFlag='+flag;
		partyURL += '&idElementName='+elementId;
		partyURL += '&descElementName='+elementName;
		openWindow(partyURL,'SearchParty','left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes','true');
	}

	function openChildPartyWindow(methodName, entityId){
		var param = '/' + appName + '/preadviceinput.do?method='+methodName+'&entityId='+entityId;
		var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=580,height=700,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}

	function showMvmnt(methodName,movementId,entityId){
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
		var menuName = new String('<fmt:message key="mvmDisplay.title.window"/>');
		var param = 'movement.do?method='+methodName;
		param += '&entityCode=' + entityId;
		param += '&movementId=' + movementId;
		openWindow(param,'movementWindow','left=50,top=190,width=985,height=787,toolbar=0, resizable=yes, scrollbars=yes','true');
		return false;

	}

	function openImportWindow(methodName){
		var param = '/' + appName + '/preadviceinput.do?method='+methodName;
		var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=1200,height=900,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}



</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="<fmt:message key="PreAdviceInput.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
