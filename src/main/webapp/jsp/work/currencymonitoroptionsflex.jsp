<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Currency Monitor Options screen.
  - Also, to embed the sub screens of Currency Monitor Options screen:
  - (i) Personal Entity List
  - (ii) Personal Currency List
  - Also, to load the label values for this screen.
  - 
  - Modified by: I. Marshal <PERSON>
  - Date: 01-06-2011
  -->
<html>
<head>
<%@ include file="/taglib.jsp"%>
<title><fmt:message key="currencyMonitor.options.title"/></title>

<script type="text/javascript">
	var cancelcloseElements = new Array(1);
	var defaultDaysChange = false;
	cancelcloseElements[0] = "cancelbutton";

	<c:if test="${requestScope.parentFormRefresh == 'Y'}">

	window.opener.refreshPending = true;
	self.close();

</c:if>

	/*Start: Modified for Mantis 1386:"User defined option to show normal && small font" by Marshal on 31-May-2011*/
	/**
      * This function is used to submit the form changes when the Save
	  * button in the Options screen is clicked
	  *
	  * @param methodName - method name when the save button is being clicked
	  */
	function submitForm (methodName) {
		   // Gets the entered defaultDays value
		   var defaultDays=validateField(document.forms[0].elements['currMonitorOptions.numberOfDays'],'currMonitorOptions.numberOfDays','numberPat');
		   // Gets the entered rateStatus value
		   var rateStatus=validateField(document.forms[0].elements['currMonitorOptions.propertyValue'],'currMonitorOptions.propertyValue','numberPat');
		   // Submit form if conditions are satisfied
		   var submitFormFlag = false;
	   // Checks the availability of defaultDays, else set the focus on it
	   if(defaultDays) {
	   	// Checks the availability of rateStatus, else set the focus on it
		if (rateStatus) {
		  // If both the validation of default days && refresh rate is true, submit the form.
		  if (validateForm() && validateRefreshRate()) {
		    	document.forms[0].method.value = methodName;

		    	var result = checkDateRange();
		    	if(defaultDaysChange){
		    		if(result){
		    		// Calls the confirm function to prompt the user on setting 5 as the refresh rate
					var refreshRateConfirm = confirm('<fmt:message key="currencyMonitor.alert.dateRange"/>');
						// Added by KaisBS for mantis 1868 (issue 1054_SEL_091)
				    	// Alert the user that changed values will not be applied until he restarts the screen.
						if (refreshRateConfirm) {
							submitFormFlag = true;
						} else {
							document.forms[0].elements['currMonitorOptions.numberOfDays'].focus();
						}
		    		}else {
						submitFormFlag = true;
		    		}
				alert('<fmt:message key="currencyMonitor.alert.defDayschanged"/>');
		    	}else {
					submitFormFlag = true;
		    	}
		 	  }
		   } else {
				document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
		   }
		}
		else {
		    document.forms[0].elements['currMonitorOptions.numberOfDays'].focus();
		}
	   if(submitFormFlag)
			document.forms[0].submit();
	}
	/*End: Modified for Mantis 1386:"User defined option to show normal && small font" by Marshal on 31-May-2011*/

	//Function return of the system date + numberOfDays entred by the user exeed the date range.
	function checkDateRange() {
		var dateFormat = window.opener.dateFormat;
		var systemDate = window.opener.dbDate;
		var nDaysAheadToToday=window.opener.nDaysAheadToToday;
		var result = convertUKtoUS(systemDate,dateFormat) ;
		var todate   = new Date(result);
		var aheadDate=new Date(result);
		var numberOfDays = document.forms[0].elements["currMonitorOptions.numberOfDays"].value;
		var days = parseInt(numberOfDays,10);
		todate.setDate(todate.getDate()+days);
		aheadDate.setDate(aheadDate.getDate()+nDaysAheadToToday);
		if( todate > aheadDate){
			return true;
		}else {
			return false;
		}
	}
	//Convert date from Uk to US format
	function convertUKtoUS(input,dateFormat)
	{
		var rtn=null;
			if (dateFormat.toLowerCase() == "dd/mm/yyyy")
			{
				var dateArry=input.split("/");
				rtn=dateArry[1] + "/" + dateArry[0] + "/" + dateArry[2];
			}
			else
			{
				rtn=input;//Already in US format
			}

		return rtn;
	}
	function validateForm () {
		var defDays = document.forms[0].elements["currMonitorOptions.numberOfDays"].value;

		if (defDays > 14 || defDays < 1) {
			alert('<fmt:message key="currencyMonitor.alert.defDays"/>');
			return false;
		} else {
			return true;
		}
	}

	function bodyOnLoad () {
		var menuAccessIdParent = getMenuAccessIdOfChildWindow("currmonitorNew.do");
		/*if (menuAccessIdParent != "0") {
			document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		}	*/
	}

	window.onload = function () {
		ShowErrMsgWindow ('${actionError}');
		bodyOnLoad();
		setParentChildsFocus();
		setTitleSuffix(document.forms[0]);
	}

	window.onunload = call;

	function closeWindow() {
		window.opener.callApp("Y");
		window.close();
	}

	function callParent() {
		window.opener.callApp("Y");
	}

	/*Start: Added for Mantis 1386:"User defined option to show normal && small font" by Marshal on 31-May-2011*/
	/**
	  * This function is used to validate the given refresh rate.
      *
	  */
	function validateRefreshRate() {
		var refreshRate=document.getElementById("currMonitorOptions.propertyValue").value;
		// Checks the entered refresh rate, else set the focus to the rate text box
		if (refreshRate) {
		// Checks the given refresh rate is less than 5 && sets it's value accordingly
		if (refreshRate < 5) {
			// Calls the confirm function to prompt the user on setting 5 as the refresh rate
			var refreshRateConfirm = confirm('<fmt:message key="alert.currencyMonitor.confirmRefreshRate"/>');
			// Checks && sets the refresh rate to 5, else set the focus to the rate text box
			if (refreshRateConfirm) {
				document.getElementById("currMonitorOptions.propertyValue").value = 5;
			} else {
				document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
				return false;
			}
		}
		return true;
		} else {
			alert('<fmt:message key="alert.currencyMonitor.refreshRate"/>');
			document.forms[0].elements['currMonitorOptions.propertyValue'].focus();
			return false;
		}
	}
	/*End: Added for Mantis 1386:"User defined option to show normal && small font" by Marshal on 31-May-2011*/
	// Added by KaisBS to catch the change of defaultdays
	function affectVar(){
		defaultDaysChange = true;
	}


	</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onunload="callParent()">
<form action="currmonitorNew.do" method="post">
	<input name="method" type="hidden" value="">
	<!-- Start: Modified for Mantis 1386: User defined option to show normal && small fonts by Marshal on 19-May-2011 -->
	<div id="MonitorOptions"
		style="position: absolute; left: 20px; top: 20px; width: 470; height: 184px; border: 2px outset;"
		color="#7E97AF">
	<div id="MonitorOptions"
		style="position: absolute; left: 8px; top: 4px; width: 470px; height: 100;">
	<table width="415" border="0" cellpadding="0" cellspacing="0" height="30">
    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.useCcyMultiplier" /></b></td>
       <td width="120px">
           <input type="checkbox"
                  name="currMonitorOptions.useCurrencyMultiplier"
                  class="htmlTextAlpha"
                  style="width:13px;"
                  value="Y"
                  title="<fmt:message key='ccyMonitorOptions.useCcyMultiplier' />"
                  tabindex="1"
                  ${requestScope.currMonitorOptions.useCurrencyMultiplier == 'Y' ? 'checked' : ''}
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.hideWeekends" /></b></td>
       <td width="120px">
           <input type="checkbox"
                  name="currMonitorOptions.hideWeekends"
                  class="htmlTextAlpha"
                  style="width:13px;"
                  value="Y"
                  title="<fmt:message key='ccyMonitorOptions.hideWeekends' />"
                  tabindex="2"
                  ${requestScope.currMonitorOptions.hideWeekends == 'Y' ? 'checked' : ''}
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.hideLoro" /></b></td>
       <td width="120px">
           <input type="checkbox"
                  name="currMonitorOptions.hideLoro"
                  class="htmlTextAlpha"
                  style="width:13px;"
                  value="Y"
                  title="<fmt:message key='ccyMonitorOptions.hideLoro' />"
                  tabindex="3"
                  ${requestScope.currMonitorOptions.hideLoro == 'Y' ? 'checked' : ''}
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.defaultDays" /></b></td>
       <td width="120px" align="Left">
           <input type="text"
                  name="currMonitorOptions.numberOfDays"
                  maxlength="2"
                  class="htmlTextNumeric"
                  style="width:50px;"
                  tabindex="4"
                  value="${requestScope.currMonitorOptions.numberOfDays}"
                  title="<fmt:message key='ccyMonitorOptions.enterDefaultDays' />"
                  onchange="validateField(this,'currMonitorOptions.numberOfDays','numberPat');affectVar();"
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.usePersonalCurrencyList" /></b></td>
       <td width="120px">
           <input type="checkbox"
                  name="currMonitorOptions.ccyPersonalList"
                  class="htmlTextAlpha"
                  style="width:13px;"
                  value="Y"
                  title="<fmt:message key='ccyMonitorOptions.usePersonalCurrencyList' />"
                  tabindex="5"
                  ${requestScope.currMonitorOptions.ccyPersonalList == 'Y' ? 'checked' : ''}
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.refreshRate" /></b></td>
       <td width="120px">
           <input type="text"
                  name="currMonitorOptions.propertyValue"
                  maxlength="3"
                  class="htmlTextNumeric"
                  style="width:50px;"
                  tabindex="6"
                  value="${requestScope.currMonitorOptions.propertyValue}"
                  title="<fmt:message key='tooltip.ccyMonitorOptions.enterRefreshRate' />"
                  onchange="return validateField(this,'currMonitorOptions.propertyValue','numberPat');"
           />
       </td>
    </tr>

    <tr height="24">
       <td width="180px"><b><fmt:message key="ccyMonitorOptions.fontSize" /></b></td>
       <td width="160px">
           <input type="radio"
                  id="1"
                  style="width:13px;"
                  tabindex="7"
                  name="currMonitorOptions.fontSize"
                  value="Normal"
                  title="<fmt:message key='tooltip.ccyMonitorOptions.selectFontSizeNormal' />"
                  ${requestScope.currMonitorOptions.fontSize == 'Normal' || empty requestScope.currMonitorOptions.fontSize ? 'checked' : ''}
           />
           <label for="1"><fmt:message key="ccyMonitorOptions.fontSizeNormal" />&nbsp;</label>

           <input type="radio"
                  id="2"
                  style="width:13px;"
                  tabindex="8"
                  name="currMonitorOptions.fontSize"
                  value="Small"
                  title="<fmt:message key='tooltip.ccyMonitorOptions.selectFontSizeSmall' />"
                  ${requestScope.currMonitorOptions.fontSize == 'Small' ? 'checked' : ''}
           />
           <label for="2"><fmt:message key="ccyMonitorOptions.fontSizeSmall" /></label>
       </td>
    </tr>
</table>
	</div>
	</div>

	<div id="ddimagebuttons"
		style="position: absolute; z-index: 99; left: 290px; top: 100px; width: 100px; height: 0px; visibility: visible">
	<div id="buttonstatus"
		style="position: absolute; left: 12; top: 19; width: 100px; height: 30px; visibility: visible;">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="linkbutton"></td>
		</tr>
		<tr height="25px" valign="bottom">
			<td id="contactbutton" style="padding-bottom: 5px;"><a tabindex="5"
				title='<fmt:message key="tooltip.Currencies"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow('personalcurrency.do?','personalCurrencyMaintenanceWindow','left=50,top=190,width=560,height=300,toolbar=0, resizable=yes','true')"><fmt:message key="button.currency"/></a></td>
		</tr>
	</table>
	</div>
	</div>

	<div id="MonitorOptionsButtons"
		style="position: absolute; left: 390; top: 218; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="7" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Monitor Options'),'monitoroptionswindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
			<td align="right" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 210; border: 2px outset; width: 470; height: 39px; visibility: visible;">
	<div id="MonitorOptionsButtons"
		style="position: absolute; left: 6; top: 4; width: 280px; height: 15px; visibility: visible;">

	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="savebutton" width="70px"><a tabindex="9"
				title='<fmt:message key="tooltip.save"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:submitForm('saveCurrencyMonitorOptions')"><fmt:message key="button.save"/></a></td>
			<td id="cancelbutton" width="70px"><a
				title='<fmt:message key="tooltip.canceloption"/>' tabindex="10"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="closeWindow();"><fmt:message key="button.cancel"/></a></td>
		</tr>
	</table>
	<!-- End: Modified for Mantis 1386: User defined option to show normal && small fonts by Marshal on 19-May-2011 -->
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 470; height: 15px; visibility: hidden;">
	<table width="7" border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="savedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.save"/></a></td>
		</tr>
	</table>
	</div>


	</div>
</form>
</body>
</html>