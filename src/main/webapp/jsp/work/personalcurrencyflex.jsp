<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Personal Currency List screen. 
  - Also, to load the label values for this screen.	
  - Author(s): Bala .D
  - Date: 08-03-2011
  -->
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<link rel="icon" type="image/x-icon" href="favicon.ico">
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><fmt:message key="label.personalCurrencyList.title.window"/></title>
		</head>
		<body>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">

			// set the label values
			var screenRoute = "personalCcy";
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["ok"] = "<fmt:message key="label.personalCurrencyList.button.ok"/>";
			label["tip"]["ok"] = "<fmt:message key="tooltip.personalCurrencyList.button.ok"/>";
			label["text"]["cancel"] = "<fmt:message key="label.personalCurrencyList.button.cancel"/>";
			label["tip"]["cancel"] = "<fmt:message key="tooptip.personalCurrencyList.button.cancel"/>";
			/*Start Added by Imed b on 20-02-2014*/
			label["text"]["label-personalCurrency"] = "<fmt:message key="personalCurrency.title"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["label-number"] = "<fmt:message key="alert.forecastMonitor.enterNumber"/>";
                        label["text"]["label-positiveNumber"] = "<fmt:message key="alert.interfaceSettings.posnumber"/>";
                        label["text"]["label-NumberBetween"] = "<fmt:message key="personalCurrencyList.numberBetweenBigRange"/>";
			/*End Added by Imed b on 20-02-2014*/
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("entityMonitor.do");
			/*
 			 * Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
             */
            // get the system date
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			// get the dateFormat
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			// get the application name
			var appName = "<%=SwtUtil.appName%>";

			// This method is used to close the window
			function closeWindow() {
				window.close();
			}

			// This function opens the help screen
			function help(){
				openWindow(buildPrintURL('print','Personal Currency List'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

		</script>
<%@ include file="/angularscripts.jsp"%>
	</body>

		<form id="exportDataForm" target="tmp" method="post">

		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="personalCurrencyMaintenance.title.mainWindow"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />

</html>