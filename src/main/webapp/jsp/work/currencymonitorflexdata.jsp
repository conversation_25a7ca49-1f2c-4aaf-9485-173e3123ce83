<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="java.util.*" %>
<%@ page import="org.swallow.util.SwtConstants" %>



<currencymonitor
		databuilding="${not empty requestScope.jobFlagStatus ? 'true' : 'false'}"
		currencyFormat="${requestScope.session.CDM.currencyFormat}"
		refresh="${requestScope.autoRefreshRate}"
		hideLoro="${requestScope.hideLoro}"
		to="${requestScope.currencyMonitor.toDateAsString}"
		from="${requestScope.currencyMonitor.fromDateAsString}"
		sysDateFrmSession="${requestScope.sysDateFrmSession}"
		lastRefTime="${requestScope.lastRefTime}"
		dateComparing="${requestScope.dateComparing eq 'Y'}"
		currfontsize="${requestScope.fontSize}"
>

		<%-- Response status - OK --%>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

		<%-- Job timing --%>
	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>

		<%-- Currency Monitor grid data --%>
	<grid>
			<%-- Grid column metadata --%>
		<metadata>
			<columns>
					<%-- Alerting --%>
				<column
						heading=""
						draggable="false"
						filterable="false"
						type="str"
						dataelement="alerting"
						width="10"
						sort="true"
						clickable="true"
						resizable="false"
				/>
					<%-- Currency code --%>
				<column
						heading="<fmt:message key="sweep.currencyCode"/>"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="ccy"
						sort="true"
						width="${requestScope.column_width.ccy}"
				/>
					<%-- Loro balance --%>
				<c:if test="${requestScope.hideLoro != 'Y'}">
					<column
							heading="<fmt:message key='acc.lorotype'/>"
							draggable="false"
							filterable="false"
							type="num"
							dataelement="loro"
							sort="true"
							width="${requestScope.column_width.loro}"
					/>
				</c:if>
					<%-- Predicted balance --%>
				<c:forEach items="${requestScope.currencyMonitor.headerDetails}" var="headerRecord" varStatus="headerStatus">
					<column
							heading="${headerRecord.balanceDateAsString}"
							draggable="false"
							filterable="false"
							holiday="${headerRecord.holiday != 'N' ? 'true' : 'false'}"
							type="num"
							sort="true"
							dataelement="predictdate${headerStatus.count}"
							width="${requestScope.column_width['predictdate'.concat(headerStatus.count)]}"
					/>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${requestScope.currencyMonitor.balanceDetails.size()}">
			<c:forEach items="${requestScope.currencyMonitor.balanceDetails}" var="currencyRecord">
			<row>
				<ccy clickable="false">${currencyRecord.currCode}</ccy>
				<alerting clickable="false">${currencyRecord.scenarioHighlighted}</alerting>
				<c:if test="${requestScope.hideLoro != 'Y'}">
					<loro
							negative="${currencyRecord.loroBalanceNegative ? 'true' : 'false'}"
							clickable="${requestScope.currencyMonitor.entityId != 'All' ? 'true' : 'false'}"
					>${currencyRecord.loroBalanceAsString}</loro>
				</c:if>

				<c:forEach items="${currencyRecord.balanceDateRecords}" var="balanceRecord" varStatus="balanceStatus">
				<c:set var="predictDateElement" value="predictdate${balanceStatus.count}" />
				<${predictDateElement}
						date="${balanceRecord.balanceOrgDateAsString}"
				holiday="${balanceRecord.holiday != 'N' ? 'true' : 'false'}"
				negative="${balanceRecord.predBalanceNegative ? 'true' : 'false'}"
				clickable="${requestScope.currencyMonitor.entityId != 'All' ? 'true' : 'false'}"
				>${balanceRecord.predBalanceAsString.trim()}</${predictDateElement}>
			</c:forEach>
		</row>
		</c:forEach>
	</rows>

	<c:set var="totalRecord" value="${requestScope.currencyMonitor.balanceTotal}"/>
	<totals>
		<total>
			<ccy clickable="false"><fmt:message key="positionlevel.total" /> ${totalRecord.currCode}</ccy>
			<c:if test="${requestScope.hideLoro != 'Y'}">
				<loro
						negative="${totalRecord.loroBalanceNegative ? 'true' : 'false'}"
				>${totalRecord.loroBalanceAsString}</loro>
			</c:if>

			<c:forEach items="${totalRecord.balanceDateRecords}" var="totalBalanceRecord" varStatus="totalStatus">
			<c:set var="predictTotalElement" value="predictdate${totalStatus.count}" />
			<${predictTotalElement}
					negative="${totalBalanceRecord.predBalanceNegative ? 'true' : 'false'}"
			>${totalBalanceRecord.predBalanceAsString}</${predictTotalElement}>
		</c:forEach>
	</total>
</totals>
</grid>

	<selects>
	<select id="entity">
		<c:forEach items="${requestScope.entities}" var="entity">
			<option
					value="${entity.value}"
					selected="${requestScope.currencyMonitor.entityId == entity.value ? '1' : '0'}"
			>${entity.label}</option>
		</c:forEach>
	</select>

	<select id="currencygroup">
		<c:forEach items="${requestScope.currencyGroupList}" var="group">
			<option
					value="${group.value}"
					selected="${requestScope.currencyMonitor.currGrp == group.value ? '1' : '0'}"
			>${group.label}</option>
		</c:forEach>
	</select>
</selects>
</currencymonitor>