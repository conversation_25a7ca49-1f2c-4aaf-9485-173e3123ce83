<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="manSweeping.title.window"/></title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>



<SCRIPT language="JAVASCRIPT">

var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];
var hideAccountsAfterCutOff= "${requestScope.hideAccountsAfterCutOff}";
hideAccountsAfterCutOff = hideAccountsAfterCutOff === "true" || hideAccountsAfterCutOff === true;
var dynamicTab = true;
var tabIndex=initialtab[0];
var selectedtab = initialtab[1];
var previoustab=""
var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
var globalWindowName = "";
var globalAttr = "";
var globalIsCascad = ""
var tabNames = new Array( 'sweepTodayParent','sweepTodayPlusOneParent','sweepTodayPlusTwoParent','sweepTodayPlusThreeParent',
					'sweepTodayPlusFourParent','sweepTodayPlusFiveParent','sweepTodayPlusSixParent','sweepTodayPlusSevenParent','sweepTodayPlusEightParent');
var lastRefTime = "${requestScope.lastRefTime}";

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
var oXMLHTTP = new XMLHttpRequest();
var screenRoute = "ManualSweep";

/* Start : Code added for Mantis 2150 by M.Bouraoui on 01/04/2012  */

// set isSelectedNoWorkingDay flag from request parameters
var isSelectedNoWorkingDay="${requestScope.tabFlag}".charAt(tabIndex-1);
/* End : Code added for Mantis 2150 by M.Bouraoui on 01/04/2012  */

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function openSweepWindow(entityId, selectedList){
	console.log('selectedList*******',selectedList);
	var param='sweepdetail.do?method=manual&entityCode=';
	param +=entityId;
	param +='&selectedList='+selectedList;
	param +='&calledFrom='+"manualsweeping";
	openWindow(param,'sweepdetailsWindow','left=50,top=190,width=1021,height=655,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
	return false;
}

function checkSweep(entityCode, selectedList) {
	var oXMLHTTP = new XMLHttpRequest();
	var sURL=requestURL + appName+"/sweep.do?method=checkSweep";
	sURL = sURL + "&entityCode="+entityCode;
	sURL = sURL + "&selectedList="+selectedList;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();

	var str=new String(oXMLHTTP.responseText);
	return str;
}

function getAccess(entityId, selectedList)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName +"/sweep.do?method=checkAccess"
	sURL= sURL+"&selectedList="+selectedList;
	sURL = sURL + "&entityId=" + entityId;
	sURL = sURL + "&status=Sweeping";
	oXMLHTTP.open( "POST", sURL, false );
	//sending request
	oXMLHTTP.send();
	//getting response
	sweepAccess=oXMLHTTP.responseText;
	return sweepAccess;
}

function updateData(isEntityChanged){
	Main.updateData(isEntityChanged);
}

</script>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="<fmt:message key="manSweeping.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>