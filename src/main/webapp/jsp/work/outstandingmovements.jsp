<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="openQueue.screen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<STYLE type="text/css">
<!--
A:link {
	text-decoration: none;
}

A:visited {
	text-decoration: none;
}
-->
</STYLE>
<SCRIPT language="JAVASCRIPT">
// Added for mantis 1967 by KaisBS: Improving && integrating SWFProfiler tool on all Flex screens
function returnSwfProfEnabValue(){
	return window.opener.returnSwfProfEnabValue();
}

var closeFlag=true;
var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];
var selectedtab = initialtab[1];
var previoustab=""
var dynamicTab = true;
// variable to hold dateSelected flag
var dateSelected = false;
// variable to get the date format value
var dateFormatValue = '${fn:escapeXml(sessionScope.CDM.dateFormatValue)}';

// variable to get the date format
var dateFormat = '${fn:escapeXml(sessionScope.CDM.dateFormat)}';
// Initialize the CalendarPopup
var cal = new CalendarPopup("caldiv",true);
// set the offset values
cal.offsetX = 22;
cal.offsetY = 0;
// varible to hold the date when click the calender date
var valueDateInForm = "";
// variable to hold the today date
var today = "${requestScope.today}";
var lastRefTime = "${requestScope.lastRefTime}";
var todaySysDate = "${requestScope.todaySysDate}";
var todaySysDatePlusOne = "${requestScope.todaySysDatePlusOne}";
var todaySysDatePlusTwo = "${requestScope.todaySysDatePlusTwo}";
var todaySysDatePlusThree = "${requestScope.todaySysDatePlusThree}";
var todaySysDatePlusFour = "${requestScope.todaySysDatePlusFour}";
var todaySysDatePlusFive = "${requestScope.todaySysDatePlusFive}";
var todaySysDatePlusSix = "${requestScope.todaySysDatePlusSix}";
//variable to get the database date calculated based on entity offset
var dbDate="${requestScope.dbDate}";

// varibale to hold the tab names
var tabNames = new Array( 'OutStandingMovementTodayParent','OutStandingMovementTodayPlusOneParent','OutStandingMovementTodayPlusTwoParent','OutStandingMovementTodayPlusThreeParent',
				'OutStandingMovementTodayPlusFourParent','OutStandingMovementTodayPlusFiveParent','OutStandingMovementTodayPlusSixParent','OutStandingMovementAllParent','OutStandingMovementSelectedParent');
// variable to hold the flag value
var alertFlag = "false";
var sortFilterStatusStr;
var colDataType;
var xl;
var isRefresfFromChild = "false";
var isDoNotCloseMyChilds = false;
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
//Variable to hold  existing entity.This variable will be used to set the  default date tab if entity getting changed
var existingEntity = "${requestScope.existingEntityId}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function unloadOutStandingMovementWindow()
{

	if(isRefresfFromChild != "false")
	{
		isDoNotCloseMyChilds = true;
	}

	unloadMyWindow();
}

function call2(){

	if(isRefresfFromChild == "false")
	{

		call();
	}
}
var x5;
function updateColors5()
{
	var rows = x5.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x5.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}
}

var x2;

function updateColors2()
{
	var rows = x2.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x2.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}
}

var x3;

function updateColors3()
{
	var rows = x3.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x3.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}
}

var x4;
function updateColors4(){
	var rows = x4.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x4.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "odd" : "even");
			addClassName(rows[i], count % 2 ? "even" : "odd");
			count++;
		}
	}
}

function tooltipDate() {
	if (dateFormat == 'datePat2') {
		document.getElementById("movement.valueDateAsString").title="<fmt:message key="tooltip.ValueDateMMDDYY"/>";
	}else{
		document.getElementById("movement.valueDateAsString").title="<fmt:message key="tooltip.enterValueDate"/>";
	}
}
function bodyOnLoad()
{

	adjustTableWidth("table_1","OutStandingMovementDetailsToday","ddscrolltable","OutStandingMovementToday1");


	x5 = new XLSheet("OutStandingMovementDetailsToday","table_1", colDataType, sortFilterStatusStr);
	x5.onsort = x5.onfilter = updateColors5;

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";

	document.getElementById("entityName").innerText = '${entityName}';

	var entitydropBox = new SwSelectBox(document.forms[0].elements["movement.id.entityId"],document.getElementById("entityName"));

	new SwSelectBox(document.forms[0].elements["movement.currencyGrpId"],document.getElementById("currGrpName"));

	window.onbeforeunload = unloadOutStandingMovementWindow;


    document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";

	// set the valueDateInForm values
	valueDateInForm = document.forms[0].elements['movement.valueDateAsString'].value;
	// If SelectedTab is All, disabled the selected tab
	if (getSelectedTabIndex() == "8"){
		selectedTab.disabled = true;
		document.forms[0].elements["movement.valueDateAsString"].value = "";
	}
	// set the selectedValueDate value
	document.forms[0].selectedValueDate.value = document.forms[0].elements["movement.valueDateAsString"].value;
	document.getElementById("lastRefTime").innerText = lastRefTime;
	tooltipDate();
}




function onSelectTableRow(rowElement)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedCurrencyCode.value = hiddenElement.value;
}

function showEntityDropDown()
{
	document.getElementById("dropdowndiv_1").style.visibility="visible";
}
function submitForm(methodName){
 document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
 	// if dateSelected true, empty the tabindex && tabname value
	if (dateSelected == false){
		setTabInfo();
	}
	else{
		document.forms[0].elements["selectedTabIndex"].value = "";
		document.forms[0].elements["selectedTabName"].value = "";
	}
	// set the selectedValueDate value
	if (document.forms[0].elements["movement.valueDateAsString"].value != "")
		document.forms[0].selectedValueDate.value = document.forms[0].elements["movement.valueDateAsString"].value;
	document.forms[0].method.value = methodName;
	document.forms[0].elements["dbDate"].value = dbDate;
	//store current entity  id into form elements so as the value will be checked at action layer for date with entityoffset calcuations
	document.forms[0].elements["existingEntityId"].value = existingEntity;

	//To prevent closing the child window on autoRefresh
	isDoNotCloseMyChilds= true;
	document.forms[0].submit();
}

function refreshAfterApplyCurrencyThreshold(methodName){
 document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	setTabInfo();
	document.forms[0].method.value = methodName;
	document.forms[0].applyCurrencyThresholdInd.value = "1";
	// set the date selected value
	if (document.forms[0].elements["movement.valueDateAsString"].value != "")
		document.forms[0].selectedValueDate.value = document.forms[0].elements["movement.valueDateAsString"].value;
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";
	document.forms[0].elements["dbDate"].value = dbDate;
	isDoNotCloseMyChilds = true;
	document.forms[0].submit();
}

function setTabInfo()
{
	document.forms[0].elements["selectedTabIndex"].value = getSelectedTabIndex();
	document.forms[0].elements["selectedTabName"].value = getselectedtab();
}
function clickLink(element,e)
{
	var event = (window.event|| e);
	document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.forms[0].workflow.value = "${requestScope.workflow}";

	var sURL = element.href;
	sURL = sURL +"&menuAccessId="+document.forms[0].menuAccessId.value;


	sURL = sURL +"&applyCurrencyThreshold="+document.forms[0].applyCurrencyThreshold.value+"&workflow="+document.forms[0].workflow.value;
	openWindow(sURL,'addpositionlevelWindow','left=50,top=190,width=1350,height=680,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
    if(event.preventDefault) {
        event.preventDefault();
    }else{
        window.event.returnValue = false;
    }
	return false;
}


function adjustTableWidth(tableHeader, maintable, scrollTable, divId  ){
	var dynamicCols = '${dynamicColsCount}';
	var tableHeaderObj =  document.getElementById(tableHeader);
	var tableObj = document.getElementById(maintable);
	var tableHeaderWidth = 350;
	var tableObjWidth = 350;
	sortFilterStatusStr = "22";
	colDataType = new Array(dynamicCols);
	colDataType[0] = "String";
	colDataType[1] = "String";
	for( var i = 0 ;i < dynamicCols ; i++) {
		tableObjWidth += 143;
		tableHeaderWidth += 143;
		sortFilterStatusStr += "2";
		colDataType[i+2] = "Number";
	}
	var tfoot = tableObj.getElementsByTagName("tfoot")[0];
	var row = tfoot.getElementsByTagName("tr")[0];
	var col = row.getElementsByTagName("td")[0]

	col.colSpan = parseInt(dynamicCols) + 2 ;
	tableHeaderObj.width = tableHeaderWidth;

	tableObj.width = tableObjWidth;

	document.getElementById(scrollTable).style.backgroundColor = 'white';
	document.getElementById(divId).style.backgroundColor = 'white';



}

//This function is called when Today date tab is pressed.

function checkTabIndexToday(obj) {
	changeselected('OutStandingMovementTodayParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";

	if(tabIndex != originalTabIndex) {
		expandcontent('OutStandingMovementTodayParent', obj);
		// set the valueDateAsString value
		document.forms[0].elements["movement.valueDateAsString"].value = todaySysDate;
		submitForm('refreshOpenMovement');
		}
 }

 //This function is called when TodayPlusOne date tab is pressed.

function checkTabIndexTodayPlusOne(obj) {
	changeselected('OutStandingMovementTodayPlusOneParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";

	if(tabIndex != originalTabIndex) {
			expandcontent('OutStandingMovementTodayParent', this);
			// set the valueDateAsString value
			document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusOne;
			submitForm('refreshOpenMovement' );
		}
}


//This function is called when TodayPlusTwo date tab is pressed.

function checkTabIndexTodayPlusTwo(obj) {
	changeselected('OutStandingMovementTodayPlusTwoParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";

	if(tabIndex != originalTabIndex) {
			expandcontent('OutStandingMovementTodayParent', this);
			// set the valueDateAsString value
			document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusTwo;
			submitForm('refreshOpenMovement');
			}
}

//This function is called when TodayPlusThree date tab is pressed.
function checkTabIndexTodayPlusThree(obj) {
changeselected('OutStandingMovementTodayPlusThreeParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	if(tabIndex != originalTabIndex) {
		expandcontent('OutStandingMovementTodayParent', this);
		document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusThree;
		submitForm('refreshOpenMovement',tabIndex);
	}
}

//This function is called when TodayPlusFour date tab is pressed.
function checkTabIndexTodayPlusFour(obj) {
changeselected('OutStandingMovementTodayPlusFourParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	if(tabIndex != originalTabIndex) {
		expandcontent('OutStandingMovementTodayParent', this);
		document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusFour;
		submitForm('refreshOpenMovement',tabIndex);
	}
}

//This function is called when TodayPlusFive date tab is pressed.
function checkTabIndexTodayPlusFive(obj) {
changeselected('OutStandingMovementTodayPlusFiveParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	if(tabIndex != originalTabIndex) {
		expandcontent('OutStandingMovementTodayParent', this);
		document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusFive;
		submitForm('refreshOpenMovement',tabIndex);
	}
}

//This function is called when TodayPlusSix date tab is pressed.
function checkTabIndexTodayPlusSix(obj) {
changeselected('OutStandingMovementTodayPlusSixParent');
	var tabIndex = getSelectedTabIndex();
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	if(tabIndex != originalTabIndex) {
		expandcontent('OutStandingMovementTodayParent', this);
		document.forms[0].elements["movement.valueDateAsString"].value = todaySysDatePlusSix;
		submitForm('refreshOpenMovement',tabIndex);
	}
}

//This function is called when 'All' tab is pressed.
function clickAllTab(object)
{
	var originalTabIndex = "${requestScope.selectedTabIndex}";
	changeselected('OutStandingMovementAllParent');
	var tabIndex = getSelectedTabIndex();
	if(originalTabIndex != tabIndex)
	{
		expandcontent('OutStandingMovementTodayParent', object);
		document.forms[0].elements["movement.valueDateAsString"].value ="";
		submitForm("refreshOpenMovement");
	}


}

//This function is called when 'Selected' tab is pressed.
function clickSelectedTab(object)
{
	if(document.forms[0].elements['movement.valueDateAsString'].value == "")	{
		alert('<fmt:message key="accountMonitorNew.alert.date"/>');
		return false

	} else {
		var originalTabIndex = "${requestScope.selectedTabIndex}";
		changeselected('OutStandingMovementSelectedParent');
		var tabIndex = getSelectedTabIndex();
		if(originalTabIndex != tabIndex)
	    {
		    expandcontent('OutStandingMovementTodayParent', object);
			submitForm("refreshOpenMovement");
		}
	}

}

var dateNewFlag = true;
/**
*  This function is called when theris is change in date feild
*  this funtion also validates the date field && submits the form
*  @param obj
*  @returns boolean
*/
function onDateChange(obj,e){
	//check for whether the validation is already done
	if(dateNewFlag){
		//date validation
		return dateForValidation(e);
	} else {
		dateNewFlag = true;
	}

}


/**
*  This function is called when calender key press for validating the date feild in editable mode.
*  @param obj
*  @returns boolean
*/
function onDateKeyPress(obj,e)	{
	var event = (window.event|| e);
	//check for tab key
	if(event.keyCode == 9 && alertFlag != "false"){
		//date validation
		return dateForValidation(event);
	}
	//check for enter key
	if(event.keyCode == 13){
		dateNewFlag = false;
		//date validation
		return dateForValidation(event);
	}
}

/**
 *  This function is used to validate the date
 *  @returns boolean
 */
function dateForValidation(e){

	var bodyrect = window.document.body.getClientRects()[0];
	if (!(e.y < 0 || e.x > bodyrect.right) && closeFlag==true){
		//validate date feild
		if(document.forms[0].elements['movement.valueDateAsString'].value != "" && validateField(document.forms[0].elements['movement.valueDateAsString'],'date',dateFormat)){
			var dateNew = document.forms[0].elements['movement.valueDateAsString'].value;
			//changing the date format
			if(dateFormat == "datePat2"){
				var valueDate = new Array();
				valueDate = dateNew.split("/");
				var temp = valueDate[0];
				valueDate[0] = valueDate[1];
				valueDate[1] = temp;
				dateNew = valueDate.join("/");
			}
			var days2 = (parseDate(dateNew,dateFormat).getTime())/(60*60*1000);
			//get the today date
			var days1 = (parseDate(today,dateFormat).getTime())/(60*60*1000);
			//Check for wehether the selected date exceeds the range 30 days from the todays date
			// Modified by Vivekanandan for issue reported in beta 6 in mantis 1863 for DST issue
			if(Math.round(((days2-days1))/24) > 30){
				if (alertFlag == "false"){
					alert('<fmt:message key="accountMonitorNew.alert.exceedDate"/>');
				}else {
					alertFlag = "false";
				}
				document.forms[0].elements['movement.valueDateAsString'].value=valueDateInForm;
				return false;
			} else {
				dateSelected = true;
				submitForm('refreshOpenMovement');
				return true;
			}
		} else {
			return false;
		}
	} else {
		closeFlag = true;

	}
}
</SCRIPT>


</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad();"
	onunload="call()">

<form action="outstandingmovement.do" method="post">
	<input name="method" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value="">
	<input name="selectedTabIndex" type="hidden" value="1">
	<input name="dbDate" type="hidden" value="">
	<input name="existingEntityId" type="hidden" value="">

	<input name="workflow" type="hidden" value="${requestScope.workflow}">

	<input name="selectedTabName" type="hidden"
		value="OutStandingMovementTodayParent">
	<input name="divWidth" type="hidden" value="">

	<input name="menuAccessId" type="hidden">

	<input name="applyCurrencyThreshold" type="hidden">
	<input name="applyCurrencyThresholdInd" type="hidden" value="0">
	<c:set var="CDM" value="${sessionScope.CDM}"/>
	<input name="selectedValueDate" type="hidden" value="">
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<div id="OutStandingMovement"
		style="position: absolute; left: 20px; top: 20px; width: 1216px; height: 61px; border: 2px outset;"
		color="#7E97AF">
	<div id="OutStandingMovement"
		style="position: absolute; left: 8px; top: 4px; width: 1215px; height: 38px;">
	<table width="1202px" border="0" cellpadding="0" cellspacing="0"
		height="50px">
		<tr height="25px">
			<td width="95px"><b><fmt:message key="entity.id"/></b></td>
			<!-- Start: Code modified by Vivekanandan A for mantis 1991 on 14-07-2012 -->
			<td width="150px"><select
					class="htmlTextAlpha"
					tabindex="1"
					name="movement.id.entityId"
					id="movement.id.entityId"
					title="<fmt:message key='tooltip.selectEntityId'/>"
					onchange="submitForm('displayOpenMovementCountByEntity')"
					style="width:150px">
					<c:forEach items="${requestScope.entities}" var="entity">
						<option value="${entity.value}"
						<c:if test="${movement.id.entityId == entity.value}">selected="selected"</c:if>
						>${entity.label}</option>
					</c:forEach>
				</select></td>
			<td width="400px"><span id="entityName" name="entityName"
				class="spantext"></td>
		</tr>

		<tr height="25px">
			<td width="125px"><b style="width: 110px; margin-bottom: 5px;"><fmt:message key="currMonitor.crrGrp"/></b></td>
			<td width="170px"><select
    class="htmlTextAlpha"
    tabindex="1"
    name="movement.currencyGrpId"
     titleKey="tooltip.selectCurrencyGrp"
    onchange="submitForm('displayOpenMovementCountByCurrency')"
    style="width: 150px; margin-bottom: 5px;">
    <c:forEach items="${requestScope.currencyGroupList}" var="item">
        <option value="${item.value}"
        <c:if test="${movement.currencyGrpId == item.value}">selected="selected"</c:if>
        >${item.label}</option>
    </c:forEach>
</select> </td>
			<td width="300px" style="padding-bottom: 5px;"><span id="currGrpName" name="currGrpName"
				class="spantext"></td>
			<td width="20px">&nbsp;</td>
			<td width="50px" style="padding-bottom: 5px;" ><b ><fmt:message key="date"/></b></td>
			<td width="2px">&nbsp;</td>
			<td width="300px"><input type="text"  name="movement.valueDateAsString" value="${movement.valueDateAsString}"  cssClass="htmlTextNumeric"
				maxlength="10" tabindex="2"
				onkeydown="return onDateKeyPress(this,event);"
				onblur="return onDateChange(this,event);" style="width: 80px; margin-bottom: 5px;height: 22px;" /> <A
				 id="datelink" tabindex="5"
				onClick="cal.select(document.forms[0].elements['movement.valueDateAsString'],'datelink',dateFormatValue);return false;"><img
				title='<fmt:message key="tooltip.selectDate"/>'
				src="images/calendar-16.gif"></A></td>
			<td width="220" style="padding-bottom: 5px; "><b ><fmt:message key="mvmt.applyCurrencyThreshold"/></b></td>
			<td width="25"><input type="checkbox"  name="movement.applyCurrencyThreshold" value="Y" ${requestScope.movement.applyCurrencyThreshold == 'Y' ? 'checked' : ''} fieldValue="Y"
				value='${requestScope.movement.applyCurrencyThreshold =="Y"}'
				style="width:13px;margin-bottom: 5px;" titleKey="tooltip.applyCurrencyThreshold"
				cssClass="htmlTextAlpha" tabindex="2"
				onclick="javascript:refreshAfterApplyCurrencyThreshold('refreshOpenMovement')" />
			</td>
		</tr>

	</table>
	</div>
	</div>
	<div id="ddimagetabs" style="position: absolute; left: 20px; top: 89px; width: 832px; height: 20px;">

    <c:if test="${requestScope.isBusinessDayForToday == true}">
        <a id="todayTab"
            onmouseout="revertback('OutStandingMovementTodayParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayParent',this)"
            tabindex="4" onClick="checkTabIndexToday(this);">
            <b>${requestScope.todayTablabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForToday != true}">
        <a id="todayTab"
            onmouseout="revertback('OutStandingMovementTodayParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayParent',this)"
            style="color: #A0A0A0" tabindex="4"
            onClick="checkTabIndexToday(this);">
            <b>${requestScope.todayTablabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusOne == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusOneParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusOneParent',this)"
            tabindex="5" onClick="checkTabIndexTodayPlusOne(this);">
            <b>${requestScope.todayTabPlusOnelabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusOne != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusOneParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusOneParent',this)"
            style="color: #A0A0A0" tabindex="5"
            onClick="checkTabIndexTodayPlusOne(this);">
            <b>${requestScope.todayTabPlusOnelabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusTwo == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusTwoParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusTwoParent',this)"
            tabindex="6" onClick="checkTabIndexTodayPlusTwo(this);">
            <b>${requestScope.todayTabPlusTwolabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusTwo != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusTwoParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusTwoParent',this)"
            style="color: #A0A0A0" tabindex="6"
            onClick="checkTabIndexTodayPlusTwo(this);">
            <b>${requestScope.todayTabPlusTwolabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusThree == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusThreeParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusThreeParent',this)"
            tabindex="7" onClick="checkTabIndexTodayPlusThree(this);">
            <b>${requestScope.todayTabPlusThreelabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusThree != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusThreeParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusThreeParent',this)"
            style="color: #A0A0A0" tabindex="7"
            onClick="checkTabIndexTodayPlusThree(this);">
            <b>${requestScope.todayTabPlusThreelabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusFour == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusFourParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusFourParent',this)"
            tabindex="8" onClick="checkTabIndexTodayPlusFour(this);">
            <b>${requestScope.todayTabPlusFourlabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusFour != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusFourParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusFourParent',this)"
            style="color: #A0A0A0" tabindex="8"
            onClick="checkTabIndexTodayPlusFour(this);">
            <b>${requestScope.todayTabPlusFourlabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusFive == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusFiveParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusFiveParent',this)"
            tabindex="9" onClick="checkTabIndexTodayPlusFive(this);">
            <b>${requestScope.todayTabPlusFivelabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusFive != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusFiveParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusFiveParent',this)"
            style="color: #A0A0A0" tabindex="9"
            onClick="checkTabIndexTodayPlusFive(this);">
            <b>${requestScope.todayTabPlusFivelabel}</b>
        </a>
    </c:if>

    <c:if test="${requestScope.isBusinessDayForTodayPlusSix == true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusSixParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusSixParent',this)"
            tabindex="10" onClick="checkTabIndexTodayPlusSix(this);">
            <b>${requestScope.todayTabPlusSixlabel}</b>
        </a>
    </c:if>
    <c:if test="${requestScope.isBusinessDayForTodayPlusSix != true}">
        <a onmouseout="revertback('OutStandingMovementTodayPlusSixParent',this);"
            onmouseover="changecontent('OutStandingMovementTodayPlusSixParent',this)"
            style="color: #A0A0A0" tabindex="10"
            onClick="checkTabIndexTodayPlusSix(this);">
            <b>${requestScope.todayTabPlusSixlabel}</b>
        </a>
    </c:if>

   <a id="allTab"
			onmouseout="revertback('OutStandingMovementAllParent',this);"
			onmouseover="changecontent('OutStandingMovementAllParent',this)"
			tabindex="11" onClick="clickAllTab(this);">
			<b><fmt:message key="addjob.All" /></b>
		</a>

		<a id="selectedTab"
			onmouseout="revertback('OutStandingMovementSelectedParent',this);"
			onmouseover="changecontent('OutStandingMovementSelectedParent',this)"
			tabindex="12" onClick="clickSelectedTab(this);">
			<b><fmt:message key="accountmonitor.selected" /></b>
		</a>

</div>
	<div id="Line"
		style="position: absolute; left: 349px; top: 107px; width: 865px; height: 8px;">
	<table width="595px">
		<tr>
			<td><img src="images/tabline.gif" width="883px" height="1"></td>
		</tr>
	</table>
	</div>
	<div id="tabcontentcontainer"
		style="position: absolute; left: 20px; top: 111px; width: 1215px; height: 460px;">
	<div id="OutStandingMovementTodayParent" class="tabcontent">
	<div id="OutStandingMovementToday"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1197px; height: 10px;">
	<table class="sort-table" id="table_1" bgcolor="#B0AFAF" border="0"
		cellspacing="1" cellpadding="0" height="25px">
		<thead>
			<tr>
				<td width="70px" style=" border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="movement.currency"/></b></td>
				<td width="280px"
					title='<fmt:message key="tooltip.sortCurrencyName"/>'><b><fmt:message key="movement.CcyName"/></b></td>
				<c:forEach items="${requestScope.entityPosNamesColl}" var="entityPosNamesColl">
					<td width="130px"><b>${entityPosNamesColl.posLevelName}</b>&nbsp;</td>
				</c:forEach>


			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 1215px; height: 458px; overflow: scroll">
	<div id="OutStandingMovementToday1"
		style="position: absolute; z-index: 99; left: 0px; top: 24px; width: 1221px; height: 10px;">
	<table class="sort-table" id="OutStandingMovementDetailsToday"
    border="0" cellspacing="1" cellpadding="0" height="416">
    <tbody>
        <c:forEach items="${requestScope.OutStandingMovementDetailsToday}"
            var="movement" varStatus="status">

            <tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">
                <input type="hidden" name="OutStandingMovementDetailsToday.currencyCode"
                    value="${movement.currencyCode}" />

                <td align="left" width="70px">
                    ${movement.currencyCode}&nbsp;
                </td>
                <td width="235px">
                    ${movement.currencyName}&nbsp;
                </td>

                <c:forEach items="${movement.posLvlNames}" var="posLevel" varStatus="colStatus">
                    <td align="right" width="130px">
                        <c:if test="${not empty posLevel.movementTotalAsString}">
                            <c:choose>
                                <c:when test="${colStatus.index == 0}">
                                    <a href="outstandingmovement.do?method=flexDisplayOpenMovements&initialinputscreen=E&totalFlag=Y&${posLevel.summaryTotalUrlParams}"
                                        onclick="clickLink(this,event);">
                                        ${posLevel.movementTotalAsString}
                                    </a>
                                </c:when>
                                <c:otherwise>
                                    <a href="outstandingmovement.do?method=flexDisplayOpenMovements&initialinputscreen=E&totalFlag=N&${posLevel.summaryTotalUrlParams}"
                                        onclick="clickLink(this,event);">
                                        ${posLevel.movementTotalAsString}
                                    </a>
                                </c:otherwise>
                            </c:choose>
                        </c:if>
                    </td>
                </c:forEach>
            </tr>
        </c:forEach>
    </tbody>
    <tfoot>
        <tr>
            <td></td>
        </tr>
    </tfoot>
</table>
	</div>
	</div>
	</div>

	</div>
	<div id="outStanding"
		style="position: absolute; left: 1140; top: 589; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="8" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Outstanding Movements'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " style="padding-left: 13px" name="Help" border="0"></a></td>
			<td align="right" id="Print"><a tabindex="8"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 580; width: 1216px; height: 39px; visibility: visible;">
	<div id="outStanding"
		style="position: absolute; left: 6; top: 4; width: 425px; height: 15px; visibility: visible;">
	<table width="140px" border="0" cellspacing="1" cellpadding="0"
		height="20">
		<tr>
			<td id="refreshbutton"><a
				title='<fmt:message key="tooltip.refreshWindow"/>' tabindex="6"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitForm('refreshOpenMovement');"><fmt:message key="button.Refresh"/></a></td>

			<td><a id="closebutton" title='<fmt:message key="tooltip.close"/>' tabindex="7"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this);closeFlag=false;"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
		</tr>
	</table>
	</div>
	<table height="35">
		<tr>
			<td id="lastRefTimeLable" width="1000px" align="right"><fmt:message key="label.lastRefTime"/></td>
			<td id="lastRefTime"><input class="textAlpha"
				style="background: transparent; border: 0;" tabindex="-1" readonly
				name="maxPageNo" value="" size="14"></td>
		</tr>
	</table>
	<div
		style="position: absolute; left: 6; top: 4; width: 705px; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="refreshdisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.Refresh"/></a></td>
	</table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>