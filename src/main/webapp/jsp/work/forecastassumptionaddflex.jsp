<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Forecast Assumption Add screen.
  - Author(s): Bala .D
  - Date: 08-06-2011
  -->
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
	<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<c:choose>
			<c:when test="${requestScope.screenName == 'addScreen'}">
				<title><fmt:message key="label.forecastAssumptionsAdd.title.window"/></title>
			</c:when>
			<c:otherwise>
				<title><fmt:message key="label.forecastAssumptionsChange.title.window"/></title>
			</c:otherwise>
		</c:choose>
		</head>
		<script type="text/javascript">
		var screenRoute = "forecastAssumptionAdd";
			// get the request variable values
			// get the entity id
			var entityId="${requestScope.entityId}";
			// get the assumption Id
			var assumptionId="${requestScope.assumptionId}";
			// get the currency code
			var currencyCode="${requestScope.currencyCode}";
			// get the currency name
			var currencyName="${requestScope.currencyName}";
			// get the template id
			var templateId="${requestScope.templateId}";
			// get the date
			var date="${requestScope.date}";
			// Start: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			// variable amount
			var amount="";
			// variable assumption
			var assumption = "";
			// End: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			// currency format
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';
			// set the label
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("forecastMonitor.do");
			// If parent refresh, close the window
			<c:if test="${requestScope.parentFormRefresh == 'Y'}">
				window.opener.refreshPending = true;
				self.close();
			</c:if>
			
			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			
			/**
             * sendRequest
             * This method is used to send the request
             * @param requestURL
 			 * @return Number
			 */		
			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();
				var count = new Number (oXHR.responseText);
				return count;
			}
			
			/**
             * formatCurrency
 			 * This method is used to format the amount in system currency format (called by flex)
 			 * @param amount
 			 * @return String 
			 */
			function formatCurrency(amount){
				var strAmount=formatCurrency_forecastMonitor(amount, currencyFormat, null);
				return strAmount;
			}
			
			/**
             * help
             * This function opens the help screen
 			 * @return none
			 */
			 function help(){
				openWindow(buildPrintURL('print','Forecast Monitor Assumptions Add'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
            
         	/**
             * closeWindow
             * This function used to close the window
 			 * @return none
			 */
            function closeWindow() {
				window.close();
			}      
			
			/**
             * unloadCloseWindow
             * This function is called, when unload the window
 			 * @return none
			 */
			function unloadCloseWindow(){
				window.close();
				window.opener.reloadAssumption();
			}

			// Start: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen
			/**
             * init
             * This method is called when onload and set the assumption & amount data
 			 * @return none
			 */
			function init(){
				assumption = window.opener.assumption;
				amount = window.opener.amount;
			}
			// End: code added by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen

			            			
		</script>
    <%@ include file="/angularscripts.jsp"%>
	<!-- Start: Code modified by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen -->
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="init();setParentChildsFocus();" >
	<!-- End: Code modified by Bala on 29-Aug-2011 for Issues found on 1053 Beta testing - create Assumption with Spl characters, Amount and description not displayed in the change Assumption screen -->
		<form id="exportDataForm" target="tmp" method="post">
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>