<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<c:set var="recordCount" value="${requestScope.forecastAssumption.assumptionList.size()}"/>
<assumption currencyFormat="${requestScope.currencyFormat}">
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<grid>
		<metadata>
			<columns>
				<column
						heading="<fmt:message key="label.forecastMonitor.entity"/>"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="entity"
						width="120"
						sort="true"
				/>
				<column
						heading="<fmt:message key="label.forecastMonitorGrid.currency"/>"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="currency"
						width="70"
						sort="true"
				/>
				<column
						heading="<fmt:message key="label.forecastAssumptionAdd.date"/>"
						draggable="false"
						filterable="true"
						type="num"
						dataelement="date"
						width="90"
						sort="true"
				/>
				<column
						heading="<fmt:message key="label.forecastAssumptionAdd.amount"/>"
						draggable="false"
						filterable="true"
						type="num"
						dataelement="amount"
						width="185"
						sort="true"
				/>
				<column
						heading="<fmt:message key="label.forecastAssumptionAdd.assumption"/>"
						draggable="false"
						filterable="true"
						type="num"
						dataelement="assumption"
						width="185"
						sort="true"
				/>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach items="${requestScope.forecastAssumption.assumptionList}" var="assumptionData">
				<row>
					<id clickable="false">${assumptionData.assumptionId}</id>
					<entity clickable="false">${assumptionData.entityId}</entity>
					<currency clickable="false">${assumptionData.currencyCode}</currency>
					<date clickable="false">${assumptionData.valueDateAsString}</date>
					<assumption clickable="false">${assumptionData.assumption}</assumption>
					<amount clickable="false">${assumptionData.assumptionsAmountAsString}</amount>
				</row>
			</c:forEach>
		</rows>
	</grid>
</assumption>