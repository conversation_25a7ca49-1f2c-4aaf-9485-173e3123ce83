<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="java.util.*"%>
<%@ page import="org.swallow.util.OpTimer"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.work.model.Movement"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>




<movementsummarydisplay
		access="<c:out value='${requestScope.access == "readOnly" ? "readOnly" : ""}'/>"
		currencythreshold="<c:out value='${requestScope.applyCurrencyThreshold == "Y" ? "true" : "false"}'/>"
		currencyFormat="<c:out value='${requestScope.currencyFormat}'/>"
		selectedFilter="<c:out value='${requestScope.selectedFilter}'/>"
		selectedSort="<c:out value='${requestScope.selectedSort}'/>"
		lastRefTime="<c:out value='${requestScope.lastRefTime}'/>"
		initialinputscreen="<c:out value='${requestScope.initialinputscreen}'/>"
		filterAcctType="<c:out value='${requestScope.filterAcctType}'/>"
		totalOverPages="<c:out value='${requestScope.totalOverPages}'/>"
		totalInPage="<c:out value='${requestScope.totalInPage}'/>"
		currfontsize="<c:out value='${requestScope.fontSize}'/>"
		currentProfile="<c:out value='${requestScope.currentProfile}'/>"
		useAddCols="<c:out value='${requestScope.useAddCols}'/>"
		profileAddCols="<c:out value='${requestScope.profileAddCols}'/>"
		profileAddColsData="<c:out value='${requestScope.profileAddCols}'/>"
		profileAddColsList="<c:out value='${requestScope.profileAddColsList}'/>"
		addColsForFilter="<c:out value='${requestScope.addColsForFilter}'/>"
		nodata="<c:out value='${requestScope.nodata}'/>"
		currentFilterConf="<c:out value='${requestScope.currentFilterConf}'/>"
		openMovementFlagSearch="<c:out value='${requestScope.openMovementFlagSearch}'/>">

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">
				<c:out value="${opTime.value}" />
			</operation>
		</c:forEach>
	</timing>

	<pages
			maxPage="${requestScope.maxPage}"
			currentPage="${requestScope.currentPage}"
			accountTypeAccount="${requestScope.accountType}"
			balanceTypeAccount="${requestScope.balanceType}"
			accountIdSearch="${requestScope.accountId}"
			memoryErrorFlag="${sessionScope.outofMemory}">

		<c:forEach items="${requestScope.pageSummaryList}" var="pageSummaryListLocal" begin="0" end="11">
			<c:choose>
				<c:when test="${pageSummaryListLocal.pageNo == -2}">
					<prev enabled="${requestScope.prevEnabled == 'true'}" />
				</c:when>
				<c:when test="${pageSummaryListLocal.pageNo == -1}">
					<next enabled="${requestScope.nextEnabled == 'true'}" />
				</c:when>
				<c:otherwise>
					<page id="${pageSummaryListLocal.pageNo}" enabled="${requestScope.currentPage != pageSummaryListLocal.pageNo}" />
				</c:otherwise>
			</c:choose>
		</c:forEach>
	</pages>

	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order}" var="order">
					<c:choose>
						<c:when test="${order == 'alerting'}">
							<column heading=""
									tooltip="<fmt:message key='tooltip.movement.alerting'/>"
									draggable="false" filterable="false" columnNumber="27" type="str"
									sort="true" dataelement="alerting"
									width="${requestScope.column_width['alerting']}" />
						</c:when>

						<c:when test="${order == 'pos'}">
							<column heading="<fmt:message key='movement.position'/>"
									tooltip="<fmt:message key='tooltip.movement.position'/>"
									draggable="false" filterable="true" columnNumber="0" type="str"
									sort="true" dataelement="pos"
									width="${requestScope.column_width['pos']}" />
						</c:when>

						<c:when test="${order == 'entity'}">
							<column heading="<fmt:message key='movement.id.entityId'/>"
									tooltip="<fmt:message key='tooltip.movement.entityId'/>"
									draggable="true" filterable="true" columnNumber="4" type="str"
									sort="true" dataelement="entity"
									width="${requestScope.column_width['entity']}" />
						</c:when>

						<c:when test="${order == 'value'}">
							<c:choose>
								<c:when test="${requestScope.method != 'search'}">
									<column heading="<fmt:message key='movement.value'/>"
											tooltip="<fmt:message key='tooltip.movement.value'/>"
											draggable="true" filterable="false" columnNumber="1" type="num"
											sort="true" dataelement="value"
											width="${requestScope.column_width['value']}" />
								</c:when>
								<c:otherwise>
									<column heading="<fmt:message key='movement.value'/>"
											tooltip="<fmt:message key='tooltip.movement.value'/>"
											draggable="true" filterable="true" columnNumber="1" type="num"
											sort="true" dataelement="value"
											width="${requestScope.column_width['value']}" />
								</c:otherwise>
							</c:choose>
						</c:when>

						<c:when test="${order == 'amount'}">
							<column heading="<fmt:message key='movement.amount1'/>"
									tooltip="<fmt:message key='tooltip.movement.amount1'/>"
									draggable="true" filterable="false" columnNumber="2"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="amount"
									width="${requestScope.column_width['amount']}" />
						</c:when>

						<c:when test="${order == 'sign'}">
							<column heading="<fmt:message key='movement.sign'/>"
									tooltip="<fmt:message key='tooltip.movement.sign'/>"
									draggable="true" filterable="true" columnNumber="3" type="str" sort="true"
									dataelement="sign"
									width="${requestScope.column_width['sign']}" />
						</c:when>
						<c:when test="${order == 'ccy'}">
							<column heading="<fmt:message key='movement.ccy'/>"
									tooltip="<fmt:message key='tooltip.movement.ccy'/>"
									draggable="true" filterable="true" columnNumber="5"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ccy"
									width="${requestScope.column_width['ccy']}" />
						</c:when>
						<c:when test="${order == 'ref1'}">
							<column heading="<fmt:message key='movement.reference1'/>"
									tooltip="<fmt:message key='tooltip.movement.reference1'/>"
									draggable="true" filterable="true" columnNumber="6"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ref1"
									width="${requestScope.column_width['ref1']}" />
						</c:when>
						<c:when test="${order == 'account'}">
							<column heading="<fmt:message key='movement.account'/>"
									tooltip="<fmt:message key='tooltip.movement.account'/>"
									draggable="true" filterable="true" columnNumber="7"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="account"
									width="${requestScope.column_width['account']}" />
						</c:when>
						<c:when test="${requestScope.method != 'search' && order == 'input'}">
							<column heading="<fmt:message key='movement.input'/>"
									tooltip="<fmt:message key='tooltip.movement.input'/>"
									draggable="true" filterable="false" columnNumber="8"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="input"
									width="${requestScope.column_width['input']}" />
						</c:when>
						<c:when test="${requestScope.method == 'search' && order == 'input'}">
							<column heading="<fmt:message key='movement.input'/>"
									tooltip="<fmt:message key='tooltip.movement.input'/>"
									draggable="true" filterable="true" columnNumber="8"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="input"
									width="${requestScope.column_width['input']}" />
						</c:when>
						<c:when test="${order == 'cparty'}">
							<column heading="<fmt:message key='movement.counterPartyId'/>"
									tooltip="<fmt:message key='tooltip.movement.counterPartyId'/>"
									draggable="true" filterable="true" columnNumber="9"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="cparty"
									width="${requestScope.column_width['cparty']}" />
						</c:when>
						<c:when test="${order == 'pred'}">
							<column heading="<fmt:message key='movement.pred'/>"
									tooltip="<fmt:message key='tooltip.movement.pred'/>"
									draggable="true" filterable="true" columnNumber="10"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="pred"
									width="${requestScope.column_width['pred']}" />
						</c:when>
						<c:when test="${order == 'ext'}">
							<column heading="<fmt:message key='label.movement.externalBalanceStatus'/>"
									tooltip="<fmt:message key='tooltip.movement.externalBalanceStatus'/>"
									draggable="true" filterable="true" columnNumber="11"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ext"
									width="${requestScope.column_width['ext']}" />
						</c:when>
						<c:when test="${order == 'status'}">
							<column heading="<fmt:message key='movement.status'/>"
									tooltip="<fmt:message key='tooltip.movement.status'/>"
									draggable="true" filterable="true" columnNumber="12" sort="true"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" dataelement="status"
									width="${requestScope.column_width['status']}" />
						</c:when>
						<c:when test="${order == 'matchid'}">
							<column heading="<fmt:message key='movement.matchId'/>"
									tooltip="<fmt:message key='tooltip.movement.matchId'/>"
									draggable="true" filterable="true" columnNumber="13"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="matchid"
									width="${requestScope.column_width['matchid']}" />
						</c:when>
						<c:when test="${order == 'source'}">
							<column heading="<fmt:message key='movement.source'/>"
									tooltip="<fmt:message key='tooltip.movement.source'/>"
									draggable="true" filterable="true" columnNumber="14"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="source"
									width="${requestScope.column_width['source']}" />
						</c:when>
						<c:when test="${order == 'format'}">
							<column heading="<fmt:message key='movement.msgformat'/>"
									tooltip="<fmt:message key='tooltip.movement.msgformat'/>"
									draggable="true" filterable="true" columnNumber="15"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="format"
									width="${requestScope.column_width['format']}" />
						</c:when>
						<c:when test="${order == 'notes'}">
							<column heading="<fmt:message key='movement.notes'/>"
									tooltip="<fmt:message key='tooltip.movement.notes'/>"
									draggable="true" filterable="false" columnNumber="16"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="false" dataelement="notes"
									width="${requestScope.column_width['notes']}" />
						</c:when>
						<c:when test="${order == 'beneficiary'}">
							<column heading="<fmt:message key='movement.beneficiary'/>"
									tooltip="<fmt:message key='tooltip.movement.beneficiary'/>"
									draggable="true" filterable="true" columnNumber="17"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="beneficiary"
									width="${requestScope.column_width['beneficiary']}" />
						</c:when>
						<c:when test="${order == 'ref2'}">
							<column heading="<fmt:message key='movement.reference2'/>"
									tooltip="<fmt:message key='tooltip.movement.reference2'/>"
									draggable="true" filterable="true" columnNumber="18"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ref2"
									width="${requestScope.column_width['ref2']}" />
						</c:when>
						<c:when test="${order == 'ref3'}">
							<column heading="<fmt:message key='movement.reference3'/>"
									tooltip="<fmt:message key='tooltip.movement.reference3'/>"
									draggable="true" filterable="true" columnNumber="19"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ref3"
									width="${requestScope.column_width['ref3']}" />
						</c:when>
						<c:when test="${order == 'movement'}">
							<column heading="<fmt:message key='movement.movementId'/>"
									tooltip="<fmt:message key='tooltip.movement.movementId'/>"
									draggable="true" filterable="true" columnNumber="20"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="movement"
									width="${requestScope.column_width['movement']}" />
						</c:when>
						<c:when test="${order == 'book'}">
							<column heading="<fmt:message key='movement.bookcode'/>"
									tooltip="<fmt:message key='tooltip.movement.bookcode'/>"
									draggable="true" filterable="true" columnNumber="21"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="book"
									width="${requestScope.column_width['book']}" />
						</c:when>
						<c:when test="${order == 'custodian'}">
							<column heading="<fmt:message key='movement.custodian'/>"
									tooltip="<fmt:message key='tooltip.movement.custodian'/>"
									draggable="true" filterable="true" columnNumber="22"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="custodian"
									width="${requestScope.column_width['custodian']}" />
						</c:when>
						<c:when test="${order == 'xref'}">
							<column heading="<fmt:message key='movement.extraRef'/>"
									tooltip="<fmt:message key='tooltip.movement.extraRef'/>"
									draggable="true" filterable="true" columnNumber="23"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="xref"
									width="${requestScope.column_width['xref']}" />
						</c:when>
						<c:when test="${order == 'uetr'}">
							<column heading="<fmt:message key='movement.uetr'/>"
									tooltip="<fmt:message key='tooltip.movement.uetr'/>"
									draggable="true" filterable="true" columnNumber="24"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="uetr"
									width="${requestScope.column_width['uetr']}" />
						</c:when>
						<c:when test="${order == 'update_date'}">
							<column heading="<fmt:message key='movement.update_date'/>"
									tooltip="<fmt:message key='tooltip.movement.update_date'/>"
									draggable="true" filterable="false" columnNumber="25"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="update_date"
									width="${requestScope.column_width['update_date']}" />
						</c:when>
						<c:when test="${order == 'matchingparty'}">
							<column heading="<fmt:message key='movement.matchingParty'/>"
									tooltip="<fmt:message key='tooltip.movement.matchingParty'/>"
									draggable="true" filterable="true" columnNumber="26"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="matchingparty"
									width="${requestScope.column_width['matchingparty']}" />
						</c:when>
						<c:when test="${order == 'producttype'}">
							<column heading="<fmt:message key='movement.productType'/>"
									tooltip="<fmt:message key='tooltip.movement.productType'/>"
									draggable="true" filterable="true" columnNumber="27"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="producttype"
									width="${requestScope.column_width['producttype']}" />
						</c:when>
						<c:when test="${order == 'postingdate'}">
							<column heading="<fmt:message key='movement.postingDateMSD'/>"
									tooltip="<fmt:message key='tooltip.movement.postingDate'/>"
									draggable="true" filterable="true" columnNumber="28"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="num" sort="true" dataelement="postingdate"
									width="${requestScope.column_width['postingdate']}" />
						</c:when>
						<c:when test="${order == 'extra_text1'}">
							<column heading="<fmt:message key='movement.extraText1'/>"
									tooltip="<fmt:message key='tooltip.movement.extra_text1'/>"
									draggable="true" filterable="true" columnNumber="29"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="extra_text1"
									width="${requestScope.column_width['extra_text1']}" />
						</c:when>
						<c:when test="${order == 'ilmfcast'}">
							<column heading="<fmt:message key='movement.ilmFcast'/>"
									tooltip="<fmt:message key='tooltip.movement.ilmFcast'/>"
									draggable="true" filterable="true" columnNumber="30"
									balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
									type="str" sort="true" dataelement="ilmfcast"
									width="${requestScope.column_width['ilmfcast']}" />
						</c:when>

					</c:choose>
				</c:forEach>


				<c:if test="${requestScope.useAddCols == 'Y'}">
					<c:forEach var="entry" items="${requestScope.profileAddCols}">
						<c:if test="${!requestScope.column_order.contains(entry.sequenceKey)}">
							<column
									heading="${entry.label}"
									draggable="true"
									filterable="true"
									tooltip="${entry.msdDisplayColumns.columnName}"
									visible="false"
									balancetype="${SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED}"
									type="${entry.msdDisplayColumns.dataType}"
									sort="true"
									dataelement="${entry.sequenceKey}"
									width="${requestScope.column_width[entry.sequenceKey]}"
							/>
						</c:if>
					</c:forEach>
				</c:if>

			</columns>
		</metadata>

		<rows size="${totalCount}">
			<c:forEach items="${requestScope.OutStandingMovementSummaryDetails}" var="OutStandingMovementSummaryDetails">
				<row>
					<pos clickable="false" positionlevel="${OutStandingMovementSummaryDetails.positionLevel}">
							${OutStandingMovementSummaryDetails.positionLevelName}
					</pos>
					<entity clickable="false">
							${OutStandingMovementSummaryDetails.id.entityId}
					</entity>
					<value clickable="false">
							${OutStandingMovementSummaryDetails.valueDateAsString}
					</value>
					<amount negative="${OutStandingMovementSummaryDetails.sign == 'C' ? 'false' : 'true'}"
							clickable="false">
							${OutStandingMovementSummaryDetails.amountAsString}
					</amount>
					<sign clickable="false">
							${OutStandingMovementSummaryDetails.sign}
					</sign>
					<ccy clickable="false">${OutStandingMovementSummaryDetails.currencyCode}</ccy>
					<ref1 clickable="false">${OutStandingMovementSummaryDetails.reference1}</ref1>
					<account clickable="false">${OutStandingMovementSummaryDetails.accountId}</account>
					<input clickable="false">${OutStandingMovementSummaryDetails.inputDateAsString}</input>
					<cparty clickable="false">${OutStandingMovementSummaryDetails.counterPartyId}</cparty>
					<pred clickable="false">${OutStandingMovementSummaryDetails.predictStatus}</pred>
					<ext clickable="false">${OutStandingMovementSummaryDetails.extBalStatus}</ext>
					<status clickable="false">${OutStandingMovementSummaryDetails.matchStatusDesc}</status>
					<matchid clickable="false">${OutStandingMovementSummaryDetails.matchId}</matchid>
					<source clickable="false">${OutStandingMovementSummaryDetails.inputSource}</source>
					<format clickable="false">${OutStandingMovementSummaryDetails.messageFormat}</format>
					<notes clickable="false">${OutStandingMovementSummaryDetails.hasNotes}</notes>
					<beneficiary clickable="false">${OutStandingMovementSummaryDetails.beneficiaryId}</beneficiary>
					<ref2 clickable="false">${OutStandingMovementSummaryDetails.reference2}</ref2>
					<ref3 clickable="false">${OutStandingMovementSummaryDetails.reference3}</ref3>
					<movement clickable="false">${OutStandingMovementSummaryDetails.id.movementId}</movement>
					<book clickable="false">${OutStandingMovementSummaryDetails.bookCode}</book>
					<custodian clickable="false">${OutStandingMovementSummaryDetails.custodianId}</custodian>
					<xref clickable="false">${OutStandingMovementSummaryDetails.reference4}</xref>
					<update_date clickable="false">${OutStandingMovementSummaryDetails.updateDateAsString}</update_date>
					<matchingparty clickable="false">${OutStandingMovementSummaryDetails.matchingParty}</matchingparty>
					<producttype clickable="false">${OutStandingMovementSummaryDetails.productType}</producttype>
					<postingdate clickable="false">${OutStandingMovementSummaryDetails.postingDateAsString}</postingdate>
					<extra_text1 clickable="false">${OutStandingMovementSummaryDetails.extraText1}</extra_text1>
					<alerting clickable="false">${OutStandingMovementSummaryDetails.scenarioHighlighted}</alerting>
					<ilmfcast clickable="false">${OutStandingMovementSummaryDetails.ilmFcastStatus}</ilmfcast>
					<uetr clickable="false">${OutStandingMovementSummaryDetails.uetr}</uetr>

					<c:if test="${OutStandingMovementSummaryDetails != null}">
						<jsp:useBean id="OutStandingMovementSummaryDetails" class="org.swallow.work.model.Movement" />
						<% Movement mvt = (Movement)OutStandingMovementSummaryDetails; %>
							<%= mvt.getAttributeXml() %>
					</c:if>
				</row>
			</c:forEach>
		</rows>
	</grid>

	<c:if test="${requestScope.method != 'search'}">
		<bottomgrid>
			<metadata>
				<columns>
					<c:forEach items="${requestScope.column_order}" var="order">
						<c:choose>
							<c:when test="${order == 'alerting'}">
								<column heading=""
										tooltip="<fmt:message key='tooltip.movement.alerting'/>"
										draggable="false" filterable="false" type="str"
										sort="true" dataelement="alerting"
										width="${requestScope.column_width['alerting']}" />
							</c:when>

							<c:when test="${order == 'pos'}">
								<column heading="<fmt:message key='movement.position'/>"
										tooltip="<fmt:message key='tooltip.movement.position'/>"
										draggable="false" filterable="false" type="str" sort="false"
										dataelement="pos"
										width="${requestScope.column_width['pos']}" />
							</c:when>

							<c:when test="${order == 'entity'}">
								<column heading="<fmt:message key='movement.id.entityId'/>"
										tooltip="<fmt:message key='tooltip.movement.entityId'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="entity"
										width="${requestScope.column_width['entity']}" />
							</c:when>

							<c:when test="${order == 'value'}">
								<column heading="<fmt:message key='movement.value'/>"
										tooltip="<fmt:message key='tooltip.movement.value'/>"
										draggable="true" filterable="false" type="num" sort="false"
										dataelement="value"
										width="${requestScope.column_width['value']}" />
							</c:when>

							<c:when test="${order == 'amount'}">
								<column heading="<fmt:message key='movement.amount1'/>"
										tooltip="<fmt:message key='tooltip.movement.amount1'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="false" dataelement="amount"
										width="${requestScope.column_width['amount']}" />
							</c:when>

							<c:when test="${order == 'sign'}">
								<column heading="<fmt:message key='movement.sign'/>"
										tooltip="<fmt:message key='tooltip.movement.sign'/>"
										draggable="true" filterable="false" type="str" sort="false"
										dataelement="sign"
										width="${requestScope.column_width['sign']}" />
							</c:when>

							<c:when test="${order == 'ccy'}">
								<column heading="<fmt:message key='movement.ccy'/>"
										tooltip="<fmt:message key='tooltip.movement.ccy'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="ccy"
										width="${requestScope.column_width['ccy']}" />
							</c:when>

							<c:when test="${order == 'ref1'}">
								<column heading="<fmt:message key='movement.reference1'/>"
										tooltip="<fmt:message key='tooltip.movement.reference1'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="ref1"
										width="${requestScope.column_width['ref1']}" />
							</c:when>

							<c:when test="${order == 'account'}">
								<column heading="<fmt:message key='movement.account'/>"
										tooltip="<fmt:message key='tooltip.movement.account'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="account"
										width="${requestScope.column_width['account']}" />
							</c:when>

							<c:when test="${order == 'input'}">
								<column heading="<fmt:message key='movement.input'/>"
										tooltip="<fmt:message key='tooltip.movement.input'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="false" dataelement="input"
										width="${requestScope.column_width['input']}" />
							</c:when>

							<c:when test="${order == 'cparty'}">
								<column heading="<fmt:message key='movement.counterPartyId'/>"
										tooltip="<fmt:message key='tooltip.movement.counterPartyId'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="cparty"
										width="${requestScope.column_width['cparty']}" />
							</c:when>

							<c:when test="${order == 'pred'}">
								<column heading="<fmt:message key='movement.pred'/>"
										tooltip="<fmt:message key='tooltip.movement.pred'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="pred"
										width="${requestScope.column_width['pred']}" />
							</c:when>

							<c:when test="${order == 'ext'}">
								<column heading="<fmt:message key='label.movement.externalBalanceStatus'/>"
										tooltip="<fmt:message key='tooltip.movement.externalBalanceStatus'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="ext"
										width="${requestScope.column_width['ext']}" />
							</c:when>

							<c:when test="${order == 'status'}">
								<column heading="<fmt:message key='movement.status'/>"
										tooltip="<fmt:message key='tooltip.movement.status'/>"
										draggable="true" filterable="true"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="status"
										width="${requestScope.column_width['status']}" />
							</c:when>

							<c:when test="${order == 'matchid'}">
								<column heading="<fmt:message key='movement.matchId'/>"
										tooltip="<fmt:message key='tooltip.movement.matchId'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="false" dataelement="matchid"
										width="${requestScope.column_width['matchid']}" />
							</c:when>

							<c:when test="${order == 'source'}">
								<column heading="<fmt:message key='movement.source'/>"
										tooltip="<fmt:message key='tooltip.movement.source'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="source"
										width="${requestScope.column_width['source']}" />
							</c:when>

							<c:when test="${order == 'format'}">
								<column heading="<fmt:message key='movement.msgformat'/>"
										tooltip="<fmt:message key='tooltip.movement.msgformat'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="format"
										width="${requestScope.column_width['format']}" />
							</c:when>

							<c:when test="${order == 'notes'}">
								<column heading="<fmt:message key='movement.notes'/>"
										tooltip="<fmt:message key='tooltip.movement.notes'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="notes"
										width="${requestScope.column_width['notes']}" />
							</c:when>

							<c:when test="${order == 'beneficiary'}">
								<column heading="<fmt:message key='movement.beneficiary'/>"
										tooltip="<fmt:message key='tooltip.movement.beneficiary'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="beneficiary"
										width="${requestScope.column_width['beneficiary']}" />
							</c:when>

							<c:when test="${order == 'ref2'}">
								<column heading="<fmt:message key='movement.reference2'/>"
										tooltip="<fmt:message key='tooltip.movement.reference2'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="ref2"
										width="${requestScope.column_width['ref2']}" />
							</c:when>

							<c:when test="${order == 'ref3'}">
								<column heading="<fmt:message key='movement.reference3'/>"
										tooltip="<fmt:message key='tooltip.movement.reference3'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="ref3"
										width="${requestScope.column_width['ref3']}" />
							</c:when>

							<c:when test="${order == 'movement'}">
								<column heading="<fmt:message key='movement.movementId'/>"
										tooltip="<fmt:message key='tooltip.movement.movementId'/>"
										draggable="true" filterable="true"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="false" dataelement="movement"
										width="${requestScope.column_width['movement']}" />
							</c:when>

							<c:when test="${order == 'book'}">
								<column heading="<fmt:message key='movement.bookcode'/>"
										tooltip="<fmt:message key='tooltip.movement.bookcode'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="book"
										width="${requestScope.column_width['book']}" />
							</c:when>

							<c:when test="${order == 'custodian'}">
								<column heading="<fmt:message key='movement.custodian'/>"
										tooltip="<fmt:message key='tooltip.movement.custodian'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="false" dataelement="custodian"
										width="${requestScope.column_width['custodian']}" />
							</c:when>

							<c:when test="${order == 'xref'}">
								<column heading="<fmt:message key='movement.extraRef'/>"
										tooltip="<fmt:message key='tooltip.movement.extraRef'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="xref"
										width="${requestScope.column_width['xref']}" />
							</c:when>

							<c:when test="${order == 'uetr'}">
								<column heading="<fmt:message key='movement.uetr'/>"
										tooltip="<fmt:message key='tooltip.movement.uetr'/>"
										draggable="true" filterable="true"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="uetr"
										width="${requestScope.column_width['uetr']}" />
							</c:when>

							<c:when test="${order == 'update_date'}">
								<column heading="<fmt:message key='movement.update_date'/>"
										tooltip="<fmt:message key='tooltip.movement.update_date'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="false" dataelement="update_date"
										width="${requestScope.column_width['update_date']}" />
							</c:when>

							<c:when test="${order == 'matchingparty'}">
								<column heading="<fmt:message key='movement.matchingParty'/>"
										tooltip="<fmt:message key='tooltip.movement.matchingParty'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="matchingparty"
										width="${requestScope.column_width['matchingparty']}" />
							</c:when>

							<c:when test="${order == 'producttype'}">
								<column heading="<fmt:message key='movement.productType'/>"
										tooltip="<fmt:message key='tooltip.movement.productType'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="producttype"
										width="${requestScope.column_width['producttype']}" />
							</c:when>

							<c:when test="${order == 'postingdate'}">
								<column heading="<fmt:message key='movement.postingDateMSD'/>"
										tooltip="<fmt:message key='tooltip.movement.postingDate'/>"
										draggable="true" filterable="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="num" sort="true" dataelement="postingdate"
										width="${requestScope.column_width['postingdate']}" />
							</c:when>


							<c:when test="${order == 'extra_text1'}">
								<column heading="<fmt:message key='movement.extraText1'/>"
										tooltip="<fmt:message key='tooltip.movement.extra_text1'/>"
										draggable="true" filterable="true" columnNumber="29"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="extra_text1"
										width="${requestScope.column_width['extra_text1']}" />
							</c:when>
							<c:when test="${order == 'ilmfcast'}">
								<column heading="<fmt:message key='movement.ilmFcast'/>"
										tooltip="<fmt:message key='tooltip.movement.ilmFcast'/>"
										draggable="true" filterable="true"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="str" sort="true" dataelement="ilmfcast"
										width="${requestScope.column_width['ilmfcast']}" />
							</c:when>

						</c:choose>
					</c:forEach>
					<c:if test="${requestScope.useAddCols == 'Y'}">
						<c:forEach items="${requestScope.profileAddCols}" var="entry">
							<c:if test="${entry.sequenceKey == order}">
								<column heading="${entry.label}"
										draggable="true" filterable="true"
										tooltip="${entry.msdDisplayColumns.columnName}"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="${entry.msdDisplayColumns.dataType}" sort="true"
										dataelement="${entry.sequenceKey}"
										width="${requestScope.column_width[entry.sequenceKey]}" />
							</c:if>
						</c:forEach>
					</c:if>

					<c:if test="${requestScope.useAddCols == 'Y'}">
						<c:forEach items="${requestScope.profileAddCols}" var="entry">
							<c:if test="${!requestScope.column_order.contains(entry.sequenceKey)}">
								<column heading="${entry.label}"
										draggable="true" filterable="true"
										tooltip="${entry.msdDisplayColumns.columnName}"
										visible="false"
										balancetype="<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
										type="${entry.msdDisplayColumns.dataType}" sort="true"
										dataelement="${entry.sequenceKey}"
										width="${requestScope.column_width[entry.sequenceKey]}" />
							</c:if>
						</c:forEach>
					</c:if>
				</columns>
			</metadata>


			<rows size="${totalCount}">
				<c:forEach items="${requestScope.selectedMovementSummaryDetails}" var="selectedMovementSummaryDetails">
					<row>
						<pos clickable="false" positionlevel="${selectedMovementSummaryDetails.positionLevel}">
								${selectedMovementSummaryDetails.positionLevelName}
						</pos>
						<entity clickable="false">${selectedMovementSummaryDetails.id.entityId}</entity>
						<value clickable="false">${selectedMovementSummaryDetails.valueDateAsString}</value>
						<amount negative="${selectedMovementSummaryDetails.sign == 'C' ? 'false' : 'true'}" clickable="false">
								${selectedMovementSummaryDetails.amountAsString}
						</amount>
						<sign clickable="false">${selectedMovementSummaryDetails.sign}</sign>
						<ccy clickable="false">${selectedMovementSummaryDetails.currencyCode}</ccy>
						<ref1 clickable="false">${selectedMovementSummaryDetails.reference1}</ref1>
						<account clickable="false">${selectedMovementSummaryDetails.accountId}</account>
						<input clickable="false">${selectedMovementSummaryDetails.inputDateAsString}</input>
						<cparty clickable="false">${selectedMovementSummaryDetails.counterPartyId}</cparty>
						<pred clickable="false">${selectedMovementSummaryDetails.predictStatus}</pred>
						<ext clickable="false">${selectedMovementSummaryDetails.extBalStatus}</ext>
						<status clickable="false">${selectedMovementSummaryDetails.matchStatusDesc}</status>
						<matchid clickable="false">${selectedMovementSummaryDetails.matchId}</matchid>
						<source clickable="false">${selectedMovementSummaryDetails.inputSource}</source>
						<format clickable="false">${selectedMovementSummaryDetails.messageFormat}</format>
						<notes clickable="false">${selectedMovementSummaryDetails.hasNotes}</notes>
						<beneficiary clickable="false">${selectedMovementSummaryDetails.beneficiaryId}</beneficiary>
						<ref2 clickable="false">${selectedMovementSummaryDetails.reference2}</ref2>
						<ref3 clickable="false">${selectedMovementSummaryDetails.reference3}</ref3>
						<movement clickable="false">${selectedMovementSummaryDetails.id.movementId}</movement>
						<book clickable="false">${selectedMovementSummaryDetails.bookCode}</book>
						<custodian clickable="false">${selectedMovementSummaryDetails.custodianId}</custodian>
						<xref clickable="false">${selectedMovementSummaryDetails.reference4}</xref>
						<update_date clickable="false">${selectedMovementSummaryDetails.updateDateAsString}</update_date>
						<matchingparty clickable="false">${selectedMovementSummaryDetails.matchingParty}</matchingparty>
						<producttype clickable="false">${selectedMovementSummaryDetails.productType}</producttype>
						<postingdate clickable="false">${selectedMovementSummaryDetails.postingDateAsString}</postingdate>
						<extra_text1 clickable="false">${selectedMovementSummaryDetails.extraText1}</extra_text1>
						<alerting clickable="false">${selectedMovementSummaryDetails.scenarioHighlighted}</alerting>
						<ilmfcast clickable="false">${selectedMovementSummaryDetails.ilmFcastStatus}</ilmfcast>
						<uetr clickable="false">${selectedMovementSummaryDetails.uetr}</uetr>

						<c:if test="${selectedMovementSummaryDetails != null}">
							<jsp:useBean id="selectedMovementSummaryDetails" class="org.swallow.work.model.Movement" />
							<% Movement mvt = (Movement) selectedMovementSummaryDetails; %>
								<%= mvt.getAttributeXml() %>
						</c:if>
					</row>
				</c:forEach>
			</rows>

		</bottomgrid>

	</c:if>

	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option value="<c:out value='${entity.value}'/>"
						selected="${entity.value == requestScope.movement.id.entityId}">
					<c:out value='${entity.label}'/>
				</option>
			</c:forEach>
		</select>

		<select id="profilesList">
			<c:forEach items="${requestScope.profiles}" var="profile">
				<option value="<c:out value='${profile.value}'/>"
						selected="${profile.value == requestScope.movement.id.entityId}">
					<c:out value='${profile.label}'/>
				</option>
			</c:forEach>
		</select>

		<c:if test="${requestScope.initialinputscreen != 'S'}">
			<select id="filterList">
				<c:forEach items="${requestScope.filters}" var="filter">
					<option value="<c:out value='${filter.value}'/>"
							selected="${filter.label == requestScope.currentFilterConf}">
						<c:out value='${filter.label}'/>
					</option>
				</c:forEach>
			</select>
		</c:if>
	</selects>

</movementsummarydisplay>