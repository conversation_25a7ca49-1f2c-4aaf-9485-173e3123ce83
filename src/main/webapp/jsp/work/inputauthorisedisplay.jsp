<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>

 <c:choose>
        <c:when test="${request.inputscreen == 'authorise'}">
            <title><fmt:message key="inputAuth.title.window" /></title>
        </c:when>
        <c:otherwise>
            <title><fmt:message key="inputreffered.title.window" /></title>
        </c:otherwise>
    </c:choose>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT">
var lastRefTime = '<%=SwtUtil.getLastRefTimeOnGMTOffset(request,"") %>'
   <c:if test="${request.parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].method.value = "displayInputAuthorise";
		window.opener.refreshScreen("displayInputAuthorise");
	</c:if>

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var matches = "${requestScope.matchList}";
var currentIndex = "${requestScope.currentIndex}";
var manualMatch = "${requestScope.screen}";
var selectedtab="${requestScope.selectedTab}";
var valueDate = "${requestScope.valueDate}";
var day="${requestScope.queueTabSelected}";
var inptMethod="${requestScope.method}";
var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
var inputSource = "${requestScope.source}";
var authRefreshFlag = false; 
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function getRowCount(){
	var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var count=rows.length
	return count;
}

function getAllMovementList(){
	 var table = document.getElementById("movementSummaryDetails"); 
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var movementList = "";
	var columns;
	
	for (i=0; i < rows.length; i++) 
	{
		movementList = movementList+rows[i].cells[11].innerText+",";
		
	 }  		
	 return movementList;
	
}

function onSelectTableRow(rowElement, isSelected) {
    var hiddenEl4 = rowElement.cells[12].innerText.trim();
    var hiddenElement = rowElement.getElementsByTagName("input")[0].value;

    document.forms[0].selectedMovementId.value = hiddenEl4;
    document.forms[0].hiddenSource.value = hiddenElement;

    if (menuEntityCurrGrpAccess == "0" && isSelected) {
        document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;

        // Check input screen type and update buttons accordingly
        <c:choose>
            <c:when test="${requestScope.inputscreen == 'refere'}">
                <c:choose>
                    <c:when test="${requestScope.fromMenu == 'yes'}">
                        // Coming from Menu
                        document.getElementById("submitbutton").innerHTML = document.getElementById("submitenablebutton").innerHTML;
                    </c:when>
                    <c:otherwise>
                        document.getElementById("submitbutton").innerHTML = document.getElementById("submitenablebutton").innerHTML;
                    </c:otherwise>
                </c:choose>
            </c:when>

            <c:when test="${requestScope.inputscreen == 'authorise'}">
                document.getElementById("authbutton").innerHTML = document.getElementById("authenablebutton").innerHTML;
                document.getElementById("referbutton").innerHTML = document.getElementById("referenablebutton").innerHTML;
                document.getElementById("delbutton").innerHTML = document.getElementById("delenablebutton").innerHTML;
            </c:when>
        </c:choose>
    } else {
        document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

        <c:choose>
            <c:when test="${requestScope.inputscreen == 'refere'}">
                document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
            </c:when>

            <c:when test="${requestScope.inputscreen == 'authorise'}">
                document.getElementById("authbutton").innerHTML = document.getElementById("authdisablebutton").innerHTML;
                document.getElementById("referbutton").innerHTML = document.getElementById("referenablebutton").innerHTML;
                document.getElementById("delbutton").innerHTML = document.getElementById("deldisablebutton").innerHTML;
            </c:when>
        </c:choose>
    }

    accountAccess();
}


function onMultiSelectTableRow(rowElement,isSelected)
{
		if(isSelected){
		unHighLightTableRow(rowElement);
		// Added by Mefteh Bouazizi for Mantis 2137:Highlight movement that has note attached in Input Authorise Queue
		var hasNote=rowElement.cells[8].innerText.trim();
		if(hasNote.replace(/^\s+|\s+$/g, '')=='Y')
		    rowElement.style.backgroundColor="#FA8C8C";
	}
	else{
	    rowElement.style.backgroundColor="";
    	highLightTableRow(rowElement);
	}
		var noOfRows= getCountRowsSelected(rowElement);
		AuthorisedisplayAccess(noOfRows);

}

function AuthorisedisplayAccess(rowCount) {
    <c:choose>
        <c:when test="${requestScope.inputscreen == 'refere'}">
            if (getValidAccess()) {
                <c:choose>
                    <c:when test="${requestScope.fromMenu == 'yes'}">
                        // coming from Menu
                        document.getElementById("submitbutton").innerHTML = document.getElementById("submitenablebutton").innerHTML;
                    </c:when>
                </c:choose>
            }
        </c:when>
    </c:choose>

    // Get the number of rows currently selected
    var noOfRows = rowCount;

    // If no row is selected
    if (noOfRows === 0) {
        document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
        <c:if test="${requestScope.inputscreen == 'refere'}">
            document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
        </c:if>
        <c:if test="${requestScope.inputscreen == 'authorise'}">
            document.getElementById("authbutton").innerHTML = document.getElementById("authdisablebutton").innerHTML;
            document.getElementById("referbutton").innerHTML = document.getElementById("referdisablebutton").innerHTML;
            document.getElementById("delbutton").innerHTML = document.getElementById("deldisablebutton").innerHTML;
        </c:if>
    }

    if (noOfRows > 0 && menuEntityCurrGrpAccess === "0") {
        document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
        <c:if test="${requestScope.inputscreen == 'authorise'}">
            document.getElementById("authbutton").innerHTML = document.getElementById("authenablebutton").innerHTML;
            document.getElementById("referbutton").innerHTML = document.getElementById("referenablebutton").innerHTML;
            document.getElementById("delbutton").innerHTML = document.getElementById("delenablebutton").innerHTML;
        </c:if>
    }

    if (noOfRows > 0) {
        <c:if test="${requestScope.inputscreen != 'refere'}">
            if (getValidSourceType()) {
                document.getElementById("referbutton").innerHTML = document.getElementById("referenablebutton").innerHTML;
            }
        </c:if>
    }

    // Handle input_source and mov ids for both cases: auth and refered
    if (noOfRows === 1) {
        var tempIds = getSelectedMovIdList();
        document.forms[0].selectedMovementId.value = tempIds.substring(0, tempIds.length - 1);
        document.forms[0].hiddenSource.value = getSelectedSource();
        if (menuEntityCurrGrpAccess === "1") {
            <c:if test="${requestScope.inputscreen == 'authorise'}">
                document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
                document.getElementById("authbutton").innerHTML = document.getElementById("authdisablebutton").innerHTML;
                document.getElementById("referbutton").innerHTML = document.getElementById("referdisablebutton").innerHTML;
                document.getElementById("delbutton").innerHTML = document.getElementById("deldisablebutton").innerHTML;
            </c:if>
        } else {
            <c:if test="${requestScope.inputscreen == 'authorise'}">
                document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
                document.getElementById("authbutton").innerHTML = document.getElementById("authenablebutton").innerHTML;
                document.getElementById("delbutton").innerHTML = document.getElementById("delenablebutton").innerHTML;
                document.getElementById("referbutton").innerHTML = document.getElementById("referenablebutton").innerHTML;
            </c:if>
        }
    } else {
        document.forms[0].selectedMovementId.value = getSelectedMovIdList();
        document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
    }

    accountAccess();
}

function accountAccess() {
    var table = document.getElementById("movementSummaryDetails");
    var tbody = table.getElementsByTagName("tbody")[0];
    var rows = tbody.getElementsByTagName("tr");
    var selectedList = "";
    var flag = true;
    var entity = document.forms[0].elements["movement.id.entityId"].value;

    for (var i = 0; i < rows.length; i++) {
        if (isRowSelected(rows[i])) {
            var accountId = rows[i].cells[9].innerText;
            flag = accountAccessConfirm(accountId.trim(), entity);
            if (flag === "false") {
                <c:choose>
                    <c:when test="${requestScope.inputscreen == 'refere'}">
                        document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
                    </c:when>
                    <c:when test="${requestScope.inputscreen == 'authorise'}">
                        document.getElementById("authbutton").innerHTML = document.getElementById("authdisablebutton").innerHTML;
                        document.getElementById("referbutton").innerHTML = document.getElementById("referdisablebutton").innerHTML;
                        document.getElementById("delbutton").innerHTML = document.getElementById("deldisablebutton").innerHTML;
                    </c:when>
                </c:choose>
            }
        }
    }
}

	

function accountAccessConfirm(accountId,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";
	sURL = sURL + "&accountId="+accountId;
	sURL = sURL + "&entityId="+entity;
	sURL = sURL + "&status=Input";
	oXMLHTTP.open( "POST", sURL, false );	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}
	
function getValidSourceType(){

	var table = document.getElementById("movementSummaryDetails");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";

	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){

			var hiddenElement1 = rows[i].getElementsByTagName("input")[0].value;
			if(hiddenElement1 != 'MANUAL' && hiddenElement1 != 'PRE-ADVICE'){
			   return false
			}
						
		}
	 }  		
	return true;
}

function getValidAccess(){
	var table = document.getElementById("movementSummaryDetails");
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
	for (i=0; i < rows.length; i++)
	 {
		if( isRowSelected(rows[i])){
			var hiddenElement2 = rows[i].getElementsByTagName("input")[1].value;
			if(hiddenElement2 != 'true'){
				return false;
			}			
		}
	 }  		
	return true;
}



//Retunrs the selected MovId List
function getSelectedMovIdList(){
    
	var table = document.getElementById("movementSummaryDetails");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedMovIdList = "";
	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var hiddenE15 = rows[i].cells[12].innerText.trim();
			selectedMovIdList = selectedMovIdList +hiddenE15+",";
		}
	 }  		
	
	 return selectedMovIdList;
}


//Retunrs the selected input source that decides the movement screen will be pre-advice or manual input
function getSelectedSource(){
    
	var table = document.getElementById("movementSummaryDetails");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedSource= "";
	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){
			var hiddenE15 = rows[i].getElementsByTagName("input")[0].value;
			selectedSource = selectedSource +hiddenE15;
		}
	 }  		
	 return selectedSource;
}


function onFilter2(){	
	disableButtons();
	updateColors2();
}

function updateColors2()
{	
	var rows = x2.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (x2.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "even" : "odd");
			addClassName(rows[i], count % 2 ? "odd" : "even");
			count++;
		}
	}
}


/* this function return the row index */
function getRowindex(item)
{
	var index=-1;
	var rows = xl.dataTable.tBody.rows;
	var l=rows.length;
	var i=0;
	
	while ( i<l && index ==-1)
	 {
		 if(rows[i].cells[12].innerText == item)
			{	index=i;
				break;
			}
		 i++;
	  }
	
	return index;	
	
}

function onFilter1(){	
	disableButtons();
	updateColors1();
}


function updateColors1()
{
	var rows = xl.dataTable.tBody.rows;
	var l = rows.length;
	var count = 0;
	for (var i=0; i<l; i++)
	{
		if (xl.isRowVisible(i))
		{
			removeClassName(rows[i], count % 2 ? "even" : "odd");
			addClassName(rows[i], count % 2 ? "odd" : "even");
			count++;
		}
	}
}


function disableButtons(){
	document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
}

function bodyOnLoad() {

    xl = new XLSheet("movementSummaryDetails", "table_2", ["String", "Date", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "Number"], "11111111111111");
    xl.onsort = xl.onfilter = onFilter1;

    x2 = new XLSheet("movementDetailsView", "table_2_View", ["String", "Date", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "Number"], "11111111111111");
    x2.onsort = x2.onfilter = onFilter2;

    var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.id.entityId"], document.getElementById("entityDesc"));
    var dropBox2 = new SwSelectBox(document.forms[0].elements["movement.currencyCode"], document.getElementById("currencyDesc"));

    document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

    <c:if test="${requestScope.options == 'N'}">
        document.getElementById("entityDesc").innerText = '${entityDesc}';
        document.getElementById("currencyDesc").innerText = '${currencyDesc}';
    </c:if>

    <c:if test="${requestScope.inputscreen == 'authorise'}">
        highlightMultiTableRows("movementSummaryDetails");

        document.getElementById("authbutton").innerHTML = document.getElementById("authdisablebutton").innerHTML;
        document.getElementById("referbutton").innerHTML = document.getElementById("referdisablebutton").innerHTML;
        document.getElementById("delbutton").innerHTML = document.getElementById("deldisablebutton").innerHTML;
    </c:if>

    <c:if test="${requestScope.inputscreen == 'refere'}">

        <c:if test="${requestScope.fromMenu != 'yes'}">
            highlightTableRows("movementSummaryDetails");
        </c:if>
        <c:if test="${requestScope.fromMenu == 'yes'}">
            document.forms[0].fromMenu.value = "yes";
            highlightMultiTableRows("movementSummaryDetails");
        </c:if>

        document.getElementById("submitbutton").innerHTML = document.getElementById("submitdisablebutton").innerHTML;
    </c:if>

    document.forms[0].dateFlag.value = '${requestScope.dateFlag}';
    document.forms[0].selectedCurrencyGroup.value = '${requestScope.selectedCurrencyGroup}';

    document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
    document.getElementById("lastRefTime").innerText = lastRefTime;

    // Get the selected row list.
    var selectedMvtIdList = getStoredParam('selectedList');

    var rows = xl.dataTable.tBody.rows;
    var l = rows.length;

    for (var i = 0; i < l; i++) {
        // Highlight movement that has note attached in Input Authorise Queue
        var hasNote = rows[i].cells[8].innerText;
        if (hasNote.replace(/^\s+|\s+$/g, '') == 'Y') {
            rows[i].style.backgroundColor = "#FA8C8C";
        }
    }

    if (selectedMvtIdList != null) {
        selectedMvtIdList = selectedMvtIdList.trim().split(",");
        var l = selectedMvtIdList.length;
        var countrow = l - 1;
        for (var i = 0; i < l - 1; i++) {
            var rowindex = getRowindex(selectedMvtIdList[i]);
            rows[rowindex].style.backgroundColor = "";
            highLightTableRow(rows[rowindex]);
        }

        <c:if test="${requestScope.inputscreen == 'refere'}">
            document.getElementById("submitbutton").innerHTML = document.getElementById("submitenablebutton").innerHTML;
        </c:if>

        AuthorisedisplayAccess(countrow);
        accountAccess();
    }
}



function closeWindow() {
    <c:if test="${requestScope.method == 'movementDisplay'}">
        <c:if test="${requestScope.match != 'manual'}">
            window.opener.setTabInfo();
            window.opener.document.forms[0].method.value = "displayListByEntity";
            window.opener.document.forms[0].submit();
        </c:if>
    </c:if>
    self.close();
}


/**
  * This function is used to display the Movement Display screen
  *@param methodName
  *return boolean
  *
  */
function displayMovement(methodName) {
    if (document.forms[0].hiddenSource.value == "PRE-ADVICE") {
        // Variable declared for menuAccessIdOfChildWindow
        var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Pre-advice Display");
        // Variable declared for param
        var param = 'preadviceinput.do?method=' + methodName;
        param += '&entityId=' + document.forms[0].elements['movement.id.entityId'].value;
        param += '&movementId=' + document.forms[0].selectedMovementId.value;
        param += '&externalSearch=true';

        <c:if test="${requestScope.fromMenu != 'yes'}">
            param += '&cancleType=close';
            param += '&parentScreen=authorise';
        </c:if>
        <c:if test="${requestScope.inputscreen != 'authorise'}">
            param += '&mode=veiw';
        </c:if>

        param += '&menuAccessId=' + menuAccessIdOfChildWindow;
        // Variable declared for menuName
        var menuName = new String('<fmt:message key="preAdviceDisplay.title.window" />');
        // Variable declared for smrtPredPos
        var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict" />');
        menuName = menuName.substr(0, smrtPredPos - 3);

        if (menuAccessIdOfChildWindow == 2) {
            alert('<fmt:message key="alert.AccessNotAvl" />' + menuName + '<fmt:message key="alert.ContactSysAdm" />');
        } else {
            window.open(param, 'movementWindow', 'left=50,top=190,width=998,height=308,toolbar=0,resizable=yes,scrollbars=yes', 'true');
        }
    }

    if (document.forms[0].hiddenSource.value != "PRE-ADVICE") {
        // Variable declared for movId
        var movId = new String(document.forms[0].selectedMovementId.value).trim();
        // Variable declared for param
        var param = 'movement.do?method=' + "displayMovement";
        param += '&entityId=' + document.forms[0].elements['movement.id.entityId'].value;
        param += '&selectedMovementId=' + document.forms[0].selectedMovementId.value;
        param += '&cancleType=close';
        param += '&parentScreen=authorise';

        <c:if test="${requestScope.inputscreen == 'authorise'}">
            param += '&mode=veiw';
        </c:if>

        window.open(param, 'movementWindow', 'left=50,top=190,width=994,height=789,toolbar=0,resizable=yes,scrollbars=yes', 'true');
    }
}

/**
 *   This method is used to change the movement based on user input.
 *  @param methodName
 */
function changeMovement(methodName)
{
	document.forms[0].method.value					=	methodName;
	document.forms[0].entityId.value				=	document.forms[0].elements['movement.id.entityId'].value;
	document.forms[0].currencyCode.value			=	document.forms[0].elements['movement.currencyCode'].value;
	document.forms[0].inputSourceValue.value		=	inputSource;
	document.forms[0].selectedMovementId.value		=	document.forms[0].selectedMovementId.value;
	document.forms[0].refreshAuthSelection.value = 'Y';

	setStoredParam("selectedList",null);
	// Start:Code Modified For Mantis 1934 by Narasimhulu.P on 29-Aug-2012 to display confirmation dialogue when user chooses'delete'
	// add new confirmation warning dialogue 'Are you sure you want to delete based on the user input do the deletion.
	if (methodName=="deleteMovements")
	{
		var confirm_delete=window.confirm('<fmt:message key="confirm.delete"/>');
                // If user agree to delete , delete the selected record 
		if (confirm_delete==true){
			var entity = document.forms[0].elements["movement.id.entityId"].value;
			var oXMLHTTP = new XMLHttpRequest();
			var sURL = requestURL + appName+"/inputauthorise.do?method=checkLockedMvts";

			sURL = sURL + "&mvtsList="+getSelectedMovIdList();
			sURL = sURL + "&entityId="+entity;

			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var str=new String(oXMLHTTP.responseText);
			if(str != ""){
				alert(str);
			}else{
				document.forms[0].submit();
			}
		}
	}else
	{
		var entity = document.forms[0].elements["movement.id.entityId"].value;
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/inputauthorise.do?method=checkLockedMvts";

		sURL = sURL + "&mvtsList="+getSelectedMovIdList();
		sURL = sURL + "&entityId="+entity;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=new String(oXMLHTTP.responseText);
		if(str != ""){
			alert(str);
		}else{
			document.forms[0].submit();
		}
	}
}
	// End:Code Modified For Mantis 1934 by Narasimhulu.P on 29-Aug-2012 to display confirmation dialogue when user chooses'delete'
function submitForm(methodName)
{
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	document.forms[0].method.value			=	methodName;
	document.forms[0].fromMenu.value		=	'${requestScope.fromMenu}';
	document.forms[0].entityId.value		=	document.forms[0].elements['movement.id.entityId'].value;
	document.forms[0].currencyCode.value	=	document.forms[0].elements['movement.currencyCode'].value;
	document.forms[0].inputSourceValue.value		=	inputSource;
	document.forms[0].submit();
}

function refreshScreen(methodName){
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
    setStoredParam("selectedList",getSelectedMovIdList());
	submitForm(methodName);
}
</script>

</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onUnload="call();">
<form action="inputauthorise.do" method="post">
	<input name="method" type="hidden" value="">
	<input name="matchList" type="hidden" value="">
	<input name="currentIndex" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="nextRecord" type="hidden" value="">
	<input name="copiedMovementId" type="hidden" value="">

	<input name="entityId" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">
	<input name="inputSourceValue" type="hidden" value="">
	<input name="selectedMovementId" type="hidden" value="">
	<input name="hiddenSource" type="hidden" value="">
	<input name="refreshAuthSelection" type="hidden" value="">
	<input name="selectedCurrencyGroup" type="hidden" value="">

	<input name="selectedMatchId" type="hidden" value="">
	<input name="day" type="hidden" value="">
	<input name="movementList" type="hidden" value="">
	<input name="rowCount" type="hidden" value="">
	<input name="inputMethod" type="hidden" value="">
	<input name="isNotesPresent" type="hidden" value="">
	<input name="fromMenu" type="hidden" value="">
	<input name="externalSearch" type="hidden" value="true">
	<c:if test="${request.options == 'N'}">
				<input type="hidden" name="options" value="N">
			</c:if>

	<input name="dateFlag" type="hidden" value="">
	<input name="selectedtab" type="hidden" value='${selectedTab}'>

	<input name="menuAccessId" type="hidden">

	<div id="Movement"
		style="position: absolute; left: 20px; top: 16px; width: 960px; height: 67px; border: 2px outset;"
		color="#7E97AF">
	<div id="Movement"
		style="position: absolute; left: 8px; top: 4px; width: 510px; height: 38px;">

	<table width="350" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="25">
			<td width="30">
				<b><fmt:message key="currency.entityId" /></b>
			</td>
			<td width="35">&nbsp;</td>
			<td width="150px">
				<select
					name="movement.id.entityId"
					onchange="submitForm('display');"
					titleKey="tooltip.selectEntityId"
					style="width:120px"
					tabindex="1">
					<c:forEach items="${requestScope.entities}" var="entity">
						<option
							value="${entity.value}"
							<c:if test="${movement.id.entityId == entity.value}">selected="selected"</c:if>>
							${entity.label}
						</option>
					</c:forEach>
				</select>
			</td>
			<td width="20">&nbsp;</td>
			<td width="300">
				<span id="entityDesc" name="entityDesc" class="spantext"></span>
			</td>
		</tr>

		<input type="hidden" name="movement.inputSource" />

<tr height="25">
    <td width="30">
        <b><fmt:message key="currency.currencyCode" /></b>
    </td>
    <td width="35">&nbsp;</td>
    <td width="150px">
        <c:choose>
            <c:when test="${request.options != 'N'}">
                <select
                    name="movement.currencyCode"
                    onchange="submitForm('display');"
                    titleKey="tooltip.selectCurrencyCode"
                    style="width:120px"
                    tabindex="2">
                    <c:forEach items="${requestScope.currencies}" var="currency">
                        <option
                            value="${currency.value}"
                            <c:if test="${movement.currencyCode == currency.value}">selected="selected"</c:if>>
                            ${currency.label}
                        </option>
                    </c:forEach>
                </select>
            </c:when>
            <c:otherwise>
                <input
                    type="text"
                    name="movement.currencyCode"
                    value="${movement.currencyCode}"
                    class="htmlTextAlpha"
                    readonly="readonly"
                    style="width:120px;background:transparent;border:thin;"
                    tabindex="-1" />
            </c:otherwise>
        </c:choose>
    </td>
    <td width="20">&nbsp;</td>
    <td width="300">
        <span id="currencyDesc" name="currencyDesc" class="spantext"></span>
    </td>
</tr>

	</table>

	</div>
	</div>
	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute;  left: 20px; top: 90px; width: 960px; height: 300px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 0; top: 1; width: 950; height: 1px; visibility: visible;">
	<table width="960" border="0" cellspacing="0" cellpadding="0">
		<tr>
    <td width="13%" height="10px" class="topbar" align="left">
        <c:choose>
            <c:when test="${request.inputscreen == 'authorise'}">
                <b>&nbsp;&nbsp;<fmt:message key="queue.authorizePanel" /></b>
            </c:when>
            <c:otherwise>
                <b>&nbsp;&nbsp;<fmt:message key="queue.refferedPanel" /></b>
            </c:otherwise>
        </c:choose>
    </td>
</tr>
	</table>
	</div>
	<div id="MatchSummaryParent" color="#7E97AF"
		style="position: absolute;  left: 0px; top: 20px; width: 960px; height: 275px;">
	<div id="MatchSummary"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 944px; height: 10px; overflowX: scroll;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1500"
		border="0" cellspacing="1" cellpadding="0" height="23">
		<thead>
			<tr height="15px">
			<td width="100" style="border-left-width: 0px;"
				title="<fmt:message key='tooltip.sortPositionLevel' />" align="left">
				<b><fmt:message key="movement.pos1" /></b>
			</td>
			<td width="85" title="<fmt:message key='tooltip.sortValueDate' />" align="left">
				<b><fmt:message key="movement.date" /></b>
			</td>
			<td width="165" title="<fmt:message key='tooltip.sortAmount' />" align="left">
				<b><fmt:message key="movement.amount1" /></b>
			</td>
			<td width="75" title="<fmt:message key='tooltip.signField' />" align="left">
				<b><fmt:message key="movement.sign" /></b>
			</td>
			<td width="72" title="<fmt:message key='tooltip.sortCurrency' />" align="left">
				<b><fmt:message key="movement.currency" /></b>
			</td>
			<td width="143" title="<fmt:message key='tooltip.referenceField' />" align="left">
				<b><fmt:message key="movement.newreference1" /></b>
			</td>
			<td width="120" title="<fmt:message key='tooltip.partyField' />" align="left">
				<b><fmt:message key="movement.counterPartyId" /></b>
			</td>
			<td width="78" title="<fmt:message key='tooltip.predictField' />" align="left">
				<b><fmt:message key="movement.pred" /></b>
			</td>
			<td width="83" title="<fmt:message key='tooltip.sortbynotes' />" align="left">
				<b><fmt:message key="movement.notes" /></b>
			</td>
			<td width="220" title="<fmt:message key='tooltip.accountField' />" align="left">
				<b><fmt:message key="movement.account" /></b>
			</td>
			<td width="120" title="<fmt:message key='tooltip.sortBookcode' />" align="left">
				<b><fmt:message key="book.bookCode" /></b>
			</td>
			<td width="143" title="<fmt:message key='tooltip.referenceField2' />" align="left">
				<b><fmt:message key="movement.newreference2" /></b>
			</td>
			<td width="120" title="<fmt:message key='tooltip.sortMvmId' />" align="left">
				<b><fmt:message key="movement.movementId" /></b>
			</td>
		</tr>

		</thead>
	</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 1px; width: 960px; height: 272px; overflow: scroll">
	<div id="MatchSummary"
		style="position: absolute; z-index: 99; left: 1px; top: 22px; width: 900px; height: 10px;">
				<table class="sort-table" id="movementSummaryDetails" width="1500"
				border="0" cellspacing="1" cellpadding="0" height="232">
				<tbody>
					<c:forEach var="movementSummaryDetails" items="${requestScope.movementSummaryDetails}" varStatus="status">
						<tr height="20px" class="${status.index % 2 == 0 ? 'even' : 'odd'}">
							<input type="hidden" name="inputSource" value="${movementSummaryDetails.inputSource}" />
							<input type="hidden" name="access" value="${movementSummaryDetails.access}" />
							<td align="left" width="100" style="border-left-width: 0px;">
								${movementSummaryDetails.positionLevelAsString}&nbsp;
							</td>
							<td align="center" width="85">
								${movementSummaryDetails.valueDateAsString}&nbsp;
							</td>
							<td align="right" width="165">
								${movementSummaryDetails.amountAsString}&nbsp;
							</td>
							<td width="75" align="center">
								${movementSummaryDetails.sign}&nbsp;
							</td>
							<td width="72">
								${movementSummaryDetails.currencyCode}&nbsp;
							</td>
							<td width="143">
								${movementSummaryDetails.reference1}&nbsp;
							</td>
							<td width="120">
								${movementSummaryDetails.counterPartyId}&nbsp;
							</td>
							<td width="78" align="center">
								${movementSummaryDetails.predictStatus}&nbsp;
							</td>
							<td width="83" align="center">
								${movementSummaryDetails.hasNotes}&nbsp;
							</td>
							<td width="220">
								${movementSummaryDetails.accountId}&nbsp;
							</td>
							<td width="120">
								${movementSummaryDetails.bookCode}&nbsp;
							</td>
							<td width="143">
								${movementSummaryDetails.reference2}&nbsp;
							</td>
							<td align="right" width="120">
								${movementSummaryDetails.id.movementId}&nbsp;
							</td>
						</tr>
					</c:forEach>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="14"></td>
					</tr>
				</tfoot>
			</table>

	</div>
	</div>

	<div id="SweepingQueues" color="#7E97AF"
		style="position: absolute;  left: -4px; top: 280px; width: 962px; height: 160px;">
	<div id="SweepingQueues"
		style="position: absolute; left: 4px; top: 1; width: 945; height: 1px; visibility: visible;">
	<table width="960" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td align="center" width="13%" height="10px" class="topbar"><b>&nbsp;&nbsp;<fmt:message key="sweep.otherPanel" /></b></td>
		</tr>
	</table>
	</div>

	<div id="MatchSummaryParent" color="#7E97AF"
		style="position: absolute;  left: 4px; top: 20px; width: 960px; height: 135px;">
	<div id="MatchSummary"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 943px; height: 10px; overflowX: scroll;">
			<table class="sort-table" id="table_2_View" bgcolor="#B0AFAF"
			width="1500" border="0" cellspacing="1" cellpadding="0" height="23">
			<thead>
				<tr height="15px">
					<td width="75" style="border-left-width: 0px;"
						title="<fmt:message key='tooltip.sortPositionLevel' />" align="left">
						<b><fmt:message key="movement.pos1" /></b>
					</td>
					<td width="85" title="<fmt:message key='tooltip.sortValueDate' />" align="left">
						<b><fmt:message key="movement.date" /></b>
					</td>
					<td width="165" title="<fmt:message key='tooltip.sortAmount' />" align="left">
						<b><fmt:message key="movement.amount1" /></b>
					</td>
					<td width="75" title="<fmt:message key='tooltip.signField' />" align="left">
						<b><fmt:message key="movement.sign" /></b>
					</td>
					<td width="72" title="<fmt:message key='tooltip.sortCurrency' />" align="left">
						<b><fmt:message key="movement.currency" /></b>
					</td>
					<td width="143" title="<fmt:message key='tooltip.referenceField' />" align="left">
						<b><fmt:message key="movement.newreference1" /></b>
					</td>
					<td width="120" title="<fmt:message key='tooltip.partyField' />" align="left">
						<b><fmt:message key="movement.counterPartyId" /></b>
					</td>
					<td width="78" title="<fmt:message key='tooltip.predictField' />" align="left">
						<b><fmt:message key="movement.pred" /></b>
					</td>
					<td width="83" title="<fmt:message key='tooltip.sortbynotes' />" align="left">
						<b><fmt:message key="movement.notes" /></b>
					</td>
					<td width="220" title="<fmt:message key='tooltip.accountField' />" align="left">
						<b><fmt:message key="movement.account" /></b>
					</td>
					<td width="120" title="<fmt:message key='tooltip.sortBookcode' />" align="left">
						<b><fmt:message key="book.bookCode" /></b>
					</td>
					<td width="143" title="<fmt:message key='tooltip.referenceField2' />" align="left">
						<b><fmt:message key="movement.newreference2" /></b>
					</td>
					<td width="120" title="<fmt:message key='tooltip.sortMvmId' />" align="left">
						<b><fmt:message key="movement.movementId" /></b>
					</td>
				</tr>
			</thead>
		</table>

	</div>
	<div id="ddscrolltable"
		style="position: relative; left: 0px; top: 1px; width: 960px; height: 132px; overflow: scroll">
	<div id="MatchSummary"
		style="position: relative; z-index: 99; left: 1px; top: 22px; width: 900px; height: 10px;">
			<table class="sort-table" id="movementDetailsView" width="1500"
			border="0" cellspacing="1" cellpadding="0" height="92">
			<tbody>
				<c:forEach var="movementDetailsView" items="${requestScope.movementDetailsView}" varStatus="status">
					<tr height="20px" class="${status.index % 2 == 0 ? 'even' : 'odd'}">
						<input type="hidden" name="movementDetailsView.inputSource" value="${movementDetailsView.inputSource}" />
						<input type="hidden" name="movementDetailsView.access" value="${movementDetailsView.access}" />
						<td align="left" width="100">${movementDetailsView.positionLevelAsString}&nbsp;</td>
						<td align="center" width="85">${movementDetailsView.valueDateAsString}&nbsp;</td>
						<td align="right" width="165">${movementDetailsView.amountAsString}&nbsp;</td>
						<td width="75" align="center">${movementDetailsView.sign}&nbsp;</td>
						<td width="72">${movementDetailsView.currencyCode}&nbsp;</td>
						<td width="143">${movementDetailsView.reference1}&nbsp;</td>
						<td width="120">${movementDetailsView.counterPartyId}&nbsp;</td>
						<td width="78" align="center">${movementDetailsView.predictStatus}&nbsp;</td>
						<td width="83" align="center">${movementDetailsView.hasNotes}&nbsp;</td>
						<td width="220">${movementDetailsView.accountId}&nbsp;</td>
						<td width="120">${movementDetailsView.bookCode}&nbsp;</td>
						<td width="143">${movementDetailsView.reference2}&nbsp;</td>
						<td align="right" width="120">${movementDetailsView.id.movementId}&nbsp;</td>
					</tr>
				</c:forEach>
			</tbody>
			<tfoot>
				<tr>
					<td colspan="14"></td>
				</tr>
			</tfoot>
		</table>

	</div>
	</div>
	</div>


	<div id="MovementMatchHelpButtons"
		style="position: absolute; left: 882; top: 169; width: 70; height: 15px; visibility: visible;">
			<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="right">
						<a
							tabindex="11"
							href="#"
							title="<fmt:message key='tooltip.help' />"
							onclick="javascript:openWindow(buildPrintURL('print', '${requestScope.inputscreen == "authorise" ? "Input Authorise Queue" : "Input Referred Queue"}'), 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no', 'true');"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help', '', 'images/help_default.GIF', 1)">
							<img style="padding-left: 15px" src="images/help_default.GIF" name="Help" border="0" />
						</a>
					</td>
					<td align="right" id="Print">
						<a
							tabindex="11"
							onclick="printPage();"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Print', '', 'images/Print_R.gif', 1)">
							<img src="images/Print.gif" name="Print" border="0" title="<fmt:message key='tooltip.printScreen' />" />
						</a>
					</td>
				</tr>
			</table>

	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 4px; top: 163; width: 960px; height: 39px; visibility: visible;">
	<div id="Currency"
		style="position: absolute; left: 6; top: 4; width: 810; height: 15px; visibility: visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="mvmntbutton"></td>

			<c:choose>
				<c:when test="${requestScope.inputscreen == 'authorise'}">
					<td id="authbutton" width="70" title="<fmt:message key='tooltip.authmvmntdetails' />"></td>
					<td id="referbutton" width="70" title="<fmt:message key='tooltip.refmvmntdetails' />"></td>
					<td id="delbutton" width="70" title="<fmt:message key='tooltip.deletemvmntdetails' />"></td>
				</c:when>
				<c:when test="${requestScope.inputscreen == 'refere'}">
					<td id="submitbutton"></td>
				</c:when>
			</c:choose>

			<td id="refreshbutton" width="70" title="<fmt:message key='tooltip.refreshWindow' />">
				<a tabindex="7"
				   onMouseOut="collapsebutton(this)"
				   onKeyDown="submitEnter(this, event)"
				   onMouseOver="highlightbutton(this)"
				   onMouseDown="expandbutton(this)"
				   onMouseUp="highlightbutton(this)"
				   onClick="javascript:refreshScreen('display')">
					<fmt:message key="button.Refresh" />
				</a>
			</td>

			<td id="closebutton" width="70" title="<fmt:message key='tooltip.close' />">
				<a tabindex="8"
				   onKeyDown="submitEnter(this, event)"
				   onMouseOut="collapsebutton(this)"
				   onMouseOver="highlightbutton(this)"
				   onMouseDown="expandbutton(this)"
				   onMouseUp="highlightbutton(this)"
				   onclick="confirmClose('P');">
					<fmt:message key="button.close" />
				</a>
			</td>
		</tr>

	</table>
	</div>
	<table height="30">
		<tr>
			<td id="lastRefTimeLable" width="762px" align="right"><fmt:message key="label.lastRefTime" /></td>
			<td id="lastRefTime"><input class="textAlpha"
				style="background: transparent; border: 1;" tabindex="-1" readonly
				name="maxPageNo" value="" size="14"></td>
		</tr>
	</table>
	<div style="position: absolute; left: 6; top: 4; width: 810; height: 15px; visibility: hidden;">
		<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility: hidden">
			<tr>
				<c:choose>
					<c:when test="${requestScope.inputscreen == 'authorise'}">
						<c:choose>
							<c:when test="${requestScope.AuthButton == 'true'}">
								<td id="authenablebutton">
									<a tabindex="4"
									   title="<fmt:message key='tooltip.authmvmntdetails' />"
									   onKeyDown="submitEnter(this,event)"
									   onMouseOut="collapsebutton(this)"
									   onMouseOver="highlightbutton(this)"
									   onMouseDown="expandbutton(this)"
									   onMouseUp="highlightbutton(this)"
									   onClick="javascript:changeMovement('authoriseMovement');">
										<fmt:message key="button.auth" />
									</a>
								</td>
								<td id="delenablebutton">
									<a tabindex="6"
									   title="<fmt:message key='tooltip.deletemvmntdetails' />"
									   onKeyDown="submitEnter(this,event)"
									   onMouseOut="collapsebutton(this)"
									   onMouseOver="highlightbutton(this)"
									   onMouseDown="expandbutton(this)"
									   onMouseUp="highlightbutton(this)"
									   onClick="javascript:changeMovement('deleteMovements');">
										<fmt:message key="button.delete" />
									</a>
								</td>
							</c:when>
							<c:otherwise>
								<td id="authenablebutton">
									<a class="disabled" disabled="disabled">
										<fmt:message key="button.auth" />
									</a>
								</td>
								<td id="delenablebutton">
									<a class="disabled" disabled="disabled">
										<fmt:message key="button.delete" />
									</a>
								</td>
							</c:otherwise>
						</c:choose>
						<td id="authdisablebutton">
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.auth" />
							</a>
						</td>
						<td id="deldisablebutton">
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.delete" />
							</a>
						</td>
						<c:choose>
							<c:when test="${requestScope.RefButton == 'true'}">
								<td id="referenablebutton">
									<a tabindex="5"
									   title="<fmt:message key='tooltip.refmvmntdetails' />"
									   onKeyDown="submitEnter(this,event)"
									   onMouseOut="collapsebutton(this)"
									   onMouseOver="highlightbutton(this)"
									   onMouseDown="expandbutton(this)"
									   onMouseUp="highlightbutton(this)"
									   onClick="javascript:changeMovement('refereMovement');">
										<fmt:message key="button.Refer" />
									</a>
								</td>
							</c:when>
							<c:otherwise>
								<td id="referenablebutton">
									<a class="disabled" disabled="disabled">
										<fmt:message key="button.Refer" />
									</a>
								</td>
							</c:otherwise>
						</c:choose>
						<td id="referdisablebutton">
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.Refer" />
							</a>
						</td>
					</c:when>
					<c:when test="${requestScope.inputscreen == 'refere'}">
						<c:choose>
							<c:when test="${requestScope.SubmitButton == 'true'}">
								<td id="submitenablebutton">
									<a tabindex="4"
									   title="<fmt:message key='tooltip.submitmvmntdetails' />"
									   onKeyDown="submitEnter(this,event)"
									   onMouseOut="collapsebutton(this)"
									   onMouseOver="highlightbutton(this)"
									   onMouseDown="expandbutton(this)"
									   onMouseUp="highlightbutton(this)"
									   onClick="javascript:changeMovement('submitMovement');">
										<fmt:message key="button.submit" />
									</a>
								</td>
							</c:when>
							<c:otherwise>
								<td id="submitenablebutton">
									<a tabindex="4"
									   title="<fmt:message key='tooltip.submitmvmntdetails' />"
									   onKeyDown="submitEnter(this,event)"
									   onMouseOut="collapsebutton(this)"
									   onMouseOver="highlightbutton(this)"
									   onMouseDown="expandbutton(this)"
									   onMouseUp="highlightbutton(this)"
									   onClick="javascript:changeMovement('submitMovement');">
										<fmt:message key="button.submit" />
									</a>
								</td>
							</c:otherwise>
						</c:choose>
						<td id="submitdisablebutton">
							<a class="disabled" disabled="disabled">
								<fmt:message key="button.submit" />
							</a>
						</td>
					</c:when>
				</c:choose>
				<td id="refreshdisablebutton">
					<a class="disabled" disabled="disabled">
						<fmt:message key="button.Refresh" />
					</a>
				</td>
			</tr>
		</table>
	</div>
	<div id="MovementDisplay" style="position: absolute; left: 6; top: 4; width: 430px; height: 15px; visibility: hidden;">
		<table border="0" cellspacing="0" cellpadding="0" width="260px">
			<tr>
				<td id="mvmntenablebutton">
					<a tabindex="3"
					   title="<fmt:message key='tooltip.shwmvmntdetails' />"
					   onKeyDown="submitEnter(this,event)"
					   onMouseOut="collapsebutton(this)"
					   onMouseOver="highlightbutton(this)"
					   onMouseDown="expandbutton(this)"
					   onMouseUp="highlightbutton(this)"
					   onClick="displayMovement('search');">
						<fmt:message key="button.mvmnt" />
					</a>
				</td>
				<td id="mvmntdisablebutton">
					<a class="disabled" disabled="disabled">
						<fmt:message key="button.mvmnt" />
					</a>
				</td>
			</tr>
		</table>
	</div>

	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>