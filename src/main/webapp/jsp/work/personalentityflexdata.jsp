<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>



<c:set var="recordCount" value="${requestScope.personalEntityList.size()}"/>
<personalentity>
	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<column	heading="<fmt:message key="label.personalEntityList.entityId"/>"
						draggable="false"
						sort="true"
						type="str"
						dataelement="entityid"
						width="125"
						sortable = "true"
					/>
					<column
						heading="<fmt:message key="label.personalEntityList.entityName"/>"
						draggable="false"
						type="str"
						sort="true"
						dataelement="entityname"
						width="220"
					/>
					<column
						heading="<fmt:message key="label.personalEntityList.display"/>"
						draggable="false"
						type="bool"
						dataelement="display"
						width="125"
					/>
					<column
						heading="<fmt:message key="label.personalEntityList.defaultDays"/>"
						draggable="false"
						type="num"
						width="125"
						dataelement="displaydays"
						editable="true"
						clickable="true"
						maxChars="2"
					/>
					<column
						heading="<fmt:message key="label.personalEntityList.priorityOrder"/>"
						draggable="false"
						type="num"
						width="130"
						dataelement="priority"
						editable="true"
						clickable="true"
						maxChars="3"
					/>
			</columns>
		</metadata>

		<rows size="${recordCount}">
			<c:forEach items="${requestScope.personalEntityList}" var="rowrecord">
				<row>
					<entityid aggregate="${rowrecord.yesAggrgEntity}">${fn:escapeXml(rowrecord.id.entityId)}</entityid>
					<entityname clickable="false">${fn:escapeXml(rowrecord.entityName)}</entityname>
					<display selected="${rowrecord.yesDisplay == 'Y' ? 'true' : 'false'}">
							${rowrecord.yesDisplay == 'Y' ? 'Y' : 'N'}
					</display>
					<displaydays clickable="false">${fn:escapeXml(rowrecord.displayDays)}</displaydays>
					<priority clickable="false">${fn:escapeXml(rowrecord.priorityOrder)}</priority>
				</row>
			</c:forEach>
		</rows>
	</grid>
</personalentity>