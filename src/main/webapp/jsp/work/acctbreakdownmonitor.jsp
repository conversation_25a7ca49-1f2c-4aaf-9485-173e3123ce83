<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%--
 	acctbreakdownmonitor.jsp
 	
 	<AUTHOR> R / 07-Mar-2012
 	
 	This screen used to display account breakdown details.
 	- On load of this screen search criteria controls, tabs, buttons && image
 	  buttons will be populated
 	- On change of entity, currency && account class corresponding components
 	  will be populated by AJAX request
 	- DataGrid && totals will always be populated by AJAX request
--%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
	<title><fmt:message key="title.acctBreakdown.window"/></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

	<script type="text/javascript" src="js/jquery.exportselect.js"></script>
	<style type="text/css">
		#tip {
			position: absolute;
			width: 150px;
			border: 1px solid black;
			padding: 1px;
			background-color: lightyellow;
			visibility: hidden;
			z-index: 100;
		}

		a:link {
			text-decoration: none;
		}

		a:visited {
			text-decoration: none;
		}
	</style>
	<script type="text/javascript">
		// Flag denotes whether the screen is peforming a operation || not
		var isBusy = false;
		// Xml Http Request variable for handling AJAX request
		var oXHR;
		// Flag to apply currency threshold
		var applyCurrencyThreshold = "${requestScope.applyCurrencyThreshold}";
		// Auto refresh rate value
		var refreshRate = "${requestScope.autoRefreshRate.propertyValue}";
		//Timer to refresh grid in every 'n' seconds
		var timerAutoRefresh;
		//Access right for selected entity && currency
		var menuEntityCurrGrpAccess;
		var menuAccessId = "${requestScope.menuAccessId}";
		// VARIABLES FOR TAB CONTROL
		// Index of the selected tab (default is first tab)
		var selectedTabIndex = 1;
		// Tab name array, this array contains tab ids, which will be used by
		// tabnew.js to select/deselect tabs
		var tabNames;
		// Initial tab selected
		var initialtab;
		// Selected tab name/id
		var selectedtab;

		// VARIABLES FOR CALENDAR/DATE
		// Date format value. It should be either 'dd/MM/yyyy' or'MM/dd/yyyy'
		var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
		// Date format. 1-dd/MM/yyyy, 2-MM/dd/yyyy
		var dateFormat = '${sessionScope.CDM.dateFormat}';
        console.log('dateFormat',dateFormat);
        console.log('dateFormatValue',dateFormatValue);
		var lastSelectedValueDate;
		// Close button id
		var cancelcloseElements =  ["closeBtn"];
		// Flag indicates whether the date field is on focus
		var dateFocusFlag = true;
		// Inititalize calendar object
		var cal = new CalendarPopup("pnlCal", true);
		// Set calendar properties
		cal.CP_dateFormat = dateFormatValue;
		cal.offsetX = -125;
		cal.offsetY = 20;

		// VARIABLES FOR GRID
		// Currency format value. It should be either '999,999.99' or'999.999,99'
		var currencyFormat = '${sessionScope.CDM.currencyFormat}';
		//Sort type grid column
		var sortTypeArr = ["String", "String", "String","String", currencyFormat, currencyFormat, currencyFormat, "String"];
		//To maintain sort && filter status of the grid while refresh
		var sortFilterFlag = false;
		// Grid data
		var tblGridData;
		// Current filter status of the grid
		var filterStatusArr;
		// Current sort status of the grid
		var sortStatus;
		// Current sorting order
		var sortDescending;
		// Current scroll position
		var gridScrollTop;
		//Flag denotes move balance to loro to predicted balance || viceversa
		var loroToPredictedFlag;
		//Sum flag value of selected record
		var sumFlag;
		//Account Id of selected record
		var selectedAccount;
		var appName = "<%=SwtUtil.appName%>";
		/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
            mandatoryFieldsArray= "undefined" ;

		/**
		 * This function will be invoked once the page is loaded. It populates tabs names by iterating
		 * tab controls && dropdown controls
		 */
		function bodyOnLoad() {
			//Populate tab name array && select initial tab
			tabNames = new Array()
			//Tab array index (temp variable)
			var tabCount = 0;
			//Flag variable to indicate the tab index to the selected tab.
			var tabDateFlag = false;
			//Iterate through tab controls && populate tab array.
			for (i = 0; i < document.getElementById("ddimagetabs").childNodes.length; i++) {
				var child = document.getElementById("ddimagetabs").childNodes[i];
				if ( child.type != undefined) {
					tabNames[tabCount] = child.id;
					tabCount++;
				}
				if(document.getElementById("acctBreakdownMonitor.valueDateAsString").value == tabNames[tabCount-1]){
					selectedTabIndex = tabCount;
					tabDateFlag = true;
				}
			}
			if(!tabDateFlag){
				selectedTabIndex = tabCount;
			}
			initialtab=[selectedTabIndex, tabNames[selectedTabIndex - 1]];
			selectedtab = initialtab[selectedTabIndex];
			if(tabDateFlag){
				lastSelectedValueDate = tabNames[selectedTabIndex - 1];
			}else{
				lastSelectedValueDate = document.getElementById("acctBreakdownMonitor.valueDateAsString").value;
			}
			//To select initial tab after 300 millisec, as tabnew.js has to finish its job
			setTimeout("selectInitTab()", 300);

			//Dropdown controls. To show selected items description adjecent to the control.
			new SwSelectBox(document.getElementById("acctBreakdownMonitor.entityId"), document.getElementById("entityName"));
			new SwSelectBox(document.getElementById("acctBreakdownMonitor.acctId"), document.getElementById("acctName"));
			new SwSelectBox(document.getElementById("acctBreakdownMonitor.currencyCode"), document.getElementById("currencyDesc"));

			var headerData = [];
			var dataprovider = new Array();

			var newElement1 = {};
			newElement1[headerData[0]] = 'Pdf';
			dataprovider.push(newElement1);

			var newElement2 = {};
			newElement2[headerData[0]] = 'Excel';
			dataprovider.push(newElement2);

			var newElement3 = {};
			newElement3[headerData[0]] = 'Csv';
			dataprovider.push(newElement3);

			if(dateFormatValue==("dd/MM/yyyy")){
				document.getElementById("acctBreakdownMonitor.valueDateAsString").title="<fmt:message key="tooltip.enterValueDate"/>";
			}else{
				document.getElementById("acctBreakdownMonitor.valueDateAsString").title="<fmt:message key="tooltip.ValueDateMMDDYY"/>";
			}

            $("#exportReport").exportselect ({
            	dataprovider: dataprovider,
            	change: exportReport,
				selectedIndex:0
			  });


			//Change status of currency threshold checkbox based on balance type && user option
			changeCurrencyThresholdStatus();
			//Change status of hide zero balances checkbox based on user option
			if ("${requestScope.hideZeroBalances}" == "<%= SwtConstants.YES %>") {
				document.getElementById("acctBreakdownMonitor.hideZeroBalances").checked = true;
		    }
		    //Display datagrid
		    refresh();
		}

		/**
		 * This function selects initial tab. This is invoked by a timer, as this has to be
		 * executed only after tabnew.js complets its task.
		 */
		function selectInitTab() {
			selectTab(document.getElementById("acctBreakdownMonitor.valueDateAsString"));
		}

		/**
		 * This function selects currently clicked tab && deselects previously selected tab && * refresh the grid based on selected date
		 *
		 * @param objTab
		 */
		function checkTabIndex(objTab) {
			//Select currently clicked tab && deselect previouly selected one
			var tabIndex = selectTabIndex(objTab);
			//Set value date based on the tab selected
			if (document.getElementById("acctBreakdownMonitor.valueDateAsString").value != tabNames[tabIndex - 1]) {
				if (tabIndex != tabNames.length) {
					document.getElementById("acctBreakdownMonitor.valueDateAsString").value = tabNames[tabIndex - 1];
					// Assigning tab date value to the date field value
					lastSelectedValueDate = tabNames[tabIndex - 1];
				}
				refresh();
			}
		}

		/**
		 * This function is invoked on change of date field. It will select tab based on selected/entered
		 * date. If the date is not availabe in the tab bar, "Seleted" tab will be chosen.
		 *
		 * @param objDate
		 */
		function selectTab(objDate) {
			var i = 0;
			for(; i < tabNames.length; i++) {
				if (tabNames[i] == objDate.value) {
					break;
				}
			}
			if (i >= tabNames.length) {
				i = tabNames.length - 1;
			}
			selectTabIndex(document.getElementById(tabNames[i]));
		}

		/**
		 * This function selects given tab && deselects previously selected tab
		 *
		 * @param objTab
		 * @return int - index of teh selected tab
		 */
		function selectTabIndex(objTab) {
			changeselected(objTab.id);
			var tabIndex = getSelectedTabIndex();
			if (tabIndex != selectedTabIndex) {
				//expandcontent(tabNames[0], objTab);
				highlighttab(objTab);
				selectedTabIndex = tabIndex;
			}
			return tabIndex;
		}

		/**
		 * This function highlights holiday tabs, based on holiday list for selected entity
		 *
		 * @param tabFlag
		 */
		function changeHoliday(tabFlag) {
			for (i = 0; i < tabFlag.length; i++) {
				document.getElementById(tabNames[i]).style.color = ((tabFlag.charAt(i) == 'Y') ? "#A0A0A0" : "#000000");
			}
		}

		/**
		 * This function validates value date field
		 *
		 * @return boolean
		 */
		function validateValueDate() {
			//Value date field
			var objDate = document.getElementById('acctBreakdownMonitor.valueDateAsString');
			if (isEmpty(objDate)) {
				alert('<fmt:message key="alert.acctBreakdown.date"/>');
				objDate.focus();
				return false;
			} else {
				return true;
			}
		}

		/**
		 * This function will be called when ever date value is changed. First it will validate the date. Then check
		 * the date should not exceed 30 days from value/server date,  If it satisfy previous conditions it will
		 * select the corresponding tab && refresh the grid
		 *
		 * @param objDate
		 * @return boolean - success/failure
		 */
		function onDateChange(objDate) {
			if (dateFocusFlag) {
				if (objDate.value != lastSelectedValueDate) {
					refreshOnDateChange(objDate);
				}
			} else {
				dateFocusFlag = true;
			}
		}

		/**
		 * This function will be called when ever date value is entered. First it will validate the date. Then check
		 * the date should not exceed 30 days from value/server date,  If it satisfy previous conditions it will
		 * select the corresponding tab && refresh the grid
		 *
		 * @param objDate
		 * @return boolean - success/failure
		 */
		function onDateKeyPress(objDate,e) {
			var event =(window.event|| e);
			if (event.keyCode == 13) {
				dateFocusFlag = false;
				refreshOnDateChange(objDate);
			}
		}

		/**
		 * This function will be called when ever date value is changed. First it will validate the date. Then check
		 * the date should not exceed 30 days from value/server date,  If it satisfy previous conditions it will
		 * select the corresponding tab && refresh the grid
		 *
		 * @param objDate
		 * @return boolean - success/failure
		 */
		function refreshOnDateChange(objDate) {
			if (!isEmpty(objDate)) {
				if (validateField(objDate, 'date', dateFormat)) {
					var selectedDate = (parseFormattedDate(objDate.value).getTime()) / (60*60*1000);
					var valueDate = (parseFormattedDate(tabNames[0]).getTime()) / (60*60*1000);

					if (Math.round(((selectedDate - valueDate) / 24)) > 30) {
						alert("<fmt:message key="alert.acctBreakdown.exceedDate"/>");
						objDate.value = lastSelectedValueDate;
					} else {
                        // Assigning the last selected date value to the date field
						lastSelectedValueDate = objDate.value;
						refresh();
					}

					// Should select the tab if && only if the entered date is valid
					//Select tab based on date selection
					selectTab(objDate);
				}

			} else if (!(document.activeElement.id == "tdDate"
					|| document.activeElement.id == "closeBtn"
					|| document.activeElement.id.indexOf("/") > 0)) {
				alert('<fmt:message key="alert.acctBreakdown.date"/>');;
			}
		}

		/**
		 * This function parses given date with dateformat chosen by user
		 *
		 * @param dateValue
		 * @return date
		 */
		function parseFormattedDate(dateValue) {
			if (dateFormat == 'datePat1') {
				return parseDate(dateValue, dateFormat);
			} else {
				return parseDate(dateValue);
			}
		}

		/**
		 * This function enables/disables && check/uncheck the currency threshold checkbox
		 * For balanceType PREDICTED, LORO && EXTERNAL, currency threshold will always be disabled.
		 * For other balanceType it will be checked/unchecked based on the value.
		 */
		function changeCurrencyThresholdStatus() {
			//Get selected balance type
			var balanceType = document.getElementById("acctBreakdownMonitor.balanceType").value;
			//Get currency threshold object
			var cbCurrencyThreshold = document.getElementById("acctBreakdownMonitor.applyCurrencyThreshold");

			//Based on balance type selection, enable/disable currency threshold control
			if (balanceType == "<%=SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED%>"
				|| balanceType == "<%=SwtConstants.ACCT_MONITOR_BALTYPE_LORO%>"
				|| balanceType == "<%=SwtConstants.ACCT_MONITOR_BALTYPE_EXTERNAL%>") {
				cbCurrencyThreshold.checked = false;
			    cbCurrencyThreshold.disabled = true;
			} else {
			    cbCurrencyThreshold.disabled = false;
			    if (applyCurrencyThreshold == "<%= SwtConstants.YES %>") {
					cbCurrencyThreshold.checked = true;
			    }
			}
			//Reset value as it is valid for one time (page load)
			applyCurrencyThreshold = "<%= SwtConstants.NO %>"
		}

		/**
		 * This function populates related fields of changed one.
		 *
		 * @param operation
		 */
		function onDataChange(operation) {
			if (!isBusy) {
				//Set busy flag
				isBusy = true;
				//Get criteria form
				var objForm = getControlForm();
				//Set method name
				objForm.method.value = "getControlData";
				//Based on the control changed, populates related fields
				//Get request body
				var requestBody = getRequestBody(objForm) + requestParam("operation", operation);
				//Send request to populate controls
				sendRequest("acctbreakdownmonitor.do", requestBody);
			}

		}

		/**
		 * This function populates dropdown controls. Once controls are populated
		 * grid will be refreshed based on criteria selected.
		 *
		 * @param responseXml
		 */
		function populateControls(responseXml) {
			var dropdownData = responseXml.getElementsByTagName('dropdown');
			if (dropdownData.length > 0) {
				for (i = 0; i < dropdownData.length; i++) {
					populateDropdown(dropdownData[i]);
				}
			}
			//Set busy flag
			isBusy = false;
			refresh();
		}

		/**
		 * This function populate one dropdown control.
		 *
		 * @param dataXml
		 */
		function populateDropdown(dataXml) {
			//Get id of the dropdown
			var fieldName = dataXml.attributes.getNamedItem("id").value;
			//Get xml string
			var xmlString;

			if (typeof window.XMLSerializer != "undefined") {
				xmlString = (new window.XMLSerializer())
						.serializeToString(dataXml
								.getElementsByTagName('select')[0]);
			} else if (typeof dataXml.childNodes[0].xml != "undefined") {
				xmlString = dataXml.childNodes[0].xml;
			}

			//Repaint dropdown control
			document.getElementById(fieldName).innerHTML = xmlString;

			//To show selected items description adjecent to the control.
			if (fieldName == "ddAcctId") {
				new SwSelectBox(document
						.getElementById("acctBreakdownMonitor.acctId"),
						document.getElementById("acctName"));
			} else if (fieldName == "ddCurrencyCode") {
				new SwSelectBox(document
						.getElementById("acctBreakdownMonitor.currencyCode"),
						document.getElementById("currencyDesc"));
			}
		}

		/**
		 * This function refresh the grid based on selected criteria
		 */
		function refreshGrid() {
			sortFilterFlag = true;
			refresh();
		}

		/**
		 * This function validates date field before refresh the grid when chcek/uncheck checkbox
		 *
		 * @return boolean
		 */
		function refreshOnCheckboxChange() {
			if (validateValueDate()) {
				refresh();
				return true;
			} else {
				return false;
			}
		}

		/**
		 * This function send a AJAX request to server && get grid data.
		 */
		function refresh() {
			$("#exportReport").hide();
			//Value date field
			var objDate = document
					.getElementById('acctBreakdownMonitor.valueDateAsString');
			if (!isBusy) {
				onStart();
				// Assigning the last selected date value to the date field when the screen refreshes
				document
						.getElementById('acctBreakdownMonitor.valueDateAsString').value = lastSelectedValueDate;
				//Get criteria form
				var objForm = getControlForm();
				//Set method name
				objForm.method.value = "refresh";
				sendRequest("acctbreakdownmonitor.do", getRequestBody(objForm));
			}
		}

		/**
		 * This function paints grid data && process the data for grid
		 * sorting && filtering
		 *
		 * @param gridData
		 */
		function populateGrid(gridData) {
			console.log('----------11')
			try{


			document.getElementById("pnlTabContainer").innerHTML = gridData;
			document.getElementById("ddimagetabs").innerHTML = "";
			document.getElementById("ddimagetabs").innerHTML = document
					.getElementById("ddimagetabsgrid").innerHTML;
			document.getElementById("ddimagetabsgrid").style.display = "none";
			document.getElementById("acctBreakdownMonitor.valueDateAsString").value = document
					.getElementById("refreshValueDate").value;
			document.getElementById("acctBreakdownMonitor.existingEntityId").value = document
					.getElementById("acctBreakdownMonitorCurrentEntity").value;
			//Set scroll position of the grid
			if (gridScrollTop != "") {
				getGridTable().scrollTop = gridScrollTop;
			}
			//
			highlightTableRows("tblGrid");
			//Add sort/filter option to the grid
			tblGridData = new XLSheet("tblGrid", "tblGridHeader", sortTypeArr,
					"1111110", 'false', undefined, undefined, "ddscrolltable");
			// Set filter status
			if (filterStatusArr != undefined && filterStatusArr.length != 0
					&& document.getElementById("tblGrid").innerText != "") {
				tblGridData.setFilterStatus(filterStatusArr);
			}
			//Set sort status
			if (sortStatus != undefined && sortStatus != "") {
				tblGridData.dataTable.defaultDescending = sortDescending;
				tblGridData.dataTable.doManualSorting(sortStatus);
			}
			//Add callback function
			tblGridData.onsort = tblGridData.onfilter = updateGridColors;
			//Update grid row color based on grid value
			//updateGridColors();
			var tabCount = 0;
			var tabDateFlag = false;
			//Iterate through tab controls && populate tab array.
			for (i = 0; i < document.getElementById("ddimagetabs").childNodes.length; i++) {
				var child = document.getElementById("ddimagetabs").childNodes[i];
				if (child.type != undefined) {
					tabNames[tabCount] = child.id;
					tabCount++;
				}
				if (document
						.getElementById("acctBreakdownMonitor.valueDateAsString").value == tabNames[tabCount - 1]) {
					selectedTabIndex = tabCount;
					tabDateFlag = true;
				}
			}
			if (!tabDateFlag) {
				selectedTabIndex = tabCount;
			}
			initialtab = [ selectedTabIndex, tabNames[selectedTabIndex - 1] ];
			selectedtab = initialtab[selectedTabIndex];
			if (tabDateFlag) {
				lastSelectedValueDate = tabNames[selectedTabIndex - 1];
			} else {
				lastSelectedValueDate = document
						.getElementById("acctBreakdownMonitor.valueDateAsString").value;
			}
			var highLightFlag = false;

			for (i = 0; i < document.getElementById("ddimagetabs").childNodes.length; i++) {
				var child = document.getElementById("ddimagetabs").childNodes[i];
				if (child.type != undefined) {
					if (document
							.getElementById("acctBreakdownMonitor.valueDateAsString").value == child.id) {
						highLightFlag = true;
						highlighttab(child);
						break;
					}
				}
			}
			if (!highLightFlag) {
				highlighttab(document.getElementById("ddimagetabs").childNodes[document
						.getElementById("ddimagetabs").childNodes.length - 2]);
			}
			selectTab(document
					.getElementById("acctBreakdownMonitor.valueDateAsString"));
			//Based on grid refresh data do perform some operation
			//Update holiday indicator tabs
			changeHoliday(document
					.getElementById("acctBreakdownMonitor.tabFlag").value);
			//Update last refresh time
			console.log('**--',document
					.getElementById("acctBreakdownMonitor.lastRefreshTime").value)
			console.log('-------------------')
			document.getElementById("lastRefreshTime").value = document
					.getElementById("acctBreakdownMonitor.lastRefreshTime").value;
			console.log('----------------zz---',document.getElementById("lastRefreshTime"),document
					.getElementById("acctBreakdownMonitor.lastRefreshTime").value)
			//Show/Hide data build message based on the status
			if (document.getElementById("acctBreakdownMonitor.dataBuildStatus").value == "false") {
				document.getElementById('pnlDataBuild').style.visibility = "visible";
			}
			//Entity && currency group access right
			menuEntityCurrGrpAccess = document
					.getElementById("menuEntityCurrGrpAccess").value;
			//Show grid tooltip on scrollbar
			showTooltip();
			onStop();
			var rows = tblGridData.dataTable.tBody.rows;
			var l = rows.length;
			var selectedAccount = getStoredParam('accountSelected');
			for (var i = 0; i < l; i++) {
				if (rows[i].cells[2].innerText.trim() == selectedAccount) {
					highLightTableRow(rows[i]);
					rows[i].className = 'selectrow';
					break;
				}
			}
			if (!isIEunder10 && document.getElementById("pnlGrid") != undefined) {
				setStyleForBrowsers();
			}
			}catch (e ){
				console.log(e)
			}
		}

		/**
		 * This function creats an AJAX request to the server
		 *
		 * @param url
		 * @param requestBody
		 */
		function sendRequest(url, requestBody) {
			//Create AJAX request object
			oXHR = zXmlHttp.createRequest();
			//Set request properties, like request method, url, content type
			oXHR.open("POST", url, true);
			oXHR.setRequestHeader("Content-Type",
					"application/x-www-form-urlencoded");
			//Register callback method to handle response
			oXHR.onreadystatechange = requestCallBack;
			//Send request parameters in a body of the request as this is a POST request
			oXHR.send(requestBody);
		}

		/**
		 * This is a callback function for AJAX request.
		 * It receives two type of response from server
		 * 1. HTML
		 *	- Its should be grid data, so paint grid data in its container.
		 * 2. XML
		 *  - It should be either error message || data for controls.
		 *  = If it is error message then show alert message.
		 *  - If it is a data, then populate the same in corressponding controls
		 */
		function requestCallBack() {
			if (oXHR.readyState == 4) {
				if (oXHR.status == 200 || oXHR.status == 304) {
					if (oXHR.responseXML != null
							&& oXHR.responseXML.documentElement != null) {
						//XML response, so either show error message || populate controls
						var response = oXHR.responseXML.documentElement;
						var requestReply = response
								.getElementsByTagName('request_reply');
						if (requestReply.length > 0) {
							//Show error message
							alert(requestReply[0].childNodes[1].nodeValue
									+ "\n"
									+ requestReply[0].childNodes[2].nodeValue)
						} else {
							//Process result from server

							//populate controls
							populateControls(response);
						}
					} else {
						//HTML response, so display grid data
						populateGrid(oXHR.responseText);
					}
				} else {
					//statusText is not always accurate
					alert(oXHR.statusText);
				}
			}
		}

		/**
		 * This function gets grid information, like sort status, filter
		 * status && scroll position. This information will be used on
		 * grid refresh
		 */
		function getGridInfo() {
			if (tblGridData != null && tblGridData != undefined) {
				if (sortFilterFlag) {
					//Get sort status
					sortStatus = tblGridData.dataTable.sortColumn;
					//Get sort order
					sortDescending = tblGridData.dataTable.descending;
					//Get filter details in a comma separated value
					filterStatusArr = new Array(5);
					if (document.getElementById("tblGrid").innerText != "")
						filterStatusArr = tblGridData.getFilterStatus();
				}
				//Get scroll position of the grid
				var scrollTable = getGridTable();
				if (scrollTable != undefined && scrollTable != null) {
					gridScrollTop = scrollTable.scrollTop;
				}
			}
		}

		/**
		 * This function resets grid informations.
		 */
		function resetGridInfo() {
			filterStatusArr = [];
			sortStatus = "";
			sortDescending = "";
			gridScrollTop = "";
			sortFilterFlag = false;
		}

		/**
		 * This function updated background color for grid row.
		 * The row color is based in sum flag.
		 * - If Sum Flag is P, then it should light grey
		 * - If Sum Flag is N, then it should grey
		 * - If other value, then it should be either odd || even
		 */
		function updateGridColors() {
			//Get grid rows
			var rows = tblGridData.dataTable.tBody.rows;
			//Row count

			var count = 0;
			//Index of Selected Row
			var selectedRowIndex = -1;
			//Row class name(style)
			var className;

			//Iterate through all rows && set row color based on value
			for (var i = 0; i < rows.length; i++) {
				if (tblGridData.isRowVisible(i)) {
					//Get sum value
					var sumFlg = rows[i].cells[7].innerText.trim();
					/*
					 * Start:Code Modified By Chinniah on 19-Jul-2012 for
					 * 1054_STL_63 in Mantis 1933:Color Flag not Proper for
					 * SUM='C'
					 */
					var summable = rows[i].cells[9].innerText.trim();
					//Get index of selected record.
					if (rows[i].className == "selectrow") {
						selectedRowIndex = i;
					}
					//Get background color based on sum flag
					if (sumFlg == "P") {
						className = "accp";
					} else if (sumFlg == "N" || summable == "N") {
						className = "accn";
					} else {
						className = (count++ % 2 == 0 ? "even" : "odd");
					}
					/*
					 * End:Code Modified By Chinniah on 19-Jul-2012 for
					 * 1054_STL_63 in Mantis 1933:Color Flag not Proper for
					 * SUM='C'
					 */
					//Paint background color
					rows[i].className = className;
				}
			}
			//If any record selected before, then make it selected
			if (selectedRowIndex != -1) {
				rows[selectedRowIndex].className = "selectrow";
			}
		}
		var tooltipEntityId;
		var tooltipCurrencyCode;
		var tooltipFacilityId;
		var tooltipSelectedDate;
		var tooltipSelectedAccount;
		var tooltipOtherParams = [];
		function openAlertingWindow(){
			var requestURL = "acctbreakdownmonitor.do";
			requestURL += "?method=openAlerting";
			tooltipEntityId= document.getElementById("acctBreakdownMonitor.entityId").value;
			tooltipCurrencyCode= document.getElementById("acctBreakdownMonitor.currencyCode").value;
			tooltipFacilityId='ACCOUNT_BRK_MONITOR_ACCOUNT_ROW';
			tooltipSelectedDate= document.getElementById("acctBreakdownMonitor.valueDateAsString").value;
			tooltipSelectedAccount= lastSelectedRow.cells[2].innerText.trim();
			var cbCurrencyThreshold = document.getElementById("acctBreakdownMonitor.applyCurrencyThreshold");

		    var tooltipOtherParams = new Array(); // || var  arr  = [];
		    tooltipOtherParams['currencythresholdFlag'] = (cbCurrencyThreshold.checked ? "Y" : "N");

			requestURL += "&tooltipEntityId="+tooltipEntityId;
			requestURL += "&tooltipCurrencyCode="+tooltipCurrencyCode;
			requestURL += "&tooltipFacilityId="+tooltipFacilityId;
			requestURL += "&tooltipSelectedDate="+tooltipSelectedDate;
			requestURL += "&tooltipSelectedAccount="+tooltipSelectedAccount;
			requestURL += "&tooltipOtherParams="+tooltipOtherParams.toString();
			openWindow (requestURL, 'openAlertingJSP','left=10,top=230,width=450,height=480,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		}

		function paramsFromParent(){
			  var params = { facilityId: tooltipFacilityId, selectedNodeId:null, treeLevelValue:null ,
				      tooltipCurrencyCode :tooltipCurrencyCode , tooltipEntityId:tooltipEntityId,tooltipSelectedDate:tooltipSelectedDate,tooltipSelectedAccount: tooltipSelectedAccount,
				      tooltipOtherParams:tooltipOtherParams};
		    return params;
		}

		/*
		 * Start:Code Modified By Chinniah on 28-May-2012 for Mantis
		 * 1933:Account Monitor: Screen hangs with "Function returned without
		 * value" if closed accounts
		 */
		/**
		 * This function enables/disables buttons based on grid record selected
		 *
		 * @param rowElement
		 * @param isSelected
		 */
		 var lastSelectedRow = null;
		function onSelectTableRow(rowElement, isSelected) {
// 		    openAlertingWindow(rowElement);
			//Disable buttons, they will be enabled based on row selection && access right
			disableSumButton();
			disableMoveButton();
			updateGridColors();
			//Get access right for selected currency
			var accessRight = getCurrencyAccessRight(rowElement);
			//Based on the value enable/disable fields
			if (isSelected && accessRight == "0") {
				lastSelectedRow = rowElement;
				sumFlag = rowElement.cells[7].innerText.trim();
				accountStatus = rowElement.cells[8].innerText.trim();
				selectedAccount = rowElement.cells[2].innerText.trim();
				var isLoroOrCurrAcct = rowElement.getElementsByTagName("input")[0].value;
				loroToPredictedFlag = rowElement.getElementsByTagName("input")[1].value;

				//check if user has full menu access
				if(menuAccessId==0){
				//Enable sum button except "C" account
				if (sumFlag != "C" && accountStatus != "C") {
					enableSumButton();
				}
				}
				//Enable move button only for "Loro" && "Current" accounts
				if (isLoroOrCurrAcct == "Y" && accountStatus != "C") {
					enableMoveButton();
				}
			}
			if (isSelected) {
				// window.opener.savedValuesFromScreens["maintenanceLogMainSeqNo"] = rowElement.getElementsByTagName("input")[5].value;
				setStoredParam('accountSelected', rowElement.cells[2].innerText
						.trim());
			} else
				setStoredParam('accountSelected', '');

		}
		/*
		 * End:Code Modified By Chinniah on 28-May-2012 for Mantis
		 * 1933:Account Monitor: Screen hangs with "Function returned without
		 * value" if closed accounts
		 */
		/**
		 * This function return access right of selected currency
		 *
		 * @param rowElement
		 * @return String - access right in string format
		 */
		function getCurrencyAccessRight(rowElement) {
			if (document.getElementById("acctBreakdownMonitor.currencyCode").value == "All") {
				//Form request body to check access right
				var requestBody = requestParam("entityId", document
						.getElementById("acctBreakdownMonitor.entityId").value)
						+ requestParam("currencyId",
								rowElement.cells[1].innerText);
				//Create AJAX request object
				oXHR = zXmlHttp.createRequest();
				//Set request properties, like request method, url, content type
				oXHR.open("POST", "currency.do?method=checkCurrencyAccess",
						false);
				oXHR.setRequestHeader("Content-Type",
						"application/x-www-form-urlencoded");
				//Send request parameters in a body of the request as this is a POST request
				oXHR.send(requestBody);
				//Get access right
				return oXHR.responseText;
			} else {
				return menuEntityCurrGrpAccess;
			}
		}

		/**
		 * This function refreshes parent screen
		 */
		function refreshParent() {
			opener.refreshPending = true;
		}

		/**
		 * This function enables given button. That means show enabled button
		 * && hides disabled button
		 *
		 * @param btnName
		 * @param btnDisabledName
		 */
		function enableButton(btnName, btnDisabledName) {
			document.getElementById(btnName).style.visibility = "visible";
			document.getElementById(btnDisabledName).style.visibility = "hidden";
		}

		/**
		 * This function enables given button. That means show disabled button
		 * && hides enabled button
		 *
		 * @param btnName
		 * @param btnDisabledName
		 */
		function disableButton(btnName, btnDisabledName) {
			document.getElementById(btnName).style.visibility = "hidden";
			document.getElementById(btnDisabledName).style.visibility = "visible";
		}

		/**
		 * This function enables Refresh button
		 */
		function enableRefreshButton() {
			enableButton('btnRefresh', 'btnRefreshDisabled');
		}

		/**
		 * This function disables Refresh button
		 */
		function disableRefreshButton() {
			disableButton('btnRefresh', 'btnRefreshDisabled');
		}

		/**
		 * This function enables Sum button
		 */
		function enableSumButton() {
			enableButton('btnSum', 'btnSumDisabled');
		}

		/**
		 * This function disables Sum button
		 */
		function disableSumButton() {
			disableButton('btnSum', 'btnSumDisabled');
		}

		/**
		 * This function enables Move button
		 */
		function enableMoveButton() {
			enableButton('btnMove', 'btnMoveDisabled');
		}

		/**
		 * This function disables Move button
		 */
		function disableMoveButton() {
			disableButton('btnMove', 'btnMoveDisabled');
		}

		/**
		 * This function enables Export select
		 */
		function enableGridExport() {
			$("#exportReport").disabled(false);
		}

		/**
		 * This function disables Export select
		 */
		function disableGridExport() {
			$("#exportReport").disabled(true);
		}

		/**
		 * This function returns grid table container object
		 *
		 * @return Object - Grid table container
		 */
		function getGridTable() {
			return document.getElementById('ddscrolltable');
		}

		/**
		 * This function will be invoked when, system sends AJAX request to server. This
		 * function disables controls.
		 */
		function onStart() {
			//Set busy flag
			isBusy = true;
			//Stop refresh timer, it will be start after refresh
			stopRefreshTimer();
			//Get grid information like sort status, filter status && scroll position
			getGridInfo();
			//Hide tooltip
			resetTooltip();
			//Disable search controls && tabs
			disableEnableControls(true);
			document.getElementById("ddimagetabs").disabled = true;
			//Disable buttons
			disableRefreshButton();
			disableSumButton();
			disableMoveButton();
			//Disable grid export controls
			disableGridExport();
			//Show loading image
			document.getElementById('pnlRefreshIcon').style.visibility = "visible";
			//Hide data build message
			document.getElementById('pnlDataBuild').style.visibility = "hidden";
		}

		/**
		 * This function will be invoked when, when system receives AJAX response from
		 * server. This function enables controls.
		 */
		function onStop() {
			//Rest values
			isBusy = false;
			//To refresh grid after 'n' seconds
			startRefreshTimer();
			//Reset grid information
			resetGridInfo();
			//Enable search controls && tabs
			disableEnableControls(false);
			changeCurrencyThresholdStatus();
			document.getElementById("ddimagetabs").disabled = false;
			//Enable buttons
			enableRefreshButton();
			//Hide loading image
			document.getElementById('pnlRefreshIcon').style.visibility = "hidden";
			//Enable grid export controls
			if (record() > 0) {
				enableGridExport();
			}
		}

		/**
		 * This method either disables/enables search fields based on the given flag
		 *
		 * @param disableEnableFlag
		 */
		function disableEnableControls(disableEnableFlag) {
			var objForm = getControlForm();
			//Iterate through all form elements && disable/enable controls
			for (i = 0; i < objForm.elements.length; i++) {
				objForm.elements[i].disabled = disableEnableFlag;
			}
		}

		/**
		 * This function forms request body. Each request parameter will be formed as key/value
		 * pair separated by "=" && earh parameter will be separated by "&"
		 *
		 * @param objForm - form object
		 * @return string value - body for request
		 */
		function getRequestBody(objForm) {
			var formBody = "";
			//Iterate through all form elements && form request body
			for (i = 0; i < objForm.elements.length; i++) {
				var formElement = objForm.elements[i];
				//If the control is checkbox then get checked status. According the checkecd status set value as "Y" || "N"
				//For remaining fields get values from the control
				if (formElement.type == "checkbox") {
					formBody += requestParam(formElement.name,
							formElement.checked ? "Y" : "N");
				} else {
					formBody += requestParam(formElement.name,
							formElement.value);
				}
			}
			return formBody;
		}

		/**
		 * This function build request parameter as key/value pair.
		 *
		 * @param key
		 * @param value
		 * @return String value - request parameter
		 */
		function requestParam(key, value) {
			return encodeURIComponent(key) + "=" + encodeURIComponent(value)
					+ "&"
		}

		/**
		 * This function returns form of search criteria
		 *
		 * @return Object - form object
		 */
		function getControlForm() {
			return document.forms[0];
		}

		/**
		 * This function initialize timer to refresh the grid after 'n' seconds
		 */
		function startRefreshTimer() {
			if (refreshRate != 0) {
				//Screen will be refreshed every 'n' seconds
				timerAutoRefresh = window.setTimeout(refreshGrid,
						refreshRate * 1000);
			}
		}

		/**
		 * This function stops refresh timer
		 */
		function stopRefreshTimer() {
			clearTimeout(timerAutoRefresh);
		}

		/**
		 * This method either includes selected account in sum calculation || * exclude selected account from sum calculation
		 */
		function sumIncludeExclude() {
			var title = '';
			if (sumFlag == "Y") {
				ShowErrMsgWindowWithBtn(title,'<fmt:message key="confirm.acctBreakdown.excludeFromTotal"/>',OK_CANCEL, excludeOk);
			} else if (sumFlag == "N") {
				 ShowErrMsgWindowWithBtn(title,'<fmt:message key="confirm.acctBreakdown.includeInTotal"/>',OK_CANCEL, includeOk);
			} else if (sumFlag == "P") {
				 ShowErrMsgWindowWithBtn(title,'<fmt:message key="confirm.acctBreakdown.includeExcludeFromTotal"/>',INCLUDE_EXCLUDE_CANCEL, includeOk, excludeOk);
			}

		}
		function includeOk() {
			updateAccount("Y", "")
		}
		function excludeOk() {
			updateAccount("N", "")
		}

		/**
		 * This function moves loro/current balance to predicted balance && vice-versa
		 * based on the record selected
		 */
		function moveBalance() {
			//Alert window title
			var title = '';
			if (loroToPredictedFlag == "Y") {
				 ShowErrMsgWindowWithBtn(title,'<fmt:message key="confirm.acctBreakdown.moveToLoro"/>',OK_CANCEL, loroToPredictN);

			} else if (loroToPredictedFlag == "N") {
				ShowErrMsgWindowWithBtn(title,'<fmt:message key="confirm.acctBreakdown.moveFromLoro"/>',OK_CANCEL, loroToPredictY);
			}

		}
		function loroToPredictN() {
			updateAccount("", "N")
		}
		function loroToPredictY() {
			updateAccount("", "Y")
		}

		/**
		 * This function performs two type of operation
		 * - Either includes selected account in sum calculation || exclude
		 *   selected account from sum calculation
		 * - Move loro/current balance to predicted balance && vice-versa
		 *
		 * @param sumFlagValue
		 * @param loroToPredictedFlagValue
		 */
		function updateAccount(sumFlagValue, loroToPredictedFlagValue) {
			refreshParent();

			//Form request body to update account details
			var requestBody = requestParam("entityId", document
					.getElementById("acctBreakdownMonitor.entityId").value)
					+ requestParam("accountId", selectedAccount)
					+ requestParam("sumFlag", sumFlagValue)
					+ requestParam("loroToPredicted", loroToPredictedFlagValue);
			//Create AJAX request object
			oXHR = zXmlHttp.createRequest();
			//Set request properties, like request method, url, content type
			oXHR.open("POST", "acctbreakdownmonitor.do?method=updateAccount",
					false);
			oXHR.setRequestHeader("Content-Type",
					"application/x-www-form-urlencoded");
			//Send request parameters in a body of the request as this is a POST request
			oXHR.send(requestBody);
			//Get response from server
			var response = oXHR.responseText;
			//Check whether the request is processed successfully.
			if (response == "") {
				//Request is processed successfully. So refresh the grid
				refresh();
			} else {
				//Error occurs, display the error message to the user.
				alert(response);
			}
		}

		/**
		 * This function performs two type of operation
		 * - First checks movements available for selected account,
		 *   using AJAX request
		 * - If movements available then open movement screen
		 *
		 * @param balanceType
		 */
		function openMovement(balanceType,e) {
			 var event = (window.event|| e);
			 var target = (event.srcElement || event.target);
			if (validateValueDate()) {
				//Clicked row element
				var trElement = target.parentElement.parentElement;
				//Form request body to get no of movements
				var requestBody = requestParam("entityId", document
						.getElementById("acctBreakdownMonitor.entityId").value)
						+ requestParam("currencyCode",
								trElement.cells[1].innerText.trim())
						+ requestParam("accountId",
								trElement.cells[2].innerText.trim())
						+ requestParam(
								"valueDate",
								document
										.getElementById("acctBreakdownMonitor.valueDateAsString").value)
						+ requestParam("balanceType", balanceType)
						+ requestParam("applyCurrencyThreshold", "N");
				//Create AJAX request object
				oXHR = zXmlHttp.createRequest();
				//Set request properties, like request method, url, content type
				oXHR.open("POST",
						"acctbreakdownmonitor.do?method=getMovementCount",
						false);
				oXHR.setRequestHeader("Content-Type",
						"application/x-www-form-urlencoded");
				//Send request parameters in a body of the request as this is a POST request
				oXHR.send(requestBody);
				//Get response from server
				var response = oXHR.responseText;
				var noOfMovements;
				if (isNaN(response)) {
					noOfMovements = -1;
				} else {
					noOfMovements = new Number(response);
				}

				if (noOfMovements == -1) {
					//Error occurs, show error message
					alert(response);
				} else if (noOfMovements == 0) {
					//No movements available for the selected account
					alert('<fmt:message key="alert.acctBreakdown.noMovements"/>');
				} else {
					//Movements available, so open movement screen
					var openUnExpectedURL = getUrl()
							+ "/outstandingmovement.do?method=flexAccount&initialinputscreen=accountbreakdownmonitor&"
							+ requestBody;
					openWindow(
							openUnExpectedURL,
							'startingbalancelog',
							'left=100, top=140, width=1280, height=685, toolbar=0, resizable=yes, scrollbars=yes, status=yes',
							'true');
				}
				if (window.event){
					window.event.returnValue = false;
				}else {
					event.preventDefault();
				}
			}
		}

		/**
		 * This function opens movement screen for selected account && predicted balance
		 *
		 * @param element
		 */
		function clickPredictedLink(element,e) {
			openMovement(document
					.getElementById("acctBreakdownMonitor.balanceType").value,e);
		}

		/**
		 * This function opens movement screen for selected account && unexpected balance
		 *
		 * @param element
		 */
		function clickUnexpectedLink(element,e) {
			openMovement("OpenUnexpected",e);
		}

		/**
		 * This function opens starting balance log screen for selected account && * SOD balance
		 *
		 * @param element
		 */
		function clickSODLink(element,e) {
			 var event = (window.event|| e);
			var target = (event.srcElement || event.target);
			if (validateValueDate()) {
				//Clicked row element
				var trElement = target.parentElement.parentElement;
				// Form url to open starting balance log screen
				var url = getUrl()
						+ "/balanceLog.do?"
						+ requestParam(
								"entityCode",
								document
										.getElementById("acctBreakdownMonitor.entityId").value)
						+ requestParam("selectedBalTypeId",
								trElement.cells[2].innerText.trim())
						+ requestParam("selectedBalTypeName",
								trElement.cells[3].innerText)
						+ requestParam("selectedBalType", "C")
						+ requestParam(
								"selectedDate",
								document
										.getElementById("acctBreakdownMonitor.valueDateAsString").value);
				// Open starting balance log screen
				openWindow(
						url,
						'startingbalancelog',
						'left=100, top=140, width=1270, height=575, toolbar=0, resizable=yes, scrollbars=yes, status=yes',
						'true');
				if (window.event){
					window.event.returnValue = false;
				}else {
					event.preventDefault();
				}
			}
		}

		/**
		 * This function exports grid data
		 *
		 * @param exportType
		 */
		function exportData(exportType) {
			if (validateValueDate()) {
				//Get criteria form
				var objForm = getControlForm();
				//Set method name
				objForm.method.value = "export";
				//Set export type
				objForm.exportType.value = exportType.trim();
				//Set account class name
				var acctClass = document
						.getElementById("acctBreakdownMonitor.acctClass");
				objForm.acctClassName.value = acctClass.options[acctClass.selectedIndex].text;
				//Set value for hide zero balance && apply currency threshold checkboxes
				setCheckboxValue(
						document.getElementById("applyCurrencyThreshold"),
						document
								.getElementById("acctBreakdownMonitor.applyCurrencyThreshold"));
				setCheckboxValue(
						document.getElementById("hideZeroBalances"),
						document
								.getElementById("acctBreakdownMonitor.hideZeroBalances"));

				//Export grid data
				objForm.submit();
			}
		}

		/**
		 * This function sets "Y" if checkbox is selected, otherwise sets "N"
		 *
		 * @param targetElement
		 * @param formElement
		 */
		function setCheckboxValue(targetElement, formElement) {
			targetElement.value = formElement.checked ? "Y" : "N";
		}
		function exportReport() {
			var type = $("#exportReport").getSelected(0);
			return exportData(type.toLowerCase());
		}
	</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();bodyOnLoad();setTitleSuffix(getControlForm());"
	onunload="call();">

    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<form action="acctbreakdownmonitor.do" target="ifmReport" method="post">
    <%-- START: DISPLAY CALENDAR --%>
    <div id="pnlCal"
        style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
    <iframe id="calFrame" src="javascript:false;" scrolling="no"
        frameborder="0"
        style="position: absolute; top: 0px; left: 0px; display: none;"></iframe>
    <%-- END: DISPLAY CALENDAR --%>

    <%-- START: REPORT --%>
    <iframe id="ifmReport" name="ifmReport" style="display: none;"></iframe>
    <input type="hidden" name="method">
    <input type="hidden" name="exportType">
    <input type="hidden" name="acctClassName">
    <input type="hidden" name="applyCurrencyThreshold">
    <input type="hidden" name="hideZeroBalances">
    <input type="hidden" name="acctBreakdownMonitor.existingEntityId">
    <input type="hidden" name="screen" value="<fmt:message key="title.acctBreakdown.window" />" />
    <%-- END: REPORT --%>

    <%-- START: ACCOUNT MONITOR FIELDS (SEARCH CRITERIA) --%>
    <div id="pnlControls"
        style="position: absolute; left: 10px; top: 10px; width: 1168px; height: 116px; visibility: visible; border: 2px outset;">
        <div id="pnlControlsInner"
            style="position: absolute; left: 8px; top: 4px; width: 540px; height: 20px;">
            <table width="1150px" border="0" cellpadding="0" cellspacing="0" height="90px" style="font-family:verdana, helvetica; font-size: 9pt;">
                <tr height="25px">
                    <%-- ENTITY --%>
                    <td width="120px"><b><fmt:message key="label.acctBreakdown.entity" /></b></td>
                    <td width="240px">
                        <select
                            id="acctBreakdownMonitor.entityId"
                            name="acctBreakdownMonitor.entityId"
                            class="htmlTextAlpha"
                            onmouseup="javascript: return validateValueDate();"
                            onkeydown="javascript: return validateValueDate();"
                            onchange="onDataChange('entityChanged')"
                            style="width:240px"
                            title="<fmt:message key="tooltip.selectEntityId" />"
                            tabindex="1"
                        >
                            <c:forEach items="${requestScope.entities}" var="entity">
                               <option value="${entity.value}" <c:if test="${acctBreakdownMonitor.entityId == entity.value}">selected</c:if>>${entity.label}</option>
                            </c:forEach>
                        </select>
                    </td>
                    <td width="5px">&nbsp;</td>
                    <td width="540px"><span id="entityName" name="entityName" class="spantext"></span></td>
                    <td width="5px">&nbsp;</td>
                    <%-- DATE --%>
                    <td id="tdDate" width="240px" align="right">
                        <b style="height: 18px"><fmt:message key="label.acctBreakdown.date" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b>
                        <input type="text"
                            name="acctBreakdownMonitor.valueDateAsString"
                            id="acctBreakdownMonitor.valueDateAsString"
                            value="${acctBreakdownMonitor.valueDateAsString}"
                            class="htmlTextNumeric"
                            maxlength="10"
                            tabindex="2"
                            title="<fmt:message key="tooltip.enterValueDate" />"
                            style="width:81px;margin-bottom: 4px;height: 22px;"
                            onblur="onDateChange(this);"
                            onkeydown="onDateKeyPress(this,event);"
                        />
                        <a name="datelink" id="datelink" style="margin-left: 1px; margin-top: 2px"
                            onClick="cal.select(document.getElementById('acctBreakdownMonitor.valueDateAsString'),'datelink',dateFormatValue);dateSelected=true;refreshFlag=false;return false;">
                            <img
                            title='<fmt:message key="tooltip.clickValueDate"/>'
                            onClick="refreshFlag=false;"
                            src="images/calendar-16.gif">
                        </a>
                    </td>
                </tr>
                <tr height="25px">
                    <%-- CURRENCY --%>
                    <td><b><fmt:message key="label.acctBreakdown.currency" /></b></td>
                    <td>
                        <div id="ddCurrencyCode">
                            <select
                                id="acctBreakdownMonitor.currencyCode"
                                name="acctBreakdownMonitor.currencyCode"
                                onchange="onDataChange('currencyChanged')"
                                onmouseup="javascript: return validateValueDate();"
                                onkeydown="javascript: return validateValueDate();"
                                class="htmlTextAlpha"
                                tabindex="3"
                                style="width:70px"
                                title="<fmt:message key="tooltip.selectCurrencyCode" />"
                            >
                                <c:forEach items="${requestScope.currencies}" var="currency">
                                <option value="${currency.value}" <c:if test="${acctBreakdownMonitor.currencyCode == currency.value}">selected</c:if>>${currency.label}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </td>
                    <td>&nbsp;</td>
                    <td><span id="currencyDesc" name="currencyDesc" class="spantext"></span></td>
                    <td>&nbsp;</td>
                    <%-- BALANCE --%>
                    <td align="right">
                        <b style="padding-right: 8px;"><fmt:message key="label.acctBreakdown.balance" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b>
                        <select
                            id="acctBreakdownMonitor.balanceType"
                            name="acctBreakdownMonitor.balanceType"
                            onchange="refresh();"
                            onmouseup="return validateValueDate();"
                            onkeydown="return validateValueDate();"
                            class="htmlTextAlpha"
                            tabindex="4"
                            style="width:140px"
                            title="<fmt:message key="tooltip.selectBalanceType" />"
                        >
                            <c:forEach items="${requestScope.balanceTypes}" var="balanceType">
                                <option value="${balanceType.value}" <c:if test="${acctBreakdownMonitor.balanceType == balanceType.value}">selected</c:if>>${balanceType.label}</option>
                            </c:forEach>
                        </select>
                    </td>
                </tr>
                <tr height="25px">
                    <%-- ACCOUNT CLASS --%>
                    <td><b><fmt:message key="label.acctBreakdown.acctClass" /></b></td>
                    <td>
                        <div id="ddAcctClass">
                        <select
                            id="acctBreakdownMonitor.acctClass"
                            name="acctBreakdownMonitor.acctClass"
                            onchange="onDataChange('acctClassChanged')"
                            onmouseup="return validateValueDate();"
                            onkeydown="return validateValueDate();"
                            class="htmlTextAlpha"
                            tabindex="5"
                            style="width:240px"
                            title="<fmt:message key="tooltip.selectAccountClass" />"
                        >
                            <c:forEach items="${requestScope.accountClasses}" var="accountClass">
                                <option value="${accountClass.value}" <c:if test="${acctBreakdownMonitor.acctClass == accountClass.value}">selected</c:if>>${accountClass.label}</option>
                            </c:forEach>
                        </select>
                        </div>
                    </td>
                    <td>&nbsp;</td>
                    <td><span id="accountClass" name="accountClass" class="spantext"></span></td>
                    <td>&nbsp;</td>
                    <%-- HIDE ZERO BALANCE RECORDS --%>
                    <td align="right">
                        <b style="padding-right: 8px;"><fmt:message key="label.acctBreakdown.hideZeroBalances" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b>
                        <input type="checkbox"
                            name="acctBreakdownMonitor.hideZeroBalances"
                            value="Y"
                            style="width:13px;"
                            class="htmlTextAlpha"
                            title="<fmt:message key="tooltip.hideZeroBalances" />"
                            tabindex="6"
                            onclick="javascript: return refreshOnCheckboxChange();"
                            ${requestScope.acctBreakdownMonitor.hideZeroBalances == 'Y' ? 'checked' : ''}
                        />
                    </td>
                </tr>
                <tr height="25px">
                    <%-- ACCOUNTS --%>
                    <td><b><fmt:message key="label.acctBreakdown.acct" /></b></td>
                    <td>
                        <div id="ddAcctId">
                            <select
                                id="acctBreakdownMonitor.acctId"
                                name="acctBreakdownMonitor.acctId"
                                class="htmlTextAlpha"
                                tabindex="7"
                                title="<fmt:message key="tooltip.selectAccountId" />"
                                style="width:240px"
                                onmouseup="javascript: return validateValueDate();"
                                onkeydown="javascript: return validateValueDate();"
                                onchange="javascript: refresh();"
                            >
                                <c:forEach items="${requestScope.accountIds}" var="account">
                                    <option value="${account.value}" <c:if test="${acctBreakdownMonitor.acctId == account.value}">selected</c:if>>${account.label}</option>
                                </c:forEach>
                            </select>
                        </div>
                    </td>
                    <td>&nbsp;</td>
                    <td><span id="acctName" name="acctName" class="spantext"></span></td>
                    <td>&nbsp;</td>
                    <%-- APPLY CURRENCY THRESHOLD --%>
                    <td align="right">
                        <b style="padding-right: 8px;"><fmt:message key="label.acctBreakdown.applyCurrencyThreshold" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b>
                        <input type="checkbox"
                            name="acctBreakdownMonitor.applyCurrencyThreshold"
                            id="acctBreakdownMonitor.applyCurrencyThreshold"
                            value="Y"
                            style="width:13px;"
                            title="<fmt:message key="tooltip.applyCurrencyThreshold" />"
                            class="htmlTextAlpha"
                            tabindex="8"
                             ${requestScope.acctBreakdownMonitor.applyCurrencyThreshold == 'Y' ? 'checked' : ''}
                            onclick="javascript: return refreshOnCheckboxChange();"
                        />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <%-- END: ACCOUNT MONITOR FIELDS (SEARCH CRITERIA) --%>
</form>

<%-- START: 7 DAYS TABS --%>
	<div id="ddimagetabs"
		style="position: absolute; left: 10px; top: 132px; width: 832px; height: 30px;">
		<%
			int tabIndex = 9;
		%>
		<c:forEach items="${requestScope.tabDates}" var="tabInfo">
			<a id="${tabInfo[0]}"
				onmouseout="revertback('${tabInfo[0]}',this);"
				onmouseover="changecontent('${tabInfo[0]}',this)"
				onKeyDown="submitEnter(this,event)" tabindex="<%= tabIndex++ %>"
				onClick="checkTabIndex(this);"><b>${tabInfo[1]}</b></a>
		</c:forEach>

	</div>
	<div id="pnlTabLine"
		style="position: absolute; left: 670px; top: 150px; width: 510px; height: 220px;">
		<table width="505px">
			<tr>
				<td><img src="images/tabline.gif" width="505px" height="1"></td>
			</tr>
		</table>
	</div>
	<%-- END: 7 DAYS TABS --%>

	<%-- START: TAB CONTAINER --%>
	<div id="pnlTabContainer"
		style="position: absolute; left: 10px; top: 155px; width: 1168px; height: 480px;">
	</div>
	<%-- END: TAB CONTAINER --%>

	<%-- START: BUTTON BAR --%>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10px; top: 645px; width: 1168px; height: 39px; visibility: visible;">
		<div id="pnlButton"
			style="position: absolute; left: 6px; top: 1px; width: 390px; height: 15px; margin-top: 5px; visibility: visible;">
			<%-- REFRESH BUTTON --%>
			<div id="btnRefresh" style="position: absolute; width: 55px; height: 15px;">
				<a tabindex="21"
					title='<fmt:message key="tooltip.acctBreakdown.refresh" />'
					onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onClick="javascript:refreshGrid()">
					<fmt:message key="button.Refresh" /></a>
			</div>
			<div id="btnRefreshDisabled" style="position: absolute; width: 55px; height: 15px; visibility: hidden;">
				<a class="disabled" disabled="disabled"><fmt:message key="button.Refresh" /></a>
			</div>
			<%-- RATE BUTTON --%>
			<div id="btnRate" style="position: absolute; left: 60px; width: 45px; height: 15px; margin-left:10px;">
				<a tabindex="22" title='<fmt:message key="tooltip.rateButton" />'
					onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onClick="javascript:showRatePopup()"
					style="margin-left:10px;"
					>
					<fmt:message key="button.rate" /></a>
			</div>
			<%-- SUM BUTTON --%>
			<div id="btnSum" style="position: absolute; left: 120px; width: 45px; height: 15px; visibility: hidden; margin-left: 20px;">
				<a tabindex="23" title='<fmt:message key="tooltip.acctBreakdown.sum" />'
					onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onClick="sumIncludeExclude()"
					style="margin-left:15px;">
					<fmt:message key="button.sum" /></a>
			</div>
			<div id="btnSumDisabled" style="position: absolute; left: 120px; width: 45px; height: 15px; margin-left: 20px;">
				<a class="disabled" disabled="disabled" style="margin-left:15px;"><fmt:message key="button.sum" /></a>
			</div>
			<%-- MOVE BUTTON --%>
			<div id="btnMove" style="position: absolute; left: 180px; width: 45px; height: 15px; visibility: hidden; margin-left: 30px;">
				<a tabindex="24" style="margin-left:20px;"
					title="<fmt:message key="tooltip.acctBreakdown.move" />"
					onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onClick="moveBalance()">
					<fmt:message key="button.move" /></a>
			</div>
			<div id="btnMoveDisabled" style="position: absolute; left: 180px; width: 45px; height: 15px; margin-left: 30px;">
				<a class="disabled" disabled="disabled" style="margin-left:20px;"><fmt:message key="button.move" /></a>
			</div>
			<%-- CLOSE BUTTON --%>
			<div id="btnClose" style="position: absolute; left: 240px; width: 45px; height: 15px; margin-left: 40px;">
				<a tabindex="25" id="closeBtn" title='<fmt:message key="tooltip.close" />'
					onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"
					style="margin-left:25px;">
					<fmt:message key="button.close" /></a>
			</div>
		</div>

	</div>
	<%-- END: BUTTON BAR --%>

	<%-- START: IMAGE BUTTONS --%>
	<div id="pnlImageButton"
		style="position: absolute; left: 1075px; top: 653px; width: 90px; height: 21px; margin-top: 5px; visibility: visible;">


		<%-- REPORT ICONS --%>
		<div id="pnlReport" style="position: absolute; left: 12px; top: -1px;  width: 45px; height: 21px; visibility: visible;">
		<table ><div id="exportReport"/></table>
		</div>

		<%-- HELP ICON --%>
		<div id="pnlHelp" style="position: absolute; left: 58px; top: -2px; width: 10px; height: 15px; visibility: visible;">
			<a title='Help Screen Content' tabindex="29" href="#"
				onKeyDown="submitEnter(this,event)"
				onclick="javascript:openWindow(buildPrintURL('print','Account Breakdown Monitor '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onfocusOut="setFocusOnElement('acctBreakdownMonitor.entityId')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
				<img src="images/help_default.GIF " name="Help" border="0"></a>
			</div>
			<%-- PROGRESS(LOADING) IMAGE --%>
			<div id="pnlRefreshIcon" style="position: absolute; left: 80px; top: 1px; width: 10px; height: 15px; visibility: hidden;">
				<img src="images/Loading.gif" name="Loading" border="0">
			</div>
		</div>
		<%-- END: IMAGE BUTTONS --%>

		<%-- START: DATA BUILD IN PROGRESS --%>
		<div id="pnlDataBuild"
			style="position: absolute; left: 475px; top: 653px; width: 410px; height: 15px; visibility: hidden;">
			<b><font color="red"><fmt:message key="monitor.dataInsertionJobRunning" /></font></b>
		</div>
		<%-- END: DATA BUILD IN PROGRESS --%>

		<%-- START: LAST REFRESH TIME --%>
		<div id="pnlRefreshTime"
			style="position: absolute; left: 825px; top: 653px; width: 260px; height: 15px;margin-top: 5px; visibility: visible;">
			<fmt:message key="label.lastRefTime2" />&nbsp;
			<input id="lastRefreshTime" name="lastRefreshTime" class="textAlpha"
				style="width:150px; background: transparent; border: 0;" tabindex="-1" readonly size="14">
		</div>
		<%-- END: LAST REFRESH TIME --%>

		<%-- START: REFRESH RATE SCREEN --%>
		<div id="blanket"
			style="position: absolute; left: 0px; top: 0px; width: 1180px; height: 695px; display: none;"></div>
		<jsp:include page="/jsp/work/refreshrate.jsp" />
		<%-- END: REFRESH RATE SCREEN --%>
</body>
</body>
</html>