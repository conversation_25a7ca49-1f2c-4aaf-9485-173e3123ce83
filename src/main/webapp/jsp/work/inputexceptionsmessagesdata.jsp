<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<inputexceptions
		from="${requestScope.fromDate}"
		refresh="${requestScope.autoRefreshRate}"
		to="${requestScope.toDate}"
		dateformat="${requestScope.session.CDM.dateFormatValue}"
		dateComparing="${requestScope.dateComparing == 'Y' ? 'true' : 'false'}"
		sysDateFrmSession="${requestScope.sysDateFrmSession}"
		lastRefTime="${requestScope.lastRefTime}"
		sessionToDate="${requestScope.sessionToDate}"
>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>

	<grid>
		<metadata>
			<datapage
					records_total="${requestScope.page.totalSize}"
					records_thispage="${requestScope.page.pageSize}"
					records_eachpage="${requestScope.page.recordsPerPage}"
					page_count="${requestScope.page.totalPages}"
					page_number="${requestScope.page.pageNumber}"
			/>

			<columns>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.messageid' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="msgid"
						width="${requestScope.column_width['msgid']}"
				/>
				<c:if test="${requestScope.fromDashboard == 'yes'}">
					<column
							heading="<fmt:message key='inputexceptions.messages.header.messageType' />"
							draggable="false"
							filterable="false"
							sort="true"
							type="str"
							dataelement="messagetype"
							width="${requestScope.column_width['messagetype']}"
					/>
				</c:if>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.statusnotes' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="str"
						dataelement="statusnotes"
						width="${requestScope.column_width['statusnotes']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.inputdate' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="str"
						dataelement="inputdate"
						width="${requestScope.column_width['inputdate']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.valuedate' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="str"
						dataelement="valuedate"
						width="${requestScope.column_width['valuedate']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.currency' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="str"
						dataelement="currency"
						width="${requestScope.column_width['currency']}"
				/>
				<column
						heading="<fmt:message key='inputexceptions.messages.header.amount' />"
						draggable="false"
						filterable="false"
						sort="true"
						type="num"
						dataelement="amount"
						width="${requestScope.column_width['amount']}"
				/>
				<c:if test="${requestScope.fromPCM}">
					<column
							heading="<fmt:message key='inputexceptions.messages.header.accountId' />"
							draggable="false"
							filterable="false"
							sort="true"
							type="str"
							dataelement="accountid"
							width="${requestScope.column_width['account']}"
					/>
				</c:if>
				<c:if test="${not requestScope.fromPCM}">
					<column
							heading="<fmt:message key='inputexceptions.messages.header.sign' />"
							draggable="false"
							filterable="false"
							sort="true"
							type="str"
							dataelement="sign"
							width="${requestScope.column_width['sign']}"
					/>
				</c:if>
				<c:choose>
					<c:when test="${requestScope.fromPCM}">
						<column
								heading="<fmt:message key='inputexceptions.messages.header.reference' />"
								draggable="false"
								filterable="false"
								type="str"
								dataelement="ref1"
								sort="true"
								width="${requestScope.column_width['ref1']}" />
					</c:when>
					<c:otherwise>
						<column
								heading="<fmt:message key='inputexceptions.messages.header.reference1' />"
								draggable="false"
								filterable="false"
								type="str"
								dataelement="ref1"
								sort="true"
								width="${requestScope.column_width['ref1']}" />
					</c:otherwise>
				</c:choose>
			</columns>
		</metadata>

		<rows size="${requestScope.page.totalSize}">
			<c:forEach items="${requestScope.page.currentPageData}" var="record">
				<row>
					<msgid clickable="false">
						<c:out value="${record.seqNo}" />
					</msgid>
					<c:if test="${requestScope.fromDashboard == 'yes'}">
						<messagetype clickable="false">
							<c:out value="${record.messageType}" />
						</messagetype>
					</c:if>
					<statusnotes clickable="false">
						<c:out value="${record.statusNotes}" />
					</statusnotes>
					<inputdate clickable="false">
						<c:out value="${record.startTimeAsString}" />
					</inputdate>
					<valuedate clickable="false">
						<c:out value="${record.valueDateAsString}" />
					</valuedate>
					<currency clickable="false">
						<c:out value="${record.currencyCode1}" />
					</currency>
					<c:if test="${requestScope.fromPCM}">
						<accountid clickable="false">
							<c:out value="${record.accountId}" />
						</accountid>
					</c:if>
					<c:if test="${not requestScope.fromPCM}">
						<sign clickable="false">
							<c:out value="${record.amountSign1}" />
						</sign>
					</c:if>
					<amount clickable="false">
						<c:out value="${record.amount1AsString}" />
					</amount>
					<ref1 clickable="false">
						<c:out value="${requestScope.fromPCM ? record.transactionReferenceNumber3 : record.transactionReferenceNumber1}" />
					</ref1>
				</row>
			</c:forEach>
		</rows>
	</grid>

	<selects>
		<select id="messageTypeList">
			<c:forEach items="${requestScope.messageTypeList}" var="messageType">
				<option value="${messageType.value}" selected="${requestScope.selectedMessageType == messageType.value ? '1' : '0'}">
					<c:out value="${messageType.label}" />
				</option>
			</c:forEach>
		</select>
	</selects>
</inputexceptions>
