<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="cpyFromManualInput.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
var selectedMovementId = '${requestScope.selectedMovementId}';
var selectedEntityId = '${requestScope.selectedEntityId}';
mandatoryFieldsArray=["*"];
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

	self.close();
	window.opener.opener.document.forms[0].method.value = "add";
	window.opener.opener.document.forms[0].selectedMovementId.value = selectedMovementId;
	window.opener.opener.document.forms[0].submit();
	window.opener.close();
</c:if>

<c:if test="${requestScope.parentFormRefreshOk == 'yes'}">


	window.opener.document.forms[0].method.value = "add";
	window.opener.document.forms[0].selectedMovementId.value = selectedMovementId;
	window.opener.document.forms[0].selectedEntityId.value = selectedEntityId;
	window.opener.document.forms[0].submit();
	self.close();
</c:if>

<c:if test="${requestScope.inputscreen == 'movementSummary'}">

	window.opener.opener.opener.document.forms[0].method.value = "add";
	window.opener.opener.opener.document.forms[0].selectedMovementId.value = selectedMovementId;
	window.opener.opener.opener.document.forms[0].submit();
	window.opener.opener.close();
	window.opener.close();
	self.close();
</c:if>

function submitForm(methodName)
{
   if(validateField(document.forms[0].elements['movement.id.movementIdAsString'],'movement.id.movementIdAsString','numberPat'))
   {
   if(validateForm(document.forms[0]) )
   {
	document.forms[0].method.value = methodName;
	document.forms[0].selectedEntityId.value = selectedEntityId;
	document.forms[0].submit();
  }
  }else
  {
    document.forms[0].elements['movement.id.movementIdAsString'].focus();
  }

}
<!-- Start:code modified the Alibasha for Mantis 1560 to display proper alignment -->

/**
  *	This function is used checking the  Movement Id
  */
function checkingMovementid()
{
   //variable declare for movementValue for validate movement id
   var movementValue=validateField(document.forms[0].elements['movement.id.movementIdAsString'],'movement.id.movementIdAsString','numberPat');
   if(movementValue)
   {
	 javascript:openWindow(searchmovements('populate'),'SearchWindow','left=50,top=190,width=981,height=660,toolbar=0, resizable=yes, scrollbars=no','true');
   }
   else
   {
    document.forms[0].elements['movement.id.movementIdAsString'].focus();
   }
}
<!-- End:code modified the Alibasha for Mantis 1560 to display proper alignment -->
function buildMovement(methodName){
        var param = 'movement.do?method='+methodName+'&selectedMovementId=';
		param +=document.forms[0].elements["movement.id.movementIdAsString"].value;
		param += '&screenName=' + '<fmt:message key="cpyFromManualInput.copyFrm"/>';
		return  param;
}


function searchmovements(methodName)
{
	var param = 'movementsearch.do?method='+methodName;
	param += '&initialinputscreen=movementSearch' ;
	return param;
}

function validateForm(objForm){
  var elementsRef = new Array(1);
 elementsRef[0] = objForm.elements["movement.id.movementIdAsString"];
  return validate(elementsRef);
}

function setAttribDisplay(methodName)
{
	 document.forms[0].clickDisplay.value = "yes";
	 submitForm(methodName);

}
</SCRIPT>


</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="movement.do" method="post">
<input name="method" type="hidden" value="display">
<input name="buttonDiv" type="hidden" value="GBP">
<input name="selectedMovementId" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value="">
<input name="clickDisplay" type="hidden" value="">
<input name="test" value="" style="visibility:hidden">

<div id="MovementDisplay" style="position:absolute; left:20px; top:20px; width:365px; height:36px; border:2px outset;" color="#7E97AF">
	<div id="MovementDisplay" style="position:absolute;left:8px; top:4px; width:325px; height:90px;">
		<table width="223px" border="0" cellpadding="0" cellspacing="0" height="25px">
			<tr height="25px">
				<td width="75px"><b><fmt:message key="manualInput.id.movementId"/></b>*</td>
			    <td width="28px">&nbsp;</td>
				<td width="120px">
					<input type="text" tabindex="1" titleKey="tooltip.enterMvmId" class="htmlTextNumeric"
						   name="movement.id.movementIdAsString" maxlength="12"
						   onchange="return validateField(this,'movementId','numberPat');"
						   style="width:120px;"
						   value="${movement.id.movementIdAsString}" />

				</td>
			</tr>
		</table>
	</div>
</div>
<div id="MovementDisplay" style="position:absolute; left:320; top:69px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Copy From Manual Input '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
		</td>
			<td align="right" id="Print">
				<a onKeyDown="submitEnter(this,event)" onclick="printPage();" tabindex="6" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:60; width:365px; height:39px; visibility:visible;">
	<div id="MovementDisplay" style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:visible;">
		<table border="0" cellspacing="0"  cellpadding="0"  width="280px">
			<tr>
			
			<!-- Ok Button -->
				<td>
					<a tabindex="2" title='<fmt:message key="tooltip.ok"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 
onKeyDown="submitEnter(this,event)" onclick="javascript:submitForm('copy');"><fmt:message key="button.ok"/></a>
				</td>

			<!-- Search Button -->
				<td>
					<a tabindex="3" title='<fmt:message key="tooltip.searchMvmId"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="checkingMovementid();"><fmt:message key="button.search"/></a>
				</td>

			<!-- Display Button -->
				<td>
					<a tabindex="4" title='<fmt:message key="tooltip.displayMvmDetails"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:setAttribDisplay('copy');"><fmt:message key="button.display"/></a>
				</td>

			<!-- Cancel Button -->
				<td id="cancelbutton">
				<a tabindex="5" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a>
				</td>
			</tr>
		</table>
	</div>
</div>
<!-- Start:code modified the Alibasha for Mantis 1560 to display proper alignment -->
<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.movementAvailable == 'no'}">
    ShowErrMsgWindowWithBtn('', '<fmt:message key="cpyFromManualInput.alert.mvmNotOnFile"/>', null);
</c:if>

<c:if test="${requestScope.movementAvailable == 'yes'}">
    javascript:window.open(buildMovement('displayMovement'), 'movementDisplayWindow', 'left=170,top=210,width=994,height=789,toolbar=0, resizable=yes, scrollbars=yes');
</c:if>

<c:if test="${requestScope.entityAccessNotAvailable == 'yes'}">
    ShowErrMsgWindowWithBtn('', '<fmt:message key="cpyFromManualInput.alert.rights"/>', null);
</c:if>

<c:if test="${requestScope.entityAccessNotAvailable != 'yes'}">
    <c:if test="${requestScope.ccyGrpAccessNotAvailable == 'yes'}">
        ShowErrMsgWindowWithBtn("", '<fmt:message key="cpyFromManualInput.alert.rightsCcyGrp"/>', null);
    </c:if>
</c:if>


</SCRIPT>


<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
		
</form>
</body>
</html>