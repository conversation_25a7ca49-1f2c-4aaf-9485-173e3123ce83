<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>
<script type="text/javascript" src="js/preloadtabs.js"></script>
<script type="text/javascript" src="js/commonJS.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/sortabletable.js"></script>
<script type="text/javascript" src="js/select.js"></script>
<script type="text/javascript" src="js/filter.js"></script>
<script type="text/javascript" src="js/dialog.js"></script>
<script type="text/javascript" src="js/excel.js"></script>
<script type="text/javascript" src="js/datavalidation.js"></script>
<script type="text/javascript" src="js/calendar.js"></script>
<script type="text/javascript" src="js/tabnew.js"></script>
<script type="text/javascript" src="js/zxml.js"></script>
<script type="text/javascript" src="js/jquery-3.6.0.min.js"></script>
<script type="text/javascript" src="js/swt-jquery.extension.js"></script>
<script type="text/javascript" src="js/moment.min.js"></script>
<link rel="stylesheet" type="text/css" href="style/Style.css">
<link rel="stylesheet" type="text/css" href="style/excel.css">
<link rel="stylesheet" type="text/css" href="style/tabstylenew.css">
<link rel="stylesheet" type="text/css" href="style/calendar.css">
<!-- <msdropdown> -->
<link rel="stylesheet" type="text/css"
	href="ILMHighcharts/combonoxComponent/css/jquery-editable-select.css" />
<script src="ILMHighcharts/combonoxComponent/js/jquery-editable-select.js"></script>
<!-- </msdropdown> -->
<script type="text/javascript">
	function closeAlertPopup() {
		parent.getFlashObject("mySwf").closeAlertWindowFromCallBack();
	}

	function GetURLParameter(sParam) {
		var sPageURL = window.location.search.substring(1);
		var sURLVariables = sPageURL.split('&');
		for (var i = 0; i < sURLVariables.length; i++) {
			var sParameterName = sURLVariables[i].split('=');
			if (sParameterName[0] == sParam) {
				return sParameterName[1];
			}
		}
	}

	function bodyOnLoad() {
		setParentChildsFocus();	
		document.body.onmousedown = function (e) {
			  // Get IE event object
			  e = e || window.event;
			  // Get target in W3C browsers & IE
			  var elementId = e.target ? e.target.id : e.srcElement.id;
			  
			  if(elementId!= 'es-list' && elementId!='editable-select' ){
				  $('#editable-select').editableSelect('hide');
			  }
			}
		populateComboBox(window.opener.saveProfileCollection, window.opener.selectedProfileItem)
	}
	
	function populateComboBox(saveProfileArrayCollection, selectedElement){
		$('#container').empty();
		var selectList = "<select id='editable-select' name='editable-select' style='margin-top:5px; width: 350px'>";
		for (var i = 0; i < saveProfileArrayCollection.length; i++) {
		    selectList += "<option>" + saveProfileArrayCollection[i] + "</option>";
		}
		selectList += "</select>";
		$('#container').html(selectList);
		
		
		$('#editable-select').editableSelect({
			effects : 'slide'
		});
		
		if(selectedElement != null && selectedElement != "") {
			//$('#editable-select').editableSelect('select', selectedElement);
			$('#editable-select').val(selectedElement);
		}

	}


	function minimisePopup() {
		parent.getFlashObject("mySwf").minimisePopupById("saveProfileIFrame");
	}
	
	function saveProfileClickHandler() {
		parent.getFlashObject("mySwf").saveProfileClickHandlerFromIframe($('#editable-select').val());
	}
</script>
<style type="text/css">
#buttonsDiv a {
	display: block;
	text-decoration: none;
	font: 11px Verdana; /* tab font */
	color: black; /* font color */
	width: 70px; /* width of tab image */
	height: 21px; /* height of tab image */
	margin-left: 3px; /* spacing between tabs */
	margin-right: 3px;
	margin-top: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
	background-image: url(images/skyButtonUp.png) !important;
	background-repeat: no-repeat;
	border: 2px;
	text-align: center;
}

#buttonsDiv a.hover {
	background-image: url(images/skyButtonOver.png) !important;
	/* URL to tab image */
	color: darkblue; /* font color */
	cursor: hand;
	cursor: pointer;
}

#buttonsDiv a.current {
	background-image: url(images/skyButtonDown.png) !important;
	/* URL to tab image */
	color: darkblue; /* font color */
	padding-left: 3px;
	padding-top: 3px; /* vertical offset of tab text from top of tab */
	cursor: hand;
	cursor: pointer;
}

#buttonsDiv a.disabled {
	background-image: url(images/skyButtonDisabled.png) !important;
	/* URL to tab image */
}

.mochaLoadingIcon {
	display: none !important;
}

div[style*="outset"] {
	-moz-box-sizing: border-box !important;
	box-sizing: border-box;
	border-color: #FFFFFF !important;
	border-left-style: double !important;
	border-left-color: #FFFFFF !important;
	border-left-width: 2px !important;
	border-top-style: double !important;
	border-top-color: #FFFFFF !important;
	border-top-width: 2px !important;
	border-right-style: outset !important;
	border-right-color: #FFFFFF !important;
	border-right-width: 2px !important;
	border-bottom-style: outset !important;
	border-bottom-color: #FFFFFF !important;
	border-bottom-width: 2px !important;
}

body {
	margin-top: 25px;
	margin-bottom: 5px;
	margin-left: 10px;
}

.es-list { max-height: 80px !important; overflow: auto !important; }

</style>
</head>
<body style="overflow: hidden; padding-bottom: 49px;"
	onLoad="bodyOnLoad();">
	<label style="left:50px;width:200px;display:inline-block;padding-bottom:10px; font-weight:bold">Save profile</label>
	
	<div id="ddimagebuttons"
		style="border: 2px outset; left: 20; width: 100%; height: 42px">
		<div style="width: 100%; height: 100%; overflow: auto;">
			<table id="comboboxesContainer" style="width: 300px">
				<tr>
				<td>
				</td>
					<td id="container">
					</td>
				</tr>
			</table>
		</div>
	</div>
	<div id="ddimagebuttons"
		style="border: 2px outset; height: 39px; position: fixed !important; top: auto !important; bottom: 10 !important; left: 9; right: 9;">
		<div id="ScenarioMaintenance"
			style="left: 2; top: 6; height: 15px; visibility: visible;">
			<table width="100" border="0" cellspacing="0" cellpadding="0"
				height="20" style="padding-top: 5px; padding-left: 10px;">
				<tr>
					<td align="left" id="okButton"><a tabindex="1" title="OK"
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:saveProfileClickHandler();">OK</a></td>
					<td align="left" id="cancelButton"><a tabindex="2"
						title="Cancel" onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:window.close();">Cancel</a></td>
				</tr>
			</table>
		</div>
	</div>
</body>
</html>