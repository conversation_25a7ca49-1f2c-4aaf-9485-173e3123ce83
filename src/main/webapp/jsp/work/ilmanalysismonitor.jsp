<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><fmt:message key="ilmanalysismonitor.title.window"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<script type="text/javascript">
			// timer used for cheking if download has been finished
			var fileDownloadCheckTimer;
			var screenRoute = "ILMMainScreen";
			//"IntraDayLiquidity";
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["entity"] = "<fmt:message key="ilmanalysismonitor.entity.title"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";
			label["text"]["tip-entity"] = "<fmt:message key="tooltip.entitytimeframe"/>";

			label["text"]["currency"] = "<fmt:message key="ilmanalysismonitor.ccy.title"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.selectCurrencyCode"/>";
			label["text"]["tip-currency"] = "<fmt:message key="tooltip.currencytimeframe"/>";

			label["text"]["valuedate"] = "<fmt:message key="ilmanalysismonitor.date.title"/>";
			label["tip"]["valuedateDDMMYY"] = "<fmt:message key="tooltip.enterValueDate"/>";
			label["tip"]["valuedateMMDDYY"] = "<fmt:message key="tooltip.ValueDateMMDDYY"/>";

			label["text"]["timeframe"] = "<fmt:message key="ilmanalysismonitor.timeframe.title"/>";
			label["tip"]["timeframe"] = "<fmt:message key="tooltip.timeframe"/>";

			label["text"]["refreshevery"] = "<fmt:message key="label.refreshevery"/>";
			label["tip"]["refreshevery"] = "<fmt:message key="tooltip.refreshevery"/>";

			label["text"]["ccymultiplier"] = "<fmt:message key="ccyMonitorOptions.useCcyMultiplier"/>";
			label["text"]["lastrefresh"] = "<fmt:message key="label.lastRefTime"/>";

			label["text"]["globalview"] = "<fmt:message key="ilmanalysismonitor.globalview.title"/>";
			label["text"]["groupanalysis"] = "<fmt:message key="ilmanalysismonitor.groupanalysis.title"/>";
			label["text"]["combinedview"] = "<fmt:message key="ilmanalysismonitor.combinedview.title"/>";
			label["text"]["groups"] = "<fmt:message key="ilmanalysismonitor.groups.title"/>";
			label["text"]["scenarios"] = "<fmt:message key="ilmanalysismonitor.scenarios.title"/>";
			label["text"]["balances"] = "<fmt:message key="ilmanalysismonitor.balances.title"/>";

			label["text"]["time"] = "<fmt:message key="ilmanalysismonitor.time"/>";
			label["text"]["balance"] = "<fmt:message key="ilmanalysismonitor.balance"/>";
			label["text"]["accumDC"] = "<fmt:message key="ilmanalysismonitor.accumDC"/>";
			label["text"]["interval"] = "<fmt:message key="ilmanalysismonitor.interval"/>";
			label["text"]["showscale"] = "<fmt:message key="ilmanalysismonitor.showscale"/>";
			label["text"]["includeMvnts"] = "<fmt:message key="ilmanalysismonitor.includeMvnts"/>";
			label["text"]["sumByCutOff"] = "<fmt:message key="ilmanalysismonitor.sumByCutOff"/>";
			label["text"]["includeSOD"] = "<fmt:message key="ilmanalysismonitor.includeSOD"/>";
			label["text"]["maintain"] = "<fmt:message key="ilmanalysismonitor.maintain"/>";
			label["tip"]["button-maintain-grp"] = "<fmt:message key="tooltip.maintainGrpButton"/>";
			label["tip"]["button-maintain-scn"] = "<fmt:message key="tooltip.maintainScnButton"/>";
			label["tip"]["button-setStyle"] = "<fmt:message key="tooltip.setStyleButton"/>";

			label["text"]["nodata"] = "<fmt:message key="ilmanalysismonitor.nodata"/>";
			label["text"]["nodata.forexport"] = "<fmt:message key="ilmanalysismonitor.nodata.forexport"/>";

			label["text"]["noglobalgroup"] = "<fmt:message key="ilmanalysismonitor.noglobalgroup"/>";
			label["text"]["showactual"] = "<fmt:message key="ilmanalysismonitor.showActual"/>";
			label["text"]["expandall"] = "<fmt:message key="ilmanalysismonitor.expandall"/>";
			label["text"]["collapseall"] = "<fmt:message key="ilmanalysismonitor.collapseall"/>";
			label["text"]["cancelExit"] = "<fmt:message key="tooltip.cancelExit"/>";
			label["text"]["saveExit"] = "<fmt:message key="tooltip.saveExit"/>";
			label["text"]["screenCancel"] = "<fmt:message key="screen.cancel"/>";
			label["text"]["setStylePopupName"] = "<fmt:message key="ilmanalysismonitor.setStylePopupName"/>";
			label["text"]["exportProgress"] = "<fmt:message key="ilmanalysismonitor.exportLabelWaiting"/>";

			label["text"]["aac"] = "<fmt:message key="ilmanalysismonitor.legend.accActualC"/>";
			label["text"]["aad"] = "<fmt:message key="ilmanalysismonitor.legend.accActualD"/>";
			label["text"]["afc"] = "<fmt:message key="ilmanalysismonitor.legend.accForeC"/>";
			label["text"]["afd"] = "<fmt:message key="ilmanalysismonitor.legend.accForeD"/>";
			label["text"]["ab"] = "<fmt:message key="ilmanalysismonitor.legend.actBalance"/>";
			label["text"]["fbb"] = "<fmt:message key="ilmanalysismonitor.legend.forebasic"/>";
			label["text"]["fbia"] = "<fmt:message key="ilmanalysismonitor.legend.forecIncludeAct"/>";

			label["text"]["button-close"] = "<fmt:message key="button.genericdisplaymonitor.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
			label["text"]["label-operationTakeTime"] = "<fmt:message key="label.operationTakeTime"/>";
			label["text"]["recalculateILMData"] = "<fmt:message key="label.recalculateILMData"/>";
			label["text"]["label-existingILMData"] = "<fmt:message key="label.existingILMData"/>";
			label["text"]["label-incomplete"] = "<fmt:message key="label.incomplete"/>";
			label["text"]["label-inconsistent"] = "<fmt:message key="label.inconsistent"/>";
			label["text"]["label-newDataExistFor"] = "<fmt:message key="label.newDataExistFor"/>";
			label["text"]["label-numberAccounts"] = "<fmt:message key="label.numberAccounts"/>";
			label["text"]["label-dataFrom"] = "<fmt:message key="label.dataFrom"/>";
			label["text"]["label-LastUpdate"] = "<fmt:message key="ilmanalysismonitor.grid.lastupdate"/>";
			label["text"]["label-dataFor"] = "<fmt:message key="label.dataFor"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.alert.error"/>";
			label["text"]["label-errorContactSystemAdmin"] = "<fmt:message key="label.errorContactSystemAdmin"/>";
			label["text"]["label-screenName"] = "<fmt:message key="ilmanalysismonitor.title.screenName"/>";
			label["text"]["label-contactAdmin"] = "<fmt:message key="genericDisplayMonitor.contactAdmin"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";

			label["text"]["label-lossConnection"] = "<fmt:message key="label.lossConnection"/>";
			label["text"]["label-refresh"] = "<fmt:message key="sweep.refresh"/>";
			label["text"]["label-setStyle"] = "<fmt:message key="label.setStyle"/>";

			label["text"]["label-unableSave"] = "<fmt:message key="label.unableSave"/>";
			label["text"]["label-programmingErrorMethod"] = "<fmt:message key="label.programmingErrorMethod"/>";
			label["text"]["label-chartNotExist"] = "<fmt:message key="label.chartNotExist"/>";
			label["text"]["label-thresholds"] = "<fmt:message key="ilmanalysismonitor.grid.thresholds"/>";
			label["text"]["label-actual"] = "<fmt:message key="label.actual"/>";

			label["text"]["errorOnServerSide"] = "<fmt:message key="ilmanalysismonitor.errorOnServerSide"/>";
			label["text"]["recalculationError"] = "<fmt:message key="ilmanalysismonitor.recalculationError"/>";
			label["text"]["continueWithoutRecalculate"] = "<fmt:message key="ilmanalysismonitor.continueWithoutRecalculate"/>";
			label["text"]["recalculationWindowTitle"] = "<fmt:message key="ilmanalysismonitor.recalculationWindowTitle"/>";
			label["text"]["enableToMaintain"] = "<fmt:message key="alert.ilmanalysis.enableToMaintain"/>";
			label["text"]["nonValidValue"] = "<fmt:message key="alert.ilmanalysis.nonValidValue"/>";



			label["text"]["alert-errorDataNotUpToDate"] = "<fmt:message key="ilmanalysismonitor.errorDataNotUpToDate"/>";
			label["text"]["label-recalculateConfirmAlertTitle"] = "<fmt:message key="ilmanalysismonitor.recalculateConfirmAlertTitle"/>";
			label["text"]["alert-recalculateConfirm"] = "<fmt:message key="ilmanalysismonitor.recalculateConfirm"/>";

			label["text"]["alert-NodataForSelection"] = "<fmt:message key="ilmanalysismonitor.alertNodataForSelection"/>";
			label["text"]["label-requestRecalculation"] = "<fmt:message key="ilmanalysismonitor.requestRecalculation"/>";
			label["text"]["label-recalculationInProgress"] = "<fmt:message key="ilmanalysismonitor.recalculationInProgress"/>";
			label["text"]["alert-alertRecalculateRunning"] = "<fmt:message key="ilmanalysismonitor.alertRecalculateRunning"/>";
			label["text"]["alert-dateOutsideRange"] = "<fmt:message key="ilmanalysismonitor.alertDateOutsideRange"/>";
			label["text"]["alert-noCcyEntity"] = "<fmt:message key="ilmanalysismonitor.alertNoCcyEntity"/>";

			label["text"]["label-lastProfile"] = "<fmt:message key="ilmanalysismonitor.lastProfile"/>";
			label["text"]["label-noneProfile"] = "<fmt:message key="ilmanalysismonitor.noneProfile"/>";

			label["text"]["alert-deleteProfile"] = "<fmt:message key="ilmanalysismonitor.alertDeleteProfile"/>";
			label["text"]["alert-overwriteProfile"] = "<fmt:message key="ilmanalysismonitor.alertOverwriteProfile"/>";
			label["text"]["alert-revertProfile"] = "<fmt:message key="ilmanalysismonitor.alertRevertProfile"/>";
			label["text"]["alert-profileSaved"] = "<fmt:message key="ilmanalysismonitor.alertProfileSaved"/>";
			label["text"]["alert-profileDeleted"] = "<fmt:message key="ilmanalysismonitor.alertProfileDeleted"/>";
			label["tip"]["combo-profile"] = "<fmt:message key="ilmanalysismonitor.profileComboTooltip"/>";
			label["tip"]["combo-deleteprofile"] = "<fmt:message key="ilmanalysismonitor.deleteProfileImageTooltip"/>";
			label["tip"]["combo-saveprofile"] = "<fmt:message key="ilmanalysismonitor.saveProfileImageTooltip"/>";
			label["tip"]["combo-reloadProfile"] = "<fmt:message key="ilmanalysismonitor.reloadProfileImageTooltip"/>";


			label["text"]["label-sourcesOfLiquidity"] = "<fmt:message key="ilmanalysismonitor.sourcesOfLiquidity"/>";
			label["text"]["combo-groupLabel"] = "<fmt:message key="ilmanalysismonitor.groupComboLabel"/>";
			label["text"]["combo-scenarioLabel"] = "<fmt:message key="ilmanalysismonitor.scenarioComboLabel"/>";


			label["tip"]["combo-groupTooltip"] = "<fmt:message key="ilmanalysismonitor.groupComboTooltip"/>";
			label["tip"]["combo-ScenarioTooltip"] = "<fmt:message key="ilmanalysismonitor.scenarioComboTooltip"/>";

			label["tip"]["combo-saveAs"] = "<fmt:message key="ilmanalysismonitor.saveAsComboprofilePopupTooltip"/>";
			label["text"]["button-saveProfile"] = "<fmt:message key="ilmanalysismonitor.saveAsButtonprofilePopupLabel"/>";
			label["text"]["alert-fillMandatoryFields"] = "<fmt:message key="ilmanalysismonitor.alertFillMandatoryFields"/>";

			label["tip"]["tip-allGlobalEntities"] = "<fmt:message key="ilmanalysismonitor.tooltip.allEntityGlobalSummation"/>";
			label["tip"]["tip-allGlobalAlterEntities"] = "<fmt:message key="ilmanalysismonitor.tooltip.allEntityGlobalAlternSummation"/>";
			label["tip"]["tip-allAlternatives"] = "<fmt:message key="ilmanalysismonitor.tooltip.allEntityAlternSummation"/>";

			var alertOrangeImage = "images/Alert/scenario/normal.png";
			var alertRedImage = "images/Alert/scenario/critical.png";

			label["text"]["SUM_CREDIT_LINE_TOTAL"] = "CLTOT";
			label["text"]["SUM_COLLATERAL"] = "COLL";
			label["text"]["SUM_UN_LIQUID_ASSETS"] = "ULA";
			label["text"]["SUM_OTHER_TOTAL"] = "OTHER";
			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var roleId = '${requestScope.roleId}';
			var userId = '${requestScope.userId}';
			var dbDate = '${requestScope.sysDateAsString}';
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			var entityId = '${requestScope.defaultEntityId}';
			var currencyId = '${requestScope.defaultCcyId}';
			var selectedDate = '${requestScope.sysDateAsString}';
			var useCcyMultiplier = '${requestScope.useCcyMultiplier}';
			var uniqueSequenceNumber = '${requestScope.uniqueSequenceNumber}';
			var checkStateRefreshTime="<%=PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.ILM_DATA_STATE_CHECK_REFRESH_TIME)%>";

			var timeSlotSize ="<%=PropertiesFileLoader.getInstance().getPropertiesValue(SwtConstants.INTRADAYLIQUIDITY_TIMESLOTSIZE)%>";


			/**
			 * if the document is ready then add a listener to exportDataForm in order to capture when
			 * the download will be finished after exporting
			 */
			jQuery(document).ready(function () {
				jQuery('#DataForm').submit(function () {
					//Not needed
			    	//blockUIForDownload();
			    });
			});

			/**
			 * This method is called when window is onloaded.
			 */
			window.onload = function () {
				setParentChildsFocus();
				$('<form id="exportDataForm" target="tmp" method="post">				<input type="hidden" name="data" id="exportData" /> <input type="hidden" name="screen" id="exportDataScreen" value="ILMAnalysis" />			</form>').appendTo('body')
			};

			/**
			* Call the stop recalculation process when unloading the screen
			**/
			window.onunload=function(){
				getMenuWindow().stopRecalculateProcess(uniqueSequenceNumber);
			};
			function openOptions() {
				var param = "ilmAnalysisMonitor.do?method=optionScreen";
				var 	mainWindow = openWindow (param, 'ilmOptions','left=10,top=230,width=980,height=700,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
		        return false;

			}

			/**
			 * This is used to capture when the download has been finished in order to close the popup in flex part
			 */
			function blockUIForDownload() {
				// Use the current timestamp as the token value
			    var token = new Date().getTime();
			    // Set the hidden input download_token_value_id as token. It will be sent to the action part.
				jQuery('#download_token_value_id').val(token);
			   	/* The setInterval() method calls a function at specified intervals (in milliseconds).
			   	   If we received the same token (from cookie) then the download is finished */
			    fileDownloadCheckTimer = window.setInterval(function () {
			      var downloadToken;
				  var downloadError;
			      var cookieValue = jQuery.cookie('fileDownloadToken');

			      downloadToken=downloadError=cookieValue;
			      if (cookieValue!= null){
				      var fileDownloadTokenValue= cookieValue.split("|");
				      downloadToken=fileDownloadTokenValue[0];
				      downloadError=fileDownloadTokenValue[1] ;
			      }

			      if (downloadToken == token)
			       downloadFinished();
			      if (downloadError == "KO")
			     	 errorFileDownload() ;
			    }, 300); // the intervals (in milliseconds) on how often to execute the code
			}

			/**
			 * Clear the interval, clear the token && then close the popup in flex part using call back method
			 */
			function downloadFinished() {
				  window.clearInterval(fileDownloadCheckTimer);
				  jQuery.cookie('fileDownloadToken', null); //clears this cookie value
// 				  getFlashObject("mySwf").closePopup();
			}
			function errorFileDownload(){
				 window.clearInterval(fileDownloadCheckTimer);
				 jQuery.cookie('fileDownloadToken', null); //clears this cookie value
// 				 getFlashObject("mySwf").downloadError();
			}


			function closeAlertPopup(){
// 				getFlashObject("mySwf").closeAlertWindowFromCallBack();
			}


			function openChildWindow(methodName){
				var appName = "<%=SwtUtil.appName%>";
				var param = '/' + appName + '/ilmAnalysisMonitor.do?method='+methodName;
				var 	mainWindow = openWindow(param, 'methodName','left=10,top=230,width=600,height=600,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
				return false;

			}


			function openILMThroughPutRatioMonitor(entityId, ccyCode ,accountGroupId ){
				var appName = "<%=SwtUtil.appName%>";
				var param = '/' + appName + '/ilmAnalysisMonitor.do?method=ilmThroughPutMonitor';

				param = param + "&entityId="
						+ entityId
						+ "&selectedScenario="
						+ "&currencyId="
						+ ccyCode
						+ "&accountGroup="
						+ accountGroupId
						+ "&fromILMMonitor=true";



				var 	mainWindow = openWindow(param, 'methodName','left=10,top=230,width=1200,height=600,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
				return false;

			}





			function highlightLegendFromHighChart(id, yField, highlight){
				var isglobal ;

				if(id == "IframeGlobalView"){
					isglobal = true;
				}else {
					isglobal = false;
				}

// 				getFlashObject("mySwf").highlightLegendFromHighChart(isglobal, yField, highlight);
			}


			function updateChartsLiveValues(id, values ,sizeOfArray, dateAsString){
				var isglobal ;

				if(id == "IframeGlobalView"){
					isglobal = true;
				}else {
					isglobal = false;
				}

// 				getFlashObject("mySwf").updateChartsLiveValues(isglobal, values, sizeOfArray, dateAsString);
			}


			function onExport(chartSnapshot, legendSnapshot, data, exportType, entityId, currencyId,selectedDate, timeFrame){
 				document.getElementById('DataForm').action='ilmAnalysisMonitor.do?method=exportLineChartDetails';
				document.getElementById('chartSnapshot').value=legendSnapshot;
				document.getElementById('legendSnapshot').value="";
				document.getElementById('dataXML').value=data;
				document.getElementById('exportType').value=exportType;
				document.getElementById('entityId').value=entityId;
				document.getElementById('currencyId').value=currencyId;
				document.getElementById('selectedDate').value=selectedDate;
				document.getElementById('timeFrame').value=timeFrame;
				document.getElementById('mybutton').click();
			}

			function getProfileList(entityId, currencyCode){
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/ilmAnalysisMonitor.do?method=getProfileList";
				requestURL = requestURL + "&entityId=" + entityId + "&currencyCode=" + currencyCode;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var profileList = new String(oXMLHTTP.responseText);
				return profileList.toString();
			}

		function openILMSeriesStyleSceen(){

				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/intraDayLiquidity.do?method=getAccessForILMGroup";
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				requestURL = requestURL + "&userId=" + creator
				requestURL = requestURL + "&publicPrivate=" + groupType;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var ilmGrpAccess = new String(oXMLHTTP.responseText);
				if(ilmGrpAccess == 0){
					var access = getMenuAccessIdOfChildWindow("intraDayLiquidity.do\\?method=listAccountGroups");
					if (access != 2)
				    	openWindow(buildOpenGroupURL(selectedAccountGroup,entityId,currencyCode),'accountgroupchange','left=50,top=190,width=995,height=750,toolbar=0, resizable=yes, scrollbars=yes','true');
					else
						return false;
					return true;
				}
				else
					return false;
			}

			/**
			 * Build URL wich will open the ILMScenario details screens
			 */
			function buildOpenScenarioURL(methodName,selectedScenarioId,entityId,currencyCode) {

				var param = 'ilmTransScenario.do?method=' + methodName;
					param = param + "&selectedEntityId="
							+ entityId
							+ "&selectedEntityName="
							+ ""
							+ "&selectedCurrencyCode="
							+ currencyCode;

						param = param + "&selectedScenarioId="
						+ selectedScenarioId;
						param = param + '&parentScreen=liquidityMonitor';
				return param;
			}

			function buildOpenGroupURL(selectedAccountGroup,entityId,currencyCode){

				var param = 'intraDayLiquidity.do?method=accountGroupDetailsFlex';
				param +='&selectedAccountGroup='+selectedAccountGroup;
				param +='&currencyCode='+currencyCode;
				param +='&entityId='+entityId;
				param +='&parentScreen=liquidityMonitor';

				var access = getMenuAccessIdOfChildWindow("intraDayLiquidity.do\\?method=listAccountGroups");
				if (access == 0)
					param +='&methodName=change';
				else if (access == 1)
					param +='&methodName=view';

				return param;
			}

			function openILMGroup(selectedAccountGroup,groupType,creator,entityId,currencyCode){

				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/intraDayLiquidity.do?method=getAccessForILMGroup";
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				requestURL = requestURL + "&userId=" + creator
				requestURL = requestURL + "&publicPrivate=" + groupType;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var ilmGrpAccess = new String(oXMLHTTP.responseText);
				if(ilmGrpAccess == 0){
					var access = getMenuAccessIdOfChildWindow("intraDayLiquidity.do\\?method=listAccountGroups");
					if (access != 2)
				    	openWindow(buildOpenGroupURL(selectedAccountGroup,entityId,currencyCode),'accountgroupchange','left=50,top=190,width=995,height=750,toolbar=0, resizable=yes, scrollbars=yes','true');
					else
						return false;
					return true;
				}
				else
					return false;
			}

			function openILMScenario(selectedScenarioId,scenarioType,creator,entityId,currencyCode){

				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL=requestURL.substring(0,idy+1) ;
				requestURL = requestURL + appName+"/ilmTransScenario.do?method=getEntityCurrencyAccessForScenario"
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				requestURL = requestURL + "&userId=" + creator;
				requestURL = requestURL + "&publicPrivate=" + scenarioType;
				var oXMLHTTP = new XMLHttpRequest();
				oXMLHTTP.open( "POST", requestURL, false );
				oXMLHTTP.send();
				var ilmScnAccess =new String(oXMLHTTP.responseText);

				if(ilmScnAccess == 0){

					var access = getMenuAccessIdOfChildWindow("ilmTransScenario.do\\?method=listScenarios");
					if (access == 2)
						return false;
					else if (access == 1)
						openWindow(buildOpenScenarioURL('viewScenario',selectedScenarioId,entityId,currencyCode),'scenariochange','left=50,top=190,width=940,height=715,toolbar=0, resizable=yes, scrollbars=yes','true');
					else if (access == 0)
						openWindow(buildOpenScenarioURL('changeScenario',selectedScenarioId,entityId,currencyCode),'scenariochange','left=50,top=190,width=940,height=715,toolbar=0, resizable=yes, scrollbars=yes','true');

					return true;
				}else
					return false;
			}

			function refreshGridData(){
				//getFlashObject("mySwf").updateData(null);
				window.parent.postMessage(['updateData'], null)
			}

			var analysisSegmentsLineCharts = [];
			var analysisareasLineCharts = [];
			var globalSegmentsLineCharts = [];
			var globalareasLineCharts = [];
			var selectedTab = '';

			//function used after addding HighChart HTML5
			function setILMStylesValues(id, segmentsLineCharts, areasLineCharts){
// 				var frame = document.getElementById(id).contentWindow;
// 				frame.setILMStylesValues(segmentsLineCharts, areasLineCharts);
				selectedTab = id;
				if(id === "GlobalView") {
					globalSegmentsLineCharts = segmentsLineCharts;
					globalareasLineCharts = areasLineCharts;
				}else {
					analysisSegmentsLineCharts = segmentsLineCharts;
					analysisareasLineCharts = areasLineCharts;
				}


			}

			function setILMDataForCharts(isglobal, JSONDataSODAsString, addNewCharts, resetAll){
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.setILMData(JSONDataSODAsString,addNewCharts, resetAll);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

			var saveProfileCollection = [];
			var selectedProfileItem = [];
			function populateProfileList(id, saveProfileArrayCollection, selectedElement){
// 				var frame = document.getElementById(id).contentWindow;
// 				frame.populateComboBox(saveProfileArrayCollection, selectedElement);
				saveProfileCollection = saveProfileArrayCollection;
				selectedProfileItem = selectedElement;
			}

			function callIframeFunction(id){
				var iframe = document.getElementById("analysisPopupStylePopup_iframe");
				iframe.contentWindow.callfromFlex();
			}


			function isThisRuningHTMLContent() {
				return true;
			}

			var groupAnalysisLoadData;
			var globalViewLoadData;


			function setJSONChartsData(forGlobal, data, includeSOD, sourceOfLiqudity,liquidityZonesDataAsJSON, useCcyMulitplier,isCurrencyTimeFrom,
					fromTime, toTime, showActualDSOnly, currencyFormat, currencyMutiplierValue, currencyDecimalPlaces, dateFormatAsString, saveHighligtedCharts, highlightedSeries) {
				if(forGlobal == true) {
					globalViewLoadData = [data, includeSOD, sourceOfLiqudity,liquidityZonesDataAsJSON, useCcyMulitplier, isCurrencyTimeFrom,
						fromTime, toTime, showActualDSOnly, currencyFormat, currencyMutiplierValue, currencyDecimalPlaces, dateFormatAsString, saveHighligtedCharts, highlightedSeries];
				}else {
					groupAnalysisLoadData = [data, includeSOD, sourceOfLiqudity,liquidityZonesDataAsJSON, useCcyMulitplier, isCurrencyTimeFrom,
						fromTime, toTime, showActualDSOnly, currencyFormat, currencyMutiplierValue, currencyDecimalPlaces, dateFormatAsString, saveHighligtedCharts, highlightedSeries];
				}
			}


			function getJSONData(id) {
				if(id != "IframeGlobalView")
					return groupAnalysisLoadData;
				else
					return globalViewLoadData;
			}


			function highlightChartFromFlex(isglobal, yField, highligh) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				frame.highlightSerie(yField, highligh);
			}


			function showHideChartFromFlex(isglobal, yField, showHide) {

				var frame;
				var id  ;
				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.showSerie(yField, showHide);
				}
				catch(err) {
				    console.log(err.message);
				    console.log(err.stack);
				}

			}

			function showHideMultipleCharts(isglobal, seriesToShow, showHide) {

				var frame;
				var id  ;
				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.showOrHideMultipleSeries(seriesToShow, showHide);
				}
				catch(err) {
				    console.log(err.message);
				    console.log(err.stack);
				}

			}
			function removeMultipleCharts(isglobal, seriesToRemove) {
				var frame;
				var id  ;
				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.removeMultipleCharts(seriesToRemove);
				}
				catch(err) {
				    console.log(err.message);
				    console.log(err.stack);
				}

			}

			function showHideThresholdFromFlex(isglobal, showHide, groupId ) {

				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.showHideThreshold(showHide, groupId);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

			function changeChartsStyleFromPopup(isglobal, newStyles ) {

				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.changeChartsStyle(newStyles);
				}
				catch(err) {
				    console.log(err.message);
				}

			}


			function updateNowTimeInChart(isglobal, nowTime) {

				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.updateClock(nowTime);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

			function showHideActualDataSetFromFlex(isglobal, showHide, visibleYFields) {

				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.showHideActualDataSet(showHide, visibleYFields);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

			var firstLoadGlobaldataZonesJSON;
			var firstLoadAnalysisdataZonesJSON;

			function showHideSourcesOfLiquidity(isglobal, showHide, valuesUpdated, dataZonesJSON, visibleItemsInTree) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
					firstLoadGlobaldataZonesJSON = dataZonesJSON;
				}else {
					id = "IframeAnalysis";
					firstLoadAnalysisdataZonesJSON = dataZonesJSON;
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.showHideSourcesOfLiquidity(showHide, valuesUpdated,  dataZonesJSON, visibleItemsInTree);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

			function alignScaleFromFlex(isglobal, alignScaleSelected) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.alignScaleWithSOD(alignScaleSelected);
				}
				catch(err) {
				    console.log(err.message);
				}
			}

			function updateScreenSize(isglobal, newWidth, newHeight) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.updateScreenSize(newWidth, newHeight);
				}
				catch(err) {
				    console.log(err.message);
				}
			}


			function applyZoomFromFlex(isglobal, fromTimeStamp, toTimeStamp) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.zoom(fromTimeStamp, toTimeStamp);
				}
				catch(err) {
				    console.log(err.message);
				}

			}



			function setEntityOrCurrencyTimeFrame(isglobal, isCurrencySelected, timeFromAsInt,timeToAsInt) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.setEntityOrCurrencyTimeFrame(isCurrencySelected,timeFromAsInt ,timeToAsInt);
				}
				catch(err) {
				    console.log(err.message);
				}

			}
			function exportChartAsPDF(isglobal, chartSnapshot, legendSnapshot, dataXML, exportType, entityId, currencyId,selectedDate, timeFrame) {
				var frame;
				var id  ;

				if(isglobal == true) {
					id = "IframeGlobalView";
				}else {
					id = "IframeAnalysis";
				}
				frame = document.getElementById(id).contentWindow;
				try {
					frame.exportChart(chartSnapshot, legendSnapshot, dataXML, exportType, entityId, currencyId,selectedDate, timeFrame);
				}
				catch(err) {
				    console.log(err.message);
				}

			}




			function updateValuesUsingCcyMultiplierFromFlex(ccyMuliplierSelected) {

				var frameGroupAnalysis;
				var frameGlobalView;

				frameGroupAnalysis = document.getElementById("IframeAnalysis").contentWindow;
				frameGlobalView = document.getElementById("IframeGlobalView").contentWindow;


				try {
					frameGlobalView.ccyMultiplierEventHandler(ccyMuliplierSelected);
				}
				catch(err) {
				    console.log(err.message);
				}

				try {
					frameGroupAnalysis.ccyMultiplierEventHandler(ccyMuliplierSelected);
				}
				catch(err) {
				    console.log(err.message);
				}

			}

		</script>

        <%@ include file="/angularscripts.jsp"%>

		<form id="DataForm" style="width: 100%" method="post" target="tmp" enctype="multipart/form-data">
			<input name="method" type="hidden" value="">
			<input name="exportType" type="hidden" value="">
			<input name="chartSnapshot" type="hidden" value="">
			<input name="legendSnapshot" type="hidden" value="">
			<input name="dataXML" type="hidden" value="">
			<input name="entityId" type="hidden" value="">
			<input name="currencyId" type="hidden" value="">
			<input name="selectedDate" type="hidden" value="">
			<input name="timeFrame" type="hidden" value="">
			<input name="menuAccessId" type="hidden" >
			<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
			<input name="mybutton" type="submit" value="post" style="visibility: hidden;"/>
			<iframe name="tmp" width="0%" height="0%" src="#" />
		</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
<!-- we put mootools after the body because of the operation aborted bug in ie6 && ie7. could be moved? -->

	</body>
</html>