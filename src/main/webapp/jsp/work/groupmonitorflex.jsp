<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page
	import="org.swallow.work.model.MetagroupMonitorCurrencyBalanceTO"%>

<html>
<head>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<title><fmt:message key="BookgroupMonitor.title.window" /></title>

<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>

<!-- <script type="text/javascript" src="jsp/work/assets/swfobject.js"></script> -->
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<script type="text/javascript">
		// <![CDATA[
			var screenRoute = "bookGroupMonitor";
			var calledFromParent = <%=(""+request.getAttribute("callFromParent")).equalsIgnoreCase("Y")?"true":"false"%>;
			
			var entityId1 = '${requestScope.entityId1}';
			var currencyId1 = '${requestScope.currencyId1}';
			var datefr = '${requestScope.datefr}';
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			label["text"]["entity"] = "<fmt:message key="bookCode.entity"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";

			label["text"]["currency"] = "<fmt:message key="bookMonitor.currency"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.selectCurrencyId"/>";

			label["text"]["location"] = "<fmt:message key="bookMonitor.location"/>";
			label["tip"]["location"] = "<fmt:message key="tooltip.selectLocationId"/>";

			label["text"]["monitor"] = "<fmt:message key="bookMonitor.monitor"/>";
			label["tip"]["monitor"] = "<fmt:message key="tooltip.selectMonitor"/>";

			label["text"]["date"] = "<fmt:message key="bookMonitor.date"/>";
			label["tip"]["date"] = "<fmt:message key="tooltip.enterDate"/>";

			label["text"]["balance"] = "<fmt:message key="bookMonitor.balance"/>";
			label["tip"]["balance"] = "<fmt:message key="bookMonitor.total"/>";

			label["text"]["tab1"] = "<fmt:message key="bookMonitor.today"/>";

			label["text"]["tab2"] = "<fmt:message key="bookMonitor.today1"/>";

			label["text"]["tab3"] = "<fmt:message key="bookMonitor.today2"/>";

			label["text"]["tab4"] = "<fmt:message key="bookMonitor.selected"/>";

			label["text"]["metagroup"] = "<fmt:message key="metagroupMonitor.metagroup"/>";
			label["tip"]["metagroup"] = "<fmt:message key="tooltip.selectMetagroupId"/>";

			label["text"]["group"] = "<fmt:message key="groupMonitor.group"/>";
			label["tip"]["group"] = "<fmt:message key="tooltip.selectGroupId"/>";

			label["text"]["name"] = "<fmt:message key="bookMonitor.name"/>";
			label["tip"]["sortMetagroupName"] = "<fmt:message key="tooltip.sortMetagroupName"/>";

			label["text"]["level"] = "<fmt:message key="metagroupMonitor.level"/>";
			label["tip"]["sortLevel"] = "<fmt:message key="tooltip.sortLevel"/>";

			label["text"]["total"] = "<fmt:message key="positionlevel.total"/>";
			label["tip"]["predictField"] = "<fmt:message key="tooltip.predictField"/>";

			label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

			label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";


			label["text"]["button-options"] = "<fmt:message key="button.groupMonitor.options"/>";
			label["tip"]["button-options"] = "<fmt:message key="tooltip.groupMonitor.options"/>";

			label["text"]["alert.currencyAccess"] = "<fmt:message key="alert.currencyAccess"/>";
			/*Start:Added by Imed B on 20-02-2014*/
			label["text"]["label-bookGroupMonitor"] = "<fmt:message key="groupMonitor.title.bookGroupMonitor"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-error"] = "<fmt:message key="groupMonitor.notANumber"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["label-refreshRateSelected"] = "<fmt:message key="groupMonitor.refreshRateSelected"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			label["text"]["label-notANumber"] = "<fmt:message key="accountMonitor.alert.label.notANumber"/>";
			label["text"]["label-entity"] = "<fmt:message key="accountMonitor.label.entity"/>";
			/*End: Added by Imed B on20-02-2014*/

			label["text"]["label-location"] = "<fmt:message key="groupMonitor.location"/>";
			label["tip"]["dateMMDDYY"] = "<fmt:message key="groupMonitor.dateMMDDYY"/>";

			var currentFontSize = "<%=request.getAttribute("fontSize")%>";


				if(valueClosed()=="1"){
					self.close();
				}
				var pageLoaded = true;

				var dateFormat = '${sessionScope.CDM.dateFormat}';
				var currencyFormat = '${sessionScope.CDM.currencyFormat}';


				var dynamicTab = true;
				var initialtab=[${requestScope.selectedTabIndex}, "${requestScope.selectedTabName}"];
				var selectedtab = initialtab[1];
				var previoustab="";
				var tabNames = new Array( 'metagroupMonitorTodayParent','metagroupMonitorTodayPlusOneParent','metagroupMonitorTodayPlusTwoParent','metagroupMonitorSelectedDateParent');
				var entityAccess = "${requestScope.EntityAccess}";

				var filterstatus= "${requestScope.filterStatus}";
				var sortStatus="${requestScope.sortStatus}";
				var sortDescending="${requestScope.sortDescending}";
				<%
				String scrollLeft = (String)request.getAttribute("scrollLeft");
				String scrollTop = (String)request.getAttribute("scrollTop");
				String scrollTableLeft=(String)request.getParameter("scrollTableLeft");
				String scrollTableTop=(String)request.getParameter("scrollTableTop");

				if(scrollLeft == null || scrollLeft.trim().length()==0)
					scrollLeft = "0";
				if(scrollTop == null || scrollTop.trim().length()==0)
					scrollTop = "0";
				if(scrollTableLeft == null || scrollTableLeft.trim().length()==0)
					scrollTableLeft= "0";
				if(scrollTableTop == null || scrollTableTop.trim().length() ==0)
					scrollTableTop = "0";
				%>
				var mouse_click_counter=0;
				var mouse_down_counter=0;
				var refreshFlag=true;
				var refreshFlag_drop_down=true;
				var refreshFlagCal=true;     //calender
				var checkClickStatus= false; // To check whether a click is made inside/on the select box.

			var itemId = '${requestScope.itemId}';
			var metagroupMenuItemId = '${requestScope.metagroupMenuItemId}';
			var groupMenuItemId = '${requestScope.groupMenuItemId}';
			var bookMenuItemId = '${requestScope.bookMenuItemId}';
			var metagroupScreenId = '${requestScope.metagroupScreenId}';
			var groupScreenId = '${requestScope.groupScreenId}';
			var bookScreenId = '${requestScope.bookScreenId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';
			var monitorType = '${requestScope.monitorType}';
			var refreshPending = false;

			var appName = "<%=SwtUtil.appName%>";
			var testDate= "<%=SwtUtil.getSystemDateString() %>";
			/*
 			* Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
 			*/
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			var monitor="group";


			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';


			function submitForm(methodName){
				document.forms[0].method.value = methodName;
				document.forms[0].submit();
			}
			function submitFormWithoutSettingTabInfo(methodName){
				document.forms[0].method.value = methodName;
				document.forms[0].submit();
			}

			function submitFormForNoCacheData(methodName){
				isDoNotCloseMyChilds= true;
				document.forms[0].refreshFlag.value = "Y";
				document.forms[0].method.value = methodName;
				if (pageLoaded) {
					pageLoaded = false;
					document.forms[0].submit();
				}
			}

			function getGroupMonitorDetails(e){
			var event = (window.event|| e);
			var target = (event.srcElement || event.target);
			 var entityId = document.forms[0].elements["metagroupMonitor.entityId"].value;
			 var currencyCode = document.forms[0].elements["metagroupMonitor.currencyId"].value;
			 var locationId = document.forms[0].elements["metagroupMonitor.locationId"].value;
			 var valueDate = "${requestScope.submitDate}";
			 var trElement = target.parentElement.parentElement;
			 var metagroupId= new String(trElement.cells[0].innerText).trim();
			 var menuAccessIdChild = getMenuAccessIdOfChildWindow("'groupmonitor.do");
			 var menuName = new String('<fmt:message key="groupMonitor.title.window"/>');
			 var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
			 menuName = menuName.substr(0,smrtPredPos-3);
			 if (menuAccessIdChild == 2) {
			     alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		    } else {
			     var requestURL = "groupmonitor.do?";
			     arrtributString="left=50,top=190,width=860,height=665,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
			     requestURL = requestURL + "&entityId=" + entityId;
			     requestURL = requestURL + "&currencyCode=" + currencyCode;
			     requestURL = requestURL + "&locationId=" + locationId;
			     requestURL = requestURL + "&valueDate=" + valueDate;
			     requestURL = requestURL + "&metagroupId=" + metagroupId;
			     openWindow(requestURL,'groupMonitorJsp',arrtributString);
			 }

			 if (window.event){
					window.event.returnValue = false;
					window.event.cancelBubble = true;
				}else {
					event.preventDefault();
					event.stopPropagation();
				}
			 return false;
			}

			var pvousObj = null;
			function click_on_drop(event)
			{
				setFlag2();
				ev= event||window.event;
				pvousObj = ev.srcElement;
				mouse_click_counter++;
				if (checkClickStatus) {
					resetFlag2();
				}
				checkClickStatus = false;
			}
			function mouse_down()
			{
				ev= event||window.event;
				mouse_down_counter++;
				if(mouse_down_counter==2)
				{
					var  maxoption=30;
					var scroll_width=20;
					if(this.options.length<=maxoption)
					{
						var x= this.offsetHeight*this.options.length;
						scroll_width=0;
					}else
						var x= this.offsetHeight*30;
					// if  out  side  click on  first ,  second  , 3rd  qurdent  of  select  box
					if( ev.offsetX<0|| ev.offsetY<0 )
					{
						resetFlag2();
						mouse_down_counter=0;
					}
					// code  for  out side  forth  qudrent
					else if( ev.offsetX>this.offsetWidth)
					{
						resetFlag2();
						mouse_down_counter=0;
					}else if(ev.offsetX<=this.offsetWidth&& ev.offsetY<=this.offsetHeight)
					{// calculate  hight && width  of  select  box  ....

						if (pvousObj.sourceIndex == ev.srcElement.sourceIndex) {
						checkClickStatus = true; //Click is made on the select box
						mouse_down_counter=0;
						}else {
						mouse_down_counter--;
						}
						resetFlag2();
					}else if(ev.offsetX<=this.offsetWidth-(scroll_width)&& ev.offsetY<=x) // calculate  hight && width  of  select  box  ....
					{
						if (pvousObj.sourceIndex == ev.srcElement.sourceIndex){
						checkClickStatus = true;  //Click is made inside the select box
						}
						resetFlag2();
						mouse_down_counter=0;
					}else
						mouse_down_counter--;
				}
			}

			function setFlag2(){
				refreshFlag_drop_down=false;
			}

			function resetFlag2(){
				refreshFlag_drop_down=true;
			}

			function setrefreshFlag(){
				if(document.getElementById(cal.divName).style.visibility=='hidden'){
					refreshFlagCal=true;
				}
			}

			function bodyOnLoad(){
				highlightTableRows("monitorDetails");
				var cancelcloseElements = new Array(1);
				cancelcloseElements[0] = "closebutton";
				document.getElementById("entityName").innerText = '${entityName}';
				document.getElementById("currencyName").innerText = '${currencyName}';
				document.getElementById("locationName").innerText = '${locationName}';
				var dropBox1=new  SwSelectBox(document.forms[0].elements["metagroupMonitor.entityId"],document.getElementById("entityName"));
				var dropBox2 = new SwSelectBox(document.forms[0].elements["metagroupMonitor.currencyId"],document.getElementById("currencyName"));
				var dropBox3 = new SwSelectBox(document.forms[0].elements["metagroupMonitor.locationId"],document.getElementById("locationName"));

						if(filterstatus !="")
					 {
						var filterStatus1 ;
						filterStatus1 = filterstatus.split(",");
					 }

			for (i = 0; i < document.getElementsByTagName("select").length; i++) {
			obj = document.getElementsByTagName("select")[i];
			obj.onblur=resetFlag2;
			obj.onmousedown=mouse_down;
			obj.onclick =click_on_drop;

			}
			}

			function onSelectTableRow(rowElement){
				var hiddenElement = rowElement.getElementsByTagName("input")[0];
			}

			var autoRefreshRate= "${requestScope.autoRefreshRate}";
			var dynamicCols = '${dynamicColsCount}';
		     var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
			function openRefreshRateWindow(methodName){
				var param = 'screenOption.do?method='+methodName;
				param +='&screenId='+<%=SwtConstants.METAGROUP_MONITOR_ID%>;
				return param;
			}
			function refreshScreen(){
				if(refreshFlagCal==true&&refreshFlag_drop_down==true){

						//To prevent closing the child window on autoRefresh
						isDoNotCloseMyChilds= true;
			           if(pageLoaded)
					   {
					   pageLoaded = false;
						submitForm('displayMetagroupDetails','');
						}
				}

				var refreshScreenCheckObj = window.setTimeout(refreshScreen, autoRefreshRate*1000);
			}

			if(autoRefreshRate != 0){
				var refreshScreenCheckObj = window.setTimeout(refreshScreen, autoRefreshRate*1000);
			}

			function clickSelectedTab(object)
			{
				var originalTabIndex = "${requestScope.selectedTabIndex}";

					if(document.forms[0].elements['metagroupMonitor.date'].value == "")
					{
						alert("<fmt:message key="alert.selectDate"/>");
					} else {
					changeselected('metagroupMonitorSelectedDateParent');
					var tabIndex = getSelectedTabIndex();
					if(originalTabIndex != tabIndex)
					 {
						expandcontent('metagroupMonitorTodayParent', object);
						submitForm("displayMetagroupDetails");
					}
				}
			}
			function checkTabIndexToday(obj) {
			changeselected('metagroupMonitorTodayParent');
			var tabIndex = getSelectedTabIndex();
			var originalTabIndex = "${requestScope.selectedTabIndex}";

			if(tabIndex != originalTabIndex) {
				expandcontent('metagroupMonitorTodayParent', obj);
				document.forms[0].elements['metagroupMonitor.date'].value = "";
				submitForm('displayMetagroupDetails');

				}
			}


			function checkTabIndexTodayPlusOne(obj) {
			changeselected('metagroupMonitorTodayPlusOneParent');
				var tabIndex = getSelectedTabIndex();
				var originalTabIndex = "${requestScope.selectedTabIndex}";

				if(tabIndex != originalTabIndex) {
						expandcontent('metagroupMonitorTodayParent', this);
						document.forms[0].elements['metagroupMonitor.date'].value = "";
						submitForm('displayMetagroupDetails');
					}

			}

			function checkTabIndexTodayPlusTwo(obj) {
			changeselected('metagroupMonitorTodayPlusTwoParent');
				var tabIndex = getSelectedTabIndex();
				var originalTabIndex = "${requestScope.selectedTabIndex}";

				if(tabIndex != originalTabIndex) {
						expandcontent('metagroupMonitorTodayParent', this);
						document.forms[0].elements['metagroupMonitor.date'].value = "";
						submitForm('displayMetagroupDetails');
						}
			}


/*Start:Code Modified by sandeep kumar for Mantis 1798  for issue found in beat testing defect number 1054_SEL_042 on 29-Mar-2012: Invalid alert while choosing Date in book group Monitor screen */
			/**
			*  This function is used to changing the date format
			*
			*/
			function parseFormattedDate(dateValue) {
				if (dateFormat == 'dd/MM/yyyy') {
					return parseDate(dateValue, 'datePat1');
				} else {
					return parseDate(dateValue);
				}
			}
		/**
		*  This function is used to validate the date
		*  @returns boolean
		*/
			function onDateKeyPress(sD1){
			   var days2=(parseFormattedDate(sD1,dateFormat).getTime())/(60*60*1000);
			   //get the today date
				var days1=(parseFormattedDate(testDate,dateFormat).getTime())/(60*60*1000);
				//Check for wehether the selected date exceeds the range 30 days from the todays date
				if(((days2-days1)/24)>29){
				alert('<fmt:message key="currMonitor.alert.dateRangeValidation"/>');
						return false;
				}
				return true;
			}
/*End:Code Modified by sandeep kumar for Mantis 1798  for issue found in beat testing defect number 1054_SEL_042 on 29-Mar-2012: Invalid alert while choosing Date in book group Monitor screen*/
			function getUpdateRefreshRequest (rate,item_id) {
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + rate;
			}

			function getMovementDetails(entityId,currencyCode,selectedTabIndex,valueDate,bookCode,baseURL){

				var requestURL = baseURL;

				requestURL = requestURL + "metagroupmonitor.do?method=countMovements";
				requestURL = requestURL + "&entityId=" + entityId;
				requestURL = requestURL + "&currencyCode=" + currencyCode;
				requestURL = requestURL + "&valueDate=" + valueDate;
				requestURL = requestURL + "&bookCode=" + bookCode;
				var noOfMovements = sendRequest(requestURL);
				if (noOfMovements == 0) {
			        alert('<fmt:message key="alert.noMovemensAvailable"/>');
				} else {
				   var requestURL = baseURL;
				
				    requestURL = requestURL + "outstandingmovement.do?method=flexBook";	
				    requestURL = requestURL + "&entityId=" + entityId;
				    requestURL = requestURL + "&currencyCode=" + currencyCode;
				    requestURL = requestURL + "&valueDate=" + valueDate;
				    requestURL = requestURL + "&bookCode=" + bookCode;
				    requestURL = requestURL + "&selectedTabIndex=" + selectedTabIndex;
				    requestURL = requestURL + "&initialinputscreen=" + "bookmonitor";
				    openWindow(requestURL,'MovementDetailsSummary','left=50,top=190,width=1280,height=780,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
	}

	window.event.returnValue = false;
	window.event.cancelBubble = true;	

	return false;
}
			function sendRequest(sURL)
			{
				var oXMLHTTP = new XMLHttpRequest();

				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var count=new Number(oXMLHTTP.responseText);
				return count;
			}

            function help(){
                  openWindow(buildPrintURL('print','Bookgroup Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
                  } 


			function getExistingDate(obj) {
				
				if(existingDate  == "") {
				existingDate = document.forms[0].elements['metagroupMonitor.date'].value;
				}
			}		// ]]>
			
		
			/**
    		  * This function is used for calling the saveFontSize() method from ScreenOption 
    		  * to store the Font size of the screen data grid
    		  * 
    		  * @param fontSize - set by the user.
    		  *
   			  */
			function getUpdateFontSize(fontSize, item_id) {
				return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + fontSize;
			}
			
		</script>
</head>

<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">


<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
	value="<fmt:message key="BookgroupMonitor.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
