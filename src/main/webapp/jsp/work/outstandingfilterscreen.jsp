<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="movSearch.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var initialscreen = "${requestScope.initialinputscreen}";

var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
/* Start : modified by <PERSON><PERSON><PERSON> for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */
var currencyFormat = '<c:out value="${sessionScope.CDM.currencyFormat}" />';
var dateFormat = '<c:out value="${sessionScope.CDM.dateFormat}" />';
var sysDateAsstring = "${requestScope.sysDateAsstring}";
var currencyCode = '${requestScope.currencyCode}';
var selectedFilter =  '${requestScope.selectedFilter}' ;
var tabpressed = '${requestScope.dateStr}';
var toDate ='${requestScope.valueToDateFrmReq}';
// get the reference flag value from request and save in referenceflag variable. 
var referenceflag = '${requestScope.refFlagFilterFrmReq}';
var openMovementFlag = '${requestScope.openMovementFlagReq}';
var posLvlId = '${posLvlId}';
var currentFilterConf = '${currentFilterConf}';

var includeRef;
var excludeRef; 
var includeRefFlag;
var excludeRefFlag;
var includeRef1Flag;
var excludeRef1Flag;
var includeRef2Flag;
var excludeRef2Flag;
var includeRef3Flag;
var excludeRef3Flag;
var includeRef4Flag;
var excludeRef4Flag;
/* End : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */
function submitForm(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	

	
}

/**
* This method is used to clear all filter criterias, while click the clear button.
* 
* @param none
* @return none
*/
function clearFilterCriteria(){
/* Start : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */
	document.forms[0].elements["movement.amountunderasstring"].value = "";
	document.forms[0].elements["movement.amountoverasstring"].value = "";
	document.forms[0].elements["groupId"].value = "";	
	document.forms[0].elements["groupName"].value = "";
	document.forms[0].elements["metaGroupName"].value = "";	
	document.forms[0].elements["metagroupId"].value = "";
	document.forms[0].elements["movement.inputDateAsString"].value = "";	
	document.forms[0].elements["movement.timefrom"].value = "";
	document.forms[0].elements["movement.timeto"].value = "";
	
	document.forms[0].elements["referencemap.includeRef"].value = "";	
	document.forms[0].elements["referencemap.excludeRef"].value = "";	
	document.forms[0].elements['referencemap.includeRefFlag'].checked = false;
	document.forms[0].elements['referencemap.includeRef1Flag'].checked = true;
	document.forms[0].elements['referencemap.includeRef2Flag'].checked = true;
	document.forms[0].elements['referencemap.includeRef3Flag'].checked = true;
	document.forms[0].elements['referencemap.includeRef4Flag'].checked = true;
	document.forms[0].elements['referencemap.excludeRefFlag'].checked = false;
	document.forms[0].elements['referencemap.excludeRef1Flag'].checked = true;
	document.forms[0].elements['referencemap.excludeRef2Flag'].checked = true;
	document.forms[0].elements['referencemap.excludeRef3Flag'].checked = true;
	document.forms[0].elements['referencemap.excludeRef4Flag'].checked = true;
	document.getElementById("movement.openMovementFlag").checked=true;	
	
	document.getElementById("movement.group").value = "";				
	document.getElementById("movement.metaGroup").value = "";	
	
	if( tabpressed == "ALL" || toDate =="ALL")
	{
	
	document.forms[0].elements["movement.valueToDateAsString"].value = "";
	document.forms[0].elements["movement.valueFromDateAsString"].value = "${requestScope.sysDateAsstring}";
	}
	else{
		if(window.opener.initialscreen == "E") {
			document.forms[0].elements["movement.valueToDateAsString"].value = '${requestScope.dateStr}';
			document.forms[0].elements["movement.valueFromDateAsString"].value = '${requestScope.dateStr}';
		} else {
			document.forms[0].elements['movement.valueFromDateAsString'].value  = "";
			document.forms[0].elements['movement.valueToDateAsString'].value  = "";
		}
	}
	/* End : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */
}

function getAmtValue(amt){
var amtvalue='';
 for(var idx=0; idx < amt.length; idx++){
	el = amt.charAt(idx);	
	if(el!=',' && el!='.'){	
	 amtvalue += el;
	}	 
 }
 return amtvalue; 
}

function validateAmount(amtover,amtunder){

  if(amtover !="" && amtunder !="")
    {
	var amountOver = new Number(getAmtValue(amtover)) ;
	var amountUnder = new Number(getAmtValue(amtunder)) ;	
	if(amountUnder >= amountOver){
	return true;
	} 
	else{
	return false;
	}

   }
   else{
	return true;
   }

}

function enableFields(){
	document.forms[0].elements['movement.valueFromDateAsString'].disabled="";
	document.forms[0].elements['movement.valueToDateAsString'].disabled="";
	document.forms[0].elements['movement.inputDateAsString'].disabled="";
	
	
}
function getRadioButtonValue(button){

var d = button;
var len = d.length ;
for(i=0;i<len;i++)
{
	if(d[i].checked)
	 return d[i].value;
}
if(i == len)
 return "null";
}


/**
* This method is used to compare two times.
* 
* @param time1
* @param time2
* @return retValue
*/
function compareTwoTimes(time1, time2)
{
// create date1 and date2 
var date1 = new Date();
var date2 = new Date();
// set retValue is true as default. 
var retValue = true;
var strTime1 = new String(time1);
var strTime2 = new String(time2);
// trim strTime1 and strTime2.
strTime1 = strTime1.trim();
strTime2 = strTime2.trim();
// check time1 and time2 length equal to 5.
if(strTime1.length==5 && strTime2.length==5)
{
var parts1 = strTime1.split(":");
date1.setHours(parts1[0]);
date1.setMinutes(parts1[1]);

parts1 = strTime2.split(":");
date2.setHours(parts1[0]);
date2.setMinutes(parts1[1]);
// check whether date2 less than date1, return false.
if(date2 < date1)
{ 
	retValue = false;
}
}
	return retValue;
}

/**
* This method is used to validate and submit the form,when click the ok button.
* 
* @param methodName
*/
function getMovementDetails(methodName) {

    if (validateCurrency(document.forms[0].elements['movement.amountoverasstring'], 'movement.amountoverasstring', currencyFormat)) {
        if (validateCurrency(document.forms[0].elements['movement.amountunderasstring'], 'movement.amountunderasstring', currencyFormat)) {
        	elementTrim(document.forms[0]);
            var amountover = document.forms[0].elements['movement.amountoverasstring'].value;
            var amountunder = document.forms[0].elements['movement.amountunderasstring'].value;
            if (validateAmount(amountover, amountunder)) {
                
				 if (validateField(document.forms[0].elements['movement.valueFromDateAsString'], 'movement.valuefrom', dateFormat)) {
                if (validateField(document.forms[0].elements['movement.valueToDateAsString'], 'movement.valueto', dateFormat)) {
                    if (comparedates(document.forms[0].elements['movement.valueFromDateAsString'].value, document.forms[0].elements['movement.valueToDateAsString'].value, dateFormat, 'From Date', 'To Date')) {
                        if (validateField(document.forms[0].elements['movement.inputDateAsString'], 'movement.inputdate', dateFormat)) {
                            var fromTimeValue = validateField(document.forms[0].elements['movement.timefrom'], 'Number 2', 'timePat');
                            if (fromTimeValue) {
                                var toTimeValue = validateField(document.forms[0].elements['movement.timeto'], 'Number 2', 'timePat');
                                if (toTimeValue) {
                                    var compareTimes = compareTwoTimes(document.forms[0].elements['movement.timefrom'].value, document.forms[0].elements['movement.timeto'].value);
                                    if (compareTimes) {
                                       
                                        var includeRefName = validateField(document.forms[0].elements['referencemap.includeRef'], 'referencemap.includeRef', 'alphaNumPatWithoutPercentage');
                                        var excludeRefName = validateField(document.forms[0].elements['referencemap.excludeRef'], 'referencemap.excludeRef', 'alphaNumPatWithoutPercentage');
                                        if (includeRefName && excludeRefName) {
                                        	includeRef = document.forms[0].elements['referencemap.includeRef'].value;
											excludeRef = document.forms[0].elements['referencemap.excludeRef'].value;
											if (document.forms[0].elements['referencemap.includeRefFlag'].checked == false) {
												includeRefFlag = "N";
									        } else {
									        	includeRefFlag = "Y";
									        }
											if (document.forms[0].elements['referencemap.includeRef1Flag'].checked == false) {
												includeRef1Flag = "N";
									        } else {
									        	includeRef1Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.includeRef2Flag'].checked == false) {
												includeRef2Flag = "N";
									        } else {
									        	includeRef2Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.includeRef3Flag'].checked == false) {
												includeRef3Flag = "N";
									        } else {
									        	includeRef3Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.includeRef4Flag'].checked == false) {
												includeRef4Flag = "N";
									        } else {
									        	includeRef4Flag = "Y";
									        }
											
											if (document.forms[0].elements['referencemap.excludeRefFlag'].checked == false) {
												excludeRefFlag = "N";
									        } else {
									        	excludeRefFlag = "Y";
									        }
											if (document.forms[0].elements['referencemap.excludeRef1Flag'].checked == false) {
												excludeRef1Flag = "N";
									        } else {
									        	excludeRef1Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.excludeRef2Flag'].checked == false) {
												excludeRef2Flag = "N";
									        } else {
									        	excludeRef2Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.excludeRef3Flag'].checked == false) {
												excludeRef3Flag = "N";
									        } else {
									        	excludeRef3Flag = "Y";
									        }
											if (document.forms[0].elements['referencemap.excludeRef4Flag'].checked == false) {
												excludeRef4Flag = "N";
									        } else {
									        	excludeRef4Flag = "Y";
									        }
											if (includeRef != '' || excludeRef != '')
											{
												if (includeRefFlag == 'N' &&  excludeRefFlag == 'N' && includeRef1Flag == 'N' && excludeRef1Flag == 'N' 
														&& includeRef2Flag == 'N' && excludeRef2Flag == 'N' && includeRef3Flag == 'N' && excludeRef3Flag 
														== 'N' && includeRef4Flag == 'N' && excludeRef4Flag == 'N') 
												{
													ShowErrMsgWindowWithBtn('' ,'<fmt:message key="alert.movementSearch.reference"/>' , YES_NO, yesContinue, noContinue );
													return;
												}
											}
											if(window.opener.initialscreen != "E") {
												enableFields();
											}else {
												if (document.forms[0].elements['movement.openMovementFlag'].checked == false) {
	                                                window.opener.document.forms[0].openMovementFlagSearch.value = "N";
	                                            } else {
	                                                window.opener.document.forms[0].openMovementFlagSearch.value = "Y";
	                                            }
											}
                                            window.opener.document.forms[0].method.value = "filterOutMovementSummary";
                                            window.opener.document.forms[0].entityId.value = '${requestScope.entityId}';
                                            window.opener.document.forms[0].currencyCode.value = '${requestScope.currencyCode}';
                                            window.opener.document.forms[0].amountover.value = amountover;
                                            window.opener.document.forms[0].amountunder.value = amountunder;
                                            window.opener.document.forms[0].valueFromDate.value = document.forms[0].elements['movement.valueFromDateAsString'].value;
                                            window.opener.document.forms[0].valueToDate.value = document.forms[0].elements['movement.valueToDateAsString'].value;
                                            window.opener.document.forms[0].group.value = document.forms[0].elements['movement.group'].value;
                                            window.opener.document.forms[0].metagroup.value = document.forms[0].elements['movement.metaGroup'].value;
                                            window.opener.document.forms[0].timefrom.value = document.forms[0].elements['movement.timefrom'].value;
                                            window.opener.document.forms[0].timeto.value = document.forms[0].elements['movement.timeto'].value;
                                            window.opener.document.forms[0].inputDate.value = document.forms[0].elements['movement.inputDateAsString'].value;
                                            window.opener.document.forms[0].reference.value = getMenuWindow().encode64(getxmlRef());
                                            window.opener.document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
                                            window.opener.document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
                                            window.opener.document.forms[0].currentFilterConf.value = currentFilterConf;
                                            window.opener.document.forms[0].selectedTab.value = tabpressed;
                                            window.opener.document.forms[0].posLvlId.value = '${posLvlId}';
                                            
                                            
                                            
                                            var isReset = isFilterReset();
                                            window.opener.callApp(isReset);
                                            self.close();
                                        } else {
                                            document.forms[0].elements['referencemap.includeRef'].focus();
                                        }
                                       
                                    } else {
                                        alert('<fmt:message key="alert.movementSearch.compareTwoTime"/>');
                                        document.forms[0].elements['movement.timeto'].focus();
                                        return false;
                                    }
                                } else {
                                    document.forms[0].elements['movement.timeto'].focus();
                                }
                            } else {
                                document.forms[0].elements['movement.timefrom'].focus();

                            }
                        }
                    }

                }
               	
            } 
			}else {
                alert('<fmt:message key="movSearch.alert.amountoverLess"/>');
            }
        } else {
            document.forms[0].elements['movement.amountunderasstring'].focus();
        }
    } else {
        document.forms[0].elements['movement.amountoverasstring'].focus();
    }
}
function yesContinue() {
	var amountover = document.forms[0].elements['movement.amountoverasstring'].value;
    var amountunder = document.forms[0].elements['movement.amountunderasstring'].value;
	if(window.opener.initialscreen != "E") {
		enableFields();
	}else {
		if (document.forms[0].elements['movement.openMovementFlag'].checked == false) {
            window.opener.document.forms[0].openMovementFlagSearch.value = "N";
        } else {
            window.opener.document.forms[0].openMovementFlagSearch.value = "Y";
        }
	}
    window.opener.document.forms[0].method.value = "filterOutMovementSummary";
    window.opener.document.forms[0].entityId.value = '${requestScope.entityId}';
    window.opener.document.forms[0].currencyCode.value = '${requestScope.currencyCode}';
    window.opener.document.forms[0].amountover.value = amountover;
    window.opener.document.forms[0].amountunder.value = amountunder;
    window.opener.document.forms[0].valueFromDate.value = document.forms[0].elements['movement.valueFromDateAsString'].value;
    window.opener.document.forms[0].valueToDate.value = document.forms[0].elements['movement.valueToDateAsString'].value;
    window.opener.document.forms[0].group.value = document.forms[0].elements['movement.group'].value;
    window.opener.document.forms[0].metagroup.value = document.forms[0].elements['movement.metaGroup'].value;
    window.opener.document.forms[0].timefrom.value = document.forms[0].elements['movement.timefrom'].value;
    window.opener.document.forms[0].timeto.value = document.forms[0].elements['movement.timeto'].value;
    window.opener.document.forms[0].inputDate.value = document.forms[0].elements['movement.inputDateAsString'].value;
    window.opener.document.forms[0].reference.value = getMenuWindow().encode64(getxmlRef());
    window.opener.document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
    window.opener.document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
    window.opener.document.forms[0].currentFilterConf.value = currentFilterConf;
    window.opener.document.forms[0].selectedTab.value = tabpressed;
    window.opener.document.forms[0].posLvlId.value = '${posLvlId}';
    
    
    
    var isReset = isFilterReset();
    window.opener.callApp(isReset);
    self.close();
	
}
function noContinue() {
	document.forms[0].elements['referencemap.includeRef'].focus();
}

function isFilterReset () {
	var inputdate = document.forms[0].elements['movement.inputDateAsString'].value;
	
	var amountunderTest  = document.forms[0].elements["movement.amountunderasstring"].value == "";
	var amountoverasstringTest = document.forms[0].elements["movement.amountoverasstring"].value == "";
	var groupIdTest = document.forms[0].elements["groupId"].value == "";	
	var groupNameTest = document.forms[0].elements["groupName"].value == "";
	var metaGroupNameTest = document.forms[0].elements["metaGroupName"].value == "";	
	var metagroupIdTest = document.forms[0].elements["metagroupId"].value == "";
	var inputDateAsStringTest = document.forms[0].elements["movement.inputDateAsString"].value == "";	
	var timefromTest = document.forms[0].elements["movement.timefrom"].value == "";
	var timetoTest = document.forms[0].elements["movement.timeto"].value == "";
	var	openMovementFlagTest = document.getElementById("movement.openMovementFlag").checked==true;
	var openMovementFlagTest ;
	var valueFromDateAsStringTest ;
	var valueToDateAsStringTest ;
	if(window.opener.initialscreen == "E") {
		valueFromDateAsStringTest = document.forms[0].elements['movement.valueFromDateAsString'].value  == tabpressed;
		valueToDateAsStringTest = document.forms[0].elements['movement.valueToDateAsString'].value  == tabpressed;
	}else {
		valueFromDateAsStringTest = document.forms[0].elements['movement.valueFromDateAsString'].value  == "";
		valueToDateAsStringTest = document.forms[0].elements['movement.valueToDateAsString'].value  == "";
	}
	
	
	if(includeRef== "" && 	excludeRef == "" &&  includeRefFlag == "N" &&excludeRefFlag== "N" && 	includeRef1Flag == "Y" &&
	excludeRef1Flag == "Y" && 	includeRef2Flag == "Y" && 	excludeRef2Flag == "Y" && 	includeRef3Flag == "Y" && 	excludeRef3Flag == "Y" &&
	includeRef4Flag == "Y" &&	excludeRef4Flag == "Y" && amountunderTest && amountoverasstringTest && groupIdTest &&
	groupNameTest && metaGroupNameTest && metagroupIdTest && inputDateAsStringTest && timefromTest && timetoTest && 
	openMovementFlagTest && valueFromDateAsStringTest && valueToDateAsStringTest) {
		return true;
	}else {
		return false;
	}
}
function validateFormAmount(objForm){
  var elementsRef = new Array(2);

 
  elementsRef[0] = objForm.elements["movement.amountoverasstring"];
  elementsRef[1] = objForm.elements["movement.amountunderasstring"];


  if(elementsRef[0].value != "" && elementsRef[1].value != "")
{
  var amountover=parseFloat(elementsRef[0].value);
  var amountunder=parseFloat(elementsRef[1].value);
 
  if(amountover > amountunder)
	{
	  alert('<fmt:message key="movSearch.alert.amountoverLess"/>');
	  return false;
	}
	else
    return true;
}
else
	return true;
}

function validateFormTime(objForm){
  var elementsRef = new Array(2);
 
 
  elementsRef[0] = objForm.elements["movement.timefrom"];
  elementsRef[1] = objForm.elements["movement.timeto"];


  if(elementsRef[0].value != "" && elementsRef[0].value != "")
{
  if(elementsRef[0].value > elementsRef[1].value)
	{
	    alert('<fmt:message key="movSearch.alert.time"/>');
        return false;
	}

  else

	    return true;
 
}	
else
return true;

} 

function checkDate()
{

var enteredDate = document.forms[0].elements['movement.valueFromDateAsString'].value

if(enteredDate!=""){

if(compareTwoDates(sysDateAsstring,enteredDate,dateFormat)== true)
		{
		
		getMovementDetails('search');
		}
	else 
		{
		alert('<fmt:message key="movSearch.alert.valueFrom"/>');
		return false;
		}
	}
else{
	document.forms[0].elements['movement.valueFromDateAsString'].value = sysDateAsstring ;
	getMovementDetails('search')
}

}

/**
* This method is used to set the default values, when screen loading.
* 
* @param none
* @return none
*/
function bodyOnLoad()
{

var divElement = document.getElementById("dropdowndiv_1");
	var selectElement = document.forms[0].elements["movement.group"];
	var idElement = document.forms[0].elements["groupId"];
	var descElement = document.forms[0].elements["groupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_1"];
	var idLength = 12;
	var descLength = 30;

	var groupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	
	// Replace &nbsp; as empty space in groupname. 
	var groupNameTemp = new String(document.forms[0].elements["groupName"].value);
	document.forms[0].elements["groupName"].value = groupNameTemp.replace(/&nbsp\;/g," ");	
	
/* Start : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */
var divElement = document.getElementById("dropdowndiv_2");
	var selectElement = document.forms[0].elements["movement.metaGroup"];
	var idElement = document.forms[0].elements["metagroupId"];
	var descElement = document.forms[0].elements["metaGroupName"];
	var arrowElement = document.forms[0].elements["dropdownbutton_2"];
	var idLength = 12;
	var descLength = 30;
	var metaGroupDropBoxElement = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	
	// Replace &nbsp; as empty space in metaGroupName.	
	var metaGroupNameTemp = new String(document.forms[0].elements["metaGroupName"].value);
	document.forms[0].elements["metaGroupName"].value = metaGroupNameTemp.replace(/&nbsp\;/g," ");
	// If reference flag is Y in request, reference check box is selected as default.	
	if(openMovementFlag == 'N'){
		document.forms[0].elements['movement.openMovementFlag'].checked = false;
	}else{
	document.forms[0].elements['movement.openMovementFlag'].checked = true;
	}
	/* End : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" */	
	
	if(window.opener.initialscreen != "E") {
		document.forms[0].elements['movement.valueFromDateAsString'].value="";
		document.forms[0].elements['movement.valueFromDateAsString'].disabled="true";
		document.forms[0].elements['movement.valueToDateAsString'].value="";
		document.forms[0].elements['movement.valueToDateAsString'].disabled="true";
		document.forms[0].elements['movement.openMovementFlag'].disabled = "true";
		document.getElementById("movement.openMovementFlag").checked=true;	
		
	}
	validateCurrency(document.forms[0].elements['movement.amountoverasstring'], 'movement.amountoverasstring', currencyFormat);
	validateCurrency(document.forms[0].elements['movement.amountunderasstring'], 'movement.amountunderasstring', currencyFormat);
}


function populateDropBoxes()
{
	ShowErrMsgWindow('${actionError}');	
	bodyOnLoad();	
}

function comparedates(date1,date2,format,date1Caption,date2Caption)
{
	var retValue = "true";
	var strdate1 = new String(date1);
	var strdate2 = new String(date2);
	
	if( typeof strdate1 != 'undefined' && strdate1 != null && typeof strdate2 != 'undefined' && strdate2 != null)
	{
		strdate1 = strdate1.trim();
		strdate2 = strdate2.trim();
		if(strdate1.length > 0 && strdate2.length > 0)
		{
			if(format == "datePat1")
			{
				var parts = date1.split("/");
				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[0]);
				date1.setMonth(parts[1] - 1);			

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[0]);
				date2.setMonth(parts[1] - 1);			

				if(date2 < date1)
					retValue = "false";
			}
			if(format == "datePat2")
			{
				var parts = date1.split("/");

				date1 = new Date(0);
				date1.setFullYear(parts[2]);
				date1.setDate(parts[1]);
				date1.setMonth(parts[0] - 1);			

				var parts = date2.split("/");
				date2 = new Date(0);
				date2.setFullYear(parts[2]);
				date2.setDate(parts[1]);
				date2.setMonth(parts[0] - 1);			

				if(date2 < date1)
					retValue = "false";

			}
		}

	}
	if(retValue =="false"){
	alert('' + date2Caption + '<fmt:message key="movSearch.alert.dateComparison"/>' + date1Caption+'');
	 return false;
	 }else{
	 return true;
	 }
}
	// Return a string that contains a full reference chosen by user (includes and excludes)
	// It will be like <refparams><include ref1="N" ref2="Y" ref3="Y" ref4="Y" like="Y">ABCD</include><exclude ref1="Y" ref2="Y" ref3="Y" ref4="Y" like="Y">EFGH</exclude></refparams>
	function getxmlRef() {
		var includeArray = [includeRef1Flag, includeRef2Flag, includeRef3Flag, includeRef4Flag];
		var excludeArray = [excludeRef1Flag, excludeRef2Flag, excludeRef3Flag, excludeRef4Flag];

		var xmlDocument = "<refparams>";
		var includeNode = "<include ";
		var excludeNode = "<exclude ";
		for (var i=0;i<4;i++) {
		 	includeNode +=  "ref" + (i+1) + "=\"" + includeArray[i] + "\" ";
		    excludeNode +=  "ref" + (i+1) + "=\"" + excludeArray[i] + "\" ";
		}
		if (includeRefFlag == 'Y') {
			includeNode += "like=\"Y\" ><![CDATA[" + includeRef + "]]></include>";
		} else {
			includeNode += "like=\"N\" ><![CDATA[" + includeRef + "]]></include>";
		}
		if (excludeRefFlag == 'Y') {
			excludeNode += "like=\"Y\" ><![CDATA[" + excludeRef + "]]></exclude>";
		} else {
			excludeNode += "like=\"N\" ><![CDATA[" + excludeRef + "]]></exclude>";
		}
		xmlDocument += includeNode + excludeNode;
		return (xmlDocument + "</refparams>");
	}

</SCRIPT>

<SCRIPT language="JAVASCRIPT">
  var dateFormatValue = '${fn:escapeXml(sessionScope.CDM.dateFormatValue)}';
	var cal = new CalendarPopup("caldiv",false,"calFrame");
    cal.setCssPrefix("CAL");
	cal.offsetX = 18;
    cal.offsetY = 0;
	
	var cal2 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal2.setCssPrefix("CAL");
    cal2.offsetX = -85;
    cal2.offsetY = 20;
	 
	var cal3 = new CalendarPopup("caldiv",false,"calFrame"); 
    cal3.setCssPrefix("CAL");
    cal3.offsetX = 18;
    cal3.offsetY = 0;

</SCRIPT>

</head>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();" onunload="call()">

<form action="outstandingmovement.do" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedEntityName" type="hidden" value="">
<input name="initialinputscreen" type="hidden" value="">
<c:set var="CDM" value="${sessionScope.CDM}" />



<!----------------------------------caldiv----------------------------------------------------------------->
<div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
	</iframe>
<!-----------------------------------end of caldiv----------------------------------------------->

<!--------------entity drop down---------------->



<div id="dropdowndiv_1" style="z-index:99;position:absolute;width:200px;left:156px;top:111px;visibility:hidden" class="swdropdown">
    <select class="htmlTextFixed" id="movement.group" name="movement.group" size="10" style="width:437px;z-index:99;">
        <c:forEach var="group" items="${requestScope.groupdetails}">
            <option value="${group.value}">${group.label}</option>
        </c:forEach>
    </select>
</div>

<div id="dropdowndiv_2" style="z-index:99;position:absolute;width:200px;left:156px;top:146px;visibility:hidden" class="swdropdown">
    <select class="htmlTextFixed" id="movement.metaGroup" name="movement.metaGroup" size="10" style="width:437px;z-index:99;">
        <c:forEach var="metaGroup" items="${requestScope.metagroupdetails}">
            <option value="${metaGroup.value}">${metaGroup.label}</option>
        </c:forEach>
    </select>
</div>

		<!--------------------start of extra fieldset------------------------------------------>
		<!--   Start : modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" -->
		<div style="position:absolute; border:2px outset;height:300px;width:585px;left:10px; top:10px;">
			<fieldset style="width:585px;border:2px ;height:300px;">
				<div style="position:absolute; left:8px; top:5px;">
					<table width="576px" border="0" cellpadding="0" cellspacing="1" height="18">  
						<!------value date---->
							<tr height="20px">						
						<td width="114px">
						<B><fmt:message key="movementsearch.amountover"/></B>
						</td>
						<td width="3px">&nbsp;</td>
						<td width="162px">

						<input type="text"
							   tabindex="21"
							   name="movement.amountoverasstring"
							   style="width:160px;"
							   titleKey="tooltip.amountOver"
							   onchange="return validateCurrency(this, 'movement.amountover', currencyFormat, currencyCode);"
							   class="htmlTextNumeric"
							   maxlength="28" />
					</td>
				
					
						<td width="60px">&nbsp;</td>
						<td width="25px">
							<B><fmt:message key="movementsearch.valueto"/></B></td>
						
						<td width="8px">&nbsp;</td>
						<td width="160px" height="28">
							<input type="text"
								   titleKey="tooltip.amountUnder"
								   tabindex="22"
								   class="htmlTextNumeric"
								   name="movement.amountunderasstring"
								   style="width:160px;"
								   onchange="return validateCurrency(this, 'movement.amountunder', currencyFormat, currencyCode);"
								   maxlength="28" />
						</td>
						</tr>
					</table>
					<table width="576px" border="0" cellpadding="0" cellspacing="1" height="19">  
						<tr height="20px">
							<td width="115px">
							<B><fmt:message key="movementsearch.valuefrom"/></B></td>
							
							<td width="3px">&nbsp;</td>
							<td width="164px">
							<input type="text"
									   titleKey="tooltip.enterValueDateFrom"
									   tabindex="23"
									   maxlength="10"
									   class="htmlTextAlpha"
									   name="movement.valueFromDateAsString"
									   style="width:80px; margin-bottom: 5px;"
									   onchange="return validateField(this, 'movement.valuefrom', dateFormat);" />
								<a title="${sessionScope['tooltip.selectFromDate']}"
								   tabindex="-24"
								   name="datelink"
								   id="datelink"
								   onkeydown="submitEnter(this, event)"
								   onclick="cal.select(document.forms[0].elements['movement.valueFromDateAsString'], 'datelink', dateFormatValue); return false;">
									<img src="images/calendar-16.gif" />
								</a>
							</td>

							<td width="60px">&nbsp;</td>
							<td width="25px"><B><fmt:message key="movementsearch.valueto"/></B></td>
							<td width="9px">&nbsp;</td>
							<td width="162px">
								
								    <input type="text"
										   titleKey="tooltip.enterValueDateTo"
										   tabindex="25"
										   maxlength="10"
										   class="htmlTextAlpha"
										   name="movement.valueToDateAsString"
										   style="width:80px; margin-bottom: 5px;"
										   onchange="return validateField(this, 'movement.valueto', dateFormat);" />
									<a title="${sessionScope['tooltip.selectToDate']}"
									   tabindex="26"
									   name="datelink2"
									   id="datelink2"
									   onkeydown="submitEnter(this, event)"
									   onclick="cal2.select(document.forms[0].elements['movement.valueToDateAsString'], 'datelink2', dateFormatValue); return false;">
										<img src="images/calendar-16.gif" />
									</a>
								
							</td>
						</tr>

						
						
					</table>
							<table width="576px" border="0" cellpadding="0" cellspacing="1" height="100">  
						
					
						<tr height="20px" border="0">
							<td width="120px"><B><fmt:message key="movementsearch.group"/></B></td>
							<td width="3px">&nbsp;</td>
							<td width="145px" >
							 <input styleclass="textAlpha" name="groupId" tabindex="2" style="width:120px;" title='<fmt:message key="tooltip.groupId"/>' disabled><input title='<fmt:message key="tooltip.clickSelGroupId"/>' id="dropdownbutton_1" tabindex="27" type="button" value="..." ></td>
							   <td width="1px">&nbsp;</td>
							   <td><input styleclass="textAlpha" style="width:280px;background:transparent; border: thin;" name="groupName" size="20" tabindex="-1" readonly="readonly">
						
							</td>
					
						</tr>
					
						<tr height="20px" >
							<td width="120px"><B><fmt:message key="movementsearch.metagroup"/></B></td>
							<td width="5px">&nbsp;</td>
							<td width="140px">
							 <input styleclass="textAlpha1" name="metagroupId" tabindex="2" style="width:120px;" title='<fmt:message key="metaGroup.id.mgroupId1"/>' disabled><input title='<fmt:message key="tooltip.clickSelMetaGroupId"/>' id="dropdownbutton_2" tabindex="28" type="button" value="..." ></td>
							   <td width="1px">&nbsp;</td>
							   <td><input styleclass="textAlpha" style="width:280px;background:transparent; border: thin;" name="metaGroupName" size="20" tabindex="-1" readonly="readonly">
							</td>
					
						</tr>
					
						<tr height="20px">
							<td width="120px">
								<B><fmt:message key="movementsearch.inputdate"/></B></td>
							<td width="5px">&nbsp;</td>
						
							<td width="140px">
								<input type="text"
									   name="movement.inputDateAsString"
									   class="htmlTextAlpha"
									   disabled="false"
									   titleKey="tooltip.enterInputDate"
									   tabindex="29"
									   maxlength="10"
									   style="width:80px; margin-bottom: 5px;"
									   onchange="return validateField(this, 'movement.inputdate', dateFormat);" />
								&nbsp;
								<a title="${sessionScope['tooltip.selectInputDate']}"
								   tabindex="30"
								   name="datelink3"
								   id="datelink3"
								   onclick="cal3.select(document.forms[0].elements['movement.inputDateAsString'], 'datelink3', dateFormatValue); return false;"
								   onkeydown="submitEnter(this, event)">
									<img src="images/calendar-16.gif" />
								</a>
							</td>
							<td width="300px" colspan="5">&nbsp;
							</td>
						</tr>
						</table>

						<!-----time----->
						<table width="576px" border="0" cellpadding="0" cellspacing="1" height="27"> 
						<tr height="20px">
							<td width="115px"><B><fmt:message key="movementsearch.timefrom"/></B>
							</td>
							<td width="3px">&nbsp;</td>
							<td width="164px">
								<input type="text"
									   titleKey="tooltip.enterTomeFrom"
									   tabindex="31"
									   name="movement.timefrom"
									   class="htmlTextNumeric"
									   style="width:44px;"
									   onchange="return validateField(this, 'movement.timefrom', 'timePat');" />
							</td>
							<td width="60px">&nbsp;</td>
							<td width="25px"><B><fmt:message key="movementsearch.timeto"/></B></td>
							<td width="9px">&nbsp;</td>
							<td width="88px">
								<input type="text"
									   name="movement.timeto"
									   titleKey="tooltip.enterTimeTo"
									   tabindex="32"
									   style="width:44px;"
									   class="htmlTextNumeric"
									   onchange="return validateField(this, 'movement.timeto', 'timePat')" />
							</td>
							<td width="72px">&nbsp;</td>
						</tr>
					</table>
					<table width="576px" border="0" cellpadding="0" cellspacing="1" height="27">  
					
						<tr height="30px">
							<td width="191px"><B><fmt:message key="movementsearch.reference"/></B></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
							</td>
							<td width="5px">&nbsp;</td>
							<td width="40px"><B><fmt:message key="movementsearch.like"/></B></td>
							<td width="40px"><B><fmt:message key="movementsearch.ref1"/></B></td>
							<td width="40px"><B><fmt:message key="movementsearch.ref2"/></B></td>
							<td width="40px"><B><fmt:message key="movementsearch.ref3"/></B></td>
							<td width="40px"><B><fmt:message key="movementsearch.extra"/></B></td>
						</tr>
						<tr height="20px">
							<td width="191px" align="right"><b><fmt:message key="movementsearch.include" />&nbsp;&nbsp;&nbsp;</b></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
								<input type="text"
									   name="referencemap.includeRef"
									   class="htmlTextAlpha"
									   titleKey="tooltip.enterRefIncl"
									   tabindex="33"
									   style="width:200px;"
									   onchange="return validateField(this, 'referencemap.includeRef', 'alphaNumPatWithoutPercentage');" />
							</td>
							<td width="5px">&nbsp;</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.includeRefFlag"
									   style="width:13px; margin-left: 5px;"
									   titleKey="tooltip.likeFlag"
									   value="Y"
									   <c:if test="${request.referencemap.includeRefFlag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.includeRef1Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.includeRef1Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.includeRef2Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.includeRef2Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.includeRef3Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.includeRef3Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.includeRef4Flag"
									   style="width:13px; margin-left: 8px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.includeRef4Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
						</tr>
						<tr height="20px">
							<td width="191px" align="right"><b><fmt:message key="movementsearch.exclude" />&nbsp;&nbsp;&nbsp;</b></td>
							<td width="3px">&nbsp;</td>
							<td width="102px" colspan="6">
								<input type="text"
									   name="referencemap.excludeRef"
									   class="htmlTextAlpha"
									   titleKey="tooltip.enterRefExcl"
									   tabindex="33"
									   style="width:200px;"
									   onchange="return validateField(this, 'referencemap.excludeRef', 'alphaNumPatWithoutPercentage');" />
							</td>
							<td width="5px">&nbsp;</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.excludeRefFlag"
									   style="width:13px; margin-left: 5px;"
									   titleKey="tooltip.likeFlag"
									   value="Y"
									   <c:if test="${request.referencemap.excludeRefFlag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.excludeRef1Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.excludeRef1Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.excludeRef2Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.excludeRef2Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.excludeRef3Flag"
									   style="width:13px; margin-left: 6px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.excludeRef3Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
							<td width="70px">
								<input type="checkbox"
									   name="referencemap.excludeRef4Flag"
									   style="width:13px; margin-left: 8px;"
									   titleKey="tooltip.refFlag"
									   value="Y"
									   <c:if test="${request.referencemap.excludeRef4Flag == 'Y'}">checked</c:if>
									   class="htmlTextAlpha"
									   tabindex="34" />
							</td>
						</tr>

					</table>
                  <table width="150px" border="0" cellpadding="0" cellspacing="1" height="27">  
                       <tr height="30px">
							<td width="170px"><B><fmt:message key="movementsearch.openMovements"/></B></td>
							<td width="30px">
								<input type="checkbox"
									   name="movement.openMovementFlag"
									   value="Y"
									   <c:if test="${request.movement.openMovementFlag == 'Y'}">checked</c:if>
									   style="width:13px;"
									   titleKey="tooltip.openMovementFlag"
									   class="htmlTextAlpha"
									   tabindex="35" />
							</td>
						</tr>
						</table>
				</fieldset>
		   </div>
		<!-- End: modified by sandeepkumar for Mantis 1756 :"Excluded Outstandings, searching movements restricted to Todays Date" -->
		<!---------------------end of extra fieldset------------------------------------------>

	</div>
</div>

<!-----------------------Print button------------------------->
<div id="PrintDiv" style="position:absolute; left:530; top:340px; width:70; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
				 <a tabindex="38" href=# onclick="javascript:openWindow(buildPrintURL('print','Movement Filter'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
		   </td>

			<td align="right" id="Print">
				<a tabindex="39" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0"  title='<fmt:message key="tooltip.printScreen"/>'/></a>	
			</td>
		</tr>
	</table>
	
</div>
<!--------------------------end of print button-------------------------->


<!-----------------Ok and close buttons-------------------------------->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:10; top:330; width:590px; height:39px; visibility:visible;">
  <div id="FilterOutMov" style="position:absolute; left:6; top:4; width:585px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	
		<tr>
			<!-- Ok button is used to submit the selected filter criteria values. -->		
			<td width="70" id="okbutton">			
			<a title='<fmt:message key="tooltip.ok"/>'  tabindex="35" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:getMovementDetails('search')"><fmt:message key="button.ok"/></a>			
			</td>
			<td>&nbsp;&nbsp;</td>
			<!-- Clear button is used to clear the selected filter criteria values. -->
			<td width="70" id="clearbutton">			
			<a title='<fmt:message key="tooltip.clear"/>'  tabindex="36" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:clearFilterCriteria()"  ><fmt:message key="button.clear"/></a>			
			</td>
			<td>&nbsp;&nbsp;</td>						
			<!-- Close button is used to close the Filter Movement search. -->
			<td width="70" id="closebutton">		
			<a title='<fmt:message key="tooltip.cancel"/>' tabindex="37" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>			
			</td>
  		 </tr>
	  		 
  </table>
</div>
<div>

<!-----------------------end buttons---------------------------------->

</form>
</body>
</html>