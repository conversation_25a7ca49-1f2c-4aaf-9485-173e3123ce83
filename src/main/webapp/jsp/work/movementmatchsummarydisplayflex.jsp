<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.match != 'manual'}">
        <title><fmt:message key="mvmMatchSummDisplay.title.window" /></title>
    </c:when>
    <c:when test="${requestScope.method == 'movementDisplay'}">
        <title><fmt:message key="mvmMatchSummDisplay.title.window" /></title>
    </c:when>
    <c:when test="${requestScope.calledFrom == 'msd'}">
        <title><fmt:message key="mvmMatchSummDisplay.title.window" /></title>
    </c:when>
    <c:when test="${requestScope.match == 'manual'}">
        <title><fmt:message key="manualMatch.title.window" /></title>
    </c:when>
</c:choose>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setTitleSuffix(document.forms[0]);"
	onunload="deleteLock();call();bodyunload()">
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script language="JAVASCRIPT" src="js/movementsummary.js"></script>
<script language="JAVASCRIPT" >
        var screenRoute = "MovementMatchSummaryDisplay";
	/* var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();
	label["text"]["entityname"] = "<fmt:message key="matchQuality.entityId"/>";	
	label["tip"]["entityname"] = "<fmt:message key="matchQuality.entityId"/>";
	label["text"]["matchid"] = "<fmt:message key="matchQuality.matchId"/>";	
	label["tip"]["matchid"] = "<fmt:message key="matchQuality.matchId"/>";
    label["text"]["currencycode"] = "<fmt:message key="matchQuality.currencyCode"/>";	
	label["tip"]["currencycode"] = "<fmt:message key="matchQuality.currencyCode"/>";
	label["text"]["posTotal"] = "<fmt:message key="matchQuality.posTotal"/>";	
	label["tip"]["posTotal"] = "<fmt:message key="matchQuality.posTotal"/>";
	label["text"]["internal"] = "<fmt:message key="matchQuality.posTotalInternal"/>";	
	label["tip"]["internal"] = "<fmt:message key="matchQuality.posTotalInternal"/>";
	label["text"]["external"] = "<fmt:message key="matchQuality.posTotalExternal"/>";	
	label["tip"]["external"] = "<fmt:message key="matchQuality.posTotalExternal"/>";
	label["text"]["button-previous"] = "<fmt:message key="button.previous"/>";	
	label["tip"]["button-previous"] = "<fmt:message key="tooltip.previousMatch"/>";
	label["text"]["button-next"] = "<fmt:message key="button.next"/>";	
	label["tip"]["button-next"] = "<fmt:message key="tooltip.nextMatch"/>";
	label["text"]["button-notes"] = "<fmt:message key="button.notes"/>";	
	label["tip"]["button-notes"] = "<fmt:message key="tooltip.matchNotes"/>";
	label["text"]["button-log"] = "<fmt:message key="button.log"/>";	
	label["tip"]["button-log"] = "<fmt:message key="tooltip.logSelMvm"/>";
	label["text"]["button-mvmnt"] = "<fmt:message key="button.mvmnt"/>";	
	label["tip"]["button-mvmnt"] = "<fmt:message key="tooltip.showSelMovDetail"/>";
	label["text"]["button-suspend"] = "<fmt:message key="button.suspend"/>";	
	label["tip"]["button-suspend"] = "<fmt:message key="tooltip.suspMatch"/>";
	label["text"]["button-unmatch"] = "<fmt:message key="button.unmatch"/>";	
	label["tip"]["button-unmatch"] = "<fmt:message key="tooltip.unMatch"/>";
	label["text"]["button-confirm"] = "<fmt:message key="button.confirm"/>";	
	label["tip"]["button-confirm"] = "<fmt:message key="tooltip.ConfMatch"/>";
	label["text"]["button-reconcile"] = "<fmt:message key="button.reconcile"/>";	
	label["tip"]["button-reconcile"] = "<fmt:message key="tooltip.reconMatch"/>";
	label["text"]["button-remove"] = "<fmt:message key="button.remove"/>";	
	label["tip"]["button-remove"] = "<fmt:message key="tooltip.removeSelMov"/>";
	label["text"]["button-add"] = "<fmt:message key="button.add"/>";	
	label["tip"]["button-add"] = "<fmt:message key="tooltip.addMov"/>";
	label["text"]["button-close"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
	label["text"]["button-options"] = "<fmt:message key="button.options"/>";
	label["tip"]["button-options"] = "<fmt:message key="tooltip.options"/>";
	label["text"]["label-matchIDFiledAmended"] = "<fmt:message key="label.matchIDFiledAmended"/>";
	label["text"]["label-matchUserByAnotherProcess"] = "<fmt:message key="label.matchUserByAnotherProcess"/>";
	label["text"]["label-movementCannotBeUnlocked"] = "<fmt:message key="label.movementCannotBeUnlocked"/>";
	label["text"]["label-matchDifferentAmountTotalsAcrossPositionLevels"] = "<fmt:message key="label.matchDifferentAmountTotalsAcrossPositionLevels"/>";
	label["text"]["label-includedItemsAtMultiplePositionLevels"] = "<fmt:message key="label.includedItemsAtMultiplePositionLevels"/>";
	label["text"]["label-includedItemsForExternalBalance"] = "<fmt:message key="label.includedItemsForExternalBalance"/>";
	label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
	label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
	label["text"]["label-matchHasBeenChanged"] = "<fmt:message key="label.matchHasBeenChanged"/>";
	label["text"]["label-matchIsInUseByanotherProcess"] = "<fmt:message key="label.matchIsInUseByanotherProcess"/>";
	label["text"]["label-resourceBusy"] = "<fmt:message key="label.resourceBusy"/>";
	label["text"]["label-validMatchId"] = "<fmt:message key="label.validMatchId"/>";
	label["text"]["label-MvmtAreBusy"] = "<fmt:message key="label.mvmtAreBusy"/>";
	label["text"]["label-matchHasBeenChanged"] = "<fmt:message key="label.matchHasBeenChanged"/>";
	label["text"]["label-matchChangedByanotherUser"] = "<fmt:message key="label.matchChangedByanotherUser"/>";
	label["text"]["label-movementCannotBeUnlocked"] = "<fmt:message key="label.movementCannotBeUnlocked"/>";
	label["text"]["label-doesnotexist"] = "<fmt:message key="match.doesnotexist"/>";
	label["text"]["alert-error"] = "<fmt:message key="screen.alert.error"/>";
	label["text"]["label-movement"] = "<fmt:message key="manualInput.id.movementId"/>";
	label["text"]["label-usedBye"] = "<fmt:message key="label.isInBusyBy"/>";*/
	
	var screenId = '${requestScope.screenId}';
	var hostId = '${requestScope.hostId}';
	var userId = '${requestScope.userId}';	
	var currentFontSize = '${requestScope.fontSize}';
	var day="${requestScope.queueTabSelected}";
	var matchCount = "${requestScope.matchCount}";
	var currencyCode = "${requestScope.currencyCode}";
	var status = "${requestScope.status}";
	var date="${requestScope.date}";
	var match = "${requestScope.match}";
	var entityId = "${requestScope.entityId}";
	var quality="${requestScope.quality}";
	var archiveId="${requestScope.archiveId}";
	var menuAccessId="${requestScope.menuAccessId}";
	var applyCurrencyThreshold = "${requestScope.applyCurrencyThreshold}";
	var noIncludedMovementMatches="${requestScope.noIncludedMovementMatches}";
	var dateTabIndFlag="${requestScope.dateTabIndFlag}";
	var dateTabInd="${requestScope.dateTabInd}";
	var screen = "${requestScope.screen}";
	var inputMethod="${requestScope.method}";
	var matches = "${requestScope.matchList}";
	var currentIndex = "${requestScope.currentIndex}";
	var calledFrom = "${requestScope.calledFrom}";
	var matchIdFromGeneric = "${requestScope.matchIdFromGeneric}";
	var movId = "${requestScope.movId}";
	var selectedList = "${requestScope.selectedList}";
	var selectedTab = "${requestScope.selectedTab}";
	var currencyId = "${requestScope.currencyId}";
	var matchNotOnFile = "${requestScope.matchNotOnFile}";
	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	var accessInd = "${requestScope.accessInd}";
	var currentIndex = "${requestScope.currentIndex}";
	var manualMatch = "${requestScope.screen}";
	var valueDate = "${requestScope.valueDate}";
	var inptMethod="${requestScope.method}";	
	var matchId = "${requestScope.matchId}";	 
	 var parentScreen ="${requestScope.parentScreen}"; 
	 var loggedInUser ="${requestScope.loggedInUser}";	  
	var notesImage = "images/notes.gif";
	var blankImage = "images/blank.png";
	var redAlertImage = "images/Alert/alert.png";
	var yellowAlertImage = "images/Alert/DAlert.gif";
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL= requestURL.substring(0,idy+1) ;
	var initialscreen="movementmatchsummarydisplay";
	var currentUser="<%=SwtUtil.getCurrentUserId(request.getSession())%>";
	var scenarioId ="${requestScope.scenarioId}";
	var currGrp ="${requestScope.currGrp}";
	var accountAccessStatus = "${requestScope.accountAccessStatus}";
	var alertOrangeImage = "images/Alert/scenario/normal.png";
	var alertRedImage = "images/Alert/scenario/critical.png";
	var blankImage = "images/blank.png";
	var selectedMovementForLock="";
	function setSelectedMovementForLock(selected){
		selectedMovementForLock = selected;
	}
	function showMatchNotes(methodName,matchIds,entityid){
		var param = 'notes.do?method='+methodName+'&matchId=';			 
		param += matchIds;
		param += '&entityCode=' + entityid;
		param += '&screenName=' + 'matchScreen';	
		param +='&archiveId='+archiveId;
	
		openWindow(param,'NotesWindow','left=50,top=190,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes','true');

	}
 function matchLog(methodName,matchIds,updateDate) {
 	var param = 'auditlog.do?method='+methodName;
	param += '&selectedMatchId=';
	param += matchIds;
	param += '&statusDate='+updateDate;
	openWindow(param,'matchLogWindow','left=50,top=190,width=615,height=565,toolbar=0, resizable=yes, scrollbars=yes','true');
	
 } 
  function addMovement(methodName) {
	
	openAddWindow(methodName,'currencymaintenanceaddWindow','left=50,top=190,width=290,height=110,toolbar=0, resizable=yes, scrollbars=yes','true');
  
}
function unlockMovementOnServer(movementIds)
{
	if(archiveId=="")
	{
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementLock.do?method=unlockMovement";
		sURL = sURL + "&movementId="+movementIds;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
		}
	return "true";
	
}
/**
  * This function is used to display the Movement Display screen
  *@param methodName
  *@param movementId
  *@param entityid
  *return boolean
  *
  */

	function showMvmnt(methodName,movementId,entityid){
	//variable declared for menuAccessIdOfChildWindow
    	var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
    //variable declared for menuName
    	var menuName = new String('<fmt:message key="mvmDisplay.title.window"/>');
    //variable declared for smrtPredPos
		var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
		menuName = menuName.substr(0,smrtPredPos-3);
		if (menuAccessIdOfChildWindow == 2) {
	    	alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');  
	   
    	} else {
       
        var param = 'movement.do?method='+methodName;
        param += '&entityCode=' + entityid;
        param += '&movementId=' + movementId;
        param += '&menuAccessId=' + menuAccessIdOfChildWindow;
        
        openWindow(param,'movementWindow','left=50,top=190,width=994,height=789,toolbar=0, resizable=yes, scrollbars=yes','true');    
        
	}

	return false;
}
function checkStatus(matchIds,entityid,matchStatus,selectedList,rowcount,count,maxValue, previousMatchHash){
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkMatchStatus";
	sURL = sURL + "&matchId="+matchIds;
	sURL = sURL + "&entityId="+entityid;
	sURL = sURL + "&status="+matchStatus;
	sURL = sURL + "&movementList="+selectedList;
	sURL = sURL + "&rowCount="+rowcount;
    sURL = sURL + "&maxValue="+maxValue;
    sURL = sURL + "&previousMatchHash="+previousMatchHash;
	sURL = sURL + "&count="+count;
	
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	
   return str;

}
function lockMatchOnServer(process,matchIds,entityid)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=lockMatch";
	sURL = sURL + "&matchId="+matchIds;
	sURL = sURL + "&entityId="+entityid;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}
function addMovement(method,status,matchIds,entityid,matchStatus,selectedList,rowcount)
{
    var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkMatchStatus";

	sURL = sURL + "&matchId="+matchIds;
	sURL = sURL + "&entityId="+entityid;
	sURL = sURL + "&status="+matchStatus;
	sURL = sURL + "&movementList="+selectedList;
	sURL = sURL + "&rowCount="+rowcount;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}	
function openAddWindow(matchIds,entityid)
{
   var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=lockMatch";
	sURL = sURL + "&matchId="+matchId;
	sURL = sURL + "&entityId="+entityId;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}

 
 /**
 * This function used to check whether the match id is valid or not by sending AJAX request
 * @param matchId
 * @return matchIdExist
 */
function checkMatchId(matchId){
//create AJAX requesty
	var oXMLHTTP = new XMLHttpRequest();
	//Framing URL
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkMatchIdIfExist";
	sURL = sURL + "&matchId="+matchId;
	sURL = sURL + "&entityId="+entityId;
        // Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
        // Archive setup: Remove redundant fields from Archive setup screen
	sURL = sURL + "&archiveId="+archiveId;
	oXMLHTTP.open( "POST", sURL, false );
	//send request
	oXMLHTTP.send();
	//get response of AJAX request
	var matchIdExist=oXMLHTTP.responseText;
	return matchIdExist;
}



function checkLockForMatch(matchId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkLockForMatch";
	sURL = sURL + "&matchId="+matchId;
	sURL = sURL + "&entityId="+entityId;
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var lockUserId=oXMLHTTP.responseText;
	return lockUserId;
}
function openAddScreen(methodName,entityid,currencycode,isAmountDiffer,selectedMvmntsAmount,matchCount,matchQuality){
	var param='movementmatchdisplay.do?method='+methodName;
	param+='&entityId='+entityid;
	param+='&currencyCode='+currencycode;
	param = param + "&isAmountDiffer="+isAmountDiffer;
	param = param + "&selectedMovementsAmount="+selectedMvmntsAmount;// appending absolute value of amount and its sign also
	param = param + "&matchCount="+matchCount;
	openWindow(param,'currencymaintenanceaddWindow','left=50,top=190,width=310,height=110,toolbar=0, resizable=yes, scrollbars=yes','true');
	
}
function checkUnmatchFlag(matchId,selectedMvntList,checkFlag,entityId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementmatchdisplay.do?method=checkUnmatch";		    
			sURL = sURL +"&entityId="+entityId+ "&matchId="+matchId+"&selectedMvntList="+selectedMvntList+"&checkFlag="+checkFlag;			
			oXMLHTTP.open( "POST", sURL, false );
			oXMLHTTP.send();
			var unmatchFlag=oXMLHTTP.responseText;	
			
	return unmatchFlag;	
}
function lockMovementOnServer(movementId)
{
	if(archiveId=="")
	{
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementLock.do?method=lockMovement";
		sURL = sURL + "&movementId="+movementId;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}
	return "true";
	
}	
function checkLockOnServer(movementId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=checkLock";
	sURL = sURL + "&movementId="+movementId;

	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}
function checkLocksOnServer(movementId)
{
	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/movementLock.do?method=checkLocks";

	oXMLHTTP.open("POST", sURL, false);
	oXMLHTTP.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

	var data = "movementIds=" + movementId;
	oXMLHTTP.send(data);
	var str = oXMLHTTP.responseText;
	return str;
}

function callApp(method,movementId,notesFlag) {
	
	var event = new CustomEvent("mmsd.movementNotes", 
        {
            detail: {method:method,movementId:movementId,notesFlag:notesFlag},
            bubbles: true,
            cancelable: true
        }
    );    
    window.dispatchEvent(event);
    
// 	Main.movementNotes(method,movementId,notesFlag);
}
function accountAccessConfirm(accountId,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirm";
	sURL = sURL + "&accountId="+accountId;
	sURL = sURL + "&entityId="+entity;
	sURL = sURL + "&status=Matching";
	oXMLHTTP.open( "POST", sURL, false );
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;	
	return str;
}

		function help() {
			<c:choose>
			<c:when test="${requestScope.match != 'manual'}">
			openWindow(buildPrintURL('print', 'Movement Match Summary Display '),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no',
					'true');
			</c:when>
			<c:when test="${requestScope.match == 'manual' && requestScope.method == 'movementDisplay'}">
			openWindow(buildPrintURL('print', 'Movement Match Summary Display '),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no',
					'true');
			</c:when>
			<c:when test="${requestScope.match == 'manual' && requestScope.calledFrom == 'msd'}">
			openWindow(buildPrintURL('print', 'Movement Match Summary Display '),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no',
					'true');
			</c:when>
			<c:when test="${requestScope.match == 'manual' && requestScope.method != 'movementDisplay' && requestScope.calledFrom != 'msd'}">
			openWindow(buildPrintURL('print', 'Manual Matching '),
					'sectionprintdwindow',
					'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no',
					'true');
			</c:when>
			</c:choose>
		}

function parentFormRefresh1(){

	window.opener.document.forms[0].method.value="display";
	window.opener.document.forms[0].status.value = '${requestScope.status}';
	window.opener.document.forms[0].submit();
	self.close();
}
function parentFormMovDispRefresh(){

	 if (window.opener.document.forms[0].elements["movement.id.movementIdAsString"] != null) {
	
		  var movId =  window.opener.document.forms[0].elements["movement.id.movementIdAsString"].value;	 
			if (movId != null && movId != "") {
			window.opener.document.forms[0].selectedMovementId.value = movId;
			window.opener.document.forms[0].method.value = "displayMovement";
			window.opener.document.forms[0].parentScreen.value = parentScreen;
			window.opener.document.forms[0].buttonDiv.value = "movementDisplay";
			window.opener.document.forms[0].submit();
			self.close();
		 } 
	 }else {
	
		window.opener.document.forms[0].method.value="refreshScreen";	
		window.opener.document.forms[0].submit();
		
		window.opener.isRefresfFromChild = "true";
		window.opener.document.forms[0].method.value="displayOpenMovements";	
		window.opener.document.forms[0].selectedList.value = "";	
		window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';	
		window.opener.document.forms[0].refreshFromMatchScreen.value = 'Y';	
		window.opener.document.forms[0].tableScrollbarLeft.value = '${requestScope.tableScrollbarLeft}';
		window.opener.document.forms[0].tableScrollbarTop.value = '${requestScope.tableScrollbarTop}';
		window.opener.document.forms[0].scrollbarLeft.value = '${requestScope.scrollbarLeft}';
		window.opener.document.forms[0].scrollbarTop.value = '${requestScope.scrollbarTop}';
		window.opener.document.forms[0].submit();
		self.close();
	}
}
function QueueRefresh(){

	window.opener.setTabInfo();
		
		window.opener.document.forms[0].method.value="display";

		window.opener.document.forms[0].status.value = '${requestScope.status}';
	    
		window.opener.document.forms[0].submit();
		
	  self.close();
}
 function closeWindow(){
	self.close();
}

        function deleteLock() {

            beaconUnlock(selectedMovementForLock);
            Main.unlockMvmnt();
            closeWindow();
        }


        // For page unload cases
        function beaconUnlock(movementIds) {
            try {
                var appName = "<%=SwtUtil.appName%>";
                var requestURL = new String('<%=request.getRequestURL()%>');
                const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

                // Create the data to be sent
                const formData = new FormData();
                formData.append('movementIds', movementIds);
                formData.append('method', 'unlockMovements');

                // Send using beacon API
                const success = navigator.sendBeacon(sURL, formData);
                return success ? "success" : "error";
            } catch (error) {
                console.error('Error during beacon unlock:', error);
                return "error";
            }
        }

/**
* This function is refresh the Movement summary Display screen While closing this screen
*/
function CallBackApp(){

	if(calledFrom=="msd"){
		window.opener.CallBackApp();
	}
}

/**
  * This function is used to refresh the Movement Summary Display screen,  
  * when close the MMSD screen this method is called and refresh the MSD screen.
  * 
  * @param none.
  *
  */
function bodyunload(){	
	Main.closeHandler();
	// check calledFrom is msd, then refresh the movement summary display screen.	
	if(calledFrom=="msd"){
		window.opener.CallBackApp();
	}
}

function expandAmtDifference(amtDifference,currencyFormat,currencyCode){
	return expandCurrencyAmountFlex(amtDifference, currencyFormat, currencyCode);
}

/**
  * This function is used for calling the saveFontSize() method from ScreenOption 
  * to store the Font size of the screen data grid
  * 
  * @param fontSize - set by the user.
  *
  */
function getUpdateFontSize(fontSize) {
	return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + screenId + "&screenOption.propertyValue=" + fontSize;
}

function onFlexInitialized(){
    <!-- Force the browser to set flex app with focus -->
      // getFlashObject("mySwf").focus();
   }
</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> 
	<input type="hidden" name="mvmntid" /> 
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="mvmMatchSummDisplay.title.window" />" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>