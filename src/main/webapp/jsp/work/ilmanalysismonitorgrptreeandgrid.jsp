<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.work.model.InputExceptionsDataModel"%>
<%@ page import="org.swallow.util.OpTimer"%>
<%@ page import="java.text.NumberFormat"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<intradayliquidity
		timeframe="${timeframe}"
		currencyDecimalPlaces="${currencyDecimalPlaces}"
		currencyMutiplierValue="${currencyMutiplierValue}"
		lastRefTime="${requestScope.lastRefTime}"
		selectedDateTimeFame="${requestScope.selectedDateTimeFame}"
		sysDateWithCcyTimeFrame="${requestScope.sysDateWithCcyTimeFrame}"
		profileTreeData="${profileTreeData}"
		profileGroupGrid="${profileGroupGrid}"
		profileScenarioGrid="${profileScenarioGrid}"
		includeOpenMvnts="${includeOpenMvnts}" sumByCutOff="${sumByCutOff}">
		sumByCutOff="${sumByCutOff}">

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<group>
		<grid>
			<metadata>
				<columns>
					<c:forEach items="${requestScope.grpColOrder}" var="order">
						<c:choose>
							<c:when test="${order == 'use'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.use'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.use.tooltip'/>"
										dataelement="use"
										width="${requestScope.grpColWidth.use}"
										draggable="false"
										filterable="false"
										sort="true"
										type="checkbox"
										editable="true"/>
							</c:when>
							<c:when test="${order == 'group'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.group'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.group.tooltip'/>"
										dataelement="group"
										width="${requestScope.grpColWidth.group}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'thresholds'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.thresholds'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.thresholds.tooltip'/>"
										dataelement="thresholds"
										width="${requestScope.grpColWidth.thresholds}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'type'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.type'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.type.tooltip'/>"
										dataelement="type"
										width="${requestScope.grpColWidth.type}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'creator'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.creator'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.creator.tooltip'/>"
										dataelement="creator"
										width="${requestScope.grpColWidth.creator}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
						</c:choose>
					</c:forEach>
				</columns>
			</metadata>

			<rows size="${grpRowSize}">
				<c:forEach items="${requestScope.grpsDetaillist}" var="grpsDetaillist">
					<row>
						<use global="${grpsDetaillist.global}">
							<c:choose>
								<c:when test="${grpsDetaillist.global == 'Y'}">true</c:when>
								<c:otherwise>false</c:otherwise>
							</c:choose>
						</use>
						<group editable="false">
								${grpsDetaillist.id.ilmGroupId} : ${grpsDetaillist.ilmGroupName}
						</group>
						<group_name editable="false">
								${grpsDetaillist.ilmGroupName}
						</group_name>
						<group_id editable="false">
								${grpsDetaillist.id.ilmGroupId}
						</group_id>
						<default_legend_text editable="false">
								${grpsDetaillist.defaultLegendText}
						</default_legend_text>
						<extsod editable="false">
								${grpsDetaillist.externalSOD}
						</extsod>
						<exteod editable="false">
								${grpsDetaillist.externalEOD}
						</exteod>
						<fcastsod editable="false">
								${grpsDetaillist.forecastSOD}
						</fcastsod>
						<fcasteod editable="false">
								${grpsDetaillist.forecastEOD}
						</fcasteod>
						<thresholds editable="false">
							Min1:${grpsDetaillist.thresholdMin1}; Min2:${grpsDetaillist.thresholdMin2}; Max1:${grpsDetaillist.thresholdMax1}; Max2:${grpsDetaillist.thresholdMax2}
						</thresholds>
						<type editable="false">
								${grpsDetaillist.publicPrivate}
						</type>
						<creator editable="false">
								${grpsDetaillist.createdByUser}
						</creator>
					</row>
				</c:forEach>
			</rows>
		</grid>
	</group>

	<scenario>
		<grid>
			<metadata>
				<columns>
					<c:forEach items="${requestScope.scenColOrder}" var="order">
						<c:choose>
							<c:when test="${order == 'use'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.use'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.use.tooltip'/>"
										dataelement="use"
										width="${requestScope.scenColWidth.use}"
										draggable="false"
										filterable="false"
										sort="true"
										type="checkbox"
										editable="true"/>
							</c:when>
							<c:when test="${order == 'scenario'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.scenario'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.scenario.tooltip'/>"
										dataelement="scenario"
										width="${requestScope.scenColWidth.scenario}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'name'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.name'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.name.tooltip'/>"
										dataelement="name"
										width="${requestScope.scenColWidth.name}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'type'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.type'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.type.tooltip'/>"
										dataelement="type"
										width="${requestScope.scenColWidth.type}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'creator'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.creator'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.creator.tooltip'/>"
										dataelement="creator"
										width="${requestScope.scenColWidth.creator}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
						</c:choose>
					</c:forEach>
				</columns>
			</metadata>

			<rows size="${scnRowSize}">
				<c:forEach items="${requestScope.scenariosDetailList}" var="scenariosDetailList">
					<row>
						<use editable="false">
							<c:choose>
								<c:when test="${scenariosDetailList.id.ilmScenarioId == 'Standard'}">true</c:when>
								<c:otherwise>false</c:otherwise>
							</c:choose>
						</use>
						<scenario editable="false">
								${scenariosDetailList.id.ilmScenarioId}
						</scenario>
						<name editable="false">
								${scenariosDetailList.ilmScenarioName}
						</name>
						<type editable="false">
								${scenariosDetailList.publicPrivate}
						</type>
						<creator editable="false">
								${scenariosDetailList.createdByUser}
						</creator>
					</row>
				</c:forEach>
			</rows>
		</grid>
	</scenario>

	<balance>
		<grid>
			<metadata>
				<columns>
					<c:forEach items="${requestScope.balColOrder}" var="order">
						<c:choose>
							<c:when test="${order == 'use'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.use'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.use.tooltip'/>"
										dataelement="use"
										width="${requestScope.balColWidth.use}"
										draggable="false"
										filterable="false"
										sort="true"
										type="checkbox"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'group'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.group'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.group.tooltip'/>"
										dataelement="group"
										width="${requestScope.balColWidth.group}"
										draggable="true"
										filterable="false"
										sort="true"
										type="str"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'extsod'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.extsod'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.extsod.tooltip'/>"
										dataelement="extsod"
										width="${requestScope.balColWidth.extsod}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'exteod'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.exteod'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.exteod.tooltip'/>"
										dataelement="exteod"
										width="${requestScope.balColWidth.exteod}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'fcastsod'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.fcastsod'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.fcastsod.tooltip'/>"
										dataelement="fcastsod"
										width="${requestScope.balColWidth.fcastsod}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'fcasteod'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.fcasteod'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.fcasteod.tooltip'/>"
										dataelement="fcasteod"
										width="${requestScope.balColWidth.fcasteod}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'openunexp'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.openunexp'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.openunexp.tooltip'/>"
										dataelement="openunexp"
										width="${requestScope.balColWidth.openunexp}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'openunsett'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.openunsett'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.openunsett.tooltip'/>"
										dataelement="openunsett"
										width="${requestScope.balColWidth.openunsett}"
										draggable="true"
										filterable="false"
										sort="true"
										type="num"
										editable="false"/>
							</c:when>
							<c:when test="${order == 'lastupdate'}">
								<column heading="<fmt:message key='ilmanalysismonitor.grid.lastupdate'/>"
										tooltip="<fmt:message key='ilmanalysismonitor.grid.lastupdate.tooltip'/>"
										dataelement="lastupdate"
										width="${requestScope.balColWidth.lastupdate}"
										draggable="true"
										filterable="false"
										sort="true"
										type="date"
										editable="false"/>
							</c:when>
						</c:choose>
					</c:forEach>
				</columns>
			</metadata>

			<rows size="${grpRowSize}">
				<c:forEach items="${requestScope.grpsDetaillistForBalance}" var="grpsDetaillistForBalance">
					<row>
						<use global="${grpsDetaillistForBalance.global}">
							<c:choose>
								<c:when test="${grpsDetaillistForBalance.global == 'Y'}">true</c:when>
								<c:otherwise>false</c:otherwise>
							</c:choose>
						</use>
						<group editable="false">
								${grpsDetaillistForBalance.id.ilmGroupId} : ${grpsDetaillistForBalance.ilmGroupName}
						</group>
						<group_id editable="false">
								${grpsDetaillistForBalance.id.ilmGroupId}
						</group_id>
						<extsod editable="false">${grpsDetaillistForBalance.externalSOD}</extsod>
						<exteod editable="false">${grpsDetaillistForBalance.externalEOD}</exteod>
						<fcastsod editable="false">${grpsDetaillistForBalance.forecastSOD}</fcastsod>
						<fcasteod editable="false">${grpsDetaillistForBalance.forecastEOD}</fcasteod>
						<openunexp editable="false">${grpsDetaillistForBalance.openUnexp}</openunexp>
						<openunsett editable="false">${grpsDetaillistForBalance.openUnsett}</openunsett>
						<lastupdate editable="false">${grpsDetaillistForBalance.lastUpdated}</lastupdate>
					</row>
				</c:forEach>
			</rows>
		</grid>
	</balance>

	<tree label="">
		<c:forEach items="${requestScope.treeGrpsDetail}" var="group">
			<node type="group"
				  label="${group.id.ilmGroupId}"
				  tooltip="${group.ilmGroupName}"
				  id="${group.id.ilmGroupId}.grp"
				  isBranch="true"
				  visible="${group.global == 'Y' ? 'true' : 'false'}">
				<node type="scenario"
					  label="<fmt:message key='ilmanalysismonitor.tree.thresholds'/>"
					  tooltip="<fmt:message key='ilmanalysismonitor.tree.thresholds'/>"
					  isBranch="false"
					  selected="${group.global == 'Y' ? 'true' : 'false'}"
					  visible="true"
					  yField="${group.id.ilmGroupId}.Thresholds"/>
				<c:forEach items="${requestScope.treeScenariosDetail}" var="scenario">
					<node type="scenario"
						  label="${scenario.id.ilmScenarioId}"
						  tooltip="${scenario.ilmScenarioName}"
						  id="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.scen"
						  isBranch="true"
						  selected="${group.global == 'Y' ? 'true' : 'false'}"
						  visible="${scenario.id.ilmScenarioId == 'Standard' ? 'true' : 'false'}">
						<node label="<fmt:message key='ilmanalysismonitor.tree.actBalance'/>"
							  selected="${scenario.id.ilmScenarioId == 'Standard' && group.global == 'Y' ? 'true' : 'false'}"
							  isBranch="false"
							  yField="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.ab"/>
						<node label="<fmt:message key='ilmanalysismonitor.tree.forebasic'/>"
							  selected="${scenario.id.ilmScenarioId == 'Standard' && group.global == 'Y' ? 'true' : 'false'}"
							  isBranch="false"
							  yField="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.fbb"/>
						<node label="<fmt:message key='ilmanalysismonitor.tree.forecIncludeAct'/>"
							  selected="${scenario.id.ilmScenarioId == 'Standard' && group.global == 'Y' ? 'true' : 'false'}"
							  isBranch="false"
							  yField="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.fbia"/>
						<node label="<fmt:message key='ilmanalysismonitor.tree.accTotals'/>"
							  id="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.tot"
							  visible="true"
							  isBranch="true">
							<node label="<fmt:message key='ilmanalysismonitor.tree.accActualCD'/>"
								  selected="${scenario.id.ilmScenarioId == 'Standard' && group.global == 'Y' ? 'true' : 'false'}"
								  isBranch="false"
								  yField="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.aac,${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.aad"/>
							<node label="<fmt:message key='ilmanalysismonitor.tree.accForeCD'/>"
								  selected="${scenario.id.ilmScenarioId == 'Standard' && group.global == 'Y' ? 'true' : 'false'}"
								  isBranch="false"
								  yField="${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.afc,${group.id.ilmGroupId}.${scenario.id.ilmScenarioId}.afd"/>
						</node>
					</node>
				</c:forEach>
			</node>
		</c:forEach>
	</tree>

</intradayliquidity>

