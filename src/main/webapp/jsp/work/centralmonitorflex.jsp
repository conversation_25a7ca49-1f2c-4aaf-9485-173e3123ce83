<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtUtil"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>

<title><fmt:message key="centralMonitor.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<!-- Start: code commented by <PERSON><PERSON><PERSON> due to changes inside the body-tag 06-04-2010 -->
<!--  <script type="text/javascript" src="jsp/work/assets/swfobject.js"></script> -->
<!-- End: code commented by <PERSON><PERSON><PERSON> G due to changes inside the body-tag 06-04-2010 -->
<script type="text/javascript">
			var timeStamp =null;
			var screenRoute = "centralBankMonitor";
			/**
			 * This method is called when window is onloaded.
			 */
			window.onload = function () {
				//Aliveni: Calling setTitleSuffix() function in CommonJS.js file to distinguish multiple
				//Implementations (Mantis 857), on 27-AUG-2010
				setTitleSuffix(document.forms[0]);
				<%
				    SimpleDateFormat fmFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
					String timeStr = fmFormat.format(new Date());
				%>
				timeStamp ="<%=timeStr %>";
				setParentChildsFocus();
				// Start: code commented by Kalidass G due to changes inside the body-tag on 06-04-2010
				// var so = new SWFObject ("jsp/work/centralbankmonitor.swf", "centralbankMonitor", "100%", "100%", "9", "#D6E3FE");
				// so.write("swf");
				// End: code commented by Kalidass G due to changes inside the body-tag on 06-04-2010
			};

			window.onunload = call;

			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();

			label["text"]["entity"] = "<fmt:message key="entity.id"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityId"/>";

			label["text"]["currency"] = "<fmt:message key="currency.group"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.selectCuurencyGrp"/>";

			label["text"]["currencylimit"] = "<fmt:message key="centralMonitor.currencylimit"/>";
			label["tip"]["currencylimit"] = "<fmt:message key="tooltip.currencyLimit"/>";

			label["text"]["datefrom"] = "<fmt:message key="centralMonitor.fromDate"/>";
			label["tip"]["datefrom"] = "<fmt:message key="tooltip.selectFromDate"/>";

			label["tip"]["showdays"] = "<fmt:message key="tooltip.showdays"/>";
			label["text"]["showdays"] = "<fmt:message key="text.showdays"/>";
			label["text"]["day"] = "<fmt:message key="text.day"/>";
			label["text"]["days"] = "<fmt:message key="text.days"/>";

			label["text"]["breakdown"] = "<fmt:message key="currMonitor.breakdown"/>";
			/* Start:Code modified by Kalidass to display the Account Breakdown label && tooltip option
			 instead of Account Monitor on 06-04-2010 */
			label["text"]["acctbrkdown"] = "<fmt:message key="acountMonitorNew.AccountBreakDownScreen"/>";
			label["tip"]["acctbrkdown"] = "<fmt:message key="tooltip.AcctBreakDown"/>";
			/* End:Code modified by Kalidass to display the Account Breakdown label && tooltip option
			 instead of Account Monitor on 06-04-2010 */
			label["text"]["mvmntbrkdown"] = "<fmt:message key="manualInput.id.movementId"/>";
			label["tip"]["mvmntbrkdown"] = "<fmt:message key="tooltip.MvmntBrkdown"/>";


			label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

			label["text"]["button-rate"] = "<fmt:message key="accountmonitorbutton.Rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.rateButton"/>";

			label["text"]["button-options"] = "<fmt:message key="button.option"/>";
			label["tip"]["button-options"] = "<fmt:message key="tooltip.option"/>";

			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

			label["tip"]["datefromDDMMYY"] = "<fmt:message key="tooltip.fromDateDDMMYY"/>"
			label["tip"]["datetoDDMMYY"] = "<fmt:message key="tooltip.toDateDDMMYY"/>"

			label["tip"]["datefromMMDDYY"] = "<fmt:message key="tooltip.fromDateMMDDYY"/>"
			label["tip"]["datetoMMDDYY"] = "<fmt:message key="tooltip.toDateMMDDYY"/>"
			/*Start :Added by Imed B on 20-02-2014*/
			label["text"]["label-centralBankMonitor"] = "<fmt:message key="screenTitle.centralBankMonitor"/>";
			label["text"]["label-showXML"] = "<fmt:message key="screen.showXML"/>";
			label["text"]["label-showValue"] = "<fmt:message key="centralBankMonitor.showValue"/>";
			label["text"]["alert-error"] = "<fmt:message key="screen.error"/>";
			label["text"]["label-whatIfAnalysis"] = "<fmt:message key="centralBankMonitor.whatIfAnalysis"/>";
			label["text"]["label-validNumber"] = "<fmt:message key="centralBankMonitor.validNumber"/>";
			label["text"]["label-refreshRateSelected"] = "<fmt:message key="centralBankMonitor.refreshRateSelected"/>";
			label["text"]["alert-warning"] = "<fmt:message key="screen.warning"/>";
			label["text"]["label-entity"] = "<fmt:message key="centralBankMonitor.entity"/>";
			label["text"]["label-currencyLimit"] = "<fmt:message key="centralBankMonitor.currencyLimit"/>";
			label["text"]["label-currencyCode"] = "<fmt:message key="centralBankMonitor.currencyCode"/>";
			label["text"]["label-currencyMultiplier"] = "<fmt:message key="centralBankMonitor.currencyMultiplier"/>";
			label["text"]["label-fromDate"] = "<fmt:message key="centralBankMonitor.fromDate"/>";
			label["text"]["label-toDate"] = "<fmt:message key="centralBankMonitor.toDate"/>";
			label["text"]["label-amountGreaterThanZero"] = "<fmt:message key="centralBankMonitor.amountGreaterThanZero"/>";
			label["text"]["label-validAmount"] = "<fmt:message key="centralBankMonitor.validAmount"/>";
			label["text"]["label-fromToOutsideRange"] = "<fmt:message key="centralBankMonitor.fromToOutsideRange"/>";
			label["text"]["label-fromOutsideRange"] = "<fmt:message key="centralBankMonitor.fromOutsideRange"/>";
			label["text"]["label-ToOutsideRange"] = "<fmt:message key="centralBankMonitor.ToOutsideRange"/>";
			label["text"]["label-buildInProgress"] = "<fmt:message key="screen.buildInProgress"/>";
			label["text"]["label-connectionError"] = "<fmt:message key="screen.connectionError"/>";
			label["text"]["label-lastRefresh"] = "<fmt:message key="screen.lastRefresh"/>";
			label["text"]["alert-showValue"] = "<fmt:message key="alert.showValue"/>";
			/*End :Added by Imed B on 20-02-2014*/

			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';

			var refreshPending = false;

			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;
			var testDate= "<%=SwtUtil.getSystemDateString() %>";
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';
			 // get the font size from request
			var currentFontSize = "<%=request.getAttribute("fontSize")%>";
			 // Start:added by Mefteh for Mantis 2016
			<% List<Integer> listPriorAheadDays=SwtUtil.getPriorAndAheadDaysToToday();%>
            var nDaysPriorToToday =<%=listPriorAheadDays.get(0)%>;
			var nDaysAheadToToday =<%=listPriorAheadDays.get(1)%>;
		     // End: added by Mefteh for Mantis 2016


			/**
             * formatCurrency
             *
 			 * This method is used to format the amount in system currency format (called by flex)
			 */
			function formatCurrency(amount){
				var strAmount=formatCurrency_centralMonitor(amount, currencyFormat, null);
				return strAmount;
			}

			/**
             * bodyUnload
             *
 			 * This method is called when window is unloaded.
			 */
			function bodyUnload()
			{
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL+ appName+"/centralBankMonitor.do?method=deleteCentralMonitorDetails";
				sURL = sURL + "&currTimeStamp="+timeStamp;
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
			}
			// Start:Code modified by Kalidass for Mantis 1181 on 23-July-2010
			/**
             * saveMonitorDetails
             *
 			 * This method is used to save the montior details
			 */
			function saveMonitorDetails(valueDate,valueBeforeEdit, valueAfterEdit,entityId,isCheckNeeded)
			{
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL+ appName+"/centralBankMonitor.do?method=saveCentralMonitorDetails";
				sURL = sURL +"&valueDate="+valueDate+"&valueBeforeEdit="+valueBeforeEdit + "&amount="+valueAfterEdit+"&entityId="+entityId+"&isCheckNeeded="+isCheckNeeded;
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var result=oXMLHTTP.responseText;
				if(result == "true"){
					refreshPending = true;
				}
				return result;
				// End:Code modified by Kalidass for Mantis 1181 on 23-July-2010
			}

			/**
             * clickLink
             *
 			 * This method is used to open the link for account monitor || movement summary display
			 */
			 // Start:Code modified by Kalidass for Mantis 1181 on 23-July-2010
			function clickLink (sEntityId, sEntityName, sCurrencyCode, sAccountId,sBalanceName,sColumnDate, sActionCode) {
			// End:Code modified by Kalidass for Mantis 1181 on 23-July-2010
				var menuAccessIdChild = 0;

				var paramString="";
				paramString = paramString + "&entityId=" + sEntityId;
				paramString = paramString + "&currencyCode=" + sCurrencyCode;
				paramString = paramString + "&valueDate=" + sColumnDate;
				paramString = paramString + "&accountId=" + sAccountId;
				paramString = paramString + "&balanceName=" + sBalanceName;

				var requestURL = new String ('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf ('/'+appName+'/');
				var arrtributString;

				requestURL=requestURL.substring (0, idy+1) ;
				// Start:Code modified by Kalidass for Mantis 1181 on 23-July-2010
				if(sBalanceName == "Corporate Entries"){
					var requestURL = 'corporateAccount.do?method=flexCorporateAccount';
					arrtributString="left=50,top=190,width=650,height=413,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
					requestURL = requestURL + "&selectedDate=" + sColumnDate;
					requestURL += "&selectedEntityId=" + sEntityId;
					requestURL += "&selectedEntityName=" + sEntityName;
				}
				else if (sActionCode == "A") {
				// End:Code modified by Kalidass for Mantis 1181 on 23-July-2010
					/* Start:Code added/commented by Kalidass to open the Account Breakdown Monitor Screen while clicking
					 the link when Account option is checked on 06-04-2010 */
					if(sBalanceName == "Predicted Balance"){
						sBalanceName = "Predicted";
					}else if(sBalanceName == "External Balance"){
						sBalanceName = "External";
					}
					var menuAccessIdChild = getMenuAccessIdOfChildWindow("acctbreakdownmonitor.do");

					var requestURL = "acctbreakdownmonitor.do";
					requestURL += "?method=display";
					requestURL += "&entityId=" + sEntityId;
					requestURL += "&accountId=" + sAccountId;
					requestURL += "&selectedValueDate=" + sColumnDate;
					requestURL += "&selectedBalanceType=" + sBalanceName;
					requestURL += "&currencyCode=" + sCurrencyCode;
					requestURL += "&fromMenu=N";
					requestURL += "&menuAccessId=" + menuAccessIdChild;
					requestURL += "&applyCurrencyThreshold=N";
					requestURL += "&hideZeroBalances=N";
					//Added for mantis 1094 by selvakumar on 31.May.2010
					requestURL += "&callstatus=true";

					var menuName = new String('<fmt:message key="accountmonitorBrkDown.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert ('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					} else {
						openWindow (requestURL,'accountBreakDownMonitorJsp','left=50,top=190,width=1200,height=700,toolbar=0, resizable=yes, scrollbars=yes,status=yes');
					}

					window.event.returnValue = false;
					window.event.cancelBubble = true;
				/*	menuAccessIdChild = getMenuAccessIdOfChildWindow("acctmonitornew.do");
					var menuName = new String('<fmt:message key="accountmonitor.title.window"/>');
					var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
					menuName = menuName.substr(0,smrtPredPos-3);
					if (menuAccessIdChild == 2) {
						alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
					}
					requestURL = requestURL +appName +"/acctmonitornew.do?method=callFromCurrencyMonitor&menuAccessId="+menuAccessIdChild;
					arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes"; */
				 /* End:Code added/commented by Kalidass to open the Account Breakdown Monitor Screen while clicking
					 the link when Account option is checked on 06-04-2010 */

				} else if (sActionCode == "M") {

					requestURL = requestURL +appName +"/centralBankMonitor.do?method=countMovements&breakdown=All";
					requestURL += paramString;

					var noOfMovements = sendRequest (requestURL);

					if(noOfMovements == 0) {
						alert('<fmt:message key="monitor.nomovements"/>');
						return false;
					} else {

						var requestURL = 'outstandingmovement.do?method=flexCurrency';
						arrtributString="left=50,top=190,width=1280,height=685,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
						requestURL = requestURL + "&initialinputscreen=" + "currencymonitor";
					}
				}else{
					//do nothing
				}

				requestURL += "&entityId=" + sEntityId;
				requestURL += "&currencyCode=" + sCurrencyCode;
				requestURL += "&valueDate=" + sColumnDate;
				requestURL += "&callfromcurrency=y";


				if (menuAccessIdChild != 2) {
					openWindow (requestURL, 'bookMonitorJsp', arrtributString);
				}
				if(window.event) {
				window.event.returnValue = false;
				window.event.cancelBubble = true;
				}
				return false;
			}

			/**
             * sendRequest
             *
 			 * This method is used to open the link for account monitor || movement summary display
			 */
			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();

				var count = new Number (oXHR.responseText);
				return count;
			}

			/**
             * openOptionsWindow
             *
 			 * This method is used to open the monitor options window
			 */
			function openOptionsWindow (entityId) {
				var param = '/' + appName + '/centralBankMonitor.do?method=displayCentralMonitorOptions&entityId='+entityId;
				openWindow (param, 'centralMonitorOptions', 'width=550,height=270,toolbar=0, resizable=yes, scrollbars=no', 'true');
			}

			/**
             * help
             *
 			 * This method is used to open the help window for central bank monitor
			 */
			function help(){
					openWindow(buildPrintURL('print','Central Bank Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

			/**
			 * getUpdateRefreshRequest
			 *
			 * flash uses this to determine what to request to save a new refresh rate
			 */
			function getUpdateRefreshRequest (rate) {
				/* Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 16-Aug-2010 */
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + rate;
			}

			//START: code added by Kalidass G to start auto-refreshing in corresponding flash file while pressing the "cancel" button for auto-refreshment
			function callApp(refreshStart){
				Main.startAutoRefresh(refreshStart);
				//getFlashObject("mySwf").AutoRefreshStart(refreshStart);
			}
			//END  : code added by Kalidass G to start auto-refreshing in corresponding flash file while pressing the "cancel" button for auto-refreshment
			//Start:Code added by Kalidass for Mantis 1181 on 23-July-2010
			function CallBackApp(){
				Main.refreshParent()
				//getFlashObject("mySwf").RefreshParent();
			}
			//End:Code added by Kalidass for Mantis 1181 on 23-July-2010

				/**
			* This function is used to send the request to save the font size set by the user
			* && sets it in the database.
			*
			* @param fontSize - set by the user.
			*
			*/
			function getUpdateFontSize(fontSize) {
				return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + fontSize;
			}

		</script>
<%@ include file="/angularscripts.jsp"%>
<!-- Start:Code modified by Kalidass for Mantis 1181 0n 23-July-2010 -->
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"	>
<form id="exportDataForm" target="tmp" method="post">

<input
	type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<fmt:message key="centralMonitor.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
<form name="test1" target="tmp1" method="post"><input
	type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" value= /></form>
<iframe name="tmp1" width="0%" height="0%" src="#" />
</body>
</html>
