<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ page import="java.util.*" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>



<c:set var="recordCount" value="${requestScope.centralMonitor.monitorRecords.size()}" />


<centralbankmonitor
        databuilding="${requestScope.jobFlagStatus != null ? 'true' : 'false'}"
        refresh="${requestScope.autoRefreshRate}"
        to="${requestScope.centralMonitor.toDateAsString}"
        from="${requestScope.centralMonitor.fromDateAsString}"
        currencylimit="${requestScope.centralMonitor.currencyLimit}"
        multiplier="${requestScope.multiplier}"
        entityfromdate="${requestScope.entityFromDate}"
        entitytodate="${requestScope.entityToDate}"
        currencycode="${requestScope.currencyCode}"
        accountid="${requestScope.accountId}"
        lastRefTime="${requestScope.lastRefTime}"
        defaultDays="${requestScope.defaultDays}"
        currfontsize="${requestScope.fontSize}"
>

    <request_reply>
        <status_ok>${requestScope.reply_status_ok}</status_ok>
        <message>${requestScope.reply_message}</message>
        <location />
    </request_reply>

    <timing>
        <c:forEach items="${requestScope.opTimes}" var="opTime">
            <operation id="${opTime.key}">${opTime.value}</operation>
        </c:forEach>
    </timing>

    <grid>
        <metadata>
            <columns>
                <column heading="<fmt:message key='centralMonitor.closingBalances'/>"
                        draggable="false"
                        filterable="false"
                        type="str"
                        dataelement="balance"
                        editable="false"
                        width="${requestScope.column_width.balance}" />

                <c:if test="${recordCount > 0}">
                    <c:forEach items="${requestScope.centralMonitor.monitorRecords[0].centralMonitor}" var="predictRecord" varStatus="status">
                        <column
                                heading="${predictRecord.balanceDateAsString}"
                                draggable="false"
                                filterable="false"
                                holiday="${predictRecord.holiday != null && predictRecord.holiday != 'N' ? 'true' : 'false'}"
                                type="num"
                                maxChars="100"
                                dataelement="predictdate${status.index + 1}"
                                editable="true"
                                date="${predictRecord.balanceOrgDateAsString}"
                                width="${requestScope.column_width['predictdate'.concat(status.count+ 1)]}"
                                 />
                    </c:forEach>
                </c:if>
            </columns>
        </metadata>

        <rows size="${recordCount}">
            <c:forEach items="${requestScope.centralMonitor.monitorRecords}" var="record" varStatus="rowStatus">
                <row>
                    <balance negative="${record.negativeBal}">${record.balance}</balance>
                    <c:forEach items="${record.centralMonitor}" var="predictRecord" varStatus="innerLoopStatus">
                        <predictdate${innerLoopStatus.index}
                                date="${predictRecord.balanceOrgDateAsString}"
                                holiday="${predictRecord.holiday != null && predictRecord.holiday != 'N' ? 'true' : 'false'}"
                                negative="${predictRecord.predictedBalNegative}"
                                clickable="${record.clickableFlag}"
                            ${record.editableFlag ? 'editable="true"' : ''}>${predictRecord.predictedBalAsString}</predictdate${innerLoopStatus.index}>
                    </c:forEach>
                </row>
                <c:if test="${rowStatus.index == 3 || rowStatus.index == 6 || rowStatus.index == 8 || rowStatus.index == 9 || rowStatus.index == 12}">
                    <row />
                </c:if>
            </c:forEach>
        </rows>
    </grid>

    <selects>
        <select id="entity">
            <c:forEach items="${requestScope.entities}" var="entity">
                <option
                        value="${entity.value}"
                    ${requestScope.centralMonitor.entityId == entity.value ? 'selected="1"' : 'selected="0"'}>
                        ${entity.label}
                </option>
            </c:forEach>
        </select>
    </selects>
</centralbankmonitor>