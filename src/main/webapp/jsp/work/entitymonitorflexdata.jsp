<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<entitymonitor
		databuilding="${not empty jobFlagStatus ? 'true' : 'false'}"
		refresh="${autoRefreshRate}"
		currentdate="${requestScope.entityMonitorForm.currentDateAsString}"
		dateComparing="${dateComparing eq 'Y' ? 'true' : 'false'}"
		lastRefTime="${lastRefTime}"
		sysDate="${sysDate}"
		currfontsize="${fontSize}"
>
	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>

	<timing>
		<c:forEach items="${opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>

	<grid>
		<metadata>
			<columns>
				<column
						heading=""
						draggable="false"
						filterable="false"
						type="str"
						dataelement="alerting"
						width="10"
						sort="true"
						clickable="true"
						resizable="false"
				/>
				<column
						heading="<fmt:message key="sweep.currencyCode"/>"
						draggable="false"
						filterable="false"
						type="str"
						dataelement="ccy"
						width="90"
						sort="true"
						clickable="true"
						resizable="false"
				/>

				<c:if test="${not empty requestScope.entityMonitorForm.entityRecords}">
					<c:set var="record" value="${requestScope.entityMonitorForm.entityRecords[0].currencyRecords[0]}" />
					<c:set var="recordCount" value="${requestScope.entityMonitorForm.entityRecords.size()}" />
					<% int j=0;int k=0; %>
					<c:forEach items="${record.entityRecords}" var="entityrecord" varStatus="status">
						<c:choose>
							<c:when test="${status.index % 2 == 0}">
								<group heading="${entityrecord.entityId}" collapsable="true" headerTooltip="tooltip">
							</c:when>
							<c:otherwise>
								<group heading="${entityrecord.entityId}" collapsable="true" headerColor="true">
							</c:otherwise>
						</c:choose>

						<c:forEach items="${entityrecord.dateRecords}" var="daterecord" varStatus="dateStatus">
							<% j++; %>
							<column
									heading="${daterecord.balanceDateAsString}"
									draggable="false"
									filterable="false"
									type="num"
									dataelement="predictdate<%=j%>"
									width="${column_width}"
									headerTooltip="${daterecord.tooltip}"
									clickable="${daterecord.clickable}"
									holiday="${daterecord.headerFlag != 'N' ? 'true' : 'false'}"
									<c:if test="${status.index % 2 != 0}">
										headerColor="true"
									</c:if>
							/>
						</c:forEach>
						</group>
					</c:forEach>
				</c:if>
				<column width="1"></column>
			</columns>
		</metadata>

		<metadataTotal>
			<columns>
				<column
						heading=""
						draggable="false"
						filterable="false"
						type="str"
						dataelement="alerting"
						width="10"
						sort="true"
						clickable="true"
						resizable="false"
				/>
				<column
						heading="<fmt:message key="sweep.currencyCode"/>"
						draggable="false"
						filterable="false"
						type="str"
						dataelement="ccy"
						width="90"
						sort="true"
						clickable="false"
						resizable="false"
				/>

				<c:if test="${not empty requestScope.entityMonitorForm.entityRecords}">
					<c:set var="record" value="${requestScope.entityMonitorForm.entityRecords[0].currencyRecords[0]}" />
					<% int j=0;int k=0; %>
					<c:forEach items="${record.entityRecords}" var="entityrecord" varStatus="status">
						<c:choose>
							<c:when test="${status.index % 2 == 0}">
								<group heading="${entityrecord.entityId}" collapsable="true" headerTooltip="tooltip">
							</c:when>
							<c:otherwise>
								<group heading="${entityrecord.entityId}" collapsable="true" headerColor="true">
							</c:otherwise>
						</c:choose>

						<c:forEach items="${entityrecord.dateRecords}" var="daterecord" varStatus="dateStatus">
							<% j++; %>
							<column
									heading="${daterecord.balanceDateAsString}"
									draggable="false"
									filterable="false"
									type="num"
									dataelement="predictdate<%=j%>"
									width="${column_width}"
									headerTooltip="${daterecord.tooltip}"
									clickable="false"
									holiday="${daterecord.headerFlag == 'N' ? 'true' : 'false'}"
									<c:if test="${status.index % 2 != 0}">
										headerColor="true"
									</c:if>
							/>
						</c:forEach>
						</group>
					</c:forEach>
				</c:if>
				<column width="1"></column>
			</columns>
		</metadataTotal>
		<rows size="${recordCount}">
			<c:forEach items="${requestScope.entityMonitorForm.entityRecords}" var="entityrecord">
				<row>
					<ccy clickable="false">${entityrecord.currCode}</ccy>
					<alerting clickable="false">${entityrecord.scenarioHighlighted}</alerting>
					<% int j=0; %>
					<c:forEach items="${entityrecord.currencyRecords}" var="currencyRe">
						<c:forEach items="${currencyRe.entityRecords}" var="entityRe">
							<c:forEach items="${entityRe.dateRecords}" var="dateRe" varStatus="dateStatus">
								<% j++; %>
								<c:set var="dateNum" value="${dateStatus.count}" />
								<predictdate<%=j%>
										date="${dateRe.balanceDate}"
										atef="${dateRe.holidayFlag}"
										negative="${dateRe.predBalanceNegative ? 'true' : 'false'}"
										holiday="${dateRe.holidayFlag != 'N' ? 'true' : 'false'}"
								>${dateRe.balanceAsString}</predictdate<%=j%>>
							</c:forEach>
						</c:forEach>
					</c:forEach>
				</row>
			</c:forEach>
		</rows>

		<totals>
			<total>
				<ccy isTotalHeader="true" clickable="false">
					<c:if test="${viewTotal eq 'Y'}"><fmt:message key="positionlevel.total"/> </c:if>${reportingCurrency}
				</ccy>

				<c:forEach items="${requestScope.entityMonitorForm.totals}" var="totalRecord" varStatus="totalStatus">
					<predictdate${totalStatus.count}
							clickable="false"
							negative="${totalRecord.predBalanceNegative eq 'true' ? 'true' : 'false'}"
					>${totalRecord.predBalanceAsString}</predictdate${totalStatus.count}>
				</c:forEach>
			</total>
		</totals>
	</grid>

	<selects>
		<select id="currencygroup">
			<c:forEach items="${currencyGroupList}" var="group">
				<option
						value="${group.label}"
						selected="${requestScope.entityMonitorForm.currGrp eq group.label ? '1' : '0'}"
				>${group.value}</option>
			</c:forEach>
		</select>
	</selects>
</entitymonitor>