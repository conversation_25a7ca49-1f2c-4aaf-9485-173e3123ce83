public function fixPosition():void{
			/*offer_btn.label ="3";
			offerNoInc_btn.label ="41,33,784";
			suspend_btn.label ="9";
			suspendNoInc_btn.label ="67,53,784";
			confrmd_btn.label ="9";
			confrmdNoInc_btn.label ="86,54,783";*/
		
		///////////////  
			slash1_txt.text = "/";
			slash2_txt.text = "/";
			slash3_txt.text = "/";
			slash1_txt.y = 25;
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==1){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 212;
				slash1_txt.x = 198;
				offer_btn.x = 191;	
				offer_btn.width = 12;			
			}
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==2){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 181;		
				offer_btn.width = 22;
			}
			
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==3){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 170;		
				offer_btn.width = 32;
			}
			
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==5){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 160;		
				offer_btn.width = 42;
			}
			
			
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==6){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 152;		
				offer_btn.width = 50;
			}
			
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==8){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 142;		
				offer_btn.width = 63;
			}
			
			if(	offerNoInc_btn.label.length == 1 && offer_btn.label.length ==9){
				offerNoInc_btn.width = 12;
				offerNoInc_btn.x = 208;
				slash1_txt.x = 195;
				offer_btn.x = 133;		
				offer_btn.width = 70;
			}
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==1){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 181;		
				offer_btn.width = 12;		
			}
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==2){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 173;		
				offer_btn.width = 22;
			}
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==3){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 163;		
				offer_btn.width = 32;
			}
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==5){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 152;		
				offer_btn.width = 42;
			}
			
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==6){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 144;		
				offer_btn.width = 50;
			}

			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==8){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 132;		
				offer_btn.width = 63;
			}
			
			if(	offerNoInc_btn.label.length == 2 && offer_btn.label.length ==9){
				offerNoInc_btn.width = 22;
				offerNoInc_btn.x = 200;
				slash1_txt.x = 187;
				offer_btn.x = 124;		
				offer_btn.width = 70;
			}
			
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==1){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 177;
				offer_btn.x = 172;	
				offer_btn.width = 12;		
			}
			
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==2){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 164;	
				offer_btn.width = 22;		
			}
			
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==3){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 155;	
				offer_btn.width = 32;		
			}
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==5){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 145;	
				offer_btn.width = 42;		
			}
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==6){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 135;	
				offer_btn.width = 50;		
			}
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==8){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 122;	
				offer_btn.width = 63;		
			}
			if(	offerNoInc_btn.label.length == 3 && offer_btn.label.length ==9){
				
				offerNoInc_btn.width = 32;
				offerNoInc_btn.x = 190;
				slash1_txt.x = 178;
				offer_btn.x = 116;	
				offer_btn.width = 70;		
			}
			
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==1){
								
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 164;	
				offer_btn.width = 12;						
			}

			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==2){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 156;		
				offer_btn.width = 22;
			}
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==3){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 146;		
				offer_btn.width = 32;
			}
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==5){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 136;		
				offer_btn.width = 42;
			}
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==6){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 128;		
				offer_btn.width = 50;
			}
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==8){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 115;		
				offer_btn.width = 63;
			}
			if(	offerNoInc_btn.label.length == 5 && offer_btn.label.length ==9){
				offerNoInc_btn.width = 42;
				offerNoInc_btn.x = 182;
				slash1_txt.x = 170;
				offer_btn.x = 105;		
				offer_btn.width = 70;
			}					
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==1){				
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 158;	
				offer_btn.width = 12;							
			}						
			
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==2){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 148;		
				offer_btn.width = 22;
			}
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==3){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 140;		
				offer_btn.width = 32;
			}
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==5){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 128;		
				offer_btn.width = 42;
			}
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==6){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 120;		
				offer_btn.width = 50;
			}
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==8){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 108;		
				offer_btn.width = 63;
			}
			if(	offerNoInc_btn.label.length == 6 && offer_btn.label.length ==9){
				offerNoInc_btn.width = 50;
				offerNoInc_btn.x = 174;
				slash1_txt.x = 162;
				offer_btn.x = 100;		
				offer_btn.width = 70;
			}
							
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==1){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 145;	
				offer_btn.width = 12;							
			}			
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==2){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 135;	
				offer_btn.width = 22;							
			}			
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==3){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 127;	
				offer_btn.width = 32;							
			}
						
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==5){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 115;	
				offer_btn.width = 42;							
			}			
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==6){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 107;	
				offer_btn.width = 50;							
			}		
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==8){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 95;	
				offer_btn.width = 63;							
			}			
			if(	offerNoInc_btn.label.length == 8 && offer_btn.label.length ==9){				
				offerNoInc_btn.width = 63;
				offerNoInc_btn.x = 162;
				slash1_txt.x = 150;
				offer_btn.x = 88;	
				offer_btn.width = 70;							
			}			
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==1){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 137;	
				offer_btn.width = 12;							
			}	
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==2){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 127;	
				offer_btn.width = 22;							
			}	
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==3){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 119;	
				offer_btn.width = 32;							
			}		
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==5){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 107;	
				offer_btn.width = 42;							
			}
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==6){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 100;	
				offer_btn.width = 50;							
			}
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==8){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 87;	
				offer_btn.width = 63;							
			}
			if(	offerNoInc_btn.label.length == 9 && offer_btn.label.length ==9){				
				offerNoInc_btn.width = 70;
				offerNoInc_btn.x = 155;
				slash1_txt.x = 143;
				offer_btn.x = 80;	
				offer_btn.width = 70;							
			}
			////////////////////// Second Row //////////////////////////
			
				slash2_txt.y = 53;
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==1){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 212;
				slash2_txt.x = 198;
				suspend_btn.x = 191;	
				suspend_btn.width = 12;				
			}
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==2){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 181;		
				suspend_btn.width = 22;
			}
			
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==3){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 170;		
				suspend_btn.width = 32;
			}
			
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==5){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 160;		
				suspend_btn.width = 42;
			}
			
			
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==6){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 153;		
				suspend_btn.width = 50;
			}
			
			
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==8){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 142;		
				suspend_btn.width = 63;
			}
			
			if(	suspendNoInc_btn.label.length == 1 && suspend_btn.label.length ==9){
				suspendNoInc_btn.width = 12;
				suspendNoInc_btn.x = 208;
				slash2_txt.x = 195;
				suspend_btn.x = 133;		
				suspend_btn.width = 70;
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==1){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 181;		
				suspend_btn.width = 12;		
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==2){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 173;		
				suspend_btn.width = 22;
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==3){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 163;		
				suspend_btn.width = 32;
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==5){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 152;		
				suspend_btn.width = 42;
			}
			
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==6){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 144;		
				suspend_btn.width = 50;
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==8){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 132;		
				suspend_btn.width = 63;
			}
			
			if(	suspendNoInc_btn.label.length == 2 && suspend_btn.label.length ==9){
				suspendNoInc_btn.width = 22;
				suspendNoInc_btn.x = 200;
				slash2_txt.x = 187;
				suspend_btn.x = 124;		
				suspend_btn.width = 70;
			}

			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==1){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 177;
				suspend_btn.x = 172;	
				suspend_btn.width = 12;		
			}
			
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==2){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 164;	
				suspend_btn.width = 22;		
			}
			
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==3){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 155;	
				suspend_btn.width = 32;		
			}
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==5){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 145;	
				suspend_btn.width = 42;		
			}
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==6){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 135;	
				suspend_btn.width = 50;		
			}
			
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==8){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 122;	
				suspend_btn.width = 63;		
			}
			if(	suspendNoInc_btn.label.length == 3 && suspend_btn.label.length ==9){
				
				suspendNoInc_btn.width = 32;
				suspendNoInc_btn.x = 190;
				slash2_txt.x = 178;
				suspend_btn.x = 116;	
				suspend_btn.width = 70;		
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==1){
								
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 164;	
				suspend_btn.width = 12;						
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==2){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 156;		
				suspend_btn.width = 22;
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==3){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 146;		
				suspend_btn.width = 32;
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==5){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 136;		
				suspend_btn.width = 42;
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==6){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 128;		
				suspend_btn.width = 50;
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==8){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 115;		
				suspend_btn.width = 63;
			}
			if(	suspendNoInc_btn.label.length == 5 && suspend_btn.label.length ==9){
				suspendNoInc_btn.width = 42;
				suspendNoInc_btn.x = 182;
				slash2_txt.x = 170;
				suspend_btn.x = 105;		
				suspend_btn.width = 70;
			}				
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==1){				
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 158;	
				suspend_btn.width = 12;							
			}						
			
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==2){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 148;		
				suspend_btn.width = 22;
			}
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==3){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 140;		
				suspend_btn.width = 32;
			}
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==5){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 128;		
				suspend_btn.width = 42;
			}
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==6){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 120;		
				suspend_btn.width = 50;
			}
			
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==8){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 108;		
				suspend_btn.width = 63;
			}
			if(	suspendNoInc_btn.label.length == 6 && suspend_btn.label.length ==9){
				suspendNoInc_btn.width = 50;
				suspendNoInc_btn.x = 174;
				slash2_txt.x = 162;
				suspend_btn.x = 100;		
				suspend_btn.width = 70;
			}
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==1){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 145;	
				suspend_btn.width = 12;							
			}			
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==2){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 135;	
				suspend_btn.width = 22;							
			}			
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==3){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 127;	
				suspend_btn.width = 32;							
			}
						
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==5){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 115;	
				suspend_btn.width = 42;							
			}			
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==6){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 107;	
				suspend_btn.width = 50;							
			}		
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==8){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 95;	
				suspend_btn.width = 63;							
			}			
			if(	suspendNoInc_btn.label.length == 8 && suspend_btn.label.length ==9){				
				suspendNoInc_btn.width = 63;
				suspendNoInc_btn.x = 162;
				slash2_txt.x = 150;
				suspend_btn.x = 88;	
				suspend_btn.width = 70;							
			}			
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==1){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 137;	
				suspend_btn.width = 12;							
			}	
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==2){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 127;	
				suspend_btn.width = 22;							
			}	
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==3){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 119;	
				suspend_btn.width = 32;							
			}		
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==5){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 107;	
				suspend_btn.width = 42;							
			}
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==6){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 100;	
				suspend_btn.width = 50;							
			}
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==8){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 87;	
				suspend_btn.width = 63;							
			}
			if(	suspendNoInc_btn.label.length == 9 && suspend_btn.label.length ==9){				
				suspendNoInc_btn.width = 70;
				suspendNoInc_btn.x = 155;
				slash2_txt.x = 143;
				suspend_btn.x = 80;	
				suspend_btn.width = 70;							
			}

			/////////////////////// Third Row /////////////////
			
				slash3_txt.y = 82;
		if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==1){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 210;
				slash3_txt.x = 198;
				confrmd_btn.x = 191;	
				confrmd_btn.width = 12;				
							
			}
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==2){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 181;		
				confrmd_btn.width = 22;
			}
			
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==3){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 170;		
				confrmd_btn.width = 32;
			}
			
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==5){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 160;		
				confrmd_btn.width = 42;
			}
			
			
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==6){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 153;		
				confrmd_btn.width = 50;
			}
			
			
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==8){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 142;		
				confrmd_btn.width = 63;
			}
			
			if(	confrmdNoInc_btn.label.length == 1 && confrmd_btn.label.length ==9){
				confrmdNoInc_btn.width = 12;
				confrmdNoInc_btn.x = 208;
				slash3_txt.x = 195;
				confrmd_btn.x = 133;		
				confrmd_btn.width = 70;
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==1){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 181;		
				confrmd_btn.width = 12;		
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==2){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 173;		
				confrmd_btn.width = 22;
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==3){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 163;		
				confrmd_btn.width = 32;
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==5){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 152;		
				confrmd_btn.width = 42;
			}
			
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==6){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 144;		
				confrmd_btn.width = 50;
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==8){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 132;		
				confrmd_btn.width = 63;
			}
			
			if(	confrmdNoInc_btn.label.length == 2 && confrmd_btn.label.length ==9){
				confrmdNoInc_btn.width = 22;
				confrmdNoInc_btn.x = 200;
				slash3_txt.x = 187;
				confrmd_btn.x = 124;		
				confrmd_btn.width = 70;
			}
			
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==1){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 177;
				confrmd_btn.x = 172;	
				confrmd_btn.width = 12;		
			}
			
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==2){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 164;	
				confrmd_btn.width = 22;		
			}
			
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==3){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 155;	
				confrmd_btn.width = 32;		
			}
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==5){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 145;	
				confrmd_btn.width = 42;		
			}
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==6){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 135;	
				confrmd_btn.width = 50;		
			}
			
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==8){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 122;	
				confrmd_btn.width = 63;		
			}
			if(	confrmdNoInc_btn.label.length == 3 && confrmd_btn.label.length ==9){
				
				confrmdNoInc_btn.width = 32;
				confrmdNoInc_btn.x = 190;
				slash3_txt.x = 178;
				confrmd_btn.x = 116;	
				confrmd_btn.width = 70;		
			}
			
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==1){
								
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 164;	
				confrmd_btn.width = 12;						
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==2){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 156;		
				confrmd_btn.width = 22;
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==3){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 146;		
				confrmd_btn.width = 32;
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==5){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 136;		
				confrmd_btn.width = 42;
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==6){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 128;		
				confrmd_btn.width = 50;
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==8){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 115;		
				confrmd_btn.width = 63;
			}
			if(	confrmdNoInc_btn.label.length == 5 && confrmd_btn.label.length ==9){
				confrmdNoInc_btn.width = 42;
				confrmdNoInc_btn.x = 182;
				slash3_txt.x = 170;
				confrmd_btn.x = 105;		
				confrmd_btn.width = 70;
			}				
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==1){				
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 158;	
				confrmd_btn.width = 12;							
			}						
			
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==2){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 148;		
				confrmd_btn.width = 22;
			}
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==3){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 140;		
				confrmd_btn.width = 32;
			}
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==5){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 128;		
				confrmd_btn.width = 42;
			}
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==6){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 120;		
				confrmd_btn.width = 50;
			}
			
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==8){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 108;		
				confrmd_btn.width = 63;
			}
			if(	confrmdNoInc_btn.label.length == 6 && confrmd_btn.label.length ==9){
				confrmdNoInc_btn.width = 50;
				confrmdNoInc_btn.x = 174;
				slash3_txt.x = 162;
				confrmd_btn.x = 100;		
				confrmd_btn.width = 70;
			}
			
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==1){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 145;	
				confrmd_btn.width = 12;							
			}			
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==2){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 135;	
				confrmd_btn.width = 22;							
			}			
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==3){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 127;	
				confrmd_btn.width = 32;							
			}
						
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==5){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 115;	
				confrmd_btn.width = 42;							
			}			
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==6){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 107;	
				confrmd_btn.width = 50;							
			}		
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==8){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 95;	
				confrmd_btn.width = 63;							
			}			
			if(	confrmdNoInc_btn.label.length == 8 && confrmd_btn.label.length ==9){				
				confrmdNoInc_btn.width = 63;
				confrmdNoInc_btn.x = 162;
				slash3_txt.x = 150;
				confrmd_btn.x = 88;	
				confrmd_btn.width = 70;							
			}			
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==1){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 137;	
				confrmd_btn.width = 12;							
			}	
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==2){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 127;	
				confrmd_btn.width = 22;							
			}	
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==3){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 119;	
				confrmd_btn.width = 32;							
			}		
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==5){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 107;	
				confrmd_btn.width = 42;							
			}
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==6){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 100;	
				confrmd_btn.width = 50;							
			}
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==8){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 87;	
				confrmd_btn.width = 63;							
			}
			if(	confrmdNoInc_btn.label.length == 9 && confrmd_btn.label.length ==9){				
				confrmdNoInc_btn.width = 70;
				confrmdNoInc_btn.x = 155;
				slash3_txt.x = 143;
				confrmd_btn.x = 80;	
				confrmd_btn.width = 70;							
			}

}