<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the ForecastMonitor screen. 
  - Also, to load the label values for this screen.	
  -
  - Author(s): Bala .D
  - Date: 08-05-2011
  -->

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
		<script type="text/javascript">
		var screenTitle = "";
		screenTitle = getMessage("label.forecastMonitor.title.window", null);
		document.title = screenTitle;
		var screenRoute = "ForecastMonitor";
		// This method is called when onload
		window.onload = function () {
		    setTitleSuffix(document.forms[0]);
			setParentChildsFocus();									
		};

		window.onunload = call;
		
	

			/*label["text"]["currency"] = "<fmt:message key="label.forecastMonitor.currency"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.forecastMonitor.selectCurrency"/>";
			
			label["text"]["entity"] = "<fmt:message key="label.forecastMonitor.entity"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.forecastMonitor.selectEntity"/>";
			
			label["text"]["breakdown"] = "<fmt:message key="label.forecastMonitor.breakdown"/>";
			label["text"]["mvmntbrkdown"] = "<fmt:message key="label.forecastMonitor.movementId"/>";
			label["tip"]["mvmntbrkdown"] = "<fmt:message key="tooltip.forecastMonitor.mvmntBrkdown"/>";
			label["text"]["bookbrkdown"] = "<fmt:message key="label.forecastMonitor.bookCode"/>";
			label["tip"]["bookbrkdown"] = "<fmt:message key="tooltip.forecastMonitor.bookBrkdown"/>";
			label["text"]["button-refresh"] = "<fmt:message key="button.forecastMonitor.refresh"/>";
			label["tip"]["button-refresh"] = "<fmt:message key="tooltip.forecastMonitor.refreshWindow"/>";
			label["text"]["button-rate"] = "<fmt:message key="button.forecastMonitor.rate"/>";
			label["tip"]["button-rate"] = "<fmt:message key="tooltip.forecastMonitor.rateButton"/>";
			label["text"]["button-options"] = "<fmt:message key="button.forecastMonitor.option"/>";
			label["tip"]["button-options"] = "<fmt:message key="tooltip.forecastMonitor.option"/>";
			label["text"]["button-close"] = "<fmt:message key="button.forecastMonitor.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.forecastMonitor.close"/>";
			label["text"]["templateid"] = "<fmt:message key="label.forecastMonitor.templateId"/>";	
			
			label["tip"]["label-hideButtonBar"] = "<fmt:message key="label.hideButtonBar"/>";
			label["tip"]["label-showButtonBar"] = "<fmt:message key="label.showButtonBar"/>";
			label["alert"]["template-locked"] = "<fmt:message key="alert.forecasttemplate.templatelocked"/>";
			label["alert"]["template-delete"] = "<fmt:message key="alert.forecasttemplate.templatedelete"/>";
			label["alert"]["template-mandatory"]="<fmt:message key="alert.forecasttemplate.mandatory"/>";
			label["alert"]["template-totalmultiplier"]="<fmt:message key="alert.forecasttemplate.totalmultiplier"/>";
			label["alert"]["template-columnnumbers"]="<fmt:message key="alert.forecasttemplate.columnnumbers"/>";
			
			// Changed by KaisBS for mantis 2004 to have the common alert  
			label["text"]["noAccessCurrency"] = "<fmt:message key="alert.currencyAccess"/>";
			label["text"]["totalValues"] = "<fmt:message key="alert.forecastMonitor.totalValues"/>";
			label["text"]["enterNumber"] = "<fmt:message key="alert.forecastMonitor.enterNumber"/>";
			label["text"]["validNumber"] = "<fmt:message key="alert.forecastMonitor.validNumber"/>";
			label["text"]["refreshRate"] = "<fmt:message key="alert.forecastMonitor.refreshRate"/>";
			label["text"]["connectionLost"] = "<fmt:message key="alert.forecastMonitor.connectionLost"/>";
			label["text"]["notNumber"] = "<fmt:message key="alert.forecastMonitor.notNumber"/>";
                        label["text"]["label-connectionLost"] = "<fmt:message key="label.lossConnection"/>";
			label["alert"]["template-columndelete"] = "<fmt:message key="alert.forecasttemplate.columndelete"/>";
			label["alert"]["template-exist"] = "<fmt:message key="alert.forecasttemplate.templateexist"/>";
			label["alert"]["template-save"] = "<fmt:message key="alert.forecasttemplate.savetemplate"/>";	
			label["alert"]["template-validnumber"]="<fmt:message key="alert.forecasttemplate.validnumber"/>";*/
			
			/*
 			* Code modified by karthik on 20110817 for Mantis 1525 - ING connection exhaust issue
 			*/
			// get the system date
			var dbDate="<%=SwtUtil.getSysDateWithFmt(SwtUtil.getSystemDateFromDB())%>";
			// get the date format
			var dateFormat = '${sessionScope.CDM.dateFormatValue}';;
			// get the item id
			var itemId = '${requestScope.itemId}';
			// get the host id
			var hostId = '${requestScope.hostId}';
			// get the user id
			var userId = '${requestScope.userId}';		 
			
			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			// option window
			var optionWindow = "";
			// currency format
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';
			// This method is used to send the request
			function sendRequest (requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open ("GET", requestURL, false);
				oXHR.send ();
				var count = new Number (oXHR.responseText);
				return count;
			}
			
			/**
             * formatCurrency
             * This method is used to format the amount in system currency format (called by flex) 
             * @param amount
 			 * @return String
			 */
			function formatCurrency(amount){
				var strAmount=formatCurrency_forecastMonitor(amount, currencyFormat, null);
				return strAmount;
			}
			
			/**
             * openForecastOptionsWindow
             * This function opens the Forecast Monitor Option Window 
 			 * @return none
			 */
            function openForecastOptionsWindow () {	
				var param = '/' + appName + '/forecastMonitor.do?method=flexOption';
				optionWindow = window.open (param, 'forecastMonitorOptions', 'width=900,height=750,toolbar=0, resizable=yes, scrollbars=no, left=5, top=242', 'true');
			}
			
			/**
             * openAssumptionWindow
             * This function opens the Forecast Assumption Window
             * @param assumptions
 			 * @return none
			 */
            function openAssumptionWindow (assumptions) {	
				var param = '/' + appName + '/forecastMonitor.do?method=flexAssumption&entity='+assumptions.entity+'&currency='+assumptions.currency+'&date='+assumptions.date+'&templateId='+assumptions.templateId;
				optionWindow = window.open (param, 'forecastMonitorAssumptions', 'width=700,height=500,toolbar=0, resizable=yes, scrollbars=no, left=5, top=242', 'true');
			}

			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Forecast Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }

		
			
			/**
             * closeChild
             * This function used to close the child window
 			 * @return none
			 */
			function closeChild(){
				// Added By KaisBS : issue 1054_STL_038 : avoid the javascript error if we aren't open the option screen
				if(typeof(optionWindow) == window)
				optionWindow.close();
			}
			function callApp(refreshStart){		
				Main.startAutoRefresh(refreshStart);
			}
			
			/**
             * getUpdateRefreshRequest
             * This function used to determine what to request to save a new refresh rate
             * @param rate
             * @param item_id
 			 * @return String
			 */
			function getUpdateRefreshRequest (rate,item_id) {
				return "screenOption.do?method=save&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + item_id + "&screenOption.propertyValue=" + rate;
			}
			
			/**
             * closeWindow
             * This function used to close the window
 			 * @return none
			 */
            function closeWindow() {
				window.close();
			}      
			
    </script>
    <%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> 
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.forecastMonitor.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
