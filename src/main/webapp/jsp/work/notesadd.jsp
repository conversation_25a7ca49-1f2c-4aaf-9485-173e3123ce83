<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.viewButtonStatus == 'view'}">
        <title><fmt:message key="viewNotes.title.window" /></title>
    </c:when>
    <c:otherwise>
        <title><fmt:message key="addNotes.title.window" /></title>
    </c:otherwise>
</c:choose>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script type="text/javascript" src="js/datavalidation.js"></script>

<SCRIPT language="JAVASCRIPT">

var screenName = '${requestScope.screenName}';
var isNotesPresent = '${requestScope.isNotesPresent}';
var currencyCode = '${requestScope.currencyCode}';
var date = '${requestScope.date}';
var currencyAccess="${requestScope.currencyAccess}";
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
    <c:choose>
        <c:when test="${requestScope.notesStatus == 'matchNotes'}">
            window.opener.document.forms[0].method.value = "showMatchNotes";
            window.opener.document.forms[0].screenName.value = screenName;
            window.opener.document.forms[0].selectedMatchId.value = "${requestScope.matchId}";
        </c:when>
        <c:otherwise>
            window.opener.document.forms[0].currencyAccess.value = currencyAccess;
            <c:choose>
                <c:when test="${requestScope.notesStatus == 'sweepNotes'}">
                    window.opener.document.forms[0].method.value = "showSweepNotes";
                    window.opener.document.forms[0].selectedSweepId.value = "${requestScope.sweepId}";
                </c:when>
                <c:otherwise>
                    window.opener.document.forms[0].recordFound.value = "${requestScope.recordFound}";
                    window.opener.document.forms[0].method.value = "notes";
                    window.opener.document.forms[0].selectedMovId.value = "${requestScope.movId}";
                    window.opener.document.forms[0].movId.value = "${requestScope.movId}";
                    window.opener.document.forms[0].screenName.value = screenName;
                    window.opener.document.forms[0].isNotesPresent.value = isNotesPresent;
                    window.opener.document.forms[0].currencyCode.value = currencyCode;
                    window.opener.document.forms[0].date.value = date;
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>
    window.opener.document.forms[0].selectedEntityId.value = "${requestScope.entityCode}";
    window.opener.document.forms[0].isParentScreenRefresh.value = "N";
    window.opener.document.forms[0].unLock.value = "${requestScope.unLock}";
    window.opener.document.forms[0].submit();
    self.close();
</c:if>





var maxlength = 500;
var entityCode="${requestScope.entityCode}";
var matchId="${requestScope.matchId}";
var sweepId="${requestScope.sweepId}";


console.log("entityCode: " + entityCode, "matchId: " + matchId, "sweepId: " + sweepId);


function textAreaPress(element,e)
{
	var event = (window.event|| e);
	if(element.value.length > maxlength-1 ){
		if(window.event)
			window.event.returnValue = false;
		else
			event.preventDefault();
	}
}


function textAreaPaste(element) {

    <c:choose>
    <c:when test="${requestScope.method == 'showMatchNotes'}">
    var element = document.forms[0].elements["notesMatch.noteText"];
    </c:when>
    <c:otherwise>
    <c:choose>
    <c:when test="${requestScope.method == 'showSweepNotes'}">
    var element = document.forms[0].elements["notesSweep.noteText"];
    </c:when>
    <c:otherwise>
    var element = document.forms[0].elements["notes.noteText"];
    </c:otherwise>
    </c:choose>
    </c:otherwise>
    </c:choose>

var pasteData = window.clipboardData.getData("Text");

var existingDataLength = element.value.length;


var dataAllowedToBePastedLength = maxlength-existingDataLength;


if(dataAllowedToBePastedLength >0)
{
	var remainingData = new String(pasteData);
	if(pasteData.length > dataAllowedToBePastedLength)
	{
		remainingData = new String(pasteData).substring(0,dataAllowedToBePastedLength);
	
	}
	element.value = element.value + remainingData;
	
}
	if (window.event)
		window.event.returnValue = false;

}

function loadBody() {
    setTitleSuffix(document.forms[0]);

    <c:choose>
        <c:when test="${requestScope.method == 'showMatchNotes'}">
            document.forms[0].elements["notesMatch.noteText"].onpaste = textAreaPaste;
        </c:when>
        <c:otherwise>
            <c:choose>
                <c:when test="${requestScope.method == 'showSweepNotes'}">
                    document.forms[0].elements["notesSweep.noteText"].onpaste = textAreaPaste;
                </c:when>
                <c:otherwise>
                    document.forms[0].elements["notes.noteText"].onpaste = textAreaPaste;
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>

    this.window.focus();
    setFocus();
}


function submitForm(methodName){
elementTrim(document.forms[0]);
	 if(validateForm(document.forms[0])){
	document.forms[0].selectedEntityId.value = entityCode ;
	document.forms[0].selectedMovId.value = "${requestScope.movId}" ;
	document.forms[0].method.value = methodName;
	document.forms[0].screenName.value = screenName;
	
	document.forms[0].currencyAccess.value=currencyAccess;
	document.forms[0].unLock.value = window.opener.unLock;
	document.forms[0].isNotesPresent.value = isNotesPresent;
	document.forms[0].currencyCode.value = currencyCode;
	document.forms[0].date.value = date;
	document.forms[0].submit();
	}
	
}
function validateForm(objForm) {
    var elementsRef = new Array(1);

    <c:choose>
        <c:when test="${requestScope.method == 'showMatchNotes'}">
            elementsRef[0] = objForm.elements["notesMatch.noteText"];
        </c:when>
        <c:otherwise>
            <c:choose>
                <c:when test="${requestScope.method == 'showSweepNotes'}">
                    elementsRef[0] = objForm.elements["notesSweep.noteText"];
                </c:when>
                <c:otherwise>
                    elementsRef[0] = objForm.elements["notes.noteText"];
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>

    return validateNotes(elementsRef);
}


function validateNotes(elArr) {
   
    var v = "notEmptyPat"; 
    var thePat = PatternsDict[v]; 
    for(var i=0; i<elArr.length; i++)
    with(elArr[i]) { 
        var gotIt = thePat.exec(elArr[i].value); 
        if(!gotIt) {
            var returnStr;
            readName = elArr[i].name;
            returnStr = "Please enter some notes!";
            alert(returnStr);
            elArr[i].focus();
            return false;
        }
    } 
    return true;
}

function saveMatchNotes(methodName)
{
 if(validateForm(document.forms[0])){
	 document.forms[0].selectedEntityId.value = entityCode ;
	 document.forms[0].selectedMatchId.value = matchId ;
     document.forms[0].screenName.value = screenName;
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}

}


function saveSweepNotes(methodName)
{
elementTrim(document.forms[0]);
if(validateForm(document.forms[0])){
	 document.forms[0].selectedEntityId.value = entityCode ;
	 document.forms[0].selectedSweepId.value = sweepId ;
	document.forms[0].method.value = methodName;
	
	document.forms[0].currencyAccess.value=currencyAccess;
	document.forms[0].submit();
	}

}

function setFocus() {
    <c:choose>
        <c:when test="${requestScope.method == 'showMatchNotes'}">
            if (document.forms[0].elements["notesMatch.noteText"] != null) {
                document.forms[0].elements["notesMatch.noteText"].focus();
            }
        </c:when>
        <c:otherwise>
            <c:choose>
                <c:when test="${requestScope.method == 'showSweepNotes'}">
                    if (document.forms[0].elements["notesSweep.noteText"] != null) {
                        document.forms[0].elements["notesSweep.noteText"].focus();
                    }
                </c:when>
                <c:otherwise>
                    if (document.forms[0].elements["notes.noteText"] != null) {
                        document.forms[0].elements["notes.noteText"].focus();
                    }
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>
}


</SCRIPT>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad=" loadBody();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus();" onunload="call()">
<form action="notes.do" onsubmit="return validate(this);" method="post">
<input name="method" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value="">
<input name="selectedMatchId" type="hidden" value="">
<input name="selectedSweepId" type="hidden" value="">
<input name="selectedMovId" type="hidden" value="">
<input name="unLock" type="hidden" value="">
<input name="screenName" type="hidden" value="">
<input name="isNotesPresent" type="hidden" value="">
<input name="currencyCode" type="hidden" value="">
<input name="date" type="hidden" value="">
<input name="currencyAccess" type="hidden" value="">


<c:if test="${requestScope.viewButtonStatus == 'view'}">
 <div id="Notes" style="position:absolute; left:20px; top:20px; width:488px; height:43px; border:2px outset;" color="#7E97AF">
<div id="Notes" style="position:absolute;left:8px; top:4px; width:380px; height:43px;">
 <table width="477" border="0" cellpadding="0" cellspacing="0" class="content">
	<tr height="25px" >
	<c:if test="${requestScope.method == 'showMatchNotes'}">
    <td width="25"><b><fmt:message key="matchQuality.matchId"/></b></td>
	</c:if>
	<c:if test="${requestScope.method != 'showMatchNotes'}">
		<c:choose>
			<c:when test="${requestScope.method == 'showSweepNotes'}">
				<td width="25"><b><fmt:message key="sweep.sweepId"/></b></td>
			</c:when>
			<c:otherwise>
				<td width="25"><b><fmt:message key="movement.movementId"/></b></td>
			</c:otherwise>
		</c:choose>
	</c:if>


	    <td width="28">&nbsp;</td>
	  <td width="140">
	  <c:if test="${requestScope.method == 'showMatchNotes'}">
    <input type="text" name="notesMatch.id.matchId" readonly="readonly"  class="htmlTextNumeric"
           style="width:92px;background:transparent; border:thin;"
           value="${notesMatch.id.matchId}" />
		</c:if>
		<c:if test="${requestScope.method != 'showMatchNotes'}">
			<c:choose>
				<c:when test="${requestScope.method == 'showSweepNotes'}">
					<input type="text" name="notesSweep.id.sweepId" readonly="readonly"  class="htmlTextNumeric"
						   style="width:92px;background:transparent; border:thin;"
						   value="${notesSweep.id.sweepId}" />
				</c:when>
				<c:otherwise>
					<input type="text" name="notes.id.movementId" readonly="readonly"  class="htmlTextNumeric"
						   style="width:92px;background:transparent; border:thin;"
						   value="${notes.id.movementId}" />
				</c:otherwise>
			</c:choose>
		</c:if>

	  </td>
	      <td width="28">&nbsp;</td>
<c:if test="${requestScope.method == 'showMatchNotes'}">
    <td width="25">
        <input type="text" name="notesMatch.id.updateDateAsString" readonly="readonly"  class="htmlTextNumeric"
               style="width:79px;background:transparent;color:green; border:thin;"
               value="${notesMatch.id.updateDateAsString}" />
    </td>
</c:if>
	<c:if test="${requestScope.method != 'showMatchNotes'}">
		<c:choose>
			<c:when test="${requestScope.method == 'showSweepNotes'}">
				<td width="25">
					<input type="text" name="notesSweep.id.updateDateAsString" readonly="readonly"  class="htmlTextNumeric"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notesSweep.id.updateDateAsString}" />
				</td>
			</c:when>
			<c:otherwise>
				<td width="25">
					<input type="text" name="notes.id.updateDateAsString" readonly="readonly"  class="htmlTextNumeric"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notes.id.updateDateAsString}" />
				</td>
			</c:otherwise>
		</c:choose>
	</c:if>
	<td width="28">&nbsp;</td>
	<c:if test="${requestScope.method == 'showMatchNotes'}">
		<td width="25">
			<input type="text" name="notesMatch.id.updateTimeAsString" readonly="readonly"  class="htmlTextNumeric"
				   style="width:79px;background:transparent;color:green; border:thin;"
				   value="${notesMatch.id.updateTimeAsString}" />
		</td>
	</c:if>
	<c:if test="${requestScope.method != 'showMatchNotes'}">
		<c:choose>
			<c:when test="${requestScope.method == 'showSweepNotes'}">
				<td width="25">
					<input type="text" name="notesSweep.id.updateTimeAsString" readonly="readonly"  class="htmlTextNumeric"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notesSweep.id.updateTimeAsString}" />
				</td>
			</c:when>
			<c:otherwise>
				<td width="25">
					<input type="text" name="notes.id.updateTimeAsString" readonly="readonly"  class="htmlTextNumeric"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notes.id.updateTimeAsString}" />
				</td>
			</c:otherwise>
		</c:choose>
	</c:if>




	    <td width="28">&nbsp;</td>
	  <td width="144">
		<c:if test="${requestScope.method == 'showMatchNotes'}">
			<input type="text" name="notesMatch.updateUser" readonly="readonly"
				   style="width:79px;background:transparent;color:green; border:thin;"
				   value="${notesMatch.updateUser}" />
		</c:if>
		<c:if test="${requestScope.method != 'showMatchNotes'}">
			<c:choose>
				<c:when test="${requestScope.method == 'showSweepNotes'}">
					<input type="text" name="notesSweep.updateUser" readonly="readonly"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notesSweep.updateUser}" />
				</c:when>
				<c:otherwise>
					<input type="text" name="notes.updateUser" readonly="readonly"
						   style="width:79px;background:transparent;color:green; border:thin;"
						   value="${notes.updateUser}" />
				</c:otherwise>
			</c:choose>
		</c:if>
	  </td>
	  </tr>

</table>
</div>
</div>
</c:if>
<c:if test="${requestScope.viewButtonStatus == 'view'}">

<div id="Notes" style="position:absolute; left:20px; top:66px; width:488px; height:135px; border:2px outset;" color="#7E97AF">
<div id="Notes" style="position:absolute;left:8px; top:4px; width:488px; height:120px;">

<table  width="400" border="0" cellspacing="0" cellpadding="0"  height="120">
<table  width="0" border="0" cellspacing="0" cellpadding="0"  height="10">

	</table>
	<div id="NotesAdd" style="position:absolute;left:0px; top:0px; width:300px; height:120px;">
	<table  width="300" border="0" cellspacing="0" cellpadding="0"  height="120">
	<tr height="25px">


	<td width= 400>
		<c:choose>
			<c:when test="${requestScope.method == 'showMatchNotes'}">
				<textarea style="width:470px;height:120px;" rows="8" cols="75" name="notesMatch.noteText" onkeypress="textAreaPress(this,event)" readonly="${requestScope.screenFieldsStatus}">${notesMatch.noteText}</textarea>
			</c:when>
			<c:otherwise>
				<c:choose>
					<c:when test="${requestScope.method == 'showSweepNotes'}">
						<textarea style="width:470px;height:120px;" rows="8" cols="75" name="notesSweep.noteText" onkeypress="textAreaPress(this,event)" readonly="${requestScope.screenFieldsStatus}">${notesSweep.noteText}</textarea>
					</c:when>
					<c:otherwise>
						<textarea style="width:470px;height:120px;" rows="8" cols="75" name="notes.noteText" onkeypress="textAreaPress(this,event)" readonly="${requestScope.screenFieldsStatus}">${notes.noteText}</textarea>
					</c:otherwise>
				</c:choose>
			</c:otherwise>
		</c:choose>
	</td>
	</tr>


	</table>
</div>
</div>
</c:if>
<c:if test="${requestScope.viewButtonStatus != 'view'}">
<div id="Notes" style="position:absolute; left:20px; top:8px; width:488px; height:135px; border:2px outset;" color="#7E97AF">
<div id="Notes" style="position:absolute;left:6px; top:4px; width:488px; height:120px;">

<table  width="400" border="0" cellspacing="0" cellpadding="0"  height="120">
<table  width="0" border="0" cellspacing="0" cellpadding="0"  height="10">

	</table>
	<div id="NotesAdd" style="position:absolute;left:0px; top:0px; width:300px; height:120px;">
	<table  width="300" border="0" cellspacing="0" cellpadding="0"  height="120">
	<tr height="25px">


	<td width="400">
    <c:if test="${requestScope.method == 'showMatchNotes'}">
       <textarea
           style="width:470px;height:120px;"
           rows="8"
           cols="75"
           name="notesMatch.noteText"
           onkeypress="textAreaPress(this,event)"
           <c:if test="${requestScope.screenFieldsStatus}">readonly</c:if>
           title="<fmt:message key='tooltip.enterNotes' />">${notesMatch.noteText}</textarea>
    </c:if>
    <c:if test="${requestScope.method != 'showMatchNotes'}">
       <c:if test="${requestScope.method == 'showSweepNotes'}">
          <textarea
              style="width:470px;height:120px;"
              rows="8"
              cols="75"
              name="notesSweep.noteText"
              onkeypress="textAreaPress(this,event)"
              <c:if test="${requestScope.screenFieldsStatus}">readonly</c:if>
              title="<fmt:message key='tooltip.enterNotes' />">${notesSweep.noteText}</textarea>
       </c:if>
       <c:if test="${requestScope.method != 'showSweepNotes'}">
          <textarea
              style="width:470px;height:120px;"
              rows="8"
              cols="75"
              name="notes.noteText"
              onkeypress="textAreaPress(this,event)"
              <c:if test="${requestScope.screenFieldsStatus}">readonly</c:if>
              title="<fmt:message key='tooltip.enterNotes' />">${notes.noteText}</textarea>
       </c:if>
    </c:if>
</td>
	 </tr>
	 </table>
	 </div>
	 </div>
	 </c:if>

</div>
<c:if test="${requestScope.viewButtonStatus != 'view'}">

<div id="Notes" style="position:absolute ;left:430; top:155px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				 <a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Notes Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		   </td>

			<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
</c:if>

<c:if test="${requestScope.viewButtonStatus == 'view'}">
<div id="Notes" style="position:absolute ;left:435; top:213px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr><td align="Right">
				 <a tabindex="5" onKeyDown="submitEnter(this,event)" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Notes Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		   </td>
			<td align="right" id="Print">
				<a tabindex="5" tabindex="6" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
  </c:if>
  <c:if test="${requestScope.viewButtonStatus != 'view'}">
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:146; width:488px; height:39px; visibility:visible;">
</c:if>
 <c:if test="${requestScope.viewButtonStatus == 'view'}">
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:205; width:488px; height:39px; visibility:visible;">
  </c:if>

<c:if test="${requestScope.method == 'showMatchNotes'}">
<div id="notes" style="position:absolute; left:6; top:4; width:400px; height:15px; visibility:visible;">
  <table width="130" border="0" cellspacing="0" cellpadding="0" height="20">

    <c:if test="${requestScope.viewButtonStatus != 'view'}">
	<tr>
	 <!-- Save Button  -->
		  <td width="70px">
		  <a title='<fmt:message key="tooltip.SaveNotesandExit"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:saveMatchNotes('saveMatchNotes');"><fmt:message key="button.save"/></a>
		  </td>

	 <!-- Cancel Button  -->
		  <td  width="70px">
			<a title='<fmt:message key="tooltip.cancel"/>' tabindex="3"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a>
		  </td>
		</tr>
	 </c:if>

	 <c:if test="${requestScope.viewButtonStatus == 'view'}">
	 <tr>
	 <!-- close Button  -->
	<td id="closebutton"><a title='<fmt:message key="tooltip.close"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"><fmt:message key="button.close"/></a></td>
		</tr>
	 </c:if>

	</table>
  </div>
  </c:if>
<c:if test="${requestScope.method != 'showMatchNotes'}">
  <c:if test="${requestScope.method == 'showSweepNotes'}">

<div id="notes" style="position:absolute; left:6; top:4; width:400px; height:15px; visibility:visible;">
  <table width="130" border="0" cellspacing="0" cellpadding="0" height="20">

    <c:if test="${requestScope.viewButtonStatus != 'view'}">
	<tr>
	 <!-- Save Button  -->
		  <td width="70px">
		  <a title='<fmt:message key="tooltip.SaveNotesandExit"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:saveSweepNotes('saveSweepNotes');"><fmt:message key="button.save"/></a>
		  </td>
<c:out value="aaa"/>
	 <!-- Cancel Button  -->
		  <td >
			<a title="<fmt:message key='tooltip.cancel'/>" tabindex="3"
					   onMouseOut="collapsebutton(this)"
					   onMouseOver="highlightbutton(this)"
					   onMouseDown="expandbutton(this)"
					   onMouseUp="highlightbutton(this)"
					   onKeyDown="submitEnter(this,event)"
					   onclick="javascript:window.close();">
					   <fmt:message key="button.cancel"/>
					</a>

		  </td>
		</tr>
	 </c:if>

	<c:if test="${requestScope.viewButtonStatus == 'view'}">
	 <tr>
	 <c:out value="bbb"/>
	 <!-- close Button  -->
	<td id="closebutton"><a title='<fmt:message key="tooltip.close"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"><fmt:message key="button.close"/></a></td>
		</tr>
	 </c:if>

	</table>
  </div>
  </c:if>
   <c:if test="${requestScope.method != 'showSweepNotes'}">
<div id="notes" style="position:absolute; left:6; top:4; width:400px; height:15px; visibility:visible;">
  <table width="130" border="0" cellspacing="0" cellpadding="0" height="20">

    <c:if test="${requestScope.viewButtonStatus != 'view'}">
	<tr>
	 <!-- Save Button  -->
		  <td width="70px">
		  <a title="<fmt:message key='tooltip.SaveNotesandExit'/>" onmouseout="collapsebutton(this)" tabindex="2" onmouseover="highlightbutton(this)" onmousedown="expandbutton(this)" onmouseup="highlightbutton(this)" onkeydown="submitEnter(this,event)" onclick="javascript:submitForm('save');">
			<fmt:message key="button.save"/>
		</a>
		  </td>

	 <!-- Cancel Button  -->
		  <td width="70px">
			<a title="<fmt:message key='tooltip.cancel'/>" tabindex="3" onmouseout="collapsebutton(this)" onmouseover="highlightbutton(this)" onmousedown="expandbutton(this)" onmouseup="highlightbutton(this)" onkeydown="submitEnter(this,event)" onclick="javascript:window.close();">
    <fmt:message key="button.cancel"/>
		</a>
		  </td>
		</tr>
	 </c:if>

	<c:if test="${requestScope.viewButtonStatus == 'view'}">
	 <tr>
	 <!-- close Button  -->
	<td id="closebutton">
    	<a title="<fmt:message key='tooltip.close'/>" tabindex="3" onmouseout="collapsebutton(this)" onmouseover="highlightbutton(this)" onmousedown="expandbutton(this)" onmouseup="highlightbutton(this)" onkeydown="submitEnter(this,event)" onclick="javascript:window.close();">
        	<fmt:message key="button.close"/>
		</a>
	</td>

		</tr>
	 </c:if>

	</table>
  </div>
  </c:if>
  </c:if>

</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>