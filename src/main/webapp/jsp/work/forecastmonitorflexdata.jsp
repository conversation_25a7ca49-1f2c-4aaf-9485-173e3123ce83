<?xml version="1.0" encoding="UTF-8"?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for ForecastMonitor screen.
-
- Author(s): Bala .D
- Date: 08-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ page import="java.util.*"  %>
<%@ page import="org.swallow.util.OpTimer"  %>
<%@ page import="org.swallow.util.SwtConstants"  %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<!-- Start: Code modified by <PERSON><PERSON> for 1053 beta3 testing issue on 14-Sep-2011 - Set the Template Id tooltip as Template Name -->
<forecastmonitor
databuilding="<%= request.getAttribute("jobFlagStatus")!=null?"true":"false" %>"
refresh="${requestScope.autoRefreshRate}"
lastRefTime="${requestScope.lastRefTime}"
templateId="${requestScope.templateId}"
templateName="${requestScope.templateName}"
dividerWidth="${requestScope.divider_width}"
>
<!-- End: Code modified by Bala for 1053 beta3 testing issue on 14-Sep-2011 - Set the Template Id tooltip as Template Name -->
<request_reply>
	<status_ok>${requestScope.reply_status_ok}</status_ok>
	<message>${requestScope.reply_message}</message>
	<location />
</request_reply>

<timing>
	<c:forEach var="opTime" items="${requestScope.opTimes}">
		<operation id="${opTime.key}">${opTime.value}</operation>
	</c:forEach>
</timing>

<grid>
<metadata>
	<columns>
		<column
				heading="EXPAND"
				draggable="false"
				filterable="false"
				dataelement="expand"
				width="5"
				resizable="false"
				sort="false"
				type="str"
		/>
		<c:set var="record" value="${requestScope.forecastMonitor.grandTotalRecords}"/>
		<c:forEach var="forecastrecord" items="${requestScope.forecastMonitor.grandTotalRecords}">
			<c:if test="${forecastrecord.header != 'Bucket'}">
				<c:choose>
					<c:when test="${forecastrecord.gridType != 'sub'}">
						<column
								heading="${forecastrecord.header}"
								draggable="false"
								filterable="false"
								editable="<c:choose>
                                        <c:when test="${requestScope.menuAccess == '1'}">false</c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${forecastrecord.header == 'Scenario'}">true</c:when>
                                                <c:otherwise>false</c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>"
								columntype="${forecastrecord.gridType}"
								type="num"
								sort="false"
								dataelement="${forecastrecord.headerData}"
								width="<c:choose>
                                        <c:when test="${fn:contains(forecastrecord.header, 'Date')}">${requestScope.main_width}</c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${forecastrecord.gridType == 'sub'}">${requestScope.sub_width}</c:when>
                                                <c:otherwise>${requestScope.main_width}</c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>"
								clickable="false"
								holiday="false"
								headerTooltip="${forecastrecord.headerTooltip}"
						/>
					</c:when>
					<c:otherwise>
						<column
								heading="${forecastrecord.header}"
								draggable="false"
								filterable="false"
								editable="<c:choose>
                                        <c:when test="${requestScope.menuAccess == '1'}">false</c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${forecastrecord.header == 'Scenario'}">true</c:when>
                                                <c:otherwise>false</c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>"
								columntype="${forecastrecord.gridType}"
								type="str"
								sort="false"
								dataelement="${forecastrecord.headerData}"
								width="<c:choose>
                                        <c:when test="${fn:contains(forecastrecord.header, 'Date')}">90</c:when>
                                        <c:otherwise>
                                            <c:choose>
                                                <c:when test="${forecastrecord.gridType == 'sub'}">${requestScope.sub_width}</c:when>
                                                <c:otherwise>${requestScope.main_width}</c:otherwise>
                                            </c:choose>
                                        </c:otherwise>
                                    </c:choose>"
								clickable="false"
								holiday="false"
								visible="false"
								headerTooltip="${forecastrecord.headerTooltip}"
						/>
					</c:otherwise>
				</c:choose>
			</c:if>
		</c:forEach>
	</columns>
</metadata>
<rows size="${requestScope.recordCount}">
<c:forEach var="forecastrecord" items="${requestScope.forecastMonitor.forecastMonitorRecords}">
	<c:choose>
		<c:when test="${fn:contains(forecastrecord.balanceDate, 'Total')}">
			<row visible="true">
			<expand clickable="false" opened="true" content="Y"></expand>
		</c:when>
		<c:otherwise>
			<row visible="${forecastrecord.bucketState == 'Y' ? 'true' : 'false'}">
			<expand clickable="false" opened="true" content="N"></expand>
		</c:otherwise>
	</c:choose>
	<open>${forecastrecord.bucketState == 'Y' ? 'true' : 'false'}</open>
	<c:forEach var="bucketrecord" items="${forecastrecord.bucketColl}">
		<c:if test="${bucketrecord.headerData != 'bucket'}">
			<c:choose>
				<c:when test="${bucketrecord.headerData == 'date'}">
					<c:choose>
						<c:when test="${fn:contains(bucketrecord.balanceDate, 'Total')}">
							<date isopen="true" clickable="false" haschildren="true"
								  holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">${bucketrecord.balanceDate}</date>
						</c:when>
						<c:otherwise>
							<date isopen="false" clickable="false" haschildren="false"
								  holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">${bucketrecord.balanceDate}</date>
						</c:otherwise>
					</c:choose>
				</c:when>
				<c:otherwise>
					<c:choose>
						<c:when test="${fn:contains(bucketrecord.balanceDate, 'Total')}">
							<${bucketrecord.headerData} bold="true" negative="${bucketrecord.predBalanceNegative == true ? 'true' : 'false'}"
							holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">${bucketrecord.balanceAsString}</${bucketrecord.headerData}>
						</c:when>
						<c:otherwise>
							<${bucketrecord.headerData} bold="false" negative="${bucketrecord.predBalanceNegative == true ? 'true' : 'false'}"
							holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">${bucketrecord.balanceAsString}</${bucketrecord.headerData}>
						</c:otherwise>
					</c:choose>
				</c:otherwise>
			</c:choose>
		</c:if>
		<c:if test="${bucketrecord.headerData == 'bucket'}">
			<${bucketrecord.headerData} clickable="false">${bucketrecord.bucketId}</${bucketrecord.headerData}>
		</c:if>
	</c:forEach>
	</row>
</c:forEach>

<row visible="true">
<c:if test="${record != null && !empty record}">
	<expand clickable="false" opened="true" content="Y"></expand>
	<open>true</open>
</c:if>
<c:forEach var="forecastrecordItem" items="${record}">
	<c:choose>
		<c:when test="${forecastrecordItem.headerData == 'date'}">
			<${forecastrecordItem.headerData} haschildren="true" bold="true"
			negative="${forecastrecordItem.predBalanceNegative == true ? 'true' : 'false'}"
			holiday="${forecastrecordItem.holidayFlag != 'N' ? 'true' : 'false'}">Grand total</${forecastrecordItem.headerData}>
		</c:when>
		<c:otherwise>
			<${forecastrecordItem.headerData} haschildren="false" bold="true"
			negative="${forecastrecordItem.predBalanceNegative == true ? 'true' : 'false'}"
			holiday="${forecastrecordItem.holidayFlag != 'N' ? 'true' : 'false'}">${forecastrecordItem.balanceAsString}</${forecastrecordItem.headerData}>
		</c:otherwise>
	</c:choose>
</c:forEach>
</row>
</rows>
</grid>

<gridSub>
<metadata>
	<columns>
		<column
				heading=""
				draggable="false"
				filterable="false"
				dataelement="maingroup"
				width="30"
				columntype="main"
				sort="false"
				type="str"
				bold="true"
				visible="false"
				clickable="true"
		/>
		<c:set var="record" value="${requestScope.forecastMonitor.grandTotalRecords}"/>
		<c:forEach var="forecastrecord" items="${requestScope.forecastMonitor.grandTotalRecords}">
			<c:if test="${forecastrecord.header != 'Bucket'}">
				<c:if test="${forecastrecord.gridType == 'sub'}">
					<c:choose>
						<c:when test="${fn:contains(forecastrecord.headerData, 'total')}">
							<column
									heading="${forecastrecord.header}"
									draggable="false"
									filterable="false"
									editable="<c:choose>
                                            <c:when test="${requestScope.menuAccess == '1'}">false</c:when>
                                            <c:otherwise>
                                                <c:choose>
                                                    <c:when test="${forecastrecord.header == 'Scenario'}">true</c:when>
                                                    <c:otherwise>false</c:otherwise>
                                                </c:choose>
                                            </c:otherwise>
                                        </c:choose>"
									columntype="${forecastrecord.gridType}"
									type="num"
									dataelement="${forecastrecord.headerData}"
									width="<c:choose>
                                            <c:when test="${fn:contains(forecastrecord.header, 'Date')}">90</c:when>
                                            <c:otherwise>
                                                <c:choose>
                                                    <c:when test="${forecastrecord.gridType == 'sub'}">${requestScope.sub_width}</c:when>
                                                    <c:otherwise>${requestScope.main_width}</c:otherwise>
                                                </c:choose>
                                            </c:otherwise>
                                        </c:choose>"
									holiday="false"
									bold="true"
									headerTooltip="${forecastrecord.headerTooltip}"
							/>
						</c:when>
						<c:otherwise>
							<column
									heading="${forecastrecord.header}"
									draggable="false"
									filterable="false"
									editable="<c:choose>
                                            <c:when test="${requestScope.menuAccess == '1'}">false</c:when>
                                            <c:otherwise>
                                                <c:choose>
                                                    <c:when test="${forecastrecord.header == 'Scenario'}">true</c:when>
                                                    <c:otherwise>false</c:otherwise>
                                                </c:choose>
                                            </c:otherwise>
                                        </c:choose>"
									columntype="${forecastrecord.gridType}"
									type="num"
									dataelement="${forecastrecord.headerData}"
									width="<c:choose>
                                            <c:when test="${fn:contains(forecastrecord.header, 'Date')}">90</c:when>
                                            <c:otherwise>
                                                <c:choose>
                                                    <c:when test="${forecastrecord.gridType == 'sub'}">${requestScope.sub_width}</c:when>
                                                    <c:otherwise>${requestScope.main_width}</c:otherwise>
                                                </c:choose>
                                            </c:otherwise>
                                        </c:choose>"
									holiday="false"
									maxChars="24"
									headerTooltip="${forecastrecord.headerTooltip}"
							/>
						</c:otherwise>
					</c:choose>
				</c:if>
			</c:if>
		</c:forEach>
		<column width="1"></column>
	</columns>
</metadata>
<rows size="${requestScope.recordCount}">
<% int i=0; %>
<c:forEach var="forecastrecord" items="${requestScope.forecastMonitor.forecastMonitorRecords}">
	<% i++; %>
	<c:choose>
		<c:when test="${forecastrecord.balanceDate == 'Total'}">
			<row visible="true">
			<rowId><%=i%></rowId>
			<maingroup>${forecastrecord.balanceDate}</maingroup>
		</c:when>
		<c:otherwise>
			<row visible="${forecastrecord.bucketState == 'Y' ? 'true' : 'false'}">
			<rowId><%=i%></rowId>
			<maingroup>${forecastrecord.balanceDate}</maingroup>
		</c:otherwise>
	</c:choose>
	<open>${forecastrecord.bucketState == 'Y' ? 'true' : 'false'}</open>
	<c:forEach var="bucketrecord" items="${forecastrecord.bucketColl}">
		<c:if test="${bucketrecord.headerData != 'bucket'}">
			<c:choose>
				<c:when test="${bucketrecord.headerData == 'date'}">
					<c:choose>
						<c:when test="${fn:contains(bucketrecord.balanceDate, 'Total')}">
							<${bucketrecord.headerData} bold="true" holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">
							${bucketrecord.balanceDate}
							</${bucketrecord.headerData}>
						</c:when>
						<c:otherwise>
							<${bucketrecord.headerData} bold="false" holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}">
							${bucketrecord.balanceDate}
							</${bucketrecord.headerData}>
						</c:otherwise>
					</c:choose>
				</c:when>
				<c:otherwise>
					<c:choose>
						<c:when test="${fn:contains(bucketrecord.balanceDate, 'Total')}">
							<${bucketrecord.headerData} bold="true"
							holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}"
							negative="${bucketrecord.predBalanceNegative == true ? 'true' : 'false'}">
							${bucketrecord.balanceAsString}
							</${bucketrecord.headerData}>
						</c:when>
						<c:otherwise>
							<${bucketrecord.headerData} bold="false"
							holiday="${bucketrecord.holidayFlag != 'N' ? 'true' : 'false'}"
							negative="${bucketrecord.predBalanceNegative == true ? 'true' : 'false'}"
							>
							${bucketrecord.balanceAsString}
							</${bucketrecord.headerData}>
						</c:otherwise>
					</c:choose>
				</c:otherwise>
			</c:choose>
		</c:if>
		<c:if test="${bucketrecord.headerData == 'bucket'}">
				<${bucketrecord.headerData}>
					${bucketrecord.bucketId}
			</${bucketrecord.headerData}>
		</c:if>
	</c:forEach>
	</row>
</c:forEach>
<row visible="true">
<c:if test="${record != null && !empty record}">
	<maingroup>Grand total</maingroup>
	<open>true</open>
</c:if>
<c:forEach var="forecastrecordItem" items="${record}">
	<c:choose>
		<c:when test="${forecastrecordItem.headerData == 'date'}">
			<${forecastrecordItem.headerData} bold="true"
			negative="${forecastrecordItem.predBalanceNegative == true ? 'true' : 'false'}"
			holiday="${forecastrecordItem.holidayFlag != 'N' ? 'true' : 'false'}">
			Grand total
			</${forecastrecordItem.headerData}>
		</c:when>
		<c:otherwise>
				<${forecastrecordItem.headerData} bold="true"
			negative="${forecastrecordItem.predBalanceNegative == true ? 'true' : 'false'}"
			holiday="${forecastrecordItem.holidayFlag != 'N' ? 'true' : 'false'}">
			${forecastrecordItem.balanceAsString}
			</${forecastrecordItem.headerData}>
		</c:otherwise>
	</c:choose>
</c:forEach>
</row>
</rows>
</gridSub>

	<selects>
	<select id="entity">
		<c:forEach var="entity" items="${requestScope.entities}">
			<c:choose>
				<c:when test="${requestScope.forecastMonitor.entityId == entity.value}">
					<option value="${entity.value}" selected="1">${entity.label}</option>
				</c:when>
				<c:otherwise>
					<option value="${entity.value}" selected="0">${entity.label}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	</select>
	<select id="currency">
		<c:forEach var="currency" items="${requestScope.currencyList}">
			<c:choose>
				<c:when test="${requestScope.forecastMonitor.currency == currency.value}">
					<option value="${currency.value}" selected="1">${currency.label}</option>
				</c:when>
				<c:otherwise>
					<option value="${currency.value}" selected="0">${currency.label}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	</select>
</selects>
</forecastmonitor>