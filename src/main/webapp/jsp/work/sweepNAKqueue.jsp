<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><fmt:message key="sweepNAKQueue.title.window"/></title>

<STYLE type="text/css">
<!--
A:link {text-decoration: none;}
A:visited {text-decoration: none;}
-->
</STYLE>
<SCRIPT language="JAVASCRIPT">
var appName = "<%=SwtUtil.appName%>";
var dateFormat = '${sessionScope.CDM.dateFormat}';
var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
var lastRefTime = "${requestScope.lastRefTime}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function bodyOnLoad(){

		xl = new XLSheet("sweepNAKQueueDetails","table_2", ["String", "String", "Number", "Number" ],"1111", "false" ,undefined,undefined,"ddscrolltableTotal");
		xl.onsort = xl.onfilter=updateColors;
		var dropBox1=new  SwSelectBox(document.forms[0].elements["sweepNAKQueue.entityId"],document.getElementById("entityName"));

		var dropBox2 = new SwSelectBox(document.forms[0].elements["sweepNAKQueue.currGrp"],document.getElementById("currencyName"));

		document.getElementById("lastRefTime").innerText = lastRefTime;

}

function submitForm(methodName,itemChanged){

		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
		document.forms[0].changedValue.value=itemChanged;
		document.forms[0].method.value=methodName;
		document.forms[0].submit();
	}

/**
*	This class is used to display the Sweep Exceptions Summary Details.
* 	@param source
*/
function clickLink(source, e){
	var event = (window.event || e);
	var target = (event.srcElement || event.target);
	var entityId = 	document.forms[0].elements["sweepNAKQueue.entityId"].value;
	var tdElement = target.parentElement;
	var trElement = target.parentElement.parentElement;
	var currencyCode = new String(trElement.cells[0].innerText).trim();
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	var arrtributString;
	requestURL=requestURL.substring(0,idy+1) ;
	requestURL = requestURL +appName +"/sweepNAKqueue.do?method=showMessages";
	// Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure".
	// Reduce the height.
	arrtributString="left=50,top=190,width=805,height=436,toolbar=0, resizable=yes, scrollbars=yes,status=yes";
	requestURL = requestURL + "&entityId=" + entityId;
	requestURL = requestURL + "&currencyCode=" + currencyCode;
	requestURL = requestURL + "&linkSource="+source;
    if(event.preventDefault) {
        event.preventDefault();
    }else{
        window.event.returnValue = false;
    }
	openWindow(requestURL,'bookMonitorJsp',arrtributString);
	return false;
}



</SCRIPT>
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad()" onunload="call()">
<form action="sweepNAKqueue.do" method="post" >

<input name="method" type="hidden" value="">
<input name="changedValue" type="hidden" value="">
<input name="linkSource" type="hidden" value="">




<div id="CurrencyExchange" style="z-index:99;position:absolute;left:20px;top:20px;width:655px;height:70px;border:2px outset;color="#7E97AF">
  <div id="CurrencyExchage" style="z-index:99;position:absolute;left:8px;top:4px;width:610px;height:90px" >
  <table width="630px" cellspacing="0" cellpadding="0" border="0" height="50px" >
    <tr height="25px">
    <td width="70px"><b style="width: 110px;"><fmt:message key="bookCode.entity"/></b></td>
    <td width="20px">&nbsp;</td>
	<td width="140px">
	  <select id="sweepNAKQueue.entityId" name="sweepNAKQueue.entityId" class="htmlTextAlpha"
						 style="width:140px" titleKey="tooltip.entityId" tabindex="1" onchange="javascript:submitForm('displayMessageQueue','entity');">
					  <c:forEach items="${requestScope.entities}" var="item">
						  <option value="${item.value}" <c:if test="${sweepNAKQueue.entityId == item.value}">selected</c:if> >
								  ${item.label}
						  </option>
					  </c:forEach>
					</select>
	  </td>
	   <td width="20">&nbsp;</td>
	  <td width="280">
		<span id="entityName" name="entityName" class="spantext">
	   </td>
	 </tr>
	  <tr height="25px">
    <td width="95px"><b style="width: 110px;"><fmt:message key="currMonitor.crrGrp"/></b></td>
    <td width="20px">&nbsp;</td>
		  <td width="140px">
			  <select id="sweepNAKQueue.currGrp" name="sweepNAKQueue.currGrp" class="htmlTextAlpha"
					  style="width:140px" titleKey="tooltip.entityId" tabindex="2" onchange=" javascript:submitForm('displayMessageQueue','currGrp');">
			  <c:forEach items="${requestScope.currencyGroupList}" var="item">
				  <option value="${item.value}"
						  <c:if test="${sweepNAKQueue.currGrp == item.value}">selected</c:if> >
						  ${item.label}
				  </option>
			  </c:forEach>
			  </select>
		  </td>
	   <td width="20">&nbsp;</td>
	  <td width="280">
		<span id="currencyName" name="currencyName" class="spantext">
	   </td>
	 </tr>

  </table>
  </div>
</div>


<div id="CurrencyMonitor" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:92px; width:655px; height:423px;z-index:99;">
<div id="CurrencyMonitor" style="position:absolute;z-index:99;left:0px; top:0px; width:634px; height:10px;">
<table  class="sort-table" id="table_2"  width ="630px"  bgcolor="#B0AFAF"  border="0" cellspacing="1" cellpadding="0"  >
	<thead>
		<tr height="20px">

			<td  width="100px" style="border-left-width: 0px;"align="center" title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="sweep.currencyCode"/></b></td>
			<td  width="330px" title='<fmt:message key="tooltip.sortCurrencyName"/>'   align="center" ><b><fmt:message key="matchQueue.CcyName"/></b></td>
			<td width="100px" title='<fmt:message key="tooltip.sortNAks"/>'  align="left"><b><fmt:message key="sweep.NAKs"/></b></td>

			<td width="100px" title='<fmt:message key="tooltip.sortNAks"/>'  align="left"><b><fmt:message key="sweepNAKQueue.overduetime"/></b></td>

		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:652px; height:418px;">
<div id="CurrencyMonitor" style="position:absolute;z-index:99;left:0px;top:21px; width:634px; height:10px;">

<table class="sort-table" id="sweepNAKQueueDetails" width ="630px"   border="0" cellspacing="1" cellpadding="0" height="380">
	<tbody>
	<%int count = 0; %>
	<c:forEach items="${requestScope.sweepNAKQueueDetails}" var='sweepNAKQueueDetails' >

		<% if( count%2 == 0 ) {

		%><tr  class="even"><% }else  {
			%> <tr class="odd"> <%}++count; %>

			<td width="100px" align="left"> ${sweepNAKQueueDetails.currencyCode}&nbsp;</td>
			<td width="330px" align="left">${sweepNAKQueueDetails.currencyName}&nbsp;</td>
			<td width="100px" align="center">
			<c:if test="${'false'==sweepNAKQueueDetails.zeroCount}">

				<A href="" onclick="clickLink('NAK',event)" >${sweepNAKQueueDetails.NAKs}&nbsp;

</c:if>

			<c:if test="${'true'==sweepNAKQueueDetails.zeroCount}">

				${sweepNAKQueueDetails.NAKs}&nbsp;

</c:if>
			</td>
			<td width="100px" align="center">
			<!-- Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure".  -->
			<c:if test="${'false'==sweepNAKQueueDetails.zeroCountOverdueACK}">

				<A tabindex="9" href="" onclick="clickLink('OverdueACK',event)" >${sweepNAKQueueDetails.overdueACKs}&nbsp;

</c:if>

			</td>

		</tr>
	</c:forEach>
	</tbody>
	<tfoot><tr><td colspan="4" ></td></tr></tfoot>
</table>
</div>
</div>
</div>



<!-- Start:  Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure".  -->
<div id="CurrencyMonitor" style="position:absolute; left:595; top:528; width:70; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		<td align="Right">
			<a tabindex="12" href=# onKeyDown="submitEnter(this,event)" onclick="javascript:openWindow(buildPrintURL('print','Sweep NAKs'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a>
		 </td>

			<td align="right" id="Print">
				<a tabindex="12" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:520; width:655px; height:39px; visibility:visible;">
	<div id="manual" style="position:absolute; left:6; top:4; width:220px; height:15px; visibility:visible;">
		 <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
				<td id="refreshbutton" width="70px">
				<a title='<fmt:message key="tooltip.refreshWindow"/>' tabindex="10"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('displayMessageQueue','currGrp');"><fmt:message key="button.Refresh"/></a>
				</td>

				<td width="70px">
				<a title='<fmt:message key="tooltip.close"/>' tabindex="10"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:window.close();"><fmt:message key="button.close"/></a>
				</td>
			</tr>
		</table>
	</div>
	<table height="33"><tr>
		<td id="lastRefTimeLable" width="440px" align="right" >
		<fmt:message key="label.lastRefTime"/>
		</td>
		<td id="lastRefTime" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">
		</td>
		</tr>
	</table>
	<div style="position:absolute; left:6; top:4; width:655px; height:15px; visibility:hidden;">
		<table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
			<tr>
			<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
		</td>
		</tr>
		</table>
	</div>
<!-- End:  Modified by venkat for Mantis 1434: "Sweep Exception Queue - overdue figure".  -->
</div>
</form>
</html>