<?xml version="1.0" encoding="UTF-8"?>
<%@page import="org.swallow.util.SwtUtil"%>
<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.OpTimer" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>




<intradayliquidity
		hrefresh="${requestScope.liquidityMonitorOptions.refreshRate}"
		withrefresh="${requestScope.liquidityMonitorOptions.withRefresh}"
		currencymultiplier="${requestScope.liquidityMonitorOptions.useCurrencyMultiplier}"
		lastRefTime="${requestScope.lastRefTime}"
		valueDate="${requestScope.sysDateAsString}"
		currencyFormat="${requestScope.currencyFormat}"
		lastUsedProfile="${requestScope.lastUsedProfile}">

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<option value="${entity.value}" ${requestScope.defaultEntityId == entity.value ? 'selected="1"' : 'selected="0"'}>
						${entity.label}
				</option>
			</c:forEach>
		</select>

		<select id="currency">
			<c:forEach items="${requestScope.currencies}" var="currency">
				<option value="${currency.value}" ${requestScope.defaultCcyId == currency.value ? 'selected="1"' : 'selected="0"'}>
						${currency.label}
				</option>
			</c:forEach>
		</select>

		<select id="profileList">
			<c:forEach items="${requestScope.profiles}" var="profile">
				<option value="<c:out value='${profile.value}'/>" ${requestScope.lastUsedProfile == profile.value ? 'selected="1"' : 'selected="0"'}>
					<c:out value="${profile.label}" />
				</option>
			</c:forEach>
		</select>
	</selects>

</intradayliquidity>