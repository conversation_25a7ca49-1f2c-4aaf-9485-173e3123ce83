<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<%@ include file="/taglib.jsp"%>
<title><fmt:message key="currencyMonitor.options.title" /></title>

<script type="text/javascript">
    var cancelcloseElements = new Array(1);
    cancelcloseElements[0] = "cancelbutton";
    <c:if test="${requestScope.parentFormRefresh == 'Y'}">
        window.opener.refreshPending = true;
        self.close();
    </c:if>

    function submitForm(methodName) {
        var defaultDays = validateField(document.forms[0].elements['centralMonitorOptions.numberOfDays'], 'centralMonitorOptions.numberOfDays', 'numberPat');
        if (defaultDays) {
            if (validateForm()) {
                document.forms[0].method.value = methodName;
                document.forms[0].submit();
            }
        } else {
            document.forms[0].elements['centralMonitorOptions.numberOfDays'].focus();
        }
    }

    function validateForm() {
        var defDays = document.forms[0].elements["centralMonitorOptions.numberOfDays"].value;
        if (defDays > 14 || defDays < 2) {
            alert('<fmt:message key="centralMonitor.alert.defDays" />');
            return false;
        } else {
            return true;
        }
    }

    function bodyOnLoad() {
        document.getElementById("currencyLimit").value = "${requestScope.currencyLimit != null ? requestScope.currencyLimit : ''}";
        document.getElementById("limitFrom").value = "${requestScope.limitFrom != null ? requestScope.limitFrom : ''}";
        document.getElementById("limitTo").value = "${requestScope.limitTo != null ? requestScope.limitTo : ''}";
        var menuAccessIdParent = getMenuAccessIdOfChildWindow("centralBankMonitor.do");
        if (menuAccessIdParent != "0") {
            document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
        }
    }

    window.onload = function () {
        ShowErrMsgWindow('${actionError}');
        bodyOnLoad();
        setParentChildsFocus();
        setTitleSuffix(document.forms[0]);
    }

    function closeWindow() {
        window.opener.callApp("Y");
        window.close();
    }
</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0" onunload="callParent()">
<form action="centralBankMonitor.do" method="post">
    <input name="method" type="hidden" value="">
    <div id="MonitorOptions" style="position: absolute; left: 20px; top: 20px; width: 470px; height: 165px; border: 2px outset;" color="#7E97AF">
        <div id="MonitorOptions" style="position: absolute; left: 8px; top: 4px; width: 470px; height: 100;">
            <table width="420" border="0" cellpadding="0" cellspacing="0" height="30">
                <tr height="24">
                    <td width="60%"><b><fmt:message key="ccyMonitorOptions.useCcyMultiplier" /></b></td>
                    <td width="28px">&nbsp;</td>
                    <td width="25px">
                        <input type="checkbox" name="centralMonitorOptions.useCurrencyMultiplier" value="Y"
                               ${requestScope.centralMonitorOptions.useCurrencyMultiplier == 'Y' ? 'checked' : ''} titleKey="ccyMonitorOptions.useCcyMultiplier">
                    </td>
                </tr>

                <tr height="24">
                    <td width="185px"><b><fmt:message key="ccyMonitorOptions.hideWeekends" /></b></td>
                    <td width="28px">&nbsp;</td>
                    <td width="25px">
                        <input type="checkbox" name="centralMonitorOptions.hideWeekends" value="Y"
                               ${requestScope.centralMonitorOptions.hideWeekends == 'Y' ? 'checked' : ''} titleKey="ccyMonitorOptions.hideWeekends">
                    </td>
                </tr>

                <tr height="24">
                    <td width="185px"><b><fmt:message key="ccyMonitorOptions.defaultDays" /></b>*</td>
                    <td width="28px">&nbsp;</td>
                    <td width="25px" align="Right">
                        <input type="text" name="centralMonitorOptions.numberOfDays" maxlength="2"
                               style="width:25px;" value="${requestScope.centralMonitorOptions.numberOfDays}"
                               onchange="return validateField(this,'centralMonitorOptions.numberOfDays','numberPat');" titleKey="ccyMonitorOptions.enterDefaultDays">
                    </td>
                </tr>

                <tr height="24">
                    <td width="185px"><b><fmt:message key="entity.crrLimit" /></b></td>
                    <td width="28px">&nbsp;</td>
                    <td width="25px" align="Right">
                        <input type="text" id="currencyLimit" disabled="disabled" size="20" style="background-color: #eeeeee;">
                    </td>
                </tr>

                <tr height="3">
                    <td width="10px"></td>
                </tr>

                <tr height="24">
                    <td width="185px"><label><b><fmt:message key="centralMonitorOptions.fontSize" /></b></label></td>
                    <td width="180px">&nbsp;</td>
                    <td width="150">
                        <input type="radio" id="1" style="width:13px;"
                               tabindex="7" name="centralMonitorOptions.fontSize" value="Normal"
                               ${requestScope.centralMonitorOptions.fontSize == 'Normal' ? 'checked' : ''} titleKey="tooltip.centralMonitorOptions.selectFontSizeNormal">
                        <label for="1"><fmt:message key="centralMonitorOptions.fontSizeNormal" />&nbsp;</label>
                        <input type="radio" id="2" style="width:13px;"
                               tabindex="8" name="centralMonitorOptions.fontSize" value="Small"
                               ${requestScope.centralMonitorOptions.fontSize == 'Small' ? 'checked' : ''} titleKey="tooltip.centralMonitorOptions.selectFontSizeSmall">
                        <label for="2"><fmt:message key="centralMonitorOptions.fontSizeSmall" />&nbsp;</label>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <div id="MonitorOptionsButtons" style="position: absolute; left: 390; top: 200; width: 70px; height: 15px; visibility: visible;">
        <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
            <tr>
                <td align="Right">
                    <a tabindex="7" href="#" onclick="javascript:openWindow(buildPrintURL('print','Monitor Options'),'monitoroptionswindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')">
                        <img src="images/help_default.GIF " name="Help" border="0" title="<fmt:message key='tooltip.helpScreen' />">
                    </a>
                </td>
                <td align="right" id="Print">
                    <a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)">
                        <img src="images/Print.gif " name="Print" border="0" title="<fmt:message key='tooltip.printScreen' />">
                    </a>
                </td>
            </tr>
        </table>
    </div>

    <div id="ddimagebuttons" color="#7E97AF" style="position: absolute; border: 2px outset; left: 20px; top: 190; width: 470; height: 39px; visibility: visible;">
        <div id="MonitorOptionsButtons" style="position: absolute; left: 6; top: 4; width: 280px; height: 15px; visibility: visible;">
            <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr>
                    <td id="savebutton" width="70px">
                        <a tabindex="9" title="<fmt:message key='tooltip.save' />" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitForm('saveCentralMonitorOptions')">
                            <fmt:message key="button.save" />
                        </a>
                    </td>
                    <td id="cancelbutton" width="70px">
                        <a title="<fmt:message key='tooltip.cancel' />" tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();">
                            <fmt:message key="button.cancel" />
                        </a>
                    </td>
                </tr>
            </table>
        </div>
        <div style="position: absolute; left: 6; top: 4; width: 470; height: 15px; visibility: hidden;">
            <table width="7" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility: hidden">
                <tr>
                    <td id="savedisablebutton">
                        <a class="disabled" disabled="disabled">
                            <fmt:message key="button.save" />
                        </a>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</form>
</body>
</html>

</html>
