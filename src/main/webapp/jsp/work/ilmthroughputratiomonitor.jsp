<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title><fmt:message key="ilmthroughput.title.window"/></title>
<!-- <base href="."> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "ILMThroughPutRatioMonitor";

var entityId = '${requestScope.entityId}';
var currencyId = '${requestScope.currencyId}';
var selectedScenario = '${requestScope.selectedScenario}';
var accountGroup = '${requestScope.accountGroup}';
var fromILMMonitor = '${requestScope.fromILMMonitor}';
var alertOrangeImage = "images/Alert/scenario/normal.png";
var alertRedImage = "images/Alert/scenario/critical.png";

var label = new Array ();
label["text"] = new Array ();
label["tip"] = new Array ();

label["text"]["entity"] = "<fmt:message key="message.entityId"/>";
label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityid"/>";

label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

label["text"]["button-close"] = "<fmt:message key="button.close"/>";
label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

label["text"]["button-options"] = "<fmt:message key="button.options"/>";
label["tip"]["button-options"] = "<fmt:message key="tooltip.options"/>";

function openChildWindow(sEntityId, sCurrencyCode, ilm_group, scenarioId, valueDate){
	var paramString="";
	paramString = paramString + "&entityId=" + sEntityId;
	paramString = paramString + "&currencyCode=" + sCurrencyCode;
	paramString = paramString + "&valueDate=" + valueDate;
	paramString = paramString + "&ilmGroup=" + ilm_group;
	paramString = paramString + "&scenarioId=" + scenarioId;
	
	
	var param = '/' + appName + '/ilmAnalysisMonitor.do?method=ilmThroughPutRatioMonitorBreakdown'+paramString;
	var 	mainWindow = openWindow (param, 'stopRuleAdd','left=10,top=230,width=1100,height=725,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	return false;
}

function report(entityId, ccyCombo, scenarioCombo, selectedDate,
		groupCombo, exportType, chartsData, entityName, currencyName,
		scenarioName, accountGroupName, calculateAs) {
	document.getElementById('exportDataForm').action = 'ilmAnalysisMonitor.do?method=ilmThrouputreport';
	document.getElementById('exportType').value = exportType;
	document.getElementById('entityId').value = entityId;
	document.getElementById('currencyCode').value = ccyCombo;
	document.getElementById('scenarioId').value = scenarioCombo;
	document.getElementById('chartsData').value = chartsData;
	document.getElementById('valueDate').value = selectedDate;
	document.getElementById('fromDate').value=""+selectedDate;
	document.getElementById('accountGroup').value = groupCombo;
	document.getElementById('accountGroupName').value = accountGroupName;
	document.getElementById('scenarioName').value = scenarioName;
	document.getElementById('ccyCodeName').value = currencyName;
	document.getElementById('entityName').value = entityName;
	document.getElementById('calculateAs').value=calculateAs;
	document.forms[0].submit();
}

/**
 * help
 * This function opens the help screen 
 * @return none
 */
function help(){
	openWindow(buildPrintURL('print','ILM ThroughPut Ratio Monitor'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
}


</script>
<%@ include file="/angularscripts.jsp"%>
	
	<form id="exportDataForm" method="post" enctype="multipart/form-data" target="tmp">
		<input name="exportType" id="exportType" type="hidden" value="">
		<input name="reportType" id="reportType" type="hidden" value="">
		<input name="entityId" id="entityId" type="hidden" value="">
		<input name="currencyCode" id="currencyCode" type="hidden" value="">
		<input  name="accountGroup" id="accountGroup" type="hidden" value="">
		<input  name="fromDate" id="fromDate" type="hidden" value="">
		<input  name="chartsData" id="chartsData" type="hidden" value="">
		<input name="scenarioId" id="scenarioId" type="hidden" value="">
		<input name="valueDate" id="valueDate" type="hidden" value="">
		<input name="useCcy" id="useCcy" type="hidden" value="">
		<input name="singleOrRange" id="singleOrRange" type="hidden" value="">
		<input name="scenarioName" id="scenarioName" type="hidden" value="">
		<input name="accountGroupName" id="accountGroupName" type="hidden" value="">
		<input name="ccyCodeName" id="ccyCodeName" type="hidden" value="">
		<input name="entityName" id="entityName" type="hidden" value="">
		<input name="calculateAs" id="calculateAs" type="hidden" value="">
		<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
		<input id="mybutton" type="submit" value="post" style="visibility: hidden;" />
		<iframe name="tmp" width="0%" height="0%" src="#" style="border-width: 0px; height: 0px;"></iframe>
	</form>
</body>
</html>
