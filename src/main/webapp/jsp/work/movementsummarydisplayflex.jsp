
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.PropertiesFileLoader"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<!-- <script type="text/javascript" src="js/movementsummary.js"></script> -->
<script language="javascript">
	var screenRoute = "msd";
	var appName = "<%=SwtUtil.appName%>";
	var alertOrangeImage = "images/Alert/scenario/normal.png";
	var alertRedImage = "images/Alert/scenario/critical.png";
	var blankImage = "images/blank.png";
	var currencyFormat = '${sessionScope.CDM.currencyFormat}';
	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	var tabPressed="${requestScope.date}";
	var method = "${requestScope.method}";
	var currencyId="${requestScope.currencyCode}";
	var filterstatus= "${requestScope.filterStatus}";
	var sortStatus="${requestScope.sortStatus}";
	var sortDescending="${requestScope.sortDescending}";
	var nextMovIdFocus="${requestScope.nextMovIdFocus}";
	var initialscreen = "${requestScope.initialinputscreen}";
	var isRefresfFromChild = "false";
	var dateStr = '${requestScope.date}';
	var maxPage='${requestScope.maxPage}';
	var currPage = '${requestScope.currentPage}';
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1) ;
	var isDoNotCloseMyChilds = false;
	var currentFilter="${requestScope.selectedFilter}";
	var currentSort = "${requestScope.selectedSort}";
	var filterValues=new Array();
	var calledFromNext;
	var selectedMvmtList="${requestScope.selectedList}";
	var menuAccessId="${requestScope.menuAccessId}"
	var posLvlId = '${posLvlId}';
	var workflow = "${requestScope.workflow}";
	var scenarioId = "${requestScope.scenarioId}";
	var currGrp = "${requestScope.currGrp}";
	var profileId= "${requestScope.profileId}";
	var msdDisplayColsList=  "${requestScope.msdDisplayColsList}";
	var multiMvtUpdateNbr=  "${requestScope.multiMvtUpdateNbr}";
	var posLevelList=  "${requestScope.posLevelList}";
	var sourcelList=  "${requestScope.sourceList}";
	var formatList=  "${requestScope.formatList}";
	var entityList=  "${requestScope.entityList}";
	var currentFilterValues = currentFilter.split("|");
	var sortingValues = currentSort.split("|");
	var applyCurrencyThreshold = '${requestScope.applyCurrencyThreshold}';

	/* COMMONN METHOD NAME FOR MSD */
	var sysDateFormat =  "<%=SwtUtil.getCurrentDateFormat(null)%>";
	var outstandingMethodName = '${requestScope.outstandingMethodName}';
	var methodName = '${requestScope.method}';
	var currentPage = '';
	var pageNoValue = '';

	var selectedFilterSearch = '${requestScope.selectedFilter}';
	var selectedSortSearch = '${requestScope.selectedSort}';
	var archiveIdSearch = '${requestScope.archiveId}';
	var filterFromSerachSearch = '${requestScope.filterFromSerach}';
	var filterCriteriaSearch = '${requestScope.filterCriteria}';
	var applyCurrencyThresholdSearch = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndSearch = '${requestScope.applyCurrencyThresholdInd}';
	var entityIdSearch = '${requestScope.entityId}';
	var matchStatusSearch = '${requestScope.matchStatus}';
	var movementTypeSearch = '${requestScope.movementType}';
	var signSearch = '${requestScope.sign}';
	var predictStatusSearch = '${requestScope.predictStatus}';
	/* get the External Balance Status from Request. store as Global variable, then call this variable from Mxml.  */
	var extBalStatusSearch = '${requestScope.extBalStatus}';
	var amountoverSearch = '${requestScope.amountover}';
	var amountunderSearch = '${requestScope.amountunder}';
	var currencyCode= '${requestScope.currencyCode}';
	var initialinputscreenSearch = '${requestScope.initialinputscreen}';
	var beneficiaryIdSearch = '${requestScope.beneficiaryId}';
	var custodianIdSearch = '${requestScope.custodianId}';
	var accountIdSearch = '${requestScope.accountId}';
	var groupSearch = '${requestScope.group}';
	var metaGroupSearch = '${requestScope.metaGroup}';
	var bookCodeSearch = '${requestScope.bookCode}';
	var fintradeSearch = '${requestScope.fintrade}';
	var accountClassSearch = '${requestScope.accountClass}';
	var positionlevelSearch = '${requestScope.positionlevel}';
	var valueFromDateAsStringSearch = '${requestScope.valueFromDateAsString}';
	var valueToDateAsStringSearch = '${requestScope.valueToDateAsString}';
	var timefromSearch = '${requestScope.timefrom}';
	var timetoSearch = '${requestScope.timeto}';
	var referenceSearch = '${requestScope.reference}';
	var messageIdSearch = '${requestScope.messageId}';
	var inputDateAsStringSearch = '${requestScope.inputDateAsString}';
	var counterPartyIdSearch = '${requestScope.counterPartyId}';
	var isAmountDifferSearch = '${requestScope.isAmountDiffer}';
	var selectedMovementsAmountSearch = '${requestScope.selectedMovementsAmount}';
	var referenceFlagSearch = '${requestScope.referenceFlag}';
	var openFlagSearch='${requestScope.openMovementFlag}';
	var openFlag = '${requestScope.openFlag}';
	var matchingparty = '${requestScope.matchingparty}';
	var producttype = '${requestScope.producttype}';
	var uetr = '${requestScope.uetr}';
	var postingDateFrom = '${requestScope.postingDateFrom}';
	var postingDateTo = '${requestScope.postingDateTo}';
	var entityIdOpenMovements = '${requestScope.entityId}';
	var dateOpenMovements = '${requestScope.date}';
	var selectedListOpenMovements = '${requestScope.selectedList}';
	var posLvlId = '${requestScope.posLvlId}';
	var totalFlagOpenMovements = '${requestScope.totalFlag}';
	var selectedFilterOpenMovements = '${requestScope.selectedFilter}';
	var selectedSortOpenMovements = '${requestScope.selectedSort}';
	currentPage = '${requestScope.currentPage}';
	pageNoValue = '${requestScope.pageNoValue}';
	var exportMaxPages = '${requestScope.exportMaxPages}';

	var refreshFromMatchScreenOpenMovements = '${requestScope.refreshFromMatchScreen}';
	var initialinputscreenOpenMovements = '${requestScope.initialinputscreen}';
	var applyCurrencyThresholdOpenMovements = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndOpenMovements = '${requestScope.applyCurrencyThresholdInd}';
	var tableScrollbarLeftOpenMovements = '${requestScope.tableScrollbarLeft}';
	var tableScrollbarTopOpenMovements = '${requestScope.tableScrollbarTop}';


	/* FROM BOOK MONITOR DETAILS */
	var entityIdBook = '${requestScope.entityId}';
	var valueDate = '${requestScope.valueDate}';
	var bookCodeBook = '${requestScope.bookCode}';
	var selectedTabIndexBook = '${requestScope.selectedTabIndex}';
	var initialinputscreenBook = '${requestScope.initialinputscreen}';
	var selectedFilterBook = '${requestScope.selectedFilter}';
	var selectedSortBook = '${requestScope.selectedSort}';
	currentPage = '${requestScope.currentPage}';
	pageNoValue = '${requestScope.pageNoValue}';
	var maxPageBook = '${requestScope.maxPage}';
	var applyCurrencyThresholdBook = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndBook = '${requestScope.applyCurrencyThresholdInd}';
	/* END FROM BOOK MONITOR DETAILS */

	/* FROM CURRENCY MONITOR DETAILS */
	var entityIdCurrency = '${requestScope.entityId}';
	var initialinputscreenCurrency = '${requestScope.initialinputscreen}';
	var selectedFilterCurrency = '${requestScope.selectedFilter}';
	var locationIdCurrency = '${requestScope.locationId}';
	var selectedSortCurrency = '${requestScope.selectedSort}';
	var currentPageCurrency = '${requestScope.currentPage}';
	var pageNoValueCurrency = '${requestScope.pageNoValue}';
	var maxPageCurrency = '${requestScope.maxPage}';
	var applyCurrencyThresholdCurrency = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndCurrency = '${requestScope.applyCurrencyThresholdInd}';
	/* END FROM CURRENCY MONITOR DETAILS

    /* FROM ACCOUNT MONITOR DETAILS */
	var entityIdAccount= '${requestScope.entityId}';
	var initialinputscreenAccount= '${requestScope.initialinputscreen}';
	var selectedFilterAccount= '${requestScope.selectedFilter}';
	var accountIdAccount= '${requestScope.accountId}';
	var balanceTypeAccount = '${requestScope.balanceType}';
	var selectedSortAccount= '${requestScope.selectedSort}';
	var currentPageAccount= '${requestScope.currentPage}';
	var pageNoValueAccount= '${requestScope.pageNoValue}';
	var maxPageAccount= '${requestScope.maxPage}';
	var applyCurrencyThresholdAccount= '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndAccount= '${requestScope.applyCurrencyThresholdInd}';
	var accountTypeAccount= '${requestScope.accountType}';
	var bookCodeAccount= '${requestScope.bookCode}';
	var selectedTabIndexAccount= '${requestScope.selectedTabIndex}';
	/* END FRON ACCOUNT MONITOR DETAILS */

	/* FROM WORKFLOW UNSETTELEDYESTERDAY MONITOR DETAILS */
	var entityIdUnsetteled = '${requestScope.entityId}';
	var currGrp = '${requestScope.currGrp}';
	var selectedFilterUnsetteled = '${requestScope.selectedFilter}';
	var selectedSortUnsetteled = '${requestScope.selectedSort}';
	var currentPageUnsetteled = '${requestScope.currentPage}';
	var pageNoValueUnsetteled = '${requestScope.pageNoValue}';
	var maxPageUnsetteled = '${requestScope.maxPage}';
	var applyCurrencyThresholdUnsetteled = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndUnsetteled = '${requestScope.applyCurrencyThresholdInd}';
	var roleId = '${requestScope.roleId}';
	var totalCountUnsetteled = '${requestScope.totalCount}';
	/*END FROM WORKFLOW UNSETTELEDYESTERDAY MONITOR DETAILS */


	/* FROM WORKFLOWMONITOR UNEXPECTED DETAILS */
	var entityIdUnexpected = '${requestScope.entityId}';
	var selectedFilterUnexpected = '${requestScope.selectedFilter}';
	var selectedSortUnexpected = '${requestScope.selectedSort}';
	var currentPageUnexpected = '${requestScope.currentPage}';
	var pageNoValueUnexpected = '${requestScope.pageNoValue}';
	var maxPageUnexpected = '${requestScope.maxPage}';
	var applyCurrencyThresholdUnexpected = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndUnexpected = '${requestScope.applyCurrencyThresholdInd}';
	var totalCountUnexpected = '${requestScope.totalCount}';
	/*END FROM WORKFLOWMONITOR UNEXPECTED  DETAILS */

	/* FROM WORKFLOWMONITOR BACKVALUE DETAILS */
	var entityIdBackvalue = '${requestScope.entityId}';
	var selectedFilterBackvalue = '${requestScope.selectedFilter}';
	var selectedSortBackvalue = '${requestScope.selectedSort}';
	var currentPageBackvalue = '${requestScope.currentPage}';
	var pageNoValueBackvalue = '${requestScope.pageNoValue}';
	var maxPageBackvalue = '${requestScope.maxPage}';
	var applyCurrencyThresholdBackvalue = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndBackvalue = '${requestScope.applyCurrencyThresholdInd}';
	var totalCountBackvalue = '${requestScope.totalCount}';
	/*END FROM WORKFLOWMONITOR BACKVALUE  DETAILS */


	/* FROM WORKFLOWMONITOR WORKFLOW DETAILS */
	var entityIdWorkflow = '${requestScope.entityId}';
	var tabIndicatorWorkflow = '${requestScope.tabIndicator}';
	var selectedFilterWorkflow = '${requestScope.selectedFilter}';
	var selectedSortWorkflow = '${requestScope.selectedSort}';
	var currentPageWorkflow = '${requestScope.currentPage}';
	var pageNoValueWorkflow = '${requestScope.pageNoValue}';
	var maxPageWorkflow = '${requestScope.maxPage}';
	var applyCurrencyThresholdWorkflow = '${requestScope.applyCurrencyThreshold}';
	var applyCurrencyThresholdIndWorkflow = '${requestScope.applyCurrencyThresholdInd}';
	var totalCountWorkflow = '${requestScope.totalCount}';
	var predictStatusWorkflow = '${requestScope.predictStatus}';
	var linkFlagWorkflow = '${requestScope.linkFlag}';
	var matchStatusWorkflow = '${requestScope.matchStatus}';
	var totalFlag = '${requestScope.totalFlag}';
	//Added for Mantis 5843
	var extraFilter = '${requestScope.extraFilter}';

	/*END FROM WORKFLOWMONITOR WORKFLOW  DETAILS */

	var screenTitleDrillDown = "";
	if(("${requestScope.balanceType}" != 'undefined' && "${requestScope.balanceType}" != null && "${requestScope.balanceType}" != "") ){

		if("${requestScope.balanceType}" == "Predicted") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitlePredicted"/>";
		}else if("${requestScope.balanceType}" == "Unsettled") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitleUnsettled"/>";
		}else if("${requestScope.balanceType}" == "Unexpected") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitleUnexpected"/>";
		}else if("${requestScope.balanceType}" == "Loro") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitleLoro"/>";
		}else if("${requestScope.balanceType}" == "External") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitleExternal"/>";
		}else if("${requestScope.balanceType}" == "OpenUnexpected") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitleOpenUnexpected"/>";
		}
		document.title = ("<fmt:message key="movementSummDisplay.title.Window"/>".replace("{0}", "("+screenTitleDrillDown+")"));
	}else {
		if("${requestScope.initialinputscreen}" == "currencymonitor"  || "${requestScope.initialinputscreen}" == "entitymonitor") {
			screenTitleDrillDown = "<fmt:message key="movementSummaryDisplay.drilldownTitlePredicted"/>";
			document.title = ("<fmt:message key="movementSummDisplay.title.Window"/>".replace("{0}", "("+screenTitleDrillDown+")"));
		}
		else
			document.title = "<fmt:message key="movementSummDisplay.title.Window"/>".replace("{0}", "");

	}

	var msdPageSize = "<%=SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE)%>";
	var msdMaxPageSize = "<%=PropertiesFileLoader.getInstance()
    .getPropertiesValue(SwtConstants.EXPORT_MAX_PAGE_SIZE)%>";
	var authRefreshFlag = false;


	function getCurrencyDecimal(currCode,amount){
		var decimals = getMenuWindow().CurrencyDecimalMetaData[currCode];
		if(decimals != 'undefined' && decimals != null){
		}else{
			decimals = "2";
		}

		var length = amount.length;
		var part1 =  amount.substring(0,length - strDecimals);
		var part2 = amount.substring(length - strDecimals);
		amount = part1 + "." + part2 ;
		return amount;
	}

	var itemId = "<%=request.getAttribute("screenId")%>";
	var hostId = "<%=request.getAttribute("hostId")%>";
	var userId = "<%=request.getAttribute("userId")%>";
	var currentFontSize = "<%=request.getAttribute("fontSize")%>";

	var label = new Array ();
	label["text"] = new Array ();
	label["tip"] = new Array ();

	label["text"]["entity"] = "<fmt:message key="message.entityId"/>";
	label["tip"]["entity"] = "<fmt:message key="tooltip.selectEntityid"/>";

	label["text"]["threshold"] = "<fmt:message key="mvmt.applyCurrencyThreshold"/>";
	label["tip"]["threshold"] = "<fmt:message key="tooltip.applyCurrencyThreshold"/>";

	label["text"]["button-refresh"] = "<fmt:message key="button.Refresh"/>";
	label["tip"]["button-refresh"] = "<fmt:message key="tooltip.refreshWindow"/>";

	label["text"]["button-movement"] = "<fmt:message key="button.mvmnt"/>";
	label["tip"]["button-movement"] = "<fmt:message key="tooltip.showSelMovDetail"/>";

	label["text"]["button-notes"] = "<fmt:message key="button.notes"/>";
	label["tip"]["button-notes"] = "<fmt:message key="tooltip.mvmntnotes"/>";

	label["text"]["button-message"] = "<fmt:message key="button.message"/>";
	label["tip"]["button-message"] = "<fmt:message key="tooltip.msgSelMvm"/>";

	label["text"]["button-filter"] = "<fmt:message key="button.Filter"/>";
	label["tip"]["button-filter"] = "<fmt:message key="tooltip.srchmvmnt"/>";

	label["text"]["button-addCols"] = "<fmt:message key="button.addCols"/>";
	label["tip"]["button-addCols"] = "<fmt:message key="tooltip.addCols"/>";

	label["text"]["button-match"] = "<fmt:message key="button.match"/>";
	label["tip"]["button-makeOfferedMatch"] = "<fmt:message key="tooltip.makeOfferedMatch"/>";
	label["tip"]["button-displayMatch"] = "<fmt:message key="tooltip.displayMatch"/>";

	label["text"]["button-suspend"] = "<fmt:message key="button.suspend"/>";
	label["tip"]["button-suspend"] = "<fmt:message key="tooltip.suspMatch"/>";

	label["text"]["button-confirm"] = "<fmt:message key="button.confirm"/>";
	label["tip"]["button-confirm"] = "<fmt:message key="tooltip.ConfMatch"/>";

	label["text"]["button-recon"] = "<fmt:message key="button.reconcile"/>";
	label["tip"]["button-recon"] = "<fmt:message key="tooltip.reconMatch"/>";

	label["text"]["button-remove"] = "<fmt:message key="button.remove"/>";
	label["tip"]["button-remove"] = "<fmt:message key="button.remove"/>";

	label["text"]["button-close"] = "<fmt:message key="button.close"/>";
	label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";

	label["text"]["button-options"] = "<fmt:message key="button.options"/>";
	label["tip"]["button-options"] = "<fmt:message key="tooltip.options"/>";


	label["text"]["button-update"] = "<fmt:message key="button.update"/>";
	label["tip"]["button-update"] = "<fmt:message key="tooltip.update"/>";

	label["text"]["label-movementCannotBeUnlocked"] = "<fmt:message key="label.movementCannotBeUnlocked"/>";
	label["text"]["label-movementSummaryDisplay"] = "<fmt:message key="label.movementSummaryDisplay"/>";
	label["text"]["label-selectedOnlyMatchedItems"] = "<fmt:message key="label.selectedOnlyMatchedItems"/>";
	label["text"]["label-amountsDiffer"] = "<fmt:message key="label.amountsDiffer"/>";
	label["text"]["label-movementChanged"] = "<fmt:message key="label.movementChanged"/>";
	label["text"]["label-difference"] = "<fmt:message key="label.difference"/>";
	label["text"]["label-of"] = "<fmt:message key="genericDisplayMonitor.labelOf"/>";
	label["text"]["label-selectedOnlyMatchedItems"] = "<fmt:message key="label.selectedOnlyMatchedItems"/>";
	label["text"]["label-maxNumberPages"] = "<fmt:message key="label.maxNumberPages"/>";
	label["text"]["label-serverHasRunOutOfMemory"] = "<fmt:message key="label.serverHasRunOutOfMemory"/>";
	label["text"]["label-amountSelectedMovementsDiffer"] = "<fmt:message key="label.amountSelectedMovementsDiffer"/>";
	label["text"]["label-matchingMovementWillChange"] = "<fmt:message key="label.matchingMovementWillChange"/>";
	label["text"]["label-movement"] = "<fmt:message key="label.entityMonitor.movementId"/>";
	label["text"]["label-isInBusyBy"] = "<fmt:message key="label.isInBusyBy"/>";

	label["text"]["label-ConnectionError"] = "<fmt:message key="label.ConnectionError"/>";
	label["text"]["label-difference"] = "<fmt:message key="label.difference"/>";

	label["text"]["label-total-selected"] = "<fmt:message key="label.totalSelected"/>";
	label["text"]["label-total-in-page"] = "<fmt:message key="label.totalInPages"/>";
	label["text"]["label-total-over-pages"] = "<fmt:message key="label.labelTotalOverPages"/>";
	label["text"]["alert-contactAdmin"] = "<fmt:message key="alert.movementsummaryWhileExport"/>";
	label["text"]["label-noneFilter"] = "<fmt:message key="msd.noneFilter"/>";
	label["text"]["label-adhocFilter"] = "<fmt:message key="msd.adhocFilter"/>";

	label["text"]["label-filterName"] = "<fmt:message key="msd.title.filterName"/>";

	label["text"]["label-currentFilter"] = "<fmt:message key="label.currenctFilter"/>";
	label["text"]["alert-deletefilter"] = "<fmt:message key="msd.alertDeleteProfile"/>";
	label["text"]["alert-overwritefilter"] = "<fmt:message key="msd.alertOverwriteProfile"/>";
	label["text"]["alert-filtersaved"] = "<fmt:message key="msd.alertFilterSaved"/>";
	label["text"]["alert-filterdeleted"] = "<fmt:message key="msd.alertFilterDeleted"/>";
	label["tip"]["combo-filter"] = "<fmt:message key="msd.filterComboTooltip"/>";
	label["tip"]["combo-deletefilter"] = "<fmt:message key="msd.deleteFilterImageTooltip"/>";
	label["tip"]["combo-savefilter"] = "<fmt:message key="msd.saveFilterImageTooltip"/>";
	label["tip"]["combo-reloadfilter"] = "<fmt:message key="msd.reloadFilterImageTooltip"/>";

	label["tip"]["combo-saveAs"] = "<fmt:message key="ilmanalysismonitor.saveAsComboprofilePopupTooltip"/>";
	label["text"]["button-saveProfile"] = "<fmt:message key="ilmanalysismonitor.saveAsButtonprofilePopupLabel"/>";
	label["text"]["alert-fillMandatoryFields"] = "<fmt:message key="msd.alertFillMandatoryFields"/>";

	label["text"]["radio-searchDateBehaviour"] = "<fmt:message key="msd.dateradio.label"/>";
	label["text"]["radio-fixed"] = "<fmt:message key="msd.dateradio.option1.text"/>";
	label["tip"]["radio-fixed"] = "<fmt:message key="msd.dateradio.option1.tooltip"/>";
	label["text"]["radio-relative"] = "<fmt:message key="msd.dateradio.option2.text"/>";
	label["tip"]["radio-relative"] = "<fmt:message key="msd.dateradio.option2.tooltip"/>";


	var sortedValues = new Array();
	sortedValues[0] = sortingValues[0];
	sortedValues[1] = sortingValues[1];

	var amountDifference = 0;
	var totalCount = '${totalCount}';
	<c:if test="${'yes'==requestScope.parentFormRefresh}">


	window.opener.document.forms[0].method.value="displayOpenMovementCountByCurrency";
	window.opener.isRefresfFromChild = "true";
	window.opener.document.forms[0].submit();


	</c:if>

	var currentUser="<%=SwtUtil.getCurrentUserId(request.getSession())%>";

	function checkLockOnServer(movementId)
	{
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementLock.do?method=checkLock";
		sURL = sURL + "&movementId="+movementId;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}


	/**
	 * This function is used to display the Movement Display screen
	 *@param methodName
	 *@param movementId
	 *@param entityid
	 *@param isNotesPresent
	 *return boolean
	 *
	 */
	function showMvmnt(methodName,movementId,entityId,isNotesPresent){
		//variable declared for menuAccessIdOfChildWindow
		var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
		//variable declared for menuName
		var menuName = new String('<fmt:message key="mvmDisplay.title.window"/>');
		//variable declared for smrtPredPos
		var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
		menuName = menuName.substr(0,smrtPredPos-3);
		//access level check
		if (menuAccessIdOfChildWindow == 2) {
			alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');
		} else {
			//to open Movement display screen
			var movId = movementId;
			var param = 'movement.do?method='+methodName;
			param += '&entityCode=' + entityId;
			param += '&movementId=' + movId;
			param += '&archiveId=${archiveId}';
			param += '&isNotesPresent='+ isNotesPresent;
			param += '&menuAccessId='+menuAccessIdOfChildWindow;
			//Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
			param += '&initialInputScreen='+'${requestScope.initialinputscreen}';
			openWindow(param,'movementWindow','left=50,top=190,width=994,height=789,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
		}
		return false;
	}

	function getSelectedMovementId(){

		var tableId = "OutStandingMovementSummaryDetails";
		var checkCondition = false;
		<c:if test="${'search'==requestScope.method}">

		<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

		checkCondition = true;
		tableId = "selectedMovementSummaryDetails";

		</c:if>
		<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

		checkCondition = true;
		tableId = "selectedMovementSummaryDetails";

		</c:if>

		</c:if>

		<c:if test="${'search'!=requestScope.method}">

		checkCondition = true;
		tableId = "selectedMovementSummaryDetails";

		</c:if>

		var table = document.getElementById(tableId);
		var tbody = table.getElementsByTagName("tbody")[0];
		var rows = tbody.getElementsByTagName("tr");
		for (i=0; i < rows.length; i++)
		{
			if( checkCondition || isRowSelected(rows[i])){
				document.forms[0].selectedMovementId.value = rows[i].cells[18].innerText;

			}
		}
	}
	/**
	 * This method is used to search the movement from Movement Summary Display.
	 *
	 * @param methodName
	 * @param entityId
	 * @param currencyId
	 * @param currentFilter
	 * @param currentSort
	 * @param posLvlId
	 * @param workflow
	 * @param filterParam
	 * @return none
	 */

	function searchmovements(methodName,entityId,currencyId,currentFilter,currentSort,posLvlId,workflow,filterParam){
		var param = 'outstandingmovement.do?method='+methodName;
		param += '&entityCode=' + entityId;
		param += '&currencyCode=' + currencyId;
		param += '&selectedTab=' + dateStr;
		param += '&selectedFilter=' + currentFilter ;
		param += '&selectedSort=' + currentSort;
		param+= '&posLvlId=' + posLvlId;
		param += '&workflow=' + workflow;
		param += filterParam;
		openWindow(param,'SearchWindow','left=50,top=190,width=640,height=390,toolbar=0, resizable=yes, scrollbars=no','true');
	}
	/*
	* This function is used to open Movement notes screen
	* @param mname
	* @param movementId
	* @param isNotesPresent
	*/
	function movementNotesFromSearch(mname,movementId,isNotesPresent){
		//framing url for sending reguest
		var param = 'notes.do?method='+mname+'&movId=';
		param += movementId;
		param += '&entityCode=' + "${requestScope.entityId}";
		param += '&screenName='+'searchParams';
		param += '&isNotesPresent='+isNotesPresent;
		param += '&currencyCode='+currencyId;
		param += '&date='+dateStr;
		param += '&archiveId=${archiveId}';
		param += '&currencyAccess='+menuEntityCurrGrpAccess;
		//check the lock of movement id if the method name is search
		if(methodName == "search"){
			var disBtnLock = checkLockOnServer(movementId);
			if (disBtnLock != "true")
				disBtnLock  = "Y";
			else
				disBtnLock  = "N";
			param += '&disBtnLock='+disBtnLock;
		}
		//function to new window, i.e., Movement notes
		openWindow(param,'NotesWindow','left=50,top=190,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes','true');
	}

	function lockMovementOnServer(movementId){
		var oXMLHTTP = new XMLHttpRequest();

		var sURL = requestURL + appName+"/movementLock.do?method=lockMovement";

		sURL = sURL + "&movementId="+movementId;

		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}

	function checkMovementStatus(movementIds,entityId){
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movement.do?method=checkMovementStatus";
		sURL = sURL + "&movementIds="+movementIds+"&entityId="+entityId;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}


	function expandAmtDifference(amtDifference,currencyFormat,currencyCode){
		return expandCurrencyAmountFlex(amtDifference, currencyFormat, currencyCode);
	}
	function unlockMovementOnServer(movementId){
		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movementLock.do?method=unlockMovement";

		sURL = sURL + "&movementId="+movementId;


		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}

	window.addEventListener("message", function (event) {
		// Optionally check origin with: event.origin === 'http://localhost:8088'
		if (event.data && event.data.type === 'callbackApp') {
			CallBackApp(); // Or just CallBackApp() if no data needed
		}
	});


	function accountAccessConfirm(movementId,entity){

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/accountAccess.do?method=acctAccessConfirmMSD";
		sURL = sURL + "&movementId="+movementId;
		sURL = sURL + "&entityId="+entity;
		sURL = sURL + "&status=Matching";
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();
		var str=oXMLHTTP.responseText;
		return str;
	}
	function sendParams(params) {

		for (var key in params) {
			if (params.hasOwnProperty(key)) {
				try {
					if(key != "nocache")
						document.getElementById(key).value = params[key]
				} catch(e) {
					//console.log('eee'+ e+key)
				}
			}
		}
		document.forms[0].method.value = 'exportMovementScreen';
		document.forms[0].target = 'tmp';
		document.forms[0].submit();

	}
	function exportData(exportType) {
		var methodName = 'exportMovementScreen';
		var oldTarget = document.forms[0].target;
		enableFields();
		getSelectedList();

		document.forms[0].method.value = 'exportMovementScreen';
		document.forms[0].target = 'tmp';
		document.forms[0].exportType.value = exportType;
		document.forms[0].selectedEntityId.value = document.forms[0].elements["entityId"].value;
		document.forms[0].entityId.value = document.forms[0].elements['movement.id.entityId'].value;
		document.forms[0].currencyCode.value = currencyId;
		document.forms[0].date.value = dateStr;
		document.forms[0].initialinputscreen.value = initialscreen;
		document.forms[0].currentPage.value = '${currentPage}';
		document.forms[0].totalFlag.value = "${totalFlag}";

		<c:if test="${method != 'search'}">
		<c:if test="${initialinputscreen == 'filterscreen'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].balanceType.value = "${balanceType}";
		document.forms[0].accountId.value = "${accountId}";
		document.forms[0].accountType.value = "${accountType}";
		document.forms[0].totalCount.value = '${totalCount}';
		document.forms[0].selectedFilter.value = '${selectedFilter}';
		document.forms[0].selectedSort.value = '${selectedSort}';
		</c:if>
		</c:if>

		<c:if test="${method == 'search'}">
		document.forms[0].maxPage.value = '${maxPage}';
		document.forms[0].totalCount.value = '${totalCount}';
		document.forms[0].accountType.value = "${accountType}";

		<c:if test="${access != 'readOnly'}">
		<c:choose>
		<c:when test="${initialinputscreen == 'currencymonitor'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].balanceType.value = "${balanceType}";
		document.forms[0].locationId.value = "${locationId}";
		</c:when>

		<c:when test="${initialinputscreen == 'accountmonitor'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].balanceType.value = "${balanceType}";
		document.forms[0].accountId.value = "${accountId}";
		document.forms[0].accountType.value = "${accountType}";
		</c:when>

		<c:when test="${initialinputscreen == 'accountbreakdownmonitor'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].balanceType.value = "${balanceType}";
		document.forms[0].accountId.value = "${accountId}";
		</c:when>

		<c:when test="${initialinputscreen == 'bookmonitor'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].bookCode.value = "${bookCode}";
		</c:when>

		<c:when test="${initialinputscreen == 'mvmntsfromWorkFlowMonitor'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].tabIndicator.value = "${tabIndicator}";
		document.forms[0].currGrp.value = "${currGrp}";
		document.forms[0].roleId.value = "${roleId}";
		document.forms[0].matchStatus.value = "${matchStatus}";
		document.forms[0].predictStatus.value = "${predictStatus}";
		document.forms[0].linkFlag.value = "${linkFlag}";
		</c:when>

		<c:when test="${initialinputscreen == 'unSettledYesterday' ||
                       initialinputscreen == 'backValuedMvmnts' ||
                       initialinputscreen == 'openUnexpectedMvmnts'}">
		document.forms[0].valueDate.value = "${valueDate}";
		document.forms[0].currGrp.value = "${currGrp}";
		document.forms[0].roleId.value = "${roleId}";
		document.forms[0].balanceType.value = "${balanceType}";
		</c:when>

		<c:otherwise>
		document.forms[0].archiveId.value = "${archiveId}";
		document.forms[0].filterFromSerach.value = "true";
		</c:otherwise>
		</c:choose>
		</c:if>
		</c:if>

		var scrollTable = document.getElementById('ddscrolltable');
		document.forms[0].tableScrollbarLeft.value = scrollTable.scrollLeft;
		document.forms[0].tableScrollbarTop.value = scrollTable.scrollTop;
		document.forms[0].scrollbarLeft.value = document.body.scrollLeft;
		document.forms[0].scrollbarTop.value = document.body.scrollTop;
		document.forms[0].isAmountDiffer.value = '${isAmountDiffer}';
		document.forms[0].selectedMovementsAmount.value = '${selectedMovementsAmount}';

		if ((document.forms[0].totalCount.value = '${totalCount}') > 0) {
			blockUIForDownload();
			document.forms[0].submit();
		}
		document.forms[0].target = oldTarget;
	}


	function submitForm(methodName) {
		enableFields();
		getSelectedList();
		document.forms[0].method.value = getMethodName(methodName);
		document.forms[0].selectedEntityId.value = document.forms[0].elements["entityId"].value;
		document.forms[0].entityId.value = document.forms[0].elements['movement.id.entityId'].value;
		document.forms[0].currencyCode.value = currencyId;
		document.forms[0].date.value = dateStr;
		document.forms[0].initialinputscreen.value = initialscreen;
		document.forms[0].currentPage.value = '${requestScope.currentPage}';
		document.forms[0].totalFlag.value = "${requestScope.totalFlag}";

		<c:if test="${requestScope.method == 'search'}">
		document.forms[0].maxPage.value = '${requestScope.maxPage}';
		document.forms[0].totalCount.value = '${requestScope.totalCount}';
		<c:if test="${requestScope.access != 'readOnly'}">
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'currencymonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].locationId.value = "${requestScope.locationId}";
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'accountmonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].accountId.value = "${requestScope.accountId}";
		document.forms[0].accountType.value = "${requestScope.accountType}";
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'bookmonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].bookCode.value = "${requestScope.bookCode}";
		</c:when>
		<c:otherwise>
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].tabIndicator.value = "${requestScope.tabIndicator}";
		document.forms[0].currGrp.value = "${requestScope.currGrp}";
		document.forms[0].roleId.value = "${requestScope.roleId}";
		document.forms[0].matchStatus.value = "${requestScope.matchStatus}";
		document.forms[0].predictStatus.value = "${requestScope.predictStatus}";
		document.forms[0].linkFlag.value = "${requestScope.linkFlag}";
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:if>
		</c:if>

		var scrollTable = document.getElementById('ddscrolltable');
		document.forms[0].tableScrollbarLeft.value = scrollTable.scrollLeft;
		document.forms[0].tableScrollbarTop.value = scrollTable.scrollTop;
		document.forms[0].scrollbarLeft.value = document.body.scrollLeft;
		document.forms[0].scrollbarTop.value = document.body.scrollTop;
		document.forms[0].isAmountDiffer.value = '${requestScope.isAmountDiffer}';
		document.forms[0].selectedMovementsAmount.value = '${requestScope.selectedMovementsAmount}';

		document.forms[0].submit();
	}


	function refreshAfterApplyCurrencyThreshold(methodName) {
		enableFields();
		getSelectedList();
		document.forms[0].currentPage.value = 1;
		document.forms[0].applyCurrencyThresholdInd.value = "1";
		document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";
		document.forms[0].method.value = getMethodName(methodName);
		document.forms[0].selectedEntityId.value = document.forms[0].elements["entityId"].value;
		document.forms[0].entityId.value = document.forms[0].elements['movement.id.entityId'].value;
		document.forms[0].currencyCode.value = currencyId;
		document.forms[0].date.value = dateStr;
		document.forms[0].initialinputscreen.value = initialscreen;

		<c:if test="${requestScope.method == 'search'}">
		document.forms[0].maxPage.value = '${requestScope.maxPage}';
		document.forms[0].totalCount.value = '${requestScope.totalCount}';
		<c:if test="${requestScope.access != 'readOnly'}">
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'currencymonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].locationId.value = "${requestScope.locationId}";
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'accountmonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].accountId.value = "${requestScope.accountId}";
		document.forms[0].accountType.value = "${requestScope.accountType}";
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'bookmonitor'}">
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].bookCode.value = "${requestScope.bookCode}";
		</c:when>
		<c:otherwise>
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].tabIndicator.value = "${requestScope.tabIndicator}";
		document.forms[0].currGrp.value = "${requestScope.currGrp}";
		document.forms[0].roleId.value = "${requestScope.roleId}";
		document.forms[0].matchStatus.value = "${requestScope.matchStatus}";
		document.forms[0].predictStatus.value = "${requestScope.predictStatus}";
		document.forms[0].linkFlag.value = "${requestScope.linkFlag}";
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:if>
		</c:if>

		var scrollTable = document.getElementById('ddscrolltable');
		document.forms[0].tableScrollbarLeft.value = scrollTable.scrollLeft;
		document.forms[0].tableScrollbarTop.value = scrollTable.scrollTop;
		document.forms[0].scrollbarLeft.value = document.body.scrollLeft;
		document.forms[0].scrollbarTop.value = document.body.scrollTop;
		document.forms[0].isAmountDiffer.value = '${requestScope.isAmountDiffer}';
		document.forms[0].selectedMovementsAmount.value = '${requestScope.selectedMovementsAmount}';
		document.forms[0].submit();
	}

	function getMethodName(methodName) {
		<c:choose>
		<c:when test="${requestScope.method != 'search'}">
		return methodName;
		</c:when>
		<c:when test="${requestScope.method == 'search'}">
		return 'search' + methodName;
		</c:when>
		</c:choose>
	}




	function optionClick_server_filter_JSP(index, value, action) {
		if (action == "filter") {
			value = replace(value, '&nbsp;', ' ');
			value = replace(value, '&amp;', '&');
			var filterValue = "";

			if (currentFilter == "all" || currentFilter == "undefined") {
				for (var idx = 0; idx < xl.numColumns; ++idx) {
					if (idx == index) filterValue += value + "|";
					else filterValue += "All" + "|";
				}
			} else {
				var filter = currentFilter.split("|");
				filter[index] = value;
				for (var idx = 0; idx < xl.numColumns; ++idx) {
					filterValue += filter[idx] + "|";
				}
			}

			document.forms[0].selectedSort.value = currentSort;
			document.forms[0].selectedFilter.value = filterValue;
		} else {
			var sortColum = index;
			var sortDesc = value;
			document.forms[0].selectedSort.value = sortColum + "|" + sortDesc + "|";
			document.forms[0].selectedFilter.value = currentFilter;
		}
		document.forms[0].entityId.value = document.forms[0].elements['movement.id.entityId'].value;
		document.forms[0].currencyCode.value = currencyId;
		document.forms[0].date.value = dateStr;
		document.forms[0].posLvlId.value = posLvlId;
		document.forms[0].totalCount.value = '${requestScope.totalCount}';
		getSelectedList();

		<c:choose>
		<c:when test="${requestScope.method != 'search'}">
		document.forms[0].method.value = "displayOpenMovements";
		document.forms[0].initialinputscreen.value = initialscreen;
		</c:when>
		<c:when test="${requestScope.method == 'search'}">
		<c:choose>
		<c:when test="${requestScope.access == 'readOnly'}">
		document.forms[0].method.value = "displayOpenMovements";
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'currencymonitor'}">
		document.forms[0].method.value = "getCurrencyMonitorMvmnts";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].locationId.value = "${requestScope.locationId}";
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'accountmonitor'}">
		document.forms[0].method.value = "getAccountMonitorMvmnts";
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].balanceType.value = "${requestScope.balanceType}";
		document.forms[0].accountId.value = "${requestScope.accountId}";
		document.forms[0].accountType.value = "${requestScope.accountType}";
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'bookmonitor'}">
		document.forms[0].method.value = "getBookMonitorMvmnts";
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].bookCode.value = "${requestScope.bookCode}";
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'mvmntsfromWorkFlowMonitor'}">
		document.forms[0].method.value = "getMvmntsfromWorkFlowMonitor";
		document.forms[0].valueDate.value = "${requestScope.valueDate}";
		document.forms[0].tabIndicator.value = "${requestScope.tabIndicator}";
		document.forms[0].currGrp.value = "${requestScope.currGrp}";
		document.forms[0].roleId.value = "${requestScope.roleId}";
		document.forms[0].matchStatus.value = "${requestScope.matchStatus}";
		document.forms[0].predictStatus.value = "${requestScope.predictStatus}";
		document.forms[0].linkFlag.value = "${requestScope.linkFlag}";
		</c:when>
		<c:otherwise>
		document.forms[0].method.value = "search";
		document.forms[0].archiveId.value = "${archiveId}";
		document.forms[0].filterFromSerach.value = "true";
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:when>
		</c:choose>

		document.forms[0].currentPage.value = 1;
		document.forms[0].submit();
	}


	function offeredMatches(param){
		openWindow(param,'manualMatchWindow','left=50,top=190,width=1280,height=585,toolbar=0, resizable=yes, scrollbars=yes','true');
	}

	function bodyOnLoad(){
		window.onbeforeunload = unloadMovementWindow;
		var filterValues=new Array();

		var dateFormat = '${sessionScope.CDM.dateFormat}';
		var selectedMovlist = '${requestScope.selectedList}';

		document.forms[0].initialinputscreen.value = "${requestScope.initialinputscreen}" ;
		document.forms[0].currentPage.value = '${requestScope.currentPage}';
		document.getElementById("selectedMovementSummary").style.visibility = 'visible';
		document.getElementById("ddimagebuttons").style.top = 564;
		document.getElementById("printButtonDiv").style.top = 573;
		<c:if test="${'search'==requestScope.method}">

		xl = new XLSheet("OutStandingMovementSummaryDetails","table_1", ["String", "String",currencyFormat,"String","String","String","String", "String","String","String","String","Number","String","String","Number", "String","String","String","Number","String","String","String"],"1121121111011101222111","false",currentFilterValues,sortedValues);


		document.getElementById("selectedMovementSummary").style.visibility = 'hidden';
		document.getElementById("selectedMovementSummary").style.top = 0;
		document.getElementById("ddimagebuttons").style.top = 453;
		document.getElementById("printButtonDiv").style.top = 462;



		</c:if>

		<c:if test="${'search'!=requestScope.method}">

		document.forms[0].selectedTab.value = '${requestScope.date}';

		xl = new XLSheet("OutStandingMovementSummaryDetails","table_1", ["String", "String",currencyFormat,"String","String","String","String", "String","String","String","String","Number","String","String","Number", "String","String","String","Number","String","String","String"],"1121121111011101222111","true",currentFilterValues,sortedValues);

		getDifferenceAmount("selectedMovementSummaryDetails");


		</c:if>

		xl2 = new XLSheet("selectedMovementSummaryDetails","table_2", ["String", "String",currencyFormat,"String","String","String","String", "String","String","String","String","Number","String","String","Number", "String","String","String","Number","String","String","String"],"000000000000000000000","true");

		var defaultSortingOnValueDate = true;
		var isFilterSortStatus = false;

		if(filterstatus !="")
		{
			var filterStatus1 ;
			filterStatus1 = filterstatus.split(",");
			if(typeof xl != 'undefined' && xl != null && typeof xl.setFilterStatus != 'undefined')
				xl.setFilterStatus(filterStatus1);
			isFilterSortStatus = true;
		}


		if(sortDescending !="" && sortDescending !="null" && sortDescending == "true")
		{
			xl.dataTable.defaultDescending = sortDescending
			isFilterSortStatus = true;
		}

		if(sortDescending !="" && sortDescending !="null" && sortDescending == "false")
		{
			xl.dataTable.doManualSorting(sortStatus);
			isFilterSortStatus = true;
		}

		if(nextMovIdFocus !="" && nextMovIdFocus !="-1")
		{
			xl.getNextScrollIntoView(nextMovIdFocus);
			isFilterSortStatus = true;
		}
		if(defaultSortingOnValueDate && !isFilterSortStatus)

		{
			xl.dataTable.defaultDescending = "true";
		}

		updateColors();

		xl.onsort = xl.onfilter = onFilter;

		if(menuEntityCurrGrpAccess == "0"){
			highlightMultiTableRows("OutStandingMovementSummaryDetails");
		}else{

			<c:if test="${'search'!=requestScope.method}">

			highlightMultiTableRows("OutStandingMovementSummaryDetails");

			</c:if>

			<c:if test="${'search'==requestScope.method}">

			highlightMultiTableRows("OutStandingMovementSummaryDetails");

			</c:if>
		}

		highlightTableRows("selectedMovementSummaryDetails");


		<c:if test="${'search'==requestScope.method}">

		var entityIdsearch = "${requestScope.entityId}";
		var entityNamesearch  = "${requestScope.entityName}";
		document.forms[0].elements["movement.id.entityId"].value= entityIdsearch;

		</c:if>

		var entitydropBox = new SwSelectBox(document.forms[0].elements["movement.id.entityId"],document.getElementById("entityName"));

		document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshenablebutton").innerHTML;
		document.getElementById("closebutton").innerHTML = document.getElementById("closeenablebutton").innerHTML;

		<c:if test="${'search'!=requestScope.method}">

		if(selectedMovlist.indexOf(',') != -1){
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

			document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

			document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
			document.getElementById("matchbutton").innerHTML = document.getElementById("matchenablebutton").innerHTML;
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
			document.getElementById("reconbutton").innerHTML = document.getElementById("reconenablebutton").innerHTML;
			document.getElementById("okbutton").innerHTML = document.getElementById("okhidebutton").innerHTML;
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
			document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
		}else if (selectedMovlist.length > 0 ){
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;

			setMsgButtonStatus();

			document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
			document.getElementById("matchbutton").innerHTML = document.getElementById("matchenablebutton").innerHTML;
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
			enabledisableReconButton("selectedMovementSummaryDetails");
			document.getElementById("okbutton").innerHTML = document.getElementById("okhidebutton").innerHTML;
			document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;

		}else{
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
			document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

			document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
			document.getElementById("matchbutton").innerHTML = document.getElementById("matchdisablebutton").innerHTML;
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
			document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
			document.getElementById("okbutton").innerHTML = document.getElementById("okhidebutton").innerHTML;
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
			document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
		}

		highlightSelectedRows("OutStandingMovementSummaryDetails", selectedMovlist);


		</c:if>
		<c:if test="${'search'==requestScope.method}">

		<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
		document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;
		if(selectedMovlist.indexOf(',') != -1){
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
		}else if(selectedMovlist.length > 0){
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
			setMsgButtonStatus();
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
		}
		highlightSelectedRows("OutStandingMovementSummaryDetails", selectedMovlist);

		</c:if>
		<c:if test="${'matchSearch'!=requestScope.initialinputscreen}">

		<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

		document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;
		if(selectedMovlist.indexOf(',') != -1){
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
		}else if(selectedMovlist.length > 0){
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
			setMsgButtonStatus();
			document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
		}
		highlightSelectedRows("OutStandingMovementSummaryDetails", selectedMovlist);

		</c:if>
		<c:if test="${'movementSearch'!=requestScope.initialinputscreen}">

		document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
		document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;


		</c:if>

		</c:if>

		</c:if>
		bringMyChildOnTop(window.name);
		var scrollTable=document.getElementById('ddscrolltable');

		if ('${requestScope.tableScrollbarLeft}' != null && '${requestScope.tableScrollbarLeft}' != "") {
			scrollTable.scrollLeft = '${requestScope.tableScrollbarLeft}';
			scrollTable.scrollTop = '${requestScope.tableScrollbarTop}';
		}

		document.body.scrollLeft = '${requestScope.scrollbarLeft}';
		document.body.scrollTop = '${requestScope.scrollbarTop}';

		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

		document.forms[0].isAmountDiffer.value = '${requestScope.isAmountDiffer}';
		document.forms[0].selectedMovementsAmount.value = '${requestScope.selectedMovementsAmount}';

		document.forms[0].applyCurrencyThreshold.value = "${requestScope.applyCurrencyThreshold}";

		document.forms[0].totalFlag.value = "${requestScope.totalFlag}";
	}

	function highlightSelectedRows(tableId, selectedList){

		var table = document.getElementById(tableId);
		var tbody = table.getElementsByTagName("tbody")[0];
		var rows = tbody.getElementsByTagName("tr");
		var mvmtId = "";
		var mvmntArray=new Array();
		var selectedvmnt=selectedList.split(",");
		for (i=0; i < rows.length; i++)
		{

			mvmtId = rows[i].cells[18].innerText.trim();
			if(selectedList != "")
			{
				for(var j=0;j<selectedvmnt.length;j++){
					if(selectedvmnt[j] == mvmtId){
						highLightTableRow(rows[i]);

					}
				}
			}
		}
	}


	function selectMulTableRow(e)
	{

		var event = (window.event|| e);
		var target = (event.srcElement || event.target);
		if(target.tagName == "TR" || target.tagName == "TD")
		{
			var rowElement = target.parentElement;
			var tblElement = target.parentElement.parentElement.parentElement;
			<c:if test="${'search'==requestScope.method}">



			<c:if test="${'outstanding'==requestScope.screenName}">

			var isRowSel = isRowSelected(rowElement);
			resetTableRowsStyle(tblElement);

			if(isRowSel == false)
				rowElement.className = 'selectrow'
			onMultiSelectTableRow2(rowElement,isRowSelected(rowElement));

			</c:if>



			<c:if test="${'outstanding'!=requestScope.screenName}">

			onMultiSelectTableRow(rowElement,isRowSelected(rowElement));

			</c:if>


			</c:if>


			<c:if test="${'search'!=requestScope.method}">

			onMultiSelectTableRow(rowElement,isRowSelected(rowElement));

			</c:if>

		}

	}

	function onMultiSelectTableRow2(rowElement,isSelected){

		var hiddenElement = rowElement.getElementsByTagName("input")[0];

		var movementId = hiddenElement.value;
		document.forms[0].selectedMovementId.value = hiddenElement.value;

		<c:if test="${'search'==requestScope.method}">


		var count;

		count = getCountRowsSelectedFromTable("OutStandingMovementSummaryDetails", true);


		if(count==1)
		{

			enableDisableMovementNotesBtns(count);

		}
		else {
			document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;

			document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;

		}

		</c:if>


	}

	function onMultiSelectTableRow(rowElement,isSelected){

		var hiddenElement = rowElement.getElementsByTagName("input")[0];

		var movementId = hiddenElement.value;
		document.forms[0].selectedMovementId.value = hiddenElement.value;
		<c:if test="${'search'==requestScope.method}">

		if(isSelected){
			unHighLightTableRow(rowElement);
			count = getCountRowsSelectedFromTable("OutStandingMovementSummaryDetails", false);
			<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

			deleteRowFromSelection("selectedMovementSummaryDetails", rowElement);
			count = getCountRowsSelectedFromTable("selectedMovementSummaryDetails", true);

			</c:if>
			<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

			deleteRowFromSelection("selectedMovementSummaryDetails", rowElement);
			count = getCountRowsSelectedFromTable("selectedMovementSummaryDetails", true);

			</c:if>
		}
		else{
			highLightTableRow(rowElement);
			count = getCountRowsSelectedFromTable("OutStandingMovementSummaryDetails", false);
			<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

			addRowToSelection("selectedMovementSummaryDetails", rowElement);
			count = getCountRowsSelectedFromTable("selectedMovementSummaryDetails", true);

			</c:if>
			<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

			addRowToSelection("selectedMovementSummaryDetails", rowElement);
			count = getCountRowsSelectedFromTable("selectedMovementSummaryDetails", true);

			</c:if>
		}


		</c:if>

		<c:if test="${'search'!=requestScope.method}">


		if(isSelected){

			if(unlockMovementOnServer(movementId) == "true"){
				unHighLightTableRow(rowElement);
				deleteRowFromSelection("selectedMovementSummaryDetails", rowElement);


			}
		}else
		{
			if(lockMovementOnServer(movementId) == "true")
			{
				highLightTableRow(rowElement);
				addRowToSelection("selectedMovementSummaryDetails", rowElement);


			}
		}
		count = getCountRowsSelectedFromTable("selectedMovementSummaryDetails", true);

		getDifferenceAmount("selectedMovementSummaryDetails");


		</c:if>


		if(count<1){
			<c:if test="${'search'!=requestScope.method}">


			enableDisableMovementNotesBtns(count);
			document.getElementById("matchbutton").innerHTML = document.getElementById("matchdisablebutton").innerHTML;
			document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
			document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
			document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
			document.getElementById("removebutton").innerHTML = document.getElementById("removedisablebutton").innerHTML;
			if(document.getElementById("okbutton") != null)
				document.getElementById("okbutton").innerHTML = document.getElementById("okhidebutton").innerHTML;

			</c:if>

			<c:if test="${'search'==requestScope.method}">


			<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

			enableDisableMovementNotesBtns(count);
			if(document.getElementById("okbutton") != null)
				document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;

			</c:if>
			<c:if test="${'matchSearch'!=requestScope.initialinputscreen}">

			<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

			enableDisableMovementNotesBtns(count);
			document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;

			</c:if>
			<c:if test="${'movementSearch'!=requestScope.initialinputscreen}">

			enableDisableMovementNotesBtns(count);

			</c:if>

			</c:if>

			</c:if>
		}
		if(count>1)
		{
			if(menuEntityCurrGrpAccess == "0"){
				<c:if test="${'search'!=requestScope.method}">

				document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
				document.getElementById("notesbutton").innerHTML = document.getElementById("notesdisablebutton").innerHTML;
				document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
				document.getElementById("msgbutton").innerHTML = document.getElementById("msgdisablebutton").innerHTML;
				document.getElementById("matchbutton").innerHTML = document.getElementById("matchenablebutton").innerHTML;
				document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
				document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
				enabledisableReconButton("selectedMovementSummaryDetails");


				</c:if>
				<c:if test="${'search'==requestScope.method}">

				<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

				enableDisableMovementNotesBtns(count);
				if(document.getElementById("okbutton") != null)
					document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

				</c:if>
				<c:if test="${'matchSearch'!=requestScope.initialinputscreen}">

				<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

				enableDisableMovementNotesBtns(count);
				if(document.getElementById("okbutton") != null)
					document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;

				</c:if>
				<c:if test="${'movementSearch'!=requestScope.initialinputscreen}">

				enableDisableMovementNotesBtns(count);


				</c:if>

				</c:if>

				</c:if>
			}
			else{
				if(menuEntityCurrGrpAccess == "1"){
					<c:if test="${'search'!=requestScope.method}">

					document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
					enableDisableMovementNotesBtns(count);

					document.getElementById("matchbutton").innerHTML = document.getElementById("matchdisablebutton").innerHTML;
					document.getElementById("suspendbutton").innerHTML = document.getElementById("suspenddisablebutton").innerHTML;
					document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmdisablebutton").innerHTML;
					document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;


					</c:if>
					<c:if test="${'search'==requestScope.method}">

					enableDisableMovementNotesBtns(count);
					if(document.getElementById("okbutton") != null)
						document.getElementById("okbutton").innerHTML = document.getElementById("okdisablebutton").innerHTML;

					</c:if>
				}
			}
		}
		else{
			if(count==1)
			{
				if(menuEntityCurrGrpAccess == "0" || menuEntityCurrGrpAccess == "1" ){

					<c:if test="${'search'!=requestScope.method}">

					document.getElementById("parmsbutton").innerHTML = document.getElementById("parmsenablebutton").innerHTML;
					enableDisableMovementNotesBtns(count);
					if(menuEntityCurrGrpAccess == "0") {
						document.getElementById("matchbutton").innerHTML = document.getElementById("matchenablebutton").innerHTML;
						document.getElementById("suspendbutton").innerHTML = document.getElementById("suspendenablebutton").innerHTML;
						document.getElementById("confirmbutton").innerHTML = document.getElementById("confirmenablebutton").innerHTML;
						enabledisableReconButton("selectedMovementSummaryDetails");
					}


					</c:if>

					<c:if test="${'search'==requestScope.method}">

					<c:if test="${'matchSearch'==requestScope.initialinputscreen}">

					enableDisableMovementNotesBtns(count);
					if(document.getElementById("okbutton") != null)
						document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

					</c:if>
					<c:if test="${'matchSearch'!=requestScope.initialinputscreen}">

					<c:if test="${'movementSearch'==requestScope.initialinputscreen}">

					document.getElementById("notesbutton").innerHTML = document.getElementById("notesenablebutton").innerHTML;
					document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
					setMsgButtonStatus();
					if(document.getElementById("okbutton") != null)
						document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;

					</c:if>
					<c:if test="${'movementSearch'!=requestScope.initialinputscreen}">

					enableDisableMovementNotesBtns(count);


					</c:if>

					</c:if>

					</c:if>
				}
			}
		}
	}


	function clickLink() {
		var element = '';
		calledFromNext = "true";

		element += '&selectedList=';
		element += selectedMvmtList;

		element += '&selectedSort=';
		element += currentSort;
		element += '&selectedFilter=';
		element += currentFilter;
		element += '&archiveId=${archiveId}';

		<c:choose>
		<c:when test="${requestScope.method == 'search'}">
		if (initialscreen == "E") {
			element += '&method=displayOpenMovements';
			element += '&initialinputscreen=' + initialscreen;
		}
		<c:choose>
		<c:when test="${requestScope.access == 'readOnly'}">
		element += '&method=next';
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'currencymonitor'}">
		element += '&valueDate=' + '${requestScope.valueDate}';
		element += '&balanceType=' + '${requestScope.balanceType}';
		element += '&locationId=' + '${requestScope.locationId}';
		element += '&method=getCurrencyMonitorMvmnts';
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'accountmonitor'}">
		element += '&valueDate=' + '${requestScope.valueDate}';
		element += '&balanceType=' + '${requestScope.balanceType}';
		element += '&accountId=' + '${requestScope.accountId}';
		element += '&accountType=' + '${requestScope.accountType}';
		element += '&initialinputscreen=' + "${requestScope.initialinputscreen}";
		element += '&method=getAccountMonitorMvmnts';
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'bookmonitor'}">
		element += '&valueDate=' + '${requestScope.valueDate}';
		element += '&method=getBookMonitorMvmnts';
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'mvmntsfromWorkFlowMonitor'}">
		element += '&method=getMvmntsfromWorkFlowMonitor';
		element += '&linkFlag=' + '${requestScope.linkFlag}';
		element += '&tabIndicator=' + '${requestScope.tabIndicator}';
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'unSettledYesterday'}">
		element += '&method=getUnsettledYesterdayMvmnts';
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'backValuedMvmnts'}">
		element += '&method=getBackValuedMvmnts';
		</c:when>
		<c:when test="${requestScope.initialinputscreen == 'openUnexpectedMvmnts'}">
		element += '&method=getOpenUnexpectedMvmnts';
		</c:when>
		<c:otherwise>
		element += '&method=searchnext';
		element += '&filterCriteria=';
		element += '';
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>
		</c:when>
		<c:otherwise>
		<c:choose>
		<c:when test="${requestScope.initialinputscreen == 'filterscreen'}">
		element += '&amountover=' + '${requestScope.amountover}';
		element += '&amountunder=' + '${requestScope.amountunder}';
		element += '&group=' + '${requestScope.group}';
		element += '&metaGroup=' + '${requestScope.metaGroup}';
		element += '&valueFromDate=' + '${requestScope.valueFromDate}';
		element += '&valueToDate=' + '${requestScope.valueToDate}';
		element += '&timefrom=' + '${requestScope.timefrom}';
		element += '&timeto=' + '${requestScope.timeto}';
		element += '&inputDate=' + '${requestScope.inputDate}';
		element += '&reference=' + '${requestScope.reference}';
		element += '&fintrade=' + '${requestScope.fintrade}';
		element += '&method=nextfilterOutMovementSummary';
		</c:when>
		<c:otherwise>
		element += '&method=displayOpenMovements';
		element += '&initialinputscreen=' + initialscreen;
		</c:otherwise>
		</c:choose>
		</c:otherwise>
		</c:choose>

		element += '&menuAccessId=' + "${requestScope.menuAccessId}";
		element += '&applyCurrencyThreshold=' + '${requestScope.applyCurrencyThreshold}';
		element += '&totalFlag=' + '${requestScope.totalFlag}';
		element += '&isAmountDiffer=' + '${requestScope.isAmountDiffer}';
		element += '&selectedMovementsAmount=' + '${requestScope.selectedMovementsAmount}';

		alert(element);
		return element;
	}

	function checkMvmtLockStatus(process){
		var allowMatch = true;
		if (!checkSelectedMovementsAmounts()) {
			allowMatch= false;
			var title = '<fmt:message key="accountmonitor.confirm.title"/>';
			var testMessageY = '<fmt:message key="confirm.addMovementsTomatch"/>';
			buttonId = ShowErrMsgWindowWithBtn(title  ,testMessageY , OK_CANCEL);
			if (buttonId == "3") {
				allowMatch = true;
			}
		}
		if (allowMatch) {
			var selectedList = document.forms[0].selectedList.value;
			var entityId = document.forms[0].elements['movement.id.entityId'].value;
			var oXMLHTTP = new XMLHttpRequest();
			if(process == "offeredMatch") {
				javascript:openWindow(offeredMatch('offeredMatch'),'manualMatchWindow','left=50,top=190,width=2020,height=585,toolbar=0, resizable=yes, scrollbars=yes','true');
			} else if (process == "suspendMatch") {
				suspend(process);
			} else if (process == "confirmMatch") {
				confirm(process);
			}else if (process == "reconMatch") {
				reconcile(process);
			}
		}
	}

	function getDifferenceAmount(tableId) {

		var highestPosLvl = getHighestPosLvl(tableId);

		var nextHighestPosLvl = getNextHighestPosLvl(tableId, highestPosLvl);

		var highestPosLvlTotalAmount = 0;
		if(typeof highestPosLvl != 'undefined' )
		{
			highestPosLvlTotalAmount = getPosLvlTotalAmount(tableId, highestPosLvl);

		}

		var nextPosLvlTotalAmount = 0;
		if (typeof nextHighestPosLvl != 'undefined')
		{
			nextPosLvlTotalAmount = getPosLvlTotalAmount(tableId, nextHighestPosLvl);

			<%-- Amount difference to be calculated when movements of different position Level ID are selected  --%>

			amountDifference = highestPosLvlTotalAmount - nextPosLvlTotalAmount ;


			var ele = document.getElementById("amountDiff");

			var textBox = ele.getElementsByTagName("Input")[0];

			if(amountDifference != 0 ) {

				var amtsign = "";
				if(amountDifference < 0) {
					amtsign = 'D';
					amountDifference *= -1;
				}
				else {
					amtsign = 'C';
				}


				amountDifference =  expandCurrencyAmount(amountDifference, currencyFormat, currencyId);



				var textBoxStyle =  textBox.style;

				textBoxStyle.textAlign = "right";

				textBox.value = amountDifference + " " + amtsign;

			} else {
				textBox.value = "";
			}
		}
		else {
			var ele = document.getElementById("amountDiff");

			var textBox = ele.getElementsByTagName("Input")[0];
			textBox.value = "";
		}
	}


	function setSelectedMovementForLock(selected){
		console.log("selectedMovementForLock", selected);
		selectedMovementForLock = selected;
	}
	var selectedMovementForLock = "";
	function deleteLock()
	{


		beaconUnlock(selectedMovementForLock);
		Main.unlockMvmnt("unlock");
	}

	// For page unload cases
	function beaconUnlock(movementIds) {
		try {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');

			const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

			// Create the data to be sent
			const formData = new FormData();
			formData.append('movementIds', movementIds);
			formData.append('method', 'unlockMovements');

			// Send using beacon API
			const success = navigator.sendBeacon(sURL, formData);
			return success ? "success" : "error";
		} catch (error) {
			console.error('Error during beacon unlock:', error);
			return "error";
		}
	}

	function callApp(reset) {
		if(reset  == true){
			Main.filterScreen("Reset");
			//getFlashObject("mySwf").FilterScreen("Reset");
		}
		else {
			Main.filterScreen("Filter");
			//getFlashObject("mySwf").FilterScreen("Filter");
		}
	}
	function amountOver(){
		return document.getElementById("amountover").value;
	}
	function amountUnder(){
		return document.getElementById("amountunder").value;
	}
	function getGroup(){
		return document.getElementById("group").value;
	}
	function getMetagroup(){
		return document.getElementById("metagroup").value;
	}
	function valueFromDate(){
		return document.getElementById("valueFromDate").value;
	}
	function valueToDate(){
		return document.getElementById("valueToDate").value;
	}
	function timefrom(){
		return document.getElementById("timefrom").value;
	}
	function timeto(){
		return document.getElementById("timeto").value;
	}
	function inputDate(){
		return document.getElementById("inputDate").value;
	}
	function fintrade(){
		return document.getElementById("fintrade").value;
	}
	function reference(){
		return document.getElementById("reference").value;
	}
	function currentFilterConf(){
		return document.getElementById("currentFilterConf").value;
	}
	function referenceFlag(){
		return document.getElementById("refFlagFilterSearch").value;
	}

	function openMovementFlag(){
		return document.getElementById("openMovementFlagSearch").value;
	}

	function help(){
		openWindow(buildPrintURL('print','Movement Summary Display '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
	}



	function giveWarningForAmtDifference(str, methodName) {
		if(!str) {
			var title = '<fmt:message key="accountmonitor.confirm.title"/>';
			var testMessageY = '<fmt:message key="confirm.addMovementsToExistingmatch"/>';
			buttonId = ShowErrMsgWindowWithBtn(title  ,testMessageY , OK_CANCEL);
			if (buttonId == "3") {

				document.forms[0].method.value = methodName;
				getSelectedList();
				document.forms[0].submit();
			}
		}else {
			document.forms[0].method.value = methodName;
			getSelectedList();
			document.forms[0].submit();
		}
	}
	function getRequestURL() {
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1);
		return requestURL;
	}

	var noOfMsgsForSelectedMov = 0; //This variable holds no. Messages present for selected movement
	var msgId = ""; // This variable holds msgId of the single Message present for the selected moveemnt

	function setMsgButtonStatus(movId) {

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName+"/movement.do?method=getMvmntMessageCount";
		sURL+= '&movmentId=' + movId;
		oXMLHTTP.open( "POST", sURL, false );
		oXMLHTTP.send();

		var concatStr = new String(oXMLHTTP.responseText);
		return ""+concatStr;
	}

	function onclosemenuwindow(){
		window.opener.childScreen();
	}

	function openMsgDisplayWindow(movementId , noOfMsgsSelectedMov,mesId) {
		if (noOfMsgsSelectedMov == 1) {
			var param = 'inputexceptionsmessages.do?method=single&seqid=';
			param += mesId;
			javascript:openWindow(param,'messageViewScreen','left=50,top=190,width=650,height=500,toolbar=0, resizable=yes, scrollbars=yes','true')

		} else {
			var param='movement.do?method=mvmntMessageDisplay';
			param += '&movmentId='+movementId;
			javascript:openWindow(param,'movMessageWindow','left=50,top=190,width=560,height=445,toolbar=0, resizable=yes, scrollbars=yes','true');
		}
	}

	function onWinClose() {
		alert("Closing!!!")
	}
	function CallBackApp(){
		console.log("CallBackApp");
		//getFlashObject("mySwf").RefreshParent();
		try {
			Main.refreshParent();
			//Main.cellLogic(null);
		}catch(e){
			console.log("CallBackApp", e);
		}

	}
	/**
	 * This function is used for calling the saveFontSize() method from ScreenOption
	 * to store the Font size of the screen data grid
	 *
	 * @param fontSize - set by the user.
	 *
	 */
	function getUpdateFontSize(fontSize) {
		return "screenOption.do?method=saveFontSize&screenOption.id.hostId=" + hostId + "&screenOption.id.userId=" + userId + "&screenOption.id.screenId=" + itemId + "&screenOption.propertyValue=" + fontSize;
	}


	/**
	 * This is used to capture when the download has been finished in order to close the popup in flex part
	 */
	function blockUIForDownload() {
		// Use the current timestamp as the token value
		var token = new Date().getTime();
		// Set the hidden input download_token_value_id as token. It will be sent to the action part.
		$('#download_token_value_id').val(token);
		/* The setInterval() method calls a function at specified intervals (in milliseconds).
           If we received the same token (from cookie) then the download is finished */
		fileDownloadCheckTimer = window.setInterval(function () {
			var downloadToken;
			var downloadError;
			var cookieValue = $.cookie('fileDownloadToken');
			downloadToken=downloadError=cookieValue;
			if (cookieValue!= null){
				var fileDownloadTokenValue= cookieValue.split("|");
				downloadToken=fileDownloadTokenValue[0];
				downloadError=fileDownloadTokenValue[1] ;
			}
			if (downloadError == "KO")
				errorFileDownload() ;
			else if (downloadError == "MEM"){
				errorMemFileDownload() ;
			}else if (downloadError == "ERR"){
				errorErrFileDownload() ;
			}else  if (downloadToken == token)
				downloadFinished();
		}, 300); // the intervals (in milliseconds) on how often to execute the code
	}


	/**
	 * Clear the interval, clear the token && then close the popup in flex part using call back method
	 */
	function downloadFinished() {
		window.clearInterval(fileDownloadCheckTimer);
		$.cookie('fileDownloadToken', null); //clears this cookie value
		//getFlashObject("mySwf").closePopup();
		Main.closePopup();
		// hideLoadingGif();
	}
	function errorFileDownload(){
		window.clearInterval(fileDownloadCheckTimer);
		$.cookie('fileDownloadToken', null); //clears this cookie value
		//getFlashObject("mySwf").closePopup();
		Main.closePopup();
		//alert('Report canceled');

	}
	function errorMemFileDownload(){
		window.clearInterval(fileDownloadCheckTimer);
		$.cookie('fileDownloadToken', null); //clears this cookie value
		//hideLoadingGif();
		//getFlashObject("mySwf").closePopup("mem");
		Main.closePopup("mem");
		//alert('Out of memory');

	}
	function errorErrFileDownload(){
		window.clearInterval(fileDownloadCheckTimer);
		$.cookie('fileDownloadToken', null); //clears this cookie value
		//getFlashObject("mySwf").closePopup("err");
		Main.closePopup("err");
		//alert('Error when creating report, please check logs');

	}

	function updateValues(amountOver, amountUnder, valueFromDate, valueToDate, group, metaGroup, timeFrom, timeTo, inputDate, reference, openMovementFlag) {
		document.getElementById("amountover").value = amountOver;
		document.getElementById("amountunder").value = amountUnder;
		document.getElementById("valueFromDate").value = valueFromDate;
		document.getElementById("valueToDate").value = valueToDate;
		document.getElementById("group").value = group;
		document.getElementById("metagroup").value = metaGroup;
		document.getElementById("timefrom").value = timeFrom;
		document.getElementById("timeto").value = timeTo;
		document.getElementById("inputDate").value = inputDate;
		document.getElementById("reference").value = reference;
		document.getElementById("openMovementFlagSearch").value = openMovementFlag;
	}

	function initializeValues(amountOver, amountUnder, valueFromDate, valueToDate, group, metaGroup, timeFrom, timeTo, inputDate, reference, openMovementFlag) {
		updateValues(amountOver, amountUnder, valueFromDate, valueToDate, group, metaGroup, timeFrom, timeTo, inputDate, reference, openMovementFlag);
	}

	function addColumns(methodName, profileId, source){
		var param = '/' + appName + '/outstandingmovement.do?method='+methodName;
		param += '&profileId='+profileId;
		param += '&source='+source;
		var mainWindow = openWindow (param, 'addColumns','left=10,top=230,width=600,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}

	function openMultiMvtActions(methodName, selectedMvtIdsList, entityId){
		deleteLock();
		var param = '/' + appName + '/multipleMvtActions.do?method='+methodName;
		param += '&selectedMvtIdsList='+selectedMvtIdsList;
		param += '&entityId='+entityId;
		var 	mainWindow = openWindow (param, 'multipleMvtActions','left=10,top=230,width=1350,height=780,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}


</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	  onLoad="setParentChildsFocus();setFocus(document.forms[0]);"
	  onunload="deleteLock();call();">

<form id="exportDataForm" target="tmp" method="post">
	<input name="selectedMovementId" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="method" type="hidden" value="">
	<input name="selectedTab" type="hidden" value='${date}'>
	<input name="selectedEntityId" type="hidden" value="">
	<input name="selectedEntityName" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value='${currencyCode}'>
	<input name="selectedNextMovId" type="hidden" value="">
	<input name="inputFrom" type="hidden" value="">
	<input name="initialinputscreen" type="hidden" value='${initialinputscreen}'>
	<input name="isNotesPresent" type="hidden" value="">
	<input name="entityId" type="hidden" value='${entityId}'>
	<input name="date" type="hidden" value='${date}'>
	<input name="currencyCode" type="hidden" value='${currencyCode}'>
	<input name="currentPage" type="hidden" value="">
	<input name="prevEnabled" type="hidden" value="">
	<input name="nextEnabled" type="hidden" value="">
	<input name="filterCriteria" type="hidden" value='${filterCriteria}'>
	<input name="messageId" type="hidden" value="">
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="filterFromSerach" type="hidden" value="">
	<input name="valueDate" type="hidden" value="">
	<input name="balanceType" type="hidden" value="">
	<input name="accountId" type="hidden" value="">
	<input name="accountType" type="hidden" value="">
	<input name="posLvlId" type="hidden" value='${posLvlId}'>
	<input name="archiveId" type="hidden" value="">
	<input name="bookCode" type="hidden" value="">
	<input name="entityId" type="hidden" value="">
	<input name="amountover" type="hidden" value="">
	<input name="group" type="hidden" value="">
	<input name="metagroup" type="hidden" value="">
	<input name="amountunder" type="hidden" value="">
	<input name="valueFromDate" type="hidden" value="">
	<input name="valueToDate" type="hidden" value="">
	<input name="timefrom" type="hidden" value="">
	<input name="timeto" type="hidden" value="">
	<input name="inputDate" type="hidden" value="">
	<input name="fintrade" type="hidden" value="">
	<input name="reference" type="hidden" value="">
	<input name="currentFilterConf" type="hidden" value="">
	<input name="amountDifference" type="hidden" value="0">
	<input name="posLvlSelectedList" type="hidden" value="">
	<input name="currGrp" type="hidden" value="">
	<input name="filterAcctType" type="hidden" value="">
	<input name="roleId" type="hidden" value="">
	<input name="predictStatus" type="hidden" value="">
	<input name="matchStatus" type="hidden" value="">
	<input name="linkFlag" type="hidden" value="">
	<input name="refreshScreen" type="hidden" value="">
	<input name="tabIndicator" type="hidden" value="">
	<input name="maxPage" type="hidden" value="">
	<input name="totalCount" type="hidden" value="">
	<input name="tableScrollbarLeft" type="hidden" value="">
	<input name="tableScrollbarTop" type="hidden" value="">
	<input name="scrollbarLeft" type="hidden" value="">
	<input name="scrollbarTop" type="hidden" value="">
	<input name="refreshFromMatchScreen" type="hidden" value="">
	<input name="menuAccessId" type="hidden">
	<input name="isAmountDiffer" type="hidden" value="">
	<input name="selectedMovementsAmount" type="hidden" value="">
	<input name="applyCurrencyThreshold" type="hidden">
	<input name="applyCurrencyThresholdInd" type="hidden" value="0">
	<input name="locationId" type="hidden"> <input name="totalFlag" type="hidden">
	<input name="exportType" type="hidden" value="">
	<input name="refFlagFilterSearch" type="hidden" value="">
	<input name="openMovementFlagSearch" type="hidden" value="">
	<input name="pageCount" type="hidden" value="">
	<input name="workflow" type="hidden" value="">
	<input name="matchingparty" type="hidden" value="">
	<input name="producttype" type="hidden" value="">
	<input name="uetr" type="hidden" value="">
	<input name="postingDateFrom" type="hidden" value="">
	<input name="postingDateTo" type="hidden" value="">
	<input name="scenarioId" type="hidden" value="">
	<input type="hidden" name="tokenForDownload" id="download_token_value_id"/>
	<input name="totalOverPagesValue" type="hidden" value="">
	<input name="totalInPageValue" type="hidden" value="">
	<input name="extraFilter" type="hidden" value="">

	<!-- 		<div id="swf"> -->
	<!-- 			<object id='mySwf' classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='100%' width='100%'> -->
	<%-- 	<param name='src' value='jsp/work/movementsummarydisplay.swf?version=<%= SwtUtil.appVersion %>' /> --%>
	<!-- 	<param name='flashVars' value='' /> -->
	<%-- 				<embed name='mySwf' src='jsp/work/movementsummarydisplay.swf?version=<%= SwtUtil.appVersion %>' height='100%' width='100%' flashVars='' /> --%>
	<!-- 			</object> -->
	<!-- 		</div> -->
	<input type="hidden" name="data" id="exportData" />
	<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="movementSummDisplay.title.Window"/>"/>
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>