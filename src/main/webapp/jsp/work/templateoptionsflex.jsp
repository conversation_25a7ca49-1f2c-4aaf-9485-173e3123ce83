<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Template Options screen.
  - Also, to embed the sub screens Forecast Monitor Options screen:
  - Author(s): Bala .D
  - Date: 08-06-2011
  -->
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		 <link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><fmt:message key="label.forecastTemplateOptions.title.window"/></title>
	</head>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="init();setParentChildsFocus();" onunload="unload();">
		<form id="exportDataForm" target="tmp" method="post">
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
			var userId = "${requestScope.userId}";
			var entityId = "${requestScope.entityId}";
			var currencyCode = "${requestScope.currencyCode}";
			var templateId = "${requestScope.templateId}";
			var screenRoute = "TemplateOptions";
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			//variable template
			var templateWindow = "";
			var flag = false;
			requestURL = requestURL.substring(0, idy + 1);
			// set the label
			var label = new Array();
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("forecastMonitor.do");
			label["text"] = new Array();
			label["tip"] = new Array();
			label["text"]["currency"] = "<fmt:message key="label.forecastMonitor.currency"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.forecastMonitor.selectCurrency"/>";

			label["text"]["entity"] = "<fmt:message key="label.forecastMonitor.entity"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.forecastMonitor.selectEntity"/>";

			label["text"]["template"] = "<fmt:message key="label.forecastTemplateOption.template"/>";
			label["tip"]["template"] = "<fmt:message key="tooltip.forecastTemplateOption.template"/>";

			label["text"]["add"] = "<fmt:message key="button.forecastMonitor.add"/>";
			label["tip"]["add"] = "<fmt:message key="tooltip.forecastMonitor.add"/>";
			label["text"]["change"] = "<fmt:message key="button.forecastMonitor.change"/>";
			label["tip"]["change"] = "<fmt:message key="tooltip.forecastMonitor.change"/>";

			label["text"]["save"] = "<fmt:message key="button.forecastMonitor.ok"/>";
			label["tip"]["save"] = "<fmt:message key="tooltip.forecastMonitor.ok"/>";
			label["text"]["cancel"] = "<fmt:message key="button.forecastMonitor.cancel"/>";
			label["tip"]["cancel"] = "<fmt:message key="tooltip.forecastMonitor.cancel"/>";

			label["text"]["templateLock"] = "<fmt:message key="alert.templateOption.templateLock"/>";
			label["text"]["recordExist"] = "<fmt:message key="alert.templateOption.recordExist"/>";
			label["text"]["duplicateTemplate"] = "<fmt:message key="alert.templateOption.duplicateTemplate"/>";

			// If parent refresh, close the window
			<c:if test='${requestScope.parentFormRefresh == "Y"}'>
				window.opener.refreshPending = true;
				self.close();
			</c:if>

			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			// variable xml Template
			var xmlTemplate = "";

			/**
			 * sendRequest
			 * This method is used to send the request
			 * @param requestURL
			 * @return Number
			 */
			function sendRequest(requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open("GET", requestURL, false);
				oXHR.send();
				var count = new Number(oXHR.responseText);
				return count;
			}

			/**
			 * help
			 * This function opens the help screen
			 * @return none
			 */
			function help() {
				openWindow(buildPrintURL('print', 'Template Options'), 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no', 'true')
			}

			/**
			 * closeWindow
			 * This function used to close the window
			 * @return none
			 */
			function closeWindow() {
				window.close();
			}

			/**
			 * unloadCloseWindow
			 * @param template
			 * This function is called, when unload the window
			 * @return none
			 */
			function unloadCloseWindow(template) {
				window.opener.reloadOption(template);
				window.close();
			}

			/**
			 * unload
			 * This function is called, when unload the window
			 * @return none
			 */
			function unload() {
				if (!flag)
					window.close();
				if (templateWindow != "") {
					templateWindow.close();
				}
			}

			/**
			 * openTemplateAddWindow
			 * Method to open the template add window
			 * @param methodName
			 * @return boolean
			 */
			function openTemplateAddWindow(methodName) {
				var param = '/' + appName + '/forecastMonitorTemplate.do?method=' + methodName;
				templateWindow = window.open(param, 'forecastMonitorTemplateFromOptionAdd', 'left=15,top=304,width=670,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes', 'true');
				return false;

			}

			/**
			 * openTemplateChangeWindow
			 * Method to load child screens
			 * @param methodName
			 * @return boolean
			 */
			function openTemplateChangeWindow(methodName) {
				var param = '/' + appName + '/forecastMonitorTemplate.do?method=' + methodName;
				templateWindow = window.open(param, 'forecastMonitorTemplateFromOptionChange', 'left=15,top=304,width=670,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes', 'true');
				return false;
			}

			/**
			 * lockTemplate
			 * This method is used to get template details
			 * @param templateId
			 * @param userId
			 * @return String
			 */
			function getTemplateDetails(templateId, userId) {
				var param = "/forecastMonitorTemplate.do?method=getTemplateDetails";
				param = param + "&templateId=" + templateId;
				param = param + "&userId=" + userId;
				var sURL = requestURL + appName + param;
				var oXHR = new XMLHttpRequest();
				oXHR.open("POST", sURL, false);
				oXHR.send();
				var name = oXHR.responseText;
				return name;
			}

			/**
			 * init
			 * This method is called when onload
			 * @return none
			 */
			function init() {
				xmlTemplate = window.opener.xmlTemplate;
				flag = true;
			}

			/**
			 * lockTemplate
			 * Method to load lock the template
			 * @param templateId
			 * @param templateName
			 * @param userId
			 * @param isPublic
			 * @return String
			 */
			function lockTemplate(templateId, userId, templateName, isPublic) {
				var param = "/forecastMonitorTemplate.do?method=lockTemplate";
				param = param + "&templateId=" + templateId;
				param = param + "&userId=" + userId;
				param = param + "&templateName=" + templateName;
				param = param + "&isPublic=" + isPublic;
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName + param;
				oXMLHTTP.open("POST", sURL, false);
				oXMLHTTP.send();
				var str = oXMLHTTP.responseText;
				return str;

			}

		</script>
			<%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>
