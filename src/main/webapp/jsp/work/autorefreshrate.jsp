<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<html>
<head>
<title><fmt:message key="refreshRate.title.window"/></title>

<SCRIPT language="JAVASCRIPT">
<!--Betcy::16/01/2009:Added for Mantis 774 to close the screen(start)-->
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "closebutton";
<!--Betcy::16/01/2009:Added for Mantis 774 to close the screen(end)-->

var parentMethod= "${parentRefreshMethod}";


<c:if test="${requestScope.parentRefresh == 'yes'}">

	window.opener.maintainSortFilterStatus();
	window.opener.document.forms[0].method.value=parentMethod;
	window.opener.submitForm(parentMethod,'','Y');
	self.close();
</c:if>


function submitFrom(methodName){
		<%-- START: Modified the code to show the alert message confirm dialog box to set the refreshRate as 5 . Modified by Kalidass on 04 Jun 2008. --%>
		/* Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 */
		var refreshRate = document.getElementById("screenOption.propertyValue").value ;
		var rateValue=validateField(document.forms[0].elements['screenOption.propertyValue'],'screenOption.propertyValue','numberPat');
		if(rateValue)
		{
		if(rateValue)
		if( refreshRate < 5)
		{
		var refreshRateConfirm = confirm ("Refresh rate selected was below minimum.Set it to 5 seconds.")
			if(refreshRateConfirm)
			{
				/* Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 */
				document.getElementById("screenOption.propertyValue").value = 5;
			document.forms[0].method.value=methodName;
			document.forms[0].parentRefreshMethod.value=parentMethod;
			document.forms[0].submit();
			}else{
			
			}
		
		
		}else
		{
		document.forms[0].method.value=methodName;
		document.forms[0].parentRefreshMethod.value=parentMethod;
		document.forms[0].submit();
		}
	<%-- END: Modified the code to show the alert message confirm dialog box to set the refreshRate as 5 .Modified by Kalidass on 04 Jun 2008. --%>
}
else
	{
	  /* Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 */
	  document.forms[0].elements['screenOption.propertyValue'].focus();
	}
}



</SCRIPT>
</head>

<%-- START: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()"> 
<%-- END: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<%-- START: Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 --%>
<form action="screenOption.do" method="post" >
<input name="method" type="hidden" value="">
<input name="parentRefreshMethod" type="hidden" value="">
<input type="hidden" name="screenOption.id.hostId" value="${screenOption.id.hostId}" />
<input type="hidden" name="screenOption.id.userId" value="${screenOption.id.userId}" />
<input type="hidden" name="screenOption.id.screenId" value="${screenOption.id.screenId}" />
<input type="hidden" name="screenOption.id.propertyName" value="${screenOption.id.propertyName}" />

<%-- END: Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 --%>
<!-- Start: UAT Phase2Defects20061012_STL Defect No: 47 -->
<!-- putting a dummy hidden field so that on pressing enter in the rate text field form doesnot get submitted by default  -->
<input name = "test" value = "" style = "visibility:hidden">
<!-- End: UAT Phase2Defects20061012_STL Defect No: 47 -->

	
<div id="RefreshRate" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:10px; width:300px; height:70px;">
<div id="RefreshRate" color="#7E97AF" style="position:absolute; left:4px; top:4px; width:290x; height:35px;">
<table width="286" border="0" cellspacing="0" cellpadding="0">
	 <tr height=""><td>&nbsp;</td></tr>
	 <tr height="25">
	 
		  <td width="120"><b><fmt:message key="autoRefreshRate"/></b></td>
		  <td width="26px">&nbsp;</td>
		  <td width="60px">
			<%-- Modified for using screen option instead of auto refresh(Mantis 1207) by Sutheendran Balaji A on 10-Aug-2010 --%>
			<input type="text" tabindex="1" titleKey="tooltip.enterRate" maxlength="3"
					   name="screenOption.propertyValue" style="width:60px;"
					   value="${screenOption.propertyValue}"
					   onchange="return validateField(this,'refreshRate','numberPat');" />

		  </td> <td width="20px">&nbsp;</td>
		   <td width="60"><b>seconds</b></td>

	 </tr>
     </table> 
	 </div>
</div>
<div id="MovementDisplay" style="position:absolute; left:240; top:98; width:70px; height:15px; visibility:visible;">
			<table width="60 border="0" cellspacing="0" cellpadding="0" height="20">
				 <tr>

				 <td align="Right">
					<a tabindex="5"  href=# onclick="javascript:openWindow(buildPrintURL('print','Auto-refresh Rate '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
				</td>

					<td align="right" id="Print">
						<a onclick="printPage();" tabindex="5" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
					</td>
				</tr>
			</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:90; width:300px; height:39px; visibility:visible;">
	<div id="manual" style="position:absolute; left:6; top:4; width:220px; height:15px; visibility:visible;">
		 <table width="210" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr>
						
				<td width="70px">
				<a title='<fmt:message key="tooltip.save"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitFrom('save')"><fmt:message key="button.save"/></a>		
				</td>
				<td id="closebutton">		
				<a  title='<fmt:message key="tooltip.close"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a>			
			</td>	
			</tr>
		</table>
	</div>
	
</div>
</form>
</body>
</html>