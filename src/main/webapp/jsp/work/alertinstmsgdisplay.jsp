<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<%
	//variable declaration
	String instanceId = "";
	//get the params from request attribute 
	if (request.getAttribute("instanceId") != null) {
		instanceId = request.getAttribute("instanceId")
				.toString();
	}
%>

<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Alert Instance Messages Display  - SMART-Predict</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var appName = "<%=SwtUtil.appName%>";
	var instanceId = "<%= instanceId %>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "AlertInstMsgDisplay";



</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>