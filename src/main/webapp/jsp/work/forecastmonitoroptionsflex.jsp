<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Forecast Monitor Options screen.
  - Also, to embed the sub screens Forecast Monitor Options screen:
  - Author(s): Bala .D
  - Date: 08-06-2011
  -->
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
	<head>
	    <link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<title><fmt:message key="label.forecastMonitorOptions.title.window"/></title>
	</head>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="init();setParentChildsFocus();" onunload="unloadCloseWindow();">
	
		
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
		<script type="text/javascript">
			// initialize the label
			var label = new Array();
			// get the menuAccessId
			var menuAccessIdParent = getMenuAccessIdOfChildWindow("forecastMonitor.do");
			// boolean variable
			var flag = false;
			// set the label values
			label["text"] = new Array();
			label["tip"] = new Array();
			label["alert"] = new Array();

			label["text"]["currency"] = "<fmt:message key="label.forecastMonitor.currency"/>";
			label["tip"]["currency"] = "<fmt:message key="tooltip.forecastMonitor.selectCurrency"/>";

			label["text"]["entity"] = "<fmt:message key="label.forecastMonitor.entity"/>";
			label["tip"]["entity"] = "<fmt:message key="tooltip.forecastMonitor.selectEntity"/>";

			label["text"]["breakdown"] = "<fmt:message key="label.forecastMonitor.breakdown"/>";
			label["text"]["mvmntbrkdown"] = "<fmt:message key="label.forecastMonitor.movementId"/>";
			label["tip"]["mvmntbrkdown"] = "<fmt:message key="tooltip.forecastMonitor.mvmntBrkdown"/>";
			label["text"]["bookbrkdown"] = "<fmt:message key="label.forecastMonitor.bookCode"/>";
			label["tip"]["bookbrkdown"] = "<fmt:message key="tooltip.forecastMonitor.bookBrkdown"/>";

			label["text"]["applyccymultiplier"] = "<fmt:message key="label.forecastMonitorOptions.applyCurrencyMultiplier"/>";
			label["tip"]["applyccymultiplier"] = "<fmt:message key="tooltip.forecastMonitorOptions.applyCurrencyMultiplier"/>";
			label["text"]["hideweekend"] = "<fmt:message key="label.forecastMonitorOptions.hideweekend"/>";
			label["tip"]["hideweekend"] = "<fmt:message key="tooltip.forecastMonitorOptions.hideweekend"/>";
			label["text"]["cumulativetotal"] = "<fmt:message key="label.forecastMonitorOptions.cumulativetotal"/>";
			label["tip"]["cumulativetotal"] = "<fmt:message key="tooltip.forecastMonitorOptions.cumulativetotal"/>";
			label["text"]["hidezerovalue"] = "<fmt:message key="label.forecastMonitorOptions.hidezerovalue"/>";
			label["tip"]["hidezerovalue"] = "<fmt:message key="tooltip.forecastMonitorOptions.hidezerovalue"/>";
			label["text"]["hidezerosum"] = "<fmt:message key="label.forecastMonitorOptions.hidezerosum"/>";
			label["tip"]["hidezerosum"] = "<fmt:message key="tooltip.forecastMonitorOptions.hidezerosum"/>";
			label["text"]["hidetotal"] = "<fmt:message key="label.forecastMonitorOptions.hidetotal"/>";
			label["tip"]["hidetotal"] = "<fmt:message key="tooltip.forecastMonitorOptions.hidetotal"/>";
			label["text"]["hideassumption"] = "<fmt:message key="label.forecastMonitorOptions.hideassumption"/>";
			label["tip"]["hideassumption"] = "<fmt:message key="tooltip.forecastMonitorOptions.hideassumption"/>";
			label["text"]["hidescenario"] = "<fmt:message key="label.forecastMonitorOptions.hidescenario"/>";
			label["tip"]["hidescenario"] = "<fmt:message key="tooltip.forecastMonitorOptions.hidescenario"/>";


			label["text"]["usertemplate"] = "<fmt:message key="label.forecastMonitorOptions.usertemplate"/>";
			label["text"]["userbucket"] = "<fmt:message key="label.forecastMonitorOptions.userbucket"/>";

			label["text"]["add"] = "<fmt:message key="button.forecastMonitor.add"/>";
			label["tip"]["add"] = "<fmt:message key="tooltip.forecastMonitor.add"/>";
			label["text"]["change"] = "<fmt:message key="button.forecastMonitor.change"/>";
			label["tip"]["change"] = "<fmt:message key="tooltip.forecastMonitor.change"/>";
			label["text"]["delete"] = "<fmt:message key="button.forecastMonitor.delete"/>";
			label["tip"]["delete"] = "<fmt:message key="tooltip.forecastMonitor.delete"/>";
			label["text"]["save"] = "<fmt:message key="button.forecastMonitor.save"/>";
			label["tip"]["save"] = "<fmt:message key="tooltip.forecastMonitor.save"/>";
			label["text"]["cancel"] = "<fmt:message key="button.forecastMonitor.cancel"/>";
			label["tip"]["cancel"] = "<fmt:message key="tooltip.forecastMonitor.cancel"/>";
			// Modified for mantis 2004 by KaisBS
			label["text"]["noAccessCurrency"] = "<fmt:message key="alert.currencyAccess"/>";
			;
			label["text"]["maxValue"] = "<fmt:message key="alert.forecastMonitorOption.maxValue"/>";

			label["text"]["greaterThanPrevious"] = "<fmt:message key="alert.forecastMonitorOption.greaterThanPrevious"/>";
			label["text"]["lesserThanNext"] = "<fmt:message key="alert.forecastMonitorOption.lesserThanNext"/>";
			label["text"]["bucketExist"] = "<fmt:message key="alert.forecastMonitorOption.bucketExist"/>";

			label["text"]["user-templates"] = "<fmt:message key="label.forecastMonitorOption.userTemplates"/>";
			label["text"]["user-buckets"] = "<fmt:message key="label.forecastMonitorOption.userBuckets"/>";

			label["tip"]["label-hideButtonBar"] = "<fmt:message key="label.hideButtonBar"/>";
			label["tip"]["label-showButtonBar"] = "<fmt:message key="label.showButtonBar"/>";
			label["alert"]["template-validnumber"] = "<fmt:message key="alert.forecasttemplate.validnumber"/>";
			label["alert"]["template-locked"] = "<fmt:message key="alert.forecasttemplate.templatelocked"/>";
			label["alert"]["template-delete"] = "<fmt:message key="alert.forecasttemplate.templatedelete"/>";
			label["alert"]["template-mandatory"] = "<fmt:message key="alert.forecasttemplate.mandatory"/>";
			label["alert"]["template-totalmultiplier"] = "<fmt:message key="alert.forecasttemplate.totalmultiplier"/>";
			label["alert"]["template-columnnumbers"] = "<fmt:message key="alert.forecasttemplate.columnnumbers"/>";

			// If parent refresh, close the window
			<c:if test="${requestScope.parentFormRefresh == 'Y'}">
			window.opener.refreshPending = true;
			self.close();
			</c:if>
			var screenRoute = "ForecastMonitorOptions";
			//variable template Option
			var templateOption = "";
			// get the application name
			var appName = "<%=SwtUtil.appName%>";
			// variable xml Template 
			var xmlTemplate = "";

			/**
			 * sendRequest
			 * This method is used to send the request
			 * @param requestURL
			 * @return Number
			 */
			function sendRequest(requestURL) {
				var oXHR = new XMLHttpRequest();
				oXHR.open("GET", requestURL, false);
				oXHR.send();
				var count = new Number(oXHR.responseText);
				return count;
			}

			/**
			 * help
			 * This function opens the help screen
			 * @return none
			 */
			function help() {
				openWindow(buildPrintURL('print', 'Forecast Monitor Options'), 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no', 'true')
			}

			/**
			 * openTemplateOptionsWindow
			 * This function opens the help screen
			 * @param usertemplate
			 * @return none
			 */
			function openTemplateOptionsWindow(usertemplate) {
				if (usertemplate.entity != "undefined") {
					var param = '/' + appName + '/forecastMonitor.do?method=flexTemplateOption&entity=' + usertemplate.entity + '&currency=' + usertemplate.currency + '&template=' + usertemplate.template;
					templateOption = window.open(param, 'UserTemplateOptions', 'width=760,height=200,toolbar=0, resizable=yes, scrollbars=no, left=10, top=274', 'true');
				} else {
					var param = '/' + appName + '/forecastMonitor.do?method=flexTemplateOption';
					templateOption = window.open(param, 'UserTemplateOptions', 'width=760,height=200,toolbar=0, resizable=yes, scrollbars=no, left=10, top=274', 'true');
				}
				xmlTemplate = usertemplate.xml;

			}

			/**
			 * closeWindow
			 * This function used to close the window
			 * @return none
			 */
			function closeWindow() {
				window.opener.callApp("Y");
				window.close();
			}

			/**
			 * init
			 * This method is called when onload
			 * @return none
			 */
			function init() {
				flag = true;
			}

			/**
			 * reloadOption
			 * This method is used to reload the screen
			 * @param template
			 * @return none
			 */
			function reloadOption(template) {
				// getFlashObject("mySwf").reloadOption(template.entity, template.currency, template.template);
				Main.reloadOption(template.entity, template.currency, template.template);
			}

			/**
			 * unloadCloseWindow
			 * This function is called, when unload the window
			 * @return none
			 */
			function unloadCloseWindow() {
				if (!flag)
					window.close();
				if (templateOption != "") {
					templateOption.close();
				}
				window.opener.callApp("Y");
			}


		</script>
		<%@ include file="/angularscripts.jsp" %>
	<form id="exportDataForm" target="tmp" method="post">
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
	