<?xml version="1.0" encoding="utf-8"?>
<!--
- The main purpose of this jsp file is to load the Forecast Monitor Template screen.
-	And to embed the Add a column to or change a column of Forecast Monitor Template (details)flex screen.
- Also, to load the label values for this screen.
-
- Author(s): Vivekanandan A
- Date: 25-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>






<forecastmonitor columnId="${requestScope.forecastMonitorTemplateColSrc.id.columnId}"
				 ordinalPos="${requestScope.ordinalPos}" displayName="${requestScope.shortName}" description="${requestScope.description}" totalMultiplier="${requestScope.totalMultiplier}">
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key="label.forecasttemplate.column.type"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="type" width="96"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.entity"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="entity" width="93"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.id"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="id" width="200"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.name"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="name" width="236"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.multiplier"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="numval" editable="true" dataelement="multiplier" width="100"/>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach items="${requestScope.listMonitorTemplateDetailsColSrc}" var="listMonitorTemplateDetailsColSrc">
				<row>
					<type negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.id.sourceType}</type>
					<entity negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.entityId}</entity>
					<id negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.id.sourceId}</id>
					<name negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.name}</name>
					<multiplier negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.multiplier}</multiplier>
					<modify negative="false" clickable="false">true</modify>
				</row>
			</c:forEach>
		</rows>
	</grid>
	<selects>
		<select id="columnType">
			<c:forEach items="${requestScope.columnTypeList}" var="columnTypeList">
				<option value="${columnTypeList.value}"
						selected="${requestScope.type == requestScope.forecastMonitorTemplateColSrc.id.sourceId ? '1' : '0'}">${columnTypeList.label}</option>
			</c:forEach>
		</select>
	</selects>
</forecastmonitor>