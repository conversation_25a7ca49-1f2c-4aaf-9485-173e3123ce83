<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><c:if test="${requestScope.methodName == 'save'}">
	<fmt:message key="defaultaccountmaintenance.title.addWindow" />
</c:if><c:if test="${requestScope.methodName == 'update'}">
	<fmt:message key="defaultaccountmaintenance.title.changeWindow" />
</c:if></title>
<script type="text/javascript">
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	window.opener.document.forms[0].method.value = "listview";
	window.opener.document.forms[0].submit();
	self.close();
	</c:if>
	mandatoryFieldsArray=["*"];
	window.onload = function () {
		setParentChildsFocus();
		setFocus(document.forms[0]);
		document.getElementById("currencyDesc").innerText = '${currencyName}';
		<c:if test="${requestScope.methodName == 'save'}">
		var dropBox1 = new SwSelectBox(document.getElementById('currencyCode'),document.getElementById('currencyDesc'));
		</c:if>
		var divElement = document.getElementById("dropdowndiv_2");
		var selectElement = document.forms[0].elements["defaultAcct.acctId"];
		var idElement = document.forms[0].elements["accountId"];
		var descElement = document.forms[0].elements["accountDesc"];
		var arrowElement = document.forms[0].elements["dropdownbutton_2"];
		var idLength = 12;
		var descLength = 30;
		var accountBox = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
		ShowErrMsgWindow('${actionError}');
		changeBorderColor();
		$(document).on('change', '#accountDropDown', function() {  
		      changeBorderColor();
		});
	}
		window.onunload = call;
		/*function $ (id) {
		return document.getElementById (id);
	}*/
		function clearDisabled () {
			document.getElementById('entityId').disabled = false;
			document.getElementById('xrefCode').disabled = false;
			document.getElementById('currencyCode').disabled = false;
	}
	/* Validates the form and submit */
	function submitForm (methodName) {
		elementTrim(document.forms[0]);
		// Validates the form
		if (methodName=="add" || validate()) {
			document.getElementById ('method').value = methodName;
			// Enable the entity id,xrefcode and currency code
			clearDisabled ();
			//submits the form
			document.forms[0].submit();
		}
	}
/*Start:The modified the mantis 1435 on 22-02-2012.The purpose for Tab navigation fields when enter key pressed to display  and close screen*/
/* This Method is used for save or update the data and close the screen when enter key pressed 
 *@param methodname
 */	
   function onKeyEnterSubmit(method,e){
	   var event = (window.event|| e);
        if(event.keyCode == 13){
	    if(method == "update"){
		  submitForm('update');
		}else if(method == "save"){
		  submitForm('save');
		}else if(method == "close"){
	      window.close();
		}else if(method == "print"){
		  printPage();
	   }
	}
}
	/* function validate is used to validate the BIC Code field */
	function validate () {		
		// Checks the empty for xref code
		if (document.getElementById('xrefCode').value.trim()=="") {
			alert ("<fmt:message key="defaultaccountmaintenance.alert.xrefCode"/>");
            	 document.forms[0].elements['xrefCode'].focus();			
		    return false;
		}
		
		// Validates the xrefCode field for alpha,numeric and and the symbols (.,%>/|\\-_+=^;:[]*()?#) only
		if(!document.getElementById ('xrefCode').disabled && !validateField(document.getElementById ('xrefCode'), 'defaultAcct.id.xrefCode', 'alphaNumPatExtended'))
		{
			return false;
		}
		// Checks the empty for account id
		if (document.getElementById('accountId').value=="") {
		    alert ("<fmt:message key="defaultaccountmaintenance.alert.account"/>");
			 document.getElementById("dropdownbutton_2").focus();
			return false;
		}
		return true;
	}		
	 function changeBorderColor() {
		 var element_ = document.getElementById('accountId');
		   if(element_!=null&&typeof element_!='undefined'){
			   element_.style.border = element_.value==""?"red 1px solid":"#a9a9a9 1px solid";
		   }
	    }
	
</script>
</head>
<body>
<form action="defaultacctmaintenance.do" style="top: 50px; margin-top: 25px; height: 250px;">
	<input type="hidden" id="method" name="method" value="" />
	<div id="dropdowndiv_2"
		style="position: absolute; width: 265px; visibility: hidden; z-index: 99 ;top:-30"
		class="swdropdown"><select id="accountDropDown" class="htmlTextFixed"
		name="defaultAcct.acctId" size="10" 
		style=" height: 140px; width:238px;z-index:99;margin-top: 45px;margin-left: 125px;">
		<c:forEach items="${requestScope.accounts}" var="acct">
			<option
				 value="${acct.value}"
				 <c:if test="${defaultAcct.acctId == acct.value}">selected="selected"</c:if>>
				 ${acct.label}
			</option>
		</c:forEach> /></select></div>
 </div>
	<div  style="width: 690px; height: 180px; margin-top: 10px; margin-left: 20px; border: 2px outset; left: 20px">
	<table cellspacing="0" style="height: 160px; ">
		<tr style="height: 30px; ">
			<th style="padding-right: 20; width: 105px"><fmt:message key="label.forecastMonitor.entity"/>*</th>
			<td><input type="text" titleKey="tooltip.entityId"  value="${defaultAcct.id.entityId}"  disabled="true" style="height: 22px"
				name="defaultAcct.id.entityId" id="entityId"/></td>
			<td>${requestScope.entityName}&nbsp;</td>
		</tr>
		<tr style="height: 30px; ">
			<th><fmt:message key="ilmAccountGroupDetails.currency"/>*</th>
			<td><c:if test="${requestScope.methodName == 'save'}">
				<select style="width:55px" id="currencyCode" name="defaultAcct.id.currencyCode" titleKey="tooltip.selectCurrencyId" tabindex="1" onchange="submitForm('add')">
				    <c:forEach items="${requestScope.currencies}" var="ccy">
					   <option
						  value="${ccy.value}"
						  <c:if test="${defaultAcct.id.currencyCode == ccy.value}">selected="selected"</c:if>>
						  ${ccy.label}
					   </option>
					</c:forEach></select>
			</c:if> <c:if test="${requestScope.methodName == 'update'}">
				<input type="text" titleKey="tooltip.selectCurrencyId" style="width:55px" id="currencyCode"
					name="defaultAcct.id.currencyCode" value="${defaultAcct.id.currencyCode}" disabled="true"  />
			</c:if></td>
			<td><span class="spantext" id="currencyDesc"></span></td>
		</tr>
		<tr style="height: 30px; ">
			<th style="padding-left: 30"><fmt:message key="defaultaccountmaintenance.BIC"/>*</th>
			<td><c:if test="${requestScope.methodName == 'update'}">
				<input type="text" titleKey="tooltip.enterBIC" value="${defaultAcct.id.xrefCode}" tabindex="2"  id="xrefCode" name="defaultAcct.id.xrefCode" style="height: 22px"
					disabled="true" />
			</c:if> <c:if test="${requestScope.methodName == 'save'}">
				<input type="text" titleKey="tooltip.enterBIC"  value="${defaultAcct.id.xrefCode}" tabindex="2" id="xrefCode" style="height: 22px"
					name="defaultAcct.id.xrefCode" maxlength="11" />
			</c:if></td>
			<td>&nbsp;</td>
		</tr>
		<tr style="height: 30px; ">
			<th style="padding-right:6px"><fmt:message key="ilaapgeneral.account"/>*</th><td><input class="textAlpha" id="accountId" name="accountId"
				style="width: 220px;height: 22px;margin-bottom: 0px;"  readonly="readonly"
				title='<fmt:message key="tooltip.accountId"/>'>
				<c:if test="${requestScope.methodName == 'save'}"><input id="dropdownbutton_2" tabindex="3"
					title='<fmt:message key="tooltip.movAccount"/>' type="button"
					value="..." /></c:if>
				<c:if test="${requestScope.methodName == 'update'}">
			<input id="dropdownbutton_2" tabindex="1"
					title='<fmt:message key="tooltip.movAccount"/>' type="button"
					value="..." /></c:if></td><td><input type="text" id="accountDesc" size="20"
				style="width: 305px; background: transparent; border: thin;"  tabindex="-1"
				onpropertychange="changeBorderColor()" value="" readonly /></td>
		</tr>
	</table>
	</div>
	<c:if test="${requestScope.methodName == 'update'}">
		<div  style="width: 690px;height:40px;border: outset;top: 5px; position: relative;left: 20px">
		<div id="ddimagebuttons" style="top: 3px ;position: relative; "><span><a
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
			tabindex="2" onMouseDown="expandbutton(this)"
			onMouseUp="highlightbutton(this)" onclick="submitForm('update');"
			title='<fmt:message key="tooltip.save"/>' onKeydown = "onKeyEnterSubmit('update',event);"><fmt:message key="button.save" /></a></span> <span><a
			title='<fmt:message key="tooltip.cancel"/>'
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
			onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
			onclick="window.close();" tabindex="3" onKeydown ="onKeyEnterSubmit('close',event);"><fmt:message key="button.cancel" /></a></span></div>
		<div id="container-icons" style="left: 470px; top: 5px; position: relative;width: 200px;"><span><a tabindex="4" href=#
			onclick="javascript:openWindow(buildPrintURL('print','Default Account Maintenance - Change'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
			onMouseOut="MM_swapImgRestore()"
			onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)" ><img
			src="images/help_default.GIF " name="Help" border="0"
			title='<fmt:message key="tooltip.helpScreen"/>'></a></span> <span
			id="Print"><a tabindex="5" onKeydown ="onKeyEnterSubmit('print',event);" onclick="printPage();"
			onMouseOut="MM_swapImgRestore()"
			onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)" ><img
			src="images/Print.gif " name="Print" border="0"
			title='<fmt:message key="tooltip.printScreen"/>'></a></span></div>
		</div>
	</c:if>
	<c:if test="${requestScope.methodName == 'save'}">
		<div style="width: 690px;height:40px; border: 2px outset; top: 5px; position: relative; padding-top: 3px; left: 20px;" >
		<div id="ddimagebuttons"  style=" position: relative; "><span><a
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" 
			tabindex="4" onMouseDown="expandbutton(this)"
			onMouseUp="highlightbutton(this)" onclick="submitForm('save');"
			title='<fmt:message key="tooltip.save"/>'  onKeydown = "onKeyEnterSubmit('save',event);"><fmt:message key="button.save" /></a></span> <span><a 
			title='<fmt:message key="tooltip.cancel"/>'
			onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)"
			onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
			onclick="window.close();" tabindex="5" onKeydown ="onKeyEnterSubmit('close',event);"><fmt:message key="button.cancel" /></a></span></div>
		<div id="container-icons" style="left: 470px; top: 5px; position: relative;width: 200px;"><span><a tabindex="5"
			tabindex="6" href=#
			onclick="javascript:openWindow(buildPrintURL('print','Default Account Maintenance - Add'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
			onMouseOut="MM_swapImgRestore()"
			onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
			src="images/help_default.GIF " name="Help" border="0"
			title='<fmt:message key="tooltip.helpScreen"/>'></a></span> <span
			id="Print"><a tabindex="7"  onKeydown ="onKeyEnterSubmit('print',event);" onclick="printPage();"
			onMouseOut="MM_swapImgRestore()"
			onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
			src="images/Print.gif " name="Print" border="0"
			title='<fmt:message key="tooltip.printScreen"/>'></a></span></div>
		</div>
		</c:if>
<!--/*End:The modified the mantis 1435 on 22-02-2012.The purpose for Tab navigation fields when enter key pressed to display  and close screen*/-->
</form>
</body>
</html>