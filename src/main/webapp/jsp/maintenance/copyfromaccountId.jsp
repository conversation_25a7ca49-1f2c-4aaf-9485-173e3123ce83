<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="acctmaintenance.title.cpyfrom" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="style/displaytag.css">

<SCRIPT language="JAVASCRIPT">
var newAcctId = '${requestScope.newAcctId}';
/* Start:Code modified by sudhakar on 22-12-2011:Mantis1592: Account Maintenance screen allows to create account for entity that has no currency access */
var selectedCurrencyCode='${requestScope.selectedCurrencyCode}';
var accountFlag=true;
var linkAccSelectElement=null;
var linkSwSel = null;
/**
  * This method is used to update the Add accountmaintenance window while clicking on the ok button.
  * 			
  *  @param methodName
  */
function submitForm(methodName){
		if(validateForm(document.forms[0]) ){
			window.opener.document.forms[0].selectedAccountId.value=document.forms[0].elements['acctMaintenance.id.accountId'].value;
			window.opener.document.forms[0].method.value = methodName;
			window.opener.document.forms[0].newAcctId.value = newAcctId;
			/* Code Added For Mantis 1562 by Sudhakar on 12-12-2011:Account Maintenance Main screen: currency selection changes after adding a new account*/
			window.opener.document.forms[0].selectedCurrencyCode.value = selectedCurrencyCode;
			self.close();
			window.opener.document.forms[0].submit();
	
		}
}
function validateForm(objForm){
  var elementsRef = new Array(1);
 
  elementsRef[0] = objForm.elements["acctMaintenance.id.accountId"];
  
  
  return validate(elementsRef);
}

function onbodyLoad()
{

var currencyDropBox = new SwSelectBox(document.forms[0].elements["acctMaintenance.currcode"],document.getElementById("currencyNameDisp"));
changeBorderColor();
$(document).on('change', '#accountDropDown', function() {  
    changeBorderColor();
});
}
/**
  * getLinkAccountList
  * 			
  * This method is used to get the collection of link account.  
  */
function getCopyFromAccountList(event) {
	    //get the all elements for selectbox
		if(accountFlag){
		var divElement = document.getElementById("dropdowndiv_2");
		var idElement = document.forms[0].elements["accountId"];
		var descElement = document.forms[0].elements["accountName"];
		var arrowElement = document.forms[0].elements["dropdownbutton_2"];
		linkAccSelectElement = document.forms[0].elements["acctMaintenance.id.accountId"];
		var idLength = 12;
		var descLength = 30;
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var entityId = '${requestScope.entityId}';
		var currencyCode = document.forms[0].elements["acctMaintenance.currcode"].value;
		
		//get the requestURL 
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		//frame the requestURL
		requestURL = requestURL + appName+"/acctMaintenance.do?method=getCopyFromAccountList";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		//send the request for list
		oXMLHTTP.send();
		//get the response text
		var accountListValue=new String(oXMLHTTP.responseText);
		accountListValue = accountListValue.split('\n');
		//add the account into option	
		var index = 0;
		for(var i=0;i < accountListValue.length -1;i++){
			var labelValue = accountListValue[i].split('~~~');
			var option = document.createElement("option");
			option.text = labelValue[0];	
			option.value = labelValue[1];
			linkAccSelectElement.options.add(option);
		}
		//set the selected index
		linkAccSelectElement.selectedIndex = index;
		//frame the selectbox component 
		linkSwSel = new SwMainSelectBox(divElement,linkAccSelectElement,idElement,descElement,arrowElement,idLength,descLength);
		linkSwSel.setClickFlag();
		//call to populate the list in the list box
		linkSwSel.arrowElementOnClick(event);
		accountFlag = false;
	 }	
	}
/**
 * changeCurrencyCode
 * 			
 * This method is called while changing the currency code to empty the collection in list box.  
 */
	function changeCurrencyCode(){
		accountFlag = true;	
		//clear the link account list and value
		if(linkAccSelectElement != null && linkAccSelectElement.options != null)
			linkAccSelectElement.options.length =0;
		if(linkSwSel != null){
			linkSwSel.arrowElement.onclick = getCopyFromAccountList;
			linkSwSel.idElement.value =""; 
			linkSwSel.descElement.value =""; 	
		}	
	}
	
	 function changeBorderColor() {
		 var element_ = document.getElementById('accountId');
		   if(element_!=null&&typeof element_!='undefined')
		         element_.style.border = element_.value==""?"red 1px solid":"#a9a9a9 1px solid";
			 
	    }
	
</SCRIPT>

</head>


<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}'); onbodyLoad()"
	onunload="call()">

<form action="acctMaintenance.do">
	<input name="method" type="hidden" value="copy">
	<input name="selectedCurrencyCode" type="hidden" value="">
	<!-- Code modified by sudhakar on 22-12-2011:Mantis1553: Account maintenance 'Copy from' account combo-box is too small -->
	<div id="dropdowndiv_2"
		style="z-index: 99; position: absolute; width: 200px; left: 104px; top: 80px; visibility: hidden"
		class="swdropdown"><select id="accountDropDown" cssClass="htmlTextFixed"
		name="acctMaintenance.id.accountId" size="10"
		style="width:329px;z-index:99;">
		<option value="${value}">${label}</option>

	</select></div>
	<div id="acctMaintenanceCopyFrom" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 20px; width: 650px; height: 114px;">
	<div id="acctMaintenanceCopyFrom"
		style="position: absolute; z-index: 99; left: 8px; top: 4px; width: 255px; height: 20px;">

	<table width="370" height="30" border="0" cellspacing="0"
		cellpadding="0">
		<tr height="26">
			<td width="35"><b><fmt:message key="acct.currency.id" /></b></td>
			<td width="12px">&nbsp;</td>
			<td width="300"><select name="acctMaintenance.currcode"
				tabindex="6" style="width:60px" titleKey="tooltip.currencyCode"
				onchange="changeCurrencyCode()">
    				<c:forEach var="item" items="${requestScope.acctmaintenancelistadd}">
       					<option value="${item.value}" <c:if test="${acctMaintenance.currcode == item.value}">selected</c:if>>${item.label}</option>
    				</c:forEach>
			</select> &nbsp;&nbsp;&nbsp; <span id="currencyNameDisp"
				name="currencyNameDisp" class="spantext" /></td>
		</tr>
	</table>

	<table width="640" height="26" border="0" cellspacing="0"
		cellpadding="0">
		<tr height="29">
			<td width="35"><b><fmt:message key="acct.id" /></b>*</td>
			<td width="43px">&nbsp;</td>
			<td width="350px"><input class="textAlpha" name="accountId"
				tabindex="2" style="width: 220px; height: 22px; margin-right: 5px; "
				title='<fmt:message key="tooltip.AccountID"/>' readonly="readonly"><input
				title='<fmt:message key="tooltip.ClicktoSelectA/cId"/>'
				id="dropdownbutton_2" type="button" value="..."
				onclick="getCopyFromAccountList(event)"></td>
			<td width="28px">&nbsp;</td>
			<td width="280"><input class="textAlpha" readonly="readonly"
				style="width: 320px; background: transparent; border: thin;"onpropertychange="changeBorderColor()"
				name="accountName" size="20"></td>
		</tr>
	</table>

	</div>
	</div>
	<div id="AccountMaintenance"
		style="position: absolute; left: 585; top: 149px; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="4" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Copy From Account Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a tabindex="4"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 141px; width: 650px; height: 39px; visibility: visible;">

	<div id="acctMaintenanceCopyFrom"
		style="position: absolute; left: 2; top: 1; width: 140px; height: 15px; visibility: visible;">
	<table>
		<tr>
			<td width="70" title='<fmt:message key="tooltip.ok"/>'><a
				tabindex="2" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitForm('copy')"><fmt:message key="button.ok" /></a></td>

			<td id="closebutton" width="70px" tabindex="3"
				title='<fmt:message key="tooltip.close"/>'><a
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<!-- End:Code modified by sudhakar on 22-12-2011:Mantis1592: Account Maintenance screen allows to create account for entity that has no currency access  -->
</form>

</body>
</html>
