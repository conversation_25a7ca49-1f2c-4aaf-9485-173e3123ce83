<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title>

<c:choose>
    <c:when test="${requestScope.methodName != 'change'}">

	<fmt:message key="generalsysparam.mainScreen"/>

    </c:when>
    <c:otherwise>
		<fmt:message key="generalsysparam.changescreen"/>

    </c:otherwise>
</c:choose>

</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';

var cancelcloseElements = new Array(2);

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var selectedTimeZoneRegion="";
var selectedTimeZoneOffset="";

cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";

function printPage()
{
	window.print();
}

/**
 * When window is load, find each 'tr' that contains a 'td'  has an input
 * which value contains  '*',
 * then apply the style 'required' for each empty input
 * as well as this jquery code call 'validateSummerTimesDates()' function to validate the summer time
 * each time we try to fill the (start/end)date fields
 *
 * Added by Med Amine Ben Ahmed for M 2352 ILAAP
 *
 **/
 $(window).on("load",function(){

	$("input[type='text']").filter("[tabIndex='11'],[tabIndex='9']").not('[disabled="disabled"]').on('blur keyup', function(){
		 if ($(this).val() == '')
             $(this).css('border-color','red');
         else
             $(this).css('border-color','');
	});

	$("input[type='text']").not('[disabled="disabled"]').css('background','white');
	$("input[type='checkbox']").on('click', function(){
	 	$("input[type='text']").removeClass('is-disabled');
	   	$("input[disabled='disabled'][type='text']").addClass('is-disabled');
	    $("input[disabled='disabled'][type='text']").css('background','#e5e5e5');
	    $("input[type='text']").css('border-color','');
	    $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function() {
	    	// Get the current tr
	    	var tr = $(this);
        	tr.find('input').removeClass();
        	tr.find('input').css('background-color','');
        	tr.find('input').addClass('inputText');
	    	tr.find('input').css('border-color',function(index){
					if ($(this).val() == '')
					return 'red';
			}).on('blur keyup', function(){
                        if ($(this).val() == '')
                            $(this).css('border-color','red');
                        else
                            $(this).css('border-color','');
                 });
	    });
	});
});

 function validateSummerTimesDates(){
	  /* var validate = comparedates(document.forms[0].elements['sysParams.dstStartDateAsString'].value,
			                     document.forms[0].elements['sysParams.dstEndDateAsString'].value,dateFormat,'Start Date','End Date');*/
	   return true;
}

/**
  * This method is used to submit form while clicking on the save button.
  *
  * @Param methodName
  */
function submitForm(methodName){

   /*Start: Code modified For Mantis 1687 by Sudhakar on 17-01-2012:Parameterise retention time for suppressed and rejected messages */
 //Validate the date,systemlog,maintenancelog,errorlog,rejectedSuppressedInput filed and set field focus to the same.
  if(methodName == 'update')
  {
    if(document.forms[0].elements['sysParams.systemLog'].value == "")
			document.forms[0].elements['systemLog'].value=null;
	if(document.forms[0].elements['sysParams.maintenanceLog'].value == "")
			document.forms[0].elements['maintenanceLog'].value = null;
	if(document.forms[0].elements['sysParams.errorLog'].value == "")
			document.forms[0].elements['errorLog'].value = null;
	if(document.forms[0].elements['sysParams.rejectedSuppressedInput'].value == "")
			document.forms[0].elements['rejectedSuppressedInput'].value = null;
	if(dateFormat == 'datePat2')
	{
		if(validateField(document.forms[0].elements['sysParams.dateAsString'],'testDateAsString','datePat2'))
		{

					if(validateSummerTimesDates()){
		    			var sysLogValue=validateField(document.forms[0].elements['sysParams.systemLog'],'Number','numberPat');
			  			if(sysLogValue){
			    			var mainLogValue=validateField(document.forms[0].elements['sysParams.maintenanceLog'],'Number','numberPat');
			    			if(mainLogValue){
				  				var errorLogValue=validateField(document.forms[0].elements['sysParams.errorLog'],'Number','numberPat');
								if(errorLogValue){
									var rejSuppInputValue=validateField(document.forms[0].elements['sysParams.rejectedSuppressedInput'],'Number','numberPat');
									if(rejSuppInputValue){
										enableFields();
										document.forms[0].method.value = methodName;
										document.forms[0].submit();
									}else{
										document.forms[0].elements['sysParams.rejectedSuppressedInput'].focus();
									}
								}else{
			   						document.forms[0].elements['sysParams.errorLog'].focus();
								}
							}else{
			  					document.forms[0].elements['sysParams.maintenanceLog'].focus();
							}
						}
					}else{
			   			document.forms[0].elements['sysParams.systemLog'].focus();
					}
		}
	}else if(dateFormat == 'datePat1'){
		if(validateField(document.forms[0].elements['sysParams.dateAsString'],'testDateAsString','datePat1')){
					if(validateSummerTimesDates()){
						var sysLogValue=validateField(document.forms[0].elements['sysParams.systemLog'],'Number','numberPat');
						if(sysLogValue){
							var mainLogValue=validateField(document.forms[0].elements['sysParams.maintenanceLog'],'Number','numberPat');
							if(mainLogValue){
								var errorLogValue=validateField(document.forms[0].elements['sysParams.errorLog'],'Number','numberPat');
								if(errorLogValue){
							  		var rejSuppInputValue=validateField(document.forms[0].elements['sysParams.rejectedSuppressedInput'],'Number','numberPat');
							  		if(rejSuppInputValue){
										enableFields();
										document.forms[0].method.value = methodName;
										document.forms[0].submit();
									}else{
										document.forms[0].elements['sysParams.rejectedSuppressedInput'].focus();
									}
								}else{
									document.forms[0].elements['sysParams.errorLog'].focus();
								}
							}else{
				  				document.forms[0].elements['sysParams.maintenanceLog'].focus();
							}
						}else{
				   			document.forms[0].elements['sysParams.systemLog'].focus();
						}
					}
		}
	}
  }else{
    enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
  }
    /*End: Code modified For Mantis 1687 by Sudhakar on 17-01-2012:Parameterise retention time for suppressed and rejected messages */
}
// Modified by Med Amine for M 2352 : ILAAP
function validateDateField(elem,fieldName){
   if(dateFormat == 'datePat2'){
		if(validateField(document.forms[0].elements[elem],fieldName,'datePat4')){
			document.forms[0].method.value = 'refresh';
			enableFields();
			document.forms[0].submit();
		}

	}
	else if(dateFormat == 'datePat1'){

		if(validateField(document.forms[0].elements[elem],fieldName,'datePat1')){
			document.forms[0].method.value = 'refresh';
			enableFields();
			document.forms[0].submit();
		}

	}
}


function isOk() {
	window.focus();
    window.close();
}


function submitCancel(methodName){


	<%
	String canButSts = (String) request.getAttribute(SwtConstants.CAN_BUT_STS);
	if (canButSts != null && canButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	  let errmsg = '<fmt:message key="general.closeConfirm"/>';
	  ShowErrMsgWindowWithBtn("", errmsg, OK_CANCEL, isOk);	<%
	} else {
	%>
    document.forms[0].method.value = methodName;
    window.close();	<%
	}
	%>
}
function submitMaintain(actionMethod){
	var appName = "<%=SwtUtil.appName%>";
	var param = '/' + appName + '/sysParams.do?method='+actionMethod;
		param = param +"&screenFieldsStatus="+${requestScope.screenFieldsStatus};

	mainWindow = window.open (param, 'summaryScreen','left=10,top=230,width=530,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');
	return false;
}

/**
  * This method is called before the form submit to be needed to enable to get the values.
  *
  */
function enableFields(){
	document.forms[0].elements["sysParams.systemLog"].disabled = "";
	document.forms[0].elements["sysParams.maintenanceLog"].disabled = "";
	document.forms[0].elements["sysParams.errorLog"].disabled = "";
	document.forms[0].elements["sysParams.dateAsString"].disabled = "";
	document.forms[0].elements["sysParams.dateFormat"].disabled = "";
	document.forms[0].elements["sysParams.amountDelimiter"].disabled = "";
	document.forms[0].elements["sysParams.sysTimeZone"].disabled = "";


	//Code Added For Mantis 1687 by Sudhakar on 17-01-2012:Parameterise retention time for suppressed and rejected messages
	document.forms[0].elements["sysParams.rejectedSuppressedInput"].disabled = "";
}

/**
 * Added by Med Amine for M 2352 : ILAAP
 * This function called each time the checked state of enableDaylight CheckBox was changed :
 * This checked state  will cause the Start/End Date fields to be enabled or disabled as appropriate
 */
function updateSummerTimeDaylight(){
	var enableDaylightCheckBox  = document.forms[0].elements['sysParams.enableDst'];
	if(enableDaylightCheckBox .checked == true){
		document.forms[0].elements['sysParams.enableDst'].value = 'Y';
		if ("${requestScope.screenFieldsStatus}" == "false" ){
			document.getElementById('mandatoryDstStartDate').innerHTML ="*";
			document.getElementById('mandatoryDstEndDate').innerHTML ="*";
			//getAppropriateDStValues();
		}
	}else{
		document.forms[0].elements['sysParams.enableDst'].value = 'N';
	    document.getElementById('mandatoryDstStartDate').innerHTML = "";
		document.getElementById('mandatoryDstEndDate').innerHTML = "";
	}

}

function closeWindow(){
	window.close();
}


function raiseAlert(){
	var yourstate=window.confirm('<fmt:message key="generalParams.timeZoneRegion.alert"/>');
	if (!yourstate){
	document.forms[0].elements["sysParams.sysTimeZone"].value=selectedTimeZoneRegion;
	document.getElementById("timeZoneOffset").innerHTML= selectedTimeZoneOffset;
    }
}


function bodyOnLoad(){
	var menuaccessId = document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	var dropBox1 = new SwSelectBox(document.forms[0].elements["sysParams.sysTimeZone"],document.getElementById("timeZoneOffset"));
	selectedTimeZoneRegion=  document.forms[0].elements["sysParams.sysTimeZone"].value;
	selectedTimeZoneOffset= document.getElementById("timeZoneOffset").innerHTML;

	document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;

	/*Modified by Imed B to disable/enable button change as appropriate for mantis ILAAP : M 2352 */

	<%
	String saveButSts = (String) request.getAttribute(SwtConstants.SAV_BUT_STS);
	if (saveButSts != null && saveButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	<%
	} else {
	%>
	document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	<%
	}
	%>



	<%
	String chgButSts = (String) request.getAttribute(SwtConstants.CHG_BUT_STS);
	if (chgButSts != null && chgButSts.equals(SwtConstants.STR_TRUE)) {
	%>
 	if(menuaccessId == 0)
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
 	else
 		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	<%
	} else {
	%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%
	}
	%>

	<%
	if (canButSts != null && canButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
	<%
	} else {
	%>
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
 	if(menuaccessId == 0)
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
 	else
 	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%
	}
	%>

}

</SCRIPT>
<script language="JAVASCRIPT">
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var cal = new CalendarPopup("caldiv"); 
	cal.offsetX = -15;
	cal.offsetY = -25;
	
</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad(); "
	onunload="call();">
	<form action="sysParams.do">
		<input name="amountDelimiter" type="hidden" value="${amountDelimiter}">
		<input name="testDate" type="hidden" value="${testDate}">
		<input name="dateFormat" type="hidden" value="${dateFormat}">
		<input name="dateFormatDisplay" type="hidden" value="${dateFormatDisplay}">
		<input name="errorLog" type="hidden" value="${errorLog}">
		<!--Start: Code modified by sudhakar on 17-01-2012:1687: Parameterise retention time for suppressed and rejected messages -->
		<input name="rejectedSuppressedInput" type="hidden"
			value="${rejectedSuppressedInput}">
		<input name="maintenanceLog" type="hidden" value="${maintenanceLog}">
		<input name="systemLog" type="hidden" value="${systemLog}">
		<input name="method" type="hidden" value="list">
		<bean:define id="CDM" name="CDM"
			type="org.swallow.util.CommonDataManager" scope="session" />

		<input name="menuAccessId" type="hidden">
		<div id="GeneralParameter"
			style="position: absolute; left: 20px; top: 25px; width: 523px; height: 250px; border: 2px outset;"
			color="#7E97AF">
			<div style="position: absolute; left: 6px; top: 0px; width: 480px; height: 120px;">
				<fieldset style="width: 430px; border: 2px groove; height: 130px;">
					<legend>
						<fmt:message key="generalsystem.generalSettings" />
					</legend>
					<table width="500px" border="0" cellspacing="0" cellpadding="0">
						<tr height="24px">
							<td width="128px"><b>&nbsp;&nbsp;<fmt:message key="dateFormat" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="115px">
			              <c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="radio" id="1" tabindex="1"
									titleKey="tooltip.selectDateFormatDMY"
									style="width:13px;margin-bottom: 5px;"
									name="sysParams.dateFormat"
									${sysParams.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}"
									disabled="true" />
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
									<input type="radio" id="1" tabindex="1"
									titleKey="tooltip.selectDateFormatDMY"
									style="width:13px;margin-bottom: 5px;"
									name="sysParams.dateFormat"
									${sysParams.dateFormat == '1' ? 'checked="checked"' : ''} value="${'1'}"/>
							</c:if>
									<label  for="1"
											titleKey="tooltip.selectDateFormatDMY" 
											style="height: 19px;">
										<fmt:message key="dateFormatDDMMYY" />
									</label>
							</td>
							<td width="112px"
								style="margin-bottom: 8px; padding-bottom: 5px;">
								<c:if test="${requestScope.screenFieldsStatus == 'true'}">
								<input type="radio"
									id="2" tabindex="2" titleKey="tooltip.selectDateFormatMDY"
									style="width:13px;" name="sysParams.dateFormat"
									${sysParams.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}"
									disabled="true" /> 
								</c:if>
								<c:if test="${requestScope.screenFieldsStatus != 'true'}">
								<input type="radio"
									id="2" tabindex="2" titleKey="tooltip.selectDateFormatMDY"
									style="width:13px;" name="sysParams.dateFormat"
									${sysParams.dateFormat == '2' ? 'checked="checked"' : ''} value="${'2'}" />
								</c:if>
										<label 	for="2"
												titleKey="tooltip.selectDateFormatMDY">
												<fmt:message key="dateFormatMMDDYY" />
										</label>
							</td>
						</tr>

						<tr height="24px">
							<td width="128px"><b>&nbsp;&nbsp;<fmt:message key="amountDelimiter" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="115px">
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="radio" id="3" tabindex="3"
									titleKey="tooltip.selectAmountFormat"
									name="sysParams.amountDelimiter" value="1"
									<c:if test="${sysParams.amountDelimiter == '1'}">checked="checked"</c:if>
									style="width:13;" disabled="true" />
							</c:if>
									
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
							<input type="radio" id="3" tabindex="3"
									titleKey="tooltip.selectAmountFormat"
									name="sysParams.amountDelimiter" value="1"
									<c:if test="${sysParams.amountDelimiter == '1'}">checked="checked"</c:if>
									style="width:13;"/>
							</c:if>
								<label for="3" titleKey="tooltip.selectAmountFormat"><fmt:message key="amountcomabeforedecimal" /></label></td>
							<td width="112px">
							
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="radio" id="4" tabindex="4"
									titleKey="tooltip.selectAmountFormat"
									name="sysParams.amountDelimiter" value="2"
									<c:if test="${sysParams.amountDelimiter == '2'}">checked="checked"</c:if>
									style="width:13;" disabled="true" />
									
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
								<input type="radio" id="4" tabindex="4"
									titleKey="tooltip.selectAmountFormat"
									name="sysParams.amountDelimiter" value="2"
									<c:if test="${sysParams.amountDelimiter == '2'}">checked="checked"</c:if>
									style="width:13;" />
							</c:if>
								<label for="4" titleKey="tooltip.selectAmountFormat"><fmt:message key="amountcomaafterdecimal" /></label></td>
						</tr>


						<tr height="24px">
							<td width="140px"><b>&nbsp;&nbsp;<fmt:message key="testDateAsString" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="50px" colspan="2"  style="padding-left: 5px">
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="text" tabindex="5"
									titleKey="tooltip.systemDateTesting"
									value="${sysParams.dateAsString}"
									class="htmlTextNumeric" name="sysParams.dateAsString"
									style="width:79px; margin-bottom:5px;height: 21px;"
									maxlength="10" disabled="true"
									onchange="validateDateField('sysParams.dateAsString','testDateAsString');" />
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
								<input type="text" tabindex="5"
									titleKey="tooltip.systemDateTesting"
									value="${sysParams.dateAsString}"
									class="htmlTextNumeric" name="sysParams.dateAsString"
									style="width:79px; margin-bottom:5px;height: 21px;"
									maxlength="10" 
									onchange="validateDateField('sysParams.dateAsString','testDateAsString');" />
							</c:if>
								<A titleKey='<fmt:message key="tooltip.systemDateTesting"/>'
								tabindex="6" name="datelink" ID="datelink"  
								onClick="cal.select(document.forms[0].elements['sysParams.dateAsString'],'datelink',dateFormatValue); return false;"><img
									src="images/calendar-16.gif"></A></td>
<!-- 							<td width="112px">&nbsp; -->
<!-- 							<td> -->
						</tr>

						<tr height="24px">
			<td width="128px"><b style="width: 128px">&nbsp;&nbsp;<fmt:message key="tooltip.timeZoneRegion" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="320px" colspan="2"  style="padding-left: 5px">
																		

			<c:if test="${requestScope.screenFieldsStatus == 'true'}">
		 	<select  name="sysParams.sysTimeZone"  disabled="true" tabindex="1" 
		 	 onchange="raiseAlert()" titleKey="tooltip.timeZoneRegion" class="htmlTextAlpha" style="width:260px">
    		<c:forEach items="${requestScope.timeZoneRegionsList}" var="timeZoneRegion">
               <option
                  value="${timeZoneRegion.value}"
                  <c:if test="${sysParams.sysTimeZone == timeZoneRegion.value}">selected="selected"</c:if>>
                  ${timeZoneRegion.label}
               </option>
			</c:forEach>
			</select>
			</c:if>
			<c:if test="${requestScope.screenFieldsStatus != 'true'}">
			<select  name="sysParams.sysTimeZone"  tabindex="1" 
		 	 onchange="raiseAlert()" titleKey="tooltip.screenName" class="htmlTextAlpha" style="width:260px">

    		 <c:forEach items="${requestScope.timeZoneRegionsList}" var="timeZoneRegion">
               <option
                  value="${timeZoneRegion.value}"
                  <c:if test="${sysParams.sysTimeZone == timeZoneRegion.value}">selected="selected"</c:if>>
                  ${timeZoneRegion.label}
               </option>
			</c:forEach>

			</select>
			</c:if>
			&nbsp;
			<span id="timeZoneOffset" name="#request.timeZoneOffset"
				class="spantext"></td>
						</tr>

					</table>
				</fieldset>
			</div>

			<DIV ID="caldiv"
				STYLE="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></DIV>

			<div id="GeneralParameter"
				style="position: absolute; left: 6; top: 122; width: 480; height: 120px; visibility: visible;">
				<fieldset style="width: 460px; border: 2px groove; height: 120px;">
					<legend>
						<fmt:message key="generalsystem.retentionParameters" />
					</legend>
					<table width="500px" border="0" cellspacing="0" cellpadding="0">
						<tr height="24px">
							<td width="255px">&nbsp;&nbsp;<b><fmt:message key="systemlog" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="45px">
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="text" tabindex="12"
									titleKey="tooltip.enterDaysRetainSysLog"
									class="htmlTextNumeric" name="sysParams.systemLog"
									value="${sysParams.systemLog}"
									maxlength="4" style="width:45px;"
									disabled="true"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
								<input type="text" tabindex="12"
									titleKey="tooltip.enterDaysRetainSysLog"
									class="htmlTextNumeric" name="sysParams.systemLog"
									value="${sysParams.systemLog}"
									maxlength="4" style="width:45px;"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							</td>
							<td width="20px">&nbsp;</td>
							<td width="40px"><b><fmt:message key="days" /></b></td>
						</tr>

						<tr height="24px">
							<td width="255px">&nbsp;&nbsp;<b><fmt:message key="maintenanceLog" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="45px">
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="text" tabindex="13"
									titleKey="tooltip.enterDaysRetainMaintenanceLog"
									class="htmlTextNumeric"
									name="sysParams.maintenanceLog" maxlength="4"
									value="${sysParams.maintenanceLog}"
									style="width:45px;"
									disabled="true"
									onchange="return validateField(this,'Number','numberPat')" />
								</c:if>

								<c:if test="${requestScope.screenFieldsStatus != 'true'}">
									<input type="text" tabindex="13"
									titleKey="tooltip.enterDaysRetainMaintenanceLog"
									class="htmlTextNumeric"
									name="sysParams.maintenanceLog" maxlength="4"
									value="${sysParams.maintenanceLog}"
									style="width:45px;"
									onchange="return validateField(this,'Number','numberPat')" />
								</c:if>
									
								</td>
							<td width="20px">&nbsp;</td>
							<td width="40px"><b><fmt:message key="days" /></b></td>
						</tr>

						<tr height="24px">
							<td width="255px">&nbsp;&nbsp;<b><fmt:message key="errorLog" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="45px">
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="text" tabindex="14"
									titleKey="tooltip.enterDaysRetainErrorLog"
									class="htmlTextNumeric" name="sysParams.errorLog"
									value="${sysParams.errorLog}"
									maxlength="4" style="width:45px;"
									disabled="true"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
								<input type="text" tabindex="14"
									titleKey="tooltip.enterDaysRetainErrorLog"
									class="htmlTextNumeric" name="sysParams.errorLog"
									value="${sysParams.errorLog}"
									maxlength="4" style="width:45px;"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							</td>
							<td width="20px">&nbsp;</td>
							<td width="40px"><b><fmt:message key="days" /></b></td>
						</tr>
						<tr height="24px">
							<td width="255px">&nbsp;&nbsp;<b><fmt:message key="rejectedSuppressedInput" /></b></td>
							<td width="28px">&nbsp;</td>
							<td width="45px">
							
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">
							<input type="text" tabindex="15"
									titleKey="tooltip.enterDaysRejectedSuppressedInput"
									class="htmlTextNumeric"
									name="sysParams.rejectedSuppressedInput" maxlength="4"
									value="${sysParams.rejectedSuppressedInput}"
									style="width:45px;"
									disabled="true"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							<c:if test="${requestScope.screenFieldsStatus != 'true'}">
									<input type="text" tabindex="15"
									titleKey="tooltip.enterDaysRejectedSuppressedInput"
									class="htmlTextNumeric"
									name="sysParams.rejectedSuppressedInput" maxlength="4"
									value="${sysParams.rejectedSuppressedInput}"
									style="width:45px;"
									onchange="return validateField(this,'Number','numberPat')" />
							</c:if>
							</td>
							<td width="20px">&nbsp;</td>
							<td width="40px"><b><fmt:message key="days" /></b></td>
						</tr>
					</table>
				</fieldset>
			</div>
		</div>

		<div id="general"
			style="position: absolute; left: 470; top: 300px; width: 70px; height: 39px; visibility: visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td align="Right"><a tabindex="16" href=#
						onclick="javascript:openWindow(buildPrintURL('print','System Parameter'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
							src="images/help_default.GIF " name="Help" border="0"
							titleKey='<fmt:message key="tooltip.helpScreen"/>'></a></td>

					<td align="right" id="Print"><a tabindex="17" tabindex="6"
						onclick="printPage();" onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							titleKey='<fmt:message key="tooltip.printScreen"/>'></a></td>
				</tr>
			</table>
		</div>

		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 20; top: 290; width: 525px; height: 39px; visibility: visible;">
			<div id="GeneralParameter"
				style="position: absolute; left: 6; top: 4; width: 290; height: 15px; visibility: visible;">
				<table width="280" border="0" cellspacing="0" cellpadding="0"
					height="20">

					<tr>
						<td id="changebutton" width="70"></td>
						<td id="savebutton" width="70"></td>
						<td id="cancelbutton" width="70"></td>

						<td id="closebutton" width="70"><a tabindex="18"
							titleKey='<fmt:message key="tooltip.close"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitCancel('close')"><fmt:message key="button.close" /></a></td>
					</tr>

				</table>
			</div>

			<!--End: Code modified by sudhakar on 17-01-2012:1687: Parameterise retention time for suppressed and rejected messages -->
			<div
				style="position: absolute; left: 6; top: 4; width: 480; height: 15px; visibility: hidden;">
				<table width="280px" border="1" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden; display: none;">
					<tr>
						<td id="changeenablebutton"><a tabindex="19"
							titleKey='<fmt:message key="tooltip.changeGenSysParam"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitForm('change')"><fmt:message key="button.change" /></a></td>
						<td id="changedisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.change" /></a></td>


						<td id="saveenablebutton"><a tabindex="20"
							titleKey='<fmt:message key="tooltip.SaveChanges"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="javascript:submitForm('update');"><fmt:message key="button.save" /></a></td>
						<td id="savedisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.save" /></a></td>
						<td id="cancelenablebutton"><a tabindex="21"
							titleKey='<fmt:message key="tooltip.CancelChanges"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitForm('display')"><fmt:message key="button.cancel" /></a></td>
						<td id="canceldisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.cancel" /></a></td>

					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>


		<script type="text/javascript">
</script>
	</form>
</body>
</html>