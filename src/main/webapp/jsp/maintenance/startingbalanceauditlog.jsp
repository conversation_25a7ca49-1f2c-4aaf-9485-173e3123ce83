<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.PageDetails"%>
<%@ include file="/taglib.jsp"%>
<html>
<head>
<title><fmt:message key="startBalLog.title.MainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
  /** start:Modified the Mantis 1876 by the sunil.The purpose to display the Account ID && pagination */
var totalCount="${requestScope.totalCount}";
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var dateFormat = '${sessionScope.CDM.dateFormat}';
var filterValues=new Array();
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];

mandatoryFieldsArray ="undefined" ;
	/**
 	 * Method called on page onload
 	 */
function bodyOnLoad(){
		var currencyFormat = '${sessionScope.CDM.currencyFormat}';
		xl = new XLSheet("balLogDetails","table_2", ["String","String",currencyFormat,"String",currencyFormat,currencyFormat,currencyFormat,"Number","String"],"*********","false",currentFilterValues,sortedValues);
		xl.onsort = xl.onfilter=updateColors;
		highlightTableRows("balLogDetails");
		document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
	}

	/**
 	 * Method called while click on data grid row
 	 * @param rowElement
 	 * @param isSelected
 	 */
	function onSelectTableRow(rowElement, isSelected){
 		try{
			if(isSelected){
				try{
		  		var movId = rowElement.querySelector('td.movementId').innerHTML.toLowerCase();
				var movId1 = movId.split("<br>")[0];
				var movId2 = movId.split("<br>")[1];
				movId = movId.replace("<br>","")
				if(movId1.trim()==movId2.trim())
					movId = movId1.trim();
				movId = movId.replace("&nbsp;","").replace("&nbsp;","");
				movId = movId.replace("<b>","").replace("</b>","").replace("<b>","").replace("</b>","");
				document.forms[0].selectedMovId.value = movId;

					if(movId.trim()!=""){
						document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntenablebutton").innerHTML;
					}else{
						document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
					}
				}catch(e){
					console.log(e)
				}
			}else{
				document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
			}
 		}catch(err){
 			document.getElementById("mvmntbutton").innerHTML = document.getElementById("mvmntdisablebutton").innerHTML;
		}
	}
	/**
	  * This function is used to display the Movement Display screen
	  *@param methodName
	  *return boolean
	  *
	  */
	function showMvmnt(methodName){

		//variable declared for menuAccessIdOfChildWindow
		  var menuAccessIdOfChildWindow = getMenuAccessIdOfChildWindow("Movement Display");
		  //variable declared for menuName
		  var menuName = new String('<fmt:message key="mvmDisplay.title.window"/>');
		  //variable declared for smrtPredPos
		 var smrtPredPos = menuName.search('<fmt:message key="alert.SmartPredict"/>');
		 menuName = menuName.substr(0,smrtPredPos-3);
		 if (menuAccessIdOfChildWindow == 2) {
			alert('<fmt:message key="alert.AccessNotAvl"/>' + menuName + '<fmt:message key="alert.ContactSysAdm"/>');

		 } else {
		 //variable declared for param
		     var param = 'movement.do?method='+methodName;
		     param += '&entityCode=${entityId}';

		     param += '&movementId=' + document.forms[0].selectedMovId.value;


		     param += '&menuAccessId='+menuAccessIdOfChildWindow;

		      openWindow(param,'movementWindow','left=50,top=190,width=994,height=789,toolbar=0, resizable=yes, scrollbars=yes','true');

		}

		return false;

	}

	/**
	 * Method to validate page number given in page field
	 * @param
	 */
	function validatePageNumber(pageNo) {
		var re = /^\d+$/;
		var maxPage = "${maxPage}";
		if (pageNo  && (re.test(pageNo.value) && pageNo.value != 0)){
			if(parseInt(pageNo.value) > maxPage){
				pageNo.value = maxPage;
			}
			goToResultsPage(pageNo.value,maxPage);
		}else{
			alert('<fmt:message key="party.alert.pagination"/>');
			pageNo.value = "${currentPage}";

		}


	}
	/**
	 * Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form
	 * Method called click on pagination
	 * @param goToPageNo
	 */
	function clickLink(goToPageNo){
	    var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((PageDetails)((ArrayList<PageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
	    var url='balanceLog.do?'+baseUrl;
	    url+='&goToPageNo='+goToPageNo+'&method=next&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&entityId='+'${entityId}'+'&date='+'${date}';
	    url+='&balType='+'${balType}'+'&totalCount='+'${totalCount}'+'&currentPage='+'${currentPage}'+'&balanceTypeId='+'${balanceTypeId}';
		url+='&selectedBalTypeName='+'${selectedBalTypeName}'+'&selectedCurrency='+'${selectedCurrency}';
		submitFormFromURL(url,window);


	}

	/**
	 * Method to send details for pagination values
	 * @param goTOPageNo
	 */
	function goToResultsPage(goToPageNo,maxPage) {
		document.forms[0].method.value = "next";
		document.forms[0].maxPages.value=maxPage;
		document.forms[0].goToPageNo.value=goToPageNo;
		document.forms[0].totalCount.value='${totalCount}';
		document.forms[0].currentPage.value='${currentPage}';
		document.forms[0].selectedSort.value=currentSort;
		document.forms[0].selectedFilter.value=currentFilter;
		document.forms[0].entityId.value ='${entityId}';
		document.forms[0].date.value ='${date}';
		document.forms[0].balType.value='${balType}';
		document.forms[0].balanceTypeId.value='${balanceTypeId}';
		document.forms[0].selectedBalTypeName.value='${selectedBalTypeName}';
		document.forms[0].selectedCurrency.value='${selectedCurrency}';
		document.forms[0].submit();
	}

	/**
 	 * Method called when datd grid is filtered
 	 * @param index
 	 * @param value
 	 * @param action
 	 */
	function optionClick_server_filter_JSP(index,value,action){

		if(action == "filter"){
			value = value.trim();
			value = replace(value,'&nbsp;',' ');
			var filterValue ="";
			if(currentFilter =="all" || currentFilter=="undefined"){
				for(var idx = 0 ; idx < xl.numColumns ; ++idx){
					if(idx == index)
						filterValue +=  value + "|";
					else
						filterValue +=  "All" + "|";
				}
			}else{
				var filter=currentFilter.split("|");
				filter[index]=value;
				for(var idx = 0 ; idx < xl.numColumns ; ++idx){
					filterValue+=filter[idx] + "|";
				}
			}
			document.forms[0].selectedSort.value=currentSort;
			document.forms[0].selectedFilter.value =filterValue ;
		}else{
			var sortColum=index;
			var sortDesc=value;
			document.forms[0].selectedSort.value=sortColum + "|" +sortDesc;
			document.forms[0].selectedFilter.value =currentFilter ;
		}
		document.getElementById('ddscrolltable').innerHTML='';
		document.forms[0].method.value='next';
		document.forms[0].currentPage.value=1;
		document.forms[0].initialPageCount.value=0;
		document.forms[0].maxPages.value=0;
		document.forms[0].goToPageNo.value=1;
		document.forms[0].selectedBalTypeName.value=document.forms[0].elements['startingBalanceLog.balanceTypeName'].value;
		document.forms[0].submit();

  	}

</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad(); setParentChildsFocus(); ShowErrMsgWindow('${actionError}');setTitleSuffix(document.forms[0]);"
	onunload="call();">
<form action="balanceLog.do" method="post">
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="entityId" type="hidden" value='${entityId}'>
	<input name="balType" type="hidden" value='${balType}'>
	<input name="balanceTypeId" type="hidden" value='${balanceTypeId}'>
	<input name="balanceTypeName" type="hidden" value='${balanceTypeName}'>
	<input name="date" type="hidden" value='${date}'>
	<input name="method" type="hidden" value='next'>
	<input name="currentPage" type="hidden" value="">
	<input name="totalCount" type="hidden" value="">
	<input name="initialPageCount" type="hidden" value="">
	<input name="maxPages" type="hidden" value="">
	<input name="pageNoValue" type="hidden" value="">
	<input name="selectedBalSrc" type="hidden" value="">
	<input name="selectedMovId" type="hidden" value="">
	<input name="selectedBalTypeName" type="hidden" value="">
	<!--  Modified by Atef S. for Mantis 2047 : Starting Balance Audit log displays incorrect decimal places while sorting -->
	<input name="selectedCurrency" type="hidden" value="${selectedCurrency}">
	<input name="goToPageNo" type="hidden" value="">

	<div id="BalanceMaintenance"
		style="position: absolute; left: 10px; top: 10px; width: 1205px; height: 55px; border: 2px outset;"
		color="#7E97AF">
	<div id="BalanceMaintenance"
		style="position: absolute; left: 8px; top: 4px; width: 850px; height: 55px;">
	<table width="930" border="0px" cellpadding="0" cellspacing="1"
		height="">
	  <tr color="black" border="0">
			<td width="120"><b><fmt:message key="balmaintenance.accountId"/></b></td>
			<td width="435"><input type="text" tabindex="-1"

				class="textlabelalpha" name="startingBalanceLog.balanceTypeId" value="${startingBalanceLog.balanceTypeId}"  style="width:375px;"
				readonly="readonly"  /></td>
			<td width="130"><input type="text" tabindex="-1"

				class="textlabelalpha" name="startingBalanceLog.balanceTypeName" value="${startingBalanceLog.balanceTypeName}"  style="width:350px;"
				readonly="readonly"  /></td>
		</tr>
		</table>
		<table width="230" border="0px" cellpadding="0" cellspacing="1"
		height="">
		<tr>
			<td width="100"><b><fmt:message key="date"/></b></td>
			<td width="15"><input type="text" tabindex="-1"

				class="textlabelalpha" name="startingBalanceLog.balanceDateAsString" value="${startingBalanceLog.balanceDateAsString}"
				style="width:80px;" readonly="readonly"  /></td>
		</tr>
	</table>
	</div>
	</div>

	<div id="pageSummaryList"
		style="position: absolute; left: 1030px; top: 0px; width: 180px; height:25px; border: 2px;">
	 <c:if test="${'true' != requestScope.hidePagination}">

	<table class="pagebuttons" border="0" cellpadding="0" cellspacing="0" height="90">
	  <tr height="34" >
	  	<%String currentPageAsString = (String)request.getAttribute("currentPage");%>
			<%String maxPageAsString = (String)request.getAttribute("maxPage");%>
			<%int currentPage = Integer.parseInt(currentPageAsString);%>
			<%int countPage = 1;%>
			<%int maxPage = Integer.parseInt(maxPageAsString);%>
			<c:forEach items="${requestScope.pageSummaryList}" var='pageSummaryList' >
	      	<% if( countPage <=1) {++countPage; %>

	  	<td height="34"><b>Page</b>&nbsp;&nbsp;
	  	<input class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5" align="top" style="height: 21px;margin-top:1px;"  value="<%=currentPageAsString %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
	  	</td>
	    <td>
	    <c:if test="${'true'==requestScope.nextEnabled}">

		    <a href="#"  onclick="clickLink(-1);">
	    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="height: 11px;"></img><br />
			</a>

</c:if>
	    <c:if test="${'true'!=requestScope.nextEnabled}">
	    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="height: 11px;"></img><br />
	    </c:if>
	    <c:if test="${'true'==requestScope.prevEnabled}">

		    <a href="#"  onclick="clickLink(-2);">
	    <img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="height: 11px;"></img><br />
			</a>

</c:if>
	    <c:if test="${'true'!=requestScope.nextEnabled}">
	    <img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="height: 11px;"></img><br />
	    </c:if>
	    </td>
	    <td style="text-align: center;font-size:10pt;padding-top:3px;">&nbsp;&nbsp;<fmt:message key="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
	    <input class="textAlpha" style="background:transparent;border: 0; height:17;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
	  	</td>

		<%}%>
	</c:forEach>
	  </tr>
	  </table>

</c:if>
	</div>
	</div>
	</div>
	<!-- End:Modified the Mantis 1876 by the sunil.The purpose to display the Account ID must be shown in full in the header -->
	<div id="BalanceMaintenance" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 10px; top: 70px; width: 1220px; height: 428px;">
	<div id="BalanceMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 1204px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1900px"
		border="1" cellspacing="1" cellpadding="0">
		<thead>
			<tr>

				<td width="145px" height="20px" align="center" style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortByDateandTime"/>'><b><fmt:message key="logBalance.dateandtime"/></b></td>
				<td width="70px" height="20px" align="center" title='' margin-left = "2px">&nbsp;</td>



				<td width="190px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortBySODChange"/>'><b><fmt:message key="logBalance.workingSODChange"/></b></td>

				<td width="150px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortBySOD"/>'><b><fmt:message key="logBalance.workingSODType"/></b></td>

				<td width="190px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortByWorkingSOD"/>'><b><fmt:message key="logBalance.workingSOD"/></b></td>




				<td width="70px" height="20px" align="center" title='' margin-left = "2px">&nbsp;</td>
				<td width="190px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortBySODChange"/>'><b><fmt:message key="logBalance.suppliedSODChange"/></b></td>

				<td width="150px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortBySOD"/>'><b><fmt:message key="logBalance.suppliedSODSource"/></b></td>
				<td width="190px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortByWorkingSOD"/>'><b><fmt:message key="logBalance.suppliedSOD"/></b></td>




				<td width="157px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortByBvAdjustChange"/>'><b><fmt:message key="logBalance.bvAdjustChange"/></b></td>
				<td width="157px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortByBvAdj"/>'><b><fmt:message key="logBalance.bvAdj"/></b></td>

				<td width="114px" height="20px" align="center"
					title='<fmt:message key="tooltip.sortById"/>'><b><fmt:message key="logBalance.id"/></b></td>
				<td width="114px" height="20px" align="center"
					title='<fmt:message key="tooltip.user"/>'><b><fmt:message key="balmaintenance.updateUser"/></b></td>

			</tr>
		</thead>
	</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 1220px; height: 425px; ">
	<div id="BalanceMaintenance"
		style="position: absolute; z-index: 99; left: 1px; top: 22px; width: 1204px; height: 10px;">

	<table class="sort-table" id="balLogDetails" width="1900px" border="0"
		cellspacing="1" cellpadding="0" height="403px">
		<tbody>
			<%
				int count = 0;
			%>
			<c:forEach items="${requestScope.balLogDetails}" var='balLogDetails' >
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>

				<tr class="odd">
					<%
						}
							++count;
					%>
					<input type="hidden" name="test" value="${test}" />
					<!-- date/time column -->
					<td width="145px" height="40" align="left">${balLogDetails.updateDateAsString}&nbsp;${balLogDetails.updateTimeAsString}</td>
					<td width="70px" align="center"><fmt:message key="logBalance.forecast"/>:<br>
					<fmt:message key="logBalance.external"/>:</td>

					<!-- working SOD change column -->
					<td width="190px" align="right">
					<c:if test="${true == balLogDetails.workingForcastSODChangeNegative}">

						<font color="red">
							<c:if test="${'0.0' !=balLogDetails.workingForcastSODChangeAsString}">

							<c:if test="${'0,00' != balLogDetails.workingForcastSODChangeAsString}">

								<b>&nbsp;${balLogDetails.workingForcastSODChangeAsString}</b>

</c:if>
							<c:if test="${'0,00' == balLogDetails.workingForcastSODChangeAsString}">

								&nbsp;${balLogDetails.workingForcastSODChangeAsString}

</c:if>

</c:if>
						 <c:if test="${'0.0' ==balLogDetails.workingForcastSODChangeAsString}">

							&nbsp;${balLogDetails.workingForcastSODChangeAsString}

</c:if>
						</font>


</c:if>

					<c:if test="${true != balLogDetails.workingForcastSODChangeNegative}">

						<c:if test="${'0.0' !=balLogDetails.workingForcastSODChangeAsString}">

							<c:if test="${'0,00' == balLogDetails.workingForcastSODChangeAsString}">

					&nbsp;${balLogDetails.workingForcastSODChangeAsString}

</c:if>

							<c:if test="${'0,00' != balLogDetails.workingForcastSODChangeAsString}">

								<b>&nbsp;${balLogDetails.workingForcastSODChangeAsString}</b>

</c:if>

</c:if>

						<c:if test="${'0.0' ==balLogDetails.workingForcastSODChangeAsString}">

					&nbsp;${balLogDetails.workingForcastSODChangeAsString}

</c:if>

</c:if> <br>


					<c:if test="${true==balLogDetails.workingExternalSODChangeNegative}">

						<font color="red">
						<c:if test="${'0.0' !=balLogDetails.workingExternalSODChangeAsString}">

							<b>&nbsp;${balLogDetails.workingExternalSODChangeAsString}</b>

</c:if>
						<c:if test="${'0.0' ==balLogDetails.workingExternalSODChangeAsString}">

						&nbsp;${balLogDetails.workingExternalSODChangeAsString}

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.workingExternalSODChangeNegative}">

						<c:if test="${'0.0' !=balLogDetails.workingExternalSODChangeAsString}">

							<c:if test="${'0,00' == balLogDetails.workingExternalSODChangeAsString}">

							&nbsp;${balLogDetails.workingExternalSODChangeAsString}

</c:if>
							<c:if test="${'0,00' != balLogDetails.workingExternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.workingExternalSODChangeAsString}</b>

</c:if>

</c:if>
						<c:if test="${'0.0' ==balLogDetails.workingExternalSODChangeAsString}">

						&nbsp;${balLogDetails.workingExternalSODChangeAsString}

</c:if>

</c:if>
				  	</td>


					<!-- working SOD Type column -->
					<td width="150px" align="left">
						<c:if test="${'0.0' != balLogDetails.workingForcastSODChangeAsString}">

							<c:if test="${'' == balLogDetails.workingForcastSODChangeAsString}">

 									&nbsp;${balLogDetails.newWorkingForcastSODTypeAsString}

</c:if>
 							<c:if test="${'' != balLogDetails.workingForcastSODChangeAsString}">

					  			<c:if test="${'0,00' != balLogDetails.workingForcastSODChangeAsString}">

                                     <b>&nbsp;${balLogDetails.newWorkingForcastSODTypeAsString}</b>

</c:if>
                            	<c:if test="${'0,00' == balLogDetails.workingForcastSODChangeAsString}">

                                     &nbsp;${balLogDetails.newWorkingForcastSODTypeAsString}

</c:if>

</c:if>

</c:if>

                        <c:if test="${'0.0' == balLogDetails.workingForcastSODChangeAsString}">

                                     &nbsp;${balLogDetails.newWorkingForcastSODTypeAsString}

</c:if>
                     <br>
						<c:if test="${'0.0' != balLogDetails.workingExternalSODChangeAsString}">

							<c:if test="${'' == balLogDetails.workingExternalSODChangeAsString}">

 									&nbsp;${balLogDetails.newWorkingExternalSODTypeAsString}

</c:if>
 							<c:if test="${'' != balLogDetails.workingExternalSODChangeAsString}">

					  			<c:if test="${'0,00' != balLogDetails.workingExternalSODChangeAsString}">

                                     <b>&nbsp;${balLogDetails.newWorkingExternalSODTypeAsString}</b>

</c:if>
                            	<c:if test="${'0,00' == balLogDetails.workingExternalSODChangeAsString}">

                                     &nbsp;${balLogDetails.newWorkingExternalSODTypeAsString}

</c:if>

</c:if>

</c:if>

                        <c:if test="${'0.0' == balLogDetails.workingExternalSODChangeAsString}">

                                     &nbsp;${balLogDetails.newWorkingExternalSODTypeAsString}

</c:if>
					</td>


					<!-- working SOD column -->
					<td width="190px" align="right">
					<c:if test="${true==balLogDetails.newWorkingForcastSODNegative}">

						<font color="red">
						<c:if test="${ '' == balLogDetails.workingForcastSODChangeAsString}">

							&nbsp;${balLogDetails.newWorkingForcastSODAsString}

</c:if>
						<c:if test="${ '' != balLogDetails.workingForcastSODChangeAsString}">

							<b>&nbsp;${balLogDetails.newWorkingForcastSODAsString}</b>

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.newWorkingForcastSODNegative}">

						<c:if test="${ '0.0' != balLogDetails.workingForcastSODChangeAsString}">

							<c:if test="${ '' == balLogDetails.workingForcastSODChangeAsString}">

								&nbsp;${balLogDetails.newWorkingForcastSODAsString}

</c:if>
						<c:if test="${ '' != balLogDetails.workingForcastSODChangeAsString}">

								<c:if test= '${ "0,00" != balLogDetails.workingForcastSODChangeAsString}'>
									<b>&nbsp;${balLogDetails.newWorkingForcastSODAsString}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.workingForcastSODChangeAsString}">

									&nbsp;${balLogDetails.newWorkingForcastSODAsString}

</c:if>

</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.workingForcastSODChangeAsString}">

						&nbsp;${balLogDetails.newWorkingForcastSODAsString}

</c:if>

					</c:if> <br>
					<c:if test="${true==balLogDetails.newWorkingExternalSODNegative}">

						<font color="red">
						<c:if test="${'' == balLogDetails.workingExternalSODChangeAsString}">

							&nbsp;${balLogDetails.newWorkingExternalSODAsString}

</c:if>
						<c:if test="${'' != balLogDetails.workingExternalSODChangeAsString}">

							<b>&nbsp;${balLogDetails.newWorkingExternalSODAsString}</b>

</c:if> </font>

</c:if>
					 <c:if test="${true!=balLogDetails.newWorkingExternalSODNegative}">

						<c:if test="${'0.0' != balLogDetails.workingExternalSODChangeAsString}">

							<c:if test="${'' == balLogDetails.workingExternalSODChangeAsString}">

								&nbsp;${balLogDetails.newWorkingExternalSODAsString}

</c:if>

							<c:if test="${'' != balLogDetails.workingExternalSODChangeAsString}">

								<c:if test= '${ "0,00" != balLogDetails.workingExternalSODChangeAsString}'>
									<b>&nbsp;${balLogDetails.newWorkingExternalSODAsString}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.workingExternalSODChangeAsString}">

									&nbsp;${balLogDetails.newWorkingExternalSODAsString}

</c:if>


</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.workingExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newWorkingExternalSODAsString}

</c:if>
					</c:if></td>


					<td width="70px" align="center"><fmt:message key="logBalance.suppliedInternal"/>:<br>
					<fmt:message key="logBalance.external"/>:</td>


					 <!-- Supplied SOD change column -->
					<td width="190px" align="right">
					<c:if test="${true==balLogDetails.suppliedInternalSODChangeNegative}">

						<font color="red">
						<c:if test="${'0.0' != balLogDetails.suppliedInternalSODChangeAsString}">

							<c:if test="${'0,00' != balLogDetails.suppliedInternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}</b>

</c:if>
							<c:if test="${'0,00' == balLogDetails.suppliedExternalSODChangeAsString}">

								&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}

</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.suppliedInternalSODChangeAsString}">

								&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}

</c:if> </font>


</c:if>

					<c:if test="${true!=balLogDetails.suppliedInternalSODChangeNegative}">


						<c:if test="${'0.0' != balLogDetails.suppliedInternalSODChangeAsString}">

							<c:if test="${'0,00' == balLogDetails.suppliedInternalSODChangeAsString}">

								&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}

</c:if>

							<c:if test="${'0,00' != balLogDetails.suppliedInternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}</b>

</c:if>

</c:if>

						<c:if test="${'0.0' == balLogDetails.suppliedInternalSODChangeAsString}">

								&nbsp;${balLogDetails.suppliedInternalSODChangeAsString}

</c:if>



</c:if> <br>

					<c:if test="${true==balLogDetails.suppliedExternalSODChangeNegative}">

						<font color="red">
						<c:if test="${'0.0' != balLogDetails.suppliedExternalSODChangeAsString}">

							<b>&nbsp;${balLogDetails.suppliedExternalSODChangeAsString}</b>

</c:if>
						<c:if test="${'0.0' == balLogDetails.suppliedExternalSODChangeAsString}">

							&nbsp;${balLogDetails.suppliedExternalSODChangeAsString}

</c:if> </font>


</c:if>

					<c:if test="${true!=balLogDetails.suppliedExternalSODChangeNegative}">

						<c:if test="${'0.0' != balLogDetails.suppliedExternalSODChangeAsString}">

							<c:if test="${'0,00' == balLogDetails.suppliedExternalSODChangeAsString}">

									&nbsp;${balLogDetails.suppliedExternalSODChangeAsString}

</c:if>
							<c:if test="${'0,00' != balLogDetails.suppliedExternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.suppliedExternalSODChangeAsString}</b>

</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.suppliedExternalSODChangeAsString}

</c:if>

</c:if></td>


					<!-- Supplied SOD source column -->
					<td width="150px" align="left">
					<c:if test="${'0.0' != balLogDetails.suppliedInternalSODChangeAsString}">

						<c:if test="${'' == balLogDetails.suppliedInternalSODChangeAsString}">

							&nbsp;${balLogDetails.newInternalBalSource}

</c:if>
						<c:if test="${'' != balLogDetails.suppliedInternalSODChangeAsString}">

							<c:if test="${'0,00' != balLogDetails.suppliedInternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.newInternalBalSource}</b>

</c:if>
							<c:if test="${'0,00' == balLogDetails.suppliedInternalSODChangeAsString}">

								&nbsp;${balLogDetails.newInternalBalSource}

</c:if>

</c:if>



</c:if>

					<c:if test="${'0.0' == balLogDetails.suppliedInternalSODChangeAsString}">








						&nbsp;${balLogDetails.newInternalBalSource}






</c:if> <br>

					<c:if test="${'0.0' != balLogDetails.suppliedExternalSODChangeAsString}">

						<c:if test="${'' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newExternalBalSource}

</c:if>
						<c:if test="${'' != balLogDetails.suppliedExternalSODChangeAsString}">

							<c:if test="${'0,00' != balLogDetails.suppliedExternalSODChangeAsString}">

								<b>&nbsp;${balLogDetails.newExternalBalSource}</b>

</c:if>
							<c:if test="${'0,00' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newExternalBalSource}

</c:if>

</c:if>

</c:if> <c:if test="${'0.0' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newExternalBalSource}

</c:if></td>

					<!-- Supplied SOD  column -->
					<td width="190px" align="right">
					<c:if test="${true==balLogDetails.newSuppliedInternalSODNegative}">

						<font color="red">
						<c:if test="${'' == balLogDetails.suppliedInternalSODChangeAsString}">

							&nbsp;${balLogDetails.newSuppliedInternalSODAsString}

</c:if>
						<c:if test="${'' != balLogDetails.suppliedInternalSODChangeAsString}">

							<b>&nbsp;${balLogDetails.newSuppliedInternalSODAsString}</b>

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.newSuppliedInternalSODNegative}">

						<c:if test="${'0.0' != balLogDetails.suppliedInternalSODChangeAsString}">

							<c:if test="${'' == balLogDetails.suppliedInternalSODChangeAsString}">

								&nbsp;${balLogDetails.newSuppliedInternalSODAsString}

</c:if>
							<c:if test="${'' != balLogDetails.suppliedInternalSODChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.suppliedInternalSODChangeAsString}">

									<b>&nbsp;${balLogDetails.newSuppliedInternalSODAsString}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.suppliedInternalSODChangeAsString}">

									&nbsp;${balLogDetails.newSuppliedInternalSODAsString}

</c:if>

</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.suppliedInternalSODChangeAsString}">

						&nbsp;${balLogDetails.newSuppliedInternalSODAsString}

</c:if>
					</c:if> <br>

					<c:if test="${true==balLogDetails.newSuppliedExternalSODNegative}">

						<font color="red">
						<c:if test="${'' == balLogDetails.suppliedExternalSODChangeAsString}">

							&nbsp;${balLogDetails.newSuppliedExternalSODAsString}

</c:if>
						<c:if test="${'' != balLogDetails.suppliedExternalSODChangeAsString}">

							<b>&nbsp;${balLogDetails.newSuppliedExternalSODAsString}</b>

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.newSuppliedExternalSODNegative}">

						<c:if test="${'0.0' != balLogDetails.suppliedExternalSODChangeAsString}">

							<c:if test="${'' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newSuppliedExternalSODAsString}

</c:if>
							<c:if test="${'' != balLogDetails.suppliedExternalSODChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.suppliedExternalSODChangeAsString}">
									<b>&nbsp;${balLogDetails.newSuppliedExternalSODAsString}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.suppliedExternalSODChangeAsString}">

							&nbsp;${balLogDetails.newSuppliedExternalSODAsString}

</c:if>


</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.suppliedExternalSODChangeAsString}">

						&nbsp;${balLogDetails.newSuppliedExternalSODAsString}

</c:if>
					</c:if></td>

					<!-- BV Adjust Change SOD  column nnn -->

					<td width="157px" align="right">
					<c:if test="${true==balLogDetails.bvForecastAdjustChangeNegative}">

						<font color="red">
						<c:if test="${'0.0' != balLogDetails.bvForecastAdjustChangeAsString}">

							<b>&nbsp;${balLogDetails.bvForecastAdjustChangeAsString}</b>

						</c:if>
						<c:if test="${'0.0' == balLogDetails.bvForecastAdjustChangeAsString}">

						&nbsp;${balLogDetails.bvForecastAdjustChangeAsString}

						</c:if> </font>

					</c:if>
					<c:if test="${true!=balLogDetails.bvForecastAdjustChangeNegative}">

						<c:if test="${'0.0' != balLogDetails.bvForecastAdjustChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.bvForecastAdjustChangeAsString}">

								<b>&nbsp;${balLogDetails.bvForecastAdjustChangeAsString}</b>

					</c:if>
							<c:if test="${'0,00' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.bvForecastAdjustChangeAsString}

</c:if>

</c:if>
							<c:if test="${'0.0' == balLogDetails.bvForecastAdjustChangeAsString}">

						&nbsp;${balLogDetails.bvForecastAdjustChangeAsString}

					</c:if>

</c:if> <br>
					<c:if test="${true==balLogDetails.bvExternalAdjustChangeNegative}">

						<font color="red">
							<c:if test="${'0.0' != balLogDetails.bvExternalAdjustChangeAsString}">

							<b>&nbsp;${balLogDetails.bvExternalAdjustChangeAsString}</b>

					</c:if>
							<c:if test="${'0.0' == balLogDetails.bvExternalAdjustChangeAsString}">

						&nbsp;${balLogDetails.bvExternalAdjustChangeAsString}

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.bvExternalAdjustChangeNegative}">

						<c:if test="${'0.0' != balLogDetails.bvExternalAdjustChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.bvExternalAdjustChangeAsString}">

									<b>&nbsp;${balLogDetails.bvExternalAdjustChangeAsString}</b>

								</c:if>
								<c:if test="${'0,00' == balLogDetails.bvExternalAdjustChangeAsString}">

									&nbsp;${balLogDetails.bvExternalAdjustChangeAsString}

</c:if>

</c:if>
							<c:if test="${'0.0' == balLogDetails.bvExternalAdjustChangeAsString}">

						&nbsp;${balLogDetails.bvExternalAdjustChangeAsString}

</c:if>

</c:if></td>
				
					<!-- BV Adjust  column -->
					<td width="157px" align="right">
					<c:if test="${true==balLogDetails.newBVForecastAdjustNegative}">

						<font color="red">
							<c:if test="${'' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVForecastAdjustAsString}

</c:if>
							<c:if test="${'' != balLogDetails.bvForecastAdjustChangeAsString}">

								<c:if test="${'0.0' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVForecastAdjustAsString}

</c:if>
								<c:if test="${'0.0' != balLogDetails.bvForecastAdjustChangeAsString}">

								<b>&nbsp;${balLogDetails.newBVForecastAdjustAsString}</b>

</c:if>

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.newBVForecastAdjustNegative}">

							<c:if test="${'0.0' != balLogDetails.bvForecastAdjustChangeAsString}">


							<c:if test="${'' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVForecastAdjustAsString}

</c:if>
								<c:if test="${'' != balLogDetails.bvForecastAdjustChangeAsString}">

									<c:if test="${'0,00' != balLogDetails.bvForecastAdjustChangeAsString}">
									<b>&nbsp;${balLogDetails.newBVForecastAdjustAsString}</b>

</c:if>

									<c:if test="${'0,00' == balLogDetails.bvForecastAdjustChangeAsString}">

								&nbsp;${balLogDetails.newBVForecastAdjustAsString}

</c:if>

</c:if>

</c:if>
							<c:if test="${'0.0' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVForecastAdjustAsString}

</c:if>

					</c:if> <br>
					<c:if test="${true==balLogDetails.newBVExternalAdjustNegative}">

						<font color="red">
							<c:if test="${'' == balLogDetails.bvExternalAdjustChangeAsString}">

						&nbsp;${balLogDetails.newBVExternalAdjustAsString}

</c:if>
							<c:if test="${'' != balLogDetails.bvExternalAdjustChangeAsString}">

								<c:if test="${'0.0' == balLogDetails.bvExternalAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVExternalAdjustAsString}

</c:if>
								<c:if test="${'0.0' != balLogDetails.bvExternalAdjustChangeAsString}">

								<b>&nbsp;${balLogDetails.newBVExternalAdjustAsString} </b>

</c:if>

</c:if> </font>

</c:if>
					<c:if test="${true!=balLogDetails.newBVExternalAdjustNegative}">

							<c:if test="${'0.0' != balLogDetails.bvExternalAdjustChangeAsString}">

							<c:if test="${'' == balLogDetails.bvExternalAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVExternalAdjustAsString}

							</c:if>
								<c:if test="${'' != balLogDetails.bvExternalAdjustChangeAsString}">

									<c:if test="${'0,00' != balLogDetails.bvExternalAdjustChangeAsString}">
									<b>&nbsp;${balLogDetails.newBVExternalAdjustAsString} </b>

</c:if>

									<c:if test="${'0,00' == balLogDetails.bvExternalAdjustChangeAsString}">

								&nbsp;${balLogDetails.newBVExternalAdjustAsString}

</c:if>

</c:if>

</c:if>
							<c:if test="${'0.0' == balLogDetails.bvExternalAdjustChangeAsString}">

							&nbsp;${balLogDetails.newBVExternalAdjustAsString}

</c:if>
					</c:if></td>

					<!-- ID  column -->
					<td width="114px" class="movementId" align="center">
						<c:if test="${'0.0' != balLogDetails.bvForecastAdjustChangeAsString}">

							<c:if test="${'' == balLogDetails.bvForecastAdjustChangeAsString}">

							&nbsp;${balLogDetails.bvForecastAdjustLastId}

</c:if>
							<c:if test="${'' != balLogDetails.bvForecastAdjustChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.bvForecastAdjustChangeAsString}">

								<b>&nbsp;${balLogDetails.bvForecastAdjustLastId}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.bvForecastAdjustChangeAsString}">

						&nbsp;${balLogDetails.bvForecastAdjustLastId}

</c:if>








</c:if>













</c:if>
						<c:if test="${'0.0' == balLogDetails.bvForecastAdjustChangeAsString}">

				&nbsp;${balLogDetails.bvForecastAdjustLastId}

</c:if> <br>
						<c:if test="${'0.0' != balLogDetails.bvExternalAdjustChangeAsString}">

							<c:if test="${'' == balLogDetails.bvExternalAdjustChangeAsString}">

					&nbsp;${balLogDetails.bvExternalAdjustLastId}

</c:if>
						<c:if test="${'' != balLogDetails.bvExternalAdjustChangeAsString}">

								<c:if test="${'0,00' != balLogDetails.bvExternalAdjustChangeAsString}">

								<b>&nbsp;${balLogDetails.bvExternalAdjustLastId}</b>

</c:if>
								<c:if test="${'0,00' == balLogDetails.bvExternalAdjustChangeAsString}">

								&nbsp;${balLogDetails.bvExternalAdjustLastId}

</c:if>

</c:if>

</c:if>
						<c:if test="${'0.0' == balLogDetails.bvExternalAdjustChangeAsString}">

							&nbsp;${balLogDetails.bvExternalAdjustLastId}

</c:if></td>

					<!-- User  column -->
					<td width="114px" align="center">&nbsp;${balLogDetails.updateUser}<br>
					</td>
				</tr>

			</c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="9"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>

	</div>


	<div id="BookCode"
		style="position: absolute; left: 1120; top: 503; width: 0px; height: 39px; visibility: visible;">
	<table width="70px" cellspacing="0" cellpadding="0" height="39"
		border="0">
		<tr>
			<td align="Right"><a tabindex="7" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Balance Audit Log Maintenance'),'sectionprintdwindow','left=50,top=190,width=442,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
			<td align="right" valign="middle" id="Print"><a tabindex="7"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)">
			<img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	</td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 504; width: 1205px; height: 39px; visibility: visible;">
	<!--End:Code added && modified by arumugam for mantis 997 on 08-10-09:To display screen-->

	<div id="BalanceMaintenance"
		style="position: absolute; left: 6; top: 4; width: 250px; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="mvmntbutton" width="70"></td>
			<td id="closebutton" width="70"><a
				title='<fmt:message key="tooltip.close"/>' tabIndex="7"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 700px; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>
			<td id="mvmntenablebutton" width="70"><a tabindex="5"
				title='<fmt:message key="tooltip.showSelMovDetail"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:showMvmnt('showMovementDetails')";><fmt:message key="button.mvmnt"/></a></td>
			<td id="mvmntdisablebutton" width="70"><a class="disabled"
				disabled="disabled"><fmt:message key="button.mvmnt"/></a></td>
		</tr>
	</table>
	</div>
	</div>


</form>
</body>
</html>