<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.EntityPositionLevel"%>
<html>
<head>
<title><fmt:message key="positionLevel.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	self.close();
</c:if>
var selectedWeekDayIndex = '${selectedWeekDayIndex}';
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
	/**
	 * Method to refresh parent window
	 */
	function refresh(){
		/* Set an empty event handler for window close event for other form submit actions in the parent screen*/
		/* Code modified for Mantis 1871 by <PERSON><PERSON><PERSON><PERSON> .<PERSON> on 11-Sep-2012 */
		window.opener.setEmptyWindow<PERSON>();
		window.opener.document.forms[0].method.value="refreshEntityDetails";
		window.opener.document.forms[0].selectedEntityId.value='${selectedEntityId}';
		window.opener.document.forms[0].screenStatus.value='${screenStatus}';
		window.opener.document.forms[0].selectedWeekDayIndex.value= selectedWeekDayIndex;		
		window.opener.document.forms[0].submit();
		self.close();
	}
	
	/**
	 * Method for body on load functionalities
	 */
	function bodyOnLoad(){
		document.forms[0].selectedEntityId.value = '${selectedEntityId}';
		document.forms[0].screenStatus.value = '${screenStatus}';
		document.forms[0].selectedSweepPosLvl.value = '${selectedSweepPosLvl}';
		document.forms[0].selectedPreAdvPosLvl.value = '${selectedPreAdvPosLvl}';
		xl = new XLSheet("entPosLvlcoll","table_2", ["Number","String", "String"],"111");
		xl.onsort = xl.onfilter = onFilterandSort;
		highlightTableRows("entPosLvlcoll");
		<c:choose>
			<c:when test="${requestScope.screenStatus == 'readonly'}">
				document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			</c:when>
			<c:when test="${requestScope.screenStatus == 'change'}">
				document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			</c:when>
			<c:when test="${requestScope.screenStatus == 'add'}">
				document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			</c:when>
		</c:choose>
		
		
	}
	
	/**
		function onFilterandSort
		Handler function for sort and filter, This function will update the sort indication and filter color  
	**/
	function onFilterandSort(){
		updateColors();
		disableAllButtons();
	}

	/**
		function disableAllButtons
		Disable the kill and view buttons 
	**/
	function disableAllButtons(){
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;		
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
	
	/**
	 * Method to submit form 
	 * @param methodName
	 */
	function submitForm(methodName){
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}
	
	
	/**
	 * Method to delete selected position level
	 * @param methodName
	 */
	function submitDeleteForm(methodName){
		document.forms[0].method.value = methodName;
		if (document.forms[0].poslvlId.value.trim() == document.forms[0].selectedSweepPosLvl.value.trim() || document.forms[0].poslvlId.value.trim() == document.forms[0].selectedPreAdvPosLvl.value.trim()){
			var poslvlAssigned = window.confirm("<fmt:message key="ent.deleteSelectedPosLvl"/>");
			 if (poslvlAssigned==true) {		 		
				document.forms[0].submit();
			 }	
		}else{
			var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
			if (yourstate==true){
				document.forms[0].submit();
			}
		}
	}

	/**
	 * Method to build add csreen params
	 * @param methodName
	 */
	function buildAddPosLvl(methodName){
		document.forms[0].method.value = methodName;
	    var	param = 'entity.do?method='+methodName;
		param +='&selectedEntityId=';
		param +=document.forms[0].selectedEntityId.value;
		param +='&screenStatus=';
		param +=document.forms[0].screenStatus.value;
		param +='&selectedSweepPosLvl=';
		param +=document.forms[0].selectedSweepPosLvl.value;
		param +='&selectedPreAdvPosLvl=';
		param +=document.forms[0].selectedPreAdvPosLvl.value;
	
		if(methodName == 'changePosLvl') {
			param +='&entPosLvlId='+document.forms[0].poslvlId.value;
			param +='&entPosLvlName=';
			param +=escape(encodeURIComponent(document.forms[0].posLvlName.value));
			param +='&entPosLvlIndicator=';
			param +=document.forms[0].posLvlIndicator.value;
	
		}
		return param;
	}

	/**
	 * Method to get selected values
	 * @param rowElement
	 * @param isSelected
	 */
	function onSelectTableRow(rowElement,isSelected){
		if(isSelected) {
			var hiddenElement = rowElement.cells[0].innerText;
			document.forms[0].poslvlId.value =  new String(hiddenElement).trim().valueOf();
			var hiddenElement = rowElement.cells[1].innerText;
			document.forms[0].posLvlName.value = new String(hiddenElement).trim().valueOf();
			var hiddenElement = rowElement.cells[2].innerText;
			document.forms[0].posLvlIndicator.value = new String(hiddenElement).trim().valueOf();
			<c:choose>
				<c:when test="${requestScope.screenStatus == 'readonly'}">
					document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
					document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
				</c:when>
				<c:when test="${requestScope.screenStatus == 'change'}">
					document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
					document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
				</c:when>
				<c:when test="${requestScope.screenStatus == 'add'}">
					document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
					document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
					document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
				</c:when>
			</c:choose>
		
		} else { 
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;		
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}

	}

</SCRIPT>
</head>

<form action="entity.do"  method="post">
<input name="method" type="hidden" value="">
<input name="poslvlId" type="hidden" value="">
<input name="posLvlName" type="hidden" value="">
<input name="posLvlIndicator" type="hidden" value="">
<input name="screenStatus" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value="">
<input name="selectedSweepPosLvl" type="hidden" value="">
<input name="selectedPreAdvPosLvl" type="hidden" value="">
<input name="selectedWeekDayIndex" type="hidden" value="">

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">


<div id="EntityPositionLevelNameMaintenance" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:20px; width:370px; height:209;">

	<div id="EntPosLevelName" style="position:absolute;z-index:50;left:px; top:0px; width:360px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="350" border="0" cellspacing="1" cellpadding="0"  height="20">
			<thead>
				<tr>					
					<td title='<fmt:message key="tooltip.positionlevel"/>' width="100px"  style="border-left-width: 0px;" value='<fmt:message key="entity.PosLvl"/>' ><b><fmt:message key="entity.PosLvl"/></b></td>
					<td title='<fmt:message key="tooltip.sortname"/>' width="143px" value='<fmt:message key="entity.PosLvl"/>'><b><fmt:message key="section.sectionName"/></b></td>
					<td title='<fmt:message key="tooltip.entityu.indicator"/>' width="100px" value='<fmt:message key="entity.PosLvl"/>'><b><fmt:message key="poslevel.indicator"/></b></td>
					
				</tr>
			</thead>
		</table>
	</div>	
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:367px; height:205px; overflowY:scroll">
		<div id="PositionLevel" style="position:absolute;z-index:99; left:0px; top:20px; width:331px; height:10px;">
				<table class="sort-table" id="entPosLvlcoll" width="350" border="0" cellspacing="1" cellpadding="0" height="185">
				<tbody>
				<%
				int count = 0;
				%>
				<c:forEach items="${requestScope.entPosLvlcoll}" var="entPosLvl">
					<%
					if(count % 2 == 0){
					%>
						<tr class="even">
					<% } else { %>
						<tr class="odd">
					<%
					}
					count++;
					%>
					<td width="100px" align="left"><c:out value="${entPosLvl.id.positionLevel}"/>&nbsp;</td>

					<td width="143px" align="left"><c:out value="${entPosLvl.positionLevelName}"/>&nbsp;</td>

					<td width="100px" align="left"><c:out value="${entPosLvl.indicator}"/>&nbsp;</td>

					</tr>
				</c:forEach>
				</tbody>
				<tfoot>
					<tr><td colspan="3"></td></tr>
				</tfoot>
			</table>
		</div>
	</div>
</div>


<div id="MetaGroup" style="position:absolute; left:315; top:249px; width:70; height:39px; visibility:visible;">

	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td>&nbsp;&nbsp;
		</td>
			 <td align="Right">

			    <a tabindex="6" onKeyDown="submitEnter(this,event)" href=# onclick="javascript:openWindow(buildPrintURL('print','Position Level Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
             </td>

			<td align="right" id="Print">
				<a tabindex="6" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>



<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:240px; width:370px; height:39px; visibility:visible;">


<div id="MetagroupMaintenance" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
  <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"></td>
		<td id="changebutton" width="70"></td>
		<td id="deletebutton" width="70"></td>
		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>' >
			<c:choose>
				<c:when test="${requestScope.screenStatus == 'readonly'}">
					<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="window.close();">
						<fmt:message key="button.close"/>
					</a>
				</c:when>
				<c:when test="${requestScope.screenStatus == 'change' || requestScope.screenStatus == 'add'}">
					<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="javascript:refresh();">
						<fmt:message key="button.close"/>
					</a>
				</c:when>
			</c:choose>
						
		</td>
		
	</tr>	
	</table>
</div>

<div style="position:absolute; left:6; top:4; width:370px; height:15px; visibility:hidden; display:none;">  	
<table  border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
<tr>
    <td id="addenablebutton">		
		<a title='<fmt:message key="button.entity.add"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
		onClick="javascript:openWindow(buildAddPosLvl('addPosLvl'),'positionlvladdWindow','left=50,top=190,width=340,height=167,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
	</td>		
	<td id="adddisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="button.entity.add"/>' ><fmt:message key="button.add"/></a>
	</td>
	<td id="changeenablebutton">		
		<a title='<fmt:message key="buttton.entity.change"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:openWindow(buildAddPosLvl('changePosLvl'),'positionlvladdWindow','left=50,top=190,width=340,height=167,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
	</td>		
	<td id="changedisablebutton">
		<a title='<fmt:message key="buttton.entity.change"/>' class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
	</td>
	<td id="deleteenablebutton">		
		<a title='<fmt:message key="tooltip.delete"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitDeleteForm('deletePosLvl');"><fmt:message key="button.delete"/></a>
	</td>		
	<td id="deletedisablebutton">
		<a title='<fmt:message key="tooltip.delete"/>' class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
	</td>	
</tr>
</table>
</div>

</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>   	