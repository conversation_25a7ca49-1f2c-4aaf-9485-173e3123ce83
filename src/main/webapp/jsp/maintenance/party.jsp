<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.util.SwtConstants" %> <%-- For button state flags --%>
<%@ page import="org.apache.commons.lang.StringEscapeUtils" %>

<html>
<head>
<title><fmt:message key="party.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">

<script type="text/javascript">
    // Global variables for Angular
    var appName = "<%=StringEscapeUtils.escapeJavaScript(SwtUtil.appName)%>";
    var baseURL = new String('<%=StringEscapeUtils.escapeJavaScript(request.getRequestURL().toString())%>'); // Ensure full base URL
    var screenRoute = "PartyMaintenance"; // Route for Angular to load this component

    // Initial parameters from JSP request scope, escaped for JavaScript
    var initialParams = {
        menuEntityCurrGrpAccess: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("menuEntityCurrGrpAccess")))%>",
        selectedFilter: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("selectedFilter")))%>",
        selectedSort: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("selectedSort")))%>",
        filterCriteria: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("filterCriteria")))%>",
        currentPage: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("currentPage")))%>",
        maxPage: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("maxPage")))%>",
        totalCount: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("totalCount")))%>",
        partyIdSearch: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("partyId")))%>", // Initial search criteria
        partyNameSearch: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("partyName")))%>", // Initial search criteria
        defaultEntityId: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(request.getAttribute("defaultEntityId")))%>", // If you have a default entity

        // Server-side button permissions (example, adjust keys as needed)
        swt_add_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.ADD_BUT_STS))))%>",
        swt_chg_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.CHG_BUT_STS))))%>",
        swt_del_btn_sts: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf(SwtConstants.STR_TRUE.equals(request.getAttribute(SwtConstants.DEL_BUT_STS))))%>",

        // Flags from JSP for Angular to handle
        criteriaNotMatch: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf("yes".equals(request.getAttribute("criteriaNotMatch"))))%>",
        parentFormRefresh: "<%=StringEscapeUtils.escapeJavaScript(String.valueOf("yes".equals(request.getAttribute("parentFormRefresh"))))%>"
    };

    // --- JavaScript functions to be called by Angular via ExternalInterface ---

    function openPartyAddWindow(entityCode, entityName, parentScreenPartyId, parentScreenPartyName, currentPage, filterCriteria, selectedSort, selectedFilter, maxPage) {
        var param = 'party.do?method=add'; // Or specific Angular add screen if different
        param += '&entityCode=' + encodeURIComponent(entityCode);
        param += '&entityName=' + encodeURIComponent(entityName);
        param += '&parentScreenPartyId=' + encodeURIComponent(parentScreenPartyId);
        param += '&parentScreenPartyName=' + encodeURIComponent(parentScreenPartyName);
        param += '&currentPage=' + encodeURIComponent(currentPage);
        param += '&filterCriteria=' + encodeURIComponent(filterCriteria);
        param += '&selectedSort=' + encodeURIComponent(selectedSort);
        param += '&selectedFilter=' + encodeURIComponent(selectedFilter);
        param += '&maxPage=' + encodeURIComponent(maxPage);
        return openWindow(param, 'partymaintenanceaddWindow', 'left=50,top=190,width=700,height=290,toolbar=0,status=1', 'true');
    }

    function openPartyChangeWindow(entityCode, entityName, partyId, partyName, currentPage, filterCriteria, selectedSort, selectedFilter, maxPage, parentScreenPartyId, parentScreenPartyName) {
        var param = 'party.do?method=change';
        param += '&entityCode=' + encodeURIComponent(entityCode);
        param += '&entityName=' + encodeURIComponent(entityName);
        param += '&partyId=' + encodeURIComponent(partyId);
        param += '&partyName=' + encodeURIComponent(partyName);
        param += '&currentPage=' + encodeURIComponent(currentPage);
        param += '&filterCriteria=' + encodeURIComponent(filterCriteria);
        param += '&selectedSort=' + encodeURIComponent(selectedSort);
        param += '&selectedFilter=' + encodeURIComponent(selectedFilter);
        param += '&maxPage=' + encodeURIComponent(maxPage);
        param += '&parentScreenPartyId=' + encodeURIComponent(parentScreenPartyId);
        param += '&parentScreenPartyName=' + encodeURIComponent(parentScreenPartyName);
        return openWindow(param, 'partymaintenancechangeWindow', 'left=50,top=190,width=700,height=290,toolbar=0,status=1', 'true');
    }

    function openPartyAliasWindow(selectedEntityId, selectedPartyId, partyName, entityDesc, menuAccessIdParam) {
        var param = 'party.do?method=displayAliasDetails';
        param += '&selectedEntityId=' + encodeURIComponent(selectedEntityId);
        param += '&selectedPartyId=' + encodeURIComponent(selectedPartyId);
        param += '&partyName=' + encodeURIComponent(partyName);
        param += '&entityDesc=' + encodeURIComponent(entityDesc);
        param += '&menuAccessId=' + encodeURIComponent(menuAccessIdParam);
        return openWindow(param, 'partymaintenancealiasWindow', 'left=50,top=190,width=785,height=525,toolbar=0,resizable=yes,scrollbars=yes', 'true');
    }

    function openLegacyWindow(url,screenname,params,isCascade){
        return openWindow(url, screenname, params, isCascade);
    }

    function openHelpWindow(printKey, screenTitle) {
        var helpUrl = buildPrintURL(printKey, screenTitle); // Assuming buildPrintURL is globally available from angularJSUtils.jsp or similar
        if (helpUrl) {
            return openWindow(helpUrl, 'sectionprintdwindow', 'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no', 'true');
        }
        return null;
    }

    function CallBackApp(){
        try {
            Main.refreshGridData();
        }catch(e){
            console.log("CallBackApp", e);
        }

    }



    function submitLegacyForm(actionUrl, params) {
        var form = document.getElementById('legacyActionForm');
        form.action = actionUrl;
        // Clear previous hidden inputs if any, or ensure they are overwritten
        form.innerHTML = ''; // Simple way to clear

        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", params[key]);
                form.appendChild(hiddenField);
            }
        }
        form.submit();
    }

    // Function to be called from Angular for print. It will populate and submit the hidden form.
    function requestPrintParty(params) {
        submitLegacyForm('print.do', params); // Assuming print.do is the target
    }

    // Function for delete confirmation and action (if delete is a simple GET/POST not requiring form)
    // If delete requires specific form submission for CSRF tokens etc., adapt 'requestPrintParty' pattern.
    // For now, assuming delete will be an AJAX call from Angular, and this is just for confirmation.
    function confirmDeletePartyMessage() {
        return '<fmt:message key="confirm.delete"/>'; // For Angular to get the localized message
    }

    function getLocalizedMessage(key) {
        // This is a generic way to get messages.
        // You might need to map keys if fmt:message keys are complex.
        var messages = {
            "confirm.delete": "<fmt:message key="confirm.delete"/>",
            "alert.checkParty": "<fmt:message key="alert.checkParty"/>",
            "party.alert.criteriaNotMatch": "<fmt:message key="party.alert.criteriaNotMatch"/>",
            "party.alert.pagination": "<fmt:message key="party.alert.pagination"/>"
            // Add other keys used by Angular's SwtAlert here
        };
        return messages[key] || key; // Return key if not found
    }

    // Listener for messages from child windows (e.g., to refresh grid)
    // window.addEventListener("message", receiveChildMessage, false);
    // function receiveChildMessage(event) {
    //   if (event && event.data && window.PartyMaintenanceComponent && window.PartyMaintenanceComponent.handleChildMessage) {
    //     window.PartyMaintenanceComponent.handleChildMessage(event.data);
    //   }
    // }

</script>
</head>

<body>
    <%-- Standard includes for Angular bootstrapping --%>
    <%@ include file="/angularJSUtils.jsp"%> <%-- Contains openWindow, buildPrintURL etc. --%>
    <%@ include file="/angularscripts.jsp"%> <%-- Loads Angular app --%>

    <%-- Hidden form for legacy actions like export or print if they require form submission --%>
    <form id="legacyActionForm" target="legacyActionFrame" method="post" style="display:none;">
        <%-- Fields will be populated by JavaScript --%>
    </form>
    <iframe name="legacyActionFrame" width="0" height="0" style="display:none;"></iframe>

    <%-- The Angular application will be mounted here by angularscripts.jsp --%>
    <app-root></app-root>
</body>
</html>
