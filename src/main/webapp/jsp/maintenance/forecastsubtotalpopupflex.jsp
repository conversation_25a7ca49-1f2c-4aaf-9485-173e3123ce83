<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the subtotalpopup.swf to load Add / Find subtotal pop up 
  -	And to embed the Subtotal pop up flex screen. 
  - Also, to load the label values for this screen.	
  -
  - Author(s): Vivekanandan A
  - Date: 25-05-2011
  -->
  

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<html>
<head>
<title><fmt:message key="label.findoraddpopupsubtotal.title.window" /></title>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
		
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;

			window.onunload = call;
			
			var templateId = '${requestScope.templateId}';
			var templateName = '${requestScope.templateName}';
			var userId = '${requestScope.userId}';
			var isPublic = '${requestScope.isPublic}';
			var screenName = '${requestScope.screenName}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var	shortName = '${requestScope.shortName}';
			var	description = '${requestScope.description}';
			var	type = '${requestScope.type}';
			var	columnId = '${requestScope.columnId}';
			var ordinalPos = '${requestScope.ordinalPos}';
			var pressedbutton = '${requestScope.pressedbutton}';
			var screenRoute = "subTotalPopUp";
			
			var optionWindow= null;
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			// Search Id
			label["text"]["id"] = "<fmt:message key="label.findoraddpopup.id"/>";
			label["tip"]["id"] = "<fmt:message key="tooltip.findoraddpopup.id"/>";
			
			// Search name
			label["text"]["name"] = "<fmt:message key="label.findoraddpopup.name"/>";
			label["tip"]["name"] = "<fmt:message key="tooltip.findoraddpopup.name"/>";
			
			// Searchbutton
			label["text"]["button-search"] = "<fmt:message key="button.search"/>";
			label["tip"]["button-search"] = "<fmt:message key="tooltip.findoraddpopup.Search"/>";
			
			// Add button
			label["text"]["button-add"] = "<fmt:message key="button.add"/>";
			label["tip"]["button-add"] = "<fmt:message key="button.add"/>";	
			
			label["text"]["button-cancel"] = "<fmt:message key="button.cancel"/>";
			label["tip"]["button-cancel"] = "<fmt:message key="button.cancel"/>";	
			
			/**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshParent(){
			 
				window.opener.refreshSubDetail();
				window.close();
			 }
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
    			var param = 'forecastMonitorTemplate.do?method='+methodName;
	    		optionWindow = openWindow(param,'movementWindow','left=50,top=190,width=670,height=500,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
					
				return false;
			}
			
			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Find pop up Subtotal'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			 /**
			  * closeChild
			  * This function used to close the child window
			  */
			function closeChild(){
				// Added By KaisBS :  issue 1054_STL_038 : avoid the javascript error if we aren't open the option screen
				if(typeof(optionWindow) == window)
				optionWindow.close();
			}
			
		</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();" onunload="closeChild()">
<form id="exportDataForm" target="tmp" method="post">


<input type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<fmt:message key="label.findoraddpopupsubtotal.title.window"/>" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
