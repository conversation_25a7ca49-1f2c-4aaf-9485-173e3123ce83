<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
	<title>EmailTemplate Maintenance- SMART-Predict</title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "EmailTemplateMaintenance";
 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';;
	var menuAccessId = "${requestScope.menuAccessId}";


		function subEmailTemplateMaintenance(screenName, params) {
			console.log("screenName",screenName)
			console.log("params",params)
				var param = "emailTemplateMaintenance.do?method=displayEmailTemplate&screenName="+screenName;
						param += '&subscreen=true&params=' + params;
					var 	mainWindow = openWindow (param, 'subEmailTemplateMaintenance','left=10,top=230,width=800,height=600,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
        return false;
	}


</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">


<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
				value="<fmt:message key="EmailTemplateMaintenance.title.window"/>" /></form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
		</body>
		</html>

