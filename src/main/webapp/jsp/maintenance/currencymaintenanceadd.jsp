<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<title><c:choose>
    <c:when test="{requestScope.methodName == 'save'}">

		<fmt:message key="currencymaitenance.addScreen"/>

    </c:when>
    <c:otherwise>
        <c:if test="${requestScope.methodName == 'update'}">

			<fmt:message key="currencymaitenance.changeScreen"/>

</c:if>
    </c:otherwise>
</c:choose></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">
//Added by Med Amine : to hold dateFormat
var dateFormat =  '${sessionScope.CDM.dateFormat}';

/**
 * When window is load, find each 'tr' that contains a 'td'  has an input
 * which value contains  '*',
 * then apply the style 'required' for each empty input
 * as well as this jquery code call 'validateSummerTimesDates()' function to validate the summer time
 * each time we try to fill the (start/end)date fields
 *
 * Added by Med Amine Ben Ahmed for M 2352 ILAAP
 *
 **/
 $(window).on("load",function(){

	$("td[id='startDate'],td[id='endDate']").each(function(){
		// Get the current td
    	var td = $(this);
    	td.find('input').on('blur keyup', function(){
			 if ($(this).val() == '')
	             $(this).css('border-color','red');
	         else
	             $(this).css('border-color','');
    	});
	});

	$("input[type='text']").not('[disabled="disabled"]').css('background','white');
	$("input[type='checkbox']").on('click', function(){
	 	$("input[type='text']").removeClass('is-disabled');
	   	$("input[disabled='disabled'][type='text']").addClass('is-disabled');
	    $("input[disabled='disabled'][type='text']").css('background','#e5e5e5');
	    $("input[type='text']").css('border-color','');
	    $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function() {
	    	// Get the current tr
	    	var tr = $(this);
        	tr.find('input').removeClass();
        	tr.find('input').css('background-color','');
        	tr.find('input').addClass('inputText');
	    	tr.find('input').css('border-color',function(index){
					if ($(this).val() == '')
					return 'red';
			}).on('blur keyup', function(){
                        if ($(this).val() == '')
                            $(this).css('border-color','red');
                        else
                            $(this).css('border-color','');
                 });
	    });
	});
});

mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);

cancelcloseElements[0] = "cancelbutton";
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="displayListByEntity";
window.opener.document.forms[0].submit();
self.close();

</c:if>


function showCurrencyDropDown()
{
	document.getElementById("dropdowndiv_1").style.visibility="visible";
}

function bodyOnLoad()
{
	<c:if test="${requestScope.methodName == 'save'}">

		var dropBox1 = new SwSelectBox(document.forms[0].elements["currency.id.currencyCode"],document.getElementById("currencyName"));
		var dropBox2 = new SwSelectBox(document.forms[0].elements["currency.currencyGroupId"],document.getElementById("currencyGroupName"));

</c:if>

	 document.getElementById("entityName").innerText = '${entityText}';
	 <c:if test="${requestScope.methodName == 'update'}">

			document.getElementById("currencyName").innerText = '${currencyName}';
			var dropBox2 = new SwSelectBox(document.forms[0].elements["currency.currencyGroupId"],document.getElementById("currencyGroupName"));
			document.getElementById("timeZoneOffset").innerText = '${timeZoneOffset}'.split("**")[1];

</c:if>
     ShowErrMsgWindow('${actionError}');
     maintainStatus();
}
/**
*This method is used to submit the page on clicking the Save button
*@param methodName
**/
function submitForm(methodName){
   //validation for order field
    var orderValue=validateField(document.forms[0].elements['currency.priorityOrder'],'Number 2','numberPat');
	/*Start:Code Modified for 1053 Beta Issue by Chinniah on 20-Sep-2011:"Date integrity violation Occurred" displayed. Enter more values in the Threshold text box && save the same.in this case Currency Group list box getting Empty list*/
	//get the current thresh hold value
	 var digitValidation=document.forms[0].elements['currency.threshold'];
	//codition check for valid decimal no
	if(decimalValidation(digitValidation))
	{

	if(orderValue)
	{
	//validation for thersh hold field
	  var thresholdValue=validateField(document.forms[0].elements['currency.threshold'],'Number 2','decimalPat');

	  if(thresholdValue)
	  {
	  var toleranceValue=validateField(document.forms[0].elements['currency.tolerance'],'Number 2','numberPat');
	    if(toleranceValue)
		{

	var cuttOff=validateField(document.forms[0].elements['currency.cutOffTime'],'Number 2','timePat');
	if(cuttOff)
	{  if(dateFormat == 'datePat2'){


	//validating the whole form
	if(validateForm(document.forms[0]))
	{

		enableFields();

		document.forms[0].method.value = methodName;
		window.opener.postMessage(['refreshParent', []], "*");
		document.forms[0].submit();
	}
	}

	else if(dateFormat == 'datePat1'){

	//validating the whole form
	if(validateForm(document.forms[0]))
	{

		enableFields();
		window.opener.postMessage(['refreshParent', []], "*");
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}

	}
	}

	else
	{
	  document.forms[0].elements['currency.cutOffTime'].focus();
	}
	}
	else
	{
	  document.forms[0].elements['currency.tolerance'].focus();
	}
	}
	else
	{
	 document.forms[0].elements['currency.threshold'].focus();
	}
	}
	else
	{
	  document.forms[0].elements['currency.priorityOrder'].focus();
	}
	}
	/*End:Code Modified for 1053 Beta Issue by Chinniah on 20-Sep-2011:"Date integrity violation Occurred" displayed. Enter more values in the Threshold text box && save the same.in this case Currency Group list box getting Empty list*/
}


function validateForm(objForm){

  var elementsRef = new Array(3);

  elementsRef[0] = objForm.elements["currency.id.currencyCode"];

 elementsRef[1] = objForm.elements["currency.currencyGroupId"];
 elementsRef[2] = objForm.elements["currency.priorityOrder"];


  return validate(elementsRef);
}

/*Added by Med Amine for M 2352 : ILAAP
 *
 * Used to validate date fields
 */
function validateDateField(elem,fieldName){
   if(dateFormat == 'datePat2'){
		if(validateField(document.forms[0].elements[elem],fieldName,'datePat4'))
			return true;
		else
		    return false;
	}
	else if(dateFormat == 'datePat1'){

		if(validateField(document.forms[0].elements[elem],fieldName,'datePat1'))
			return true;
		else
		     return false;
	}
}


function enableFields()
{
	var entityIdElement = document.forms[0].elements["currency.id.entityId"];
	var currencyCodeElement = document.forms[0].elements["currency.id.currencyCode"];

	if(entityIdElement)
		entityIdElement.disabled = "";

	if(currencyCodeElement)
		currencyCodeElement.disabled = "";
		document.getElementById("currencyName").disabled = "";
		document.getElementById("entityName").disabled = "";

}
/*Start:Code Modified for 1053 Beta Issue by Chinniah on 20-Sep-2011:"Date integrity violation Occurred" displayed. Enter more values in the Threshold text box && save the same.in this case Currency Group list box getting Empty list*/
/**
*This method is used to validate the threshhold value- The Threshold value should not exceed 5 digits in whole/fractional part of threshold field
* @param obj
* @return validNoFlag
**/
function decimalValidation(obj)
{
//get the tresh hold value
var threshhold=obj.value;
//intialize a flag for determining the valid number
var validNoFlag=true;
//validation for decimal no
if(threshhold.indexOf(".")!=-1)
 {
 var arrVlaues=threshhold.split(".");
 var beforeDecimal = arrVlaues[0];
 var afterDecimal = arrVlaues[1];
 //checking wheather the whole part is having more than 5 digits
 if(beforeDecimal.length>5 || afterDecimal.length>5)
  {
   alert('<fmt:message key="currencymaintenance.alert.validDecimal"/>');
  validNoFlag = false;
  }
 }
else if(threshhold.length>5)//validation for whole no
 {
  alert('<fmt:message key="currencymaintenance.alert.validDecimal"/>');
  validNoFlag = false;
  }
return validNoFlag;
}

function submitMaintain(actionMethod){
	// disabled the currency field once the DSt currency is opened
 	document.forms[0].elements["currency.id.currencyCode"].disabled = true;
 	document.forms[0].elements["currency.id.currencyCode"].className='is-disabled';

	var param = 'currency.do?method='+actionMethod+'&screenFieldsStatus=';
	param +=  '${requestScope.screenFieldsStatus}';
	param += '&selectedEntityId='+document.forms[0].elements["currency.id.entityId"].value;
	param += '&selectedEntityName='+document.getElementById("entityName").innerText;
	param += '&selectedCcyCode='+document.forms[0].elements["currency.id.currencyCode"].value;
	param += '&selectedCcyName='+document.getElementById("currencyName").innerText;
	return  param;

}

function getAppropriateCurrencyOffset(){
	var appName = "<%=SwtUtil.appName%>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	var selectedCcyValue = document.forms[0].elements["currency.id.currencyCode"].value;
	requestURL=requestURL.substring(0,idy+1) ;
	requestURL = requestURL + appName+"/currency.do?method=getAppropriateCurrencyOffset"
	requestURL = requestURL + "&currencyCode=" + selectedCcyValue;
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open("POST", requestURL, false);
	oXMLHTTP.send();
	var res = new String(oXMLHTTP.responseText);
	if (res.split("**")[0] != null)
	document.forms[0].elements["currency.currencyMaster.ccyTimeZone"].value = res.split("**")[0];
	if (res.split("**")[1] != null) document.getElementById("timeZoneOffset").innerText = res.split("**")[1];

}

	function maintainStatus() {
		<c:if test="${requestScope.methodName !='update'}">

		clearSessionValues();
		getAppropriateCurrencyOffset();

</c:if>
	}



	function closeHandler(){
		window.opener.postMessage(['clearParams', []], "*");
		self.close();
	}


	function clearSessionValues(){
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');

			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName
					+ "/currency.do?method=clearSessionValues";

			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			oXMLHTTP.send();
			var res = new String(oXMLHTTP.responseText);

	}


	/*End:Code Modified for 1053 Beta Issue by Chinniah on 20-Sep-2011:"Date integrity violation Occurred" displayed. Enter more values in the Threshold text box && save the same.in this case Currency Group list box getting Empty list*/
</SCRIPT>
<script language="JAVASCRIPT">
var dateFormatValue =  '${sessionScope.CDM.dateFormatValue}';
  var cal = new CalendarPopup("caldiv");
	cal.offsetX = -3;
	cal.offsetY = -97;
</script>


</head>

<form action="currency.do" method="post">
	<input name="method" type="hidden" value="save">
	<c:if test="${requestScope.methodName == 'save'}">

		<body leftmargin="0" topmargin="0" marginheight="0"
			onLoad="bodyOnLoad(); setParentChildsFocus();setFocus(document.forms[0]);"
			onunload="call(); closeHandler()">

</c:if>
	<c:if test="${requestScope.methodName == 'update'}">

		<body leftmargin="0" topmargin="0" marginheight="0"
			onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);"
			onunload="call(); closeHandler()">

</c:if>


	<div id="CurrencyAdd"
		style="position: absolute; left: 20px; top: 20px; width: 570px; height: 405px; border: 2px outset;"
		color="#7E97AF">
		<div id="CurrencyAdd"
			style="position: absolute; left: 8px; top: 6px; width: 570px; height: 225;">
			<table width="559px" border="0" cellpadding="0" cellspacing="0"
				height="148" class="content">
				<tr height="23">
					<td width="116px"><b><fmt:message key="entity.id"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text"  class="htmlTextAlpha" name="currency.id.entityId" value="${currency.id.entityId}" style="width:120px;" disabled="disabled" /></td>
					<td width="20px">&nbsp;</td>
					<td width="280px" style="word-break: break-all;"><span id="entityName" name="requestScope.entityName"
						class="spantext"></td>

				</tr>
				<tr height="25">
					<c:if test="${requestScope.methodName == 'save'}">

						<td width="116px"><B><fmt:message key="currency.currencyCode"/></B>*</td>
						<td width="28px">&nbsp;</td>
						<td width="120px">
							<div style="width: 55px">
				 <select id="ccyCodeId" name="currency.id.currencyCode" tabindex="1" titleKey="tooltip.CurrencyIdentifier"
	        	 onchange="maintainStatus()" class="htmlTextAlpha" style="width:55px">
       				<c:forEach var="item" items="${requestScope.currencyMaster}">
       			<option value="${item.value}">
       			${item.label}</option>
    				</c:forEach>
				</select>
							</div>
						</td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="currencyName" name="requestScope.currencyName"
							class="spantext"></td>

</c:if>
					<c:if test="${requestScope.methodName == 'update'}">

						<td width="116px"><B><fmt:message key="currency.currencyCode"/></B></td>
						<td width="28px">&nbsp;</td>
						<td width="120px"><input type="text"  class="htmlTextAlpha" name="currency.id.currencyCode" value="${currency.id.currencyCode}" style="width:37px" disabled="disabled" /></td>
						<td width="20px">&nbsp;</td>
						<td width="280px"><span id="currencyName" name="requestScope.currencyName"
							class="spantext"></td>

</c:if>

				</tr>
				<!-- Currency group added -->
				<tr height="25px">
					<td width="116px"><B><fmt:message key="currency.currencyGroup"/></B>*</td>
					<td width="28px">&nbsp;</td>
					<td width="120px">
					<select id="ccyGrpId" name="currency.currencyGroupId" tabindex="2" titleKey="currency.currencyGroup"
	        	    class="htmlTextAlpha" style="width:120px">
       				<c:forEach var="item" items="${requestScope.currencyGroupList}">
       			<option value="${item.value}" <c:if test="${currency.currencyGroupId == item.value}">selected</c:if>>
       			${item.label}</option>
    				</c:forEach>
				</select>
						</td>
					<td width="20px">&nbsp;</td>
					<td width="280px"><span id="currencyGroupName"
						name="requestScope.currencyGroupName" class="spantext"></td>
				</tr>
				<!-- Order field added -->
				<tr height="25px">
					<td width="116px"><B><fmt:message key="currency.order"/></B>*</td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text" name="currency.priorityOrder" value="${currency.priorityOrder}" maxlength="2" class="htmlTextNumeric" titleKey="currency.order" onchange="return validateField(this,'Number 2','numberPat');" style="width:55px" tabindex="3" /></td>

				</tr>

				<!-- Multiplier field added -->

				<tr height="25px">
					<td width="116px"><B><fmt:message key="currency.multiplier"/></B>*</td>
					<td width="28px">&nbsp;</td>
					<td width="120px">

				<select id="ccyMultiplierId" name="currency.multiplier" tabindex="4" titleKey="tooltip.selectMultiplier"
	        	  class="htmlTextAlpha" style="width:120px">
       			<c:forEach var="item" items="${requestScope.multiplierList}">
       			<option value="${item.value}" <c:if test="${currency.multiplier == item.value}">selected</c:if>>
       		    ${item.label}</option>
    				</c:forEach>
				</select>
						</td>
					<td width="20px">&nbsp;</td>
				</tr>


				<tr height="25">
					<td width="116px"><b><fmt:message key="currency.exchangeRate1"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px">
					<input type="text" name="currency.exchangeRate" value="${currency.exchangeRate}" maxlength="20" class="htmlTextNumeric" titleKey="currency.exchangeRate1" style="width:120px"  onchange="return validateField(this,'Number 2','decimalPat');" tabindex="5" disabled="true" /></td>
				</tr>

				<tr height="25">
					<td width="116px"><b><fmt:message key="currency.interestBasis1"/></b>*</td>
					<td width="28px">&nbsp;</td>
					<td width="120px">

				<select id="ccyInterestBasisId" name="currency.interestBasis" tabindex="2" titleKey="currency.interestBasis1"
	        	 class="htmlTextAlpha" style="width:52px">
       				<c:forEach var="item" items="${requestScope.interestBasis}">
       			<option value="${item.value}" <c:if test="${currency.interestBasis == item.value}">selected</c:if>>
       			${item.label}</option>
    				</c:forEach>
				</select></td>
				</tr>

				<!-- Threshold  field added -->

				<tr height="25">
					<td width="180px"><b><fmt:message key="currency.threshold"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text" name="currency.threshold" value="${currency.threshold}" maxlength="10" class="htmlTextNumeric" titleKey="currency.threshold" style="width:120px" onchange="return validateField(this,'Number 2','decimalPat');" onblur="decimalValidation(this)" tabindex="7" /></td>
				</tr>


				<tr height="25">
					<td width="116px"><b><fmt:message key="currency.toleranceAdd"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text" name="currency.tolerance" value="${currency.tolerance}" class="htmlTextNumeric" style="width:65px" titleKey="currency.toleranceAdd" maxlength="7" onchange="return validateField(this,'Number 2','numberPat');" tabindex="8" /></td>
				</tr>
				<tr height="25">
					<td width="116px"><b><fmt:message key="currency.cutOffTime1"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text" name="currency.cutOffTime" value="${currency.cutOffTime}" class="htmlTextNumeric" style="width:50px" titleKey="currency.CutoffTime" maxlength="5" onchange="return validateField(this,'Number 2','timePat');" tabindex="9" /></td>
				</tr>
				<tr height="25">
					<td width="140px">
						<b style="width: 140px"><fmt:message key="currency.preFlag" /></b>
					</td>
					<td width="25px">&nbsp;</td>
					<td width="170px">
						<input type="radio" id="1" name="currency.preFlag" style="width:13px"
							   value="1"
							   <c:if test="${currency.preFlag == '1'}">checked="checked"</c:if>
							   titleKey="currency.EnableCurrencyForPredict" />
						<label for="1" tabindex="9" title="<fmt:message key='currency.EnableCurrencyForPredict' />">
							<fmt:message key="currency.preFlag.active" />
						</label>&nbsp;&nbsp;&nbsp;&nbsp;

						<input type="radio" id="2" name="currency.preFlag" style="width:13px"
							   value="2"
							   <c:if test="${currency.preFlag == '2'}">checked="checked"</c:if>
							   titleKey="currency.DisableCurrencyForPredict" />
						<label for="2" tabindex="10" title="<fmt:message key='currency.DisableCurrencyForPredict' />">
							<fmt:message key="currency.preFlag.InActive" />
						</label>
					</td>
				</tr>

            </table>
			<table>
			<tr height="23">
					<td width="130px"><b><fmt:message key="tooltip.timeZoneRegion"/></b></td>
					<td width="28px">&nbsp;</td>
					<td width="120px"><input type="text" id="ccyTimeZoneText" class="htmlTextAlpha" name="currency.currencyMaster.ccyTimeZone" value="${currency.currencyMaster.ccyTimeZone}" style="width:220px;" disabled="disabled" /></td>
					<td width="20px">&nbsp;</td>
					<td width="80px" style="word-break: break-all;"><span
						id="timeZoneOffset" name="requestScope.timeZoneOffset" class="spantext"></td>


				</tr>
			</table>
		</div>
	</div>
	<DIV id="caldiv" style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></DIV>
	<div id="CurrencyAdd"
		style="position: absolute; left: 515; top: 440; width: 70px; height: 39px; visibility: visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td align="Right"><c:choose>
    <c:when test="{requestScope.methodName == 'save'}">

						<a tabindex="15" href=#
							onclick="javascript:openWindow(buildPrintURL('print','Add Currency Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
							src="images/help_default.GIF " name="Help" border="0"
							title='<fmt:message key="tooltip.helpScreen"/>'></a>

    </c:when>
    <c:otherwise>
        <c:if test="${requestScope.methodName == 'update'}">

							<a tabindex="16" href=#
								onclick="javascript:openWindow(buildPrintURL('print','Change Currency Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
    </c:otherwise>
</c:choose></td>

				<td align="right" id="Print"><a tabindex="17"
					onclick="printPage();" onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
						src="images/Print.gif " name="Print" border="0"
						title='<fmt:message key="tooltip.printScreen"/>'></a></td>
			</tr>
		</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; left: 20; top: 430; border: 2px outset; width: 570px; height: 39px; visibility: visible;">
		<div id="CurrencyAdd"
			style="position: absolute; left: 6; top: 4; width: 425; height: 15px; visibility: visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td title='<fmt:message key="tooltip.save"/>'><a tabindex="18"
						width="70" onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:submitForm('${methodName}');"><fmt:message key="button.save"/></a></td>
					<td id="cancelbutton" title='<fmt:message key="tooltip.cancel"/>'>
						<a tabindex="19" onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="confirmClose('C');clearSessionValues()"><fmt:message key="button.cancel"/></a>
				</tr>
			</table>
		</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>