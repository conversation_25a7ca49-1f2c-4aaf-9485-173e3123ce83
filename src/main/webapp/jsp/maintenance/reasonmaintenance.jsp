<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.ReasonMaintenance"%>
<html>
<head>
<title><fmt:message key="reasonMaintenance.mainTitle"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<script language="javascript">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	
}
function bodyOnLoad()
{
xl = new XLSheet("reasonMaintenanceDetailsTable","table_2", ["String","String"],"11");
xl.onsort = xl.onfilter = onFilterandSort;
highlightTableRows("reasonMaintenanceDetailsTable");	

	
document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
    

var dropBox1 = new SwSelectBox(document.forms[0].elements["reasonMaintenance.id.entityId"],document.getElementById("entityName"));		
if(menuEntityCurrGrpAccess == "0"){
	
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;	
	}
document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
}

function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement0 = rowElement.cells[0].innerText.trim();	
	var hiddenElement1 = rowElement.cells[1].innerText.trim();	
	document.forms[0].selectedReasonCode.value = hiddenElement0;
	document.forms[0].selectedDescription.value = hiddenElement1;
	//Modified by Atef S. for Mantis 1769 : Change and delete buttons are enabled in reason maintenance screen
	if(menuEntityCurrGrpAccess == "0" && isRowSelected(rowElement) )
		{
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;	
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;	
					
		}else{	
		
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}
	
}


function buildAddReasonMaintenance(methodName){
	
	var param = 'reasonMaintenance.do?method='+methodName+'&entityId=';
	param +=  document.forms[0].elements["reasonMaintenance.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	return  param;	
}

/**
  *	 To click the change Button this method is executed,to open
  * change Reason maintenace Screen. 
  * @ param
  * @ return param  
  */
function buildChangeReasonMaintenance(methodName){
	var param = 'reasonMaintenance.do?method='+methodName+'&entityId=';
	param +=  document.forms[0].elements["reasonMaintenance.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	param += '&selectedReasonCode=' + document.forms[0].selectedReasonCode.value ;
	<!--code modified by nageswararao for mantis 1580 issues found on STL testing-->
	param += '&selectedDescription=' + escape(encodeURIComponent(document.forms[0].selectedDescription.value)) ;
	return  param;	
}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 
	document.forms[0].submit();
	}
}

</script>
</head>
<body  onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()"  >
<form action="reasonMaintenance.do"  >
<input name="method" type="hidden" >
<input name="entityCode" type="hidden" >
<input name="selectedReasonCode" type="hidden" >
<input name="selectedDescription" type="hidden" >
<input name="menuAccessId" type="hidden" >

  
 <div id="ReasonMaintenance" style="position:absolute; left:20px; top:21px; width:701px; height:42px; border:2px outset;" color="#7E97AF">
 
 <div id="ReasonMaintenance" style="position:absolute; left:8px; top:4px; width:470px; height:380;">
 <table width="468" border="0" cellpadding="0" cellspacing="0" height="30">
 	<tr color="black" border="0">
 	  <td width="38"><b><fmt:message key="reasonMaintenance.entity"/></b></td>
 	  <td width="28">&nbsp;</td>
 	  <td width="140px">
 	  
 	  
 	  <select
			    id="reasonMaintenance.id.entityId"
			    name="reasonMaintenance.id.entityId"
			    class="htmlTextAlpha"
			    onchange="submitForm('displayListByEntity')"
			    style="width:140px"
			    titleKey="tooltip.entityId"
			    tabindex="1">

		  <c:forEach items="${requestScope.entities}" var="entity">
               <option
                  value="${entity.value}"
                  <c:if test="${reasonMaintenance.id.entityId == entity.value}">selected="selected"</c:if>>
                  ${entity.label}
               </option>
			</c:forEach>
			</select>

 	  </td>
 	   <td width="20">&nbsp;</td>
 	  <td width="280">
 		<span id="entityName" name="entityName" class="spantext">
 	   </td>
 	 </tr>
 </table>
</div>
</div>


<div id="ReasonMaintenance" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:67px; width:701px; height:279px;">
	<div id="ReasonMaintenance" style="position:absolute;z-index:50;left:0px; top:0px; width:680px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="680x" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr height="23px">
		    <td  title ='<fmt:message key="reasonCodeTooltip.sortreasonCode"/>' width="230px" style="padding-left: 0px;"><b><fmt:message key="reasonMaintenance.reasonCode"/></b></td>
			<td title ='<fmt:message key="descriptionTooltip.sortdescription"/>' width="450px" style="padding-left: 0px;"><b><fmt:message key="reasonMaintenance.description"/></b></td>
			
			
	
		</tr>
	</thead>
</table>
 </div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:698px; height:275px; overflowY:scroll;overflow-x:hidden; ">
	<div id="ReasonMaintenance" style="position:absolute;z-index:50;left:0px; top:22px; width:680px; height:10px;">
		<table class="sort-table" id="reasonMaintenanceDetailsTable" width="680px" border="0" cellspacing="1" cellpadding="0" height="250">
	<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="reason" items="${requestScope.reasonMaintenanceDetails}">

        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <td align="left" width="230px">
                ${reason.id.reasonCode}&nbsp;
            </td>
            <td width="450px" align="left">
                ${reason.description}&nbsp;
            </td>
        </tr>
        <c:set var="count" value="${count + 1}"/>
    </c:forEach>
    </tbody>
	<tfoot><tr><td colspan="2" ></td></tr></tfoot>
</table>
</div>
</div>

 <div id="ReasonMaintenance" style="position:absolute; left:440; top:290; width:70px; height:15px; visibility:visible;">
	<table width="250px" border="0" cellspacing="0" cellpadding="0" height="0">
		<tr>
		    <td align="Right" width="205px">
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Reason Maintenance'),'sectionprintdwindow','left=50,top=190,width=412,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
            </td><td>&nbsp;&nbsp;</td>

			<td align="left" width="40px" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div> 
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:-2px; top:282px; width:701px; height:39px; visibility:visible;">
  <div id="ReasonMaintenance" style="position:absolute; left:6; top:4; width:701; height:15px; visibility:visible;">
  	  <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="width: 270px">
	<tr>
		<td id="addbutton" width="70"> </td>
		<td id="changebutton" width="70"> </td>
		<td id="deletebutton" width="70" ></td>		
		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
	</tr>
	</table>
</div>
<div style="position:absolute; left:0px; top:4px; width:708px; height:15px; visibility:hidden;">  	
    <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton" >		
		<a tabindex="1" title='<fmt:message key="tooltip.AddNewReasonCode"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddReasonMaintenance('add'),'reasonmaintenancechildWindow','left=50,top=190,width=680,height=188,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
		</td>		
		<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
		</td>
		
		<td id="changeenablebutton">		
		<a tabindex="2" title='<fmt:message key="tooltip.ChangeSelectedReasonCode"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeReasonMaintenance('change'),'reasonmaintenancechildWindow','left=50,top=190,width=680,height=188,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
		</td>
		
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
		</td>
		<td id="deleteenablebutton"  >		
			<a  tabindex="3" title='<fmt:message key="tooltip.DeleteSelectedReasonCode"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><fmt:message key="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>	
	
	</tr>
    </table>
  </div>
</div>

<blockquote>&nbsp;</blockquote>


<script type="text/javascript">
</script>
</form>
</body>
</html>
 
  
