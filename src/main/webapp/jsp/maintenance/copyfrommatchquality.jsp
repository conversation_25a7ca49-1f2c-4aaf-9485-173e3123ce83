<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<title><fmt:message key="matchQuality.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var currencyId = '${requestScope.currencyId}';
var posLevel = '${requestScope.posLevel}';
mandatoryFieldsArray=["*"];

function submitForm(methodName){
	 if(validateForm(document.forms[0]) )
	{
	 
	 window.opener.document.forms[0].selectedPostionLevel.value=document.forms[0].elements['matchQuality.id.posLevel'].value;
	 window.opener.document.forms[0].selectedCurrencyCode.value=document.forms[0].elements['matchQuality.id.currencyCode'].value;
	//window.opener.document.forms[0].selectedEntityName.value=document.forms[0].elements['entityName'].value;
	 window.opener.document.forms[0].method.value = methodName;
	 window.opener.document.forms[0].posLevel.value = posLevel;
	 window.opener.document.forms[0].currencyId.value = currencyId;
	 window.opener.document.forms[0].submit();
	 self.close();
	}
 
}
function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["matchQuality.id.currencyCode"];
  elementsRef[1] = objForm.elements["matchQuality.id.posLevel"];
  return validate(elementsRef);
}
function submitCurrencyId(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].currencyId.value = currencyId;
	document.forms[0].posLevel.value = posLevel;
	window.opener.document.forms[0].selectedCurrencyCode.value=document.forms[0].elements['matchQuality.id.currencyCode'].value;
	document.forms[0].submit();
}
function bodyOnLoad(){
var dropBox1 = new SwSelectBox(document.forms[0].elements["matchQuality.id.currencyCode"],document.getElementById("currencyName"));


}
</SCRIPT> 
</head>

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"  onunload="call()">
<form action="matchQuality.do">
<input name="entityName" type="hidden" value= "${entityName}">
<input name="method" type="hidden" value="copy">
<input name="currencyId" type="hidden" value="add">
<input name="posLevel" type="hidden" value="add">

<div id="matchQualityCopyFrom" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:470px; height:59px;">
<div id="matchQualityCopyFrom" style="position:absolute;z-index:99;left:8px; top:4px; width:463px; height:46px;">
<table width="473" height="46" border="0" cellspacing="0" cellpadding="0">
	 
	  <tr height="24" color="black" border="0">
	  <td width="98"><b><fmt:message key="matchQuality.currencyCode"/></b>*</td>
	  <td width="28">&nbsp;</td>
	  <td width="37" title='<fmt:message key="tooltip.SelectCurrency"/>'>
		 
		  <select styleClass="htmlTextAlpha" name="matchQuality.id.currencyCode" style="width:55px"  onchange="javascript:submitCurrencyId('filterPositionlevel');" tabindex="1">
    			<c:forEach items="${requestScope.currency}" var="ccy">
				   <option
					  value="${ccy.value}"
					  <c:if test="${matchQuality.id.currencyCode == ccy.value}">selected="selected"</c:if>>
					  ${ccy.label}
				   </option>
				</c:forEach>
		  </select>
	 </td>
	 <td width="20">&nbsp;</td>
	 <td width="280">
	    <span id="currencyName" name="currencyName" class="spantext">
	  </td>
	</tr>

 	<tr height="24" color="black" border="0">
	  <td width="105"><b><fmt:message key="matchQuality.posLevel"/></b>*</td>
	  <td width="28">&nbsp;</td>
	  <td width="37">
	  <div style="width:40px">
		<select styleClass="htmlTextAlpha" name="matchQuality.id.posLevel" style="width:40px" title='<fmt:message key="tooltip.selectPosLevel"/>' tabindex="2">
				<c:forEach items="${requestScope.position}" var="pos">
				   <option
					  value="${pos.value}"
					  <c:if test="${matchQuality.id.posLevel == pos.value}">selected="selected"</c:if>>
					  ${pos.label}
				   </option>
				</c:forEach>
		</select>
	  </div>
	  </td>
	<td width="20">&nbsp;</td>
	 <td width="280">
	    <span id="posLvlName" name="posLvlName" class="spantext">
	  </td>
	</tr>
   </table> 
</div>
</div>

<div id="matchQualityCopyFrom" style="position:absolute; left:414; top:93; width:70px; height:15px; visibility:visible;">
	<table width="60px" cellspacing="0" cellpadding="0" height="20" border="0">
		<tr>
		
			<td align="Right">
			<!--start:sarada10/11/08 modify help contents-->
				 <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Copy From Match Quality'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
		   			<!--end:sarada10/11/08 modify help contents--></td>
		   		   </td>

			<td align="right" valign="middle" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif" name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:84px; width:470px; height:39px; visibility:visible;">
<div id="matchQualityCopyFrom" style="position:absolute; left:5; top:5; width:140px; height:15px; visibility:visible;">
<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
    <tr>
    <c:if test="${requestScope.methodName != 'Hide'}">
      <td width="70" ><a title='<fmt:message key="tooltip.CopyMatchQualityProfile"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('copy')"><fmt:message key="button.copy"/></a></td>
    </c:if>
    <td width="70" id="cancelbutton">		
		<a title='<fmt:message key="tooltip.cancel"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>			
	</td>
	<c:if test="${requestScope.methodName == 'Hide'}">
		<td width="70">&nbsp;</td>
	</c:if>
    </tr>
</table>
</div>
</div>
 
</form>
</body>
</html>
