<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>


<attributeusagesummaryadd name="attributeusagesummaryadd" restriction="${restriction}" displayOrder="${displayOrder}" grandTotal="${grandTotal}">

	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location /> 
	</request_reply> 

	<selects>
		<select id="functionalGrpList">
			<c:forEach items="${requestScope.functionalGrpList}" var="functionalGrps">
				<option
						value="${functionalGrps.id.functionalGroup}"
						selected="${requestScope.functionalGrp == functionalGrps.id.functionalGroup ? '1' : '0'}"
				>${fn:escapeXml(functionalGrps.functionalGroupDesc)}</option>
			</c:forEach>
		</select>
		<select id="acctAttributHDRList">
			<c:forEach items="${requestScope.acctAttributHDRList}" var="acctAttributHDRList">
				<option
						value="${acctAttributHDRList.id.attributeId}"
						selected="${requestScope.attributeId == acctAttributHDRList.id.attributeId ? '1' : '0'}"
				>${fn:escapeXml(acctAttributHDRList.attributeName)}</option>
			</c:forEach>
		</select>
	</selects>
</attributeusagesummaryadd>
