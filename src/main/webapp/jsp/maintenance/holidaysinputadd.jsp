<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<title><fmt:message key="holidays.addScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
mandatoryFieldsArray=["*"]
/*Start : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
var dateSelected = false;
/*End : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="cancelbutton";
var dateFormat = '${sessionScope.CDM.dateFormat}';
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="showDetails";
window.opener.document.forms[0].submit();
self.close();
</c:if>

function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["holiday.holidayDateAsString"];
  return validate(elementsRef);
 }

 function bodyOnLoad()
{
	var dropBox1 = new SwSelectBox(document.forms[0].elements["holiday.id.countryCode"],document.getElementById("countryName"));
}

function submitForm(methodName){
if(validateForm(document.forms[0]) ){
	/*Start : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	if(validateDateField(document.forms[0].elements['holiday.holidayDateAsString'],'holiday.holidayDate',dateFormat)){
	/*End : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	    enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
		/*Start : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
		}
		/*End : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	}
}


function enableFields(){
	document.forms[0].elements["holiday.id.entityId"].disabled = "";
	document.forms[0].elements["holiday.id.countryCode"].disabled = "";	
	
	document.getElementById("entityName").disabled = "";	
}

/* Start:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */
function validateHolidayField(){
	if(document.forms[0].elements['holiday.holidayDateAsString']!=null && document.forms[0].elements['holiday.holidayDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['holiday.holidayDateAsString'],'holiday.holidayDate',dateFormat)){
		}
	} else {
		alert('<fmt:message key="alert.enterValidDate"/>');
	}	
}
/* End:Marshal: Method added for Date field editable: for Mantis 1262 on 17-11-2010 */	
	
</script>  

<script language="JAVASCRIPT">
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var cal = new CalendarPopup("caldiv"); 

  cal.offsetX = 22;
  cal.offsetY = -73;
</script> 
</head>


<body leftmargin="0" topmargin="0" marginheight="0" onLoad="ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"onunload="call()">
<form action="holiday.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="display">

<div id="Holiday" style="position:absolute; left:20px; top:20px; width:550px; height:85px; border:2px outset;" color="#7E97AF">

<div id="Holiday" style="position:absolute; left:8px; top:4px; width:550px; height:72;">
<table width="538" border="0" cellpadding="0" cellspacing="0" height="30">
	<tr height="24">
	  <td width="100px" ><b><fmt:message key="bookCode.entity"/></b></td>
	  <td width="28px" >&nbsp;</td>
	  <td width="120px">
	  
	 <c:if test="${requestScope.screenFieldsStatus == 'true'}">
		<input type="text" name="holiday.id.entityId" value="${holiday.id.entityId}" style="width:120px;" class="htmlTextAlpha" disabled="true" titleKey="tooltip.entityId"/></td>
     </c:if>
     <c:if test="${requestScope.screenFieldsStatus != 'true'}">
     	<input type="text" name="holiday.id.entityId" value="${holiday.id.entityId}" style="width:120px;" class="htmlTextAlpha" disabled="" titleKey="tooltip.entityId"/></td>
     </c:if>
     
     
   
    <td width="24" >&nbsp;</td>
	  <td width="320" >
	   <input value="${entityName}" name="entityName" style="background:transparent; border:thin;width:190px;" />
	
	   </td>
     
 </tr>

 <tr height="24">
		   <td width="100px" ><b><fmt:message key="holiday.countryId"/></b>*</td>
		   <td width="28px" >&nbsp;</td>
		   <td width="120px" >
			  		
			  	<select  name="holiday.id.countryCode"  tabindex="1" titleKey="tooltip.countryCode" class="htmlTextAlpha" style="width:45px">
					<c:forEach items="${requestScope.countries}" var="country">
               			<option
						  value="${country.value}"
						  <c:if test="${holiday.id.countryCode == country.value}">selected="selected"</c:if>>
						  ${country.label}
               			</option>
					</c:forEach>
				</select>
		</td>
		<td width="24" >&nbsp;</td>
		 <td width="320" >

			  		<span id="countryName" name="#request.countryName" class="spantext">
			  		<!-- <html:text property="holiday.id.countryCode" style="width:30px;" class="htmlTextAlpha" disabled="${requestScope.screenFieldsStatus}" /> -->
			</td>
</tr>

 <tr height="24">
   <td width="128px" > <b><fmt:message key="holiday.holidayDate"/></b>* </td>	
  <td width="28px" >&nbsp;</td>
  		  		<!-- Betcy:24/06/2008 :Added new propert to disable the text box -->
  		  		<!-- Start : Modified by Marshal for Mantis 1262 -->
  <td><input type="text" name= "holiday.holidayDateAsString" titleKey="tooltip.enterHolidayDate" class="htmlTextNumeric"  maxlength="10" tabindex="1" style="width:80px;margin-bottom:5px;height:20px;" onchange="return validateField(this,'holiday.holidayDate',dateFormat);validateHolidayField();" onmouseout="dateSelected=false;"/>
  <!-- End : Modified by Marshal for Mantis 1262 -->
	 &nbsp;<A title='<fmt:message key="tooltip.selectHolidayDate"/>' tabindex="2" name="datelink" ID="datelink" onClick="cal.select(document.forms[0].elements['holiday.holidayDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img src="images/calendar-16.gif"></A> </td>
 </tr>
 </table>
</div>
</div>
<div id="Holiday" style="position:absolute; left:495; top:118px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">

				<td align="Right">
					<a tabindex="5" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Holiday Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
		    </td>

	 		<td align="right" id="Print">
				<a tabindex="5" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:110; width:550; height:39px; visibility:visible;">
  <div id="Holiday" style="position:absolute; left:6; top:4; width:460; height:15px; visibility:visible;">
  <table width="150" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			
			<td id="savebutton" width="70px">
			<a title='<fmt:message key="tooltip.save"/>' tabindex="3"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save')"><fmt:message key="button.save"/></a>
			</td>
			<td id="cancelbutton" width="70px">		
				<a title='<fmt:message key="tooltip.cancel"/>' tabindex="4"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>			
			</td>
		</tr>
		</table>
	</div>
</div>
</form >
<DIV ID="caldiv" STYLE="position:absolute;visibility:hidden;background-color:white;layer-background-color:white;"></DIV>
</body>
</html>
