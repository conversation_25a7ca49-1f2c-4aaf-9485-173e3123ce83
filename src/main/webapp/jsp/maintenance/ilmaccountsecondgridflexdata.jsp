<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>




<ilmaccountgroupdetails name="ilmaccountgroupdetails" >

	<request_reply>
		<status_ok>
				${requestScope.reply_status_ok}
		</status_ok>
		<message>
				${requestScope.reply_message}
		</message>
		<location />
	</request_reply>
	<timing>
		<c:forEach items="${requestScope.opTimes}" var="opTime">
			<operation id="${opTime.key}">${opTime.value}</operation>
		</c:forEach>
	</timing>
	<singletons>
		<exceptioninquery>
				${exceptionInQuery}
		</exceptioninquery>
		<selectedGroup>
				${secondGridAccountGroup}
		</selectedGroup>
	</singletons>

	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key='ilmAccountGroupDetails.accountIdName' />"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="account_id_name"
						sort="false"
						width="${requestScope.column_width['accountIdName']}"/>
				<column heading="<fmt:message key='ilmAccountGroupDetails.type' />"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="type"
						sort="false"
						width="${requestScope.column_width['type']}"/>
				<column heading="<fmt:message key='ilmAccountGroupDetails.class' />"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="class"
						sort="true"
						width="${requestScope.column_width['class']}"/>
				<column heading="<fmt:message key='ilmAccountGroupDetails.level' />"
						draggable="false"
						filterable="true"
						type="str"
						dataelement="level"
						sort="true"
						width="${requestScope.column_width['level']}"/>
			</columns>

		</metadata>

		<rows size="${rowSize}">
			<c:forEach items="${requestScope.accountInGroup}" var="accountInGroup">
				<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false"  >

					<account_id_name clickable="false">
							${accountInGroup.id.accountId} - ${accountInGroup.acctname}
					</account_id_name>
					<type clickable="false">
						<c:choose>
							<c:when test="${accountInGroup.accttype == 'C'}">Cash</c:when>
							<c:when test="${accountInGroup.accttype == 'U'}">Custodian</c:when>
						</c:choose>
					</type>
					<class clickable="false">
						<c:choose>
							<c:when test="${accountInGroup.acctClass == 'C'}">Current</c:when>
							<c:when test="${accountInGroup.acctClass == 'L'}">Loro</c:when>
							<c:when test="${accountInGroup.acctClass == 'E'}">Netting</c:when>
							<c:when test="${accountInGroup.acctClass == 'N'}">Nostro</c:when>
							<c:when test="${accountInGroup.acctClass == 'O'}">Others</c:when>
						</c:choose>
					</class>
					<level clickable="false">
						<c:choose>
							<c:when test="${accountInGroup.acctlevel == 'M'}">Main</c:when>
							<c:otherwise>Sub</c:otherwise>
						</c:choose>
					</level>
					<entity_id clickable="false">
							${accountInGroup.id.entityId}
					</entity_id>
					<account_id clickable="false">
							${accountInGroup.id.accountId}
					</account_id>
				</row>
			</c:forEach>
		</rows>
	</grid>

	<selects>
		<select id="secondgridgroups">
			<c:if test="${requestScope.dynamic != 'true'}">
				<c:forEach items="${requestScope.accountGroups}" var="group">
					<c:if test="${group.value != requestScope.selectedAccountGroup}">
						<option value="${group.value}"
								selected="${requestScope.secondGridAccountGroup == group.value ? '1' : '0'}">
								${group.label}
						</option>
					</c:if>
				</c:forEach>
			</c:if>
		</select>
	</selects>
</ilmaccountgroupdetails>