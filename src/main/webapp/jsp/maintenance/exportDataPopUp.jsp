<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
 "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="Content-Script-Type" content="text/javascript">
<meta name="Content-Style-Type" content="text/css">
<title>Export data</title>

<SCRIPT LANGUAGE="JavaScript">
	function getSelectedButton(buttonGroup) {
		for ( var i = 0; i < buttonGroup.length; i++) {
			if (buttonGroup[i].checked) {
				return i;
			}
		}
		return 0;
	}
	function sendSelectedItem(form) {
		var i = getSelectedButton(form.Export);

		var myObj = window.dialogArguments;
		myObj.setTheVal(form.Export[i].value); //calls the js method in the main page.
		window.close();
	}
</SCRIPT>
</HEAD>
<BODY  style="background: #ECE9DB;overflow: hidden">
	<FORM>
		<B style="position:absolute;left:15px;font-family:Tahoma; font-size :10pt">Pages to export :</B><br>

	<div  style="position:absolute;left:50px;top:30px;width:100%;height:60%;overflow: auto;" >

		<INPUT TYPE="radio" NAME="Export" VALUE="1" CHECKED /><label style="font-family:Tahoma; font-size :10pt;">Current Page</label><br>
		<INPUT TYPE="radio" NAME="Export" VALUE="2" /><label style="font-family:Tahoma; font-size :10pt;">All Pages</label><br>

	</div>	
	<div id='divYesNo' style="position:absolute;width:100%;top:70%;">	
		<table width="100%" border="0" cellspacing="0" cellspacing="0">
			<tr style="width: 100%;">
				<td width="50%"><input type="button" value="OK" name="btnYes"
					class="bolButton"
					style="width: 60px; height: 21px; font-family: Tahoma; font-size: 8pt; float: right; margin-right: 20px;"
					onclick="sendSelectedItem(this.form)"></td>
				<td width="50%"><input type="button" value="Cancel"
					name="btnNo" class="bolButton"
					style="width: 60px; height: 21px; font-family: Tahoma; float: left; margin-left: 10px; font-size: 8pt"
					onclick="window.close();"></td>
			</tr>
		</table>
	</div>

	</FORM>
</BODY>
</HTML>