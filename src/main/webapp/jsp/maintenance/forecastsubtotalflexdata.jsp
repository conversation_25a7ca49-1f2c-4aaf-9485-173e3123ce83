<?xml version="1.0" encoding="utf-8"?>
<!--
- The main purpose of this jsp file is to load the Forecast Monitor Template screen.
-	And to embed the Sub Total column for Forecast Monitor Template details flex screen.
- Also, to load the label values for this screen.
-
- Author(s): Vivekanandan A
- Date: 25-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<forecastmonitor columnId="${requestScope.forecastMonitorTemplateColSrc.id.columnId}"
				 ordinalPos="${requestScope.ordinalPos}" displayName="${requestScope.shortName}" description="${requestScope.description}" totalMultiplier="${requestScope.totalMultiplier}">
	<request_reply>
		<status_ok>true</status_ok>
		<message>Data fetch ok</message>
		<location/>
	</request_reply>
	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key="label.forecasttemplate.column.columnno"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="num" editable="false" dataelement="ordinalpos" width="96"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.displayname"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="displayname" width="223"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.description"/>" clickable="false" draggable="false" filterable="true" holiday="false" type="str" editable="false" dataelement="description" width="250"/>
				<column heading="<fmt:message key="label.forecasttemplate.column.multiplier"/>" clickable="false" draggable="false" filterable="true" holiday="false" editable="true" type="numval" dataelement="multiplier" width="165"/>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach items="${requestScope.listMonitorTemplateDetailsColSrc}" var="listMonitorTemplateDetailsColSrc">
				<row>
					<columnno negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.id.columnId}</columnno>
					<ordinalpos negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.ordinalPos}</ordinalpos>
					<displayname negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.columnDisplayName}</displayname>
					<description negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.columnDescription}</description>
					<multiplier negative="false" clickable="false">${listMonitorTemplateDetailsColSrc.multiplier}</multiplier>
					<modify negative="false" clickable="false">true</modify>
				</row>
			</c:forEach>
		</rows>
	</grid>
	<selects>
		<select id="columnType">
			<c:forEach items="${requestScope.columnTypeList}" var="columnTypeList">
				<option value="${columnTypeList.value}"
						selected="${requestScope.selectedColumnType == columnTypeList.label ? '1' : '0'}">${columnTypeList.label}</option>
			</c:forEach>
		</select>
	</selects>
</forecastmonitor>