<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
	<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
	<script type="text/javascript">
		var screenTitle = "";
		screenTitle = getMessage("attributeusagesummary.title.window", null);
		document.title = screenTitle;
		var appName = "<%=SwtUtil.appName%>";
		var testDate= "<%=SwtUtil.getSystemDateString() %>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		var mainWindow= null;
		requestURL=requestURL.substring(0,idy+1) ;
		var screenRoute = "AttributeUsage";
			window.onload = function () {
				setTitleSuffix(document.forms[0]);			
				setParentChildsFocus();
			};

			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
			var itemId = '${requestScope.itemId}';
			var hostId = '${requestScope.hostId}';
			var userId = '${requestScope.userId}';	
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName, attributeId, functionalGrp){
    			var param = '/' + appName + '/accountAttribute.do?method=addChangeAttributeUsageSummary';
    			param += '&calledFrom=' + methodName;
    			param += '&attributeId=' + attributeId;
    			param += '&functionalGrp=' + functionalGrp;
	    		popUpWindow = window.open(param,'attributeUsageSummary','left=30,top=345,width=670,height=245,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
				return false;
			}
			
			function help(){
				openWindow(buildPrintURL('print','Attribute Usage Summary'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			/**
			 * callBack
			 * This function used refresh data grid
			 */
			function refreshGridData(){
				// document.getElementById("attributeusagesummary").refreshdetails();
			}
			
			
			
			/**
			* closeChild
			* This function used to close the child window
			*/
			function closeChild(){
				window.close();	
			}
			
			function checkExistingAccountAttributeDefintionList() {
				var appName = "<%=SwtUtil.appName%>"; 		
				// Get domestic currency code
				//var currencyCode = document.forms[0].elements["entity.domesticCurrency"].value;
				//get the requestURL
				var requestURL = new String('<%=request.getRequestURL()%>');
				// get index of application name
				var idy = requestURL.indexOf('/'+appName+'/');
				requestURL = requestURL.substring(0, idy + 1);
				requestURL = requestURL + appName+"/accountAttribute.do?method=checkExistingAccountAttributeDefintionList";	
				var oXMLHTTP =  new XMLHttpRequest();  
				oXMLHTTP.open( "POST", requestURL, false );
				//send the request for list
				oXMLHTTP.send();
				var res = new String(oXMLHTTP.responseText);
				return res == "N" ? false : true;
			}
		
</script>
<%@ include file="/angularscripts.jsp"%>
	<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData" /> <input
			type="hidden" name="screen" id="exportDataScreen"
			value="<fmt:message key="attributeusagesummary.title.window"/>" />
	</form>
	<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
