<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="partymaintenance.addAliasScreen"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
 
</head>
<SCRIPT language="JAVASCRIPT">
<c:if test="${'yes' == requestScope.parentFormRefresh}">
window.opener.document.forms[0].method.value="displayAliasDetails";
window.opener.document.forms[0].selectedEntityId.value = "${requestScope.selectedEntityId}";
window.opener.document.forms[0].selectedPartyId.value = "${requestScope.selectedPartyId}";
window.opener.document.forms[0].entityDesc.value = "${requestScope.entityDesc}";
window.opener.document.forms[0].partyName.value = "${requestScope.partyName}";
window.opener.document.forms[0].parentRefreshRequired.value = "${requestScope.parentRefreshRequired}";
window.opener.document.forms[0].submit();
self.close();
</c:if>

mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";

function submitForm(methodName){
   if(validateForm(document.forms[0]) ){
     enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].entityDesc.value = "${requestScope.entityDescription}";
	
	
	document.forms[0].partyName.value = "${requestScope.partyDescription}";
	document.forms[0].submit();
	}
}
function enableFields(){
	document.forms[0].elements["party.id.partyId"].disabled = "";
	document.forms[0].elements["party.id.entityId"].disabled = "";	
		
	
}	

function validateForm(objForm){
  var elementsRef = new Array(1); 
  elementsRef[0] = objForm.elements["party.partyAlias"];  
  return validate(elementsRef);
}

//this method is used to Remove all the special characters 
function formatPartyAlias(element) {
var str=element.value;
//Replacing special characters
str=str.replace(/[^a-zA-Z 0-9]+/g,'');
//Replacing Space
element.value=str.split(' ').join('');
//Changing the value in to upper case
element.value = element.value.toUpperCase();
}

	
</SCRIPT>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"onunload="call()">

<form action="party.do" onsubmit="return validate(this);" method="post">

<input name="method" type="hidden" value="save">
<input name="entityDesc" type="hidden" value="">
<input name="partyName" type="hidden" value="">
<div id="AliasAdd" style="position:absolute; left:20px; top:20px; width:800px; height:88px; border:2px outset;" color="#7E97AF">
<div id="AliasAdd" style="position:absolute;left:8px; top:4px; width:470px; height:100px;">

 
	<table width="770" border="0" cellpadding="0" cellspacing="0" height="80">        
         <tr height="24">
			 <td width="42" align="left"><b><fmt:message key="party.entityId"/></b></td>
			  <td width="28">&nbsp;</td>
			   <!-- start:modified the mantis 1549 by the sunil.The purpose meaningful to display tooltip -->
			  <td width="350" align="left">
				  <input type="text" name="party.id.entityId" style="width:120px;" title="<fmt:message key='tooltip.entityId'/>" class="htmlTextAlpha" disabled value="${party.id.entityId}" />&nbsp;&nbsp;&nbsp;&nbsp;<input value="${entityDescription}" name="entityDescription" readonly="readonly"  style="background:transparent; border:thin;width:190px;" />
			  </td>
		  </tr>
		  <tr height="24">		  
		    <td width="42" align="left"><b><fmt:message key="party.partyId"/></b></td>
			 <td width="28">&nbsp;</td>
			 <td title='<fmt:message key="tooltip.enterPartyId"/>' width="350">
				 <input type="text" tabindex="1" name="party.id.partyId" style="width:120px;" title="<fmt:message key='tooltip.partyId'/>" class="htmlTextAlpha" disabled value="${party.id.partyId}" />&nbsp;&nbsp;&nbsp;&nbsp;<input value="${partyDescription}" name="partyDescription" readonly="readonly"  style="background:transparent; border:thin;width:300px;" />
		      </td>	       	  
	    </tr>

		 <tr height="24">
		    <td width="42" align="left"><b><fmt:message key="party.partyAlias"/></b>*</td>
			 <td width="28">&nbsp;</td>
			 <td title='<fmt:message key="tooltip.enterPartyAlias"/>' width="700">
				 <input type="text" tabindex="1" name="party.partyAlias" style="width:700px;" title="<fmt:message key='tooltip.enterPartyAlias'/>" class="htmlTextAlpha" maxlength="70" onblur="formatPartyAlias(this)" value="${party.partyAlias}" />
		      </td>	       	  
	    </tr>

	</table>
	   <!-- End:modified the mantis 1549 by the sunil.The purpose meaningful to display tooltip -->	
</div>
</div>

<div id="PartyMainadd" style="position:absolute ;left:745; top:120px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

			<td align="Right">
			   <a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Add PartyAlias'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
		    </td>
			<td align="right" id="Print">
				<a tabindex="9" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div> 

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:112; width:800px; height:39px; visibility:visible;">
<div id="AliasMaintenance" style="position:absolute; left:6; top:4; width:525px; height:15px; visibility:visible;">
<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>	 
		<td width="70"><a tabindex="7" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('saveAliasRecord');"><fmt:message key="button.save"/></a></td>
  	                  
		<td id ="cancelbutton" width="70"><a tabindex="8" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a></td>
	</tr>
	  </table>
</div>
</div>
	  <blockquote>&nbsp;</blockquote>
            <p>&nbsp;</p>
</form>
</body>
</html>
 
