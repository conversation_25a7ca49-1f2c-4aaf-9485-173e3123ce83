<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>

<html>

<head>
<title><fmt:message key="personalCurrencyMaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

var menuAccessIdParent = getMenuAccessIdOfChildWindow("currmonitorNew.do");

function bodyOnLoad()
{
	xl = new XLSheet("personalCurrColl","table_2", ["String","String", "Number"],"221");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("personalCurrColl");
	var dropBox1 = new SwSelectBox(document.forms[0].elements["personalCurrency.id.entityId"],document.getElementById("entityName"));

	//if(menuAccessIdParent == "0"){
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	/*}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}*/
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	var yourstate=window.confirm('<fmt:message key="confirm.remove"/>');
		if (yourstate==true){
		document.forms[0].submit();
		}
}

function buildAddPersonalCurrency(methodName){

	var param = 'personalcurrency.do?method='+methodName;
	return  param;
}

function buildChangePersonalCurrency(methodName){

	var curID = document.forms[0].elements["selectedPersonalCurrencyId"].value.trim();
	var curName = document.forms[0].elements["selectedPersonalCurrencyName"].value.trim();
	var priority = document.forms[0].elements["selectedPriorityOrder"].value.trim();
	//alert("==" + curID +"== : =="+curName+"= : =="+priority + "==");
	var param = 'personalcurrency.do?method='+methodName+'&currencyCode=';
	param +=  curID;
	param += '&currencyName=' + curName;
	param += '&priorityOrder=' + priority;
	//alert(param);
	return  param;
}


function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement0 = rowElement.cells[0].innerText;
	var hiddenElement1 = rowElement.cells[1].innerText;
	var hiddenElement2 = rowElement.cells[2].innerText;

	document.forms[0].elements["selectedPersonalCurrencyId"].value = hiddenElement0.trim();
	document.forms[0].elements["selectedPersonalCurrencyName"].value = hiddenElement1.trim();
	document.forms[0].elements["selectedPriorityOrder"].value = hiddenElement2.trim();

	var count = getCountRowsSelected(rowElement);

	/*if(menuAccessIdParent == "0" && count == 1)
	{*/
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;

	/*}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	}*/
}

</SCRIPT>
</head>

<form action="personalcurrency.do" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedPersonalCurrencyId" type="hidden" >
<input name="selectedPersonalCurrencyName" type="hidden" >
<input name="selectedPriorityOrder" type="hidden" >

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->
<input name="menuAccessId" type="hidden" >
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">

<div id="PersonalCurrency" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:20px; width:513px; height:210px;">
	<div id="PersonalCurrency" style="position:absolute;z-index:50;left:0px; top:0px; width:490px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="490px" border="0" cellspacing="1" cellpadding="0"  height="23px">
	<thead>
		<tr height="20px">
		    <td  title ='<fmt:message key="tooltip.personalCurrency"/>' width="100px"><b><fmt:message key="personalCurrency.personalCurrency"/></b></td>
			<td title ='<fmt:message key="tooltip.personalCurrencyName"/>' width="250px"><b><fmt:message key="personalCurrency.personalCurrencyName"/></b></td>
			<td title ='<fmt:message key="tooltip.priority"/>' width="130px"><b><fmt:message key="personalCurrency.priority"/></b></td>


		</tr>
	</thead>
</table>
 </div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:508px; height:205px; overflowY:scroll;">
	<div id="PersonalCurrency" style="position:absolute;z-index:99;left:0px; top:22px; width:490px; height:10px;">
		<table class="sort-table" id="personalCurrColl" width="490px" border="0" cellspacing="1" cellpadding="0" height="183">
	<tbody>
    <c:set var="count" value="0"/>
    <c:forEach items="${requestScope.personalCurrColl}" var="currency">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <td align="left" width="100px">
                <input type="hidden" name="currencyCode" value="${currency.id.currencyCode}"/>
                ${currency.id.currencyCode}&nbsp;
            </td>
            <td width="250px" align="left">
                <input type="hidden" name="currencyName" value="${currency.currencyName}"/>
                ${currency.currencyName}&nbsp;
            </td>
            <td width="130px" align="left">
                ${currency.priorityOrder}&nbsp;
            </td>
        </tr>
        <c:set var="count" value="${count + 1}"/>
    </c:forEach>
</tbody>
	<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

</div>
<div id="Currency" style="position:absolute; left:456; top:250; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		    <td align="Right">
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Personal Currency'),'personalcurrencywindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
            </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:240; width:513px; height:39px; visibility:visible;">
  <div id="Currency" style="position:absolute; left:6; top:4; width:513; height:15px; visibility:visible;">
  	  <table width="250" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"> </td>
		<td id="changebutton" width="70" ></td>
		<td id="deletebutton" width="70" ></td>

		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:513; height:15px; visibility:hidden;">
    <table width="250" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton" >
		<%-- Start : Vivekanandan :12/11/2008: Modified
			 Modified width to display currency name elaborately with out fluctuation --%>
		<a tabindex="2" title='<fmt:message key="tooltip.AddNewPersonalCurrency"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddPersonalCurrency('add'),'currencygroupmaintenancechildWindow','left=50,top=190,width=430,height=155,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
		<%-- End : Vivekanandan :12/11/2008: Modified
			 Modified width to display currency name elaborately with out fluctuation --%>
		</td>
		<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
		</td>

		<td id="changeenablebutton" >
			<%-- Start : Vivekanandan :12/11/2008: Modified
				 Modified width to display currency name elaborately with out fluctuation --%>
			<a  tabindex="3" title='<fmt:message key="tooltip.ChangePersonalCurrency"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangePersonalCurrency('change'),'currencygroupmaintenancechildWindow','left=50,top=190,width=430,height=155,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
			<%-- End : Vivekanandan :12/11/2008: Modified
				 Modified width to display currency name elaborately with out fluctuation --%>
		</td>
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
		</td>

		<td id="deleteenablebutton"  >
			<a  tabindex="4" title='<fmt:message key="tooltip.RemovePersonalCurrency"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('remove')"><fmt:message key="button.remove"/></a>
		</td>
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.remove"/></a>
		</td>

	</tr>
    </table>
  </div>
</div>

</form>
</body>
</html>