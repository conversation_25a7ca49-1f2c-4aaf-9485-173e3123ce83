<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>

<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>
    <c:choose>
        <c:when test="${requestScope.methodName != 'changeGeneralPrams'}">
            <fmt:message key="ilaapgeneralsysparam.mainScreen" />
        </c:when>
        <c:otherwise>
            <fmt:message key="ilaapgeneralsysparam.changescreen" />
        </c:otherwise>
    </c:choose>
</title>

<script language="JAVASCRIPT">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var cancelcloseElements = new Array(2);

cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";
function bodyOnLoad(){



	document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;

	<%
	String saveButSts = (String) request.getAttribute(SwtConstants.SAV_BUT_STS);
	if (saveButSts != null && saveButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;<%
	} else {
	%>
	document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	<%
	}
	%>

	<%
	String chgButSts = (String) request.getAttribute(SwtConstants.CHG_BUT_STS);
	if (chgButSts != null && chgButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%
	} else {
	%>
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%
	}
	%>

	<%
	String canButSts = (String) request.getAttribute(SwtConstants.CAN_BUT_STS);
	if (canButSts != null && canButSts.equals(SwtConstants.STR_TRUE)) {
	%>
	document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
	<%
	} else {
	%>
	document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
	<%
	}
	%>

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	<c:if test="${requestScope.methodName != 'changeGeneralPrams'}">

	if(menuEntityCurrGrpAccess == 0){
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	}


</c:if>

}

/**
 * This method is used to submit form while clicking on the save button.
 *
 * @Param methodName
 */
function submitForm(methodName){
	if( methodName == "displayGeneralPrams"){
		document.forms[0].method.value = methodName;
	    document.forms[0].submit();
	} else{
	//validation for datasetUpdateInterval field
	    var datasetUpdateInterval = validateField(document.forms[0].elements['ilmParams.datasetUpdateInterval'],'Number 2','numberPat');
		if(datasetUpdateInterval){
			//set datasetUpdateInterval as default value (10 min) when the datasetUpdateInterval field is empty
			if(document.forms[0].elements['ilmParams.datasetUpdateInterval'].value == "")
				 document.forms[0].elements['ilmParams.datasetUpdateInterval'].value = 10;

			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}
	}
}


function submitCancel(methodName){

		<%
		if (canButSts != null && canButSts.equals(SwtConstants.STR_TRUE)) {
		%>
	     var yourstate=window.confirm('<fmt:message key="general.closeConfirm"/>');
		    if (yourstate==true){
		    	window.focus();
	            window.close();
			}	<%
		} else {
		%>
	    document.forms[0].method.value = methodName;
	    window.close();	<%
		}
		%>

}

</script>
</head>

<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad=" setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad();"
	onunload="call();">
	<form action="intraDayLiquidity.do" method="post">
		<input name="method" type="hidden" value="list">
		<input name="menuAccessId" type="hidden">
		<bean:define id="CDM" name="CDM"
			type="org.swallow.util.CommonDataManager" scope="session" />
		<div id="IlaapGeneralParameter"
			style="position: absolute; left: 20px; top: 20px; width: 450px; height: 140px; border: 2px outset;"
			color="#7E97AF">
			<div id="IlaapParameter"
				style="position: absolute; left: 6px; top: 5px; width: 440px; height: 5px;">
			<div style ="height: 120px;" >
				<fieldset
					style="width: 430px; border: 2px groove;  margin-left: 5px;">
					<legend>
						<fmt:message key="ilaapgeneral.generalSettings"/>
					</legend>
					<table width="420px" height="100px" ; border="0" cellspacing="0"
						cellpadding="0">
						<tr height="30px">
							<td width="145px"><b>&nbsp;&nbsp;<fmt:message key="timeslotSize"/></b></td>
							<td width="28px">&nbsp;</td>
							<td width="250px">

							<c:choose>
							<c:when  test="${requestScope.screenFieldsStatus == 'true'}">

							<select name="ilmParams.timeslotSize"
									disabled="true" tabindex="1" onchange=""
									titleKey="tooltip.timeslotSize" class="htmlTextAlpha"
									style="width: 60px">
										<c:forEach var="item" items="${requestScope.timeslotList}">

											<option value="${item.value}"
												<c:if test="${ilmParams.timeslotSize == item.value}">selected="selected"</c:if>>
												${item.label}</option>
										</c:forEach>

								</select>

							</c:when>
							<c:otherwise>
							<select name="ilmParams.timeslotSize"
									tabindex="1" onchange=""
									titleKey="tooltip.timeslotSize" class="htmlTextAlpha"
									style="width: 60px">
										<c:forEach var="item" items="${requestScope.timeslotList}">

											<option value="${item.value}"
												<c:if test="${ilmParams.timeslotSize == item.value}">selected="selected"</c:if>>
												${item.label}</option>
										</c:forEach>

							</select>
							 </c:otherwise>
    						</c:choose>
							</td>

						</tr>

						<tr height="30px">
							<td width="140px"><b>&nbsp;&nbsp;<fmt:message key="datasetUpdateInterval"/></b></td>
							<td width="28px">&nbsp;</td>
							<td width="250px">

							<c:choose>
        					<c:when test="${requestScope.screenFieldsStatus == 'true'}">

							<input type="text" tabindex="2"
									titleKey="tooltip.datasetUpdateInterval"

									cssClass="htmlTextNumeric" name="ilmParams.datasetUpdateInterval" value="${ilmParams.datasetUpdateInterval}"
									style="width:60px; height: 21px; text-align:left"
									onchange="return validateField(this,'Number','numberPat')"
									maxlength="2" disabled="true" />

									 </c:when>
       								 <c:otherwise>
									<input type="text" tabindex="2"
									titleKey="tooltip.datasetUpdateInterval"

									cssClass="htmlTextNumeric" name="ilmParams.datasetUpdateInterval" value="${ilmParams.datasetUpdateInterval}"
									style="width:60px; height: 21px; text-align:left"
									onchange="return validateField(this,'Number','numberPat')"
									maxlength="2" />
									</c:otherwise>
    								</c:choose>

							</td>
						</tr>

						<tr height="30px">
							<td width="145px"><b>&nbsp;&nbsp;<fmt:message key="systemLoggingLevel"/></b></td>
							<td width="28px">&nbsp;</td>
							<td width="250px">

						    <c:choose>
        					<c:when test="${requestScope.screenFieldsStatus == 'true'}">

							<input type="radio" name="ilmParams.systemLoggingLevel" ${ilmParams.systemLoggingLevel == 'E' ? 'checked="checked"' : ''} value="${'E'}" id="4" tabindex="3"
									style="width:13px;" titleKey="ilaapgeneral.entity"

									disabled="true" /> <label
								style="width: 80px;"
								title='<fmt:message key="ilaapgeneral.entity"/>' for="4"><fmt:message key="ilaapgeneral.entity"/></label>&nbsp;<input type="radio" name="ilmParams.systemLoggingLevel" ${ilmParams.systemLoggingLevel == 'C' ? 'checked="checked"' : ''} value="${'C'}" id="5"
									style="width:13px;" tabindex="4"
									titleKey="ilaapgeneral.currency"

									disabled="true" /> <label
								style="width: 80px;"
								title='<fmt:message key="ilaapgeneral.currency"/>' for="5"><fmt:message key="ilaapgeneral.currency"/></label>&nbsp;

							    </c:when>
        						<c:otherwise>
								<input type="radio" name="ilmParams.systemLoggingLevel" ${ilmParams.systemLoggingLevel == 'E' ? 'checked="checked"' : ''} value="${'E'}" id="4" tabindex="3"
									style="width:13px;" titleKey="ilaapgeneral.entity"   /> <label

								style="width: 80px;"
								title='<fmt:message key="ilaapgeneral.entity"/>' for="4"><fmt:message key="ilaapgeneral.entity"/></label>&nbsp;<input type="radio" name="ilmParams.systemLoggingLevel" ${ilmParams.systemLoggingLevel == 'C' ? 'checked="checked"' : ''} value="${'C'}" id="5"
									style="width:13px;" tabindex="4"
									titleKey="ilaapgeneral.currency"   /> <label

								style="width: 80px;"
								title='<fmt:message key="ilaapgeneral.currency"/>' for="5"><fmt:message key="ilaapgeneral.currency"/></label>&nbsp;

								</c:otherwise>
    							</c:choose>


								</td>

						</tr>
					</table>

				</fieldset>
			</div>
			</div>
		</div>

		<div id="IlaapParameterHelp"
			style="position: absolute; left: 400; top: 173px; width: 70px; height: 30px; visibility: visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td align="Right"><a tabindex="11" href=#
						onclick="javascript:openWindow(buildPrintURL('print','ILM General Parameters'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
							src="images/help_default.GIF " name="Help" border="0"
							title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

					<td align="right" id="Print"><a tabindex="12" tabindex="6"
						onclick="printPage();" onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							title='<fmt:message key="tooltip.printScreen"/>'></a></td>
				</tr>
			</table>
		</div>

		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 20; top: 165; width: 450px; height: 39px; visibility: visible;">
			<div id="GeneralParameter"
				style="position: absolute; left: 6; top: 4; width: 290; height: 15px; visibility: visible;">
				<table width="300" border="0" cellspacing="0" cellpadding="0"
					height="20">

					<tr>
						<td id="changebutton" width="70"></td>
						<td id="savebutton" width="70"></td>
						<td id="cancelbutton" width="70"></td>

						<td id="closebutton" width="70"><a tabindex="10"
							title='<fmt:message key="tooltip.close"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitCancel('close')"><fmt:message key="button.close"/></a></td>
					</tr>

				</table>
			</div>

			<!--End: Code modified by sudhakar on 17-01-2012:1687: Parameterise retention time for suppressed && rejected messages -->
			<div
				style="position: absolute; left: 6; top: 4; width: 403; height: 15px; visibility: hidden;">
				<table width="280px" border="1" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden; display: none;">
					<tr>
						<td id="changeenablebutton"><a tabindex="6"
							title='<fmt:message key="tooltip.changeIlmParam"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitForm('changeGeneralPrams')"><fmt:message key="button.change"/></a></td>
						<td id="changedisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.change"/></a></td>


						<td id="saveenablebutton"><a tabindex="7"
							title='<fmt:message key="tooltip.SaveChanges"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="javascript:submitForm('updateGeneralPrams');"><fmt:message key="button.save"/></a></td>
						<td id="savedisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.save"/></a></td>
						<td id="cancelenablebutton"><a tabindex="8"
							title='<fmt:message key="tooltip.CancelChanges"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onClick="javascript:submitForm('displayGeneralPrams')"><fmt:message key="button.cancel"/></a></td>
						<td id="canceldisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.cancel"/></a></td>

					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

	</form>
</body>
</html>