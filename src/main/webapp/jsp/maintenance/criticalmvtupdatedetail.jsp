<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%
	//variable declaration
	String screenName = "";
	screenName = request.getAttribute("screenName").toString();


%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>	<s:text name="criticalMvtUpdateMaint.title"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "CriticalMvtUpdateDetail";
var uploadFileImage = "images/open_up.png";
var screen = '${requestScope.screen}';
var screenName = "<%= screenName %>";

 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>