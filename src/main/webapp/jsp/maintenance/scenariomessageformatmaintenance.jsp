<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.ScenarioMessageFormats"%>
<html>
<head>
<title><fmt:message key="messageFormatScenario.title.MainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
<c:if test="${'yes' == requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();

</c:if>


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var from = '${requestScope.from}';
// var scenarioId = '${requestScope.scenarioId}';

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function closeHandler(){
	if(window.opener && window.opener.getMsgFormats)
		window.opener.getMsgFormats();
	self.close();
}
function submitForm(methodName){
if(methodName != 'deleteScenario')
	{
		document.forms[0].method.value = methodName;
	    document.forms[0].submit();
	}
	else
	{
		var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
		if (yourstate==true){
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
		}
	}

}
/**
 * buildAddMessageFormat
 * To open child screen
 * @param methodName
 */
function buildAddMessageFormat(methodName){
   	var param = 'messageformats.do?method='+methodName+'&formatId=';
	param += document.forms[0].selectedFormatId.value;
	if(from != "eventsSub"){
// 		param += '&scenarioId=' + document.forms[0].elements['scenarioMessageFormats.id.scenarioId'].value;
		param += '&from='+"others";
	}else{
// 		param += '&scenarioId=' + document.forms[0].elements['scenarioId'].value;
		param += '&from='+from;
	}

	param += '&formatName=' + document.forms[0].selectedFormatName.value;
	param += '&formatType=' + document.forms[0].selectedFormatType.value;
	param += '&fldDelimiter=' + document.forms[0].selectedFieldDelimiter.value;
	param += '&hexFldDelimiter=' + document.forms[0].selectedHexFldDelimiter.value;
	param += '&msgSeparator=' + document.forms[0].selectedMsgSeparator.value;
	param += '&hexMsgSeparator=' + document.forms[0].selectedHexMsgSeparator.value;
// 	param += '&scenarioDesc=' +document.getElementById('scenarioDesc').innerText;
	return  param;
}
function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

}

function onFilterandSort(){
	updateColors();
	disableAllButtons();
}

function bodyOnLoad()
{
	xl = new XLSheet("msgFormatDetails","table_2", ["String", "String","String","String","String","String","String","String","String","String","String"],"11111111111");

	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("msgFormatDetails");

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";
// 	if(scenarioId!= null){
// 		document.forms[0].elements['scenarioId'].value = scenarioId;
// 	}

// 	var dropBox1 = new SwSelectBox(document.forms[0].elements["scenarioMessageFormats.id.scenarioId"],document.getElementById("scenarioDesc"));
	document.forms[0].from.value = from;
	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

    <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>


	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";


}
/**
 * This function enables/disables buttons based on grid record selected
 *
 * @param rowElement
 * @param isSelected
 */
function onSelectTableRow(rowElement, isSelected){
	if(rowElement && rowElement.cells.length == 7){
		var hiddenFormatId = rowElement.cells[0].getElementsByTagName("input")[0];
		var hiddenFormatName = rowElement.cells[1].getElementsByTagName("input")[0];
		var hiddenFormatType = rowElement.cells[2].getElementsByTagName("input")[0];
		var hiddenFieldDelimiter = rowElement.cells[3].getElementsByTagName("input")[0];
		var hiddenHexFldDelimiter = rowElement.cells[4].getElementsByTagName("input")[0];
		var hiddenMsgSeparator = rowElement.cells[5].getElementsByTagName("input")[0];
		var hiddenHexMsgSeparator = rowElement.cells[6].getElementsByTagName("input")[0];
	}
	document.forms[0].selectedFormatId.value = rowElement.cells[0].innerText.trim();
	document.forms[0].selectedFormatName.value = hiddenFormatName.value;
	document.forms[0].selectedFormatType.value = hiddenFormatType.value;
	document.forms[0].selectedFieldDelimiter.value = hiddenFieldDelimiter.value;
	document.forms[0].selectedHexFldDelimiter.value = hiddenHexFldDelimiter.value;
	document.forms[0].selectedMsgSeparator.value = hiddenMsgSeparator.value;
	document.forms[0].selectedHexMsgSeparator.value=hiddenHexMsgSeparator.value;
	if(from != "eventsSub"){
// 		document.forms[0].selectedScenarioId.value = document.forms[0].elements['scenarioMessageFormats.id.scenarioId'].value;
	}else{
// 		document.forms[0].selectedScenarioId.value = document.forms[0].elements['scenarioId'].value;
	}

	/*Start:code modified by sandeepkumar for mantis 2094 View button not enabled if screen having View access  */
	if(isSelected){
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else if(menuEntityCurrGrpAccess == "1" && isSelected){
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	}
	else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
	/*End:code modified by sandeepkumar for mantis 2094 View button not enabled if screen having View access */
}
</SCRIPT>

</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="messageformats.do" method="post">

<input name="method" type="hidden" value="display">
<!-- <input name="selectedScenarioId" type="hidden" value=""> -->
<input name="from" type="hidden" value="">
<input name="selectedFormatId" type="hidden" value="">
<input name="selectedFormatName" type="hidden" value="">
<input name="selectedFormatType" type="hidden" value="">
<input name="selectedFieldDelimiter" type="hidden" value="">
<input name="selectedHexFldDelimiter" type="hidden" value="">
<input name="selectedMsgSeparator" type="hidden" value="">
<input name="selectedHexMsgSeparator" type="hidden" value="">


<input name="menuAccessId" type="hidden" >


<!-- <div id="MessageFormats" style="position:absolute; left:20px; top:20px; width:960px; height:37px; border:2px outset;" color="#7E97AF"> -->
<!-- 	<div style="position:absolute; left:8px; top:4px; width:610px; height:22px;"> -->
<!-- 		<table width="848px" border="0" cellpadding="0" cellspacing="1" height="22px"> -->
<!-- 		  <tr height="25px"> -->
<%-- 			  <td width="100px"><b><fmt:message key="scenario.scenarioId"/></b></td> --%>
<!--   			  <td width="28px">&nbsp;</td> -->
<!-- 			  <td width="200px"> -->
<%-- 			  	<c:if test="${'eventsSub' == requestScope.from}">
       --%>
<!-- 				<input type="text" name="scenarioId"  style="width:200px"> -->
<!-- 				</input> -->
<%--
</c:if> --%>
<%-- 				<c:if test="${'eventsSub' != requestScope.from}">
      		   --%>
<%-- 				<s:select id="scenarioMessageFormats.id.scenarioId" name="scenarioMessageFormats.id.scenarioId" onchange="submitForm('displayScenarioFormats')" titleKey = "tooltip.selectscenarioId" tabindex="1" style="width:200px" --%>
<%-- 				 --%>
<%--
</c:if> --%>
<!-- 			  </td> -->
<!--   			  <td width="20px">&nbsp;</td> -->
<!-- 			  <td width="400px"> -->
<!-- 					<span id="scenarioDesc" name="scenarioDesc" class="spantext"> -->
<!-- 			   </td> -->
<!-- 		  </tr> -->
<!-- 		</table> -->
<!-- 	</div> -->
<!-- </div> -->
<div id="MessageFormats" color="#7E97AF" style="position:absolute; border:0px outset; left:20px; top:11px; width:960px; height:435px;">
	<div id="MessageFormats" style="position:absolute;z-index:99;left:0px; top:0px; width:944px; height:10px;">
		<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1350" border="0" cellspacing="1" cellpadding="0" height="22px">
			<thead>
				<tr height="20px">
					<td width="120px"   style="border-left-width: 0px;"title='<fmt:message key="tooltip.sortMsgId"/>'><b><fmt:message key="messageFormats.id.formatId"/></b></td>
					<td width="280" title='<fmt:message key="tooltip.sortMsgName"/>'><b><fmt:message key="messageFormats.formatName"/></b></td>
					<td width="180" title='<fmt:message key="tooltip.sortbyformattype"/>'><b><fmt:message key="messageFormats.formatType1"/></b></td>
					<td width="105" title='<fmt:message key="tooltip.sortAuthFlag"/>'><b><fmt:message key="messageFormats.authorizeFlag"/></b></td>
					<td width="105" title='<fmt:message key="tooltip.sortOutputType"/>'><b><fmt:message key="messageFormats.outputType1"/></b></td>
					<td width="280" title='<fmt:message key="tooltip.sortFileName"/>'><b><fmt:message key="messageFormats.fileName"/></b></td>
					<td width="280" title='<fmt:message key="tooltip.sortPath"/>'><b><fmt:message key="messageFormats.path"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:960px; height:430px;overflow:scroll">
	<div id="MessageFormats" style="position:absolute;z-index:99;left:0px; top:24px; width:960px; height:10px;">
	<table class="sort-table" id="msgFormatDetails" width="1350" border="0" cellspacing="1" cellpadding="0" height="339">
		<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="msgFormatDetails" items="${requestScope.msgFormatDetails}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <td width="120px">
                <input type="hidden" name="formatId" value="${msgFormatDetails.id.formatId}" disabled="true" />
                ${msgFormatDetails.id.formatId}&nbsp;
            </td>
            <td width="280px">
                <input type="hidden" name="formatName" value="${msgFormatDetails.formatName}" disabled="true" />
                ${fn:escapeXml(msgFormatDetails.formatName) != null ? fn:escapeXml(msgFormatDetails.formatName).replace(' ', '&nbsp;') : ''}&nbsp;
            </td>
            <td width="180px">
                <input type="hidden" name="formatTypeDisplay" value="${msgFormatDetails.formatTypeDisplay}" disabled="true" />
                ${msgFormatDetails.formatTypeDisplay}&nbsp;
            </td>
            <td width="105px" align="center">
                <input type="hidden" name="authorizeFlag" value="${msgFormatDetails.authorizeFlag}" disabled="true" />
                ${msgFormatDetails.authorizeFlag}&nbsp;
            </td>
            <input type="hidden" name="fieldDelimeter" value="${msgFormatDetails.fieldDelimeter}" disabled="true" />
            <input type="hidden" name="hexaFldDelimeter" value="${msgFormatDetails.hexaFldDelimeter}" disabled="true" />
            <input type="hidden" name="msgSeparator" value="${msgFormatDetails.msgSeparator}" disabled="true" />
            <input type="hidden" name="hexaMsgSeparator" value="${msgFormatDetails.hexaMsgSeparator}" disabled="true" />
            <td width="105px">
                <input type="hidden" name="outputTypeDisplay" value="${msgFormatDetails.outputTypeDisplay}" disabled="true" />
                ${msgFormatDetails.outputTypeDisplay}&nbsp;
            </td>
            <td width="280px">
                <input type="hidden" name="fileName" value="${msgFormatDetails.fileName}" disabled="true" />
                ${msgFormatDetails.fileName}&nbsp;
            </td>
            <td width="280px">
                <input type="hidden" name="path" value="${msgFormatDetails.path}" disabled="true" />
                ${msgFormatDetails.path}&nbsp;
            </td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>
<tfoot><tr><td colspan="7" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="MessageFormats" style="position:absolute; left:895; top:459; width:80px; height:15px; visibility:visible;">
	<table width="70px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				<a tabindex="5" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Message Format'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</td>
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:450; width:960px; height:39px; visibility:visible;">
  <div id="MessageFormats" style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:visible;">
  <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td width="70px" id="addbutton" title='<fmt:message key="tooltip.addnewmsgformat"/>'>
			</td>
			<td width="70px" id="changebutton" title='<fmt:message key="tooltip.changeselectedmsgformat"/>'>
			</td>
			<td width="70px" id="viewbutton" title='<fmt:message key="tooltip.viesselectedmsgformat"/>'>
			</td>
			<td width="70px" id="deletebutton" title='<fmt:message key="tooltip.deleteselectedmsgformat"/>'>
			</td>
			<td width="70px" id="closebutton" title='<fmt:message key="tooltip.close"/>'>
				<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:closeHandler();"><fmt:message key="button.close"/></a>
			</td>
		</tr>
		</table>
	</div>
<div style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:hidden;">
    <table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton">
		<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageFormat('addScenarioFormat'),'messageformataddwindow','left=50,top=190,width=690,height=490,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
		</td>
		<td id="adddisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
		</td>
		<td id="changeenablebutton">
			<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageFormat('changeScenarioFormat'),'messageformatchangewindow','left=50,top=190,width=690,height=490,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
		</td>
		<td id="changedisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.change"/></a>
		</td>
		<td id="viewenablebutton">
			<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMessageFormat('viewScenarioFormat'),'messageformatviewwindow','left=50,top=190,width=690,height=490,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
		</td>
		<td id="viewdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.view"/></a>
		</td>
		<td id="deleteenablebutton">
			<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('deleteScenario')"><fmt:message key="button.delete"/></a>
		</td>
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>
	</tr>
    </table>
  </div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>

    
  