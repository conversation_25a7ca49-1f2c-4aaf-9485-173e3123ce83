<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>




<accountattribute >
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	  </request_reply>
	
	  <grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key="label.accountattribute.attribute"/>" clickable="true" draggable="false" filterable="true" type="str" dataelement="attribute" width="156"/>
				<column heading="<fmt:message key="label.accountattribute.effectivedateTime"/>" clickable="true" draggable="false"   type="date" dataelement="effectiveDate" width="165"/>
				<column heading="<fmt:message key="label.accountattribute.value"/>" clickable="true" draggable="false" type="num" dataelement="value" width="160"/>
				<column heading="<fmt:message key="label.accountattributedefinition.column.updatedTime"/>" clickable="true" draggable="false"  type="date" dataelement="updateDate" width="165"/>
				<column heading="<fmt:message key="label.accountattributedefinition.column.user"/>" clickable="true" draggable="fil false" type="str" dataelement="updateuser" width="130"/>
			</columns>
		</metadata>
		  <rows size="${recordCount}">
			  <c:forEach var="lastValuesAttributes" items="${requestScope.lastValuesAttributesList}">
				  <row>
					  <attributeid>${lastValuesAttributes.attributeId}</attributeid>
					  <attribute clickable="true">${lastValuesAttributes.attributeName}</attribute>
					  <effectiveDate clickable="true">${lastValuesAttributes.effectiveDateAsString}</effectiveDate>
					  <value clickable="true">${lastValuesAttributes.valueAsString}</value>
					  <updateDate clickable="true">${lastValuesAttributes.updateDateAsString}</updateDate>
					  <updateuser clickable="true">${lastValuesAttributes.updateUser }</updateuser>
					  <sequenceKey clickable="true">${lastValuesAttributes.attributeId}</sequenceKey>
					  <type clickable="true">${lastValuesAttributes.typeAsString}</type>
				  </row>
			  </c:forEach>
		  </rows>





	</grid>	
</accountattribute>
