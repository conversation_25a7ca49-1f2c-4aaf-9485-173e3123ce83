<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>
		<c:if test="${requestScope.methodName == 'addTransactionDetail'}">
		<fmt:message key="ilmtransSetDetailAdd.title.window.addScreen" />
	</c:if> <c:if test="${requestScope.methodName == 'changeTransactionDetail'}">
		<fmt:message key="ilmtransSetDetailAdd.title.window.changeScreen" />
	</c:if></title>

<script language="JAVASCRIPT">
var currencyFormat = '${sessionScope.CDM.currencyFormat}';
mandatoryFieldsArray=["ilmTransactionSetDTLTime","ilmTransactionSetDTLAccountId"];
	/**
	 * Called the parent screen to insert a change in grid
	 */
	function goBackToParentWind() {
		if (document.forms[0].elements["ilmTransactionSetDTL.id.time"].value == ""
				      || document.forms[0].elements["ilmTransactionSetDTL.id.accountId"].value == "") {
			alert("<fmt:message key='alert.pleaseFillAllMandatoryFields'/>");
			return;
		}
		if(!validateField(document.forms[0].elements["ilmTransactionSetDTL.description"],'description','ascii_standard'))
			return;
		var isvalidTime = validateField(document.forms[0]
				.elements["ilmTransactionSetDTL.id.time"], 'transactionTime',
				'timePat');
		if (isvalidTime) {
			var accountId = document.forms[0]
					.elements["ilmTransactionSetDTL.id.accountId"].value;
			var time = document.forms[0]
					.elements["ilmTransactionSetDTL.id.time"].value;
			var credits = document.forms[0]
					.elements["ilmTransactionSetDTL.creditsAsString"].value;
			var debits = document.forms[0]
					.elements["ilmTransactionSetDTL.debitsAsString"].value;
			var description = document.forms[0]
					.elements["ilmTransactionSetDTL.description"].value;

			<c:choose>
			<c:when test="${requestScope.methodName == 'addTransactionDetail'}">
			if (testDataIntegrityViolation(this.opener.xl.dataTable.tBody.rows,
					accountId, time) == -1)
				var operation = "add";
			else {
				alert('<fmt:message key="alert.recordAlreadyExists"/>');
				return;
			}

			</c:when>
			<c:otherwise>
			operation = "change";
			</c:otherwise>
			</c:choose>

			this.opener.createNewTransactionObj();
			this.opener.transactionObject.init(accountId, time, credits,
					debits, description, operation);
			this.opener.updateGrid();

			self.close();
		}
	}

/**
 * Called on first load of the screen
 */
function bodyOnLoad() {

	document.getElementById("okbutton").innerHTML = document
				.getElementById("okenablebutton").innerHTML;

		var accountDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["ilmTransactionSetDTL.id.accountId"], document
				.getElementById("accountName"));
		<c:if test="${requestScope.methodName != 'addTransactionDetail'}">
		document.getElementById("accountName").innerText = '${accoutNameText}';
		</c:if>
	}

	/**
	 * Used to validate Credit && Debit field values
	 */
	function validateDebitCredit(strFiled) {
		var res = validateCurrency(strFiled,'Credit_Debit',currencyFormat, '${currencyCode}');

		if (!res)
			strFiled.value = "";
		return res;
	}
	/**
	 * Used to check data integrity
	 */
	function testDataIntegrityViolation(grid, accountId, time) {

		var l = grid.length;
		for ( var i = 0; i < l; i++) {
			if (grid[i].cells[0].innerText.trim() == accountId.trim()
					&& grid[i].cells[1].innerText.trim() == time.trim())
				return i;

		}
		return -1;
	}
</script>


</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onunload="call()">
	<form action="ilmTransScenario.do">
		<input name="method" type="hidden" value="">
		<input name="entityId" type="hidden" value="">
		<input name="currencyCode" type="hidden" value="">

		<div id="ilmTransactionSetDetail"
			style="position: absolute; left: 20px; top: 20px; width: 600px; height: 170px; border: 2px outset;"
			color="#7E97AF">
			<div id="ilmTransactionSetDetail"
				style="position: absolute; left: 0px; top: 15px; width: 590px; height: 150;">
				<table width="580px" border="0" cellpadding="0" cellspacing="0"
					height="130px" class="content">


					<tr height="28">
						<td width="120" style="padding-left: 10px;"><b
							style="width: 100px"><fmt:message key="ilmTransactionSet.accountId" />
							<c:if test="${requestScope.methodName == 'addTransactionDetail'}">*</c:if></b></td>
						<td width="28">&nbsp;</td>
						<c:if test="${requestScope.methodName == 'addTransactionDetail'}">
							<td width="160" id="ilmTransactionSetDTLAccountId"><div style="display: inline;width:150px; height: 22px">
							<select
									name="ilmTransactionSetDTL.id.accountId"
									titleKey="tooltip.accountId" style="width:150px;" tabindex="1">
							<c:forEach var="item" items="${requestScope.accountList}">
       						 <option value="${item.value}" <c:if test="${ilmTransactionSetDTL.id.accountId == item.value}">selected</c:if>>${item.label}</option>
    						</c:forEach>
							</select></div></td>
						</c:if>
						<c:if test="${requestScope.methodName == 'changeTransactionDetail'}">

							<td width="160" ><input type="text"  name="ilmTransactionSetDTL.id.accountId" value="${ilmTransactionSetDTL.id.accountId}"
									titleKey="tooltip.accountId" style="width:150px;" tabindex="1"
									<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>/></td>
						</c:if>
						<td width="220"><span id="accountName" class="spantext"></td>
					</tr>
					<tr height="28">
						<td width="120px" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.time" />
						<c:if test="${requestScope.methodName == 'addTransactionDetail'}">*</c:if></b></td>
						<td width="5px">&nbsp;</td>
						<td width="125px" id="ilmTransactionSetDTLTime"><input type="text" cssClass="htmlTextNumeric" tabindex="2"
								maxlength="5" style="width:60px ;height :21px;"
								titleKey="tooltip.time" name="ilmTransactionSetDTL.id.time" value="${ilmTransactionSetDTL.id.time}"
								<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
								onchange="return validateField(this,'transactionTime','timePat');" />
						</td>
					</tr>
					<tr height="28">
						<td width="120px" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.credits" /></b></td>
						<td width="5px">&nbsp;</td>
						<td width="125px"><input type="text" cssClass="htmlTextNumeric"

								style="width:100px ;height :21px;" name="ilmTransactionSetDTL.creditsAsString" value="${ilmTransactionSetDTL.creditsAsString}"
								titleKey="tooltip.credits" tabindex="3"
								onchange="return validateDebitCredit(this);" /></td>
					</tr>
					<tr height="28">
						<td width="120px" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.debits" /></b></td>
						<td width="5px">&nbsp;</td>
						<td width="125px"><input type="text" cssClass="htmlTextNumeric"

								style="width:100px;height :21px;" tabindex="4" name="ilmTransactionSetDTL.debitsAsString" value="${ilmTransactionSetDTL.debitsAsString}"  titleKey="tooltip.debits"
								onchange="return validateDebitCredit(this);" /></td>

					</tr>
					<tr height="28">
						<td width="120px" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.description" /></b></td>
						<td width="5px">&nbsp;</td>
						<td width="125px" colspan="2"><input type="text" maxlength="50"
								style="width:400px;height :21px;" tabindex="5" name="ilmTransactionSetDTL.description" value="${ilmTransactionSetDTL.description}"

								onchange="return validateField(this,'description','ascii_standard');"
								titleKey="tooltip.description" /></td>
					</tr>
				</table>
			</div>
		</div>

		<div id="ilmTransactionSetDetail"
			style="position: absolute; left: 545; top: 210; width: 70px; height: 29px; visibility: visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<c:choose>
					<c:when test="${requestScope.methodName == 'addTransactionDetail'}">
						<td align="Right"><a tabindex="8" href=#
							onclick="javascript:openWindow(buildPrintURL('print','Add Transaction Detail'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								style="margin-bottom: 4px;" src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

						</c:when>
						<c:otherwise>
						<td align="Right"><a tabindex="8" href=#
							onclick="javascript:openWindow(buildPrintURL('print','Change Transaction Detail'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
						</c:otherwise>
						</c:choose>
					<td align="right" id="Print">&nbsp; <a tabindex="9"
						onclick="printPage();" onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							title='<fmt:message key="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>

		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 20; top: 200; width: 600px; height: 39px; visibility: visible;">
			<div id="ilmTransactionSetDetail"
				style="position: absolute; left: 2; top: 4; width: 200px; height: 15px; visibility: visible;">
				<table width="140" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td id="okbutton"></td>
						<td id="cancelbutton" width="70px"><a
							title='<fmt:message key="tooltip.cancel"/>' tabindex="7"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel" /></a></td>
					</tr>
				</table>
			</div>
			<div
				style="position: absolute; left: 6; top: 4; width: 554px; height: 15px; visibility: hidden; display: none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden">
					<tr>
						<td id="okenablebutton" width="70"><a tabindex="6"
							title='<fmt:message key="tooltip.ok"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="javascript:goBackToParentWind();"><fmt:message key="button.ok" /></a></td>
						<td id="okdisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.ok" /></a></td>
					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</form>

</body>
</html>