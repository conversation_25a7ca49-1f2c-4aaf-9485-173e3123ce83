<?xml version="1.0" encoding="UTF-8" ?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>





<accountspecificsweepformat>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<singletons>
		<c:if test="${requestScope.screenName != 'addScreen'}">
			<hostId>${requestScope.accountSpecificSweepFormat.id.hostId}</hostId>
			<entityId>${requestScope.accountSpecificSweepFormat.id.entityId}</entityId>
			<accountId>${requestScope.accountSpecificSweepFormat.id.accountId}</accountId>
			<specifiedAccountId>${requestScope.accountSpecificSweepFormat.id.specifiedAccountId}</specifiedAccountId>
			<newInternalCrFormat>${requestScope.accountSpecificSweepFormat.newInternalCrFormat}</newInternalCrFormat>
			<newInternalDrFormat>${requestScope.accountSpecificSweepFormat.newInternalDrFormat}</newInternalDrFormat>
			<newExternalCrFormat>${requestScope.accountSpecificSweepFormat.newExternalCrFormat}</newExternalCrFormat>
			<newExternalDrFormat>${requestScope.accountSpecificSweepFormat.newExternalDrFormat}</newExternalDrFormat>
			<newExternalCrFormatInt>${requestScope.accountSpecificSweepFormat.newExternalCrFormatInt}</newExternalCrFormatInt>
			<newExternalDrFormatINt>${requestScope.accountSpecificSweepFormat.newExternalDrFormatINt}</newExternalDrFormatINt>
		</c:if>
	</singletons>

	<selects>
		<select id="entity">
			<c:forEach var="entity" items="${requestScope.entities}">
				<option value="${entity.value}" selected="${entity.value == requestScope.entityId ? '1' : '0'}">
						${entity.label}
				</option>
			</c:forEach>
		</select>
		<select id="currency">
			<c:forEach var="currency" items="${requestScope.currencies}">
				<option value="${currency.value}" selected="${currency.value == requestScope.currencyId ? '1' : '0'}">
						${currency.label}
				</option>
			</c:forEach>
		</select>
		<select id="accounts">
			<c:forEach var="account" items="${requestScope.accounts}">
				<option value="${account.value}" selected="${account.value == requestScope.accountId ? '1' : '0'}">
						${account.label}
				</option>
			</c:forEach>
		</select>
		<select id="specEntities">
			<c:forEach var="entity" items="${requestScope.entities}">
				<option value="${entity.value}" selected="${entity.value == requestScope.specificEntityId ? '1' : '0'}">
						${entity.label}
				</option>
			</c:forEach>
		</select>
		<select id="specAccounts">
			<c:forEach var="account" items="${requestScope.specAccounts}">
				<c:if test="${account.value != requestScope.accountId}">
					<option value="${account.value}" selected="${account.value == requestScope.specificAccountId ? '1' : '0'}">
							${account.label}
					</option>
				</c:if>
			</c:forEach>
		</select>
		<select id="formats">
			<c:forEach var="format" items="${requestScope.formats}">
				<option value="${format.value}" selected="${format.value == requestScope.specificAccountId ? '1' : '0'}">
						${format.label}
				</option>
			</c:forEach>
		</select>
	</selects>

</accountspecificsweepformat>
