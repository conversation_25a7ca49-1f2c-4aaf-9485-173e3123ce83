<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${requestScope.methodName == 'add'}">
    <fmt:message key="CurrencyInterest.addScreen" />
</c:if>

<c:if test="${requestScope.methodName == 'change'}">
    <fmt:message key="CurrencyInterest.changeScreen" />
</c:if>

</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<SCRIPT language="JAVASCRIPT">
/*Start : Variable added for issues found on V1051 beta testing by Marshal on 21-12-2010*/
var dateSelected = false;
/*End : Variable added for issues found on V1051 beta testing by <PERSON> on 21-12-2010*/
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(start)-->
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(end)-->
var dateFormat = '${sessionScope.CDM.dateFormat}';
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="showDetails";
window.opener.document.forms[0].submit();
self.close();

</c:if>
mandatoryFieldsArray=["*"];
function bodyOnLoad()
{

	var dropBox2 = new SwSelectBox(document.forms[0].elements["currencyInterest.id.currencyCode"],document.getElementById("currencyDesc"));

	/*
	 * Vivekanandan:27/05/2008
	 * This statement is unwanted
	<c:if test="${requestScope.SaveButton != 'true'}">

		document.getElementById("savebutton").innerHTML = document.getElementById("savebuttonDisabled").innerHTML;

</c:if>*/

	<c:if test="${requestScope.error == 'true'}">

		document.forms[0].elements["currencyInterest.interestRateDateAsString"].value = "";

		/* Vivekanandan:05/06/2008:Commented
		 * The error message is directly thrown
		alert('${requestScope.errorMessage}');*/


</c:if>

	<c:if test="${requestScope.methodName == 'change'}">


	document.forms[0].elements["currencyInterest.id.currencyCode"].disabled = "true";

</c:if>

	document.getElementById("entityDesc").innerText = '${entityDesc}';
}


/**
  *	Default function needed for date tab.Dont delete this method otherwise u ll get jscript error.
  */
function onDateChange(element){
}


function enableFields(){
	document.forms[0].elements["currencyInterest.id.entityId"].disabled = "";
	document.forms[0].elements["currencyInterest.id.CurrencyInterestId"].disabled = "";

}

function submitForm(methodName){
/*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(start)*/
var interestDate=validateField(document.forms[0].elements['currencyInterest.interestRateDateAsString'],'currencyInterest.interestRateDateAsString',dateFormat );


if(interestDate)
{
  var interestRate=validateField(document.forms[0].elements['currencyInterest.interestRate'],'Number 2','negativeWithDigits');
  if(interestRate)
  {
  /*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(end)*/
if(validateForm(document.forms[0])){
	/*Start : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	if(validateDateField(document.forms[0].elements['currencyInterest.interestRateDateAsString'],'auditLog.logDate_Date',dateFormat)){
	/*End : Condition for validating date field starts here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
		document.forms[0].method.value = methodName;
		document.forms[0].currencyCode.value = document.forms[0].elements['currencyInterest.id.currencyCode'].value;
		document.forms[0].entityId.value = document.forms[0].elements['currencyInterest.id.entityId'].value;
		document.forms[0].selectedInterestDate.value=document.forms[0].elements['currencyInterest.interestRateDateAsString'].value;
		document.forms[0].selectedInterestRate.value=document.forms[0].elements['currencyInterest.interestRate'].value;
		document.forms[0].submit();
		/*Start : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
		}
		/*End : Condition for validating date field ends here - for issues found on V1051 beta testing by Marshal on 21-12-2010*/
	}
}
/*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(start)*/
else
{
   document.forms[0].elements['currencyInterest.interestRate'].focus();
}
}
else
{
   document.forms[0].elements['currencyInterest.interestRateDateAsString'].focus();
}
/*Betcy:12/01/2009:Added to check the validation in "SAVE" button click(end)*/
}

function submitFormByDropDown(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].currencyCode.value = document.forms[0].elements['currencyInterest.id.currencyCode'].value;
	document.forms[0].entityId.value = document.forms[0].elements['currencyInterest.id.entityId'].value;
	document.forms[0].submit();

}


/**
  *	This function is used to validate given elements.
  */
function validateForm(objForm){

	var elementsRef = new Array(2);

	elementsRef[0] = objForm.elements["currencyInterest.interestRateDateAsString"];

	elementsRef[1] = objForm.elements["currencyInterest.interestRate"];

	return validate(elementsRef);
}

function validateRate(element){

	if(validateField(element,'Number 2','negativeWithDigits')){
		return true;
	}else{

		return false;
	}

}

<!-- Start : Marshal : Modified for Mantis 1262 -->
function validateCurrDateField(){
	if(document.forms[0].elements['currencyInterest.interestRateDateAsString']!=null && document.forms[0].elements['currencyInterest.interestRateDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['currencyInterest.interestRateDateAsString'],'auditLog.logDate_Date',dateFormat)){
		}
	}
}
<!-- End : Marshal : Modified for Mantis 1262 -->

</SCRIPT>
 <script language="JAVASCRIPT">
	    var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
		var cal = new CalendarPopup("caldiv",false,"calFrame");
		cal.setCssPrefix("CAL");
		cal.offsetX = -30;
		cal.offsetY = -100;
		var cal2 = new CalendarPopup("caldiv",false,"calFrame");
		cal2.setCssPrefix("CAL");
		cal2.offsetX = 0;
		cal2.offsetY = -100;

  </script>
<form action="currencyinterest.do" method="post" onsubmit="return validate(this);">
<input name="method" type="hidden" value="">
<input name="currencyCode" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="selectedInterestRate" type="hidden" value="">
<input name="selectedInterestDate" type="hidden" value="">


<%-- START: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad(); setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}')" onunload="call()">
<%-- END: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>

<c:set var="CDM" value="${sessionScope.CDM}"/>
  <div id="CurrencyInterest" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:420px; height:143px;">
 <div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>
 <div style="position:absolute; left:8px; top:4px; width:300px; height:80px;">
	<table width="400" border="0" cellpadding="0" cellspacing="0" height="50">

		<tr height="23">
			<td  width="50px"><b><fmt:message key="currency.entityId"/></b></td>
			<td width="25px">&nbsp;</td>
			<td  width="120px" >
				<input type="text"  name="currencyInterest.id.entityId" value="${currencyInterest.id.entityId}"   class="htmlTextAlpha"  titleKey="tooltip.entityId" maxlength="12" style="width:120px;" disabled = "true" />
			</td>
			<td width="20px">&nbsp;</td>
			<td width="100px">
				<span id="entityDesc"   name="entityDesc" class="spantext">
			</td>
		</tr>
		<tr height="7"><td></td></tr>
		<tr height="23">
			<td  width="50px"><b><fmt:message key="sweep.currencyCode"/></b></td>
			<td width="25px">&nbsp;</td>
			<td  width="120px" >
				  <select tabindex="1" name="currencyInterest.id.currencyCode" onchange="submitFormByDropDown('add');" style="width:120px">
				   <c:forEach var="item" items="${requestScope.currencies}">

       						 <option value="${item.value}" <c:if test="${currencyInterest.id.currencyCode == item.value}">selected</c:if>>${item.label}</option>

    				</c:forEach>


				  </select>
			</td>
			<td width="20px">&nbsp;</td>
			<td width="280px">
				<span id="currencyDesc"   name="currencyDesc" class="spantext">
			</td>
		</tr>
		<tr height="7"><td></td></tr>
		<tr height="23">
			<td  width="50px"><b><fmt:message key="auditLog.logDate_Date"/>*</b></td>
			<td width="25px">&nbsp;</td>
			<td  width="120px" >
			<c:if test="${requestScope.methodName == 'change'}">

			<!-- Start : Marshal : Modified for Mantis 1262 -->
				<input type="text" class="htmlTextAlpha" name="currencyInterest.interestRateDateAsString" value="${currencyInterest.interestRateDateAsString}"  style="width:80px;height:20px;" titleKey="tooltip.selectBalDate" disabled = "true" maxlength="10" onchange="if(validateForm(document.forms[0])){validateCurrDateField();}" onmouseout="dateSelected=false" />&nbsp;

</c:if>
			<c:if test="${requestScope.methodName != 'change'}">
			<!-- Vivekanandan:27/05/2008:Added:To modify Date text box as read only -->
			<!-- Betcy:13/1/2009:Remove Date text box property read only -->
				<input type="text" tabindex="2" class="htmlTextAlpha" name="currencyInterest.interestRateDateAsString" value="${currencyInterest.interestRateDateAsString}"  style="width:80px;height:20px;"  titleKey="tooltip.selectBalDate" onchange="if(validateForm(document.forms[0])){return validateField(this,'addjob.Date',dateFormat);validateCurrDateField();}" maxlength="10" onmouseout="dateSelected=false;" />&nbsp;
				<!-- End : Marshal : Modified for Mantis 1262 -->
				<A  name="datelink" ID="datelink" tabindex="3" onClick="cal.select(document.forms[0].elements['currencyInterest.interestRateDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;" ><img title='<fmt:message key="tooltip.selectInterestRateDate"/>' onClick="onDateChange(this);" src="images/calendar-16.gif" style="margin-bottom: -5px; margin-left: -8px;"></A>

			</c:if>

		</td>
			<td width="20px">&nbsp;</td>
			<td width="280px">
				<span id="interestRateDateAsString"   name="interestRateDateAsString" class="spantext" >
			</td>
		</tr>

		<tr height="7"><td></td></tr>
		<tr height="20">
			<td  width="50px"><b><fmt:message key="currencyInterest.interestRate.label"/>*</b></td>
			<td width="28px">&nbsp;</td>
			<td  width="120px" >
				<input type="text"  name="currencyInterest.interestRate" value="${currencyInterest.interestRate}"  tabindex="4"  onchange="validateRate(this);"  titleKey="tooltip.enterInterestRate"
				class="htmlTextAlpha" maxlength="12" style="width:120px;"  />
			</td>
			<td width="20px">&nbsp;</td>
			<td width="280px">
				<span id="interestRate"   name="interestRate" class="spantext">
			</td>
		</tr>

		</table>
	</div>
</div>

 <!-- Sarada:06/01/2009: Modified top attribute to display calender control properly for mantis 774-->
 <div id="CurrencyInterest" style="position:absolute; left:356px; top:188; width:70px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			<!-- Start:Vivekanandan:27/05/2008: Modified to open separate help window for add && change currency interest window -->
				<c:if test="${requestScope.methodName == 'add'}">

				    <a tabindex="7" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Currency Interest Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>

			<c:if test="${requestScope.methodName == 'change'}">

			    <a tabindex="7" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Currency Interest Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>

</c:if>
			<!-- End:Vivekanandan:27/05/2008: Modified to open separate help window for add && change currency interest window -->
		   </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
 <!-- Sarada:06/01/2009: Modified top attribute to display calender control properly for mantis 774-->
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:178px; width:420px; height:39px; visibility:visible;">
 <div id="CurrencyInterest" style="position:absolute; left:6; top:4; width:400; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" >
		<tr>
		<c:if test="${requestScope.methodName == 'add'}">

		<td width="70" id="savebutton"><a title='<fmt:message key="tooltip.save"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('save');"><fmt:message key="button.save"/></a></td>


</c:if>

  	  <c:if test="${requestScope.methodName == 'change'}">

                <td width="70" id="savebutton"><a title='<fmt:message key="tooltip.save"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('update');"><fmt:message key="button.save"/></a></td>

</c:if>

			 <td  width="70px" id="cancelbutton">
               <a  tabindex="6" title='<fmt:message key ="tooltip.cancel"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');" ><fmt:message key="button.cancel"/></a>
		  </td>
        </tr>
    </table>
</div>

<div id="MovementDisplay" style="position:absolute; left:6; top:4; width:430px; height:15px; visibility:hidden;">

					<table border="0" cellspacing="0"  cellpadding="0"  width="260px">

						<tr>
								<td id="savebuttonDisabled">
										<a  class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>
								</td>

						</tr>
					</table>
</div>


</body>
</form>
</html>