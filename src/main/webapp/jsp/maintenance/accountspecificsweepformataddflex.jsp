<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">


<%@ page import="org.swallow.util.SwtUtil"%>
<html>
	<head>
		<%@ include file="/taglib.jsp"%>
		<title>
			<fmt:message key="label.accountSpecificSweepFormatAdd.title.window" />
		</title>
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		var screenRoute = "accountspecificsweepformatadd";
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			requestURL=requestURL.substring(0,idy+1) ;
		
			var hostId = '${requestScope.hostId}';			
			var entityId = '${requestScope.entityId}';			
			var accountId = '${requestScope.accountId}';			
			var currencyId = '${requestScope.currencyId}';
			var id = '${requestScope.id}';
			var parentMethodName = '${requestScope.parentMethodName}';
						
			var specifiedAccountId = '${requestScope.specifiedAccountId}';	
			var specifiedEntityId = '${requestScope.specifiedEntityId}';	
			var menuAccessId = '${requestScope.menuAccessId}';
			var screenName = '${requestScope.screenName}';
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();			
			};
		
			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["alert"] = new Array ();
			
			label["text"]["screen-title"] = "<fmt:message key="label.accountspecificsweepformat.title.window"/>";
			// label  fields
			
			// Save button
			label["text"]["button-ok"] = "<fmt:message key="button.ok"/>";
			label["tip"]["button-ok"] = "<fmt:message key="button.ok"/>";	
			
			// Cancel button
			label["text"]["button-cancel"] = "<fmt:message key="button.cancel"/>";
			label["tip"]["button-cancel"] = "<fmt:message key="button.cancel"/>";	
		
			//Text
			label["text"]["label-entity"] = "<fmt:message key="label.accountspecificsweepformat.text.entity"/>";    
			label["text"]["label-currency"] = "<fmt:message key="label.accountspecificsweepformat.text.currency"/>";    
			label["text"]["label-accountId"] = "<fmt:message key="label.accountspecificsweepformat.text.accountId"/>";    
			label["text"]["label-specAccountId"] = "<fmt:message key="label.accountspecificsweepformat.text.specAccountId"/>";    
			label["text"]["label-credit"] = "<fmt:message key="label.accountspecificsweepformat.text.credit"/>";    
			label["text"]["delete-debit"] = "<fmt:message key="label.accountspecificsweepformat.text.debit"/>";    
			label["text"]["label-internal"] = "<fmt:message key="label.accountspecificsweepformat.text.internal"/>";    
			label["text"]["label-external"] = "<fmt:message key="label.accountspecificsweepformat.text.external"/>";    
			label["text"]["label-extViaIntermediary"] = "<fmt:message key="label.accountspecificsweepformat.text.extViaIntermediary"/>";    
			
			//Tooltip fields
			label["tip"]["label-entity"] = "<fmt:message key="tooltip.entityId"/>";    
			label["tip"]["label-currency"] = "<fmt:message key="tooltip.currency"/>";    
			label["tip"]["label-accountID"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.accountID"/>";    
			label["tip"]["label-specAccountID"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.specAccountID"/>";    
			label["tip"]["label-internalCredit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.externalCredit"/>";    
			label["tip"]["label-internalDebit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.internalDebit"/>";    
			label["tip"]["label-externalCredit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.externalDebit"/>";    
			label["tip"]["label-externalDebit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.externalDebit"/>";    
			label["tip"]["label-extViaIntermediaryCredit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.extViaIntermediaryCredit"/>";    
			label["tip"]["label-extViaIntermediaryDebit"] = "<fmt:message key="label.accountspecificsweepformat.tooltip.extViaIntermediaryDebit"/>";    
			
			// Alerts
			label["alert"]["label-warningExistAccountData"] = "<fmt:message key="label.accountspecificsweepformat.alert.warningExistAccountData"/>";    
			label["alert"]["label-specAcctIdRequired"] = "<fmt:message key="label.accountspecificsweepformat.alert.specAccountIdRequired"/>";    
			label["alert"]["label-specEntityIdRequired"] = "<fmt:message key="label.accountspecificsweepformat.alert.specEntityIdRequired"/>";    
	
			// Used to check if the object have an existing data
			function checkExistingDataMethod(entityId, accountId, specifiedAccountId, csrf, parentMethodName) {
				var appName = "<%=SwtUtil.appName%>";
				var requestURL = new String('<%=request.getRequestURL()%>');
				var idy = requestURL.indexOf('/' + appName + '/');

				requestURL = requestURL.substring(0, idy + 1);
				requestURL = requestURL + appName + "/accountSpecificSweepFormat.do?method=checkExistingDataMethod"
							+ "&entityId="+entityId
							+ "&accountId="+accountId
							+ "&specifiedAccountId="+specifiedAccountId
							+ "&parentMethodName="+parentMethodName
							+ "&id="+csrf;
					var oXMLHTTP = new XMLHttpRequest();
					oXMLHTTP.open("POST", requestURL, false);
					oXMLHTTP.send();
					var res = new String(oXMLHTTP.responseText);
				return res == "N" ? false : true;
			}
			
			
			/**
			 * Close window
			 */
			function closeWindow() {
				//refresh data grid in parent screen
				window.opener.refreshGridData();
				window.close();
			}
		
			/**
			 * help
			 * This function opens the help screen 
			 */
			function help() {
				openWindow(
						buildPrintURL('print', 'Define AccountSpecificSweepFormat'),
						'sectionprintdwindow',
						'left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no',
						'true')
			}
		</script>
	</head>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
	<%@ include file="/angularJSUtils.jsp"%>
    <%@ include file="/angularscripts.jsp"%>
	</body>
</html>
