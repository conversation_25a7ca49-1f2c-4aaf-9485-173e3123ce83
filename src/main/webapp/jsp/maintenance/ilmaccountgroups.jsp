<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.ILMAccountGroup"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="ilmaccountgroup.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">



	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad()
	{
		xl = new XLSheet("ilmAccountGroupsDetails","table_2", ["String","String", "String", "String","Number","String","String","String"],"********");

		xl.onsort = xl.onfilter = disableButtons;

		highlightTableRows("ilmAccountGroupsDetails");


		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

		var dropBox1 = new SwSelectBox(document.forms[0].elements["intraDayMaintenanceAccountGroups.id.entityId"],document.getElementById("entityName"));
		var dropBox2 = new SwSelectBox(document.forms[0].elements["intraDayMaintenanceAccountGroups.id.currencyCode"],document.getElementById("currencyDesc"));



		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);

		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);

		 buttonStatus = "<%= request.getAttribute(SwtConstants.VIEW_BUT_STS) %>";
		document.getElementById("viewbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);

		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);

		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	}

	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e)
	{
        var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}

		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;

		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);

		if(isRowSel == false)
			rowElement.className = 'selectrow' ;

		onSelectTableRow(rowElement,!isRowSel);
	}

	/**
	 * Disable buttons && colors when the user does not select any row in the grid
	 *
	 **/
	function disableButtons(){

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		updateColors();
	}

	/**
	 * Triggered when the user deletes a scenario via delete button
	 * @ param methodName
	 **/
	function submitDeleteForm(methodName){
		var global=document.forms[0].selectedGlobalFlag.value;
		if(global!="Y")
		{
			document.forms[0].method.value = methodName;
			var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
			if (yourstate==true)
				document.forms[0].submit();
		}else
			alert("<fmt:message key="alert.groupSpecifiedAsCurrencyGlobal"/>");

	}

	/**
	 * Build URL that will open the add scenario screen
	 * @ param methodName
	 **/
	function buildAddGroupURL(methodName, action){
		var param = 'intraDayLiquidity.do?method='+methodName;
		if(action!="add"){
			param +='&selectedAccountGroup='+document.forms[0].selectedAccountGroup.value;
		}else {
			param +='&selectedAccountGroup=';
			param +='&entityId='+document.forms[0].elements["intraDayMaintenanceAccountGroups.id.entityId"].value;
			param +='&currencyCode='+document.forms[0].elements["intraDayMaintenanceAccountGroups.id.currencyCode"].value;
		}
		param +='&parentScreen=IlmAccountGroupMaintenance';
		param +='&methodName='+action;
		return  param;
	}

	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected
	 **/
	function onSelectTableRow(rowElement , isSelected)
	{
		var hiddenElement = rowElement.getElementsByTagName("input")[0];
		var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
		var hiddenElement3 = rowElement.getElementsByTagName("input")[2];
		var hiddenElement4 = rowElement.getElementsByTagName("input")[3];
		var hiddenElement5 = rowElement.getElementsByTagName("input")[4];
		var hiddenElement6 = rowElement.getElementsByTagName("input")[5];

		document.forms[0].selectedAccountGroup.value = hiddenElement4.value;
		document.forms[0].selectedGlobalFlag.value = hiddenElement5.value;

		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
		requestURL = requestURL + appName+"/intraDayLiquidity.do?method=getAccessForILMGroup";
		requestURL = requestURL + "&entityId=" + hiddenElement3.value;
		requestURL = requestURL + "&currencyCode=" + hiddenElement2.value;
		requestURL = requestURL + "&userId=" + hiddenElement.value;
		requestURL = requestURL + "&publicPrivate=" + hiddenElement6.value;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		menuEntityCurrGrpAccess=new String(oXMLHTTP.responseText);
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		document.getElementById("viewbutton").innerHTML = (isSelected?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);

	if (document.forms[0].menuAccessId.value == 0) {

			document.getElementById("changebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" ? document
					.getElementById("changeenablebutton").innerHTML : document
					.getElementById("changedisablebutton").innerHTML);

			document.getElementById("deletebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" ? document
					.getElementById("deleteenablebutton").innerHTML : document
					.getElementById("deletedisablebutton").innerHTML);

		} else if (document.forms[0].menuAccessId.value == "1" && isSelected)
			document.getElementById("viewbutton").innerHTML = document
					.getElementById("viewenablebutton").innerHTML;
	}

	function submitForm(methodName) {
		if (methodName == "listAccountGroupsByEntity")
			document.forms[0]
					.elements["intraDayMaintenanceAccountGroups.id.currencyCode"].value = "All";
		if (methodName == "listAccountGroupsByEntity"
				|| methodName == "listAccountGroupsByCcy")
			methodName = "listAccountGroups";

		document.forms[0].method.value = methodName;
		document.forms[0].submit();

	}
</SCRIPT>
</head>

<form action="intraDayLiquidity.do" method="post">
<input name="method" type="hidden" value="listAccountGroups">
<input name="selectedAccountGroup" type="hidden" value="">
<input name="selectedGlobalFlag" type="hidden" value="">
<input name="menuAccessId" type="hidden" >

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
<div id="AccountMaintenance"
		style="position: absolute; left: 20px; top: 20px; width: 1165px; height: 62px; border: 2px outset;"
		color="#7E97AF">
	<div
		style="position: absolute; left: 8px; top: 4px; width: 705px; height: 70px;">
	<table width="690px" border="0" cellpadding="0" cellspacing="0"
		height="50">
 	<tr color="black" border="0">
 	  <td width="65"><b><fmt:message key="accountGroup.entityId"/></b></td>
 	  <td width="28">&nbsp;</td>
 	  				<td width="150px" >

			<select id="intraDayMaintAcctGrpsEntityId" name="intraDayMaintenanceAccountGroups.id.entityId"
			tabindex="1" titleKey="tooltip.selectEntityid" class="htmlTextAlpha" onchange="submitForm('listAccountGroupsByEntity')"
			style="width: 140px">
			<c:forEach var="item" items="${requestScope.entities}">
				<option value="${item.value}"
					 <c:if test="${intraDayMaintenanceAccountGroups.id.entityId == item.value}">selected="selected"</c:if>>
					 ${item.label}
				</option>
			</c:forEach>
			</select>
				</td>
 	   <td width="20">&nbsp;</td>
 	  <td width="280">
 		<span id="entityName" name="entityName" class="spantext">
 	   </td>
 	 </tr>

 	 <tr>
			<td width="60"><b><fmt:message key="matchQuality.currencyCode"/></b></td>
			<td width="28">&nbsp;</td>
				<td width="150">
			<select id="intraDayMaintAcctGrpsCcyCode" name="intraDayMaintenanceAccountGroups.id.currencyCode"
			tabindex="1" titleKey="tooltip.selectCurrencyCode" class="htmlTextAlpha" onchange="submitForm('listAccountGroupsByCcy')"
			style="width: 55px">
			<c:forEach var="item" items="${requestScope.currencyList}">
				<option value="${item.value}"
					 <c:if test="${intraDayMaintenanceAccountGroups.id.currencyCode == item.value}">selected="selected"</c:if>>
					 ${item.label}
				</option>
			</c:forEach>
			</select>

				</td>

			<td width="20">&nbsp;</td>
			<td width="432"><span id="currencyDesc" class="spantext"></td>
		</tr>
 </table>
</div>
</div>

	<div id="IlmAccountGroups" style="position:absolute; border:2px outset; left:20px; top:88px; width:1165px; height:400;">
		<div id="IlmAccountGroups" style="position:absolute;z-index:99;left:0px; top:0px; width:1162; height:10px;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1122" border="0" cellspacing="1" cellpadding="0">
				<thead>
					<tr height="22px">
						<td width="270" align="left" title='<fmt:message key="tooltip.accountGroupId"/>'><b><fmt:message key="accountGroup.id"/></b></td>
						<td width="280" align="left" title='<fmt:message key="tooltip.accountGroup.ilmGroupName"/>'><b><fmt:message key="accountGroup.ilmGroupName"/></b></td>
						<td width="70" align="left"  title='<fmt:message key="tooltip.accountGroup.currencyCode"/>'><b><fmt:message key="accountGroup.currencyCode"/></b></td>
						<td width="115" align="left" title='<fmt:message key="tooltip.accountGroup.groupType"/>'><b><fmt:message key="accountGroup.groupType"/></b></td>
						<td width="87" align="left" title='<fmt:message key="tooltip.accountGroup.accs"/>'><b><fmt:message key="accountGroup.accs"/></b></td>
						<td width="90" align="left" title='<fmt:message key="tooltip.accountGroup.global"/>'><b><fmt:message key="accountGroup.global"/></b></td>
						<td width="100" align="left" title='<fmt:message key="tooltip.accountGroup.publicPrivate"/>'><b><fmt:message key="accountGroup.publicPrivate"/></b></td>
						<td width="120" align="left" title='<fmt:message key="tooltip.accountGroup.createdByUser"/>'><b><fmt:message key="accountGroup.createdByUser"/></b></td>
					</tr>
				</thead>
			</table>
		</div>
		<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:1159px; height:390;overflowY:scroll">
			<div id="IlmAccountGroups" style="position:absolute;z-index:99;left:0px; top:22px; width:1130; height:10px;">
				<table class="sort-table" id="ilmAccountGroupsDetails" width="1122px" border="0" cellspacing="1" cellpadding="0" height="368">
					<tbody>
						<%int count = 0; %>
						<c:forEach var="item" items="${ilmAccountGroupsDetails}">
							<% if( count%2 == 0 ) {%><tr height="20px" class="even"><% }else  { %> <tr height="20px" class="odd"> <%}++count; %>
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.createdByUser}" disabled="true" />
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.currencyCode}" disabled="true" />
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.entityId}" disabled="true" />
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.id.ilmGroupId}" disabled="true" />
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.global}" disabled="true" />
								<input type="hidden" name="ilmAccountGroupsDetails" value="${item.publicPrivateAsString}" disabled="true" />
								<td width="270" align="left">${item.id.ilmGroupId}&nbsp;</td>
								<td width="280" align="left">${item.ilmGroupName}&nbsp;</td>
								<td width="70" align="left">${item.currencyCode}&nbsp;</td>
								<td width="115" align="left">${item.dynamicFixed}&nbsp;</td>
								<td width="87" align="right">${item.accs}&nbsp;</td>
								<td width="90" align="center">${item.global}&nbsp;</td>
								<td width="100" align="left">${item.publicPrivateAsString}&nbsp;</td>
								<td width="120" align="left">${item.createdByUser}&nbsp;</td>
							</tr>
						</c:forEach>
					</tbody>
					<tfoot>
						<tr><td colspan="8" ></td></tr>
					</tfoot>
				</table>
			</div>
		</div>
		<div id="IlmAccountGroups" style="position:absolute; left:1075; top:415; width:70px; height:29px; visibility:visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','ILM Account Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help" style="margin-top: -4px;"  border="0" ></a>
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="9" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:495;  width:1165px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:1140px; height:15px; visibility:visible;">
				<table width="410" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td id="closebutton" width="70px">
							<a title='<fmt:message key="tooltip.close"/>' tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden; display:none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
			            <td id="addenablebutton">
							<a title='<fmt:message key="tooltip.addAccountGroup"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddGroupURL('accountGroupDetailsFlex','add'),'accountgroupadd','left=50,top=190,width=995,height=800,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add"/></a>
						</td>
						<td id="adddisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
						</td>
						<td id="changeenablebutton">
							<a title='<fmt:message key="tooltip.changeAccountGroup"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddGroupURL('accountGroupDetailsFlex','change'),'accountgroupchange','left=50,top=190,width=995,height=800,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
						</td>
						<td id="changedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
						</td>
						<td id="viewenablebutton">
							<a  title='<fmt:message key="tooltip.viewAccountGroup"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddGroupURL('accountGroupDetailsFlex','view'),'accountgroupview','left=50,top=190,width=995,height=800,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
						</td>
						<td id="viewdisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
						</td>
						<td id="deleteenablebutton">
							<a  title='<fmt:message key="tooltip.deleteAccountGroup"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('deleteAccountGroup');" ><fmt:message key="button.delete"/></a>
						</td>
						<td id="deletedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
						</td>
						<td id="roleenablebutton">

						</td>
						<td id="roledisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.role"/></a>
						</td>
					</tr>
				</table>
			  </div>
			</div>

		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
</body>
</form>
</html>            