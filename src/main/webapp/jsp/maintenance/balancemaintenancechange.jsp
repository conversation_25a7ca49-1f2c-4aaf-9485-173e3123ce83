<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.selectedMode != 'view'}">
	<title><fmt:message key="balMaintenance.title.addWindow" /></title>
    </c:when>
    <c:otherwise>
	<title><fmt:message key="balMaintenance.title.viewWindow" /></title>
    </c:otherwise>
</c:choose>
<SCRIPT language="JAVASCRIPT">
var dateFormat = '${sessionScope.CDM.dateFormat}';
var currencyFormat = '${sessionScope.CDM.currencyFormat}';
var currentPage = "${requestScope.currentPage}";
var selectedFilter = "${requestScope.selectedFilter}";
var selectedSort = "${requestScope.selectedSort}";
var balType = "${requestScope.balanceType}";
var cancelcloseElements = new Array(1);
cancelcloseElements[0]="cancelbutton";
var selectedCurrCode = '${selectedCurrencyCode}';
var oldWorkingForecast="";
var oldWorkingExternal="";
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="next";
window.opener.document.forms[0].selectedBalTypeId.value ='${selectedBalTypeId}' ;
window.opener.document.forms[0].selectedBalType.value = '${balanceType}' ;
window.opener.document.forms[0].balanceType.value = balType;
window.opener.document.forms[0].balanceDate.value = '${selectedDate}' ;
window.opener.document.forms[0].entityId.value = "${requestScope.entityId}";
window.opener.document.forms[0].currencyCode.value = '${selectedCurrency}';
window.opener.document.forms[0].currentPage.value = "${requestScope.currentPage}";
window.opener.document.forms[0].selectedFilter.value ='${requestScope.selectedFilter}';
window.opener.document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
window.opener.document.forms[0].selectedCurrency.value='${selectedCurrency}';
window.opener.document.forms[0].selectedName.value = '${selectedName}' ;
window.opener.document.forms[0].reasonCode.value = '${requestScope.reasonCode}' ;
window.opener.document.forms[0].reasonDesc.value = '${requestScope.reasonDesc}' ;
window.opener.document.forms[0].userNotes.value = '${requestScope.userNotes}' ;
window.opener.document.forms[0].goToPageNo.value = "${requestScope.currentPage}";
window.opener.document.forms[0].submit();
self.close();
</c:if>

	/**
	 * Method to update the changes in Balance maintenance screen
	 * @param methodName
	 */
	function submitForm(methodName){
		if (validateField(
				document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'],
				'balmaintenance.internalBalanceEodDateAsString', dateFormat) && validateField(document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'],
				'balmaintenance.externalBalanceEodDateAsString', dateFormat)){
		var workingExternalSOD = document.forms[0].elements['balmaintenance.workingExternalSODAsString'].value;
		var workingForecastSOD = document.forms[0].elements['balmaintenance.workingForecastSODAsString'].value;
		if(checkBalance(document.forms[0].elements['balmaintenance.workingExternalSODAsString'])&&checkBalance(document.forms[0].elements['balmaintenance.workingForecastSODAsString'])){
			document.forms[0].method.value = methodName;
			document.forms[0].selectedBalType.value = document.forms[0].balType.value ;
			document.forms[0].selectedBalTypeId.value = document.forms[0].elements['balmaintenance.id.balanceTypeId'].value;
			document.forms[0].selectedDate.value = document.forms[0].elements['balmaintenance.balancedateAsString'].value;

			if(document.forms[0].elements['balmaintenance.updateUser'].value=="")
			{
				document.forms[0].selectedSodBalance.value='${sodBalance}';
			}
			else
			document.forms[0].selectedSodBalance.value='0.0';
			document.forms[0].selectedCurrency.value='${selectedCurrency}';
			document.forms[0].currentFilter.value = '${requestScope.selectedFilter}';
			document.forms[0].currentSort.value = '${requestScope.selectedSort}';
			document.forms[0].currentPage.value = '${requestScope.currentPage}';
			document.forms[0].reasonCode.value = '${requestScope.reasonCode}';
			document.forms[0].reasonDesc.value = '${requestScope.reasonDesc}';
			document.forms[0].userNotes.value = '${requestScope.userNotes}';
			document.forms[0].balanceType.value = balType;
			document.forms[0].selectedName.value = document.forms[0].name.value ;
			document.forms[0].selectedEntity.value=document.forms[0].elements['balmaintenance.id.entityId'].value;
			document.forms[0].selectedWorkingForecastSOD.value=workingForecastSOD;
			document.forms[0].selectedWorkingForecastSODType.value=document.forms[0].elements['balmaintenance.workingForecastSODType'].value;
			document.forms[0].selectedWorkingExternalSOD.value=workingExternalSOD;
			document.forms[0].selectedWorkingExternalSODType.value=document.forms[0].elements['balmaintenance.workingExternalSODType'].value;
			document.forms[0].selectedBvForecastAdjust.value=document.forms[0].elements['balmaintenance.bvForecastAdjustAsString'].value;
			document.forms[0].selectedBvForecastAdjustLastId.value=document.forms[0].elements['balmaintenance.bvForecastAdjustLastId'].value;
			document.forms[0].selectedBvExternalAdjust.value=document.forms[0].elements['balmaintenance.bvExternalAdjustAsString'].value;
			document.forms[0].selectedBvExternalAdjustLastId.value=document.forms[0].elements['balmaintenance.bvExternalAdjustLastId'].value;
			document.forms[0].selectedCalculatedIntPredBalance.value=document.forms[0].elements['balmaintenance.calculatedIntPredBalanceAsString'].value;
			document.forms[0].selectedCalculatedExtPredBalance.value=document.forms[0].elements['balmaintenance.calculatedExtPredBalanceAsString'].value;
			document.forms[0].selectedSuppliedInternalBalance.value=document.forms[0].elements['balmaintenance.suppliedInternalBalanceAsString'].value;
			document.forms[0].selectedSuppliedExternalBalance.value=document.forms[0].elements['balmaintenance.suppliedExternalBalanceAsString'].value;
			document.forms[0].selectedInternalBalanceSource.value=document.forms[0].elements['balmaintenance.internalBalanceSource'].value;
			document.forms[0].selectedExternalBalanceSource.value=document.forms[0].elements['balmaintenance.externalBalanceSource'].value;
			document.forms[0].selectedInterBalEodDate.value=document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'].value;
			document.forms[0].selectedExtBalEodDate.value=document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'].value;
			document.forms[0].oldWorkingForecast.value=oldWorkingForecast;
			document.forms[0].oldWorkingExternal.value=oldWorkingExternal;
			document.forms[0].submit();
		}
		}
	}
	/**
	 * Method to enable balance fields
	 */
	function enableFields(){
		document.forms[0].elements["balmaintenance.id.entityId"].disabled = "";
		document.forms[0].elements["balmaintenance.balancedateAsString"].disabled = "";
		document.forms[0].elements["balmaintenance.id.balanceTypeId"].disabled = "";
		document.forms[0].elements["balmaintenance.replaceDate"].disabled = "";
		document.forms[0].elements["balmaintenance.updateUser"].disabled = "";
		document.forms[0].elements["balmaintenance.startBalanceasString"].disabled = "";
		document.forms[0].elements["balmaintenance.internalBalanceEodDateAsString"].disabled="";
		document.forms[0].elements["balmaintenance.externalBalanceEodDateAsString"].disabled="";

	}

	/**
	 * Method called when Page loads
	 */
	function bodyOnLoad(){
		oldWorkingExternal = document.forms[0].elements['balmaintenance.workingExternalSODAsString'].value;
		oldWorkingForecast = document.forms[0].elements['balmaintenance.workingForecastSODAsString'].value;

		document.forms[0].initialSuppliedInternalBalance.value=document.forms[0].elements['balmaintenance.suppliedInternalBalanceAsString'].value;
		document.forms[0].initialSuppliedExternalBalance.value=document.forms[0].elements['balmaintenance.suppliedExternalBalanceAsString'].value;
		}

	/**
	 * Method to Validate balances
	 * @param element
	 */
	function checkBalance(element) {
		var result = false;
		var amtStr = new String(element.value);
		var firstChar = amtStr.charAt(0);
		if (firstChar == '-') {
			result =  validateNegativeCurrencyPlaces(element,'balance',currencyFormat, selectedCurrCode);
		}else{
			result =   validateCurrency(element,'balance',currencyFormat, selectedCurrCode);
		}

		if(element.value=="" && element==document.forms[0].elements['balmaintenance.suppliedInternalBalanceAsString']){
			document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'].value="";
		}else if (element.value=="" && element==document.forms[0].elements['balmaintenance.suppliedExternalBalanceAsString']){
			document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'].value="";
		}


		return result;
	}

	var lockedInternalBal = true;
	var lockedExternalBal = true;
	var lockedInterBalEodDate = true;
	var lockedExtBalEodDate = true;

	function changeIcon(from) {

		if(from == 'internalSupplied'){
			if(lockedInternalBal)	{
				document.getElementById("lockUnlockImgInternalSupplied").src =  "images/unlock.png";
				lockedInternalBal = false;
				document.forms[0].elements["balmaintenance.suppliedInternalBalanceAsString"].disabled = false;
				document.forms[0].elements["balmaintenance.suppliedInternalBalanceAsString"].style.backgroundColor = "white";
			}else{
				document.getElementById("lockUnlockImgInternalSupplied").src =  "images/lock.png";
				lockedInternalBal = true;
				document.forms[0].elements["balmaintenance.suppliedInternalBalanceAsString"].disabled = true;
				document.forms[0].elements["balmaintenance.suppliedInternalBalanceAsString"].style.backgroundColor = "rgb(229, 229, 229)";
			}
		}		else if (from == 'externalSupplied') {
			if(lockedExternalBal)	{
				document.getElementById("lockUnlockImgExternalSupplied").src =  "images/unlock.png";
				lockedExternalBal = false;
				document.forms[0].elements["balmaintenance.suppliedExternalBalanceAsString"].disabled = false;
				document.forms[0].elements["balmaintenance.suppliedExternalBalanceAsString"].style.backgroundColor = "white";
			}else{
				document.getElementById("lockUnlockImgExternalSupplied").src =  "images/lock.png";
				lockedExternalBal = true;
				document.forms[0].elements["balmaintenance.suppliedExternalBalanceAsString"].disabled = true;
				document.forms[0].elements["balmaintenance.suppliedExternalBalanceAsString"].style.backgroundColor = "rgb(229, 229, 229)";
			}
		}else if (from == 'interBalEodDate') {
			if(lockedInterBalEodDate)	{
				document.getElementById("lockUnlockImgInterBalEodDate").src =  "images/unlock.png";
				lockedInterBalEodDate = false;
				document.forms[0].elements["balmaintenance.internalBalanceEodDateAsString"].disabled = false;
				document.forms[0].elements["balmaintenance.internalBalanceEodDateAsString"].style.backgroundColor = "white";
			}else{
				document.getElementById("lockUnlockImgInterBalEodDate").src =  "images/lock.png";
				lockedInterBalEodDate = true;
				document.forms[0].elements["balmaintenance.internalBalanceEodDateAsString"].disabled = true;
				document.forms[0].elements["balmaintenance.internalBalanceEodDateAsString"].style.backgroundColor = "rgb(229, 229, 229)";
			}
		}else  {
			if(lockedExtBalEodDate)	{
				document.getElementById("lockUnlockImgExtBalEodDate").src =  "images/unlock.png";
				lockedExtBalEodDate = false;
				document.forms[0].elements["balmaintenance.externalBalanceEodDateAsString"].disabled = false;
				document.forms[0].elements["balmaintenance.externalBalanceEodDateAsString"].style.backgroundColor = "white";
			}else{
				document.getElementById("lockUnlockImgExtBalEodDate").src =  "images/lock.png";
				lockedExtBalEodDate = true;
				document.forms[0].elements["balmaintenance.externalBalanceEodDateAsString"].disabled = true;
				document.forms[0].elements["balmaintenance.externalBalanceEodDateAsString"].style.backgroundColor = "rgb(229, 229, 229)";
			}
		}
	}


    function interBalEodDateClick(){
		cal.select(document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'],'datelink',dateFormatValue);
		return false;
	}


    function extBalEodDateClick(){
		cal2.select(document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'],'datelink2',dateFormatValue);
		return false;
	}


	function checkSuppliedInterBalance() {

		var suppliedInterBal = document.forms[0].elements['balmaintenance.suppliedInternalBalanceAsString'].value;
		if (suppliedInterBal == "") {
			ShowErrMsgWindowWithBtn("", '<fmt:message key = "message.alert.emptySuppliedInterBalance"/>', null);
			document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'].value = "";
		} else {
			validateField(document.forms[0].elements['balmaintenance.internalBalanceEodDateAsString'], 'balmaintenance.internalBalanceEodDateAsString', dateFormat);
		}
	}

	function checkSuppliedExtBalance() {

		var suppliedExtBal = document.forms[0].elements['balmaintenance.suppliedExternalBalanceAsString'].value;
		if (suppliedExtBal == "") {
			ShowErrMsgWindowWithBtn("", '<fmt:message key = "message.alert.emptySuppliedExtBalance"/>', null);
			document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'].value = "";
		} else {
			validateField(document.forms[0].elements['balmaintenance.externalBalanceEodDateAsString'], balmaintenance.externalBalanceEodDateAsString, dateFormat);
		}
	}

</SCRIPT>

  <script language="JAVASCRIPT">
  var cal = new CalendarPopup("caldiv");
  cal.offsetX = -105;
  cal.offsetY = -75;
  var cal2 = new CalendarPopup("caldiv");
  cal2.offsetX = -115;
  cal2.offsetY = -75;
  var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
  var dateSelected= true;
  </script>

<style>

#ddimagetabs #firstsc{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.default{
   background-image: url(images/new_bluetaboverBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.current{
   background-image: url(images/new_bluetabBIS.gif); /* URL to tab image */
}

#ddimagetabs #firstsc.hoverall{
   background-image: url(images/new_bluetabover_glowBIS.gif); /* URL to tab image */
}

#parametersStatusInfo.hover{
	text-decoration: underline;
}

</style>

</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="ShowErrMsgWindow('${actionError}');bodyOnLoad();setFocus(document.forms[0]); setParentChildsFocus();"
	onunload="call()">
<form action="balMaintenance.do" onsubmit="validate(this);">
	<input name="selectedBalType" type="hidden" value="C">
	<input name="selectedBalTypeId" type="hidden" value="AC001">
	<input name="selectedName" type="hidden" value="Name">
	<input name="selectedDate" type="hidden" value="AC001">
	<input name="selectedSodBalance" type="hidden" value="001">
	<input name="selectedCurrency" type="hidden" value="EUR">
	<input name="selectedEntity" type="hidden" value="EUR">
	<input name="selectedWorkingForecastSOD" type="hidden" value="">
	<input name="selectedWorkingForecastSODType" type="hidden" value="">
	<input name="selectedWorkingExternalSOD" type="hidden" value="">
	<input name="selectedWorkingExternalSODType" type="hidden" value="">
	<input name="selectedBvForecastAdjust" type="hidden" value="">
	<input name="selectedBvForecastAdjustLastId" type="hidden" value="">
	<input name="selectedBvExternalAdjust" type="hidden" value="">
	<input name="selectedBvExternalAdjustLastId" type="hidden" value="">
	<input name="selectedCalculatedIntPredBalance" type="hidden" value="">
	<input name="selectedCalculatedExtPredBalance" type="hidden" value="">
	<input name="selectedSuppliedInternalBalance" type="hidden" value="">
	<input name="selectedSuppliedExternalBalance" type="hidden" value="">
	<input name="initialSuppliedInternalBalance" type="hidden" value="">
	<input name="initialSuppliedExternalBalance" type="hidden" value="">
	<input name="selectedInternalBalanceSource" type="hidden" value="">
	<input name="selectedExternalBalanceSource" type="hidden" value="">
	<input name="selectedInterBalEodDate" type="hidden" value="">
	<input name="selectedExtBalEodDate" type="hidden" value="">
	<input name="balanceType" type="hidden" value='${balType}'>
	<input name="reasonCode" type="hidden" value='${reasonCode}'>
	<input name="reasonDesc" type="hidden" value='${reasonDesc}'>
	<input name="userNotes" type="hidden" value='${userNotes}'>
	<input name="currentFilter" type="hidden" value='${selectedFilter}'>
	<input name="currentSort" type="hidden" value='${selectedSort}'>
	<input name="currentPage" type="hidden" value='${currPage}'>
	<input name="oldWorkingForecast" type="hidden" value=''>
	<input name="oldWorkingExternal" type="hidden" value=''>
    <c:set var="CDM" value="${sessionScope.CDM}" />
	<input name="method" type="hidden" value="list">
	<div id="BalanceMaintenance"
		style="position: absolute; left: 20px; top: 5px; width: 583px; height: 460px; border: 2px outset;"
		color="#7E97AF">
	<div id="BalanceMaintenance"
		style="position: absolute; left: 3px; top: 5px; width: 410px; height: 38px;">
	<!------------------------------first fieldset------------------------------------------->
	<DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
	<div style="left: 3px; top: 4px;height: 143px;">
	<fieldset style="width: 570px; border: 2px groove; ">
	<legend> <fmt:message key="changeBalance.legend.acctBalance" />
	</legend>
	<table width="570" border="0" cellpadding="0" cellspacing="1"
		height="125">
		<!-- start:modified the mantis 1876 by the sunil.The purpose of the  account alignment and pagination.-->
		<tr color="black" height="24px">
			<td width="140">&nbsp;<b><fmt:message key="bookCode.entity" /></b></td>
			<td width="120"><input type="text" class="htmlTextAlpha"
				name="balmaintenance.id.entityId" value="${balmaintenance.id.entityId}" style="width:120px;"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>/></td>
			<td width="300"><input class="textAlpha"
				style="width: 230px; background: transparent; border: thin;"
				value="${entityName}" size="20" title="${entityName}" readonly="readonly" ></td>
			</td>
		</tr>
		<tr height="24px">
			<td width="120px">&nbsp;<b><fmt:message key="date" /></b></td>

			<td width="160px"><input type="text" class="htmlTextNumeric"
				name="balmaintenance.balancedateAsString" value="${balmaintenance.balancedateAsString}"  style="width:80px;"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> /></td>
		</tr>
		<tr height="24px">
			<td width="120px">&nbsp;<b><fmt:message key="type" /></b></td>

			<td width="128px"><input value="${balType}" name="balType"
				style="width: 120px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />

			</td>
		</tr>
		<tr height="24px">
			<td width="180Px">&nbsp;<b><fmt:message key="changeBalance.acctId" /></b></td>
			<td width="220px"><input type="text" class="htmlTextAlpha"
				name="balmaintenance.id.balanceTypeId" value="${balmaintenance.id.balanceTypeId}" style="width:220px;"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> /></td>
			<td width="280">&nbsp; <input class="textAlpha"
				style="width: 230px; background: transparent; border: thin;"
				value="${name}" size="20" readonly="readonly" ></td>
			</td>
		</tr>
		<tr height="24px">
			<td width="300px">&nbsp;<b><fmt:message key="changeBalance.updateUser" /></b></td>
			<td width="120px"><input type="text" class="htmlTextAlpha"
				name="balmaintenance.updateUser" value="${balmaintenance.updateUser}" style="width:144px;"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
			<td width="300"><input type="text" class="htmlTextAlpha"
				name="balmaintenance.replaceDate" value="${balmaintenance.replaceDate}" style="width:85px;"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />&nbsp; <input type="text"
				class="htmlTextAlpha" name="balmaintenance.inputtime" value="${balmaintenance.inputtime}"
				style="width:70px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
			</td>
		</tr>
	<!-- End:modified the mantis 1876 by the sunil.The purpose of the  account alignment and pagination.-->

	</table>
	</fieldset>
	</div>
	<!--------------------------------------end of first fieldset---------------------------------->
	<tr height="8px">

	</tr>
	<!------------------------------Second fieldset------------------------------------------->
	<div style="left: 3px; top: 4px; height: 124px;">
	<fieldset style="width: 570px; border: 2px groove;">

	<legend> <fmt:message key="changeBalance.legend.suppSods" />
	</legend>
			<table border="0" width="570">
				<tr>
					<td width="67px">&nbsp;<b><fmt:message key="changeBalance.internal" /></b></td>
					<td width="17px">&nbsp;</td>
					<c:if test="${requestScope.selectedMode != 'view'}">
					<img  id="lockUnlockImgInternalSupplied" onclick="changeIcon('internalSupplied')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 161px; left: 75px;"		border="0" title="">
					</c:if>
					<td width="120px"><input type="text" class="htmlTextNumeric"
						name="balmaintenance.suppliedInternalBalanceAsString"
						value="${balmaintenance.suppliedInternalBalanceAsString}"
						onchange="checkBalance(this);"
						style="width:144px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
					</td>
					<td width="150px">&nbsp;<b><fmt:message key="changeBalance.preditInternal" /></b></td>
					<td width="105px"><input type="text" class="htmlTextNumeric"
						value="${balmaintenance.calculatedIntPredBalanceAsString}"
						name="balmaintenance.calculatedIntPredBalanceAsString"
						style="width:144px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
					</td>
				</tr>
			</table>
			<table>
				<tr>
					<td width="38px">&nbsp;<b><fmt:message key="changeBalance.source" /></b></td>
					<td width="41px">&nbsp;</td>
					<td width="140px"><input type="text" class="htmlTextAlpha"
						name="balmaintenance.internalBalanceSource"
						value="${balmaintenance.internalBalanceSource}"
						style="width:288px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
					</td>
					<td width="36px">&nbsp;</td>
					<c:if test="${requestScope.selectedMode != 'view'}">
					<img  id="lockUnlockImgInterBalEodDate" onclick="changeIcon('interBalEodDate')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 188px; left: 400px;"		border="0" title="">
					</c:if>
					<td width="209px"><input type="text" class="htmlTextNumeric"
						name="balmaintenance.internalBalanceEodDateAsString"
						value="${balmaintenance.internalBalanceEodDateAsString}"
						titleKey="tooltip.balance.interBalEodDate"
						onchange="checkSuppliedInterBalance();"
						style="width:80px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
					&nbsp; <A name="datelink" ID="datelink" tabindex="28"
				onClick="interBalEodDateClick();">
			<img title='<fmt:message key="tooltip.interBalEodDate"/>'
				src="images/calendar-16.gif"> </A></td>

				</tr>
			</table>


				<table border="0" width="570">
					<tr>
						<td width="67px">&nbsp;<b><fmt:message key="changeBalance.external" /></b></td>
						<td width="17px">&nbsp;</td>
						<c:if test="${requestScope.selectedMode != 'view'}">
						<img  id="lockUnlockImgExternalSupplied" onclick="changeIcon('externalSupplied')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 214px; left: 75px;"		border="0" title="">
						</c:if>
						<td width="120px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.suppliedExternalBalanceAsString"
							value="${balmaintenance.suppliedExternalBalanceAsString}"
							style="width:144px;"
							onchange="checkBalance(this);"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
						<td width="150px">&nbsp;<b><fmt:message key="changeBalance.preditExternal" /></b></td>

						<td width="105px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.calculatedExtPredBalanceAsString"
							value="${balmaintenance.calculatedExtPredBalanceAsString}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
					</tr>
				</table>
				<table>
					<tr>
						<td width="34px">&nbsp;<b><fmt:message key="changeBalance.source" /></b></td>
						<td width="45px"></td>
						<td width="140px"><input type="text" class="htmlTextAlpha"
							name="balmaintenance.externalBalanceSource"
							value="${balmaintenance.externalBalanceSource}"
							style="width:288px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
							<td width="35px">&nbsp;</td>
							<c:if test="${requestScope.selectedMode != 'view'}">
							<img  id="lockUnlockImgExtBalEodDate" onclick="changeIcon('extBalEodDate')" width="18" height="18" src='images/lock.png'	style="position: absolute; top: 243px; left: 400px;"		border="0" title="">
						    </c:if>
						<td width="209px"><input type="text" class="htmlTextNumeric"
										name="balmaintenance.externalBalanceEodDateAsString"
										value="${balmaintenance.externalBalanceEodDateAsString}"
							            titleKey="tooltip.balance.extBalEodDate"
										onchange="checkSuppliedExtBalance();" style="width:80px;"
										<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
              &nbsp; <A name="datelink2" ID="datelink2" tabindex="28"
				onClick="extBalEodDateClick();">
			<img title='<fmt:message key="tooltip.extBalEodDate"/>'
				src="images/calendar-16.gif"> </A></td>
					 </tr>
				</table>
				</table>
				</fieldset>
				</div>
				<!------------------------------Third fieldset------------------------------------------->
				<div style="left: 3px; top: 4px;height: 76px;">
				<fieldset style="width: 570px; border: 2px groove; ">
				<legend> <fmt:message key="changeBalance.legend.backValAdj" />
				</legend>
				<table width="570" border="0" cellpadding="0" cellspacing="1"
					height="59">
					<tr height="24px">
						<td width="94px">&nbsp;<b><fmt:message key="changeBalance.internal" /></b></td>
						<td width="3px">&nbsp;</td>
						<td width="196px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.bvForecastAdjustAsString"
							value="${balmaintenance.bvForecastAdjustAsString}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
						<td width="125px">&nbsp;<b><fmt:message key="changeBalance.lastMov" /></b></td>
						<td width="153px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.bvForecastAdjustLastId"
							value="${balmaintenance.bvForecastAdjustLastId}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
					</tr>
					<tr height="24px">
						<td width="94px">&nbsp;<b><fmt:message key="changeBalance.external" /></b></td>
						<td width="3px">&nbsp;</td>
						<td width="145px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.bvExternalAdjustAsString"
							value="${balmaintenance.bvExternalAdjustAsString}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
						<td width="125px">&nbsp;<b><fmt:message key="changeBalance.lastMov" /></b></td>
						<td width="93px"><input type="text" class="htmlTextNumeric"
							name="balmaintenance.bvExternalAdjustLastId"
							value="${balmaintenance.bvExternalAdjustLastId}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
					</tr>
					</tr>
				</table>
				</fieldset>
				</div>
				<!------------------------------Fourth fieldset------------------------------------------->
				<div style="left: 3px; top: 4px;height: 76px;">
				<fieldset style="width: 570px; border: 2px groove; ">
				<legend> <fmt:message key="changeBalance.legend.workingBal" />
				</legend>
				<table width="570" border="0" cellpadding="0" cellspacing="1"
					height="59">
					<tr height="24px">
						<td width="94px">&nbsp;<b><fmt:message key="changeBalance.internal" /></b></td>
						<td width="3px">&nbsp;</td>
						<td width="196px"><c:choose>
						<c:when test="${requestScope.selectedMode != 'view'}">
							<input type="text" class="htmlTextNumeric"
								name="balmaintenance.workingForecastSODAsString"
								value="${balmaintenance.workingForecastSODAsString}"
								style="width:144px;" titleKey="tooltip.internalSOD"
								onchange="checkBalance(this);"  maxlength="24"/>
						    </c:when>
    					<c:otherwise>
							<input type="text" class="htmlTextNumeric"
								name="balmaintenance.workingForecastSODAsString"
								value="${balmaintenance.workingForecastSODAsString}"
								style="width:144px;"
								<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
						    </c:otherwise>
						</c:choose></td>
						<td width="125px"><b><fmt:message key="type" /></b></td>
						<td width="153px"><input type="hidden" class="htmlTextNumeric"
							name="balmaintenance.workingForecastSODType"
							value="${balmaintenance.workingForecastSODType}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /> <input type="text"
							class="htmlTextAlpha"
							name="balmaintenance.workingForecastSODTypeAsString"
							value="${balmaintenance.workingForecastSODTypeAsString}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>
					</tr>
					<tr height="24px">
						<td width="94px">&nbsp;<b><fmt:message key="changeBalance.external" /></b></td>
						<td width="3px">&nbsp;</td>
						<td width="197px"><input type="hidden" class="htmlTextNumeric"
							name="balmaintenance.workingExternalSOD" value="${balmaintenance.workingExternalSOD}" style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /> <input type="hidden"
							class="htmlTextNumeric"
							name="balmaintenance.workingForecastSOD"
							value="${balmaintenance.workingForecastSOD}" style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
						<c:choose>
    					<c:when test="${requestScope.selectedMode != 'view'}">
							<input type="text" class="htmlTextNumeric"
								name="balmaintenance.workingExternalSODAsString"
								value="${balmaintenance.workingExternalSODAsString}"
								style="width:144px;" titleKey="tooltip.externalSOD"
								onchange="checkBalance(this);" maxlength="24"/>
						    </c:when>
    						<c:otherwise>
							<input type="text" class="htmlTextNumeric"
								name="balmaintenance.workingExternalSODAsString"
								value="${balmaintenance.workingExternalSODAsString}"
								style="width:144px;"
								<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  />
						</c:otherwise></c:choose></td>
						<td width="125px"><b><fmt:message key="type" /></b></td>

						<td width="80px"><input type="hidden" class="htmlTextNumeric"
							name="balmaintenance.workingExternalSODType" value="${balmaintenance.workingExternalSODType}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /> <input type="text"
							class="htmlTextAlpha"
							name="balmaintenance.workingExternalSODTypeAsString"
							value="${balmaintenance.workingExternalSODTypeAsString}"
							style="width:144px;"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  /></td>

					</tr>
					</tr>
				</table>
				</fieldset>
				</div>
				<!--------------------------------------end of Fourth fieldset---------------------------------->
				</div>
				</div>
			<div id="BookCode"
					style="position: absolute; left: 535; top: 470; width: 70; height: 39px; visibility: visible;">
				<table width="60" cellspacing="0" cellpadding="0" height="39"
					border="0">
					<tr>
						<td align="Right"><c:choose>
    					<c:when test="${requestScope.selectedMode == 'view'}">
							<a tabindex="4" href=#
								onclick="javascript:openWindow(buildPrintURL('print','Change Start of Day Balance Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a>
						    </c:when>
    					<c:otherwise>
							<a tabindex="4" href=#
								onclick="javascript:openWindow(buildPrintURL('print','View Start of Day Balance Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a>

						</c:otherwise></c:choose></td>
						<td align="right" valign="middle" id="Print"><a tabindex="4"
							onclick="printPage();" onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							title='<fmt:message key="tooltip.printScreen"/>'></a></td>
					</tr>
				</table>
				</div>
				<c:choose>
    			<c:when test="${requestScope.selectedMode != 'view'}">

					<div id="ddimagebuttons" color="#7E97AF"
						style="position: absolute; border: 2px outset; left: 20; top: 470; width: 582px; height: 39px; visibility: visible;">

					<div id="Balance"
						style="position: absolute; left: 6; top: 5; width: 400; height: 15px; visibility: visible;">
					<table width="140" border="0" cellspacing="0" cellpadding="0"
						height="20">
						<tr>

							<td id="savebutton"><a
								title='<fmt:message key="tooltip.save"/>' tabIndex="2"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onclick="javascript:submitForm('save')"><fmt:message key="button.save" /></a></td>
							<td id="cancelbutton"><a
								title='<fmt:message key="tooltip.cancel"/>' tabIndex="3"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel" /></a></td>
						</tr>
					</table>
					</div>
					</div>
						</c:when>
    					<c:otherwise>

					<div id="ddimagebuttons" color="#7E97AF"
						style="position: absolute; border: 2px outset; left: 20; top: 470; width: 582px; height: 39px; visibility: visible;">
					<div id="Balance"
						style="position: absolute; left: 6; top: 5; width: 400; height: 15px; visibility: visible;">

					<table width="70" border="0" cellspacing="0" cellpadding="0"
						height="20">
						<tr>
							<td id="closebutton"><a
								title='<fmt:message key="tooltip.close"/>' tabIndex="2"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)" onclick="window.close()"><fmt:message key="button.close" /></a></td>
						</tr>
					</table>
					</div>
					</div>
				</c:otherwise></c:choose>
				</form>
</body>
</html>