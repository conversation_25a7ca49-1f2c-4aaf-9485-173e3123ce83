<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.CorrespAccountSummary"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@page import="org.swallow.maintenance.model.CorrespondentAcct"%>
<%@page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title>
	<fmt:message key="correspondentaccountmaintenance.title.mainWindow" />
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript">
var filterValues=new Array();
var filterstatus="${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
 var maxPage = "${requestScope.maxPage}";
 var currPage = '${requestScope.currentPage}';
 var totalCount = '${totalCount}';
 var appName = "<%=SwtUtil.appName%>";
 var requestURL = new String('<%=request.getRequestURL()%>');
 var idy = requestURL.indexOf('/'+appName+'/');
 requestURL=requestURL.substring(0,idy+1) ;

function disableAllButtons () {
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort () {
	updateColors();
	disableAllButtons();
}
var currentSort = "${requestScope.selectedSort}";
var sortingValues = currentSort.split("|");
var sortedValues = new Array();
if(currentSort != "") {
	sortedValues[0] = sortingValues[0];
	sortedValues[1] = sortingValues[1];	
} else {
	sortedValues[0] = "-1";
	sortedValues[1] = "true";	
}



function init () {
	var currentFilterValues = ["All", "All", "All", "All", "All"];
	var divElement = document.getElementById("dropdowndiv_1");
	var selectElement = document.forms[0].elements["correspondentAcct.id.messageType"];
	var idElement = document.forms[0].elements["messageType"];
	var descElement ="";
	var arrowElement = document.forms[0].elements["dropdowndiv_1"];
	var idLength = 12;
	var descLength = 30;
	var messageTypeBox = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	xl = new XLSheet("correspondentAcctDetails","table_2", ["String","String", "String", "String", "String"],"22222","true",currentFilterValues,sortedValues,"ddscrolltable");
	// true changed to false
	xl.onsort = xl.onfilter = onFilterandSort;

	var dropBox1 = new SwSelectBox(document.getElementById('entityId'), document.getElementById('entityDesc'));
	var dropBox2 = new SwSelectBox(document.getElementById('currencyCode'),document.getElementById('currencyDesc'));
 
	highlightTableRows("correspondentAcctDetails");

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML; 
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
			
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		if(menuEntityCurrGrpAccess=='0') 
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		<%}%>
		
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		<%}%>
	
	if (filterstatus !="") {
		var filterStatus1 = filterstatus.split(",");
		if (document.getElementById("correspondentAcctDetails").innerText != "")
			xl.setFilterStatus (filterStatus1);
	 }
	 if (sortDescending !="" && sortDescending !="null" && sortDescending == "true") {	
		xl.dataTable.defaultDescending = true;
// 		if(document.getElementById("correspondentAcctDetails").innerText != "")
// 			xl.dataTable.doManualSorting (sortStatus);
	 }

     if (sortDescending !="" && sortDescending !="null" && sortDescending == "false") {	
		if(document.getElementById("correspondentAcctDetails").innerText != "")
			xl.dataTable.doManualSorting (sortStatus);	
	 }

	xl.onsort = xl.onfilter = onFilterandSort;
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	ShowErrMsgWindow('${actionError}');
}

function entitysubmitForm (methodName)
{
document.forms[0].elements["correspondentAcct.id.currencyCode"].value="All";
document.forms[0].elements["correspondentAcct.id.messageType"].value="";
var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
if(searchMessageType == "M")
{
document.forms[0].elements["correspondentAcct.otherMessageType"].value = "";
}
document.forms[0].method.value = methodName;
document.forms[0].submit();
}


function buildAddCorrespondentAcct (methodName) {
	maintainSortFilterStatus();
	var param = 'correspondentacctmaintenance.do?method='+methodName+'&entityId='+document.getElementById('entityId').options[document.getElementById('entityId').selectedIndex].value;
	
	param += '&messageType='+encodeURIComponent(document.getElementById('selectedMessageType').value);
	param += '&currencyCode='+document.getElementById('selectedCurrencyCode').value;
	
	param += '&entityName=' +document.getElementById('entityDesc').innerText;
	return  param;
}
function submitForm (methodName) {
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
function changeCorrespondentAcct (methodName) {
	maintainSortFilterStatus();
	var param = 'correspondentacctmaintenance.do?method='+methodName+'&entityId='+document.getElementById('entityId').options[document.getElementById('entityId').selectedIndex].value;
	
	param += '&messageType='+encodeURIComponent(document.getElementById('selectedRowMessageType').value);
	param += '&currencyCode='+document.getElementById('selectedRowCurrencyCode').value;
	param += '&corresAccId='+encodeURIComponent(document.getElementById('selectedCorresAccId').value);
	
	param += '&entityName=' +document.getElementById('entityDesc').innerText;
	return  param;
}
function submitDeleteForm (methodName) {
	document.getElementById('method').value = methodName;
	document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["correspondentAcct.id.currencyCode"].value;
	document.forms[0].selectedMessageType.value = document.forms[0].elements["correspondentAcct.id.messageType"].value;
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true) {
		maintainSortFilterStatus ();
		document.forms[0].submit ();
	}
}

function onSelectTableRow (rowElement, isSelected) {
	var hiddenElements = rowElement.getElementsByTagName("input");
	document.forms[0].selectedEntityId.value = document.forms[0].elements["correspondentAcct.id.entityId"].value;
	document.forms[0].selectedRowMessageType.value = hiddenElements[0].value;
	document.forms[0].selectedRowCurrencyCode.value = hiddenElements[1].value;
	document.forms[0].selectedCorresAccId.value = hiddenElements[2].value;


	var flag=currencyAccessConfirm(hiddenElements[1].value,document.forms[0].elements["correspondentAcct.id.entityId"].value)			

	if (flag=="true" && isSelected && document.forms[0].menuAccessId.value  == 0) {
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else {
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
}


function currencyAccessConfirm(currency,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/defaultacctmaintenance.do?method=currencyAccessConfirm";
	sURL = sURL + "&currency="+currency;
	sURL = sURL + "&entityId="+entity;
	oXMLHTTP.open( "POST", sURL, false );	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}


function maintainSortFilterStatus () {
	document.forms[0].selectedSortStatus.value = "";
	document.forms[0].selectedSortDescending.value ="";
	document.forms[0].selectedFilterStatus.value="";
	var sortColumn = xl.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;

	var sSortDescending =  xl.dataTable.descending;		
	document.forms[0].selectedSortDescending.value = sSortDescending ;
	
	var filterArr = new Array(5);

	if(document.getElementById("correspondentAcctDetails").innerText != "")
	filterArr = xl.getFilterStatus();

	for(var idy = 0 ; idy < filterArr.length-1; ++idy)	
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;
}
function getRadioButtonValue(button){
	var d = button;
	var len = d.length;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		{
		 return d[i].value;
		}
	}
	if(i == len)
	 return "null";
	}

function showOtherMessageType(){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
		document.forms[0].elements["correspondentAcct.otherMessageType"].value = "";
		submitForm('display');
	}
	else if(searchMessageType == "M")
	{
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
		document.forms[0].elements["correspondentAcct.id.messageType"].value = "";
	}	
}
function confirmClose(methodName)
{
	self.close();
}
window.onload = function () {
	setParentChildsFocus();
	setFocus(document.forms[0]);
	init ();
	ShowErrMsgWindow('${actionError}');
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
	}
	else if(searchMessageType == "M")
	{
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
	}
}
window.onmousemove = reportMove;
window.onunload = call;
/**
 * Method to filtered values
 * @param index
 * @param value
 * @param action
 */
function optionClick_server_filter_JSP(index,value,action){
	var	sortColum=index;
	var sortDesc=value;
	document.forms[0].selectedSort.value=sortColum + "|" +sortDesc;
	document.forms[0].method.value = "display";    
	//document.forms[0].currentPage.value = 1;			
	document.forms[0].submit();
}
function clickLink(goToPageNo) {
			var currPage = '${requestScope.currentPage}';
			var menuAccessId = '${requestScope.menuAccessId}';
			 var url='correspondentacctmaintenance.do?';		
			 url +='&goToPageNo='+goToPageNo+'&entityId='+document.forms[0].elements['correspondentAcct.id.entityId'].value+'&currencyCode='+document.forms[0].elements['correspondentAcct.id.currencyCode'].value;	
			 url +='&currentPage=' + currPage;
			 url +='&maxPages=' + maxPage;
			 url +='&totalCount=' + totalCount;
			 url +='&selectedSort=' + currentSort
			 url +='&correspondentAcct.id.messageType=' + document.forms[0].elements['correspondentAcct.id.messageType'].value
			 url +='&correspondentAcct.searchMessageType=' + document.forms[0].elements['correspondentAcct.searchMessageType'].value
			 url +='&messageType=' + document.forms[0].elements['messageType'].value
			 url +='&correspondentAcct.otherMessageType=' + encodeURIComponent(document.forms[0].elements['correspondentAcct.otherMessageType'].value)
 			 url +='&menuAccessId='+menuAccessId+'&method=display';
 			 submitFormFromURL(url,window); 
		}
function validatePageNumber(strObject)
{
    var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0)) 
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		clickLink(strObject.value);
	}
	else
	{
		
		alert('<fmt:message key="party.alert.pagination"/>');
		
		strObject.value = currPage;
	}
}
	

</script>
<style>
	
	div.padded {
		padding: 5px;
	}
	
	#container-icons {
		position: absolute;
		right: 20px;
		top: 5px;
	}
	
	
</style>
</head>

<body>
<form action="correspondentacctmaintenance.do">
	<input name="method" type="hidden" value="display"/>
	<input name="selectedAccountId" type="hidden" value="">
	<input name="selectedCorresAccId" type="hidden" value="">
	<input name="selectedEntityId" type="hidden" value="">
	<input name="selectedCurrencyCode" type="hidden" value="">
	<input name="currentPage" type="hidden" value="">
	<input name="selectedMessageType" type="hidden" value="">
	<input name="selectedRowCurrencyCode" type="hidden" value="">
	<input name="selectedRowMessageType" type="hidden" value="">
	
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="menuAccessId" type="hidden" value=""/>
	<!-- Start:Code Modified by Sudhakar on 24-Jun-2011 for Mantis 1349:Increase Account name field in some screens -->
	<div id="dropdowndiv_1"
		style="position: absolute; width: 119px; left: 192px; top: 102px; visibility: hidden; z-index: 100"
		class="swdropdown">
		<select class="htmlTextFixed" id="correspondentAcct.id.messageType"
		name="correspondentAcct.id.messageType" size="10" style="width:119px;z-index:99"
		onchange="submitForm('display');">
		<c:forEach items="${requestScope.messagetypes}" var="messagetype">
               <option
                  value="${messagetype.value}"
                  <c:if test="${correspondentAcct.id.messageType == messagetype.value}">selected="selected"</c:if>>
                  ${messagetype.label}
               </option>
		</c:forEach>
		</select>
		</div>

	<div class="block padded" style="position: absolute; left: 20px; top: 20px; width: 935px; height: 120px; border: 2px outset;"
		color="#7E97AF">
		<table border="0" cellpadding="0" cellspacing="0" width="921px">
			<tr height="25">
				<td width="150px"><b><fmt:message key="entity.id"/></b></td>
				<td width="28px">&nbsp;</td>
				<td width="160px">
				
					<select id="entityId" name="correspondentAcct.id.entityId"
					onchange="entitysubmitForm('display')" titleKey="tooltip.selectEntity"
					style="width:140px" tabindex="1">
						<c:forEach items="${requestScope.entities}" var="entity">
               			<option
						  value="${entity.value}"
						  <c:if test="${correspondentAcct.id.entityId == entity.value}">selected="selected"</c:if>>
						  ${entity.label}
               			</option>
						</c:forEach></select>
				</td>
				<td width="20">&nbsp;</td>
				<td width="642"><span id="entityDesc" name="entityDesc" class="spantext">${requestScope.entityName}</span></td>
				<!-- End:Code Modified by Sudhakar on 24-Jun-2011 for Mantis 1349:Increase Account name field in some screens -->
			</tr>
			<tr height="25">
				<td width="150px"><b><fmt:message key="correspondentaccountmaintenance.messageType"/></b></td>
				<td width="620px" colspan="4">
					<table align="left">
						<tr>
							<td width="18px">&nbsp;</td>
							<td width="120px">
								<input type="radio"  tabindex="2" id="6" name="correspondentAcct.searchMessageType" ${correspondentAcct.searchMessageType == 'P' ? 'checked="checked"' : ''} value="${'P'}"
								 style="width:13;" titleKey="correspondentaccountmaintenance.predefinedMessageType" onclick="showOtherMessageType();"  />
								<label title='<fmt:message key="correspondentaccountmaintenance.predefinedMessageType"/>'><fmt:message key="correspondentaccountmaintenance.predefinedMessageType"/></label>
							</td>
							<td width="20px">&nbsp;</td>
							<td width="120px">
								<input type="radio"  tabindex="3" id="6" name="correspondentAcct.searchMessageType" ${correspondentAcct.searchMessageType== 'M' ? 'checked="checked"' : ''} value="${'M'}"
								style="width:13;" titleKey="correspondentaccountmaintenance.manualMessageType" onclick="showOtherMessageType();"  />
								<label title='<fmt:message key="correspondentaccountmaintenance.manualMessageType"/>'><fmt:message key="correspondentaccountmaintenance.manualMessageType"/></label>
							</td>
							<td width="340px">&nbsp;</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr height="25">
				<td width="150px">&nbsp;</td>
				<td width="620px" colspan="4">
					<table align="left">		
						<tr>
							<td width="18px">&nbsp;</td>
							<td>
							<div id="predefinedMsgTypeDiv" style="height:25;top:60">
								<input  readonly="readonly"  name="messageType" class="textAlpha"	style="width: 118px;height: 22px;" title='<fmt:message key = "tooltip.selectMessageType"/>'> 
								<input id="dropdowndiv_1" tabindex="4"	title='<fmt:message key = "tooltip.selectMessageType"/>' type="button" value="...">
								&nbsp;&nbsp;
							</div>
							<div id="manualMsgTypeDiv" style="height:25;top:60">
							<input type="text"  name="correspondentAcct.otherMessageType" class="htmlTextAlpha" value="${correspondentAcct.otherMessageType}"
							titleKey="tooltip.manualMessageType" style="width:140px;" tabindex="4" onchange="submitForm('display');" onkeydown="if (event.keyCode == 13)submitForm('display');" maxlength="30" />
							</div>
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr height="25">
				<td width="150px"><b><fmt:message key="correspondentaccountmaintenance.currencyCode"/></b></td>
				<td width="28px">&nbsp;</td>
				
				<td width="160px">
					<select id="currencyCode"  name="correspondentAcct.id.currencyCode"
					onchange="submitForm('display')" titleKey="tooltip.selectCurrencyId"
					style="width:140px" tabindex="5">
					<c:forEach items="${requestScope.currencies}" var="ccy">
					   <option
						  value="${ccy.value}"
						  <c:if test="${correspondentAcct.id.currencyCode == ccy.value}">selected="selected"</c:if>>
						  ${ccy.label}
					   </option>
					</c:forEach></select>
				</td>
				<td width="20">&nbsp;</td>
				<td width="442"><span id="currencyDesc" name="currencyDesc" class="spantext"></span></td>
			</tr>
		</table>
		<div  id="pageSummaryList" style="position:absolute; left:666px; top:1px; width:280px; height:25px;border:2px;">
	<c:if test="${requestScope.hidePagination != 'true'}">
    <table border="0" cellpadding="0" cellspacing="1" height="25px">
        <tr height="25px">
            <%
                String currentPageAsString = (String) request.getAttribute("currentPage");
                String maxPageAsString = (String) request.getAttribute("maxPage");
                int currentPage = Integer.parseInt(currentPageAsString);
                int countPageNo = 1;
                int maxPage = Integer.parseInt(maxPageAsString);
            %>
            <c:forEach  items="${requestScope.pageSummaryList}" var="pageSummaryList">
			<% if( countPageNo <=12) {++countPageNo; %>
			<td height="34"><b>Page</b>&nbsp;&nbsp;
	  		<input class="htmlTextNumeric" title='<fmt:message key="tooltip.enterPageNo"/>' id="pageNoText" name="pageNo" size="5"  style="height: 21px;" align="top" value="<%=currentPageAsString %>" onchange="validatePageNumber(this);" onkeydown="if (event.keyCode == 13)validatePageNumber(this);">
	  		</td>
	  		<td>
			   	<c:choose>
    <c:when test="${'true' == requestScope.nextEnabled}">

				 <a href="#"  onclick="clickLink(-1);">
			    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="padding-top:9px; height: 11px;"></img><br />
			     </a>

    </c:when>
    <c:otherwise>
        <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="padding-top:9px; height: 11px;"></img><br />
    </c:otherwise>
</c:choose>
		       	<c:choose>
    <c:when test="${'true' == requestScope.prevEnabled}">

			   <a href="#"  onclick="clickLink(-2);">
		    	<img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style=" padding-bottom:9px; height: 11px;"></img><br />
				</a>

    </c:when>
    <c:otherwise>
        <img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="padding-bottom:9px; height: 11px;"></img><br />
    </c:otherwise>
</c:choose>
		    </td>
		    <td style="text-align: center;">&nbsp;&nbsp;<fmt:message key="genericDisplayMonitor.labelOf"/>&nbsp;&nbsp;
		    <input class="textAlpha" style="background:transparent;border: 0; height:17;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
		  	</td>
			<%}%>
		</c:forEach>
        </tr>
    </table>
</c:if>
	</div>
	</div>
	
	<!-- results -->
	 <!-- Start:Code Modified by Sudhakar on 24-Jun-2011 for Mantis 1349:Increase Account name field in some screens -->
	<div class="block" style="height:430px;width:937px;position: absolute; border: 0px outset; left: 20px; top: 155px;" >
	<div id="CorrespondentAcctMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:915px; height:10px;">
		<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="914" border="0" cellspacing="1" cellpadding="0"  height="20">
			<thead>
				<tr>
					<td width="130" height="20px" align="left" style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortMessageType"/>'><b><fmt:message key="correspondentaccountmaintenance.messageType"/></b></td>
					<td width="70" height="20px" align="left" title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="correspondentaccountmaintenance.currencyCode"/></b></td>
					<td width="180" height="20px" align="left" title='<fmt:message key="tooltip.sortCorrespondentAccId"/>'><b><fmt:message key="correspondentaccountmaintenance.correspondentAccId"/></b></td>
					<td width="220" height="20px" align="left" title='<fmt:message key="tooltip.sortAccountId"/>'><b><fmt:message key="correspondentaccountmaintenance.accountId"/></b></td>
					<td width="312" height="20px" align="left" title='<fmt:message key="tooltip.sortAccountName"/>'><b><fmt:message key="correspondentaccountmaintenance.accountName"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; width:934px; height:425px; overflowY:scroll; top:1px;">
		<div id="CorrespondentAcctMaintenance" style="position:absolute;z-index:99;left:1px; top:20px; width:898px; height:10px;">
		<table class="sort-table" id="correspondentAcctDetails" width="916" border="0" cellspacing="1" cellpadding="0" height="405">
			<tbody>
				<c:set var="count" value="0" />
				<c:forEach var="correspondentAcctDetails" items="${requestScope.listViewData}">
					<c:set var="correspondentAcct1" value="${correspondentAcctDetails}" />

					<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
						<input type="hidden" value="${correspondentAcctDetails.id.messageType}" name="id.messageType"  disabled="true"/>
						<input type="hidden" value="${correspondentAcctDetails.id.currencyCode}" name="id.currencyCode" disabled="true" />
						<input type="hidden" value="${correspondentAcctDetails.id.corresAccId}" name="id.corresAccId"  disabled="true" />
						<td width="120" align="left" style="word-break: break-all;word-wrap: break-word;" nowrap="">
							${fn:escapeXml(correspondentAcct1.id.messageType)}&nbsp;
						</td>
						<td width="70" align="left">
							${fn:escapeXml(correspondentAcct1.id.currencyCode)}&nbsp;
						</td>
						<td width="180" align="left" style="word-break: break-all;word-wrap: break-word;" nowrap="">
							${fn:escapeXml(correspondentAcct1.id.corresAccId)}&nbsp;
						</td>
						<td width="220" align="left">
							${fn:escapeXml(correspondentAcct1.accountId)}&nbsp;
						</td>
						<td width="312" align="left">
							${fn:escapeXml(correspondentAcct1.accountName)}&nbsp;
						</td>
					</tr>

					<c:set var="count" value="${count + 1}" />
				</c:forEach>
			</tbody>
			<tfoot><tr><td colspan="5" ></td></tr></tfoot>
		</table>
		</div>
	</div>
	</div>
	
	<!-- buttons -->
	<div class="block padded" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:598px; width:935px; height:40px; visibility:visible;">
		<div id="ddimagebuttons">
			<span id="addbutton"></span>
			<span id="changebutton"></span>
			<span id="deletebutton"></span>
			<span id="closebutton" title='<fmt:message key="tooltip.close"/>'>		
				<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" tabindex="9"><fmt:message key="button.close"/></a>			
			</span>
		</div>
		<div id="container-icons" >
			<span><a tabindex="10" href=# onclick="javascript:openWindow(buildPrintURL('print','Correspondent Account Alias Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a></span>
			
			<span id="Print"><a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a></span>
		</div>
		<div style="display:none">
			<span id="addenablebutton"><a title='<fmt:message key="tooltip.addNewCorrespondentAccount"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddCorrespondentAcct('add'),'accountsmaintenancechangewindow','left=50,top=150,width=850,height=290,toolbar=0, resizable=yes, status=yes, scrollbars=no','true')" tabindex="6"><fmt:message key="button.add"/></a></span>
			<span id="adddisablebutton"><a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a></span>
			<span id="changeenablebutton"><a title='<fmt:message key="tooltip.changeSelCorrespondentAcct"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(changeCorrespondentAcct('change'),'accountsmaintenancechangewindow','left=50,top=150,width=850,height=290,toolbar=0, resizable=yes, status=yes, scrollbars=no','true')" tabindex="7"><fmt:message key="button.change"/></a></span>
			<span id="changedisablebutton"><a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a></span>
			<span id="deleteenablebutton"><a title='<fmt:message key="tooltip.deleteSelCorrespondentAcct"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')" tabindex="8"><fmt:message key="button.delete"/></a></span>		
			<span id="deletedisablebutton"><a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a></span>
		</div>
		<!-- End:Code Modified by Sudhakar on 24-Jun-2011 for Mantis 1349:Increase Account name field in some screens -->
	</div>
</form>
</body>
</html>
