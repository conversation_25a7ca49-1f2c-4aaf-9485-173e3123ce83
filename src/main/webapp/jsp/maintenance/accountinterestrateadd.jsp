<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.method != 'add'}">
	<title><fmt:message key="title.rates.change" /></title>
    </c:when>
    <c:otherwise>
	<title><fmt:message key="title.rates.add" /></title>
    </c:otherwise>
</c:choose>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
// get the duplicate entry status from the request
var dupStatus = "${requestScope.dupstatus}";
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray=["*"];
var dateFormatValue = '${sessionScope.CDM.dateFormatValue}'; /*  Calendar control added*/
var cal = new CalendarPopup("caldiv", true, "calFrame");
// var dateFormat used to validate the date in the form
var dateFormat = '${sessionScope.CDM.dateFormat}';
cal.setCssPrefix("CAL"); /*Calendar component X & Y location changed*/
cal.offsetX = -70;
cal.offsetY = -50;
<c:if test="${'yes'== requestScope.parentFormRefresh}">
window.opener.document.forms[0].method.value = "displayAccInterestRate";
window.opener.document.forms[0].submit();
self.close(); 
</c:if>


function reset(){
<c:if test="${'true'== requestScope.dupstatus}">
ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.templateOption.recordExist"/>', null, okCall);
	
</c:if>
}
function okCall() {
	document.forms[0].elements['acctInterestRate.interestDateRateAsString'].value="";
	document.forms[0].elements['acctInterestRate.creditRate'].value="";
	document.forms[0].elements['acctInterestRate.overdraftRate'].value="";
}
var creditRate, overdraftRate;

function submitForm(methodName) {
    // check the duplicate entry status and set the creditRate & overdraftRate value as empty.
    if (dupStatus == "true") {
        creditRate = "";
        overdraftRate = "";
    }
    // Start :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
	// Displays "infinite" for infinite numbers
    //Validates the creditValue
    var creditValue = validateFieldAccRate(document.forms[0].elements['acctInterestRate.creditRate'], 'acctInterestRate.creditRate', 'negativeWithDigits');
    if (creditValue) {
        var overdraftValue = validateFieldAccDraft(document.forms[0].elements['acctInterestRate.overdraftRate'], 'acctInterestRate.overdraftRate', 'negativeWithDigits');
        //Validates the overdraftValue
        if (overdraftValue) {
            if (creditRate != document.forms[0].elements['acctInterestRate.creditRate'].value || overdraftRate != document.forms[0].elements['acctInterestRate.overdraftRate'].value || creditRate == "" || overdraftRate == "") {
            	// Adds the zero before the decimal point
            	document.forms[0].elements['acctInterestRate.creditRate'].value = formatPrecisionDecimal(document.forms[0].elements['acctInterestRate.creditRate'].value);
            	document.forms[0].elements['acctInterestRate.overdraftRate'].value = formatPrecisionDecimal(document.forms[0].elements['acctInterestRate.overdraftRate'].value);
    // End :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
	// Displays "infinite" for infinite numbers        	            	
                if (methodName == "updateAcctInterestRate") {
                    if (validateForm(document.forms[0])) {
                        document.forms[0].method.value = methodName;
                        document.forms[0].elements["acctInterestRate.interestDateRateAsString"].disabled = "";
                        document.forms[0].submit();
                    }
                } else {
                    if (validateForm(document.forms[0])) {
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                }
            } else {
                self.close();
            }
        } else {
            document.forms[0].elements['acctInterestRate.overdraftRate'].focus();
        }
    } else {
        document.forms[0].elements['acctInterestRate.creditRate'].focus();
    }
}

function validateFieldAccRate(strField, strLabel, strPat, maxValue, minValue) {
	var creditRate = parseInt(strField.value);
    if(!isNaN(creditRate) && creditRate > 0) {
    	ShowErrMsgWindowWithBtn('','<fmt:message key="alert.currencyExchangeRate.invalidCreditRate"/>', null);
//     	 strField.focus();
//     	 strField.select();
// 	      return false;
  }else
	// If we here, then validate with common validation method
	return validateField(strField, strLabel, strPat, maxValue, minValue);
}
function validateFieldAccDraft(strField, strLabel, strPat, maxValue, minValue) {
	 var overDraft = parseInt(strField.value);
 if (!isNaN(overDraft) && overDraft <0) {
	 ShowErrMsgWindowWithBtn('', '<fmt:message key="alert.currencyExchangeRate.invalidDraftRate"/>', null);
// 	  strField.focus();
// 	  strField.select();
//       return false;
 }else
	// If we here, then validate with common validation method
	return validateField(strField, strLabel, strPat, maxValue, minValue);
}
// Start :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
// Displays "infinite" for infinite numbers
// Adds the zero before the decimal point
function formatPrecisionDecimal(decimalValue){
	if (decimalValue.indexOf(".") == 0)
    	decimalValue = "0" + decimalValue;
	/* Start: Code Added for Mantis 2130 by M.BOURAOUI on 27/11/2012 */
	if (decimalValue.indexOf("-.") == 0) {
		 decimalValue=decimalValue.replace("-", "-0");
		}
	/* End: Code Added for Mantis 2130 by M.BOURAOUI on 27/11/2012 */
    return decimalValue;
}
// End :Nithiyananthan 23/12/2011 Mantis 1559: Account Interest Rate :
// Displays "infinite" for infinite numbers
function validateForm(objForm) {
    var elementsRef = new Array(2);
    elementsRef[0] = objForm.elements["acctInterestRate.creditRate"];
    elementsRef[1] = objForm.elements["acctInterestRate.overdraftRate"];
    elementsRef[2] = objForm.elements["acctInterestRate.interestDateRateAsString"];
    return validate(elementsRef);
}

function bodyOnLoad() {
    creditRate = document.forms[0].elements['acctInterestRate.creditRate'].value;
    overdraftRate = document.forms[0].elements['acctInterestRate.overdraftRate'].value;
    document.forms[0].callerMethod.value = window.opener.screenStatus;
    document.forms[0].relatedToaccountId.value = window.opener.document.forms[0].relatedToaccountId.value;
}
</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);reset();"
	onunload="call()">
<form action="acctMaintenance.do">
	<input name="method" type="hidden" value="">
	<input name="callerMethod" type="hidden" value="">
	<input name="callerMethod" type="hidden" value="">
	<input name="relatedToaccountId" type="hidden" value="">

	<c:if test="${'add'!= requestScope.method}">
		<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
		<div id="InterestRateAddAcc" color="#7E97AF"
			style="position: absolute; left: 20px; border: 2px outset; top: 20px; width: 360px; height: 110px;">
		<div
			style="position: absolute; left: 8px; top: 4px; width: 280px; height: 97px;">
		<fieldset
			style="width: 336px; border: 2px groove; height: 95px!important; left: 8px; top: 8px;">
		<legend><fmt:message key="contact.fieldet" /></legend>
		 <div id="caldiv"
			style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no"
			frameborder="0"
			style="position: absolute; top: 0px; left: 0px; display: none;">
		</iframe>
		<table width="320" border="0" cellpadding="0" cellspacing="0"
			height="75">
			<tr height="25px">
				<td width="45px">&nbsp;<b><fmt:message key="accIntRate.Date" /></b></td>
				<td width="28px">&nbsp;</td>
				<td width="80px"><input type="text"
					name="acctInterestRate.interestDateRateAsString" maxlength="10"
					value="${acctInterestRate.interestDateRateAsString}"
					titleKey="tooltip.accIntRateDate" class="htmlTextNumeric"
					tabindex="1" style="width:80px;" disabled="true" />	
					</td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><fmt:message key="accIntRate.Credit" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<td width="170px"><input type="text"
					name="acctInterestRate.creditRate"
					value="${acctInterestRate.creditRate}"
					titleKey="tooltip.enteraccIntRateCredit"
					class="htmlTextNumeric" tabindex="2" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccRate(this,'message.StartPosition','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
			<tr height="25px">

				<td width="115px">&nbsp;<b><fmt:message key="accIntRate.OverDraft" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><input type="text"
					 name="acctInterestRate.overdraftRate"
					value="${acctInterestRate.overdraftRate}"
					titleKey="tooltip.enteraccIntRateOverDraft"
					class="htmlTextNumeric" tabindex="3" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccDraft(this,'message.end','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
		</table>
		</fieldset>
		</div>
		<div id="EntityMaintenance"
			style="position: absolute; left: 290; top: 115; width: 70px; height: 39px; visibility: visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td align="Right"><a tabindex="6" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Account InterestRateChange Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

				<td align="right" id="Print"><a tabindex="6"
					onclick="printPage();" onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
					src="images/Print.gif " name="Print" border="0"
					title='<fmt:message key="tooltip.printScreen"/>' /></a></td>
			</tr>
		</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: -2px; top: 110px; width: 360px; height: 39px; visibility: visible;">
		<c:if test="${'readonly'== requestScope.screenStatus}">
			<div id="AccountMaintenanceContact"
				style="position: absolute; left: 6; top: 10; width: 360px; height: 15px; visibility: visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td id="closebutton" width="70"
						title='<fmt:message key="tooltip.close"/>'><a
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><fmt:message key="button.close" /></a></td>
				</tr>
			</table>
			</div>
		</c:if>
	</c:if>
	<c:if test="${'add'== requestScope.method}">
		<div id="InterestRateAddAcc" color="#7E97AF"
			style="z-index: 99; position: absolute; left: 20px; border: 2px outset; top: 20px; width: 360px; height: 110px;">
		<div
			style="z-index: 99; position: absolute; left: 8px; top: 4px; width: 280px; height: 75px;">

		<div id="caldiv"
			style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no"
			frameborder="0"
			style="position: absolute; top: 0px; left: 0px; display: none;">
		</iframe>
		<div style="left:8px; top:4px;">
		<fieldset
			style="width: 336px; border: 2px groove; height: 95px!important; left: 8 px; top: 8px;">
		<legend><fmt:message key="contact.fieldet" /></legend>
		<table width="320" border="0" cellpadding="0" cellspacing="0"
			height="50">
			<tr height="25px">
				<td width="45px">&nbsp;<b><fmt:message key="accIntRate.Date" /></b>*</td>
				<td width="28px">&nbsp;</td>
				<td width="160px"><input type="text" class="htmlTextAlpha"
					maxlength="10" name="acctInterestRate.interestDateRateAsString"
					value="${acctInterestRate.interestDateRateAsString}"
					style="width:80px;margin-bottom: 5px;height:20px;" titleKey="tooltip.accIntRateDate" tabindex="1"
					 onchange="return validateField(this,'acctInterestRate.interestDateRateAsString',dateFormat);" /> &nbsp; <A
					title='<fmt:message key="tooltip.selectInterestRateDate"/>'
					name="datelink" ID="datelink" tabindex="1"
					onClick="cal.select(document.forms[0].elements['acctInterestRate.interestDateRateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;">
				<img title='<fmt:message key="tooltip.selectInterestRateDate"/>'
					src="images/calendar-16.gif"></A></td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><fmt:message key="accIntRate.Credit" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><input type="text"
					name="acctInterestRate.creditRate"
					value="${acctInterestRate.creditRate}"
					titleKey="tooltip.enteraccIntRateCredit"
					class="htmlTextNumeric" tabindex="2" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccRate(this,'message.StartPosition','negativeWithDigits');" />
				<!-- End : Nithiyananthan 14/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
			<tr height="25px">
				<td width="115px">&nbsp;<b><fmt:message key="accIntRate.OverDraft" /></b>*</td>
				<td width="48px">&nbsp;</td>
				<!-- Start : Nithiyananthan 12/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				<td width="170px"><input type="text"
					name="acctInterestRate.overdraftRate"
					value="${acctInterestRate.overdraftRate}"
					titleKey="tooltip.enteraccIntRateOverDraft"
					class="htmlTextNumeric" tabindex="3" style="width:170px;"
					maxlength="21"
					onchange="return validateFieldAccDraft(this,'message.end','negativeWithDigits');" />
				<!-- End : Nithiyananthan 12/12/2011 Mantis 1559: Account Interest Rate : Displays "infinite" for infinite numbers -->
				</td>
			</tr>
		</table>
		</fieldset>
		</div>
		</div>

		<div id="EntityMaintenance"
			style="position: absolute; left: 290; top: 115; width: 70px; height: 39px; visibility: visible;">
		<table width="60px" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td align="Right"><a tabindex="6" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Account InterestRateAdd Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
				<td align="right" id="Print"><a tabindex="6"
					onclick="printPage();" onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
					src="images/Print.gif " name="Print" border="0"
					title='<fmt:message key="tooltip.printScreen"/>'></a></td>
			</tr>
		</table>
		</div>
		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: -2px; top: 110px; width: 360px; height: 39px; visibility: visible;">
			<c:if test="${'readonly'== requestScope.screenStatus}">
			<div id="AccountMaintenanceContact"
				style="position: absolute; left: 6; top: 4; width: 360px; height: 15px; visibility: visible;">
			<table width="140" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<td id="closebutton" width="70"
						title='<fmt:message key="tooltip.close"/>'><a
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><fmt:message key="button.close" /></a></td>
				</tr>
			</table>
			</div>
		</c:if>
		</c:if>
			<c:if test="${'readonly'!= requestScope.screenStatus}">
		<div id="AccountMaintenanceContact"
			style="position: absolute; left: 6; top: 4; width: 360px; height: 15px; visibility: visible;">
		<table width="140" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<c:if test="${'change'== requestScope.method}">
				<tr>
					<td id="okbutton" width="70"><a tabindex="4"
						title='<fmt:message key="tooltip.ok"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:submitForm('updateAcctInterestRate');"><fmt:message key="button.ok" /></a></td>
					<td id="cancelbutton" width="70"><a tabindex="5"
						title='<fmt:message key="tooltip.cancel"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><fmt:message key="button.cancel" /></a>
					</td>
				</tr>
			</c:if>
			<c:if test="${'change'!= requestScope.method}">
				<tr>
					<td id="okbutton" width="70"><a tabindex="4"
						title='<fmt:message key="tooltip.ok"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="javascript:submitForm('saveAcctInterestRate');"><fmt:message key="button.ok" /></a></td>
					<td id="cancelbutton" width="70"><a tabindex="5"
						title='<fmt:message key="tooltip.cancel"/>'
						onMouseOut="collapsebutton(this)"
						onMouseOver="highlightbutton(this)"
						onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
						onclick="window.close();"><fmt:message key="button.cancel" /></a>
					</td>
				</tr>
			</c:if>
		</table>
		</div>
	</c:if>
</form>
</body>
</html>