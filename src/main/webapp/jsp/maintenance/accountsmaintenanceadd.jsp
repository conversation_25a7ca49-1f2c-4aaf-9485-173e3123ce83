<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.Country"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ page import="org.swallow.maintenance.model.AcctMaintenance"%>
<%
	//variable declaration
	String linkAcctId = "";
	String linkAcctName = "";
	String countryId = "";
	String countryName = "";
	String currencyId = "";
	String currencyName = "";
	String mainAcctId = "";
	String mainAcctName = "";
	String bookCode = "";
	String bookCodeName = "";
	String acctLevel = "";
	//get the acctMaintenance from request
	if (request.getAttribute("acctMaintenance") != null) {
		//set the AcctMaintenance bean
		AcctMaintenance acMaintenance = (AcctMaintenance) request
				.getAttribute("acctMaintenance");
		//get the linked account id
		linkAcctId = acMaintenance.getLinkAccID();
		//check linked account name not null
		if (request.getAttribute("linkAcctName") != null)
			//get the linked account name from request
			linkAcctName = request.getAttribute("linkAcctName")
					.toString();
		//get the country id
		countryId = acMaintenance.getHolidaycalendar();
		//check country name not null
		if (request.getAttribute("countryName") != null)
			//get the country name from request
			countryName = request.getAttribute("countryName")
					.toString();
		//get the currency code
		currencyId = acMaintenance.getCurrcode();
		//check currency name not null
		if (request.getAttribute("currencyName") != null)
			//get the currency name from request
			currencyName = request.getAttribute("currencyName")
					.toString();
		//get the main account id
		mainAcctId = acMaintenance.getMinacctcode();
		//check main account name not null
		if (request.getAttribute("mainAcctName") != null)
			//get the main account name from request
			mainAcctName = request.getAttribute("mainAcctName")
					.toString();
		//get book code
		bookCode = acMaintenance.getSweepbookcode();
		//check book code name not null
		if (request.getAttribute("bookCodeName") != null)
			//get the book code name from request
			bookCodeName = request.getAttribute("bookCodeName")
					.toString();
		//get the account level
		acctLevel = acMaintenance.getAcctlevel();



		if (acMaintenance.getLatestCreditRate()!=null ){
			acMaintenance.setLatestCreditRate( SwtUtil.formatPrecisionDecimal(acMaintenance.getLatestCreditRate()));
		}
		if (acMaintenance.getLatestOverDraftRate()!=null ){
			acMaintenance.setLatestOverDraftRate(SwtUtil.formatPrecisionDecimal(acMaintenance.getLatestOverDraftRate()));
		}

	}
%>
<html>
<head>
<title><c:if test="${'add'==requestScope.methodName}">

	<fmt:message key="accountmaintenance.title.addWindow"/>

</c:if> <c:if test="${'add'!=requestScope.methodName}">

	<c:if test="${'change'==requestScope.methodName}">

		<fmt:message key="accountmaintenance.changeaccountdetails.addWindow"/>

</c:if>
	<c:if test="${'view'==requestScope.methodName}">

		<fmt:message key="accountmaintenance.viewaccountdetails.addWindow"/>

</c:if>

</c:if></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<style type="text/css">
.upperText{
text-transform: uppercase;
}
</style>
<SCRIPT language="JAVASCRIPT">


	/**
	 * When window is load, find each input,select disabled
	 * find each 'tr' that contains a 'td' which text ends with '*'
	 * then apply the style 'required' for each one
	 * Added by Med Amine Ben Ahmed
	 *
	 **/
	$(window).on("load", function () {
		$("input[disabled='disabled'][type='text'],select:disabled").addClass('is-disabled');
		$("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function () {
			var tr = $(this);
			tr.find('input').not('[name*="bookCodeId"]').css('border-color', function (index) {
				if ($(this).val() == '')
					return 'red';
			}).change(function () {
				if ($(this).val() == '')
					$(this).css('border', 'red 1px solid');
				else {
					$(this).css('border-color', '');
					$(this).addClass('inputText');
				}
			});
		});
	});

	/**
	 * When document is ready, we attach a handler to a click event for the (radio,check) input
	 * to update style && find each 'tr' that contains a 'td'
	 * which value contains  '*',
	 * then apply the style 'required' for each empty input
	 * Added by Med Amine Ben Ahmed
	 *
	 **/
	$(document).ready(function () {

		$("input[type='radio'],input[type='checkbox']").on('click', function () {
			$("input[disabled='disabled'][type='text']").addClass('is-disabled ');
			$("input[type='text']").css('border-color', '');

			$("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function () {
				var tr = $(this);
				tr.find('input').not('[name*="bookCodeId"],[disabled="disabled"]').css('border-color', function (index) {
					if ($(this).val() == '')
						return 'red';
				}).change(function () {
					if ($(this).val() == '')
						$(this).css('border', 'red 1px solid');
					else {
						$(this).css('border-color', '');
						$(this).addClass('inputText');
					}
				});
			});
		});


	});
	//get the value from the bean && assign into it variable
	var linkAcctId = "<%= linkAcctId %>";
	var linkAcctName = "<%= linkAcctName %>";
	var countryId = "<%= countryId %>";
	var countryName = "<%= countryName %>";
	var currencyId = "<%= currencyId %>";
	var currencyName = "<%= currencyName %>";
	var mainAcctId = "<%= mainAcctId %>";
	var mainAcctName = "<%= mainAcctName %>";
	var bookCode = "<%= bookCode %>";
	var bookCodeName = "<%= bookCodeName %>";
	var methodName = '${requestScope.methodName}'
	var selectedCurrencyCode = '${requestScope.selectedCurrencyCode}';
	var asJson = '${requestScope.acctSweepSchedulecollJson}';
	var acctScheduleNextVal = '${requestScope.acctScheduleNextVal}';
	var acctSweepBalGrpCollInSessionJson = '${requestScope.acctSweepBalGrpCollInSessionJson}';
	var accountLevel = "<%= acctLevel %>";
	//variable to hold objTab from ddimagetabs
	var objTab = null;
	//variable to hold objTabLink from element tag
	var objTabLink = null;
	//variable to hold elementsRef
	var elementsRef;
	//variable to hold validateScriptFlag
	var validateScriptFlag;
	<c:if test="${'true'==requestScope.closePosLvl}">

	var initialtab = [3, "sc3"];
	var selectedtab = initialtab[3];

</c:if>
	var showLoroToPredicted =
	<%= (request.getAttribute("showLoroToPredicted") != null
                                       && !SwtUtil.isEmptyOrNull(request.getAttribute("showLoroToPredicted").toString())) ?
                                     (request.getAttribute("showLoroToPredicted").toString().equals(SwtConstants.YES) ?
                                       SwtConstants.STR_FALSE : SwtConstants.STR_TRUE) : SwtConstants.STR_TRUE %>

	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
	var currencyFormat = '${sessionScope.CDM.currencyFormat}';
	//based on the parentFormRefresh value submit the opener form.
	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	closeHandler();
	self.close();
	</c:if>

	function closeHandler() {
		window.opener.postMessage(['refreshParentScreen', []], "*");
	}

	//assign the default value for the variable
	var linkAcctclk = true;
	var linkAccSelectElement = null;
	var mainAcctclk = true;
	var mainAccSelectElement = null;
	var bookCodeclk = true;
	var bookSelectElement = null;
	var countryclk = true;
	var currencyclk = true;
	var linkSwSel = null;
	var mainSwSel = null;
	var bookSwSel = null;

	/**
	 * onLoadBtnStatus
	 *
	 * This method is used to enable/disable the components based on the response comes from the server.
	 */
	function onLoadBtnStatus() {
		//set the disabled property for elements based on the add/change/view account
		document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
		document.forms[0].elements["acctMaintenance.id.accountId"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.acctname"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.currcode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "true" : "true"));
		//collection of radio group to set the disable property
		var radioGroupName = ['acctMaintenance.accttype', 'acctMaintenance.acctClass', 'acctMaintenance.forecastSOD',
			'acctMaintenance.externalSOD', 'acctMaintenance.futureBalances', 'acctMaintenance.acctlevel',
			'acctMaintenance.autoswpswitch']
		for (var i = 0; i < radioGroupName.length; i++) {
			if (document.forms[0].elements[radioGroupName[i]] != null) {
				for (var j = 0; j < document.forms[0].elements[radioGroupName[i]].length; j++) {
					if (document.forms[0].elements[radioGroupName[i]][j] != null) {
						if (radioGroupName[i] == "acctMaintenance.acctlevel")
							document.forms[0].elements[radioGroupName[i]][j].disabled = (methodName == "add" ? "" :
									(methodName == "change" ? "" : "true"));
						else
							document.forms[0].elements[radioGroupName[i]][j].disabled = (methodName == "add" ? "" :
									(methodName == "change" ? "" : "true"));
					}
				}
			}
		}

		document.getElementById("linkaccountId").disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.getElementById("mainaccountId").disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.getElementById("bookCodeId").disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.aggAccount"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.archiveData"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctstatusflg"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.holidaycalendar"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.glcode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.corresacccode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctbiccode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctIBAN"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctextraid"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.latestInterestDateRate"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.latestCreditRate"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.latestOverDraftRate"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.monitor"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctMonitorSum"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctPriorityOrder"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.autoOpenUnsettled"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.autoOpenUnexpected"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.allPreAdviceEntity"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.primaryForecast"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.primaryExternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.secondaryForecast"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.secondaryExternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
// 		document.forms[0].elements["acctMaintenance.subAcctim"].disabled = (methodName == "add" ? "" :
// 					 		(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.mansweepflg"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));

		document.forms[0].elements["acctMaintenance.subAcctim"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));

		document.forms[0].elements["acctMaintenance.defaultSettleMethod"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));


		document.forms[0].elements["acctMaintenance.swpdays"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.sweepCode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.cutoff"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.sweepFrmbal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["sweepintermediaries.intermediary"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.eodSweeptime"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		//TODO:
		document.forms[0].elements["acctMaintenance.targetbalanceasString"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.tgtbalsign"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.minseepamtasString"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.maxsweepamteasString"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		//TODO:Change
// 		document.forms[0].elements["acctMaintenance.intraDaySweeptime"].disabled = (methodName == "add" ? "" :
// 					 		(methodName == "change" ? "" : "true"));
// 		document.forms[0].elements["acctMaintenance.eodTargetbalanceasString"].disabled = (methodName == "add" ? "" :
// 					 		(methodName == "change" ? "" : "true"));
// 		document.forms[0].elements["acctMaintenance.eodTgtBalsign"].disabled = (methodName == "add" ? "" :
// 					 		(methodName == "change" ? "" : "true"));
// 		document.forms[0].elements["acctMaintenance.eodMinseepamtasString"].disabled = (methodName == "add" ? "" :
// 					 		(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctNewCrInternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctNewDrInternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctNewCrExternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.acctNewDrExternal"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.creditExternalInter"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.debitExternalInter"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.minacctcode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.sweepbookcode"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.linkAccID"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["dropdownbutton_link"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["dropdownbutton_2"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["dropdownbutton_3"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.accountPartyId"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.getElementById("partyLink").disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.isIlmCentralBankMember"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.isIlmLiqContributor"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.isIlmCustomerAccount"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.defaultSettleMethod"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.fmi"].disabled = "true";

		document.forms[0].elements["acctMaintenance.servicingEntityId"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.accNameInSvcEntity"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.thisEntityInclBalFlag"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.thisEntityInclFrom"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.thisEntityInclTo"].disabled = (methodName == "add" ? "" :
				(methodName == "change" ? "" : "true"));
		document.forms[0].elements["acctMaintenance.svcEntityInclBalFlag"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.svcEntityInclFrom"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		document.forms[0].elements["acctMaintenance.svcEntityInclTo"].disabled = (methodName == "add" ? "true" :
				(methodName == "change" ? "true" : "true"));
		//document.forms[0].elements['acctMaintenance.loroToPredicted'].disabled = showLoroToPredicted;
	}

	/**
	 * checkalphanumeric
	 *
	 * This method is used to check the alphaNumPat for given element
	 */
	var globalElement

	function checkalphanumeric(element) {
		globalElement = element;
		var thePat = PatternsDict['biccode'];
		var message = PatternsMetaData['biccode'];

		if (validateField(element, 'acctbiccode', 'alphaNumPat') && element.value != "") {
			match = thePat.exec(element.value);
			if (!match) {
				ShowErrMsgWindowWithBtn('', message, YES_NO, null, noContinue);
			}
		}
		return true;
	}

	function noContinue() {
		if (window.event)
			window.event.returnValue = false;
		globalElement.focus();
	}

	/**
	 * getAmtValue
	 *
	 * This method is used to get the amount value from given param.
	 */
	function getAmtValue(amt) {
		var amtvalue = '';
		for (var idx = 0; idx < amt.length; idx++) {
			el = amt.charAt(idx);
			if (el != ',' && el != '.') {
				amtvalue += el;
			}
		}
		return amtvalue;
	}

	/**
	 * validateSweepAmount
	 *
	 * This method is used to validate the sweep amount
	 */
	function validateSweepAmount(intField) {
		//TODO:
		// Gets the field value && converts abbreviation && validates
		var result = validateCurrency(intField, 'creditintrates', currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value);
		//checks the validation result
		if (result) {
//         	return true;
			//Variable to hold eod minimum Sweep amount
			var amountOver = document.forms[0].elements['acctMaintenance.minseepamtasString'].value;
			//Variable to hold Maximum Sweep amount
			var amountUnder = document.forms[0].elements['acctMaintenance.maxsweepamteasString'].value;

			//Variable to hold Intra Day minimum Sweep amount
// 				var amountOverIntra = document.forms[0].elements['acctMaintenance.eodMinseepamtasString'].value ;
			// Checks the maximum amount is greater than minimum amount, && if not throws error message
			if (validateAmount(amountOver, amountUnder)) {
				return true;
			} else {
				return false;
			}
//                		validateAmount(amountOverIntra,amountUnder);
		}
	}


	/**
	 *
	 * This method is used to validate the Maximum Sweep amount with Eod/IntraDay Minimum Sweep time Balances
	 */
	function validateAmount(amtover, amtunder) {
		if (amtover != "" && amtunder != "") {
			var amountOver = new Number(getAmtValue(amtover));
			var amountUnder = new Number(getAmtValue(amtunder));
			if (amountUnder >= amountOver) {
				return true;
			} else {
				objTabLink[2].onclick();
				alert('<fmt:message key="accountmaintenance.MaximumMinimum"/>');
				document.forms[0].elements["acctMaintenance.maxsweepamteasString"].focus();
				return false;
			}
		} else {
			return true;
		}
	}

	/**
	 *
	 * This method is used to validate the tab content text fields from selected current tab to all tabs(General/Balances/Sweeping) that
	 * text Mandatory fields are blank && valid
	 *
	 * @param objForm
	 * @return validateScriptFlag
	 */
	function textFieldValidation(objForm) {
		elementsRef = new Array();
		elementsRef[0] = objForm.elements["acctMaintenance.id.accountId"];
		elementsRef[1] = objForm.elements["acctMaintenance.acctname"];
		//Validate the accountid,account name that mandatory fields are blank.
		validateScriptFlag = validate(elementsRef);
		//Validate the accountid that text fields are valid.
		if (validateScriptFlag) {
			<c:if test="${'add'==requestScope.methodName}">

			validateScriptFlag = validateField(document.forms[0].elements["acctMaintenance.id.accountId"], 'account.AccountId', 'alphaNumPat')

</c:if>
		}
		//Validate the account name that text fields are valid.
		if (validateScriptFlag)
			validateScriptFlag = validateField(document.forms[0].elements["acctMaintenance.acctname"], 'section.sectionName', 'ascii_standard');

		if (selectedtab == "sc1" && validateScriptFlag == true) {
			//Call generalTabValidation to validate general tab text field
			generalTabValidation();
			if (validateScriptFlag)
					//Call balancesTabValidation to validate balances tab text field
				balancesTabValidation(objForm);
			if (validateScriptFlag)
					//Call sweepingTabValidation to validate sweeping tab text field
				sweepingTabValidation(objForm);
			if (validateScriptFlag)
				objTabLink[0].onclick();
		} else if (selectedtab == "sc2" && validateScriptFlag == true) {
			//Call balancesTabValidation to validate balances tab text field
			balancesTabValidation(objForm);
			if (validateScriptFlag)
					//Call sweepingTabValidation to validate sweeping tab text field
				sweepingTabValidation(objForm);
			if (validateScriptFlag)
					//Call generalTabValidation to validate general tab text field
				generalTabValidation();
			if (validateScriptFlag)
				objTabLink[1].onclick();
		} else if (selectedtab == "sc3" && validateScriptFlag == true) {
			//Call sweepingTabValidation to validate sweeping tab text field
			sweepingTabValidation(objForm);
			if (validateScriptFlag)
					//Call balancesTabValidation to validate balances tab text field
				balancesTabValidation(objForm);
			if (validateScriptFlag)
					//Call generalTabValidation to validate general tab text field
				generalTabValidation();
			if (validateScriptFlag)
				objTabLink[2].onclick();
		}
		return validateScriptFlag;
	}


	/*Start Code:Modified by Naseema.Sd for Mantis 1797 */
	/**
	 * This method is used to validate text fields in general tab contents && set the focus to text field.
	 */
	function generalTabValidation() {
		//Set focus to General tab
		objTabLink[0].onclick();
		//Validate generalTab text field content as correspondent code,gl code,extra id && set the flag
		// Extended acctname field validation for GL code, Correspondent code && Extra Id, biicode Validation for biccode
		validateScriptFlag = (validateField(document.forms[0].elements["acctMaintenance.glcode"], 'acctglcode', 'alphaNumPatExtended_AcctName') &&
				validateField(document.forms[0].elements["acctMaintenance.corresacccode"], 'corrscode', 'alphaNumPatExtended_AcctName') &&
				validateField(document.forms[0].elements["acctMaintenance.acctextraid"], 'acctextraid', 'alphaNumPatExtended_AcctName') &&
				validateField(document.forms[0].elements["acctMaintenance.fmi"], 'acctMaintenance.fmi', 'alphaNumPatExtended_AcctName'))
	}

	/*End Code:Modified by Naseema.Sd for Mantis 1797 */


	/**
	 *
	 * This method is used to validate mandatory text fields are empty, text fields are valid in Balances tab contents && * set the focus to text field.

	 *
	 * @Param objForm
	 */
	function balancesTabValidation(objForm) {
		//Set focus to Balances tab
		objTabLink[1].onclick();
		elementsRef = new Array();
		elementsRef[0] = objForm.elements["acctMaintenance.acctPriorityOrder"];
		//validate Priority order mandatory field is empty
		validateScriptFlag = validate(elementsRef);

		//Validate balancesTab text field content as priority order && set the flag
		if (validateScriptFlag)
			validateScriptFlag = validateField(document.forms[0].elements["acctMaintenance.acctPriorityOrder"], 'currency.order', 'numberPat') &&
					validateField(document.forms[0].elements["acctMaintenance.thisEntityInclFrom"], 'applyBetween', 'timePat') &&
					validateField(document.forms[0].elements["acctMaintenance.thisEntityInclTo"], 'and', 'timePat')
	}

	/**
	 *
	 * This method is used to validate mandatory text fields are empty ,text fields are valid in Sweeping tab contents,
	 *    Mandatory text fields will be enabled based on 'auto sweep' && 'Manual sweeping' field && set the focus to text field.
	 *
	 * @Param objForm
	 */
	function sweepingTabValidation(objForm) {


		//Set focus to Sweeping tab
		objTabLink[2].onclick();
		//Variable to hold eod minimum Sweep amount
		var amountover = document.forms[0].elements['acctMaintenance.minseepamtasString'].value;
// 		//Variable to hold Maximum Sweep amount
		var amountunder = document.forms[0].elements['acctMaintenance.maxsweepamteasString'].value;
		//Variable to hold Intra Day minimum Sweep amount
// 		var amountoverintra = document.forms[0].elements['acctMaintenance.eodMinseepamtasString'].value ;
		//validate eodsweep time,cutofftime,eodtargetbalances,intradaytargetbalances mandatory fields are empty based on manual sweeping check && auto sweep
		if (checkSweepRadioStatus()) {
			elementsRef = new Array();
			//Check the condition Use sub account-Timing checkbox is selected
			if (document.forms[0].elements["acctMaintenance.subAcctim"].checked == false) {
				elementsRef[2] = objForm.elements["acctMaintenance.cutoff"];
				elementsRef[3] = objForm.elements["acctMaintenance.eodSweeptime"];
// 				if (!checkIntraSweepTime()){
// 					elementsRef[5] = objForm.elements["acctMaintenance.eodTargetbalanceasString"];
// 				}
			} else if (!checkIntraSweepTime()) {
// 				elementsRef[3] = objForm.elements["acctMaintenance.eodTargetbalanceasString"];
			}
			elementsRef[0] = objForm.elements["acctMaintenance.targetbalanceasString"];
			elementsRef[1] = objForm.elements["acctMaintenance.tgtbalsign"];
// 			elementsRef[2] = objForm.elements["acctMaintenance.eodTgtBalsign"];
			validateScriptFlag = validate(elementsRef);
		}

		//Validate sweeping tab text field content as sweep days,sweepCode,cutofftime,eod sweep time,eod targetbalances,eod minimum balances,intraday sweep time,intraday target balances,intraday minimum balances
		if (validateScriptFlag)
			validateScriptFlag = (validateField(document.forms[0].elements["acctMaintenance.swpdays"], 'swptime', 'numberPatAll', 99, -99) &&
					validateField(document.forms[0].elements["acctMaintenance.sweepCode"], 'sweepCode', 'alphaNumPat') &&
					validateField(document.forms[0].elements["acctMaintenance.cutoff"], 'cutofftime', 'timePat') &&
					validateCurrency(document.forms[0].elements["acctMaintenance.maxsweepamteasString"], 'creditintrates', currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value) &&
					validateField(document.forms[0].elements["acctMaintenance.eodSweeptime"], 'swptime', 'timePat') &&
					validateCurrency(document.forms[0].elements["acctMaintenance.targetbalanceasString"], 'creditintrates', currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value)
					&&
					validateCurrency(document.forms[0].elements["acctMaintenance.minseepamtasString"], 'creditintrates', currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value) &&
					// 				validateField(document.forms[0].elements["acctMaintenance.intraDaySweeptime"],'swptime','timePat') &&
					// 				validateCurrency(document.forms[0].elements["acctMaintenance.eodTargetbalanceasString"],'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value) &&
					// 				validateCurrency(document.forms[0].elements["acctMaintenance.eodMinseepamtasString"],'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value)
					(validateAmount(amountover, amountunder))
// 				&& (validateAmount(amountoverintra,amountunder))
			)


	}

	/**
	 * submitCopyFrom
	 *
	 * This method is used to open the copy from window while clicking on the cpyFrom button.
	 */
	function submitCopyFrom(methodName) {
		//variable to hold the param variable
		var param = 'acctMaintenance.do?method=' + methodName;
		param += '&entityCode=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&newAcctId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&selectedCurrencyCode=' + selectedCurrencyCode;
		param += '&currencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		return param;
	}


	/**
	 * submitForm
	 *
	 * This method is used to submit form while clicking on the save/update button.
	 */
	function submitForm(methodName) {
		var pcmEnabled = "<%=SwtUtil.getPcmEnabled()%>";
		elementTrim(document.forms[0]);
		var validText = false;
		validText = textFieldValidation(document.forms[0]);
		if (validText) {
			if (ValidateAccountParty() == "false") {
				var confirmAlert = confirm('<fmt:message key="accountmaintenace.alert.partyId"/>');
				if (!confirmAlert) {
					return;
				}
			}
			var contactName = document.forms[0].contactName.value;
			var checkResult = checkSweepDays();
			if (!checkResult) {
				alert('<fmt:message key="accountmaintenace.alert.PositiveSweepDays"/>');
				return;
			}
			if (accountLevel == "<%=SwtConstants.ACCT_SUB%>") {
				document.forms[0].elements["acctMaintenance.minacctcode"].disabled = "";
				var mainAccount = document.forms[0].elements['acctMaintenance.minacctcode'].value;
				if (mainAccount == "") {
					objTabLink[2].onclick();
					alert('<fmt:message key="accountmaintenace.alert.selectAccount"/>');
				} else {
					enableFields();
					document.forms[0].method.value = methodName;
					document.forms[0].selectedCurrencyCode.value = selectedCurrencyCode;
					if (pcmEnabled == "true") {
						if (methodName == "save" && document.forms[0].elements["acctMaintenance.acctClass"][0].checked) {
							alert('<fmt:message key="accountmaintenace.alert.pcmAccount"/>');
						}
					}
					document.forms[0].submit();
				}
			} else {
				enableFields();
				document.forms[0].method.value = methodName;
				document.forms[0].selectedCurrencyCode.value = selectedCurrencyCode;
				if (pcmEnabled == "true") {
					if (methodName == "save" && document.forms[0].elements["acctMaintenance.acctClass"][0].checked) {
						alert('<fmt:message key="accountmaintenace.alert.pcmAccount"/>');
					}
				}
				document.forms[0].submit();
			}

		}
	}

	function ValidateAccountParty() {
		var accountPartyId = document.forms[0].elements['acctMaintenance.accountPartyId'].value;
		if (accountPartyId == "") {
			return true;
		}
		var entityId = document.forms[0].elements['acctMaintenance.id.entityId'].value;

		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName + "/acctMaintenance.do?method=checkIfPartyIdExists";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&partyId=" + accountPartyId;

		return (sendRequest(requestURL));
	}

	/**
	 * checkIntraSweepTime
	 *
	 * This method is used to validate the target balance for intraday sweep based on sweeping ON/OFF
	 */
	function checkIntraSweepTime() {
		try {

			var resultFlag = true;
			var sweepTime = document.forms[0].elements["acctMaintenance.eodSweeptime"].value;
			var targetBal = document.forms[0].elements["acctMaintenance.targetbalanceasString"];
			var objTargetBalance = document.getElementById("trgtBalintra");
			//if sweep time is present then force to fill target balance for intra day when sweeping ON.
			if ((sweepTime.trim() != "" && sweepTime.trim().length > 0) && (checkSweepRadioStatus())) {
				objTargetBalance.innerText = "*";
				if (targetBal.value.trim() == "" && targetBal.value.trim().length <= 0)
					resultFlag = false;
			} else {
				objTargetBalance.innerText = "";
			}
		} catch (e) {
			console.log('e', e)

		}
		return resultFlag;
	}


	function refreshScreen(methodName) {
		document.forms[0].method.value = methodName;
		enableFields();
		document.forms[0].submit();
	}

	function checkSweepDays() {
		// Convert sweepDays to integer && handle non-numeric input
		var sweepDays = document.forms[0].elements['acctMaintenance.swpdays'].value;

		try {
			sweepDays = parseInt(sweepDays);
		} catch (error) {
			return false;
		}
		console.log("sweepDays", sweepDays)

		// Check if sweepDays is 0 or less
		return true;
		//Commented for mantis 0007400: Sweeping process: support positive sweep days
		//return sweepDays <= 0;
	}

	/**
	 * enableFields
	 *
	 * This method is called before the form submit to be needed to enable to get the values.
	 */
	function enableFields() {
		var counElement = document.forms[0].elements["acctMaintenance.holidaycalendar"];
		var accountlevel = '${requestScope.accountLevel}';
		document.forms[0].accountLevel.value = accountlevel;
		document.forms[0].elements["acctMaintenance.id.accountId"].disabled = "";
		document.forms[0].elements["acctMaintenance.currcode"].disabled = "";
		document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
		document.getElementById("currencyNameDisp").disabled = "";
		document.forms[0].elements["acctMaintenance.cutoff"].disabled = "";
		document.forms[0].elements["acctMaintenance.minacctcode"].disabled = "";
		document.forms[0].elements["acctMaintenance.linkAccID"].disabled = "";
		document.forms[0].elements["acctMaintenance.latestCreditRate"].disabled = "";
		document.forms[0].elements["acctMaintenance.latestOverDraftRate"].disabled = "";
		document.forms[0].elements["acctMaintenance.latestInterestDateRate"].disabled = "";
		document.forms[0].linkAcctName.value = document.getElementById("linkaccountName").value;
		//get the countryName for selected country in the options
		if (counElement.options != null && counElement.options.length > 0)
			document.forms[0].countryName.value = counElement.options[counElement.selectedIndex].text;
		document.forms[0].currencyName.value = document.getElementById("currencyNameDisp").innerText;
		document.forms[0].mainAcctName.value = document.getElementById("mainaccountName").value;
		document.forms[0].bookCodeName.value = document.getElementById("bookCodeNameDisp").value;

		//TODO:
		document.forms[0].elements["acctMaintenance.eodSweeptime"].disabled = "";
// 		document.forms[0].elements["acctMaintenance.intraDaySweeptime"].disabled = "";

	}

	function calllostFocus() {
		if (document.forms[0].elements["acctMaintenance.minacctcode"].tagName != "SELECT")
			document.forms[0].elements["acctMaintenance.minacctcode"].disabled = "true";
	}

	function callShowMainAcctId() {
		document.forms[0].elements["acctMaintenance.minacctcode"].disabled = "";
	}

	function callShowSweeping() {
		//TODO:
		if (!checkSweepRadioStatus()) {
			callHideSweeping();
// 			document.forms[0].elements["acctMaintenance.tgtbalsign"].value ='C';
		} else {
			enableSweepingFields();
		}
		disableintra();

		checkIntraSweepTime();


	}

	/**
	 *
	 * This method is used to validate the sweeptime && clear the invalid time.
	 */
	function validateSweeptime() {
		var sweepEod = validateField(document.forms[0].elements["acctMaintenance.eodSweeptime"], 'eodswptime', 'timePat');
		var sweepInra = validateField(document.forms[0].elements["acctMaintenance.intraDaySweeptime"], 'eodswptime', 'timePat');
		if (!sweepEod) {
			document.forms[0].elements["acctMaintenance.eodSweeptime"].value = "";
		}
		if (!sweepInra) {
			document.forms[0].elements["acctMaintenance.intraDaySweeptime"].value = "";
		}
	}

	/**
	 * enableSweepingFields
	 *
	 * This method is used to enable the sweeping fields.
	 */
	function enableSweepingFields() {
		//TODO:
		document.forms[0].elements["acctMaintenance.autoswpswitch"].disabled = "";
		document.forms[0].elements["acctMaintenance.eodSweeptime"].disabled = "";
		var objSwtTimeLabel = document.getElementById("swpTime");
		objSwtTimeLabel.innerText = "*";
		var objCutOffTimeLable = document.getElementById("cutOffTime");
		objCutOffTimeLable.innerText = "*";
		var objTargetBalance = document.getElementById("trgtBal");
		objTargetBalance.innerText = "*";
		var earliestTimeLabel = document.getElementById("earliestSweepTimelabel");
		earliestTimeLabel.innerText = "*";

		document.forms[0].elements["acctMaintenance.swpdays"].disabled = "";
		document.forms[0].elements["acctMaintenance.minseepamtasString"].disabled = "";
		document.forms[0].elements["acctMaintenance.maxsweepamteasString"].disabled = "";
		document.forms[0].elements["acctMaintenance.targetbalanceasString"].disabled = "";
		document.forms[0].elements["acctMaintenance.tgtbalsign"].disabled = "";
		document.forms[0].elements["acctMaintenance.tgtbalsign"].value = "C";
		document.forms[0].elements["acctMaintenance.cutoff"].disabled = "";
		document.forms[0].elements["acctMaintenance.sweepbookcode"].disabled = "";
// 		document.forms[0].elements["acctMaintenance.tgtbalsign"].value="C";
		document.forms[0].elements["acctMaintenance.sweepCode"].disabled = "";
	}

	function callHideSweeping() {
		//TODO:
		var objSwtTimeLabel = document.getElementById("swpTime");
		objSwtTimeLabel.innerText = "";
		var objCutOffTimeLable = document.getElementById("cutOffTime");
		objCutOffTimeLable.innerText = "";
		var objTargetBalance = document.getElementById("trgtBal");
		objTargetBalance.innerText = "";

		var earliestTimeLabel = document.getElementById("earliestSweepTimelabel");
		earliestTimeLabel.innerText = "";

	}

	/**
	 * populateDropDowns
	 *
	 * This method is called on the body onload to populate the select box.
	 */
	function populateDropDowns() {
		//if the account has link account then set the linkaccountId && linkaccountName
		if (linkAcctId != "" && linkAcctId != "null") {
			linkAccSelectElement = document.forms[0].elements["acctMaintenance.linkAccID"];
			var opt = document.createElement("option");
			opt.text = linkAcctName;
			opt.value = linkAcctId;
			linkAccSelectElement.options.add(opt);
			linkAccSelectElement.selectedIndex = 0;
			document.getElementById("linkaccountId").value = linkAcctId;
			if (linkAcctName != "" && linkAcctName != "null")
				document.getElementById("linkaccountName").value = linkAcctName;
		}
		//if the account has countryId then add country in the select box
		if (countryId != "" && countryId != "null") {
			var selectElement = document.forms[0].elements["acctMaintenance.holidaycalendar"];
			var opt = document.createElement("option");
			opt.text = countryName;
			opt.value = countryId;
			selectElement.options.add(opt);
			selectElement.selectedIndex = 0;
		}
		<c:if test="${'add'!=requestScope.methodName}">

		//add the currency into currency select box.
		if (currencyId != "" && currencyId != "null" && currencyName != "" && currencyName != "null") {
			var selectElement = document.forms[0].elements["acctMaintenance.currcode"];
			var opt = document.createElement("option");
			opt.text = currencyName;
			opt.value = currencyId;
			selectElement.options.add(opt);
			selectElement.selectedIndex = 0;
		}

</c:if>

		//if the account has main account then set the mainaccountId && mainaccountName
		if (mainAcctId != "" && mainAcctId != "null") {
			mainAccSelectElement = document.forms[0].elements["acctMaintenance.minacctcode"];
			var opt = document.createElement("option");
			opt.text = mainAcctName;
			opt.value = mainAcctId;
			mainAccSelectElement.options.add(opt);
			mainAccSelectElement.selectedIndex = 0;
			document.getElementById("mainaccountId").value = mainAcctId;
			if (mainAcctName != "" && mainAcctName != "null")
				document.getElementById("mainaccountName").value = mainAcctName;
		}
		//if the account has bookcode then set the bookCodeId && bookCodeNameDisp
		if (bookCode != "" && bookCode != "null") {
			bookSelectElement = document.forms[0].elements["acctMaintenance.sweepbookcode"];
			var opt = document.createElement("option");
			opt.text = bookCodeName;
			opt.value = bookCode;
			bookSelectElement.options.add(opt);
			bookSelectElement.selectedIndex = 0;
			document.getElementById("bookCodeId").value = bookCode;
			if (bookCodeName != "" && bookCodeName != "null")
				document.getElementById("bookCodeNameDisp").value = bookCodeName;
		}
		var formatCrDbName = ["acctNewCrInternal", "acctNewDrInternal", "acctNewCrExternal",
			"acctNewDrExternal", "creditExternalInter", "debitExternalInter"];
		// add different message format into options.
		for (var i = 0; i < formatCrDbName.length; i++) {
			if (document.forms[0].elements[formatCrDbName[i]] != null) {
				var fmtVal = document.forms[0].elements[formatCrDbName[i]].value;
				if (fmtVal != "" && fmtVal != "null") {
					if (document.forms[0].elements["acctMaintenance." + formatCrDbName[i]] != null) {
						var selectElement = document.forms[0].elements["acctMaintenance." + formatCrDbName[i]];
						var opt = document.createElement("option");
						opt.text = fmtVal;
						opt.value = fmtVal;
						selectElement.options.add(opt);
						selectElement.selectedIndex = 0;
					}
				}
			}
		}
	}

	function populateDropBoxes() {
		ShowErrMsgWindow('${actionError}');
		bodyOnLoad();
		callHideSweeping();
		<c:if test="${'view'!=requestScope.methodName}">

		callShowSweeping();

</c:if>
		<c:if test="${'change'==requestScope.methodName}">

		//TODO:
		document.forms[0].elements["acctMaintenance.tgtbalsign"].value = '${acctMaintenance.tgtbalsign}';

</c:if>
		<c:if test="${'add'==requestScope.methodName}">

		var statusflg = "${requestScope.statusflagmain}";
		if (statusflg == 'M') {
			calllostFocus();
		} else {
			callShowMainAcctId();
		}

</c:if>
		<c:if test="${'change'==requestScope.methodName}">

		var statusflg = "${requestScope.statusflagmain}";
		if (statusflg == 'M') {
			calllostFocus();
		} else {
			callShowMainAcctId();
		}

</c:if>
	}

	function onFilterandSort() {
		updateColors();
		disableAllButtons();

	}

	function disableAllButtons() {
	}

	/**
	 * bodyOnLoad
	 *
	 * This method is called when the screen is onloaded to set the status for elements.
	 */
	function bodyOnLoad() {
		createGridData(asJson);


		xl = new XLSheet("acctSweepSchedulecoll", "table_2", ["String", "String", "String", "String", "Number", "String", "String", "String", "Number", "Number", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String"], "*****************111");
		xl.onsort = xl.onfilter = onFilterandSort;

		highlightTableRows("acctSweepSchedulecoll");

		onLoadBtnStatus();
		populateDropDowns();
		//Added by Mefteh for Mantis 2110: Account Maintenance: Introduce IBAN for future usage
		<% ArrayList<Country> countryList=(ArrayList<Country>)request.getAttribute("countryList"); %>
		// Display the IBAN in Paper Format in case of View || Change action.
		var iban = document.forms[0].elements['acctMaintenance.acctIBAN'].value;
		var paperIBANFomat = '';
		while (iban.length > 0) {
			paperIBANFomat += iban.substring(0, 4) + ' ';
			iban = iban.substring(4);
		}
		document.forms[0].elements['acctMaintenance.acctIBAN'].value = paperIBANFomat;

		document.forms[0].acctNewCrInternal.value = '${requestScope.newCrInternal}';
		document.forms[0].acctNewCrExternal.value = '${requestScope.newCrExternal}';
		document.forms[0].acctNewDrInternal.value = '${requestScope.newDrInternal}';
		document.forms[0].acctNewDrExternal.value = '${requestScope.newDrExternal}';
		document.forms[0].creditExternalInter.value = '${requestScope.CrExternalInt}';
		document.forms[0].debitExternalInter.value = '${requestScope.DdExternalInt}';

		new SwSelectBox(document.forms[0].elements["acctMaintenance.currcode"], document.getElementById("currencyNameDisp"));
		<c:if test="${'view'!=requestScope.methodName}">

// 			if(document.forms[0].elements["acctMaintenance.acctlevel"][0].checked == false) {
// 				document.forms[0].elements["acctMaintenance.subAcctim"].disabled = "true";
// 			}else {
// 				document.forms[0].elements["acctMaintenance.subAcctim"].disabled = "";
// 			}
		//TODO:
// 			disableintra();

</c:if>
		document.getElementById("entityDesc").innerText = '${entityName}';
		document.forms[0].screenStatus.value = '${methodName}';

		if (accountLevel == "<%=SwtConstants.ACCT_SUB%>") {
			document.forms[0].elements["accountIdMandatory"].value = "*";
		} else {
			document.forms[0].elements["accountIdMandatory"].value = "";
			document.getElementById("dropdownbutton_2").disabled = "true";
		}
		objTab = document.getElementById("ddimagetabs");
		//variable to hold objTabLink from element tag
		objTabLink = objTab.getElementsByTagName("A");
		document.getElementById("partyDesc").innerText = '${partyDesc}';
		<c:if test="${'change'==requestScope.methodName}">

		if (document.getElementById("currencyNameDisp").innerText.length <= 0)
			document.getElementById("currencyNameDisp").innerText = '${currencyName}';
		document.getElementById("linkbutton").innerHTML = document.getElementById("linkenablebutton").innerHTML;
		<c:if test="${'N'==requestScope.acctMaintenance.statusflag}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountenablebutton").innerHTML;

</c:if>
		<c:if test="${'Y'==requestScope.acctMaintenance.statusflag}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountdisablebutton").innerHTML;

</c:if>

</c:if>
		<c:if test="${'view'==requestScope.methodName}">

		document.getElementById("currencyNameDisp").innerText = '${currencyName}';
		<c:if test="${'yes'==requestScope.linkedAccount}">

		document.getElementById("linkbutton").innerHTML = document.getElementById("linkdisablebutton").innerHTML;

</c:if>
		<c:if test="${'yes'!=requestScope.linkedAccount}">

		<c:if test="${'no'==requestScope.linkedAccount}">

		document.getElementById("linkbutton").innerHTML = document.getElementById("linkenablebutton").innerHTML;

		</c:if>

		</c:if>
		<c:if test="${'N'==requestScope.acctMaintenance.statusflag}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountenablebutton").innerHTML;

</c:if>
		<c:if test="${'Y'==requestScope.acctMaintenance.statusflag}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountdisablebutton").innerHTML;

</c:if>

</c:if>
		<c:if test="${'add'==requestScope.methodName}">

		<c:if test="${'Y'!=requestScope.showLoroToPredicted}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountdisablebutton").innerHTML;
		document.getElementById("linkbutton").innerHTML = document.getElementById("linkdisablebutton").innerHTML;
		//var obj = document.forms[0].elements['acctMaintenance.loroToPredicted'];
		//obj.disabled = true;

</c:if>

</c:if>
		if (isChrome) {
			var myElement = document.getElementById("secondaryExternal");
			myElement.setAttribute("style", "width:130px;position:relative;");
		}

		<c:if test="${'view'==requestScope.methodName}">

		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;


</c:if>
		<c:if test="${'view'!=requestScope.methodName}">

		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;


</c:if>

		document.getElementById("usedInOtherSize").value = "${requestScope.usedInOtherSweepCount}";

		document.getElementById("assAcctForSweepBal").value = "${requestScope.acctSweepBalCount}";

		getSpecificAccountSweepSize();
		getAssAcctSweepBalGrpSize();


	}

	function createGridData(data) {

		try {
			if (data === undefined || data == null || data.length <= 0) {
				return;
			}

			var obj = JSON.parse(data);
			var res = [];
			var rowData = "";
			var j = 0;

// 	        $('#tableAcctSchedule').innerHTML = "";
			var elmtTable = document.getElementById('tableAcctSchedule');
			var tableRows = elmtTable.getElementsByTagName('tr');
			var rowCount = tableRows.length;

			for (var x = rowCount - 1; x > 0; x--) {
				elmtTable.removeChild(tableRows[x]);
			}

			$("#tableAcctSchedule").children().remove();


			const elements = document.getElementsByClassName("sort-arrow");
			while (elements.length > 0) {
				elements[0].parentNode.removeChild(elements[0]);
			}


			for (var i in obj) {

				if (obj[i] != null && obj[i].accountId != null) {
					rowData = "";
					if (j % 2 == 0) {
						rowData += '<tr  class="even">'
					} else {
						rowData += '<tr  class="odd"> '

					}

					if (getValueOf(obj[i].targetBalanceType) == "C" || getValueOf(obj[i].targetBalanceType) == "D") {
						var balance = getValueOf(obj[i].targetBalanceAsString);
					} else {
						var balance = getValueOf(obj[i].targetBalanceTypeId);
					}
					j++;
					rowData += '<td align="center" width="68px"><input type="hidden" name="sweepScheduleId" value="' + obj[i].sweepScheduleId + '"/>' + getValueOf(obj[i].scheduleFrom) + '&nbsp;</td>';

					rowData += '<td width="68px" align="center"><input type="hidden" name="uniqueId" value="' + obj[i].uniqueId + '"/>' + getValueOf(obj[i].scheduleTo) + '&nbsp;</td>';
					rowData += '<td width="83px" align="left">' + getValueOf(obj[i].sweepFromBalanceTypeAsString) + '&nbsp;</td>';
					rowData += '<td width="83px" align="left">' + getValueOf(obj[i].sweepOnGroupBalance) + '&nbsp;</td>';
					rowData += '<td width="83px" align="left">' + getValueOf(obj[i].targetBalanceTypeAsString) + '&nbsp;</td>';
					rowData += '<td width="150px" align="right">' + balance + '&nbsp;</td>';
					rowData += '<td width="160px" align="left">' + getValueOf(obj[i].sweepDirectionAsString) + '&nbsp;</td>';
					rowData += '<td width="100px" align="right">' + getValueOf(obj[i].minAmountAsString) + '&nbsp;</td>';
					rowData += '<td width="85px" align="left">' + getValueOf(obj[i].allowMultipleAsString) + '&nbsp;</td>';
					rowData += '<td width="100px" align="left">' + getValueOf(obj[i].thisAccSweepBookcodeCr) + '&nbsp;</td>';
					rowData += '<td width="100px" align="left">' + getValueOf(obj[i].thisAccSweepBookcodeDr) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].thisAccSettleMethodCr) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].thisAccSettleMethodDr) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].sweepAccountEntity) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].sweepAccountId) + '&nbsp;</td>';
					rowData += '<td width="83px" align="left">' + getValueOf(obj[i].otherSweepFromBalTypeAsString) + '&nbsp;</td>';
					rowData += '<td width="100px" align="left">' + getValueOf(obj[i].otherAccSweepBookcodeCr) + '&nbsp;</td>';
					rowData += '<td width="100px" align="left">' + getValueOf(obj[i].otherAccSweepBookcodeDr) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].otherAccSettleMethodCr) + '&nbsp;</td>';
					rowData += '<td width="130px" align="left">' + getValueOf(obj[i].otherAccSettleMethodDr) + '&nbsp;</td>';
					rowData += '</tr>';

					$('#tableAcctSchedule').append(rowData);
				}


			}

			if (xl != null) {
				xl.recreateFilters("acctSweepSchedulecoll", "table_2", ["String", "String", "String", "String", "Number", "String", "String", "String", "Number", "Number", "String", "String", "String", "String", "String", "String", "String", "String", "String"], "*****************");


				highlightTableRows("acctSweepSchedulecoll");
			}
		} catch (e) {
			console.log(e);
		}


	}

	function getValueOf(val) {
		return (val === undefined || val == null || val.length <= 0) ? "" : val;
	}

	function checkSweepRadioStatus() {
		var checkBoxFlag = document.forms[0].elements["acctMaintenance.mansweepflg"].checked;
		var radio1flag = document.forms[0].elements["acctMaintenance.autoswpswitch"][0].checked;
		var radio2flag = document.forms[0].elements["acctMaintenance.autoswpswitch"][1].checked;
		var radio3flag = document.forms[0].elements["acctMaintenance.autoswpswitch"][2].checked;
		if ((radio3flag == true) && (checkBoxFlag == false)) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * This method is called when click on sub account
	 *
	 * @Param methodname
	 */
	function buildSubAccount(methodName) {
		var subAccountsPresent = '${requestScope.subAccountsPresent}';
		if (subAccountsPresent != "yes") {
			alert('<fmt:message key="accountmaintenance.NoSubAc"/>');
		} else {
			var param = 'acctMaintenance.do?method=' + methodName;
			param += '&entityCode=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
			param += '&selectedAccountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
			param += '&selectedCurrencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
			//Changing WIDTH of window so that 'Class' column is also visible
			javascript:openWindow(param, 'subaccountswindow', 'left=50,top=190,width=985,height=610,toolbar=0, resizable=yes, scrollbars=yes', 'true');
		}
	}


	/**
	 * This method is called when click on sub account
	 *
	 * @Param methodname
	 */
	function buildSpecificAccount() {
		var param = 'accountSpecificSweepFormat.do?method=getSpecificAccounts';
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&accountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&currencyId=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		param += '&parentMethodName=' + '${requestScope.methodName}';
		param += '&id=' + getCrsfMetaTagContent();
		//Changing WIDTH of window so that 'Class' column is also visible
		javascript:openWindow(param, 'subaccountswindow', 'left=50,top=190,width=1110,height=610,toolbar=0, resizable=yes, scrollbars=yes', 'true');
	}


	window.addEventListener("message", receiveMessage, false);


	function receiveMessage(e) {
		if (e && e.data) {
			var methodName = e.data[0];
			if (methodName == "getSpecificAccountSweepSize") {
				getSpecificAccountSweepSize();
			} else if (methodName == "getAssAcctSweepBalGrpSize") {
				getAssAcctSweepBalGrpSize();
			}
		}

	}

	function buildAccsSweepsScheduleUrl() {

		var param = 'acctMaintenance.do?method=getAccsSweepSchedule';
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&accountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&currencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		param += '&id=' + getCrsfMetaTagContent();
		//Changing WIDTH of window so that 'Class' column is also visible
		javascript:openWindow(param, 'subaccountswindow', 'left=50,top=190,width=900,height=345,toolbar=0, resizable=yes, scrollbars=yes', 'true');
	}


	function getSpecificAccountSweepSize() {
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName + "/accountSpecificSweepFormat.do?method=getSpecificAccountSweepSize";
		sURL = sURL + "&id=" + getCrsfMetaTagContent();
		sURL = sURL + "&entityId=" + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		sURL = sURL + "&accountId=" + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		sURL = sURL + "&parentMethodName=" + '${requestScope.methodName}';
		oXMLHTTP.open("POST", sURL, false);
		oXMLHTTP.send();
		var str = oXMLHTTP.responseText;
		document.getElementById("subsweepaccountsize").value = parseInt(str) || 0;
		;
		return str;
	}


	function getAssAcctSweepBalGrpSize() {
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName + "/acctMaintenance.do?method=getAssAcctSweepBalGrpSize";
		requestURL = requestURL + "&accountId=" + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		requestURL = requestURL + "&parentMethodName=" + '${requestScope.methodName}';

		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		var str = oXMLHTTP.responseText;
		document.getElementById("assAcctForSweepBal").value = parseInt(str) || 0;
		return str;
	}


	function checkSubAccounts(element) {
// 		if(document.forms[0].elements["acctMaintenance.acctlevel"][0].checked == false) {
// 			document.forms[0].elements["acctMaintenance.subAcctim"].disabled = "true";
// 			document.forms[0].elements["acctMaintenance.subAcctim"].checked=false;
// 		}
// 		else {
// 			document.forms[0].elements["acctMaintenance.subAcctim"].disabled = "";
// 		}
		accountLevel = element.value
		if (element.value == "<%=SwtConstants.ACCT_SUB%>") {
			var subAccountsPresent = '${requestScope.subAccountsPresent}';
			if (subAccountsPresent == "yes") {
				alert('<fmt:message key="accountmaintenance.AccountLevelCannotChanged"/>');
				var radioButton = document.forms[0].elements["acctMaintenance.acctlevel"][0];
				radioButton.checked = true;
				document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountenablebutton").innerHTML;
				document.forms[0].elements["accountIdMandatory"].value = "";
				document.getElementById("dropdownbutton_2").disabled = "true";
				accountLevel = "<%=SwtConstants.ACCT_MAIN%>";
			} else {
				document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountdisablebutton").innerHTML;
				document.forms[0].elements["accountIdMandatory"].value = "*";
				document.getElementById("dropdownbutton_2").disabled = "";
			}
		} else {
			document.forms[0].elements["mainaccountName"].value = "";
			document.forms[0].elements["mainaccountId"].value = "";
			document.forms[0].elements["acctMaintenance.minacctcode"].value = "";
			document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountenablebutton").innerHTML;
			document.forms[0].elements["accountIdMandatory"].value = "";
			document.getElementById("dropdownbutton_2").disabled = "true";
		}
		<c:if test="${'add'==requestScope.methodName}">

		document.getElementById("subaccountbutton").innerHTML = document.getElementById("subaccountdisablebutton").innerHTML;

</c:if>
	}

	function getLinkAccounts(methodName) {
		var param = 'acctMaintenance.do?method=' + methodName;
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&accountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&selectedCurrencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		return param;
	}

	function addContactDetails(methodName) {
		var param = 'acctMaintenance.do?method=' + methodName;
		param += '&entityCode=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&contactName=' + document.forms[0].contactName.value;
		param += '&contactPhoneNumber=' + document.forms[0].contactPhoneNumber.value;
		param += '&contactEmailAddr=' + document.forms[0].contactEmailAddr.value;
		param += '&screenStatus=' + '${methodName}';
		return param;
	}

	function addAccInterestRateDetail(methodName) {
		var param = 'acctMaintenance.do?method=' + methodName;
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&relatedToaccountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&screenStatus=' + '${methodName}';
		return param;
	}


	function changeStatusOpenUnexpected(value) {

		var obj = document.forms[0].elements['acctMaintenance.thisEntityInclBalFlag'];

		if (obj.options.length > 0) {
			for (var index = 0; index < 4; index++) {
				obj.options.remove(1);
			}
		}

		if (value == "L" || value == "C") {
			var opt = document.createElement("option");
			opt.text = "Also include Loro in Predicted balance by addition";
			opt.value = "A+";
			var opt1 = document.createElement("option");
			opt1.text = "Also include Loro in Predicted balance by subtraction";
			opt1.value = "A-";
			var opt2 = document.createElement("option");
			opt2.text = "Move Loro to Predicted balance by addition";
			opt2.value = "M+";
			var opt3 = document.createElement("option");
			opt3.text = "Move Loro to Predicted balance by subtraction";
			opt3.value = "M-";
			obj.options.add(opt);
			obj.options.add(opt1);
			obj.options.add(opt2);
			obj.options.add(opt3);
			//obj.option = false;
		} else {
			var opt = document.createElement("option");
			opt.text = "Also include Predicted in Loro balance by addition";
			opt.value = "A+";
			var opt1 = document.createElement("option");
			opt1.text = "Also include Predicted in Loro balance by subtraction";
			opt1.value = "A-";
			var opt2 = document.createElement("option");
			opt2.text = "Move Predicted to Loro balance by addition";
			opt2.value = "M+";
			var opt3 = document.createElement("option");
			opt3.text = "Move Predicted to Loro balance by subtraction";
			opt3.value = "M-";
			obj.options.add(opt);
			obj.options.add(opt1);
			obj.options.add(opt2);
			obj.options.add(opt3);
			//obj.checked = false; // uncheck the Open Unexpected checkbox
			//obj.disabled = true;
		}
	}

	function getIntermediaryRecord(element) {
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
		var entityId = document.forms[0].elements["acctMaintenance.id.entityId"].value;
		document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
		var currencyCode = document.forms[0].elements["acctMaintenance.currcode"].value;
		var acctbiccode = document.forms[0].elements["acctMaintenance.acctbiccode"].value.trim();
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName + "/acctMaintenance.do?method=getIntermediaryRecord";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&currencyCode=" + currencyCode;
		requestURL = requestURL + "&acctbiccode=" + acctbiccode;
		var intermediaryBic = "";
		if (isValid(element, 'alphaNumPat') && element.value != "") {
			var flag = isValid(element, 'alphaNumPatWithUppercase');
			if (flag) {
				intermediaryBic = sendRequest(requestURL);
			}
		}
		if (intermediaryBic == "null") {
			intermediaryBic = "";
		}
		document.forms[0].elements["sweepintermediaries.intermediary"].disabled = "";
		document.forms[0].elements["sweepintermediaries.intermediary"].value = intermediaryBic;
		document.forms[0].elements["sweepintermediaries.intermediary"].disabled = "true";
	}


	function sendRequest(sURL) {
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", sURL, false);
		oXMLHTTP.send();
		var count = new String(oXMLHTTP.responseText);
		return count;
	}

	/**
	 * disableintra
	 *
	 * This method is used to disable the eod,intradaysweep time.
	 */
	function disableintra() {
		if (document.forms[0].elements["acctMaintenance.subAcctim"].checked == true) {
// 			document.forms[0].elements["acctMaintenance.eodSweeptime"].disabled = "true";
// 			document.forms[0].elements["acctMaintenance.intraDaySweeptime"].disabled = "true";
		} else {
// 			document.forms[0].elements["acctMaintenance.eodSweeptime"].disabled = "";
// 			document.forms[0].elements["acctMaintenance.intraDaySweeptime"].disabled = "";
		}
	}

	function onFocusFirstField() {
		if (!document.forms[0].elements["acctMaintenance.id.accountId"].disabled) {
			document.forms[0].elements["acctMaintenance.id.accountId"].focus();
		} else if (!document.forms[0].elements["acctMaintenance.acctname"].disabled) {
			document.forms[0].elements["acctMaintenance.acctname"].focus();
		} else {
			document.getElementById("firstsc").focus();
		}
	}

	/**
	 * getLinkAccountList
	 *
	 * This method is used to get the collection of link account.
	 */
	function getLinkAccountList(event) {
		if (linkAcctclk) {
			//get the all elements for selectbox
			var currentAccountId = document.forms[0].elements['acctMaintenance.id.accountId'].value;
			var divElement = document.getElementById("dropdowndiv_link");
			var idElement = document.forms[0].elements["linkaccountId"];
			var descElement = document.forms[0].elements["linkaccountName"];
			var arrowElement = document.forms[0].elements["dropdownbutton_link"];
			linkAccSelectElement = document.forms[0].elements["acctMaintenance.linkAccID"];
			var idLength = 12;
			var descLength = 30;
			var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
			var entityId = document.forms[0].elements["acctMaintenance.id.entityId"].value;
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
			var currencyCode = document.forms[0].elements["acctMaintenance.currcode"].value;
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			requestURL = requestURL.substring(0, idy + 1);
			//frame the requestURL
			requestURL = requestURL + appName + "/acctMaintenance.do?method=getLinkAccountList";
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			requestURL = requestURL + "&currentAccountId=" + currentAccountId;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			var listValue = new String(oXMLHTTP.responseText);
			if (listValue == "null") {
				alert('<fmt:message key="accountmaintenance.alreadyLinked"/>');
			} else {
				listValue = listValue.split('\n');
				if (linkAccSelectElement.options.length > 0) {
					linkAccSelectElement.remove(0);
				}
				//add the link account into option
				var index = 0;
				for (var i = 0; i < listValue.length - 1; i++) {
					var lblValue = listValue[i].split('~~~');
					/*Start Code:Modified by Atef for Mantis 2012:Same account displayed in linked account drop down in change account maintenance screen */
					if (lblValue[1] == currentAccountId)
						continue;
					/*End Code:Modified by Atef for Mantis 2012:Same account displayed in linked account drop down in change account maintenance screen */
					var opt = document.createElement("option");
					opt.text = lblValue[0];
					opt.value = lblValue[1];
					if (opt.value == linkAcctId)
						index = i;
					linkAccSelectElement.options.add(opt);
				}
				//set the selected index
				linkAccSelectElement.selectedIndex = index;
				//frame the selectbox component
				linkSwSel = new SwMainSelectBox(divElement, linkAccSelectElement, idElement, descElement, arrowElement, idLength, descLength);
				linkSwSel.setClickFlag();
				//call to populate the list in the list box
				linkSwSel.arrowElementOnClick(event);
				linkAcctclk = false;
			}
		}
	}

	/**
	 * getMainAccountList
	 *
	 * This method is used to get the collection of main account list.
	 */
	function getMainAccountList(event) {
		if (mainAcctclk) {
			//get the all elements for selectbox
			var divElement = document.getElementById("dropdowndiv_2");
			var idElement = document.forms[0].elements["mainaccountId"];
			var descElement = document.forms[0].elements["mainaccountName"];
			var arrowElement = document.forms[0].elements["dropdownbutton_2"];
			mainAccSelectElement = document.forms[0].elements["acctMaintenance.minacctcode"];
			var mainAccidLength = 12;
			var mainAccdescLength = 30;
			var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
			var entityId = document.forms[0].elements["acctMaintenance.id.entityId"].value;
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
			var currencyCode = document.forms[0].elements["acctMaintenance.currcode"].value;
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			//frame the requestURL
			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName + "/acctMaintenance.do?method=getMainAccountList";
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			var listValue = new String(oXMLHTTP.responseText);
			listValue = listValue.split('\n');
			if (mainAccSelectElement.options.length > 0) {
				mainAccSelectElement.remove(0);
			}
			var index = 0;
			//add the main account into option
			for (var i = 0; i < listValue.length - 1; i++) {
				var lblValue = listValue[i].split('~~~');
				var opt = document.createElement("option");
				opt.text = lblValue[0];
				opt.value = lblValue[1];
				if (opt.value == mainAcctId)
					index = i;
				mainAccSelectElement.options.add(opt);
			}
			//set the selected index
			mainAccSelectElement.selectedIndex = index;
			//frame the selectbox component
			mainSwSel = new SwMainSelectBox(divElement, mainAccSelectElement, idElement, descElement, arrowElement, mainAccidLength, mainAccdescLength);
			mainSwSel.setClickFlag();
			//call to populate the list in the list box
			mainSwSel.arrowElementOnClick(event);
			mainAcctclk = false;
		}
	}

	/**
	 * getBookCodeList
	 *
	 * This method is used to get the collection of book code list.
	 */
	function getBookCodeList(event) {
		if (bookCodeclk) {
			//get the all elements for selectbox
			var divElement = document.getElementById("dropdowndiv_3");
			var idElement = document.forms[0].elements["bookCodeId"];
			var descElement = document.forms[0].elements["bookCodeNameDisp"];
			var arrowElement = document.forms[0].elements["dropdownbutton_3"];
			bookSelectElement = document.forms[0].elements["acctMaintenance.sweepbookcode"];
			var idLength = 12;
			var descLength = 30;
			var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
			var entityId = document.forms[0].elements["acctMaintenance.id.entityId"].value;
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
			var currencyCode = document.forms[0].elements["acctMaintenance.currcode"].value;
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			//frame the requestURL
			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName + "/acctMaintenance.do?method=getBookCodeList";
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			var listValue = new String(oXMLHTTP.responseText);
			listValue = listValue.split('\n');
			if (bookSelectElement.options.length > 0) {
				bookSelectElement.remove(0);
			}
			var index = 0;
			//add the book code into option
			for (var i = 0; i < listValue.length - 1; i++) {
				var lblValue = listValue[i].split('~~~');
				var opt = document.createElement("option");
				opt.text = lblValue[0];
				opt.value = lblValue[1];
				if (opt.value == bookCode) {
					index = i;
				}
				bookSelectElement.options.add(opt);
			}

			//set the selected index
			bookSelectElement.selectedIndex = index;
			//frame the selectbox component
			bookSwSel = new SwMainSelectBox(divElement, bookSelectElement, idElement, descElement, arrowElement, idLength, descLength);
			bookSwSel.setClickFlag();
			//call to populate the list in the list box
			bookSwSel.arrowElementOnClick(event);
			bookCodeclk = false;
		}
	}

	/**
	 * getCountryList
	 *
	 * This method is used to get the collection of country list.
	 */
	function getCountryList() {
		if (countryclk) {
			//get the element for selectbox
			var selectElement = document.forms[0].elements["acctMaintenance.holidaycalendar"];
			var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			//frame the requestURL
			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName + "/acctMaintenance.do?method=getCountryList";
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			var listValue = new String(oXMLHTTP.responseText);
			listValue = listValue.split('\n');
			if (selectElement.options.length > 0) {
				selectElement.remove(0);
			}
			var index = 0
			//add the country into option
			for (var i = 0; i < listValue.length - 1; i++) {
				var lblValue = listValue[i].split('~~~');
				var opt = document.createElement("option");
				opt.text = lblValue[0];
				opt.value = lblValue[1];
				if (opt.value == countryId) {
					index = i;
				}
				selectElement.options.add(opt);
			}
			//set the selected index
			selectElement.selectedIndex = index;
			countryclk = false;
		}
	}


	var formatClk = true;
	var formatLen = 0;
	var listValue;

	/**
	 * getFormatList
	 *
	 * This method is used to get the collection of message format list.
	 */
	function getFormatList(element) {
		var firstElement = null;
		var hiddenEle = element.name.split('.')[1];
		if (formatClk) {
			//get the element for selectbox
			firstElement = element;
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
			var entityId = document.forms[0].elements["acctMaintenance.id.entityId"].value;
			document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "true";
			var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			//frame the requestURL
			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName + "/acctMaintenance.do?method=getFormatList";
			requestURL = requestURL + "&entityId=" + entityId;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			listValue = new String(oXMLHTTP.responseText);
			listValue = listValue.split('\n');
			if (firstElement.options.length > 0) {
				firstElement.remove(0);
			}
			//add the message format into option
			var index = 0;
			for (var i = 0; i < listValue.length - 1; i++) {
				var lblValue = listValue[i].split('~~~');
				var opt = document.createElement("option");
				opt.text = lblValue[0];
				opt.value = lblValue[1];
				if (document.forms[0].elements[hiddenEle] != null) {
					if (opt.value == document.forms[0].elements[hiddenEle].value) {
						index = i;
					}
				}
				firstElement.options.add(opt);
			}
			formatLen = firstElement.options.length;
			//set the selected index
			firstElement.selectedIndex = index;
			formatClk = false;
		} else {
			if (element.options.length != formatLen) {
				if (element.options.length > 0)
					element.remove(0);
				var index = 0
				for (var i = 0; i < listValue.length - 1; i++) {
					var lblValue = listValue[i].split('~~~');
					var opt = document.createElement("option");
					opt.text = lblValue[0];
					opt.value = lblValue[1];
					if (document.forms[0].elements[hiddenEle] != null) {
						if (opt.value == document.forms[0].elements[hiddenEle].value) {
							index = i;
						}
					}
					element.options.add(opt);
				}
				//set the selected index
				element.selectedIndex = index;
				formatClk = false;
			}
		}
	}

	/**
	 * changeCurrencyCode
	 *
	 * This method is called while changing the currency code to empty the collection in list box.
	 */
	function changeCurrencyCode() {
		linkAcctclk = true;
		//clear the link account list && value
		if (linkAccSelectElement != null && linkAccSelectElement.options != null)
			linkAccSelectElement.options.length = 0;
		document.forms[0].elements["linkaccountId"].value = "";
		document.forms[0].elements["linkaccountName"].value = "";
		if (linkSwSel != null) {
			linkSwSel.arrowElement.onclick = getLinkAccountList;
		}
		mainAcctclk = true;
		//clear the  main account list && value
		if (mainAccSelectElement != null && mainAccSelectElement.options != null)
			mainAccSelectElement.options.length = 0;
		document.forms[0].elements["mainaccountId"].value = "";
		document.forms[0].elements["mainaccountName"].value = "";
		if (mainSwSel != null) {
			mainSwSel.arrowElement.onclick = getMainAccountList;
		}
		bookCodeclk = true;
		//clear the  book code list && value
		if (bookSelectElement != null && bookSelectElement.options != null)
			bookSelectElement.options.length = 0;
		document.forms[0].elements["bookCodeId"].value = "";
		document.forms[0].elements["bookCodeNameDisp"].value = "";
		if (bookSwSel != null) {
			bookSwSel.arrowElement.onclick = getBookCodeList;
		}

	}

	//Added by Mefteh for Mantis 2110: To validate the IBAN code
	function validateIBAN() {
		// Validate the IBAN format
		var IBANCode = document.forms[0].elements['acctMaintenance.acctIBAN'].value;
		if (IBANCode.length == 0)
			return true;

		var isValidIBAN = validateField(document.forms[0].elements["acctMaintenance.acctIBAN"], 'acctIBAN', 'alphaNumPatWithSpace_IBAN');
		if (!isValidIBAN) {
			document.forms[0].elements["acctMaintenance.acctIBAN"].focus();
			return false;
		} else {
			IBANCode = IBANCode.replace(new RegExp(" ", "g"), "");
			var paperIBANFomat = '';
			while (IBANCode.length > 0) {
				paperIBANFomat += IBANCode.substring(0, 4) + ' ';
				IBANCode = IBANCode.substring(4);
			}
			document.forms[0].elements['acctMaintenance.acctIBAN'].value = paperIBANFomat;

			// Validate the IBAN value

			var iban = document.forms[0].elements['acctMaintenance.acctIBAN'].value;
			var ibanValidationModulo = 97;
			// covert to basic (electronic) format by deleting all non-alphanumeric characters ( spaces ).
			iban = iban.replace(new RegExp(" ", "g"), "");
			//Check that the total IBAN length is correct as per the country. This information is stored in the new field S_COUNTRY.IBAN_LENGTH.
			//If the length is null || does not match, the IBAN is invalid*/
			var IBANPrefix = iban.substring(0, 2);
			var IBANCodeLength = iban.length;
			<%
             Iterator itr=countryList.iterator();
             while(itr.hasNext()){
                 Country	country = (Country)(itr.next());
                 %>
			var countryCod = '<%=country.getCountryCode().trim() %>';
			var IBANlength = '<%=country.getIBANLength() %>';
			if (IBANPrefix == countryCod) {
				if (IBANCodeLength != IBANlength.trim()) {
					alert('<fmt:message key="alert.ibanFormatInvalid"/>');
					document.forms[0].elements["acctMaintenance.acctIBAN"].focus();
					return false;
				}
			}
			<%
        }
        %>
			// Move the first four characters of the IBAN to the right of the number.
			modifiedIban = iban.substring(4, iban.length) + iban.substr(0, 4);
			// Convert the letters to numeric where A=10,B=11, C=12,....,Z=35
			numericIbanString = "";
			for (var index = 0; index < modifiedIban.length; index++) {
				currentChar = modifiedIban.charAt(index);
				currentCharCode = modifiedIban.charCodeAt(index);
				// if the character is a digit, then copy it
				if ((currentCharCode > 47) && (currentCharCode < 58)) {
					numericIbanString = numericIbanString + currentChar;
				}
				// if the character is a character, convert it to value
				else if ((currentCharCode > 64) && (currentCharCode < 91)) {
					value = currentCharCode - 65 + 10;
					numericIbanString = numericIbanString + value;
				}
				// else, the IBAN code is invalid (invalid character).
				else {
					alert('<fmt:message key="alert.ibanFormatInvalid"/>');
					document.forms[0].elements["acctMaintenance.acctIBAN"].focus();
					return false;
				}
			}
			//To calculate the modulo, we must go step by step:- Number is cut into blocks of 5 digits. - For each block, with the prefix modulo the previous block
			//(So we made a number of 7 digits , manageable javascript ).
			var previousModulo = 0;
			for (var index = 0; index < numericIbanString.length; index += 5) {
				subpart = previousModulo + "" + numericIbanString.substr(index, 5);
				previousModulo = subpart % ibanValidationModulo;
			}
			if (previousModulo != 1) {
				alert('<fmt:message key="alert.ibanFormatInvalid"/>');
				document.forms[0].elements["acctMaintenance.acctIBAN"].focus();
				return false;
			} else
				return true;
		}
	}

	function displayAttributesDetails(methodName) {
		var param = 'accountAttribute.do?method=' + methodName;
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&entityName=' + document.getElementById("entityDesc").innerText;
		param += '&accountId=' + document.forms[0].elements["acctMaintenance.id.accountId"].value;
		param += '&accountName=' + document.forms[0].elements['acctMaintenance.acctname'].value;
		param += '&currencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		return param;
	}

	/**
	 * party
	 *
	 * This method is used to open the party search screen when click on the
	 * counterparty/matching party drop down button
	 **/
	function party(flag, elementId, elementName) {
		var entityId = document.forms[0].elements['acctMaintenance.id.entityId'].value;
		var partyURL = 'party.do?method=preSearchParties&entityId=' + entityId;
		partyURL += '&custodianFlag=' + flag;
		partyURL += '&idElementName=' + elementId;
		partyURL += '&descElementName=' + elementName;
		openWindow(partyURL, 'SearchParty', 'left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes', 'true');
	}

	/**
	 * updatePartyDesc
	 *
	 * This method is called on change of the matching party && used to clear the
	 * description of the matchingparty
	 **/
	function updatePartyDesc() {
		var accountPartyId = document.forms[0].elements['acctMaintenance.accountPartyId'].value;
		if (accountPartyId == "") {
			document.getElementById("partyDesc").innerText = "";
		}
		var entityId = document.forms[0].elements['acctMaintenance.id.entityId'].value;

		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName + "/acctMaintenance.do?method=getPartyNameForAjax";
		requestURL = requestURL + "&entityId=" + entityId;
		requestURL = requestURL + "&partyId=" + accountPartyId;

		var partyDesc = sendRequest(requestURL);
		if (partyDesc != "null") {
			document.getElementById("partyDesc").innerText = partyDesc;
		} else {
			document.getElementById("partyDesc").innerText = "";
		}
	}

	/**
	 * Added by Idriss for M 3407, note 13897
	 * This function called each time the to detect if an account is a member of an ILM group
	 */
	function checkIfAccountIsIlmDataMember(methodName) {

		var archiveDataCheckBox = document.forms[0].elements['acctMaintenance.archiveData'];
		if (archiveDataCheckBox.checked == true) {
			//nothing to do
		} else {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');
			requestURL = requestURL.substring(0, idy + 1);
			var oXMLHTTP = new XMLHttpRequest();
			//framing url
			var sURL = requestURL + appName + "/acctMaintenance.do?method=" + methodName;
			sURL = sURL + "&selectedCurrencyCode=" + selectedCurrencyCode;
			sURL = sURL + "&selectedEntityName=" + document.forms[0].elements['acctMaintenance.id.entityId'].value;
			sURL = sURL + "&selectedAccountId=" + document.forms[0].elements['acctMaintenance.id.accountId'].value;
			oXMLHTTP.open("POST", sURL, false);
			//sending request
			oXMLHTTP.send();
			//getting the response
			var value = oXMLHTTP.responseText;
			// if value = N : the account  is a member of an ILM group.
			if (value == 'N') {
				alert('<fmt:message key="accountmaintenance.DeletionWithoutArchivingIsRequired"/>');
			} else {
				alert('<fmt:message key="accountmaintenance.AccountIsIlmDataMember"/>');
			}
		}
	}


	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e) {
		var event = (window.event || e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while (srcEl.tagName != 'TD') {
			srcEl = srcEl.parentElement;
		}

		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;

		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);

		if (isRowSel == false)
			rowElement.className = 'selectrow';

		onSelectTableRow(rowElement, !isRowSel);
	}

	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected
	 **/
	function onSelectTableRow(rowElement, isSelected) {
		var hiddenElement = rowElement.getElementsByTagName("input")[0];
		var hiddenElement2 = rowElement.getElementsByTagName("input")[1];

		document.forms[0].sweepScheduleId.value = hiddenElement.value;
		document.forms[0].sweepAccountId.value = hiddenElement2.value;
		if (isSelected)
			sweepScheduleId = hiddenElement2.value;
		else
			sweepScheduleId = "";
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		<c:if test="${'view'!=requestScope.methodName}">

		document.getElementById("viewbutton").innerHTML = (isSelected ? document.getElementById("viewenablebutton").innerHTML : document.getElementById("viewdisablebutton").innerHTML);
		document.getElementById("changebutton").innerHTML = (isSelected ? document.getElementById("changeenablebutton").innerHTML : document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML = (isSelected ? document.getElementById("deleteenablebutton").innerHTML : document.getElementById("deletedisablebutton").innerHTML);


</c:if>
		<c:if test="${'view'==requestScope.methodName}">

		document.getElementById("viewbutton").innerHTML = (isSelected ? document.getElementById("viewenablebutton").innerHTML : document.getElementById("viewdisablebutton").innerHTML);
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

</c:if>
	}

	var sweepScheduleId = "";

	function submitDeleteForm(methodName) {

		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName + "/acctMaintenance.do?method=deleteAccountScheduleSweep";
		sURL = sURL + "&id=" + getCrsfMetaTagContent();
		sURL = sURL + "&entityId=" + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		sURL = sURL + "&accountId=" + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		sURL = sURL + "&seqNumber=" + sweepScheduleId;
		sURL = sURL + "&currencyCode=" + document.forms[0].elements['acctMaintenance.currcode'].value;
		sURL = sURL + "&callerMethod=" + methodName;
// 		sURL = sURL + "&allRecords="+ asJson;

		oXMLHTTP.open("POST", sURL, false);
		oXMLHTTP.send();
		var str = oXMLHTTP.responseText;

		createGridData(str)

		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;


	}

	function updateData() {
		var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');
		requestURL = requestURL.substring(0, idy + 1);

		var oXMLHTTP = new XMLHttpRequest();
		var sURL = requestURL + appName + "/acctMaintenance.do?method=getAccountScheduleSweepList";
		sURL = sURL + "&id=" + getCrsfMetaTagContent();
		sURL = sURL + "&entityId=" + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		sURL = sURL + "&accountId=" + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		sURL = sURL + "&currencyCode=" + document.forms[0].elements['acctMaintenance.currcode'].value;
		sURL = sURL + "&callerMethod=" + methodName;

		oXMLHTTP.open("POST", sURL, false);
		oXMLHTTP.send();
		var str = oXMLHTTP.responseText;

		createGridData(str)

		//disable buttons
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	}

	function buildAccsSweepsScheduleDetailsUrl(method) {
		var sweepScheduleIdToUrl = "";
		if (method != "add") {
			sweepScheduleIdToUrl = sweepScheduleId;
		} else {
			sweepScheduleIdToUrl = acctScheduleNextVal;
			acctScheduleNextVal++;
		}
		var param = 'acctMaintenance.do?method=acctScheduleSweepDetails';
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&accountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&currencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		param += '&methodName=' + method;
		param += '&parentMethodName=' + '${methodName}'
		param += '&defaultTargetBalance=' + document.getElementById("trgtBal").value;
		param += '&defaultTargetBalanceType=' + document.forms[0].elements["acctMaintenance.tgtbalsign"].value;
		param += '&defaultBookCode=' + document.forms[0].elements["acctMaintenance.sweepbookcode"].value;
		param += '&defaultSettlementMethod=' + document.forms[0].elements["acctMaintenance.defaultSettleMethod"].value;
		param += '&defaultMinAmount=' + document.forms[0].elements["acctMaintenance.minseepamtasString"].value;
		param += '&defaultFromBalanceType=' + document.forms[0].elements["acctMaintenance.sweepFrmbal"].value;
		param += '&defaultOtherFromBalType=' + document.forms[0].elements["acctMaintenance.sweepFrmbal"].value;
		param += '&maxAmoutValue=' + document.getElementById("maxAmountField").value;
		param += '&seqNumber=' + sweepScheduleIdToUrl;
		param += '&id=' + getCrsfMetaTagContent();
		//Changing WIDTH of window so that 'Class' column is also visible
		javascript:openWindow(param, 'subaccountswindow', 'left=50,top=190,width=1420,height=545,toolbar=0, resizable=yes, scrollbars=yes', 'true');
	}


	function getRecordsTiming() {
		var table = document.getElementById("tableAcctSchedule");
		var listOfTimings = [];


		for (i = 0; i < table.rows.length; i++) {
			var from = table.rows[i].cells[0].innerText.trim();
			var to = table.rows[i].cells[1].innerText.trim();
			var elem = {
				"startTime": from,
				"endTime": to,
			}
			listOfTimings.push(elem);


		}
		return listOfTimings;
	}

	/**
	 * This method is called when click on maintain button
	 *
	 * @Param methodname
	 */
	function buildSpecificAcctSweepBalGrp() {
		var param = 'acctMaintenance.do?method=handleAcctSweepBalGrp';
		param += '&entityId=' + document.forms[0].elements['acctMaintenance.id.entityId'].value;
		param += '&currencyCode=' + document.forms[0].elements['acctMaintenance.currcode'].value;
		param += '&accountId=' + document.forms[0].elements['acctMaintenance.id.accountId'].value;
		param += '&methodName=' + methodName;
		//Changing WIDTH of window so that 'Class' column is also visible
		javascript:openWindow(param, 'acctsweepbalgrpwindow', 'left=50,top=190,width=660,height=350,toolbar=0, resizable=yes, scrollbars=yes', 'true');

	}

</SCRIPT>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();"
	onunload="call()" style="overflow: hidden;">
<form action="acctMaintenance.do" method="post" onsubmit="validate(this);">
	<input name="oldValue" type="hidden" value="${oldValue}">
	<input name="method" type="hidden" value="displayListByEntity">
	<input name="selectedAccountId" type="hidden" value="GBP">
	<input name="selectedCurrencyCode" type="hidden" value="">
	<input name="newAcctId" type="hidden" value="">
	<input name="screenStatus" type="hidden">
	<input name="selectedEntityName" type="hidden">
	<input name="contactName" type="hidden"
		value='${requestScope.contactName}'>
	<input name="contactPhoneNumber" type="hidden"
		value='${requestScope.contactPhoneNumber}'>
	<input name="contactEmailAddr" type="hidden"
		value='${requestScope.contactEmailAddr}'>
	<input name="acctNewCrInternal" type="hidden"
		value='${requestScope.newCrInternal}'>
	<input name="acctNewCrExternal" type="hidden"
		value='${requestScope.newCrExternal}'>
	<input name="acctNewDrInternal" type="hidden"
		value='${requestScope.newDrInternal}'>
	<input name="acctNewDrExternal" type="hidden"
		value='${requestScope.newDrExternal}'>
	<input name="creditExternalInter" type="hidden"
		value='${requestScope.CrExternalInt}'>
	<input name="debitExternalInter" type="hidden"
		value='${requestScope.DdExternalInt}'>
	<input name="forecastSOD" type="hidden" value="">
	<input name="externalSOD" type="hidden" value="">
	<input name="Balance Calculation" type="hidden" value="">
	<input name="selectedPrimaryForecast" type="hidden" value="">
	<input name="selectedPrimaryExternal" type="hidden" value="">
	<input name="selectedSecondaryForecast" type="hidden" value="">
	<input name="selectedSecondaryExternal" type="hidden" value="">
	<input name="selectedtabName" type="hidden">
	<input name="sweepScheduleId" type="hidden">
	<input name="sweepAccountId" type="hidden">
	<input name="accountLevel" type="hidden"
		value='${requestScope.accountLevel}'>
	<input name="linkAcctName" type="hidden"
		value='${requestScope.linkAcctName}'>
	<input name="countryName" type="hidden"
		value='${requestScope.countryName}'>
	<input name="currencyName" type="hidden"
		value='${requestScope.currencyName}'>
	<input name="mainAcctName" type="hidden"
		value='${requestScope.mainAcctName}'>
	<input name="bookCodeName" type="hidden"
		value='${requestScope.bookCodeName}'>
	<input name="callerMethodName" type="hidden"
		value='${requestScope.methodName}'>

	<c:set var="CDM" value="${sessionScope.CDM}"/>
	<div id="dropdowndiv_3"
		style="position: absolute; width: 265px; left: 567px; top: 302px; visibility: hidden; z-index: 100"
		class="swdropdown"><select class="htmlTextFixed"
		name="acctMaintenance.sweepbookcode" size="7"
		style="width:265px;z-index:99">
	</select></div>

	<div id="dropdowndiv_link"
		style="position: absolute; width: 265px; left: 196px; top: 239px; visibility: hidden; z-index: 110"
		class="swdropdown"><select class="htmlTextFixed"
		name="acctMaintenance.linkAccID" size="10"
		style="width:329px;z-index:110">
	</select></div>

	<div id="dropdowndiv_2"
		style="position: absolute; width: 200px; left: 185px; top: 190px; visibility: hidden; z-index: 100"
		class="swdropdown"><select
		name="acctMaintenance.minacctcode" size="10"
		style="width:329px;z-index:99" class="htmlTextFixed">
	</select></div>

	<div id="AddAccountMaintenance"
		style="position: absolute; left: 27px; top: 10px; width: 860px; height: 95px; border: 2px outset;"
		color="#7E97AF">
	<table width="805" border="0" cellpadding="0" cellspacing="0"  style="margin: 5px"
		height="10">
		<tr height="22">
			<td width="125">&nbsp;<b><fmt:message key="entity.id"/></b></td>
			<td width="100"><input type="text"  name="acctMaintenance.id.entityId" value="${acctMaintenance.id.entityId}"
				class="htmlTextAlpha" titleKey="tooltip.entityId" /></td>
			<td width="300" ><span id="entityDesc" name="entityDesc"
				class="spantext" /></td>
		</tr>
		</table>
		<table width="765" border="0" cellpadding="0" cellspacing="0"style="margin: 5px"
		  height="50">
		<tr height="10">
			<td width="100">&nbsp;<b><fmt:message key="acct.id"/></b><c:if test="${'add'==requestScope.methodName}">
    *
</c:if></td>
			<td width="10px">&nbsp;</td>

			<td width="370px"><input type="text"  name="acctMaintenance.id.accountId" value="${acctMaintenance.id.accountId}"  tabindex="1"
				style="width:360px;" titleKey="tooltip.acctId" maxlength="35"
				onchange="return validateField(this,'Number 2','alphaNumPat');" />
			</td>
		</tr>
		<tr >
			<c:if test="${'view'!=requestScope.methodName}">

				<td width="150" height="10">&nbsp;<b><fmt:message key="section.sectionName"/></b>*</td>

</c:if>
			<c:if test="${'view'==requestScope.methodName}">

				<td width="150" height="10">&nbsp;<b><fmt:message key="section.sectionName"/></b></td>

</c:if>
			<td width="28px">&nbsp;</td>

			<td width="358px" height="26"><input type="text"  name="acctMaintenance.acctname" value="${acctMaintenance.acctname}"  tabindex="2"
				style="width:360px;" titleKey="tooltip.acctName" maxlength="30"
				onchange="return validateField(this,'section.sectionName','ascii_standard')" />
			</td>
			<td width="210">&nbsp;</td>
			<td width="25" align="left">&nbsp;</td>
		</tr>
	</table>
	</div>

	<div id="ddimagetabs"
		style="position: absolute; left: 25px; width: 808px; heighy: 20px; top: 110px">
	<a href="#" id="firstsc" onmouseout="revertback('sc1',this);"
		tabindex="3" onmouseover="changecontent('sc1',this)"
		onClick="changeselected('sc1');expandcontent('sc1', this)"><b><fmt:message key="Account.generalTab"/></b></a> <a href="#"
		onmouseout="revertback('sc2',this);" tabindex="4"
		onmouseover="changecontent('sc2',this)"
		onClick="changeselected('sc2');expandcontent('sc2', this)"><b><fmt:message key="Account.monitorTab"/></b></a> <a href="#"
		onmouseout="revertback('sc3',this);" tabindex="5"
		onmouseover="changecontent('sc3',this)"
		onClick="changeselected('sc3');expandcontent('sc3', this)"><b><fmt:message key="Account.sweepTab"/></b></a></div>

	<div id="Line"
		style="position: absolute; left: 270px; top: 127px; width: 620px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
	</div>

	<div id="sc1" class="tabcontent">
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 24px; top: 130px; width: 865px; height: 650px; border: 2px outset; border-top: thin;">
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 4px; top: -5px; width: 865px; height: 490px;">
	<table height="10">
		<tr>
			<td></td>
		</tr>
	</table>
	<fieldset
		style="width: 853px; border: 2px groove; height: 200px!important; top: 20px;">
	<legend><fmt:message key="fielset.genDeatils"/></legend>
	<table width="842" border="0" cellpadding="0" cellspacing="0"
		height="22">
		<tr height="22">
			<td width="133">&nbsp;<b><fmt:message key="acct.currency.id"/></b></td>
			<td width="340"><select name="acctMaintenance.currcode"
				onchange="changeCurrencyCode()" tabindex="6" style="width:60px"
				titleKey="tooltip.currencyCode">
				<c:if test="${'add'==requestScope.methodName}">

				<c:forEach var="item" items="${requestScope.acctmaintenancelistadd}">
				<option value="${item.value}"
				<c:if test="${acctMaintenance.currcode == item.value}">selected</c:if>>
				${item.label}</option>
				</c:forEach>

				</c:if>
			</select> &nbsp;&nbsp;&nbsp; <span id="currencyNameDisp"
				name="currencyNameDisp" class="spantext" /></td>
			<td width="165" align="right">&nbsp;<b style="float:left;position:relative;width:133px;"><fmt:message key="acctMaintenance.archiveData"/></b></td>
			<td width="25" align="left">
				<input type="checkbox" tabindex="9" style="width:13;" name="acctMaintenance.archiveData" value="Y" ${requestScope.acctMaintenance.archiveData == 'Y' ? 'checked' : ''} fieldValue="Y"

				value='${requestScope.acctMaintenance.archiveData == "Y"}'
				class="htmlTextAlpha" titleKey="tooltip.archiveData"
				onclick="checkIfAccountIsIlmDataMember('checkAccountIlmDataMember');" />




				</td>
		</tr>
	</table>
	<table width="842" border="0" cellpadding="0" cellspacing="0"
		height="22">
		<tr height="22">
			<td width="130">&nbsp;<b><fmt:message key="accttype"/></b></td>
			<td width="91"><input type="radio" name="acctMaintenance.accttype" ${acctMaintenance.accttype == 'C' ? 'checked="checked"' : ''} value="${'C'}" id="1" tabindex="7"   style="width:13;" titleKey="tooltip.cash" /> <label
				title='<fmt:message key="tooltip.cash"/>' for="1"><fmt:message key="cash"/> </label></td>
			<td width="120"><input type="radio" name="acctMaintenance.accttype" ${acctMaintenance.accttype == 'U' ? 'checked="checked"' : ''} value="${'U'}" id="2" tabindex="8"   style="width:13;" titleKey="tooltip.custodain" /> <label
				title='<fmt:message key="tooltip.custodain"/>' for="2"><fmt:message key="custodian"/></label></td>
			<td width="173"></td>
			<td width="133" align="right">&nbsp;<b style="float:left;position:relative;width:133px;"><fmt:message key="acctMaintenance.aggAccount"/></b></td>
			<td width="25" align="left"><input type="checkbox" tabindex="9" style="width:13;" name="acctMaintenance.aggAccount" value="Y" ${requestScope.acctMaintenance.aggAccount == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.aggAccount == "Y"}' class="htmlTextAlpha" titleKey="tooltip.aggAcct" /></td>
		</tr>
	</table>
	<table width="733" border="0" cellpadding="0" cellspacing="0"
		height="22">
		<tr height="22">
			<td width="130">&nbsp;<b><fmt:message key="account.accclass"/></b></td>
			<td width="90"><input type="radio" name="acctMaintenance.acctClass" ${acctMaintenance.acctClass == 'N' ? 'checked="checked"' : ''} value="${'N'}" id="3" tabindex="10"   style="width:13;" titleKey="account.nostrotype" onclick="javascript:changeStatusOpenUnexpected(this.value);" /> <label
				title='<fmt:message key="account.nostrotype"/>' for="3"><fmt:message key="account.nostrotype"/></label></td>
			<td width="90"><input type="radio" name="acctMaintenance.acctClass" ${acctMaintenance.acctClass == 'L' ? 'checked="checked"' : ''} value="${'L'}" id="4" tabindex="11"   style="width:13;" titleKey="acc.lorotype" onclick="javascript:changeStatusOpenUnexpected(this.value);" /> <label
				title='<fmt:message key="acc.lorotype"/>' for="4"><fmt:message key="acc.lorotype"/></label></td>
			<td width="90"><input type="radio" name="acctMaintenance.acctClass" ${acctMaintenance.acctClass == 'C' ? 'checked="checked"' : ''} value="${'C'}" id="5" tabindex="12"   style="width:13;" titleKey="acc.currenttype" onclick="javascript:changeStatusOpenUnexpected(this.value);" /> <label
				title='<fmt:message key="acc.currenttype"/>' for="5"><fmt:message key="acc.currenttype"/></label></td>
			<td width="90"><input type="radio" name="acctMaintenance.acctClass" ${acctMaintenance.acctClass == 'E' ? 'checked="checked"' : ''} value="${'E'}" id="6" tabindex="13"   style="width:13;" titleKey="acc.netting" onclick="javascript:changeStatusOpenUnexpected(this.value);" /> <label
				title='<fmt:message key="acc.netting"/>' for="6"><fmt:message key="acc.netting"/></label></td>
			<td width="90"><input type="radio" name="acctMaintenance.acctClass" ${acctMaintenance.acctClass == 'O' ? 'checked="checked"' : ''} value="${'O'}" id="7" tabindex="14"   style="width:13;" titleKey="acc.othertype" onclick="javascript:changeStatusOpenUnexpected(this.value);" /> <label
				title='<fmt:message key="acc.othertype"/>' for="7"><fmt:message key="acc.othertype"/></label></td>
		</tr>
	</table>
	<table width="828" border="0" cellpadding="0" cellspacing="0"
		height="85">
		<tr height="17px">
			<td width="150">&nbsp;<b><fmt:message key="account.linkacc"/></b><input
				class="textAlpha" style="background: transparent; border: thin;"
				class="textAlpha" readonly name="accountIdMandatory" size="1"></td>
			<td width="8px">&nbsp;&nbsp;</td>

			<td width="642px"><input name="linkaccountId" readonly="readonly"
				class="textAlpha" style="width: 220px; margin-right: 5px; height: 22px;"
				title='<fmt:message key="tooltip.linkAcctId"/>'><input
				tabindex="16" id="dropdownbutton_link" type="button" value="..."
				onclick="getLinkAccountList(event)" disabled=""
				title='<fmt:message key="tooltip.link.linkAcctId"/>'>&nbsp;&nbsp;&nbsp;<input
				style="background: transparent; border: thin; text-align: top; height: 19; width: 320"
				class="textAlpha" readonly name="linkaccountName" size="20"></td>
			<td width="28px" colspan="2">&nbsp;</td>
		</tr>
		<tr height="22px">
			<td width="150px"><b>&nbsp;<fmt:message key="acctstatus"/></b></td>
			<td width="18px">&nbsp;&nbsp;</td>
			<td width="240px"><select
				name="acctMaintenance.acctstatusflg" tabindex="18"
				titleKey="tooltip.acctStatus" style="width:72px;">
				<c:forEach var="item" items="${requestScope.acctmaintstatuslist}">
				<option value="${item.value}"
				<c:if test="${acctMaintenance.acctstatusflg == item.value}">selected</c:if>>
				${item.label}</option>
				</c:forEach>
			</select></td>
			<td width="28px" colspan="2">&nbsp;</td>
		</tr>
		<tr height="23px">
			<td width="142px">&nbsp;<b><fmt:message key="country"/></b></td>
			<td width="18px">&nbsp;</td>

			<td width="280px"><select
				name="acctMaintenance.holidaycalendar" tabindex="19"
				titleKey="tooltip.countryName" style="width:235px;"
				onmouseover="getCountryList()" onfocus="getCountryList()">
			</select></td>

		</tr>
	</table>
	<div id="ddimagebuttons"
		style="position: absolute; z-index: 99; left: 730px; top: 95px; width: 100px; height: 111px; visibility: visible">
	<div id="buttonstatus"
		style="position: absolute; left: 6; top: 0; width: 100px; height: 66px; visibility: visible;">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="linkbutton" height="26px"></td>
		</tr>
		<tr height="26px" valign="bottom">
			<td id="contactbutton"><a id="contactButton" align="right"
				title='<fmt:message key="tooltip.contactDt"/>' tabindex="20"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:openWindow(addContactDetails('displayContact'),'accountscontactDetailsWindow','left=50,top=190,width=435,height=185,toolbar=0, resizable=yes, scrollbars=yes','true')"
				onKeyDown="submitEnter(this,event)"><fmt:message key="button.contact"/></a></td>
		</tr>
		<tr height="26px" valign="bottom">
		<c:if test="${'add'!=requestScope.methodName}">


			<td id="attributesbutton"><a id="attributesbutton" align="right"
				title='<fmt:message key="button.attributes"/>' tabindex="20"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:openWindow(displayAttributesDetails('displayAttributesFlex'),'accountAttributeLatestValues','left=50,top=190,width=800,height=550,toolbar=0, resizable=yes, scrollbars=yes','true')"
				onKeyDown="submitEnter(this,event)"><fmt:message key="button.attributes"/></a></td>

</c:if>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 100px; height: 45px; visibility: hidden;">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr height="45px">
			<td id="linkenablebutton" style="display:none;"><a id="contactButton" align="right"
				tabindex="17" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(getLinkAccounts('getLinkedAccounts'),'linkAccountWindow','left=50,top=190,width=985,height=612,toolbar=0, resizable=yes, scrollbars=yes')"
				title='<fmt:message key="tooltip.button.linked"/>'><fmt:message key="button.linked"/></a></td>
			<td id="linkdisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="tooltip.button.linked"/>'><fmt:message key="button.linked"/></a></td>
		</tr>
	</table>
	</div>
	</div>
	<table width="418" height="22" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr height="22">
				<td width="165">&nbsp;<b><fmt:message key="acctMaintenance.fmi"/></b></td>

			    <td width="200px" align="right"><input type="text"  name="acctMaintenance.fmi" value="${acctMaintenance.fmi}"  style="width:250px;" tabindex="22"
				titleKey="acctMaintenance.tooltip.fmi" maxlength="35"
				onchange="return validateField(this,'acctMaintenance.fmi','alphaNumPatExtended_AcctName');" /></td>
			</tr>
		</tbody>
	</table>
	</fieldset>
	</div>

	<div id="GeneralReferences"
		style="position: absolute; z-index: 99; left: 4px; top: 206px; width: 183px; height: 120px;">
	<fieldset style="width: 853px; border: 2px groove; height: 138px!important;">
	<legend><fmt:message key="references"/></legend>
	<table width="850px" border="0" cellpadding="0" cellspacing="1"
		class="content" height="60px">
		<tr height="27px">
			<td width="218px"><b>&nbsp;<fmt:message key="acctglcode"/></b></td>

			<td width="200px" align="right"><input type="text"  name="acctMaintenance.glcode" value="${acctMaintenance.glcode}"  style="width:250px;" tabindex="22"
				titleKey="tooltip.glCode" maxlength="35"
				onchange="return validateField(this,'acctglcode','alphaNumPatExtended_AcctName');" /></td>
			<td width="160px" align="right"><b>&nbsp;<fmt:message key="corrscode"/></b></td>

			<td width="300px" align="right"><input type="text"  name="acctMaintenance.corresacccode" value="${acctMaintenance.corresacccode}"  tabindex="23"
				titleKey="tooltip.corrCode" style="width:250px;margin-right:5px" maxlength="35"
				onchange="return validateField(this,'corrscode','alphaNumPatExtended_AcctName');" />
			</td>
		</tr>
		<!-- Start Code:Modified by Naseema.Sd for Mantis 1797 -->
		<tr height="27px">
			<td width="160px" align="left"><b>&nbsp;<fmt:message key="acctbiccode"/></b></td>
			<td width="200px"><input type="text"  name="acctMaintenance.acctbiccode" value="${acctMaintenance.acctbiccode}"

				onblur="getIntermediaryRecord(this);return checkalphanumeric(this)" tabindex="24"
				titleKey="tooltip.acctBic" style="width:250px;" maxlength="12" /></td>
			<td width="160px" align="right"><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="acctextraid"/></b></td>

			<td width="300px" align="right"><input type="text"  name="acctMaintenance.acctextraid" value="${acctMaintenance.acctextraid}"  style="width:250px;margin-right:5px;"
				tabindex="25" titleKey="tooltip.extraID" maxlength="35"
				onchange="return validateField(this,'acctextraid','alphaNumPatExtended_AcctName');" />
			</td>
		</tr>
		<!-- End Code:Modified by Naseema.Sd for Mantis 1797 -->
		<tr height="27px" >
			<td width="160px" align="left"><b>&nbsp;<fmt:message key="acctiban"/></b></td>
			<td width="300px" align="left" style="top:71;left:170;position:absolute;"><input type="text"  name="acctMaintenance.acctIBAN" value="${acctMaintenance.acctIBAN}"

				class="upperText"
				tabindex="26"
				onchange="return validateIBAN();"
				titleKey="tooltip.acctIBAN" style="width:433px;" maxlength="50" />
			</td>
		</tr>
		<tr height="27px" >
			<td width="160px" align="left"><b>&nbsp;<fmt:message key="account.accountParty"/></b></td>
			<td align="left" style="top:95;left:168;"><input type="text"  name="acctMaintenance.accountPartyId" value="${acctMaintenance.accountPartyId}"

				onchange="updatePartyDesc()"
				tabindex="27"
				disabled=""
				titleKey="tooltip.accountParty" style="height: 22px;" maxlength="12" />
			</td>
			<td width="50px">
				<input 	id="partyLink" type="button" tabindex="28"
    					title='<fmt:message key="tooltip.clickSelAccountParty"/>'
    					value="..."
    					onClick="javascript:party('M','acctMaintenance.accountPartyId','partyDesc')"
    					style="top:101;margin-left:-85;position:absolute;"/>
    		</td>
    		<td width="280px"><span id="partyDesc"
    								name="partyDesc" class="spantext"
    								style="top:105;left:360;position:absolute;width:250px"></td>

		</tr>
	</table>
	</fieldset>
	</div>

	<div id="CurrentIntRates"
		style="position: absolute; z-index: 99; left: 4px; top: 345px; width: 800px; height: 50px;">
	<fieldset style="width: 853px; border: 2px groove; height: 90px!important;padding-bottom:7.5px">
	<legend><fmt:message key="crintrates"/></legend>
	<table width="850px" height="25" border="0" cellpadding="0"
		cellspacing="1" class="content">
		<tr height="25px">
			<td width="240px"><b>&nbsp;<fmt:message key="addjob.Date"/></b></td>


			<td width="200px"><input type="text" titleKey="tooltip.date" name="acctMaintenance.latestInterestDateRate" value="${acctMaintenance.latestInterestDateRate}"  maxlength="25"
				style="width:80px;" /></td>
			<td width="28px" /><!-- Rates button added -->
			<td width="70px"></td>
			<td width="300px" id="ddimagebuttons"><a id="searchButton"
				align="right"
				title='<fmt:message key="tooltip.accountInterestRate"/>'
				tabindex="29" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:openWindow(addAccInterestRateDetail('displayAccInterestRate'),'AccInterestRateWindow','left=50,top=190,width=815,height=467,toolbar=0, resizable=yes, scrollbars=yes','true')"
				onKeyDown="submitEnter(this,event)"><fmt:message key="button.rates"/></a></td>
		</tr>
		<tr height="25px">
			<td width="159px"><b>&nbsp;<fmt:message key="acc.credit"/></b></td>
			<td width="160px"><input type="text" titleKey="tooltip.addAccountCredit" name="acctMaintenance.latestCreditRate" value="${acctMaintenance.latestCreditRate}"  maxlength="25"

				style="width:160px;" /></td>
			<td width="200px"></td>
			<td width="163px" align="right"><b><fmt:message key="acc.overdraft"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>

			<td width="310px"><input type="text" titleKey="tooltip.debit" name="acctMaintenance.latestOverDraftRate" value="${acctMaintenance.latestOverDraftRate}"  maxlength="25"
				style="width:160px;" /> </td>
		</tr>
	</table>
	</fieldset>
	</div>

	<div id="OneNostro"
		style="position: absolute; z-index: 99; left: 4px; top: 438px; width: 800px; height: 50px;">
	<fieldset style="width: 853px; border: 2px groove; height: 90px!important;padding-bottom:7.5px">
	<legend><fmt:message key="acctMaintenance.oneNostro"/></legend>
	<table width="850px" height="25" border="0" cellpadding="0"
		cellspacing="1" class="content">
		<tr height="25px">
			<td width="265px"><b>&nbsp;<fmt:message key="acctMaintenance.servicingEntityId"/></b></td>

			<td width="240px"><select
				name="acctMaintenance.servicingEntityId" tabindex="18"
				titleKey="acctMaintenance.tooltip.servicingEntityId" style="width:200px;">

				<option value="${item.value}">
				${item.label}</option>
			</select></td>

			<td width="28px" />
			<td width="70px"></td>
			<td width="300px"></td>
		</tr>
		<tr height="25px">
			<td width="159px"><b>&nbsp;<fmt:message key="acctMaintenance.accNameInSvcEntity"/></b></td>
			<td width="160px"><input type="text" titleKey="acctMaintenance.tooltip.accNameInSvcEntity" name="acctMaintenance.accNameInSvcEntity" value="${acctMaintenance.accNameInSvcEntity}"  maxlength="25"

				style="width:360px;" /></td>
			<td width="200px"></td>
			<td width="163px"> </td>
		</tr>
	</table>
	</fieldset>
	</div>
	</div>
	</div>

	<div id="sc2" class="tabcontent">
	<div id="AddAccountMaintenance1"
		style="position: absolute; top: 130px; width: 865px; height: 650px; border: 2px outset; border-top: thin; left: 24px"
		color="#7E97AF">
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 4px; top: 6px; width: 243px; height: 490px;">
	<fieldset style="width: 415px; border: 2px groove; height: 98px!important;">
	<legend><fmt:message key="account.fieldset"/></legend>
	<table width="203px" border="0" cellspacing="0" cellpadding="0"
		height="76">
		<tr height="22px">
			<td title='<fmt:message key="account.monitor"/>' width="50px"><b>&nbsp;<fmt:message key="account.monitor"/></b></td>
			<td width="200px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="13px"><input type="checkbox" titleKey="account.monitor"

			tabindex="6" style="width:13;" name="acctMaintenance.monitor" value="Y" ${requestScope.acctMaintenance.monitor == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.monitor == "Y"}' class="htmlTextAlpha" /></td>

		</tr>
		<tr height="22px">
			<td title='<fmt:message key="account.sum"/>' width="50px"><b>&nbsp;<fmt:message key="account.sum"/></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px"><select
				name="acctMaintenance.acctMonitorSum" tabindex="7"
				titleKey="account.sum" style="width:180px;">
				<option value="N" <c:if test="${'N' == requestScope.acctMaintenance.acctMonitorSum}">
    selected='selected'
</c:if>>Do Not Sum</option>
				<option value="Y" <c:if test="${'Y' == requestScope.acctMaintenance.acctMonitorSum}">
    selected='selected'
</c:if>>Always Sum</option>
				<option value="C" <c:if test="${'C' == requestScope.acctMaintenance.acctMonitorSum}">
    selected='selected'
</c:if>>Sum according to cut-off</option>
			</select></td>
		</tr>
		<tr height="22px">
			<td title='<fmt:message key="currency.order"/>' width="50px"><b>&nbsp;<fmt:message key="currency.order"/>*</b></td>
			<td width="28px">&nbsp;</td>

			<td width="13px"><input type="text" titleKey="currency.order" name="acctMaintenance.acctPriorityOrder" value="${acctMaintenance.acctPriorityOrder}"  tabindex="8"
				style="width:30px;" maxlength="2"
				onchange="return validateField(this,'currency.order','numberPat');" /></td>
		</tr>
	</table>
	</fieldset>
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 417px; top: 0px; width: 315px; height: 190px;">
	<fieldset style="width: 436px; border: 2px groove; height: 98px!important;">
	<legend><fmt:message key="account.options"/></legend>
	<table width="375" border="0" cellspacing="0" cellpadding="0"
		STYLE="position: relative; left: 0px; top: 1px;">
		<tr height="20px">
			<td title='<fmt:message key="tooltip.account.autoopenunsettled"/>'
				width="220px">&nbsp;<b><fmt:message key="account.autoopenunsettled"/></b></td>
			<td width="20px"><input type="checkbox" tabindex="10" style="width:13;" name="acctMaintenance.autoOpenUnsettled" value="Y" ${requestScope.acctMaintenance.autoOpenUnsettled == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.autoOpenUnsettled == "Y"}' class="htmlTextAlpha" titleKey="tooltip.autoOpenUnsettled" /></td>
		</tr>
		<tr height="20px">
			<td title='<fmt:message key="tooltip.account.autoopenunexpected"/>'
				width="220px">&nbsp;<b><fmt:message key="account.autoopenunexpected"/></b></td>
			<td width="20px"><input type="checkbox" tabindex="11" style="width:13;" name="acctMaintenance.autoOpenUnexpected" value="Y" ${requestScope.acctMaintenance.autoOpenUnexpected == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.autoOpenUnexpected == "Y"}' class="htmlTextAlpha" titleKey="tooltip.autoOpenUnexpected" /></td>
		</tr>
		<tr height="20px">
			<td title='<fmt:message key="tooltip.account.allpreadviceentity"/>'
				width="220px">&nbsp;<b><fmt:message key="account.allpreadviceentity"/></b></td>
			<td width="20px"><input type="checkbox" tabindex="12" style="width:13;" name="acctMaintenance.allPreAdviceEntity" value="Y" ${requestScope.acctMaintenance.allPreAdviceEntity == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.allPreAdviceEntity == "Y"}' class="htmlTextAlpha" titleKey="tooltip.allPreAdviceEntity" /></td>
		</tr>
	</table>
	</fieldset>
	</div>
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 0px; top: 100px; width: 300px; height: 350px;">
	<fieldset style="width: 853px; border: 2px groove; height: 85px!important;">
	<legend><fmt:message key="account.fieldsetBalance"/></legend>
	<table width="472" height="70px" border="0" cellspacing="0"
		cellpadding="0" Style="position: relative; left: 0px; top: 0px;">
		<tr height="15px">
			<td width="178px">&nbsp</td>
			<td width="20px"><label><b><fmt:message key="acctmaintenance.predict"/></b></label></td>
			<td width="20px"></td>
			<td width="20px"><label><b><fmt:message key="acctmaintenance.external"/></b></label></td>
		</tr>
		<tr height="18px">
			<td width="140px"><label><b>&nbsp;<fmt:message key="acctmaintenance.primary"/></b></label></td>
			<td width="20px"><select
				name="acctMaintenance.primaryForecast" tabindex="13"
				titleKey="tooltip.primaryForecast" style="width:130px;">
				<option value="I" <c:if test="${'I' == requestScope.acctMaintenance.primaryForecast}">
    selected='selected'
</c:if>>Internal</option>
				<option value="E" <c:if test="${'E' == requestScope.acctMaintenance.primaryForecast}">
    selected='selected'
</c:if>>External</option>
				<option value="P" <c:if test="${'P' == requestScope.acctMaintenance.primaryForecast}">
    selected='selected'
</c:if>>Predicted</option>
				<option value="Z" <c:if test="${'Z' == requestScope.acctMaintenance.primaryForecast}">
    selected='selected'
</c:if>>Zero</option>
			</select></td>
			<td width="20px"></td>
			<td width="20px"><select
				name="acctMaintenance.primaryExternal" tabindex="14"
				titleKey="tooltip.primaryExternal" style="width:130px;">
				<option value="I" <c:if test="${'I' == requestScope.acctMaintenance.primaryExternal}">
    selected='selected'
</c:if>>Internal</option>
				<option value="E" <c:if test="${'E' == requestScope.acctMaintenance.primaryExternal}">
    selected='selected'
</c:if>>External</option>
				<option value="P" <c:if test="${'P' == requestScope.acctMaintenance.primaryExternal}">
    selected='selected'
</c:if>>Predicted</option>
				<option value="Z" <c:if test="${'Z' == requestScope.acctMaintenance.primaryExternal}">
    selected='selected'
</c:if>>Zero</option>
			</select></td>
		</tr>
		<tr height="22px">
			<td width="140px"><label><b>&nbsp;<fmt:message key="acctmaintenance.secondary"/></b></label></td>
			<td width="20px"><select
				name="acctMaintenance.secondaryForecast" tabindex="15"
				titleKey="tooltip.secondaryForecast" style="width:130px;">
				<option value="I" <c:if test="${'I' == requestScope.acctMaintenance.secondaryForecast}">
    selected='selected'
</c:if>>Internal</option>
				<option value="E" <c:if test="${'E' == requestScope.acctMaintenance.secondaryForecast}">
    selected='selected'
</c:if>>External</option>
				<option value="P" <c:if test="${'P' == requestScope.acctMaintenance.secondaryForecast}">
    selected='selected'
</c:if>>Predicted</option>
				<option value="Z" <c:if test="${'Z' == requestScope.acctMaintenance.secondaryForecast}">
    selected='selected'
</c:if>>Zero</option>
				<option value="N" <c:if test="${'N' == requestScope.acctMaintenance.secondaryForecast}">
    selected='selected'
</c:if>>None</option>
			</select></td>
			<td width="20px"></td>
			<td width="20px"><select
				name="acctMaintenance.secondaryExternal" tabindex="16" id="secondaryExternal"
				titleKey="tooltip.secondaryExternal" style="width:130px;position:relative;transform:translateY(39%)">
				<option value="I" <c:if test="${'I' == requestScope.acctMaintenance.secondaryExternal}">
    selected='selected'
</c:if>>Internal</option>
				<option value="E" <c:if test="${'E' == requestScope.acctMaintenance.secondaryExternal}">
    selected='selected'
</c:if>>External</option>
				<option value="P" <c:if test="${'P' == requestScope.acctMaintenance.secondaryExternal}">
    selected='selected'
</c:if>>Predicted</option>
				<option value="Z" <c:if test="${'Z' == requestScope.acctMaintenance.secondaryExternal}">
    selected='selected'
</c:if>>Zero</option>
				<option value="N" <c:if test="${'N' == requestScope.acctMaintenance.secondaryExternal}">
    selected='selected'
</c:if>>None</option>
			</select></td>
		</tr>
	</table>
	</fieldset>
	</div>

	<div id="GeneralParameters"
		style="position: absolute; z-index: 99; left: 0px; top: 259px; width: 140px; height: 20px;">
	<fieldset style="width: 853px; border: 2px groove;height:40px!important"><legend><fmt:message key="acctmain.balances"/></legend>
	<table width="611" border="0" cellpadding="0" cellspacing="0"
		height="25px">
		<tr>
			<td width="203">&nbsp;<b><fmt:message key="acctmain.futurebalances"/></b></td>
			<td width="182"><input type="radio" name="acctMaintenance.futureBalances" ${acctMaintenance.futureBalances == 'T' ? 'checked="checked"' : ''} value="${'T'}" id="16" tabindex="25"   style="width:13;" titleKey="acctmain.usetodaysod" /> <label
				title="<fmt:message key="acctmain.usetodaysod"/>" for="16"><fmt:message key="acctmain.usetodaysod"/></label></td>

			<td width="160"><input type="radio" name="acctMaintenance.futureBalances" ${acctMaintenance.futureBalances == 'N' ? 'checked="checked"' : ''} value="${'N'}" id="17" tabindex="26"   style="width:13;" titleKey="acctmain.usenearestsod" /> <label
				title='<fmt:message key="acctmain.usenearestsod"/>' for="17"><fmt:message key="acctmain.usenearestsod"/></label></td>
			<td width="140"><input type="radio" name="acctMaintenance.futureBalances" ${acctMaintenance.futureBalances == 'Z' ? 'checked="checked"' : ''} value="${'Z'}" id="18" tabindex="27"   style="width:13;" titleKey="acctmain.usezero" /> <label
				title='<fmt:message key="acctmain.usezero"/>' for="18"><fmt:message key="acctmain.usezero"/></label></td>
		</tr>
	</table>
	</fieldset>
	</div>

	<div id="GeneralParameters"
		style="position: absolute; z-index: 99; left: 0px; top: 301px; width: 140px; height: 20px;">
	<fieldset style="width: 853px; border: 2px groove;height:80px!important"><legend><fmt:message key="acctmain.ilmParameters"/></legend>
	<table width="362" border="0" cellpadding="0" cellspacing="0"
		height="25px">
		<tr height="22px">
			<td title='<fmt:message key="tooltip.isIlmContributer"/>' width="200px"><b>&nbsp;<fmt:message key="acctmain.isIlmContributer"/></b></td>
			<td width="13px"><input type="checkbox" titleKey="acctmain.isIlmContributer" tabindex="28" style="width:13;" name="acctMaintenance.isIlmLiqContributor" value="Y" ${requestScope.acctMaintenance.isIlmLiqContributor == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.isIlmLiqContributor == "Y"}' class="htmlTextAlpha" /></td>
		</tr>
		<tr height="22px">
			<td title='<fmt:message key="tooltip.isCentralBank"/>' width="220px"><b>&nbsp;<fmt:message key="acctmain.isCentralBank"/></b></td>
			<td width="13px"><input type="checkbox" titleKey="acctmain.isCentralBank" tabindex="29" style="width:13;" name="acctMaintenance.isIlmCentralBankMember" value="Y" ${requestScope.acctMaintenance.isIlmCentralBankMember == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.isIlmCentralBankMember == "Y"}' class="htmlTextAlpha" /></td>
		</tr>
		<tr height="22px">
			<td title='<fmt:message key="tooltip.isCustomerAccount"/>' width="200px"><b>&nbsp;<fmt:message key="acctmain.isCustomerAccount"/></b></td>
			<td width="13px"><input type="checkbox" titleKey="acctmain.isCustomerAccount" tabindex="30" style="width:13;" name="acctMaintenance.isIlmCustomerAccount" value="Y" ${requestScope.acctMaintenance.isIlmCustomerAccount == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.isIlmCustomerAccount == "Y"}' class="htmlTextAlpha" /></td>
		</tr>
	</table>
	</fieldset>
	</div>
		<div id="AddAccountMaintenance"
		style="position: absolute; top: 385px; width: 420px; height: 190px;">
	<fieldset style="width: 415px; border: 2px groove; height: 98px!important;">
	<legend><fmt:message key="acctMaintenance.includeLoro"/></legend>
	<table width="400px" border="0" cellspacing="0" cellpadding="0"
		height="76" style="padding-left:5px">
		<tr height="22px" width="10px">
			<td title='<fmt:message key="acctMaintenance.inThisEntity"/>' width="200px"><fmt:message key="acctMaintenance.inThisEntity"/></td>
		</tr>

		<tr height="22px" width="10px">
			<td width="50px"><select
				name="acctMaintenance.thisEntityInclBalFlag" tabindex="7"
				titleKey="acctMaintenance.tooltip.inThisEntity" style="width:400px;">
				<c:if test="${'L'==requestScope.acctMaintenance.acctClass}">

				<option value="N" <c:if test="${'N' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by subtraction</option>

</c:if>
				<c:if test="${'C'==requestScope.acctMaintenance.acctClass}">

				<option value="N" <c:if test="${'N' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by subtraction</option>

</c:if>
				<c:if test="${'N'==requestScope.acctMaintenance.acctClass}">

				<option value="N"  <c:if test="${'N' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by subtraction</option>

</c:if>
				<c:if test="${'O'==requestScope.acctMaintenance.acctClass}">

				<option value="N"  <c:if test="${'N' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by subtraction</option>

</c:if>
				<c:if test="${'E'==requestScope.acctMaintenance.acctClass}">

				<option value="N"  <c:if test="${'N' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Predicted in Loro balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.thisEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Predicted to Loro balance by subtraction</option>

</c:if>
			</select></td>
		</tr>

		<tr height="22px"  width="10px" >
			<td title='<fmt:message key="acctMaintenance.applyBetween"/>' width="100px">
			<b><fmt:message key="acctMaintenance.applyBetween"/>&nbsp;&nbsp;&nbsp;&nbsp;<b>

				<input type="text" titleKey="acctMaintenance.tooltip.applyBetween" name="acctMaintenance.thisEntityInclFrom" value="${acctMaintenance.thisEntityInclFrom}"  tabindex="8"

				style="width:60px;" maxlength="5"
				onchange="return validateField(this,'applyBetween','timePat');" />

				<b>&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="acctMaintenance.and"/>&nbsp;&nbsp;&nbsp;&nbsp;<b>


				<input type="text" titleKey="acctMaintenance.tooltip.and" name="acctMaintenance.thisEntityInclTo" value="${acctMaintenance.thisEntityInclTo}"  tabindex="8"
				style="width:60px;" maxlength="5"
				onchange="return validateField(this,'and','timePat');" />
			</td>

		</tr>
	</table>
	</fieldset>
	</div>
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 417px; top: 392px; width: 315px; height: 190px;">
	<fieldset style="width: 436px; border: 2px groove; height: 91px!important;">
	<legend></legend>
	<table width="375" border="0" cellspacing="0" cellpadding="0"
		STYLE="position: relative; padding-left:5px; padding-top:5px; left: 0px; top: 1px;">
     <tr height="24px" width="10px">
			<td title='<fmt:message key="acctMaintenance.inServEntity"/>' width="200px"><fmt:message key="acctMaintenance.inServEntity"/></td>
		</tr>

		<tr height="25px" width="10px">
			<td width="50px"><select
				name="acctMaintenance.svcEntityInclBalFlag" tabindex="7"
				titleKey="acctMaintenance.tooltip.inServEntity" style="width:400px;">
				<option value="N"  <c:if test="${'N' == requestScope.acctMaintenance.svcEntityInclBalFlag}">
    selected='selected'
</c:if>>Do not include in other balances</option>
				<option value="A+" <c:if test="${'A+' == requestScope.acctMaintenance.svcEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by addition</option>
				<option value="A-" <c:if test="${'A-' == requestScope.acctMaintenance.svcEntityInclBalFlag}">
    selected='selected'
</c:if>>Also include Loro in Predicted balance by subtraction</option>
				<option value="M+" <c:if test="${'M+' == requestScope.acctMaintenance.svcEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by addition</option>
				<option value="M-" <c:if test="${'M-' == requestScope.acctMaintenance.svcEntityInclBalFlag}">
    selected='selected'
</c:if>>Move Loro to Predicted balance by subtraction</option>
			</select></td>
		</tr>

		<tr height="22px"  width="10px" >
			<td title='<fmt:message key="acctMaintenance.applyBetween"/>' width="100px">
			<b><fmt:message key="acctMaintenance.applyBetween"/>&nbsp;&nbsp;&nbsp;&nbsp;<b>

				<input type="text" titleKey="acctMaintenance.tooltip.applyBetween" name="acctMaintenance.svcEntityInclFrom" value="${acctMaintenance.svcEntityInclFrom}"  tabindex="8"

				style="width:60px;" maxlength="5" />
				<b>&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="acctMaintenance.and"/>&nbsp;&nbsp;&nbsp;&nbsp;<b>

				<input type="text" titleKey="acctMaintenance.tooltip.and" name="acctMaintenance.svcEntityInclTo" value="${acctMaintenance.svcEntityInclTo}"  tabindex="8"
				style="width:60px;" maxlength="5" />
			</td>


		</tr>
	</table>
	</fieldset>
	</div>

	<div id="GeneralParameters"
		style="position: absolute; z-index: 99; left: 0px; top: 187px; width: 483px; height: 20px;">
	<fieldset style="width: 853px; border: 2px groove; height: 70px!important;">
	<legend><fmt:message key="acctmaintenance.backvalue"/></legend>
	<table width="713" border="0" cellpadding="0" cellspacing="0"
		height="55px">
		<tr>
			<td width="110">&nbsp;<b><fmt:message key="acctmaintenance.forecastsod"/></b></td>
			<td width="180"><input type="radio" name="acctMaintenance.forecastSOD" ${acctMaintenance.forecastSOD == 'I' ? 'checked="checked"' : ''} value="${'I'}" id="8"   tabindex="17" style="width:13;" titleKey="acctmaintenance.useinternal" />
			<label title='<fmt:message key="acctmaintenance.useinternal"/>'
				for="8"><fmt:message key="acctmaintenance.useinternal"/></label></td>
			<td width="160"><input type="radio" name="acctMaintenance.forecastSOD" ${acctMaintenance.forecastSOD == 'E' ? 'checked="checked"' : ''} value="${'E'}" id="9"   tabindex="18" style="width:13;" titleKey="acctmaintenance.useexternal" />
			<label title='<fmt:message key="acctmaintenance.useexternal"/>'
				for="9"><fmt:message key="acctmaintenance.useexternal"/></label></td>
			<td width="190"><input type="radio" name="acctMaintenance.forecastSOD" ${acctMaintenance.forecastSOD == 'P' ? 'checked="checked"' : ''} value="${'P'}" id="10"   tabindex="19" style="width:13;" titleKey="acctmaintenance.usepredicted" />
			<label title='<fmt:message key="acctmaintenance.usepredicted"/>'
				for="10"><fmt:message key="acctmaintenance.usepredicted"/></label></td>
			<td width="80"><input type="radio" name="acctMaintenance.forecastSOD" ${acctMaintenance.forecastSOD == 'N' ? 'checked="checked"' : ''} value="${'N'}" id="11"   tabindex="20" style="width:13;" titleKey="acctmaintenance.usenone" /> <label
				title='<fmt:message key="acctmaintenance.usenone"/>' for="11"><fmt:message key="acctmaintenance.usenone"/></label></td>
		</tr>
		<tr>
			<td width="200">&nbsp;<b><fmt:message key="acctmaintenance.externalsod"/></b></td>
			<td width="120"><input type="radio" name="acctMaintenance.externalSOD" ${acctMaintenance.externalSOD == 'I' ? 'checked="checked"' : ''} value="${'I'}" id="12"   tabindex="21" style="width:13;" titleKey="acctmaintenance.useinternal" />
			<label title='<fmt:message key="acctmaintenance.useinternal"/>'
				for="12"><fmt:message key="acctmaintenance.useinternal"/></label>
			</td>

			<td width="120"><input type="radio" name="acctMaintenance.externalSOD" ${acctMaintenance.externalSOD == 'E' ? 'checked="checked"' : ''} value="${'E'}" id="13"   tabindex="22" style="width:13;" titleKey="acctmaintenance.useexternal" />
			<label title='<fmt:message key="acctmaintenance.useexternal"/>'
				for="13"><fmt:message key="acctmaintenance.useexternal"/></label>
			</td>
			<td width="140"><input type="radio" name="acctMaintenance.externalSOD" ${acctMaintenance.externalSOD == 'P' ? 'checked="checked"' : ''} value="${'P'}" id="14"   tabindex="23" style="width:13;" titleKey="acctmaintenance.usepredicted" />
			<label title='<fmt:message key="acctmaintenance.usepredicted"/>'
				for="14"><fmt:message key="acctmaintenance.usepredicted"/></label></td>
			<td width="60"><input type="radio" name="acctMaintenance.externalSOD" ${acctMaintenance.externalSOD == 'N' ? 'checked="checked"' : ''} value="${'N'}" id="15"   tabindex="24" style="width:13;" titleKey="acctmaintenance.usenone" /> <label
				title='<fmt:message key="acctmaintenance.usenone"/>' for="15"><fmt:message key="acctmaintenance.usenone"/></label></td>
		</tr>
	</table>
	</fieldset>
	</div>
	</div>
	</div>
	</div>

	<div id="sc3" class="tabcontent" visible="true" enabled="true"
		onfocus="true">
	<div id="AddAccountMaintenance1"
		style="position: absolute; top: 130px; width: 865px; height: 650px; border: 2px outset; border-top: thin; left: 24px"
		color="#7E97AF">
	<div id="Sweeping"
		style="position: absolute; z-index: 99; left: 4px; top: 5px; width: 850px; height: 75px;">
	<fieldset style="width: 853px; border: 2px groove; height: 253px!important;">
	<legend><fmt:message key="fielset.genDeatils"/></legend>
	<div id="General Parameters"
		style="position: absolute; z-index: 99; left: 8px; top: 10px; width: 680px; height: 32px;">
	<table width="950" height="188px" border="0" cellpadding="0"
		cellspacing="0" class="content">
		<tr height="22">
			<td width="220px"><b><fmt:message key="acclevel"/></b></td>
			<td colspan="2"><input type="radio" name="acctMaintenance.acctlevel" ${acctMaintenance.acctlevel == 'M' ? 'checked="checked"' : ''} value="${'M'}" id="19" tabindex="7"   style="width:13;" titleKey="tooltip.main"

			onclick="javascript:calllostFocus(); checkSubAccounts(this);" /> <label
				title='<fmt:message key="tooltip.main"/>' for="19"><fmt:message key="main"/></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<input type="radio" name="acctMaintenance.acctlevel" ${acctMaintenance.acctlevel == 'S' ? 'checked="checked"' : ''} value="${'S'}" id="20" tabindex="8"   style="width:13;"
				titleKey="tooltip.sub" onclick="javascript:callShowMainAcctId(); checkSubAccounts(this);" />
			<label title='<fmt:message key="tooltip.sub"/>' for="20"><fmt:message key="sub"/></label></td>
			<td width="385px">&nbsp;</td>
		</tr>
		<tr height="22">
			<td width="190px" align="left"><b><fmt:message key="mainacctId"/></b><input class="textAlpha"
				style="background: transparent; border: thin;" class="textAlpha"
				readonly name="accountIdMandatory" size="1"></td>
			<td colspan="3"><input id="mainaccountId" name="mainaccountId" readonly="readonly"
				class="textAlpha" style="width: 220px;margin-right: 5px; height: 22px;"
				title='<fmt:message key="tooltip.mainAcctID"/>'><input
				tabindex="11" id="dropdownbutton_2" type="button" value="..."
				onclick="getMainAccountList(event)" disabled=""
				title='<fmt:message key="tooltip.mainAcctID"/>'>&nbsp;&nbsp;&nbsp;<input
				style="background: transparent; border: thin; text-align: top; height: 19; width: 320px;"
				class="textAlpha" readonly name="mainaccountName" size="40"></td>
		</tr>
		<tr height="20">
			<td width="190px"><b><fmt:message key="autoswpswitch"/></b></td>
			<td width="140px" align="left"><input type="radio" name="acctMaintenance.autoswpswitch" ${acctMaintenance.autoswpswitch == 'S' ? 'checked="checked"' : ''} value="${'S'}"    id="21" style="width:13;" onclick="javascript:callShowSweeping();" titleKey="tooltip.stp" tabindex="12" /> <label
				title='<fmt:message key="tooltip.stp"/>' for="21"><fmt:message key="stp"/></label></td>


				<td width="240px" align="right"><b><fmt:message key="manswp"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="385px"><input type="checkbox"  name="acctMaintenance.mansweepflg" value="Y" ${requestScope.acctMaintenance.mansweepflg == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.mansweepflg == "Y"}' titleKey="tooltip.manSweep" style="width:13;" tabindex="15" onclick="javascript:callShowSweeping();" /></td>
		</tr>
		<tr height="20">
			<td width="190px">&nbsp;</td>
			<td width="140px"><input type="radio" name="acctMaintenance.autoswpswitch" ${acctMaintenance.autoswpswitch == 'U' ? 'checked="checked"' : ''} value="${'U'}" id="22" tabindex="14"   style="width:13;" onclick="javascript:callShowSweeping();" titleKey="tooltip.submit" />
			<label title='<fmt:message key="tooltip.submit"/>' for="22"><fmt:message key="submit"/></label></td>
			<td width="270px" align="right"><b><fmt:message key="subacctim"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b></td>
			<td width="385px"><input type="checkbox"  name="acctMaintenance.subAcctim" value="Y" ${requestScope.acctMaintenance.subAcctim == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.acctMaintenance.subAcctim == "Y"}' titleKey="subacctim.tooltip" style="width:13;" tabindex="13" onclick="javascript:callShowSweeping();" /></td>
		</tr>
		<tr height="20">
			<td width="190px">&nbsp;</td>
			<td width="140px" align="left"><input type="radio" name="acctMaintenance.autoswpswitch" ${acctMaintenance.autoswpswitch == 'N' ? 'checked="checked"' : ''} value="${'N'}" id="23" tabindex="16"   style="width:13;" onclick="javascript:callShowSweeping();" titleKey="tooltip.notincluded" /> <label
				title='<fmt:message key="tooltip.notincluded"/>' for="23"><fmt:message key="notincl"/></label></td>
			<td width="240px" align="right">&nbsp;&nbsp;</td>
			<td width="365px">&nbsp;</td>
		</tr>
		<tr height="24">
			<td width="190"><b><fmt:message key="accountmaintenanceadd.earliestSweepTime"/></b><label
				id="earliestSweepTimelabel"></label></td>

			<td width="140"><input type="text" id="swpTime" name="acctMaintenance.eodSweeptime" value="${acctMaintenance.eodSweeptime}"

				class="htmlTextNumeric" titleKey="tooltip.earliestSweepTime"
				maxlength="5" tabindex="19"
				style="width:42px;"
				onchange="return validateField(this,'cutofftime','timePat');" />
			</td>
			<td width="190px" align="right" ><b><fmt:message key="cutofftime"/></b><label
				id="cutOffTime"></label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="140px"><input type="text"  name="acctMaintenance.cutoff" value="${acctMaintenance.cutoff}"
				maxlength="5" tabindex="19" titleKey="tooltip.cutOffTime"
				style="width:42px;"
				onchange="return validateField(this,'cutofftime','timePat');" /></td>


		</tr>
		<tr height="20">
		<td width="190px"><b><fmt:message key="swpdays"/></b></td>
			<td width="140px"><input type="text"  name="acctMaintenance.swpdays" value="${acctMaintenance.swpdays}"
				maxlength="3" tabindex="17" titleKey="swpdays" style="width:30px;"
				onchange="return validateField(this,'swptime','numberPatAll',99,-99);" />
			</td>
			<td width="240px" align="right"><b><fmt:message key="sweepCode"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>

			<td width="365px"><input type="text"  name="acctMaintenance.sweepCode" value="${acctMaintenance.sweepCode}"  maxlength="3"
				onchange="return validateField(this,'sweepCode','alphaNumPat');"
				tabindex="18" titleKey="sweepCode" style="width:42px;" /></td>


		</tr>
		<tr height="22">
			<td width="190px"><b><fmt:message key="SweepFromBalance"/></b></td>
			<td width="140px"><select
				name="acctMaintenance.sweepFrmbal" tabindex="21"
				titleKey="SweepFromBalance" style="width:85px;">
				<option value="P" <c:if test="${'P' == requestScope.acctMaintenance.sweepFrmbal}">
    selected='selected'
</c:if>> Predicted </option>
				<option value="E" <c:if test="${'E' == requestScope.acctMaintenance.sweepFrmbal}">
    selected='selected'
</c:if>> External </option>
			</select></td>
			<td width="240px" align="right"><b><fmt:message key="bookcode"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="430px"><input id="bookCodeId" name="bookCodeId" class="textAlpha" style="margin-right: 5px; height: 22px;"
				readonly="readonly" size="12" title='<fmt:message key="tooltip.bookCode"/>'><input
				tabindex="20" id="dropdownbutton_3" type="button" value="..."
				onclick="getBookCodeList(event)" disabled=""
				title='<fmt:message key="tooltip.bookCode"/>'>
			<input class="textAlpha"
				style="background: transparent; width:130px; border: thin; text-align: top; height: 19;"
				name="bookCodeNameDisp" size="25"></td>
		</tr>
		<tr height="20">
			<td width="190"><b><fmt:message key="accountmaintenanceadd.defaulttargetBalance"/><label id="trgtBalintra"></label></b></td>

			<td width="140"><input type="text" id="trgtBal" name="acctMaintenance.targetbalanceasString" value="${acctMaintenance.targetbalanceasString}"

				class="htmlTextNumeric" titleKey="tooltip.tarBalAmount"
				maxlength="28" tabindex="22" style="width:165px;"
				onchange="return validateCurrency(this,'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value)" />

			</td>


			<td width="240px"  style="padding-left: 5px"><b><select name="acctMaintenance.tgtbalsign" tabindex="25"
				titleKey="tooltip.tarBalSign" style="width:40px;">
				<c:forEach var="item" items="${requestScope.targetsignlist}">
				<option value="${item.value}"
				<c:if test="${acctMaintenance.tgtbalsign == item.value}">selected</c:if>>
				${item.label}</option>
				</c:forEach>
			</select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message key="intermediary"/></b></td>

			<td width="365px"><input type="text" titleKey="intermediary" name="sweepintermediaries.intermediary" value="${sweepintermediaries.intermediary}"  style="width:250px;"
				maxlength="12" /></td>

		</tr>


		<tr height="20">
			<td width="190"><b><fmt:message key="maxswpamnt"/></b></td>

			<td width="140"><input type="text" id="maxAmountField" name="acctMaintenance.maxsweepamteasString" value="${acctMaintenance.maxsweepamteasString}"

				class="htmlTextNumeric" titleKey="tooltip.maxSweepAmount"
				maxlength="28" tabindex="22" style="width:165px;"
				onchange="validateSweepAmount(this)" />
			</td>

			<td width="240px" align="right" style="padding-left: 30px"><b><fmt:message key="accountmaintenanceadd.defaultSettMethod"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			<td width="365px"><select name="acctMaintenance.defaultSettleMethod" tabindex="25"
				titleKey="accountmaintenanceadd.defaultSettleMethodTooltip" style="width:165px;">
				<c:forEach var="item" items="${requestScope.defaultSettleMethodList}">
				<option value="${item.value}"
				<c:if test="${acctMaintenance.defaultSettleMethod == item.value}">selected</c:if>>
				${item.label}</option>
				</c:forEach>
			</select></td>

		</tr>


		<tr height="25">
			<td width="190"><b><fmt:message key="minswpamnt"/></b></td>

			<td width="140">

			<input type="text"  name="acctMaintenance.minseepamtasString" value="${acctMaintenance.minseepamtasString}"
				class="htmlTextNumeric" titleKey="tooltip.minSweepAmount"
				tabindex="26" maxlength="37" style="width:165px;"
				onchange="return validateCurrency(this,'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value)" />
			</td>

			<td width="240px" align="right"><b><fmt:message key="accountmaintenanceadd.assocForSweepBalnce"/></b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
			   <td width="350px"><input id="assAcctForSweepBal"
			     title='<fmt:message key="tooltip.assocForSweepBalnce"/>'
				 maxlength="3" disabled="true"

				tabindex="18"  style="width:42px;" />

				<div id="ddimagebuttons" style="margin-top: -24px;  margin-left: 90px; visibility: visible">
				<a	onMouseOut="collapsebutton(this)" tabindex="888"
				title='<fmt:message key="tooltip.assAcct"/>'
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onKeyDown="submitEnter(this)" onMouseUp="highlightbutton(this)"
				onClick="buildSpecificAcctSweepBalGrp(this)"><fmt:message key="button.maintain"/></a>

<!-- 				<a -->
<%-- 				class="disabled" disabled="disabled" title='<fmt:message key="tooltip.subAc"/>'><fmt:message --%>
<%-- 				key="button.maintain" /></a>	 --%>

				</div>

				</td>


		</tr>

	</table>
	</div>

	<div id="Sweeping"
		style="position: absolute; left: 8px; top: 10px; width: 700px; height: 32px;">
	<div id="ddimagebuttons"
		style="position: absolute; z-index: 99; left: 525px; top: 0px; width: 100px; height: 8px; visibility: visible">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td width="70px" id="subaccountbutton"
				title='<fmt:message key="tooltip.subAc"/>'></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons"
		style="position: absolute; z-index: 99; left: 10px; top: 0px; width: 10px; height: 8px; visibility: hidden">
	<table width="70" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td id="subaccountenablebutton" width="70px"><a
				onMouseOut="collapsebutton(this)" tabindex="9"
				title='<fmt:message key="tooltip.subAc"/>'
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onKeyDown="submitEnter(this)" onMouseUp="highlightbutton(this)"
				onClick="buildSubAccount('subAccounts')"><fmt:message key="button.sub"/></a></td>
			<td id="subaccountdisablebutton" width="70px"><a
				class="disabled" disabled="disabled" title='<fmt:message key="tooltip.subAc"/>'><fmt:message key="button.sub"/></a></td>


		</tr>
	</table>
	</div>
	</div>
	</fieldset>
	</div>

	<div id="Eod Sweeping"
		style="position: absolute; left: 4px; top: 262px; width: 856px; height: 22px;">
	<fieldset style="width: 853px; border: 2px groove; height: 267px!important;">
	<legend><fmt:message key="accountmaintenance.EODSweeping"/></legend>



	<div id="Currency" color="#7E97AF" style="word-wrap: break-word;position:absolute; border:0px outset; left:5px; width:847px; height:200px;">
	<div id="Currency" style="word-wrap: break-word;position:absolute;z-index:50;left:0px; top:0px; width:827px; height:60px !important;verflow-x: hidden">
		<table  class="sort-table"  bgcolor="#B0AFAF" width="1530px" border="0" cellspacing="1" cellpadding="0"  height="16px">
	<thead>
		<tr height="20px">
		    <td  align="center" title ='<fmt:message key="account.schedSweep.thisAccount"/>' width="1587" style="border-left-width: 0px;"><b><fmt:message key="account.schedSweep.thisAccount"/></b></td>
			<td  align="center" title ='<fmt:message key="account.schedSweep.againstAccount"/>' width="852px"><b><fmt:message key="account.schedSweep.againstAccount"/></b></td>
		</tr>
	</thead>
	</table>
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="2443px" border="0" cellspacing="1" cellpadding="0"  height="30px">
	<thead>
		<tr height="20px">
		    <td  title ='<fmt:message key="account.schedSweep.tooltip.scheduleFrom"/>' width="68px" style="border-left-width: 0px;"><b><fmt:message key="account.schedSweep.heading.scheduleFrom"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.scheduleTo"/>' width="68px"><b><fmt:message key="account.schedSweep.heading.scheduleTo"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepFromBalanceType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.sweepFromBalanceType"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepOnGrpBalance"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.sweepOnGrpBalance"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.targetBalanceType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.targetBalanceType"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.targetBalance"/>' align="center" width="150px"><b><fmt:message key="account.schedSweep.heading.targetBalance"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepDirection"/>' align="center" width="160px"><b><fmt:message key="account.schedSweep.heading.sweepDirection"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.minAmount"/>' align="center" width="100px"><b><fmt:message key="account.schedSweep.heading.minAmount"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.allowMultiple"/>' width="85px"><b><fmt:message key="account.schedSweep.heading.allowMultiple"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSweepBookcodeCr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.thisAccSweepBookcodeCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSweepBookcodeDr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.thisAccSweepBookcodeDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodCr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodDr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepEntityId"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.sweepEntityId"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepAccountId"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.sweepAccountId"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherSweepFromBalType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.otherSweepFromBalType"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherAccSweepBookcodeCr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.otherAccSweepBookcodeCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherAccSweepBookcodeDr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.otherAccSweepBookcodeDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodCr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodDr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodDr"/></b></td>
		</tr>
	</thead>
	</table>

</div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:843px; height:225px; overflow:scroll;">
	<div id="Currency" style="position:absolute;z-index:99;left:0px; top:22px; width:930px; height:10px;">
		<table class="sort-table" id="acctSweepSchedulecoll" width="2443px" border="0" cellspacing="1" cellpadding="0" height="433">
		<tbody id="tableAcctSchedule">
			<%int count = 0; %>
				<c:forEach items="${requestScope.acctSweepSchedulecoll}" var='acctSweepSchedule' >
				<% if( count%2 == 0 ) {%><tr  class="even"><% }else  { %> <tr  class="odd"> <%}++count; %>
					<td align="center" width="68px">
						<input type="hidden" name="sweepScheduleId" value="${acctSweepSchedule.sweepScheduleId}" />
						${acctSweepSchedule.scheduleFrom}&nbsp;
					</td>
					<td width="68px" align="center">
						<input type="hidden" name="uniqueId" value="${acctSweepSchedule.uniqueId}" />
						${acctSweepSchedule.scheduleTo}&nbsp;
					</td>
					<td width="83px" align="left">${acctSweepSchedule.sweepFromBalanceTypeAsString}&nbsp;</td>
					<td width="83px" align="left">${acctSweepSchedule.sweepOnGroupBalance}&nbsp;</td>
					<td width="83px" align="left">${acctSweepSchedule.targetBalanceTypeAsString}&nbsp;</td>
					<td width="150px" align="right">${acctSweepSchedule.targetBalanceAsString}&nbsp;</td>
					<td width="160px" align="left">${acctSweepSchedule.sweepDirectionAsString}&nbsp;</td>
					<td width="100px" align="right">${acctSweepSchedule.minAmountAsString}&nbsp;</td>
					<td width="85px" align="center">${acctSweepSchedule.allowMultipleAsString}&nbsp;</td>
					<td width="100px" align="left">${acctSweepSchedule.thisAccSweepBookcodeCr}&nbsp;</td>
					<td width="100px" align="left">${acctSweepSchedule.thisAccSweepBookcodeDr}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.thisAccSettleMethodCr}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.thisAccSettleMethodDr}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.sweepAccountEntity}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.sweepAccountId}&nbsp;</td>
					<td width="83px" align="left">${acctSweepSchedule.otherSweepFromBalTypeAsString}&nbsp;</td>
					<td width="100px" align="left">${acctSweepSchedule.otherAccSweepBookcodeCr}&nbsp;</td>
					<td width="100px" align="left">${acctSweepSchedule.otherAccSweepBookcodeDr}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.otherAccSettleMethodCr}&nbsp;</td>
					<td width="130px" align="left">${acctSweepSchedule.otherAccSettleMethodDr}&nbsp;</td>
				</tr>
			</c:forEach>
		</tbody>

	<tfoot><tr><td colspan="15" ></td></tr></tfoot>

</table>
</div>
</div>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute;  top:233px; width:965px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:955px; height:15px; visibility:visible;">
				<table width="900" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td style="padding-left: 220px" width="180px"><b><fmt:message key="account.schedSweep.usedInOther"/></b></td>
						<td width="50px"><input id="usedInOtherSize"
						    title='<fmt:message key="tooltip.usedInOtherSchedulers"/>'
							style="width:42px;" disabled
							tabindex="27" maxlength="5" /></td>
						<td width="140px">
						<div id="ddimagebuttons" style="margin-top: -3px;  visibility: visible"><a
							onMouseOut="collapsebutton(this)" tabindex="888"
							title='<fmt:message key="tooltip.schedSweep.show"/>'
							onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
							onKeyDown="submitEnter(this)" onMouseUp="highlightbutton(this)"
							onClick="buildAccsSweepsScheduleUrl(this)"><fmt:message key="account.schedSweep.show"/></a>	</div></td>
					</tr>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden; display:none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
			            <td id="addenablebutton">
							<a title='<fmt:message key="tooltip.addScenario"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAccsSweepsScheduleDetailsUrl('add'),'scenarioadd','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add"/></a>
						</td>
						<td id="adddisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
						</td>
						<td id="changeenablebutton">
							<a title='<fmt:message key="tooltip.changeScenario"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAccsSweepsScheduleDetailsUrl('change'),'scenariochange','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
						</td>
						<td id="changedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
						</td>
						<td id="viewenablebutton">
							<a  title='<fmt:message key="tooltip.viewScenario"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAccsSweepsScheduleDetailsUrl('view'),'scenarioview','left=50,top=190,width=1000,height=750,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
						</td>
						<td id="viewdisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
						</td>
						<td id="deleteenablebutton">
							<a  title='<fmt:message key="tooltip.deleteScenario"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('delete');" ><fmt:message key="button.delete"/></a>
						</td>
						<td id="deletedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
						</td>
					</tr>
				</table>

			  </div>
			</div>














	</fieldset>
	</div>

	<div id="Format"
		style="position: absolute; left: 4px; top: 530px; width: 700px; height: 32px;">
	<fieldset style="width: 853px; border: 2px groove; height: 116px!important;">
	<legend><fmt:message key="accountmaintenance.Format"/></legend>
	<table width="839px" height="50px" border="0" cellpadding="0"
		cellspacing="0" class="content">
		<tr>
			<td width="197">&nbsp;<b><fmt:message key="format.default"/>:</td>
			<td width="140px">&nbsp;<b><fmt:message key="format.Credit"/></b></td>
			<td width="30px"></td>
			<td width="140px">&nbsp;<b><fmt:message key="format.Debit"/></b></td>
			<td width="50px"></td>
			<td width="140px"><b><fmt:message key="format.accountSpecific"/>:</b></td>
			<td width="50px"><input id="subsweepaccountsize"
			    title='<fmt:message key="tooltip.accountSpecific"/>'
				style="width:42px;" disabled
				tabindex="27" maxlength="5" /></td>
			<td width="140px">
			<div id="ddimagebuttons" style="margin-top: -3px;  visibility: visible"><a
				onMouseOut="collapsebutton(this)" tabindex="888"
				title='<fmt:message key="tooltip.acctSpecBtn"/>'
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onKeyDown="submitEnter(this)" onMouseUp="highlightbutton(this)"
				onClick="buildSpecificAccount(this)"><fmt:message key="button.maintain"/></a>	</div></td>
		</tr>

		<tr height="25px">
			<td width="197">&nbsp;<b><fmt:message key="label.format.Int"/></b></td>
			<td width="140px"><select
				name="acctMaintenance.acctNewCrInternal" tabindex="32"
				style="width:140px;" titleKey="tooltip.creditIntMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
			<td width="30px"></td>
			<td width="146px"><select
				name="acctMaintenance.acctNewDrInternal" style="width:140px"
				tabindex="33" titleKey="tooltip.debitIntMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
		</tr>
		<tr height="25px">
			<td width="147px">&nbsp;<b><fmt:message key="label.format.Ext"/></b></td>
			<td width="140px"><select
				name="acctMaintenance.acctNewCrExternal" style="width:140px"
				tabindex="34" titleKey="tooltip.creditExtMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
			<td width="30px"></td>
			<td width="140px"><select
				name="acctMaintenance.acctNewDrExternal" style="width:140px"
				tabindex="35" titleKey="tooltip.debitExtMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
		</tr>
		<tr height="25px">
			<td width="141px">&nbsp;<b><fmt:message key="fomat.cdExtInt"/></b></td>
			<td width="140px"><select
				name="acctMaintenance.creditExternalInter" style="width:140px"
				tabindex="36" titleKey="tooltip.creditInterMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
			<td width="30px"></td>
			<td width="140px"><select
				name="acctMaintenance.debitExternalInter" style="width:140px"
				tabindex="37" titleKey="tooltip.debitInterMsg"
				onmouseover="getFormatList(this)" onfocus="getFormatList(this)">
			</select></td>
		</tr>

	</table>
	</fieldset>
	</div>
	</div>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 23; top: 785px; width: 867px; height: 39px; visibility: visible;">
	<div id="AddAccountMaintenance"
		style="position: absolute; left: 8; top: 4; width: 240px; height: 15px; visibility: visible;">
	<table width="210" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<c:if test="${'subAccountsAdd'==requestScope.methodName}">

				<td width="70px"><a onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:submitForm('save');"
					onKeyDown="submitEnter(this,event)" tabindex="39"
					title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a></td>
				<td width="70px">&nbsp;</td>
				<td width="70px">&nbsp;</td>

</c:if>

			<c:if test="${'view'==requestScope.methodName}">

				<td width="70px"><a onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="confirmClose('C');" onKeyDown="submitEnter(this,event)"
					tabindex="40" title='<fmt:message key="tooltip.close"/>'><fmt:message key="button.close"/></a></td>

</c:if>
			<c:if test="${'add'==requestScope.methodName}">


				<td width="70px"><a onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" tabindex="38"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)"
					onClick="javascript:openWindow(submitCopyFrom('copyFrom'),'messagefieldsWindow','left=50,top=190,width=690,height=236,toolbar=0, resizable=yes,scrollbars=no')"
					title='<fmt:message key="tooltip.copyFrom"/>'> <fmt:message key="button.cpyFrom"/></a></td>
				<td width="70px"><a onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" tabindex="39"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:submitForm('save');"
					onKeyDown="submitEnter(this,event)"
					title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a></td>
				<td width="70px" id="cancelbutton"><a
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:window.close();" onKeyDown="submitEnter(this,event)"
					tabindex="40" title='<fmt:message key="tooltip.cancel"/>'><fmt:message key="button.cancel"/></a></td>

</c:if>
			<c:if test="${'change'==requestScope.methodName}">

				<td width="70px"><a onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)" tabindex="39"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:submitForm('update');"
					onKeyDown="submitEnter(this,event)"
					title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save"/></a></td>
				<td width="70px" id="cancelbutton"><a
					title='<fmt:message key="tooltip.cancel"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onKeyDown="submitEnter(this,event)" onclick="javascript:window.close();"
					tabindex="40"><fmt:message key="button.cancel"/></a></td>

</c:if>
			<c:if test="${'change'==requestScope.methodName}">

				<td width="70px">&nbsp;</td>

</c:if>
		</tr>
	</table>
	</div>
	</div>

	<div id="AddAccountMaintenance"
		style="position: absolute; left: 795; top: 795px; width: 70px; height: 15px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<c:if test="${'add'==requestScope.methodName}">

				<td align="Right"><a tabindex="41" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Account Maintenance '), 'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

</c:if>
			<c:if test="${'change'==requestScope.methodName}">

				<td align="Right"><a tabindex="41" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Account Maintenance '), 'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

</c:if>
			<c:if test="${'view'==requestScope.methodName}">

				<td align="Right"><a tabindex="41" href=#
					onclick="javascript:openWindow(buildPrintURL('print','View Account Maintenance'), 'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

</c:if>
			<td align="right" id="Print"><a onclick="printPage();"
				tabindex="42" onfocusOut="onFocusFirstField()"
				onMouseOut="MM_swapImgRestore()" onKeyDown="submitEnter(this,event)"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
</form>
</body>
</html>