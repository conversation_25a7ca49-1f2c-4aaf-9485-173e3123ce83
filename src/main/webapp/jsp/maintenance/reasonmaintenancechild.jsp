<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
    <c:choose>
    <c:when test="${requestScope.methodName == 'save'}">

	<fmt:message key="reasonMaintenance.addScreen"/>

    </c:when>
    <c:otherwise>
        <c:if test="${requestScope.methodName == 'update'}">

		<fmt:message key="reasonMaintenance.changeScreen"/>

        </c:if>
    </c:otherwise>
</c:choose>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<SCRIPT language="JAVASCRIPT">

    var cancelcloseElements = new Array(1);
    cancelcloseElements[0] = "cancelbutton";

    <c:if test="${requestScope.parentFormRefresh == 'yes'}">
    window.opener.document.forms[0].method.value = "displayListByEntity";
    window.opener.document.forms[0].submit();
    self.close();
    </c:if>
    mandatoryFieldsArray = ["*"]

    /**
     * bodyOnLoad
     *
     * This method is called when window onloading and used to set form
     * values in the page and enable/disable the button
     */
    function bodyOnLoad() {



        <c:if test="${requestScope.methodName == 'save'}">
        var dropBox1 = new SwSelectBox(document.forms[0].elements['reasonMaintenance.id.entityId'], document.getElementById('entityName'));

        </c:if>


        document.getElementById("entityName").innerText = '${requestScope.entityName}';
        <!--start:code added by nageswararao for mantis 1580 issues found on STL testing-->
        document.forms[0].elements["reasonMaintenance.description"].value = unescape(document.forms[0].elements["reasonMaintenance.description"].value);

    }

    <!--End:code added by nageswararao for mantis 1580 issues found on STL testing-->

    /**
     * submitForm
     *
     * This method is used to submit form while clicking on the save/update button.
     */
    function submitForm(methodName) {
        if (methodName == 'save') {
            if (validateField(document.forms[0].elements['reasonMaintenance.id.reasonCode'], 'Number 2', 'alphaNumPat')) {

                if (validateForm(document.forms[0])) {
                    elementTrim(document.forms[0]);

                    if ((document.forms[0].elements["reasonMaintenance.description"].value).trim().length == 0) {
                        alert("<fmt:message key='alert.enterValideReasonDescription'/>");
                    } else if (document.forms[0].elements["reasonMaintenance.description"].value.length <= 50) {
                        document.forms[0].elements["reasonMaintenance.id.entityId"].disabled = "";
                        document.forms[0].method.value = methodName;
                        document.forms[0].selectedReasonCode.value = document.forms[0].elements["reasonMaintenance.id.reasonCode"].value;
                        document.forms[0].entityText.value = '${requestScope.entityName}';
                        document.forms[0].submit();
                    } else {
                        alert("<fmt:message key='interfacerules.alert.ruleValueLengthValidation2'/>");
                        document.forms[0].elements["reasonMaintenance.description"].focus();
                    }
                }
            } else {
                document.forms[0].elements['reasonMaintenance.id.reasonCode'].focus();
            }
        } else if (methodName == 'update') {
            if (validateForm(document.forms[0])) {
                elementTrim(document.forms[0]);
                if ((document.forms[0].elements["reasonMaintenance.description"].value).trim().length == 0) {
                    alert("<fmt:message key='alert.enterValideReasonDescription'/>");
                } else if (document.forms[0].elements["reasonMaintenance.description"].value.length <= 50) {
                    document.forms[0].elements["reasonMaintenance.id.entityId"].disabled = "";
                    document.forms[0].method.value = methodName;
                    document.forms[0].selectedReasonCode.value = document.forms[0].elements["reasonMaintenance.id.reasonCode"].value;
                    document.forms[0].entityText.value = '${requestScope.entityName}';
                    document.forms[0].submit();
                } else {
                    alert("<fmt:message key='interfacerules.alert.ruleValueLengthValidation2'/>");
                    document.forms[0].elements["reasonMaintenance.description"].focus();
                }
            }
        }


    }

    function validateForm(objForm) {
        var elementsRef = new Array(2);
        elementsRef[0] = objForm.elements["reasonMaintenance.id.reasonCode"];
        elementsRef[1] = objForm.elements["reasonMaintenance.description"];
        return validate(elementsRef);
    }

    function maxLengthTextArea(field, maxChars) {
        if (field.value.length > maxChars) {
            field.value = field.value.substring(0, maxChars);
        }
    }


</SCRIPT>
<form action="reasonMaintenance.do">
<input name="method" type="hidden" value="">
<input name="hostId" type="hidden" value="">
<input name="entityText" type="hidden" value="">
<input name="selectedReasonCode" type="hidden" value="">

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()" >
  <div id="ReasonMaintenanceChild" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:19px; width:620px; height:104px;">

 <div style="position:absolute; left:8px; top:2px; width:505px; height:80px;">  
	
	<table width="600" border="0" cellpadding="0" cellspacing="0">
    <tr height="23" width="100%">
        <td width="125px"><b><fmt:message key="reasonMaintenance.entity" /></b></td>
   
        <td width="100px">
            <input type="text"  name="reasonMaintenance.id.entityId" tabindex="1" class="htmlTextAlpha" titleKey="tooltip.entityId" maxlength="12" style="width:120px;" disabled="true"
                   value="${reasonMaintenance.id.entityId}"/>
        </td>

        <td width="250px" align="left">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span id="entityName" name="entityName" class="spantext" width="100%"></span>
        </td>
    </tr>
    <tr height="23" width="100%">
            <c:if test="${requestScope.methodName == 'save'}">
            <td width="125px"><b><fmt:message key="reasonMaintenance.reasonCode" /></b>*</td>

            <td width="100px">
                <input type="text" name="reasonMaintenance.id.reasonCode"  value="${reasonMaintenance.id.reasonCode}" tabindex="2" class="htmlTextAlpha" titleKey="tooltip.ReasonCode" maxlength="12" style="width:120px;" onchange="return validateField(this,'Number 2','alphaNumPat');" />
            </td>

            <td width="225px">
                <span id="reasonCode" name="reasonCode" class="spantext"></span>
            </td>
        </c:if>
         <c:if test="${requestScope.methodName != 'save'}">
            <td width="125px"><b><fmt:message key="reasonMaintenance.reasonCode" /></b></td>

            <td width="100px">
                <input type="text" name="reasonMaintenance.id.reasonCode" value="${reasonMaintenance.id.reasonCode}" tabindex="2" class="htmlTextAlpha" maxlength="30" style="width:120px;" disabled="true" onchange="validateField(this,'Number 2','alphaNumPat');" />
            </td>

            <td width="225px">
                <span id="reasonCode" name="reasonCode" class="spantext"></span>
            </td>
        </c:if>
    </tr>

    <tr height="23">
        <td width="125px"><b><fmt:message key="reasonMaintenance.description" /></b>*</td>

        <td width="325px" colspan="2">
            <textarea name="reasonMaintenance.description"   tabindex="3" titleKey="tooltip.Description"  class="htmlTextAlpha" style="width:200px;height:36px;overflow-y:scroll;overflow-x:hidden;" onkeydown="maxLengthTextArea(this,50);" onkeyup="maxLengthTextArea(this,50);">${reasonMaintenance.description}</textarea>
        </td>
    </tr>
</table>
	
	
</div>
</div>

 <div id="ReasonMaintenanceChild" style="position:absolute; left:560px; top:138px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		 <c:if test="${requestScope.methodName == 'save'}">
			<td align="Right">
				 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Reason Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
			 </td>
		 </c:if>
		  <c:if test="${requestScope.methodName == 'update'}">
		 			<td align="Right">
		 				 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Reason Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
		 			 </td>
		</c:if>
	
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:130px; width:620px; height:39px; visibility:visible;">
 <div id="ReasonMaintenanceChild" style="position:absolute; left:6; top:4; width:590px; height:19px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" >    
		<tr>                    
		 <td id="savebutton">		
			<a  tabindex="4" title='<fmt:message key = "tooltip.save"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"  onclick="javascript:submitForm('${methodName}')" onMouseUp="highlightbutton(this)" ><fmt:message key="button.save"/></a>
		</td>	
		  <td  width="70px" id="cancelbutton">		
               <a  tabindex="5" title='<fmt:message key = "tooltip.cancel"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');" ><fmt:message key="button.cancel"/></a>
		  </td>	
        </tr>
    </table>
</div>
</body>
</form>
</html>