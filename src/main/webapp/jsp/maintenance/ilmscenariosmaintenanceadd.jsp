<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title><c:if test="${requestScope.methodName == 'addScenario'}">
		<fmt:message key="ilmScenarioAdd.title.window.addScreen" />
	</c:if> <c:if test="${requestScope.methodName == 'changeScenario'}">
		<fmt:message key="ilmScenarioAdd.title.window.changeScreen" />
	</c:if><c:if test="${requestScope.methodName == 'viewScenario'}">
		<fmt:message key="ilmScenarioAdd.title.window.viewScreen" />
	</c:if></title>
<script language="JAVASCRIPT">
	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
		<c:choose>
		<c:when test="${requestScope.parentScreen != 'liquidityMonitor'}">
						window.opener.document.forms[0].method.value="listScenarios";
						window.opener.document.forms[0].submit();
		</c:when>
		<c:otherwise>
					window.opener.refreshGridData();
		</c:otherwise>
		</c:choose>
		self.close();
	</c:if>
	mandatoryFieldsArray=["ilmScenarioId","ilmScenarioName", "creditSuccessRate", "creditPctDelayed", "creditDelayTime", "successRateDebitPct",
	                      "debitPctDelayed", "debitDelayTime", "nonScnCreditSuccessRate", "nonScnDebitSuccessRate", "collateralAvlbl",
	                      "creditlineAvlbl", "unencumberedLiqAssetAvlbl", "otherSourcesAvlbl"];
	var allowReportingOnLoad = true;
	/**
	 * Called on first load of the screen
	 */
	function bodyOnLoad() {
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		//Enable or disable save button depending in save button request value
		var saveButton = "<%=request.getAttribute(SwtConstants.SAV_BUT_STS)%>";
		document.getElementById("savebutton").innerHTML = saveButton == 'true' ? document.getElementById("saveenablebutton").innerHTML : document.getElementById("savedisablebutton").innerHTML;
		<c:if test="${requestScope.methodName == 'addScenario'}">
		var entityDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmScenario.entityId"], document.getElementById("entityName"));
		var currencyGrpDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmScenario.currencyCode"], document.getElementById("currencyDesc"));
		</c:if>

		var transactionDropBoxElement = new SwSelectBox(document.forms[0].elements["ilmScenario.txnSetId"], document.getElementById("transactionName"));
		<c:if test="${requestScope.methodName != 'addScenario'}">
		allowReportingOnLoad = document.forms[0].elements["ilmScenario.allowReporting"].checked;
		document.getElementById("entityName").innerHTML = '${entityText}';
		document.getElementById("currencyDesc").innerHTML = '${currencyCodeText}';
		</c:if>
		var systemValue = "<%=request.getAttribute("systemValue")%>";
		if (systemValue == 'Y') {
			$('#publicPrivateRadio *').attr("disabled", true);
			$('#publicPrivateRadio1 *').attr("disabled", true);
			document.forms[0].elements["ilmScenario.dynamicTsetQuery"].disabled = true;
			document.forms[0].elements["ilmScenario.filterCondition"].disabled = true;
			document.forms[0].elements["ilmScenario.exclusionCondition"].disabled = true;
		}
		if (document.forms[0].elements["ilmScenario.currencyCode"].value == "All") {
			document.forms[0].elements["ilmScenario.txnSetId"].disabled = true;
		}
	}

	function validateSuccessRate(strField) {
		var res = validateField(strField,'Number 2','numberPat');
		 if(!res){
			strField.value = "";
			strField.focus();
	     }
		 return res;
	}

	/**
	* This method is used to check the delay time value
	*
	* @param strField
	* @return
	*/
	function validateDelayTime(strField){

		var delayTimeVal = strField.value;

		if((delayTimeVal.indexOf("+") == -1 && delayTimeVal.indexOf("-") == -1) && delayTimeVal.length == 4 && (delayTimeVal % 1 == 0)){
			alert("<fmt:message key='alert.delayTimeInterval'/>");
			strField.value = "";
			strField.focus();
			return false;
		}else{
		    var res = validateField(strField,'DelayTime','numberPatAll');
			 if(!res){
				strField.value = "";
				strField.focus();
		     }
			 return res;
		}
   }
	/**
	* This method is used to check the delay rate value
	*
	* @param strField
	* @return
	*/
	function validateDelayRate(strField){
		var delayRateVal=strField.value;
		if( delayRateVal.length == 3 && (delayRateVal % 1 == 0) && (delayRateVal >100)){
			alert("<fmt:message key='alert.delayRateBetween'/>");
			strField.value = "";
			strField.focus();
			return false;
		}else{
			var res = validateField(strField,'Number 2','numberPat');
			if(!res){
				strField.value = "";
				strField.focus();
		    }
			return res;
		}
	}

	/**
	* This method is used to check the delay rate value
	*
	* @param strField
	* @return
	*/
	function validateSourcesValue(strField){
		var delayRateVal=strField.value;
		if( delayRateVal.length == 3 && (delayRateVal % 1 == 0) && (delayRateVal >100)){
			alert("<fmt:message key='ilmScenario.alert.SourcesValue'/>");
			strField.value = "";
			strField.focus();
			return false;
		}else{
			var res = validateField(strField,'Number 2','numberPat');
			if(!res){
				strField.value = "";
				strField.focus();
		    }
			return res;
		}
	}

	function validatefilterCondtion(filterCondition){
		if(filterCondition.length>0){
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');

			requestURL=requestURL.substring(0,idy+1) ;
			requestURL = requestURL + appName+"/ilmTransScenario.do?method=testFilterCondtionClause";
			requestURL = requestURL + "&filterCondition=" + getMenuWindow().encode64(filterCondition);
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			oXMLHTTP.send();
			ResultAsString=new String(oXMLHTTP.responseText);

			if (ResultAsString!='SUCCESS'){
				alert("<fmt:message key="scenario.testQuery.error"/>" + ResultAsString);
							return false;
			}
		}
		return true;

		}

	/**
	* This method is used to check the maximum character length
	*
	* @param textArea
	* @param maxChars
	* @return none
	*/
	function maxLengthTextArea (textArea,maxChars){
		if(textArea.value.length > maxChars)
			textArea.value = textArea.value.substring(0, maxChars);
	}

	function submitForm(methodName){
		if(methodName != 'addScenario'){
			if(document.forms[0].elements["ilmScenario.id.ilmScenarioId"].value == ""||document.forms[0].elements["ilmScenario.ilmScenarioName"].value==""
				|| document.forms[0].elements["ilmScenario.collateralAvlbl"].value == "" || document.forms[0].elements["ilmScenario.creditlineAvlbl"].value == ""
				|| document.forms[0].elements["ilmScenario.unencumberedLiqAssetAvlbl"].value == "" || document.forms[0].elements["ilmScenario.otherSourcesAvlbl"].value == ""){
				alert("<fmt:message key='alert.pleaseFillAllMandatoryFields'/>");
		  		return;
			}
			if(document.forms[0].elements["ilmScenario.id.ilmScenarioId"].value.toUpperCase().trim() == "STANDARD"){
				alert("<fmt:message key='alert.changeScenarioId'/>");
		  		return;
			}
			if(!validateField(document.forms[0].elements["ilmScenario.id.ilmScenarioId"],'ilmScenarioId','alphaNumPatWithHyphenAndUnderScore'))
				return;
		}
		if (!validatefilterCondtion(document.forms[0].elements["ilmScenario.filterCondition"].value))
				return;

		if (document.forms[0].elements["ilmScenario.exclusionCondition"].value.trim().length > 0){
			if (!validatefilterCondtion(document.forms[0].elements["ilmScenario.exclusionCondition"].value))
				return;
		} else {
			document.forms[0].elements["ilmScenario.exclusionCondition"].value ="";
		}

		if (document.forms[0].elements["ilmScenario.creditSuccessRate"].value =="")
			document.forms[0].elements["ilmScenario.creditSuccessRate"].value ="100";

		if (document.forms[0].elements["ilmScenario.creditPctDelayed"].value =="")
			document.forms[0].elements["ilmScenario.creditPctDelayed"].value ="0";

		if (document.forms[0].elements["ilmScenario.creditDelayTime"].value =="")
			document.forms[0].elements["ilmScenario.creditDelayTime"].value ="0";

		if (document.forms[0].elements["ilmScenario.debitSuccessRate"].value =="")
			document.forms[0].elements["ilmScenario.debitSuccessRate"].value ="100";

		if (document.forms[0].elements["ilmScenario.debitPctDelayed"].value =="")
			document.forms[0].elements["ilmScenario.debitPctDelayed"].value ="0";

		if (document.forms[0].elements["ilmScenario.debitDelayTime"].value =="")
			document.forms[0].elements["ilmScenario.debitDelayTime"].value ="0";

		if (document.forms[0].elements["ilmScenario.nonScnCreditSuccessRate"].value =="")
			document.forms[0].elements["ilmScenario.nonScnCreditSuccessRate"].value ="100";

		if (document.forms[0].elements["ilmScenario.nonScnDebitSuccessRate"].value =="")
			document.forms[0].elements["ilmScenario.nonScnDebitSuccessRate"].value ="100";

		$("div[id^='publicPrivateRadio']").each(function() {
			var div = $(this);
			var checkedValue =	 div.find("input[type ='radio']:checked").val();
			if(checkedValue != undefined)
				document.forms[0].publicPrivate.value = checkedValue;
		});
		$("div[id^='defaultLegendTextRadio']").each(function() {
			var div = $(this);
			var checkedValue =	 div.find("input[type ='radio']:checked").val();
			if(checkedValue != undefined)
				document.forms[0].defaultLegendText.value = checkedValue;
		});
		document.forms[0].selectedEntityId.value = document.forms[0].elements["ilmScenario.entityId"].value;
		document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["ilmScenario.currencyCode"].value;
		document.forms[0].ilmScenarioId.value = document.forms[0].elements["ilmScenario.id.ilmScenarioId"].value;
		document.forms[0].parentScreen.value='${parentScreen}';
		document.forms[0].method.value = methodName;
		enableFields();
		if (document.forms[0].elements["ilmScenario.allowReporting"].checked) {
			if ('${requestScope.methodName}' == 'changeScenario' && allowReportingOnLoad) {
				document.forms[0].submit();
				return;
			}
			var yourstate=window.confirm('<fmt:message key="ilmScenario.alert.allowReporting"/>');
			if (yourstate==true){
			   document.forms[0].submit();
			}
		} else {
		   document.forms[0].submit();
		}
	}

	/**
	 * Enable all the fields which are disabled before submitting
	 **/
	function enableFields(){
		document.forms[0].elements["ilmScenario.publicPrivate"].disabled = "";
		document.forms[0].elements["ilmScenario.systemScenario"].disabled = "";
		document.forms[0].elements["ilmScenario.activeScenario"].disabled = "";
		document.forms[0].elements["ilmScenario.allowReporting"].disabled = "";
		document.forms[0].elements["ilmScenario.filterCondition"].disabled = "";
		document.forms[0].elements["ilmScenario.exclusionCondition"].disabled = "";
		document.forms[0].elements["ilmScenario.dynamicTsetQuery"].disabled = "";
		document.forms[0].elements["ilmScenario.defaultLegendText"].disabled = "";
		document.forms[0].elements["ilmScenario.throughputMonitor"].disabled = "";
	}
	</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onunload="call()">
	<form action="ilmTransScenario.do">
		<input name="method" type="hidden" value="">
		<input name="selectedEntityId" type="hidden" value="">
		<input name="selectedCurrencyCode" type="hidden" value="">
		<input name="publicPrivate" type="hidden" value="">
		<input name="ilmScenarioId" type="hidden" value="">
		<input name="parentScreen" type="hidden" value="">
		<input name="defaultLegendText" type="hidden" value="">

		<div id="ilmScenarioMaintenance"
			style="position: absolute; left: 10px; top: 10px; width: 880px; height: 93; border: 2px outset;"
			color="#7E97AF">
				<div id="ilmScenarioMaintenance" style="position: absolute; left: 8px; top: 0px; width: 740px; height: 93;">
					<table width="700px" border="0" cellpadding="0" cellspacing="0" height="60px" class="content">
						<c:choose>
    					<c:when test="${requestScope.methodName == 'addScenario'}">
							<tr>
								<td width="150" style="padding-left:10px;">
									<b><fmt:message key="ilmScenario.entity" /></b>
								</td>
								<td width="20">&nbsp;</td>
								<td width="200px" >
									<select name="ilmScenario.entityId"
												 onchange="submitForm('addScenario');"
												 titleKey="tooltip.selectEntity" style="width:120px"
												 tabindex="1">
							<c:forEach items="${requestScope.entities}" var="entity">
							   <option
								  value="${entity.value}"
								  <c:if test="${ilmScenario.entityId == entity.value}">selected="selected"</c:if>>
								  ${entity.label}
							   </option>
							</c:forEach>
									</select>
								</td>
								<td width="15">&nbsp;</td>
								<td width="290" ><span id="entityName" name="entityName"
									class="spantext"></td>
							</tr>
						</c:when>
						<c:otherwise>
							<tr>
								<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmScenario.entity" /></b></td>
								<td width="20">&nbsp;</td>
								<td width="200px"><input type="text" cssClass="htmlTextAlpha"
										name="ilmScenario.entityId" value="${ilmScenario.entityId}"
										style="width:120px;height: 21px" disabled="true" /></td>
								<td width="20">&nbsp;</td>
								<td width="290"><span id="entityName" name="entityName"
									class="spantext"></td>
							</tr>
						    </c:otherwise>
							</c:choose>
						<c:choose>
    					<c:when test="${requestScope.methodName == 'addScenario'}">
							<tr>
								<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmScenario.currency" /></b></td>
								<td width="20">&nbsp;</td>
								<td width="200"><select
										name="ilmScenario.currencyCode"
										titleKey="tooltip.selectCurrencyCode"
										style="width:55px;height: 21px"
										onchange="submitForm('addScenario')" tabindex="2">
									<c:forEach items="${requestScope.currencyList}" var="ccy">
									   <option
										  value="${ccy.value}"
										  <c:if test="${ilmScenario.currencyCode == ccy.value}">selected="selected"</c:if>>
										  ${ccy.label}
									   </option>
									</c:forEach>
									</select></td>

								<td width="20">&nbsp;</td>
								<td width="290"><span id="currencyDesc" class="spantext"></td>
							</tr>
						</c:when>
						<c:otherwise>
							<tr>
								<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmScenario.currency" /></b></td>
								<td width="20">&nbsp;</td>
								<td width="200"><input type="text" cssClass="htmlTextAlpha"
										name="ilmScenario.currencyCode" value="${ilmScenario.currencyCode}"
										style="width:55px;height: 21px" disabled="true" /></td>

								<td width="20">&nbsp;</td>
								<td width="290"><span id="currencyDesc" class="spantext"></td>
							</tr>
						</c:otherwise>
						</c:choose>
						</table>
						<table width="520px" border="0" cellpadding="0" cellspacing="0"
						height="25px" class="content">
						<tr>
							<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmScenario.id" />
							<c:if test="${requestScope.methodName == 'addScenario'}"> *</c:if> </b></td>
							<td width="4">&nbsp;</td>
							<td width="285" id="ilmScenarioId"><input type="text" cssClass="htmlTextAlpha"
									name="ilmScenario.id.ilmScenarioId" value="${ilmScenario.id.ilmScenarioId}"
									titleKey="tooltip.scenarioId" style="width:285px;height: 21px"
									maxlength="22" tabindex="3"
									onchange="return validateField(this,'ilmScenarioId','alphaNumPatWithHyphenAndUnderScore');"
									<c:if test="${requestScope.methodName != 'addScenario'}">disabled</c:if>/></td>

							<td width="20">&nbsp;</td>
						</tr>
					</table>
					<div style="position: absolute; left: 600; top: 3; width: 270px">
						<div>
							<b><fmt:message key="ilmScenario.createdBy" /></b>
							<input type="text" cssClass="htmlTextAlpha"
								name="ilmScenario.createdByUser" value="${ilmScenario.createdByUser}"
								style="width:125px;height: 21px;background:transparent;border:thin;margin-left:25px;padding-top:6" />
						</div>
					</div>
					<div style="position: absolute; left: 650; top: 30; width: 200px">
						<div>
							<b><fmt:message key="ilmScenario.on" /></b>
							<input type="text" cssClass="htmlTextAlpha"
								name="ilmScenario.createDateAsString" value="${ilmScenario.createDateAsString}"
								style="width:125px;height: 21px;background:transparent;border:thin;margin-left:25px;padding-top:6" />
						</div>
					</div>

				</div>
				<div id="Line"
					style="position: absolute; left: 245px; top: 120px; width: 636px; height: 20px;">
					<table width="100%">
						<tr>
							<td><img src="images/tabline.gif" width="100%" height="1"></td>
						</tr>
					</table>
				</div>
			<div id="ddimagetabs" style="position: absolute; left:0px;height: 20px; top: 102px">
				<a href="#" id="firstsc" onmouseout="revertback('sc1',this);" tabindex="3"
							onmouseover="changecontent('sc1',this)"
							onClick="changeselected('sc1');expandcontent('sc1', this)">
					<b><fmt:message key="ilmScenario.tab.general" /></b>
				</a>
				<a href="#" onmouseout="revertback('sc2',this);" tabindex="4"
							onmouseover="changecontent('sc2',this)"
							onClick="changeselected('sc2');expandcontent('sc2', this)">
					<b><fmt:message key="ilmScenario.tab.movements" /></b>
	       		 </a>
	       		<a href="#" onmouseout="revertback('sc3',this);" tabindex="5"
							onmouseover="changecontent('sc3',this)"
							onClick="changeselected('sc3');expandcontent('sc3', this)">
					<b><fmt:message key="ilmScenario.tab.sources" /></b>
	       		</a>
			</div>
			<div id="sc1" class="tabcontent">
				<div style="position: absolute; top: 124px; width: 878px; height: 501px; border: 2px outset; border-top: 1px outset;">
					<div style="position: absolute; left: 4px; top: -5px; width: 865px; height: 280px;">
						<fieldset style="position: absolute; top:20;left: 5px;width: 845px; border:2px groove; height: 300px; ">
							<legend><fmt:message key="ilmScenario.legend.mainDetails" /></legend>
							<div id="ilmScenarioMaintenance" style="position: absolute; left: 10; width: 840px; height: 75px;">
								<table width="840px" border="0" cellpadding="0" cellspacing="0"
									height="75px" class="content">
									<tr height="25" >
										<td style="padding-left: 10px; width: 170px"><b><fmt:message key="ilmScenario.scenarioName" />
										<c:if test="${requestScope.methodName != 'viewScenario'}"> *</c:if></b></td>
										<td id="ilmScenarioName"><input type="text" cssClass="htmlTextAlpha"
												name="ilmScenario.ilmScenarioName"
												value="${ilmScenario.ilmScenarioName}"
												tabindex="6"
												titleKey="tooltip.scenarioName" maxlength="50"
												style="width:640px;height: 21px"
												onchange="return validateField(this,'section.sectionName','ascii_standard')"
												<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>/></td>
									</tr>
									<tr height="35">
										<td style="padding-left: 10px; padding-bottom: 35px; width: 170px"><b><fmt:message key="ilmScenario.scenarioDescription" /></b></td>

										<td><textarea tabindex="7"
												titleKey="tooltip.scenarioDescription"
												name="ilmScenario.ilmScenarioDescription"
												onkeydown="maxLengthTextArea(this,100);"
												onkeyup="maxLengthTextArea(this,100);"
												<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
												style="width: 640px;height: 50px">${ilmScenario.ilmScenarioDescription}</textarea></td>
									</tr>
									<tr height="25">
										<td style="padding-left: 10px; width: 170px;padding-top:20px">
											<b>
				 								<fmt:message key="ilmAccountGroupDetails.labelLegendText" />
											</b>
										</td>
										<td>
											<div id="defaultLegendTextRadio" style="position: absolute;">
												<input type="radio" style="width:15px;" tabindex="8"
													titleKey="ilmAccountGroupDetails.tootlipLegendText"
													name="ilmScenario.defaultLegendText"
													${ilmScenario.defaultLegendText == 'I' ? 'checked="checked"' : 'Yes'} value="${'I'}"
													 <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
													 />
				 								<label title='<fmt:message key="ilmAccountGroupDetails.tootlipLegendText"/>'><fmt:message key="ilmAccountGroupDetails.labelId" /></label>&nbsp;
											</div>
											<div id="defaultLegendTextRadio1" style="position: absolute; left:280">
												<input type="radio" tabindex="9" style="width:16px;"
													titleKey="ilmAccountGroupDetails.tootlipLegendText"
													name="ilmScenario.defaultLegendText"  ${ilmScenario.defaultLegendText == 'N' ? 'checked="checked"' : 'Yes'} value="${'N'}"
													   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
												<label title='<fmt:message key="ilmAccountGroupDetails.tootlipLegendText"/>'><fmt:message key="ilmAccountGroupDetails.labelName" /></label>&nbsp;
											</div>
										</td>
									</tr>
									<tr height="25">
										<td style="padding-left: 10px; width: 170px;padding-top:20px">
											<b>
				 								<fmt:message key="ilmScenario.label.privatePublic" />
											</b>
										</td>
										<td>
											<div id="publicPrivateRadio" style="width: 100; position: absolute; left: 176;">
												<input type="radio" id="5" style="width:16px; margin-left: 2px;" tabindex="4"
													titleKey="tooltip.privatePublic"
													name="ilmScenario.publicPrivate"
													${ilmScenario.publicPrivate == 'PRIVATE' ? 'checked="checked"' : 'Yes'} value="${'PRIVATE'}"
													<c:if test="${requestScope.methodName == 'viewScenario'}">disabled</c:if>
													 />
												<label title='<fmt:message key="tooltip.privatePublic"/>' ><fmt:message key="ilmScenario.private" /></label>&nbsp;
											</div>
											<div id="publicPrivateRadio1" style="width: 150; position: absolute; left: 280;">
												<input type="radio" id="4" tabindex="5" style="width:16px;"
													titleKey="tooltip.privatePublic"
													name="ilmScenario.publicPrivate"
													${ilmScenario.publicPrivate == 'PUBLIC' ? 'checked="checked"' : 'Yes'} value="${'PUBLIC'}"
													   <c:if test="${requestScope.methodName == 'viewScenario'}">disabled</c:if> />
												<label title='<fmt:message key="tooltip.privatePublic"/>' ><fmt:message key="ilmScenario.public" /></label>&nbsp;
											</div>
										</td>
									</tr>
								</table>
								<table style="position: absolute; width: 210; top: 159;">
									<tr style="padding-bottom: 50px;">
										<td style="padding-left: 10px;"><b><fmt:message key="ilmScenario.label.systemScen" /></b></td>
										<td width="32">
											<input type="checkbox" 	tabindex="10"
															style="width:13px;margin-left:5px"
															titleKey="ilmScenario.tooltip.systemScen"
															name="ilmScenario.systemScenario"
															disabled="true"
															value="Y"
															${ilmScenario.systemScenario == 'Y' ? 'checked' : ''}
															cssClass="htmlTextAlpha" />
										</td>
									</tr>
									<tr>
										<td style="padding-left: 10px; padding-top:10px"><b><fmt:message key="ilmScenario.label.activeScen" /></b></td>
										<td width="32" style="padding-top: 10px;">
											<input type="checkbox" 	tabindex="10"
															style="width:13px;margin-left:5px"
															titleKey="ilmScenario.tooltip.activeScen"
															name="ilmScenario.activeScenario"
												   			<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
															value="Y"
															${ilmScenario.activeScenario == 'Y' ? 'checked' : ''}
															cssClass="htmlTextAlpha"/>
										</td>
									</tr>
									<tr>
										<td style="padding-left: 10px; padding-top:10px"><b><fmt:message key="ilmScenario.label.AllowReporting" /></b></td>
										<td width="32" style="padding-top: 10px;">
											<input type="checkbox" 	tabindex="10"
															style="width:13px;margin-left:5px"
															titleKey="ilmScenario.tooltip.allowReporting"
															name="ilmScenario.allowReporting"
												   			<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
															value="Y"
															${ilmScenario.allowReporting == 'Y' ? 'checked' : ''}
															cssClass="htmlTextAlpha" />
										</td>
									</tr>
									<tr>
										<td style="padding-left: 10px; padding-top:10px"><b><fmt:message key="ilmScenario.label.throughputMonitoring" /></b></td>
										<td width="32" style="padding-top: 10px;">
											<input type="checkbox"	tabindex="10"
															style="width:13px;margin-left:5px"
															titleKey="ilmScenario.tooltip.throughputMonitoring"
															name="ilmScenario.throughputMonitor"
												  			 <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
															value="Y"
															${ilmScenario.throughputMonitor == 'Y' ? 'checked' : ''}
															cssClass="htmlTextAlpha" />
										</td>
									</tr>
								</table>
							</div>
						</fieldset>
					</div>
				</div>
			</div>
			<div id="sc2" class="tabcontent">
				<div style="position: absolute; top: 124px; width: 878px; height: 508px; border: 2px outset; border-top: 1px outset;">

					<div
						style="position: absolute; left: 4px; width: 865px; height: 15px; top: 3px;">
						<table style="position: absolute; width: 250; height: 15px;">
							<tr style="padding-bottom: 10px;">
								<td width="25"><input type="checkbox" tabindex="10"
										style="width:13px;margin-left:20px"
										titleKey="ilmScenario.tooltip.joinMvt"
										name="ilmScenario.joinMvt"
										<c:if test="${requestScope.methodName == 'viewScenario'}">disabled</c:if>
                                        value="Y" ${ilmScenario.joinMvt== 'Y' ? 'checked' : ''}
										cssClass="htmlTextAlpha" /></td>
								<td style="padding-left: 10px;"><b><fmt:message key="ilmScenario.label.joinToMvt" /></b></td>

							</tr>
						</table>

					</div>

					<div style="position: absolute; left: 4px; top: 15px; width: 865px; height: 105px;">

						<fieldset style="position: absolute; top:10;left: 5px;width: 845px; border:2px groove; height: 110px; ">
								<legend>
									<fmt:message key="ilmScenario.legend.excludedMovements" />
								</legend>
								<div id="ilmScenarioMaintenance"
														 style="position: absolute; left: 16; height: 25px;">
									<table>
										<tr height="50">
											<td width="167px" style=" padding-bottom: 60px;">
												<b style="width: 167px">
													<fmt:message key="ilmScenario.filterCondition" />
												</b>
											</td>
											<td>
												<textarea  tabindex="10"
																					titleKey="tooltip.excludedCondition"
																					name="ilmScenario.exclusionCondition"
														   							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
																					style="width: 640px;height: 80px">${ilmScenario.exclusionCondition}</textarea>
											</td>
										</tr>
									</table>
								</div>
						</fieldset>
					</div>
					<div style="position: absolute; left: 4px; top: 135px; width: 865px; height: 176px;">
						<fieldset style="position: absolute;left: 5px;width: 845px; border:2px groove; height: 184px; ">
							<legend><fmt:message key="ilmScenario.legend.scenarioMovements" /></legend>
							<div id="ilmScenarioMaintenance"
								 style="position: absolute; left: 16; height: 25px;">
								<table>
									<tr height="50">
										<td width="167px" style=" padding-bottom: 60px;">
											<b style="width: 167px"><fmt:message key="ilmScenario.filterCondition" /></b>
										</td>
										<td>
											<textarea  tabindex="10"
															titleKey="tooltip.filterCondition"
															name="ilmScenario.filterCondition"
													   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
															style="width: 640px;height: 80px">${ilmScenario.filterCondition}</textarea>
										</td>
									</tr>
								</table>
							</div>
							<div id="ilmScenarioMaintenance"
								style="position: absolute; left: 190; padding-top: 91; width: 80px; height: 25px;z-index: -1;">
								<b><fmt:message key="ilmScenario.credits" /></b>
							</div>
							<div id="ilmScenarioMaintenance"
								style="position: absolute; left: 490; padding-top: 91; width: 80px; height: 25px;z-index: -1;">
								<b><fmt:message key="ilmScenario.debits" /></b>
							</div>
							<div style="position: relative; top: 106; width: 440px; height: 70px;">
								<table width="410px" border="0" cellpadding="0" cellspacing="0"
									height="52px" class="content">
									<tr height="10">
										<td style="padding-left: 20px; width: 170px">
											<b><fmt:message key="ilmScenario.succesRate" />
											<c:if test="${requestScope.methodName != 'viewScenario'}">*</c:if></b>
										</td>
										<td id="creditSuccessRate" style="width: 100px">
											<input type="text" cssClass="htmlTextAlpha" tabindex="11"
														titleKey="tooltip.successRateCreditPct" maxlength="3"
														name="ilmScenario.creditSuccessRate"
												   		value="${ilmScenario.creditSuccessRate}"
														onchange="return validateSuccessRate(this);"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b>
										</td>
									</tr>
									<tr height="10" style="margin-top: : 20px;">
										<td style="padding-left: 20px; width: 170px">
											<b><fmt:message key="ilmScenario.delayRate" />
											<c:if test="${requestScope.methodName != 'viewScenario'}">*</c:if></b>
										</td>
										<td id="creditPctDelayed" style="width: 100px">
											<input type="text"  	cssClass="htmlTextAlpha" titleKey="tooltip.delayRateCreditPct"
														name="ilmScenario.creditPctDelayed" value="${ilmScenario.creditPctDelayed}" maxlength="3"
														onchange="return validateDelayRate(this);" tabindex="12"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percentBy" /></b>
										</td>
										<td id="creditDelayTime" style="padding-left: 10px">
											<input type="text"  cssClass="htmlTextAlpha" titleKey="tooltip.delayTime"
														maxlength="4" name="ilmScenario.creditDelayTime" value="${ilmScenario.creditDelayTime}"
														onchange="return validateDelayTime(this);" tabindex="13"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.mins" /></b>
										</td>
									</tr>
								</table>
							</div>
							<div
								style="position: relative; left: 490; top: 36; width: 330px; height: 60px;">
								<table width="370px" border="0" cellpadding="0" cellspacing="0"
									height="52px" class="content">
									<tr height="10">
										<td id="successRateDebitPct" style="width: 100px"><input type="text"
												cssClass="htmlTextAlpha"
												titleKey="tooltip.successRateDebitPct" maxlength="3"
												name="ilmScenario.debitSuccessRate" value="${ilmScenario.debitSuccessRate}"
												onchange="return validateSuccessRate(this);" tabindex="14"
												<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  style="width:40px;height: 21px" /><b
												style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b></td>

									</tr>
									<tr height="10">
										<td id="debitPctDelayed" style="width: 100px"><input type="text"
												cssClass="htmlTextAlpha"
												name="ilmScenario.debitPctDelayed" value="${ilmScenario.debitPctDelayed}"
												titleKey="tooltip.delayRateDebitPct" maxlength="3"
												onchange="return validateDelayRate(this);" tabindex="15"
												<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px" /><b
											style="margin-left: 10px"><fmt:message key="ilmScenario.percentBy" /></b></td>
										<td id="debitDelayTime" style="padding-left: 10px"><input type="text"
												cssClass="htmlTextAlpha" name="ilmScenario.debitDelayTime"
												value="${ilmScenario.debitDelayTime}"
												titleKey="tooltip.delayTime" maxlength="4"
												onchange="return validateDelayTime(this);" tabindex="16"
												<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  style="width:40px;height: 21px" /><b
											style="margin-left: 10px"><fmt:message key="ilmScenario.mins" /></b></td>
									</tr>
								</table>
							</div>
						</fieldset>
						</div>
						<div style="position: absolute; left: 4px; top: 315px; width: 865px; height: 61px;">

						<fieldset style="position: absolute;left: 5px;width: 845px; border:2px groove; height: 65px; ">
							<legend><fmt:message key="ilmScenario.legend.remainingMovements" /></legend>
							<div id="ilmScenarioMaintenance"
								style="position: absolute; left: 190; width: 80px; height: 15px;">
								<b><fmt:message key="ilmScenario.credits" /></b>
							</div>
							<div id="ilmScenarioMaintenance"
								style="position: absolute; left: 490; width: 80px; height: 15px;">
								<b><fmt:message key="ilmScenario.debits" /></b>
							</div>
							<table width="410px" border="0" cellpadding="0" cellspacing="0"
									height="40px" class="content">
									<tr height="30">
										<td style="position: absolute;top:38;padding-left: 20px; width: 170px ;top:20;"><b><fmt:message key="ilmScenario.succesRate" />
										<c:if test="${requestScope.methodName != 'viewScenario'}"> *</c:if></b></td>
										<td id="nonScnCreditSuccessRate" style="width: 100px;position: absolute;left:190; top:20;">
										<input type="text"
												cssClass="htmlTextAlpha" tabindex="17"
												titleKey="tooltip.successRateCreditPct" maxlength="3"
												name="ilmScenario.nonScnCreditSuccessRate" value="${ilmScenario.nonScnCreditSuccessRate}"
												onchange="return validateSuccessRate(this);"
											   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px" /><b
												style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b></td>

										<td id="nonScnDebitSuccessRate" style="width: 100px;position: absolute;left:490; top:20;">
										<input type="text"
												cssClass="htmlTextAlpha"
												titleKey="tooltip.successRateDebitPct" maxlength="3"
												name="ilmScenario.nonScnDebitSuccessRate" value="${ilmScenario.nonScnDebitSuccessRate}"
												onchange="return validateSuccessRate(this);" tabindex="18"
											   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px" /><b
												style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b></td>

									</tr>
									</table>
									</fieldset>
									</div>
								<div style="position: absolute; left: 4px; top: 375px; width: 865px; height: 117px;">

								<fieldset style="position: absolute; top:5;left: 5px;width: 845px; border:2px groove; height: 115px; ">
									<legend><fmt:message key="ilmScenario.extraTransaction" /></legend>
									<table>
									<tr style="position: absolute;left:17;">
										<td width="165px"><b style="width: 160px;"><fmt:message key="ilmScenario.extraTransaction" /></b></td>

										<td width="130px">
										<select
												name="ilmScenario.txnSetId" style="width:120px"
												<c:if test="${requestScope.screenFieldsStatus == 'true'? 'true': ''}">disabled</c:if> tabindex="19"
												titleKey="tooltip.extraTransactionSet">

										<c:forEach items="${requestScope.transactionList}" var="transaction">
										   <option
											  value="${transaction.value}"
											  <c:if test="${ilmScenario.txnSetId == transaction.value}">selected="selected"</c:if>>
											  ${transaction.label}
										   </option>
										</c:forEach>

										</select>
											</td>
										<td width="280px"><span style=" width: 280px; padding-left: 10px;" id="transactionName" class="spantext"></td>
									</tr>
							</table>
							<div id="ilmScenarioMaintenance"
								 style="position: absolute; left: 16; top: 25; height: 25px;">
								<table>
									<tr height="50">
										<td style=" padding-bottom: 60px; width: 167px">
											<b style="width: 167px"><fmt:message key="ilmScenario.label.dynamicQuery" /></b>
										</td>
										<td>
											<textarea  tabindex="10"
															titleKey="ilmScenario.tooltip.dynamicQuery"
															name="ilmScenario.dynamicTsetQuery" ${ilmScenario.dynamicTsetQuery}
													   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
															style="width: 640px;height: 65px"></textarea>
										</td>
									</tr>
								</table>
							</div>

						</fieldset>
					</div>
				</div>
			</div>
			<div id="sc3" class="tabcontent">
				<div style="position: absolute; top: 124px; width: 878px; height: 501px; border: 2px outset; border-top: 1px outset;">
					<div style="position: absolute; left: 4px; top: -5px; width: 865px; height: 150px;">
						<fieldset style="position: absolute; top:20;left: 5px;width: 845px; border:2px groove; height: 150px; ">
							<legend><fmt:message key="ilmScenario.legend.sourcesOfFunds" /></legend>
							<div id="ilmScenarioMaintenance" style="position: absolute; width: 840px; height: 75px;">
								<table width="500px" border="0" cellpadding="0" cellspacing="0" height="75px" class="content">
									<tr height="30">
										<td style="padding-left: 20px; width: 250px">
											<b><fmt:message key="ilmScenario.label.collateralAvlbl" />
											<c:if test="${requestScope.methodName != 'viewScenario'}"> *</c:if></b>
										</td>
										<td id=collateralAvlbl style="width: 100px">
											<input type="text"  cssClass="htmlTextAlpha" tabindex="11"
														titleKey="ilmScenario.tooltip.collateralAvlbl" maxlength="3"
														name="ilmScenario.collateralAvlbl" value="${ilmScenario.collateralAvlbl}"
														onchange="return validateSourcesValue(this);"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b>
										</td>
									</tr>
									<tr height="30">
										<td style="padding-left: 20px; width: 250px">
											<b><fmt:message key="ilmScenario.label.creditlineAvlbl" />
											<c:if test="${requestScope.methodName != 'viewScenario'}"> *</c:if></b>
										</td>
										<td id="creditlineAvlbl" style="width: 100px">
											<input type="text"  cssClass="htmlTextAlpha" tabindex="11"
														titleKey="ilmScenario.tooltip.creditlineAvlbl" maxlength="3"
														name="ilmScenario.creditlineAvlbl"  value="${ilmScenario.creditlineAvlbl}"
														onchange="return validateSourcesValue(this);"
                                                   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b>
										</td>
									</tr>
									<tr height="30">
										<td style="padding-left: 20px; width: 300px">
											<b><fmt:message key="ilmScenario.label.unencumberedLiqAssetAvlbl" />
											<c:if test="${requestScope.methodName != 'viewScenario'}">*</c:if></b>
										</td>
										<td id="unencumberedLiqAssetAvlbl" style="width: 100px">
											<input type="text" cssClass="htmlTextAlpha" tabindex="11"
														titleKey="ilmScenario.tooltip.unencumberedLiqAssetAvlbl" maxlength="3"
														name="ilmScenario.unencumberedLiqAssetAvlbl"
												   		value="${ilmScenario.unencumberedLiqAssetAvlbl}"
														onchange="return validateSourcesValue(this);"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b>
										</td>
									</tr>
									<tr height="30">
										<td style="padding-left: 20px; width: 250px">
											<b><fmt:message key="ilmScenario.label.otherSourcesAvlbl" />
											<c:if test="${requestScope.methodName != 'viewScenario'}"> *</c:if></b>
										</td>
										<td id="otherSourcesAvlbl" style="width: 100px">
											<input type="text"  cssClass="htmlTextAlpha" tabindex="11"
														titleKey="ilmScenario.tooltip.otherSourcesAvlbl" maxlength="3"
														name="ilmScenario.otherSourcesAvlbl" value="${ilmScenario.otherSourcesAvlbl}"
														onchange="return validateSourcesValue(this);"
												   		<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:40px;height: 21px"/>
											<b style="margin-left: 10px"><fmt:message key="ilmScenario.percent" /></b>
										</td>
									</tr>
								</table>
							</div>
						</fieldset>
					</div>
				</div>
			</div>

		</div>
		

		<div id="ilmccyParamsMaintenance"
			style="position: absolute; left: 825; top: 660; width: 60px; height: 29px; visibility: visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0"
				height="20">
				<tr>
					<c:if test="${requestScope.methodName == 'addScenario'}">
						<td align="Right"><a tabindex="20" href=#  
							onclick="javascript:openWindow(buildPrintURL('print','Add ILM Scenario Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()" 
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img style=" margin-bottom: 5px;"
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
					</c:if>
					<c:if test="${requestScope.methodName == 'changeScenario'}">
						<td align="Right"><a tabindex="21" href=#
							onclick="javascript:openWindow(buildPrintURL('print','Change ILM Scenario Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
					</c:if>
					<c:if test="${requestScope.methodName == 'viewScenario'}">
						<td align="Right"><a tabindex="22" href=#
							onclick="javascript:openWindow(buildPrintURL('print','View ILM Scenario Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
							onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
								src="images/help_default.GIF " name="Help" border="0"
								title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
					</c:if>
					<td align="right" id="Print">&nbsp; <a tabindex="23"
						onclick="printPage();" onMouseOut="MM_swapImgRestore()"
						onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
							src="images/Print.gif " name="Print" border="0"
							title='<fmt:message key="tooltip.printScreen"/>'></a>
					</td>
				</tr>
			</table>
		</div>

		<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 10; top: 650; width: 880px; height: 39px; visibility: visible;">
			<div id="ScenarioCategory"
				style="position: absolute; left: 2; top: 4; width: 200px; height: 15px; visibility: visible;">
				<table width="140" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<td id="savebutton"></td>
						<td id="cancelbutton" width="70px"><a
							title='<fmt:message key="tooltip.cancel"/>' tabindex="24"
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.cancel" /></a></td>
					</tr>
				</table>
			</div>
			<div style="position: absolute; left: 6; top: 4; width: 554px; height: 15px; visibility: hidden; display: none;">
				<table width="350" border="0" cellspacing="0" cellpadding="0"
					height="20" style="visibility: hidden">
					<tr>
						<td id="saveenablebutton" width="70"><a tabindex="25"
							title='<fmt:message key="tooltip.save"/>'
							onMouseOut="collapsebutton(this)"
							onMouseOver="highlightbutton(this)"
							onMouseDown="expandbutton(this)"
							onMouseUp="highlightbutton(this)"
							onclick="javascript:submitForm('${methodName eq 'addScenario' ? 'saveScenario': 'updateScenario'}');"><fmt:message key="button.save" /></a></td>
						<td id="savedisablebutton"><a class="disabled"
							disabled="disabled"><fmt:message key="button.save" /></a></td>
					</tr>
				</table>
			</div>
		</div>
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
	</form>

</body>
</html>