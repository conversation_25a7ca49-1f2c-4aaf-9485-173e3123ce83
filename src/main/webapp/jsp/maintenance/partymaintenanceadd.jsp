<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title>
    <c:choose>
        <c:when test="${'add' == requestScope.methodName}">
            <fmt:message key="partymaintenance.addScreen" />
        </c:when>
        <c:when test="${'change' == requestScope.methodName}">
            <fmt:message key="partymaintenance.changeScreen" />
        </c:when>
    </c:choose>
</title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <link rel="stylesheet" type="text/css" href="SwtComponents/select2/select2.min.css" />
	<script src="SwtComponents/select2/lodash.min.js"></script>
	<script src="SwtComponents/select2/select2.min.js"></script>
</head>

<style type="text/css">
.select2-results {
    max-height: 120px;
}

table#tab1,table#tab2,table#tab3 
{
    border-collapse:separate;
    border-spacing:0 5px;
}

.select2-container {
	width: 180px !important;
}

.select2-choice .select2-default {
height: 21px !important;
}
.select2-container .select2-choice{
	border-radius :0px !important;

}

.select2-container .select2-choice > .select2-chosen{
	color:black !important;
}
input[type=text]
{
	height: 23px !important;
}
.select2-container .select2-choice .select2-arrow{
	border-left : 0px !important;
	background: none !important;
	background: rgba(0, 0, 0, 0) !important;
}

</style>
		
<SCRIPT language="JAVASCRIPT">
var totalCount = '${totalCount}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterCriteria = "${requestScope.filterCriteria}";
var filterstatus= "${requestScope.filterStatus}";
var currPage = '${requestScope.currentPage}';
var maxPage = '${requestScope.maxPage}';

var requestURL=new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;    
var oXMLHTTP = new XMLHttpRequest();

<c:if test="${'yes' == requestScope.parentFormRefresh}">
window.opener.CallBackApp();
self.close();
</c:if>


mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";

function enableFields(){
	document.forms[0].elements["party.id.partyId"].disabled = "";
	document.forms[0].elements["party.id.entityId"].disabled = "";	
	document.forms[0].elements["entityName"].disabled = "";	
	
}	
function assignCheckBoxId(tableId)
	{
		var tableElement = document.getElementById(tableId);
		var tbodyElement = tableElement.getElementsByTagName("tbody")[0];
		var rowElements = tbodyElement.getElementsByTagName("tr");

		for( var rowIdx = 0 ; rowIdx < rowElements.length ; ++rowIdx)
		{
			var paramCode = rowElements[rowIdx].getAttribute("paramCode");
			var colls = rowElements[rowIdx].getElementsByTagName("td");

			addHiddenElement("paramCode_"+rowIdx,paramCode);

			for( var colIdx = 1 ; colIdx < colls.length ; ++colIdx)
			{
				var checkBoxElement = colls[colIdx].getElementsByTagName("input")[0];
				var isChecked = checkBoxElement.checked;
				var hiddenElId = "custodian_"+paramCode +"_"+colIdx;
				checkBoxElement.id = hiddenElId;
				checkBoxElement.name = hiddenElId;
			}			
		}
	}
/*
*This fuction excecuted when save button clicked in Add party screen 
*@param element
*/
function submitFormSave(element){
	
	if($('#parentPartyCombo').select2('data') && $('#parentPartyCombo').select2('data').text) {
		document.forms[0].elements['party.parentParty'].value = $('#parentPartyCombo').select2('data').text;
	}else {
		document.forms[0].elements['party.parentParty'].value = "";
	}
	//validation for party id
	if(validateField(document.forms[0].elements['party.id.partyId'],'party.partyId','alphaNumPat')){
		//validation for party name
		if(validateField(document.forms[0].elements['party.partyName'],'custodian.custodianName','alphaNumPatWithHyphenUnderscoreWithSpace')){
			elementTrim(document.forms[0]);
			//validation of form
			if(validateForm(document.forms[0]) ){
				enableFields();
				//setting values and submitting form
				document.forms[0].method.value = 'save';	
				document.forms[0].filterCriteria.value = '${requestScope.filterCriteria}';
				document.forms[0].pageNoValue.value = '${requestScope.currentPage}';
				document.forms[0].maxPages.value = '${requestScope.maxPage}';
				document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
				document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
				document.forms[0].currentPage.value = '${requestScope.currentPage}';
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
				document.forms[0].parentScreenPartyId.value = "${requestScope.parentScreenPartyId}";
				document.forms[0].parentScreenPartyName.value = "${requestScope.parentScreenPartyName}";
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
				document.forms[0].parentScreenEntityId.value = '${requestScope.parentScreenEntityId}';
				document.forms[0].submit();
			}
		}
		else{
		document.forms[0].elements['party.partyName'].focus();
		}
	}else{
	document.forms[0].elements['party.id.partyId'].focus();
	}
}
/*
*This fuction used to upadte the changed fileds and submit the form
*@param element
*/
function submitFormUpdate(element){
	if($('#parentPartyCombo').select2('data') && $('#parentPartyCombo').select2('data').text) {
		document.forms[0].elements['party.parentParty'].value = $('#parentPartyCombo').select2('data').text;
	}else {
		document.forms[0].elements['party.parentParty'].value = "";
	}
	//check for parentID
	if(checkParentId()==0){
		// validation for partyname
		if(validateField(document.forms[0].elements['party.partyName'],'custodian.custodianName','alphaNumPatWithHyphenUnderscoreWithSpace')){
			//Validating the form
			if(validateForm(document.forms[0]) ){
				enableFields();
				//update the base values
				document.forms[0].method.value = 'update';	
				document.forms[0].pageNoValue.value = '${requestScope.currentPage}';
				document.forms[0].maxPages.value = '${requestScope.maxPage}';	
				document.forms[0].filterCriteria.value = '${requestScope.filterCriteria}';
				document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
				document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
				document.forms[0].currentPage.value = '${requestScope.currentPage}';
			/*
			 * Start:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
				document.forms[0].parentScreenPartyId.value = "${requestScope.parentScreenPartyId}";
				document.forms[0].parentScreenPartyName.value = "${requestScope.parentScreenPartyName}";
			/*
			 * End:Code Modified by Chinna on 7-Nov-2012 for Mantis
			 * 1961:INTERNAL: Refactor frame party_list procedure including
			 * removing duplication to improve maintainability
			 */
				document.forms[0].parentScreenEntityId.value = '${requestScope.parentScreenEntityId}';
				document.forms[0].submit();
			}
		}else{
		document.forms[0].elements['party.partyName'].focus();
		}
	}else{
	//throughing alert for wrong parent Id
	alert("<fmt:message key="alert.changeParty.parentParty"/>");
	document.forms[0].elements['party.parentParty'].focus();
	}	
}
function validateForm(objForm){
  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["party.id.partyId"];
  elementsRef[1] = objForm.elements["party.partyName"];
  
  return validate(elementsRef);
}
/*
*This fuction load first when screen is laoding.in this function parentparty combox laoding is performed
*/

	function bodyOnLoad() {
		//load the PArent Party Combo box
		var parties;
		var dataProvider ;
		var dropBox2 = new SwSelectBox(
				document.forms[0].elements["party.parentParty"], document
						.getElementById("partyDesc"));
			if("${requestScope.partiesAsString}"){
				parties = JSON.parse('${requestScope.partiesAsString}');
			}else {
				parties = "";
			}
			var listItems = "<option selected str=''></option>";
			var parentPartyId = '${requestScope.parentPartyId}';
			var parentPartyName = '${requestScope.parentPartyName}';
			
			dataProvider = Array.isArray(parties.party)?parties.party:[parties.party]
		  // init select 2
		  $('#parentPartyCombo').select2({
		    data: dataProvider,
		    placeholder: '',
		    allowClear: true,   // Shows an X to allow the user to clear the value.
		    multiple: false,
		    // query with pagination
		    query: function(q) {
		      var pageSize,
		        results,
		        that = this;
		      pageSize = 20; // or whatever pagesize
		      results = [];
		      if (q.term && q.term !== '') {
		        // HEADS UP; for the _.filter function i use underscore (actually lo-dash) here
		        results = _.filter(that.data, function(e) {
		        	if(e.text) {
		        		var text = ""+e.text;
		          		return text.toUpperCase().indexOf(q.term.toUpperCase()) >= 0;
		        	}else 
	        		{
	        			return false;
	        		}
		        });
		      } else if (q.term === '') {
		        results = that.data;
		      }
		      q.callback({
		        results: results? results.slice((q.page - 1) * pageSize, q.page * pageSize) : [],
		        more: results?results.length >= q.page * pageSize:[],
		      });
		    },
		  });
		  
		  $("#parentPartyCombo").on("select2-selecting", function(e) {
			    var value = $(this).val();
			    document.getElementById("partyDesc").textContent = e.choice.id;
		  });
		  console.log('parentPartyId', parentPartyId);
		  console.log('parentPartyName', parentPartyName);
		  $('#parentPartyCombo').select2('data', {"text":parentPartyId,"id":parentPartyName});

		  
		  document.getElementById("partyDesc").textContent =  $('#parentPartyCombo').select2('data').id;
	}
	/*
	 *This fuction used to chaeck the status of the Parent Party Id
	 *return value
	 */
	function checkParentId() {
		//get the parent Party Id
		var changeParentId = document.forms[0].elements['party.parentParty'].value;
		//get the current entity id
		var entityId = document.forms[0].elements['party.id.entityId'].value;
		//get the current party Id
		var partyId = document.forms[0].elements['party.id.partyId'].value;
		//framing URL for Ajax request
		var sURL = requestURL + appName + "/party.do?method=checkParentId";

		//apending values in the request
		sURL = sURL + "&entityId=" + entityId;
		sURL = sURL + "&partyId=" + partyId;
		sURL = sURL + "&changeParentId=" + changeParentId;
		oXMLHTTP.open("POST", sURL, false);
		//sending request
		oXMLHTTP.send();
		//geting response from ajax request
		var value = oXMLHTTP.responseText;
		if (value == 0 || value == 1) {
			return value;
		} else {
			alert(value);
		}
	}
	/**
	 * This method is used to check the maximum character length
	 * 
	 * @param element
	 * @param maxlength
	 * @return none	 
	 */
     function limitTextArea(element,maxlength,e)
     {
		var event = (window.event || e);
		var key = event.keyCode;
		var selectedText;

		if (window.getSelection) {
			selectedText = window.getSelection();
		} else if (document.getSelection) // FireFox
		{
			selectedText = document.getSelection();
		} else if (document.selection) // IE 6/7
		{
			selectedText = document.selection.createRange().text;
		}

		if (element.value.length > maxlength) {
			element.value = element.value.substring(0, maxlength);
		}
		if ((selectedText == "") && element.value.length > maxlength - 1) {
			if ((!(key == 8)) && (!(key == 46)) && (!(key == 37))
					&& (!(key == 9)) && (!(key == 35)) && (!(key == 36))
					&& (!(key == 38)) && (!(key == 39)) && (!(key == 40)) // protect some key from "onkeyDown" event, that can be used by user 
					&& (element.value.length >= maxlength)) // we need to protect this button from "onkeyDown" event , because user can use it to edit text
			{
				if (window.event)
					window.event.returnValue = false;
				else
					event.preventDefault();

			}
		}

	}
</SCRIPT>


<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);"
	onunload="call()">


<form action="party.do" onsubmit="return validate(this);" method="post">

	<input name="oldValue" type="hidden" value="">

	<input name="method" type="hidden" value="save">
	<input name="parentScreenPartyId" type="hidden" value="">
	<input name="parentScreenPartyName" type="hidden" value="">
	<input name="parentScreenEntityId" type="hidden" value="">
	<input name="pageNoValue" type="hidden" value='${currPage}'>
	<input name="maxPages" type="hidden" value='${maxPage}'>

	<input name="filterCriteria" type="hidden" value='${filterCriteria}'>
	<input name="selectedFilter" type="hidden" value='${CurrentFilter}'>
	<input name="selectedSort" type="hidden" value='${currentSort}'>
	<input name="totalCount" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="currentPage" type="hidden" value='${currPage}'>
	<input name="prevEnabled" type="hidden" value="">
	<input name="nextEnabled" type="hidden" value="">
	
	<input name="party.parentParty" type="hidden" value="">


	<input name="goToPageNo" type="hidden" value="">

	<div id="CustodianAdd"
		style="position: absolute; left: 20px; top: 10px; width: 660px; height: 223px; border: 2px outset;"
		color="#7E97AF">
	<div id="CustodianAdd"
		style="position: absolute; left: 8px; top: 4px; width: 470px; height: 100px;">


	<table width="470" id="tab1"  border="0" cellpadding="0" cellspacing="0"
		height="80">
		<tr height="24">
			<td width="42" align="left"><b><fmt:message key="party.entityId" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="350" align="left"><c:if test="${'change' == requestScope.methodName}">
    <input type="text" title="<fmt:message key='tooltip.entityId'/>" name="party.id.entityId" style="width:120px;" class="htmlTextAlpha"
           disabled="${screenFieldsStatus}" value="${party.id.entityId}" />&nbsp;&nbsp;&nbsp;&nbsp;<input
					value="${entityName}" name="entityName"
					readonly=true style="background: transparent; border: thin; width: 190px;" />
			</c:if> <c:if test="${'add' == requestScope.methodName}">
    <input type="text" title="<fmt:message key='tooltip.entityId'/>" name="party.id.entityId" style="width:120px;" class="htmlTextAlpha"
           disabled="${screenFieldsStatus}" value="${party.id.entityId}" />&nbsp;&nbsp;&nbsp;&nbsp;<input
					value="${entityName}" name="entityName"
					readonly=true style="background: transparent; border: thin; width: 190px;" />
			</c:if></td>
		</tr>

		<tr height="24">

			<c:if test="${'change' == requestScope.methodName}">
				<td width="42" align="left"><b><fmt:message key="party.partyId" /></b></td>
				<td width="28">&nbsp;</td>
                <!-- adding tooltip for party id-->
				<td title='<fmt:message key="tooltip.partyId"/>' width="280"><input type="text" tabindex="1" name="party.id.partyId" class="htmlTextAlpha"
       maxlength="12" style="width:120px;" disabled="${screenFieldsStatus}" value="${party.id.partyId}" /></td>
			</c:if>
			<c:if test="${'add' == requestScope.methodName}">
				<td width="42" align="left"><b><fmt:message key="party.partyId" /></b>*</td>
				<td width="28">&nbsp;</td>
				<td title='<fmt:message key="tooltip.enterPartyId"/>' width="280">
					<input type="text" tabindex="1" name="party.id.partyId" style="width:120px;" class="htmlTextAlpha" maxlength="12" value="${party.id.partyId}" />
				</td>
			</c:if>

		</tr>
		<tr height="24">
			<td width="42"><b><fmt:message key="party.partyName" /></b>*</td>
			<td width="53">&nbsp;</td>
			<td title='<fmt:message key="tooltip.enterPartyName"/>' width="280">
				<textarea name="party.partyName" rows="2" cols="50" style="width:280px;height:50px;"
						  onkeyup="limitTextArea(this,60,event);" onkeydown="limitTextArea(this,60,event);"
						  tabindex="2">${party.partyName}</textarea>
			</td>
		</tr>
	</table>
	<!-- Type field added -->
	<table width="490" id="tab2" border="0" cellspacing="0" cellpadding="0">
			<tr height="24">
				<td width="42"><b><fmt:message key="party.header" /></b></td>
				<td width="46">&nbsp;</td>
				<td width="95">
					<input type="radio" id="1" tabindex="3" name="party.partyType" value="U" style="width:13px;" title="<fmt:message key='party.typecust'/>"
						   ${party.partyType == 'U' ? 'checked' : ''} />
					<label title='<fmt:message key="party.type"/>' for="1"><fmt:message key="party.typecust" /></label>
				</td>
				<td width="80">
					<input type="radio" id="2" tabindex="4" name="party.partyType" value="B" style="width:13px;" title="<fmt:message key='party.branch'/>"
						   ${party.partyType == 'B' ? 'checked' : ''} />
					<label title='<fmt:message key="party.type"/>' for="2"><fmt:message key="party.branch" /></label>
				</td>
				<td width="80">
					<input type="radio" id="3" tabindex="5" name="party.partyType" value="C" style="width:13px;" title="<fmt:message key='party.current'/>"
						   ${party.partyType == 'C' ? 'checked' : ''} />
					<label title='<fmt:message key="party.type"/>' for="3"><fmt:message key="party.current" /></label>
				</td>
				<td width="80">
					<input type="radio" id="4" tabindex="6" name="party.partyType" value="O" style="width:13px;" title="<fmt:message key='acc.othertype'/>"
						   ${party.partyType == 'O' ? 'checked' : ''} />
					<label title='<fmt:message key="party.type"/>' for="4"><fmt:message key="acc.othertype" /></label>
				</td>
			</tr>
		</table>
	<table border="0" width="650">
		<tr height="26px" width="97">
			<td width="95"><b><fmt:message key="party.parentId" /><label
				id="parentParty"></label></td>
			<td width="200"><input placeholder=" " id="parentPartyCombo" /></td><td width="350"><span id="partyDesc" class="spantext"></td></td>

		</tr>
	</table>
	</div>
	</div>

	<div id="PartyMainadd"
		style="position: absolute; left: 605; top: 250px; width: 70px; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right">
				<c:choose>
					<c:when test="${'add' == requestScope.methodName}">
						<a tabindex="10" href="#"
						   onclick="javascript:openWindow(buildPrintURL('print','Add Party Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)"><img
						   src="images/help_default.GIF" name="Help" border="0"
						   title='<fmt:message key="tooltip.helpScreen"/>'></a>
					</c:when>
					<c:when test="${'change' == requestScope.methodName}">
						<a tabindex="10" href="#"
						   onclick="javascript:openWindow(buildPrintURL('print','Change Party Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
						   onMouseOut="MM_swapImgRestore()"
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)"><img
						   src="images/help_default.GIF" name="Help" border="0"
						   title='<fmt:message key="tooltip.helpScreen"/>'></a>
					</c:when>
				</c:choose>
			</td>

			<td align="right" id="Print">
				<a tabindex="10" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)">
					<img src="images/Print.gif" name="Print" border="0" title='<fmt:message key="tooltip.printScreen"/>'>
				</a>
			</td>
		</tr>
	</table>
	</div>


	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 243; width: 660px; height: 39px; visibility: visible;">
	<div id="SectionMaintenance"
		style="position: absolute; left: 6; top: 4; width: 525px; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<c:choose>
				<c:when test="${'add' == requestScope.methodName}">
					<!-- Commented for pagination and sorting -->

					<td width="70">
						<a tabindex="8" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitFormSave(this);">
							<fmt:message key="button.save" />
						</a>
					</td>
				</c:when>

				<c:when test="${'change' == requestScope.methodName}">
					<td width="70">
						<a tabindex="8" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitFormUpdate(this);">
							<fmt:message key="button.save" />
						</a>
					</td>
				</c:when>
			</c:choose>

			<td id="cancelbutton" width="70"><a tabindex="9"
				title='<fmt:message key="tooltip.cancel"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><fmt:message key="button.cancel" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>
