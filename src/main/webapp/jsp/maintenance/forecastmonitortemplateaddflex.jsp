<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
<head>
<title>
<c:choose>
    <c:when test="${requestScope.screenName == 'changeScreen'}">
        <fmt:message key="label.forecastMonitorTemplateChange.title.window" />
    </c:when>
    <c:otherwise>
        <fmt:message key="label.forecastMonitorTemplateAdd.title.window" />
    </c:otherwise>
</c:choose></title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<style>
body {
	margin: 0px;
	overflow: hidden
}
</style>
<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
<script type="text/javascript">
		var appName = "<%=SwtUtil.appName%>";
		var screenRoute = "forecastMonitorTemplateMain";
		var requestURL = new String('<%=request.getRequestURL()%>');
		
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ;
			

			window.onunload = call;
			
			var templateId = '${requestScope.selectedTemplateId}';
			var templateName = '${requestScope.selectedTemplateName}';
			var userId = '${requestScope.selectedUserId}';
			var isPublic = '${requestScope.isPublic}';
			var screenName = '${requestScope.screenName}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var callFrom = '${requestScope.callFrom}';
			var detailWindow= null;
			var copyWindow= null;
			
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			
			// Set the label values
			/*var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["alert"] = new Array ();
			// Copy From button
			label["text"]["button-cpyFrom"] = "<fmt:message key="button.cpyFrom"/>";
			label["tip"]["button-cpyFrom"] = "<fmt:message key="button.cpyFrom"/>";	
			
			// Template id
			label["text"]["templateid"] = "<fmt:message key="label.forecastMonitorTemplateAdd.templateId"/>";
			label["tip"]["templateid"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.templateId"/>";		
			
			// Template name
			label["text"]["templatename"] = "<fmt:message key="label.forecastMonitorTemplateAdd.templateName"/>";
			label["tip"]["templatename"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.templateName"/>";
			
			// User id
			label["text"]["userid"] = "<fmt:message key="label.forecastMonitorTemplateAdd.userId"/>";
			label["tip"]["userid"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.userId"/>";
			
			// Public
			label["text"]["public"] = "<fmt:message key="label.forecastMonitorTemplateAdd.public"/>";
			label["tip"]["public"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.public"/>";
			
			// move up
			label["tip"]["moveup"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.moveUp"/>";
			
			// move down
			label["tip"]["movedown"] = "<fmt:message key="tooltip.forecastMonitorTemplateAdd.moveDown"/>";
			
			
			// Add button
			label["text"]["button-add"] = "<fmt:message key="button.add"/>";
			label["tip"]["button-add"] = "<fmt:message key="button.add"/>";	
			
			// Change button
			label["text"]["button-change"] = "<fmt:message key="button.change"/>";
			label["tip"]["button-change"] = "<fmt:message key="button.change"/>";	
			
			// Delete button
			label["text"]["button-delete"] = "<fmt:message key="button.delete"/>";
			label["tip"]["button-delete"] = "<fmt:message key="button.delete"/>";	
			
			
			// Close button
			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
			
			
			label["text"]["label-confirm"] = "<fmt:message key="button.confirm"/>";
			
			// alerts
			label["alert"]["template-save"] = "<fmt:message key="alert.forecasttemplate.savetemplate"/>";	
			label["alert"]["template-exist"] = "<fmt:message key="alert.forecasttemplate.templateexist"/>";
			label["alert"]["template-mandatory"] = "<fmt:message key="alert.forecasttemplate.mandatory"/>";
			label["alert"]["template-columndelete"] = "<fmt:message key="alert.forecasttemplate.columndelete"/>";*/
			
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
				var param = '/' + appName + '/forecastMonitorTemplate.do?method='+methodName;
    			detailWindow = window.open(param,'forecastMonitorTemplateColumn','left=20,top=290,width=800,height=500,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
				return false;
			}
			
			/**
			 * openCopyFromWindow
			 * @param methodName
			 * Method to load copy from  screen
			 */
			function openCopyFromWindow(methodName){
				var param = '/' + appName + '/forecastMonitorTemplate.do?method='+methodName;
    			copyWindow = window.open(param,'forecastMonitorTemplateCopy','left=20,top=290,width=370,height=96,toolbar=0, resizable=yes, status=no, scrollbars=yes','true');
				return false;
			}
			
			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Add / Change Forecast Monitor Template'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			/**
			 * unLockTemplate
			 * @param templateId
			 * @param templateName
			 * @param userId
			 * @param isPublic
			 * Method to load child screens
			 */
			function unLockTemplate(templateId,templateName,userId,isPublic){
    			var param = "/forecastMonitorTemplate.do?method=unLockTemplate";
				param = param+"&templateId="+templateId;
				param = param+"&templateName="+templateName;
				param = param+"&userId="+userId;
				param = param+"&isPublic="+isPublic;
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			/**
			 * refreshDetail
			 * Method to refresh grid to load datas
			 */
			 function reloadMain(){
				// getFlashObject("mySwf").reloadMain();
			}
			
			/**
			 * unLockTemplateOnUnload
			 * @param templateId
			 * @param templateName
			 * @param userId
			 * @param isPublic
			 * Method to load child screens
			 */
			function unLockTemplateOnUnload(){


				var message = {
					data: 'unLockTemplateOnUnload',
					templateId: templateId,
					templateName: templateName,
					userId: userId,
					isPublic: isPublic,
				};

				window.opener.postMessage(message, "*");

			}
			
			/**
			 * checkTemplateExists
			 * @param templateId
			 * @param userId
			 * Method to load child screens
			 */
			function checkTemplateExists(templateId,userId){
			
    			var param = "/forecastMonitorTemplate.do?method=checkTemplateExists";
				param = param+"&templateId="+templateId;
				param = param+"&userId="+userId;
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			/**
			 * clearSessionInstance
			 * Method to clear session
			 */
			function clearSessionInstance(){
			
    			var param = "/forecastMonitorTemplate.do?method=clearSessionInstance";
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			/**
			  * reloadOption
			  * @param selectedUserId
			  * @param templateId
			  * This function used to refresh grid with copied details
			  */
			function reloadOption(selectedUserId,templateId)
			{
				//getFlashObject("mySwf").reloadCopy(selectedUserId,templateId);
			}
			
			/**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshParent(){
			 	//window.opener.callBack();
				close();
			 }
			 
			 /**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshMonitor(){
			    var url=document_location_href();				
				submitFormFromURL(url,window);	
				close();
			 }
			 /**
			  * closeChild
			  * This function used to close the child window
			  */
			function closeChild(){
			// Start : Modified for 1054 beta 6 1054_STL_038 :To Avoid java script error by Nageswara Rao on 18-05-2012
				if(detailWindow != null)
					detailWindow.close();
				if(copyWindow != null)	
					copyWindow.close();
			// End: Modified for 1054 beta 6 1054_STL_038 :To Avoid java script error by Nageswara Rao on 18-05-2012
			}
			
		</script>
<%@ include file="/angularscripts.jsp"%>
<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"
	onunload="unLockTemplateOnUnload();closeChild()">
<form id="exportDataForm" target="tmp" method="post">

<input type="hidden" name="data" id="exportData" /> <input type="hidden"
	name="screen" id="exportDataScreen"
	value="<fmt:message key="label.forecastMonitorTemplateChange.title.window"/>" />
</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>