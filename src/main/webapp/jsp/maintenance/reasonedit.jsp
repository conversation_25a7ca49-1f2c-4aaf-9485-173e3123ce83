<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.util.LabelValueBean"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title>
	<fmt:message key="startofday.editreason"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.multicolselect.js"></script>
</head>

<SCRIPT language="JAVASCRIPT">
var totalCount = '${totalCount}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterCriteria = "${requestScope.filterCriteria}";
var filterstatus= "${requestScope.filterStatus}";
var currPage = '${requestScope.currentPage}';
var maxPages = '${requestScope.maxPages}';
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
/*Set parent screen hidden values */
//start by vivek
window.opener.document.forms[0].balanceDate.value = '${selectedDate}' ;
window.opener.document.forms[0].currencyCode.value = '${requestScope.currencyCode}';
window.opener.document.forms[0].selectedCurrency.value='${requestScope.currencyCode}';
window.opener.document.forms[0].selectedName.value = '${requestScope.selectedName}' ;
//end by vivek
window.opener.document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
window.opener.document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
window.opener.document.forms[0].filterCriteria.value = "${requestScope.filterCriteria}";
window.opener.document.forms[0].selectedFilterStatus.value = "${requestScope.filterStatus}";
window.opener.document.forms[0].filterFromSerach.value = "true";
window.opener.document.forms[0].entityId.value = "${requestScope.entityId}";
window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';
window.opener.document.forms[0].maxPages.value = '${requestScope.maxPages}';
window.opener.document.forms[0].reasonCode.value='${requestScope.reasonCode}';
window.opener.document.forms[0].userNotes.value='${requestScope.userNotes}';

/*Start code commented by betcy for mantis 997*/
//window.opener.document.forms[0].startBalanceAsString.value='${requestScope.startBalanceAsString}';
/*End code commented by betcy for mantis 997*/
window.opener.document.forms[0].selectedDate.value='${requestScope.selectedDate}';

window.opener.document.forms[0].selectedBalTypeId.value='${requestScope.selectedBalTypeId}';
window.opener.document.forms[0].balanceType.value='${requestScope.balanceType}';




/**
 * <pre>
 * START: Added and Commented by RK on 04-Dec-2009
 * Mantis Issue-1083 : Amend primary key defintion on P_balance. The balance
 * type is not necessary in the index and it presence is causing the index
 * not to be used, or poor range selections to be employed.
 * </pre>
 */
//window.opener.document.forms[0].elements['balmaintenance.id.balanceType'].value='${requestScope.balanceType}';
/** END: Added and Commented by RK on 04-Dec-2009 Mantis Issue-1083 */
//self.close();
window.opener.postMessage(['refreshParentScreen', []], "*");
self.close();
</c:if>

/*cancel operation */
var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
var reasonCodeValue ="${requestScope.reasonCode}"
var reasonName = "${requestScope.reasonName}";
/*save operation */
function submitFormSave(){
   	var userNotes = document.forms[0].elements["balmaintenance.userNotes"].value;

	/*Mantis 1110 : Text area is made to accept 250 characters.Before it was accepting only 50 characters*/
	if (userNotes.length > 250){
   		alert("<fmt:message key='interfacerules.alert.ruleValueLengthValidation'/>");
   		document.forms[0].elements["balmaintenance.userNotes"].focus();
   	}else{
		document.forms[0].method.value = 'saveNotes';
		document.forms[0].selectedDate.value='${requestScope.selectedDate}';
		document.forms[0].entityCode.value='${requestScope.entityId}';
		document.forms[0].selectedBalTypeId.value='${requestScope.selectedBalTypeId}';
		document.forms[0].selectedBalType.value='${requestScope.balanceType}';
		document.forms[0].currentPage.value='${requestScope.currentPage}';
		document.forms[0].currencyCode.value='${requestScope.currencyCode}';
		document.forms[0].filterSortStatus.value='${requestScope.filterSortStatus}';
		document.forms[0].userName.value='${requestScope.userName}';
		document.forms[0].selectedFilter.value='${requestScope.selectedFilter}';
		document.forms[0].selectedSort.value='${requestScope.selectedSort}';
		document.forms[0].maxPages.value='${requestScope.maxPages}';
		document.forms[0].reasonCode.value=document.getElementById("mltsel").value;//document.forms[0].elements["balmaintenance.reasonCode"].value;
		document.forms[0].userNotes.value=document.forms[0].elements["balmaintenance.userNotes"].value.trim();
		/*Start code modified by betcy for mantis 997*/
		document.forms[0].sodBalanceAsString.value='${requestScope.sodBalanceAsString}';
		/*End code modified by betcy for mantis 997*/
		document.forms[0].submit();
	}

}


/* validate form fields */

var headers= ["ID", "Description"];

var dataprovider = new Array();

function loadCodesList()
{
	<%
		ArrayList<LabelValueBean> codeList = (ArrayList<LabelValueBean>)request.getAttribute("reasondetails");
		Iterator it = codeList.iterator();
		while (it.hasNext())
		{
			LabelValueBean lvb = (LabelValueBean) it.next();
	%>
		var newElement = {};
		newElement[headers[0]] = "<%=lvb.getValue()%>";
		newElement[headers[1]] = "<%=lvb.getLabel()%>";
		dataprovider.push(newElement);
	<%
		}

	%>


}


function bodyOnLoad()
{
	/*to set selected reason */
// 	var reasonCode_Length=document.forms[0].elements["balmaintenance.reasonCode"].length;
// 	var rightvalue='${requestScope.reasonCode}';
	// Start: code added by Bala for Mantis 1406 Currency Group Access on 05-Apr-2011
	var access='${requestScope.access}';
	if (access != "0"){
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
	} else{
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
	}
	// End: code added by Bala for Mantis 1406 Currency Group Access on 05-Apr-2011



	loadCodesList();

	$("#dropDownReasonCode").multicolselect({
		headerData: headers,
		dataprovider: dataprovider,
		name: 'errorLog.dropDownReasonCode',
		selectedIndex: 0,
		descriptionElement: $('#reasonDesc')
     });


	if(reasonName !="" && reasonName !="null")
		document.getElementById("reasonDesc").innerHTML =reasonName;


	if(reasonCodeValue !="" && reasonCodeValue !="null")
		document.getElementById("mltsel").value =reasonCodeValue;



}
function populateDropBoxes()
{
/*populate  reasoncode and description  */
// var dropBox1 = new SwSelectBox(document.forms[0].elements["balmaintenance.reasonCode"],document.getElementById("reasonDesc"));
}
// Start: Modified code to remove the scrollbars and to limit the characters for Mantis 1285 by aliveni on 22-OCT-2010
function limitTextArea(element,e)
{
	var event = (window.event|| e);
	var maxlength = 250;
	if(element.value.length > maxlength-1 ){
		if (window.event)
			window.event.returnValue = false;
		else
			event.preventDefault();
	}

}


var bookSelectElement = null;
var bookSwSel = null;
/**
  * getBookCodeList
 *
 * This method is used to get the collection of book code list.
 */
function getReasonCodeList(event) {
	if(bookCodeclk) {
	//get the all elements for selectbox
	var divElement = document.getElementById("dropdowndiv_3");
	var idElement = document.forms[0].elements["reasonCodeId"];
	var descElement = document.forms[0].elements["reasonDesc"];
	var arrowElement = document.forms[0].elements["dropdownbutton_3"];
	bookSelectElement = document.forms[0].elements["balmaintenance.reasonCode"];
	var idLength = 12;
	var descLength = 30;
	var appName = "<%=SwtUtil.appName%>"; //Application name is picked up from property file
	//get the requestURL
	var requestURL = new String('<%=request.getRequestURL()%>');
	var idy = requestURL.indexOf('/'+appName+'/');
	//frame the requestURL
	requestURL=requestURL.substring(0,idy+1) ;
	requestURL = requestURL + appName+"/balMaintenance.do?method=getReasonCodeList";
	requestURL = requestURL + "&entityCode=" + "${requestScope.entityId}";
	var oXMLHTTP = new XMLHttpRequest();
	oXMLHTTP.open( "POST", requestURL, false );
	//send the request for list
	oXMLHTTP.send();
	//get the response text
	var listValue=new String(oXMLHTTP.responseText);
	listValue = listValue.split('\n');
	if(bookSelectElement.options.length > 0){
		bookSelectElement.remove(0);
	}
	var index = 0;
	//add the book code into option
	for(var i=0;i < listValue.length -1;i++){
		var lblValue = listValue[i].split('~~~');
		var opt = document.createElement("option");
		opt.text = lblValue[0];
		opt.value = lblValue[1];
		if(opt.value == reasonCodeValue){
			index = i;
		}
		bookSelectElement.options.add(opt);
	}

	//set the selected index
	bookSelectElement.selectedIndex = index;
	//frame the selectbox component
	bookSwSel = new SwMainSelectBox(divElement,bookSelectElement,idElement,descElement,arrowElement,idLength,descLength);
	bookSwSel.setClickFlag();
	//call to populate the list in the list box
	bookSwSel.arrowElementOnClick(event);
	bookCodeclk = false;
}
}
var bookCodeclk= true;

//End: Modified code to remove the scrollbars and to limit the characters for Mantis 1285 by aliveni on 22-OCT-2010
</SCRIPT>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"onunload="call()">

<form action="balMaintenance.do" onsubmit="return validate(this);">
<input name="selectedDate" type="hidden" value="">
<input name="method" type="hidden" value="saveNotes">
<input name="entityCode" type="hidden" value="">
<input name="selectedBalTypeId" type="hidden" value="">
<input name="selectedBalType" type="hidden" value="">
<input name="reasonCode" type="hidden" value="">
<input name="userNotes" type="hidden" value="">
<input name="currentPage" type="hidden" value="">
<input name="currencyCode" type="hidden" value="">
<input name="filterSortStatus" type="hidden" value="">
<input name="selectedFilter" type="hidden" value="">
<input name="selectedSort" type="hidden" value="">
<input name="maxPages" type="hidden" value="">
<input name="userName" type="hidden" value="GBP">

<input name="identifier" type="hidden" value="">
<input name="entityId" type="hidden" value="">
<input name="name" type="hidden" value="">
<!--Start code modified by betcy for mantis 997-->
<input name="sodBalanceAsString" type="hidden" value="">
<!--End code modified by betcy for mantis 997-->
<input name="balanceSource" type="hidden" value="">
<input name="user" type="hidden" value="">
<input name="balanceType" type="hidden" value="">
<input name="balanceDate" type="hidden" value="">
<input name="replacebalanceDate" type="hidden" value="">
<input name="currency" type="hidden" value="">

<div id="dropdowndiv_3"
		style="position: absolute; width: 265px; left: 40px; top: 10px; visibility: hidden; z-index: 100"
		class="swdropdown"><select styleClass="htmlTextFixed"
		name="balmaintenance.reasonCode" size="7"
		style="width:600px;z-index:99">
	</select></div>

<div id="ReasonEdit" style="position:absolute; left:20px; top:20px; width:920px; height:115px; border:2px outset;" color="#7E97AF">
<div id="ReasonEdit" style="position:absolute;left:8px; top:4px; width:620px; height:100px;">

<table width="620px" border="0" cellpadding="0" cellspacing="1" height="23">

		<tr height="24">
			  <td width="112px" align="left"><b><fmt:message key="balmaintenance.reasonCode"/></b></td>
			  <td width="155px">
			  <div id="dropDownReasonCode"/></div>
			  </td>
			   <td width="30px">&nbsp;</td>
			  <td width="400px" style="z-index:10" align="left">
				<span id="reasonDesc" name="reasonDesc" style="background:transparent; border:thin;width:400px;"/>
			   </td>
		</tr>

	  </table>

	 <table width="800px" border="0" cellpadding="0" cellspacing="1" height="23">
	     <tr height="24">
		  	<td width="107px"  align="left"><b><fmt:message key="balmaintenance.userNotes"/></b></td>
		 	<td width="8">&nbsp;</td>
		 	<td colspan="2">
		 	<table>
		 	<!-- Start: Modified code to remove the scrollbars and to limit the characters for Mantis 1285 by aliveni on 22-OCT-2010 -->
		  	<td title='<fmt:message key="tooltip.enterNotes"/>'>
		      <textarea tabindex="2"  name="balmaintenance.userNotes" class="htmlTextAlpha" style="width:697;height:51;overflow:auto;" rows="3" onkeypress="limitTextArea(this,event);">${balmaintenance.userNotes}</textarea>
		     </td>
		     <!-- Start: Modified code to remove the scrollbars and to limit the characters for Mantis 1285 by aliveni on 22-OCT-2010 -->
		     </table>
	    </tr>
	</table>


</div>
</div>

<div id="ReasonEditor" style="position:absolute ;left:865; top:150px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			   <a tabindex="9" href=# onclick="javascript:openWindow(buildPrintURL('print','Edit Reason'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a>
		    </td>

			<td align="right" id="Print">
				<a tabindex="9" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:141; width:920px; height:39px; visibility:visible;">
<div id="SectionMaintenance" style="position:absolute; left:6; top:4; width:875px; height:15px; visibility:visible;">
<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<!-- Start: code modified by Bala for Mantis 1406 Currency Group Access on 05-Apr-2011 -->
		<td id="savebutton" width="70"></td>
		<td id ="cancelbutton" width="70"><a tabindex="8" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a></td>
	</tr>
	</table>

	<div style="display:none;visibility:hidden;">
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20";style="visibility:hidden;">
				<td id ="saveenablebutton" width="70"><a tabindex="7" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitFormSave();"><fmt:message key="button.save"/></a></td>
				<td id="savedisablebutton"><a class="disabled"
					disabled="disabled" title='<fmt:message key="tooltip.SaveChanges"/>'><fmt:message key="button.save" /></a></td>
		</table>

			</div>
		<!-- End: code modified by Bala for Mantis 1406 Currency Group Access on 05-Apr-2011 -->

</div>
</div>
	  <blockquote>&nbsp;</blockquote>
            <p>&nbsp;</p>
</form>
</body>
</html>
 