<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:choose>
    <c:when test="${requestScope.methodName == 'add'}">

	<fmt:message key="metagrpmaintenance.addScreen"/>

    </c:when>
    <c:otherwise>
        <c:if test="${requestScope.methodName == 'change'}">

		<fmt:message key="metagrpmaintenance.changeScreen"/>

        </c:if>
    </c:otherwise>
</c:choose>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="displayListByEntity";
window.opener.document.forms[0].submit();
self.close();

</c:if>

mandatoryFieldsArray=["*"];
var entityAccess = "${requestScope.EntityAccess}";

var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="cancelbutton";

function populateDropBoxes()
{

	ShowErrMsgWindow('${actionError}');
}

/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/

function submitForm(methodName)

{

	var metgroupId=validateField(document.forms[0].elements['metaGroup.id.mgroupId'],'metaGroup.id.mgroupId1add','alphaNumPat');

	if(metgroupId)
	{

	    var metagroupName=validateField(document.forms[0].elements['metaGroup.mgroupName'],'metaGroup.mgroupName','alphaNumPatExtended');
	    if(metagroupName)
		{
	if(validateForm(document.forms[0]) ){
	enableFields();
   elementTrim(document.forms[0]);
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	}
	}
	else
	{
    alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	  document.forms[0].elements['metaGroup.mgroupName'].focus();

	}
	}
	else
	{
	 document.forms[0].elements['metaGroup.id.mgroupId'].focus();
	}
}
/* This method is user to validate for symbolic characters && throws alert message. */
function symbolicCharactersValidation(strField, strLabel,strPat) {
	var validText = false;
	if(validateField(strField,strLabel,strPat)){
		validText = true;
		}
	if(validText == false){
	alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	}
}
/* This Method is used for save || update the data && close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event || e);
	 if(event.keyCode == 13){
	 if(method == "update"){
		submitForm('update');
		}else if(method == "save"){
		submitForm('save');
		}else if(method == "C"){
		confirmClose('C');
		}
	}
}

function enableFields(){
	document.forms[0].elements["metaGroup.id.entityId"].disabled = "";
	document.forms[0].elements["metaGroup.id.mgroupId"].disabled = "";
	document.forms[0].elements["metaGroup.id.entityId"].disabled = "";
	document.forms[0].elements["metaGroup.mgrpLvlCode"].disabled = "";
}
function validateForm(objForm){
  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["metaGroup.id.mgroupId"];
  elementsRef[1] = objForm.elements["metaGroup.mgroupName"];
  elementsRef[2] = objForm.elements["metaGroup.mgrpLvlCode"];
  return validate(elementsRef);
}

function bodyOnLoad()
{
	 document.getElementById("entityName").innerText = '${entityName}';
	 document.getElementById("mgrpLvlName").innerText = '${mgrpLvlName}';

}

function checkIdWithSpecialCharacters(element)
{
	if( isCancelorCloseButtonPressed() == false)
	 {
		var charsAllowed="~#!@$%^&*()-_=+[]:;'\",<.>/?";
		charsAllowed +="0123456789";
		charsAllowed +="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		var charsAllowedString = new String(charsAllowed);
		var idField = new Array(charsAllowedString.length);

		var inputId =  new String(element.value);
		var inputIdArray = new Array(inputId.length);

		var idNotValid = "false";

		for(var i=0; i<charsAllowedString.length ; i++)
		{
			idField[i] = charsAllowedString.charAt(i);
		}

		for(i = 0; i<inputId.length;i++)
		{
			inputIdArray[i] = inputId.charAt(i);
		}

		for(i = 0; i<inputId.length ; i++)
		{
			for(var j=0; j<charsAllowedString.length ;j++)
				{
					if(inputIdArray[i] == idField[j])
						{
							idNotValid = "false";
							break;
						 }

						idNotValid = "true";
				}
				if(idNotValid == "true")
				{
					alert("<fmt:message key="metagroupmaintenance.alert.validString"/>");
					window.event.returnValue = false;
					return false;

				}
		}
	  return true;
	}
	return true;
 }


</Script>
</head>

<form action="metaGroup.do" method="post" onsubmit="validate(this);">
<input name="method" type="hidden" value="save">
<input name="oldValue" type="hidden" value= "${oldValue}">
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();populateDropBoxes();" onunload="call()">

<div id="MetaGroupAdd" style="position:absolute; left:20px; top:20px; width:587px; height:138px; border:2px outset;" color="#7E97AF">

<div id="MetaGroupAdd" style="position:absolute;left:8px; top:6px; width:587px; height:120px;">

	<table width="575" border="0" cellpadding="0" cellspacing="0" height="100">
	<!--start:Mantis 1549 modified by sunil.The purpose to display meaningful tooltip -->
       <tr height="24" >
          <td  width="127px"><b><fmt:message key="entity.id"/></b></td>
  	  <td width="28px">&nbsp;</td>
          <td width="420px" >
		<input type="text"  name="metaGroup.id.entityId" value="${metaGroup.id.entityId}"  class="htmlTextAlpha"  titleKey="tooltip.entityId"
		onchange="submitForm('displayMGrpListByEntity')"
		disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}"  style="width:120px;" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<span id="entityName" name="entityName" class="spantext" >
          </td>
        </tr>

     <tr height="24" >
	    <c:if test="${requestScope.methodName == 'add'}">

          <td  width="127px"><b><fmt:message key="metaGroup.id.mgroupId1add"/></b>*</td>
		  <td width="28px">&nbsp;</td>
          <td  width="125px">

			 		<input type="text" titleKey="tooltip.enterMGID" class="htmlTextAlpha" tabindex="1" name="metaGroup.id.mgroupId" value="${metaGroup.id.mgroupId}"   maxlength="12" style="width:120px;" onchange="return validateField(this,'metaGroup.id.mgroupId1add','alphaNumPat');" />
					</td>

</c:if>
			   <c:if test="${requestScope.methodName == 'change'}">

			     <td  width="127px"><b><fmt:message key="metaGroup.id.mgroupId1add"/></b></td>
		  <td width="28px">&nbsp;</td>
          <td  width="125px">
			 		<input type="text" titleKey="tooltip.enterMGID" class="htmlTextAlpha"  tabindex="1" name="metaGroup.id.mgroupId" value="${metaGroup.id.mgroupId}"
			 		disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}" style="width:120px;" />

</c:if>
          </td>

        </tr>

       <tr height="24" >
          <td width="127px"><B><fmt:message key="metaGroup.mgroupName1add"/></B>*</td>
		  <td width="28px">&nbsp;</td>
		  <td  width="280px">

           <input type="text" titleKey="tooltip.enterMGName" tabindex="2" onchange="return symbolicCharactersValidation(this,'metaGroup.mgroupName1add','alphaNumPatExtended');"  class="htmlTextAlpha" name="metaGroup.mgroupName" value="${metaGroup.mgroupName}"  style="width:280px;" maxlength="30" /></td>

        </tr>

		<tr height="24" >
		<td width="127px"><B><fmt:message key="metaGroup.mgrpLvlID1"/></B>*</td>
		<td width="28px">&nbsp;</td>
		<td width="420px">
			<input type="text" titleKey="tooltip.mgrpLvlID1" name="metaGroup.mgrpLvlCode" value="${metaGroup.mgrpLvlCode}"   class="htmlTextAlpha"
			disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}" style="width:25px;" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span id="mgrpLvlName" name="mgrpLvlName" class="spantext" >
		</td>
       </tr>
<!--End:Mantis 1549 modified by sunil.The purpose to display meaningful tooltip -->
	 <tr height="24" >
       <td  width="127px"><B><fmt:message key="metaGroup.category"/></B></td>
	   <td width="28px">&nbsp;</td>
	   <td width="240px">

			   <input type="radio" name="metaGroup.financeTrade" ${metaGroup.financeTrade == 'F' ? 'checked="checked"' : ''} value="${'F'}" id="1" titleKey="tooltip.selectMGCategory"  style="width:13;"  tabindex="4" />
			     <label for ="1" titleKey="tooltip.selectMGCategory">
				   <fmt:message key="metaGroup.finance"/>&nbsp;
				</label>
			 	   &nbsp;&nbsp;&nbsp;
			   <input type="radio" name="metaGroup.financeTrade" ${metaGroup.financeTrade == 'T' ? 'checked="checked"' : ''} value="${'T'}" id="2" titleKey="tooltip.selectMGCategory"  style="width:13;"  tabindex="5" />
			   <label for ="2" titleKey="tooltip.selectMGCategory">
			   <fmt:message key="metaGroup.trade"/></B>&nbsp;
			   </label>
	    </td>
		</tr>
	</table>
   </div>

</div>

<div id="MetaGroup" style="position:absolute; left:533; top:174; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

			<td align="Right">
			   <a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Metagroup Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		    </td>

			<td align="right" id="Print">
				<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:165; width:587px; height:39px; visibility:visible;">
<div id="MetaGroupAdd" style="position:absolute; left:6; top:4; width:420px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="2" cellpadding="0">
    <tr>

       <c:if test="${requestScope.methodName == 'add'}">

		  <td width="70"><a tabindex="6" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('save');" onKeydown = "onKeyEnterSubmit('save',event);"><fmt:message key="button.save"/></a></td>

</c:if>
		<c:if test="${requestScope.methodName == 'change'}">

		  <td width= "70"><a tabindex="6" title='<fmt:message key="tooltip.saveMG"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('update');" onKeydown = "onKeyEnterSubmit('update',event);"><fmt:message key="button.save"/></a></td>

</c:if>


	  <td  id="cancelbutton" ><a tabindex="7" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');" onKeydown = "onKeyEnterSubmit('C',event);"><fmt:message key="button.cancel"/></a></td>

	</tr>
  </table>
</div>
</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>