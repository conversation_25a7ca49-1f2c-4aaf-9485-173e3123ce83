<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.InterfaceRulesPageDetails"%>
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<!--Start:code modified by venkat for issues found on V1051 beta testing- interface rules issues.-->
<%@page import="org.swallow.util.SwtUtil"%>
<%@page import="org.swallow.maintenance.model.InterfaceRule"%>
<!--End:code modified by venkat for issues found on V1051 beta testing- interface rules issues.-->
<html>

<head>
    <title>
        <fmt:message key="interfacerulesmaintenance.title.mainWindow"/>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <script type="text/javascript" src="js/jquery.exportselect.js"></script>
    <SCRIPT language="JAVASCRIPT">
        var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";
var dateFormat = '${sessionScope.CDM.dateFormat}';

var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterValues=new Array();
/* Start: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
/* get delimiter symbol from SwtConstants */
var currentFilterValues = currentFilter.split("<%=SwtConstants.DELIMITER_SYMBOL%>");
var sortingValues = currentSort.split("<%=SwtConstants.DELIMITER_SYMBOL%>");
/* End: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
var sortedValues = new Array();
var totalCount = '${totalCount}';
var dateSelected = false;
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function bodyOnLoad(){
	xl = new XLSheet("interfaceRulesList","table_2", ["String", "String", "String", "String",],"1122","false",currentFilterValues,sortedValues);
	xl.onsort = xl.onfilter = updateColors;
	xl.dataTable.tBody.style.cursor="";
	highlightTableRows("interfaceRulesList");

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		if(menuEntityCurrGrpAccess=='0')
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>

	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>

	setMessageType();

	var headerData = [];
	var dataprovider = new Array();
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);

	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);

	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);

	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });

	if(record()== 0)
		 $("#exportReport").disabled(true);
	else
		 $("#exportReport").disabled(false);




	}

function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return buildExportInterfaceRulesURL('exportInterfaceRules',type.toLowerCase());
}


function getRadioButtonValue(button){
	var d = button;
	var len = d.length;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		{
			return d[i].value;
		}
	}
	if(i == len)
		return "null";
}
function setMessageType()
{
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);

	if(searchMessageType == "P")
	{
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
	}
	else if(searchMessageType == "M")
	{
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
	}
}

function submitForm (methodName) {
	if(methodName == "getList"){
		document.forms[0].selectedFilter.value ="all" ;
	}
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var messageType = document.forms[0].elements['interfaceRule.id.messageType'].value;
	var otherMessageType = document.forms[0].elements['interfaceRule.otherMessageType'].value;
	var ruleId = document.forms[0].elements['interfaceRule.id.ruleId'].value;

	document.forms[0].searchMessageType.value = searchMessageType;

	document.forms[0].messageType.value = messageType;
	document.forms[0].otherMessageType.value = otherMessageType;
	document.forms[0].ruleId.value = ruleId;
	var partialRuleId = document.forms[0].elements['interfaceRule.partialRuleId'].checked;
	if(partialRuleId == true)
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'Y';
		document.forms[0].partialRuleId.value = 'Y';
	}
	else
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'N';
		document.forms[0].partialRuleId.value = 'N';
	}
	document.forms[0].method.value = methodName;

	document.forms[0].submit();
}

function optionClick_server_filter_JSP(index,value,action)
{

if(action == "filter")
{
	value = replace(value,'&nbsp;',' ');
	var filterValue ="";

	if(currentFilter =="all" || currentFilter=="undefined"){
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
			if(idx == index)
			{
				for (var row=0 ;row<document.getElementById('interfaceRulesList').rows.length;row++)
				{
				/* Start:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
				if(value!="All" && value!= "(Not empty)" && value!= "(Empty)")
				{
					var valueOne=document.getElementById('interfaceRulesList').rows[row].cells[parseInt(index)].innerText;
				/* special characters replaced. */
					valueOne = replace(valueOne,'&','&amp;');
					valueOne = replace(valueOne,'>','&gt;');
					valueOne = replace(valueOne,'<','&lt;');
					valueOne = valueOne.trim()
					value = value.trim();
				/* End:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
					if (value==valueOne && document.getElementById('interfaceRulesList').rows[row].cells.length>2)
					{
						valueTwo=document.getElementById('interfaceRulesList').rows[row].cells[parseInt(index)].title;
						valueTwo=replace(valueTwo,'\n','');
						if (valueTwo!="")
							value = valueTwo;
						break;
						}
					}
				}
				/* Start: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
				filterValue +=  value + "<%=SwtConstants.DELIMITER_SYMBOL%>";
				}
			else
				filterValue +=  "All" + "<%=SwtConstants.DELIMITER_SYMBOL%>";
				/* End: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
		}
	}else{
			for (var row=0 ;row<document.getElementById('interfaceRulesList').rows.length;row++)
				{
					if(value!="All" && document.getElementById('interfaceRulesList').rows[row].cells.length>2)
					{
						var valueOne=document.getElementById('interfaceRulesList').rows[row].cells[parseInt(index)].innerText;
						valueOne = replace(valueOne,'>','&gt;');
						valueOne = valueOne.trim();
						value = value.trim();
						if (value== valueOne)
						{
							valueTwo=document.getElementById('interfaceRulesList').rows[row].cells[parseInt(index)].title;
							valueTwo=replace(valueTwo,'\n','');
							if (valueTwo!="")
								value = valueTwo;
							break;
						}
					}
				}

		var filter=currentFilter.split("<%=SwtConstants.DELIMITER_SYMBOL%>");
		filter[index]=value;

		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
		/* Start: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
		/* get delimiter symbol from SwtConstants */
			filterValue+=filter[idx] + "<%=SwtConstants.DELIMITER_SYMBOL%>";
		/* End: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
		}
	}
		document.forms[0].selectedSort.value=currentSort;
		document.forms[0].selectedFilter.value =filterValue ;
	}else{
		var sortColum=index;
		var sortDesc=value;
		/* Start: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
		document.forms[0].selectedSort.value=sortColum + "<%=SwtConstants.DELIMITER_SYMBOL%>" +sortDesc;
		/* End: Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." */
		document.forms[0].selectedFilter.value =currentFilter ;

		 }
	document.getElementById('ddscrolltable').innerHTML='';
	document.forms[0].method.value='getList';
	document.forms[0].submit();
  }

function validatePageNumber(strObject)
{
    var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0))
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		goToResultsPage(strObject.value);
	}
	else
	{
		alert("<fmt:message key="alert.enterValidPageNumber"/>");
		strObject.value = currPage;
	}
}

function changeInterfaceRule (methodName) {
//Start:code modified by venkat for issues found on V1051 beta testing- interface rules issues.
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var ruleId = document.forms[0].elements["interfaceRule.id.ruleId"].value;
	var messageType = "";
	if(searchMessageType == "P")
	{
		messageType = document.forms[0].elements["interfaceRule.id.messageType"].value;
	}
	else if (searchMessageType == "M")
	{
		messageType = document.forms[0].elements["interfaceRule.otherMessageType"].value;
	}
	var param = 'interfacerules.do?method='+methodName;
	param += '&selectedSearchType='+searchMessageType;
	param += '&selectedMessageType='+escape(encodeURIComponent(messageType));
	param += '&selectedRuleId='+escape(encodeURIComponent(ruleId));
	param += '&selectedRowMessageType='+escape(encodeURIComponent(document.getElementById('selectedRowMessageType').value));
	param += '&selectedRowRuleId='+escape(encodeURIComponent(document.getElementById('selectedRowRuleId').value));
	param += '&selectedRowRuleKey='+escape(encodeURIComponent(document.getElementById('selectedRowRuleKey').value));
	param +='&currentPage='+currPage;
	param +='&selectedSort='+document.forms[0].selectedSort.value;
/*  Start: code modified by venkat on 15-mar-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen" */
/*  hash symbol has specific meaning in a URL */
	param +='&selectedFilter='+escape(encodeURIComponent(document.forms[0].selectedFilter.value));
/* 	End: code modified by venkat on 15-mar-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen" */
	param +='&maxPage='+maxPage;
	return  param;
//End:code modified by venkat for issues found on V1051 beta testing- interface rules issues.
}

function buildAddInterfaceRule (methodName) {
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var ruleId = document.forms[0].elements["interfaceRule.id.ruleId"].value;
	var messageType = "";
	if(searchMessageType == "P")
	{
		messageType = document.forms[0].elements["interfaceRule.id.messageType"].value;
	}
	else if (searchMessageType == "M")
	{
		messageType = document.forms[0].elements["interfaceRule.otherMessageType"].value;
	}
	var param = 'interfacerules.do?method='+methodName;

	param += '&selectedSearchType='+searchMessageType;
	param += '&selectedMessageType='+escape(encodeURIComponent(messageType));
	param += '&selectedRuleId='+escape(encodeURIComponent(ruleId));
	param +='&currentPage='+currPage;
	param +='&selectedSort='+document.forms[0].selectedSort.value;
/*  Start: code modified by venkat on 15-mar-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen" */
/*  hash symbol has specific meaning in a URL */
	param +='&selectedFilter='+escape(encodeURIComponent(document.forms[0].selectedFilter.value));
/* 	End: code modified by venkat on 15-mar-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen" */
	param +='&maxPage='+maxPage;
	return  param;
}
function goToResultsPage(goToPageNo)
{
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var messageType = document.forms[0].elements['interfaceRule.id.messageType'].value;
	var otherMessageType = document.forms[0].elements['interfaceRule.otherMessageType'].value;
	var ruleId = document.forms[0].elements['interfaceRule.id.ruleId'].value;;
	document.forms[0].searchMessageType.value = searchMessageType;
	document.forms[0].messageType.value = messageType;
	document.forms[0].otherMessageType.value = otherMessageType;
	document.forms[0].ruleId.value = ruleId;
	var partialRuleId = document.forms[0].elements['interfaceRule.partialRuleId'].checked;
	if(partialRuleId == true)
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'Y';
		document.forms[0].partialRuleId.value = 'Y';
	}
	else
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'N';
		document.forms[0].partialRuleId.value = 'N';
	}
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].maxPages.value=maxPage;
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].goToPageClicked.value = 'Y';
	document.forms[0].submit();
}
/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
function clickLink(goToPageNo)
{
	var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((InterfaceRulesPageDetails)((ArrayList<InterfaceRulesPageDetails>)request.getAttribute("pageSummaryList")).get(0), "pageDetails") %>';
	var url='interfacerules.do?'+baseUrl;
	url +='&method=next';
	var searchMessageType =getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var partialRuleId = document.forms[0].elements["interfaceRule.partialRuleId"].checked;
	var ruleId = document.forms[0].elements['interfaceRule.id.ruleId'].value;
	var messageType = document.forms[0].elements['interfaceRule.id.messageType'].value;
	var otherMessageType = document.forms[0].elements['interfaceRule.otherMessageType'].value;
	url +='&searchMessageType='+searchMessageType+'&messageType='+messageType;
	if(partialRuleId == true)
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'Y';
		url +='&partialRuleId=Y';
	}
	else
	{
		document.forms[0].elements['interfaceRule.partialRuleId'].value = 'N';
		url +='&partialRuleId=N';
	}
	url +='&ruleId='+escape(encodeURIComponent(ruleId))+'&goToPageNo='+goToPageNo+'&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&otherMessageType='+escape(encodeURIComponent(otherMessageType));
// Start:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen."
// maintain menuAccessId for pagination
	url +='&menuAccessId='+menuEntityCurrGrpAccess;
// End:Code added by Venkat on 16-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen."
   submitFormFromURL(url,window);
}

function buildExportInterfaceRulesURL(methodName,exportType){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	var messageType = "";
	if(searchMessageType == "P")
	{
		messageType = document.forms[0].elements["interfaceRule.id.messageType"].value;
	}
	else if (searchMessageType == "M")
	{
		messageType = document.forms[0].elements["interfaceRule.otherMessageType"].value;
	}
	var ruleId = document.forms[0].elements["interfaceRule.id.ruleId"].value;
	document.forms[0].selectedRuleId.value = ruleId;
	document.forms[0].selectedMessageType.value = messageType;
	document.forms[0].method.value = methodName;
	document.forms[0].exportType.value = exportType.trim();
	document.forms[0].screen.value = '<fmt:message key="interfacerulesmaintenance.title.mainWindow"/>';
	if(record()> 0)
	{
	document.forms[0].submit();
	}
}

function onSelectTableRow(rowElement, isSelected) {
	var hiddenElements = rowElement.getElementsByTagName("input");
	document.forms[0].selectedRowMessageType.value = hiddenElements[0].value;
	document.forms[0].selectedRowRuleId.value = hiddenElements[1].value;
	document.forms[0].selectedRowRuleKey.value = hiddenElements[2].value;

	if (menuEntityCurrGrpAccess == "0" && isSelected) {
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}

	if (!isSelected) {
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
}

function showOtherMessageType(){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
		document.forms[0].elements["interfaceRule.otherMessageType"].value = "";
		// Added by KaisBS for mantis 1568 (Interface Rules: Data's of previously selected message type are visible in grid despite the message type being changed)
		submitForm('getList');
	}
	else if(searchMessageType == "M")
	{
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
		document.forms[0].elements["interfaceRule.id.messageType"].value = "";
	}
}
function submitDeleteForm (methodName) {
	document.getElementById('method').value = methodName;
	document.forms[0].selectedMessageType.value = document.forms[0].selectedRowMessageType.value
	document.forms[0].selectedRuleId.value = document.forms[0].selectedRowRuleId.value;//document.forms[0].elements["interfaceRule.id.ruleId"].value;
	document.forms[0].selectedRuleKey.value = document.forms[0].selectedRowRuleKey.value;//document.forms[0].elements["interfaceRule.id.ruleKey"].value;
	document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
	document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	document.forms[0].maxPages.value = '${requestScope.maxPage}';
	document.forms[0].maxPage.value = maxPage;
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true) {
		document.forms[0].submit ();
	}
}
//Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen."
// Form submit when enter key event
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "delete"){
		submitDeleteForm('delete')
		}else if(method == "change"){
	javascript:openWindow(changeInterfaceRule('change'),'accountsmaintenancechangewindow','left=50,top=150,width=855,height=420,toolbar=0, resizable=yes, status=yes, scrollbars=no','true');
		}else if(method == "close"){
		confirmClose('P');
		}else if(method == "add"){
	javascript:openWindow(buildAddInterfaceRule('add'),'accountsmaintenancechangewindow','left=50,top=150,width=855,height=420,toolbar=0, resizable=yes, status=yes, scrollbars=no','true');
		}else if(method == "search"){
		submitForm('getList')
		}
	}
}
//End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen."
</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]);" onunload="call();">
    <form action="interfacerules.do" method="post" method="post" onsubmit="return validate(this);">
        <input name="method" type="hidden" value="getList">
        <input name="selectedFilter" type="hidden" value='${selectedFilter}'>
        <input name="selectedSort" type="hidden" value='${selectedSort}'>
        <input name="currentPage" type="hidden" value="">
        <input name="totalCount" type="hidden" value="">
        <input name="messageType" type="hidden" value="">
        <input name="ruleId" type="hidden" value="">
        <input name="exportType" type="hidden" value="" />
        <input name="screen" type="hidden" value="" />
        <input name="selectedRowMessageType" type="hidden" value="" />
        <input name="selectedRowRuleId" type="hidden" value="" />
        <input name="selectedRowRuleKey" type="hidden" value="" />
        <input name="selectedRuleKey" type="hidden" value="" />
        <input name="goToPageClicked" type="hidden" value="" />
        <input name="searchMessageType" type="hidden" value="">
        <!-- START:Code added by Venkat on 16-Feb-2011 for mantis 1365:"maintain menuAccessId when page get refresh." -->
        <input name="menuAccessId" type="hidden" value='${menuEntityCurrGrpAccess}'>
        <!-- End:Code added by Venkat on 16-Feb-2011 for mantis 1365:"maintain menuAccessId when page get refresh." -->
        <input name="goToPageNo" type="hidden" value="">
        <input name="selectedMessageType" type="hidden" value="">
        <input name="selectedRuleId" type="hidden" value="">
        <input name="selectedPartialRuleId" type="hidden" value="">
        <input name="selectedSearchType" type="hidden" value="">
        <input name="maxPage" type="hidden" value="">
        <input name="maxPages" type="hidden" value="">
        <input name="otherMessageType" type="hidden" value="">
        <input name="partialRuleId" type="hidden" value="">
        <div id="InterfaceRuleMaintenance" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:1180px; height:75px;">
            <!-- Start: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
            <div id="InterfaceRuleMaintenance" style="position:absolute; left:1px; top:1px; width:1179px; height:112px; visibility:visible;">
                <table width="1174px" border="0px" cellpadding="0" cellspacing="0" class="content">
                    <tr height="20px">
                        <td width="95px" style=" padding-left: 10px;" valign="middle"><b style="font-size: 9pt">
                                <fmt:message key="interfacerulesmaintenance.messageType"/></b></td>
                        <td>
                            <table align="left">
                                <tr>
                                    <td width="1px">&nbsp;</td>
                                    <td style="padding-top: 7px" width="100px">
                                        <input type="radio" id="1" name="interfaceRule.searchMessageType" value="P"
                                               style="width:13px;"
                                               onclick="showOtherMessageType();"
                                               title="<fmt:message key='tooltip.Predefined'/>"
                                               <c:if test="${interfaceRule.searchMessageType == 'P'}">checked="checked"</c:if> />
                                        <label for="1" style="font-size: 9pt">
                                            <fmt:message key="interfacerulesmaintenance.predefinedMessageType"/>
                                        </label>
                                    </td>
                                    <td width="15px">&nbsp;</td>
                                    <td style="padding-top: 7px" width="100px">
                                        <input type="radio" id="2" name="interfaceRule.searchMessageType" value="M"
                                               style="width:13px;"
                                               onclick="showOtherMessageType();"
                                               title="<fmt:message key='tooltip.Manual'/>"
                                               <c:if test="${interfaceRule.searchMessageType == 'M'}">checked="checked"</c:if> />
                                        <label for="2" style="font-size: 9pt">
                                            <fmt:message key="interfacerulesmaintenance.manualMessageType"/>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                            <table align="left">
                                <tr>
                                    <td style="padding-top: 8 ">
                                        <div id="predefinedMsgTypeDiv" style="height: 20; top: 60">
                                            <select
                                                class="htmlTextAlpha"
                                                id="interfaceRule.id.messageType"
                                                name="interfaceRule.id.messageType"
                                                style="width:160px"
                                                title="<fmt:message key='tooltip.selectMessageType'/>"
                                                onchange="submitForm('getList')"
                                                tabindex="3">
                                                <c:forEach items="${requestScope.messagetypes}" var="messageType">
                                                    <option
                                                        value="${messageType.value}"
                                                        <c:if test="${interfaceRule.id.messageType == messageType.value}">selected="selected"</c:if>>
                                                        ${messageType.label}
                                                    </option>
                                                </c:forEach>
                                            </select>

                                            <!-- <input
						disabled="disabled" name="messageType" class="textAlpha"
						style="width: 118px;"
						title='<fmt:message key="tooltip.selectMessageType"/>'>
					<input id="dropdowndiv_1" tabindex="4"
						title='<fmt:message key="tooltip.selectMessageType"/>'
						type="button" value="...">-->
                                            &nbsp;&nbsp;
                                        </div>
                                        <!-- Start: Code modified by Sudhakar on 05-Apr-2011 for Mantis:1350"Decrease width of message type field  in Interface Rules Screen." -->
                                        <div id="manualMsgTypeDiv" style="height: 20; top: 60">
                                            <input type="text"  name="interfaceRule.otherMessageType" value="${interfaceRule.otherMessageType}"  cssClass="htmlTextAlpha" titleKey="tooltip.manualMessageType" style="width:300px;" tabindex="3" maxlength="30" />
                                        </div>
                                        <!-- End: Code modified by Sudhakar on 05-Apr-2011 for Mantis:1350"Decrease width of message type field  in Interface Rules Screen." -->
                                    </td>
                                </tr>
                            </table>
                            <!-- Start:code modified by venkat for issues found on V1051 beta testing- interface rules issues. -->
                            <!-- Start:code modified by Chinniah for issues found on V1051 beta testing- interface rules issues. -->
                            <table align="left">
                                <tr>
                                    <td width="450px">&nbsp;&nbsp;&nbsp;
                                    </td>
                                    <!-- End:code modified by Chinniah for issues found on V1051 beta testing- interface rules issues. -->
                                    <!-- End:code modified by venkat for issues found on V1051 beta testing- interface rules issues. -->
                                    <!-- Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                                    <!-- End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                                </tr>
                            </table>
                        </td>
                        <td>&nbsp;</td>
                    </tr>
                </table>
                <table align="left">
                    <tr>
                        <td width="">&nbsp;</td>
                        <td width="105px"><b style="font-size: 9pt">
                                <fmt:message key="interfacerulesmaintenance.ruleId"/></b></td>
                        <td colspan="3">
                            <input type="text" id="interfaceRule.id.ruleId" name="interfaceRule.id.ruleId" value="${interfaceRule.id.ruleId}"  class="htmlTextAlpha" titleKey="tooltip.enterRuleId" style="width:710px;" tabindex="4" maxlength="100" />&nbsp;&nbsp;
                            <input type="checkbox"
                                   name="interfaceRule.partialRuleId"
                                   id="interfaceRule.partialRuleId"
                                   value="Y"
                                   tabindex="5"
                                   style="width:13px;"
                                   class="htmlTextAlpha"
                                   title="<fmt:message key='tooltip.partialRuleId'/>"   ${requestScope.interfaceRule.partialRuleId == 'Y' ? 'checked' : ''} />
                            &nbsp;&nbsp;<label style="font-size: 9pt">
                                <fmt:message key="interfacerulesmaintenance.partialRuleId"/></label>&nbsp;</td>
                        <td width="60" align="left" id="ddimagebuttons">
                            <a id="searchButton" title='<fmt:message key="tooltip.executeSearch"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeydown="onKeyEnterSubmit('search', event);" onClick="submitForm('getList')">
                                <fmt:message key="button.search"/></a>
                        </td>
                    </tr>
                </table>
                        <!-- End: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
            </div>
            <!-- Start: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
            <!-- Start:code modified by Chinniah for issues found on V1051 beta testing- interface rules issues. -->
            <div id="pageSummaryList" style="position:absolute; left:920px; top:1px;  height:25px;border:6px;">
                <!-- End:code modified by Chinniah for issues found on V1051 beta testing- interface rules issues. -->
               <c:if test="${empty requestScope.hidePagination or requestScope.hidePagination ne 'true'}">
    <table border="0" cellpadding="0" cellspacing="1" height="25px" width="250px" style="padding-bottom: 7px;">
        <tr height="25px">
            <td height="34">
                <b style="font-size: 10pt">Page</b>&nbsp;&nbsp;
                <input class="htmlTextNumeric"
                       id="pageNoText"
                       tabindex="7"
                       name="pageNo"
                       size="5"
                       style="height: 21px;"
                       align="top"
                       value="${requestScope.currentPage}"
                       onchange="validatePageNumber(this);"
                       onkeydown="if (event.keyCode == 13)validatePageNumber(this);">
            </td>
            <td>
                <c:choose>
                    <c:when test="${requestScope.nextEnabled eq 'true'}">
                        <a href="#" onclick="clickLink(-1);" tabindex="7">
                            <img alt="Next page"
                                 src="images/page_up.png"
                                 align="top"
                                 border="0"
                                 width="18"
                                 style="padding-top:6px; height: 11px;"><br />
                            </img>
                        </a>
                    </c:when>
                    <c:otherwise>
                        <img alt="Next page"
                             src="images/page_up.png"
                             align="top"
                             border="0"
                             width="18"
                             style="padding-top:6px; height: 11px;"><br />
                        </img>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <c:when test="${requestScope.prevEnabled eq 'true'}">
                        <a href="#" onclick="clickLink(-2);" tabindex="7">
                            <img alt="Previous page"
                                 src="images/page_down.png"
                                 align="bottom"
                                 width="18"
                                 border="0"
                                 style="padding-bottom:6px; height: 11px;"><br />
                            </img>
                        </a>
                    </c:when>
                    <c:otherwise>
                        <img alt="Previous page"
                             src="images/page_down.png"
                             align="bottom"
                             width="18"
                             border="0"
                             style="padding-bottom:6px; height: 11px;"><br />
                        </img>
                    </c:otherwise>
                </c:choose>
            </td>
            <td style="text-align: center; font-size: 10pt">&nbsp;&nbsp;
                Of&nbsp;&nbsp;
                <input class="textAlpha"
                       style="background:transparent;border: 0;height:17; width:65px;"
                       tabindex="-1"
                       readonly
                       name="maxPageNo"
                       value="${requestScope.maxPage}"
                       size="3">
            </td>
        </tr>
    </table>
</c:if>
                <!-- End: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
            </div>
        </div>
        <!-- results -->
        <DIV ID="caldiv" STYLE="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></DIV>
        <div id="InterfaceRulesHeader" style="position:absolute; border-left:2px outset; left:20px; top:110px; width:1180px; height:477px;">
            <div id="InterfaceRulesHeader" style="position:absolute;z-index:99;left:0px; top:0px; width:1160px;height:10px;">
                <table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1752px" border="0" cellspacing="1" cellpadding="0" height="23">
                    <thead>
                        <tr>
                            <!-- Start: Code modified by Sudhakar on 05-Apr-2011 for Mantis:1350"Decrease width of message type coloumn field in Interface Rules Screen." -->
                            <td title='<fmt:message key="tooltip.sortMessageType"/>' width="224px" height="20px" align="center" style="border-left-width: 0px;"><b>
                                    <fmt:message key="interfacerulesmaintenance.messageType"/></b></td>
                            <!-- End: Code modified by Sudhakar on 05-Apr-2011 for Mantis:1350"Decrease width of message type coloumn field  in Interface Rules Screen." -->
                            <td title='<fmt:message key="tooltip.sortRuleId"/>' width="333px" height="20px" align="center"><b>
                                    <fmt:message key="interfacerulesmaintenance.ruleId"/></b></td>
                            <td title='<fmt:message key="tooltip.sortRuleKey"/>' width="330px" height="20px" align="center"><b>
                                    <fmt:message key="interfacerulesmaintenance.ruleKey"/></b></td>
                            <td title='<fmt:message key="tooltip.sortRuleValue"/>' width="865px" height="20px" align="left"><b>
                                    <fmt:message key="interfacerulesmaintenance.ruleValue"/></b></td>
                        </tr>
                    </thead>
                </table>
            </div>
            <div id="ddscrolltable" style="position:absolute; left:1px; top:1px; width:1175px; height:100% ;overflow:scroll">
                <div id="InterfaceRulesMaintenance" style="position:absolute;z-index:99;left:1px; top:18px; width:1175px; height:10px;">
                    <table class="sort-table" id="interfaceRulesList" width="1752px" border="0" cellspacing="1" cellpadding="0" height="438px">
                       <tbody>
                            <c:set var="count" value="0"/>
                            <c:forEach items="${requestScope.listViewData}" var="interfaceRulesList">
                                <tr height="20px" class="${count % 2 == 0 ? 'even' : 'odd'}">
                                    <input type="hidden" name="#messageType"  disabled value="${interfaceRulesList.id.messageType}"/>
                                    <input type="hidden" name="#ruleId" disabled value="${interfaceRulesList.id.ruleId}"/>
                                    <input type="hidden" name="#ruleKey" disabled value="${interfaceRulesList.id.ruleKey}"/>

                                    <td width="224px" title="${interfaceRulesList.id.messageType}" align="left" style="word-break: break-all;word-wrap: break-word;" nowrap="">
                                        ${fn:escapeXml(interfaceRulesList.id.messageType)}&nbsp;
                                    </td>
                                    <td width="333px" align="left" title="${interfaceRulesList.id.ruleId}" style="word-break: break-all;word-wrap: break-word;" nowrap="">
                                        ${fn:escapeXml(interfaceRulesList.id.ruleId)}&nbsp;
                                    </td>
                                    <td width="335px" align="left" title="${interfaceRulesList.id.ruleKey}" style="word-break: break-all;word-wrap: break-word;" nowrap="">
                                        ${fn:escapeXml(interfaceRulesList.id.ruleKey)}&nbsp;
                                    </td>
                                    <td width="860px" align="left" title="${interfaceRulesList.ruleValue}" style="word-break: break-all;word-wrap: break-word;" nowrap="">
                                        ${fn:escapeXml(interfaceRulesList.ruleValue)}&nbsp;
                                    </td>
                                </tr>
                                <c:set var="count" value="${count + 1}"/>
                            </c:forEach>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        <!-- Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
        <!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
        <div id="pnlReport" style="position:absolute; left:1113; top:615px; width:70px; height:39px;z-index:5; visibility:visible;">
            <table>
                <div id="exportReport" />
            </table>
        </div>
        <div id="helpIcon" style="position:absolute; left:1160; top:613px; width:30px; height:39px;">
            <a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="14" href=# onclick="javascript:openWindow(buildPrintURL('print','Interface Rule Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
                <img src="images/help_default.GIF " name="Help" border="0"></a>
        </div>
        <!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
        <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:603px; width:1180px; height:39px; visibility:visible;">
            <div id="InterfaceRuleMaintenance" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
                <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20">
                    <tr>
                        <td id="addbutton" width="70"></td>
                        <td id="changebutton" width="70"></td>
                        <td id="deletebutton" width="70"></td>
                        <!-- Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="closebutton" width="70"><a title='<fmt:message key="tooltip.close"/>' tabIndex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeydown="onKeyEnterSubmit('close', event);" onclick="confirmClose('P');">
                                <fmt:message key="button.close"/></a></td>
                        <!-- End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                    </tr>
                </table>
            </div>
            <div style="position: absolute; left: 6; top: 4; width: 700px; height: 15px; visibility: hidden;">
                <table border="0" cellspacing="0" cellpadding="0" height="20" style="visibility: hidden">
                    <tr>
                        <!-- Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="addenablebutton"><a title='<fmt:message key="tooltip.addNewInterfaceRule"/>' tabIndex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeydown="onKeyEnterSubmit('add', event);" onClick="javascript:openWindow(buildAddInterfaceRule('add'),'accountsmaintenancechangewindow','left=50,top=150,width=860,height=420 ,toolbar=0, resizable=yes, status=yes, scrollbars=no','true')">
                                <fmt:message key="button.add"/></a></td>
                        <!-- End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="adddisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.addNewInterfaceRule"/>'>
                                <fmt:message key="button.add"/></a></td>
                        <!-- Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="changeenablebutton"><a title='<fmt:message key="tooltip.changeSelInterfaceRule"/>' tabIndex="8" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeydown="onKeyEnterSubmit('change', event);" onClick="javascript:openWindow(changeInterfaceRule('change'),'accountsmaintenancechangewindow','left=50,top=150,width=860,height=420, toolbar=0, resizable=yes, status=yes, scrollbars=no','true')">
                                <fmt:message key="button.change"/></a></td>
                        <!-- End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="changedisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.changeSelInterfaceRule"/>'>
                                <fmt:message key="button.change"/></a></td>
                        <!-- Start:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="deleteenablebutton"><a title='<fmt:message key="tooltip.deleteSelInterfaceRule"/>' tabIndex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeydown="onKeyEnterSubmit('delete', event);" onClick="submitDeleteForm('delete')">
                                <fmt:message key="button.delete"/></a></td>
                        <!-- End:Code added by Venkat on 17-Feb-2011 for mantis 1365:"Allow Special characters in Interface Rules Screen." -->
                        <td id="deletedisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.deleteSelInterfaceRule"/>'>
                                <fmt:message key="button.delete"/></a>
                        </td>
                    </tr>
                </table>
            </div>
            <!-- Start: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
        </div>
    </form>
    <!-- End: Code modified by venkat on 24-feb-2011 for Mantis:1365"Allow Special characters in Interface Rules Screen." -->
</body>

</html>