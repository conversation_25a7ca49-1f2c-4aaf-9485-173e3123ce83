<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>






<accountspecificsweepformat>
	<singletons>
		<menuAccessId>${requestScope.menuAccessId}</menuAccessId>
	</singletons>

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<c:forEach var="order" items="${requestScope.column_order}">
					<c:choose>
						<c:when test="${order == 'specifiedEntityId'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.entityId'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="specifiedEntityId"
									width="${requestScope.column_width.specifiedEntityId}" />
						</c:when>
						<c:when test="${order == 'specifiedAccountId'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.accountId'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="specifiedAccountId"
									width="${requestScope.column_width.specifiedAccountId}" />
						</c:when>
						<c:when test="${order == 'accountName'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.accountName'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="accountName"
									width="${requestScope.column_width.accountName}" />
						</c:when>
						<c:when test="${order == 'newInternalCrFormat'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.newInternalCrFormat'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="newInternalCrFormat"
									width="${requestScope.column_width.newInternalCrFormat}" />
						</c:when>
						<c:when test="${order == 'newInternalDrFormat'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.newInternalDrFormat'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="newInternalDrFormat"
									width="${requestScope.column_width.newInternalDrFormat}" />
						</c:when>
						<c:when test="${order == 'newExternalCrFormat'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.newExternalCrFormat'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="newExternalCrFormat"
									width="${requestScope.column_width.newExternalCrFormat}" />
						</c:when>
						<c:when test="${order == 'newExternalDrFormat'}">
							<column heading="<fmt:message key='label.accountspecificsweepformat.column.newExternalDrFormat'/>"
									clickable="true"
									draggable="true"
									sort="true"
									filterable="true"
									type="str"
									dataelement="newExternalDrFormat"
									width="${requestScope.column_width.newExternalDrFormat}" />
						</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>

		<rows size="${requestScope.recordCount}">
			<c:forEach var="accountSpecificSweepFormatList" items="${requestScope.accountSpecificSweepFormatList}">
				<row>
					<hostId clickable="false">${accountSpecificSweepFormatList.id.hostId}</hostId>
					<entityId clickable="false">${accountSpecificSweepFormatList.id.entityId}</entityId>
					<accountId clickable="false">${accountSpecificSweepFormatList.id.accountId}</accountId>
					<accountName clickable="false">${accountSpecificSweepFormatList.accountName}</accountName>
					<specifiedEntityId clickable="false">${accountSpecificSweepFormatList.id.specifiedEntityId}</specifiedEntityId>
					<specifiedAccountId clickable="false">${accountSpecificSweepFormatList.id.specifiedAccountId}</specifiedAccountId>
					<newInternalCrFormat clickable="false">${accountSpecificSweepFormatList.newInternalCrFormat}</newInternalCrFormat>
					<newInternalDrFormat clickable="false">${accountSpecificSweepFormatList.newInternalDrFormat}</newInternalDrFormat>
					<newExternalCrFormat clickable="false">${accountSpecificSweepFormatList.newExternalCrFormat}</newExternalCrFormat>
					<newExternalDrFormat clickable="false">${accountSpecificSweepFormatList.newExternalDrFormat}</newExternalDrFormat>
				</row>
			</c:forEach>
		</rows>
	</grid>

	<selects>
		<select id="entity">
			<c:forEach var="entity" items="${requestScope.entities}">
				<option value="${entity.value}" selected="${entity.value == requestScope.entityId ? '1' : '0'}">
						${entity.label}
				</option>
			</c:forEach>
		</select>
		<select id="currency">
			<c:forEach var="currency" items="${requestScope.currencies}">
				<option value="${currency.value}" selected="${currency.value == requestScope.currencyId ? '1' : '0'}">
						${currency.label}
				</option>
			</c:forEach>
		</select>
		<select id="accounts">
			<c:forEach var="account" items="${requestScope.accounts}">
				<option value="${account.value}" selected="${account.value == requestScope.accountId ? '1' : '0'}">
						${account.label}
				</option>
			</c:forEach>
		</select>
	</selects>
</accountspecificsweepformat>
