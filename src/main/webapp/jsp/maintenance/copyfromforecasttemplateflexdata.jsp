<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<forecastmonitor
		databuilding="false"
		refresh="30"
		hideLoro="N"
		to="07/10/2008"
		from="01/10/2008"
		sysDateFrmSession="01/10/2008"
		dateComparing="false">

	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<grid/>

	<selects>
		<select id="templates">
			<c:forEach var="listMonitorTemplateDetails" items="${requestScope.listMonitorTemplateDetails}">
				<option value="${listMonitorTemplateDetails.userId}" selected="1">
						${listMonitorTemplateDetails.id.templateId}
				</option>
			</c:forEach>
		</select>
	</selects>
</forecastmonitor>
