<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${requestScope.methodName == 'save'}">
	<fmt:message key="currencyGroup.addScreen"/>
</c:if>

<c:if test="${requestScope.methodName == 'update'}">
		<fmt:message key="currencyGroup.changeScreen"/>
</c:if>

</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<SCRIPT language="JAVASCRIPT">
mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
	 cancelcloseElements[0] = "cancelbutton";

<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="displayListByEntity";
window.opener.document.forms[0].submit();
self.close();
</c:if>

function bodyOnLoad(){
	
	document.getElementById("entityName").innerText = '${requestScope.entityName}';
}

function enableFields(){
	document.forms[0].elements["currencyGroup.id.entityId"].disabled = "";
	document.forms[0].elements["currencyGroup.id.currencyGroupId"].disabled = "";
	
}


	

function validateForm(objForm){
	
   var elementsRef = new Array(3);
   elementsRef[0] = objForm.elements["currencyGroup.id.entityId"];
   elementsRef[1] = objForm.elements["currencyGroup.id.currencyGroupId"];
   elementsRef[2] = objForm.elements["currencyGroup.currencyGroupName"];
   return validate(elementsRef);
}

function submitForm(methodName){
/*Betcy:23/12/2008:Added to check the validation in save button for Mantis 774(start)*/
  var cgroupId=validateField(document.forms[0].elements['currencyGroup.id.currencyGroupId'],'currencyGroup.id.currencyGroupId','alphaNumPat');
  
  
  
    if(cgroupId)
	{
	  var cgroupName=validateField(document.forms[0].elements['currencyGroup.currencyGroupName'],'book.bookCodeadd','alphaNumPatWithSpace');
	  if(cgroupName)
	  {
	 /*Betcy:23/12/2008:Added to check the validation in save button for Mantis 774(end)*/ 
	if(validateForm(document.forms[0]) && validateCurrencyGrpId()){
		 enableFields();
	
    	document.forms[0].method.value = methodName;
		document.forms[0].entityText.value = '${requestScope.entityName}';
	    document.forms[0].submit();
	}
	/*Betcy:23/12/2008:Added to check the validation in save button for Mantis 774(start)*/
	}
	else
	{
	  document.forms[0].elements['currencyGroup.currencyGroupName'].focus();
	}
	}
	else
	{
	   document.forms[0].elements['currencyGroup.id.currencyGroupId'].focus();
	}
	/*Betcy:23/12/2008:Added to check the validation in save button for Mantis 774(end)*/
}


String.prototype.equalsIgnoreCase=MatchIgnoreCase;

function MatchIgnoreCase(strTerm){
var strToSearch = this.toLowerCase();
strTerm = strTerm.toLowerCase();
if(strToSearch==strTerm){
return true;
}else
{
return false;
}
}



function validateCurrencyGrpId()
{
	var currencyGrpId = document.forms[0].elements["currencyGroup.id.currencyGroupId"].value;	
	var str1 = (new String("all")).valueOf();
	var comp = currencyGrpId.equalsIgnoreCase(str1);		
	if(comp == true){			
			alert("<fmt:message key="currencyGroupChild.alert.IdasAll"/>");
			if(window.event)
				window.event.returnValue = false;
			document.forms[0].elements["currencyGroup.id.currencyGroupId"].focus();			
			return false;
			
		}else {
			return true;
		}
}

</SCRIPT>
<form action="currencygroup.do" onsubmit="return validate(this);">
<input name="method" type="hidden" value="">
<input name="entityText" type="hidden" value="">


<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()" >
  <div id="CurrencyGroupChild" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:520px; height:103px;">

 <div style="position:absolute; left:8px; top:4px; width:505px; height:80px;">  
	<table width="498" border="0" cellpadding="0" cellspacing="0" height="50">
		<tr height="23">
			<td  width="50px"><b><fmt:message key="entity.id"/></b></td>
			<td width="28px">&nbsp;</td>
			<td  width="120px" >				
				<input type="text" name="currencyGroup.id.entityId" value="${currencyGroup.id.entityId}"  tabindex="1"  styleClass="htmlTextAlpha"  titleKey="tooltip.entityId" maxlength="12" style="width:120px;" disabled = "true" />
			</td>			
			<td width="20px">&nbsp;</td>
			<td width="280px">				
				<span id="entityName"   name="entityName" class="spantext"> 
			</td>				
		</tr>
		 <tr height="25px">
		  <c:if test="${requestScope.methodName == 'save'}">
			<td width="50px"><b><fmt:message key="currencygroup.group"/></b>*</td>
			<td width="28px">&nbsp;</td>
			<td width="120px">
				<input type="text" name="currencyGroup.id.currencyGroupId" value="${currencyGroup.id.currencyGroupId}" tabindex="2"  styleClass="htmlTextAlpha" titleKey="tooltip.currencyGroupIdentifier"  maxlength="12" style="width:120px;" onchange="validateField(this,'currencyGroupId','alphaNumPat');" />
			</td>
		  </c:if>

		  <c:if test="${requestScope.methodName == 'update'}">
			<td width="50px"><b><fmt:message key="currencygroup.group"/></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px">
				<input type="text" name="currencyGroup.id.currencyGroupId" value="${currencyGroup.id.currencyGroupId}" tabindex="2"  styleClass="htmlTextAlpha"  maxlength="12" style="width:120px;" disabled= "true"   onchange="validateField(this,'currencyGroupId','alphaNumPat');" />
			</td>
		  </c:if>

			
		</tr>
		</table>
		<table width="518" border="0" cellpadding="0" cellspacing="0" height="25">
		<tr height="23">
				<td width="50px"><b><fmt:message key="section.sectionName"/></b>*</td>
				<td width="22px">&nbsp;</td>
				<td width="280px">
				<!-- Vivekanandan:09/06/2008:Added  onchange action is called for the textfield -->
				<input type="text" name="currencyGroup.currencyGroupName"  value="${currencyGroup.currencyGroupName}" tabindex="3" titleKey="tooltip.currencyGroupName" styleClass="htmlTextAlpha"   maxlength="30" style="width:280px;"  onchange="validateField(this,'currencyGroupName','alphaNumPatWithSpace');" />
				</td>
				<td width="151px">&nbsp;</td>
				</tr>					
		</table>
	
</div>
</div>

 <div id="CurrencyGroupChild" style="position:absolute; left:465px; top:136; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<!-- Start : Vivekanandan : 09/06/2008 : To display separate help window for add and change currency group -->
			<td align="Right">
			<c:if test="${requestScope.methodName == 'save'}">
				 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Currency Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if>
			<c:if test="${requestScope.methodName == 'update'}">
				<a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Currency Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if>
			<!-- End : Vivekanandan : 09/06/2008 : To display separate help window for add and change currency group -->
			 </td>
	
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:128px; width:520px; height:39px; visibility:visible;">
 <div id="CurrencyGroupChild" style="position:absolute; left:6; top:4; width:520; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" >    
		<tr>                    
		 <td id="savebutton">		
			<a  tabindex="4" title='<fmt:message key = "tooltip.save"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onclick="javascript:submitForm('${methodName}')" onMouseUp="highlightbutton(this)" ><fmt:message key="button.save"/></a>
		  </td>	
			 <td  width="70px" id="cancelbutton">		
               <a  tabindex="5" title='<fmt:message key = "tooltip.cancel"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" ><fmt:message key="button.cancel"/></a>
		  </td>	
        </tr>
    </table>
</div>
</body>
</form>
</html>