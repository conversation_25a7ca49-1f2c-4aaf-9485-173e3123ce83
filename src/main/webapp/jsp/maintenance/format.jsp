<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<%@ include file="/taglib.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<head>
	<!--Betcy:12/11/08:Added(start):To display title content correctly-->
	<c:choose>
		<c:when test="${screenStatus == 'add'}">
			<title><fmt:message key="title.addFormats"/></title>
		</c:when>
		<c:when test="${screenStatus == 'change'}">
			<title><fmt:message key="title.changeFormats"/></title>
		</c:when>
		<c:when test="${screenStatus == 'view'}">
			<title><fmt:message key="title.viewFormats"/></title>
		</c:when>
	</c:choose>
	<!--Betcy:12/11/08:Added(end)To display title content correctly-->
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

	<SCRIPT language="JAVASCRIPT">
		<c:if test="${parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].newCrInternal.value = '${requestScope.newCrInternal}';
		window.opener.document.forms[0].newCrExternal.value ='${requestScope.newCrExternal}';
		window.opener.document.forms[0].newDrInternal.value='${requestScope.newDrInternal}';
		window.opener.document.forms[0].newDrExternal.value='${requestScope.newDrExternal}';
		window.opener.document.forms[0].CrExternalInt.value='${requestScope.CrExternalInt}';
		window.opener.document.forms[0].DdExternalInt.value='${requestScope.DdExternalInt}';
		self.close();
		</c:if>
		function submitForm(methodName){
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}

		function bodyOnLoad(){
			//alert("screenStatus is "+'${requestScope.screenStatus}');
		}
</SCRIPT>
</head>
<%-- START: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">
<form action="acctMaintenance.do" method="post" name="acctMaintenanceForm">
<input name="method" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value='${selectedEntityId}'>

<div id="CurrencyChange" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:354px; height:193px;">
    <div style="position:absolute; left:8px; top:4px; width:345px; height:80px;">
        <table width="335" border="0" cellpadding="0" cellspacing="0" height="150">
            <tr height="25px">
                <td width="152px">&nbsp;<b><fmt:message key="format.cdInt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.acctNewCrInternal" tabindex="1" disabled="${requestScope.screenFieldsStatus}" style="width:140px;" title="<fmt:message key="tooltip.selectNewCrMsg"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.acctNewCrInternal == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
            <tr height="25px">
                <td width="152px">&nbsp;<b><fmt:message key="fomat.cdExt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.acctNewCrExternal" style="width:140px" tabindex="2" disabled="${requestScope.screenFieldsStatus}" title="<fmt:message key="tooltip.newcdext"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.acctNewCrExternal == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
            <tr height="25px">
                <td width="132px">&nbsp;<b><fmt:message key="fomat.cdExtInt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.creditExternalInter" style="width:140px" tabindex="6" disabled="${requestScope.screenFieldsStatus}" title="<fmt:message key="tooltip.cancdext"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.creditExternalInter == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
            <tr height="25px">
                <td width="152px">&nbsp;<b><fmt:message key="fomat.ddInt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.acctNewDrInternal" style="width:140px" tabindex="3" disabled="${requestScope.screenFieldsStatus}" title="<fmt:message key="tooltip.newddint"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.acctNewDrInternal == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
            <tr height="25px">
                <td width="152px">&nbsp;<b><fmt:message key="format.ddExt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.acctNewDrExternal" style="width:140px" tabindex="4" disabled="${requestScope.screenFieldsStatus}" title="<fmt:message key="tooltip.newddext"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.acctNewDrExternal == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
            <tr height="25px">
                <td width="128px">&nbsp;<b><fmt:message key="fomat.ddExtInt"/></b></td>
                <td width="28px">&nbsp;</td>
                <td width="140px">
                    <select name="acctMaintenance.debitExternalInter" style="width:140px" tabindex="5" disabled="${requestScope.screenFieldsStatus}" title="<fmt:message key="tooltip.cancdint"/>">
                        <c:forEach items="${acctmaintFormatlist}" var="format">
                            <option value="${format.value}" ${acctMaintenance.debitExternalInter == format.value ? 'selected' : ''}>${format.label}</option>
                        </c:forEach>
                    </select>
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- Additional elements like buttons for help, print, etc. -->

<c:choose>
    <c:when test="${screenStatus == 'view'}">
        <!-- View specific buttons/links -->
        <div id="EntityMaintenance" style="position:absolute; left:288; top:228; width:70px; height:39px; visibility:visible;">
            <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr>
                    <td align="Right">
                        <a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','View Format'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)"><img src="images/help_default.GIF" name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>" /></a>
                    </td>
                    <td align="right" id="Print">
                        <a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)"><img src="images/Print.gif" name="Print" border="0" title="<fmt:message key='tooltip.printScreen'/>" /></a>
                    </td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:220px; width:352px; height:39px; visibility:visible;">
            <div id="AccountMaintenanceContact" style="position:absolute; left:6; top:4; width:338px; height:15px; visibility:visible;">
                <table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
                    <tr>
                        <td id="closebutton" width="70" title="<fmt:message key='tooltip.cancel'/>">
                            <a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();"><fmt:message key="button.close"/></a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </c:when>
    <c:otherwise>
        <!-- Add/Change specific buttons/links -->
        <div id="EntityMaintenance" style="position:absolute; left:288; top:228; width:70px; height:39px; visibility:visible;">
            <table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr>
                    <td align="Right">
                        <c:choose>
                            <c:when test="${screenStatus == 'add'}">
                                <a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Format'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)"><img src="images/help_default.GIF" name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>" /></a>
                            </c:when>
                            <c:when test="${screenStatus == 'change'}">
                                <a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','Change Format'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)"><img src="images/help_default.GIF" name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>" /></a>
                            </c:when>
                        </c:choose>
                    </td>
                    <td align="right" id="Print">
                        <a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)"><img src="images/Print.gif" name="Print" border="0" title="<fmt:message key='tooltip.printScreen'/>" /></a>
                    </td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:220px; width:352px; height:39px; visibility:visible;">
            <div id="EntityMaintenance" style="position:absolute; left:6; top:4; width:338; height:15px; visibility:visible;">
                <table width="140" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td id="okbutton">
                            <a tabindex="9" title="<fmt:message key='tooltip.ok'/>" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('saveFormatDetails');"><fmt:message key="button.ok"/></a>
                        </td>
                        <td width="70px" id="cancelbutton">
                            <a tabindex="10" title="<fmt:message key='tooltip.cancel'/>" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();"><fmt:message key="button.cancel"/></a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </c:otherwise>
</c:choose>

</form>
</body>
</html>