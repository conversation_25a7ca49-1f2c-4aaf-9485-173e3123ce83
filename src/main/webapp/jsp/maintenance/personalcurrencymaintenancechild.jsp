<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
	<title><fmt:message key="personalCurrency.childscreenName"/></title>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(start)-->
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
<!--Betcy::12/01/2009:Added for Mantis 774 to close the screen(end)-->
mandatoryFieldsArray = ["*"];
<c:if test="${requestScope.parentFormRefresh == 'Y'}">

window.opener.document.forms[0].method.value="displayPersonalCurrencyList";
window.opener.document.forms[0].submit();
self.close();

</c:if>

function isEqualToZero(input){

	if (input.value=="0"||input.value=="00"){
		alert("<fmt:message key="alert.priorityNotBeZero"/>");
		input.value="";
		input.focus();
		return false;
	}
	return true;
}
function submitForm(methodName){
/*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
    var priorityValue=validateField(document.forms[0].elements['personalCurrency.priorityOrder'],'personalCurrency.priorityOrder','numberPat');
	/*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
	var methodNameVar = '${requestScope.methodName}';

	if (methodNameVar == "save") {
	document.forms[0].saveOrUpdateFlag.value = "save";
	} else {
	document.forms[0].saveOrUpdateFlag.value = "update";
	}
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
  if(priorityValue)
  {
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
  if(validateForm(document.forms[0]) )
  {
	  //personalCurrency.alert.priority
	  enableFields();
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
  }
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(Start)*/
  }
  else
  {
    document.forms[0].elements['personalCurrency.priorityOrder'].focus();
  }
  /*Betcy:12/01/09:Added for Mantis issue 751 to check the validation in "SAVE" button click(End)*/
}

function enableFields(){
	document.forms[0].elements["personalCurrency.id.currencyCode"].disabled = "";
}

function bodyOnLoad()
{
    <c:if test="${requestScope.methodName == 'save'}">

 		var currencydropBox = new SwSelectBox(document.forms[0].elements["personalCurrency.id.currencyCode"],document.getElementById("currencyName"));

</c:if>
	<c:if test="${requestScope.methodName != 'save'}">

		document.getElementById("currencyName").innerText = '${requestScope.currencyName}'

</c:if>


	}

function validateForm(objForm){
  var elementsRef = new Array();
  elementsRef[0] = objForm.elements["personalCurrency.priorityOrder"];

  return validate(elementsRef);
}

var currencyFormat = '${sessionScope.CDM.currencyFormat}';
</SCRIPT>

<html>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">
<form action="personalcurrency.do" onsubmit="validate(this);">
<input name="method" type="hidden" value="">
<input name="saveOrUpdateFlag" type="hidden" value="">
<input name="dummy" value="" style="visibility:hidden">

<div id="PersonalCurrencyChild" style="position:absolute; left:20px; top:20px; width:390px; height:61px; border:2px outset;" color="#7E97AF">
	<div id="PersonalCurrencyChild" style="position:absolute;z-index:99;left:8px; top:4px; width:350px; height:35px;">
		<table width="380px" border="0" cellpadding="0" cellspacing="0" height="50">
		<tr>
			<td width="93px"><b><fmt:message key="role.perCurrrency.currency"/></b></td>
			<td width="370px">
				<c:if test="${requestScope.methodName eq 'save'}">
					<select
						id="personalCurrency.id.currencyCode"
						name="personalCurrency.id.currencyCode"
						style="width:60px"
						title='<fmt:message key="tooltip.selectCurrencyId"/>'
						tabindex="1">
						<c:forEach items="${requestScope.perCurrList}" var="currency">
							<option value="${currency.value}">${currency.label}</option>
						</c:forEach>
					</select>
				</c:if>
				<c:if test="${requestScope.methodName eq 'update'}">
					<input type="text" tabindex="1" name="personalCurrency.id.currencyCode" value="${personalCurrency.id.currencyCode}"  cssClass="htmlTextAlpha"  style="width:40px;" disabled="true" />
				</c:if>&nbsp;&nbsp;&nbsp;
				<span id="currencyName" name="currencyName" class="spantext"></span>
			</td>
		</tr>
		<tr>
			<c:if test="${requestScope.methodName eq 'save' or requestScope.methodName eq 'update'}">
				<td width="93px"><b><fmt:message key="role.perCurrrency.priority"/></b>*</td>
			</c:if>
			<td width="370px">
							<input type="text" tabindex="2" titleKey="tooltip.enterPriority" cssClass="htmlTextNumeric" name="personalCurrency.priorityOrder" value="${personalCurrency.priorityOrder}"  maxlength="2"   style="width:25px;" onchange="return validateField(this,'personalCurrency.priorityOrder','numberPat');"  onblur="isEqualToZero(this)" />

			</td>
		</tr>
		</table>
	</div>
</div>

<div id="PersonalCurrencyChild" style="position:absolute; left:340; top:94px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		<td align="Right">
				<a tabindex="6" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Personal Currency'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
					<img src="images/help_default.GIF " name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'/>
				</a>
	    </td>
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)">
					<img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'/>
				</a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:85px; width:390px; height:39px; visibility:visible;">
<div id="PersonalCurrencyChild" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td width="70px">
		<a title='<fmt:message key="tooltip.save"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveOrUpdate');"><fmt:message key="button.save"/></a>
		</td>
		<td id="cancelbutton" width="70px">
			<a title='<fmt:message key="tooltip.cancel"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
		</td>
	</tr>
  </table>
</div>
</div>
</form>
</body>
</html>