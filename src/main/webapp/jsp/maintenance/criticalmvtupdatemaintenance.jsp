<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
<meta charset="utf-8">
<title>	<s:text name="criticalMvtUpdateMaint.title"/></title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "CriticalMvtUpdateMaint";
var uploadFileImage = "images/open_up.png";
var menuAccessId = '${requestScope.menuAccessId}';

function openDetailScreen(methodName, screen){
	var param = '/' + appName + '/criticalPaymentType.do?method='+methodName;
	param += '&screen=' + screen;
	var 	mainWindow = openWindow (param, 'criticalPayUpdateDetail','left=10,top=230,width=1200,height=900,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
	return false;
}


 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>