<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<title><c:if test="${requestScope.methodName == 'add'}">
	<fmt:message key="matchQuality.addScreen" />
</c:if> <c:if test="${requestScope.methodName == 'change'}">
	<fmt:message key="matchQuality.changeScreen" />
</c:if> <c:if test="${requestScope.methodName == 'view'}">
	<fmt:message key="matchQuality.viewScreen" />
</c:if></title>


<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</c:if>

	function submitForm(methodName){
		assignCheckBoxId('collMatchParams');
		IsChecked('collMatchParams');
		if(validateForm(document.forms[0]) ){
		    enableFields();
		    document.forms[0].method.value = methodName;
		    document.forms[0].submit();
		}
	}

	function buildParamCodeURL(methodName){
	
		var param = 'matchQuality.do?method='+methodName+'&entityCode=';
		    param +=document.forms[0].elements['matchQuality.id.entityId'].value;
		    param +='&currencyId=';
		    param +=document.forms[0].elements['matchQuality.id.currencyCode'].value;
		    param +='&posLevel=';
		    param +=document.forms[0].elements['matchQuality.id.posLevel'].value;	    
		 
		return  param;
	}
	
	function enableFields()
	{
		
		if(document.forms[0].elements['matchQuality.id.entityId'])
			document.forms[0].elements['matchQuality.id.entityId'].disabled = "";
      
		if(document.forms[0].elements['matchQuality.id.currencyCode'])
			document.forms[0].elements['matchQuality.id.currencyCode'].disabled = "";
         
		if(document.forms[0].elements['matchQuality.id.posLevel'])
			document.forms[0].elements['matchQuality.id.posLevel'].disabled = "";
				
		document.getElementById("entityDesc").disabled = "";
		document.getElementById("currencyName").disabled = "";
	}

	function assignCheckBoxId(tableId)
	{
		var tableElement = document.getElementById(tableId);
		var tbodyElement = tableElement.getElementsByTagName("tbody")[0];
		var rowElements = tbodyElement.getElementsByTagName("tr");

		for( var rowIdx = 0 ; rowIdx < rowElements.length ; ++rowIdx)
		{
			
			var paramCode = rowElements[rowIdx].getElementsByTagName("input")[0].value;
			var colls = rowElements[rowIdx].getElementsByTagName("td");

			addHiddenElement("paramCode_"+rowIdx,paramCode);

			for( var colIdx = 1 ; colIdx < colls.length ; ++colIdx)
			{
				var checkBoxElement = colls[colIdx].getElementsByTagName("input")[0];
				var isChecked = checkBoxElement.checked;
				var hiddenElId = "matchquality_"+paramCode +"_"+colIdx;
				checkBoxElement.id = hiddenElId;
				checkBoxElement.name = hiddenElId;
			}			
		}
	}


// This function assignes the matchQualityAction as 'No Match' if no parameter is checked for that Match Quality
	function IsChecked(tableId)
	{
		var tableElement = document.getElementById(tableId);
		var tbodyElement = tableElement.getElementsByTagName("tbody")[0];
		var rowElements = tbodyElement.getElementsByTagName("tr");
		var isChecked = false;
		var noofCols = rowElements[0].getElementsByTagName("td"); // Array of 'td' in first row
		
		for(var colIdx =1 ; colIdx < noofCols.length; ++colIdx) {

			for( var rowIdx = 0 ; rowIdx < rowElements.length ; ++rowIdx)
			{
				
				var paramCode = rowElements[rowIdx].getElementsByTagName("input")[0].value;
				var colls = rowElements[rowIdx].getElementsByTagName("td");

			
					var checkBoxElement = colls[colIdx].getElementsByTagName("input")[0];
					
					isChecked = checkBoxElement.checked;
			
					if (isChecked) {
		
					break;
					}
		
			}
 
			if (!isChecked) { // enters if  No Check box is checked for this colIdx
			switch (colIdx)
			{
			 case 1:

				 document.forms[0].elements["matchQualityA"].value = 'N';
				
				 break;
			 case 2:

				 document.forms[0].elements["matchQualityB"].value = 'N';
				
				 break;
			 case 3:

				 document.forms[0].elements["matchQualityC"].value = 'N';
				
				 break;

			 case 4:

				 document.forms[0].elements["matchQualityD"].value = 'N';
				
				 break;
			  case 5:

				 document.forms[0].elements["matchQualityE"].value = 'N';
				
				 break;
			}
			}
		}
	} 


	function addHiddenElement(id,value)
	{
		var el = document.createElement("input");
		el.type="hidden";
		el.value=value;
		el.id = id;
		el.name = id;
		document.forms[0].appendChild(el);
	}

	function disabledAllFields()
	{
		// Disabled all Check Boxes
		
		var tableElement = document.getElementById("collMatchParams");
		var tbodyElement = tableElement.getElementsByTagName("tbody")[0];
		var rowElements = tbodyElement.getElementsByTagName("tr");
		for( var rowIdx = 0 ; rowIdx < rowElements.length ; ++rowIdx)
		{
			var colls = rowElements[rowIdx].getElementsByTagName("td");
			for( var colIdx = 1 ; colIdx < colls.length ; ++colIdx)
			{
				var checkBoxElement = colls[colIdx].getElementsByTagName("input")[0];
				checkBoxElement.disabled="true";
			}
		}

		document.forms[0].elements["matchQualityA"].disabled="true";
		document.forms[0].elements["matchQualityB"].disabled="true";
		document.forms[0].elements["matchQualityC"].disabled="true";
		document.forms[0].elements["matchQualityD"].disabled="true";		
		document.forms[0].elements["matchQualityE"].disabled="true";		

	}

function formOnLoad()
{
		document.getElementById("entityDesc").innerText = '${entityName}';
		document.getElementById("currencyName").innerText = '${currencyName}';
		document.getElementById("posLvlName").innerText = '${requestScope.posLevelName}';

	<c:if test="${requestScope.methodName == 'view'}">
			disabledAllFields();
	</c:if>
}

function submitCopyFrom(methodName){
		
	var param = 'matchQuality.do?method='+methodName+'&entityCode=';
		    param +=document.forms[0].elements['matchQuality.id.entityId'].value;
		    param +='&currencyId=';
		    param +=document.forms[0].elements['matchQuality.id.currencyCode'].value;
		    param +='&posLevel=';
		    param +=document.forms[0].elements['matchQuality.id.posLevel'].value;	 
		     param +='&entityName=';
		    param +=document.getElementById('entityDesc').innerText;	    
		 
		return  param;
	}
function bodyOnLoad()
{
	
	var dropBox1 = new SwSelectBox(document.forms[0].elements["matchQuality.id.currencyCode"],document.getElementById("currencyName"));
	//code  added on 08 08 2006 as per Match Quality Maintenance Design Document
	
	new SwSelectBox(document.forms[0].elements["matchQuality.id.posLevel"],document.getElementById("posLvlName"));
	
}

function validateForm(objForm){
  var elementsRef = new Array(2);
   elementsRef[0] = objForm.elements["matchQuality.id.currencyCode"];
  elementsRef[1] = objForm.elements["matchQuality.id.posLevel"];
  return validate(elementsRef);
}
</SCRIPT>

</head>
<!-- Start:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove functionality that disable 'Amount Total' checkbox -->
<c:choose>
    <c:when test="${requestScope.methodName == 'add'}">
        <body leftmargin="0" topmargin="0" marginheight="0"
            onLoad="formOnLoad(); bodyOnLoad(); setParentChildsFocus(); setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');"
            onunload="call()">
        </body>
    </c:when>
    <c:otherwise>
        <body leftmargin="0" topmargin="0" marginheight="0"
            onLoad="formOnLoad(); setParentChildsFocus(); setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');"
            onunload="call()">
        </body>
    </c:otherwise>
</c:choose>

<form action="matchQuality.do" onsubmit="validate(this);">
	<input name="method" type="hidden" value="add">
	<input name="oldValue" type="hidden" value="${oldValue}">
	<input name="entityName" type="hidden" value="${entityName}">
	<input name="selectedPostionLevel" type="hidden" value="add">
	<input name="selectedCurrencyCode" type="hidden" value="add">
	<input name="currencyId" type="hidden" value="add">
	<input name="posLevel" type="hidden" value="add">
	<div id="MatchQualityAdd"
		style="position: absolute; left: 20px; top: 20px; width: 739px; height: 85px; border: 2px outset;"
		color="#7E97AF">
	<div id="MatchQualityAdd"
		style="position: absolute; z-index: 99; left: 8px; top: 5px; width: 550px; height: 85px;">
	<table width="550" border="0" cellpadding="0" cellspacing="0"
		height="65">

		<tr height="24" color="black" border="0">
			<td width="102"><b><fmt:message key="entity.id" /></b></td>
			<td width="28">&nbsp;</td>
			<td width="120"><input type="text" cssClass="htmlTextAlpha"
				name="matchQuality.id.entityId" value="${matchQuality.id.entityId}" style="width:120px"
				disabled="true" /></td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="entityDesc" name="entityDesc"
				class="spantext"></td>
		</tr>

		<tr height="24" color="black" border="0">
		<c:choose>
			<c:when test="${requestScope.methodName == 'add'}">
				<td width="102"><b><fmt:message key="matchQuality.currency.idadd" /></b>*</td>
				<td width="28">&nbsp;</td>
				<td width="120"><select cssClass="htmlTextAlpha" 
				name="matchQuality.id.currencyCode" 
				style="width:55px" title='<fmt:message key="tooltip.selectCurrencyCode"/>'
				tabindex="1">
    			<c:forEach items="${requestScope.currencies}" var="ccy">
					   <option
						  value="${ccy.value}"
						  <c:if test="${matchQuality.id.currencyCode == ccy.value}">selected="selected"</c:if>>
						  ${ccy.label}
					   </option>
				</c:forEach>
				</select></td>
			</c:when>
			<c:otherwise>
				<td width="102"><b><fmt:message key="matchQuality.currency.idadd" /></b></td>
				<td width="28">&nbsp;</td>
				<td width="120"><input type="text" cssClass="htmlTextAlpha"
					name="matchQuality.id.currencyCode" value="${matchQuality.id.currencyCode}" disabled="true"
					style="width:35px" tabindex="1" /></td>
			</c:otherwise>
			</c:choose>
			</td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="currencyName" name="currencyName"
				class="spantext"></td>
		</tr>

		<tr height="24" color="black" border="0">
				<c:choose>
    			<c:when  test="${requestScope.methodName == 'add'}">
				<td width="102"><b><fmt:message key="matchQuality.posLeveladd" /></b>*</td>
				<td width="28">&nbsp;</td>
				<td width="120"><select styleClass="htmlTextAlpha"
					name="matchQuality.id.posLevel" style="width:40px"
					title='<fmt:message key="tooltip.selectPosLevel"/>' tabindex="2">
    				    <c:forEach items="${requestScope.position}" var="pos">
						   <option
							  value="${pos.value}"
							  <c:if test="${matchQuality.id.posLevel == pos.value}">selected="selected"</c:if>>
							  ${pos.label}
						   </option>
						</c:forEach>
				</select></td>
				<td width="20">&nbsp;</td>
				<td width="280"><span id="posLvlName" name="posLvlName"
					class="spantext"></td>

			    </c:when>
				<c:otherwise>

				<td width="102"><b><fmt:message key="matchQuality.posLeveladd" /></b></td>
				<td width="28">&nbsp;</td>
				<td width="120"><input type="text" cssClass="htmlTextAlpha"
					name="matchQuality.id.posLevel" value="${matchQuality.id.posLevel}"  style="width:19px"
					disabled="true" tabindex="2" /></td>
				<td width="20">&nbsp;</td>
				<td width="280"><span id="posLvlName" name="posLvlName"
					class="spantext"></td>
			    </c:otherwise>
				</c:choose>
			</td>

		</tr>
	</table>
	</div>
	</div>

	<div id="MatchQualityAdd" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20px; top: 110px; width: 739px; height: 260px;">

	<div id="MatchQualityAdd"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 739px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="735"
		border="0" cellspacing="1" cellpadding="0">
		<thead>
			<tr height="20px">
				<td width="280"
					title='<fmt:message key="tooltip.matchQuality.paramDesc"/>'
					align="center"><b><fmt:message key="matchQuality.paramDesc" /></b></td>
				<td width="88"
					title='<fmt:message key="tooltip.matchQuality.matchQuaA"/>'
					align="center"><b><fmt:message key="matchQuality.matchQuaA" /></b></td>
				<td width="88"
					title='<fmt:message key="tooltip.matchQuality.matchQuaB"/>'
					align="center"><b><fmt:message key="matchQuality.matchQuaB" /></b></td>
				<td width="88"
					title='<fmt:message key="tooltip.matchQuality.matchQuaC"/>'
					align="center"><b><fmt:message key="matchQuality.matchQuaC" /></b></td>
				<td width="88"
					title='<fmt:message key="tooltip.matchQuality.matchQuaD"/>'
					align="center"><b><fmt:message key="matchQuality.matchQuaD" /></b></td>
				<td width="88"
					title='<fmt:message key="tooltip.matchQuality.matchQuaE"/>'
					align="center"><b><fmt:message key="matchQuality.matchQuaE" /></b></td>
			</tr>
		</thead>
	</table>
	</div>

	<div id="MatchQualityAdd"
		style="position: absolute; left: 0px; top: 20px; width: 666px; height: 280px;">
	<table class="sort-table" id="collMatchParams" width="735" border="0"
		cellspacing="1" cellpadding="0" height="188">

	<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="collMatchParams" items="${requestScope.collMatchParams}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}" height="20">
            <c:set var="paramCode" value="${collMatchParams.paramCode}" />
            <input type="hidden" name="paramCode" value="${paramCode}" />
            <td width="280" height="20" align="left">${collMatchParams.paramDesc}</td>
            <td width="88" height="20" align="center">
                <input type="checkbox" ${collMatchParams.matchQualityAHTML == 'checked' ? 'checked' : ''} />
            </td>
            <td width="88" height="20" align="center">
                <input type="checkbox" ${collMatchParams.matchQualityBHTML == 'checked' ? 'checked' : ''} />
            </td>
            <td width="88" height="20" align="center">
                <input type="checkbox" ${collMatchParams.matchQualityCHTML == 'checked' ? 'checked' : ''} />
            </td>
            <td width="88" height="20" align="center">
                <input type="checkbox" ${collMatchParams.matchQualityDHTML == 'checked' ? 'checked' : ''} />
            </td>
            <td width="88" height="20" align="center">
                <input type="checkbox" ${collMatchParams.matchQualityEHTML == 'checked' ? 'checked' : ''} />
            </td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
	</tbody>
		<tfoot>
			<tr>
				<td colspan="6"></td>
			</tr>
		</tfoot>
	</table>
	</div>

	<div id="action"
		style="position: absolute; z-index: 99; left: 1px; top: 232px; width: 555px; height: 10px;">
	<table width="735" border="0" cellpadding="0" cellspacing="1"
		bgcolor="#FFFFFF">

		<tr>
			<td width="287"></td>
			<td width="90" title='<fmt:message key="tooltip.reqconoffered"/>'>
			<select cssClass="htmlTextAlpha" name="matchQualityA"
				style="width:90px" tabindex="3">
    			<c:forEach items="${requestScope.qualityActionColl}" var="action">
				   <option
					  value="${action.value}"
					  <c:if test="${requestScope.matchQualityA == action.value}">selected="selected"</c:if>>
					  ${action.label}
				   </option>
				</c:forEach>
			</select></td>
			<td width="90"><select cssClass="htmlTextAlpha"
				name="matchQualityB" style="width:90px" tabindex="4">
    			<c:forEach items="${requestScope.qualityActionColl}" var="action">
				   <option
					  value="${action.value}"
					  <c:if test="${requestScope.matchQualityB == action.value}">selected="selected"</c:if>>
					  ${action.label}
				   </option>
				</c:forEach>
			</select></td>
			<td width="90"><select cssClass="htmlTextAlpha"
				name="matchQualityC" style="width:90px" tabindex="5">
    			<c:forEach items="${requestScope.qualityActionColl}" var="action">
				   <option
					  value="${action.value}"
					  <c:if test="${requestScope.matchQualityC == action.value}">selected="selected"</c:if>>
					  ${action.label}
				   </option>
				</c:forEach>
			</select></td>
			<td width="90"><select cssClass="htmlTextAlpha"
				name="matchQualityD" style="width:90px" tabindex="6">
    			<c:forEach items="${requestScope.qualityActionColl}" var="action">
				   <option
					  value="${action.value}"
					  <c:if test="${requestScope.matchQualityD == action.value}">selected="selected"</c:if>>
					  ${action.label}
				   </option>
				</c:forEach>
			</select></td>
			<td width="87"><select cssClass="htmlTextAlpha"
				name="matchQualityE" style="width:87px" tabindex="7">
    			<c:forEach items="${requestScope.qualityActionColl}" var="action">
				   <option
					  value="${action.value}"
					  <c:if test="${requestScope.matchQualityE == action.value}">selected="selected"</c:if>>
					  ${action.label}
				   </option>
				</c:forEach>
			</select></td>
		</tr>
	</table>
	</div>
	</div>

	<div id="MatchQualityAdd"
		style="position: absolute; left: 684; top: 380; width: 70px; height: 15px; visibility: visible;">

	<table width="60px" cellspacing="0" cellpadding="0" height="20"
		border="0">
		<tr>

			<td align="Right"><c:if test="${requestScope.methodName == 'add'}">
				<a tabindex="13" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Match Quality Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if> <c:if test="${requestScope.methodName == 'change'}">
				<a tabindex="13" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Match Quality Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if> <c:if test="${requestScope.methodName == 'view'}">
				<a tabindex="13" href=#
					onclick="javascript:openWindow(buildPrintURL('print','View Match Quality Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if></td>

			<td align="right" valign="middle" id="Print"><a tabindex="13"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif" name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 375; width: 739px; height: 39px; visibility: visible;">

	<div id="MatchQualityAdd"
		style="position: absolute; left: 5; top: 4; width: 555px; height: 15px; visibility: visible;">
	<table width="210" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<c:if test="${requestScope.methodName == 'change'}">
				<td width="70"><a title='<fmt:message key="tooltip.save"/>'
					tabindex="8" onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="submitForm('update')"><fmt:message key="button.save" /></a></td>

			</c:if>

			<c:if test="${requestScope.methodName == 'add'}">
				<td width="70"><a
					title='<fmt:message key="tooltip.cpyMatchQuality"/>' tabindex="9"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="javascript:openWindow(submitCopyFrom('copyFrom'),'matchqualitymaintenanceaddWindow','left=170,top=210,width=529,height=143,toolbar=0, resizable=yes, scrollbars=yes', true)"><fmt:message key="button.cpyFrom" /></a></td>

				<td width="70"><a title='<fmt:message key="tooltip.save"/>'
					tabindex="10" onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="submitForm('save')"><fmt:message key="button.save" /></a></td>
			</c:if>

			<td width="70"><c:if test="${requestScope.methodName == 'view'}">
				<a title='<fmt:message key="tooltip.close"/>' tabindex="11"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="confirmClose('C');"><fmt:message key="button.close" /></a>
			</c:if> <c:if test="${requestScope.methodName != 'view'}">
				<a title='<fmt:message key="tooltip.cancel"/>' tabindex="11"
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="confirmClose('C');"><fmt:message key="button.cancel" /></a>
			</c:if></td>

			<c:if test="${requestScope.methodName == 'change'}">
				<td width="70">&nbsp;</td>
			</c:if>
		</tr>
	</table>
	</div>
	</div>
	<!-- End:Code Modified for Manits 1997 by chinniah on 16-Jul-2012: Remove functionality that disable 'Amount Total' checkbox -->
</form>
</body>
</html>
