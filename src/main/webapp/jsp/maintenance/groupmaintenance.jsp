<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>

<%@page import="org.swallow.maintenance.model.Group"%>
<html>
<head>
<title><fmt:message key="groupmaintenance.title.MainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();

	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	</c:if>

var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="closebutton";

var entityAccess = "${requestScope.EntityAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
	
function bodyOnLoad() {
	xl = new XLSheet("groupColl", "table_2", ["Number", "String", "String", "String", "String", "Number"], "211111");
	xl.onsort = xl.onfilter = onFilterandSort;

	highlightTableRows("groupColl");

	var dropBox1 = new SwSelectBox(document.forms[0].elements["group.id.entityId"], document.getElementById("entityName"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["group.groupLvlCode"], document.getElementById("grpLvlName"));

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	var buttonStatus = "<%=request.getAttribute(SwtConstants.ADD_BUT_STS)%>";

	if (entityAccess == 0) {
		document.getElementById("addbutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("addenablebutton").innerHTML : document.getElementById("adddisablebutton").innerHTML);
	} else {
		document.getElementById("addbutton").innerHTML = (buttonStatus != "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("adddisablebutton").innerHTML : document.getElementById("addenablebutton").innerHTML);

	}

	buttonStatus = "<%=request.getAttribute(SwtConstants.CHG_BUT_STS)%>";
	document.getElementById("changebutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("changeenablebutton").innerHTML : document.getElementById("changedisablebutton").innerHTML);

	buttonStatus = "<%=request.getAttribute(SwtConstants.DEL_BUT_STS)%>";
	document.getElementById("deletebutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("deleteenablebutton").innerHTML : document.getElementById("deletedisablebutton").innerHTML);

	document.getElementById("bookcodebutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("bookcodeenablebutton").innerHTML : document.getElementById("bookcodedisablebutton").innerHTML);

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	<c:if test="${requestScope.disableAdd == 'Y'}">
	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	</c:if>
}

	function submitForm(methodName) {

		document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
	if (yourstate==true){ 
	
	document.forms[0].submit();
	
	}
}

function buildChangeGroupURL(methodName){
	var groupId = new String(document.forms[0].selectedgroupCode.value);
	var len = groupId.length;

	for(var i = 0; i<len;i++)
	{
		groupId = groupId.replace('%',' '); // replacing all '%' by ' ' i.e the space character
	}
	
	for(var i = 0; i<len;i++)
	{
		groupId = groupId.replace(' ',escape('%')); //now replacing all spaces by the escape code for that character
	}

	for(var i = 0; i<len;i++)
	{
		groupId = groupId.replace('~',escape('~'));
		groupId = groupId.replace('#',escape('#'));
		groupId = groupId.replace('!',escape('!'));
		groupId = groupId.replace('@',escape('@'));
		groupId = groupId.replace('$',escape('$'));
		
		groupId = groupId.replace('^',escape('^'));
		groupId = groupId.replace('&',escape('&'));
		
		groupId = groupId.replace('(',escape('('));
		groupId = groupId.replace(')',escape(')'));
		groupId = groupId.replace('-',escape('-'));
		groupId = groupId.replace('_',escape('_'));
		groupId = groupId.replace('=',escape('='));
		groupId = groupId.replace('+',encodeURIComponent('+'));
		groupId = groupId.replace('[',escape('['));
		groupId = groupId.replace(']',escape(']'));
		groupId = groupId.replace(':',escape(':'));
		groupId = groupId.replace(';',escape(';'));
		
		groupId = groupId.replace('"',escape('"'));
		groupId = groupId.replace(',',escape(','));
		groupId = groupId.replace('<',escape('<'));
		groupId = groupId.replace('.',escape('.'));
		groupId = groupId.replace('>',escape('>'));
		groupId = groupId.replace('/',escape('/'));
		groupId = groupId.replace('?',escape('?'));
	}
	
	var param = 'group.do?method='+methodName+'&entityCode=';
	param +=document.forms[0].elements['group.id.entityId'].value;	
	param +='&entityName=';
	param +=document.getElementById("entityName").innerText;	
	param +='&groupId=';
	param +=groupId;
 		
	param +='&groupName=';
	param +=escape(encodeURIComponent(document.forms[0].selectedgroupName.value));
	
	param +='&cutoffOffset=';
	param +=document.forms[0].selectedgroupcutoffOffset.value; 
	param +='&gLevel=';
	param +=document.forms[0].elements['group.groupLvlCode'].value;
	param +='&grpLvlName=';
	param +=document.getElementById("grpLvlName").innerText;

return  param;
}
/* This Method is used for save or update the data and close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "add"){
		openWindow(buildChangeGroupURL('add'),'groupmaintenanceaddWindow','left=50,top=190,width=615,height=233,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else if(method == "change"){
		openWindow(buildChangeGroupURL('change'),'groupmaintenanceaddWindow','left=50,top=190,width=615,height=233,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else if(method == "close"){
		confirmClose('P');
		}else if(method == "bookcodes")
		{
		openWindow(buildChangeGroupURL('bookcodes'),'groupmaintenancebookcodesWindow','left=50,top=190,width=488,height=518,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else if(method == "delete"){
			submitDeleteForm('delete');
		}
	}
}
	
function onSelectTableRow(rowElement, isSelected)
{
	
	
	var hiddenElement = rowElement.cells[0].innerText;
	document.forms[0].selectedgroupCode.value =  new String(hiddenElement).trim().valueOf();
	
	var hiddenElement = rowElement.cells[1].innerText;
	document.forms[0].selectedgroupName.value = new String(hiddenElement).trim().valueOf();
		
	var hiddenElement = rowElement.cells[2].innerText;
	document.forms[0].selectedmgroupId.value = new String(hiddenElement).trim().valueOf();
	
	var hiddenElement = rowElement.cells[3].innerText;
	document.forms[0].selectedgroupcutoffOffset.value =  new String(hiddenElement).trim().valueOf();
		 
	var menuEntityCurrGrpAccess = document.forms[0].menuAccessId.value;
	  
	if(menuEntityCurrGrpAccess == 0 && entityAccess == 0 &&  isSelected)
	{
		 
	
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		if(rowElement.cells[4].innerText== 0){
		if(isSelected)
			document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML;
		}else
		document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodeenablebutton").innerHTML;
	}
	else
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
		document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML;
	}
	
	

	}

</SCRIPT>	
</head>

<form action="group.do">
<input name="method" type="hidden" value="display">		
<input name="selectedgroupCode" type="hidden" value="">
<input name="selectedgroupName" type="hidden" value="">		
<input name="selectedgroupLvlCode" type="hidden" value="">
<input name="selectedmgroupId" type="hidden" value="">		
<input name="selectedgroupcutoffOffset" type="hidden" value=""> 

<input name="menuAccessId" type="hidden" >
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus(); setFocus(document.forms[0]); ShowErrMsgWindow('${actionError}');" onunload="call()">

<div id="Group" style="position:absolute; left:20px; top:20px; width:744px; height:60px; border:2px outset;" color="#7E97AF">
	<div id="Group" style="position:absolute; left:8px; top:4px; width:450px; height:25;">
		<table width="510" border="0" cellpadding="0" cellspacing="0" height="25">
			<tr height="24">
				<td width="30px"><b><fmt:message key="bookCode.entity"/></b></td>
					  <td width="28px">&nbsp;</td>
					  <td width="140px">
						  <select class="htmlTextAlpha" name="group.id.entityId" titleKey="tooltip.selectEntityId" onchange="submitForm('displayList')" style="width:140px" tabindex="1">
							<c:forEach items="${requestScope.entities}" var="entity">
								<option
								  value="${entity.value}"
								  <c:if test="${group.id.entityId == entity.value}">selected="selected"</c:if>>
								  ${entity.label}
							   </option>
							</c:forEach>
						  </select>
					  </td>
					  <td width="20">&nbsp;</td>
					  <td width="280">
						<span id="entityName" name="entityName" class="spantext">
					  </td>
				
			</tr>
			<tr height="24">
				  <td width="30px"><b><fmt:message key="metaGroup.mgrpLvlCode"/></b></td>
					  <td width="28px">&nbsp;</td>
					  <td width="140px">
						  <select class="htmlTextAlpha" name="group.groupLvlCode" titleKey="tooltip.selectlevel" onchange="submitForm('displayList')" style="width:140px" tabindex="1">
    			      		<c:forEach items="${requestScope.groupLevel}" var="grpLevel">
								<option
								  value="${grpLevel.value}"
								  <c:if test="${group.groupLvlCode == grpLevel.value}">selected="selected"</c:if>>
								  ${grpLevel.label}
							   </option>
							</c:forEach>
						  </select>
					  </td>
					  <td width="20">&nbsp;</td>
					  <td width="280">
						<span id="grpLvlName" name="grpLvlName" class="spantext">
					  </td>
				
			</tr>
	</table>
</div>
</div>

<div id="Group" style="position:absolute; border:2px outset; left:20px; top:83px; width:744px; height:429;">
<div id="Group" style="position:absolute;z-index:99;left:0px; top:0px; width:723px; height:10px;">
<table  class="sort-table" id="table_2"  width="720" border="0" cellspacing="1" cellpadding="0" >
	<thead>
		<tr height="20px">
			<td width="120px" height="20px" align="center"  style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortGroupId"/>'><b><fmt:message key="group.groupId"/></b></td>
			<td width="280px" height="20px" align="center" title='<fmt:message key="tooltip.sortGroupName"/>'><b><fmt:message key="group.groupName"/></b></td>	
			<td width="120" height="20px" align="center" title='<fmt:message key="tooltip.sortMetaGroupId"/>'><b><fmt:message key="group.mgroupId"/></b></td>
			<td width="100" height="20px" align="center" title='<fmt:message key="tooltip.sortCutOffOffset"/>'><b><fmt:message key="group.cutoffOffset"/></b></td>
			<td title='<fmt:message key="tooltip.sortTotalNoBooks"/>' width="100px" height="20px" align="center"><b><fmt:message key="metagroup.groupNameTotal"/></b></td>
		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:741px; height:425px; overflowY:auto">
<div id="Group" style="position:absolute;z-index:99;left:1px; top:22px; width:723px; height:10px;">
<table class="sort-table" id="groupColl" width="720" height="403px" border="0" cellspacing="1" cellpadding="0" >
	<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="group" items="${requestScope.groupColl}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <!-- Start: Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580: "Spaces should not be saved to end of input values." -->
            <input type="hidden" name="mgroupId" value="${group.mgroupId}" disabled="true" />
            <input type="hidden" name="id.groupId" value="${group.id.groupId}" disabled="true" />
            <td width="120px" align="left">${group.id.groupId}&nbsp;</td>
            <input type="hidden" name="groupName" value="${group.groupName}" disabled="true" />
            <td width="280px" align="left">${group.groupName}&nbsp;</td>
            <!-- End: Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580: "Spaces should not be saved to end of input values." -->
            <input type="hidden" name="groupLvlCode" value="${group.groupLvlCode}" disabled="true" />
            <td width="120px" align="left">${group.mgroupId}&nbsp;</td>
            <input type="hidden" name="cutoffOffset" value="${group.cutoffOffset}" disabled="true" />
            <td width="100px" align="center">${group.cutoffOffset}&nbsp;</td>
            <td width="98px" align="center">${group.noOfBookCode}&nbsp;</td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>
	<tfoot><tr><td colspan="5" ></td></tr></tfoot>
</table>
</div>
</div>
</div>
<div id="Group" style="position:absolute; left:688; top:523px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">
				<td align="Right">
					 <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Group Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
			  </td>

			<td align="right" id="Print">
				<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:516; width:744px; height:39px; visibility:visible;">
<div id="Group" style="position:absolute; left:6; top:4; width:742px; height:15px; visibility:visible;">
  <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"></td>
		<td id="changebutton" width="70"></td>
		<td id="deletebutton" width="70"></td>
		<td id="bookcodebutton" width="70"></td>

		<td id="closebutton" width="70">		
			<a title='<fmt:message key="tooltip.close"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" onKeydown = "onKeyEnterSubmit('close',event);"><fmt:message key="button.close"/></a>			
		</td>
		
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:683px; height:15px; visibility:hidden;">  	
<table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
<tr>

    <td id="addenablebutton">		
		<a title='<fmt:message key="tooltip.addNewGroup"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeGroupURL('add'),'groupmaintenanceaddWindow','left=50,top=190,width=615,height=245,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('add',event);"><fmt:message key="button.add"/></a>
	</td>		
	<td id="adddisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.addNewGroup"/>'><fmt:message key="button.add"/></a>
	</td>
	<td id="changeenablebutton">		
		<a title='<fmt:message key="tooltip.changeSelGroup"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeGroupURL('change'),'groupmaintenanceaddWindow','left=50,top=190,width=615,height=245,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('change',event);"><fmt:message key="button.change"/></a>
	</td>		
	<td id="changedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.changeSelGroup"/>'><fmt:message key="button.change"/></a>
	</td>
	<td id="deleteenablebutton">		
		<a title='<fmt:message key="tooltip.deleteSelGroup"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete');" onKeydown = "onKeyEnterSubmit('delete',event);"><fmt:message key="button.delete"/></a>
	</td>		
	<td id="deletedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.deleteSelGroup"/>'><fmt:message key="button.delete"/></a>
	</td>
	<td id="bookcodeenablebutton">
	<a title='<fmt:message key="tooltip.viewBookSelGroup"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:openWindow(buildChangeGroupURL('bookcodes'),'groupmaintenancebookcodesWindow','left=50,top=190,width=488,height=518,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('bookcodes',event);"><fmt:message key="button.books"/></a>									
	</td>		
	<td id="bookcodedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.viewBookSelGroup"/>'><fmt:message key="button.books"/></a>
	</td>

</tr>
</table>
</div>
</div>

</form>
</body>
</html>