<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@page import="org.swallow.maintenance.model.AccountInterestRate"%>
<html>
<head>
<title><fmt:message key="title.rates" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script language="JAVASCRIPT" type="text/javascript">
var screenStatus = '${requestScope.screenStatus}'


function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function enableAllButtons()
{
	<c:if test="${'view'!= requestScope.screenStatus}">
	document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML; 
	</c:if>
}
function onFilterandSort()
{
	updateColors();
	<c:if test="${'view'!= requestScope.screenStatus}">
		disableAllButtons();
	</c:if>
	

}

function submitFormDelete(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 
	
	document.forms[0].submit();
	
	}
}
function bodyOnLoad()
{
	
	highlightTableRows("accInterestRatecoll");
	
	if( screenStatus == null || screenStatus == ""){
		 screenStatus == window.opener.methodName;
	}
	<c:if test="${'view'!= requestScope.screenStatus}">
	 	document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		disableAllButtons();
	</c:if>
	
	document.forms[0].relatedToaccountId.value = window.opener.document.forms[0].elements['acctMaintenance.id.accountId'].value;
	document.forms[0].callerMethod.value = window.opener.methodName;
}

function submitForm(methodName){
	 
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

/** 
function onSelectTableRow

Calls onSelectTableRow when click the record in the data grid. 
Gets the values of the record properties.

**/ 
function onSelectTableRow(rowElement, isSelected) {
	// check the record is selected
    if (isSelected) {
    	// Enable the buttons
        enableAllButtons();
        // Gets the record property values
        var hiddenElement = rowElement.cells[0].innerText;
        document.forms[0].interestRateDate.value = new String(hiddenElement).trim().valueOf();        
        var hiddenElement = rowElement.cells[1].innerText;
        document.forms[0].creditInterestRate.value = new String(hiddenElement).trim().valueOf();
        var hiddenElement = rowElement.cells[2].innerText;
        document.forms[0].overdraftInterestRate.value = new String(hiddenElement).trim().valueOf();
        var interestDateValue = document.forms[0].interestRateDate.value;
        var creditValue = document.forms[0].creditInterestRate.value;
        var overdraftValue = document.forms[0].overdraftInterestRate.value;
    }
    // Start : Nithiyananthan 02/01/2012 Mantis 1659
    else {
    	// If record is not selected then disable the buttons.
        disableAllButtons();
    }
    // End : Nithiyananthan 02/01/2012 Mantis 1659
}
	
function buildAddAccIntRateURL(methodName){
	
	document.forms[0].method.value = methodName;
   var	param = 'acctMaintenance.do?method='+methodName;
	
  return param;

	  
	}	


function addInterestRate(methodName){
	document.forms[0].method.value = methodName;
   var	param = 'acctMaintenance.do?method='+methodName;	
	param +='&creditIntRate='+document.forms[0].creditInterestRate.value;
	param +='&overdraftIntRate=';
	param +=document.forms[0].overdraftInterestRate.value;
	param +='&interestRateDate=';
	param +=document.forms[0].interestRateDate.value;	

  return param;
} 

function submitFormDelete(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 
	
	document.forms[0].submit();
	
	}
}

function refreshParentWindow(methodName) {

var table = document.getElementById("accInterestRatecoll");
var tbody = table.getElementsByTagName("tbody")[0];  
var rows = tbody.getElementsByTagName("tr");

if(typeof rows[0] != 'undefined') {  // Enter block, if contains atleast one row  
var accIntRateDateASString= rows[0].cells[0].innerText;
var accIntCreditRate= rows[0].cells[1].innerText;
var accIntOverDraftRate= rows[0].cells[2].innerText;


window.opener.document.forms[0].elements["acctMaintenance.latestCreditRate"].value = accIntCreditRate;
window.opener.document.forms[0].elements["acctMaintenance.latestOverDraftRate"].value = accIntOverDraftRate;
window.opener.document.forms[0].elements["acctMaintenance.latestInterestDateRate"].value =accIntRateDateASString;
} else {
	window.opener.document.forms[0].elements["acctMaintenance.latestCreditRate"].value = "";
window.opener.document.forms[0].elements["acctMaintenance.latestOverDraftRate"].value = "";
window.opener.document.forms[0].elements["acctMaintenance.latestInterestDateRate"].value = "";
}

self.close();

}

</script>

</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="call()">

<form action="acctMaintenance.do">

	<input name="method" type="hidden" value="">
	<input name="creditInterestRate" type="hidden" value="">
	<input name="overdraftInterestRate" type="hidden" value="">
	<input name="interestRateDate" type="hidden" value="">
	<input name="relatedToaccountId" type="hidden" value="">
	<input name="callerMethod" type="hidden" value="">
	<div id="AccInterestRate" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 20px; top: 20px; width: 755px; height: 375px;">
	<div id="AccInterestRateHeader"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 765px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="735px"
		border="0" cellspacing="1" cellpadding="0" height="20px">
		<thead>
			<tr align="center">
				<td title='<fmt:message key="tooltip.accIntRateDate"/>' width="80px" style="border-left-width: 0px;"
					height="20px"><b><fmt:message key="accIntRate.Date" /></b></td>
				<td title='<fmt:message key="tooltip.accIntCredit"/>' width="180px"
					height="20px"><b><fmt:message key="accIntRate.Credit" /></b></td>
				<td title='<fmt:message key="tooltip.accIntRateOverDraft"/>'
					width="180px" height="20px"><b><fmt:message key="accIntRate.OverDraft" /></b></td>
				<td title='<fmt:message key="tooltip.accIntRateUpdateDate"/>'
					width="150px" height="20px"><b><fmt:message key="accIntRate.UpdateDate" /></b></td>
				<td title='<fmt:message key="tooltip.accIntRateUpdateUser"/>'
					width="100px" height="20px"><b><fmt:message key="accIntRate.UpdateUser" /></b></td>

			</tr>
		</thead>
	</table>
	</div>
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 752; height: 371px">
	<div id="paryMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 735; height: 10px;">
	<table class="sort-table" id="accInterestRatecoll" width="735px"
		border="0" cellspacing="1" cellpadding="0" height="348px">
				<tbody>
			<%
				int count = 0;
			%>

			<c:forEach items="${requestScope.interestRateColl}" var='interestRateColl' >
				<%
					if (count % 2 == 0) {
				%><tr class="even">
					<%
						} else {
					%>
				</tr>
				<tr class="odd">
					<%
						}
							++count;
					%>
					<c:set var="interestRateColl" value="${interestRateColl}"/>
					<jsp:useBean id="interestRateColl" class="org.swallow.maintenance.model.AccountInterestRate" />
					<%
							// Gets the AccountInterestRate from list
							AccountInterestRate acctInterestRate = (AccountInterestRate) interestRateColl;
							// Checks the decimal point index, if it is zero then adds zero in front of the value
								acctInterestRate.setCreditRate(SwtUtil.formatPrecisionDecimal(acctInterestRate.getCreditRate()));

								acctInterestRate.setOverdraftRate(SwtUtil.formatPrecisionDecimal(acctInterestRate.getOverdraftRate()));

					%>

					<td align="right" width="80px">${interestRateColl.interestDateRateAsString}&nbsp;</td>
					<td align="right" width="180px">${interestRateColl.creditRate}&nbsp;</td>
					<td align="right" width="180px">${interestRateColl.overdraftRate}&nbsp;</td>
					<td align="right" width="150px">${interestRateColl.updateDateAsString}&nbsp;</td>
					<td align="center" width="100px">${interestRateColl.updateUser }&nbsp;</td>

				</tr>
			</c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="5"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>

	<div id="sectionMain"
		style="position: absolute; left: 700; top: 407px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><a tabindex="6" href="#"
				onclick="javascript:openWindow(buildPrintURL('print','Account InterestRate'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>' alt=""></a></td>

			<td align="right" id="Print"><a tabindex="5" tabindex="6"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>' alt=""></a></td>
		</tr>
	</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 22; top: 400; width: 753; height: 39px; visibility: visible;">
	<div id="CustodianMaintenance"
		style="position: absolute; left: 6; top: 4; width: 739; height: 15px; visibility: visible;">
	<c:if test="${'view'== requestScope.screenStatus}">
		<table width="70" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td id="closebutton"><a tabindex="1"
					title='<fmt:message key="tooltip.close"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="confirmClose('P');"><fmt:message key="button.close" /></a>
				</td>
			</tr>
		</table>
	</c:if> <c:if test="${'view'!= requestScope.screenStatus}">
		<table width="280" border="0" cellspacing="0" cellpadding="0"
			height="20">
			<tr>
				<td id="addbutton"></td>
				<td id="changebutton"></td>
				<td id="deletebutton"></td>
				<td id="closebutton"><a tabindex="4"
					title='<fmt:message key="tooltip.close"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
					onclick="refreshParentWindow() ;"><fmt:message key="button.close" /></a></td>
			</tr>
		</table>
	</c:if></div>
	<div
		style="position: absolute; left: 6; top: 4; width: 712; height: 15px; visibility: hidden;">
	<table width="280" border="1" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="addenablebutton"><a tabindex="2"
				title='<fmt:message key="button.ratesAdd"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildAddAccIntRateURL('addAcctInterestRate'),'accountInterestRateAddWindow','left=50,top=190,width=420,height=195,toolbar=0','true')"><fmt:message key="button.add" /></a></td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="button.ratesAdd"/>'><fmt:message key="button.add" /></a></td>

			<td id="changeenablebutton"><a tabindex="3"
				title='<fmt:message key="button.ratesChange"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(addInterestRate('changeAcctInterestRate'),'accountInterestRateAddWindow','left=50,top=190,width=420,height=195,toolbar=0','true')"><fmt:message key="button.change" /></a></td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="button.ratesChange"/>'><fmt:message key="button.change" /></a></td>

			<td id="deleteenablebutton"><a tabindex="4"
				title='<fmt:message key="button.ratesDelete"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitFormDelete('deleteAcctInterestRate')"><fmt:message key="button.delete" /></a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="button.ratesDelete"/>'><fmt:message key="button.delete" /></a></td>

		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>


	<script type="text/javascript">
</script>
</form>
</body>
</html>