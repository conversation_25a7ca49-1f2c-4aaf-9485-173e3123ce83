<?xml version="1.0" encoding="UTF-8"?>
<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>



<accoutattributehdr>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<singletons>
		<c:if test="${requestScope.screenName != 'addScreen'}">
			<attributeid>${requestScope.accountAttributeHDR.id.attributeId}</attributeid>
			<attibutename>${requestScope.accountAttributeHDR.attributeName}</attibutename>
			<tooltip>${requestScope.accountAttributeHDR.tooltipText}</tooltip>
			<effectivedaterequired>${requestScope.accountAttributeHDR.effectiveDateRequired}</effectivedaterequired>
			<effectivedateallowtime>${requestScope.accountAttributeHDR.effectiveDateAllowTime}</effectivedateallowtime>
			<minvalue>${requestScope.minValue}</minvalue>
			<maxvalue>${requestScope.maxValue}</maxvalue>
			<minlength>${requestScope.accountAttributeHDR.validateTextMinLen}</minlength>
			<maxlength>${requestScope.accountAttributeHDR.validateTextMaxLen}</maxlength>
			<regexvalidation>${requestScope.accountAttributeHDR.validateTextRegex}</regexvalidation>
			<validationmessage>${requestScope.accountAttributeHDR.validateTextRegexMsg}</validationmessage>
			<dateValue>${requestScope.accountAttributeHDR.validateDateAllowTime}</dateValue>
			<currencyformat>${requestScope.currencyFormat}</currencyformat>
		</c:if>
	</singletons>

	<selects>
		<select id="types">
			<c:forEach var="types" items="${requestScope.typesList}">
				<option value="${types}" selected="${requestScope.selectedType == types ? '1' : '0'}">
				</option>
			</c:forEach>
		</select>
	</selects>
</accoutattributehdr>
