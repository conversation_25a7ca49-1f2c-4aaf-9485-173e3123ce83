<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>4-Eyes Review - SMART-Predict</title>
	<!-- <base href="./"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body >
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
	var appName = "<%=SwtUtil.appName%>";
	var baseURL = new String('<%=request.getRequestURL()%>');
	var screenRoute = "MaintenanceEvent";

	var menuAccessId = '${requestScope.menuAccessId}';

	function buildMaintenanceDetailsURL(methodName,maintEvent){
		var	param = 'maintenanceEvent.do?method=maintenanceEventDetails';
		param += '&maintEvent='+ maintEvent;
		var 	mainWindow = openWindow (param, 'maintenanceDetails','left=10,top=230,width=1100,height=650,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}


	function buildChangeAccIntRateURL(methodName, parentMethod, accountId, interestRateDate, creditIntRate, overdraftIntRate){
		var	param = 'acctMaintenance.do?method='+methodName;
		param += '&parentMethod='+ parentMethod;
		param += '&accountId='+ accountId;
		param += '&interestRateDate='+ interestRateDate;
		param += '&creditIntRate='+ creditIntRate;
		param += '&overdraftIntRate='+ overdraftIntRate;

		var 	mainWindow = openWindow (param, 'acctInterestRateChange','left=10,top=230,width=350,height=170,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
		return false;
	}


</script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>