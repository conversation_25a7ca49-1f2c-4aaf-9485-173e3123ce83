<!DOCTYPE HTML>

<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html>
<head>
	<title>EmailTemplate Maintenance- SMART-Predict</title>
<title>
    <c:choose>
        <c:when test="${requestScope.screenName == 'add'}">
            <fmt:message key="emailtemplate.addScreenTitle"/>
        </c:when>
        <c:when test="${requestScope.screenName == 'change'}">
            <fmt:message key="emailtemplate.changeScreenTitle"/>
        </c:when>
    </c:choose>
</title>

<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "EmailTemplateMaintenanceDetails";
 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';;
	var menuAccessId = "${requestScope.menuAccessId}";
	var params = "${requestScope.params}";
	var screenName = "${requestScope.screenName}";


</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">


<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
				value="<fmt:message key="EmailTemplateMaintenance.title.window"/>" /></form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
		</body>
		</html>

