<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<%
	//variable declaration
	String params = "";
	//get the all params from request attribute 
	if (request.getAttribute("params") != null) {
		params = request.getAttribute("params")
				.toString();
	}	
%>
<html>
<head>
<title>Account Currency Maintenance Period VIEW LOG - SMART-Predict</title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<SCRIPT language="JAVASCRIPT">
    var params = "<%= params %>";
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>"; 
	var idy = requestURL.indexOf('/'+appName+'/');	
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "AcctCcyPeriodMaintViewLog";

 	/**
  	*	This section is used to handle calender button on the screen and is used to set the position of the same.
  	*/
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var menuAccessId = "${requestScope.menuAccessId}";
	

	
</SCRIPT>


<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">


<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
<input type="hidden" name="data" id="exportData" /> <input
	type="hidden" name="screen" id="exportDataScreen"
	value="<fmt:message key="PreAdviceInput.title.window"/>" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
