<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.CurrencyExchangePageSummary"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@page import="org.swallow.maintenance.model.CurrencyExchange"%>
<html>
<head>
<title><fmt:message key="exchange.mainTitle" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<script type="text/javascript" src="js/jquery.exportselect.js"></script>

<script language="javascript">


var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;




var dateFlag=true;
mandatoryFieldsArray = ["exchangeRateFromDate","exchangeRateToDate"] ;

var currencyCode='${requestScope.currencyCode}';
var exchangeRateFromDateAsString='${requestScope.exchangeRateFromDateAsString}';
var exchangeRateToDateAsString='${requestScope.exchangeRateToDateAsString}';

var currentFilter = "${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var selectedMvmtList="${requestScope.selectedList}";
var nextMovIdFocus="${requestScope.nextMovIdFocus}";
var filterCriteria = "${requestScope.filterCriteria}";
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';

var isRefresfFromChild = "false";
var totalCount = '${totalCount}';
var currentFilterValues = currentFilter.split("|");
var sortingValues = currentSort.split("|");
var filterValues=new Array();
var calledFromNext;
var sortedValues = new Array();
sortedValues[0] = sortingValues[0];
sortedValues[1] = sortingValues[1];
var cancelcloseElements = new Array(1);
cancelcloseElements[0]="closebutton";


var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";


var entityAccess = "${requestScope.EntityAccess}";
var dateSelected = false;


var dateFormat = '${sessionScope.CDM.dateFormat}';
var fromDateSelected = false;



function sortDateValidation() {
if(validateForm(document.forms[0]) ){
	if(validateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat)){
	if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')) {
		dateFlag = true;
		return dateFlag;
	} else {
		dateFlag = false;
		return dateFlag;
		}
		} else {
			dateFlag = false;
			return dateFlag;
		}
	}
}


function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}
function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function optionClick_server_filter_JSP(index,value,action)
{

	var filterValue ="";
	if(action == "filter"){
	value = replace(value,'&nbsp;',' ');
	
	if(currentFilter =="all" || currentFilter=="undefined"){
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
			if(idx == index)
				filterValue +=  value + "|";
			else
				filterValue +=  "All" + "|";
		}
	}else{          
		var filter=currentFilter.split("|");
		filter[index]=value;
		for(var idx = 0 ; idx < xl.numColumns ; ++idx)
		{
			filterValue+=filter[idx] + "|";
		}
	}	
	document.forms[0].selectedSort.value=currentSort;	
	document.forms[0].selectedFilter.value =filterValue ;
}else{
	var	sortColum=index;
	var sortDesc=value;
	filterValue = "${requestScope.filterCriteria}";
	document.forms[0].selectedSort.value=sortColum + "|" +sortDesc + "|";
	document.forms[0].selectedFilter.value =currentFilter ;
	}
	document.forms[0].currencyCode.value = document.forms[0].elements['currencyexchange.id.currencyCode'].value;	
	document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	getSelectedList();	
	document.forms[0].method.value = "displayList";
	document.forms[0].filterFromSerach.value = "true";    
	document.forms[0].currentPage.value = 1;	
	document.forms[0].filterCriteria.value=filterValue;
	document.forms[0].submit();
}

function exportData(exportType) {

	if(validateForm(document.forms[0]) ){
		if(validateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')) {

				var currPage = '${requestScope.currentPage}';			
				document.forms[0].method.value = 'exportCurrencyExchange';
				document.forms[0].exportType.value = exportType.trim();
				document.forms[0].currentPage.value = currPage;	
				if(record()> 0)
				{
				document.forms[0].submit();
				}

			}
		}
	}

}
/**
	 * This method is used to load the intial screen setup.
	  * @return none
 	 * 
	 */
function bodyOnLoad()
{
	var dateFormat = '${sessionScope.CDM.dateFormat}';
	xl = new XLSheet("currencyExchangeList","table_2", ["String","String",dateFormat,"Number",dateFormat,"String"],"121222","true",currentFilterValues,sortedValues);
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyexchange.id.entityId"],document.getElementById("entityName"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["currencyexchange.id.currencyCode"],document.getElementById("currencyName"));
    var defaultSortingOnValueDate = true;
	var isFilterSortStatus = true;
	 if(filterstatus !="")
	 {
		var filterStatus1 ;
		filterStatus1 = filterstatus.split(",");
		if(typeof xl != 'undefined' && xl != null && typeof xl.setFilterStatus != 'undefined')
			xl.setFilterStatus(filterStatus1);
		isFilterSortStatus = true;
	 }
	if(sortDescending !="" && sortDescending !="null" && sortDescending == "true")
	{	
		xl.dataTable.defaultDescending = sortDescending

		isFilterSortStatus = true;
	}

	if(sortDescending !="" && sortDescending !="null" && sortDescending == "false")
	{	
	  	xl.dataTable.doManualSorting(sortStatus);	
		isFilterSortStatus = true;
	}
	if(nextMovIdFocus !="" && nextMovIdFocus !="-1")
	{ 
		xl.getNextScrollIntoView(nextMovIdFocus);
		isFilterSortStatus = true;
	}		
	xl.onsort = xl.onfilter = onFilter;		
	highlightTableRows("currencyExchangeList");	
	document.forms[0].totalCount.value = '${requestScope.totalCount}';
	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	if(menuEntityCurrGrpAccess == "0") {
  	<%if ( request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>
	}else {
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}

	 <%if ( !request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	 <%}%>

	 <%if ( request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}else{%>

		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( request.getAttribute(SwtConstants.DEL_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}else{%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	//Start:Create a Select Export :Code added by Med Amine
	var headerData = [];
	var dataprovider = new Array();
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);

	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);

	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);

	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
	if(totalCount==0)
	 $("#exportReport").disabled(true); 
	else
	 $("#exportReport").disabled(false); 

} 
function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return exportData(type.toLowerCase());
}

function submitForm(methodName)
{

	if(validateForm(document.forms[0]) ){
		document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;
		document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
		document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
		document.forms[0].elements['exchangeRateDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
						
		document.forms[0].method.value = 'displayList';	

		document.forms[0].submit();
	}
	
}

function submitFormEntity(methodName) {

	fromDateSelected = false;
	dateSelected = false;
	

	if(validateForm(document.forms[0]) ){
    clearDate();
	var fromExchangeDate=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;	
	var toExchangeDate=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;	

	document.forms[0].elements['currencyCode'].value='All'	

	document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	document.forms[0].elements['currentPage'].value="${requestScope.currentPage}";
	document.forms[0].filterCriteria.value = "All|All|All|All";
	document.forms[0].selectedFilter.value = "All|All|All|All";		
	document.forms[0].method.value = "displayList";
	document.forms[0].submit();
	}
	
}
function getSelectedList(){
	var table = document.getElementById("CurrencyExchange"); 
	
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";
	var columns;
	for (i=0; i < rows.length; i++) 
 	{
		selectedList = selectedList + rows[i].cells[2].innerText.trim()+",";
 	}  	
 	document.forms[0].selectedList.value = selectedList; 	
}
function submitFormDelete(methodName){

	dateSelected = false;
	fromDateSelected = false;
	var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat)) {		
	if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat)) {			
	if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){

	document.forms[0].method.value = methodName;	
    document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
	document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	document.forms[0].maxPages.value = '${requestScope.maxPage}';
	document.forms[0].selectedFilterStatus.value = "${requestScope.filterStatus}";	
	document.forms[0].filterCriteria.value = filterCriteria;	
	document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;
	document.forms[0].filterFromSerach.value = "true";  	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 	
		document.forms[0].submit();
	}

	} else{
		return false;
		}
	} else{			
		return false;
		}
	} else{		
		return false;
		}

}

function onCurrencyChange(methodName){

	fromDateSelected = false;
	dateSelected = false;

	if(validateForm(document.forms[0]) ){
    clearDate();
	var fromExchangeDate=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	var toExchangeDate=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;
	document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	document.forms[0].elements['currentPage'].value="${requestScope.currentPage}";
	document.forms[0].method.value = "displayList";
	document.forms[0].submit();
	}
}


function onSelectTableRow(rowElement, isSelected){

	if(rowElement && rowElement.cells.length == 6)
	{
		
		var cell1 = rowElement.cells[0];

		var cell2 = rowElement.cells[1];
		var cell3 = rowElement.cells[2];	
		var cell4 = rowElement.cells[3];		
		var hiddenEl1 = cell1.getElementsByTagName("input")[0];

		var hiddenEl2 = cell2.getElementsByTagName("input")[0];
		var hiddenEl3 = cell3.getElementsByTagName("input")[0];	
		var hiddenEl4 = cell4.getElementsByTagName("input")[0];	
	}
	var hiddenElement = rowElement.getElementsByTagName("input")[0];

	document.forms[0].selectedCurrencyCode.value = hiddenEl1.value;
	document.forms[0].selectedCurrencyName.value = hiddenEl2.value;
	document.forms[0].selectedExchangeDate.value = hiddenEl3.value; 
	document.forms[0].selectedExchangeRate.value = hiddenEl4.value;
    
	var flag=currencyAccessConfirm(hiddenEl1.value,document.forms[0].elements['currencyexchange.id.entityId'].value)
	if(flag=="true" && menuEntityCurrGrpAccess == "0" && isSelected)
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
}




function currencyAccessConfirm(currency,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/defaultacctmaintenance.do?method=currencyAccessConfirm";
	sURL = sURL + "&currency="+currency;
	sURL = sURL + "&entityId="+entity;
	oXMLHTTP.open( "POST", sURL, false );	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}


function onFilter(){
	unlockSelected();	
	updateColors();
}
function buildAddExchangeURL(methodName){

	dateSelected = false;
	fromDateSelected = false;

	var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat))
		{		
			if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat))
			{			
				if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date'))
				{
		
	   document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;
	   document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	   document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	   var param = 'currencyexchange.do?method='+methodName+'&entityCode=';
	   param +=document.forms[0].elements['currencyexchange.id.entityId'].value;	
 
	   param +='&entityName='+document.getElementById("entityName").innerText;
	   param+='&exchangeRateFromDateAsString='+document.forms[0].exchangeRateFromDateAsString.value;
	   param+='&exchangeRateToDateAsString='+document.forms[0].exchangeRateToDateAsString.value;	 
	   param+='&currencyCode='+document.forms[0].currencyCode.value;
	   param +='&currentPage=';
	   param +=currPage;
	   param +='&filterCriteria=';
	   param +="${requestScope.filterCriteria}";	
	   param +='&selectedSort=';
	   param +=document.forms[0].selectedSort.value;	
	   param +='&selectedFilter=';
	   param +=document.forms[0].selectedFilter.value;
	   param +='&maxPages=';
	   param +=maxPage;	 
	     
	   window.openWindow(param,'currencyexchangechildmaintenanceWindow','left=50,top=500,width=600,height=220,toolbar=0','true');
	   
		}else{
			return false;
			}
			}else{			
				return false;
			}
			}else{		
				return false;
			}	
	   	
		
	}
function buildChangeExchangeURL(methodName){

	dateSelected = false;
	fromDateSelected = false;

	var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat))
		{		
			if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat))
			{			
				if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date'))
				{
document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;
	var param = 'currencyexchange.do?method='+methodName+'&entityCode=';	
	    param +=document.forms[0].elements['currencyexchange.id.entityId'].value;
		param +='&entityName='+document.getElementById("entityName").innerText;
	    param +='&currencyId=';
	    param +=document.forms[0].selectedCurrencyCode.value;
	    param +='&currencyName=';
	    param +=document.forms[0].selectedCurrencyName.value;	
	    param +='&currentPage=';
	    param +=currPage;
	    param +='&filterCriteria=';
	    param +=document.forms[0].filterCriteria.value;	
	    param +='&selectedSort=';
	    param +=document.forms[0].selectedSort.value;	
	    param +='&selectedFilter=';
	    param +=document.forms[0].selectedFilter.value;
	    param+='&currencyCode='+document.forms[0].currencyCode.value;	
	    param +='&maxPages=';
	   	param +=maxPage;
		param+='&exchangeDate=';
		param+=document.forms[0].selectedExchangeDate.value;	
		param+='&exchangeRate=';	
		param+=document.forms[0].selectedExchangeRate.value;
	    param+='&exchangeRateFromDateAsString='+document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	    param+='&exchangeRateToDateAsString='+document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;	
	    

		window.openWindow(param,'currencyexchangechildmaintenanceWindow','left=50,top=190,width=600,height=220,toolbar=0','true');
		
	
		}else{
			return false;
			}
			}else{			
				return false;
			}
			}else{		
				return false;
			}	
}
 
function goToResultsPage(goToPageNo)
{
	var currPage = "${requestScope.currentPage}";	
	document.forms[0].method.value = "next";
	document.forms[0].goToPageNo.value=goToPageNo;
	document.forms[0].entityCode.value = document.forms[0].elements['currencyexchange.id.entityId'].value;
	document.forms[0].currencyCode.value = document.forms[0].elements['currencyexchange.id.currencyCode'].value;
	document.forms[0].exchangeRateFromDateAsString.value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
	document.forms[0].exchangeRateToDateAsString.value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	document.forms[0].currentPage.value=currPage;
	document.forms[0].filterFromSerach.value="true";
	document.forms[0].selectedSort.value=currentSort;
	document.forms[0].selectedFilter.value=currentFilter;
	document.forms[0].filterCriteria.value=document.forms[0].filterCriteria.value;
	document.forms[0].submit();
}


function clickLink(goToPageNo)
{

	fromDateSelected = false;
	dateSelected = false;
	
	if (document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value != "" && document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value != "") {
	if(validateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'fromDateAsString',dateFormat)){	
	if(validateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'toDateAsString',dateFormat)){	
	var currPage = "${requestScope.currentPage}";	
	/*  START:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */
    var baseUrl = '<%=SwtUtil.convertBeanToUrlParams((CurrencyExchangePageSummary)((ArrayList<CurrencyExchangePageSummary>)request.getAttribute("pageSummaryList")).get(0), "currencyExchangepageSummary") %>';
    var url='currencyexchange.do?'+baseUrl;   	
    url+='&entityCode='+document.forms[0].elements['currencyexchange.id.entityId'].value+'&currencyCode='+document.forms[0].elements['currencyexchange.id.currencyCode'].value;	
	url+='&exchangeRateFromDateAsString='+document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value+'&exchangeRateToDateAsString='+document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
	url+='&currentPage='+currPage+'&filterFromSerach=true&method=next&selectedSort='+currentSort+'&selectedFilter='+currentFilter+'&filterCriteria='+document.forms[0].filterCriteria.value;	
	url+='&goToPageNo='+goToPageNo+'&menuAccessId='+document.forms[0].menuAccessId.value;	
    submitFormFromURL(url,window); 
	/*  END:Modified by Mefteh Bouazizi for Mantis_1534 to convert GET URL to POST URL using a form */

	} else {		
		return false;
	}
	} else {		
		return false;
	}
	} else {
		alert("<fmt:message key='alert.enterValidDate'/>");
	}
}




function storeToDate(){	
document.forms[0].elements['preexchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;
}


 
<!--Checking TO Date should be greater than From date -->
function onDateChange(obj){	
	if(dateSelected)
		 {
		    if(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value != "" && document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value != ""){
			if (validateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'auditLog.to',dateFormat)) {
			cal2.hideCalendar()
			if(validateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'auditLog.from',dateFormat)){			
					var predata = document.forms[0].elements['preexchangeRateToDateAsString'].value;			
					document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;			
				    document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;		    
				    document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;		    
					var from_date=document.forms[0].elements['exchangeRateFromDateAsString'].value;
					var to_date=document.forms[0].elements['exchangeRateToDateAsString'].value;
					var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
					var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
					if(compare_date == 1)
					
					{
					document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value=predata;
					cal2.hideCalendar()
					alert("<fmt:message key='alert.interestCharges.toDate'/>");
					}else{
				    document.forms[0].elements['exchangeRateDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;		    			
				    document.forms[0].elements['exchangeRateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateAsString'].value;	
				    document.forms[0].method.value = 'displayList';		    		   	
				    document.forms[0].submit();
					}
			fromDateSelected = false;
			dateSelected = false;
			return true;
			} else {
					fromDateSelected = false;
					dateSelected = false;
					return false;
				}
			} else {
					fromDateSelected = false;
					dateSelected = false;
					return false;
				}			
		}else{
			alert("<fmt:message key='alert.enterValidDate'/>");
			dateSelected = false;
			return false;
			}
	} else {
		return false;
	}		
}


function onFromDateKeyPress(obj,e){
	fromDateSelected = false;
	dateSelected = false;
	var event = (window.event|| e);
	var obj2 = document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'];
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(obj2,'auditLog.to',dateFormat)){
		if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){
			submitForm('displayList');
			} else {				
				return false;
				}
			} else {				
				return false;
				}
		}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(obj2,'auditLog.to',dateFormat)){
			if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){
			submitForm('displayList');
			} else {				
				return false;
				}
			} else {				
				return false;
			}
		}
}



function onToDateKeyPress(obj,e){
	fromDateSelected = false;
	dateSelected = false;
	var event = (window.event|| e);
	var obj2 = document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'];
	if(event.keyCode == 9){   // tab
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(obj2,'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){
			submitForm('displayList');
			} else {				
				return false;
				}
			} else {				
				return false;
				}
		}
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(obj2,'auditLog.from',dateFormat)){
			if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){
			submitForm('displayList');
			} else {				
				return false;
				}
			} else {				
				return false;
			}
		}
}



function storeFromDate(){
document.forms[0].elements['preExchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;
}



function onFromDateChange(obj){
	if (fromDateSelected) {
	if(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value != "" && document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'fromDateAsString',dateFormat)){
		cal.hideCalendar()
		if (validateDateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'toDateAsString',dateFormat)) {
				var predata = document.forms[0].elements['preExchangeRateFromDateAsString'].value;
				document.forms[0].elements['currencyCode'].value=document.forms[0].elements['currencyexchange.id.currencyCode'].value;			
				document.forms[0].elements['exchangeRateFromDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;	
				document.forms[0].elements['exchangeRateToDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value;		    
				var from_date=document.forms[0].elements['exchangeRateFromDateAsString'].value;
				var to_date=document.forms[0].elements['exchangeRateToDateAsString'].value;
				var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
				var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
					if(compare_date == 1) {
					document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value=predata;
					cal.hideCalendar()
					alert("<fmt:message key='alert.interestCharges.toDate'/>");
					}else{					
						document.forms[0].elements['exchangeRateDateAsString'].value=document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value;		    			
						document.forms[0].method.value = 'displayList';		    		   	
						document.forms[0].submit();
					}
					fromDateSelected = false;
					dateSelected = false;
					return true;
				} else {
					fromDateSelected = false;
					dateSelected = false;
					return false;
					}
				} else {
					fromDateSelected = false;
					dateSelected = false;
					return false;
					}
			}else{
				alert("<fmt:message key='alert.enterValidDate'/>");
				fromDateSelected = false;
				dateSelected = false;
				return false;
			}
		} else {
			return false;
		}	
}


 function getValidAccess(){
    
	var table = document.getElementById("currencyExchangeList");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";	

	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){

			var hiddenElement2 = rows[i].getElementsByTagName("input")[0].value;
			
			if(hiddenElement2 != 'true'){
				return false;
			}			
		}
	 }  		
	return true;
}
	

function onDateKeyPress(obj,e){

	var dateFormat = '${sessionScope.CDM.dateFormat}';
	var event = (window.event|| e);
	if(event.keyCode == 13){  //enter	
		if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'fromDateAsString',dateFormat)){
			if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'toDateAsString',dateFormat)){	
			
				if(comparedates(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value,document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value,dateFormat,'From Date','To Date')){
					submitForm('displayList');
				}else{
					return false;
				}
			}else{
				return false;
			}
		}else{
				return false;
		}
  }
}

function validateForm(objForm){
	var dateFormat = '${sessionScope.CDM.dateFormat}';
  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["currencyexchange.exchangeRateFromDateAsString"];
  elementsRef[1] = objForm.elements["currencyexchange.exchangeRateToDateAsString"];
 
	 return validate(elementsRef);
}

function clearDate() {

document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value ="";
document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value ="";

}



function validatePageNumber(strObject)
{
if (document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'].value != "" && document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'].value != "") {
if(validateField(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'fromDateAsString',dateFormat)){	
	if(validateField(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'toDateAsString',dateFormat)){	
var re = /^\d+$/;
	if (strObject && (re.test(strObject.value) && strObject.value != 0)) 
	{
		if(parseInt(strObject.value) > maxPage)
		{
			strObject.value = maxPage;
		}
		goToResultsPage(strObject.value);
	}
	else
	{
		alert("<fmt:message key='alert.enterValidPageNumber'/>");
		strObject.value = currPage;
	}
	} else {		
		return false;
	}
	} else {		
		return false;
	}
	} else {
		alert("<fmt:message key='alert.enterValidDate'/>");
	}
}

</script>
<script language="JAVASCRIPT">
		var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
		var cal = new CalendarPopup("caldiv",true,"calFrame"); 
		cal.setCssPrefix("CAL");
		cal.offsetX = -30;
		cal.offsetY = -100;
		var cal2 = new CalendarPopup("caldiv",true,"calFrame"); 
		cal2.setCssPrefix("CAL");
		cal2.offsetX = 0;
		cal2.offsetY = -100;
	
  </script>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
	marginheight="0"
	onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="call()">
<form action="currencyexchange.do" enctype="multipart/form-data">
	<input name="exportType" type="hidden" value="">
	<input type="hidden" name="screen" id="exportDataScreen"
		value="CurrencyExchangeRates-SmartPredict" />
	<input name="selectedCurrencyCode" type="hidden">
	<input name="selectedCurrencyName" type="hidden">
	<input name="selectedExchangeDate" type="hidden">
	<input name="selectedExchangeFromDate" type="hidden">
	<input name="selectedExchangeToDate" type="hidden">
	<input name="selectedExchangeRate" type="hidden">
	<input name="filterCriteria" type="hidden" value='${filterCriteria}'>
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="totalCount" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="currentPage" type="hidden" value='${currPage}'>
	<input name="prevEnabled" type="hidden" value="">
	<input name="nextEnabled" type="hidden" value="">
	<input name="maxPages" type="hidden" value='${maxPage}'>
	<input name="method" type="hidden" value="">
	<input name="filterFromSerach" type="hidden" value="">
	<input name="refreshScreen" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">


	<input name="preexchangeRateToDateAsString" type="hidden" value="">
	<input name="preExchangeRateFromDateAsString" type="hidden" value="">
	<input name="exchangeRateFromDateAsString" type="hidden" value="">
	<input name="exchangeRateToDateAsString" type="hidden" value="">
	<input name="exchangeRateDateAsString" type="hidden" value="">
	<input name="exchangeRateAsString" type="hidden" value="">
	<input name="exchangeRate" type="hidden" value="">

	<input name="entityCode" type="hidden" value="">
	<input name="goToPageNo" type="hidden" value="">



	<input name="menuAccessId" type="hidden">


	<input name="domesticcurr" type="hidden" value="">
  <c:set var="CDM" value="${sessionScope.CDM}"/>
	<!-- Start : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered  -->
	<div id="CurrencyExchange"
		style="z-index: 99; position: absolute; left: 20px; top: 20px; width: 930px; height: 90px; border: 2px outset;"#7E97AF">
	<!--  End : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="CurrencyExchange"
		style="z-index: 99; position: absolute; left: 8px; top: 4px; width: 530px; height: 90px">
	<table width="510px" cellspacing="0" cellpadding="0" border="0"
		height="50px">
		<tr height="25px">
			<td width="38px"><b style="font-size: 12px"><fmt:message key="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="140px">
			
			   <select
				name="currencyexchange.id.entityId" cssClass="htmlTextAlpha"
				style="width:140px" onchange="submitFormEntity('displayList');"
				titleKey="tooltip.selectEntityid" tabindex="1">
					<c:forEach var="item" items="${requestScope.entities}">
					<option value="${item.value}"<c:if test="${currencyexchange.id.entityId == item.value}">selected</c:if>>
					${item.label}</option>
					</c:forEach>
			    </select>
						
			</td>
			<td width="20">&nbsp;</td>
			<td width="280"><span  id="entityName" name="entityName" style="font-size: 12px"
				class="spantext"></td>
		</tr>
		<tr height="25px">
			<td width="38px"><b style="font-size: 12px"> <fmt:message key="currencygroup.currencies" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="140px"><select
				name="currencyexchange.id.currencyCode"
				cssClass="htmlTextAlpha" style="width:55px"
				onchange="onCurrencyChange('showDetails');"
				titleKey="tooltip.selectCurrencyId" tabindex="2">
				<c:forEach var="item" items="${requestScope.currencies}">
				<option value="${item.value}"<c:if test="${currencyexchange.id.currencyCode == item.value}">selected</c:if>>
				${item.label}</option>
				</c:forEach>
				</select></td>


			<td width="20">&nbsp;</td>
			<td width="280"><span id="currencyName" name="currencyName"  style="font-size: 12px"
				class="spantext"></td>


		</tr>
	</table>
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>

	<table width="422px" border="0" cellspacing="0" cellpadding="0"
		height="25">
		<tr height="25px">
			<td width="120"><b style="font-size: 12px"><fmt:message key="auditLog.from" /></b>*</td>
			<td width="27">&nbsp;</td>

			<td width="215px" id="exchangeRateFromDate"><input type="text" cssClass="htmlTextAlpha"
				name="currencyexchange.exchangeRateFromDateAsString"
				value="${currencyexchange.exchangeRateFromDateAsString}"
				style="width:80px;height:20px;" maxlength="10"
				onkeydown="onFromDateKeyPress(this,event);"
				onblur="onFromDateChange(this);"
				onmouseout="fromDateSelected=false;" titleKey="tooltip.fromDate"
				tabindex="3" /> &nbsp; <A
				title='<fmt:message key="tooltip.selectFromDate"/>' name="datelink"
				ID="datelink" tabindex="4"
				onClick="cal.select(document.forms[0].elements['currencyexchange.exchangeRateFromDateAsString'],'datelink',dateFormatValue);storeFromDate();fromDateSelected=true;return false;"><img
				title='<fmt:message key="tooltip.selectBalanceDate"/>'
				src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;"></A></td>
			<td width="28">&nbsp;</td>
			<td width="50" height="26"><b style="font-size: 12px;padding-left: 10px"><fmt:message key="auditLog.to" /></b>*</td>
			<td width="26">&nbsp;</td>
			<td width="215px" id="exchangeRateToDate"><input type="text" cssClass="htmlTextAlpha"
				name="currencyexchange.exchangeRateToDateAsString"
				value="${currencyexchange.exchangeRateToDateAsString}"
				style="width:80px;height:20px;" maxlength="10"
				onkeydown="onToDateKeyPress(this,event);" onblur="onDateChange(this);"
				onmouseout="dateSelected=false;" titleKey="tooltip.toDate"
				tabindex="5" /> &nbsp; <!-- To store To date --> <A
				title='<fmt:message key="tooltip.selectToDate"/>' name="datelink2"
				ID="datelink2" tabindex="6"
				" onClick="cal2.select(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected=true;return false;"><img
				title='<fmt:message key="tooltip.selectBalanceDate"/>'
				src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;"></A></td>
		</tr>
	</table>
<!-- Start : Nithiyananthan 22/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="pageSummaryList"
		style="position: absolute; left: 704px; top: 0px; width: 210px; height: 25px; border: 2px;">
	<c:if test="${'true' != requestScope.hidePagination}">
	<table class="pagebuttons" border="0" cellpadding="0" cellspacing="1" height="35">
	  <tr height="34" >
	  	<%String currentPageAsString = (String)request.getAttribute("currentPage");%>
			<%String maxPageAsString = (String)request.getAttribute("maxPage");%>
			<%int currentPage = Integer.parseInt(currentPageAsString);%>
			<%int countPage = 1;%>
			<%int maxPage = Integer.parseInt(maxPageAsString);%>
	  	
		 <c:forEach items="${requestScope.pageSummaryList}" var="pageSummaryList" >
		<% if( countPage <=12) {++countPage; %>
		
	  	<td height="34"><b>Page</b>&nbsp;&nbsp;
	  	<input class="htmlTextNumeric" id="pageNoText" name="pageNo" size="5" height="34" align="top" value="<%=currentPageAsString %>" onkeydown="if (event.keyCode == 9 || event.keyCode == 13)validatePageNumber(this);">
	  	</td>
	    <td>
	    <c:if test="${'true' == requestScope.nextEnabled}">
		    <a href="#"  onclick="clickLink(-1);">
	    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="height: 11px;"></img><br />
			</a>	    
	    </c:if>
	    <c:if test="${'true' != requestScope.nextEnabled}">
	    <img alt="Next page" src="images/page_up.png" align="top" border="0" width="18" style="height: 11px;"></img><br />
	    </c:if>
	    <c:if test="${'true' == requestScope.prevEnabled}">
		    <a href="#"  onclick="clickLink(-2);">
	    <img alt="Previous page" src="images/page_down.png" align="bottom" width="18" border="0" style="margin-bottom: -2px; height: 11px;"></img><br />
			</a>
	    </c:if>
	    <c:if test="${'true' != requestScope.prevEnabled}">
	    <img alt="Previous page" src="images/page_down.png" align="bottom" height="12" width="18" border="0" style="margin-bottom: -2px; height: 11px;"></img>
	    </c:if>
	    </td>
	    <td style="text-align: center;">&nbsp;&nbsp;of&nbsp;&nbsp;
	    <input class="textAlpha" style="background:transparent;border: 0;" readonly name="maxPageNo" value="<%=maxPageAsString %>" size="5">
	  	</td>
	    
		<%}%>	
	</c:forEach>
	  </tr>
	  </table>
	  </c:if>
	</div>
<!-- End : Nithiyananthan 22/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->


	</div>
	</div>
	<!-- Start : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="BalanceMaintenance" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 20px; top: 115px; width: 930px; height: 458px;">
	<div id="BalanceMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 915px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="900"
		border="0" cellspacing="1" cellpadding="0">
		<!-- End : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
		<thead>
			<tr height="20px">
				<td width="70px" align="center" style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="sweep.currencyCode" /></b></td>
				<td width="280px" align="center"
					title='<fmt:message key="tooltip.currencyname"/>'><b><fmt:message key="userStatus.header.name" /></b></td>
				<td width="80px" align="center"
					title='<fmt:message key="tooltip.sortByDate"/>'><b><fmt:message key="auditLog.logDate_Date" /></b></td>
				<!-- Start : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
				 <c:if test="${'true' != requestScope.entityFlag}">
					<td id="domestic" width="180px" align="center"
						title='<fmt:message key="tooltip.exchangerate"/>'><b><fmt:message key="contact.dom.exchange" /> (${requestScope.domesticcurr}/Ccy)</b></td>
				</c:if>
				<c:if test="${'true' == requestScope.entityFlag}">
					<td id="currency" width="180px" align="center"
						title='<fmt:message key="tooltip.exchangerate"/>'><b><fmt:message key="contact.ccy.exchange" /> (Ccy/${requestScope.domesticcurr})</b></td>
				</c:if>

				<td width="150px" align="center"
					title='<fmt:message key="tooltip.updatedatetime"/>'><b><fmt:message key="accint.timedate" /></b></td>
				<td width="142px" align="center"
					title='<fmt:message key="tooltip.user"/>'><b><fmt:message key="balmaintenance.updateUser" /></b></td>
				<!-- End : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
			</tr>
		</thead>
	</table>
	</div>
	<!-- Start : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 930px; height: 453px; overflow-x: hidden">
	<div id="CurrencyExchange"
		style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 885px; height: 20px;">

	<table class="sort-table" id="currencyExchangeList" width="915"
		border="0" cellspacing="1" cellpadding="0" height="432">
		<!-- End : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="currencyExchange" items="${requestScope.currencyExchangeList}">
			<!-- End : Nithiyananthan 23/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->

		<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
		    <input type="hidden" name="currencyAccess" value="${currencyExchange.currencyAccess}"/>
            <td width="70px" align="left">
                <input type="hidden" name="id.currencyCode" value="${currencyExchange.id.currencyCode}" />
                ${currencyExchange.id.currencyCode}
            </td>
            <td width="280px" align="left">
                <input type="hidden" name="currencyName" value="${currencyExchange.currencyName}" />
                ${currencyExchange.currencyName}&nbsp;
            </td>
            <td width="80px" align="left">
                <input type="hidden" name="exchangeRateDateAsString" value="${currencyExchange.exchangeRateDateAsString}" />
                ${currencyExchange.exchangeRateDateAsString}&nbsp;
            </td>
            <td width="180px" align="right">
                <input type="hidden" name="exchangeRate" value="${currencyExchange.exchangeRate}" />
                ${currencyExchange.formattedExchangeRate}&nbsp;
            </td>
            <td width="150px" align="left">
                ${currencyExchange.updateDateAsString}&nbsp;
            </td>
            <td width="140px" align="left">
                ${currencyExchange.updateUser }&nbsp;
            </td>
        </tr>
		<c:set var="count" value="${count + 1}" />
        </c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="6"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>

	<div id="PartyMain"
		style="position: absolute; left: 842; top: 472px; width: 70px; height: 39px; z-index:5;visibility: visible;">
			<table >
			    <div id="exportReport"/>	
			</table>
	</div>
	<div id="helpScreen" style="position: absolute; left: 890; top: 470px; width: 30px; height: 39px;visibility: visible;">
	      <a tabindex="11" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Currency Exchange Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
				<img src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'> </a>

	</div>
	<!-- Start : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: -2; top: 460; width: 930; height: 39px; visibility: visible;">
	<!-- End : Nithiyananthan 20/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
	<div id="CustodianMaintenance"
		style="position: absolute; left: 6; top: 4; width: 539; height: 15px; visibility: visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td id="addbutton"></td>
			<td id="changebutton"></td>
			<td id="deletebutton"></td>
			<td id="closebutton"><a tabindex="10"
				title='<fmt:message key="tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>



	<div
		style="position: absolute; left: 6; top: 4; width: 512; height: 15px; visibility: hidden;">
	<table width="280" border="1" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td id="addenablebutton"><a tabindex="7"
				title='<fmt:message key="exchange.add"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="if(validateForm(document.forms[0]) ){buildAddExchangeURL('add');}"><fmt:message key="button.add" /></a></td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.add" /></a></td>

			<td id="changeenablebutton"><a tabindex="8"
				title='<fmt:message key="exchange.change"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="if(validateForm(document.forms[0]) ){buildChangeExchangeURL('change');}"><fmt:message key="button.change" /></a></td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.change" /></a></td>

			<td id="deleteenablebutton"><a tabindex="9"
				title='<fmt:message key="exchange.delete"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="if(validateForm(document.forms[0]) ){javascript:submitFormDelete('delete');}"><fmt:message key="button.delete" /></a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.delete" /></a></td>

		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
</form>
</body>

</html>

