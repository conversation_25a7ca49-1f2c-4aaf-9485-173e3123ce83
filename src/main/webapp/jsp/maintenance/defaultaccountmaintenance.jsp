<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.DefaultAcct"%>
<html>
<head>
<title><fmt:message key="defaultaccountmaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<script type="text/javascript">
var filterstatus="${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;

/* Variable declaration for urrencycode_temp */
var currencycode_temp='';
/* Variable declaration for xrefcode_temp */
var xrefcode_temp='';
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function disableAllButtons () {
	document.getElementById("changebutton").innerHTML =document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML =document.getElementById("deletedisablebutton").innerHTML; 
}
function onFilterandSort () {
	updateColors();
	disableAllButtons();
}

function init () {
	xl = new XLSheet("defaultAcctMaintenanceDetails","table_2", ["String","String", "String", "String"],"1222","false",undefined,undefined,"ddscrolltable"); // true changed to false
	xl.onsort = xl.onfilter = onFilterandSort;
	var dropBox1 = new SwSelectBox(document.getElementById('entityId'),document.getElementById('entityDesc'));
 	highlightTableRows("defaultAcctMaintenanceDetails");
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "closebutton";
	document.getElementById("changebutton").innerHTML =document.getElementById("changedisablebutton").innerHTML; 
	document.getElementById("deletebutton").innerHTML =document.getElementById("deletedisablebutton").innerHTML; 
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		if(menuEntityCurrGrpAccess=='0') 
			document.getElementById("addbutton").innerHTML =document.getElementById("addenablebutton").innerHTML;
	<%}%>
	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
	document.getElementById("addbutton").innerHTML =document.getElementById("adddisablebutton").innerHTML;
	<%}%>
	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
	 document.getElementById("changebutton").innerHTML =document.getElementById("changeenablebutton").innerHTML;
	 <%}%>
    <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
    document.getElementById("changebutton").innerHTML =document.getElementById("changedisablebutton").innerHTML;
    <%}%>
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
	document.getElementById("deletebutton").innerHTML =document.getElementById("deleteenablebutton").innerHTML;
	<%}%>
    <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
    document.getElementById("deletebutton").innerHTML =document.getElementById("deletedisablebutton").innerHTML;
    <%}%>
   if (filterstatus !="") {
		var filterStatus1 = filterstatus.split(",");
		if (document.getElementById("defaultAcctMaintenanceDetails").innerText != "")
			xl.setFilterStatus (filterStatus1);
	 }
   if (sortDescending !="" && sortDescending !="null" && sortDescending == "true") {	
		xl.dataTable.defaultDescending = true;
		if(document.getElementById("defaultAcctMaintenanceDetails").innerText != "")
			xl.dataTable.doManualSorting (sortStatus);
	 }
   if (sortDescending !="" && sortDescending !="null" && sortDescending == "false") {	
		if(document.getElementById("defaultAcctMaintenanceDetails").innerText != "")
			xl.dataTable.doManualSorting (sortStatus);	
	 }
	xl.onsort = xl.onfilter = onFilterandSort;
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	ShowErrMsgWindow('${actionError}');
}
/*Start:The modified the mantis 1435 on 22-02-2012.The purpose for Tab navigation fields when enter key pressed to display  and close screen*/
/* This Method is used for save or update the data and close the screen when enter key pressed 
 *@param methodname
 */
function onKeyEnterSubmit(methodName,event){
	var event = (window.event|| e);
     if(event.keyCode == 13){
     if(methodName=="add"){
     openWindow(buildAddaccount('add'),'accountsmaintenancechangewindow','left=50,top=150,width=735,height=289,toolbar=0, resizable=yes, status=yes, scrollbars=no','true');
     }else if(methodName=="change"){
     openWindow(buildAddaccount('change'),'accountsmaintenancechangewindow','left=50,top=150,width=735,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=no','true');
     }else if(methodName=="delete"){
     submitDeleteForm('delete');
     }else if(methodName=="close"){
     confirmClose('P');
     }
  }
}
/**
* This method is called when click on change button
*
* @param methodName
* @return param
*/
function buildAddaccount (methodName) {
	maintainSortFilterStatus();
	var param = 'defaultacctmaintenance.do?method='+methodName+'&entityId='+document.getElementById('entityId').options[document.getElementById('entityId').selectedIndex].value;
	param += '&xrefCode='+escape(encodeURIComponent(xrefcode_temp));
	param += '&currencyCode='+currencycode_temp;
	return  param;
}
function submitForm (methodName) {
	document.forms[0].method.value = methodName;
	document.forms[0].submit ();
}
/**
* This method is called when click on delete button
*
* @param methodName
* @return 
*/
function submitDeleteForm (methodName) {
	document.getElementById('method').value = methodName;
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true) {
		maintainSortFilterStatus ();
		document.forms[0].xrefCode.value=xrefcode_temp;
		document.forms[0].currencyCode.value=currencycode_temp;		
		document.forms[0].submit ();
	}
}


function currencyAccessConfirm(currency,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/defaultacctmaintenance.do?method=currencyAccessConfirm";
	sURL = sURL + "&currency="+currency;
	sURL = sURL + "&entityId="+entity;
	oXMLHTTP.open( "POST", sURL, false );	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}

/**
* This method is called when select the grid row
* @param rowElement
* @param isSelected
*/
function onSelectTableRow (rowElement, isSelected) {
	var hiddenElements = rowElement.getElementsByTagName("input");
	currencycode_temp=hiddenElements[0].value;
	xrefcode_temp = hiddenElements[1].value;

	var flag=currencyAccessConfirm(currencycode_temp,document.forms[0].elements['defaultAcct.id.entityId'].value)			
	
	if(flag=="true" && isSelected && document.forms[0].menuAccessId.value  == 0)
	{
		document.getElementById("changebutton").innerHTML =document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML =document.getElementById("deleteenablebutton").innerHTML;

	}else {
		document.getElementById("changebutton").innerHTML =document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML =document.getElementById("deletedisablebutton").innerHTML;

	}

}
function maintainSortFilterStatus () {
	document.forms[0].selectedSortStatus.value = "";
	document.forms[0].selectedSortDescending.value ="";
	document.forms[0].selectedFilterStatus.value="";
	var sortColumn = xl.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;
	var sSortDescending =  xl.dataTable.descending;		
	document.forms[0].selectedSortDescending.value = sSortDescending ;
	var filterArr = new Array(5);
	if(document.getElementById("defaultAcctMaintenanceDetails").innerText != "")
	filterArr = xl.getFilterStatus();
	for(var idy = 0 ; idy < filterArr.length-1; ++idy)	
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;
}


window.onload = function () {
   	setParentChildsFocus();
	setFocus(document.forms[0]);
	init ();
	ShowErrMsgWindow('${actionError}');
	// Start: Create a Select Export :Code added by Med Amine 
	var headerData = [];
	var dataprovider = new Array();
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
			
	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
	
   if(record()== 0)
	   $("#exportReport").disabled(true); 
   else
	   $("#exportReport").disabled(false);

    }


function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return exportData(type.toLowerCase());
}

window.onmousemove = reportMove;
window.onunload = call;
function record()
{
 return document.getElementById ("ddscrolltable").getElementsByTagName('tr').length-1;
}
function exportData(exportType) 
 {
  document.forms[0].method.value = 'exportDefaultacc';
  document.forms[0].exportType.value = exportType.trim();
  document.forms[0].entityCode.value = document.forms[0].elements['defaultAcct.id.entityId'].value;
   if(record() > 0){
    document.forms[0].submit();
	}
   document.forms[0].reset();
 }
</script>
</head>
<body>
<form action="defaultacctmaintenance.do">
<input name="method" id="method" type="hidden" value="listview"/>
<input name="entityCode" type="hidden" value="">
<input name="exportType" type="hidden" value="">
<input type="hidden" name="screen" id="exportDataScreen" value="DefaultAccountMaintenance-SmartPredict"/>
<input name="selectedFilterStatus" type="hidden" value=""/>
<input name="selectedSortDescending" type="hidden" value=""/>
<input name="selectedSortStatus" type="hidden" value=""/>
<input name="xrefCode" type="hidden" value=""/>
<input name="currencyCode" type="hidden" value=""/>
<input name="menuAccessId" type="hidden" value=""/>
<div id="AccountMaintenance"
		style="position: absolute; left: 20px; top: 20px; width: 751px; height: 36px; border: 2px outset;"
		color="#7E97AF">
	<div
		style="position: absolute; left: 8px; top: 4px; width: 705px; height: 70px;">
	<table border="0" cellpadding="0" cellspacing="0" width="741px" style="font-family: verdana, helvetica; font-size: 9pt;">
		<tr>
			<td width="60px"><b><fmt:message key="entity.id"/></b></td>
			<td width="28px">&nbsp;</td>
			<td width="150px">
				<select id="entityId" name="defaultAcct.id.entityId" onchange="submitForm('listview')" titleKey="tooltip.selectEntityId" style="width:140px" tabindex="1"
						<c:forEach items="${requestScope.entities}" var="entity">
						   <option
							  value="${entity.value}"
							  <c:if test="${defaultAcct.id.entityId == entity.value}">selected="selected"</c:if>>
							  ${entity.label}
						   </option>
						</c:forEach>/></select>
			</td>
			<td width="20">&nbsp;</td>
			<td width="432"><span id="entityDesc" name="entityDesc" class="spantext">${requestScope.entityName}</span></td>
			</tr>
	</table>
</div>
</div>
<div color="#7E97AF"
		style="position: absolute; border: 0px outset; left: 20px; top: 65px; width: 756px; height: 460px;">
	<div id="AccountMaintenance"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 735px; height: 10px;">
		<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="730" border="0" cellspacing="1" cellpadding="0"  height="23">
			<thead>
				<tr>
					<td width="70"  height="20px"  style="border-left-width: 0px;"align="left" title='<fmt:message key="tooltip.sortCurrencyCode"/>'><b><fmt:message key="defaultaccountmaintenance.currencyCode"/></b></td>
					<td width="120" height="20px" align="left" title='<fmt:message key="tooltip.BIC"/>'><b><fmt:message key="defaultaccountmaintenance.BIC"/></b></td>
					<td width="220" height="20px" align="left" title='<fmt:message key="tooltip.sortAccountId"/>'><b><fmt:message key="defaultaccountmaintenance.accountId"/></b></td>
					<td width="315" height="20px" align="left" title='<fmt:message key="tooltip.sortAccountName"/>'><b><fmt:message key="defaultaccountmaintenance.accountName"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable" style="position:absolute; width:753px; height:456px; overflowY:scroll; top:1px; ">
		<div id="DAccMaintenance" style="position:absolute;z-index:99;left:1px; top:20px; width:582px; height:10px;">
			<table class="sort-table" id="defaultAcctMaintenanceDetails" width="735" border="0" cellspacing="1" cellpadding="0" height="435">
<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="acct" items="${requestScope.listViewData}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <c:set var="defalutAcct" value="${acct}" />
            <input type="hidden" name="null1" value="${acct.id.currencyCode}" />
            <input type="hidden" name="null2" value="${acct.id.xrefCode}" />
            <td width="70" align="left">${acct.id.currencyCode}&nbsp;</td>
            <td width="120" align="left">
                <c:choose>
                    <c:when test="${not empty defalutAcct.id.xrefCode}">
                        ${fn:replace(defalutAcct.id.xrefCode, ' ', '&nbsp;')}&nbsp;
                    </c:when>
                    <c:otherwise>
                        &nbsp;
                    </c:otherwise>
                </c:choose>
            </td>
            <td width="220" align="left">${acct.accountMaster.id.accountId}&nbsp;</td>
            <td width="315" align="left">${acct.accountMaster.accountName}&nbsp;</td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>
			<tfoot><tr><td colspan="4" ></td></tr></tfoot>
		</table>
		</div>
	</div>
</div>
<div id="SchedulerMaintenance" style="position:absolute; left:740px; top:553px; width:35px; height:29px; visibility:visible;">
    <table width="20px" border="0" cellspacing="0" cellpadding="0" height="20">
        <tr>

          <td align="Right">
                <a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Scheduler '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
        </td>
        </tr>
    </table>
</div>
<div id="ddimagebuttons" color="#7E97AF"
			style="position: absolute; border: 2px outset; left: 20; top: 545; width: 756px; height: 39px; visibility: visible;">
  	<div id="ddimagebuttons" style="position: absolute; left: 5; top: 3px; width: 470px; height: 30px; visibility: visible; " >
		<span id="addbutton"></span>
		<span id="changebutton"></span>
		<span id="deletebutton"></span>
		<span id="closebutton">		
			<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" tabindex="5" onKeydown = "onKeyEnterSubmit('close',event);" ><fmt:message key="button.close"/></a>			
		</span>
	</div>
<div id="accex" style="width:753;">
	<div id="pnlReport"
		style="position: absolute; left: 675; top: 8px; width: 70px; height: 25px; visibility: visible;">
         <table >
		    <div id="exportReport"  />
		  </table>	
   </div>
	</div>
	
	<div style="display:none;">
		<span id="addenablebutton"><a title='<fmt:message key="tooltip.addNewAccount"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddaccount('add'),'accountsmaintenancechangewindow','left=50,top=150,width=735,height=289,toolbar=0, resizable=yes, status=yes, scrollbars=no','true')" tabindex="2" onKeydown = "onKeyEnterSubmit('add',event);"><fmt:message key="button.add"/></a></span>
		<span id="adddisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.addNewAccount"/>'><fmt:message key="button.add"/></a></span>
		<span id="changeenablebutton"><a title='<fmt:message key="tooltip.changeSelAc"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddaccount('change'),'accountsmaintenancechangewindow','left=50,top=150,width=735,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=no','true')" tabindex="3" onKeydown = "onKeyEnterSubmit('change',event);" ><fmt:message key="button.change"/></a></span>
	    <span id="changedisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.changeSelAc"/>'><fmt:message key="button.change"/></a></span>
		<span id="deleteenablebutton"><a title='<fmt:message key="tooltip.deleteSelAc"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')" tabindex="4" onKeydown = "onKeyEnterSubmit('delete',event);" ><fmt:message key="button.delete"/></a></span>		
		<span id="deletedisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.deleteSelAc"/>'><fmt:message key="button.delete"/></a></span>
	</div>
<!--End:The modified the mantis 1435 by sunil on 22-02-2012.The purpose for Tab navigation fields when enter key pressed to display  and close screen-->
</div>
</form>
</body>
</html>            
