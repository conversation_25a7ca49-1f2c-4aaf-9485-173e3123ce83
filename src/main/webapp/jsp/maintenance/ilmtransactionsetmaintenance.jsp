<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.ILMTransactionSetHDR"%>
<%@page import="org.swallow.maintenance.model.ILMScenario"%>
<%@page import="org.swallow.maintenance.model.ILMAccountGroup"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="ilmtransaction.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	
	/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
     mandatoryFieldsArray= "undefined" ;
	
	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad()
	{
		xl = new XLSheet("ilmTransactionSet","table_2", ["String","String", "String", "String",],"1122");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("ilmTransactionSet");
		
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
		var dropBox1 = new SwSelectBox(document.forms[0].elements["intraDayTransactionSetsMaintenance.id.entityId"],document.getElementById("entityName"));
		var dropBox2 = new SwSelectBox(document.forms[0].elements["intraDayTransactionSetsMaintenance.id.currencyCode"],document.getElementById("currencyDesc"));
		
	
		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";	
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.VIEW_BUT_STS) %>";	
		document.getElementById("viewbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);	
	
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	}
	
	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e)
	{
        var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 *
	 **/
	function disableButtons(){
	
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	
		updateColors();
	}
	
	/**
	 * Triggered when the user deletes a scenario via delete button
	 * @ param methodName
	 **/
	function submitDeleteForm(methodName){
		
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/ilmTransScenario.do?method=checkPossibilityToDelete";
		requestURL = requestURL + "&selectedTxId=" + document.forms[0].selectedTxId.value;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		var	possibleToDelete = new String(oXMLHTTP.responseText);
		
		if(possibleToDelete == "false"){
			alert("<fmt:message key='alert.transactionSetSpecified'/>");
			return;
		}
		else{
		document.forms[0].method.value = methodName;	
		var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
		if (yourstate==true) 	
			document.forms[0].submit();
		}
	}
	

	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected 
	 **/
	function onSelectTableRow(rowElement , isSelected)
	{
		var transactionId = rowElement.getElementsByTagName("input")[0];
		var entityId = rowElement.getElementsByTagName("input")[1];
		var currencyCode = rowElement.getElementsByTagName("input")[2];
		document.forms[0].selectedEntityId.value = entityId.value;
		document.forms[0].selectedCurrencyCode.value = currencyCode.value;
		document.forms[0].selectedTxId.value = transactionId.value;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/ilmTransScenario.do?method=checkTransAccess";
		requestURL = requestURL + "&selectedEntityId=" + entityId.value;
		requestURL = requestURL + "&selectedCurrencyCode=" + currencyCode.value;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		menuEntityCurrGrpAccess = new String(oXMLHTTP.responseText);
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		
	if (document.forms[0].menuAccessId.value == "0") {
		
			document.getElementById("viewbutton").innerHTML = (isSelected
					&& (menuEntityCurrGrpAccess == "0" || menuEntityCurrGrpAccess == "1") ? document
					.getElementById("viewenablebutton").innerHTML
					: document.getElementById("viewdisablebutton").innerHTML);
			
			document.getElementById("changebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" ? document
					.getElementById("changeenablebutton").innerHTML : document
					.getElementById("changedisablebutton").innerHTML);
			
			document.getElementById("deletebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" ? document
					.getElementById("deleteenablebutton").innerHTML : document
					.getElementById("deletedisablebutton").innerHTML);
			
		} else if (document.forms[0].menuAccessId.value == "1" && isSelected)
			document.getElementById("viewbutton").innerHTML = document
					.getElementById("viewenablebutton").innerHTML;
	}

	function submitForm(methodName) {
		if (methodName == "listScenariosByEntity")
			document.forms[0].elements["ilmTransactionSetHDR.id.currencyCode"].value = "All";
		if (methodName == "listScenariosByEntity"
				|| methodName == "listScenariosByCcy")
			methodName = "listScenarios";

		document.forms[0].method.value = methodName;
		document.forms[0].submit();

	}

	function buildAddTransactionURL(methodName) {

		var param = 'ilmTransScenario.do?method=' + methodName;
		if (methodName != "addTransactionSet")
			param = param + "&selectedEntityId="
					+ document.forms[0].selectedEntityId.value
					+ "&selectedCurrencyCode="
					+ document.forms[0].selectedCurrencyCode.value
					+ "&selectedTransactionId="
					+ document.forms[0].selectedTxId.value;

		return param;

	}
</SCRIPT>
</head>

<form action="ilmTransScenario.do">
<input name="method" type="hidden" value="listAccountGroups">
<input name="selectedTxId" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value="">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="selectedSystemFlag" type="hidden" value="">
<input name="menuAccessId" type="hidden" >

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
	
	<div id="IlmScenario" style="position:absolute; border:2px outset; left:20px; top:20px; width:855px; height:400;">
		<div id="IlmScenario" style="position:absolute;z-index:99;left:0px; top:0px; width:840; height:10px;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="830" border="0" cellspacing="1" cellpadding="0">
				<thead>
					<tr height="22px">
						<td width="170" align="left" title='<fmt:message key="tooltip.sort.transactionEntityId"/>'><b><fmt:message key="ilmTransactionSet.entityId"/></b></td>
						<td width="70" align="left" title='<fmt:message key="tooltip.sort.transactionCcy"/>'><b><fmt:message key="ilmTransactionSet.currencyCode"/></b></td>
						<td width="250" align="left"  title='<fmt:message key="tooltip.sort.transactionSetId"/>'><b><fmt:message key="ilmTransactionSet.transactionSetId"/></b></td>
						<td width="338" align="left" title='<fmt:message key="tooltip.sort.transactionName"/>'><b><fmt:message key="ilmTransactionSet.transactionSetName"/></b></td>
					</tr>
				</thead>
			</table>
		</div>
		<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:850px; height:390;overflowY:scroll">
			<div id="IlmScenario" style="position:absolute;z-index:99;left:0px; top:22px; width:825px; height:10px;">
				<table class="sort-table" id="ilmTransactionSet" width="830px" border="0" cellspacing="1" cellpadding="0" height="365">
	<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="transactionSet" items="${ilmTransactionSetsDetails}">
				<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
					<input type="hidden" name="id.txnSetId" value="${transactionSet.id.txnSetId}" disabled="true"/>
					<input type="hidden" name="id.entityId" value="${transactionSet.id.entityId}" disabled="true"/>
					<input type="hidden" name="id.currencyCode" value="${transactionSet.id.currencyCode}" disabled="true"/>
					<td width="170" align="left">${transactionSet.id.entityId}&nbsp;</td>
					<td width="70" align="left">${transactionSet.id.currencyCode}&nbsp;</td>
					<td width="250" align="left">${transactionSet.id.txnSetId}&nbsp;</td>
					<td width="338" align="left">${transactionSet.txnSetName}&nbsp;</td>
        		</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
		</tbody>
					<tfoot>
						<tr><td colspan="4" ></td></tr>
					</tfoot>
				</table>
			</div>
		</div>
		<div id="IlmScenario" style="position:absolute; left:770; top:415; width:70px; height:29px; visibility:visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','ILM Transaction Set Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
					</td>
				</tr>
			</table>
		</div>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:428;  width:855px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:845px; height:15px; visibility:visible;">
				<table width="410" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td id="closebutton" width="70px">		
							<a title='<fmt:message key="tooltip.close"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>			
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden; display:none;">  	
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
			            <td id="addenablebutton">		
							<a title='<fmt:message key="tooltip.add.TransactionSet"/>' tabindex="1" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddTransactionURL('addTransactionSet'),'transactionadd','left=50,top=190,width=720,height=580,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add"/></a>
						</td>				
						<td id="adddisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
						</td>
						<td id="changeenablebutton">		
							<a title='<fmt:message key="tooltip.change.TransactionSet"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddTransactionURL('changeTransactionSet'),'transactionchange','left=50,top=190,width=720,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
						</td>		
						<td id="changedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
						</td>
						<td id="viewenablebutton">		
							<a  title='<fmt:message key="tooltip.view.TransactionSet"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddTransactionURL('viewTransaction'),'scenarioview','left=50,top=190,width=720,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
						</td>		
						<td id="viewdisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
						</td>
						<td id="deleteenablebutton">		
							<a  title='<fmt:message key="tooltip.delete.TransactionSet"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('deleteTransaction');" ><fmt:message key="button.delete"/></a>
						</td>		
						<td id="deletedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
						</td>
						<td id="roleenablebutton">		
							
						</td>		
						<td id="roledisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.role"/></a>
						</td>
					</tr>
				</table>
			  </div>
			</div>
		
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
</body>
</form>
</html>            