<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.SwtUtil"%>

<html lang="en">
	<head>
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title><c:if test="${requestScope.methodName == 'add'}">
			<fmt:message key="ilmAccountGrpAdd.title.window.addScreen" />
		</c:if><c:if test="${requestScope.methodName == 'change'}">
			<fmt:message key="ilmAccountGrpAdd.title.window.changeScreen" />
		</c:if><c:if test="${requestScope.methodName == 'view'}">
			<fmt:message key="ilmAccountGrpAdd.title.window.viewScreen" />
		</c:if><c:if test="${requestScope.methodName == 'addfromILM'}">
		<fmt:message key="ilmAccountGrpAdd.title.window.addScreen" /></c:if>
		</title>
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>


		<script type="text/javascript">
			var selectedAccountGroup = '${selectedAccountGroup}';
			var methodName = '${methodName}';
			var currencyCode = '${currencyCode}';
			var entityId = '${entityId}';
			var description ='${description}';
			var createdBy = '${createdBy}';
			var createdOn = '${createdOn}';
			var groupDefaultName='${groupDefaultName}';
			var filter ='${filter}';
			var screenRoute = "ilmAccountGroupDetail";
			var parentScreen='${parentScreen}';
			var maintainAnyGroup = '${maintainAnyGroup}';
			var dateFormat ='${sessionScope.CDM.dateFormat}';
			var currencyFormat = '${sessionScope.CDM.currencyFormat}';
			var ccyPattern = 0;
		
				if(currencyFormat.indexOf("1")!=-1)
					ccyPattern = 0;
				else if(currencyFormat.indexOf("2")!=-1)
					ccyPattern = 1;
 			var label = new Array ();
 			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["text"]["button-save"] = "<fmt:message key="button.save"/>";
 			label["text"]["button-test"] = "<fmt:message key="button.test"/>";
 			label["text"]["button-cancel"] = "<fmt:message key="button.cancel"/>";
 			
 			label["text"]["label-entity"] = "<fmt:message key="ilmAccountGroupDetails.entity"/>";
 			label["text"]["label-currency"] = "<fmt:message key="ilmAccountGroupDetails.currency"/>";
 			label["text"]["label-groupId"] = "<fmt:message key="ilmAccountGroupDetails.grpId"/>";
 			label["text"]["label-pubPriv"] = "<fmt:message key="ilmAccountGroupDetails.privPub"/>";
 			label["text"]["label-type"] = "<fmt:message key="ilmAccountGroupDetails.type"/>";
 			label["text"]["label-name"] = "<fmt:message key="ilmAccountGroupDetails.name"/>";
 			label["text"]["label-description"] = "<fmt:message key="ilmAccountGroupDetails.description"/>";
 			label["text"]["label-firstMin"] = "<fmt:message key="ilmAccountGroupDetails.firstMin"/>";
 			label["text"]["label-secondMin"] = "<fmt:message key="ilmAccountGroupDetails.secondMin"/>";
 			label["text"]["label-firstMax"] = "<fmt:message key="ilmAccountGroupDetails.firstMax"/>";
 			label["text"]["label-secondMax"] = "<fmt:message key="ilmAccountGroupDetails.secondMax"/>";
 			label["text"]["label-filterCondition"] = "<fmt:message key="ilmAccountGroupDetails.filterCondition"/>";
 			label["text"]["label-grpMbrAcct"] = "<fmt:message key="ilmAccountGroupDetails.grpMemberAccount"/>";
 			label["text"]["label-acctInGrp"] = "<fmt:message key="ilmAccountGroupDetails.accountInGrp"/>";
 			label["text"]["label-acctNotInGrp"] = "<fmt:message key="ilmAccountGroupDetails.accountNotInGrp"/>";
 			label["text"]["label-quickSearch"] = "<fmt:message key="ilmAccountGroupDetails.quickSearch"/>";
 			label["text"]["label-listFromGrp"] = "<fmt:message key="ilmAccountGroupDetails.listFromGrp"/>";
 			label["text"]["label-createdBy"] = "<fmt:message key="ilmAccountGroupDetails.createdBy"/>";
 			label["text"]["label-createdOn"] = "<fmt:message key="ilmAccountGroupDetails.createdOn"/>";
 			label["text"]["label-public"] = "<fmt:message key="ilmAccountGroupDetails.pub"/>";
 			label["text"]["label-private"] = "<fmt:message key="ilmAccountGroupDetails.priv"/>";
 			label["text"]["label-dynamic"] = "<fmt:message key="ilmAccountGroupDetails.dynamic"/>";
 			label["text"]["label-fixed"] = "<fmt:message key="ilmAccountGroupDetails.fixed"/>";
 			
 			label["tip"]["label-entity"] = "<fmt:message key="tooltip.selectEntityid"/>";
 			label["tip"]["label-currency"] = "<fmt:message key="tooltip.selectCurrencyId"/>";
 			label["tip"]["button-cancel"] = "<fmt:message key="tooltip.cancel"/>";
 			label["tip"]["button-save"] = "<fmt:message key="tooltip.save"/>";
 			label["tip"]["button-test"] = "<fmt:message key="tooltip.ILMTestButton"/>";
 			label["tip"]["label-groupId"] = "<fmt:message key="tooltip.ILMgroupId"/>";
 			label["tip"]["label-pubPriv"] = "<fmt:message key="tooltip.privPub"/>";
 			label["tip"]["label-type"] = "<fmt:message key="tooltip.ilmGrpType"/>";
 			label["tip"]["label-name"] = "<fmt:message key="tooltip.name"/>";
 			label["tip"]["label-description"] = "<fmt:message key="tooltip.description"/>";
 			label["text"]["label-absoulte-thresholds"] = "<fmt:message key="ilmAccountGroupDetails.absoluteThresholds"/>";
 			label["text"]["label-net-thresholds"] = "<fmt:message key="ilmAccountGroupDetails.netThresholds"/>";
 			label["text"]["label-maximum"] = "<fmt:message key="ilmAccountGroupDetails.maximum"/>";
 			label["text"]["label-minimum"] = "<fmt:message key="ilmAccountGroupDetails.minimum"/>";
 			label["tip"]["label-firstMin"] = "<fmt:message key="tooltip.firstMin"/>";
 			label["tip"]["label-secondMin"] = "<fmt:message key="tooltip.secondMin"/>";
 			label["tip"]["label-firstMax"] = "<fmt:message key="ilmAccountGroupDetails.firstMax"/>";
 			label["tip"]["label-secondMax"] = "<fmt:message key="ilmAccountGroupDetails.secondMax"/>";
 			label["tip"]["label-Max"] = "<fmt:message key="tooltip.Max"/>";
 			label["tip"]["label-Min"] = "<fmt:message key="tooltip.Min"/>";
 			label["tip"]["label-filterCondition"] = "<fmt:message key="tooltip.acctGrpFilter"/>";
 			label["tip"]["label-quickSearch"] = "<fmt:message key="tooltip.quickSearch"/>";
 			label["tip"]["label-listFromGrp"] = "<fmt:message key="tooltip.listFromGrp"/>";
 			label["tip"]["label-legendText"] = "<fmt:message key="ilmAccountGroupDetails.tootlipLegendText"/>";
 			
 			label["text"]["label-ilmAccountgrpdetailsTitle"] = "<fmt:message key="ilmAccountGroupDetails.titleScreen"/>";  			
 			label["text"]["label-accntInGrp"] = "<fmt:message key="ilmAccountGroupDetails.accntInGrp"/>";
 			label["text"]["label-accntNotInGrp"] = "<fmt:message key="ilmAccountGroupDetails.accntNotInGrp"/>";
 			label["text"]["label-ilmAccountgrpdetailsTitle"] = "<fmt:message key="ilmAccountGroupDetails.ilmAccountgrpdetailsTitle"/>";
 			label["text"]["label-showXMLRightGrid"] = "<fmt:message key="ilmAccountGroupDetails.showXMLRightGrid"/>";
 			label["text"]["label-showXMLLeftGrid"] = "<fmt:message key="ilmAccountGroupDetails.showXMLLeftGrid"/>";
 			label["text"]["label-invalidAmountAlert"] = "<fmt:message key="ilmAccountGroupDetails.invalidAmountAlert"/>";
 			label["text"]["label-recordExistsAlert"] = "<fmt:message key="ilmAccountGroupDetails.recordExistsAlert"/>";
 			label["text"]["label-syntaxAlert"] = "<fmt:message key="ilmAccountGroupDetails.syntaxAlert"/>";
 			label["text"]["label-syntaxAlertTitle"] = "<fmt:message key="ilmAccountGroupDetails.syntaxAlertTitle"/>";
 			label["text"]["label-nbOfRecords"] = "<fmt:message key="ilmAccountGroupDetails.nbOfRecords"/>";
 			label["text"]["label-acctGrpRequired"] = "<fmt:message key="ilmAccountGroupDetails.acctGrpRequired"/>";
	
 			label["text"]["label-legendText"] = "<fmt:message key="ilmAccountGroupDetails.labelLegendText"/>";
 			label["text"]["label-id"] = "<fmt:message key="ilmAccountGroupDetails.labelId"/>";
 			label["text"]["label-name"] = "<fmt:message key="ilmAccountGroupDetails.labelName"/>";
 			
 			label["text"]["label-allowReporting"] = "<fmt:message key="ilmAccountGroupDetails.allowReporting"/>";
 			label["tip"]["label-allowReporting"] = "<fmt:message key="ilmAccountGroupDetails.tootlip.allowReporting"/>";
 			label["text"]["label-netCum"] = "<fmt:message key="ilmAccountGroupDetails.netCum"/>";
 			label["tip"]["label-netCum"] = "<fmt:message key="ilmAccountGroupDetails.tootlip.netCum"/>";
 			label["text"]["label-correspBank"] = "<fmt:message key="ilmAccountGroupDetails.correspBank"/>";
 			label["tip"]["label-correspBank"] = "<fmt:message key="ilmAccountGroupDetails.tootlip.correspBank"/>";
 			
 			label["text"]["alert-allowReporting"] = "<fmt:message key="ilmScenario.alert.allowReporting"/>";
 			label["text"]["alert-netMinimum"] = "<fmt:message key="ilmAccountGroupDetails.alert.netMinimum"/>";
 			label["text"]["alert-netMaximum"] = "<fmt:message key="ilmAccountGroupDetails.alert.netMaximum"/>";
 			
 			label["text"]["label-mainAgent"] = "<fmt:message key="ilmAccountGroupDetails.mainAgentText"/>";
 			label["tip"]["label-mainAgent"] = "<fmt:message key="ilmAccountGroupDetails.tootlip.mainAgentText"/>";
 			
			/**
             * formatCurrency
             * 			
 			 * This method is used to format the amount in system currency format (called by flex) 
			 */
			function formatCurrency(amount){
			
				return validateCurrency(document.forms[0].elements["acctMaintenance.minseepamtasString"],'creditintrates',currencyFormat, document.forms[0].elements['acctMaintenance.currcode'].value);
			}
			
			function validateCurrencyOriginal(strField, currCode){
				var thePat = PatternsDict[currencyFormat]; 
				var strVal = strField;
				var strDecimals = '2';
								   
				if(currCode != 'undefined' && currCode != null ){
					strDecimals = getCurrencyDecimal(currCode);
				}
				return expandMBTValue(strField, thePat, strDecimals, strVal);
			}

			
			function help(){
				  openWindow(buildPrintURL('print','ILM Account Group Details Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true');
			}
			
		
			/**
			* Used to export report as  pdf, xls,csv format
			**/
			function onExport(accountGrpId, exportType){
				
				document.getElementById('exportDataForm').action = 'intraDayLiquidity.do?method=exportAccountGrpDetails';
				document.getElementById('accountGrpId').value = accountGrpId;
				document.getElementById('exportType').value = exportType;
			
				document.forms[0].submit();
		
			}
			
			function refreshParent() {
				<c:if test="${requestScope.parentScreen == 'addIlmCurrencyParameter'}">
				window.opener.document.forms[0].method.value = window.opener.getStoredParam("methodNameParent");
				window.opener.document.forms[0].selectedEntityId.value = window.opener.document.forms[0].elements["ilmCcyParams.id.entityId"].value;
				window.opener.document.forms[0].currencyCode.value = window.opener.document.forms[0].elements["ilmCcyParams.id.currencyCode"].value;
				window.opener.document.forms[0].selectedEntityName.value = window.opener.document.getElementById("entityName").textContent;
				window.opener.document.forms[0].submit();
				</c:if>
				<c:if test="${requestScope.parentScreen == 'IlmAccountGroupMaintenance'}">
				window.opener.document.forms[0].method.value = "listAccountGroups";
				window.opener.document.forms[0].submit();
				</c:if>
				<c:if test="${requestScope.parentScreen == 'liquidityMonitor'}">
				window.opener.refreshGridData();
				</c:if>

			}
		</script>
<%@ include file="/angularscripts.jsp" %>
<form id="exportDataForm" target="tmp" method="post">
			<input name="method" type="hidden" value="">
			<input name="accountGrpId" type="hidden"  value="" >
			<input name="exportType" type="hidden"  value="" >
			<input type="hidden" name="screen" value="<fmt:message key="ilmAccountGrpAdd.title.window.viewScreen" />" />
			<iframe name="tmp" width="0%" height="0%" src="#" />
		</form>
		
		
	</body>
</html>