<?xml version="1.0" encoding="utf-8"?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for Forecast Monitor Template Add Normal pop up screen.
-
- Author(s): Vivekanandan A
- Date: 24-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<forecastmonitor>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key='label.forecasttemplate.column.id'/>" clickable="false" draggable="false" filterable="true" type="str" dataelement="id" width="156"/>
				<column heading="<fmt:message key='label.forecasttemplate.column.name'/>" clickable="false" draggable="false" filterable="true" type="str" dataelement="name" width="156"/>
			</columns>
		</metadata>
		<rows size="13">

			<%
				String typeId = (String) request.getAttribute("typeId");
				if (SwtConstants.BOOK_LABEL.equals(typeId)) {
			%>
			<c:forEach items="${requestScope.bookDetailsList}" var="bookDetailsList">
				<row>
					<id negative="false" clickable="false">${bookDetailsList.id.bookCode}</id>
					<name negative="false" clickable="false">${bookDetailsList.bookName}</name>
				</row>
			</c:forEach>
			<%
			} else if (SwtConstants.GROUP_LABEL.equals(typeId)) {
			%>
			<c:forEach items="${requestScope.groupDetailsList}" var="groupDetailsList">
				<row>
					<id negative="false" clickable="false">${groupDetailsList.id.groupId}</id>
					<name negative="false" clickable="false">${groupDetailsList.groupName}</name>
				</row>
			</c:forEach>
			<%
			} else if (SwtConstants.META_GROUP_LABEL.equals(typeId)) {
			%>
			<c:forEach items="${requestScope.metaGroupList}" var="metaGroupList">
				<row>
					<id negative="false" clickable="false">${metaGroupList.id.mgroupId}</id>
					<name negative="false" clickable="false">${metaGroupList.mgroupName}</name>
				</row>
			</c:forEach>
			<%
				}
			%>
		</rows>
	</grid>
	<selects>
		<select id="types">
			<c:forEach items="${requestScope.typeList}" var="typeList">
				<option value="${typeList.value}" selected="${'Entity' == typeList.value ? '1' : '0'}">
						${typeList.label}
				</option>
			</c:forEach>
		</select>

		<select id="entity">
			<c:forEach items="${requestScope.entityCollection}" var="entityCollection">
				<option value="${entityCollection.value}"
						selected="${entityCollection.value == requestScope.entityId ? '1' : '0'}">${entityCollection.label}
				</option>
			</c:forEach>
		</select>
	</selects>
</forecastmonitor>