<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
		
		<title>
			<fmt:message key="label.accountattributelastvalues.title.window" />
		</title>
		<style>
			body {
				margin: 0px;
				overflow: hidden
			}
		</style>
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
	<body onunload="closeChild()">
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			requestURL=requestURL.substring(0,idy+1) ;
			var screenRoute = "AccountAttributeLastValues";
			var menuAccessId = '${requestScope.menuAccessId}';
			var entityId='${entityId}';
			var entityName='${entityName}';
			var accountId='${accountId}';
			var accountName='${accountName}';
			var currencyCode='${currencyCode}';
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();									
			};
			
			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
	
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(actionMethod){
				var param = '/' + appName + '/accountAttribute.do?method='+actionMethod;
				mainWindow = window.open (param, 'accountAttributeMaintenance','left=10,top=230,width=700,height=500,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');	
				return false;
			}
			
			/**
			  * help
			  * This function opens the help screen 
			  * @return none
			  */
			function help(){
				openWindow(buildPrintURL('print','Account Attribute Latest Values '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
			}
			
			/**
			 * lockTemplate
			 * @param templateId
			 * @param templateName
			 * @param userId
			 * @param isPublic
			 * Method to load child screens
			 */
			function lockTemplate(templateId,templateName,userId,isPublic){
			 			var param = "/forecastMonitorTemplate.do?method=lockTemplate";
				param = param+"&templateId="+templateId;
				param = param+"&templateName="+templateName;
				param = param+"&userId="+userId;
				param = param+"&isPublic="+isPublic;
				
				var oXMLHTTP = new XMLHttpRequest();
				var sURL = requestURL + appName+param;
				
				oXMLHTTP.open( "POST", sURL, false );
				oXMLHTTP.send();
				var str=oXMLHTTP.responseText;
				return str;
				
			}
			
			/**
			 * callBack
			 * This function used refresh data grid
			 */
			function refreshGridData(){
				Main.refreshdetails();
			}
			
			/**
			 * closeChild
			 * This function used to close the child window
			 */
			function closeChild(){
				if(typeof(mainWindow) == window)  
				mainWindow.close();
			}
				
		</script>
<%@ include file="/angularscripts.jsp"%>
		<form id="exportDataForm" target="tmp" method="post">
			<input type="hidden" name="data" id="exportData" />
			<input type="hidden" name="screen" id="exportDataScreen"
				   value="<fmt:message key="label.accountattributelastvalues.title.window"/>" />
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>