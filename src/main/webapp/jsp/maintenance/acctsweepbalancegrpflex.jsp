<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<%
	//variable declaration
	String entityId = "";
    String currencyCode = "";
    String accountId = "";
    String methodName = "";
	//get the all params from request attribute 
	if (request.getAttribute("entityId") != null) {
		entityId = request.getAttribute("entityId").toString();
	}
	
	if (request.getAttribute("currencyCode") != null) {
		currencyCode = request.getAttribute("currencyCode").toString();
	}
	
	if (request.getAttribute("accountId") != null) {
	 accountId = request.getAttribute("accountId").toString();
	}

	if (request.getAttribute("methodName") != null) {
		methodName = request.getAttribute("methodName").toString();
	}

%>

<html lang="en">
<head>
<meta charset="utf-8">
<title>Account Sweep Balance Group  - SMART-Predict</title>
<!-- <base href="./"> -->
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body  onunload="closeHandler()">
<%@ include file="/angularJSUtils.jsp"%>

<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var entityId = "<%= entityId %>";
var currencyCode = "<%= currencyCode %>";
var accountId = "<%= accountId %>";
var methodName = "<%= methodName %>";
var baseURL = new String('<%=request.getRequestURL()%>');
var screenRoute = "AcctSweepBalGrp";

/**
 * closeChild
 * This function used to close the child window
 */
function closeHandler(){
	window.opener.postMessage(['getAssAcctSweepBalGrpSize', []], "*");
	self.close();
}


 </script>
<%@ include file="/angularscripts.jsp"%>
</body>
</html>