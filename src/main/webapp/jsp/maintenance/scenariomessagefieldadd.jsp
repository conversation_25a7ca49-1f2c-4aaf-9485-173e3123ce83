<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:if test="${'save' == requestScope.methodName}">

	<fmt:message key="messagefieldformat.addScreen"/>

</c:if>
<c:if test="${'save' != requestScope.methodName}">

	<c:if test="${'update' == requestScope.methodName}">

		<fmt:message key="messagefieldformat.changeScreen"/>

</c:if>

</c:if>

<c:if test="${'save' != requestScope.methodName}">

<c:if test="${'update' != requestScope.methodName}">

<fmt:message key="messagefieldFormats.viewScreen"/>

</c:if>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
/**
 * When document is loaded, we add 'is-disabled' class for each input disabled.
 * Find each 'tr' that contains a 'td'  has an input
 * which value contains  '*',
 * then apply the style 'required' for each empty input
 *
 *Added by Med Amine Ben Ahmed
 **/
 $(window).on("load",function(){
   	$("input[disabled='disabled'][type='text']").addClass('is-disabled ');
   	$("tr:has(td:has(input[value*= '*']))").each(function()
			{
				var tr = $(this);
				tr.find('input').css('border-color',function(index){
											if ($(this).val() == '')
												return 'red';
										}).change(function(){
											if ($(this).val() == '')
												$(this).css('border','red 1px solid');
									  		else
											{$(this).css('border-color','');
											$(this).addClass('inputText');}
										});
			});
});
/**
 * When document is ready, we attach a handler to a click event for the radio input
 * to update style && find each 'tr' that contains a 'td' which text ends with '*'
 * then apply the style 'required' for each empty input
 *
 * Added by Med Amine Ben Ahmed
 **/
$(document).ready(function(){

	   $("input[type='radio']").on('click', function(){
		 	$("input[type='text']").removeClass('is-disabled ');
		   	$("input[disabled='disabled'][type='text']").addClass('is-disabled ');
		    $("input[type='text']").css('border-color','');

		    $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function()
			{
				var tr = $(this);
				tr.find('input').css('border-color',function(index){
											if ($(this).val() == '')
												return 'red';
										}).change(function(){
											if ($(this).val() == '')
												$(this).css('border','red 1px solid');
									  		else
									  		{$(this).css('border-color','');
											$(this).addClass('inputText');}
										});

			});
		});


});

mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";

var fieldType = "";
var formatId = '${requestScope.formatId}';
var scenarioId = '${requestScope.scenarioId}';
var formatType = '${sessionScope.formatTypeInSession}';
var selectedSeqNo = '${requestScope.selectedSeqNo}';
var selectedLineNo = '${requestScope.selectedLineNo}';
var isSeqNoExisting = '${requestScope.isSeqNoExisting}';
var isLineNoExisting = '${requestScope.isLineNoExisting}';
var selectedStartPos = '${requestScope.selectedStartPos}';
var selectedEndPos = '${requestScope.selectedEndPos}';
<c:if test="${'yes' == requestScope.parentFormRefresh}">

	window.opener.document.forms[0].method.value="scenarioFormatDisplay";
	window.opener.document.forms[0].isSeqNoExisting.value = isSeqNoExisting;
	window.opener.document.forms[0].submit();
self.close();


</c:if>

<c:if test="${'Y' == requestScope.disableEndPos}">

//alert('disabled !')

</c:if>
function submitForm(methodName){
if(methodName=="save"){
	methodName = "scenarioFormatSave";
}else if(methodName=="update"){
	methodName = "scenarioFormatUpdate";

}
 <c:if test="${'Y' == requestScope.showPosition}">

	  <c:if test="${'Y' == requestScope.showLineNo}">

		<c:if test="${'Y' == requestScope.disableEndPos}">
      {

			if (validateFormFormatTypeTaggedVariable(document.forms[0])) {

			if(validateField(document.forms[0].elements['scenarioMessageFields.lineNoAsString'],'message.SequenceNumber','numberPat'))
			{
				if(validateField(document.forms[0].elements['scenarioMessageFields.startPos'],'scenarioMessageFields.endPos','numberPat'))
				{
				if(!(checkLineNoForPositiveValue()))
				{
					alert('<fmt:message key="messagefieldadd.alert.LineNumber"/>');
					document.forms[0].elements["scenarioMessageFields.lineNoAsString"].focus();

				}
				else
				  {

					document.forms[0].method.value = methodName;
					document.forms[0].elements["scenarioMessageFields.lineNoAsString"].disabled = false;
					document.forms[0].elements["scenarioMessageFields.seqNoAsString"].disabled = false;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].selectedLineNo.value = selectedLineNo;
					document.forms[0].selectedStartPos.value = selectedStartPos;
					document.forms[0].selectedEndPos.value = selectedEndPos;
					document.forms[0].elements["scenarioMessageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["scenarioMessageFields.value"].value);
					document.forms[0].submit();
				}
                }else{
				document.forms[0].elements['scenarioMessageFields.startPos'].focus();
                }
				}else{
				document.forms[0].elements['scenarioMessageFields.lineNoAsString'].focus();
				}
			}

			}

</c:if>
			<c:if test="${'Y' != requestScope.disableEndPos}">

			if(validateFormFormatTypeTagged(document.forms[0]))
			{
			if(validateField(document.forms[0].elements['scenarioMessageFields.lineNoAsString'],'message.SequenceNumber','numberPat'))
			{
				if(validateField(document.forms[0].elements['scenarioMessageFields.startPos'],'scenarioMessageFields.endPos','numberPat'))
				{
				if(validateField(document.forms[0].elements['scenarioMessageFields.endPos'],'scenarioMessageFields.endPos','numberPat'))
				{
				if(!(checkLineNoForPositiveValue()))
				{
					alert('<fmt:message key="messagefieldadd.alert.LineNumber"/>');
					document.forms[0].elements["scenarioMessageFields.lineNoAsString"].focus();

				}else if(comparePosition() == "0")
				{
					alert('<fmt:message key="messagefieldadd.alert.StartPosition"/>');
					document.forms[0].elements["scenarioMessageFields.startPos"].focus();

				}else if(comparePosition() == "1")
				{
					alert('<fmt:message key="messagefieldadd.alert.EndPosition"/>');
					document.forms[0].elements["scenarioMessageFields.endPos"].focus();

				}else if(comparePosition() == "2")
				{

					alert('<fmt:message key="messagefieldadd.alert.EPSP"/>');
				}else if(!isHex()) {
					alert('<fmt:message key="alert.comboBox.invalidValue"/>');
					document.forms[0].elements["scenarioMessageFields.value"].focus();
				}
				else if(!valueLengthValidate())
				{
					alert('<fmt:message key="messagefieldadd.alert.space"/>');
					if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
						document.forms[0].elements["scenarioMessageFields.value"].focus();

				}else {
					document.forms[0].method.value = methodName;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].elements["scenarioMessageFields.lineNoAsString"].disabled = false;
					document.forms[0].selectedLineNo.value = selectedLineNo;

					document.forms[0].selectedStartPos.value = selectedStartPos;

					document.forms[0].selectedEndPos.value = selectedEndPos;
					document.forms[0].elements["scenarioMessageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["scenarioMessageFields.value"].value);
					document.forms[0].submit();
				}
		}else{
		document.forms[0].elements['scenarioMessageFields.endPos'].focus();
        }
        }else{
		document.forms[0].elements['scenarioMessageFields.startPos'].focus();
        }
		}else{
		document.forms[0].elements['scenarioMessageFields.lineNoAsString'].focus();
		}
		}

</c:if>

</c:if>

	  <c:if test="${'Y' != requestScope.showLineNo}">

		if(validateFormFixed(document.forms[0]))
			{
					if( validateField(document.forms[0].elements['scenarioMessageFields.startPos'],'message.StartPosition','numberPat'))
					{
					if( validateField(document.forms[0].elements['scenarioMessageFields.endPos'],'scenarioMessageFields.endPos','numberPat'))
					{
				if(comparePosition() == "0")
				{
					alert('<fmt:message key="messagefieldadd.alert.StartPosition"/>');
					document.forms[0].elements["scenarioMessageFields.startPos"].focus();
				}else
				if(comparePosition() == "1")
				{
					alert('<fmt:message key="messagefieldadd.alert.EndPosition"/>');
					document.forms[0].elements["scenarioMessageFields.endPos"].focus();
				}else
				if(comparePosition() == "2")
				{
					alert('<fmt:message key="messagefieldadd.alert.EPSP"/>');
				}
				else if(!isHex()) {
					alert('<fmt:message key="alert.comboBox.invalidValue"/>');
					document.forms[0].elements["scenarioMessageFields.value"].focus();
				}else if(!valueLengthValidate()) {
					alert('<fmt:message key="messagefieldadd.alert.space"/>');
					if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
						document.forms[0].elements["scenarioMessageFields.value"].focus();
				}else

				  {
					document.forms[0].method.value = methodName;
					document.forms[0].selectedSeqNo.value = selectedSeqNo;
					document.forms[0].selectedLineNo.value = selectedLineNo;
					document.forms[0].elements["scenarioMessageFields.startPos"].disabled = false;
					document.forms[0].selectedStartPos.value = selectedStartPos;
					document.forms[0].selectedEndPos.value = selectedEndPos;
					document.forms[0].elements["scenarioMessageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["scenarioMessageFields.value"].value);
					document.forms[0].submit();
				}
				}else
				{
				document.forms[0].elements['scenarioMessageFields.endPos'].focus();

				}
				}else
				{
					document.forms[0].elements['scenarioMessageFields.startPos'].focus();
				}
		}

</c:if>

</c:if>

<c:if test="${'Y' != requestScope.showPosition}">

	if(validateFormFormatTypeDelimited(document.forms[0]))
	{
	 if(validateField(document.forms[0].elements['scenarioMessageFields.seqNoAsString'],'message.SequenceNumber','numberPat'))
	 {

		if(!(checkSeqNoForPositiveValue()))
		{
			alert('<fmt:message key="messagefieldadd.alert.sequenceNo"/>');
			document.forms[0].elements["scenarioMessageFields.seqNoAsString"].focus();

		}else
		{
			document.forms[0].method.value = methodName;
			document.forms[0].elements["scenarioMessageFields.seqNoAsString"].disabled = false;
			document.forms[0].selectedSeqNo.value = selectedSeqNo;
			document.forms[0].selectedLineNo.value = selectedLineNo;
			document.forms[0].selectedStartPos.value = selectedStartPos;
			document.forms[0].selectedEndPos.value = selectedEndPos;
			document.forms[0].elements["scenarioMessageFields.value"].value =  getMenuWindow().encode64(document.forms[0].elements["scenarioMessageFields.value"].value);
			document.forms[0].submit();
		}

	}else
	{
		document.forms[0].elements['scenarioMessageFields.seqNoAsString'].focus();
	}
	}


</c:if>

}

 function checkFieldType()
 {


	fieldType = '${requestScope.fieldType}'

	if( fieldType == "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "visible";
		document.forms[0].elements["scenarioMessageFields.value"].value = "";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "hidden";
	}
 }

function insertValidate() {
	document.forms[0].insertFlag.value = '${requestScope.insertOperation}';

	if (document.forms[0].elements["insertFlag"].value == "Y") {

	 if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>") {
	 document.forms[0].elements["scenarioMessageFields.startPos"].disabled = true;

	}else if (formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>") {
				document.forms[0].elements["sequenceNoMandatory"].value = "";
				document.forms[0].elements["scenarioMessageFields.lineNoAsString"].disabled = "true";
				document.forms[0].elements["lineNoMandatory"].value = "";

		} else if( formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>"){
				document.forms[0].elements["scenarioMessageFields.lineNoAsString"].disabled = "true";
				document.forms[0].elements["lineNoMandatory"].value = "";
		} else{

				document.forms[0].elements["scenarioMessageFields.seqNoAsString"].disabled = "true";
				document.forms[0].elements["sequenceNoMandatory"].value = "";
	}
 }
}
function validateFormFixed(objForm){
  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["scenarioMessageFields.startPos"];
  elementsRef[1] = objForm.elements["scenarioMessageFields.endPos"];
	if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[2] = objForm.elements["scenarioMessageFields.value"];
  else
	 elementsRef[2] = objForm.elements["scenarioMessageFields.valueKeyWord"];

  return validate(elementsRef);
}


function validateFormFormatTypeDelimited(objForm){

  var elementsRef = new Array(2);

  elementsRef[0] = objForm.elements["scenarioMessageFields.seqNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[1] = objForm.elements["scenarioMessageFields.value"];
  else
	 elementsRef[1] = objForm.elements["scenarioMessageFields.valueKeyWord"];


  return validate(elementsRef);
}

function validateFormFormatTypeTagged(objForm){

  var elementsRef = new Array(4);
  elementsRef[0] = objForm.elements["scenarioMessageFields.startPos"];
  elementsRef[1] = objForm.elements["scenarioMessageFields.endPos"];
  elementsRef[2] = objForm.elements["scenarioMessageFields.lineNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[3] = objForm.elements["scenarioMessageFields.value"];
  else
	 elementsRef[3] = objForm.elements["scenarioMessageFields.valueKeyWord"];

  return validate(elementsRef);
}

function validateFormFormatTypeTaggedVariable(objForm){

  var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["scenarioMessageFields.startPos"];
  elementsRef[1] = objForm.elements["scenarioMessageFields.lineNoAsString"];
  if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	 elementsRef[2] = objForm.elements["scenarioMessageFields.value"];
  else
	 elementsRef[2] = objForm.elements["scenarioMessageFields.valueKeyWord"];

  return validate(elementsRef);
}

function showKeyWordsDropDown(element)
{

	fieldType = element.value;

	if(element.value == "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "visible";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "hidden";
	}else
	{
		document.getElementById("MessageFieldsAddDropDown").style.visibility = "hidden";
		document.getElementById("MessageFieldsAddTextField").style.visibility = "visible";
		document.forms[0].elements["scenarioMessageFields.value"].value = "";
	}
}

function valueLengthValidate()
{
	var temp = document.forms[0].elements["scenarioMessageFields.startPos"].value;
	//Removing zeros which are present before first non zero digits in the start position field
	var i = 0;
	for(i=0; i<temp.length ; i++)
			{
				if (temp.charAt(i) != '0')
				{
						break;
				}
			}
	temp = temp.substring(i);
	var startPosition = parseInt(temp);
	temp = document.forms[0].elements["scenarioMessageFields.endPos"].value;
	//Removing zeros which are present before first non zero digits in the end position field
	var i = 0;
	for(i=0; i<temp.length ; i++)
			{
				if (temp.charAt(i) != '0')
				{
						break;
				}
			}
	temp = temp.substring(i);
	var endPosition = parseInt(temp);
	 var diff = endPosition - startPosition +1;
	 var textLength ;

	 if(fieldType != "<%=SwtConstants.FIELD_TYPE_KEYWORD%>")
		   textLength = document.forms[0].elements["scenarioMessageFields.value"].value;
	 else{
		if(startPosition > 0){
			return true;
		}else{
			return false;
		}
	 }
	 if(fieldType == "<%=SwtConstants.FIELD_TYPE_HEXADECIMAL%>") {
		 if( diff >= (textLength.length/2))
		 {

			return true;
		 }
		 else
		 {
			 return false;
		 }
	 }else {
	 if( diff >= textLength.length)
	 {
		return true;
	 }
	 else
	 {
		 return false;
	 }
	}
}


function isHex() {
	 var textValue ;
	 textValue = document.forms[0].elements["scenarioMessageFields.value"].value;
	 if(fieldType == "<%=SwtConstants.FIELD_TYPE_HEXADECIMAL%>"){
		 if((textValue.length)%2 == 0) {
			var a = parseInt(textValue,16);
			return (a.toString(16).toUpperCase() === textValue.toUpperCase())
		 }else{
			return false;
		 }

	}else {
		return true;
	}
}

function comparePosition()
{

var temp = document.forms[0].elements["scenarioMessageFields.startPos"].value;
//Removing zeros which are present before first non zero digits in the start position field
var i = 0;
for(i=0; i<temp.length ; i++)
		{
			if (temp.charAt(i) != '0')
			{
					break;
			}
		}
temp = temp.substring(i);
var startPosition = parseInt(temp);
temp = document.forms[0].elements["scenarioMessageFields.endPos"].value;
//Removing zeros which are present before first non zero digits in the end position field
var i = 0;
for(i=0; i<temp.length ; i++)
		{
			if (temp.charAt(i) != '0')
			{
					break;
			}
		}
temp = temp.substring(i);
var endPosition = parseInt(temp);

   if(startPosition == 0)
	{
	return "0";
	}else if(endPosition == 0)
	 {
	return "1";
	}else if(startPosition > endPosition)
	{
			 return "2";
	}
	else
	{
		return "3";
	}
}

function checkLineNoForPositiveValue()
{
	var lineNo = document.forms[0].elements["scenarioMessageFields.lineNoAsString"].value;
	if(lineNo != "0")
	  return true;
	 else
	  return false;
}

function checkSeqNoForPositiveValue()
{
	 var seqNo = document.forms[0].elements["scenarioMessageFields.seqNoAsString"].value;
	  if(seqNo != "0")
		return true;
	  else
		return false;
}

function checkformatType()
{
		 <c:if test="${'yes' != requestScope.isViewField}">

			 if(formatType == "<%=SwtConstants.FORMAT_TYPE_FIXED%>" || formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>"
			  ||formatType == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>")
			 {
				document.forms[0].elements["scenarioMessageFields.seqNoAsString"].disabled = "true";
				document.forms[0].elements["sequenceNoMandatory"].value = "";
			 }else
				document.forms[0].elements["sequenceNoMandatory"].value = "*";

</c:if>
}
</SCRIPT>

</head>
<!--code modified by sandeepkumar for mantis 2093:value changed while range overlapping -->
<body leftmargin="0" topmargin="0" marginheight="0"  onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}'); checkFieldType(); checkformatType();insertValidate();" onunload="call()">
<form action="messagefields.do" method="post"  onsubmit="return validate(this);">
<input name="oldValue" type="hidden" value= "">
<input name="method" type="hidden" value="save">
<input name="formatId" type="hidden" value="save">
<input name="formatType" type="hidden" value="save">
<input name="scenarioId" type="hidden" value="save">
<input name="selectedSeqNo" type="hidden" value="">
<input name="selectedLineNo" type="hidden" value="">
<input name="selectedStartPos" type="hidden" value="">
<input name="selectedEndPos" type="hidden" value="">
<input name="insertFlag" type="hidden" value="">

<div id="MessageFieldsAdd" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:20px; width:640px; height:133px;">
	<div id="MessageFieldsAdd" style="position:absolute;z-index:99;left:8px; top:4px; width:457px; height:10px;">
	  <table width="475px" height="70px" border="0" cellpadding="0" cellspacing="1" class="content">
		<tr heigth="25px">
		 <c:if test="${'Y' == requestScope.showLineNo}">

		   <c:if test="${'yes' == requestScope.isViewField}">

			 <td width="128px"><b><fmt:message key="messageFields.lineNoDisplay"/></b></td>

</c:if>

		    <c:if test="${'yes' != requestScope.isViewField}">

			 <td width="128px"><b><fmt:message key="messageFields.lineNoDisplay"/></b></b><input  value = "*" class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="lineNoMandatory" size="1"></td>

</c:if>

</c:if>

		   <c:if test="${'Y' != requestScope.showLineNo}">

			 <td width="128px"><b><fmt:message key="messageFields.lineNoDisplay"/></b></td>

</c:if>

		 <c:if test="${'Y' == requestScope.showLineNo}">

			  <td width="22px"><input type="text" tabindex="1" titleKey="tooltip.selectLine" name="scenarioMessageFields.lineNoAsString" value="${scenarioMessageFields.lineNoAsString}"  style="width:45px;" class="htmlTextNumeric" maxlength = "5" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> onchange="return validateField(this,'message.SequenceNumber','numberPat');" /> </td>

</c:if>

		  <c:if test="${'Y' != requestScope.showLineNo}">

			  <td width="22px"><input type="text" tabindex="1"  class="htmlTextNumeric" disabled = "true" name="scenarioMessageFields.lineNoAsString" value="${scenarioMessageFields.lineNoAsString}"   titleKey="tooltip.selectLine" style="width:45px;" maxlength = "5" onchange="return validateField(this,'message.SequenceNumber','numberPat');" /> </td>

</c:if>

	   </tr>
		 	<tr heigth="25px">
		    <td width="128px"><nobr><b><fmt:message key="messageFields.sequenceNo"/></b><input  class="textAlpha" style="background:transparent; border: thin;" class="textAlpha" readonly name="sequenceNoMandatory" size="1"></nobr></td>
		    <td ><input type="text" tabindex="2" titleKey="tooltip.enterSeqNo" class="htmlTextNumeric" maxlength = "5" name="scenarioMessageFields.seqNoAsString" value="${scenarioMessageFields.seqNoAsString}"  style="width:45px;" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> onchange="return validateField(this,'message.SequenceNumber','numberPat');" /></td>
		</tr>

	<tr heigth="25px">
			  <td width="128px"><b><fmt:message key="message.FieldType"/></b></td>
			  <td width="250px">
				<input type="radio"titleKey="tooltip.selectText" id="1" style="width:13;" tabindex="3" name="scenarioMessageFields.fieldType" value="${SwtConstants.FIELD_TYPE_TEXT}"  ${scenarioMessageFields.fieldType == SwtConstants.FIELD_TYPE_TEXT ? 'checked="checked"' : ''}  <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="3" title='<fmt:message key="tooltip.selectText"/>' Select text" for="1"><fmt:message key="messageFields.fieldType.text"/></label>&nbsp;&nbsp;&nbsp;
				<input type="radio" titleKey="tooltip.selectKeyword" id="2" style="width:13;" tabindex="4" name="scenarioMessageFields.fieldType" value="${SwtConstants.FIELD_TYPE_KEYWORD}"  ${scenarioMessageFields.fieldType == SwtConstants.FIELD_TYPE_KEYWORD ? 'checked="checked"' : ''}  <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="4" title='<fmt:message key="tooltip.selectKeyword"/>' for="2"><fmt:message key="messageFields.fieldType.keyword"/></label>&nbsp;&nbsp;&nbsp;
				<input type="radio" titleKey="tooltip.selectHexa" id="3" style="width:13;" tabindex="5" name="scenarioMessageFields.fieldType" value="${SwtConstants.FIELD_TYPE_HEXADECIMAL}"  ${scenarioMessageFields.fieldType == SwtConstants.FIELD_TYPE_HEXADECIMAL ? 'checked="checked"' : ''}  <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> onclick = "showKeyWordsDropDown(this)" />
					<label tabindex="5" title='<fmt:message key="tooltip.selectHexa"/>' for="3"><fmt:message key="messageFields.fieldType.hexaDecimal"/></label></td>
			</tr>
	  </table>
	</div>


	<div id="MessageFieldsAddTextField" style="position:absolute;z-index:99;left:8px; top:73px; width:457px; height:5px; visibility:visible ">
		 <table width="626px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="25px" >
			<c:if test="${'yes' == requestScope.isViewField}">

			  <td width="128px"><b><fmt:message key="message.Value"/></b></td>

</c:if>

			<c:if test="${'yes' != requestScope.isViewField}">

			  <td width="128px"><b><fmt:message key="message.Value"/></b>*</td>

</c:if>

			  <td width="45px">&nbsp;</td>
			  <td width="445px">
				<input type="text" tabindex="6" titleKey="tooltip.enterValue" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> maxlength = "50" class="htmlTextAlpha" name="scenarioMessageFields.value" value="${fn:escapeXml(scenarioMessageFields.value)}"  style="width:440px;" />
			  </td>
			</tr>
		 </table>
	</div>

<div id="MessageFieldsAddDropDown" style="position:absolute;z-index:99;left:8px; top:73px; width:457px; height:5px; visibility:hidden">
		 <table width="437px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr  height="25px">
			 <c:if test="${'yes' == requestScope.isViewField}">

			  <td width="120px"><b><fmt:message key="message.Value"/></b></td>

</c:if>

			<c:if test="${'yes' != requestScope.isViewField}">

			  <td width="120px"><b><fmt:message key="message.Value"/></b>*</td>

</c:if>
			  <td width="36px">&nbsp;</td>
			  <td width="230px">
			<select id="scenarioMessageFields.valueKeyWord" name="scenarioMessageFields.valueKeyWord"
				<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  tabindex="3" titleKey="tooltip.enterValue"
				style="width:190px " >
				<c:forEach items="${requestScope.keyWords}" var="word">
				   <option
					  value="${word.value}"
					  <c:if test="${scenarioMessageFields.valueKeyWord == word.value}">selected="selected"</c:if>>
					  ${word.label}
				   </option>
				</c:forEach>
			</select>

			  </td>
			</tr>
		 </table>
</div>
<div id="MessageFieldsAdd" style="position:absolute;z-index:99;left:8px; top:96px; width:457px; height:10px;">
 <table width="438px" height="20px" border="0" cellpadding="0" cellspacing="1" class="content">
	<tr height="25px">
		 <c:if test="${'Y' == requestScope.showPosition}">

			<c:if test="${'yes' == requestScope.isViewField}">

				<td width="120px"><b><fmt:message key="message.StartPosition"/></b></td>

</c:if>

			<c:if test="${'yes' != requestScope.isViewField}">

				<td width="120px"><b><fmt:message key="message.StartPosition"/></b>*</td>

</c:if>


</c:if>

		  <c:if test="${'Y' != requestScope.showPosition}">

			 <td width="120px"><b><fmt:message key="message.StartPosition"/></b></td>

</c:if>

		  <td width="36px">&nbsp;</td>

		  <c:if test="${'Y' == requestScope.showPosition}">

			 <td width="38px"> <input type="text" titleKey="tooltip.enterStartPosition" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> class="htmlTextNumeric" name="scenarioMessageFields.startPos" value="${scenarioMessageFields.startPos}"  maxlength = "4"  style="width:37px;" tabindex="7"  onchange="return validateField(this,'message.StartPosition','numberPat');" /></td>

</c:if>

		   <c:if test="${'Y' != requestScope.showPosition}">

			  <td width="38px"> <input type="text" titleKey="tooltip.enterStartPosition" class="htmlTextNumeric" maxlength = "4" disabled = "true" name="scenarioMessageFields.startPos" value="${scenarioMessageFields.startPos}"   tabindex="7" style="width:37px;" onchange="return validateField(this,'message.StartPosition','numberPat');" /></td>

</c:if>

		  <td width="28px">&nbsp;</td>
		   <c:if test="${'Y' == requestScope.showPosition}">

			 <c:if test="${'yes' == requestScope.isViewField}">

				<td width="90px"><b><fmt:message key="message.EndPosition"/></b></td>

</c:if>

			<c:if test="${'yes' != requestScope.isViewField}">

			  <c:if test="${'Y' == requestScope.disableEndPos}">

				<td width="90px"><b><fmt:message key="message.EndPosition"/></b></td>

</c:if>

			  <c:if test="${'Y' != requestScope.disableEndPos}">

				<td width="90px"><b><fmt:message key="message.EndPosition"/></b>*</td>

</c:if>


</c:if>

</c:if>

		 <c:if test="${'Y' != requestScope.showPosition}">

			 <td width="90px"><b><fmt:message key="message.EndPosition"/></b></td>

</c:if>

		  <td width="25px">&nbsp;</td>
		  <c:if test="${'Y' == requestScope.showPosition}">

		    <c:if test="${'Y' == requestScope.disableEndPos}">

				 <td width="47px"><input type="text" titleKey="tooltip.enterEndPosition" disabled = "true" class="htmlTextNumeric" maxlength = "4" name="scenarioMessageFields.endPos" value="${scenarioMessageFields.endPos}"  tabindex="8" style="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');" /></td>

</c:if>

			<c:if test="${'Y' != requestScope.disableEndPos}">

				 <td width="47px"><input type="text" titleKey="tooltip.enterEndPosition" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> class="htmlTextNumeric" maxlength = "4" name="scenarioMessageFields.endPos" value="${scenarioMessageFields.endPos}"  tabindex="8" style="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');" /></td>

</c:if>


</c:if>

		   <c:if test="${'Y' != requestScope.showPosition}">

			 <td width="47px"><input type="text" titleKey="tooltip.enterEndPosition" class="htmlTextNumeric" maxlength = "4" disabled = "true" name="scenarioMessageFields.endPos" value="${scenarioMessageFields.endPos}"  tabindex="8" style="width:37px;" onchange="return validateField(this,'message.EndPosition','numberPat');" /></td>

</c:if>
	   </tr>
 </table>
 <div>

</div>
</div>
<div id="MessageFieldsAdd" style="position:absolute; left:563; top:146px; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				 <a tabindex="11" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Message Field Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			 </td>

			<td align="right" id="Print">
				<a tabindex="11" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:-2px; top:136px; width:640px; height:39px; visibility:visible;">
 <div id="MessageFieldsAdd" style="position:absolute; left:0px; top:4; width:140px; height:15px; visibility:visible;">
   <c:if test="${'yes' != requestScope.isViewField}">

		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="savebutton" width="70px" title='<fmt:message key="tooltip.ok"/>' >
				<a tabindex="9" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('${requestScope.methodName}');"><fmt:message key="button.ok"/></a>
			</td>
			<td id="cancelbutton" width="70px" title='<fmt:message key="tooltip.cancel"/>' >
				<a tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();"><fmt:message key="button.cancel"/></a>
				</td>
		</tr>
	   </table>

</c:if>

  <c:if test="${'yes' == requestScope.isViewField}">

		<table width="70" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td id="cancelbutton" width="70px" title='<fmt:message key="tooltip.close"/>'>
				<a tabindex="10" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.close"/></a>
				</td>
		</tr>
	   </table>

</c:if>

  </div>

 <script language = "javascript">
<c:if test="${'yes' == requestScope.isSeqNoExisting}">

	checkformatType();
	alert('<fmt:message key="messagefieldadd.alert.duplicateSeqNo"/>');


</c:if>

<c:if test="${'yes' == requestScope.isRangeOverlapping}">

	alert('<fmt:message key="messagefieldadd.alert.rangeOverlapping"/>');


</c:if>

<c:if test="${'Y' == requestScope.recordExists}">

	alert('<fmt:message key="messagefieldadd.alert.isStartPosExisting"/>');


</c:if>

</script>

</form>
</body>
</html>