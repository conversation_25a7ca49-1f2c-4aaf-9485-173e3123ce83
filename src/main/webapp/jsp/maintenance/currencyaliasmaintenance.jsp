<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>

<html>

<head>
<title><fmt:message key="currencyAliasMaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">


/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

//var entityAccess = "${requestScope.EntityAccess}";
function disableAllButtons()
{
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function bodyOnLoad()
{
	/* Start:Vivekanandan:13/11/2008:Modified
	 * Modified Number as String to fix currency name filtering*/
	xl = new XLSheet("currencyAliasDetails","table_2", ["String","String", "String"],"111");
	/* End:Vivekanandan:13/11/2008:Modified
	 * Modified Number as String to fix currency name filtering*/
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("currencyAliasDetails");	
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyAlias.id.entityId"],document.getElementById("entityName"));	
	//alert(menuEntityCurrGrpAccess);
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0"){
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
	}else{
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;	
	}
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	//document.getElementById("currencybutton").innerHTML = document.getElementById("currencydisablebutton").innerHTML;
		
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
		if (yourstate==true){ 
		document.forms[0].entityCode.value = document.forms[0].elements["currencyAlias.id.entityId"].value;
		document.forms[0].submit();
		}
}

function buildAddCurrencyAlias(methodName){


	var param = 'currencyalias.do?method='+methodName+'&entityCode=';
	param +=  document.forms[0].elements["currencyAlias.id.entityId"].value;
	param += '&entityText=' + document.getElementById('entityName').innerText;
	param += '&entityId=' + document.forms[0].elements["currencyAlias.id.entityId"].value;
	return  param;	
}




function onSelectTableRow(rowElement, isSelected)
{	
	var hiddenElement0 = rowElement.cells[0].innerText;	
	var hiddenElement1 = rowElement.cells[1].innerText;	
	document.forms[0].selectedAliasId.value = hiddenElement0.trim();	
	document.forms[0].selectedCurrencyCode.value = hiddenElement1.trim();	
	
	
	var count = getCountRowsSelected(rowElement);
	
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0" && count == 1)
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	{		
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;		
		
	}else{		
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
	}

	
}

</SCRIPT>
</head>

<form action="currencyalias.do">
<input name="method" type="hidden" value="display">
<input name="entityCode" type="hidden" >
<input name="selectedCurrencyCode" type="hidden" >
<input name="selectedAliasId" type="hidden" >

<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->
<input name="menuAccessId" type="hidden" >
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">

<div id="CurrencyAlias" style="position:absolute; left:20px; top:20px; width:513px; height:42px; border:2px outset;" color="#7E97AF">

<div id="CurrencyAlias" style="position:absolute; left:8px; top:4px; width:470px; height:400;">
<table width="468" border="0" cellpadding="0" cellspacing="0" height="30">
	<tr color="black" border="0">
	  <td width="38"><b><fmt:message key="bookCode.entity"/></b></td>
	  <td width="28">&nbsp;</td>
	  <td width="140px">
	  <select id="ccyAliasId" name="currencyAlias.id.entityId"
			tabindex="1" titleKey="tooltip.entityId" class="htmlTextAlpha" onchange="submitForm('unspecified')"
			style="width: 140px">
		  <c:forEach items="${requestScope.entities}" var="entity">
               <option
                  value="${entity.value}"
                  <c:if test="${currencyAlias.id.entityId == entity.value}">selected="selected"</c:if>>
                  ${entity.label}
               </option>
			</c:forEach>
		</select>

	  </td>
	   <td width="20">&nbsp;</td>
	  <td width="280">
		<span id="entityName" name="#request.entityName" class="spantext">
	   </td>
	 </tr>
</table>
</div>

</div>

<div id="CurrencyAlias" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:65px; width:513px; height:459px;">
	<div id="CurrencyAlias" style="position:absolute;z-index:50;left:0px; top:0px; width:493px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="488x" border="0" cellspacing="1" cellpadding="0"  height="20px">
	<thead>
		<tr height="20px">
		    <td  title ='<fmt:message key="aliasTooltip.sortalias"/>' width="150px" style="border-left-width: 0px;"><b><fmt:message key="currencyalias.alias"/></b></td>
			<td title ='<fmt:message key="aliasTooltip.sortcurrency"/>' width="158px"><b><fmt:message key="currencyalias.currency"/></b></td>
			<td title ='<fmt:message key="aliasTooltip.sortname"/>' width="180px"><b><fmt:message key="currencyalias.name"/></b></td>
			
	
		</tr>
	</thead>
</table>
 </div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:510px; height:455px; overflowY:scroll;">
	<div id="CurrencyAlias" style="position:absolute;z-index:99;left:0px; top:22px; width:488px; height:10px;">
		<table class="sort-table" id="currencyAliasDetails" width="493px" border="0" cellspacing="1" cellpadding="0" height="433">
		<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="currency" items="${requestScope.currencyAliasDetails}">
				<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
					<td align="left" width="150px">
						<input type="hidden" name="id.alias" value="${currency.id.alias}" disabled="true" />
						${currency.id.alias}&nbsp;
					</td>
					<td width="158px" align="left">
						${currency.currencyCode}&nbsp;
					</td>
					<td width="180px" align="left">
						<input type="hidden" name="currencyName" value="${currency.currencyName}" disabled="true" />
						${currency.currencyName}&nbsp;
					</td>
				</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
 		</tbody>
	<tfoot><tr><td colspan="3" ></td></tr></tfoot>
</table>
</div>
</div>
</div>

</div>
<div id="Currency" style="position:absolute; left:456; top:537; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		    <td align="Right">
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Alias Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
            </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:528; width:513px; height:39px; visibility:visible;">
  <div id="Currency" style="position:absolute; left:6; top:4; width:513; height:15px; visibility:visible;">
  	  <table width="200" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"> </td>
		<td id="deletebutton" width="70" ></td>		
		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:513; height:15px; visibility:hidden;">  	
    <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		<td id="addenablebutton" >		
		<a tabindex="2" title='<fmt:message key="tooltip.AddNewCurrencyAlias"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddCurrencyAlias('add'),'currencyaliasmaintenancechildWindow','left=50,top=190,width=580,height=185,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
		</td>		
		<td id="adddisablebutton">
			<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
		</td>
		
		

		<td id="deleteenablebutton"  >		
			<a  tabindex="4" title='<fmt:message key="tooltip.DeleteSelectedCurrencyAlias"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><fmt:message key="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a  class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
		</td>	
	
	</tr>
    </table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>