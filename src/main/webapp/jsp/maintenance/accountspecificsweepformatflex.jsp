<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<%@ page import="org.swallow.util.SwtUtil"%>

<html>
	<head>
		<%@ include file="/taglib.jsp"%>
		<title>
			<fmt:message key="label.accountSpecificSweepFormat.title.window" />
		</title>
	
		<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		    var screenRoute = "accountspecificsweepformat";
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			var mainWindow= null;
			requestURL=requestURL.substring(0,idy+1) ;
			
			
			var menuAccessId = '${requestScope.menuAccessId}';
			
			var currencyId = '${currencyId}';
			var entityId = '${entityId}';
			var parentMethodName = '${parentMethodName}';
			var accountId = '${accountId}';
			var id = '${id}';
			
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			window.onunload = call;
			var menuAccessId = '${requestScope.menuAccessId}';
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["alert"] = new Array ();
			
			label["text"]["screen-title"] = "<fmt:message key="label.accountSpecificSweepFormat.title.window"/>";
			
			// Add button
			label["text"]["button-add"] = "<fmt:message key="button.add"/>";
			label["tip"]["button-add"] = "<fmt:message key="button.add"/>";	
			label["text"]["screen-tilte"] = "Helo heloo";	
			
			// Change button
			label["text"]["button-change"] = "<fmt:message key="button.change"/>";
			label["tip"]["button-change"] = "<fmt:message key="button.change"/>";	
			
			// View button
			label["text"]["button-view"] = "<fmt:message key="button.view"/>";
			label["tip"]["button-view"] = "<fmt:message key="button.view"/>";	
			
			// Delete button
			label["text"]["button-delete"] = "<fmt:message key="button.delete"/>";
			label["tip"]["button-delete"] = "<fmt:message key="button.delete"/>";	
			
			// Close button
			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";
			
			// Alert messages
			label["alert"]["delete-confirm"] = "<fmt:message key="alert.deletion.confirm"/>";
			label["alert"]["delete-record"] = "<fmt:message key="confirm.delete"/>";     
			label["alert"]["server_error"] = "<fmt:message key="ilmanalysismonitor.errorOnServerSide"/>";  
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(actionMethod){
				var param = '/' + appName + '/accountSpecificSweepFormat.do?method='+actionMethod
						+ '&entityId=' + entityId
						+ '&currencyId=' + currencyId
						+ '&accountId=' + accountId
						+ '&id=' + id;
				
				mainWindow = window.open (param, 'accountSpecificSweepFormat','left=10,top=230,width=700,height=330,toolbar=0, resizable=yes, //status=yes, scrollbars=yes','true');
				return false;
			}
			
			/**
			  * help
			  * This function opens the help screen 
			  * @return none
			  */
			function help(){
				openWindow(buildPrintURL('print','AccountSpecificSweepFormat'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
			}
			
			/**
			 * callBack
			 * This function used refresh data grid
			 */
			function refreshGridData(){
				//getFlashObject("mySwf").refreshdetails();
				instanceElement.refreshdetails();
			}
			
			/**
			 * closeChild
			 * This function used to close the child window
			 */
			function closeHandler(){
				window.opener.postMessage(['getSpecificAccountSweepSize', []], "*");
// 				Main.updateSpecAcctCount();
				self.close();
			}


			
		</script>
	</head>
	
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onunload="closeHandler()">
	<%@ include file="/angularJSUtils.jsp"%>
    <%@ include file="/angularscripts.jsp"%>
	</body>
</html>
