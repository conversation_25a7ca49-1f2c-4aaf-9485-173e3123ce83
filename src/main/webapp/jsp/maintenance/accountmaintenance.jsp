<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.calledFromILMCcy == 'true'}">
        <title><fmt:message key="accountmaintenance.title.selectAnAccount"/></title>
    </c:when>
    <c:when test="${requestScope.methodName != 'subAccounts'}">
        <title><fmt:message key="accountmaintenance.title.mainWindow"/></title>
    </c:when>
    <c:otherwise>
        <c:choose>
            <c:when test="${requestScope.linkedAccount == 'yes'}">
                <title><fmt:message key="title.linkedaccount"/></title>
            </c:when>
            <c:otherwise>
                <title><fmt:message key="title.subaccounts"/></title>
            </c:otherwise>
        </c:choose>
    </c:otherwise>
</c:choose>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
<SCRIPT language="JAVASCRIPT">

var method = '${requestScope.method}';
document.write('<base href="' + window.location.href  + '"" />');

$( window ).on( "load", function() {
	try{
		var f = document.createElement("form");
		f.setAttribute('id',"formAngular");
		document.body.appendChild(f);

		<% Enumeration<String> parameterNames = request.getParameterNames();

			while (parameterNames.hasMoreElements()) {

		    String paramName = parameterNames.nextElement();
		    String paramValue = request.getParameter(paramName);
		    %>
		    var paramName = '<%=paramName%>';
		    var paramValue = '<%=paramValue%>';
		    $('<input>', {
			    type: 'hidden',
			    name: paramName,
			    value: paramValue
			}).appendTo('#formAngular');
			<%
		    }
		%>
		
		  $('<input>', {
			    type: 'hidden',
			    id: 'csrf',
			    name: 'csrf',
			    value: $('meta[name=csrf_token]').attr("content")
			}).appendTo('#formAngular');
		  
		  
		  
		}catch(e){
			console.log('e',e)
		}
		$( "iframe" ).each(function( index ) {
			if($( this ).width() == 0){
				$( this ).css('visibility', 'hidden');	
			}
		});
		
		checkOpenerAndCloseWindow();
});

window.onload = function () {
    setTitleSuffix(document.forms[0]);
};

document.onkeydown = rewriteUrl;
//document.onkeyup = rewriteUrl;

function rewriteUrl(e) {
	  e = e || window.event;
	 if((e.which || e.keyCode)==116){
	        e.preventDefault();
	        if(method){
	        	 var currentUrl = window.location.href;
	       	  if(method && currentUrl.indexOf("method=") == -1)
	       		$( "#formAngular" ).submit();
	        }else 
	        	$( "#formAngular" ).submit();
	    }
}


function getMessage(label, lang){
	return	getMenuWindow().getMessage(label, lang);
}

function exportData(type , str) {
	
	document.getElementById('exportDataForm').action='flexdataexport.do?method=' + type ;
	document.getElementById('exportData').value= str;
	document.getElementById('exportDataForm').submit();
	
}


// var screenTitle = "";
// screenTitle = getMessage("label.accountattributeadd.title.window", null);
// document.title = screenTitle;
var filterstatus= "${requestScope.filterStatus}";
var sortStatus="${requestScope.sortStatus}";
var sortDescending="${requestScope.sortDescending}";
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var accountId = '${requestScope.accountId}';
var calledFromILMCcy = '${requestScope.calledFromILMCcy}';

var accountName;
var methodNameValue;

var screenRoute = "AccountMaintenance";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
 methodNameValue = "${requestScope.methodName}";
 var menuAccessId = 0;
var accountEntityName = '${requestScope.accountEntityName}';
var accountCurrencyName = '${requestScope.accountCurrencyName}';
var selectedCurrency = '${requestScope.selectedCurrency}';
var selectedEntity = '${requestScope.selectedEntity}';
var linkedAccount = '${requestScope.linkedAccount}';

function disableAllButtons()
{
	<c:if test="${requestScope.methodName != 'subAccounts'}">
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	</c:if>
	
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML; 
	
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function checkOpenerAndCloseWindow() {
	  setInterval(function() {
		if(!hasMainScreenAncestor()){
			window.close();
		}
	  }, 2000);
}


	
	
function hasMainScreenAncestor() {
	  let currentWindow = window;
	  while (currentWindow && currentWindow !== currentWindow.opener) {
	    currentWindow = currentWindow.opener;
	    if (currentWindow && currentWindow.isMainScreen === true) {
	      return true;
	    }
	  }
	  return false;
	}
	


function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["acctMaintenance.currcode"].value;	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){
	maintainSortFilterStatus();
	document.forms[0].submit();
	}
}
function enableFields(){
	document.forms[0].elements["acctMaintenance.id.accountId"].disabled = "";
	document.forms[0].elements["acctMaintenance.id.entityId"].disabled = "";
}

function buildAddaccount(methodName, selectedAccountId, selectedAccountName, entityId,  entityName ,selectedCurrencyCode  ){

// maintainSortFilterStatus();
// document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["acctMaintenance.currcode"].value;

var param = 'acctMaintenance.do?method='+methodName+'&selectedAccountId=';
	param += selectedAccountId;
	    param += '&selectedAccountName=' + selectedAccountName;
		param += '&entityCode=' + entityId;
		param += '&entityName=' +entityName;
		param += '&selectedCurrencyCode=' +selectedCurrencyCode;
<c:choose>
    <c:when test="${requestScope.linkedAccount == 'yes'}">
		param += '&linkedAccount=' +'yes';
    </c:when>
    <c:otherwise>
		param += '&linkedAccount=' +'no';
    </c:otherwise>
</c:choose>


	return  param;
}

function onSelectTableRow(rowElement, isSelected)
{

	//variable to hold hiddenelement
	var accountIdElement = rowElement.getElementsByTagName("input")[0];
	var currAccessElement = rowElement.getElementsByTagName("input")[1];
	accountName = rowElement.getElementsByTagName("td")[1].innerHTML;
	document.forms[0].selectedAccountId.value = accountIdElement.value;
	document.forms[0].selectedEntityId.value = document.forms[0].elements['acctMaintenance.id.entityId'].value;
	accountId=accountIdElement.value;
	//Enable the buttons based on the currencygroup access
	if(document.getElementById("changebutton")){
		if(menuEntityCurrGrpAccess == "0" && isSelected) {
            <c:if test="${requestScope.methodName != 'subAccounts'}">
            if (currAccessElement.value == <%=SwtConstants.CURRENCYGRP_FULL_ACCESS%>) {
                document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
                document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
                document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
            } else if (currAccessElement.value == <%=SwtConstants.CURRENCYGRP_READ_ACCESS%>) {
                document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
                document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
                document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
            } else {
                document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
                document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
                document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
            }

            </c:if>
        } else {
            document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
			}

		if(!isSelected){
			<c:if test="${requestScope.methodName != 'subAccounts'}">
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			</c:if>
				document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		}
	}else {
		if(document.getElementById("okbutton") != null){
			if(isSelected){
				document.getElementById("okbutton").innerHTML = document.getElementById("okenablebutton").innerHTML;
			}
			else {
				document.getElementById("okbutton").innerHTML = document.getElementById('okdisablebutton').innerHTML;
			}
		}
	}
}



window.addEventListener("message", receiveMessage, false);


function receiveMessage(e) {
	if(e && e.data){
		var methodName = e.data[0];
		if(methodName == "refreshParentScreen"){
			Main.refreshdetails(null);
		}
	}
	
}



function maintainSortFilterStatus(){
	return;
	document.forms[0].selectedSortStatus.value = "";
	document.forms[0].selectedSortDescending.value ="";
	document.forms[0].selectedFilterStatus.value="";
	var sortColumn = xl.dataTable.sortColumn ;
	document.forms[0].selectedSortStatus.value = sortColumn ;

	var sSortDescending =  xl.dataTable.descending;		
	document.forms[0].selectedSortDescending.value = sSortDescending ;
	
	var filterArr = new Array(5);

	if(document.getElementById("acctmaintenanceDetails").innerText != "")
	filterArr = xl.getFilterStatus();

	for(var idy = 0 ; idy < filterArr.length-1; ++idy)	
	document.forms[0].selectedFilterStatus.value += filterArr[idy] + "," ;

}
function closeAndRefreshParent(){
 	this.opener.document.forms[0].elements["ilmCcyParams.primaryAccountId"].value=accountId;
 	this.opener.document.getElementById("acctName").innerHTML=accountName;
 	this.opener.document.getElementById("primaryAccountIdSelect").options.length=0;
	var option = this.opener.document.createElement("option");
 	 var text = this.opener.document.createTextNode(accountId);
	option.appendChild(text)
	option.selected = 'selected';
	option.value = accountId;
 	this.opener.document.getElementById("primaryAccountIdSelect").appendChild(option);
	self.close();
}

var allSubWindows= new Map();
function openChildWindow(methodName, selectedAccountId, selectedAccountName, entityId,  entityName ,selectedCurrencyCode  ){
	
	//check windows status
	removeClosedWindowsFromMap();
	
	if(allSubWindows.has(selectedAccountId)){
		Main.raiseAlert();
	}else{
	var param = buildAddaccount(methodName, selectedAccountId, selectedAccountName, entityId,  entityName ,selectedCurrencyCode)
	var maintWindow = openWindow (param, 'accountsmaintenancechangewindow','left=10,top=230,width=920,height=850,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');	
	// Store the mainWindow object in the Map with the account ID as the key
	allSubWindows.set(selectedAccountId, maintWindow);
	return false;
	}
}


function removeClosedWindowsFromMap() {
	allSubWindows.forEach(function (maintWindow, accountId) {
	    if (maintWindow.closed) {
	    	allSubWindows.delete(accountId);
	    }
	  });
}

</script>
</head>
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="<fmt:message key="label.accountattributedefinition.title.window"/>" />
</form>
</html>