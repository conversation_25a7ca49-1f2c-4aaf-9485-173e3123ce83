<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.EntityPositionLevel"%>
<html>
<head>
<c:choose>
    <c:when test="${requestScope.method == 'add'}">
        <title><fmt:message key="positionlevel.addScreen"/></title>
    </c:when>
    <c:when test="${requestScope.method == 'change'}">
        <title><fmt:message key="positionlevel.changeScreen"/></title>
    </c:when>
</c:choose>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="displayPosLvl";

window.opener.document.forms[0].screenStatus.value = "${requestScope.screenStatus}";
window.opener.document.forms[0].selectedEntityId.value = "${requestScope.selectedEntityId}";
window.opener.document.forms[0].selectedSweepPosLvl.value = "${requestScope.selectedSweepPosLvl}";
window.opener.document.forms[0].selectedPreAdvPosLvl.value = "${requestScope.selectedPreAdvPosLvl}";
window.opener.document.forms[0].submit();
self.close();
</c:if>
mandatoryFieldsArray=["positionLevelName"];
/**
 * submitForm
 * 			
* This method is used to submit form while clicking on the save/update button. 
*/
function submitForm(methodName){

	elementTrim(document.forms[0]);
	
	document.forms[0].method.value = methodName;
	document.forms[0].screenStatus.value = '${screenStatus}';
	document.forms[0].selectedEntityId.value = '${selectedEntityId}';
	enableFields();
	if(validateForm(document.forms[0]) ) {
	 document.forms[0].submit();
	 }
}

	/**
	 * Method to enable postion level drop down
	 */
	function enableFields(){
		document.forms[0].elements["entityPosLvlName.id.positionLevel"].disabled = "";
	}


	/**
	 * Method to validate form values
	 * @param objForm
	 */
	function validateForm(objForm){
	  var elementsRef = new Array(2);
	 
	  elementsRef[0] = objForm.elements["entityPosLvlName.id.positionLevel"];
	  elementsRef[1] = objForm.elements["entityPosLvlName.positionLevelName"];
	  return validate(elementsRef);
	}
	
	
	/**
	 * This method is used to avoid unwanted submission of page while pressing entr key in position level name field
	 */
	function submitUponEnter(e){
		 var event = (window.event|| e);
		if (event.keyCode == 13){//check for enter key
		//validate the mandatory feilds
			validateForm(document.forms[0]);
			return false;
		}
	}

	/**
	 * Method to handle body onload functions
	 */
	function bodyOnLoad()
	{
		<c:if test="${requestScope.method == 'add'}">
			document.forms[0].elements['entityPosLvlName.indicator'][0].checked = "true";
		</c:if>
		document.forms[0].selectedEntityId.value = '${selectedEntityId}';
		document.forms[0].selectedSweepPosLvl.value = '${selectedSweepPosLvl}';
		document.forms[0].selectedPreAdvPosLvl.value = '${selectedPreAdvPosLvl}';
		document.forms[0].method.value = '${method}';
		<c:if test="${requestScope.recordExist == 'yes'}">
			alert("<fmt:message key="ent.posLvlRecordExist"/>");
		</c:if>
		// Start: Code Added by Nithi for Mantis 1617 on 10-02-2012
		<c:if test="${requestScope.method == 'change'}">
			var positionName = decodeURIComponent(document.forms[0].elements["entityPosLvlName.positionLevelName"].value);
			document.forms[0].elements["entityPosLvlName.positionLevelName"].value = positionName;
		</c:if>
		// End: Code Added by Nithi for Mantis 1617 on 10-02-2012
	}

</SCRIPT>
</head>

<form action="entity.do" onsubmit="validate(this);" method="post">
<input name="method" type="hidden" value="">
<input name="screenStatus" type="hidden" value="">
<input name="selectedEntityId" type="hidden" value="">
<input name="selectedSweepPosLvl" type="hidden" value="">
<input name="selectedPreAdvPosLvl" type="hidden" value="">

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();" onunload="call()">

<div id="dropdowndiv_2" style="z-index:99;position:absolute;width:0px;left:265px;top:25px;visibility:hidden" 	class="swdropdown">

</div>
<div id="entityPosLvlNameAdd" style="position:absolute; left:20px; top:20px; width:285px; height:85px; border:2px outset;" color="#7E97AF">
<div id="entityPosLvlNameAdd" style="position:absolute;left:8px; top:4px; width:270px; height:100px;">
	<table width="237" border="0" cellpadding="0" cellspacing="0" height="50">

      
	 <tr height="24px">
        
		 <c:choose>
			<c:when test="${requestScope.method == 'change'}">
				<td width="60px"><b><fmt:message key="entity.PosLvl"/></b></td>
				<td width="28px">&nbsp;</td>
				<td width="145px">
					<input type="text" name="entityPosLvlName.id.positionLevel" title="<fmt:message key='tooltip.positionLevel'/>" class="htmlTextAlpha" tabindex="1" disabled style="width:120px;" value="${entityPosLvlName.id.positionLevel}" />
				</td>
			</c:when>
			<c:when test="${requestScope.method == 'add'}">
				<td width="60px"><b><fmt:message key="entity.PosLvl"/></b>*</td>
				<td width="28px">&nbsp;</td>
				<td width="145px">
					<select name="entityPosLvlName.id.positionLevel" class="htmlTextAlpha" style="width:55px" title="<fmt:message key='tooltip.positionLevel'/>" tabindex="1">
						<c:forEach items="${requestScope.positionLvlColl}" var="positionLevel">
							<option value="${positionLevel.value}" ${entityPosLvlName.id.positionLevel == positionLevel.value ? 'selected' : ''}>${positionLevel.label}</option>
						</c:forEach>
					</select>
				</td>
			</c:when>
		</c:choose>
           
      	  </tr>
	
		<tr height="24px">
			<td width="60px" ><b><fmt:message key="section.sectionName"/></b>*</td>
			<td width="28px" >&nbsp;</td>			
			<td width="145px" id="positionLevelName">
				<input type="text" name="entityPosLvlName.positionLevelName" title="<fmt:message key='label.entity.positionName'/>" class="htmlTextAlpha" tabindex="2" maxlength="15" style="width:120px;" onkeydown="return submitUponEnter(event)" value="${entityPosLvlName.positionLevelName}" />
			</td>
		</tr>
	</table>
		<table width="270" border="0" cellpadding="0" cellspacing="0">

		<tr height="15px">
			<td width="50px"><b><fmt:message key="poslevel.indicator"/></b>*</td>
			<td width="28px">&nbsp;</td>
			<td width="80px">
				<input type="radio" tabindex="3" style="width:13px" title="<fmt:message key='label.entity.indicator'/>" name="entityPosLvlName.indicator" value="I" ${entityPosLvlName.indicator == 'I' ? 'checked' : ''} />
				<label title='<fmt:message key="label.entity.indicator"/>' tabindex="3" for="3">
					<fmt:message key="label.entity.internalIndicator" />
				</label>
			</td>
			<td width="88px">
				<input type="radio" tabindex="4" style="width:25px" title="<fmt:message key='label.entity.indicator'/>" name="entityPosLvlName.indicator" value="E" ${entityPosLvlName.indicator == 'E' ? 'checked' : ''} />
				<label title='<fmt:message key="label.entity.indicator"/>' tabindex="4" for="4">
					<fmt:message key="label.entity.externalIndicator" />
				</label>
			</td>
		</tr>
		</table>
	
</div>
</div>

  <div id="Group" style="position:absolute; left:231; top:116px; width:70px; height:39px; visibility:visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr height="25">
			<td align="Right">
				<c:choose>
					<c:when test="${requestScope.method == 'add'}">
						<a tabindex="8" onKeyDown="submitEnter(this,event)" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Position Level'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
							<img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
						</a>
					</c:when>
					<c:when test="${requestScope.method == 'change'}">
						<a tabindex="8" onKeyDown="submitEnter(this,event)" href="#" onclick="javascript:openWindow(buildPrintURL('print','Change Position Level'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
							<img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
						</a>
					</c:when>
				</c:choose>
		  </td>

			<td align="right" id="Print">
				<a tabindex="8" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div> 

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:108; width:285px; height:39px; visibility:visible;">
<div id="GroupAdd" style="position:absolute; left:6; top:4; width:0px; height:15px; visibility:visible;">
  <table width="145" border="0" cellspacing="2" cellpadding="0">
    <tr>
		<c:choose>
			<c:when test="${requestScope.method == 'add'}">
				<td>
					<a id="save" title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('savePosLvl');">
						<fmt:message key="button.save" />
					</a>
				</td>
			</c:when>
			<c:when test="${requestScope.method == 'change'}">
				<td>
					<a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('updatePosLvl');">
						<fmt:message key="button.save" />
					</a>
				</td>
			</c:when>
		</c:choose>
		  <td id="cancelbutton"><a title='<fmt:message key="tooltip.cancel"/>' tabindex="7"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="confirmClose('C');"><fmt:message key="button.cancel" /></a></td>
	</tr>
  </table>
</div>
</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>