<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<html>
<head>
<title>
<c:choose>
    <c:when test="${requestScope.methodName == 'add'}">

	<fmt:message key="exchange.addTitle" />

    </c:when>
    <c:otherwise>
        <c:if test="${requestScope.methodName == 'change'}">

		<fmt:message key="exchange.changeTitle" />

        </c:if>
    </c:otherwise>
</c:choose></title>


<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

var dateSelected = false;

mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";


var dateFormat = '${sessionScope.CDM.dateFormat}';
var totalCount = '${totalCount}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";
var filterCriteria = "${requestScope.filterCriteria}";
var filterstatus= "${requestScope.filterStatus}";
var currPage = '${requestScope.currentPage}';
var maxPage = '${requestScope.maxPages}';
var ref =  '${requestScope.parentFormRefresh}'
<c:if test="${'yes'==requestScope.parentFormRefresh}">

window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].selectedFilter.value ='${requestScope.selectedFilter}';
window.opener.document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
window.opener.document.forms[0].filterCriteria.value = '${requestScope.filterCriteria}';
window.opener.document.forms[0].selectedFilterStatus.value = '${requestScope.filterStatus}';
window.opener.document.forms[0].exchangeRateFromDateAsString.value = '${requestScope.exchangeRateFromDateAsString}';
window.opener.document.forms[0].exchangeRateToDateAsString.value = '${requestScope.exchangeRateToDateAsString}';
window.opener.document.forms[0].currencyCode.value = '${requestScope.currencyCode}';


window.opener.document.forms[0].filterFromSerach.value = "true";  
window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';

window.opener.document.forms[0].maxPages.value = '${requestScope.maxPages}';
 
window.opener.document.forms[0].goToPageNo.value = '${requestScope.currentPage}';
 
window.opener.document.forms[0].submit();
self.close();
</c:if>

  


function bodyOnLoad()
{
   
     document.forms[0].selectedExchangeFromDate.value='${exchangeDateFrom}';	
	 document.forms[0].selectedExchangeToDate.value='${exchangeDateTo}';	
	var dropBox1= new SwSelectBox(document.forms[0].elements["currencyexchange.id.currencyCode"],document.getElementById("currencyName"));

	document.forms[0].entityFlag.value ='${requestScope.entityFlag}';
	

		currencychange();
		if(document.forms[0].elements['currencyexchange.exchangeRate'].value == 0)
{
document.getElementById("ccy").value=0;
}
else{
		var exch=1/document.forms[0].elements['currencyexchange.exchangeRate'].value;
		
		document.getElementById("ccy").value=exch.toFixed(6);
		
}

		}
function enableFields(){
	document.forms[0].elements["currencyexchange.id.entityId"].disabled = "";	
	document.getElementById("entityName").innerText.disabled = "";
	document.forms[0].elements["currencyexchange.id.currencyCode"].disabled = "";	
	document.getElementById("currencyName").innerText.disabled = "";	
	document.forms[0].elements["currencyexchange.exchangeRateDateAsString"].disabled = "";		
	
	
}	
/**
*This method is used to submit the page on clicking the Save button
*@param methodName
**/
function submitForm(methodName)
{

    var exchangeDate=validateField(document.forms[0].elements['currencyexchange.exchangeRateDateAsString'],'currencyexchange.exchangeRateDateAsString',dateFormat );
	
	//Validating exchange rate
	if(exchangeDate)
	{		
		 var digitValidation=document.forms[0].elements['currencyexchange.exchangeRate'];
		 //Validating decimal field value 
		 if(decimalValidation(digitValidation))
	{
	    var exchangeRate=validateField(document.forms[0].elements['currencyexchange.exchangeRate'],'Number 2','decimalPatWithoutNegative');
	    if(exchangeRate)
		{
		
	    if(validateForm(document.forms[0]) ){
		//validating the exchangerate field value
		if(document.forms[0].elements['currencyexchange.exchangeRate']!=null && document.forms[0].elements['currencyexchange.exchangeRate'].value != ""){
			if (document.forms[0].elements['currencyexchange.exchangeRate'].value != 0) {

		enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].filterCriteria.value = '${requestScope.filterCriteria}';

		document.forms[0].maxPages.value = '${requestScope.maxPages}';
		document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
		document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
		document.forms[0].currentPage.value = '${requestScope.currentPage}';
		
		document.forms[0].currencyCode.value='${requestScope.currencyCode}';
		
		document.forms[0].exchangeRateFromDateAsString.value='${requestScope.exchangeRateFromDateAsString}';
		document.forms[0].exchangeRateToDateAsString.value='${requestScope.exchangeRateToDateAsString}';
		document.forms[0].exchangeRateDateAsString.value='${requestScope.exchangeRateDateAsString}';

		document.forms[0].submit();
	

	} else {
				alert("<fmt:message key='alert.nonZeroExchangeRate'/>");
				document.forms[0].elements['currencyexchange.exchangeRate'].focus();
				return false;
			}
		} else {
				alert("<fmt:message key='alert.validExchangeRate'/>");
				document.forms[0].elements['currencyexchange.exchangeRate'].focus();
				return false;
			}

	}

    }
	else
	{
	  document.forms[0].elements['currencyexchange.exchangeRate'].focus();
	}
	}
	}	
	else
	{
	  document.forms[0].elements['currencyexchange.exchangeRateDateAsString'].focus();
	}
}
function validateForm(objForm){
  var elementsRef = new Array(2);
 
  elementsRef[0] = objForm.elements["currencyexchange.id.currencyCode"];
  elementsRef[1] = objForm.elements["currencyexchange.exchangeRateDateAsString"];  
  
  return validate(elementsRef);
}
</SCRIPT>
<script language="JAVASCRIPT">
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
      var cal = new CalendarPopup("caldiv",true); 
      cal.offsetX = 0;
      cal.offsetY = -100;
  </script>

<script type="text/javascript">
function validateArchiveDateField(){
	if(document.forms[0].elements['currencyexchange.exchangeRateDateAsString']!=null && document.forms[0].elements['currencyexchange.exchangeRateDateAsString'].value != ""){
		if(validateDateField(document.forms[0].elements['currencyexchange.exchangeRateDateAsString'],'addjob.Date',dateFormat)){
		}
	}
}

function validateExchangeRate(e) {
	var event = (window.event|| e);
	if(event.keyCode == 9){   // tab
		if(document.forms[0].elements['currencyexchange.exchangeRate']!=null && document.forms[0].elements['currencyexchange.exchangeRate'].value != ""){
			if (document.forms[0].elements['currencyexchange.exchangeRate'].value != 0) {
				return true;
			} else {
				alert("<fmt:message key='alert.nonZeroExchangeRate'/>");
				return false;
			}
		} else {
				alert("<fmt:message key='alert.validExchangeRate'/>");
				return false;
			}
	}
	if(event.keyCode == 13){  //enter
		if(document.forms[0].elements['currencyexchange.exchangeRate']!=null && document.forms[0].elements['currencyexchange.exchangeRate'].value != ""){
			if (document.forms[0].elements['currencyexchange.exchangeRate'].value != 0) {
				return true;
			} else {
				alert("<fmt:message key='alert.nonZeroExchangeRate'/>");
				return false;
			}
		} else {
				alert("<fmt:message key='alert.validExchangeRate'/>");
				return false;
			}
	}
}

function currencychange()
{

	if(document.forms[0].elements["currencyexchange.id.currencyCode"].value == "" || document.forms[0].elements["currencyexchange.id.currencyCode"].value == "All")
	{
		document.getElementById("curcode").innerText="Ccy";
	document.getElementById("curcodes").innerText="Ccy";
	}
	else{
		document.getElementById("curcode").innerText=document.forms[0].elements["currencyexchange.id.currencyCode"].value;
		document.getElementById("curcodes").innerText=document.forms[0].elements["currencyexchange.id.currencyCode"].value;
		}
}
function IsNumeric(sText)
{
   var ValidChars = "0123456789.";
   var IsNumber=true;
   var dotCount = 0;
   var Char;
   for (i = 0; i < sText.length && IsNumber == true; i++) 
    { 	  
		Char = sText.charAt(i); 
		if(Char == "."){
			dotCount++;
		}
		if(dotCount > 1){
			IsNumber = false;
		}
		if (ValidChars.indexOf(Char) == -1) 
		{
			IsNumber = false;
		}
	}
   return IsNumber;
   
   }
   
/**
 *
 * This method is used to validate the exchangeRate value on key up and change the fx rate reverse field 
 * @param fxRate
 * @return validationFlag
 *
 **/   
function rateChange(fxRate)
{
  if (fxRate.value.length != 1 || fxRate.value.trim() != ".") 
    {
		//Checks value is numeric    
        var validationFlag = IsNumeric(fxRate.value);
        if (validationFlag)
        {
            //Checks value is zero, if zero then set zero to the fx rate reverse
            if (fxRate.value == 0)
            {
                document.getElementById("ccy").value = 0;
            }
            else
            {
            	//set fx rate reverse
                var fxRateInverse = 1 / fxRate.value;
                document.getElementById("ccy").value = fxRateInverse.toFixed(6);
            }
            return validationFlag;
        }
        else
        {
            alert('<fmt:message key="alert.currencyExchangeRate.invalidRate"/>');
            document.getElementById("ccy").value = 0;
            return validationFlag;
        }
    }
}
/**
 *
 *This method is used to validate the exchangeRate value- The exchangeRate value should not exceed 13 *digits in whole and not exceed 7 digits in fractional part of exchangeRate field
 * @param digitValidation
 * @return validNoFlag
 **/

function decimalValidation(digitValidation)
{
    //get the exchangeRate value
    var exchangeRate = digitValidation.value;
    //intialize a flag for determining the valid number
    var validNoFlag = true;
    //validation for decimal no
    if (exchangeRate.indexOf(".") != -1)
    {
        var arrVlaues = exchangeRate.split(".");
        var beforeDecimal = arrVlaues[0];
        var afterDecimal = arrVlaues[1];
        //checking wheather the whole part is having more than 13 digits
        if (beforeDecimal.length > 13 || afterDecimal.length > 7)
        {
            alert('<fmt:message key="alert.currencyExchangeRate.invalidRate"/>');
            document.forms[0].elements['currencyexchange.exchangeRate'].focus();
            validNoFlag = false;
        }
    }
    else if (exchangeRate.length > 13) //validation for whole no
    {
            alert('<fmt:message key="alert.currencyExchangeRate.invalidRate"/>');
        document.forms[0].elements['currencyexchange.exchangeRate'].focus();
        validNoFlag = false;
    }
    return validNoFlag;
}
</script>

</head>


<body
	onLoad="setFocus(document.forms[0]);setParentChildsFocus();bodyOnLoad();ShowErrMsgWindow('${actionError}');"
	onunload="call()">

<form action="currencyexchange.do">
	<!-- start of pagingation -->

	<input name="selectedCurrencyCode" type="hidden">
	<input name="selectedCurrencyName" type="hidden">
	<input name="selectedExchangeDate" type="hidden">
	<input name="selectedExchangeFromDate" type="hidden">
	<input name="selectedExchangeToDate" type="hidden">
	<input name="selectedExchangeRate" type="hidden">
	<!-- Added for Pagination,sorting and Filter -->

	<input name="filterCriteria" type="hidden" value='${filterCriteria}'>
	<input name="selectedFilter" type="hidden" value='${selectedFilter}'>
	<input name="selectedSort" type="hidden" value='${selectedSort}'>
	<input name="totalCount" type="hidden" value="">
	<input name="selectedList" type="hidden" value="">
	<input name="selectedSortDescending" type="hidden" value="">
	<input name="selectedFilterStatus" type="hidden" value="">
	<input name="selectedSortStatus" type="hidden" value="">
	<input name="currentPage" type="hidden" value='${currPage}'>
	<input name="prevEnabled" type="hidden" value="">
	<input name="nextEnabled" type="hidden" value="">
	<input name="maxPages" type="hidden" value='${maxPage}'>
	<input name="method" type="hidden" value="displayList">
	<input name="filterFromSerach" type="hidden" value="">
	<input name="refreshScreen" type="hidden" value="">
	<input name="currencyCode" type="hidden" value="">
	<input name="exchangeRateFromDateAsString" type="hidden" value="">
	<input name="exchangeRateToDateAsString" type="hidden" value="">

	<input name="goToPageNo" type="hidden" value="">
	<input name="entityFlag" type="hidden" value="">


	<!-- filed in the table -->
	<input name="exchangeRateDateAsString" type="hidden" value="">

	<!-- end of pagination -->
    <c:set var="CDM" value="${sessionScope.CDM}"/>
	<div id="ExchangeChild"
		style="z-index: 99; position: absolute; left: 20px; top: 20px; width: 543px; height: 135px; border: 2px outset;"
		color="#7E97AF">
	<div id="ExchangeChild"
		style="z-index: 99; position: absolute; left: 8px; top: 4px; width: 533px; height: 130px;">

	<DIV ID="caldiv"
		STYLE="z-index: 99; position: absolute; visibility: hidden; background-color: white; layer-background-color: white;"></DIV>

	<table width="500" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="24">
			<td width="200px"><b><fmt:message key="party.entityId" /></b></td>
			

			<td width="100px"><input type="text"
				name="currencyexchange.id.entityId" value="${currencyexchange.id.entityId}"  style="width:124px;"
				class="htmlTextAlpha"
				disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}" /></td>
				<td width="27">&nbsp;</td>				
			<td width="200px"><span name="entityName" id="entityName"
				 class="spantext"  >${entityName}</td>
		</tr>
	</table>
	<table width="500" cellspacing="0" cellpadding="0" border="0"
		height="25">
		<tr height="24">
			<c:if test="${requestScope.methodName == 'add'}">
				<td width="123"><b><fmt:message key="currencygroup.currencies" /></b>*</td>
				<td width="66">&nbsp;</td>
				<td width="55">
				<div style="width:55px;">				
				<select id="currencyexchange.id.currencyCode"
				name="currencyexchange.id.currencyCode" class="htmlTextAlpha" 
				style="width:55px" titleKey="tooltip.currency" tabindex="1" 
				onchange="currencychange()" >
					<c:forEach items="${requestScope.currencies}" var="ccy">
					   <option
						  value="${ccy.value}"
						  <c:if test="${currencyexchange.id.currencyCode == ccy.value}">selected="selected"</c:if>>
						  ${ccy.label}
					   </option>
					</c:forEach>
				</select>

				</div>
				</td>
				<td width="50">&nbsp;</td>
				<td width="250"><span id="currencyName" name="currencyName"
					class="spantext"></td>
			</c:if>
			<!-- Start : code modified by Nageswararao 12/04/2012 1054_STL_014: saving the currency exchanges rates -->

			<c:if test="${requestScope.methodName == 'save'}">
				<td width="50"><b><fmt:message key="currencygroup.currencies" /></b>*</td>
				<td width="91">&nbsp;</td>
				<td width="55">
				<select id="currencyexchange.id.currencyCode"
				name="currencyexchange.id.currencyCode" class="htmlTextAlpha" 
				style="width:55px" titleKey="tooltip.currency" tabindex="1" 
				onchange="currencychange()" >
					<c:forEach items="${requestScope.currencies}" var="ccy">
					   <option
						  value="${ccy.value}"
						  <c:if test="${currencyexchange.id.currencyCode == ccy.value}">selected="selected"</c:if>>
						  ${ccy.label}
					   </option>
					</c:forEach>
				</select>

				</td>
				<td width="83">&nbsp;</td>
				<td width="280"><span id="currencyName" name="currencyName"
					class="spantext"></td>
			</c:if>
			<c:if test="${requestScope.methodName != 'add'}">
				<td width="134"><b><fmt:message key="currencygroup.currencies" /></b>*</td>
				<td width="83">&nbsp;</td>
				<td width="55"><input type="text"
				    value="${currencyexchange.id.currencyCode}"
					name="currencyexchange.id.currencyCode" style="width:55px;"
					class="htmlTextAlpha"
					disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}" /></td>
				</td>
				<td width="81">&nbsp;</td>
				<td width="280"><span 
					name="currencyName" id="currencyName" >${currencyName}</td>
			</c:if>
					<!-- End : code modified by Nageswararao 12/04/2012 1054_STL_014: saving the currency exchanges rates -->
		</tr>
	</table>
	<table width="482" cellspacing="0" cellpadding="0" border="0"
		height="25">
		<tr height="24">
			<c:if test="${requestScope.methodName == 'add'}">
				<td width="75"><b><fmt:message key="addjob.Date" /></b>*</td>


				<td title='<fmt:message key="exchange.date"/>' width="280"><input type="text"
					tabindex="2" maxlength="10"
					name="currencyexchange.exchangeRateDateAsString"
					value="${currencyexchange.exchangeRateDateAsString}"
					class="htmlTextAlpha" style="width:80px;height:20px;"
					onchange="if(validateForm(document.forms[0]) ){validateArchiveDateField();}"
					onmouseout="dateSelected=false;" /> &nbsp;<A name="datelink"
					ID="datelink" tabindex="3"
					onClick="cal.select(document.forms[0].elements['currencyexchange.exchangeRateDateAsString'],'datelink',dateFormatValue);dateSelected=true;return false;"><img
					title='<fmt:message key="exchange.cal"/>'
					src="images/calendar-16.gif" style="margin-bottom: -5px; margin-left: -8px;"></A></td>
			</c:if>

			<c:if test="${requestScope.methodName == 'change'}">
				<td width="75"><b><fmt:message key="addjob.Date" /></b></td>
				<td width="280"><input type="text" tabindex="2" maxlength="30"
					name="currencyexchange.exchangeRateDateAsString"
					value="${currencyexchange.exchangeRateDateAsString}"
					disabled="true" class="htmlTextAlpha" style="width:80px;"
					onblur="validateExchangeDate();" /></td>
			</c:if>
		</tr>
		<tr height="24">
			<c:if test="${requestScope.entityFlag != 'true'}">
				<td id="domestic" width="160"><b><fmt:message key="contact.dom.exchange" /> (${requestScope.domesticcurr}/<span
					id="curcode" name="curcode" class="spantext">${requestScope.currencyCode}</span>)</b>*
				</td>
			</c:if>
			<c:if test="${requestScope.entityFlag == 'true'}">
				<span id="currencyName" name="currencyName" class="spantext">
				<td id="currency" width="160"><b><fmt:message key="contact.ccy.exchange" /> (<span id="curcode" name="curcode"
					class="spantext">${requestScope.currencyCode}</span>/${requestScope.domesticcurr})</b>*</td>

			</c:if>

            <!-- Start : code modified by Nageswararao 12/04/2012 1054_STL_014: saving the currency exchanges rates -->
			<td title='<fmt:message key="exchange.rate"/>' width="160"><input type="text"
				tabindex="4" maxlength="21" name="currencyexchange.exchangeRate"
				value="${currencyexchange.exchangeRate}"
				class="htmlTextNumeric" style="width:200px;" 
				onkeydown="return validateExchangeRate(event);"
				onkeyup="rateChange(this);" /></td>
				<!-- End : code modified by Nageswararao 12/04/2012 1054_STL_014: saving the currency exchanges rates -->
		</tr>
		<tr height="24">
			<c:if test="${requestScope.entityFlag != 'false'}">
				<td id="domestic" width="160"><b> <fmt:message key="contact.dom.exchange" /> (${requestScope.domesticcurr}/<span
					id="curcodes" name="curcodes" class="spantext">${requestScope.currencyCode}</span>)</b>
				</td>
			</c:if>
			<c:if test="${requestScope.entityFlag == 'false'}">
				<span id="currencyName" name="currencyName" class="spantext">
				<td id="currency" width="160"><b> <fmt:message key="contact.ccy.exchange" /> (<span id="curcodes" name="curcodes"
					class="spantext">${requestScope.currencyCode}</span>/${requestScope.domesticcurr})
				</b></td>
			</c:if>

			<td width="160"><input type="text" name="ccy" size="29"
				maxlength="21" style="width: 200px;"
				disabled="${requestScope.screenFieldsStatus == 'true' ? 'true': ''}"  /></td>

		</tr>

	</table>
	</div>
	</div>
	<div id="PartyMain"
		style="position: absolute; left: 486; top: 168px; width: 70px; height: 39px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<td align="Right"><c:if test="${requestScope.methodName == 'add'}">
				<a tabindex="7" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Currency Exchange Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if><c:if test="${requestScope.methodName == 'change'}">
				<a tabindex="7" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Currency Exchange Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if></td>

			<td align="right" id="Print"><a tabindex="7"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 160; width: 543px; height: 39px; visibility: visible;">
	<div id="ExchangeChild"
		style="position: absolute; left: 6; top: 4; width: 525px; height: 15px; visibility: visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>
			<c:if test="${requestScope.methodName == 'add'}">
				<td width="70"><a tabindex="5"
					title='<fmt:message key="tooltip.save"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this);"
					onclick="javascript:submitForm('save');"><fmt:message key="button.save" /></a></td>
			</c:if>
			<c:if test="${requestScope.methodName == 'change'}">
				<td width="70"><a tabindex="4"
					title='<fmt:message key="tooltip.save"/>'
					onMouseOut="collapsebutton(this)"
					onMouseOver="highlightbutton(this)"
					onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this);"
					onclick="javascript:submitForm('update');"><fmt:message key="button.save" /></a></td>
			</c:if>



			<td id="cancelbutton" width="70"><a tabindex="6"
				title='<fmt:message key="tooltip.cancel"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();"><fmt:message key="button.cancel" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>