<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<title><fmt:message key="matchquamaintenance.title.MatchQuality"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

//var entityAccess = "${requestScope.EntityAccess}";

/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */

function bodyOnLoad()
{
	xl = new XLSheet("MatchColl","table_2", ["String", "Number"],"12");
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	xl.onsort = xl.onfilter = onFilterandSort;
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */

	highlightTableRows("MatchColl");

	var dropBox1 = new SwSelectBox(document.forms[0].elements["matchQuality.id.entityId"],document.getElementById("entityName"));
	
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 

	/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0"){
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	 document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	}else{
	 document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	}


	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	 <%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	 <%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
	 <%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.VIEW_BUT_STS)) ) {%>
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
	 <%}%>
	
	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>
	
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 */
	
}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
	enableFields();
	document.forms[0].submit();
}

function enableFields(){
	document.forms[0].elements["matchQuality.id.entityId"].disabled = "";
	document.getElementById("entityName").disabled = "";	
}

function buildChangeMatchQuality(methodName){
	var param = 'matchQuality.do?method='+methodName+'&entityId=';
	param += document.forms[0].elements['matchQuality.id.entityId'].value;
	param += '&entityName=';
	param += document.getElementById("entityName").innerText;
	param += '&selectedCurrencyCode=' + document.forms[0].selectedCurrencyCode.value;
	param += '&selectedPostionLevel=' + document.forms[0].selectedPostionLevel.value;
	
	return  param;
}

function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	var hiddenElement1 = rowElement.getElementsByTagName("input")[1];

	
	document.forms[0].selectedCurrencyCode.value = hiddenElement.value;
	document.forms[0].selectedPostionLevel.value = hiddenElement1.value;
	 
	 
	/* START: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	if(menuEntityCurrGrpAccess == "0" && isSelected)
	/* END: Code changed as par ING requirements to the Roles - Menu Access, 21-JUN-2007 */
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else 
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		/* START: Code fixed for proper button enabling/disabling after selction/deselection of a row, 10-SEP-2007 */
		// Modified by Atef S. for Mantis 2071: Match Quality Maintenance screen: unable to view with 'View Access' role privilege 
		if (menuEntityCurrGrpAccess == "1" && isSelected) 
			document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
		else
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		/* END: Code fixed for proper button enabling/disabling after selction/deselection of a row, 10-SEP-2007 */
	}
}

function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 
	
	document.forms[0].submit();
	
	}
}

</SCRIPT>
</head>

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call();">

<form action="matchQuality.do">
<input name="selectedCurrencyCode" type="hidden" value="">
<input name="selectedPostionLevel" type="hidden" value="">
<input name="method" type="hidden" value="display">


<!-- START: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->
<input name="menuAccessId" type="hidden" >
<!-- END: Code changed as par ING requirements to the Roles - Menu Access, 14-JUN-2007 -->

<div id="MatchQuality" style="position:absolute; left:20px; top:20px; width:475px; height:39px; border:2px outset;" color="#7E97AF">
<div id="MatchQuality" style="position:absolute; left:8px; top:2px; width:475px; height:37px;">
<table width="485" border="0" cellpadding="0" cellspacing="0" height="32">
	<tr color="black" border="0">
	  <td width="37"><b><fmt:message key="matchQuality.entityId"/></b></td>
	  <td width="28">&nbsp;</td>
	  <td width="140" title='<fmt:message key="tooltip.selectEntity"/>'>
	  <select class="htmlTextAlpha" name="matchQuality.id.entityId" onchange="submitForm('displayList')" style="width:140px"  tabindex="1">
		  <c:forEach items="${requestScope.entities}" var="entity">
               <option
                  value="${entity.value}"
                  <c:if test="${matchQuality.id.entityId == entity.value}">selected="selected"</c:if>>
                  ${entity.label}
               </option>
		  </c:forEach>
	  </select>
	 </td>
	 <td width="20">&nbsp;</td>
	 <td width="280">
	    <span id="entityName" class="spantext">
	  </td>
	 </tr>
</table>
</div>
</div>
<div id="MatchQuality" color="#7E97AF" style="position:absolute; border-left:2px outset; left:20px; top:64px; width:475px; height:340px;">
<div id="MatchQuality" style="position:absolute;z-index:99;left:0px; top:0px; width:470px; height:10px;">
<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="455" border="0" cellspacing="1" cellpadding="0"  height="23px">
	<thead>
		<tr>
			<td width="310" height="20px" align="center"  style="border-left-width: 0px;" title='<fmt:message key="tooltip.sortCurrencyCode"/>'> <b><fmt:message key="matchQuality.currencyCode"/></b></td>
			<td width="35%" height="20px" align="center" title='<fmt:message key="tooltip.sortPositionLevel"/>'> <b><fmt:message key="matchQuality.posLevel"/></b></td>
		</tr>
	</thead>
</table>
</div>
<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:471px; height:335px;overflowY:scroll">
<div id="MatchQuality" style="position: absolute; left:0px; top:21px; width:453px; height:10px;">
<table class="sort-table" id="MatchColl" width="454" border="0" cellspacing="1" cellpadding="0" height="314">
<tbody>
    <c:set var="count" value="0" />
    <c:forEach var="match" items="${requestScope.MatchColl}">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <input type="hidden" name="id.currencyCode" value="${match.id.currencyCode}" />
            <input type="hidden" name="id.posLevel" value="${match.id.posLevel}" />
            <td width="310" align="center">${match.id.currencyCode}&nbsp;</td>
            <td width="35%" align="center">${match.id.posLevel}&nbsp;</td>
        </tr>
        <c:set var="count" value="${count + 1}" />
    </c:forEach>
</tbody>
	<tfoot><tr><td colspan="2" ></td></tr></tfoot>
</table>
</div>
</div>
</div>
<%-- Start Comment: Following code has been replaced by the code incorporated with help -- Amit Shukla 07Nov2006 --%>
<%-- <div id="MatchQuality" style="position:absolute; left:75; top:418; width:400; height:15px; visibility:visible;">
	<table width="400" cellspacing="0" cellpadding="0" height="20" border="0">
		<tr>
			<td align="right" valign="middle" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif" name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div> --%> 
<%-- End Comment --%>
<%-- Start: UAT Phase2Defects20061024_STL2 Defect No:27  --%>
<div id="MatchQuality" style="position:absolute; left:410; top:418; width:80; height:15px; visibility:visible;">
	<table width="70" cellspacing="0" cellpadding="0" height="20" border="0">
		<tr>
			<td align="Right">
				<a tabindex="5" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Match Quality'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>	
			</td>
			<td align="right" valign="middle" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif" name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<%-- End: UAT Phase2Defects20061024_STL2 Defect No:27  --%>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:409; width:475px; height:39px; visibility:visible;">
<div id="MatchQuality" style="position:absolute; left:6; top:4; width:355px; height:15px; visibility:visible;">
  <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		
		<td id="addbutton" width="70"></td>
		<td id="changebutton" width="70"></td>
		<td id="viewbutton" width="70"></td>
		<td id="deletebutton" width="70"></td>
		<td id="closebutton" width="70">		
		<a title='<fmt:message key="tooltip.close"/>' style="margin-right: 0px; margin-left: 5px;" tabIndex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
				
		</td>
	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:475px; height:15px; visibility:hidden; display:none;">  	
	<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
	<tr>
		
		<td id="addenablebutton">
			<!--START : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
			<!--START:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<a  tabIndex="2" style="margin-right: 0px;" title='<fmt:message key="tooltip.addNewMatchQuality"/>'onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeMatchQuality('add'),'matchqualitymaintenanceaddWindow','left=50,top=190,width=796,height=430,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
			<!--END:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<!--END : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
		</td>
		<td id="adddisablebutton">
			<a class="disabled"  style="margin-right: 0px;"disabled="disabled"><fmt:message key="button.add"/></a>
		</td>
		<td id="changeenablebutton">
			<!--START : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
			<!--START:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<a tabIndex="3"style="margin-right: 0px; margin-left: 5px;" title='<fmt:message key="tooltip.changeNewMatchQuality"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeMatchQuality('change'),'matchqualitymaintenancechangeWindow','left=50,top=190,width=796,height=430,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
			<!--END:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<!--END : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
		</td>		
		<td id="changedisablebutton">
			<a class="disabled" disabled="disabled" style="margin-right: 0px; margin-left: 5px;"><fmt:message key="button.change"/></a>
		</td>
		<td id="viewenablebutton">
			<!--START : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
			<!--START:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<a tabIndex="4"  style="margin-right: 0px; margin-left: 5px; "title='<fmt:message key="tooltip.viewSelMatchQuality"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeMatchQuality('view'),'matchqualitymaintenanceviewWindow','left=50,top=190,width=796,height=430,toolbar=0,status=yes, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
			<!--END:code added by Mahesh on 17-Sep-2009 for 1049_Beta test issues(Mantis 1031) : to removed vertical scroll bar, increase the height -->
			<!--END : code modified by Mahesh on 13-Oct-2009 for Mantis 1034: increased popup screen height -->
		</td>		
		<td id="viewdisablebutton">
			<a class="disabled"  style="margin-right: 0px; margin-left: 5px;"disabled="disabled"><fmt:message key="button.view"/></a>
		</td>
		<td id="deleteenablebutton">		
			<a tabIndex="5"  style="margin-right: 0px; margin-left: 5px;"title='<fmt:message key="tooltip.deleteSelMAtchQuality"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><fmt:message key="button.delete"/></a>
		</td>		
		<td id="deletedisablebutton">
			<a class="disabled" disabled="disabled" style="margin-right: 0px; margin-left: 5px;"><fmt:message key="button.delete"/></a>
		</td>						
	</tr>
	</table>
  </div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>

</form>
</body>
</html>           