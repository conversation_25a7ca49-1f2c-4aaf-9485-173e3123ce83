<!doctype html>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title> Cash Reserve Balance Management - SMART-Predict</title>
	<!-- <base href="."> -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" type="image/x-icon" href="favicon.ico">
	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
<body>
<%@ include file="/angularJSUtils.jsp"%>
<SCRIPT language="JAVASCRIPT">
	var requestURL = new String('<%=request.getRequestURL()%>');
	var appName = "<%=SwtUtil.appName%>";
	var idy = requestURL.indexOf('/'+appName+'/');
	requestURL=requestURL.substring(0,idy+1);
	var oXMLHTTP = new XMLHttpRequest();
	var screenRoute = "CashRsrvBalManagmnt";

	/**
	 *	This section is used to handle calender button on the screen && is used to set the position of the same.
	 */
	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
	var menuAccessId = "${requestScope.menuAccessId}";



	function subAcctCcyPeriodMaint(screenName, params) {
		var param = "accountPeriod.do?method=OpenSubAcctCcyPeriodMaint&screenName="+screenName;
		param += '&allParams=' + params;
		var 	mainWindow = openWindow (param, 'subAcctCcyPeriodMaint','left=10,top=230,width=550,height=380,toolbar=0, resizable=yes, status=yes, scrollbars=yes');
		return false;
	}


</SCRIPT>




<%@ include file="/angularscripts.jsp"%>

<form id="exportDataForm" target="tmp" method="post">
	<input type="hidden" name="data" id="exportData" /> <input
		type="hidden" name="screen" id="exportDataScreen"
		value="CashRsvrBalManagmnt" /></form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>
