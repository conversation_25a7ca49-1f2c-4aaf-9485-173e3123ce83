<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="copyFromFormatId.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
var copyFromAddOrChange = '${requestScope.copyFromAddOrChange}';
var previousFormatId = '${requestScope.previousFormatId}';
var newFormatId = '${requestScope.newFormatId}';
/*Start:Code Modified by sand<PERSON>kumar for Mantis 2092 */
// var scenarioDesc = '${requestScope.scenarioDesc}';
// var selectedScenarioId = '${requestScope.selectedScenarioId}';
<c:if test="${'yes' == requestScope.parentFormRefresh}">
		window.opener.document.forms[0].method.value = "changeScenarioFormat";
		window.opener.document.forms[0].formatId.value = '${requestScope.selectedFormatId}';
// 		window.opener.document.forms[0].scenarioId.value = '${requestScope.selectedScenarioId}';
// 		window.opener.document.forms[0].selectedScenarioId.value = '${requestScope.selectedScenarioCode}';
// 		window.opener.document.forms[0].scenarioDesc.value = scenarioDesc;
		window.opener.document.forms[0].copyFromAddOrChange.value = copyFromAddOrChange;
		window.opener.document.forms[0].previousFormatId.value = previousFormatId;
		window.opener.document.forms[0].newFormatId.value = newFormatId;
		window.opener.document.forms[0].submit();
/*End:Code Modified by sandeepkumar for Mantis 2092 */
		self.close();
</c:if>

/*Start:Code Modified by sandeepkumar for Mantis 2092 */
	/**
	 * submitForm
	 *
	 * This method is used to submit the form while perform button operation on copy from format id screen.
	 **/
function submitForm(methodName){
	document.forms[0].copiedFormatId.value=document.forms[0].elements['scenarioMessageFormats.id.formatId'].value;
	document.forms[0].method.value=methodName;
	document.forms[0].copyFromAddOrChange.value=copyFromAddOrChange;
	document.forms[0].previousFormatId.value=previousFormatId;
	document.forms[0].newFormatId.value=newFormatId;
// 	document.forms[0].selectedScenarioId.value = selectedScenarioId;
// 	document.forms[0].scenarioDesc.value=scenarioDesc;
	document.forms[0].submit();
}
/*End:Code Modified by sandeepkumar for Mantis 2092 */

/*Start:Code Modified by sandeepkumar for Mantis 2095 : Sweep Message Format : Allow user to copy Message format across entity  */
	/**
	 * copyForm
	 *
	 * This method is used to submit the form while perform operation on entity drop down on copy from format id screen.
	 **/
function copyForm(methodName){
// 	document.forms[0].selectedScenarioId.value=document.forms[0].elements['scenarioMessageFormats.id.scenarioId'].value;
	document.forms[0].method.value=methodName;
	document.forms[0].copyFromAddOrChange.value=copyFromAddOrChange;
	document.forms[0].previousFormatId.value=previousFormatId;
// 	document.forms[0].scenarioId.value = '${requestScope.scenarioId}';
	document.forms[0].newFormatId.value=newFormatId;
	document.forms[0].submit();
}

/**
 * bodyOnLoad
 *
 * This method is called when window onloading and used to set form
 * values in the page and enable/diable the button
 */
function bodyOnLoad(){
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
// 	var dropBox1 = new SwSelectBox(document.forms[0].elements["scenarioMessageFormats.id.scenarioId"],document.getElementById("scenarioDesc"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["scenarioMessageFormats.id.formatId"],document.getElementById("formatName"));
	 if (document.forms[0].elements['scenarioMessageFormats.id.formatId'].value == "") {
		 document.getElementById("copybutton").innerHTML = document.getElementById("copydisablebutton").innerHTML;
	 }
}

/*End:Code Modified by sandeepkumar for Mantis 2095 : Sweep Message Format : Allow user to copy Message format across entity  */
</SCRIPT>
</head>
<form action="messageformats.do">
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');setParentChildsFocus();" onunload="call()">
<input name="entityCode" type="hidden" value="">
<input name="method" type="hidden" value="">
<input name="copiedFormatId" type="hidden" value="">
<input name="formatId" type="hidden" value="">
<input name="copyFromAddOrChange" type="hidden" value="">
<input name="previousFormatId" type="hidden" value="">
<input name="newFormatId" type="hidden" value="">
<!-- Start:Code Modified by sandeepkumar for Mantis 2095:Allow user to copy Message format across entity -->
<!-- <input name="selectedScenarioId" type="hidden" value=""> -->
<%-- <input name="scenarioId" type="hidden" value="${requestScope.scenarioId}"> --%>
<%-- <input name="scenarioDesc" type="hidden" value="${requestScope.scenarioDesc}"> --%>

<div id="MessageFormats" color="#7E97AF" style="position:absolute; border:2px outset; left:12px; top:11px; width:502px; height:55px;">
<div id="MessageFormats" style="position:absolute;z-index:99;left:6px; top:2px; width:485; height:60px;">
<table width="485" border="0" cellspacing="0" cellpadding="0">
<!-- 	<tr height="20px"> -->
<%-- 			  <td width="100px"><b><fmt:message key="scenario.scenarioId"/></b></td> --%>
<!--   			  <td width="28px">&nbsp;</td> -->
<!-- 			  <td width="120px">		   -->
<%-- 				<s:select id="scenarioMessageFormats.id.scenarioId" name="scenarioMessageFormats.id.scenarioId" onchange="javascript:copyForm('copyFromScenario')" titleKey = "tooltip.selectEntityid" tabindex="1" style="width:120px" --%>
<%-- 				 --%>
<!-- 			  </td> -->
<!-- 			  <td width="20px" >&nbsp;</td> -->
<!-- 		  <td width="280px"> -->
<!-- 			<span id="scenarioDesc" name="scenarioDesc" class="spantext"> -->
<!-- 	      </td> -->
<!-- 		  </tr> -->
	 <tr height="31">
		  <td width="37"><b><fmt:message key="messageFormats.id.formatIdcopy"/></td>
		   <td width="28px" >&nbsp;</td>
		   <td width="120px" >
			<select id="scenarioMessageFormats.id.formatId" name="scenarioMessageFormats.id.formatId"
					class="htmlTextAlpha" titleKey="tooltip.SelectFormatID"
					style="width:120px">
					<c:forEach items="${requestScope.collmsgFormatId}" var="msgFormatId">
						   <option
							  value="${msgFormatId.value}"
							  <c:if test="${scenarioMessageFormats.id.formatId == msgFormatId.value}">selected="selected"</c:if>>
							  ${msgFormatId.label}
						   </option>
					</c:forEach>/>
			</select>

		  </td>
		   <td width="20px" >&nbsp;</td>
		  <td width="280px">
			<span id="formatName" name="formatName" class="spantext">
	      </td>

	 </tr>
     </table>
</div>
</div>
 <div id="MessageFormats" style="position:absolute; left:445; top:70; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="52">
		<tr>

		<td align="Right">
    <a tabindex="4" href=# onclick="javascript:openWindow(buildPrintURL('print','Message Format '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
   </td>

			<td align="right" id="Print">
				<a tabindex="4" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:12; top:74px; width:502px; height:38px; visibility:visible;">
<div id="MessageFormats" style="position:absolute; left:4; top:4; width:225px; height:15px; visibility:visible;">

    <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="copybutton" width="70px">
		<a  tabindex="2" title='<fmt:message key="tooltip.CopySelectedFormat"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('copyScenario')"><fmt:message key="button.copy"/></a>
		</td>
			<td id="cancelbutton" width="70px">
				<a  tabindex="3"  title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel"/></a>
			</td>

	</tr>
    </table>
  </div>
<div  style="position:absolute; left:4; top:4; width:225px; height:15px; visibility:hidden;">
 <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
 <tr>
  <td id="copydisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.copy"/></a>
		</td>
		 </tr>
 </table>
</div>
<!-- End:Code Modified by sandeepkumar for Mantis 2095:Allow user to copy Message format across entity -->
</form>
</body>
</html>