<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="account.sweep.scheduleused.title"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<script language="javascript">

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}
function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	
}
function bodyOnLoad()
{
	xl = new XLSheet("acctSweepSchedulecoll","table_2", ["String","String","String","String", "Number","String","String","String", "Number", "Number", "String","String","String","String","String","String","String","String","String"],"1111111111111111111");
xl.onsort = xl.onfilter = onFilterandSort;
highlightTableRows("acctSweepSchedulecoll");	

	
document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
    

// if(menuEntityCurrGrpAccess == "0"){
	
// 		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;	
// 	}else{
// 		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;	
// 	}
// document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
// document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
}

function onSelectTableRow(rowElement, isSelected)
{
// 	var hiddenElement0 = rowElement.cells[0].innerText.trim();	
// 	var hiddenElement1 = rowElement.cells[1].innerText.trim();	
// 	document.forms[0].selectedReasonCode.value = hiddenElement0;
// 	document.forms[0].selectedDescription.value = hiddenElement1;
// 	//Modified by Atef S. for Mantis 1769 : Change and delete buttons are enabled in reason maintenance screen
// 	if(menuEntityCurrGrpAccess == "0" && isRowSelected(rowElement) )
// 		{
// 			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;	
// 			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;	
					
// 		}else{	
		
// 			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;	
// 			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
// 		}
	
}



</script>
</head>
<body  onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()"  >
<form action="reasonMaintenance.do"  >
<input name="method" type="hidden" >
<input name="entityCode" type="hidden" >
<input name="selectedReasonCode" type="hidden" >
<input name="selectedDescription" type="hidden" >
<input name="menuAccessId" type="hidden" >


<div id="Currency" color="#7E97AF" style="word-wrap: break-word;position:absolute; border:2px outset; left:20px; top:15px; width:860px; height:250;">
	<div id="Currency" style="word-wrap: break-word;position:absolute;z-index:50;left:0px; top:0px; width:827px; height:60px !important;verflow-x: hidden">
	
	<table  class="sort-table"  bgcolor="#B0AFAF" width="1670px" border="0" cellspacing="1" cellpadding="0"  height="16px">
	<thead>
		<tr height="20px">
		    <td  align="center" title ='<fmt:message key="account.schedSweep.againstAccount"/>' width="1915" style="border-left-width: 0px;"><b><fmt:message key="account.schedSweep.againstAccount"/></b></td>
			<td  align="center" title ='<fmt:message key="account.schedSweep.thisAccount"/>' width="580px"><b><fmt:message key="account.schedSweep.thisAccount"/></b></td>
		</tr>
	</thead>
	</table>
	
	
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="2500px" border="0" cellspacing="1" cellpadding="0"  height="30px">
	<thead>
		<tr height="20px">
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepEntityId"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.entityId"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepAccountId"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.account"/></b></td>
		    <td  title ='<fmt:message key="account.schedSweep.tooltip.scheduleFrom"/>' width="68px" style="border-left-width: 0px;"><b><fmt:message key="account.schedSweep.heading.scheduleFrom"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.scheduleTo"/>' width="68px"><b><fmt:message key="account.schedSweep.heading.scheduleTo"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherSweepFromBalType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.otherSweepFromBalType"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.targetBalanceType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.targetBalanceType"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.targetBalance"/>' align="center" width="150px"><b><fmt:message key="account.schedSweep.heading.targetBalance"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepDirection"/>' align="center" width="160px"><b><fmt:message key="account.schedSweep.heading.sweepDirection"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.minAmount"/>' align="center" width="100px"><b><fmt:message key="account.schedSweep.heading.minAmount"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.allowMultiple"/>' width="85px"><b><fmt:message key="account.schedSweep.heading.allowMultiple"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSweepBookcodeCr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.thisAccSweepBookcodeCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSweepBookcodeDr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.thisAccSweepBookcodeDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodCr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodDr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.sweepFromBalanceType"/>' width="83px"><b><fmt:message key="account.schedSweep.heading.sweepFromBalanceType"/></b></td>			
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherAccSweepBookcodeCr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.otherAccSweepBookcodeCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.otherAccSweepBookcodeDr"/>' width="100px"><b><fmt:message key="account.schedSweep.heading.otherAccSweepBookcodeDr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodCr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodCr"/></b></td>
			<td title ='<fmt:message key="account.schedSweep.tooltip.thisAccSettleMethodDr"/>' width="130px"><b><fmt:message key="account.schedSweep.heading.thisAccSettleMethodDr"/></b></td>
		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:843px; height:275px; overflow:scroll;">
	<div id="Currency" style="position:absolute;z-index:99;left:0px; top:22px; width:930px; height:10px;">
		<table class="sort-table" id="acctSweepSchedulecoll" width="2500px" border="0" cellspacing="1" cellpadding="0" height="433">
			<tbody>
				<c:set var="count" value="0" />
				<c:forEach var="acctSweepSchedule" items="${requestScope.acctSweepSchedulecoll}">
					<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
						<td width="130px" align="left">${acctSweepSchedule.entityId}&nbsp;</td>
						<td width="130px" align="left">${acctSweepSchedule.accountId}&nbsp;</td>
						<td align="center" width="68px">
							<input type="hidden" name="scheduleFrom" value="${acctSweepSchedule.scheduleFrom}" />
							${acctSweepSchedule.scheduleFrom}&nbsp;
						</td>
						<td width="68px" align="center">
							<input type="hidden" name="scheduleTo" value="${acctSweepSchedule.scheduleTo}" />
							${acctSweepSchedule.scheduleTo}&nbsp;
						</td>
						<td width="83px" align="left">${acctSweepSchedule.sweepFromBalanceTypeAsString}&nbsp;</td>
						<td width="83px" align="left">${acctSweepSchedule.targetBalanceTypeAsString}&nbsp;</td>
						<td width="150px" align="right">${acctSweepSchedule.targetBalanceAsString}&nbsp;</td>
						<td width="160px" align="left">${acctSweepSchedule.sweepDirectionAsString}&nbsp;</td>
						<td width="100px" align="right">${acctSweepSchedule.minAmountAsString}&nbsp;</td>
						<td width="85px" align="left">${acctSweepSchedule.allowMultipleAsString}&nbsp;</td>
						<td width="100px" align="left">${acctSweepSchedule.thisAccSweepBookcodeCr}&nbsp;</td>
						<td width="100px" align="left">${acctSweepSchedule.thisAccSweepBookcodeDr}&nbsp;</td>
						<td width="130px" align="left">${acctSweepSchedule.thisAccSettleMethodCr}&nbsp;</td>
						<td width="130px" align="left">${acctSweepSchedule.thisAccSettleMethodDr}&nbsp;</td>
						<td width="83px" align="left">${acctSweepSchedule.sweepFromBalanceTypeAsString}&nbsp;</td>
						<td width="100px" align="left">${acctSweepSchedule.otherAccSweepBookcodeCr}&nbsp;</td>
						<td width="100px" align="left">${acctSweepSchedule.otherAccSweepBookcodeDr}&nbsp;</td>
						<td width="130px" align="left">${acctSweepSchedule.otherAccSettleMethodCr}&nbsp;</td>
						<td width="130px" align="left">${acctSweepSchedule.otherAccSettleMethodDr}&nbsp;</td>
					</tr>
					<c:set var="count" value="${count + 1}" />
				</c:forEach>
			</tbody>
	
	<tfoot><tr><td colspan="15" ></td></tr></tfoot>
	
</table>
</div>
</div>
</div>

 <div id="ReasonMaintenance" style="position:absolute; left:600; top:290; width:70px; height:15px; visibility:visible;">
	<table width="250px" border="0" cellspacing="0" cellpadding="0" height="0">
		<tr>
		    <td align="Right" width="205px">
			    <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Account Scheduled Sweeps Summary'),'sectionprintdwindow','left=50,top=190,width=412,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
            </td><td>&nbsp;&nbsp;</td>

			<td align="left" width="40px" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div> 
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:282px; width:845px; height:39px; visibility:visible;">
  <div id="ReasonMaintenance" style="position:absolute; left:6; top:4; width:701; height:15px; visibility:visible;">
  	  <table width="200" border="0" cellspacing="0" cellpadding="0" height="20" style="width: 270px">
	<tr>
		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>'>	<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
	</tr>
	</table>
</div>
</div>

<blockquote>&nbsp;</blockquote>


<script type="text/javascript">
</script>
</form>
</body>
</html>
 
  
