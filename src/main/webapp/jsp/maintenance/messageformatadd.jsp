<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<style type="text/css">

.inputText[disabled] {
	background-color: rgba(229,229,229,1) !important;
	color: rgb(124,124,124) !important;
	text-shadow: 1px 1px 0 rgb(255,255,255) !important;
}

</style>
<title>
<c:if test="${'save' == requestScope.methodName}">

	<fmt:message key="messageFormats.addScreen"/>

</c:if>
<c:if test="${'add' == requestScope.methodName}">

	<fmt:message key="messageFormats.addScreen"/>

</c:if>
<c:if test="${'save' != requestScope.methodName}">

	<c:if test="${'update' == requestScope.methodName}">

		<fmt:message key="messageFormats.changeScreen"/>

</c:if>

</c:if>
<c:if test="${'save' != requestScope.methodName}">

<c:if test="${'update' != requestScope.methodName}">

<fmt:message key="messageFormats.viewScreen"/>

</c:if>

</c:if>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
/**
 * When document is loaded, we attach a handler to a click event for the radio input
 * to update style && find each 'tr' that contains a 'td' which text ends with '*'
 *
 * then apply the style 'required' for each empty input,select
 * Added by Med Amine Ben Ahmed
 *
 **/

 $(window).on("load",function(){

	    setParentChildsFocus();
		focusOnBodyLoad();
		bodyOnLoad();
		ShowErrMsgWindow('${actionError}');

	 $("input[type=text]:enabled ").css('background','white');
	 $("input[type=text]:disabled").addClass('is-disabled ');
	 $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function()
				{
					var tr = $(this);
					tr.find('input:not([type=checkbox])').not('[disabled="disabled"]').css('border-color',function(index){
												if ($(this).val() == '')
													return 'red';
											}).change(function(){
												if ($(this).val() == '')
													$(this).css('border','red 1px solid');
										  		else
										  		{$(this).css('border-color','');
												$(this).addClass('inputText');}
											});
			 });
	   $("input[type='radio']").on('click', function(){
		 	$("input[type='text']").removeClass('is-disabled ');
		   	$("input[type=text]:disabled").addClass('is-disabled ');
		    $("input[type='text']").css('border-color','');
		    $("input[type='text'],select").not('[disabled="disabled"]').css('background','white');
		    $("input[disabled='disabled'][type='text'],select[disabled='disabled']").css('background','#e5e5e5');
		    $("div").removeClass('divRed');
		    $("tr:has(td:endsWith('*')),tr:has(th:endsWith('*'))").each(function()
			{
				var tr = $(this);
				tr.find('input:not([type=checkbox])').not('[disabled="disabled"]').css('border-color',function(index){
											if ($(this).val() == '')
												return 'red';
										}).change(function(){
											if ($(this).val() == '')
												$(this).css('border','red 1px solid');
									  		else
									  		{$(this).css('border-color','');
											$(this).addClass('inputText');}
										});
				  if (tr.find('select').val() == ''){
					   tr.find('div').addClass(function(index){
						   											return 'divRed';
										}).change(function() {
											if (tr.find('select').val() == ''){
												tr.find('div').removeClass();
												tr.find('div').addClass('divRed');
											}else{
												tr.find('div').removeClass('divRed');
												tr.find('div').addClass('divNORed');
												}
											});
									}
			});
		});

});
<c:if test="${'yes' == requestScope.parentFormRefresh}">
window.opener.document.forms[0].method.value="display";
window.opener.document.forms[0].submit();
self.close();

</c:if>

var formatType = "";
formatType = '${sessionScope.formatTypeInSession}';
var usage = "";
usage = '${sessionScope.usageInSession}';
var isformatTypeOther = false;
var isformatTypeSweep = true;
var isformatTypeFixed = true;
var isformatTypeDelimited = false;
var isformatTypeTagged = false;
var copyFromAddOrChange = '${requestScope.methodName}';
//Code Modified by sandeepkumar for Mantis 2092
var entityName = '${requestScope.entityName}';
//This funtion is used to load the entire screen.
function bodyOnLoad()
{


<c:if test="${'yes' != requestScope.isViewFormat}">

changeOutput();


</c:if>

<c:if test="${'yes' == requestScope.isViewFormat}">

document.forms[0].elements["messageFormats.interfaceId"].disabled=true;

</c:if>

	document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
	document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].disabled = "true";
	document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].disabled = "true";

	document.forms[0].sessionFormatType.value = '${sessionScope.formatTypeInSession}';
	document.forms[0].sessionUsage.value = '${sessionScope.usageInSession}';
	if(document.forms[0].sessionFormatType.value == "")
		{
		   document.forms[0].sessionFormatType.value = "<%=SwtConstants.FORMAT_TYPE_FIXED%>"
        }
	if(document.forms[0].sessionUsage.value == "" ){
		document.forms[0].sessionUsage.value  = "<%=SwtConstants.FORMAT_TYPE_SWEEP%>"
	}
	if(document.forms[0].sessionUsage.value == "<%=SwtConstants.FORMAT_TYPE_SWEEP%>")
	{

		<c:if test="${'yes' != requestScope.isViewFormat}">

			document.forms[0].elements["messageFormats.authorizeFlag"].disabled = "";
			document.forms[0].elements["messageFormats.overdueTime"].disabled = "";

</c:if>
		isformatTypeOther = false;
	    isformatTypeSweep = true;

	}

	if(document.forms[0].sessionUsage.value == "<%=SwtConstants.FORMAT_TYPE_OTHER%>"){
		<c:if test="${'yes' != requestScope.isViewFormat}">

			document.forms[0].elements["messageFormats.authorizeFlag"].disabled = "true";
			document.forms[0].elements["messageFormats.overdueTime"].disabled = "true";

</c:if>
		isformatTypeOther = true;
	    isformatTypeSweep = false;
	}
	document.forms[0].messageFieldDetailsInSessionSize.value = '${sessionScope.messageFieldDetailsInSessionSize}';
	if(document.forms[0].sessionFormatType.value == "<%=SwtConstants.FORMAT_TYPE_DELIMITED%>")
		{
			<c:if test="${'yes' != requestScope.isViewFormat}">

				document.getElementById("FieldDelimiter").style.display = "none";
				document.getElementById("FieldDelimiterDelimited").style.display = "block";
				document.forms[0].elements["messageFormats.hexaFldDelimeter"][1].disabled = "";
				document.forms[0].elements["messageFormats.fieldDelimeter"][1].disabled = "";

</c:if>
			  isformatTypeFixed = false;
			  isformatTypeDelimited = true;
			  isformatTypeTagged = false;
		}

	if(document.forms[0].sessionFormatType.value == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>" || document.forms[0].sessionFormatType.value == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>")
		{
		<c:if test="${'yes' != requestScope.isViewFormat}">

			document.getElementById("MessageSeparator").style.display = "none";
			document.getElementById("MessageSeparatorTagged").style.display = "block";
			document.forms[0].elements["messageFormats.hexaMsgSeparator"][1].disabled = "";
			document.forms[0].elements["messageFormats.msgSeparator"][1].disabled = "";

</c:if>
		  isformatTypeFixed = false;
		  isformatTypeDelimited = false;
		  isformatTypeTagged = true;
		}
	if(document.forms[0].sessionFormatType.value == "<%=SwtConstants.FORMAT_TYPE_FIXED%>")
		{
		 <c:if test="${'yes' != requestScope.isViewFormat}">

			 document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
			 document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
			 document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].disabled = "true";
			 document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].disabled = "true";

</c:if>
		 isformatTypeFixed = true;
         isformatTypeDelimited = false;
         isformatTypeTagged = false;
		}

   document.getElementById("entityDesc").innerText = '${entityName}';
   var cancelcloseElements = new Array(1);
   cancelcloseElements[0] = "closebutton";
}
/*Start:Code Modified by sandeepkumar for Mantis 2092:Session Related issues */
/**
 * submitForm
 *
 * This method is used to submit the form while perform button operation on save && change
 * @param methodName
 */
function submitForm(methodName){
	document.forms[0].entityName.value=entityName;
	if(validateField(document.forms[0].elements['messageFormats.id.formatId'],'messageFormats.id.formatId','alphaNumPat')){
    if(validateField(document.forms[0].elements['messageFormats.formatName'],'messageFormats.formatName','alphaNumPatWithSpace')){
	      var fileValue=validateField(document.forms[0].elements['messageFormats.fileName'],'messageFormats.formatName','alphaNumPatWithUnderScoreAndDotAndQuestionmark');
	 if(fileValue){
  		var formatTypeDoNotMatch = false;
		if(methodName != 'cancel'){
		var fieldCollectionSize =  document.forms[0].messageFieldDetailsInSessionSize.value
		var sessionFormatType =  document.forms[0].sessionFormatType.value;
		var sessionUsage =  document.forms[0].sessionUsage.value;
		//if format type && session format type values are not equal make the message field details size as zero
 	 	 if(formatType != sessionFormatType){
  			fieldCollectionSize = "0";
			 }
/*End:Code Modified by sandeepkumar for Mantis 2092:Session Related issues */
  		if(validateForm(document.forms[0])){
			if(isformatTypeDelimited){
				enableFields();
				document.forms[0].method.value = methodName;
				document.forms[0].elements["messageFormats.fileName"].disabled="";
				document.forms[0].elements["messageFormats.interfaceId"].disabled="";
				document.forms[0].submit();
    		}
			if(isformatTypeTagged){
				if(validateFormTagged(document.forms[0])){
					if(fieldCollectionSize !="0" && fieldCollectionSize != ""){
						enableFields();
						document.forms[0].method.value = methodName;
						document.forms[0].elements["messageFormats.fileName"].disabled="";
						document.forms[0].elements["messageFormats.interfaceId"].disabled="";
						document.forms[0].submit();
					}else{
						alert('<fmt:message key="messageFormatAdd.alert.defineFields"/>');
					}
	   		 	}
     		}
			if(isformatTypeFixed){
				if(fieldCollectionSize !="0" && fieldCollectionSize != ""){
					enableFields();
					document.forms[0].method.value = methodName;
					document.forms[0].elements["messageFormats.fileName"].disabled="";
					document.forms[0].elements["messageFormats.interfaceId"].disabled="";
					document.forms[0].submit();
				}else{
					alert('<fmt:message key="messageFormatAdd.alert.defineFields"/>');
				}
			}

		}
 	}else{
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
 	}
  }else{
  	document.forms[0].elements["messageFormats.fileName"].focus();
  }
 }else {
 	document.forms[0].elements["messageFormats.formatName"].focus();
 }
}else {
 document.forms[0].elements["messageFormats.id.formatId"].focus();
 }
}

function submitCopyFrom(methodName){
	document.forms[0].elements["messageFormats.id.formatId"].disabled = false;
	var param = 'messageformats.do?method='+methodName+'&formatId=';
	param += document.forms[0].elements['messageFormats.id.formatId'].value;
	param += '&entityCode=' + document.forms[0].elements['messageFormats.id.entityId'].value;
	param += '&entityName=' +document.getElementById('entityDesc').innerText;
	param += '&copyFromAddOrChange=' +copyFromAddOrChange;
	param += '&newFormatId=' + document.forms[0].elements["messageFormats.id.formatId"].value;
	<c:if test="${'update' == requestScope.methodName}">

		document.forms[0].elements["messageFormats.id.formatId"].disabled = "true";

</c:if>
	return  param;
}

/**
 * buildMessageFields
 *
 * This method is used to open the subscreen of add message filed screen with all details
 * @param methodName
 */
 /*Start : Code Modified by sandeepkumar for Mantis 2092*/
function buildMessageFields(methodName){
   	var param = 'messagefields.do?method='+methodName+'&formatId=';
	param += document.forms[0].elements['messageFormats.id.formatId'].value;
	param += '&formatType=' + formatType;
	param += '&usage=' + usage;
	param += '&fieldSize='+ document.forms[0].messageFieldDetailsInSessionSize.value;
	param += '&isViewFormat=' + '${requestScope.isViewFormat}';
	return  param;
}
/**
 * enableFields
 * This method is used to  check enble field id values for the whole form
 */
function enableFields(){
	document.forms[0].elements["messageFormats.id.entityId"].disabled = "";
	document.forms[0].elements["messageFormats.path"].disabled = "";
	document.forms[0].elements["messageFormats.id.formatId"].disabled = false;
}
/*End : Code Modified by sandeepkumar for Mantis 2092*/
//This method is used to  check for mandatory for the whole form
function validateForm(objForm){
  var elementsRef = new Array(3);

  elementsRef[0] = objForm.elements["messageFormats.id.formatId"];
 elementTrim(document.forms[0]);
  elementsRef[1] = objForm.elements["messageFormats.formatName"];
  if(document.getElementById("fileName").innerText=="*")
  {
  elementsRef[2] = objForm.elements["messageFormats.fileName"];
  }
  else
  {

    elementsRef[2] = objForm.elements["messageFormats.interfaceId"];
  }

  var rtn = validate (elementsRef) && checkOverdue (document.getElementById('overdue').value);
  return rtn;
}

function validateFormDelimited(objForm){
  var elementsRef = new Array(1);

  elementsRef[0] = objForm.elements["messageFormats.fieldDelimeter"][1];
  return validate(elementsRef);
}

function validateFormTagged(objForm){
  var elementsRef = new Array(1);

  elementsRef[0] = objForm.elements["messageFormats.msgSeparator"][1];
  return validate(elementsRef);
}
/**
 * clickMessageFields
 *This method is used to check the message field details size in session && to open the message field screen
 */
function clickMessageFields(){
	var sessionFormatType = document.forms[0].sessionFormatType.value;
	var messageFieldDetailsInSessionSize = document.forms[0].messageFieldDetailsInSessionSize.value;
	var openScreen = false;
	/*Start:code modified by sandeepkumar for mantis 2092:Session Related issues*/
	//if format type && session format type values are not equal make the message field details size as zero
 	 if(formatType != sessionFormatType){
  		messageFieldDetailsInSessionSize = "0";
	 }
	 /*End:code modified by sandeepkumar for mantis 2092:Session Related issues*/
	if(messageFieldDetailsInSessionSize !="0" && messageFieldDetailsInSessionSize != ""){
		if(formatType == ""){
		 formatType = sessionFormatType;
		}
		if(formatType == sessionFormatType){
			openScreen = true;
		}else{
			var yourstate=window.confirm('<fmt:message key="messageFormatAdd.alert.fieldsRemoved"/>');
			if (yourstate==true){
			openScreen = true;
		}
	 }
    }else
		openScreen = true;
	/*Modified width value*/
	if(openScreen)

		openWindow(buildMessageFields('fields'),'messagefieldsWindow','left=50,top=190,width=998,height=538,toolbar=0, resizable=yes,status=yes,scrollbars=yes','true');
}



function checkUsage(element){
	usage = element.value;
	if(usage == "<%=SwtConstants.FORMAT_TYPE_OTHER%>"){
 	  document.forms[0].elements["messageFormats.authorizeFlag"].checked = "";
 	  document.forms[0].elements["messageFormats.authorizeFlag"].disabled = "true";
 	  document.forms[0].elements["messageFormats.overdueTime"].value = "";
 	  document.forms[0].elements["messageFormats.overdueTime"].disabled = "true";

 	  isformatTypeOther = true;
      isformatTypeSweep = false;
 	}
	if(usage == "<%=SwtConstants.FORMAT_TYPE_SWEEP%>"){

 	  document.forms[0].elements["messageFormats.authorizeFlag"].disabled = "";
 	  document.forms[0].elements["messageFormats.authorizeFlag"].checked = "";
 	  document.forms[0].elements["messageFormats.overdueTime"].disabled = "";
 	  document.forms[0].elements["messageFormats.overdueTime"].value = "";

 	  isformatTypeOther = false;
      isformatTypeSweep = true;


 	}
}

 function checkFormatType(element)
 {
  	formatType = element.value;

  if(element.value == "<%=SwtConstants.FORMAT_TYPE_FIXED%>")
	{

	  document.getElementById("FieldDelimiter").style.display = "block";
	  document.getElementById("FieldDelimiterDelimited").style.display = "none";

	  document.getElementById("MessageSeparator").style.display = "block";
	  document.getElementById("MessageSeparatorTagged").style.display = "none";

	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].value = "";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].checked = "";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].disabled = "true";

	  document.forms[0].elements["messageFormats.msgSeparator"][0].value = "";
	  document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].checked = "";
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].disabled = "true";

	  isformatTypeFixed = true;
      isformatTypeDelimited = false;
      isformatTypeTagged = false;

	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"].disabled = "true";

	}

	 if(element.value == "<%=SwtConstants.FORMAT_TYPE_DELIMITED%>")
	{

	  document.getElementById("FieldDelimiter").style.display = "none";
	  document.getElementById("FieldDelimiterDelimited").style.display = "block";

	  document.getElementById("MessageSeparator").style.display = "block";
	  document.getElementById("MessageSeparatorTagged").style.display = "none";



	  document.forms[0].elements["messageFormats.fieldDelimeter"][1].disabled = "";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][1].value = "";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].value = "";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"][1].disabled = "";
	   document.forms[0].elements["messageFormats.hexaFldDelimeter"][1].checked = "";


	  document.forms[0].elements["messageFormats.msgSeparator"][0].value = "";
	  document.forms[0].elements["messageFormats.msgSeparator"][1].value = "";
	  document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.msgSeparator"][1].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].checked = "";
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][0].disabled = "true";

	   isformatTypeFixed = false;
       isformatTypeDelimited = true;
       isformatTypeTagged = false;


	}

	if(element.value == "<%=SwtConstants.FORMAT_TYPE_TAGGED%>" || element.value == "<%=SwtConstants.FORMAT_TYPE_TAGGED_VARIABLE%>")

	{

	  document.getElementById("MessageSeparator").style.display = "none";
	  document.getElementById("MessageSeparatorTagged").style.display = "block";

	  document.getElementById("FieldDelimiter").style.display = "block";
	  document.getElementById("FieldDelimiterDelimited").style.display = "none";

	  document.forms[0].elements["messageFormats.msgSeparator"][1].disabled = "";
	  document.forms[0].elements["messageFormats.msgSeparator"][1].value = "";
	  document.forms[0].elements["messageFormats.msgSeparator"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.msgSeparator"][0].value = "";

	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][1].disabled = ""
	  document.forms[0].elements["messageFormats.hexaMsgSeparator"][1].checked = ""


	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].value = "";
	  document.forms[0].elements["messageFormats.fieldDelimeter"][0].disabled = "true";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].checked = "";
	  document.forms[0].elements["messageFormats.hexaFldDelimeter"][0].disabled = "true";

	   isformatTypeFixed = false;
       isformatTypeDelimited = false;
       isformatTypeTagged = true;
	}
}

function isNumeric(sText) {
	var ValidChars = "0123456789";
	var IsNumber = true;
	var Char;

	for (i = 0; i < sText.length && IsNumber == true; i++) {
		Char = sText.charAt(i);
		if (ValidChars.indexOf(Char) == -1)
			IsNumber = false;
	}
	return IsNumber;
}

function checkOverdue (sText) {
	if(sText.length==0){
		return true;
	}
	var rtn = true;
	if (sText.length!=8)
		rtn = false;
	else if (sText.substr(2,1)!=":" || sText.substr(5,1)!=":")
		rtn = false;
	else if (!isNumeric (sText.substr(0,2)) || !isNumeric (sText.substr(3,2)) || !isNumeric (sText.substr(6,2)))
		rtn = false;

	if (! rtn)
		alert ('<fmt:message key="messageFormats.alert.overdueFormat"/>');

	return rtn;
}

function focusOnBodyLoad()
{
	<c:if test="${'yes' != requestScope.isViewFormat}">

	setFocus(document.forms[0]);


</c:if>

	<c:if test="${'yes' == requestScope.isViewFormat}">

	setTitleSuffix(document.forms[0]);

</c:if>

}

window.onunload=call;
document.onmousemove=reportMove;
//This method is used to determine,which field to be disabled while changing Output Radio buton groups
function changeOutput()
{
//Condtion to find which radio button is checked
if(document.forms[0].elements["messageFormats.outputType"][0].checked==true)
{
//disabling interfaceid Field
document.forms[0].elements["messageFormats.interfaceId"].disabled=true;
document.forms[0].elements["messageFormats.fileName"].disabled="";
//Make file name field mandatory field
document.getElementById("fileName").innerText="*";
//Make interfaceID field Non mandatory field
document.getElementById("interfaceName").innerText="";

}
else
{
//disabling file name
document.forms[0].elements["messageFormats.fileName"].disabled=true;
document.forms[0].elements["messageFormats.interfaceId"].disabled="";
document.forms[0].elements["messageFormats.fileName"].style.borderColor="";
//Make file name field Non mandatory field
document.getElementById("fileName").innerText="";
//Make interfaceID field mandatory field
document.getElementById("interfaceName").innerText="*";
}
}

</SCRIPT>


</head>



<body>
<form action="messageformats.do"  onsubmit="return validate(this);">
<input name="method" type="hidden" value="">
<input name="formatId" type="hidden" value="">
<input name="copiedEntityId" type="hidden" value="">
<input name="entityCode" type="hidden" value="">
<input name="sessionFormatType" type="hidden" value="">
<input name="sessionUsage" type="hidden" value="">
<input name="messageFieldDetailsInSessionSize" type="hidden" value="">
<input name="copyFromAddOrChange" type="hidden" value="">
<input name="previousFormatId" type="hidden" value="">
<input name="newFormatId" type="hidden" value="">
<!-- code modified by sandeepkumar for mantis 2092 -->
<input name="entityName" type="hidden" value="">
<input name="selectedEntityCode" type="hidden" value="">
<div id="AccountMaintenance"
		style="position: absolute; left: 10px; top: 10px; width: 650px; height: 30px; border: 2px outset;" color="#7E97AF">
<div >
   	<table width="619px" border="0" cellpadding="1" cellspacing="1" class="content" style="margin-left: 5px;">
        <tr height="25px">
          <td width="38px"><b><fmt:message key="message.entityId"/></b></td>
 			  <td width="28px">&nbsp;</td>
          <td width="440px">
             <input type="text"  name="messageFormats.id.entityId" value="${messageFormats.id.entityId}"   style="width:170px;" disabled="true" /> &nbsp;&nbsp;&nbsp;&nbsp; <span id="entityDesc" name="entityDesc" class="spantext">
          </td>
        </tr>
	</table>
</div>
</div>
<div color="#7E97AF"	style="position: absolute; border: 2px outset; left: 10px; top: 50px; width: 650px; height: 380px;">
  <div id="useroptions" style="position:absolute;  top:4px; width:620px; height:38px;">

   <div style="left:8px; top:4px;">
   <div style="height: 160px;" >
   	<fieldset style="border:2px groove;margin-left:14;width: 620px;">
		<legend><fmt:message key="messageFormats.msgFormatDetails"/></legend>
		 <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
	         <tr height="26px">
			  <c:if test="${'yes' == requestScope.isViewFormat}">

				<td width="144px">&nbsp;<b><fmt:message key="messageFormats.id.formatId"/></b></td>

</c:if>

			   <c:if test="${'yes' != requestScope.isViewFormat}">

				<td width="144px">&nbsp;<b><fmt:message key="messageFormats.id.formatId"/></b>*</td>

</c:if>

  			  <td width="28px">&nbsp;</td>
			  <td width="432px">
				<input type="text" tabindex="1" titleKey="tooltip.msgID" maxlength  = "12" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> name="messageFormats.id.formatId" value="${messageFormats.id.formatId}"  style="width:120px;" onchange="return validateField(this,'messageFormats.id.formatId1','alphaNumPat');" />
			  </td>
			   <c:if test="${'yes' != requestScope.isViewFormat}">

			   <td width="288px">
					<input type="radio" name="messageFormats.usage" ${messageFormats.usage == SwtConstants.FORMAT_TYPE_SWEEP ? 'checked="checked"' : ''}  style="width:13px;" value="${SwtConstants.FORMAT_TYPE_SWEEP}" id="1" tabindex="3" titleKey="tooltip.selectSweep" onclick = "checkUsage(this)" />
						 <label title='<fmt:message key="tooltip.selectSweep"/>' tabindex="3" for="1" ><fmt:message key="messageFormats.usage.sweep"/></label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio" name="messageFormats.usage"  style="width:13px;" ${messageFormats.usage == SwtConstants.FORMAT_TYPE_OTHER ? 'checked="checked"' : ''} value="${SwtConstants.FORMAT_TYPE_OTHER}" id="2" tabindex="4" titleKey="tooltip.selectOther" onclick = "checkUsage(this)" />
						<label title='<fmt:message key="tooltip.selectOther"/>' for="2" tabindex="4"><fmt:message key="messageFormats.usage.other"/></label>&nbsp;&nbsp;&nbsp;
			 	</td>

</c:if>

			 <c:if test="${'yes' == requestScope.isViewFormat}">

			 	<td width="288px">
					<input type="radio"  name="messageFormats.usage" disabled = "true" style="width:13px;" ${messageFormats.usage == SwtConstants.FORMAT_TYPE_SWEEP ? 'checked="checked"' : ''} value="${SwtConstants.FORMAT_TYPE_SWEEP}" id="1"  titleKey="tooltip.selectSweep" onclick = "checkUsage(this)" />
						 <label title='<fmt:message key="tooltip.selectSweep"/>'  for="1" ><b><fmt:message key="messageFormats.usage.sweep"/></b></label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio"  name="messageFormats.usage" disabled = "true" style="width:13px;" value="${SwtConstants.FORMAT_TYPE_OTHER}" ${messageFormats.usage == SwtConstants.FORMAT_TYPE_OTHER ? 'checked="checked"' : ''}  id="2" titleKey="tooltip.selectOther" onclick = "checkUsage(this)" />
						<label title='<fmt:message key="tooltip.selectOther"/>' for="2" ><b><fmt:message key="messageFormats.usage.other"/><b></label>&nbsp;&nbsp;&nbsp;
			 	</td>

</c:if>

	        </tr>


			<tr height="26px">
			 <c:if test="${'yes' == requestScope.isViewFormat}">

				<td >&nbsp;<b><fmt:message key="messageFormats.formatName"/></b></td>

</c:if>

			 <c:if test="${'yes' != requestScope.isViewFormat}">

				<td >&nbsp;<b><fmt:message key="messageFormats.formatName"/></b>*</td>

</c:if>

  			  <td >&nbsp;</td>
			  <c:if test="${'yes' != requestScope.isViewFormat}">

				  <td  colspan="2">
					<input type="text"  name="messageFormats.formatName" value="${messageFormats.formatName}"  maxlength  = "30"  tabindex="2" titleKey="tooltip.msgname"  style="width:280px;" onchange="return validateField(this,'messageFormats.formatName','alphaNumPatWithSpace');" />
				  </td>

</c:if>

			   <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td  colspan="2">
					<input type="text"  name="messageFormats.formatName" value="${messageFormats.formatName}"  maxlength  = "30"  tabindex="2" disabled = "true" titleKey="tooltip.msgname"   style="width:280px;" onchange="return validateField(this,'messageFormats.formatName','alphaNumPatWithSpace');" />
				  </td>

</c:if>

	        </tr>


			<tr height="26px">
			  <td >&nbsp;<b><fmt:message key="messageFormats.formatType"/></b></td>
  			  <td >&nbsp;</td>
  			  <!-- Start:code modified by sandeepkumar for Manits 2093 -->
			 <c:if test="${'yes' != requestScope.isViewFormat}">

				  <td  colspan="2">
					<input type="radio" name="messageFormats.formatType" style="width:13px;" ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_FIXED ? 'checked="checked"' : ''}  value="${SwtConstants.FORMAT_TYPE_FIXED}" id="1" tabindex="3" titleKey="tooltip.selectFixed" onclick = "checkFormatType(this)" />
						 <label title='<fmt:message key="tooltip.selectFixed"/>' tabindex="3" for="1" ><fmt:message key="messageFormats.formatType.fixed"/></label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio"  name="messageFormats.formatType"  style="width:13px;" value="${SwtConstants.FORMAT_TYPE_DELIMITED}" ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_DELIMITED ? 'checked="checked"' : ''} id="2" tabindex="4" titleKey="tooltip.selectDelimited" onclick = "checkFormatType(this)" />
						<label title='<fmt:message key="tooltip.selectDelimited"/>' for="2" tabindex="4"><fmt:message key="messageFormats.formatType.delimited"/></label>&nbsp;&nbsp;&nbsp;
					<input type="radio"  titleKey="tooltip.selectMultiline" id="3" tabindex="5" style="width:13px;" name="messageFormats.formatType" value="${SwtConstants.FORMAT_TYPE_TAGGED}"  ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_TAGGED? 'checked="checked"' : ''} onclick = "checkFormatType(this)"/>
						<label title='<fmt:message key="tooltip.selectMultiline"/>' for="3" tabindex="5"><fmt:message key="messageFormats.formatType.tagged"/></label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio"  titleKey="tooltip.selectMultiline" id="4" tabindex="6" style="width:13px;" name="messageFormats.formatType"  ${messageFormats.formatType == 'X' ? 'checked="checked"' : ''}  value="X" onclick = "checkFormatType(this)" />
						<label title='<fmt:message key="tooltip.selectMultilineVariablesFields"/>' for="4" tabindex="6"><fmt:message key="messageFormats.formatType.tagged.variable"/></label>


				  </td>



</c:if>

			 <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td  colspan="2" >
					<input type="radio" name="messageFormats.formatType" ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_FIXED ? 'checked="checked"' : ''}  value="${SwtConstants.FORMAT_TYPE_FIXED}"    disabled = "true" style="width:13px;" value="${SwtConstants.FORMAT_TYPE_FIXED}" id="1"  titleKey="tooltip.selectFixed" onclick = "checkFormatType(this)" />
						 <label title='<fmt:message key="tooltip.selectSubmit"/>'  for="1" ><fmt:message key="messageFormats.formatType.fixed"/></label>&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio"  name="messageFormats.formatType" disabled = "true" style="width:13px;" value="${SwtConstants.FORMAT_TYPE_DELIMITED}" ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_DELIMITED ? 'checked="checked"' : ''} id="2" titleKey="tooltip.selectDelimited" onclick = "checkFormatType(this)" />
						<label title='<fmt:message key="tooltip.selectDelimited"/>' for="2" ><fmt:message key="messageFormats.formatType.delimited"/></label>&nbsp;&nbsp;&nbsp;
					<input type="radio"  titleKey="tooltip.selectTagged" id="3" disabled = "true" style="width:13px;" name="messageFormats.formatType"  value="${SwtConstants.FORMAT_TYPE_TAGGED}"  ${messageFormats.formatType == SwtConstants.FORMAT_TYPE_TAGGED? 'checked="checked"' : ''} onclick = "checkFormatType(this)" />
						<label title='<fmt:message key="tooltip.selectTagged"/>'  for="3" ><fmt:message key="messageFormats.formatType.tagged"/></label>
						&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="radio"  titleKey="tooltip.selectMultiline" id="4" tabindex="6" style="width:13px;" name="messageFormats.formatType"   ${messageFormats.formatType == 'X' ? 'checked="checked"' : ''}  value="X" disabled = "true" />
						<label title='<fmt:message key="tooltip.selectMultiline"/>' for="4" tabindex="6"><fmt:message key="messageFormats.formatType.tagged.variable"/></label>


				  </td>

</c:if>

	        </tr>


			<tr height="26px">
			  <td >&nbsp;<b><fmt:message key="messageFormats.authorizeFlag"/></b></td>
  			  <td >&nbsp;</td>
			  <c:if test="${'yes' != requestScope.isViewFormat}">

				  <td colspan="2">
				  <input type="checkbox" style="width:13px;" titleKey="tooltip.selectFormatReqAuth"  tabindex="6" name="messageFormats.authorizeFlag" value="Y" ${requestScope.messageFormats.authorizeFlag == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.messageFormats.authorizeFlag == "Y"}' />
				  </td>

</c:if>

			 <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td colspan="2" >
				  <input type="checkbox" style="width:13px;" disabled = "true" titleKey="tooltip.selectFormatReqAuth"  tabindex="6" name="messageFormats.authorizeFlag" value="Y" ${requestScope.messageFormats.authorizeFlag == 'Y' ? 'checked' : ''} fieldValue="Y" value='${requestScope.messageFormats.authorizeFlag == "Y"}' />
				  </td>

</c:if>

	        </tr>
	        <tr height="26px">
			  <td>&nbsp;<b><fmt:message key="messageFormats.overdue"/></b></td>
  			  <td>&nbsp;</td>
			  <c:if test="${'yes' != requestScope.isViewFormat}">

				  <td colspan="2"><input type="text" id="overdue" size="8" maxlength="8" titleKey="tooltip.overdue"  tabindex="7" name="messageFormats.overdueTime" value="${messageFormats.overdueTime}"  onchange="return validateField(this,'Number 2','timePatWithSec');" /></td>

</c:if>

			 <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td colspan="2"><input type="text" disabled="true" size="8" maxlength="8" id="overdue" titleKey="tooltip.overdue"  tabindex="7" name="messageFormats.overdueTime" value="${messageFormats.overdueTime}"  /></td>

</c:if>
	        </tr>
		</table>
	</fieldset>
	</div>
	<div style="height: 72px;" >
	<fieldset style="border:2px groove; margin-left:14;width: 620px;">
		<legend><fmt:message key="messageFormats.msgStructure"/></legend>

		<div id="FieldDelimiter">
		 <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="26px">
			  <td width="140px" align = "left">&nbsp;<b><fmt:message key="messageFormats.fieldDelimeter1"/></b></td>

  			  <td width="32px">&nbsp;</td>
			  <td width="90px">
				<input type="text"  name="messageFormats.fieldDelimeter" value="${messageFormats.fieldDelimeter}"  maxlength  = "4" d<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> titleKey="tooltip.enterFieldDelimiter" tabindex="7" style="width:50px;" />
			  </td>
			    <td width="22px">&nbsp;</td>

				  <td width="139px"><b><fmt:message key="messageFormats.hexaFldDelimeter"/></b></td>

				 <td width="183px">
				<input type="checkbox" titleKey="tooltip.enterHexaFieldDelimiter" name="messageFormats.hexaFldDelimeter" value="Y" ${requestScope.messageFormats.hexaFldDelimeter == 'Y' ? 'checked' : ''} d<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:13px;" fieldValue="Y" value='${requestScope.messageFormats.hexaFldDelimeter == "Y"}' tabindex="8" />
			   </td>
	        </tr>
			</table>
		</div>

		<div id="FieldDelimiterDelimited" style="display:none;">
		 <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="26px">
			  <td width="140px">&nbsp;<b><fmt:message key="messageFormats.fieldDelimeter1"/></b></td>

  			  <td width="32px">&nbsp;</td>
			  <td width="90px">
				<input type="text"  name="messageFormats.fieldDelimeter" value="${messageFormats.fieldDelimeter}"  maxlength  = "4"  titleKey="tooltip.enterFieldDelimiter" tabindex="9" style="width:50px;" />
			  </td>
			    <td width="22px">&nbsp;</td>
				  <td width="139px"><b><fmt:message key="messageFormats.hexaFldDelimeter"/></b></td>

				 <td width="183px">

				 <input type="checkbox" titleKey="tooltip.enterHexaFieldDelimiter" name="messageFormats.hexaFldDelimeter" value="Y" ${requestScope.messageFormats.hexaFldDelimeter == 'Y' ? 'checked' : ''} <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:13px;" tabindex="10" fieldValue="Y" value='${requestScope.messageFormats.hexaFldDelimeter == "Y"}' />
			  </td>
	        </tr>
			</table>
		</div>

		<div id="MessageSeparator">
		  <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="26px">
			  <td width="142px">&nbsp;<b><fmt:message key="messageFormats.msgSeparator1"/></b></td>
  			  <td width="26px">&nbsp;</td>
			  <td width="90px">
				<input type="text"  name="messageFormats.msgSeparator" value="${messageFormats.msgSeparator}"  maxlength  = "11" d<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> titleKey="tooltip.enterMsgSeparator" tabindex="9" style="width:90px;" />
			  </td>
			     <td width="20px">&nbsp;</td>
				  <td width="118px"><b><fmt:message key="messageFormats.hexaFldDelimeter"/></b></td>
				  <td width="17px">&nbsp;</td>
				     <td width="180px">
			  	    <input type="checkbox" titleKey="tooltip.enterHexMsgSeparator" name="messageFormats.hexaMsgSeparator" value="Y" ${requestScope.messageFormats.hexaMsgSeparator == 'Y' ? 'checked' : ''} <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:13px;" tabindex="12" fieldValue="Y" value='${requestScope.messageFormats.hexaMsgSeparator == "Y"}' />
			 </td>
			</tr>
			</table>
		</div>

		<div id="MessageSeparatorTagged" style="display:none;">
		  <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="26px">
			  <td width="142px">&nbsp;<b><fmt:message key="messageFormats.msgSeparator1"/></b>*</td>
  			  <td width="28px">&nbsp;</td>
			  <td width="90px">
				<input type="text"  name="messageFormats.msgSeparator" value="${messageFormats.msgSeparator}"  maxlength = "10"  d<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> titleKey="tooltip.enterMsgSeparator"  tabindex="13" style="width:90px;" />
			  </td>
			    <td width="20px">&nbsp;</td>
				  <td width="118px"><b><fmt:message key="messageFormats.hexaFldDelimeter"/></b></td>
				  <td width="20px">&nbsp;</td>
				     <td width="180px">
			  	    <input type="checkbox" titleKey="tooltip.enterHexMsgSeparator" name="messageFormats.hexaMsgSeparator" value="Y" ${requestScope.messageFormats.hexaMsgSeparator == 'Y' ? 'checked' : ''} <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> style="width:13px;" tabindex="14" fieldValue="Y" value='${requestScope.messageFormats.hexaMsgSeparator == "Y"}' />
					</td>
			</tr>
			</table>
		</div>
	</fieldset>
	</div>
	<div style="height: 125px;" >
	<fieldset style="border:2px groove; margin-left:14;margin-top:7;width: 620px;">
		<legend><fmt:message key="messageFormats.outputParameter"/></legend>
		 <table width="615px" border="0" cellpadding="0" cellspacing="1" class="content">
			<tr height="26px">
			  <td width="140px">&nbsp;<b><fmt:message key="messageFormats.outputType1"/></b></td>
  			  <td width="26px">&nbsp;</td>
			  <c:if test="${'yes' != requestScope.isViewFormat}">

				  <td width="429px">

					<input type="radio" name="messageFormats.outputType"  ${messageFormats.outputType == SwtConstants.OUTPUT_TYPE ? 'checked="checked"' : ''} titleKey="tooltip.selectFile" id="5" tabindex="15" style="width:13px;"  value="${SwtConstants.OUTPUT_TYPE}" onclick="javaScript:changeOutput()"/>
						<label title='<fmt:message key="tooltip.selectFile"/>' for="5" tabindex="15"><fmt:message key="messageFormats.outputType.file"/></label> &nbsp;&nbsp;&nbsp;
					<input type="radio"  titleKey="tooltip.selectMQInterface" id="6" style="width:13px;" tabindex="16" name="messageFormats.outputType"  ${messageFormats.outputType == 'Q' ? 'checked="checked"' : ''} value="Q"  onclick="javaScript:changeOutput()" />
						<label title='<fmt:message key="tooltip.selectMQInterface"/>' for="6" tabindex="16"><fmt:message key="messageFormats.outputType.mqInterface"/></label>
				  </td>

</c:if>

			  <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td width="429px">
					<input type="radio" name="messageFormats.outputType"  ${messageFormats.outputType == SwtConstants.OUTPUT_TYPE ? 'checked="checked"' : ''}  titleKey="tooltip.selectFile"  id="5" style="width:13px;" disabled = "true"  value="${SwtConstants.OUTPUT_TYPE}"/>
						<label title='<fmt:message key="tooltip.selectFile"/>' for="5"><fmt:message key="messageFormats.outputType.file"/></label> &nbsp;&nbsp;&nbsp;
					<input type="radio"   titleKey="tooltip.selectMQInterface"  ${messageFormats.outputType == 'Q' ? 'checked="checked"' : ''} value="Q"  id="6" disabled = "true" style="width:13px;" name="messageFormats.outputType"  />

						<label title='<fmt:message key="tooltip.selectMQInterface"/>' for="6" ><fmt:message key="messageFormats.outputType.mqInterface"/></label>
				  </td>
				  <!-- End:code modified by sandeepkumar for Manits 2093 -->

</c:if>

	        </tr>

			<tr height="26px">
			 <c:if test="${'yes' == requestScope.isViewFormat}">

					<td >&nbsp;<b><fmt:message key="messageFormats.path"/></b></td>
  					<td >&nbsp;</td>
					 <td >
					<input type="text" tabindex="17" titleKey="tooltip.enterpath" maxlength  = "100" disabled = "true" name="messageFormats.path" value="${messageFormats.path}"  style="width:280px;" />
					</td>

</c:if>

			   <c:if test="${'yes' != requestScope.isViewFormat}">

					 <td >&nbsp;<b><fmt:message key="messageFormats.path"/></b></td>
  					 <td >&nbsp;</td>
					 <td >
					<input type="text" tabindex="17"  titleKey="tooltip.enterpath" maxlength  = "100" disabled = "true" name="messageFormats.path" value="${messageFormats.path}"  style="width:280px;" />
					</td>

</c:if>
			</tr>

			<tr height="26px">
			 <c:if test="${'yes' != requestScope.isViewFormat}">


			  <td>&nbsp;<b><fmt:message key="messageFormats.fileName"/></b><label id="fileName">*</label></td>
  			  <td >&nbsp;</td>

				  <td >
				   <input type="text"  name="messageFormats.fileName" value="${messageFormats.fileName}"  maxlength  = "30" tabindex="18" titleKey="tooltip.file" style="width:280px;" onchange="return validateField(this,'messageFormats.fileName','alphaNumPatWithUnderScoreAndDotAndQuestionmark');" />

				  </td>

</c:if>

			  <c:if test="${'yes' == requestScope.isViewFormat}">

				  <td >&nbsp;<b><fmt:message key="messageFormats.fileName"/></b></td>
  				<td >&nbsp;</td>

				  <td >
				  <input type="text"  name="messageFormats.fileName" value="${messageFormats.fileName}"  maxlength  = "30" disabled = "true" tabindex="18" titleKey="tooltip.file" style="width:280px;" />
				  </td>

</c:if>

</tr>
<tr height="26px">
  <td>&nbsp;<b><fmt:message key="messageFormats.interfaceId"/></b><label id="interfaceName"></label></td>
  <td >&nbsp;</td>

<td>
<div style="width:280px">
<select id="messageFormats.interfaceId" name="messageFormats.interfaceId" titleKey="tooltip.sweepMsgFormat.selectInterfaceId" style="width:280px">
	<c:forEach var="item" items="${requestScope.interfaceId}">
		<option value="${item.value}" <c:if test="${item.value == messageFormats.interfaceId}">selected</c:if>>${item.label}</option>
	</c:forEach>
</select>

</div>
</td>

</tr>

	      </table>
	</fieldset>
</div>
</div>

<div  style="position: absolute;height: 40px;top:382px;width:649px; border:2px outset; " color="#7E97A">
	<c:if test="${'yes' != requestScope.isViewFormat}">
    <div id="ddimagebuttons" style = "top: 5px; width: 619px; position: relative;" >
		<span><a title='<fmt:message key="tooltip.copyFrom"/>' tabindex="19" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"  onClick="javascript:openWindow(submitCopyFrom('copyFrom'),'','left=50,top=190,width=542px,height=123px,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.cpyFrom"/></a></span>
		<span><a title='<fmt:message key="tooltip.msgfield"/>' tabindex="20" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="clickMessageFields()"><fmt:message key="button.fields"/></a></span>
  <!-- Start:code modified by sandeepkumar for Manits 2092 -->
		<c:if test="${'update' != requestScope.methodName}">

		<span><a title='<fmt:message key="tooltip.save"/>' tabindex="21" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"  onclick="javascript:submitForm('save')"><fmt:message key="button.save"/></a></span>

</c:if>
		<c:if test="${'update' == requestScope.methodName}">

		<span><a title='<fmt:message key="tooltip.save"/>' tabindex="21" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"  onclick="javascript:submitForm('update')"><fmt:message key="button.save"/></a></span>

</c:if>

  	<span><a title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();" tabindex="50"><fmt:message key="button.cancel"/></a></span>
	</div>
</c:if>

	<c:if test="${'yes' == requestScope.isViewFormat}">
    <div id="ddimagebuttons" style = "top: 5px; width: 619px; position: relative;">
		<span><a title='<fmt:message key="tooltip.msgfield"/>' tabindex="19" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="clickMessageFields()"><fmt:message key="button.fields"/></a></span>
		<span><a title='<fmt:message key="tooltip.close"/>' tabindex="20"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close()"><fmt:message key="button.close"/></a></span>
	</div>
</c:if>

	<div  id="container-icons" style="left:580px; top: 5px;position:absolute; ">
		<span><a tabindex="51" href=# onclick="javascript:openWindow(buildPrintURL('print','Default Account Maintenance - Add'),'sectionprintdwindow','left=100,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a></span>
		<span id="Print"><a onclick="printPage();" tabindex="23" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a></span>
	</div>
	<!-- End:code modified by sandeepkumar for Manits 2092 -->
</div>

</form>
</body>
</html>