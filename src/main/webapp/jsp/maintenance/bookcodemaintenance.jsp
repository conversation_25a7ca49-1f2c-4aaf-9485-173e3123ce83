<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.BookCode"%>
<html>
<head>
<title><fmt:message key="bookMaintenance.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</c:if>



var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function bodyOnLoad()
{
	
	xl = new XLSheet("bookCodeColl","table_2", ["String","String", "String", "String", "String" , "String"],"211111");
	xl.onsort = xl.onfilter = onFilterandSort;

	highlightTableRows("bookCodeColl");

	var dropBox1 = new SwSelectBox(document.forms[0].elements["bookCode.id.entityId"],document.getElementById("entityName"));
		
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
 
 
	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	<%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	<%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	<%}%>
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	
	//Start: Create a select Export :Code added by Med Amine
	var headerData = [];
	var dataprovider = new Array();
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
			
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
			
	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });
if(record()== 0)
		 $("#exportReport").disabled(true); 
	else
		 $("#exportReport").disabled(false); 


}
	
function exportReport(){
	var type=$("#exportReport").getSelected(0);
	return exportData(type.toLowerCase());
	}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true)
	{ 
		document.forms[0].submit();
	}
}

function buildChangeGroupURL(methodName){
	var param = 'bookCode.do?method='+methodName+'&entityCode=';
	param +=document.forms[0].elements['bookCode.id.entityId'].value;
	param +='&bookCodeId=';
	param +=document.forms[0].selectedbookCode.value;
	param +='&bookName=';
	param +=document.forms[0].selectedbookName.value;	
	param +='&bookLocation=';
	param +=document.forms[0].selectedbookLocation.value;
	param +='&groupIdLevel1=';
	param +=document.forms[0].selectedgroupIdLevel1.value;	
	param +='&groupIdLevel2=';
	param +=document.forms[0].selectedgroupIdLevel2.value;	
	param +='&groupIdLevel3=';
	param +=document.forms[0].selectedgroupIdLevel3.value;	
	param +='&entityName=';
	param +=document.getElementById("entityName").innerText;
	
	 
return  param;
}

function populate(){
	bodyOnLoad();
}

function onSelectTableRow(rowElement, isSelected)
{
	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedbookCode.value = hiddenElement.value;
	 
	if(menuEntityCurrGrpAccess == "0" && isSelected)
	{
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
}

function record()
{
return document.getElementById ("ddscrolltable").getElementsByTagName('tr').length-1;

}


function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "add"){
		openWindow(buildChangeGroupURL('add'),'groupmaintenanceaddWindow','left=50,top=190,width=643,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
		}else if(method == "change"){
		openWindow(buildChangeGroupURL('change'),'groupmaintenanceaddWindow','left=50,top=190,width=643,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
		}else if(method == "close"){
		confirmClose('P');
		}else if(method == "delete"){
		submitDeleteForm('delete');
		}
	}
}

function exportData(exportType) 
{

document.forms[0].method.value = 'exportBookCode';
document.forms[0].exportType.value = exportType.trim();
document.forms[0].entityCode.value = document.forms[0].elements['bookCode.id.entityId'].value;
if(record() > 0){
document.forms[0].submit();
}
}		
</SCRIPT>	
</head>
<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}'); populate();" onunload="call()">
<form action="bookCode.do">
<input name="method" type="hidden" value="display">
<input name="selectedbookCode" type="hidden" value="">
<input name="selectedbookName" type="hidden" value="">
<input name="selectedbookLocation" type="hidden" value="">
<input name="selectedgroupIdLevel1" type="hidden" value="">
<input name="selectedgroupIdLevel2" type="hidden" value="">
<input name="selectedgroupIdLevel3" type="hidden" value="">

<input name="entityCode" type="hidden" value=" ">
	
<input name="exportType" type="hidden" value="">
<input type="hidden" name="screen" id="exportDataScreen" value="BookMaintenence-SmartPredict"/>



<input name="menuAccessId" type="hidden" >

<div id="BookCode" style="position:absolute; left:20px; top:20px; width:959px; height:38px; border:2px outset;" color="#7E97AF">
	<div id="BookCode" style="position:absolute; left:8px; top:4px; width:780px; height:30;">
		<table width="514px" border="0" cellpadding="0" cellspacing="0" height="25px">
			<tr color="black" border="0">
			  <td width="30px"><b><fmt:message key="bookCode.entity"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="140px">
				  <select id="bookCode.id.entityId" name="bookCode.id.entityId" onchange="submitForm('displayList')" style="width:140px" titleKey="tooltip.selectEntity" tabindex="1">
				    <c:forEach items="${requestScope.entities}" var="entity">
						<option
						  value="${entity.value}"
						  <c:if test="${bookCode.id.entityId == entity.value}">selected="selected"</c:if>>
						  ${entity.label}
					   </option>
					</c:forEach>
				  </select>
			  </td>
			  <td width="20px">&nbsp;</td>
			  <td width="280px">
				<span id="entityName" class="spantext">
			   </td>
			 </tr>
		</table>
	</div>
</div>
<div color="#7E97AF" style="position:absolute; border:0px outset; left:20px; top:63px; width:959px; height:400;">
	<div id="BookCode" style="position:absolute;z-index:99;left:0px; top:0px; width:944px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="931" border="0" cellspacing="1" cellpadding="0"  height="23px">
			<thead>
				<tr height="20px">

					<td title='<fmt:message key="tooltip.sortBookcodeId"/>' width="120px"  style="border-left-width: 0px;"><b><fmt:message key="book.bookCode"/></b></td>
					<td title='<fmt:message key="tooltip.SortByBookCode"/>' width="250px"><b><fmt:message key="bookCode.bookName"/></b></td>
					<td title='<fmt:message key="tooltip.sortBookLocation"/>' width="120px"><b><fmt:message key="bookCode.bookLocation"/></b></td>
					<td title='<fmt:message key="tooltip.GrpLvlName1"/> ${groupLevelName1}' width="150px"><b>${groupLevelName1}</b></td>
					<td title='<fmt:message key="tooltip.GrpLvlName2"/> ${groupLevelName2}' width="150px"><b>${groupLevelName2}</b></td>
					<td title='<fmt:message key="tooltip.GrpLvlName3"/> ${groupLevelName3}' width="140px"><b>${groupLevelName3}</b></td>
				</tr>
			</thead>
		</table>
	</div>
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:955px; height:395px; overflowY:scroll">
		<div id="BookCode" style="position:absolute;z-index:99;left:1px; top:21px; width:936px; height:10px;">
			<table class="sort-table" id="bookCodeColl" width="936" border="0" cellspacing="1" cellpadding="0" height="374">
	<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="bookCodeColl" items="${requestScope.bookCodeColl}">
				<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
					<!-- Start: Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580: "Spaces should not be saved to end of input values." -->
					<c:set var="bookcode" value="${bookCodeColl}" />
					<input type="hidden" name="${bookcode.id.bookCode}" value="${bookcode.id.bookCode}"  disabled="true" />
					<td width="120px" align="left">${bookCodeColl.id.bookCode}&nbsp;</td>
					<input type="hidden" name="${bookcode.bookName}" value="${bookcode.bookName}" disabled="true" />
					<td width="250px">${bookcode.bookName != null ? fn:replace(bookcode.bookName, ' ', '&nbsp;') : ''}&nbsp;</td>
					<!-- End: Code added by Nageswara Rao on 03-Jan-2012 for mantis 1580: "Spaces should not be saved to end of input values." -->
					<input type="hidden" name="${bookcode.bookLocation}" value="${bookcode.bookLocation}" disabled="true" />
					<td width="120px">${bookCodeColl.bookLocation}&nbsp;</td>
					<input type="hidden" name="${bookcode.groupIdLevel1}" value="${bookcode.groupIdLevel1}" disabled="true" />
					<td width="150px">${bookCodeColl.groupIdLevel1}&nbsp;</td>
					<input type="hidden" name="${bookcode.groupIdLevel2}" value="${bookcode.groupIdLevel2}" disabled="true" />
					<td width="150px">${bookCodeColl.groupIdLevel2}&nbsp;</td>
					<input type="hidden" name="${bookcode.groupIdLevel3}" value="${bookcode.groupIdLevel3}" disabled="true" />
					<td width="140px">${bookCodeColl.groupIdLevel3}&nbsp;</td>
				</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
</tbody>
				<tfoot><tr><td colspan="6" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>

<!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
<div id="pnlReport" style="position:absolute; left:895; top:480; width:70px; height:15px; z-index:5; visibility:visible;">
	<table >
	   <div id="exportReport" />
	</table>
	</div>
<div id ="helpIcon" style="position:absolute; left:940; top:478; width:30px;">
	<a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Book Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
		<img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>' ></a> 
	</div>
<!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:468; width:959px; height:39px; visibility:visible;">
<div id="BookCode" style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:visible;">
  <table width="280px" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td width="70px" id="addbutton" ></td>
		<td width="70px" id="changebutton"></td>
		<td width="70px" id="deletebutton"></td>

		<td width="70px" id="closebutton" title='<fmt:message key="tooltip.close"/>'>		
			<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" onKeydown = "onKeyEnterSubmit('close',event);"><fmt:message key="button.close"/></a>			
		</td>

	</tr>
	</table>
</div>
<div style="position:absolute; left:6; top:4; width:290px; height:15px; visibility:hidden;">  	
<table width="280px" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
<tr>

<td id="addenablebutton">		
		<a title='<fmt:message key="tooltip.AddNewBookcode"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeGroupURL('add'),'groupmaintenanceaddWindow','left=50,top=190,width=643,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('add',event);"><fmt:message key="button.add"/></a>
	</td>		
	<td id="adddisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.AddNewBookcode"/>'><fmt:message key="button.add"/></a>
	</td>

	<td id="changeenablebutton">		
		<a title='<fmt:message key="tooltip.ChangeSelectedBookcode"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeGroupURL('change'),'groupmaintenanceaddWindow','left=50,top=190,width=643,height=293,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('change',event);"><fmt:message key="button.change"/></a>
	</td>		
	<td id="changedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.ChangeSelectedBookcode"/>'><fmt:message key="button.change"/></a>
	</td>
	<td id="deleteenablebutton">		
		<a title='<fmt:message key="tooltip.DeleteSelectedBookcode"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete');" onKeydown = "onKeyEnterSubmit('delete',event);"><fmt:message key="button.delete"/></a>
	</td>		
	<td id="deletedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.DeleteSelectedBookcode"/>'><fmt:message key="button.delete"/></a>
	</td>						

</tr>
</table>
</div>
</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>