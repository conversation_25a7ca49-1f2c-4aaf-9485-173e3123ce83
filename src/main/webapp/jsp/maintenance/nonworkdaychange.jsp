<!--
countryoverridechange.jsp 
JSP File to display Add / Chnage Non work day Maintenance Screen, to edit the
Facility settings for each entty 
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
	cancelcloseElements[0]="cancelbutton";
var dateFormat = '${sessionScope.CDM.dateFormat}';
mandatoryFieldsArray=["*"]
/**
 * bodyOnLoad()
 * Method called on body onload to hold selected Facility and other values fro the child screen
 */
 function bodyOnLoad() {

	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	window.opener.document.forms[0].method.value = "showDetails";
	window.opener.document.forms[0].submit();
	self.close();
	</c:if>
	if ('<%=request.getParameter("method")%>' == "change") {

		document.title = '<fmt:message key="nonworkday.title.changeScreen"/>'
		<c:if test="${requestScope.parentFormRefresh != 'yes'}">

		document.getElementById("entityName").innerText = '<%=request.getParameter("EntityName")%>'
		changeinexchange("nonWorkday.applyEntityCountry", '<%=request.getParameter("applEntity")%>')
		changeinexchange("nonWorkday.applyAccountCountry", '<%=request.getParameter("applAccount")%>')
		changeinexchange("nonWorkday.applyCurrencyCountry", '<%=request.getParameter("applCurrency")%>')
		</c:if>
	} else {
		document.title = '<fmt:message key="nonworkday.title.addScreen"/>'
		document.getElementById("entityName").innerText = document.forms[0].entityCode.value
	}
}

/**
 * changeinexchange()
 * @param element
 * @param flag
 * Method to select / de select the radio buttons
 */
function changeinexchange(element,flag)
{
	
	flag=new String(flag);
	if (flag.trim()=="Y")
		document.forms[0].elements[element][0].checked = true;
	else
		document.forms[0].elements[element][1].checked = true;
	}

/**
 * validateForm()
 * @param objForm
 * Method to validate the mandatory fields
 */
function validateForm(objForm){
  var elementsRef = new Array(1);
  elementsRef[0] = objForm.elements["nonWorkday.id.facility"];
  return validate(elementsRef);
 }
 
/**
 * submitForm()
 * Method to save or update the Facility settings
 */
function submitForm(){
	if(validateForm(document.forms[0]) ){
		enableFields()		
		document.forms[0].method.value = "save";
		document.forms[0].submit();
	}

}

/**
 * enableFields()
 * Method to enable fields while submiting the form
 */
function enableFields(){
	document.forms[0].elements["nonWorkday.id.entityId"].disabled = "";
	document.forms[0].elements["nonWorkday.id.facility"].disabled = "";			
}


	
</script>


</head>


<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"
	onunload="call()">
<form action="nonworkday.do" method="post">
	<input name="method" type="hidden" value="display">
	<input name="saveStatus" type="hidden"
		value='${requestScope.saveStatus}'>
	<input name="entityCode" type="hidden"
		value='${requestScope.entityCode}'>

	<input name="screenFieldsStatus" type="hidden"
		value="${requestScope.screenFieldsStatus}">

	<div id="nonworkday"
		style="position: absolute; left: 10px; top: 10px; width: 640px; height: 135px; border: 2px outset;"
		color="#7E97AF">

	<div id="nonworkday"
		style="position: absolute; left: 8px; top: 4px; width: 640px; height: 72;">
	<table width="620" border="0" cellpadding="0" cellspacing="0"
		height="30">
		<tr height="24">
			<td width="140px"><b><fmt:message key="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="280px"><input type="text" name="nonWorkday.id.entityId" value="${nonWorkday.id.entityId}"
				style="width:120px;" styleClass="htmlTextAlpha" disabled="true"
				titleKey="tooltip.entityId" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
				id="entityName" name="#request.entityName" class="spantext"></span></td>


		</tr>

		<tr height="24">
			<td width="140px"><b><fmt:message key="label.nonworkday.facility" />
			<c:if test="${requestScope.screenFieldsStatus == ''}">*</c:if> </b></td>
			<td width="28px">&nbsp;</td>
			<td width="410px">
			<c:if test="${requestScope.screenFieldsStatus == 'true'}">
				<input type="text"  name="nonWorkday.id.facility" style="width:410px;"
					styleClass="htmlTextAlpha" disabled="true"
					titleKey="tooltip.facility" value="${nonWorkday.id.facility}"/>
			</c:if> <c:if test="${requestScope.screenFieldsStatus == ''}">
				<div style="width: 410px">
				<select name="nonWorkday.id.facility" tabindex="1"
					titleKey="tooltip.facility" class="htmlTextAlpha"
					style="width: 410px">
					<c:forEach items="${requestScope.facilitycoll}" var="facility">
					   <option
						  value="${facility.value}"
						  <c:if test="${nonWorkday.id.facility == facility.value}">selected="selected"</c:if>>
						  ${facility.label}
					   </option>
					</c:forEach>
				</select>
				</div>
			</c:if></td>

		</tr>
		<tr height="24">
			<td width="160px"><b><fmt:message key="label.nonworkday.applyEntityCountry"/></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px"><input type="radio" id="1" tabindex="2" style="width:13;"
			titleKey="tooltip.optionYes" name="nonWorkday.applyEntityCountry" ${nonWorkday.applyEntityCountry == '1' ? 'checked="checked"' : 'Yes'} value="${'1'}" />
			<fmt:message key="label.nonworkday.optionYes"/>
			&nbsp;&nbsp;&nbsp; <input type="radio"
				id="2" tabindex="3" style="width:13;"
				name="nonWorkday.applyEntityCountry" ${nonWorkday.applyEntityCountry == '2' ? 'checked="checked"' : 'No'} value="${'2'}"
				titleKey="tooltip.optionNo" /> <fmt:message key="label.nonworkday.optionNo"/></td>

		</tr>
		<tr height="24">
			<td width="160px"><b><fmt:message key="label.nonworkday.applyAccountCountry" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px"><input type="radio" id="3" tabindex="4"
				style="width:13;" titleKey="tooltip.optionYes"
				name="nonWorkday.applyAccountCountry"   ${nonWorkday.applyAccountCountry == '1' ? 'checked="checked"' : 'Yes'} value="${'1'}"/> <fmt:message key="label.nonworkday.optionYes"/>
				&nbsp;&nbsp;&nbsp; <input type="radio"
				id="4" tabindex="5" style="width:13;"
				name="nonWorkday.applyAccountCountry"  ${nonWorkday.applyAccountCountry == '2' ? 'checked="checked"' : 'No'} value="${'2'}"
				titleKey="tooltip.optionNo" /> <fmt:message key="label.nonworkday.optionNo"/></td>

		</tr>
		<tr height="24">
			<td width="160px"><b><fmt:message key="label.nonworkday.applyCurrencyCountry" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px"><input type="radio" id="5" tabindex="6"
				style="width:13;" titleKey="tooltip.optionYes"
				name="nonWorkday.applyCurrencyCountry"  ${nonWorkday.applyCurrencyCountry == '1' ? 'checked="checked"' : 'Yes'} value="${'1'}" />
				<fmt:message key="label.nonworkday.optionYes"/>
				&nbsp;&nbsp;&nbsp; <input type="radio"
				id="6" tabindex="7" style="width:13;"
				name="nonWorkday.applyCurrencyCountry"  ${nonWorkday.applyCurrencyCountry == '2' ? 'checked="checked"' : 'No'} value="${'2'}"
				titleKey="tooltip.optionNo" /> <fmt:message key="label.nonworkday.optionNo"/></td>

		</tr>

	</table>
	</div>
	</div>
	<div id="country"
		style="position: absolute; left: 580; top: 158px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr height="25">

			<td align="Right"><c:if test="${requestScope.screenFieldsStatus == 'true'}">
				<a tabindex="10" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Non Workday Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if> <c:if test="${requestScope.screenFieldsStatus == ''}">
				<a tabindex="11" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Non Workday Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if></td>

			<td align="right" id="Print"><a tabindex="12"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 150; width: 640; height: 39px; visibility: visible;">
	<div id="Holiday"
		style="position: absolute; left: 6; top: 4; width: 460; height: 15px; visibility: visible;">
	<table width="150" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td id="savebutton" width="70px"><a
				title='<fmt:message key="tooltip.save"/>' tabindex="8"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="javascript:submitForm()"><fmt:message key="button.save" /></a></td>
			<td id="cancelbutton" width="70px"><a
				title='<fmt:message key="tooltip.cancel"/>' tabindex="9"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('C');"><fmt:message key="button.cancel" /></a></td>
		</tr>
	</table>
	</div>
	</div>
</form>
<DIV ID="caldiv"
	STYLE="position: absolute; visibility: hidden; background-color: white; layer-background-color: white;"></DIV>
</body>
</html>
