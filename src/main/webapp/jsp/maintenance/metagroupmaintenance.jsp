<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.maintenance.model.MetaGroup"%>
<html>
<head>
<title><fmt:message key="metaGroup.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">



<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh == 'yes'}">

window.opener.document.forms[0].method.value="displayListByEntity";
window.opener.document.forms[0].submit();
self.close();

	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

</c:if>
var entityAccess = "${requestScope.EntityAccess}";
function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
    document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("groupbutton").innerHTML = document.getElementById("groupdisablebutton").innerHTML;
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function bodyOnLoad()
{
	xl = new XLSheet("metaGroupListDetails","table_2", ["String", "String", "String", "Number"],"2111");
	xl.onsort = xl.onfilter = onFilterandSort;

	highlightTableRows("metaGroupListDetails");

	var dropBox1 = new SwSelectBox(document.forms[0].elements["metaGroup.id.entityId"],document.getElementById("entityDesc"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["metaGroup.mgrpLvlCode"],document.getElementById("mgrpLvlName"));

	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	document.getElementById("groupbutton").innerHTML = document.getElementById("groupdisablebutton").innerHTML;

	var buttonStatus = "<%=request.getAttribute(SwtConstants.ADD_BUT_STS)%>";

    if(entityAccess  == "0") {
	document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);

    }else {
		document.getElementById("addbutton").innerHTML = (buttonStatus!="<%=SwtConstants.STR_TRUE%>"?document.getElementById("adddisablebutton").innerHTML:document.getElementById("addenablebutton").innerHTML);
     }
	document.getElementById("addbutton").innerHTML = (buttonStatus!="<%=SwtConstants.STR_TRUE%>"?document.getElementById("adddisablebutton").innerHTML:document.getElementById("addenablebutton").innerHTML);


	buttonStatus = "<%=request.getAttribute(SwtConstants.CHG_BUT_STS)%>";
	document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);


	buttonStatus = "<%=request.getAttribute(SwtConstants.DEL_BUT_STS)%>";
	document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);

	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	<c:if test="${'Y'==requestScope.disableAdd}">

		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;

</c:if>
}
/* This Method is used for save || update the data && close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "add"){
		openWindow(buildAddMetaGroup('add'),'metaGroupmaintenanceaddWindow','left=50,top=190,width=647,height=225,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else if(method == "change"){
		openWindow(buildAddMetaGroup('change'),'metagroupmaintenancechangeWindow','left=50,top=190,width=647,height=225,toolbar=0, resizable=yes, scrollbars=yes','true')
		}else if(method == "close"){
		confirmClose('C');
		}else if(method == "group"){
		openWindow(buildAddMetaGroup('group'),'metaGroupmaintenancegroupWindow','left=50,top=190,width=584,height=514,toolbar=0, resizable=yes, scrollbars=yes','true');
		}else if(method == "delete"){
			submitDeleteForm('delete');
		}
	}
}

function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}
function submitDeleteForm(methodName){
	document.forms[0].method.value = methodName;
	var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
	if (yourstate==true){

	document.forms[0].submit();

	}
}
function buildAddMetaGroup(methodName){

	var metaGropId = new String(document.forms[0].selectedMetaGroupId.value);
	var len = metaGropId.length;

	for(var i = 0; i<len;i++)
	{
		metaGropId = metaGropId.replace('%',' '); // replacing all '%' by ' ' i.e the space character
	}

	for(var i = 0; i<len;i++)
	{
			metaGropId = metaGropId.replace(' ',escape('%')); //now replacing all spaces by the escape code for that character
	}


for(var i = 0; i<len;i++)
	{
		metaGropId = metaGropId.replace('~',escape('~'));
		metaGropId = metaGropId.replace('#',escape('#'));
		metaGropId = metaGropId.replace('!',escape('!'));
		metaGropId = metaGropId.replace('@',escape('@'));
		metaGropId = metaGropId.replace('$',escape('$'));

		metaGropId = metaGropId.replace('^',escape('^'));
		metaGropId = metaGropId.replace('&',escape('&'));

		metaGropId = metaGropId.replace('(',escape('('));
		metaGropId = metaGropId.replace(')',escape(')'));
		metaGropId = metaGropId.replace('-',escape('-'));
		metaGropId = metaGropId.replace('_',escape('_'));
		metaGropId = metaGropId.replace('=',escape('='));
		metaGropId = metaGropId.replace('+',encodeURIComponent('+'));
		metaGropId = metaGropId.replace('[',escape('['));
		metaGropId = metaGropId.replace(']',escape(']'));
		metaGropId = metaGropId.replace(':',escape(':'));
		metaGropId = metaGropId.replace(';',escape(';'));

		metaGropId = metaGropId.replace('"',escape('"'));
		metaGropId = metaGropId.replace(',',escape(','));
		metaGropId = metaGropId.replace('<',escape('<'));
		metaGropId = metaGropId.replace('.',escape('.'));
		metaGropId = metaGropId.replace('>',escape('>'));
		metaGropId = metaGropId.replace('/',escape('/'));
		metaGropId = metaGropId.replace('?',escape('?'));
	}
	var param = 'metaGroup.do?method='+methodName+'&selectedMetaGroupId=';
	param += metaGropId;
	param += '&entityCode=' + document.forms[0].elements['metaGroup.id.entityId'].value;
	param +='&entityName=';
	param +=document.getElementById('entityDesc').innerText;
	param += '&metaGroupLvlCode=' + document.forms[0].elements['metaGroup.mgrpLvlCode'].value;
	param +='&metaGroupLvlName=';
	param +=document.getElementById('mgrpLvlName').innerText;

	return  param;
}

function onSelectTableRow(rowElement,isSelected)
{

	var hiddenElement = rowElement.cells[0].innerText;
	hiddenElement = new String(hiddenElement).trim().valueOf();

	document.forms[0].selectedMetaGroupId.value = hiddenElement;
	var cell3 = rowElement.cells[3];
	var hiddenEl3 = cell3.getElementsByTagName("input")[0];
	var menuEntityCurrGrpAccess = document.forms[0].menuAccessId.value;
	if(menuEntityCurrGrpAccess == 0 && entityAccess == 0 && isSelected)
	{

		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
			/* Whenever Groups value is zero, the 'Groups' button should be disabled otherwise it should be enabled*/
			if(hiddenEl3.value > 0){
			document.getElementById("groupbutton").innerHTML = document.getElementById("groupenablebutton").innerHTML;
		} else
		{
			document.getElementById("groupbutton").innerHTML = document.getElementById("groupdisablebutton").innerHTML;
			}
			}

	else{
	    document.getElementById("groupbutton").innerHTML = document.getElementById("groupdisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

	}


}

--></SCRIPT>
</head>

<form action="metaGroup.do" method="post" >
<input name="method" type="hidden" value="display">
<input name="selectedMetaGroupId" type="hidden" value="">
<input name="selectedmgroupName" type="hidden" value="test">

<input name="menuAccessId" type="hidden" >

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">
<div id="MetagroupMaintenance" style="position:absolute; left:20px; top:20px; width:646px; height:74px; border:2px outset;" color="#7E97AF">
	<div id="BookCode" style="position:absolute; left:8px; top:4px; width:646px; height:74px;">
<!--start:Mantis 1549 modified by sunil.The purpose to display meaningful tooltip -->
		<table width="504px" border="0" cellpadding="0" cellspacing="0" height="50px">
			<tr>
			  <td width="30px"><b><fmt:message key="bookCode.entity"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="140px">
			  <select name="metaGroup.id.entityId" onchange="submitForm('displayListByEntity')" style="width:140px" titleKey="tooltip.selectEntityId" tabindex="1">
       		    <c:forEach var="item" items="${requestScope.entities}">
       				<option value="${item.value}" <c:if test="${metaGroup.id.entityId == item.value}">selected</c:if>>${item.label}</option>
    			</c:forEach>
    		 </select>

			  </td>
			   <td width="20">&nbsp;</td>
			  <td width="280px">
				<span id="entityDesc" name="entityDesc" class="spantext">
			   </td>
			 </tr>
			 <tr>
			  <td width="30px"><b><fmt:message key="metaGroup.mgrpLvlCode"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="140px">
			  <select  name="metaGroup.mgrpLvlCode" onchange="submitForm('displayListByEntity')" style="width:140px" titleKey="tooltip.selectMetaGroupLevel" tabindex="1">
				 <c:forEach var="item" items="${requestScope.metaGrpLvlList}">
       				<option value="${item.value}" <c:if test="${metaGroup.mgrpLvlCode == item.value}">selected</c:if>>${item.label}</option>
    			</c:forEach>
			  </select>
			  </td>
			   <td width="20">&nbsp;</td>
			  <td width="280px">
				<span id="mgrpLvlName" name="mgrpLvlName" class="spantext">
			   </td>
			 </tr>
		</table>
	</div>
</div>



<div id="MetagroupMaintenance" color="#7E97AF" style="position:absolute; border:2px outset; left:20px; top:95px; width:646px; height:429;">

	<div id="MetagroupMaintenance" style="position:absolute;z-index:50;left:0px; top:0px; width:628px; height:10px;">
		<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="623" border="0" cellspacing="1" cellpadding="0"  height="20">
			<thead>
				<tr>
				<td title='<fmt:message key="tooltip.sortMGId"/>' width="120px"><b><fmt:message key="metaGroup.id.mgroupId"/></b></td>
					<td title='<fmt:message key="tooltip.sortMGName"/>' width="280px"><b><fmt:message key="metaGroup.mgroupName"/></b></td>
					<td title='<fmt:message key="tooltip.sortFinTrade"/>' width="113px"><b><fmt:message key="metaGroup.financeTrade"/></b></td>
					<td title='<fmt:message key="tooltip.sortNoGroups"/>' width="107px"><b><fmt:message key="metaGroup.noofGroups"/></b></td>
				</tr>
			</thead>
		</table>
	</div>
	<!--End:Mantis 1549 modified by sunil.The purpose to display meaningful tooltip -->
	<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:642px; height:425px; overflow-x:hidden">
		<div id="MetaGroup" style="position:absolute;z-index:99; left:0px; top:20px; width:625px; height:10px;">
			<table class="sort-table" id="metaGroupListDetails" width="624" border="0" cellspacing="1" cellpadding="0" height="405">
				<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<tbody>
    <c:set var="count" value="0"/>
    <c:forEach items="${requestScope.metaGroupListDetails}" var="metaGroup">
        <tr class="${count % 2 == 0 ? 'even' : 'odd'}">
            <input type="hidden" name="mgroupId" value="${metaGroup.id.mgroupId}" disabled/>
            <td width="120px" align="left">${metaGroup.id.mgroupId}&nbsp;</td>
            <td width="280px" align="left">${metaGroup.mgroupName}&nbsp;</td>
            <td width="113px" align="middle">${metaGroup.displayedFinanceTrade}&nbsp;</td>
            <td width="108px" align="middle">
                <input type="hidden" name="noofGroups" value="${metaGroup.noofGroups}" disabled/>
                ${metaGroup.noofGroups}&nbsp;
            </td>
        </tr>
        <c:set var="count" value="${count + 1}"/>
    </c:forEach>
</tbody>
				<tfoot><tr><td colspan="4" ></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>

<div id="MetaGroup" style="position:absolute; left:590; top:538px; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			   <a tabindex="7" href=# onclick="javascript:openWindow(buildPrintURL('print','Metagroup Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
		    </td>

			<td align="right" id="Print">
				<a tabindex="7" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>


<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:528px; width:646px; height:39px; visibility:visible;">

<div id="MetagroupMaintenance" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
  <table width="350" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="addbutton" width="70"></td>
		<td id="changebutton" width="70"></td>
		<td id="deletebutton" width="70"></td>
		<td id="groupbutton" width="70"></td>

		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>' >
			<a tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C')" onKeydown ="onKeyEnterSubmit('close',event);"><fmt:message key="button.close"/></a>
		</td>

	</tr>
	</table>
</div>

<div style="position:absolute; left:6; top:4; width:646px; height:15px; visibility:hidden;">
<table  border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
<tr>

    <td id="addenablebutton">
		<a title='<fmt:message key="tooltip.addNewMetaGroup"/>' tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMetaGroup('add'),'metaGroupmaintenanceaddWindow','left=50,top=190,width=647,height=225,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('add',event);"><fmt:message key="button.add"/></a>
	</td>
	<td id="adddisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.addNewMetaGroup"/>'><fmt:message key="button.add"/></a>
	</td>
	<td id="changeenablebutton">
		<a title='<fmt:message key="tooltip.changeSelMG"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddMetaGroup('change'),'metagroupmaintenancechangeWindow','left=50,top=190,width=647,height=225,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('change',event);"><fmt:message key="button.change"/></a>
	</td>
	<td id="changedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.changeSelMG"/>'><fmt:message key="button.change"/></a>
	</td>
	<td id="deleteenablebutton">
		<a title='<fmt:message key="tooltip.deleteSelMetaGroup"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete');" onKeydown = "onKeyEnterSubmit('delete',event);"><fmt:message key="button.delete"/></a>
	</td>
	<td id="deletedisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.deleteSelMetaGroup"/>'><fmt:message key="button.delete"/></a>
	</td>
	<td id="groupenablebutton">
		<a title='<fmt:message key="tooltip.viewGroup"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:openWindow(buildAddMetaGroup('group'),'metaGroupmaintenancegroupWindow','left=50,top=190,width=584,height=514,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('group',event);"><fmt:message key="button.groups"/></a>
	</td>
	<td id="groupdisablebutton">
		<a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.viewGroup"/>'><fmt:message key="button.groups"/></a>
	</td>

</tr>
</table>
</div>


</div>
<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>   	