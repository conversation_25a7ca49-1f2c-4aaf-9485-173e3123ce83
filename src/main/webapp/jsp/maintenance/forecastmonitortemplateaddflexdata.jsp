<?xml version="1.0" encoding="utf-8"?>
<!--
  - The main purpose of this jsp file is to load the resultant xml data for Add / change Forecast Monitor Template screen.
  - 
  - Author(s): Vivekanandan A
  - Date: 24-05-2011
  -->
<%@ page contentType="text/xml"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>



<forecastmonitor
	selectedTemplateId="${requestScope.forecastMonitorTemplate.id.templateId}"
selectedTemplateName ="${requestScope.forecastMonitorTemplate.templateName}">

<request_reply>
	<status_ok>${requestScope.reply_status_ok}</status_ok>
	<message>${requestScope.reply_message}</message>
<location /> 
</request_reply>


 <grid> <metadata>
<columns> 
<!-- Start : Modified by <PERSON><PERSON><PERSON><PERSON><PERSON> for issues reported in 1053 Beta 2 on 15-09-2011 to disable data grid filter for all columns -->
<column heading="<fmt:message key="label.forecasttemplate.column.columnno"/>" clickable="false"
	draggable="false" filterable="false" holiday="false" type="num"
	dataelement="rowid" width="100" /> 

<column heading="<fmt:message key="label.forecasttemplate.column.displayname"/>"
	clickable="false" draggable="false" filterable="false" holiday="false"
	type="str" dataelement="displayname" width="170" /> 
	
<column
	heading="<fmt:message key="label.forecasttemplate.column.description"/>" clickable="false" draggable="false"
	filterable="false" holiday="false" type="str" dataelement="description"
	width="220" /> 
	
<column heading="<fmt:message key="label.forecasttemplate.column.type"/>" clickable="false"
	draggable="false" filterable="false" holiday="false" type="str"
	dataelement="type" width="70" /> </columns> </metadata> <rows size="${recordCount}">
<!-- End :Modified by Vivekanandan for issues reported in 1053 Beta 2 on 15-09-2011 to disable data grid filter for all columns -->
<%int i = 1;%>
	 <c:forEach var="forecastMonitorTemplateCol" items="${requestScope.forecastMonitorTemplate.forecastTemplateColList}" varStatus="status">
		 <row>
			 <rowid negative="false" color="${forecastMonitorTemplateCol.rowColor}">${status.index + 1}</rowid>
			 <columnno negative="false" color="${forecastMonitorTemplateCol.rowColor}">${forecastMonitorTemplateCol.id.columnId}</columnno>
			 <ordinalpos negative="false" color="${forecastMonitorTemplateCol.rowColor}">${forecastMonitorTemplateCol.ordinalPos}</ordinalpos>
			 <displayname negative="false" color="${forecastMonitorTemplateCol.rowColor}">${forecastMonitorTemplateCol.columnDisplayName}</displayname>
			 <description negative="false" color="${forecastMonitorTemplateCol.rowColor}">${forecastMonitorTemplateCol.columnDescription}</description>
			 <type negative="false" color="${forecastMonitorTemplateCol.rowColor}">${forecastMonitorTemplateCol.columnType}</type>
			 <modify negative="false" clickable="true" color="${forecastMonitorTemplateCol.rowColor}">true</modify>
		 </row>
	 </c:forEach></rows> </grid>
 <selects> 

	 <select id="users">
		 <c:forEach var="templateUser" items="${requestScope.templateUsersList}">
			 <option value="${templateUser.userName}"
					 selected="${templateUser.id.userId == '*DEFAULT*' ? 'selected' : ''}">
					 ${templateUser.id.userId}
			 </option>
		 </c:forEach>
	 </select>


</selects> </forecastmonitor>
