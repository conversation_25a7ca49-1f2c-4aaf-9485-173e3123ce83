<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.CommonDataManager" %>
<html>
<head>
<!--Betcy:12/11/08:Added(start):To display title content correctly-->
<c:if test="${requestScope.screenStatus == 'add'}">
    <title><fmt:message key="title.addContacts"/></title>
</c:if>
<c:if test="${requestScope.screenStatus == 'change'}">
    <title><fmt:message key="title.changeContacts"/></title>
</c:if>
<c:if test="${requestScope.screenStatus == 'view'}">
    <title><fmt:message key="title.viewContacts"/></title>
</c:if>
<!--Betcy:12/11/08:Added(end)To display title content correctly-->
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT" >
<!--Betcy::15/01/2009:Added for Mantis 774 to close the screen(start)-->
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray=["*"];
<!--Betcy::15/01/2009:Added for Mantis 774 to close the screen(end)-->

<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	window.opener.document.forms[0].contactName.value = '${requestScope.accContactName}';
	window.opener.document.forms[0].contactPhoneNumber.value ='${requestScope.accPhoneNumber}';
	window.opener.document.forms[0].contactEmailAddr.value='${requestScope.accEmailAddr}';
	self.close();
</c:if>


function submitForm(methodName){
	var addr = document.forms[0].elements["acctMaintenance.acctEmailAddr"].value;
	/*Betcy:12/01/2009:Added for Mantis 774 to check the validation in "Save" button click(start)*/
	var contactName=validateField(document.forms[0].elements['acctMaintenance.acctContactName'],'acctMaintenance.acctContactName','alphaNumPatWithSpace');

	if(contactName)
	{
	if (Blank_TextField_Validator() )
	{
		if(IsNumeric(document.forms[0].elements["acctMaintenance.acctPhone"].value) ){


			if( validateEmail(addr)) {

			document.forms[0].method.value = methodName;
			document.forms[0].submit();
			}
		}

	}
	/*Betcy:12/01/2009:Added for Mantis 774 to check the validation in "Save" button click(end)*/
	}
	/*Betcy:12/01/2009:Added for Mantis 774 to check the validation in "Save" button click(start)*/
	else
	{
	   document.forms[0].elements['acctMaintenance.acctContactName'].focus();
	}
	/*Betcy:12/01/2009:Added for Mantis 774 to check the validation in "Save" button click(end)*/

		}

function validateEmail(addr) {
	//alert ("email address is "+addr);
	if(addr.length>0)
	{

var invalidChars = '\/\'\\ ";:?!()[]\{\}^|';
for (i=0; i<invalidChars.length; i++) {
   if (addr.indexOf(invalidChars.charAt(i),0) > -1) {
	   ShowErrMsgWindowWithBtn('', '<fmt:message key="contactDetails.alert.emailaddinvalidchar"/>', null);
		//document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
      return false;
   }
}
for (i=0; i<addr.length; i++) {
   if (addr.charCodeAt(i)>127) {
	   ShowErrMsgWindowWithBtn('', '<fmt:message key="contactDetails.alert.emailaddcontainsnonasciichar"/>', null);
//document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
      return false;
   }
}

var atPos = addr.indexOf('@',0);
if (atPos == -1) {
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.emailaddcontain@"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (atPos == 0) {
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.notstrtemailaddcontain@"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (addr.indexOf('@', atPos + 1) > - 1) {
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.cntainonceemailaddcontain@"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (addr.indexOf('.', atPos) == -1) {
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.emailaddcontainperiod"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (addr.indexOf('@.',0) != -1) {
	ShowErrMsgWindowWithBtn('', '<fmt:message key="contactDetails.alert.emailaddntfollow@"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (addr.indexOf('.@',0) != -1){
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.emailaddntprecede@"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
if (addr.indexOf('..',0) != -1) {
	ShowErrMsgWindowWithBtn('', '<fmt:message key="contactDetails.alert.twoperiodntadjacentemailadd"/>', null);
document.forms[0].elements["acctMaintenance.acctEmailAddr"].focus();
   return false;
}
var suffix = addr.substring(addr.lastIndexOf('.')+1);
if (suffix.length != 2 && suffix != 'com' && suffix != 'net' && suffix != 'org' && suffix != 'edu' && suffix != 'int' && suffix != 'mil' && suffix != 'gov' & suffix != 'arpa' && suffix != 'biz' && suffix != 'aero' && suffix != 'name' && suffix != 'coop' && suffix != 'info' && suffix != 'pro' && suffix != 'museum') {
	ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.invaildprimrydomain"/>', null);
  // if (db) alert('invalid primary domain in email address');

   return false;
}
	}
return true;
}

function Blank_TextField_Validator()
{

	//alert("inside blank_textField_validator");
var text_name =document.forms[0].elements["acctMaintenance.acctContactName"].value;
//alert ("text name is"+text_name);
// Check the value of the element named text_name
// from the form named text_form
if (text_name == "")
{
// If null display and alert box
ShowErrMsgWindowWithBtn('','<fmt:message key="contactDetails.alert.fillnamefield"/>', null, focusName);

// Place the cursor on the field for revision

// return false to stop further processing
return (false);
}

// If text_name is not null continue processing
//alert("name is present");
return (true);
document.forms[0].elements["acctMaintenance.acctContactName"].focus();
}
function focusName() {

}


//phone number validation starts


function IsNumeric(strString)
   //  check for valid numeric strings
   {
	  // alert("inside phone validation");
   var strValidChars = "0123456789-";
   var strChar;
   var blnResult = true;

   if (strString.length == 0) return true;

   //  test strString consists of valid characters listed above
   for (i = 0; i < strString.length && blnResult == true; i++)
      {
      strChar = strString.charAt(i);
      if (strValidChars.indexOf(strChar) == -1)
         {
         blnResult = false;
         ShowErrMsgWindowWithBtn('', '<fmt:message key="contactDetails.alert.phonenocontain0123456789"/>', null);
		 //document.forms[0].elements['acctMaintenance.acctPhone'].focus();
         }
      }
   return blnResult;
   }

// only allow 0-9 be entered





function bodyOnLoad() {
}


</SCRIPT>
</head>
<%-- START: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()">
<%-- END: Code fixed to maintain the Internet explorer memory usage, 21 May 2007 --%>
<form action="acctMaintenance.do" onsubmit="validate(this);">
<input name="method" type="hidden" value="saveContactDetails">
  <div id="CurrencyChange" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:385px; height:103px;">
 <div style="position:absolute; left:8px; top:4px; width:370px; height:80px;">
   <div style="height:92px;">
      	 <fieldset style="width:370px;border:2px groove ; top:8px;">
				<legend><fmt:message key="contact.fieldet"/></legend>
				<table width="365" border="0" cellpadding="0" cellspacing="0" height="75">
		   			<tr height="25px">
						<td width="45px">&nbsp;<b><fmt:message key="section.sectionName"/>*</b></td>
						<td width="28px">&nbsp;</td>
						<td width="280px">
						<!-- Betcy 11/08/2008 -Added Validation to allow spaces.But not allow special characters.  -->
						<input type="text" name="acctMaintenance.acctContactName"  titleKey="tooltip.contactName" class="htmlTextAlpha"
							   value="${acctMaintenance.acctContactName}" 	<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>  tabindex="1" maxlength="30"  style="width:280px" onchange="return validateField(this,'book.bookCodeadd','alphaNumPatWithSpace');"/>

						</td>
					</tr>
					<tr height="25px">
						<td width="45px">&nbsp;<b><fmt:message key="contact.phone"/></b></td>
						<td width="28px">&nbsp;</td>
						<td width="280px">

						<!--
						<html:text property="acctMaintenance.acctPhone"  titleKey="tooltip.enterPhNo" styleClass="htmlTextAlpha" tabindex="2" maxlength="20"  style="width:280px;"  disabled="${requestScope.screenFieldsStatus}" />
						-->
						<!-- Betcy 11/08/2008 -Added Validation to allow NUMERIC VALUE  -->
						<input type="text" name="acctMaintenance.acctPhone"  titleKey="tooltip.enterPhNo" class="htmlTextAlpha"
						value="${acctMaintenance.acctPhone}" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> tabindex="2" maxlength="20"  style="width:280px;" />

						</td>
					</tr>
					<tr height="25px">
						<td width="45px">&nbsp;<b><fmt:message key="contact.email"/></b></td>
						<td width="28px">&nbsp;</td>

						<td width="280px"><input type="text" name="acctMaintenance.acctEmailAddr"  titleKey="tooltip.emailId" class="htmlTextAlpha"
						value="${acctMaintenance.acctEmailAddr}" <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> tabindex="3" maxlength="50"  style="width:280px;"/>
						</td>
					</tr>
				</table>
	</fieldset>
   </div>
</div>
 <div id="EntityMaintenance" style="position:absolute; left:310; top:116; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
		<!--Betcy:12/11/08:Modified(start):To display the help content correctly-->
			<c:if test="${'add'== requestScope.screenStatus}">
			    <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Contact'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key= "tooltip.helpScreen"/>'></a>
			</c:if>
			<c:if test="${'change'== requestScope.screenStatus}">
			    <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Contact '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key= "tooltip.helpScreen"/>'></a>
			</c:if>

			<c:if test="${'view'== requestScope.screenStatus}">
			    <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','View Contact '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key= "tooltip.helpScreen"/>'></a>
			</c:if>
	        </td>
	<!--Betcy:12/11/08:Modified(end):To display the help content correctly-->
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key= "tooltip.printScreen"/>'></a>
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:0; top:105px; width:385px; height:39px; visibility:visible;">
<c:if test="${'view'== requestScope.screenStatus}">
<div id="AccountMaintenanceContact" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>

		<td id="closebutton" width="70" title='<fmt:message key="tooltip.close"/>' >
		<a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();"><fmt:message key="button.close"/></a>
		</td>
	</tr>
	</table>
</div>
</c:if>
<c:if test="${'view'!= requestScope.screenStatus}">
<div id="AccountMaintenanceContact" style="position:absolute; left:6; top:4; width:360px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="0" cellpadding="0" height="20">
	<tr>
		<td id="okbutton" width="70">
		<a  tabindex="4" title='<fmt:message key="tooltip.ok"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:submitForm('saveContactDetails');" ><fmt:message key="button.ok"/></a></td>
		<td id="cancelbutton" width="70">
		<a  tabindex="5" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="window.close();" ><fmt:message key="button.cancel"/></a>
		</td>
				
	</tr>
	</table>
</div>
</c:if>
</div>
</form>
</body>


</html>