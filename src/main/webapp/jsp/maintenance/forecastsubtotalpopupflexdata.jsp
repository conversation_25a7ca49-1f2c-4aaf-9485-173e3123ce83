<?xml version="1.0" encoding="utf-8"?>
<!--
- The main purpose of this jsp file is to load the resultant xml data for Forecast Monitor Template Add Normal pop up screen.
-
- Author(s): Vivekanandan A
- Date: 24-05-2011
-->

<%@ page contentType="text/xml" %>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>



<forecastmonitor>
	<request_reply>
		<status_ok>${requestScope.reply_status_ok}</status_ok>
		<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>
	<grid>
		<metadata>
			<columns>
				<column heading="<fmt:message key='label.forecasttemplate.column.id'/>" clickable="false" draggable="false" filterable="true" type="str" dataelement="id" width="156"/>
				<column heading="<fmt:message key='label.forecasttemplate.column.name'/>" clickable="false" draggable="false" filterable="true" type="str" dataelement="name" width="156"/>
			</columns>
		</metadata>
		<rows size="13">
			<c:forEach items="${requestScope.listMonitorTemplateDetailsCol}" var="listMonitorTemplateDetailsCol">
				<row>
					<id negative="false" clickable="false">${listMonitorTemplateDetailsCol.columnDisplayName}</id>
					<name negative="false" clickable="false">${listMonitorTemplateDetailsCol.columnDescription}</name>
					<ordinalpos negative="false" clickable="false">${listMonitorTemplateDetailsCol.ordinalPos}</ordinalpos>
					<columnno negative="false" clickable="false">${listMonitorTemplateDetailsCol.id.columnId}</columnno>
					<displayname negative="false" clickable="false">${listMonitorTemplateDetailsCol.columnDisplayName}</displayname>
					<description negative="false" clickable="false">${listMonitorTemplateDetailsCol.columnDescription}</description>
					<multiplier negative="false" clickable="false">1</multiplier>
				</row>
			</c:forEach>
		</rows>
	</grid>
</forecastmonitor>