<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
	<fmt:message key="currencyalias.addScreen"/>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

</head>
<SCRIPT language="JAVASCRIPT">
mandatoryFieldsArray=["*"];
var cancelcloseElements = new Array(1);
	 cancelcloseElements[0] = "cancelbutton";

<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="unspecified";
window.opener.document.forms[0].submit();
self.close();
</c:if>

function bodyOnLoad(){	
	document.getElementById("entityName").innerText = '${requestScope.entityName}';
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyAlias.currencyCode"],document.getElementById("currencyName"));

}

function enableFields(){
	document.forms[0].elements["currencyAlias.id.entityId"].disabled = "";
	
}

function validateForm(objForm){
   var elementsRef = new Array(3);
  elementsRef[0] = objForm.elements["currencyAlias.id.entityId"];
  elementsRef[1] = objForm.elements["currencyAlias.id.alias"];
  elementsRef[2] = objForm.elements["currencyAlias.currencyCode"];
  return validate(elementsRef);
}

function submitForm(methodName){
/*Betcy:13/01/2009:Added to check the validation in save button for Mantis 774(start)*/
	var aliasName=validateField(document.forms[0].elements['currencyAlias.id.alias'],'currencyAlias.id.alias','alphaNumPat');
	if(aliasName)
	{
	/*Betcy:13/01/2009:Added to check the validation in save button for Mantis 774(end)*/
	
	if(validateForm(document.forms[0]) && validateCurrencyAlias()){	
		 enableFields();
    	document.forms[0].method.value = methodName;
		document.forms[0].entityText.value = '${requestScope.entityName}';

		document.forms[0].aliasId.value = document.forms[0].elements["currencyAlias.id.alias"].value;

		//alert(document.forms[0].elements["currencyAlias.id.entityId"].value +" : "+document.forms[0].elements["currencyAlias.id.alias"].value);
	 document.forms[0].submit();
	 /*Betcy:13/01/2009:Added to check the validation in save button for Mantis 774(start)*/
	}
}
else
	{
	  document.forms[0].elements['currencyAlias.id.alias'].focus();
	}
	/*Betcy:13/01/2009:Added to check the validation in save button for Mantis 774(end)*/
}


String.prototype.equalsIgnoreCase=MatchIgnoreCase;

function MatchIgnoreCase(strTerm){
var strToSearch = this.toLowerCase();
strTerm = strTerm.toLowerCase();
if(strToSearch==strTerm){
return true;
}else
{
return false;
}
}



function validateCurrencyAlias()
{
/*start:08/12/2011: mantis 1536 modify by the sunil removing alert symbol(start)*/
	var currencyAlias = document.forms[0].elements["currencyAlias.id.alias"].value;	
	
	var str1 = (new String("all")).valueOf();
	var comp = currencyAlias.equalsIgnoreCase(str1);	

	if(comp == true){			
			alert("<fmt:message key="currencyAliasChild.alert.IdasAll"/>");
			if(window.event)
				window.event.returnValue = false;
			document.forms[0].elements["currencyAlias.id.alias"].focus();			
			return false;
			
		}else {
			return true;
		}
		/*end:08/12/2011: mantis 1536 modify by the sunil removing alert symbol(start)*/
}

</SCRIPT>
<form action="currencyalias.do" onsubmit="return validate(this);">
<input name="method" type="hidden" value="">
<input name="entityText" type="hidden" value="">
<input name="aliasId" type="hidden" value="">
<input name="hostId" type="hidden" value="">


<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);" onunload="call()" >
  <div id="CurrencyAliasChild" color="#7E97AF" style="position:absolute; left:20px; border:2px outset; top:20px; width:520px; height:103px;">

 <div style="position:absolute; left:8px; top:4px; width:505px; height:80px;">  
	<table width="398" border="0" cellpadding="0" cellspacing="0" height="50">
		<tr height="23">
			<td  width="50px"><b><fmt:message key="entity.id"/></b></td>
			<td width="33px">&nbsp;</td>
			<td  width="140px" >				
				<input type="text"  name="currencyAlias.id.entityId" tabindex="1"  class="htmlTextAlpha"  titleKey="tooltip.entityId" maxlength="12"
					   value="${currencyAlias.id.entityId}"   style="width:120px;" disabled = "true" />
			</td>			
			<td width="0px">&nbsp;</td>
			<td width="0">				
				<span id="entityName"   name="#request.entityName" class="spantext"> 
			</td>				
		</tr>
		<tr height="23">
				<td width="50px"><b><fmt:message key="currencyalias.alias"/></b>*</td>
				<td width="33px">&nbsp;</td>
				<td width="140px">
				<input type="text"  name="currencyAlias.id.alias"  tabindex="3" titleKey="tooltip.AliasName" class="htmlTextAlpha" maxlength="30" style="width:120px;"
					   value="${currencyAlias.id.alias}"     onchange="validateField(this,'Number 2','alphaNumPat');"  />
				</td>						
			<td width="0px">&nbsp;</td>
			<td width="0">				
			</td>	
				</tr>					
		</table>  
	
	<table width="308" border="0" cellpadding="0" cellspacing="0" height="25">
		<tr height="23">
			<td width="50px" ><B><fmt:message key="currency.currencyCode"/></B>*</td>	
				<td width="28px">&nbsp;</td>
				<td width="100px">
				<div style="width:55px">
			<select id="ccyAliasId" name="currencyAlias.currencyCode"
			tabindex="1" titleKey="tooltip.AliasIdentifier" class="htmlTextAlpha"
			style="width: 55px">
			 <c:forEach items="${requestScope.currencyMaster}" var="ccy">
               <option
                  value="${ccy.value}"
                  <c:if test="${currencyAlias.currencyCode == ccy.value}">selected="selected"</c:if>>
                  ${ccy.label}
               </option>
			</c:forEach>
			</select>

				</div>
				</td>
			    <td width="20px">&nbsp;</td>
			    <td width="280px">
					<span id="currencyName"  name="#request.currencyName" class="spantext">
				</td>

							
		</table>  
</div>
</div>

 <div id="CurrencyAliasChild" style="position:absolute; left:465px; top:136; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
				 <a tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Alias Add'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
			 </td>
	
			<td align="right" id="Print">
				<a tabindex="6" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
 <div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:128px; width:520px; height:39px; visibility:visible;">
 <div id="CurrencyAliasChild" style="position:absolute; left:6; top:4; width:520; height:15px; visibility:visible;">
	<table width="140" border="0" cellspacing="0" cellpadding="0" >    
		<tr>                    
		 <td id="savebutton">		
			<a  tabindex="4" title='<fmt:message key = "tooltip.save"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onclick="javascript:submitForm('${methodName}')" onMouseUp="highlightbutton(this)" ><fmt:message key="button.save"/></a>
		  </td>	
			 <td  width="70px" id="cancelbutton">		
               <a  tabindex="5" title='<fmt:message key = "tooltip.cancel"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');" ><fmt:message key="button.cancel"/></a>
		  </td>	
        </tr>
    </table>
</div>
</body>
</form>
</html>