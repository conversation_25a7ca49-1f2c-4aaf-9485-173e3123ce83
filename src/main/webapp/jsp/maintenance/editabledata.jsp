<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:choose>
    <c:when test="${requestScope.methodName eq 'add'}">
        <fmt:message key="defineEditableFields.addScreen"/>
    </c:when>

    <c:when test="${requestScope.methodName eq 'change'}">
        <fmt:message key="defineEditableFields.changeScreen"/>
    </c:when>

    <c:when test="${requestScope.methodName eq 'list'}">
        <fmt:message key="defineEditableFields.viewScreen"/>
    </c:when>
</c:choose>

 </title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
<c:if test="${requestScope.parentFormRefresh eq 'yes'}">
	self.close();
</c:if>
/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
function submitForm(methodName){

	document.forms[0].method.value = methodName;
	document.forms[0].selectedEntityId.value = '${selectedEntityId}';
    document.forms[0].submit();
}

function bodyOnLoad(){
	<c:choose>
		<c:when test="${requestScope.methodName eq 'list'}">
			document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		</c:when>
		<c:otherwise>
			document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
		</c:otherwise>
	</c:choose>
}

</SCRIPT>
</head>

<form action="entity.do" onsubmit="return validate(this);" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedEntityId" type="hidden">

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()">

	
	<div id="Entity"
		style="position: absolute; left: 20px; top: 8px; width: 520px; height: 400px; border-left: 2px outset;"
		color="#7E97AF">
	<div id="Entity" style="position:absolute;z-index:99;left:0px; top:0px; width:500px; height:10px;">
		<table  class="sort-table"  width="500px" border="0" cellspacing="1" cellpadding="0"  height="20px">
			<thead>
			<!-- start:modified the mantis 1549 by sunil on 10-02-2012 .The purpose to display the meaningful tooltip -->
				<tr height="20px" >
					<td width="135px" style="border-left-width: 0px;" title='<fmt:message key="tooltip.dataField"/>' class="topbar"><b><fmt:message key="entity.field"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.noChange"/>' class="topbar"><b><fmt:message key="entity.nochange"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.manualOnly"/>' class="topbar"><b><fmt:message key="entity.manualonly"/></b></td>
					<td width="100px" title='<fmt:message key="tooltip.allMovements"/>' class="topbar"><b><fmt:message key="entity.allmovement"/></b></td>
				</tr>
				<!-- End:modified the mantis 1549 by sunil on 10-02-2012 .The purpose to display the meaningful tooltip -->
			</thead>
		</table>
	</div>

	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 517px; height: 397px; overflowY: scroll">
	<div id="Entity"
		style="position: absolute; z-index: 99; left: 0px; top: 22px; width: 500px; height: 10px;">
	<table id="editableDataList" class="sort-table" width="500px"
		border="0" cellspacing="1" cellpadding="0" height="374px">
	
			
				<tbody> 		
				<% int count = 0; %>  
				<c:forEach var="editableData" items="${requestScope.editableDataList}">
					<% if (count % 2 == 0) { %>
						<tr class="evencheckbox">
					<% } else { %>
						<tr class="oddcheckbox">
					<% } %>
					<% count++; %>
					
					<td width="135px" align="left">${editableData.id.movementField}&nbsp;</td>
					
					<c:choose>
						<c:when test="${requestScope.screenFieldsStatus eq 'true'}">
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML1 eq 'checked'}">checked</c:if> value="0" disabled="true"/>
							</td>
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML2 eq 'checked'}">checked</c:if> value="1" disabled="true"/>
							</td>
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML3 eq 'checked'}">checked</c:if> value="2" disabled="true"/>
							</td>
						</c:when>
			
						<c:otherwise>
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML1 eq 'checked'}">checked</c:if> value="0"/>
							</td>
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML2 eq 'checked'}">checked</c:if> value="1"/>
							</td>
							<td width="100px" align="center">
								<input type="radio" tabIndex="1" name="editableDataList<%=count%>" 
									   <c:if test="${editableData.editableDataHTML3 eq 'checked'}">checked</c:if> value="2"/>
							</td>
						</c:otherwise>
					</c:choose>
			
					</tr>
				</c:forEach>  
			</tbody>

					<tfoot><tr><td colspan="4"></td></tr></tfoot>
			</table>
		</div>
	</div>
</div>


	<div id="Role"
		style="position: absolute; left: 460; top: 420px; width: 70px; height: 15px; visibility: visible;">
	
	
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>

		  <td align="Right">
				<c:choose>
					<c:when test="${requestScope.methodName eq 'add'}">
						<a title="<fmt:message key='tooltip.helpScreen'/>" tabindex="3" href="#" 
						   onclick="javascript:openWindow(buildPrintURL('print','Add Editable Fields '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" 
						   onMouseOut="MM_swapImgRestore()" 
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
						   <img src="images/help_default.GIF " name="Help" border="0">
						</a> 
					</c:when>
					
					<c:when test="${requestScope.methodName eq 'change'}">
						<a title="<fmt:message key='tooltip.helpScreen'/>" tabindex="3" href="#" 
						   onclick="javascript:openWindow(buildPrintURL('print','Change Editable Fields'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" 
						   onMouseOut="MM_swapImgRestore()" 
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
						   <img src="images/help_default.GIF " name="Help" border="0">
						</a> 
					</c:when>
					
					<c:when test="${requestScope.methodName eq 'list'}">
						<a title="<fmt:message key='tooltip.helpScreen'/>" tabindex="3" href="#" 
						   onclick="javascript:openWindow(buildPrintURL('print','Editable Fields '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" 
						   onMouseOut="MM_swapImgRestore()" 
						   onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
						   <img src="images/help_default.GIF " name="Help" border="0">
						</a> 
					</c:when>
				</c:choose>

		  </td>
		  
   


			<td align="right" id="Print">
				<a tabindex="3" onKeyDown="submitEnter(this,event)" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 20; top: 412; width: 520px; height: 39px; visibility: visible;">
	
	<div id="Entity" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
		<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">

				<tr>
				<td width="70px" id="savebutton"></td>		
				<td title='<fmt:message key="tooltip.cancel"/>' width="70px" id="cancelbutton">		
					<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onclick="confirmClose('P');"><fmt:message key="button.cancel"/></a>			
				</td>
				</tr>

		</table>
	</div>
		<div id="Entity" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:hidden;">
		<table width="140px" border="0" cellspacing="0" cellpadding="0" height="20">

				<tr>
				<td width="70px" id="saveenablebutton"  >		
					<a tabindex="1" onMouseOut="collapsebutton(this)" title='<fmt:message key= "tooltip.SaveChanges"/>' onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)" onClick="javascript:submitForm('saveEditableFields');"><fmt:message key="button.save"/></a></td>	
				</td>
				<td id="savedisablebutton">
					<a  class="disabled" disabled="disabled" title='<fmt:message key="tooltip.SaveChanges"/>'><fmt:message key="button.save"/></a>
				</td>	
				</tr>

		</table>
	</div>
</div>

<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
<script type="text/javascript">
</script>
</form>
</body>
</html>