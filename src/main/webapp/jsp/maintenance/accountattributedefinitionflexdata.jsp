<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>




<accountattribute >
	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>

	<grid>
		<metadata>
			<columns>
				<c:forEach items="${requestScope.column_order}" var="order">
					<c:choose>
					<c:when test="${order == 'attributeid'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.attributeid"/>"
						 clickable="false"
						 draggable="true"
						 filterable="true" 
						 sort="true" 
						 type="str" 
						 dataelement="attributeid" 
						 width="${requestScope.column_width.attributeid}"/>
					</c:when>
					<c:when test="${order == 'attributename'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.attributename"/>"
						clickable="false"
						draggable="true" 
						filterable="true"  
						sort="true" 
						type="str" 
						dataelement="attributename" 
						width="${requestScope.column_width.attributename}"/>
					</c:when>
					<c:when test="${order == 'type'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.type"/>"
						clickable="false" 
						draggable="true" 
						filterable="true" 
						sort="true" 
						type="str" 
						dataelement="type" 
						width="${requestScope.column_width.type}"/>
					</c:when>
					<c:when test="${order == 'updateDateAsString'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.updated"/>"
						 clickable="false" 
						 draggable="true" 
						 filterable="true" 
						 type="str" 
						 sort="true" 
						 dataelement="updateDateAsString" 
						 width="${requestScope.column_width.updateDateAsString}"/>
					</c:when>
					<c:when test="${order == 'updateuser'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.user"/>"
						clickable="false" 
						draggable="true" 
						filterable="true" 
						sort="true" 
						type="str" 
						dataelement="updateuser" 
						width="${requestScope.column_width.updateuser}"/>
					</c:when>
					<c:when test="${order == 'systemFlag'}">
						<column heading="<fmt:message key="label.accountattributedefinition.column.systemFlag"/>"
						clickable="false" 
						draggable="true" 
						filterable="true" 
						sort="true" 
						type="str" 
						dataelement="systemFlag" 
						width="${requestScope.column_width.systemFlag}"/>
					</c:when>
					</c:choose>
				</c:forEach>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach items="${requestScope.accountAttributeDetailList}" var="accountAttributeDetailList">
				<row>
					<attributeid clickable="false">${accountAttributeDetailList.id.attributeId}</attributeid>
					<attributename clickable="false">${accountAttributeDetailList.attributeName}</attributename>
					<type clickable="false">
						<c:if test="${accountAttributeDetailList.valueType == 'N'}"><fmt:message key="type.numeric"/></c:if>
						<c:if test="${accountAttributeDetailList.valueType == 'T'}"><fmt:message key="type.text"/></c:if>
						<c:if test="${accountAttributeDetailList.valueType == 'D'}"><fmt:message key="type.date"/></c:if>
					</type>
					<updateDateAsString clickable="false">${accountAttributeDetailList.updateDateAsString}</updateDateAsString>
					<updateuser clickable="false">${accountAttributeDetailList.updateUser}</updateuser>
					<systemFlag clickable="false">${accountAttributeDetailList.systemFlag}</systemFlag>
				</row>
			</c:forEach>
		</rows>						
	</grid>					
</accountattribute>
