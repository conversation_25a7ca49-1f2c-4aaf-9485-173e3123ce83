<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>

<head>
    <title>
        <c:choose>
            <c:when test="${requestScope.method == 'add'}">
                <fmt:message key="addlocationmaintenanace.title.mainWindow"/>
            </c:when>
            <c:when test="${requestScope.method == 'change'}">
                <fmt:message key="changelocationmaintenanace.title.mainWindow"/>
            </c:when>
        </c:choose>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <SCRIPT language="JAVASCRIPT">
        var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];
 <c:if test="${'yes' ==requestScope.parentFormRefresh}">
            window.opener.document.forms[0].method.value="displayList";
            window.opener.document.forms[0].submit();
            self.close();
</c:if>
 
 
/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/
	
	function submitForm(methodName){
	    		
		if(methodName=='saveLocDefinition')
		{
		
		var locationId=validateField(document.forms[0].elements['location.id.locationId'],'location.id.locationId','alphaNumPatWithHyphenOnly');
	    
		
		document.forms[0].method.value = methodName;
    	document.forms[0].selectedEntityId.value = '${selectedEntityId}';
		
		if(locationId)
		{			
		  var locationName=validateField(document.forms[0].elements['location.locationName'],'location.locationName','alphaNumPatWithSpace');
		  if(locationName)
		  {
		  		
		    if(validateForm(document.forms[0])) {
		    document.forms[0].submit();
	   }
	          
	   }
	   
	   else
	   {
	     document.forms[0].elements['location.locationName'].focus();
	   }
	   }
	   else
	   {
	     document.forms[0].elements['location.id.locationId'].focus();
	   }
} 
	   else if(methodName=='updateLocDefinition')
	   {
	    var locationName=validateField(document.forms[0].elements['location.locationName'],'location.locationName','alphaNumPatWithSpace');
		if(locationName)
		{
		    document.forms[0].method.value = methodName;
	    	document.forms[0].selectedEntityId.value = '${selectedEntityId}';
			if(validateForm(document.forms[0])) {
			 document.forms[0].submit();
		   }
	    }
	   }
	  

	}

	function validateForm(objForm){
	  var elementsRef = new Array(2);
	 
	  elementsRef[0] = objForm.elements["location.id.locationId"];
	 /* code Added  by Nageswara Rao on 03_Jan_2012 for mantis 1580: "Spaces should not be saved to end of inputted values " */
	  elementTrim(document.forms[0]);
	  elementsRef[1] = objForm.elements["location.locationName"];
	  
	  return validate(elementsRef);
	}

	function bodyOnLoad() {
        setParentChildsFocus();
        setFocus(document.forms[0]);
        ShowErrMsgWindow('${actionError}');
        document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
        document.getElementById("entityName").innerText = '${entityName}';
        document.forms[0].selectedEntityId.value = '${selectedEntityId}';
        document.forms[0].method.value = '${requestScope.method}';
        <c:if test="${requestScope.method == 'change'}">
            document.getElementById("selectedLocation").value = document.forms[0].elements['location.id.locationId'].value;
        </c:if>
    }

    </SCRIPT>
</head>
<form action="location.do" onsubmit="validate(this);">
     <input name="method" type="hidden" value="">
    <input name="selectedEntityId" type="hidden" value="">
    <body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();" onunload="call()">
        <div id="locationdefinitionAdd" style="position: absolute; left: 20px; top: 20px; width: 525px; height: 105px; border: 2px outset;">
            <div style="position: absolute; left: 8px; top: 4px; width: 510px; height: 120px;">
                <table width="507px" border="0" cellpadding="0" cellspacing="0" height="50">
                    <tr height="24px">
                        <td width="100px" colspan="2"><b><fmt:message key="entity.id"/></b></td>
                        <td width="28px">&nbsp;</td>
                        <td width="125px">
                            <input type="text" name="location.id.entityId" class="htmlTextAlpha" tabindex="1" disabled="true" style="width:125px;"
                                   value="${location.id.entityId}" />
                        </td>
                        <td width="20px">&nbsp;</td>
                        <td width="230px"><span id="entityName" name="entityName" class="spantext"></span></td>
                    </tr>
                    <tr height="24px">
                        <td width="100px" colspan="2"><b><fmt:message key="location.locationId"/></b>*</td>
                        <td width="28px">&nbsp;</td>
                        <td width="251px" colspan="5">
                            <c:if test="${requestScope.method == 'add'}">
                                <input type="text" name="location.id.locationId" titleKey="tooltip.enterLocationId"
                                       class="htmlTextAlpha" tabindex="2" maxlength="12" style="width:124px;"
                                       onchange="validateField(this,'locationId','alphaNumPatWithHyphenOnly');"
                                       value="${location.id.locationId}" />
                            </c:if>

                            <c:if test="${requestScope.method == 'change'}">
                                <input type="text" id="selectedLocation" class="htmlTextAlpha" disabled="true" style="width:124px;" />
                                <input type="text" name="location.id.locationId" class="htmlTextAlpha" readonly="readonly"
                                       style="visibility:hidden;width:0px;" value="${location.id.locationId}" />
                            </c:if>
                        </td>
                    </tr>
                    <tr height="24px">
                        <td width="80px" colspan="2"><b><fmt:message key="location.locationName"/></b>*</td>
                        <td width="28px">&nbsp;</td>
                        <td width="100px" colspan="5">
                            <input type="text" name="location.locationName" titleKey="tooltip.enterLocationName"
                                   class="htmlTextAlpha" tabindex="2" maxlength="30" style="width:250px;"
                                   onchange="validateField(this,'locationName','alphaNumPatWithSpace');"
                                   value="${location.locationName}" />
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div id="locationdefinition" style="position: absolute; left: 467px; top: 139px; width: 40px; height: 39px; visibility: visible;">
            <table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr height="25">
                    <td align="right">
                        <c:choose>
                            <c:when test="${requestScope.method == 'add'}">
                                <a tabindex="8" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Location Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no','true')">
                                    <img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>' />
                                </a>
                            </c:when>
                            <c:when test="${requestScope.method == 'change'}">
                                <a tabindex="8" href="#" onclick="javascript:openWindow(buildPrintURL('print','Change Location Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes,status=yes,scrollbars=no','true')">
                                    <img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>' />
                                </a>
                            </c:when>
                        </c:choose>
                    </td>
                    <td align="right" id="Print">
                        <a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif',1)">
                            <img src="images/Print.gif" name="Print" border="0" title='<fmt:message key="tooltip.printScreen"/>' />
                        </a>
                    </td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF" style="position: absolute; border: 2px outset; left: 20; top: 134px; width: 525px; height: 39px; visibility: visible;">
            <div id="locationdefinition" style="position: absolute; left: 6; top: 4; width: 400px; height: 15px; visibility: visible;">
                <table border="0" cellspacing="0" cellpadding="0" width="145">
                    <tr>
                        <td id="savebutton"></td>
                        <td id="cancelbutton"><a title='<fmt:message key="tooltip.cancel"/>' tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');">
                                <fmt:message key="button.cancel"/></a></td>
                    </tr>
                </table>
            </div>
            <div id="locationdefinition" style="position: absolute; left: 6; top: 4; width: 400px; height: 15px; visibility: hidden;">
                <table width="145" border="0" cellspacing="2" cellpadding="0">
                    <tr>
                     <c:if test="${requestScope.method == 'add'}">
                            <td id="saveenablebutton"><a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveLocDefinition');">
                                    <fmt:message key="button.save"/></a></td>
                        </c:if>
                      <c:if test="${requestScope.method == 'change'}">
                            <td id="saveenablebutton"><a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('updateLocDefinition');">
                                    <fmt:message key="button.save"/></a></td>
                        </c:if>
                        <td id="savedisablebutton"><a class="disabled" disabled="disabled" title='<fmt:message key="tooltip.SaveChanges"/>'>
                                <fmt:message key="button.save"/></a></td>
                    </tr>
                </table>
            </div>
        </div>
        <blockquote>&nbsp;</blockquote>
        <p>&nbsp;</p>
    </body>
</form>

</html>