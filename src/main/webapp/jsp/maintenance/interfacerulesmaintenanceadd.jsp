<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>

<head>
    <title>
        <c:choose>
            <c:when test="${'save' == requestScope.methodName}">
                <fmt:message key="interfacerulesmaintenance.title.addWindow"/>
            </c:when>
            <c:when test="${'modify' == requestScope.methodName}">
                <fmt:message key="interfacerulesmaintenance.title.changeWindow"/>
            </c:when>
        </c:choose>
    </title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <SCRIPT language="JAVASCRIPT">
        mandatoryFieldsArray = ["*"];
var selectedMessageType = '${requestScope.selectedMessageType}';
var selectedPartialRuleId = '${requestScope.selectedPartialRuleId}';
var selectedRuleId = '${requestScope.selectedRuleId}';
var selectedSearchType = '${requestScope.selectedSearchType}';
var totalCount = '${totalCount}';
var maxPage = "${requestScope.maxPage}";
var currPage = '${requestScope.currentPage}';
var currentFilter="${requestScope.selectedFilter}";
var currentSort = "${requestScope.selectedSort}";

<c:if test="${'yes' == requestScope.parentFormRefresh}">
window.opener.document.forms[0].method.value="getList";
window.opener.document.forms[0].selectedFilter.value ="${requestScope.selectedFilter}";
window.opener.document.forms[0].selectedSort.value = "${requestScope.selectedSort}";
window.opener.document.forms[0].currentPage.value = '${requestScope.currentPage}';
window.opener.document.forms[0].goToPageNo.value = '${requestScope.currentPage}';
window.opener.document.forms[0].maxPages.value = '${requestScope.maxPage}';
if(selectedSearchType == 'P')
{
	window.opener.document.forms[0].messageType.value = selectedMessageType;
}
else if(selectedSearchType == 'M')
{
	window.opener.document.forms[0].otherMessageType.value = selectedMessageType;
}
window.opener.document.forms[0].selectedRuleId.value = selectedRuleId;
window.opener.document.forms[0].selectedPartialRuleId.value = selectedPartialRuleId;
window.opener.document.forms[0].method.value="getList";
window.opener.document.forms[0].submit();
self.close();
</c:if>
</SCRIPT>
    <SCRIPT language="JAVASCRIPT">
        function populateDropBoxes(){

	bodyOnLoad();
	
	ShowErrMsgWindow('${actionError}');
	
}
function getRadioButtonValue(button){
	var d = button;
	var len = d.length;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		{
		 return d[i].value;
		}
	}
	if(i == len)
	 return "null";
	}
function bodyOnLoad(){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.forms[0].actualMessageType.value = document.forms[0].elements["interfaceRule.otherMessageType"].value;
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
	}
	else if(searchMessageType == "M")
	{
		document.forms[0].actualMessageType.value = document.forms[0].elements["interfaceRule.id.messageType"].value;
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
	}
}


/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/

function submitForm(methodName) {
/* code Added  by Nageswara Rao on 04_Jan_2012 for mantis 1580:"Spaces should not be saved to end of inputted values " */
elementTrim(document.forms[0]);
	var validText = false;
	var ruleValue = document.forms[0].elements["interfaceRule.ruleValue"].value;
	var ruleId = document.forms[0].elements["interfaceRule.id.ruleId"].value;
	var ruleKey = document.forms[0].elements["interfaceRule.id.ruleKey"].value;
	
	var selectedType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);	
	if(selectedType == "P"){
		if(validateField(document.forms[0].elements["interfaceRule.id.messageType"],'ruleid','alphaNumPatExtended'))		
		if(validateField(document.forms[0].elements["interfaceRule.id.ruleId"],'ruleid','alphaNumPatExtended'))
		if(validateField(document.forms[0].elements["interfaceRule.id.ruleKey"],'ruleid','alphaNumPatExtended'))
		if(validateField(document.forms[0].elements["interfaceRule.ruleValue"],'ruleid','notEmptyPat')){
 		validText = true;
		}
		}else if(selectedType == "M"){
		if(validateField(document.forms[0].elements["interfaceRule.otherMessageType"],'ruleid','alphaNumPatExtended'))
		if(validateField(document.forms[0].elements["interfaceRule.id.ruleId"],'ruleid','alphaNumPatExtended'))
		if(validateField(document.forms[0].elements["interfaceRule.id.ruleKey"],'ruleid','alphaNumPatExtended'))
		if(validateField(document.forms[0].elements["interfaceRule.ruleValue"],'ruleid','notEmptyPat')){
 		validText = true;
		}
		}
	if(validText == false){
	alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	}

	if(ruleValue.length > 500){
 		alert("<fmt:message key="interfacerules.alert.ruleValueLengthValidation"/>");
   		document.forms[0].elements["interfaceRule.ruleValue"].focus();	
   		
	}
	else if (ruleId.length>100) {
		alert("<fmt:message key="interfacerules.alert.ruleIdLengthValidation"/>");
   		document.forms[0].elements["interfaceRule.ruleId"].focus();	
	}
	else if (ruleKey.length>250) {
		alert("<fmt:message key="interfacerules.alert.ruleKeyLengthValidation"/>");
   		document.forms[0].elements["interfaceRule.ruleKey"].focus();	
	}
	
	
	else if(validText){

	document.forms[0].method.value = methodName;
	// Encode the ruleValue to base64
    var encodedRuleValue = getMenuWindow().encode64(ruleValue);
    document.forms[0].elements["interfaceRule.ruleValue"].value = encodedRuleValue;
	document.forms[0].selectedMessageType.value = selectedMessageType;
	document.forms[0].selectedPartialRuleId.value = selectedPartialRuleId;
	document.forms[0].selectedSearchType.value = selectedSearchType;
	document.forms[0].selectedRuleId.value = selectedRuleId;
	document.forms[0].pageNoValue.value = '${requestScope.currentPage}';
	document.forms[0].maxPages.value = '${requestScope.maxPage}';
	document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
	document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
	document.forms[0].currentPage.value = '${requestScope.currentPage}';
	if(validate())
	{
		document.forms[0].submit();
	}
}

}

function validate () {
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	if(searchMessageType == "P")
	{
		var messageType = document.forms[0].elements["interfaceRule.id.messageType"].value.trim();
		if(messageType == null || messageType=="") {
			alert("<fmt:message key="interfacerulesmaintenance.alert.messageType"/>");
			return false;
		}
	}
	
	else if(searchMessageType == "M")
	{
		var otherMessageType = document.forms[0].elements["interfaceRule.otherMessageType"].value.trim();
		if(otherMessageType == null || otherMessageType == "") {
			alert("<fmt:message key="interfacerulesmaintenance.alert.messageType"/>");
			return false;
		}
	}
	
	var ruleId = document.forms[0].elements["interfaceRule.id.ruleId"].value.trim();
	if( ruleId == null || ruleId == "") {
		alert("<fmt:message key="interfacerulesmaintenance.alert.ruleId"/>");
		return false;
	}
	var ruleKey = document.forms[0].elements["interfaceRule.id.ruleKey"].value.trim();
	if (ruleKey == null || ruleKey == "") {
		alert ("<fmt:message key="interfacerulesmaintenance.alert.ruleKey"/>");
		return false;
	}
	return true;
}

function interfaceRuleValidation(strField, strLabel,strPat) {
	var validText = false;
	if(validateField(strField,strLabel,strPat)){
		validText = true;
		}	
	if(validText == false){
	alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	}
}
/**
* This method is used to submit the form in the changeinterface rules 
*screen and click the
* the save button.
*
* @param methodName
*/
function modifyInterfaceRule(methodName) {
/* code Added  by Nageswara Rao on 04_Jan_2012 for mantis 1580:"Spaces should not be saved to end of inputted values " */
elementTrim(document.forms[0]);
	var validText = false;
	/* 	get Rulevalue from form */
	var ruleValue = document.forms[0].elements["interfaceRule.ruleValue"].value;
	if(validateField(document.forms[0].elements["interfaceRule.ruleValue"],'ruleid','notEmptyPat')){
		validText = true;
		}	
	if(validText == false){
	alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	}
	/*check whether ruleValue length is greater than 500 characters and throw alertmessage */
	if(ruleValue.length > 500){
 		alert("<fmt:message key="interfacerules.alert.ruleValueLengthValidation"/>");
   		document.forms[0].elements["interfaceRule.ruleValue"].focus();
	}else if(validText){
		document.forms[0].method.value = methodName;
		// Encode the ruleValue to base64
        var encodedRuleValue = getMenuWindow().encode64(ruleValue);
        document.forms[0].elements["interfaceRule.ruleValue"].value = encodedRuleValue;
		var searchType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
		var messageType = "";
		if(searchType == "P")
		{
			messageType = document.forms[0].elements["interfaceRule.id.messageType"].value;
		}
		else if(searchType == "M")
		{
			messageType = document.forms[0].elements["interfaceRule.otherMessageType"].value;
		}
		document.forms[0].messageType.value = messageType;
		document.forms[0].ruleId.value = document.forms[0].elements["interfaceRule.id.ruleId"].value;
		document.forms[0].ruleKey.value = document.forms[0].elements["interfaceRule.id.ruleKey"].value;
				
		document.forms[0].selectedMessageType.value = selectedMessageType;
		document.forms[0].selectedRuleId.value = selectedRuleId;
		document.forms[0].selectedPartialRuleId.value = selectedPartialRuleId;
		document.forms[0].selectedSearchType.value = selectedSearchType;
		document.forms[0].pageNoValue.value = '${requestScope.currentPage}';
		document.forms[0].maxPages.value = '${requestScope.maxPage}';
		document.forms[0].selectedFilter.value = '${requestScope.selectedFilter}';
		document.forms[0].selectedSort.value = '${requestScope.selectedSort}';
		document.forms[0].currentPage.value = '${requestScope.currentPage}';
		document.forms[0].submit();
}
 
}

function showOtherMessageType(){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['interfaceRule.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
		document.forms[0].elements["interfaceRule.otherMessageType"].value = "";
	}
	else if(searchMessageType == "M")
	{
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
		document.forms[0].elements["interfaceRule.id.messageType"].value = "";
	}
}
	/**
	 Limits text caracters numbers 
	*/
	function limitTextArea (field,maxChars,e) {
		var event = (window.event|| e);
        var key = event.keyCode;
		if  (key==13) { 
			if (window.event){
				window.event.returnValue = false;
			}else {
				event.preventDefault();
			}
		
		}

		if(field.value.match(/\n|\r\n/g))

			field.value = field.value.replace(/\n|\r\n/g, ' ') ;

		if(field.value.length > maxChars) 

			field.value = field.value.substring(0, maxChars);
    
    }
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "modify"){
		modifyInterfaceRule('modify')
		}else if(method == "save"){
		submitForm('save');
		}else if(method == "cancel"){
		javascript:window.close();
		}
	}
}

</SCRIPT>
</head>
<form action="interfacerules.do" method="post">
    <input name="method" type="hidden">
    <input name="oldMethod" type="hidden">
    <input name="selectedAccountId" type="hidden">
    <input name="selectedMessageType" type="hidden">
    <input name="selectedRuleKey" type="hidden">
    <input name="selectedRuleId" type="hidden">
    <input name="selectedPartialRuleId" type="hidden">
    <input name="actualMessageType" type="hidden">
    <input name="pageNoValue" type="hidden">
    <input name="maxPages" type="hidden">
    <input name="selectedFilter" type="hidden">
    <input name="selectedSort" type="hidden">
    <input name="currentPage" type="hidden">
    <input name="ruleId" type="hidden">
    <input name="ruleKey" type="hidden">
    <input name="messageType" type="hidden">
    <input name="selectedSearchType" type="hidden">

    <body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();" onunload="call()" style="overflow: hidden;">
        <div id="interfaceRuleAdd" style="position: absolute; left: 15px; top: 20px; width: 830px; height: 350px; border: 2px outset;" color="#7E97AF">
            <div style="position: absolute; left: 8px; top: 4px; width: 820px; height: 175px;">
                <table width="810px" border="0" cellpadding="0" cellspacing="0" height="175">
                    <tr height="20px">
                       <c:choose>
                            <c:when test="${'save' == requestScope.methodName}">
                                <td width="115px"><b>
                                    <fmt:message key="interfacerulesmaintenance.add.messageType"/></b>*</td>
                            </c:when>
                            <c:otherwise>
                                <td width="115px"><b>
                                    <fmt:message key="interfacerulesmaintenance.add.messageType"/></b></td>
                            </c:otherwise>
                        </c:choose>
                        <td width="685px" colspan="3">
                            <table align="left">
                                <tr>
                                    <td width="12px">&nbsp;</td>
                                    <td width="120px">
                                        <c:choose>
                                            <c:when test="${'save' == requestScope.methodName}">
                                                <input type="radio" id="1" name="interfaceRule.searchMessageType" value="P" style="width:13px;" onclick="showOtherMessageType();" tabindex="1" title="<fmt:message key='interfacerulesmaintenance.predefinedMessageType'/>"
                                                       ${interfaceRule.searchMessageType == 'P' ? 'checked' : ''} />
                                            </c:when>
                                            <c:when test="${'modify' == requestScope.methodName}">
                                                <input type="radio" id="1" name="interfaceRule.searchMessageType" value="P" style="width:13px;" onclick="showOtherMessageType();" tabindex="1" title="<fmt:message key='interfacerulesmaintenance.predefinedMessageType'/>" disabled
                                                       ${interfaceRule.searchMessageType == 'P' ? 'checked' : ''} />
                                            </c:when>
                                        </c:choose>
                                        <label title='<fmt:message key="tooltip.Predefined"/>' for="1">
                                            <fmt:message key="interfacerulesmaintenance.predefinedMessageType"/>
                                        </label>
                                    </td>
                                    <td width="20px">&nbsp;</td>
                                   <td width="120px">
                                    <c:choose>
                                        <c:when test="${'save' == requestScope.methodName}">
                                            <input type="radio" id="2" name="interfaceRule.searchMessageType" value="M" style="width:13px;" onclick="showOtherMessageType();" tabindex="2" title="<fmt:message key='interfacerulesmaintenance.manualMessageType'/>"
                                                   ${interfaceRule.searchMessageType == 'M' ? 'checked' : ''} />
                                        </c:when>
                                        <c:when test="${'modify' == requestScope.methodName}">
                                            <input type="radio" id="2" name="interfaceRule.searchMessageType" value="M" style="width:13px;" onclick="showOtherMessageType();" tabindex="2" title="<fmt:message key='interfacerulesmaintenance.manualMessageType'/>" disabled
                                                   ${interfaceRule.searchMessageType == 'M' ? 'checked' : ''} />
                                        </c:when>
                                    </c:choose>
                                    <label title='<fmt:message key="tooltip.Manual"/>' for="2">
                                        <fmt:message key="interfacerulesmaintenance.manualMessageType"/>
                                    </label>
                                </td>
                                    <td width="400px">&nbsp;</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr height="20px">
                        <td width="115px">&nbsp;</td>
                        <td width="685px" colspan="3">
                            <table align="left">
                                <tr>
                                    <td width="12px">&nbsp;</td>
                                    <td colspan="4">
                                        <div id="predefinedMsgTypeDiv">
                                            <c:choose>
                                                <c:when test="${'save' == requestScope.methodName}">
                                                    <select class="htmlTextAlpha" id="interfaceRule.id.messageType" name="interfaceRule.id.messageType" style="width:140px" title="<fmt:message key='tooltip.selectMessageType'/>" tabindex="3" onchange="showOtherMessageType();">
                                                        <c:forEach items="${requestScope.messagetypes}" var="messageType">
                                                            <option value="${messageType.value}" ${interfaceRule.id.messageType == messageType.value ? 'selected' : ''}>${messageType.label}</option>
                                                        </c:forEach>
                                                    </select>
                                                </c:when>
                                                <c:when test="${'modify' == requestScope.methodName}">
                                                    <select class="htmlTextAlpha" id="interfaceRule.id.messageType" name="interfaceRule.id.messageType" style="width:140px" title="<fmt:message key='tooltip.selectMessageType'/>" tabindex="3" onchange="showOtherMessageType();" disabled>
                                                        <c:forEach items="${requestScope.messagetypes}" var="messageType">
                                                            <option value="${messageType.value}" ${interfaceRule.id.messageType == messageType.value ? 'selected' : ''}>${messageType.label}</option>
                                                        </c:forEach>
                                                    </select>
                                                </c:when>
                                            </c:choose>
                                        </div>
                                        <div id="manualMsgTypeDiv">
                                            <c:choose>
                                                <c:when test="${'save' == requestScope.methodName}">
                                                    <input type="text" name="interfaceRule.otherMessageType" class="htmlTextAlpha" title="<fmt:message key='tooltip.messageType'/>" style="width:330px;" tabindex="3" maxlength="30" onchange="return interfaceRuleValidation(this,'otherMessageType','alphaNumPatExtended');" value="${interfaceRule.otherMessageType}"/>
                                                </c:when>
                                                <c:when test="${'modify' == requestScope.methodName}">
                                                    <input type="text" name="interfaceRule.otherMessageType" class="htmlTextAlpha" title="<fmt:message key='tooltip.messageType'/>" style="width:330px;" tabindex="3" maxlength="30" disabled value="${interfaceRule.otherMessageType}"/>
                                                </c:when>
                                            </c:choose>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr height="20px">
                        <td width="115px" valign="top">
                            <b><fmt:message key="interfacerulesmaintenance.add.ruleID"/></b>
                            <c:if test="${'save' == requestScope.methodName}">*</c:if>
                        </td>
                        <td width="20px">&nbsp;</td>
                        <td width="685px" colspan="2">
                            <c:choose>
                                <c:when test="${'save' == requestScope.methodName}">
                                    <textarea style="align: bottom;width:670px;height:32px;" rows="2" cols="110" name="interfaceRule.id.ruleId" class="htmlTextAlpha" tabindex="5" title="<fmt:message key='tooltip.enterInterfaceRuleId'/>" onkeyup="limitTextArea(this,100,event);" onkeydown="limitTextArea(this,100,event);" onchange="return interfaceRuleValidation(this,'Number 2','alphaNumPatExtended');">${interfaceRule.id.ruleId}</textarea>
                                </c:when>
                                <c:when test="${'modify' == requestScope.methodName}">
                                    <textarea style="align: bottom;width:670px;height:32px;" rows="2" cols="110" name="interfaceRule.id.ruleId" class="htmlTextAlpha" tabindex="5" title="<fmt:message key='tooltip.enterInterfaceRuleId'/>" disabled>${interfaceRule.id.ruleId}</textarea>
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                    <tr height="20px">
                        <td width="115px" valign="top">
                            <b><fmt:message key="interfacerulesmaintenance.add.ruleKey"/></b>
                            <c:if test="${'save' == requestScope.methodName}">*</c:if>
                        </td>
                        <td width="20px">&nbsp;</td>
                        <td width="685px" colspan="2">
                            <c:choose>
                                <c:when test="${'save' == requestScope.methodName}">
                                    <textarea style="align: bottom;width:670px;height:76px;" rows="2" cols="110" name="interfaceRule.id.ruleKey" class="htmlTextAlpha" tabindex="5" title="<fmt:message key='tooltip.enterInterfaceRuleKey'/>" onkeyup="limitTextArea(this,250,event);" onkeydown="limitTextArea(this,250,event);" onchange="return interfaceRuleValidation(this,'Number 2','alphaNumPatExtended');">${interfaceRule.id.ruleKey}</textarea>
                                </c:when>
                                <c:when test="${'modify' == requestScope.methodName}">
                                    <textarea style="align: bottom;width:670px;height:76px;" rows="2" cols="110" name="interfaceRule.id.ruleKey" class="htmlTextAlpha" tabindex="5" title="<fmt:message key='tooltip.enterInterfaceRuleKey'/>" disabled>${interfaceRule.id.ruleKey}</textarea>
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                    <tr height="20px">
                        <td width="115px" valign="top"><b><fmt:message key="interfacerulesmaintenance.add.ruleValue"/></b></td>
                        <td width="20px">&nbsp;</td>
                        <td width="685px" colspan="2">
                            <textarea style="align: bottom;width:670px;height:176px;" rows="5" cols="110" class="htmlTextAlpha" tabindex="7" name="interfaceRule.ruleValue" title="<fmt:message key='tooltip.enterInterfaceRuleValue'/>" onkeyup="limitTextArea(this,500,event);" onkeydown="limitTextArea(this,500,event);" onchange="return interfaceRuleValidation(this,'Number 2','notEmptyPat');">${interfaceRule.ruleValue}</textarea>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <!-- Buttons -->
        <div id="interfaceRuleAdd" style="position: absolute; left: 770 ; top: 380px; width: 40px; height: 39px; visibility: visible;">
            <table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
                <tr height="25">
                    <td align="Right">
                        <c:choose>
                            <c:when test="${'save' == requestScope.methodName}">
                                <a tabindex="9" href="#" onclick="javascript:openWindow(buildPrintURL('print','Add Interface Rule'),'sectionprintdwindow','left=50,top=190,width=552,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
                                    <img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
                                </a>
                            </c:when>
                            <c:when test="${'modify' == requestScope.methodName}">
                                <a tabindex="9" href="#" onclick="javascript:openWindow(buildPrintURL('print','Change Interface Rule'),'sectionprintdwindow','left=50,top=190,width=552,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
                                    <img src="images/help_default.GIF" name="Help" border="0" title='<fmt:message key="tooltip.helpScreen"/>'>
                                </a>
                            </c:when>
                        </c:choose>
                    </td>
                    <td align="right" id="Print"><a tabindex="9" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print" border="0" title='<fmt:message key="tooltip.printScreen"/>'></a></td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" style="position: absolute; border: 2px outset; left: 15; top: 375px; width: 830px; height: 39px; visibility: visible;">
            <div id="interfaceRuleAdd" style="position: absolute; left: 6; top: 4; width: 820px; height: 15px; visibility: visible;">
                <table border="0" cellspacing="0" cellpadding="0" width="145">
                    <tr>
                        <td id="savebutton">
                            <c:choose>
                                <c:when test="${'save' == requestScope.methodName}">
                                    <a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" tabindex="7" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="submitForm('save');" onKeydown="onKeyEnterSubmit('save',event);" title='<fmt:message key="tooltip.save"/>'>
                                        <fmt:message key="button.save"/>
                                    </a>
                                </c:when>
                                <c:when test="${'modify' == requestScope.methodName}">
                                    <a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" tabindex="7" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="modifyInterfaceRule('modify');" onKeydown="onKeyEnterSubmit('modify',event);" title='<fmt:message key="tooltip.save"/>'>
                                        <fmt:message key="button.save"/>
                                    </a>
                                </c:when>
                            </c:choose>
                        </td>
                        <td id="cancelbutton"><a onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="javascript:window.close();" tabindex="8" onKeydown="onKeyEnterSubmit('cancel',event);" title='<fmt:message key="tooltip.cancel"/>'>
                                <fmt:message key="button.cancel"/></a></td>
                    </tr>
                </table>
            </div>
        </div>
    </body>
</form>

</html>