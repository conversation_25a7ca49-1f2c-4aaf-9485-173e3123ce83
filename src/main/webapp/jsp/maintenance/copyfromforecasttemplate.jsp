<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Add a column to / change a column of Forecast Monitor Template (details) screen. 
  -	And to embed the Add Forecast Monitor Template flex screen. 
  - Also, to load the label values for this screen.	
  -
  - Author(s): Vivekanandan A
  - Date: 25-05-2011
  -->
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
	<title>
		<fmt:message key="label.copyforecasttemplate.title.window"/>
	</title>
</head>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		
			

			window.onunload = call;
			
			var templateId = '${requestScope.templateId}';
			var templateName = '${requestScope.templateName}';
			var userId = '${requestScope.userId}';
			var isPublic = '${requestScope.isPublic}';
			var screenName = '${requestScope.screenName}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var	displayName = '${requestScope.displayName}';
			var	description = '${requestScope.description}';
			var	type = '${requestScope.type}';
			var	columnId = '${requestScope.columnId}';
			var ordinalPos = '${requestScope.ordinalPos}';
			var screenRoute = "copyForecastTemplate";
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			
			// Set the label values
			/*var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			
			
			// ok button
			label["text"]["button-copy"] = "<fmt:message key="button.ok"/>";
			label["tip"]["button-copy"] = "<fmt:message key="button.ok"/>";	
			
			// Combo box
			label["text"]["copy-from"] = "<fmt:message key="tooltip.copyFrom"/>";
			label["tip"]["forecastMonitorTemplateCopy.templateId"] = "<fmt:message key="tooltip.forecastMonitorTemplateCopy.templateId"/>";	
			
			// Close button
			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";	*/
			
			
			
			/**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshParent(selectedUserId,templateId){
			 	//window.opener.location.reload(true);
			 	window.opener.reloadOption(selectedUserId,templateId);
				close();
			 }
			 
			 
			 /**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Copy Forecast Monitor Template'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			
		</script>
	
<%@ include file="/angularscripts.jsp"%>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0"  onunload="closeChild()">
		<form id="exportDataForm" target="tmp" method="post">
		
<!-- 		<div id="swf"> -->
<!-- 			<object id='mySwf' classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000' height='100%' width='100%'> -->
<%-- 				<param name='src' value='jsp/maintenance/copyforecasttemplate.swf?version=<%= SwtUtil.appVersion %>' /> --%>
<!-- 	<param name='flashVars' value='' /> -->
<%-- 	<embed name='mySwf' src='jsp/maintenance/copyforecasttemplate.swf?version=<%= SwtUtil.appVersion %>' --%>
<!-- 		height='100%' width='100%' flashVars='' /></object></div> -->
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.entityMonitor.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>
