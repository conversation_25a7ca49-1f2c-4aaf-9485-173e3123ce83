<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
	<title><c:if test="${requestScope.methodName == 'addTransactionSet'}">
			<fmt:message key="ilmtransSetAdd.title.window.addScreen" />
		</c:if><c:if test="${requestScope.methodName == 'changeTransactionSet'}">
			<fmt:message key="ilmtransSetAdd.title.window.changeScreen" />
		</c:if><c:if test="${requestScope.methodName == 'viewTransaction'}">
			<fmt:message key="ilmtransSetAdd.title.window.viewScreen" />
		</c:if>
	</title>
<script language="JAVASCRIPT">

	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].method.value="listTransactionSets";
		window.opener.document.forms[0].submit();
		self.close();
	</c:if>
	mandatoryFieldsArray = ["transactionSetId"] ;
	function swtTransactionObject() {
		this.accountId = "";
		this.time = "";
		this.credit = 0;
		this.debit = 0;
		this.desc = null;
		this.oper = null;
	}
	
	swtTransactionObject.prototype.init = function(accountId, time, credit,
			debit, desc, oper) {
	
		this.accountId = accountId;
		this.time = time;
		this.credit = credit;
		this.debit = debit;
		this.desc = desc;
		this.oper = oper;
	}

	var addTransactionObjArray = [];
	var changeTransactionObjArray = [];
	var deleteTransactionObjArray = [];

	
	var XMLTransactions =  document.createElement("div"); 
	var root = document.createElement("ilaap");
    var transactionObject;
    
	 function createNewTransactionObj(){
		 transactionObject = new swtTransactionObject();
	 }
	 
	 /*
	 * Used to update grid 
	 *
	 */
	function updateGrid() {
			
		if (transactionObject.oper == "add") {
			addNewRowToGrid(transactionObject);
			addTransactionObjArray.push(transactionObject);
			
		}
		if (transactionObject.oper == "change") {
			updateTransactionObjArray(transactionObject);
			updateGridRow(transactionObject);
		}
		/* Disable required fields and buttons */
		disableButtons();
		document.forms[0].elements["ilmTransactionSetHDR.id.entityId"].disabled ="true";
		document.forms[0].elements["ilmTransactionSetHDR.id.currencyCode"].disabled ="true";
		document.forms[0].elements["ilmTransactionSetHDR.id.entityId"].className ="is-disabled";
		document.forms[0].elements["ilmTransactionSetHDR.id.currencyCode"].className ="is-disabled"; 
	}
	
	/**
	*
	* Used to convert arrays data to XML 
	*
	**/
	function TransactionArrayToXMl(addTransactionObjArray , changeTransactionObjArray ,deleteTransactionObjArray ){
	     var transactionArray=	addTransactionObjArray.concat(changeTransactionObjArray ,deleteTransactionObjArray);
	     if(transactionArray.length>0)
	     
			for(var i= 0; i<transactionArray.length; i++){
				var transObj = transactionArray[i];
				storeInformationIntoXml(transObj);
			}
	}
	
	/**
	*
	* Used to create XML Data that contain information 
	* about Transactions that would contribute to a scenario
	*
	**/
	function storeInformationIntoXml(transactionObject){
		var transactionNode = document.createElement("transaction");
		
		var acctNode = document.createElement("accountid");
		acctNode.appendChild(document.createTextNode(transactionObject.accountId));
		
		var timeNode = document.createElement("time");
		timeNode.appendChild(document.createTextNode(transactionObject.time));
		
		var creditNode = document.createElement("credit");
		creditNode.appendChild(document.createTextNode(transactionObject.credit));
		
		var debitNode = document.createElement("debit");
		debitNode.appendChild(document.createTextNode(transactionObject.debit));
		
		var descNode = document.createElement("description");
		descNode.appendChild(document.createTextNode(transactionObject.desc));
		
		var operNode = document.createElement("operation");
		operNode.appendChild(document.createTextNode(transactionObject.oper));
		
		transactionNode.appendChild(operNode);
		transactionNode.appendChild(acctNode);
		transactionNode.appendChild(timeNode);
		transactionNode.appendChild(creditNode);
		transactionNode.appendChild(debitNode);
		transactionNode.appendChild(descNode);
		
		root.appendChild(transactionNode);
		
		XMLTransactions.appendChild(root);
	}
	
	/**
	* Used to update the (add/change)TransactionObjArray thus :
	*  New row will be appended to addTransactionObjArray and 
	*  when we change an existing row, the change will be stored into changeTransactionObjArray
	*
	**/
	function updateTransactionObjArray(transactionObject) {
		var i = findTransactionObj(transactionObject.accountId, transactionObject.time, addTransactionObjArray);
		if(i != -1){
			addTransactionObjArray[i].credit = transactionObject.credit;
			addTransactionObjArray[i].debit  = transactionObject.debit;
			addTransactionObjArray[i].desc   = transactionObject.desc;	
		}else{
			var j = findTransactionObj(transactionObject.accountId, transactionObject.time , changeTransactionObjArray);
			if(j != -1){
				changeTransactionObjArray[j].credit = transactionObject.credit;
				changeTransactionObjArray[j].debit  = transactionObject.debit;
				changeTransactionObjArray[j].desc   = transactionObject.desc;
			}else
				changeTransactionObjArray.push(transactionObject);
		}
	}

	/*
	 * Used to find transaction detail object  
	 */
	function findTransactionObj(accountId, time , transactionArr) {
		if(transactionArr == null)
			return -1;
		for ( var i = 0; i < transactionArr.length; i++) {
			if (transactionArr[i].accountId.trim() == accountId.trim()
					&& transactionArr[i].time.trim() == time.trim()) {
				return i;
			}
		}
		return -1;
	}

	/*
	 * Return Row num to be changed
	 */
	function getRowTobeChanged(rows,accountId, time){
		var l = rows.length;
		for (var i=0; i<l; i++)
		{
			if(rows[i].cells[0].innerText.trim() == accountId.trim() && rows[i].cells[1].innerText.trim() == time.trim() ) 
					return i;
	    }
		return null;
	}
	
	/*
	 * Return the selected row num
	 */
	function getSelectedRow(rows) {
		var l = rows.length;
		for ( var i = 0; i < l; i++) {
			if (isRowSelected(rows[i])) {
			return i ;
			}
		}
	}


	/*
	 * Used to delete a row from data grid 
	 */
	function deleteGridRow() {
		var rows = xl.dataTable.tBody.rows;
		//disable required buttons when grid will be empty
		if(rows.length == 1){
			document.forms[0].elements["ilmTransactionSetHDR.id.entityId"].disabled ="";
			document.forms[0].elements["ilmTransactionSetHDR.id.currencyCode"].disabled ="";
			document.forms[0].elements["ilmTransactionSetHDR.id.entityId"].className ="";
			document.forms[0].elements["ilmTransactionSetHDR.id.currencyCode"].className =""; 
			disableButtons();
		}
		var rowNum = getSelectedRow(rows);
		if (rowNum != null) {
			var accountId = rows[rowNum].cells[0].innerText.trim();
			var time = rows[rowNum].cells[1].innerText.trim();
			//delete row from grid
			 document.getElementById('ilmTransactionSetsDetails')
							.getElementsByTagName('tbody')[0].deleteRow(rowNum);
			 updateColors();
			var existInAdd = findTransactionObj(accountId, time,
					addTransactionObjArray);
			if (existInAdd != -1) {
				//delete from addTransactionObjArray
				addTransactionObjArray.splice(existInAdd,1);
				return;
			}
			var existInChange = findTransactionObj(accountId, time,
					changeTransactionObjArray);
			if (existInChange != -1) {
				//delete from changeTransactionObjArray
				changeTransactionObjArray.splice(existInAdd,1);
				//add the row to deleteTransactionObjArray
				var transObj = new swtTransactionObject();
				transObj.init(accountId, time);
				deleteTransactionObjArray.push(transObj);
				return;
			}
			//add the row to deleteTransactionObjArray
			var transObj = new swtTransactionObject();
			transObj.init(accountId, time,"","","","delete");
			deleteTransactionObjArray.push(transObj);
			disableButtons();
		}
		
	}
	/**
	 * Disable buttons and colors when the user does not select any row in the grid or when grid is empty
	 *
	 **/
	function disableButtons(){ 
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}
	
	//Used to update data grid (change row value)
	function updateGridRow(transactionObject) {
		var rows = xl.dataTable.tBody.rows;
		var numRow = getRowTobeChanged(rows, transactionObject.accountId,
				transactionObject.time);
		if (numRow != null) {
			rows[numRow].cells[2].innerText = transactionObject.credit;
			rows[numRow].cells[3].innerText = transactionObject.debit;
		}
		updateColors();
	}

	/*
	 * Used to add a new row to data grid 
	 */
	function addNewRowToGrid(transactionObject) {
		var tableRef = document.getElementById('ilmTransactionSetsDetails')
				.getElementsByTagName('tbody')[0];

		// Insert a row in the table at the last row
		var newRow = tableRef.insertRow(tableRef.rows.length);
		// Insert a new account cell in the new row 
		var newAccoutCell = newRow.insertCell(0);
		newAccoutCell.style.width = "250px";
		// Insert a new time cell in the new row 
		var newTimeCell = newRow.insertCell(1);
		newTimeCell.style.width = "80px";
		// Insert a new credit cell in the new row 
		var newCreditCell = newRow.insertCell(2);
		newCreditCell.style.width = "151px";
		// Insert a new debit cell in the new row 
		var newDebitCell = newRow.insertCell(3);
		newDebitCell.style.width = "151px";

		/* Append text node to the new cells */
		var accoutText = document.createTextNode(transactionObject.accountId);
		newAccoutCell.appendChild(accoutText);

		var timeText = document.createTextNode(transactionObject.time);
		newTimeCell.appendChild(timeText);

		var creditText = document.createTextNode(transactionObject.credit);
		newCreditCell.appendChild(creditText);

		var debitText = document.createTextNode(transactionObject.debit);
		newDebitCell.appendChild(debitText);
		updateColors();
		highlightTableRows("ilmTransactionSetsDetails");
	}

	function bodyOnLoad() {
		xl = new XLSheet("ilmTransactionSetsDetails", "table_2", ["String",
			"String", "Number", "Number"], "1111");

		xl.onsort = xl.onfilter = disableButtons;

		highlightTableRows("ilmTransactionSetsDetails");

		document.getElementById("deletebutton").innerHTML = document
				.getElementById("deletedisablebutton").innerHTML;

		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";
		document.getElementById("addbutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("addenablebutton").innerHTML : document.getElementById("adddisablebutton").innerHTML);

		buttonStatus = "<%= request.getAttribute(SwtConstants.SAV_BUT_STS) %>";
		document.getElementById("savebutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("saveenablebutton").innerHTML : document.getElementById("savedisablebutton").innerHTML);


		buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";
		document.getElementById("changebutton").innerHTML = (buttonStatus == "<%=SwtConstants.STR_TRUE%>" ? document.getElementById("changeenablebutton").innerHTML : document.getElementById("changedisablebutton").innerHTML);

		<c:choose>
		<c:when test="${requestScope.methodName == 'addTransactionSet'}">
		var entityDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["ilmTransactionSetHDR.id.entityId"], document
				.getElementById("entityName"));
		var currencyDropBoxElement = new SwSelectBox(document.forms[0]
				.elements["ilmTransactionSetHDR.id.currencyCode"], document
				.getElementById("currencyDesc"));
		</c:when>
		<c:otherwise>
		document.getElementById("entityName").innerText = '${entityText}';
		document.getElementById("currencyDesc").innerText = '${currencyCodeText}';
		</c:otherwise>
		</c:choose>
	}

	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e) {
		var event = (window.event || e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while (srcEl.tagName != 'TD') {
			srcEl = srcEl.parentElement;
		}

		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;

		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);

		if (isRowSel == false)
			rowElement.className = 'selectrow';
		<c:if test="${requestScope.methodName == 'viewTransaction'}">
		return;
		</c:if>


		var entityId = document.forms[0]
				.elements["ilmTransactionSetHDR.id.entityId"].value;

		var currencyCode = document.forms[0]
				.elements["ilmTransactionSetHDR.id.currencyCode"].value;

		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/' + appName + '/');

		requestURL = requestURL.substring(0, idy + 1);
		requestURL = requestURL + appName
				+ "/ilmTransScenario.do?method=checkTransAccess";
		requestURL = requestURL + "&selectedEntityId=" + entityId.trim()
				+ "&selectedCurrencyCode=" + currencyCode.trim();
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open("POST", requestURL, false);
		oXMLHTTP.send();
		menuEntityCurrGrpAccess = new String(oXMLHTTP.responseText);

		document.getElementById("changebutton").innerHTML = (!isRowSel && menuEntityCurrGrpAccess == "0" ?
				document.getElementById("changeenablebutton").innerHTML :
				document.getElementById("changedisablebutton").innerHTML);
		document.getElementById("deletebutton").innerHTML = (!isRowSel && menuEntityCurrGrpAccess == "0" ?
				document.getElementById("deleteenablebutton").innerHTML :
				document.getElementById("deletedisablebutton").innerHTML);

		if (!isRowSel && menuEntityCurrGrpAccess == "0")
			onSelectTableRow(rowElement, !isRowSel);
	}

	function onSelectTableRow(rowElement, isSelected) {
		document.forms[0].selectedAccountId.value = rowElement.cells[0].innerText
				.trim();
		document.forms[0].selectedTime.value = rowElement.cells[1].innerText
				.trim();
	}

	function submitForm(methodName,actionFrom) {
		if(document.forms[0].elements["ilmTransactionSetHDR.id.txnSetId"].value == "" && methodName != 'addTransactionSet'){
			alert('<fmt:message key="alert.pleaseFillAllMandatoryFields"/>');
  		return;
		}
		if(!validateField(document.forms[0].elements["ilmTransactionSetHDR.id.txnSetId"],'txnSetId','alphaNumPat'))
			return;
		if(!validateField(document.forms[0].elements["ilmTransactionSetHDR.txnSetName"],'txnSetName','ascii_standard'))
			return;
		document.forms[0].method.value = methodName;
		document.forms[0].selectedEntityId.value = document.forms[0]
				.elements["ilmTransactionSetHDR.id.entityId"].value;
		if(actionFrom != "entityCombo") {
			document.forms[0].selectedCurrencyCode.value = document.forms[0]
					.elements["ilmTransactionSetHDR.id.currencyCode"].value;
		}else {
			document.forms[0].selectedCurrencyCode.value =  "";
		}
		document.forms[0].transactionId.value = document.forms[0]
				.elements["ilmTransactionSetHDR.id.txnSetId"].value;
		document.forms[0].transactionName.value = document.forms[0]
				.elements["ilmTransactionSetHDR.txnSetName"].value;
		if(methodName != 'addTransactionSet'){
			TransactionArrayToXMl(addTransactionObjArray,
					changeTransactionObjArray, deleteTransactionObjArray);
			document.forms[0].XMLTransactions.value = getMenuWindow().encode64(XMLTransactions.innerHTML);	
		}
		document.forms[0].submit();
	}

	function buildAddTransactionDetailURL(methodName) {

		var param = 'ilmTransScenario.do?method=' + methodName;
		param = param
				+ "&selectedEntityId="
				+ document.forms[0]
						.elements["ilmTransactionSetHDR.id.entityId"].value
				+ "&selectedCurrencyCode="
				+ document.forms[0]
						.elements["ilmTransactionSetHDR.id.currencyCode"].value;
		return param;
	}

	function buildChangeTransactionDetailURL(methodName) {
		var param = 'ilmTransScenario.do?method=' + methodName;
		param = param
				+ "&selectedEntityId="
				+ document.forms[0]
						.elements["ilmTransactionSetHDR.id.entityId"].value
						.trim()
				+ "&selectedCurrencyCode="
				+ document.forms[0]
						.elements["ilmTransactionSetHDR.id.currencyCode"].value
						.trim()
				+ "&selectedtxnSetId="
				+ document.forms[0]
						.elements["ilmTransactionSetHDR.id.txnSetId"].value
						.trim() + "&selectedAccountId="
				+ document.forms[0].selectedAccountId.value + "&selectedTime="
				+ document.forms[0].selectedTime.value;

		var indexAdd = findTransactionObj(
				document.forms[0].selectedAccountId.value,
				document.forms[0].selectedTime.value, addTransactionObjArray);
		
		if (indexAdd != -1)
			param = param + "&isNewRecord=true" + "&selectedCredit="
					+ addTransactionObjArray[indexAdd].credit + "&selectedDebit="
					+ addTransactionObjArray[indexAdd].debit
					+ "&selectedDecription="
					+ addTransactionObjArray[indexAdd].desc;
		else{
			  var indexChange = findTransactionObj(
					document.forms[0].selectedAccountId.value,
					document.forms[0].selectedTime.value, changeTransactionObjArray);
			  if (indexChange != -1)
					param = param + "&isNewRecord=true" + "&selectedCredit="
							+ changeTransactionObjArray[indexChange].credit + "&selectedDebit="
							+ changeTransactionObjArray[indexChange].debit
							+ "&selectedDecription="
							+ changeTransactionObjArray[indexChange].desc;
		}
		return param;
	}
</script>
</head>
<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="bodyOnLoad();setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');"
	onunload="call()">
	<form action="ilmTransScenario.do">
		<input name="method" type="hidden" value="">
		<input name="selectedEntityId" type="hidden" value="">
		<input name="selectedCurrencyCode" type="hidden" value="">
		<input name="selectedAccountId" type="hidden" value="">
		<input name="selectedCredit" type="hidden" value="">
		<input name="selectedDebit" type="hidden" value="">
		<input name="selectedTime" type="hidden" value="">
		<input name="transactionId" type="hidden" value="">
		<input name="transactionName" type="hidden" value="">
		<input name="XMLTransactions" type="hidden" value="">
		<input name="actionFrom" type="hidden" value="">



		<div id="ilmTransactionsetDisplay"
			style="position: absolute; left: 20px; top: 20px; width: 660px; height: 130px; border: 2px outset;"
			color="#7E97AF">
			<div id="ilmTransactionsetDisplay"
				style="position: absolute; left: 8px; top: 15px; width: 590px; height: 100;">
				<table width="580px" border="0" cellpadding="0" cellspacing="0"
					height="100px" class="content">

					<tr>
						<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.entityId" /></b></td>
						<td width="28">&nbsp;</td>
					<c:choose>
    					<c:when test="${requestScope.methodName == 'addTransactionSet'}">
							<td width="90px"><select id="ilmTransactionSetHDREntityId"
									name="ilmTransactionSetHDR.id.entityId"
									onchange="submitForm('addTransactionSet','entityCombo');"
									titleKey="tooltip.selectEntity" style="width:120px"
									tabindex="1">
    						<c:forEach items="${requestScope.entities}" var="entity">
							   <option
								  value="${entity.value}"
								  <c:if test="${ilmTransactionSetHDR.id.entityId == entity.value}">selected="selected"</c:if>>
								  ${entity.label}
							   </option>
							</c:forEach>
								</select></td>
						</c:when>
						<c:otherwise>
							<td width="90px"><input type="text"
									name="ilmTransactionSetHDR.id.entityId"
									value="${ilmTransactionSetHDR.id.entityId}"
									titleKey="tooltip.selectEntity" style="width:120px"
									tabindex="1" disabled="true" /></td>
						</c:otherwise>
					</c:choose>
						<td width="15">&nbsp;</td>
						<td width="220"><span id="entityName" name="entityName"
							class="spantext"></td>
					</tr>

					<tr>
						<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.currency" /></b></td>
						<td width="28">&nbsp;</td>
					<c:choose>
    					<c:when test="${requestScope.methodName == 'addTransactionSet'}">
							<td width="90"><select
									name="ilmTransactionSetHDR.id.currencyCode"
									titleKey="tooltip.selectCurrencyCode"
									style="width:55px;height: 21px"
									onchange="submitForm('addTransactionSet', 'currencyCombo')" tabindex="2">
									<c:forEach items="${requestScope.currencyList}" var="ccy">
									   <option
										  value="${ccy.value}"
										  <c:if test="${ilmTransactionSetHDR.id.currencyCode == ccy.value}">selected="selected"</c:if>>
										  ${ccy.label}
									   </option>
									</c:forEach>
								</select></td>
						</c:when>
						<c:otherwise>
							<td width="90"><input type="text"
									name="ilmTransactionSetHDR.id.currencyCode"
									value="${ilmTransactionSetHDR.id.currencyCode}"
									titleKey="tooltip.selectCurrencyCode"
									style="width:55px;height: 21px" tabindex="2" disabled="true" />
							</td>
						</c:otherwise>
					</c:choose>
						<td width="20">&nbsp;</td>
						<td width="220"><span id="currencyDesc" class="spantext"></td>
					</tr>

					<tr>
						<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.transactionSetId" /> 
						<c:if test="${requestScope.methodName == 'addTransactionSet'}"> *</c:if></b></td>
						<td width="28">&nbsp;</td>
						<td colspan="3" id="transactionSetId"><input type="text" maxlength="20"
								name="ilmTransactionSetHDR.id.txnSetId"
								value="${ilmTransactionSetHDR.id.txnSetId}"
								tabindex="3"
								<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>
								titleKey="tooltip.transactionSetId"
								style="width:280px;height: 21px"
								onchange="return validateField(this,'transactionSetId','alphaNumPat');"/></td>


					</tr>
					<tr>
						<td width="150" style="padding-left: 10px;"><b><fmt:message key="ilmTransactionSet.transactionSetName" /></b></td>
						<td width="28">&nbsp;</td>
						<td colspan="3"><input type="text" maxlength="50"
								name="ilmTransactionSetHDR.txnSetName"
								titleKey="tooltip.transactionSetName"
								value="${ilmTransactionSetHDR.txnSetName}"
								tabindex="4"
								style="width:380px;height: 21px"
								onchange="return validateField(this,'transactionSetName','ascii_standard');"
								<c:if test="${requestScope.methodName == 'viewTransaction'}">disabled</c:if>/></td>


					</tr>

				</table>

			</div>
			<div id="IlmTransactionDetails"
				style="position: absolute; border: 2px outset; top: 135px; left: 0px; width: 660px; height: 350;">
				<div id="IlmTransactionDetails"
					style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 645; height: 10px;">
					<table class="sort-table" id="table_2" bgcolor="#B0AFAF"
						width="636" border="0" cellspacing="1" cellpadding="0">
						<thead>
							<tr height="22px">
								<td width="250" align="left"
									title='<fmt:message key="tooltip.sort.accountId"/>'><b><fmt:message key="ilmTransactionSet.accountId" /></b></td>
								<td width="80" align="left"
									title='<fmt:message key="tooltip.sort.time"/>'><b><fmt:message key="ilmTransactionSet.time" /></b></td>
								<td width="151" align="left"
									title='<fmt:message key="tooltip.sort.credits"/>'><b><fmt:message key="ilmTransactionSet.credits" /></b></td>
								<td width="151" align="left"
									title='<fmt:message key="tooltip.sort.debits"/>'><b><fmt:message key="ilmTransactionSet.debits" /></b></td>
						</thead>
					</table>
				</div>
				<div id="ddscrolltable"
					style="position: absolute; left: 0px; top: 2px; width: 656px; height: 340; overflowY:scroll">
					<div id="IlmTransactionDetails"
						style="position: absolute; z-index: 99; left: 0px; top: 22px; width: 638px; height: 10px;">
						<table class="sort-table" id="ilmTransactionSetsDetails"
							width="636px" border="0" cellspacing="1" cellpadding="0"
							height="318">
						<tbody>
							<c:set var="count" value="0" />
							<c:forEach var="detail" items="${requestScope.ilmTransactionSetsDetails}">
								<c:choose>
									<c:when test="${count % 2 == 0}">
										<tr height="20px" class="even">
									</c:when>
									<c:otherwise>
										<tr height="20px" class="odd">
									</c:otherwise>
								</c:choose>
								<c:set var="count" value="${count + 1}" />

								<td width="250" align="left">${detail.id.accountId}&nbsp;</td>
								<td width="80" align="left">${detail.id.time}&nbsp;</td>
								<td width="151" align="left">${detail.creditsAsString}&nbsp;</td>
								<td width="151" align="left">${detail.debitsAsString}&nbsp;</td>
							</tr>
							</c:forEach>
						</tbody>
							<tfoot>
								<tr>
									<td colspan="6"></td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>

			</div>
			<div id="IlmTransactionDetails"
				style="position: absolute; left: 580; top: 510; width: 70px; height: 29px; visibility: visible;">
				<table width="60px" border="0" cellspacing="0" cellpadding="0"
					height="20">
					<tr>
						<c:if test="${requestScope.methodName == 'addTransactionSet'}">
							<td align="Right"><a tabindex="10" href=#
								onclick="javascript:openWindow(buildPrintURL('print','Add ILM Transaction Set Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
									src="images/help_default.GIF " name="Help" border="0"
									title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
						</c:if>
						<c:if test="${requestScope.methodName == 'changeTransactionSet'}">
							<td align="Right"><a tabindex="10" href=#
								onclick="javascript:openWindow(buildPrintURL('print','Change ILM Transaction Set Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
									src="images/help_default.GIF " name="Help" border="0"
									title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
						</c:if>
						<c:if test="${requestScope.methodName == 'viewTransaction'}">
							<td align="Right"><a tabindex="10" href=#
								onclick="javascript:openWindow(buildPrintURL('print','View ILM Transaction Set Detail Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
								onMouseOut="MM_swapImgRestore()"
								onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
									src="images/help_default.GIF " name="Help" border="0"
									title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
						</c:if>
						<td align="right" id="Print">&nbsp; <a tabindex="11"
							onclick="printPage();" onMouseOut="MM_swapImgRestore()"
							onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
								src="images/Print.gif " name="Print" border="0"
								title='<fmt:message key="tooltip.printScreen"/>'></a>
						</td>
					</tr>
				</table>
			</div>
			<div id="ddimagebuttons" color="#7E97AF"
				style="position: absolute; border: 2px outset; left: 0; top: 500; width: 660px; height: 39px; visibility: visible;">
				<div id="IlmTransactionDetails"
					style="position: absolute; left: 2; top: 4; width: 600px; height: 15px; visibility: visible;">
					<table width="410" border="0" cellspacing="0" cellpadding="0"
						height="20">
						<tr>
							<td id="addbutton"></td>
							<td id="changebutton"></td>
							<td id="deletebutton"></td>
							<td id="savebutton"></td>
							<td id="closebutton" width="70px"><a
								title='<fmt:message key="tooltip.close"/>' tabindex="9"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
						</tr>
					</table>
				</div>
				<div
					style="position: absolute; left: 6; top: 4; width: 654px; height: 15px; visibility: hidden; display: none;">
					<table width="350" border="0" cellspacing="0" cellpadding="0"
						height="20" style="visibility: hidden;">
						<tr>
							<td id="addenablebutton"><a
								title='<fmt:message key="tooltip.addScenario"/>' tabindex="5"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:openWindow(buildAddTransactionDetailURL('addTransactionDetail'),'transactiondetailadd','left=50,top=190,width=650,height=260,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add" /></a></td>
							<td id="adddisablebutton"><a class="disabled"
								disabled="disabled"><fmt:message key="button.add" /></a></td>
							<td id="changeenablebutton"><a
								title='<fmt:message key="tooltip.changeScenario"/>' tabindex="6"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:openWindow(buildChangeTransactionDetailURL('changeTransactionDetail'),'transactiondetailchange','left=50,top=190,width=650,height=260,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change" /></a></td>
							<td id="changedisablebutton"><a class="disabled"
								disabled="disabled"><fmt:message key="button.change" /></a></td>
							<td id="saveenablebutton"><a
								title='<fmt:message key="tooltip.viewScenario"/>' tabindex="7"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)"
								onClick="javascript:submitForm('${methodName eq 'addTransactionSet' ? 'saveTransaction': 'updateTransaction'}', 'saveButton');"><fmt:message key="button.save" /></a></td>
							<td id="savedisablebutton"><a class="disabled"
								disabled="disabled"><fmt:message key="button.save" /></a></td>
							<td id="deleteenablebutton"><a
								title='<fmt:message key="tooltip.deleteScenario"/>' tabindex="8"
								onMouseOut="collapsebutton(this)"
								onMouseOver="highlightbutton(this)"
								onMouseDown="expandbutton(this)"
								onMouseUp="highlightbutton(this)" onClick="deleteGridRow()"><fmt:message key="button.delete" /></a></td>
							<td id="deletedisablebutton"><a class="disabled"
								disabled="disabled"><fmt:message key="button.delete" /></a></td>

						</tr>
					</table>
				</div>
			</div>
			<blockquote>&nbsp;</blockquote>
			<p>&nbsp;</p>
	</form>

</body>
</html>