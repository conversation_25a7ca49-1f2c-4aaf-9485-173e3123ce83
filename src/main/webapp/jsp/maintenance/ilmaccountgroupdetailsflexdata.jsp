<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>


<ilmaccountgroupdetails
	name="ilmaccountgroupdetails" >

<request_reply> <status_ok>${reply_status_ok}</status_ok>
<message>${reply_message}</message>
<location /> </request_reply>
<timing>
	<c:forEach items="${opTimes}" var="opTime">
		<operation id="${opTime.key}">${opTime.value}</operation>
	</c:forEach>
</timing>
<singletons> 
	<publicprivate>
		${requestScope.accountGroup.publicPrivate}
	</publicprivate>
	<grouptype>
		${requestScope.accountGroup.groupType}
	</grouptype>
	<groupname>
		${requestScope.accountGroup.ilmGroupName}
	</groupname>
	<groupdescription>
		${requestScope.accountGroup.ilmGroupDescription}
	</groupdescription>
	<idname>
		${requestScope.accountGroup.defaultLegendText}
	</idname>
	<mainAgent>
		${requestScope.accountGroup.mainAgent}
	</mainAgent>
	<allowReporting>
		${requestScope.accountGroup.allowReporting}
	</allowReporting>
	<collectNetCumPos>
		${requestScope.accountGroup.collectNetCumPos}
	</collectNetCumPos>
	<correspondentBank>
		${requestScope.accountGroup.correspondentBank}
	</correspondentBank>
	<firstminimum> 
		${requestScope.accountGroup.thresholdMin1}
	</firstminimum>
	<firstmaximum> 
		${requestScope.accountGroup.thresholdMax1}
	</firstmaximum>
	<secondminimum>
	  	${requestScope.accountGroup.thresholdMin2}
	</secondminimum>
	<secondmaximum > 
		${requestScope.accountGroup.thresholdMax2}
	</secondmaximum>
	<filtercondition >
		${requestScope.accountGroup.filterCondition}
			
	</filtercondition>
	<minNcpThreshold >
		${requestScope.accountGroup.minNcpThreshold}
	</minNcpThreshold>
	<maxNcpThreshold >
		${requestScope.accountGroup.maxNcpThreshold}
	</maxNcpThreshold>
	<createDate>
		${createdDate}
	</createDate>
	<maintainAnyGroup>
		${maintainAnyGroup}
	</maintainAnyGroup>
	<global>
		${global}
	</global>
	<central>
		${central}
	</central>
	<createdByUser >
		${requestScope.accountGroup.createdByUser}
	</createdByUser>
	<thresh1Percent >
		${requestScope.accountGroup.thresh1Percent}
	</thresh1Percent>
	<thresh2Percent >
		${requestScope.accountGroup.thresh2Percent}
	</thresh2Percent>
	<thresh1Time >
		${requestScope.accountGroup.thresh1Time}
	</thresh1Time>
	<thresh2Time >
		${requestScope.accountGroup.thresh2Time}
	</thresh2Time>
	<createThroughputRatio >
		${requestScope.accountGroup.createThroughputRatio}
	</createThroughputRatio>
</singletons>
<grid>
	<metadata>
		<columns>
			<column heading="<fmt:message key="ilmAccountGroupDetails.accountIdName" />"
					draggable="false"
					filterable="true"
					type="str"
					dataelement="account_id_name"
					sort="false"
					width="${requestScope.column_width['accountIdName']}"/>
			<column heading="<fmt:message key="ilmAccountGroupDetails.type" />"
					draggable="false"
					filterable="true"
					type="str"
					dataelement="type"
					sort="false"
					width="${requestScope.column_width['type']}"/>
			<column heading="<fmt:message key="ilmAccountGroupDetails.class" />"
					draggable="false"
					filterable="true"
					type="str"
					dataelement="class"
					sort="true"
					width="${requestScope.column_width['class']}"/>
			<column heading="<fmt:message key="ilmAccountGroupDetails.level" />"
					draggable="false"
					filterable="true"
					type="str"
					dataelement="level"
					sort="true"
					width="${requestScope.column_width['level']}"/>
		</columns>
	</metadata>

	<rows size="${rowSize}">
		<c:forEach var="accountInGroup" items="${requestScope.accountInGroup}">
			<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false">
				<account_id_name clickable="false">
						${accountInGroup.id.account.id.accountId} - ${accountInGroup.id.account.acctname}
				</account_id_name>
				<type clickable="false">
					<c:if test="${accountInGroup.id.account.accttype == 'C'}">Cash</c:if>
					<c:if test="${accountInGroup.id.account.accttype == 'U'}">Custodian</c:if>
				</type>
				<class clickable="false">
					<c:if test="${accountInGroup.id.account.acctClass == 'C'}">Current</c:if>
					<c:if test="${accountInGroup.id.account.acctClass == 'L'}">Loro</c:if>
					<c:if test="${accountInGroup.id.account.acctClass == 'E'}">Netting</c:if>
					<c:if test="${accountInGroup.id.account.acctClass == 'N'}">Nostro</c:if>
					<c:if test="${accountInGroup.id.account.acctClass == 'O'}">Others</c:if>
				</class>
				<level clickable="false">
					<c:if test="${accountInGroup.id.account.acctlevel == 'M'}">Main</c:if>
					<c:if test="${accountInGroup.id.account.acctlevel != 'M'}">Sub</c:if>
				</level>
				<entity_id clickable="false">${accountInGroup.id.account.id.entityId}</entity_id>
				<account_id clickable="false">${accountInGroup.id.account.id.accountId}</account_id>
			</row>
		</c:forEach>
	</rows>
</grid>

<selects> 
	<select id="entity">
		<c:forEach items="${requestScope.entities}" var="entity">
			<c:choose>
				<c:when test="${requestScope.accountGroup.entityId == entity.value}">
					<option value="${entity.value}" selected="1">${entity.label}</option>
				</c:when>
				<c:otherwise>
					<option value="${entity.value}" selected="0">${entity.label}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	</select> 
				
	<select id="currency">
		<c:forEach items="${requestScope.currencies}" var="currency">
			<c:choose>
				<c:when test="${requestScope.accountGroup.currencyCode == currency.value}">
					<option value="${currency.value}" selected="1">${currency.label}</option>
				</c:when>
				<c:otherwise>
					<option value="${currency.value}" selected="0">${currency.label}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	 </select>
	<select id="accountgroups">
		<c:forEach items="${requestScope.accountGroups}" var="group">
			<c:choose>
				<c:when test="${requestScope.accountGroup.id.ilmGroupId == group.value}">
					<option value="${group.value}" selected="1">${group.label}</option>
				</c:when>
				<c:otherwise>
					<option value="${group.value}" selected="0">${group.label}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	</select>
</selects>
 </ilmaccountgroupdetails>