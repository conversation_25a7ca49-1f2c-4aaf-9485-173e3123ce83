<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--
  - The main purpose of this jsp file is to load the Add a column to / change a column of Forecast Monitor Template (details) screen. 
  -	And to embed the Add Forecast Monitor Template flex screen. 
  - Also, to load the label values for this screen.	
  -
  - Author(s): Vivekanandan A
  - Date: 25-05-2011
  -->
<%@ include file="/taglib.jsp"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil"%>
<%@ include file="/angularJSUtils.jsp"%>

<html>
	<head>
	<title>
		<c:choose>
			<c:when test="${'change' == requestScope.screenName}">
				<fmt:message key="label.forecastMonitorTemplateChangeDetail.title.window"/>
			</c:when>
			<c:otherwise>
				<fmt:message key="label.forecastMonitorTemplateAddDetail.title.window"/>
			</c:otherwise>
		</c:choose>
	</title>
		
		<style>
			body { margin: 0px; overflow:hidden }
		</style>
		<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
</head>
		<script type="text/javascript" src="jsp/work/assets/swfobject.js"></script>
		<script type="text/javascript">
		
						
			window.onunload = call;
			
			var templateId = '${requestScope.templateId}';
			var templateName = '${requestScope.templateName}';
			var userId = '${requestScope.userId}';
			var isPublic = '${requestScope.isPublic}';
			var screenName = '${requestScope.screenName}';
			var menuAccessId = '${requestScope.menuAccessId}';
			var	shortName = '${requestScope.shortName}';
			var	description = '${requestScope.description}';
			var	type = '${requestScope.type}';
			var	columnId = '${requestScope.columnId}';
			var ordinalPos = '${requestScope.ordinalPos}';
			var detailRowCount = '${requestScope.detailRowCount}';
			var modify= '${requestScope.modify}';
			var copiedTemplateId= '${requestScope.copiedTemplateId}';
			var copyFromFlag= '${requestScope.copyFromFlag}';
			var copiedUserId= '${requestScope.copiedUserId}';
			var addClick= '${requestScope.addClick}';
			var screenRoute = "forecastMonitorTemplateDetail";

			
			
			
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/'+appName+'/');
			requestURL=requestURL.substring(0,idy+1) ;
			
			// pop up window
			var popUpWindow = "";
			// This method is called when onload
			window.onload = function () {
			    setTitleSuffix(document.forms[0]);
				setParentChildsFocus();		
			};
			
			
			// Set the label values
			var label = new Array ();
			label["text"] = new Array ();
			label["tip"] = new Array ();
			label["alert"] = new Array ();
			
			// Column Type
			label["text"]["columntype"] = "<fmt:message key="label.forecastMonitorTemplateAddDetail.columnType"/>";
			label["tip"]["columntype"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.columnType"/>";
			
			// Column ID
			label["text"]["columnid"] = "<fmt:message key="label.forecastMonitorTemplateAddDetail.columnId"/>";
			label["tip"]["columnid"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.columnId"/>";	
			
			// ShortName
			label["text"]["shortname"] = "<fmt:message key="label.forecastMonitorTemplateAddDetail.shortName"/>";
			label["tip"]["shortname"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.shortName"/>";
			
			// Description
			label["text"]["description"] = "<fmt:message key="label.forecastMonitorTemplateAddDetail.description"/>";
			label["tip"]["description"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.description"/>";
			
			// Contribute check
			label["text"]["contributecheck"] = "<fmt:message key="label.forecastMonitorTemplateAddDetail.contributeCheck"/>";
			label["tip"]["contributecheck"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.contributeCheck"/>";
			
			// Contribute text
			label["tip"]["contributetext"] = "<fmt:message key="tooltip.forecastMonitorTemplateAddDetail.contributeText"/>";
			
			
			// find button
			label["text"]["button.forecastMonitor.find"] = "<fmt:message key="button.forecastMonitor.find"/>";
			label["tip"]["button.forecastMonitor.find"] = "<fmt:message key="button.forecastMonitor.find"/>";

			// Suggest button
			label["text"]["button.forecastmonitor.suggest"] = "<fmt:message key="button.forecastmonitor.suggest"/>";
			label["tip"]["button.forecastmonitor.suggest"] = "<fmt:message key="button.forecastmonitor.suggest"/>";
			
			//alert
			label["alert"]["template-columndelete"]="<fmt:message key="alert.forecasttemplate.columndelete"/>";
			label["alert"]["template-mandatory"]="<fmt:message key="alert.forecasttemplate.mandatory"/>";
			label["alert"]["template-totalmultiplier"]="<fmt:message key="alert.forecasttemplate.totalmultiplier"/>";
			label["alert"]["template-columnnumbers"]="<fmt:message key="alert.forecasttemplate.columnnumbers"/>";
			label["alert"]["template-validnumber"]="<fmt:message key="alert.forecasttemplate.validnumber"/>";
			
			// Add button
			label["text"]["button-add"] = "<fmt:message key="button.add"/>";
			label["tip"]["button-add"] = "<fmt:message key="button.add"/>";	
			
			// OK button
			label["text"]["button-ok"] = "<fmt:message key="button.ok"/>";
			label["tip"]["button-ok"] = "<fmt:message key="button.ok"/>";	
			
			// Delete button
			label["text"]["button-delete"] = "<fmt:message key="button.delete"/>";
			label["tip"]["button-delete"] = "<fmt:message key="button.delete"/>";	
			
			
			// Close button
			label["text"]["button-close"] = "<fmt:message key="button.close"/>";
			label["tip"]["button-close"] = "<fmt:message key="tooltip.close"/>";	
			
			label["text"]["label-confirm"] = "<fmt:message key="button.confirm"/>";
			
			/**
			 * openChildWindow
			 * @param methodName
			 * Method to load child screens
			 */
			function openChildWindow(methodName){
    			var param = '/' + appName + '/forecastMonitorTemplate.do?method='+methodName;
	    		popUpWindow = window.open(param,'forecastMonitorTemplateColumnSrc','left=30,top=345,width=670,height=500,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true');
				return false;
			}
			
			/**
			 * refreshParent
			 * Method to refresh parent
			 */
			 function refreshParent(){
			 	window.opener.reloadMain();
				window.close();
				closeChild();
			 }
			 /**
			 * closeChild
			 * This function used to close the child window
			 */
			function closeChild(){
				// Added By KaisBS : issue 1054_STL_038 : avoid the javascript error if we aren't open the popup screen
				if(typeof(popUpWindow) == window) 
				popUpWindow.close();
			}
			
			/**
             * help
             * This function opens the help screen 
 			 * @return none
			 */
			function help(){
				openWindow(buildPrintURL('print','Add a Column to / Change a column in Forecast Monitor Template'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')
            }
			
			
			/**
			 * refreshDetail
			 * Method to refresh grid to load datas for normal
			 */
			 function refreshDetail(){
				 Main.refreshDetail();
				// getFlashObject("mySwf").refreshDetail();
			 }
			
			/**
			 * refreshSubDetail
			 * Method to refresh grid to load datas for subtotal
			 */
			 function refreshSubDetail(){
				 Main.refreshSubDetail();
			 }
		</script>
<%@ include file="/angularscripts.jsp"%>
	<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();"  onunload="closeChild()">
		<form id="exportDataForm" target="tmp" method="post">
		<input type="hidden" name="data" id="exportData"/>
		<input type="hidden" name="screen" id="exportDataScreen" value="<fmt:message key="label.forecastMonitorTemplateChangeDetail.title.window"/>"/>
		</form>
		<iframe name="tmp" width="0%" height="0%" src="#" />
	</body>
</html>
