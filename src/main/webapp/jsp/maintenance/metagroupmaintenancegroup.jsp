<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="metaGroup.title.mainWindow"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


<SCRIPT language="JAVASCRIPT">
<c:if test="${'yes' == requestScope.parentFormRefresh}">
window.opener.document.forms[0].method.value="displayListByEntity";
window.opener.document.forms[0].submit();
self.close();
</c:if>
</SCRIPT>

<SCRIPT language="JAVASCRIPT">

function disableAllButtons()
{
	document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML;
}


function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

function populate(){
	
	 
	bodyOnLoad();
	ShowErrMsgWindow('${actionError}');
}
function bodyOnLoad()
{
	 
	xl = new XLSheet("groupListDetails","table_2", ["String","String", "Number"],"111");
	xl.onsort = xl.onfilter = onFilterandSort;
	 
	highlightTableRows("groupListDetails");
	
	document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML;
}
function onSelectTableRow(rowElement,isSelected)
{  
	var cell3 = rowElement.cells[2];	
	var hiddenEl3 = cell3.getElementsByTagName("input")[0];		

	var hiddenElement = rowElement.getElementsByTagName("input")[0];
	document.forms[0].selectedgroupId.value = hiddenElement.value;
	
	if(hiddenEl3.value > 0 && isSelected){		
		document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodeenablebutton").innerHTML;}	
	else{
	
		document.getElementById("bookcodebutton").innerHTML = document.getElementById("bookcodedisablebutton").innerHTML;}
	} 

function buildGrpBook(methodName){
	var param = 'metaGroup.do?method='+methodName+'&selectedgroupId=';
	param +=  document.forms[0].selectedgroupId.value;
	
	return  param;
}
function submitForm(methodName){
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

/* This Method is used for save or update the data and close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "close"){
		confirmClose('C');
		}else if(method == "bookcode"){
		openWindow(buildGrpBook('bookcodes'),'groupmaintenancebookcodesWindow','left=50,top=190,width=488,height=520,toolbar=0, resizable=yes, scrollbars=yes','true');		
		}
	}
}
</SCRIPT>
</head>

<form action="metaGroup.do" method="post">
<input name="method" type="hidden" value="display">
<input name="selectedgroupId" type="hidden" value=""> 

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);populate();" onunload="call()">
<div id="Group" style="position:absolute; left:20px; top:20px; width:525px; height:430px; border:2px outset;" color="#7E97AF">

<div id="Group" style="position:absolute;z-index:50;left:0px; top:0px; width:500px; height:10px;">

<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="500" border="0" cellspacing="1" cellpadding="0"  height="20">
	<thead>
		<tr>
			<td title='<fmt:message key="tooltip.sortGrId"/>' width="120px" height="20px" align="left"><b><fmt:message key="metagroup.groupId"/></b></td>
			<td title='<fmt:message key="tooltip.sortGrName"/>' width="280px" height="20px" align="left"><b><fmt:message key="metagroup.groupName"/></b></td>
			<td title='<fmt:message key="tooltip.sortTotalNoBooks"/>' width="100px" height="20px" align="left"><b><fmt:message key="metagroup.groupNameTotal"/></b></td>
		</tr>
	</thead>
</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:1px; width:520px; height:425px; overflowY:scroll;overflow-x:hidden; ">

<div id="Group" style="position:absolute;z-index:99;left:1px; top:20px; width:492px; height:10px;">
<table class="sort-table" id="groupListDetails" width="500" border="0" cellspacing="1" cellpadding="0" height="405">
	<tbody>
	<%
	int count = 0;
	%>
	<c:forEach items="${requestScope.groupListDetails}" var="group">
		<%
		if(count % 2 == 0){
		%>
			<tr class="even">
		<% } else { %>
			<tr class="odd">
		<%
		}
		count++;
		%>
			<input type="hidden" name="id.groupId" value="${group.id.groupId}"/>
			<td align="left" width="120"><c:out value="${group.id.groupId}"/>&nbsp;</td>
			<td align="left" width="280"><c:out value="${group.groupName}"/>&nbsp;</td>
			<td align="middle" width="100">
				<input type="hidden" name="noOfBookCode" value="${group.noOfBookCode}"/>
				<c:out value="${group.noOfBookCode}"/>&nbsp;
			</td>
		</tr>
	</c:forEach>
	</tbody>
	<tfoot><tr><td colspan="3"></td></tr></tfoot>
</table>
</div>
</div>
</div>

<div id="Group" style="position:absolute; left:469; top:463; width:70px; height:39px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			   <a tabindex="3" href=# onclick="javascript:openWindow(buildPrintURL('print','Metagroup Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
		    </td>

			<td align="right" id="Print">
				<a tabindex="3" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>

<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:454; width:525px; height:39px; visibility:visible;">

<div id="Group" style="position:absolute; left:6; top:2; width:150px; height:15px; visibility:visible;">
  <table width="140" border="0" cellspacing="2" cellpadding="0">
    <tr>
	  <td width="70px" id="bookcodebutton">
	  </td>

	  <td width="70px"><a tabindex="2" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('C');" onKeydown = "onKeyEnterSubmit('close',event);"><fmt:message key="button.close"/></a></td>
	</tr>
  </table>
</div>

<div style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:hidden;">  	
		<table width="140" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
		<tr>

			<td id="bookcodeenablebutton">		
				<a tabindex="1" title='<fmt:message key="tooltip.viewBookSelGroup"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildGrpBook('bookcodes'),'groupmaintenancebookcodesWindow','left=50,top=190,width=488,height=520,toolbar=0, resizable=yes, scrollbars=yes','true')" onKeydown = "onKeyEnterSubmit('bookcode',event);"><fmt:message key="button.books"/></a> 
			</td>
		 			<!-- start:modified the mantis 1549  by the sunil.the purpose to display the meaningful tooltip -->	
			<td id="bookcodedisablebutton">
				<a class="disabled" disabled="disabled"  title='<fmt:message key="tooltip.viewBookSelGroup"/>'><fmt:message key="button.books"/></a>
			</td>
                   <!-- end:modified the mantis 1549  by the sunil.the purpose to display the meaningful tooltip -->			 					
		</tr>
		</table> 
	  </div>
</div>

	<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
 
</form>
</body>
</html>  