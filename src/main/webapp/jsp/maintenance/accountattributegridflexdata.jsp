
<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>



<c:if test="${requestScope.showGridcontent == 'true'}">
<accountattribute accessInd="${accessInd}" selectedRow="${selectedRow}">

	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>
</c:if>
	<grid>
	<metadata>
			<columns>
			<c:forEach items="${requestScope.column_order}" var="order">
				  <c:choose>
				  <c:when test="${order == 'accountId'}">
				<column heading="<fmt:message key="label.accountattribute.accountId"/>"
				        clickable="true" 
				        draggable="true" 
				        filterable="true" 
				         sort="true" 
				        type="str" 
				        dataelement="accountId" 
				        width="${requestScope.column_width.accountId}"/>
				  </c:when>
					<c:when test="${order == 'effectivedateTime'}">
				<column heading="<fmt:message key="label.accountattribute.effectivedateTime"/>"
				        clickable="true" 
				        draggable="true" 
				        filterable="false"
				         sort="true"   
				        type="str" 
				        dataelement="effectivedateTime" 
				        width="${requestScope.column_width.effectivedateTime}"/>
					</c:when>
					  <c:when test="${order == 'value'}">
				<column heading="<fmt:message key="label.accountattribute.value"/>" 
				        clickable="true" 
				        draggable="true" 
				        filterable="false" 
				         sort="true" 
				        type="${accountHDRType}"
				        dataelement="value" 
				        width="${requestScope.column_width.value}"/>
					  </c:when>
					  <c:when test="${order == 'updateDateAsString'}">
				<column heading="<fmt:message key="label.accountattributedefinition.column.updated"/>"
				        clickable="true" 
				        draggable="true" 
				        filterable="false" 
				         sort="true" 
				        type="str" 
				        dataelement="updateDateAsString" 
				        width="${requestScope.column_width.updateDateAsString}"/>
					  </c:when>
					  <c:when test="${order == 'updateuser'}">
				<column heading="<fmt:message key="label.accountattributedefinition.column.user"/>"
				       clickable="true" 
				       draggable="true" 
				       filterable="false" 
				        sort="true" 
				       type="str" 
				       dataelement="updateuser" 
				       width="${requestScope.column_width.updateuser}"/>
					  </c:when>
				  </c:choose>
			</c:forEach>
			</columns>
		</metadata>
		<rows size="${recordCount}">
			<c:forEach var="accountAttributes" items="${requestScope.accountAttributes}">
				<row>
					<accountId clickable="false">${accountAttributes.accountId}</accountId>
					<effectivedateTime clickable="false">${accountAttributes.effectiveDateAsString}</effectivedateTime>
					<value clickable="false">${accountAttributes.valueAsString}</value>
					<updateDateAsString clickable="false">${accountAttributes.updateDateAsString}</updateDateAsString>
					<updateuser clickable="false">${accountAttributes.updateUser }</updateuser>
					<sequenceKey clickable="false">${accountAttributes.sequenceKey}</sequenceKey>
				</row>
			</c:forEach>
		</rows>						
	</grid>
<c:if test="${requestScope.showGridcontent == 'true'}">
</accountattribute>
</c:if>
