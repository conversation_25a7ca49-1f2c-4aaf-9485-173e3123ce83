<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title>
<c:choose>
    <c:when test="${'save' == requestScope.methodName}">
        <fmt:message key="bookmaintenance.addScreen"/>
    </c:when>
    <c:otherwise>
        <c:if test="${'change' == requestScope.methodName}">
            <fmt:message key="bookmaintenance.changeScreen"/>
        </c:if>
    </c:otherwise>
</c:choose>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "cancelbutton";
mandatoryFieldsArray = ["*"];
<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="displayList";
window.opener.document.forms[0].submit();
self.close();
</c:if>


var entityAccess = "${requestScope.EntityAccess}";

/**
* This method is used to submit the form when click the save button.
*
* @param methodName
* @return none
**/
function submitForm(methodName){

    var bookId=validateField(document.forms[0].elements['bookCode.id.bookCode'],'bookCode.id.bookCode','alphaNumPat');
	
	
	if(bookId)
	{
	
	  var bookName=validateField(document.forms[0].elements['bookCode.bookName'],'book.bookCodeadd','alphaNumPatExtended');
	  if(bookName)
	  {
	if(validateForm(document.forms[0]) ){
	enableFields();
	document.forms[0].method.value = methodName;	
	document.forms[0].submit();
	}
	}
	else
	{
	  alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");	
	  document.forms[0].elements['bookCode.bookName'].focus();
		  
	}
	}
	
	else
	{
	   document.forms[0].elements['bookCode.id.bookCode'].focus();
	}
}

function symbolicCharactersValidation(strField, strLabel,strPat) {
	var validText = false;
	if(validateField(strField,strLabel,strPat)){
		validText = true;
		}	
	if(validText == false){
	alert("<fmt:message key="interfacerulesmaintenance.alert.ruleValidation"/>");
	}
}
/* This Method is used for save or update the data and close the screen when enter key pressed */
function onKeyEnterSubmit(method,e){
	var event = (window.event|| e);
	 if(event.keyCode == 13){
	 if(method == "update"){
		submitForm('update');
		}else if(method == "save"){
		submitForm('save');
		}else if(method == "C"){
		confirmClose('C')
		}
	}
}

function enableFields(){
	document.forms[0].elements["bookCode.id.entityId"].disabled = "";
	document.forms[0].elements["bookCode.id.bookCode"].disabled = "";

}

function validateForm(objForm){
  var elementsRef = new Array(2);
  elementsRef[0] = objForm.elements["bookCode.id.bookCode"];
 /* code Added  by Nageswara Rao on 03_Jan_2012 for mantis 1580:" Spaces should not be saved to end of inputted values " */
 elementTrim(document.forms[0]);
  elementsRef[1] = objForm.elements["bookCode.bookName"];
  
  return validate(elementsRef);
}

function bodyOnLoad(){
	
	document.getElementById("entityName").innerText = '${entityName}';
	
	var divElement = document.getElementById("dropdowndiv_2");
	var selectElement = document.forms[0].elements["bookCode.groupIdLevel1"];
	var idElement = document.forms[0].elements["groupIdLevel1"];
	var descElement = document.forms[0].elements["groupName1"];	
	var arrowElement = document.forms[0].elements["dropdownbutton_2"];
	var idLength = 12;
	var descLength = 30;
	var groupLevel1Box = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);	

	divElement = document.getElementById("dropdowndiv_3");
	selectElement = document.forms[0].elements["bookCode.groupIdLevel2"];
	 idElement = document.forms[0].elements["groupIdLevel2"];
	 descElement = document.forms[0].elements["groupName2"];	
	 arrowElement = document.forms[0].elements["dropdownbutton_3"];	
	 var groupLevel2Box = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);	

	divElement = document.getElementById("dropdowndiv_4");
	 selectElement = document.forms[0].elements["bookCode.groupIdLevel3"];
	 idElement = document.forms[0].elements["groupIdLevel3"];
	 descElement = document.forms[0].elements["groupName3"];
	 arrowElement = document.forms[0].elements["dropdownbutton_4"];
	var groupLevel3Box = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);



}

</SCRIPT>
</head>

<form action="bookCode.do" onsubmit="validate(this);">
<input name="method" type="hidden" value="save">
<input name="oldValue" type="hidden" value= "${oldValue}">

<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');bodyOnLoad();" onunload="call()">

<div id="dropdowndiv_2" style="z-index:99;position:absolute;width:200px;left:154px;top:153px;visibility:hidden" 	class="swdropdown">
		<select class="htmlTextFixed" id="bookCode.groupIdLevel1" name="bookCode.groupIdLevel1" 	size="7"
		 style="width:329px;z-index:99;">
			<c:forEach items="${requestScope.groupLevel_1}" var="grpLvl">
               <option
                  value="${grpLvl.value}"
                  <c:if test="${bookCode.groupIdLevel1 == grpLvl.value}">selected="selected"</c:if>>
                  ${grpLvl.label}
               </option>
			</c:forEach>
		 </select>
</div>

<div id="dropdowndiv_3" style="z-index:99;position:absolute;width:200px;left:154px;top:178px;visibility:hidden" 	class="swdropdown">
			<select class="htmlTextFixed" id="bookCode.groupIdLevel2" name="bookCode.groupIdLevel2"
			size="7" style="width:329px;z-index:99;">
			<c:forEach items="${requestScope.groupLevel_2}" var="grpLvl">
               <option
                  value="${grpLvl.value}"
                  <c:if test="${bookCode.groupIdLevel2 == grpLvl.value}">selected="selected"</c:if>>
                  ${grpLvl.label}
               </option>
			</c:forEach>
			</select>

</div>

<div id="dropdowndiv_4" style="z-index:99;position:absolute;width:200px;left:154px;top:203px;visibility:hidden" 	class="swdropdown">
			<select class="htmlTextFixed" id="bookCode.groupIdLevel3" name="bookCode.groupIdLevel3"
			size="5" style="width:329px;z-index:99;">
			<c:forEach items="${requestScope.groupLevel_3}" var="grpLvl">
               <option
                  value="${grpLvl.value}"
                  <c:if test="${bookCode.groupIdLevel3 == grpLvl.value}">selected="selected"</c:if>>
                  ${grpLvl.label}
               </option>
			</c:forEach>
			</select>
</div>


<div id="BookCodeAdd" style="position:absolute; left:20px; top:20px; width:585px; height:198px; border:2px outset;" color="#7E97AF">
	<div id="BookCodeAdd" style="position:absolute;left:8px; top:6px; width:543px; height:124px;">
		<table width="580px" border="0" cellpadding="0" cellspacing="0">
			<tr height="24px">
			  <td width="93px"><b><fmt:message key="entity.id"/></b></td>
			  <td width="31px">&nbsp;</td>
			  <td width="121px">
				 <input type="text" name="bookCode.id.entityId" class="htmlTextAlpha"
				 value="${bookCode.id.entityId}" disabled="%{#attr.screenFieldsStatus}" style="width:120px;height:22px"/>
				 </td>
				  <td width="50px">&nbsp;</td>
				  <td width="280px">
				
				 <span id="entityName"   name="entityName" class="spantext"> 
			  </td>
			</tr>
		<tr height="24px">
			 <c:if test="${'save' == requestScope.methodName}">
			  <td width="93px"><b><fmt:message key="book.bookCodeadd"/></b>*</td>
			  <td width="31px">&nbsp;</td>
			 
			  <td width="121px">
				 <input type="text" name="bookCode.id.bookCode" tabindex="1" class="htmlTextAlpha"
				 value="${bookCode.id.bookCode}" titleKey="tooltip.EnterBookcodeID" maxlength="12" style="width:120px;height: 22px;"onchange="return validateField(this,'book.bookCodeadd','alphaNumPat');"/>
			  </td>
			  </c:if>

			  <c:if test="${'change' == requestScope.methodName}">
			   <td width="93px"><b><fmt:message key="book.bookCodeadd"/></b></td>
			  <td width="28px">&nbsp;</td>
			 
			  <td width="120px">
				 <input type="text" name="bookCode.id.bookCode" value="${bookCode.id.bookCode}"
				 class="htmlTextAlpha" disabled="%{#attr.screenFieldsStatus}" style="width:120px;height: 22px;"/>
			  </td>
			  </c:if>
			   <td width="50px">&nbsp;</td>
				  <td width="280px"></td>
			</tr>
			</table>
			<table width="411px" border="0" cellpadding="0" cellspacing="1">
			<tr height="24px">
			  <td width="93px"><b><fmt:message key="book.bookName"/></b>*</td>
			  <td width="27px">&nbsp;</td>
			  <td width="280px">
			  			  
			  <input type="text" name="bookCode.bookName" value="${bookCode.bookName}" tabindex="2"  class="htmlTextAlpha"
					 titleKey="tooltip.EnterBookcodeName" maxlength="30" style="width:280px;height: 22px;"
					 onchange="return symbolicCharactersValidation(this,'bookCode.bookName','alphaNumPatExtended');"/>
			  
			  </td>
			</tr>
			</table>

			<table width="249px" border="0" cellpadding="0" cellspacing="1">
			<tr height="24px">
			  <td width="95px"><b><fmt:message key="book.bookLocation"/></b></td>
			  <td width="28px">&nbsp;</td>
			  <td width="120px">
				 <select id="bookCode.bookLocation" name="bookCode.bookLocation" style="width:120px;"
				 titleKey="tooltip.selectBookLocation" tabindex="2">
					<c:forEach items="${requestScope.locationIdsColl}" var="location">
					   <option
						  value="${location.value}"
						  <c:if test="${bookCode.bookLocation == location.value}">selected="selected"</c:if>>
						  ${location.label}
					   </option>
					</c:forEach>
				 </select>
				 
			  </td>
			</tr>
			</table>
			
			<table width="593px" border="0" cellpadding="0" cellspacing="0">
		     <tr height="28px">
			   <td width="93px"><nobr><B>${groupLevelName1}</B></nobr></td>		  
			  <td width="28px">&nbsp;</td>
			  <td width="180px" style="padding-left: 15px">				
				     <input styleclass="textAlpha" name="groupIdLevel1" tabindex="2" style="width:120px;margin-right:10px;height: 23px;" title='<fmt:message key="tooltip.EnterGroupIDLvl1"/>' readonly><input tabindex="2" title='<fmt:message key="tooltip.ClickSelectGroupIDLvl1"/>' id="dropdownbutton_2" type="button" value="..." >
					 </td>
					   <td width="28px">&nbsp;</td>
					    <td width="280px">	
					<input styleclass="textAlpha" tabindex="-1" style="width:280px;background:transparent; border: thin;" readonly name="groupName1" size="20" >
			  </td>
				    
			  </td>
		   </tr>
			<tr height="28px">
			   <td width="93px"><nobr><B>${groupLevelName2}</B></nobr></td>		  
			  <td width="28px">&nbsp;</td>
			  <td width="180px" style="padding-left: 15px">
				  <input styleclass="textAlpha" name="groupIdLevel2" tabindex="2" style="width:120px;margin-right:10px;height: 23px;" title='<fmt:message key="tooltip.EnterGroupIDLvl2"/>' readonly><input tabindex="2" title='<fmt:message key="tooltip.ClickSelectGroupIDLvl2"/>' id="dropdownbutton_3" type="button" value="..." >
				  </td>
				   <td width="28px">&nbsp;</td>
				   <td width="280px">

					<input styleclass="textAlpha" tabindex="-1" style="width:280px;background:transparent; border: thin;" readonly name="groupName2" size="20" > 

			  </td>
		   </tr>
		 <tr height="28px">
			  <td width="93px"><nobr><B>${groupLevelName3}</B></nobr></td>		  
			  <td width="28px">&nbsp;</td>
 			  <td width="180px" style="padding-left: 15px">
			   <input styleclass="textAlpha" name="groupIdLevel3" tabindex="2" style="width:120px; margin-right:10px;height: 23px;" title='<fmt:message key="tooltip.EnterGroupIDLvl3"/>' readonly><input tabindex="2" title='<fmt:message key="tooltip.ClickSelectGroupIDLvl3"/>' id="dropdownbutton_4" type="button" value="..." >
			   </td>
			      <td width="28px">&nbsp;</td>
			   <td width="280px">
					<input styleclass="textAlpha" tabindex="-1" style="width:280px;background:transparent; border: thin;" name="groupName3" size="20" >
			  </td>
		   </tr>
		</table> 
	</div>
</div>
<div id="BookCodeAdd" style="position:absolute; left:530; top:233; width:70px; height:15px; visibility:visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
			<td align="Right">
			<c:if test="${'save' == requestScope.methodName}">
				  <a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Book Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
            </c:if>
             <c:if test="${'change' == requestScope.methodName}">
				  <a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Book Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
            </c:if>
		   </td>

			<td align="right" id="Print">
				<a onclick="printPage();" tabindex="8" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
			</td>
		</tr>
	</table>
</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:224px; width:585px; height:39px; visibility:visible;">
	<div id="BookCodeAdd" style="position:absolute; left:6; top:4; width:150px; height:15px; visibility:visible;">
		<table width="140px" border="0" cellspacing="1" cellpadding="0">

			<tr>
			<c:if test="${'save' == requestScope.methodName}">
			  <td width="70px"><a tabindex="6" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('save');" onKeydown = "onKeyEnterSubmit('save',event);"><fmt:message key="button.save"/></a></td>
			</c:if>
			<c:if test="${'change' == requestScope.methodName}">
			  <td width="70px"><a tabindex="6" title='<fmt:message key="tooltip.save"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('update');" onKeydown = "onKeyEnterSubmit('update',event);"><fmt:message key="button.save"/></a></td>
			</c:if>

			  <td id="cancelbutton" width="70px"><a tabindex="7" title='<fmt:message key="tooltip.cancel"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');" onKeydown = "onKeyEnterSubmit('C',event);"><fmt:message key="button.cancel"/></a></td>
			</tr>
			
		</table>
	</div>
</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
</form>
</body>
</html>