<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page import="org.swallow.maintenance.model.ILMScenario"%>
<%@page import="org.swallow.maintenance.model.ILMAccountGroup"%>
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="ilmScenario.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">

	var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
	
	/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;
	
	/**
	 * Called on first load of the screen
	 *
	 **/
	function bodyOnLoad()
	{
		xl = new XLSheet("ilmScenariosDetails","table_2", ["String","String","String","String", "String", "String","String","String"],"********");
		
		xl.onsort = xl.onfilter = disableButtons;
		
		highlightTableRows("ilmScenariosDetails");
		
	
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		
		var dropBox1 = new SwSelectBox(document.forms[0].elements["ilmScenario.entityId"],document.getElementById("entityName"));
		var dropBox2 = new SwSelectBox(document.forms[0].elements["ilmScenario.currencyCode"],document.getElementById("currencyDesc"));
		
		document.forms[0].selectedEntityId.value = document.forms[0].elements["ilmScenario.entityId"].value;
		document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["ilmScenario.currencyCode"].value;
		
		var buttonStatus = "<%= request.getAttribute(SwtConstants.ADD_BUT_STS) %>";	
		document.getElementById("addbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("addenablebutton").innerHTML:document.getElementById("adddisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.CHG_BUT_STS) %>";	
		document.getElementById("changebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("changeenablebutton").innerHTML:document.getElementById("changedisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.VIEW_BUT_STS) %>";	
		document.getElementById("viewbutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("viewenablebutton").innerHTML:document.getElementById("viewdisablebutton").innerHTML);	
	
		 buttonStatus = "<%= request.getAttribute(SwtConstants.DEL_BUT_STS) %>";	
		document.getElementById("deletebutton").innerHTML = (buttonStatus=="<%=SwtConstants.STR_TRUE%>"?document.getElementById("deleteenablebutton").innerHTML:document.getElementById("deletedisablebutton").innerHTML);	
	
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	}
	
	/**
	 * Dispatched when the user selects a row in the grid
	 *
	 **/
	function selectTableRow(e)
	{
        var event = (window.event|| e);
		var target = (event.srcElement || event.target)
		var srcEl = target;
		while(srcEl.tagName != 'TD')
		{
			srcEl = srcEl.parentElement;
		}
		
		var rowElement = srcEl.parentElement;
		var tblElement = srcEl.parentElement.parentElement.parentElement;
	
		var isRowSel = isRowSelected(rowElement);
		resetTableRowsStyle(tblElement);
		
		if(isRowSel == false)
			rowElement.className = 'selectrow' ;
	
		onSelectTableRow(rowElement,!isRowSel);
	}
	
	/**
	 * Disable buttons and colors when the user does not select any row in the grid
	 *
	 **/
	function disableButtons(){
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		updateColors();
	}
	
	/**
	 * Triggered when the user deletes a scenario via delete button
	 * @ param methodName
	 **/
	function submitDeleteForm(methodName){
		document.forms[0].selectedEntityId.value = document.forms[0].elements["ilmScenario.entityId"].value;
		document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["ilmScenario.currencyCode"].value;
			document.forms[0].method.value = methodName;	
			var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
			if (yourstate==true) 	
				document.forms[0].submit();
	}
	

	/**
	 * Build URL wich will open the Add/Change the ILAAP Scenario details screens for ILM monitoring
	 */

	function buildAddScenarioURL(methodName) {

		var param = 'ilmTransScenario.do?method=' + methodName;
			param = param + "&selectedEntityId="
					+ document.forms[0].selectedEntityId.value.trim()
					+ "&selectedEntityName="
					+ document.getElementById('entityName').innerText
					+ "&selectedCurrencyCode="
					+ document.forms[0].selectedCurrencyCode.value.trim()
					+"&parentScreen=ilmScenarioMaintenance"
			if(methodName != "addScenario")
				param = param + "&selectedScenarioId="
				+ document.forms[0].selectedScenarioId.value.trim();
		return param;
	}
	
	/**
	 * This function is called when the user selects a row, it will change the visual of buttons
	 * @ rowElement
	 * @ isSelected 
	 **/
	function onSelectTableRow(rowElement , isSelected)
	{
		var hiddenElement = rowElement.getElementsByTagName("input")[0];
		var hiddenElement2 = rowElement.getElementsByTagName("input")[1];
		var hiddenElement3 = rowElement.getElementsByTagName("input")[2];
	
		document.forms[0].selectedScenarioId.value = rowElement.cells[2].innerText.trim();
		var publicPrivate = rowElement.cells[6].innerText.trim();
		var isSystem = rowElement.cells[5].innerText.trim();
		document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
		
		var appName = "<%=SwtUtil.appName%>";
		var requestURL = new String('<%=request.getRequestURL()%>');
		var idy = requestURL.indexOf('/'+appName+'/');
		requestURL=requestURL.substring(0,idy+1) ; 
		requestURL = requestURL + appName+"/ilmTransScenario.do?method=getEntityCurrencyAccessForScenario"
		requestURL = requestURL + "&entityId=" + hiddenElement3.value;
		requestURL = requestURL + "&currencyCode=" + hiddenElement2.value;
		requestURL = requestURL + "&userId=" + hiddenElement.value;
		requestURL = requestURL + "&publicPrivate=" + publicPrivate;
		var oXMLHTTP = new XMLHttpRequest();
		oXMLHTTP.open( "POST", requestURL, false );
		oXMLHTTP.send();
		menuEntityCurrGrpAccess=new String(oXMLHTTP.responseText);
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		
	if (document.forms[0].menuAccessId.value == "0") {
		
			document.getElementById("viewbutton").innerHTML = (isSelected ? document
					.getElementById("viewenablebutton").innerHTML
					: document.getElementById("viewdisablebutton").innerHTML);
			
			document.getElementById("changebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" ? document
					.getElementById("changeenablebutton").innerHTML : document
					.getElementById("changedisablebutton").innerHTML);
			
			document.getElementById("deletebutton").innerHTML = (isSelected
					&& menuEntityCurrGrpAccess == "0" && isSystem =="N" ? document
					.getElementById("deleteenablebutton").innerHTML : document
					.getElementById("deletedisablebutton").innerHTML);
			
		} else if (document.forms[0].menuAccessId.value == "1" && isSelected)
			document.getElementById("viewbutton").innerHTML = document
					.getElementById("viewenablebutton").innerHTML;
	}

	function submitForm(methodName) {
		if (methodName == "listScenariosByEntity")
			document.forms[0].elements["ilmScenario.currencyCode"].value = "All";
		if (methodName == "listScenariosByEntity"
				|| methodName == "listScenariosByCcy")
			methodName = "listScenarios";
		document.forms[0].method.value = methodName;
		document.forms[0].submit();

	}
</SCRIPT>
</head>

<form action="ilmTransScenario.do">
	<input name="method" type="hidden" value="">
	<input name="selectedEntityId" type="hidden">
	<input name="selectedCurrencyCode" type="hidden">
	<input name="selectedScenarioId" type="hidden">
	<input name="menuAccessId" type="hidden" >

<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);" onunload="call()">
<div id="ILMScenarioMaintenance"
		style="position: absolute; left: 20px; top: 20px; width: 1139px; height: 62px; border: 2px outset;"
		color="#7E97AF">
	<div
		style="position: absolute; left: 8px; top: 4px; width: 705px; height: 70px;">
	<table width="690px" border="0" cellpadding="0" cellspacing="0"
		height="50">
 	<tr color="black" border="0">
 	  <td width="65"><b><fmt:message key="accountGroup.entityId"/></b></td>
 	  <td width="28">&nbsp;</td>
 	  				<td width="150px" ><select
					name="ilmScenario.entityId"
					onchange="submitForm('listScenariosByEntity');"
					titleKey="tooltip.selectEntity" style="width:140px" tabindex="1">
    		<c:forEach items="${requestScope.entities}" var="entity">
               <option
                  value="${entity.value}"
                  <c:if test="${ilmScenario.entityId == entity.value}">selected="selected"</c:if>>
                  ${entity.label}
               </option>
			</c:forEach>
				</select></td>
 	   <td width="20">&nbsp;</td>
 	  <td width="280">
 		<span id="entityName" name="entityName" class="spantext">
 	   </td>
 	</tr>
 	 
 	<tr>
			<td width="60"><b><fmt:message key="matchQuality.currencyCode" /></b></td>
			<td width="28">&nbsp;</td>
				<td width="150"><select
					name="ilmScenario.currencyCode"
					titleKey="tooltip.selectCurrencyCode" style="width:140px;"
					onchange="submitForm('listScenariosByCcy')" tabindex="2">
    		<c:forEach items="${requestScope.currencyList}" var="ccy">
               <option
                  value="${ccy.value}"
                  <c:if test="${ilmScenario.currencyCode == ccy.value}">selected="selected"</c:if>>
                  ${ccy.label}
               </option>
			</c:forEach>
				</select></td>

			<td width="20">&nbsp;</td>
			<td width="432"><span id="currencyDesc" class="spantext"></td>
	</tr>
 </table>
</div>
</div>
	<div id="IlmScenario" style="position:absolute; border:2px outset; left:20px; top:88px; width:1139px; height:395;">
		<div id="IlmScenario" style="position:absolute;z-index:99;left:0px; top:0px; width:1119; height:10px;">
			<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1025" border="0" cellspacing="1" cellpadding="0">
				<thead>
					<tr height="22px">
						<td width="100" align="left" title='<fmt:message key="tooltip.scenarioEntity"/>'><b><fmt:message key="accountGroup.entityId"/></b></td>
						<td width="70" align="left" title='<fmt:message key="tooltip.scenarioCcy"/>'><b><fmt:message key="ilmScenario.currencyCode"/></b></td>
						<td width="200" align="left" title='<fmt:message key="tooltip.scenarioID"/>'><b><fmt:message key="ilmScenario.id"/></b></td>
						<td width="150" align="left"  title='<fmt:message key="tooltip.scenarioName"/>'><b><fmt:message key="ilmScenario.scenarioName"/></b></td>
						<td width="300" align="left" title='<fmt:message key="tooltip.scenarioDescriptions"/>'><b><fmt:message key="ilmScenario.scenarioDescription"/></b></td>
						<td width="90" align="left" title='<fmt:message key="scenario.system"/>'><b><fmt:message key="scenario.system"/></b></td>
						<td width="100" align="left" title='<fmt:message key="tooltip.scenarioPublicPrivate"/>'><b><fmt:message key="ilmScenario.publicPrivate"/></b></td>
						<td width="100" align="left" title='<fmt:message key="tooltip.scenarioCreator"/>'><b><fmt:message key="ilmScenario.createdByUser"/></b></td>
					</tr>
				</thead>
			</table>
		</div>
		<div id="ddscrolltable"  style="position:absolute; left:0px; top:2px; width:1136px; height:390">
			<div id="IlmScenario" style="position:absolute;z-index:99;left:0px; top:22px; width:1025px; height:10px;">
				<table class="sort-table" id="ilmScenariosDetails" width="1025px" border="0" cellspacing="1" cellpadding="0" height="365">
		<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="scenario" items="${requestScope.ilmScenariosDetails}">
				<tr height="20px" class="${count % 2 == 0 ? 'even' : 'odd'}">
					<input type="hidden" name="createdByUser " value="${scenario.createdByUser }" disabled="true"/>
					<input type="hidden" name="currencyCode" value="${scenario.currencyCode}" disabled="true"/>
					<input type="hidden" name="entityId" value="${scenario.entityId}" disabled="true"/>
					<input type="hidden" name="id.ilmScenarioId" value="${scenario.id.ilmScenarioId}" disabled="true"/>
					<td width="100" align="left">${scenario.entityId}&nbsp;</td>
					<td width="70" align="left">${scenario.currencyCode}&nbsp;</td>
					<td width="200" align="left">${scenario.id.ilmScenarioId}&nbsp;</td>
					<td width="150" align="left">${scenario.ilmScenarioName}&nbsp;</td>
					<td width="300" align="left">${scenario.ilmScenarioDescription}&nbsp;</td>
					<td width="90" align="left">${scenario.systemScenario}&nbsp;</td>
					<td width="100" align="left">${scenario.publicPrivate}&nbsp;</td>
					<td width="100" align="left">${scenario.createdByUser }&nbsp;</td>
				</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
		</tbody>
					<tfoot>
						<tr><td colspan="8" ></td></tr>
					</tfoot>
				</table>
			</div>
		</div>
		<div id="IlmScenario" style="position:absolute; left:1064; top:415; width:70px; height:29px; visibility:visible;">
			<table width="60px" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td align="Right">
						<a title='<fmt:message key="tooltip.helpScreen"/>' tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','ILM Scenario Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" ></a> 
					</td>
					<td align="right" id="Print">&nbsp;
						<a tabindex="9" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>	
					</td>
				</tr>
			</table>
		</div>
		</div>
		<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:495;  width:1139px; height:39px; visibility:visible;">
			<div id="ScenarioMaintenance" style="position:absolute; left:2; top:4; width:945px; height:15px; visibility:visible;">
				<table width="410" border="0" cellspacing="0" cellpadding="0" height="20">
					<tr>
						<td id="addbutton"></td>
						<td id="changebutton"></td>
						<td id="viewbutton"></td>
						<td id="deletebutton"></td>
						<td id="closebutton" width="70px">		
							<a title='<fmt:message key="tooltip.close"/>' tabindex="7" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>			
						</td>
					</tr>
				</table>
			</div>
			<div style="position:absolute; left:6; top:4; width:654px; height:15px; visibility:hidden; display:none;">  	
				<table width="350" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden;">
					<tr>
			            <td id="addenablebutton">		
							<a title='<fmt:message key="tooltip.addScenario"/>' tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('addScenario'),'addScenario','left=50,top=190,width=920,height=700,toolbar=0, resizable=yes scrollbars=yes','true')"><fmt:message key="button.add"/></a>
						</td>				
						<td id="adddisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
						</td>
						<td id="changeenablebutton">		
							<a title='<fmt:message key="tooltip.changeScenario"/>' tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('changeScenario'),'scenariochange','left=50,top=190,width=920,height=700,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
						</td>		
						<td id="changedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
						</td>
						<td id="viewenablebutton">		
							<a  title='<fmt:message key="tooltip.viewScenario"/>' tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddScenarioURL('viewScenario'),'scenarioview','left=50,top=190,width=920,height=700,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.view"/></a>
						</td>		
						<td id="viewdisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.view"/></a>
						</td>
						<td id="deleteenablebutton">		
							<a  title='<fmt:message key="tooltip.deleteScenario"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" 	onClick="javascript:submitDeleteForm('deleteScenario');" ><fmt:message key="button.delete"/></a>
						</td>		
						<td id="deletedisablebutton">
							<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
						</td>
						
					</tr>
				</table>
			  </div>
			</div>
		
		<blockquote>&nbsp;</blockquote>
		<p>&nbsp;</p>
</body>
</form>
</html>            