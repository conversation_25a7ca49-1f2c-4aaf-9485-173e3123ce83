<?xml version="1.0" encoding="UTF-8"?>
<!--
  - The main purpose of this jsp file is to load the resultant xml data for Forecast Monitor Template screen.
  - 
  - Author(s): Vivekanandan A
  - Date: 24-05-2011
  -->
  
<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>



	<forecastmonitor currentuserid="${requestScope.currentUserId}">
		<request_reply>
			<status_ok>${requestScope.reply_status_ok}</status_ok>
			<message>${requestScope.reply_message}</message>
		<location />
	</request_reply>

					<grid>
						<metadata>
							<columns>
								<column heading="<fmt:message key="label.forecasttemplate.column.templateid"/>" draggable="false" filterable="true" type="str" dataelement="templateid" width="156"/>
								<column heading="<fmt:message key="label.forecasttemplate.column.templatename"/>" draggable="false" filterable="true"  type="str" dataelement="templatename" width="153"/>
								<column heading="<fmt:message key="label.forecasttemplate.column.userid"/>" draggable="false" filterable="true" type="str" dataelement="userid" width="156"/>
								<column heading="<fmt:message key="label.forecasttemplate.column.public"/>" draggable="false" filterable="true" type="str" dataelement="ispublic" width="132"/>
							</columns>
						</metadata>
						<rows size="${recordCount}">
							<c:forEach var="collMonitorTemplateDetails" items="${requestScope.collMonitorTemplateDetails}">
								<row>
									<templateid clickable="false">${collMonitorTemplateDetails.id.templateId}</templateid>
									<templatename clickable="false">${collMonitorTemplateDetails.templateName}</templatename>
									<userid clickable="false">${collMonitorTemplateDetails.userId}</userid>
									<ispublic clickable="false">${collMonitorTemplateDetails.publicTemplate}</ispublic>
								</row>
							</c:forEach>
						</rows>						
					</grid>					
				</forecastmonitor>
