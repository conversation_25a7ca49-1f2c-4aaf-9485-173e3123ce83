<!doctype HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><c:if test="${requestScope.methodName == 'save'}">
	<fmt:message key="correspondentaccountmaintenance.title.addWindow" />
</c:if><c:if test="${requestScope.methodName == 'modify'}">
	<fmt:message key="correspondentaccountmaintenance.title.changeWindow" />
</c:if></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
	var selectedCurrencyCode = '${requestScope.selectedCurrencyCode}';
	<c:if test="${requestScope.parentFormRefresh == 'yes'}">
	window.opener.document.forms[0].selectedCurrencyCode.value = selectedCurrencyCode;
	window.opener.document.forms[0].method.value = "display";
	window.opener.document.forms[0].submit();
	self.close();
	</c:if>
</SCRIPT>
	<SCRIPT language="JAVASCRIPT">
		function populateDropBoxes(){

	bodyOnLoad();
	var divElement = document.getElementById("dropdowndiv_1");
	var selectElement = document.forms[0].elements["correspondentAcct.id.messageType"];
	var idElement = document.forms[0].elements["messageType"];
	var descElement ="";
	var arrowElement = document.forms[0].elements["dropdowndiv_1"];
	var idLength = 12;
	var descLength = 30;
	var messageTypeBox = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	
	var divElement = document.getElementById("dropdowndiv_3");
	var selectElement = document.forms[0].elements["correspondentAcct.accountId"];
	var idElement = document.forms[0].elements["accountId"];
	var descElement = document.forms[0].elements["accountDesc"];
	var arrowElement = document.forms[0].elements["dropdowndiv_3"];
	var idLength = 12;
	var descLength = 30;
	var accountIdBox = new SwMainSelectBox(divElement,selectElement,idElement,descElement,arrowElement,idLength,descLength);
	ShowErrMsgWindow('${actionError}');
	changeBorderColor();
	$(document).on('change', '#accountDropDown', function() {
	      changeBorderColor();
	});


	}
function getRadioButtonValue(button){
	var d = button;
	var len = d.length;
	for(i=0;i<len;i++)
	{
		if(d[i].checked)
		{
		 return d[i].value;
		}
	}
	if(i == len)
	 return "null";
	}

function bodyOnLoad() {
	document.getElementById("entityDesc").innerText = '${entityName}';

	document.getElementById("correspondentAcct.id.entityId").value = '${entityId}';

	var dropBox1 = new SwSelectBox(document.forms[0].elements["correspondentAcct.id.currencyCode"], document.getElementById("currencyName"));
	document.getElementById("currencyName").innerText = '${currencyName}';
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if (searchMessageType == "P") {
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
	} else if (searchMessageType == "M") {
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
	}

	var copyValues = '${requestScope.copyActualValues}';
	<c:if test="${requestScope.copyActualValues == 'Y'}">
	document.forms[0].actualCurrencyCode.value = document.forms[0].elements["correspondentAcct.id.currencyCode"].value;
	document.forms[0].actualCorresAccId.value = document.forms[0].elements["correspondentAcct.id.corresAccId"].value;
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if (searchMessageType == "M") {
		document.forms[0].actualMessageType.value = document.forms[0].elements["correspondentAcct.otherMessageType"].value;
	} else if (searchMessageType == "P") {
		document.forms[0].actualMessageType.value = document.forms[0].elements["correspondentAcct.id.messageType"].value;
	}
	document.forms[0].actualAccountId.value = document.forms[0].elements["correspondentAcct.accountId"].value;
	</c:if>
	<c:if test="${requestScope.copyActualValues == 'N'}">
	document.forms[0].actualCurrencyCode.value = '${requestScope.actualCurrencyCode}';
	document.forms[0].actualCorresAccId.value = '${requestScope.actualCorresAccId}';
	document.forms[0].actualMessageType.value = '${requestScope.actualMessageType}';
	document.forms[0].actualAccountId.value = '${requestScope.actualAccountId}';
	</c:if>

}

		/**
		 * submitForm
		 *
		 * This method is used to submit form while clicking on the save/update button.
      */

function submitForm (methodName) {
/* code Added  by Nageswara Rao on 04_Jan_2012 for mantis 1580: "Spaces should not be saved to  end of inputted values " */
elementTrim(document.forms[0]);
	if (validate()) {
		document.forms[0].method.value = methodName;
		document.forms[0].selectedEntityId.value = document.forms[0].elements["correspondentAcct.id.entityId"].value;
		document.forms[0].submit();
	}
}

function modifyCorrespondentAccount(methodName) {
	if (validate()) {
		document.forms[0].method.value = methodName;
		document.forms[0].selectedEntityId.value = document.forms[0].elements["correspondentAcct.id.entityId"].value;
		document.forms[0].submit();
	}
}

function refreshScreen(methodName){
	document.forms[0].selectedMessageType.value = document.forms[0].elements['correspondentAcct.id.messageType'].value;
	document.forms[0].selectedEntityId.value = document.forms[0].elements['correspondentAcct.id.entityId'].value;
	document.forms[0].selectedCorresAccId.value = document.forms[0].elements['correspondentAcct.id.corresAccId'].value;
	document.forms[0].oldMethod.value = "${requestScope.methodName}";
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function validate () {
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if(searchMessageType == "P")
	{
		var messageType = document.forms[0].elements["correspondentAcct.id.messageType"].value.trim();
		if(messageType == null || messageType=="") {
			alert("<fmt:message key="correspondentaccountmaintenance.alert.messageType"/>");
			return false;
		}
	}
	else if(searchMessageType == "M")
	{
		var otherMessageType = document.forms[0].elements["correspondentAcct.otherMessageType"].value.trim();
		if(otherMessageType == null || otherMessageType == "") {
			alert("<fmt:message key="correspondentaccountmaintenance.alert.messageType"/>");
			return false;
		}
	}

	
	var currencyCode = document.forms[0].elements["correspondentAcct.id.currencyCode"].value.trim();
	if( currencyCode == null || currencyCode == "" || currencyCode == 'All') {
		alert("<fmt:message key="correspondentaccountmaintenance.alert.currencyCode"/>");
		return false;
	}
	var corresAccId = document.forms[0].elements["correspondentAcct.id.corresAccId"].value.trim();
	if (corresAccId == null || corresAccId == "") {
		alert ("<fmt:message key="correspondentaccountmaintenance.alert.corresAccId"/>");
		return false;
	}
	var accountId = document.forms[0].elements["correspondentAcct.accountId"].value.trim();
	if (accountId == null || accountId == "") {
		alert ("<fmt:message key="correspondentaccountmaintenance.alert.accountId"/>");
		return false;
	}
	return true;
}

function showOtherMessageType(){
	var searchMessageType = getRadioButtonValue(document.forms[0].elements['correspondentAcct.searchMessageType']);
	if(searchMessageType == "P")
	{
		document.forms[0].elements["correspondentAcct.otherMessageType"].value = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "";
		document.getElementById("manualMsgTypeDiv").style.display = "none";
	}
	else if(searchMessageType == "M")
	{
		var txt = document.getElementById("messageTypeText");
		txt.value = "";
		document.forms[0].elements["correspondentAcct.id.messageType"].value = "";
		document.getElementById("manualMsgTypeDiv").style.display = "";
		document.getElementById("predefinedMsgTypeDiv").style.display = "none";
	}
}

function changeBorderColor() {
	 var element_ = document.getElementById('accountId');
	   if(element_!=null&&typeof element_!='undefined'){
		   element_.style.border = (element_.value==""?"red 1px solid":"#a9a9a9 1px solid");
	   }
   }
</SCRIPT>
</head>

<form action="correspondentacctmaintenance.do">
	<input name="method" type="hidden">
	<input name="oldMethod" type="hidden">
	<input name="selectedAccountId" type="hidden">
	<input name="selectedMessageType" type="hidden">
	<input name="selectedCurrencyCode" type="hidden">
	<input name="selectedCorresAccId" type="hidden">
	<input name="selectedEntityId" type="hidden" >
	<input name="actualMessageType" type="hidden">
	<input name="actualCurrencyCode" type="hidden">
	<input name="actualCorresAccId" type="hidden">
	<input name="actualAccountId" type="hidden">
	<input name="copyActualValues" type="hidden">
	
	<div id="dropdowndiv_1"
		style="position: absolute; width: 162px; left: 242px; top: 111px; visibility: hidden; z-index: 99"
		class="swdropdown"><select class="htmlTextFixed" id="correspondentAcct.id.messageType"
		name="correspondentAcct.id.messageType" size="10" style="width:162px;z-index:99"
		onchange="showOtherMessageType();">
			<c:forEach items="${requestScope.messagetypes}" var="type">
               <option
                  value="${type.value}"
                  <c:if test="${correspondentAcct.id.messageType == type.value}">selected="selected"</c:if>>
                  ${type.label}
               </option>
			</c:forEach>
	    </select></div>

	<div id="dropdowndiv_3"
		style="position: absolute; width: 240px; left: 242px; bottom: 124px; visibility: hidden; z-index: 99"
		class="swdropdown"><select id="accountDropDown" class="htmlTextFixed"  name="correspondentAcct.accountId"
		size="10" style="width:240px;bottom: 177px;z-index:99">
			<c:forEach items="${requestScope.accounts}" var="acct">
               <option
                  value="${acct.value}"
                  <c:if test="${correspondentAcct.accountId == acct.value}">selected="selected"</c:if>>
                  ${acct.label}
               </option>
			</c:forEach>
			</select></div>
	<body leftmargin="0" topmargin="0" marginheight="0"
		onLoad="setParentChildsFocus();setFocus(document.forms[0]);populateDropBoxes();"
		onunload="call()" style="overflow: hidden;">
	<div id="correspondentAcctAdd"
		style="position: absolute; left: 20px; top: 15px; width: 810px; height: 195px; border: 2px outset;"
		color="#7E97AF">
	<div
		style="position: absolute; left: 8px; top: 4px; width: 750px; height: 70px;">
	<table width="805px" border="0" cellpadding="0" cellspacing="0"
		height="175">
		
		<tr height="25">
			<td width="195px"><b><fmt:message key="correspondentaccountmaintenance.add.entityId" /></b></td>
			<td width="20px">&nbsp;</td>
			<td width="530px" colspan="2"><input type="text" name="correspondentAcct.id.entityId" class="htmlTextAlpha"
				value="${correspondentAcct.id.entityId}"
				titleKey="tooltip.entityId" style="width:160px;height: 22px;" disabled="true"  />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<span id="entityDesc" class="spantext"></span></td>
		</tr>
		
		<tr height="25">
			<td width="195px"><b><fmt:message key="correspondentaccountmaintenance.add.messageType" /></b></td>
			<!-- Code Modified by Sudhakar on 24-Jun-2011 for Mantis 1349:Increase Account name field in some screens -->	
			<td width="600px" colspan="3">
				<table align="left">
					<tr>
					<td width="13px">&nbsp;</td>
						<td width="120px">
							<c:if test="${requestScope.methodName == 'save'}">
								<input type="radio"  id="6" name="correspondentAcct.searchMessageType"
								${correspondentAcct.searchMessageType == 'P' ? 'checked="checked"' : ''} value="${'P'}" style="width:13;" titleKey="correspondentaccountmaintenance.predefinedMessageType"
								onclick="showOtherMessageType();" tabindex="1"  />
							</c:if>
							<c:if test="${requestScope.methodName == 'modify'}">
								<input type="radio"  disabled="true" id="6" name="correspondentAcct.searchMessageType"
								${correspondentAcct.searchMessageType == 'P' ? 'checked="checked"' : ''} value="${'P'}" style="width:13;" titleKey="correspondentaccountmaintenance.predefinedMessageType"
								onclick="showOtherMessageType();" tabindex="1"  />
							</c:if>
							<label title='<fmt:message key="correspondentaccountmaintenance.predefinedMessageType"/>'><fmt:message key="correspondentaccountmaintenance.predefinedMessageType"/></label>
						</td>
						<td width="20px">&nbsp;</td>
						<td width="120px">
							<c:if test="${requestScope.methodName == 'save'}">
							<input type="radio" id="6" name="correspondentAcct.searchMessageType"
							${correspondentAcct.searchMessageType == 'M' ? 'checked="checked"' : ''} value="${'M'}" style="width:13;" titleKey="correspondentaccountmaintenance.manualMessageType"
							onclick="showOtherMessageType();" tabindex="2"  />
							</c:if>
							<c:if test="${requestScope.methodName == 'modify'}">
							<input type="radio" disabled="true" id="6" name="correspondentAcct.searchMessageType"
							${correspondentAcct.searchMessageType == 'M' ? 'checked="checked"' : ''} value="${'M'}" style="width:13;" titleKey="correspondentaccountmaintenance.manualMessageType"
							onclick="showOtherMessageType();" tabindex="2"  />
							</c:if>
							<label title='<fmt:message key="correspondentaccountmaintenance.manualMessageType"/>'><fmt:message key="correspondentaccountmaintenance.manualMessageType"/></label>
						</td>
						<td width="270px">&nbsp;</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr height="25">
			<td width="195px">&nbsp;</td>
			
			<td width="600px" colspan="3">
				<table align="left">
					<tr>
					<td width="13px">&nbsp;</td>
					<td colspan="4">
						<div id="predefinedMsgTypeDiv" style="height:25;top:60">
							<c:if test="${requestScope.methodName == 'save'}">
							<input readonly="readonly" id="messageTypeText" name="messageType" class="textAlpha" style="width: 160px;height: 22px;" title='<fmt:message key = "tooltip.selectMessageType"/>'> 
							<input id="dropdowndiv_1" tabindex="3" title='<fmt:message key = "tooltip.selectMessageType"/>'	type="button" value="...">
							</c:if>
							<c:if test="${requestScope.methodName == 'modify'}">
							<input readonly="readonly" id="messageTypeText" name="messageType" class="textAlpha" style="width: 160px;height: 22px;" title='<fmt:message key = "tooltip.selectMessageType"/>' disabled="disabled"> 
							<input id="dropdowndiv_1" tabindex="3" title='<fmt:message key = "tooltip.selectMessageType"/>'	type="button" value="..." disabled="disabled">
							</c:if>
						</div>
						<div id="manualMsgTypeDiv" style="height:25;top:60">
						<c:if test="${requestScope.methodName == 'save'}">
						<input type="text" name="correspondentAcct.otherMessageType"  value="${correspondentAcct.otherMessageType}" class="htmlTextAlpha"
						titleKey="tooltip.messageType" style="width:160px;height: 22px;" tabindex="3" maxlength="30" />
						</c:if>
						<c:if test="${requestScope.methodName == 'modify'}">
						<input type="text" name="correspondentAcct.otherMessageType"   value="${correspondentAcct.otherMessageType}" class="htmlTextAlpha"
						titleKey="tooltip.messageType" style="width:160px;height: 22px;" tabindex="3" maxlength="30" disabled="true" />
						</c:if>
						</div>
					</td>
					</tr>
				</table>
			</td>
		</tr>
		
		<tr height="25">
			<td width="195px"><b><fmt:message key="correspondentaccountmaintenance.add.currencyCode" /></b></td>
			<td width="20px">&nbsp;</td>
			
			<td width="600px" height="25" colspan="2">
			<c:if test="${requestScope.methodName == 'save'}">
			<select id="correspondentAcct.id.currencyCode" name="correspondentAcct.id.currencyCode"
			tabindex="4" style="width:60px" onchange="refreshScreen('refreshAccountList');"
			titleKey="tooltip.selectCurrencyId">
			<c:forEach items="${requestScope.currencies}" var="ccy">
               <option
                  value="${ccy.value}"
                  <c:if test="${correspondentAcct.id.currencyCode == ccy.value}">selected="selected"</c:if>>
                  ${ccy.label}
               </option>
			</c:forEach>
			</select>
			</c:if>
			<c:if test="${requestScope.methodName == 'modify'}">
			<select id="correspondentAcct.id.currencyCode" name="correspondentAcct.id.currencyCode"
			tabindex="4" style="width:60px" onchange="refreshScreen('refreshAccountList');"
			titleKey="tooltip.selectCurrencyId" disabled="true">
			<c:forEach items="${requestScope.currencies}" var="ccy">
               <option
                  value="${ccy.value}"
                  <c:if test="${correspondentAcct.id.currencyCode == ccy.value}">selected="selected"</c:if>>
                  ${ccy.label}
               </option>
			</c:forEach></select>
			</c:if>
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span id="currencyName" class="spantext"></span></td>
		</tr>
		<tr height="25">
			<td width="195px"><b><fmt:message key="correspondentaccountmaintenance.add.correspondentAccID" /></b></td>
			<td width="20px">&nbsp;</td>
			
			<td width="600px" colspan="2">
			<c:if test="${requestScope.methodName == 'save'}">
			<input type="text" name="correspondentAcct.id.corresAccId" tabindex="5"  value="${correspondentAcct.id.corresAccId}"
				style="width:255px;height: 22px;" titleKey="tooltip.enterCorrespondentAcctId" maxlength="35" />
			</c:if>
			<c:if test="${requestScope.methodName == 'modify'}">
			<input type="text" name="correspondentAcct.id.corresAccId" tabindex="5"  value="${correspondentAcct.id.corresAccId}"
				style="width:255px;height: 22px;" titleKey="tooltip.enterCorrespondentAcctId" maxlength="35" disabled="true" />
			</c:if>
			</td>
		</tr>
		
		<tr height="25">
			<td width="195px"><b><fmt:message key="correspondentaccountmaintenance.add.accountID" /></b></td>
			<td width="20px">&nbsp;</td>
			<td width="600px" colspan="2"><input id="accountId" readonly="readonly"
				style="width:220px;margin-bottom: 0px;height: 22px;" class="textAlpha"
				title='<fmt:message key = "tooltip.selectAccountId"/>'> <input
				tabindex="6" id="dropdowndiv_3" type="button" value="..."
				title='<fmt:message key = "tooltip.selectAccountId"/>'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input
				style="background: transparent; border: thin;width:320px;" class="textAlpha"
				onchange="changeBorderColor()"
				readonly name="accountDesc" size="40"></td>
		</tr>
		
	</table>
	</div>
	</div>
	<!-- Buttons -->
	<div id="correspondentAcctAdd" style="position: absolute; left: 755; top: 230px; width: 40px; height: 39px; visibility: visible;">
	
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr height="25">
			<td align="Right"><c:if test="${requestScope.methodName == 'save'}">
				<a tabindex="9" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Add Correspondent Account Alias'),'sectionprintdwindow','left=50,top=190,width=552,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if> <c:if test="${requestScope.methodName == 'modify'}">
				<a tabindex="9" href=#
					onclick="javascript:openWindow(buildPrintURL('print','Change Correspondent Account Alias'),'sectionprintdwindow','left=50,top=190,width=552,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
					onMouseOut="MM_swapImgRestore()"
					onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
					src="images/help_default.GIF " name="Help" border="0"
					title='<fmt:message key="tooltip.helpScreen"/>'></a>
			</c:if></td>

			<td align="right" id="Print"><a tabindex="9"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	
	<div id="ddimagebuttons" style="position: absolute; border: 2px outset; left: 20; top: 225px; width: 810px; height: 39px; visibility: visible;">
	<div id="correspondentAcctAdd" style="position: absolute; left: 6; top: 4; width: 650px; height: 15px; visibility: visible;">
	<table border="0" cellspacing="0" cellpadding="0" width="145">
		<tr>
			<td id="savebutton"><c:if test="${requestScope.methodName == 'save'}"><a onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" tabindex="7"
				onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
				onclick="submitForm('save');"
				title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save" /></a></c:if>
				<c:if test="${requestScope.methodName == 'modify'}"><a onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" tabindex="7"
				onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
				onclick="modifyCorrespondentAccount('modify');"
				title='<fmt:message key="tooltip.save"/>'><fmt:message key="button.save" /></a></c:if></td>
			<td id="cancelbutton"><a onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onclick="javascript:window.close();" tabindex="8"
				title='<fmt:message key="tooltip.cancel"/>'><fmt:message key="button.cancel" /></a></td>
		</tr>
	</table>
	</div>

	</div>



	</body>


</form>
</html>
