<!--
nonworkday.jsp 
JSP File for Non workday Maintenance Screen to display the Non workday for each Facility settings  
-->
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><fmt:message key="nonworkday.title.mainWindow" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">

/* Get Menu Entity Currency group for this screen*/
var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/* variable to hold selected data */
var selectElement;

/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
 mandatoryFieldsArray= "undefined" ;

/**
 * onFilterandSort
 * Method to update the filter color, sort icon , 
 * and enabling and disabling the buttons while sorting and filtering the grid
 */
function onFilterandSort(){
	updateColors();
	disableAllButtons();
}

/**
 * bodyOnLoad()
 * Method called on body onload
 * to load the grid and enable the buttons based on the access 
 */
function bodyOnLoad(){
	xl = new XLSheet("nonWorkdayList","table_2", ["String","String", "String","String"],"2111");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("nonWorkdayList");
	var dropBox1 = new SwSelectBox(document.forms[0].elements["nonWorkday.id.entityId"],document.getElementById("entityName"));	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
	if(document.forms[0].menuAccessId.value == "0" && menuEntityCurrGrpAccess == "0")
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	else
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
}

/**
 * submitForm()
 * @param methodName
 * Method to submit the form for record deletetion
 */
function submitDeleteForm(methodName){
     document.forms[0].method.value = methodName;
	 document.forms[0].facility.value = selectElement.getElementsByTagName("td")[0].innerText.trim();
	 document.forms[0].entityId.value = document.forms[0].elements['nonWorkday.id.entityId'].value;
	 var yourstate=window.confirm("<fmt:message key="confirm.delete"/>");
	if (yourstate==true){ 
		document.forms[0].submit();
	 }
}

/**
 * submitForm()
 * @param methodName
 * Method to submit the form
 */
function submitForm(methodName){
     document.forms[0].method.value = methodName;
	 document.forms[0].submit();
}

/**
 * disableAllButtons()
 * Method to disable  buttons when the grid unselected
 */
function disableAllButtons(){
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

/**
 * buildaddNonWorkdayURL()
 * @param methodName
 * Method to build add screen URL to open the screen with default parameters
 */
function buildaddNonWorkdayURL(methodName){

	var param = 'nonworkday.do?method='+methodName+'&entityId=';
	    param +=document.forms[0].elements['nonWorkday.id.entityId'].value;
	    param +='&EntityName=';
		param +=document.getElementById('entityName').innerText;
		return param;
	    
}

/**
 * buildChangeNonWorkdayURL()
 * @param methodName
 * Method to build change screen URL to open the screen with selected parameters
 */
function buildChangeNonWorkdayURL(methodName){

	var param = 'nonworkday.do?method='+methodName+'&entityId=';
	    param +=document.forms[0].elements['nonWorkday.id.entityId'].value;
	    param +='&facility=';
	    param +=selectElement.getElementsByTagName("td")[0].innerText;
	    param +='&EntityName=';
		param +=document.getElementById('entityName').innerText;
		param +='&applEntity=';
	    param +=selectElement.getElementsByTagName("td")[1].innerText;
		param +='&applAccount=';
	    param +=selectElement.getElementsByTagName("td")[2].innerText;
		param +='&applCurrency=';
	    param +=selectElement.getElementsByTagName("td")[3].innerText;		
	    return param;
	    
}

/**
 * onSelectTableRow()
 * @param rowElement
 * @param isSelected
 * Method called when a row in data grid selected, to enable buttons
 */	
function onSelectTableRow(rowElement, isSelected){
	
	selectElement = rowElement;	
	if(menuEntityCurrGrpAccess == "0" && isSelected){
	
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	}else{	
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	}

}



</script>
</head>
	<body onmousemove="reportMove()" leftmargin="0" topmargin="0"
		marginheight="0"
		onLoad="ShowErrMsgWindow('${actionError}'); setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"
		onunload="call();">

<form action="nonworkday.do">
	<input name="method" type="hidden" value="showDetails">

	<input name="entityId" type="hidden" value=" ">
	<input name="facility" type="hidden" value=" ">

	
	<input name="menuAccessId" type="hidden">


	<div id="nonworkday"
		style="position: absolute; left: 10px; top: 10px; width: 915px; height: 36px; border: 2px outset;"
		color="#7E97AF">
	<div id="nonworkday"
		style="position: absolute; left: 8px; top: 4px; width: 915px; height: 38;">
	<table width="450" border="0" cellpadding="0" cellspacing="0"
		height="25">
		<tr height="24">
			<td width="56px"><b><fmt:message key="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="120px">
			
			<select id="nonWorkdayId" name="nonWorkday.id.entityId"
			tabindex="1" titleKey="tooltip.selectEntityid" class="htmlTextAlpha" onchange="submitForm('showDetails')"
			style="width: 120px">

			 <c:forEach items="${requestScope.entities}" var="entity">
               <option
                  value="${entity.value}"
                  <c:if test="${nonWorkday.id.entityId == entity.value}">selected="selected"</c:if>>
                  ${entity.label}
               </option>
			</c:forEach>
			</select>
			
			
			</td>
			<td width="20">&nbsp;</td>
			<td width="280"><span id="entityName" name="#request.entityName"
				class="spantext"></td>
		</tr>
	</table>
	</div>
	</div>
	<!--  Start : mantis:1306 - to increased the table row height by Arumugam on 01-Apr-2011 -->
	<div id="nonworkday" color="#7E97AF"
		style="position: absolute; border-left: 2px outset; left: 10px; top: 50px; width: 898px; height: 392px;">
	<div id="nonworkday"
		style="position: absolute; z-index: 99; left: 0px; top: 0px; width: 898px; height: 10px;">
	<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="896"
		border="0" cellspacing="0" cellpadding="0" height="24">
		<thead>
			<tr height="24px">
				<td width="320px" align="left" style="border-left-width: 0px;"
					title='<fmt:message key="tooltip.sortFacility"/>'><b><fmt:message key="label.nonworkday.facility" /></b></td>
				<td width="180px" align="center"
					title='<fmt:message key="tooltip.sortApplyEntityCountry"/>'><b><fmt:message key="label.nonworkday.applyEntityCountry" /></b></td>
				<td width="190px" align="center"
					title='<fmt:message key="tooltip.sortApplyAccountCountry"/>'><b><fmt:message key="label.nonworkday.applyAccountCountry" /></b></td>
				<td width="200px" align="center"
					title='<fmt:message key="tooltip.sortApplyCurrencyCountry"/>'><b><fmt:message key="label.nonworkday.applyCurrencyCountry" /></b></td>
			</tr>
		</thead>
	</table>
	</div>



	<div id="ddscrolltable"
		style="position: absolute; left: 0px; top: 0px; width: 913px; height: 389px; overflowY: scroll">
	<div id="nonworkday"
		style="position: absolute; z-index: 99; left: 0px; top: 21px; width: 896px; height: 10px;">

	<table class="sort-table" id="nonWorkdayList" width="896" border="0"
		cellspacing="1" cellpadding="0" height="363">
		<tbody>
				<c:set var="count" value="0" />
				<c:forEach var="facility" items="${facilityList}">
					<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
						<td width="320px" align="left">
							<c:out value="${facility.id.facility}" />&nbsp;
						</td>
						<td width="180px" align="center">
							<c:out value="${facility.applyEntityCountry}" />&nbsp;
						</td>
						<td width="190px" align="center">
							<c:out value="${facility.applyAccountCountry}" />&nbsp;
						</td>
						<td width="200px" align="center">
							<c:out value="${facility.applyCurrencyCountry}" />&nbsp;
						</td>
					</tr>
					<c:set var="count" value="${count + 1}" />
				</c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="4"></td>
			</tr>
		</tfoot>
	</table>
	</div>
	</div>
	</div>
	<div id="country"
		style="position: absolute; left: 856; top: 462px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr height="25">

			<td align="Right"><a tabindex="6" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Non Workday Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>

			<td align="right" id="Print"><a tabindex="7"
				onclick="printPage();" onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 454px; width: 915px; height: 39px; visibility: visible;">
	<div id="nonworkday"
		style="position: absolute; left: 6; top: 4; width: 220; height: 15px; visibility: visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td width="70px" id="addbutton"></td>
			<td width="70px" id="changebutton"></td>
			<td width="70px" id="deletebutton"></td>
			<td width="70px" id="closebutton"
				title='<fmt:message key="tooltip.close"/>'><a tabindex="5"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close" /></a></td>
		</tr>
	</table>
	</div>
	<!--  End : mantis:1306 - to increased the table row height by Arumugam on 01-Apr-2011 -->
	<div
		style="position: absolute; left: 6; top: 4; width: 140; height: 15px; visibility: hidden;">
	<table border="0" cellspacing="0" cellpadding="0" height="20"
		style="visibility: hidden">
		<tr>


			<td id="addenablebutton"><a
				title='<fmt:message key="tooltip.nonworkday.addButton"/>' tabindex="2"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildaddNonWorkdayURL('add'),'nonworkdaymaintenanceaddWindow','left=50,top=190,width=677,height=205,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.add" /></a></td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled"	><fmt:message key="button.add" /></a></td>

			<td id="changeenablebutton"><a
				title='<fmt:message key="tooltip.nonworkday.changeButton"/>'
				tabindex="3" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:openWindow(buildChangeNonWorkdayURL('change'),'nonworkdaymaintenanceaddWindow','left=50,top=190,width=677,height=205,toolbar=0, resizable=yes, status=yes, scrollbars=yes','true')"><fmt:message key="button.change" /></a></td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.change" /></a></td>
			<td id="deleteenablebutton"><a
				title='<fmt:message key="tooltip.nonworkday.deleteButton"/>'
				tabindex="4" onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onClick="javascript:submitDeleteForm('delete');"><fmt:message key="button.delete" /></a></td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled"><fmt:message key="button.delete" /></a></td>

		</tr>
	</table>
	</div>

	</div>
</form>
</body>
</html>