<?xml version="1.0" encoding="utf-8"?>

<%@ page contentType="text/xml" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%
	//variable declaration
	String minValue="";
    String maxValue="";
    if (request.getAttribute("minValue") != null)
	//get the minValue   from request
	minValue = request.getAttribute("minValue")
			.toString();
    
    if (request.getAttribute("maxValue") != null)
	//get the maxValue from request
	maxValue = request.getAttribute("maxValue")
			.toString();

%>
<accountattribute accessInd="${accessInd}">
	<singletons>
		<attrEffDateRequired>${requestScope.accountHDR.effectiveDateRequired}</attrEffDateRequired>
		<attrAllowTime>${requestScope.accountHDR.effectiveDateAllowTime}</attrAllowTime>
		<validateDateAllowTime>${requestScope.accountHDR.validateDateAllowTime}</validateDateAllowTime>
		<tooltipText>${requestScope.accountHDR.tooltipText}</tooltipText>
		<validatetextregexmsg>${requestScope.accountHDR.validateTextRegexMsg}</validatetextregexmsg>
		<validatetextregex>${requestScope.accountHDR.validateTextRegex}</validatetextregex>
		<validatetextminlen>${requestScope.accountHDR.validateTextMinLen}</validatetextminlen>
		<validatetextmaxlen>${requestScope.accountHDR.validateTextMaxLen}</validatetextmaxlen>
		<validatenummin>${minValue}</validatenummin>
		<validatenummax>${maxValue}</validatenummax>
	</singletons>
	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location />
	</request_reply>
<%@ include file="/jsp/maintenance/accountattributegridflexdata.jsp"%>
	<selects>
		<select id="entity">
			<c:forEach items="${requestScope.entities}" var="entity">
				<c:choose>
					<c:when test="${requestScope.entityId == entity.value}">
						<option value="${entity.value}" selected="1">${entity.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${entity.value}" selected="0">${entity.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="currency">
			<c:forEach items="${requestScope.currencies}" var="currency">
				<c:choose>
					<c:when test="${requestScope.currencyCode == currency.value}">
						<option value="${currency.value}" selected="1">${currency.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${currency.value}" selected="0">${currency.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="accounts">
			<c:forEach items="${requestScope.accounts}" var="account">
				<c:choose>
					<c:when test="${requestScope.selectedAccount == account.value}">
						<option value="${account.value}" selected="1">${account.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${account.value}" selected="0">${account.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="attributes">
			<c:forEach items="${requestScope.accountAttributesDetails}" var="attribute">
				<c:choose>
					<c:when test="${requestScope.selectedAttribute == attribute.value}">
						<option value="${attribute.value}" selected="1">${attribute.label}</option>
					</c:when>
					<c:otherwise>
						<option value="${attribute.value}" selected="0">${attribute.label}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>

		<select id="types">
			<c:forEach items="${requestScope.typesList}" var="types">
				<c:choose>
					<c:when test="${requestScope.selectedType == types}">
						<option value="${types}" selected="1">${types}</option>
					</c:when>
					<c:otherwise>
						<option value="${types}" selected="0">${types}</option>
					</c:otherwise>
				</c:choose>
			</c:forEach>
		</select>
	</selects>			
</accountattribute>
