<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<html>
<head>
<title><fmt:message key="CurrencyInterestmaintenance.title.window"/></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script type="text/javascript" src="js/jquery.exportselect.js"></script>
<script language="javascript">

var appName = "<%=SwtUtil.appName%>";
var requestURL = new String('<%=request.getRequestURL()%>');
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;


var dateFlag=true;
var lastRefTime = "${requestScope.lastRefTime}";
var dateFormat =  '${sessionScope.CDM.dateFormat}';
var fromDateSelected = false;
var cancelcloseElements = new Array(1);
cancelcloseElements[0] = "closebutton";

var entityAccess = "${requestScope.EntityAccess}";
var dateSelected = false;
mandatoryFieldsArray = ["currencyInterestFromDate","currencyInterestToDate"] ;

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
/**
	* This method is used to Validate the date while click on sort
	* @return Boolean
	 */
function sortDateValidation() {
if(validateForm(document.forms[0]) ){
//Validate the Date field
	if(validateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'auditLog.from',dateFormat) && validateField(document.forms[0].elements['currencyInterest.toDateAsString'],'auditLog.to',dateFormat)){
	//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
				//Comparing TO date with from date
	if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {			
			dateFlag = true;
			return dateFlag;
			} else {
				dateFlag = false;
				return dateFlag;
				}
			} else {
				dateFlag = false;
				return dateFlag;
			} 
	} 
}



function disableAllButtons()
{
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML; 
}

function onFilterandSort()
{
	updateColors();
	disableAllButtons();

}

 function bodyOnLoad()
{
	var dateFormat =  '${sessionScope.CDM.dateFormat}';
	//Start: Create a Selet Export :Code added by Med Amine
	var headerData = [];
	var dataprovider = new Array();
	var newElement1 = {};
	newElement1[headerData[0]] = 'Pdf';
	dataprovider.push(newElement1);
	
	var newElement2 = {};
	newElement2[headerData[0]] = 'Excel';
	dataprovider.push(newElement2);
			
	var newElement3 = {};
	newElement3[headerData[0]] = 'Csv';
	dataprovider.push(newElement3);
	
	$("#exportReport").exportselect ({
		dataprovider: dataprovider,
		change: exportReport,
		selectedIndex:0
	  });

	if(record()== 0)
		 $("#exportReport").disabled(true); 
	else
		 $("#exportReport").disabled(false); 


	xl = new XLSheet("currencyInterestList","table_2", ["String","String","String","Number","String","String"],"101000");
	xl.onsort = xl.onfilter = onFilterandSort;
	highlightTableRows("currencyInterestList");
	
	var dropBox1 = new SwSelectBox(document.forms[0].elements["currencyInterest.id.entityId"],document.getElementById("entityId"));
	var dropBox2 = new SwSelectBox(document.forms[0].elements["currencyInterest.id.currencyCode"],document.getElementById("currencyCode"));

	document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	
  	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
	<%}%>

	<%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
	<%}%>

	 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
	 <%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
	  <%}%>

	<%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
	 <%}%>

	 <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
	 <%}%>
	
	document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

	document.getElementById("lastRefTime").innerText = lastRefTime;

} 
 
 function exportReport(){
		var type=$("#exportReport").getSelected(0);
		return exportData(type.toLowerCase());
	}
 
 

function submitForm(methodName)
{

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function submitFormDelete(methodName){

	dateSelected = false;
	fromDateSelected = false;	
	var dateFormat = '${sessionScope.CDM.dateFormat}';
	if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat)) {		
	if(validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)) {			
	if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')){

	document.forms[0].method.value = methodName;
	
	var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
	if (yourstate==true){ 
		document.forms[0].method.value = methodName;
		document.forms[0].submit();
	}

	} else{
		return false;
		}
	} else{			
		return false;
		}
	} else{		
		return false;
		}

}


function onCurrencyChange(methodName)
{

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	var fromExchangeDate=document.forms[0].elements['currencyInterest.fromDateAsString'].value;
	var toExchangeDate=document.forms[0].elements['currencyInterest.toDateAsString'].value;
	
	document.forms[0].method.value = methodName;
	document.forms[0].submit();
}

function onSelectTableRow(rowElement, isSelected){
	if(rowElement && rowElement.cells.length == 6)
	{
		var cell1 = rowElement.cells[0];
		var cell2 = rowElement.cells[1];
		var cell3 = rowElement.cells[2];
		var cell4 = rowElement.cells[3];
		var hiddenEl1 = rowElement.cells[0].innerText;	
		var hiddenEl2 = cell2.getElementsByTagName("input")[0];
		var hiddenEl3 = cell3.getElementsByTagName("input")[0];				
		var hiddenEl4 = rowElement.cells[3].innerText;			

	}


	var flag=currencyAccessConfirm(rowElement.cells[0].innerText.trim(),document.forms[0].elements['currencyInterest.id.entityId'].value)

	if(flag=="true" && menuEntityCurrGrpAccess == 0 && isSelected)
	{

		document.forms[0].selectedCurrencyCode.value = rowElement.cells[0].innerText;
		
		document.forms[0].selectedCurrencyName.value =rowElement.cells[1].innerText;
		
		document.forms[0].selectedInterestDate.value = rowElement.cells[2].innerText;
		
		document.forms[0].selectedInterestRate.value = hiddenEl4;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;

	}else{
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

		document.forms[0].selectedCurrencyCode.value = "";
		document.forms[0].selectedCurrencyName.value = "";
		document.forms[0].selectedInterestDate.value = ""; 
		document.forms[0].selectedInterestRate.value = "";

	}
		

}


function currencyAccessConfirm(currency,entity){

	var oXMLHTTP = new XMLHttpRequest();
	var sURL = requestURL + appName+"/defaultacctmaintenance.do?method=currencyAccessConfirm";
	sURL = sURL + "&currency="+currency;
	sURL = sURL + "&entityId="+entity;
	oXMLHTTP.open( "POST", sURL, false );	
	oXMLHTTP.send();
	var str=oXMLHTTP.responseText;
	return str;
}

function getValidAccess(){
    
	var table = document.getElementById("currencyInterestList");  
	var tbody = table.getElementsByTagName("tbody")[0];    
	var rows = tbody.getElementsByTagName("tr");
	var selectedList = "";

	for (i=0; i < rows.length; i++) 
	 {
		if( isRowSelected(rows[i])){

			var hiddenElement2 = rows[i].getElementsByTagName("input")[0].value;
			if(hiddenElement2 != 'true'){
				return false;
			}			
		}
	 }  		
	return true;
}



function buildAddExchangeURL(methodName){
fromDateSelected = false;
dateSelected = false;
	if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat) && validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)){
	//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
				//Comparing TO date with from date
	if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {  
	var param = 'currencyinterest.do?method='+methodName+'&currencyCode=';
	  param +=document.forms[0].elements['currencyInterest.id.currencyCode'].value;	 
	  param +='&currencyName='+document.getElementById("currencyCode");
	  param +='&entityId='+document.forms[0].elements['currencyInterest.id.entityId'].value;


	window.openWindow(param,'currencyexchangechildmaintenanceWindow','left=50,top=500,,width=480,height=225,toolbar=0','true');

	} else {
		return false;
	}
	} else {			
			return false;
		}
	}


function buildChangeExchangeURL(methodName){
fromDateSelected = false;
dateSelected = false;
if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat) && validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)){
//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
				//Comparing TO date with from date
	if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {  
	var param = 'currencyinterest.do?method='+methodName+'&selectedCurrencyCode=';
	  param +=document.forms[0].selectedCurrencyCode.value;	 
	  param +='&currencyName='+document.getElementById("currencyCode");
	  param +='&entityId='+document.forms[0].elements['currencyInterest.id.entityId'].value;
	  param +='&selectedInterestRate='+document.forms[0].selectedInterestRate.value;
	  param +='&selectedInterestDate='+document.forms[0].selectedInterestDate.value;
	  window.openWindow(param,'currencyexchangechildmaintenanceWindow','left=50,top=190,width=480,height=225,toolbar=0','true');
	
	} else {			
			return false;
		}
	} else {			
			return false;
		}

}
//This funtion is for validate date and get the details for that particular date
function onFromDateKeyPress(obj,e){
	var event = (window.event|| e);
	fromDateSelected = false;
	dateSelected = false;
	var obj2 = document.forms[0].elements['currencyInterest.toDateAsString'];
	//Condition for checking the key event
	if(event.keyCode == 9){   // tab
	//validating the Text field
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(obj2,'auditLog.to',dateFormat)){
		//Comparing TO date with from date
		if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('showDetails');
			} else {				
				return false;
			}
			} else {				
				return false;
			}
		}
		//Condition for checking the key event
	if(event.keyCode == 13){  //enter
		if(validateField(obj,'auditLog.from',dateFormat) && validateField(obj2,'auditLog.to',dateFormat)){
				//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
				//Comparing TO date with from date
		if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('showDetails');
			} else {
				return false;
			} 
			} else {				
				return false;
			}
		}
}
//This function is used to check the date validation for To date
function onToDateKeyPress(obj,e){
	fromDateSelected = false;
	dateSelected = false;
	var event = (window.event|| e);
	var obj2 = document.forms[0].elements['currencyInterest.fromDateAsString'];
	//Condition for checking the key event
	if(event.keyCode == 9){   // tab
	//validating the Text field 
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(obj2,'auditLog.from',dateFormat)){
	//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
	//Comparing TO date with from date
	if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('showDetails');
			} else {			
				return false;
			}
			} else {				
				return false;
			}
	}
	if(event.keyCode == 13){  //enter
	//validating the Date field 
		if(validateField(obj,'auditLog.to',dateFormat) && validateField(obj2,'auditLog.from',dateFormat)){
		//Code Modified for Mantis 1545 by Chinniah on 05-SEP-2011:Currency interest : To Date validation message displays wrong 
		//Comparing TO date with from date
		if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {
			submitForm('showDetails');
			} else {
				return false;
				}
			} else {				
				return false;
			}	
	}
}




function storeToDate(){	
document.forms[0].elements['preInterestRateToDateAsString'].value=document.forms[0].elements['currencyInterest.toDateAsString'].value;
}

<!-- Java script Method  to store from date before it is changed -->
function storeFromDate(){	
document.forms[0].elements['preInterestRateFromDateAsString'].value=document.forms[0].elements['currencyInterest.fromDateAsString'].value;
}
<!-- Java script method to validate from and to date validations and to display records for selected from date -->



function onFromDateChange(obj){

	if(fromDateSelected){
		if(document.forms[0].elements['currencyInterest.fromDateAsString'].value !="" && validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat)) {
			cal.hideCalendar()
			if(document.forms[0].elements['currencyInterest.toDateAsString'].value !="" && validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)) {
				var predata = document.forms[0].elements['preInterestRateFromDateAsString'].value;
				document.forms[0].elements['selectedCurrencyCode'].value=document.forms[0].elements['currencyInterest.id.currencyCode'].value;			
			    document.forms[0].elements['currencyInterest.fromDateAsString'].value=document.forms[0].elements['currencyInterest.fromDateAsString'].value;	
				document.forms[0].elements['currencyInterest.toDateAsString'].value=document.forms[0].elements['currencyInterest.toDateAsString'].value;		    
				var from_date=document.forms[0].elements['currencyInterest.fromDateAsString'].value;
				var to_date=document.forms[0].elements['currencyInterest.toDateAsString'].value;
				var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
				var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);				
					if(compare_date == 1) {
					document.forms[0].elements['currencyInterest.fromDateAsString'].value=predata;
					cal.hideCalendar();
					//Code Modified for Mantis 1545 by Chinniah on 9-SEP-2011:Issue found on Beta2
					alert('<fmt:message key="alertDateShouldNotBeEarlierEhanFromDate"/>');				
					
					}else{
					
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	

					
				    document.forms[0].elements['selectedInterestDate'].value=document.forms[0].elements['currencyInterest.fromDateAsString'].value;		    			
				    document.forms[0].method.value = 'showDetails';		    		   	
				    document.forms[0].submit();
					}
					
				fromDateSelected = false;
				dateSelected = false;	
			    return true;
			} else{
				fromDateSelected = false;
				dateSelected = false;
		        return false;
		     }
		} else{
			fromDateSelected = false;
			dateSelected = false;
			return false;
			}
		} else{
			return false;
		}
}




<!-- Code modified to check To Date should be greater than From Date -->
function onDateChange(obj){
	if(dateSelected){
		if(document.forms[0].elements['currencyInterest.toDateAsString'].value !="" && validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)) {	
		cal2.hideCalendar()
		if(document.forms[0].elements['currencyInterest.fromDateAsString'].value !="" && validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat)) {			
				var predata = document.forms[0].elements['preInterestRateToDateAsString'].value;
				document.forms[0].elements['selectedCurrencyCode'].value=document.forms[0].elements['currencyInterest.id.currencyCode'].value;			
			    document.forms[0].elements['currencyInterest.fromDateAsString'].value=document.forms[0].elements['currencyInterest.fromDateAsString'].value;	
				document.forms[0].elements['currencyInterest.toDateAsString'].value=document.forms[0].elements['currencyInterest.toDateAsString'].value;		    
				var from_date=document.forms[0].elements['currencyInterest.fromDateAsString'].value;
				var to_date=document.forms[0].elements['currencyInterest.toDateAsString'].value;
				var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
				var compare_date=compareDates(from_date,dateFormatValue,to_date,dateFormatValue);
					if(compare_date == 1) {
					document.forms[0].elements['currencyInterest.toDateAsString'].value=predata;
					cal2.hideCalendar();
					//Code Modified for Mantis 1545 by Chinniah on 9-SEP-2011:Issue found on Beta2
					alert('<fmt:message key="alertDateShouldNotBeEarlierEhanFromDate"/>');					
					
					}else{
					
document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;
	

					
				    document.forms[0].elements['selectedInterestDate'].value=document.forms[0].elements['currencyInterest.toDateAsString'].value;		    			
				    document.forms[0].method.value = 'showDetails';		    		   	
				    document.forms[0].submit();
					}					
				fromDateSelected = false;
				dateSelected = false;	
			    return true;
			} else{
				fromDateSelected = false;
				dateSelected = false;	
		        return false;
		     }
			 } else{
				fromDateSelected = false;
				dateSelected = false;	
				return false;
			}
		} else{			
			return false;
		}

}

function onDateKeyPress(obj,e){
	var event = (window.event|| e);
	document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	var dateFormat = '${sessionScope.CDM.dateFormat}';
	
	if(event.keyCode == 13){  //enter
		
		if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat))
		{
			if(validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat))
			{		
				if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date'))
				{
					submitForm('showDetails'); 
				}else{
					return false;
				}
			}else{
				return false;
			}
		}else{
			return false;
		}
	}
}

function refreshWindow(){
fromDateSelected = false;
dateSelected = false;	

document.getElementById("refreshbutton").innerHTML = document.getElementById("refreshdisablebutton").innerHTML;

	var dateFormat = '${sessionScope.CDM.dateFormat}';
		if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat))
		{
			if(validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat))
			{		
				if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date'))
				{
					submitForm('showDetails'); 
				}else{
					return false;
				}
			}else{
				return false;
			}
		}else{
			return false;
		}	

}



function validateForm(objForm){
  var elementsRef = new Array(2);
   elementsRef[0] = objForm.elements["currencyInterest.fromDateAsString"];
   elementsRef[1] = objForm.elements["currencyInterest.toDateAsString"];

  return validate(elementsRef);
}
				 
function clearDate() {

document.forms[0].elements['currencyInterest.fromDateAsString'].value ="";
document.forms[0].elements['currencyInterest.toDateAsString'].value ="";

}


</script>
 <script language="JAVASCRIPT">
	   var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
		
		var cal = new CalendarPopup("caldiv",true,"calFrame"); 
	
		cal.setCssPrefix("CAL");
		cal.offsetX = -30;
	
		cal.offsetY = -10;
	
		var cal2 = new CalendarPopup("caldiv",true,"calFrame");
		cal2.setCssPrefix("CAL");
	
		cal2.offsetX = 150;
		cal2.offsetY = -10;
	
	
	
	function exportData(exportType) {
	
	var dateFormat = '${sessionScope.CDM.dateFormat}';
		if(validateDateField(document.forms[0].elements['currencyInterest.fromDateAsString'],'fromDateAsString',dateFormat)) {
		if(validateDateField(document.forms[0].elements['currencyInterest.toDateAsString'],'toDateAsString',dateFormat)) {			
			if(comparedates(document.forms[0].elements['currencyInterest.fromDateAsString'].value,document.forms[0].elements['currencyInterest.toDateAsString'].value,dateFormat,'From Date','To Date')) {
	
			document.forms[0].method.value = 'exportCurrencyInterest';
			document.forms[0].exportType.value = exportType.trim();
			document.forms[0].entityCode.value = document.forms[0].elements["currencyInterest.id.entityId"].value;
			document.forms[0].selectedCurrencyCode.value = document.forms[0].elements["currencyInterest.id.currencyCode"].value;
			document.forms[0].FromDate.value= document.forms[0].elements["currencyInterest.fromDateAsString"].value;
			document.forms[0].ToDate.value= document.forms[0].elements["currencyInterest.toDateAsString"].value;
			if(record()> 0) {
				document.forms[0].submit();
				}
				
				} else {
					return false;
					}
				} else{			
					return false;
				}
			} else{
			return false;
			}
			
}
  
  </script> 
</head>


<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();ShowErrMsgWindow('${actionError}');" onunload="call()">

<form action="currencyinterest.do" onsubmit="return validate(this);" enctype="multipart/form-data">
<input name="method" type="hidden" value="display">

<input name="exportType" type="hidden" value="">
<input type="hidden" name="screen" id="exportDataScreen" value="CurrencyInterestMaintenance-SmartPredict"/>
<input name="entityCode" type="hidden">

<input name="selectedCurrencyCode" type="hidden" >
<input name="selectedCurrencyName" type="hidden" >

<input name="FromDate" type="hidden" >
<input name="ToDate" type="hidden">

<input name="selectedInterestDate" type="hidden" >
<input name="selectedInterestRate" type="hidden" >
<input name="preInterestRateToDateAsString" type="hidden" />

<input name="preInterestRateFromDateAsString" type="hidden" />

<c:set var="CDM" value="${sessionScope.CDM}"/>

<input name="menuAccessId" type="hidden" >
<div id="CurrencyInterest" style="z-index:99;position:absolute; left:20px; top:20px; width:905px; height:90px; border:2px outset;" color="#7E97AF">
<div id="CurrencyInterest"style="z-index:99;position:absolute; left:8px; top:4px; width:510px; height:38px; ">

	<table width="510" border="0" cellpadding="0" cellspacing="0" height="25">
		<tr height="25">
			<td width="30">
				<b style="font-size: 12px"><fmt:message key="currency.entityId"/></b>
			</td>
			<td width="28">
				&nbsp;
			</td>
			<td width="28">		  
			<select tabindex="1" name="currencyInterest.id.entityId" onchange="clearDate();submitForm('showDetails');"  titleKey="tooltip.entityId" style="width:140px">
			<c:forEach var="item" items="${requestScope.entities}">
			<option value="${item.value}"<c:if test="${currencyInterest.id.entityId == item.value}">selected</c:if>>
			${item.label}</option>
			</c:forEach>
			</select>
			</td>
			<td width="20">
				&nbsp;
			</td>
			<td width="300">
					<span id="entityId" name="entityId" class="spantext"  style="font-size: 12px">
			</td>
		</tr>
		<tr height="25">
			<td width="30">
				<b style="font-size: 12px"><fmt:message key="currency.currencyCode" /></b>
			</td>
			<td width="28">
				&nbsp;
			</td>
			<td width="28">		  
			<select tabindex="1" name="currencyInterest.id.currencyCode" onchange="clearDate();submitForm('showDetails');" style="width:55px" titleKey="tooltip.currencyIdentifier" >
			<c:forEach var="item" items="${requestScope.currencies}">
			<option value="${item.value}"<c:if test="${currencyInterest.id.currencyCode == item.value}">selected</c:if>>
			${item.label}</option>
			</c:forEach>
			</select>
			</td>
			<td width="20">
				&nbsp;
			</td>
			<td width="300">
				<span id="currencyCode" name="currencyCode" class="spantext"  style="font-size: 12px">
			</td>
		</tr>
	</table>

<div id="caldiv" style="z-index:99;position:absolute;visibility:visible;background-color:white;layer-background-color:white;"></div>
		<iframe id="calFrame" src="javascript:false;" scrolling="no" frameborder="0" style="position:absolute; top:0px; left:0px; display:none;">
		</iframe>

	<table width="397px" border="0" cellspaving="0" cellpadding="0" height="25">
		<tr height="25px">
			<td width="120" >
				<b style="font-size: 12px"><fmt:message key="auditLog.from"/></b>*
			</td>
			<td width="26">
				&nbsp;
			</td>
			<td  width="215px"style="padding-left: 8px" id="currencyInterestFromDate">
				
				
				<input type="text"  class="htmlTextAlpha" name="currencyInterest.fromDateAsString"  value="${currencyInterest.fromDateAsString}"
				maxlength="10" style="width:80px;height:20px;" titleKey="tooltip.selectFromDate" tabindex="2" onblur="onFromDateChange(this);" onkeydown="onFromDateKeyPress(this,event)" onmouseout="fromDateSelected=false;"/>
				
				&nbsp;
				<A  name="datelink" ID="datelink" tabindex="2" onClick="cal.select(document.forms[0].elements['currencyInterest.fromDateAsString'],'datelink',dateFormatValue);storeFromDate();fromDateSelected=true;return false;" >
				<img title='<fmt:message key="tooltip.selectFromDate"/>'  src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;">
				</A>
			</td>
			<td width="25">
				&nbsp;
			</td>
			<td width="90" height="26">
				<b style="font-size: 12px;padding-left: 25px"><fmt:message key="auditLog.to"/></b>*
			</td>
			
			<td  width="215px" id="currencyInterestToDate" >
			
			<input type="text" class="htmlTextAlpha" name="currencyInterest.toDateAsString" value="${currencyInterest.toDateAsString}"
			style="width:80px;height:20px;"
				titleKey="tooltip.selectToDate"  maxlength="10"
				tabindex="4" onblur="onDateChange(this);" onkeydown="onToDateKeyPress(this,event)" onmouseout="dateSelected=false;"/> &nbsp;								
				
				<A name="datelink2" ID="datelink2" tabindex="5" onClick="cal2.select(document.forms[0].elements['currencyInterest.toDateAsString'],'datelink',dateFormatValue);storeToDate();dateSelected=true;return false;">
				<img title='<fmt:message key="tooltip.selectBalanceDate"/>'
				src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;"></A>
			</td>
			
		</tr> 
	</table> 
	</div>
</div>



<div id="BalanceMaintenance" color="#7E97AF" style="position:absolute; border:0px outset; left:20px; top:115px; width:906px; height:434px;">
<div id="BalanceMaintenance" style="position:absolute;z-index:99;left:0px; top:0px; width:895px; height:10px;">
	<table  class="sort-table" id="table_2" bgcolor="#B0AFAF" width="881" border="0" cellspacing="1" cellpadding="0"  >
		<thead>
			<tr height="20px">
			
				<td  width="70px"	align="center" style="border-left-width: 0px;"title='<fmt:message key="tooltip.sortByCurrencyCode"/>'><b><fmt:message key="sweep.currencyCode"/></b></td>
				<td  width="270px"  align="center" title='<fmt:message key="interest.tooltip.sortByName"/>'><b><fmt:message key="userStatus.header.name"/></b></td>
				<td  width="97px"	align="center" title='<fmt:message key="interest.tooltip.sortByInterestRateDate"/>'><b><fmt:message key="auditLog.logDate_Date"/></b></td>
				<td  width="160px"	align="center" title='<fmt:message key="interest.tooltip.sortByInterestRate"/>'><b><fmt:message key="currencyInterest.interestRate.label"/></b></td>

				
				<td  width="150px"	align="center" title='<fmt:message key="interest.tooltip.updateDate"/>'><b><fmt:message key="accint.timedate"/></b></td>
				


				<td  width="135px"	align="center" title='<fmt:message key="interest.tooltip.sortByUserId"/>'><b><fmt:message key="balmaintenance.updateUser"/></b></td>
			</tr>
		</thead>
	</table>
</div>

<div id="ddscrolltable"  style="position:absolute; left:0px; top:0px; width:903px; height:432px;overflow-x:hidden">
<div id="CurrencyInterest" style="position:absolute;z-index:99;left:0px; top:21px; width:890px; height:10px;">
	<table class="sort-table" id="currencyInterestList" width="881" border="0" cellspacing="1" cellpadding="0" height="409">
		<tbody>
			<c:set var="count" value="0" />
			<c:forEach var="currency" items="${requestScope.currencyInterestList}">
				<tr class="${count % 2 == 0 ? 'even' : 'odd'}">
					<td width="70px" align="left">
						<input type="hidden" name="currencyAccess" value="${currency.currencyAccess}"/>
						<input type="hidden" name="id.currencyCode" value="${currency.id.currencyCode}" />
						${currency.id.currencyCode}
					</td>
					<td width="270px" align="left">
						<input type="hidden" name="currencyName" value="${currency.currencyName}" />
						${currency.currencyName}&nbsp;
					</td>
					<td width="97px" align="left">
						<input type="hidden" name="id.interestRateDate" value="${currency.id.interestRateDate}" />
						${currency.interestRateDateAsString}&nbsp;
					</td>
					<td width="160px" align="right">
						${currency.interestRate}&nbsp;
					</td>
					<td width="150px" align="left">
						<input type="hidden" name="updateDateAsString" value="${currency.updateDateAsString}" />
						${currency.updateDateAsString}&nbsp;
					</td>
					<td width="135px" align="left">
						${currency.updateUser }&nbsp;
					</td>
				</tr>
				<c:set var="count" value="${count + 1}" />
			</c:forEach>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="6" ></td>
			</tr>
		</tfoot>
	</table>
</div>
</div> 
<div id="pnlReport" style="position:absolute ;left:823; top:449px; width:70px; height:30px; z-index:5;visibility:visible;">
	<table>
	  <div id="exportReport" />
	</table>
	</div>
<div id ="helpScreen" style="position:absolute ;left:870; top:447px; width:30px; height:30px;">
	   <a tabindex="11" tabindex="6" href=# onclick="javascript:openWindow(buildPrintURL('print','Currency Interest Rate Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
	          <img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a> 
	</div>
<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:0; top:437; width:906; height:39px; visibility:visible;">
<div id="CustodianMaintenance" style="position:absolute; left:6; top:4; width:539; height:15px; visibility:visible;">
	<table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
		<tr>
		   
		    <td id="refreshbutton">
		   
				<a tabindex="4"title='<fmt:message key="tooltip.refreshWindow"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){refreshWindow();}"><fmt:message key="button.Refresh"/>
				</a>
			</td>				
		  
			<td id="addbutton"></td>
			<td id="changebutton"></td>
			<td id="deletebutton"></td>
			<td id="closebutton">
				<A tabindex="7" title='<fmt:message key="tooltip.close"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/>
				</A>			
			</td>
		</tr>
	</table>
</div>
	
	<table height="35"><tr>
		<td id="lastRefTimeLable" width="682px" align="right" style="font-size:12" >
		<fmt:message key="label.lastRefTime"/>
		</td>			
		<td id="lastRefTime" style="font-size:12" >
		<input class="textAlpha" style="background:transparent;border: 0;" tabindex="-1" readonly name="maxPageNo" value="" size="14">				
		</td>
		</tr>
	</table>
	
<div style="position:absolute; left:6; top:4; width:512; height:15px; visibility:hidden;">  	
	<table width="280" border="1" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
		<tr>
		
		<td id="refreshdisablebutton">
			<a  class="disabled"  disabled="disabled"><fmt:message key="button.Refresh"/></a>
		</td>
		
			<td id="addenablebutton">
		
				<a tabindex="4"title='<fmt:message key="tooltip.addNewCurrencyInterest"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){buildAddExchangeURL('add');}"><fmt:message key="button.add"/>
		
				</a>
			</td>		
			<td id="adddisablebutton">
				<a  class="disabled" disabled="disabled">
					<fmt:message key="button.add"/>
				</a>
			</td>
			<td id="changeenablebutton">		
		
				<a tabindex="5" title='<fmt:message key="tooltip.changeSelCurrencyInterest"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){buildChangeExchangeURL('change');}"><fmt:message key="button.change"/>
				
				</a>
			</td>		
			<td id="changedisablebutton">
				<a  class="disabled" disabled="disabled">
					<fmt:message key="button.change"/>
				</a>
			</td>
			<td id="deleteenablebutton">		
				<a tabindex="6" title='<fmt:message key="tooltip.delSelCurrencyInterest"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="if(validateForm(document.forms[0]) ){javascript:submitFormDelete('delete');}"><fmt:message key="button.delete"/>
				</a>
			</td>
			<td id="deletedisablebutton">
				<a  class="disabled" disabled="disabled">
					<fmt:message key="button.delete"/>
				</a>
			</td>
		</tr>
	</table>
</div>
</div>


<script type="text/javascript"></script>
</form>
</body>
</html>