<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"  %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@page import="org.swallow.maintenance.model.MessageFormats"%>
<html>
<head>
<title><fmt:message key="messageFormat.title.MainWindow"/></title>
<%@ include file="/angularJSUtils.jsp"%>
<link rel="stylesheet"
	href="angularSources/styles.css?version=<%=SwtUtil.appVersion%>">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<SCRIPT language="JAVASCRIPT">
    <c:if test="${requestScope.parentFormRefresh == 'yes'}">
    window.opener.document.forms[0].method.value = "displayList";
    window.opener.document.forms[0].submit();
    self.close();
    </c:if>

    var screenRoute = "MessageFormats";
    var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";

    /*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
    mandatoryFieldsArray = "undefined";

    function submitForm(methodName) {
        if (methodName != 'delete') {
            document.forms[0].method.value = methodName;
            document.forms[0].submit();
        } else {
            var yourstate = window.confirm('<fmt:message key="confirm.delete"/>');
            if (yourstate == true) {
                document.forms[0].method.value = methodName;
                document.forms[0].submit();
            }
        }

    }

    /**
     * buildAddMessageFormat
     * To open child screen
     * @param methodName
     */
    function openChildWindow(methodName, formatId, entityCode, formatName, formatType, fldDelimiter, hexFldDelimiter, msgSeparator, hexMsgSeparator, entityName) {

        var param = 'messageformats.do?method=' + methodName;
        param += '&formatId=' + formatId;
        param += '&entityCode=' + entityCode;
        param += '&formatName=' + formatName;
        param += '&formatType=' + formatType;
        param += '&fldDelimiter=' + fldDelimiter;
        param += '&hexFldDelimiter=' + hexFldDelimiter;
        param += '&msgSeparator=' + msgSeparator;
        param += '&hexMsgSeparator=' + hexMsgSeparator;
        param += '&entityName=' + entityName;

        return openWindow(param, 'messageformataddwindow', 'left=50,top=190,width=690,height=490,toolbar=0, resizable=yes, status=yes, scrollbars=yes', 'true');
    }

    function disableAllButtons() {
        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;

        document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

    }

    function onFilterandSort() {
        updateColors();
        disableAllButtons();
    }

    function bodyOnLoad() {
        xl = new XLSheet("msgFormatDetails", "table_2", ["String", "String", "String", "String", "String", "String", "String", "String", "String", "String", "String"], "11111111111");

        xl.onsort = xl.onfilter = onFilterandSort;
        highlightTableRows("msgFormatDetails");

        var cancelcloseElements = new Array(1);
        cancelcloseElements[0] = "closebutton";

        var dropBox1 = new SwSelectBox(document.forms[0].elements["messageFormats.id.entityId"], document.getElementById("entityDesc"));

        document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
        document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;

        <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
        document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
        <%}%>

        <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
        document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
        <%}%>

        <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
        document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
        <%}%>

        <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
        document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
        <%}%>

        <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
        document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
        <%}%>

        <%if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
        document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
        <%}%>


        document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";


    }

    /**
     * This function enables/disables buttons based on grid record selected
     *
     * @param rowElement
     * @param isSelected
     */
    function onSelectTableRow(rowElement, isSelected) {
        if (rowElement && rowElement.cells.length == 7) {
            var hiddenFormatId = rowElement.cells[0].getElementsByTagName("input")[0];
            var hiddenFormatName = rowElement.cells[1].getElementsByTagName("input")[0];
            var hiddenFormatType = rowElement.cells[2].getElementsByTagName("input")[0];
            var hiddenFieldDelimiter = rowElement.cells[3].getElementsByTagName("input")[0];
            var hiddenHexFldDelimiter = rowElement.cells[4].getElementsByTagName("input")[0];
            var hiddenMsgSeparator = rowElement.cells[5].getElementsByTagName("input")[0];
            var hiddenHexMsgSeparator = rowElement.cells[6].getElementsByTagName("input")[0];
        }
        document.forms[0].selectedFormatId.value = rowElement.cells[0].innerText.trim();
        document.forms[0].selectedFormatName.value = hiddenFormatName.value;
        document.forms[0].selectedFormatType.value = hiddenFormatType.value;
        document.forms[0].selectedFieldDelimiter.value = hiddenFieldDelimiter.value;
        document.forms[0].selectedHexFldDelimiter.value = hiddenHexFldDelimiter.value;
        document.forms[0].selectedMsgSeparator.value = hiddenMsgSeparator.value;
        document.forms[0].selectedHexMsgSeparator.value = hiddenHexMsgSeparator.value;
        document.forms[0].selectedEntityId.value = document.forms[0].elements['messageFormats.id.entityId'].value;
        /*Start:code modified by sandeepkumar for mantis 2094 View button not enabled if screen having View access  */
        if (menuEntityCurrGrpAccess == "0" && isSelected) {
            document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
            document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
            document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
        } else if (menuEntityCurrGrpAccess == "1" && isSelected) {
            document.getElementById("viewbutton").innerHTML = document.getElementById("viewenablebutton").innerHTML;
        } else {
            document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
            document.getElementById("viewbutton").innerHTML = document.getElementById("viewdisablebutton").innerHTML;
            document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
        }
        /*End:code modified by sandeepkumar for mantis 2094 View button not enabled if screen having View access */
    }
</SCRIPT>

</head>
<body>
<%@ include file="/angularscripts.jsp"%>
<script type="text/javascript">
var appName = "<%=SwtUtil.appName%>";
var baseURL = new String('<%=request.getRequestURL()%>');


 </script>
</body>
</body>
</html>

    
  