<!--
countryoverridechange.jsp 
JSP File to display Add / Chnage Country Maintenance Screen, to edit the
Default Weekend and Override Weekend for each country 
-->

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.CommonDataManager"%>
<html>
<head>
<title><fmt:message key="country.title.changeScreen" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<SCRIPT language="JAVASCRIPT">
var cancelcloseElements = new Array(1);
cancelcloseElements[0]="cancelbutton";
var dateFormat = '${sessionScope.CDM.dateFormat}';

<c:if test="${requestScope.parentFormRefresh == 'yes'}">
window.opener.document.forms[0].method.value="showDetails";
window.opener.document.forms[0].submit();
self.close();
</c:if>

/**
 * bodyOnLoad()
 * Method called on body onload to hold selected country and other values fro the child screen
 */
 function bodyOnLoad()
{
	var preoverrideWeekend1=new String('<%=request.getParameter("overrideWeekend1")%>');
	var preoverrideWeekend2=new String('<%=request.getParameter("overrideWeekend2")%>');
	var overrideWeekend1combo=document.getElementById("countryOverride.overrideWeekend1").options
	
	for(var i=0;i<overrideWeekend1combo.length;i++) {
		if(overrideWeekend1combo[i].text==preoverrideWeekend1.trim()){
			document.getElementById("countryOverride.overrideWeekend1").value=overrideWeekend1combo[i].value;
			break;
		}    
	}
	var overrideWeekend1combo=document.getElementById("countryOverride.overrideWeekend2").options
	for(var i=0;i<overrideWeekend1combo.length;i++) {
		if(overrideWeekend1combo[i].text==preoverrideWeekend2.trim()){
			document.getElementById("countryOverride.overrideWeekend2").value=overrideWeekend1combo[i].value;
			break;
		}    
	}
	
	document.getElementById("countryName").innerText=document.getElementById("countryOverride.countryName").value;
	document.getElementById("entityName").innerText='<%=request.getParameter("EntityName")%>'
}

/**
 * submitForm()
 * Method to submit the edit values to save or update
 */
function submitForm(){
	var overrideWeekend1combo=document.getElementById("countryOverride.overrideWeekend1");
	var overrideWeekend1 =new String(overrideWeekend1combo.options[overrideWeekend1combo.selectedIndex].text);
	var overrideWeekend2combo=document.getElementById("countryOverride.overrideWeekend2");
	var overrideWeekend2 = new String(overrideWeekend2combo.options[overrideWeekend2combo.selectedIndex].text);
	var preoverrideWeekend1=new String('<%=request.getParameter("overrideWeekend1")%>');
	var preoverrideWeekend2=new String('<%=request.getParameter("overrideWeekend2")%>');
	var status="";
	if((overrideWeekend1.trim()==""&&overrideWeekend2.trim()=="")&&(preoverrideWeekend1.trim()!=""||preoverrideWeekend2.trim()!="")){
		status="delete";
	}
	else if((overrideWeekend1.trim()!=""||overrideWeekend2.trim()!="")&&(preoverrideWeekend1.trim()==""&&preoverrideWeekend2.trim()=="")){
		status="save";
	}
	else {
		status="update"
	}

	enableFields()
	document.forms[0].saveStatus.value = status;
	document.forms[0].method.value = "save";
	document.forms[0].submit();

}

/**
 * enableFields()
 * Method to enable entityId and country code while saving
 */
function enableFields(){
	document.forms[0].elements["countryOverride.id.entityId"].disabled = "";
	document.forms[0].elements["countryOverride.id.countryCode"].disabled = "";			
}


</script>
</head>


<body leftmargin="0" topmargin="0" marginheight="0"
	onLoad="ShowErrMsgWindow('${actionError}');setParentChildsFocus();setFocus(document.forms[0]);bodyOnLoad();"
	onunload="call()">
<form action="countryoverride.do" method="post">
	<input name="method" type="hidden" value="change">
	<input name="saveStatus" type="hidden" value="">
	<input type="hidden" name="countryOverride.countryName" value="${countryOverride.countryName}" />

	<div id="Country"
		style="position: absolute; left: 10px; top: 10px; width: 755px; height: 165px; border: 2px outset;"
		color="#7E97AF">

	<div id="Holiday"
		style="position: absolute; left: 8px; top: 4px; width: 755px; height: 72;">
	<table width="740" border="0" cellpadding="0" cellspacing="0"
		height="30">
		<tr height="24">
			<td width="140px"><b><fmt:message key="bookCode.entity" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px"><input type="text"
				name="countryOverride.id.entityId" style="width:120px;"
				cssClass="htmlTextAlpha" disabled="true"
				titleKey="tooltip.entityId" value="${countryOverride.id.entityId}"/></td>

			<td width="24">&nbsp;</td>
			<td width="200"><span id="entityName" name="entityName"
				class="spantext"></span></td>

		</tr>

		<tr height="24">
			<td width="140px"><b><fmt:message key="holiday.countryId" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px"><input type="text"
				name="countryOverride.id.countryCode" style="width:120px;"
				class="htmlTextAlpha" disabled="true"
				titleKey="tooltip.countryCode"  value="${countryOverride.id.countryCode}" /></td>
			<td width="24">&nbsp;</td>
			<td width="400"><span id="countryName" name="countryName"
				class="spantext"></span></td>
		</tr>
		<tr height="24">
			<td width="140px"><b><fmt:message key="label.country.defaultWeekends1" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px"><input type="text"
				name="countryOverride.weekend1" style="width:120px;"
				cssClass="htmlTextAlpha" disabled="true"
				titleKey="tooltip.defaultWeekend1" value="${countryOverride.weekend1}"/></td>
			<td width="24">&nbsp;</td>
			<td width="200">&nbsp;</td>
		</tr>
		<tr height="24">
			<td width="140px"><b><fmt:message key="label.country.defaultWeekends2" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px"><input type="text"
				name="countryOverride.weekend2" style="width:120px;"
				cssClass="htmlTextAlpha" disabled="true"
				titleKey="tooltip.defaultWeekend2" value="${countryOverride.weekend2}" /></td>
			<td width="24">&nbsp;</td>
			<td width="200">&nbsp;</td>
		</tr>
		<tr height="24">
			<td width="140px"><b><fmt:message key="label.country.overrideWeekends1" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px">
				<select id="overrideWeekend1Id" name="countryOverride.overrideWeekend1" tabindex="1" titleKey="tooltip.overrideWeekend1" class="htmlTextAlpha" style="width:120px">

    				<c:forEach items="${requestScope.dayList}" var="day">
						   <option
							  value="${day.value}"
							  <c:if test="${countryOverride.overrideWeekend1 == day.value}">selected="selected"</c:if>>
							  ${day.label}
						   </option>
					</c:forEach>
				</select>
			</td>
			<td width="24">&nbsp;</td>
			<td width="200">&nbsp;</td>
		</tr>
		<tr height="24">
			<td width="140px"><b><fmt:message key="label.country.overrideWeekends2" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="50px">
			

				<select id="overrideWeekend2Id" name="countryOverride.overrideWeekend2" tabindex="2" titleKey="tooltip.overrideWeekend2" class="htmlTextAlpha" style="width:120px">

    				<c:forEach items="${requestScope.dayList}" var="day">
						   <option
							  value="${day.value}"
							  <c:if test="${countryOverride.overrideWeekend2 == day.value}">selected="selected"</c:if>>
							  ${day.label}
						   </option>
					</c:forEach>
				</select>
			</td>
			<td width="24">&nbsp;</td>
			<td width="200">&nbsp;</td>
		</tr>

	</table>
	</div>
	</div>
	<div id="country"
		style="position: absolute; left: 695; top: 188px; width: 70px; height: 39px; visibility: visible;">
	<table width="60" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr height="25">

			<td align="Right"><a tabindex="5" href=#
				onclick="javascript:openWindow(buildPrintURL('print','Change Country Override Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
				src="images/help_default.GIF " name="Help" border="0"
				title='<fmt:message key="tooltip.helpScreen"/>'></a></td>
			<!--Start Code modified by  Chidambaranathan for Mantis 1461 for Tab Navigation on 12-May-2011-->
			<td align="right" id="Print"><a tabindex="5"
				onclick="printPage();" 
				onKeyDown="submitEnter(this,event)"
				onMouseOut="MM_swapImgRestore()"
				onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
				src="images/Print.gif " name="Print" border="0"
				title='<fmt:message key="tooltip.printScreen"/>'></a></td>
		</tr>
	</table>
	</div>
	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 10; top: 180; width: 755px; height: 39px; visibility: visible;">
	<div id="Holiday"
		style="position: absolute; left: 6; top: 4; width: 460; height: 15px; visibility: visible;">
	<table width="150" border="0" cellspacing="0" cellpadding="0"
		height="20">
		<tr>

			<td id="savebutton" width="70px"><a
				title='<fmt:message key="tooltip.save"/>' tabindex="3"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" 
				onKeyDown="submitEnter(this,event)"
				onclick="javascript:submitForm()"><fmt:message key="button.save" /></a></td>
			<td id="cancelbutton" width="70px"><a
				title='<fmt:message key="tooltip.cancel"/>' tabindex="4"
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)" 
				onKeyDown="submitEnter(this,event)"
				onclick="confirmClose('C');"><fmt:message key="button.cancel" /></a></td>
		</tr>
	</table>
	</div>
	</div>
	<!--End Code modified by  Chidambaranathan for Mantis 1461 for Tab Navigation on 12-May-2011-->
</form>
</body>
</html>
