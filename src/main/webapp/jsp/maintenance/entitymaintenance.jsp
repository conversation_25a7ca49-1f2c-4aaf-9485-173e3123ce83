<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ page import="org.swallow.util.SwtConstants"%>

<%@ page import="org.swallow.util.SwtUtil"%>
<html>
	<head>
		<title>
		<c:choose>
			<c:when test="${requestScope.methodName == 'add'}">
				<fmt:message key="entitymaintenance.addScreen" />
			</c:when>
			<c:when test="${requestScope.methodName == 'change'}">
				<fmt:message key="entitymaintenance.changeScreen" />
			</c:when>
			<c:otherwise>
				<fmt:message key="entitymaintenance.mainScreen" />
			</c:otherwise>
		</c:choose>

		</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<script language="JAVASCRIPT" src="js/datavalidation.js"></script>
<SCRIPT language="JAVASCRIPT">
mandatoryFieldsArray=["*"]
//variable to hold objTab from ddimagetabs
var objTab=null;
//variable to hold objTabLink from element tag
var objTabLink =null;
var selectedDayIndex = "${requestScope.selectedDayIndex}";
var dateFormat = '${sessionScope.CDM.dateFormat}';
var requestURL=new String('<%=request.getRequestURL()%>');
var appName = "<%=SwtUtil.appName%>";
var idy = requestURL.indexOf('/'+appName+'/');
requestURL=requestURL.substring(0,idy+1) ;
var oXMLHTTP = new XMLHttpRequest();
//variable to hold validateScriptFlag
var validateScriptFlag;
var isPositionLvlClicked = false;
var radioValue=0;

<c:if test="${'change' == requestScope.methodName}">
<c:if test="${'true' != requestScope.closePosLvl}">
	var initialtab = [${requestScope.selectedtabIndex}, "${requestScope.selectedtabName}"];
	var selectedtab = initialtab[${requestScope.selectedtabIndex}];
</c:if>
</c:if>
<c:if test="${'true' == requestScope.closePosLvl}">
	var initialtab = [2, "sc2"];
	var selectedtab = initialtab[2];
</c:if>

var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
var cancelcloseElements = new Array(2);
cancelcloseElements[0] = "cancelbutton";
cancelcloseElements[1] = "closebutton";
var currencyFormat = '${sessionScope.CDM.currencyFormat}';

	/**
	 * Method to load change entity screen
	 * @param methodName
	 */
	function submitFormChange(methodName){
		enableFields();
		document.forms[0].method.value = methodName;
		document.forms[0].selectedtabName.value = selectedtab;
		document.forms[0].selectedEntityCode.value=document.getElementById("entityName").innerText;
		/* Set an empty event handler for window close event for other form submit actions */
		setEmptyWindowHandler();
		document.forms[0].submit();
	}

	/**
	 * Method to load entity details for selectd entity
	 * @param methodName
	 */
	function submitFormDropDown(methodName){

		document.forms[0].method.value = methodName;
		document.forms[0].entPosLvlName.value = '${entPosLvlNameDetails}';
		/* Set an empty event handler for window close event for other form submit actions */
		setEmptyWindowHandler();
		document.forms[0].submit();
	}

	/*Start Code:Modified by Naseema.Sd for Mantis 1797 */
	/**
	 * Method to validate all text fields with their corresponding validation
	 */
	function textFieldValidation(){
		var result = false;
		if(validateField(document.forms[0].elements["entity.id.entityId"],'bookCode.entity','alphaNumPat'))
			if(validateField(document.forms[0].elements["entity.entityName"],'entityName','alphaNumPatWithSpace'))
				//if(validateField(document.forms[0].elements["entity.entServerTimeOffSet"],'entity.general.serverTomeOffSet','timePatWithSign'))
						if(validateField(document.forms[0].elements["entity.movementRetention"],'entity.predict.retentionParam.movement','numberPat'))
							if(validateField(document.forms[0].elements["entity.smallMovementRetain"],'entity.smallMovementRetain','numberPat'))
								if(validateField(document.forms[0].elements["entity.exchangeRateRetain"],'entity.exchangeRateRetain','numberPat'))
									if(validateField(document.forms[0].elements["entity.interestRateRetain"],'entity.interestRateRetain','numberPat'))
										if(validateField(document.forms[0].elements["entity.inputRetention"],'Number 3','numberPat'))
											if(validateField(document.forms[0].elements["entity.outputRetention"],'Number 3','numberPat'))
												if(validateCurrency(document.forms[0].elements["entity.cashFilterThresholdAsString"],'entity.cashFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
													if(validateCurrency(document.forms[0].elements["entity.securitiesFilterThresholdAsString"],'entity.securitiesFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
													  if(validateField(document.forms[0].elements["entity.sweepCutoffLeadTime"],'Number 3','numberPat'))
															if(validateCurrency(document.forms[0].elements["entity.largeSmallMovementThresholdAsString"],'entity.largeSmallMovementThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
																if(validateField(document.forms[0].elements["entity.balance"],'Number 3','numberPat'))
																  if(validateField(document.forms[0].elements["entity.ilmRetainAsString"],'Number 3','numberPat'))
																	if(validateField(document.forms[0].elements["entity.ilmCalcPastDaysAsString"],'Number 3','numberPat'))
																	if(validateField(document.forms[0].elements["entity.balanceLog"],'Number 3','numberPat'))
																		if(comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'Date From','Date To'))
																			if(validateCurrLimitForZero())
																				if (parseInt(document.forms[0].elements["entity.balance"].value)!=parseInt(document.forms[0].elements["entity.balanceLog"].value)&&(parseInt(document.forms[0].elements["entity.balance"].value)<parseInt(document.forms[0].elements["entity.balanceLog"].value))){
																					alert("<fmt:message key='alert.balanceLogRententionperiod'/>");
																					result = false;
																				}else{
																					result=true;
																				}




	return result;

	}
	 /*End Code:Modified by Naseema.Sd for Mantis 1797 */




	/*Start Code:Modified by Naseema.Sd for Mantis 1797 */
	/**
	 * Method to validate all text fields with their corresponding validation for change screen
	 */
	function textFieldValidationChange(){
		var result= false;
		if(validateField(document.forms[0].elements["entity.entityName"],'entityName','alphaNumPatWithSpace'))
			//if(validateField(document.forms[0].elements["entity.entServerTimeOffSet"],'entity.general.serverTomeOffSet','timePatWithSign'))
					if(validateField(document.forms[0].elements["entity.movementRetention"],'entity.predict.retentionParam.movement','numberPat'))
						if(validateField(document.forms[0].elements["entity.smallMovementRetain"],'entity.smallMovementRetain','numberPat'))
							if(validateField(document.forms[0].elements["entity.exchangeRateRetain"],'entity.exchangeRateRetain','numberPat'))
								if(validateField(document.forms[0].elements["entity.interestRateRetain"],'entity.interestRateRetain','numberPat'))
									if(validateCurrency(document.forms[0].elements["entity.cashFilterThresholdAsString"],'entity.cashFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
								    	if(validateCurrency(document.forms[0].elements["entity.securitiesFilterThresholdAsString"],'entity.securitiesFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
											if(validateField(document.forms[0].elements["entity.sweepCutoffLeadTime"],'Number 3','numberPat'))
												if(validateCurrency(document.forms[0].elements["entity.largeSmallMovementThresholdAsString"],'entity.largeSmallMovementThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
									         		if(validateField(document.forms[0].elements["entity.balance"],'Number 3','numberPat'))
														if(validateField(document.forms[0].elements["entity.balanceLog"],'Number 3','numberPat'))
															if(validateField(document.forms[0].elements["entity.inputRetention"],'Number 3','numberPat'))
															  if(validateField(document.forms[0].elements["entity.ilmRetainAsString"],'Number 3','numberPat'))
																if(validateField(document.forms[0].elements["entity.ilmCalcPastDaysAsString"],'Number 3','numberPat'))
																if(validateField(document.forms[0].elements["entity.outputRetention"],'Number 3','numberPat'))
																	if(document.forms[0].elements['entity.crrLimitToDateAsString'].value !="" ){
																		if(validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat))																 if(validateCurrLimitForZero())
																			if (parseInt(document.forms[0].elements["entity.balance"].value)!=parseInt(document.forms[0].elements["entity.balanceLog"].value)&&parseInt(document.forms[0].elements["entity.balance"].value)<parseInt(document.forms[0].elements["entity.balanceLog"].value)){
																				alert("<fmt:message key='alert.balanceLogRententionperiod'/>");
																				result = false;
																		}else{
																			result=true;
																		}
														}else{
															if (parseInt(document.forms[0].elements["entity.balance"].value)!=parseInt(document.forms[0].elements["entity.balanceLog"].value)&&parseInt(document.forms[0].elements["entity.balance"].value)<parseInt(document.forms[0].elements["entity.balanceLog"].value)){
																alert("<fmt:message key='alert.balanceLogRententionperiod'/>");
																result = false;
															}else{
																result=true;
															}
														}

	return result;
	}
	/*End Code:Modified by Naseema.Sd for Mantis 1797 */



 	/**
	 * Method to save / update entity details
	 * @param methodName
	 */
	function submitForm(methodName){
		var fieldValidation = textFieldValidation();
		if(fieldValidation){
			if(document.forms[0].elements['entity.crrLimitFromDateAsString'].value != "" ){
				if( validateDateField(document.forms[0].elements['entity.crrLimitFromDateAsString'],'crrLimitFromDateAsString',dateFormat)){
					var fromDate=document.forms[0].elements['entity.crrLimitFromDateAsString'].value;
					var toDate=document.forms[0].elements['entity.crrLimitToDateAsString'].value;
					var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
					if(compareDate == 1){
						alert("<fmt:message key='alert.interestCharges.toDate'/>");
						fieldValidationChange = false;
					}else{
						fieldValidationChange = true;
					}

				}else{
					fieldValidation = false;
				}
			}else if(document.forms[0].elements['entity.crrLimitToDateAsString'].value != "" ){
				if( validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat)){
					fieldValidation = true;
				}else{
					fieldValidation = false;
				}
			}
		}

		if(fieldValidation){
			if(validateForm(document.forms[0]) ){
			elementTrim(document.forms[0]);
				document.forms[0].method.value = methodName;
				document.forms[0].selectedWeekDay.value = document.forms[0].elements["entity.startDay"].value;
				//if(document.forms[0].elements["entity.entServerTimeOffSet"].value == "")
					//document.forms[0].elements["entity.entServerTimeOffSet"].value = "+00:00";
				/* Set an empty event handler for window close event for other form submit actions */
				setEmptyWindowHandler();
				document.forms[0].submit();
			}
		}
	}

	/**
	 * Method to load entity add screen
	 */
	function submitFormAdd(methodName){
		document.forms[0].method.value = methodName;
		/* Set an empty event handler for window close event for other form submit actions */
		setEmptyWindowHandler();
		document.forms[0].submit();
	}
	/**
	 * Method to load position level screen
	 * @param method
	 */
	function posLvl(method){

		<c:choose>
			<c:when test="${requestScope.methodName == 'list'}">
				document.forms[0].screenStatus.value = "readonly";
			</c:when>
			<c:when test="${requestScope.methodName == 'add'}">
				document.forms[0].screenStatus.value = "add";
			</c:when>
			<c:when test="${requestScope.methodName == 'change'}">
				document.forms[0].screenStatus.value = "change";
			</c:when>
		</c:choose>
		isPositionLvlClicked = true;
		if((document.forms[0].screenStatus.value =="readonly") || (validateForm(document.forms[0]))){
			objTabLink[1].onclick();
			var param = 'entity.do?method='+method;
			param += '&screenStatus='+document.forms[0].screenStatus.value;
			param += '&selectedEntityId='+document.forms[0].elements["entity.id.entityId"].value;
			param += '&selectedSweepPosLvl='+document.forms[0].elements["entity.sweepPosition"].value;
			param += '&selectedPreAdvPosLvl='+document.forms[0].elements["entity.preAdvicePosition"].value;
			param += '&selectedWeekDayIndex='+document.getElementById("entity.startDay").selectedIndex;
			openWindow(param,'positionLevelNameWindow','left=50,top=190,width=425,height=290,toolbar=0, resizable=yes, scrollbars=yes','true');
		}
	}

	/**
	 * Method to cancel changes in entity screen
	 * @param methodName
	 */
	function submitFormCancel(methodName){
		document.forms[0].method.value = methodName;
		/* Set an empty event handler for window close event for other form submit actions */
		setEmptyWindowHandler();
		document.forms[0].submit();
	}
	/**
	 * Method to update entity changes
	 * @param methodName
	 */
	function submitFormUpdate(methodName){
		var fieldValidationChange = textFieldValidationChange();
		if(fieldValidationChange){
			if(document.forms[0].elements['entity.crrLimitFromDateAsString'].value != "" ){
				if( validateDateField(document.forms[0].elements['entity.crrLimitFromDateAsString'],'crrLimitFromDateAsString',dateFormat)){
					var fromDate=document.forms[0].elements['entity.crrLimitFromDateAsString'].value;
					var toDate=document.forms[0].elements['entity.crrLimitToDateAsString'].value;
					var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
					if(compareDate == 1){
						alert("<fmt:message key='alert.interestCharges.toDate'/>");
						fieldValidationChange = false;
					}else{
						fieldValidationChange = true;
					}

				}else{
					fieldValidationChange = false;
				}
			}else if(document.forms[0].elements['entity.crrLimitToDateAsString'].value != "" ){
				if( validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat)){
					fieldValidationChange = true;
				}else{
					fieldValidationChange = false;
				}
			}
		}

		if(fieldValidationChange){
			if(validateFormChange(document.forms[0]) ){
			elementTrim(document.forms[0]);
				document.forms[0].selectedEntityCode.value = document.forms[0].elements["entity.entityName"].value;
				enableEntityName();
				document.forms[0].method.value = methodName;
				document.forms[0].selectedWeekDay.value = document.forms[0].elements["entity.startDay"].value;
				//if(document.forms[0].elements["entity.entServerTimeOffSet"].value == "")
					//document.forms[0].elements["entity.entServerTimeOffSet"].value = "+00:00";
				/* Set an empty event handler for window close event for other form submit actions */
				setEmptyWindowHandler();
				document.forms[0].submit();
			}
		}
	}
	/**
	 * Function to set an empty event handler for window close event.
	 * This method will prevent the resetting of session object with user's
	 * default entity details.
	 */
	function setEmptyWindowHandler(){
		window.onbeforeunload = emptyWindowHandler;
	}

	/**
	 * Function invoked to do nothing for other form submit actions except window close
	 */
	function emptyWindowHandler(){
	 unloadMyWindow();
	}
	/**
	 * Function resetes the session values with the user's default value while closing the screen
	 */
	function windowCloseHandler(){
		document.forms[0].method.value = 'cancelAdd';
		unloadMyWindow();
		document.forms[0].submit();
	}

	/**
	 * Method to cancel changes in entity screen
	 * @param methodName
	 */
	function submitCancel(methodName){
			<%
			String canButSts = (String) request.getAttribute(SwtConstants.CAN_BUT_STS);
			if (canButSts != null && canButSts.equals(SwtConstants.STR_TRUE)) {
			%>
			var yourstate=window.confirm('<fmt:message key="entity.CloseConfirm"/>');
			if (yourstate==true){
				// Submit the form to reset the changes made by the user for the selected entity.
				document.forms[0].method.value = methodName;
				/* Code modified to avoid sending the request twice for resetting the session details */
				setEmptyWindowHandler();
				document.forms[0].submit();
				window.close();
			}
			<% }else{%>
			document.forms[0].method.value = methodName;
			window.close();
			<%}%>
	}

	/**
	 * Method to enable or disable entity name
	 */
	function enableEntityName() {
		<c:if test="${requestScope.methodName != 'change'}">
			document.getElementById("entityName").disabled = "";
		</c:if>
		document.forms[0].elements["entity.id.entityId"].disabled = "";
	}

/**
 * Method to delete entity
 */
function submitFormDelete(methodName) {
	document.forms[0].method.value = methodName;
		var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
		if (yourstate==true){
			/* Set an empty event handler for window close event for other form submit actions */
			setEmptyWindowHandler();
			document.forms[0].submit();
		}
	}
	/**
	 * Method to enable fields for change screen
	 */
	function enableFields() {

		document.forms[0].elements["entity.domesticCurrency"].disabled = "";
		document.forms[0].elements["entity.reprotingCurrency"].disabled = "";
		document.forms[0].elements["entity.countryId"].disabled = "";
		<c:if test="${requestScope.methodName != 'change'}">
			document.getElementById("entityName").disabled = "";
		</c:if>

		document.getElementById("reportingCurrencyName").disabled = "";
		document.getElementById("countryName").disabled = "";
		document.getElementById("currencyName").disabled = "";
		//document.forms[0].elements["entity.entServerTimeOffSet"].disabled = "";
		document.forms[0].elements["entity.securitiesFilterThresholdAsString"].disabled = "";
		document.forms[0].elements["entity.movementRetention"].disabled = "";
		document.forms[0].elements["entity.cashFilterThresholdAsString"].disabled = "";
		document.forms[0].elements["entity.metaGroupLevel1"].disabled = "";
		document.forms[0].elements["entity.metaGroupLevel2"].disabled = "";
		document.forms[0].elements["entity.metaGroupLevel3"].disabled = "";
		document.forms[0].elements["entity.groupLevel1"].disabled = "";
		document.forms[0].elements["entity.groupLevel2"].disabled = "";
		document.forms[0].elements["entity.groupLevel3"].disabled = "";
		(document.forms[0].elements["entity.exchangeRateFormat"])[0].disabled = "";
		(document.forms[0].elements["entity.exchangeRateFormat"])[1].disabled = "";
		document.forms[0].elements["entity.entityBIC"].disabled = "";
		document.forms[0].elements["entity.interestRateRetain"].disabled = "";
		document.forms[0].elements["entity.exchangeRateRetain"].disabled = "";
		document.forms[0].elements["entity.sweepPosition"].disabled = "";
		document.forms[0].elements["entity.preAdvicePosition"].disabled = "";
		document.forms[0].elements["entity.internalBalance"].disabled = "";
		document.forms[0].elements["entity.externalBalance"].disabled = "";
		document.forms[0].elements["entity.sweepCutoffLeadTime"].disabled = "";
		document.forms[0].elements["entity.smallMovementRetain"].disabled = "";
		document.forms[0].elements["entity.largeSmallMovementThresholdAsString"].disabled = "";
		document.forms[0].elements["entity.balance"].disabled = "";
		document.forms[0].elements["entity.balanceLog"].disabled = "";
		document.forms[0].elements["entity.entTimeZone"].disabled = "";


	}

function raiseAlert() {
	var yourstate = window.confirm('<fmt:message key="generalParams.timeZoneRegion.alert"/>');
	if (!yourstate){
		document.forms[0].elements["entity.entTimeZone"].value=selectedTimeZoneRegion;
		document.getElementById("timeZoneOffset").innerHTML= selectedTimeZoneOffset;
	    }
}


	/**
	 * Method to load change entity details
	 */
	function bodyOnLoadChange(){
		if (document.forms[0].elements["entity.exchangeRateFormat"][0].checked == true){
			radioValue = 0;
		}else{
			radioValue = 1;
		}
		<c:if test="${methodName != 'change'}">
			document.getElementById("entityName").innerText = '${entityText}';
		</c:if>
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
}
	/**
	 * Method call on body onload for defaul entity of user
	 */
	function bodyOnLoad(){
		<c:if test="${requestScope.methodName != 'change'}">
			var dropBox1 = new SwSelectBox(document.forms[0].elements["entity.id.entityId"],document.getElementById("entityName"));
			var dropBox2 = new SwSelectBox(document.forms[0].elements["entity.entTimeZone"],document.getElementById("timeZoneOffset"));
			selectedTimeZoneRegion=  document.forms[0].elements["entity.entTimeZone"].value;
			selectedTimeZoneOffset= document.getElementById("timeZoneOffset").innerHTML;
		</c:if>
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;

		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {%>
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		<%}%>


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		<%}%>


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		<%}%>


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
			document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		<%}%>

		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
			document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
		<%}%>


		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";

		if(menuEntityCurrGrpAccess != 0){
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}
		/*
		 * New condition added to disable the Add button only when the user has View access for this screen.
		 */
		if(document.forms[0].menuAccessId.value == 1){
			document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		}

		var obj = document.forms[0].elements["entity.id.entityId"];
		if(obj.value == "") {
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}
		<c:if test="${requestScope.entitySaved == 'true'}">
		alert("<fmt:message key='entitymaintenance.alert.entitySaved'/>");
		</c:if>


	}
	/**
	 * Method to load controls
	 */
	function loadControls(){
		var dropBox2 = new SwSelectBox(document.forms[0].elements["entity.domesticCurrency"],document.getElementById("currencyName"));
		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		var dropBox3 = new SwSelectBox(document.forms[0].elements["entity.reprotingCurrency"],document.getElementById("reportingCurrencyName"));
		var dropBox3 = new SwSelectBox(document.forms[0].elements["entity.countryId"],document.getElementById("countryName"));
		new SwSelectBox(document.forms[0].elements["entity.sweepPosition"],document.getElementById("sweepPosLvlName"));
		new SwSelectBox(document.forms[0].elements["entity.preAdvicePosition"],document.getElementById("preAdvicePosLvlName"));
		new SwSelectBox(document.forms[0].elements["entity.centralBankAccount"],document.getElementById("acctName"));
		new SwSelectBox(document.forms[0].elements["entity.internalBalance"],document.getElementById("internalPosLvlName"));
		new SwSelectBox(document.forms[0].elements["entity.externalBalance"],document.getElementById("externalPosLvlName"));
		new SwSelectBox(document.forms[0].elements["entity.startDay"],"");
		<c:if test="${requestScope.methodName != 'add' && requestScope.methodName != 'change'}">
			document.forms[0].elements["entity.startDay"].value = document.getElementById("entity.startDay")[selectedDayIndex].innerText;
		</c:if>
	}
	/**
	 * Method called when entity screen on load
	 */
	function bodyOnLoadAdd(){
		loadTabs();
		document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CHG_BUT_STS)) ) {%>
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
		<%}%>


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.DEL_BUT_STS)) ) {%>
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		<%}%>


		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.SAV_BUT_STS)) ) {%>
			document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("savebutton").innerHTML = document.getElementById("savedisablebutton").innerHTML;
		<%}%>

		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
			document.getElementById("cancelbutton").innerHTML = document.getElementById("cancelenablebutton").innerHTML;
		<%}else{ %>

			document.getElementById("cancelbutton").innerHTML = document.getElementById("canceldisablebutton").innerHTML;
		<%}%>

		 <%if ( (SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.ADD_BUT_STS)) ) {
		   if ( !(SwtConstants.STR_TRUE).equals(request.getAttribute(SwtConstants.CAN_BUT_STS)) ) {%>
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
		<%}
		}else{ %>

			document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
		<%}%>

		document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
		var dropBox2 = new SwSelectBox(document.forms[0].elements["entity.entTimeZone"],document.getElementById("timeZoneOffset"));
		selectedTimeZoneRegion=  document.forms[0].elements["entity.entTimeZone"].value;
		selectedTimeZoneOffset= document.getElementById("timeZoneOffset").innerHTML;
	}
	/**
	 * Method to load tabs
	 */
	function loadTabs(){
		//variable to hold the tab
		objTab=document.getElementById("ddimagetabs");
		//variable to hold objTabLink from element tag
		objTabLink =objTab.getElementsByTagName("A");
	}
	/**
	 * Method to populate drop boxes
	 */
	function populateDropBoxes(){
		loadTabs();
		bodyOnLoad();
		loadControls();
		ShowErrMsgWindow('${actionError}');
	}
	/**
	 * Method to load drop boxes for add screen
	 */
	function populateDropBoxesAdd(){
		bodyOnLoadAdd();

		window.onbeforeunload = windowCloseHandler;
		<c:choose>
			<c:when test="${requestScope.methodName == 'change'}">
				bodyOnLoadChange();
				setFocus(document.forms[0]);
			</c:when>
			<c:when test="${requestScope.methodName == 'add'}">
				document.forms[0].elements["entity.exchangeRateFormat"][0].checked = true;
			</c:when>
		</c:choose>
		loadControls();
		ShowErrMsgWindow('${actionError}');
	}
	/**
	 * Method to validate form values
	 */
	function validateForm(objForm) {
		var elementsRef = new Array(2);
		<c:if test="${requestScope.methodName == 'list'}">
		if (document.forms[0].elements['entityId'])
			elementsRef[0] = objForm.elements["entityId"];
			elementsRef[1] = objForm.elements["entityName"];
		</c:if>

		<c:if test="${requestScope.methodName == 'add' || requestScope.methodName == 'change'}">
			elementsRef[0] = objForm.elements["entity.id.entityId"];
			elementsRef[1] = objForm.elements["entity.entityName"];
		</c:if>
		elementTrim(document.forms[0]);
		validateScriptFlag = validate(elementsRef);
		if (validateScriptFlag)
			validateScriptFlag = validateField(document.forms[0].elements["entity.id.entityId"], 'bookCode.entity', 'alphaNumPat') &&
					validateField(document.forms[0].elements["entity.entityName"], 'entityName', 'alphaNumPatWithSpace');
		if (validateScriptFlag && selectedtab == "sc1") {
			validateGenaral(objForm)
			if (validateScriptFlag) {
				if (isPositionLvlClicked)
					validatePredictForPos(objForm)
				else
					validatePredict(objForm)
			}
		} else if (validateScriptFlag && selectedtab == "sc2") {
			if (isPositionLvlClicked)
				validatePredictForPos(objForm)
			else
				validatePredict(objForm)
			if (validateScriptFlag)
				validateGenaral(objForm)
		} else if (!isPositionLvlClicked) {
			if (validateScriptFlag)
				validatePredict(objForm)
		}
		return validateScriptFlag;
	}


/*Start Code:Modified by Naseema.Sd for Mantis 1797 */

/**
 * Method to validate general tab controls
 * @param objFrom
 */
function validateGenaral(objForm) {
	var elementsRef = new Array(3);
	objTabLink[0].onclick();

	elementsRef[0] = objForm.elements["entity.domesticCurrency"];
	elementsRef[1] = objForm.elements["entity.reprotingCurrency"];
	elementsRef[2] = objForm.elements["entity.countryId"];
	validateScriptFlag = validate(elementsRef);
// 		if(validateScriptFlag){
// 			validateScriptFlag = validateField(document.forms[0].elements["entity.entServerTimeOffSet"],'entity.general.serverTomeOffSet','timePatWithSign');
// 		}

	}
	/*End Code:Modified by Naseema.Sd for Mantis 1797 */


	/**
	 * Method to validate predict tab controls
	 * @param objFrom
	 */
	function validatePredict(objForm){
		objTabLink[1].onclick();
		var elementsRef = new Array(2);
		elementsRef[0] = objForm.elements["entity.preAdvicePosition"];
		elementsRef[1] = objForm.elements["entity.sweepPosition"];
		validateScriptFlag = validate(elementsRef);
		if(validateScriptFlag){
			validateScriptFlag =validateField(document.forms[0].elements["entity.movementRetention"],'entity.predict.retentionParam.movement','numberPat') &&
			validateField(document.forms[0].elements["entity.smallMovementRetain"],'entity.smallMovementRetain','numberPat') &&
			validateField(document.forms[0].elements["entity.exchangeRateRetain"],'entity.exchangeRateRetain','numberPat')&&
			validateField(document.forms[0].elements["entity.interestRateRetain"],'entity.interestRateRetain','numberPat')&&
			validateField(document.forms[0].elements["entity.balance"],'Number 3','numberPat')&&
			validateField(document.forms[0].elements["entity.balanceLog"],'Number 3','numberPat') &&
			validateField(document.forms[0].elements["entity.inputRetention"],'Number 3','numberPat')&&
			validateField(document.forms[0].elements["entity.outputRetention"],'Number 3','numberPat')&&
			validateCurrency(document.forms[0].elements["entity.cashFilterThresholdAsString"],'entity.cashFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			validateCurrency(document.forms[0].elements["entity.securitiesFilterThresholdAsString"],'entity.securitiesFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			validateField(document.forms[0].elements["entity.sweepCutoffLeadTime"],'Number 3','numberPat')&&
			validateCurrency(document.forms[0].elements["entity.largeSmallMovementThresholdAsString"],'entity.largeSmallMovementThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'Date From','Date To');
		}
		if(validateScriptFlag){
			if (parseInt(document.forms[0].elements["entity.balance"].value)!=parseInt(document.forms[0].elements["entity.balanceLog"].value)&&		parseInt(document.forms[0].elements["entity.balance"].value)<parseInt(document.forms[0].elements["entity.balanceLog"].value)){
					alert("<fmt:message key='alert.balanceLogRententionperiod'/>");
					validateScriptFlag = false;
			}else{
				validateScriptFlag=true;
			}
		}
		if(validateScriptFlag && document.forms[0].elements['entity.crrLimitFromDateAsString'].value !="" ){
			validateScriptFlag = validateDateField(document.forms[0].elements['entity.crrLimitFromDateAsString'],'crrLimitFromDateAsString',dateFormat);

		}
		if(validateScriptFlag && document.forms[0].elements['entity.crrLimitToDateAsString'].value !="" ){
			validateScriptFlag = validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat);

		}
		if(validateScriptFlag){
			validateScriptFlag = validateCurrLimitForZero();
		}

	}


	function validatePredictForPos(objForm){
		objTabLink[1].onclick();
		if(validateScriptFlag){
			validateScriptFlag =validateField(document.forms[0].elements["entity.movementRetention"],'entity.predict.retentionParam.movement','numberPat') &&
			validateField(document.forms[0].elements["entity.smallMovementRetain"],'entity.smallMovementRetain','numberPat') &&
			validateField(document.forms[0].elements["entity.exchangeRateRetain"],'entity.exchangeRateRetain','numberPat')&&
			validateField(document.forms[0].elements["entity.interestRateRetain"],'entity.interestRateRetain','numberPat')&&
			validateField(document.forms[0].elements["entity.balance"],'Number 3','numberPat')&&
			validateField(document.forms[0].elements["entity.balanceLog"],'Number 3','numberPat') &&
			validateField(document.forms[0].elements["entity.inputRetention"],'Number 3','numberPat')&&
			validateField(document.forms[0].elements["entity.outputRetention"],'Number 3','numberPat')&&
			validateCurrency(document.forms[0].elements["entity.cashFilterThresholdAsString"],'entity.cashFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			validateCurrency(document.forms[0].elements["entity.securitiesFilterThresholdAsString"],'entity.securitiesFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			validateField(document.forms[0].elements["entity.sweepCutoffLeadTime"],'Number 3','numberPat')&&
			validateCurrency(document.forms[0].elements["entity.largeSmallMovementThresholdAsString"],'entity.largeSmallMovementThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)&&
			comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'Date From','Date To');
		}

		if(validateScriptFlag){
			if (parseInt(document.forms[0].elements["entity.balance"].value)!=parseInt(document.forms[0].elements["entity.balanceLog"].value)&&		parseInt(document.forms[0].elements["entity.balance"].value)<parseInt(document.forms[0].elements["entity.balanceLog"].value)){
					alert("<fmt:message key='alert.balanceLogRententionperiod'/>");
					validateScriptFlag = false;
			}else{
				validateScriptFlag=true;
			}
		}

		if(validateScriptFlag && document.forms[0].elements['entity.crrLimitFromDateAsString'].value !="" ){
			validateScriptFlag = validateDateField(document.forms[0].elements['entity.crrLimitFromDateAsString'],'crrLimitFromDateAsString',dateFormat);

		}

		if(validateScriptFlag && document.forms[0].elements['entity.crrLimitToDateAsString'].value !="" ){
			validateScriptFlag = validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat);

		}

	if(validateScriptFlag){
			validateScriptFlag = validateCurrLimitForZero();
			}

	}
	/**
	 * Method to validate controls for change screen
	 */
	function validateFormChange(objForm){
		var elementsRef = new Array(6);
		<c:if test="${requestScope.methodName == 'list'}">
			if(document.forms[0].elements['entityId'])
				elementsRef[0] = objForm.elements["entityId"];
		</c:if>

		<c:if test="${requestScope.methodName == 'add' || requestScope.methodName == 'change'}">
			elementsRef[0] = objForm.elements["entity.id.entityId"];
		</c:if>
		elementsRef[1] = objForm.elements["entity.domesticCurrency"];
		elementsRef[2] = objForm.elements["entity.reprotingCurrency"];
		elementsRef[3] = objForm.elements["entity.countryId"];
		elementsRef[4] = objForm.elements["entity.sweepPosition"];
		elementsRef[5] = objForm.elements["entity.preAdvicePosition"];

		return validate(elementsRef);
	}

	/**
	 * Method to load editable data screen
	 */
	function buildEditableDate(methodName) {
		document.forms[0].calleeMethodName.value = '${methodName}';
		var param = 'entity.do?method='+methodName;
		param += '&selectedEntityId=' +document.forms[0].elements["entity.id.entityId"].value;
		param += '&methodName=' + document.forms[0].calleeMethodName.value;
		return param;
	}

</SCRIPT>

<script language="JAVASCRIPT">
		// variable to check currency
		var isCurrencyChanged = false;
		var cal = new CalendarPopup("caldiv",true,"calFrame");
		cal.offsetX = -65;
		cal.offsetY = -75;
		var cal2 = new CalendarPopup("caldiv",true,"calFrame");
		cal2.offsetX = -85;
		cal2.offsetY = -75;
		var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
		var dateSelected= true;


	/**
	 * Method to store the previous From date while changing the From date
	 */
	function storeFromDate(){
		document.forms[0].elements['preCrrLimitFromDateAsString'].value=document.forms[0].elements['entity.crrLimitFromDateAsString'].value;
	}

	/**
	 * Method to store the previous TO date while changing the TO date
	 */
	function storeToDate(){
		document.forms[0].elements['preCrrLimitToDateAsString'].value=document.forms[0].elements['entity.crrLimitToDateAsString'].value;
	}

		/**
		 * This Funtion is used to dvalidate and display the From date
		 * @pram obj
		 */
	function onFromDateChange(obj){

		if(dateSelected){
			if(document.forms[0].elements['entity.crrLimitFromDateAsString'].value !="" && validateDateField(document.forms[0].elements['entity.crrLimitFromDateAsString'],'crrLimitFromDateAsString',dateFormat)){
				var fromDate=document.forms[0].elements['entity.crrLimitFromDateAsString'].value;
				var preFromDate=document.forms[0].elements['preCrrLimitFromDateAsString'].value;
				var toDate=document.forms[0].elements['entity.crrLimitToDateAsString'].value;
				var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
				if(compareDate == 1){
					alert("<fmt:message key='alert.fromDateShouldBeLess'/>");
					document.forms[0].elements['entity.crrLimitFromDateAsString'].value=preFromDate;
				}
			}
		}

	}

	/**
	 * This Funtion is used to dvalidate and display the To date
	 */
	function onToDateChange(){
		var returnVal = false;
		if(dateSelected){
			if(document.forms[0].elements['entity.crrLimitToDateAsString'].value !="" &&
			validateDateField(document.forms[0].elements['entity.crrLimitToDateAsString'],'crrLimitToDateAsString',dateFormat)){
				var fromDate=document.forms[0].elements['entity.crrLimitFromDateAsString'].value;
				var toDate=document.forms[0].elements['entity.crrLimitToDateAsString'].value;
				var preToDate=document.forms[0].elements['preCrrLimitToDateAsString'].value;
				var compareDate=compareDates(fromDate,dateFormatValue,toDate,dateFormatValue);
				if(compareDate == 1){
					alert("<fmt:message key='alert.interestCharges.toDate'/>");
					document.forms[0].elements['entity.crrLimitToDateAsString'].value=preToDate;
					returnVal = false;
				}else{
					returnVal = true;
				}
			}
			else{
				returnVal = true;
			}

		}
		return returnVal;
	}



	/**
	 * Method to validate currency limit for zero
	 */
   function validateCurrLimitForZero(){
		if(validateCurrency(document.forms[0].elements["entity.crrLimitAsString"],'entity.crrLimitAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)){
		   if(isCancelorCloseButtonPressed()==false){
		       if(parseFloat(document.forms[0].elements['entity.crrLimitAsString'].value) == 0){
		           alert('<fmt:message key="entity.crrlimit.zerovalue"/>');
				   return false;

		       }else{
			      return validateCurrency(document.forms[0].elements["entity.crrLimitAsString"],'entity.crrLimitAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)
			   }
			}else{
			   return validateCurrency(document.forms[0].elements["entity.crrLimitAsString"],'entity.crrLimitAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)
		  }
		 }else{
		   return false;
		 }

	}

	/**
	 * Method to validate from date on key press event
	 * @pram obj
	 */
	function onFromDateKeyPress(obj,e){
		dateSelected = false;
		var event = (window.event|| e);
		var obj2 = document.forms[0].elements['entity.crrLimitToDateAsString'];
		if(event.keyCode == 9){
			if(validateField(obj,'crrLimitFromDateAsString',dateFormat) && validateField(obj2,'crrLimitToDateAsString',dateFormat)){
				if(comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'From Date','To Date')){
				} else {
					return false;
				}
			} else {
			return false;
			}
		}

		if(event.keyCode == 13){
			if(validateField(obj,'crrLimitFromDateAsString',dateFormat) && validateField(obj2,'crrLimitToDateAsString',dateFormat)){
				if(comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'From Date','To Date')){
				} else {
					return false;
				}
			} else {
				return false;
			}
		}
	}

	/**
	 * Method to validate To date on key press event
	 * @pram obj
	 */
	function onToDateKeyPress(obj,e){
		var event = (window.event|| e);
		dateSelected = false;
		var obj2 = document.forms[0].elements['entity.crrLimitFromDateAsString'];
		if(event.keyCode == 9){
			if(validateField(obj,'crrLimitToDateAsString',dateFormat) && validateField(obj2,'crrLimitFromDateAsString',dateFormat)){
				if(comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'From Date','To Date')){
				} else {
					return false;
					}
				} else {
					return false;
					}
			}
		if(event.keyCode == 13){
			if(validateField(obj,'crrLimitToDateAsString',dateFormat) && validateField(obj2,'crrLimitFromDateAsString',dateFormat)){
				if(comparedates(document.forms[0].elements['entity.crrLimitFromDateAsString'].value,document.forms[0].elements['entity.crrLimitToDateAsString'].value,dateFormat,'From Date','To Date')){
				} else {
					return false;
					}
				} else {
					return false;
				}
			}
	}

	/**
	 * Method to vslidate biccode
	 */
	function checkalphanumeric(element){
		if(validateField(element,'acctbiccode','alphaNumPat')&& element.value!="")
			{
				validateField(element,'acctbiccode','biccode');
			}
			return true;
		}

	/**
	 * Method to change exchange rate
	 */
	function changeinexchange(num){
		if(radioValue != num){
			var yourstate=window.confirm('<fmt:message key="ExchangeRate.confirm.IntoNew"/>');
			if (yourstate!=true){
				document.forms[0].elements["entity.exchangeRateFormat"][radioValue].checked = true;
			}

		}
	}

	/**
	 * This fumtion is used to store the Previous entity brfoe changeing the entity
	 */
	function getOldEntityId(){
		//Assigning the old entity while onfocus
		document.forms[0].elements["oldEntityId"].value=document.forms[0].elements["entity.id.entityId"].value;
	}

	/**
	 * This function is used to find the change in entity and if any changes means,send request to server side using AJAX
	 */
	function getNewEntityId(){
		//Assining old entity id to variable
		var oldEntityId=document.forms[0].elements["oldEntityId"].value;
		//Assigning the new entity ID
		var newEntityId=document.forms[0].elements["entity.id.entityId"].value;
		//comparing the Entityid
		if(oldEntityId!=newEntityId){
			//Framing url for request
			var sURL = requestURL + appName +"/entity.do?method=setNewEntityIdPosLvl"
			sURL=sURL+"&oldEntityId="+oldEntityId;
			sURL=sURL+"&newEntityId="+newEntityId;
			oXMLHTTP.open( "POST", sURL, false );
			//sending request
			oXMLHTTP.send();
		}
	}

	/**
	  * getAccountList
	  *
	  * This method is used to get the collection of account list.
	  */
	function getAccountList(e) {
		var event = (window.event|| e);
		var target = (event.srcElement || event.target);
		// set true to indicate currency changed
		isCurrencyChanged = true;
		// get selected entity
		var entityId = document.forms[0].elements["entity.id.entityId"].value;
		// check entity Id is null
		if( entityId !=null && 0 != entityId.length){
			// Get central bank account id
			var accSelectElement =  document.forms[0].elements["entity.centralBankAccount"];
			//Application name is picked up from property file
			var appName = "<%=SwtUtil.appName%>";
			// Get domestic currency code
			var currencyCode = document.forms[0].elements["entity.domesticCurrency"].value;
			//get the requestURL
			var requestURL = new String('<%=request.getRequestURL()%>');
			// get index of application name
			var idy = requestURL.indexOf('/'+appName+'/');
			//frame the requestURL
			requestURL=requestURL.substring(0,idy+1) ;
			// Get account list for selected entity and currency through ajax
			requestURL = requestURL + appName+"/entity.do?method=getCentralBankAccountList";
			requestURL = requestURL + "&entityId=" + entityId;
			requestURL = requestURL + "&currencyCode=" + currencyCode;
			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open( "POST", requestURL, false );
			//send the request for list
			oXMLHTTP.send();
			//get the response text
			var listValue=new String(oXMLHTTP.responseText);
			listValue = listValue.split('\n');
			//remove items
			while(accSelectElement.options.length > 0)
			{
			    accSelectElement.remove(0);
			}
			accSelectElement.onchange = function () {
				var selectedIndex = accSelectElement.selectedIndex;
				if (selectedIndex < 0) selectedIndex = 0;
				var selectedOption = accSelectElement.options[selectedIndex];
				// Need to check
				var selectelement = null;
				if (accSelectElement != undefined && accSelectElement != null) {
					selectelement = accSelectElement;
				} else {
					selectelement = target;
				}
				var mainobject = selectelement.parent;
				mainobject.selectElement = accSelectElement;
				var optionsArray = mainobject.selectElement.options;
				var noofoptions = optionsArray.length;
				var selectedIndex = mainobject.selectElement.selectedIndex;
				if (selectedIndex >= 0) {
					var selectedOption = optionsArray[selectedIndex];
					document.getElementById("acctName").innerText = selectedOption.getAttribute("str");
				}
			}
			var index = 0;
			//add the main account into option
			for(var i=0;i < listValue.length -1;i++){
				var lblValue = listValue[i].split('~~~');
				var opt = document.createElement("option");
				opt.text = lblValue[1];
				opt.value = lblValue[0];
				// Need to check
				opt.setAttribute("str", opt.text);
				opt.text = opt.value;

				accSelectElement.options.add(opt);
			}
			document.getElementById("acctName").innerText = accSelectElement.options[0].value;
		}
	}

	/**
	  * changeAmountsOnCurrency
	  *
	  * This method is used to Currency format of all currency fields depending on the domestic currency.
	  */
	function changeAmountsOnCurrency() {
        // Get the value cashFilterThreshold
        var cashFilter = document.forms[0].elements["entity.cashFilterThresholdAsString"];
        // Get the value securitiesFilterThreshold
        var securitesFilter = document.forms[0].elements["entity.securitiesFilterThresholdAsString"];
        // Get the largeSmallMovementThreshold
        var largeSmallMntThd = document.forms[0].elements["entity.largeSmallMovementThresholdAsString"];
        // Get the crrLimit
        var crrLimit = document.forms[0].elements["entity.crrLimitAsString"];
        // Condition is checked for currency changed
        if (isCurrencyChanged == true) {
                if (cashFilter.value.length > 0 && validateCurrency(cashFilter, 'entity.cashFilterThresholdAsString', currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
					if (securitesFilter.value.length > 0 && validateCurrency(securitesFilter, 'entity.securitiesFilterThresholdAsString', currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value))
						if (largeSmallMntThd.value.length > 0 && validateCurrency(largeSmallMntThd, 'entity.largeSmallMovementThresholdAsString', currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)) {
                        // Change the status
                        isCurrencyChanged = false;
                        if (parseFloat(document.forms[0].elements['entity.crrLimitAsString'].value) == 0) document.forms[0].elements['entity.crrLimitAsString'].value = "";
                        if (crrLimit.value.length > 0 && validateCurrLimitForZero()) return;
                }
        }
}



    /*Start Code:Added by Naseema.Sd for Mantis 1797 */
    /**
	 * Method called when fromdate onclick
	  */
    function fromDateClick(){
		setEmptyWindowHandler();
		cal.select(document.forms[0].elements['entity.crrLimitFromDateAsString'],'datelink',dateFormatValue);
		storeFromDate();
		dateSelected=true;
		return false;
	}


	/**
	 * Method called when todate onclick
	  */
	function toDateClick(){
		setEmptyWindowHandler();
		cal2.select(document.forms[0].elements['entity.crrLimitToDateAsString'],'datelink2',dateFormatValue);
		storeToDate();
		dateSelected=true;
		return false;
  	}
	/*End Code:Added by Naseema.Sd for Mantis 1797 */

 </script>
</head>
	<c:choose>
	<c:when test="${requestScope.methodName == 'list'}">
	<body leftmargin="0" topmargin="0" marginheight="0"
		  onLoad="setParentChildsFocus();populateDropBoxes();setFocus(document.forms[0]);"
		  onunload="call()">
	</c:when>
	<c:when test="${requestScope.methodName == 'add'}">
	<body leftmargin="0" topmargin="0" marginheight="0"
		  onLoad="setParentChildsFocus();populateDropBoxesAdd();setFocus(document.forms[0]);"
		  onunload="call()">
	</c:when>
	<c:when test="${requestScope.methodName == 'change'}">
	<body leftmargin="0" topmargin="0" marginheight="0"
		  onLoad="setParentChildsFocus();populateDropBoxesAdd();"
		  onunload="call()">
	</c:when>
	</c:choose>
<form action="entity.do" method="post" >
	<bean:define id="currencyMast" name="currencyMaster" scope="request" />
	<bean:define id="countries" name="countries" scope="request" />
	<bean:define id="entities" name="entities" scope="request" />
	<input name="cancelVal" type="hidden" value="${cancelVal}">
	<input name="oldValue" type="hidden" value="${oldValue}">
	<input name="method" type="hidden" value="list">
	<input name="selectedEntityId" type="hidden">
	<input name="selectedEntityCode" type="hidden">
	<input name="oldValue" type="hidden" value="${oldValue}">
	<input name="entPosLvlName" type="hidden"
		value='${entPosLvlNameDetails}'>
	<input name="screenStatus" type="hidden">
	<input name="calleeMethodName" type="hidden">
	<input name="selectedtabName" type="hidden">
	<input name="selectedWeekDay" type="hidden">
	<input name="selectedWeekDayIndex" type="hidden">
	<input name="menuAccessId" type="hidden">
    <input name="preCrrLimitFromDateAsString" type="hidden" value="">
    <input name="preCrrLimitToDateAsString" type="hidden" value="">
    <input name="oldEntityId" type="hidden" value="">
	<div id="EntityMaintenance"
		style="position: absolute; left: 8px; top: 8px; width: 768px; height: 40px; border: 2px outset;"
		color="#7E97AF">
	<div id="EntityMaintenance"
		style="position: absolute; left: 8px; top: 4px; width: 765px; height: 420;">
	<table width="573px" border="0" cellpadding="0" cellspacing="0"
		height="30">
	<tr>
    <c:choose>
        <c:when test="${requestScope.methodName != 'list'}">
            <c:choose>
                <c:when test="${requestScope.methodName == 'change'}">
                    <td width="30px"><b><fmt:message key="bookCode.entity" /></b></td>
                    <td width="28">&nbsp;</td>
                    <td width="140">
                        <input type="text"
                               class="htmlTextAlpha"
                               name="entity.id.entityId"
                               maxlength="12"
                               style="width:140px;"
                               titleKey="entity.EnterEntityID"
                               value="${entity.id.entityId}"
                               <c:if test="${requestScope.screenFieldsStatusChange == 'true'}">disabled="true"</c:if> />
                    </td>
                </c:when>
                <c:when test="${requestScope.methodName == 'add'}">
                    <td width="30px"><b><fmt:message key="bookCode.entity" /></b>*</td>
                    <td width="28">&nbsp;</td>
                    <td width="140">
                        <input type="text"
                               class="htmlTextAlpha"
                               name="entity.id.entityId"
                               maxlength="12"
                               style="width:140px;"
                               titleKey="entity.EnterEntityID"
                               value="${entity.id.entityId}"
                               onchange="return validateField(this,'bookCode.entity','alphaNumPat');"
                               onblur="getNewEntityId();"
                               onfocus="return getOldEntityId();"
                               tabindex="1" />
                    </td>
                </c:when>
            </c:choose>
        </c:when>
        <c:otherwise>
            <td width="30px"><b><fmt:message key="bookCode.entity" /></b></td>
            <td width="28">&nbsp;</td>
            <td width="140">
                <select name="entity.id.entityId"
                        class="htmlTextAlpha"
                        onchange="submitFormDropDown('display')"
                        style="width:140px"
                        titleKey="tooltip.selectEntityId"
                        tabindex="1"
                        value="${entity.id.entityId}">
                    <c:forEach var="item" items="${requestScope.entities}">
                        <option value="${item.value}" <c:if test="${entity.id.entityId == item.value}">selected</c:if>>
                            ${item.label}
                        </option>
                    </c:forEach>
                </select>
            </td>
        </c:otherwise>
    </c:choose>

    <td width="20">&nbsp;</td>

    <td width="280px">
        <c:choose>
            <c:when test="${requestScope.methodName != 'list'}">
                <input type="text"
                       class="htmlTextAlpha"
                       name="entity.entityName"
                       maxlength="30"
                       style="width:280px;"
                       titleKey="entity.EnterEntityName"
                       value="${entity.entityName}"
                       onchange="return validateField(this,'entityName','alphaNumPatWithSpace');"
                       tabindex="2" />
            </c:when>
            <c:otherwise>
                <span id="entityName" name="entityName" class="spantext"></span>
            </c:otherwise>
        </c:choose>
    </td>
</tr>

	</table>
	</div>
	</div>
	<div id="ddimagetabs"
		style="position: absolute; left: 8px; top: 55px; width: 768px; height: 20px;">

	<a href="#" onmouseout="revertback('sc1',this);" tabindex="3"
		onmouseover="changecontent('sc1',this)"
		onClick="changeselected('sc1');expandcontent('sc1', this)"><b><fmt:message key="entity.generalTab" /></b></a> <a href="#"
		onmouseout="revertback('sc2',this);" tabindex="13"
		onmouseover="changecontent('sc2',this)"
		onClick="changeselected('sc2');expandcontent('sc2', this);changeAmountsOnCurrency();"><b><fmt:message key="entity.predictTab" /></b></a></div>
	<div id="Line"
		style="position: absolute; left: 170px; top: 73px; width: 606px; height: 20px;">
	<table width="100%">
		<tr>
			<td><img src="images/tabline.gif" width="100%" height="1"></td>
		</tr>
	</table>
	</div>

	<div id="tabcontentcontainer"
		style="position: absolute; left: 8px; top: 76px; width: 768px; height: 637px;">
	<div id="sc1" class="tabcontent">
	<div style="position: absolute; left: 6px; top: 6px; width: 765px; height: 85px;">
	<table width="625px" border="0" cellspacing="1" cellpadding="0">
		<tr height="22px">
			<c:choose>
				<c:when test="${requestScope.methodName == 'list'}">
					<td width="145px"><b><fmt:message key="entity.general.domesticCurrency" /></b></td>
				</c:when>
				<c:otherwise>
					<td width="145px"><b><fmt:message key="entity.general.domesticCurrency" /></b>*</td>
				</c:otherwise>
			</c:choose>


			<td width="28px">&nbsp;</td>
			<td width="355px">
				<div style="display: inline-table; width:55px; height:22px; position:relative; float:left">
					<select name="entity.domesticCurrency"
							tabindex="4"
							class="htmlTextAlpha"
							style="width:55px"
							onchange="getAccountList(event)"
							titleKey="tooltip.entity.general.domesticCurrency"
							value="${entity.domesticCurrency}"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
						<c:forEach var="item" items="${requestScope.currencyMaster}">
							<option value="${item.value}" <c:if test="${entity.domesticCurrency == item.value}">selected</c:if>>
								${item.label}
							</option>
						</c:forEach>
					</select>
				</div>
				&nbsp;&nbsp;&nbsp;
				<span style="display: inline;" id="currencyName" name="currencyName" class="spantext"></span>
			</td>

		</tr>
		<tr height="22px">
		<c:choose>
			<c:when test="${requestScope.methodName == 'list'}">
				<td width="145px"><b><fmt:message key="entity.general.reportingCurrency" /></b></td>
			</c:when>
			<c:otherwise>
				<td width="145px"><b><fmt:message key="entity.general.reportingCurrency" /></b>*</td>
			</c:otherwise>
		</c:choose>


			<td width="28px">&nbsp;</td>
			<td width="355px">
				<div style="display: inline-table; width:55px; height:22px; margin-top:1px; float:left; position:relative;">
					<select name="entity.reprotingCurrency"
							class="htmlTextAlpha"
							tabindex="6"
							style="width:55px"
							titleKey="tooltip.entity.general.reportingCurrency"
							value="${entity.reprotingCurrency}"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
						<c:forEach var="item" items="${requestScope.currencyMaster}">
							<option value="${item.value}" <c:if test="${entity.reprotingCurrency == item.value}">selected</c:if>>
								${item.label}
							</option>
						</c:forEach>
					</select>
				</div>
				&nbsp;&nbsp;&nbsp;
				<span style="display: inline;" id="reportingCurrencyName" name="reportingCurrencyName" class="spantext"></span>
			</td>

		</tr>
		<tr height="22px">
			<c:choose>
				<c:when test="${requestScope.methodName == 'list'}">
					<td width="145px"><b><fmt:message key="entity.general.country" /></b></td>
				</c:when>
				<c:otherwise>
					<td width="145px"><b><fmt:message key="entity.general.country" /></b>*</td>
				</c:otherwise>
			</c:choose>

			<td width="28px">&nbsp;</td>
			<td width="355px">
				<div style="display: inline-table; width:55px; height:22px; margin-top:1px; float:left; position:relative;">
					<select name="entity.countryId"
							class="htmlTextAlpha"
							tabindex="7"
							style="width:55px"
							titleKey="tooltip.entity.countryCode"
							value="${entity.countryId}"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
						<c:forEach var="item" items="${requestScope.countries}">
							<option value="${item.value}" <c:if test="${entity.countryId == item.value}">selected</c:if>>
								${item.label}
							</option>
						</c:forEach>
					</select>
				</div>
				&nbsp;&nbsp;&nbsp;
				<span style="display: inline;" id="countryName" name="countryName" class="spantext"></span>
			</td>

		</tr>
		<!-- Start Code:Modified by Naseema.Sd for Mantis 1797 -->
		<tr height="22px">
			<td width="145px"><b><fmt:message key="entity.general.BIC" /></b></td>
			<td width="28px">&nbsp;</td>
			<td width="355px">
			<input type="text"
				   name="entity.entityBIC"
				   class="htmlTextAlpha"
				   maxlength="12"
				   style="width:160px;"
				   tabindex="8"
				   titleKey="tooltip.entity.general.BIC"
				   value="${entity.entityBIC}"
				   onblur="return checkalphanumeric(this);"
				   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />

			</td>
		</tr>
  <!-- End Code:Modified by Naseema.Sd for Mantis 1797 -->
		<tr height="22px">
				<td width="145px"><b><fmt:message key="tooltip.timeZoneRegion" /></b></td>
				<td width="28px">&nbsp;</td>
				<td>
					<select name="entity.entTimeZone"
							class="htmlTextAlpha"
							titleKey="tooltip.timeZoneRegion"
							onchange="raiseAlert()"
							style="width:250px"
							tabindex="1"
							value="${entity.entTimeZone}"
							<c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
						<c:forEach var="item" items="${requestScope.timeZoneRegionsList}">
							<option value="${item.value}" <c:if test="${entity.entTimeZone == item.value}">selected</c:if>>
								${item.label}
							</option>
						</c:forEach>
					</select>
					<span id="timeZoneOffset" name="timeZoneOffset" class="spantext"></span>
				</td>
			</tr>


		<tr height="22px">
    <td width="148px"><b><fmt:message key="entity.general.exchangeRateFormat" /></b></td>
    <td width="28px">&nbsp;</td>
    <td width="355px">
        <input type="radio"
               id="1"
               tabindex="11"
               style="width:13px;"
               titleKey="entity.SelectDomesticCCY/CCY"
               name="entity.exchangeRateFormat"
               value="1"
               onclick="javaScript:changeinexchange(0)"
               ${entity.exchangeRateFormat == '1' ? 'checked="checked"' : ''}
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        <label for="1" title='<fmt:message key="entity.SelectDomesticCCY/CCY"/>'>
            <fmt:message key="entity.general.exchangeRateFormat.domestic.ccy" />
        </label>
        &nbsp;&nbsp;&nbsp;

        <input type="radio"
               id="2"
               tabindex="12"
               style="width:13px;"
               titleKey="entity.SelectCCY/DomesticCCY"
               name="entity.exchangeRateFormat"
               value="2"
               onclick="javaScript:changeinexchange(1)"
               ${entity.exchangeRateFormat == '2' ? 'checked="checked"' : ''}
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        <label for="2" title='<fmt:message key="entity.SelectCCY/DomesticCCY"/>'>
            <fmt:message key="entity.general.exchangeRateFormat.ccy.domestic" />
        </label>
    </td>
</tr>

	</table>
	</div>
	</div>

	<div id="sc2" class="tabcontent">
	<div
		style="position: absolute; left: 8px; top: 4px; width: 765px;">
	<fieldset style="width: 750px; border: 2px groove; height: 130px;">

	<legend><fmt:message key="entity.predict.retentionParameter" /></legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
		<tr height="20px">

			<td width="158px"><b>&nbsp;<fmt:message key="entity.predict.retentionParam.movement" /></b></td>
			<td width="203px">
			<input type="text"
				   name="entity.movementRetention"
				   class="htmlTextNumeric"
				   tabindex="14"
				   style="width:40px;align:left;"
				   maxlength="4"
				   value="${entity.movementRetention}"
				   titleKey="tooltip.entity.movementRetentionParameter"
				   onchange="return validateField(this,'entity.predict.retentionParam.movement','numberPat');"
				   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
			&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="days" /></b>
		</td>


			<td width="246px"><b>&nbsp;<fmt:message key="entity.smallMovementRetain" /></b></td>
			<td width="148px">
			<input type="text"
					   name="entity.smallMovementRetain"
					   class="htmlTextNumeric"
					   tabindex="15"
					   maxlength="4"
					   style="width:40px; align:right;"
					   value="${entity.smallMovementRetain}"
					   titleKey="tooltip.entity.smallMovementRetentionParameter"
					   onchange="return validateField(this,'entity.smallMovementRetain','numberPat');"
					   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
				&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;<fmt:message key="days" /></b>
			</td>

		</tr>


		<tr height="20px">
			<td width="152px"><b>&nbsp;<fmt:message key="exchangeRate" /></b></td>
			<td width="209px">
				<input type="text"
					   name="entity.exchangeRateRetain"
					   class="htmlTextNumeric"
					   tabindex="16"
					   maxlength="4"
					   style="width:40px;"
					   value="${entity.exchangeRateRetain}"
					   titleKey="tooltip.exchangeRateRetention"
					   onchange="return validateField(this,'entity.exchangeRateRetain','numberPat');"
					   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
				&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="days" /></b>
			</td>

			<td width="246px"><b>&nbsp;<fmt:message key="interestRate" /></b></td>
			<td width="148px">
				<input type="text"
					   name="entity.interestRateRetain"
					   class="htmlTextNumeric"
					   tabindex="17"
					   maxlength="4"
					   style="width:40px; align:right;"
					   value="${entity.interestRateRetain}"
					   titleKey="tooltip.interestRateRetention"
					   onchange="return validateField(this,'entity.interestRateRetain','numberPat');"
					   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
				&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;<fmt:message key="days" /></b>
			</td>
		</tr>

		<tr height="20px">
			<td width="152px"><b>&nbsp;<fmt:message key="entity.balance" /></b></td>
			<td width="209px">
				<input type="text"
					   name="entity.balance"
					   class="htmlTextNumeric"
					   tabindex="18"
					   maxlength="4"
					   style="width:40px;"
					   value="${entity.balance}"
					   titleKey="tooltip.entity.balance"
					   onchange="return validateField(this,'entity.exchangeRateRetain','numberPat');"
					   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
				&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="days" /></b>
			</td>

			<td width="246px"><b>&nbsp;<fmt:message key="entity.balancelog" /></b></td>
			<td width="148px">
				<input type="text"
					   name="entity.balanceLog"
					   class="htmlTextNumeric"
					   tabindex="19"
					   maxlength="4"
					   style="width:40px; align:right;"
					   value="${entity.balanceLog}"
					   titleKey="tooltip.entity.balancelog"
					   onchange="return validateField(this,'entity.interestRateRetain','numberPat');"
					   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
				&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;<fmt:message key="days" /></b>
			</td>
		</tr>

		<tr height="20px">
    <td width="152px"><b>&nbsp;<fmt:message key="entity.input" /></b></td>
    <td width="209px">
        <input type="text"
               name="entity.inputRetention"
               class="htmlTextNumeric"
               tabindex="20"
               maxlength="4"
               style="width:40px;"
               value="${entity.inputRetention}"
               titleKey="tooltip.entity.input"
               onchange="return validateField(this,'entity.exchangeRateRetain','numberPat');"
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        &nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="days" /></b>
    </td>

    <td width="246px"><b>&nbsp;<fmt:message key="entity.output" /></b></td>
    <td width="148px">
        <input type="text"
               name="entity.outputRetention"
               class="htmlTextNumeric"
               tabindex="21"
               maxlength="4"
               style="width:40px; align:right;"
               value="${entity.outputRetention}"
               titleKey="tooltip.entity.output"
               onchange="return validateField(this,'entity.interestRateRetain','numberPat');"
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        &nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;<fmt:message key="days" /></b>
    </td>
</tr>

<tr height="20px">
    <td width="152px"><b>&nbsp;<fmt:message key="entity.ilmRetain" /></b></td>
    <td width="209px">
        <input type="text"
               name="entity.ilmRetainAsString"
               class="htmlTextNumeric"
               tabindex="16"
               maxlength="4"
               style="width:40px;"
               value="${entity.ilmRetainAsString}"
               titleKey="tooltip.ilmRetain"
               onchange="return validateField(this,'entity.ilmRetain','numberPat');"
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        &nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="days" /></b>
    </td>

    <td width="246px"><b>&nbsp;<fmt:message key="entity.allowRecalculation" /></b></td>
    <td width="148px">
        <input type="text"
               name="entity.ilmCalcPastDaysAsString"
               class="htmlTextNumeric"
               tabindex="21"
               maxlength="4"
               style="width:40px; align:right;"
               value="${entity.ilmCalcPastDaysAsString}"
               titleKey="tooltip.entity.output"
               onchange="return validateField(this,'entity.allowRecalculation','numberPat');"
               <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        &nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;<fmt:message key="days" /></b>
    </td>
</tr>

	</table>
	</fieldset>
	<div id="ddimagebuttons">
	<fieldset style="width: 750px; border: 2px groove; height: 25px;">
	<legend>Movements</legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
		<tr height="20px">
			<td width="152px"><b>&nbsp;<fmt:message key="entity.editablefield" /></b></td>


			<td id="definebutton" width="593px"  style="padding-bottom: 3">
			<a tabindex="21"
				title='<fmt:message key="tooltip.defineEditableFields"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:openWindow(buildEditableDate('getEditableFields'),'entityaccesslistWindow','left=170,top=210,width=578,height=470,toolbar=0, status=yes, resizable=yes, scrollbars=yes')"><fmt:message key="entity.defineEditableFields" /></a>
			</td>
		</tr>
	</table>
	</fieldset>
	</div>

	<fieldset style="width: 750px; border: 2px groove; height: 63px;">
	<legend><fmt:message key="entity.predict.thresholdParameter" /></legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
    <tr height="20px">
        <td width="160px">
            <b>&nbsp;<fmt:message key="entity.predict.thresholdParam.cashFilter" /></b>
        </td>
        <td width="201px">
            <input type="text"
                   name="entity.cashFilterThresholdAsString"
                   class="htmlTextNumeric"
                   tabindex="22"
                   maxlength="28"
                   style="width:140px;"
                   value="${entity.cashFilterThresholdAsString}"
                   titleKey="entity.predict.thresholdParam.cashFilter"
                   onchange="return validateCurrency(this,'entity.cashFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>

        <td width="246px">
            <b>&nbsp;<fmt:message key="entity.predict.thresholdParam.sweepCutoffLeadTime" /></b>
        </td>
        <td width="148px">
            <input type="text"
                   name="entity.sweepCutoffLeadTime"
                   class="htmlTextNumeric"
                   tabindex="23"
                   maxlength="3"
                   style="width:40px;"
                   value="${entity.sweepCutoffLeadTime}"
                   titleKey="entity.predict.thresholdParam.sweepCutoffLeadTime"
                   onchange="return validateField(this,'Number 3','numberPat')"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
            &nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;<fmt:message key="minutes" /></b>
        </td>
    </tr>

    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.thresholdParam.securitiesFilter" /></b>
        </td>
        <td width="209px">
            <input type="text"
                   name="entity.securitiesFilterThresholdAsString"
                   class="htmlTextNumeric"
                   tabindex="24"
                   maxlength="28"
                   style="width:140px;"
                   value="${entity.securitiesFilterThresholdAsString}"
                   titleKey="entity.predict.thresholdParam.securitiesFilter"
                   onchange="return validateCurrency(this,'entity.securitiesFilterThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>

        <td width="246px">
            <b>&nbsp;<fmt:message key="entity.largeSmallMovementThresholdAsString" /></b>
        </td>
        <td width="148px">
            <input type="text"
                   name="entity.largeSmallMovementThresholdAsString"
                   class="htmlTextNumeric"
                   tabindex="25"
                   maxlength="24"
                   style="width:140px; align:right;"
                   value="${entity.largeSmallMovementThresholdAsString}"
                   titleKey="tooltip.entity.largeSmallMovementThresholdAsString"
                   onchange="return validateCurrency(this,'entity.largeSmallMovementThresholdAsString',currencyFormat, document.forms[0].elements['entity.domesticCurrency'].value)"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>
</table>

	</fieldset>

	<fieldset style="width: 750px; border: 2px groove; height: 87px;">
	<legend><fmt:message key="entity.predict.centralBank" /></legend>
	<div id="caldiv"
		style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
	<iframe id="calFrame" src="javascript:false;" scrolling="no"
		frameborder="0"
		style="position: absolute; top: 0px; left: 0px; display: none;">
	</iframe>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
    <tr height="20px">
        <td width="160px"><b>&nbsp;<fmt:message key="entity.accountId" /></b></td>
        <td colspan="3">
            <select name="entity.centralBankAccount"
                    class="htmlTextAlpha"
                    tabindex="26"
                    style="width:220px;"
                    titleKey="tooltip.entity.accountId"
                    value="${entity.centralBankAccount}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.accountIdColl}">
                    <option value="${item.label}" <c:if test="${entity.centralBankAccount == item.label}">selected</c:if>>
                        ${item.value}
                    </option>
                </c:forEach>
            </select>
            &nbsp;&nbsp;<span id="acctName" name="acctName" class="spantext"></span>
        </td>
    </tr>

    <tr height="20px">
        <td width="152px"><b>&nbsp;<fmt:message key="entity.dateFrom" /></b></td>
        <td width="209px">
            <input type="text"
                   name="entity.crrLimitFromDateAsString"
                   class="htmlTextAlpha"
                   tabindex="27"
                   maxlength="10"
                   style="width:80px; margin-bottom: 5px; height:20px;"
                   value="${entity.crrLimitFromDateAsString}"
                   titleKey="tooltip.entity.dateFrom"
                   onblur="onFromDateChange(this);"
                   onkeydown="onFromDateKeyPress(this,event);"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
            &nbsp;
            <a name="datelink" id="datelink" tabindex="28" onClick="fromDateClick();">
                <img title='<fmt:message key="tooltip.selectFromDate"/>' src="images/calendar-16.gif" />
            </a>
        </td>

        <td width="246px"><b><fmt:message key="entity.dateTo" /></b></td>
        <td width="148px">
            <input type="text"
                   name="entity.crrLimitToDateAsString"
                   class="htmlTextAlpha"
                   tabindex="29"
                   maxlength="10"
                   style="width:80px; margin-bottom: 5px; height:20px;"
                   value="${entity.crrLimitToDateAsString}"
                   titleKey="tooltip.entity.dateTo"
                   onblur="onToDateChange();"
                   onkeydown="onToDateKeyPress(this,event);"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
            <a name="datelink2" id="datelink2" tabindex="30" onClick="toDateClick();">&nbsp;
                <img title='<fmt:message key="tooltip.selectToDate"/>' src="images/calendar-16.gif" />
            </a>
        </td>
    </tr>

    <tr height="24px">
        <td width="152px"><b>&nbsp;<fmt:message key="entity.crrLimit" /></b></td>
        <td width="209px">
            <input type="text"
                   name="entity.crrLimitAsString"
                   class="htmlTextNumeric"
                   tabindex="31"
                   maxlength="26"
                   style="width:140px;"
                   value="${entity.crrLimitAsString}"
                   titleKey="tooltip.entity.crrLimit"
                   onchange="validateCurrLimitForZero();"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>

        <td width="246px"><b><fmt:message key="entity.startDay" /></b></td>
        <td width="148px">
            <select name="entity.startDay"
                    class="htmlTextAlpha"
                    tabindex="32"
                    style="width:120px;"
                    titleKey="tooltip.entity.startDay"
                    value="${entity.startDay}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.weekDayColl}">
                    <option value="${item.label}" <c:if test="${entity.startDay == item.label}">selected</c:if>>
                        ${item.value}
                    </option>
                </c:forEach>
            </select>
        </td>
    </tr>
</table>

	</fieldset>
	<fieldset style="width: 750px; border: 2px groove; height: 87px;">
	<legend><fmt:message key="entity.predict.metagroupLevelNames" /></legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.metagroupLevelNames.level1" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.metaGroupLevel1"
                   class="htmlTextAlpha"
                   tabindex="33"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.metaGroupLevel1}"
                   titleKey="entity.MetagroupLevel1Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>

    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.metagroupLevelNames.level2" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.metaGroupLevel2"
                   class="htmlTextAlpha"
                   tabindex="34"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.metaGroupLevel2}"
                   titleKey="entity.MetagroupLevel2Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>

    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.metagroupLevelNames.level3" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.metaGroupLevel3"
                   class="htmlTextAlpha"
                   tabindex="35"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.metaGroupLevel3}"
                   titleKey="entity.MetagroupLevel3Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>
</table>

	</fieldset>
	<fieldset style="width: 750px; border: 2px groove; height: 87px;">
	<legend><fmt:message key="entity.predict.groupLevelNames" /></legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.groupLevelNames.level1" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.groupLevel1"
                   class="htmlTextAlpha"
                   tabindex="36"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.groupLevel1}"
                   titleKey="entity.GroupLevel1Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>

    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.groupLevelNames.level2" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.groupLevel2"
                   class="htmlTextAlpha"
                   tabindex="37"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.groupLevel2}"
                   titleKey="entity.GroupLevel2Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>

    <tr height="20px">
        <td width="152px">
            <b>&nbsp;<fmt:message key="entity.predict.groupLevelNames.level3" /></b>
        </td>
        <td colspan="5">
            <input type="text"
                   name="entity.groupLevel3"
                   class="htmlTextAlpha"
                   tabindex="38"
                   maxlength="20"
                   style="width:280px;"
                   value="${entity.groupLevel3}"
                   titleKey="entity.GroupLevel3Name"
                   <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if> />
        </td>
    </tr>
</table>

	</fieldset>

	<fieldset style="width: 750px; border: 2px groove; height: 108px;">
	<legend><fmt:message key="entity.predict.sweepposition" /></legend>
	<table width="745px" border="0" cellspacing="2" cellpadding="0">
    <!-- PreAdvice Position -->
    <tr height="20px">
        <td width="152px">
            <c:choose>
                <c:when test="${requestScope.methodName == 'list'}">
                    &nbsp;<b><fmt:message key="entity.preadv" /></b>
                </c:when>
                <c:otherwise>
                    &nbsp;<b><fmt:message key="entity.preadv" /></b>*
                </c:otherwise>
            </c:choose>
        </td>
        <td colspan="2">
            <select name="entity.preAdvicePosition"
                    class="htmlTextAlpha"
                    tabindex="42"
                    style="width:55px"
                    titleKey="tooltip.preAdvicePosition"
                    value="${entity.preAdvicePosition}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.positionLvlColl}">
                    <option value="${item.value}" <c:if test="${entity.preAdvicePosition == item.value}">selected</c:if>>${item.label}</option>
                </c:forEach>
            </select>
            &nbsp;&nbsp;&nbsp;<span id="preAdvicePosLvlName" name="preAdvicePosLvlName" class="spantext"></span>
        </td>
    </tr>

    <!-- Sweep Position -->
    <tr height="20px">
        <td width="152px">
            <c:choose>
                <c:when test="${requestScope.methodName == 'list'}">
                    &nbsp;<b><fmt:message key="entity.sweepPoslvl" /></b>
                </c:when>
                <c:otherwise>
                    &nbsp;<b><fmt:message key="entity.sweepPoslvl" /></b>*
                </c:otherwise>
            </c:choose>
        </td>
        <td colspan="2">
            <select name="entity.sweepPosition"
                    class="htmlTextAlpha"
                    tabindex="43"
                    style="width:55px"
                    titleKey="tooltip.sweepPosition"
                    value="${entity.sweepPosition}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.positionLvlColl}">
                    <option value="${item.value}" <c:if test="${entity.sweepPosition == item.value}">selected</c:if>>${item.label}</option>
                </c:forEach>
            </select>
            &nbsp;&nbsp;&nbsp;<span id="sweepPosLvlName" name="sweepPosLvlName" class="spantext"></span>
        </td>
    </tr>

    <!-- Internal Balance -->
    <tr height="20px">
        <td width="152px">
            &nbsp;<b><fmt:message key="label.internalBalance" /></b>
        </td>
        <td colspan="2">
            <select name="entity.internalBalance"
                    class="htmlTextAlpha"
                    tabindex="44"
                    style="width:55px"
                    titleKey="tooltip.internalPosLvlName"
                    value="${entity.internalBalance}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.internalPositionLvlColl}">
                    <option value="${item.value}" <c:if test="${entity.internalBalance == item.value}">selected</c:if>>${item.label}</option>
                </c:forEach>
            </select>
            &nbsp;&nbsp;&nbsp;<span id="internalPosLvlName" name="internalPosLvlName" class="spantext"></span>
        </td>
    </tr>

    <!-- External Balance -->
    <tr height="20px">
        <td width="152px">
            &nbsp;<b><fmt:message key="accountmonitor.erBalance" /></b>
        </td>
        <td colspan="2">
            <select name="entity.externalBalance"
                    class="htmlTextAlpha"
                    tabindex="45"
                    style="width:55px"
                    titleKey="tooltip.externalPosLvlName"
                    value="${entity.externalBalance}"
                    <c:if test="${requestScope.screenFieldsStatus == 'true'}">disabled</c:if>>
                <c:forEach var="item" items="${requestScope.externalPositionLvlColl}">
                    <option value="${item.value}" <c:if test="${entity.externalBalance == item.value}">selected</c:if>>${item.label}</option>
                </c:forEach>
            </select>
            &nbsp;&nbsp;&nbsp;<span id="externalPosLvlName" name="externalPosLvlName" class="spantext"></span>
        </td>

        <!-- Conditional Button -->
        <td width="60" style="padding-right: 75px;" id="ddimagebuttons">
            <c:choose>
                <c:when test="${requestScope.methodName == 'list'}">
                    <a id="searchButton"
                       align="right"
                       title='<fmt:message key="entity.positionLevel"/>'
                       tabindex="30"
                       onmouseout="collapsebutton(this)"
                       onmouseover="highlightbutton(this)"
                       onmousedown="expandbutton(this)"
                       onmouseup="highlightbutton(this)"
                       onkeydown="submitEnter(this,event)"
                       onclick="posLvl('displayPosLvl')">
                        <fmt:message key="entity.PosLvl" />
                    </a>
                </c:when>
                <c:when test="${requestScope.methodName == 'add'}">
                    <a id="searchButton"
                       align="right"
                       title='<fmt:message key="entity.positionLevel"/>'
                       tabindex="40"
                       onmouseout="collapsebutton(this)"
                       onmouseover="highlightbutton(this)"
                       onmousedown="expandbutton(this)"
                       onmouseup="highlightbutton(this)"
                       onkeydown="submitEnter(this,event)"
                       onclick="posLvl('displayPosLvl')">
                        <fmt:message key="entity.PosLvl" />
                    </a>
                </c:when>
                <c:when test="${requestScope.methodName == 'change'}">
                    <a id="searchButton"
                       align="right"
                       title='<fmt:message key="entity.positionLevel"/>'
                       tabindex="41"
                       onmouseout="collapsebutton(this)"
                       onmouseover="highlightbutton(this)"
                       onmousedown="expandbutton(this)"
                       onmouseup="highlightbutton(this)"
                       onkeydown="submitEnter(this,event)"
                       onclick="posLvl('displayPosLvl')">
                        <fmt:message key="entity.PosLvl" />
                    </a>
                </c:when>
            </c:choose>
        </td>
    </tr>
</table>

	</fieldset>
	</div>
	</div>
	</div>
	</div>

	<div id="EntityMaintenance"
		style="position: absolute; left: 700; top: 725; width: 70px; height: 34px; visibility: visible;">
	<table width="60px" border="0" cellspacing="0" cellpadding="0">
    <tr>
        <!-- Help Icon -->
        <td align="Right">
            <c:choose>
                <c:when test="${requestScope.methodName == 'list'}">
                    <a tabindex="50" href="#"
                       onclick="javascript:openWindow(buildPrintURL('print','Entity Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                       onmouseout="MM_swapImgRestore()"
                       onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)">
                        <img src="images/help_default.GIF " name="Help" border="0"
                             title='<fmt:message key="tooltip.helpScreen"/>' />
                    </a>
                </c:when>
                <c:when test="${requestScope.methodName == 'add'}">
                    <a tabindex="50" href="#"
                       onclick="javascript:openWindow(buildPrintURL('print','Add Entity '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                       onmouseout="MM_swapImgRestore()"
                       onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)">
                        <img src="images/help_default.GIF " name="Help" border="0"
                             title='<fmt:message key="tooltip.helpScreen"/>' />
                    </a>
                </c:when>
                <c:when test="${requestScope.methodName == 'change'}">
                    <a tabindex="50" href="#"
                       onclick="javascript:openWindow(buildPrintURL('print','Change Entity '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                       onmouseout="MM_swapImgRestore()"
                       onmouseover="MM_swapImage('Help','','images/help_default.GIF ',1)">
                        <img src="images/help_default.GIF " name="Help" border="0"
                             title='<fmt:message key="tooltip.helpScreen"/>' />
                    </a>
                </c:when>
            </c:choose>
        </td>

        <!-- Print Icon -->
        <td align="right" id="Print">
            <a tabindex="51"
               onclick="printPage();"
               onkeydown="submitEnter(this,event)"
               onmouseout="MM_swapImgRestore()"
               onmouseover="MM_swapImage('Print','','images/Print_R.gif ',1)">
                <img src="images/Print.gif " name="Print" border="0"
                     title='<fmt:message key="tooltip.printScreen"/>' />
            </a>
        </td>
    </tr>
</table>

	</div>

	<div id="ddimagebuttons" color="#7E97AF"
		style="position: absolute; border: 2px outset; left: 8px; top: 718px; width: 768px; height: 35px; visibility: visible;">
	<div id="EntityMaintenance"
		style="position: absolute; left: 6; top: 3; width: 740; height: 15px; visibility: visible;">
	<table border="0" cellspacing="0" cellpadding="0" width="420">
		<tr>
			<td id="addbutton"></td>
			<td id="addbutton"></td>
			<td id="changebutton"></td>
			<td id="deletebutton"></td>
			<td id="savebutton"></td>
			<td id="cancelbutton"></td>
			<td id="closebutton" width="70px">

			<a tabindex="48" title='<fmt:message key="tooltip.close"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:submitCancel('cancelAdd')"><fmt:message key="button.close" /></a>
			</td>
		</tr>
	</table>
	</div>
	<div
		style="position: absolute; left: 6; top: 4; width: 653px; height: 15px; visibility: hidden;">
	<table width="350" border="0" cellspacing="0" cellpadding="0"
		height="20" style="visibility: hidden">
		<tr>
			<td width="70px" id="addenablebutton">
			<a tabindex="35" title='<fmt:message key="entity.AddEntity"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:submitFormAdd('add')"><fmt:message key="button.add" /></a>
			</td>
			<td id="adddisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="entity.AddEntity"/>'><fmt:message key="button.add" /></a></td>

			<td width="70px" id="changeenablebutton">
			<a tabindex="36" title='<fmt:message key="entity.ChangeEntity"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:submitFormChange('change')"><fmt:message key="button.change" /></a>
			</td>
			<td id="changedisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="entity.ChangeEntity"/>'><fmt:message key="button.change" /></a></td>

			<td width="70px" id="deleteenablebutton">
			<a tabindex="37" title='<fmt:message key="entity.DeleteEntity"/>'
				onMouseOut="collapsebutton(this)"
				onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
				onMouseUp="highlightbutton(this)"
				onKeyDown="submitEnter(this,event)"
				onClick="javascript:submitFormDelete('delete')"><fmt:message key="button.delete" /></a>
			</td>
			<td id="deletedisablebutton"><a class="disabled"
				disabled="disabled" title='<fmt:message key="entity.DeleteEntity"/>'><fmt:message key="button.delete" /></a></td>

		<td id="saveenablebutton">
			<c:choose>
				<c:when test="${requestScope.methodName == 'add'}">
					<a tabindex="46"
					   title='<fmt:message key="tooltip.SaveChanges"/>'
					   onmouseout="collapsebutton(this)"
					   onmouseover="highlightbutton(this)"
					   onmousedown="expandbutton(this)"
					   onmouseup="highlightbutton(this)"
					   onkeydown="submitEnter(this,event)"
					   onclick="isPositionLvlClicked=false;if(validateForm(document.forms[0])) { javascript:submitForm('save'); }">
						<fmt:message key="button.save"/>
					</a>
				</c:when>
				<c:when test="${requestScope.methodName == 'change'}">
					<a tabindex="46"
					   title='<fmt:message key="tooltip.SaveChanges"/>'
					   onmouseout="collapsebutton(this)"
					   onmouseover="highlightbutton(this)"
					   onmousedown="expandbutton(this)"
					   onmouseup="highlightbutton(this)"
					   onkeydown="submitEnter(this,event)"
					   onclick="if(validateForm(document.forms[0])) { javascript:submitFormUpdate('update'); }">
						<fmt:message key="button.save"/>
					</a>
				</c:when>
			</c:choose>
		</td>

		<td id="savedisablebutton">
			<a class="disabled"
			   disabled="disabled"
			   title='<fmt:message key="tooltip.SaveChanges"/>'>
				<fmt:message key="button.save"/>
			</a>
		</td>

		<td id="cancelenablebutton">
			<c:choose>
				<c:when test="${requestScope.methodName == 'add'}">
					<a tabindex="47"
					   title='<fmt:message key="tooltip.CancelChanges"/>'
					   onmouseout="collapsebutton(this)"
					   onmouseover="highlightbutton(this)"
					   onmousedown="expandbutton(this)"
					   onmouseup="highlightbutton(this)"
					   onkeydown="submitEnter(this,event)"
					   onclick="javascript:submitFormCancel('cancelAdd')">
						<fmt:message key="button.cancel"/>
					</a>
				</c:when>
				<c:when test="${requestScope.methodName == 'change'}">
					<a tabindex="47"
					   title='<fmt:message key="tooltip.CancelChanges"/>'
					   onmouseout="collapsebutton(this)"
					   onmouseover="highlightbutton(this)"
					   onmousedown="expandbutton(this)"
					   onmouseup="highlightbutton(this)"
					   onkeydown="submitEnter(this,event)"
					   onclick="javascript:submitFormCancel('cancelAdd')">
						<fmt:message key="button.cancel"/>
					</a>
				</c:when>
			</c:choose>
		</td>

		<td id="canceldisablebutton">
			<a class="disabled"
			   disabled="disabled"
			   title='<fmt:message key="tooltip.CancelChanges"/>'>
				<fmt:message key="button.cancel"/>
			</a>
		</td>

		</tr>
	</table>
	</div>
	</div>

	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>


</form>
</body>

</html>
