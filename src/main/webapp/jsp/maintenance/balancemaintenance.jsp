<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp"%>
<%@ include file="/angularJSUtils.jsp"%>
<%@ include file="/angularscripts.jsp"%>
<%@ page import="org.swallow.util.SwtUtil"%>
<html>
<head>
<title><fmt:message key="balMaintenance.title.MainWindow" /></title>
  <link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
  <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>

<SCRIPT language="JAVASCRIPT">
    var appName = "<%=SwtUtil.appName%>";
    var baseURL = new String('<%=request.getRequestURL()%>');
    var screenRoute = "BalMaintenance";
    var menuAccessId="${requestScope.menuAccessId}";
    var hostId = '<%= SwtUtil.getCurrentHostId() %>';
    var alertOrangeImage = "images/Alert/scenario/normal.png";
    var alertRedImage = "images/Alert/scenario/critical.png";
/**
 * Method to export data grid 
 * @param exportType
 */
 
	function exportData(reportType, noOfPages, currPage, entityId, currencyCode, balanceDate, balanceType,selectedFilter, selectedSort) {
		document.getElementById('exportDataForm').action = 'balMaintenance.do?method=exportBalanceMaintenance';
		document.getElementById('exportType').value = reportType.trim();
		if(noOfPages > 1)
			document.getElementById('currentPage').value = 0;
		else
			document.getElementById('currentPage').value = currPage;
		document.getElementById('entityId').value = entityId; // Use entityId from parameters
		document.getElementById('entityCode').value = entityId; // Use entityId from parameters
		document.getElementById('currencyCode').value = currencyCode; // Use currencyCode from parameters
		document.getElementById('balanceDate').value = balanceDate; // Use balanceDate from parameters
		document.getElementById('balanceType').value = balanceType; // Use balanceType from parameters
        document.getElementById('selectedFilter').value = selectedFilter;
        document.getElementById('selectedSort').value = selectedSort;

		document.forms[0].submit();
	}

	/* Method to Reason edit screen	 */
	function buildeditreason(entityCode, selectedBalTypeId, selectedDate,
			selectedName, selectedBalType, currentPage, selectedCurrency,
			currentSort, currentFilter, maxPage, userName, reasonCode,
			selectedSodBalance, access) {

		var param = 'balMaintenance.do?method=addNotes';
		param += '&entityCode=' + encodeURIComponent(entityCode);
		param += '&selectedBalTypeId=' + encodeURIComponent(selectedBalTypeId);
		param += '&selectedDate=' + encodeURIComponent(selectedDate);
		param += '&selectedName=' + encodeURIComponent(selectedName);
		param += '&selectedBalType=' + encodeURIComponent(selectedBalType);
		param += '&currentPage=' + encodeURIComponent(currentPage);
		param += '&selectedCurrency=' + encodeURIComponent(selectedCurrency);
		param += '&currentSort=' + encodeURIComponent(currentSort);
		param += '&currentFilter=' + encodeURIComponent(currentFilter);
		param += '&maxPage=' + encodeURIComponent(maxPage);
		param += '&userName=' + encodeURIComponent(userName);
		param += '&reasonCode=' + encodeURIComponent(reasonCode);
		param += '&selectedSodBalance='
				+ encodeURIComponent(selectedSodBalance);
		param += '&access=' + encodeURIComponent(access);

		// Open the window with the constructed URL
		return openWindow(
				param,
				'reasonChange',
				'left=100,top=140,width=960,height=200,toolbar=0, resizable=yes, scrollbars=no,status=yes',
				'true');
	}

	/* Method to load view / change balance details screen */
	function buildChangeBalMaintURL(methodName, entityCode, selectedBalTypeId,
			selectedName, currentPage, currentSort, currentFilter, userName,
			selectedBalType, selectedDate, selectedCurrency,
			selectedCurrencyCode, EntityName, balanceSource, reasonDesc,
			suppliedExternalBalanceSod, forecastSOD, forecastSODType,
			externalSOD) {
		// Build the URL with parameters
		var param = 'balMaintenance.do?method=' + methodName;
		param += '&entityCode=' + encodeURIComponent(entityCode);
		param += '&selectedBalTypeId=' + encodeURIComponent(selectedBalTypeId);
		param += '&selectedName=' + encodeURIComponent(selectedName);
		param += '&currentPage=' + encodeURIComponent(currentPage);
		param += '&currentSort=' + encodeURIComponent(currentSort);
		param += '&currentFilter=' + encodeURIComponent(currentFilter);
		param += '&userName=' + encodeURIComponent(userName);
		param += '&selectedBalType=' + encodeURIComponent(selectedBalType);
		param += '&selectedDate=' + encodeURIComponent(selectedDate);
		param += '&selectedCurrency=' + encodeURIComponent(selectedCurrency);
		param += '&selectedCurrencyCode='
				+ encodeURIComponent(selectedCurrencyCode);
		param += '&EntityName=' + encodeURIComponent(EntityName);
		param += '&balanceSource=' + encodeURIComponent(balanceSource);
		param += '&reasonDesc=' + encodeURIComponent(reasonDesc);
		param += '&suppliedExternalBalanceSod='
				+ encodeURIComponent(suppliedExternalBalanceSod);
		param += '&forecastSOD=' + encodeURIComponent(forecastSOD);
		param += '&forecastSODType=' + encodeURIComponent(forecastSODType);
		param += '&externalSOD=' + encodeURIComponent(externalSOD);

		// Open the window with the constructed URL
		return openWindow(
				param,
				'balancemaintenancechange',
				'left=100,top=140,width=630,height=520,toolbar=0, resizable=yes, scrollbars=yes',
				'true');
	}

	/* Method to load balance log screen  */
	function buildViewBalLogURL(entityCode, selectedBalTypeId, selectedBalType,
			selectedCurrency, selectedDate, balTypeName) {
		// Build the URL with parameters
		var param = 'balanceLog.do?entityCode='
				+ encodeURIComponent(entityCode);
		param += '&selectedBalTypeId=' + encodeURIComponent(selectedBalTypeId);
		param += '&selectedBalType=' + encodeURIComponent(selectedBalType);
		param += '&selectedCurrency=' + encodeURIComponent(selectedCurrency);
		param += '&selectedDate=' + encodeURIComponent(selectedDate);
		param += '&selectedBalTypeName=' + encodeURIComponent(balTypeName);
		// Open the window with the constructed URL
		return openWindow(
				param,
				'startingbalancelog',
				'left=100,top=140,width=1250,height=558,toolbar=0, resizable=yes, scrollbars=yes,status=yes',
				'true');
	}

    window.addEventListener("message", receiveMessage, false);
    function receiveMessage(e) {
      if(e && e.data){
        var methodName = e.data[0];
        if(methodName == "refreshParentScreen"){
          Main.updateData();
        }
      }

    }
</SCRIPT>

<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0">
<%@ include file="/angularscripts.jsp"%>
<form id="exportDataForm" target="tmp" method="post">
  <input type="hidden" name="data" id="exportData" />
  	<input name="exportType" type="hidden" value="">
	<input type="hidden" name="screen" id="exportDataScreen"
		value="BalanceMaintenance-SmartPredict" />
  <input name="selectedBalTypeId" type="hidden" value="GBP">
  <input name="selectedBalType" type="hidden" value="GBP">
  <input name="userName" type="hidden" value="GBP">
  <input name="selectedName" type="hidden" value="GBP">
  <input name="selectedCurrency" type="hidden" value="EURO">
  <input name="selectedCurrencyCode" type="hidden" value="EUR">

  <input name="goToPageNo" type="hidden" value="">
  <input name="entityCode" type="hidden" value="">
  <input name="totalCount" type="hidden" value="">
  <input name="selectedDate" type="hidden" value="date">
  <input name="currentPage" type="hidden" value='${currPage}'>
  <input name="prevEnabled" type="hidden" value="">
  <input name="nextEnabled" type="hidden" value="">
  <input name="maxPages" type="hidden" value='${maxPage}'>
  <input name="filterCriteria" type="hidden" value='${filterCriteria}'>
  <input name="selectedFilter" type="hidden" value='${selectedFilter}'>
  <input name="selectedSort" type="hidden" value='${selectedSort}'>
  <input name="selectedSortDescending" type="hidden" value="">
  <input name="selectedFilterStatus" type="hidden" value="">
  <input name="selectedSortStatus" type="hidden" value="">
  <input name="selectedList" type="hidden" value="">
  <input name="currencyCode" type="hidden" value="">
  
  <input name="entityId" type="hidden" value="">
  <input name="name" type="hidden" value="">
  <input name="sodBalanceAsString" type="hidden" value="">
  <input name="filterFromSerach" type="hidden" value="">
  <input name="balanceSource" type="hidden" value="">
  <input name="user" type="hidden" value="">
  <input name="inputDateAsString" type="hidden" value="">
  <input name="inputTimeAsString" type="hidden" value="">

  <input name="balanceType" type="hidden" value="">
  <input name="balanceDate" type="hidden" value="">
  <input name="replacebalanceDate" type="hidden" value="">
  <input name="currency" type="hidden" value="">
  <input name="reasonCode" type="hidden" value="">
  <input name="reasonDesc" type="hidden" value="">
  <input name="userNotes" type="hidden" value="">
  <input name="suppliedExternalBalanceSod" type="hidden" value="">
  <input name="selectedSodBalance" type="hidden" value="">
  <input name="menuAccessId" type="hidden">

  <input name="forecastSOD" type="hidden" value="">
  <input name="forecastSODTypeAsString" type="hidden" value="">
  <input name="externalSOD" type="hidden" value="">
  <input name="externalSODType" type="hidden" value="">
  <input name="forecastSODType" type="hidden" value="">
  

</form>
<iframe name="tmp" width="0%" height="0%" src="#" />
</body>
</html>