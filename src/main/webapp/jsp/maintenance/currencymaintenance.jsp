<%@ include file="/taglib.jsp"%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
	<title><fmt:message key="currencyMaintenance.title.mainWindow"/></title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<script type="text/javascript" src="js/jquery.exportselect.js"></script>
	<SCRIPT language="JAVASCRIPT">
		<c:if test="${request.parentFormRefresh == 'yes'}">
		window.opener.document.forms[0].method.value="displayList";
		window.opener.document.forms[0].submit();
		self.close();
		</c:if>

		var menuEntityCurrGrpAccess = "${requestScope.menuEntityCurrGrpAccess}";
		var dateFormat = "${sessionScope.CDM.dateFormat}";
		var totalCount = '${totalCount}';
		/*Added to apply style on mandatory field ,if it's equal to undefined then no field will be colored*/
		mandatoryFieldsArray = "undefined";


		function disableAllButtons()
		{
			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
		}
		function onFilterandSort()
		{
			updateColors();
			disableAllButtons();

		}
		window.addEventListener("message", receiveMessage, false);


		function receiveMessage(e) {
			if(e && e.data){
				var methodName = e.data[0];
				if(methodName == "clearParams"){
					clearParams();
				}
				if(methodName == "refreshParent"){
					setTimeout(()=>{
						window.document.forms[0].method.value="displayListByEntity";
						window.document.forms[0].submit();
					},1000);
				}
			}

		}
		function clearParams() {
			var appName = "<%=SwtUtil.appName%>";
			var requestURL = new String('<%=request.getRequestURL()%>');
			var idy = requestURL.indexOf('/' + appName + '/');

			requestURL = requestURL.substring(0, idy + 1);
			requestURL = requestURL + appName
					+ "/currency.do?method=clearSessionValues";

			var oXMLHTTP = new XMLHttpRequest();
			oXMLHTTP.open("POST", requestURL, false);
			oXMLHTTP.send();
			var res = new String(oXMLHTTP.responseText);
		}
		function bodyOnLoad()
		{
			xl = new XLSheet("currencyListDetails","table_2", ["String","String","String", "Number","String","String","String", "Number", "Number", "String","String","String","String"],"1111111111111");
			xl.onsort = xl.onfilter = onFilterandSort;

			highlightTableRows("currencyListDetails");


			var dropBox1 = new SwSelectBox(document.forms[0].elements["currency.id.entityId"],document.getElementById("entityName"));

			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;



			<%if (request.getAttribute(SwtConstants.ADD_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
			document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;
			<%}else{ %>

			document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;
			<%}%>


			<%if (request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
			document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
			<%}else{ %>

			document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
			<%}%>


			<%if (request.getAttribute(SwtConstants.DEL_BUT_STS).equals(SwtConstants.STR_TRUE)) {%>
			document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
			<%}else{ %>

			document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			<%}%>

			if(menuEntityCurrGrpAccess == "0")
			{
				document.getElementById("addbutton").innerHTML = document.getElementById("addenablebutton").innerHTML;

			}else{
				document.getElementById("addbutton").innerHTML = document.getElementById("adddisablebutton").innerHTML;

			}

			document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
			/**
			 *  Start: fill the export select
			 **/
			var headerData = [];
			var dataprovider = new Array();
			var newElement1 = {};
			newElement1[headerData[0]] = 'Pdf';
			dataprovider.push(newElement1);

			var newElement2 = {};
			newElement2[headerData[0]] = 'Excel';
			dataprovider.push(newElement2);

			var newElement3 = {};
			newElement3[headerData[0]] = 'Csv';
			dataprovider.push(newElement3);

			$("#exportReport").exportselect ({
				dataprovider: dataprovider,
				change: exportReport,
				selectedIndex:0
			});

			/**
			 *  End: fill the export select
			 **/

		}

		function exportReport(){
			var type=$("#exportReport").getSelected(0);
			return exportData(type.toLowerCase());
		}

		function submitForm(methodName){
			document.forms[0].method.value = methodName;
			document.forms[0].submit();
		}
		function submitDeleteForm(methodName){
			document.forms[0].method.value = methodName;

			var yourstate=window.confirm('<fmt:message key="confirm.delete"/>');
			if (yourstate==true){
				document.forms[0].submit();
			}
		}

		function buildAddCurrency(methodName){
			var param = 'currency.do?method='+methodName+'&entityCode=';
			param +=  document.forms[0].elements['currency.id.entityId'].value;
			param += '&entityText=' + document.getElementById('entityName').innerText;

			return  param;
		}

		function buildChangeCurrency(methodName){

			var param = 'currency.do?method='+methodName+'&entityCode=';
			param +=  document.forms[0].elements['currency.id.entityId'].value;
			param += '&entityText=' + document.getElementById('entityName').innerText;
			param += '&selectedCurrencyCode=' + document.forms[0].selectedCurrencyCode.value ;
			param += '&currencyName=' + document.forms[0].selectedCurrencyName.value ;
			return  param;
		}

		function onSelectTableRow(rowElement, isSelected)
		{
			var hiddenElement = rowElement.getElementsByTagName("input")[0];
			document.forms[0].selectedCurrencyCode.value = hiddenElement.value;
			var hiddenElement = rowElement.getElementsByTagName("input")[1];
			document.forms[0].selectedCurrencyName.value = hiddenElement.value;


			if(menuEntityCurrGrpAccess == "0" && isSelected)

			{
				document.getElementById("changebutton").innerHTML = document.getElementById("changeenablebutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("deleteenablebutton").innerHTML;
			}else{
				document.getElementById("changebutton").innerHTML = document.getElementById("changedisablebutton").innerHTML;
				document.getElementById("deletebutton").innerHTML = document.getElementById("deletedisablebutton").innerHTML;
			}
		}

		function exportData(exportType)
		{
			var currPage = '${requestScope.currentPage}';
			document.forms[0].method.value = 'exportCurrency';
			document.forms[0].exportType.value = exportType.trim();
			document.forms[0].currentPage.value = currPage;
			if(record()> 0)
			{
				document.forms[0].submit();
			}
		}
		function record()
		{
			return document.getElementById ("ddscrolltable").getElementsByTagName('tr').length-1;
		}



	</SCRIPT>
</head>

<form action="currency.do" method="post">
	<input name="method" type="hidden" value="display">
	<input name="selectedCurrencyCode" type="hidden">
	<input name="totalCount" type="hidden" value="">
	<input name="currentPage" type="hidden" value="${currPage}">
	<input name="exportType" type="hidden" value="">
	<input type="hidden" name="screen" id="exportDataScreen" value="CurrencyMaintenance-SmartPredict"/>
	<input name="selectedCurrencyName" type="hidden">
	<input name="menuAccessId" type="hidden">

	<body onmousemove="reportMove()" leftmargin="0" topmargin="0" marginheight="0"
		  onLoad="bodyOnLoad();setParentChildsFocus();ShowErrMsgWindow('${actionError}');setFocus(document.forms[0]);"
		  onunload="call()">
	<div id="Currency" style="position:absolute; left:20px; top:10px; width:965px; height:42px; border:2px outset;" color="#7E97AF">

		<div id="Currency" style="position:absolute; left:8px; top:4px; width:690px; height:400;">
			<table width="506" border="0" cellpadding="0" cellspacing="0" height="30">
				<tr color="black" border="0">
					<td width="38"><b><fmt:message key="bookCode.entity"/></b></td>
					<td width="28">&nbsp;</td>
					<td width="140px">

					<select id="entityId" name="currency.id.entityId" tabindex="1" titleKey="tooltip.selectEntityid"
						onchange="submitForm('displayListByEntity')" class="htmlTextAlpha" style="width:140px">
						<c:forEach var="entity" items="${requestScope.entities}">
							<option value="${entity.value}" <c:if test="${entity.value == currency.id.entityId}">selected</c:if>>
								${entity.label}
							</option>
						</c:forEach>
					</select>


					</td>
					<td width="20">&nbsp;</td>
					<td width="280">
        <span id="entityName" name="#request.entityName" class="spantext">
					</td>
				</tr>
			</table>
		</div>
	</div>

	<div id="Currency" color="#7E97AF" style="position:absolute; border:0px outset; left:20px; top:57px; width:965px; height:459px;">
		<div id="Currency" style="position:absolute;z-index:50;left:0px; top:0px; width:945px; height:10px;overflow-x: hidden">
			<table class="sort-table" id="table_2" bgcolor="#B0AFAF" width="1440px" border="0" cellspacing="1" cellpadding="0" height="23px">
				<thead>
				<tr height="20px">
					<td title="<fmt:message key='tooltip.sortCurrencyId'/>" width="68px" style="border-left-width: 0px;"><b><fmt:message key="currency.id"/></b></td>
					<td title="<fmt:message key='tooltip.sortCurrencyName'/>" width="280px"><b><fmt:message key="currency.name"/></b></td>
					<td title="<fmt:message key='tooltip.sortCurrencyGroup'/>" width="148px"><b><fmt:message key="currency.group"/></b></td>
					<td title="<fmt:message key='tooltip.sortOrder'/>" width="83px"><b><fmt:message key="currency.order"/></b></td>
					<td title="<fmt:message key='tooltip.SortByExchangeRate'/>" align="center" width="120px"><b><fmt:message key="currency.exchangeRate2"/></b></td>
					<td title="<fmt:message key='tooltip.multiplier'/>" align="center" width="110px"><b><fmt:message key="currency.multiplier"/></b></td>
					<td title="<fmt:message key='tooltip.threshold'/>" align="center" width="140px"><b><fmt:message key="currency.thresholdMainScreen"/></b></td>
					<td title="<fmt:message key='tooltip.SortByInterestBasis'/>" width="98px"><b><fmt:message key="currency.interestBasis"/></b></td>
					<td title="<fmt:message key='tooltip.SortByTolerance'/>" width="65px"><b><fmt:message key="currency.tolerance"/></b></td>
					<td title="<fmt:message key='tooltip.SortByCutofftime'/>" width="105px"><b><fmt:message key="currency.cutOffTime"/></b></td>
					<td title="<fmt:message key='tooltip.SortByPreflag'/>" width="95px">&nbsp;<b><fmt:message key="currency.preFlag"/></b></td>
					<td title="<fmt:message key='tooltip.sortByTimeZoneOffset'/>" width="280px"><b><fmt:message key="title.timeZoneOffset"/></b></td>
					<td title="<fmt:message key='tooltip.SortByGmtOffset'/>" width="200px"><b><fmt:message key="currency.gmtOffset"/></b></td>
				</tr>
				</thead>
			</table>
		</div>

		<div id="ddscrolltable" style="position:absolute; left:0px; top:0px; width:961px; height:475px; overflow:scroll;">
			<div id="Currency" style="position:absolute;z-index:99;left:0px; top:22px; width:930px; height:10px;">
				<table class="sort-table" id="currencyListDetails" width="1440px" border="0" cellspacing="1" cellpadding="0" height="433">
					<tbody>
					<c:forEach items="${requestScope.currencyListDetails}" var="currency" varStatus="status">
						<tr class="${status.index % 2 == 0 ? 'even' : 'odd'}">
							<td align="left" width="68px">
								<input type="hidden" name="id.currencyCode" value="${currency.id.currencyCode}"/>
									${currency.id.currencyCode}&nbsp;
							</td>
							<td width="280px" align="left">
								<input type="hidden" name="currencyMaster.currencyName" value="${currency.currencyMaster.currencyName}"/>
									${currency.currencyMaster.currencyName}&nbsp;
							</td>
							<td width="148px" align="left">${currency.currencyGroupId}&nbsp;</td>
							<td width="83px" align="left">${currency.priorityOrder}&nbsp;</td>
							<td width="120px" align="right">${currency.exchangeRate}&nbsp;</td>
							<td width="110px" align="left">${currency.multiplierDesc}&nbsp;</td>
							<td width="140px" align="right">${currency.threshold}&nbsp;</td>
							<td width="98px" align="center">${currency.interestBasis}&nbsp;</td>
							<td width="65px" align="right">${currency.tolerance}&nbsp;</td>
							<td width="105px" align="middle">${currency.cutOffTime}&nbsp;</td>
							<td width="95px" align="middle">${currency.preFlag}&nbsp;</td>
							<td width="280px" align="left">${currency.currencyMaster.ccyTimeZone}&nbsp;</td>
							<td width="200px" align="middle">${currency.gmtOffset}&nbsp;</td>
						</tr>
					</c:forEach>
					</tbody>
					<tfoot><tr><td colspan="15"></td></tr></tfoot>
				</table>
			</div>
		</div>
	</div>

	<div id="Currency" style="position:absolute; left:905; top:550; width:70px; height:15px;z-index: 5; visibility:visible;">
		<table>
			<div id="exportReport"/>
		</table>
	</div>

	<div id="helpIcon" style="position:absolute; left:950; top:548; width:30px; height:15px; visibility:visible;">
		<a tabindex="5" href="#" onclick="javascript:openWindow(buildPrintURL('print','Currency Maintenance'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF',1)">
			<img src="images/help_default.GIF" name="Help" border="0" title="<fmt:message key='tooltip.helpScreen'/>">
		</a>
	</div>

	<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:538; width:965px; height:39px; visibility:visible;">
		<div id="Currency" style="position:absolute; left:6; top:4; width:810; height:15px; visibility:visible;">
			<table width="280" border="0" cellspacing="0" cellpadding="0" height="20">
				<tr>
					<td id="addbutton" width="70" title="<fmt:message key='tooltip.AddNewCurrency'/>"></td>
					<td id="changebutton" width="70" title="<fmt:message key='tooltip.ChangeSelectedCurrency'/>"></td>
					<td id="deletebutton" width="70" title="<fmt:message key='tooltip.DeleteSelectedCurrency'/>"></td>
					<td id="closebutton" width="70" title="<fmt:message key='tooltip.close'/>">
						<a tabindex="5" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onclick="confirmClose('P');"><fmt:message key="button.close"/></a>
					</td>
				</tr>
			</table>
		</div>

		<div style="position:absolute; left:6; top:4; width:810; height:15px; visibility:hidden;">
			<table width="280" border="0" cellspacing="0" cellpadding="0" height="20" style="visibility:hidden">
				<tr>
					<td id="addenablebutton">
						<a tabindex="2" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddCurrency('add'),'currencymaintenanceaddWindow','left=50,top=190,width=628,height=480,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.add"/></a>
					</td>
					<td id="adddisablebutton">
						<a class="disabled" disabled="disabled"><fmt:message key="button.add"/></a>
					</td>
					<td id="changeenablebutton">
						<a tabindex="3" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildChangeCurrency('change'),'currencymaintenancechangeWindow','left=50,top=190,width=628,height=480,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message key="button.change"/></a>
					</td>
					<td id="changedisablebutton">
						<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
					</td>
					<td id="deleteenablebutton">
						<a tabindex="4" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitDeleteForm('delete')"><fmt:message key="button.delete"/></a>
					</td>
					<td id="deletedisablebutton">
						<a class="disabled" disabled="disabled"><fmt:message key="button.delete"/></a>
					</td>
				</tr>
			</table>
		</div>
	</div>
</form>
</body>
</html>