<?xml version="1.0" encoding="UTF-8"?>

<%@ page contentType="text/xml"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>



<attributeusagesummary name="attributeusagesummary" restriction="${restriction}">

	<request_reply>
		<status_ok>${reply_status_ok}</status_ok>
		<message>${reply_message}</message>
		<location /> 
	</request_reply> 

<grid>
	<metadata>
		<columns>
		 <c:forEach items="${requestScope.column_order}" var="order">
			 <c:choose>
				 <c:when test="${order == 'attribute_id'}">
			<column heading="<fmt:message key="attributeusagesummary.attributeId" />"
					draggable="true"					
					filterable="true"
					type="str"
					dataelement="attribute_id"
					sort="false"
					width="${requestScope.column_width.attribute_id}"/>
				 </c:when>
				 <c:when test="${order == 'name'}">
			<column heading="<fmt:message key="attributeusagesummary.name" />"
					draggable="true"					
					filterable="false"
					type="str"
					dataelement="name"
					sort="false"
					width="${requestScope.column_width.name}"/>
				 </c:when>
				 <c:when test="${order == 'type'}">
			<column heading="<fmt:message key="attributeusagesummary.type" />"
					draggable="true"					
					filterable="false"
					type="str"
					dataelement="type"
					sort="true"
					width="${requestScope.column_width.type}"/>
				 </c:when>
				 <c:when test="${order == 'display'}">
			<column heading="<fmt:message key="attributeusagesummary.display" />"
					draggable="true"					
					filterable="false"
					type="num"
					dataelement="display"
					sort="true"
					width="${requestScope.column_width.display}"/>
				 </c:when>
			 </c:choose>
		 </c:forEach>
		</columns>	
	</metadata>

	<rows size="${rowSize}">
			<c:forEach items="${requestScope.acctAttrFunGroupList}" var="acctAttrFunGroupList">
			<row left_hidden="true" right_hidden="false" original="true" filtred="true" shared="false"  >
				<attribute_id clickable="false">
						${acctAttrFunGroupList.id.attributeId}
				</attribute_id>
				<name clickable="false">
					${acctAttrFunGroupList.accountAttributeHDR.attributeName}
				</name>
				<type clickable="false">
					<c:if test="${acctAttrFunGroupList.accountAttributeHDR.valueType == 'T'}"><fmt:message key="type.text" /></c:if>
					<c:if test="${acctAttrFunGroupList.accountAttributeHDR.valueType == 'N'}"><fmt:message key="type.numeric" /></c:if>
					<c:if test="${acctAttrFunGroupList.accountAttributeHDR.valueType == 'D'}"><fmt:message key="type.date" /></c:if>
				</type>
				<display clickable="false">
					${acctAttrFunGroupList.displayOrder}
				</display>
			</row>
			</c:forEach>
	</rows>
</grid>

<selects> 
	<select id="functionalGrpList"> 
		<c:forEach items="${requestScope.functionalGrpList}" var="functionalGrps">
			<option
					value="${functionalGrps.id.functionalGroup}"
					selected="${requestScope.selectedFunctionalGrp == functionalGrps.id.functionalGroup ? '1' : '0'}"
			>${fn:escapeXml(functionalGrps.functionalGroupDesc)}</option>
		</c:forEach>
	</select> 
</selects>
</attributeusagesummary>
