	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	<%@ include file="/taglib.jsp"%>
	<%@ page import="org.swallow.util.SwtConstants" %>
	<html>
	<head>
	<title>

	<c:if test="${'add' == requestScope.method}">

	<fmt:message key="sweepInter.childScreen"/>


</c:if>

	<c:if test="${'add' != requestScope.method}">

		<c:if test="${'change' == requestScope.method}">

		<fmt:message key="sweepInter.changeChildScreen"/>

</c:if>

</c:if>

	</title>
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">


	<SCRIPT language="JAVASCRIPT">



	<c:if test="${'yes' == requestScope.parentFormRefresh}">

	window.opener.document.forms[0].method.value="displayList";
	window.opener.document.forms[0].submit();
	self.close();

</c:if>
	mandatoryFieldsArray=["*"];
	var cancelcloseElements = new Array(1);
	cancelcloseElements[0] = "cancelbutton";
	var bicCode="";
	function submitForm(methodName){
		document.forms[0].method.value = methodName;

		document.forms[0].selectedEntityId.value = document.forms[0].elements["sweepintermediaries.id.entityId"].value;
		document.forms[0].selectedCurrencyCode.value = document.forms[0].elements['sweepintermediaries.id.currencyCode'].value;
		document.forms[0].selectedCurrencyName.value = '${currencyName}';


		document.forms[0].selectedTargetBic.value = document.forms[0].elements['sweepintermediaries.id.targetBic'].value;
		document.forms[0].selectedIntermediaryBic.value = document.forms[0].elements['sweepintermediaries.intermediary'].value;

		document.forms[0].selectedAccountId.value = document.forms[0].elements["sweepintermediaries.accountId"].value;

		if(validateForm(document.forms[0])) {
			if(validateField(document.forms[0].elements['sweepintermediaries.id.targetBic'],'sweepintermediaries.id.targetBic','alphaNumPat'))
			{

				if(validateField(document.forms[0].elements['sweepintermediaries.intermediary'],'sweepintermediaries.intermediary','alphaNumPat'))
				{

					if(validateField(document.forms[0].elements['sweepintermediaries.accountId'],'sweepintermediaries.accountId','alphaNumPat'))
					{
						document.forms[0].submit();
					}
					else
					{
						document.forms[0].elements["sweepintermediaries.accountId"].focus();
					}

				}else{
					document.forms[0].elements["sweepintermediaries.intermediary"].focus();
				}
			}else {
				document.forms[0].elements["sweepintermediaries.id.targetBic"].focus();
			}
		}



		}

	function validateForm(objForm){

	  var elementsRef = new Array(2);
	  elementsRef[0] = objForm.elements["sweepintermediaries.id.currencyCode"];
	  elementsRef[1] = objForm.elements["sweepintermediaries.id.targetBic"];
	  return validate(elementsRef);

	}

	function bodyOnLoad()
	{
		var currencyBox = new SwSelectBox(document.forms[0].elements["sweepintermediaries.id.currencyCode"],document.getElementById("currencyName"));
		ShowErrMsgWindow('${actionError}');
		document.getElementById("savebutton").innerHTML = document.getElementById("saveenablebutton").innerHTML;
		document.getElementById("entityName").innerText = '${entityName}';
		document.forms[0].method.value = '${method}';
		<c:if test="${'change' == requestScope.method}">

		document.getElementById("currencyName").innerText = '${currencyName}';


</c:if>

		}

	/**
 	  * checkalphanumeric
      *
      * This method is used to check the alphaNumPat for given element
      */
/*Start:Code Modified by Chinna for Mantis 1797: BIC code validation should not prevent save / update in Account && Entity maintenance screens*/
	function checkalphanumeric(element,bicId,e){
		var event = (window.event|| e);
		if(validateField(element,'acctbiccode','alphaNumPat') && element.value!="" && isCancelorCloseButtonPressed() == false){
			return validateBicCode(element,'acctbiccode','biccode',bicId,event);
		}
	}
	var globalBicId;
	var globalStrField;
	function validateBicCode(strField, strLabel, strPat,bicId,event) {
		var thePat = PatternsDict[strPat];
		var gotIt = thePat.exec(strField.value);
		globalBicId = bicId;
		globalStrField = strField
		if (strPat == "biccode") {
			if (!gotIt) {
				returnStr = PatternsMetaData[strPat];
				//confirm message to get the conformation for BIC code from the user
				if(bicCode=="" || bicCode==bicId){
					ShowErrMsgWindowWithBtn('',returnStr,YES_NO, yesContinue, noContinue);
				}
				return true;
			}
		}
	}
	function yesContinue() {
		bicCode="";
	}
	function noContinue() {
		bicCode=globalBicId;
		if (window.event){
			window.event.returnValue = false;
		}
		 setTimeout(function() { globalStrField.focus(); }, 0);
	}

/*End:Code Modified by Chinna for Mantis 1797: BIC code validation should not prevent save / update in Account && Entity maintenance screens*/
	</SCRIPT>
	</head>

	<form action="sweepintermediaries.do" method="post" onsubmit="validate(this);">
	<input name="method" type="hidden" value="">
	<input name="selectedEntityId" type="hidden" value="">

	<input name="selectedCurrencyCode" type="hidden" value="">
	<input name="selectedCurrencyName" type="hidden" value="">
	<input name="selectedTargetBic" type="hidden" value="">
	<input name="selectedIntermediaryBic" type="hidden" value="">
	<input name="selectedEntityCode" type="hidden" value="">
	<input name="selectedAccountId" type="hidden" value="">
	<body leftmargin="0" topmargin="0" marginheight="0" onLoad="bodyOnLoad(); setParentChildsFocus();setTitleSuffix(document.forms[0]);" onunload="call()">
	<div id="sweepIntermediariesdefinitionAdd" style="position:absolute; left:20px; top:20px; width:525px; height:145px; border:2px outset;" color="#7E97AF">
	<div id="sweepIntermediariesdefinitionAdd" style="position:absolute;left:8px; top:4px; width:510px; height:140px;">
		<table width="450px" border="0" cellpadding="0" cellspacing="0" height="70">
			 <tr height="24px">
				  <td width="120px" colspan="2"><b><fmt:message key="entity.id"/></b></td>
					<td width="110px" style="margin-left: 10px; padding-left: 10px">
					  <input type="text"  name="sweepintermediaries.id.entityId" value="${sweepintermediaries.id.entityId}"  class="htmlTextAlpha" tabindex="1"  disabled="true"  style="width:100px;" />
				    </td>

				  <td width="200px">
					<span id="entityName" name="entityName" class="spantext" style="padding-left: 10px">
				  </td>
		 </tr>

		<c:if test="${'add' == requestScope.method}">

		 <tr height="24px">
				<td width="120px" colspan="2"><b><fmt:message key="sweepIntermediaries.currencyCode"/></b>*</td>
			    <td width="55px"  style="padding-left: 10px">
			       <div width="55px" >
				    <select id="sweepintermediaries.id.currencyCode" name="sweepintermediaries.id.currencyCode"
				    titleKey="tooltip.selectCurrencyCode" tabindex="2">
					   <c:forEach items="${requestScope.currencyCodesColl}" var="ccy">
						   <option
							  value="${ccy.value}"
							  <c:if test="${sweepintermediaries.id.currencyCode == ccy.value}">selected="selected"</c:if>>
							  ${ccy.label}
						   </option>
						</c:forEach>
					</select>
				   </div>
			    </td>
				<td width="200px">
					<span id="currencyName" name="currencyName" class="spantext" style="padding-left: 10px">
				</td>
		</tr>


</c:if>

		<c:if test="${'change' == requestScope.method}">

		<tr height="24px">
				<td width="120px" colspan="2"><b><fmt:message key="sweepIntermediaries.currencyCode"/></b></td>
			    <td width="80px"  style="padding-left: 10px">
					<input type="text" class="htmlTextAlpha" name="sweepintermediaries.id.currencyCode" value="${sweepintermediaries.id.currencyCode}"  style="width:37px" tabindex="2" disabled="true" />
			    </td>
				<td width="200px">
					<span id="currencyName" name="currencyName" class="spantext" style="padding-left: 10px">
				</td>
		</tr>

</c:if>
		<c:if test="${'change' == requestScope.method}">

		<tr height="24px">
			 <td width="120px" colspan="2"><b><fmt:message key="sweepIntermediaries.targetBic"/></b></td>
			 <td width="100px" colspan="5"  style="padding-left: 10px">
			  <input type="text"  name="sweepintermediaries.id.targetBic" value="${sweepintermediaries.id.targetBic}"  class="htmlTextAlpha" tabindex="2"   maxlength="12" style="width:165px;" disabled="true" />
			 	</td>
		</tr>


</c:if>

		<c:if test="${'add' == requestScope.method}">

		<tr height="24px">
			 <td width="150px" ><b><fmt:message key="sweepIntermediaries.targetBic"/></b>*</td>
			 <td width="100px" colspan="5"  style="padding-left: 10px">
			   <!-- Start:Code Modified by Naseema.Sd for Mantis 1797 -->
			   <input type="text"  name="sweepintermediaries.id.targetBic" value="${sweepintermediaries.id.targetBic}"   titleKey="tooltip.enterTargetBic" class="htmlTextAlpha" tabindex="2"   maxlength="12" style="width:165px;" onblur="return checkalphanumeric(this,'target',event);" />
			   <!-- End:Code Modified by Naseema.Sd for Mantis 1797 -->
			 </td>
		</tr>


</c:if>

		<tr height="24px">
			 <td width="120px" colspan="2"><b><fmt:message key="sweepIntermediaries.intermediary"/></b></td>
			 <td width="100px" colspan="5"  style="padding-left: 10px">
			  <!-- Start:Code Modified by Naseema.Sd for Mantis 1797 -->
			   <input type="text"  name="sweepintermediaries.intermediary" value="${sweepintermediaries.intermediary}"   titleKey="tooltip.enterIntermediary" class="htmlTextAlpha" tabindex="2"   maxlength="12" style="width:165px;" onblur="return checkalphanumeric(this,'inter',event);" />
			  <!-- End:Code Modified by Naseema.Sd for Mantis 1797 -->
			 </td>
		</tr>


		 <tr height="24px">
				<td width="120px" colspan="2"><b><fmt:message key="sweepIntermediaries.accountId"/></b></td>
				<td width="100px" colspan="5" style="padding-left: 10px">
			    <input type="text"  name="sweepintermediaries.accountId" value="${sweepintermediaries.accountId}"   titleKey="tooltip.enterAccount" class="htmlTextAlpha" tabindex="2"   maxlength="34" style="width:290px;" onchange="return validateField(this,'sweepintermediaries.accountId','alphaNumPat');" />
				</td>
		</tr>

		</table>

	</div>
	</div>

	<div id="sweepIntermediariesdefinition" style="position:absolute; left:467; top:182px; width:40px; height:39px; visibility:visible;">

		<table width="60" border="0" cellspacing="0" cellpadding="0" height="20">
			<tr height="25">

				<c:if test="${'add' == requestScope.method}">

				<td align="Right">
					<a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Sweep Intermediary Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			    </td>

</c:if>
				<c:if test="${'change' == requestScope.method}">

				<td align="Right">
					<a tabindex="8" href=# onclick="javascript:openWindow(buildPrintURL('print','Change Sweep Intermediary Setup'),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<fmt:message key="tooltip.helpScreen"/>'></a>
			    </td>

</c:if>

				<td align="right" id="Print">
					<a tabindex="8" onclick="printPage();" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img src="images/Print.gif " name="Print"  border="0" title='<fmt:message key="tooltip.printScreen"/>'></a>
				</td>
			</tr>
		</table>
	</div>

	<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:174px; width:525px; height:39px; visibility:visible;">

	<div id="sweepIntermediariesdefinition" style="position:absolute; left:6; top:4; width:400px; height:15px; visibility:visible;">
	  <table border="0" cellspacing="0"  cellpadding="0"  width="145">
		<tr>
			<td id="savebutton">
			</td>
			<td id="cancelbutton"><a title='<fmt:message key="tooltip.cancel"/>' tabindex="7"onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="confirmClose('C');"><fmt:message key="button.cancel" /></a>
			</td>
		</tr>
	  </table>
	</div>
	<div id="sweepIntermediariesdefinition" style="position:absolute; left:6; top:4; width:400px; height:15px; visibility:hidden;">
	  <table width="145" border="0" cellspacing="2" cellpadding="0">
		<tr>
			<c:if test="${'add' == requestScope.method}">

			<td id="saveenablebutton" >
			  <a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveLocDefinition');"><fmt:message key="button.save" /></a>
			</td>


</c:if>

			<c:if test="${'change' == requestScope.method}">

			<td id="saveenablebutton" >
			  <a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('updateLocDefinition');"><fmt:message key="button.save" /></a>
			</td>

</c:if>

			<td id="saveenablebutton" >
			  <a title='<fmt:message key="tooltip.save"/>' tabindex="6" onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:submitForm('saveLocDefinition');"><fmt:message key="button.save" /></a>
			</td>

		</tr>
	  </table>
	</div>
	</div>
	<blockquote>&nbsp;</blockquote>
	<p>&nbsp;</p>
	</form>
	</body>
	</html>